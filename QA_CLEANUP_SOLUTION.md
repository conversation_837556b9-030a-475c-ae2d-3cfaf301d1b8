# QA Cleanup Branch-Specific Solution

## Problem
The QA cleanup workflow was always running from the main branch when triggered by issue closure, instead of running from the branch where the QA instance was originally deployed. This meant that any cleanup workflow changes in the deployment branch wouldn't be used during cleanup.

## Solution Implemented
We've implemented the **workflow_dispatch API approach** as suggested by <PERSON><PERSON><PERSON><PERSON>, which provides backward compatibility and solves the branch-specific execution problem **without adding a new workflow**.

### Changes Made

#### Modified `qa-cleanup.yml` Only
- **Kept** the `issues: types: [closed]` trigger
- **Added** `triggered_by` input parameter to track how the workflow was triggered
- **Added** smart detection logic to determine if running from main/master branch
- **Added** `trigger-api` job that runs when triggered from main branch
- **Modified** `cleanup` job to only run when NOT triggering API (prevents infinite loops)
- **Added** branch extraction from issue body for API calls

### How It Works

1. **Issue Closure**: When a QA issue is closed, the cleanup workflow triggers
2. **Branch Detection**: The workflow detects if it's running from main/master branch or feature branch
3. **Two Paths**:
   - **From main/master**: Extracts branch name from issue body and uses GitHub API to trigger cleanup on the deployment branch
   - **From feature branch**: Runs cleanup directly (this happens when API call triggers it)
4. **Information Extraction**:
   - Deployment URL from issue title (format: `QA-Instance ready [URL]`)
   - Branch name from issue body (line starting with `Branch:`) when needed for API call
5. **Branch-Specific Execution**: The cleanup ultimately runs from the deployment branch, using that branch's version of the workflow

### Benefits

- ✅ **No New Workflow**: Follows Lukáš's guidance - only modifies existing workflow
- ✅ **Backward Compatible**: Old workflows continue to work without modification
- ✅ **Branch-Specific**: Cleanup runs from the correct branch
- ✅ **No Infinite Loops**: Smart detection prevents re-triggering when called via API
- ✅ **Robust**: Includes validation and error handling
- ✅ **Single File**: All logic contained in one workflow file

### Testing

To test this solution:

1. **Deploy a QA instance** from a feature branch
2. **Close the generated issue**
3. **Verify** that the cleanup workflow runs from the feature branch (check the workflow run details)
4. **Confirm** that any branch-specific cleanup logic is executed

### Rollback Plan

If issues arise, you can quickly rollback by:
1. Reverting the changes to `qa-cleanup.yml` to restore the original simple logic
2. Remove the `trigger-api` job and the conditional logic from the `cleanup` job

This will restore the original behavior while you investigate any problems.
