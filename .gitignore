# global

.DS_Store
node_modules
.hg
.idea
nbproject
*.orig
*.rej
.tmp
.hgtags
.hgignore
.vscode
.env
tsconfig.tsbuildinfo

./offlineBuild/*

#frontend
/frontend/dist/*
/frontend/build/*
/frontend/config/tas.js
/frontend/config/_colors.scss
/frontend/assets/uploads/*
/frontend/assets/logos/*
/frontend/Dockerfile/*
/frontend/logs/*
/frontend/package-lock.json


#backend
/backend/src/logs/*
/backend/config/local.js
/backend/config/config/local.js
/backend/config/config/local.ts
/backend/src/config/local.ts
/backend/src/config/local.ts
/backend/src/config/database/mssqlForceTypesUpdated.json
/backend/storage/*
/backend/src/cache/*
/backend/src/dms-data/storage/*
/backend/src/test/unit/api/calculations/files/dst/*
/backend/src/test/libreoffice/dst/*
/backend/src/test/libreoffice/tmp/*
/backend/src/scripts/Blank.js
/backend/src/scripts/Blank.ts
/backend/src/scripts/crons/.locks
/backend/src/certificates/*
/backend/src/test/performance/reports/*
/backend/src/test/api/scripts/done/generated.txt
/backend/src/test/api/scripts/done/generated.xml
/backend/src/test/api/scripts/once/generated.txt
/backend/src/test/api/scripts/once/generated.xml
/backend/src/test/api/scripts/done/file1.xml
/backend/src/test/api/scripts/duplicite/file1.xml
/backend/src/test/api/scripts/done/file2.xml
/backend/src/test/cypress-test/screenshots
/backend/src/test/cypress-test/videos
/backend/src/test/files/TESTUJEME_HODNEAR_INV_260038579_signed.pdf
/backend/src/screenshots
/backend/src/videos
/backend/src/dist
/backend/src/documentation/monaco/defaults.json
/backend/dist
/backend/dist-*

#tests-cy
/tests-cy/cypress.config.local.ts
/tests-cy/cypress/screenshots
/tests-cy/cypress/videos
/tests-cy/cypress/downloads

!.hgkeep
!.keep
!.gitkeep
backend/BlankRunner.js
team-assistant-firebase-admin.json
admin_pw.sql
