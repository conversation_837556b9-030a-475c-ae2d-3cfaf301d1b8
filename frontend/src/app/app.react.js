import i18next from 'i18next';
import PropTypes from 'prop-types';
import moment from 'moment';
import React from 'react';
import _ from 'lodash';
import path from 'path';
import {
    guid,
    isSupportedBrowser,
    browserStorage,
} from '../common/utils';
import Routes from '../routes';
import browserHistory from '../common/history';
import ModalMaintenance from '../components5.0/sections/administration/maintenance/ModalMaintenance';
import ConfirmModal from '../pages/modals/confirmModal.react';
import AlertsList from '../components/alerts/alertsList.react';
// components5.0
import MainLayout from '../components5.0/layouts/MainLayout';
import ModalRefreshPage from '../components5.0/Modals/ModalRefreshPage';
import ApiRequest from '../api/apiRequest';
import ModalRefreshSession from '../components5.0/Modals/ModalRefreshSession';
import CrossPlatform from '../common/CrossPlatform';
import ModalNews from '../components5.0/Modals/ModalNews';
import { useStore } from '../components5.0/zustand/boundStore';
// flux
import AlertsActions from '../components/alerts/alerts.actions';
import LoggedUserStore from '../flux/loggedUser.store';
import LoggedUserActions from '../flux/loggedUser.actions';
import MenuActions from '../flux/menu.actions';

const timeToRestoreConnection = 10;

class App extends React.Component {

    constructor(props) {
        super();
        this.state = _.extend(
            {
                key: 0,
                restoreConnectionTime: timeToRestoreConnection,
                expirationCountdownVisible: false,
                anotherUserLoggedIn: false,
                isOpen: false,
                passwordWillExpireModalIsOpen: false,
                overlayLogo: path.resolve('/assets/images/logo-login.png'),
                newsModalOpen: false,
            },
            LoggedUserStore.getState(),
        );

        this.runConnectionTimer = this.runConnectionTimer.bind(this);
        this.connectionCheck = this.connectionCheck.bind(this);
        this._onChange = this._onChange.bind(this);
        this.runLoginExpirationTimer = this.runLoginExpirationTimer.bind(this);
        this.stayLogged = this.stayLogged.bind(this);
        this.checkLoggedUser = this.checkLoggedUser.bind(this);
        this.getTime = this.getTime.bind(this);
        this.refreshPage = this.refreshPage.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.setOverlayLogo = this.setOverlayLogo.bind(this);
        this.openPasswordWillExpireModal =
            this.openPasswordWillExpireModal.bind(this);
        this.closePasswordWillExpireModal =
            this.closePasswordWillExpireModal.bind(this);
        this.toPasswordChange = this.toPasswordChange.bind(this);
        this.toLoginPage = this.toLoginPage.bind(this);
        this.handleCopy = this.handleCopy.bind(this);
        this.handleOpenNewsModal = this.handleOpenNewsModal.bind(this);
        this.handleCloseNewsModal = this.handleCloseNewsModal.bind(this);
        this.maintainSeenUserNewsInfo = this.maintainSeenUserNewsInfo.bind(this);
    }

    componentDidMount() {
        if (
            !isSupportedBrowser(navigator.userAgent) &&
            !window.localStorage.getItem('unsupportedBrowserSeen')
        ) {
            this.setState({ isOpen: true });
        }

        window.addEventListener('focus', this.checkLoggedUser);
        LoggedUserStore.listen(this._onChange);
        LoggedUserActions.fetchUserParams({ apiUrl: '/user-parameters' });
        LoggedUserActions.fetchUserPriorityNewsPosts();
        this.alertAdded = false;

        this.expirationCountdown = 0;
        this.runLoginExpirationTimer();

        window.addEventListener('beforeunload', this.beforeUnload);

        // Theme
        const darkTheme = localStorage.getItem('darkTheme');
        LoggedUserActions.switchDarkMode(darkTheme === 'true');

        // HIDE_MAIN_MENU
        const hideMainMenu = localStorage.getItem('hideMainMenu');
        if (hideMainMenu) {
            MenuActions.setMenuHideFromStorage(hideMainMenu === 'false');
        }

        // TOOLTIP
        const showTooltips = localStorage.getItem('showTooltips');
        LoggedUserActions.showHelp(showTooltips !== 'false');

        // TABLES_COLUMNS
        const tableColumns = localStorage.getItem('tableColumns');

        if (tableColumns) {
            let tableColumnsArr;

            try {
                tableColumnsArr = JSON.parse(tableColumns);
            } catch (e) {
                tableColumnsArr = [];
                LoggedUserActions.saveTablesColumns([]);
            }
            LoggedUserActions.saveColumnsWidthsArr(tableColumnsArr);
        } else {
            LoggedUserActions.saveTablesColumns([]);
        }

        const passwordExpiration = browserStorage.getItem(
            'auth.passwordExpirationStatus',
        );

        if (passwordExpiration === 'N') {
            this.openPasswordWillExpireModal();
        }

        browserStorage.removeItem('auth.passwordExpirationStatus');

        // cleans old seen news data in localStorage
        this.maintainSeenUserNewsInfo();
    }

    componentDidUpdate(prevProps, prevState) {
        // reload app if admin/user is switched
        if (
            prevState.disableAdmin !== null &&
            this.state.disableAdmin !== prevState.disableAdmin
        ) {
            setTimeout(() => {
                this.setState({ key: guid() });
            });
        }

        if (
            prevState.connectionFailed === false &&
            this.state.connectionFailed === true
        ) {
            this.setOverlayLogo();
            this.runConnectionTimer();
        }

        if (prevState.prolongSwitch !== this.state.prolongSwitch) {
            clearInterval(this.expirationTimer);
            this.runLoginExpirationTimer();
        }

        if (
            (!prevState.expirationCountdownVisible &&
                this.state.expirationCountdownVisible) ||
            (!prevState.anotherUserLoggedIn &&
                this.state.anotherUserLoggedIn) ||
            (!LoggedUserStore.isAdmin() &&
                !prevState.maintenance &&
                this.state.maintenance)
        ) {
            this.setOverlayLogo();
        }

        const { seenPriorityNewsInfo } = useStore.getState();

        if (
            prevState.userPriorityNewsPosts === null
            && this.state.userPriorityNewsPosts !== null
            && this.state.userPriorityNewsPosts.length > 0
        ) {
            const newUnseenPostsExist = this.state.userPriorityNewsPosts.some((postId) => (
                !seenPriorityNewsInfo?.minDate
                || moment(postId.post_publication_date).isAfter(moment(seenPriorityNewsInfo.minDate))
            ));

            if (newUnseenPostsExist) {
                this.handleOpenNewsModal();
            }
        }
    }

    componentWillUnmount() {
        clearInterval(this.connectionTimer);
        if (this.loggedUserRequest) this.loggedUserRequest.cancel();
        LoggedUserActions.setConnectionFailed(false);

        clearInterval(this.expirationTimer);
        if (this.prolongLoginRequest) this.prolongLoginRequest.cancel();

        LoggedUserStore.unlisten(this._onChange);

        window.removeEventListener('focus', this.checkLoggedUser);
        window.removeEventListener('beforeunload', this.beforeUnload);
    }

    _onChange(state) {
        this.setState(state);
    }

    setOverlayLogo() {
        const overlayLogo = LoggedUserStore.getLogo();
        this.setState({ overlayLogo });
    }

    checkLoggedUser() {
        if (!this.state.useSessionStorage) {
            const sStorageUsrId = sessionStorage.getItem('auth.userId');
            const lStorageUsrId = localStorage.getItem('auth.userId');
            if (
                sStorageUsrId &&
                lStorageUsrId &&
                sStorageUsrId !== lStorageUsrId
            ) {
                this.setState({ anotherUserLoggedIn: true });
            }
        }
    }

    getTime(string) {
        function leadingZero(num) {
            return (num < 10 ? '0' : '') + num;
        }

        const date = new Date(string);
        return `${leadingZero(date.getHours())}:${leadingZero(
            date.getMinutes(),
        )}`;
    }

    runConnectionTimer() {
        this.setState({ restoreConnectionTime: timeToRestoreConnection });

        this.connectionTimer = setInterval(() => {
            if (this.state.restoreConnectionTime > 0) {
                this.setState({
                    restoreConnectionTime: this.state.restoreConnectionTime - 1,
                });
            } else {
                clearInterval(this.connectionTimer);
                this.connectionCheck();
            }
        }, 1000);
    }

    runLoginExpirationTimer() {
        if (!CrossPlatform.isNativeMobileApp()) {
            const expirationTimeLeft = LoggedUserStore.getConfig('tas.sessionExpiration.notifyBeforeExpiration') || 1800;
            this.expirationCountdown = this.state.sessionTimeout;

            this.expirationTimer = setInterval(() => {
                if (this.expirationCountdown > 0) {
                    this.expirationCountdown -= 1;

                    if (this.expirationCountdown <= expirationTimeLeft && !this.state.expirationCountdownVisible) {
                        this.setState({
                            expirationCountdownVisible: true,
                            expirationCountdown: this.expirationCountdown,
                        });
                    } else if (this.expirationCountdown > expirationTimeLeft && this.state.expirationCountdownVisible) {
                        this.setState({ expirationCountdownVisible: false });
                    } else if (this.state.expirationCountdownVisible) {
                        this.setState({ expirationCountdown: this.expirationCountdown });
                    }
                } else {
                    clearInterval(this.expirationTimer);
                }
            }, 1000);
        }
    }

    connectionCheck() {
        if (CrossPlatform.isNativeMobileApp()) {
            CrossPlatform.call('connectionCheck');
        }
        this.loggedUserRequest = ApiRequest.get('/logged-user')
            .then((payload) => {
                LoggedUserActions.setConnectionFailed(false);
                this.setState({ key: guid() });
            })
            .catch((err) => {
                this.runConnectionTimer();
            });
    }

    stayLogged(e) {
        const vice = JSON.parse(browserStorage.getItem('vice'));
        const oldAccessToken = vice ? vice.viceToken : undefined;
        const oldRefreshToken = vice ? vice.viceRefreshToken : undefined;
        const postObj = {
            accessToken: oldAccessToken,
            refreshToken: oldRefreshToken,
            setCookies: !vice,
            forceRefresh: true,
        };

        this.prolongLoginRequest = ApiRequest.post(
            '/authorization/token-refresh',
            postObj,
        )
            .then(({ accessToken, refreshToken, expire }) => {
                if (vice) {
                    browserStorage.setItem(
                        'vice',
                        JSON.stringify({
                            viceToken: accessToken,
                            viceRefreshToken: refreshToken,
                            viceName: vice.viceName,
                        }),
                    );
                }

                if (!vice) {
                    localStorage.setItem('cookieExpiration', expire);
                }
                LoggedUserActions.setSessionTimeout();
                this.setState({ expirationCountdownVisible: false });
            })
            .catch((errorMessage) => {
                // Delete expired information from storage
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrSessionExpired'),
                    serverError: errorMessage,
                });

                clearInterval(this.expirationTimer);
                this.runLoginExpirationTimer();
                this.setState({ expirationCountdownVisible: false });
            });
    }

    refreshPage(e) {
        location.reload(true); // not from cache
    }

    closeModal() {
        this.setState({ isOpen: false });
        window.localStorage.setItem('unsupportedBrowserSeen', true);
    }

    openPasswordWillExpireModal() {
        this.setState({ passwordWillExpireModalIsOpen: true });
    }

    closePasswordWillExpireModal() {
        this.setState({ passwordWillExpireModalIsOpen: false });
    }

    toPasswordChange() {
        this.closePasswordWillExpireModal();
        browserHistory.push('/settings/user/change-password');
    }

    toLoginPage() {
        browserHistory.push(window.config.loginPage);
    }

    handleCopy(e) {
        // t3f-1629 kopírování textu z TASu

        // extract links from copied anchor tags and add them in brackets
        // must go through the node tree and reconstruct it with added links
        const reconstructWithLinks = (elements) => {
            const reconstructedDiv = document.createElement('div');

            elements.forEach((element) => {
                if (element.nodeType === Node.TEXT_NODE) {
                    reconstructedDiv.appendChild(
                        document.createTextNode(element.textContent),
                    );
                } else if (element.nodeType === Node.ELEMENT_NODE) {
                    const clonedElement = element.cloneNode(false);
                    reconstructedDiv.appendChild(clonedElement);

                    if (element.nodeName === 'A' && element.href) {
                        const linkText = document.createTextNode(
                            ` ${element.textContent} (${element.href})`,
                        );
                        reconstructedDiv.appendChild(linkText);
                    } else if (element.childNodes.length > 0) {
                        const childNodes = Array.from(element.childNodes);
                        const reconstructedChild =
                            reconstructWithLinks(childNodes);
                        clonedElement.appendChild(reconstructedChild);
                    }
                }
            });

            return reconstructedDiv;
        };

        const selectionRange = window.getSelection().getRangeAt(0);

        const tempDiv = document.createElement('div');
        tempDiv.appendChild(selectionRange.cloneContents());

        const copiedElements = Array.from(tempDiv.childNodes);

        // for some reason inputs don't get cloned, so if copying from an input, use default copy behaviour
        if (!_.isEmpty(copiedElements)) {
            const reconstructedDiv = reconstructWithLinks(copiedElements);

            // remove all formatting, copy text only
            const reconstructedText = reconstructedDiv.textContent;
            e.clipboardData.setData('text/plain', reconstructedText);

            e.preventDefault();
        }
    }

    handleOpenNewsModal() {
        this.setState({ newsModalOpen: true });
    }

    handleCloseNewsModal() {
        const newPriorityNewsMinDate = moment.max(
            this.state.userPriorityNewsPosts.map((post) => moment(post.post_publication_date)),
        ).toISOString();

        useStore.setState({
            seenPriorityNewsInfo: {
                minDate: newPriorityNewsMinDate,
            },
        });

        this.setState({ newsModalOpen: false });
    }

    maintainSeenUserNewsInfo() {
        const { seenTaskNewsInfo } = useStore.getState();

        const getCleanSeenTaskNewsInfo = (taskNews, cutoffAge) => (
            Object.fromEntries(
                Object.entries(taskNews).filter(
                    ([taskId, taskNewsInfo]) => moment(taskNewsInfo.lastTimeVisited).add(cutoffAge).isAfter(moment()),
                ),
            )
        );

        const initalCutoffAge = moment.duration(6, 'month');

        let cleanSeenTaskNewsInfo = seenTaskNewsInfo;

        // localStorage usually has a size limit of 5-10MB
        // if the size of cleanSeenTaskNewsInfo exceeds maxAcceptableTaskNewsInfoSize,
        // start iteratively decreasing the cutoff age until the size is acceptable
        const maxAcceptableTaskNewsInfoSize = 2000000; // around 2MB
        // - max size of one task record is 120B, so you'd need around 16k records (visited template tasks) to reach this size...
        // - this was made when the size of each task record was significantly larger, but we can keep this just to be extra safe
        for (
            let seenTaskNewsExpirationDuration = initalCutoffAge;
            (
                JSON.stringify(cleanSeenTaskNewsInfo).length >= maxAcceptableTaskNewsInfoSize
                && Object.keys(cleanSeenTaskNewsInfo).length > 0
            );
            seenTaskNewsExpirationDuration = moment.duration(Math.floor(seenTaskNewsExpirationDuration.asHours() / 2), 'hours')
        ) {
            cleanSeenTaskNewsInfo = getCleanSeenTaskNewsInfo(cleanSeenTaskNewsInfo, seenTaskNewsExpirationDuration);

            if (
                seenTaskNewsExpirationDuration.asHours() === 0
                && JSON.stringify(cleanSeenTaskNewsInfo).length >= maxAcceptableTaskNewsInfoSize
            ) {
                // the remaining records are either in the future or in the last hour - clear them all
                cleanSeenTaskNewsInfo = {};
            }
        }

        useStore.setState({
            seenTaskNewsInfo: cleanSeenTaskNewsInfo,
            seenTaskNewsInfoMaintained: true,
        });
    }

    render() {
        const { overlayLogo, userPriorityNewsPosts } = this.state;
        const { seenPriorityNewsInfo } = useStore.getState();
        const newUnseenPriorityNewsPosts = userPriorityNewsPosts?.filter(
            (post) => (
                !seenPriorityNewsInfo.minDate
                || moment(post.post_publication_date).isAfter(seenPriorityNewsInfo.minDate)
            ),
        ) || [];

        return (
            <div
                id="main"
                className="row large-collapse small-collapse"
                key={this.state.key}
                onCopy={this.handleCopy}
            >
                <MainLayout location={this.props.location}>
                    <div id="main-content" style={{ width: '100%' }}>
                        <Routes />
                    </div>
                    <AlertsList />
                    <ModalMaintenance />
                    {this.state.connectionFailed && (
                        <div id="app-overlay" className="connection-failed">
                            <div id="app-overlay-message">
                                <p>
                                    <img
                                        src={overlayLogo}
                                        alt="Team Assistant"
                                    />
                                </p>
                                {i18next.t('unableToConnect')}
                                <p>
                                    {`${i18next.t(
                                        'attemptToRestoreConnection',
                                    )}: ${this.state.restoreConnectionTime}`}
                                </p>
                            </div>
                        </div>
                    )}
                    <ModalRefreshPage
                        isOpen={this.state.anotherUserLoggedIn}
                        onRefresh={this.refreshPage}
                    />
                    <ModalRefreshSession
                        isOpen={(
                            this.state.expirationCountdownVisible && this.state.expirationCountdown >= 0
                        ) || (
                            this.state.expirationCountdownVisible && this.state.expirationCountdown === 0
                        )}
                        isTimedOut={this.state.expirationCountdown === 0}
                        onRefresh={this.stayLogged}
                        onLogin={this.toLoginPage}
                        timeRemaining={Math.ceil(this.state.expirationCountdown / 60)}
                    />
                    {this.state.newsModalOpen && newUnseenPriorityNewsPosts.length > 0 && (
                        <ModalNews
                            isOpen={this.state.newsModalOpen}
                            onClose={this.handleCloseNewsModal}
                            posts={newUnseenPriorityNewsPosts}
                            pagingLabelTitle={
                                i18next.t(
                                    'newsSinceLastVisitAmount',
                                    { amount: newUnseenPriorityNewsPosts.length },
                                )
                            }
                        />
                    )}
                </MainLayout>
                <ConfirmModal
                    text={i18next.t('unsupportedBrowser')}
                    width="tiny"
                    isOpen={this.state.isOpen}
                    onClose={this.closeModal}
                    onConfirm={this.closeModal}
                    saveButton={i18next.t('ok')}
                    dropButton={i18next.t('close')}
                />
                <ConfirmModal
                    text={i18next.t('passwordWillExpire')}
                    width="tiny"
                    isOpen={this.state.passwordWillExpireModalIsOpen}
                    onClose={this.closePasswordWillExpireModal}
                    onConfirm={this.toPasswordChange}
                    saveButton={i18next.t('changePassword')}
                    dropButton={i18next.t('close')}
                    saveButtonIcon="icon-key-1"
                />
            </div>
        );
    }

}

App.displayName = 'App';

App.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default App;
