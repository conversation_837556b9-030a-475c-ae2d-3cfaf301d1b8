import { Theme } from '@mui/material/styles';
import { ButtonProps, buttonClasses } from '@mui/material/Button';

export default function Button(theme: Theme) {
    const rootStyles = (ownerState: ButtonProps) => {
        const containedVariant = ownerState.variant === 'contained';

        const textVariant = ownerState.variant === 'text';

        const smallSize = ownerState.size === 'small';

        const mediumSize = ownerState.size === 'medium';

        const largeSize = ownerState.size === 'large';

        const defaultStyle = {
            '&.css-1bizjcw-MuiButtonBase-root-MuiListItemButton-root.Mui-focusVisible':
                {
                    backgroundColor: 'transparent',
                },
            minWidth: 'unset',
            whiteSpace: 'nowrap',
            '&:hover': {
                boxShadow: 'none',
            },
            boxShadow: 'none',
            lineHeight: '1.5rem',
            ...(containedVariant && {
                '& .Mui-active': {
                    backgroungColor: theme.palette.primary.darker || theme.palette.primary.dark,
                },
            }),

            ...(textVariant && {
                color: theme.palette.secondary.contrastText,
                '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                },
            }),
        };

        const disabledState = {
            [`&.${buttonClasses.disabled}`]: {
                // CONTAINED
                ...(containedVariant && {
                    backgroundColor: theme.palette.action.disabledBackground,
                    color: theme.palette.common.white,
                }),
                ...(textVariant && {
                    color: theme.palette.action.disabled,
                }),
            },
        };

        const size = {
            ...(smallSize && {
                fontWeight: 500,
                fontSize: 12,
                padding: '0.375rem 0.75rem',
                '& .MuiButton-startIcon': {
                    span: { fontSize: '1.25rem', marginTop: '-0.125rem' },
                },
            }),
            ...(mediumSize && {
                fontWeight: 500,
                fontSize: 14,
                padding: '0.375rem 0.75rem',
                lineHeight: '1.5rem',

                '& .MuiButton-startIcon': {
                    span: {
                        fontSize: '1.5rem',
                        marginTop: '-0.0625rem',
                        marginLeft: '0.1875rem',
                    },
                },
            }),
            ...(largeSize && {
                fontSize: 14,
                fontWeight: 700,
                padding: '0.625rem 1.25rem',
                '& .MuiButton-startIcon': {
                    span: { fontSize: '1.5rem' },
                },
            }),
        };

        return [disabledState, size, defaultStyle];
    };

    return {
        MuiButton: {
            defaultProps: {
                size: 'small',
                color: 'secondary',
                disableTouchRipple: true,
                disableFocusRipple: true,
            },

            styleOverrides: {
                root: ({ ownerState }: { ownerState: ButtonProps }) =>
                    rootStyles(ownerState),
            },
        },
    };
}
