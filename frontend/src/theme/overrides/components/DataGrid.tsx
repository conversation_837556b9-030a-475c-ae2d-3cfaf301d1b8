// @mui
import React, { Theme, styled } from '@mui/material/styles';
import { Box, Skeleton, LinearProgress } from '@mui/material';
import {
    GridArrowDownwardIcon,
    GridArrowUpwardIcon,
    GridHeaderFilterMenu,
} from '@mui/x-data-grid-pro';
// hooks
import { useTranslation } from 'react-i18next';
// components
import NotFound from '../../../components5.0/NotFound';
import MyIcon from '../../../components5.0/MyIcon';
import DataGridIconTooltip from '../../../components5.0/data-grid/components/DataGridIconTooltip';
// utils
import { pxToRem, primaryFont } from '../../typography';

const StyledGridOverlay = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
}));

const CustomNoRowsOverlay = () => {
    const { t } = useTranslation();

    return (
        <StyledGridOverlay>
            <NotFound text={t('emptyDataMessage')} />
        </StyledGridOverlay>
    );
};

const FilterIconButton = (props: any) => {
    return <GridHeaderFilterMenu {...props} style={{ position: 'absolute', right: '0.625rem' }} />;
};

// ----------------------------------------------------------------------

export default function DataGrid(theme: Theme) {
    return {
        MuiDataGrid: {
            defaultProps: {
                /* for skeletons lazy loading */
                // rowsLoadingMode: 'server',
                filterMode: 'server',
                sortingMode: 'server',
                disableMultipleRowSelection: true,
                disableColumnReorder: true,
                filterDebounceMs: 500,
                columnHeaderHeight: 38,
                disableColumnMenu: true,
                rowHeight: 38,
                slots: {
                    headerFilterMenu: FilterIconButton,
                    noRowsOverlay: CustomNoRowsOverlay,
                    noResultsOverlay: CustomNoRowsOverlay,
                    loadingOverlay: LinearProgress,
                    filterPanelAddIcon: () => <MyIcon icon="add" />,
                    filterPanelRemoveAllIcon: () => <MyIcon icon="trash" />,
                    filterPanelDeleteIcon: () => (
                        <DataGridIconTooltip title="delete">
                            <span>
                                <MyIcon icon="close" />
                            </span>
                        </DataGridIconTooltip>
                    ),
                    openFilterButtonIcon: () => (
                        <DataGridIconTooltip title="operators">
                            <span>
                                <MyIcon icon="filter" sx={{ fontSize: '1.25rem' }} />
                            </span>
                        </DataGridIconTooltip>
                    ),
                    columnUnsortedIcon: () => (
                        <DataGridIconTooltip title="sortVars">
                            <span
                                style={{
                                    height: '0.8125rem',
                                    width: '1.0625rem',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginTop: '0',
                                    marginRight: '0.0625rem',
                                    marginLeft: '0',
                                }}
                            >
                                <MyIcon
                                    icon="sort-filled"
                                    sx={{
                                        color: theme.palette.text.secondary,
                                        fontSize: '0.75rem !important',
                                    }}
                                />
                            </span>
                        </DataGridIconTooltip>
                    ),
                    columnSortedAscendingIcon: () => (
                        <DataGridIconTooltip title="asc">
                            <GridArrowUpwardIcon
                                style={{
                                    width: '1.125rem',
                                    height: '1.125rem',
                                }}
                            />
                        </DataGridIconTooltip>
                    ),
                    columnSortedDescendingIcon: () => (
                        <DataGridIconTooltip title="desc">
                            <GridArrowDownwardIcon
                                style={{
                                    width: '1.125rem',
                                    height: '1.125rem',
                                }}
                            />
                        </DataGridIconTooltip>
                    ),
                    columnFilteredIcon: () => (
                        <Box
                            component="span"
                            borderRadius="3.125rem"
                            bgcolor={theme.palette.primary.main}
                            height="0.5rem"
                            width="0.5rem"
                            mb="0.1875rem"
                        />
                    ),
                    // cell: (props: any) => {
                    //     return (
                    //         <GridCell {...props}>
                    //             {props.children
                    //                 ? props.children
                    //                 : (props.formattedValue ?? props.value) && (
                    //                       <TextMaxLine
                    //                           line={1}
                    //                           sx={{
                    //                               whiteSpace: 'normal',
                    //                               wordBreak: 'break-all',
                    //                           }}
                    //                           fontSize={pxToRem(13)}
                    //                           fontWeight={500}
                    //                           placement="right"
                    //                       >
                    //                           {props.children ||
                    //                               props.formattedValue ||
                    //                               props.value}
                    //                       </TextMaxLine>
                    //                   )}
                    //         </GridCell>
                    //     );
                    // },
                    skeletonCell: (props: any) => (
                        <Box
                            {...props}
                            display="flex"
                            alignItems="center"
                            borderBottom="0.5px solid #D0D3DB"
                            borderTop="0.5px solid #D0D3DB"
                        >
                            <Skeleton
                                variant="text"
                                height={25}
                                width="50%"
                                sx={{ margin: ' 0rem 0.625rem 0rem 0.625rem' }}
                            />
                        </Box>
                    ),
                },
                slotProps: {
                    baseTooltip: {
                        arrow: true,
                    },
                    baseFormControl: {
                        color: 'secondary',
                        variant: 'outlined',
                    },
                    // baseInputLabel: {
                    //     shrink: true,
                    //     sx: {
                    //         // textTransform: 'uppercase',
                    //         fontSize: '0.8125rem',
                    //     },
                    // },
                    baseSelect: {
                        color: 'secondary',
                        variant: 'outlined',
                        size: 'small',
                        native: false,
                        fullWidth: true,
                    },
                    outlinedInput: {
                        notched: false,
                    },
                    baseTextField: {
                        color: 'secondary',
                        variant: 'outlined',
                        size: 'small',
                        fullWidth: true,
                        InputLabelProps: {
                            // shrink: true,
                            sx: {
                                visibility: 'hidden',
                                // textTransform: 'uppercase',
                                fontSize: '0.8125rem',
                                '&.Mui-focused, &.MuiInputLabel-shrink': {
                                    visibility: 'visible',
                                },
                            },
                        },
                    },
                    baseIconButton: {
                        title: '',
                        color: 'secondary',
                        size: 'small',
                        sx: {
                            margin: 0,
                            '& span': {
                                fontSize: '1rem',
                            },
                        },
                    },
                    filterPanel: {
                        filterFormProps: {
                            logicOperatorInputProps: {
                                variant: 'outlined',
                                size: 'small',
                            },
                            columnInputProps: {
                                variant: 'outlined',
                                size: 'small',
                                sx: { mt: 'auto' },
                            },
                            operatorInputProps: {
                                variant: 'outlined',
                                size: 'small',
                                sx: { mt: 'auto' },
                            },
                            valueInputProps: {
                                InputComponentProps: {
                                    variant: 'outlined',
                                    size: 'small',
                                },
                            },
                        },
                        panelFooterProps: {},
                        sx: {
                            // Customize inputs using css selectors
                            '& .MuiDataGrid-filterForm': { p: 2 },
                            // '& .MuiDataGrid-filterForm:nth-child(even)': {
                            //   backgroundColor: (theme: Theme) =>
                            //     theme.palette.mode === 'dark' ? '#444' : '#f5f5f5',
                            // },
                            '& .MuiDataGrid-filterFormLogicOperatorInput': {
                                mr: 2,
                            },
                            '& .MuiDataGrid-filterFormColumnInput': {
                                mr: 2,
                                width: 150,
                            },
                            '& .MuiDataGrid-filterFormOperatorInput': { mr: 2 },
                            '& .MuiDataGrid-filterFormValueInput': {
                                width: 200,
                            },
                            '& .MuiFormLabel-root': {
                                left: '-0.1875rem',
                            },
                        },
                    },
                },
            },
            styleOverrides: {
                '& .MuiDataGrid-topContainer': {
                    borderBottom: '0.5px solid #D0D3DB !important',
                },
                columnHeaderCheckbox: {
                    width: '3rem !important', // 3.125 minus margin
                    padding: '0 0.75rem',
                    // add margin to prevent checkbox column from hiding a part of the next columns's border
                    margin: '0 0.0625rem 0 0.0625rem',
                    bottom: '-2.1875rem',
                    '&:focus-within': {
                        outline: 'none',
                    },
                    '& span.MuiCheckbox-root.MuiCheckbox-indeterminate': {
                        width: '1.5625rem',
                        height: '2rem',
                    },
                },
                cellCheckbox: {
                    width: '3.125rem',
                    '&:focus-within': {
                        outline: 'none',
                    },
                },
                panelContent: {
                    width: 'fit-content',
                },
                menu: {
                    '& .MuiList-root': {
                        padding: '0.5rem 0.625rem',
                        '& .MuiMenuItem-root': {
                            fontSize: '0.75rem ',
                            lineHeight: '0.875rem',
                            '& .MuiListItemIcon-root': {
                                fontSize: '0.75rem',
                            },
                        },
                    },
                },

                row: {
                    cursor: 'pointer',
                },
                cell: {
                    padding: '0 0.75rem',
                    fontWeight: 500,
                    cursor: 'pointer',
                    '&:focus': {
                        outline: 'none',
                    },
                },
                columnHeaders: {
                    overflow: ' visible',
                    borderBottom: '1px solid',
                    borderColor: theme.palette.divider,
                },

                columnHeader: {
                    padding: '0rem 0.75rem 0.1875rem 0rem',
                    height: '2.375rem !important',
                    borderBottom: 'none !important',
                    '&:focus-within': {
                        outline: 'none',
                    },
                },

                // virtualScroller: {
                //     marginTop: '0.5625rem',
                // },
                headerFilterRow: {
                    // borderBottom: '0.5px solid #D0D3DB',
                    '& .MuiDataGrid-columnHeader': {
                        borderTop: 'none',
                    },
                    // paddingBottom: '0.2375rem',
                },
                panelWrapper: {
                    padding: '0.5rem',
                },
                columnHeaderDraggableContainer: {
                    alignItems: 'flex-end',
                },
                columnHeaderTitleContainer: {
                    alignItems: 'flex-end',
                    height: 30,
                },
                columnHeaderTitle: {
                    // textTransform: 'uppercase',
                    fontSize: pxToRem(13),
                    // fontWeight: 600,
                    lineHeight: pxToRem(24),
                    alignItems: 'end',
                    margin: '0 0.75rem',
                    color: theme.palette.text.secondary,
                    fontWeight: 600,
                },
                toolbarContainer: {
                    zIndex: 2,
                    position: 'relative',
                    bottom: '-0.7rem',
                },
                root: {
                    // '--borderTop': { borderTop: 'none' },
                    '& .MuiDataGrid-filler': {
                        border: 'none !important',
                    },

                    '--DataGrid-rowBorderColor': theme.palette.divider,
                    '--DataGrid-containerBackground': theme.palette.background.paper,
                    '--DataGrid-pinnedBackground': theme.palette.background.paper,
                    '.MuiDataGrid-filler--pinnedRight': {
                        borderLeft: 'none',
                    },
                    '& .MuiDataGridPro-cell--pinnedRight': {
                        '& :hover': { backgroundImage: 'none' },
                    },
                    '& .MuiDataGrid-actionsCell': {
                        gridGap: '0rem',
                    },
                    border: 'none',
                    paper: {
                        padding: '0.5rem',
                    },
                    '& .MuiDataGrid-scrollbarFiller.MuiDataGrid-scrollbarFiller--borderTop': {
                        border: 'none',
                    },
                    '.MuiDataGrid-columnHeader--pinnedRight': {
                        boxShadow: 'none',
                        background: 'transparent',
                    },
                    '.MuiDataGrid-columnHeader--withLeftBorder': {
                        borderLeft: 'none',
                    },
                    '.MuiDataGrid-columnHeader--alignRight .MuiDataGrid-columnHeaderDraggableContainer .MuiDataGrid-columnHeaderTitleContainer':
                        {
                            flexDirection: 'row',
                        },
                    '& .MuiDataGrid-cell--withLeftBorder': {
                        borderLeft: 'none',
                    },
                    '.MuiDataGrid-headerFilterRow .MuiDataGrid-columnHeader.MuiDataGrid-columnHeaderCheckbox':
                        {
                            visibility: 'hidden',
                        },
                    '& .MuiPaper-root': { padding: '0.5rem' },
                    '*, & *': {
                        fontFamily: primaryFont,
                    },
                },
            },
        },
    };
}
