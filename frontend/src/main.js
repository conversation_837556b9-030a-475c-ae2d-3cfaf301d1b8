import React from 'react';
import { Router, Switch, Route } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import { createRoot } from 'react-dom/client';
import _ from 'lodash';
import browserHistory from './common/history';
import i18n from './common/i18n'; // initialized i18next instance
import { getParameterByName, browserStorage, disableIosTextFieldZoom } from './common/utils';
import ThemeProvider from './theme/index';
import Login from './components5.0/SigninSimple';
import MuiXLicense from './components5.0/utils/MuiXLicense';
import CrossPlatform from './common/CrossPlatform';
import SetExpiredPassword from './app/setExpiredPassword.react';
import MobileAppRedirect from './components5.0/MobileAppRedirect';
import HistoryActions from './flux/history.actions';
import AlertsActions from './components/alerts/alerts.actions';
import LoggedUserActions from './flux/loggedUser.actions';
import LoggedUserStore from './flux/loggedUser.store';
import App from './app/app.react';
import CasePrint from './pages/case/casePrint.react';

const app = document.getElementById('app');
const root = createRoot(app);

const accessToken = getParameterByName('ext-access-token', window.location.search); // the querystring part of a URL
const refreshToken = getParameterByName('ext-refresh-token', window.location.search); // the querystring part of a URL
const uuidToken = getParameterByName('token', window.location.search); // the querystring part of a URL
const deviceToken = getParameterByName('deviceToken', window.location.search); // the querystring part of a URL
const urlPostfix = getParameterByName('url-postfix', window.location.search);
const error = getParameterByName('error', window.location.search);
const cookieExpiration = getParameterByName('expire', window.location.search);

browserHistory.listen((location) => {
    HistoryActions.saveLastPath(`${location.pathname}${location.search}`);
});

// history 4 - listen is not called after page reload, we need save current url
HistoryActions.saveLastPath(`${window.location.pathname}${window.location.search}`);

i18n.on('initialized', async () => {
    if (window.location.pathname === '/app-store-redirect') {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/android/i.test(userAgent)) {
            window.location.href = 'https://play.google.com/store/apps/details?id=app.teamassistant.android';
        } else if (/iPad|iPhone/.test(userAgent)) {
            window.location.href = 'https://apps.apple.com/us/app/team-assistant/id6503063799';
        }
    }

    // disable auto zoom on input click on iphone
    disableIosTextFieldZoom();

    await LoggedUserActions.fetchColors();

    if (
        typeof accessToken !== 'undefined' &&
        typeof refreshToken !== 'undefined' &&
        accessToken &&
        refreshToken
    ) {
        browserStorage.removeItem('vice');

        if (typeof urlPostfix !== 'undefined' && urlPostfix !== null) {
            browserHistory.push(urlPostfix);
        } else {
            browserHistory.push(document.location.pathname); // the path name of the current URL
        }
    }

    if (cookieExpiration) {
        localStorage.setItem('cookieExpiration', cookieExpiration);
    }
    LoggedUserActions.setSessionTimeout();

    if (typeof error !== 'undefined' && error !== null) {
        let parsedError;
        let serverErrorObj;
        let warning = false;

        try {
            parsedError = JSON.parse(atob(error));
            serverErrorObj = { error: parsedError };
        } catch (e) {
            if (error === 'LOGIN_ERROR') {
                parsedError = { message: i18n.t('loginError') };
                serverErrorObj = { error: { message: error } };
            } else if (error === 'USER_LOCKED') {
                warning = true;
                parsedError = { message: i18n.t('userLocked') };
                serverErrorObj = { error: { message: error } };
            } else if (error === 'USER_NOT_CACHED' || error === 'REFRESH_TOKEN_EXPIRED') {
                warning = true;
                parsedError = { message: i18n.t('alrSessionExpired') };
                serverErrorObj = { error: { message: error } };
            } else {
                parsedError = { message: error };
                serverErrorObj = { error: parsedError };
            }
        }
        AlertsActions.addAlert({
            type: warning ? 'warning' : 'alert',
            message: parsedError.message,
            serverError: serverErrorObj,
            show: true,
        });
    }

    const providerRouter = (
        <ThemeProvider>
            <MuiXLicense />
            <I18nextProvider i18n={i18n}>
                <Router history={browserHistory}>
                    <MobileAppRedirect />
                    <Switch>
                        <Route exact path="/login" component={Login} />
                        <Route exact path="/authenticate" component={Login} />
                        <Route exact path="/case-print/:caseId/:printId" component={CasePrint} />
                        <Route
                            exact
                            path="/alukov/:caseId/:printId/:selectedCaseId?/:selectedTaskId?/:tabName?"
                            component={CasePrint}
                        />
                        <Route
                            exact
                            path="/tablet/:caseId/:printId/:selectedCaseId?/:selectedTaskId?/:tabName?"
                            component={CasePrint}
                        />
                        <Route
                            exact
                            path="/gui/:caseId/:printId/:selectedCaseId?/:selectedTaskId?/:tabName?"
                            component={CasePrint}
                        />
                        <Route exact path="/set-expired-password" component={SetExpiredPassword} />
                        <Route path="/" component={App} />
                    </Switch>
                </Router>
            </I18nextProvider>
        </ThemeProvider>
    );

    // mobile app authentication sends a uuid token to backend to set cookies
    if (
        CrossPlatform.isNativeMobileApp() &&
        window.location.pathname === '/mobile-authentication' &&
        typeof uuidToken !== 'undefined' &&
        uuidToken
    ) {
        await LoggedUserActions.authenticateMobileApp(uuidToken);
        browserHistory.push('/');
    } else if (
        CrossPlatform.isNativeMobileApp() &&
        window.location.pathname === '/mobile-authentication/getFirebaseToken' &&
        typeof uuidToken !== 'undefined' &&
        uuidToken
    ) {
        await LoggedUserActions.getFirebaseToken(uuidToken);
        browserHistory.push('/');
    } else if (
        CrossPlatform.isNativeMobileApp() &&
        window.location.pathname === '/mobile-authentication/resetBadgeCounter' &&
        typeof uuidToken !== 'undefined' &&
        uuidToken
    ) {
        await LoggedUserActions.resetBadgeCounter(uuidToken);
        browserHistory.push('/');
    } else if (
        CrossPlatform.isNativeMobileApp() &&
        window.location.pathname === '/mobile-authentication/getFirebaseProject' &&
        typeof uuidToken !== 'undefined' &&
        uuidToken
    ) {
        await LoggedUserActions.getFirebaseProject();
        browserHistory.push('/');
    } else if (
        CrossPlatform.isNativeMobileApp() &&
        window.location.pathname === '/mobile-authentication/setDeviceToken' &&
        typeof uuidToken !== 'undefined' &&
        uuidToken &&
        typeof deviceToken !== 'undefined' &&
        deviceToken
    ) {
        await LoggedUserActions.setDeviceToken(uuidToken, deviceToken);
        browserHistory.push('/');
    } else if (
        !CrossPlatform.isNativeMobileApp() &&
        window.location.pathname === '/mobile-authentication'
    ) {
        browserHistory.push('/');
    }

    if (window.location.pathname === '/authenticate') {
        // Z duvodu moznosti zacykleni se nevola logged-user
        root.render(providerRouter);
    } else if (window.location.pathname === '/login') {
        // don't call '/logged-user' on login - important for case print by puppeteer
        root.render(providerRouter);
    } else {
        LoggedUserActions.fetchSettings({
            loggedUserActions: LoggedUserActions,
        })
            .then((user) => {
                // In case of ext-token login
                sessionStorage.setItem('auth.userId', user.id);
                if (!LoggedUserStore.getState().useSessionStorage) {
                    localStorage.setItem('auth.userId', user.id);
                }
                // server communicates so user params can be set (including language)
                root.render(providerRouter);
            })
            .catch((errorMessage) => {
                // whole app will be in default language (first value of config.langs)
                if (_.get(errorMessage, 'body.error.codeName')) {
                    if (errorMessage.body.error.codeName === 'INVALID_HASH') {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18n.t('alrInvLogginHash'),
                        });
                    } /* else if (errorMessage.body.error.codeName == 'TOKEN_EXPIRED') { // don't show TOKEN_EXPIRED alert
                    AlertsActions.addAlert({type: 'warning', message: i18n.t('alrLoginExpired')});
                } */
                } else if (_.get(errorMessage, 'error.codeName') === 'IN_MAINTENANCE') {
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18n.t('alrMaintenanceMsg'),
                        show: true,
                        allowCountdown: true,
                    });
                } else if (errorMessage?.message === 'REFRESH_TOKEN_EXPIRED') {
                    // don't show REFRESH_TOKEN_EXPIRED alert
                } else {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18n.t('alrUserDataLoadFailed'),
                        serverError: errorMessage,
                    });
                }

                root.render(providerRouter);
            });
    }
});
