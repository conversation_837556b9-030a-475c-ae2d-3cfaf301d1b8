import PropTypes from 'prop-types';
import React from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import loader from '@monaco-editor/loader';
import Editor from '@monaco-editor/react';
import _ from 'lodash';
import cx from 'classnames';
import acornLoose from 'acorn-loose';
import LoggedUserStore from '../../../flux/loggedUser.store';
import { register as registerDefaults } from './ideDefaults';
import { register as registerTheme } from './customTheme';
import ideHints from './ideHints';
import { guid } from '../../../common/utils';

class IDE extends React.Component {

    constructor(props) {
        super(props);

        loader.config({ paths: { vs: '/assets/libs/monaco-editor/min/vs' } });

        this.state = _.extend(
            {
                value: this.props.value || null,
                fullscreen: false,
                hasFocus: false,
                componentKey: guid(),
            },
            LoggedUserStore.getState(),
        );

        this.updateHeight = this.updateHeight.bind(this);
        this.onMount = this.onMount.bind(this);
        this.toggleFullscreen = this.toggleFullscreen.bind(this);
        this.addHints = this.addHints.bind(this);
        this.registerCompletions = this.registerCompletions.bind(this);
        this.registerInlays = this.registerInlays.bind(this);
        this.insertSnippet = this.insertSnippet.bind(this);
        this._onChange = this._onChange.bind(this);
        this.monaco = React.createRef();
        this.editor = React.createRef();
        this.editorWrapperRef = React.createRef();
        this.watchHeightAndFocus = this.watchHeightAndFocus.bind(this);
    }

    UNSAFE_componentWillMount() {
        this.id = guid();
    }

    componentDidMount() {
        LoggedUserStore.listen(this._onChange);

        if (this.props.watchFocus) {
            document.addEventListener('mousedown', this.watchHeightAndFocus);
        }
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        if (this.props.isInCurrentTab !== prevProps.isInCurrentTab) {
            // tas-1800: couldn't click on the first row after switching tabs, must force rerender
            this.setState({ componentKey: guid() });
        }

        if (
            !this.props.isInCurrentTab &&
            this.props.isInCurrentTab !== prevProps.isInCurrentTab
        ) {
            this.completions?.dispose();
        }

        if (
            this.props.isInCurrentTab &&
            this.props.isInCurrentTab !== prevProps.isInCurrentTab
        ) {
            this.addHints();
        }

        if (
            this.props.calculationsGlobalScripts !==
                prevProps.calculationsGlobalScripts ||
            !_.isEqual(this.props.vars, prevProps.vars) ||
            this.props.defaultLibs !== prevProps.defaultLibs
        ) {
            this.addHints();
        }

        if (!_.isEqual(prevProps.value, this.props.value)) {
            this.setState({ value: this.props.value });
        }
    }

    componentWillUnmount() {
        // due to shared context of editors, must dispose of editor and hints on unmount
        this.completions?.dispose();
        this.editor.current?.getModel()?.dispose();
        LoggedUserStore.unlisten(this._onChange);

        if (this.props.watchFocus) {
            document.removeEventListener('mousedown', this.watchHeightAndFocus);
        }
    }

    onMount(editor, monaco) {
        const { colors } = this.state;
        registerTheme(editor, monaco, colors);

        this.monaco.current = monaco;
        this.editor.current = editor;

        // change theme if dark mode is enabled and LoggedUserState has been delayed
        this.monaco.current.editor.setTheme(
            this.state.darkModeIsEnabled ? 'tas-dark' : 'tas-light',
        );

        this.completions?.dispose();

        this.addHints();

        // fullscreen (F11)
        this.editor.current.addAction({
            id: 'toggleFullscreenCommand',
            label: 'View: Toggle Full Screen',
            keybindings: [this.monaco.current.KeyCode.F11],
            precondition: null,
            keybindingContext: null,
            contextMenuGroupId: 'navigation',
            contextMenuOrder: 1.5,
            run: this.toggleFullscreen,
        });

        // size change listener
        if (this.props.watchHeight) {
            this.editor.current.onDidContentSizeChange(this.updateHeight);
            this.updateHeight();
        }

        // onFocus
        this.editor.current.onDidFocusEditorText(() => {
            if (typeof this.props.onFocus === 'function') {
                this.props.onFocus();
            }
        });

        // onBlur
        this.editor.current.onDidBlurEditorText(() => {
            if (typeof this.props.onBlur === 'function') {
                this.props.onBlur();
            }
        });

        if (this.props.forceLfLineEndings) {
            this.editor.current.getModel().setEOL(monaco.editor.EndOfLineSequence.LF);
        }
    }

    onChange(value) {
        this.setState({ value: value });
        if (this.props.onChange) {
            this.props.onChange(value);
        }
    }

    watchHeightAndFocus(e) {
        const editorDomElement = document
            .getElementById(this.id)
            .querySelector('.editor');

        if (!editorDomElement.contains(e.target)) {
            if (this.state.hasFocus) {
                this.setState({ hasFocus: false });
            }
            if (
                this.props.watchHeight &&
                this.props.isInCurrentTab &&
                !this.state.fullscreen
            ) {
                this.updateHeight();
            }
        } else if (!this.state.hasFocus) {
            this.setState({ hasFocus: true }, () => {
                this.addHints();
            });
            if (
                this.props.watchHeight &&
                this.props.isInCurrentTab &&
                !this.state.fullscreen
            ) {
                this.updateHeight();
            }
        }
    }

    updateHeight() {
        if (!this.state.fullscreen) {
            const contentHeight = this.editor.current.getContentHeight();
            const maxHeight = 620;
            const minHeight = 200;
            const height = Math.min(
                Math.max(contentHeight, minHeight),
                maxHeight,
            );

            const width =
                this.editorWrapperRef.current?.getBoundingClientRect()?.width;

            if (width) {
                this.editorWrapperRef.current.style.height = `${height}px`;

                this.editor.current.layout({ width, height });
            }
        }
    }

    _onChange() {
        this.setState(LoggedUserStore.getState());
    }

    addHints() {
        this.inlays = [];
        if (
            this.monaco.current &&
            this.props.isInCurrentTab &&
            (!this.props.watchFocus || this.state.hasFocus)
        ) {
            const { hints, calculationES } = this.props;
            const libs = [
                {
                    filePath: 'lib.es6.d.ts',
                },
            ];

            // register sandbox
            if (this.props.defaultLibs) {
                this.props.defaultLibs.forEach((item) => {
                    libs.push({ content: item.default });
                });
            }

            if (hints === 'conditionsScript') {
                libs.push({
                    content: ideHints.conditionsScript,
                });
            } else if (hints === 'templateTaskCalculations') {
                // register variables
                const vars = this.props.vars.filter(
                    (varObj) => varObj.name || varObj.title,
                );
                const varAttrs = vars.map((variable) => {
                    return `'${variable.name || variable.title}': new IVariableApi();`;
                });
                const lib = `
                    const vars = {${varAttrs.join('\n')}};
                `;

                libs.push({ content: lib });

                // register global scripts with inlays and hover provider
                if (this.props.calculationsGlobalScripts) {
                    this.props.calculationsGlobalScripts.forEach((script) => {
                        if (script && calculationES) {
                            // extract names for each function and variable in the top level of a script
                            // to be used for inlay hints and hover provider
                            const declarations = {};
                            const code = script[calculationES];

                            const ast = acornLoose.parse(code, {
                                sourceType: 'script',
                                ecmaVersion: 2020,
                                locations: true,
                                onComment: (block, text, start, end, startLoc, endLoc) => {
                                    if (block) {
                                        declarations.comments = declarations.comments || [];
                                        declarations.comments.push({
                                            text,
                                            start,
                                            end,
                                            startLoc,
                                            endLoc,
                                        });
                                    }
                                },
                            });

                            const walk = (node) => {
                                let jsDoc = '';
                                const comment = declarations?.comments?.find((comm) => comm.endLoc.line === node.loc.start.line - 1);
                                if (node.type === 'VariableDeclaration') {
                                    node.declarations.forEach((decl) => {
                                        if (decl.id?.type === 'Identifier') {
                                            const codeSlice = code.slice(
                                                node.start,
                                                node.end,
                                            );
                                            if (comment) {
                                                jsDoc = `/**\n${comment.text}\n\n*${script.name} global script\n\n***SOURCE:**\n\`\`\`\n${codeSlice}\n\`\`\`\n\n*/\n`;
                                            } else if (!jsDoc) {
                                                jsDoc = `/**\n*${script.name} global script\n\n***SOURCE:**\n\`\`\`\n${codeSlice}\n\`\`\`\n\n*/\n`;
                                            }

                                            declarations[decl.id.name] = `${jsDoc}${codeSlice}`;
                                        }
                                    });
                                } else if (node.type === 'FunctionDeclaration') {
                                    if (node.id && node.id?.type === 'Identifier') {
                                        const codeSlice = code.slice(
                                            node.start,
                                            node.end,
                                        );
                                        if (comment) {
                                            jsDoc = `/**\n${comment.text}\n\n*${script.name} global script\n\n***SOURCE:**\n\`\`\`\n${codeSlice}\n\`\`\`\n\n*/\n`;
                                        } else if (!jsDoc) {
                                            jsDoc = `/**\n*${script.name} global script\n\n***SOURCE:**\n\`\`\`\n${codeSlice}\n\`\`\`\n\n*/\n`;
                                        }

                                        declarations[node.id.name] = `${jsDoc}${codeSlice}`;
                                    }
                                }
                            };

                            ast.body.forEach((node) => {
                                if (
                                    node.type === 'VariableDeclaration' ||
                                    node.type === 'FunctionDeclaration'
                                ) {
                                    walk(node);
                                }
                            });

                            Object.keys(declarations).forEach((key) => {
                                if (key !== 'comments') {
                                    this.registerInlays(key, script.name);
                                    libs.push({ content: declarations[key] });
                                }
                            });
                        }
                    });
                }
            }

            this.registerCompletions();
            registerDefaults(
                this.editor.current,
                this.monaco.current,
                this.props.language,
                libs,
                this.props.isConsole,
            );
        }
    }

    registerCompletions() {
        const { hints } = this.props;
        let varsSuggestions = [];
        let snippetSuggestions = [];

        if (!_.isEmpty(this.props.vars)) {
            varsSuggestions = this.props.vars.map((varObj) => {
                return {
                    label: varObj.displayText || varObj.title,
                    kind: this.monaco.current.languages.CompletionItemKind
                        .Snippet,
                    detail: varObj.text || varObj.value,
                    documentation: 'Variable',
                    insertText: varObj.text || varObj.value,
                };
            });
        }

        if (!_.isEmpty(ideHints[hints]) && Array.isArray(ideHints[hints])) {
            const snippets = ideHints[hints];
            snippetSuggestions = snippets.map((hint) => {
                return {
                    label: hint.displayText,
                    kind: this.monaco.current.languages.CompletionItemKind
                        .Snippet,
                    detail: hint.displayText,
                    documentation: hint.text,
                    insertText: hint.text,
                };
            });
        }

        const suggestions = varsSuggestions.concat(snippetSuggestions);

        this.completions?.dispose();
        this.completions =
            this.monaco.current.languages.registerCompletionItemProvider(
                this.props.language === 'jsx'
                    ? 'javascript'
                    : this.props.language,
                {
                    provideCompletionItems() {
                        return {
                            suggestions: suggestions,
                        };
                    },
                },
            );
    }

    registerInlays(key, label) {
        this.inlays.push({
            key,
            label,
        });
    }

    toggleFullscreen() {
        if (
            this.state.fullscreen &&
            document.body.classList.contains('editor-fullscreen')
        ) {
            document.body.classList.remove('editor-fullscreen');
        } else if (
            !this.state.fullscreen &&
            !document.body.classList.contains('editor-fullscreen')
        ) {
            document.body.classList.add('editor-fullscreen');
        }

        this.setState({ fullscreen: !this.state.fullscreen }, () => {
            if (this.props.watchHeight && !this.state.fullscreen) {
                this.updateHeight();
            }
        });
    }

    insertSnippet(variable) {
        const selection = this.editor.current.getSelection();
        this.editor.current.executeEdits('', [
            {
                range: new this.monaco.current.Range(
                    selection.selectionStartLineNumber,
                    selection.selectionStartColumn,
                    selection.endLineNumber,
                    selection.endColumn,
                ),
                text: variable,
                forceMoveMarkers: true,
            },
        ]);
    }

    render() {
        const options = {
            automaticLayout: true,
            inlayHints: {
                enabled: true,
                fontSize: 12,
                padding: 8,
            },
            scrollBeyondLastLine: false,
            scrollbar: {
                alwaysConsumeMouseWheel: false,
            },
        };

        if (this.props.readonly) {
            options.readOnly = true;
            options.lineNumbers = 'off';
        }

        // console should have no JS hints except given sandbox constants
        if (this.props.isConsole) {
            options.suggest = {
                showClasses: false,
                showKeywords: false,
                showModules: false,
                showVariables: false,
            };
        }

        const value = this.state.value === null ? this.props.value : this.state.value;

        const key = this.props.readonly ? 'readonlyEditor' : 'writableEditor';

        return (
            <div id={this.id}>
                <Editor
                    key={`${key}-${this.state.componentKey}`}
                    className={cx('editor', {
                        fullscreen: this.state.fullscreen,
                    })}
                    height={this.props.watchHeight ? '200px' : this.props.height}
                    wrapperProps={
                        this.props.watchHeight
                            ? {
                                  ref: this.editorWrapperRef,
                                  style: {
                                      width: '100%',
                                      minHeight: '200px',
                                  },
                              }
                            : {}
                    }
                    defaultLanguage={
                        this.props.language === 'jsx' ? 'javascript' : this.props.language
                    }
                    value={this.props.isInCurrentTab ? value : ''}
                    theme={this.state.darkModeIsEnabled ? 'tas-dark' : 'tas-light'}
                    onMount={this.onMount}
                    onChange={(val) => {
                        this.registerCompletions();
                        this.onChange(val);
                    }}
                    onValidate={this.props.onValidate}
                    options={options}
                />
            </div>
        );
    }

}

IDE.defaultProps = {
    value: null,
    hints: null,
    defaultLibs: null,
    calculationsGlobalScripts: [],
    calculationES: null,
    onChange: null,
    onValidate: null,
    readonly: false,
    language: 'javascript',
    vars: [],
    height: '200px',
    watchHeight: false,
    isInCurrentTab: true,
    watchFocus: false,
    onFocus: null,
    onBlur: null,
    forceLfLineEndings: false,
};

IDE.propTypes = {
    value: PropTypes.string,
    height: PropTypes.string,
    hints: PropTypes.oneOf([
        'dynRowsScript',
        'dynRowsTable',
        'conditionsScript',
        'templatePrint',
        'healthStatus',
        'templatePrintReact',
        'templateTaskCalculations',
    ]),
    defaultLibs: PropTypes.arrayOf(PropTypes.object),
    calculationsGlobalScripts: PropTypes.arrayOf(PropTypes.object),
    calculationES: PropTypes.oneOf(['js', 'js_es6']),
    onChange: PropTypes.func,
    onValidate: PropTypes.func,
    readonly: PropTypes.bool,
    language: PropTypes.string,
    vars: PropTypes.arrayOf(PropTypes.object),
    watchHeight: PropTypes.bool,
    isInCurrentTab: PropTypes.bool,
    watchFocus: PropTypes.bool,
    isConsole: PropTypes.bool,
    onFocus: PropTypes.func,
    onBlur: PropTypes.func,
    forceLfLineEndings: PropTypes.bool,
};

export default IDE;
