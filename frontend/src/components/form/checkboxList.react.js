import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Waypoint } from 'react-waypoint';
import { guid } from '../../common/utils';
import { checkRevision } from '../../pages/uploadDocumentUtil';
import Dropzone from '../dropzone.react';
import ApiRequest from '../../api/apiRequest';
import React from 'react';
import cx from 'classnames';
import Formsy from 'formsy-react';
import Loader from 'react-loader';
import _ from 'lodash';
import createReactClass from 'create-react-class';
import AlertsActions from '../alerts/alerts.actions';
import ComponentMixin from './Formsy/component';
import Component<PERSON>abel from './componentLabel.react';
import FormsyLikeMixin from './Formsy/formsyLikeMixin';

const checkboxListObj = {
    mixins: [FormsyLikeMixin, ComponentMixin],
    propTypes: {
        options: PropTypes.arrayOf(PropTypes.object),
        caseId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        taskId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        parent: PropTypes.object,
        readonly: PropTypes.bool,
        dropzone: PropTypes.bool,
        dropdown: PropTypes.bool,
        inline: PropTypes.bool,
        componentStyle: PropTypes.objectOf(PropTypes.string),
        helperText: PropTypes.string,
        upperLabel: PropTypes.bool,
        withPreview: PropTypes.bool.isRequired,
        checkboxListOnBlur: PropTypes.func,
        emptyOptionsText: PropTypes.string,
        noInternalSetValue: PropTypes.bool,
        addWaypoint: PropTypes.bool, // Easy loading - ads waypoint below  options
        onEnterWaypoint: PropTypes.func,
        attachmentsFilter: PropTypes.string, // filter for attachments services
        onChangeIndividually: PropTypes.func,
        valueTypeString: PropTypes.bool, // multiBoxSelectionModal
    },

    getDefaultProps: function () {
        return {
            readonly: false,
            dropzone: false,
            dropdown: false,
            inline: false,
            helperText: null,
            upperLabel: false,
            withPreview: false,
            checkboxListOnBlur: null,
            emptyOptionsText: null,
            noInternalSetValue: false,
            addWaypoint: false,
            onEnterWaypoint: null,
            attachmentsFilter: null,
            onChangeIndividually: null,
            valueTypeString: false,
        };
    },

    getInitialState: function () {
        return {
            options: this.props.options,
            attachmentsCount: null,
            loading: false,
            isOpen: false,
            windowHeight: window.innerHeight,
            openUp: false,
            listHeight: 0,
            checkboxListFocused: false,
            runDynRowsEvalOnBlur: false,
            dynRowsToListenTo: [],
            value: this.props.value,
            displayed: {},
        };
    },

    componentDidMount: function () {
        if (typeof this.props.taskId !== 'undefined') {
            this.loadAttachments(
                'tasks',
                this.props.taskId,
                this.props.attachmentsFilter,
            );
        } else if (typeof this.props.caseId !== 'undefined') {
            this.loadAttachments(
                'processes',
                this.props.caseId,
                this.props.attachmentsFilter,
            );
        }

        if (
            this.props.dropdown ||
            typeof this.props.checkboxListOnBlur === 'function'
        ) {
            document.addEventListener('click', this.documentClick);
            window.addEventListener('resize', this.handleResize);
        }

        if (!_.isEmpty(this.props.drWatchVariables)) {
            this.props.drWatchVariables.forEach((dr) => {
                if (dr.watchVars.indexOf(this.props.tvarName) > -1) {
                    if (this.props.readonly) {
                        // console.warn(`!! Proměnná ${this.props.tvarName} je do úkolu namapována pro čtení, změňte prosím její povinnost pro správné fungování watchVariables.`)
                    } else {
                        this.addChangeListener(dr.drName);
                    }
                }
            });
        }
    },

    componentWillUnmount() {
        document.removeEventListener('click', this.documentClick);
        window.removeEventListener('resize', this.handleResize);

        if (this.attachmentsRequest) {
            this.attachmentsRequest.cancel();
        }
    },

    UNSAFE_componentWillReceiveProps: function (nextProps) {
        if (this.props.options !== nextProps.options) {
            this.setState({ options: nextProps.options });
        }

        if (!_.isEqual(this.props.value, nextProps.value)) {
            this.setState({ value: nextProps.value });
        }

        if (this.props.attachmentsCount !== nextProps.attachmentsCount) {
            if (this.props.taskId) {
                this.reloadAttachments(
                    'tasks',
                    nextProps.attachmentsCount,
                    this.props.taskId,
                    nextProps.attachmentsFilter,
                );
            } else if (this.props.caseId) {
                this.reloadAttachments(
                    'processes',
                    nextProps.attachmentsCount,
                    this.props.caseId,
                    nextProps.attachmentsFilter,
                );
            }
        }

        if (this.props.attachmentsFilter !== nextProps.attachmentsFilter) {
            if (this.props.taskId) {
                this.loadAttachments(
                    'tasks',
                    this.props.taskId,
                    nextProps.attachmentsFilter,
                );
            } else if (this.props.caseId) {
                this.loadAttachments(
                    'processes',
                    this.props.caseId,
                    nextProps.attachmentsFilter,
                );
            }
        }
    },

    reloadAttachments: function (type, count, id, filter) {
        if (
            count !== null &&
            this.state.attachmentsCount !== null &&
            count !== this.state.attachmentsCount
        ) {
            this.loadAttachments(type, id, filter);
        }
    },

    loadAttachments: function (path, id, filter) {
        this.setState({ loading: true });

        if (this.attachmentsRequest) {
            this.attachmentsRequest.cancel();
        }

        let url = `/${path}/${id}/attachments`;

        if (filter) {
            url += `?filter=${encodeURIComponent(filter)}&order=name`;
        } else {
            url += `?order=name&limit=${config.restLimit}`;
        }

        url += '&total_count=false';

        this.attachmentsRequest = ApiRequest.get(url)
            .then((payload) => {
                if (payload) {
                    const options = [];
                    for (const key in payload.items) {
                        const value = payload.items[key];
                        const option = {
                            value: value.name,
                            title: value.name,
                            dmsf_id: value.id,
                        };
                        options.push(option);
                    }

                    if (this.props.withPreview && !_.isEmpty(options)) {
                        if (!_.isEmpty(this.props.value)) {
                            const firstSelected = _.find(
                                options,
                                ['title', this.props.value[0]],
                            );
                            this.changePreview(firstSelected);
                        } else {
                            this.changePreview(options[0]);
                        }
                    }

                    this.setState({
                        options: options,
                        attachmentsCount: payload.items.length,
                        loading: false,
                    });

                    // if a document from this.state.value was deleted before and thus can't be checked,
                    // reset value due to the right validation of "required"
                    if (payload.items.length === 0 && this.state.value) {
                        this.setState({ value: [] });
                        this.setValue();
                    } else if (!_.isEmpty(options)) {
                        let isAnyChecked = false;

                        options.map((checkbox, key) => {
                            let checked;
                            if (!_.isEmpty(this.state.value)) {
                                // if default value -> option is checked
                                checked =
                                    this.state.value.indexOf(checkbox.value) !==
                                    -1;
                                if (
                                    checked &&
                                    _.find(
                                        this.state.options,
                                        ['value', checkbox.value],
                                    )
                                ) {
                                    isAnyChecked = true;
                                }
                            }
                        });

                        if (!isAnyChecked && !_.isEmpty(this.state.value)) {
                            this.setState({ value: [] });
                            this.setValue();
                        }
                    }

                    if (this.props.withPreview && payload.items.length === 0) {
                        // when changing the filter and if there are no files, reset the preview file
                        this.changePreview(null);
                    }
                }
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrLoadAttachmentsFailed'),
                    serverError: errorMessage,
                });
            });
    },

    // drag&drop over dropzone in task form
    onLoad: function (file, readerEvt) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            hasSpinner: true,
            type: 'info',
            message: i18next.t('alrDocumentAdding'),
        });

        const { taskId } = this.props;
        const { caseId } = this.props;
        const { tabActionAttach } = this.props.parent;

        const checkUploaded = () => {
            const index = this.state.options.length - 1;
            this.refs[`${this.props.name}-${index}`].checked = true;
            this.changeCheckboxList();
        };

        const formData = new FormData();
        const filename = file.name;

        // text parameters must be first! http://sailsjs.com/documentation/reference/request-req/req-file
        formData.append('filename', filename);
        formData.append('iprocId', caseId);
        if (taskId) formData.append('itaskId', taskId);
        // the file itself must be the last appended param
        formData.append('file', file);

        ApiRequest.postFile('/dms/upload', formData, file, caseId)
            .then((payload) => {
                const optionsClone = Array.isArray(this.state.options)
                    ? _.cloneDeep(this.state.options)
                    : [];
                if (!_.find(optionsClone, ['value', filename])) {
                    optionsClone.push({
                        value: filename,
                        title: filename,
                        dmsf_id: payload.id,
                    });
                }

                this.setState({ options: optionsClone }, checkUploaded);

                if (taskId) {
                    tabActionAttach.getLabelCount({
                        apiUrl: `/tasks/${taskId}/attachments`,
                    });
                    this.loadAttachments(
                        'tasks',
                        taskId,
                        this.props.attachmentsFilter,
                    );
                } else {
                    tabActionAttach.getLabelCount({
                        apiUrl: `/processes/${caseId}/attachments`,
                    });
                    this.loadAttachments(
                        'processes',
                        caseId,
                        this.props.attachmentsFilter,
                    );
                }

                const fileId = payload.id;

                const folder = 'current'; // do not rewrite revision's folder

                checkRevision(
                    fileId,
                    alertId,
                    null,
                    folder,
                    payload.message,
                ).then((metadataArr) => {
                    this.postMetadata(
                        metadataArr,
                        alertId,
                        fileId,
                        payload.message,
                    );
                });
            })
            .catch((errorMessage) => {
                if (
                    _.get(errorMessage, 'error.codeName') ===
                    'VICE_VIEW_ONLY_ERROR'
                ) {
                    AlertsActions.removeButtonAlert(alertId);
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('alrNoPermsToAddDocInVice'),
                        show: true,
                        allowCountdown: true,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachSaveFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    },

    postMetadata(metadataArr, alertId, fileId, additionalMessage) {
        ApiRequest.post(
            `/dms/file/${fileId}/metadata`,
            JSON.stringify(metadataArr),
        )
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachSaved'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrAttachMetaFailed'),
                    serverError: errorMessage,
                });
            });
    },

    changeCheckboxList: function (event) {
        const value = [];
        this.state.options.forEach((option, i) => {
            // if any option is checked save to value
            if (this.refs[`${this.props.name}-${i}`].checked) {
                value.push(option.value);
            }
        });

        if (!this.props.noInternalSetValue) {
            this.setState({
                value: value,
                isUntouched: false,
            });
            this.setValue(value);
        }

        this.props.onChange(this.props.name, value, this); // affect other component

        // returns whole object and information if changed item is checked
        if (typeof this.props.onChangeIndividually === 'function') {
            const valueObj = {
                title: event.target.getAttribute('data-title'),
                value: this.props.valueTypeString
                    ? event.target.value
                    : Number(event.target.value),
            };
            this.props.onChangeIndividually(
                this.props.name,
                valueObj,
                event.target.checked,
                this,
            );
        }

        this.evalOnBlur();
    },

    evalOnBlur: function () {
        let checked;
        if (!_.isEmpty(this.state.options)) {
            this.state.options.map((checkbox, i) => {
                if (!_.isEmpty(this.state.value)) {
                    // if default value -> option is checked
                    checked = this.state.value.indexOf(checkbox.value) !== -1;
                } else {
                    // no default value
                    checked = false;
                }
            });
        }

        if (this.state.runDynRowsEvalOnBlur) {
            for (let i = 0; i < this.state.dynRowsToListenTo.length; i++) {
                this.props.onBlur(
                    this.props.name,
                    checked,
                    this,
                    this.state.dynRowsToListenTo[i],
                );
            }
        } else if (this.props.onBlur && !this.props.readonly) {
            this.props.onBlur(this.props.name, checked, this);
        }
    },

    open() {
        this.setState(
            {
                isOpen: !this.state.isOpen,
            },
            () => {
                if (this.dropdownButton) {
                    const dropDownPos =
                        this.dropdownButton.getBoundingClientRect().bottom; // bottom edge of dropdown from the top
                    const dropdownListHeight =
                        this.dropdownButton.querySelector(
                            '.dropdown-list',
                        ).offsetHeight;
                    this.setState({
                        openUp:
                            dropDownPos + dropdownListHeight + 35 >
                            this.state.windowHeight,
                    });
                }
            },
        );
    },

    // eslint-disable-next-line consistent-return
    documentClick(e) {
        if (this.dropdownButton && !this.dropdownButton.contains(e.target)) {
            this.setState({ isOpen: false });
        }

        // t3f-911 Interaktivní identifikace nevyplněných proměnných ve formuláři úkolu
        const checkboxList = this.checkboxListDivRef;

        if (checkboxList && checkboxList.contains(e.target)) {
            this.setState({ checkboxListFocused: true });
        }

        if (
            this.state.checkboxListFocused &&
            checkboxList &&
            !checkboxList.contains(e.target)
        ) {
            if (typeof this.props.checkboxListOnBlur === 'function') {
                this.props.checkboxListOnBlur(this.props.name);
                this.setState({ checkboxListFocused: false });
            }
        }

        // t3b-2743 Duplicitní zobrazení názvu dokumentu po drag and drop souboru do proměnné seznam dokumentů
        // In order to invoke file insertion by clicking on empty dropdown, it must be like this
        if (this.dropdownDropzoneRef && this.dropdownDropZoneTextRef) {
            const { options } = this.state;
            const targetParent = e.target?.parentNode; // click on dropdown arrow icon wrap
            const targetParent2 = targetParent?.parentNode; // click on dropdown arrow

            if (!targetParent || !targetParent2) {
                return false;
            }

            if (
                (_.isEmpty(options) &&
                    (this.dropdownButton.contains(targetParent) ||
                        this.dropdownButton.contains(targetParent2) ||
                        this.dropdownDropZoneTextRef.contains(e.target))) ||
                (!_.isEmpty(options) &&
                    this.dropdownDropZoneTextRef.contains(e.target))
            ) {
                this.dropdownDropzoneRef.open();
            }
        }
    },

    handleResize: function () {
        this.setState({ windowHeight: window.innerHeight });

        if (this.dropdownButton) {
            const dropDownPos =
                this.dropdownButton.getBoundingClientRect().bottom; // bottom edge of dropdown from the top
            const dropdownListHeight =
                this.dropdownButton.querySelector(
                    '.dropdown-list',
                ).offsetHeight;
            this.setState({
                openUp:
                    dropDownPos + dropdownListHeight + 35 >
                    this.state.windowHeight,
            });
        }
    },

    changePreview(file) {
        this.setState({ displayed: file || {} }); // can be null
        if (typeof file !== 'undefined') {
            this.props.changePreview(file);
        }
    },

    setHeight(height) {
        this.setState({ listHeight: height });
    },

    addChangeListener: function (dynRowsName) {
        this.setState((prevState) => ({
            runDynRowsEvalOnBlur: true,
            dynRowsToListenTo: [...prevState.dynRowsToListenTo, dynRowsName],
        }));
    },

    onEnterWaypoint() {
        if (typeof this.props.onEnterWaypoint === 'function') {
            this.props.onEnterWaypoint();
        }
    },

    render: function () {
        // if input is invalid
        if (this.showErrors()) {
            var showErrors = (
                <div className="validation-error">{this.getErrorMessage()}</div>
            );
        }

        let content;
        if (this.props.dropdown) {
            const inputValue = [];

            if (this.state.options) {
                this.state.options.map((checkbox, i) => {
                    if (!_.isEmpty(this.state.value)) {
                        // if default value -> option is checked
                        if (this.state.value.indexOf(checkbox.value) !== -1) {
                            inputValue.push(checkbox.title);
                        }
                    }
                });
            }

            content = (
                <div
                    className={cx({
                        'small-12 columns flex': this.props.inline,
                    })}
                >
                    <div
                        className="checkbox-list-with-dropdown mar"
                        onClick={this.open}
                        ref={(c) => (this.dropdownButton = c)}
                    >
                        <input
                            title={inputValue.join(', ')}
                            ref="input"
                            type="text"
                            value={inputValue.join(', ')}
                            className="pointer"
                            readOnly
                        />
                        <div className="icon-wrap">
                            <span className="pointer icon icon-arrow-65" />
                        </div>
                        <div
                            className={cx('dropdown-list checkbox-list', {
                                'open-dropdown': this.state.isOpen,
                                'open-up': this.state.openUp,
                            })}
                        >
                            {this.renderElement()}
                        </div>
                    </div>
                </div>
            );

            if (this.props.dropzone) {
                const infoText = _.isEmpty(this.state.options)
                    ? 'noAttach'
                    : 'clickToAddAttach';

                content = (
                    <div className="doc-dropzone-dropdown">
                        <Dropzone
                            ref={(r) => {
                                this.dropdownDropzoneRef = r;
                            }}
                            onLoad={this.onLoad}
                            className="file"
                            activeClassName="file-active attach-list"
                            disableClick // opens via ref in documentClick
                        >
                            {content}
                            <div
                                ref={(r) => {
                                    this.dropdownDropZoneTextRef = r;
                                }}
                                className="no-checkbox"
                            >
                                {i18next.t(
                                    this.props.emptyOptionsText
                                        ? this.props.emptyOptionsText
                                        : infoText,
                                )}
                            </div>
                        </Dropzone>
                        {this.props.children}
                    </div>
                );
            }
        } else if (this.props.dropzone) {
            content = (
                <div className="doc-dropzone-no-dropdown">
                    <Dropzone
                        onLoad={this.onLoad}
                        className="file"
                        activeClassName={cx('file-active attach-list', {
                            'with-preview': this.props.withPreview,
                        })}
                    >
                        {_.isEmpty(this.state.options) && (
                            <span className="no-checkbox">
                                {i18next.t(
                                    this.props.emptyOptionsText
                                        ? this.props.emptyOptionsText
                                        : 'noAttach',
                                )}
                            </span>
                        )}
                        {!_.isEmpty(this.state.options) && (
                            <span className="no-checkbox">
                                {i18next.t(
                                    this.props.emptyOptionsText
                                        ? this.props.emptyOptionsText
                                        : 'clickToAddAttach',
                                )}
                            </span>
                        )}
                    </Dropzone>
                    {this.renderElement(
                        this.props.withPreview ? 'medium-5 columns' : '',
                    )}
                    {this.props.children}
                </div>
            );
        } else {
            content = (
                <div
                    className={cx({
                        'small-12 columns flex': this.props.inline,
                    })}
                >
                    {this.renderElement()}
                </div>
            );
        }

        const style = { ...this.props.componentStyle } || {};
        style.backgroundColor = this.props.bgColor;

        return (
            <div
                className={cx(
                    'row checkbox-list',
                    this.getExpertModeCxClasses(),
                    { 'not-valid': this.showErrors() },
                )}
                data-type={this.props.dataType}
                data-id={this.props.dataId}
                data-side={this.props.dataSide}
                data-name={this.props.name}
                onClick={this.props.onClick}
                style={style}
            >
                {this.props.label && (
                    <ComponentLabel
                        label={this.props.label}
                        required={this.props.required}
                        cursive={this.props.cursive}
                        fontSize={this.props.fontSize}
                        fontColor={this.props.fontColor}
                        helperText={this.props.helperText}
                        upperLabel={this.props.upperLabel}
                    />
                )}
                <div
                    className={cx(
                        'small-12 end columns checkbox-list-content',
                        {
                            'medium-7':
                                this.props.label && !this.props.upperLabel,
                        },
                    )}
                    ref={(r) => {
                        this.checkboxListDivRef = r;
                    }}
                >
                    <Loader
                        loaded={!this.state.loading}
                        scale={0.8}
                        left="2.1875rem"
                    />
                    {content}
                    {showErrors}
                </div>
            </div>
        );
    },

    renderElement: function (classes) {
        if (!_.isEmpty(this.state.options)) {
            return (
                <div
                    className={classes}
                    style={
                        this.props.withPreview &&
                        !this.props.dropdown &&
                        window.innerWidth > 640
                            ? { height: `${this.state.listHeight / 16}rem` }
                            : {}
                    }
                >
                    {this.state.options.map((checkbox, i) => {
                        let checked;
                        if (!_.isEmpty(this.state.value)) {
                            // if default value -> option is checked
                            checked =
                                this.state.value.indexOf(checkbox.value) !== -1;
                        } else {
                            // no default value
                            checked = false;
                        }
                        return (
                            <div
                                className={cx({
                                    'h-25': !this.props.withPreview,
                                })}
                                key={i}
                                style={checkbox.hide ? { display: 'none' } : {}}
                            >
                                <input
                                    type="checkbox"
                                    className="checkbox-custom"
                                    ref={`${this.props.name}-${i}`}
                                    id={`${this.props.name}-${i}`}
                                    name={`${this.props.name}-${i}`}
                                    value={checkbox.value}
                                    checked={checked}
                                    onChange={this.changeCheckboxList}
                                    disabled={this.props.readonly}
                                    data-title={checkbox.title}
                                />
                                <label
                                    title={
                                        this.props.dropdown
                                            ? checkbox.title
                                            : ''
                                    }
                                    htmlFor={`${this.props.name}-${i}`}
                                    className={cx(
                                        'checkbox-custom-label icon icon-check-3 fw-400',
                                        {
                                            'with-preview':
                                                this.props.withPreview,
                                        },
                                        { readonly: this.props.readonly },
                                        /* "disable-selection" */
                                    )}
                                    style={
                                        this.props.withPreview
                                            ? {
                                                wordBreak: 'break-all',
                                                display: 'inline',
                                            }
                                            : {}
                                    }
                                >
                                    {checkbox.title}
                                </label>
                                {this.props.withPreview && (
                                    <span
                                        className="icon-preview-1"
                                        onClick={this.changePreview.bind(
                                            null,
                                            checkbox,
                                        )}
                                        style={{
                                            verticalAlign: 'middle',
                                            marginLeft: this.props.dropdown
                                                ? '0.3125rem'
                                                : '0',
                                        }}
                                    >
                                        {checkbox.dmsf_id ===
                                            this.state.displayed.dmsf_id &&
                                            i18next.t('selectedPreview')}
                                    </span>
                                )}
                            </div>
                        );
                    })}
                    {this.props.addWaypoint && (
                        <div className="waypoint-wrap">
                            <span className="icon-arrow-down" />
                            <div className="waypoint">
                                <Waypoint onEnter={this.onEnterWaypoint} />
                            </div>
                        </div>
                    )}
                </div>
            );
        }
    },
};

const CheckboxListPure = createReactClass(checkboxListObj);

checkboxListObj.mixins = [Formsy.Mixin, ComponentMixin];

const CheckboxList = createReactClass(checkboxListObj);

export { CheckboxList, CheckboxListPure };
