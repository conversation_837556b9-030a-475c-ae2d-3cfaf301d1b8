import i18next from "i18next";
import PropTypes from "prop-types";
import React from "react";
import Formsy from "formsy-react";
import createReactClass from "create-react-class";
import cronParser from "cron-parser"; // todo - jiny nebo prelozit?
import cronstrue from "cronstrue/i18n";
import { Text } from "./text.react";
import ComponentMixin from "./Formsy/component";
import Label from "./label.react";
import LoggedUserStore from "../../flux/loggedUser.store";

const CronRepeat = createReactClass({
    propTypes: {
        hasSeconds: PropTypes.bool,
        value: PropTypes.string,
    },

    mixins: [Formsy.Mixin, ComponentMixin],

    getDefaultProps() {
        return {
            hasSeconds: true, // todo
        };
    },

    getInitialState() {
        return {
            helpType: "",
            cronValue:
                this.props.value ||
                (this.props.hasSeconds ? "* * * * * *" : "* * * * *"), // todo
            phraseStyle: {},
            isValid: true,
            readableFormat: "",
        };
    },

    UNSAFE_componentWillMount() {
        let readableFormat;
        const lang = LoggedUserStore.getState().userLanguage;

        try {
            cronParser.parseExpression(this.state.cronValue);
            readableFormat = cronstrue.toString(this.state.cronValue, {
                locale: lang,
            });
            this.setState({ phraseStyle: {} });
        } catch (err) {
            readableFormat = `Error: ${err.message || err}`; // todo jazyky
            this.setState({ phraseStyle: { color: "red" }, isValid: false });
        }

        this.setState({
            readableFormat: readableFormat,
        });
    },

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (this.props.value !== nextProps.value && nextProps.value !== null) {
            let readableFormat;
            const lang = LoggedUserStore.getState().userLanguage;

            try {
                cronParser.parseExpression(nextProps.value);
                readableFormat = cronstrue.toString(nextProps.value, {
                    locale: lang,
                });
                this.setState({ phraseStyle: {} });
            } catch (err) {
                readableFormat = `Error: ${err.message || err}`; // todo jazyky
                this.setState({
                    phraseStyle: { color: "red" },
                    isValid: false,
                });
            }

            this.setState({
                readableFormat: readableFormat,
                cronValue: nextProps.value,
            });
        }
    },

    getPartValue(index) {
        const arr = this.state.cronValue.split(" ");
        return arr[index];
    },

    getNewValue(index, value) {
        const arr = this.state.cronValue.split(" ");
        arr[index] = value;
        if (this.props.hasSeconds) {
            return arr.slice(0, 6).join(" "); // just 6 groups
        }
        return arr.slice(0, 5).join(" "); // just 5 groups
    },

    handleFocus(type) {
        this.setState({ helpType: type });
    },

    changeValue(name, partialValue) {
        let value = partialValue;

        if (value) {
            value = value.trim();
        }

        if (value === "" || typeof value === "undefined" || value === null) {
            value = "*";
        }
        const lang = LoggedUserStore.getState().userLanguage;
        let newValue;
        let readableFormat;

        switch (name) {
            case "inputSeconds":
                newValue = this.getNewValue(0, value);
                break;
            case "inputMinutes":
                newValue = this.getNewValue(1, value);
                break;
            case "inputHours":
                newValue = this.getNewValue(2, value);
                break;
            case "inputDays":
                newValue = this.getNewValue(3, value);
                break;
            case "inputMonths":
                newValue = this.getNewValue(4, value);
                break;
            case "inputWeeks":
                newValue = this.getNewValue(5, value);
                break;
            default:
                break;
        }

        try {
            cronParser.parseExpression(newValue);
            readableFormat = cronstrue.toString(newValue, { locale: lang });
            this.setState({ phraseStyle: {}, isValid: true });
        } catch (err) {
            readableFormat = `Error: ${err.message || err}`; // todo jazyky
            this.setState({ phraseStyle: { color: "red" }, isValid: false });
        }
        this.setState({
            readableFormat: readableFormat,
            cronValue: newValue,
        });

        this.setValue(newValue); // due to the right Form function of formChanged()

        // this.props.onChange(this.props.name, newValue, this);
    },

    render() {
        return (
            <div className="row">
                <Label
                    key="label"
                    value={this.state.readableFormat}
                    style={this.state.phraseStyle}
                    side="left"
                />
                {this.props.hasSeconds && (
                    <Text
                        key="inputSeconds"
                        name="inputSeconds"
                        style={{ textAlign: "center" }}
                        upperLabel
                        fullLabel
                        value={this.getPartValue(0)}
                        label={i18next.t("seconds")}
                        onChange={this.changeValue}
                        onFocus={this.handleFocus.bind(null, "s")}
                        minimal
                    />
                )}
                <Text
                    key="inputMinutes"
                    name="inputMinutes"
                    style={{ textAlign: "center" }}
                    upperLabel
                    fullLabel
                    value={this.getPartValue(1)}
                    label={i18next.t("minutes")}
                    onChange={this.changeValue}
                    onFocus={this.handleFocus.bind(null, "m")}
                    minimal
                />
                <Text
                    key="inputHours"
                    name="inputHours"
                    style={{ textAlign: "center" }}
                    upperLabel
                    fullLabel
                    value={this.getPartValue(2)}
                    label={i18next.t("hours")}
                    onChange={this.changeValue}
                    onFocus={this.handleFocus.bind(null, "h")}
                    minimal
                />
                <Text
                    key="inputDays"
                    name="inputDays"
                    style={{ textAlign: "center" }}
                    upperLabel
                    fullLabel
                    value={this.getPartValue(3)}
                    label={i18next.t("days")}
                    onChange={this.changeValue}
                    onFocus={this.handleFocus.bind(null, "D")}
                    minimal
                />
                <Text
                    key="inputMonths"
                    name="inputMonths"
                    style={{ textAlign: "center" }}
                    upperLabel
                    fullLabel
                    value={this.getPartValue(4)}
                    label={i18next.t("months")}
                    onChange={this.changeValue}
                    onFocus={this.handleFocus.bind(null, "M")}
                    minimal
                />
                <Text
                    key="inputWeeks"
                    name="inputWeeks"
                    style={{ textAlign: "center" }}
                    upperLabel
                    fullLabel
                    value={this.getPartValue(5)}
                    label={i18next.t("weeks")}
                    onChange={this.changeValue}
                    onFocus={this.handleFocus.bind(null, "W")}
                    minimal
                />

                {this.state.helpType === "s" && (
                    <div className="large-12 medium-12 small-12 columns">
                        <Label
                            label={i18next.t("contrab", {
                                variable: "$t(cSecond)",
                            })}
                            side="left"
                        />
                        <table className="full-width invoice-table cron-table">
                            <tbody>
                                <tr>
                                    <td width="5rem">0-59</td>
                                    <td
                                        title={i18next.t("secondAllowedValues")}
                                    >
                                        {i18next.t("secondAllowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>*</td>
                                    <td title={i18next.t("everySec")}>
                                        {i18next.t("everySec")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>,</td>
                                    <td title={i18next.t("listOfSec")}>
                                        {i18next.t("listOfSec")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>-</td>
                                    <td title={i18next.t("rangeOfSec")}>
                                        {i18next.t("rangeOfSec")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>/</td>
                                    <td title={i18next.t("slashSec")}>
                                        {i18next.t("slashSec")}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
                {this.state.helpType === "m" && (
                    <div className="large-12 medium-12 small-12 columns">
                        <Label
                            label={i18next.t("contrab", {
                                variable: "$t(cMinute)",
                            })}
                            side="left"
                        />
                        <table className="full-width invoice-table cron-table">
                            <tbody>
                                <tr>
                                    <td width="5rem">0-59</td>
                                    <td
                                        title={i18next.t("minuteAllowedValues")}
                                    >
                                        {i18next.t("minuteAllowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>*</td>
                                    <td title={i18next.t("everyMin")}>
                                        {i18next.t("everyMin")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>,</td>
                                    <td title={i18next.t("listOfMin")}>
                                        {i18next.t("listOfMin")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>-</td>
                                    <td title={i18next.t("rangeOfMin")}>
                                        {i18next.t("rangeOfMin")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>/</td>
                                    <td title={i18next.t("slashMin")}>
                                        {i18next.t("slashMin")}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
                {this.state.helpType === "h" && (
                    <div className="large-12 medium-12 small-12 columns">
                        <Label
                            label={i18next.t("contrab", {
                                variable: "$t(cHour)",
                            })}
                            side="left"
                        />
                        <table className="full-width invoice-table cron-table">
                            <tbody>
                                <tr>
                                    <td width="5rem">0-23</td>
                                    <td title={i18next.t("hourAllowedValues")}>
                                        {i18next.t("hourAllowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>*</td>
                                    <td title={i18next.t("everyHour")}>
                                        {i18next.t("everyHour")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>,</td>
                                    <td title={i18next.t("listOfHour")}>
                                        {i18next.t("listOfHour")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>-</td>
                                    <td title={i18next.t("rangeOfHour")}>
                                        {i18next.t("rangeOfHour")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>/</td>
                                    <td title={i18next.t("slashHour")}>
                                        {i18next.t("slashHour")}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
                {this.state.helpType === "D" && (
                    <div className="large-12 medium-12 small-12 columns">
                        <Label
                            label={i18next.t("contrab", {
                                variable: "$t(cDay)",
                            })}
                            side="left"
                        />
                        <table className="full-width invoice-table cron-table">
                            <tbody>
                                <tr>
                                    <td width="5rem">1-31</td>
                                    <td title={i18next.t("dayAllowedValues")}>
                                        {i18next.t("dayAllowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>*</td>
                                    <td title={i18next.t("everyMonthDay")}>
                                        {i18next.t("everyMonthDay")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>,</td>
                                    <td title={i18next.t("listOfDay")}>
                                        {i18next.t("listOfDay")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>-</td>
                                    <td title={i18next.t("rangeOfDay")}>
                                        {i18next.t("rangeOfDay")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>/</td>
                                    <td title={i18next.t("slashDay")}>
                                        {i18next.t("slashDay")}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
                {this.state.helpType === "M" && (
                    <div className="large-12 medium-12 small-12 columns">
                        <Label
                            label={i18next.t("contrab", {
                                variable: "$t(cMonth)",
                            })}
                            side="left"
                        />
                        <table className="full-width invoice-table cron-table">
                            <tbody>
                                <tr>
                                    <td width="5rem">1-12</td>
                                    <td title={i18next.t("allowedValues")}>
                                        {i18next.t("allowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>JAN-DEC</td>
                                    <td title={i18next.t("allowedValues")}>
                                        {i18next.t("allowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>*</td>
                                    <td title={i18next.t("everyMonth")}>
                                        {i18next.t("everyMonth")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>,</td>
                                    <td title={i18next.t("listOfMonth")}>
                                        {i18next.t("listOfMonth")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>-</td>
                                    <td title={i18next.t("rangeOfMonth")}>
                                        {i18next.t("rangeOfMonth")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>/</td>
                                    <td title={i18next.t("slashMonth")}>
                                        {i18next.t("slashMonth")}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
                {this.state.helpType === "W" && (
                    <div className="large-12 medium-12 small-12 columns">
                        <Label
                            label={i18next.t("contrab", {
                                variable: "$t(cWeekDay)",
                            })}
                            side="left"
                        />
                        <table className="full-width invoice-table cron-table">
                            <tbody>
                                <tr>
                                    <td width="5rem">0-6</td>
                                    <td title={i18next.t("weekAllowedValues")}>
                                        {i18next.t("weekAllowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>SUN-SAT</td>
                                    <td title={i18next.t("allowedValues")}>
                                        {i18next.t("allowedValues")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>*</td>
                                    <td title={i18next.t("everyWeekDay")}>
                                        {i18next.t("everyWeekDay")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>,</td>
                                    <td title={i18next.t("listOfWeekDay")}>
                                        {i18next.t("listOfWeekDay")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>-</td>
                                    <td title={i18next.t("rangeOfWeekDay")}>
                                        {i18next.t("rangeOfWeekDay")}
                                    </td>
                                </tr>
                                <tr>
                                    <td>/</td>
                                    <td title={i18next.t("slashWeek")}>
                                        {i18next.t("slashWeek")}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                )}
            </div>
        );
    },
});

export default CronRepeat;