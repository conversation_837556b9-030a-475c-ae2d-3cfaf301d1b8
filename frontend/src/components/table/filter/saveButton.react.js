import PropTypes from 'prop-types';

const React = require('react');

class SaveButton extends React.Component {

    render() {
        return (
            <a
                className="button modal-button-green"
                onClick={this.props.onClick}
            >
                <span className={`icon ${this.props.icon} not name`} />
                <span className="name">{this.props.title}</span>
            </a>
        );
    }

}

SaveButton.propTypes = {
    title: PropTypes.string.isRequired,
    onClick: PropTypes.func,
    icon: PropTypes.string,
};

SaveButton.defaultProps = {
    icon: 'icon-check-2',
};

module.exports = SaveButton;
