import i18next from 'i18next';
import PropTypes from 'prop-types';
import { CalendarWithClockpicker } from '../../../form/calendarWithClockpicker.react';
import { Calendar } from '../../../form/calendar.react';
import { SelectBox } from '../../../form/selectBox.react';
import React from 'react';
import _ from 'lodash';
import moment from 'moment-timezone';
import Modal from '../../../modal.react';
import Form from '../../../form/form.react';
import Label from '../../../form/label.react';
import EmptyComponent from '../../../form/emptyComponent.react';
import WrapComponent from '../../../form/wrapComponent.react';
import TabsButtonsOther from '../../../tabs/tabsButtonsOther.react';
import TabsButton from '../../../tabs/tabsButton.react';
import ConfirmModal from '../../../../pages/modals/confirmModal.react';

class ConfirmEventModal extends React.Component {

    constructor(props) {
        super();

        const eventStart = props.confirmEvent.start;
        let eventEnd = props.confirmEvent.end;
        let startHours = null;
        let startMinutes = null;
        let endHours = null;
        let endMinutes = null;

        if (
            props.useSelectBoxesToSelectTime &&
            eventStart !== null &&
            eventEnd !== null
        ) {
            eventEnd = this.eventEndMidnightCheck(
                eventEnd,
                props.calendarHoursMax,
                props.confirmEvent,
            );
            const start = moment(eventStart);
            const end = moment(eventEnd);
            startHours = start.hours();
            startMinutes = start.minutes();
            endHours = end.hours();
            endMinutes = end.minutes();
        }

        let buttonIsActive = true;
        let initialValues = {};

        // not allow the event to be saved without changing
        if (
            props.useSelectBoxesToSelectTime &&
            !props.allowEventEditWithoutChange &&
            props.confirmEvent.action === 'edit' &&
            props.confirmEvent.subAction === null
        ) {
            buttonIsActive = false;
            initialValues = {
                startDate: props.confirmEvent.start,
                startHour: startHours,
                startMinute: startMinutes,
                endDate: props.confirmEvent.end,
                endHour: endHours,
                endMinute: endMinutes,
            };
        }

        this.state = {
            loading: false,
            startHours: startHours,
            startMinutes: startMinutes,
            endHours: endHours,
            endMinutes: endMinutes,
            delEventModalIsOpen: false,
            saveButtonIsActive: buttonIsActive,
            initialValues: initialValues,
        };

        this.formRef = React.createRef();

        this.closeModal = this.closeModal.bind(this);
        this.saveEvent = this.saveEvent.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.openDelEventModal = this.openDelEventModal.bind(this);
        this.closeDelEventModal = this.closeDelEventModal.bind(this);
        this.deleteEvent = this.deleteEvent.bind(this);
        this.sbStartHourChange = this.sbStartHourChange.bind(this);
        this.sbEndHourChange = this.sbEndHourChange.bind(this);
        this.formChanged = this.formChanged.bind(this);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    handleSubmit(data) {
        this.onFormAction(data);
    }

    saveEvent(e) {
        if (e) e.preventDefault();

        this.onFormAction = (data) => {
            const { confirmEvent, onConfirmEvent, useSelectBoxesToSelectTime } =
                this.props;
            const postData = data;
            postData.activityId = confirmEvent.activityId;
            postData.iprocId = confirmEvent.iprocId;

            if (useSelectBoxesToSelectTime) {
                const startDate = moment(postData.startDate);
                const endDate = moment(postData.endDate);
                startDate.set(
                    'hour',
                    typeof postData.startHour.value !== 'undefined'
                        ? postData.startHour.value
                        : postData.startHour,
                );
                startDate.set(
                    'minute',
                    typeof postData.startMinute.value !== 'undefined'
                        ? postData.startMinute.value
                        : postData.startMinute,
                );
                endDate.set(
                    'hour',
                    typeof postData.endHour.value !== 'undefined'
                        ? postData.endHour.value
                        : postData.endHour,
                );
                endDate.set(
                    'minute',
                    typeof postData.endMinute.value !== 'undefined'
                        ? postData.endMinute.value
                        : postData.endMinute,
                );

                postData.start = startDate.toISOString();
                postData.end = endDate.toISOString();
            }

            // it is possible to enter midnight for eventEnd
            if (this.props.calendarHoursMax === null) {
                // calendar with clockpicker
                let dateWithoutMillisecond = moment(postData.end)
                    .add(1, 'day')
                    .subtract(1, 'millisecond');
                let dayEnd = moment(postData.end).endOf('day');

                // selectboxes
                if (useSelectBoxesToSelectTime) {
                    dateWithoutMillisecond = moment(postData.end).subtract(
                        1,
                        'millisecond',
                    );
                    dayEnd = moment(postData.end)
                        .subtract(1, 'day')
                        .endOf('day');
                }

                // if 24:00 is selected we must set 23:59
                if (dateWithoutMillisecond.isSame(dayEnd)) {
                    postData.end = dayEnd
                        .seconds(0)
                        .milliseconds(0)
                        .toISOString();
                }
            }

            onConfirmEvent(confirmEvent.action, postData);
            this.closeModal();
        };

        this.formRef.current.submit();
    }

    openDelEventModal(e) {
        if (e) e.preventDefault();
        this.setState({ delEventModalIsOpen: true });
    }

    closeDelEventModal() {
        this.setState({ delEventModalIsOpen: false });
    }

    deleteEvent(e) {
        if (e) e.preventDefault();

        this.props.onConfirmEvent('delete', {
            iprocId: this.props.confirmEvent.iprocId,
        });
        this.closeModal();
    }

    sbStartHourChange(name, val) {
        this.setState({ startHours: val.value });
    }

    sbEndHourChange(name, val) {
        this.setState({ endHours: val.value });
    }

    formChanged(currentValues, isChanged) {
        // not allow the event to be saved without changing
        if (
            this.props.useSelectBoxesToSelectTime &&
            !this.props.allowEventEditWithoutChange &&
            this.props.confirmEvent.action === 'edit' &&
            this.props.confirmEvent.subAction === null
        ) {
            const currentVals = {};

            _.forEach(currentValues, (value, key) => {
                if (value && typeof value.value !== 'undefined') {
                    currentVals[key] = value.value;
                } else if (key === 'startDate') {
                    const startDate = moment(value);
                    startDate.set(
                        'hour',
                        typeof currentValues.startHour.value !== 'undefined'
                            ? currentValues.startHour.value
                            : currentValues.startHour,
                    );
                    startDate.set(
                        'minute',
                        typeof currentValues.startMinute.value !== 'undefined'
                            ? currentValues.startMinute.value
                            : currentValues.startMinute,
                    );
                    currentVals[key] = startDate.toISOString();
                } else if (key === 'endDate') {
                    const endDate = moment(value);
                    endDate.set(
                        'hour',
                        typeof currentValues.endHour.value !== 'undefined'
                            ? currentValues.endHour.value
                            : currentValues.endHour,
                    );
                    endDate.set(
                        'minute',
                        typeof currentValues.endMinute.value !== 'undefined'
                            ? currentValues.endMinute.value
                            : currentValues.endMinute,
                    );
                    currentVals[key] = endDate.toISOString();
                } else {
                    currentVals[key] = value;
                }
            });

            this.setState({
                saveButtonIsActive: !_.isEqual(
                    this.state.initialValues,
                    currentVals,
                ),
            });
        }
    }

    eventEndMidnightCheck(eventEnd, calendarHoursMax, confirmEvent) {
        // it is possible to enter midnight for eventEnd
        if (
            calendarHoursMax === null &&
            (confirmEvent.action === 'move' ||
                (confirmEvent.action === 'edit' &&
                    confirmEvent.subAction === 'resize'))
        ) {
            // eventEnd is moved to midnight we have to set 23:59 (22:30 -> 23:59)
            const dateWithoutMillisecond = moment(eventEnd).subtract(
                1,
                'millisecond',
            );
            const dayEnd = moment(eventEnd).subtract(1, 'day').endOf('day');

            if (dateWithoutMillisecond.isSame(dayEnd)) {
                return dayEnd.seconds(0).milliseconds(0).toISOString();
            }

            // eventEnd was 23:59, we have to add a minute when moving outside midnight (23:59 -> 22:30)
            const eventEndForDayEndCheck = moment(eventEnd)
                .seconds(59)
                .milliseconds(999);
            const eventEndDayEnd = moment(eventEnd).endOf('day');
            const eventEndMinutes = moment(eventEnd).minute();

            if (
                eventEndMinutes.toString().includes('9') &&
                !eventEndForDayEndCheck.isSame(eventEndDayEnd)
            ) {
                return moment(eventEnd)
                    .add(1, 'minute')
                    .seconds(0)
                    .milliseconds(0)
                    .toISOString();
            }

            return eventEnd;
        }

        return eventEnd;
    }

    render() {
        const {
            confirmEvent,
            toCaseDetail,
            useSelectBoxesToSelectTime,
            hourOptions,
            minuteOptions,
            validateHoursSelectStartMin,
            validateHoursSelectEndMax,
        } = this.props;

        const {
            startHours,
            startMinutes,
            endHours,
            endMinutes,
            saveButtonIsActive,
        } = this.state;

        const eventStart = confirmEvent.start;
        const eventEnd = this.eventEndMidnightCheck(
            confirmEvent.end,
            this.props.calendarHoursMax,
            confirmEvent,
        );

        // validation of time selection using selectBoxes
        const sbStartHourValidations = {};
        const sbStartHourValidationErrors = {
            isDefaultRequiredValue: i18next.t('isRequired'),
        };
        const sbEndHourValidations = {};
        const sbEndHourValidationErrors = {
            isDefaultRequiredValue: i18next.t('isRequired'),
        };
        const sbStartMinuteValidations = {};
        const sbStartMinuteValidationErrors = {
            isDefaultRequiredValue: i18next.t('isRequired'),
        };
        const sbEndMinuteValidations = {};
        const sbEndMinuteValidationErrors = {
            isDefaultRequiredValue: i18next.t('isRequired'),
        };

        if (validateHoursSelectStartMin !== null) {
            sbStartHourValidations.isHigherOrEqualThan =
                validateHoursSelectStartMin;
            sbStartHourValidationErrors.isHigherOrEqualThan = i18next.t(
                'valueCannotBeEntered',
            );
            sbEndHourValidations.isHigherOrEqualThan =
                validateHoursSelectStartMin;
            sbEndHourValidationErrors.isHigherOrEqualThan = i18next.t(
                'valueCannotBeEntered',
            );
        }
        if (validateHoursSelectEndMax !== null) {
            sbStartHourValidations.isLowerOrEqualThan =
                validateHoursSelectEndMax;
            sbStartHourValidationErrors.isLowerOrEqualThan = i18next.t(
                'valueCannotBeEntered',
            );
            sbEndHourValidations.isLowerOrEqualThan = validateHoursSelectEndMax;
            sbEndHourValidationErrors.isLowerOrEqualThan = i18next.t(
                'valueCannotBeEntered',
            );

            if (startHours === validateHoursSelectEndMax) {
                sbStartMinuteValidations.isLowerThan = 1;
                sbStartMinuteValidationErrors.isLowerThan = i18next.t(
                    'valueCannotBeEntered',
                );
            }
            if (endHours === validateHoursSelectEndMax) {
                sbEndMinuteValidations.isLowerThan = 1;
                sbEndMinuteValidationErrors.isLowerThan = i18next.t(
                    'valueCannotBeEntered',
                );
            }
        }

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                // onEnter={this.saveTimeRange}
            >
                <div className="modal big-calendar-confirm">
                    <div className="row">
                        <TabsButtonsOther key="buttons" inModal>
                            {confirmEvent.iprocId && (
                                <TabsButton
                                    key="case"
                                    icon="icon-clipboard-1"
                                    enableOn={!this.state.loading}
                                    onClick={toCaseDetail}
                                    hideTitle={false}
                                >
                                    {i18next.t('case')}
                                </TabsButton>
                            )}
                            <TabsButton
                                key="save"
                                icon="icon-floppy-disk"
                                enableOn={
                                    !this.state.loading && saveButtonIsActive
                                }
                                onClick={this.saveEvent}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            {confirmEvent.action === 'edit' && (
                                <TabsButton
                                    key="delete"
                                    icon="icon-bin-2"
                                    isActive
                                    onClick={this.openDelEventModal}
                                    hideTitle={false}
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                            )}
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref={this.formRef}
                        name="formConfirmEvent"
                        className="form-container"
                        onValidSubmit={this.handleSubmit}
                        onChange={this.formChanged}
                        oneColumn
                    >
                        <Label
                            key="actionLabel"
                            label={i18next.t(confirmEvent.action)}
                            fullLabel
                            noValueHeight
                            fontSize="1rem"
                        />
                        {confirmEvent.eventTitle && (
                            <Label
                                key="eventTitle"
                                label={`${i18next.t('name')}:`}
                                value={confirmEvent.eventTitle}
                            />
                        )}
                        {confirmEvent.action !== 'move' && (
                            <Label
                                key="activityTitle"
                                label={`${i18next.t('activity')}:`}
                                value={confirmEvent.activityTitle}
                            />
                        )}
                        {confirmEvent.fromStart && confirmEvent.fromEnd && (
                            <WrapComponent key="wrap1">
                                <EmptyComponent key="empty1" />
                                <Label
                                    key="eventFrom"
                                    label={`${i18next.t('ttGraphLinkFrom')}`}
                                    fullLabel
                                    noValueHeight
                                    fontSize="1rem"
                                />
                                <EmptyComponent key="empty2" />
                                {confirmEvent.fromActivityTitle && (
                                    <Label
                                        key="fromActivityTitle"
                                        label={`${i18next.t('activity')}:`}
                                        value={confirmEvent.fromActivityTitle}
                                    />
                                )}
                                <Label
                                    key="fromStart"
                                    label={`${i18next.t('start')}:`}
                                    value={confirmEvent.fromStart}
                                    labelType="D"
                                    timeStamp
                                />
                                <Label
                                    key="fromEnd"
                                    label={`${i18next.t('end')}:`}
                                    value={confirmEvent.fromEnd}
                                    labelType="D"
                                    timeStamp
                                />
                                <EmptyComponent key="empty3" />
                                <Label
                                    key="eventTo"
                                    label={`${i18next.t('ttGraphLinkTo')}`}
                                    fullLabel
                                    noValueHeight
                                    fontSize="1rem"
                                />
                                <EmptyComponent key="empty4" />
                            </WrapComponent>
                        )}
                        {confirmEvent.action === 'move' && (
                            <Label
                                key="activityTitle"
                                label={`${i18next.t('activity')}:`}
                                value={confirmEvent.activityTitle}
                            />
                        )}
                        {useSelectBoxesToSelectTime ? (
                            <WrapComponent key="wrapStartEnd">
                                <div
                                    key="wrapStart"
                                    className="row time-select-wrap"
                                >
                                    <div className="small-12 columns medium-5">
                                        <label
                                            className=""
                                            title={`${i18next.t('start')}:`}
                                        >
                                            <span>{`${i18next.t('start')}:`}</span>
                                        </label>
                                    </div>
                                    <div className="small-12 medium-7 columns">
                                        <div className="comp-wrap calendar">
                                            <Calendar
                                                key="startDate"
                                                name="startDate"
                                                value={eventStart}
                                                required
                                                validationErrors={{
                                                    isDefaultRequiredValue:
                                                        i18next.t('isRequired'),
                                                }}
                                            />
                                        </div>
                                        <div className="comp-wrap">
                                            <div className="sb-wrap sb-hour">
                                                <SelectBox
                                                    key="startHour"
                                                    name="startHour"
                                                    options={hourOptions}
                                                    value={startHours}
                                                    nullable={false}
                                                    required
                                                    validations={
                                                        sbStartHourValidations
                                                    }
                                                    validationErrors={
                                                        sbStartHourValidationErrors
                                                    }
                                                    onComponentChange={
                                                        this.sbStartHourChange
                                                    }
                                                />
                                            </div>
                                            <div>:</div>
                                            <div className="sb-wrap sb-minute">
                                                <SelectBox
                                                    key="startMinute"
                                                    name="startMinute"
                                                    options={minuteOptions}
                                                    value={startMinutes}
                                                    nullable={false}
                                                    required
                                                    validations={
                                                        sbStartMinuteValidations
                                                    }
                                                    validationErrors={
                                                        sbStartMinuteValidationErrors
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    key="wrapEnd"
                                    className="row time-select-wrap"
                                >
                                    <div className="small-12 columns medium-5">
                                        <label
                                            className=""
                                            title={`${i18next.t('end')}:`}
                                        >
                                            <span>{`${i18next.t('end')}:`}</span>
                                        </label>
                                    </div>
                                    <div className="small-12 medium-7 columns">
                                        <div className="comp-wrap calendar">
                                            <Calendar
                                                key="endDate"
                                                name="endDate"
                                                value={eventEnd}
                                                required
                                                validationErrors={{
                                                    isDefaultRequiredValue:
                                                        i18next.t('isRequired'),
                                                }}
                                            />
                                        </div>
                                        <div className="comp-wrap">
                                            <div className="sb-wrap sb-hour">
                                                <SelectBox
                                                    key="endHour"
                                                    name="endHour"
                                                    options={hourOptions}
                                                    value={endHours}
                                                    nullable={false}
                                                    required
                                                    validations={
                                                        sbEndHourValidations
                                                    }
                                                    validationErrors={
                                                        sbEndHourValidationErrors
                                                    }
                                                    onComponentChange={
                                                        this.sbEndHourChange
                                                    }
                                                />
                                            </div>
                                            <div>:</div>
                                            <div className="sb-wrap sb-minute">
                                                <SelectBox
                                                    key="endMinute"
                                                    name="endMinute"
                                                    options={minuteOptions}
                                                    value={endMinutes}
                                                    nullable={false}
                                                    required
                                                    validations={
                                                        sbEndMinuteValidations
                                                    }
                                                    validationErrors={
                                                        sbEndMinuteValidationErrors
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </WrapComponent>
                        ) : (
                            <WrapComponent key="wrapStartEnd">
                                <CalendarWithClockpicker
                                    key="start"
                                    name="start"
                                    label={`${i18next.t('start')}:`}
                                    value={eventStart}
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                    useMoment
                                />
                                <CalendarWithClockpicker
                                    key="end"
                                    name="end"
                                    label={`${i18next.t('end')}:`}
                                    value={eventEnd}
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                    useMoment
                                />
                            </WrapComponent>
                        )}
                    </Form>
                </div>
                <ConfirmModal
                    isOpen={this.state.delEventModalIsOpen}
                    width="tiny"
                    text={i18next.t('confirmDeleteDialog', {
                        variable: i18next.t('event').toLowerCase(),
                    })}
                    onClose={this.closeDelEventModal}
                    onConfirm={this.deleteEvent}
                    modalInModal
                />
            </Modal>
        );
    }

}

ConfirmEventModal.displayName = 'ConfirmEventModal';

ConfirmEventModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    confirmEvent: PropTypes.objectOf(PropTypes.any).isRequired, // confirmEvent.action - add, edit, move
    onConfirmEvent: PropTypes.func.isRequired,
    toCaseDetail: PropTypes.func.isRequired,
    useSelectBoxesToSelectTime: PropTypes.bool.isRequired,
    hourOptions: PropTypes.arrayOf(PropTypes.object), // used when useSelectBoxesToSelectTime
    minuteOptions: PropTypes.arrayOf(PropTypes.object), // used when useSelectBoxesToSelectTime
    validateHoursSelectStartMin: PropTypes.number, // only for useSelectBoxesToSelectTime
    validateHoursSelectEndMax: PropTypes.number, // only for useSelectBoxesToSelectTime
    allowEventEditWithoutChange: PropTypes.bool, // false value - only for useSelectBoxesToSelectTime
    calendarHoursMax: PropTypes.instanceOf(Date),
};

ConfirmEventModal.defaultProps = {
    width: 'own-width-600',
    hourOptions: [],
    minuteOptions: [],
    validateHoursSelectStartMin: null,
    validateHoursSelectEndMax: null,
    allowEventEditWithoutChange: true,
    calendarHoursMax: null,
};

export default ConfirmEventModal;
