import PropTypes from 'prop-types';
import React from 'react';
import Loader from 'react-loader';
import Tab from './tab.react';

class Tabs extends React.Component {

    constructor(props) {
        super();
        this.state = {
            selectedTab: 0,
        };

        this.documentClick = this.documentClick.bind(this);
        this.findTab = this.findTab.bind(this);
        this.selectTab = this.selectTab.bind(this);
    }

    componentDidMount() {
        const tabs = document.querySelectorAll('.tab-item');
        React.Children.count(this.props.children) > 1 &&
            this.tabArrow.addEventListener('click', function (event) {
                event.stopPropagation();
                for (let i = 0; i < tabs.length; i++) {
                    tabs[i].classList.toggle('is-shown');
                }
                this.classList.toggle('icon-arrow-65');
                this.classList.toggle('icon-arrow-66');
            });

        document.addEventListener('click', this.documentClick);
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.documentClick);
    }

    documentClick() {
        const tabs = document.querySelectorAll('.tab-item');
        if (React.Children.count(this.props.children) > 1) {
            var arrow = this.tabArrow;
        }
        for (let i = 0; i < tabs.length; i++) {
            tabs[i].classList.remove('is-shown');
        }
        if (arrow) {
            arrow.classList.remove('icon-arrow-66');
            arrow.classList.add('icon-arrow-65');
        }
    }

    /**
     * Find right tab by tabName and params.tabName or by index if tabLink not used
     * @param children
     * @param index
     * @return result (tab)
     */
    findTab(children, index) {
        let i = 0;
        let result;

        children.map((child) => {
            if (typeof child.props.tabLink !== 'undefined') {
                if (child.props.tabName == this.props.params.tabName) {
                    result = child;
                }
            } else if (index == i) {
                result = child;
            }

            i++;
        });
        return result;
    }

    selectTab(index) {
        this.setState({ selectedTab: index });
    }

    render() {
        const children = [];
        React.Children.forEach(this.props.children, (child, index) => {
            if (child) {
                children.push(
                    React.cloneElement(child, {
                        active:
                            typeof child.props.tabLink !== 'undefined'
                                ? child.props.tabName ==
                                  this.props.params.tabName
                                : index === this.state.selectedTab,
                        index: index,
                        selectTab: this.selectTab,
                        noTabLinks: this.props.noTabLinks,
                        labelColor: child.props.labelColor,
                        showTag: child.props.showTag,
                        tagTitle: child.props.tagTitle,
                        tagLetter: child.props.tagLetter,
                        'data-cy': child.props['data-cy'],
                    }),
                );
            }
        });

        const findTab = this.findTab(children, this.state.selectedTab);

        return (
            <div>
                <div
                    className="tabs"
                    style={{ display: this.props.hidden ? 'none' : 'block' }}
                >
                    {children}
                    {React.Children.count(this.props.children) > 1 && (
                        <span
                            ref={(c) => (this.tabArrow = c)}
                            className="icon icon-arrow-65 pointer"
                        />
                    )}
                </div>
                <div>
                    {typeof findTab !== 'undefined' ? (
                        <div>
                            {findTab.props.showLoader && (
                                <Loader
                                    top={findTab.props.loaderTop}
                                    loaded={findTab.props.loaded}
                                />
                            )}
                            {findTab.props.children}
                        </div>
                    ) : (
                        <h1>404 error</h1>
                    )}
                </div>
            </div>
        );
    }

}

Tabs.propTypes = {
    params: PropTypes.object, // required if tabLink is used in tab
    noTabLinks: PropTypes.bool,
    hidden: PropTypes.bool,
};

Tabs.defaultProps = {
    noTabLinks: false,
    hidden: false,
};

Tabs.Tab = Tab;
export default Tabs;
