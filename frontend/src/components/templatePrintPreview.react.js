import { Rnd } from 'react-rnd';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import ApiRequest from '../api/apiRequest';

import React from 'react';
import _ from 'lodash';
import Loader from 'react-loader';
import MenuStore from '../flux/menu.store';
import CasePrint from '../pages/case/casePrint.react';
import AlertsActions from './alerts/alerts.actions';

class TemplatePrintPreview extends React.Component {

    constructor(props) {
        super();

        this.state = _.extend(
            {
                showPreview: false,
                babelisedJs: null,
                loading: false,
            },
            MenuStore.getState(),
        );

        this._onChange = this._onChange.bind(this);
        this.closePreview = this.closePreview.bind(this);
    }

    componentDidMount() {
        MenuStore.listen(this._onChange);

        if (!_.isEmpty(this.props.js)) {
            this.babelTransform(this.props.js);
        } else {
            this.setState({ showPreview: true });
        }
    }

    componentWillUnmount() {
        MenuStore.unlisten(this._onChange);
    }

    _onChange(state) {
        this.setState(state);
        this.setState(MenuStore.getState());
    }

    babelTransform(js) {
        this.setState({ loading: true });

        ApiRequest.post(
            `${document.location.origin}/babel-transform`,
            JSON.stringify({ js: js }),
        )
            .then((payload) => {
                this.setState({
                    babelisedJs: payload.js,
                    showPreview: true,
                    loading: false,
                });
            })
            .catch((errorMessage) => {
                this.setState({ loading: false });

                AlertsActions.addAlert({
                    type: 'alert',
                    message: `${i18next.t('thumbnail')} - (Babel transform error. Position: ${JSON.stringify(errorMessage.loc)})`,
                });
            });
    }

    closePreview() {
        if (typeof this.props.closePreview === 'function') {
            this.props.closePreview();
        }
    }

    render() {
        const width = this.props.width ? this.props.width : '70%';
        const x = this.state.toHide ? -100 : 55;

        return (
            <Rnd
                className="rnd-wrapper"
                dragHandleClassName="head"
                default={{
                    x: x,
                    y: -50,
                    width: width,
                    height: window.innerHeight - 110,
                }}
            >
                <Loader loaded={!this.state.loading} />
                <div className="box no-cursor" style={{ height: 'inherit' }}>
                    <div className="head">
                        <div>
                            <span
                                className="icon icon-cursor-move"
                                title={i18next.t('move')}
                            />
                        </div>
                        <div>
                            <span
                                className="icon icon-delete-1"
                                title={i18next.t('close')}
                                onClick={this.closePreview}
                            />
                        </div>
                    </div>
                    <div className="print-preview">
                        {this.state.showPreview && (
                            <CasePrint
                                key="preview"
                                print={this.props.print}
                                css={this.props.css}
                                js={this.state.babelisedJs}
                                printPreviewId={this.props.printPreviewId}
                            />
                        )}
                    </div>
                </div>
            </Rnd>
        );
    }

}

TemplatePrintPreview.propTypes = {
    closePreview: PropTypes.func.isRequired,
    width: PropTypes.string,
    js: PropTypes.string,
    print: PropTypes.string,
    css: PropTypes.string,
    printPreviewId: PropTypes.string.isRequired,
};

TemplatePrintPreview.defaultProps = {
    width: null,
    js: null,
    print: null,
    css: null,
};

export default TemplatePrintPreview;
