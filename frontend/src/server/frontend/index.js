import axios from 'axios';
import render from './render';

const express = require('express');
const path = require('path');
const fs = require('fs');
const https = require('https');
const bodyParser = require('body-parser');

const app = express();
const _ = require('lodash');
const mkdirp = require('mkdirp');
const expressStaticGzip = require('express-static-gzip');
const babel = require('@babel/core');
const Bluebird = require('bluebird');
const configReader = require('../../common/configReader');
const config = require('../config');

const tasConfig = configReader();
const prefix = '_';
const assetsConfigPath = tasConfig.assetsStoragePath;
const manualsStorage = `${assetsConfigPath}${path.sep}uploads`;
const logosStorage = `${assetsConfigPath}${path.sep}logos`;
const assetsServicePath = '/assets';
const schemaLogosFolderName = '_schemaLogos_';
const orgLogosFolderName = '_org_';
// const serveIndex = require('serve-index');

app.use(bodyParser.json({ limit: `${tasConfig.maxUploadSize}mb`, extended: true }));

app.use((req, res, next) => {
    _.forEach(tasConfig.responseHeaders, (value, key) => {
        res.header(key, value);
    });

    // to not provide potentially sensitive information
    res.removeHeader('x-powered-by');
    next();
});

// TODO: Move to CDN.
app.use('/dist', expressStaticGzip(path.join(__dirname, '../../../dist')));
app.use('/status', (req, res) => {
    res.send('status:OK');
});
app.use('/assets', express.static('assets'));
app.use('/build', (req, res) => {
    res.sendFile('config/build', { root: path.join(__dirname, '../../../') });
});

let iconUrl = path.join(__dirname, '../../../assets/images/_favicon.ico');
if (tasConfig.faviconUrl !== null) {
    iconUrl = tasConfig.faviconUrl;
}

// endpoint for serving favicons
app.get('/favicon.ico', (req, res) => {
    const resOptions = {
        headers: {
            'Cache-Control': 'no-cache, no-store',
        },
    };

    try {
        const schemaFaviconUrl = `${manualsStorage}${path.sep}${schemaLogosFolderName}${path.sep}favicon.png`;
        fs.statSync(schemaFaviconUrl);

        res.sendFile(`${schemaFaviconUrl}`, resOptions);
    } catch (e) {
        res.sendFile(iconUrl, resOptions);
    }
});

app.get('/colors', (req, res) => {
    res.sendFile('assets/css/defaultColors.scss', {
        root: path.join(__dirname, '../../../'),
    });
});

// app.use('/assets/uploads', serveIndex('assets/uploads', { 'icons': true, 'view': 'details' }));

function checkLogin(req) {
    return new Promise((resolve, reject) => {
        let { backendUrl } = tasConfig;

        if (tasConfig.multitenantBackend) {
            backendUrl = `${req.get('origin')}/api`;
        }

        const rawUrl = `${backendUrl}/logged-user`;
        const { vice, accessToken } = req.body;
        let token = typeof vice !== 'undefined' && vice !== null ? vice.viceToken : accessToken;
        if (token?.includes('Bearer')) {
            token = token.replace('Bearer ', '').trim();
        }

        const axiosConfig = {
            httpsAgent: new https.Agent({
                rejectUnauthorized: false,
            }),
            headers: {
                ...(token && { [tasConfig.authorizationHeader]: `Bearer ${token}` }),
                'Cache-Control': 'no-cache, no-store',
                Pragma: 'no-cache',
                Expires: 0,
            },
        };

        axios
            .get(rawUrl, axiosConfig)
            .then((res) => {
                const contentType = res.headers['content-type'];

                if (!/^application\/json/.test(contentType)) {
                    const error = {
                        error: {
                            message: `Invalid content-type. Expected application/json but received ${contentType}`,
                        },
                    };
                    return reject(error);
                }
                if (!res.data.id) {
                    const error = {
                        error: {
                            codeName: 'UNAUTHORIZED',
                            message: 'Not logged user.',
                        },
                    };
                    return reject(error);
                }

                return resolve({ ...res.data });
            })
            .catch((err) => {
                const { status, data } = err.response;

                let rejectError = {
                    error: {
                        message: err.message,
                        codeName: 'LOGGED_USER',
                        status: status,
                    },
                };

                if (data) {
                    rejectError = {
                        error: {
                            createdAt: data.error.createdAt,
                            message: data.error.message,
                            codeName: data.error.codeName,
                            status: status,
                            redirect: data.error.redirect,
                        },
                    };
                }

                return reject(rejectError);
            });
    });
}

app.post('/assets/uploads/folders', (req, res) => {
    return checkLogin(req)
        .then((response) => {
            const rootFolder = req.query.root;
            return getFolderTree(rootFolder, _.get(response, 'roles'))
                .then((results) => {
                    return res.send({
                        ...results,
                        newTokens: response.newTokens,
                    });
                })
                .catch((err) => {
                    return res.status(500).send({
                        error: {
                            message: JSON.stringify(err.message || err),
                        },
                    });
                });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/assets/uploads/files', (req, res) => {
    return checkLogin(req)
        .then((response) => {
            const rootFolder = req.query.root;
            const { offset, limit } = req.query;

            if (req.query.filter && req.query.filter.indexOf('..') !== -1) {
                return res.status(500).send({ error: { message: 'Lack of permissions!' } });
            }

            return getFolderFlatTree(rootFolder, req.query.filter, _.get(response, 'roles'))
                .then((results) => {
                    return res.send({
                        items: results.slice(offset, offset + limit),
                        total_count: results.length,
                        newTokens: response.newTokens,
                    });
                })
                .catch((err) => {
                    return res.status(500).send({
                        error: {
                            message: JSON.stringify(err.message || err),
                        },
                    });
                });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/assets/uploads', (req, res) => {
    if (req.query.path && req.query.path.indexOf('..') !== -1) {
        return res.status(500).send({ error: { message: 'Lack of permissions!' } });
    }
    if (req.query.root && req.query.root.indexOf('..') !== -1) {
        return res.status(500).send({ error: { message: 'Lack of permissions!' } });
    }

    const rootFolder = req.query.root;
    const folderPath = path.resolve(
        rootFolder === 'logos' ? logosStorage : manualsStorage,
        req.query.path,
    );
    const { filename } = req.query;
    const { content } = req.body;
    const manualPath = path.resolve(folderPath, filename);

    return checkLogin(req)
        .then((response) => {
            if (hasRights(_.get(response, 'roles'))) {
                return saveManual(manualPath, content, rootFolder)
                    .then((ignored) => {
                        return res.send({
                            result: true,
                            newTokens: response.newTokens,
                        });
                    })
                    .catch((e) => {
                        return res.status(500).send({
                            error: {
                                message: JSON.stringify(e.message || e),
                            },
                        });
                    });
            }
            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions!',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.delete('/assets/uploads', (req, res) => {
    const rootFolder = req.query.root;
    const manualPath = path.resolve(
        rootFolder === 'logos' ? logosStorage : manualsStorage,
        req.query.path,
    );

    return checkLogin(req)
        .then((response) => {
            if (hasRights(_.get(response, 'roles'))) {
                const stats = fs.lstatSync(manualPath);
                const createRevision = !path.basename(manualPath).startsWith(prefix);
                if (stats.isDirectory()) {
                    return removeDir(manualPath, rootFolder, createRevision)
                        .then((ignored) => {
                            return res.send({
                                result: true,
                                newTokens: response.newTokens,
                            });
                        })
                        .catch((e) => {
                            return res.status(500).send({
                                error: {
                                    message: JSON.stringify(e.message || e),
                                },
                            });
                        });
                }
                return deleteFile(manualPath, rootFolder, createRevision)
                    .then((ignored) => {
                        return res.send({
                            result: true,
                            newTokens: response.newTokens,
                        });
                    })
                    .catch((e) => {
                        return res.status(500).send({
                            error: {
                                message: JSON.stringify(e.message || e),
                            },
                        });
                    });
            }
            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions!',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/assets/rename', (req, res) => {
    const rootFolder = req.query.root;
    const folderPath = path.resolve(
        rootFolder === 'logos' ? logosStorage : manualsStorage,
        req.query.path,
    );
    const { newName, newPath } = req.body;
    const newDirPath = path.resolve(
        rootFolder === 'logos' ? logosStorage : manualsStorage,
        newPath,
    );
    const wholePathNew = path.resolve(newDirPath, newName);

    return checkLogin(req)
        .then((response) => {
            if (hasRights(_.get(response, 'roles'))) {
                return renameFolder(folderPath, wholePathNew, rootFolder)
                    .then((ignored) => {
                        return res.send({
                            result: true,
                            newTokens: response.newTokens,
                        });
                    })
                    .catch((e) => {
                        return res.status(500).send({
                            error: {
                                message: JSON.stringify(e.message || e),
                            },
                        });
                    });
            }
            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions!',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/assets/new', (req, res) => {
    const { newName, newPath } = req.body;
    const rootFolder = req.query.root;
    const newDirPath = path.resolve(
        rootFolder === 'logos' ? logosStorage : manualsStorage,
        newPath,
    );
    const wholePathNew = path.resolve(newDirPath, newName);

    return checkLogin(req)
        .then((response) => {
            if (hasRights(_.get(response, 'roles'))) {
                return makeDir(wholePathNew, rootFolder)
                    .then((ignored) => {
                        return res.send({
                            result: true,
                            newTokens: response.newTokens,
                        });
                    })
                    .catch((e) => {
                        return res.status(500).send({
                            error: {
                                message: JSON.stringify(e.message || e),
                            },
                        });
                    });
            }
            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions!',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/assets/download', (req, res, next) => {
    const { root: rootFolder, path: queryPath } = req.query;

    if (!rootFolder || !queryPath) {
        return res.status(400).send({
            error: { message: 'Parameters root and path are required' },
        });
    }

    return checkLogin(req)
        .then((response) => {
            const wholePath = path.resolve(
                rootFolder === 'logos' ? logosStorage : manualsStorage,
                queryPath,
            );

            try {
                checkAssetsFolder(wholePath, rootFolder);
            } catch (err) {
                return res.status(500).send({
                    error: { message: JSON.stringify(err.message || err) },
                });
            }

            return res.sendFile(wholePath, (err) => {
                if (err) {
                    // encoding user input
                    err.message = encodeSpecialCharacters(err.message);
                    next(
                        process.env.NODE_ENV === 'production'
                            ? JSON.stringify({
                                  error: {
                                      message: 'Unknown server error. See log for details.',
                                  },
                              })
                            : err,
                    );
                }
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

function hasRights(roles) {
    const isAdmin = _.some(roles, { id: -1 });
    const isDmsAdmin = _.some(roles, { id: -4 });

    return isAdmin || isDmsAdmin;
}

function encodeSpecialCharacters(content) {
    const dictionary = {
        '&': '&amp',
        '<': '&lt',
        '>': '&gt',
        '"': '&quot',
        "'": '&#x27',
        '/': '&#x2F',
        ';': '&#x3B',
        '(': '&#x28',
        ')': '&#x29',
    };

    const regexGen = `\\${Object.keys(dictionary).join('|\\')}`;
    const result = content.replace(new RegExp(regexGen, 'g'), (char) => dictionary[char]);
    return result;
}

function getFolderTree(rootFolder, roles) {
    const storageDir = rootFolder === 'logos' ? logosStorage : manualsStorage;

    try {
        checkAssetsFolder(storageDir, rootFolder);
    } catch (err) {
        throw err;
    }

    const dirTree = function (filename) {
        var filename = path.join(storageDir, filename); // absolute path
        const stats = fs.lstatSync(filename);
        const info = {
            id: filename,
            name: path.basename(filename),
        };

        if (stats.isDirectory()) {
            info.children = fs
                .readdirSync(filename)
                .filter((child) => {
                    // escape .keep
                    return (
                        child.charAt(0) != '.' &&
                        fs.lstatSync(path.join(filename, child)).isDirectory() &&
                        (hasRights(roles) || !child.startsWith(prefix))
                    );
                })
                .map((child) => {
                    // relative path
                    return dirTree(`${path.relative(storageDir, filename)}/${child}`);
                });
        }

        // change absolute path to relative
        info.id = path.relative(storageDir, filename);
        return info;
    };

    return new Promise((resolve, reject) => {
        resolve(dirTree(''));
    });
}

function getFolderFlatTree(rootFolder, filter, roles) {
    let storageDir = rootFolder === 'logos' ? logosStorage : manualsStorage;
    const items = [];

    if (filter) {
        storageDir = path.join(storageDir, filter);
    }

    try {
        checkAssetsFolder(storageDir, rootFolder);
    } catch (err) {
        throw err;
    }

    const dirTree = function (fName) {
        const filename = path.join(storageDir, fName); // absolute path
        let tableFilePath = '';
        tableFilePath = assetsServicePath;
        tableFilePath += filename.substr(assetsConfigPath.length, filename.length);

        tableFilePath = tableFilePath.replace(/\\/g, '/');
        const pathArr = tableFilePath.split('/');
        const isVisible = _.every(pathArr, (item) => {
            return !_.startsWith(item, '_');
        });
        const stats = fs.lstatSync(filename);
        const info = {
            id: filename,
            name: path.basename(filename),
            modified: stats.mtime,
            size: Math.floor(stats.size / 1024),
            path: tableFilePath,
            visible: isVisible ? 'Y' : 'N',
        };

        if (stats.isDirectory()) {
            fs.readdirSync(filename)
                .filter((child) => {
                    // display the logos files only on the logos tab
                    if (rootFolder === 'manuals' && filename === logosStorage) {
                        return false;
                    }

                    // escape .keep
                    return (
                        child.charAt(0) != '.' &&
                        (hasRights(roles) || !child.startsWith(prefix)) &&
                        child !== schemaLogosFolderName
                    );
                })
                .map((child) => {
                    // relative path
                    return dirTree(`${path.relative(storageDir, filename)}/${child}`);
                });
        } else {
            // Assuming it's a file. In real life it could be a symlink or
            // something else!
            items.push(info);
        }

        // change absolute path to relative
        if (filter) {
            info.id = path.join(filter, path.relative(storageDir, filename));
        } else {
            info.id = path.relative(storageDir, filename);
        }
        return items;
    };

    return new Promise((resolve, reject) => {
        if (!fs.existsSync(storageDir)) {
            return resolve([]);
        }

        resolve(dirTree(''));
    });
}

/**
 * @param {string} file file path
 * @param {string} rootFolder (logos or manuals)
 */
function checkAssetsFolder(file, rootFolder = 'logos') {
    let filePath = file;
    let assetsFolder = assetsConfigPath;

    if (rootFolder === 'logos') {
        // create the logos folder if it does not exist
        if (!fs.existsSync(`${assetsFolder}${path.sep}logos`)) {
            fs.mkdirSync(`${assetsFolder}${path.sep}logos`);
        }
    } else if (rootFolder === 'manuals') {
        // create the uploads folder if it does not exist
        if (!fs.existsSync(`${assetsFolder}${path.sep}uploads`)) {
            fs.mkdirSync(`${assetsFolder}${path.sep}uploads`);
        }
    }

    assetsFolder = assetsFolder.replace(/[/\\]/g, path.sep);
    filePath = filePath.replace(/[/\\]/g, path.sep);

    if (!filePath.startsWith(assetsFolder)) {
        throw new Error('Lack of permissions!');
    }

    return true;
}

function saveManual(relPath, data, rootFolder) {
    return new Promise((resolve, reject) => {
        const wholePath = path.resolve(
            rootFolder === 'logos' ? logosStorage : manualsStorage,
            relPath,
        );
        const folder = path.resolve(wholePath, '../');

        try {
            checkAssetsFolder(wholePath, rootFolder);
        } catch (err) {
            return reject(err);
        }

        const save = function () {
            if (!fs.existsSync(wholePath)) {
                fs.writeFile(wholePath, data, 'base64', (err) => {
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            } else {
                // revision
                fs.stat(wholePath, (err, stats) => {
                    if (err) {
                        reject(err);
                    }

                    fs.readFile(wholePath, (err, revData) => {
                        if (err) {
                            reject(err);
                        }

                        const birthtime = `${new Date(stats.ctime)
                            .toISOString()
                            .substring(0, 19)
                            .replace(/T/, '-')
                            .replace(/:/g, '-')}_`;
                        const basename = path.basename(wholePath);
                        const revisionName = wholePath.replace(
                            basename,
                            prefix + birthtime + basename,
                        );

                        // resave the previous version of the file as revision
                        fs.writeFile(revisionName, revData, (err) => {
                            if (err) {
                                reject(err);
                            }
                            // and save the new version of the file with the original name
                            fs.writeFile(wholePath, data, 'base64', (err) => {
                                if (err) {
                                    reject(err);
                                }
                                resolve();
                            });
                        });
                    });
                });
            }
        };

        if (!fs.existsSync(folder)) {
            mkdirp(folder, (err) => {
                // is recursive
                if (err) {
                    reject(err);
                }
                save();
            });
        } else {
            save();
        }
    });
}

function deleteFile(wholePath, rootFolder, createRevision) {
    return new Promise((resolve, reject) => {
        const dirnamePathArr = path.dirname(wholePath).split(path.sep);
        const dirname = dirnamePathArr[dirnamePathArr.length - 1];

        if (dirname === schemaLogosFolderName) {
            return reject('Lack of permissions!');
        }

        try {
            checkAssetsFolder(wholePath, rootFolder);
        } catch (err) {
            return reject(err);
        }

        if (createRevision) {
            fs.stat(wholePath, (err, stats) => {
                if (err) {
                    reject(err);
                }

                fs.readFile(wholePath, (err, revData) => {
                    if (err) {
                        reject(err);
                    }

                    const birthtime = `${new Date(stats.ctime)
                        .toISOString()
                        .substring(0, 19)
                        .replace(/T/, '-')
                        .replace(/:/g, '-')}_`;
                    const basename = path.basename(wholePath);

                    const revisionName = wholePath.replace(basename, prefix + birthtime + basename);

                    // save the last version of the file as revision
                    fs.writeFile(revisionName, revData, (err) => {
                        if (err) {
                            reject(err);
                        }
                        // and unlink the file with the original name
                        fs.unlink(wholePath, (err) => {
                            if (err) {
                                reject(err);
                            }
                            resolve();
                        });
                    });
                });
            });
        } else {
            fs.stat(wholePath, (err, stats) => {
                // check if file exists
                if (err) {
                    reject(err);
                }
                fs.unlink(wholePath, (err) => {
                    // remove file
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            });
        }
    });
}

function renameFolder(relPathToFolder, newPath, rootFolder) {
    return new Promise((resolve, reject) => {
        const storageDir = rootFolder === 'logos' ? logosStorage : manualsStorage;
        const wholePathOld = path.resolve(storageDir, relPathToFolder);
        const wholePathNew = path.resolve(storageDir, newPath);
        const oldDirPath = path.dirname(wholePathOld);
        const newDirPath = path.dirname(wholePathNew);
        const oldBasename = path.basename(wholePathOld);

        if (storageDir === wholePathOld) {
            return reject('Root folder cannot be renamed!');
        }

        if (
            oldBasename === schemaLogosFolderName ||
            oldBasename === orgLogosFolderName ||
            oldDirPath.endsWith(orgLogosFolderName)
        ) {
            return reject('Lack of permissions!');
        }

        try {
            checkAssetsFolder(wholePathOld, rootFolder);
            checkAssetsFolder(wholePathNew, rootFolder);
        } catch (err) {
            return reject(err);
        }

        if (!fs.existsSync(newDirPath)) {
            mkdirp(newDirPath, (err) => {
                // is recursive
                if (err) {
                    reject(err);
                }
                fs.rename(wholePathOld, wholePathNew, (err) => {
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            });
        } else {
            fs.rename(wholePathOld, wholePathNew, (err) => {
                if (err) {
                    reject(err);
                }
                resolve();
            });
        }
    });
}

function makeDir(newDirName, rootFolder) {
    return new Promise((resolve, reject) => {
        const newDirPath = path.resolve(
            rootFolder === 'logos' ? logosStorage : manualsStorage,
            newDirName,
        );
        try {
            checkAssetsFolder(newDirPath, rootFolder);
        } catch (err) {
            return reject(err);
        }

        if (!fs.existsSync(newDirPath)) {
            mkdirp(newDirPath, (err) => {
                // is recursive
                if (err) {
                    reject(err);
                }
                resolve();
            });
        } else {
            reject(new Error('Folder already exist'));
        }
    });
}

function removeDir(relPath, rootFolder, createRevision) {
    return new Promise((resolve, reject) => {
        const storageDir = rootFolder === 'logos' ? logosStorage : manualsStorage;
        const wholePathOld = path.resolve(storageDir, relPath);
        let wholePathNew = wholePathOld;
        const basename = path.basename(wholePathOld);
        const dirname = path.dirname(wholePathOld);

        if (storageDir == wholePathOld) {
            return reject('Root folder cannot be deleted!');
        }
        if (basename === schemaLogosFolderName || basename === orgLogosFolderName) {
            return reject('Lack of permissions!');
        }

        try {
            checkAssetsFolder(wholePathOld, rootFolder);
            checkAssetsFolder(wholePathNew, rootFolder);
        } catch (err) {
            return reject(err);
        }

        if (createRevision) {
            fs.stat(wholePathOld, (err, stats) => {
                if (err) {
                    reject(err);
                }

                const birthtime = new Date(stats.ctime)
                    .toISOString()
                    .substring(0, 19)
                    .replace(/T/, '-')
                    .replace(/:/g, '-');
                wholePathNew = path.resolve(
                    dirname,
                    `${prefix}${basename}-created-on-${birthtime}`,
                );

                fs.rename(wholePathOld, wholePathNew, (err) => {
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            });
        } else {
            fs.stat(wholePathOld, (err, stats) => {
                // check if folder exists
                if (err) {
                    reject(err);
                }
                fs.rmdir(wholePathOld, (err) => {
                    // remove folder
                    if (err) {
                        reject(err);
                    }
                    resolve();
                });
            });
        }
    });
}

app.post('/schema-logo', (req, res) => {
    const { data } = req.body;

    return checkLogin(req)
        .then((response) => {
            if (_.some(_.get(response, 'roles'), { id: -1 })) {
                try {
                    const logosDirPath = manualsStorage + path.sep + schemaLogosFolderName;

                    if (!fs.existsSync(logosDirPath)) {
                        fs.mkdirSync(logosDirPath);
                    }

                    return Bluebird.mapSeries(data, (logo) => {
                        if (logo.name.indexOf('..') !== -1) {
                            throw new Error('Lack of permissions!');
                        }

                        fs.writeFileSync(
                            manualsStorage +
                                path.sep +
                                schemaLogosFolderName +
                                path.sep +
                                logo.name,
                            logo.content,
                            'base64',
                        );
                    }).then((ignored) => {
                        return res.send({
                            result: true,
                            newTokens: response.newTokens,
                        });
                    });
                } catch (e) {
                    return res.status(500).send({
                        error: { message: JSON.stringify(e.message || e) },
                    });
                }
            }

            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions!',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/check-logo', (req, res) => {
    const { type } = req.body;
    const logoTypes = !Array.isArray(type) ? [type] : type;
    const result = {};

    Bluebird.mapSeries(logoTypes, (logoType) => {
        if (logoType.indexOf('..') !== -1) {
            throw new Error('Lack of permissions!');
        }

        try {
            result[logoType] = fs
                .statSync(
                    `${manualsStorage}${path.sep}${schemaLogosFolderName}${path.sep}${logoType}.png`,
                )
                .isFile();
        } catch (e) {
            result[logoType] = false;
        }
    })
        .then((ignored) => {
            return res.send(result);
        })
        .catch((err) => {
            return res.status(500).send({
                error: { message: JSON.stringify(err.message || err) },
            });
        });
});

app.delete('/schema-logo', (req, res) => {
    const { name } = req.body;

    if (name.indexOf('..') !== -1) {
        return res.status(500).send({ error: { message: 'Lack of permissions!' } });
    }

    return checkLogin(req)
        .then((response) => {
            if (_.some(_.get(response, 'roles'), { id: -1 })) {
                try {
                    fs.unlinkSync(
                        manualsStorage + path.sep + schemaLogosFolderName + path.sep + name,
                    );
                    return res.send({
                        result: true,
                        newTokens: response.newTokens,
                    });
                } catch (e) {
                    return res.status(500).send({
                        error: { message: JSON.stringify(e.message || e) },
                    });
                }
            }

            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions!',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.get('/public/schema-logos/:logoName', (req, res) => {
    const logoPath =
        manualsStorage + path.sep + schemaLogosFolderName + path.sep + req.params.logoName;

    fs.stat(logoPath, (err, stat) => {
        if (err) {
            return res.status(500).send({ error: 'Logo not found!' });
        }
        return res.sendFile(logoPath);
    });
});

app.get(`${assetsServicePath}/*`, (req, res) => {
    const restOfUrl = req.params[Object.keys(req.params)[0]];
    let wholeUrl = assetsConfigPath + path.sep + restOfUrl;

    if (_.endsWith(wholeUrl, '/')) {
        // don't return index.html if the url is an existing folder
        wholeUrl += '_please_dont_return_index_';
    }

    fs.stat(wholeUrl, (err, stat) => {
        if (err) {
            return res.send('Not found');
        }

        return res.sendFile(wholeUrl);
    });
});

function saveLog(data) {
    return new Promise((resolve, reject) => {
        const logsPath = path.resolve(__dirname, '../../../logs');
        const now = new Date();
        const timestamp = now.toISOString().replace(/T/, ' ').replace(/Z/, '');
        const filePath = path.join(logsPath, 'loginTest.log');

        const stream = fs.createWriteStream(filePath, { flags: 'a' });
        stream.write(`${timestamp}: ${data.message}\n`);
        stream.end();
        resolve();
    });
}

app.post('/logger', (req, res) => {
    saveLog(req.body)
        .then(() => {
            res.send(true);
        })
        .catch((err) => {
            console.log(err);
            res.status(500).send(err);
        });
});

function getLogos(storageDir) {
    const paths = [];

    const dirTree = function (filename) {
        var filename = path.join(storageDir, filename); // absolute path
        const stats = fs.lstatSync(filename);

        if (stats.isDirectory()) {
            fs.readdirSync(filename)
                .filter((child) => {
                    // escape .keep
                    return child.charAt(0) != '.';
                })
                .map((child) => {
                    // relative path
                    return dirTree(`${path.relative(storageDir, filename)}/${child}`);
                });
        } else {
            paths.push({
                path: path.join('/assets/logos', path.relative(storageDir, filename)),
            });
        }

        return paths;
    };

    return new Promise((resolve, reject) => {
        if (!fs.existsSync(storageDir)) {
            return resolve([]);
        }

        resolve(dirTree(''));
    });
}

// service for flutter mobile app
app.post('/mobile-device/getFirebaseProject', async (req, res) => {
    let { backendUrl } = tasConfig;
    if (tasConfig.multitenantBackend) {
        backendUrl = `${req.get('origin')}/api`;
    }
    const rawUrl = `${backendUrl}/mobile-device/getFirebaseProject`;

    const { vice, accessToken } = req.body;

    let token = typeof vice !== 'undefined' && vice !== null ? vice.viceToken : accessToken;
    if (token?.includes('Bearer')) {
        token = token.replace('Bearer ', '').trim();
    }

    const axiosConfig = {
        httpsAgent: new https.Agent({
            rejectUnauthorized: false,
        }),
        headers: {
            ...(token && { [tasConfig.authorizationHeader]: `Bearer ${token}` }),
            'Cache-Control': 'no-cache, no-store',
            Pragma: 'no-cache',
            Expires: 0,
        },
    };
    try {
        const response = await axios.get(`${rawUrl}`, axiosConfig);
        res.status(200).send(response.data);
    } catch (error) {
        res.status(500).send(error.message);
    }
});

app.post('/mobile-device/setDeviceToken', async (req, res) => {
    let { backendUrl } = tasConfig;

    const { deviceToken, accessToken, vice } = req.body;

    let token = typeof vice !== 'undefined' && vice !== null ? vice.viceToken : accessToken;
    if (token?.includes('Bearer')) {
        token = token.replace('Bearer ', '').trim();
    }

    if (tasConfig.multitenantBackend) {
        backendUrl = `${req.get('origin')}/api`;
    }

    const rawUrl = `${backendUrl}/mobile-device/${token}/setDeviceToken`;

    const axiosConfig = {
        httpsAgent: new https.Agent({
            rejectUnauthorized: false,
        }),
        headers: {
            ...(token && { [tasConfig.authorizationHeader]: `Bearer ${token}` }),
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store',
            Pragma: 'no-cache',
            Expires: 0,
        },
    };
    try {
        const response = await axios.post(`${rawUrl}`, { token: deviceToken }, axiosConfig);
        res.status(200).send(response.data);
    } catch (error) {
        res.status(500).send(error.message);
    }
});

app.post('/mobile-device/resetBadgeCounter', async (req, res) => {
    let { backendUrl } = tasConfig;

    const { accessToken, vice } = req.body;

    let token = typeof vice !== 'undefined' && vice !== null ? vice.viceToken : accessToken;
    if (token?.includes('Bearer')) {
        token = token.replace('Bearer ', '').trim();
    }

    if (tasConfig.multitenantBackend) {
        backendUrl = `${req.get('origin')}/api`;
    }

    const rawUrl = `${backendUrl}/mobile-device/${token}/resetBadgeCounter`;

    const axiosConfig = {
        httpsAgent: new https.Agent({
            rejectUnauthorized: false,
        }),
        headers: {
            ...(token && { [tasConfig.authorizationHeader]: `Bearer ${token}` }),
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store',
            Pragma: 'no-cache',
            Expires: 0,
        },
    };
    try {
        const response = await axios.post(`${rawUrl}`, {}, axiosConfig);
        res.status(200).send(response.data);
    } catch (error) {
        res.status(500).send(error.message);
    }
});

app.post('/mobile-device/getDomainWhitelist', async (req, res) => {
    let { backendUrl } = tasConfig;
    if (tasConfig.multitenantBackend) {
        backendUrl = `${req.get('origin')}/api`;
    }
    const rawUrl = `${backendUrl}/mobile-device/getDomainWhitelist`;

    const { vice, accessToken } = req.body;

    let token = typeof vice !== 'undefined' && vice !== null ? vice.viceToken : accessToken;
    if (token?.includes('Bearer')) {
        token = token.replace('Bearer ', '').trim();
    }

    const axiosConfig = {
        httpsAgent: new https.Agent({
            rejectUnauthorized: false,
        }),
        headers: {
            ...(token && { [tasConfig.authorizationHeader]: `Bearer ${token}` }),
            'Cache-Control': 'no-cache, no-store',
            Pragma: 'no-cache',
            Expires: 0,
        },
    };
    try {
        const response = await axios.get(`${rawUrl}`, axiosConfig);
        res.status(200).send(response.data);
    } catch (error) {
        res.status(500).send(error.message);
    }
});

// let { backendUrl } = tasConfig;
// // Proxy middleware
// app.use(
//     '/mobile-device/getFirebaseProject', // Match all routes that start with /mobile-device
//     createProxyMiddleware({
//         target: backendUrl,
//         changeOrigin: true,
//     }),
// );

// service for logos list in org. unit
app.get('/logos', (req, res) => {
    getLogos(logosStorage)
        .then((results) => {
            return res.send(results);
        })
        .catch((err) => {
            return res.status(500).send(err);
        });
});

// org. logos
app.post('/org-logo', (req, res) => {
    const { data, orgId } = req.body;

    if (!data) {
        return res.status(500).send({
            error: {
                codeName: 'MISSING_PARAMETER',
                message: 'Missing "data" or parameter!',
            },
        });
    }

    return checkLogin(req)
        .then((response) => {
            if (_.some(_.get(response, 'roles'), { id: -1 })) {
                try {
                    const logosDirPath = logosStorage + path.sep + orgLogosFolderName;
                    const orgDirPath = logosDirPath;

                    if (!fs.existsSync(logosDirPath)) {
                        fs.mkdirSync(logosDirPath);
                    }

                    return Bluebird.mapSeries(data, (logo) => {
                        if (logo.name.indexOf('..') !== -1) {
                            throw new Error(
                                'Lack of permissions! (post org-logo - suspicious request)',
                            );
                        }

                        const writePath = orgDirPath + path.sep + logo.name;

                        fs.writeFileSync(writePath, logo.content, 'base64');
                    }).then((ignored) => {
                        return res.send({
                            result: true,
                            newTokens: response.newTokens,
                        });
                    });
                } catch (e) {
                    return res.status(500).send({
                        error: { message: JSON.stringify(e.message || e) },
                    });
                }
            }

            return res.status(500).send({
                error: {
                    codeName: 'LACK_OF_PERMISSIONS',
                    message: 'Lack of permissions! (post org-logo)',
                },
            });
        })
        .catch((err) => {
            const status = err?.error?.status || 500;
            return res.status(status).send(err);
        });
});

app.post('/babel-transform', (req, res) => {
    try {
        res.send({
            js: babel.transform(req.body.js, {
                presets: [
                    [
                        '@babel/preset-env',
                        {
                            targets: {
                                // base on browserlist defaults+yPhantomJS 2.1
                                android: '67',
                                chrome: '73',
                                edge: '17',
                                firefox: '60',
                                ie: '11',
                                ios: '11.3',
                                opera: '60',
                                safari: '6', // due to Phantom 2.1
                                // safari: '12', without Phantom
                                samsung: '8.2',
                            },
                            modules: false, // do not replace undefined "this" to "void 0"
                        },
                    ],
                    '@babel/preset-react',
                ],
            }).code,
        });
    } catch (err) {
        res.status(500).send(err);
    }
});

// returns content of local file
// /get-file-content?path=../../../assets/libs/ckeditor/contents.css
app.get('/get-file-content', (req, res) => {
    const filePath = req.query.path;

    if (!filePath) {
        res.status(500).send('Path is missing in query! (?path=)');
    }

    fs.readFile(path.resolve(__dirname, filePath), 'utf-8', (err, data) => {
        if (err) {
            res.status(500).send(err);
        }

        res.send(data);
    });
});

const pdfjsPath = path.dirname(require.resolve('pdfjs-dist'));
const cmapsPath = pdfjsPath.replace(/(\/|\\)build$/, '$1cmaps');
app.use('/pdfjs/cmaps', express.static(cmapsPath));

// Example how initialState, which is the same for all users, is enriched with
// user state. With state-less Flux, we don't need instances.
app.use((req, res, next) => {
    const acceptsLanguages = req.acceptsLanguages(config.appLocales);

    /* req.userState = {
        i18n: {
            locales: acceptsLanguages || config.defaultLocale
        },
        todos: {
            list: [
                {id: 2, title: 'relax'}
            ]
        }
  }; */

    next();
});

app.get('*', (req, res, next) => {
    render(req, res); // .catch(next);
});

app.on('mount', () => {
    console.log('App is available at %s', app.mountpath);
});

module.exports = app;
