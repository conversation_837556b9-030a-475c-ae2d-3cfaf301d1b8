import React from 'react';
import ReactDOMServer from 'react-dom/server';
import Html from './html.react';
import config from '../config';

const _ = require('lodash');
const configReader = require('../../common/configReader');

const tasConfig = configReader();

function getPageHtml() {
    const appHtml = `
        <section id="app">
            <div class="ball-loader">
                <div class="ball-loader-ball ball1"></div>
                <div class="ball-loader-ball ball2"></div>
                <div class="ball-loader-ball ball3"></div>
            </div>
        </section>
    `;

    // Serialize app state for client.
    const scriptHtml = `
    <script>
        if (${tasConfig.clearAuthCache}) {
            document.execCommand('ClearAuthenticationCache', 'false');
        }
        window.config = ${JSON.stringify(tasConfig)};
        window.lodash = ${Function.prototype.toString.call(_)};

        window.onerror = function(message, source, lineno, colno, error) {
            if (navigator.userAgent.indexOf('Trident') != -1 || navigator.userAgent.indexOf("MSIE") != -1) {
                alert('Fatal error: Internet Explorer is not supported anymore. Please try another browser.')
            }
        };
    </script>
    <script src="/assets/libs/urlPolyfill.min.js"></script>
    <script src="${config.isProduction ? '/dist/vendors.js' : `${tasConfig.localhostUrl}:${tasConfig.webpackPort}/build/vendors.js`}"></script>
    <script src="${config.isProduction ? '/dist/app.js' : `${tasConfig.localhostUrl}:${tasConfig.webpackPort}/build/app.js`}"></script>
    <script src="/assets/libs/browser-polyfill.min.js"></script>`;

    const title = 'Team Assistant'; // DocumentTitle.rewind();

    return `<!DOCTYPE html>${ReactDOMServer.renderToStaticMarkup(
        <Html
            bodyHtml={appHtml + scriptHtml}
            isProduction={config.isProduction}
            title={title}
            version={config.version}
        />,
    )}`;
}

function renderPage(req, res) {
    let pathname = req.url;
    if (pathname === '/login' || pathname === '/authenticate') {
        pathname = '/';
    }
    return new Promise((resolve, reject) => {
        res.send(getPageHtml());
        resolve();
    });
}

export default function (req, res) {
    return renderPage(req, res);
}
