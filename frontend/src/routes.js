import React from 'react';
import { Route, Switch, Redirect } from 'react-router-dom';
// components 5.0
import AdministrationMenu from './components5.0/pages/administration/AdministrationMenu';
import Cases from './components5.0/pages/Cases';
import Dashboard from './components5.0/pages/Dashboard';
import Documents from './components5.0/pages/Documents';
import DynTables from './components5.0/pages/administration/DynTables';
import DynTableDetail from './components5.0/pages/administration/DynTableDetail';
import HrAgenda from './components5.0/pages/administration/HrAgenda';
import Licenses from './components5.0/pages/administration/Licenses';
import Manuals from './components5.0/pages/Manuals';
import Overviews from './components5.0/pages/Overviews';
import Plans from './components5.0/pages/Plans';
import PlanDetail from './components5.0/pages/PlanDetail';
import Structure from './components5.0/pages/Structure';
import StructureDetail from './components5.0/pages/StructureDetail';
import Task from './components5.0/pages/Task';
import Tasks from './components5.0/pages/Tasks';
import UserAccount from './components5.0/pages/UserAccount';
import Users from './components5.0/pages/Users';
import UserDetail from './components5.0/pages/UserDetail';
import VariableAssignment from './components5.0/pages/VariableAssignment';
import Sequences from './components5.0/pages/administration/Sequences';
import NewsManagement from './components5.0/pages/administration/NewsManagement';
import NewsManagementPostDetail from './components5.0/pages/administration/NewsManagementPostDetail';
import NewsTagsManagement from './components5.0/pages/administration/NewsTagsManagement';
import News from './components5.0/pages/News';

// old components
import AddTask from './pages/addTask.react';
import Administration from './pages/administration.react';
import AdministrationAuth from './pages/administrationAuth.react';
import AdministrationConfiguration from './pages/administrationConfiguration.react';
import AdministrationConsole from './pages/administrationConsole.react';
import AdministrationCron from './pages/administrationCron.react';
import AdministrationCronRunLogs from './pages/administrationCronRunLogs.react';
import AdministrationCrons from './pages/administrationCrons.react';
import AdministrationHealthStatus from './pages/administrationHealthStatus.react';
import AdministrationMailsQueue from './pages/administrationMailsQueue.react';
import AdministrationMigrations from './pages/administrationMigrations.react';
import AdministrationProcessingQueues from './pages/administrationProcessingQueues.react';
import AdministrationScheduledTasks from './pages/administrationScheduledTasks.react';
import AdministrationSchema from './pages/administrationSchema.react';
import AdministrationScript from './pages/administrationScript.react';
import AdministrationScripts from './pages/administrationScripts.react';
import AdministrationServiceOperations from './pages/administrationServiceOperations.react';
import Case from './pages/case.react';
import CaseGraph from './pages/caseGraph.react';
import Certificates from './pages/certificates.react';
import Competence from './pages/competence.react';
import CompetenceRule from './pages/competenceRule.react';
import DmsAssignAttributes from './pages/dmsAssignAttributes.react';
import DmsAttribute from './pages/dmsAttribute.react';
import EntityImport from './pages/entityImport.react';
import Event from './pages/event.react';
import EventRule from './pages/eventRule.react';
import Events from './pages/events.react';
import GraphSettings from './pages/graph.react';
import Guides from './pages/administrationGuides.react';
import GuideSettings from './pages/administrationGuideSettings.react';
import ImportModels from './pages/importModels.react';
import ImportStates from './pages/importStates.react';
import Logs from './pages/logs/administrationLogs.react';
import ManualEvent from './pages/manualEvent.react';
import MappingVariables from './pages/mappingVariables.react';
import MappingVariablesVisual from './pages/mappingVariablesVisual.react';
import MenuLink from './pages/menuLink.react';
import NotFound from './app/notFound.react';
import Reports from './pages/reports.react';
import ReportSettings from './pages/report.react';
import Roles from './pages/roles.react';
import Role from './pages/role.react';
import Template from './pages/template.react';
import TemplateConnection from './pages/templateConnection.react';
import TemplateCopyImport from './pages/templateCopyImport.react';
import TemplateHeader from './pages/templateHeader.react';
import TemplateLink from './pages/templateLink.react';
import TemplatePrint from './pages/templatePrint.react';
import Templates from './pages/templates.react';
import TemplateTask from './pages/templateTask.react';
import TemplateVariable from './pages/templateVariable.react';
import UploadCsv from './pages/uploadCsv.react';
import UsageStats from './pages/administrationUsageStatistics.react';
import XmlProcessImport from './pages/xmlProcessImport.react';

export default () => (
    <Switch>
        <Route exact path="/" component={Dashboard} />
        <Redirect from="/cases/overviews" to="/overviews" />
        <Redirect from="/cases/overviews/:cvId" to="/overviews/:cvId" />
        <Redirect from="/cases/active" to="/cases/all" />
        <Redirect from="/cases/inactive" to="/cases/suspended" />
        <Redirect from="/tasks/solvers" to="/tasks/all" />
        <Redirect from="/tasks/done" to="/tasks/all" />
        <Redirect exact from="/reports" to="/reports/graphs" />
        {/* tas-847: if next task has the same id (task loop), reload component */}
        <Redirect exact from="/tasks/task/:taskId/redirect" to="/tasks/task/:taskId" />
        {/* versioning - dashboard favourites redirect */}
        <Redirect
            exact
            from="/templates/template/:templateId"
            to="/templates/template/:templateId/1"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/headers"
            to="/templates/template/:templateId/1/headers"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/tasks"
            to="/templates/template/:templateId/1/tasks"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/variables"
            to="/templates/template/:templateId/1/variables"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/statuses"
            to="/templates/template/:templateId/1/statuses"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/print"
            to="/templates/template/:templateId/1/print"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/graph"
            to="/templates/template/:templateId/1/graph"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/connection"
            to="/templates/template/:templateId/1/connection"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/variables/mapping"
            to="/templates/template/:templateId/1/variables/mapping"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/variables/mapping-visual"
            to="/templates/template/:templateId/1/variables/mapping-visual"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-header/:templateHeaderId"
            to="/templates/template/:templateId/1/template-header/:templateHeaderId"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-task/:templateTaskId"
            to="/templates/template/:templateId/1/template-task/:templateTaskId"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-task/:templateTaskId/:tabName"
            to="/templates/template/:templateId/1/template-task/:templateTaskId/:tabName"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-link/:templateLinkId"
            to="/templates/template/:templateId/1/template-link/:templateLinkId"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-link/:templateLinkId/:tabName"
            to="/templates/template/:templateId/1/template-link/:templateLinkId/:tabName"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-variable/:templateVariableId"
            to="/templates/template/:templateId/1/template-variable/:templateVariableId"
        />
        <Redirect
            exact
            from="/templates/template/:templateId/template-print/:templatePrintId"
            to="/templates/template/:templateId/1/template-print/:templatePrintId"
        />
        <Route exact path="/tasks/task/:taskId/:tabName/add-task" component={AddTask} />
        <Route exact path="/tasks/task/:taskId/edit-historical/:taskhId" component={Task} />
        <Route
            exact
            path="/tasks/task/:taskId/edit-historical/:taskhId/:tabName"
            component={Task}
        />
        <Route exact path="/tasks/task/:taskId/historical/:taskhId/:tabName" component={Task} />
        <Route exact path="/tasks/task/:taskId" component={Task} />
        <Route exact path="/tasks/task/:taskId/:tabName" component={Task} />
        <Route exact path="/tasks/task/:taskId/historical/:taskhId" component={Task} />
        <Route exact path="/tasks" component={Tasks} />
        <Route exact path="/tasks/:tabName" component={Tasks} />
        <Route exact path="/tasks/:tabName/:taskId" component={Tasks} />
        {/* /tasks/to-pull/taskId */}
        <Route exact path="/cases/case/:caseId" component={Case} />
        <Route path="/cases/case/:caseId/graph" component={CaseGraph} />
        <Route path="/cases/case/:caseId/:tabName" component={Case} />
        <Route exact path="/cases" component={Cases} />
        <Route path="/cases/:tabName" component={Cases} />
        <Route exact path="/overviews" component={Overviews} />
        <Route exact path="/overviews/:cvId" component={Overviews} />
        <Route exact path="/reports" component={Reports} />
        <Route exact path="/reports/overviews/settings/:reportId" component={ReportSettings} />
        <Route exact path="/reports/graphs/settings/:graphId" component={GraphSettings} />
        <Route exact path="/reports/graphs/settings/:graphId/:tabName" component={GraphSettings} />
        <Route exact path="/reports/:tabName" component={Reports} />
        <Route exact path="/reports/:tabName/:graphId" component={Reports} />
        <Route exact path="/templates/import" component={TemplateCopyImport} />
        <Route path="/templates/import/:tabName" component={TemplateCopyImport} />
        <Route exact path="/templates/copy/:templateId/:versionId" component={TemplateCopyImport} />
        <Route
            path="/templates/copy/:templateId/:versionId/:tabName"
            component={TemplateCopyImport}
        />
        <Route exact path="/templates/template/:templateId/:versionId" component={Template} />
        <Route
            path="/templates/template/:templateId/:versionId/connection"
            component={TemplateConnection}
        />
        <Route
            path="/templates/template/:templateId/:versionId/variables/mapping"
            component={MappingVariables}
        />
        <Route
            path="/templates/template/:templateId/:versionId/variables/import-models"
            component={ImportModels}
        />
        <Route
            path="/templates/template/:templateId/:versionId/variables/import-states/:tabName"
            component={ImportStates}
        />
        <Route
            path="/templates/template/:templateId/:versionId/variables/mapping-visual"
            component={MappingVariablesVisual}
        />
        <Route
            path="/templates/template/:templateId/:versionId/template-header/:templateHeaderId"
            component={TemplateHeader}
        />
        <Route
            exact
            path="/templates/template/:templateId/:versionId/template-task/:templateTaskId"
            component={TemplateTask}
        />
        <Route
            exact
            path="/templates/template/:templateId/:versionId/template-task/:templateTaskId/:tabName"
            component={TemplateTask}
        />
        <Route
            exact
            path="/templates/template/:templateId/:versionId/variable-assignment/:templateTaskId/:tabName?"
            component={VariableAssignment}
        />
        <Route
            exact
            path="/templates/template/:templateId/:versionId/variable-assignment/:templateTaskId/test-task/:taskId"
            component={VariableAssignment}
        />
        <Route
            path="/templates/template/:templateId/:versionId/template-link/:templateLinkId"
            component={TemplateLink}
        />
        <Route
            path="/templates/template/:templateId/:versionId/template-link/:templateLinkId/:tabName"
            component={TemplateLink}
        />
        <Route
            exact
            path="/templates/template/:templateId/:versionId/template-variable/:templateVariableId/clone"
            component={TemplateVariable}
        />
        <Route
            path="/templates/template/:templateId/:versionId/template-variable/:templateVariableId"
            component={TemplateVariable}
        />
        <Route
            path="/templates/template/:templateId/:versionId/template-print/:templatePrintId"
            component={TemplatePrint}
        />
        <Route
            path="/templates/template/:templateId//:versionIdtemplate-print/:templatePrintId/:tabName"
            component={TemplatePrint}
        />
        <Route path="/templates/template/:templateId/:versionId/:tabName" component={Template} />
        <Route exact path="/templates" component={Templates} />
        <Route path="/templates/:tabName" component={Templates} />
        <Route exact path="/users" component={Users} />
        <Route exact path="/users/:tabName" component={Users} />
        <Route exact path="/users/user/:userId" component={UserDetail} />
        <Route exact path="/roles" component={Roles} />
        <Route exact path="/roles/:tabName" component={Roles} />
        <Route exact path="/roles/role/:roleId" component={Role} />
        <Route exact path="/roles/role/:roleId/:tabName" component={Role} />
        <Route exact path="/roles/competences/competence/:competenceId" component={Competence} />
        <Route
            exact
            path="/roles/competences/competence/:competenceId/:tabName"
            component={Competence}
        />
        <Route
            exact
            path="/roles/competence-rules/rule/:competenceRuleId"
            component={CompetenceRule}
        />
        <Route
            exact
            path="/roles/competence-rules/rule/:competenceRuleId/:tabName"
            component={CompetenceRule}
        />
        <Route exact path="/structure" component={Structure} />
        <Route exact path="/structure/org-unit/:orgUnitId" component={StructureDetail} />
        <Route exact path="/events" component={Events} />
        <Route exact path="/events/event/:eventId" component={Event} />
        <Route exact path="/events/event/:eventId/:tabName" component={Event} />
        <Route exact path="/events/event/:eventId/rules/rule/:ruleId" component={EventRule} />
        <Route
            exact
            path="/events/event/:eventId/rules/rule/:ruleId/:tabName"
            component={EventRule}
        />
        <Route exact path="/documents" component={Documents} />
        <Route path="/documents/:tabName" component={Documents} />
        <Route exact path="/administration/configuration" component={AdministrationConfiguration} />
        <Route
            exact
            path="/administration/configuration/:tabName"
            component={AdministrationConfiguration}
        />
        <Route exact path="/settings" component={UserAccount} />
        <Route exact path="/administration/public-files" component={Manuals} />
        <Route path="/administration/public-files/:tabName" component={Manuals} />
        <Route path="/administration-menu" component={AdministrationMenu} />

        <Route exact path="/administration/certificates" component={Certificates} />
        <Route exact path="/administration/hr-agenda/:tabName?" component={HrAgenda} />
        <Route exact path="/administration/news-management" component={NewsManagement} />
        <Route exact path="/administration/news-management/post/:postId" component={NewsManagementPostDetail} />
        <Route exact path="/administration/news-tags-management" component={NewsTagsManagement} />
        <Route exact path="/administration/health-status" component={AdministrationHealthStatus} />
        <Route exact path="/administration/health-status/:tabName" component={AdministrationHealthStatus} />
        <Route exact path="/administration/service-console" component={AdministrationConsole} />
        <Route exact path="/administration/logs" component={Logs} />
        <Route exact path="/administration/logs/:tabName" component={Logs} />
        <Route exact path="/administration/xml-process-import" component={XmlProcessImport} />
        <Route
            exact
            path="/administration/xml-process-import/:tabName"
            component={XmlProcessImport}
        />
        <Route exact path="/administration/usage-statistics" component={UsageStats} />
        <Route exact path="/administration/usage-statistics/:tabName" component={UsageStats} />
        <Route exact path="/administration/guides" component={Guides} />
        <Route exact path="/administration/guide/:guideId" component={GuideSettings} />
        <Route exact path="/administration/scripts" component={AdministrationScripts} />
        <Route exact path="/administration/scripts/:tabName" component={AdministrationScripts} />
        <Route
            exact
            path="/administration/scripts/script/:scriptId"
            component={AdministrationScript}
        />
        <Route
            exact
            path="/administration/scripts/react-script/:scriptId"
            component={AdministrationScript}
        />
        <Route
            exact
            path="/administration/scripts/calculation-script/:scriptId"
            component={AdministrationScript}
        />
        <Route exact path="/administration/crons" component={AdministrationCrons} />
        <Route exact path="/administration/crons/:tabName" component={AdministrationCrons} />
        <Route exact path="/administration/crons/cron/:cronId" component={AdministrationCron} />
        <Route
            exact
            path="/administration/crons/history/:cronRunId"
            component={AdministrationCronRunLogs}
        />
        <Route
            exact
            path="/administration/crons/history/:cronRunId/:tabName"
            component={AdministrationCronRunLogs}
        />
        <Route
            exact
            path="/administration/scheduled-tasks"
            component={AdministrationScheduledTasks}
        />
        <Route
            exact
            path="/administration/scheduled-tasks/:tabName"
            component={AdministrationScheduledTasks}
        />
        <Route exact path="/administration/sequences" component={Sequences} />
        <Route exact path="/administration/licenses" component={Licenses} />
        <Route exact path="/administration/authentication" component={AdministrationAuth} />
        <Route exact path="/administration/schema" component={AdministrationSchema} />
        <Route exact path="/administration/emails-queue" component={AdministrationMailsQueue} />
        <Route exact path="/administration/schema/:tabName" component={AdministrationSchema} />
        <Route exact path="/administration/import-structure/:tabName" component={EntityImport} />
        <Route
            exact
            path="/administration/service-operations"
            component={AdministrationServiceOperations}
        />
        <Route
            exact
            path="/administration/service-operations/:tabName"
            component={AdministrationServiceOperations}
        />
        <Route exact path="/administration/migrations" component={AdministrationMigrations} />
        <Route
            exact
            path="/administration/processing-queues"
            component={AdministrationProcessingQueues}
        />

        {/* NEW DYN TABLES */}
        <Route exact path="/administration/dyn-tables" component={DynTables} />
        <Route
            exact
            path="/administration/dyn-tables/:dynTableId"
            component={DynTableDetail}
        />
        <Route
            exact
            path="/administration/dyn-tables/:dynTableId/:tabName"
            component={DynTableDetail}
        />

        <Route exact path="/administration" component={Administration} />
        <Route exact path="/administration/:tabName" component={Administration} />
        <Route
            exact
            path="/administration/dms-attributes/attribute/:attributeId"
            component={DmsAttribute}
        />
        <Route exact path="/administration/dms-attributes/assign" component={DmsAssignAttributes} />

        <Route exact path="/administration/settings/upload-csv" component={UploadCsv} />

        <Route exact path="/plans" component={Plans} />
        <Route exact path="/plans/plan/:planId" component={PlanDetail} />

        <Route path="/manualevent" component={ManualEvent} />
        <Route path="/menu-link" component={MenuLink} />
        <Route exact path="/public-files" component={Manuals} />
        <Route path="/public-files/:tabName" component={Manuals} />
        <Route exact path="/news" component={News} />
        <Route component={NotFound} />
    </Switch>
);
