import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid2';
import Container from '@mui/material/Container';
import { Form } from './components';
// components
import News from './components/News';
import ModalConfirm from '../Modals/ModalConfirm';
import ModalMaintenance from '../sections/administration/maintenance/ModalMaintenance';
import AlertsList from '../../components/alerts/alertsList.react';
// hooks
import { useResponsive } from '../hooks/use-responsive';
import { useBoolean } from '../hooks/use-boolean';
import { useStore } from '../zustand/boundStore';
// utils
import { isSupportedBrowser } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';
// flux
import AlertsActions from '../../components/alerts/alerts.actions';
import LoggedUserActions from '../../flux/loggedUser.actions';

const SigninSimple = (): JSX.Element => {
    const { t } = useTranslation();

    const [authConfig, setAuthConfig] = useState([]);
    const [showNews, setShowNews] = useState(true);
    const [, setLoadingAuthorities] = useState(false);
    const theme = useTheme();
    const isDarkMode = theme.palette.mode === 'dark';
    const smUp = useResponsive('up', 'sm');

    const mobileAppActive = useStore((state) => state.mobileAppActive);
    const setMobileAppActive = useStore((state) => state.setMobileAppActive);

    const confirmModal = useBoolean(false);

    const darkTheme = localStorage.getItem('darkTheme') === 'true';

    function tryDarkTheme() {
        localStorage.setItem('darkTheme', 'true');
        localStorage.setItem('triedDarkTheme', 'true');
        LoggedUserActions.switchDarkMode(true);
    }

    function closeModal() {
        confirmModal.onFalse();
        window.localStorage.setItem('unsupportedBrowserSeen', 'true');
    }

    useEffect(() => {
        // -------------darkTheme-------------------------
        if (
            !darkTheme &&
            window.matchMedia('(prefers-color-scheme: dark)')?.matches === true &&
            localStorage.getItem('triedDarkTheme') !== 'true'
        ) {
            AlertsActions.addAlert({
                type: 'info',
                message: t('tryDarkTheme'),
                defaultOnClickAction: tryDarkTheme,
                show: true,
            });
        }
        LoggedUserActions.switchDarkMode(darkTheme);

        // -------------supported browser-------------------------
        if (
            !isSupportedBrowser(navigator.userAgent) &&
            !window.localStorage.getItem('unsupportedBrowserSeen')
        ) {
            confirmModal.onTrue();
        }

        // -------------authorities-------------------------

        setLoadingAuthorities(true);
        ApiRequest.get('/authorization/list').then((result: any) => {
            setAuthConfig(result.authConfig);
            if (result.loginConfig?.login?.showNews !== undefined) {
                setShowNews(result.loginConfig.login.showNews);
            }
            setMobileAppActive(result.mobileAppActive);
            setLoadingAuthorities(false);
        });
    }, []);

    let gridBackgroundColor = 'none';
    if (showNews || !smUp) {
        if (isDarkMode) {
            gridBackgroundColor = theme.palette.common.black;
        } else {
            gridBackgroundColor = theme.palette.common.white;
        }
    }

    return (
        <>
            <Box
                position="relative"
                display="flex"
                alignItems="center"
                justifyContent="center"
                height="100svh"
                width={1}
                bgcolor="background.default"
            >
                <Grid container height={1} direction={{ md: 'row-reverse' }}>
                    <Grid
                        container
                        alignItems="center"
                        justifyContent="center"
                        size={{ xs: 12, md: showNews ? 6 : 12, lg: showNews ? 6 : 12 }}
                        sx={{
                            backgroundColor: gridBackgroundColor,
                        }}
                    >
                        {showNews || !smUp ? (
                            <Container maxWidth="xs">
                                <Form
                                    authConfig={authConfig}
                                    showNews={showNews}
                                    mobileAppActive={mobileAppActive}
                                />
                            </Container>
                        ) : (
                            <Card sx={{ padding: '2rem 4rem' }}>
                                <Container maxWidth="xs">
                                    <Form
                                        authConfig={authConfig}
                                        showNews={showNews}
                                        mobileAppActive={mobileAppActive}
                                    />
                                </Container>
                            </Card>
                        )}
                    </Grid>
                    {smUp && showNews && (
                        <Grid
                            container
                            justifyContent="center"
                            alignItems="center"
                            size={{ xs: 12, md: 6, lg: 6 }}
                        >
                            <Container maxWidth="lg">
                                <News />
                            </Container>
                        </Grid>
                    )}
                </Grid>
            </Box>
            <ModalConfirm
                text={t('unsupportedBrowser')}
                isOpen={confirmModal.value}
                onClose={() => closeModal}
                onConfirm={() => closeModal}
            />
            <ModalMaintenance />
            <AlertsList />
        </>
    );
};

export default SigninSimple;
