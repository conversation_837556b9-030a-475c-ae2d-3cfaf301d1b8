import React, { useEffect, useState } from 'react';
// @mui
import {
    Box, Typography, Stack, Skeleton,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
// hooks
import { useTranslation } from 'react-i18next';
// components
import SectionsCard from '../../../SectionsCard';
import TextMaxLine from '../../../text-max-line/text-max-line';
import { SycaNewsLogo, TeamAssistantNewsLogo } from '../../../illustrations/Illustrations';
// type
import { News } from './type';

const News = () => {
    const { t } = useTranslation();
    // const theme = useTheme();
    // const isDarkMode = theme.palette.mode === 'dark';

    const [news, setNews] = useState<any>(null);
    const [loading, setLoading] = useState(true);

    // get item userLang from locale storage
    const userLang = localStorage.getItem('userLang');

    useEffect(() => {
        const fetchData = async () => {
            const spaceId = 'ddm1175g9ues';
            const environment = 'master';
            const accessToken = (window as any)?.config.contentfullAccessToken;
            const contentTypeId = 'loginPageNews';
            const localeCode = userLang === 'cs' ? 'cs' : 'en-US';

            // eslint-disable-next-line max-len
            const url = `https://cdn.contentful.com/spaces/${spaceId}/environments/${environment}/entries?access_token=${accessToken}&content_type=${contentTypeId}&order=-fields.date&limit=3&include=2&locale=${localeCode}`;

            try {
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const json = await response.json();
                setNews(json.items);
            } catch (error: any) {
                console.log(error.message);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    return (
        <Grid container spacing={4} px={{ lg: 10, xl: 25 }}>
            <Grid size={12}>
                <Box display="flex" flexDirection="row" alignItems="center" justifyContent="center">
                    {(window.config as any).teamassistantTheme ? (
                        <TeamAssistantNewsLogo sx={{ height: 46 }} />
                    ) : (
                        <SycaNewsLogo sx={{ height: 46 }} />
                    )}

                    {/* <Box
                        component="img"
                        src="../../../../../assets/images/logo/logo_tas_news_black_green.svg"

                    /> */}
                    {/* <Typography
                    color="text.secondary"
                    fontWeight={800}
                    sx={{
                        fontSize: "3.75rem",
                        lineHeight: "5rem",
                        letterSpacing: "-0.4134rem",
                        textAlign: " left",
                    }}
                >
                    news
                </Typography> */}
                </Box>
            </Grid>
            <Grid size={12}>
                <Typography variant="subtitle1" align="center">
                    {t('whatsNewInTAS')}
                </Typography>
                <Typography variant="subtitle1" align="center">
                    {t('whatsNewInTASDescription')}
                </Typography>
            </Grid>
            {loading ? (
                <Grid size={12}>
                    <Grid container spacing={3}>
                        {[...Array(3)].map((_, i: number) => {
                            return (
                                <Grid
                                    size={{
                                        xs: 12,
                                        sm: 6,
                                        md: 12,
                                        lg: 12,
                                    }}
                                    key={i}
                                >
                                    <Skeleton height="7.5rem" />
                                </Grid>
                            );
                        })}
                    </Grid>
                </Grid>
            ) : (
                <Grid size={12}>
                    <Grid container spacing={3}>
                        {news?.map((item: News, i: number) => {
                            const { subtitle, title, url } = item.fields;
                            return (
                                <Grid
                                    size={{
                                        xs: 12,
                                        sm: 6,
                                        md: 12,
                                        lg: 12,
                                    }}
                                    key={i}
                                >
                                    <Box component="a" target="_blank" href={url}>
                                        <SectionsCard key={url}>
                                            <Typography variant="h2" mb={1}>
                                                {title}
                                            </Typography>
                                            <TextMaxLine variant="subtitle1">
                                                {subtitle}
                                            </TextMaxLine>
                                        </SectionsCard>
                                    </Box>
                                </Grid>
                            );
                        })}
                    </Grid>
                </Grid>
            )}

            <Grid size={12}>
                <Stack width={1} justifyContent="center">
                    <Typography
                        width={1}
                        component="a"
                        href="https://www.syca.app/news"
                        target="_blank"
                        align="center"
                        sx={{
                            textDecoration: 'underline',
                        }}
                    >
                        {t('overviewOfAllNews')}
                    </Typography>
                </Stack>
            </Grid>
        </Grid>
    );
};

export default News;
