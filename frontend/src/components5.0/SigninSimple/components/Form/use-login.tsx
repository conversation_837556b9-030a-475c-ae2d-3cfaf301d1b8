import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { get } from 'lodash';
import ClientOAuth2 from 'client-oauth2';
// hooks
import { useRouter } from '../../../routes/hooks/use-router';
// utils
import { getBackendUrl, browserStorage } from '../../../../common/utils';
// flux
import AlertsActions from '../../../../components/alerts/alerts.actions';
import HistoryStore from '../../../../flux/history.store';
import LoggedUserActions from '../../../../flux/loggedUser.actions';

export const useLogin = (authConfig = []) => {
    const [isLoging, setIsLoging] = useState(false);
    const [previousURL, setPreviousURL] = useState<string | null>(null);
    const [useSessionStorage] = useState(false);

    const router = useRouter();

    const { t } = useTranslation();
    /**
     * getPrevURL method gets previous URL address and set state
     */
    function getPrevURL() {
        setPreviousURL(HistoryStore.getState().prevPath);
    }

    useEffect(() => {
        getPrevURL();
    }, []);

    function onClickLogin(authId: number | null, userName?: string, pass?: string, e?: any) {
        e?.preventDefault();
        setIsLoging(true);
        // const userName = this.refs.userName.value;
        // const pass = this.refs.pass.value;

        // alert popup if not login

        const options: any = {};

        if (authId) {
            options.body = { auth_id: authId, setCookies: true };
        } else {
            const authArr: any = authConfig.filter((auth: any) => {
                return auth.auth_module === 'Password';
            });

            if (authArr[0] && authArr[0].auth_id) {
                options.body = {
                    auth_id: authArr[0].auth_id,
                    setCookies: true,
                };
            }
        }

        let authenticateUrl = `${getBackendUrl()}/authenticate`;

        if (
            previousURL !== null &&
            previousURL !== '/login' &&
            previousURL !== '/authenticate' &&
            previousURL !== '/set-expired-password'
        ) {
            authenticateUrl += `?redirect-url=${previousURL}`;
        }

        /**
         * grant_type=password username, password
         */
        // Get the access token object.
        const githubAuth = new ClientOAuth2({
            accessTokenUri: authenticateUrl,
            // @ts-expect-error
            authorizationUri: null,
            authorizationGrants: ['credentials'],
            grant_type: 'password',
            // @ts-expect-error
            redirectUri: null,
            scopes: [],
            body: {
                // @ts-expect-error
                setCookies: true,
            },
            state: 'randomstring',
        });

        githubAuth.owner
            // @ts-expect-error
            .getToken(userName, pass, options)
            .then((user: any) => {
                if (user) {
                    browserStorage.removeItem('vice');

                    if (user.data) {
                        browserStorage.setItem(
                            'auth.passwordExpirationStatus',
                            user.data.password_expiration_status,
                        );
                        browserStorage.removeItem('auth.expiredPasswordUsername');
                        browserStorage.removeItem('auth.expiredPasswordValidations');

                        localStorage.setItem('cookieExpiration', user.data.expire);
                        LoggedUserActions.setSessionTimeout();
                    }

                    LoggedUserActions.fetchSettings({
                        loggedUserActions: LoggedUserActions,
                    })
                        .then(async (fetchedUser: any) => {
                            await LoggedUserActions.fetchColors();

                            if (useSessionStorage) {
                                localStorage.setItem('auth.userId', fetchedUser.id);
                            }
                            sessionStorage.setItem('auth.userId', fetchedUser.id);

                            // redirect after login
                            if (
                                previousURL !== null &&
                                previousURL !== '/login' &&
                                previousURL !== '/authenticate' &&
                                previousURL !== '/set-expired-password'
                            ) {
                                AlertsActions.clearList();
                                router.push(previousURL);
                            } else {
                                // normal login
                                AlertsActions.clearList();
                                router.push('/');
                            }
                        })
                        .catch((err: any) => {
                            setIsLoging(false);

                            if (get(err, 'body.error.codeName') === 'INVALID_HASH') {
                                AlertsActions.addAlert({
                                    type: 'warning',
                                    message: t('alrInvLogginHash'),
                                });
                            } else if (get(err, 'error.codeName') === 'IN_MAINTENANCE') {
                                /* AlertsActions.addAlert({
                                      type: 'warning',
                                      message: t('alrMaintenanceMsg'),
                                      show: true,
                                      allowCountdown: true,
                                  }); */
                            } else {
                                AlertsActions.addAlert({
                                    type: 'alert',
                                    message: t('alrUserDataLoadFailed'),
                                    serverError: err,
                                });
                            }
                        });
                }
            })
            .catch((err: any) => {
                setIsLoging(false);

                if (err.body) {
                    const codeName = get(err, 'body.error.codeName');
                    if (codeName === 'USER_IS_INACTIVE') {
                        // user is locked
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: t('alrUserIsNotActive'),
                            serverError: err,
                            show: true,
                            allowCountdown: true,
                        });
                    } else if (
                        codeName === 'UNAUTHORIZED' ||
                        codeName === 'BAD_LOGIN' ||
                        codeName === 'USER_NOT_FOUND'
                    ) {
                        if (
                            typeof err.body.error.redirect !== 'undefined' &&
                            err.body.error.redirect !== null
                        ) {
                            if (
                                previousURL !== null &&
                                previousURL !== '/login' &&
                                previousURL !== '/authenticate'
                            ) {
                                window.location.href = err.body.error.redirect;
                            } else {
                                const redirect = err.body.error.redirect.replace(
                                    '{backend}',
                                    getBackendUrl(),
                                );
                                window.location = redirect;
                            }
                        } else {
                            AlertsActions.addAlert({
                                type: 'warning',
                                message: t('alrBadLogin'),
                                show: true,
                                allowCountdown: true,
                            });
                        }
                        // password expired - redirect to change password
                    } else if (err.body.error?.meta?.password_expiration_status === 'E') {
                        browserStorage.setItem('auth.expiredPasswordUsername', userName);
                        browserStorage.setItem(
                            'auth.expiredPasswordValidations',
                            JSON.stringify(err.body.error.meta.validations),
                        );

                        AlertsActions.clearList();
                        router.push('/set-expired-password');
                    } else {
                        AlertsActions.addAlert({
                            type: 'alert',
                            message: t('loginError'),
                            serverError: err,
                        });
                    }
                } else {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: t('loginError'),
                        serverError: err,
                    });
                }
            });
    }

    return {
        onClickLogin,
        isLoging,
    };
};
