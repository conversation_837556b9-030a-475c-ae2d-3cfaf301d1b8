import React from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
// @mui
import { LoadingButton } from '@mui/lab';
import { InputAdornment } from '@mui/material';
import Grid from '@mui/material/Grid2';
// components
import TextField from '../../../../form/textField/TextField';
import IconButton from '../../../../IconButton';
import FormProvider from '../../../../form/FormProvider';
// hooks
import { useBoolean } from '../../../../hooks/use-boolean';
import { useLogin } from '../use-login';
// flux
import AlertsActions from '../../../../../components/alerts/alerts.actions';

type FormValuesProps = {
    authId: number | null;
    userName: string;
    password: string;
};

type FormLoginProps = {
    authConfig: any;
    isLoginButton: boolean;
};
const FormLogin = ({ isLoginButton, authConfig }: FormLoginProps) => {
    const { t } = useTranslation();
    const showPassword = useBoolean(false);

    const { onClickLogin, isLoging } = useLogin(authConfig);

    const defaultValues = {
        authId: null,
        userName: '',
        password: '',
    };

    const methods = useForm<FormValuesProps>({
        defaultValues,
    });

    const {
        handleSubmit,
        formState: { isSubmitting },
    } = methods;

    const onSubmit = (data: FormValuesProps) => {
        if (data.userName === '' || data.password === '') {
            AlertsActions.addAlert({
                type: 'warning',
                message: t('alrFillNameAndPass'),
                show: true,
                allowCountdown: true,
            });
            return;
        }
        onClickLogin(data.authId, data.userName, data.password);
    };

    return (
        <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={1}>
                <Grid size={12}>
                    <TextField
                        type="text"
                        label={t('userName')}
                        name="userName"
                        fullWidth
                    />
                </Grid>
                <Grid size={12}>
                    <TextField
                        label={t('password')}
                        name="password"
                        type={showPassword.value ? 'text' : 'password'}
                        fullWidth
                        endAdornment={
                            <InputAdornment position="end">
                                <IconButton
                                    size="medium"
                                    color="secondary"
                                    icon={showPassword.value ? 'hide' : 'view'}
                                    aria-label="toggle password visibility"
                                    onClick={showPassword.onToggle}
                                />
                            </InputAdornment>
                        }
                    />
                </Grid>

                {isLoginButton && (
                    <Grid size={12}>
                        <LoadingButton
                            loading={isLoging || isSubmitting}
                            type="submit"
                            color="primary"
                            fullWidth
                            size="large"
                            variant="contained"
                            sx={{
                                '& .MuiCircularProgress-root': {
                                    color: '#fff',
                                },
                            }}
                        >
                            {t('logIn')}
                        </LoadingButton>
                    </Grid>
                )}
            </Grid>
        </FormProvider>
    );
};

export default FormLogin;
