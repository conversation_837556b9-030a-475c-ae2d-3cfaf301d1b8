import React from 'react';
// @mui
import { Grid } from '@mui/material';
// compoenents
import Button from '../../../../Button';
import Label from '../../../../label/Label';
// hooks
import { useLogin } from '../use-login';

type FormAuthenticateProps = {
    authorities: any[];
    authConfig: any;
};

const FormAuthenticate = ({
    authorities,
    authConfig,
}: FormAuthenticateProps) => {
    const { onClickLogin, isLoging } = useLogin(authConfig);
    return (
        <Grid container spacing={2}>
            {authorities?.map((auth: any) => {
                const meta = JSON.parse(auth.auth_meta || '{}');
                return (
                    <Grid item xs={12} key={auth.auth_name}>
                        <Button
                            sx={{
                                backgroundColor: meta.backgroundColor,
                                color: 'common.white',
                                '&:hover': {
                                    backgroundColor: meta.backgroundColor,
                                    filter: 'brightness(0.85)',
                                },
                            }}
                            disabled={isLoging}
                            type="submit"
                            label={auth.auth_name}
                            size="large"
                            variant="contained"
                            fullWidth
                            onClick={() => {
                                onClickLogin(auth.auth_id);
                            }}
                        />
                    </Grid>
                );
            })}
        </Grid>
    );
};

export default FormAuthenticate;
