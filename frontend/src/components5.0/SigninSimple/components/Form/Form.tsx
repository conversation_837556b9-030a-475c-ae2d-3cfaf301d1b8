import React, { useState, useEffect } from 'react';
import { isEmpty, keys } from 'lodash';
import path from 'path';
// @mui
import {
    Box, Link as MuiLink, Typography, Stack,
} from '@mui/material';
import Grid from '@mui/material/Grid2';
// hooks
import { useTranslation } from 'react-i18next';
import QRCode from 'react-qr-code';
import { usePathname } from '../../../routes/hooks';
import { useResponsive } from '../../../hooks';
// components
import Link from '../../../routes/components/RouterLink';
import FormLogin from './components/FormLogin';
import FormAuthenticate from './components/FormAuthenticate';
import Label from '../../../label/Label';
import DefaultLogo from '../../../logo/components/DefaultLogo';
import CustomLogo from '../../../logo/components/CustomLogo';
// utils
import { getParameterByName, checkLogo } from '../../../../common/utils';
import CrossPlatform from '../../../../common/CrossPlatform';
// img
import { SignIn } from '../../../illustrations/Illustrations';

const Form = ({ authConfig, showNews, mobileAppActive }: any): JSX.Element => {
    const [logoName, setLogoName] = useState<string | null>(null);
    const [timestamp] = useState(new Date().getTime());
    const { t } = useTranslation();
    const pathName = usePathname();
    const [isLoginButton, setIsLoginButton] = useState(true);

    const smUp = useResponsive('up', 'sm');
    const lgUp = useResponsive('up', 'lg');

    let authorities = authConfig?.filter((auth: any) => {
        const meta = JSON.parse(auth.auth_meta || '{}');
        return (
            // eslint-disable-next-line eqeqeq
            auth.auth_module != 'Password' &&
            (meta.visibility === 'Y' || !Object.prototype.hasOwnProperty.call(meta, 'visibility'))
        );
    });

    if (window.location.search) {
        // /authenticate?auth_name="OAuth2"
        let moduleName = getParameterByName('auth_name', window.location.search);
        if (moduleName) {
            setIsLoginButton(false);
            try {
                moduleName = JSON.parse(moduleName);
            } catch (e) {
                // moduleName = moduleName;
            }
            authorities = authorities?.filter((auth: any) => {
                return auth.auth_name === moduleName;
            });
        }
    }

    useEffect(() => {
        const checkLogoFunc = async () => {
            const logo = await checkLogo();

            if (!isEmpty(logo)) {
                // { logoLoginDefaultTheme: true }
                const logoNameKey = keys(logo)[0];

                setLogoName(logo[logoNameKey] === true ? `${logoNameKey}.png` : null);
            }
        };
        checkLogoFunc();
    }, []);

    return (
        <Box pb={{ sm: 5, lg: 0 }}>
            <Grid container spacing={4}>
                <Grid size={12}>
                    <SignIn
                        sx={{
                            width: '10rem',
                            height: '10rem',
                            mx: 'auto',
                        }}
                    />
                </Grid>
                <Grid size={12}>
                    <Box height={!logoName ? '2rem' : '5rem'} mx="auto" width="12.5rem">
                        {!logoName && <DefaultLogo size="large" />}

                        {logoName && (
                            <CustomLogo
                                src={`${path.resolve(`/public/schema-logos/${logoName}?${timestamp}`)}`}
                                alt="Login logo"
                                sx={{
                                    overflow: 'hidden',
                                }}
                            />
                        )}
                    </Box>
                </Grid>
                <Grid size={12}>
                    {pathName === '/login' && (
                        <FormLogin isLoginButton={isLoginButton} authConfig={authConfig} />
                    )}
                    {pathName === '/authenticate' &&
                        (authorities.length !== 0 ? (
                            <FormAuthenticate authorities={authorities} authConfig={authConfig} />
                        ) : (
                            <Label color="info" sx={{ width: '100%' }}>
                                {t('noAuthorities')}
                            </Label>
                        ))}
                </Grid>
                {authorities.length !== 0 && (
                    <Grid size={12}>
                        <Box display="flex" alignItems="center">
                            <Box
                                sx={{
                                    borderBottom: '1px solid #D9D9D9',
                                    flexGrow: 1,
                                    margin: '0 1rem',
                                }}
                            />
                            <Typography variant="body1">{t('or')}</Typography>
                            <Box
                                sx={{
                                    borderBottom: '1px solid #D9D9D9',
                                    flexGrow: 1,
                                    margin: '0 1rem',
                                }}
                            />
                        </Box>
                    </Grid>
                )}
                <Grid size={12}>
                    {pathName === '/authenticate' && (
                        <Stack width={1} justifyContent="center">
                            <Typography
                                component={Link}
                                href="/login"
                                align="center"
                                gutterBottom
                                sx={{
                                    textDecoration: 'underline',
                                }}
                            >
                                {t('loginWithUsernamePassword')}
                            </Typography>
                        </Stack>
                    )}
                    {pathName === '/login' && authorities.length !== 0 && (
                        <Stack width={1} justifyContent="center">
                            <Typography
                                component={Link}
                                href="/authenticate"
                                align="center"
                                gutterBottom
                                sx={{
                                    textDecoration: 'underline',
                                }}
                            >
                                {t('signInWithCorporateIdentity')}
                            </Typography>
                        </Stack>
                    )}
                </Grid>
                {mobileAppActive && !CrossPlatform.isNativeMobileApp() && (
                    <Grid size={12}>
                        <Stack
                            direction="column"
                            gap={1.5}
                            alignItems="center"
                            justifyContent="center"
                            sx={{
                                mt: '1rem',
                            }}
                        >
                            {lgUp && (
                                <QRCode
                                    value={`${window.location.origin}/app-store-redirect`}
                                    size={74}
                                />
                            )}
                            <Typography variant="body1" align="center">
                                {t(smUp ? 'getMobileAppTextQr' : 'getMobileAppText')}
                            </Typography>
                            <Stack direction="row" gap={1.5}>
                                <MuiLink
                                    href="https://apps.apple.com/us/app/team-assistant/id6503063799"
                                    target="_blank"
                                    rel="noopener"
                                    underline="none"
                                >
                                    <img
                                        src="/assets/images/appstore.png"
                                        alt="App Store"
                                        style={{
                                            width: '8.75rem',
                                            height: '2.625rem',
                                            maxWidth: '8.75rem',
                                        }}
                                    />
                                </MuiLink>
                                <MuiLink
                                    href="https://play.google.com/store/apps/details?id=app.teamassistant.android"
                                    target="_blank"
                                    rel="noopener"
                                    underline="none"
                                >
                                    <img
                                        src="/assets/images/googleplay.png"
                                        alt="Google Play"
                                        style={{
                                            width: '8.9375rem',
                                            height: '2.625rem',
                                            maxWidth: '8.9375rem',
                                        }}
                                    />
                                </MuiLink>
                            </Stack>
                        </Stack>
                    </Grid>
                )}
                {(!showNews || !smUp) && (
                    <Grid size={12}>
                        <Stack width={1} justifyContent="center">
                            <Typography
                                width={1}
                                component="a"
                                href="https://www.syca.app/news"
                                target="_blank"
                                align="center"
                                sx={{
                                    textDecoration: 'underline',
                                }}
                            >
                                {t('whatsNewInTAS')}
                            </Typography>
                        </Stack>
                    </Grid>
                )}
            </Grid>
        </Box>
    );
};
export default Form;
