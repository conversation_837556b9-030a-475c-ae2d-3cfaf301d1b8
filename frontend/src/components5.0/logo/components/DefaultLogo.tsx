import React from 'react';
import {
    TeamAssistantLargeLogo,
    TeamAssistantTinyLogo,
    SycaLargeLogo,
    SycaTinyLogo,
} from '../../illustrations/Illustrations';

type DefaultLogoProps = {
    size: 'large' | 'small';
};
const DefaultLogo = ({ size }: DefaultLogoProps) => {
    const isTeamAssistantTheme = (window.config as any).teamassistantTheme;

    if (size === 'small') {
        const Logo = isTeamAssistantTheme ? SycaTinyLogo : TeamAssistantTinyLogo;
        return (
            <Logo
                sx={{
                    width: '100%',
                    height: '100%',
                    mx: 'auto',
                }}
            />
        );
    }
    const Logo = isTeamAssistantTheme ? TeamAssistantLargeLogo : SycaLargeLogo;
    return (
        <Logo
            sx={{
                display: 'block',
                height: '100%',
                cursor: 'pointer',
                objectFit: 'contain',
            }}
        />
    );
};

export default DefaultLogo;
