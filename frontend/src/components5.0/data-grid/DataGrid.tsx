import React, { useRef, useState, useEffect, forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { startsWith } from 'lodash';
// @mui
import { unstable_debounce as debounce } from '@mui/utils';
import { Box, CircularProgress } from '@mui/material';
import {
    GridFilterModel,
    GridSortModel,
    DataGridPro as MuiDataGridPro,
    useGridApiRef,
    GridLogicOperator,
    GridLocaleText,
    GridRowScrollEndParams,
    MuiEvent,
    GridCallbackDetails,
    GridRowSelectionModel,
} from '@mui/x-data-grid-pro';
import { enUS, csCZ, deDE, frFR, itIT, plPL, roRO, skSK, hrHR } from '@mui/x-data-grid-pro/locales';
// hooks
import useDataGrid from './use-dataGrid';
import { useRouter } from '../routes/hooks/use-router';
import { useStore } from '../zustand/boundStore';
import { useResponsive } from '../hooks/use-responsive';
// components
import modifyColumns from './components/DataGridHeaderInputFilters';
import CustomFilterPanel from './components/DataGridCustomFilters/DataGridCustomFilterPanel';
import DataGridFooterSlot from './components/DataGridFooter/DataGridFooterSlot';
// types
import { DataGridProps } from './types';
// utils
import { generateRowId as genId } from '../utils/utils';

const LoggedUserStore = require('../../flux/loggedUser.store');

declare module '@mui/x-data-grid-pro' {
    interface FooterPropsOverrides {
        gridId: string;
        url: string;
        defaultFilter?: string;
    }
    interface FilterPanelPropsOverrides {
        gridId: string;
    }
}

const DataGrid = forwardRef<any, DataGridProps>(
    (
        {
            id,
            apiUrl,
            columns,
            defaultSortModel = [],
            defaultFilterModel = {
                items: [],
                logicOperator: GridLogicOperator.And,
            },
            defaultFilter,
            handleRowDoubleClick = () => undefined,
            onRowClick = () => undefined,
            toolbar,
            disableRowSelectionOnClick = false,
            inModal = false,
            checkboxSelection = false,
            headerFilters = true,
            apiCallback,
            totalCount = false,
            post,
            generateRowId = false,
            alwaysSort = undefined,
            openInNewTabUrl = null,
            loading = false,
            disableMultipleRowSelection = true,
            isEmpty = false,
            hideFooter = false,
            showId = false,
            rowCountFetch = null,
            handleKeyDown,
        }: DataGridProps,
        ref,
    ) => {
        const dataGridRef = useRef(null);
        const [allFetched, setAllFetched] = useState(false);

        const [deftSortModel] = useState<GridSortModel>(() => [...defaultSortModel]);

        const [defFilterModel] = useState<GridFilterModel>(() => defaultFilterModel);

        const [rowCount, setRowCount] = useState(1);
        const fetchMore = useRef(false);

        const localRowSelectionUpdatedRef = useRef(false);

        const changedRows = useStore((state) => state.changedRows);
        const dataGridRowDensity = useStore((state) => state.dataGridRowDensity);
        const dataGridColDimensions = useStore((state) => state.dataGridColDimensions);
        const dataGridFetchColDimensions = useStore((state) => state.dataGridFetchColDimensions);
        const dataGridRows = useStore((state) => state.dataGridRows);
        const dataGridsSort = useStore((state) => state.dataGridsSort);
        const dataGridsFilter = useStore((state) => state.dataGridsFilter);
        const columnsLoading = useStore((state) => state.columnsLoading);
        const dataGridSelectedRows = useStore((state) => state.dataGridSelectedRows);

        const setDataGridRows = useStore((state) => state.setDataGridRows);
        const setDataGridFetchColDimensions = useStore(
            (state) => state.setDataGridFetchColDimensions,
        );
        const setDataGridColDimensions = useStore((state) => state.setDataGridColDimensions);
        const setDataGridSort = useStore((state) => state.setDataGridSort);
        const setDataGridFilter = useStore((state) => state.setDataGridFilter);
        const setDataGridSelectedRows = useStore((state) => state.setDataGridSelectedRows);
        const setDataGridTotalRowsCount = useStore((state) => state.setDataGridTotalRowsCount);
        const smUp = useResponsive('up', 'sm');
        // Get a reference to the data grid API
        let apiRef: any;
        if (!ref) {
            apiRef = useGridApiRef();
        } else {
            apiRef = ref;
        }

        const { t } = useTranslation();

        const { rowsLoading, lazyFetchRows } = useDataGrid(apiRef);

        // get filter from url params
        const { location } = useRouter();
        // Extract the search part of the URL
        const searchParams = new URLSearchParams(location.search);
        // Get the value of the "filter" parameter
        const filterValueFromUrl: string | null = searchParams.get('filter');

        const filter = dataGridsFilter[id]?.value?.items?.length
            ? dataGridsFilter[id].value
            : defFilterModel;
        const sort = dataGridsSort[id]?.value?.length ? dataGridsSort[id].value : deftSortModel;

        const dataGridLoading: boolean = isEmpty ? false : loading || rowsLoading;
        const dataGridColumns = modifyColumns(columns, deftSortModel);
        const isAutosizeOnMount = !dataGridColDimensions[id] && !dataGridFetchColDimensions[id];
        const isCircularProgress = columnsLoading || !dataGridRows[id];

        // logic for open row in new tab when mouse wheel click, Add a listener to the data grid to handle middle mouse button clicks
        useEffect(() => {
            const grid = dataGridRef.current as HTMLElement | null;
            if (!grid) return undefined;
            const handleClick = (event: any) => {
                // Check if the middle mouse button was pressed
                if (event.button === 1 || event.button === 2) {
                    const row = event.target.closest('.MuiDataGrid-row');
                    if (row && openInNewTabUrl) {
                        // Split the rowId by '-' and take the first part because the rowId can be in the format `rowId-generateRowId`
                        const rowId = startsWith(row.dataset.id, '-')
                            ? `-${row.dataset.id.split('-')[1]}`
                            : row.dataset.id.split('-')[0];
                        // Prevent the default middle mouse button behavior (usually scrolling)
                        event.preventDefault();
                        // Implement your logic here, e.g., navigate to a detail view
                        window.open(`${openInNewTabUrl}${rowId}`, '_blank');
                    }
                }
            };
            grid.addEventListener('mousedown', handleClick);

            return () => {
                grid.removeEventListener('mousedown', handleClick);
            };
        }, []);

        // set view value to state data grid id when leave datagrid and id
        useEffect(() => {
            setDataGridSelectedRows([]);
            localRowSelectionUpdatedRef.current = true;
        }, [apiUrl]);

        // set selected rows when dataGridSelectedRows changed externally
        useEffect(() => {
            if (!localRowSelectionUpdatedRef.current) {
                apiRef.current?.setRowSelectionModel?.(dataGridSelectedRows);
            }

            localRowSelectionUpdatedRef.current = false;
        }, [dataGridSelectedRows]);

        // logic for navigate in dataGrid by key arrow up or down and open row by enter key
        const handleCellKeyDown = (params: any, event: any) => {
            if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
                event.preventDefault();
                // Find the current row index from the DataGrid's sorted row models
                const currentRowIndex = apiRef.current.getSortedRowIds().indexOf(params.id);
                const nextRowIndex =
                    event.key === 'ArrowDown' ? currentRowIndex + 1 : currentRowIndex - 1;

                if (nextRowIndex >= 0 && nextRowIndex < apiRef.current.getSortedRowIds().length) {
                    const nextRowId = apiRef.current.getSortedRowIds()[nextRowIndex];
                    apiRef.current.selectRow(nextRowId, true, true);
                }
            } else if (event.key === 'Enter') {
                handleRowDoubleClick(params.row);
            }

            if (handleKeyDown) {
                handleKeyDown(params.row, event);
            }
        };

        // first fetch rows
        useEffect(() => {
            let ignore = false;
            let rowCountForFetch: number | null = rowCountFetch;
            // dynamic row count for first fetch depend on grid height
            if (dataGridRef.current && !rowCountFetch) {
                const gridHeight = (dataGridRef.current as any).getBoundingClientRect().height;
                // take number depend on variant row density
                let rowHeight: number;
                if (dataGridRowDensity === 'compact') {
                    rowHeight = 26;
                } else if (dataGridRowDensity === 'standard') {
                    rowHeight = 38;
                } else {
                    rowHeight = 49;
                }
                rowCountForFetch = Math.ceil(gridHeight / rowHeight);
            }

            // for documents all rows fetch
            setAllFetched(false);
            const fetch = async () => {
                // setDataGridRows(id,[]);
                // setLastRowToRender(20);
                // setDataGridTotalRowCount(0);
                const { items } = await lazyFetchRows(
                    apiUrl,
                    sort,
                    filter,
                    defaultFilter,
                    0,
                    rowCountForFetch || undefined,
                    false /* totalCount, */,
                    deftSortModel,
                    post,
                    id,
                    alwaysSort,
                    apiCallback,
                    undefined,
                    filterValueFromUrl ?? undefined,
                );
                if (items === null || items === undefined || ignore) {
                    return;
                }
                setDataGridRows(id, items);
                setRowCount(items.length);
                if (items.length !== rowCountForFetch) {
                    fetchMore.current = false;
                    setDataGridTotalRowsCount(id, items.length);
                } else {
                    fetchMore.current = true;
                }
            };
            if (apiUrl !== '') {
                fetch();
            }
            return () => {
                ignore = true;
            };
        }, [apiUrl, dataGridsSort[id], dataGridsFilter[id], changedRows, defaultFilter]);

        // Fetch rows as they on the bottom of the viewport
        const handleFetchRowsBottomViewPort = async () => {
            if (fetchMore.current) {
                const rowsFetch = rowCountFetch || 20;
                const { items } = await lazyFetchRows(
                    apiUrl,
                    sort,
                    filter,
                    defaultFilter,
                    rowCount,
                    rowsFetch,
                    false,
                    deftSortModel,
                    post,
                    id,
                    alwaysSort,
                    apiCallback,
                );
                if (items === null || items === undefined) {
                    return;
                }
                setDataGridRows(id, dataGridRows[id].concat(items));
                if (items.length === rowsFetch) {
                    setRowCount((prevRowsCount) => prevRowsCount + rowsFetch);
                } else {
                    setDataGridTotalRowsCount(id, dataGridRows[id].concat(items).length);
                    setRowCount(dataGridRows[id].concat(items).length);
                    fetchMore.current = false;
                }
            }
        };

        // fetch all rows for documents all max length 1000 rows
        const fetchAllRows = async () => {
            const { items } = await lazyFetchRows(
                apiUrl,
                sort,
                filter,
                defaultFilter,
                0,
                1000,
                false,
                deftSortModel,
                post,
                id,
                alwaysSort,
                apiCallback,
            );

            setAllFetched(true);

            setDataGridRows(id, items);

            setRowCount(items.length);

            return items;
        };

        // logic for select all rows in document dataGrid
        const handleRowSelection = async (rowIds: GridRowSelectionModel) => {
            if (
                !allFetched &&
                rowIds.length > 0 &&
                rowIds.length === rowCount &&
                rowIds.length !== 1
            ) {
                const fetchedRows = await fetchAllRows();
                const fetchedRowIds = fetchedRows.map((row: any) => row.id);
                setDataGridSelectedRows(fetchedRowIds);
                apiRef.current?.setRowSelectionModel?.(fetchedRowIds);
            } else {
                setDataGridSelectedRows(rowIds);
                apiRef.current?.setRowSelectionModel?.(rowIds);
            }

            localRowSelectionUpdatedRef.current = true;
        };

        const handleColumnWidthChange = debounce(() => {
            setDataGridFetchColDimensions({
                [id]: apiRef.current.exportState()?.columns?.dimensions || undefined,
            });
        }, 500);

        const handleColumnResize = debounce(() => {
            setDataGridColDimensions({
                [id]: apiRef.current.exportState()?.columns?.dimensions || undefined,
            });
        }, 500);

        const getLocaleText = () => {
            const userLang = LoggedUserStore.getState().userLanguage;

            // Translation key customization
            const obj = {
                headerFilterOperatorDoesNotContain: t('notContains'),
                headerFilterOperatorDoesNotEqual: t('notEquals'),
                headerFilterOperatorIsAnyOfValue: t('isAnyOfValue'),
                headerFilterOperatorFromTo: t('fromTo'),
                filterOperatorFromTo: t('fromto'),
                filterOperatorDoesNotContain: t('notcontains'),
                filterOperatorDoesNotEqual: t('notequals'),
                filterOperatorIsAnyOfValue: t('isanyofvalue'),

                // Uncomment or add more custom keys as needed
                // filterOperatorDoesNotContain: t('notContains'),
            };

            // Map of available language codes to their respective locale data
            const locales: { [key: string]: Partial<GridLocaleText> } = {
                cs: csCZ.components.MuiDataGrid.defaultProps.localeText,
                en: enUS.components.MuiDataGrid.defaultProps.localeText,
                de: deDE.components.MuiDataGrid.defaultProps.localeText,
                fr: frFR.components.MuiDataGrid.defaultProps.localeText,
                it: itIT.components.MuiDataGrid.defaultProps.localeText,
                pl: plPL.components.MuiDataGrid.defaultProps.localeText,
                ro: roRO.components.MuiDataGrid.defaultProps.localeText,
                sk: skSK.components.MuiDataGrid.defaultProps.localeText,
                hr: hrHR.components.MuiDataGrid.defaultProps.localeText,
                ru: enUS.components.MuiDataGrid.defaultProps.localeText,
                sr: enUS.components.MuiDataGrid.defaultProps.localeText,
            };

            // Retrieve the correct locale based on user language or return undefined if not found
            const defaultLocale = locales[userLang];

            // Apply custom translations to the fetched locale data if available
            return defaultLocale ? { ...defaultLocale, ...obj } : undefined;
        };

        // inModal ? (smUp ? '1rem' : '0.5rem') : '0rem'
        let mainMargin = '0rem';
        if (inModal && !smUp) {
            mainMargin = '0.5rem';
        } else if (inModal && smUp) {
            mainMargin = '1rem';
        }

        let footerPadding = '0rem';
        if (inModal) {
            footerPadding = '0.5rem 0.5rem';
        }

        return (
            <Box
                id={id}
                width={1}
                height={1}
                position="relative"
                ref={dataGridRef}
                sx={{
                    '& .MuiDataGrid-root': {
                        top: inModal ? '0rem' : '-0.7rem',
                        height: inModal ? '100%' : 'calc(100% + 0.7rem)',
                        '--DataGrid-headersTotalHeight': headerFilters
                            ? '76px !important'
                            : '38px !important',
                        '--DataGrid-topContainerHeight': headerFilters
                            ? '76px !important'
                            : '38px !important',
                    },
                    '& .MuiDataGrid-main': {
                        marginX: mainMargin,
                    },
                    '& .DataGridFooter': {
                        padding: footerPadding,
                    },
                }}
            >
                {isCircularProgress ? (
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            height: '100%',
                            width: '100%',
                        }}
                    >
                        <CircularProgress color="primary" />
                    </Box>
                ) : (
                    <MuiDataGridPro
                        rows={dataGridRows[id] || []}
                        apiRef={apiRef}
                        rowCount={rowCount}
                        columns={dataGridColumns}
                        // scrollEndThreshold={1500}
                        onRowsScrollEnd={handleFetchRowsBottomViewPort}
                        loading={dataGridLoading}
                        density={dataGridRowDensity}
                        paginationMode="server"
                        onColumnWidthChange={handleColumnWidthChange}
                        autosizeOnMount={isAutosizeOnMount}
                        autosizeOptions={{
                            includeOutliers: true,
                            includeHeaders: false,
                            outliersFactor: 1.5,
                            expand: true,
                        }}
                        onColumnResize={handleColumnResize}
                        className="scrollable-container"
                        initialState={{
                            pinnedColumns: { right: ['actions'] },
                            columns: {
                                columnVisibilityModel: {
                                    id: showId,
                                },
                                dimensions:
                                    dataGridColDimensions[id] || dataGridFetchColDimensions[id],
                            },
                            sorting: {
                                sortModel: [...deftSortModel],
                            },
                        }}
                        slots={{
                            toolbar: () => toolbar,
                            footer: DataGridFooterSlot,
                            filterPanel: CustomFilterPanel,
                        }}
                        slotProps={{
                            filterPanel: {
                                gridId: id,
                            },
                            footer: {
                                gridId: id,
                                url: !isEmpty ? apiUrl : undefined,
                                defaultFilter: defaultFilter,
                            },
                        }}
                        localeText={getLocaleText()}
                        onRowDoubleClick={(params) => handleRowDoubleClick(params.row)}
                        onRowClick={(params, event) => onRowClick(params.row, event)}
                        headerFilters={headerFilters}
                        filterModel={filter}
                        sortModel={sort}
                        onFilterModelChange={(params, details) => {
                            details?.api?.scroll({ top: 0 });
                            setDataGridFilter(id, params);
                        }}
                        onSortModelChange={(params, details) => {
                            details?.api?.scroll({ top: 0 });
                            setDataGridSort(id, params);
                        }}
                        disableColumnReorder
                        // columnVisibilityModel={{
                        //     // Hide columns status and traderName, the other columns will remain visible
                        //     id: false,
                        // }}
                        disableMultipleRowSelection={disableMultipleRowSelection}
                        getRowId={(row: any) => (generateRowId ? genId(row.id) : row.id)}
                        disableRowSelectionOnClick={disableRowSelectionOnClick}
                        checkboxSelection={checkboxSelection}
                        onRowSelectionModelChange={handleRowSelection}
                        keepNonExistentRowsSelected
                        onCellKeyDown={handleCellKeyDown}
                        hideFooter={hideFooter}
                    />
                )}
            </Box>
        );
    },
);

export default DataGrid;
