// hooks
import { useTranslation } from 'react-i18next';
// utils
import {
    checkLangMutation,
    checkLangMutationDiffNames,
    checkLangMutationTemplInst,
    momentFormatDate,
} from '../../../../common/utils';

const useDataGridCards = (
    translateColumns?: {
        field: string;
        by: string;
        func: 'checkLangMutationTemplInst' | 'checkLangMutation' | 'checkLangMutationDiffNames';
    }[],
) => {
    const { t } = useTranslation();

    const getLangMutationFunction = (func: string) => {
        switch (func) {
            case 'checkLangMutationTemplInst':
                return checkLangMutationTemplInst;
            case 'checkLangMutation':
                return checkLangMutation;
            case 'checkLangMutationDiffNames':
                return checkLangMutationDiffNames;
            default:
                return checkLangMutation;
        }
    };

    // if translateColumns.field contains row[column.field] then return checkLangMutationTemplInst function
    const handleCheckLangMut = (row: any, columnField: string) => {
        const translateColumn = translateColumns?.find((column) => column.field === columnField);
        if (translateColumn) {
            const langMutationFunction = getLangMutationFunction(translateColumn.func);
            return langMutationFunction(row, translateColumn.by);
        }
        return row[columnField];
    };

    // Get the value of a row for a given column
    const getRowValue = (column: any, row: any) => {
        let value = row[column.field];
        if (column.valueGetter) {
            value = (column.valueGetter as any)(row[column.field], row, column, null);
        }

        let formattedValue = value;
        if (column.valueFormatter) {
            formattedValue = (column.valueFormatter as any)(value, row, column, null);
        }

        let renderCellOutput = null;
        if (column.renderCell) {
            try {
                renderCellOutput = column.renderCell({
                    id: row.id,
                    field: column.field,
                    value,
                    formattedValue,
                    row,
                    colDef: column as any,
                    hasFocus: false,
                    cellMode: 'view',
                    isEditable: false,
                    tabIndex: 0,
                } as Parameters<typeof column.renderCell>[0]);
            } catch (e) {
                /* ignore */
            }
        }

        if (column.valueGetter || column.valueFormatter || renderCellOutput) {
            return renderCellOutput || formattedValue || value;
        }

        switch (column.type) {
            case 'boolean':
                return row[column.field] ? 'Ano' : 'Ne';
            case 'date':
            case 'dateTime': {
                const date: Date = row[column.field];
                return date ? momentFormatDate(date, false, true) : '';
            }
            case 'select': {
                return row[column.field] === 'A' ? t('active') : t('finished');
            }
            default: {
                const data = handleCheckLangMut(row, column.field);
                return data; /* checkLangMutationTemplInst(data, column.field); */
            }
        }
    };

    return {
        getRowValue,
    };
};

export default useDataGridCards;
