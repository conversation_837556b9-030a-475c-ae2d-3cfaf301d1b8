import { StateCreator } from 'zustand';
import { GridDensity, GridColumnsInitialState } from '@mui/x-data-grid-pro';
import { SeenBellNewsInfoType, SeenPriorityNewsInfoType, SeenTaskNewsInfoType } from '../sections/administration/news-management/types';

type DataGridColDimensions = Record<string, Record<string, number>>;

type State = {
    dataGridRowDensity: GridDensity;
    darkMode: boolean;
    isNavMini: boolean;
    dataGridColDimensions: DataGridColDimensions;
    seenPriorityNewsInfo: SeenPriorityNewsInfoType;
    seenBellNewsInfo: SeenBellNewsInfoType;
    seenTaskNewsInfo: SeenTaskNewsInfoType;
    seenTaskNewsInfoMaintained: boolean;
};

type Actions = {
    setDataGridRowDensity: (density: GridDensity) => void;
    toggleDarkMode: () => void;
    toggleNav: () => void;
    setDataGridColDimensions: (dataGridColDimensions: DataGridColDimensions) => void;
    setSeenPriorityNewsInfo: (info: SeenPriorityNewsInfoType) => void;
    setSeenBellNewsInfo: (info: SeenBellNewsInfoType) => void;
    setSeenTaskNewsInfo: (info: SeenTaskNewsInfoType) => void;
    setSeenTaskNewsInfoMaintained: (maintained: boolean) => void;
};

export type UseLocalStorageT = State & Actions;

export const useLocalStorageSlice: StateCreator<UseLocalStorageT, [], [], UseLocalStorageT> = (
    set,
    get,
) => ({
    darkMode: false,
    dataGridRowDensity: 'standard' as GridDensity,
    isNavMini: false,
    dataGridColDimensions: {} as DataGridColDimensions,
    seenPriorityNewsInfo: {
        minDate: null,
    },
    seenBellNewsInfo: {
        minDate: null,
        lastVisiblePostIds: [],
    },
    seenTaskNewsInfo: {},
    // do not persist seenTaskNewsInfoMaintained - it needs to be reset to false on reload
    seenTaskNewsInfoMaintained: false,

    toggleNav: () => set((state) => ({ isNavMini: !state.isNavMini })),
    setDataGridRowDensity: (dataGridRowDensity: GridDensity) => set({ dataGridRowDensity }),
    toggleDarkMode: () => set((state) => ({ darkMode: !state.darkMode })),
    setDataGridColDimensions: (dataGridColDimensions: DataGridColDimensions) =>
        set((state) => ({
            dataGridColDimensions: {
                ...state.dataGridColDimensions,
                ...dataGridColDimensions,
            },
        })),
    setSeenPriorityNewsInfo: (info: SeenPriorityNewsInfoType) => set({ seenPriorityNewsInfo: info }),
    setSeenBellNewsInfo: (info: SeenBellNewsInfoType) => set({ seenBellNewsInfo: info }),
    setSeenTaskNewsInfo: (info: SeenTaskNewsInfoType) => set({ seenTaskNewsInfo: info }),
    setSeenTaskNewsInfoMaintained: (maintained: boolean) => set({ seenTaskNewsInfoMaintained: maintained }),
});
