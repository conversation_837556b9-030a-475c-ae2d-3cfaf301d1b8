import { create } from 'zustand';
import useDataGridSlice, { UseDataGridSliceT } from './dataGridStore';
import useDocumentsSlice, { UseDocumentsSliceT } from './documentsStore';
import { useOverviewSlice, UseOverviewSliceT } from './overviewStore';
import { useTreeSlice, UseTreeSliceT } from './treeStore';
import { useLoggedUserSlice, UseLoggedUserSliceT } from './loggedUserStore';
import { useMenuSlice, UseMenuSliceT } from './menuStore';
import { useStructureSlice, UseStructureSliceT } from './structureStore';
import { useTaskStoreSlice, UseTaskStoreSliceT } from './taskStore';
import { useUserDetailSlice, UseUserDetailSliceT } from './userDetailStore';
import { useUserAccountSlice, UseUserAccountSliceT } from './userAccountStore';
import { dashboardSlice, DashboardSliceT } from './dashboardStore';
import { usePlanSlice, UsePlanSliceT } from './planStore';
import { usePersistedLocalStorageSlice, PersistedLocalStorageSliceT } from './persistedLocalStorageStore';
import { useDynTableDetailSlice, UseDynTableDetailSliceT } from './dynTableDetailStore';
import { favouritesSlice, FavouritesSliceT } from './favouritesStore';
import { variableAssignmentSlice, VariableAssignmentSliceT } from './variableAssignmentStore';

export type StoreT = UseDataGridSliceT &
    UseOverviewSliceT &
    UseDocumentsSliceT &
    UseTreeSliceT &
    UseLoggedUserSliceT &
    UseMenuSliceT &
    UseStructureSliceT &
    UseTaskStoreSliceT &
    UsePlanSliceT &
    UseUserDetailSliceT &
    UseUserAccountSliceT &
    DashboardSliceT &
    UseDynTableDetailSliceT &
    PersistedLocalStorageSliceT &
    FavouritesSliceT &
    VariableAssignmentSliceT;

export const useStore = create<StoreT>()((...a) => ({
    ...useDataGridSlice(...a),
    ...useOverviewSlice(...a),
    ...useDocumentsSlice(...a),
    ...useTreeSlice(...a), // Update the type of folders to be an array
    ...useLoggedUserSlice(...a),
    ...useMenuSlice(...a),
    ...useStructureSlice(...a),
    ...useTaskStoreSlice(...a),
    ...useUserDetailSlice(...a),
    ...useUserAccountSlice(...a),
    ...dashboardSlice(...a),
    ...usePlanSlice(...a),
    ...useDynTableDetailSlice(...a),
    ...usePersistedLocalStorageSlice(...a),
    ...favouritesSlice(...a),
    ...variableAssignmentSlice(...a),
}));
