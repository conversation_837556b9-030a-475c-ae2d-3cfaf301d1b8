import { Controller, useFormContext } from 'react-hook-form';
import React, {
    forwardRef, useEffect, useId, useState, useRef,
} from 'react';
import { useTranslation } from 'react-i18next';
import { defineScript } from '@jrblatt/light-script';
import ApiRequest from '../../../api/apiRequest';
// components
import FormControl from '../FormControl';
import ModalInsertSelected from './ModalInsertSelected';
import { useFormFunctions } from '../../hooks/form/use-form-functions';
// utils
import { downloadEncoded, guid } from '../../../common/utils';
// types
import { CkEditorProps } from './types';
// flux
import AlertsActions from '../../../components/alerts/alerts.actions';
import LoggedUserStore from '../../../flux/loggedUser.store';

const CkEditor = forwardRef(
    (
        {
            name,
            value,
            advancedEditor,
            mainButtonsInMaximizedEditor,
            height,
            taskHeight,
            error,
            onComponentBlur,
            onFocus,
            validations = {},
            drWatchVariables,
            tvarName,
            required,
            helperText,
            readOnly,
            disabled,
            templateId,
            fullWidth = true,
            tvarId, // for task sections
            axisX,
            axisY,
            bgColor,
            fontColor,
            disableSource,
            taskVarFuncRef,
            ckEditorRef,
            ...rest
        }: CkEditorProps,
        ref: any,
    ) => {
        const { CKEDITOR }: any = window;

        const { userLanguage } = LoggedUserStore.getState();
        const [scriptLoaded, setScriptLoaded] = useState(false);
        const [editorIsSet, setEditorIsSet] = useState(false);
        const [editorMode, setEditorMode] = useState<'wysiwyg' | 'source'>('wysiwyg');
        const [editorVariableModalIsOpen, setEditorVariableModalIsOpen] = useState(false);
        const [editorSnippetModalIsOpen, setEditorSnippetModalIsOpen] = useState(false);
        const [darkModeIsEnabled, setDarkModeIsEnabled] = useState<boolean>(
            LoggedUserStore.getState().darkModeIsEnabled,
        );

        let editorRef = useRef<any>(null);
        if (ckEditorRef) {
            editorRef = ckEditorRef;
        }

        const wrapperRef = useRef<HTMLDivElement>(null);
        // react-hook-form
        const { control, setValue, getValues } = useFormContext();
        const id = useId();
        const editorValue = tvarName ? getValues(name) : value;

        const { t } = useTranslation();
        const { evalOnBlur } = useFormFunctions(
            name,
            drWatchVariables,
            tvarName,
            readOnly,
            false,
            onFocus,
            onComponentBlur,
        );

        useEffect(() => {
            defineScript('/assets/libs/ckeditor/ckeditor.js', {
                wrapper: 'body',
                onSuccess: () => {
                    setScriptLoaded(true);

                    if (CKEDITOR && taskHeight) {
                        CKEDITOR.config.height = '9.0625rem'; // min height of content area in task
                    }
                },
            });

            const onChange = () => {
                setDarkModeIsEnabled(LoggedUserStore.getState().darkModeIsEnabled);
            };

            LoggedUserStore.listen(onChange);

            return () => {
                editorRef.current?.destroy();
                LoggedUserStore.unlisten(onChange);
            };
        }, []);

        useEffect(() => {
            if (scriptLoaded) {
                // eslint-disable-next-line no-use-before-define
                initEditor();
            }
        }, [scriptLoaded]);

        // watch value (react-hook-form value) - in task (dynamic conditions)
        useEffect(() => {
            if (
                tvarName &&
                editorIsSet &&
                editorRef.current &&
                taskVarFuncRef?.current?.valueFromDynConds
            ) {
                // setValue for correct detection of value change in editor.on('blur')
                setValue(name, editorValue);
                editorRef.current.setData(editorValue);

                // eslint-disable-next-line no-param-reassign
                taskVarFuncRef.current = { ...taskVarFuncRef.current, valueFromDynConds: false };
            }
        }, [editorIsSet, editorRef.current, editorValue, taskVarFuncRef]);

        // switching codemirror source mode theme (default/dark)
        useEffect(() => {
            if (editorRef.current && editorMode === 'source') {
                const codemirrorWrap = wrapperRef.current?.querySelector('.CodeMirror');

                if (codemirrorWrap) {
                    if (darkModeIsEnabled) {
                        codemirrorWrap.classList.remove('cm-s-default');
                        codemirrorWrap.classList.add('cm-s-abcdef');
                    } else {
                        codemirrorWrap.classList.remove('cm-s-abcdef');
                        codemirrorWrap.classList.add('cm-s-default');
                    }
                }
            }
        }, [editorMode, darkModeIsEnabled]);

        const createPdf = async () => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrExportPreparing'),
            });

            try {
                const css = await ApiRequest.get(
                    `${document.location.origin}/get-file-content?path=../../../assets/libs/ckeditor/contents.css`,
                );

                let content = `<style type="text/css">${css}</style>`;
                const data = editorRef.current.getData();
                content += data;

                const body = {
                    content: content,
                };

                const payload = await ApiRequest.post('/to-pdf', JSON.stringify(body));

                downloadEncoded(payload.pdf, 'Editor-export.pdf', 'pdf');

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrExportCompleted'),
                });
            } catch (errorMessage) {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: t('alrExportFailed'),
                    serverError: errorMessage,
                });
            }
        };

        const openEditorVariableModal = () => {
            setEditorVariableModalIsOpen(true);
        };

        const openEditorSnippetModal = () => {
            setEditorSnippetModalIsOpen(true);
        };

        const closeEditorVariableModal = () => {
            setEditorVariableModalIsOpen(false);
        };

        const closeEditorSnippetModal = () => {
            setEditorSnippetModalIsOpen(false);
        };

        // insert text from modal
        const insertToEditor = (string: string) => {
            CKEDITOR.instances[`ckeditor-${name}`].insertText(string);
        };

        // TODO mainButtonsInMaximizedEditor in templatePrint
        // called from outside
        /* const minimize = () => {
            for (const i in CKEDITOR.instances) {
                // editor is maximized
                if (CKEDITOR.instances[i].getCommand('maximize').state === 1) {
                    CKEDITOR.instances[i].execCommand('maximize');
                }
            }
        }; */

        const initEditor = () => {
            let removeButtons = 'Underline,Subscript,Superscript,Replace,BidiLtr,BidiRtl';

            if (!advancedEditor) {
                removeButtons += ',Find,SelectAll,Smiley,PageBreak';
                removeButtons += ',ShowBlocks,ExportPdf,Preview,Print';
                removeButtons +=
                    ',CopyFormatting,CreateDiv,JustifyLeft,JustifyCenter,JustifyRight,JustifyBlock';
                removeButtons += ',BidiLtr,BidiRtl,Font,FontSize,TextColor,BGColor';
            }

            if (disableSource) {
                removeButtons += ',Source,searchCode,autoFormat,CommentSelectedRange,UncommentSelectedRange,AutoComplete';
            }

            let editorLang = userLanguage;

            if (editorLang === 'sr') {
                editorLang = 'sr-latn';
            }

            editorRef.current = CKEDITOR.replace(`ckeditor-${name}`, {
                language: editorLang,
                removeButtons: removeButtons,
            });

            editorRef.current.on('contentDom', () => {
                editorRef.current.on('instanceReady', () => {
                    const defaultValue = tvarName ? getValues(name) : value;

                    // after editor is loaded
                    if (defaultValue) {
                        editorRef.current.setData(defaultValue);
                    }
                    if (height) {
                        editorRef.current.resize('auto', height);
                    }
                });

                editorRef.current.on('change', () => {
                    // change is also called on mouse movement, setValue moved to blur

                    // Updates the <textarea> element that was replaced by the editor with the current data
                    // available in the editor.
                    editorRef.current.updateElement();
                });

                editorRef.current.on('blur', () => {
                    const val = tvarName ? getValues(name) : value;

                    if (!readOnly && val !== editorRef.current.getData()) {
                        setValue(name, editorRef.current.getData(), {
                            shouldValidate: true,
                            shouldDirty: true,
                            shouldTouch: true,
                        });
                    }

                    if (!readOnly) {
                        evalOnBlur(undefined, editorRef.current.getData());
                    }
                });

                editorRef.current.on('mode', (event: any) => {
                    setEditorMode(event.editor.mode);
                });

                editorRef.current.on('maximize', (event: any) => {
                    const maximizeState = event.data;
                    if (mainButtonsInMaximizedEditor) {
                        // maximize/minimize editor
                        // TODO mainButtonsInMaximizedEditor in templatePrint
                        // const mainButtons = document.getElementById('beside-tabs-nav');
                        // if (mainButtons !== null) {
                        //     if (maximizeState === 1) {
                        //         // maximized
                        //         mainButtons.classList.add('above-editor');
                        //     } else {
                        //         mainButtons.classList.remove(
                        //             'above-editor',
                        //         );
                        //     }
                        // }
                    }

                    const body = document.querySelector('body');
                    if (body && darkModeIsEnabled && maximizeState === 1) {
                        body.classList.add('dark-theme');
                    }
                });
            });

            if (advancedEditor) {
                // Insert button: export to PDF
                editorRef.current.addCommand('pdfExport', {
                    exec: () => {
                        createPdf();
                    },
                });

                editorRef.current.ui.addButton('pdfExportButton', {
                    label: t('exportToPdf'),
                    command: 'pdfExport',
                    toolbar: 'document',
                    icon: 'skins/ckePdf.png',
                });
            }

            // Insert variable button
            if (templateId) {
                editorRef.current.ui.addButton('varButton', {
                    label: t('insertVar'),
                    command: 'insertVar',
                    icon: 'skins/ckeVar2.png',
                });

                editorRef.current.addCommand('insertVar', {
                    exec: () => {
                        openEditorVariableModal();
                    },
                });

                editorRef.current.ui.addButton('snippetButton', {
                    label: t('insertSnippet'),
                    command: 'insertSnippet',
                    icon: 'skins/ckeSnip.png',
                });

                editorRef.current.addCommand('insertSnippet', {
                    exec: () => {
                        openEditorSnippetModal();
                    },
                });
            }

            setEditorIsSet(true);
        };

        return (
            <>
                <Controller
                    control={control}
                    name={name}
                    rules={validations}
                    defaultValue={value || ''}
                    render={({
                        // field,
                        fieldState,
                    }) => {
                        return (
                            <FormControl
                                id={id}
                                disabled={disabled}
                                helperText={fieldState?.error?.message || helperText}
                                error={fieldState?.invalid}
                                required={required || validations?.required}
                                fullWidth={fullWidth}
                                backgroundColor={bgColor}
                                fontColor={fontColor}
                                {...rest}
                            >
                                <div ref={ref}>
                                    <div ref={wrapperRef} className="ckeditor">
                                        <textarea
                                            id={`ckeditor-${name}`}
                                            name={name}
                                            style={{ display: 'none' }}
                                        />
                                    </div>
                                </div>
                            </FormControl>
                        );
                    }}
                />
                {templateId && editorVariableModalIsOpen && (
                    <ModalInsertSelected
                        isOpen={editorVariableModalIsOpen}
                        onClose={closeEditorVariableModal}
                        templateId={templateId}
                        insertToEditor={insertToEditor}
                        type="variable"
                    />
                )}
                {templateId && editorSnippetModalIsOpen && (
                    <ModalInsertSelected
                        isOpen={editorSnippetModalIsOpen}
                        onClose={closeEditorSnippetModal}
                        templateId={templateId}
                        insertToEditor={insertToEditor}
                        type="snippet"
                    />
                )}
            </>
        );
    },
);

export default CkEditor;
