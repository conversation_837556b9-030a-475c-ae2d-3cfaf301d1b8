import { ToggleButtonProps } from '@mui/material';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd';
import { FormControlProps } from '../FormControl';
import { CommonFormProps } from '../types';

export type MultiselectItemRaw = {
    title: string;
    value: any;
};

export type SelectTypeType =
    | 'DLU'
    | 'DLR'
    | 'DLO'
    | 'DT'
    | 'DL'
    | 'LT'
    | 'LD'
    | 'LN'; // // DL == DLL
export type MetaType = {
    selectAll: boolean;
    structuredList: boolean;
    dlUrl: null | string;
    dynTable: {
        filter: null | string;
        urlParams: null | string;
        order: null | string;
        sort: null | string;
        colIndex: null | number;
    };
    suggestBox: {
        apiUrl: null;
        prop: null;
        index: null;
        options: null;
    };
};

type Props = {
    value?: MultiselectItemRaw[] | number[] | string[];
    options?: MultiselectItemRaw[];
    label: string;
    rightLabel?: string;
    autosearch?: boolean;
    allowSelectAll?: boolean;
    allowRightSearch?: boolean;
    allowCsvExportButton?: boolean;
    maxItemsBeforeScroll?: number;
    enableAssignmentsOrdering?: boolean;
    showRightTotal?: boolean;
    multiselectType?: SelectTypeType;
    filteredList?: null | [];
    meta?: MetaType;
    dataUrl?: string;
    dataFilter?: string;
    dynTableId?: number | string;
    colIndex?: number | string;
    dlName?: null | string;
    dataStructureRenderer?: (option: {}) => {};
    timeStamp?: boolean;
    valueIsArrayOfObjects?: boolean;
    sortArrayOfObjects?: boolean;
    ignoreUserRestrictions?: boolean;
    watchPropValue?: boolean;
    unassignedServiceIsUsed?: boolean;
    doubleHeight?: boolean;
};

export type MultiselectProps = Omit<FormControlProps, 'children' | 'label'> &
    CommonFormProps &
    Props;

export type CustomListProps = Pick<
    MultiselectProps,
    | 'maxItemsBeforeScroll'
    | 'allowCsvExportButton'
    | 'allowSelectAll'
    | 'allowRightSearch'
    | 'autosearch'
    | 'disabled'
    | 'tooltip'
    | 'error'
    | 'helperText'
    | 'required'
    | 'doubleHeight'
> & {
    items: MultiselectItemRaw[];
    isLeft?: boolean;
    handleChecked: (value?: number) => void;
    checked: number[];
    onHandleDrop: (items: MultiselectItemRaw[]) => void;
    setChecked: (items: number[]) => void;
    enableAssignmentsOrdering?: boolean;
    showRightTotal?: boolean;
    searchPlaceholder?: string;
    paginated?: boolean;
    loadNextOptions?: () => void;
    totalCountAvailable?: number | null;
    addWaypoint?: boolean;
    filterOptionsByApi?: (searchText: string) => void;
    readOnly?: boolean;
    loadAllOptionsForToggleAll?: () => Promise<boolean>;
    allOptionsLoaded?: boolean;
    // onChange?: any;
};

export interface MultiselectItemProps extends ToggleButtonProps {
    item: MultiselectItemRaw;
    isLeft?: boolean;
    handleChecked?: () => void;
    dragHandleProps?: DraggableProvidedDragHandleProps | null | undefined;
    isDragging?: boolean;
    isDraggable?: boolean;
    showAddButton?: boolean;
    readOnly?: boolean;
}

export type ForwardRefT = {
    element: HTMLDivElement | null;
    getValueObjArray: () => MultiselectItemRaw[];
};
