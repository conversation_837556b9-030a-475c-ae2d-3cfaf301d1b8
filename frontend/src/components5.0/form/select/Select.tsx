import { Controller, useFormContext } from 'react-hook-form';
import React, {
    ForwardedRef, forwardRef, useId,
} from 'react';
import { Waypoint } from 'react-waypoint';
import { useTranslation } from 'react-i18next';
// mui
import { TextField, Autocomplete, Box } from '@mui/material';
import { createFilterOptions } from '@mui/material/Autocomplete';
// components
import FormControl from '../FormControl';
import { useFormFunctions } from '../../hooks/form/use-form-functions';
import { useFormFunctionsSelect } from '../../hooks/form/use-form-functions-select';
import { SelectPropTypes } from './types';
import PagingFooter from './PagingFooter';
import useSelectScrollUtils from './useSelectScrollUtils';
// flux
import LoggedUserStore from '../../../flux/loggedUser.store';

type SelectProps = SelectPropTypes & {
    forwardedRef?: ForwardedRef<HTMLSelectElement>;
};

const Select = forwardRef<unknown, SelectProps>(
    (
        {
            outputValueId = false,
            disableCloseOnSelect,
            getOptionDisabled,
            filterSelectedOptions,
            disableClearable,
            clearOnEscape,
            multiple,
            multipleValueIsArrayOfObjects,
            disabled,
            error,
            freeSolo = false,
            options,
            placeholder,
            blurOnSelect = false,
            value,
            loading,
            name,
            onComponentChange,
            onComponentBlur,
            onFocus,
            validations = {},
            drWatchVariables,
            tvarName,
            required,
            helperText,
            selectType,
            loadDataOnOpen, // dynamic rows
            dataStructureRenderer,
            dataStructure,
            timeStamp,
            nullable = true, // TODO ???
            evalOnChange,
            filteredList = null,
            meta = {
                dlUrl: null,
                dynTable: {
                    filter: null,
                    urlParams: null,
                    order: null,
                    sort: 'asc',
                    colIndex: null,
                },
                suggestBox: {
                    apiUrl: null,
                    prop: null,
                    index: null,
                    options: null,
                },
            },
            dataUrl,
            dynTableId,
            colIndex,
            dataFilter,
            isTemplateDr,
            dlName,
            defaultValue,
            readOnly,
            dataRenderer,
            fullWidth = true,
            size,
            enlargeOnOpen, // for dynamic rows - not used yet
            tvarId, // for task sections
            axisX,
            axisY,
            bgColor,
            fontColor,
            taskVarFuncRef,
            ignoreUserRestrictions,
            suggestAddNew,
            getSuggestAddNewTitle,
            onAddNew,
            ...rest
        }: SelectProps,
        ref,
    ) => {
        // react-hook-form
        const { control, setValue, getValues } = useFormContext();
        const id = useId();

        const { t } = useTranslation();
        const { handleEvalOnChange, handleFocus, evalOnBlur } =
            useFormFunctions(
                name,
                drWatchVariables,
                tvarName,
                readOnly,
                false,
                onFocus,
                onComponentBlur,
            );

        const {
            optionsState,
            paginated,
            optionsTotalCount,
            searchTotalCount,
            searchText,
            freeSoloState,
            inputValue,
            loadNextOptions,
            filterOptionsByApi,
            willOpen,
            isOpen,
            onOpen,
            onClose,
            handleIsOptionEqualToValue,
            getOptionLabel,
            filterOptions,
            changeValue,
            optionsLoading,
        } = useFormFunctionsSelect(
            name,
            setValue,
            handleEvalOnChange,
            tvarName ? getValues(name) : value, // getValues for task,
            options,
            selectType,
            timeStamp,
            dataStructureRenderer,
            dataStructure,
            filteredList,
            dynTableId,
            colIndex,
            dlName,
            dataUrl,
            dataFilter,
            defaultValue,
            meta,
            isTemplateDr,
            loadDataOnOpen,
            drWatchVariables,
            tvarName,
            readOnly,
            dataRenderer,
            freeSolo,
            onComponentChange,
            evalOnChange,
            undefined,
            undefined,
            false, // enlargeOnOpen - dynamic rows
            multiple,
            multipleValueIsArrayOfObjects,
            taskVarFuncRef,
            ignoreUserRestrictions,
        ); // props

        const { textInputKeyDownHandler } = useSelectScrollUtils({
            isOpen,
            optionsState,
            inputElementId: id,
            paginated,
            totalCount: searchText ? searchTotalCount : optionsTotalCount,
        });

        const filterOptionsDef: any = createFilterOptions({
            ignoreAccents: !!LoggedUserStore.getConfig(
                'tas.disableAccentSensitivity',
            ),
        });

        const isFreeSolo: boolean = freeSoloState || freeSolo;
        // enlargeOnOpen for dynamic rows
        const sx =
            enlargeOnOpen && willOpen
                ? {
                    minWidth: 230,
                    zIndex: 10000,
                }
                : {};

        return (
            <Controller
                control={control}
                name={name}
                rules={validations}
                defaultValue={value || (multiple ? [] : '')}
                render={({ field, fieldState, formState }) => {
                    return (
                        <FormControl
                            id={id}
                            disabled={disabled}
                            helperText={
                                fieldState?.error?.message || helperText
                            }
                            error={fieldState?.invalid}
                            required={required || validations?.required}
                            fullWidth={fullWidth}
                            backgroundColor={bgColor}
                            fontColor={fontColor}
                            {...rest}
                        >
                            <Autocomplete
                                id={id}
                                ref={(instance) => {
                                    field.ref(instance);
                                    if (typeof ref === 'function') {
                                        ref(instance);
                                    } else if (ref) {
                                        // eslint-disable-next-line no-param-reassign
                                        ref.current = instance;
                                    }
                                }}
                                open={isOpen}
                                sx={sx}
                                filterSelectedOptions={filterSelectedOptions}
                                value={field.value}
                                noOptionsText={t('notFound')}
                                getOptionLabel={getOptionLabel}
                                isOptionEqualToValue={
                                    handleIsOptionEqualToValue
                                }
                                disableCloseOnSelect={disableCloseOnSelect}
                                loading={loading || optionsLoading}
                                loadingText={t('loading')}
                                blurOnSelect={blurOnSelect}
                                freeSolo={isFreeSolo}
                                multiple={multiple}
                                disabled={disabled}
                                readOnly={readOnly}
                                getOptionDisabled={getOptionDisabled}
                                role="combobox"
                                options={optionsState}
                                inputValue={inputValue}
                                disableClearable={disableClearable}
                                autoHighlight
                                onChange={(e, autocompleteValue: any, reason) => {
                                    let val: any = autocompleteValue;
                                    let pickedSuggestedNewOption: any = null;

                                    if (isFreeSolo || outputValueId) {
                                        if (autocompleteValue?.isSuggestedNewOption) {
                                            pickedSuggestedNewOption = autocompleteValue;
                                            val = '';
                                        } else {
                                            val = autocompleteValue?.value || val;
                                        }
                                    } else if (multiple) {
                                        val = autocompleteValue.filter((option: any) => {
                                            if (option?.isSuggestedNewOption) {
                                                pickedSuggestedNewOption = option;
                                            }

                                            return !option?.isSuggestedNewOption;
                                        }).map(
                                            // react-hook-form for multiple return array of string (id), if props outputTypeId return array of id else array object
                                            (item: any) => item?.value || item,
                                        );
                                    }

                                    if (pickedSuggestedNewOption !== null) {
                                        onAddNew?.(pickedSuggestedNewOption.value);
                                    }

                                    field.onChange(val); // react-hook-form
                                    changeValue(val, reason); // local

                                    // due to update fieldState.touchedFields on change
                                    field.onBlur();
                                }}
                                onBlur={(e) => {
                                    if (isFreeSolo) {
                                        evalOnBlur(e);
                                    }
                                }}
                                onFocus={() => {
                                    handleFocus();
                                }}
                                onOpen={onOpen}
                                onClose={onClose}
                                filterOptions={(optionsToFilter, params) => {
                                    const filteredOptions: any[] = (paginated ? filterOptions : filterOptionsDef)(optionsToFilter, params);

                                    if (suggestAddNew) {
                                        filteredOptions.unshift({
                                            title: (
                                                getSuggestAddNewTitle?.(params.inputValue) || (
                                                    params.inputValue
                                                        ? `${t('add')} "${params.inputValue}"`
                                                        : t('add')
                                                )
                                            ),
                                            value: params.inputValue || '',
                                            isSuggestedNewOption: true,
                                        });
                                    }

                                    return filteredOptions;
                                }}
                                onInputChange={filterOptionsByApi}
                                onKeyDown={(event) => {
                                    if (isOpen && (event.key === 'Enter' || event.keyCode === 13)) {
                                        event.stopPropagation();
                                    }
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        color="secondary"
                                        placeholder={placeholder}
                                        error={fieldState?.invalid}
                                        {...params}
                                        size={size}
                                        inputProps={{
                                            ...params.inputProps,
                                            onKeyDown: textInputKeyDownHandler,
                                        }}
                                    />
                                )}
                                renderOption={(props, option, state) => (
                                    <Box
                                        component="li"
                                        {...props}
                                        sx={{
                                            textDecoration: (
                                                (option as any)?.isSuggestedNewOption
                                                    ? 'underline'
                                                    : 'none'
                                            ),
                                        }}
                                        key={state.index}
                                    >
                                        {option.title}
                                        {
                                            paginated &&
                                            optionsState.length === state.index + 1 && (
                                                <Waypoint
                                                    onEnter={loadNextOptions}
                                                />
                                            )
                                        }
                                    </Box>
                                )}
                                ListboxComponent={PagingFooter}
                                ListboxProps={
                                    {
                                        paginated: paginated,
                                        optionsCount: optionsState.length,
                                        totalCount: searchText
                                            ? searchTotalCount
                                            : optionsTotalCount,
                                    } as any
                                }
                            />
                        </FormControl>
                    );
                }}
            />
        );
    },
);

export default Select;
