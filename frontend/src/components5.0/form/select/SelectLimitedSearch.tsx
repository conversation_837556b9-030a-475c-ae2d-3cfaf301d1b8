import { Controller, useFormContext } from 'react-hook-form';
import {
    forwardRef, useId, useImperativeHandle, useRef,
} from 'react';
import { Waypoint } from 'react-waypoint';
import { useTranslation } from 'react-i18next';
import { map, find, compact } from 'lodash';

// mui
import { TextField, Autocomplete, Box } from '@mui/material';
import { createFilterOptions } from '@mui/material/Autocomplete';

// components
import FormControl from '../FormControl';
import { useFormFunctions } from '../../hooks/form/use-form-functions';
import { useFormFunctionsSelect } from '../../hooks/form/use-form-functions-select';
import { ForwardRefT, SelectPropTypes } from './types';
import PagingFooter from './PagingFooter';
import useSelectScrollUtils from './useSelectScrollUtils';
// flux
import LoggedUserStore from '../../../flux/loggedUser.store';

type SelectLimitedSearchProps = {
    disableSearch?: boolean;
    isMobileDevice?: boolean;
};
type SelectProps = Omit<SelectPropTypes, 'freeSolo'> & SelectLimitedSearchProps;

const numberOfOptionsToCheck = 15;

const SelectLimitedSearch = forwardRef<ForwardRefT, SelectProps>(
    (
        {
            disableSearch = false,
            // isMobileDevice - to check the number of options on mobile - (>= 15 - disableSearch on mobile)
            // isMobileDevice = undefined - to check the number of options (>= 15 - disableSearch)
            isMobileDevice,
            outputValueId = false,
            disableCloseOnSelect,
            getOptionDisabled,
            filterSelectedOptions,
            disableClearable,
            clearOnEscape,
            multiple,
            multipleValueIsArrayOfObjects,
            disabled,
            error,
            options,
            placeholder,
            blurOnSelect = false,
            value,
            loading,
            name,
            onComponentChange,
            onComponentBlur,
            onFocus,
            validations = {},
            drWatchVariables,
            tvarName,
            required,
            helperText,
            selectType,
            loadDataOnOpen, // dynamic rows
            dataStructureRenderer,
            dataStructure,
            timeStamp,
            evalOnChange,
            filteredList = null,
            meta = {
                dlUrl: null,
                dynTable: {
                    filter: null,
                    urlParams: null,
                    order: null,
                    sort: 'asc',
                    colIndex: null,
                },
                suggestBox: {
                    apiUrl: null,
                    prop: null,
                    index: null,
                    options: null,
                },
            },
            dataUrl,
            dynTableId,
            colIndex,
            dataFilter,
            isTemplateDr,
            dlName,
            defaultValue,
            readOnly,
            dataRenderer,
            fullWidth = true,
            size,
            enlargeOnOpen, // for dynamic rows - not used yet
            tvarId, // for task sections
            axisX,
            axisY,
            bgColor,
            fontColor,
            taskVarFuncRef,
            ignoreUserRestrictions,
            suggestAddNew,
            getSuggestAddNewTitle,
            onAddNew,
            ...rest
        }: SelectProps,
        ref,
    ) => {
        const inputRef = useRef<HTMLInputElement | null>(null);
        // react-hook-form
        const { control, setValue, getValues } = useFormContext();
        const id = useId();
        const { t } = useTranslation();

        const { handleEvalOnChange, handleFocus } =
            useFormFunctions(
                name,
                drWatchVariables,
                tvarName,
                readOnly,
                false,
                onFocus,
                onComponentBlur,
            );

        const {
            optionsState,
            paginated,
            optionsTotalCount,
            searchTotalCount,
            searchText,
            inputValue,
            loadNextOptions,
            filterOptionsByApi,
            willOpen,
            isOpen,
            onOpen,
            onClose,
            handleIsOptionEqualToValue,
            getOptionLabel,
            filterOptions,
            changeValue,
            optionsLoading,
            multipleValuesFromService,
        } = useFormFunctionsSelect(
            name,
            setValue,
            handleEvalOnChange,
            tvarName ? getValues(name) : value, // getValues for task,
            options,
            selectType,
            timeStamp,
            dataStructureRenderer,
            dataStructure,
            filteredList,
            dynTableId,
            colIndex,
            dlName,
            dataUrl,
            dataFilter,
            defaultValue,
            meta,
            isTemplateDr,
            loadDataOnOpen,
            drWatchVariables,
            tvarName,
            readOnly,
            dataRenderer,
            false,
            onComponentChange,
            evalOnChange,
            undefined,
            undefined,
            false, // enlargeOnOpen - dynamic rows
            multiple,
            multipleValueIsArrayOfObjects,
            taskVarFuncRef,
            ignoreUserRestrictions,
        ); // props

        const { textInputKeyDownHandler } = useSelectScrollUtils({
            isOpen,
            optionsState,
            inputElementId: id,
            paginated,
            totalCount: searchText ? searchTotalCount : optionsTotalCount,
        });

        useImperativeHandle(ref, () => {
            const values = getValues(name) || [];

            return {
                element: inputRef.current,
                // for multiple - used in dynamic conditions
                getValueObjArray() {
                    // find options by values
                    const result = map(values, (val) => {
                        const option = find(optionsState, { value: val });
                        if (option) {
                            return option;
                        }
                        return find(multipleValuesFromService, { value: val });
                    });

                    // Remove undefined values when a value was not found - should not occur
                    return compact(result) as Record<string, any>[];
                },
            };
        }, [multipleValuesFromService, optionsState]);

        const filterOptionsDef: any = createFilterOptions({
            ignoreAccents: !!LoggedUserStore.getConfig('tas.disableAccentSensitivity'),
        });

        const checkNumberOfOptions = typeof isMobileDevice === 'undefined' || isMobileDevice;
        const searchIsDisabled = disableSearch || (checkNumberOfOptions && optionsState.length < numberOfOptionsToCheck);

        // enlargeOnOpen for dynamic rows
        const sx =
            enlargeOnOpen && willOpen
                ? {
                    minWidth: 230,
                    zIndex: 10000,
                }
                : {};

        return (
            <Controller
                control={control}
                name={name}
                rules={validations}
                defaultValue={value || (multiple ? [] : '')}
                render={({
                    field,
                    fieldState,
                    // formState,
                }) => {
                    return (
                        <FormControl
                            id={id}
                            disabled={disabled}
                            helperText={
                                fieldState?.error?.message || helperText
                            }
                            error={fieldState?.invalid}
                            required={required || validations?.required}
                            fullWidth={fullWidth}
                            backgroundColor={bgColor}
                            fontColor={fontColor}
                            {...rest}
                        >
                            <Autocomplete
                                id={id}
                                ref={(instance: HTMLInputElement) => {
                                    field.ref(instance);
                                    inputRef.current = instance;
                                }}
                                open={isOpen}
                                sx={sx}
                                filterSelectedOptions={filterSelectedOptions}
                                value={field.value}
                                noOptionsText={t('notFound')}
                                getOptionLabel={getOptionLabel}
                                isOptionEqualToValue={
                                    handleIsOptionEqualToValue
                                }
                                disableCloseOnSelect={disableCloseOnSelect}
                                loading={loading || optionsLoading}
                                loadingText={t('loading')}
                                blurOnSelect={blurOnSelect}
                                multiple={multiple}
                                disabled={disabled}
                                readOnly={readOnly}
                                getOptionDisabled={getOptionDisabled}
                                role="combobox"
                                options={optionsState}
                                inputValue={inputValue}
                                disableClearable={disableClearable}
                                autoHighlight
                                onChange={(e, autocompleteValue: any, reason) => {
                                    let val: any = autocompleteValue;
                                    let pickedSuggestedNewOption: any = null;

                                    if (outputValueId) {
                                        if (autocompleteValue?.isSuggestedNewOption) {
                                            pickedSuggestedNewOption = autocompleteValue;
                                            val = '';
                                        } else {
                                            val = autocompleteValue?.value || val;
                                        }
                                    } else if (multiple) {
                                        val = autocompleteValue.filter((option: any) => {
                                            if (option?.isSuggestedNewOption) {
                                                pickedSuggestedNewOption = option;
                                            }

                                            return !option?.isSuggestedNewOption;
                                        }).map(
                                            // react-hook-form for multiple return array of string (id), if props outputTypeId return array of id else array object
                                            (item: any) => item?.value || item,
                                        );
                                    }

                                    if (pickedSuggestedNewOption !== null) {
                                        onAddNew?.(pickedSuggestedNewOption.value);
                                    }

                                    field.onChange(val); // react-hook-form
                                    changeValue(val, reason); // local

                                    // due to update fieldState.touchedFields on change
                                    field.onBlur();
                                }}
                                onFocus={() => {
                                    handleFocus();
                                }}
                                onOpen={onOpen}
                                onClose={onClose}
                                filterOptions={(optionsToFilter, params) => {
                                    const filteredOptions: any[] = (paginated ? filterOptions : filterOptionsDef)(optionsToFilter, params);

                                    if (suggestAddNew) {
                                        filteredOptions.unshift({
                                            title: (
                                                getSuggestAddNewTitle?.(params.inputValue) || (
                                                    params.inputValue
                                                        ? `${t('add')} "${params.inputValue}"`
                                                        : t('add')
                                                )
                                            ),
                                            value: params.inputValue || '',
                                            isSuggestedNewOption: true,
                                        });
                                    }

                                    return filteredOptions;
                                }}
                                onInputChange={filterOptionsByApi}
                                onKeyDown={(event) => {
                                    if (isOpen && (event.key === 'Enter' || event.keyCode === 13)) {
                                        event.stopPropagation();
                                    }
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        color="secondary"
                                        placeholder={placeholder}
                                        error={fieldState?.invalid}
                                        {...params}
                                        size={size}
                                        inputProps={{
                                            ...params.inputProps,
                                            readOnly: searchIsDisabled,
                                            onKeyDown: textInputKeyDownHandler,
                                        }}
                                    />
                                )}
                                selectOnFocus={!searchIsDisabled}
                                renderOption={(props, option, state) => (
                                    <Box
                                        component="li"
                                        {...props}
                                        sx={{
                                            textDecoration: (
                                                (option as any)?.isSuggestedNewOption
                                                    ? 'underline'
                                                    : 'none'
                                            ),
                                        }}
                                        key={state.index}
                                    >
                                        {option.title}
                                        {paginated && optionsState.length === state.index + 1 && (
                                            <Waypoint
                                                onEnter={loadNextOptions}
                                            />
                                        )}
                                    </Box>
                                )}
                                ListboxComponent={PagingFooter}
                                ListboxProps={
                                    {
                                        paginated: paginated,
                                        optionsCount: optionsState.length,
                                        totalCount: searchText
                                            ? searchTotalCount
                                            : optionsTotalCount,
                                    } as any
                                }
                            />
                        </FormControl>
                    );
                }}
            />
        );
    },
);

export default SelectLimitedSearch;
