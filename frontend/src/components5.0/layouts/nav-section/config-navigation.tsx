import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import LoggedUserStore from '../../../flux/loggedUser.store';
// ----------------------------------------------------------------------

export function useNavData() {
    const { t } = useTranslation();
    const userRoles = LoggedUserStore.getState().roles;
    const isAdmin = LoggedUserStore.isAdmin();
    const hasPermition = !userRoles.some((role: { id: string }) => ['-1', '-2'].includes(role.id));

    const data = useMemo(
        () => [
            // Oblibene
            // ----------------------------------------------------------------------
            /*  {
        subheader: 'Oblibene',
        items: [
          { title: 'nova objednavka', path: 'nic', icon: 'favorite' },
          { title: 'nastaveni', path: 'nic', icon: 'favorite' },
          { title: 'smlova', path: 'nic', icon: 'favorite' },
        ],
      }, */
            // User
            // ----------------------------------------------------------------------
            {
                subheader: '',
                items: [
                    {
                        title: t('dashboard'),
                        path: '/',
                        icon: 'home',
                        caption: t('dashboard'),
                        deep: false,
                    },
                    {
                        title: t('tasks'),
                        path: '/tasks',
                        icon: 'tasks',
                        caption: t('tasks'),
                        deep: true,
                    },
                    {
                        title: t('cases'),
                        path: '/cases',
                        icon: 'cases',
                        caption: t('cases'),
                        deep: true,
                    },
                    {
                        title: t('overviews'),
                        path: '/overviews',
                        icon: 'overviews',
                        caption: t('overviews'),
                        deep: true,
                    },
                    {
                        title: t('reports'),
                        path: '/reports/graphs',
                        icon: 'reports',
                        caption: t('reports'),
                        deep: true,
                    },
                    {
                        title: t('documents'),
                        path: '/documents',
                        icon: 'documents',
                        caption: t('documents'),
                        deep: true,
                    },
                    {
                        title: t('Manuals'),
                        path: '/public-files',
                        icon: 'manuals',
                        caption: t('Manuals'),
                        deep: true,
                    },
                    {
                        title: t('news'),
                        path: '/news',
                        icon: 'news',
                        caption: t('news'),
                        deep: true,
                    },
                    /*  {
            title: 'oblíbené',
            path: 'nic',
            icon: 'favorite',
            children: [
              { title: 'nova objednavka', path: 'nic' },
              { title: 'nastaveni', path: 'nic' },
              { title: 'smlouva', path: 'nic' },
            ],
          }, */
                ],
            },

            // Admin
            // ----------------------------------------------------------------------
            {
                subheader: isAdmin && hasPermition ? 'Admin' : null,
                items: [
                    {
                        title: t('templates'),
                        path: '/templates',
                        icon: 'templates',
                        caption: t('templates'),
                        deep: true,
                        roles: ['-2', '-1'],
                    },
                    {
                        title: t('administration'),
                        path: '/administration-menu',
                        icon: 'administrator',
                        caption: t('administration'),
                        deep: true,
                        roles: ['-5', '-4', '-1'],
                    },
                    {
                        title: t('plans'),
                        path: '/plans',
                        icon: 'planning',
                        caption: t('plans'),
                        deep: true,
                        roles: ['-2', '-1'],
                    },
                    {
                        title: t('users'),
                        path: '/users',
                        icon: 'users',
                        caption: t('users'),
                        deep: true,
                        roles: ['-1'],
                    },
                    {
                        title: t('roles'),
                        path: '/roles',
                        icon: 'roles',
                        caption: t('roles'),
                        deep: true,
                        roles: ['-1'],
                    },
                    {
                        title: t('orgStructure'),
                        path: '/structure',
                        icon: 'structure',
                        caption: t('orgStructure'),
                        deep: true,
                        roles: ['-1'],
                    },
                    {
                        title: t('events'),
                        path: '/events',
                        icon: 'events',
                        caption: t('events'),
                        deep: true,
                        roles: ['-1'],
                    },
                ],
            },
        ],
        [],
    );

    return data;
}
