import React from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { useTheme } from '@mui/material/styles';
import {
    IconButton as MuiIconButton, Box, Link, Tooltip, Stack,
} from '@mui/material';
// theme
import { hideScroll } from '../../../theme/css';
// hooks
import { useBoolean } from '../../hooks/use-boolean';
// components
import IconButton from '../../IconButton';
import Logo from '../../logo/Logo';
import NavSectionMini from './mini/NavSectionMini';
import ModalNewCase from '../../Modals/ModalNewCase';
import { PoweredBySycaLogo } from '../../illustrations/Illustrations';
//
import { NAV } from '../config-layout';
import NavToggleButton from './NavToggleButton';
// types
import { NavData } from './types';
// flux
import LoggedUserStore from '../../../flux/loggedUser.store';
import MenuStore from '../../../flux/menu.store';

type Props = {
    navMiniToggle: VoidFunction;
    navMini: boolean;
    location: any;
    items: NavData[];
};

const NavMini = ({
    navMiniToggle, navMini, location, items,
}: Props) => {
    const theme = useTheme();
    const { t } = useTranslation();
    const newCaseModal = useBoolean();
    const hideNewCaseButton = LoggedUserStore.getConfig('tas.buttons.hideNewProcess');
    const hidePoweredByTAS = LoggedUserStore.getConfig('tas.hidePoweredByTAS');

    return (
        <>
            <Box
                id="main-navi"
                component="nav"
                sx={{
                    flexShrink: { lg: 0 },
                    width: { lg: NAV.W_MINI },
                }}
            >
                <Stack
                    spacing={1.5}
                    sx={{
                        top: 0,
                        backgroundColor: theme.palette.background.paper,
                        padding: '0.75rem 0.5rem',
                        height: 1,
                        position: 'fixed',
                        width: NAV.W_MINI,
                        borderRight: `1px solid ${theme.palette.divider}`,
                        ...hideScroll.x,
                    }}
                >
                    <Box height="1.7rem" mx="auto">
                        <Logo size="small" />
                    </Box>
                    {!hideNewCaseButton && (
                        <Tooltip title={t('newCase')} arrow placement="right">
                            <IconButton
                                icon="add"
                                size="large"
                                sx={{
                                    marginBottom: '1rem',
                                    height: '3rem',
                                    width: '3rem',
                                    alignSelf: 'center',
                                    '& span': { fontSize: '1.5rem' },
                                }}
                                onClick={newCaseModal.onTrue}
                                disableRipple
                            />
                        </Tooltip>
                    )}

                    <NavSectionMini
                        location={location}
                        data={items}
                        config={{
                            hiddenLabel: true,
                        }}
                        sx={{ pb: hidePoweredByTAS ? '2.875rem' : '5.25rem' }}
                    />
                </Stack>
                {!hidePoweredByTAS && (
                    <Stack
                        position="fixed"
                        bottom="2.875rem"
                        alignItems="center"
                        justifyContent="center"
                        direction="row"
                        sx={{
                            width: '3.5rem', // NAV.W_MINI - left padding, to center and to not overlap with border
                            p: '0.5rem 0 0 0.5rem',
                            backgroundColor: 'background.paper',
                        }}
                    >
                        <Tooltip title="Powered by Syca" placement="right" arrow>
                            <Box>
                                <Link
                                    href="https://www.syca.app"
                                    target="_blank"
                                    rel="noopener"
                                    underline="none"
                                    sx={{
                                        pb: '0.0625rem',
                                        pointerEvents: 'auto',
                                    }}
                                >
                                    <MuiIconButton
                                        disableTouchRipple
                                        size="medium"
                                        color="default"
                                        sx={{
                                            zIndex: theme.zIndex.appBar + 1,
                                            color: '#99A0AE',
                                            ...theme.applyStyles('dark', {
                                                color: '#828795',
                                            }),
                                            '&.MuiButtonBase-root.MuiIconButton-root span': {
                                                mt: 0,
                                            },
                                            pointerEvents: 'auto',
                                        }}
                                    >
                                        <PoweredBySycaLogo
                                            sx={{ height: '1.1875rem', margin: '0.1875rem 0' }}
                                        />
                                    </MuiIconButton>
                                </Link>
                            </Box>
                        </Tooltip>
                    </Stack>
                )}
                <Box
                    display="flex"
                    justifyContent="center"
                    position="fixed"
                    bottom="0"
                    width="3.5rem"
                    sx={{
                        p: '0.5rem 0 0.625rem 0.5rem',
                        backgroundColor: 'background.paper',
                    }}
                >
                    <NavToggleButton navMini={navMini} navMiniToggle={navMiniToggle} />
                </Box>
            </Box>
            {newCaseModal.value && (
                <ModalNewCase
                    isOpen={newCaseModal.value}
                    onClose={newCaseModal.onFalse}
                    headerId={MenuStore.getState().headerId}
                    location={location}
                />
            )}
        </>
    );
};
export default NavMini;
