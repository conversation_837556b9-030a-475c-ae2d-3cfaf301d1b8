import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import {
    IconButton as MuiIconButton, Box, Drawer, Link, Stack, Typography,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
// hooks
import { useBoolean, useResponsive } from '../../hooks';
import { usePathname } from '../../routes/hooks';
// components
import Logo from '../../logo/Logo';
import Scrollbar from '../../scrollbar';
import NavSectionVertical from './vertical/NavSectionVertical';
import NavToggleButton from './NavToggleButton';
import Button from '../../Button';
import MyIcon from '../../MyIcon';
import ModalNewCase from '../../Modals/ModalNewCase';
import { PoweredBySycaLogo } from '../../illustrations/Illustrations';
//
import { NAV } from '../config-layout';
// types
import { NavData } from './types';
// flux
import LoggedUserStore from '../../../flux/loggedUser.store';
import MenuStore from '../../../flux/menu.store';

type Props = {
    openNav: boolean;
    onCloseNav: VoidFunction;
    navMiniToggle: VoidFunction;
    navMini: boolean;
    location: AnalyserOptions;
    items: NavData[];
};

const NavVertical = ({
    openNav, onCloseNav, navMiniToggle, navMini, location, items,
}: Props) => {
    const theme = useTheme();
    const { t } = useTranslation();
    const pathname = usePathname();
    const lgUp = useResponsive('up', 'lg');
    const newCaseModal = useBoolean();
    const hideNewCaseButton = LoggedUserStore.getConfig('tas.buttons.hideNewProcess');
    const hidePoweredByTAS = LoggedUserStore.getConfig('tas.hidePoweredByTAS');

    useEffect(() => {
        if (openNav) {
            onCloseNav();
        }
    }, [pathname]);

    const renderContent = (
        <Scrollbar
            sx={{
                backgroundColor: theme.palette.background.paper,
                color: theme.palette.text.primary,
                height: 1,
                padding: '0.75rem 0.5rem',
                '& .simplebar-content': {
                    height: 1,
                    display: 'flex',
                    flexDirection: 'column',
                },
            }}
        >
            <Stack direction="column" spacing={1.5}>
                {/*   logo */}

                <Logo size="large" sx={{ height: '1.7rem' }} />
                {/* button */}
                {!hideNewCaseButton && (
                    <Button
                        label={t('newCase')}
                        variant="contained"
                        size="large"
                        color="primary"
                        startIcon={<MyIcon icon="add" />}
                        fullWidth
                        onClick={newCaseModal.onTrue}
                        sx={{ marginBottom: '0.9375rem' }}
                        disableRipple
                        disableElevation
                    />
                )}
                {/*  button links */}
                <NavSectionVertical
                    data={items}
                    location={location}
                    sx={{ pb: hidePoweredByTAS ? '2.625rem' : '5.0625rem' }}
                />
            </Stack>
            {!hidePoweredByTAS && (
                <Stack
                    position="fixed"
                    bottom="2.625rem"
                    left="0rem"
                    alignItems="center"
                    justifyContent="center"
                    direction="row"
                    sx={{
                        gap: '0.5rem',
                        width: NAV.W_VERTICAL - 1, // NAV.W_VERTICAL - right border
                        padding: '0.5rem 0 0 0',
                        backgroundColor: 'background.paper',
                    }}
                >
                    <Link
                        href="https://www.syca.app"
                        target="_blank"
                        rel="noopener"
                        underline="none"
                        sx={{
                            pb: '0.0625rem',
                            pointerEvents: 'auto',
                        }}
                    >
                        <MuiIconButton
                            disableTouchRipple
                            size="medium"
                            color="default"
                            sx={{
                                zIndex: theme.zIndex.appBar + 1,
                                color: '#99A0AE',
                                ...theme.applyStyles('dark', {
                                    color: '#828795',
                                }),
                                '&.MuiButtonBase-root.MuiIconButton-root span': {
                                    mt: 0,
                                },
                                pointerEvents: 'auto',
                            }}
                        >
                            <PoweredBySycaLogo
                                sx={{
                                    height: '1.1875rem',
                                    margin: '0.1875rem 0',
                                }}
                            />
                        </MuiIconButton>
                    </Link>
                    <Typography color="text.secondary" variant="caption">
                        Powered by Syca
                    </Typography>
                </Stack>
            )}
            <Box
                position="fixed"
                bottom="0"
                left="0"
                sx={{
                    width: NAV.W_VERTICAL - 1, // NAV.W_VERTICAL - right border
                    p: '0.5rem 0 0.625rem 0.75rem',
                    backgroundColor: 'background.paper',
                }}
            >
                <NavToggleButton navMini={navMini} navMiniToggle={navMiniToggle} />
            </Box>
        </Scrollbar>
    );

    return (
        <>
            <Box
                id="main-navi"
                component="nav"
                sx={{
                    flexShrink: { lg: 0 },
                    width: { lg: NAV.W_VERTICAL },
                }}
            >
                {lgUp ? (
                    <Stack
                        sx={{
                            top: 0,
                            height: 1,
                            position: 'fixed',
                            width: NAV.W_VERTICAL,
                            borderRight: `1px solid ${theme.palette.divider}`,
                        }}
                    >
                        {renderContent}
                    </Stack>
                ) : (
                    <Drawer
                        open={openNav}
                        onClose={onCloseNav}
                        PaperProps={{
                            sx: {
                                width: NAV.W_VERTICAL,
                            },
                        }}
                    >
                        {renderContent}
                    </Drawer>
                )}
            </Box>
            {newCaseModal.value && (
                <ModalNewCase
                    isOpen={newCaseModal.value}
                    onClose={newCaseModal.onFalse}
                    headerId={MenuStore.getState().headerId}
                    location={location}
                />
            )}
        </>
    );
};
export default NavVertical;
