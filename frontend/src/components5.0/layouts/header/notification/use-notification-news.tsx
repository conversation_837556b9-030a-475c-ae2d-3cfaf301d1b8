import { useState } from 'react';
import moment from 'moment';
// hooks
import { useTranslation } from 'react-i18next';
import { useStore } from '../../../zustand/boundStore';
// utils
import ApiRequest from '../../../../api/apiRequest';
// types
import { NewsPostTypeGET } from '../../../sections/administration/news-management/types';
// flux
import AlertsActions from '../../../../components/alerts/alerts.actions';

const useNotificationNews = () => {
    const { t } = useTranslation();

    const [bellNewsPosts, setBellNewsPosts] = useState<NewsPostTypeGET[]>([]);

    const seenBellNewsInfo = useStore((state) => state.seenBellNewsInfo);
    const setSeenBellNewsInfo = useStore((state) => state.setSeenBellNewsInfo);

    const removeBellNewsPost = (post: NewsPostTypeGET) => {
        const { lastVisiblePostIds } = seenBellNewsInfo;

        setSeenBellNewsInfo({
            ...seenBellNewsInfo,
            lastVisiblePostIds: lastVisiblePostIds.filter((id) => id !== post.post_id),
        });

        setBellNewsPosts(bellNewsPosts.filter((p) => p.post_id !== post.post_id));
    };

    const handleSetBellNewsPosts = (posts: NewsPostTypeGET[]) => {
        const { minDate } = seenBellNewsInfo;

        const possibleNewMinDate = moment.max(posts.map((p) => moment(p.post_publication_date)));
        const shouldSetNewMinDate = !minDate || possibleNewMinDate.isAfter(moment(minDate));

        setSeenBellNewsInfo({
            lastVisiblePostIds: posts.map((p) => p.post_id),
            minDate: shouldSetNewMinDate ? possibleNewMinDate.toISOString() : minDate,
        });

        setBellNewsPosts(posts);
    };

    const fetchBellNewsPosts = () => {
        const { minDate, lastVisiblePostIds } = seenBellNewsInfo;

        // using start of day since the BE as of now ignores the time part in <ge> comparisons
        // - TODO: remove once the BE can handle the time part
        const usedMinDate = minDate ? moment(minDate).startOf('day').toISOString() : null;

        const minDatePart = usedMinDate ? `<and>post_publication_date<ge>"${usedMinDate}"` : '';
        const limit = 5;

        ApiRequest.get(`/posts?filter=post_state<eq>"ACTIVE"${minDatePart}&limit=${limit}&order=post_publication_date,id&sort=desc,desc`)
            .then((payload: any) => {
                const filteredItems = (payload.items as NewsPostTypeGET[]).filter((post: NewsPostTypeGET) => (
                    // finish the filtering on the client side
                    !minDate || moment(post.post_publication_date).isAfter(moment(minDate))
                ));

                let newVisiblePosts = filteredItems;

                if (filteredItems.length < limit && lastVisiblePostIds.length > 0) {
                    const postIdsFilterPart = `<and>post_id<in>"${lastVisiblePostIds.join(',')}"`;

                    ApiRequest.get(`/posts?filter=post_state<eq>"ACTIVE"${postIdsFilterPart}&order=post_publication_date,id&sort=desc,desc`)
                        .then((lastVisiblePostsPayload: any) => {
                            const lastVisiblePosts = (lastVisiblePostsPayload.items as NewsPostTypeGET[]);
                            const lastVisiblePostsWithNoNewVersionInLimitedFetch = lastVisiblePosts.filter((lastVisiblePost) => (
                                !newVisiblePosts.find((post) => post.post_id === lastVisiblePost.post_id)
                            ));

                            newVisiblePosts = filteredItems.concat(lastVisiblePostsWithNoNewVersionInLimitedFetch).slice(0, limit);

                            handleSetBellNewsPosts(newVisiblePosts);
                        })
                        .catch((errorMessage: any) => {
                            AlertsActions.addAlert({
                                type: 'alert',
                                message: t('alrNotificationsNewsLoadFailed'),
                                serverError: errorMessage,
                            });
                        });
                } else {
                    handleSetBellNewsPosts(newVisiblePosts);
                }
            })
            .catch((errorMessage: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrNotificationsNewsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    };

    return {
        bellNewsPosts,
        fetchBellNewsPosts,
        removeBellNewsPost,
    };
};

export default useNotificationNews;
