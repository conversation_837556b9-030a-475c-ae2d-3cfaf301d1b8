// @mui
import {
    MenuItem,
    ListItemText,
    ListItemIcon,
    Box,
    Typography,
    menuItemClasses,
} from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../routes/hooks';
// components
import MyIcon from '../../../MyIcon';
// utils
import {
    checkLangMutation,
    hexToRgba,
} from '../../../../common/utils';
// types
import { NewsPostTypeGET } from '../../../sections/administration/news-management/types';
import { pxToRem } from '../../../utils/utils';

type NotificationNewsProps = {
    posts: NewsPostTypeGET[];
    onPostOpen: (post: NewsPostTypeGET) => void;
    closePopover: VoidFunction;
};

const NotificationNews = ({
    posts,
    onPostOpen,
    closePopover,
}: NotificationNewsProps) => {
    const { push } = useRouter();
    const { t } = useTranslation();

    return (
        <>
            <MenuItem
                divider
                disabled
                sx={{
                    borderRadius: 0,
                    paddingX: 0,
                    '&:hover': {
                        backgroundColor: 'initial',
                    },

                    // the disabled prop applies 0.48 opacity to the whole MenuItem
                    // to be able to have full opacity on the "more news" text, we need to work around it through these styles:
                    opacity: 1,
                    [`&.${menuItemClasses.disabled}`]: {
                        opacity: '1 !important',
                        '& > div:first-of-type': {
                            opacity: '0.48 !important',
                        },
                        borderBottomColor: (theme) => hexToRgba(theme.palette.divider, 0.48),
                    },
                }}
            >
                <ListItemText primaryTypographyProps={{ variant: 'overline' }}>
                    {t('news')}
                </ListItemText>
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        pointerEvents: 'all',
                    }}
                >
                    <Typography
                        variant="subtitle1"
                        onClick={() => {
                            closePopover();
                            push('/news');
                        }}
                        sx={{
                            color: (theme) => theme.palette.text.primary,
                            typography: 'subtitle1',
                            textDecoration: 'underline',
                            cursor: 'pointer',
                            lineHeight: pxToRem(14),
                        }}
                    >
                        {t('moreNews')}
                    </Typography>
                </Box>
            </MenuItem>
            {posts.map((post) => (
                <MenuItem
                    key={post.post_id}
                    onClick={() => {
                        closePopover();
                        onPostOpen(post);
                    }}
                >
                    <ListItemIcon>
                        <MyIcon icon="news" sx={{ color: (theme) => theme.palette.action.disabled }} />
                    </ListItemIcon>
                    <ListItemText
                        primaryTypographyProps={{
                            variant: 'subtitle1',
                            sx: { whiteSpace: 'normal' },
                        }}
                    >
                        {checkLangMutation(post, 'post_title')}
                    </ListItemText>
                </MenuItem>
            ))}
        </>
    );
};

export default NotificationNews;
