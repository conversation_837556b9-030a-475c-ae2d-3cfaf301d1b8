import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// hooks
import { useBoolean } from '../../../hooks';
//  components
import NotFound from '../../../NotFound';
import IconButton from '../../../IconButton';
import CustomPopper, { usePopover } from '../../../custom-popover';
import NotificationMaintenance from './NotificationMaintenance';
import NotificationTasks from './NotificationTasks';
import NotificationNews from './NotificationNews';
import ModalNews from '../../../Modals/ModalNews';
// types
import { NewsPostTypeGET } from '../../../sections/administration/news-management/types';
import useNotificationNews from './use-notification-news';
import LoggedUserStore from '../../../../flux/loggedUser.store';
import LoggedUserActions from '../../../../flux/loggedUser.actions';

const NotificationPopover = () => {
    const [userState, setUserState] = useState(LoggedUserStore.getState());
    useEffect(() => {
        LoggedUserStore.listen(setUserState);
        return () => {
            LoggedUserStore.unlisten(setUserState);
        };
    }, []);

    const { t } = useTranslation();

    const {
        bellNewsPosts,
        fetchBellNewsPosts,
        removeBellNewsPost,
    } = useNotificationNews();

    const [openedPost, setOpenedPost] = useState<NewsPostTypeGET | null>(null);

    const popover = usePopover();
    const modalNews = useBoolean(false);

    const bellNotifsCount = (
        userState.bellTasksCount
        + userState.bellNotificationsCount
        + bellNewsPosts.length
    );

    // const removeTaskFromList = (taskId: string) => {
    //     LoggedUserActions.removeTask(taskId);
    // };

    const onOpenNotificationPopover = (
        e: React.MouseEvent<HTMLElement, MouseEvent>,
    ) => {
        popover.onOpen(e);
        // if (userState.bellTasksCount > 0) {
        //     setTimeout(() => {
        //         LoggedUserActions.setBellTasksCount(0);
        //     });
        // }
    };

    const handleNewsPostOpen = (post: NewsPostTypeGET) => {
        setOpenedPost(post);
        modalNews.onTrue();

        removeBellNewsPost(post);
    };

    useEffect(() => {
        fetchBellNewsPosts();
    }, []);

    return (
        <>
            <IconButton
                icon={popover.open ? 'bell-filled' : 'bell'}
                color="default"
                size="medium"
                badge={bellNotifsCount > 0}
                onClick={(e) => onOpenNotificationPopover(e)}
                sx={{
                    ...(popover.open && {
                        color: (theme) => theme.palette.primary.main,
                    }),
                }}
            />
            <CustomPopper
                open={popover.open}
                onClose={popover.onClose}
                sx={{ width: 300, p: 0 }}
            >
                {userState.bellNotificationsCount !== 0 && (
                    <NotificationMaintenance
                        bellNotifications={userState.bellNotifications}
                    />
                )}
                {bellNewsPosts && bellNewsPosts.length > 0 && (
                    <NotificationNews
                        posts={bellNewsPosts}
                        onPostOpen={handleNewsPostOpen}
                        closePopover={popover.onClose}
                    />
                )}
                {/* userState.bellTasks.length !== 0 && (
                    <NotificationTasks
                        bellTasks={userState.bellTasks}
                        removeFromList={removeTaskFromList}
                        closePopover={popover.onClose}
                    />
                ) */}
                {(!bellNewsPosts || bellNewsPosts.length === 0) && (
                    <NotFound text={t('noNews')} />
                )}
            </CustomPopper>
            {modalNews.value && openedPost && (
                <ModalNews
                    isOpen={modalNews.value}
                    onClose={() => {
                        modalNews.onFalse();
                        setOpenedPost(null);
                    }}
                    posts={[openedPost]}
                />
            )}
        </>
    );
};

export default NotificationPopover;
