import * as React from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import LinearProgress, {
    LinearProgressProps,
} from '@mui/material/LinearProgress';
import MuiAlert, { AlertProps } from '@mui/material/Alert';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
// components
import IconButton from '../IconButton';
// hooks
import { useCopyToClipboard } from '../hooks/use-copy-to-clipboard';

import AlertsActions from '../../components/alerts/alerts.actions';

const LinearProgressWithLabel = (
    props: LinearProgressProps & { value: number },
) => {
    return (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress
                    color="inherit"
                    variant="determinate"
                    sx={{ height: 7, borderRadius: 5, opacity: 0.7 }}
                    {...props}
                />
            </Box>
            <Box sx={{ minWidth: 35 }}>
                <Typography variant="body2" color="inherit">
                    {`${Math.round(props.value)}%`}
                </Typography>
            </Box>
        </Box>
    );
};

const Alert = React.forwardRef<HTMLDivElement, AlertProps>((props, ref) => {
    return <MuiAlert elevation={3} ref={ref} {...props} />;
});

type Props = {
    id: string;
    type?: 'info' | 'error' | 'success' | 'warning';
    message: string | object;
    serverError?: any;
    defaultOnClickAction?: Function;
    defaultActionMessage?: string;
    reload?: boolean;
    fadeOut?: boolean;
    hasSpinner?: boolean;
    duration?: number;
    allowCountdown?: boolean;
    progress?: any;
    show?: boolean;
    onClose?: Function;
};

const Alerts = ({
    id,
    type,
    message,
    serverError,
    defaultOnClickAction,
    defaultActionMessage,
    reload,
    fadeOut,
    hasSpinner = false,
    duration = 5,
    allowCountdown = false,
    progress = 0,
    show = false,
    onClose,
}: Props) => {
    const [progressValue, setProgressValue] = React.useState(0);
    const [timeProgress, setTimeProgress] = React.useState(100);
    const [actionMessage, setActionMessage] = React.useState('');
    const [copyServerError, setCopyServerError] = React.useState('');
    const { copy } = useCopyToClipboard();
    const { t, i18n } = useTranslation();

    React.useEffect(() => {
        setProgressValue(progress);
        return () => {
            setProgressValue(0);
        };
    }, [progress]);

    React.useEffect(() => {
        setCopyServerError(finalServerError);
        return () => {
            setCopyServerError('');
        };
    }, [serverError]);

    function defaultAction() {
        defaultOnClickAction?.();
    }

    // -------------------------------------copy to clickboard-----------------------------------

    const onCopy = React.useCallback(
        (event: React.MouseEvent<HTMLElement, MouseEvent>, text: string) => {
            // prevent from bubbling up to div element with another onClick function
            event?.stopPropagation?.();

            if (text) {
                copy(text);
            }
        },
        [copy],
    );

    // -------------------------------------server errors-----------------------------------
    // This block of code extracts the server error message, error object, and code name from the server error response.
    // If there is no error object, it sets the server message and whole error to the server error.
    // The final server error message is set to the server message if there is a server error, otherwise it is set to the message prop.

    let serverMessage = '';
    let wholeError = '';
    let codeName = '';

    if (serverError) {
        if (serverError.body || serverError.error) {
            const errorObject = serverError.body
                ? serverError.body.error
                : serverError.error;
            serverMessage = errorObject?.languages
                ? errorObject?.languages[i18n.language]
                : errorObject.message;
            wholeError = JSON.stringify(errorObject);
            codeName = errorObject.codeName;
        } else {
            serverMessage = serverError;
            wholeError = serverError;
        }
    }

    const serverErrorMessage = serverError ? serverMessage.toString() : '';
    const finalServerError = serverError ? wholeError : message?.toString() || '';

    // -----------------------------------default action meassage--------------------------------------

    /* if (defaultActionMessage) {
        setActionMessage(defaultActionMessage);
    } else if (reload) {
        setActionMessage(t('clickToRepeat') as string);
    } */

    // -----------------------------------set timer--------------------------------------

    React.useEffect(() => {
        setTimeProgress(100);
        // Calculate the progress increment per millisecond
        const progressIncrement = 100 / (duration * 10); // 100% over 6000 milliseconds (6 seconds)
        let timer: any;
        // Start a timer to update progress
        if (show && allowCountdown) {
            // setAutoHideDuration(duration * 1000);
            timer = setInterval(() => {
                setTimeProgress((oldProgress) => {
                    const newProgress = oldProgress - progressIncrement;
                    if (newProgress <= 0) {
                        removeAlert();
                        // If progress reaches 100%, close the Snackbar and clear the timer
                        return 0;
                    }
                    return newProgress;
                });
            }, 100);
        } // Update every 100 milliseconds

        return () => {
            clearInterval(timer);
        };
    }, []);

    // ------------------------------------remove alert--------------------------------

    const removeAlert = (event?: React.MouseEvent<HTMLElement, MouseEvent>) => {
        // prevent from bubbling up to div element with another onClick function
        event?.stopPropagation?.();

        AlertsActions.removeAlert(id);
        onClose?.(); // Call onClose if it exists (ES6+)
    };

    // const handleClose = (
    //     event?: React.SyntheticEvent | Event,
    //     reason?: string
    // ) => {
    //     if (reason === "clickaway") {
    //         return;
    //     }
    //     removeAlert();
    // };

    // ------------------refreshPage------------------------

    function refreshPage(e: any) {
        if (e.stopPropagation) e.stopPropagation(); // prevent from bubbling up to div element with another onClick function
        location.reload(); // not from cache
    }

    // ------------------action buttons on the end of alert------------------------

    const action = (
        <Box display="flex" alignItems="center" height={1}>
            {allowCountdown && (
                <CircularProgress
                    color="inherit"
                    variant="determinate"
                    value={timeProgress}
                    size={20}
                    sx={{ mr: 1 }}
                />
            )}
            {serverErrorMessage.indexOf('Minified exception occured') !==
                -1 && (
                <Tooltip
                    arrow
                    title={t('clickToRepeat')}
                    PopperProps={{ style: { zIndex: 2000000003 } }}
                >
                    <IconButton
                        icon="reload"
                        size="medium"
                        aria-label={t('clickToRepeat')}
                        color="inherit"
                        onClick={(e) => refreshPage(e)}
                    />
                </Tooltip>
            )}
            {serverError && (
                <Tooltip
                    arrow
                    title={t('copyToClipboard')}
                    PopperProps={{ style: { zIndex: 2000000003 } }}
                >
                    <IconButton
                        icon="duplicate"
                        size="medium"
                        aria-label={t('copyToClipboard')}
                        color="inherit"
                        onClick={(event) => onCopy(event, copyServerError)}
                    />
                </Tooltip>
            )}

            <IconButton
                icon="close"
                aria-label={t('close')}
                color="inherit"
                size="medium"
                onClick={removeAlert}
            />
        </Box>
    );

    //-----------------------------------------------------------------------------

    const messageAsString =
        typeof message === 'string' ? message : JSON.stringify(message);
    return (
        // <Snackbar
        //     id={"main-alert" + id}
        //     open={show}
        //     autoHideDuration={autoHideDuration}
        //     onClose={removeAlert}
        //     anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        //     TransitionComponent={Slide}
        // >
        <Box width={1} display="flex" justifyContent="center" mb={1}>
            <Alert
                id={`main-alert${id}`}
                severity={type || 'info'}
                action={action}
                onClick={() => defaultAction}
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                }}
            >
                <Box display="flex" alignItems="center">
                    <Box component="div" />
                    <Box
                        display="flex"
                        flexDirection="column"
                        justifyContent="center"
                        alignItems="flex-start"
                    >
                        <p style={{ padding: 0, margin: 0 }}>
                            {serverErrorMessage}
                        </p>
                        <p style={{ padding: 0, margin: 0 }}>
                            {messageAsString}
                        </p>
                    </Box>
                    {/*  Progress bar */}
                    {progressValue !== 0 && (
                        <Box width="18.75rem" ml={1} mt="0.125rem">
                            <LinearProgressWithLabel
                                variant="determinate"
                                value={progressValue}
                            />
                        </Box>
                    )}
                </Box>
            </Alert>
        </Box>
        // </Snackbar>
    );
};

export default Alerts;
