import { useEffect, useRef } from 'react';
import dragula from 'react-dragula';
import { Stack } from '@mui/material';
import { cloneDeep } from 'lodash';
// hooks
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import useAutoscroll from '../../../../../hooks/use-autoscroll';
// utils
import { DYNAMIC_CONDITIONS_BUILDER_DRAGULA_CONSTS } from '../../../utils';
import { isMobile } from '../../../../../../common/utils';
// components
import MyIcon from '../../../../../MyIcon';
import Button from '../../../../../Button';
import SectionsCard from '../../../../../SectionsCard';
import DynamicConditionsBuilderStructuredBlockValidationCondition from './DynamicConditionsBuilderStructuredBlockValidationCondition';
import DynamicConditionsBuilderBlockHeading from '../DynamicConditionsBuilderBlockHeading';
import DynamicConditionsBuilderStructuredBlockConditionGroup from './DynamicConditionsBuilderStructuredBlockConditionGroup';
import DynamicConditionsBuilderStructuredBlockWatchVars from './DynamicConditionsBuilderStructuredBlockWatchVars';
import PersistentHelpTooltip from '../../../../../PersistentHelpTooltip';
// types
import { ModalDynamicConditionsFormType } from '../../../types';
import { DynamicConditionsBuilderHelp } from '../../../../modal-help/dynamic-conditions-builder/types';

const DynamicConditionsBuilderStructuredBlock = ({
    blockIndex,
    templateVariables,
    onRemove,
    onConvert,
    helpMode = false,
    helpExampleInfoTranslations,
}: {
    blockIndex: number;
    templateVariables: any[];
    onRemove: () => void;
    onConvert: () => void;
    helpMode?: boolean;
    helpExampleInfoTranslations?: DynamicConditionsBuilderHelp['exampleInfoTranslations'];
}) => {
    const { t } = useTranslation();

    const { getValues } = useFormContext<ModalDynamicConditionsFormType>();

    const block = useWatch<
        ModalDynamicConditionsFormType,
        `builderBlocks.${number}`
    >({ name: `builderBlocks.${blockIndex}` });

    const blockFunction = block.function;

    const conditionGroupFieldArray = useFieldArray<ModalDynamicConditionsFormType, `builderBlocks.${number}.conditionGroups`>({
        name: `builderBlocks.${blockIndex}.conditionGroups`,
    });

    const {
        append: appendConditionGroup,
        remove: removeConditionGroup,
    } = conditionGroupFieldArray;

    const {
        startListening,
        stopListening,
        stopAutoScroll,
    } = useAutoscroll({
        scrollSpeed: 10,
        scrollStartOffset: 100,
        pointerMoveElement: window,
    });

    const conditionFieldArrayRefs = useRef<
        ReturnType<typeof useFieldArray<ModalDynamicConditionsFormType, `builderBlocks.${number}.conditionGroups.${number}.conditions`>>[]
    >([]);

    const drake = useRef<dragula.Drake | null>(null);

    const isMobileDevice = useRef(
        isMobile(navigator.userAgent || navigator.vendor || (window as any).opera),
    );

    const blockIndexRef = useRef(blockIndex);
    const conditionGroupFieldArrayRef = useRef(conditionGroupFieldArray);

    useEffect(() => {
        blockIndexRef.current = blockIndex;
        conditionGroupFieldArrayRef.current = conditionGroupFieldArray;
    }, [blockIndex, conditionGroupFieldArray]);

    useEffect(() => {
        if (isMobileDevice.current || helpMode) {
            return () => {};
        }

        const dialogContent = document.getElementsByClassName(
            DYNAMIC_CONDITIONS_BUILDER_DRAGULA_CONSTS.BLOCKS_AUTOSCROLL_CONTAINER_CLASS,
        )[0] as HTMLElement;

        const containers = conditionGroupFieldArrayRef.current.fields.map((conditionGroup) => (
            document.getElementById(conditionGroup.id) as HTMLElement
        ));

        const getConditionGroupIndexByItsId = (conditionGroupId: string) => {
            return conditionGroupFieldArrayRef.current.fields.findIndex((conditionGroup) => conditionGroup.id === conditionGroupId);
        };

        drake.current = dragula(containers, {
            moves: (element, container, handle) => {
                let foundHandle = false;

                // if the handle is within an excluded element, don't allow dragging
                for (
                    let e = handle;
                    e !== element && e !== container;
                    e = e?.parentElement || undefined
                ) {
                    if (e?.classList.contains(DYNAMIC_CONDITIONS_BUILDER_DRAGULA_CONSTS.CONDITION_DRAG_HANDLE_CLASS)) {
                        foundHandle = true;
                    }
                }

                return foundHandle;
            },
            removeOnSpill: false,
            revertOnSpill: true,
        });

        let draggedConditionIndex: number | null = null;
        let draggedConditionGroupIndex: number | null = null;

        drake.current.on('drag', (element, source) => {
            draggedConditionIndex = Array.prototype.indexOf.call(element.parentNode?.children, element);

            const draggedConditionGroupId = source.getAttribute('id');
            draggedConditionGroupIndex = draggedConditionGroupId ? getConditionGroupIndexByItsId(draggedConditionGroupId) : null;

            startListening(dialogContent);
        });

        drake.current.on('drop', (element, target, source, sibling) => {
            const conditionDropIndex = Array.prototype.indexOf.call(element.parentNode?.children, element);
            const conditionGroupDropId = target.getAttribute('id');
            const conditionGroupDropIndex = conditionGroupDropId ? getConditionGroupIndexByItsId(conditionGroupDropId) : null;

            drake.current?.cancel(); // Cancel drag event so dragula thinks the container was reverted

            if (
                draggedConditionIndex === null
                || draggedConditionGroupIndex === null
                || conditionGroupDropIndex === null
            ) {
                return;
            }

            const sourceConditionGroupConditions = getValues(
                `builderBlocks.${blockIndexRef.current}.conditionGroups.${draggedConditionGroupIndex}.conditions`,
            );

            const condition = cloneDeep(sourceConditionGroupConditions[draggedConditionIndex]);

            if (draggedConditionGroupIndex === conditionGroupDropIndex) {
                conditionFieldArrayRefs.current[draggedConditionGroupIndex].move(draggedConditionIndex, conditionDropIndex);
                return;
            }

            const shouldRemoveSourceConditionGroup = sourceConditionGroupConditions.length === 1;

            conditionFieldArrayRefs.current[draggedConditionGroupIndex].remove(draggedConditionIndex);
            conditionFieldArrayRefs.current[conditionGroupDropIndex].insert(conditionDropIndex, condition);

            if (shouldRemoveSourceConditionGroup) {
                conditionGroupFieldArrayRef.current.remove(draggedConditionGroupIndex);
            }
        });

        drake.current.on('cancel', () => {
            stopListening(dialogContent);
            stopAutoScroll();
        });

        return () => {
            if (drake.current) {
                drake.current.destroy();
            }
        };
    }, []);

    useEffect(() => {
        if (drake.current) {
            drake.current.containers = conditionGroupFieldArray.fields.map((conditionGroup) => (
                document.getElementById(conditionGroup.id) as HTMLElement
            ));
        }
    }, [conditionGroupFieldArray.fields.length]);

    return (
        <SectionsCard
            sx={{
                '&.gu-mirror, & .gu-mirror': {
                    cursor: 'grabbing',
                    [`& .${DYNAMIC_CONDITIONS_BUILDER_DRAGULA_CONSTS.BLOCK_DRAG_HANDLE_CLASS}`]: {
                        cursor: 'grabbing',
                    },
                    [`& .${DYNAMIC_CONDITIONS_BUILDER_DRAGULA_CONSTS.CONDITION_DRAG_HANDLE_CLASS}`]: {
                        cursor: 'grabbing',
                    },
                },
            }}
        >
            <Stack spacing={2}>
                <PersistentHelpTooltip
                    open={helpMode}
                    title={helpExampleInfoTranslations?.whatShouldHappen}
                    placement="top"
                    xOffset="11.25%"
                    yOffset="108.75%"
                    sx={{
                        zIndex: 1301,
                        width: '100%',
                    }}
                >
                    <DynamicConditionsBuilderBlockHeading
                        blockIndex={blockIndex}
                        templateVariables={templateVariables}
                        onRemoveBlock={onRemove}
                        onConvertBlock={onConvert}
                    />
                </PersistentHelpTooltip>
                <PersistentHelpTooltip
                    open={helpMode}
                    title={helpExampleInfoTranslations?.onWhatConditionsShouldItHappen}
                    placement="top"
                    xOffset="55%"
                    yOffset={
                        blockFunction === 'validate'
                            ? 'calc(100% + 0.5rem)'
                            : 'calc(100% - 0.25rem)'
                    }
                    sx={{
                        zIndex: 1301,
                        width: '100%',
                    }}
                >
                    {blockFunction === 'validate' ? (
                        <DynamicConditionsBuilderStructuredBlockValidationCondition
                            blockIndex={blockIndex}
                        />
                    ) : (
                        <Stack
                            spacing={1}
                            sx={{
                                p: 1,
                                border: '1px dashed',
                                borderColor: 'text.secondary',
                                borderRadius: 1,
                            }}
                        >
                            {conditionGroupFieldArray.fields.map((conditionGroup, conditionGroupIndex) => (
                                <DynamicConditionsBuilderStructuredBlockConditionGroup
                                    key={conditionGroup.id}
                                    dragulaWrapperId={conditionGroup.id}
                                    blockIndex={blockIndex}
                                    conditionGroupIndex={conditionGroupIndex}
                                    templateVariables={templateVariables}
                                    conditionFieldArrayRefs={conditionFieldArrayRefs}
                                    onRemove={removeConditionGroup}
                                />
                            ))}
                            <Stack direction="row" spacing={2}>
                                <PersistentHelpTooltip
                                    open={helpMode}
                                    title={helpExampleInfoTranslations?.addAnotherCondition}
                                    placement="bottom"
                                    xOffset="62.5%"
                                    yOffset="95%"
                                    sx={{ zIndex: 1301 }}
                                >
                                    <Button
                                        variant="text"
                                        color="secondary"
                                        size="small"
                                        label={t('addCondition')}
                                        startIcon={<MyIcon icon="add" />}
                                        onClick={() => (
                                            appendConditionGroup({
                                                conditions: [
                                                    {
                                                        tvar_id: null,
                                                        operator: null,
                                                        value: null,
                                                    },
                                                ],
                                                internalJunction: 'and',
                                            })
                                        )}
                                    />
                                </PersistentHelpTooltip>
                            </Stack>
                        </Stack>
                    )}
                </PersistentHelpTooltip>
                <DynamicConditionsBuilderStructuredBlockWatchVars
                    blockIndex={blockIndex}
                    templateVariables={templateVariables}
                />
            </Stack>
        </SectionsCard>
    );
};

export default DynamicConditionsBuilderStructuredBlock;
