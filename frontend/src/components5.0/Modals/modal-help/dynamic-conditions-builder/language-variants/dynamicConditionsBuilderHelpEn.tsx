/* eslint-disable max-len */
/* eslint-disable react/jsx-one-expression-per-line */

import { DynamicConditionsBuilderHelp } from '../types';

const dynamicConditionsBuilderHelpEn: DynamicConditionsBuilderHelp = {
    commonSectionName: 'General',
    commonSectionDescription: (
        `
            <section>
                <h1>
                    The Builder in Team Assistant is used to define the dynamic behavior of variables in a task, which can adapt based on the conditions you define.
                </h1>
            </section>
            <section>
                <h2>
                    This tool allows you to easily manage variable visibility, change their properties, or validate data based on predefined rules.
                </h2>
            </section>
            <section>
                <h2>
                    Each function can be combined with conditions. Conditions allow you to control when specific actions are executed, thereby tailoring form behavior to specific situations.
                </h2>
            </section>
        `
    ),
    useInBuilderButtonTranslation: 'Use in Builder',
    exampleInfoTranslations: {
        whatShouldHappen: 'What should happen',
        onWhatConditionsShouldItHappen: 'On what conditions should it happen',
        addAnotherCondition: 'Add another condition',
    },
    functions: [
        {
            builderBlockFunction: 'hide',
            nameTranslation: 'Hide',
            description: (
                `
                    <section>
                        <h1>
                            The condition-setting function allows you to automatically hide certain variables based on rules you define yourself. This provides flexibility in configuring forms and customizing displayed variables according to current needs.
                        </h1>
                    </section>
                    <section>
                        <h2>How it works</h2>
                        <ul>
                            <li>
                                <b>Variable:</b> A form element that can dynamically hide based on the rules you set.
                            </li>
                            <li>
                                <b>Condition:</b> A rule that determines when the variable is hidden. The condition consists of another variable and its value.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Example</h2>
                        <p>
                            If you want to hide the variable <b>“Select multiple orders”</b> when the variable <b>“More than one order”</b> is set to <b>“Yes”</b>, follow these steps:
                        </p>
                        <ul>
                            <li>
                                <b>Select the variable:</b> Specify which variable you want to hide. In our example, this will be the variable <b>“Select multiple orders”</b>.
                            </li>
                            <li>
                                <b>Define the condition:</b> Set the rule for hiding. Choose the variable <b>“More than one order”</b>, then the condition <b>“equals”</b>, and the value <b>“Yes”</b>.
                            </li>
                            <li>
                                <b>Save the rule:</b> Once the condition is set, the variable <b>“Select multiple orders”</b> will be hidden whenever the value of <b>“More than one order”</b> is <b>“Yes”</b>.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Tips</h2>
                        <ul>
                            <li>
                                You can set multiple conditions for one variable.
                            </li>
                            <li>
                                Combine conditions for greater flexibility and control over what users see at any given moment.
                            </li>
                            <li>
                                You can edit or delete conditions at any time.
                            </li>
                        </ul>
                        <p>
                            This feature helps you efficiently customize forms according to user preferences or specific situations.
                        </p>
                    </section>
                `
            ),
            exampleTemplateVariables: [
                {
                    id: 1,
                    tvar_name: 'Full order price including VAT',
                    tvar_type: 'N',
                },
                {
                    id: 2,
                    tvar_name: 'Order number selection',
                    tvar_type: 'LN',
                },
            ],
            exampleData: {
                builderBlocks: [{
                    function: 'hide',
                    tvar_id: 1,
                    conditionGroups: [
                        {
                            conditions: [
                                {
                                    tvar_id: 2,
                                    operator: 'isn',
                                    value: null,
                                },
                            ],
                            internalJunction: 'and',
                        },
                    ],
                    globalJunction: 'and',
                    changeToValue: null,
                    script: null,
                    validationCondition: null,
                    watchVars: [],
                    useWatchVars: false,
                }],
                script: '',
            },
        },
        /* TODO: add description, exampleTemplateVariables, exampleData & then uncomment
        {
            builderBlockFunction: 'read',
            nameTranslation: 'Read',
            description: ``,
        },
        {
            builderBlockFunction: 'write',
            nameTranslation: 'Read and Write',
            description: ``,
        },
        {
            builderBlockFunction: 'must',
            nameTranslation: 'Mandatory Variable',
            description: ``,
        },
        {
            builderBlockFunction: 'solve',
            nameTranslation: 'Allow Completion on Change',
            description: ``,
        },
        */
        {
            builderBlockFunction: 'change',
            nameTranslation: 'Change',
            description: (
                `
                    <section>
                        <h1>
                            The content change function allows you to automatically change the value of a specific variable based on rules you define yourself. This function provides flexibility for dynamic form adjustments and automation of certain values, streamlining user workflows.
                        </h1>
                    </section>
                    <section>
                        <h2>How it works</h2>
                        <ul>
                            <li>
                                <b>Variable:</b> A form element whose content (value) can dynamically change based on set conditions.
                            </li>
                            <li>
                                <b>Condition:</b> A rule that determines when and to what value the variable's content changes. The condition consists of another variable and its value.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Example</h2>
                        <p>
                            If you want the value of the variable <b>“Priority”</b> to automatically set to <b>“High”</b> when the variable <b>“Request Type”</b> is set to <b>“Emergency”</b>, follow these steps:
                        </p>
                        <ul>
                            <li>
                                <b>Select the variable:</b> Specify which variable you want to change. In our example, this will be the variable <b>“Priority”</b>.
                            </li>
                            <li>
                                <b>Define the condition:</b> Set the rule for the change. Choose the variable <b>“Request Type”</b>, select the condition <b>“equals”</b>, and the value <b>“Emergency”</b>.
                            </li>
                            <li>
                                <b>Set the new value:</b> Specify the new value for the variable <b>“Priority”</b> that you want to set when the condition is met – in this case, <b>“High”</b>.
                            </li>
                            <li>
                                <b>Save the rule:</b> After setting the condition, the value of the variable <b>“Priority”</b> will automatically change to <b>“High”</b> whenever the value of <b>“Request Type”</b> is <b>“Emergency”</b>.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Tips</h2>
                        <ul>
                            <li>
                                You can set multiple rules for one variable if you want to change the value in different situations.
                            </li>
                            <li>
                                Conditions can be edited or removed at any time, allowing you to adapt to changes in business processes.
                            </li>
                            <li>
                                Using this function can help prevent errors and automate parts of forms.
                            </li>
                        </ul>
                        <p>
                            This feature allows you to automatically set variable values based on predefined rules, increasing user efficiency and ensuring data consistency.
                        </p>
                    </section>
                `
            ),
            exampleTemplateVariables: [
                {
                    id: 1,
                    tvar_name: 'Payment method',
                    tvar_type: 'LT',
                    tvar_lov: [
                        {
                            title: 'Cash',
                            value: 'cash',
                        },
                    ],
                },
                {
                    id: 2,
                    tvar_name: 'Supplier',
                    tvar_type: 'LT',
                    tvar_lov: [
                        {
                            title: 'Marian Kadlec',
                            value: 'marian_kadlec',
                        },
                    ],
                },
            ],
            exampleData: {
                builderBlocks: [{
                    function: 'change',
                    tvar_id: 1,
                    conditionGroups: [
                        {
                            conditions: [
                                {
                                    tvar_id: 2,
                                    operator: 'eq',
                                    value: 'marian_kadlec',
                                },
                            ],
                            internalJunction: 'and',
                        },
                    ],
                    globalJunction: 'and',
                    changeToValue: 'cash',
                    script: null,
                    validationCondition: null,
                    watchVars: [],
                    useWatchVars: false,
                }],
                script: '',
            },
        },
        {
            builderBlockFunction: 'validate',
            nameTranslation: 'Validate',
            description: (
                `
                    <section>
                        <h1>
                            The validation function allows you to verify that variable values are correct and meet established criteria, ensuring the quality and accuracy of entered data. The builder offers various preset validation options that you can easily use to set rules for checking different types of inputs.
                        </h1>
                    </section>
                    <section>
                        <h2>Preset validation options:</h2>
                    </section>
                    <section>
                        <h2>Email Address</h2>
                        <p>
                            Verifies that the variable value contains the correct email format (i.e., must include the characters “@” and “.”).
                        </p>
                        <ul>
                            <li>
                                <b>Example:</b> Set validation on the variable <b>“Contact Email”</b> to ensure the value is a valid email address. If the entered text does not meet the email format requirements, the user will be prompted to correct it.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Phone Number</h2>
                        <p>
                            Verifies that the value contains a valid phone number format. Typically ensures that the variable contains only digits or specific formatting required for phone numbers.
                        </p>
                        <ul>
                            <li>
                                <b>Example:</b> The variable <b>“Contact Phone”</b> must have a phone number format (e.g., 123-456-789). If the format is incorrect, the system will notify the user of the error.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Number</h2>
                        <p>
                            Ensures that the variable value contains only digits, which is useful for verifying values such as quantities, prices, or other numerical data.
                        </p>
                        <ul>
                            <li>
                                <b>Example:</b> The variable <b>“Number of Items”</b> must be a number. If the user enters text or symbols, the system will prompt them to correct it.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Date</h2>
                        <p>
                            Verifies that the variable contains a valid date, usually in the format DD/MM/YYYY or a similar standard.
                        </p>
                        <ul>
                            <li>
                                <b>Example:</b> The variable <b>“Date of Birth”</b> must contain a valid date. If the format is incorrect, the user will be prompted to enter the value again.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Text Length</h2>
                        <p>
                            Checks whether the entered text is within the specified minimum and/or maximum number of characters. This validation can be used, for example, to ensure that the user has entered enough information but has not exceeded the required text length.
                        </p>
                        <ul>
                            <li>
                                <b>Example:</b> The variable <b>“Project Description”</b> should contain at least 10 and at most 200 characters.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>How to set validation:</h2>
                        <ul>
                            <li>
                                <b>Select the variable:</b> Specify which variable you want to validate.
                            </li>
                            <li>
                                <b>Choose the validation type:</b> Select one of the preset validation types (e.g., email, phone number, date).
                            </li>
                            <li>
                                <b>Save the rule:</b> Once you set the validation, the system will automatically check the variable value during input and notify the user in case of incorrect format.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Tips</h2>
                        <ul>
                            <li>
                                Set validation for all important inputs to prevent incorrect or incomplete data.
                            </li>
                            <li>
                                Combine multiple validation types for more thorough input data checks.
                            </li>
                            <li>
                                Validation can be edited at any time to meet specific requirements.
                            </li>
                        </ul>
                        <p>
                            The validation function ensures that forms are more accurate, helping to prevent errors in data entry and ensuring consistency and quality.
                        </p>
                    </section>
                `
            ),
            exampleTemplateVariables: [
                {
                    id: 1,
                    tvar_name: 'ID',
                    tvar_type: 'T',
                },
            ],
            exampleData: {
                builderBlocks: [{
                    function: 'validate',
                    tvar_id: 1,
                    conditionGroups: null,
                    globalJunction: null,
                    changeToValue: null,
                    script: null,
                    validationCondition: {
                        operator: 'stringLength',
                        value: 8,
                    },
                    watchVars: [],
                    useWatchVars: false,
                }],
                script: '',
            },
        },
    ],
};

export default dynamicConditionsBuilderHelpEn;
