/* eslint-disable max-len */
/* eslint-disable react/jsx-one-expression-per-line */

import { DynamicConditionsBuilderHelp } from '../types';

const dynamicConditionsBuilderHelpCs: DynamicConditionsBuilderHelp = {
    commonSectionName: 'Obecné',
    commonSectionDescription: (
        `
            <section>
                <h1>
                    Builder v Team assistant slou<PERSON><PERSON> k definici dynamického chování proměnných na úkolu, které se mohou přizpůsobovat na základě podmínek, které definujete.
                </h1>
            </section>
            <section>
                <h2>
                    Tento nástroj vám umožní jednoduše spravovat zobrazení proměnných, měnit jejich vlastnosti nebo ověřovat data na základě předem stanovených pravidel.
                </h2>
            </section>
            <section>
                <h2>
                    Kaž<PERSON>u funkci můžete kombinovat s podmínkami. Podmínky umož<PERSON><PERSON><PERSON><PERSON>, kdy se mají jednotlivé akce pro<PERSON>, a tím přizpůsobit chování formuláře specifickým situacím.
                </h2>
            </section>
        `
    ),
    useInBuilderButtonTranslation: 'Použít v builderu',
    exampleInfoTranslations: {
        whatShouldHappen: 'Co se má stát',
        onWhatConditionsShouldItHappen: 'Za jakých podmínek se to stane',
        addAnotherCondition: 'Přidání další podmínky',
    },
    functions: [
        {
            builderBlockFunction: 'hide',
            nameTranslation: 'Skrýt',
            description: (
                `
                    <section>
                        <h1>
                            Funkce pro nastavování podmínek umožňuje automaticky skrývat určité proměnné na základě pravidel, která si sami nastavíte. To vám poskytne flexibilitu při konfiguraci formulářů a přizpůsobení zobrazených proměnných podle aktuálních potřeb.
                        </h1>
                    </section>
                    <section>
                        <h2>Jak to funguje</h2>
                        <ul>
                            <li>
                                <b>Proměnná:</b> Prvek formuláře, který se může dynamicky skrývat na základě vámi nastavených pravidel.
                            </li>
                            <li>
                                <b>Podmínka:</b> Pravidlo, které určuje, kdy se proměnná skryje. Podmínka se skládá z jiné proměnné a její hodnoty.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Příklad</h2>
                        <p>
                            Pokud chcete skrýt proměnnou <b>„Výběr více objednávek”</b> v případě, že proměnná <b>„Víc než jedna objednávka”</b> je nastavena na <b>„Ano”</b>, postupujte následovně:
                        </p>
                        <ul>
                            <li>
                                <b>Vyberte proměnnou:</b> Určete, kterou proměnnou chcete skrýt. V našem příkladu to bude proměnná <b>„Výběr více objednávek”</b>.
                            </li>
                            <li>
                                <b>Definujte podmínku:</b> Nastavte pravidlo pro skrytí. Zvolíte proměnnou <b>„Víc než jedna objednávka”</b>, následně podmínku <b>„rovná se”</b> a hodnotu <b>„Ano”</b>.
                            </li>
                            <li>
                                <b>Uložte pravidlo:</b> Jakmile je podmínka nastavena, proměnná <b>„Výběr více objednávek”</b> bude skryta vždy, když hodnota <b>„Víc než jedna objednávka”</b> bude <b>„Ano”</b>.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Tipy</h2>
                        <ul>
                            <li>
                                Můžete nastavit více podmínek pro jednu proměnnou.
                            </li>
                            <li>
                                Zkombinujte podmínky pro větší flexibilitu a kontrolu nad tím, co uživatelé v daný moment uvidí.
                            </li>
                            <li>
                                Kdykoli můžete podmínky upravit nebo smazat.
                            </li>
                        </ul>
                        <p>
                            Tato funkce vám pomůže přehledně a efektivně přizpůsobit formuláře dle uživatelských preferencí či konkrétních situací.
                        </p>
                    </section>
                `
            ),
            exampleTemplateVariables: [
                {
                    id: 1,
                    tvar_name: 'Celková cena objednávky bez DPH',
                    tvar_type: 'N',
                },
                {
                    id: 2,
                    tvar_name: 'Výběr čísla objednávky',
                    tvar_type: 'LN',
                },
            ],
            exampleData: {
                builderBlocks: [{
                    function: 'hide',
                    tvar_id: 1,
                    conditionGroups: [
                        {
                            conditions: [
                                {
                                    tvar_id: 2,
                                    operator: 'isn',
                                    value: null,
                                },
                            ],
                            internalJunction: 'and',
                        },
                    ],
                    globalJunction: 'and',
                    changeToValue: null,
                    script: null,
                    validationCondition: null,
                    watchVars: [],
                    useWatchVars: false,
                }],
                script: '',
            },
        },
        /* TODO: add descriptions & then uncomment
        {
            builderBlockFunction: 'read',
            nameTranslation: 'Pro čtení',
            description: ``,
        },
        {
            builderBlockFunction: 'write',
            nameTranslation: 'Pro čtení i zápis',
            description: ``,
        },
        {
            builderBlockFunction: 'must',
            nameTranslation: 'Povinná proměnná',
            description: ``,
        },
        {
            builderBlockFunction: 'solve',
            nameTranslation: 'Povolit splnění při změně',
            description: ``,
        },
        */
        {
            builderBlockFunction: 'change',
            nameTranslation: 'Změnit',
            description: (
                `
                    <section>
                        <h1>
                            Funkce pro nastavení změny obsahu proměnné umožňuje automaticky měnit hodnotu konkrétní proměnné na základě pravidel, která si sami nastavíte. Tato funkce vám poskytne flexibilitu pro dynamické úpravy formulářů a automatizaci některých hodnot, což zefektivní práci uživatelů.
                        </h1>
                    </section>
                    <section>
                        <h2>Jak to funguje</h2>
                        <ul>
                            <li>
                                <b>Proměnná:</b> Prvek formuláře, jehož obsah (hodnota) se může dynamicky měnit na základě nastavených podmínek.
                            </li>
                            <li>
                                <b>Podmínka:</b> Pravidlo, které určuje, kdy a na jakou hodnotu se obsah proměnné změní. Podmínka se skládá z jiné proměnné a její hodnoty.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Příklad</h2>
                        <p>
                            Pokud chcete, aby se hodnota proměnné <b>„Priorita”</b> automaticky nastavila na <b>„Vysoká“</b>, když je proměnná <b>„Typ žádosti”</b> nastavena na <b>„Nouzová“</b>, postupujte následovně:
                        </p>
                        <ul>
                            <li>
                                <b>Vyberte proměnnou:</b> Určete, kterou proměnnou chcete změnit. V našem příkladu to bude proměnná <b>„Priorita”</b>.
                            </li>
                            <li>
                                <b>Definujte podmínku:</b> Nastavte pravidlo pro změnu. Vyberete proměnnou <b>„Typ žádosti”</b>, zvolíte podmínku <b>„rovná se”</b> a hodnotu <b>„Nouzová“</b>.
                            </li>
                            <li>
                                <b>Nastavte novou hodnotu:</b> Uveďte novou hodnotu pro proměnnou <b>„Priorita”</b>, kterou chcete při splnění podmínky nastavit – v tomto případě <b>„Vysoká“</b>.
                            </li>
                            <li>
                                <b>Uložte pravidlo:</b> Po nastavení podmínky se hodnota proměnné <b>„Priorita”</b> automaticky změní na <b>„Vysoká“</b>, kdykoli bude hodnota <b>„Typ žádosti”</b> <b>„Nouzová“</b>.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Tipy</h2>
                        <ul>
                            <li>
                                Můžete nastavit několik pravidel pro jednu proměnnou, pokud chcete měnit hodnotu v různých situacích.
                            </li>
                            <li>
                                Podmínky lze kdykoli upravit nebo odstranit, což vám umožní reagovat na změny ve firemních procesech.
                            </li>
                            <li>
                                Používání této funkce vám může pomoci předcházet chybám a automatizovat některé části formulářů.
                            </li>
                        </ul>
                        <p>
                            Díky této funkci můžete automaticky nastavovat hodnoty proměnných podle předdefinovaných pravidel a zvýšit tak efektivitu práce uživatelů a konzistenci zadávaných dat.
                        </p>
                    </section>
                `
            ),
            exampleTemplateVariables: [
                {
                    id: 1,
                    tvar_name: 'Typ platby',
                    tvar_type: 'LT',
                    tvar_lov: [
                        {
                            title: 'Hotově',
                            value: 'cash',
                        },
                    ],
                },
                {
                    id: 2,
                    tvar_name: 'Dodavatel',
                    tvar_type: 'LT',
                    tvar_lov: [
                        {
                            title: 'Marian Kadlec',
                            value: 'marian_kadlec',
                        },
                    ],
                },
            ],
            exampleData: {
                builderBlocks: [{
                    function: 'change',
                    tvar_id: 1,
                    conditionGroups: [
                        {
                            conditions: [
                                {
                                    tvar_id: 2,
                                    operator: 'eq',
                                    value: 'marian_kadlec',
                                },
                            ],
                            internalJunction: 'and',
                        },
                    ],
                    globalJunction: 'and',
                    changeToValue: 'cash',
                    script: null,
                    validationCondition: null,
                    watchVars: [],
                    useWatchVars: false,
                }],
                script: '',
            },
        },
        {
            builderBlockFunction: 'validate',
            nameTranslation: 'Validovat',
            description: (
                `
                    <section>
                        <h1>
                            Funkce validace umožňuje ověřovat, zda jsou hodnoty v proměnných správné a odpovídají stanoveným kritériím, čímž zajišťujete kvalitu a přesnost zadávaných dat. Builder nabízí různé přednastavené možnosti validace, které můžete snadno použít k nastavení pravidel pro kontrolu různých typů vstupů.
                        </h1>
                    </section>
                    <section>
                        <h2>Přednastavené možnosti validace:</h2>
                    </section>
                    <section>
                        <h2>E-mailová adresa</h2>
                        <p>
                            Ověřuje, zda hodnota proměnné obsahuje správný formát e-mailu (tj. musí obsahovat znaky „@” a „.“).
                        </p>
                        <ul>
                            <li>
                                <b>Příklad:</b> Nastavte validaci na proměnnou <b>„Kontaktní e-mail”</b> tak, aby ověřila, že hodnota je správná e-mailová adresa. Pokud zadávaný text nesplňuje požadavky e-mailového formátu, uživatel bude vyzván k opravě.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Telefonní číslo</h2>
                        <p>
                            Ověřuje, zda hodnota obsahuje platný formát telefonního čísla. Obvykle zajišťuje, že proměnná obsahuje pouze číslice nebo specifické formátování, které je u telefonních čísel požadováno.
                        </p>
                        <ul>
                            <li>
                                <b>Příklad:</b> Proměnná <b>„Kontaktní telefon”</b> musí mít formát telefonního čísla (např. 123-456-789). Pokud je formát nesprávný, systém upozorní uživatele na chybu.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Číslo</h2>
                        <p>
                            Zajišťuje, že hodnota proměnné obsahuje pouze číslice, což je užitečné při ověřování hodnot, jako jsou počty, ceny nebo jiné číselné údaje.
                        </p>
                        <ul>
                            <li>
                                <b>Příklad:</b> Proměnná <b>„Počet kusů”</b> musí být číslo. Pokud uživatel zadá text nebo symboly, systém ho vyzve k opravě.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Datum</h2>
                        <p>
                            Ověřuje, že proměnná obsahuje platné datum, obvykle ve formátu DD/MM/RRRR nebo podobném standardu.
                        </p>
                        <ul>
                            <li>
                                <b>Příklad:</b> Proměnná <b>„Datum narození”</b> musí obsahovat platné datum. Při nesprávném formátu bude uživatel vyzván k zadání hodnoty znovu.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Délka textu</h2>
                        <p>
                            Kontroluje, zda je zadaný text v rámci stanoveného minimálního a/nebo maximálního počtu znaků. Tuto validaci můžete využít například pro kontrolu, že uživatel zadal dostatek informací, ale nepřekročil požadovanou délku textu.
                        </p>
                        <ul>
                            <li>
                                <b>Příklad:</b> Proměnná <b>„Popis projektu”</b> by měla obsahovat minimálně 10 a maximálně 200 znaků
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Jak nastavit validaci:</h2>
                        <ul>
                            <li>
                                <b>Vyberte proměnnou:</b> Určete, kterou proměnnou chcete validovat.
                            </li>
                            <li>
                                <b>Zvolte typ validace:</b> Vyberte jeden z přednastavených typů validace (např. e-mail, telefonní číslo, datum).
                            </li>
                            <li>
                                <b>Uložte pravidlo:</b> Jakmile nastavíte validaci, systém bude automaticky kontrolovat hodnotu proměnné při jejím vyplňování a v případě nesprávného formátu upozorní uživatele.
                            </li>
                        </ul>
                    </section>
                    <section>
                        <h2>Tipy</h2>
                        <ul>
                            <li>
                                Nastavte validaci pro všechny důležité vstupy, abyste předešli nesprávným nebo nekompletním údajům.
                            </li>
                            <li>
                                Kombinujte více typů validace pro důkladnější kontrolu vstupních dat.
                            </li>
                            <li>
                                Validaci můžete kdykoli upravit podle specifických požadavků.
                            </li>
                        </ul>
                        <p>
                            Díky funkci validace mohou být formuláře přesnější, což pomáhá předcházet chybám při zadávání dat a zajistit jejich konzistenci a kvalitu.
                        </p>
                    </section>
                `
            ),
            exampleTemplateVariables: [
                {
                    id: 1,
                    tvar_name: 'IČ',
                    tvar_type: 'T',
                },
            ],
            exampleData: {
                builderBlocks: [{
                    function: 'validate',
                    tvar_id: 1,
                    conditionGroups: null,
                    globalJunction: null,
                    changeToValue: null,
                    script: null,
                    validationCondition: {
                        operator: 'stringLength',
                        value: 8,
                    },
                    watchVars: [],
                    useWatchVars: false,
                }],
                script: '',
            },
        },
    ],
};

export default dynamicConditionsBuilderHelpCs;
