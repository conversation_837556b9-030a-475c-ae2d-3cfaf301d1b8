import { ModalDynamicConditionsFormType, ModalDynamicConditionsFormTypeSubmittable } from '../../modal-dynamic-conditions/types';

export type DynamicConditionsBuilderHelpFunction = {
    builderBlockFunction: ModalDynamicConditionsFormType['builderBlocks'][0]['function'];
    nameTranslation: string;
    description: string;
    exampleData: ModalDynamicConditionsFormTypeSubmittable;
    exampleTemplateVariables: any[];
};

export type DynamicConditionsBuilderHelp = {
    commonSectionName: string;
    commonSectionDescription: string;
    useInBuilderButtonTranslation: string;
    exampleInfoTranslations: {
        whatShouldHappen: string;
        onWhatConditionsShouldItHappen: string;
        addAnotherCondition: string;
    };
    functions: DynamicConditionsBuilderHelpFunction[];
};
