// mui
import { Box, Stack, useTheme } from '@mui/material';
// hooks
import { useForm } from 'react-hook-form';
// utils
import { pxToRem } from '../../../../theme/typography';
// components
import ModalHelpSectionCard from '../ModalHelpSectionCard';
import { DynCondBuilderHelpGeneralRightIllustration } from '../../../illustrations/Illustrations';
import DynamicConditionsBuilderStructuredBlock from '../../modal-dynamic-conditions/tabs/dynamic-conditions-builder-tab/structured-block/DynamicConditionsBuilderStructuredBlock';
import FormProvider from '../../../form/FormProvider';
// types
import { ModalDynamicConditionsFormTypeSubmittable } from '../../modal-dynamic-conditions/types';
import { DynamicConditionsBuilderHelp } from './types';

type Props = {
    name: string;
    text: string;
    /**
     * If true, it will have `display: none` style.
     * It's recommended to use this prop instead of conditional rendering
     * (the builder blocks within the function sections are costly to rerender).
     * */
    hidden?: boolean;
} & (
    {
        type: 'function';
        exampleData: ModalDynamicConditionsFormTypeSubmittable;
        exampleTemplateVariables: any[];
        exampleInfoTranslations: DynamicConditionsBuilderHelp['exampleInfoTranslations'];
        useInBuilderButtonLabel: string;
        onUseInBuilder?: () => void;
    } | {
        type: 'general';
        exampleData?: never;
        exampleTemplateVariables?: never;
        exampleInfoTranslations?: never;
        useInBuilderButtonLabel?: never;
        onUseInBuilder?: never;
    }
);

const ModalHelpDynamicConditionsBuilderSection = ({
    type,
    name,
    hidden = false,
    text,
    exampleData,
    exampleTemplateVariables,
    exampleInfoTranslations,
    useInBuilderButtonLabel,
    onUseInBuilder,
} : Props) => {
    const theme = useTheme();
    const exampleForm = useForm({
        defaultValues: exampleData || {},
        mode: 'onChange',
    });

    return (
        <ModalHelpSectionCard
            title={name}
            rightButtons={
                (type !== 'general' && onUseInBuilder) ? [
                    {
                        label: useInBuilderButtonLabel,
                        onClick: onUseInBuilder || (() => {}),
                        icon: 'duplicate',
                    },
                ] : []
            }
            sx={{
                ...(hidden && { display: 'none' }),
            }}
        >
            <Stack
                direction={type === 'general' ? 'row-reverse' : 'column'}
                sx={{
                    rowGap: pxToRem(32),
                    columnGap: pxToRem(16),
                    width: '100%',
                    justifyContent: 'center',
                    alignItems: 'center',
                    ...(type === 'general' && {
                        flexWrap: 'wrap',
                    }),
                }}
            >
                <Box
                    sx={{
                        maxWidth: type === 'general' ? '100%' : pxToRem(1152),
                        ...(type === 'general' && {
                            width: 'auto',
                            minWidth: 'fit-content',
                        }),
                        ...(type === 'function' && {
                            width: '100%',
                            paddingTop: pxToRem(16),
                        }),
                        '& img,svg': {
                            maxWidth: '100%',
                            height: 'auto',
                        },
                        pointerEvents: 'none',
                    }}
                >
                    {type === 'general' ? (
                        <DynCondBuilderHelpGeneralRightIllustration />
                    ) : (
                        <FormProvider
                            methods={exampleForm}
                        >
                            <DynamicConditionsBuilderStructuredBlock
                                helpMode
                                helpExampleInfoTranslations={exampleInfoTranslations}
                                blockIndex={0}
                                templateVariables={exampleTemplateVariables || []}
                                onConvert={() => {}}
                                onRemove={() => {}}
                            />
                        </FormProvider>
                    )}
                </Box>
                <Box
                    sx={{
                        width: '100%',
                        maxWidth: type === 'general' ? pxToRem(660) : pxToRem(900),
                        '& h1': theme.typography.h1,
                        '& h2': theme.typography.h2,
                        '& p': theme.typography.body2,
                        '& li': {
                            ...theme.typography.body2,
                            marginBottom: pxToRem(12),
                            '&:last-child': {
                                marginBottom: 0,
                            },
                        },
                        '& section': {
                            marginBottom: type === 'general' ? pxToRem(16) : pxToRem(32),
                            '&:last-child': {
                                marginBottom: 0,
                            },
                            '& > h1, h2, p, ul': {
                                marginBottom: pxToRem(12),
                                '&:last-child': {
                                    marginBottom: 0,
                                },
                            },
                        },
                        '& p b, & li b': theme.typography.subtitle1,
                    }}
                >
                    <div dangerouslySetInnerHTML={{ __html: text }} />
                </Box>
            </Stack>
        </ModalHelpSectionCard>
    );
};

export default ModalHelpDynamicConditionsBuilderSection;
