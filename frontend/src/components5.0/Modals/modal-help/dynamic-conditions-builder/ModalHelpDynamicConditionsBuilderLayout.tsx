import { DialogContent, DialogTitle, Stack } from '@mui/material';
import { useRef, useState } from 'react';
// components
import Button from '../../../Button';
import ModalHelpDynamicConditionsBuilderSection from './ModalHelpDynamicConditionsBuilderSection';
// utils
import { pxToRem } from '../../../../theme/typography';
// help
import dynamicConditionsBuilderHelpCs from './language-variants/dynamicConditionsBuilderHelpCs';
import dynamicConditionsBuilderHelpEn from './language-variants/dynamicConditionsBuilderHelpEn';
// types
import { DynamicConditionsBuilderHelpFunction } from './types';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';

const ModalHelpDynamicConditionsBuilderLayout = ({
    hidden = false,
    onUseInBuilder,
}: {
    /** If true, it will have `display: none` style */
    hidden?: boolean;
    onUseInBuilder?: (blockFunction: DynamicConditionsBuilderHelpFunction['builderBlockFunction']) => void;
}) => {
    const [tab, setTab] = useState<number | 'general' | null>(null);
    const [userLanguage] = useState(LoggedUserStore.getState().userLanguage);

    const scrollableRef = useRef<HTMLDivElement>(null);

    const handleSetTab = (newTab: typeof tab) => {
        if (tab === newTab) {
            setTab(null);
        } else {
            setTab(newTab);
        }

        scrollableRef.current?.scrollTo({
            top: 0,
            behavior: 'instant',
        });
    };

    const dynamicConditionsBuilderHelp = (
        userLanguage === 'cs'
            ? dynamicConditionsBuilderHelpCs
            : dynamicConditionsBuilderHelpEn
    );

    const generalSectionComponent = (
        <ModalHelpDynamicConditionsBuilderSection
            name={dynamicConditionsBuilderHelp.commonSectionName}
            type="general"
            text={dynamicConditionsBuilderHelp.commonSectionDescription}
            hidden={tab !== null && tab !== 'general'}
        />
    );

    const functionSectionComponents = dynamicConditionsBuilderHelp.functions.map((func, index) => (
        <ModalHelpDynamicConditionsBuilderSection
            key={index}
            name={func.nameTranslation}
            type="function"
            text={func.description}
            useInBuilderButtonLabel={dynamicConditionsBuilderHelp.useInBuilderButtonTranslation}
            onUseInBuilder={() => onUseInBuilder?.(func.builderBlockFunction)}
            exampleInfoTranslations={dynamicConditionsBuilderHelp.exampleInfoTranslations}
            exampleData={func.exampleData}
            exampleTemplateVariables={func.exampleTemplateVariables}
            hidden={tab !== null && tab !== index}
        />
    ));

    return (
        <>
            <DialogTitle
                sx={{
                    maxHeight: `calc(${pxToRem(120)} + 1rem)`, // 120px for the Stack height with 3 rows of buttons & 1rem for the padding
                    overflowY: 'auto',
                    display: hidden ? 'none' : 'block',
                }}
            >
                <Stack
                    direction="row"
                    sx={{
                        width: '100%',
                        justifyContent: 'flex-start',
                        flexWrap: 'wrap',
                        flexGrow: 1,
                        gap: 1,
                    }}
                >
                    <Button
                        label={dynamicConditionsBuilderHelp.commonSectionName}
                        color="secondary"
                        onClick={() => handleSetTab('general')}
                        variant="text"
                        size="small"
                        sx={{
                            backgroundColor: (theme) => (
                                tab === 'general' ? theme.palette.primary.light : ''
                            ),
                        }}
                    />
                    {dynamicConditionsBuilderHelp.functions.map((func, index) => (
                        <Button
                            key={index}
                            label={func.nameTranslation}
                            color="secondary"
                            onClick={() => handleSetTab(index)}
                            variant="text"
                            size="small"
                            sx={{
                                backgroundColor: (theme) => (
                                    tab === index ? theme.palette.primary.light : ''
                                ),
                            }}
                        />
                    ))}
                </Stack>
            </DialogTitle>
            <DialogContent
                ref={scrollableRef}
                dividers
                sx={{
                    height: '100%',
                    width: '100%',
                    backgroundColor: (theme) => theme.palette.background.default,
                    display: hidden ? 'none' : 'block',
                }}
            >
                <Stack
                    spacing={2}
                >
                    {generalSectionComponent}
                    {functionSectionComponents}
                </Stack>
            </DialogContent>
        </>
    );
};

export default ModalHelpDynamicConditionsBuilderLayout;
