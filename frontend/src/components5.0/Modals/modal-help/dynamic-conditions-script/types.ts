export type DynamicConditionsScriptHelpFunction<Sanitized = false> = {
    nameTechnical: string;
    nameTranslation: string | null;
    description: string;
    isMajor: boolean;
    arguments: {
        name: string;
        types: ('String' | 'Number' | 'Boolean' | 'Array' | 'Object' | 'Function' | 'Any')[];
        description: string;
    }[];
    examples: string[];
} & (Sanitized extends true ? { sanitizedDescription: string } : { sanitizedDescription?: never });

export type DynamicConditionsScriptHelpValidations = {
    name: string;
    innerTitle: string;
    validations: {
        name: string;
        allowedValuesString: string;
        description: string;
    }[];
};

export type DynamicConditionsScriptHelp<Sanitized = false> = {
    validationsSectionNameTranslation: string;
    moreFunctionsButtonTranslation: string;
    parametersSectionNameTranslation: string;
    examplesSectionNameTranslation: string;
    searchPlaceholderTranslation: string;
    functionsButtonTranslation: string;
    servicesButtonTranslation: string;
    functions: DynamicConditionsScriptHelpFunction<Sanitized>[];
    validations: DynamicConditionsScriptHelpValidations;
};
