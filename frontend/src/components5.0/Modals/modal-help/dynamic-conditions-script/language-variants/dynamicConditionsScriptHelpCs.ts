/* eslint-disable quotes */
/* eslint-disable max-len */

import { DynamicConditionsScriptHelp } from '../types';

const dynamicConditionsScriptHelpCs: DynamicConditionsScriptHelp = {
    validationsSectionNameTranslation: 'Validace',
    moreFunctionsButtonTranslation: '<PERSON><PERSON><PERSON> funkce',
    parametersSectionNameTranslation: 'Parametry',
    examplesSectionNameTranslation: 'Příklady',
    searchPlaceholderTranslation: 'Např. "hideVarOn" nebo "úkol" ...',
    functionsButtonTranslation: 'Funkce',
    servicesButtonTranslation: 'Služby',
    functions: [
        {
            nameTechnical: 'hideVarOn',
            nameTranslation: 'Skrýt',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, kterou schováváme, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podm<PERSON><PERSON>, p<PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON> splnění se proměnná schová. Pokud false, proměnná se neskryje/znovu objeví.',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultně false - vrací se do svého původního stavu jako před spuštěním skriptu (tedy na povinnost, na jakou byla proměnná namapována), když condition neplatí. Pokud je returnToPreviousState true, vrací se do stavu předešlého, v jakém byla před provedením této funkce.',
                },
            ],
            description: 'Pokud je podmínka <i>condition</i> pravdivá, skryje proměnnou s <i>tvar_name</i>.',
            isMajor: true,
            examples: [
`hideVarOn('Místo začátku pracovní cesty', function() {
    return vSync('Nepřítomnost') == 'Služební cesta';
});`,
            ],
        },
        {
            nameTechnical: 'varReadOn',
            nameTranslation: 'Pouze ke čtení',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, které měníme povinnost zápisu, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, za které se povinnost proměnné změní. Pokud false, proměnná bude mít defaultní povinnost.',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultně false - vrací se do svého původního stavu jako před spuštěním skriptu (tedy na povinnost, na jakou byla proměnná namapována), když condition neplatí. Pokud je returnToPreviousState true, vrací se do stavu předešlého, v jakém byla před provedením této funkce.',
                },
            ],
            description: 'Změní proměnnou s <i>tvar_name</i> na proměnnou ke čtení, pokud <i>condition</i> == true.',
            isMajor: true,
            examples: [
`varReadOn('Středisko objednatele', function() {
    return vSync('Jméno objednatele') === "Krejčí";
});`,
`// Příklad na returnToPreviousState - FAKTURACNI_EMAIL je namapována pro zápis, při spuštění úkolu nemá odeslatFakturu
// vyplněnou hodnotu. FAKTURACNI_EMAIL se proto zobrazí jako proměnná pro čtení (díky varReadOn). Pokud odeslatFakturu změním na Ano,
// bude FAKTURACNI_EMAIL povinný (díky varReadOn s přepínačem returnToPreviousState, který vrátí proměnnou do předchozího,
// tedy povinného stavu). Pokud vyberu v odeslatFakturu Ne, bude FAKTURACNI_EMAIL (nejprve pro zápis a pak) pro čtení.
varMustOn(['FAKTURACNI_EMAIL'], function() {
    return vSync('odeslatFakturu') == 'Ano';
});

varReadOn(['FAKTURACNI_EMAIL'], function() {
    return vSync('odeslatFakturu') != 'Ano';
}, null, true);
//
`,
            ],
        },
        {
            nameTechnical: 'varMustOn',
            nameTranslation: 'Povinné',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, které měníme povinnost zápisu, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, za které se povinnost proměnné změní. Pokud false, proměnná bude mít defaultní povinnost.',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultně false - vrací se do svého původního stavu jako před spuštěním skriptu (tedy na povinnost, na jakou byla proměnná namapována), když condition neplatí. Pokud je returnToPreviousState true, vrací se do stavu předešlého, v jakém byla před provedením této funkce.',
                },
            ],
            description: 'Změní proměnnou s <i>tvar_name</i> na povinnou proměnnou, pokud <i>condition</i> == true.',
            isMajor: true,
            examples: [
`varMustOn('Středisko objednatele', v('Jméno objednatele') == v('Iniciator'));`,
            ],
        },
        {
            nameTechnical: 'varWriteOn',
            nameTranslation: 'K zápisu',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, které měníme povinnost zápisu, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, za které se povinnost proměnné změní. Pokud false, proměnná bude mít defaultní povinnost.',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultně false - vrací se do svého původního stavu jako před spuštěním skriptu (tedy na povinnost, na jakou byla proměnná namapována), když condition neplatí. Pokud je returnToPreviousState true, vrací se do stavu předešlého, v jakém byla před provedením této funkce.',
                },
            ],
            description: 'Změní proměnnou s <i>tvar_name</i> na proměnnou k zápisu, pokud <i>condition</i> == true.',
            isMajor: true,
            examples: [
`varWriteOn('Datum nepřítomnosti DO', fnOr( fnEq(v('Nepřítomnost'), 'Služební cesta'), fnEq(v('Žadatel'), 14987) ));`,
            ],
        },
        {
            nameTechnical: 'changeVarVal',
            nameTranslation: 'Změnit hodnotu proměnné',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, které měníme hodnotu, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, za které se hodnota proměnné změní. Pokud false, proměnná bude mít hodnotu z databáze.',
                },
                {
                    name: 'value',
                    types: ['Any'],
                    description: 'Objekt nebo pole objektů (pokud Array.isArray(tvar_name)) s vlastnostmi proměnné (entity)',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
                {
                    name: 'force',
                    types: ['Boolean'],
                    description: 'Zavolá setValue() pokaždé, kdy je daná proměnná změněna. Tedy dojde ke změně hodnoty i přesto, že daná proměnná byla manuálně editována. Defaultně false.',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultně false - vrací se do svého původního stavu jako před spuštěním skriptu (tedy na hodnotu, jakou měla při načtení úkolu z databáze), když condition neplatí. Pokud je returnToPreviousState true, vrací se na hodnotu, jakou měla před provedením této funkce.',
                },
            ],
            description: 'Nastaví proměnné s <i>tvar_name</i> novou hodnotu <i>value</i>, pokud <i>condition</i> == true.',
            isMajor: true,
            examples: [
`/* Příklad doplnění jména uživatele podle userId z DT */
changeVarVal("Iniciator", fnIsNotEmpty(v('Číslo')), function() {
    var userId = vSync('Číslo');
    if (userId) {
        return Api.request.get('/users/' + userId).then(function(user) {
            return {value: userId, title: user.user_display_name};
        }).catch(function(error) {
            return {title: null, value: null}; // user not found
        })
    }
    return {title: null, value: null};
});`,
`changeVarVal('text', true, function() {
    return formatEmails(vSync('Adresy'));
}, null, true); // force`,
`changeVarVal('dt multi', true, function() {
    ...
    return ['1','2','4','8']; // pole dtv indexů
});`,
`changeVarVal('dt dropdown', true, function() {
    ...
    return '2'; // dtv_index
});`,
`/* Příklad zacházení s hodnotou z dynamického listu tak, aby nedocházelo k dotazu na uložení změn bez uživatelské změny */
changeVarVal(['totalWvat', 'DLU'], true, function() {
    if (!_.isEmpty(dataShare)) {
        return [
            dataShare['totalWvat'].ivar_value,
            { value: dataShare['DLU'].ivar_value, title: dataShare['DLU'].ivar_lov_value }
        ];
    } else {
        if (vSync('DLU') === null) {
            return [vSync('totalWvat'), null];
        }
        return [vSync('totalWvat'), vSyncObj('DLU')];
    }
});`,
`changeVarVal(['LOV text', 'LOV numbers', 'datum'], true, function() { // todo LOV dates nefunguje spravne
    const lt = ['foo', 'bar'];
    const ln = [1, '2'];
    // const ld = [new Date('02/17/2021').toISOString()]; // ['02/17/2021'];

    return [lt, ln, '02/17/2021']
});`,
            ],
        },
        {
            nameTechnical: 'varDefOn',
            nameTranslation: 'Měnit definici proměnné',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, které měníme definici, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, za které se definice proměnné změní. Pokud false, proměnná se vrátí do původního stavu.',
                },
                {
                    name: 'definition',
                    types: ['Function', 'Array', 'Object'],
                    description: 'Objekt nebo pole objektů (pokud Array.isArray(tvar_name)) s vlastnostmi proměnné (entity)',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
            ],
            description: 'Mění definici proměnné s <i>tvar_name</i>, pokud <i>condition</i> == true.',
            isMajor: true,
            examples: [
`varDefOn('DLU', true, function() {
    var dlRoleId = vSync('DLR');
    if (dlRoleId) {
        return { tvar_meta: { dlUrl: \`/users/role/\${dlRoleId}?filter=user_first_name<like>"%admin%"&order=user_name&sort=asc\` } };
    }
    return { tvar_meta: { dlUrl: '/users/active' } };
});`,
`varDefOn('DLU', true, function() {
    return {
        dataStructureRenderer: function (user) {
            return {
                value: user.id,
                title: \`\${user.user_name} (\${user.user_display_name})\`
            }
        }
    };
});`,
`varDefOn('DT', true, function() {
    return {
        tvar_meta: {
            dynTable: {
                filter: "col_3<like>"%a%"", // <and>, <or>, <eq>, <ne>, <nin>, <in>, <like>, <nlike>, <le>, <ge>, <lt>, <gt>, <isnn>, <isn>
                urlParams: "distinct=true", // Odstranění duplicit v seznamu možností,
                order: "col_3",
                sort: "desc",
                colIndex: 1 // sloupec, který se zobrazí
            }
        }
    };
});`,
`varDefOn('DR', true, function() {
    return {
        tvar_meta: {
            tableDefinition: {
                columns: {
                  ICO:
                    {
                        duty: "mandatory"
                    }
                }
            }
        }
    };
});`,
`varDefOn('variable', true, function() {
    return {
        ivar_name_cs: název,
        tvar_meta: {
            customization: {
                elementColor: "#79e095",
                fontColor: "#c90c0c",
                fontSize: "S",
                cursive: true
            }
        }
    };
});`,
`varDefOn(['files'], true, function() {
    return { attachmentsFilter:  'name<like>"%test%"' }; // <and>, <or>, <eq>, <ne>, <nin>, <in>, <like>, <nlike>, <le>, <ge>, <lt>, <gt>, <isnn>, <isn>
});`,
`varDefOn('naseptavac', currentTask.state.taskInfo.headerName == 'hlavicka', function() {
    return {
        tvar_meta: {
            suggestBox: {
                apiUrl: "/dyn-table/31/values?filter=col_2<like>"%cz%"",
                prop: "col_2"
            }
        }
    };
});`,
`varDefOn('datum', true, function() {
    return {
        tvar_meta: {
            defaultViewDate: "2020-08-13T22:00:00.000Z", // "05/08/2019" (samotný new Date() nepoužívat!!!)
            onlyWorkingDays: true,
            startDate: "2020-08-13T22:00:00.000Z", // "05/08/2019"
            endDate: "2020-08-13T22:00:00.000Z", // "05/08/2019"
            datesDisabled: ["2020-08-13T22:00:00.000Z", "05/08/2019"]
        }
    };
});`,
            ],
        },
        {
            nameTechnical: 'onChangeVar',
            nameTranslation: 'Při změně proměnné',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, jejíž změnu chceme odchytit, nebo pole proměnných',
                },
                {
                    name: 'callback',
                    types: ['Function'],
                    description: 'Funkce, která bude zavolána při změně proměnné. Parametry: tvar_name, index.',
                },
            ],
            description: 'Slouží k provedení akce při změně proměnné s <i>tvar_name</i>.',
            isMajor: true,
            examples: [
`var iproc_id = 0;
var previousPO = '';

onChangeVar('POnumberSelect', function(tvar_name, index) {
    iproc_id = vSync('POnumberSelect');
    if (iproc_id && previousPO != iproc_id) {
        return Api.request.get('/processes/' + iproc_id + '/shared-variables').then(function(data) {
            previousPO = iproc_id;
            ...
        });
    }
});`,
            ],
        },
        {
            nameTechnical: 'solveTaskOn',
            nameTranslation: 'Splnění úkolu',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, jejíž změna hodnoty spouští funkci. Na rozdíl od validateVarOn() se aplikuje i na proměnné pro čtení <Label>',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, při jejímž splnění lze úkol splnit.',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
            ],
            description: 'Pokud je podmínka <i>condition</i> false, znemožní splnění úkolu.',
            isMajor: true,
            examples: [
`solveTaskOn('Duplicity', function() {
    return fnIsNotEmptySync(v('Výstraha')); // případně vSync('Duplicity') === true
});`,
            ],
        },
        {
            nameTechnical: 'validateVarOn',
            nameTranslation: 'Validace proměnné',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'Název proměnné, která se validuje, nebo jejich pole',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'Podmínka, za které se proměnná bude validovat. Pokud false, bude se proměnná validovat defaultně (povinnost, číslo...)',
                },
                {
                    name: 'validations',
                    types: ['Function', 'Object'],
                    description: 'Seznam validací (viz Validace)',
                },
                {
                    name: 'validationErrors',
                    types: ['Object', 'Function'],
                    description: 'Hláška/y, která se zobrazí v případě, kdy je testovaná proměnná nevalidní. { isLowerThan: \'Datum1 je větší než datum2.\' } nebo s překlady { isLowerThan: {cs: \'Datum1 je větší než datum2.\', en: \'Date1 is higher than date2.\'} }',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'String nebo pole tvar_name, jejichž změna způsobí opětovné provedení celého skriptu',
                },
            ],
            description: 'Přidá proměnné s <i>tvar_name</i> validace <i>validations</i> spolu s hláškami <i>validationErrors</i>, pokud <i>condition</i> == true. Neaplikuje se na proměnné pro čtení <Label>, které se nevalidují.',
            isMajor: true,
            examples: [
`validateVarOn('datum1', true, function() {
    return { isLowerThan: vSync('datum2') };
}, { isLowerThan: 'Datum1 je větší než datum2.' });`,
`// Bez překladů
validateVarOn('datum1', true, { isLowerThan: vSync('datum2') }, { isLowerThan: 'Datum1 je větší než datum2.' });`,
`// S překlady
validateVarOn('datum1', true, { isLowerThan: vSync('datum2') }, { isLowerThan: {cs: 'Datum1 je větší než datum2.', en: 'Date1 is higher than date2.'} });`,
`// Obecná custom validace s daty z DT
var _dtSuppliers = vSync('_dtSuppliers');
getDynTable(_dtSuppliers);

validateVarOn('supplier', true,
  function() {
    var foundSupplier = _.find(dynTable[_dtSuppliers], 'col_8', vSync('supplier'));
    return {
      isValid: typeof foundSupplier != 'undefined'
    }
  },
  { isValid: {cs: 'Neexistující dodavatel.', en: 'Supplier does not exist.'} }
);`,
`// Validace s využitím proměnných ve validačních hláškách
var max;
var min;
validateVarOn('quantity', true,
  function() {
    var findRow = _.find(dynTable[dtRequestsItems], 'col_5', vSync('typOfCard'));
    max = Number(findRow.col_13);
    min = Number(findRow.col_14);

    return {
        isValid: (vSync('quantity') >= min) && (vSync('quantity') <= max)
    }
  },
  function() {
    return { isValid: {cs: 'Hodnota je rozdílná než povolené množství: min '+min+', max '+max+'.', en: 'Value is different from alowed quantity: min '+min+', max '+max+'.'} };
  }
);`,
            ],
        },
        {
            nameTechnical: 'repeatScript',
            nameTranslation: 'Opakovat skript',
            arguments: [],
            description: 'Manuální opětovné spuštění celého skriptu.',
            isMajor: true,
            examples: [
`repeatScript();`,
            ],
        },

        // --------------------------------------------------- Non-major functions ---------------------------------------------------

        // variable value getters
        ...(([
            [
                'v',
                'Vrací hodnotu proměnné úkolu.',
                [
`/* Funguje dynamicky */
changeVarVal('Cena s DPH', v('Jméno objednatele') != null, 1000);
hideVarOn('Místo začátku pracovní cesty', fnEq(v('Nepřítomnost'), 'Služební cesta'));`,
`/* Funguje jen v okamžiku načtení formuláře */
var promenna = fnEqSync(v('Nepřítomnost'), 'Služební cesta');`,
                ],
            ],
            [
                'vSync',
                'Vrací hodnotu proměnné úkolu.',
                [
`/* Funguje dynamicky */
hideVarOn('Místo začátku pracovní cesty', function() {
    return vSync('Nepřítomnost') === 'Služební cesta';
});`,
`/* Funguje jen v okamžiku načtení formuláře */
hideVarOn('Místo začátku pracovní cesty', vSync('Nepřítomnost') === 'Služební cesta');`,
                ],
            ],
            [
                'vObj, vSyncObj',
                'Vrací hodnotu proměnné úkolu.\nU proměnných typu Select vrací objekt { title, value }.\nPozor: pokud je hodnota proměnné dynamickými podmínkami změněna a je jí nastaveno id jako hodnota, vrátí id.',
                [],
            ],
            [
                'procV, procVSync',
                'Vrací hodnotu proměnné případu.',
                [],
            ],
            [
                'procVObj, procVObjSync',
                'Vrací hodnotu proměnné případu.\nU proměnných typu Select vrací objekt { title, value }.',
                [],
            ],
        ] as ([string, string, string[]])[]).map((([name, description, examples]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['String'] as ['String'],
                    description: 'Název proměnné',
                },
            ],
            description: description,
            isMajor: false,
            examples: examples,
        })))),

        // two-argument comparison functions
        ...([
            ['fnEq, fnEqSync', 'Porovnává dvě hodnoty (===). Argumenty mohou být i funkce.'],
            ['fnEqArray, fnEqArraySync', 'Vrací true, pokud se <i>value</i> rovná (===) alespoň jedné hodnotě v poli. Argument <i>value</i> může být i funkce.'],
            ['fnNe, fnNeSync', 'Porovnává dvě hodnoty (!==). Argumenty mohou být i funkce.'],
            ['fnNeArray, fnNeArraySync', 'Vrací true, pokud se <i>value</i> nerovná (!==) ani jedné hodnotě v poli. Argument <i>value</i> může být i funkce.'],
            ['fnGt, fnGtSync', 'Vrací true, pokud <i>value1</i> je větší než <i>value2</i>. Argumenty mohou být i funkce.'],
            ['fnGe, fnGeSync', 'Vrací true, pokud <i>value1</i> je větší nebo rovno <i>value2</i>. Argumenty mohou být i funkce.'],
            ['fnLt, fnLtSync', 'Vrací true, pokud <i>value1</i> je menší než <i>value2</i>. Argumenty mohou být i funkce.'],
            ['fnLe, fnLeSync', 'Vrací true, pokud <i>value1</i> je menší nebo rovno <i>value2</i>. Argumenty mohou být i funkce.'],
            ['fnLike, fnLikeSync', 'Vrací true, pokud <i>value1</i> obsahuje <i>value2</i>.'],
            ['fnNLike, fnNLikeSync', 'Vrací true, pokud <i>value1</i> neobsahuje <i>value2</i>.'],
        ].map((([name, description]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'value1',
                    types: ['Any'] as ['Any'],
                    description: 'První hodnota',
                },
                {
                    name: 'value2',
                    types: ['Any'] as ['Any'],
                    description: 'Druhá hodnota',
                },
            ],
            description: description,
            isMajor: false,
            examples: [],
        })))),

        // one-argument comparison functions
        ...([
            ['fnIsEmpty, fnIsEmptySync', 'Zadaná hodnota je undefined, null, "", [] nebo {}.'],
            ['fnIsNotEmpty, fnIsNotEmptySync', 'Zadaná hodnota není undefined, null, "", [] ani {}.'],
        ].map((([name, description]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'value',
                    types: ['Any'] as ['Any'],
                    description: 'Hodnota',
                },
            ],
            description: description,
            isMajor: false,
            examples: [],
        })))),

        // logical operations
        ...([
            ['fnAnd, fnAndSync', 'Logické AND zadaných funkcí nebo hodnot.'],
            ['fnOr, fnOrSync', 'Logické OR zadaných funkcí nebo hodnot.'],
        ].map((([name, description]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'valueOrFunction1',
                    types: ['Any', 'Function'] as ['Any', 'Function'],
                    description: 'Hodnota nebo funkce, která vrací hodnotu',
                },
                {
                    name: 'valueOrFunction2',
                    types: ['Any', 'Function'] as ['Any', 'Function'],
                    description: 'Hodnota nebo funkce, která vrací hodnotu',
                },
                {
                    name: '...',
                    types: ['Any', 'Function'] as ['Any', 'Function'],
                    description: 'Další hodnoty nebo funkce, které vrací hodnotu',
                },
            ],
            description: description,
            isMajor: false,
            examples: [],
        })))),

        // other non-major functions
        {
            nameTechnical: 'jsonSumArray, jsonSumArraySync',
            nameTranslation: null,
            arguments: [
                {
                    name: 'json',
                    types: ['String', 'Object'] as ['String', 'Object'],
                    description: 'JSON ve formátu string/json',
                },
                {
                    name: 'pathInJson',
                    types: ['String'],
                    description: 'Cesta k vlastnosti, např. obj.arr',
                },
            ],
            description: 'Parametr json ve formátu string/json, např. {obj: {ignored: true, arr: [1, 2, 3]}}. String pathInJson je cesta k vlastnosti, např. obj.arr. Vrací číslo.',
            isMajor: false,
            examples: [],
        },
        {
            nameTechnical: 'formatEmails, formatEmailsSync',
            nameTranslation: null,
            arguments: [
                {
                    name: 'emails',
                    types: ['Array'],
                    description: 'Emailové adresy',
                },
            ],
            description: 'Formátuje e-mailové adresy na jednotlivé adresy oddělené středníkem. Používá se v kombinaci s changeVarVal() s parametrem <i>force</i> === true.',
            isMajor: false,
            examples: [],
        },
        {
            nameTechnical: 'canSolveTask',
            nameTranslation: null,
            arguments: [
                {
                    name: 'bool',
                    types: ['Boolean'],
                    description: 'Umožnit uživateli splnění úkolu',
                },
            ],
            description: 'Pokud je parametrem <i>false</i>, zamezuje uživateli splnění úkolu zešednutím tlačítek "Uložit" a "Jen uložit".',
            isMajor: false,
            examples: [],
        },
    ],
    validations: {
        name: 'Validace',
        innerTitle: 'Seznam definovaných validací nad proměnnými',
        validations: [
            { name: 'isExisty', allowedValuesString: 'true', description: 'Není null ani undefined.' },
            { name: 'matchRegexp', allowedValuesString: 'Regex', description: 'Test na libovolný regulární výraz.' },
            { name: 'isUndefined', allowedValuesString: 'true', description: 'value === undefined' },
            { name: 'isEmptyString', allowedValuesString: 'true', description: 'Prázdný string.' },
            { name: 'isEmail', allowedValuesString: 'true', description: 'E-mailová adresa.' },
            { name: 'isEmails', allowedValuesString: 'true', description: 'Emailové adresy oddělené středníkem.' },
            { name: 'isUrl', allowedValuesString: 'true', description: 'Vrací true, pokud je validní url.' },
            { name: 'isTrue', allowedValuesString: 'true', description: 'value === true' },
            { name: 'isFalse', allowedValuesString: 'true', description: 'value === false' },
            { name: 'isNumeric', allowedValuesString: 'true', description: 'Je typu Number (42; -3.14).' },
            { name: 'isAlpha', allowedValuesString: 'true', description: 'Obsahuje pouze písmena.' },
            { name: 'isAlphanumeric', allowedValuesString: 'true', description: 'Obsahuje číslice a písmena.' },
            { name: 'isInt', allowedValuesString: 'true', description: 'Integer (42; -12; 0).' },
            { name: 'isFloat', allowedValuesString: 'true', description: 'Float (42; -3.14; 1e3).' },
            { name: 'isWords', allowedValuesString: 'true', description: 'isAlpha s bílými znaky.' },
            { name: 'isSpecialWords', allowedValuesString: 'true', description: 'isAlpha včetně speciálních písmen (a-z,ú,ø,æ,å).' },
            { name: 'isLength', allowedValuesString: 'Number', description: 'Vrací true, pokud je rovno zadané délce.' },
            { name: 'equals', allowedValuesString: 'value', description: 'ivar_value == value.' },
            { name: 'equalsField', allowedValuesString: 'field', description: 'ivar_value == variables[field].' },
            { name: 'maxLength', allowedValuesString: 'Number', description: 'Je menší nebo rovno.' },
            { name: 'minLength', allowedValuesString: 'Number', description: 'Je větší nebo rovno.' },
            { name: 'isIC', allowedValuesString: 'true', description: 'Identifikační číslo.' },
            { name: 'isTelephoneNumber', allowedValuesString: 'true', description: 'Telefonní číslo.' },
            { name: 'minValue', allowedValuesString: 'Number', description: 'Je větší nebo rovno.' },
            { name: 'maxValue', allowedValuesString: 'Number', description: 'Je menší nebo rovno.' },
            { name: 'isLowerThan', allowedValuesString: 'Number, Date', description: 'Je menší.' },
            { name: 'isHigherThan', allowedValuesString: 'Number, Date', description: 'Je větší.' },
            { name: 'isLowerOrEqualThan', allowedValuesString: 'Number, Date', description: 'Je menší nebo se rovná.' },
            { name: 'isHigherOrEqualThan', allowedValuesString: 'Number, Date', description: 'Je větší nebo se rovná.' },
            { name: 'isDate', allowedValuesString: 'value', description: 'Je datum ve správném formátu s ohledem na lokalizaci.' },
            { name: 'isPath', allowedValuesString: 'true', description: 'Cesta k souboru.' },
            { name: 'isValid', allowedValuesString: 'Boolean', description: 'Možnost libovolného porovnání s tím, že pokud vrátí false, objeví se validační hláška.' },
        ],
    },
};

export default dynamicConditionsScriptHelpCs;
