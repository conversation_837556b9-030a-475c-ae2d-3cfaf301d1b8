/* eslint-disable quotes */
/* eslint-disable max-len */

import { DynamicConditionsScriptHelp } from '../types';

const dynamicConditionsScriptHelpEn: DynamicConditionsScriptHelp = {
    validationsSectionNameTranslation: 'Validations',
    moreFunctionsButtonTranslation: 'More functions',
    parametersSectionNameTranslation: 'Parameters',
    examplesSectionNameTranslation: 'Examples',
    searchPlaceholderTranslation: 'E.g., "hideVarOn" or "task" ...',
    functionsButtonTranslation: 'Functions',
    servicesButtonTranslation: 'Services',
    functions: [
        {
            nameTechnical: 'hideVarOn',
            nameTranslation: 'Hide',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of tvar_name of variables to be hidden',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'condition that decides about hiding variable(s)',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultly false - returns to its original state as before the script was run (mapped duty), if condition is false. If returnToPreviousState is true, the variable returns to the state it was in before the function was executed.',
                },
            ],
            description: 'If <i>condition</i> true, hides the variable with <i>tvar_name</i>.',
            isMajor: true,
            examples: [
`hideVarOn('Místo začátku pracovní cesty', function() {
    return vSync('Nepřítomnost') == 'Služební cesta';
});`,
            ],
        },
        {
            nameTechnical: 'varReadOn',
            nameTranslation: 'Read-only',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are changing the duty to, or array of variables',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'condition that decides whether the variable(s) duty will change',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultly false - returns to its original state as before the script was run (mapped duty), if condition is false. If returnToPreviousState is true, the variable returns to the state it was in before the function was executed.',
                },
            ],
            description: 'Changes the variable with <i>tvar_name</i> to read, if <i>condition</i> true.',
            isMajor: true,
            examples: [
`varReadOn('Středisko objednatele', function() {
    return vSync('Jméno objednatele') === "Krejčí";
});`,
`// Real example of returnToPreviousState use - FAKTURACNI_EMAIL is mapped for write, odeslatFakturu does not have any value filled
// when starting the task. FAKTURACNI_EMAIL shows as for read (thanks to varReadOn). If I set odeslatFakturu to "Yes",
// FAKTURACNI_EMAIL will be mandatory (thanks to varReadOn with returnToPreviousState flag, which returns the variable to its previous mandatory state).
// If I select "No" in odeslatFakturu, FAKTURACNI_EMAIL will be (for writing and then) for read.
varMustOn(['FAKTURACNI_EMAIL'], function() {
    return vSync('odeslatFakturu') == 'Ano';
});

varReadOn(['FAKTURACNI_EMAIL'], function() {
    return vSync('odeslatFakturu') != 'Ano';
}, null, true);
//
`,
            ],
        },
        {
            nameTechnical: 'varMustOn',
            nameTranslation: 'Mandatory',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are changing the duty to, or array of variables',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'condition that decides whether the variable(s) duty will change',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultly false - returns to its original state as before the script was run (mapped duty), if condition is false. If returnToPreviousState is true, the variable returns to the state it was in before the function was executed.',
                },
            ],
            description: 'Changes the variable with <i>tvar_name</i> to mandatory, if <i>condition</i> true.',
            isMajor: true,
            examples: [
`varMustOn('Středisko objednatele', v('Jméno objednatele') == v('Iniciator'));`,
            ],
        },
        {
            nameTechnical: 'varWriteOn',
            nameTranslation: 'Writable',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are changing the duty to, or array of variables',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'condition that decides whether the variable(s) duty will change',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultly false - returns to its original state as before the script was run (mapped duty), if condition is false. If returnToPreviousState is true, the variable returns to the state it was in before the function was executed.',
                },
            ],
            description: 'Changes the variable with <i>tvar_name</i> to write, if <i>condition</i> true.',
            isMajor: true,
            examples: [
`varWriteOn('Datum nepřítomnosti DO', fnOr( fnEq(v('Nepřítomnost'), 'Služební cesta'), fnEq(v('Žadatel'), 14987) ));`,
            ],
        },
        {
            nameTechnical: 'changeVarVal',
            nameTranslation: 'Change variable value',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are changing value to, or array of variables',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'If true, the value will change.',
                },
                {
                    name: 'value',
                    types: ['Any'],
                    description: 'Any value or function returning any value or array of values, if Array.isArray(tvar_name)',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
                {
                    name: 'force',
                    types: ['Boolean'],
                    description: 'Fires setValue() every time even if the variable value was manually changed (e.g. with formatEmails()). Default false.',
                },
                {
                    name: 'returnToPreviousState',
                    types: ['Boolean'],
                    description: 'Defaultly false - returns to its original value as before the script was run (database value), if condition is false. If returnToPreviousState is true, the variable returns to the value it had before the function was executed.',
                },
            ],
            description: 'Sets new <i>value</i> to the variable with <i>tvar_name</i>, if <i>condition</i> true.',
            isMajor: true,
            examples: [
`/* Example of returning user name from DT */
changeVarVal("Iniciator", fnIsNotEmpty(v('Číslo')), function() {
    var userId = vSync('Číslo');
    if (userId) {
        return Api.request.get('/users/' + userId).then(function(user) {
            return {value: userId, title: user.user_display_name};
        }).catch(function(error) {
            return {title: null, value: null}; // user not found
        })
    }
    return {title: null, value: null};
});`,
`changeVarVal('text', true, function() {
    return formatEmails(vSync('Adresy'));
}, null, true); // force`,
`changeVarVal('dt multi', true, function() {
    ...
    return ['1','2','4','8']; // pole dtv indexů
});`,
`changeVarVal('dt dropdown', true, function() {
    ...
    return '2'; // dtv_index
});`,
`/* Example of handling a value from dynamic list so user is not asked to save changes without user change */
changeVarVal(['totalWvat', 'DLU'], true, function() {
    if (!_.isEmpty(dataShare)) {
        return [
            dataShare['totalWvat'].ivar_value,
            { value: dataShare['DLU'].ivar_value, title: dataShare['DLU'].ivar_lov_value }
        ];
    } else {
        if (vSync('DLU') === null) {
            return [vSync('totalWvat'), null];
        }
        return [vSync('totalWvat'), vSyncObj('DLU')];
    }
});`,
`changeVarVal(['LOV text', 'LOV numbers', 'datum'], true, function() { // todo LOV dates nefunguje spravne
    const lt = ['foo', 'bar'];
    const ln = [1, '2'];
    // const ld = [new Date('02/17/2021').toISOString()]; // ['02/17/2021'];

    return [lt, ln, '02/17/2021']
});`,
            ],
        },
        {
            nameTechnical: 'varDefOn',
            nameTranslation: 'Change variable definition',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are changing definition to, or array of variables',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'If true, the variable definition will change. If false, variable returns to its original state.',
                },
                {
                    name: 'definition',
                    types: ['Function', 'Array', 'Object'],
                    description: 'Object or function returning an object or array of objects, if Array.isArray(tvar_name), with variable properties',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
            ],
            description: 'Changes definition of variable with <i>tvar_name</i>, if <i>condition</i> true.',
            isMajor: true,
            examples: [
`varDefOn('DLU', true, function() {
    var dlRoleId = vSync('DLR');
    if (dlRoleId) {
        return { tvar_meta: { dlUrl: \`/users/role/\${dlRoleId}?filter=user_first_name<like>"%admin%"&order=user_name&sort=asc\` } };
    }
    return { tvar_meta: { dlUrl: '/users/active' } };
});`,
`varDefOn('DLU', true, function() {
    return {
        dataStructureRenderer: function (user) {
            return {
                value: user.id,
                title: \`\${user.user_name} (\${user.user_display_name})\`
            }
        }
    };
});`,
`varDefOn('DT', true, function() {
    return {
        tvar_meta: {
            dynTable: {
                filter: "col_3<like>"%a%"", // <and>, <or>, <eq>, <ne>, <nin>, <in>, <like>, <nlike>, <le>, <ge>, <lt>, <gt>, <isnn>, <isn>
                urlParams: "distinct=true", // Remove duplicates in the list of options
                order: "col_3",
                sort: "desc",
                colIndex: 1 // visible column
            }
        }
    };
});`,
`varDefOn('DR', true, function() {
    return {
        tvar_meta: {
            tableDefinition: {
                columns: {
                  ICO:
                    {
                        duty: "mandatory"
                    }
                }
            }
        }
    };
});`,
`varDefOn('variable', true, function() {
    return {
        ivar_name_en: name,
        tvar_meta: {
            customization: {
                elementColor: "#79e095",
                fontColor: "#c90c0c",
                fontSize: "S",
                cursive: true
            }
        }
    };
});`,
`varDefOn(['files'], true, function() {
    return { attachmentsFilter:  'name<like>"%test%"' }; // <and>, <or>, <eq>, <ne>, <nin>, <in>, <like>, <nlike>, <le>, <ge>, <lt>, <gt>, <isnn>, <isn>
});`,
`varDefOn('naseptavac', currentTask.state.taskInfo.headerName == 'hlavicka', function() {
    return {
        tvar_meta: {
            suggestBox: {
                apiUrl: "/dyn-table/31/values?filter=col_2<like>"%cz%"",
                prop: "col_2"
            }
        }
    };
});`,
`varDefOn('datum', true, function() {
    return {
        tvar_meta: {
            defaultViewDate: "2020-08-13T22:00:00.000Z", // "05/08/2019" (don't use new Date() alone!!!)
            onlyWorkingDays: true,
            startDate: "2020-08-13T22:00:00.000Z", // "05/08/2019"
            endDate: "2020-08-13T22:00:00.000Z", // "05/08/2019"
            datesDisabled: ["2020-08-13T22:00:00.000Z", "05/08/2019"]
        }
    };
});`,
            ],
        },
        {
            nameTechnical: 'onChangeVar',
            nameTranslation: 'On change of variable',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are watching, or array of variables',
                },
                {
                    name: 'callback',
                    types: ['Function'],
                    description: 'Function that will be called when the variable changes. Parameters: tvar_name, index.',
                },
            ],
            description: 'Catches change of variale with <i>tvar_name</i>.',
            isMajor: true,
            examples: [
`var iproc_id = 0;
var previousPO = '';

onChangeVar('POnumberSelect', function(tvar_name, index) {
    iproc_id = vSync('POnumberSelect');
    if (iproc_id && previousPO != iproc_id) {
        return Api.request.get('/processes/' + iproc_id + '/shared-variables').then(function(data) {
            previousPO = iproc_id;
            ...
        });
    }
});`,
            ],
        },
        {
            nameTechnical: 'solveTaskOn',
            nameTranslation: 'Solve task',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable whose value change triggers the function. Unlike validateVarOn() it is applied also on variables for reading <Label>',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'If true, the task can be solved.',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
            ],
            description: 'If <i>condition</i> is false, it prevents solving the task.',
            isMajor: true,
            examples: [
`solveTaskOn('Duplicity', function() {
    return fnIsNotEmptySync(v('Výstraha')); // případně vSync('Duplicity') === true
});`,
            ],
        },
        {
            nameTechnical: 'validateVarOn',
            nameTranslation: 'Validate variable',
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['Array', 'String'],
                    description: 'The name of the variable that we are validating, or array of variables',
                },
                {
                    name: 'condition',
                    types: ['Function', 'Boolean'],
                    description: 'If true, variable will be validated. If false, variable will be defaultly validated (duty, number...)',
                },
                {
                    name: 'validations',
                    types: ['Function', 'Object'],
                    description: 'List of validations (see Validations)',
                },
                {
                    name: 'validationErrors',
                    types: ['Object', 'Function'],
                    description: 'Message(s) that will appear in case of invalid variable. { isLowerThan: \'Datum1 je větší než datum2.\' } or with translations { isLowerThan: {cs: \'Datum1 je větší než datum2.\', en: \'Date1 is higher than date2.\'} }',
                },
                {
                    name: 'watchVars',
                    types: ['Array', 'String'],
                    description: 'tvar_name or array of variables tvar_name whose change causes the script execution',
                },
            ],
            description: 'Adds <i>validations</i> and <i>validationErrors</i> to variable with <i>tvar_name</i>, if <i>condition</i> true.',
            isMajor: true,
            examples: [
`validateVarOn('datum1', true, function() {
    return { isLowerThan: vSync('datum2') };
}, { isLowerThan: 'Datum1 je větší než datum2.' });`,
`// Without translations
validateVarOn('datum1', true, { isLowerThan: vSync('datum2') }, { isLowerThan: 'Datum1 je větší než datum2.' });`,
`// With translations
validateVarOn('datum1', true, { isLowerThan: vSync('datum2') }, { isLowerThan: {cs: 'Datum1 je větší než datum2.', en: 'Date1 is higher than date2.'} });`,
`// General custom validation with data from DT
var _dtSuppliers = vSync('_dtSuppliers');
getDynTable(_dtSuppliers);

validateVarOn('supplier', true,
  function() {
    var foundSupplier = _.find(dynTable[_dtSuppliers], 'col_8', vSync('supplier'));
    return {
      isValid: typeof foundSupplier != 'undefined'
    }
  },
  { isValid: {cs: 'Neexistující dodavatel.', en: 'Supplier does not exist.'} }
);`,
`// Validation with variables in validationErrors
var max;
var min;
validateVarOn('quantity', true,
  function() {
    var findRow = _.find(dynTable[dtRequestsItems], 'col_5', vSync('typOfCard'));
    max = Number(findRow.col_13);
    min = Number(findRow.col_14);

    return {
        isValid: (vSync('quantity') >= min) && (vSync('quantity') <= max)
    }
  },
  function() {
    return { isValid: {cs: 'Hodnota je rozdílná než povolené množství: min '+min+', max '+max+'.', en: 'Value is different from alowed quantity: min '+min+', max '+max+'.'} };
  }
);`,
            ],
        },
        {
            nameTechnical: 'repeatScript',
            nameTranslation: 'Repeat script',
            arguments: [],
            description: 'Manually reruns the entire script.',
            isMajor: true,
            examples: [
`repeatScript();`,
            ],
        },

        // --------------------------------------------------- Non-major functions ---------------------------------------------------

        // variable value getters
        ...(([
            [
                'v',
                'Returns variable value.',
                [
`/* Works dynamically */
changeVarVal('Cena s DPH', v('Jméno objednatele') != null, 1000);
hideVarOn('Místo začátku pracovní cesty', fnEq(v('Nepřítomnost'), 'Služební cesta'));`,
`/* It only works when the form is loaded */
var promenna = fnEqSync(v('Nepřítomnost'), 'Služební cesta');`,
                ],
            ],
            [
                'vSync',
                'Returns variable value.',
                [
`/* Works dynamically */
hideVarOn('Místo začátku pracovní cesty', function() {
    return vSync('Nepřítomnost') === 'Služební cesta';
});`,
`/* It only works when the form is loaded */
hideVarOn('Místo začátku pracovní cesty', vSync('Nepřítomnost') === 'Služební cesta');`,
                ],
            ],
            [
                'vObj, vSyncObj',
                'Returns variable value.\nFor variables of type Select, it returns an object { title, value }.\nNote: if the value of the variable is changed by dynamic conditions and id is set as its value, it returns the id.',
                [],
            ],
            [
                'procV, procVSync',
                'Returns process variable value.',
                [],
            ],
            [
                'procVObj, procVObjSync',
                'Returns process variable value.\nFor variables of type Select, it returns an object { title, value }.',
                [],
            ],
        ] as ([string, string, string[]])[]).map((([name, description, examples]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'tvar_name',
                    types: ['String'] as ['String'],
                    description: 'Variable name',
                },
            ],
            description: description,
            isMajor: false,
            examples: examples,
        })))),

        // two-argument comparison functions
        ...([
            ['fnEq, fnEqSync', 'Compares two values (===). Arguments can also be functions.'],
            ['fnEqArray, fnEqArraySync', 'Returns true, if <i>value</i> equals (===) to at least one value from the array. Argument <i>value</i> can also be a function.'],
            ['fnNe, fnNeSync', 'Compares two values (!==). Arguments can also be functions.'],
            ['fnNeArray, fnNeArraySync', 'Returns true, if <i>value</i> does not equal (!==) to any value from the array. Argument <i>value</i> can also be a function.'],
            ['fnGt, fnGtSync', 'Returns true, if <i>value1</i> is greater than <i>value2</i>. Arguments can also be functions.'],
            ['fnGe, fnGeSync', 'Returns true, if <i>value1</i> is greater than or equal to <i>value2</i>. Arguments can also be functions.'],
            ['fnLt, fnLtSync', 'Returns true, if <i>value1</i> is lower than <i>value2</i>. Arguments can also be functions.'],
            ['fnLe, fnLeSync', 'Returns true, if <i>value1</i> is lower than or equal to <i>value2</i>. Arguments can also be functions.'],
            ['fnLike, fnLikeSync', 'Returns true, if <i>value1</i> contains <i>value2</i>.'],
            ['fnNLike, fnNLikeSync', 'Returns true, if <i>value1</i> des not contain <i>value2</i>.'],
        ].map((([name, description]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'value1',
                    types: ['Any'] as ['Any'],
                    description: 'First value',
                },
                {
                    name: 'value2',
                    types: ['Any'] as ['Any'],
                    description: 'Second value',
                },
            ],
            description: description,
            isMajor: false,
            examples: [],
        })))),

        // one-argument comparison functions
        ...([
            ['fnIsEmpty, fnIsEmptySync', 'Given value is undefined, null, "", [] or {}.'],
            ['fnIsNotEmpty, fnIsNotEmptySync', 'Given value is not undefined, null, "", [] nor {}.'],
        ].map((([name, description]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'value',
                    types: ['Any'] as ['Any'],
                    description: 'Value',
                },
            ],
            description: description,
            isMajor: false,
            examples: [],
        })))),

        // logical operations
        ...([
            ['fnAnd, fnAndSync', 'Logical AND of given functions or values.'],
            ['fnOr, fnOrSync', 'Logical OR of given functions or values.'],
        ].map((([name, description]) => ({
            nameTechnical: name,
            nameTranslation: null,
            arguments: [
                {
                    name: 'valueOrFunction1',
                    types: ['Any', 'Function'] as ['Any', 'Function'],
                    description: 'Value or a function which returns a value',
                },
                {
                    name: 'valueOrFunction2',
                    types: ['Any', 'Function'] as ['Any', 'Function'],
                    description: 'Value or a function which returns a value',
                },
                {
                    name: '...',
                    types: ['Any', 'Function'] as ['Any', 'Function'],
                    description: 'More values or functions which return values',
                },
            ],
            description: description,
            isMajor: false,
            examples: [],
        })))),

        // other non-major functions
        {
            nameTechnical: 'jsonSumArray, jsonSumArraySync',
            nameTranslation: null,
            arguments: [
                {
                    name: 'json',
                    types: ['String', 'Object'] as ['String', 'Object'],
                    description: 'JSON in string or json format',
                },
                {
                    name: 'pathInJson',
                    types: ['String'],
                    description: 'Path to property, i.e. obj.arr',
                },
            ],
            description: 'Json in string/json format. Like {obj: {ignored: true, arr: [1, 2, 3]}}. String pathInJson is path to array property like obj.arr. Returns number.',
            isMajor: false,
            examples: [],
        },
        {
            nameTechnical: 'formatEmails, formatEmailsSync',
            nameTranslation: null,
            arguments: [
                {
                    name: 'emails',
                    types: ['Array'],
                    description: 'Email adresses',
                },
            ],
            description: 'Formats email addresses to individual addresses separated by semicolon. Used to in combination with changeVarVal() with <i>force</i> parameter.',
            isMajor: false,
            examples: [],
        },
        {
            nameTechnical: 'canSolveTask',
            nameTranslation: null,
            arguments: [
                {
                    name: 'bool',
                    types: ['Boolean'],
                    description: 'Allow the user to solve the task',
                },
            ],
            description: 'If <i>false</i> is given as the argument, prevents user from solving the task by graying the "Save" and "Just save" buttons.',
            isMajor: false,
            examples: [],
        },
    ],
    validations: {
        name: 'Validations',
        innerTitle: 'List of defined validations over variables',
        validations: [
            { name: 'isExisty', allowedValuesString: 'true', description: 'Not null nor undefined.' },
            { name: 'matchRegexp', allowedValuesString: 'Regex', description: 'Test for any regular expression.' },
            { name: 'isUndefined', allowedValuesString: 'true', description: 'value === undefined' },
            { name: 'isEmptyString', allowedValuesString: 'true', description: 'Empty string.' },
            { name: 'isEmail', allowedValuesString: 'true', description: 'Email address.' },
            { name: 'isEmails', allowedValuesString: 'true', description: 'Email addresses separated by semicolon.' },
            { name: 'isUrl', allowedValuesString: 'true', description: 'Returns true, if valid url.' },
            { name: 'isTrue', allowedValuesString: 'true', description: 'value === true' },
            { name: 'isFalse', allowedValuesString: 'true', description: 'value === false' },
            { name: 'isNumeric', allowedValuesString: 'true', description: 'Is type of Number (42; -3.14).' },
            { name: 'isAlpha', allowedValuesString: 'true', description: 'Contains only letters.' },
            { name: 'isAlphanumeric', allowedValuesString: 'true', description: 'Contains only letters or numbers.' },
            { name: 'isInt', allowedValuesString: 'true', description: 'Integer (42; -12; 0).' },
            { name: 'isFloat', allowedValuesString: 'true', description: 'Float (42; -3.14; 1e3).' },
            { name: 'isWords', allowedValuesString: 'true', description: 'As isAlpha including spaces and tabs.' },
            { name: 'isSpecialWords', allowedValuesString: 'true', description: 'As isAlpha including special letters (a-z,ú,ø,æ,å).' },
            { name: 'isLength', allowedValuesString: 'Number', description: 'Returns true if the value length is the equal.' },
            { name: 'equals', allowedValuesString: 'value', description: 'ivar_value == value.' },
            { name: 'equalsField', allowedValuesString: 'field', description: 'ivar_value == variables[field].' },
            { name: 'maxLength', allowedValuesString: 'Number', description: 'Is lower or equal.' },
            { name: 'minLength', allowedValuesString: 'Number', description: 'Is greater or equal.' },
            { name: 'isIC', allowedValuesString: 'true', description: 'Identification number.' },
            { name: 'isTelephoneNumber', allowedValuesString: 'true', description: 'Telephone number.' },
            { name: 'minValue', allowedValuesString: 'Number', description: 'Is higher or equal.' },
            { name: 'maxValue', allowedValuesString: 'Number', description: 'Is lower or equal.' },
            { name: 'isLowerThan', allowedValuesString: 'Number, Date', description: 'Is lower.' },
            { name: 'isHigherThan', allowedValuesString: 'Number, Date', description: 'Is higher.' },
            { name: 'isLowerOrEqualThan', allowedValuesString: 'Number, Date', description: 'Is lower or equals.' },
            { name: 'isHigherOrEqualThan', allowedValuesString: 'Number, Date', description: 'Is higher or equals.' },
            { name: 'isDate', allowedValuesString: 'value', description: 'Date in the right format according to localization.' },
            { name: 'isPath', allowedValuesString: 'true', description: 'Path to file.' },
            { name: 'isValid', allowedValuesString: 'Boolean', description: 'Possibility of any custom comparison, if returns false custom validation message is shown.' },
        ],
    },
};

export default dynamicConditionsScriptHelpEn;
