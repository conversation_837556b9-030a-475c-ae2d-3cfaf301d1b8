import React, { useMemo, useRef, useState } from 'react';
import { debounce } from 'lodash';
// mui
import {
    Box,
    DialogContent,
    DialogTitle,
    MenuItem,
    Stack,
    Theme,
} from '@mui/material';
// components
import Button from '../../../Button';
import MyIcon from '../../../MyIcon';
import Search from '../../../Search';
import CustomPopper, { usePopover } from '../../../custom-popover';
import ModalHelpDynamicConditionsScriptSectionFunction from './sections/ModalHelpDynamicConditionsScriptSectionFunction';
import ModalHelpDynamicConditionsScriptSectionValidations from './sections/ModalHelpDynamicConditionsScriptSectionValidations';
// utils
import { pxToRem } from '../../../../theme/typography';
import getSanitizedScriptHelp from './language-variants/getSanitizedScriptHelp';
// help
import dynamicConditionsScriptHelpCs from './language-variants/dynamicConditionsScriptHelpCs';
import dynamicConditionsScriptHelpEn from './language-variants/dynamicConditionsScriptHelpEn';
// types
import { DynamicConditionsScriptHelp, DynamicConditionsScriptHelpFunction } from './types';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';

const ModalHelpDynamicConditionsScriptLayout = ({
    hidden = false,
} : {
    /** If true, it will have `display: none` style */
    hidden?: boolean;
}) => {
    const [tab, setTab] = useState<`major-${number}` | `nonMajor-${number}` | 'validations' | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [userLanguage] = useState(LoggedUserStore.getState().userLanguage);

    const scrollableRef = useRef<HTMLDivElement>(null);

    const dynamicConditionsScriptHelp = useMemo<DynamicConditionsScriptHelp<true>>(() => (
        getSanitizedScriptHelp(
            userLanguage === 'cs'
                ? dynamicConditionsScriptHelpCs
                : dynamicConditionsScriptHelpEn,
        )
    ), []);

    const [functions, setFunctions] = useState(
        dynamicConditionsScriptHelp.functions,
    );
    const [showValidations, setShowValidations] = useState(true);

    const [majorFunctionTabs, setMajorFunctionTabs] = useState(
        dynamicConditionsScriptHelp.functions.filter(
            (func) => func.isMajor,
        ),
    );
    const [nonMajorFunctionTabs, setNonMajorFunctionTabs] = useState(
        dynamicConditionsScriptHelp.functions.filter(
            (func) => !func.isMajor,
        ),
    );
    const [showValidationsTab, setShowValidationsTab] = useState(true);

    const nonMajorFunctionsPopper = usePopover();

    const functionMatchesSearchQuery = (func: DynamicConditionsScriptHelpFunction<true>, query: string) => {
        return (
            func.nameTechnical.toLocaleLowerCase().includes(query.toLocaleLowerCase())
            || func.nameTranslation?.toLocaleLowerCase().includes(query.toLocaleLowerCase())
            || func.sanitizedDescription.toLocaleLowerCase().includes(query.toLocaleLowerCase())
        );
    };

    const validationsSectionMatchesSearchQuery = (query: string) => {
        return dynamicConditionsScriptHelp.validationsSectionNameTranslation.toLocaleLowerCase().includes(query.toLocaleLowerCase());
    };

    const updateTabsBasedOnSearchQuery = (query?: string) => {
        const queryToUse = typeof query === 'undefined' ? searchQuery : query;

        const filteredMajorFunctions = dynamicConditionsScriptHelp.functions.filter((func) => (
            func.isMajor && functionMatchesSearchQuery(func, queryToUse)
        ));

        const filteredNonMajorFunctions = dynamicConditionsScriptHelp.functions.filter((func) => (
            !func.isMajor && functionMatchesSearchQuery(func, queryToUse)
        ));

        setMajorFunctionTabs(filteredMajorFunctions);
        setNonMajorFunctionTabs(filteredNonMajorFunctions);
        setShowValidationsTab(validationsSectionMatchesSearchQuery(queryToUse));
    };

    const updateContentBasedOnSearchQuery = (query?: string) => {
        const queryToUse = typeof query === 'undefined' ? searchQuery : query;

        setFunctions(
            dynamicConditionsScriptHelp.functions.filter((func) => (
                functionMatchesSearchQuery(func, queryToUse)
            )),
        );

        setShowValidations(validationsSectionMatchesSearchQuery(queryToUse));
    };

    const handleSetTab = (newTab: typeof tab) => {
        if (tab === newTab) {
            setTab(null);
        } else {
            setTab(newTab);
        }

        if (tab === newTab || newTab === null) {
            updateContentBasedOnSearchQuery();
        } else if (newTab === 'validations') {
            setFunctions([]);
            setShowValidations(true);
        } else {
            const isMajorTab = newTab.startsWith('major-');
            const index = Number(newTab.split('-')[1]);
            const selectedFunction = isMajorTab
                ? majorFunctionTabs[index]
                : nonMajorFunctionTabs[index];

            setFunctions([selectedFunction]);
            setShowValidations(false);
        }

        if (nonMajorFunctionsPopper.open) {
            nonMajorFunctionsPopper.onClose();
        }

        scrollableRef.current?.scrollTo({
            top: 0,
            behavior: 'instant',
        });
    };

    const handleSearchQueryChange = debounce(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setTab(null);
            setSearchQuery(e.target.value);
            updateContentBasedOnSearchQuery(e.target.value);
            updateTabsBasedOnSearchQuery(e.target.value);

            scrollableRef.current?.scrollTo({
                top: 0,
                behavior: 'instant',
            });
        },
        500,
    );

    const showTabsDialogTitle = (
        majorFunctionTabs.length > 0 ||
        nonMajorFunctionTabs.length > 0 ||
        showValidationsTab
    );

    const validationsMatchSearchQuery = validationsSectionMatchesSearchQuery(searchQuery);

    const renderValidationsTabAfterValidateVarOnTab = (
        majorFunctionTabs.some((func) => func.nameTechnical === 'validateVarOn')
    ) && validationsMatchSearchQuery;

    const renderValidationsAfterValidateVarOn = (
        functions.some((func) => func.nameTechnical === 'validateVarOn')
    ) && validationsMatchSearchQuery;

    return (
        <>
            <DialogTitle
                sx={{
                    borderBottom: (theme: Theme) => `1px solid ${theme.palette.divider}`,
                    display: hidden ? 'none' : 'block',
                }}
            >
                <Stack
                    direction="row"
                    sx={{
                        justifyContent: 'space-between',
                        flexGrow: 1,
                        gap: 1,
                    }}
                >
                    <Box
                        sx={{
                            flexGrow: 1,
                            maxWidth: pxToRem(500),
                            width: '100%',
                        }}
                    >
                        <Search
                            onChange={handleSearchQueryChange}
                            placeholder={dynamicConditionsScriptHelp.searchPlaceholderTranslation}
                        />
                    </Box>
                    <Stack
                        direction="row"
                        sx={{
                            justifyContent: 'flex-end',
                            alignItems: 'center',
                            flexGrow: 1,
                            gap: 1,
                        }}
                    >
                        <a
                            href="https://lodash.com/docs/4.17.15"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <Button
                                color="secondary"
                                startIcon={<MyIcon icon="link" />}
                                label={dynamicConditionsScriptHelp.functionsButtonTranslation}
                                variant="text"
                                size="small"
                            />
                        </a>
                        <a
                            href="https://tas.docs.apiary.io/"
                            target="_blank"
                            rel="noopener noreferrer"
                        >
                            <Button
                                color="secondary"
                                startIcon={<MyIcon icon="link" />}
                                label={dynamicConditionsScriptHelp.servicesButtonTranslation}
                                variant="text"
                                size="small"
                            />
                        </a>
                    </Stack>
                </Stack>
            </DialogTitle>
            {showTabsDialogTitle && (
                <DialogTitle
                    sx={{
                        maxHeight: `calc(${pxToRem(120)} + 1rem)`, // 120px for the Stack height with 3 rows of buttons & 1rem for the padding
                        overflowY: 'auto',
                        display: hidden ? 'none' : 'block',
                    }}
                >
                    <Stack
                        direction="row"
                        sx={{
                            width: '100%',
                            justifyContent: 'flex-start',
                            flexWrap: 'wrap',
                            flexGrow: 1,
                            gap: 1,
                        }}
                    >
                        {majorFunctionTabs.map((func, index) => (
                            <React.Fragment key={func.nameTechnical}>
                                <Button
                                    label={func.nameTechnical}
                                    color="secondary"
                                    onClick={() => handleSetTab(`major-${index}`)}
                                    variant="text"
                                    size="small"
                                    sx={{
                                        backgroundColor: (theme) => (
                                            tab === `major-${index}` ? theme.palette.primary.light : ''
                                        ),
                                    }}
                                />
                                {(
                                    showValidationsTab
                                    && func.nameTechnical === 'validateVarOn'
                                    && renderValidationsTabAfterValidateVarOnTab
                                ) && (
                                    <Button
                                        label={dynamicConditionsScriptHelp.validationsSectionNameTranslation}
                                        color="secondary"
                                        onClick={() => handleSetTab('validations')}
                                        variant="text"
                                        size="small"
                                        sx={{
                                            backgroundColor: (theme) => (
                                                tab === 'validations' ? theme.palette.primary.light : ''
                                            ),
                                        }}
                                    />
                                )}
                            </React.Fragment>
                        ))}
                        {(
                            showValidationsTab
                            && !renderValidationsTabAfterValidateVarOnTab
                        ) && (
                            <Button
                                label={dynamicConditionsScriptHelp.validationsSectionNameTranslation}
                                color="secondary"
                                onClick={() => handleSetTab('validations')}
                                variant="text"
                                size="small"
                                sx={{
                                    backgroundColor: (theme) => (
                                        tab === 'validations' ? theme.palette.primary.light : ''
                                    ),
                                }}
                            />
                        )}
                        {nonMajorFunctionTabs.length > 0 && (
                            <>
                                <Button
                                    label={dynamicConditionsScriptHelp.moreFunctionsButtonTranslation}
                                    color="secondary"
                                    onClick={nonMajorFunctionsPopper.onOpen}
                                    variant="text"
                                    size="small"
                                    sx={{
                                        backgroundColor: (theme) => (
                                            tab !== 'validations' && tab?.split('-')[0] === 'nonMajor'
                                                ? theme.palette.primary.light
                                                : ''
                                        ),
                                    }}
                                />
                                <CustomPopper
                                    open={nonMajorFunctionsPopper.open}
                                    onClose={nonMajorFunctionsPopper.onClose}
                                    sx={{
                                        maxWidth: '20rem',
                                        maxHeight: '30rem',
                                        overflowY: 'auto',
                                    }}
                                >
                                    {nonMajorFunctionTabs.map((func, index) => (
                                        <MenuItem
                                            key={func.nameTechnical}
                                            onClick={() => handleSetTab(`nonMajor-${index}`)}
                                            sx={{
                                                whiteSpace: 'break-spaces',
                                                backgroundColor: (theme) => (
                                                    tab === `nonMajor-${index}` ? theme.palette.primary.light : ''
                                                ),
                                            }}
                                        >
                                            {func.nameTechnical}
                                        </MenuItem>
                                    ))}
                                </CustomPopper>
                            </>
                        )}
                    </Stack>
                </DialogTitle>
            )}
            <DialogContent
                ref={scrollableRef}
                dividers={showTabsDialogTitle}
                sx={{
                    height: '100%',
                    width: '100%',
                    backgroundColor: (theme) => theme.palette.background.default,
                    display: hidden ? 'none' : 'block',
                }}
            >
                <Stack spacing={2}>
                    {functions.map((scriptFunction) => (
                        <React.Fragment key={scriptFunction.nameTechnical}>
                            <ModalHelpDynamicConditionsScriptSectionFunction
                                scriptFunction={scriptFunction}
                                examplesSectionNameTranslation={
                                    dynamicConditionsScriptHelp.examplesSectionNameTranslation
                                }
                                parametersSectionNameTranslation={
                                    dynamicConditionsScriptHelp.parametersSectionNameTranslation
                                }
                            />
                            {(
                                showValidations
                                && scriptFunction.nameTechnical === 'validateVarOn'
                                && renderValidationsAfterValidateVarOn
                            ) && (
                                <ModalHelpDynamicConditionsScriptSectionValidations
                                    validations={dynamicConditionsScriptHelp.validations}
                                />
                            )}
                        </React.Fragment>
                    ))}
                    {(
                        showValidations
                        && !renderValidationsAfterValidateVarOn
                    ) && (
                        <ModalHelpDynamicConditionsScriptSectionValidations
                            validations={dynamicConditionsScriptHelp.validations}
                        />
                    )}
                </Stack>
            </DialogContent>
        </>
    );
};

export default ModalHelpDynamicConditionsScriptLayout;
