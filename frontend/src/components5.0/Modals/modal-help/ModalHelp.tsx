import { I18nextProvider, useTranslation } from 'react-i18next';
import React, { useEffect, useRef, useState } from 'react';
import i18next from 'i18next';
// @mui
import {
    Dialog, dialogClasses, DialogTitle, Stack,
} from '@mui/material';
import { Theme } from '@mui/material/styles';
// i18n
import i18n from '../../../common/i18n';
// components
import MyIcon from '../../MyIcon';
import TextMaxLine from '../../text-max-line';
import ResponsiveButton from '../../ResponsiveButton';
import DraggablePaperWrapper from '../DraggablePaperWrapper';
import ModalHelpDynamicConditionsBuilderLayout from './dynamic-conditions-builder/ModalHelpDynamicConditionsBuilderLayout';
import ModalHelpDynamicConditionsScriptLayout from './dynamic-conditions-script/ModalHelpDynamicConditionsScriptLayout';
// hooks
import { useResponsive } from '../../hooks/use-responsive';
// utils
import { remToPx } from '../../../theme/typography';
// types
import { ModalHelpProps } from './types';
// flux
import LoggedUserStore from '../../../flux/loggedUser.store';

const ModalHelp = ({
    isOpen,
    onClose,
    helpFor,
    onUseInBuilder,
    keepMounted,
}: ModalHelpProps) => {
    const [useRndPositionInfo, setUseRndPositionInfo] = useState<boolean>(true);
    const [userLanguage] = useState(LoggedUserStore.getState().userLanguage);

    // help is only available in english and czech - using english for i18next as well keeps the translations consistent
    const [i18nextInstanceForHelpContent, setI18nextInstanceForHelpContent] = useState<typeof i18n>(i18n);

    const paperMargin = remToPx('2rem');
    const paperHeight = window.innerHeight - paperMargin * 2;
    const paperMaxWidth = remToPx('75rem'); // 1200px for base font size 16
    const paperWidth = window.innerWidth > paperMaxWidth + paperMargin * 2 ? paperMaxWidth : window.innerWidth - paperMargin * 2;

    const rndPositionInfo = useRef<{
        x: number;
        y: number;
        width: number;
        height: number;
    }>({
        x: window.innerWidth / 2 - paperWidth / 2,
        y: window.innerHeight / 2 - paperHeight / 2,
        width: paperWidth,
        height: paperHeight,
    });

    const smUp = useResponsive('up', 'sm');
    const { t } = useTranslation();

    useEffect(() => {
        if (!smUp) {
            setUseRndPositionInfo(false);
            return;
        }

        // after the dialog is opened, switch to uncontrolled mode for Rnd's position and size props
        // (we only need the props to remember the position and size when the dialog is closed)
        // (if we always used it in controlled mode, the resizing and dragging wouldn't be so smooth)
        if (isOpen) {
            setUseRndPositionInfo(false);
        } else {
            setUseRndPositionInfo(true);
        }
    }, [isOpen, smUp]);

    useEffect(() => {
        if (
            userLanguage !== 'cs'
            && userLanguage !== 'en'
            && !i18next.hasResourceBundle('en', 'common') // en not loaded yet
        ) {
            // additionaly load english for i18next translations used inside help content
            // (help is only available in english and czech - using english for i18next as well keeps the translations consistent)
            i18next.loadLanguages(['en'], () => {
                setI18nextInstanceForHelpContent(
                    i18next.cloneInstance({ lng: 'en' }),
                );
            });
        }
    }, []);

    return (
        <Dialog
            PaperComponent={smUp ? DraggablePaperWrapper : undefined}
            PaperProps={
                smUp
                    ? {
                        handlePosition: false,
                        sx: {
                            margin: 0,
                            zIndex: 1300,
                            width: '100%',
                            height: '100%',
                            maxWidth: 'none !important',
                            maxHeight: 'none !important',
                        },
                        rndProps: {
                            default: rndPositionInfo.current,
                            enableResizing: isOpen,
                            disableDragging: !isOpen,
                            style: {
                                boxShadow: 'none',
                                border: 'none',
                                background: 'none',
                                // removed visibility, other styles are same as in DraggablePaperWrapper
                            },
                            ...(useRndPositionInfo && {
                                size: {
                                    width: rndPositionInfo.current.width,
                                    height: rndPositionInfo.current.height,
                                },
                                position: {
                                    x: rndPositionInfo.current.x,
                                    y: rndPositionInfo.current.y,
                                },
                            }),
                            onDragStop: (e: any, d: any) => {
                                rndPositionInfo.current = {
                                    ...rndPositionInfo.current,
                                    x: d.x,
                                    y: d.y,
                                };
                            },
                            onResize: (e: any, direction: any, ref: any, delta: any, position: any) => {
                                rndPositionInfo.current = {
                                    ...position,
                                    width: ref.offsetWidth,
                                    height: ref.offsetHeight,
                                };
                            },
                        },
                    }
                    : undefined
            }
            hideBackdrop
            disableEnforceFocus
            keepMounted={keepMounted}
            fullScreen={!smUp}
            onClose={(e, reason) => {
                if (reason !== 'backdropClick') {
                    onClose();
                }
            }}
            aria-labelledby="customized-dialog-title"
            open={isOpen}
            sx={{
                ...(smUp && {
                    [`&.${dialogClasses.root}`]: {
                        // position: 'static',
                        height: 0,
                    },
                    '& #draggable-paper': {
                        zIndex: 1300,
                    },
                }),
            }}
        >
            <DialogTitle
                className="draggable-dialog-title"
                sx={{
                    ...(isOpen ? { cursor: 'move' } : {}),
                    borderBottom: (theme: Theme) => `1px solid ${theme.palette.divider}`,
                }}
            >
                <Stack
                    direction="row"
                    sx={{
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <Stack
                        direction="row"
                        spacing={1}
                        sx={{
                            alignItems: 'center',
                            height: 1,
                            mr: 3,
                        }}
                    >
                        <MyIcon
                            icon="informative"
                            sx={{
                                color: (theme) => theme.palette.primary.main,
                            }}
                        />
                        <TextMaxLine
                            line={1}
                            variant="h1"
                            id="customized-dialog-title"
                            sx={{
                                color: (theme) => theme.palette.text.primary,
                            }}
                        >
                            {t('help')}
                        </TextMaxLine>
                    </Stack>
                    <Stack
                        direction="row"
                        spacing={1}
                        sx={{
                            alignItems: 'center',
                        }}
                    >
                        <ResponsiveButton
                            icon="close"
                            color="secondary"
                            label={t('close')}
                            ariaLabel={t('close') as string}
                            buttonSize="small"
                            iconButtonSize="large"
                            variant="text"
                            onClick={onClose}
                            className="non-draggable"
                        />
                    </Stack>
                </Stack>
            </DialogTitle>
            <I18nextProvider i18n={i18nextInstanceForHelpContent}>
                {( // modal used in two versions, but in the same context
                    helpFor === 'dynamicConditionsBuilder'
                    || helpFor === 'dynamicConditionsScript'
                ) && (
                    <>
                        {/*
                            keep both layouts mounted and only hide the one of them when the other one is to be visible
                            - this way the user can switch between them without losing the scroll position, selected tabs, search query, etc.
                            - the downside is that both layouts are always rendered in the DOM
                        */}
                        <ModalHelpDynamicConditionsBuilderLayout
                            hidden={helpFor !== 'dynamicConditionsBuilder'}
                            onUseInBuilder={onUseInBuilder}
                        />
                        <ModalHelpDynamicConditionsScriptLayout
                            hidden={helpFor !== 'dynamicConditionsScript'}
                        />
                    </>
                )}
            </I18nextProvider>
        </Dialog>
    );
};

export default ModalHelp;
