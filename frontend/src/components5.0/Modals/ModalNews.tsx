import { useState } from 'react';
// @mui
import {
    Box,
    Chip,
    Dialog, DialogContent,
    DialogTitle,
    Divider,
    formControlLabelClasses,
    Link,
    Stack,
    Typography,
    typographyClasses,
} from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { useResponsive } from '../hooks/use-responsive';
// utils
import { checkLangMutation, utilSanitizeHtml } from '../../common/utils';
import { pxToRem } from '../../theme/typography';
// components
import TextReadOnly from '../form/textReadOnly/TextReadOnly';
import Label from '../label';
import MyIcon from '../MyIcon';
import ResponsiveButton from '../ResponsiveButton';
import FormLabel from '../form/FormLabel';
import PagingLabel from '../PagingLabel';
// types
import { NewsPostTypeGET } from '../sections/administration/news-management/types';

const ModalNews = ({
    isOpen,
    onClose,
    posts,
    openedPostIndex: openedPostIndexProp = 0,
    pagingLabelTitle,
}: {
    isOpen: boolean;
    onClose: VoidFunction;
    posts: (
        Pick<
            NewsPostTypeGET,
            'post_title'
            | 'post_content'
            | 'post_publication_date'
            | 'post_publication_end_date'
            | 'post_tags'
            | 'post_phone'
            | 'post_email'
            | 'post_custom_url'
        >
    )[];
    openedPostIndex?: number;
    pagingLabelTitle?: string;
}) => {
    const { t } = useTranslation();
    const smUp = useResponsive('up', 'sm');

    const [openedPostIndex, setOpenedPostIndex] = useState<number>(openedPostIndexProp);

    return (
        <Dialog
            fullScreen={!smUp}
            maxWidth="md"
            scroll="paper"
            fullWidth
            onClose={() => onClose()}
            aria-labelledby="news-dialog-title"
            aria-describedby="news-dialog-description"
            open={isOpen}
        >
            <DialogTitle>
                <Stack
                    direction="row"
                    justifyContent="space-between"
                    alignItems="center"
                    sx={{
                        m: 0,
                    }}
                >
                    <Stack direction="row" spacing={1} height={1} alignItems="center">
                        <MyIcon
                            icon="news-filled"
                            sx={{
                                color: (theme) => theme.palette.primary.main,
                            }}
                        />
                        <Typography id="news-dialog-title" variant="h1" height={1}>
                            {checkLangMutation(posts[openedPostIndex], 'post_title')}
                        </Typography>
                    </Stack>
                    <Stack direction="row" spacing={1} height={1} alignItems="right">
                        <ResponsiveButton
                            icon="close"
                            color="secondary"
                            label={t('close')}
                            ariaLabel={t('close') as string}
                            buttonSize="small"
                            iconButtonSize="large"
                            variant="text"
                            onClick={onClose}
                        />
                    </Stack>
                </Stack>
            </DialogTitle>
            <Divider />
            <DialogContent
                id="news-dialog-description"
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    height: smUp ? '90vh' : 'auto',
                    ...(smUp && {
                        px: 0,
                    }),
                    ...(posts.length > 1 && {
                        // 34 - height of PagingLabel, 32 - 2x1rem normal DialogContent padding
                        paddingBottom: `${pxToRem(34 + 32)} !important`,
                    }),
                }}
            >
                <Stack
                    direction="column"
                    spacing={3}
                    sx={{
                        height: '100%',
                        minHeight: 'fit-content',
                    }}
                >
                    <Stack>
                        <Stack
                            direction="row"
                            sx={{
                                columnGap: '1.5rem',
                                flexWrap: 'wrap',
                            }}
                        >
                            <TextReadOnly
                                label={t('published')}
                                value={posts[openedPostIndex].post_publication_date}
                                type="D"
                                dateWithoutTime
                                sx={{
                                    width: 'auto',
                                    minWidth: 'fit-content',
                                    [`.${formControlLabelClasses.root}`]: {
                                        minWidth: 'fit-content',
                                        whiteSpace: 'nowrap',
                                    },
                                    [`.${typographyClasses.root}`]: {
                                        minWidth: 'fit-content',
                                        whiteSpace: 'nowrap',
                                    },
                                }}
                            />
                            {posts[openedPostIndex].post_publication_end_date && (
                                <TextReadOnly
                                    label={t('avaibleUntil')}
                                    value={posts[openedPostIndex].post_publication_end_date}
                                    type="D"
                                    dateWithoutTime
                                    sx={{
                                        width: 'auto',
                                        minWidth: 'fit-content',
                                        [`.${formControlLabelClasses.root}`]: {
                                            minWidth: 'fit-content',
                                            whiteSpace: 'nowrap',
                                        },
                                        [`.${typographyClasses.root}`]: {
                                            minWidth: 'fit-content',
                                            whiteSpace: 'nowrap',
                                        },
                                    }}
                                />
                            )}
                            {posts[openedPostIndex].post_tags.length > 0 && (
                                <Stack
                                    sx={{
                                        paddingBottom: pxToRem(13),
                                    }}
                                >
                                    <FormLabel>
                                        {t('tags')}
                                    </FormLabel>
                                    <Stack
                                        direction="row"
                                        sx={{
                                            flexWrap: 'wrap',
                                            pt: pxToRem(7),
                                            paddingLeft: '1rem',
                                            columnGap: '0.5rem',
                                            rowGap: '0.5rem',
                                        }}
                                    >
                                        {posts[openedPostIndex].post_tags.map((tag) => (
                                            <Label
                                                key={tag.tag_name}
                                                color="primary"
                                                size="small"
                                                sx={{
                                                    marginLeft: 0,
                                                }}
                                            >
                                                {checkLangMutation(tag, 'tag_name')}
                                            </Label>
                                        ))}
                                    </Stack>
                                </Stack>
                            )}
                        </Stack>
                        <Divider sx={{ mx: '1rem' }} />
                    </Stack>
                    <Box
                        sx={{
                            display: 'flex',
                            width: '100%',
                            maxWidth: '100%',
                            minHeight: 'fit-content',
                            justifyContent: 'start',
                            whiteSpace: 'pre-wrap',
                            overflowX: 'auto',
                            px: '1rem',
                        }}
                    >
                        <Typography
                            dangerouslySetInnerHTML={{
                                __html: utilSanitizeHtml(checkLangMutation(posts[openedPostIndex], 'post_content')),
                            }}
                            variant="body1"
                            sx={{
                                width: '100%',
                            }}
                        />
                    </Box>
                    {(
                        posts[openedPostIndex].post_phone
                        || posts[openedPostIndex].post_email
                        || posts[openedPostIndex].post_custom_url
                    ) && (
                        <Stack
                            direction="column"
                            spacing={2}
                            sx={{
                                px: '1rem',
                            }}
                        >
                            <Divider />
                            <Box
                                sx={{
                                    width: '100%',
                                    display: 'flex',
                                    justifyContent: 'center',
                                }}
                            >
                                <Chip
                                    color="default"
                                    size="medium"
                                    sx={{
                                        width: smUp ? 'auto' : '100%',
                                        maxWidth: smUp ? 'fit-content' : '100%',
                                        px: 3,
                                        py: 3,
                                        ...(!smUp && {
                                            height: 'auto',
                                        }),
                                    }}
                                    label={
                                        <Stack
                                            direction={smUp ? 'row' : 'column'}
                                            spacing={smUp ? 3 : 1}
                                            sx={{
                                                ...(!smUp && {
                                                    alignItems: 'center',
                                                }),
                                            }}
                                        >
                                            <Stack
                                                direction="row"
                                                spacing={1}
                                                sx={{
                                                    color: (theme) => theme.palette.text.secondary,
                                                }}
                                            >
                                                <MyIcon icon="e-mail2" />
                                                <Typography variant="body1" color="text.secondary">
                                                    {t('contacts')}
                                                </Typography>
                                            </Stack>
                                            {posts[openedPostIndex].post_phone && (
                                                <Link
                                                    href={`tel:${posts[openedPostIndex].post_phone}`}
                                                    variant="body1"
                                                    sx={{
                                                        color: (theme) => `${theme.palette.text.primary} !important`,
                                                        textDecorationColor: (theme) => `${theme.palette.text.primary} !important`,
                                                    }}
                                                >
                                                    {posts[openedPostIndex].post_phone}
                                                </Link>
                                            )}
                                            {posts[openedPostIndex].post_email && (
                                                <Link
                                                    href={`mailto:${posts[openedPostIndex].post_email}`}
                                                    variant="body1"
                                                    sx={{
                                                        color: (theme) => `${theme.palette.text.primary} !important`,
                                                        textDecorationColor: (theme) => `${theme.palette.text.primary} !important`,
                                                    }}
                                                >
                                                    {posts[openedPostIndex].post_email}
                                                </Link>
                                            )}
                                            {posts[openedPostIndex].post_custom_url && (
                                                <Link
                                                    href={`http://${posts[openedPostIndex].post_custom_url}`}
                                                    target="_blank"
                                                    variant="body1"
                                                    sx={{
                                                        color: (theme) => `${theme.palette.text.primary} !important`,
                                                        textDecorationColor: (theme) => `${theme.palette.text.primary} !important`,
                                                    }}
                                                >
                                                    {posts[openedPostIndex].post_custom_url}
                                                </Link>
                                            )}
                                        </Stack>
                                    }
                                />
                            </Box>
                        </Stack>
                    )}
                </Stack>
            </DialogContent>
            {posts.length > 1 && (
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: '1rem',
                        px: '1rem',
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <PagingLabel
                        withTitle
                        defaultTitle={pagingLabelTitle || t('totalNewsAmount', { amount: posts.length })}
                        pageAmount={posts.length}
                        selectedPageIndex={openedPostIndex}
                        onPageChange={(index) => setOpenedPostIndex(index)}
                        color="info"
                        sx={{
                            maxWidth: '100%',
                            minWidth: 'calc((100% / 3) * 2)',
                            ...(!smUp && {
                                width: '100%',
                            }),
                        }}
                    />
                </Box>
            )}
        </Dialog>
    );
};

export default ModalNews;
