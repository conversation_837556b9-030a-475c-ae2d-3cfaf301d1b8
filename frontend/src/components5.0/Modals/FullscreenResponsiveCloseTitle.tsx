import React from 'react';
// @mui
import { Box, DialogTitle } from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { useResponsive } from '../hooks';
// components
import IconButton from '../IconButton';
// types
import { TFullscreenResponsiveCloseTitle } from './types';

const FullscreenResponsiveCloseTitle = ({
    onClose,
    id,
}: TFullscreenResponsiveCloseTitle) => {
    const { t } = useTranslation();
    const smUp = useResponsive('up', 'sm');

    return smUp ? (
        <Box
            p="0.75rem 0.75rem 0.5rem 0.75rem"
            display="flex"
            justifyContent="flex-end"
            id={id}
        >
            <IconButton
                color="secondary"
                icon="close"
                size="medium"
                onClick={onClose}
                aria-label={t('close')}
            />
        </Box>
    ) : (
        <DialogTitle
            sx={{ display: 'flex', justifyContent: 'flex-end' }}
            id={id}
        >
            <IconButton
                color="secondary"
                icon="close"
                size="medium"
                onClick={onClose}
                aria-label={t('close')}
            />
        </DialogTitle>
    );
};

export default FullscreenResponsiveCloseTitle;
