// @ts-nocheck
import { useState } from 'react';
import { forEach, find, isEmpty } from 'lodash';
// @mui
import {
    Dialog, DialogTitle, DialogActions, DialogContent, Stack,
} from '@mui/material';
// hooks
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import useDocuments from '../sections/documents/use-documents';
// components
import Checkbox from '../form/checkbox/Checkbox';
import Select from '../form/select/Select';
import Button from '../Button';
import IconButton from '../IconButton';
import MyIcon from '../MyIcon';
import FormProvider from '../form/FormProvider';
// utils
import { guid, checkLangMutation } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';
// flux
import AlertsActions from '../../components/alerts/alerts.actions';
import Factory from '../../flux/factory';
import CasePrintStore from '../../flux/casePrint.store';
import CasePrintActions from '../../flux/casePrint.actions';

// Types for props
interface PrintCaseModalProps {
    onClose: () => void;
    isOpen: boolean;
    taskId?: number;
    caseId: string | number;
    fileName: string;
    archived?: boolean;
    entityName: 'task' | 'case';
}

// Types for state
interface PrintTemplate {
    value: number;
    title: any;
    isReactPrint?: boolean;
}

const PrintCaseModal = ({
    onClose,
    isOpen,
    taskId,
    caseId,
    fileName,
    archived,
    entityName,
}: PrintCaseModalProps) => {
    const [dbPrints, setDbPrints] = useState<PrintTemplate[]>([]);
    const [loading, setLoading] = useState(true);
    const { t } = useTranslation();
    const { getDocumentCount } = useDocuments(entityName, undefined, taskId);

    const loadModalData = async () => {
        const defaultValues = {
            printchoise: null,
            printtype: { value: 'printer', title: t('printer') },
            saveAsAttach: false,
        };

        try {
            const payload = await ApiRequest.get(
                `/processes/${archived ? 'archived/' : ''}${caseId}/prints?order=prnt_order&sort=asc&limit=${(window as any).config.restLimit}`,
            );

            if (payload) {
                const dbPrintsArr: PrintTemplate[] = [];

                payload.items.forEach((item: any) => {
                    const modalItem: PrintTemplate = {
                        value: item.id,
                        title: checkLangMutation(item, 'prnt_name'),
                        isReactPrint: item.prnt_react === 'Y',
                    };
                    dbPrintsArr.push(modalItem);
                });

                setDbPrints(dbPrintsArr);

                if (dbPrintsArr[0]) {
                    defaultValues.printchoise = dbPrintsArr[0];
                }
            }

            return defaultValues;
        } catch (error) {
            // Handle errors here
            AlertsActions.addAlert({
                type: 'alert',
                message: t('alrFailedPrintData'),
                serverError: error,
            });

            return defaultValues;
        } finally {
            setLoading(false); // Ensure loading state is updated in case of success or failure
        }
    };

    const methods = useForm({
        defaultValues: () => loadModalData(),
    });
    const { handleSubmit, watch, setValue } = methods;

    const printType = watch('printtype');

    const closeModal = (e?: React.MouseEvent) => {
        if (e) e.preventDefault();
        onClose();
    };

    // object values to JSON
    const editPrintValues = (values: any) => {
        const editedValues: any = {};

        forEach(values, (value: any, key: any) => {
            if (typeof value === 'object' && !Array.isArray(value)) {
                editedValues[key] = JSON.stringify(value);
            } else {
                editedValues[key] = value;
            }
        });

        return editedValues;
    };

    const openPrintInNewTab = (blob: any, title: any) => {
        // if (window.navigator.msSaveOrOpenBlob) {
        //     window.navigator.msSaveOrOpenBlob(blob, title);
        // } else {
        const fileURL = URL.createObjectURL(blob);
        window.open(fileURL);
        // }
    };

    const handlePrintAction = async (data: any) => {
        // const { tabActionAttach } = parent;
        const title = fileName;
        const printtype = data.printtype.value;
        const printchoise = data.printchoise.value;
        const printId = printchoise === -2 ? -1 : printchoise; // default
        const casePrintObj = Factory.registerFlux(
            CasePrintStore,
            CasePrintActions,
            `casePrint-${caseId}-${printId}`,
        );
        const casePrintStore = casePrintObj.store;
        const printValuesObj = editPrintValues(casePrintStore.getState().printValuesObj);

        // find out if it's a React print
        const findPrint = find(dbPrints, {
            value: printchoise,
        });

        // custom print
        if (findPrint && findPrint.isReactPrint) {
            if (printtype === 'printer') {
                if (printId !== -1) {
                    closeModal();

                    let urlParams = '?print=true';
                    let urlValues = '';

                    if (!isEmpty(printValuesObj)) {
                        forEach(printValuesObj, (value: any, key: any) => {
                            if (typeof value === 'number') {
                                urlValues += `&${key}=N~${value}`;
                            } else if (Array.isArray(value)) {
                                urlValues += `&${key}=${JSON.stringify(value)}`;
                            } else {
                                urlValues += `&${key}=${value}`;
                            }
                        });
                    }

                    if (!isEmpty(printValuesObj)) {
                        urlParams += urlValues;
                    }

                    const newWindow = window.open(
                        `${window.location.origin}/case-print/${caseId}/${printId}${urlParams}`,
                        '_blank',
                    );

                    if (!newWindow) {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: t('alrBlockedPopups'),
                            show: true,
                        });
                    }
                } else {
                    const obj = {
                        iprocId: caseId,
                        itaskId: printchoise === -2 && taskId ? Number(taskId) : null,
                        id: printId, // printID -1, 241, 229...
                        docType: printtype, // "printer", "pdf", "html"
                        storeDMS: data.saveAsAttach,
                        values: printValuesObj,
                        archived: archived,
                    };

                    const alertId = guid();
                    AlertsActions.addAlert({
                        id: alertId,
                        type: 'info',
                        message: t('alrPreparingPrint'),
                    });

                    ApiRequest.post(`/processes/${caseId}/printToFile`, JSON.stringify(obj))
                        .then((answer: any) => {
                            closeModal();

                            if (printtype === 'printer') {
                                // only print into new window
                                AlertsActions.removeButtonAlert(alertId);
                                const newWindow = window.open('', '_blank');

                                if (newWindow) {
                                    newWindow.document.write(answer.result);
                                    newWindow.document.close();
                                } else {
                                    AlertsActions.changeAlert({
                                        id: alertId,
                                        type: 'warning',
                                        message: t('alrBlockedPopups'),
                                        show: true,
                                    });
                                }
                            } else {
                                // append html or pdf to process or open to a new window
                                AlertsActions.changeAlert({
                                    id: alertId,
                                    type: 'success',
                                    message: t('alrFileDownloaded'),
                                });

                                if (data.saveAsAttach) {
                                    if (answer.message === 'VICE_VIEW_ONLY_ERROR') {
                                        AlertsActions.addAlert({
                                            type: 'success',
                                            message: t('alrFileSaveLikeAttachViceError'),
                                            show: true,
                                        });
                                    } else if (answer.message === 'FILE_STORE_ERROR') {
                                        AlertsActions.addAlert({
                                            type: 'success',
                                            message: t('alrFileSaveLikeAttachStoreError'),
                                            show: true,
                                        });
                                    } else {
                                        // update attachments count
                                        if (typeof taskId !== 'undefined') {
                                            getDocumentCount();
                                        } else {
                                            getDocumentCount();
                                        }

                                        AlertsActions.addAlert({
                                            type: 'success',
                                            message: t('alrFileSavedLikeAttach'),
                                        });
                                    }
                                } else {
                                    let fileType: string;
                                    if (printtype === 'doc') {
                                        fileType = 'application/msword';
                                    } else if (printtype === 'pdf') {
                                        fileType = 'application/pdf';
                                    } else {
                                        fileType = 'text/html';
                                    }

                                    if (printtype === 'pdf') {
                                        // base64
                                        const byteCharacters = atob(answer.result);
                                        const byteNumbers = new Array(byteCharacters.length);

                                        for (let i = 0; i < byteCharacters.length; i += 1) {
                                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                                        }

                                        const byteArray = new Uint8Array(byteNumbers);
                                        let blob;

                                        if (fileType) {
                                            blob = new Blob([byteArray], {
                                                type: fileType,
                                            });
                                        } else {
                                            blob = new Blob([byteArray]);
                                        }

                                        openPrintInNewTab(blob, `${title}.pdf`);
                                    } else {
                                        const blob = new Blob([answer.result], {
                                            type: fileType,
                                        });
                                        openPrintInNewTab(blob, `${title}.html`);
                                    }
                                }
                            }
                        })
                        .catch((errorMessage: any) => {
                            AlertsActions.changeAlert({
                                id: alertId,
                                type: 'alert',
                                message: t('alrFailedCreatePrint'),
                                serverError: errorMessage,
                            });
                        });
                }
            } else {
                const obj = {
                    iprocId: caseId,
                    itaskId: printchoise === -2 && taskId ? Number(taskId) : null,
                    id: printId, // printID -1, 241, 229...
                    docType: printtype, // "printer", "pdf", "html"
                    storeDMS: data.saveAsAttach,
                    values: printValuesObj,
                    archived: archived,
                };

                const alertId = guid();
                AlertsActions.addAlert({
                    id: alertId,
                    type: 'info',
                    message: t('alrPreparingPrint'),
                });

                ApiRequest.post(`/processes/${caseId}/printToFile`, JSON.stringify(obj))
                    .then((answer: any) => {
                        closeModal();

                        if (printtype === 'printer') {
                            // only print into new window
                            AlertsActions.removeButtonAlert(alertId);
                            const newWindow = window.open('', '_blank');

                            if (newWindow) {
                                newWindow.document.write(answer.result);
                                newWindow.document.close();
                            } else {
                                AlertsActions.changeAlert({
                                    id: alertId,
                                    type: 'warning',
                                    message: t('alrBlockedPopups'),
                                    show: true,
                                });
                            }
                        } else {
                            // append html or pdf to process or open to a new window
                            AlertsActions.changeAlert({
                                id: alertId,
                                type: 'success',
                                message: t('alrFileDownloaded'),
                            });

                            if (data.saveAsAttach) {
                                if (answer.message === 'VICE_VIEW_ONLY_ERROR') {
                                    AlertsActions.addAlert({
                                        type: 'success',
                                        message: t('alrFileSaveLikeAttachViceError'),
                                        show: true,
                                    });
                                } else if (answer.message === 'FILE_STORE_ERROR') {
                                    AlertsActions.addAlert({
                                        type: 'success',
                                        message: t('alrFileSaveLikeAttachStoreError'),
                                        show: true,
                                    });
                                } else {
                                    // update attachments count
                                    if (typeof taskId !== 'undefined') {
                                        getDocumentCount();
                                        // tabActionAttach.getLabelCount({
                                        //     apiUrl: `/tasks/${taskId}/attachments`,
                                        // });
                                    } else {
                                        getDocumentCount();
                                        // tabActionAttach.getLabelCount({
                                        //     apiUrl: `/processes/${caseId}/attachments`,
                                        // });
                                    }

                                    AlertsActions.addAlert({
                                        type: 'success',
                                        message: t('alrFileSavedLikeAttach'),
                                    });
                                }
                            } else {
                                let fileType: string;
                                if (printtype === 'doc') {
                                    fileType = 'application/msword';
                                } else if (printtype === 'pdf') {
                                    fileType = 'application/pdf';
                                } else {
                                    fileType = 'text/html';
                                }

                                if (printtype === 'pdf') {
                                    // base64
                                    const byteCharacters = atob(answer.result);
                                    const byteNumbers = new Array(byteCharacters.length);

                                    for (let i = 0; i < byteCharacters.length; i += 1) {
                                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                                    }

                                    const byteArray = new Uint8Array(byteNumbers);
                                    let blob;

                                    if (fileType) {
                                        blob = new Blob([byteArray], {
                                            type: fileType,
                                        });
                                    } else {
                                        blob = new Blob([byteArray]);
                                    }

                                    openPrintInNewTab(blob, `${title}.pdf`);
                                } else {
                                    const blob = new Blob([answer.result], {
                                        type: fileType,
                                    });
                                    openPrintInNewTab(blob, `${title}.html`);
                                }
                            }
                        }
                    })
                    .catch((errorMessage: any) => {
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'alert',
                            message: t('alrFailedCreatePrint'),
                            serverError: errorMessage,
                        });
                    });
            }
        } else {
            const obj = {
                iprocId: caseId,
                itaskId: printchoise === -2 && taskId ? Number(taskId) : null,
                id: printId, // printID -1, 241, 229...
                docType: printtype, // "printer", "pdf", "html"
                storeDMS: data.saveAsAttach,
                values: printValuesObj,
                archived: archived,
            };

            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrPreparingPrint'),
            });

            ApiRequest.post(`/processes/${caseId}/printToFile`, JSON.stringify(obj))
                .then((answer: any) => {
                    closeModal();

                    if (printtype === 'printer') {
                        // only print into new window
                        AlertsActions.removeButtonAlert(alertId);
                        const newWindow = window.open('', '_blank');

                        if (newWindow) {
                            newWindow.document.write(answer.result);
                            newWindow.document.close();
                        } else {
                            AlertsActions.changeAlert({
                                id: alertId,
                                type: 'warning',
                                message: t('alrBlockedPopups'),
                                show: true,
                            });
                        }
                    } else {
                        // append html or pdf to process or open to a new window
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'success',
                            message: t('alrFileDownloaded'),
                        });

                        if (data.saveAsAttach) {
                            if (answer.message === 'VICE_VIEW_ONLY_ERROR') {
                                AlertsActions.addAlert({
                                    type: 'success',
                                    message: t('alrFileSaveLikeAttachViceError'),
                                    show: true,
                                });
                            } else if (answer.message === 'FILE_STORE_ERROR') {
                                AlertsActions.addAlert({
                                    type: 'success',
                                    message: t('alrFileSaveLikeAttachStoreError'),
                                    show: true,
                                });
                            } else {
                                // update attachments count
                                if (typeof taskId !== 'undefined') {
                                    getDocumentCount();
                                    // tabActionAttach.getLabelCount({
                                    //     apiUrl: `/tasks/${taskId}/attachments`,
                                    // });
                                } else {
                                    getDocumentCount();
                                    // tabActionAttach.getLabelCount({
                                    //     apiUrl: `/processes/${caseId}/attachments`,
                                    // });
                                }

                                AlertsActions.addAlert({
                                    type: 'success',
                                    message: t('alrFileSavedLikeAttach'),
                                });
                            }
                        } else {
                            let fileType: string;
                            if (printtype === 'doc') {
                                fileType = 'application/msword';
                            } else if (printtype === 'pdf') {
                                fileType = 'application/pdf';
                            } else {
                                fileType = 'text/html';
                            }

                            if (printtype === 'pdf') {
                                // base64
                                const byteCharacters = atob(answer.result);
                                const byteNumbers = new Array(byteCharacters.length);

                                for (let i = 0; i < byteCharacters.length; i += 1) {
                                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                                }

                                const byteArray = new Uint8Array(byteNumbers);
                                let blob;

                                if (fileType) {
                                    blob = new Blob([byteArray], {
                                        type: fileType,
                                    });
                                } else {
                                    blob = new Blob([byteArray]);
                                }

                                openPrintInNewTab(blob, `${title}.pdf`);
                            } else {
                                const blob = new Blob([answer.result], {
                                    type: fileType,
                                });
                                openPrintInNewTab(blob, `${title}.html`);
                            }
                        }
                    }
                })
                .catch((errorMessage: any) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrFailedCreatePrint'),
                        serverError: errorMessage,
                    });
                });
        }
    };

    const handleValidSubmit = (data: any) => {
        handlePrintAction(data);
    };

    const onSubmit = async (data: any) => {
        try {
            // Assuming you've split the main logic into separate functions
            await handleValidSubmit(data);
        } catch (error) {
            // Handle any errors that might occur during the print process
            AlertsActions.addAlert({
                type: 'alert',
                message: t('alrFailedCreatePrint'),
                serverError: error,
            });
        }
    };

    const printTypeOptions = [
        { value: 'printer', title: t('printer') },
        /* {value: 'doc', title: 'MSWord doc'}, */
        { value: 'pdf', title: 'PDF' },
        { value: 'html', title: 'HTML' },
    ];

    return (
        <Dialog maxWidth="xs" fullWidth open={isOpen} onClose={() => closeModal()}>
            <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
                <DialogTitle display="flex" justifyContent="flex-end">
                    <IconButton
                        color="secondary"
                        icon="close"
                        onClick={() => closeModal()}
                        aria-label={t('close')}
                    />
                </DialogTitle>
                <DialogContent>
                    <Stack spacing={1}>
                        <Select
                            name="printchoise"
                            label={t('choosePrint')}
                            options={dbPrints}
                            loading={loading}
                            disableClearable
                            blurOnSelect={false}
                            validations={{
                                required: t('isRequired'),
                            }}
                        />
                        <Select
                            name="printtype"
                            label={t('printType')}
                            options={printTypeOptions}
                            disableClearable
                            blurOnSelect={false}
                            onComponentChange={(name: string, value: any) => {
                                if (value?.value === 'printer') {
                                    setValue('saveAsAttach', false);
                                }
                            }}
                        />
                        <Checkbox
                            name="saveAsAttach"
                            label={t('saveAsAttachment')}
                            disabled={printType?.value === 'printer'}
                        />
                    </Stack>
                </DialogContent>
                <DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
                    <Button
                        type="submit"
                        startIcon={<MyIcon icon="print" />}
                        variant="contained"
                        color="primary"
                        label={t('print')}
                    />
                    <Button
                        label={t('close')}
                        onClick={() => closeModal()}
                        color="secondary"
                        variant="text"
                    />
                </DialogActions>
            </FormProvider>
        </Dialog>
    );
};

export default PrintCaseModal;
