import { useState } from 'react';
// hooks
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
// types
import { TModalEventOption } from '../types';
// utils
import { checkLangMutationTemplInst, guid, sortArrayBy } from '../../../common/utils';
import browserHistory from '../../../common/history';
import ApiRequest from '../../../api/apiRequest';
// flux
import AlertsActions from '../../../components/alerts/alerts.actions';
import LoggedUserActions from '../../../flux/loggedUser.actions';
import LoggedUserStore from '../../../flux/loggedUser.store';

const UseModalEvent = (caseId: number) => {
    const [eventOptions, setEventOptions] = useState<TModalEventOption[]>([]);
    const [eventVars, setEventVars] = useState<any[]>([]);
    const [maxWidth, setMaxWidth] = useState<'xs' | 'sm'>('xs');
    const [chosenOption, setChosenOption] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const location = useLocation();
    const { t } = useTranslation();

    const loadEvents = () => {
        if (caseId) {
            ApiRequest.get(`/process-events/${caseId}?limit=${(window as any).config.restLimit}`)
                .then((payload: any) => {
                    const array: TModalEventOption[] = [];
                    payload.items.forEach((item: any) => {
                        const obj: TModalEventOption = {
                            value: item.itask_id.toString(),
                            title: checkLangMutationTemplInst(item, 'task_name'),
                        };
                        array.push(obj);
                    });
                    setEventOptions(sortArrayBy(array, 'title', 'title'));
                    setIsLoading(false);
                })
                .catch((err: any) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: t('alrFailedEvents'),
                        serverError: err,
                    });
                });
        }
    };

    // load event data after preferred option is chosen
    const getEventVars = (eventId: number) => {
        if (caseId) {
            ApiRequest.get(`/task-event/${eventId}`)
                .then((payload: any) => {
                    setEventVars(payload.variables);

                    let width: 'xs' | 'sm' = 'xs';
                    payload.variables.forEach((v: any) => {
                        if (v.ivar_attribute !== null && v.ivar_attribute === 'M') {
                            width = 'sm';
                        }
                    });
                    setMaxWidth(width);

                    setIsLoading(false);
                })
                .catch((err: any) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: t('alrFailedEventVariables'),
                        serverError: err,
                    });
                });
        }
    };

    const launchEvent = (data: any) => {
        // TODO: prevent submit when?
        // let canSubmit = true;

        // this._selectBoxes.forEach(selectbox => {
        //     if (selectbox && selectbox.state.open) {
        //         canSubmit = false;
        //     }
        // });
        //
        // this._calendars.forEach(calendar => {
        //     if (calendar && calendar.state.calendarIsOpen) {
        //         canSubmit = false;
        //     }
        // });

        // // prevent to submit if textArea is focused
        // if (document.activeElement.tagName === 'TEXTAREA') {
        //     canSubmit = false;
        // }
        //
        // if (canSubmit) {
        //     this.refs.formStatus.submit();
        // }

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: t('alrRunEvent'),
        });

        const arr: { TVAR_ID: number; VALUE: any }[] = [];
        Object.keys(data).forEach((key: string) => {
            // scan objects
            if (key !== 'eventId') {
                const obj = {
                    TVAR_ID: Number(key),
                    VALUE: data[key]?.value ?? data[key],
                };

                arr.push(obj);
            }
        });

        const params = {
            IPROC_ID: caseId,
            ID: Number(chosenOption),
            params: arr,
        };

        ApiRequest.post(
            `/process-events/${params.IPROC_ID}/handInvoke/${params.ID}`,
            JSON.stringify(arr),
        )
            .then((payload: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrEventTriggered'),
                });

                const taskQueue = LoggedUserStore.getTaskQueue();

                // if new task exists
                if (payload.NEXT_ITASK_ID) {
                    browserHistory.push({
                        pathname: `/tasks/task/${payload.NEXT_ITASK_ID}`,
                        state: {
                            breadPrevPath: '/tasks',
                            closePrevPath: location.pathname,
                        },
                    });

                    // add remaining tasks to queue
                    if (payload.tasksToQueue.length > 0) {
                        const { tasksToQueue } = payload;
                        tasksToQueue.forEach((task: any) => {
                            taskQueue.push(task);
                        });
                        LoggedUserActions.setTaskQueue(taskQueue);
                    }
                } else if (location.pathname.split('/')[2] === 'case') {
                    // refresh case overview
                    browserHistory.push({
                        pathname: location.pathname,
                        state: { refreshPage: true },
                    });
                }
            })
            .catch((err: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: t('alrFailedEventStart'),
                    serverError: err,
                });
            });
    };

    const chooseOption = (val: any) => {
        setIsLoading(true);
        setChosenOption(val);
        setEventVars([]);
        getEventVars(val);
    };

    return {
        eventOptions,
        eventVars,
        isLoading,
        loadEvents,
        launchEvent,
        chooseOption,
        chosenOption,
        maxWidth,
    };
};

export default UseModalEvent;
