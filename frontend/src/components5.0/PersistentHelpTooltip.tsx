import { ReactElement } from 'react';
// mui
import {
    Box,
    BoxProps,
    Stack,
    Typography,
    useTheme,
} from '@mui/material';
// utils
import { pxToRem } from '../theme/typography';
// components
import Label from './label';
import { BottomLeftLongArrowIcon, TopLeftLongArrowIcon } from './illustrations/Illustrations';

const PersistentHelpTooltip = ({
    open = true,
    title,
    placement = 'top',
    xOffset = '100%',
    yOffset = '50%',
    children,
    sx,
}: {
    open?: boolean;
    title?: string;
    placement?: 'top' | 'bottom';
    /** Offset from the left of a box containing the children. Can be any CSS unit. */
    xOffset?: string;
    /** Offset from the top (for `placement === 'bottom'`) / bottom (for `placement === 'top'`) of a box containing the children. Can be any CSS unit. */
    yOffset?: string;
    children: ReactElement<unknown, any>;
    sx?: BoxProps['sx'];
}) => {
    const theme = useTheme();

    return (
        <Box
            sx={{
                position: 'relative',
                ...sx,
            }}
        >
            <Box>
                {children}
            </Box>
            {open && (
                <Label
                    color="primary"
                    sx={{
                        position: 'absolute',
                        ...(placement === 'top' ? { bottom: yOffset } : { top: yOffset }),
                        left: xOffset,
                        whiteSpace: 'nowrap',
                        paddingY: pxToRem(4),
                        paddingX: pxToRem(8),
                    }}
                >
                    <Stack
                        direction="row"
                        sx={{
                            columnGap: pxToRem(12),
                            flexWrap: 'nowrap',
                        }}
                    >
                        {placement === 'top' ? (
                            <BottomLeftLongArrowIcon
                                sx={{
                                    height: pxToRem(14),
                                    '& svg': {
                                        maxWidth: 'none',
                                        height: '100%',
                                        width: 'auto',
                                    },
                                }}
                            />
                        ) : (
                            <TopLeftLongArrowIcon
                                sx={{
                                    height: pxToRem(14),
                                    '& svg': {
                                        maxWidth: 'none',
                                        height: '100%',
                                        width: 'auto',
                                    },
                                }}
                            />
                        )}
                        <Typography
                            variant="caption"
                            color={theme.palette.success.darker}
                            sx={{
                                textTransform: 'uppercase',
                            }}
                        >
                            {title || ''}
                        </Typography>
                    </Stack>
                </Label>
            )}
        </Box>
    );
};

export default PersistentHelpTooltip;
