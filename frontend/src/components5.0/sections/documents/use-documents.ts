import { useShallow } from 'zustand/shallow';
import { useState, useEffect } from 'react';
import { get } from 'lodash';
// hooks
import { useTranslation } from 'react-i18next';
import { useStore } from '../../zustand/boundStore';
import { useParams } from '../../routes/hooks';
// utils
import {
    guid,
    recursivelyLoadFolders,
    isImage,
    isPdf,
    isText,
    isWord,
    isVideo,
    popupPdf,
    popupImage,
    popupVideo,
    popupText,
    popupDocx,
} from '../../../common/utils';
import { TModalDocumentDetailFile } from '../../Modals/types';
import { checkRevision } from '../../../pages/uploadDocumentUtil';
import ApiRequest from '../../../api/apiRequest';
import { saveAs } from '../../../../assets/libs/filesaver';
import CrossPlatform from '../../../common/CrossPlatform';
// api
import TaskApi from '../../../api/TaskApi';
// flux
import AlertsActions from '../../../components/alerts/alerts.actions';
import LoggedUserStore from '../../../flux/loggedUser.store';

const useDocuments = (
    entityName?: 'task' | 'case' | 'all',
    resetStoreOnUnmount?: boolean,
    tskId?: number | string,
    afterPostFile?: (file: Record<string, any>, response: Record<string, any>) => void,
    archived?: boolean,
) => {
    const { caseId, taskId }: { caseId?: string; taskId?: string } = useParams();

    let apiUrl: string;

    switch (entityName) {
        case 'task':
            apiUrl = `/tasks/${archived ? 'archived/' : ''}${taskId || tskId}/attachments`;
            break;
        case 'case':
            apiUrl = `/processes/${archived ? 'archived/' : ''}${caseId}/attachments`;
            break;
        default:
            apiUrl = '/dms/current';
    }

    const {
        documents,
        documentsLoading,
        documentCountLoading,
        foldersTree,
        foldersOptions,
        foldersLoading,
        documentsCount,
        setDocuments,
        setDocumentsLoading,
        setDocumentCountLoading,
        setFoldersTree,
        setFoldersOptions,
        setFoldersLoading,
        setDocumentsCount,
        updateRows,
        resetStoreDocuments,
    } = useStore(
        useShallow((state) => ({
            documents: state.documents,
            documentsLoading: state.documentsLoading,
            documentCountLoading: state.documentCountLoading,
            foldersTree: state.foldersTree,
            foldersOptions: state.foldersOptions,
            foldersLoading: state.foldersLoading,
            documentsCount: state.documentsCount,
            setDocuments: state.setDocuments,
            setDocumentsLoading: state.setDocumentsLoading,
            setDocumentCountLoading: state.setDocumentCountLoading,
            setFoldersTree: state.setFoldersTree,
            setFoldersOptions: state.setFoldersOptions,
            setFoldersLoading: state.setFoldersLoading,
            setDocumentsCount: state.setDocumentsCount,
            updateRows: state.updateRows,
            resetStoreDocuments: state.resetStoreDocuments,
        })),
    );

    useEffect(() => {
        return () => {
            if (resetStoreOnUnmount) {
                resetStoreDocuments();
            }
        };
    }, []);

    const [document, setDocument] = useState<TModalDocumentDetailFile>({});

    const { t } = useTranslation();

    // ------------------------------ document preview ---------------------------------
    const canOpenPreview = (name: string) =>
        isImage(name) || isPdf(name) || isText(name) || isWord(name) || isVideo(name);

    const getFile = (fileId: string) => {
        return ApiRequest.getFile(`/dms/${archived ? 'archived/' : ''}download/${fileId}`);
    };

    const openPreviewInNewWindow = async (file: any) => {
        if (!canOpenPreview(file.name)) {
            return;
        }

        try {
            const attach = await getFile(file.id);

            if (isPdf(file.name)) {
                popupPdf(attach);
            } else if (isImage(file.name)) {
                popupImage(attach);
            } else if (isText(file.name)) {
                popupText(attach, file.name);
            } else if (isWord(file.name)) {
                popupDocx(attach, file.name);
            } else if (isVideo(file.name)) {
                popupVideo(attach, file.name);
            }
        } catch (errorMessage: any) {
            AlertsActions.addAlert({
                type: 'alert',
                message: t('alrAttachDownloadFailed'),
                serverError: errorMessage,
            });
        }
    };

    // ------------------------------ getDocumentCount ---------------------------------

    const getDocumentCount = () => {
        setDocumentCountLoading(true);
        ApiRequest.get(`${apiUrl}?only_count=true`)
            .then((payload: { total_count: number }) => {
                if (payload) {
                    setDocumentsCount(payload.total_count);
                }

                return payload.total_count;
            })
            .catch(({ message: errorMessage }: any) => {
                setDocumentsCount(0);

                AlertsActions.addAlert({
                    type: 'alert',
                    message: errorMessage,
                    serverError: errorMessage,
                });
            })
            .finally(() => {
                setDocumentCountLoading(false);
            });
    };

    // ------------------------------ fetch documents ---------------------------------

    const fetchDocuments = async () => {
        setDocumentsLoading(true);

        const url = `${apiUrl}?&order=datetime_insert&sort=desc&limit=${(window as any).config.restLimit}&total_count=false`;

        try {
            const documnents = await ApiRequest.get(url);

            setDocuments(documnents.items);
        } catch (errorMessage) {
            AlertsActions.addAlert({
                type: 'alert',
                message: t('alrLoadAttachmentsFailed'),
                serverError: errorMessage,
            });
        } finally {
            setDocumentsLoading(false);
        }
    };

    const fetchDocumentsAndCount = () => {
        getDocumentCount();
        fetchDocuments();
    };

    // ------------------------------ folders ---------------------------------

    const fetchTreeFolders = async () => {
        setFoldersLoading(true);
        try {
            const url = `/dms/folders-tree?order=folder_name&sort=asc&limit=${(window as any).config.restLimit}`;
            const payload = await ApiRequest.get(url);

            const root = [
                {
                    id: 'root',
                    label: t('allFiles'),
                    children: payload.items || [],
                },
            ];

            setFoldersTree(root);

            const options: any = [];

            payload.items?.forEach((item: any) => {
                const option = {
                    value: item.id,
                    title: item.label,
                    parentId: item.parent_id,
                };
                options.push(option);
                recursivelyLoadFolders(options, item);
            });

            setFoldersOptions(options);
        } catch (errorMessage) {
            AlertsActions.addAlert({
                type: 'alert',
                message: t('alrTreeDataFailed'),
                serverError: errorMessage,
            });
        } finally {
            setFoldersLoading(false);
        }
    };

    const fetchFoldersOptions = async () => {
        try {
            const url = `/dms/folders-tree?order=folder_name&sort=asc&limit=${(window as any).config.restLimit}`;
            const payload = await ApiRequest.get(url);

            if (payload.items) {
                const options: any = [];

                payload.items.forEach((item: any) => {
                    const option = {
                        value: item.id,
                        title: item.label,
                        parentId: item.parent_id,
                    };
                    options.push(option);

                    recursivelyLoadFolders(options, item);
                });

                setFoldersOptions(options);
            }
        } catch (errorMessage) {
            AlertsActions.addAlert({
                type: 'alert',
                message: t('alrFailedFoldersData'),
                serverError: errorMessage,
            });
        } finally {
            setFoldersLoading(false);
        }
    };

    const deleteFolder = async (id: string | undefined | null) => {
        const alertId = guid();
        if (id === null) {
            return;
        }

        try {
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrDeleting'),
            });

            await ApiRequest.delete('/dms/folder', { FOLDER_ID: id });

            AlertsActions.changeAlert({
                id: alertId,
                type: 'success',
                message: t('alrDeleted'),
            });

            fetchTreeFolders();
        } catch (errorMessage) {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'alert',
                message: t('alrDeleteFailed'),
                serverError: errorMessage,
            });
        }
    };

    const postEditFolder = async (data: any) => {
        const alertId = guid();

        try {
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrSaving'),
            });

            await ApiRequest.post('/dms/folder', JSON.stringify(data));

            AlertsActions.changeAlert({ id: alertId, type: 'success' });

            fetchTreeFolders();
        } catch (errorMessage: any) {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'alert',
                message: t('alrFailed'),
                serverError: errorMessage.message || errorMessage,
            });
        }
    };

    // ------------------------------downloadFile---------------------------------
    const downloadFile = (id: any, attachName?: string, e?: any) => {
        const isMultiple = Array.isArray(id);
        if (e) e.preventDefault();

        const fileName = isMultiple ? 'documents.zip' : attachName;

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: t('alrAttachDownloading'),
        });

        // download file for flutter app
        if (CrossPlatform.isNativeMobileApp()) {
            let fileUrl;
            if (Array.isArray(id)) {
                fileUrl = `api/dms/${archived ? 'archived/' : ''}download-all`;
            }
            fileUrl = `api/dms/${archived ? 'archived/' : ''}download/${id}`;
            CrossPlatform.downloadFile(
                fileUrl,
                fileName,
                alertId,
                t('alrAttachDownloaded'),
                t('alrAttachDownloadFailed'),
            );
            return;
        }

        const progress = (percent: any) => {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'info',
                message: t('alrAttachDownloading'),
                progress: percent,
            });
        };

        TaskApi.getDownloadedFile(id, progress, archived)
            .then((payload: any) => {
                saveAs?.(payload, fileName); // payload is Blob

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrAttachDownloaded'),
                });
            })
            .catch((errorMessage: any) => {
                if (get(errorMessage, 'error.codeName') === 'INACCESSIBLE_FILE') {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                } else if (get(errorMessage, 'error.codeName') === 'FILE_LACK_OF_PERMISSIONS') {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrAttachDownloadLackOfPerms'),
                        serverError: errorMessage,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    };

    // ------------------------------documentDetail & documentDelete modals---------------------------------

    const openDocumentDetail = (openDocument: any, id: number, name: string) => {
        setDocument({
            title: name,
            value: name,
            dmsf_id: id,
        });
        openDocument();
    };

    const postMetadata = async (metadataArr: any[], alertId: any, fileId: any) => {
        try {
            await ApiRequest.post(`/dms/file/${fileId}/metadata`, JSON.stringify(metadataArr));
            if (entityName === 'task' || entityName === 'case') {
                fetchDocumentsAndCount();
            }
            updateRows();

            AlertsActions.changeAlert({
                id: alertId,
                type: 'success',
                message: t('alrAttachSaved'),
            });
        } catch (errorMessage) {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'alert',
                message: t('alrAttachMetaFailed'),
                serverError: errorMessage,
            });
        }
    };

    const deleteDocument = async () => {
        const alertId = guid();
        const id = document.dmsf_id;

        try {
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrAttachDeleting'),
            });

            await ApiRequest.delete(`/dms/file/${id}`);

            AlertsActions.changeAlert({
                id: alertId,
                message: t('alrAttachDeleted'),
                type: 'success',
            });

            if (entityName === 'task' || entityName === 'case') {
                fetchDocumentsAndCount();
            }
            updateRows();
        } catch (errorMessage) {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'alert',
                message: t('alrAttachDeleteFailed'),
                serverError: errorMessage,
            });
        }
    };

    const openDocumentDelete = (openModal: any, id: number) => {
        setDocument({
            dmsf_id: id,
        });
        openModal();
    };

    const documentOnLoad = (
        metaFormValues: any,
        files: any,
        iprocId?: string,
        itaskId?: string,
        revisionId?: string,
        keepActualRevFolder?: boolean,
    ) => {
        const logicalType = metaFormValues['-1']?.value || null;
        let folder = metaFormValues['-4']?.value || null;

        files.forEach((newFile: any) => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                hasSpinner: true,
                type: 'info',
                message: t('alrDocumentAdding'),
            });

            const formData = new FormData();

            // text parameters must be first! http://sailsjs.com/documentation/reference/request-req/req-file
            formData.append('filename', newFile.name);
            if (iprocId) {
                formData.append('iprocId', iprocId);
            }
            if (itaskId) {
                formData.append('itaskId', itaskId);
            }

            if (revisionId) {
                formData.append('lastRevisionId', revisionId);
            }

            // the file itself must be the last appended param
            formData.append('file', newFile);

            const url = iprocId || itaskId ? '/dms/upload' : '/dms/upload/private';

            ApiRequest.postFile(url, formData, newFile, iprocId)
                .then((payload: any) => {
                    const fileId = payload.id;
                    // eslint-disable-next-line no-param-reassign
                    newFile.dmsf_id = fileId;

                    // ModalAddDocument for DynamicRows
                    if (typeof afterPostFile === 'function') {
                        afterPostFile(newFile, payload);
                    }

                    if (keepActualRevFolder) {
                        folder = 'current';
                    }

                    checkRevision(fileId, alertId, logicalType, folder).then((metadataArr: any) => {
                        postMetadata(metadataArr, alertId, fileId);
                    });
                })
                .catch((err: any) => {
                    if (get(err, 'error.codeName') === 'VICE_VIEW_ONLY_ERROR') {
                        AlertsActions.removeButtonAlert(alertId);
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: t('alrNoPermsToAddDocInVice'),
                            show: true,
                            allowCountdown: true,
                        });
                    } else {
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'alert',
                            message: t('alrAttachSaveFailed'),
                            serverError: err,
                        });
                    }
                });
        });
    };

    const indexDocument = async (documentId?: number | string) => {
        if (!documentId) {
            return;
        }

        const fulltext = LoggedUserStore.getConfig('dms.fulltext'); // if fulltext set ElasticSearch is running
        const indexingIsEnabled = fulltext !== null;

        if (indexingIsEnabled) {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrDocumentIndexing'),
            });

            try {
                const result = await ApiRequest.post(
                    `/dms/index-file/${documentId}`,
                    JSON.stringify({}),
                );

                const tikaError = result.message[0];

                if (tikaError) {
                    // TIKA_PARSING_FAILED or TIKA_NOT_RUNNING
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: t('alrDocumentIndexedWithMinMetadata'),
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: t('alrDocumentIndexed'),
                    });
                }

                updateRows();
            } catch (errorMessage) {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: t('alrDocumentIndexingFailed'),
                    serverError: errorMessage,
                });
            }
        } else {
            AlertsActions.addAlert({
                type: 'info',
                message: t('alrFulltextNotSet'),
            });
        }
    };

    // ------------------------------ Bulk Delete Documents ------------------------------

    const dataGridWholeUrls = useStore((state) => state.dataGridWholeUrls);
    const dataGridSelectedRows = useStore((state) => state.dataGridSelectedRows);
    const setDataGridSelectedRows = useStore((state) => state.setDataGridSelectedRows);

    const bulkDeleteDocuments = (selected: string, dataGridId: string, cb?: () => void) => {
        let deleteUrl = '/dms/bulk-delete?limit=1000';

        if (selected === 'A') {
            // all documents
            const dataGridUrl = dataGridWholeUrls[dataGridId];
            const urlQuery = dataGridUrl.split('?')[1];
            const urlQueryArr = urlQuery.split('&');

            const filterIndex = urlQueryArr.findIndex((el) => el.includes('filter='));
            if (filterIndex !== -1) {
                const filter = urlQueryArr[filterIndex];
                deleteUrl += `&${filter}`;
            }

            const orderIndex = urlQueryArr.findIndex((el) => el.includes('order='));
            if (orderIndex !== -1) {
                const order = urlQueryArr[orderIndex];
                deleteUrl += `&${order}`;
            }

            const sortIndex = urlQueryArr.findIndex((el) => el.includes('sort='));
            if (sortIndex !== -1) {
                const sort = urlQueryArr[sortIndex];
                deleteUrl += `&${sort}`;
            }
        } else if (selected === 'S') {
            // selected only
            const filter = `id<in>"${(dataGridSelectedRows.get('documentsAll') ?? []).join('","')}"`;
            deleteUrl += `&filter=${encodeURIComponent(filter)}`;
        }

        const alertId = guid();
        AlertsActions.addAlert({ id: alertId, type: 'info', message: t('alrDeleting') });

        ApiRequest.delete(deleteUrl, JSON.stringify({}))
            .then((payload: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrDeleted'),
                });
                cb?.();
                updateRows();
            })
            .catch((err: any) => {
                if (
                    err.error &&
                    err.error.codeName &&
                    err.error.codeName === 'MASS_MULTIPLE_TASKS'
                ) {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'warning',
                        message: t('alrBulkChangeMultiTypeErr'),
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrDeleteFailed'),
                        serverError: err,
                    });
                }
            });
    };

    const bulkDownloadDocuments = (selected: string, dataGridId: string) => {
        let fetchUrl = '/dms/bulk-download?limit=1000';
        const fileName = 'documents.zip';

        if (selected === 'A') {
            // all documents
            const dataGridUrl = dataGridWholeUrls[dataGridId];

            const urlQuery = dataGridUrl.split('?')[1];
            const urlQueryArr = urlQuery.split('&');

            const filterIndex = urlQueryArr.findIndex((el) => el.includes('filter='));
            if (filterIndex !== -1) {
                const filter = urlQueryArr[filterIndex];
                fetchUrl += `&${filter}`;
            }

            const orderIndex = urlQueryArr.findIndex((el) => el.includes('order='));
            if (orderIndex !== -1) {
                const order = urlQueryArr[orderIndex];
                fetchUrl += `&${order}`;
            }

            const sortIndex = urlQueryArr.findIndex((el) => el.includes('sort='));
            if (sortIndex !== -1) {
                const sort = urlQueryArr[sortIndex];
                fetchUrl += `&${sort}`;
            }
        } else if (selected === 'S') {
            // selected only
            const selectedRows = dataGridSelectedRows.get(dataGridId);
            const filter = selectedRows ? `id<in>"${selectedRows.join('","')}"` : '';
            fetchUrl += `&filter=${encodeURIComponent(filter)}`;
        }

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: t('alrAttachDownloading'),
        });
        // download file for flutter app
        if (CrossPlatform.isNativeMobileApp()) {
            CrossPlatform.downloadFile(
                `api/${fetchUrl}`,
                fileName,
                alertId,
                t('alrAttachDownloaded'),
                t('alrAttachDownloadFailed'),
                'GET',
            );
            return;
        }

        const progress = (percent: any) => {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'info',
                message: t('alrAttachDownloading'),
                progress: percent,
            });
        };

        ApiRequest.getFile(fetchUrl, null, progress, null, true)
            .then((payload: any) => {
                saveAs?.(payload, fileName); // payload is Blob

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrAttachDownloaded'),
                });
                setDataGridSelectedRows([], dataGridId);
            })
            .catch((errorMessage: any) => {
                if (get(errorMessage, 'error.codeName') === 'INACCESSIBLE_FILE') {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                } else if (get(errorMessage, 'error.codeName') === 'FILE_LACK_OF_PERMISSIONS') {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrAttachDownloadLackOfPerms'),
                        serverError: errorMessage,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    };

    return {
        openPreviewInNewWindow,
        canOpenPreview,
        documentsCount,
        fetchDocuments,
        fetchDocumentsAndCount,
        documents,
        documentsLoading,

        loadFolderTree: fetchTreeFolders,
        folderTree: foldersTree,
        folderTreeLoading: foldersLoading,
        fetchFoldersOptions,
        foldersOptions,
        deleteFolder,
        postEditFolder,

        downloadFile,
        openDocumentDetail,
        document,
        postMetadata,
        deleteDocument,
        openDocumentDelete,
        documentOnLoad,
        indexDocument,

        bulkDeleteDocuments,
        bulkDownloadDocuments,

        getDocumentCount,
        documentCountLoading,
    };
};

export default useDocuments;
