import { useTranslation } from 'react-i18next';
import React, { useRef, useState, useEffect } from 'react';
// @mui
import { Dialog, DialogTitle, Stack, Typography, DialogContent } from '@mui/material';
import { GridValidRowModel } from '@mui/x-data-grid-pro';
// components
import DataGrid from '../../../../data-grid/DataGrid';
import MyIcon from '../../../../MyIcon';
import Button from '../../../../Button';
import IconButton from '../../../../IconButton';
import ModalTaskDetail from '../../../../Modals/modal-task-detail/ModalTaskDetail';
import ModalConfirm from '../../../../Modals/ModalConfirm';
// hooks
import { useBoolean } from '../../../../hooks';
import { useResponsive } from '../../../../hooks/use-responsive';
import useCases from '../../use-cases';
import { useParams } from '../../../../routes/hooks/use-params';
import useTasks from '../../../tasks/use-tasks';
// utils
import { checkLangMutationTemplInst, momentFormatDate } from '../../../../../common/utils';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';

type ModalCasesActualTasksProps = {
    isOpen: boolean;
    onClose: VoidFunction;
    openContactModal5: (row: any) => void;
    openHandoverModal: (row: any) => void;
    archived?: boolean;
};

type TToolbarProps = {
    onClose: VoidFunction;
    archived?: boolean;
    dataGridRef: React.RefObject<any>;
};

const Toolbar = ({ onClose, archived, dataGridRef }: TToolbarProps) => {
    const smUp = useResponsive('up', 'sm');
    const { t } = useTranslation();

    const { handleActualTaskSolve, canSolveActualTask } = useCases();
    const [actualRow, setActualRow] = useState<any>(null);

    useEffect(() => {
        const updateActualRow = () => {
            const selectedRow =
                dataGridRef.current?.table?.getSelectedRowModel()?.rows?.[0]?.original;
            setActualRow(selectedRow || null);
        };
        updateActualRow();
    }, [dataGridRef.current?.table?.getSelectedRowModel()?.rows]);

    return (
        <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Stack direction="row" spacing={1} height={1} alignItems="center">
                <MyIcon icon="tasks" isActive />
                <Typography variant="h1" height={1}>
                    {t('actualTsks')}
                </Typography>
            </Stack>
            <Stack direction="row" spacing={1} alignItems="center">
                {/* <Label color="secondary" variant="soft" size="small">
              Případ čeká na událost: "TAS-PO_closeToBilling"
            </Label> */}
                {smUp && !archived && (
                    <Button
                        startIcon={<MyIcon icon="pen" />}
                        color="primary"
                        label={t('solve')}
                        size="small"
                        variant="contained"
                        aria-label={t('solve')}
                        disabled={!actualRow || !canSolveActualTask(actualRow)}
                        onClick={() => {
                            if (actualRow) {
                                handleActualTaskSolve(actualRow.id);
                            }
                        }}
                    />
                )}
                {!smUp && !archived && (
                    <IconButton
                        icon="pen"
                        size="large"
                        color="primary"
                        disabled={!actualRow || !canSolveActualTask(actualRow)}
                        onClick={() => {
                            if (actualRow) {
                                handleActualTaskSolve(actualRow.id);
                            }
                        }}
                    />
                )}
                {smUp ? (
                    <Button
                        startIcon={<MyIcon icon="close" />}
                        color="secondary"
                        label={t('close')}
                        size="small"
                        variant="text"
                        aria-label={t('close')}
                        onClick={() => onClose()}
                    />
                ) : (
                    <IconButton
                        icon="close"
                        size="large"
                        color="secondary"
                        onClick={() => onClose()}
                    />
                )}
            </Stack>
        </Stack>
    );
};

const ModalCasesActualTasks = ({
    isOpen,
    onClose,
    openContactModal5,
    openHandoverModal,
    archived,
}: ModalCasesActualTasksProps) => {
    const [taskId, setTaskId] = useState<number | undefined>(undefined);
    const [urlTablePart, setUrlTablePart] = useState<'pull-to-me' | 'assign-to-me'>('pull-to-me');
    const { t } = useTranslation();
    const modalTaskDetail = useBoolean();
    const modalTakeTask = useBoolean();
    const smUp = useResponsive('up', 'sm');

    const dataGridRef = useRef<any>(null);
    // Get the case ID from the URL
    const { caseId }: { caseId?: string } = useParams();

    const { handleActualTaskSolve, canSolveActualTask, toNestedCase, openTimingTaskTab } =
        useCases();
    const { handleTakeActualTask, toTask } = useTasks();

    const openModalTaskDetail = (item: GridValidRowModel) => {
        setTaskId(item.id);
        modalTaskDetail.onTrue();
    };

    const closeModalTaskDetail = () => {
        modalTaskDetail.onFalse();
        setTaskId(undefined);
    };

    const openModalTakeTask = (row: GridValidRowModel) => {
        setTaskId(row.id);
        setUrlTablePart(
            row.itask_status === 'T' && row.c_take === 1 ? 'pull-to-me' : 'assign-to-me',
        );
        modalTakeTask.onTrue();
    };

    const handleDoubleClick = (row: GridValidRowModel) => {
        if (canSolveActualTask(row)) {
            handleActualTaskSolve(row.id);
        } else if (row.itask_status === 'T' && row.c_take === 1) {
            openModalTakeTask(row);
        } else if (row.itask_type === 'P') {
            // subprocess
            toNestedCase(row.itask_subprocess_iproc_id);
        } else {
            openModalTaskDetail(row);
        }
    };

    // --------------------------------DataGridColumns-----------------------------

    const COLUMNS: any[] = [
        {
            field: 'itask_name_translated',
            headerName: t('tskName'),
            type: 'string',
            accessorFn: (originalRow: any) => checkLangMutationTemplInst(originalRow, 'task_name'),
        },
        ...(LoggedUserStore.isAdmin()
            ? [
                  {
                      field: 'itask_name',
                      headerName: t('defaultTaskName'),
                      type: 'string',
                  },
              ]
            : []),
        {
            field: 'solver_user_display_name',
            headerName: t('solver'),
            type: 'string',
        },
        {
            field: 'itask_actual_date_start',
            headerName: t('startDate'),
            type: 'date',
            accessorFn: (originalRow: any) =>
                originalRow.itask_actual_date_start &&
                momentFormatDate(originalRow.itask_actual_date_start, false, true),
        },
        {
            field: 'itask_status',
            headerName: t('status'),
            type: 'string',
            minSize: 150,
            accessorFn: (orignalRow: any) => {
                const value = orignalRow.itask_status;
                const userName: string =
                    orignalRow.itask_assesment_user_id !== null ||
                    orignalRow.itask_assesment_user !== ' '
                        ? orignalRow.itask_assesment_user
                        : orignalRow.iproc_owner_display_name;
                let title = t(`taskStatus${value}`);

                if (value === 'W') {
                    // To assign
                    title = `${t('taskStatusW')}: ${userName}`;
                }

                if (
                    orignalRow.itask_due_offset === 'po' ||
                    (orignalRow.itask_duration === 'po' &&
                        orignalRow.itask_due_date_finish === null)
                ) {
                    // To scheduling
                    title = `${t('taskStatusWT')}: ${userName}`;
                }

                if (value === 'N' && orignalRow.itask_auto_start === 'Y') {
                    // Scheduled
                    let date = '';
                    if (orignalRow.itask_due_date_start !== null) {
                        date = momentFormatDate(orignalRow.itask_due_date_start, false, true);
                    }

                    title = `${t('taskStatusP')}: ${date}`;
                }

                if (orignalRow.iproc_status === 'S') {
                    // Suspended
                    title = t('taskStatusS');
                }

                if (value === 'A' && orignalRow.itask_type === 'P') {
                    // Active sub process
                    title = t('taskStatusAP');
                }

                return title;
            },
        },
        {
            field: 'actions',
            type: 'actions',
            size: 30,
            minSize: 30,
            getActions: (params: any) => [
                {
                    icon: 'view',
                    label: t('detail'),
                    showInMenu: true,
                    onClick: () => openModalTaskDetail(params.row.original),
                },
                {
                    icon: 'pen',
                    label: t('solve'),
                    showInMenu: true,
                    disabled: !canSolveActualTask(params.row.original) || archived,
                    onClick: () => handleActualTaskSolve(params.row.original.id),
                },
                {
                    icon: 'up',
                    label: t('nested'),
                    showInMenu: true,
                    disabled: !(params.row.original.itask_subprocess_iproc_id > 0),
                    onClick: () => toNestedCase(params.row.original.itask_subprocess_iproc_id),
                },
                {
                    icon: 'handover',
                    label: t('handover'),
                    showInMenu: true,
                    disabled: params.row.original.c_handover === 0 || archived,
                    onClick: () => openHandoverModal(params.row.original),
                },
                {
                    icon: 'assume',
                    label: t('takeover'),
                    showInMenu: true,
                    disabled: params.row.original.c_take === 0 || archived,
                    onClick: () => openModalTakeTask(params.row.original),
                },
                {
                    icon: 'account',
                    label: t('contact'),
                    showInMenu: true,
                    disabled: params.row.original.itask_user_id === null,
                    onClick: () => openContactModal5(params.row.original),
                },
                {
                    icon: 'time',
                    label: t('scheduling'),
                    showInMenu: true,
                    disabled: params.row.original.c_due === 0 || archived,
                    onClick: () => openTimingTaskTab(params.row.original.id),
                },
            ],
        },
    ];
    return (
        <>
            <Dialog
                fullWidth
                fullScreen={!smUp}
                maxWidth="lg"
                onClose={onClose}
                aria-labelledby="customized-dialog-title"
                open={isOpen}
            >
                <DialogTitle>
                    <Toolbar onClose={onClose} archived={archived} dataGridRef={dataGridRef} />
                </DialogTitle>
                <DialogContent dividers sx={{ height: '80vh', overflowX: 'hidden' }}>
                    <DataGrid
                        id={`modal-cases-actual-tasks-${caseId}`}
                        apiUrl={`/processes/${archived ? 'archived/' : ''}${caseId}/tasks`}
                        columns={COLUMNS}
                        handleRowDoubleClick={archived ? undefined : handleDoubleClick}
                        disableRowSelectionOnClick={false}
                        defaultSortModel={[
                            {
                                field: 'itask_actual_date_start',
                                sort: 'asc',
                            },
                        ]}
                        alwaysSort={{ field: 'id', sort: 'asc' }}
                        dataGridRef={dataGridRef}
                    />
                </DialogContent>
                {/* </Box> */}
            </Dialog>
            {modalTaskDetail.value && taskId && (
                <ModalTaskDetail
                    isOpen={modalTaskDetail.value}
                    onClose={closeModalTaskDetail}
                    taskId={taskId}
                    archived={archived}
                />
            )}
            {modalTakeTask.value && (
                <ModalConfirm
                    isOpen={modalTakeTask.value}
                    text={t('confirmTakeoverTsk')}
                    onClose={modalTakeTask.onFalse}
                    onConfirm={() => handleTakeActualTask(taskId, urlTablePart)}
                    additionalButton={
                        urlTablePart === 'pull-to-me' && (
                            <Button
                                color="secondary"
                                startIcon={<MyIcon icon="view" />}
                                label={t('justOpen')}
                                size="small"
                                variant="text"
                                onClick={() => toTask(taskId)}
                            />
                        )
                    }
                />
            )}
        </>
    );
};

export default ModalCasesActualTasks;
