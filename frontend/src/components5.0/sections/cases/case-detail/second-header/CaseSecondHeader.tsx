import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { Stack } from '@mui/material';
// hooks
import { useResponsive } from '../../../../hooks/use-responsive';
import { useBoolean } from '../../../../hooks/use-boolean';
import { useParams } from '../../../../routes/hooks/use-params';
import { useFluxStore } from '../../../../hooks/use-flux-store';
import useDocuments from '../../../documents/use-documents';
// components
import MyIcon from '../../../../MyIcon';
import Button from '../../../../Button';
import NotesPopover from '../../../common/notes/NotesPopover';
import DocumentsPopover from '../../../documents/DocumentsPopover';
import ModalDocuments from '../../../documents/ModalDocuments';
import ActualTasks from './CaseActualTasksPopover';
import IconButton from '../../../../IconButton';
import ModalCasesActualTasks from './ModalCasesActualTasks';
import ModalCaseLogs from '../../../../Modals/modal-case-logs/ModalCaseLogs';
// types
import { CaseSecondHeaderProps } from '../../types';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';
import TabStore from '../../../../../flux/tab.store';
import TabActions from '../../../../../flux/tab.actions';

const CaseSecondHeader = ({
    caseTitle,
    caseStatus,
    actualTasks,
    openContactModal5,
    openHandoverModal,
    archived,
}: CaseSecondHeaderProps) => {
    // Use the useResponsive hook to determine if the screen size is greater than or equal to 'sm'
    const smUp = useResponsive('up', 'sm');
    // Use the useBoolean hook to create a boolean state for the ModalDocuments component
    const modalDoc = useBoolean();
    // Use the useBoolean hook to create a boolean state for the ModalNotes component
    const modalLogs = useBoolean();
    const actualTasksModal = useBoolean(false);

    const { t } = useTranslation();
    const { caseId }: { caseId?: number } = useParams();

    const { documentCountLoading, getDocumentCount, documentsCount } = useDocuments(
        'case',
        true,
        undefined,
        undefined,
        archived,
    );

    useEffect(() => {
        getDocumentCount();
    }, []);

    const objErrorLogs = useFluxStore(TabStore, TabActions, `tabCaseLogs-${caseId}`);
    const tabActionErrorLogs = objErrorLogs.actions;

    const handleDragEvent = (e: React.DragEvent) => {
        e.stopPropagation();
    };

    return (
        <>
            <Stack
                direction="row"
                flexGrow={1}
                justifyContent={{ xs: 'flex-end', md: 'space-between' }}
            >
                {/* Display the task information, documents popover, and notes popover */}
                {smUp ? (
                    <Stack
                        flexGrow={1}
                        direction="row"
                        spacing={{ xs: 0.5, sm: 1 }}
                        justifyContent={{ xs: 'space-around', sm: 'end' }}
                    >
                        {LoggedUserStore.isAdmin() && (
                            <Button
                                color="secondary"
                                startIcon={<MyIcon icon="logs" />}
                                label={t('logs')}
                                size="small"
                                variant="text"
                                onClick={modalLogs.onTrue}
                                count={
                                    objErrorLogs?.state?.labelCount === 0
                                        ? null
                                        : objErrorLogs?.state?.labelCount
                                }
                            />
                        )}

                        <ActualTasks
                            modalClose={actualTasksModal.onFalse}
                            modalOpen={actualTasksModal.onTrue}
                            modalValue={actualTasksModal.value}
                            rows={actualTasks}
                            archived={archived}
                        />
                        <DocumentsPopover
                            openDocumentModal={modalDoc.onTrue}
                            taskInfo={{
                                procName: caseTitle,
                                iprocCaseStatus: caseStatus,
                            }}
                            pillCount={documentsCount}
                            pillLoading={documentCountLoading}
                            entityName="case"
                            archived={archived}
                        />
                        {caseId && <NotesPopover id={caseId} archived={archived} />}
                    </Stack>
                ) : (
                    // Display the "More" button and popover if the screen size is less than 'sm'
                    <Stack direction="row" justifyContent="space-around" width={1}>
                        {LoggedUserStore.isAdmin() && (
                            <IconButton
                                color="secondary"
                                size="large"
                                icon="logs"
                                onClick={modalLogs.onTrue}
                                aria-label={t('logs')}
                                count={objErrorLogs?.state?.labelCount}
                            />
                        )}
                        <IconButton
                            color="secondary"
                            size="large"
                            icon="tasks"
                            count={actualTasks.length || undefined}
                            onClick={() => {
                                actualTasksModal.onTrue();
                            }}
                            aria-label={t('actualTsks')}
                        />
                        <IconButton
                            color="secondary"
                            size="large"
                            icon="documents"
                            count={documentsCount || null}
                            onClick={() => {
                                modalDoc.onTrue();
                            }}
                            aria-label={t('documents')}
                        />
                        {caseId && <NotesPopover id={caseId} archived={archived} />}
                    </Stack>
                )}
            </Stack>
            <div
                onDragEnter={handleDragEvent}
                onDragOver={handleDragEvent}
                onDragLeave={handleDragEvent}
                onDrop={handleDragEvent}
            >
                {/* ModalDocuments component to display the documents */}
                {modalDoc.value && (
                    <ModalDocuments
                        isOpen={modalDoc.value}
                        onClose={modalDoc.onFalse}
                        taskInfo={{
                            procName: caseTitle,
                            iprocCaseStatus: caseStatus,
                        }}
                        apiUrl={`/processes/${archived ? 'archived/' : ''}${caseId}/attachments`}
                        entityName="case"
                        archived={archived}
                    />
                )}
                {/* ModalActualtasks component to display the actual tasks */}
                <ModalCasesActualTasks
                    isOpen={actualTasksModal.value}
                    onClose={actualTasksModal.onFalse}
                    openContactModal5={openContactModal5}
                    openHandoverModal={openHandoverModal}
                    archived={archived}
                />
                {LoggedUserStore.isAdmin() && caseId && (
                    <ModalCaseLogs
                        isOpen={modalLogs.value}
                        onClose={modalLogs.onFalse}
                        caseId={caseId}
                        tabActionErrorLogs={tabActionErrorLogs}
                    />
                )}
            </div>
        </>
    );
};

export default CaseSecondHeader;
