import React, { useEffect, useState } from 'react';
// @mui
import {
    Box, Stack, Typography, useTheme,
} from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';
import { useParams } from '../../../routes/hooks';
import useCaseDetail from './use-case-detail';
import useDocuments from '../../documents/use-documents';
// components
import MainScrollHeight from '../../common/MainScrollHeight';
import AppBarSticky from '../../common/AppBarSticky';
import AppBarSlide from '../../common/AppBarSlide';
import CaseFirstHeader from './first-header/CaseFirstHeader';
import CaseSecondHeader from './second-header/CaseSecondHeader';
import { DragDropDocumentsIllustration } from '../../../illustrations/Illustrations';
// utils
import { pxToRem } from '../../../utils/utils';
import { NAV } from '../../../layouts/config-layout';
// types
import { CaseDetailsLayoutProps } from '../types';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';
// zustand
import { useStore } from '../../../zustand/boundStore';

const CaseDetailsLayout = ({
    caseTitle,
    children,
    eventButtonOnClick,
    closeButtonOnClick,
    caseStatus,
    eventButtonDisabled,
    showDiagram,
    toParentCase,
    printCase,
    openServiceModal,
    superirorDisabled,
    openContactModal5,
    openHandoverModal,
    openEditVariableModal,
    archived,
    caseInfo,
}: CaseDetailsLayoutProps) => {
    const { t } = useTranslation();
    const theme = useTheme();
    const [windowWidth, setWindowWidth] = useState(window.innerHeight);
    const [boxHeight, setBoxHeight] = useState<number | undefined>(undefined); // Initial height
    const { fetchActualTasks, actualTasks } = useCaseDetail();

    const params: any = useParams();

    useEffect(() => {
        fetchActualTasks(params?.caseId, archived);

        // Update the window height whenever it changes
        function handleResize() {
            setWindowWidth(window.innerWidth);
        }

        // Add a window resize event listener
        window.addEventListener('resize', handleResize);

        // Clean up the event listener when the component unmounts
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useEffect(() => {
        const element = document.getElementById('appbarId');
        const dynamicHeight = element?.clientHeight;
        setBoxHeight(dynamicHeight);
    }, [windowWidth]);

    const { documentOnLoad } = useDocuments(
        'case',
        /* true */ undefined,
        undefined,
        undefined,
        archived,
    ); // TODO recycle store

    const onDrop = (acceptedFiles: File[]) => {
        documentOnLoad({}, acceptedFiles, params?.caseId, undefined, undefined, true);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        noClick: true,
        noKeyboard: true,
        onDrop,
    });

    const canUpload =
        (caseInfo.iprocCaseStatus !== t('finished') || LoggedUserStore.hasRole(-4)) && !archived;

    let dropzoneRootProps = {};
    if (canUpload) {
        dropzoneRootProps = getRootProps();
    }

    const isNavMini = useStore((state) => state.isNavMini);
    const navWidth = isNavMini ? NAV.W_MINI : NAV.W_VERTICAL;

    return (
        <Box sx={{ position: 'relative' }} {...dropzoneRootProps}>
            {canUpload && <input {...getInputProps()} />}
            {isDragActive && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        zIndex: 9999,
                        backgroundColor: theme.palette.background.paper,
                        opacity: 0.9,
                    }}
                >
                    <Box
                        sx={{
                            position: 'fixed',
                            top: '50%',
                            left: `calc(50% + ${navWidth / 2}px)`,
                            transform: 'translate(-50%, -50%)',
                            textAlign: 'center',
                            backgroundColor: theme.palette.primary.light,
                            padding: '0.5rem 0.75rem',
                            borderRadius: '0.5rem',
                            border: `dashed 2px ${theme.palette.primary.main}`,
                        }}
                    >
                        <Stack
                            direction="column"
                            justifyContent="center"
                            alignItems="center"
                            spacing={1}
                        >
                            <DragDropDocumentsIllustration sx={{ height: pxToRem(120) }} />
                            <Typography variant="body1" sx={{ mt: 1 }}>
                                {t('uploadByDraggingAnywhere')}
                            </Typography>
                        </Stack>
                    </Box>
                </Box>
            )}
            <AppBarSticky>
                <CaseFirstHeader
                    closeButtonOnClick={closeButtonOnClick}
                    eventButtonOnClick={eventButtonOnClick}
                    caseTitle={caseTitle}
                    caseStatus={caseStatus}
                    eventButtonDisabled={eventButtonDisabled}
                    showDiagram={showDiagram}
                    toParentCase={toParentCase}
                    printCase={printCase}
                    openServiceModal={openServiceModal}
                    superirorDisabled={superirorDisabled}
                    openContactModal5={openContactModal5}
                    openEditVariableModal={openEditVariableModal}
                    archived={archived}
                    caseInfo={caseInfo}
                />
            </AppBarSticky>
            <AppBarSlide topSpacing={boxHeight}>
                <CaseSecondHeader
                    openContactModal5={openContactModal5}
                    caseTitle={caseTitle}
                    caseStatus={caseStatus}
                    actualTasks={actualTasks}
                    openHandoverModal={openHandoverModal}
                    archived={archived}
                />
            </AppBarSlide>
            <MainScrollHeight
                headersCount={3}
                sx={{
                    overflowX: 'auto',
                }}
            >
                {children}
            </MainScrollHeight>
        </Box>
    );
};

export default CaseDetailsLayout;
