import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { Stack, Box } from '@mui/material';
// hooks
import { useResponsive } from '../../../../hooks/use-responsive';
import { useBoolean } from '../../../../hooks/use-boolean';
// components
import Button from '../../../../Button';
import MyIcon from '../../../../MyIcon';
import Label from '../../../../label';
import CaseDetailsMorePopover from './CaseDetailsMorePopover';
import CaseModalHistory from './CaseModalHistory';
import CaseVariablesModal from './CaseVariablesModal';
import TextMaxLine from '../../../../text-max-line/text-max-line';
import CasePermissionsModal from './case-permissions/CasePermissionsModal';
import IconButton from '../../../../IconButton';
import CopyButton from '../../../../CopyButton';
// types
import { CaseFirstHeaderProps } from '../../types';

const CaseFirstHeader = ({
    caseTitle,
    caseStatus,
    eventButtonOnClick,
    closeButtonOnClick,
    eventButtonDisabled,
    showDiagram,
    toParentCase,
    printCase,
    openServiceModal,
    superirorDisabled,
    openContactModal5,
    openEditVariableModal,
    archived,
    caseInfo,
}: CaseFirstHeaderProps) => {
    const [textHovered, setTextHovered] = useState(false);
    const smUp = useResponsive('up', 'sm');
    const mdDown = useResponsive('down', 'md');
    const { t } = useTranslation();
    const modalHistory = useBoolean(false);
    const modalVariables = useBoolean(false);
    const modalPermissions = useBoolean(false);

    useEffect(() => {
        document.title = `${t('case')} - ${caseTitle}`;
    }, [caseTitle]);

    const handleDragEvent = (e: React.DragEvent) => {
        e.stopPropagation();
    };

    return (
        <>
            <Stack direction="row" flexGrow={1} justifyContent="space-between">
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="flex-start"
                    spacing={{ xs: 1, sm: 1.5 }}
                >
                    <IconButton
                        color="secondary"
                        icon="arrow-left"
                        size="medium"
                        onClick={closeButtonOnClick}
                    />

                    <MyIcon icon="cases" isActive />

                    <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        spacing={1}
                        alignItems={{ xs: 'flex-start', sm: 'center' }}
                        onMouseEnter={() => setTextHovered(true)}
                        onMouseLeave={() => setTextHovered(false)}
                    >
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                            }}
                        >
                            <TextMaxLine variant="h1" line={3} color="text.primary">
                                {caseTitle}
                            </TextMaxLine>
                            {textHovered && <CopyButton copyText={caseTitle} />}
                        </Box>

                        {caseStatus && (
                            <Label
                                size="small"
                                color="error"
                                startIcon="flag-filled"
                                variant="soft"
                            >
                                {caseStatus}
                            </Label>
                        )}
                    </Stack>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={{ xs: 0, md: 1 }}>
                    {smUp && (
                        <Button
                            id="case-close-btn"
                            color="secondary"
                            startIcon={<MyIcon icon="close" />}
                            label={t('close')}
                            size="small"
                            variant="text"
                            onClick={closeButtonOnClick}
                        />
                    )}
                    <CaseDetailsMorePopover
                        showDiagram={showDiagram}
                        toParentCase={toParentCase}
                        printCase={printCase}
                        openServiceModal={openServiceModal}
                        superirorDisabled={superirorDisabled}
                        openModalHistory={modalHistory.onTrue}
                        openModalVariables={modalVariables.onTrue}
                        openModalPermissions={modalPermissions.onTrue}
                        archived={archived}
                    />
                    {mdDown ? (
                        <Box
                            display="flex"
                            flexDirection="row-reverse"
                            gap={1}
                            position="fixed"
                            bottom="0rem"
                            left="0rem"
                            padding="0.75rem 1.5rem"
                            width="100vw"
                            bgcolor="background.paper"
                        >
                            <Button
                                id="case-event-btn"
                                fullWidth={!smUp}
                                color="primary"
                                startIcon={<MyIcon icon="signal" />}
                                label={t('event')}
                                size="small"
                                variant="contained"
                                onClick={eventButtonOnClick}
                                disabled={eventButtonDisabled}
                            />
                        </Box>
                    ) : (
                        <Button
                            id="case-event-btn"
                            fullWidth={!smUp}
                            color="primary"
                            startIcon={<MyIcon icon="signal" />}
                            label={t('event')}
                            size="small"
                            variant="contained"
                            onClick={eventButtonOnClick}
                            disabled={eventButtonDisabled}
                        />
                    )}
                </Stack>
            </Stack>
            <div
                onDragEnter={handleDragEvent}
                onDragOver={handleDragEvent}
                onDragLeave={handleDragEvent}
                onDrop={handleDragEvent}
            >
                <CaseModalHistory
                    isOpen={modalHistory.value}
                    onClose={modalHistory.onFalse}
                    openContactModal5={openContactModal5}
                    archived={archived}
                />
                <CaseVariablesModal
                    onClose={modalVariables.onFalse}
                    isOpen={modalVariables.value}
                    openEditVariableModal={openEditVariableModal}
                    archived={archived}
                    caseInfo={caseInfo}
                />
                <CasePermissionsModal
                    onClose={modalPermissions.onFalse}
                    isOpen={modalPermissions.value}
                    heading={caseTitle}
                    archived={archived}
                />
            </div>
        </>
    );
};

export default CaseFirstHeader;
