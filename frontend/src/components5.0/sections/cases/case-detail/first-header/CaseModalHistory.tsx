import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { Dialog, DialogContent, DialogTitle, Stack, Typography } from '@mui/material';
import {
    GridValidRowModel,
} from '@mui/x-data-grid-pro';
// components
import DataGrid from '../../../../data-grid/DataGrid';
import MyIcon from '../../../../MyIcon';
import Button from '../../../../Button';
import ModalTaskDetail from '../../../../Modals/modal-task-detail/ModalTaskDetail';
// hooks
import { useResponsive } from '../../../../hooks/use-responsive';
import useCases from '../../use-cases';
import { useParams } from '../../../../routes/hooks/use-params';
import { useBoolean } from '../../../../hooks';
// utils
import { momentFormatDate, checkLangMutationTemplInst } from '../../../../../common/utils';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';

type CaseModalHistoryProps = {
    isOpen: boolean;
    onClose: VoidFunction;
    openContactModal5: (row: any) => void;
    archived?: boolean;
};

// ---------------------------------ModalHeader---------------------------------

const CaseModalHistory = ({
    isOpen,
    onClose,
    openContactModal5,
    archived,
}: CaseModalHistoryProps) => {
    const [iTaskId, setITaskId] = useState<number | null>(null);
    const [taskhId, setTaskhId] = useState<number | null>(null);
    const [actualRow, setActualRow] = useState<any>(null);
    const smUp = useResponsive('up', 'sm');
    const { t } = useTranslation();
    // Get the case ID from the URL
    const { caseId }: { caseId?: string } = useParams();
    const { toNestedCase } = useCases();
    const modalTaskDetail = useBoolean();

    const dataGridRef = useRef<any>(null);
    useEffect(() => {
        const updateActualRow = () => {
            const selectedRow =
                dataGridRef.current?.table?.getSelectedRowModel()?.rows?.[0]?.original;
            setActualRow(selectedRow || null);
        };
        updateActualRow();
    }, [dataGridRef.current?.table?.getSelectedRowModel()?.rows]);

    const openModalTaskDetail = (row: GridValidRowModel) => {
        setITaskId(row.itask_id);
        setTaskhId(row.id);
        modalTaskDetail.onTrue();
    };

    const closeModalTaskDetail = () => {
        modalTaskDetail.onFalse();
        setITaskId(null);
        setTaskhId(null);
    };

    const onRowClick = (event: any, table: any, row: any) => {
        setActualRow(row.original);
    };
    // tas-1004: for non-admin users, hide technical tasks that don't generate a record to history
    const apiCallback = (items: any) =>
        items.filter((item: any) => {
            if (!LoggedUserStore.isAdmin() && item.itask_gen_history === 'N') {
                return false;
            }

            return true;
        });

    const COLUMNS: any[] = [
        {
            headerName: t('tskName') || '',
            field: 'itask_name_translated',
            type: 'string',
            accessorFn: (originalRow: any) => {
                return checkLangMutationTemplInst(originalRow, 'task_name') || '';
            },
        },
        ...(LoggedUserStore.isAdmin()
            ? [
                  {
                      field: 'itask_name',
                      headerName: t('defaultTaskName'),
                      type: 'string',
                  },
              ]
            : []),
        {
            headerName: t('solvedBy') || '',
            field: 'itaskh_finished_by_user_name',
            type: 'string',
            accessorFn: (originalRow: any) => {
                let val = '';
                if (
                    originalRow.itaskh_user_id === null ||
                    originalRow.itaskh_user_id === originalRow.itaskh_finished_by_user_id
                ) {
                    val = originalRow.itaskh_finished_by_user_name || '';
                } else if (
                    originalRow.itaskh_finished_by_user_name &&
                    originalRow.itaskh_finished_by_user_name.trim()
                ) {
                    val = `${originalRow.itaskh_finished_by_user_name} (${t(
                        'insteadOf',
                    )} : ${originalRow.itask_user_display_name || ''})`;
                }
                return val;
            },
        },
        {
            headerName: t('startDate') || '',
            field: 'itaskh_actual_date_start',
            type: 'date',
            accessorFn: (originalRow: any) =>
                originalRow.itaskh_actual_date_start &&
                momentFormatDate(originalRow.itaskh_actual_date_start, false, true),
        },
        {
            headerName: t('actualEnd') || '',
            field: 'itaskh_actual_date_finish',
            type: 'date',
            accessorFn: (originalRow: any) =>
                originalRow.itaskh_actual_date_finish &&
                momentFormatDate(originalRow.itaskh_actual_date_finish, false, true),
        },
        {
            headerName: t('detail') || '',
            field: 'itaskh_note',
            type: 'string',
        },

        {
            field: 'actions',
            type: 'actions',
            size: 30,
            minSize: 30,
            getActions: (params: any) => [
                {
                    icon: 'view',
                    label: t('detail'),
                    showInMenu: true,
                    disabled: params.row.original.itask_id === null,
                    onClick: () => openModalTaskDetail(params.row.original),
                },
                {
                    icon: 'up',
                    label: t('nested'),
                    showInMenu: true,
                    disabled: !(params.row.original.itaskh_subprocess_iproc_id > 0),
                    onClick: () => toNestedCase(params.row.original.itask_subprocess_iproc_id),
                },
                {
                    icon: 'account',
                    label: t('contact'),
                    showInMenu: true,
                    disabled: params.row.original.itaskh_user_id === null,
                    onClick: () => openContactModal5(params.row.original),
                },
            ],
        },
    ];

    return (
        <>
            <Dialog
                fullWidth
                fullScreen={!smUp}
                maxWidth="lg"
                onClose={() => onClose()}
                aria-labelledby="customized-dialog-title"
                open={isOpen}
            >
                <DialogTitle>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                        <Stack direction="row" spacing={1} height={1} alignItems="center">
                            <MyIcon icon="history" isActive />
                            <Typography variant="h1" height={1}>
                                {t('history')}
                            </Typography>
                        </Stack>
                        <Stack direction="row" spacing={1} alignItems="center">
                            <Button
                                startIcon={<MyIcon icon="view" />}
                                color="primary"
                                label={t('detail')}
                                size="small"
                                variant="contained"
                                aria-label={t('edit')}
                                disabled={
                                    actualRow === null ||
                                    (actualRow && actualRow.itask_id === null)
                                }
                                onClick={() => actualRow && openModalTaskDetail(actualRow)}
                            />
                            <Button
                                startIcon={<MyIcon icon="close" />}
                                color="secondary"
                                label={t('close')}
                                size="small"
                                variant="text"
                                aria-label={t('close')}
                                onClick={() => onClose()}
                            />
                        </Stack>
                    </Stack>
                </DialogTitle>
                <DialogContent dividers sx={{ height: '80vh', overflowX: 'hidden' }}>
                    <DataGrid
                        id="cases-history-datagrid"
                        apiUrl={`/processes/${archived ? 'archived/' : ''}${caseId}/history`}
                        columns={COLUMNS}
                        apiCallback={apiCallback}
                        handleRowDoubleClick={(row: any) =>
                            row.itask_id !== null && openModalTaskDetail(row)
                        }
                        defaultSortModel={[
                            {
                                field: 'itaskh_actual_date_finish',
                                sort: 'asc',
                            },
                        ]}
                        alwaysSort={{ field: 'id', sort: 'asc' }}
                        onRowClick={onRowClick}
                        dataGridRef={dataGridRef}
                    />
                </DialogContent>
            </Dialog>
            {modalTaskDetail.value && iTaskId && (
                <ModalTaskDetail
                    isOpen={modalTaskDetail.value}
                    onClose={closeModalTaskDetail}
                    taskId={iTaskId}
                    taskhId={taskhId}
                    isHistorical
                    archived={archived}
                />
            )}
        </>
    );
};

export default CaseModalHistory;
