import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';

// components
import Button from '../../../../Button';
import CustomPopper, { usePopover } from '../../../../custom-popover';
import IconButton from '../../../../IconButton';
import MyIcon, { Icon as IconType } from '../../../../MyIcon';
// hooks
import { useResponsive } from '../../../../hooks/use-responsive';
// types
import { CaseFirstHeadeMorePopoverProps } from '../../types';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';

const CaseDetailsMorePopover = ({
    showDiagram,
    toParentCase,
    printCase,
    openServiceModal,
    superirorDisabled,
    openModalHistory,
    openModalVariables,
    openModalPermissions,
    archived,
}: CaseFirstHeadeMorePopoverProps) => {
    const smUp = useResponsive('up', 'sm');

    const popover = usePopover();

    const { t } = useTranslation();

    const OPTIONS = useMemo(
        () => [
            {
                id: 'case-permissions-btn',
                label: t('permissions'),
                function: () => openModalPermissions(),
                icon: 'informative',
                disabled: false,
                isVisible: true,
            },
            {
                id: 'case-print-btn',
                label: t('print'),
                function: () => printCase(),
                icon: 'print',
                disabled: false,
                isVisible: true,
            },
            {
                id: 'case-vars-btn',
                label: t('vars'),
                function: () => openModalVariables(),
                icon: 'variable',
                isVisible: true,
            },
            {
                id: 'case-history-btn',
                label: t('history'),
                function: () => openModalHistory(),
                icon: 'history',
                isVisible: true,
            },
            {
                id: 'case-graph-btn',
                label: t('graph'),
                function: () => showDiagram(),
                icon: 'reports',
                disabled: false,
                isVisible: LoggedUserStore.isAdmin() && !archived,
            },
            {
                id: 'case-parent-case-btn',
                label: t('superior'),
                function: () => toParentCase(),
                icon: 'up',
                disabled: superirorDisabled,
                isVisible: true,
            },
            {
                id: 'case-change-entity-btn',
                label: t('changeEntity'),
                function: () => openServiceModal(),
                icon: 'settings',
                disabled: false,
                isVisible: LoggedUserStore.isSuperAdmin() && !archived,
            },
        ],
        [t],
    );

    return (
        <>
            {smUp ? (
                <Button
                    id="case-more-btn"
                    color="secondary"
                    startIcon={<MyIcon icon="more" />}
                    label={t('more')}
                    size="small"
                    variant="text"
                    onClick={popover.onOpen}
                    sx={{
                        ...(popover.open && {
                            background: (theme) => theme.palette.primary.light,
                        }),
                    }}
                />
            ) : (
                <IconButton
                    id="case-more-btn"
                    color="secondary"
                    icon="more"
                    size="large"
                    sx={{
                        ...(popover.open && {
                            background: (theme) => theme.palette.primary.light,
                        }),
                    }}
                    onClick={popover.onOpen}
                />
            )}

            <CustomPopper
                open={popover.open}
                onClose={popover.onClose}
                sx={{ width: 'auto', p: 0 }}
                placement="bottom-end"
                keepMounted
                anchorId="case-more-btn"
            >
                {OPTIONS.map(
                    (option, index) =>
                        option.isVisible && (
                            <MenuItem
                                key={index}
                                id={option.id}
                                onClick={option.function}
                                disabled={option.disabled}
                            >
                                <ListItemIcon>
                                    <MyIcon icon={option.icon as IconType} size={18} />
                                </ListItemIcon>
                                <ListItemText>{option.label}</ListItemText>
                            </MenuItem>
                        ),
                )}
            </CustomPopper>
        </>
    );
};
export default CaseDetailsMorePopover;
