import { useState } from 'react';
import ApiRequest from '../../../../api/apiRequest';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';

// Define the type for your custom hook's return value
// type HookReturnValue = {
//     // Define your hook's state variables and their types here
// };

const useCaseDetail = () /* : HookReturnValue */ => {
    // Define your hook's state variables and their initial values here
    const [actualTasks, setActualTasks] = useState([]);

    // Define any functions or methods that your hook exposes here

    // ---------------------------------FetchActualTasks---------------------------------
    const fetchActualTasks = async (caseId: any, archived?: boolean) => {
        try {
            const response = await ApiRequest.get(
                `/processes/${archived ? 'archived/' : ''}${caseId}/tasks?limit=${(window as any).config.restLimit}`,
            );

            // tas-1004: for non-admin users, hide technical tasks that don't generate a record to history
            const actualTasksFiltered = response.items.filter((item: any) => {
                if (!LoggedUserStore.isAdmin() && item.itask_gen_history === 'N') {
                    return false;
                }

                return true;
            });

            setActualTasks(actualTasksFiltered);
        } catch (error) {
            console.log(error);
        }
    };

    // Return the state variables and functions/methods that your hook exposes
    return {
        actualTasks,
        fetchActualTasks,
    };
};

export default useCaseDetail;
