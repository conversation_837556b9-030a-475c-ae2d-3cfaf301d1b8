import { GridValidRowModel } from '@mui/x-data-grid-pro';

export type CaseDetailsLayoutProps = {
    caseTitle: string;
    children: React.ReactNode;
    eventButtonOnClick: () => void;
    caseStatus: string;
    closeButtonOnClick: () => void;
    eventButtonDisabled?: boolean;
    showDiagram: () => void;
    toParentCase: VoidFunction;
    printCase: VoidFunction;
    openServiceModal: VoidFunction;
    superirorDisabled: boolean;
    openContactModal5: (row: any) => void;
    openHandoverModal: (row: any) => void;
    openEditVariableModal: (row: any) => void;
    archived?: boolean;
    caseInfo: any;
};

export type CaseFirstHeaderProps = Pick<
    CaseDetailsLayoutProps,
    | 'caseTitle'
    | 'caseStatus'
    | 'eventButtonOnClick'
    | 'closeButtonOnClick'
    | 'eventButtonDisabled'
    | 'showDiagram'
    | 'toParentCase'
    | 'printCase'
    | 'openServiceModal'
    | 'superirorDisabled'
    | 'openContactModal5'
    | 'openEditVariableModal'
    | 'archived'
    | 'caseInfo'
>;

export type CaseFirstHeadeMorePopoverProps = Pick<
    CaseDetailsLayoutProps,
    | 'showDiagram'
    | 'toParentCase'
    | 'printCase'
    | 'openServiceModal'
    | 'superirorDisabled'
    | 'archived'
> & {
    openModalHistory: VoidFunction;
    openModalVariables: VoidFunction;
    openModalPermissions: VoidFunction;
};

export type CaseSecondHeaderProps = Pick<
    CaseDetailsLayoutProps,
    'caseTitle' | 'caseStatus' | 'openContactModal5' | 'openHandoverModal' | 'archived'
> & {
    actualTasks: GridValidRowModel[];
};
