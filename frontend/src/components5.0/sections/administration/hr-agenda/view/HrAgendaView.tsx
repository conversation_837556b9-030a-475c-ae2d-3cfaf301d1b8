import React, { useEffect, useState } from 'react';
// @mui
import { TabContext, TabPanel } from '@mui/lab';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams, useRouter } from '../../../../routes/hooks';
import useHrAgendaVice from '../hr-agenda-vice/use-hr-agenda-vice';
import useHrAgendaUsers from '../hr-agenda-users/use-hr-agenda-users';
import { useResponsive } from '../../../../hooks/use-responsive';
// components
import AppBarSticky from '../../../common/AppBarSticky';
import HrAgendaLayout from '../HrAgendaLayout';
import MainStrictHeight from '../../../common/MainStrictHeight';
import HrAgendaVice from '../hr-agenda-vice/HrAgendaVice';
import HrAgendaUsers from '../hr-agenda-users/HrAgendaUsers';
import HrAgendaAgenda from '../hr-agenda-agenda/HrAgendaAgenda';
import HrAgendaLogs from '../HrAgendaLogs';
import MainScrollHeight from '../../../common/MainScrollHeight';
import HrAgendaUsersHeader from '../hr-agenda-users/HrAgendaUsersHeader';
import HrAgendaAgendaHeader from '../hr-agenda-agenda/HrAgendaAgendaHeader';
import HrAgendaViceHeader from '../hr-agenda-vice/HrAgendaViceHeader';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';

const HrAgendaView = () => {
    const { tabName }: { tabName?: string } = useParams();
    const [tableTape, setTableTape] = useState<'business' | 'business-locked'>('business');
    const { push } = useRouter();
    const { t } = useTranslation();

    const smUp = useResponsive('up', 'sm');

    const hrAgendaVice = useHrAgendaVice();
    const hrAgendaUsers = useHrAgendaUsers();

    useEffect(() => {
        if (tabName === 'vices' && !LoggedUserStore.hasRole(-5)) {
            push('/administration/hr-agenda/agenda');
        }
    }, []);

    useEffect(() => {
        if (tabName) {
            let subtitle = tabName;
            if (tabName === 'users') {
                subtitle = 'byUser';
            } else if (tabName === 'agenda') {
                subtitle = 'byAgenda';
            }

            document.title = `${t('hrAgenda')} - ${t(subtitle)}`;
        }
    }, [tabName]);

    return (
        <HrAgendaLayout>
            <TabContext
                value={
                    !LoggedUserStore.hasRole(-5) && tabName === 'vices'
                        ? 'agenda'
                        : tabName || 'agenda'
                }
            >
                {LoggedUserStore.hasRole(-5) && (
                    <TabPanel value="vices">
                        <AppBarSticky>
                            <HrAgendaViceHeader hrAgendaVice={hrAgendaVice} />
                        </AppBarSticky>
                        <MainStrictHeight headersCount={smUp ? 3 : 4}>
                            <HrAgendaVice hrAgendaVice={hrAgendaVice} />
                        </MainStrictHeight>
                    </TabPanel>
                )}
                <TabPanel value="users">
                    <AppBarSticky>
                        <HrAgendaUsersHeader hrAgendaUsers={hrAgendaUsers} />
                    </AppBarSticky>
                    <MainScrollHeight headersCount={smUp ? 2 : 3}>
                        <HrAgendaUsers hrAgendaUsers={hrAgendaUsers} />
                    </MainScrollHeight>
                </TabPanel>
                <TabPanel value="agenda">
                    <AppBarSticky>
                        <HrAgendaAgendaHeader
                            tableType={tableTape}
                            setTableType={setTableTape}
                        />
                    </AppBarSticky>
                    <MainStrictHeight headersCount={smUp ? 3 : 4}>
                        <HrAgendaAgenda
                            tableType={tableTape}
                        />
                    </MainStrictHeight>
                </TabPanel>
                <TabPanel value="logs">
                    <MainStrictHeight headersCount={smUp ? 2 : 3}>
                        <HrAgendaLogs />
                    </MainStrictHeight>
                </TabPanel>
            </TabContext>
        </HrAgendaLayout>
    );
};

export default HrAgendaView;
