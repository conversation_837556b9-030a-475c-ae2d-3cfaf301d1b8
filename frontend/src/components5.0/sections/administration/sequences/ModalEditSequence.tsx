import React from 'react';
import { FieldValues, useForm } from 'react-hook-form';
// @mui
import {
    Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Stack,
} from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import useSequences from './use-sequences';
import useEnterKeyListener from '../../../hooks/use-enter-key-listener';
// components
import IconButton from '../../../IconButton';
import FormProvider from '../../../form/FormProvider';
import Button from '../../../Button';
import MyIcon from '../../../MyIcon';
import TextField from '../../../form/textField/TextField';

type ModalEditSequenceProps = {
    isOpen: boolean;
    onClose: () => void;
    sequence: Record<string, any>;
}

const ModalEditSequence = ({ isOpen, onClose, sequence }: ModalEditSequenceProps) => {
    const { t } = useTranslation();
    const { editSequence } = useSequences();

    const methods = useForm();
    const {
        handleSubmit,
        formState: { isSubmitting },
    } = methods;

    const onSubmit = (data: FieldValues) => {
        const postObj = {
            seq_id: data.seq_id,
            seq_name: sequence.seq_name,
        };
        editSequence(postObj);
        onClose();
    };

    useEnterKeyListener(isOpen, handleSubmit(onSubmit));

    return (
        <Dialog
            fullWidth
            maxWidth="xs"
            open={isOpen}
            onClose={onClose}
            aria-labelledby="modal-edit-sequence"
            scroll="paper"
        >
            <DialogTitle display="flex" justifyContent="end">
                <IconButton
                    color="secondary"
                    icon="close"
                    size="medium"
                    onClick={onClose}
                    aria-label={t('close')}
                />
            </DialogTitle>
            <DialogContent>
                <DialogContentText
                    variant="body1"
                    id="modal-edit-sequence"
                    textAlign="center"
                    sx={{ mb: 2 }}
                >
                    {t('seqIdEdit')}
                </DialogContentText>
                <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
                    <Stack
                        width={1}
                        height={1}
                        alignItems="center"
                    >
                        <TextField
                            key="seq_id"
                            name="seq_id"
                            label={`${t('seqId')}:`}
                            validations={{ required: t('isRequired') }}
                            value={sequence.seq_id}
                            onlyNumbers
                        />
                    </Stack>
                </FormProvider>
            </DialogContent>
            <DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                    color="primary"
                    label={t('save')}
                    size="small"
                    variant="contained"
                    startIcon={<MyIcon icon="save" />}
                    disabled={isSubmitting}
                    onClick={handleSubmit(onSubmit)}
                />
                <Button
                    color="secondary"
                    label={t('cancel')}
                    size="small"
                    variant="text"
                    startIcon={<MyIcon icon="close" />}
                    onClick={onClose}
                />
            </DialogActions>
        </Dialog>
    );
};

export default ModalEditSequence;
