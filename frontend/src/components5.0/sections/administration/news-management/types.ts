export type NewsPostTagType = {
    id: number;
    tag_name: string;
    created_at: string;
    updated_at: string;
} & (
    Record<`tag_name_${string}`, string | null>
);

export type NewsPostTypeGET = {
    post_id: number;
    post_title: string;
    post_content: string | null;
    created_at: string;
    updated_at: string;
    post_phone: string | null;
    post_email: string | null;
    post_custom_url: string | null;
    post_state: 'ACTIVE' | 'INACTIVE' | 'PLANNED';
    post_publication_end_date: string | null;
    post_send_email: boolean;
    post_priority: 1 | 0;
    post_created_by: number;
    post_updated_by: number;
    post_publication_date: string | null;
    post_visibility: {
        organization_structures: { orgstr_id: number; orgstr_name: string }[];
        roles: { role_id: number; role_name: string }[];
        users: { user_id: number; user_display_name: string }[];
        templates: (
            { tproc_id: number; tproc_version: number; tproc_name: string } & (
                Record<`tproc_name_${string}`, string | null>
            )
        )[];
        headers: (
            { header_id: number; header_name: string } & (
                Record<`header_name_${string}`, string | null>
            )
        )[];
        tasks: (
            { ttask_id: number; ttask_name: string } & (
                Record<`ttask_name_${string}`, string | null>
            )
        )[];
    };
    post_tags: (
        {
            /** tag id */
            tag: number;
            tag_name: string;
        } & (
            Record<`tag_name_${string}`, string | null>
        )
    )[];
} & (
    Record<`post_title_${string}`, string | null>
) & (
    Record<`post_content_${string}`, string | null>
);

export type NewsPostTypePOST = (
    Omit<
        NewsPostTypeGET,
        'post_id'
        | 'created_at'
        | 'updated_at'
        | 'post_created_by'
        | 'post_updated_by'
        | 'post_visibility'
        | 'post_tags'
    > & {
        post_visibility: {
            organization_structures: number[];
            roles: number[];
            users: number[];
            templates: number[]; // TODO: change to `${number}-${number}` or similiar format including tproc_version once the backend accepts it
            headers: number[];
            tasks: number[];
        };
        post_tags: number[];
    }
);

export type SeenPriorityNewsInfoType = {
    /** min date for fetch - ISO time string */
    minDate: string | null;
}

export type SeenBellNewsInfoType = {
    /** unseen posts from the last time */
    lastVisiblePostIds: number[];
    /** min date for fetch - ISO time string */
    minDate: string | null;
}

export type SeenTaskNewsInfoType = (
    Record<number, {
        /** unseen posts from the last time */
        lastVisiblePostIds: number[];
        /** min date for fetch - ISO time string */
        minDate: string;
        /** ISO time string */
        lastTimeVisited: string;
    }>
);
