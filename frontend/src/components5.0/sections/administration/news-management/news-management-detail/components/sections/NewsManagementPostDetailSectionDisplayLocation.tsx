import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { Stack } from '@mui/material';
// hooks
import { useFormContext } from 'react-hook-form';
import useNewsManagement from '../../../use-news-management';
// components
import SectionsCard from '../../../../../../SectionsCard';
import Select from '../../../../../../form/select/Select';
// types
import { SelectPropTypes } from '../../../../../../form/select/types';

const NewsManagementPostDetailSectionDisplayLocation = () => {
    const { setValue, getValues } = useFormContext();

    const { t } = useTranslation();

    const {
        fetchTemplateOptions,
        fetchTemplateHeaderOptions,
        fetchTemplateTaskOptions,
    } = useNewsManagement();

    // template options
    const [templateOptions, setTemplateOptions] = useState<SelectPropTypes['options']>([]);

    // sub options (headers, tasks based on selected template)
    const [headerOptions, setHeaderOptions] = useState<SelectPropTypes['options']>([]);
    const [tasksOptions, setTasksOptions] = useState<SelectPropTypes['options']>([]);

    // loading state for sub options
    const [subOptionsLoading, setSubOptionsLoading] = useState(false);

    // we could use form context, but we need the states to be in sync with each other...
    // ...so that the disabled state doesn't flash on and off when the template...
    // ...is changed but setSubOptionsLoading has not taken effect yet
    const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

    const handleSelectedTemplateChange = (newSelectedTemplate: string | null) => {
        if (selectedTemplate && selectedTemplate !== newSelectedTemplate) {
            // clear the sub options if the template has changed
            setValue('post_visibility.headers', []);
            setValue('post_visibility.tasks', []);
        }

        setSelectedTemplate(newSelectedTemplate);

        if (newSelectedTemplate === null) {
            setHeaderOptions([]);
            setTasksOptions([]);
            setSubOptionsLoading(false);
            return;
        }

        setSubOptionsLoading(true);

        const [tprocId, tprocVersion] = newSelectedTemplate.split('-');

        Promise.all([
            fetchTemplateHeaderOptions(tprocId, tprocVersion),
            fetchTemplateTaskOptions(tprocId, tprocVersion),
        ]).then(([headers, tasks]: SelectPropTypes['options'][]) => {
            setHeaderOptions(headers);
            setTasksOptions(tasks);
            setSubOptionsLoading(false);
        });
    };

    // inital load of template processes
    useEffect(() => {
        fetchTemplateOptions().then((options) => {
            setTemplateOptions(options);
        });

        const preselectedTemplate = getValues('post_visibility.templates[0]');
        if (preselectedTemplate) {
            handleSelectedTemplateChange(preselectedTemplate);
        }
    }, []);

    return (
        <SectionsCard title={t('displayLocation')}>
            <Stack sx={{ width: '100%' }} spacing={1.5}>
                <Select
                    name="post_visibility.templates[0]"
                    label={t('pickNewsPostDisplayOnTemplate')}
                    options={templateOptions}
                    nullable
                    fullWidth
                    outputValueId
                    onComponentChange={(_name, value) => handleSelectedTemplateChange(value)}
                />
                <Select
                    name="post_visibility.headers"
                    label={t('pickNewsPostDisplayOnHeaders')}
                    options={headerOptions}
                    loading={subOptionsLoading}
                    disabled={subOptionsLoading || !selectedTemplate}
                    helperText={
                        !selectedTemplate
                            ? t('pickNewsPostDisplaySubOptionsHelperText')
                            : undefined
                    }
                    nullable
                    multiple
                    fullWidth
                />
                <Select
                    name="post_visibility.tasks"
                    label={t('pickNewsPostDisplayOnTasks')}
                    options={tasksOptions}
                    loading={subOptionsLoading}
                    disabled={subOptionsLoading || !selectedTemplate}
                    helperText={
                        !selectedTemplate
                            ? t('pickNewsPostDisplaySubOptionsHelperText')
                            : undefined
                    }
                    nullable
                    multiple
                    fullWidth
                />
            </Stack>
        </SectionsCard>
    );
};

export default NewsManagementPostDetailSectionDisplayLocation;
