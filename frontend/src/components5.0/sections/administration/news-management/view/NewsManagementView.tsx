import { useState } from 'react';
// mui
import { GridColDef } from '@mui/x-data-grid-pro';
// hooks
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../routes/hooks';
import { useBoolean } from '../../../../hooks';
import { useStore } from '../../../../zustand/boundStore';
import useNewsManagement from '../use-news-management';
// utils
import { checkLangMutation, momentFormatDate } from '../../../../../common/utils';
import {
    getNewsDataGridSpecialFilterOperators,
    getNewsPostStateDataGridHeaderFilterOptions,
    getNewsPostStateTranslation,
} from '../newsManagementUtils';
// components
import NewsManagementLayout from '../NewsManagementLayout';
import NewsManagementDataGrid from '../NewsManagementDataGrid';
import { ModalAlert } from '../../../../Modals';
import 'overview-components/dist/components/lit-badge';
import { Content } from 'overview-components/dist/components/lit-badge';
// types
import { NewsPostTypeGET } from '../types';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';

const NewsManagementView = () => {
    const deleteModal = useBoolean(false);
    const [singleSelectedPost, setSingleSelectedPost] = useState<NewsPostTypeGET | null>(null);
    const [userLanguage] = useState(LoggedUserStore.getState().userLanguage);

    const { push } = useRouter();
    const { t } = useTranslation();
    const { dataGridSelectedRows, updateRows, setDataGridSelectedRows } = useStore();

    const { deletePosts } = useNewsManagement();

    const handleDeleteModalConfirm = () => {
        deletePosts(
            singleSelectedPost !== null
                ? [singleSelectedPost.post_id]
                : (dataGridSelectedRows.get('news-management') as number[]),
        ).then(() => {
            updateRows();
            setDataGridSelectedRows([], 'news-management');
        });
    };

    const columns: any = [
        {
            field: 'post_title',
            headerName: t('name'),
            type: 'string',
            accessorFn: (originalRow: any) => {
                return checkLangMutation(originalRow, 'post_title');
            },
        },
        {
            field: 'post_publication_date',
            headerName: t('published'),
            type: 'date',
            accessorFn: (originalRow: any) =>
                originalRow.post_publication_date &&
                momentFormatDate(originalRow.post_publication_date, false, true),
        },
        {
            field: 'post_tags',
            headerName: t('tags'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) =>
                originalRow.post_tags
                    .map((tag: any) => checkLangMutation(tag, 'tag_name'))
                    .join(', '),
            filterOperators: getNewsDataGridSpecialFilterOperators('post_tags', t, userLanguage),
        },
        {
            field: 'post_visibility.roles',
            headerName: t('rolesRestriction'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) =>
                originalRow.post_visibility.roles.map((role: any) => role.role_name).join(', '),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_visibility.roles',
                t,
                userLanguage,
            ),
        },
        {
            field: 'post_visibility.users',
            headerName: t('usersRestriction'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) =>
                originalRow.post_visibility.users
                    .map((user: any) => user.user_display_name)
                    .join(', '),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_visibility.users',
                t,
                userLanguage,
            ),
        },
        {
            field: 'post_visibility.organization_structures',
            headerName: t('orgUnitsRestriction'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) =>
                originalRow.post_visibility.organization_structures
                    .map((orgStr: any) => orgStr.orgstr_name)
                    .join(', '),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_visibility.organization_structures',
                t,
                userLanguage,
            ),
        },
        {
            field: 'post_state',
            headerName: t('state'),
            type: 'select',
            valueOptions: getNewsPostStateDataGridHeaderFilterOptions(t),
            accessorFn: (originalRow: any) =>
                getNewsPostStateTranslation(originalRow.post_state, t),
            cell: (params: any, html: any) => {
                let labelColor: Content = 'success';

                if (params.row.original.post_state === 'INACTIVE') {
                    labelColor = 'neutral';
                } else if (params.row.original.post_state === 'PLANNED') {
                    labelColor = 'warning';
                }

                return html`
                    <lit-badge text=${params.getValue()} content=${labelColor}></lit-badge>
                `;
            },
        },
        {
            field: 'actions',
            type: 'actions',
            size: 30,
            minSize: 30,
            getActions: (params: any) => [
                {
                    icon: 'pen',
                    label: t('edit'),
                    showInMenu: true,
                    onClick: () => {
                        push(`/administration/news-management/post/${params.row.original.id}`);
                    },
                },
                {
                    icon: 'trash',
                    label: t('delete'),
                    showInMenu: true,
                    onClick: () => {
                        setSingleSelectedPost(params.row.original.id);
                        deleteModal.onTrue();
                    },
                },
            ],
        },
    ];

    return (
        <>
            <NewsManagementLayout
                onDeleteMultiplePosts={() => {
                    setSingleSelectedPost(null);
                    deleteModal.onTrue();
                }}
            >
                <NewsManagementDataGrid
                    id="news-management"
                    apiUrl="/posts?all=true"
                    columns={columns}
                    clickCallbackCard={(row: any) => {
                        push(`/administration/news-management/post/${row.post_id}`);
                    }}
                />
            </NewsManagementLayout>
            {deleteModal.value && (
                <ModalAlert
                    text={
                        singleSelectedPost !== null
                            ? t('confirmDeletePost', { postTitle: singleSelectedPost.post_title })
                            : t('confirmDeleteMultiplePosts')
                    }
                    isOpen={deleteModal.value}
                    onClose={deleteModal.onFalse}
                    onDelete={handleDeleteModalConfirm}
                />
            )}
        </>
    );
};

export default NewsManagementView;
