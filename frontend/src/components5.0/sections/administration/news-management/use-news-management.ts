import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
// utils
import ApiRequest from '../../../../api/apiRequest';
import { checkLangMutation, guid } from '../../../../common/utils';
// types
import { NewsManagementPostDetailForm } from './news-management-detail/types';
import { NewsPostTypeGET, NewsPostTypePOST } from './types';
import { SelectPropTypes } from '../../../form/select/types';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';
import AlertsActions from '../../../../components/alerts/alerts.actions';

const useNewsManagement = () => {
    const { t } = useTranslation();
    const [languages] = useState(LoggedUserStore.getState().languages);

    // ---------------------------------------- getFullPublicationDate ----------------------------------------

    const getFullPublicationDate = (
        publicationDate: string | null,
        publicationTime: string | null,
        clearAfterMinutes?: boolean,
    ): string | null => {
        let postPublicationDate = publicationDate ? moment(publicationDate) : null;
        if (postPublicationDate !== null && publicationTime) {
            // add time to the full publication date
            postPublicationDate = postPublicationDate.set({
                hour: Number(publicationTime.split(':')[0]),
                minute: Number(publicationTime.split(':')[1]),
                ...(clearAfterMinutes && {
                    second: 0,
                    millisecond: 0,
                }),
            });
        }

        return postPublicationDate?.toISOString() || null;
    };

    // ---------------------------------------- submitPostFormData ----------------------------------------

    const submitPostFormData = async (
        postId: number | 'new',
        data: NewsManagementPostDetailForm,
        defaultValues: NewsManagementPostDetailForm,
    ): Promise<{ result: number; id: number }> => {
        const initialPostState = defaultValues.post_state;
        const newPostState = data.post_state;

        let usedPublicationDate = null;

        // made with the help of a 3-state state diagram, i suggest you do the same when you need to change the logic :D
        if (newPostState === 'PLANNED') {
            usedPublicationDate = getFullPublicationDate(
                data.publicationDate,
                data.publicationTime,
                true, // clear the seconds and milliseconds
            );
        } else if (
            (
                initialPostState === 'PLANNED'
            ) || (
                initialPostState === 'INACTIVE'
                && (newPostState === 'ACTIVE' || newPostState === 'INACTIVE')
            )
        ) {
            usedPublicationDate = null; // BE will set the current date for ACTIVE state & keep null for INACTIVE
        } else if (
            initialPostState === 'ACTIVE'
            && (newPostState === 'INACTIVE' || newPostState === 'ACTIVE')
        ) {
            usedPublicationDate = getFullPublicationDate(
                defaultValues.publicationDate,
                defaultValues.publicationTime,
                false, // keep the seconds and milliseconds
            ); // the initial date was when the post was activated
        }

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: t('alrSaving'),
        });

        const url = `/posts${postId === 'new' ? '' : `/${postId}`}`;

        const obj: NewsPostTypePOST = {
            post_title: data.post_title,
            ...Object.fromEntries(
                languages.map((lang: string) => ([`post_title_${lang}`, data[`post_title_${lang}`] || null])),
            ),
            post_content: data.post_content,
            ...Object.fromEntries(
                languages.map((lang: string) => ([`post_content_${lang}`, data[`post_content_${lang}`] || null])),
            ),
            post_tags: data.post_tags,
            post_phone: data.post_phone || null,
            post_email: data.post_email || null,
            post_custom_url: data.post_custom_url || null,
            post_state: data.post_state || 'INACTIVE',
            post_publication_end_date: data.endOnSpecificDate ? data.post_publication_end_date || null : null,
            post_send_email: data.post_send_email,
            post_priority: data.post_priority ? 1 : 0,
            post_publication_date: usedPublicationDate,
            post_visibility: {
                ...data.post_visibility,
                // .filter(Boolean) because the empty select gives '' as its value
                templates: data.post_visibility.templates.filter(Boolean).map((template) => (
                    // TODO: change to `tproc_id` & `tproc_version` combo once the backend accepts it
                    template.split('-').map((item) => Number(item))[0]
                )),
            },
        };

        return ApiRequest.post(url, obj)
            .then((result: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrPostSaved'),
                });

                return result;
            })
            .catch((error: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: t('alrPostSaveFailed'),
                    serverError: error,
                });
            });
    };

    // ---------------------------------------- fetchPostFormData ----------------------------------------

    const fetchPostFormData = async (postId: number): Promise<NewsManagementPostDetailForm> => {
        return ApiRequest.get(`/posts/${postId}`)
            .then((payload: NewsPostTypeGET) => {
                const publicationTime = (
                    payload.post_publication_date
                        ? moment(payload.post_publication_date).format('HH:mm')
                        : null
                );

                return {
                    post_title: payload.post_title,
                    ...Object.fromEntries(
                        languages.map((lang: string) => ([`post_title_${lang}`, payload[`post_title_${lang}`] || ''])),
                    ),
                    post_content: payload.post_content || '',
                    ...Object.fromEntries(
                        languages.map((lang: string) => ([`post_content_${lang}`, payload[`post_content_${lang}`] || ''])),
                    ),
                    post_tags: payload.post_tags.map((tag) => tag.tag),
                    post_phone: payload.post_phone || '',
                    post_email: payload.post_email || '',
                    post_custom_url: payload.post_custom_url || '',
                    post_state: payload.post_state,
                    endOnSpecificDate: payload.post_publication_end_date !== null,
                    post_publication_end_date: payload.post_publication_end_date || '',
                    post_send_email: payload.post_send_email,
                    post_priority: payload.post_priority === 1,
                    publicationDate: payload.post_publication_date || null,
                    publicationTime: publicationTime || null,
                    post_visibility: {
                        organization_structures: payload.post_visibility.organization_structures.map((org) => org.orgstr_id),
                        roles: payload.post_visibility.roles.map((role) => role.role_id),
                        users: payload.post_visibility.users.map((user) => user.user_id),
                        templates: payload.post_visibility.templates.map((template) => (
                            `${template.tproc_id}-${template.tproc_version}`
                        )),
                        headers: payload.post_visibility.headers.map((header) => header.header_id),
                        tasks: payload.post_visibility.tasks.map((header) => header.ttask_id),
                    },
                } as NewsManagementPostDetailForm;
            })
            .catch((error: any) => {
                const alertId = guid();
                AlertsActions.addAlert({
                    id: alertId,
                    type: 'alert',
                    message: t('alrPostLoadFailed'),
                    serverError: error,
                });
            });
    };

    // ---------------------------------------- deletePosts ----------------------------------------

    const deletePosts = async (ids: number[]) => {
        if (ids.length === 0) {
            return;
        }

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: t('alrDeleting'),
        });

        await ApiRequest.delete('/posts', { ids })
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: ids.length > 1 ? t('alrPostsDeleted') : t('alrPostDeleted'),
                });
            })
            .catch((errorMessage: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: ids.length > 1 ? t('alrPostsDeleteFailed') : t('alrPostDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    };

    // ---------------------------------------- fetchTemplateOptions ----------------------------------------

    const fetchTemplateOptions = async (): Promise<SelectPropTypes['options']> => {
        return ApiRequest.get('/template-processes').then((payload: any) => {
            const { items } = payload;

            return items.filter((item: any) => (
                item.id.tproc_version === 1 // TODO: remove filter once the BE supports saving multiple versions of the same template
            )).map((item: any) => ({
                value: `${item.id.tproc_id}-${item.id.tproc_version}`,
                title: checkLangMutation(item, 'tproc_name', true),
            }));
        }).catch((error: any) => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'alert',
                message: t('alrFailedTemplateProcesses'),
                serverError: error,
            });

            return [];
        });
    };

    // ---------------------------------------- fetchTemplateHeaderOptions ----------------------------------------

    const fetchTemplateHeaderOptions = async (
        tprocId: number | string,
        tprocVersion: number | string,
    ): Promise<SelectPropTypes['options']> => {
        return ApiRequest.get(`/template-processes/${tprocId}/${tprocVersion}/headers`).then((payload: any) => {
            const { items } = payload;

            return items.map((item: any) => ({
                value: item.id,
                title: checkLangMutation(item, 'header_name', true),
            }));
        }).catch((error: any) => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'alert',
                message: t('alrTempHeadersLoadFailed'),
                serverError: error,
            });

            return [];
        });
    };

    // ---------------------------------------- fetchTemplateTaskOptions ----------------------------------------

    const fetchTemplateTaskOptions = async (
        tprocId: number | string,
        tprocVersion: number | string,
    ): Promise<SelectPropTypes['options']> => {
        return ApiRequest.get(`/template-processes/${tprocId}/${tprocVersion}/template-tasks`).then((payload: any) => {
            const { items } = payload;

            return items.map((item: any) => ({
                value: item.id,
                title: checkLangMutation(item, 'ttask_name', true),
            }));
        }).catch((error: any) => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'alert',
                message: t('alrTempTasksLoadFailed'),
                serverError: error,
            });

            return [];
        });
    };

    // ---------------------------------------- fetchTagOptions ----------------------------------------
    const fetchTagOptions = async (): Promise<SelectPropTypes['options']> => {
        return ApiRequest.get('/posts/tags').then((payload: any) => {
            const { items } = payload;

            return items.map((item: any) => ({
                value: item.tag_id,
                title: checkLangMutation(item, 'tag_name', true),
            }));
        }).catch((error: any) => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'alert',
                message: t('alrLoadingTagsFailed'),
                serverError: error,
            });

            return [];
        });
    };

    // ---------------------------------------- return ----------------------------------------

    return {
        submitPostFormData,
        fetchPostFormData,
        deletePosts,
        fetchTemplateOptions,
        fetchTemplateTaskOptions,
        fetchTemplateHeaderOptions,
        fetchTagOptions,
    };
};

export default useNewsManagement;
