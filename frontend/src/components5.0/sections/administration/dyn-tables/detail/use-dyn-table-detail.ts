import { useState } from 'react';
import { isBoolean } from 'lodash';
import { FieldValues } from 'react-hook-form';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams } from '../../../../routes/hooks';
// utils
import ApiRequest from '../../../../../api/apiRequest';
import { checkLangMutation, guid } from '../../../../../common/utils';
// zustand
import { useStore } from '../../../../zustand/boundStore';
// flux
import LoggedUserStore from '../../../../../flux/loggedUser.store';
import AlertsActions from '../../../../../components/alerts/alerts.actions';

type DynTableFormDefaultValues = {
    dtName?: string;
    dtDescription?: string;
    dtNote?: string;
    dtPublic?: boolean;
    dtAccessRoleId?: undefined;
    dtAccessRoleName?: undefined;
    dtAccessHeaderId?: undefined;
    firstLineColNames?: boolean;
    fileData?: string;
    fileName?: string;
    delimiter?: string;
};

const useDynTableDetail = () => {
    const [defaultValues, setDefaultValues] = useState<DynTableFormDefaultValues>({});
    const [uploadedFromFile, setUploadedFromFile] = useState<string>('');
    const [loading, setLoading] = useState(true);
    const [headersOptions, setHeadersOptions] = useState<any[]>([]);
    const [rowOptions, setRowOptions] = useState<any[]>([]);
    const { dynTableId }: { dynTableId?: string } = useParams();
    const { t } = useTranslation();

    const { updateRows } = useStore();
    const dynTableDetailName = useStore((state) => state.dynTableDetailName);
    const newDynTableId = useStore((state) => state.newDynTableId);

    const setDynTableDetailColumns = useStore((state) => state.setDynTableDetailColumns);
    const setDynTableDetailName = useStore((state) => state.setDynTableDetailName);
    const setColumnsLoading = useStore((state) => state.setColumnsLoading);

    const loadHeaders = () => {
        ApiRequest.get(`/header?order=header_name&limit=${(window as any).config.restLimit}`)
            .then((payload: any) => {
                const headersArr: { value: number; title: string }[] = [];

                payload.items.forEach((item: Record<string, any>) => {
                    headersArr.push({
                        value: item.id,
                        title: checkLangMutation(item, 'header_name'),
                    });
                });

                setHeadersOptions(headersArr);
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrTempHeadersLoadFailed'),
                    serverError: err,
                });
            });
    };

    const loadDynTable = (id?: number) => {
        const tableId = id || dynTableId || newDynTableId;
        if (!tableId) {
            setLoading(false);
            setDefaultValues({
                dtName: '',
                dtDescription: '',
                dtNote: '',
                dtPublic: true,
                dtAccessRoleId: undefined,
                dtAccessRoleName: undefined,
                dtAccessHeaderId: undefined,
                firstLineColNames: true,
                fileData: '',
                fileName: '',
                delimiter: ',',
            });

            return;
        }

        ApiRequest.get(`/dyn-table/${tableId}`)
            .then((payload: any) => {
                let uploadedFrom = '';
                const fileHistoryArr = JSON.parse(payload.dt_file_history || '[]');
                const lastFileHistory = fileHistoryArr[fileHistoryArr.length - 1];

                if (lastFileHistory) {
                    const localization =
                        (window as any).config.dateTimeFormat ||
                        LoggedUserStore.getState().userLocale;
                    const intOpts: Intl.DateTimeFormatOptions = {
                        day: 'numeric',
                        month: 'numeric',
                        year: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric',
                        second: 'numeric',
                    };

                    const date = Intl.DateTimeFormat(localization, intOpts).format(
                        Date.parse(lastFileHistory.date),
                    );
                    uploadedFrom = `${lastFileHistory.fileName} - ${date}`;
                }

                const isPublic = isBoolean(payload.dt_public)
                    ? payload.dt_public
                    : payload.dt_public === 'Y';

                setDynTableDetailName(payload.dt_name);
                setUploadedFromFile(uploadedFrom);
                setLoading(false);

                setDefaultValues({
                    dtName: payload.dt_name,
                    dtDescription: payload.dt_description,
                    dtNote: payload.dt_note,
                    dtPublic: isPublic,
                    dtAccessRoleId: payload.dt_access_role_id,
                    dtAccessRoleName: payload.role_name,
                    dtAccessHeaderId: payload.dt_access_header_id,
                    firstLineColNames: true,
                    fileData: '',
                    fileName: '',
                    delimiter: ',',
                });
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrDynTableDataLoadFailed'),
                    serverError: err,
                });
            });
    };

    const loadDynTableColumns = (id?: number, firstLoad?: boolean) => {
        const tableId = id || dynTableId || newDynTableId;
        ApiRequest.get(
            `/dyn-table/${tableId}/cols?order=dtc_id&sort=asc&limit=${(window as any).config.restLimit}`,
        )
            .then((payload: any) => {
                setDynTableDetailColumns(payload.items);
                if (firstLoad) {
                    setColumnsLoading(false);
                } else {
                    updateRows();
                }
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrDynTableDataLoadFailed'),
                    serverError: err,
                });
            });
    };

    const deleteRow = (rowId: string) => {
        ApiRequest.delete(`/dyn-table/${dynTableId || newDynTableId}/rows/${encodeURIComponent(rowId)}`)
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrDeleted'),
                });
                updateRows();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrDeleteFailed'),
                    serverError: err,
                });
            });
    };

    const editRow = (rowId: number, values: FieldValues) => {
        const dtvValues: Record<string, string> = {};
        Object.keys(values).forEach((key) => {
            dtvValues[key] = values[key];
        });

        const postObj = {
            dtv_index: rowId,
            dlv_values: dtvValues,
        };

        ApiRequest.post(
            `/dyn-table/${dynTableId || newDynTableId}/change-values`,
            JSON.stringify(postObj),
        )
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrSaved'),
                });
                updateRows();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrSavingFailed'),
                    serverError: err,
                });
            });
    };

    const addRow = (values: FieldValues) => {
        const dtvValues: Record<string, string> = {};
        Object.keys(values).forEach((key) => {
            if (key !== 'dlvIndex' && key !== 'moveIndex') {
                dtvValues[key] = values[key];
            }
        });

        const postObj = {
            dtv_index: values.dlvIndex,
            dtv_order_index: values.moveIndex || undefined,
            values: dtvValues,
        };

        ApiRequest.post(
            `/dyn-table/${dynTableId || newDynTableId}/add-row`,
            JSON.stringify(postObj),
        )
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrSaved'),
                });
                updateRows();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrSavingFailed'),
                    serverError: err,
                });
            });
    };

    const moveRow = (values: FieldValues, rowId: number) => {
        const postObj = {
            dtv_order_index: values.moveIndex || undefined,
        };

        ApiRequest.post(
            `/dyn-table/${dynTableId || newDynTableId}/rows/${encodeURIComponent(rowId)}/move`,
            JSON.stringify(postObj),
        )
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrSaved'),
                });
                updateRows();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrSavingFailed'),
                    serverError: err,
                });
            });
    };

    const renameCols = (values: FieldValues) => {
        const postObj: { items: { dtc_id: string; dtc_name: string }[] } = { items: [] };
        Object.keys(values).forEach((key) => {
            postObj.items.push({
                dtc_id: key,
                dtc_name: values[key],
            });
        });

        ApiRequest.post(
            `/dyn-table/${dynTableId || newDynTableId}/rename-col`,
            JSON.stringify(postObj),
        )
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrSaved'),
                });
                loadDynTableColumns();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrSavingFailed'),
                    serverError: err,
                });
            });
    };

    const deleteCol = (values: FieldValues) => {
        ApiRequest.delete(`/dyn-table/${dynTableId || newDynTableId}/cols/${values.column}`)
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrDeleted'),
                });
                loadDynTableColumns();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrDeleteFailed'),
                    serverError: err,
                });
            });
    };

    const addCol = (values: FieldValues) => {
        const postObj = { dtc_name: values.column };

        ApiRequest.post(
            `/dyn-table/${dynTableId || newDynTableId}/add-col`,
            JSON.stringify(postObj),
        )
            .then(() => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: t('alrSaved'),
                });
                loadDynTableColumns();
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrSavingFailed'),
                    serverError: err,
                });
            });
    };

    const loadRowOptions = () => {
        ApiRequest.get(
            `/dyn-table/${dynTableId || newDynTableId}/values?order=dtv_order_index&limit=${(window as any).config.restLimit}`,
        )
            .then((payload: any) => {
                const rowOpts: { value: number; title: string }[] = [];

                payload.items.forEach((item: Record<string, any>) => {
                    rowOpts.push({
                        value: item.dtv_order_index,
                        title: item.dtv_index,
                    });
                });

                setRowOptions(rowOpts);
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrDynTableDataLoadFailed'),
                    serverError: err,
                });
            });
    };

    const guessCsvDelimiter = (csv: string) => {
        let delimiter = '';
        try {
            // try to find delimiter.
            const delimiters = [',', ';', '\t', ':', '\\|'];
            let delimiterCount = 0;

            for (let i = 0; i < delimiters.length; i += 1) {
                const currDelimiter = delimiters[i];
                const count = (csv.match(new RegExp(currDelimiter, 'g')) || []).length;
                if (count > delimiterCount) {
                    delimiterCount = count;
                    delimiter = currDelimiter;
                }
            }
        } catch (err) {
            console.error(err);
        }
        return delimiter;
    };

    const saveDynTableName = (name: string) => {
        const obj = {
            dt_name: name,
        };

        ApiRequest.post(`/dyn-table/${dynTableId}/edit`, JSON.stringify(obj))
            .then(() => {
                setDynTableDetailName(name);
            })
            .catch((err: any) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: t('alrSaveFailed'),
                    serverError: err,
                });
            });
    };

    const saveCsv = (values: FieldValues, callback?: any, id?: number) => {
        if (id || dynTableId || newDynTableId) {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: t('alrCsvSaving'),
            });

            const csvObj = {
                data: values.fileData,
                fileName: values.fileName,
                delimiter: values.delimiter,
                append: false,
                firstLineColNames: values.firstLineColNames,
            };

            ApiRequest.post(`/dyn-table/${id || dynTableId || newDynTableId}/importCsv`, csvObj)
                .then(() => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: t('alrCsvSaved'),
                    });

                    if (!dynTableId && id) {
                        callback(id);
                    } else {
                        callback();
                    }
                })
                .catch((err: any) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: t('alrCsvSaveFailed'),
                        serverError: err,
                    });
                });
        }
    };

    const saveDynTable = async (values: FieldValues, callback: any) => {
        const saveUrl =
            dynTableId || newDynTableId
                ? `/dyn-table/${dynTableId || newDynTableId}/edit`
                : '/dyn-table';

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: t('alrSaving'),
        });

        const obj = {
            dt_name: dynTableId ? dynTableDetailName : values.dtName,
            dt_public: values.dtPublic,
            dt_description: values.dtDescription,
            dt_note: values.dtNote,
            dt_access_role_id: values.dtAccessRoleId,
            dt_access_header_id: values.dtAccessHeaderId,
        };

        return ApiRequest.post(saveUrl, JSON.stringify(obj))
            .then((payload: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: t('alrSaved'),
                });

                if (values.fileData) {
                    saveCsv(values, callback, payload.id);
                } else if (!dynTableId && payload.id) {
                    callback(payload.id);
                } else {
                    callback();
                }

                return true;
            })
            .catch((err: any) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: t('alrSaveFailed'),
                    serverError: err,
                });

                return false;
            });
    };

    return {
        defaultValues,
        uploadedFromFile,
        loading,
        headersOptions,
        rowOptions,
        loadHeaders,
        loadDynTable,
        loadDynTableColumns,
        deleteRow,
        editRow,
        renameCols,
        deleteCol,
        addCol,
        addRow,
        moveRow,
        loadRowOptions,
        guessCsvDelimiter,
        saveDynTableName,
        saveDynTable,
    };
};

export default useDynTableDetail;
