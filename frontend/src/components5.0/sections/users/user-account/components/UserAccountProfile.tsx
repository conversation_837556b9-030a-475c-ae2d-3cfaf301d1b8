import React, { useEffect } from 'react';
import moment from 'moment-timezone';
// @mui
import { Skeleton, Stack, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
// hooks
import { useTranslation } from 'react-i18next';
import { useResponsive } from '../../../../hooks';
import useUserAccount from '../use-user-account';
// components
import SectionsCard from '../../../../SectionsCard';
import UserAvatar from '../../common/UserAvatar';
import TextReadOnly from '../../../../form/textReadOnly/TextReadOnly';
import TextField from '../../../../form/textField/TextField';
// types
import { TSelectValueObj } from '../types';
// require
const LoggedUserStore = require('../../../../../flux/loggedUser.store');

const UserAccountProfile = () => {
    const mdUp = useResponsive('up', 'md');
    const { t } = useTranslation();

    const {
        settingsState, loggedUserState, findSett, loadUserManagedOrgs, userManagedOrg,
    } =
        useUserAccount();

    useEffect(() => {
        if (LoggedUserStore.isAdmin() && loggedUserState.id) {
            loadUserManagedOrgs();
        }
    }, [loggedUserState.id]);

    return (
        <SectionsCard title={t('userInfo') as string} icon="account" mb={4}>
            <Grid container direction="row">
                <Grid
                    size={{ xs: 12, md: 4 }}
                    sx={{
                        borderRight: mdUp ? '1px solid' : 'none',
                        borderBottom: mdUp ? 'none' : '1px solid',
                        borderColor: (theme) => theme.palette.divider,
                        padding: mdUp ? '0 0.75rem 0 0' : '0 0 0.75rem 0',
                    }}
                >
                    <UserAvatar
                        helperText={
                            <Typography
                                variant="caption"
                                sx={{
                                    mt: 3,
                                    mx: 'auto',
                                    display: 'block',
                                    textAlign: 'center',
                                    color: 'text.disabled',
                                }}
                            >
                                {t('profilePhotoCaption')}
                            </Typography>
                        }
                    />
                </Grid>
                <Grid
                    size={{ xs: 12, md: 4 }}
                    sx={{
                        borderRight: mdUp ? '0.0625rem solid' : 'none',
                        borderBottom: mdUp ? 'none' : '0.0625rem solid',
                        borderColor: (theme) => theme.palette.divider,
                        padding: mdUp ? '0 0.75rem' : '0.75rem 0',
                    }}
                >
                    {settingsState.settingsLoaded && !LoggedUserStore.isAdmin() && (
                        <Stack gap={1}>
                            <TextReadOnly label={t('userName')} value={settingsState?.userName} />
                            <TextReadOnly
                                label={t('firstLastName')}
                                value={`${findSett('USER_FIRST_NAME')} ${findSett('USER_LAST_NAME')}`}
                            />
                            {findSett('USER_EMAIL') && (
                                <TextReadOnly label={t('email')} value={findSett('USER_EMAIL')} />
                            )}
                            {findSett('CONTACT_PHONE') && (
                                <TextReadOnly
                                    label={t('mobilePhone')}
                                    value={findSett('CONTACT_PHONE')}
                                />
                            )}
                            {findSett('CONTACT_SKYPE') && (
                                <TextReadOnly
                                    label={t('skype')}
                                    value={findSett('CONTACT_SKYPE')}
                                />
                            )}
                            {findSett('CONTACT_INFO') && (
                                <TextReadOnly
                                    label={t('notesOnContacts')}
                                    value={findSett('CONTACT_INFO')}
                                />
                            )}
                        </Stack>
                    )}
                    {settingsState.settingsLoaded && LoggedUserStore.isAdmin() && (
                        <Stack gap={1}>
                            <TextReadOnly label={t('userName')} value={settingsState?.userName} />
                            <TextField
                                name="USER_FIRST_NAME"
                                label={t('firstName')}
                                value={findSett('USER_FIRST_NAME') || ''}
                                validations={{ required: t('isRequired') }}
                            />
                            <TextField
                                name="USER_LAST_NAME"
                                label={t('lastName')}
                                value={findSett('USER_LAST_NAME') || ''}
                                validations={{ required: t('isRequired') }}
                            />
                            <TextField
                                name="USER_EMAIL"
                                label={t('email')}
                                value={findSett('USER_EMAIL') || ''}
                            />
                            <TextField
                                name="CONTACT_PHONE"
                                label={t('mobilePhone')}
                                value={findSett('CONTACT_PHONE') || ''}
                            />
                            <TextField
                                name="CONTACT_SKYPE"
                                label={t('skype')}
                                value={findSett('CONTACT_SKYPE') || ''}
                            />
                            <TextField
                                name="CONTACT_INFO"
                                label={t('notesOnContacts')}
                                value={findSett('CONTACT_INFO') || ''}
                                multiline
                                rows={3}
                            />
                        </Stack>
                    )}
                    {!settingsState.settingsLoaded &&
                        [1, 2, 3, 4, 5, 6].map((num: number) => (
                            <Skeleton
                                key={num}
                                variant="text"
                                height="3.125rem"
                                width="100%"
                                sx={{
                                    mb: 1,
                                }}
                            />
                        ))}
                </Grid>
                <Grid
                    size={{ sm: 12, md: 4 }}
                    sx={{
                        padding: mdUp ? '0 0 0 0.75rem' : '0.75rem 0 0 0',
                    }}
                >
                    {settingsState.settingsLoaded ? (
                        <Stack gap={1}>
                            <TextReadOnly
                                label={t('userOrgStruct')}
                                value={loggedUserState?.organization?.title}
                            />
                            {LoggedUserStore.isAdmin() && (
                                <TextReadOnly
                                    label={t('isManagerOrgUnit')}
                                    value={userManagedOrg
                                        .map((val: TSelectValueObj) => val.title)
                                        .join('\n')}
                                    innerSx={{
                                        maxHeight: '15rem',
                                        overflowY: 'auto',
                                        overflowX: 'hidden',
                                    }}
                                />
                            )}
                            <TextReadOnly label={t('timezone')} value={moment.tz.guess()} />
                            {LoggedUserStore.isAdmin() ? (
                                <TextField
                                    name="USER_DISPLAY_NAME"
                                    label={t('displayName')}
                                    value={findSett('USER_DISPLAY_NAME') || ''}
                                />
                            ) : (
                                <TextReadOnly
                                    label={t('displayName')}
                                    value={findSett('USER_DISPLAY_NAME') || ''}
                                />
                            )}
                        </Stack>
                    ) : (
                        [1, 2, 3].map((num: number) => (
                            <Skeleton
                                key={num}
                                variant="text"
                                height="3.125rem"
                                width="100%"
                                sx={{
                                    mb: 1,
                                }}
                            />
                        ))
                    )}
                </Grid>
            </Grid>
        </SectionsCard>
    );
};

export default UserAccountProfile;
