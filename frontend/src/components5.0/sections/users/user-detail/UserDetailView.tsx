import React, {
    ReactElement, useEffect, useRef, useState,
} from 'react';
// @mui
import { Stack, Tooltip, Typography } from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { flushSync } from 'react-dom';
import { useBoolean, useResponsive } from '../../../hooks';
import { useRouter } from '../../../routes/hooks';
import useUserDetail from './use-user-detail';
// components
import MainScrollHeight from '../../common/MainScrollHeight';
import AppBarSticky from '../../common/AppBarSticky';
import MyIcon, { Icon } from '../../../MyIcon';
import FormProvider from '../../../form/FormProvider';
import ModalChangePassword from '../common/ModalChangePassword';
import UserDetailCompetences from './components/UserDetailCompetences';
import UserDetailRoles from './components/UserDetailRoles';
import UserDetailProfile from './components/UserDetailProfile';
import UserDetailPreferences from './components/UserDetailPreferences';
import ResponsiveButton from '../../../ResponsiveButton';
import UserMobileApp from '../common/mobile-app/UserMobileApp';
import withNavigationConfirmation from '../../../hoc/withNavigationConfirmation';
// zustand
import { useStore } from '../../../zustand/boundStore';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';

type UserDetailViewProps = {
    setConfirmFunction: any;
    setNavigationBlocked: any;
    setIsFormValid: any;
};

const UserDetailView = ({
    setConfirmFunction,
    setNavigationBlocked,
    setIsFormValid,
}: UserDetailViewProps) => {
    const [activeSection, setActiveSection] = useState<string | null>('profile');
    const [isReadOnly, setIsReadOnly] = useState<boolean>(false);
    const { t } = useTranslation();
    const router = useRouter();
    const modalChangePassword = useBoolean();
    const profileRef = useRef<HTMLDivElement>(null);
    const preferencesRef = useRef<HTMLDivElement>(null);
    const mobileAppRef = useRef<HTMLDivElement>(null);
    const rolesRef = useRef<HTMLDivElement>(null);
    const smUp = useResponsive('up', 'sm');

    const methods = useForm<Record<string, any>>();
    const {
        handleSubmit,
        formState: { isDirty, isValid, isSubmitting },
    } = methods;

    const userDetailIsBlocking = useStore((state) => state.userDetailIsBlocking);
    const setUserDetailIsBlocking = useStore((state) => state.setUserDetailIsBlocking);
    const setUserToCopyRoles = useStore((state) => state.setUserToCopyRoles);
    const setIncludeCompetences = useStore((state) => state.setIncludeCompetences);

    const {
        userData,
        userParams,
        userRoles,
        userManagedOrg,
        dataLoaded,
        saveUser,
        handleChangePassword,
        loadUserData,
        loadUserParameters,
    } = useUserDetail();

    const justSave = async (data: any) => {
        await saveUser(data, false, () => {
            flushSync(() => setNavigationBlocked(false));
            loadUserData();
            loadUserParameters();
            setUserToCopyRoles({});
            setIncludeCompetences(false);
        });
    };

    const saveAndClose = async (data: any) => {
        await saveUser(data, false, () => {
            flushSync(() => {
                setNavigationBlocked(false);
            });
            router.push('/users');
        });
    };

    useEffect(() => {
        loadUserData();
        loadUserParameters();

        const handleScroll = () => {
            const sections = [profileRef, preferencesRef, mobileAppRef, rolesRef];
            const currentSection = sections.find((section, i) => {
                const rect = section.current?.getBoundingClientRect();
                if (i === 0) return rect && rect.bottom >= 140;
                return rect && rect.top <= 172 && rect.bottom >= 140;
            });
            setActiveSection(currentSection?.current?.id || null);
        };

        window.addEventListener('scroll', handleScroll);

        return () => {
            window.removeEventListener('scroll', handleScroll);
            setUserDetailIsBlocking(false);
        };
    }, []);

    // Only SuperAdmin can edit system users
    useEffect(() => {
        if (userData.user_system && !LoggedUserStore.isSuperAdmin()) {
            setIsReadOnly(true);
        }
    }, [userData]);

    useEffect(() => {
        let docTitle = t('user');
        if (userData?.user_display_name) {
            docTitle += ` - ${userData.user_display_name}`;
        }
        document.title = docTitle;
    }, [userData]);

    useEffect(() => {
        const isBlocking = userDetailIsBlocking || isDirty;
        setNavigationBlocked(isBlocking);
        setConfirmFunction(() => handleSubmit(justSave)());
        setIsFormValid(isValid);
    }, [userDetailIsBlocking, isDirty, isValid]);

    const executeScroll = ({ current }: React.RefObject<HTMLDivElement>) => {
        if (current) {
            // Adjust for sticky header
            const topPosition = current.getBoundingClientRect().top + window.scrollY - 60;
            window.scrollTo({
                top: topPosition,
                behavior: 'smooth',
            });
        }
    };

    const sections: (
        | {
              title: string;
              ref: React.RefObject<HTMLDivElement>;
              icon: Icon;
          }
        | undefined
    )[] = [
        {
            title: t('userInfo'),
            ref: profileRef,
            icon: 'account',
        },
        {
            title: t('userPreferences'),
            ref: preferencesRef,
            icon: 'email',
        },
        LoggedUserStore.getConfig('mobileApp.isActive')
            ? {
                title: t('mobileApp'),
                ref: mobileAppRef,
                icon: 'deviceregister',
            }
            : undefined,
        {
            title: t('roles'),
            ref: rolesRef,
            icon: 'roles',
        },
    ];

    const secondHeaderBtns: ReactElement[] = [];
    sections.forEach((section, index) => {
        if (section) {
            const isActive = activeSection === section.ref?.current?.id;
            secondHeaderBtns.push(
                <Tooltip key={index} title={smUp ? '' : section.title} placement="bottom" arrow>
                    <div>
                        <ResponsiveButton
                            showLabelIconButton={isActive}
                            icon={section.icon}
                            color="secondary"
                            label={section.title}
                            ariaLabel={section.title as string}
                            buttonSize="small"
                            iconButtonSize="large"
                            variant="text"
                            onClick={() => executeScroll(section.ref)}
                            sx={{
                                ...(isActive && {
                                    backgroundColor: (theme) => theme.palette.primary.light,
                                }),
                            }}
                        />
                    </div>
                </Tooltip>,
            );
        }
    });

    return (
        <>
            <FormProvider methods={methods}>
                <AppBarSticky>
                    <Stack direction="row" flexGrow={1} justifyContent="space-between">
                        <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="flex-start"
                            spacing={{ xs: 1, sm: 1.5 }}
                        >
                            <MyIcon
                                icon="users-filled"
                                sx={{
                                    color: (theme) => theme.palette.primary.main,
                                    pl: 1,
                                }}
                            />
                            {smUp && (
                                <Typography variant="h1" noWrap>
                                    {t('users')}
                                </Typography>
                            )}

                            {userData?.user_display_name && (
                                <>
                                    <Typography variant="h1" color="text.secondary" noWrap>
                                        /
                                    </Typography>
                                    <Typography
                                        variant="h1"
                                        color="text.secondary"
                                        noWrap
                                        maxWidth="35vw"
                                    >
                                        {userData.user_display_name}
                                    </Typography>
                                </>
                            )}
                        </Stack>

                        <Stack direction="row" alignItems="center" spacing={{ xs: 0.5, sm: 1 }}>
                            <ResponsiveButton
                                icon="close"
                                color="secondary"
                                label={t('close')}
                                ariaLabel={t('close')}
                                buttonSize="small"
                                iconButtonSize="large"
                                variant="text"
                                onClick={router.back}
                            />
                            {!isReadOnly && (
                                <>
                                    <ResponsiveButton
                                        icon="authentication"
                                        color="secondary"
                                        label={t('passwordChange')}
                                        ariaLabel={t('passwordChange')}
                                        buttonSize="small"
                                        iconButtonSize="large"
                                        variant="text"
                                        onClick={modalChangePassword.onTrue}
                                    />
                                    <ResponsiveButton
                                        icon="save"
                                        color="secondary"
                                        label={t('save')}
                                        ariaLabel={t('justSave')}
                                        buttonSize="small"
                                        iconButtonSize="large"
                                        variant="text"
                                        disabled={isSubmitting || !dataLoaded || !isValid}
                                        onClick={handleSubmit(justSave)}
                                    />
                                    <ResponsiveButton
                                        icon="save"
                                        color="primary"
                                        label={t('saveAndClose')}
                                        ariaLabel={t('save')}
                                        buttonSize="small"
                                        iconButtonSize="large"
                                        variant="contained"
                                        disabled={isSubmitting || !dataLoaded || !isValid}
                                        onClick={handleSubmit(saveAndClose)}
                                    />
                                </>
                            )}
                        </Stack>
                    </Stack>
                </AppBarSticky>
                <AppBarSticky>
                    <Stack
                        direction="row"
                        width={1}
                        justifyContent="flex-end"
                        spacing={{ xs: 0.5, sm: 1 }}
                    >
                        {secondHeaderBtns}
                    </Stack>
                </AppBarSticky>
                <MainScrollHeight headersCount={0}>
                    <div ref={profileRef} id="profile">
                        <UserDetailProfile
                            userData={userData}
                            userParams={userParams}
                            userManagedOrg={userManagedOrg}
                            dataLoaded={dataLoaded}
                            isReadOnly={isReadOnly}
                        />
                    </div>
                    <div ref={preferencesRef} id="preferences">
                        <UserDetailPreferences
                            userParams={userParams}
                            dataLoaded={dataLoaded}
                            isReadOnly={isReadOnly}
                        />
                    </div>
                    {LoggedUserStore.getConfig('mobileApp.isActive') && (
                        <div ref={mobileAppRef} id="mobileApp">
                            <UserMobileApp isReadOnly={isReadOnly} />
                        </div>
                    )}
                    <div ref={rolesRef} id="roles">
                        <UserDetailRoles userRoles={userRoles} isReadOnly={isReadOnly} />
                        <UserDetailCompetences />
                    </div>
                </MainScrollHeight>
            </FormProvider>
            {modalChangePassword.value && (
                <ModalChangePassword
                    isOpen={modalChangePassword.value}
                    onClose={modalChangePassword.onFalse}
                    onSubmit={handleChangePassword}
                />
            )}
        </>
    );
};

export default withNavigationConfirmation(UserDetailView);
