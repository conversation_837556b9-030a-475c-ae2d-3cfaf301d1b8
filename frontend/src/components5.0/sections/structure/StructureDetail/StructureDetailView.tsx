import React, { useEffect } from 'react';
import { flushSync } from 'react-dom';
import { useForm } from 'react-hook-form';
// @mui
import { Skeleton } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { TabContext, TabPanel } from '@mui/lab';
// hooks
import { useTranslation } from 'react-i18next';
import { useStructureDetail } from './use-structure-detail';
import { useParams, useRouter } from '../../../routes/hooks';
// components
import StructureDetailLayout from './components/StructureDetailLayout';
import StructureDetailSettings from './components/StructureDetailSettings';
import StructureDetailSetLogo from './components/StructureDetailSetLogo';
import FormProvider from '../../../form/FormProvider';
import SectionsCard from '../../../SectionsCard';
import withNavigationConfirmation from '../../../hoc/withNavigationConfirmation';
// zustand
import { useStore } from '../../../zustand/boundStore';
// types
import { StructureViewProps } from '../types';

const StructureDetailView = ({
    setConfirmFunction,
    setNavigationBlocked,
    setIsFormValid,
}: StructureViewProps) => {
    const { orgUnitId }: { orgUnitId?: number } = useParams();
    const { t } = useTranslation();

    const router = useRouter();

    const { orgUnitData, orgLoading, getOrgUnitData, getLogos, saveOrgUnitData } =
        useStructureDetail();

    const methods = useForm();

    const {
        setValue,
        watch,
        handleSubmit,
        reset,
        formState: { isDirty, isValid },
    } = methods;

    const structureDetailIsBlocking = useStore((state) => state.structureDetailIsBlocking);
    const structureTabId = useStore((state) => state.structureTabId);
    const setStructureTabId = useStore((state) => state.setStructureTabId);
    const setStructureDetailIsBlocking = useStore((state) => state.setStructureDetailIsBlocking);
    const setOrgUsers = useStore((state) => state.setOrgUsers);
    const setStructureCropLogoNull = useStore((state) => state.setStructureCropLogoNull);

    const justSave = async (data: any) => {
        await saveOrgUnitData(data, orgUnitId, () => {
            flushSync(() => setNavigationBlocked(false));
        });
    };

    const saveAndClose = async (data: any) => {
        await saveOrgUnitData(data, orgUnitId, () => {
            flushSync(() => {
                setNavigationBlocked(false);
            });
            router.push('/structure');
        });
    };

    useEffect(() => {
        const isBlocking = structureDetailIsBlocking || isDirty;
        setNavigationBlocked(isBlocking);
        setConfirmFunction(() => handleSubmit(justSave)());
        setIsFormValid(isValid);
    }, [structureDetailIsBlocking, isDirty, isValid]);

    useEffect(() => {
        const fetchData = () => {
            getLogos();
            getOrgUnitData(orgUnitId, reset);
        };
        fetchData();

        return () => {
            setStructureTabId('1');
            setStructureDetailIsBlocking(false);
            setOrgUsers([]);
            setStructureCropLogoNull();
        };
    }, []);

    useEffect(() => {
        let docTitle = t('orgStructure');
        if (orgUnitData?.orgstr_name) {
            docTitle += ` - ${orgUnitData?.orgstr_name}`;
        }
        document.title = docTitle;
    }, [orgUnitData?.orgstr_name]);

    return (
        <FormProvider methods={methods}>
            <StructureDetailLayout
                justSave={handleSubmit(justSave)}
                saveAndClose={handleSubmit(saveAndClose)}
                orgName={orgUnitData?.orgstr_name}
            >
                {orgLoading ? (
                    <SectionsCard>
                        <Grid container spacing={1}>
                            {[...Array(8)].map((_, index) => (
                                <Grid size={{ xs: 12, md: 6 }} key={index}>
                                    <Skeleton variant="text" width="100%" height={70} />
                                </Grid>
                            ))}
                        </Grid>
                    </SectionsCard>
                ) : (
                    <TabContext value={String(structureTabId)}>
                        <TabPanel value="1" keepMounted>
                            <StructureDetailSettings orgUnit={orgUnitData} />
                        </TabPanel>
                        <TabPanel value="2">
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <StructureDetailSetLogo
                                        size="small"
                                        theme="light"
                                        watch={watch}
                                        setValue={setValue}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <StructureDetailSetLogo
                                        size="large"
                                        theme="light"
                                        watch={watch}
                                        setValue={setValue}
                                    />
                                </Grid>
                            </Grid>
                        </TabPanel>
                        <TabPanel value="3">
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <StructureDetailSetLogo
                                        size="small"
                                        theme="dark"
                                        watch={watch}
                                        setValue={setValue}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <StructureDetailSetLogo
                                        size="large"
                                        theme="dark"
                                        watch={watch}
                                        setValue={setValue}
                                    />
                                </Grid>
                            </Grid>
                        </TabPanel>
                    </TabContext>
                )}
            </StructureDetailLayout>
        </FormProvider>
    );
};

export default withNavigationConfirmation(StructureDetailView);
