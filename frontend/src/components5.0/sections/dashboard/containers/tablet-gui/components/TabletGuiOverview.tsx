import React from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { GridSortItem } from '@mui/x-data-grid-pro';
// components
import DataGrid from '../../../../../data-grid/DataGrid';

const TableGuiOverview = ({ setCaseId }: { setCaseId: (id: number) => void }) => {
    const { t } = useTranslation();
    const sort: GridSortItem = { field: 'id', sort: 'desc' };

    const COLUMNS = [
        {
            field: 'id',
            headerName: 'ID',
            size: 100,
        },
        {
            field: 'iproc_name',
            headerName: t('procName'),
            type: 'string',
        },
    ];

    const handleRowClick = (event: any, table: any, row: any) => {
        setCaseId(row.id);
    };

    return (
        <DataGrid
            id="table-gui-case-dataGrid"
            columns={COLUMNS}
            apiUrl="/processes/mine"
            generateRowId
            alwaysSort={sort}
            onRowClick={handleRowClick}
            hideFooter
        />
    );
};

export default TableGuiOverview;
