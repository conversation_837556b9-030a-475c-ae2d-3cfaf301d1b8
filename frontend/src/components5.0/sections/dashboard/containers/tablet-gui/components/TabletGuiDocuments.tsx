import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
// @mui
import { Box } from '@mui/material';
import { GridSortItem, GridSortModel } from '@mui/x-data-grid-pro';
// components
import DataGrid from '../../../../../data-grid/DataGrid';
import SelectLimitedSearch from '../../../../../form/select/SelectLimitedSearch';
import FormProvider from '../../../../../form/FormProvider';
import ModalDocumentDetail from '../../../../../Modals/ModalDocumentDetail';
// hooks
import useDocuments from '../../../../documents/use-documents';
import { useBoolean } from '../../../../../hooks';
// flux
import LoggedUserStore from '../../../../../../flux/loggedUser.store';

const defaultSortModel: GridSortModel = [
    {
        field: 'itask_actual_date_start',
        sort: 'desc',
    },
];

const TabletGuiDocuments = ({ caseId }: { caseId?: number }) => {
    const { t } = useTranslation();
    const documentDetail = useBoolean(false);
    const sort: GridSortItem = { field: 'id', sort: 'asc' };
    const { fetchFoldersOptions, foldersOptions, folderTreeLoading } = useDocuments();

    const { openDocumentDetail, document } = useDocuments(
        'case',
        /* true */ undefined,
        undefined,
        undefined,
        undefined,
    );

    useEffect(() => {
        fetchFoldersOptions();
    }, []);

    const COLUMNS = [
        // { field: 'id', columnVisibility: false },
        {
            field: 'name',
            headerName: t('attachments'),
            type: 'string',
        },
    ];

    const methods = useForm();
    const { watch } = methods;

    const watchFolders = watch('folder');

    const handleRowClick = (row: any) => {
        openDocumentDetail(documentDetail.onTrue, row.id, row.name);
    };

    return (
        <Box height={1}>
            <Box height="55px">
                <FormProvider methods={methods}>
                    <SelectLimitedSearch
                        key="folder"
                        name="folder"
                        options={foldersOptions}
                        placeholder="/"
                    />
                </FormProvider>
            </Box>
            <Box height="calc(100% - 55px)">
                <DataGrid
                    id="table-gui-documents-dataGrid"
                    columns={COLUMNS}
                    apiUrl={caseId ? `/processes/${caseId}/attachments` : ''}
                    generateRowId
                    alwaysSort={sort}
                    defaultFilter={watchFolders ? `dtf-4<eq>"${watchFolders.value}"` : ''}
                    headerFilters={false}
                    onRowClick={handleRowClick}
                    hideFooter
                />
            </Box>
            {documentDetail.value && (
                <ModalDocumentDetail
                    isOpen={documentDetail.value}
                    onClose={documentDetail.onFalse}
                    // taskInfo={{
                    //     procName: caseTitle,
                    //     iprocCaseStatus: caseStatus,
                    // }}
                    file={document}
                    entityName="case"
                    // archived={archived}
                    // isEditable={!archived}
                />
            )}
        </Box>
    );
};

export default TabletGuiDocuments;
