import React from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { GridSortItem, GridSortModel } from '@mui/x-data-grid-pro';
// components
import DataGrid from '../../../../../data-grid/DataGrid';
// utils
import { checkLangMutationTemplInst } from '../../../../../../common/utils';
// hooks
import useTasks from '../../../../tasks/use-tasks';
// flux
import LoggedUserStore from '../../../../../../flux/loggedUser.store';

const defaultSortModel: GridSortModel = [
    {
        field: 'itask_actual_date_start',
        sort: 'desc',
    },
    {
        field: 'id',
        sort: 'asc',
    },
];

const TabletGiuTasks = ({ caseId }: { caseId?: number }) => {
    const { t } = useTranslation();
    const sort: GridSortItem = { field: 'itask_actual_date_start', sort: 'desc' };
    const { handleSolve } = useTasks();
    const COLUMNS = [
        // {
        //     field: 'id',
        //     columnVisibility: false,
        //     width: 0,
        // },
        {
            field: 'itask_name',
            headerName: t('tskName'),
            type: 'string',
            accessorFn: (originalRow: any) => {
                return checkLangMutationTemplInst(originalRow, 'task_name');
            },
        },
    ];

    const handleRowClick = (event: any, table: any, row: any) => {
        handleSolve(row);
    };

    return (
        <DataGrid
            id="table-gui-tasks-dataGrid"
            columns={COLUMNS}
            apiUrl={caseId ? `/processes/${caseId}/tasks` : ''}
            generateRowId
            alwaysSort={sort}
            onRowClick={handleRowClick}
            headerFilters={false}
            hideFooter
        />
    );
};

export default TabletGiuTasks;
