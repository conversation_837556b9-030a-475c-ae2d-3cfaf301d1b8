import PropTypes from 'prop-types';
import { defineScript } from '@jrblatt/light-script';
import React from 'react';
import ReactDOM from 'react-dom';
import createReactClass from 'create-react-class';
import browserHistory from '../../../../../../common/history';
import { sanitizeVariable } from '../../../../../../common/utils';
import ApiRequest from '../../../../../../api/apiRequest';
import casePrintRequire from '../../../../../../pages/case/casePrintRequire';
import Factory from '../../../../../../flux/factory';
import CasePrintActions from '../../../../../../flux/casePrint.actions';
import CasePrintStore from '../../../../../../flux/casePrint.store';
import CasePrintSingleActions from '../../../../../../flux/casePrintSingle.actions';
import CaseApi from '../../../../../../flux/case.api';
import TaskApi from '../../../../../../flux/task.api';

const Api = {
    case: CaseApi,
    task: TaskApi,
    request: {
        get: (url) => {
            return ApiRequest.get(url)
                .then((payload) => {
                    if (payload && url.indexOf('variable')) {
                        if (payload.items) {
                            payload.items.forEach((item) => {
                                const prefix = item.hasOwnProperty('ivar_type') ? 'ivar' : 'tvar';
                                sanitizeVariable(item, prefix);
                            });
                        } else {
                            // single variable
                            const prefix = payload.hasOwnProperty('ivar_type') ? 'ivar' : 'tvar';
                            sanitizeVariable(payload, prefix);
                        }
                    }
                    return payload;
                })
                .catch((e) => {
                    console.log(e);
                });
        },
        post: (url, data) => {
            return ApiRequest.post(url, data);
        },
        getFile: (url, ids, callback) => {
            return ApiRequest.getFile(url, ids, callback);
        },
        postFile: (url, formData, file, iprocId, lastRevisionId) => {
            return ApiRequest.postFile(url, formData, file, iprocId, lastRevisionId);
        },
        delete: (url, data) => {
            return ApiRequest.delete(url, data);
        },
    },
};

if (window) {
    window.Api = Api;
    window.Require = casePrintRequire;
    window.printLoaded = () => {};
}

const CaseOverviewTab = createReactClass({
    propTypes: {
        caseOverview: PropTypes.string,
        caseOverviewJs: PropTypes.string,
        printCaseId: PropTypes.string,
        caseId: PropTypes.string.isRequired,
        printId: PropTypes.number.isRequired,
        printReact: PropTypes.bool.isRequired,
        caseOverviewCss: PropTypes.string.isRequired,
    },

    getInitialState: function () {
        return {
            scriptLoaded: false,
        };
    },

    componentDidMount: function () {
        defineScript('/assets/libs/Chart.min.js', {
            wrapper: 'body',
            onSuccess: (e) => {
                this.setState({ scriptLoaded: true });
            },
        });

        const { caseId, printId } = this.props;

        if (this.props.printReact) {
            const casePrintObj = Factory.registerFlux(
                CasePrintStore,
                CasePrintActions,
                `casePrint-${caseId}-${printId}`,
            );
            const casePrintActions = casePrintObj.action;

            casePrintActions.setIprocId(this.props.caseId);
            CasePrintSingleActions.setIprocId(this.props.caseId);

            window.Require.stores.CasePrintStore = casePrintObj.store;
            window.Require.actions.CasePrintAction = casePrintActions;
        }

        // function is called from case overview
        window.showProcessInfo = (caseId) => {
            browserHistory.push({
                pathname: `/cases/case/${caseId}`,
                state: { closePrevPath: this.props.closePrevPath },
            });
        };

        this.appendCss();
    },

    componentDidUpdate: function (prevProps, prevState) {
        const { caseOverviewJs } = this.props;
        const jsExisty =
            typeof caseOverviewJs !== 'undefined' &&
            caseOverviewJs !== null &&
            caseOverviewJs != '';
        if (!prevState.scriptLoaded && this.state.scriptLoaded && jsExisty) {
            // in eval can be used Chart, pdfjs and other context variables
            this.appendScript();
        }

        // Switching between cases - rerun eval
        if (
            this.state.scriptLoaded &&
            prevProps.printCaseId != this.props.printCaseId &&
            jsExisty
        ) {
            if (this.script) document.head.removeChild(this.script);
            this.appendScript();
            this.appendCss();
        }

        // re-append script after manual event
        // if (
        //     this.props.location.state &&
        //     this.props.location.state.refreshPage === true &&
        //     prevProps.caseOverview !== this.props.caseOverview &&
        //     this.props.caseOverview !== ''
        // ) {
        //     if (this.script) document.head.removeChild(this.script);
        //     this.appendScript();
        // }
    },

    componentWillUnmount: function () {
        if (this.script) document.head.removeChild(this.script);
        if (this.coCss) document.head.removeChild(this.coCss);
        if (this.props.printReact) {
            ReactDOM.unmountComponentAtNode(document.getElementById('case-print-emb'));
        }
    },

    appendScript: function () {
        this.script = document.createElement('script');
        document.head.appendChild(this.script);
        this.script.type = 'text/javascript';

        if (this.props.printReact) {
            this.script.innerHTML = this.props.caseOverviewJs;
        } else {
            this.script.innerHTML = this.props.caseOverviewJs; // todo t3f-1128
        }
    },

    appendCss: function () {
        if (this.coCss) document.head.removeChild(this.coCss);

        if (this.props.caseOverviewCss) {
            this.coCss = document.createElement('style');
            this.coCss.id = 'case-print-css';
            this.coCss.type = 'text/css';
            this.coCss.innerHTML = this.props.caseOverviewCss;
            document.head.appendChild(this.coCss);
        }
    },

    render: function () {
        return (
            <>
                {this.props.printReact && (
                    <div
                        id="case-print-emb"
                        className="case-overview default-theme"
                        style={{ padding: 0 }}
                    />
                )}
                <div
                    className="case-overview medium-12 columns"
                    style={{ padding: 0 }}
                    dangerouslySetInnerHTML={{
                        __html: this.props.caseOverview,
                    }}
                />
            </>
        );
    },
});

export default CaseOverviewTab;
