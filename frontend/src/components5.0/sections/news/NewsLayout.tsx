import React from 'react';
// @mui
import { Stack, Typography } from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { useResponsive } from '../../hooks';
// components
import MyIcon from '../../MyIcon';
import AppBarSticky from '../common/AppBarSticky';
import MainStrictHeight from '../common/MainStrictHeight';
import SectionsCard from '../../SectionsCard';
import MainScrollHeight from '../common/MainScrollHeight';

type NewsLayoutProps = {
    children: React.ReactNode;
};

const NewsLayout = ({ children }: NewsLayoutProps) => {
    const { t } = useTranslation();
    const smUp = useResponsive('up', 'sm');

    return (
        <>
            <AppBarSticky>
                <Stack
                    direction="row"
                    flexGrow={1}
                    width={1}
                    justifyContent="space-between"
                    spacing={2}
                >
                    <Stack
                        direction="row"
                        alignItems="center"
                        justifyContent="flex-start"
                        spacing={{ xs: 1, sm: 1.5 }}
                    >
                        <MyIcon
                            icon="news"
                            isActive
                            sx={{
                                pl: 1,
                            }}
                        />
                        <Typography variant="h1" noWrap>
                            {t('news')}
                        </Typography>
                    </Stack>
                </Stack>
            </AppBarSticky>
            {smUp ? (
                <MainStrictHeight headersCount={2}>
                    <SectionsCard sx={{ height: '100%' }}>{children}</SectionsCard>
                </MainStrictHeight>
            ) : (
                <MainScrollHeight>{children}</MainScrollHeight>
            )}
        </>
    );
};

export default NewsLayout;
