import { useState } from 'react';
// mui
import { GridColDef } from '@mui/x-data-grid-pro';
// hooks
import { useTranslation } from 'react-i18next';
import { useBoolean } from '../../../hooks';
// utils
import { checkLangMutation, momentFormatDate } from '../../../../common/utils';
import { getNewsDataGridSpecialFilterOperators } from '../../administration/news-management/newsManagementUtils';
// components
import NewsLayout from '../NewsLayout';
import NewsDataGrid from '../NewsDataGrid';
import ModalNews from '../../../Modals/ModalNews';
// types
import { NewsPostTypeGET } from '../../administration/news-management/types';
// flux
import LoggedUserStore from '../../../../flux/loggedUser.store';

const NewsView = () => {
    const { t } = useTranslation();

    const [selectedPost, setSelectedPost] = useState<NewsPostTypeGET | null>(null);
    const [userLanguage] = useState(LoggedUserStore.getState().userLanguage);
    const newsModal = useBoolean(false);

    const columns: Partial<GridColDef<NewsPostTypeGET> & Record<string, any>>[] = [
        {
            field: 'post_title',
            headerName: t('name'),
            type: 'string',
            accessorFn: (originalRow: any) => checkLangMutation(originalRow, 'post_title'),
        },
        {
            field: 'post_tags',
            headerName: t('tags'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) => (
                originalRow.post_tags.map((tag: any) => checkLangMutation(tag, 'tag_name')).join(', ')
            ),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_tags',
                t,
                userLanguage,
            ),
        },
        {
            field: 'post_publication_date',
            headerName: t('published'),
            type: 'dateTime',
            accessorFn: (originalRow: any) => originalRow.post_publication_date && momentFormatDate(originalRow.post_publication_date, false, true),
        },
        {
            field: 'post_visibility.roles',
            headerName: t('rolesRestriction'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) => (
                originalRow.post_visibility.roles.map((role: any) => role.role_name).join(', ')
            ),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_visibility.roles',
                t,
                userLanguage,
            ),
        },
        {
            field: 'post_visibility.users',
            headerName: t('usersRestriction'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) => (
                originalRow.post_visibility.users.map((user: any) => user.user_display_name).join(', ')
            ),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_visibility.users',
                t,
                userLanguage,
            ),
        },
        {
            field: 'post_visibility.organization_structures',
            headerName: t('orgUnitsRestriction'),
            type: 'string',
            enableSorting: false,
            accessorFn: (originalRow: any) => (
                originalRow.post_visibility.organization_structures.map((orgStr: any) => orgStr.orgstr_name).join(', ')
            ),
            filterOperators: getNewsDataGridSpecialFilterOperators(
                'post_visibility.organization_structures',
                t,
                userLanguage,
            ),
        },
    ];

    return (
        <>
            <NewsLayout>
                <NewsDataGrid
                    id="news"
                    apiUrl="/posts"
                    columns={columns}
                    clickCallbackCard={(row) => {
                        setSelectedPost(row);
                        newsModal.onTrue();
                    }}
                />
            </NewsLayout>
            {newsModal.value && selectedPost && (
                <ModalNews
                    isOpen={newsModal.value}
                    onClose={newsModal.onFalse}
                    posts={[selectedPost]}
                />
            )}
        </>
    );
};

export default NewsView;
