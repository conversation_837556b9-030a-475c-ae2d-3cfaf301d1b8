import React, { useEffect, useState, useRef } from 'react';
import { useFormState } from 'react-hook-form';
import { isEmpty } from 'lodash';
// @mui
import { Stack, useTheme, Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
// hooks
import { useResponsive } from '../../../../hooks';
import { usePopover } from '../../../../custom-popover';
// components
import MyIcon from '../../../../MyIcon';
import Button from '../../../../Button';
import Label from '../../../../label';
import TaskMorePopover from './TaskMorePopover';
import TextMaxLine from '../../../../text-max-line/text-max-line';
import IconButton from '../../../../IconButton';
import CopyButton from '../../../../CopyButton';
// hoc
import saveBeforeLeave from '../../../../hoc/withNavigationConfirmation';
// types
import { ITaskFirsHeaderProps } from '../types';
// zustand
import { useStore } from '../../../../zustand/boundStore';
// api
import TaskApi from '../../../../../api/TaskApi';
// flux
import AlertsActions from '../../../../../components/alerts/alerts.actions';

const TaskFirstHeader = ({
    goBack,
    solveClick,
    openTakeModal,
    moreButtons,
    buttonTaskAssignetDoneFuction,
    justSave,
    // hoc
    setNavigationBlocked,
    setConfirmFunction,
}: ITaskFirsHeaderProps) => {
    const theme = useTheme();
    const smUp = useResponsive('up', 'sm');
    const mdDown = useResponsive('down', 'md');
    const { t } = useTranslation();
    const { touchedFields, dirtyFields } = useFormState();

    // use the usePopover hook to manage the popover state
    const popover = usePopover();
    const buttonStatusLoaded = useRef(false);

    const [textHovered, setTextHovered] = useState(false);
    const [pendingSaveAction, setPendingSaveAction] = useState(false);
    const [pendingSolveAction, setPendingSolveAction] = useState(false);
    const [computedStatusIsLoading, setComputedStatusIsLoading] = useState(true);
    const setButtonStatus = useStore((state) => state.setButtonStatus);

    // task store
    const scriptIsRunning = useStore((state) => state.scriptIsRunning);
    const scriptIsRunningWatchVars = useStore((state) => state.scriptIsRunningWatchVars);
    const savingTask = useStore((state) => state.savingTask);
    const taskInfo = useStore((state) => state.taskInfo);
    const buttonsDisabledByDynConds = useStore((state) => state.buttonsDisabledByDynConds);
    const someDynamicRowsIsTouched = useStore((state) => state.someDynamicRowsIsTouched);

    const {
        headingTitle,
        headingStatus,
        isMainButtonEnabled,
        showSolveButton,
        taskForTiming,
        taskForAssign,
        taskToPull,
    } = taskInfo;
    const isFinished = taskInfo.real_itask_status === 'D';
    const showSaveButton = !isFinished && !taskToPull && !taskForAssign && !taskForTiming;
    const formIsDirtyAndTouched = !isEmpty(dirtyFields) && !isEmpty(touchedFields);
    const dynConditionsIsRunning = scriptIsRunning || scriptIsRunningWatchVars;

    let canSetNavigationBlocked = false;
    if (taskForTiming || taskForAssign) {
        // canSetNavigationBlocked = !!dirtyFields.user || !!dirtyFields.start || !!dirtyFields.term;
        canSetNavigationBlocked = false;
    } else {
        canSetNavigationBlocked =
            !isFinished && !savingTask && (formIsDirtyAndTouched || someDynamicRowsIsTouched);
    }

    useEffect(() => {
        setNavigationBlocked(canSetNavigationBlocked);
    }, [canSetNavigationBlocked]);

    useEffect(() => {
        document.title = `${t('task')} - ${headingTitle}`;
    }, [headingTitle]);

    // save task after executing dynamic conditions
    useEffect(() => {
        if (pendingSaveAction && !dynConditionsIsRunning) {
            // timeout - due to possible change of dyn. rows value via dyn. dyn. conditions
            setTimeout(() => {
                justSave?.function();
                setPendingSaveAction(false);
            }, 100);
        }
    }, [pendingSaveAction, dynConditionsIsRunning]);

    // solve task after executing dynamic conditions
    useEffect(() => {
        if (pendingSolveAction && !dynConditionsIsRunning) {
            // timeout - due to possible change of dyn. rows value via dyn. dyn. conditions
            setTimeout(() => {
                solveClick?.();
                setPendingSolveAction(false);
            }, 100);
        }
    }, [pendingSolveAction, dynConditionsIsRunning]);

    setConfirmFunction(justSave?.function);

    const fetchComputedValues = async () => {
        try {
            const computedValues = await TaskApi.fetchComputedValues(taskInfo.itaskId);
            setComputedStatusIsLoading(false);
            if (!computedValues) {
                return;
            }

            const buttonsStatus: any = {
                recalc: computedValues.exec_recalc,
                add: computedValues.c_add,
                handover: computedValues.c_handover,
                handoverNoLimits: computedValues.c_handover_no_limits,
                solve: computedValues.c_solve,
                timing: computedValues.c_due,
                delegation: computedValues.ttask_is_delegatable,
                reject: computedValues.ttask_is_rejectable,
            };

            setButtonStatus(buttonsStatus);
        } catch (error) {
            AlertsActions.addAlert({
                type: 'error',
                show: true,
                message: t('alrFailedData'),
                serverError: (error as Error)?.message || error,
            });
            setComputedStatusIsLoading(false);
        }
    };

    const handleJustSave = () => {
        if (dynConditionsIsRunning) {
            setPendingSaveAction(true);
        } else {
            // due to setting a ckeditor value, which is triggered on blur.
            setTimeout(() => {
                justSave?.function();
            }, 500);
        }
    };

    const handleSolveClick = () => {
        if (dynConditionsIsRunning) {
            setPendingSolveAction(true);
        } else {
            // due to setting a ckeditor value, which is triggered on blur.
            setTimeout(() => {
                solveClick?.();
            }, 500);
        }
    };

    const handlePopoverOpen = (e: React.MouseEvent<HTMLButtonElement>) => {
        popover.onOpen(e);
        if (!buttonStatusLoaded.current) {
            fetchComputedValues();
            buttonStatusLoaded.current = true;
        }
    };

    const isSaveButtonEnabled =
        isMainButtonEnabled &&
        !savingTask &&
        !pendingSaveAction &&
        !pendingSolveAction &&
        !buttonsDisabledByDynConds &&
        (formIsDirtyAndTouched || someDynamicRowsIsTouched);

    const isCompleteButtonEnabled =
        isMainButtonEnabled &&
        !buttonsDisabledByDynConds &&
        !savingTask &&
        !pendingSaveAction &&
        !pendingSolveAction;

    const handleDragEvent = (e: React.DragEvent) => {
        e.stopPropagation();
    };

    return (
        <>
            <Stack direction="row" flexGrow={1} justifyContent="space-between">
                <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="flex-start"
                    spacing={{ xs: 1, sm: 1.5 }}
                >
                    <IconButton
                        color="secondary"
                        icon="arrow-left"
                        size="medium"
                        onClick={goBack}
                    />

                    <MyIcon
                        icon="tasks-filled"
                        sx={{
                            color: theme.palette.primary.main,
                        }}
                    />

                    <Stack
                        direction={{ xs: 'column', sm: 'row' }}
                        spacing={1}
                        alignItems={{ xs: 'flex-start', sm: 'center' }}
                        onMouseEnter={() => setTextHovered(true)}
                        onMouseLeave={() => setTextHovered(false)}
                    >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <TextMaxLine line={3} variant="h1" color="text.primary">
                                {headingTitle}
                            </TextMaxLine>
                            {textHovered && <CopyButton copyText={headingTitle} />}
                        </Box>
                        {headingStatus && (
                            <Label
                                size="small"
                                color="error"
                                startIcon="flag-filled"
                                variant="soft"
                            >
                                {headingStatus}
                            </Label>
                        )}
                    </Stack>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={{ xs: 0, sm: 1 }}>
                    {smUp && (
                        <Button
                            id="task-close-btn"
                            color="secondary"
                            startIcon={<MyIcon icon="close" />}
                            label={t('close')}
                            size="small"
                            variant="text"
                            aria-label={t('close')}
                            onClick={() => goBack()}
                        />
                    )}
                    {typeof moreButtons !== 'undefined' &&
                        (smUp ? (
                            <Button
                                id="task-more-btn"
                                color="secondary"
                                startIcon={<MyIcon icon="more" />}
                                label={t('more')}
                                size="small"
                                variant="text"
                                onClick={handlePopoverOpen}
                                sx={{
                                    ...(popover.open && {
                                        background: (theme) => theme.palette.primary.light,
                                    }),
                                }}
                            />
                        ) : (
                            // otherwise, render an IconButton component
                            <IconButton
                                id="task-more-btn"
                                color="secondary"
                                icon="more"
                                size="large"
                                onClick={handlePopoverOpen}
                                sx={{
                                    ...(popover.open && {
                                        background: (theme) => theme.palette.primary.light,
                                    }),
                                }}
                                aria-label={t('more')}
                            />
                        ))}
                    {mdDown ? (
                        // <Slide appear={false} direction="up" in={!trigger}>
                        <Box
                            display="flex"
                            flexDirection="row-reverse"
                            gap={1}
                            sx={{
                                position: 'fixed',
                                bottom: '0rem',
                                left: '0rem',
                                padding: '0.75rem 1.5rem',
                                width: '100vw',
                                backgroundColor: 'background.paper',
                            }}
                        >
                            {buttonTaskAssignetDoneFuction && (
                                <Button
                                    id="task-assign-btn"
                                    fullWidth
                                    color="primary"
                                    startIcon={<MyIcon icon="check" />}
                                    label={t('done')}
                                    size="small"
                                    variant="contained"
                                    onClick={buttonTaskAssignetDoneFuction}
                                />
                            )}
                            {taskToPull && (
                                <Button
                                    id="task-take-btn"
                                    fullWidth
                                    color="primary"
                                    startIcon={<MyIcon icon="check" />}
                                    label={t('takeover')}
                                    size="small"
                                    variant="contained"
                                    onClick={openTakeModal}
                                    // disabled={!isMainButtonEnabled}
                                />
                            )}
                            {showSolveButton && (
                                <Button
                                    id="task-complete-btn"
                                    fullWidth
                                    color="primary"
                                    startIcon={<MyIcon icon="check" />}
                                    label={t('complete')}
                                    size="small"
                                    variant="contained"
                                    onClick={handleSolveClick}
                                    disabled={!isCompleteButtonEnabled}
                                />
                            )}
                            {showSaveButton && (
                                <Button
                                    id="task-save-btn"
                                    fullWidth
                                    color="secondary"
                                    startIcon={<MyIcon icon="save" />}
                                    label={t('justSave')}
                                    size="small"
                                    variant="outlined"
                                    aria-label={t('justSave')}
                                    disabled={!isSaveButtonEnabled}
                                    onClick={handleJustSave}
                                />
                            )}
                        </Box>
                    ) : (
                        // </Slide>
                        <>
                            {showSaveButton && (
                                <Button
                                    id="task-save-btn"
                                    color="secondary"
                                    startIcon={<MyIcon icon="save" />}
                                    label={t('justSave')}
                                    size="small"
                                    variant="text"
                                    aria-label={t('justSave')}
                                    disabled={!isSaveButtonEnabled}
                                    onClick={handleJustSave}
                                />
                            )}

                            {buttonTaskAssignetDoneFuction && (
                                <Button
                                    id="task-assign-btn"
                                    color="primary"
                                    startIcon={<MyIcon icon="check" />}
                                    label={t('done')}
                                    size="small"
                                    variant="contained"
                                    onClick={buttonTaskAssignetDoneFuction}
                                />
                            )}
                            {taskToPull && (
                                <Button
                                    id="task-take-btn"
                                    color="primary"
                                    startIcon={<MyIcon icon="check" />}
                                    label={t('takeover')}
                                    size="small"
                                    variant="contained"
                                    onClick={openTakeModal}
                                    // disabled={!isMainButtonEnabled}
                                />
                            )}
                            {showSolveButton && (
                                <Button
                                    id="task-complete-btn"
                                    color="primary"
                                    startIcon={<MyIcon icon="check" />}
                                    label={t('complete')}
                                    size="small"
                                    variant="contained"
                                    aria-label={t('complete')}
                                    onClick={handleSolveClick}
                                    disabled={!isCompleteButtonEnabled}
                                />
                            )}
                        </>
                    )}
                </Stack>
            </Stack>
            <div
                onDragEnter={handleDragEvent}
                onDragOver={handleDragEvent}
                onDragLeave={handleDragEvent}
                onDrop={handleDragEvent}
            >
                {typeof moreButtons !== 'undefined' && (
                    <TaskMorePopover
                        moreButtons={moreButtons}
                        popover={popover}
                        computedStatusIsLoading={computedStatusIsLoading}
                    />
                )}
            </div>
        </>
    );
};

const TaskDetailLayoutWithNavigationConfirmation = saveBeforeLeave(TaskFirstHeader);

export default TaskDetailLayoutWithNavigationConfirmation;
