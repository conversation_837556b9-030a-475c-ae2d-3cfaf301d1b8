import React, { useEffect } from 'react';
import { flushSync } from 'react-dom';
// @mui
import { Grid2 as Grid, Skeleton } from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
import { FieldValues, useForm } from 'react-hook-form';
import { TabContext, TabPanel } from '@mui/lab';
import { isEmpty } from 'lodash';
import { useParams, useRouter } from '../../../routes/hooks';
import usePlanDetail from './use-plan-detail';
// components
import SectionsCard from '../../../SectionsCard';
import PlanDetailSettings from './components/PlanDetailSettings';
import FormProvider from '../../../form/FormProvider';
import PlanDetailLayout from './components/PlanDetailLayout';
import withNavigationConfirmation from '../../../hoc/withNavigationConfirmation';
import PlanDetailRepeat from './components/PlanDetailRepeat';
import PlanDetailHistory from './components/PlanDetailHistory';
// utils
import getRepeatFormOptions from './components/repeatFormOptions';
// zustand
import { useStore } from '../../../zustand/boundStore';
// types
import { TPlanDetailViewProps } from '../types';

const PlanDetailView = ({
    setConfirmFunction,
    setNavigationBlocked,
    setIsFormValid,
}: TPlanDetailViewProps) => {
    const { planId }: { planId?: number } = useParams();
    const router = useRouter();
    const { t } = useTranslation();

    const date = new Date();
    const isoDate = date.toISOString().split('T')[0];
    date.setHours(8, 0, 0, 0);
    const isoDefaultTime = date.toISOString();
    date.setHours(12, 0, 0, 0);
    const isoDefaultTimeEnd = date.toISOString();

    const {
        restrictUsersOptions,
        userListValue,
        plan,
        planRepeatFrequency,
        loadPlanData,
        changeRestriction,
        postPlan,
        headersOptions,
        loadHeaders,
    } = usePlanDetail();

    const {
        dateTimePartOptions,
        monthOptions,
        repeatMSubOptions,
        repeatYSubOptions,
        timeFrequencyOptions,
        weekDaysOptions,
    } = getRepeatFormOptions();

    const methods: any = useForm({
        defaultValues: {
            exceptionDates: [{ date: isoDate }],
            isFrequency: false,
            dailyScheduled: [
                {
                    dSubTime: isoDefaultTime,
                    dSubWorkingDaysOnly: false,
                },
            ],
            dailyFrequencies: [
                {
                    dSubTimeStart: isoDefaultTime,
                    dSubTimeEnd: isoDefaultTimeEnd,
                    dSubFrequency: timeFrequencyOptions[0].value,
                    dSubWorkingDaysOnly: false,
                },
            ],
            weeklyScheduled: [
                {
                    wSubWeekday: weekDaysOptions[0].value,
                    wSubTime: isoDefaultTime,
                },
            ],
            weeklyFrequencies: [
                {
                    wSubWeekday: weekDaysOptions[0].value,
                    wSubTimeStart: isoDefaultTime,
                    wSubTimeEnd: isoDefaultTimeEnd,
                    wSubFrequency: timeFrequencyOptions[0].value,
                },
            ],
            monthlyScheduled: [
                {
                    mSubVariant: repeatMSubOptions[0].value,
                    mSubBy: '1',
                    mSubWeekday: weekDaysOptions[0].value,
                    mSubTime: isoDefaultTime,
                },
            ],
            monthlyFrequencies: [
                {
                    mSubVariant: repeatMSubOptions[0].value,
                    mSubBy: '1',
                    mSubWeekday: weekDaysOptions[0].value,
                    mSubTimeStart: isoDefaultTime,
                    mSubTimeEnd: isoDefaultTimeEnd,
                    mSubFrequency: timeFrequencyOptions[0].value,
                },
            ],
            yearlyScheduled: [
                {
                    ySubVariant: repeatYSubOptions[0].value,
                    ySubBy: '1',
                    ySubWeekday: weekDaysOptions[0].value,
                    ySubMonth: monthOptions[0].value,
                },
            ],
            yearlyFrequencies: [
                {
                    ySubVariant: repeatYSubOptions[0].value,
                    ySubBy: '1',
                    ySubWeekday: weekDaysOptions[0].value,
                    ySubMonth: monthOptions[0].value,
                    ySubTimeStart: isoDefaultTime,
                    ySubTimeEnd: isoDefaultTimeEnd,
                    ySubFrequency: timeFrequencyOptions[0].value,
                },
            ],
            timedScheduled: [
                {
                    tSubEvery: '10',
                    tSubVariant: dateTimePartOptions[0].value,
                    tSubWorkingDaysOnly: false,
                },
            ],
            timedFrequencies: [
                {
                    tSubEvery: '10',
                    tSubVariant: dateTimePartOptions[0].value,
                    tSubTimeStart: isoDefaultTime,
                    tSubTimeEnd: isoDefaultTimeEnd,
                    tSubWorkingDaysOnly: false,
                },
            ],
        },
    });

    const {
        setValue,
        handleSubmit,
        formState: { isDirty, isValid },
    } = methods;

    const planDetailTabId = useStore((state) => state.planDetailTabId);
    const planDetailIsBlocking = useStore((state) => state.planDetailIsBlocking);
    const setPlanDetailTabId = useStore((state) => state.setPlanDetailTabId);
    const setPlanDetailIsBlocking = useStore((state) => state.setPlanDetailIsBlocking);
    const planDetailName = useStore((state) => state.planDetailName);
    const setPlanDetailName = useStore((state) => state.setPlanDetailName);
    const planDetailIsLoading = useStore((state) => state.planDetailIsLoading);
    const setPlanDetailIsLoading = useStore((state) => state.setPlanDetailIsLoading);

    useEffect(() => {
        loadHeaders();

        if (planId) {
            loadPlanData();
        } else {
            setPlanDetailName(t('new'));
        }

        return () => {
            setPlanDetailTabId('1');
            setPlanDetailIsBlocking(false);
            setPlanDetailName('');
            setPlanDetailIsLoading(true);
        };
    }, []);

    useEffect(() => {
        if (!isEmpty(plan)) {
            setValue('variant', plan?.pln_repeat_offset_type);
            setValue('isFrequency', plan?.pln_repeat_offset_subtype === 'F');
            setValue('end', plan?.end);
            setValue('exceptions', plan?.exceptions);

            if (plan?.pln_repeat_exceptions && plan?.exceptions === 'X2') {
                setValue('exceptionDates', plan?.pln_repeat_exceptions || [{ date: isoDate }]);
            } else if (plan?.pln_repeat_exceptions && plan?.exceptions === 'X3') {
                setValue('exceptionDateFrom', plan?.pln_repeat_exceptions.from);
                setValue('exceptionDateTo', plan?.pln_repeat_exceptions.to);
            }

            if (plan.end === 'E2') {
                setValue('endDate', plan.pln_repeat_until_date);
            } else if (plan.end === 'E3') {
                setValue('endCount', plan.pln_repeat_count);
            }
        }
    }, [plan]);

    useEffect(() => {
        if (plan.pln_repeat_offset_type === 'D') {
            if (plan.pln_repeat_offset_subtype === 'F') {
                setValue('dailyFrequencies', planRepeatFrequency);
            } else {
                setValue('dailyScheduled', planRepeatFrequency);
            }
        } else if (plan.pln_repeat_offset_type === 'W') {
            if (plan.pln_repeat_offset_subtype === 'F') {
                setValue('weeklyFrequencies', planRepeatFrequency);
            } else {
                setValue('weeklyScheduled', planRepeatFrequency);
            }
        } else if (plan.pln_repeat_offset_type === 'M') {
            if (plan.pln_repeat_offset_subtype === 'F') {
                setValue('monthlyFrequencies', planRepeatFrequency);
            } else {
                setValue('monthlyScheduled', planRepeatFrequency);
            }
        } else if (plan.pln_repeat_offset_type === 'Y') {
            if (plan.pln_repeat_offset_subtype === 'F') {
                setValue('yearlyFrequencies', planRepeatFrequency);
            } else {
                setValue('yearlyScheduled', planRepeatFrequency);
            }
        } else if (plan.pln_repeat_offset_type === 'T') {
            if (plan.pln_repeat_offset_subtype === 'F') {
                setValue('timedFrequencies', planRepeatFrequency);
            } else {
                setValue('timedScheduled', planRepeatFrequency);
            }
        }
    }, [planRepeatFrequency]);

    // function for button save and close in header
    const saveAndClose = async (formData: FieldValues) => {
        await postPlan(formData, () => {
            flushSync(() => {
                setNavigationBlocked(false);
            });
            router.push('/plans');
        });
    };

    // function for button in header just save
    const justSave = async (formData: FieldValues) => {
        await postPlan(formData, () => {
            flushSync(() => setNavigationBlocked(false));
        });
    };

    useEffect(() => {
        const isBlocking = planDetailIsBlocking || isDirty;
        setNavigationBlocked(isBlocking);
        setConfirmFunction(() => handleSubmit(justSave)());
        setIsFormValid(isValid);
    }, [planDetailIsBlocking, isDirty, isValid]);

    useEffect(() => {
        document.title = t('plans');
        if (planDetailName) {
            document.title = `${t('plans')} - ${planDetailName}`;
        }
    }, [planDetailName]);

    return (
        <FormProvider methods={methods}>
            <PlanDetailLayout
                justSave={handleSubmit(justSave)}
                saveAndClose={handleSubmit(saveAndClose)}
                planName={planDetailName}
            >
                {planDetailIsLoading ? (
                    <SectionsCard>
                        <Grid container spacing={1}>
                            {[...Array(8)].map((_, index) => (
                                <Grid size={{ xs: 12, md: 6 }} key={index}>
                                    <Skeleton variant="text" width="100%" height={70} />
                                </Grid>
                            ))}
                        </Grid>
                    </SectionsCard>
                ) : (
                    <TabContext value={String(planDetailTabId)}>
                        <TabPanel value="1">
                            <SectionsCard height={1}>
                                <PlanDetailSettings
                                    plan={plan}
                                    restrictUsersOptions={restrictUsersOptions}
                                    userListValue={userListValue}
                                    changeRestriction={changeRestriction}
                                    headersOptions={headersOptions}
                                />
                            </SectionsCard>
                        </TabPanel>
                        <TabPanel value="2">
                            <SectionsCard height={1}>
                                <PlanDetailRepeat
                                    plan={plan}
                                    planRepeatFrequency={planRepeatFrequency}
                                />
                            </SectionsCard>
                        </TabPanel>
                        <TabPanel value="3">
                            <SectionsCard height={1}>
                                <PlanDetailHistory />
                            </SectionsCard>
                        </TabPanel>
                    </TabContext>
                )}
            </PlanDetailLayout>
        </FormProvider>
    );
};

export default withNavigationConfirmation(PlanDetailView);
