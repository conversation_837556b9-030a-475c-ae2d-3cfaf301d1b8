import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
// @mui
import { Stack, Tooltip } from '@mui/material';
// hooks
import { useResponsive } from '../../../hooks/use-responsive';
import { useStore } from '../../../zustand/boundStore';
import { useBoolean } from '../../../hooks';

import useOverviews from '../use-overviews';
// components
import MyIcon from '../../../MyIcon';
import MainStrictHeight from '../../common/MainStrictHeight';
import AppBarSticky from '../../common/AppBarSticky';
import SectionsCard from '../../../SectionsCard';
import OverviewDataGridConfigMode from '../components/OverviewDataGridConfigMode';
import ModalEditOverview from '../edit-overview/ModalEditOverview';
import ModalSubscribe from '../modal-subscribe/ModalSubscribe';
import TextMaxLine from '../../../text-max-line';
import OverviewDataGrid from '../components/OverviewDataGrid';
import ResponsiveButton from '../../../ResponsiveButton';
import Label from '../../../label';
import LoggedUserStore from '../../../../flux/loggedUser.store';

const OverviewsView = () => {
    const dataGridConfigModeRef = useRef<any>(null);

    const folders = useStore((state) => state.folders);

    const isInConfigMode = useStore((state) => state.isInConfigMode);
    const overviewId = useStore((state) => state.overviewId);
    const treeById = useStore((state) => state.treeById);
    const setOverviewId = useStore((state) => state.setOverviewId);
    const setIsInConfigMode = useStore((state) => state.setIsInConfigMode);
    const toggleIsTreeExpanded = useStore((state) => state.toggleIsTreeExpanded);
    const setTreeExpandedFolders = useStore((state) => state.setTreeExpandedFolders);

    const { userName } = LoggedUserStore.getState();
    const userLang = LoggedUserStore.getState().userLanguage;
    const cvNameKey = `cv_name_${userLang}`;

    const { t } = useTranslation();

    const modalEditOverview = useBoolean();
    const modalSubscribe = useBoolean();

    const smUp = useResponsive('up', 'sm');

    const {
        fetchTreeData, cvHiddenIds, getCvTreeNodesInfo, cvVisibilityLoaded,
    } = useOverviews();

    const activeFolderName = folders.find((item: any) => item.id === overviewId);

    let docTitle = t('overviews');
    if (activeFolderName) {
        docTitle += ` - ${activeFolderName[cvNameKey] || activeFolderName.cv_name}`;
    }
    document.title = docTitle;

    useEffect(() => {
        if (activeFolderName && cvVisibilityLoaded) {
            const expanded = treeById['overviewsTree']?.foldersExpanded || [];
            const activFolderIsHidden = cvHiddenIds.includes(activeFolderName.id);

            const activeParentFolderNodeIds = getCvTreeNodesInfo(
                activFolderIsHidden ? 'overviewsHiddenTree' : 'overviewsAllTree',
                activeFolderName,
            )
                .map((nodeInfo) => nodeInfo.id)
                .slice(0, -1);

            const expandedNew = new Set(expanded);
            activeParentFolderNodeIds.forEach((item) => expandedNew.add(item));
            expandedNew.add('root');

            setTreeExpandedFolders(Array.from(expandedNew), 'overviewsTree');
        }
    }, [overviewId, folders, cvVisibilityLoaded]);

    useEffect(() => {
        fetchTreeData('overviewsTree');
        return () => {
            setOverviewId(null);
        };
    }, []);

    const handleClose = () => {
        dataGridConfigModeRef.current?.handleClose();
    };

    const openExpertModeFromConfigMode = () => {
        dataGridConfigModeRef.current?.handleOpenExpertMode();
    };

    const handleCloseExpertMode = () => {
        modalEditOverview.onFalse();
    };

    const handleSave = () => {
        dataGridConfigModeRef.current?.handleSave();
    };

    const overviewOwner = activeFolderName && (
        <Tooltip
            arrow
            title={
                !smUp &&
                t(activeFolderName.cv_owner_user_name === userName ? 'myOverview' : 'sharedWithMe')
            }
        >
            <Label
                color="primary"
                size="small"
                startIcon={activeFolderName.cv_owner_user_name === userName ? 'user' : 'delegate'}
                sx={{
                    ml: 1,
                    height: '1.75rem',
                }}
            >
                {smUp &&
                    t(
                        activeFolderName.cv_owner_user_name === userName
                            ? 'myOverview'
                            : 'sharedWithMe',
                    )}
            </Label>
        </Tooltip>
    );

    const renderSubline = (
        <Stack
            direction="row"
            flexGrow={1}
            justifyContent="space-between"
            alignItems="center"
            sx={{ width: '100%', height: 1 }}
        >
            <Stack
                direction="row"
                height={1}
                alignItems="center"
                justifyContent="flex-start"
                mr={3}
                spacing={{ xs: 1, sm: 1.5 }}
            >
                <MyIcon
                    icon="overviews"
                    isActive
                    sx={{
                        pl: 1,
                    }}
                    onClick={() => toggleIsTreeExpanded()}
                />
                <TextMaxLine variant="h1" height={1} line={1}>
                    {t('overviews')}
                    {activeFolderName &&
                        `\u00A0-\u00A0${activeFolderName[cvNameKey] || activeFolderName.cv_name}`}
                </TextMaxLine>
                {smUp && overviewOwner}
            </Stack>
            <Stack
                direction="row"
                alignItems="center"
                spacing={{ xs: 0.5, sm: 1 }}
                display={isInConfigMode ? 'none' : 'flex'}
            >
                {!smUp && (
                    <ResponsiveButton
                        color="secondary"
                        icon="hamburger"
                        label={t('folders')}
                        variant="text"
                        onClick={() => toggleIsTreeExpanded()}
                    />
                )}
                {overviewId && (
                    <ResponsiveButton
                        color="secondary"
                        icon="email"
                        label={t('subscribe')}
                        variant="text"
                        onClick={modalSubscribe.onTrue}
                    />
                )}
                {overviewId && smUp && LoggedUserStore.isAdmin() && (
                    <ResponsiveButton
                        color="secondary"
                        icon="expand"
                        label={t('expertMode')}
                        variant="text"
                        onClick={modalEditOverview.onTrue}
                        disabled={typeof activeFolderName === 'undefined'}
                    />
                )}
                {overviewId && smUp && (
                    <ResponsiveButton
                        color="secondary"
                        icon="pen"
                        label={t('editOverview')}
                        variant="text"
                        onClick={() => setIsInConfigMode(true)}
                        disabled={
                            (!LoggedUserStore.isAdmin() &&
                                activeFolderName?.cv_owner_user_name !== userName) ||
                            typeof activeFolderName === 'undefined'
                        }
                    />
                )}

                {/* <Button
                            color="secondary"
                            startIcon={<MyIcon icon="share" />}
                            label="Sdílet přehled"
                            size="small"
                            variant="text"
                            // onClick={() => {
                            //     router.push('/tasks/to-pull');
                            // }}
                        /> */}
            </Stack>
            {/* when cv is in config mode */}
            <Stack
                direction="row"
                spacing={{ xs: 0.5, sm: 1 }}
                display={isInConfigMode ? 'flex' : 'none'}
            >
                <ResponsiveButton
                    color="secondary"
                    icon="expand"
                    label={t('expertMode')}
                    variant="text"
                    onClick={openExpertModeFromConfigMode}
                />
                <ResponsiveButton
                    color="secondary"
                    icon="close"
                    label={t('close')}
                    variant="text"
                    onClick={handleClose}
                />

                <ResponsiveButton
                    variant="contained"
                    color="primary"
                    icon="save"
                    label={t('save')}
                    onClick={handleSave}
                />
            </Stack>
        </Stack>
    );
    return (
        <>
            <AppBarSticky>{renderSubline}</AppBarSticky>
            <MainStrictHeight>
                <SectionsCard
                    withTree
                    height={isInConfigMode ? 'calc(100% - 1px)' : '100%'}
                    sx={{
                        borderRadius: '0.75rem',
                        border: (theme) =>
                            (isInConfigMode ? `1px dashed ${theme.palette.primary.main}` : 'none'),
                    }}
                >
                    {isInConfigMode ? (
                        <OverviewDataGridConfigMode
                            ref={dataGridConfigModeRef}
                            modalEditOverview={modalEditOverview}
                        />
                    ) : (
                        <OverviewDataGrid />
                    )}
                    {/* {children} */}
                </SectionsCard>
            </MainStrictHeight>

            {modalSubscribe.value && (
                <ModalSubscribe
                    isOpen={modalSubscribe.value}
                    onClose={modalSubscribe.onFalse}
                    cvName={
                        (activeFolderName &&
                            (activeFolderName[cvNameKey] || activeFolderName.cv_name)) ||
                        ''
                    }
                />
            )}
            {modalEditOverview.value && (
                <ModalEditOverview
                    isOpen={modalEditOverview.value}
                    onClose={handleCloseExpertMode}
                />
            )}
        </>
    );
};

export default OverviewsView;
