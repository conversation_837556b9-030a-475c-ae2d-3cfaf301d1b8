import React from 'react';
// @mui
import { Stack } from '@mui/material';
// hooks
import { useTranslation } from 'react-i18next';
// components
import SectionsCard from '../../../SectionsCard';
import Select from '../../../form/select/Select';
import FormLabel from '../../../form/FormLabel';
// zustand
import { useStore } from '../../../zustand/boundStore';

const EditOverviewSharing = () => {
    const { t } = useTranslation();

    const usersShare = useStore((state) => state.usersShare);
    const rolesShare = useStore((state) => state.rolesShare);

    return (
        <SectionsCard title={t('settings')}>
            <Stack spacing={2}>
                <FormLabel>{t('overviewSetSharing')}</FormLabel>
                <Select
                    label={t('assignedUsers')}
                    name="usersShare"
                    selectType="DLU"
                    multiple
                    disableCloseOnSelect
                    value={usersShare}
                />
                <Select
                    label={t('assignedOrgUnits')}
                    name="orgsShare"
                    selectType="DLO"
                    multiple
                    disableCloseOnSelect
                />
                <Select
                    label={t('assignedRoles')}
                    name="rolesShare"
                    selectType="DLR"
                    multiple
                    disableCloseOnSelect
                    value={rolesShare}
                />
            </Stack>
        </SectionsCard>
    );
};

export default EditOverviewSharing;
