import React from 'react';
import DocumentTitle from 'react-document-title';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams } from '../routes/hooks';
// components
import CasesView from '../sections/cases/view/CasesView';

const Cases = () => {
    const { t } = useTranslation();
    const { tabName }: { tabName?: string } = useParams();

    let title = t('cases');
    if (!tabName) {
        title += ` - ${t('mine')}`;
    } else if (tabName === 'all') {
        title += ` - ${t('all')}`;
    } else if (tabName === 'suspended') {
        title += ` - ${t('suspendedx')}`;
    } else if (tabName === 'shredded') {
        title += ` - ${t('shredded')}`;
    } else if (tabName === 'errored') {
        title += ` - ${t('errored')}`;
    } else if (tabName === 'archived') {
        title += ` - ${t('archivedx')}`;
    }

    return (
        <DocumentTitle title={title}>
            <CasesView />
        </DocumentTitle>
    );
};

export default Cases;
