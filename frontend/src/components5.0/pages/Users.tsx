import React, { useEffect } from 'react';
import DocumentTitle from 'react-document-title';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams } from '../routes/hooks';
// components
import UsersView from '../sections/users/users-overview/UsersView';
// utils
import PageRights from '../../common/pageRights';

const Users = () => {
    const { t } = useTranslation();
    const { tabName }: { tabName?: string } = useParams();

    useEffect(() => {
        PageRights.checkUserRights([-1]);
    }, []);

    let title = t('users');
    if (!tabName) {
        title += ` - ${t('registered')}`;
    } else if (tabName === 'deleted') {
        title += ` - ${t('usersDeleted')}`;
    } else if (tabName === 'roles') {
        title += ` - ${t('byRole')}`;
    } else if (tabName === 'organization') {
        title += ` - ${t('byOrganization')}`;
    }

    return (
        <DocumentTitle title={title}>
            <UsersView />
        </DocumentTitle>
    );
};

export default Users;
