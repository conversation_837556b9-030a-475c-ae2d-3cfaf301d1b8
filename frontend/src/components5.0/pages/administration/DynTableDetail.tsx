import React, { useEffect } from 'react';
import DocumentTitle from 'react-document-title';
// hooks
import { useTranslation } from 'react-i18next';
// components
import DynTableDetailView from '../../sections/administration/dyn-tables/detail/DynTableDetailView';
// utils
import PageRights from '../../../common/pageRights';
// zustand
import { useStore } from '../../zustand/boundStore';

const DynTableDetail = () => {
    const { t } = useTranslation();
    const dynTableDetailName = useStore((state) => state.dynTableDetailName);

    useEffect(() => {
        // Admin
        PageRights.checkUserRights([-1]);
    }, []);

    let docTitle = t('dynamicTable');
    if (dynTableDetailName) {
        docTitle += ` - ${dynTableDetailName}`;
    }

    return (
        <DocumentTitle title={docTitle}>
            <DynTableDetailView />
        </DocumentTitle>
    );
};

export default DynTableDetail;
