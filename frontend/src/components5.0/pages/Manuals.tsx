import React from 'react';
import DocumentTitle from 'react-document-title';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams } from '../routes/hooks';
// components
import ManualsView from '../sections/manuals/view/ManualsView';

const Manuals = () => {
    const { t } = useTranslation();
    const { tabName }: { tabName?: string } = useParams();

    let title = t('Manuals');
    if (tabName === 'manuals') {
        title += ` - ${t('files')}`;
    } else if (tabName === 'logos') {
        title += ` - ${t('logos')}`;
    }

    return (
        <DocumentTitle title={title}>
            <ManualsView />
        </DocumentTitle>
    );
};

export default Manuals;
