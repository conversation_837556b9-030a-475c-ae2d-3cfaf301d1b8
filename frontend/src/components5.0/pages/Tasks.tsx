import React from 'react';
import DocumentTitle from 'react-document-title';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams } from '../routes/hooks';
// components
import TasksView from '../sections/tasks/view/TasksView';

const Tasks = () => {
    const { t } = useTranslation();
    const { tabName }: { tabName?: string } = useParams();

    let title = t('tasks');
    if (!tabName) {
        title += ` - ${t('mine')}`;
    } else if (tabName === 'to-pull') {
        title += ` - ${t('toPull')}`;
    } else if (tabName === 'all') {
        title += ` - ${t('all')}`;
    } else if (tabName === 'bulk') {
        title += ` - ${t('bulk')}`;
    }

    return (
        <DocumentTitle title={title}>
            <TasksView />
        </DocumentTitle>
    );
};

export default Tasks;
