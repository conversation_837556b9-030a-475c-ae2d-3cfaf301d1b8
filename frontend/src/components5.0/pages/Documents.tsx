import React from 'react';
import DocumentTitle from 'react-document-title';
// hooks
import { useTranslation } from 'react-i18next';
import { useParams } from '../routes/hooks';
// components
import DocumentsView from '../sections/documents/view/DocumentsView';

const Documents = () => {
    const { t } = useTranslation();
    const { tabName }: { tabName?: string } = useParams();

    let title = t('documents');
    if (!tabName) {
        title += ` - ${t('search')}`;
    } else if (tabName === 'all') {
        title += ` - ${t('all')}`;
    } else if (tabName === 'deleted') {
        title += ` - ${t('deletedDocs')}`;
    } else if (tabName === 'access-log') {
        title += ` - ${t('accessLog')}`;
    }

    return (
        <DocumentTitle title={title}>
            <DocumentsView />
        </DocumentTitle>
    );
};

export default Documents;
