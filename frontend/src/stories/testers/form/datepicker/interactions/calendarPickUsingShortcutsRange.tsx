import { expect, userEvent } from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';
import DatePickerTesterUtils from '../DatePickerTesterUtils';

const calendarPickUsingShortcutsRange: StoryTester['interactions']['calendarPickUsingShortcutsRange'] = {
    play: async (context) => {
        const { step } = context;
        const calendarButton = CQ.calendarButton(context);
        const calendarInput = CQ.calendarInput(context);

        // today
        const today = new Date();

        // last 7 days
        const last7Days = new Date();
        last7Days.setDate(today.getDate() - 7);

        // last 14 days
        const last14Days = new Date();
        last14Days.setDate(today.getDate() - 14);

        // last 30 days
        const last30Days = new Date();
        last30Days.setDate(today.getDate() - 30);

        // last calendar month
        const lastCalendarMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        // last quarter
        let lastQuarter = new Date();
        const [Q1, Q2, Q3, Q4] = [
            new Date(today.getFullYear(), 0, 1),
            new Date(today.getFullYear(), 3, 1),
            new Date(today.getFullYear(), 6, 1),
            new Date(today.getFullYear(), 9, 1),
        ];
        if (today >= Q1 && today < Q2) {
            lastQuarter = Q1;
        } else if (today >= Q2 && today < Q3) {
            lastQuarter = Q2;
        } else if (today >= Q3 && today < Q4) {
            lastQuarter = Q3;
        } else if (today >= Q4) {
            lastQuarter = Q4;
        }

        // last 12 months
        const last12Months = new Date();
        last12Months.setFullYear(today.getFullYear() - 1);

        // last calendar year
        const lastCalendarYear = new Date(today.getFullYear() - 1, 11, 31);

        const shortcuts = [
            { title: 'last 7 days', date: last7Days },
            { title: 'last 14 days', date: last14Days },
            { title: 'last 30 days', date: last30Days },
            { title: 'last calendar month', date: lastCalendarMonth },
            { title: 'last quarter', date: lastQuarter },
            { title: 'last 12 months', date: last12Months },
            { title: 'last calendar year', date: lastCalendarYear },
        ];

        // eslint-disable-next-line no-restricted-syntax
        for await (const [index, shortcut] of shortcuts.entries()) {
            await step(
                `[DatePicker] Pick the '${shortcut.title}' shortcut`,
                async () => {
                    await step('[DatePicker] Open the calendar & click the shortcut', async () => {
                        await userEvent.click(calendarButton);
                        const calendarShortcutButtons = await CQ.calendarShortcutButtons(context);
                        await userEvent.click(calendarShortcutButtons[index]);
                    });

                    const formattedExpactedDate = DatePickerTesterUtils.getFormattedDate(
                        shortcut.date,
                        context,
                    );

                    await step(
                        `[DatePicker] The input field value should be '${formattedExpactedDate}'`,
                        async () => {
                            expect(calendarInput).toHaveDisplayValue(formattedExpactedDate);
                            expect(calendarInput).toHaveValue(formattedExpactedDate);
                        },
                    );
                },
            );
        }

        await CI.deleteInputManually(context);
    },
};

export default calendarPickUsingShortcutsRange;
