import { expect, userEvent } from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';
import DatePickerTesterUtils from '../DatePickerTesterUtils';

const calendarPickUsingShortcutsDays: StoryTester['interactions']['calendarPickUsingShortcutsDays'] = {
    play: async (context) => {
        const { step } = context;
        const calendarButton = CQ.calendarButton(context);
        const calendarInput = CQ.calendarInput(context);

        // today
        const today = new Date();

        // yesterday
        const yesterday = new Date();
        yesterday.setDate(today.getDate() - 1);

        // tomorrow
        const tomorrow = new Date();
        tomorrow.setDate(today.getDate() + 1);

        // previous work day
        const previousWorkDay = new Date();
        do {
            previousWorkDay.setDate(previousWorkDay.getDate() - 1);
        } while (previousWorkDay.getDay() === 0 || previousWorkDay.getDay() === 6);

        // beginning of the month
        const beginningOfTheMonth = new Date(today.getFullYear(), today.getMonth(), 1);

        // end of the month
        const endOfTheMonth = new Date();
        endOfTheMonth.setMonth(today.getMonth() + 1);
        endOfTheMonth.setDate(0); // 0 -> last day of the previous month (that's why we set the month to the next one (line above))

        // beginning of the year
        const beginningOfTheYear = new Date(today.getFullYear(), 0, 1);

        // end of the year
        const endOfTheYear = new Date(today.getFullYear(), 11, 31);

        const shortcuts = [
            { title: 'today', date: today },
            { title: 'yesterday', date: yesterday },
            { title: 'tommorow', date: tomorrow },
            { title: 'previousWorkDay', date: previousWorkDay },
            { title: 'beginning of the month', date: beginningOfTheMonth },
            { title: 'end of the month', date: endOfTheMonth },
            { title: 'beginning of the year', date: beginningOfTheYear },
            { title: 'end of the year', date: endOfTheYear },
        ];

        // eslint-disable-next-line no-restricted-syntax
        for await (const [index, shortcut] of shortcuts.entries()) {
            await step(
                `[DatePicker] Pick the '${shortcut.title}' shortcut`,
                async () => {
                    await step('[DatePicker] Open the calendar & click the shortcut', async () => {
                        await userEvent.click(calendarButton);
                        const calendarShortcutButtons = await CQ.calendarShortcutButtons(context);
                        await userEvent.click(calendarShortcutButtons[index]);
                    });

                    const formattedExpactedDate = DatePickerTesterUtils.getFormattedDate(
                        shortcut.date,
                        context,
                    );

                    await step(
                        `[DatePicker] The input field value should be '${formattedExpactedDate}'`,
                        async () => {
                            expect(calendarInput).toHaveDisplayValue(formattedExpactedDate);
                            expect(calendarInput).toHaveValue(formattedExpactedDate);
                        },
                    );
                },
            );
        }

        await CI.deleteInputManually(context);
    },
};

export default calendarPickUsingShortcutsDays;
