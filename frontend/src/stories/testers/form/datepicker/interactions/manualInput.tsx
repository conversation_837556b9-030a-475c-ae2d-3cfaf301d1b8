import { expect, userEvent } from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';

const manualInput: StoryTester['interactions']['manualInput'] = {
    play: async (context) => {
        const { canvasElement, step } = context;
        const calendarInput = CQ.calendarInput(context);

        await step('[DatePicker] Manually inputting the value behaves correctly', async () => {
            await step('[DatePicker] Input \'31122000\'', async () => {
                await userEvent.type(calendarInput, '31122000');

                await step('[DatePicker] The input field value is \'31.12.2000\'', async () => {
                    expect(calendarInput).toHaveDisplayValue('31.12.2000');
                    expect(calendarInput).toHaveValue('31.12.2000');
                });
            });

            await CI.deleteInputQuickly(context);

            await step(
                '[DatePicker] Input right arrow -> \'1\' -> left arrow -> 2 -> 2x right arrow -> 3',
                async () => {
                    await userEvent.pointer({
                        target: calendarInput,
                        keys: '[MouseLeft]',
                    });
                    await userEvent.pointer({
                        target: calendarInput,
                        keys: '[MouseLeft]',
                    });
                    await userEvent.keyboard('{ArrowRight}');
                    await userEvent.keyboard('1');
                    await userEvent.keyboard('{ArrowLeft}');
                    await userEvent.keyboard('2');
                    await userEvent.keyboard('{ArrowRight}');
                    await userEvent.keyboard('{ArrowRight}');
                    await userEvent.keyboard('3');
                    await userEvent.click(canvasElement);

                    await step('[DatePicker] The input field value is 02.01.0003', async () => {
                        expect(calendarInput).toHaveDisplayValue('02.01.0003');
                        expect(calendarInput).toHaveValue('02.01.0003');
                    });
                },
            );
        });

        await CI.deleteInputUsingClearButton(context);
    },
};

export default manualInput;
