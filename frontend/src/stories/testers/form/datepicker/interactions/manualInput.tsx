import { expect, userEvent } from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';
import DatePickerTesterUtils from '../DatePickerTesterUtils';

const manualInput: StoryTester['interactions']['manualInput'] = {
    play: async (context) => {
        const { canvasElement, step } = context;
        const calendarInput = CQ.calendarInput(context);

        await step('[DatePicker] Manually inputting the value behaves correctly', async () => {
            const formattedDate1 = DatePickerTesterUtils.getFormattedDate(new Date(), context);
            const toType1 = formattedDate1.split(/[^0-9]+/).filter((part) => part.length > 0).join('');

            await step(`[DatePicker] Input '${toType1}'`, async () => {
                await userEvent.type(calendarInput, toType1);

                await step(`[DatePicker] The input field value is '${formattedDate1}'`, async () => {
                    expect(calendarInput).toHaveDisplayValue(formattedDate1);
                    expect(calendarInput).toHaveValue(formattedDate1);
                });
            });

            await CI.deleteInputQuickly(context);

            const formattedDate2 = DatePickerTesterUtils.getFormattedDate(new Date(3, 1, 3), context);
            const formattedDate2Parts = formattedDate2.split(/[^0-9]+/).filter((part) => part.length > 0).map(Number);

            await step(
                `[DatePicker] Input right arrow -> ${formattedDate2Parts[1]} -> left arrow -> ${formattedDate2Parts[0]} -> 2x right arrow -> ${formattedDate2Parts[2]}`,
                async () => {
                    // the input should be focused after the first click on it (which does happen outside of Storybook), but for some reason, it's not
                    // - this is why we need to click it twice (TODO: investigate)
                    await userEvent.pointer({ target: calendarInput, keys: '[MouseLeft]' });
                    await userEvent.pointer({ target: calendarInput, keys: '[MouseLeft]' });

                    await userEvent.keyboard('{ArrowRight}');
                    await userEvent.keyboard(String(formattedDate2Parts[1]));
                    await userEvent.keyboard('{ArrowLeft}');
                    await userEvent.keyboard(String(formattedDate2Parts[0]));
                    await userEvent.keyboard('{ArrowRight}');
                    await userEvent.keyboard('{ArrowRight}');
                    await userEvent.keyboard(String(formattedDate2Parts[2]));
                    await userEvent.click(canvasElement);

                    await step(`[DatePicker] The input field value is ${formattedDate2}`, async () => {
                        expect(calendarInput).toHaveDisplayValue(formattedDate2);
                        expect(calendarInput).toHaveValue(formattedDate2);
                    });
                },
            );
        });

        await CI.deleteInputUsingClearButton(context);
    },
};

export default manualInput;
