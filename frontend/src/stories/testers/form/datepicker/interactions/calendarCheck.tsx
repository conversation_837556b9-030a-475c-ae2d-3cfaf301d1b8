import {
    expect, within, userEvent, waitFor, screen,
} from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CQ from '../DatePickerTesterCQ';

const calendarCheck: StoryTester['interactions']['calendarCheck'] = {
    play: async (context) => {
        const { canvasElement, step } = context;
        const calendarButton = CQ.calendarButton(context);

        await step('[DatePicker] The calendar displays & disappears correctly', async () => {
            await step(
                '[DatePicker] The calendar appears after clicking the calendar button',
                async () => {
                    await userEvent.click(calendarButton);
                    expect(screen.getByRole('dialog')).toBeInTheDocument();
                },
            );

            await step(
                '[DatePicker] The amount of grid cells within the calendar is divisble by 7',
                async () => {
                    expect(
                        within(screen.getByRole('dialog')).getAllByRole('gridcell').length % 7,
                    ).toBe(0);
                },
            );

            await step(
                '[DatePicker] The calendar disappears after clicking outside the calendar',
                async () => {
                    await userEvent.click(canvasElement);
                    await waitFor(
                        () => {
                            expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
                        },
                        { timeout: 500 },
                    );
                },
            );
        });
    },
};

export default calendarCheck;
