import { expect, userEvent, waitFor } from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';

const calendarPickWithArrows: StoryTester['interactions']['calendarPickWithArrows'] = {
    play: async (context) => {
        const { step, canvasElement } = context;
        const calendarButton = CQ.calendarButton(context);
        const calendarInput = CQ.calendarInput(context);

        await step('[DatePicker] Pick a day by using the arrow keys', async () => {
            await step('[DatePicker] Open the calendar', async () => {
                await userEvent.click(calendarButton);
            });

            const now = new Date();
            const getFuture = (currentDate: Date, addDays: number): any =>
                new Date(currentDate.getTime() + addDays * 24 * 60 * 60 * 1000);

            const arrowSteps = [
                { s: '{ArrowDown}', d: 7 },
                { s: '{ArrowRight}', d: 1 },
                { s: '{ArrowLeft}', d: -1 }, // 1 down
                { s: '{ArrowUp}', d: -7 },
                { s: '{ArrowDown}', d: 7 },
                { s: '{ArrowDown}', d: 7 }, // 1 down
                { s: '{ArrowRight}', d: 1 }, // 1 right
            ];

            await step(
                '[DatePicker] Use the arrows (mix all of them) to pick 2 weeks & 1 day in the future',
                async () => {
                    let nowCurrent = getFuture(new Date(), 0);
                    let lastActiveElement = document.activeElement;
                    // eslint-disable-next-line no-restricted-syntax
                    for await (const arrowStep of arrowSteps) {
                        await userEvent.keyboard(arrowStep.s);
                        nowCurrent = getFuture(nowCurrent, arrowStep.d);
                        // eslint-disable-next-line @typescript-eslint/no-loop-func, no-loop-func
                        await waitFor(
                            () => {
                                expect(document.activeElement).not.toBe(lastActiveElement);
                            },
                            { timeout: 1000 },
                        );
                        lastActiveElement = document.activeElement;
                    }
                    await userEvent.keyboard('{Enter}{Enter}');
                    await userEvent.click(canvasElement);
                },
            );

            const future = getFuture(now, 7 * 2 + 1);
            const day = String(future.getDate()).padStart(2, '0');
            const month = String(future.getMonth() + 1).padStart(2, '0');
            const year = future.getFullYear();
            await step(
                `[DatePicker] The input field value should be '${day}.${month}.${year}'`,
                async () => {
                    expect(calendarInput).toHaveDisplayValue(`${day}.${month}.${year}`);
                    expect(calendarInput).toHaveValue(`${day}.${month}.${year}`);
                },
            );
        });

        await CI.deleteInputQuickly(context);
    },
};

export default calendarPickWithArrows;
