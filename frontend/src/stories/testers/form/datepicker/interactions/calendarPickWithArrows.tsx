import { expect, userEvent } from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';
import DatePickerTesterUtils from '../DatePickerTesterUtils';

const calendarPickWithArrows: StoryTester['interactions']['calendarPickWithArrows'] = {
    play: async (context) => {
        const { step, canvasElement } = context;
        const calendarInput = CQ.calendarInput(context);

        await step('[DatePicker] Pick a day by using the arrow keys', async () => {
            const today = new Date();
            const date = new Date(today.getFullYear() + 2, 8, 2);
            const formattedExpactedDate = DatePickerTesterUtils.getFormattedDate(date, context);

            const yearArrowSteps = [
                '{ArrowUp}', // current year
                '{ArrowUp}', // current year + 1
                '{ArrowUp}', // current year + 2
            ];

            const monthArrowSteps = [
                '{ArrowDown}', // 12
                '{ArrowDown}', // 11
                '{ArrowDown}', // 10
                '{ArrowDown}', // 9 (monthIndex === 8)
            ];

            const dayArrowSteps = [
                '{ArrowUp}', // 1
                '{ArrowUp}', // 2
            ];

            const datePartOrder = DatePickerTesterUtils.getDatePartOrder(context);
            const arrowSteps: string[] = [];
            datePartOrder.forEach((part, index) => {
                switch (part) {
                    case 'year':
                        arrowSteps.push(...yearArrowSteps);
                        break;
                    case 'month':
                        arrowSteps.push(...monthArrowSteps);
                        break;
                    case 'day':
                        arrowSteps.push(...dayArrowSteps);
                        break;
                    default:
                        break;
                }

                if (index < datePartOrder.length - 1) {
                    arrowSteps.push('{ArrowRight}');
                }
            });

            await step('[DatePicker] Open the calendar (click inside the input to focus the first date part)', async () => {
                await userEvent.pointer({ target: calendarInput, keys: '[MouseLeft]' });
                await userEvent.pointer({ target: calendarInput, keys: '[MouseLeft]' });
            });

            await step(
                `[DatePicker] Use the arrow keys on the input to pick the date '${formattedExpactedDate}'`,
                async () => {
                    // eslint-disable-next-line no-restricted-syntax
                    for await (const arrowStep of arrowSteps) {
                        await userEvent.keyboard(arrowStep, { skipClick: true });
                    }
                    await userEvent.keyboard('{Enter}');
                    await userEvent.click(canvasElement);
                },
            );

            await step(
                `[DatePicker] The input field value should be '${formattedExpactedDate}'`,
                async () => {
                    expect(calendarInput).toHaveDisplayValue(formattedExpactedDate);
                    expect(calendarInput).toHaveValue(formattedExpactedDate);
                },
            );
        });

        await CI.deleteInputQuickly(context);
    },
};

export default calendarPickWithArrows;
