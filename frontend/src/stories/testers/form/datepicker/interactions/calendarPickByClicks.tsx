import {
    expect, userEvent, within, waitFor, screen,
} from '@storybook/test';

import { StoryTester } from '../../../StoryTester';
import CI from '../DatePickerTesterCI';
import CQ from '../DatePickerTesterCQ';
import DatePickerTesterUtils from '../DatePickerTesterUtils';

const calendarPickByClicks: StoryTester['interactions']['calendarPickByClicks'] = {
    play: async (context) => {
        const { step, canvasElement } = context;
        const calendarButton = CQ.calendarButton(context);
        const calendarInput = CQ.calendarInput(context);

        const now = new Date();

        await step(
            '[DatePicker] Pick a day by utilizing all of the clickable elements of the calendar',
            async () => {
                await step('[DatePicker] Open the calendar', async () => {
                    await userEvent.click(calendarButton);
                });

                // left-most buttons come first
                const calendarControlButtons = Array.from(
                    within(screen.getByRole('dialog')).getAllByRole('button'),
                ).filter( // exclude potential shortcuts
                    (button) => button.getElementsByClassName('icon2-events').length === 0,
                ).sort(
                    (a, b) => a.getBoundingClientRect().x - b.getBoundingClientRect().x,
                );

                const newYear = now.getFullYear() + 1; // next year
                await step(`[DatePicker] Pick the next year (${newYear})`, async () => {
                    await userEvent.click(calendarControlButtons[0]);
                    await userEvent.click(within(screen.getByRole('dialog')).getByText(newYear));
                });

                const newMonth = String(now.getMonth() + 1 + 1).padStart(2, '0'); // next month
                await step(
                    '[DatePicker] Cycle to the previous & then to the next month',
                    async () => {
                        await userEvent.click(calendarControlButtons[1]);
                        await userEvent.click(calendarControlButtons[2]);
                        await userEvent.click(calendarControlButtons[2]);

                        await waitFor(
                            () => {
                                // wait till all the other months' cells disappear from the DOM
                                expect(screen.getByRole('dialog')).toBeInTheDocument();
                                if (
                                    within(screen.getByRole('dialog')).getAllByRole('gridcell')
                                        .length < 28 ||
                                    within(screen.getByRole('dialog')).getAllByRole('gridcell')
                                        .length >
                                        6 * 7
                                ) {
                                    throw Error(
                                        'There\'s more calendar cells than there should be (28 <= cells <= 42)',
                                    );
                                }
                            },
                            { timeout: 2000 },
                        );
                    },
                );

                const calendarCells = within(screen.getByRole('dialog')).getAllByRole('gridcell');
                const newDayCell = calendarCells[Math.round(calendarCells.length / 2)];
                const newDay = Number(newDayCell.innerText);
                await step('[DatePicker] Pick a day (middle of the calendar)', async () => {
                    await step(
                        `[DatePicker] Picking number ${newDay} (middle of the calendar)`,
                        async () => {},
                    );
                    await userEvent.click(newDayCell);
                    await userEvent.click(canvasElement);
                });

                const formattedExpactedDate = DatePickerTesterUtils.getFormattedDate(
                    new Date(
                        newYear,
                        Number(newMonth) - 1,
                        newDay,
                    ),
                    context,
                );

                await step(
                    `[DatePicker] The input field value should be '${formattedExpactedDate}'`,
                    async () => {
                        expect(calendarInput).toHaveDisplayValue(formattedExpactedDate);
                        expect(calendarInput).toHaveValue(formattedExpactedDate);
                    },
                );
            },
        );

        await CI.deleteInputQuickly(context);
    },
};

export default calendarPickByClicks;
