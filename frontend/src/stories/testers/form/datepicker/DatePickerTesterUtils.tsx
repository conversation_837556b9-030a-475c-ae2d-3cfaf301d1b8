import { StoryContext } from '@storybook/types';
import { replace } from 'lodash';

type DayMonthYear = 'day' | 'month' | 'year';

const DatePickerTesterUtils = {
    getFormattedDate: (date: Date, context: StoryContext<any>) => {
        const { language } = context.globals;

        const toReturn = replace(
            date.toLocaleDateString(
                [language],
                {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric',
                },
            ),
            /\s/g,
            '',
        );

        if (language === 'sr') { // "DD.MM.YYYY." -> "DD. MM. YYYY."
            return toReturn.split('.').join('. ').slice(0, -1);
        }

        return toReturn;
    },
    getDatePartOrder: (context: StoryContext<any>): [DayMonthYear, DayMonthYear, DayMonthYear] => {
        const { language } = context.globals;

        if (language === 'en') {
            return ['month', 'day', 'year'];
        }

        return ['day', 'month', 'year'];
    },
};

export default DatePickerTesterUtils;
