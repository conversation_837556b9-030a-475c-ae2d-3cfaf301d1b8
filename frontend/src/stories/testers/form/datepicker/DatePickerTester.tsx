import { FormElementTester } from '../FormElementTester';
import { StoryTester } from '../../StoryTester';
import { TesterUtils } from '../../TesterUtils';
import { getInteractionMode } from '../../InteractionMode';

import calendarCheck from './interactions/calendarCheck';
import calendarPickWithArrows from './interactions/calendarPickWithArrows';
import calendarPickByClicks from './interactions/calendarPickByClicks';
import manualInput from './interactions/manualInput';
import calendarPickUsingShortcutsDays from './interactions/calendarPickUsingShortcutsDays';
import calendarPickUsingShortcutsRange from './interactions/calendarPickUsingShortcutsRange';

export const DatePickerTester: StoryTester = {
    argValidations: {
        ...FormElementTester?.argValidations,
    },
    interactions: {
        calendarCheck,
        calendarPickWithArrows,
        calendarPickByClicks,
        manualInput,
        calendarPickUsingShortcutsDays,
        calendarPickUsingShortcutsRange,
    },
};

DatePickerTester.play = TesterUtils.packPlayFunction(async (context) => {
    const iMode = getInteractionMode(context.globals.interactionMode);

    if (iMode.flags.args) {
        await TesterUtils.validateArgs(context, DatePickerTester.argValidations);
    }

    await FormElementTester.play?.(context);

    if (iMode.flags.intrusive && !context.args.disabled && !context.args.readOnly) {
        await DatePickerTester.interactions.calendarCheck.play(context);
        await DatePickerTester.interactions.calendarPickWithArrows.play(context);
        await DatePickerTester.interactions.calendarPickByClicks.play(context);
        await DatePickerTester.interactions.manualInput.play(context);

        if (context.args.shortcuts) {
            if (context.args.shortcuts === 'shortcutsDays') {
                await DatePickerTester.interactions.calendarPickUsingShortcutsDays.play(context);
            } else if (context.args.shortcuts === 'shortcutsRange') {
                await DatePickerTester.interactions.calendarPickUsingShortcutsRange.play(context);
            }
        }
    }
});
