import { FormElementTester } from '../FormElementTester';
import { StoryTester } from '../../StoryTester';
import { TesterUtils } from '../../TesterUtils';
import { getInteractionMode } from '../../InteractionMode';

import calendarCheck from './interactions/calendarCheck';
import calendarPickToday from './interactions/calendarPickToday';
import calendarPickWithArrows from './interactions/calendarPickWithArrows';
import calendarPickByClicks from './interactions/calendarPickByClicks';
import manualInput from './interactions/manualInput';

export const DatePickerTester: StoryTester = {
    argValidations: {
        ...FormElementTester?.argValidations,
    },
    interactions: {
        calendarCheck,
        calendarPickToday,
        calendarPickWithArrows,
        calendarPickByClicks,
        manualInput,
    },
};

DatePickerTester.play = TesterUtils.packPlayFunction(async (context) => {
    const iMode = getInteractionMode(context.globals.interactionMode);

    if (iMode.flags.args) {
        await TesterUtils.validateArgs(context, DatePickerTester.argValidations);
    }

    await FormElementTester.play?.(context);

    if (iMode.flags.intrusive && !context.args.disabled && !context.args.readOnly) {
        await DatePickerTester.interactions.calendarCheck.play(context);
        await DatePickerTester.interactions.calendarPickToday.play(context);
        await DatePickerTester.interactions.calendarPickWithArrows.play(context);
        await DatePickerTester.interactions.calendarPickByClicks.play(context);
        await DatePickerTester.interactions.manualInput.play(context);
    }
});
