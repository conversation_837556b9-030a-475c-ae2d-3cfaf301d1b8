import { expect, userEvent } from '@storybook/test';

import { StoryTesterCommonInteractions } from '../../StoryTester';
import CQ from './DatePickerTesterCQ';

const DatePickerTesterCI: StoryTesterCommonInteractions = {
    deleteInputQuickly: async (context) => {
        const { canvasElement, step } = context;
        const calendarInput = CQ.calendarInput(context);
        await step(
            '[DatePicker] Delete the input field value quickly (select all and backspace, no checks)',
            async () => {
                await userEvent.dblClick(calendarInput); // click the input twice to re-focus it after the calendar opens
                await userEvent.keyboard('[ControlLeft>]a{Backspace}');
                await userEvent.click(canvasElement);
            },
        );
    },
    deleteInputManually: async (context) => {
        // use this at least once to check the behaviour of this delete approach
        const { canvasElement, step } = context;
        const calendarInput = CQ.calendarInput(context);

        await step(
            '[DatePicker] Delete the input field value manually (arrows and backspaces, check the result)',
            async () => {
                // click the input twice to re-focus it after the calendar opens
                await userEvent.pointer({
                    target: calendarInput,
                    keys: '[MouseLeft]',
                });
                await userEvent.pointer({
                    target: calendarInput,
                    keys: '[MouseLeft]',
                });

                await userEvent.keyboard(
                    '{Backspace}{ArrowRight}{Backspace}{ArrowRight}{Backspace}',
                );
                await userEvent.click(canvasElement);

                await step('[DatePicker] The value was actually deleted', async () => {
                    expect(calendarInput).toHaveDisplayValue('');
                    expect(calendarInput).toHaveValue('');
                });
            },
        );
    },
    deleteInputUsingClearButton: async (context) => {
        // use this at least once to check the behaviour of this delete approach
        const { step } = context;
        const calendarInput = CQ.calendarInput(context);
        const clearInputButton = CQ.clearInputButton(context);

        await step(
            '[DatePicker] Delete the input field value using the clear button (and check the result)',
            async () => {
                await userEvent.click(clearInputButton);

                await step('[DatePicker] The value was actually deleted', async () => {
                    expect(calendarInput).toHaveDisplayValue('');
                    expect(calendarInput).toHaveValue('');
                });
            },
        );
    },
};

export default DatePickerTesterCI;
