import { expect, fireEvent, userEvent } from '@storybook/test';

import { StoryTesterCommonInteractions } from '../../StoryTester';
import CQ from './DatePickerTesterCQ';

const DatePickerTesterCI: StoryTesterCommonInteractions = {
    deleteInputQuickly: async (context) => {
        const { canvasElement, step } = context;
        const calendarInput = CQ.calendarInput(context);
        await step(
            '[DatePicker] Delete the input field value quickly (select all and backspace, no checks)',
            async () => {
                // the input should be focused after the first click on it (which does happen outside of Storybook), but for some reason, it's not
                // - this is why we need to dblClick (TODO: investigate)
                await userEvent.dblClick(calendarInput);

                // for some reason the placeholder stays as a value after the first backspace (which doesn't happen outside of Storybook)
                // - this is why we need to press backspace twice (TODO: investigate)
                await userEvent.keyboard('[ControlLeft>]a{Backspace}');
                await userEvent.keyboard('{Backspace}');

                await userEvent.click(canvasElement);

                // unless we wait for a bit, the state of the calendar will stay at the last selected date for a moment,
                // which could break following tests (TODO: find a better way to handle this)
                await new Promise((resolve) => { setTimeout(resolve, 1000); });

                await step('[DatePicker] The value was actually deleted', async () => {
                    expect(calendarInput).toHaveDisplayValue('');
                    expect(calendarInput).toHaveValue('');
                });
            },
        );
    },
    deleteInputManually: async (context) => {
        // use this at least once to check the behaviour of this delete approach
        const { canvasElement, step } = context;
        const calendarInput = CQ.calendarInput(context);

        await step(
            '[DatePicker] Delete the input field value manually (arrows and backspaces, check the result)',
            async () => {
                // the input should be focused after the first click on it (which does happen outside of Storybook), but for some reason, it's not
                // - this is why we need to dblClick (TODO: investigate)
                await userEvent.pointer({ target: calendarInput, keys: '[MouseLeft]' });
                await userEvent.pointer({ target: calendarInput, keys: '[MouseLeft]' });

                // for some reason the placeholder stays as a value before the last backspace (which doesn't happen outside of Storybook)
                // - this is why we need to press the last backspace twice (TODO: investigate)
                await userEvent.keyboard(
                    '{Backspace}{ArrowRight}{Backspace}{ArrowRight}{Backspace}',
                );
                await userEvent.keyboard('{Backspace}');
                await userEvent.click(canvasElement);

                // unless we wait for a bit, the state of the calendar will stay at the last selected date for a moment,
                // which could break following tests (TODO: find a better way to handle this)
                await new Promise((resolve) => { setTimeout(resolve, 1000); });

                await step('[DatePicker] The value was actually deleted', async () => {
                    expect(calendarInput).toHaveDisplayValue('');
                    expect(calendarInput).toHaveValue('');
                });
            },
        );
    },
    deleteInputUsingClearButton: async (context) => {
        // use this at least once to check the behaviour of this delete approach
        const { step, canvasElement } = context;
        const calendarInput = CQ.calendarInput(context);

        let clearInputButton = CQ.clearInputButton(context);

        await step(
            '[DatePicker] Delete the input field value using the clear button (and check the result)',
            async () => {
                // for some reason the placeholder stays as a value after the first clear button click (which doesn't happen outside of Storybook)
                // - this is why we need to click it twice (TODO: investigate)
                await userEvent.click(clearInputButton);
                await new Promise((resolve) => { setTimeout(resolve, 1000); });
                clearInputButton = CQ.clearInputButton(context); // re-query the button, because it might have been re-rendered
                await userEvent.click(clearInputButton);
                await userEvent.click(canvasElement);

                // unless we wait for a bit, the state of the calendar will stay at the last selected date for a moment,
                // which could break following tests (TODO: find a better way to handle this)
                await new Promise((resolve) => { setTimeout(resolve, 1000); });

                await step('[DatePicker] The value was actually deleted', async () => {
                    expect(calendarInput).toHaveDisplayValue('');
                    expect(calendarInput).toHaveValue('');
                });
            },
        );
    },
};

export default DatePickerTesterCI;
