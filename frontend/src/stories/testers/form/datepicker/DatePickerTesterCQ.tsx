import { within, screen } from '@storybook/test';

import { StoryTesterCommonQueries } from '../../StoryTester';

const DatePickerTesterCQ: StoryTesterCommonQueries = {
    canvas: (context) => within(context.canvasElement),
    calendarButton: (context) =>
        within(context.canvasElement)
            .queryAllByRole('button')
            .sort(
                // sort buttons by their x position, so the right-most one comes first (descending order)
                // the right-most button is the calendar button
                (button1, button2) =>
                    button2.getBoundingClientRect().x - button1.getBoundingClientRect().x,
            )[0] as HTMLButtonElement,
    clearInputButton: (context) =>
        within(context.canvasElement)
            .queryAllByRole('button')
            .sort(
                // sort buttons by their x position, so the left-most one comes first (ascending order)
                // the left-most button is the clear button
                (button1, button2) =>
                    button1.getBoundingClientRect().x - button2.getBoundingClientRect().x,
            )[0] as HTMLButtonElement,
    calendarInput: (context) => within(context.canvasElement).getByRole('textbox'),
    calendarShortcutButtons: async (context) =>
        within(await screen.findByRole('dialog'))
            .getAllByRole('button')
            .filter(
                // filter out the buttons that don't have the events icon element (the shortcuts have the icon)
                (button) => button.getElementsByClassName('icon2-events').length > 0,
            ) as HTMLButtonElement[],
};

export default DatePickerTesterCQ;
