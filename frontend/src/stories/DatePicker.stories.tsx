import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';

import FormProvider from '../components5.0/form/FormProvider';
import DatePicker from '../components5.0/form/datePicker/DatePicker';

import { DesignTabUtils } from './designs/DesignTabUtils';
import { DatePickerTester } from './testers/form/datepicker/DatePickerTester';

const meta: Meta<typeof DatePicker> = {
    /* 👇 The title prop is optional.
     * See https://storybook.js.org/docs/react/configure/overview#configure-story-loading
     * to learn how to generate automatic titles
     */
    title: 'FormComponents/DatePicker',
    component: DatePicker,
    tags: ['autodocs'],
    decorators: [
        (Story) => {
            const methods = useForm();
            return (
                <FormProvider methods={methods}>
                    <div style={{ maxWidth: '350px' }}>
                        <Story />
                    </div>
                </FormProvider>
            );
        },
    ],
    args: {
        name: 'DatePicker1',
        label: 'Da<PERSON>',
    },
    parameters: {
        design: DesignTabUtils.makeParams([
            {
                name: 'Filled',
                url: 'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174164&mode=design&t=VXKLExttYC0PEVkS-4',
            },
            {
                name: 'Active',
                url: 'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174167&mode=design&t=VXKLExttYC0PEVkS-4',
            },
        ]),
    },
    play: DatePickerTester.play,
};

export default meta;
type Story = StoryObj<typeof DatePicker>;

export const DatePickerDefault: Story = {
    parameters: {
        design: DesignTabUtils.makeParams([
            {
                name: 'Default',
                url: 'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174161&mode=design&t=VXKLExttYC0PEVkS-4',
            },
            ...meta.parameters?.design,
        ]),
    },
};

export const DatePickerWithShortcutsDays: Story = {
    args: {
        shortcuts: 'shortcutsDays',
    },
    parameters: {
        design: DatePickerDefault.parameters?.design,
    },
};

export const DatePickerWithShortcutsRange: Story = {
    args: {
        shortcuts: 'shortcutsRange',
    },
    parameters: {
        design: DatePickerDefault.parameters?.design,
    },
};

export const DatePickerequired: Story = {
    args: {
        ...DatePickerDefault.args,
        required: true,
    },
    parameters: {
        design: DatePickerDefault.parameters?.design,
    },
};

export const DatePickeTooltip: Story = {
    args: {
        ...DatePickerDefault.args,
        tooltip: 'Tooltip',
    },
    parameters: {
        design: DesignTabUtils.makeParams([
            {
                name: 'Tooltip hidden',
                url: 'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174178&mode=design&t=hlhS9fKSlDZHLvCr-4',
            },
            {
                name: 'Tooltip shown',
                url: 'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174181&mode=design&t=hlhS9fKSlDZHLvCr-4',
            },
            ...DatePickerDefault.parameters?.design,
        ]),
    },
};

export const DatePickeTooltipRequired: Story = {
    args: {
        ...DatePickerDefault.args,
        tooltip: 'Tooltip',
        required: true,
    },
    parameters: {
        design: DatePickeTooltip.parameters?.design,
    },
};

export const DatePickerDisabled: Story = {
    args: {
        ...DatePickerDefault.args,
        disabled: true,
    },
    parameters: {
        design: DesignTabUtils.makeParams(
            'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174175&mode=design&t=hlhS9fKSlDZHLvCr-4',
        ),
    },
};

export const DatePickerError: Story = {
    args: {
        ...DatePickerDefault.args,
        error: true,
        helperText: 'Error helper text',
    },
    parameters: {
        design: DesignTabUtils.makeParams([
            {
                name: 'Error',
                url: 'https://www.figma.com/file/ySO6PZETUWQD7qB0PXbdzB/TAS-UI?type=design&node-id=7052-174172&mode=design&t=hlhS9fKSlDZHLvCr-4',
            },
            ...DatePickerDefault.parameters?.design,
        ]),
    },
};
