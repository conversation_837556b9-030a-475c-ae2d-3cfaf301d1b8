import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';

import Button from '../components5.0/Button';
import ModalDynamicConditions from '../components5.0/Modals/modal-dynamic-conditions/ModalDynamicConditions';
import Form<PERSON>rovider from '../components5.0/form/FormProvider';

const TEMPLATE_TASK: Parameters<typeof ModalDynamicConditions>[0]['templateTask'] = {
    ttask_name: 'Template task',
};

const TEMPLATE_VARIABLES: Parameters<typeof ModalDynamicConditions>[0]['templateVariables'] = [
    {
        id: 1,
        tvar_name: 'Number 1',
        tvar_type: 'N',
    },
    {
        id: 2,
        tvar_name: 'Number 2',
        tvar_type: 'N',
    },
    {
        id: 3,
        tvar_name: 'Text 1',
        tvar_type: 'T',
    },
    {
        id: 4,
        tvar_name: 'Text 2',
        tvar_type: 'T',
    },
];

const meta: Meta<typeof ModalDynamicConditions> = {
    title: 'Modals/ModalDynamicConditions',
    component: ModalDynamicConditions,
    tags: ['autodocs'],
    decorators: [
        (Story, context) => {
            const [isOpen, setIsOpen] = useState(false);
            const methods = useForm();

            return (
                <FormProvider methods={methods}>
                    <Button
                        label="Open"
                        color="primary"
                        variant="contained"
                        onClick={() => {
                            setIsOpen(true);
                        }}
                    />
                    <Story
                        args={{
                            ...context.args,
                            isOpen,
                            onClose: () => {
                                setIsOpen(false);
                            },
                        }}
                    />
                </FormProvider>
            );
        },
    ],
};

export default meta;
type Story = StoryObj<typeof ModalDynamicConditions>;

export const ModalDynamicConditionsDefault: Story = {
    args: {
        templateTask: TEMPLATE_TASK,
        templateVariables: TEMPLATE_VARIABLES,
    },
};
