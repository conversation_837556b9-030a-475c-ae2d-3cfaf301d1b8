import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { Box } from '@mui/material';
import { GridActionsCellItem, GridColDef } from '@mui/x-data-grid-pro';
import { http, HttpResponse } from 'msw';

import { momentFormatDate } from '../common/utils';

import DataGrid from '../components5.0/data-grid/DataGrid';
import MyIcon from '../components5.0/MyIcon';
import DataGridTooltipCell from '../components5.0/data-grid/components/DataGridTooltipCell';
import { DataGridToolbarFilter } from '../components5.0/data-grid/components/DataGridCustomFilters/DataGridToolbarFilter';

const SELECT_OPTIONS = [
    {
        value: 'A',
        label: 'Option A',
    },
    {
        value: 'B',
        label: 'Option B',
    },
    {
        value: 'C',
        label: 'Option C',
    },
];

const COLUMNS: (GridColDef & Record<string, any>)[] = [
    {
        field: 'id',
        columnVisibility: false,
    },
    {
        field: 'date',
        headerName: 'Date',
        type: 'date',
        valueFormatter: (value: any) => value && momentFormatDate(value, true, false),
    },
    {
        field: 'date_time',
        headerName: 'DateTime',
        type: 'dateTime',
        valueFormatter: (value: any) => value && momentFormatDate(value, false, true),
    },
    {
        field: 'number',
        headerName: 'Number',
        type: 'number',
    },
    {
        field: 'select',
        headerName: 'Select',
        type: 'singleSelect',
        valueOptions: SELECT_OPTIONS,
    },
    {
        field: 'string',
        headerName: 'String',
        type: 'string',
    },
    {
        field: 'query_string',
        headerName: 'Query string',
        type: 'string',
        renderCell: DataGridTooltipCell,
        width: 400,
    },
    {
        field: 'actions',
        type: 'actions',
        width: 60,
        maxWidth: 60,
        getActions: (params: any) => [
            <GridActionsCellItem icon={<MyIcon icon="signal" />} label="Action 1" />,
            <GridActionsCellItem icon={<MyIcon icon="view" />} label="Action 2" showInMenu />,
            <GridActionsCellItem icon={<MyIcon icon="bell" />} label="Action 3" showInMenu />,
            <GridActionsCellItem icon={<MyIcon icon="trash" />} label="Action 4" showInMenu />,
        ],
        resizable: false,
        hideable: false,
    },
];

const ROWS = [
    {
        id: 1,
        date: '2021-01-01T12:00:00.000Z',
        date_time: '2021-01-01T12:00:00.000Z',
        number: 1,
        select: 'A',
        string: 'String 1',
        query_string: null,
    },
    {
        id: 2,
        date: '2021-02-01T13:00:00.000Z',
        date_time: '2021-02-01T13:00:00.000Z',
        number: 2,
        select: 'B',
        string: 'String 2',
        query_string: null,
    },
    {
        id: 3,
        date: '2021-03-01T14:00:00.000Z',
        date_time: '2021-03-01T14:00:00.000Z',
        number: 3,
        select: 'C',
        string: 'String 3',
        query_string: null,
    },
    {
        id: 4,
        date: '2021-04-01T15:00:00.000Z',
        date_time: '2021-04-01T15:00:00.000Z',
        number: 4,
        select: 'A',
        string: 'String 4',
        query_string: null,
    },
    {
        id: 5,
        date: '2021-05-01T16:00:00.000Z',
        date_time: '2021-05-01T16:00:00.000Z',
        number: 5,
        select: 'B',
        string: 'String 5',
        query_string: null,
    },
    {
        id: 6,
        date: '2021-06-01T17:00:00.000Z',
        date_time: '2021-06-01T17:00:00.000Z',
        number: 6,
        select: 'C',
        string: 'String 6',
        query_string: null,
    },
    {
        id: 7,
        date: '2021-07-01T18:00:00.000Z',
        date_time: '2021-07-01T18:00:00.000Z',
        number: 7,
        select: 'A',
        string: 'String 7',
        query_string: null,
    },
    {
        id: 8,
        date: '2021-08-01T19:00:00',
        date_time: '2021-08-01T19:00:00',
        number: 8,
        select: 'B',
        string: 'String 8',
        query_string: null,
    },
    {
        id: 9,
        date: '2021-09-01T20:00:00',
        date_time: '2021-09-01T20:00:00',
        number: 9,
        select: 'C',
        string: 'String 9',
        query_string: null,
    },
    {
        id: 10,
        date: '2021-10-01T21:00:00',
        date_time: '2021-10-01T21:00:00',
        number: 10,
        select: 'A',
        string: 'String 10',
        query_string: null,
    },
];

let prevTotalCount = 0;

const meta: Meta<typeof DataGrid> = {
    /* 👇 The title prop is optional.
     * See https://storybook.js.org/docs/react/configure/overview#configure-story-loading
     * to learn how to generate automatic titles
     */
    title: 'DataGrids/DataGrid',
    component: DataGrid,
    tags: ['autodocs'],
    decorators: [
        (Story) => {
            return (
                <Box
                    sx={{
                        height: '95vh',
                        width: '95vw',
                        mx: 'auto',
                        py: '0.75rem',
                        px: '0.75rem',
                        borderRadius: '0.75rem',
                        backgroundColor: 'white',
                    }}
                >
                    <Story />
                </Box>
            );
        },
    ],
    args: {
        columns: COLUMNS,
        apiUrl: '/storybook-datagrid-rows',
        alwaysSort: { field: 'id', sort: 'desc' },
    },
    parameters: {
        msw: {
            handlers: [
                http.get('http://localhost:8001/storybook-datagrid-rows', (info) => {
                    const queryString = info.request.url.split('?')[1] || null;
                    const onlyCount = queryString && queryString.includes('only_count=true');

                    const randomRows = [];
                    if (!onlyCount) {
                        for (let i = 0; i < Math.round(Math.random() * 10 + 10); i += 1) {
                            randomRows.push({
                                ...ROWS[Math.floor(Math.random() * ROWS.length)],
                                id: i + 1,
                                query_string: queryString,
                            });
                        }

                        prevTotalCount = randomRows.length;
                    }

                    return HttpResponse.json({
                        ...(onlyCount ? {} : { items: randomRows }),
                        total_count: onlyCount ? prevTotalCount : randomRows.length,
                    });
                }),
            ],
        },
    },
};

export default meta;
type Story = StoryObj<typeof DataGrid>;

export const DataGridDefault: Story = {
    args: {
        id: 'DataGridDefault',
    },
};

// export const DataGridWithToolbarFilter: Story = {
//     args: {
//         id: 'DataGridWithToolbarFilter',
//     },
// };
