import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';
import { repeat } from 'lodash';

import Button from '../../components5.0/Button';
import ModalNews from '../../components5.0/Modals/ModalNews';
import FormProvider from '../../components5.0/form/FormProvider';

const POST: Parameters<typeof ModalNews>[0]['posts'][0] = {
    post_title: 'Test post',
    post_content: 'Hello <b>world! </b><em>Nice to see you</em><b>!</b>',
    post_tags: [
        { tag: 1, tag_name: 'Tag 1' },
        { tag: 2, tag_name: 'Tag 2' },
        { tag: 3, tag_name: 'Tag 3' },
    ],
    post_publication_date: '2025-02-19T23:00:00.000Z',
    post_publication_end_date: '2025-02-19T23:00:00.000Z',
    post_phone: '998494866',
    post_email: '<EMAIL>',
    post_custom_url: 'google.com',
};

const POST2: Parameters<typeof ModalNews>[0]['posts'][0] = {
    post_title: 'Random post',
    post_content: 'Hello <b>universe! </b><em>Great to meet you</em><b>!</b>',
    post_tags: [
        { tag: 4, tag_name: 'Tag 4' },
        { tag: 5, tag_name: 'Tag 5' },
    ],
    post_publication_date: '2025-03-20T12:00:00.000Z',
    post_publication_end_date: '2025-03-21T12:00:00.000Z',
    post_phone: '123456789',
    post_email: null,
    post_custom_url: 'example.com',
};

const POST3: Parameters<typeof ModalNews>[0]['posts'][0] = {
    post_title: 'PostPostPost',
    post_content: repeat('Hello <b>universe! </b><em>Great to meet you</em><b>!</b><br>', 50),
    post_tags: [
        { tag: 4, tag_name: 'Tag A' },
        { tag: 5, tag_name: 'Tag B' },
        { tag: 6, tag_name: 'Tag C' },
        { tag: 6, tag_name: 'Tag D' },
    ],
    post_publication_date: '2025-03-20T12:00:00.000Z',
    post_publication_end_date: '2025-03-21T12:00:00.000Z',
    post_phone: '123456789',
    post_custom_url: null,
    post_email: null,
};

const POST4: Parameters<typeof ModalNews>[0]['posts'][0] = {
    post_title: 'Mostly empty post',
    post_content: repeat('Hello <b>world! </b><em>Great to meet you</em><b>!</b><br>', 5),
    post_tags: [],
    post_publication_date: '2025-03-20T12:00:00.000Z',
    post_publication_end_date: null,
    post_phone: null,
    post_custom_url: null,
    post_email: null,
};

const meta: Meta<typeof ModalNews> = {
    title: 'News/ModalNews',
    component: ModalNews,
    tags: ['autodocs'],
    decorators: [
        (Story, context) => {
            const [isOpen, setIsOpen] = useState(false);
            const methods = useForm();

            return (
                <FormProvider methods={methods}>
                    <Button
                        label="Open"
                        color="primary"
                        variant="contained"
                        onClick={() => {
                            setIsOpen(true);
                        }}
                    />
                    <Story
                        args={{
                            ...context.args,
                            isOpen,
                            onClose: () => {
                                setIsOpen(false);
                            },
                        }}
                    />
                </FormProvider>
            );
        },
    ],
    args: {
        isOpen: true,
    },
};

export default meta;
type Story = StoryObj<typeof ModalNews>;

export const SinglePost: Story = {
    args: {
        posts: [POST],
    },
};

export const MultiplePosts: Story = {
    args: {
        posts: [POST, POST2, POST3, POST4, POST],
    },
};
