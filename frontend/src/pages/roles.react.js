import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import {
    guid,
    checkUserParams,
    sortArray,
    sortArrayBy,
    downloadEncoded,
} from '../common/utils';
import TableApi from '../components/table/table.api';
import { saveAs } from '../../assets/libs/filesaver';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';

import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import TabsButtonsTable from '../components/tabs/tabsButtonsTable.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import TabsButton from '../components/tabs/tabsButton.react';
import TabsButtonFilter from '../components/tabs/tabsButtonFilter.react';
import TreeTable from '../components/tree/tree.react';
import Table from '../components/table/table.react';
import Column from '../components/table/column.react';
import ConfirmModal from './modals/confirmModal.react';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import CompetencesTab from './competencesTab.react';
import CompetenceRulesTab from './competenceRulesTab.react';
import TabsButtonMore from '../components/tabs/tabsButtonMore.react';

class Roles extends React.Component {

    constructor(props) {
        super();
        this.state = {
            deleteModalIsOpen: false,
            roleId: null,
            nodeId: null,
            parentId: null,
        };

        this.handleTableAdd = this.handleTableAdd.bind(this);
        this.handleEdit = this.handleEdit.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.openDeleteModal = this.openDeleteModal.bind(this);
        this.deleteRole = this.deleteRole.bind(this);
        this.closeDeleteModal = this.closeDeleteModal.bind(this);
        this.loadTreeData = this.loadTreeData.bind(this);
        this.treeClick = this.treeClick.bind(this);
        this.copyRole = this.copyRole.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin
        PageRights.checkUserRights([-1]);
    }

    componentDidMount() {
        BreadcrumbActions.changeBread({ name: 'Role', to: '/roles' });

        i18next.on('languageChanged', (options) => {
            BreadcrumbActions.changeBread.defer({ name: 'Role', to: '/roles' });
        });
    }

    handleTableAdd(e) {
        e.preventDefault();
        this.props.history.push('/roles/role/new');
    }

    handleEdit(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id = this.refs.tableRef.state.selectedRow.rowId || rowId;
        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/roles/role/${id}`;
            }
            this.props.history.push(`/roles/role/${id}`);
        }
    }

    handleKeyDown(event) {
        if (event.which === 46) {
            // delete key
            this.openDeleteModal(event);
        }
    }

    openDeleteModal(e) {
        e.preventDefault();
        this.setState({
            deleteModalIsOpen: true,
            roleId: this.refs.tableRef.state.selectedRow.rowId,
        });
    }

    deleteRole() {
        const id = this.state.roleId;
        this.closeDeleteModal();

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrRoleDeleting'),
        });

        const treeComp = this.refs.tree;
        const table = this.refs.tableRef;

        ApiRequest.delete(`/roles/${id}`)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrRoleDeleted'),
                });

                ApiRequest.get(`/roles/structure?limit=${config.restLimit}`)
                    .then((payload) => {
                        const treeArr = [];
                        for (const key in payload.items) {
                            if (payload.items.hasOwnProperty(key)) {
                                treeArr.push(key);
                            }
                        }

                        let node;
                        if (
                            typeof this.state.parentId !== 'undefined' &&
                            this.state.parentId !== null
                        ) {
                            node = this.state.parentId;
                        } else {
                            node = this.state.nodeId;
                        }
                        const row = table.getNextRow();
                        table.handleClickRow(row);

                        if (treeArr.indexOf(node) > -1) {
                            treeComp.saveTreeState(this.state.parentId);
                        } else {
                            treeComp.saveTreeState('All');
                        }

                        treeComp.updateTree();
                        table.handleFetchRows();
                    })
                    .catch((errorMessage) => {
                        AlertsActions.addAlert({
                            type: 'alert',
                            message: i18next.t('alrTreeDataFailed'),
                            reload: true,
                            serverError: errorMessage,
                        });
                    });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrRoleDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeDeleteModal() {
        this.setState({ deleteModalIsOpen: false });
    }

    loadTreeData(tree) {
        if (tree.state.treeState != null) {
            this.setState({ nodeId: tree.state.treeState.selected_node[0] });
        }

        ApiRequest.get(`/roles/structure?limit=${config.restLimit}`)
            .then((payload) => {
                const sortedNodes = sortArray(Object.keys(payload.items));
                const uncategorizedIndex = _.findIndex(
                    sortedNodes,
                    (role) => role === 'Nezařazeno',
                );
                sortedNodes.splice(uncategorizedIndex, 1);
                sortedNodes.unshift('Nezařazeno');
                const systemRolesIndex = _.findIndex(
                    sortedNodes,
                    (role) => role === 'System',
                );
                sortedNodes.splice(systemRolesIndex, 1);
                sortedNodes.unshift('System');

                const obj = payload.items;
                const arr = [];
                for (let i = 0; i < sortedNodes.length; i++) {
                    const newObj = {};
                    const key = sortedNodes[i];
                    if (obj.hasOwnProperty(key)) {
                        newObj.label = key;
                        newObj.children = sortArrayBy(obj[key], 'role_name');

                        arr.push(newObj);
                    }
                }

                arr.forEach((item) => {
                    item.id = item.label;
                    item.children.forEach((child) => {
                        child.label = child.role_name;
                    });
                });

                const wrapArr = [];
                wrapArr.push({
                    label: i18next.t('all'),
                    id: 'All',
                    is_open: true,
                    selected: true,
                    children: arr,
                });

                tree.loadTree(wrapArr);

                checkUserParams([], 'columnsWidths', 'rolesRole', true);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTreeDataFailed'),
                    serverError: errorMessage,
                });
            });
    }

    treeClick(nodeId, parentId) {
        this.setState({
            nodeId: nodeId,
            parentId: parentId,
        });
    }

    /* treeEditable(tree) {
        if (typeof tree.state.nodeId == 'string') {
            tree.setActiveEditButtons(true);
        } else {
            tree.setActiveEditButtons(false);
        }
    } */

    copyRole(e) {
        e.preventDefault();

        const table = this.refs.tableRef;
        const alertId = guid();

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrCopying'),
        });

        ApiRequest.post(
            `/roles/${table.state.selectedRow.rowId}/clone`,
            JSON.stringify({}),
        )
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('copied'),
                    fadeOut: true,
                });

                if (!isNaN(payload.id) && payload.id != null) {
                    this.props.history.push(`/roles/role/${payload.id}`);
                }
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrCopyFailed'),
                    serverError: errorMessage,
                });
            });
    }

    render() {
        const { nodeId } = this.state;
        let tableFilter;
        if (typeof nodeId === 'string') {
            if (nodeId == 'Nezařazeno') {
                tableFilter = `(role_category<eq>"${nodeId}"<or>role_category<isn>)`;
            } else if (nodeId == 'All') {
                tableFilter = '';
            } else {
                tableFilter = `role_category<eq>"${nodeId}"`;
            }
        } else if (typeof nodeId === 'number') {
            tableFilter = `id<eq>"${nodeId}"`;
        }

        return (
            <DocumentTitle title={i18next.t('roles')}>
                <TabsWrapper>
                    <Heading title={i18next.t('roles')} />
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="roles"
                            title="Role"
                            name="tabRoles"
                            tabLink="/roles"
                        >
                            <TabsButtonsTable
                                key={`buttonsRolesRole${nodeId}`}
                                boundTableName={`rolesRole${nodeId}`}
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-add-1"
                                    onClick={this.handleTableAdd}
                                    isActive
                                    tooltipCode="ttAdd"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEdit}
                                    tooltipCode="ttEdit"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="drop"
                                    icon="icon-bin-2"
                                    onClick={this.openDeleteModal}
                                    tooltipCode="ttDel"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                                <TabsButton
                                    key="copy"
                                    icon="icon-files-4"
                                    tooltipCode="ttCopyRole"
                                    onClick={this.copyRole}
                                    enableOnRow={(row) => {
                                        return row.id > -1;
                                    }}
                                >
                                    {i18next.t('copy')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonRolesMore"
                                    boundTableName={`rolesRole${nodeId}`}
                                >
                                    <TabsButton
                                        key="import"
                                        icon="icon-download-10"
                                        onClick={() => {}}
                                        hideTitle={false}
                                        isActive
                                    >
                                        {i18next.t('import')}
                                    </TabsButton>
                                    <TabsButton
                                        key="export"
                                        icon="icon-upload-10"
                                        hideTitle={false}
                                        onClick={() => {}}
                                        isActive
                                    >
                                        {i18next.t('export')}
                                    </TabsButton>
                                </TabsButtonMore>
                                <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    groupType="roles"
                                    parent={this}
                                    tableRef="tableRef"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                            <TreeTable
                                name="treeRoles"
                                dragAndDrop={false}
                                dataUrl="/users/role/"
                                treeClick={this.treeClick}
                                /* editable={this.treeEditable} */ loadTreeData={
                                    this.loadTreeData
                                }
                                ref="tree"
                            >
                                <Table
                                    key={`rolesRole${nodeId}`}
                                    ref="tableRef"
                                    name={`rolesRole${nodeId}`}
                                    apiUrl="/roles"
                                    defaultFilter={tableFilter}
                                    onDoubleClick={this.handleEdit}
                                    canOpenNewTab
                                    onKeyDown={this.handleKeyDown}
                                    boundTreeName="treeRoles"
                                    defaultSort={{
                                        column: 'role_name',
                                        order: 'asc',
                                    }}
                                    columnsWidthsGroup="rolesGroup"
                                    canLoad={
                                        typeof nodeId === 'string' ||
                                        typeof nodeId === 'number'
                                    }
                                >
                                    <Column
                                        title={i18next.t('id')}
                                        name="id"
                                        type="text"
                                        className="align-right"
                                        width="80"
                                    />
                                    <Column
                                        title={i18next.t('roleName')}
                                        name="role_name"
                                        type="text"
                                    />
                                    <Column
                                        title={i18next.t('category')}
                                        name="role_category"
                                        type="text"
                                    />
                                    <Column
                                        title={i18next.t('description')}
                                        name="role_note"
                                        type="text"
                                    />
                                </Table>
                            </TreeTable>
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="competences"
                            title={i18next.t('competences')}
                            tabName="competences"
                            tabLink="/roles/competences"
                            name="tabCompetences"
                        >
                            <CompetencesTab />
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="competencesPrescription"
                            title={i18next.t('competenceRules')}
                            tabName="competence-rules"
                            tabLink="/roles/competence-rules"
                            name="tabCompetencesRules"
                        >
                            <CompetenceRulesTab />
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        isOpen={this.state.deleteModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(role)',
                        })}
                        onClose={this.closeDeleteModal}
                        onConfirm={this.deleteRole}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

Roles.displayName = 'Roles';

Roles.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default Roles;
