import i18next from 'i18next';
import DocumentTitle from 'react-document-title';
import PropTypes from 'prop-types';
import { guid, checkLangMutation, checkUserParams } from '../common/utils';
import browserHistory from '../common/history';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';

import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import TabsButtonsTable from '../components/tabs/tabsButtonsTable.react';
import TabsButtonMore from '../components/tabs/tabsButtonMore.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import TabsButton from '../components/tabs/tabsButton.react';
import TabsButtonFilter from '../components/tabs/tabsButtonFilter.react';
import Table from '../components/table/table.react';
import Column from '../components/table/column.react';
import TemplatesStatusModal from './modals/templatesStatusModal.react';
import ConfirmModal from './modals/confirmModal.react';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import { saveAs } from '../../assets/libs/filesaver';
import ImportTemplateModal from './modals/importTemplateModal.react';
import TemplateCopyImportActions from '../flux/templateCopyImport.actions';

class Templates extends React.Component {

    constructor(props) {
        super();
        this.state = {
            statusModalIsOpen: false,
            templateId: null,
            versionId: null,
            exportIdsArr: [],
            deleteModalIsOpen: false,
            apiUrl: '/template-processes/',
            nodeId: null,
            tableRef: null,
            exportModalIsOpen: false,
            importModalIsOpen: false,
            restoreModalIsOpen: false,
            exportAll: false,
        };

        this.handleTableAdd = this.handleTableAdd.bind(this);
        this.handleEdit = this.handleEdit.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.openStatusModal = this.openStatusModal.bind(this);
        this.openDeleteModal = this.openDeleteModal.bind(this);
        this.deleteTemplate = this.deleteTemplate.bind(this);
        this.closeStatusModal = this.closeStatusModal.bind(this);
        this.closeDeleteModal = this.closeDeleteModal.bind(this);
        this.toCopyTemplate = this.toCopyTemplate.bind(this);
        this.openExportTemplateModal = this.openExportTemplateModal.bind(this);
        this.closeExportModal = this.closeExportModal.bind(this);
        this.exportTemplate = this.exportTemplate.bind(this);
        this.openImportTemplateModal = this.openImportTemplateModal.bind(this);
        this.closeImportModal = this.closeImportModal.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        // set breadcrumb
        BreadcrumbActions.changeBread({ name: i18next.t('templates') });

        i18next.on('languageChanged', (options) => {
            BreadcrumbActions.changeBread.defer({
                name: i18next.t('templates'),
            });
        });
    }

    handleTableAdd(e) {
        e.preventDefault();

        this.props.history.push({
            pathname: '/templates/template/new',
            state: {
                breadPrevPath: location.pathname,
                closePrevPath: location.pathname,
            },
        });
    }

    handleEdit(tableRef, e, rowId, row, inNewTab) {
        e.preventDefault();

        const id = rowId
            ? rowId.tproc_id
            : this.refs[tableRef].state.selectedRow.rowId.tproc_id;
        const version = rowId
            ? rowId.tproc_version
            : this.refs[tableRef].state.selectedRow.rowId.tproc_version;

        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/templates/template/${id}/${version}`;
            }
            this.props.history.push({
                pathname: `/templates/template/${id}/${version}`,
                state: {
                    breadPrevPath: location.pathname,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    handleKeyDown(tableRef, event) {
        if (event.which === 46) {
            // delete key
            this.openDeleteModal(tableRef, event);
        }
    }

    openStatusModal(tableRef, e) {
        e.preventDefault();
        this.setState({
            statusModalIsOpen: true,
            templateId: this.refs[tableRef].state.selectedRow.rowId.tproc_id,
            versionId:
                this.refs[tableRef].state.selectedRow.rowId.tproc_version,
            tableRef: tableRef,
        });
    }

    openDeleteModal(tableRef, e) {
        e.preventDefault();
        this.setState({
            deleteModalIsOpen: true,
            templateId: this.refs[tableRef].state.selectedRow.rowId.tproc_id,
            versionId:
                this.refs[tableRef].state.selectedRow.rowId.tproc_version,
        });
    }

    openRestoreModal = (e) => {
        e.preventDefault();
        this.setState({
            restoreModalIsOpen: true,
            templateId: this.refs.delTemplates.state.selectedRow.rowId.tproc_id,
            versionId:
                this.refs.delTemplates.state.selectedRow.rowId.tproc_version,
        });
    };

    deleteTemplate() {
        const { templateId, versionId } = this.state;
        this.closeDeleteModal();
        const table = this.refs.allTemplates;
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrDeleting'),
        });

        ApiRequest.delete(
            `/template-processes/${templateId}/${versionId}`,
            JSON.stringify({}),
        ) // empty object due to IE
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrTempDeleted'),
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrTempDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    restoreTemplate = () => {
        const { templateId, versionId } = this.state;
        this.closeRestoreModal();
        const table = this.refs.delTemplates;
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = {
            tproc_id: templateId,
            tproc_version: versionId,
            tproc_status: 'D', // developed
        };

        ApiRequest.post('/template-processes', JSON.stringify(obj))
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrTempRestored'),
                });

                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrRestorTemplFailed'),
                    serverError: errorMessage,
                });
            });
    };

    closeStatusModal() {
        this.setState({ statusModalIsOpen: false });
    }

    closeDeleteModal() {
        this.setState({ deleteModalIsOpen: false });
    }

    closeRestoreModal = () => {
        this.setState({ restoreModalIsOpen: false });
    };

    toCopyTemplate(tableRef, e) {
        e.preventDefault();

        const id = this.refs[tableRef].state.selectedRow.rowId.tproc_id;
        const version =
            this.refs[tableRef].state.selectedRow.rowId.tproc_version;

        if (!isNaN(id) && id != null) {
            this.props.history.push({
                pathname: `/templates/copy/${id}/${version}`,
                state: {
                    breadPrevPath: location.pathname,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    openExportTemplateModal(tableRef, exportAll, e) {
        e.preventDefault();

        const tableState = this.refs[tableRef].state;

        if (exportAll) {
            // get id of all templates in table
            const filterArr = [];

            // modal filter
            if (tableState.activeFilters.hasOwnProperty('filtersparams')) {
                filterArr.push(
                    encodeURIComponent(tableState.activeFilters.filtersparams),
                );
            }

            // inline filter
            if (!_.isEmpty(tableState.filterTexts)) {
                filterArr.push(encodeURIComponent(tableState.filterTexts));
            }

            const filterStr = filterArr.join('<and>');
            const url = !_.isEmpty(filterStr)
                ? `${tableState.apiUrl}?filter=${filterStr}`
                : tableState.apiUrl;

            ApiRequest.get(
                `${url + (!_.isEmpty(filterStr) ? '&' : '?')}limit=${config.restLimit}`,
            )
                .then((payload) => {
                    const exportIdsArr = payload.items.map((item) => {
                        return {
                            tproc_id: item.id.tproc_id,
                            tproc_version: item.id.tproc_version,
                        };
                    });

                    if (!_.isEmpty(exportIdsArr)) {
                        this.setState({
                            exportModalIsOpen: true,
                            exportIdsArr: exportIdsArr,
                            exportAll: true,
                        });
                    } else {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18next.t('noTemplatesToExport'),
                            show: true,
                            allowCountdown: true,
                        });
                    }
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrTempsLoadFailed'),
                        serverError: errorMessage,
                    });
                });
        } else {
            const id = tableState.selectedRow.rowId.tproc_id;
            const version = tableState.selectedRow.rowId.tproc_version;

            this.setState({
                exportModalIsOpen: true,
                templateId: !isNaN(id) && id != null ? id : null,
                versionId: version,
                exportAll: false,
            });
        }
    }

    closeExportModal() {
        this.setState({ exportModalIsOpen: false });
    }

    exportTemplate() {
        this.closeExportModal();

        const { templateId, versionId, exportIdsArr } = this.state;
        const alertId = guid();

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrExportPreparing'),
        });

        let postArr = [{ tproc_id: templateId, tproc_version: versionId }];

        if (this.state.exportAll) {
            postArr = exportIdsArr;
        }

        ApiRequest.post('/template-processes/export', JSON.stringify(postArr))
            .then((payload) => {
                const blob = new Blob([JSON.stringify(payload)], {
                    type: 'text/plain;charset=utf-8',
                });

                if (this.state.exportAll) {
                    saveAs(blob, 'templates.tpl');
                } else {
                    const table = this.refs.allTemplates;
                    let fileName = _.find(
                        table.state.items,
                        ['id.tproc_id', templateId],
                    ).tproc_name;
                    fileName = fileName.replace(/[<>:"\/\\|?*%]+/g, '-'); // dealing with special characters
                    saveAs(blob, `${fileName}.tpl`);
                }

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrExportCompleted'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrExportFailed'),
                    serverError: errorMessage,
                });
            });
    }

    openImportTemplateModal(e) {
        e.preventDefault();
        this.setState({ importModalIsOpen: true });
    }

    closeImportModal() {
        this.setState({ importModalIsOpen: false });
    }

    onLoad = (fileContent) => {
        TemplateCopyImportActions.setImportDataArr(JSON.parse(fileContent));

        browserHistory.push({
            pathname: '/templates/import',
            state: {
                breadPrevPath: location.pathname,
                closePrevPath: location.pathname,
            },
        });
    };

    render() {
        const choicesAllTemplates = [
            {
                value: 'A',
                title: i18next.t('operating'),
                className: 'icon icon-flag-1-1 green',
            },
            {
                value: 'N',
                title: i18next.t('paused'),
                className: 'icon icon-flag-1-1 red',
            },
            {
                value: 'D',
                title: i18next.t('developed'),
                className: 'icon icon-pencil-2 green',
            },
            {
                value: 'T',
                title: i18next.t('tested'),
                className: 'icon icon-flag-1-1 red',
            },
        ];

        const choicesDelTemplates = [
            {
                value: 'E',
                title: i18next.t('deletedShe'),
                className: 'icon icon-delete-2 red',
            },
        ];

        return (
            <DocumentTitle title={i18next.t('templates')}>
                <TabsWrapper>
                    <Heading title={i18next.t('templates')} />
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="allTemplates"
                            title={i18next.t('activePl')}
                            tabLink="/templates"
                            name="tabAllTemplates"
                        >
                            <TabsButtonsTable
                                key="buttonsAllTemplates"
                                boundTableName="allTemplates"
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-add-1"
                                    onClick={this.handleTableAdd}
                                    isActive
                                    tooltipCode="ttAddTemp"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEdit.bind(
                                        null,
                                        'allTemplates',
                                    )}
                                    tooltipCode="ttEditTemp"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="status"
                                    icon="icon-flag-1"
                                    onClick={this.openStatusModal.bind(
                                        null,
                                        'allTemplates',
                                    )}
                                    tooltipCode="ttStatusTemp"
                                >
                                    {i18next.t('status')}
                                </TabsButton>
                                <TabsButton
                                    key="drop"
                                    icon="icon-bin-2"
                                    onClick={this.openDeleteModal.bind(
                                        null,
                                        'allTemplates',
                                    )}
                                    tooltipCode="ttDelTemp"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonsTemplatesActiveMore"
                                    boundTableName="allTemplates"
                                >
                                    <TabsButton
                                        key="copy"
                                        icon="icon-files-4"
                                        tooltipCode="ttCopyTemp"
                                        hideTitle={false}
                                        onClick={this.toCopyTemplate.bind(
                                            null,
                                            'allTemplates',
                                        )}
                                    >
                                        {i18next.t('copy')}
                                    </TabsButton>
                                    <TabsButton
                                        key="import"
                                        icon="icon-download-10"
                                        tooltipCode="ttTemplateImport"
                                        onClick={this.openImportTemplateModal}
                                        hideTitle={false}
                                        isActive
                                    >
                                        {i18next.t('import')}
                                    </TabsButton>
                                    <TabsButton
                                        key="export"
                                        icon="icon-upload-10"
                                        hideTitle={false}
                                        tooltipCode="ttTemplatesExport"
                                        onClick={this.openExportTemplateModal.bind(
                                            null,
                                            'allTemplates',
                                            false,
                                        )}
                                    >
                                        {i18next.t('export')}
                                    </TabsButton>
                                    <TabsButton
                                        key="exportAll"
                                        icon="icon-upload-10"
                                        hideTitle={false}
                                        tooltipCode="ttTemplatesExportAll"
                                        onClick={this.openExportTemplateModal.bind(
                                            null,
                                            'allTemplates',
                                            true,
                                        )}
                                        isActive
                                    >
                                        {i18next.t('exportAll')}
                                    </TabsButton>
                                </TabsButtonMore>
                                <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    groupType="templates"
                                    tooltipCode="ttFilterTemp"
                                    parent={this}
                                    tableRef="allTemplates"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                            <Table
                                key="allTemplates"
                                ref="allTemplates"
                                name="allTemplates"
                                apiUrl="/template-processes"
                                onDoubleClick={this.handleEdit.bind(
                                    null,
                                    'allTemplates',
                                )}
                                canOpenNewTab
                                defaultSort={{
                                    column: 'tproc_name',
                                    order: 'asc',
                                }}
                                defaultSortLangMutation
                                onKeyDown={this.handleKeyDown.bind(
                                    null,
                                    'allTemplates',
                                )}
                                orderId="tproc_id"
                            >
                                <Column
                                    title={i18next.t('templateName')}
                                    name="tproc_name"
                                    type="text"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'tproc_name',
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('version')}
                                    name="tproc_version"
                                    type="number"
                                    width="70"
                                />
                                <Column
                                    title={i18next.t('description')}
                                    name="tproc_description"
                                    type="text"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'tproc_description',
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('status')}
                                    name="tproc_status"
                                    type="image"
                                    filterChoices={choicesAllTemplates}
                                    className="center"
                                    width="120"
                                />
                                <Column
                                    title={i18next.t('changedBy')}
                                    name="tproc_last_changed_by_user"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('changedWhen')}
                                    name="tproc_last_changed_date"
                                    type="datetime"
                                    width="135"
                                />
                            </Table>
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="delTemplates"
                            title={i18next.t('templateDeleted')}
                            tabName="deleted"
                            tabLink="/templates/deleted"
                            name="tabDelTemplates"
                        >
                            <TabsButtonsTable
                                key="buttonsDelTemplates"
                                boundTableName="delTemplates"
                            >
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEdit.bind(
                                        null,
                                        'delTemplates',
                                    )}
                                    tooltipCode="ttEditTemp"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="restore"
                                    icon="icon-play-2"
                                    onClick={this.openRestoreModal}
                                    tooltipCode="ttRestoreTemp"
                                >
                                    {i18next.t('restore')}
                                </TabsButton>
                                <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    groupType="templates"
                                    tooltipCode="ttFilterTemp"
                                    parent={this}
                                    tableRef="delTemplates"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                            <Table
                                key="delTemplates"
                                ref="delTemplates"
                                name="delTemplates"
                                apiUrl="/template-processes/deleted"
                                onDoubleClick={this.handleEdit.bind(
                                    null,
                                    'delTemplates',
                                )}
                                canOpenNewTab
                                defaultSort={{
                                    column: 'tproc_name',
                                    order: 'asc',
                                }}
                                defaultSortLangMutation
                                orderId="tproc_id"
                                onKeyDown={this.handleKeyDown.bind(
                                    null,
                                    'delTemplates',
                                )}
                            >
                                <Column
                                    title={i18next.t('templateName')}
                                    name="tproc_name"
                                    type="text"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'tproc_name',
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('version')}
                                    name="tproc_version"
                                    type="number"
                                    width="70"
                                />
                                <Column
                                    title={i18next.t('description')}
                                    name="tproc_description"
                                    type="text"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'tproc_description',
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('status')}
                                    name="tproc_status"
                                    type="image"
                                    filterChoices={choicesDelTemplates}
                                    className="center"
                                    width="120"
                                />
                                <Column
                                    title={i18next.t('changedBy')}
                                    name="tproc_last_changed_by_user"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('changedWhen')}
                                    name="tproc_last_changed_date"
                                    type="datetime"
                                    width="135"
                                />
                            </Table>
                        </Tabs.Tab>
                    </Tabs>
                    {this.state.statusModalIsOpen && (
                        <TemplatesStatusModal
                            templateId={this.state.templateId}
                            versionId={this.state.versionId}
                            isOpen={this.state.statusModalIsOpen}
                            width="tiny"
                            onClose={this.closeStatusModal}
                            parent={this}
                            tableRef={this.state.tableRef}
                        />
                    )}
                    <ConfirmModal
                        isOpen={this.state.deleteModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(tempVar)',
                        })}
                        onClose={this.closeDeleteModal}
                        onConfirm={this.deleteTemplate}
                    />
                    <ConfirmModal
                        isOpen={this.state.restoreModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmRestoreDialog', {
                            variable: '$t(tempVar)',
                        })}
                        onClose={this.closeRestoreModal}
                        onConfirm={this.restoreTemplate}
                    />

                    <ConfirmModal
                        isOpen={this.state.exportModalIsOpen}
                        width="tiny"
                        text={i18next.t(
                            this.state.exportAll
                                ? 'confirmExportAllTempl'
                                : 'confirmExportSelTempl',
                        )}
                        onClose={this.closeExportModal}
                        onConfirm={this.exportTemplate}
                    />

                    <ImportTemplateModal
                        isOpen={this.state.importModalIsOpen}
                        onClose={this.closeImportModal}
                        onLoad={this.onLoad}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

Templates.displayName = 'Templates';

Templates.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default Templates;
