import i18next from 'i18next';
import PropTypes from 'prop-types';
import { JSONTree } from 'react-json-tree';
import React from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import { momentFormatDate } from '../../common/utils';

const ProcessingQueueJobDetailModal = (props) => {
    const closeModal = (e) => {
        e.preventDefault();
        props.onClose();
    };

    const data = { ...props.data };

    if (data.timestamp) {
        data.timestampDate = momentFormatDate(data.timestamp, false, true);
    }

    if (data.finishedOn) {
        data.finishedOnDate = momentFormatDate(data.finishedOn, false, true);
    }

    if (data.processedOn) {
        data.processedOnDate = momentFormatDate(data.processedOn, false, true);
    }

    if (data.processedOn && data.finishedOn) {
        data.jobDurationRaw = data.finishedOn - data.processedOn;

        const formatter = new Intl.NumberFormat(i18next.language);
        data.jobDuration = `${formatter.format(data.jobDurationRaw / 1000)} s`;
    }

    if (data.timestamp && data.finishedOn) {
        data.totalDurationRaw = data.finishedOn - data.timestamp;

        const formatter = new Intl.NumberFormat(i18next.language);
        data.totalDuration = `${formatter.format(data.totalDurationRaw / 1000)} s`;
    }

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            modalOverflow
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsJobDetailModal" inModal>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                    <div className="log-wrapper">
                        <div className="message">
                            <JSONTree data={data} hideRoot />
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

ProcessingQueueJobDetailModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    data: PropTypes.objectOf(PropTypes.any),
};

ProcessingQueueJobDetailModal.defaultProps = {
    width: 'large',
    data: {},
};

export default ProcessingQueueJobDetailModal;
