import i18next from 'i18next';
import PropTypes from 'prop-types';
import { SelectBox } from '../../components/form/selectBox.react';

import ApiRequest from '../../api/apiRequest';

import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class EditorVariableModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            variables: [],
            loading: true,
        };

        this.loadVariables = this.loadVariables.bind(this);
        this.addVariable = this.addVariable.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }

    componentDidMount() {
        this.loadVariables();
    }

    loadVariables() {
        ApiRequest.get(
            `/template-processes/${this.props.templateId}/1/template-variables?order=tvar_name&sort=asc&limit=${config.restLimit}`,
        )
            .then((payload) => {
                if (Array.isArray(payload.items)) {
                    const varsArr = [];
                    payload.items.forEach((item) => {
                        varsArr.push({
                            value: item.tvar_name,
                            title: item.tvar_name,
                        });
                    });

                    const sysVars = [
                        { value: 'TaskName', title: 'TaskName' },
                        { value: 'TaskDescription', title: 'TaskDescription' },
                        { value: 'TaskUserJS', title: 'TaskUserJS' },
                        { value: 'TaskStartAt', title: 'TaskStartAt' },
                        { value: 'TaskDeadline', title: 'TaskDeadline' },
                        { value: 'TaskStart', title: 'TaskStart' },
                        { value: 'TaskFinish', title: 'TaskFinish' },
                        { value: 'TaskOwner', title: 'TaskOwner' },
                        { value: 'TaskOwnerName', title: 'TaskOwnerName' },
                        { value: 'TaskSolvedBy', title: 'TaskSolvedBy' },
                        {
                            value: 'TaskSolvedByName',
                            title: 'TaskSolvedByName',
                        },
                        { value: 'TaskInstruction', title: 'TaskInstruction' },
                        { value: 'TaskLink', title: 'TaskLink' },
                        { value: 'CaseName', title: 'CaseName' },
                        { value: 'CaseStatus', title: 'CaseStatus' },
                        { value: 'CaseStart', title: 'CaseStart' },
                        { value: 'CaseDeadline', title: 'CaseDeadline' },
                        { value: 'CasePriority', title: 'CasePriority' },
                        {
                            value: 'CasePriorityText',
                            title: 'CasePriorityText',
                        },
                        { value: 'CaseDescription', title: 'CaseDescription' },
                        { value: 'CaseOwner', title: 'CaseOwner' },
                        { value: 'CaseOwnerName', title: 'CaseOwnerName' },
                        { value: 'CaseNotes', title: 'CaseNotes' },
                        { value: 'CaseLink', title: 'CaseLink' },
                        { value: 'CaseVisRoleId', title: 'CaseVisRoleId' },
                        { value: 'FrontendLink', title: 'FrontendLink' },
                        { value: 'BackendLink', title: 'BackendLink' },
                    ];

                    this.setState({
                        variables: varsArr.concat(sysVars),
                        loading: false,
                    });
                }
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTempVarsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    addVariable(e) {
        if (e) e.preventDefault();

        if (!this.refs.variable.state.open) {
            const variable = this.refs.variable.getValue();

            if (typeof variable !== 'undefined' && variable !== null) {
                const variableValue = `{${variable.value}}`;

                this.props.parent.addVariable(variableValue);
            }

            this.closeModal();
        }
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    render() {
        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.addVariable}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsEditorVar" inModal>
                            <TabsButton
                                key="insert"
                                icon="icon-check-2"
                                enableOn={!this.state.loading}
                                onClick={this.addVariable}
                                hideTitle={false}
                            >
                                {i18next.t('insert')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        name="formVariable"
                        className="form-container"
                        oneColumn
                    >
                        <SelectBox
                            ref="variable"
                            key="variable"
                            label={`${i18next.t('variable')}:`}
                            options={this.state.variables}
                            nullable={false}
                            value={this.state.variables[0]}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

EditorVariableModal.propTypes = {
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    templateId: PropTypes.string.isRequired,
    parent: PropTypes.object,
};

export default EditorVariableModal;
