import i18next from 'i18next';
import PropTypes from 'prop-types';
import { guid } from '../../common/utils';
import { SelectBox } from '../../components/form/selectBox.react';
import { Text } from '../../components/form/text.react';
import { Checkbox } from '../../components/form/checkbox.react';
import ApiRequest from '../../api/apiRequest';
import React from 'react';
import Loader from 'react-loader';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class TreeEditNameModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            loading: false,
            folderId: null,
            folders: [],
            foldersLoaded: false,
            parentId: null,
        };

        this.loadFolders = this.loadFolders.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.saveNode = this.saveNode.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
    }

    componentDidMount() {
        this.loadFolders(this.props.nodeId);
    }

    loadFolders(nodeId) {
        if (this.props.loadFolders) {
            const arr = this.props.loadFolders();
            const allFilesNode = _.findIndex(
                arr,
                ['title', i18next.t('allFiles')],
            );
            if (allFilesNode === 0) {
                arr.splice(allFilesNode, 1);
            }
            this.setState({
                folders: arr,
                foldersLoaded: true,
                folderId: nodeId,
                parentId: this.props.parentId,
            });
        } else {
            ApiRequest.get(
                `/dms/folders-tree?order=folder_name&sort=asc&limit=${config.restLimit}`,
            )
                .then((payload) => {
                    const arr = [];
                    let parentId = null;

                    payload.items.forEach((item) => {
                        const obj = {
                            value: item.id,
                            title: item.label,
                        };
                        arr.push(obj);

                        const repeat = function (num) {
                            return new Array(isNaN(num) ? 1 : ++num).join(
                                '\u00A0\u00A0\u00A0\u00A0',
                            );
                        };
                        const recursion = function (item) {
                            const useSpace = repeat(item.tree_index + 1);
                            if (typeof item.children !== 'undefined') {
                                item.children.forEach((item) => {
                                    if (item.id == nodeId) {
                                        parentId = item.parent_id;
                                    }

                                    const obj = {
                                        value: item.id,
                                        title: useSpace + item.label,
                                    };
                                    arr.push(obj);
                                    if (
                                        typeof item.tree_index !== 'undefined'
                                    ) {
                                        recursion(item);
                                    }
                                });
                            }
                        };
                        recursion(item);
                    });

                    this.setState({
                        folders: arr,
                        foldersLoaded: true,
                        folderId: nodeId,
                        parentId: parentId,
                    });
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFolderDataFailed'),
                        serverError: errorMessage,
                    });
                });
        }
    }

    closeModal(e) {
        if (e) {
            e.preventDefault();
        }
        this.props.onClose();
    }

    saveNode(e) {
        if (e) {
            e.preventDefault();
        }

        if (!this.refs.parentFolder.state.open) {
            this.refs.formNewName.submit();
        }
    }

    handleSubmit(data) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });
        this.closeModal();
        let alertMessage = i18next.t('alrNewFolder');

        let folderName;
        if (this.props.hasHideBtn) {
            if (data.toHide) {
                folderName = _.startsWith(data.name, '_')
                    ? data.name
                    : `_${data.name}`;
            } else {
                folderName = _.startsWith(data.name, '_')
                    ? data.name.replace(/^(_)*/, '')
                    : data.name;
            }
        } else {
            folderName = data.name;
        }

        const folder = {
            folder_name: folderName,
            parent_id:
                data.parentFolder !== null &&
                typeof data.parentFolder.value !== 'undefined'
                    ? data.parentFolder.value
                    : data.parentFolder,
        };
        if (this.props.action != 'new') {
            folder.folder_id = this.state.folderId;
            alertMessage = i18next.t('alrSaved');
        }

        let promise;
        if (this.props.onEditNode) {
            promise = this.props.onEditNode(folder);
        } else {
            promise = ApiRequest.post('/dms/folder', JSON.stringify(folder));
        }

        promise
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: alertMessage,
                });
                this.props.treeUpdate();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message:
                        this.props.action != 'new'
                            ? i18next.t('alrFailedNewFolder')
                            : i18next.t('alrNewFolderFailed'),
                    serverError: errorMessage.message || errorMessage,
                });
            });
    }

    render() {
        const { nameValidationRegex } = this.props;
        const textTitle =
            this.props.action == 'new'
                ? `${i18next.t('name')}:`
                : `${i18next.t('newName')}:`;
        const getParentFolder = () => {
            const parentFolder = _.find(
                this.state.folders,
                [
                    'value',
                    this.props.action == 'new'
                        ? this.state.folderId
                        : this.state.parentId,
                ],
            );

            if (typeof parentFolder !== 'undefined') {
                return parentFolder;
            }
            return null;
        };
        const getFolderName = () => {
            const folder = _.find(
                this.state.folders,
                ['value', this.state.folderId],
            );

            if (typeof folder !== 'undefined') {
                return folder.title.trim();
            }
            return '';
        };

        const nameValidations = {};
        const nameValidationErrors = {
            isDefaultRequiredValue: i18next.t('isRequired'),
        };

        if (nameValidationRegex) {
            nameValidations.matchesRegular = nameValidationRegex;
            nameValidationErrors.matchesRegular = i18next.t('notValid');
        }

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.saveNode}
            >
                <Loader loaded={this.state.foldersLoaded} />
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsTreeEdit" inModal>
                            <TabsButton
                                key="save"
                                icon="icon-floppy-disk"
                                enableOn={!this.state.loading}
                                onClick={this.saveNode}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref="formNewName"
                        name="formNewName"
                        className="form-container"
                        onValidSubmit={this.handleSubmit}
                        oneColumn
                    >
                        <input
                            type="text"
                            name="hiddenInput"
                            style={{ display: 'none' }}
                        />
                        <SelectBox
                            ref="parentFolder"
                            key="parentFolder"
                            label={i18next.t('parentFolder')}
                            options={this.state.folders}
                            value={getParentFolder()}
                        />
                        <Text
                            key="name"
                            label={textTitle}
                            name="name"
                            required
                            value={
                                this.props.action !== 'new'
                                    ? getFolderName()
                                    : ''
                            }
                            validations={nameValidations}
                            validationErrors={nameValidationErrors}
                        />
                        {this.props.hasHideBtn && (
                            <Checkbox
                                key="toHide"
                                name="toHide"
                                label={`${i18next.t('toHide')}:`}
                                value={_.startsWith(getFolderName(), '_')}
                            />
                        )}
                    </Form>
                </div>
            </Modal>
        );
    }

}

TreeEditNameModal.propTypes = {
    action: PropTypes.string,
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    treeUpdate: PropTypes.func,
    nodeId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    parentId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    loadFolders: PropTypes.func,
    hasHideBtn: PropTypes.bool,
    nameValidationRegex: PropTypes.instanceOf(RegExp),
};

TreeEditNameModal.defaultProps = {
    nameValidationRegex: null,
};

export default TreeEditNameModal;
