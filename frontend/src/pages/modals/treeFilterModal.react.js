import React, { useState, useEffect, useRef } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Checkbox } from '../../components/form/checkbox.react';
import EmptyComponent from '../../components/form/emptyComponent.react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Form from '../../components/form/form.react';
import TabsWrapper from '../../components/tabs/tabsWrapper.react';
import Tabs from '../../components/tabs/tabs.react';

const TreeFilterModal = (props) => {
    const formRef = useRef(null);
    const [buttonChecked, setButtonChecked] = useState(props.buttonChecked);

    useEffect(() => {
        setButtonChecked(props.buttonChecked);
    }, [props.buttonChecked]);

    const saveSettings = (e) => {
        if (e) {
            e.preventDefault();
        }

        formRef.current.submit(); // -> handleValidSubmit()
    };

    const closeModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onClose();
    };

    const handleChange = (name, checked) => {
        setButtonChecked(checked);
    };

    const handleValidSubmit = (data) => {
        props.onConfirm(data.filterCheckBox);
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            onEnter={saveSettings}
            bindOnEnterEvent
        >
            <Form
                name="formFilter"
                ref={formRef}
                className="form-container"
                onValidSubmit={handleValidSubmit}
                oneColumn
            >
                <div key="filter-modal" className="modal filter-modal">
                    <TabsButtonsOther key="buttonsFilter" inModal>
                        <TabsButton
                            key="use"
                            icon="icon-floppy-disk"
                            isActive
                            onClick={saveSettings}
                            hideTitle={false}
                        >
                            {i18next.t('save')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                    <TabsWrapper>
                        <Tabs noTabLinks>
                            <Tabs.Tab
                                key="filtrate"
                                title={`${i18next.t('filtrate')} ${i18next.t('treeNodesMy')} ${props.treeFilterName}`}
                                name="tabFiltrate"
                            >
                                <div className="medium-12 columns tree-filter-button">
                                    <EmptyComponent key="empty" side="center" />
                                    <Checkbox
                                        key="selectedFilter"
                                        name="filterCheckBox"
                                        text={`${i18next.t('treeNodesMy')} ${props.treeFilterName}`}
                                        side="center"
                                        value={buttonChecked}
                                        onChange={handleChange}
                                    />
                                </div>
                            </Tabs.Tab>
                        </Tabs>
                    </TabsWrapper>
                </div>
            </Form>
        </Modal>
    );
};

TreeFilterModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    treeFilterName: PropTypes.string.isRequired,
    buttonChecked: PropTypes.bool.isRequired,
};

TreeFilterModal.defaultProps = {
    isOpen: false,
    width: 'small',
};

export default TreeFilterModal;
