import React, { useState, useEffect, useRef } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import _ from 'lodash';
import MultiBox from '../../components/form/multiBox.react';
import { SelectBox } from '../../components/form/selectBox.react';
import { Text } from '../../components/form/text.react';
import WrapComponent from '../../components/form/wrapComponent.react';
import ShowHideComponent from '../../components/form/showHideComponent.react';
import CKEditor from '../../components/form/ckEditor.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import { checkLangMutation, guid } from '../../common/utils';
import TemplateTaskApi from '../../flux/templateTask.api';
import LoggedUserStore from '../../flux/loggedUser.store';
import ApiRequest from '../../api/apiRequest';
import Modal from '../../components/modal.react';
import SaveButton from '../../components/table/filter/saveButton.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Form from '../../components/form/form.react';

const ResendEmailModal = (props) => {
    const formRef = useRef(null);
    const [items, setItems] = useState(null);
    const [templateTasksOptions, setTemplateTasksOptions] = useState(null);
    const [templateVarsUserOptions, setTemplateVarsUserOptions] =
        useState(null);
    const [templateVarsTextOptions, setTemplateVarsTextOptions] =
        useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (
            items !== null &&
            templateTasksOptions !== null &&
            templateVarsUserOptions !== null
        ) {
            setIsLoading(false);
        }
    }, [items, templateTasksOptions, templateVarsUserOptions]);

    useEffect(() => {
        ApiRequest.get(
            `/template-processes/${props.templateId}/${props.versionId}/template-tasks/${props.templateTaskId}`,
        )
            .then((payload) => {
                setItems(payload || {});
            })
            .catch((error) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });

        ApiRequest.get(
            `/template-processes/${props.templateId}/${props.versionId}/template-tasks?order=ttask_name&sort=asc&limit=${config.restLimit}`,
        )
            .then((payload) => {
                const tasksArr = [];

                if (Array.isArray(payload.items)) {
                    payload.items.forEach((item) => {
                        tasksArr.push({
                            value: String(item.id),
                            title: checkLangMutation(item, 'ttask_name'),
                        });
                    });
                }

                setTemplateTasksOptions(tasksArr);
            })
            .catch((error) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });

        ApiRequest.get(
            `/template-processes/${props.templateId}/${props.versionId}/template-variables?order=tvar_name&sort=asc&limit=${config.restLimit}`,
        )
            .then((payload) => {
                const templateVarsUserArr = [];
                const templateVarsTextArr = [];

                if (Array.isArray(payload.items)) {
                    payload.items.forEach((item) => {
                        // DLU
                        if (item.tvar_attribute == 'U') {
                            templateVarsUserArr.push({
                                value: String(item.id),
                                title: item.tvar_name,
                            });
                        }

                        // Text
                        if (
                            item.tvar_type == 'T' &&
                            item.tvar_attribute === null
                        ) {
                            templateVarsTextArr.push({
                                value: `{${item.tvar_name}}`,
                                title: `{${item.tvar_name}}`,
                            });
                        }
                    });
                }

                setTemplateVarsUserOptions(templateVarsUserArr);
                setTemplateVarsTextOptions(templateVarsTextArr);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTempVarsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }, []);

    const closeModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onClose();
    };

    const resendEmail = (data) => {
        closeModal();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSending'),
        });

        ApiRequest.post(`/tasks/resend-email/${props.taskId}`, JSON.stringify(data))
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('sent'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrFailedSendEmail'),
                    serverError: errorMessage,
                });
            });
    };

    const confirmSending = (e) => {
        e.preventDefault();
        formRef.current.submit(); // -> handleValidSubmit()
    };

    const handleValidSubmit = (data) => {
        resendEmail(data);
    };

    const getValue = function (value, options) {
        if (typeof value === 'undefined' || value === null) {
            if (typeof options[0] !== 'undefined') {
                return options[0].value;
            }
        } else {
            if (
                typeof value !== 'undefined' &&
                value !== null &&
                typeof value.type !== 'undefined'
            ) {
                return value.type;
            }
            return String(value);
        }
    };

    const getOrgUnitValue = function (item) {
        if (typeof item === 'undefined' || item === null || item == 0) {
            return { value: '0', title: 'Root' };
        }
        return item;
    };

    const getEnotOfVariableValue = (value, options) => {
        if (typeof value === 'undefined' || value === null) {
            if (typeof options[0] !== 'undefined') {
                return options[0];
            }

            return null;
        }

        return String(value);
    };

    const { languages } = LoggedUserStore.getState();
    const languageOptions = languages.map((l) => {
        return { title: l, value: l };
    });
    languageOptions.unshift({ value: null, title: i18next.t('default') });

    const notificationOptions = [
        { value: 'O', title: i18next.t('caseOwner') },
        { value: 'G', title: i18next.t('supervisor') },
        { value: 'T', title: i18next.t('taskOwner') },
        { value: 'P', title: i18next.t('email') },
        { value: 'S', title: i18next.t('plnOrgUnit') },
        { value: 'U', title: i18next.t('OfVariable') },
        { value: 'R', title: i18next.t('roles') },
    ];

    const replyNotificationOptions = _.cloneDeep(notificationOptions);
    replyNotificationOptions.unshift({
        value: null,
        title: i18next.t('default'),
    });

    const blindCopyNotificationOptions = _.cloneDeep(notificationOptions);
    blindCopyNotificationOptions.unshift({
        value: null,
        title: i18next.t('no'),
    });

    return (
        <Modal
            width={props.width}
            isOpen={props.isOpen}
            loading={false}
            onEnter={confirmSending}
            onEsc={closeModal}
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsConfrm" inModal>
                        <TabsButton
                            key="send"
                            icon="icon-mail-2"
                            isActive
                            onClick={confirmSending}
                            hideTitle={false}
                        >
                            {i18next.t('send')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                {!isLoading && (
                    <Form
                        ref={formRef}
                        name="resendEmailForm"
                        className="form-container"
                        onValidSubmit={handleValidSubmit}
                        oneColumn
                    >
                        {/* recipient */}
                        <SelectBox
                            key="ttask_enot_tgt_type"
                            label={`${i18next.t('recipient')}:`}
                            side="left"
                            options={notificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'solver',
                                P: 'email',
                                S: 'org',
                                U: 'var',
                                R: 'role',
                            }}
                            value={getValue(
                                items.ttask_enot_tgt_type,
                                notificationOptions,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_tgt_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['solver']}
                            nullable={false}
                            options={templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_tgt_ttask_id,
                                templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_tgt"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_tgt}
                            visibilityGroups={['email']}
                            helperMenu
                            helperMenuOptions={templateVarsTextOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <SelectBox
                            key="ttask_enot_external_language"
                            label={`${i18next.t('externalLang')}:`}
                            options={languageOptions}
                            visibilityGroups={['email']}
                            side="left"
                            value={getValue(
                                items.ttask_enot_external_language,
                                languageOptions,
                            )}
                            nullable={false}
                        />
                        <SelectBox
                            key="ttask_enot_tgt_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['org']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_tgt_orgstr_id,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_tgt_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['var']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_tgt_tvar_id,
                                templateVarsUserOptions,
                            )}
                            options={templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_tgt_tvar_name"
                            type="hidden"
                            visibilityGroups={['var']}
                            side="left"
                            value={items.ttask_enot_tgt}
                        />
                        <WrapComponent
                            key="notifRole"
                            side="left"
                            visibilityGroups={['role']}
                        >
                            <SelectBox
                                key="ttask_enot_tgt_role_id"
                                name="ttask_enot_tgt_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_tgt_role}
                                value={items.ttask_enot_tgt_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_tgt_role"
                                name="ttask_enot_tgt_role"
                                label=""
                                value={items.ttask_enot_tgt_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>

                        {/* reply recipient */}
                        <SelectBox
                            key="ttask_enot_reply_type"
                            label={`${i18next.t('replyRecipient')}:`}
                            side="left"
                            options={replyNotificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'replySolver',
                                P: 'replyEmail',
                                S: 'replyOrg',
                                U: 'replyVar',
                                R: 'replyRole',
                            }}
                            value={getValue(
                                items.ttask_enot_reply_type,
                                replyNotificationOptions,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_reply_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['replySolver']}
                            nullable={false}
                            options={templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_reply_ttask_id,
                                templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_reply_target"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_reply_target}
                            visibilityGroups={['replyEmail']}
                            helperMenu
                            helperMenuOptions={templateVarsTextOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <SelectBox
                            key="ttask_enot_reply_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['replyOrg']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_reply_orgstr_id,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_reply_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['replyVar']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_reply_tvar_id,
                                templateVarsUserOptions,
                            )}
                            options={templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_reply_tvar_name"
                            type="hidden"
                            visibilityGroups={['replyVar']}
                            side="left"
                            value={items.ttask_enot_reply_target}
                        />
                        <WrapComponent
                            key="notifRoleRelply"
                            side="left"
                            visibilityGroups={['replyRole']}
                        >
                            <SelectBox
                                key="ttask_enot_reply_role_id"
                                name="ttask_enot_reply_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_reply_role}
                                value={items.ttask_enot_reply_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_reply_role"
                                name="ttask_enot_reply_role"
                                label=""
                                value={items.ttask_enot_reply_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>

                        {/* copy */}
                        <SelectBox
                            key="ttask_enot_copy_type"
                            label={`${i18next.t('copyRecipient')}:`}
                            side="left"
                            options={blindCopyNotificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'copySolver',
                                P: 'copyEmail',
                                S: 'copyOrg',
                                U: 'copyVar',
                                R: 'copyRole',
                            }}
                            value={getValue(
                                items.ttask_enot_copy_type,
                                blindCopyNotificationOptions,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_copy_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['copySolver']}
                            nullable={false}
                            options={templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_copy_ttask_id,
                                templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_copy_target"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_copy_target}
                            visibilityGroups={['copyEmail']}
                            helperMenu
                            helperMenuOptions={templateVarsTextOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <SelectBox
                            key="ttask_enot_copy_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['copyOrg']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_copy_orgstr_id,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_copy_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['copyVar']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_copy_tvar_id,
                                templateVarsUserOptions,
                            )}
                            options={templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_copy_tvar_name"
                            type="hidden"
                            visibilityGroups={['copyVar']}
                            side="left"
                            value={items.ttask_enot_copy_target}
                        />
                        <WrapComponent
                            key="notifRoleCopy"
                            side="left"
                            visibilityGroups={['copyRole']}
                        >
                            <SelectBox
                                key="ttask_enot_copy_role_id"
                                name="ttask_enot_copy_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_copy_role}
                                value={items.ttask_enot_copy_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_copy_role"
                                name="ttask_enot_copy_role"
                                label=""
                                value={items.ttask_enot_copy_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>

                        {/* blind copy */}
                        <SelectBox
                            key="ttask_enot_blind_type"
                            label={`${i18next.t('bcRecipient')}:`}
                            side="left"
                            options={blindCopyNotificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'blindSolver',
                                P: 'blindEmail',
                                S: 'blindOrg',
                                U: 'blindVar',
                                R: 'blindRole',
                            }}
                            value={getValue(
                                items.ttask_enot_blind_type,
                                blindCopyNotificationOptions,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_blind_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['blindSolver']}
                            nullable={false}
                            options={templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_blind_ttask_id,
                                templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_blind_target"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_blind_target}
                            visibilityGroups={['blindEmail']}
                            helperMenu
                            helperMenuOptions={templateVarsTextOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <SelectBox
                            key="ttask_enot_blind_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['blindOrg']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_blind_orgstr_id,
                            )}
                        />
                        <SelectBox
                            key="ttask_enot_blind_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['blindVar']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_blind_tvar_id,
                                templateVarsUserOptions,
                            )}
                            options={templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        <Text
                            key="ttask_enot_blind_tvar_name"
                            type="hidden"
                            visibilityGroups={['blindVar']}
                            side="left"
                            value={items.ttask_enot_blind_target}
                        />
                        <WrapComponent
                            key="notifRoleBlind"
                            side="left"
                            visibilityGroups={['blindRole']}
                        >
                            <SelectBox
                                key="ttask_enot_blind_role_id"
                                name="ttask_enot_blind_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_blind_role}
                                value={items.ttask_enot_blind_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_blind_role"
                                name="ttask_enot_blind_role"
                                label=""
                                value={items.ttask_enot_blind_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>
                        {/* <Text key="ttask_enot_subject" label={i18next.t('subject') + ":"} side="left" */}
                        {/*     value={items.ttask_enot_subject}/> */}
                        {/* <ShowHideComponent key="langMutEnotSubject" side="left"> */}
                        {/*   { */}
                        {/*       languages.map((lang, i) => { */}
                        {/*           return ( */}
                        {/*               <Text */}
                        {/*                   key={`ttaskEnotSubject${lang}`} */}
                        {/*                   name={`ttask_enot_subject_${lang}`} */}
                        {/*                   label={`${i18next.t('subject')}:`} */}
                        {/*                   value={items[`ttask_enot_subject_${lang}`]} */}
                        {/*                   lblLang={lang} */}
                        {/*               /> */}
                        {/*           ); */}
                        {/*       }) */}
                        {/*   } */}
                        {/* </ShowHideComponent> */}
                        {/* <CKEditor key="ttask_enot_body2" label={i18next.t('body') + ":"} side="center" */}
                        {/*         dataId={taskId} */}
                        {/*         value={items.ttask_enot_body2 || items.ttask_enot_body || ""} */}
                        {/*         templateId={templateId} /> */}
                        {/* <ShowHideComponent key="langMutEnotSubject" side="center"> */}
                        {/*   { */}
                        {/*       languages.map((lang, i) => { */}
                        {/*           return ( */}
                        {/*               <CKEditor */}
                        {/*                   key={`ttaskEnotBody2${lang}`} */}
                        {/*                   name={`ttask_enot_body2_${lang}`} */}
                        {/*                   label={`${i18next.t('body')}:`} */}
                        {/*                   dataId={taskId} */}
                        {/*                   value={items[`ttask_enot_body2_${lang}`] || items[`ttask_enot_body_${lang}`] || ''} */}
                        {/*                   templateId={templateId} */}
                        {/*                   lblLang={lang} */}
                        {/*               /> */}
                        {/*           ); */}
                        {/*       }) */}
                        {/*   } */}
                        {/* </ShowHideComponent> */}
                    </Form>
                )}
            </div>
        </Modal>
    );
};

ResendEmailModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
};

ResendEmailModal.defaultProps = {
    isOpen: false,
    width: 'tiny',
};

export default ResendEmailModal;
