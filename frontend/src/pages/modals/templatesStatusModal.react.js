import i18next from 'i18next';
import PropTypes from 'prop-types';
import { guid } from '../../common/utils';

import ApiRequest from '../../api/apiRequest';

import React from 'react';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import RadioButton from '../../components/form/radioButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class TemplatesStatusModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            loading: true,
            templateStatus: null,
        };

        this.loadModalData = this.loadModalData.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.saveStatus = this.saveStatus.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
    }

    componentDidMount() {
        this.loadModalData(this.props.templateId);
    }

    loadModalData(id) {
        if (id) {
            ApiRequest.get(`/template-processes/${id}/${this.props.versionId}`)
                .then((payload) => {
                    this.setState({
                        templateStatus: payload.tproc_status,
                        loading: false,
                    });
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedData'),
                        serverError: errorMessage,
                    });
                });
        }
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    saveStatus(e) {
        if (e) e.preventDefault();
        this.refs.formStatus.submit();
    }

    handleSubmit(data) {
        this.closeModal();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = {
            tproc_id: this.props.templateId,
            tproc_version: this.props.versionId,
            tproc_status: data.status,
        };

        const table = this.props.parent.refs[this.props.tableRef];

        ApiRequest.post('/template-processes', JSON.stringify(obj))
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });

                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    render() {
        const statusOptions = [
            { value: 'A', title: i18next.t('operating') },
            { value: 'N', title: i18next.t('paused') },
            { value: 'D', title: i18next.t('developed') },
            { value: 'T', title: i18next.t('tested') },
        ];

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.saveStatus}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsTempStatus" inModal>
                            <TabsButton
                                key="insert"
                                icon="icon-floppy-disk"
                                enableOn={!this.state.loading}
                                onClick={this.saveStatus}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref="formStatus"
                        name="formStatus"
                        className="form-container"
                        onValidSubmit={this.handleSubmit}
                        oneColumn
                    >
                        <RadioButton
                            key="status"
                            label={`${i18next.t('templateStatus')}:`}
                            name="status"
                            value={this.state.templateStatus}
                            options={statusOptions}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

TemplatesStatusModal.propTypes = {
    templateId: PropTypes.number,
    versionId: PropTypes.number,
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    parent: PropTypes.object.isRequired,
    tableRef: PropTypes.string.isRequired,
};

export default TemplatesStatusModal;
