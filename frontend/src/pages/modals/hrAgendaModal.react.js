import i18next from 'i18next';
import PropTypes from 'prop-types';
import { guid } from '../../common/utils';
import { SelectBox } from '../../components/form/selectBox.react';
import { Checkbox } from '../../components/form/checkbox.react';
import ApiRequest from '../../api/apiRequest';

import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Form from '../../components/form/form.react';
import RadioButton from '../../components/form/radioButton.react';
import ConfirmModal from './confirmModal.react';

class hrAgendaModal extends React.Component {

    constructor(props) {
        super();

        this.state = {};

        this.handleSubmit = this.handleSubmit.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.save = this.save.bind(this);
    }

    save(e) {
        if (e) e.preventDefault();

        if (!this.newUserRef.state.open) {
            this.hrAgendaFormRef.submit();
        }
    }

    handleSubmit(data) {
        const postObj = {
            toUserId: data.newUser.value,
            addNewRights: data.addNewRights,
        };
        this.props.onSave(postObj);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    render() {
        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={false}
                width={this.props.width}
                onEsc={this.closeModal}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsHandover" inModal>
                            <TabsButton
                                key="save"
                                icon="icon-floppy-disk"
                                isActive
                                onClick={this.save}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref={(r) => {
                            this.hrAgendaFormRef = r;
                        }}
                        name="formHandover"
                        className="form-container"
                        onValidSubmit={this.handleSubmit}
                        oneColumn
                    >
                        <SelectBox
                            key="newUser"
                            ref={(r) => {
                                this.newUserRef = r;
                            }}
                            label={`${i18next.t('newUser')}:`}
                            selectBoxType="DLU"
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                            nullable={false}
                        />
                        <Checkbox
                            key="addNewRights"
                            label={`${i18next.t('addCaseRightNewUser')}:`}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

hrAgendaModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    onSave: PropTypes.func.isRequired,
};

export default hrAgendaModal;
