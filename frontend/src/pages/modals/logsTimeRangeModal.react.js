import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Dialog } from '@mui/material';
import { CalendarWithClockpicker } from '../../components/form/calendarWithClockpicker.react';

import React from 'react';
import createReactClass from 'create-react-class';
import _ from 'lodash';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AltManagerMixin from '../../flux/altManagerMixin';
import TableActions from '../../components/table/table.actions';
import TableStore from '../../components/table/table.store';

const LogsTimeRangeModal = createReactClass({
    propTypes: {
        onClose: PropTypes.func.isRequired,
        isOpen: PropTypes.bool.isRequired,
        width: PropTypes.string.isRequired,
        tableRef: PropTypes.any,
        tableName: PropTypes.string.isRequired,
    },

    mixins: [AltManagerMixin],

    statics: {
        registerStore: TableStore,
        registerAction: TableActions,
    },

    getInitialState() {
        this.initFlux(this.props, this.props.tableName);

        return _.extend(
            {
                loading: false,
            },
            this.store.getState(),
        );
    },

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    },

    saveTimeRange(e) {
        if (e) e.preventDefault();

        let canSubmit = true;
        if (
            this.calendarFromRef &&
            this.calendarFromRef.state &&
            this.calendarFromRef.state.calendarIsOpen
        ) {
            canSubmit = false;
        }
        if (
            this.calendarToRef &&
            this.calendarToRef.state &&
            this.calendarToRef.state.calendarIsOpen
        ) {
            canSubmit = false;
        }

        if (canSubmit) {
            this.formTimeRangeRef.submit();
        }
    },

    resetTimeRange(e) {
        if (e) e.preventDefault();

        this.action.setLogsTimeRange({});

        if (this.props.tableRef) {
            this.props.tableRef.handleFetchRows({ page: 1 });
            this.action.setHighestPageNumber(1);
        }

        this.closeModal();
    },

    handleSubmit(data) {
        this.action.setLogsTimeRange(data);

        if (this.props.tableRef) {
            this.props.tableRef.handleFetchRows({ page: 1 });
            this.action.setHighestPageNumber(1);
        }

        this.closeModal();
    },

    render() {
        return (
            <Dialog
                fullWidth
                maxWidth="xs"
                open={this.props.isOpen}
                onClose={this.closeModal}
                // isOpen={this.props.isOpen}
                // loading={this.state.loading}
                // width={this.props.width}
                // onEsc={this.closeModal}
                // onEnter={this.saveVariable}
            >
                <div className="old-modal">
                    <div className="modal logs-time-range">
                        <div className="row">
                            <TabsButtonsOther key="buttonsMaintenance" inModal>
                                <TabsButton
                                    key="save"
                                    icon="icon-floppy-disk"
                                    enableOn={!this.state.loading}
                                    onClick={this.saveTimeRange}
                                    hideTitle={false}
                                >
                                    {i18next.t('save')}
                                </TabsButton>
                                <TabsButton
                                    key="reset"
                                    icon="icon-refresh"
                                    enableOn={!this.state.loading}
                                    onClick={this.resetTimeRange}
                                    hideTitle={false}
                                >
                                    {i18next.t('reset')}
                                </TabsButton>
                                <TabsButton
                                    key="close"
                                    icon="icon-delete-1"
                                    isActive
                                    onClick={this.closeModal}
                                    hideTitle={false}
                                >
                                    {i18next.t('close')}
                                </TabsButton>
                            </TabsButtonsOther>
                        </div>
                        <Form
                            ref={(r) => {
                                this.formTimeRangeRef = r;
                            }}
                            name="formMaintenance"
                            className="form-container"
                            onValidSubmit={this.handleSubmit}
                            oneColumn
                        >
                            <span
                                key="from"
                                className="left"
                            >
                                {`${i18next.t('from')}:`}
                            </span>
                            <CalendarWithClockpicker
                                ref={(c) => {
                                    this.calendarFromRef = c;
                                }}
                                key="logsFrom"
                                value={this.state.logsTimeRange.from}
                            />
                            <span
                                key="to"
                                className="left time-range-to"
                            >
                                {`${i18next.t('to')}:`}
                            </span>
                            <CalendarWithClockpicker
                                ref={(c) => {
                                    this.calendarToRef = c;
                                }}
                                key="logsTo"
                                value={this.state.logsTimeRange.to}
                            />
                        </Form>
                    </div>
                </div>
            </Dialog>
        );
    },
});

export default LogsTimeRangeModal;
