import i18next from 'i18next';
import PropTypes from 'prop-types';

import React from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class InfoModal extends React.Component {

    constructor(props) {
        super(props);

        this.state = {
            loading: false,
        };

        this.closeModal = this.closeModal.bind(this);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    render() {
        const { extraButtons } = this.props;

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                modalOverflow={this.props.modalOverflow}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsConfrm" inModal>
                            {extraButtons.map((button) => {
                                return button;
                            })}
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <div className="center">
                        {this.props.isHtml && (
                            <div
                                className="text"
                                dangerouslySetInnerHTML={{
                                    __html: this.props.text,
                                }}
                            />
                        )}
                        {!this.props.isHtml && (
                            <div className="text">{this.props.text}</div>
                        )}
                    </div>
                </div>
            </Modal>
        );
    }

}

InfoModal.propTypes = {
    text: PropTypes.string.isRequired,
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    modalOverflow: PropTypes.bool,
    isHtml: PropTypes.bool,
    extraButtons: PropTypes.arrayOf(PropTypes.object),
};

InfoModal.defaultProps = {
    isOpen: false,
    width: 'tiny',
    modalOverflow: false,
    isHtml: false,
    extraButtons: [],
};

export default InfoModal;
