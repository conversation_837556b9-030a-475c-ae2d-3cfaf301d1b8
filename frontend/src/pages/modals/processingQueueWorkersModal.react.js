import i18next from 'i18next';
import PropTypes from 'prop-types';
import { JSONTree } from 'react-json-tree';
import React, { useEffect, useRef, useState } from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import ApiRequest from '../../api/apiRequest';
import AlertsActions from '../../components/alerts/alerts.actions';

const ProcessingQueueWorkersModal = (props) => {
    const [workersData, setWorkersData] = useState({});
    const closeModal = (e) => {
        e.preventDefault();
        props.onClose();
    };

    useEffect(() => {
        ApiRequest.get(`/queue/${props.queueName}/get-workers`)
            .then((payload) => {
                setWorkersData(payload);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });
    }, []);

    const resumeWorkers = () => {
        ApiRequest.get(`/queue/${props.queueName}/worker/resume`)
            .then((payload) => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: i18next.t('done'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailed'),
                    serverError: errorMessage,
                });
            });
    };

    const pauseWorkers = () => {
        ApiRequest.get(`/queue/${props.queueName}/worker/pause`)
            .then((payload) => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: i18next.t('done'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailed'),
                    serverError: errorMessage,
                });
            });
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            modalOverflow
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsJobDetailModal" inModal>
                        <TabsButton
                            key="resume"
                            icon="icon-play-2"
                            isActive
                            onClick={resumeWorkers}
                            hideTitle={false}
                        >
                            {i18next.t('continue')}
                        </TabsButton>
                        <TabsButton
                            key="pause"
                            icon="icon-stop-2"
                            isActive
                            onClick={pauseWorkers}
                            hideTitle={false}
                        >
                            {i18next.t('pause')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                    <div className="log-wrapper">
                        <div className="message">
                            <JSONTree data={workersData} hideRoot />
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

ProcessingQueueWorkersModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    queueName: PropTypes.string.isRequired,
};

ProcessingQueueWorkersModal.defaultProps = {
    width: 'large',
};

export default ProcessingQueueWorkersModal;
