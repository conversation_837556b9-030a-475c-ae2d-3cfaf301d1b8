import PropTypes from 'prop-types';
import i18next from 'i18next';
import { TextArea } from '../../components/form/textArea.react';
import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import LoggedUserStore from '../../flux/loggedUser.store';

class MultiBoxSingleInBulkModal extends React.Component {

    constructor(props) {
        super();

        this.state = {
            loading: false,
        };

        this.closeModal = this.closeModal.bind(this);
        this.add = this.add.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleInvalidSubmit = this.handleInvalidSubmit.bind(this);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    add(e) {
        if (e) e.preventDefault();

        this.onFormAction = (data) => {
            // prevent to add values if textArea is focused
            if (document.activeElement.tagName !== 'TEXTAREA') {
                if (data) {
                    const forAddValuesObj = {
                        default: [],
                    };
                    const defaultValuesArr = data.default.split('\n');

                    // add default values
                    defaultValuesArr.forEach((val) => {
                        const newVal = val.trim();
                        if (!_.isEmpty(newVal)) {
                            forAddValuesObj.default.push(newVal);
                        }
                    });

                    const defaultArrLength = forAddValuesObj.default.length;

                    // add translations values
                    _.forEach(data, (values, key) => {
                        if (key !== 'default') {
                            forAddValuesObj[key] = [];

                            if (values) {
                                const valuesArr = data[key].split('\n');

                                valuesArr.forEach((val, i) => {
                                    const newVal = val.trim();

                                    // count of translation values cannot be greater than count of default values
                                    if (i + 1 <= defaultArrLength) {
                                        if (!_.isEmpty(newVal)) {
                                            forAddValuesObj[key].push(newVal);
                                        } else {
                                            forAddValuesObj[key].push(null);
                                        }
                                    }
                                });
                            }
                        }
                    });

                    // Count of values for all columns must be the same. Eventually add null
                    _.forEach(forAddValuesObj, (values, key) => {
                        if (key !== 'default') {
                            if (values.length !== defaultArrLength) {
                                const diff = defaultArrLength - values.length;

                                for (let i = 0; i < diff; i += 1) {
                                    values.push(null);
                                }
                            }
                        }
                    });

                    this.props.parent.addValues(forAddValuesObj);
                }

                this.closeModal();
            }
        };

        this.onFormActionInvalid = () => {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        };

        this.formRef.submit(); // -> handleValidSubmit() / handleValidSubmitWithoutRequire() / handleInvalidSubmit()
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleInvalidSubmit() {
        this.onFormActionInvalid();
    }

    render() {
        const { languages } = LoggedUserStore.getState();

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.add}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsBulk" inModal>
                            <TabsButton
                                key="insert"
                                icon="icon-check-2"
                                isActive
                                onClick={this.add}
                                hideTitle={false}
                            >
                                {i18next.t('insert')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref={(r) => {
                            this.formRef = r;
                        }}
                        name="formValues"
                        className="form-container"
                        onValidSubmit={this.handleValidSubmit}
                        onInvalidSubmit={this.handleInvalidSubmit}
                    >
                        <TextArea
                            key="default"
                            label={`${i18next.t('default')}:`}
                            upperLabel
                            rows={10}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                        {languages.map((lang) => {
                            return (
                                <TextArea
                                    key={lang}
                                    label={`${lang}:`}
                                    upperLabel
                                    rows={10}
                                />
                            );
                        })}
                    </Form>
                </div>
            </Modal>
        );
    }

}

MultiBoxSingleInBulkModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    parent: PropTypes.objectOf(PropTypes.any).isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
};

MultiBoxSingleInBulkModal.defaultProps = {
    isOpen: false,
    width: 'large',
};

export default MultiBoxSingleInBulkModal;
