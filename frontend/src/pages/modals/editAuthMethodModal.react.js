import PropTypes from 'prop-types';
import i18next from 'i18next';
import InputColor from 'react-input-color';
import { Text } from '../../components/form/text.react';
import { TextArea } from '../../components/form/textArea.react';
import { SelectBox } from '../../components/form/selectBox.react';
import { Checkbox } from '../../components/form/checkbox.react';
import { guid } from '../../common/utils';
import { CodeArea } from '../../components/form/codeArea.react';
import ApiRequest from '../../api/apiRequest';
import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import WrapComponent from '../../components/form/wrapComponent.react';
import EmptyComponent from '../../components/form/emptyComponent.react';

class EditAuthMethodModal extends React.Component {

    constructor(props) {
        super(props);

        const meta = JSON.parse(this.props.method.auth_meta || '{}');

        this.state = {
            loading: false,
            params: this.props.params,
            buttonColor: meta.backgroundColor || '#76b703',
            visibility: meta.visibility !== 'N',
        };

        this.closeModal = this.closeModal.bind(this);
        this.saveMethod = this.saveMethod.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleInvalidSubmit = this.handleInvalidSubmit.bind(this);
        this.fillDefaultParams = this.fillDefaultParams.bind(this);
        this.changeColor = this.changeColor.bind(this);
        this.changeVisibility = this.changeVisibility.bind(this);
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        if (this.props.params !== nextProps.params) {
            this.setState({ params: nextProps.params });
        }
    }

    fillDefaultParams(name, defaultValue, selectBox) {
        const defaults = this.props.authList.filter((authority) => {
            return (
                defaultValue.title ===
                authority.module.replace('Authority.js', '')
            );
        });

        this.setState({ params: defaults[0].params });
    }

    closeModal(e) {
        if (e) {
            e.preventDefault();
        }

        this.props.onClose();
    }

    changeColor(color) {
        this.setState({
            buttonColor: color.hex,
        });
    }

    changeVisibility(name, value) {
        this.setState({
            visibility: value,
        });
    }

    saveMethod(e) {
        if (e) {
            e.preventDefault();
        }

        this.onFormAction = function onFormAction(data) {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: i18next.t('alrSaving'),
            });
            let obj = {};
            obj.auth_module = data.auth_module.value || data.auth_module;
            obj.auth_name = data.auth_name;
            obj.auth_id = this.props.id;
            obj.auth_is_fallback = data.fallback ? 'Y' : 'N';
            obj.auth_sync_enabled = data.syncEnabled ? 'Y' : 'N';
            obj.auth_enabled = data.active ? 'Y' : 'N';
            obj.auth_meta = JSON.stringify({
                backgroundColor: this.state.buttonColor,
                visibility: this.state.visibility ? 'Y' : 'N',
            });

            if (this.props.method.auth_order) {
                obj.auth_order = this.props.method.auth_order;
            }

            if (_.isEmpty(data.params)) {
                obj.auth_params = {};
            }

            try {
                obj.auth_params = JSON.parse(data.params);
            } catch (err) {
                obj.auth_params = data.params;
            }

            obj = { authConfig: obj };

            ApiRequest.post('/authorization/config', JSON.stringify(obj))
                .then((payload) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrSaved'),
                    });
                    this.closeModal();
                    this.props.refresh();
                })
                .catch((errorMessage) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrSavingFailed'),
                        serverError: errorMessage,
                    });
                });
        };

        this.onFormActionInvalid = (data) => {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        };

        this.formMethod.submit(); // -> handleValidSubmit() / handleInvalidSubmit()
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleInvalidSubmit(data) {
        this.onFormActionInvalid(data);
    }

    render() {
        const { params } = this.state;
        const options = this.props.authList.map((method) => {
            const module = method.module.replace('Authority.js', '');
            return { title: module, value: module };
        });

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                modalOverflow
            >
                <div className="modal auth-method-modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsAuthModal" inModal>
                            <TabsButton
                                key="save"
                                icon="icon-floppy-disk"
                                isActive
                                onClick={this.saveMethod}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <div className="modal-heading">
                        <h6>
                            {this.props.method.auth_name
                                ? `${this.props.method.auth_name} – ${this.props.method.auth_module}`
                                : `${i18next.t('new')}`}
                        </h6>
                    </div>
                    <Form
                        ref={(ref) => {
                            this.formMethod = ref;
                        }}
                        name="formMethod"
                        onValidSubmit={this.handleValidSubmit}
                        onInvalidSubmit={this.handleInvalidSubmit}
                    >
                        <WrapComponent
                            key="wrapModule"
                            name="heading"
                            side="centerTop"
                        >
                            <Text
                                key="auth_name"
                                name="auth_name"
                                label={i18next.t('name')}
                                side="left"
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                                value={this.props.method.auth_name}
                            />
                            <SelectBox
                                key="auth_module"
                                name="auth_module"
                                label={i18next.t('module')}
                                side="right"
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                                value={this.props.method.auth_module}
                                options={options}
                                nullable={false}
                                onChange={this.fillDefaultParams}
                            />
                        </WrapComponent>
                        <EmptyComponent key="emp" side="centerTop" />
                        <h6 key="heading" side="centerTop">
                            {i18next.t('params')}
                        </h6>
                        <Checkbox
                            key="fallback"
                            label={i18next.t('fallback')}
                            value={
                                this.props.method.auth_is_fallback === 'Y'
                                    ? true
                                    : this.props.method.auth_name == null
                            }
                            side="left"
                        />
                        <Checkbox
                            key="active"
                            label={i18next.t('active')}
                            value={
                                this.props.method.auth_enabled === 'Y'
                                    ? true
                                    : this.props.method.auth_name == null
                            }
                            side="left"
                        />
                        <Checkbox
                            key="syncEnabled"
                            label={i18next.t('syncEnabled')}
                            value={
                                this.props.method.auth_sync_enabled === 'Y'
                                    ? true
                                    : this.props.method.auth_name == null
                            }
                            side="left"
                        />
                        <Checkbox
                            key="visibility"
                            label={i18next.t('visible')}
                            value={this.state.visibility}
                            side="left"
                            onChange={this.changeVisibility}
                        />
                        <label key="loginBtnColor" side="right">
                            {i18next.t('loginBtnColor')}
                        </label>
                        <InputColor
                            key="loginColor"
                            initialValue={this.state.buttonColor}
                            onChange={this.changeColor}
                            side="right"
                        />
                        <CodeArea
                            key="params"
                            name="params"
                            label={i18next.t('params')}
                            height="500px"
                            value={params}
                            side="center"
                            fullLabel
                            upperLabel
                            language="json"
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

EditAuthMethodModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    method: PropTypes.object.isRequired,
    refresh: PropTypes.func.isRequired,
    params: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
        .isRequired,
    authList: PropTypes.array.isRequired,
    width: PropTypes.string.isRequired,
};

export default EditAuthMethodModal;
