import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Checkbox } from '../../components/form/checkbox.react';
import { SelectBox } from '../../components/form/selectBox.react';
import { guid, checkLangMutation } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';

import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Factory from '../../flux/factory';
import CasePrintStore from '../../flux/casePrint.store';
import CasePrintActions from '../../flux/casePrint.actions';

class PrintCaseModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            printTemplateState: [],
            dbPrints: [],
            loading: true,
        };

        this.loadModalData = this.loadModalData.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.printModal = this.printModal.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleValidSubmitWithoutRequire =
            this.handleValidSubmitWithoutRequire.bind(this);
        this.handleInvalidSubmit = this.handleInvalidSubmit.bind(this);
    }

    componentDidMount() {
        this.loadModalData();
    }

    componentWillUnmount() {
        this.loadModalDataRequest.cancel();
    }

    loadModalData() {
        // request for data
        this.loadModalDataRequest = ApiRequest.get(
            `/processes/${this.props.id}/prints?order=prnt_order&sort=asc&limit=${config.restLimit}`,
        )
            .then((payload) => {
                if (payload) {
                    // array of print templates
                    const printTemplate = [];
                    const dbPrints = [];

                    payload.items.forEach((item) => {
                        const modalItem = {
                            value: item.id,
                            title: checkLangMutation(item, 'prnt_name'),
                            isReactPrint: item.prnt_react === 'Y',
                        };
                        dbPrints.push(modalItem);
                    });
                    // add "výchozí tisk" option
                    printTemplate.unshift({
                        value: -1,
                        title: i18next.t('defaultPrintProcess'),
                    });
                    if (this.props.taskId) {
                        printTemplate.unshift({
                            value: -2,
                            title: i18next.t('defaultPrintTask'),
                        });
                    }
                    this.setState({
                        printTemplateState: printTemplate,
                        dbPrints: dbPrints,
                        loading: false,
                    });
                }
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedPrintData'),
                    serverError: errorMessage,
                });
            });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    // object values to JSON
    editPrintValues(values) {
        const editedValues = {};

        _.forEach(values, (value, key) => {
            if (typeof value === 'object' && !Array.isArray(value)) {
                editedValues[key] = JSON.stringify(value);
            } else {
                editedValues[key] = value;
            }
        });

        return editedValues;
    }

    // form submit
    printModal(e) {
        if (e) e.preventDefault();

        this.onFormAction = (data) => {
            const { taskId, caseId } = this.props;
            const { tabActionAttach } = this.props.parent;
            const title = this.props.fileName;
            const printtype = data.printtype.value;
            const printchoise = data.printchoise.value;
            const printId = printchoise === -2 ? -1 : printchoise; // default
            const casePrintObj = Factory.registerFlux(
                CasePrintStore,
                CasePrintActions,
                `casePrint-${caseId}-${printId}`,
            );
            const casePrintStore = casePrintObj.store;
            const printValuesObj = this.editPrintValues(
                casePrintStore.getState().printValuesObj,
            );

            // find out if it's a React print
            const findPrint = _.find(this.state.dbPrints, {
                value: printchoise,
            });

            // custom print
            if (
                findPrint &&
                findPrint.isReactPrint &&
                printtype === 'printer' &&
                printId !== -1
            ) {
                this.closeModal();

                let urlParams = '?print=true';
                let urlValues = '';

                if (!_.isEmpty(printValuesObj)) {
                    _.forEach(printValuesObj, (value, key) => {
                        if (typeof value === 'number') {
                            urlValues += `&${key}=N~${value}`;
                        } else if (Array.isArray(value)) {
                            urlValues += `&${key}=${JSON.stringify(value)}`;
                        } else {
                            urlValues += `&${key}=${value}`;
                        }
                    });
                }

                if (!_.isEmpty(printValuesObj)) {
                    urlParams += urlValues;
                }

                const newWindow = window.open(
                    `${window.location.origin}/case-print/${caseId}/${printId}${urlParams}`,
                    '_blank',
                );

                if (!newWindow) {
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('alrBlockedPopups'),
                        show: true,
                    });
                }
            } else {
                const obj = {
                    iprocId: this.props.id,
                    itaskId:
                        printchoise === -2 && taskId ? Number(taskId) : null,
                    id: printId, // printID -1, 241, 229...
                    docType: printtype, // "printer", "pdf", "html"
                    storeDMS: data.saveAsAttach,
                    values: printValuesObj,
                };

                const alertId = guid();
                AlertsActions.addAlert({
                    id: alertId,
                    type: 'info',
                    message: i18next.t('alrPreparingPrint'),
                });

                ApiRequest.post(
                    `/processes/${this.props.id}/printToFile`,
                    JSON.stringify(obj),
                )
                    .then((answer) => {
                        this.closeModal();

                        if (printtype === 'printer') {
                            // only print into new window
                            AlertsActions.removeButtonAlert(alertId);
                            const newWindow = window.open('', '_blank');

                            if (newWindow) {
                                newWindow.document.write(answer.result);
                                newWindow.document.close();
                            } else {
                                AlertsActions.changeAlert({
                                    id: alertId,
                                    type: 'warning',
                                    message: i18next.t('alrBlockedPopups'),
                                    show: true,
                                });
                            }
                        } else {
                            // append html or pdf to process or open to a new window
                            AlertsActions.changeAlert({
                                id: alertId,
                                type: 'success',
                                message: i18next.t('alrFileDownloaded'),
                            });

                            if (data.saveAsAttach) {
                                if (answer.message === 'VICE_VIEW_ONLY_ERROR') {
                                    AlertsActions.addAlert({
                                        type: 'success',
                                        message: i18next.t(
                                            'alrFileSaveLikeAttachViceError',
                                        ),
                                        show: true,
                                    });
                                } else if (
                                    answer.message === 'FILE_STORE_ERROR'
                                ) {
                                    AlertsActions.addAlert({
                                        type: 'success',
                                        message: i18next.t(
                                            'alrFileSaveLikeAttachStoreError',
                                        ),
                                        show: true,
                                    });
                                } else {
                                    // update attachments count
                                    if (typeof taskId !== 'undefined') {
                                        tabActionAttach.getLabelCount({
                                            apiUrl: `/tasks/${taskId}/attachments`,
                                        });
                                    } else {
                                        tabActionAttach.getLabelCount({
                                            apiUrl: `/processes/${caseId}/attachments`,
                                        });
                                    }

                                    AlertsActions.addAlert({
                                        type: 'success',
                                        message: i18next.t(
                                            'alrFileSavedLikeAttach',
                                        ),
                                    });
                                }
                            } else {
                                const fileType =
                                    printtype === 'doc'
                                        ? 'application/msword'
                                        : printtype === 'pdf'
                                            ? 'application/pdf'
                                            : 'text/html';

                                if (printtype === 'pdf') {
                                    // base64
                                    const byteCharacters = atob(answer.result);
                                    const byteNumbers = new Array(
                                        byteCharacters.length,
                                    );

                                    for (
                                        let i = 0;
                                        i < byteCharacters.length;
                                        i++
                                    ) {
                                        byteNumbers[i] =
                                            byteCharacters.charCodeAt(i);
                                    }

                                    const byteArray = new Uint8Array(
                                        byteNumbers,
                                    );
                                    let blob;

                                    if (fileType) {
                                        blob = new Blob([byteArray], {
                                            type: fileType,
                                        });
                                    } else {
                                        blob = new Blob([byteArray]);
                                    }

                                    this.openPrintInNewTab(
                                        blob,
                                        `${title}.pdf`,
                                    );
                                } else {
                                    const blob = new Blob([answer.result], {
                                        type: fileType,
                                    });
                                    this.openPrintInNewTab(
                                        blob,
                                        `${title}.html`,
                                    );
                                }
                            }
                        }
                    })
                    .catch((errorMessage) => {
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'alert',
                            message: i18next.t('alrFailedCreatePrint'),
                            serverError: errorMessage,
                        });
                    });
            }
        };

        this.onFormActionWithoutRequire = function (data) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillRequiredItems'),
                show: true,
                allowCountdown: true,
            });
        };

        this.onFormActionInvalid = function (data) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        };

        if (
            !this.refs.printchoise.state.open &&
            !this.refs.printtype.state.open
        ) {
            this.refs.formPrintTaskModal.submit(); // -> handleValidSubmit() / handleValidSubmitWithoutRequire() / handleInvalidSubmit()
        }
    }

    openPrintInNewTab(blob, title) {
        if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(blob, title);
            /* const link = document.createElement('a');
            link.style.display = "none";
            link.href = fileURL;
            link.download = title; // escapeHtml(title)
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link); */
        } else {
            const fileURL = URL.createObjectURL(blob);
            window.open(fileURL);
        }
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleValidSubmitWithoutRequire(data) {
        this.onFormActionWithoutRequire(data);
    }

    handleInvalidSubmit(data) {
        this.onFormActionInvalid(data);
    }

    render() {
        const printTypeOptions = [
            { value: 'printer', title: i18next.t('printer') },
            /* {value: 'doc', title: 'MSWord doc'}, */
            { value: 'pdf', title: 'PDF' },
            { value: 'html', title: 'HTML' },
        ];

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.printModal}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsPrintCase" inModal>
                            <TabsButton
                                key="print"
                                icon="icon-printer"
                                enableOn={!this.state.loading}
                                onClick={this.printModal}
                                hideTitle={false}
                            >
                                {i18next.t('print')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref="formPrintTaskModal"
                        name="formPrintTaskModal"
                        className="form-container"
                        onValidSubmit={this.handleValidSubmit}
                        onValidSubmitIgnoreRequired={
                            this.handleValidSubmitWithoutRequire
                        }
                        onInvalidSubmit={this.handleInvalidSubmit}
                        oneColumn
                    >
                        <SelectBox
                            ref="printchoise"
                            key="printchoise"
                            name="printchoise"
                            label={`${i18next.t('choosePrint')}:`}
                            value={
                                this.state.dbPrints[0] || {
                                    value: -1,
                                    title: i18next.t('defaultPrintProcess'),
                                }
                            }
                            options={this.state.printTemplateState.concat(
                                this.state.dbPrints,
                            )}
                            nullable={false} // no empty choise
                        />
                        <SelectBox
                            ref="printtype"
                            key="printtype"
                            name="printtype"
                            label={`${i18next.t('printType')}:`}
                            value={{
                                value: 'printer',
                                title: i18next.t('defaultPrintProcess'),
                            }}
                            options={printTypeOptions}
                            nullable={false} // no empty choise
                            toShowGroups={{
                                printer: 'printer',
                                doc: 'doc',
                                pdf: 'pdf',
                                html: 'html',
                            }}
                        />
                        <Checkbox
                            key="saveAsAttach"
                            label={`${i18next.t('saveAsAttachment')}:`}
                            text=""
                            visibilityGroups={['doc', 'pdf', 'html']}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

PrintCaseModal.propTypes = {
    id: PropTypes.number,
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    taskId: PropTypes.string,
    caseId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    fileName: PropTypes.string.isRequired,
    parent: PropTypes.object.isRequired,
};

export default PrintCaseModal;
