import PropTypes from 'prop-types';
import i18next from 'i18next';
import { TextArea } from '../../components/form/textArea.react';

import React from 'react';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';

class MultiBoxSingleInBulkModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            loading: false,
        };

        this.closeModal = this.closeModal.bind(this);
        this.add = this.add.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleInvalidSubmit = this.handleInvalidSubmit.bind(this);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    add(e) {
        if (e) e.preventDefault();

        this.onFormAction = function (data) {
            // prevent to add values if textArea is focused
            if (document.activeElement.tagName != 'TEXTAREA') {
                const variables = this.refs.variables.getValue();
                if (typeof variables !== 'undefined') {
                    this.props.parent.addValues(variables);
                }
                this.closeModal();
            }
        };

        this.onFormActionInvalid = function () {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        };

        this.refs.formVariable.submit(); // -> handleValidSubmit() / handleValidSubmitWithoutRequire() / handleInvalidSubmit()
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleInvalidSubmit() {
        this.onFormActionInvalid();
    }

    render() {
        let validations = {};
        let validationErrors = {};
        let placeholder;

        if (this.props.validationType == 'LD') {
            validations = { everyRowIsDate: true };
            validationErrors = { everyRowIsDate: i18next.t('invalidDate') };
            placeholder = i18next.t('dateFormat');
        } else if (this.props.validationType == 'LN') {
            validations = { everyRowIsInt: true };
            validationErrors = { everyRowIsInt: i18next.t('notIntNumber') };
        }

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.add}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsBulk" inModal>
                            <TabsButton
                                key="insert"
                                icon="icon-check-2"
                                isActive
                                onClick={this.add}
                                hideTitle={false}
                            >
                                {i18next.t('insert')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        ref="formVariable"
                        name="formVariable"
                        className="form-container"
                        onValidSubmit={this.handleValidSubmit}
                        onInvalidSubmit={this.handleInvalidSubmit}
                        oneColumn
                    >
                        <TextArea
                            key="variables"
                            ref="variables"
                            rows={12}
                            validationErrors={validationErrors}
                            validations={validations}
                            placeholder={placeholder}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

MultiBoxSingleInBulkModal.propTypes = {
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    onClose: PropTypes.func,
    parent: PropTypes.object,
};

export default MultiBoxSingleInBulkModal;
