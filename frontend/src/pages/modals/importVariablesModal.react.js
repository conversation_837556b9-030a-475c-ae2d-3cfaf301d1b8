import i18next from 'i18next';
import PropTypes from 'prop-types';
import Dropzone from '../../components/dropzone.react';
import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import LoggedUserStore from '../../flux/loggedUser.store';

class ImportVariablesModal extends React.Component {

    constructor(props) {
        super(props);
        this.state = _.extend({}, LoggedUserStore.getState());

        this.closeModal = this.closeModal.bind(this);
        this.onLoad = this.onLoad.bind(this);
    }

    // drag&drop or select from filesystem
    onLoad(file, readerEvt, isClassDiagram) {
        const fileReader = new FileReader();

        fileReader.onload = (event) => {
            const fileContent = event.target.result;

            try {
                this.closeModal();

                this.props.onLoad(fileContent, isClassDiagram);
            } catch (e) {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('templateImportFailed'),
                    show: true,
                    allowCountdown: true,
                    serverError: e,
                });
            }
        };

        fileReader.readAsText(file);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    render() {
        return (
            <Modal
                isOpen={this.props.isOpen}
                width={this.props.width}
                onEsc={this.closeModal}
                loading={false}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsImportTemplate" inModal>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <div className="row">
                        <div className="small-6 columns">
                            <span className="import-title">
                                {i18next.t('importClassDiagram')}
                            </span>
                        </div>
                        <div className="small-6 columns">
                            <span className="import-title">
                                {i18next.t('importObjectStates')}
                            </span>
                        </div>
                        <div className="small-6 columns">
                            <Dropzone
                                onLoad={(file, e) => this.onLoad(file, e, true)}
                                className="dropzoneAttach"
                                activeClassName="dropzoneAttachActive"
                                multiple={false}
                            >
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: i18next.t('dragAddFile', {
                                            here: `<b>${i18next.t('here')}</b>`,
                                        }),
                                    }}
                                />
                            </Dropzone>
                        </div>
                        <div className="small-6 columns">
                            <Dropzone
                                onLoad={(file, e) =>
                                    this.onLoad(file, e, false)}
                                className="dropzoneAttach"
                                activeClassName="dropzoneAttachActive"
                                multiple={false}
                            >
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: i18next.t('dragAddFile', {
                                            here: `<b>${i18next.t('here')}</b>`,
                                        }),
                                    }}
                                />
                            </Dropzone>
                        </div>
                    </div>
                </div>
            </Modal>
        );
    }

}

ImportVariablesModal.propTypes = {
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    onLoad: PropTypes.func.isRequired,
};

export default ImportVariablesModal;
