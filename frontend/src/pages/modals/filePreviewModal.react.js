import PropTypes from 'prop-types';
import i18next from 'i18next';
import { RemCalculator } from '../../common/utils';
import Preview from '../../components5.0/preview/Preview';

import React from 'react';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class FilePreviewModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            loading: false,
        };

        this.preview = React.createRef();

        this.closeModal = this.closeModal.bind(this);
    }

    componentDidMount() {
        const { file } = this.props;
        file.dmsf_id = file.id;
        // file.name = file.fileName;
        file.value = file.fileName;
        setTimeout(() => {
            // due to this.preview.current is null
            this.preview.current.changePreview(file);
        });
    }

    closeModal(e) {
        if (e) {
            e.preventDefault();
        }
        this.props.onClose();
    }

    render() {
        const rCalc = new RemCalculator();
        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                modalOverflow
                draggable={this.props.draggable}
            >
                <div className="modal file-preview-modal">
                    <div
                        className="row modal-draggable"
                        style={{
                            cursor: this.props.draggable ? 'move' : 'default',
                        }}
                    >
                        <TabsButtonsOther key="buttonsFilePreview" inModal>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form key="formFilePreview" name="formFilePreview">
                        <Preview
                            side="center"
                            standalone
                            previewId="modal-file-preview"
                            fileUrl={this.props.fileUrl}
                            fixedHeight={`${rCalc.remCalc(window.innerHeight - rCalc.pxScale(295))}rem`}
                            withControlIcons
                            defaultZoom={this.props.defaultZoom}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

FilePreviewModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    file: PropTypes.object.isRequired,
    defaultZoom: PropTypes.number,
};

FilePreviewModal.defaultProps = {
    defaultZoom: 1,
};

export default FilePreviewModal;
