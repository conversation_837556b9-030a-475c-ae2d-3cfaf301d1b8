import React, { useRef, useState, useEffect } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import _ from 'lodash';
import { Dialog } from '@mui/material';
import { SelectBox } from '../../components/form/selectBox.react';
import { Text } from '../../components/form/text.react';
import { TextNum } from '../../components/form/textNum.react';
import { TextArea } from '../../components/form/textArea.react';
import Label from '../../components/form/label.react';
import ShowHideComponent from '../../components/form/showHideComponent.react';
import LoggedUserStore from '../../flux/loggedUser.store';
import AlertsActions from '../../components/alerts/alerts.actions';
import { Checkbox } from '../../components/form/checkbox.react';
import { checkLangMutation } from '../../common/utils';
import SelectDropPart from '../../components/form/selectDropPart.react';
import SelectDrop from '../../components/form/selectDrop.react';
import { Calendar } from '../../components/form/calendar.react';
import ApiRequest from '../../api/apiRequest';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Tabs from '../../components/tabs/tabs.react';
import TabsWrapper from '../../components/tabs/tabsWrapper.react';
import Form from '../../components/form/form.react';

const ServiceOperationsModal = (props) => {
    const formServiceOperations = useRef(null);
    const [items, setItems] = useState({});
    const [isPristine, setIsPristine] = useState(true);
    const [loading, setLoading] = useState(true);
    const [dataDownloaded, setDataDownloaded] = useState(false);
    const [headerOptions, setHeaderOptions] = useState([]);
    const [itaskEventOptions, setItaskEventOptions] = useState([]);
    const [ttaskOptions, setTtaskOptions] = useState([]);
    const [tvarOptions, setTvarOptions] = useState([]);
    const [logosOptions, setLogosOptions] = useState([]);
    const [dynTableOptions, setDynTableOptions] = useState([]);
    const [dynTableColumnOptions, setDynTableColumnOptions] = useState([]);
    const [templateBoxesDisabled, setTemplateBoxesDisabled] = useState(false);
    const { languages } = LoggedUserStore.getState();

    const itaskStatusOptions = [
        { value: 'A', title: `${i18next.t('taskStatusA')} (A)` },
        { value: 'D', title: `${i18next.t('finished')} (D)` },
        { value: 'L', title: `${i18next.t('taskStatusL')} (L)` },
        { value: 'N', title: `${i18next.t('taskStatusN')} (N)` },
        { value: 'T', title: `${i18next.t('taskStatusT')} (T)` },
        { value: 'W', title: `${i18next.t('taskStatusW')} (W)` },
    ];

    const itaskAssesmentHierarchyOptions = [
        { value: 'G', title: `${i18next.t('assHierarchyGuarantor')} (G)` },
        { value: 'C', title: `${i18next.t('assHierarchyChildren')} (C)` },
        { value: 'D', title: `${i18next.t('assHierarchyDescendants')} (D)` },
        { value: 'P', title: `${i18next.t('assHierarchyParent')} (P)` },
        { value: 'A', title: `${i18next.t('assHierarchyAncestors')} (A)` },
        { value: 'S', title: `${i18next.t('assHierarchySiblings')} (S)` },
        { value: 'L', title: `${i18next.t('unspecified')} (L)` },
    ];

    const itaskAssesmentMethodOptions = [
        { value: 'S', title: `${i18next.t('assMethodSelect')} (S)` },
        { value: 'U', title: `${i18next.t('ASSESMENT_METHOD_SELECT_BY')} (U)` },
        { value: 'T', title: `${i18next.t('assMethodAutomatic')} (T)` },
        { value: 'W', title: `${i18next.t('assMethodLeast')} (W)` },
        { value: 'C', title: `${i18next.t('assMethodLastSolverChoice')} (C)` },
        { value: 'L', title: `${i18next.t('assMethodLastSolver')} (L)` },
        { value: 'A', title: `${i18next.t('assMethodPull')} (A)` },
        { value: 'V', title: `${i18next.t('assMethodVariable')} (V)` },
        { value: 'P', title: `${i18next.t('ASSESMENT_METHOD_OWNER')} (P)` },
    ];

    const itaskDueOffsetOptions = [
        { value: 'no', title: `${i18next.t('tTaskDueOffsetNone')} (no)` },
        { value: 'po', title: `${i18next.t('tTaskDueOffsetPO')} (po)` },
        { value: 'ps', title: `${i18next.t('tTaskDueOffsetPS')} (ps)` },
        { value: 'ts', title: `${i18next.t('tTaskDueOffsetTS')} (ts)` },
        { value: 'vo', title: `${i18next.t('tTaskDueOffsetVO')} (vo)` },
        { value: 'vc', title: `${i18next.t('tTaskDueOffsetVC')} (vc)` },
    ];

    const itaskDurationOptions = [
        { value: 'no', title: `${i18next.t('isNot')} (no)` },
        { value: 'po', title: `${i18next.t('tTaskDueOffsetPO')} (po)` },
        { value: 'ps', title: `${i18next.t('tTaskDueOffsetPS')} (ps)` },
        { value: 'ts', title: `${i18next.t('tTaskDueOffsetTS')} (ts)` },
        { value: 'vo', title: `${i18next.t('tTaskDueOffsetVO')} (vo)` },
        { value: 'vc', title: `${i18next.t('tTaskDueOffsetVC')} (vc)` },
    ];

    const itaskAgainOptions = [
        { value: 'N', title: i18next.t('continueSolving') },
        { value: 'A', title: i18next.t('reEvaluates') },
    ];

    const iprocStatusOptions = [
        { value: 'A', title: `${i18next.t('active')} (A)` },
        { value: 'D', title: `${i18next.t('finished')} (D)` },
        { value: 'N', title: `${i18next.t('inactive')} (N)` },
        { value: 'S', title: `${i18next.t('suspended')} (S)` },
        { value: 'X', title: `${i18next.t('statusErrored')} (X)` },
        { value: 'E', title: `${i18next.t('erased')} (E)` },
    ];

    const iprocDmsVisibilityOptions = [
        { value: '-null-', title: i18next.t('dmsVisNull') },
        { value: 'M', title: i18next.t('dmsVisSup') },
        { value: 'S', title: i18next.t('dmsVisSub') },
        { value: 'SM', title: i18next.t('dmsVisSupSub') },
    ];

    const userStatusOptions = [
        { value: 'A', title: `${i18next.t('active')} (A)` },
        { value: 'L', title: `${i18next.t('locked')} (L)` },
        { value: 'D', title: `${i18next.t('deleted')} (D)` },
    ];

    const orgStatusOptions = [
        { value: 'A', title: `${i18next.t('active')} (A)` },
        { value: 'D', title: `${i18next.t('deleted')} (D)` },
    ];

    const ivarTypeOptions = [
        { value: 'T', title: `${i18next.t('ATTR_TEXT')} (T)` },
        { value: 'LT', title: `${i18next.t('ATTR_TEXT_LIST')} (LT)` },
        { value: 'D', title: `${i18next.t('ATTR_DATE')} (D)` },
        { value: 'LD', title: `${i18next.t('ATTR_DATE_LIST')} (LD)` },
        { value: 'N', title: `${i18next.t('ATTR_NUMBER')} (N)` },
        { value: 'LN', title: `${i18next.t('ATTR_NUMBER_LIST')} (LN)` },
        { value: 'DL', title: `${i18next.t('ATTR_DYNAMIC_LIST')} (DL)` },
        { value: 'DT', title: `${i18next.t('ATTR_DYNAMIC_TABLE')} (DT)` },
        { value: 'B', title: `${i18next.t('ATTR_BIG')} (B)` },
        { value: 'DR', title: `${i18next.t('ATTR_DYNAMIC_ROW')} (DR)` },
    ];

    const ivarAttributeOptions = [
        { value: 'M', title: `${i18next.t('ATTR_MULTILINE')} (M)` },
        { value: 'S', title: `${i18next.t('ATTR_USER_DEFINED')} (S)` },
        { value: 'U', title: `${i18next.t('ATTR_USER')} (U)` },
        { value: 'O', title: `${i18next.t('ATTR_ORG_STRUCT')} (O)` },
        { value: 'R', title: `${i18next.t('ATTR_ROLE')} (R)` },
        { value: 'F', title: `${i18next.t('ATTR_FILE')} (F)` },
        { value: 'V', title: `${i18next.t('ATTR_STATE_VAR')} (V)` },
    ];

    const getEventOptions = () => {
        ApiRequest.get(
            `/event-definition?order=evedef_name&sort=asc&limit='${config.restLimit}'`,
        )
            .then((payloadEvent) => {
                const events = [];

                if (Array.isArray(payloadEvent?.items)) {
                    payloadEvent.items.forEach((item) => {
                        events.push({
                            value: item.evedef_name,
                            title: item.evedef_name,
                        });
                    });
                }

                setItaskEventOptions(events);
            })
            .catch((error) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: error,
                });
            });
    };

    const getTtaskOptions = (tprocId) => {
        ApiRequest.get(
            `/template-processes/${tprocId}/template-tasks?order=ttask_name&sort=asc&limit=${config.restLimit}`,
        )
            .then((payloadTasks) => {
                const templateTasksArr = [];

                if (Array.isArray(payloadTasks?.items)) {
                    payloadTasks.items.forEach((item) => {
                        templateTasksArr.push({
                            value: String(item.id),
                            title: checkLangMutation(item, 'ttask_name'),
                        });
                    });
                }

                setTtaskOptions(templateTasksArr);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTemplTsksLoadFailed'),
                    serverError: errorMessage,
                });
            });
    };

    const getTvarOptions = (tprocId) => {
        ApiRequest.get(
            `/template-processes/${tprocId}/1/template-variables?order=tvar_name&sort=asc&limit=${config.restLimit}`,
        )
            .then((payloadVars) => {
                const templateVarsAllArr = [];

                if (Array.isArray(payloadVars?.items)) {
                    payloadVars.items.forEach((item) => {
                        templateVarsAllArr.push({
                            value: String(item.id),
                            title: item.tvar_name,
                            type: item.tvar_type,
                            tvar_copy_snapshot: item.tvar_copy_snapshot,
                        });
                    });
                }

                setTvarOptions(templateVarsAllArr);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTempVarsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    };

    const getHeaderOptions = (tprocId) => {
        ApiRequest.get(`/template-processes/${tprocId}/1/headers`)
            .then((payloadHeaders) => {
                const headers = [];

                if (Array.isArray(payloadHeaders?.items)) {
                    payloadHeaders?.items.forEach((header) => {
                        headers.push({
                            value: header.header_id,
                            title: checkLangMutation(header, 'header_name'),
                        });
                    });
                }

                setHeaderOptions(headers);
            })
            .catch((error) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: error,
                });
            });
    };

    const getLogosOptions = () => {
        ApiRequest.get(`${document.location.origin}/logos`)
            .then((payload) => {
                if (payload) {
                    setLogosOptions(payload);
                }
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrLogosLoadFailed'),
                    serverError: errorMessage,
                });
            });
    };

    const getDynTableOptions = () => {
        ApiRequest.get(`/dyn-table?order=dt_name&limit=${config.restLimit}`)
            .then((payload) => {
                const dynTables = [];

                if (Array.isArray(payload?.items)) {
                    payload.items.forEach((item) => {
                        dynTables.push({
                            value: item.id,
                            title: item.dt_name,
                        });
                    });
                }

                setDynTableOptions(dynTables);
            })
            .catch((error) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: error,
                });
            });
    };

    const getDynTableColumnOptions = (tableId, idIsName) => {
        function fetchColumns(id) {
            ApiRequest.get(
                `/dyn-table/${id}/cols?order=dtc_name&limit=${config.restLimit}`,
            )
                .then((payload) => {
                    const columns = [];

                    if (Array.isArray(payload?.items)) {
                        payload.items.forEach((item) => {
                            columns.push({
                                value: item.dtc_id,
                                title: item.dtc_name,
                            });
                        });
                    }

                    setDynTableColumnOptions(columns);
                })
                .catch((error) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedData'),
                        serverError: error,
                    });
                });
        }

        if (!idIsName) {
            fetchColumns(tableId);
        } else {
            ApiRequest.get(
                `/dyn-table?filter=${encodeURIComponent(`dt_name<eq>"${tableId}"`)}&order=dt_name&sort=asc&limit=1`,
            )
                .then((payload) => {
                    if (!_.isEmpty(payload.items)) {
                        fetchColumns(payload.items[0].id);
                    }
                })
                .catch((error) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedData'),
                        serverError: error,
                    });
                });
        }
    };

    const checkTprocIdsDifferent = (objArr) => {
        const tprocIdInitial =
            objArr[0]['tii.tproc_id'] ||
            objArr[0]['ip.tproc_id'] ||
            objArr[0]['ivipi.tproc_id'] ||
            objArr[0]['tproc_id'];

        for (let i = 1; i < objArr.length; i += 1) {
            const tprocId =
                objArr[i]['tii.tproc_id'] ||
                objArr[i]['ip.tproc_id'] ||
                objArr[i]['ivipi.tproc_id'] ||
                objArr[i]['tproc_id'];
            if (tprocId !== tprocIdInitial) {
                return true;
            }
        }

        return false;
    };

    const getColumnList = () => {
        const { recordsType, colsForFilter } = props;
        let columns = [];

        // list of columns
        if (recordsType === 'tasks') {
            columns = [
                'IT.ITASK_ID',
                'IT.IPROC_ID',
                'TII.TPROC_ID',
                'IT.ITASK_STATUS',
                'IT.ITASK_USER_ID',
                'IT.ITASK_ASSESMENT_USER_ID',
                'IT.ITASK_EVENT',
                'IT.ITASK_ASSESMENT_HIERARCHY',
                'IT.ITASK_ASSESMENT_METHOD',
                'IT.ITASK_ASSESMENT_ROLE_ID',
                'IT.ITASK_ASSESMENT_ORGSTR_CNST',
                'IT.ITASK_ASSESMENT_ORGSTR_ID',
                'IT.ITASK_ASSESMENT_TTASK_ID',
                'IT.ITASK_ASSESMENT_TVAR_ID',
                'IT.ITASK_REFERENCE_USER',
                'IT.ITASK_AUTO_START',
                'IT.ITASK_DUE_OFFSET',
                'IT.ITASK_DURATION',
                'IT.ITASK_COMMENT',
                'IT.ITASK_AGAIN',
            ];

            languages.forEach((lang, i) => {
                columns.push(`IT.ITASK_COMMENT_${lang.toUpperCase()}`);
            });
        } else if (recordsType === 'cases') {
            columns = [
                'IP.IPROC_ID',
                'IP.TPROC_ID',
                'IP.IPROC_NAME',
                'IP.IPROC_CASE_STATUS',
                'IP.IPROC_INST_OWNER_USER_ID',
                'IP.IPROC_MAIN_IPROC_ID',
                'IP.IPROC_STATUS',
                'IP.IPROC_SHREDDED',
                'IP.IPROC_VIS_ROLE_ID',
                'IP.IPROC_HR_ROLE_ID',
                'IP.IPROC_SUMMARY',
                'IP.IPROC_DMS_VISIBILITY',
                'IP.HEADER_ID',
            ];

            languages.forEach((lang, i) => {
                columns.push(`IP.IPROC_SUMMARY_${lang.toUpperCase()}`);
            });
        } else if (recordsType === 'casesByTasks') {
            columns = [
                'TII.IPROC_ID',
                'TII.TPROC_ID',
                'TII.IPROC_NAME',
                'TII.IPROC_CASE_STATUS',
                'TII.IPROC_INST_OWNER_USER_ID',
                'TII.IPROC_MAIN_IPROC_ID',
                'TII.IPROC_STATUS',
                'TII.IPROC_SHREDDED',
                'TII.IPROC_VIS_ROLE_ID',
                'TII.IPROC_HR_ROLE_ID',
                'TII.IPROC_SUMMARY',
                'TII.IPROC_DMS_VISIBILITY',
                'TII.HEADER_ID',
            ];

            languages.forEach((lang, i) => {
                columns.push(`TII.IPROC_SUMMARY_${lang.toUpperCase()}`);
            });
        } else if (recordsType === 'users') {
            columns = [
                'U.USER_ID',
                'U.USER_NAME',
                'U.USER_FIRST_NAME',
                'U.USER_LAST_NAME',
                'U.USER_DISPLAY_NAME',
                'U.USER_PASSWORD',
                'U.USER_STATUS',
                'U.USER_EMAIL',
                // 'U.USER_PHOTO',
                'U.ORG_ID',
                'U.USER_CHANGE_PASSWORD',
                'U.USER_PASSWORD_LAST_CHANGE',
                'U.USER_BAD_LOGIN_COUNT',
                'U.USER_TITLE_PREFIX',
                'U.USER_TITLE_SUFFIX',
                'U.USER_EXTERNAL_LOGIN',
                'U.USER_COMP',
                'U.USER_COMP_ID',
                'U.USER_COMP_CODE',
                'U.USER_EXTERNAL_SOURCE',
                'U.EXTERNAL_ID',
                'U.LOGIN_COUNT',
            ];
        } else if (recordsType === 'roles') {
            columns = [
                'R.ROLE_ID',
                'R.ROLE_NAME',
                'R.ROLE_CATEGORY',
                'R.ROLE_NOTE',
                'R.ROLE_MAX_ASSIGNS',
                'R.ROLE_ACCESS_ROLE_ID',
                'R.ORG_ID',
            ];
        } else if (recordsType === 'orgUnits') {
            columns = [
                'OS.ORGSTR_ID',
                'OS.ORGSTR_NAME',
                'OS.EXTERNAL_ID',
                'OS.PARENT_IC',
                'OS.ORGANIZATION_TYPE',
                'OS.ADDITIONAL_ID',
                'OS.EXTERNAL_STATUS',
                'OS.COMPANY_IC',
                'OS.ORGANIZATION_STATUS',
                'OS.ORG_ID',
                'OS.LOGO_URL',
                'OS.PARENT_ORGSTR_ID',
                'OS.MANAGER_USER_ID',
            ];
        } else if (recordsType === 'userParams') {
            columns = [
                'UP.USRPAR_ID',
                'UP.USER_ID',
                'UP.USRPAR_NAME',
                'UP.USRPAR_CLIENT',
                'UP.USRPAR_VALUE',
                'UP.USRPAR_BIG_VALUE',
                'UP.ORG_ID',
            ];
        } else if (recordsType === 'usersByUserParams') {
            columns = [
                'UPUI.USER_ID',
                'UPUI.USER_NAME',
                'UPUI.USER_FIRST_NAME',
                'UPUI.USER_LAST_NAME',
                'UPUI.USER_DISPLAY_NAME',
                'UPUI.USER_PASSWORD',
                'UPUI.USER_STATUS',
                'UPUI.USER_EMAIL',
                // 'UPUI.USER_PHOTO',
                'UPUI.ORG_ID',
                'UPUI.USER_CHANGE_PASSWORD',
                'UPUI.USER_PASSWORD_LAST_CHANGE',
                'UPUI.USER_BAD_LOGIN_COUNT',
                'UPUI.USER_TITLE_PREFIX',
                'UPUI.USER_TITLE_SUFFIX',
                'UPUI.USER_EXTERNAL_LOGIN',
                'UPUI.USER_COMP',
                'UPUI.USER_COMP_ID',
                'UPUI.USER_COMP_CODE',
                'UPUI.USER_EXTERNAL_SOURCE',
                'UPUI.EXTERNAL_ID',
                'UPUI.LOGIN_COUNT',
            ];
        } else if (recordsType === 'ivars') {
            columns = [
                'IV.IVAR_ID',
                'IV.IPROC_ID',
                'IV.IVAR_NAME',
                'IV.IVAR_TYPE',
                'IV.IVAR_MULTITASK_BEHAVIOUR',
                'IV.IVAR_TEXT_VALUE',
                'IV.IVAR_NUMBER_VALUE',
                'IV.IVAR_DATE_VALUE',
                'IV.IVAR_ATTRIBUTE',
                'IV.TVAR_ID',
                'IV.DLIST_NAME',
                'IV.IVAR_MULTI',
                'IV.IVAR_MULTI_SELECTED',
                'IV.IVAR_BIG_VALUE',
                'IV.IVAR_DT_INDEX',
                'IV.IVAR_COL_INDEX',
                'IV.ORG_ID',
                'IV.IVAR_CLASS',
                'IVIPI.TPROC_ID',
            ];

            languages.forEach((lang, i) => {
                columns.push(`IV.IVAR_NAME_${lang.toUpperCase()}`);
            });
        } else if (recordsType === 'casesByIvars') {
            columns = [
                'IVIPI.IPROC_ID',
                'IVIPI.TPROC_ID',
                'IVIPI.IPROC_NAME',
                'IVIPI.IPROC_CASE_STATUS',
                'IVIPI.IPROC_INST_OWNER_USER_ID',
                'IVIPI.IPROC_MAIN_IPROC_ID',
                'IVIPI.IPROC_STATUS',
                'IVIPI.IPROC_SHREDDED',
                'IVIPI.IPROC_VIS_ROLE_ID',
                'IVIPI.IPROC_HR_ROLE_ID',
                'IVIPI.IPROC_SUMMARY',
                'IVIPI.IPROC_DMS_VISIBILITY',
                'IVIPI.HEADER_ID',
            ];

            languages.forEach((lang, i) => {
                columns.push(`IVIPI.IPROC_SUMMARY_${lang.toUpperCase()}`);
            });
        }

        // t3b-2763 Servisní operace - filtrace alias sloupců
        if (colsForFilter) {
            colsForFilter.forEach((col) => {
                if (!columns.includes(col)) {
                    columns.push(col);
                }
            });
        }

        return columns;
    };

    useEffect(() => {
        if (loading && dataDownloaded) {
            setLoading(false);
        }
    }, [dataDownloaded]);

    useEffect(() => {
        const { recordsType, selectedIds, tableRef } = props;
        const urlNamesObj = {
            tasks: 'tasks',
            casesByTasks: 'tasks',
            cases: 'processes',
            users: 'users',
            roles: 'roles',
            orgUnits: 'organization-structure',
            userParams: 'user-parameters',
            usersByUserParams: 'user-parameters',
            ivars: 'instance-variables',
            casesByIvars: 'instance-variables',
        };

        // single row is selected
        if (!dataDownloaded && selectedIds && !Array.isArray(selectedIds)) {
            const columns = getColumnList();

            // whole url
            const filter = `id<eq>"${selectedIds}"`;
            const url = `/service-operations/${urlNamesObj[recordsType]}/list?columns=${encodeURIComponent(columns.join(','))}&filter=${encodeURIComponent(filter)}`;

            // get entity data and fill in the form
            ApiRequest.get(url)
                .then((payload) => {
                    const data = payload.items[0];

                    if (data) {
                        const obj = {};
                        columns.forEach((column, i) => {
                            const lower = column.toLowerCase();
                            const lowerSplit = lower.split('.')[1];
                            obj[lowerSplit] = { value: data[lower] };
                        });

                        if (recordsType === 'tasks') {
                            getTtaskOptions(data['tii.tproc_id']);
                            getTvarOptions(data['tii.tproc_id']);
                            getEventOptions();
                        } else if (recordsType === 'cases') {
                            getHeaderOptions(data['ip.tproc_id']);
                        } else if (recordsType === 'casesByTasks') {
                            getHeaderOptions(data['tii.tproc_id']);
                        } else if (recordsType === 'ivars') {
                            getTvarOptions(data['ivipi.tproc_id']);
                            getDynTableOptions();
                            if (obj.dlist_name?.value) {
                                getDynTableColumnOptions(
                                    obj.dlist_name.value,
                                    true,
                                );
                            }
                        } else if (recordsType === 'casesByIvars') {
                            getHeaderOptions(data['ivipi.tproc_id']);
                        } else if (recordsType === 'orgUnits') {
                            getLogosOptions();
                        }

                        setItems(obj);
                    } else {
                        AlertsActions.addAlert({
                            type: 'alert',
                            message: i18next.t('alrNoDataFound'),
                        });
                    }

                    setDataDownloaded(true);
                })
                .catch((error) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedData'),
                        serverError: error,
                    });
                });

            // multiple rows are selected
        } else if (
            !dataDownloaded &&
            selectedIds &&
            Array.isArray(selectedIds)
        ) {
            // search taskId/caseId array if tproc_id is the same, then download data
            // if template not the same, disable variables, ttasks, headers
            const disable = checkTprocIdsDifferent(selectedIds);

            if (disable) {
                setTemplateBoxesDisabled(true);
            } else if (recordsType === 'tasks') {
                getTtaskOptions(selectedIds[0].tproc_id);
                getTvarOptions(selectedIds[0].tproc_id);
            } else if (
                recordsType === 'cases' ||
                recordsType === 'casesByTasks' ||
                recordsType === 'casesByIvars'
            ) {
                getHeaderOptions(selectedIds[0].tproc_id);
            } else if (recordsType === 'ivars') {
                getTvarOptions(selectedIds[0].tproc_id);
            }

            if (recordsType === 'tasks') {
                getEventOptions();
            } else if (recordsType === 'ivars') {
                getDynTableOptions();
            } else if (recordsType === 'orgUnits') {
                getLogosOptions();
            }

            setDataDownloaded(true);

            // no rows are selected (selected all based on filter)
        } else if (!dataDownloaded && !props.selectedIds) {
            const tableState = tableRef.state;
            const columns = getColumnList();

            if (recordsType === 'tasks') {
                getEventOptions();
            } else if (recordsType === 'ivars') {
                getDynTableOptions();
            } else if (recordsType === 'orgUnits') {
                getLogosOptions();
            }

            // if tasks, cases or ivars: download data to check if tproc_id is the same
            if (!_.isEmpty(columns)) {
                let url = `/service-operations/${urlNamesObj[recordsType]}/list?columns=${encodeURIComponent(columns.join(','))}`;

                // add filter
                const tableFilter = tableState.filterTexts;
                const userFilter = !_.isEmpty(tableState.activeFilters)
                    ? tableState.activeFilters.filtersparams
                    : null;

                const filterArr = [];
                if (tableFilter) {
                    filterArr.push(tableFilter);
                }
                if (userFilter) {
                    filterArr.push(userFilter);
                }
                if (!_.isEmpty(filterArr)) {
                    url += `&filter=${encodeURIComponent(filterArr.join('<and>'))}`;
                }

                ApiRequest.get(url)
                    .then((payload) => {
                        if (!_.isEmpty(payload.items)) {
                            const disable = checkTprocIdsDifferent(
                                payload.items,
                            );

                            if (disable) {
                                setTemplateBoxesDisabled(true);
                            } else if (recordsType === 'tasks') {
                                getTtaskOptions(
                                    payload.items[0]['tii.tproc_id'],
                                );
                                getTvarOptions(
                                    payload.items[0]['tii.tproc_id'],
                                );
                            } else if (recordsType === 'casesByTasks') {
                                getHeaderOptions(
                                    payload.items[0]['tii.tproc_id'],
                                );
                            } else if (recordsType === 'cases') {
                                getHeaderOptions(
                                    payload.items[0]['ip.tproc_id'],
                                );
                            } else if (recordsType === 'ivars') {
                                getTvarOptions(
                                    payload.items[0]['ivipi.tproc_id'],
                                );
                            } else if (recordsType === 'casesByIvars') {
                                getHeaderOptions(
                                    payload.items[0]['ivipi.tproc_id'],
                                );
                            }
                        } else {
                            AlertsActions.addAlert({
                                type: 'alert',
                                message: i18next.t('alrNoDataFound'),
                            });
                        }

                        setDataDownloaded(true);
                    })
                    .catch((error) => {
                        AlertsActions.addAlert({
                            type: 'alert',
                            message: i18next.t('alrFailedData'),
                            serverError: error,
                        });
                    });
            } else {
                setDataDownloaded(true);
            }
        }
    }, [props.selectedIds]);

    const closeModal = (e) => {
        if (e) e.preventDefault();
        props.onClose();
    };

    const saveStatus = (e) => {
        if (e) e.preventDefault();
        formServiceOperations.current.submit();
    };

    const handleSubmit = (data) => {
        const postObj = { data: {} };
        Object.keys(items).forEach((key) => {
            if (items[key].change) {
                postObj.data[key] = items[key].value;
            }
        });

        if (props.recordsType === 'ivars' && items['dlist_name']?.change) {
            postObj.data['dlist_name'] = items['dlist_name']?.title;
            // postObj.data['ivar_dt_index'] = items['dlist_name'].value;
            postObj.data['ivar_col_index'] = items['ivar_col_index']?.value;
        }

        props.onSave(postObj);
    };

    const handleChange = (name, value) => {
        if (!loading) {
            const checkValue =
                typeof value === 'object' && value !== null
                    ? value.value
                    : value;
            const newValue = { value: checkValue, change: items[name]?.change };

            setItems({ ...items, [name]: newValue });
            setIsPristine(false);
        }
    };

    const handleChangeDT = (name, value) => {
        if (!loading && name === 'dlist_name') {
            const inputValue = value?.value;
            const inputTitle = value?.title;

            if (inputValue) {
                getDynTableColumnOptions(inputValue);
            }

            const newDTName = {
                value: inputValue,
                title: inputTitle,
                change: items['dlist_name']?.change,
            };
            // const newDTIndex = { value: inputValue, change: items['dlist_name'].change };
            const newDTColumn = {
                value: null,
                change: items['dlist_name']?.change,
            };

            setItems({
                ...items,
                dlist_name: newDTName,
                // ivar_dt_index: newDTIndex,
                ivar_col_index: newDTColumn,
            });
            setIsPristine(false);
        }
    };

    const handleCheckbox = (name) => {
        if (!loading) {
            const newValue = {
                value: items[name]?.value || null,
                change: !items[name]?.change,
            };

            setItems({ ...items, [name]: newValue });
            setIsPristine(false);
        }
    };

    const getFormInputs = () => {
        const { recordsType } = props;

        const getAdditionalButton = (name) => (
            <span
                key={`${name}_change`}
                className={
                    items[name]?.change
                        ? 'change-button icon-delete-1'
                        : 'change-button icon-pencil-2'
                }
                title={i18next.t('change')}
                onClick={() => handleCheckbox(name)}
            />
        );

        const formInputs = [
            <Label
                key="label"
                label={i18next.t('serviceOperationsInfo')}
                name="label"
                fullLabel
                noValueHeight
            />,
        ];

        if (recordsType === 'tasks') {
            formInputs.push(
                <SelectBox
                    key="itask_status"
                    name="itask_status"
                    label={`${i18next.t('state')}:`}
                    title="itask_status"
                    options={itaskStatusOptions}
                    value={items.itask_status?.value}
                    onChange={handleChange}
                    required={items.itask_status?.change}
                    readonly={!items.itask_status?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('itask_status')}
                />,
                <SelectBox
                    key="itask_user_id"
                    name="itask_user_id"
                    label={`${i18next.t('tskSolver')}:`}
                    title="itask_user_id"
                    selectBoxType="DLU"
                    value={items.itask_user_id?.value}
                    onChange={handleChange}
                    readonly={!items.itask_user_id?.change}
                    additionalButton={getAdditionalButton('itask_user_id')}
                />,
                <SelectBox
                    key="itask_assesment_user_id"
                    name="itask_assesment_user_id"
                    label={`${i18next.t('supervisor')}:`}
                    title="itask_assesment_user_id"
                    selectBoxType="DLU"
                    value={items.itask_assesment_user_id?.value}
                    onChange={handleChange}
                    readonly={!items.itask_assesment_user_id?.change}
                    additionalButton={getAdditionalButton(
                        'itask_assesment_user_id',
                    )}
                />,
                <SelectBox
                    key="itask_event"
                    name="itask_event"
                    label={`${i18next.t('event')}:`}
                    title="itask_event"
                    options={itaskEventOptions}
                    value={items.itask_event?.value}
                    onChange={handleChange}
                    readonly={!items.itask_event?.change}
                    additionalButton={getAdditionalButton('itask_event')}
                />,
                <SelectBox
                    key="itask_assesment_hierarchy"
                    name="itask_assesment_hierarchy"
                    label={`${i18next.t('assHierarchy')}:`}
                    title="itask_assesment_hierarchy"
                    value={items.itask_assesment_hierarchy?.value}
                    options={itaskAssesmentHierarchyOptions}
                    onChange={handleChange}
                    required={items.itask_assesment_hierarchy?.change}
                    readonly={!items.itask_assesment_hierarchy?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton(
                        'itask_assesment_hierarchy',
                    )}
                />,
                <SelectBox
                    key="itask_assesment_method"
                    name="itask_assesment_method"
                    label={`${i18next.t('taskWillBeAssigned')}:`}
                    title="itask_assesment_method"
                    value={items.itask_assesment_method?.value}
                    options={itaskAssesmentMethodOptions}
                    onChange={handleChange}
                    required={items.itask_assesment_method?.change}
                    readonly={!items.itask_assesment_method?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton(
                        'itask_assesment_method',
                    )}
                />,
                <SelectBox
                    key="itask_assesment_role_id"
                    name="itask_assesment_role_id"
                    label={`${i18next.t('roles')}:`}
                    title="itask_assesment_role_id"
                    selectBoxType="DLR"
                    value={items.itask_assesment_role_id?.value}
                    onChange={handleChange}
                    readonly={!items.itask_assesment_role_id?.change}
                    additionalButton={getAdditionalButton(
                        'itask_assesment_role_id',
                    )}
                />,
                <SelectBox
                    key="itask_assesment_orgstr_cnst"
                    name="itask_assesment_orgstr_cnst"
                    label={`${i18next.t('onlyOrgUnit')}:`}
                    title="itask_assesment_orgstr_cnst"
                    selectBoxType="DLO"
                    value={items.itask_assesment_orgstr_cnst?.value}
                    onChange={handleChange}
                    readonly={!items.itask_assesment_orgstr_cnst?.change}
                    additionalButton={getAdditionalButton(
                        'itask_assesment_orgstr_cnst',
                    )}
                />,
                <SelectBox
                    key="itask_assesment_orgstr_id"
                    name="itask_assesment_orgstr_id"
                    label={`${i18next.t('orgUnitShe')}:`}
                    title="itask_assesment_orgstr_id"
                    selectBoxType="DLO"
                    value={items.itask_assesment_orgstr_id?.value}
                    onChange={handleChange}
                    readonly={!items.itask_assesment_orgstr_id?.change}
                    additionalButton={getAdditionalButton(
                        'itask_assesment_orgstr_id',
                    )}
                />,
            );
            if (templateBoxesDisabled) {
                formInputs.push(
                    <Label
                        key="itask_assesment_ttask_id"
                        name="itask_assesment_ttask_id"
                        label={`${i18next.t('tsk')}:`}
                        title="itask_assesment_ttask_id"
                    >
                        {i18next.t('disabledDifferentTemplates')}
                    </Label>,
                    <Label
                        key="itask_assesment_tvar_id"
                        name="itask_assesment_tvar_id"
                        label={`${i18next.t('variable')}:`}
                        title="itask_assesment_tvar_id"
                    >
                        {i18next.t('disabledDifferentTemplates')}
                    </Label>,
                );
            } else {
                formInputs.push(
                    <SelectBox
                        key="itask_assesment_ttask_id"
                        name="itask_assesment_ttask_id"
                        label={`${i18next.t('tsk')}:`}
                        title="itask_assesment_ttask_id"
                        options={ttaskOptions}
                        value={items.itask_assesment_ttask_id?.value}
                        onChange={handleChange}
                        readonly={!items.itask_assesment_ttask_id?.change}
                        additionalButton={getAdditionalButton(
                            'itask_assesment_ttask_id',
                        )}
                    />,
                    <SelectBox
                        key="itask_assesment_tvar_id"
                        name="itask_assesment_tvar_id"
                        label={`${i18next.t('variable')}:`}
                        title="itask_assesment_tvar_id"
                        options={tvarOptions}
                        value={items.itask_assesment_tvar_id?.value}
                        onChange={handleChange}
                        readonly={!items.itask_assesment_tvar_id?.change}
                        additionalButton={getAdditionalButton(
                            'itask_assesment_tvar_id',
                        )}
                    />,
                );
            }
            formInputs.push(
                <Text
                    key="itask_reference_user"
                    name="itask_reference_user"
                    label={`${i18next.t('referenceUser')}:`}
                    title="itask_reference_user"
                    value={items.itask_reference_user?.value}
                    onChange={handleChange}
                    required={items.itask_reference_user?.change}
                    readonly={!items.itask_reference_user?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'itask_reference_user',
                    )}
                />,
                <Checkbox
                    key="itask_auto_start"
                    name="itask_auto_start"
                    label={`${i18next.t('itask_auto_start')}:`}
                    title="itask_auto_start"
                    value={items.itask_auto_start?.value === 'Y'}
                    onChange={(name, value) =>
                        handleChange(name, value ? 'Y' : 'N')}
                    readonly={!items.itask_auto_start?.change}
                    additionalButton={getAdditionalButton('itask_auto_start')}
                />,
                <SelectBox
                    key="itask_due_offset"
                    name="itask_due_offset"
                    label={`${i18next.t('taskStart')}:`}
                    title="itask_due_offset"
                    options={itaskDueOffsetOptions}
                    value={items.itask_due_offset?.value}
                    onChange={handleChange}
                    readonly={!items.itask_due_offset?.change}
                    additionalButton={getAdditionalButton('itask_due_offset')}
                />,
                <SelectBox
                    key="itask_duration"
                    name="itask_duration"
                    label={`${i18next.t('dueDate')}:`}
                    title="itask_duration"
                    options={itaskDurationOptions}
                    value={items.itask_duration?.value}
                    onChange={handleChange}
                    readonly={!items.itask_duration?.change}
                    additionalButton={getAdditionalButton('itask_duration')}
                />,
                <Text
                    key="itask_comment"
                    name="itask_comment"
                    label={`${i18next.t('comment')}:`}
                    title="itask_comment"
                    value={items.itask_comment?.value}
                    onChange={handleChange}
                    readonly={!items.itask_comment?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('itask_comment')}
                />,
                <ShowHideComponent key="langItaskComment" side="left">
                    {languages.map((lang, i) => {
                        return (
                            <Text
                                key={`itask_comment_${lang}`}
                                name={`itask_comment_${lang}`}
                                label={`${i18next.t('comment')}:`}
                                title={`itask_comment_${lang}`}
                                value={items[`itask_comment_${lang}`]?.value}
                                lblLang={lang}
                                onChange={handleChange}
                                readonly={
                                    !items[`itask_comment_${lang}`]?.change
                                }
                                placeholder={`${i18next.t('empty')} (null)`}
                                additionalButton={getAdditionalButton(
                                    `itask_comment_${lang}`,
                                )}
                            />
                        );
                    })}
                </ShowHideComponent>,
                <SelectBox
                    key="itask_again"
                    name="itask_again"
                    label={`${i18next.t('tTaskAgain')}:`}
                    title="itask_again"
                    options={itaskAgainOptions}
                    value={items.itask_again?.value}
                    onChange={handleChange}
                    required={items.itask_again?.change}
                    readonly={!items.itask_again?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('itask_again')}
                />,
            );
        } else if (
            recordsType === 'cases' ||
            recordsType === 'casesByTasks' ||
            recordsType === 'casesByIvars'
        ) {
            formInputs.push(
                <Text
                    key="iproc_name"
                    name="iproc_name"
                    label={`${i18next.t('defaultLbl', { label: '$t(fsName)' })}:`}
                    title="iproc_name"
                    value={items.iproc_name?.value}
                    onChange={handleChange}
                    required={items.iproc_name?.change}
                    readonly={!items.iproc_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('iproc_name')}
                />,
                <Text
                    key="iproc_case_status"
                    name="iproc_case_status"
                    label={`${i18next.t('caseStatus')}:`}
                    title="iproc_case_status"
                    value={items.iproc_case_status?.value}
                    onChange={handleChange}
                    readonly={!items.iproc_case_status?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('iproc_case_status')}
                />,
                <SelectBox
                    key="iproc_inst_owner_user_id"
                    name="iproc_inst_owner_user_id"
                    label={`${i18next.t('caseOwner')}:`}
                    title="iproc_inst_owner_user_id"
                    selectBoxType="DLU"
                    value={items.iproc_inst_owner_user_id?.value}
                    onChange={handleChange}
                    required={items.iproc_inst_owner_user_id?.change}
                    readonly={!items.iproc_inst_owner_user_id?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton(
                        'iproc_inst_owner_user_id',
                    )}
                />,
                <TextNum
                    key="iproc_main_iproc_id"
                    name="iproc_main_iproc_id"
                    label={`${i18next.t('parentCase')} (ID):`}
                    title="iproc_main_iproc_id"
                    value={items.iproc_main_iproc_id?.value}
                    onChange={handleChange}
                    required={items.iproc_main_iproc_id?.change}
                    readonly={!items.iproc_main_iproc_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'iproc_main_iproc_id',
                    )}
                />,
                <SelectBox
                    key="iproc_status"
                    name="iproc_status"
                    label={`${i18next.t('status')}:`}
                    title="iproc_status"
                    options={iprocStatusOptions}
                    value={items.iproc_status?.value}
                    onChange={handleChange}
                    required={items.iproc_status?.change}
                    readonly={!items.iproc_status?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('iproc_status')}
                />,
                <Checkbox
                    key="iproc_shredded"
                    name="iproc_shredded"
                    label={`${i18next.t('shredded')}:`}
                    title="iproc_shredded"
                    value={items.iproc_shredded?.value === 'Y'}
                    onChange={(name, value) =>
                        handleChange(name, value ? 'Y' : null)}
                    readonly={!items.iproc_shredded?.change}
                    additionalButton={getAdditionalButton('iproc_shredded')}
                />,
                <SelectBox
                    key="iproc_vis_role_id"
                    name="iproc_vis_role_id"
                    label={`${i18next.t('visForRoleMembers')}:`}
                    title="iproc_vis_role_id"
                    selectBoxType="DLR"
                    value={items.iproc_vis_role_id?.value}
                    onChange={handleChange}
                    readonly={!items.iproc_vis_role_id?.change}
                    additionalButton={getAdditionalButton('iproc_vis_role_id')}
                />,
                <SelectBox
                    key="iproc_hr_role_id"
                    name="iproc_hr_role_id"
                    label={`${i18next.t('enableTasksHandoverRole')}:`}
                    title="iproc_hr_role_id"
                    selectBoxType="DLR"
                    value={items.iproc_hr_role_id?.value}
                    onChange={handleChange}
                    readonly={!items.iproc_hr_role_id?.change}
                    additionalButton={getAdditionalButton('iproc_hr_role_id')}
                />,
                <Text
                    key="iproc_summary"
                    name="iproc_summary"
                    label={`${i18next.t('procSummary')}:`}
                    title="iproc_summary"
                    value={items.iproc_summary?.value}
                    onChange={handleChange}
                    readonly={!items.iproc_summary?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('iproc_summary')}
                />,
                <ShowHideComponent key="langIprocSummary" side="left">
                    {languages.map((lang, i) => {
                        return (
                            <Text
                                key={`iproc_summary_${lang}`}
                                name={`iproc_summary_${lang}`}
                                label={`${i18next.t('procSummary')}:`}
                                title={`iproc_summary_${lang}`}
                                value={items[`iproc_summary_${lang}`]?.value}
                                lblLang={lang}
                                onChange={handleChange}
                                readonly={
                                    !items[`iproc_summary_${lang}`]?.change
                                }
                                placeholder={`${i18next.t('empty')} (null)`}
                                additionalButton={getAdditionalButton(
                                    `iproc_summary_${lang}`,
                                )}
                            />
                        );
                    })}
                </ShowHideComponent>,
                <SelectBox
                    key="iproc_dms_visibility"
                    name="iproc_dms_visibility"
                    label={`${i18next.t('dmsVisibility')}:`}
                    title="iproc_dms_visibility"
                    options={iprocDmsVisibilityOptions}
                    value={items.iproc_dms_visibility?.value}
                    onChange={handleChange}
                    readonly={!items.iproc_dms_visibility?.change}
                    additionalButton={getAdditionalButton(
                        'iproc_dms_visibility',
                    )}
                />,
            );

            if (templateBoxesDisabled) {
                formInputs.push(
                    <Label
                        key="header_id"
                        name="header_id"
                        label={`${i18next.t('header')}:`}
                        title="header_id"
                    >
                        {i18next.t('disabledDifferentTemplates')}
                    </Label>,
                );
            } else {
                formInputs.push(
                    <SelectBox
                        key="header_id"
                        name="header_id"
                        label={`${i18next.t('header')}:`}
                        title="header_id"
                        options={headerOptions}
                        value={items.header_id?.value}
                        onChange={handleChange}
                        required={items.header_id?.change}
                        readonly={!items.header_id?.change}
                        nullable={false}
                        additionalButton={getAdditionalButton('header_id')}
                    />,
                );
            }
        } else if (
            recordsType === 'users' ||
            recordsType === 'usersByUserParams'
        ) {
            formInputs.push(
                <Text
                    key="user_name"
                    name="user_name"
                    label={`${i18next.t('userName')}:`}
                    title="user_name"
                    value={items.user_name?.value}
                    onChange={handleChange}
                    required={items.user_name?.change}
                    readonly={!items.user_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_name')}
                />,
                <Text
                    key="user_first_name"
                    name="user_first_name"
                    label={`${i18next.t('firstName')}:`}
                    title="user_first_name"
                    value={items.user_first_name?.value}
                    onChange={handleChange}
                    required={items.user_first_name?.change}
                    readonly={!items.user_first_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_first_name')}
                />,
                <Text
                    key="user_last_name"
                    name="user_last_name"
                    label={`${i18next.t('lastName')}:`}
                    title="user_last_name"
                    value={items.user_last_name?.value}
                    onChange={handleChange}
                    required={items.user_last_name?.change}
                    readonly={!items.user_last_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_last_name')}
                />,
                <Text
                    key="user_display_name"
                    name="user_display_name"
                    label={`${i18next.t('displayName')}:`}
                    title="user_display_name"
                    value={items.user_display_name?.value}
                    onChange={handleChange}
                    required={items.user_display_name?.change}
                    readonly={!items.user_display_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_display_name')}
                />,
                <Text
                    key="user_email"
                    name="user_email"
                    label={`${i18next.t('email')}:`}
                    title="user_email"
                    value={items.user_email?.value}
                    onChange={handleChange}
                    readonly={!items.user_email?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_email')}
                />,
                <SelectBox
                    key="user_status"
                    name="user_status"
                    label={`${i18next.t('userStatus')}:`}
                    title="user_status"
                    options={userStatusOptions}
                    value={items.user_status?.value}
                    onChange={handleChange}
                    required={items.user_status?.change}
                    readonly={!items.user_status?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('user_status')}
                />,
                <Text
                    key="user_title_prefix"
                    name="user_title_prefix"
                    label={`${i18next.t('titlePrefix')}:`}
                    title="user_title_prefix"
                    value={items.user_title_prefix?.value}
                    onChange={handleChange}
                    readonly={!items.user_title_prefix?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_title_prefix')}
                />,
                <Text
                    key="user_title_suffix"
                    name="user_title_suffix"
                    label={`${i18next.t('titleSuffix')}:`}
                    title="user_title_suffix"
                    value={items.user_title_suffix?.value}
                    onChange={handleChange}
                    readonly={!items.user_title_suffix?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_title_suffix')}
                />,
                <Text
                    key="user_password"
                    name="user_password"
                    label={`${i18next.t('password')}:`}
                    title="user_password"
                    value={items.user_password?.value}
                    onChange={handleChange}
                    readonly={!items.user_password?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_password')}
                />,
                <Text
                    key="user_change_password"
                    name="user_change_password"
                    label={`${i18next.t('changePassword')}:`}
                    title="user_change_password"
                    value={items.user_change_password?.value}
                    onChange={handleChange}
                    readonly={!items.user_change_password?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'user_change_password',
                    )}
                />,
                <Calendar
                    key="user_password_last_change"
                    name="user_password_last_change"
                    label={`${i18next.t('passwordLastChange')}:`}
                    title="user_password_last_change"
                    value={items.user_password_last_change?.value}
                    onChange={handleChange}
                    readonly={!items.user_password_last_change?.change}
                    additionalButton={getAdditionalButton(
                        'user_password_last_change',
                    )}
                />,
                <TextNum
                    key="login_count"
                    name="login_count"
                    label={`${i18next.t('loginCount')}:`}
                    title="login_count"
                    value={items.login_count?.value}
                    onChange={handleChange}
                    readonly={!items.login_count?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('login_count')}
                />,
                <TextNum
                    key="user_bad_login_count"
                    name="user_bad_login_count"
                    label={`${i18next.t('badLoginCount')}:`}
                    title="user_bad_login_count"
                    value={items.user_bad_login_count?.value}
                    onChange={handleChange}
                    readonly={!items.user_bad_login_count?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'user_bad_login_count',
                    )}
                />,
                <Text
                    key="user_external_login"
                    name="user_external_login"
                    label={`${i18next.t('externalLogin')}:`}
                    title="user_external_login"
                    value={items.user_external_login?.value}
                    onChange={handleChange}
                    readonly={!items.user_external_login?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'user_external_login',
                    )}
                />,
                <Text
                    key="user_external_source"
                    name="user_external_source"
                    label={`${i18next.t('externalSource')}:`}
                    title="user_external_source"
                    value={items.user_external_source?.value}
                    onChange={handleChange}
                    readonly={!items.user_external_source?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'user_external_source',
                    )}
                />,
                <Text
                    key="external_id"
                    name="external_id"
                    label={`${i18next.t('externalId')}:`}
                    title="external_id"
                    value={items.external_id?.value}
                    onChange={handleChange}
                    readonly={!items.external_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('external_id')}
                />,
                <TextNum
                    key="org_id"
                    name="org_id"
                    label={`${i18next.t('orgId')}:`}
                    title="org_id"
                    value={items.org_id?.value}
                    onChange={handleChange}
                    required={items.org_id?.change}
                    readonly={!items.org_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('org_id')}
                />,
                <Text
                    key="user_comp"
                    name="user_comp"
                    label={`${i18next.t('user_comp')}:`}
                    title="user_comp"
                    value={items.user_comp?.value}
                    onChange={handleChange}
                    readonly={!items.user_comp?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_comp')}
                />,
                <Text
                    key="user_comp_id"
                    name="user_comp_id"
                    label={`${i18next.t('user_comp_id')}:`}
                    title="user_comp_id"
                    value={items.user_comp_id?.value}
                    onChange={handleChange}
                    readonly={!items.user_comp_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_comp_id')}
                />,
                <Text
                    key="user_comp_code"
                    name="user_comp_code"
                    label={`${i18next.t('user_comp_code')}:`}
                    title="user_comp_code"
                    value={items.user_comp_code?.value}
                    onChange={handleChange}
                    readonly={!items.user_comp_code?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('user_comp_code')}
                />,
            );
        } else if (recordsType === 'roles') {
            formInputs.push(
                <Text
                    key="role_name"
                    name="role_name"
                    label={`${i18next.t('name')}:`}
                    title="role_name"
                    value={items.role_name?.value}
                    onChange={handleChange}
                    required={items.role_name?.change}
                    readonly={!items.role_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('role_name')}
                />,
                <Text
                    key="role_category"
                    name="role_category"
                    label={`${i18next.t('category')}:`}
                    title="role_category"
                    value={items.role_category?.value}
                    onChange={handleChange}
                    readonly={!items.role_category?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('role_category')}
                />,
                <Text
                    key="role_note"
                    name="role_note"
                    label={`${i18next.t('description')}:`}
                    title="role_note"
                    value={items.role_note?.value}
                    onChange={handleChange}
                    readonly={!items.role_note?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('role_note')}
                />,
                <TextNum
                    key="role_max_assigns"
                    name="role_max_assigns"
                    label={`${i18next.t('maxAssigns')}:`}
                    title="role_max_assigns"
                    value={items.role_max_assigns?.value}
                    onChange={handleChange}
                    readonly={!items.role_max_assigns?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('role_max_assigns')}
                />,
                <SelectBox
                    key="role_access_role_id"
                    name="role_access_role_id"
                    label={`${i18next.t('restrictRoleAssignment')}:`}
                    title="role_access_role_id"
                    value={items.role_access_role_id?.value}
                    selectBoxType="DLR"
                    onChange={handleChange}
                    readonly={!items.role_access_role_id?.change}
                    additionalButton={getAdditionalButton(
                        'role_access_role_id',
                    )}
                />,
                <TextNum
                    key="org_id"
                    name="org_id"
                    label={`${i18next.t('orgId')}:`}
                    title="org_id"
                    value={items.org_id?.value}
                    onChange={handleChange}
                    required={items.org_id?.change}
                    readonly={!items.org_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('org_id')}
                />,
            );
        } else if (recordsType === 'orgUnits') {
            formInputs.push(
                <Text
                    key="orgstr_name"
                    name="orgstr_name"
                    label={`${i18next.t('name')}:`}
                    title="orgstr_name"
                    value={items.orgstr_name?.value}
                    onChange={handleChange}
                    required={items.orgstr_name?.change}
                    readonly={!items.orgstr_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('orgstr_name')}
                />,
                <Text
                    key="organization_type"
                    name="organization_type"
                    label={`${i18next.t('type')}:`}
                    title="organization_type"
                    value={items.organization_type?.value}
                    onChange={handleChange}
                    readonly={!items.organization_type?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('organization_type')}
                />,
                <SelectBox
                    key="organization_status"
                    name="organization_status"
                    label={`${i18next.t('status')}:`}
                    title="organization_status"
                    options={orgStatusOptions}
                    value={items.organization_status?.value}
                    onChange={handleChange}
                    required={items.organization_status?.change}
                    readonly={!items.organization_status?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton(
                        'organization_status',
                    )}
                />,
                <SelectDrop
                    key="logo_url"
                    name="logo_url"
                    label={`${i18next.t('logo')}:`}
                    title="logo_url"
                    value={items.logo_url?.value}
                    onChange={handleChange}
                    readonly={!items.logo_url?.change}
                    attributeAsValue="data-path"
                    doubleHeight
                    additionalButton={getAdditionalButton('logo_url')}
                >
                    {logosOptions.map((logo, i) => {
                        return (
                            <SelectDropPart
                                key={`${logo.path}-${i}`}
                                value={
                                    <img
                                        style={{ maxWidth: '3.125rem' }}
                                        src={logo.path}
                                        data-path={logo.path}
                                        alt="logo"
                                    />
                                }
                            />
                        );
                    })}
                </SelectDrop>,
                <SelectBox
                    key="manager_user_id"
                    name="manager_user_id"
                    label={`${i18next.t('manager')}:`}
                    title="manager_user_id"
                    value={items.manager_user_id?.value}
                    selectBoxType="DLU"
                    onChange={handleChange}
                    readonly={!items.manager_user_id?.change}
                    additionalButton={getAdditionalButton('manager_user_id')}
                />,
                <SelectBox
                    key="parent_orgstr_id"
                    name="parent_orgstr_id"
                    label={`${i18next.t('parentUnit')}:`}
                    title="parent_orgstr_id"
                    value={items.parent_orgstr_id?.value}
                    selectBoxType="DLO"
                    onChange={handleChange}
                    required={items.parent_orgstr_id?.change}
                    readonly={!items.parent_orgstr_id?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('parent_orgstr_id')}
                />,
                <Text
                    key="external_status"
                    name="external_status"
                    label={`${i18next.t('externalStatus')}:`}
                    title="external_status"
                    value={items.external_status?.value}
                    onChange={handleChange}
                    readonly={!items.external_status?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('external_status')}
                />,
                <Text
                    key="external_id"
                    name="external_id"
                    label={`${i18next.t('externalId')}:`}
                    title="external_id"
                    value={items.external_id?.value}
                    onChange={handleChange}
                    readonly={!items.external_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('external_id')}
                />,
                <TextNum
                    key="org_id"
                    name="org_id"
                    label={`${i18next.t('orgId')}:`}
                    title="org_id"
                    value={items.org_id?.value}
                    onChange={handleChange}
                    required={items.org_id?.change}
                    readonly={!items.org_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('org_id')}
                />,
                <Text
                    key="additional_id"
                    name="additional_id"
                    label={`${i18next.t('additionalId')}:`}
                    title="additional_id"
                    value={items.additional_id?.value}
                    onChange={handleChange}
                    readonly={!items.additional_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('additional_id')}
                />,
                <Text
                    key="company_ic"
                    name="company_ic"
                    label={`${i18next.t('companyIc')}:`}
                    title="company_ic"
                    value={items.company_ic?.value}
                    onChange={handleChange}
                    readonly={!items.company_ic?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('company_ic')}
                />,
                <Text
                    key="parent_ic"
                    name="parent_ic"
                    label={`${i18next.t('parentIc')}:`}
                    title="parent_ic"
                    value={items.parent_ic?.value}
                    onChange={handleChange}
                    readonly={!items.parent_ic?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('parent_ic')}
                />,
            );
        } else if (recordsType === 'userParams') {
            formInputs.push(
                <SelectBox
                    key="user_id"
                    name="user_id"
                    label={`${i18next.t('user')}:`}
                    title="user_id"
                    value={items.user_id?.value}
                    selectBoxType="DLU"
                    onChange={handleChange}
                    required={items.user_id?.change}
                    readonly={!items.user_id?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('user_id')}
                />,
                <Text
                    key="usrpar_name"
                    name="usrpar_name"
                    label={`${i18next.t('name')}:`}
                    title="usrpar_name"
                    value={items.usrpar_name?.value}
                    onChange={handleChange}
                    required={items.usrpar_name?.change}
                    readonly={!items.usrpar_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('usrpar_name')}
                />,
                <Text
                    key="usrpar_client"
                    name="usrpar_client"
                    label={`${i18next.t('client')}:`}
                    title="usrpar_client"
                    value={items.usrpar_client?.value}
                    onChange={handleChange}
                    required={items.usrpar_client?.change}
                    readonly={!items.usrpar_client?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('usrpar_client')}
                />,
                <Text
                    key="usrpar_value"
                    name="usrpar_value"
                    label={`${i18next.t('value')}:`}
                    title="usrpar_value"
                    value={items.usrpar_value?.value}
                    onChange={handleChange}
                    readonly={!items.usrpar_value?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('usrpar_value')}
                />,
                <TextArea
                    key="usrpar_big_value"
                    name="usrpar_big_value"
                    label={`${i18next.t('bigValue')}:`}
                    title="usrpar_big_value"
                    value={items.usrpar_big_value?.value}
                    rows={3}
                    onChange={handleChange}
                    readonly={!items.usrpar_big_value?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('usrpar_big_value')}
                />,
                <TextNum
                    key="org_id"
                    name="org_id"
                    label={`${i18next.t('orgId')}:`}
                    title="org_id"
                    value={items.org_id?.value}
                    onChange={handleChange}
                    required={items.org_id?.change}
                    readonly={!items.org_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('org_id')}
                />,
            );
        } else if (recordsType === 'ivars') {
            formInputs.push(
                <Text
                    key="ivar_name"
                    name="ivar_name"
                    label={`${i18next.t('name')}:`}
                    title="ivar_name"
                    value={items.ivar_name?.value}
                    onChange={handleChange}
                    required={items.ivar_name?.change}
                    readonly={!items.ivar_name?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('ivar_name')}
                />,
                <ShowHideComponent key="langIvarName" side="left">
                    {languages.map((lang, i) => {
                        return (
                            <Text
                                key={`ivar_name_${lang}`}
                                name={`ivar_name_${lang}`}
                                label={`${i18next.t('name')}:`}
                                title={`ivar_name_${lang}`}
                                value={items[`ivar_name_${lang}`]?.value}
                                lblLang={lang}
                                onChange={handleChange}
                                readonly={!items[`ivar_name_${lang}`]?.change}
                                placeholder={`${i18next.t('empty')} (null)`}
                                additionalButton={getAdditionalButton(
                                    `ivar_name_${lang}`,
                                )}
                            />
                        );
                    })}
                </ShowHideComponent>,
                <SelectBox
                    key="ivar_type"
                    name="ivar_type"
                    label={`${i18next.t('type')}:`}
                    title="ivar_type"
                    options={ivarTypeOptions}
                    value={items.ivar_type?.value}
                    onChange={handleChange}
                    required={items.ivar_type?.change}
                    readonly={!items.ivar_type?.change}
                    nullable={false}
                    additionalButton={getAdditionalButton('ivar_type')}
                />,
                <SelectBox
                    key="ivar_attribute"
                    name="ivar_attribute"
                    label={`${i18next.t('attribute')}:`}
                    title="ivar_attribute"
                    options={ivarAttributeOptions}
                    value={items.ivar_attribute?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_attribute?.change}
                    additionalButton={getAdditionalButton('ivar_attribute')}
                />,
                <Text
                    key="ivar_text_value"
                    name="ivar_text_value"
                    label={`${i18next.t('textValue')}:`}
                    title="ivar_text_value"
                    value={items.ivar_text_value?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_text_value?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('ivar_text_value')}
                />,
                <TextNum
                    key="ivar_number_value"
                    name="ivar_number_value"
                    label={`${i18next.t('numberValue')}:`}
                    title="ivar_number_value"
                    value={items.ivar_number_value?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_number_value?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('ivar_number_value')}
                />,
                <Calendar
                    key="ivar_date_value"
                    name="ivar_date_value"
                    label={`${i18next.t('dateValue')}:`}
                    title="ivar_date_value"
                    value={items.ivar_date_value?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_date_value?.change}
                    additionalButton={getAdditionalButton('ivar_date_value')}
                />,
                <TextArea
                    key="ivar_big_value"
                    name="ivar_big_value"
                    label={`${i18next.t('bigValue')}:`}
                    title="ivar_big_value"
                    value={items.ivar_big_value?.value}
                    rows={3}
                    onChange={handleChange}
                    readonly={!items.ivar_big_value?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('ivar_big_value')}
                />,
                <SelectBox
                    key="dlist_name"
                    name="dlist_name"
                    label={`${i18next.t('dynamicTable')}:`}
                    title="dlist_name"
                    options={dynTableOptions}
                    value={items.dlist_name?.title || items.dlist_name?.value}
                    onChange={handleChangeDT}
                    readonly={!items.dlist_name?.change}
                    additionalButton={getAdditionalButton('dlist_name')}
                />,
                <div key="dtWrap" className="row divided">
                    <div className="columns medium-offset-5 medium-7">
                        {/* {items.dlist_name?.change ? ( */}
                        {/*    <Label */}
                        {/*        key="ivar_dt_index" */}
                        {/*        name="ivar_dt_index" */}
                        {/*        label={`${i18next.t('ivar_dt_index')}:`} */}
                        {/*        title="ivar_dt_index" */}
                        {/*        value={items.ivar_dt_index?.value} */}
                        {/*        side="right" */}
                        {/*    /> */}
                        {/* ) : ( */}
                        {/*    <TextNum */}
                        {/*        key="ivar_dt_index" */}
                        {/*        name="ivar_dt_index" */}
                        {/*        label={`${i18next.t('ivar_dt_index')}:`} */}
                        {/*        title="ivar_dt_index" */}
                        {/*        value={items.ivar_dt_index?.value} */}
                        {/*        readonly */}
                        {/*        side="right" */}
                        {/*        additionalButton={<span/>} */}
                        {/*    /> */}
                        {/* )} */}
                        <SelectBox
                            key="ivar_col_index"
                            name="ivar_col_index"
                            label={`${i18next.t('column')}:`}
                            title="ivar_col_index"
                            options={dynTableColumnOptions}
                            value={items.ivar_col_index?.value}
                            onChange={handleChange}
                            required={
                                items.dlist_name?.change &&
                                !!items.dlist_name?.value
                            }
                            readonly={!items.dlist_name?.change}
                            nullable={false}
                            side="right"
                            additionalButton={<span />}
                        />
                    </div>
                </div>,
                <Checkbox
                    key="ivar_multi"
                    name="ivar_multi"
                    label={`${i18next.t('allowMultiple')}:`}
                    title="ivar_multi"
                    value={items.ivar_multi?.value === 'X'}
                    onChange={(name, value) =>
                        handleChange(name, value ? 'X' : null)}
                    readonly={!items.ivar_multi?.change}
                    additionalButton={getAdditionalButton('ivar_multi')}
                />,
                <Text
                    key="ivar_multi_selected"
                    name="ivar_multi_selected"
                    label={`${i18next.t('ivar_multi_selected')}:`}
                    title="ivar_multi_selected"
                    value={items.ivar_multi_selected?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_multi_selected?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'ivar_multi_selected',
                    )}
                />,
                <Text
                    key="ivar_multitask_behaviour"
                    name="ivar_multitask_behaviour"
                    label={`${i18next.t('ivar_multitask_behaviour')}:`}
                    title="ivar_multitask_behaviour"
                    value={items.ivar_multitask_behaviour?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_multitask_behaviour?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton(
                        'ivar_multitask_behaviour',
                    )}
                />,
                <TextNum
                    key="ivar_dt_index"
                    name="ivar_dt_index"
                    label={`${i18next.t('ivar_dt_index')}:`}
                    title="ivar_dt_index"
                    value={items.ivar_dt_index?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_dt_index?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('ivar_dt_index')}
                />,
                <Text
                    key="ivar_class"
                    name="ivar_class"
                    label={`${i18next.t('class')}:`}
                    title="ivar_class"
                    value={items.ivar_class?.value}
                    onChange={handleChange}
                    readonly={!items.ivar_class?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('ivar_class')}
                />,
                <TextNum
                    key="org_id"
                    name="org_id"
                    label={`${i18next.t('orgId')}:`}
                    title="org_id"
                    value={items.org_id?.value}
                    onChange={handleChange}
                    required={items.org_id?.change}
                    readonly={!items.org_id?.change}
                    placeholder={`${i18next.t('empty')} (null)`}
                    additionalButton={getAdditionalButton('org_id')}
                />,
            );
            if (templateBoxesDisabled) {
                formInputs.push(
                    <Label
                        key="tvar_id"
                        name="tvar_id"
                        label={`${i18next.t('varTemp')}:`}
                        title="tvar_id"
                    >
                        {i18next.t('disabledDifferentTemplates')}
                    </Label>,
                );
            } else {
                formInputs.push(
                    <SelectBox
                        key="tvar_id"
                        name="tvar_id"
                        label={`${i18next.t('varTemp')}:`}
                        title="tvar_id"
                        value={items.tvar_id?.value}
                        options={tvarOptions}
                        onChange={handleChange}
                        required={items.tvar_id?.change}
                        readonly={!items.tvar_id?.change}
                        nullable={false}
                        additionalButton={getAdditionalButton('tvar_id')}
                    />,
                );
            }
        }

        return formInputs;
    };

    const modalContent = (
        <div className="modal service-operations-modal">
            <div className="row">
                <TabsButtonsOther
                    key="buttonsServiceOperationsModal"
                    inModal
                >
                    <TabsButton
                        key="save"
                        icon="icon-floppy-disk"
                        isActive={!isPristine}
                        onClick={saveStatus}
                        hideTitle={false}
                    >
                        {i18next.t('save')}
                    </TabsButton>
                    <TabsButton
                        key="close"
                        icon="icon-delete-1"
                        isActive
                        onClick={closeModal}
                        hideTitle={false}
                    >
                        {i18next.t('close')}
                    </TabsButton>
                </TabsButtonsOther>
            </div>
            <TabsWrapper>
                <Tabs noTabLinks>
                    <Tabs.Tab
                        key="serviceTab"
                        title={i18next.t('changeEntities')}
                        name="serviceTab"
                    >
                        <Form
                            ref={formServiceOperations}
                            name="formServiceOperations"
                            className="form-container"
                            onValidSubmit={handleSubmit}
                            oneColumn
                        >
                            {getFormInputs()}
                        </Form>
                    </Tabs.Tab>
                </Tabs>
            </TabsWrapper>
        </div>
    );

    return props.oldModal ? (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
        >
            {modalContent}
        </Modal>
    ) : (
        <Dialog
            fullWidth
            maxWidth="md"
            open={props.isOpen}
            onClose={closeModal}
        >
            <div className="old-modal">
                {modalContent}
            </div>
        </Dialog>
    );
};

ServiceOperationsModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    onSave: PropTypes.func.isRequired,
    recordsType: PropTypes.string.isRequired,
    selectedIds: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.arrayOf(PropTypes.object),
    ]),
    tableRef: PropTypes.objectOf(PropTypes.any),
    colsForFilter: PropTypes.arrayOf(PropTypes.string),
    oldModal: PropTypes.bool,
};

ServiceOperationsModal.defaultProps = {
    width: 'small',
    selectedIds: null,
    tableRef: null,
    colsForFilter: null,
    oldModal: false,
};

export default ServiceOperationsModal;
