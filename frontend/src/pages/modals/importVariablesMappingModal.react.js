import React from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import Dropzone from '../../components/dropzone.react';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import { CheckboxPure } from '../../components/form/checkbox.react';

// ----------------------------------------VariablesMappingTableRow----------------------------------------
const VariablesMappingTableRow = ({
    taskMapping,
    index,
    onSetToUse,
}) => (
    <tr>
        <td><span title={taskMapping.ttask_name}>{taskMapping.ttask_name}</span></td>
        <td width={100} className="text-center">
            <div>
                <CheckboxPure
                    name={`use_${index}`}
                    title={i18next.t('use')}
                    simple
                    value={taskMapping.use}
                    onChange={onSetToUse}
                />
            </div>
        </td>
    </tr>
);

VariablesMappingTableRow.propTypes = {
    taskMapping: PropTypes.shape({
        use: PropTypes.bool,
        ttask_name: PropTypes.string,
        ttask_var_mapping: PropTypes.arrayOf(
            PropTypes.shape({
                tvar_name: PropTypes.string,
                usage: PropTypes.string,
                axis_x: PropTypes.number,
                axis_y: PropTypes.number,
                tsec_id: PropTypes.number,
                tsec_name: PropTypes.string,
                tsec_x: PropTypes.number,
                tsec_y: PropTypes.number,
            }),
        ),
    }).isRequired,
    index: PropTypes.number.isRequired,
    onSetToUse: PropTypes.func.isRequired,
};

// --------------------------------------ImportVariablesMappingModal--------------------------------------
class ImportVariablesMappingModal extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            taskMappings: [],
            allTaskMappingsSetToUse: true,
        };

        this.handleImport = this.handleImport.bind(this);
        this.handleSetToUse = this.handleSetToUse.bind(this);
        this.handleSetToUseAll = this.handleSetToUseAll.bind(this);
        this.onLoad = this.onLoad.bind(this);
        this.resetState = this.resetState.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }

    handleImport() {
        if (this.state.taskMappings.length === 0) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrNoVariablesMappingToImport'),
                show: true,
                allowCountdown: true,
            });
            return;
        }

        this.props.onImport?.(
            this.state.taskMappings.filter((taskMapping) => taskMapping.use).map((taskMapping) => ({
                ttask_name: taskMapping.ttask_name,
                ttask_var_mapping: taskMapping.ttask_var_mapping,
            })),
        );

        this.closeModal();
    }

    handleSetToUse(index, value) {
        this.setState((prevState) => {
            const newTaskMappings = [...prevState.taskMappings];
            newTaskMappings[index].use = value;

            return {
                taskMappings: newTaskMappings,
                allTaskMappingsSetToUse: !value ? false : prevState.allTaskMappingsSetToUse,
            };
        });
    }

    handleSetToUseAll(value) {
        this.setState((prevState) => ({
            taskMappings: prevState.taskMappings.map((taskMapping) => ({
                ...taskMapping,
                use: value,
            })),
            allTaskMappingsSetToUse: value,
        }));
    }

    // drag&drop or select from filesystem
    onLoad(file, readerEvt) {
        const fileReader = new FileReader();

        fileReader.onload = (event) => {
            let taskMappings = [];

            try {
                const importObject = JSON.parse(event.target.result);

                taskMappings = importObject.map((taskMapping) => ({
                    use: true,
                    ...taskMapping,
                }));
            } catch (e) {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrVariablesMappingImportLoadFailed'),
                    show: true,
                    allowCountdown: true,
                    serverError: e,
                });

                // keep the current state on error
                return;
            }

            this.resetState();
            this.setState({ taskMappings });
        };

        fileReader.readAsText(file);
    }

    resetState() {
        this.setState({
            taskMappings: [],
            allTaskMappingsSetToUse: true,
        });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.resetState();
        this.props.onClose?.();
    }

    render() {
        return (
            <Modal
                isOpen={this.props.isOpen}
                width={this.props.width}
                onEsc={this.closeModal}
                loading={false}
            >
                <div className="modal import-entities-modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsImportVariablesMapping" inModal>
                            <TabsButton
                                key="import"
                                className="button-green"
                                icon="icon-download-10"
                                isActive
                                onClick={this.handleImport}
                                hideTitle={false}
                            >
                                {i18next.t('doImport')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <div className="row text-center">
                        <h5>
                            {i18next.t('variablesMappingImport')}
                        </h5>
                    </div>
                    <Dropzone
                        onLoad={this.onLoad}
                        className="dropzoneAttach"
                        activeClassName="dropzoneAttachActive"
                        multiple={false}
                    >
                        <div
                            dangerouslySetInnerHTML={{
                                __html: i18next.t('dragAddFile', {
                                    here: `<b>${i18next.t('here')}</b>`,
                                }),
                            }}
                        />
                    </Dropzone>
                    <div className="row form-container">
                        <table
                            className={
                                `import-entities-modal-table${
                                    this.state.taskMappings.length === 0
                                        ? ' import-entities-modal-table-empty'
                                        : ''
                                }`
                            }
                        >
                            <thead>
                                <tr>
                                    <th>{i18next.t('task')}</th>
                                    <th width={100} className="text-center">{i18next.t('use')}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {this.state.taskMappings.map((taskMapping, index) => (
                                    <VariablesMappingTableRow
                                        key={index}
                                        taskMapping={taskMapping}
                                        index={index}
                                        onSetToUse={(_name, value) => this.handleSetToUse(index, value)}
                                    />
                                ))}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td />
                                    <td width={100} className="text-center">
                                        <CheckboxPure
                                            name="useAllMappings"
                                            title={i18next.t('useAllMappings')}
                                            value={this.state.allTaskMappingsSetToUse}
                                            simple
                                            onChange={(_name, value) => this.handleSetToUseAll(value)}
                                        />
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </Modal>
        );
    }

}

ImportVariablesMappingModal.propTypes = {
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    onImport: PropTypes.func.isRequired,
};

export default ImportVariablesMappingModal;
