import i18next from 'i18next';
import PropTypes from 'prop-types';
import browserHistory from '../../common/history';
import { guid } from '../../common/utils';
import React from 'react';
import _ from 'lodash';
import createReactClass from 'create-react-class';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import AltManagerMixin from '../../flux/altManagerMixin';
import HiddenTabs from '../../components/tabs/hiddenTabs.react';
import HiddenTab from '../../components/tabs/hiddenTab.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsWrapper from '../../components/tabs/tabsWrapper.react';
import TabsButton from '../../components/tabs/tabsButton.react';

import TemplateTaskStore from '../../flux/templateTask.store';
import TemplateTaskActions from '../../flux/templateTask.actions';
import alt from '../../flux/alt';
import DiagramStore from '../../components/diagram/diagram.store';
import DiagramActions from '../../components/diagram/diagram.actions';
import Factory from '../../flux/factory';
import TemplateTaskForm from '../templateTask/templateTaskForm.react';
import TemplateTaskFunctions from '../templateTask/templateTaskFunctions.react';

import Helper from '../../components/form/componentHelper.react';
import ConfirmModal from './confirmModal.react';

const TemplateTaskModal = createReactClass({
    propTypes: {
        parent: PropTypes.object,
        onClose: PropTypes.func,
        isOpen: PropTypes.bool,
        width: PropTypes.string,
        params: PropTypes.objectOf(PropTypes.any).isRequired,
        templateId: PropTypes.string.isRequired,
        versionId: PropTypes.string.isRequired,
        taskId: PropTypes.string.isRequired,
    },

    mixins: [AltManagerMixin],

    statics: {
        registerStore: DiagramStore,
        registerAction: DiagramActions,
    },

    getInitialState: function () {
        this.initFlux(this.props, this.props.name); // diagram store

        // template task store
        const obj = Factory.registerFlux(
            TemplateTaskStore,
            TemplateTaskActions,
            `${this.props.name}-${this.props.taskId}`,
        );
        this.templateTaskActions = obj.action;
        this.templateTaskStore = obj.store;
        this.initTemplateTaskStoreListen();

        return _.extend(
            {
                formIsPristine: true,
                someCalculationChanged: false,
                componentWithRowsChanged: false,
                tabChanged: false,
                calcsHelperIsVisible: false,
                condsHelperIsVisible: false,
                confirmTaskModalIsOpen: false,
                confirmTemplateModalIsOpen: false,
                confirmMultiinstanceModalIsOpen: false,
                multiinstanceConfirmed: false,
                selectedTabName: null,
            },
            this.templateTaskStore.getState(),
            this.store.getState(),
        );
    },

    // Listen to template task store
    initTemplateTaskStoreListen: function () {
        this.setState(this.templateTaskStore.getState());
        this.templateTaskStore.listen(this.listenState);
    },

    componentDidMount: function () {
        this.store.listen(this._onChange);

        const { templateId, versionId, taskId } = this.props;
        const task = _.find(
            this.state.tasksChanged,
            ['ttask_id', Number(taskId)],
        );

        if (taskId !== 'new' && !isNaN(taskId)) {
            // nově založené tasky a nově modelované úkoly přímo z grafu
            this.templateTaskActions.fetchComponents.defer({
                apiUrl: `/template-processes/${templateId}/${versionId}/template-tasks/${taskId}`,
                resaveFilter: typeof task === 'undefined',
            });
        } else {
            const newTask = _.find(
                this.state.tasksChanged,
                ['bpmn_ttask_id', taskId],
            );

            if (newTask && newTask.ttask_completions) {
                this.setCompletionAndCancellation(newTask.ttask_completions);
            }

            this.templateTaskActions.setLoading.defer(false);
        }

        if (task && task.ttask_completions) {
            this.setCompletionAndCancellation(task.ttask_completions);
        }

        this.templateTaskActions.fetchTemplateTasks.defer(
            templateId,
            versionId,
        );
        this.templateTaskActions.fetchTemplateVariables.defer(
            templateId,
            versionId,
        );
        this.templateTaskActions.fetchTemplateProcesses.defer();
        this.templateTaskActions.fetchUsersEmails.defer();
        this.templateTaskActions.fetchEvents.defer();
    },
    componentWillUnmount: function () {
        this.store.unlisten(this._onChange); // diagram store
        this.templateTaskStore.unlisten(this.listenState); // template task store

        alt.recycle(this.templateTaskStore);
    },

    setCompletionAndCancellation(ttaskCompletions) {
        const completionArr = [];
        const cancellationArr = [];

        ttaskCompletions.forEach((condition) => {
            if (condition.ttc_cancel_flag === 'N') {
                completionArr.push({
                    array: {
                        title: condition.tvar_id,
                        value: condition.tvar_id,
                        type: condition.tvar_type,
                    },
                    condition: condition.ttc_value,
                    operator: condition.ttc_operator,
                    ttc_concat_operator: condition.ttc_concat_operator,
                    type: condition.tvar_type,
                });
            } else {
                cancellationArr.push({
                    array: {
                        title: condition.tvar_id,
                        value: condition.tvar_id,
                        type: condition.tvar_type,
                    },
                    condition: condition.ttc_value,
                    operator: condition.ttc_operator,
                    ttc_concat_operator: condition.ttc_concat_operator,
                    type: condition.tvar_type,
                });
            }
        });

        this.templateTaskActions.setCompletionFilter.defer(completionArr);
        this.templateTaskActions.setCancellationFilter.defer(cancellationArr);
    },

    formChanged: function (currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            // when any value is changed from its initial value
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    },

    _onChange: function (state) {
        this.setState(state);
    },

    saveBeforeLeave: function (callback) {
        if (!this.state.formIsPristine) {
            this.setState({ confirmTaskModalIsOpen: true });

            this.leavePage = callback;
        } else {
            this.props.saveDiagram(callback);
        }
    },

    confirmTaskSaving() {
        // save and leave
        this.saveTemplateTask(null, this.leavePage);
        this.setState({ confirmTaskModalIsOpen: false });
    },

    cancelTaskSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmTaskModalIsOpen: false,
            },
            () => {
                this.props.saveDiagram(this.leavePage);
            },
        );
    },

    closeTaskConfirmModal() {
        // cancel and stay
        this.setState({ confirmTaskModalIsOpen: false });
    },

    closeModal: function (e) {
        if (e) e.preventDefault();

        if (
            !this.state.formIsPristine ||
            this.state.someCalculationChanged ||
            this.state.componentWithRowsChanged
        ) {
            this.setState({ confirmTemplateModalIsOpen: true });

            this.onFormAction = (data) => {
                this.checkMultiinstanceAndPost(data, null, false);
            };

            this.onFormActionWithoutRequire = () => {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrFillRequiredItems'),
                    show: true,
                    allowCountdown: true,
                });
            };

            this.onFormActionInvalid = () => {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrFillDataInRightFormat'),
                    show: true,
                    allowCountdown: true,
                });
            };
        } else {
            this.props.onClose();
        }
    },

    confirmTemplateSaving() {
        // save and leave
        this.refs.templateTaskForm.refs.formTemplateTask.submit();
        this.setState({ confirmTemplateModalIsOpen: false });
    },

    cancelTemplateSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                someCalculationChanged: false,
                componentWithRowsChanged: false,
                confirmTemplateModalIsOpen: false,
            },
            () => {
                this.props.onClose();
            },
        );
    },

    closeTemplateConfirmModal() {
        // cancel and stay
        this.setState({ confirmTemplateModalIsOpen: false });
    },

    confirmMultiinstance() {
        this.setState(
            {
                multiinstanceConfirmed: true,
                confirmMultiinstanceModalIsOpen: false,
            },
            () => {
                this.refs.templateTaskForm.refs.formTemplateTask.submit();
            },
        );
    },

    cancelConfirmMultiinstance() {
        // cancel saving and leave
        this.setState({
            formIsPristine: true,
            confirmMultiinstanceModalIsOpen: false,
        });
    },

    closeConfirmMultiinstanceModal() {
        // cancel and stay
        this.setState({ confirmMultiinstanceModalIsOpen: false });
    },

    checkMultiinstanceAndPost(data, callback) {
        if (
            data.ttask_multiinstance_flag &&
            data.ttask_iterate_over === null &&
            !this.state.multiinstanceConfirmed
        ) {
            this.setState({
                confirmMultiinstanceModalIsOpen: true,
            });
        } else {
            if (this.state.multiinstanceConfirmed) {
                this.setState({
                    multiinstanceConfirmed: false,
                });
            }
            this.postData(data, callback);
        }
    },

    handleAddVariable: function (e) {
        e.preventDefault();
        const { templateId, versionId } = this.props;
        browserHistory.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-variable/new`,
            state: { closePrevPath: location.pathname },
        });
    },

    handleValidSubmit: function (data) {
        this.onFormAction(data);
    },

    handleValidSubmitWithoutRequire: function () {
        this.onFormActionWithoutRequire();
    },

    handleInvalidSubmit: function () {
        this.onFormActionInvalid();
    },

    saveTemplateTask: function (e, callback) {
        if (e) e.preventDefault();

        this.onFormAction = function (data) {
            this.checkMultiinstanceAndPost(data, callback);
        };

        this.onFormActionWithoutRequire = function () {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillRequiredItems'),
                show: true,
                allowCountdown: true,
            });
        };

        this.onFormActionInvalid = function () {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        };

        this.refs.templateTaskForm.refs.formTemplateTask.submit();
    },

    postData: function (data, callback) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        let { taskId } = this.props;
        taskId = !isNaN(taskId) ? Number(taskId) : taskId;

        const storedValues =
            _.find(this.state.tasksChanged, ['ttask_id', taskId]) || {};
        const taskType = storedValues.ttask_type || this.state.taskType;

        const obj = TemplateTaskFunctions.getPostObject(
            data,
            this.props.templateId,
            this.props.params.versionId,
            taskId,
            taskType,
            this.state.varsMappingOrder,
            true,
        );

        // subp_mapping - array of objects to array of values
        const subpMappingObj = _.pick(data, 'subp_mapping');
        if (!_.isEmpty(subpMappingObj)) {
            obj.subp_mapping = subpMappingObj.subp_mapping;
        }
        // subr_mapping - array of objects to array of values
        const subrMappingObj = _.pick(data, 'subr_mapping');
        if (!_.isEmpty(subrMappingObj)) {
            obj.subr_mapping = subrMappingObj.subr_mapping;
        }

        this.props.saveTaskChanges(obj);
        this.props.onClose();
        AlertsActions.changeAlert({
            id: alertId,
            type: 'success',
            message: i18next.t('alrTskSaved'),
        });
        if (callback) {
            setTimeout(() => {
                // due to diagram changes its state to isPristine = false
                this.props.saveDiagram(callback);
            }, 0);
        }
    },

    taskTypeChange: function (name, value) {
        this.templateTaskActions.changeTaskType(value.value);
        this.action.changeTaskType({
            taskId: this.props.taskId,
            taskType: value.value,
        });

        if (
            value.value === 'P' &&
            !_.isEmpty(this.state.templateProcessesOptions)
        ) {
            // value = tproc_id-tproc_version
            const valueArr =
                this.state.templateProcessesOptions[0].value.split('-');
            this.templateTaskActions.changeProcessVariables(
                valueArr[0],
                valueArr[1],
            );
        }
    },

    processChange: function (name, value) {
        this.resetMapping();
        this.templateTaskActions.resetMapping();
        // value = tproc_id-tproc_version
        const valueArr = value.value.split('-');
        this.templateTaskActions.changeProcessVariables(
            valueArr[0],
            valueArr[1],
        );
        this.action.resetMapping(this.props.taskId);
    },

    eventChange: function (name, value) {
        this.resetMapping();
        this.templateTaskActions.resetMapping();
        this.templateTaskActions.changeEventMapping(value.value);
        this.action.resetMapping(this.props.taskId);
    },

    resetMapping: function () {
        ['subpMapping', 'subrMapping'].forEach((map) => {
            if (typeof this.refs.templateTaskForm.refs[map] !== 'undefined') {
                this.refs.templateTaskForm.refs[map].resetValues();
            }
        });
    },

    // checkEvent if event is defined
    checkEvent: function (eventName) {
        this.templateTaskActions.checkEvent(eventName);
    },

    // Important - if change tab or add/remove row in completion/cancellation conditions or calculations we need know new modal height,
    // setState do rerender and modal take care of it
    tabChanged: function (index, tabName) {
        this.setState((prevState) => ({
            tabChanged: !this.state.tabChanged,
            selectedTabName:
                typeof index !== 'undefined'
                    ? tabName
                    : prevState.selectedTabName,
        }));
    },

    // due to calculations not triggering 'not pristine' state
    someCalculationChanged() {
        if (!this.state.someCalculationChanged) {
            this.setState({ someCalculationChanged: true });
        }
    },

    // due to adding/removing calculations not triggering 'not pristine' state
    componentWithRowsChanged(index, tabName) {
        this.tabChanged(index, tabName);
        if (!this.state.componentWithRowsChanged) {
            this.setState({ componentWithRowsChanged: true });
        }
    },

    showDocs: function (type, e) {
        if (e) {
            e.preventDefault();
        }

        this.setState({ [type]: !this.state[type] });
    },

    render: function () {
        let { items } = this.state;
        let { subpReturnValues } = this.state;
        let { subpMappingValues } = this.state;
        const storedValues =
            _.find(this.state.tasksChanged, ['ttask_id', this.props.taskId]) ||
            _.find(
                this.state.tasksChanged,
                ['ttask_id', parseInt(this.props.taskId)],
            ) ||
            {};

        if (!_.isEmpty(storedValues)) {
            items = storedValues;

            const canSetStoredMapping = function (mapping) {
                if (typeof storedValues[mapping] !== 'undefined') {
                    if (!_.isEmpty(storedValues[mapping])) {
                        return typeof storedValues[mapping][0] === 'object';
                    }
                    return true;
                }
                return false;
            };

            if (canSetStoredMapping('subp_mapping')) {
                subpMappingValues = storedValues.subp_mapping;
            }
            if (canSetStoredMapping('subr_mapping')) {
                subpReturnValues = storedValues.subr_mapping;
            }
        }

        const taskType = storedValues.ttask_type || this.state.taskType; // pozor - ve stavu zustava taskType z db

        let customTabTitle = '';
        if (taskType == 'W') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('vars')}/${i18next.t('completion')}`;
        } else if (taskType == 'S') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('completion')}`;
        } else if (taskType == 'E') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('vars')}/${i18next.t('event')}`;
        } else if (taskType == 'P') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('vars')}`;
        } else if (taskType == 'I') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('invitation')}`;
        }

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
            >
                <div className="modal tab-modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsTemplTask" inModal>
                            <TabsButton
                                key="save"
                                icon="icon-floppy-disk"
                                enableOn={!this.state.loading}
                                onClick={this.saveTemplateTask}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <TabsWrapper>
                        {this.state.calcsHelperIsVisible && (
                            <Helper
                                type="calculationsDocs"
                                closeHelper={this.showDocs.bind(
                                    null,
                                    'calcsHelperIsVisible',
                                )}
                                onModal
                            />
                        )}
                        {this.state.condsHelperIsVisible && (
                            <Helper
                                type="conditionsDocs"
                                closeHelper={this.showDocs.bind(
                                    null,
                                    'condsHelperIsVisible',
                                )}
                                onModal
                            />
                        )}
                        <HiddenTabs
                            params={this.props.params}
                            noTabLinks
                            tabChanged={this.tabChanged}
                        >
                            <HiddenTab
                                key="activity"
                                title={i18next.t('activity')}
                            >
                                <TabsButtonsOther key="buttonsTask">
                                    <TabsButton
                                        key="add"
                                        icon="icon-add-1"
                                        isActive
                                        onClick={this.handleAddVariable}
                                        tooltipCode="ttAddActivity"
                                    >
                                        {i18next.t('addVariable')}
                                    </TabsButton>
                                </TabsButtonsOther>
                            </HiddenTab>
                            {taskType !== 'A' &&
                                taskType !== 'N' &&
                                !!customTabTitle && (
                                <HiddenTab
                                    key="custom"
                                    title={customTabTitle}
                                >
                                    <TabsButtonsOther key="buttonsPlanning">
                                        <TabsButton
                                            key="add"
                                            icon="icon-add-1"
                                            isActive={taskType != 'I'}
                                            onClick={this.handleAddVariable}
                                            tooltipCode="ttAddActivity"
                                        >
                                            {i18next.t('addVariable')}
                                        </TabsButton>
                                    </TabsButtonsOther>
                                </HiddenTab>
                            )}
                            {taskType == 'N' && (
                                <HiddenTab
                                    key="notification"
                                    title={i18next.t('notification')}
                                >
                                    <TabsButtonsOther key="buttonsNotification" />
                                </HiddenTab>
                            )}
                            <HiddenTab
                                key="calculations"
                                title={i18next.t('calculations')}
                                tabName="calculations"
                            >
                                <TabsButtonsOther key="buttonsCalculations">
                                    <TabsButton
                                        key="editorDocumentation"
                                        icon="icon2-files"
                                        isActive
                                        onClick={() =>
                                            window.open(
                                                'https://code.visualstudio.com/docs/editor/codebasics',
                                                '_blank',
                                            )}
                                    >
                                        {i18next.t('editorDocumentation')}
                                    </TabsButton>
                                    <TabsButton
                                        key="showDocs"
                                        icon="icon-bubble-ask-2"
                                        isActive
                                        onClick={this.showDocs.bind(
                                            null,
                                            'calcsHelperIsVisible',
                                        )}
                                    >
                                        {i18next.t('help')}
                                    </TabsButton>
                                </TabsButtonsOther>
                            </HiddenTab>
                            {taskType == 'S' && (
                                <HiddenTab
                                    key="instructions"
                                    title={i18next.t('instructions')}
                                >
                                    <TabsButtonsOther key="buttonsInstructions" />
                                </HiddenTab>
                            )}
                            {taskType !== 'A' && (
                                <HiddenTab
                                    key="conditions"
                                    title={i18next.t('conditions')}
                                    tabName="conditions"
                                >
                                    <TabsButtonsOther key="buttonsConditions">
                                        <TabsButton
                                            key="showDocs"
                                            icon="icon-bubble-ask-2"
                                            isActive
                                            onClick={this.showDocs.bind(
                                                null,
                                                'condsHelperIsVisible',
                                            )}
                                        >
                                            {i18next.t('help')}
                                        </TabsButton>
                                    </TabsButtonsOther>
                                </HiddenTab>
                            )}
                            <TemplateTaskForm
                                ref="templateTaskForm"
                                taskId={this.props.taskId}
                                templateId={this.props.templateId}
                                items={items}
                                taskType={taskType}
                                handleValidSubmit={this.handleValidSubmit}
                                handleValidSubmitWithoutRequire={
                                    this.handleValidSubmitWithoutRequire
                                }
                                handleInvalidSubmit={this.handleInvalidSubmit}
                                formChanged={this.formChanged}
                                tasksChanged={this.state.tasksChanged}
                                templateVarsDateOptions={
                                    this.state.templateVarsDateOptions
                                }
                                templateTasksOptions={
                                    this.state.templateTasksOptions
                                }
                                templateVarsUserOptions={
                                    this.state.templateVarsUserOptions
                                }
                                templateVarsTextOptions={
                                    this.state.templateVarsTextOptions
                                }
                                templateVarsAllOptions={
                                    this.state.templateVarsAllOptions
                                }
                                templateProcessesOptions={
                                    this.state.templateProcessesOptions
                                }
                                processMappingTargetVarsOptions={
                                    this.state.processMappingTargetVarsOptions
                                }
                                templateVarsAllPlusSysOptions={
                                    this.state.templateVarsAllPlusSysOptions
                                }
                                processReturnTargetVarsOptions={
                                    this.state.processReturnTargetVarsOptions
                                }
                                processReturnSourceVarsOptions={
                                    this.state.processReturnSourceVarsOptions
                                }
                                eventsOptions={this.state.eventsOptions}
                                subpReturnValues={subpReturnValues}
                                subpMappingValues={subpMappingValues}
                                userOptions={this.state.userOptions}
                                taskTypeChange={this.taskTypeChange}
                                processChange={this.processChange}
                                eventChange={this.eventChange}
                                checkEvent={this.checkEvent}
                                eventSourceVariablesOptions={
                                    this.state.eventSourceVariablesOptions
                                }
                                completionFilterVariablesOptions={
                                    this.state.completionFilterVariablesOptions
                                }
                                completionFilter={this.state.completionFilter}
                                cancellationFilter={
                                    this.state.cancellationFilter
                                }
                                componentWithRowsChanged={
                                    this.componentWithRowsChanged
                                }
                                suggestList={this.state.taskSuggestionList}
                                customTabTitle={customTabTitle}
                                tabName={this.state.selectedTabName}
                                someCalculationChanged={
                                    this.someCalculationChanged
                                }
                            />
                        </HiddenTabs>
                        <ConfirmModal
                            text={i18next.t('confirmSaveTaskChanges')}
                            width="tiny"
                            isOpen={this.state.confirmTaskModalIsOpen}
                            onClose={this.closeTaskConfirmModal}
                            onConfirm={this.confirmTaskSaving}
                            onDiscard={this.cancelTaskSaving}
                            modalInModal
                        />
                        <ConfirmModal
                            text={i18next.t(
                                'confirmEmptyMultiinstanceVariable',
                            )}
                            width="tiny"
                            isOpen={this.state.confirmMultiinstanceModalIsOpen}
                            onClose={this.closeConfirmMultiinstanceModal}
                            onConfirm={this.confirmMultiinstance}
                            onDiscard={this.cancelConfirmMultiinstance}
                            modalInModal
                        />
                        <ConfirmModal
                            text={i18next.t('confirmSaveChanges')}
                            width="tiny"
                            isOpen={this.state.confirmTemplateModalIsOpen}
                            onClose={this.closeTemplateConfirmModal}
                            onConfirm={this.confirmTemplateSaving}
                            onDiscard={this.cancelTemplateSaving}
                            modalInModal
                        />
                    </TabsWrapper>
                </div>
            </Modal>
        );
    },
});

export default TemplateTaskModal;
