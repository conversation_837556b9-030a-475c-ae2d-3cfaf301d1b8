import i18next from 'i18next';
import PropTypes from 'prop-types';
import Dropzone from '../../components/dropzone.react';
import React from 'react';
import cx from 'classnames';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import { CheckboxPure } from '../../components/form/checkbox.react';

// ----------------------------------------ScriptTableRow----------------------------------------
const ScriptTableRow = ({
    script,
    index,
    onSetToActivate,
    onRemove,
}) => (
    <tr>
        <td><span title={script.name}>{script.name}</span></td>
        <td width={100} className="text-center">
            <div>
                <CheckboxPure
                    name={`js_active_${index}`}
                    title={i18next.t('activate')}
                    simple
                    value={script.js_active === 'Y'}
                    onChange={onSetToActivate}
                />
            </div>
        </td>
        <td width={100} className="text-center">
            <span
                role="button"
                className="icon icon-delete-1 pointer"
                title={i18next.t('remove')}
                onClick={onRemove}
            />
        </td>
        <td width={100} />
    </tr>
);

ScriptTableRow.propTypes = {
    script: PropTypes.shape({
        name: PropTypes.string,
        js: PropTypes.string,
        js_type: PropTypes.string,
        js_active: PropTypes.string,
    }).isRequired,
    index: PropTypes.number.isRequired,
    onSetToActivate: PropTypes.func.isRequired,
    onRemove: PropTypes.func.isRequired,
};

// ---------------------------------ScriptsTableSectionHeaderRow---------------------------------
const ScriptsTableSectionHeaderRow = ({
    title,
    index,
    expanded,
    onExpand,
    active,
    onSetToActivate,
    onRemove,
    scriptCount = 0,
    toActivateCount = 0,
}) => {
    return (
        <tr
            className={
                cx(
                    'import-entities-modal-table-section-header',
                    `import-entities-modal-table-section-header-${index}`,
                    {
                        'import-entities-modal-table-section-header-expanded': expanded,
                    },
                )
            }
        >
            <td>
                <b>
                    {`${title}`}
                </b>
                {` (${i18next.t('scripts')}: ${scriptCount} | ${i18next.t('activate')}: ${toActivateCount}):`}
            </td>
            <td width={100} className="text-center">
                <CheckboxPure
                    name={`activateAll_section_${index}`}
                    title={i18next.t('activate')}
                    value={active}
                    simple
                    onChange={onSetToActivate}
                />
            </td>
            <td width={100} className="text-center">
                <span
                    role="button"
                    className="icon icon-delete-1 pointer"
                    title={i18next.t('remove')}
                    onClick={onRemove}
                />
            </td>
            <td width={100} className="text-center">
                <span
                    role="button"
                    className={cx('icon pointer', {
                        'icon-arrow-57': expanded,
                        'icon-arrow-58': !expanded,
                    })}
                    title={expanded ? i18next.t('close') : i18next.t('open')}
                    onClick={onExpand}
                />
            </td>
        </tr>
    );
};

ScriptsTableSectionHeaderRow.propTypes = {
    title: PropTypes.string.isRequired,
    index: PropTypes.number.isRequired,
    expanded: PropTypes.bool.isRequired,
    onExpand: PropTypes.func.isRequired,
    active: PropTypes.bool.isRequired,
    onSetToActivate: PropTypes.func.isRequired,
    onRemove: PropTypes.func.isRequired,
    scriptCount: PropTypes.number,
    toActivateCount: PropTypes.number,
};

ScriptsTableSectionHeaderRow.defaultProps = {
    scriptCount: 0,
    toActivateCount: 0,
};

// --------------------------------------ImportScriptsModal--------------------------------------
class ImportScriptsModal extends React.Component {
    stateKeyByScriptType = {
        F: 'scriptsCO',
        R: 'scriptsCOReact',
        C: 'scriptsCalculations',
    };

    constructor(props) {
        super(props);
        this.state = {
            scriptsCO: [],
            scriptsCOReact: [],
            scriptsCalculations: [],
            scriptsCOExpanded: false,
            scriptsCOReactExpanded: false,
            scriptsCalculationsExpanded: false,
            allScriptsActive: true,
            scriptsCOActive: true,
            scriptsCOReactActive: true,
            scriptsCalculationsActive: true,
        };

        this.handleImport = this.handleImport.bind(this);
        this.handleSetToActivate = this.handleSetToActivate.bind(this);
        this.handleRemove = this.handleRemove.bind(this);
        this.handleSetToActivateAll = this.handleSetToActivateAll.bind(this);
        this.handleRemoveAll = this.handleRemoveAll.bind(this);
        this.handleExpandTableSection = this.handleExpandTableSection.bind(this);
        this.onLoad = this.onLoad.bind(this);
        this.resetState = this.resetState.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }

    handleImport() {
        const scripts = [
            ...this.state.scriptsCO,
            ...this.state.scriptsCOReact,
            ...this.state.scriptsCalculations,
        ];

        if (scripts.length === 0) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrNoScriptsToImport'),
                show: true,
                allowCountdown: true,
            });
            return;
        }

        this.props.onImport?.(scripts);
        this.closeModal();
    }

    handleSetToActivate(index, value, type) {
        this.setState((prevState) => {
            const newScripts = [...prevState[this.stateKeyByScriptType[type]]];
            newScripts[index].js_active = value ? 'Y' : 'N';
            return {
                [this.stateKeyByScriptType[type]]: newScripts,
                [`${this.stateKeyByScriptType[type]}Active`]: !value ? false : prevState[`${this.stateKeyByScriptType[type]}Active`],
                allScriptsActive: !value ? false : prevState.allScriptsActive,
            };
        });
    }

    handleRemove(index, type) {
        this.setState((prevState) => {
            const newScripts = [...prevState[this.stateKeyByScriptType[type]]];
            newScripts.splice(index, 1);
            return { [this.stateKeyByScriptType[type]]: newScripts };
        });
    }

    handleSetToActivateAll(value, type) {
        if (!type) { // activate all types
            this.setState((prevState) => ({
                scriptsCO: prevState.scriptsCO.map((script) => ({
                    ...script,
                    js_active: value ? 'Y' : 'N',
                })),
                scriptsCOReact: prevState.scriptsCOReact.map((script) => ({
                    ...script,
                    js_active: value ? 'Y' : 'N',
                })),
                scriptsCalculations: prevState.scriptsCalculations.map((script) => ({
                    ...script,
                    js_active: value ? 'Y' : 'N',
                })),
                allScriptsActive: value,
                scriptsCOActive: value,
                scriptsCOReactActive: value,
                scriptsCalculationsActive: value,
            }));

            return;
        }

        this.setState((prevState) => {
            const newScripts = [...prevState[this.stateKeyByScriptType[type]]];

            newScripts.forEach((_script, i) => {
                newScripts[i].js_active = value ? 'Y' : 'N';
            });

            return {
                [this.stateKeyByScriptType[type]]: newScripts,
                [`${this.stateKeyByScriptType[type]}Active`]: value,
                allScriptsActive: !value ? false : prevState.allScriptsActive,
            };
        });
    }

    handleRemoveAll(type) {
        if (!type) { // remove all types
            this.setState({
                scriptsCO: [],
                scriptsCOReact: [],
                scriptsCalculations: [],
            });
            return;
        }

        this.setState({ [this.stateKeyByScriptType[type]]: [] });
    }

    handleExpandTableSection(type) {
        this.setState((prevState) => {
            const expandedKey = `${this.stateKeyByScriptType[type]}Expanded`;
            return { [expandedKey]: !prevState[expandedKey] };
        });
    }

    // drag&drop or select from filesystem
    onLoad(file, readerEvt) {
        const fileReader = new FileReader();

        fileReader.onload = (event) => {
            let scriptsCO = [];
            let scriptsCOReact = [];
            let scriptsCalculations = [];

            try {
                const importObject = JSON.parse(event.target.result);

                scriptsCO = importObject.CO.map((scriptObj) => ({
                    js: scriptObj.script,
                    name: scriptObj.name,
                    js_type: 'F',
                    js_active: 'Y',
                }));
                scriptsCOReact = importObject.COReact.map((scriptObj) => ({
                    js: scriptObj.script,
                    name: scriptObj.name,
                    js_type: 'R',
                    js_active: 'Y',
                }));
                scriptsCalculations = importObject.Calculations.map((scriptObj) => ({
                    js: scriptObj.script,
                    name: scriptObj.name,
                    js_type: 'C',
                    js_active: 'Y',
                }));
            } catch (e) {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrScriptsLoadFailed'),
                    show: true,
                    allowCountdown: true,
                    serverError: e,
                });

                // keep the current state on error
                return;
            }

            this.resetState();
            this.setState({
                scriptsCO,
                scriptsCOReact,
                scriptsCalculations,
            });
        };

        fileReader.readAsText(file);
    }

    resetState() {
        this.setState({
            scriptsCO: [],
            scriptsCOReact: [],
            scriptsCalculations: [],
            scriptsCOExpanded: false,
            scriptsCOReactExpanded: false,
            scriptsCalculationsExpanded: false,
            allScriptsActive: true,
            scriptsCOActive: true,
            scriptsCOReactActive: true,
            scriptsCalculationsActive: true,
        });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.resetState();
        this.props.onClose?.();
    }

    render() {
        const allScriptsAmount = (
            this.state.scriptsCO.length
            + this.state.scriptsCOReact.length
            + this.state.scriptsCalculations.length
        );

        const scriptsCOToActivateAmount = this.state.scriptsCO.filter((script) => script.js_active === 'Y').length;
        const scriptsCOReactToActivateAmount = this.state.scriptsCOReact.filter((script) => script.js_active === 'Y').length;
        const scriptsCalculationsToActivateAmount = this.state.scriptsCalculations.filter((script) => script.js_active === 'Y').length;

        const allToActivateAmount = (
            scriptsCOToActivateAmount
            + scriptsCOReactToActivateAmount
            + scriptsCalculationsToActivateAmount
        );

        let sectionIndex = 0;

        // eslint-disable-next-line no-return-assign
        return (
            <Modal
                isOpen={this.props.isOpen}
                width={this.props.width}
                onEsc={this.closeModal}
                loading={false}
            >
                <div className="modal import-entities-modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsImportScripts" inModal>
                            <TabsButton
                                key="import"
                                className="button-green"
                                icon="icon-download-10"
                                isActive
                                onClick={this.handleImport}
                                hideTitle={false}
                            >
                                {i18next.t('doImport')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <div className="row text-center">
                        <h5>
                            {i18next.t('scriptsImport')}
                        </h5>
                    </div>
                    <Dropzone
                        onLoad={this.onLoad}
                        className="dropzoneAttach"
                        activeClassName="dropzoneAttachActive"
                        multiple={false}
                    >
                        <div
                            dangerouslySetInnerHTML={{
                                __html: i18next.t('dragAddFile', {
                                    here: `<b>${i18next.t('here')}</b>`,
                                }),
                            }}
                        />
                    </Dropzone>
                    <div className="row form-container">
                        <table
                            className={
                                `import-entities-modal-table${
                                    this.state.scriptsCO.length === 0
                                    && this.state.scriptsCOReact.length === 0
                                    && this.state.scriptsCalculations.length === 0
                                        ? ' import-entities-modal-table-empty'
                                        : ''
                                }`
                            }
                        >
                            <thead>
                                <tr>
                                    <th>{i18next.t('name')}</th>
                                    <th width={100} className="text-center">{i18next.t('activate')}</th>
                                    <th width={100} className="text-center">{i18next.t('remove')}</th>
                                    <th width={100} />
                                </tr>
                            </thead>
                            <tbody>
                                {this.state.scriptsCO.length > 0 && (
                                    <ScriptsTableSectionHeaderRow
                                        title="CO"
                                        index={sectionIndex += 1}
                                        expanded={this.state.scriptsCOExpanded}
                                        onExpand={() => this.handleExpandTableSection('F')}
                                        active={this.state.scriptsCOActive}
                                        onSetToActivate={(_name, value) => this.handleSetToActivateAll(value, 'F')}
                                        onRemove={() => this.handleRemoveAll('F')}
                                        scriptCount={this.state.scriptsCO.length}
                                        toActivateCount={scriptsCOToActivateAmount}
                                    />
                                )}
                                {this.state.scriptsCOExpanded && this.state.scriptsCO.map((script, index) => (
                                    <ScriptTableRow
                                        key={index}
                                        script={script}
                                        index={index}
                                        onSetToActivate={(_name, value) => this.handleSetToActivate(index, value, script.js_type)}
                                        onRemove={() => this.handleRemove(index, script.js_type)}
                                    />
                                ))}
                                {this.state.scriptsCOReact.length > 0 && (
                                    <ScriptsTableSectionHeaderRow
                                        title="CO React"
                                        index={sectionIndex += 1}
                                        expanded={this.state.scriptsCOReactExpanded}
                                        onExpand={() => this.handleExpandTableSection('R')}
                                        active={this.state.scriptsCOReactActive}
                                        onSetToActivate={(_name, value) => this.handleSetToActivateAll(value, 'R')}
                                        onRemove={() => this.handleRemoveAll('R')}
                                        scriptCount={this.state.scriptsCOReact.length}
                                        toActivateCount={scriptsCOReactToActivateAmount}
                                    />
                                )}
                                {this.state.scriptsCOReactExpanded && this.state.scriptsCOReact.map((script, index) => (
                                    <ScriptTableRow
                                        key={index}
                                        script={script}
                                        index={index}
                                        onSetToActivate={(_name, value) => this.handleSetToActivate(index, value, script.js_type)}
                                        onRemove={() => this.handleRemove(index, script.js_type)}
                                    />
                                ))}
                                {this.state.scriptsCalculations.length > 0 && (
                                    <ScriptsTableSectionHeaderRow
                                        title={i18next.t('calculations')}
                                        index={sectionIndex += 1}
                                        expanded={this.state.scriptsCalculationsExpanded}
                                        onExpand={() => this.handleExpandTableSection('C')}
                                        active={this.state.scriptsCalculationsActive}
                                        onSetToActivate={(_name, value) => this.handleSetToActivateAll(value, 'C')}
                                        onRemove={() => this.handleRemoveAll('C')}
                                        scriptCount={this.state.scriptsCalculations.length}
                                        toActivateCount={scriptsCalculationsToActivateAmount}
                                    />
                                )}
                                {this.state.scriptsCalculationsExpanded && this.state.scriptsCalculations.map((script, index) => (
                                    <ScriptTableRow
                                        key={index}
                                        script={script}
                                        index={index}
                                        onSetToActivate={(_name, value) => this.handleSetToActivate(index, value, script.js_type)}
                                        onRemove={() => this.handleRemove(index, script.js_type)}
                                    />
                                ))}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td>
                                        <b>
                                            {i18next.t('all')}
                                        </b>
                                        {` (${i18next.t('scripts')}: ${allScriptsAmount} | ${i18next.t('activate')}: ${allToActivateAmount}):`}
                                    </td>
                                    <td width={100} className="text-center">
                                        <CheckboxPure
                                            name="activateAll"
                                            title={i18next.t('activateAll')}
                                            value={this.state.allScriptsActive}
                                            simple
                                            onChange={(_name, value) => this.handleSetToActivateAll(value)}
                                        />
                                    </td>
                                    <td width={100} className="text-center">
                                        <span
                                            role="button"
                                            className="icon icon-delete-1 pointer"
                                            title={i18next.t('removeAll')}
                                            onClick={() => this.handleRemoveAll()}
                                        />
                                    </td>
                                    <td width={100} />
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </Modal>
        );
    }

}

ImportScriptsModal.propTypes = {
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    onImport: PropTypes.func.isRequired,
};

export default ImportScriptsModal;
