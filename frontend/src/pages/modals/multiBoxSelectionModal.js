import PropTypes from 'prop-types';
import i18next from 'i18next';
import { CheckboxList } from '../../components/form/checkboxList.react';
import { CheckboxPure } from '../../components/form/checkbox.react';
import { TextPure } from '../../components/form/text.react';
import { sortArrayBy, replaceVariablesInUrl } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';

import React from 'react';
import ReactDOM from 'react-dom';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Label from '../../components/form/label.react';
import WrapComponent from '../../components/form/wrapComponent.react';

class MultiBoxSelectionModal extends React.Component {

    constructor(props) {
        super();

        this.state = {
            loading: false,
            searchTerm: '',
            selectedOnly: false,
            selectAll: props.options.length === props.valuesObjs.length,
            options: props.meta.structuredList
                ? props.options
                : sortArrayBy(props.options, 'title'),
            offset: 0,
            searchOffset: 0,
            selectedValues: props.valuesObjs, // rightValues from multiBox
            limit: 50,
            addWaypoint: true,
            totalCount: 0,
            searchTotalCount: 0,
        };

        this.timeout = null;
        this.loadData = this.loadData.bind(this);
        this.loadNextRecords = this.loadNextRecords.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.addValues = this.addValues.bind(this);
        this.valueChange = this.valueChange.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleInvalidSubmit = this.handleInvalidSubmit.bind(this);
        this.selectedOnlyChange = this.selectedOnlyChange.bind(this);
        this.selectedAllChange = this.selectedAllChange.bind(this);
        this.search = this.search.bind(this);
        this.resetSearch = this.resetSearch.bind(this);
        this.getLimit = this.getLimit.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }

    componentDidMount() {
        window.addEventListener('resize', this.handleResize);

        if (this.props.loadData) {
            this.loadData();
        }

        setTimeout(() => {
            this.setCheckboxListHeight();
        });
    }

    componentWillUnmount() {
        document.removeEventListener('resize', this.handleResize);

        if (this.loadDataRequest) {
            this.loadDataRequest.cancel();
        }
    }

    // getLimit is not called, limit is 50
    getLimit() {
        const limit = Math.floor((window.innerHeight - 196) / 27);
        this.setState({ limit: limit });
        return limit;
    }

    async getApiRequest(searchTerm) {
        const {
            dataUrl,
            multiBoxType,
            dataFilter,
            expertModeDataFilter,
            meta,
            colIndex,
            dynTableId,
            dlName,
        } = this.props;
        const { offset, searchOffset, limit } = this.state;
        let url;
        let apiRequest;
        let filterBy;
        let orderBy;
        let sort = 'asc';
        let metaFilter = null;
        const uLimit = !limit ? this.getLimit() : limit; // getLimit is not called, limit is 50
        const uOffset = searchTerm ? searchOffset : offset;

        switch (multiBoxType) {
            case 'DLU':
                url = dataUrl || '/users/active';
                filterBy = 'user_display_name';
                orderBy = 'user_display_name';
                break;
            case 'DLR':
                url = dataUrl || '/roles';
                filterBy = 'role_name';
                orderBy = 'role_name';
                break;
            case 'DLnull':
                if (dlName) {
                    url = `/tasks/dyn-list/${encodeURI(dlName)}`;
                    filterBy = 'dlist_value';
                    orderBy = 'dlist_value';
                }
                break;
            case 'DT':
                url = `/dyn-table/${dynTableId}/values/col_${colIndex}?nullsLast=true`;
                filterBy = `col_${colIndex}`;
                let metaOrder = null;
                let metaSort = null;

                if (_.has(meta, 'dynTable')) {
                    const metaDynTable = meta.dynTable;

                    if (_.has(metaDynTable, 'filter')) {
                        metaFilter = metaDynTable.filter;
                    }
                    if (_.has(metaDynTable, 'order')) {
                        metaOrder = metaDynTable.order;
                    }
                    if (_.has(metaDynTable, 'sort')) {
                        metaSort = metaDynTable.sort;
                    }
                }

                orderBy = metaOrder !== null ? metaOrder : `col_${colIndex}`;
                sort = metaSort !== null ? metaSort : 'asc';

                break;
        }

        if (url) {
            // Prepare filter and sort
            url += `${url.indexOf('?') === -1 ? '?' : '&'}offset=${uOffset}&limit=${uLimit}`;

            if (dataFilter) {
                url += `&filter=${encodeURIComponent(dataFilter)}`;
            } else if (
                expertModeDataFilter &&
                this.expertModeIsActive === false
            ) {
                if (expertModeDataFilter) {
                    if (
                        Array.isArray(this.valuesArr) &&
                        !_.isEmpty(this.valuesArr)
                    ) {
                        // do not hide selected value
                        const filterArr = [];
                        this.valuesArr.forEach((val) => {
                            filterArr.push(`id<eq>"${Number(val)}"`);
                        });
                        const filterString = filterArr.join('<or>');

                        url += `&filter=${encodeURIComponent(`${expertModeDataFilter}<or>(${filterString})`)}`;
                    } else {
                        url += `&filter=${encodeURIComponent(expertModeDataFilter)}`;
                    }
                }
            } else if (metaFilter) {
                url += `&filter=${encodeURIComponent(await this.replaceVariablesInUrl(metaFilter))}`;
            }

            if (
                (dataFilter ||
                    metaFilter ||
                    (expertModeDataFilter && !this.expertModeIsActive)) &&
                searchTerm
            ) {
                url += encodeURIComponent(
                    `<and>${filterBy}<like>"%${searchTerm}%"`,
                );
            } else if (searchTerm) {
                url += `&filter=${encodeURIComponent(`${filterBy}<like>"%${searchTerm}%"`)}`;
            }

            if (orderBy) {
                url += `&order=${orderBy}&sort=${sort}`;
            }

            apiRequest = ApiRequest.get(url);
        }

        return apiRequest;
    }

    loadData(searchTerm, push = false) {
        this.setState({ loading: true });

        const { multiBoxType, dlName } = this.props;

        this.loadDataRequest = this.getApiRequest(searchTerm);

        if (this.loadDataRequest) {
            this.loadDataRequest
                .then((payload) => {
                    let optionsArr = [];
                    if (push) {
                        optionsArr = _.cloneDeep(this.state.options);
                    }

                    payload.items.forEach((item) => {
                        if (multiBoxType === 'DLU') {
                            const user = {
                                value: item.id,
                                title: item.user_display_name,
                            };
                            optionsArr.push(user);
                        } else if (multiBoxType === 'DLR') {
                            const role = {
                                value: item.id,
                                title: item.role_name,
                            };
                            optionsArr.push(role);
                        } else if (multiBoxType === 'DT') {
                            // Dynamic table - '/dyn-table/id/values'
                            const dtObj = {
                                value: item.dtv_index,
                                title: item[`col_${this.props.colIndex}`],
                            };

                            optionsArr.push(dtObj);
                        } else if (dlName) {
                            // DL null
                            optionsArr.push({
                                value: item.dlist_value,
                                title: item.dlist_value,
                            });
                        }
                    });

                    if (searchTerm) {
                        this.setState({
                            options: optionsArr,
                            searchTotalCount: payload.total_count,
                            loading: false,
                            addWaypoint: true,
                        });
                    } else {
                        this.setState({
                            options: optionsArr,
                            loadedOptions: optionsArr,
                            totalCount: payload.total_count,
                            loading: false,
                        });
                    }
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedMultiBoxData'),
                        serverError: errorMessage,
                    });
                });
        }
    }

    loadNextRecords() {
        if (
            this.state.options.length <
            (this.state.searchTerm
                ? this.state.searchTotalCount
                : this.state.totalCount)
        ) {
            const offset = this.state.offset + this.state.limit;
            const searchOffset = this.state.searchOffset + this.state.limit;

            this.setState(
                {
                    offset: this.state.searchTerm ? this.state.offset : offset,
                    searchOffset: this.state.searchTerm
                        ? searchOffset
                        : this.state.searchOffset,
                },
                () => {
                    this.loadData(this.state.searchTerm, true);
                },
            );
        }
    }

    setCheckboxListHeight() {
        const buttons = ReactDOM.findDOMNode(this.buttonsRef);
        const checkboxList = ReactDOM.findDOMNode(this.checkboxListRef);
        const topWrapper = ReactDOM.findDOMNode(this.topWrapperRef);

        if (buttons && checkboxList && topWrapper) {
            const height =
                buttons.getBoundingClientRect().top -
                topWrapper.offsetHeight -
                55;
            checkboxList.setAttribute('style', `height: ${height / 16}rem`); // Math.floor(
        }
    }

    replaceVariablesInUrl(url) {
        return replaceVariablesInUrl(url, this);
    }

    handleResize() {
        this.setCheckboxListHeight();
    }

    closeModal(e) {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }
        this.props.onClose();
    }

    addValues(e) {
        if (e) e.preventDefault();

        const values = this.state.selectedValues;

        if (this.props.meta.structuredList) {
            values.forEach((value) => {
                value.title = value.title.replace(
                    /^(\u00A0\u00A0\u00A0\u00A0)*/,
                    '',
                );
            });
        }

        this.props.parent.addValuesFromModal(values);
        this.closeModal();
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleInvalidSubmit() {
        this.onFormActionInvalid();
    }

    // display selected/all options
    selectedOnlyChange(name, value) {
        this.scrollToTop();
        const selectedValues = sortArrayBy(this.state.selectedValues, 'title');

        // DLU, DLR, DLnull with more than 50 items (like multiBox with searchBox)
        if (this.props.loadData) {
            if (value === true) {
                this.setState(
                    {
                        selectedOnly: true,
                        options: selectedValues,
                    },
                    () => {
                        if (this.state.searchTerm) {
                            this.search(null, this.state.searchTerm);
                        }
                    },
                );
            } else if (this.state.searchTerm) {
                this.setState(
                    {
                        selectedOnly: false,
                        searchOffset: 0,
                        addWaypoint: false,
                    },
                    () => {
                        this.loadData(this.state.searchTerm);
                    },
                );
            } else {
                this.setState({
                    selectedOnly: false,
                    options: this.state.loadedOptions,
                });
            }
        } else {
            // other (like classic multiBox)
            this.setState(
                {
                    selectedOnly: !this.state.selectedOnly,
                    options:
                        value === true ? selectedValues : this.props.options,
                },
                () => {
                    if (!_.isEmpty(this.state.searchTerm)) {
                        this.search(null, this.state.searchTerm);
                    }
                },
            );
        }
    }

    selectedAllChange() {
        const { selectAll, searchTerm } = this.state;
        let { options } = this.state;

        if (searchTerm !== '') {
            // select only filtered
            options = _.filter(options, { hide: false });
        }

        this.setState({
            selectAll: !selectAll,
            selectedValues: selectAll ? [] : [...options],
        });
    }

    // DLU, DLR, DLnull with more than 50 items (like multiBox with searchBox)
    valueChange(name, valueObj, isChecked) {
        const selectedValuesClone = _.cloneDeep(this.state.selectedValues);

        if (isChecked) {
            selectedValuesClone.push(valueObj);
        } else if (!isChecked) {
            const findIndex = _.findIndex(
                selectedValuesClone,
                ['value', valueObj.value],
            );
            selectedValuesClone.splice(findIndex, 1);
        }

        this.setState({ selectedValues: selectedValuesClone });
    }

    search(name, searchTerm) {
        this.scrollToTop();

        if (this.props.loadData && !this.state.selectedOnly) {
            if (searchTerm !== '') {
                this.setState(
                    {
                        searchTerm: searchTerm,
                    },
                    () => {
                        if (this.loadDataRequest) {
                            this.loadDataRequest.cancel();
                        }

                        if (this.timeout) {
                            clearTimeout(this.timeout);
                        }

                        this.timeout = setTimeout(() => {
                            this.setState(
                                {
                                    searchOffset: 0,
                                },
                                () => {
                                    this.loadData(searchTerm);
                                },
                            );
                        }, 500);
                    },
                );
            } else {
                this.setState({
                    searchTerm: searchTerm,
                    options: this.state.loadedOptions,
                });
            }
        } else {
            const optionsClone = _.cloneDeep(this.state.options);

            optionsClone.forEach((option) => {
                if (
                    option.title
                        .toLowerCase()
                        .indexOf(searchTerm.toLowerCase()) === -1
                ) {
                    option.hide = true;
                } else {
                    option.hide = false;
                }
            });

            this.setState({
                options: optionsClone,
                searchTerm: searchTerm,
            });
        }
    }

    resetSearch() {
        if (this.state.searchTerm) {
            this.scrollToTop();
            this.search(null, '');
        }
    }

    scrollToTop() {
        const checkboxList = document.querySelector(
            '.mb-selection .checkbox-list',
        );
        checkboxList.scrollTop = 0;
    }

    render() {
        const values = _.map(this.state.selectedValues, 'value');
        const paging = this.props.loadData && !this.state.selectedOnly;
        const addWaypoint =
            this.state.options.length <
            (this.state.searchTerm
                ? this.state.searchTotalCount
                : this.state.totalCount);
        const { meta, loadData } = this.props;

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.addValues}
                className="mobile-modal"
            >
                <div className="modal mb-selection">
                    <div className="mb-label">{this.props.label}</div>
                    <Form
                        name="formValues"
                        className="form-container"
                        onValidSubmit={this.handleValidSubmit}
                        onInvalidSubmit={this.handleInvalidSubmit}
                        oneColumn
                    >
                        <WrapComponent
                            key="wrapper"
                            ref={(r) => {
                                this.topWrapperRef = r;
                            }}
                            className="top-wrapper"
                        >
                            <TextPure
                                key="search"
                                name="search"
                                value={this.state.searchTerm}
                                onChange={this.search}
                                placeholder={i18next.t('search')}
                                deleteButton={this.resetSearch}
                            />
                            <CheckboxPure
                                key="selectedOnly"
                                name="selectedOnly"
                                text={i18next.t('showSelectedOnly')}
                                value={this.state.selectedOnly}
                                onChange={this.selectedOnlyChange}
                            />
                            {meta.selectAll && !loadData && (
                                <CheckboxPure
                                    key="selectAlL"
                                    name="selectAlL"
                                    text={i18next.t('selectAll')}
                                    value={this.state.selectAll}
                                    onChange={this.selectedAllChange}
                                />
                            )}
                            <div key="counts-info" className="info">
                                {paging &&
                                    !this.state.selectedOnly &&
                                    `${this.state.options.length}/${this.state.searchTerm ? this.state.searchTotalCount : this.state.totalCount}`}
                            </div>
                            {!_.isEmpty(this.state.options) &&
                                _.every(this.state.options, { hide: true }) && (
                                <Label
                                    key="notFound"
                                    name="notFound"
                                    value={i18next.t('notFound')}
                                />
                            )}
                        </WrapComponent>
                        <CheckboxList
                            key="values"
                            ref={(r) => {
                                this.checkboxListRef = r;
                            }}
                            value={values}
                            options={this.state.options}
                            onChangeIndividually={this.valueChange}
                            emptyOptionsText={
                                _.isEmpty(this.state.searchTerm)
                                    ? 'empty'
                                    : 'notFound'
                            }
                            addWaypoint={
                                paging && addWaypoint && this.state.addWaypoint
                            }
                            onEnterWaypoint={this.loadNextRecords}
                            valueTypeString={this.props.valueTypeString}
                        />
                    </Form>
                    <div className="row">
                        <TabsButtonsOther
                            key="buttonsBulk"
                            inModal
                            ref={(r) => {
                                this.buttonsRef = r;
                            }}
                        >
                            <TabsButton
                                key="insert"
                                icon="icon-check-2"
                                isActive
                                onClick={this.addValues}
                                hideTitle={false}
                            >
                                {i18next.t('ok')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                </div>
            </Modal>
        );
    }

}

MultiBoxSelectionModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    parent: PropTypes.objectOf(PropTypes.any).isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    options: PropTypes.arrayOf(PropTypes.object),
    valuesObjs: PropTypes.arrayOf(PropTypes.object),
    addValuesFromModal: PropTypes.func.isRequired,
    loadData: PropTypes.bool,
    multiBoxType: PropTypes.string,
    dlName: PropTypes.string,
    dataUrl: PropTypes.string,
    dataFilter: PropTypes.string,
    expertModeDataFilter: PropTypes.string,
    dynTableId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // dynamic table
    colIndex: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // dynamic table
    meta: PropTypes.objectOf(PropTypes.any),
    findTaskVarValue: PropTypes.func, // dynamic table
    valueTypeString: PropTypes.bool, // false = type number
};

MultiBoxSelectionModal.defaultProps = {
    isOpen: false,
    width: 'xlarge',
    options: [],
    valuesObjs: [],
    loadData: false,
    multiBoxType: null,
    dlName: null,
    dataUrl: null,
    dataFilter: null,
    expertModeDataFilter: null,
    dynTableId: null,
    colIndex: null,
    meta: {},
    findTaskVarValue: null,
    valueTypeString: false,
};

export default MultiBoxSelectionModal;
