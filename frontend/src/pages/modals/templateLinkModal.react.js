import i18next from 'i18next';
import PropTypes from 'prop-types';
import browserHistory from '../../common/history';
import { guid } from '../../common/utils';
import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Tabs from '../../components/tabs/tabs.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsWrapper from '../../components/tabs/tabsWrapper.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import TemplateLinkStore from '../../flux/templateLink.store';
import TemplateLinkActions from '../../flux/templateLink.actions';
import alt from '../../flux/alt';
import DiagramStore from '../../components/diagram/diagram.store';
import DiagramActions from '../../components/diagram/diagram.actions';
import Factory from '../../flux/factory';
import TemplateLinkForm from '../templateLink/templateLinkForm.react';
import TemplateLinkFunctions from '../templateLink/templateLinkFunctions.react';
import ConfirmModal from './confirmModal.react';

class TemplateLinkModal extends React.Component {

    constructor(props) {
        super();
        this.state = _.extend(
            {
                // loading: false,
                formIsPristine: true,
                conditionsChanged: false,
                confirmModalIsOpen: false,
            },
            TemplateLinkStore.getState(),
        );

        this.initDiagramListen = this.initDiagramListen.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this._onChange = this._onChange.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.handleAddVariable = this.handleAddVariable.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.saveLink = this.saveLink.bind(this);
        this.postData = this.postData.bind(this);
        this.conditionsChanged = this.conditionsChanged.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
    }

    UNSAFE_componentWillMount() {
        const obj = Factory.registerFlux(
            DiagramStore,
            DiagramActions,
            this.props.name,
        );
        this.diagramAction = obj.action;
        this.diagramStore = obj.store;
        this.initDiagramListen();
    }

    // Listen to diagram store
    initDiagramListen() {
        this.setState(this.diagramStore.getState());
    }

    componentDidMount() {
        const { templateId, versionId } = this.props;
        const { linkId } = this.props;

        TemplateLinkStore.listen(this._onChange);
        TemplateLinkActions.loadTemplatesVariables.defer(templateId, versionId);

        const link =
            _.find(
                this.diagramStore.state.linksChanged,
                ['ttasklink_id', linkId],
            ) ||
            _.find(
                this.diagramStore.state.linksChanged,
                ['ttasklink_id', parseInt(linkId)],
            );

        if (link && link.conditions) {
            const conditionsArr = [];
            link.conditions.forEach((condition) => {
                conditionsArr.push({
                    array: {
                        title: condition.tcond_variable,
                        value: condition.tcond_variable,
                        type: condition.type,
                    },
                    condition: condition.tcond_value,
                    operator: condition.tcond_operator,
                    type: condition.type,
                });
            });

            TemplateLinkActions.setConditions.defer(conditionsArr);
        } else if (!isNaN(linkId)) {
            // ne nové linky vytvořené přímo z grafu
            TemplateLinkActions.loadLinkData.defer(linkId);
        }
    }

    componentWillUnmount() {
        TemplateLinkStore.unlisten(this._onChange);

        alt.recycle(TemplateLinkStore);
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    _onChange(state) {
        this.setState(state);
    }

    closeModal(e) {
        if (e) e.preventDefault();

        if (!this.state.formIsPristine) {
            this.setState({ confirmModalIsOpen: true });

            this.onFormAction = (data) => {
                this.postData(data, true);
            };
        } else {
            this.props.onClose();
        }
    }

    confirmSaving() {
        // save and leave
        this.refs.templateLinkForm.refs.formLink.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.props.onClose();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    handleAddVariable(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props;
        browserHistory.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-variable/new`,
            state: { closePrevPath: location.pathname },
        });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    saveLink(e) {
        if (e) e.preventDefault();

        this.onFormAction = function (data) {
            this.postData(data);
        };

        this.refs.templateLinkForm.refs.formLink.submit();
    }

    postData(data, callback) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'success',
            message: i18next.t('alrSaved'),
        });

        const { templateId, versionId, linkId } = this.props;
        const obj = TemplateLinkFunctions.getPostObject(
            data,
            templateId,
            versionId,
            linkId,
            this.state.link,
        );

        if (callback) {
            this.props.onClose();
        }

        this.props.saveLinkChanges(obj);

        this.props.onClose();
    }

    // Important - if add or remove row in conditions or after conditions are loaded, we need know new modal height,
    // setState do rerender and modal take care of it
    conditionsChanged() {
        this.setState({ conditionsChanged: !this.state.conditionsChanged });
    }

    render() {
        let { link } = this.state;
        const storedValues =
            _.find(
                this.diagramStore.state.linksChanged,
                ['ttasklink_id', this.props.linkId],
            ) ||
            _.find(
                this.diagramStore.state.linksChanged,
                ['ttasklink_id', parseInt(this.props.linkId)],
            ) ||
            {};

        if (!_.isEmpty(storedValues)) {
            link = storedValues;
        }

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
            >
                <div className="modal tab-modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsLink" inModal>
                            <TabsButton
                                key="save"
                                icon="icon-floppy-disk"
                                enableOn={!this.state.loading}
                                onClick={this.saveLink}
                                hideTitle={false}
                            >
                                {i18next.t('save')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <TabsWrapper>
                        <Tabs noTabLinks>
                            <Tabs.Tab
                                key="templateLinkConnection"
                                title={i18next.t('link')}
                                name="tabTemplateLink"
                            >
                                <TabsButtonsOther>
                                    <TabsButton
                                        key="add"
                                        icon="icon-add-1"
                                        onClick={this.handleAddVariable}
                                        isActive
                                        tooltipCode="ttAdd"
                                    >
                                        {i18next.t('addVariable')}
                                    </TabsButton>
                                </TabsButtonsOther>
                                <TemplateLinkForm
                                    ref="templateLinkForm"
                                    link={link}
                                    formChanged={this.formChanged}
                                    handleValidSubmit={this.handleValidSubmit}
                                    conditionsChanged={this.conditionsChanged}
                                    conditions={this.state.conditions}
                                    conditionsVariablesOptions={
                                        this.state.conditionsVariablesOptions
                                    }
                                    suggestList={this.state.linkSuggestionList} // TODO rename
                                />
                            </Tabs.Tab>
                        </Tabs>
                        <ConfirmModal
                            text={i18next.t('confirmSaveChanges')}
                            width="tiny"
                            isOpen={this.state.confirmModalIsOpen}
                            onClose={this.closeConfirmModal}
                            onConfirm={this.confirmSaving}
                            onDiscard={this.cancelSaving}
                            modalInModal
                        />
                    </TabsWrapper>
                </div>
            </Modal>
        );
    }

}

TemplateLinkModal.propTypes = {
    parent: PropTypes.object,
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    templateId: PropTypes.string.isRequired,
    versionId: PropTypes.string.isRequired,
    linkId: PropTypes.string.isRequired,
};

export default TemplateLinkModal;
