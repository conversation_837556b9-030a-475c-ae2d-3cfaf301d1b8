import i18next from 'i18next';
import PropTypes from 'prop-types';
import { JSONTree } from 'react-json-tree';
import ApiRequest from '../../api/apiRequest';
import React from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';

class XmlProcessModal extends React.Component {

    constructor(props) {
        super();

        this.state = {
            loading: true,
            log: {},
        };

        this.closeModal = this.closeModal.bind(this);
        this.loadLogData = this.loadLogData.bind(this);
    }

    componentDidMount() {
        if (this.props.log === null) {
            this.loadLogData(this.props.logId);
        } else {
            this.setState({ loading: false });
        }
    }

    loadLogData(logId) {
        const url =
            this.props.tableName === 'tableImportHistory'
                ? `/xml-process-import/history/${logId}`
                : `/xml-process-import/scheduled/${logId}`;

        ApiRequest.get(url)
            .then((payload) => {
                this.setState({
                    log: payload.items,
                    loading: false,
                });
            })
            .catch((errorMessage) => {
                this.setState({ loading: false });

                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrLogsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    render() {
        const log = this.props.log || this.state.log || {};
        const id = log?.id;
        const fileName = log?.xpi_file_name || log?.xml_file_name;

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                modalOverflow
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsMaintenance" inModal>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                        {log != null && (
                            <div className="log-wrapper">
                                <div className="message">
                                    <span>{`${id}: ${fileName}`}</span>
                                    <div className="row">
                                        <div className="large-6 medium-12 columns">
                                            <JSONTree
                                                data={log}
                                                sortObjectKeys
                                                hideRoot
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </Modal>
        );
    }

}

XmlProcessModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    logId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    log: PropTypes.objectOf(PropTypes.any),
    tableName: PropTypes.string.isRequired,
};

XmlProcessModal.defaultProps = {
    log: null,
};

export default XmlProcessModal;
