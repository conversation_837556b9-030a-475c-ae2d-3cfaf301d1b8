import i18next from 'i18next';
import PropTypes from 'prop-types';
import { JSONTree } from 'react-json-tree';
import _ from 'lodash';

import React, { useState, useEffect } from 'react';
import ApiRequest from '../../api/apiRequest';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';

const PreviewCertificateModal = (props) => {
    const [loading, setLoading] = useState(true);
    const [fileData, setFileData] = useState({});

    const closeModal = (e) => {
        e.preventDefault();
        props.onClose();
    };

    const loadFileData = (fileName) => {
        ApiRequest.get(`/certificates/${fileName}`)
            .then((payload) => {
                if (payload === 'LOG_TOO_BIG_TO_LOAD') {
                    setLoading(false);
                } else {
                    setFileData(payload);
                    setLoading(false);
                }
            })
            .catch((errorMessage) => {
                setLoading(false);
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrLogsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    };

    useEffect(() => {
        if (props.fileData === null) {
            loadFileData(props.fileName);
        } else {
            setLoading(false);
        }
    }, []);

    return (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
            modalOverflow
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsMaintenance" inModal>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                    <div className="log-wrapper">
                        <div className="message">
                            <div className="row">
                                <div className="large-6 medium-12 columns">
                                    {props.fileName && (
                                        <div className="row">
                                            <span>{props.fileName}</span>
                                        </div>
                                    )}
                                    {fileData && !_.isEmpty(fileData) && (
                                        <JSONTree data={fileData} hideRoot />
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

PreviewCertificateModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    fileName: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    fileData: PropTypes.objectOf(PropTypes.any),
};

PreviewCertificateModal.defaultProps = {
    fileData: null,
};

export default PreviewCertificateModal;
