import React, { useRef, useState, useEffect } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import Multibox from '../../components/form/multiBox.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import ApiRequest from '../../api/apiRequest';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Tabs from '../../components/tabs/tabs.react';
import TabsWrapper from '../../components/tabs/tabsWrapper.react';
import Form from '../../components/form/form.react';

const SelectTableColumnsModal = (props) => {
    const [loading, setLoading] = useState(true);
    const [tableColumns, setTableColumns] = useState([]);
    const [tableColumnsLoaded, setTableColumnsLoaded] = useState(false);
    const formRef = useRef(null);

    const columnTableNames = {
        tproc_id: i18next.t('templates'),
        iproc_inst_owner_user_id: i18next.t('caseOwner'),
        iproc_inst_owner_orgstr_id: i18next.t('ownerOrganization'),
        iproc_main_iproc_id: i18next.t('parentCase'),
        header_id: i18next.t('header'),
        iproc_vis_role_id: i18next.t('IPROC_VIS_ROLE'),
        iproc_hr_role_id: i18next.t('IPROC_HR_ROLE'),
        iproc_id: i18next.t('case'),
        itask_assesment_role_id: i18next.t('ITASK_ASSESMENT_ROLE'),
        itask_assesment_orgstr_cnst: i18next.t('ITASK_ASSESMENT_ORGSTR_CNST'),
        itask_assesment_orgstr_id: i18next.t('ITASK_ASSESMENT_ORGSTR'),
        itask_assesment_ttask_id: i18next.t('ITASK_ASSESMENT_TTASK'),
        itask_orgstr_id: i18next.t('ITASK_ORGSTR'),
        itask_user_id: i18next.t('solver'),
        ttask_id: i18next.t('tskTemplate'),
        itask_assesment_user_id: i18next.t('supervis'),
        itask_assesment_tvar_id: i18next.t('variable'),
        tvar_id: i18next.t('TVAR'),
        user_id: i18next.t('user'),
    };

    useEffect(() => {
        if (!tableColumnsLoaded) {
            ApiRequest.get(props.tableUrl)
                .then((payload) => {
                    const columns = [];
                    payload.columns.forEach((col) => {
                        const {
                            column,
                            table,
                            originColumn,
                            originTable,
                            type,
                        } = col;
                        const lowerColName = column.split('.')[1].toLowerCase();
                        let title;

                        if (originColumn && originTable) {
                            const originColName = originColumn.split('.')[1];
                            const lowerOriginColName =
                                originColName.toLowerCase();

                            title = `${lowerColName} (${columnTableNames[lowerOriginColName] || originColName})`;
                        } else {
                            title = lowerColName;
                        }

                        columns.push({
                            value: column,
                            title: title,
                            type: type,
                        });
                    });

                    setTableColumns(columns);
                    setTableColumnsLoaded(true);
                })
                .catch((error) => {
                    AlertsActions.addAlert({
                        message: i18next.t('alrFailedData'),
                        type: 'error',
                    });
                });
        }
    }, []);

    useEffect(() => {
        if (tableColumnsLoaded) {
            setLoading(false);
        }
    }, [tableColumnsLoaded]);

    const closeModal = (e) => {
        if (e) e.preventDefault();
        props.onClose();
    };

    const reset = (e) => {
        if (e) e.preventDefault();
        props.onReset();
    };

    const save = (e) => {
        if (e) e.preventDefault();
        formRef.current.submit();
    };

    const handleSubmit = (data) => {
        const colsArr = [];
        data.tableColumns.forEach((col) => {
            const colObj = tableColumns.find((c) => c.value === col);
            if (colObj) {
                colsArr.push({
                    value: colObj.value,
                    title: colObj.title,
                    type: colObj.type,
                });
            }
        });
        props.onSave(colsArr);
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
            onEnter={save}
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsSelectTableColumns" inModal>
                        <TabsButton
                            key="save"
                            icon="icon-floppy-disk"
                            isActive
                            onClick={save}
                            hideTitle={false}
                        >
                            {i18next.t('save')}
                        </TabsButton>
                        <TabsButton
                            key="reset"
                            icon="icon-refresh"
                            isActive
                            onClick={reset}
                            hideTitle={false}
                        >
                            {i18next.t('reset')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <TabsWrapper>
                    <Tabs noTabLinks>
                        <Tabs.Tab
                            key="serviceTab"
                            title={i18next.t('selectTableColumns')}
                            name="selectTableColumns"
                        >
                            <Form
                                ref={formRef}
                                name="selectTableColumns"
                                className="form-container"
                                onValidSubmit={handleSubmit}
                                oneColumn
                            >
                                {tableColumnsLoaded && (
                                    <Multibox
                                        key="tableColumns"
                                        label={`${i18next.t('columns')}:`}
                                        options={tableColumns}
                                        value={props.currentValue}
                                        valueIsArrayOfObjects
                                        changeableValue
                                        changeableOptions
                                        showSearchField
                                        hasArrows
                                        sortArrayOfObjects={false}
                                        required
                                    />
                                )}
                            </Form>
                        </Tabs.Tab>
                    </Tabs>
                </TabsWrapper>
            </div>
        </Modal>
    );
};

SelectTableColumnsModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    onSave: PropTypes.func.isRequired,
    onReset: PropTypes.func.isRequired,
    tableUrl: PropTypes.string.isRequired,
    currentValue: PropTypes.arrayOf(PropTypes.any).isRequired,
};
SelectTableColumnsModal.defaultProps = {
    width: 'small',
};

export default SelectTableColumnsModal;
