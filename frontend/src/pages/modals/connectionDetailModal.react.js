import i18next from 'i18next';
import PropTypes from 'prop-types';
import { JSONTree } from 'react-json-tree';
import { momentFormatDate } from '../../common/utils';
import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class ConnectionDetailModal extends React.Component {

    constructor(props) {
        super();

        this.closeModal = this.closeModal.bind(this);
        this.cancelQuery = this.cancelQuery.bind(this);
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    cancelQuery(e) {
        e.preventDefault();
        this.props.openConfirmModal();
    }

    render() {
        const { connection } = this.props;
        const formatDate = momentFormatDate(
            connection.timestamp,
            false,
            true,
            true,
        );

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={false}
                width={this.props.width}
                onEsc={this.closeModal}
                modalOverflow
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsMaintenance" inModal>
                            <TabsButton
                                key="cancelQuery"
                                icon="icon-stop-2"
                                isActive
                                onClick={this.cancelQuery}
                                hideTitle={false}
                            >
                                {i18next.t('cancel')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                        <div className="log-wrapper">
                            <div className="timestamp">{formatDate}</div>
                            <div className="message">
                                {connection.query || '\u00A0'}
                                <div className="row">
                                    <JSONTree data={connection} hideRoot />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </Modal>
        );
    }

}

ConnectionDetailModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    openConfirmModal: PropTypes.func.isRequired,
    connection: PropTypes.objectOf(PropTypes.any),
};

ConnectionDetailModal.defaultProps = {
    connection: {},
};

export default ConnectionDetailModal;
