import i18next from 'i18next';
import PropTypes from 'prop-types';
import { SelectBox } from '../../components/form/selectBox.react';

import React from 'react';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

class EditorSnippetModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            snippets: [],
            loading: true,
        };

        this.loadsnippets = this.loadsnippets.bind(this);
        this.insertSnippet = this.insertSnippet.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }

    componentDidMount() {
        this.loadsnippets();
    }

    loadsnippets() {
        const sysVars = [
            {
                value: '{overview:<PERSON><PERSON><PERSON><PERSON> p<PERSON>ehledu|Číslo=={Číslo}|table {border:1px solid #999999;}|caseLinkColumn=2}',
                title: '{overview}',
            },
            {
                value: '{notes|table {border:1px solid blue; border-collapse: collapse; width: 100%;}}',
                title: '{notes}',
            },
            { value: '{css:td {color:red;}}', title: '{css}' },
            {
                value: '{history|table {border:1px solid purple; border-collapse: collapse; width: 100%;}}',
                title: '{history}',
            },
            { value: '{attachments|td {color:red;}}', title: '{attachments}' },
        ];

        this.setState({
            snippets: sysVars,
            loading: false,
        });
    }

    insertSnippet(e) {
        if (e) e.preventDefault();

        if (!this.refs.snippet.state.open) {
            const snippet = this.refs.snippet.getValue();

            if (typeof snippet !== 'undefined' && snippet !== null) {
                this.props.parent.addVariable(snippet.value);
            }

            this.closeModal();
        }
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    render() {
        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                onEnter={this.insertSnippet}
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsEditorVar" inModal>
                            <TabsButton
                                key="insert"
                                icon="icon-check-2"
                                enableOn={!this.state.loading}
                                onClick={this.insertSnippet}
                                hideTitle={false}
                            >
                                {i18next.t('insert')}
                            </TabsButton>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                    </div>
                    <Form
                        name="formSnippet"
                        className="form-container"
                        oneColumn
                    >
                        <SelectBox
                            ref="snippet"
                            key="snippet"
                            label={`${i18next.t('snippet')}:`}
                            options={this.state.snippets}
                            nullable={false}
                            value={this.state.snippets[0]}
                        />
                    </Form>
                </div>
            </Modal>
        );
    }

}

EditorSnippetModal.propTypes = {
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    parent: PropTypes.object,
};

export default EditorSnippetModal;
