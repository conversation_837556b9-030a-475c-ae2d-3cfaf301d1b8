import PropTypes from 'prop-types';
import i18next from 'i18next';
import { useState } from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import { checkLangMutation } from '../../common/utils';

import React from 'react';
import _ from 'lodash';
import TabsButton from '../../components/tabs/tabsButton.react';
import Table from '../../components/table/table.react';
import Column from '../../components/table/column.react';

const ReportTableModal = (props) => {
    const [loading, setLoading] = useState(false);

    const closeModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onClose();
    };

    const translateLabel = (val, row) => {
        if (_.has(row, 'date_parts')) {
            // data series
            const labelValue = checkLangMutation(row, 'label');
            const labelArr = labelValue.split('-');
            const trLabelArr = [];

            // translate MONTH and WEEKDAY
            labelArr.forEach((item, i) => {
                if (row.date_parts[i] === 'MONTH') {
                    const value =
                        item === ''
                            ? i18next.t('unfilled')
                            : i18next.t(`month${item}`);
                    trLabelArr.push(value);
                } else if (row.date_parts[i] === 'WEEKDAY') {
                    const daysTrArr = [
                        'mon',
                        'tue',
                        'wed',
                        'thu',
                        'fri',
                        'sat',
                        'sun',
                    ];
                    const value =
                        item === ''
                            ? i18next.t('unfilled')
                            : i18next.t(daysTrArr[item - 1]);
                    trLabelArr.push(value);
                } else {
                    const value = item === '' ? i18next.t('unfilled') : item;
                    trLabelArr.push(value);
                }
            });

            return trLabelArr.join('-');
        }

        const label = checkLangMutation(row, 'label');
        return label || i18next.t('unfilled');
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
        >
            <div className="modal">
                <TabsButtonsOther key="buttonsMaintenance" inModal>
                    <TabsButton
                        key="close"
                        icon="icon-delete-1"
                        isActive
                        onClick={closeModal}
                        hideTitle={false}
                    >
                        {i18next.t('close')}
                    </TabsButton>
                </TabsButtonsOther>
                <Table
                    key={`reportTable${props.graphId}`}
                    name={`reportTable${props.graphId}`}
                    apiUrl={`/report-graphs/${props.graphId}/table`}
                    totalCount={false}
                    loading={loading}
                    canOpenNewTab={false}
                    isInModal
                    isExportable={false}
                    allowInlineFiltering={false}
                    allowSorting={false}
                >
                    {props.isGroups
                        ? [
                            <Column
                                key="label"
                                title={i18next.t('label')}
                                name="label"
                                type="text"
                            />,
                            <Column
                                key="value"
                                title={i18next.t('value')}
                                name="value"
                                type="text"
                            />,
                            <Column
                                key="group"
                                title={i18next.t('group')}
                                name="group"
                                type="text"
                            />,
                        ]
                        : [
                            <Column
                                key="label"
                                title={i18next.t('label')}
                                name="label"
                                type="text"
                                renderer={translateLabel}
                            />,
                            <Column
                                key="value"
                                title={i18next.t('value')}
                                name="value"
                                type="text"
                            />,
                        ]}
                </Table>
            </div>
        </Modal>
    );
};

ReportTableModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    graphId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    isGroups: PropTypes.bool.isRequired,
};

export default ReportTableModal;
