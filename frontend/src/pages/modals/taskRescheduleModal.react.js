import i18next from 'i18next';
import PropTypes from 'prop-types';
import React, { useRef } from 'react';
import { CalendarWithClockpicker } from '../../components/form/calendarWithClockpicker.react';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Form from '../../components/form/form.react';

const date = new Date();
const yesterday = date.setDate(date.getDate() - 1);

const TaskRescheduleModal = (props) => {
    const formRef = useRef(null);
    const newDateRef = useRef(null);

    const closeModal = (e) => {
        if (e) e.preventDefault();
        props.onClose();
    };

    const save = (e) => {
        if (e) {
            e.preventDefault();
        }

        if (
            !(newDateRef && newDateRef.state && newDateRef.state.calendarIsOpen)
        ) {
            formRef.current.submit();
        }
    };

    const handleSubmit = (data) => {
        props.onSave(data);
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            onEnter={save}
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsMaintenance" inModal>
                        <TabsButton
                            key="save"
                            icon="icon-floppy-disk"
                            onClick={save}
                            hideTitle={false}
                            isActive
                        >
                            {i18next.t('save')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <Form
                    ref={formRef}
                    name="formCalendar"
                    className="form-container"
                    onValidSubmit={handleSubmit}
                    oneColumn
                >
                    <CalendarWithClockpicker
                        ref={newDateRef}
                        key="newDate"
                        name="newDate"
                        label={i18next.t('dueDateStart')}
                        required
                        validations={{ isHigherOrEqualThan: yesterday }}
                        validationErrors={{
                            isDefaultRequiredValue: i18next.t('isRequired'),
                            isHigherOrEqualThan: `${i18next.t('useOnlyFutureDates')}`,
                        }}
                    />
                </Form>
            </div>
        </Modal>
    );
};

TaskRescheduleModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    onClose: PropTypes.func.isRequired,
    onSave: PropTypes.func.isRequired,
};

export default TaskRescheduleModal;
