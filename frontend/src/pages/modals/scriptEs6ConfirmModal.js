import i18next from 'i18next';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import SaveButton from '../../components/table/filter/saveButton.react';
import DropButton from '../../components/table/filter/dropButton.react';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

const ScriptEs6ConfirmModal = (props) => {
    const [inputValue, setInputValue] = useState('');
    const closeModal = (e) => {
        e?.preventDefault();
        props.onDiscard();
    };
    const confirm = (e) => {
        e?.preventDefault();
        props.onConfirm();
    };

    const handleInputChange = (event) => {
        setInputValue(event.currentTarget.value);
    };

    const buttonsVisible = inputValue.toUpperCase() === 'ES6';

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            onEnter={buttonsVisible ? confirm : () => {}}
        >
            <div className="modal script-es6-confirm-modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsConfrm" inModal>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={props.onDiscard}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <div className="center top-pad">
                    {i18next.t('calcSourceOverwriteWarning')}
                </div>
                <div className="input-wrap">
                    <label
                        htmlFor="confirm-input"
                        dangerouslySetInnerHTML={{
                            __html: i18next.t('wantToOverrideEs6'),
                        }}
                    />
                    <input
                        id="confirm-input"
                        type="text"
                        value={inputValue}
                        onChange={handleInputChange}
                        autoComplete="new-password"
                    />
                </div>
                {buttonsVisible && (
                    <div>
                        <div className="center">
                            {i18next.t('wantToContinueQ')}
                        </div>
                        <div className="buttons-wrap center">
                            <SaveButton
                                title={i18next.t('yes')}
                                onClick={confirm}
                            />
                            <DropButton
                                title={i18next.t('no')}
                                onClick={closeModal}
                            />
                        </div>
                    </div>
                )}
            </div>
        </Modal>
    );
};

ScriptEs6ConfirmModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onDiscard: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    width: PropTypes.string,
};

ScriptEs6ConfirmModal.defaultProps = {
    width: 'own-width-600',
};

export default ScriptEs6ConfirmModal;
