import React, { useEffect, useState } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import RadioButton from '../../components/form/radioButton.react';
import Form from '../../components/form/form.react';
import Label from '../../components/form/label.react';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

const EntityImportModal = (props) => {
    const [overwrite, setOverwrite] = useState('N');
    const [title, setTitle] = useState('');
    const [importOptions, setImportOptions] = useState([]);

    useEffect(() => {
        if (props.type === 'roles') {
            setTitle(i18next.t('importRoles'));
            setImportOptions([
                { value: 'N', title: i18next.t('importNewRolesOnly') },
                { value: 'Y', title: i18next.t('importNewAndExistingRoles') },
            ]);
        } else {
            if (props.type === 'users') {
                setTitle(i18next.t('importUsers'));
            } else if (props.type === 'org-units') {
                setTitle(i18next.t('importOrgUnits'));
            }
            setImportOptions([
                { value: 'N', title: i18next.t('importNewOnly') },
                { value: 'Y', title: i18next.t('importNewAndExisting') },
            ]);
        }
    }, [props.type]);

    const closeModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onClose();
    };

    const saveModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onSave(null, overwrite === 'Y');
    };

    const handleChange = (name, value) => {
        setOverwrite(value);
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
        >
            <div className="modal entity-import-modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsConfrm" inModal>
                        <TabsButton
                            key="insert"
                            icon="icon-floppy-disk"
                            isActive
                            onClick={saveModal}
                            hideTitle={false}
                        >
                            {i18next.t('import')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                {props.showWarning && (
                    <div className="center">
                        <div className="text pre-line warning">
                            {i18next.t('infoImportDataValidated')}
                        </div>
                    </div>
                )}
                <Form name="formStatus" className="form-container" oneColumn>
                    {props.textArr.map((text, index) => (
                        <Label
                            key={`label-${index}`}
                            label={text}
                            fullLabel
                            noValueHeight
                        />
                    ))}
                    <RadioButton
                        key="overwrite"
                        label={`${i18next.t(title)}:`}
                        name="overwrite"
                        value={overwrite}
                        onChange={handleChange}
                        options={importOptions}
                        helperText={
                            props.type === 'roles'
                                ? i18next.t('importRolesHelper')
                                : null
                        }
                    />
                </Form>
            </div>
        </Modal>
    );
};

EntityImportModal.propTypes = {
    textArr: PropTypes.arrayOf(PropTypes.string).isRequired,
    onClose: PropTypes.func.isRequired,
    onSave: PropTypes.func.isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    showWarning: PropTypes.bool,
    type: PropTypes.string.isRequired,
};

EntityImportModal.defaultProps = {
    isOpen: false,
    width: 'tiny',
    showWarning: false,
};

export default EntityImportModal;
