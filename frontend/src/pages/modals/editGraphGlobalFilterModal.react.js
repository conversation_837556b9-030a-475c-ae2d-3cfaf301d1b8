import i18next from 'i18next';
import PropTypes from 'prop-types';
import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { Text } from '../../components/form/text.react';
import LoggedUserStore from '../../flux/loggedUser.store';
import { SelectBox } from '../../components/form/selectBox.react';
import ShowHideComponent from '../../components/form/showHideComponent.react';
import Form from '../../components/form/form.react';
import { Checkbox } from '../../components/form/checkbox.react';
import WrapComponent from '../../components/form/wrapComponent.react';
import EmptyComponent from '../../components/form/emptyComponent.react';
import MultiBox from '../../components/form/multiBox.react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Tabs from '../../components/tabs/tabs.react';
import TabsWrapper from '../../components/tabs/tabsWrapper.react';

const EditGraphGlobalFilterModal = (props) => {
    const formRef = useRef(null);
    const varOptions = useRef(null);
    const [newGlobalFilter, setNewGlobalFilter] = useState({
        fill_dynamically: 'N',
    });
    const [reloadMultiBox, setReloadMultiBox] = useState(false);

    const closeModal = (e) => {
        if (e) e.preventDefault();
        props.onClose();
    };

    const save = (e) => {
        if (e) e.preventDefault();
        formRef.current.submit();
    };

    const handleSubmit = (data) => {
        if (props.newFilter) {
            props.onSave(
                newGlobalFilter,
                varOptions?.current?.state?.rightValues || [],
            );
            setNewGlobalFilter({
                fill_dynamically: 'N',
            });
        } else {
            props.onSave(data);
        }
    };

    const getMultiboxOptions = () => {
        const type = newGlobalFilter.tvar_type;
        const { datepart } = newGlobalFilter;
        if (type === 'D') {
            if (datepart === 'DAY' || datepart == null) {
                const days = [];
                for (let i = 1; i <= 31; i += 1) {
                    days.push({
                        title: String(i),
                        value: i,
                    });
                }
                return days;
            }
            if (datepart === 'WEEKDAY') {
                return [
                    {
                        title: i18next.t('mon'),
                        value: {
                            value: 1,
                            translate: 'mon',
                        },
                    },
                    {
                        title: i18next.t('tue'),
                        value: {
                            value: 2,
                            translate: 'tue',
                        },
                    },
                    {
                        title: i18next.t('wed'),
                        value: {
                            value: 3,
                            translate: 'wed',
                        },
                    },
                    {
                        title: i18next.t('thu'),
                        value: {
                            value: 4,
                            translate: 'thu',
                        },
                    },
                    {
                        title: i18next.t('fri'),
                        value: {
                            value: 5,
                            translate: 'fri',
                        },
                    },
                    {
                        title: i18next.t('sat'),
                        value: {
                            value: 6,
                            translate: 'sat',
                        },
                    },
                    {
                        title: i18next.t('sun'),
                        value: {
                            value: 7,
                            translate: 'sun',
                        },
                    },
                ];
            }
            if (datepart === 'WEEK') {
                const weeks = [];
                for (let i = 1; i <= 52; i += 1) {
                    weeks.push({
                        title: String(i),
                        value: i,
                    });
                }
                return weeks;
            }
            if (datepart === 'MONTH') {
                const months = [
                    {
                        title: i18next.t('thisMonth'),
                        value: {
                            value: '#thisMonth',
                            translate: 'thisMonth',
                        },
                    },
                    {
                        title: i18next.t('lastMonth'),
                        value: {
                            value: '#lastMonth',
                            translate: 'lastMonth',
                        },
                    },
                ];
                for (let i = 1; i <= 12; i += 1) {
                    months.push({
                        title: i18next.t(`month${i}`),
                        value: {
                            value: i,
                            translate: `month${i}`,
                        },
                    });
                }
                return months;
            }
            if (datepart === 'YEAR') {
                const thisYear = new Date().getFullYear();
                return [
                    {
                        title: i18next.t('thisYear'),
                        value: {
                            value: '#thisYear',
                            translate: 'thisYear',
                        },
                    },
                    {
                        title: i18next.t('lastYear'),
                        value: {
                            value: '#lastYear',
                            translate: 'lastYear',
                        },
                    },
                    {
                        title: `${thisYear}`,
                        value: `${thisYear}`,
                    },
                    {
                        title: `${thisYear - 1}`,
                        value: `${thisYear - 1}`,
                    },
                    {
                        title: `${thisYear - 2}`,
                        value: `${thisYear - 2}`,
                    },
                    {
                        title: `${thisYear - 3}`,
                        value: `${thisYear - 3}`,
                    },
                ];
            }
        } else if (_.startsWith(type, 'L')) {
            return newGlobalFilter.tvar_lov;
        } else if (_.startsWith(type, 'D')) {
            return null;
        }
        // leave empty for 'DT', 'LT', 'LD', 'LN' + DL: 'DLU', 'DLO', 'DLR'
        return [];
    };

    const handleNewGlobalFilterChange = (name, value) => {
        const newGlobalFilterClone = _.clone(newGlobalFilter);
        if (name === 'var') {
            newGlobalFilterClone.tvar_id = value.value;
            newGlobalFilterClone.tvar_type = value.type;
            newGlobalFilterClone.tvar_attribute = value.attribute;
            if (value.type === 'D') {
                newGlobalFilterClone.fill_dynamically = 'N';
                newGlobalFilterClone.datepart = 'DAY';
            } else {
                newGlobalFilterClone.dlist_name = value.dlist_name;
                newGlobalFilterClone.tvar_col_index = value.tvar_col_index;
                newGlobalFilterClone.tvar_lov = value.tvar_lov;
                newGlobalFilterClone.datepart = null;
            }
            setNewGlobalFilter(newGlobalFilterClone);
            setReloadMultiBox(true);
        } else {
            newGlobalFilterClone[name] = value?.value || value;
            setNewGlobalFilter(newGlobalFilterClone);
        }
    };

    useEffect(() => {
        if (reloadMultiBox) {
            setReloadMultiBox(false);
        }
    }, [reloadMultiBox]);

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEnter={save}
            onEsc={closeModal}
        >
            <div className="modal global-filter-modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsEditDynTable" inModal>
                        <TabsButton
                            key="save"
                            icon="icon-floppy-disk"
                            isActive
                            onClick={save}
                            hideTitle={false}
                        >
                            {i18next.t('save')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <TabsWrapper>
                    <Tabs noTabLinks>
                        <Tabs.Tab
                            key="newFilter"
                            title={
                                props.newFilter
                                    ? i18next.t('newFilter')
                                    : i18next.t('editFilter')
                            }
                            name="newFilter"
                        >
                            <Form
                                ref={formRef}
                                name="form"
                                className="form-container global-filters"
                                onValidSubmit={handleSubmit}
                            >
                                <Text
                                    key="rggf_name"
                                    name="rggf_name"
                                    label={`${i18next.t('defaultFilterName')}:`}
                                    value={props.globalFilter.rggf_name || null}
                                    onChange={
                                        props.newFilter
                                            ? (name, value) =>
                                                handleNewGlobalFilterChange(
                                                    'rggf_name',
                                                    value,
                                                )
                                            : () => {}
                                    }
                                    side="center"
                                    required
                                />
                                <ShowHideComponent
                                    key="newFilterLangName"
                                    side="center"
                                >
                                    {LoggedUserStore?.getState()?.languages?.map(
                                        (lang) => {
                                            return (
                                                <Text
                                                    key={`rggf_name_${lang}`}
                                                    name={`rggf_name_${lang}`}
                                                    label={`${i18next.t('filterName')}:`}
                                                    value={
                                                        props.globalFilter[
                                                            `rggf_name_${lang}`
                                                        ]
                                                    }
                                                    onChange={
                                                        props.newFilter
                                                            ? (name, value) =>
                                                                handleNewGlobalFilterChange(
                                                                      `rggf_name_${lang}`,
                                                                      value,
                                                                )
                                                            : () => {}
                                                    }
                                                    lblLang={lang}
                                                />
                                            );
                                        },
                                    )}
                                </ShowHideComponent>
                                {props.newFilter && (
                                    <Checkbox
                                        key="fillFromVar"
                                        name="fillFromVar"
                                        label={`${i18next.t('fillOptionsFromVar')}:`}
                                        value={newGlobalFilter.fillFromVar}
                                        onChange={(name, value) =>
                                            handleNewGlobalFilterChange(
                                                'fillFromVar',
                                                value,
                                            )}
                                        side="center"
                                    />
                                )}

                                {props.newFilter &&
                                    newGlobalFilter.fillFromVar && (
                                    <div
                                        key="globalFilterVarGroup"
                                        side="center"
                                    >
                                        <WrapComponent key="globalFilterVarWrap">
                                            <SelectBox
                                                key="globalFilterVar"
                                                name="globalFilterVar"
                                                label={`${i18next.t('fromVariable')}:`}
                                                value={
                                                    newGlobalFilter.tvar_id ||
                                                        null
                                                }
                                                onChange={(name, value) =>
                                                    handleNewGlobalFilterChange(
                                                        'var',
                                                        value,
                                                    )}
                                                options={
                                                    props.overviewVars.filter(
                                                        (option) => {
                                                            // can't fill filter from multi variables
                                                            return (
                                                                _.includes(
                                                                    [
                                                                        'DT',
                                                                        'DL',
                                                                        'LT',
                                                                        'LD',
                                                                        'LN',
                                                                        'D',
                                                                    ],
                                                                    option.tvar_type,
                                                                ) &&
                                                                    option.tvar_multi !==
                                                                        'X'
                                                            );
                                                        },
                                                    ) || []
                                                }
                                                dataStructureRenderer={(
                                                    option,
                                                ) => {
                                                    return {
                                                        value: option.value,
                                                        title: option.title,
                                                        type: option.tvar_type,
                                                        attribute:
                                                                option.tvar_attribute,
                                                        dlist_name:
                                                                option.dlist_name,
                                                        tvar_col_index:
                                                                option.tvar_col_index,
                                                        tvar_lov:
                                                                option.tvar_lov,
                                                    };
                                                }}
                                                nullable={false}
                                                helperText={i18next.t(
                                                    'fillOptionsFromVarHelper',
                                                )}
                                            />
                                            {newGlobalFilter.tvar_type ===
                                                    'D' && (
                                                    <EmptyComponent
                                                    key="emptyBeforeVarDatepart"
                                                    side="left"
                                                />
                                            )}
                                            {newGlobalFilter.tvar_type ===
                                                    'D' && (
                                                    <SelectBox
                                                    key="globalFilterVarDatepart"
                                                    name="globalFilterVarDatepart"
                                                    label={`${i18next.t('timeDimension')}:`}
                                                    value={
                                                        newGlobalFilter.datepart ||
                                                            'DAY'
                                                    }
                                                    options={[
                                                        {
                                                            title: i18next.t(
                                                                'dayOfMonth',
                                                            ),
                                                            value: 'DAY',
                                                        },
                                                        {
                                                            title: i18next.t(
                                                                'weekday',
                                                            ),
                                                            value: 'WEEKDAY',
                                                        },
                                                        {
                                                            title: i18next.t(
                                                                'week',
                                                            ),
                                                            value: 'WEEK',
                                                        },
                                                        {
                                                            title: i18next.t(
                                                                'monthVar',
                                                            ),
                                                            value: 'MONTH',
                                                        },
                                                        {
                                                            title: i18next.t(
                                                                'year',
                                                            ),
                                                            value: 'YEAR',
                                                        },
                                                    ]}
                                                    onChange={(
                                                        name,
                                                        value,
                                                    ) =>
                                                        handleNewGlobalFilterChange(
                                                            'datepart',
                                                            value,
                                                        )}
                                                    nullable={false}
                                                    side="right"
                                                />
                                            )}
                                        </WrapComponent>
                                    </div>
                                )}
                                {props.newFilter &&
                                    newGlobalFilter.fillFromVar &&
                                    newGlobalFilter.tvar_id &&
                                    newGlobalFilter.tvar_type !== 'D' && (
                                    <Checkbox
                                        key="fillDynamically"
                                        name="fill_dynamically"
                                        label={`${i18next.t('fillOptionsDynamically')}:`}
                                        value={
                                            newGlobalFilter.fill_dynamically ===
                                                'Y'
                                        }
                                        onChange={(name, value) =>
                                            handleNewGlobalFilterChange(
                                                'fill_dynamically',
                                                value ? 'Y' : 'N',
                                            )}
                                        side="center"
                                    />
                                )}
                                {props.newFilter &&
                                    !reloadMultiBox &&
                                    newGlobalFilter.fillFromVar &&
                                    newGlobalFilter.tvar_id &&
                                    newGlobalFilter.fill_dynamically ===
                                        'N' && (
                                        <MultiBox
                                        key="varOptions"
                                        name="varOptions"
                                        ref={varOptions}
                                        label={`${i18next.t('values')}:`}
                                        multiBoxType={
                                            newGlobalFilter.tvar_type +
                                                (newGlobalFilter.tvar_attribute ||
                                                    '')
                                        }
                                        options={getMultiboxOptions()}
                                        dynTableId={
                                            newGlobalFilter.dlist_name ||
                                                null
                                        }
                                        colIndex={
                                            newGlobalFilter.tvar_col_index ||
                                                null
                                        }
                                        showComponentLabel
                                        changeableOptions
                                        // changeableValue
                                        valueIsArrayOfObjects={
                                            newGlobalFilter.tvar_type ===
                                                'D'
                                                ? true
                                                : null
                                        }
                                        hasArrows
                                        showSearchField
                                        meta={{ selectAll: true }}
                                        side="center"
                                    />
                                )}
                            </Form>
                        </Tabs.Tab>
                    </Tabs>
                </TabsWrapper>
            </div>
        </Modal>
    );
};

EditGraphGlobalFilterModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    onSave: PropTypes.func.isRequired,
    globalFilter: PropTypes.object,
    newFilter: PropTypes.bool,
    overviewVars: PropTypes.array,
};

EditGraphGlobalFilterModal.defaultProps = {
    globalFilter: {},
    newFilter: false,
    overviewVars: [],
};

export default EditGraphGlobalFilterModal;
