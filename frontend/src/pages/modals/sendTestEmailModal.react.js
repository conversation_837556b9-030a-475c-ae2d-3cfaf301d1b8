import React, { useRef } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Text } from '../../components/form/text.react';

import Modal from '../../components/modal.react';
import SaveButton from '../../components/table/filter/saveButton.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Form from '../../components/form/form.react';

const SendNotificationModal = (props) => {
    const formRef = useRef(null);

    const closeModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onClose();
    };

    const confirmSending = (e) => {
        e.preventDefault();
        formRef.current.submit(); // -> handleValidSubmit()
    };

    const handleValidSubmit = (data) => {
        props.onConfirm({
            recipient: data.ttask_enot_tgt,
            subject: data.ttask_enot_subject,
        });
    };

    return (
        <Modal
            width={props.width}
            isOpen={props.isOpen}
            loading={false}
            onEnter={confirmSending}
            onEsc={closeModal}
            bindOnEnterEvent
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsConfrm" inModal>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <Form
                    ref={formRef}
                    name="addToFav"
                    className="form-container"
                    onValidSubmit={handleValidSubmit}
                    oneColumn
                >
                    <Text
                        key="ttask_enot_tgt"
                        label={`${i18next.t('email')}:`}
                        side="right"
                        value=""
                        validations={{ isEmail: true }}
                        validationErrors={{
                            isEmail: i18next.t('isEmail'),
                            isDefaultRequiredValue: i18next.t('isRequired'),
                        }}
                        required
                    />
                    <Text
                        key="ttask_enot_subject"
                        label={`${i18next.t('subject')}:`}
                        side="right"
                        value={
                            props.subject ? props.subject : i18next.t('test')
                        }
                        validationErrors={{
                            isDefaultRequiredValue: i18next.t('isRequired'),
                        }}
                        required
                    />
                </Form>
                <div className="center">
                    <SaveButton
                        title={i18next.t('send')}
                        onClick={confirmSending}
                    />
                </div>
            </div>
        </Modal>
    );
};

SendNotificationModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    onConfirm: PropTypes.func.isRequired,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    subject: PropTypes.string,
};

SendNotificationModal.defaultProps = {
    isOpen: false,
    width: 'tiny',
    subject: null,
};

export default SendNotificationModal;
