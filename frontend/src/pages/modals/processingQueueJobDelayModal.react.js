import i18next from 'i18next';
import PropTypes from 'prop-types';
import React, { useRef } from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import ApiRequest from '../../api/apiRequest';
import AlertsActions from '../../components/alerts/alerts.actions';
import { CalendarWithClockpicker } from '../../components/form/calendarWithClockpicker.react';
import Form from '../../components/form/form.react';

const ProcessingQueueJobDelayModal = (props) => {
    const formRef = useRef(null);
    const calendarRef = useRef(null);
    const closeModal = (e) => {
        e?.preventDefault();
        props.onClose();
    };

    const handleSubmit = (data) => {
        closeModal();
        const postData = {
            timestamp: data.delay,
        };

        const table = props.tableRef.current;
        const tree = props.treeRef.current;

        ApiRequest.post(
            `/queue/${props.queueName}/job/move-to-delayed/${props.jobId}`,
            JSON.stringify(postData),
        )
            .then((payload) => {
                AlertsActions.addAlert({
                    type: 'success',
                    message: i18next.t('done'),
                });
                table.handleFetchRows();
                tree.updateTree();
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailed'),
                    serverError: errorMessage,
                });
            });
    };

    const save = (e) => {
        if (e) e.preventDefault();

        let canSubmit = true;

        if (calendarRef.current.state.calendarIsOpen) {
            canSubmit = false;
        }

        if (canSubmit) {
            formRef.current.submit();
        }
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            modalOverflow
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsJobDetailModal" inModal>
                        <TabsButton
                            key="save"
                            icon="icon-floppy-disk"
                            isActive
                            onClick={save}
                            hideTitle={false}
                        >
                            {i18next.t('save')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                    <Form
                        ref={formRef}
                        name="formJobDelay"
                        className="form-container"
                        onValidSubmit={handleSubmit}
                        oneColumn
                    >
                        <CalendarWithClockpicker
                            ref={calendarRef}
                            key="delay"
                            name="delay"
                            label={`${i18next.t('date')}:`}
                        />
                    </Form>
                </div>
            </div>
        </Modal>
    );
};

ProcessingQueueJobDelayModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    queueName: PropTypes.string.isRequired,
    jobId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
    tableRef: PropTypes.objectOf(PropTypes.any).isRequired,
    treeRef: PropTypes.objectOf(PropTypes.any).isRequired,
};

ProcessingQueueJobDelayModal.defaultProps = {
    width: 'own-width-410',
};

export default ProcessingQueueJobDelayModal;
