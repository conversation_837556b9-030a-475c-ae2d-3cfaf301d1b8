import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import i18next from 'i18next';
import { momentFormatDate } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';

const MailPreviewModal = (props) => {
    const [loading, setLoading] = useState(false);
    const [emailObj, setEmailObj] = useState({});

    const loadEmailDetail = (emailId) => {
        ApiRequest.get(`/emails-queue/detail/${emailId}`)
            .then((payload) => {
                setLoading(false);
                setEmailObj(payload);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });
    };

    useEffect(() => {
        loadEmailDetail(props.emailId);
    }, []);

    const closeModal = (e) => {
        if (e) {
            e.preventDefault();
        }
        props.onClose();
    };

    const fixedHeight = document.querySelector('table').offsetHeight * 0.7;
    return (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
            modalOverflow
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsMailPreview" inModal>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <div>
                    <div className="emailFrom">
                        {`${i18next.t('from')}: ${emailObj.from}`}
                    </div>
                    <div className="emailTo">
                        {`${i18next.t('recipient')}: ${emailObj.to}`}
                    </div>
                    <div className="emailDate">
                        {`${i18next.t('sent').replace('.', '')}: ${momentFormatDate(emailObj.send_on, false, true, true)}`}
                    </div>
                    <div className="emailSubject">
                        {`${i18next.t('subject')}: ${emailObj.subject}`}
                    </div>
                    <iframe
                        title={emailObj.subject}
                        srcDoc={emailObj.body}
                        width="100%"
                        height={fixedHeight}
                    />
                </div>
            </div>
        </Modal>
    );
};

MailPreviewModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    emailId: PropTypes.number.isRequired,
};

export default MailPreviewModal;
