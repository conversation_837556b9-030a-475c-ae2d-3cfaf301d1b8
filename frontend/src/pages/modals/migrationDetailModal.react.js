import i18next from 'i18next';
import PropTypes from 'prop-types';
import { JSONTree } from 'react-json-tree';
import React from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';

const MigrationDetailModal = (props) => {
    const closeModal = (e) => {
        e.preventDefault();
        props.onClose();
    };

    const data = { ...props.migrationData };

    try {
        const migStepUpResult = JSON.parse(data.mig_step_up_result);
        delete data.mig_step_up_result;
        data.mig_step_up_result = migStepUpResult;
    } catch (e) {
        //
    }

    try {
        const migStepDownResult = JSON.parse(data.mig_step_down_result);
        delete data.mig_step_down_result;
        data.mig_step_down_result = migStepDownResult;
    } catch (e) {
        //
    }

    return (
        <Modal
            isOpen={props.isOpen}
            loading={false}
            width={props.width}
            onEsc={closeModal}
            modalOverflow
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsMigDetailModal" inModal>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                    <div className="log-wrapper">
                        <div className="message">
                            <JSONTree data={data} hideRoot />
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
};

MigrationDetailModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    migrationData: PropTypes.objectOf(PropTypes.any),
};

MigrationDetailModal.defaultProps = {
    width: 'large',
    migrationData: {},
};

export default MigrationDetailModal;
