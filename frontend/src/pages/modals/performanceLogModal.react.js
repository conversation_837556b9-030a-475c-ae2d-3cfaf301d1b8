import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Chart, registerables } from 'chart.js';
import ChartDataLabels from '../../../assets/libs/chartjs-plugin-datalabels/chartjs-plugin-datalabels.esm';
import { TextPure } from '../../components/form/text.react';
import ApiRequest from '../../api/apiRequest';
import React from 'react';
import _ from 'lodash';
import cx from 'classnames';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import LoggedUserStore from '../../flux/loggedUser.store';

class PerformanceLogModal extends React.Component {
    constructor(props) {
        super();

        this.state = {
            loading: true,
            average: 0,
            canvasKey: 0,
            selectedLimit: 100,
        };

        this.closeModal = this.closeModal.bind(this);
        this.loadLogsData = this.loadLogsData.bind(this);
        this.logsWithLimit = this.logsWithLimit.bind(this);
    }

    componentDidMount() {
        let { log } = this.props;

        if (!log) {
            const tableItems =
                this.props.parent.refs[this.props.tableRef].state.items;
            log = _.find(tableItems, ['id', this.props.logId]);
        }

        this.log = log;
        this.loadLogsData(log);
        Chart.register(...registerables);
    }

    componentWillUnmount() {
        if (this.loadGraphRequest) {
            this.loadGraphRequest.cancel();
        }
        if (this.reportChart) {
            this.reportChart.destroy(); // destroy any instance
        }
    }

    getChartTitle(category, log) {
        switch (category) {
            case 1:
                return `${i18next.t('caseActivation')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 2:
                return `${i18next.t('displayingOverview')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 3:
                return `${i18next.t('taskSolve')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 4:
                return `${i18next.t('displayingCO')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 5:
                return `${i18next.t('printCreation')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 10001:
                return `${i18next.t('copyTask')} (${i18next.t('multiInstance')} || '')`;
            case 20001:
                return `${i18next.t('calculation')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 20002:
                return `${i18next.t('taskEmailNotification')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 20003:
                return `${i18next.t('linkConditions')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 20004:
                return `${i18next.t('checkProcessCompletion')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 20005:
                return `${i18next.t('findingSolver')} - ${i18next.t('template')}: ${log.tproc_name || ''}`;
            case 7:
                return i18next.t('statistics');
            case 8:
                return i18next.t('processArchivation');
            case 9:
                return i18next.t('processUnarchivation');
            default:
                return '';
        }
    }

    loadLogsData(log, pLimit = 100) {
        if (this.loadGraphRequest) {
            this.loadGraphRequest.cancel();
        }

        let limit = config.restLimit;
        let sort = 'asc';

        if (pLimit) {
            limit = pLimit;
            sort = 'desc';
        }

        this.setState({
            loading: true,
            selectedLimit: limit,
        });

        if (log) {
            this.loadGraphRequest = ApiRequest.post(
                `/performance-logs-graph?order=perf_date&sort=${sort}&limit=${limit}`,
                JSON.stringify(log),
            )
                .then((payload) => {
                    this.setState({
                        loading: false,
                        chartTitle: this.getChartTitle(log.category, log),
                        average: Math.round(
                            _.sumBy(payload.items, 'duration') /
                                payload.items.length,
                        ),
                    });

                    const localization =
                        config.dateTimeFormat || LoggedUserStore.getState().userLocale;
                    const intOpts = {
                        day: 'numeric',
                        month: 'numeric',
                        year: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric',
                        second: 'numeric',
                    };
                    const durationsArr = [];
                    const datesArr = [];
                    let { items } = payload;

                    if (sort === 'desc') {
                        items = items.reverse();
                    }

                    items.forEach((item) => {
                        durationsArr.push(item.duration);
                        const formatDate = Intl.DateTimeFormat(localization, intOpts).format(
                            Date.parse(item.perf_date),
                        );
                        datesArr.push(formatDate);
                    });

                    this.createChart(durationsArr, datesArr, payload.graph_prefs);
                })
                .catch((errorMessage) => {
                    this.setState({ loading: false });

                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrLogsLoadFailed'),
                        serverError: errorMessage,
                    });
                });
        }
    }

    createChart(durationsArr, datesArr, graphPrefs) {
        const { darkModeIsEnabled } = LoggedUserStore.getState();
        const stepY = graphPrefs.step;
        const maxY = graphPrefs.max;
        // lodash 4 update - "Made _.max & _.min return undefined when passed an empty array" (instead of Infinity)
        const maxDuration = durationsArr.length > 0 ? _.max(durationsArr) : Infinity;
        const maxDurationRounded = Math.ceil(maxDuration / stepY) * stepY; // round up to stepY
        const ctx = this.logChartCanvasRef.getContext('2d');

        const pointBackgroundColor = darkModeIsEnabled
            ? 'rgba(159, 232, 31, 1)'
            : 'rgba(118, 183, 3, 1)';
        const backgroundColor = darkModeIsEnabled
            ? 'rgba(159, 232, 31, 0.1)'
            : 'rgba(118, 183, 3, 0.1)';
        const borderColor = darkModeIsEnabled
            ? 'rgba(159, 232, 31, 0.5)'
            : 'rgba(118, 183, 3, 0.5)';

        const data = {
            labels: datesArr,
            datasets: [
                {
                    label: null,
                    lineTension: 0.2,
                    backgroundColor: backgroundColor,
                    borderColor: borderColor,
                    pointBackgroundColor: pointBackgroundColor,
                    data: durationsArr,
                },
            ],
        };
        const options = {
            responsive: true,
            pointDotRadius: 20,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: i18next.t('date'),
                    },
                    ticks: {
                        display: false, // hide dates
                    },
                },
                y: {
                    title: {
                        display: true,
                        text: i18next.t('durationInMs'),
                    },
                    beginAtZero: true,
                    min: 0,
                    max: maxDuration > maxY ? maxDurationRounded : maxY,
                    ticks: {
                        stepSize: stepY,
                    },
                },
            },
            plugins: {
                tooltip: {
                    enabled: true,
                    mode: 'nearest',
                    callbacks: {
                        label: (tlItems) => {
                            return `${tlItems.yLabel} ms`;
                        },
                    },
                },
                legend: { display: false },
                datalabels: {
                    align: 'end',
                    anchor: 'end',
                    display: 'auto',
                },
            },
        };

        this.reportChart = new Chart(ctx, {
            type: 'line',
            options: options,
            data: data,
            plugins: [ChartDataLabels],
        });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    logsWithLimit(limit) {
        this.setState({ canvasKey: (this.state.canvasKey += 1) });
        this.loadLogsData(this.log, limit);
    }

    render() {
        const { selectedLimit } = this.state;

        return (
            <Modal
                isOpen={this.props.isOpen}
                loading={this.state.loading}
                width={this.props.width}
                onEsc={this.closeModal}
                modalOverflow
            >
                <div className="modal">
                    <div className="row">
                        <TabsButtonsOther key="buttonsMaintenance" inModal>
                            <TabsButton
                                key="close"
                                icon="icon-delete-1"
                                isActive
                                onClick={this.closeModal}
                                hideTitle={false}
                            >
                                {i18next.t('close')}
                            </TabsButton>
                        </TabsButtonsOther>
                        <div className="performance-log-chart-wrap">
                            <div className="info">
                                <div className="title">{this.state.chartTitle}</div>
                                <div className="limit-buttons">
                                    <span
                                        className={cx({
                                            selected: selectedLimit === config.restLimit,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, null)}
                                    >
                                        {i18next.t('all')}
                                    </span>
                                    <span
                                        className={cx({
                                            selected: selectedLimit === 1000,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, 1000)}
                                    >
                                        1000
                                    </span>
                                    <span
                                        className={cx({
                                            selected: selectedLimit === 500,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, 500)}
                                    >
                                        500
                                    </span>
                                    <span
                                        className={cx({
                                            selected: selectedLimit === 200,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, 200)}
                                    >
                                        200
                                    </span>
                                    <span
                                        className={cx({
                                            selected: selectedLimit === 100,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, 100)}
                                    >
                                        100
                                    </span>
                                    <span
                                        className={cx({
                                            selected: selectedLimit === 50,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, 50)}
                                    >
                                        50
                                    </span>
                                    <span
                                        className={cx({
                                            selected: selectedLimit === 10,
                                        })}
                                        onClick={this.logsWithLimit.bind(null, 10)}
                                    >
                                        10
                                    </span>
                                </div>
                                <div className="limit-input">
                                    <TextPure
                                        name="ownLimit"
                                        meta={{ onlyNumbers: true }}
                                        onEnter={this.logsWithLimit}
                                    />
                                </div>
                                <div className="avg">{`${i18next.t('average')}: ${this.state.average} ms`}</div>
                            </div>
                            <canvas
                                key={`logCanvas-${this.state.canvasKey}`}
                                ref={(r) => {
                                    this.logChartCanvasRef = r;
                                }}
                            />
                        </div>
                    </div>
                </div>
            </Modal>
        );
    }
}

PerformanceLogModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    logId: PropTypes.number,
    log: PropTypes.objectOf(PropTypes.any),
    parent: PropTypes.objectOf(PropTypes.any).isRequired,
    tableRef: PropTypes.string.isRequired,
};

PerformanceLogModal.defaultProps = {
    logId: null,
    log: null,
};

export default PerformanceLogModal;
