import i18next from 'i18next';
import PropTypes from 'prop-types';
import {
    <PERSON>, Dialog, DialogContent, DialogTitle, Stack, Typography
} from '@mui/material';
import {
    guid,
    checkLangMutation,
    checkVarLovLangMutation,
    RemCalculator, momentFormatDate, isISODateString,
} from '../../common/utils';
import { Text } from '../../components/form/text.react';
import { TextNum } from '../../components/form/textNum.react';
import { TextArea } from '../../components/form/textArea.react';
import { SelectBox } from '../../components/form/selectBox.react';
import { Calendar } from '../../components/form/calendar.react';
import { CheckboxList } from '../../components/form/checkboxList.react';
import { Checkbox } from '../../components/form/checkbox.react';
import MyIcon from '../../components5.0/MyIcon';
import Button from '../../components5.0/Button';
import ApiRequest from '../../api/apiRequest';
import Preview from '../../components5.0/preview/Preview';
import DataGridTanstack from '../../components5.0/data-grid/data-grid-tanstack/DataGridTanstack';
// zustand
import { useStore } from '../../components5.0/zustand/boundStore';
import React from 'react';
import _ from 'lodash';
import AlertsActions from '../../components/alerts/alerts.actions';
// form component
import Form from '../../components/form/form.react';
import Label from '../../components/form/label.react';
import MultiBox from '../../components/form/multiBox.react.js';
import EmptyComponent from '../../components/form/emptyComponent.react';
import CKEditor from '../../components/form/ckEditor.react';

class CaseEditVariableModal extends React.Component {

    constructor(props) {
        super();
        this.state = {
            variable: {},
            caseVariables: [],
            loading: true,
            variableName: null,
            variableValue: null,
            saveButtonIsVisible: true,
            multiBoxesArr: [],
            multiBoxesLoaded: false,
            variableType: null,
            displayedFile: null,
            drColumnsForTable: [],
            drRowsForTable: [],
        };

        this.dataGridUpdateRows = useStore.getState().updateRows;

        this.loadModalData = this.loadModalData.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.saveVariable = this.saveVariable.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.pushMultiBox = this.pushMultiBox.bind(this);
        this.changePreview = this.changePreview.bind(this);
        this.setListHeight = this.setListHeight.bind(this);
        this.findTaskVarValue = this.findTaskVarValue.bind(this);
        this.fnFindTaskVarValue = this.fnFindTaskVarValue.bind(this);
    }

    UNSAFE_componentWillMount() {
        this._selectBoxes = [];
        this._multiBoxes = [];
        this._calendars = [];
    }

    componentDidMount() {
        this.loadModalData();
    }

    converDrData(data) {
        const rows = [];
        const keys = Object.keys(data);
        const { length } = data[keys[0]];

        const columns = keys.map(key => ({
            field: key,
            headerName: key,
            type: 'text',
        }));

        for (let i = 0; i < length; i += 1) {
            const obj = {};
            keys.forEach(key => {
                let value = data[key][i];

                if (value === null) {
                    value = '';
                } else if (typeof value === 'boolean') {
                    value = value ? i18next.t('yes') : i18next.t('no');
                } else if (typeof value === 'string') {
                    if (isISODateString(value)) {
                        value = momentFormatDate(value);
                    }
                } else if (_.has(value, 'title')) {
                    value = value.title;
                } else if (Array.isArray(value)) {
                    value = value.map(val => {
                        if (_.has(val, 'fileName')) {
                            return val.fileName;
                        }
                        return val;
                    }).join(', ');
                }

                obj[key] = value;
            });
            rows.push(obj);
        }

        this.setState({
            drColumnsForTable: columns,
            drRowsForTable: rows,
        });
    }

    loadModalData() {
        ApiRequest.get(`/variables/${this.props.variableId}`)
            .then((payload) => {
                if (payload.ivar_type === 'DR') {
                    this.converDrData(payload.ivar_value);
                }

                this.setState({
                    variable: payload,
                    variableType: payload.ivar_type,
                    loading: false,
                    saveButtonIsVisible: payload.ivar_type != 'DR',
                });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedVarData'),
                    serverError: errorMessage,
                });
            });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    saveVariable(e) {
        if (e) e.preventDefault();
        let canSubmit = true;

        this._selectBoxes.forEach((selectbox) => {
            if (selectbox && selectbox.state.open) {
                canSubmit = false;
            }
        });

        this._calendars.forEach((calendar) => {
            if (calendar && calendar.state.calendarIsOpen) {
                canSubmit = false;
            }
        });

        if (this.multiBoxRef) {
            const multiBoxRef = this.multiBoxRef.refs.selectFrom;

            if (multiBoxRef && multiBoxRef.state && multiBoxRef.state.open) {
                // multiBox with selectBox
                canSubmit = false;
            } else if (document.activeElement.tagName === 'SELECT') {
                canSubmit = false;
            }
        }

        // prevent submit if textArea is focused
        if (document.activeElement.tagName == 'TEXTAREA') {
            canSubmit = false;
        }

        if (canSubmit) {
            this.refs.formEditVariable.submit();
        }
    }

    // form submit
    handleSubmit(data) {
        this.closeModal();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrVarSaving'),
        });

        const { variableId } = this.props;
        const value =
            data.variable && data.variable.hasOwnProperty('value')
                ? data.variable.value
                : data.variable;
        const { variable } = this.state;
        const object = {
            ivar_id: variableId,
            ivar_value: value,
        };

        // Number with decimals "10.00" -> 10
        if (variable && variable.ivar_type === 'N' && value !== null) {
            object.ivar_value = Number(object.ivar_value);
        }

        // const table = this.props.parent.refs.caseVariables;

        ApiRequest.post(
            `/processes/${this.props.caseId}/variables`,
            JSON.stringify([object]),
        )
            .then((answer) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrVarSaved'),
                });

                this.dataGridUpdateRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrVarSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    pushMultiBox(multiBox) {
        const multiBoxesClone = _.cloneDeep(this.state.multiBoxesArr);
        const findMultiBox = _.find(multiBoxesClone, ['name', multiBox.name]);

        if (!findMultiBox) {
            multiBoxesClone.push(multiBox);
        } else {
            findMultiBox.checked = multiBox.checked;
        }

        this.setState({ multiBoxesArr: multiBoxesClone }, () => {
            this.setState({
                multiBoxesLoaded: !_.find(multiBoxesClone, ['checked', false]),
            });
        });
    }

    changePreview(variable, file) {
        this.setState({
            displayedFile: file,
        });
    }

    setListHeight(height, tvarName) {
        this[tvarName].setHeight(height);
    }

    findTaskVarValue(varNames) {
        return new Promise((resolve, reject) => {
            const valuesArr = [];
            let varNamesArr = varNames;

            if (!Array.isArray(varNames)) {
                varNamesArr = [varNames];
            }

            Promise.all(varNamesArr.map((varName) => {
                return this.fnFindTaskVarValue(varName).then((varValue) => {
                    return valuesArr.push(varValue);
                });
            }))
                .then(() => {
                    resolve(valuesArr);
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    fnFindTaskVarValue(refName) {
        return new Promise((resolve, reject) => {
            if (_.isEmpty(this.state.caseVariables)) {
                ApiRequest.get(
                    `/processes/${this.props.caseId}/variables?limit=${config.restLimit}`,
                )
                    .then((vars) => {
                        this.setState({ caseVariables: vars.items });
                        const findVar = _.find(
                            vars.items,
                            ['tvar_name', refName],
                        );

                        if (findVar) {
                            return resolve(findVar.ivar_value);
                        }

                        return resolve();
                    })
                    .catch((errorMessage) => {
                        AlertsActions.addAlert({
                            type: 'alert',
                            message: i18next.t('alrCaseVarsLoadFailed'),
                            serverError: errorMessage,
                        });
                    });
            } else {
                const findVar = _.find(
                    this.state.caseVariables,
                    ['tvar_name', refName],
                );

                if (findVar) {
                    return resolve(findVar.ivar_value);
                }

                return resolve();
            }
        });
    }

    render() {
        const rCalc = new RemCalculator();
        const components = [];
        let showDrTable = false;
        let drName = '';

        // create component according to variables
        const createComponent = (i) => {
            const meta = JSON.parse(i.tvar_meta || '{}');

            if (i.ivar_multi === 'X') {
                // MultiBox
                let multiBoxType = i.ivar_type;
                if (i.ivar_attribute !== null) {
                    multiBoxType += i.ivar_attribute;
                }

                return (
                    <MultiBox
                        ref={(c) => {
                            this.multiBoxRef = c;
                        }}
                        pushMultiBox={this.pushMultiBox}
                        key="variable"
                        label={checkLangMutation(i, 'ivar_name')}
                        value={
                            i.ivar_type === 'DT'
                                ? i.ivar_dt_index
                                : i.ivar_value
                        }
                        multiBoxType={multiBoxType}
                        options={checkVarLovLangMutation(i)}
                        helperText={checkLangMutation(i, 'tvar_tooltip')}
                        dlName={i.dlist_name}
                        meta={meta}
                        dynTableId={i.dt_id}
                        colIndex={i.ivar_col_index}
                        findTaskVarValue={this.findTaskVarValue}
                    />
                );
            } // other component
            switch (i.ivar_type) {
                case 'T':
                    if (i.ivar_attribute === 'M') {
                        if (meta.ckEditor) {
                            return (
                                <CKEditor
                                    key="variable"
                                    label={checkLangMutation(i, 'ivar_name')}
                                    value={i.ivar_value || ''}
                                    helperText={checkLangMutation(
                                        i,
                                        'tvar_tooltip',
                                    )}
                                    advancedEditor={meta.ckEditorAdvanced}
                                />
                            );
                        }
                        return (
                            <TextArea
                                key="variable"
                                label={checkLangMutation(i, 'ivar_name')}
                                value={i.ivar_value}
                                rows={5}
                                helperText={checkLangMutation(
                                    i,
                                    'tvar_tooltip',
                                )}
                            />
                        );
                    }
                    if (i.ivar_attribute === 'F') {
                        return (
                            <CheckboxList
                                key="variable"
                                label={checkLangMutation(i, 'ivar_name')}
                                value={i.ivar_value}
                                caseId={this.props.caseId}
                                helperText={checkLangMutation(
                                    i,
                                    'tvar_tooltip',
                                )}
                                dropzone
                                parent={this.props.parent}
                                ref={(c) => {
                                    this[i.tvar_name] = c;
                                }}
                                withPreview={meta.isPreview}
                                upperLabel={meta.isPreview}
                                changePreview={this.changePreview.bind(null, i)}
                                dropdown={meta.isPreview}
                            >
                                {meta.isPreview && (
                                    <Preview
                                        tvarName={i.tvar_name}
                                        setListHeight={this.setListHeight}
                                        fixedHeight={`${rCalc.remCalc(window.innerHeight - rCalc.pxScale(410))}rem`}
                                        file={this.state.displayedFile}
                                    />
                                )}
                            </CheckboxList>
                        );
                    }
                    return (
                        <Text
                            key="variable"
                            label={checkLangMutation(i, 'ivar_name')}
                            value={i.ivar_value}
                            helperText={checkLangMutation(i, 'tvar_tooltip')}
                            meta={meta}
                            findTaskVarValue={this.findTaskVarValue}
                        />
                    );

                case 'D':
                    /* var useOnlyFutureDates = meta.useOnlyFutureDates;
                        var startDate = useOnlyFutureDates ? new Date(moment(new Date()).startOf('day')) : null; */

                    return (
                        <Calendar
                            key="variable"
                            ref={(c) => this._calendars.push(c)}
                            label={checkLangMutation(i, 'ivar_name')}
                            value={i.ivar_value}
                            helperText={checkLangMutation(
                                i,
                                'tvar_tooltip',
                            )} /* startDate={startDate} */
                            dateWithoutTime={i.tvar_date_without_time === 'Y'}
                        />
                    );
                case 'N':
                    if (meta.isCheckbox) {
                        return (
                            <Checkbox
                                key="variable"
                                label={checkLangMutation(i, 'ivar_name')}
                                value={!!i.ivar_value}
                                helperText={checkLangMutation(
                                    i,
                                    'tvar_tooltip',
                                )}
                            />
                        );
                    }
                    return (
                        <TextNum
                            key="variable"
                            label={checkLangMutation(i, 'ivar_name')}
                            value={i.ivar_value}
                            validations={{ isNumeric: true }}
                            validationErrors={{
                                isNumeric: i18next.t('notNumber'),
                            }}
                            sequence={i.ivar_attribute}
                            helperText={checkLangMutation(i, 'tvar_tooltip')}
                            numberOfDecimals={meta.numberOfDecimals}
                        />
                    );
                case 'DL':
                    return (
                        <SelectBox
                            ref={(c) => this._selectBoxes.push(c)}
                            key="variable"
                            label={checkLangMutation(i, 'ivar_name')}
                            defaultValue={i.ivar_lov_value}
                            value={i.ivar_value}
                            selectBoxType={i.ivar_type + i.ivar_attribute}
                            dlName={i.dlist_name}
                            helperText={checkLangMutation(i, 'tvar_tooltip')}
                            findTaskVarValue={this.findTaskVarValue}
                        />
                    );
                // Dynamic table
                case 'DT':
                    return (
                        <SelectBox
                            ref={(c) => this._selectBoxes.push(c)}
                            id={i.id}
                            colIndex={i.ivar_col_index}
                            key="variable"
                            label={checkLangMutation(i, 'ivar_name')}
                            meta={meta}
                            dynTableId={i.dt_id}
                            value={i.ivar_dt_index}
                            selectBoxType={i.ivar_type}
                            helperText={checkLangMutation(i, 'tvar_tooltip')}
                            findTaskVarValue={this.findTaskVarValue}
                        />
                    );
                // Dynamic rows
                case 'DR':
                    drName = checkLangMutation(i, 'ivar_name');
                    showDrTable = true;
                    return null;
                default: // LT, LN, LD
                    return (
                        <SelectBox
                            ref={(c) => this._selectBoxes.push(c)}
                            key="variable"
                            label={checkLangMutation(i, 'ivar_name')}
                            value={i.ivar_value}
                            selectBoxType={i.ivar_type}
                            options={checkVarLovLangMutation(i)}
                            helperText={checkLangMutation(i, 'tvar_tooltip')}
                        />
                    );
            }
        };

        // create component after data was load
        if (!_.isEmpty(this.state.variable)) {
            components.push(createComponent(this.state.variable));
        }

        const saveButtonIsEnabled = !_.isEmpty(this.state.multiBoxesArr)
            ? !this.state.loading && this.state.multiBoxesLoaded
            : !this.state.loading;

        return (
            <Dialog
                fullWidth
                maxWidth={this.state.variableType === 'DR' ? 'xl' : 'md'}
                open={this.props.isOpen}
                onClose={this.closeModal}
                // isOpen={this.props.isOpen}
                // loading={this.state.loading}
                // width={this.props.width}
                // onEsc={this.closeModal}
                // onEnter={this.saveVariable}
            >
                <DialogTitle
                    display="flex"
                    justifyContent="flex-end"
                    textAlign="center"
                >
                    <Stack
                        direction="row"
                        display="flex"
                        justifyContent="flex-end"
                        textAlign="center"
                        spacing={1}
                    >
                        {this.state.saveButtonIsVisible && (
                            <Button
                                color="primary"
                                startIcon={<MyIcon icon="save" />}
                                label={i18next.t('save')}
                                onClick={this.saveVariable}
                                variant="contained"
                                size="small"
                                disabled={!saveButtonIsEnabled}
                            />
                        )}
                        <Button
                            color="secondary"
                            startIcon={<MyIcon icon="close" />}
                            label={i18next.t('close')}
                            onClick={this.closeModal}
                            variant="text"
                            size="small"
                        />
                    </Stack>
                </DialogTitle>
                <DialogContent>
                    {!this.state.loading && showDrTable && (
                        <Box px="0.9375rem">
                            <Typography
                                variant="body1"
                                color="text.secondary"
                            >
                                {drName}
                            </Typography>
                            <Box height="70vh">
                                <DataGridTanstack
                                    id="dr-table"
                                    row={this.state.drRowsForTable}
                                    columns={this.state.drColumnsForTable}
                                    enableColumnPinning={false}
                                />
                            </Box>
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                mt="0.625rem"
                            >
                                {i18next.t('noteScriptsNotApplied')}
                            </Typography>
                        </Box>
                    )}
                    {!this.state.loading && !showDrTable && (
                        <Form
                            ref="formEditVariable"
                            name="formEditVariable"
                            className="form-container"
                            onValidSubmit={this.handleSubmit}
                            oneColumn
                        >
                            {components}
                            <EmptyComponent key="EmptyComponent" />
                            <Label
                                key="editVariableLabel"
                                value={i18next.t('varChange')}
                            />
                        </Form>
                    )}
                </DialogContent>
            </Dialog>
        );
    }

}

CaseEditVariableModal.propTypes = {
    variableId: PropTypes.number,
    caseId: PropTypes.string,
    onClose: PropTypes.func,
    isOpen: PropTypes.bool,
    width: PropTypes.string,
    parent: PropTypes.object.isRequired,
};

export default CaseEditVariableModal;
