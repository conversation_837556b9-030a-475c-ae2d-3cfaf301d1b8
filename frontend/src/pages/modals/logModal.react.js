import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Dialog } from '@mui/material';
import { guid } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';

import React from 'react';
import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Log from '../logs/log.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import { saveAs } from '../../../assets/libs/filesaver';

class LogModal extends React.Component {

    constructor(props) {
        super();

        this.state = {
            loading: true,
            log: {},
            logTooBig: false,
        };

        this.closeModal = this.closeModal.bind(this);
        this.loadLogData = this.loadLogData.bind(this);
        this.download = this.download.bind(this);
    }

    componentDidMount() {
        if (this.props.log === null) {
            this.loadLogData(this.props.logId);
        } else {
            // archived log, calculation log
            this.setState({ loading: false });
        }
    }

    loadLogData(logId) {
        const url = `/logs/detail/${logId}`;

        ApiRequest.get(url)
            .then((payload) => {
                if (payload === 'LOG_TOO_BIG_TO_LOAD') {
                    this.setState({
                        logTooBig: true,
                        loading: false,
                    });
                } else {
                    this.setState({
                        log: payload.items,
                        loading: false,
                    });
                }
            })
            .catch((errorMessage) => {
                this.setState({ loading: false });

                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrLogsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeModal(e) {
        if (e) e.preventDefault();
        this.props.onClose();
    }

    download() {
        const { logId, logTime } = this.props;
        const url = `/logs/download/${logId}`;

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrAttachDownloading'),
        });

        ApiRequest.getFile(url)
            .then((payload) => {
                saveAs(payload, `log_${logTime}.json`); // payload is Blob

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachDownloaded'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrLogsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    render() {
        const log = this.props.log || this.state.log || {};

        return (
            <Dialog
                fullWidth
                maxWidth="lg"
                open={this.props.isOpen}
                onClose={this.closeModal}
                // isOpen={this.props.isOpen}
                // loading={this.state.loading}
                // width={this.props.width}
                // onEsc={this.closeModal}
                // onEnter={this.saveVariable}
            >
                <div className="old-modal">
                    <div className="modal">
                        <div className="row">
                            <TabsButtonsOther key="buttonsMaintenance" inModal>
                                <TabsButton
                                    key="download"
                                    icon="icon-file-download"
                                    isActive
                                    onClick={this.download}
                                    hideTitle={false}
                                >
                                    {i18next.t('downloadLog')}
                                </TabsButton>
                                <TabsButton
                                    key="close"
                                    icon="icon-delete-1"
                                    isActive
                                    onClick={this.closeModal}
                                    hideTitle={false}
                                >
                                    {i18next.t('close')}
                                </TabsButton>
                            </TabsButtonsOther>
                            {this.state.logTooBig && (
                                <div className="log-wrapper">
                                    {i18next.t('logTooBig')}
                                </div>
                            )}
                            {!this.state.logTooBig && (
                                <Log
                                    key={`log${log.id}`}
                                    id={log.id}
                                    log={log}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </Dialog>
        );
    }

}

LogModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    logId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    log: PropTypes.objectOf(PropTypes.any), // archived log
};

LogModal.defaultProps = {
    log: null,
};

export default LogModal;
