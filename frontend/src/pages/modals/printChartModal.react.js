import React, { useState, useEffect, useRef } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Checkbox } from '../../components/form/checkbox.react';
import { SelectBox } from '../../components/form/selectBox.react';
import { Text } from '../../components/form/text.react';
import { guid } from '../../common/utils';
import { checkRevision } from '../uploadDocumentUtil';
import ApiRequest from '../../api/apiRequest';
import _ from 'lodash';
import Modal from '../../components/modal.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Form from '../../components/form/form.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import LoggedUserStore from '../../flux/loggedUser.store';

const PrintChartModal = (props) => {
    const formRef = useRef(null);

    const [loading, setLoading] = useState(true);
    const [cases, setCases] = useState([]);
    const [checked, setChecked] = useState(false);

    useEffect(() => {
        // eslint-disable-next-line
        ApiRequest.get(`/cv/${props.cvId}?limit=${config.restLimit}`) // /cv/301?total_count=false
            .then((payload) => {
                setLoading(false);
                setCases(payload.items);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedOverviewData'),
                    serverError: errorMessage,
                });
            });
    }, []);

    const closeModal = (event) => {
        if (event) {
            event.preventDefault();
        }

        props.onClose();
    };

    const printModal = (event) => {
        if (event) {
            event.preventDefault();
        }

        formRef.current.submit(); // -> handleValidSubmit() / handleInvalidSubmit()
    };

    const postMetadata = (metadataArr, alertId, fileId, additionalMessage) => {
        ApiRequest.post(
            `/dms/file/${fileId}/metadata`,
            JSON.stringify(metadataArr),
        )
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachSaved'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrAttachMetaFailed'),
                    serverError: errorMessage,
                });
            });
    };

    const postFile = (formData, file, iprocId) => {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            hasSpinner: true,
            type: 'info',
            message: i18next.t('alrDocumentAdding'),
        });

        ApiRequest.postFile('/dms/upload', formData, file, iprocId)
            .then((payload) => {
                const fileId = payload.id;
                const folder = 'current'; // do not rewrite revision's folder

                checkRevision(
                    fileId,
                    alertId,
                    null,
                    folder,
                    payload.message,
                ).then((metadataArr) => {
                    postMetadata(metadataArr, alertId, fileId, payload.message);
                });
            })
            .catch((errorMessage) => {
                if (_.get(errorMessage, 'error.codeName') === 'FILE_TOO_BIG') {
                    const maxUploadSize =
                        LoggedUserStore.getConfig('dms.maxUploadSize') /
                        1024 /
                        1024;

                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachTooBig', {
                            maxUploadSize: maxUploadSize,
                        }),
                    });
                } else if (
                    _.get(errorMessage, 'error.codeName') ===
                    'VICE_VIEW_ONLY_ERROR'
                ) {
                    AlertsActions.removeButtonAlert(alertId);
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('alrNoPermsToAddDocInVice'),
                        show: true,
                        allowCountdown: true,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachSaveFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    };

    const handleValidSubmit = (data) => {
        const canvas = document.querySelector(`#graph-${props.graphId}`);
        const dataUrl = canvas.toDataURL();

        const windowContent = `
            <!DOCTYPE html>
            <html>
                <head>
                    <title>${props.reportName}</title>
                </head>
                <body>
                    <img src="${dataUrl}">
                </body>
            </html>
        `;
        const printWin = window.open();
        printWin.document.open();
        printWin.document.write(windowContent);
        printWin.document.close();
        printWin.focus();
        printWin.print();

        // Attach to selected case
        if (data.saveAsAttach) {
            canvas.toBlob((blob) => {
                const iprocId = data.process.value;
                const formData = new FormData();

                // text parameters must be first! http://sailsjs.com/documentation/reference/request-req/req-file
                formData.append('filename', `${data.filename}.png`);
                formData.append('iprocId', iprocId);
                // the file itself must be the last appended param
                formData.append('file', blob);

                postFile(formData, blob, iprocId);
                closeModal();
            });
        }
    };

    const handleInvalidSubmit = (data) => {
        AlertsActions.addAlert({
            type: 'warning',
            message: i18next.t('alrFillDataInRightFormat'),
            show: true,
            allowCountdown: true,
        });
    };

    const handleCheck = (name, value) => {
        setChecked(value);
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
            onEnter={printModal}
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsPrintChart" inModal>
                        <TabsButton
                            key="print"
                            icon="icon-printer"
                            enableOn={!loading}
                            onClick={printModal}
                            hideTitle={false}
                        >
                            {i18next.t('print')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <Form
                    ref={formRef}
                    name="formPrintChartModal"
                    className="form-container"
                    onValidSubmit={handleValidSubmit}
                    onInvalidSubmit={handleInvalidSubmit}
                    oneColumn
                >
                    <Checkbox
                        key="saveAsAttach"
                        label={`${i18next.t('saveAsAttachment')}:`}
                        text=""
                        onChange={handleCheck}
                    />
                    {checked && (
                        <Text
                            key="filename"
                            name="filename"
                            label={`${i18next.t('fileName')}:`}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {checked && (
                        <SelectBox
                            key="process"
                            name="process"
                            label={`${i18next.t('cases')}:`}
                            // value={} // selected case
                            options={cases}
                            nullable={false} // no empty choise allowed
                            dataStructureRenderer={(option) => {
                                return {
                                    value: option.id,
                                    title: `${option.iproc_name} (${option.iproc_inst_owner_user} ${option.iproc_actual_start_date})`,
                                };
                            }}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                </Form>
            </div>
        </Modal>
    );
};

PrintChartModal.propTypes = {
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string.isRequired,
    reportName: PropTypes.string.isRequired,
    graphId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    cvId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default PrintChartModal;
