import React, { useRef, useState, useEffect } from 'react';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import { guid } from '../../common/utils';
import ApiRequest from '../../api/apiRequest';

import Modal from '../../components/modal.react';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import Form from '../../components/form/form.react';
import RadioButton from '../../components/form/radioButton.react';
import AlertsActions from '../../components/alerts/alerts.actions';

const EmailsQueueStatusModal = (props) => {
    const formStatus = useRef(null);
    const [loading, setLoading] = useState(false);
    const [maquStatus, setMaquStatus] = useState(null);

    const statusOptions = [
        {
            value: 'S',
            title: i18next.t('sent').replace('.', ''),
            inactive: true,
        },
        { value: 'N', title: i18next.t('notSent') },
        { value: 'A', title: i18next.t('waitsForSending') },
        { value: 'F', title: i18next.t('sendingFailed'), inactive: true },
    ];

    const loadEmailsQueueData = (emailId) => {
        ApiRequest.get(`/emails-queue/${emailId}`)
            .then((payload) => {
                setMaquStatus(payload.maqu_status);
                setLoading(false);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });
    };

    useEffect(() => {
        if (props.emailId) {
            loadEmailsQueueData(props.emailId);
        }
    }, []);

    const closeModal = (e) => {
        if (e) e.preventDefault();
        props.onClose();
    };

    const saveStatus = (e) => {
        if (e) e.preventDefault();
        formStatus.current.submit();
    };

    const handleSubmit = (data) => {
        props.onSave(data);
    };

    const handleChange = (name, value) => {
        setMaquStatus(value);
    };

    return (
        <Modal
            isOpen={props.isOpen}
            loading={loading}
            width={props.width}
            onEsc={closeModal}
            onEnter={maquStatus !== null && saveStatus}
        >
            <div className="modal">
                <div className="row">
                    <TabsButtonsOther key="buttonsEmailQueueStatus" inModal>
                        <TabsButton
                            key="save"
                            icon="icon-floppy-disk"
                            enableOn={!loading && maquStatus !== null}
                            onClick={saveStatus}
                            hideTitle={false}
                        >
                            {i18next.t('save')}
                        </TabsButton>
                        <TabsButton
                            key="close"
                            icon="icon-delete-1"
                            isActive
                            onClick={closeModal}
                            hideTitle={false}
                        >
                            {i18next.t('close')}
                        </TabsButton>
                    </TabsButtonsOther>
                </div>
                <Form
                    ref={formStatus}
                    name="formStatus"
                    className="form-container"
                    onValidSubmit={handleSubmit}
                    oneColumn
                >
                    <RadioButton
                        key="status"
                        label={`${i18next.t('status')}:`}
                        value={maquStatus}
                        options={statusOptions}
                        onChange={handleChange}
                    />
                </Form>
            </div>
        </Modal>
    );
};

EmailsQueueStatusModal.propTypes = {
    emailId: PropTypes.number,
    onClose: PropTypes.func.isRequired,
    isOpen: PropTypes.bool.isRequired,
    width: PropTypes.string,
    onSave: PropTypes.func.isRequired,
};
EmailsQueueStatusModal.defaultProps = {
    emailId: null,
    width: 'tiny',
};

export default EmailsQueueStatusModal;
