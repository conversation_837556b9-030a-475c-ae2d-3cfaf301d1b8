import i18next from 'i18next';
import DocumentTitle from 'react-document-title';
import PropTypes from 'prop-types';
import React from 'react';
import _ from 'lodash';
import ApiRequest from '../api/apiRequest';
import { guid, roundNumber } from '../common/utils';
import TabsButtonMore from '../components/tabs/tabsButtonMore.react';
import { saveAs } from '../../assets/libs/filesaver';
import LogsApi from '../flux/logs.api';
import PageRights from '../common/pageRights';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import MainButtons from '../components/tabs/mainButtons.react';
import MainButton from '../components/tabs/mainButton.react';
import TabsButtonsTable from '../components/tabs/tabsButtonsTable.react';
import TabsButton from '../components/tabs/tabsButton.react';
import Table from '../components/table/table.react';
import Column from '../components/table/column.react';
import LogModal from './modals/logModal.react';
import XmlProcessModal from './modals/xmlProcessModal.react';
import LogTimeRangeModal from './modals/logsTimeRangeModal.react';
import AlertsActions from '../components/alerts/alerts.actions';
import AddNoteModal from './modals/addNoteModal.react';

class XmlProcessImport extends React.Component {

    constructor(props) {
        super();
        this.state = {
            xmlProcessModalIsOpen: false,
            logDetailModalIsOpen: false,
            logTimeRangeModal: false,
            addNoteModalIsOpen: false,
            selectedLog: {}, // archived log
            logId: null,
            logTime: '',
            choicesLogCategory: [],
        };

        this.openLogDetailModal = this.openLogDetailModal.bind(this);
        this.closeLogDetailModal = this.closeLogDetailModal.bind(this);
        this.openXmlProcessModal = this.openXmlProcessModal.bind(this);
        this.closeXmlProcessModal = this.closeXmlProcessModal.bind(this);
        this.openLogTimeRangeModal = this.openLogTimeRangeModal.bind(this);
        this.closeLogTimeRangeModal = this.closeLogTimeRangeModal.bind(this);
        this.refreshTable = this.refreshTable.bind(this);
        this.goBack = this.goBack.bind(this);
        this.loadLogsCategories = this.loadLogsCategories.bind(this);
        this.openAddNoteModal = this.openAddNoteModal.bind(this);
        this.closeAddNoteModal = this.closeAddNoteModal.bind(this);
        this.downloadFile = this.downloadFile.bind(this);
        this.skipXml = this.skipXml.bind(this);
        this.importXml = this.importXml.bind(this);
        this.reImportXml = this.reImportXml.bind(this);
        this.addNote = this.addNote.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin
        PageRights.checkUserRights([-1]);
    }

    componentDidMount() {
        BreadcrumbActions.changeBread([
            { name: i18next.t('administration'), to: '/administration-menu' },
            { name: i18next.t('xmlProcessImport'), to: location.pathname },
        ]);

        this.loadLogsCategories();
    }

    loadLogsCategories() {
        LogsApi.fetchLogsCategories()
            .then((payload) => {
                this.setState({ choicesLogCategory: payload });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: `${i18next.t('alrFailedData')} (Logs categories)`,
                    serverError: errorMessage,
                });
            });
    }

    goBack(e) {
        e.preventDefault();
        this.props.history.push('/administration-menu');
    }

    openXmlProcessModal(refToTable, e, rowId, row) {
        e.preventDefault();

        const tableRef =
            this.refs.tableImportAudit ||
            this.refs.plannedImports ||
            this.refs.finishedImports;
        let selectedLog = null;

        this.setState({
            logTime: (tableRef.state.selectedRow.row || row).time,
        });

        if (
            this.refs.tableImportAudit ||
            this.refs.plannedImports ||
            this.refs.finishedImports
        ) {
            selectedLog = tableRef.state.selectedRow.row || row;

            if (typeof rowId !== 'undefined' && rowId !== null) {
                selectedLog = _.find(tableRef.state.items, ['id', rowId]);
            }
        }

        this.setState({
            xmlProcessModalIsOpen: true,
            logId: tableRef.state.selectedRow.rowId || rowId,
            selectedLog: selectedLog,
        });
    }

    closeXmlProcessModal() {
        this.setState({ xmlProcessModalIsOpen: false });
    }

    openLogDetailModal(refToTable, e, rowId, row) {
        e.preventDefault();

        const tableRef = this.refs.tableLogs;
        let selectedLog = null;

        this.setState({
            logTime: (tableRef.state.selectedRow.row || row).time,
        });

        if (
            this.refs.tableImportAudit ||
            this.refs.plannedImports ||
            this.refs.finishedImports
        ) {
            selectedLog = tableRef.state.selectedRow.row;

            if (typeof rowId !== 'undefined' && rowId !== null) {
                selectedLog = _.find(tableRef.state.items, ['id', rowId]);
            }
        }

        this.setState({
            logDetailModalIsOpen: true,
            logId: tableRef.state.selectedRow.rowId || rowId,
            selectedLog: selectedLog,
        });
    }

    closeLogDetailModal() {
        this.setState({ logDetailModalIsOpen: false });
    }

    openLogTimeRangeModal(e) {
        e.preventDefault();
        this.setState({ logTimeRangeModal: true });
    }

    closeLogTimeRangeModal() {
        this.setState({ logTimeRangeModal: false });
    }

    // Promise factory
    createTableFetchP(
        data,
        offset,
        limit,
        filter,
        mFilter,
        tFilter,
        defaultFilter,
        order,
        sort,
        defaultSort,
        totalCount,
        tableState,
    ) {
        // time range filter

        const logsFrom = tableState.logsTimeRange?.from;
        const logsTo = tableState.logsTimeRange?.to;

        const rangeFilterArr = [];
        if (logsFrom !== null) {
            rangeFilterArr.push(`from=${logsFrom}`);
        }
        if (logsTo !== null) {
            rangeFilterArr.push(`to=${logsTo}`);
        }
        const timeRangeFilter = rangeFilterArr.join('&');

        if (tableState.tableName === 'xmlLogs') {
            return LogsApi.fetchLogs(
                data,
                offset,
                limit,
                filter,
                timeRangeFilter,
                tFilter,
                defaultFilter,
                order,
                sort,
                defaultSort,
            )
                .then((payload) => {
                    if (!_.isEmpty(payload)) {
                        return payload;
                    }

                    return { items: [], total_count: 0 };
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: `${i18next.t('alrLogsLoadFailed')} (Logs)`,
                        serverError: errorMessage,
                    });
                    return { items: [], total_count: 0 };
                });
        }
    }

    apiCallback(data) {
        const array = [];

        data.items.forEach((item, i) => {
            const obj = {};

            if (!item.hasOwnProperty('id')) {
                obj.id = item._key;
            }

            for (const key in item) {
                if (item.hasOwnProperty(key)) {
                    obj[key] = item[key];
                }
            }

            array.push(obj);
        });

        return array;
    }

    logsRowStyleRendeder(row) {
        if (row.level === 1) {
            return 'red';
        }
        if (row.level === 2) {
            return 'orange';
        }

        return '';
    }

    refreshTable(e) {
        if (e) e.preventDefault();
        const tableRef =
            this.refs.tableLogs ||
            this.refs.tableImportAudit ||
            this.refs.plannedImports ||
            this.refs.finishedImports;
        if (typeof tableRef !== 'undefined') {
            tableRef.handleFetchRows();
        }
    }

    openAddNoteModal(e) {
        e.preventDefault();
        this.setState({ addNoteModalIsOpen: true });
    }

    closeAddNoteModal() {
        this.setState({ addNoteModalIsOpen: false });
    }

    addNote(data) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const tableRef = this.refs.tableImportAudit;
        const { rowId } = tableRef.state.selectedRow;

        const postObj = { xpi_note: data.note };
        ApiRequest.post(`/xml-process-import-audit/${rowId}/set-note`, postObj)
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });
                this.closeAddNoteModal();
                this.refreshTable();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSavingFailed'),
                    serverError: errorMessage,
                });
            });
    }

    downloadFile(e, fileType) {
        e.preventDefault();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const tableRef = this.refs.plannedImports || this.refs.finishedImports;
        const { row } = tableRef.state.selectedRow;

        const url = this.refs.plannedImports
            ? `/xml-process-import-plan/${fileType}/download`
            : `/xml-process-import-history/${fileType}/download`;
        const fileName = row[`${fileType}_file_name`];

        ApiRequest.getFile(url, null, null, row)
            .then((payload) => {
                saveAs(payload, fileName);

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachDownloaded'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrAttachDownloadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    skipXml(e) {
        e.preventDefault();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const tableRef = this.refs.plannedImports;
        const { row } = tableRef.state.selectedRow;

        ApiRequest.post('/xml-process-import-plan/skip', row)
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSavingFailed'),
                    serverError: errorMessage,
                });
            });
    }

    importXml(e) {
        e.preventDefault();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const tableRef = this.refs.plannedImports;
        const { row } = tableRef.state.selectedRow;

        ApiRequest.post('/xml-process-import-plan/process', row)
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSavingFailed'),
                    serverError: errorMessage,
                });
            });
    }

    reImportXml(e) {
        e.preventDefault();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const tableRef = this.refs.finishedImports;
        const { row } = tableRef.state.selectedRow;

        ApiRequest.post('/xml-process-import-history/reimport', row)
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSavingFailed'),
                    serverError: errorMessage,
                });
            });
    }

    render() {
        const choicesLogLevel = [
            { value: 1, title: 'error', htmlTitle: 1 },
            { value: 2, title: 'warn', htmlTitle: 2 },
            { value: 3, title: 'info', htmlTitle: 3 },
            { value: 4, title: 'debug', htmlTitle: 4 },
            { value: 5, title: 'trace', htmlTitle: 5 },
        ];

        const tableRef =
            this.refs.tableImportAudit ||
            this.refs.plannedImports ||
            this.refs.finishedImports;
        let tableName = '';

        if (tableRef) {
            tableName = tableRef.state.tableName;
        }

        return (
            <DocumentTitle title={i18next.t('xmlProcessImport')}>
                <TabsWrapper>
                    <Heading title={i18next.t('xmlProcessImport')}>
                        <MainButtons>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="importAudit"
                            title={i18next.t('importAudit')}
                            tabLink="/administration/xml-process-import"
                            name="tabImportAudit"
                        >
                            <TabsButtonsTable
                                key="buttonsImportAudit"
                                boundTableName="tableImportAudit"
                            >
                                <TabsButton
                                    key="logDetail"
                                    icon="icon-preview-1"
                                    onClick={this.openXmlProcessModal.bind(
                                        null,
                                        'tableImportAudit',
                                    )}
                                >
                                    {i18next.t('detail')}
                                </TabsButton>
                                <TabsButton
                                    key="addNote"
                                    icon="icon-file-edit"
                                    onClick={this.openAddNoteModal}
                                >
                                    {i18next.t('insertNote')}
                                </TabsButton>
                                <TabsButton
                                    key="logRefresh"
                                    icon="icon-sync-2"
                                    onClick={this.refreshTable}
                                    isActive
                                >
                                    {i18next.t('refresh')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="tableImportAudit"
                                ref="tableImportAudit"
                                apiUrl="/xml-process-import/audit"
                                // apiData={in table store}
                                apiCallback={this.apiCallback}
                                name="tableImportAudit"
                                allowSorting
                                showTotalCount
                                isExportable
                                onDoubleClick={this.openXmlProcessModal.bind(
                                    null,
                                    'tableImportAudit',
                                )}
                                canOpenNewTab={false}
                                // rowStyleRenderer={this.logsRowStyleRendeder}
                                presetInlineFilter={{
                                    columnName: 'XPI_FILE_NAME',
                                    order: 'asc',
                                }}
                                filterOnlyOnEnter
                                defaultSort={{ column: 'id', order: 'asc' }}
                            >
                                <Column
                                    title={i18next.t('id')}
                                    name="id"
                                    type="text"
                                    className="align-right"
                                    width="80"
                                />
                                <Column
                                    title={i18next.t('batchId')}
                                    name="xpi_batch_id"
                                    type="text"
                                    className="align-right"
                                    width="80"
                                />
                                <Column
                                    title={i18next.t('caseId')}
                                    name="iproc_id"
                                    type="text"
                                    className="align-right"
                                    width="80"
                                />
                                <Column
                                    title={i18next.t('fileName')}
                                    name="xpi_file_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('state')}
                                    name="xpi_state"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('filePath')}
                                    name="xpi_file_path"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('start')}
                                    name="xpi_start"
                                    type="datetime"
                                />
                                <Column
                                    title={i18next.t('end')}
                                    name="xpi_end"
                                    type="datetime"
                                />
                                <Column
                                    title={`${i18next.t('duration')} (ms)`}
                                    name="duration"
                                    type="number"
                                    allowInlineFiltering={false}
                                    allowSorting={false}
                                    renderer={(value, row) => {
                                        if (row.xpi_start && row.xpi_end) {
                                            const startDate = new Date(
                                                row.xpi_start,
                                            );
                                            const endDate = new Date(
                                                row.xpi_end,
                                            );
                                            return (
                                                endDate.getTime() -
                                                startDate.getTime()
                                            );
                                        }
                                    }}
                                />
                                <Column
                                    title={i18next.t('cronId')}
                                    name="cron_id"
                                    type="text"
                                    className="align-right"
                                />
                                <Column
                                    title={i18next.t('xmlFileSize')}
                                    name="xpi_xml_file_size"
                                    type="number"
                                />
                                <Column
                                    title={i18next.t('attachmentSize')}
                                    name="xpi_dms_file_size"
                                    type="number"
                                />
                                <Column
                                    title={i18next.t('taskResult')}
                                    name="xpi_result"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('note')}
                                    name="xpi_note"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('importedCount')}
                                    name="xpi_imported_count"
                                    type="number"
                                />
                                <Column
                                    title={i18next.t('retryCount')}
                                    name="xpi_retry_count"
                                    type="number"
                                />
                            </Table>
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="tabPlannedImports"
                            title={i18next.t('plannedImports')}
                            tabName="planned-imports"
                            name="tabPlannedImports"
                            tabLink="/administration/xml-process-import/planned-imports"
                        >
                            <TabsButtonsTable
                                key="buttonsPlannedImports"
                                boundTableName="plannedImports"
                            >
                                <TabsButton
                                    key="logDetail"
                                    icon="icon-preview-1"
                                    onClick={this.openXmlProcessModal.bind(
                                        null,
                                        'plannedImports',
                                    )}
                                >
                                    {i18next.t('detail')}
                                </TabsButton>
                                <TabsButton
                                    key="importXml"
                                    icon="icon-play-2"
                                    onClick={this.importXml}
                                >
                                    {i18next.t('importXml')}
                                </TabsButton>
                                <TabsButton
                                    key="skipXml"
                                    icon="icon-stop-2"
                                    onClick={this.skipXml}
                                >
                                    {i18next.t('skipXml')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonsPlannedImports"
                                    boundTableName="plannedImports"
                                >
                                    <TabsButton
                                        key="downloadXml"
                                        icon="icon-file-download"
                                        onClick={(e) =>
                                            this.downloadFile(e, 'xml')}
                                    >
                                        {i18next.t('downloadXml')}
                                    </TabsButton>
                                    <TabsButton
                                        key="downloadAttachment"
                                        icon="icon-file-download"
                                        onClick={(e) =>
                                            this.downloadFile(e, 'dms')}
                                    >
                                        {i18next.t('downloadAttachment')}
                                    </TabsButton>
                                </TabsButtonMore>
                                <TabsButton
                                    key="logRefresh"
                                    icon="icon-sync-2"
                                    onClick={this.refreshTable}
                                    isActive
                                >
                                    {i18next.t('refresh')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="plannedImports"
                                ref="plannedImports"
                                apiUrl="/xml-process-import/scheduled"
                                name="plannedImports"
                                defaultSort={{ column: 'mtime', order: 'desc' }}
                                onDoubleClick={this.openXmlProcessModal.bind(
                                    null,
                                    'plannedImports',
                                )}
                                canOpenNewTab={false}
                                allowSorting
                                showTotalCount
                                filterOnlyOnEnter
                            >
                                <Column
                                    title={i18next.t('fileName')}
                                    name="xml_file_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('filePath')}
                                    name="xml_file_path"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('cronId')}
                                    name="cron_id"
                                    type="text"
                                    className="align-right"
                                    width="100"
                                />
                                <Column
                                    title={i18next.t('lastEdit')}
                                    name="ctime"
                                    type="datetime"
                                />
                                <Column
                                    title={i18next.t('timeCreated')}
                                    name="mtime"
                                    type="datetime"
                                />
                                <Column
                                    title={i18next.t('attachmentName')}
                                    name="dms_file_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('attachmentSize')}
                                    name="dms_file_size"
                                    type="number"
                                />
                                <Column
                                    allowInlineFiltering={false}
                                    allowSorting={false}
                                    title={i18next.t('state')}
                                    name="xpi_state"
                                    type="text"
                                    fil
                                />
                                <Column
                                    allowInlineFiltering={false}
                                    allowSorting={false}
                                    title={i18next.t('importId')}
                                    name="xpi_id"
                                    type="text"
                                    className="align-right"
                                    fil
                                />
                            </Table>
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="tabFinishedImports"
                            title={i18next.t('finishedImports')}
                            tabName="finished-imports"
                            name="tabFinishedImports"
                            tabLink="/administration/xml-process-import/finished-imports"
                        >
                            <TabsButtonsTable
                                key="buttonsFinishedImports"
                                boundTableName="finishedImports"
                            >
                                <TabsButton
                                    key="logDetail"
                                    icon="icon-preview-1"
                                    onClick={this.openXmlProcessModal.bind(
                                        null,
                                        'finishedImports',
                                    )}
                                >
                                    {i18next.t('detail')}
                                </TabsButton>
                                <TabsButton
                                    key="reImportXml"
                                    icon="icon-play-2"
                                    onClick={this.reImportXml}
                                >
                                    {i18next.t('reImportXml')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonsPlannedImports"
                                    boundTableName="plannedImports"
                                >
                                    <TabsButton
                                        key="downloadXml"
                                        icon="icon-file-download"
                                        onClick={(e) =>
                                            this.downloadFile(e, 'xml')}
                                    >
                                        {i18next.t('downloadXml')}
                                    </TabsButton>
                                    <TabsButton
                                        key="downloadAttachment"
                                        icon="icon-file-download"
                                        onClick={(e) =>
                                            this.downloadFile(e, 'dms')}
                                    >
                                        {i18next.t('downloadAttachment')}
                                    </TabsButton>
                                </TabsButtonMore>
                                <TabsButton
                                    key="logRefresh"
                                    icon="icon-sync-2"
                                    onClick={this.refreshTable}
                                    isActive
                                >
                                    {i18next.t('refresh')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="finishedImports"
                                ref="finishedImports"
                                apiUrl="/xml-process-import/history"
                                name="finishedImports"
                                defaultSort={{ column: 'mtime', order: 'desc' }}
                                onDoubleClick={this.openXmlProcessModal.bind(
                                    null,
                                    'finishedImports',
                                )}
                                canOpenNewTab={false}
                                allowSorting
                                showTotalCount
                                filterOnlyOnEnter
                            >
                                <Column
                                    title={i18next.t('fileName')}
                                    name="xml_file_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('filePath')}
                                    name="xml_file_path"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('xmlFileSize')}
                                    name="xpi_xml_file_size"
                                    type="number"
                                />
                                <Column
                                    title={i18next.t('cronId')}
                                    name="cron_id"
                                    type="text"
                                    className="align-right"
                                    width="100"
                                />
                                <Column
                                    title={i18next.t('lastEdit')}
                                    name="ctime"
                                    type="datetime"
                                />
                                <Column
                                    title={i18next.t('timeCreated')}
                                    name="mtime"
                                    type="datetime"
                                />
                                <Column
                                    title={i18next.t('attachmentName')}
                                    name="dms_file_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('attachmentSize')}
                                    name="dms_file_size"
                                    type="number"
                                />
                            </Table>
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="xmlImportLogs"
                            title={i18next.t('logs')}
                            tabName="logs"
                            name="tabXmlImportLogs"
                            tabLink="/administration/xml-process-import/logs"
                        >
                            <TabsButtonsTable
                                key="buttonsXmlImportLogs"
                                boundTableName="xmlLogs"
                            >
                                <TabsButton
                                    key="logDetail"
                                    icon="icon-preview-1"
                                    onClick={this.openLogDetailModal.bind(
                                        null,
                                        'tableLogs',
                                    )}
                                >
                                    {i18next.t('detail')}
                                </TabsButton>
                                <TabsButton
                                    key="logTimeRange"
                                    icon="icon-calendar-2"
                                    onClick={this.openLogTimeRangeModal}
                                    isActive
                                    classNameRenderer={(state) => {
                                        // table state
                                        if (
                                            state.logsTimeRange?.from ||
                                            state.logsTimeRange?.to
                                        ) {
                                            return 'orange';
                                        }
                                        return '';
                                    }}
                                >
                                    {i18next.t('timeRange')}
                                </TabsButton>
                                <TabsButton
                                    key="logsRefresh"
                                    icon="icon-sync-2"
                                    onClick={this.refreshTable}
                                    isActive
                                >
                                    {i18next.t('refresh')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="xmlLogs"
                                ref="tableLogs"
                                apiUrl={this.createTableFetchP}
                                // apiData={in table store}
                                apiCallback={this.apiCallback}
                                name="xmlLogs"
                                allowSorting
                                showTotalCount
                                isExportable={false}
                                onDoubleClick={this.openLogDetailModal.bind(
                                    null,
                                    'tableLogs',
                                )}
                                canOpenNewTab={false}
                                rowStyleRenderer={this.logsRowStyleRendeder}
                                presetInlineFilter={{
                                    columnName: 'category',
                                    content: '18',
                                }}
                            >
                                <Column
                                    title={i18next.t('date')}
                                    name="time"
                                    type="exactDatetime"
                                    width="200"
                                    addMilliseconds
                                    calendarWithClockpickerFilter
                                />
                                <Column
                                    title=""
                                    name="time"
                                    type="text"
                                    width="55"
                                    allowInlineFiltering={false}
                                    allowSorting={false}
                                    rendererAddNextRow
                                    renderer={(value, row, nextRow) => {
                                        let duration = '';
                                        if (nextRow) {
                                            const diff =
                                                Date.parse(row.time) -
                                                Date.parse(nextRow.time);
                                            const rDiff = roundNumber(
                                                diff / 1000,
                                                2,
                                            );
                                            if (rDiff < 59) {
                                                duration = `${rDiff}s`;
                                            }
                                        }
                                        return duration;
                                    }}
                                />
                                <Column
                                    title={i18next.t('message')}
                                    name="message"
                                    type="miscellaneous"
                                    maxStringLength={200}
                                />
                                <Column
                                    title={i18next.t('category')}
                                    name="category"
                                    type="multiStateCheckboxList"
                                    width="150"
                                    filterChoices={
                                        this.state.choicesLogCategory
                                    }
                                    renderer={(value, row) => {
                                        const choice = _.find(
                                            this.state.choicesLogCategory,
                                            ['value', value],
                                        );
                                        return choice || value;
                                    }}
                                    checkboxListCommaSeparatedFilterValues
                                />
                                <Column
                                    title="Level"
                                    name="level"
                                    type="checkboxList"
                                    width="120"
                                    filterChoices={choicesLogLevel}
                                    renderer={(value, row) => {
                                        const choice = _.find(
                                            choicesLogLevel,
                                            ['value', value],
                                        );
                                        return choice || value;
                                    }}
                                    checkboxListCommaSeparatedFilterValues
                                />
                                <Column
                                    title={`${i18next.t('duration')} (ms)`}
                                    name="duration"
                                    type="number"
                                    width="120"
                                    filterRenderer={(value, columnName) => {
                                        return `${columnName}<gt>"${value}"`;
                                    }}
                                />
                                <Column
                                    title={i18next.t('importId')}
                                    name="xpi_id"
                                    type="text"
                                    className="align-right"
                                    width="90"
                                    filterRenderer={(value, columnName) => {
                                        return `${columnName}<eq>"${value}"`;
                                    }}
                                />
                                <Column
                                    title={i18next.t('caseId')}
                                    name="iproc_id"
                                    type="text"
                                    className="align-right"
                                    width="90"
                                    filterRenderer={(value, columnName) => {
                                        return `${columnName}<eq>"${value}"`;
                                    }}
                                />
                            </Table>
                        </Tabs.Tab>
                    </Tabs>
                    {this.state.logDetailModalIsOpen && (
                        <LogModal
                            isOpen={this.state.logDetailModalIsOpen}
                            width="large"
                            onClose={this.closeLogDetailModal}
                            log={this.state.selectedLog}
                            logId={this.state.logId}
                            logTime={this.state.logTime}
                        />
                    )}
                    {this.state.xmlProcessModalIsOpen && (
                        <XmlProcessModal
                            isOpen={this.state.xmlProcessModalIsOpen}
                            width="large"
                            onClose={this.closeXmlProcessModal}
                            log={this.state.selectedLog}
                            logId={this.state.logId}
                            logTime={this.state.logTime}
                            tableName={tableName}
                        />
                    )}
                    {this.state.logTimeRangeModal && (
                        <LogTimeRangeModal
                            isOpen={this.state.logTimeRangeModal}
                            width="own-width-335"
                            onClose={this.closeLogTimeRangeModal}
                            tableRef={this.refs.tableLogs}
                            tableName="xmlLogs"
                        />
                    )}
                    {this.state.addNoteModalIsOpen && (
                        <AddNoteModal
                            isOpen={this.state.addNoteModalIsOpen}
                            width="medium"
                            onClose={this.closeAddNoteModal}
                            onSave={this.addNote}
                            value={tableRef.state.selectedRow.row.xpi_note}
                        />
                    )}
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

XmlProcessImport.displayName = 'XmlProcessImport';

XmlProcessImport.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default XmlProcessImport;
