import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { guid } from '../common/utils';
import { Text } from '../components/form/text.react';
import { SelectBox } from '../components/form/selectBox.react';
import { Checkbox } from '../components/form/checkbox.react';
import PageRights from '../common/pageRights';
import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import MainButton from '../components/tabs/mainButton.react';
import Form from '../components/form/form.react';
import Label from '../components/form/label.react';
import ShowHideComponent from '../components/form/showHideComponent.react';
import MultiBox from '../components/form/multiBox.react';
import LoggedUserStore from '../flux/loggedUser.store';
import TemplateHeaderActions from '../flux/templateHeader.actions';
import TemplateHeaderStore from '../flux/templateHeader.store';
import TemplateHeaderApi from '../flux/templateHeader.api';
import alt from '../flux/alt';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import ConfirmModal from './modals/confirmModal.react';

class TemplateHeader extends React.Component {

    constructor(props) {
        super();
        this.state = _.extend(
            {
                formIsPristine: true,
                multiBoxesArr: [],
                multiBoxesLoaded: false,
                confirmModalIsOpen: false,
            },
            TemplateHeaderStore.getState(),
        );

        this._onChange = this._onChange.bind(this);
        this.watchHeaderFormBeforeLeave =
            this.watchHeaderFormBeforeLeave.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.saveHeader = this.saveHeader.bind(this);
        this.postData = this.postData.bind(this);
        this.goBack = this.goBack.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.pushMultiBox = this.pushMultiBox.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        TemplateHeaderStore.listen(this._onChange);

        const { templateId, versionId, templateHeaderId } =
            this.props.match.params;

        // location state for previous breadcrumb path and close button
        const locationState = this.props.location.state;
        if (locationState) {
            TemplateHeaderActions.savePrevPath(locationState);
        }

        TemplateHeaderActions.getTemplateName(templateId, versionId);

        if (templateHeaderId !== 'new') {
            TemplateHeaderActions.fetchTemplateHeader(
                templateId,
                versionId,
                templateHeaderId,
            );
        } else {
            TemplateHeaderActions.setHeaderHeading(i18next.t('newShe'));
        }

        this.watchHeaderFormBeforeLeave(templateId);
    }

    UNSAFE_componentWillUpdate(nextProps, nextState) {
        // load header data after saving new header without closing
        const { templateId, versionId, templateHeaderId } =
            this.props.match.params;
        const nextHeaderId = nextProps.match.params.templateHeaderId;

        if (nextHeaderId !== 'new' && nextHeaderId !== templateHeaderId) {
            TemplateHeaderActions.setHeaderHeading('');
            TemplateHeaderActions.fetchTemplateHeader(
                templateId,
                versionId,
                nextHeaderId,
            );
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevState.templateHeading !== this.state.templateHeading ||
            prevState.headerHeading !== this.state.headerHeading
        ) {
            const { templateId, versionId, templateHeaderId } =
                this.props.match.params;

            BreadcrumbActions.changeBread([
                {
                    name: i18next.t('templates'),
                    to:
                        this.state.breadPrevPath !== null
                            ? this.state.breadPrevPath
                            : '/templates',
                },
                {
                    name: this.state.templateHeading,
                    to: `/templates/template/${templateId}/${versionId}/headers`,
                },
                {
                    name: `${i18next.t('header')} - ${this.state.headerHeading}`,
                    to: `/templates/template/${templateId}/${versionId}/template-header/${templateHeaderId}`,
                },
            ]);
        }
    }

    componentWillUnmount() {
        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        this.unlistenForm();
        alt.recycle(TemplateHeaderStore);
        TemplateHeaderStore.unlisten(this._onChange);

        // resave previous paths after store is recycled
        TemplateHeaderActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });
    }

    _onChange(state) {
        this.setState(state);
    }

    watchHeaderFormBeforeLeave(templateId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const { versionId, templateHeaderId } = this.props.match.params;

                if (
                    !this.state.formIsPristine &&
                    location.pathname !==
                        `/templates/template/${templateId}/${versionId}/template-header/${templateHeaderId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = function (data) {
                        this.postData(data, callback);
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.refs.formHeader.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    saveHeader(close, e) {
        e.preventDefault();

        this.onFormAction = function (data) {
            this.postData(data, null, close);
        };

        this.refs.formHeader.submit();
    }

    postData(data, callback, close) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        let headersRoles = data.roles;
        if (!_.isEmpty(headersRoles) && typeof headersRoles[0] === 'object') {
            headersRoles = headersRoles.map((role) => role.value);
        }

        let headerVisRoleId;
        if (
            typeof data.hdr_vis_role_id !== 'undefined' &&
            data.hdr_vis_role_id !== null &&
            typeof data.hdr_vis_role_id.value !== 'undefined'
        ) {
            headerVisRoleId = data.hdr_vis_role_id.value;
        } else {
            headerVisRoleId = data.hdr_vis_role_id;
        }

        const obj = {
            tproc_id: this.props.match.params.templateId,
            tproc_version: this.props.match.params.versionId,
            header_enabled: data.headerActive ? 'Y' : 'N',
            header_name: data.headerName,
            header_code: data.headerIdentificator,
            header_orgstrs: data.orgUnits,
            header_roles: headersRoles,
            hdr_vis_role_id: headerVisRoleId,
            header_hr_role_id:
                data.header_hr_role_id &&
                typeof data.header_hr_role_id.value !== 'undefined'
                    ? data.header_hr_role_id.value
                    : data.header_hr_role_id,
            hdr_vis_internal_user_only: data.internalLogin ? 'Y' : 'N',
        };

        const { languages } = LoggedUserStore.getState();
        languages.forEach((lang) => {
            obj[`header_name_${lang}`] = data[`header_name_${lang}`];
        });

        const headerId = this.props.match.params.templateHeaderId;

        if (headerId !== 'new') {
            obj.header_id = headerId;
        }

        TemplateHeaderApi.saveTemplateHeader(obj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrHeaderSaved'),
                    fadeOut: true,
                });

                this.setState({ formIsPristine: true }, function () {
                    if (callback) {
                        callback();
                    } else if (close) {
                        this.goBack();
                    } else if (
                        this.props.match.params.templateHeaderId === 'new'
                    ) {
                        // just save
                        const { templateId, versionId } =
                            this.props.match.params;
                        this.props.history.push(
                            `/templates/template/${templateId}/${versionId}/template-header/${payload.id}`,
                        );
                    }
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrHeaderSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        const { templateId, versionId } = this.props.match.params;
        this.props.history.push(
            this.state.closePrevPath !== null
                ? this.state.closePrevPath
                : `/templates/template/${templateId}/${versionId}/headers`,
        );
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    pushMultiBox(multiBox) {
        const multiBoxesClone = _.cloneDeep(this.state.multiBoxesArr);
        const findMultiBox = _.find(multiBoxesClone, ['name', multiBox.name]);

        if (!findMultiBox) {
            multiBoxesClone.push(multiBox);
        } else {
            findMultiBox.checked = multiBox.checked;
        }

        this.setState({ multiBoxesArr: multiBoxesClone }, () => {
            this.setState({
                multiBoxesLoaded: !_.find(multiBoxesClone, ['checked', false]),
            });
        });
    }

    render() {
        const { languages } = LoggedUserStore.getState();
        const header = this.state.templateHeader;
        const { templateHeaderId } = this.props.match.params;

        return (
            <DocumentTitle title={i18next.t('header')}>
                <TabsWrapper>
                    <Heading
                        title={
                            templateHeaderId !== 'new'
                                ? this.state.headerHeading
                                : i18next.t('addHeader')
                        }
                    >
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                enableOn={
                                    !this.state.loading &&
                                    this.state.multiBoxesLoaded
                                }
                                onClick={this.saveHeader.bind(null, true)}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-download-5"
                                buttonColor="white"
                                enableOn={
                                    !this.state.loading &&
                                    this.state.multiBoxesLoaded
                                }
                                onClick={this.saveHeader.bind(null, false)}
                                tooltipCode="ttJustSave"
                            >
                                {i18next.t('justSave')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs>
                        <Tabs.Tab
                            key="templateHeader"
                            title={
                                templateHeaderId !== 'new'
                                    ? i18next.t('editHeader')
                                    : i18next.t('addHeader')
                            }
                            name="tabUser"
                            showLoader
                            loaded={!this.state.loading}
                        >
                            <Form
                                key={`formHeader-${templateHeaderId}`} // re-mount form after saving new header without closing
                                ref="formHeader"
                                name="formHeader"
                                onValidSubmit={this.handleValidSubmit}
                                onChange={this.formChanged}
                            >
                                <Text
                                    key="headerIdentificator"
                                    label={`${i18next.t('identificator')}:`}
                                    value={header.header_code}
                                    side="left"
                                />

                                <Text
                                    key="headerName"
                                    required
                                    label={`${i18next.t('defaultHeaderName')}:`}
                                    value={header.header_name}
                                    side="left"
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <ShowHideComponent key="langName" side="left">
                                    {languages.map((lang, i) => {
                                        return (
                                            <Text
                                                key={`headerName_${lang}`}
                                                name={`header_name_${lang}`}
                                                label={`${i18next.t('headerName')}:`}
                                                value={
                                                    header[
                                                        `header_name_${lang}`
                                                    ]
                                                }
                                                lblLang={lang}
                                                showComponentLabel
                                            />
                                        );
                                    })}
                                </ShowHideComponent>
                                {/* <Text key="defaultCaseName" required label={i18next.t('defaultCaseName') + ":"}
                                      value={items.tproc_default_case_name} side="left"
                                      validationErrors={{isDefaultRequiredValue: i18next.t('isRequired')}} /> */}
                                <Checkbox
                                    key="headerActive"
                                    label={i18next.t('activeShe')}
                                    side="left"
                                    value={header.header_enabled == 'Y'}
                                />
                                <Checkbox
                                    key="internalLogin"
                                    label={i18next.t('visInternalUserOnly')}
                                    side="left"
                                    value={
                                        header.hdr_vis_internal_user_only == 'Y'
                                    }
                                />
                                <SelectBox
                                    key="hdr_vis_role_id"
                                    label={`${i18next.t('headerVisForRole')}:`}
                                    selectBoxType="DLR"
                                    side="left"
                                    value={header.hdr_vis_role_id}
                                    defaultValue={header.hdr_vis_role_name}
                                    showComponentLabel
                                />
                                <SelectBox
                                    key="header_hr_role_id"
                                    label={`${i18next.t('enableTasksHandoverRole')}:`}
                                    side="left"
                                    selectBoxType="DLR"
                                    defaultValue={header.header_hr_role_name}
                                    value={header.header_hr_role_id}
                                    showComponentLabel
                                />
                                <Label
                                    key="orgUnitsLabel"
                                    side="left"
                                    value={i18next.t('orgUnitsExecRightsText')}
                                />
                                <MultiBox
                                    key="orgUnits"
                                    label={`${i18next.t('orgUnits')}:`}
                                    side="left"
                                    value={this.state.headerOrgUnits}
                                    multiBoxType="DLO"
                                    changeableValue
                                    pushMultiBox={this.pushMultiBox}
                                />
                                <Label
                                    key="rolesLabel"
                                    side="left"
                                    value={i18next.t('rolesExecRightsText')}
                                />
                                <MultiBox
                                    key="roles"
                                    label={`${i18next.t('roleSg')}:`}
                                    side="left"
                                    value={this.state.headerRoles}
                                    valueIsArrayOfObjects
                                    multiBoxType="DLR"
                                    changeableValue
                                    pushMultiBox={this.pushMultiBox}
                                />
                            </Form>
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplateHeader.displayName = 'TemplateHeader';

TemplateHeader.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default TemplateHeader;
