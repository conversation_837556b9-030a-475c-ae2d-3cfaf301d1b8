import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { guid } from '../common/utils';
import Dropzone from '../components/dropzone.react';
import ApiRequest from '../api/apiRequest';

const React = require('react');
const Heading = require('../components/heading.react');
const Tabs = require('../components/tabs/tabs.react');
const TabsWrapper = require('../components/tabs/tabsWrapper.react');
const MainButtons = require('../components/tabs/mainButtons.react');
const MainButton = require('../components/tabs/mainButton.react');
const AlertsActions = require('../components/alerts/alerts.actions');
const BreadcrumbActions = require('../flux/breadcrumb.actions');

class UploadCsv extends React.PureComponent {

    constructor(props) {
        super(props);

        this.onLoad = this.onLoad.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.goBack = this.goBack.bind(this);
    }

    onLoad(file, readerEvt) {
        // kontrola na .csv koncovku souboru
        const fileName = file.name;
        const extRegPattern = /(?:\.([^.]+))?$/;
        const ext = extRegPattern.exec(fileName)[1];

        if (ext == 'csv') {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: i18next.t('alrSaving'),
            });

            file.base64 = readerEvt.target.result.split(',', 2)[1];
            const obj = { fileName: fileName, fileBody: file.base64 };

            ApiRequest.post('/user-parameters/csv', JSON.stringify(obj))
                .then((payload) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrSaved'),
                    });
                    this.goBack();
                })
                .catch((errorMessage) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrFileUploadFailed'),
                        serverError: errorMessage,
                    });
                });
        } else {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrCsvUploadWrongExtension'),
            });
        }
    }

    componentDidMount() {
        BreadcrumbActions.changeBread([
            { name: i18next.t('administration'), to: '/administration-menu' },
            { name: i18next.t('Csv'), to: '/administration/csv' },
            {
                name: i18next.t('uploadCsv'),
                to: '/administration/settings/upload-csv',
            },
        ]);

        i18next.on('languageChanged', (options) => {
            BreadcrumbActions.changeBread.defer([
                {
                    name: i18next.t('administration'),
                    to: '/administration-menu',
                },
                { name: i18next.t('Csv'), to: '/administration/csv' },
                {
                    name: i18next.t('uploadCsv'),
                    to: '/administration/settings/upload-csv',
                },
            ]);
        });
    }

    handleSubmit(event) {}

    handleValidSubmit(e) {
        alert(JSON.stringify(e, null, 4));
    }

    goBack(e) {
        if (e) e.preventDefault();
        this.props.history.goBack();
    }

    render() {
        return (
            <DocumentTitle title={i18next.t('uploadCsv')}>
                <TabsWrapper>
                    <Heading title={i18next.t('uploadCsv')}>
                        <MainButtons>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs>
                        <Tabs.Tab
                            key="uploadCsv"
                            title={i18next.t('uploadCsv')}
                            name="tabUploadCsv"
                        >
                            <div id="dropzone">
                                <Dropzone
                                    onLoad={this.onLoad}
                                    className="dropzone"
                                    activeClassName="dropzone-active"
                                    multiple={false}
                                    isBase64
                                >
                                    <div
                                        dangerouslySetInnerHTML={{
                                            __html: i18next.t('dragAddFiles', {
                                                here: `<b>${i18next.t('here')}</b>`,
                                            }),
                                        }}
                                    />
                                </Dropzone>
                            </div>
                        </Tabs.Tab>
                    </Tabs>
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

UploadCsv.displayName = 'UploadCsv';

UploadCsv.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
};

module.exports = UploadCsv;
