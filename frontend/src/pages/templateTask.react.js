import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { guid, checkLangMutation } from '../common/utils';
import emailTemplate from './templateTask/emailTemplate';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';

const React = require('react');
const _ = require('lodash');
const Heading = require('../components/heading.react');
const HiddenTabs = require('../components/tabs/hiddenTabs.react');
const HiddenTab = require('../components/tabs/hiddenTab.react');
const MainButtons = require('../components/tabs/mainButtons.react');
const TabsButtonsOther = require('../components/tabs/tabsButtonsOther.react');
const TabsWrapper = require('../components/tabs/tabsWrapper.react');
const MainButton = require('../components/tabs/mainButton.react');
const TabsButton = require('../components/tabs/tabsButton.react');
const TemplateTaskStore = require('../flux/templateTask.store');
const TemplateTaskActions = require('../flux/templateTask.actions');
const TemplateTaskApi = require('../flux/templateTask.api');
const AlertsActions = require('../components/alerts/alerts.actions');
const BreadcrumbActions = require('../flux/breadcrumb.actions');
const BreadcrumbStore = require('../flux/breadcrumb.store');
const alt = require('../flux/alt');
const TemplateTaskForm = require('./templateTask/templateTaskForm.react');
const TemplateTaskFunctions = require('./templateTask/templateTaskFunctions.react');
const Helper = require('../components/form/componentHelper.react');
const LoggedUserStore = require('../flux/loggedUser.store');
const io = require('../io');
const ConfirmModal = require('./modals/confirmModal.react');
const SendTestEmailModal = require('./modals/sendTestEmailModal.react');

class TemplateTask extends React.Component {

    constructor(props) {
        super();
        this.state = _.extend(
            {
                formIsPristine: true,
                calcsHelperIsVisible: false,
                condsHelperIsVisible: false,
                confirmModalIsOpen: false,
                confirmMultiinstanceModalIsOpen: false,
                sendModalIsOpen: false,
                multiinstanceConfirmed: false,
                someCalculationChanged: false, // is used to reload calculations after just saving them
            },
            TemplateTaskStore.getState(),
        );

        this._onChange = this._onChange.bind(this);
        this.watchTemplateTaskBeforeLeave =
            this.watchTemplateTaskBeforeLeave.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleValidSubmitWithoutRequire =
            this.handleValidSubmitWithoutRequire.bind(this);
        this.handleInvalidSubmit = this.handleInvalidSubmit.bind(this);
        this.saveTemplateTask = this.saveTemplateTask.bind(this);
        this.postData = this.postData.bind(this);
        this.taskTypeChange = this.taskTypeChange.bind(this);
        this.handleAddVariable = this.handleAddVariable.bind(this);
        this.goBack = this.goBack.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.checkEvent = this.checkEvent.bind(this);
        this.processChange = this.processChange.bind(this);
        this.eventChange = this.eventChange.bind(this);
        this.resetMapping = this.resetMapping.bind(this);
        this.showDocs = this.showDocs.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.closeConfirmMultiinstanceModal =
            this.closeConfirmMultiinstanceModal.bind(this);
        this.cancelConfirmMultiinstance =
            this.cancelConfirmMultiinstance.bind(this);
        this.confirmMultiinstance = this.confirmMultiinstance.bind(this);
        this.openSendModal = this.openSendModal.bind(this);
        this.confirmSending = this.confirmSending.bind(this);
        this.closeSendModal = this.closeSendModal.bind(this);
        this.someCalculationChanged = this.someCalculationChanged.bind(this);
        this.componentWithRowsChanged =
            this.componentWithRowsChanged.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        TemplateTaskStore.listen(this._onChange);

        const { templateId, versionId, templateTaskId } =
            this.props.match.params;

        if (config.socketNotifs) {
            const url = {
                url: `/templates/template/${templateId}/template-task/${templateTaskId}`,
            };
            try {
                io.getSocket().post('/watch-url', url, (response) => {
                    // console.log(response);
                });
            } catch (e) {
                // console.log(e);
            }
        }

        // location state for previous breadcrumb path and close button
        const locationState = this.props.location.state;

        if (locationState) {
            TemplateTaskActions.savePrevPath(this.props.location.state);
        }

        // Get template heading
        ApiRequest.get(`/template-processes/${templateId}/${versionId}`)
            .then((payload) => {
                TemplateTaskActions.setTemplateHeading(
                    checkLangMutation(payload, 'tproc_name'),
                );
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTempNameLoadFailed'),
                    serverError: errorMessage,
                });
            });

        if (templateTaskId !== 'new') {
            // Fetch components, get template task heading
            this.loadVariablesRequest = TemplateTaskActions.fetchComponents({
                apiUrl: `/template-processes/${templateId}/${versionId}/template-tasks/${templateTaskId}`,
                resaveFilter: true,
            });
        } else {
            TemplateTaskActions.setTaskHeading(i18next.t('new'));
            i18next.on('languageChanged', (options) => {
                TemplateTaskActions.setTaskHeading(i18next.t('new'));
            });
            TemplateTaskActions.setLoading(false);
        }

        // TemplateTasks for selectbox options in tab solver
        TemplateTaskActions.fetchTemplateTasks(templateId, versionId);
        TemplateTaskActions.fetchTemplateVariables(templateId, versionId);
        TemplateTaskActions.fetchTemplateProcesses();
        TemplateTaskActions.fetchUsersEmails();
        TemplateTaskActions.fetchEvents();

        this.watchTemplateTaskBeforeLeave(templateId);
    }

    UNSAFE_componentWillUpdate(nextProps, nextState) {
        // load task data after new task is saved without close
        const { templateId, versionId, templateTaskId } =
            this.props.match.params;
        const nextTemplateTaskId = nextProps.match.params.templateTaskId;

        if (
            nextTemplateTaskId !== 'new' &&
            nextTemplateTaskId !== templateTaskId
        ) {
            TemplateTaskActions.setTaskHeading('');
            this.loadVariablesRequest = TemplateTaskActions.fetchComponents({
                apiUrl: `/template-processes/${templateId}/${versionId}/template-tasks/${nextTemplateTaskId}`,
                resaveFilter: true,
            });
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevState.templateHeading !== this.state.templateHeading ||
            prevState.taskHeading !== this.state.taskHeading
        ) {
            const { templateId, versionId, templateTaskId } =
                this.props.match.params;

            BreadcrumbActions.changeBread([
                {
                    name: i18next.t('templates'),
                    to:
                        this.state.breadPrevPath !== null
                            ? this.state.breadPrevPath
                            : '/templates',
                },
                {
                    name: this.state.templateHeading,
                    to: `/templates/template/${templateId}/${versionId}/tasks`,
                },
                {
                    name: `${i18next.t('tsk')} - ${this.state.taskHeading}`,
                    to: `/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}`,
                },
            ]);
        }
    }

    componentWillUnmount() {
        if (config.socketNotifs && LoggedUserStore.isLogged()) {
            const url = {
                url: `/templates/template/${this.props.match.params.templateId}/template-task/${this.props.match.params.templateTaskId}`,
            };
            io.getSocket().post('/unwatch-url', url, (msg) => {
                // console.log('unwatch', msg);
            });
        }

        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        if (typeof this.loadVariablesRequest !== 'undefined') {
            this.loadVariablesRequest.cancel();
        }

        this.unlistenForm();
        alt.recycle(TemplateTaskStore);
        TemplateTaskStore.unlisten(this._onChange);

        // resave previous paths after store is recycled
        TemplateTaskActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });
    }

    _onChange(state) {
        this.setState(state);
    }

    watchTemplateTaskBeforeLeave(templateId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const { templateTaskId, versionId } = this.props.match.params;
                const pathArr = location.pathname.split('/');
                if (pathArr.length > 7) {
                    pathArr.pop();
                }
                const newPath = pathArr.join('/');

                if (
                    (!this.state.formIsPristine ||
                        this.state.someCalculationChanged ||
                        this.state.componentWithRowsChanged) &&
                    newPath !==
                        `/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = (data) => {
                        this.checkMultiinstanceAndPost(data, callback, false);
                    };

                    this.onFormActionWithoutRequire = () => {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18next.t('alrFillRequiredItems'),
                            show: true,
                            allowCountdown: true,
                        });
                    };

                    this.onFormActionInvalid = () => {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18next.t('alrFillDataInRightFormat'),
                            show: true,
                            allowCountdown: true,
                        });
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    checkMultiinstanceAndPost(data, callback, close) {
        if (
            data.ttask_multiinstance_flag &&
            data.ttask_iterate_over === null &&
            !this.state.multiinstanceConfirmed
        ) {
            this.setState({
                confirmMultiinstanceModalIsOpen: true,
            });
        } else {
            if (this.state.multiinstanceConfirmed) {
                this.setState({
                    multiinstanceConfirmed: false,
                });
            }
            this.postData(data, callback, close);
        }
    }

    confirmSaving() {
        // save and leave
        this.refs.templateTaskForm.refs.formTemplateTask.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    confirmMultiinstance() {
        this.setState(
            {
                multiinstanceConfirmed: true,
                confirmMultiinstanceModalIsOpen: false,
            },
            () => {
                this.refs.templateTaskForm.refs.formTemplateTask.submit();
            },
        );
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                someCalculationChanged: false,
                componentWithRowsChanged: false,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    cancelConfirmMultiinstance() {
        // cancel saving and leave
        this.setState({
            confirmMultiinstanceModalIsOpen: false,
        });
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    closeConfirmMultiinstanceModal() {
        // cancel and stay
        this.setState({ confirmMultiinstanceModalIsOpen: false });
    }

    openSendModal() {
        this.setState({ sendModalIsOpen: true });
    }

    confirmSending({ recipient, subject }) {
        this.onFormAction = (data) => {
            const lang = LoggedUserStore.getState().userLanguage;
            const body = {
                recipient: recipient,
                subject: subject,
                content:
                    data[`ttask_enot_body2_${lang}`] || data.ttask_enot_body2, // default
            };

            ApiRequest.post('/mail-test', JSON.stringify(body))
                .then((payload) => {
                    AlertsActions.addAlert({
                        type: 'success',
                        message: i18next.t('sent'),
                    });
                    this.setState({ sendModalIsOpen: false });
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrSendingTestMailFailed'),
                        serverError: errorMessage,
                    });
                });
        };

        this.refs.templateTaskForm.refs.formTemplateTask.submit();
    }

    closeSendModal() {
        this.setState({ sendModalIsOpen: false });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleValidSubmitWithoutRequire() {
        this.onFormActionWithoutRequire();
    }

    handleInvalidSubmit() {
        this.onFormActionInvalid();
    }

    saveTemplateTask(close, e) {
        if (e) {
            e.preventDefault();
        }

        this.onFormAction = (data) => {
            this.checkMultiinstanceAndPost(data, null, close);
        };

        this.onFormActionWithoutRequire = () => {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillRequiredItems'),
                show: true,
                allowCountdown: true,
            });
        };

        this.onFormActionInvalid = () => {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        };

        this.refs.templateTaskForm.refs.formTemplateTask.submit();
    }

    postData(data, callback, close) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = TemplateTaskFunctions.getPostObject(
            data,
            this.props.match.params.templateId,
            this.props.match.params.versionId,
            this.props.match.params.templateTaskId,
            this.state.taskType,
            this.state.varsMappingOrder,
            false,
        );

        const saveTemplTask = () => {
            TemplateTaskApi.saveTemplateTask(obj)
                .then((payload) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrSaved'),
                    });

                    this.setState(
                        {
                            formIsPristine: true,
                            someCalculationChanged: false,
                            componentWithRowsChanged: false,
                        },
                        () => {
                            if (callback) {
                                callback();
                            } else if (close) {
                                this.goBack();
                            } else if (
                                this.props.match.params.templateTaskId === 'new'
                            ) {
                                // just save
                                this.props.history.push(
                                    window.location.pathname.replace(
                                        'new',
                                        payload.id,
                                    ),
                                );
                            } else if (this.state.someCalculationChanged) {
                                // just save
                                // due to reloading calculations for ttjscalc_js_es6 update
                                const {
                                    templateId,
                                    versionId,
                                    templateTaskId,
                                } = this.props.match.params;

                                this.loadVariablesRequest =
                                    TemplateTaskActions.fetchComponents({
                                        apiUrl: `/template-processes/${templateId}/${versionId}/template-tasks/${templateTaskId}`,
                                        resaveFilter: true,
                                    });
                            }
                        },
                    );
                })
                .catch((errorMessage) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrSaveFailed'),
                        serverError: errorMessage,
                    });
                });
        };

        saveTemplTask();
    }

    taskTypeChange(name, value) {
        // formChanged() returns isChanged == false after task type change, so
        // this.setState({formIsPristine: false}) has to be called manualy

        new Promise((resolve) => {
            resolve(TemplateTaskActions.changeTaskType(value.value));
        }).then((resolve) => {
            this.setState({
                formIsPristine: false,
                items: { ...this.state.items, ttask_enot_body2: emailTemplate },
            });
        });

        if (
            value.value === 'P' &&
            !_.isEmpty(this.state.templateProcessesOptions)
        ) {
            // value = tproc_id-tproc_version
            const valueArr =
                this.state.templateProcessesOptions[0].value.split('-');
            TemplateTaskActions.changeProcessVariables(
                valueArr[0],
                valueArr[1],
            );
        }
    }

    processChange(name, value) {
        this.resetMapping();
        TemplateTaskActions.resetMapping();
        // value = tproc_id-tproc_version
        const valueArr = value.value.split('-');
        TemplateTaskActions.changeProcessVariables(valueArr[0], valueArr[1]);
    }

    eventChange(name, value) {
        this.resetMapping();
        TemplateTaskActions.resetMapping();
        TemplateTaskActions.changeEventMapping(value.value);
    }

    resetMapping() {
        ['subpMapping', 'subrMapping'].forEach((map) => {
            if (typeof this.refs.templateTaskForm.refs[map] !== 'undefined') {
                this.refs.templateTaskForm.refs[map].resetValues();
            }
        });
    }

    // checkEvent if event is defined
    checkEvent(eventName) {
        TemplateTaskActions.checkEvent(eventName);
    }

    handleAddVariable(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        this.props.history.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-variable/new`,
            state: {
                breadPrevPath: this.state.breadPrevPath,
                closePrevPath: location.pathname,
            },
        });
    }

    showDocs(type, e) {
        if (e) {
            e.preventDefault();
        }

        this.setState({ [type]: !this.state[type] });
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }

        this.props.history.push(
            this.state.closePrevPath !== null
                ? this.state.closePrevPath
                : `/templates/template/${this.props.match.params.templateId}/tasks`,
        );
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            // when any value is changed from its initial value
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    // due to calculations not triggering 'not pristine' state
    someCalculationChanged() {
        if (!this.state.someCalculationChanged) {
            this.setState({ someCalculationChanged: true });
        }
    }

    // due to adding/removing calculations not triggering 'not pristine' state
    componentWithRowsChanged() {
        if (!this.state.componentWithRowsChanged) {
            this.setState({ componentWithRowsChanged: true });
        }
    }

    render() {
        const { items } = this.state;
        const subject = items.ttask_enot_subject; // TODO - nemeni se dynamicky

        const { taskType } = this.state;

        let customTabTitle = '';
        if (taskType == 'W') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('vars')}/${i18next.t('completion')}`;
        } else if (taskType == 'S') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('completion')}`;
        } else if (taskType == 'E') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('vars')}/${i18next.t('event')}`;
        } else if (taskType == 'P') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('vars')}`;
        } else if (taskType == 'I') {
            customTabTitle = `${i18next.t('solver')}/${i18next.t('invitation')}`;
        }

        const { templateId, versionId, templateTaskId } =
            this.props.match.params;

        const url = `/templates/template/${templateId}/template-task/${templateTaskId}`;
        const editingUsers =
            LoggedUserStore.getState().currentlyEditedUrls[url];
        let previousUser;
        let nextUser;
        const me = LoggedUserStore.getState().id;
        if (editingUsers && editingUsers.length > 1) {
            if (editingUsers[0].userId === me) {
                // I was the first user to edit the task
                nextUser = editingUsers[1].userName;
            } else {
                previousUser = editingUsers[0].userName; // user who opened the task as first
            }
        }

        return (
            <DocumentTitle title={i18next.t('tskTemplate')}>
                <TabsWrapper>
                    {this.state.calcsHelperIsVisible && (
                        <Helper
                            type="calculationsDocs"
                            closeHelper={this.showDocs.bind(
                                null,
                                'calcsHelperIsVisible',
                            )}
                        />
                    )}
                    {this.state.condsHelperIsVisible && (
                        <Helper
                            type="conditionsDocs"
                            closeHelper={this.showDocs.bind(
                                null,
                                'condsHelperIsVisible',
                            )}
                        />
                    )}
                    <Heading
                        title={
                            templateTaskId !== 'new'
                                ? this.state.taskHeading
                                : i18next.t('addTsk')
                        }
                    >
                        <MainButtons>
                            {editingUsers && editingUsers.length > 1 && (
                                <li
                                    id="blinking-multiedit-info"
                                    title={
                                        nextUser
                                            ? `${i18next.t('taskEditedByAnotherUser')} (${nextUser})`
                                            : `${i18next.t('taskAlreadyEdited')} (${previousUser})`
                                    }
                                >
                                    <span className="icon icon-pencil-2" />
                                </li>
                            )}
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                onClick={(e) => this.saveTemplateTask(true, e)}
                                enableOn={!this.state.loading}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-download-5"
                                buttonColor="white"
                                enableOn={!this.state.loading}
                                onClick={(e) => this.saveTemplateTask(false, e)}
                                tooltipCode="ttJustSave"
                            >
                                {i18next.t('justSave')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <HiddenTabs
                        params={this.props.match.params}
                        showLoader
                        loaded={!this.state.loading}
                    >
                        <HiddenTab
                            key="activity"
                            title={i18next.t('activity')}
                            tabLink={`/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}`}
                        >
                            <TabsButtonsOther key="buttonsTask">
                                <TabsButton
                                    key="add"
                                    icon="icon-add-1"
                                    isActive
                                    onClick={this.handleAddVariable}
                                    tooltipCode="ttAddActivity"
                                >
                                    {i18next.t('addVariable')}
                                </TabsButton>
                            </TabsButtonsOther>
                        </HiddenTab>
                        {taskType !== 'A' &&
                            taskType !== 'N' &&
                            !!customTabTitle && (
                            <HiddenTab
                                key="custom"
                                title={customTabTitle}
                                tabName="custom"
                                tabLink={`/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}/custom`}
                            >
                                <TabsButtonsOther key="buttonsPlanning">
                                    <TabsButton
                                        key="add"
                                        icon="icon-add-1"
                                        isActive={taskType !== 'I'}
                                        onClick={this.handleAddVariable}
                                        tooltipCode="ttAddActivity"
                                    >
                                        {i18next.t('addVariable')}
                                    </TabsButton>
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        {taskType === 'N' && (
                            <HiddenTab
                                key="notification"
                                title={i18next.t('notification')}
                                tabName="notification"
                                tabLink={`/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}/notification`}
                            >
                                <TabsButtonsOther key="buttonsNotification">
                                    <TabsButton
                                        key="sendTestMail"
                                        icon="icon-mail-2"
                                        isActive
                                        onClick={this.openSendModal}
                                    >
                                        {i18next.t('sendTestMail')}
                                    </TabsButton>
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        <HiddenTab
                            key="calculations"
                            title={i18next.t('calculations')}
                            tabName="calculations"
                            tabLink={`/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}/calculations`}
                        >
                            <TabsButtonsOther key="buttonsCalculations">
                                <TabsButton
                                    key="editorDocumentation"
                                    icon="icon2-files"
                                    isActive
                                    onClick={() =>
                                        window.open(
                                            'https://code.visualstudio.com/docs/editor/codebasics',
                                            '_blank',
                                        )}
                                >
                                    {i18next.t('editorDocumentation')}
                                </TabsButton>
                                <TabsButton
                                    key="showDocs"
                                    icon="icon-bubble-ask-2"
                                    isActive
                                    onClick={this.showDocs.bind(
                                        null,
                                        'calcsHelperIsVisible',
                                    )}
                                >
                                    {i18next.t('help')}
                                </TabsButton>
                            </TabsButtonsOther>
                        </HiddenTab>
                        {taskType === 'S' && (
                            <HiddenTab
                                key="instructions"
                                title={i18next.t('instructions')}
                                tabName="instructions"
                                tabLink={`/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}/instructions`}
                            >
                                <TabsButtonsOther key="buttonsInstructions" />
                            </HiddenTab>
                        )}
                        {taskType !== 'A' && (
                            <HiddenTab
                                key="conditions"
                                title={i18next.t('conditions')}
                                tabName="conditions"
                                tabLink={`/templates/template/${templateId}/${versionId}/template-task/${templateTaskId}/conditions`}
                            >
                                <TabsButtonsOther key="buttonsConditions">
                                    <TabsButton
                                        key="editorDocumentation"
                                        icon="icon2-files"
                                        isActive
                                        onClick={() =>
                                            window.open(
                                                'https://code.visualstudio.com/docs/editor/codebasics',
                                                '_blank',
                                            )}
                                    >
                                        {i18next.t('editorDocumentation')}
                                    </TabsButton>
                                    <TabsButton
                                        key="showDocs"
                                        icon="icon-bubble-ask-2"
                                        isActive
                                        onClick={this.showDocs.bind(
                                            null,
                                            'condsHelperIsVisible',
                                        )}
                                    >
                                        {i18next.t('help')}
                                    </TabsButton>
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        <TemplateTaskForm
                            key={`templateTaskForm-${templateTaskId}`} // re-mount form after saving new task without closing
                            ref="templateTaskForm"
                            taskId={templateTaskId}
                            templateId={templateId}
                            items={items}
                            taskType={taskType}
                            handleValidSubmit={this.handleValidSubmit}
                            handleValidSubmitWithoutRequire={
                                this.handleValidSubmitWithoutRequire
                            }
                            handleInvalidSubmit={this.handleInvalidSubmit}
                            formChanged={this.formChanged}
                            tasksChanged={this.state.tasksChanged}
                            templateVarsDateOptions={
                                this.state.templateVarsDateOptions
                            }
                            templateTasksOptions={
                                this.state.templateTasksOptions
                            }
                            templateVarsUserOptions={
                                this.state.templateVarsUserOptions
                            }
                            templateVarsTextOptions={
                                this.state.templateVarsTextOptions
                            }
                            templateVarsAllOptions={
                                this.state.templateVarsAllOptions
                            }
                            templateProcessesOptions={
                                this.state.templateProcessesOptions
                            }
                            processMappingTargetVarsOptions={
                                this.state.processMappingTargetVarsOptions
                            }
                            templateVarsAllPlusSysOptions={
                                this.state.templateVarsAllPlusSysOptions
                            }
                            processReturnTargetVarsOptions={
                                this.state.processReturnTargetVarsOptions
                            }
                            processReturnSourceVarsOptions={
                                this.state.processReturnSourceVarsOptions
                            }
                            eventsOptions={this.state.eventsOptions}
                            subpReturnValues={this.state.subpReturnValues}
                            subpMappingValues={this.state.subpMappingValues}
                            userOptions={this.state.userOptions}
                            taskTypeChange={this.taskTypeChange}
                            processChange={this.processChange}
                            eventChange={this.eventChange}
                            checkEvent={this.checkEvent}
                            eventSourceVariablesOptions={
                                this.state.eventSourceVariablesOptions
                            }
                            completionFilterVariablesOptions={
                                this.state.completionFilterVariablesOptions
                            }
                            completionFilter={this.state.completionFilter}
                            cancellationFilter={this.state.cancellationFilter}
                            showDocs={this.showDocs.bind(
                                null,
                                'calcsHelperIsVisible',
                            )}
                            suggestList={this.state.taskSuggestionList}
                            customTabTitle={customTabTitle}
                            someCalculationChanged={this.someCalculationChanged}
                            tabName={this.props.match.params.tabName}
                            componentWithRowsChanged={
                                this.componentWithRowsChanged
                            }
                        />
                    </HiddenTabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                    <ConfirmModal
                        text={i18next.t('confirmEmptyMultiinstanceVariable')}
                        width="tiny"
                        isOpen={this.state.confirmMultiinstanceModalIsOpen}
                        onClose={this.closeConfirmMultiinstanceModal}
                        onConfirm={this.confirmMultiinstance}
                        onDiscard={this.cancelConfirmMultiinstance}
                    />
                    <SendTestEmailModal
                        width="tiny"
                        isOpen={this.state.sendModalIsOpen}
                        onClose={this.closeSendModal}
                        onConfirm={this.confirmSending}
                        subject={subject}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplateTask.displayName = 'TemplateTask';

TemplateTask.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

module.exports = TemplateTask;
