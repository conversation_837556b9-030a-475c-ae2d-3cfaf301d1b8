import i18next from 'i18next';
import PropTypes from 'prop-types';
import { Checkbox } from '../../components/form/checkbox.react';
import { TextNum } from '../../components/form/textNum.react';
import React from 'react';
import Form from '../../components/form/form.react';
import Filter from '../../components/form/tempVarFilter.react';
import RadioButton from '../../components/form/radioButton.react';
import TemplateLinkFunctions from './templateLinkFunctions.react';

class TemplateLinkForm extends React.Component {

    constructor(props) {
        super();

        this.conditionsChanged = this.conditionsChanged.bind(this);
        this.getFilterActualVals = this.getFilterActualVals.bind(this);
    }

    conditionsChanged() {
        if (typeof this.props.conditionsChanged === 'function') {
            this.props.conditionsChanged();
        }
    }

    getFilterActualVals() {
        const formData = this.refs.formLink.refs.frmformLink.getCurrentValues();

        return TemplateLinkFunctions.getConditionsArr(formData);
    }

    render() {
        const typeOptions = [
            { value: 'NCOND', title: i18next.t('withoutCond') },
            { value: 'ELSE', title: i18next.t('carriedIfNoOther') },
            { value: 'COND', title: i18next.t('withConditions') },
        ];

        const condOptions = [
            { value: 'AND', title: i18next.t('allMustBeMet') },
            { value: 'OR', title: i18next.t('oneMustBeMet') },
        ];

        const { link } = this.props;

        return (
            <Form
                ref="formLink"
                name="formLink"
                onValidSubmit={this.props.handleValidSubmit}
                onChange={this.props.formChanged}
            >
                <Checkbox
                    key="ttasklink_is_mandatory"
                    label={`${i18next.t('mandatory')}:`}
                    side="left"
                    value={link.ttasklink_is_mandatory == 'Y'}
                />
                <TextNum
                    key="ttasklink_priority"
                    label={`${i18next.t('linkPriority')}:`}
                    value={link.ttasklink_priority}
                    validations={{ isNumeric: true }}
                    validationErrors={{ isNumeric: i18next.t('notNumber') }}
                />
                <RadioButton
                    key="ttasklink_else"
                    label={`${i18next.t('linkConditions')}:`}
                    options={typeOptions}
                    side="left"
                    toShowGroups={{ COND: 'cond' }}
                    value={link.ttasklink_else}
                />
                <RadioButton
                    key="ttasklink_type"
                    label={`${i18next.t('assessmentOfConds')}:`}
                    options={condOptions}
                    side="left"
                    visibilityGroups={['cond']}
                    value={
                        link.ttasklink_type == 'ELSE' ||
                        link.ttasklink_type == null
                            ? condOptions[0].value
                            : link.ttasklink_type
                    }
                />
                <Filter
                    key="conditions"
                    ref="conditions"
                    side="left"
                    label={`${i18next.t('conditions')}:`}
                    checkboxIsVisible={false}
                    getFilterActualVals={this.getFilterActualVals}
                    visibilityGroups={['cond']}
                    conditionsOperators
                    onChange={this.conditionsChanged}
                    loaded={this.conditionsChanged}
                    value={this.props.conditions}
                    variablesOptions={this.props.conditionsVariablesOptions}
                    suggestList={this.props.suggestList}
                />
            </Form>
        );
    }

}

TemplateLinkForm.propTypes = {
    link: PropTypes.object.isRequired,
    handleValidSubmit: PropTypes.func.isRequired,
    formChanged: PropTypes.func.isRequired,
    conditions: PropTypes.arrayOf(PropTypes.object),
    conditionsVariablesOptions: PropTypes.arrayOf(PropTypes.object),
    suggestList: PropTypes.object,
    conditionsChanged: PropTypes.func, // used in templateLinkModal
};

export default TemplateLinkForm;
