export default {
    /**
     * Returns array of formatted conditions for save to store.
     * @param {object} formData
     * @returns {array}
     */
    getConditionsArr: function (formData) {
        let index = 0;
        const conditionsArr = [];

        while (typeof formData[`conditions-array${index}`] !== 'undefined') {
            const condition = {};
            condition.array = formData[`conditions-array${index}`];
            condition.condition = formData[`conditions-condition${index}`];
            condition.operator = formData[`conditions-operator${index}`];
            condition.type = formData[`conditions-array${index}`].type;

            conditionsArr.push(condition);

            index++;
        }

        return conditionsArr;
    },

    /**
     * Returns object for send
     * @param {object} data - form data
     * @param templateId
     * @param versionId
     * @param linkId
     * @param {object} linkState
     * @returns {object}
     */
    getPostObject: function (data, templateId, versionId, linkId, linkState) {
        // Conditions
        let i = 0;
        const conditionsArr = [];
        while (typeof data[`conditions-array${i}`] !== 'undefined') {
            const condition = {};

            condition.tcond_variable =
                typeof data[`conditions-array${i}`].value !== 'undefined'
                    ? data[`conditions-array${i}`].value
                    : data[`conditions-array${i}`];
            condition.tcond_value = data[`conditions-condition${i}`];
            condition.tcond_operator =
                typeof data[`conditions-operator${i}`].value !== 'undefined'
                    ? data[`conditions-operator${i}`].value
                    : data[`conditions-operator${i}`];
            condition.type = data[`conditions-array${i}`].type;

            conditionsArr.push(condition);

            i++;
        }

        let linkType;
        if (data.ttasklink_else == 'COND') {
            linkType = data.ttasklink_type;
        } else if (data.ttasklink_else == 'ELSE') {
            linkType = 'ELSE';
        } else {
            linkType = 'AND';
        }

        const obj = {
            tproc_id: templateId,
            tproc_version: versionId,
            ttasklink_id: !isNaN(linkId) ? Number(linkId) : linkId,
            ttasklink_is_mandatory:
                data.ttasklink_is_mandatory == true ? 'Y' : 'N',
            ttasklink_priority: data.ttasklink_priority,
            ttasklink_type: linkType,
            ttasklink_from_ttask_id: linkState.ttasklink_from_ttask_id,
            ttasklink_to_ttask_id: linkState.ttasklink_to_ttask_id,
        };

        if (data.ttasklink_else == 'COND') {
            obj.conditions = conditionsArr;
        }

        obj.ttasklink_else = data.ttasklink_else;

        return obj;
    },
};
