import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import { guid } from '../common/utils';
import { TextArea } from '../components/form/textArea.react';
import { Text } from '../components/form/text.react';
import { CodeArea } from '../components/form/codeArea.react';
import TabsButton from '../components/tabs/tabsButton.react';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';
import React from 'react';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import MainButton from '../components/tabs/mainButton.react';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import CronRepeat from '../components/form/cronRepeat.react';
import Form from '../components/form/form.react';
import AlertsActions from '../components/alerts/alerts.actions';
import ConfirmModal from './modals/confirmModal.react';

class AdministrationCron extends React.Component {

    constructor(props) {
        super();
        this.state = {
            formIsPristine: true,
            cron: {},
            loading: true,
            confirmModalIsOpen: false,
            resetCronModalIsOpen: false,
            formKey: 0, // due to cron resetting after editing the form without saving
        };

        this.watchFormBeforeLeave = this.watchFormBeforeLeave.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.save = this.save.bind(this);
        this.postData = this.postData.bind(this);
        this.getTimeout = this.getTimeout.bind(this);
        this.getFormat = this.getFormat.bind(this);
        this.goBack = this.goBack.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.loadCron = this.loadCron.bind(this);
        this.resetToDefault = this.resetToDefault.bind(this);
        this.openCronResetModal = this.openCronResetModal.bind(this);
        this.closeCronResetModal = this.closeCronResetModal.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin
        PageRights.checkUserRights([-1]);
    }

    componentDidMount() {
        const { cronId } = this.props.match.params;

        this.loadCron();
        this.watchFormBeforeLeave(cronId);
    }

    componentWillUnmount() {
        this.unlistenForm();
    }

    loadCron() {
        const { cronId } = this.props.match.params;

        ApiRequest.get(`/crons/${cronId}`)
            .then((payload) => {
                this.setState({
                    loading: false,
                    cron: payload,
                });

                BreadcrumbActions.changeBread([
                    {
                        name: i18next.t('administration'),
                        to: '/administration-menu',
                    },
                    { name: i18next.t('crons'), to: '/administration/crons' },
                    {
                        name: `${i18next.t('cron')} ${payload.cron_name}`,
                        to: `/administration/crons/${cronId}`,
                    },
                ]);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });
    }

    watchFormBeforeLeave(cronId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                if (
                    !this.state.formIsPristine &&
                    location.pathname !== `/administration/crons/cron/${cronId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = (data) => {
                        this.postData(data, callback);
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.formCron.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        this.props.history.push('/administration/crons');
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    save(e) {
        e.preventDefault();

        this.onFormAction = (data) => {
            this.postData(data);
        };

        this.formCron.submit();
    }

    getTimeout(time) {
        const timeout = time.split(':');
        const hours = Number(timeout[0]);
        const minutes = Number(timeout[1]);
        const seconds = Number(timeout[2]);

        return (hours * 3600 + minutes * 60 + seconds) * 1000; // ms
    }

    getFormat(timeout) {
        function leadingZero(num) {
            return (num < 10 ? '0' : '') + num;
        }

        if (timeout) {
            let time = timeout / 1000; // seconds
            const hours = Math.floor(time / 3600);
            time -= hours * 3600;
            const minutes = Math.floor(time / 60);
            time -= minutes * 60;
            const seconds = time;

            return `${leadingZero(hours)}:${leadingZero(minutes)}:${leadingZero(seconds)}`;
        }
    }

    postData(data, callback) {
        if (this.refs.repeats.state.isValid) {
            const { cronId } = this.props.match.params;
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: i18next.t('alrSaving'),
            });
            let timeout = null;
            if (data.cron_timeout && data.cron_timeout !== '00:00:00') {
                timeout = this.getTimeout(data.cron_timeout);
            }

            let cronParams = null;
            if (data.cronParams) {
                try {
                    cronParams = JSON.stringify(JSON.parse(data.cronParams));
                } catch (e) {
                    return AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: e,
                        show: true,
                    });
                }
            }
            const arr = [
                data.inputSeconds,
                data.inputMinutes,
                data.inputHours,
                data.inputDays,
                data.inputMonths,
                data.inputWeeks,
            ];
            const obj = {
                items: [
                    {
                        cron_name: data.cron_name,
                        cron_syntax: arr.join(' '),
                        cron_parameters: cronParams,
                        id: cronId,
                        cron_timeout: timeout,
                        cron_alias: data.cron_alias,
                        cron_description: data.cron_description,
                    },
                ],
            };
            ApiRequest.post('/crons', JSON.stringify(obj))
                .then((payload) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrCronUpdated'),
                    });

                    this.setState({ formIsPristine: true }, () => {
                        if (callback) {
                            callback();
                        } else {
                            this.goBack();
                        }
                    });
                })
                .catch((errorMessage) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrCronUpdateFailed'),
                        serverError: errorMessage,
                    });
                });
        } else {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillDataInRightFormat'),
                show: true,
                allowCountdown: true,
            });
        }
    }

    resetToDefault(e, rowId, row) {
        e?.preventDefault();

        this.closeCronResetModal();

        const alertId = guid();
        const { cron } = this.state;

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = {
            items: [
                {
                    id: cron.id,
                    cron_file: cron.cron_file, // even for a clone, we want the default settings for the original cron (i. e. based on the file name - cron_file)
                    cron_is_clone: cron.cron_is_clone, // if cron is clone, it's alias will be reset to null (non-null alias has to be unique)
                },
            ],
        };

        ApiRequest.post('/crons/default', JSON.stringify(obj))
            .then(() => {
                this.setState((prevState) => ({
                    formKey: prevState.formKey + 1,
                }));

                this.loadCron();

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrCronReset'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrCronResetFailed'),
                    serverError: errorMessage,
                });
            });
    }

    openCronResetModal(e) {
        e?.preventDefault();

        this.setState({
            resetCronModalIsOpen: true,
        });
    }

    closeCronResetModal() {
        this.setState({ resetCronModalIsOpen: false });
    }

    render() {
        let cronParams;
        if (this.state.cron.cron_parameters) {
            cronParams = JSON.stringify(
                JSON.parse(this.state.cron.cron_parameters),
                null,
                4,
            );
        }

        const cronIsClone = this.state.cron.cron_is_clone === 'Y';

        return (
            <DocumentTitle title={i18next.t('administration')}>
                <TabsWrapper>
                    <Heading title={i18next.t('administration')}>
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                enableOn={!this.state.loading}
                                onClick={this.save}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="administrationCrons"
                            title={`${i18next.t('crons')}`}
                            tabLink="/administration/crons"
                            name="tabAdministrationCrons"
                        >
                            <TabsButtonsOther>
                                <TabsButton
                                    key="reset"
                                    icon="icon-sync-2"
                                    onClick={this.openCronResetModal}
                                    tooltipCode="ttResetCron"
                                    enableOn={!this.state.loading}
                                >
                                    {i18next.t('factorySettings')}
                                </TabsButton>
                            </TabsButtonsOther>
                            <Form
                                key={`form-${this.state.formKey}`}
                                ref={(c) => (this.formCron = c)}
                                name="formCron"
                                onValidSubmit={this.handleValidSubmit}
                                onChange={this.formChanged}
                            >
                                {this.state.cron.cron_parameters && (
                                    <CodeArea
                                        key="cronParams"
                                        ref="cronParams"
                                        name="cronParams"
                                        language="json"
                                        label={`${i18next.t('params')}:`}
                                        height="500px"
                                        side="left"
                                        value={cronParams}
                                        fullLabel
                                        upperLabel
                                    />
                                )}
                                {this.state.cron.cron_parameters &&
                                    this.state.cron.cron_help && (
                                    <div
                                        key="help"
                                        className="cron-help"
                                        dangerouslySetInnerHTML={{
                                            __html: this.state.cron
                                                .cron_help,
                                        }}
                                        side="right"
                                    />
                                )}
                                {cronIsClone && (
                                    <Text
                                        key="cron_name"
                                        label={`${i18next.t('name')}:`}
                                        value={this.state.cron.cron_name}
                                    />
                                )}
                                {cronIsClone && (
                                    <Text
                                        key="cron_alias"
                                        label={`${i18next.t('alias')}:`}
                                        value={this.state.cron.cron_alias}
                                    />
                                )}
                                {cronIsClone && (
                                    <TextArea
                                        key="cron_description"
                                        label={`${i18next.t('description')}:`}
                                        value={this.state.cron.cron_description}
                                        rows={2}
                                    />
                                )}
                                <CronRepeat
                                    key="repeats"
                                    ref="repeats"
                                    label={`${i18next.t('plnOffType')}:`}
                                    side="center"
                                    value={this.state.cron.cron_syntax}
                                />
                                <Text
                                    key="cron_timeout"
                                    label={`${i18next.t('timeoutHMS')}:`}
                                    value={
                                        this.getFormat(
                                            this.state.cron.cron_timeout,
                                        ) || '00:00:00'
                                    }
                                    validations={{
                                        matchesRegular:
                                            /(([0-1][0-9])|([2][0-3])):([0-5][0-9]):([0-5][0-9])$/,
                                    }}
                                    validationErrors={{
                                        matchesRegular:
                                            i18next.t('notInRightormat'),
                                    }}
                                    side="left"
                                />
                            </Form>
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                    <ConfirmModal
                        isOpen={this.state.resetCronModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmResetCronDialog')}
                        onClose={this.closeCronResetModal}
                        onConfirm={this.resetToDefault}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

AdministrationCron.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default AdministrationCron;
