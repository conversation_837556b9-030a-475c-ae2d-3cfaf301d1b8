import React from 'react';
import _ from 'lodash';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { Chart, registerables } from 'chart.js';
import { Rnd } from 'react-rnd';
import ChartDataLabels from '../../assets/libs/chartjs-plugin-datalabels/chartjs-plugin-datalabels.esm';
import {
    guid, checkLangMutation, checkVarLovLangMutation, hexToRgba, getFavouriteNamesObj,
} from '../common/utils';
import { SelectBox } from '../components/form/selectBox.react';
import ReportTableModal from './modals/reportTableModal.react';
import MenuStore from '../flux/menu.store';
import ReportsStore from '../flux/reports.store';
import ReportsActions from '../flux/reports.actions';
import AltManagerMixin from '../flux/altManagerMixin';
import ReportsLayout from '../components5.0/sections/reports/ReportsLayout';
import browserHistory from '../common/history';
import ApiRequest from '../api/apiRequest';

import cx from 'classnames';
import createReactClass from 'create-react-class';
import Loader from 'react-loader';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import Form from '../components/form/form.react';
import PrintChartModal from './modals/printChartModal.react';

const Reports = createReactClass({
    displayName: i18next.t('statistics'),

    mixins: [AltManagerMixin],

    statics: {
        registerStore: ReportsStore,
        registerAction: ReportsActions,
    },

    getInitialState() {
        return _.extend(
            {
                reportId: null,
                graphId: null,
                copyModalGraphIsOpen: false,
                loadingGraph: false,
                graphLoaded: false,
                switchByCol: null,
                switcherCols: [],
                printChartModalState: false,
                tableModalIsOpen: false,
                cvIds: [],
                panelMaximalized: true,
                panelWidth: '320px',
                panelHeight: 'auto',
                globalFilters: [],
                foldersLoading: true,
                favNamesObj: {},
            },
            MenuStore.getState(),
        );
    },

    handleResize(event) {
        setTimeout(() => {
            this.rnd?.updatePosition({
                x: window.innerWidth - 320 - 56,
                y: 176,
            });
        });
    },

    componentDidMount() {
        // set breadcrumb
        BreadcrumbActions.changeBread({ name: i18next.t('statistics') });

        i18next.on('languageChanged', (options) => {
            BreadcrumbActions.changeBread.defer({
                name: i18next.t('statistics'),
            });
        });

        MenuStore.listen(this._onChangeMenu);
        window.addEventListener('resize', this.handleResize);

        // function is called from case overview
        window.showProcessInfo = (caseId) => {
            this.props.history.push(`/cases/case/${caseId}`);
        };
        // Register the components with Chart.js
        Chart.register(...registerables);
    },

    componentWillUnmount() {
        // reset tprocId for new case modal in main menu
        // MenuActions.setHeaderId(null);

        window.removeEventListener('resize', this.handleResize);

        if (this.graphRequest) {
            this.graphRequest.cancel();
        }
        if (this.chartRequest) {
            this.chartRequest.cancel();
        }
        if (this.globalFilterRequest) {
            this.globalFilterRequest.cancel();
        }

        if (this.reportChart) {
            this.reportChart.destroy(); // destroy any instance
        }

        this.store?.unlisten(this._onChange);
    },

    _onChange() {
        this.setState(this.store?.getState());
    },

    _onChangeMenu() {
        this.setState(MenuStore.getState());
    },

    // TODO: 5.0 tree
    loadTreeGraphData(afterDelete) {
        this.setState({ foldersLoading: true });
        const isUrlCvId = typeof this.props.match.params[0] !== 'undefined';
        let urlCvId = null;

        ApiRequest.get(`/report-graphs/mine?limit=${config.restLimit}`)
            .then((payload) => {
                this.setState(
                    {
                        cvList: payload.items,
                    },
                    () => {
                        if (isUrlCvId) {
                            urlCvId = this.props.match.params[0];
                            this.loadData(urlCvId);
                        }
                        // else if (selectedNodeId && selectedNodeId !== 'root') {
                        //     this.loadData(selectedNodeId);
                        // }

                        // // find folder or create it if not exist
                        // var findCreateFolder = (tree, path) => {
                        //     if (tree) {
                        //         var found = tree;
                        //         if (path.length > 0) {
                        //             var folder = path.shift();
                        //             found = _.find(tree, { label: folder, children: [] });
                        //             if (!found) {
                        //                 // return created folder
                        //                 found = tree[tree.push({ id: folder, label: folder, children: [] }) - 1];
                        //             }
                        //             found = findCreateFolder(found.children, path);
                        //         }
                        //         return found;
                        //     }
                        // };

                        // var treeArr = [];
                        // const flatArr = [];
                        // payload.items.forEach(item => {
                        //     var arr = checkLangMutation(item, 'graph_name').split('/');
                        //     var reportName = arr.pop();
                        //
                        //     var curFolder = findCreateFolder(treeArr, arr);
                        //     if (curFolder) {
                        //         curFolder.push({
                        //             id: item.id,
                        //             label: reportName,
                        //             ownerClass: LoggedUserStore.getState().userName === item.graph_owner_user_name ? 'my-node' : 'shared-node',
                        //             selected: urlCvId == item.id,
                        //         });
                        //     }
                        //
                        //     flatArr.push({
                        //         id: item.id,
                        //         name: checkLangMutation(item, 'graph_name'),
                        //         ownerClass: LoggedUserStore.getState().userName === item.graph_owner_user_name ? 'my-node' : 'shared-node',
                        //     });
                        // });

                        // finishTree(isUrlCvId, urlCvId, treeArr, flatArr, tree, this.treeEditable);
                        //
                        // tree.loadTree(treeArr);

                        // Tree 5.0
                        const tree5Arr = payload.items.map((item) => ({
                            id: item.id,
                            label: checkLangMutation(item, 'graph_name'),
                            owner: item.graph_owner_user_name,
                        }));

                        if (afterDelete) {
                            this.afterDeleteGraph(tree5Arr);
                        } else {
                            this.setState({
                                foldersLoading: false,
                                folderTree: tree5Arr,
                            });
                        }
                    },
                );
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTreeDataFailed'),
                    serverError: errorMessage,
                });
            });
    },

    treeEditable(tree, nodeId) {
        if (nodeId !== null && !isNaN(nodeId)) {
            tree.setActiveEditButtons(true);
        } else {
            tree.setActiveEditButtons(false);
        }
    },

    loadData(id) {
        this.loadGlobalFilters(id);
    },

    loadGraph(id, parentId, tree, switchValue) {
        if (this.graphRequest) {
            this.graphRequest.cancel();
        }
        if (this.chartRequest) {
            this.chartRequest.cancel();
        }

        if (!isNaN(id)) {
            this.props.history.push(`/reports/graphs/${id}`);

            this.setState({
                graphId: id,
                loadingGraph: true,
                graphLoaded: false,
            });

            this.graphRequest = ApiRequest.get(`/report-graphs/${id}`)
                .then(async (reportGraph) => {
                    if (typeof reportGraph !== 'undefined') {
                        const favNamesObj = await getFavouriteNamesObj(reportGraph, 'statistics', 'graph_name');
                        const graphName = checkLangMutation(reportGraph, 'graph_name');
                        const axisLabels = {
                            x: checkLangMutation(reportGraph, 'graph_x_label'),
                            y: checkLangMutation(reportGraph, 'graph_y_label'),
                        };
                        BreadcrumbActions.changeBread([
                            {
                                name: i18next.t('statistics'),
                                to: location.pathname,
                            },
                            { name: `${i18next.t('graph')} - ${graphName}` },
                        ]);

                        let globalStaticFilterIds = [];
                        let globalDynamicFilterIds = [];

                        globalStaticFilterIds = this.state.activeGlobalStaticFilters.map(
                            (filter) => filter?.value,
                        );
                        globalDynamicFilterIds = this.state.activeGlobalDynamicFilters.map(
                            (filter) => {
                                if (filter) {
                                    return {
                                        id: filter.id,
                                        value: filter.value,
                                        title: filter.title,
                                    };
                                }
                            },
                        );

                        const postData = {
                            globalFilter: globalStaticFilterIds,
                            globalDynamicFilter: globalDynamicFilterIds,
                        };

                        const meta = JSON.parse(reportGraph.graph_meta || '{}');

                        // get current y axis (valueCol) tvar_meta (of only non-system variables) for bar-groups graphs
                        // (mainly to keep tvar_meta.numberOfDecimals consistent with the actual variable)
                        if (
                            reportGraph.graph_type === 'bar-groups' &&
                            meta.axisY.valueCol.tproc_id
                        ) {
                            ApiRequest.get(
                                `/template-processes/${meta.axisY.valueCol.tproc_id}/1/template-variables/${meta.axisY.valueCol.id}`,
                            )
                                .then((payload) => {
                                    meta.axisY.valueCol.tvar_meta = payload.tvar_meta;
                                })
                                .catch((errorMessage) => {
                                    AlertsActions.addAlert({
                                        type: 'alert',
                                        message: i18next.t('alrFailedGraphData'),
                                        serverError: errorMessage,
                                    });
                                });
                        }

                        this.chartRequest = ApiRequest.post(
                            `/report-graphs/${id}/chart`,
                            JSON.stringify(postData),
                        )
                            .then((payload) => {
                                const isGroups = reportGraph.graph_type === 'bar-groups';
                                const isSeries = reportGraph.graph_data_type === 'S';

                                const cvIds = (payload.items || payload).map((item, i) => {
                                    return item.cv_id;
                                });

                                this.setState({
                                    loadingGraph: false,
                                    graphName: graphName,
                                    cvId: cvIds[0], // TODO - pokud by body byly z více různých přehledů
                                    cvIds: cvIds,
                                    graphLoaded: true,
                                    isGroups: isGroups,
                                    graphOwner: {
                                        id: reportGraph.graph_user_id,
                                        name: reportGraph.graph_owner_name,
                                    },
                                    graphDataType: reportGraph.graph_data_type,
                                    // dataSeriesValues: isSeries ? (payload.items || payload)[0].data_series_values : null,
                                    favNamesObj: favNamesObj,
                                });

                                let labels = [];
                                let colors = [];
                                let sortedPoints = payload.items || payload;

                                if (meta.order === 'asc') {
                                    sortedPoints = _.orderBy(
                                        sortedPoints,
                                        'point_value',
                                        'asc',
                                    );
                                } else if (meta.order === 'desc') {
                                    sortedPoints = _.orderBy(
                                        sortedPoints,
                                        'point_value',
                                        'desc',
                                    );
                                }

                                if (this.reportChart) {
                                    this.reportChart.destroy(); // destroy previous instance
                                }

                                if (isGroups) {
                                    const switcherCols = [];
                                    const groups = [];
                                    const groupsLabels = [];
                                    colors = meta.axisY.colors || [];

                                    sortedPoints.forEach((point, p) => {
                                        let switcherCol;
                                        if (meta.axisX.switchByCol) {
                                            switcherCol = checkLangMutation(
                                                point,
                                                `v${meta.axisX.switchByCol.value}`,
                                            );
                                            const toBeInserted = {
                                                value: switcherCol,
                                                title: switcherCol,
                                            };
                                            // global filter
                                            if (
                                                !_.find(
                                                    switcherCols,
                                                    toBeInserted,
                                                )
                                            ) {
                                                switcherCols.push(toBeInserted);
                                            }
                                        } else {
                                            switcherCol = p;
                                        }
                                        // datasets - required
                                        const datasetLabel = checkLangMutation(
                                            point,
                                            `v${meta.axisX.dataCol.value}`,
                                        );
                                        if (labels.indexOf(datasetLabel) === -1) {
                                            labels.push(datasetLabel);
                                        }
                                        // values - required
                                        let groupValue;
                                        if (meta.axisX.groupByCol) {
                                            groupValue = checkLangMutation(
                                                point,
                                                `v${meta.axisX.groupByCol.value}`,
                                            );
                                            if (groupsLabels.indexOf(groupValue) === -1) {
                                                groupsLabels.push(groupValue);
                                            }
                                        } else {
                                            groupValue = datasetLabel;
                                        }

                                        const numberOfDecimals = meta.axisY.valueCol.tvar_meta
                                            ? JSON.parse(meta.axisY.valueCol.tvar_meta)
                                                .numberOfDecimals || null
                                            : null;

                                        let value = checkLangMutation(
                                            point,
                                            `v${meta.axisY.valueCol.value}`,
                                        );

                                        if (numberOfDecimals !== null) {
                                            value = parseFloat(value).toFixed(numberOfDecimals);
                                        }

                                        const groupX = {
                                            switcher: switcherCol,
                                            datasetLabel: datasetLabel,
                                            label: groupValue,
                                            value: value,
                                        };

                                        const globalFilter =
                                            switchValue || (switcherCols[0] || {}).value;
                                        if (meta.axisX.switchByCol) {
                                            if (
                                                !_.find(groups, groupX) &&
                                                switcherCol === globalFilter
                                            ) {
                                                groups.push(groupX);
                                            }
                                        } else if (
                                            !_.find(groups, groupX) &&
                                            switcherCol === p
                                        ) {
                                            groups.push(groupX);
                                        }
                                    });

                                    // Doplneni chybejicich dat
                                    groupsLabels.map((gl) => {
                                        labels.map((dl) => {
                                            const toBeInserted = {
                                                datasetLabel: dl,
                                                label: gl,
                                            };

                                            if (
                                                !_.find(
                                                    groups,
                                                    toBeInserted,
                                                )
                                            ) {
                                                toBeInserted.value = 0;
                                                groups.push(toBeInserted);
                                            }
                                        });
                                    });
                                    //

                                    let graphData;
                                    let sortedGroupsLabels;
                                    if (_.isEmpty(groupsLabels)) {
                                        labels = [];
                                        graphData = [];
                                        groups.map((g) => {
                                            graphData.push(g.value);
                                            labels.push(g.label);
                                        });
                                    } else {
                                        const sortedGroups = _.sortBy(
                                            groups,
                                            ['label', 'datasetLabel'],
                                        );
                                        sortedGroupsLabels = _.uniq(
                                            _.map(sortedGroups, 'label'),
                                        );
                                        graphData = {};
                                        labels.map((label) => {
                                            const group = [];
                                            sortedGroups.map((g) => {
                                                if (g.datasetLabel === label) {
                                                    group.push(g.value);
                                                }
                                            });
                                            if (typeof graphData[label] === 'undefined') {
                                                graphData[label] = [];
                                            }
                                            graphData[label] = group;
                                        });
                                    }

                                    this.setState({
                                        switcherCols: switcherCols,
                                        switchByCol: switchValue || (switcherCols[0] || {}).value,
                                    });
                                    this.createGroupsChart(
                                        graphData,
                                        sortedGroupsLabels,
                                        labels,
                                        colors,
                                        graphName,
                                        axisLabels,
                                        reportGraph.graph_show_val_labels,
                                    );
                                } else if (isSeries) {
                                    const graphData = [];
                                    const pointMeta = JSON.parse(
                                        sortedPoints[0].point_meta || '{}',
                                    );
                                    colors = pointMeta.colors || [];
                                    const groupColumns = JSON.parse(sortedPoints[0].group_columns);

                                    sortedPoints[0]?.data_series_values?.forEach((seriesValue) => {
                                        const pointValue = seriesValue.value || 0;
                                        const label = checkLangMutation(seriesValue, 'label');
                                        const labelArr = label.split('-');
                                        const trLabelArr = [];

                                        // translate MONTH and WEEKDAY
                                        labelArr.forEach((item, i) => {
                                            if (groupColumns[i]?.datepart === 'MONTH') {
                                                const value =
                                                    item === ''
                                                        ? i18next.t('unfilled')
                                                        : i18next.t(`month${item}`);
                                                trLabelArr.push(value);
                                            } else if (groupColumns[i]?.datepart === 'WEEKDAY') {
                                                const daysTrArr = [
                                                    'mon',
                                                    'tue',
                                                    'wed',
                                                    'thu',
                                                    'fri',
                                                    'sat',
                                                    'sun',
                                                ];
                                                const value =
                                                    item === ''
                                                        ? i18next.t('unfilled')
                                                        : i18next.t(daysTrArr[item - 1]);
                                                trLabelArr.push(value);
                                            } else {
                                                const value =
                                                    item === '' ? i18next.t('unfilled') : item;
                                                trLabelArr.push(value);
                                            }
                                        });

                                        graphData.push(pointValue);
                                        labels.push(trLabelArr.join('-'));
                                    });

                                    this.createPointsChart(
                                        reportGraph,
                                        graphData,
                                        labels,
                                        colors,
                                        graphName,
                                        axisLabels,
                                    );
                                } else {
                                    const graphData = [];
                                    sortedPoints.forEach((point) => {
                                        const pointMeta = JSON.parse(point.point_meta || '{}');
                                        const pointValue =
                                            point.point_value === null ? 0 : point.point_value;

                                        graphData.push(pointValue);
                                        labels.push(checkLangMutation(point, 'point_label'));
                                        colors.push(pointMeta.color);
                                    });

                                    this.createPointsChart(
                                        reportGraph,
                                        graphData,
                                        labels,
                                        colors,
                                        graphName,
                                        axisLabels,
                                    );
                                }
                            })
                            .catch((errorMessage) => {
                                this.setState({ loadingGraph: false });

                                AlertsActions.addAlert({
                                    type: 'alert',
                                    message: i18next.t('alrFailedGraphData'),
                                    serverError: errorMessage,
                                });
                            });
                    } else {
                        this.setState({ loadingGraph: false });
                    }
                })
                .catch((errorMessage) => {
                    this.setState({ loadingGraph: false });

                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedGraphData'),
                        serverError: errorMessage,
                    });
                });
        }
    },

    loadGlobalFilters(id) {
        if (this.globalFilterRequest) {
            this.globalFilterRequest.cancel();
        }

        this.initFlux(this.props, id);
        this.store?.listen(this._onChange);
        this.setState(this.store.getState());

        this.globalFilterRequest = ApiRequest.get(`/report-graphs/${id}/global-filters`)
            .then((payload) => {
                const globalFilters = [];
                const preselectedFilters = [];
                const preselectedDynamicFilters = [];

                payload.items.forEach((item) => {
                    // preselected dynamic
                    if (item.preselected_option && typeof item.preselected_option === 'string') {
                        const preselectedObj = JSON.parse(item.preselected_option || '{}');

                        if (preselectedObj.title && preselectedObj.value) {
                            preselectedDynamicFilters.push({
                                id: item.tvar_id,
                                value: preselectedObj.title,
                                title: preselectedObj.title,
                                filterId: item.id,
                            });
                        }
                    }

                    const options = item.options.map((option) => {
                        // preselected static
                        if (option.preselected_option === 'Y') {
                            preselectedFilters.push({
                                id: item.id,
                                title: checkLangMutation(option, 'rggfo_name'),
                                value: option.rggfo_id,
                            });
                        }

                        return {
                            title: checkLangMutation(option, 'rggfo_name'),
                            value: option.rggfo_id,
                            preselected_option: option.preselected_option,
                        };
                    });

                    const filterObj = {
                        id: item.id,
                        name: checkLangMutation(item, 'rggf_name'),
                        options: options,
                        tvarId: item.tvar_id,
                        tvarType: item.tvar_type,
                        tvarAttribute: item.tvar_attribute,
                        dlistName: item.dlist_name,
                        tvarColIndex: item.tvar_col_index,
                        tvarLovs: checkVarLovLangMutation(item, 't'),
                        fillDynamically: item.fill_dynamically === 'Y',
                        preselectedOption: JSON.parse(item.preselected_option || '{}'),
                    };

                    globalFilters.push(filterObj);
                });

                const activeFilters = this.state.activeGlobalStaticFilters.filter((active) => {
                    return globalFilters.find((filter) => active?.id === filter?.id);
                });
                const activeDynamicFilters = this.state.activeGlobalDynamicFilters.filter(
                    (active) => {
                        return globalFilters.find((filter) => active?.filterId === filter?.id);
                    },
                );

                if (!this.state.filtersChangedByUser) {
                    const saveGlobalFilters = async () => {
                        await this.action.changeActiveGlobalStaticFilters(preselectedFilters);
                        await this.action.changeActiveGlobalDynamicFilters(
                            preselectedDynamicFilters,
                        );
                    };

                    saveGlobalFilters();
                }

                this.setState(
                    {
                        globalFilters: globalFilters,
                    },
                    () => {
                        this.loadGraph(id);
                    },
                );
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrGlobalFilterDownloadFailed'),
                    serverError: errorMessage,
                });
            });
    },

    getLightColor() {
        const max = 255;
        let total = 0;
        let red;
        let green;
        let blue;

        while (total < 350 || total > 700) {
            red = Math.floor(Math.random() * max + 1);
            green = Math.floor(Math.random() * max + 1);
            blue = Math.floor(Math.random() * max + 1);
            total = red + green + blue;
        }

        const redStr = red.toString(16).padStart(2, '0');
        const greenStr = green.toString(16).padStart(2, '0');
        const blueStr = blue.toString(16).padStart(2, '0');

        return `#${redStr}${greenStr}${blueStr}`;
    },

    // eslint-disable-next-line consistent-return
    createGroupsChart(
        graphData,
        groups,
        datasetsLabels,
        colors = [],
        graphName,
        axisLabels,
        showValLabels,
    ) {
        if (!this.chartCanvasRef) {
            return false;
        }

        const ctx = this.chartCanvasRef.getContext('2d');
        const data = {
            labels: _.isEmpty(groups) ? [''] : groups,
            datasets: datasetsLabels.map((dl, i) => {
                let color = colors[i];
                if (!color || color === '#ffffff') {
                    color = this.getLightColor();
                }
                return {
                    label: datasetsLabels[i],
                    backgroundColor: hexToRgba(color, 0.65),
                    borderColor: colors[i],
                    borderWidth: 1,
                    data: _.isEmpty(groups) ? [graphData[i]] : graphData[dl],
                    /* datalabels: {
                        color: color,
                    } */
                };
            }),
        };

        const options = {
            responsive: true,
            pointDotRadius: 20,
            scales: {
                x: {
                    title: {
                        display: !!axisLabels.x,
                        text: axisLabels.x,
                    },
                },

                y: {
                    title: {
                        display: !!axisLabels.y,
                        text: axisLabels.y,
                    },

                    beginAtZero: true,
                },
            },
            layout: {
                padding: {
                    top: -15,
                },
            },

            plugins: {
                title: {
                    display: true,
                    text: graphName,
                    padding: 25,
                },
                legend: {
                    display: true,
                    position: 'bottom',
                },
                tooltip: {
                    enabled: true,
                    mode: 'nearest',
                    callbacks: {
                        label: (context) => {
                            // show actual data from dataset (correct decimal notation) instead of chart data
                            const tooltipItem = Array.isArray(context) ? context[0] : context || {};
                            const dataObj = context.chart.data;
                            return dataObj.datasets[tooltipItem.datasetIndex].data[
                                tooltipItem.index
                            ];
                        },
                    },
                },
                /* datalabels: {
                    color: '#000000',
                } */
            },
        };

        this.reportChart = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: options,
            plugins: showValLabels === 'Y' ? [ChartDataLabels] : [],
        });
    },

    // eslint-disable-next-line consistent-return
    createPointsChart(
        reportGraph,
        graphData,
        graphLabels,
        graphColors = [],
        graphName,
        axisLabels,
    ) {
        if (!this.chartCanvasRef) {
            return false;
        }

        const colors = [];
        graphData.forEach((point, i) => {
            const color = graphColors[i];
            if (!color || color === '#ffffff') {
                colors[i] = this.getLightColor();
            } else {
                colors[i] = graphColors[i];
            }
        });

        let type = reportGraph.graph_type;
        const ctx = this.chartCanvasRef.getContext('2d');

        let data;

        if (type === 'pie') {
            data = {
                labels: graphLabels,
                datasets: [
                    {
                        backgroundColor: colors.map((color) => hexToRgba(color, 0.65)),
                        data: graphData,
                    },
                ],
            };
        } else if (type === 'bar') {
            data = {
                labels: graphLabels,
                datasets: [
                    {
                        label: '',
                        backgroundColor: colors.map((color) => hexToRgba(color, 0.65)),
                        borderColor: colors,
                        borderWidth: 1,
                        data: graphData,
                    },
                ],
            };
        } else if (type === 'line') {
            data = {
                labels: graphLabels,
                datasets: [
                    {
                        label: '',
                        lineTension: 0.2,
                        backgroundColor: 'rgba(0, 0, 0, 0)', // colors[0]
                        borderColor: colors,
                        pointBackgroundColor: colors,
                        data: graphData,
                        fill: false,
                    },
                ],
            };
        } else if (type === 'dot') {
            type = 'line';
            data = {
                labels: graphLabels,
                datasets: [
                    {
                        label: '',
                        showLine: false,
                        backgroundColor: 'rgba(0, 0, 0, 0)', // colors[0]
                        borderColor: colors,
                        pointBackgroundColor: colors,
                        data: graphData,
                        fill: false,
                    },
                ],
            };
        }

        const options = {
            responsive: true,
            pointDotRadius: 20,

            layout: {
                padding: {
                    top: -15,
                },
            },

            plugins: {
                title: {
                    display: true,
                    text: graphName,
                    padding: 25,
                },
                legend: {
                    display: false,
                },
                tooltip: {
                    enabled: true,
                    mode: 'nearest',
                },
                datalabels: {
                    align: 'center',
                    anchor: 'center',
                },
            },
        };

        if (type !== 'pie') {
            options.scales = {
                x: {
                    title: {
                        display: !!axisLabels.x,
                        text: axisLabels.x,
                    },
                    grid: {
                        display: type !== 'bar',
                    },
                },

                y: {
                    title: {
                        display: !!axisLabels.y,
                        text: axisLabels.y,
                    },

                    beginAtZero: true,
                },
            };
        }

        if (type === 'pie') {
            options.plugins.legend = {
                display: true,
                position: 'bottom',
            };
        }

        if (type === 'line' || type === 'dot') {
            options.plugins.datalabels = {
                align: 'end',
                anchor: 'end',
            };
        }

        this.reportChart = new Chart(ctx, {
            type: type,
            data: data,
            options: options,
            plugins: reportGraph.graph_show_val_labels === 'Y' ? [ChartDataLabels] : [],
        });
    },

    copyGraph(id) {
        const alertId = guid();
        // var graphId = this.state.graphId;
        const graphId = id;

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrCopying'),
        });

        ApiRequest.post(`/report-graphs/${graphId}/clone`, JSON.stringify({})) // empty object due to IE
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrCvCopied'),
                });

                if (payload.id) {
                    this.props.history.push(`/reports/graphs/settings/${payload.id}`);
                }
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrCopyGraphFailed'),
                    serverError: errorMessage,
                });
            });
    },

    switchGlobalFilter(name, value, filterId) {
        const activeFiltersClone = this.state.activeGlobalStaticFilters.filter((active) => {
            return this.state.globalFilters.find((filter) => active?.id === filter?.id);
        });
        const activeDynamicFiltersClone = this.state.activeGlobalDynamicFilters.filter((active) => {
            return this.state.globalFilters.find((filter) => active?.filterId === filter?.id);
        });

        // ['tvar', '25048', 'DL'] or ['filter', '293']
        const keyArr = name.split('-');

        let newValue;
        if (value.title) {
            newValue = {
                id: Number(keyArr[1]),
                value: value.value,
                title: value.title,
            };
        } else {
            newValue = null;
        }

        if (keyArr[0] === 'tvar') {
            const foundIndex = activeDynamicFiltersClone.findIndex((filter) => {
                return filter?.id === Number(keyArr[1]);
            });

            if (newValue) {
                newValue.filterId = filterId;
            }

            if (newValue && keyArr[2] !== 'LD') {
                newValue.value = value.title;
            }

            if (foundIndex >= 0) {
                if (newValue) {
                    activeDynamicFiltersClone[foundIndex] = newValue;
                } else {
                    activeDynamicFiltersClone.splice(foundIndex, 1);
                }
            } else if (newValue) {
                activeDynamicFiltersClone.push(newValue);
            }
        } else {
            const foundIndex = activeFiltersClone.findIndex((filter) => {
                return filter?.id === Number(keyArr[1]);
            });

            if (foundIndex >= 0) {
                if (newValue) {
                    activeFiltersClone[foundIndex] = newValue;
                } else {
                    activeFiltersClone.splice(foundIndex, 1);
                }
            } else if (newValue) {
                activeFiltersClone.push(newValue);
            }
        }

        const saveGlobalFilters = async () => {
            await this.action.setFiltersChangedByUser(true);
            await this.action.changeActiveGlobalStaticFilters(activeFiltersClone);
            await this.action.changeActiveGlobalDynamicFilters(activeDynamicFiltersClone);
        };

        saveGlobalFilters();
        this.loadGraph(this.state.graphId);
    },

    switchGlobalFilterBarGroups(name, value) {
        this.loadGraph(this.state.graphId, null, null, value.value); // second and third parameter skipped due to treeClick
    },

    afterDeleteGraph(tree5Arr) {
        if (this.reportChart) {
            this.reportChart.destroy(); // destroy any instance
        }

        this.setState({
            foldersLoading: false,
            folderTree: tree5Arr,
            graphId: null,
            graphLoaded: false,
            isGroups: false,
            switcherCols: [],
        });
    },

    printGraph(e) {
        e?.preventDefault();
        this.setState({
            printChartModalState: true,
        });
    },

    closePrintModal() {
        this.setState({
            printChartModalState: false,
        });
    },

    openTableModal(e) {
        if (e) e.preventDefault();
        this.setState({
            tableModalIsOpen: true,
        });
    },

    closeTableModal(e) {
        if (e) e.preventDefault();
        this.setState({
            tableModalIsOpen: false,
        });
    },

    handleMinMax(e) {
        this.setState({
            panelMaximalized: !this.state.panelMaximalized,
            height: this.state.panelMaximalized ? '45px' : 'auto',
        });
    },

    treeClick(id) {
        if (id) {
            browserHistory.push(`/reports/graphs/${id}`);
            this.setState(
                {
                    globalFilters: [],
                    isGroups: false,
                },
                () => {
                    this.loadData(id);
                },
            );
        }
    },

    render() {
        const isGroupsGlobalFilter = this.state.isGroups && !_.isEmpty(this.state.switcherCols);
        const {
            graphId, graphName, cvId, globalFilters,
        } = this.state;

        return (
            <DocumentTitle title={i18next.t('statistics')}>
                <>
                    <ReportsLayout
                        graphId={graphId}
                        graphLoaded={this.state.graphLoaded}
                        loadGraph={this.loadGraph}
                        openTableModal={this.openTableModal}
                        printGraph={this.printGraph}
                        copyGraph={this.copyGraph}
                        loadTreeGraphData={this.loadTreeGraphData}
                        foldersLoading={this.state.foldersLoading}
                        folderTree={this.state.folderTree}
                        treeClick={this.treeClick}
                        favNamesObj={this.state.favNamesObj}
                    >
                        <Loader loaded={!this.state.loadingGraph} />
                        <div
                            ref={(r) => {
                                this.graphContainerRef = r;
                            }}
                            style={{
                                width: '100%',
                                height: '100%',
                            }}
                        >
                            <div
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '100%',
                                    height: '100%',
                                    padding: '0.75rem',
                                }}
                            >
                                <canvas
                                    key={`graph-${graphId}`}
                                    id={`graph-${graphId}`}
                                    ref={(r) => {
                                        this.chartCanvasRef = r;
                                    }}
                                />
                            </div>

                            {((this.state.graphDataType === 'S' && !_.isEmpty(globalFilters)) ||
                                isGroupsGlobalFilter) && (
                                <Rnd
                                    ref={(c) => {
                                        this.rnd = c;
                                    }}
                                    className="rnd-wrapper"
                                    dragHandleClassName="bpp-properties-tab-bar"
                                    default={{
                                        x: this.graphContainerRef?.clientWidth - 320,
                                        y: 0,
                                        width: '320px',
                                        height: 'auto',
                                    }}
                                    minHeight="45px"
                                    minWidth="150px"
                                    enableResizing={false}
                                    bounds="#main-content"
                                    style={{
                                        borderRadius: '12px',
                                    }}
                                >
                                    <div
                                        id="js-properties-panel"
                                        key="js-properties-panel"
                                        className="reports-panel"
                                    >
                                        <div
                                            className="bpp-properties-panel"
                                            style={{
                                                overflow: !this.state.panelMaximalized
                                                    ? 'hidden'
                                                    : 'visible',
                                            }}
                                        >
                                            <div className="bpp-properties">
                                                <div
                                                    id="report-panel-tab-bar"
                                                    className={`bpp-properties-tab-bar ${this.state.panelMaximalized ? 'tab-bar-border' : ''}`}
                                                >
                                                    <div className="group-header">
                                                        <span className="group-label">
                                                            {i18next.t('globalFilters')}
                                                        </span>
                                                    </div>
                                                    <span
                                                        className={
                                                            this.state.panelMaximalized
                                                                ? 'icon-scale-tool-2 icon'
                                                                : 'icon-scale-tool-3 icon'
                                                        }
                                                        onClick={this.handleMinMax}
                                                    />
                                                </div>
                                                <div
                                                    className={cx('bpp-properties-tabs-container', {
                                                        hide: !this.state.panelMaximalized,
                                                    })}
                                                >
                                                    <div className="bpp-properties-group">
                                                        <Form
                                                            key="globalFilter"
                                                            name="globalFilter"
                                                            ref={(r) => {
                                                                this.globalFilterFormRef = r;
                                                            }}
                                                            oneColumn
                                                        >
                                                            {globalFilters.map((filter) => {
                                                                let selectBoxType = null;
                                                                let { options } = filter;
                                                                let defaultValue;
                                                                let name = `filter-${filter.id}`;

                                                                if (!_.isEmpty(filter.tvarLovs)) {
                                                                    options = filter.tvarLovs;
                                                                }

                                                                if (filter.tvarType === 'DL') {
                                                                    selectBoxType =
                                                                        filter.tvarType +
                                                                        filter.tvarAttribute;
                                                                } else {
                                                                    selectBoxType = filter.tvarType;
                                                                }

                                                                if (filter.fillDynamically) {
                                                                    name = `tvar-${filter.tvarId}-${filter.tvarType}`;

                                                                    defaultValue =
                                                                        this.state.activeGlobalDynamicFilters.find(
                                                                            (active) => {
                                                                                return (
                                                                                    active?.filterId ===
                                                                                    filter.id
                                                                                );
                                                                            },
                                                                        );
                                                                } else {
                                                                    defaultValue =
                                                                        this.state.activeGlobalStaticFilters.find(
                                                                            (active) => {
                                                                                return (
                                                                                    active?.id ===
                                                                                    filter.id
                                                                                );
                                                                            },
                                                                        );
                                                                }

                                                                return (
                                                                    <SelectBox
                                                                        key={`filter-${filter.id}`}
                                                                        name={name}
                                                                        label={`${filter.name}:`}
                                                                        options={options}
                                                                        onChange={(name, value) =>
                                                                            this.switchGlobalFilter(
                                                                                name,
                                                                                value,
                                                                                filter.id,
                                                                            )}
                                                                        value={
                                                                            defaultValue &&
                                                                            !_.isEmpty(defaultValue)
                                                                                ? defaultValue.value
                                                                                : null
                                                                        }
                                                                        defaultValue={
                                                                            defaultValue &&
                                                                            !_.isEmpty(defaultValue)
                                                                                ? defaultValue.title
                                                                                : null
                                                                        }
                                                                        upperLabel
                                                                        selectBoxType={
                                                                            selectBoxType
                                                                        }
                                                                        dynTableId={
                                                                            filter.dlistName
                                                                        }
                                                                        colIndex={
                                                                            filter.tvarColIndex
                                                                        }
                                                                    />
                                                                );
                                                            })}
                                                            {isGroupsGlobalFilter && (
                                                                <SelectBox
                                                                    key="switchByCol"
                                                                    side="right"
                                                                    value={this.state.switchByCol}
                                                                    options={
                                                                        this.state.switcherCols
                                                                    }
                                                                    nullable={false}
                                                                    onChange={
                                                                        this
                                                                            .switchGlobalFilterBarGroups
                                                                    }
                                                                />
                                                            )}
                                                        </Form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Rnd>
                            )}
                        </div>
                    </ReportsLayout>
                    {this.state.printChartModalState && (
                        <PrintChartModal
                            width="medium"
                            isOpen={this.state.printChartModalState}
                            onClose={this.closePrintModal}
                            graphId={graphId}
                            reportName={graphName}
                            cvId={cvId}
                        />
                    )}
                    {this.state.tableModalIsOpen && (
                        <ReportTableModal
                            width="large"
                            isOpen={this.state.tableModalIsOpen}
                            onClose={this.closeTableModal}
                            graphId={graphId}
                            isGroups={this.state.isGroups || false}
                        />
                    )}
                </>
            </DocumentTitle>
        );
    },
});

export default Reports;
