import PropTypes from 'prop-types';
import i18next from 'i18next';
import DocumentTitle from 'react-document-title';
import React from 'react';
import _ from 'lodash';
import { Text } from '../components/form/text.react';
import browserHistory from '../common/history';
import { TextArea } from '../components/form/textArea.react';
import { SelectBox } from '../components/form/selectBox.react';
import { Checkbox } from '../components/form/checkbox.react';
import { TextNum } from '../components/form/textNum.react';
import { Switch } from '../components/form/switch.react';
import { guid, checkLangMutation } from '../common/utils';
import PageRights from '../common/pageRights';
import Heading from '../components/heading.react';
import TabsButtonsTable from '../components/tabs/tabsButtonsTable.react';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import MainButtons from '../components/tabs/mainButtons.react';
import MainButton from '../components/tabs/mainButton.react';
import TabsButton from '../components/tabs/tabsButton.react';
import TabsButtonMore from '../components/tabs/tabsButtonMore.react';
import TabsButtonFilter from '../components/tabs/tabsButtonFilter.react';
import Table from '../components/table/table.react';
import Column from '../components/table/column.react';
import Form from '../components/form/form.react';
import ShowHideComponent from '../components/form/showHideComponent.react.js';
import TemplateStore from '../flux/template.store';
import TemplateActions from '../flux/template.actions';
import TemplateApi from '../flux/template.api';
import alt from '../flux/alt';
import ConfirmModal from './modals/confirmModal.react';
import TemplateTaskModal from './modals/templateTaskModal.react';
import TemplateLinkModal from './modals/templateLinkModal.react';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import AlertsActions from '../components/alerts/alerts.actions';
import LoggedUserStore from '../flux/loggedUser.store';
import TemplateHeaderApi from '../flux/templateHeader.api';
import TextListRows from '../components/form/textListRows/textListRows.react';
import CKEditor from '../components/form/ckEditor.react';
import EmptyComponent from '../components/form/emptyComponent.react';
import ImportVariablesModal from './modals/importVariablesModal.react';
import HiddenTabs from '../components/tabs/hiddenTabs.react';
import HiddenTab from '../components/tabs/hiddenTab.react';
import HiddenTabContent from '../components/tabs/hiddenTabContent.react';
import { getVariableTypes } from '../components5.0/consts/variableTypes';
import { saveAs } from '../../assets/libs/filesaver';
import ApiRequest from '../api/apiRequest';
import ImportVariablesMappingModal from './modals/importVariablesMappingModal.react';

const Diagram = require('../components/diagram/diagram.react');

class Template extends React.Component {

    constructor(props) {
        super(props);

        this.state = _.extend(
            {
                formIsPristine: true,
                deleteTaskModalIsOpen: false,
                deleteVariableModalIsOpen: false,
                taskId: null,
                variableId: null,
                deletePrintModalIsOpen: false,
                deleteHeaderModalIsOpen: false,
                createVersionModalIsOpen: false,
                printId: null,
                statusModalIsOpen: false,
                confirmNewModalIsOpen: false,
                confirmModalIsOpen: false,
                confirmStatusModalIsOpen: false,
                importModalIsOpen: false,
                extraSettingsResolved: false,
                templateStatusesFormKey: 0,
                diagramKey: 0,
                renderDiagram: props.match.params.tabName === 'graph',
                importVariablesMappingModalIsOpen: false,
            },
            TemplateStore.getState(),
        );

        this._onChange = this._onChange.bind(this);
        this.watchTemplateBeforeLeave =
            this.watchTemplateBeforeLeave.bind(this);
        this.watchTemplateStatusesBeforeLeave =
            this.watchTemplateStatusesBeforeLeave.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.saveTemplate = this.saveTemplate.bind(this);
        this.postData = this.postData.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.handleAddHeader = this.handleAddHeader.bind(this);
        this.handleEditHeader = this.handleEditHeader.bind(this);
        this.handleAddTask = this.handleAddTask.bind(this);
        this.handleEditTask = this.handleEditTask.bind(this);
        this.handleAddVariable = this.handleAddVariable.bind(this);
        this.handleEditVariable = this.handleEditVariable.bind(this);
        this.handleEditLink = this.handleEditLink.bind(this);
        this.handleAddPrint = this.handleAddPrint.bind(this);
        this.handleEditPrint = this.handleEditPrint.bind(this);
        this.goBack = this.goBack.bind(this);
        this.openDeleteTaskModal = this.openDeleteTaskModal.bind(this);
        this.deleteTask = this.deleteTask.bind(this);
        this.closeDeleteTaskModal = this.closeDeleteTaskModal.bind(this);
        this.openDeleteVariableModal = this.openDeleteVariableModal.bind(this);
        this.deleteVariable = this.deleteVariable.bind(this);
        this.closeDeleteVariableModal =
            this.closeDeleteVariableModal.bind(this);
        this.openDeletePrintModal = this.openDeletePrintModal.bind(this);
        this.deletePrint = this.deletePrint.bind(this);
        this.closeDeletePrintModal = this.closeDeletePrintModal.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleMapping = this.handleMapping.bind(this);
        this.handleMappingVisual = this.handleMappingVisual.bind(this);
        this.openDeleteHeaderModal = this.openDeleteHeaderModal.bind(this);
        this.deleteHeader = this.deleteHeader.bind(this);
        this.closeDeleteHeaderModal = this.closeDeleteHeaderModal.bind(this);
        this.saveDiagram = this.saveDiagram.bind(this);
        this.closeTemplateTaskModal = this.closeTemplateTaskModal.bind(this);
        this.closeTemplateLinkModal = this.closeTemplateLinkModal.bind(this);
        this.handleSaveTaskChanges = this.handleSaveTaskChanges.bind(this);
        this.handleSaveDiagram = this.handleSaveDiagram.bind(this);
        this.saveJustDiagram = this.saveJustDiagram.bind(this);
        this.handleSaveLinkChanges = this.handleSaveLinkChanges.bind(this);
        this.generateHtml = this.generateHtml.bind(this);
        this.createVersion = this.createVersion.bind(this);
        this.changeVersion = this.changeVersion.bind(this);
        this.openCreateVersionModal = this.openCreateVersionModal.bind(this);
        this.closeCreateVersionModal = this.closeCreateVersionModal.bind(this);
        this.formStatusesChanged = this.formStatusesChanged.bind(this);
        this.saveStatuses = this.saveStatuses.bind(this);
        this.copyHeader = this.copyHeader.bind(this);
        this.setHeaderStatus = this.setHeaderStatus.bind(this);
        this.openStatusModal = this.openStatusModal.bind(this);
        this.closeStatusModal = this.closeStatusModal.bind(this);
        this.toConnection = this.toConnection.bind(this);
        this.copyVariable = this.copyVariable.bind(this);
        this.confirmSaveNew = this.confirmSaveNew.bind(this);
        this.closeConfirmNewModal = this.closeConfirmNewModal.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.confirmStatusSaving = this.confirmStatusSaving.bind(this);
        this.cancelStatusSaving = this.cancelStatusSaving.bind(this);
        this.closeStatusConfirmModal = this.closeStatusConfirmModal.bind(this);
        this.openImportVariablesMappingModal = this.openImportVariablesMappingModal.bind(this);
        this.closeImportVariablesMappingModal = this.closeImportVariablesMappingModal.bind(this);
        this.handleVariablesMappingImport = this.handleVariablesMappingImport.bind(this);
        this.handleVariablesMappingExport = this.handleVariablesMappingExport.bind(this);
        this.toVariableAssignment = this.toVariableAssignment.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        TemplateStore.listen(this._onChange);

        const { templateId, versionId } = this.props.match.params;

        // location state for previous breadcrumb path and close button
        const locationState = this.props.location.state;

        if (locationState) {
            TemplateActions.savePrevPath(this.props.location.state);
        }

        if (templateId !== 'new') {
            TemplateActions.fetchComponents({
                apiUrl: `/template-processes/${templateId}/${versionId}`,
            });
            TemplateActions.fetchVersions(templateId, versionId);
            TemplateActions.fetchTemplateStatuses(templateId);

            const fetchArr = [
                TemplateActions.fetchProcessShredding(templateId),
            ];

            if (LoggedUserStore.isAdmin()) {
                fetchArr.push(TemplateActions.fetchProcessArchive(templateId));
            }

            Promise.all(fetchArr).finally(() => {
                // no matter the outcome, mark the extra settings as resolved (we know their definitive state)

                // wait for the extra settings' inputs to update based on the possibly fetched data
                setTimeout(() => {
                    this.setState({ extraSettingsResolved: true });
                }, 0);
            });
        } else {
            TemplateActions.setHeading(i18next.t('newShe'));
            TemplateActions.setLoading(false);
        }

        this.watchTemplateBeforeLeave();
        this.watchTemplateStatusesBeforeLeave();
    }

    UNSAFE_componentWillUpdate(nextProps, nextState) {
        // load template data after saving new template without closing or after saving new template with a confirmation window
        const { templateId, versionId } = this.props.match.params;
        const nextTemplateId = nextProps.match.params.templateId;
        const nextVersionId = nextProps.match.params.versionId;

        if (
            nextTemplateId !== 'new' &&
            (nextTemplateId !== templateId || nextVersionId !== versionId)
        ) {
            TemplateActions.setHeading('');
            TemplateActions.fetchComponents({
                apiUrl: `/template-processes/${nextTemplateId}/${nextVersionId}`,
            });
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            this.props.match.params.tabName !== prevProps.match.params.tabName
            && this.props.match.params.tabName === 'graph'
        ) {
            if (!this.state.renderDiagram) {
                this.setState({ renderDiagram: true });
            }
            this.setState({ diagramKey: prevState.diagramKey === 0 ? 1 : 0 });
        }

        if (prevState.heading !== this.state.heading) {
            const { templateId, versionId } = this.props.match.params;

            BreadcrumbActions.changeBread([
                { name: i18next.t('templates'), to: this.state.breadPrevPath },
                {
                    name: this.state.heading,
                    to: `/templates/template/${templateId}/${versionId}`,
                },
            ]);
        }
    }

    componentWillUnmount() {
        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        this.unlistenForm();
        this.unlistenStatusesForm();
        TemplateStore.unlisten(this._onChange);
        alt.recycle(TemplateStore);

        // resave previous paths after store is recycled
        TemplateActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });
    }

    _onChange(state) {
        this.setState(state);
    }

    watchTemplateBeforeLeave() {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const { templateId, versionId } = this.props.match.params;
                const templateUrl = `/templates/template/${templateId}/${versionId}`;
                const NrOfChars = templateUrl.length;
                // new template, first tab and we want switch to another tab
                if (
                    location.pathname.substring(0, NrOfChars) == templateUrl &&
                    templateId == 'new' &&
                    this.refs.formTemplate
                ) {
                    this.setState({ confirmNewModalIsOpen: true });

                    this.onFormAction = (data) => {
                        this.postData(data, callback, 'watchSave');
                    };

                    this.leavePage = callback;
                } else if (
                    !this.state.formIsPristine &&
                    location.pathname != templateUrl
                ) {
                    // going out of template
                    if (
                        location.pathname.substring(0, NrOfChars) !=
                            templateUrl ||
                        location.pathname.split('/').length > 6
                    ) {
                        this.setState({ confirmModalIsOpen: true });

                        this.leavePage = callback;
                    } else {
                        // still in template first tab
                        callback();
                    }
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaveNew() {
        // save and leave
        this.refs.formTemplate.submit();
        this.setState({ confirmNewModalIsOpen: false });
    }

    closeConfirmNewModal() {
        // cancel and stay
        this.setState({ confirmNewModalIsOpen: false });
    }

    confirmSaving() {
        // save and leave
        this.onFormAction = (data) => {
            this.postData(data, this.leavePage);
        };

        this.refs.formTemplate.submit();

        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState({ formIsPristine: true });
        setTimeout(() => {
            this.setState(
                {
                    confirmModalIsOpen: false,
                },
                () => {
                    this.leavePage();
                },
            );
        });
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    watchTemplateStatusesBeforeLeave() {
        this.unlistenStatusesForm = browserHistory.listenBefore(
            (location, callback) => {
                const { templateId, versionId } = this.props.match.params;
                const statusesUrl = `/templates/template/${templateId}/${versionId}/statuses`;

                if (
                    !this.state.formStatusesIsPristine &&
                    location.pathname !== statusesUrl
                ) {
                    this.setState({ confirmStatusModalIsOpen: true });

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmStatusSaving() {
        // save and leave
        this.saveStatuses(this.leavePage);
        this.setState({ confirmStatusModalIsOpen: false });
    }

    cancelStatusSaving() {
        // cancel saving and leave
        TemplateActions.setFormStatusesToPristine(true);
        setTimeout(() => {
            this.setState(
                (prevState) => ({
                    confirmStatusModalIsOpen: false,
                    templateStatusesFormKey: prevState.templateStatusesFormKey === 0 ? 1 : 0,
                }),
                () => {
                    this.leavePage();
                },
            );
        });
    }

    closeStatusConfirmModal() {
        // cancel and stay
        this.setState({ confirmStatusModalIsOpen: false });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    saveTemplate(close, e) {
        e.preventDefault();

        this.onFormAction = (data) => {
            this.postData(data, null, null, close);
        };

        this.refs.formTemplate.submit();
    }

    postData(data, callback, watchSave, close, version = null) {
        return new Promise((resolve, reject) => {
            const alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: i18next.t('alrSaving'),
            });

            let orgId;
            if (
                typeof data.tproc_vis_orgstr_id !== 'undefined' &&
                data.tproc_vis_orgstr_id !== null &&
                typeof data.tproc_vis_orgstr_id.value !== 'undefined'
            ) {
                orgId = data.tproc_vis_orgstr_id.value;
            } else {
                orgId = data.tproc_vis_orgstr_id;
            }

            let roleId;
            if (
                typeof data.tproc_vis_role_id !== 'undefined' &&
                data.tproc_vis_role_id !== null &&
                typeof data.tproc_vis_role_id.value !== 'undefined'
            ) {
                roleId = data.tproc_vis_role_id.value;
            } else {
                roleId = data.tproc_vis_role_id;
            }

            let dmsVisibility;
            if (
                typeof data.tproc_dms_visibility !== 'undefined' &&
                typeof data.tproc_dms_visibility.value !== 'undefined'
            ) {
                if (data.tproc_dms_visibility.value === '-null-') {
                    dmsVisibility = null;
                } else {
                    dmsVisibility = data.tproc_dms_visibility.value;
                }
            } else if (data.tproc_dms_visibility === '-null-') {
                dmsVisibility = null;
            } else {
                dmsVisibility = data.tproc_dms_visibility;
            }

            const obj = {
                tproc_name: data.tproc_name,
                tproc_description: data.tproc_description,
                tproc_default_case_name: data.tproc_default_case_name,
                tproc_vis_orgstr_id: orgId,
                tproc_vis_role_id: roleId,
                tproc_hr_role_id:
                    data.tproc_hr_role_id &&
                    typeof data.tproc_hr_role_id.value !== 'undefined'
                        ? data.tproc_hr_role_id.value
                        : data.tproc_hr_role_id,
                tproc_dms_visibility: dmsVisibility,
                // tproc_url: data.tproc_url,
                // tproc_url_tab_name: data.tproc_url_tab_name,
                tproc_status: data.tproc_status.value
                    ? data.tproc_status.value
                    : data.tproc_status,
                tproc_note: data.tproc_note,
            };

            if (this.props.match.params.templateId !== 'new') {
                obj.tproc_id = this.props.match.params.templateId;
                obj.tproc_version =
                    version || this.props.match.params.versionId;
            }

            const { languages } = LoggedUserStore.getState();
            languages.forEach((lang) => {
                obj[`tproc_name_${lang}`] = data[`tproc_name_${lang}`];
                obj[`tproc_description_${lang}`] =
                    data[`tproc_description_${lang}`];
            });

            // shredding
            const { shredding } = this.state;

            // previously on now off
            if (!_.isEmpty(shredding) && !data.shreddingEnabled) {
                obj.tps_shred_on = 'none';
                obj.tps_shred_delay = shredding.tps_shred_delay;
                obj.tps_shred_dms_files = shredding.tps_shred_dms_files;
            } else if (data.shreddingEnabled) {
                obj.tps_shred_on = data.tps_shred_on.value || data.tps_shred_on;
                obj.tps_shred_delay = data.tps_shred_delay;
                obj.tps_shred_dms_files = data.tps_shred_dms_files ? 'Y' : 'N';
            }

            // archive
            if (LoggedUserStore.isAdmin()) {
                const { archive } = this.state;

                // previously on now off
                if (!_.isEmpty(archive) && !data.archiveEnabled) {
                    obj.tpa_archive_on = 'none';
                    obj.tpa_archive_delay = archive.tpa_archive_delay;
                } else if (data.archiveEnabled) {
                    obj.tpa_archive_on = data.tpa_archive_on.value || data.tpa_archive_on;
                    obj.tpa_archive_delay = data.tpa_archive_delay;
                }
            }

            TemplateApi.saveTemplate(obj)
                .then((payload) => {
                    const templateId = payload.id[0];
                    const versionId = payload.id[1];

                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrSaved'),
                    });

                    this.setState({ formIsPristine: true }, () => {
                        if (callback && watchSave === 'watchSave') {
                            // save new template before switch tab
                            callback();
                            const path = location.pathname.split('/');
                            const lastPath = path[path.length - 1];
                            browserHistory.push(
                                `/templates/template/${templateId}/${versionId}/${lastPath}`,
                            );
                        } else if (callback) {
                            callback();
                        } else if (close) {
                            this.goBack();
                        } else if (
                            this.props.match.params.templateId === 'new'
                        ) {
                            // just save
                            browserHistory.push(
                                `/templates/template/${templateId}/${versionId}`,
                            );
                        }
                    });

                    resolve();
                })
                .catch((errorMessage) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrTempSaveFailed'),
                        serverError: errorMessage,
                    });

                    reject();
                });
        });
    }

    // template form
    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine && this.state.extraSettingsResolved) {
            // when any value is changed from its initial value
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    handleAddHeader(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        this.props.history.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-header/new`,
            state: {
                breadPrevPath: this.state.breadPrevPath,
                closePrevPath: location.pathname,
            },
        });
    }

    handleEditHeader(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id = this.refs.templateHeaders.state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/templates/template/${templateId}/${versionId}/template-header/${id}`;
            }
            this.props.history.push({
                pathname: `/templates/template/${templateId}/${versionId}/template-header/${id}`,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    handleAddTask(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;
        this.props.history.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-task/new`,
            state: {
                breadPrevPath: this.state.breadPrevPath,
                closePrevPath: location.pathname,
            },
        });
    }

    handleEditTask(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id = this.refs.templateTasks.state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/templates/template/${templateId}/${versionId}/template-task/${id}`;
            }
            this.props.history.push({
                pathname: `/templates/template/${templateId}/${versionId}/template-task/${id}`,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    handleAddVariable(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;
        this.props.history.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-variable/new`,
            state: {
                breadPrevPath: this.state.breadPrevPath,
                closePrevPath: location.pathname,
            },
        });
    }

    handleEditVariable(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id = this.refs.templateVariables.state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/templates/template/${templateId}/${versionId}/template-variable/${id}`;
            }
            this.props.history.push({
                pathname: `/templates/template/${templateId}/${versionId}/template-variable/${id}`,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    handleEditLink(tableRef, e, rowId) {
        e.preventDefault();
        const id = this.refs[tableRef].state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id != null) {
            this.props.history.push({
                pathname: `/templates/template/${templateId}/${versionId}/template-link/${id}`,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    handleAddPrint(e, rowId) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        browserHistory.push(
            `/templates/template/${templateId}/${versionId}/template-print/new`,
        );
    }

    handleEditPrint(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id = this.refs.templatePrint.state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id !== null) {
            if (inNewTab) {
                return `/templates/template/${templateId}/${versionId}/template-print/${id}`;
            }
            browserHistory.push(
                `/templates/template/${templateId}/${versionId}/template-print/${id}`,
            );
        }
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }

        this.props.history.push(this.state.closePrevPath);
    }

    openDeleteTaskModal(e) {
        e.preventDefault();
        this.setState({
            deleteTaskModalIsOpen: true,
            taskId: this.refs.templateTasks.state.selectedRow.rowId,
        });
    }

    deleteTask() {
        this.closeDeleteTaskModal();
        const table = this.refs.templateTasks;
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrDeleting'),
        });
        const { templateId, versionId } = this.props.match.params;
        const id = this.state.taskId;

        TemplateApi.deleteTemplateTask(id, templateId, versionId)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrTskDeleted'),
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrTskDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeDeleteTaskModal() {
        this.setState({ deleteTaskModalIsOpen: false });
    }

    openDeleteVariableModal(e) {
        e.preventDefault();
        this.setState({
            deleteVariableModalIsOpen: true,
            variableId: this.refs.templateVariables.state.selectedRow.rowId,
        });
    }

    deleteVariable() {
        this.closeDeleteVariableModal();
        const { templateId, versionId } = this.props.match.params;
        const { variableId } = this.state;
        const table = this.refs.templateVariables;
        const alertId = guid();

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrDeleting'),
        });

        TemplateApi.deleteTemplateVariable(templateId, versionId, variableId)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrVarDeleted'),
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrVarDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeDeleteVariableModal() {
        this.setState({ deleteVariableModalIsOpen: false });
    }

    openDeletePrintModal(e) {
        e.preventDefault();
        this.setState({
            deletePrintModalIsOpen: true,
            printId: this.refs.templatePrint.state.selectedRow.rowId,
        });
    }

    deletePrint(e) {
        if (e) e.preventDefault();

        this.closeDeletePrintModal();
        const { printId } = this.state;
        const table = this.refs.templatePrint;
        const alertId = guid();
        const delObj = {
            tproc_id: this.props.match.params.templateId,
            tproc_version: this.props.match.params.versionId,
        };

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrDeleting'),
        });

        TemplateApi.deletePrint(printId, delObj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrPrintDeleted'),
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrPrintDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeDeletePrintModal() {
        this.setState({ deletePrintModalIsOpen: false });
    }

    handleKeyDown(tableRef, event) {
        if (event.which === 46) {
            // delete key
            switch (tableRef) {
                case 'templateTasks':
                    this.openDeleteTaskModal(event);
                    break;
                case 'templateVariables':
                    this.openDeleteVariableModal(event);
                    break;
                case 'templateHeaders':
                    this.openDeleteHeaderModal(event);
                    break;
                case 'templatePrint':
                    this.openDeletePrintModal(event);
                    break;
                default:
                    break;
            }
        }
    }

    handleMapping(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        browserHistory.push(
            `/templates/template/${templateId}/${versionId}/variables/mapping`,
        );
    }

    handleMappingVisual(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        browserHistory.push(
            `/templates/template/${templateId}/${versionId}/variables/mapping-visual`,
        );
    }

    openDeleteHeaderModal(e) {
        e.preventDefault();
        this.setState({
            deleteHeaderModalIsOpen: true,
            headerId: this.refs.templateHeaders.state.selectedRow.rowId,
        });
    }

    deleteHeader() {
        this.closeDeleteHeaderModal();

        const table = this.refs.templateHeaders;
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrDeleting'),
        });
        const { headerId } = this.state;
        const delObj = {
            tproc_id: this.props.match.params.templateId,
            tproc_version: this.props.match.params.versionId,
        };

        TemplateApi.deleteHeader(headerId, delObj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrHeaderDeleted'),
                    fadeOut: true,
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrHeaderDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeDeleteHeaderModal() {
        this.setState({ deleteHeaderModalIsOpen: false });
    }

    saveDiagram(close, e) {
        if (e) e.preventDefault();
        this.refs.diagram.postDiagram(this.goBack, close);
    }

    closeTemplateTaskModal() {
        TemplateActions.closeModal({ type: 'task' });
    }

    closeTemplateLinkModal() {
        TemplateActions.closeModal({ type: 'link' });
    }

    handleSaveTaskChanges(task) {
        this.refs.diagram.saveTaskChanges(task);
    }

    handleSaveDiagram(callbackRedir) {
        if (this.refs.taskModal) {
            this.refs.taskModal.saveBeforeLeave(callbackRedir);
        } else {
            this.refs.diagram.saveBeforeLeave(callbackRedir);
        }
    }

    saveJustDiagram(callbackRedir) {
        if (callbackRedir) {
            this.refs.diagram.saveBeforeLeave(callbackRedir);
        }
    }

    handleSaveLinkChanges(link) {
        this.refs.diagram.saveLinkChanges(link);
    }

    generateHtml(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;
        const alertId = guid();

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        TemplateApi.generateHtml(templateId, versionId)
            .then((payload) => {
                const { saveAs } = require('../../assets/libs/filesaver');
                const blob = new Blob([payload.result], { type: 'text/html' });

                saveAs(blob, `${this.state.heading}.htm`);

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrGenerateHtmlFailed'),
                    serverError: errorMessage,
                });
            });
    }

    createVersion(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        TemplateApi.newVersion(templateId, versionId)
            .then((payload) => {
                this.closeCreateVersionModal();

                if (!this.state.formIsPristine) {
                    this.onFormAction = (data) => {
                        this.postData(
                            data,
                            false,
                            false,
                            false,
                            payload.version,
                        )
                            .then((ignored) => {
                                browserHistory.push(
                                    `/templates/template/${templateId}/${payload.version}`,
                                );
                                TemplateActions.fetchVersions(
                                    templateId,
                                    payload.version,
                                );
                            })
                            .catch((err) => {
                                this.setState({ formIsPristine: true });
                                browserHistory.push(
                                    `/templates/template/${templateId}/${payload.version}`,
                                );
                                TemplateActions.fetchVersions(
                                    templateId,
                                    payload.version,
                                );
                            });
                    };

                    this.refs.formTemplate.submit();
                } else {
                    browserHistory.push(
                        `/templates/template/${templateId}/${payload.version}`,
                    );
                    TemplateActions.fetchVersions(templateId, payload.version);
                }
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrCreateTempVersionFailed'),
                    serverError: errorMessage,
                });
            });
    }

    openCreateVersionModal(e) {
        e.preventDefault();
        this.setState({ createVersionModalIsOpen: true });
    }

    closeCreateVersionModal() {
        this.setState({ createVersionModalIsOpen: false });
    }

    changeVersion(name, newVersion) {
        const { templateId } = this.props.match.params;

        TemplateApi.setVersion(templateId, { version: newVersion.value })
            .then((payload) => {
                browserHistory.push(
                    `/templates/template/${templateId}/${newVersion.value}`,
                );
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrChangeTempVersionFailed'),
                    serverError: errorMessage,
                });
            });
    }

    toVariableAssignment(e, rowId, row) {
        e.preventDefault();
        const id = this.refs.templateTasks.state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        // eslint-disable-next-line no-restricted-globals
        if (!isNaN(id) && id != null) {
            const url = `/templates/template/${templateId}/${versionId}/variable-assignment/${id}`;

            this.props.history.push({
                pathname: url,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: window.location.pathname,
                },
            });
        }
    }

    toConnection(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;
        browserHistory.push({
            pathname: `/templates/template/${templateId}/${versionId}/connection`,
            state: {
                breadPrevPath: this.state.breadPrevPath,
                closePrevPath: location.pathname,
            },
        });
    }

    formStatusesChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formStatusesIsPristine) {
            // when any value is changed from its initial value
            TemplateActions.setFormStatusesToPristine(false);
        } else if (isChanged === false && !this.state.formStatusesIsPristine) {
            TemplateActions.setFormStatusesToPristine(true);
        }
    }

    saveStatuses(callback, close, e) {
        if (e) {
            e.preventDefault();
        }

        const alertId = guid();
        const { languages } = LoggedUserStore.getState();
        const statusesRows = this.statusesRowsRef;
        const { templateId, versionId } = this.props.match.params;

        if (!statusesRows.runValidation()) {
            return AlertsActions.changeAlert({
                id: alertId,
                type: 'warning',
                message: i18next.t('alrFillRequiredItems'),
                show: true,
                allowCountdown: true,
            });
        }

        const rowsModel = statusesRows.state.model;
        let postArr = [];
        const toDeleteArr = [];
        const defaultArr = rowsModel.default;

        postArr = defaultArr.map((val, i) => {
            return { cs_name: val, tproc_id: templateId, cs_order: i };
        });

        languages.forEach((lang) => {
            rowsModel[lang].forEach((val, i) => {
                postArr[i][`cs_name_${lang}`] = val;
            });
        });

        const defaultValsDiff = _.difference(
            this.state.statusesValue.default,
            rowsModel.default,
        );
        // find statuses to delete
        defaultValsDiff.forEach((val) => {
            if (
                val !== null &&
                this.state.statusesValue.default.indexOf(val) !== -1
            ) {
                toDeleteArr.push({
                    cs_name: val,
                    tproc_id: templateId,
                    tproc_version: versionId,
                });
            }
        });

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        TemplateApi.saveStatuses(templateId, versionId, postArr)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });

                TemplateActions.setFormStatusesToPristine(true);

                if (!close && _.isEmpty(toDeleteArr)) {
                    TemplateActions.loadTemplateStatuses(postArr);
                }

                if (callback) {
                    callback();
                } else if (close) {
                    this.goBack();
                }
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSaveFailed'),
                    serverError: errorMessage,
                });
            });

        if (!_.isEmpty(toDeleteArr)) {
            TemplateApi.deleteStatuses(toDeleteArr).then(() => {
                if (!close) {
                    TemplateActions.loadTemplateStatuses(postArr);
                }
            }).catch((errorMessage) => {
                AlertsActions.addAlert({
                    id: alertId,
                    type: 'alert',
                    message: `${i18next.t('alrDeleteFailed')} (case statuses)`,
                    serverError: errorMessage,
                });
            });
        }
    }

    copyHeader(e) {
        e.preventDefault();

        const table = this.refs.templateHeaders;
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });
        const headerId = this.refs.templateHeaders.state.selectedRow.rowId;
        const postObj = {
            tproc_id: this.props.match.params.templateId,
            tproc_version: this.props.match.params.versionId,
        };

        TemplateHeaderApi.copyTemplateHeader(headerId, postObj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrHeaderSaved'),
                    fadeOut: true,
                });

                table.handleClickRow();
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrHeaderSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    setHeaderStatus() {
        this.closeStatusModal();

        const table = this.refs.templateHeaders;

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = {
            header_enabled:
                this.refs.templateHeaders.state.selectedRow.row
                    .header_enabled === 'N'
                    ? 'Y'
                    : 'N',
            header_id: this.state.headerId,
            tproc_id: this.props.match.params.templateId,
            tproc_version: this.props.match.params.versionId,
        };

        TemplateHeaderApi.saveTemplateHeader(obj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrHeaderSaved'),
                    fadeOut: true,
                });

                table.handleClickRow();
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrHeaderSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    openStatusModal(e) {
        e.preventDefault();
        this.setState({
            statusModalIsOpen: true,
            headerId: this.refs.templateHeaders.state.selectedRow.rowId,
        });
    }

    closeStatusModal() {
        this.setState({ statusModalIsOpen: false });
    }

    copyVariable(e) {
        e.preventDefault();

        const id = this.refs.templateVariables.state.selectedRow.rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id != null) {
            this.props.history.push({
                pathname: `/templates/template/${templateId}/${versionId}/template-variable/${id}/clone`,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: window.location.pathname,
                },
            });
        }
    }

    openImportVariablesModal = (e) => {
        e.preventDefault();
        this.setState({ importModalIsOpen: true });
    };

    closeImportModal = () => {
        this.setState({ importModalIsOpen: false });
    };

    onLoad = (fileContent, isClassDiagram) => {
        try {
            if (isClassDiagram) {
                TemplateActions.setImportedClasses(
                    JSON.parse(fileContent || '{}').classes || [],
                );
                const { templateId, versionId } = this.props.match.params;

                browserHistory.push({
                    pathname: `/templates/template/${templateId}/${versionId}/variables/import-models`,
                    // state: { breadPrevPath: location.pathname, closePrevPath: location.pathname },
                });
            } else {
                TemplateActions.setImportedStateDiagrams(
                    JSON.parse(fileContent || '{}').diagrams || [],
                );
                const { templateId, versionId } = this.props.match.params;
                browserHistory.push({
                    pathname: `/templates/template/${templateId}/${versionId}/variables/import-states/dyn-tables`,
                    // state: { breadPrevPath: location.pathname, closePrevPath: location.pathname },
                });
            }
        } catch (e) {
            AlertsActions.addAlert({
                type: 'alert',
                message: i18next.t('notInRightormat'),
            });
        }
    };

    openImportVariablesMappingModal() {
        this.setState({ importVariablesMappingModalIsOpen: true });
    }

    closeImportVariablesMappingModal() {
        this.setState({ importVariablesMappingModalIsOpen: false });
    }

    handleVariablesMappingImport(data) {
        const alertId = guid();
        const { templateId, versionId } = this.props.match.params;

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrImportingVariablesMapping'),
        });

        const arr = data.map((item) => ({
            ttask_name: item.ttask_name,
            ttask_var_mapping: item.ttask_var_mapping,
        }));

        ApiRequest.post(`/template-variables/usage/${templateId}/${versionId}/import`, JSON.stringify(arr))
            .then(() => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrVariablesMappingImported'),
                });
            })
            .catch((errorMessage) => {
                if (_.get(errorMessage, 'error.codeName') === 'MISSING_VARIABLES_WARNING') {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'warning',
                        message: i18next.t('alrVariablesMappingImportedPartially'),
                        serverError: errorMessage,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrVariablesMappingImportFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    }

    handleVariablesMappingExport() {
        const alertId = guid();
        const { templateId, versionId } = this.props.match.params;

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrExportPreparing'),
        });

        ApiRequest.get(`/template-variables/usage/${templateId}/${versionId}/export`)
            .then((payload) => {
                saveAs(
                    new Blob([JSON.stringify(payload.items)], {
                        type: 'text/plain;charset=utf-8',
                    }),
                    `variables_mapping_export_${window.location.hostname}_${this.state.items.tproc_name}_v${versionId}.json`,
                );

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrExportCompleted'),
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrExportFailed'),
                    serverError: errorMessage,
                });
            });
    }

    render() {
        const templateStatusChoices = [
            { value: 'A', title: i18next.t('operating') },
            { value: 'N', title: i18next.t('paused') },
            { value: 'D', title: i18next.t('developed') },
            { value: 'T', title: i18next.t('tested') },
        ];

        const variablesChoices = getVariableTypes();
        const variablesFilterChoices = getVariableTypes();
        // Replace var_type for checkbox to filter right way
        _.find(variablesFilterChoices, ['value', 'CB']).value = 'N';

        const tasksChoices = [
            { value: 'S', title: i18next.t('taskSolverAssign') },
            { value: 'P', title: i18next.t('taskSubprocess') },
            { value: 'A', title: i18next.t('taskAutomatic') },
            { value: 'N', title: i18next.t('taskEmailNotification') },
            { value: 'E', title: i18next.t('taskEvent') },
            { value: 'W', title: i18next.t('taskEventWait') },
            { value: 'I', title: i18next.t('invitation') },
        ];

        const relationshipChoices = [
            { value: 'G', title: i18next.t('assHierarchyGuarantor') },
            { value: 'C', title: i18next.t('assHierarchyChildren') },
            { value: 'D', title: i18next.t('assHierarchyDescendants') },
            { value: 'P', title: i18next.t('assHierarchyParent') },
            { value: 'A', title: i18next.t('assHierarchyAncestors') },
            { value: 'S', title: i18next.t('assHierarchySiblings') },
            { value: 'L', title: i18next.t('unspecified') },
        ];

        const assignChoices = [
            { value: 'S', title: i18next.t('assMethodSelect') },
            { value: 'T', title: i18next.t('assMethodAutomatic') },
            { value: 'W', title: i18next.t('assMethodLeast') },
            { value: 'C', title: i18next.t('assMethodLastSolverChoice') },
            { value: 'L', title: i18next.t('assMethodLastSolver') },
            { value: 'A', title: i18next.t('assMethodPull') },
            { value: 'V', title: i18next.t('assMethodVariable') },
        ];

        const dutyChoices = [
            { value: 'Y', title: i18next.t('duty') },
            { value: 'N', title: i18next.t('right') },
        ];

        const attachOptions = [
            { value: '-null-', title: i18next.t('dmsVisNull') },
            { value: 'M', title: i18next.t('dmsVisSup') },
            { value: 'S', title: i18next.t('dmsVisSub') },
            { value: 'SM', title: i18next.t('dmsVisSupSub') },
        ];

        const headerStatusChoices = [
            {
                value: 'Y',
                title: i18next.t('activeShe'),
                className: 'icon icon-flag-1-1 green',
            },
            {
                value: 'N',
                title: i18next.t('inactiveShe'),
                className: 'icon icon-flag-1-1 red',
            },
        ];

        const printStatusChoices = [
            {
                value: 'A',
                title: i18next.t('activeShe'),
                className: 'icon icon-flag-1-1 green',
            },
            {
                value: 'D',
                title: i18next.t('developed'),
                className: 'icon icon-pencil-2 green',
            },
        ];

        const sharingOptions = [
            { value: 'N', title: i18next.t('no') },
            { value: 'Y', title: i18next.t('forReading') },
            { value: 'W', title: i18next.t('forReadWrite') },
        ];

        const shreddingChoices = [
            { value: 'start', title: i18next.t('start') },
            { value: 'end', title: i18next.t('end') },
        ];

        const { shredding } = this.state;
        const shreddingIsOn = (
            _.map(shreddingChoices, 'value').indexOf(
                shredding.tps_shred_on,
            ) !== -1
        );

        const { archive } = this.state;
        const archiveIsOn = (
            _.map(shreddingChoices, 'value').indexOf(
                archive.tpa_archive_on,
            ) !== -1
        );

        const { templateId, versionId } = this.props.match.params;
        const { items } = this.state;

        let visOrgstValue;
        if (items.tproc_vis_orgstr_id == 0) {
            visOrgstValue = { value: '0', title: 'Root' };
        } else {
            visOrgstValue = items.tproc_vis_orgstr_id;
        }

        const { languages } = LoggedUserStore.getState();

        const getStatusValue = (value) => {
            if (typeof value === 'undefined' || value === null) {
                return templateStatusChoices[2].value;
            }
            return value;
        };

        const getStatusesColumns = () => {
            const rowsColumns = languages.map((lang, i) => {
                return (
                    <Column
                        key={`col-${lang}`}
                        title={lang}
                        name={lang}
                        type="text"
                    />
                );
            });

            rowsColumns.unshift(
                <Column
                    key="col-def"
                    title={`${i18next.t('default')} ${i18next.t('value')}`}
                    name="default"
                    type="text"
                    duty="mandatory"
                />,
            );

            return rowsColumns;
        };

        let docTitle = i18next.t('template');
        if (templateId != 'new') {
            docTitle += ` - ${this.state.heading}`;
        } else {
            docTitle += ` - ${i18next.t('addTemp')}`;
        }

        return (
            <DocumentTitle title={docTitle}>
                <TabsWrapper>
                    <Heading
                        title={
                            templateId != 'new'
                                ? this.state.heading
                                : i18next.t('addTemp')
                        }
                    >
                        <MainButtons>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <HiddenTabs params={this.props.match.params} name="template">
                        <HiddenTab
                            key="template"
                            title={i18next.t('template')}
                            tabLink={`/templates/template/${templateId}/${versionId}`}
                            name="tabTemplate"
                            showLoader
                            loaded={!this.state.loading}
                        >
                            <TabsButtonsOther key="buttonsTemplate">
                                <TabsButton
                                    key="save"
                                    icon="icon-floppy-disk"
                                    onClick={this.saveTemplate.bind(null, true)}
                                    tooltipCode="ttSave"
                                    enableOn={!this.state.loading}
                                >
                                    {i18next.t('save')}
                                </TabsButton>
                                <TabsButton
                                    key="justSave"
                                    icon="icon-download-5"
                                    onClick={this.saveTemplate.bind(
                                        null,
                                        false,
                                    )}
                                    tooltipCode="ttJustSave"
                                    enableOn={!this.state.loading}
                                >
                                    {i18next.t('justSave')}
                                </TabsButton>
                                {/* <TabsButton
                                    key="addVersion"
                                    icon="icon-add-1"
                                    onClick={this.openCreateVersionModal}
                                    tooltipCode="ttCreateTempVersion"
                                    enableOn={!this.state.loading && templateId !== 'new'}
                                >
                                    {i18next.t('addVersion')}
                                </TabsButton> */}
                                {templateId !== 'new' && (
                                    <TabsButton
                                        key="html"
                                        icon="icon-file-code"
                                        onClick={this.generateHtml}
                                        tooltipCode="ttHtml"
                                        isActive
                                    >
                                        {i18next.t('html')}
                                    </TabsButton>
                                )}
                                {/*
                                    templateId != 'new' &&
                                    <TabsButton key="validation" icon="icon-file-checked" onClick={function(){}}
                                            tooltipCode="ttValidation">{i18next.t('validation')}</TabsButton>
                                */}
                            </TabsButtonsOther>
                        </HiddenTab>
                        <HiddenTab
                            key="templateHeaders"
                            title={i18next.t('headers')}
                            tabName="headers"
                            name="tabTemplateHeaders"
                            tabLink={`/templates/template/${templateId}/${versionId}/headers`}
                        >
                            <TabsButtonsTable
                                key="buttonsTemplateHeaders"
                                boundTableName={`templateHeaders${templateId}-${versionId}`}
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-file-add"
                                    isActive
                                    onClick={this.handleAddHeader}
                                    tooltipCode="ttAddTsk"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEditHeader}
                                    tooltipCode="ttEditTsk"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="status"
                                    icon="icon-flag-1"
                                    onClick={this.openStatusModal}
                                    tooltipCode="ttStatusHdr"
                                >
                                    {i18next.t('status')}
                                </TabsButton>
                                <TabsButton
                                    key="delete"
                                    icon="icon-bin-2"
                                    onClick={this.openDeleteHeaderModal}
                                    tooltipCode="ttDelTsk"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonsTemplateHeadersMore"
                                    boundTableName={`templateHeaders${templateId}`}
                                >
                                    <TabsButton
                                        key="copy"
                                        icon="icon-files-4"
                                        tooltipCode="ttCopyHdr"
                                        hideTitle={false}
                                        onClick={this.copyHeader}
                                    >
                                        {i18next.t('copy')}
                                    </TabsButton>
                                </TabsButtonMore>
                                <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    isActive
                                    enableOnRow={false}
                                    groupType="templateHeaders"
                                    parent={this}
                                    tableRef="templateHeaders"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                        </HiddenTab>
                        <HiddenTab
                            key="templateTasks"
                            title={i18next.t('tasks')}
                            tabName="tasks"
                            name="tabTemplateTasks"
                            tabLink={`/templates/template/${templateId}/${versionId}/tasks`}
                        >
                            <TabsButtonsTable
                                key="buttonsTemplateTasks"
                                boundTableName={`templateTasks${templateId}-${versionId}`}
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-file-add"
                                    isActive
                                    onClick={this.handleAddTask}
                                    tooltipCode="ttAddTsk"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEditTask}
                                    tooltipCode="ttEditTsk"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="delete"
                                    icon="icon-bin-2"
                                    onClick={this.openDeleteTaskModal}
                                    tooltipCode="ttDelTsk"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                                <TabsButton
                                    key="variable-assignment"
                                    icon="icon2-insert-row"
                                    onClick={this.toVariableAssignment}
                                    enableOnRow={(row) => (
                                        row.ttask_type === 'S'
                                    )}
                                >
                                    {i18next.t('assignments')}
                                </TabsButton>
                                <TabsButton
                                    key="connection"
                                    icon="icon-hierarchy-7"
                                    onClick={this.toConnection}
                                    isActive
                                >
                                    {i18next.t('connection')}
                                </TabsButton>
                                <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    isActive
                                    enableOnRow={false}
                                    groupType="templateTasks"
                                    tooltipCode="ttFilterTsk"
                                    parent={this}
                                    tableRef="templateTasks"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                        </HiddenTab>
                        <HiddenTab
                            key="templateVariables"
                            title={i18next.t('vars')}
                            tabName="variables"
                            name="tabTemplateVariables"
                            tabLink={`/templates/template/${templateId}/${versionId}/variables`}
                        >
                            <TabsButtonsTable
                                key="buttonsTemplateVariables"
                                boundTableName={`templateVariables${templateId}-${versionId}`}
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-file-add"
                                    isActive
                                    onClick={this.handleAddVariable}
                                    tooltipCode="ttAddVar"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEditVariable}
                                    tooltipCode="ttEditVar"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="delete"
                                    icon="icon-bin-2"
                                    onClick={this.openDeleteVariableModal}
                                    tooltipCode="ttDelVar"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                                <TabsButton
                                    key="map2"
                                    icon="icon-hierarchy-7"
                                    isActive
                                    onClick={this.handleMappingVisual}
                                    tooltipCode="ttMapping"
                                >
                                    {i18next.t('assignments')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonsTemplateVariablesMore"
                                    boundTableName={`templateVariables${templateId}-${versionId}`}
                                >
                                    <TabsButton
                                        key="copy"
                                        icon="icon-files-4"
                                        onClick={this.copyVariable}
                                        tooltipCode="ttCopyVar"
                                        hideTitle={false}
                                    >
                                        {i18next.t('copy')}
                                    </TabsButton>
                                    <TabsButton
                                        key="map"
                                        icon="icon-treasure-map"
                                        isActive
                                        onClick={this.handleMapping}
                                        tooltipCode="ttMapping"
                                        hideTitle={false}
                                    >
                                        {i18next.t('mapping')}
                                    </TabsButton>
                                    <TabsButton
                                        key="importVariables"
                                        icon="icon-download-10"
                                        isActive
                                        onClick={this.openImportVariablesModal}
                                        tooltipCode="ttVariablesImport"
                                        hideTitle={false}
                                    >
                                        {i18next.t('classDiagram')}
                                    </TabsButton>
                                    <TabsButton
                                        key="importVariablesMapping"
                                        icon="icon-download-10"
                                        isActive
                                        onClick={this.openImportVariablesMappingModal}
                                        hideTitle={false}
                                    >
                                        {i18next.t('variablesMappingImport')}
                                    </TabsButton>
                                    <TabsButton
                                        key="exportVariablesMapping"
                                        icon="icon-upload-10"
                                        isActive
                                        onClick={this.handleVariablesMappingExport}
                                        hideTitle={false}
                                    >
                                        {i18next.t('doExportVariablesMapping')}
                                    </TabsButton>
                                </TabsButtonMore>
                                <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    isActive
                                    enableOnRow={false}
                                    groupType="templateVariables"
                                    parent={this}
                                    tableRef="templateVariables"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                        </HiddenTab>
                        <HiddenTab
                            key="templateStatuses"
                            title={i18next.t('statuses')}
                            tabName="statuses"
                            name="tabTemplateStatuses"
                            tabLink={`/templates/template/${templateId}/${versionId}/statuses`}
                            showLoader
                            loaded={!this.state.statusesLoading}
                        >
                            <TabsButtonsOther key="buttonsStatuses">
                                <TabsButton
                                    key="save"
                                    icon="icon-floppy-disk"
                                    onClick={this.saveStatuses.bind(
                                        null,
                                        null,
                                        true,
                                    )}
                                    tooltipCode="ttSave"
                                    enableOn={!this.state.statusesLoading}
                                >
                                    {i18next.t('save')}
                                </TabsButton>
                                <TabsButton
                                    key="justSave"
                                    icon="icon-download-5"
                                    onClick={this.saveStatuses.bind(
                                        null,
                                        null,
                                        false,
                                    )}
                                    tooltipCode="ttJustSave"
                                    enableOn={!this.state.statusesLoading}
                                >
                                    {i18next.t('justSave')}
                                </TabsButton>
                            </TabsButtonsOther>
                        </HiddenTab>
                        <HiddenTab
                            key="templatePrint"
                            title={i18next.t('print')}
                            tabName="print"
                            name="tabTemplatePrint"
                            tabLink={`/templates/template/${templateId}/${versionId}/print`}
                        >
                            <TabsButtonsTable
                                key="buttonsTemplatePrint"
                                boundTableName={`templatePrint${templateId}-${versionId}`}
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-file-add"
                                    isActive
                                    onClick={this.handleAddPrint}
                                    tooltipCode="ttAdd"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.handleEditPrint}
                                    tooltipCode="ttEdit"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="delete"
                                    icon="icon-bin-2"
                                    onClick={this.openDeletePrintModal}
                                    tooltipCode="ttDel"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                            </TabsButtonsTable>
                        </HiddenTab>
                        <HiddenTab
                            key="graph"
                            title={i18next.t('graph')}
                            tabName="graph"
                            name="tabTemplateGraph"
                            tabLink={`/templates/template/${templateId}/${versionId}/graph`}
                        >
                            <TabsButtonsOther
                                key="buttonsGraph"
                                boundGraphName={`graph-${templateId}`}
                            >
                                <TabsButton
                                    key="save"
                                    icon="icon-floppy-disk"
                                    onClick={this.saveDiagram.bind(null, true)}
                                    isActive
                                    tooltipCode="ttGraphSave"
                                >
                                    {i18next.t('save')}
                                </TabsButton>
                                <TabsButton
                                    key="justSave"
                                    icon="icon-download-5"
                                    onClick={this.saveDiagram.bind(null, false)}
                                    isActive
                                    tooltipCode="ttJustSave"
                                >
                                    {i18next.t('justSave')}
                                </TabsButton>
                            </TabsButtonsOther>
                        </HiddenTab>
                        <HiddenTabContent key="HiddenTabContentTemplate">
                            <Form
                                key="formTemplate"
                                ref="formTemplate"
                                name="formTemplate"
                                onValidSubmit={this.handleValidSubmit}
                                onChange={this.formChanged}
                            >
                                <Text
                                    key="tproc_name"
                                    label={`${i18next.t('defaultTemplateName')}:`}
                                    value={items.tproc_name}
                                    side="left"
                                    showComponentLabel
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <ShowHideComponent key="langName" side="left">
                                    {languages.map((lang, i) => {
                                        return (
                                            <Text
                                                key={`tprocName${lang}`}
                                                name={`tproc_name_${lang}`}
                                                label={`${i18next.t('templateName')}:`}
                                                value={
                                                    items[`tproc_name_${lang}`]
                                                }
                                                lblLang={lang}
                                                showComponentLabel
                                            />
                                        );
                                    })}
                                </ShowHideComponent>
                                <TextArea
                                    key="tproc_description"
                                    label={`${i18next.t('defaultLbl', { label: '$t(fsDescription)' })}:`}
                                    side="left"
                                    rows={3}
                                    value={items.tproc_description}
                                    showComponentLabel
                                />
                                <ShowHideComponent key="langDesc" side="left">
                                    {languages.map((lang, i) => {
                                        return (
                                            <TextArea
                                                key={`desc${lang}`}
                                                name={`tproc_description_${lang}`}
                                                label={`${i18next.t('description')}:`}
                                                side="left"
                                                rows={3}
                                                value={
                                                    items[
                                                        `tproc_description_${lang}`
                                                    ]
                                                }
                                                lblLang={lang}
                                                showComponentLabel
                                            />
                                        );
                                    })}
                                </ShowHideComponent>
                                <Text
                                    key="tproc_default_case_name"
                                    required
                                    label={`${i18next.t('defaultCaseName')}:`}
                                    value={items.tproc_default_case_name}
                                    side="left"
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <SelectBox
                                    key="tproc_status"
                                    label={`${i18next.t('status')}:`}
                                    options={templateStatusChoices}
                                    side="left"
                                    value={getStatusValue(
                                        items.tproc_status,
                                        templateStatusChoices,
                                    )}
                                    nullable={false}
                                />
                                <SelectBox
                                    key="tproc_vis_orgstr_id"
                                    label={`${i18next.t('visForOrgStrMembers')}:`}
                                    selectBoxType="DLO"
                                    side="left"
                                    value={visOrgstValue}
                                    showComponentLabel
                                />
                                <SelectBox
                                    key="tproc_vis_role_id"
                                    label={`${i18next.t('visForRoleMembers')}:`}
                                    selectBoxType="DLR"
                                    side="left"
                                    value={items.tproc_vis_role_id}
                                    defaultValue={items.tproc_vis_role_name}
                                    showComponentLabel
                                />
                                <SelectBox
                                    key="tproc_hr_role_id"
                                    label={`${i18next.t('enableTasksHandoverRole')}:`}
                                    side="left"
                                    selectBoxType="DLR"
                                    defaultValue={items.tproc_hr_role_name}
                                    value={items.tproc_hr_role_id}
                                    showComponentLabel
                                />
                                <SelectBox
                                    key="tproc_dms_visibility"
                                    label={`${i18next.t('dmsVisibility')}:`}
                                    options={attachOptions}
                                    side="left"
                                    nullable={false}
                                    value={
                                        items.tproc_dms_visibility == null
                                            ? '-null-'
                                            : items.tproc_dms_visibility
                                    }
                                />
                                {/* <Text key="tproc_url" label={i18next.t('urlAddress') + ":"} side="left" value={items.tproc_url} />
                                <Text key="tproc_url_tab_name" label={i18next.t('tabName') + ":"} side="left"
                                      value={items.tproc_url_tab_name} /> */}

                                <EmptyComponent key="empty0" side="left" />
                                <Switch
                                    key="shreddingEnabled"
                                    fontSize="0.875rem"
                                    label={`${i18next.t('shredding')}:`}
                                    side="left"
                                    value={shreddingIsOn}
                                    onChange={this.switchPrintType}
                                    toShowGroups={{ true: 'shredding' }}
                                />
                                <TextNum
                                    key="tps_shred_delay"
                                    label={`${i18next.t('shredInDays')}:`}
                                    value={shredding.tps_shred_delay}
                                    validations={{
                                        isNumeric: true,
                                        // isHigherOrEqualThan: 1,
                                    }}
                                    validationErrors={{
                                        isNumeric: i18next.t('notNumber'),
                                        // isHigherOrEqualThan: `${i18next.t('isHigherOrEqualThan')} 1`,
                                    }}
                                    side="left"
                                    showComponentLabel
                                    visibilityGroups={['shredding']}
                                />
                                <SelectBox
                                    key="tps_shred_on"
                                    label={`${i18next.t('fromBeginningOrendOfCase')}:`}
                                    options={shreddingChoices}
                                    value={
                                        shreddingIsOn
                                            ? shredding.tps_shred_on?.value || shredding.tps_shred_on
                                            : shreddingChoices[0].value
                                    }
                                    side="left"
                                    nullable={false}
                                    showComponentLabel
                                    visibilityGroups={['shredding']}
                                />
                                <Checkbox
                                    key="tps_shred_dms_files"
                                    label={`${i18next.t('shredDocuments')}:`}
                                    value={
                                        shredding.tps_shred_dms_files === 'Y'
                                    }
                                    side="left"
                                    showComponentLabel
                                    visibilityGroups={['shredding']}
                                />

                                <EmptyComponent key="empty1" side="left" />
                                {LoggedUserStore.isAdmin() && (
                                    <Switch
                                        key="archiveEnabled"
                                        fontSize="0.875rem"
                                        label={`${i18next.t('archive')}:`}
                                        side="left"
                                        value={archiveIsOn}
                                        // onChange={this.switchPrintType}
                                        toShowGroups={{ true: 'archive' }}
                                    />
                                )}
                                {LoggedUserStore.isAdmin() && (
                                    <TextNum
                                        key="tpa_archive_delay"
                                        label={`${i18next.t('archiveInDays')}:`}
                                        value={archive.tpa_archive_delay}
                                        validations={{
                                            isNumeric: true,
                                            // isHigherOrEqualThan: 1,
                                        }}
                                        validationErrors={{
                                            isNumeric: i18next.t('notNumber'),
                                            // isHigherOrEqualThan: `${i18next.t('isHigherOrEqualThan')} 1`,
                                        }}
                                        side="left"
                                        showComponentLabel
                                        visibilityGroups={['archive']}
                                    />
                                )}
                                {LoggedUserStore.isAdmin() && (
                                    <SelectBox
                                        key="tpa_archive_on"
                                        label={`${i18next.t('fromBeginningOrendOfCase')}:`}
                                        options={shreddingChoices}
                                        value={
                                            archiveIsOn
                                                ? archive.tpa_archive_on?.value || archive.tpa_archive_on
                                                : shreddingChoices[0].value
                                        }
                                        side="left"
                                        nullable={false}
                                        showComponentLabel
                                        visibilityGroups={['archive']}
                                    />
                                )}

                                <CKEditor
                                    key="tproc_note"
                                    label={`${i18next.t('notes')}:`}
                                    side="right"
                                    value={items.tproc_note || ''}
                                    dataId={templateId}
                                    height="500"
                                    disableSource
                                />
                            </Form>
                            {/* <Form key="formTemplateVersion" name="formTemplateVersion">
                                <SelectBox
                                    key="tproc_version"
                                    label={`${i18next.t('version')}:`}
                                    options={this.state.versions}
                                    onChange={this.changeVersion}
                                    side="left"
                                    nullable={false}
                                    value={this.state.version}
                                />
                            </Form> */}
                        </HiddenTabContent>
                        <HiddenTabContent key="HiddenTabContentTemplateHeaders" tabName="headers">
                            {this.props.match.params.tabName === 'headers' && (
                                <Table
                                    key="templateHeaders"
                                    ref="templateHeaders"
                                    name={`templateHeaders${templateId}-${versionId}`}
                                    onDoubleClick={this.handleEditHeader}
                                    canOpenNewTab
                                    columnsWidthsGroup="templateHeaderGroup"
                                    apiUrl={
                                        templateId !== 'new'
                                            ? `/template-processes/${templateId}/${versionId}/headers`
                                            : ''
                                    }
                                    onKeyDown={this.handleKeyDown.bind(
                                        null,
                                        'templateHeaders',
                                    )}
                                    defaultSort={{
                                        column: 'header_name',
                                        order: 'asc',
                                    }}
                                >
                                    <Column
                                        title={i18next.t('defaultHeaderName')}
                                        name="header_name"
                                        type="text"
                                    />
                                    {languages.map((lang, i) => {
                                        return (
                                            <Column
                                                key={`name-${lang}`}
                                                title={`${i18next.t('headerName')} - ${lang}`}
                                                name={`header_name_${lang}`}
                                                type="text"
                                            />
                                        );
                                    })}
                                    <Column
                                        title={i18next.t('identificator')}
                                        name="header_code"
                                        type="text"
                                    />
                                    {/* <Column title={i18next.t('defaultCaseName')} name="tproc_default_case_name" type="text" /> */}
                                    <Column
                                        title={i18next.t('status')}
                                        name="header_enabled"
                                        type="image"
                                        filterChoices={headerStatusChoices}
                                        className="center"
                                        width="120"
                                    />
                                </Table>
                            )}
                        </HiddenTabContent>
                        <HiddenTabContent key="HiddenTabContentTemplateTasks" tabName="tasks">
                            {this.props.match.params.tabName === 'tasks' && (
                                <Table
                                    key="templateTasks"
                                    ref="templateTasks"
                                    name={`templateTasks${templateId}-${versionId}`}
                                    onDoubleClick={this.handleEditTask}
                                    canOpenNewTab
                                    columnsWidthsGroup="templateTasksGroup"
                                    apiUrl={
                                        templateId !== 'new'
                                            ? `/template-processes/${templateId}/${versionId}/template-tasks`
                                            : ''
                                    }
                                    onKeyDown={this.handleKeyDown.bind(
                                        null,
                                        'templateTasks',
                                    )}
                                    defaultSort={{
                                        column: 'ttask_name',
                                        order: 'asc',
                                    }}
                                >
                                    <Column
                                        title={i18next.t('id')}
                                        name="id"
                                        type="text"
                                        className="align-right"
                                        width="80"
                                    />
                                    <Column
                                        title={i18next.t('taskType')}
                                        name="ttask_type"
                                        type="callback"
                                        filterChoices={tasksChoices}
                                        width="200"
                                        renderer={function (value, row) {
                                            const choice = _.find(
                                                tasksChoices,
                                                ['value', value],
                                            );
                                            return typeof choice !== 'undefined'
                                                ? choice.title
                                                : value;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('name')}
                                        name="ttask_name"
                                        type="text"
                                        width="200"
                                    />
                                    {languages.map((lang) => {
                                        return (
                                            <Column
                                                key={`task_name-${lang}`}
                                                title={`${i18next.t('name')} - ${lang}`}
                                                name={`ttask_name_${lang}`}
                                                type="text"
                                            />
                                        );
                                    })}
                                    <Column
                                        title={i18next.t('description')}
                                        name="ttask_description"
                                        type="text"
                                        colWithLangMutation
                                        renderer={function (value, row) {
                                            return checkLangMutation(
                                                row,
                                                'ttask_description',
                                            );
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('roleSg')}
                                        name="ttask_assesment_role_name"
                                        type="text"
                                    />
                                    <Column
                                        title={i18next.t('orgUnitShe')}
                                        name="ttask_assesment_orgstr_name"
                                        type="text"
                                    />
                                    <Column
                                        title={i18next.t('referenceUser')}
                                        name="ttask_reference_user"
                                        type="text"
                                        width="120"
                                    />
                                    <Column
                                        title={i18next.t('assHierarchy')}
                                        name="ttask_assesment_hierarchy"
                                        type="callback"
                                        filterChoices={relationshipChoices}
                                        width="200"
                                        renderer={function (value, row) {
                                            const choice = _.find(
                                                relationshipChoices,
                                                ['value', value],
                                            );
                                            return typeof choice !== 'undefined'
                                                ? choice.title
                                                : value;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('assigned')}
                                        name="ttask_assesment_method"
                                        type="callback"
                                        filterChoices={assignChoices}
                                        width="200"
                                        renderer={function (value, row) {
                                            const choice = _.find(
                                                assignChoices,
                                                ['value', value],
                                            );
                                            return typeof choice !== 'undefined'
                                                ? choice.title
                                                : value;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('rightOrDuty')}
                                        name="ttask_duty"
                                        type="callback"
                                        filterChoices={dutyChoices}
                                        width="100"
                                        renderer={function (value, row) {
                                            const choice = _.find(
                                                dutyChoices,
                                                ['value', value],
                                            );
                                            return typeof choice !== 'undefined'
                                                ? choice.title
                                                : value;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('calculations')}
                                        name="ttask_calc_count"
                                        type="text"
                                    />
                                </Table>
                            )}
                        </HiddenTabContent>
                        <HiddenTabContent key="HiddenTabContentTemplateVariables" tabName="variables">
                            {this.props.match.params.tabName === 'variables' && (
                                <Table
                                    key="templateVariables"
                                    ref="templateVariables"
                                    name={`templateVariables${templateId}-${versionId}`}
                                    onDoubleClick={this.handleEditVariable}
                                    canOpenNewTab
                                    columnsWidthsGroup="templateVariablesGroup"
                                    apiUrl={
                                        templateId !== 'new'
                                            ? `/template-processes/${templateId}/${versionId}/template-variables`
                                            : ''
                                    }
                                    onKeyDown={this.handleKeyDown.bind(
                                        null,
                                        'templateVariables',
                                    )}
                                    defaultSort={{
                                        column: 'tvar_name',
                                        order: 'asc',
                                    }}
                                >
                                    <Column
                                        title={i18next.t('id')}
                                        name="id"
                                        type="text"
                                        className="align-right"
                                        nrOfDecimals={0}
                                        width="80"
                                    />
                                    <Column
                                        title={i18next.t('defaultLbl', {
                                            label: '$t(fsName)',
                                        })}
                                        name="tvar_name"
                                        type="text"
                                    />
                                    {languages.map((lang, i) => {
                                        return (
                                            <Column
                                                key={`name-${lang}`}
                                                title={`${i18next.t('name')} - ${lang}`}
                                                name={`tvar_name_${lang}`}
                                                type="text"
                                            />
                                        );
                                    })}
                                    <Column
                                        title={i18next.t('type')}
                                        name="tvar_type"
                                        type="callback"
                                        filterChoices={variablesFilterChoices}
                                        width="180"
                                        filterRenderer={function (
                                            value,
                                            columnName,
                                        ) {
                                            if (value == 'T') {
                                                // Text
                                                return `${columnName}<eq>"T"<and>tvar_attribute<isn>`;
                                            }
                                            if (value == 'MT') {
                                                // Long text
                                                return `${columnName}<eq>"T"<and>tvar_attribute<eq>"M"`;
                                            }
                                            if (value == 'TF') {
                                                // Attachment list
                                                return `${columnName}<eq>"T"<and>tvar_attribute<eq>"F"`;
                                            }
                                            if (value == 'N') {
                                                // Number
                                                return `${columnName}<eq>"N"<and>tvar_attribute<isn>`;
                                            }
                                            if (value == 'NS') {
                                                // Sequence
                                                return `${columnName}<eq>"N"<and>tvar_attribute<eq>"S"`;
                                            }
                                            return `${columnName}<eq>"${value}"`;
                                        }}
                                        renderer={function (value, row) {
                                            const attr = row.tvar_attribute;
                                            if (attr === 'F' || attr === 'S') {
                                                // attachment list, sequence
                                                value += attr;
                                            } else if (
                                                attr === 'M' ||
                                                attr === 'T'
                                            ) {
                                                // long text, table
                                                value = attr + value;
                                            } else if (
                                                row.tvar_type === 'N' &&
                                                JSON.parse(row.tvar_meta || '{}')
                                                    .isCheckbox
                                            ) {
                                                value = 'CB';
                                            }

                                            const choice = _.find(
                                                variablesChoices,
                                                ['value', value],
                                            );
                                            return typeof choice !== 'undefined'
                                                ? choice.title
                                                : value;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('value')}
                                        name="tvar_value"
                                        type="miscellaneous"
                                        allowSorting={false}
                                        filterRenderer={(value, columnName) => {
                                            return `tvar_text_value<like>"%${value}%"<or>tvar_number_value<like>"%${value}%"`;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('sharedVar')}
                                        name="tvar_is_shared"
                                        type="callback"
                                        filterChoices={sharingOptions}
                                        width="150"
                                        renderer={function (value, row) {
                                            const choice = _.find(
                                                sharingOptions,
                                                ['value', value],
                                            );
                                            return typeof choice !== 'undefined'
                                                ? choice.title
                                                : value;
                                        }}
                                    />
                                    <Column
                                        title={i18next.t('alias')}
                                        name="tvar_alias"
                                        type="text"
                                        width="100"
                                        isVisible={LoggedUserStore.isAdmin()}
                                    />
                                    <Column
                                        title={i18next.t('classAndAttr')}
                                        name="class_and_attr"
                                        type="text"
                                        width="180"
                                    />
                                </Table>
                            )}
                        </HiddenTabContent>
                        <HiddenTabContent key="HiddenTabContentTemplateStatuses" tabName="statuses">
                            <Form
                                key={`formStatuses-${this.state.templateStatusesFormKey}`}
                                ref={(r) => {
                                    this.formStatusesRef = r;
                                }}
                                name="formStatuses"
                                onChange={this.formStatusesChanged}
                                oneColumn
                            >
                                {!this.state.statusesLoading && (
                                    <TextListRows
                                        key="statusesRows"
                                        ref={(r) => {
                                            this.statusesRowsRef = r;
                                        }}
                                        name="statusesRows"
                                        label={`${i18next.t('caseStatuses')}:`}
                                        defaultValues={this.state.statusesValue}
                                        onChange={this.formStatusesChanged}
                                        applyDuty={false}
                                        defaultValuesReadonlyColumn="default"
                                        saveButtonsSelectors={[
                                            'a.floppy-disk',
                                            'a.download-5',
                                        ]}
                                    >
                                        {getStatusesColumns()}
                                    </TextListRows>
                                )}
                            </Form>
                        </HiddenTabContent>
                        <HiddenTabContent key="HiddenTabContentTemplatePrint" tabName="print">
                            {this.props.match.params.tabName === 'print' && (
                                <Table
                                    key="templatePrint"
                                    ref="templatePrint"
                                    name={`templatePrint${templateId}-${versionId}`}
                                    apiUrl={
                                        templateId !== 'new'
                                            ? `/template-processes/${templateId}/${versionId}/prints`
                                            : ''
                                    }
                                    onKeyDown={this.handleKeyDown.bind(
                                        null,
                                        'templatePrint',
                                    )}
                                    onDoubleClick={this.handleEditPrint}
                                    canOpenNewTab
                                    columnsWidthsGroup="templatePrintGroup"
                                    defaultSort={{
                                        column: 'prnt_name',
                                        order: 'asc',
                                    }}
                                >
                                    <Column
                                        title={i18next.t('name')}
                                        name="prnt_name"
                                        type="text"
                                    />
                                    {languages.map((lang) => {
                                        return (
                                            <Column
                                                key={`prnt_name-${lang}`}
                                                title={`${i18next.t('name')} - ${lang}`}
                                                name={`prnt_name_${lang}`}
                                                type="text"
                                            />
                                        );
                                    })}
                                    <Column
                                        title={i18next.t('order')}
                                        name="prnt_order"
                                        type="number"
                                        width="100"
                                    />
                                    <Column
                                        title={i18next.t('status')}
                                        name="prnt_status"
                                        type="image"
                                        filterChoices={printStatusChoices}
                                        className="center"
                                        width="120"
                                    />
                                </Table>
                            )}
                        </HiddenTabContent>
                        <HiddenTabContent key="HiddenTabContentGraph" tabName="graph">
                            {this.state.renderDiagram && (
                                <Diagram
                                    key={`diagram-${this.state.diagramKey}`}
                                    ref="diagram"
                                    fluxName={`graph-${templateId}-${versionId}`}
                                    name={
                                        templateId != 'new'
                                            ? this.state.heading
                                            : 'diagram'
                                    }
                                    templateId={
                                        templateId !== 'new'
                                            ? Number(templateId)
                                            : null
                                    }
                                    versionId={
                                        templateId !== 'new'
                                            ? Number(versionId)
                                            : null
                                    }
                                    saveDiagram={this.handleSaveDiagram}
                                />
                            )}
                        </HiddenTabContent>
                    </HiddenTabs>
                    <ConfirmModal
                        isOpen={this.state.deleteTaskModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(tskVar)',
                        })}
                        onClose={this.closeDeleteTaskModal}
                        onConfirm={this.deleteTask}
                    />
                    <ConfirmModal
                        isOpen={this.state.deleteVariableModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(var)',
                        })}
                        onClose={this.closeDeleteVariableModal}
                        onConfirm={this.deleteVariable}
                    />
                    <ConfirmModal
                        isOpen={this.state.deletePrintModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(selectedPrint)',
                        })}
                        onClose={this.closeDeletePrintModal}
                        onConfirm={this.deletePrint}
                    />
                    <ConfirmModal
                        isOpen={this.state.deleteHeaderModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(selectedHeader)',
                        })}
                        onClose={this.closeDeleteHeaderModal}
                        onConfirm={this.deleteHeader}
                    />
                    <ConfirmModal
                        isOpen={this.state.createVersionModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmCreateTempVersion')}
                        onClose={this.closeCreateVersionModal}
                        onConfirm={this.createVersion}
                    />
                    {this.state.templateTaskModalIsOpen && (
                        <TemplateTaskModal
                            key={`taskModal${this.state.taskId}`}
                            isOpen={this.state.templateTaskModalIsOpen}
                            width="xlarge"
                            onClose={this.closeTemplateTaskModal}
                            ref="taskModal"
                            params={this.props.match.params}
                            templateId={templateId}
                            versionId={versionId}
                            taskId={this.state.taskId}
                            name={`graph-${templateId}-${versionId}`}
                            saveTaskChanges={this.handleSaveTaskChanges}
                            saveDiagram={this.saveJustDiagram}
                        />
                    )}
                    {this.state.templateLinkModalIsOpen && (
                        <TemplateLinkModal
                            key={`linkModal${this.state.linkId}`}
                            isOpen={this.state.templateLinkModalIsOpen}
                            width="xlarge"
                            onClose={this.closeTemplateLinkModal}
                            templateId={templateId}
                            versionId={versionId}
                            linkId={this.state.linkId}
                            name={`graph-${templateId}-${versionId}`}
                            saveLinkChanges={this.handleSaveLinkChanges}
                        />
                    )}
                    <ConfirmModal
                        isOpen={this.state.statusModalIsOpen}
                        width="tiny"
                        text={i18next.t('hdrStatusQ')}
                        onClose={this.closeStatusModal}
                        onConfirm={this.setHeaderStatus}
                    />
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmNewModalIsOpen}
                        onClose={this.closeConfirmNewModal}
                        onConfirm={this.confirmSaveNew}
                    />
                    <ConfirmModal
                        text={`${i18next.t('template')}: ${i18next.t('confirmSaveChanges')}`}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                    <ConfirmModal
                        text={`${i18next.t('statuses')}: ${i18next.t('confirmSaveChanges')}`}
                        width="tiny"
                        isOpen={this.state.confirmStatusModalIsOpen}
                        onClose={this.closeStatusConfirmModal}
                        onConfirm={this.confirmStatusSaving}
                        onDiscard={this.cancelStatusSaving}
                    />
                    <ImportVariablesModal
                        isOpen={this.state.importModalIsOpen}
                        onClose={this.closeImportModal}
                        onLoad={this.onLoad}
                    />
                    <ImportVariablesMappingModal
                        isOpen={this.state.importVariablesMappingModalIsOpen}
                        onClose={this.closeImportVariablesMappingModal}
                        onImport={this.handleVariablesMappingImport}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

Template.displayName = 'Template';

Template.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default Template;
