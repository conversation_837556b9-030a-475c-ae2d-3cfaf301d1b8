import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { defineScript } from '@jrblatt/light-script';
import React from 'react';
import ReactDOM from 'react-dom';
import _ from 'lodash';
import Loader from 'react-loader';
import createReactClass from 'create-react-class';
import ApiRequest from '../../api/apiRequest';
import {
    getParameterByName,
    getVarValue,
    sanitizeVariable,
} from '../../common/utils';
import casePrintRequire from './casePrintRequire';
import AlertsList from '../../components/alerts/alertsList.react';
import AlertsActions from '../../components/alerts/alerts.actions';
import Factory from '../../flux/factory';
import CasePrintActions from '../../flux/casePrint.actions';
import CasePrintStore from '../../flux/casePrint.store';
import CasePrintSingleActions from '../../flux/casePrintSingle.actions';
import <PERSON><PERSON>pi from '../../flux/case.api';
import TaskApi from '../../flux/task.api';

const Api = {
    case: CaseApi,
    task: TaskApi,
    request: {
        get: (url) => {
            return ApiRequest.get(url)
                .then((payload) => {
                    if (payload && url.indexOf('variable')) {
                        if (payload.items) {
                            payload.items.forEach((item) => {
                                const prefix = item.hasOwnProperty('ivar_type')
                                    ? 'ivar'
                                    : 'tvar';
                                sanitizeVariable(item, prefix);
                            });
                        } else {
                            // single variable
                            const prefix = payload.hasOwnProperty('ivar_type')
                                ? 'ivar'
                                : 'tvar';
                            sanitizeVariable(payload, prefix);
                        }
                    }
                    return payload;
                })
                .catch((e) => {
                    console.log(e);
                });
        },
        post: (url, data) => {
            return ApiRequest.post(url, data);
        },
        getFile: (url, ids, callback, data, isGet, fileName, alertId) => {
            return ApiRequest.getFile(url, ids, callback, data, isGet, fileName, alertId);
        },
        postFile: (url, formData, file, iprocId, lastRevisionId) => {
            return ApiRequest.postFile(
                url,
                formData,
                file,
                iprocId,
                lastRevisionId,
            );
        },
        delete: (url, data) => {
            return ApiRequest.delete(url, data);
        },
    },
};

if (window) {
    window.Api = Api;
    window.Require = casePrintRequire;
    window.getVar = function getVar(varName) {};
    window.getUnsafeVar = function getUnsafeVar(varName) {};
}
const CasePrint = createReactClass({
    propTypes: {
        params: PropTypes.objectOf(PropTypes.any),
        js: PropTypes.string,
        print: PropTypes.string,
        css: PropTypes.string,
        match: PropTypes.objectOf(PropTypes.any),
        printPreviewId: PropTypes.string,
    },

    getDefaultProps() {
        return {
            params: null,
            js: null,
            print: null,
            css: null,
            match: {},
            printPreviewId: null,
        };
    },

    getInitialState() {
        return {
            scriptLoaded: false,
            print: '',
            printJs: null,
            printCss: null,
            printName: null,
            printTimeout: 0,
            printReadyInJs: 'N',
            printReact: false,
            printLoaded: false,
            applyTasStyles: true,
        };
    },

    componentDidMount() {
        defineScript(`${window.location.origin}/assets/libs/Chart.min.js`, {
            wrapper: 'body',
            onSuccess: (e) => {
                this.setState({ scriptLoaded: true });
            },
        });

        if (this.props.match.params) {
            const { caseId, printId } = this.props.match.params;

            const casePrintObj = Factory.registerFlux(
                CasePrintStore,
                CasePrintActions,
                `casePrint-${caseId}-${printId}`,
            );
            this.casePrintActions = casePrintObj.action;
            this.casePrintStore = casePrintObj.store;

            this.casePrintActions.setIprocId(caseId);
            CasePrintSingleActions.setIprocId(caseId);

            this.loadPrint(caseId, printId);
        } else {
            const casePrintObj = Factory.registerFlux(
                CasePrintStore,
                CasePrintActions,
                `casePrint-preview-${this.props.printPreviewId}`,
            );
            this.casePrintActions = casePrintObj.action;
            this.casePrintStore = casePrintObj.store;

            // templatePrint preview
            this.loadFrontendScripts().then(() => {
                this.setState(
                    {
                        print: this.props.print,
                        printJs: this.props.js,
                        printCss: this.props.css,
                        printReact: true,
                        printLoaded: true,
                    },
                    () => {
                        if (!this.state.printJs) {
                            this.setPrintPageLoaded();
                        }
                    },
                );
            });
        }

        window.Require.stores.CasePrintStore = this.casePrintStore;
        window.Require.actions.CasePrintAction = this.casePrintActions;

        // can be called from print (for example after loading all tables)
        window.printLoaded = () => {
            this.createPrintPageLoadedDiv();
        };
    },

    componentDidUpdate(prevProps, prevState) {
        if (
            this.state.scriptLoaded &&
            _.isEmpty(prevState.printJs) &&
            !_.isEmpty(this.state.printJs)
        ) {
            // in appendScript can be used Chart, pdfjs and other context variables
            this.appendScript();
        }
    },

    componentWillUnmount() {
        if (this.script) document.body.removeChild(this.script);
        if (this.printCss) document.head.removeChild(this.printCss);
        if (this.state.printReact) {
            ReactDOM.unmountComponentAtNode(
                document.getElementById('case-print-emb'),
            );
        }
    },

    setPrintPageLoaded() {
        if (this.state.printCss) {
            this.printCss = document.createElement('style');
            this.printCss.id = 'case-print-css';
            this.printCss.type = 'text/css';
            this.printCss.innerHTML = this.state.printCss;
            document.head.appendChild(this.printCss);
        }

        if (!this.state.applyTasStyles) {
            const styles = document.getElementById('app-css');

            if (styles) {
                styles.parentNode.removeChild(styles);
            }
        }

        if (this.state.printReadyInJs === 'N') {
            this.createPrintPageLoadedDiv();
        }
    },

    createPrintPageLoadedDiv() {
        // print to PDF or HTML
        // after adding this div, puppeteer (backend) recognizes that the page is loaded
        // and invokes the creation of the PDF or takes the page HTML
        const div = document.createElement('div');
        div.id = 'case-print-page-loaded';
        document.body.appendChild(div);

        // printer
        if (getParameterByName('print', window.location.search)) {
            setTimeout(() => {
                window.print();
                window.close();
            }, 500);
        }
    },

    loadFrontendScripts() {
        return new Promise((resolve, reject) => {
            const filterActive = encodeURIComponent('js_active<eq>"Y"');

            Promise.all([
                ApiRequest.get(
                    `/scripts/frontend?filter=${filterActive}&limit=${config.restLimit}`,
                ),
                ApiRequest.get(
                    `/scripts/frontend-react?filter=${filterActive}&limit=${config.restLimit}`,
                ),
            ])
                .then((scripts) => {
                    // Scripts
                    if (scripts[0] && !_.isEmpty(scripts[0].items)) {
                        const bodyCoScripts = document.querySelector(
                            'body #case-overviews-scripts',
                        );

                        if (bodyCoScripts !== null) {
                            document.body.removeChild(bodyCoScripts);
                        }

                        const scriptsArr = scripts[0].items.map(
                            (i) => i.js_compiled || i.js,
                        );
                        const scriptString = scriptsArr.join('\r\n');
                        const script = document.createElement('script');
                        script.id = 'case-overviews-scripts';
                        script.type = 'text/javascript';
                        script.innerHTML = scriptString;
                        document.body.appendChild(script);
                    }

                    // Scripts React
                    if (scripts[1] && !_.isEmpty(scripts[1].items)) {
                        const bodyCoReactScripts = document.querySelector(
                            'body #case-overviews-react-scripts',
                        );

                        if (bodyCoReactScripts !== null) {
                            document.body.removeChild(bodyCoReactScripts);
                        }

                        const scriptsArr = scripts[1].items.map(
                            (i) => i.js_compiled || i.js,
                        );
                        const scriptString = scriptsArr.join('\r\n');
                        const script = document.createElement('script');
                        script.id = 'case-overviews-react-scripts';
                        script.type = 'text/javascript';
                        script.innerHTML = scriptString;
                        document.body.appendChild(script);
                    }

                    resolve();
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrScriptLoadFailed'),
                        serverError: errorMessage,
                    });
                });
        });
    },

    async loadPrint(caseId, printId) {
        await this.loadFrontendScripts();

        const postData = {
            iprocId: caseId,
            prntId: printId,
        };

        ApiRequest.post(
            '/processes/get-converted-print',
            JSON.stringify(postData),
        )
            .then((print) => {
                if (print) {
                    if (window && print.prnt_react === 'Y') {
                        if (print.vars !== null) {
                            window.getVar = function getVar(varName) {
                                return getVarValue(print.vars, varName);
                            };
                            window.getUnsafeVar = function getUnsafeVar(
                                varName,
                            ) {
                                return getVarValue(print.varsUnsafe, varName);
                            };
                        }
                    }

                    this.setState(
                        {
                            print: print.prnt_content,
                            printJs: print.prnt_js,
                            printCss: print.prnt_css,
                            printName: `${print.iproc_name} - ${print.prnt_name}`,
                            printTimeout: print.prnt_timeout,
                            printReadyInJs: print.prnt_ready_in_js,
                            printReact: print.prnt_react === 'Y',
                            applyTasStyles: print.prnt_apply_styles === 'Y',
                            printLoaded: true,
                        },
                        () => {
                            if (!this.state.printJs) {
                                this.setPrintPageLoaded();
                            }
                        },
                    );
                }
            })
            .catch((errorMessage) => {
                if (
                    errorMessage.error &&
                    errorMessage.error.codeName &&
                    errorMessage.error.codeName === 'LACK_OF_PERMISSIONS'
                ) {
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('noPageRights'),
                        serverError: errorMessage,
                    });
                } else {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: `${i18next.t('alrFailedData')} (Prints)`,
                        serverError: errorMessage,
                    });
                }
            });
    },

    appendScript() {
        this.script = document.createElement('script');
        this.script.id = 'case-print-js';
        this.script.type = 'text/javascript';
        this.script.innerHTML = this.state.printJs;
        document.body.appendChild(this.script);

        function deparam(paramStr) {
            const paramObj = {};

            if (!_.isEmpty(paramStr)) {
                const paramArr = paramStr.substring(1).split('&');

                paramArr.forEach((e) => {
                    const param = e.split('=');

                    let value = decodeURIComponent(param[1]);

                    if (_.startsWith(value, 'N~')) {
                        const num = value.substring(2);

                        if (!isNaN(num)) {
                            value = Number(num);
                        }
                    } else {
                        try {
                            value = JSON.parse(value);
                        } catch (err) {
                            // nothing
                        }
                    }

                    if (param[0] !== 'print') {
                        paramObj[param[0]] = value;
                    }
                });
            }

            return paramObj;
        }

        // get component values from url search
        const printValues = deparam(window.location.search);

        if (!_.isEmpty(printValues)) {
            this.casePrintActions.setPrintValue(printValues);
            this.casePrintActions.printValuesHaveBeenSet(true);
        }

        setTimeout(() => {
            this.setPrintPageLoaded();
        }, this.state.printTimeout);
    },

    render() {
        const casePrintEmbStyle = this.state.print ? { padding: 0 } : {};

        return (
            <DocumentTitle
                title={this.state.printName || i18next.t('customPrint')}
            >
                <div>
                    <Loader loaded={this.state.printLoaded} />
                    {this.state.printLoaded && this.state.printReact && (
                        <div
                            id="case-print-emb"
                            className="case-overview default-theme"
                            style={casePrintEmbStyle}
                        />
                    )}
                    {this.state.printLoaded && (
                        <div
                            className="case-overview medium-12 columns"
                            dangerouslySetInnerHTML={{
                                __html: this.state.print,
                            }}
                        />
                    )}
                    <AlertsList />
                </div>
            </DocumentTitle>
        );
    },
});

export default CasePrint;
