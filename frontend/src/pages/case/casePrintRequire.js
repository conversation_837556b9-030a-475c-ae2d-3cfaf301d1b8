import i18next from 'i18next';
import { Router, Route, Switch } from 'react-router-dom';
import PropTypes from 'prop-types';
// components 5.0
import Grid from '@mui/material/Grid';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepButton from '@mui/material/StepButton';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Collapse from '@mui/material/Collapse';
import { CaseVariablesTab } from 'overview-components/dist/components/react-wrappers/case-variables-tab';
import { DataGridTanstack } from 'overview-components/dist/components/react-wrappers/data-grid-tanstack';
import { AttachmentsTab } from 'overview-components/dist/components/react-wrappers/attachments-tab';
import { TabsOverview } from 'overview-components/dist/components/react-wrappers/tabs-overview';
import { SectionTab } from 'overview-components/dist/components/react-wrappers/section-tab';
import { Chart } from 'overview-components/dist/components/react-wrappers/chart';
import _ from 'lodash';
import React from 'react';
import ReactDOM from 'react-dom';
import Promise from 'bluebird';
import Loader from 'react-loader';
import cx from 'classnames';
import Button from '../../components5.0/Button';
import DataTable from '../../components5.0/DataTable';
import {
    CheckboxList,
    CheckboxListPure,
} from '../../components/form/checkboxList.react';
import { TextArea, TextAreaPure } from '../../components/form/textArea.react';
import { Text, TextPure } from '../../components/form/text.react';
import { Calendar, CalendarPure } from '../../components/form/calendar.react';
import {
    SelectBox,
    SelectBoxPure,
} from '../../components/form/selectBox.react';
import * as utils from '../../common/utils';
import browserHistory from '../../common/history';
import DataGrid from '../../components5.0/data-grid/DataGrid';
import TimeField from '../../components5.0/Timeline';
import MyIcon from '../../components5.0/MyIcon';
import ThemeProvider from '../../theme/index';
import DataGridDefault from '../../components5.0/prints/v1/DataGridDefault';
import GanttDiagram from '../../components5.0/prints/v1/charts/GanttDiagram';
import SimpleTable from '../../components/prints/table/v1/simpleTable.react';
import TasTable from '../../components/prints/table/v1/tasTable.react';
import Column from '../../components/prints/table/v1/column.react';
import DivTable from '../../components/prints/divTable/v1/divTable.react';
import BigCalendar from '../../components/prints/bigCalendar/v1/bigCalendar.react';
// import DataGridTanstack from '../../components5.0/data-grid/data-grid-tanstack/DataGridTanstack';

import Form from '../../components/form/form.react';
import Line from '../../components/form/line.react';
import Label from '../../components/form/label.react';
import MultiBox from '../../components/form/multiBox.react';
// import FilePreviewModal from '../modals/filePreviewModal.react';
// import Task from '../task.react'; // removed, it must be modified if necessary
import LoggedUserStore from '../../flux/loggedUser.store';
import CasePrintStore from '../../flux/casePrint.store';
import CasePrintAction from '../../flux/casePrint.actions';
import CasePrintSingle from '../../flux/casePrintSingle.store';
// import TaskStore from '../../flux/task.store';
// import TaskActions from '../../flux/task.actions';
import AlertsActions from '../../components/alerts/alerts.actions';

const sysComps = {
    Text: Text,
    TextPure: TextPure,
    TextArea: TextArea,
    TextAreaPure: TextAreaPure,
    Calendar: Calendar,
    CalendarPure: CalendarPure,
    SelectBox: SelectBox,
    SelectBoxPure: SelectBoxPure,
    Form: Form,
    Line: Line,
    Label: Label,
    MultiBox: MultiBox,
    CheckboxList: CheckboxList,
    CheckboxListPure: CheckboxListPure,
    // FilePreviewModal: FilePreviewModal,
    // Task: Task,
    DataTable: DataTable,
    SectionsCard: SectionTab,
    Grid: Grid,
    Stepper: Stepper,
    Step: Step,
    StepLabel: StepLabel,
    StepButton: StepButton,
    Button: Button,
    Table: Table,
    TableBody: TableBody,
    TableCell: TableCell,
    TableContainer: TableContainer,
    TableHead: TableHead,
    TableRow: TableRow,
    Collapse: Collapse,
    DataGrid: DataGrid,
    TimeField: TimeField,
    MyIcon: MyIcon,
    ThemeProvider: ThemeProvider,
    DataGridTanstack: DataGridTanstack,
    CaseVariablesTab: CaseVariablesTab,
    AttachmentsTab: AttachmentsTab,
    Tabs: TabsOverview,
    Chart: Chart,
};

// print components
const v1 = {
    DataGridDefault: DataGridDefault,
    GanttDiagram: GanttDiagram,
    SimpleTable: SimpleTable,
    TasTable: TasTable,
    Column: Column,
    DivTable: DivTable,
    BigCalendar: BigCalendar,
};
// const v2 = _.cloneDeep(v1);
// v2.SimpleTable = require('../../components/prints/table/v2/simpleTable.react');

// latest
const v = _.cloneDeep(v1);

const casePrintRequire = {
    PropTypes: PropTypes,
    React: React,
    ReactDOM: ReactDOM,
    Router: Router,
    Route: Route,
    Switch: Switch,
    browserHistory: browserHistory,
    lodash: _,
    i18next: i18next,
    Promise: Promise,
    Loader: Loader,
    cx: cx,
    utils: utils,
    sysComps: sysComps,
    stores: {
        LoggedUserStore: LoggedUserStore,
        CasePrintStore: CasePrintStore,
        // TaskStore: TaskStore,
        CasePrintSingle: CasePrintSingle,
    },
    actions: {
        AlertsActions: AlertsActions,
        CasePrintAction: CasePrintAction,
        // TaskActions: TaskActions,
    },
    // print components
    comps: {
        v: v, // latest
        v1: v1,
        // v2: v2,
    },
};

export default casePrintRequire;
