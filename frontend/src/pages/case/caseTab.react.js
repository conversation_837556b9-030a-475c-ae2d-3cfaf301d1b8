import i18next from 'i18next';

import PropTypes from 'prop-types';
import {
    checkLangMutation,
    checkLangMutationDiffNames,
} from '../../common/utils';
// componnents5.0
import CaseDetailsLayout from '../../components5.0/sections/cases/case-detail/CaseDetailsLayout';
import SectionsCard from '../../components5.0/SectionsCard';
import React from 'react';
import _ from 'lodash';
import TabsButtonsOther from '../../components/tabs/tabsButtonsOther.react';
import TabsButton from '../../components/tabs/tabsButton.react';
import TabsButtonMore from '../../components/tabs/tabsButtonMore.react';
import Form from '../../components/form/form.react';
import Label from '../../components/form/label.react';
import LoggedUserStore from '../../flux/loggedUser.store';

class CaseTab extends React.Component {

    render() {
        let preparedComponents = [];

        const status = function (i) {
            let status;
            switch (i.iproc_status) {
                case 'A':
                    status = i18next.t('active');
                    break;
                case 'N':
                    status = i18next.t('inactive');
                    break;
                case 'D':
                    status = i18next.t('finished');
                    break;
                case 'S':
                    status = i18next.t('suspended');
                    break;
                default:
                    status = i18next.t('unknown');
            }
            return status;
        };

        // create component after data was load
        if (typeof this.props.items !== 'undefined') {
            if (!_.isEmpty(this.props.items)) {
                const i = this.props.items;
                preparedComponents = [
                    <Label
                        key="iniciator"
                        side="left"
                        label={`${i18next.t('caseOwner')}:`}
                        value={i.iproc_owner_display_name}
                        labelType="T"
                        sanitizeLevel={1}
                    />,
                    <Label
                        key="due_date_finish"
                        side="left"
                        label={`${i18next.t('procDueDateFinish')}:`}
                        value={i.iproc_due_date_finish}
                        labelType="D"
                    />,
                    <Label
                        key="template"
                        side="left"
                        label={`${i18next.t('tprocName')}:`}
                        labelType="T"
                        value={checkLangMutation(i, 'header_name')}
                    />,
                    <Label
                        key="description"
                        side="left"
                        label={`${i18next.t('procDescription')}:`}
                        labelType="T"
                        value={checkLangMutation(i, 'tproc_description', true)}
                    />,
                    <Label
                        key="caseStatus"
                        side="left"
                        label={`${i18next.t('caseStatus')}:`}
                        value={checkLangMutationDiffNames(
                            i,
                            'iproc_case_status',
                            'cs_name',
                        )}
                        labelType="T"
                    />,
                    <Label
                        key="status"
                        side="left"
                        label={`${i18next.t('status')}:`}
                        value={status(i)}
                        labelType="T"
                    />,
                    <Label
                        key="start"
                        side="left"
                        label={`${i18next.t('startDate')}:`}
                        value={i.iproc_actual_start_date}
                        labelType="D"
                        timeStamp
                    />,
                    <Label
                        key="finish"
                        side="left"
                        label={`${i18next.t('actualEnd')}:`}
                        value={i.iproc_actual_finish_date}
                        labelType="D"
                        timeStamp
                    />,
                ];
            }
        }

        return (
            <CaseDetailsLayout
                eventButtonOnClick={this.props.eventButtonOnClick}
                eventButtonDisabled={this.props.eventButtonDisabled}
                closeButtonOnClick={this.props.closeButtonOnClick}
                caseTitle={this.props.caseTitle}
                caseStatus={this.props.caseStatus}
                showDiagram={this.props.showDiagram}
                toParentCase={this.props.toParentCase}
                printCase={this.props.printCase}
                openServiceModal={this.props.openServiceModal}
                superirorDisabled={!(this.props.items.iproc_main_iproc_id > 0)}
                openContactModal5={this.props.openContactModal5}
                openHandoverModal={this.props.openHandoverModal}
                openEditVariableModal={this.props.openEditVariableModal}
                archived={this.props.archived}
                caseInfo={this.props.items}
            >
                <SectionsCard mb={{ xs: 7, sm: 0 }}>
                    {/* <TabsButtonsOther key="buttonsCase">
                    <TabsButton
                        key="report"
                        icon="icon-information"
                        isActive
                        onClick={this.props.toPermissionsPage}
                        // tooltipCode="ttCaseReport"
                    >
                        {i18next.t('permissions')}
                    </TabsButton>
                    {LoggedUserStore.isAdmin() && (
                        <TabsButton
                            key="graph"
                            icon="icon-hierarchy-1"
                            isActive
                            onClick={this.props.showDiagram}
                            tooltipCode="ttGraph"
                        >
                            {i18next.t('graph')}
                        </TabsButton>
                    )}
                    <TabsButton
                        key="parent"
                        icon="icon-window-upload-2"
                        isActive={this.props.items.iproc_main_iproc_id > 0}
                        onClick={this.props.toParentCase}
                        onMouseWheelClick={this.props.toParentCase}
                        tooltipCode="ttParent"
                    >
                        {i18next.t('superior')}
                    </TabsButton>
                    <TabsButton
                        key="print"
                        icon="icon-printer"
                        isActive={true}
                        onClick={this.props.printCase}
                        tooltipCode="ttPrint"
                    >
                        {i18next.t('print')}
                    </TabsButton>
                    {LoggedUserStore.isSuperAdmin() && (
                        <TabsButtonMore key="buttonsTaskMore">
                            <TabsButton
                                key="changeEntity"
                                icon="icon-wrench"
                                onClick={this.props.openServiceModal}
                                isActive
                            >
                                {i18next.t('changeEntity')}
                            </TabsButton>
                        </TabsButtonMore>
                    )}
                </TabsButtonsOther> */}
                    <Form ref="formCase" name="formCase">
                        {preparedComponents}
                    </Form>
                </SectionsCard>
            </CaseDetailsLayout>
        );
    }

}

CaseTab.propTypes = {
    items: PropTypes.objectOf(PropTypes.any).isRequired,
    toParentCase: PropTypes.func.isRequired,
    goBack: PropTypes.func.isRequired,
    printCase: PropTypes.func.isRequired,
    showDiagram: PropTypes.func.isRequired,
    // toPermissionsPage: PropTypes.func.isRequired,
    openServiceModal: PropTypes.func.isRequired,
    parent: PropTypes.object.isRequired,
};

export default CaseTab;
