import i18next from 'i18next';
import ApiRequest from '../api/apiRequest';

const _ = require('lodash');
const AlertsActions = require('../components/alerts/alerts.actions');

/*
 * Check revision of uploaded file and store metadata (attributes) according to previous revision
 */
export function checkRevision(fileId, alertId, logicalType, folder) {
    return ApiRequest.get(`/dms/file/${fileId}`)
        .then((currentFilePayload) => {
            if (currentFilePayload?._raw?.REVISION > 1) {
                // is revision of existing document
                return ApiRequest.get(
                    `/dms/file/${currentFilePayload._raw.DMSF_ID_PREV}`,
                )
                    .then((lastFilePayload) => {
                        const metadataArr = [];

                        // MERGE AND UPDATE TAGS
                        const oldTags = lastFilePayload.tags;
                        for (let t = 0; t < oldTags.length; t++) {
                            const oldTag = oldTags[t];
                            if (oldTag.dmst_value !== null) {
                                metadataArr.push({
                                    DMST_ID: oldTag.dmst_id,
                                    DMST_VALUE: oldTag.dmst_value,
                                });
                            }
                        }

                        const newTags = currentFilePayload.tags;
                        for (let a = 0; a < newTags.length; a++) {
                            const newTag = newTags[a];
                            const index = _.findIndex(
                                metadataArr,
                                ['DMST_ID', newTag.dmst_id],
                            );
                            // newTag.dmst_value !== null - missing; rewrite potential old values!
                            if (index > -1) {
                                metadataArr.splice(index, 1); // delete the old value
                                // and update metadataArr with the new one
                                metadataArr.push({
                                    DMST_ID: newTag.dmst_id,
                                    DMST_VALUE: newTag.dmst_value,
                                });
                            } else if (newTag.dmst_value !== null) {
                                // store newly mined attrs
                                metadataArr.push({
                                    DMST_ID: newTag.dmst_id,
                                    DMST_VALUE: newTag.dmst_value,
                                });
                            }
                        }
                        // END MERGE

                        if (logicalType) {
                            const logTypeIndex = _.findIndex(
                                metadataArr,
                                ['DMST_ID', -1],
                            );
                            if (logTypeIndex > -1) {
                                metadataArr.splice(logTypeIndex, 1); // delete the old value
                            }
                            // pripadna zmena logickeho typu zpusobi ulozeni atributu, ktere nemuseji nalezet k danemu log.typu
                            metadataArr.push({
                                DMST_ID: -1,
                                DMST_VALUE: logicalType,
                            });
                        }
                        if (folder !== 'current') {
                            const folderIndex = _.findIndex(
                                metadataArr,
                                ['DMST_ID', -4],
                            );
                            if (folderIndex > -1) {
                                metadataArr.splice(folderIndex, 1); // delete the old value
                            }
                            metadataArr.push({
                                DMST_ID: -4,
                                DMST_VALUE: folder,
                            });
                        }

                        return metadataArr;
                    })
                    .catch((errorMessage) => {
                        AlertsActions.addAlert({
                            type: 'alert',
                            message: i18next.t('alrFailedRevisionInfo'),
                            serverError: errorMessage,
                        });
                    });
            }
            // new document
            const metadataArr = [];

            if (logicalType) {
                metadataArr.push({ DMST_ID: -1, DMST_VALUE: logicalType });
            }
            if (folder === 'current') {
                // null jako platna hodnota slozky!
                metadataArr.push({ DMST_ID: -4, DMST_VALUE: null });
            } else {
                metadataArr.push({ DMST_ID: -4, DMST_VALUE: folder });
            }

            return metadataArr;
        })
        .catch((errorMessage) => {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'alert',
                message: i18next.t('alrFileInfoFailed'),
                serverError: errorMessage,
            });
        });
}
