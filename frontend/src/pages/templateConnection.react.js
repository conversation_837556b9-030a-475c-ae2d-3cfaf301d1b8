import PropTypes from 'prop-types';
import i18next from 'i18next';
import DocumentTitle from 'react-document-title';
import browserHistory from '../common/history';
import { guid } from '../common/utils';
import PageRights from '../common/pageRights';

import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import MainButton from '../components/tabs/mainButton.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import TabsButton from '../components/tabs/tabsButton.react';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import AlertsActions from '../components/alerts/alerts.actions';
import TabsButtonsTable from '../components/tabs/tabsButtonsTable.react';
import Table from '../components/table/table.react';
import Column from '../components/table/column.react';
import TemplateLinkStore from '../flux/templateLink.store';
import ConfirmModal from './modals/confirmModal.react';
import TemplateApi from '../flux/template.api';

class TemplateConnection extends React.Component {

    constructor(props) {
        super();

        this.state = {
            connectionId: null,
            deleteConnectionModalIsOpen: false,
            breadPrevPath: '/templates',
        };

        this.openDeleteConnectionModal =
            this.openDeleteConnectionModal.bind(this);
        this.deleteConnection = this.deleteConnection.bind(this);
        this.closeDeleteConnectionModal =
            this.closeDeleteConnectionModal.bind(this);
        this.handleEditLink = this.handleEditLink.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.goBack = this.goBack.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        const locationState = this.props.location.state;
        const tlBreadPrevPath = TemplateLinkStore.getState().breadPrevPath;
        let prevUrl;

        if (
            locationState &&
            typeof locationState.breadPrevPath !== 'undefined' &&
            locationState.breadPrevPath !== null
        ) {
            prevUrl = locationState.breadPrevPath;
        } else if (tlBreadPrevPath !== null) {
            prevUrl = tlBreadPrevPath;
        } else {
            prevUrl = this.state.breadPrevPath;
        }

        this.setState({ breadPrevPath: prevUrl });

        const { templateId, versionId } = this.props.match.params;

        BreadcrumbActions.changeBread([
            { name: i18next.t('templates'), to: prevUrl },
            {
                name: i18next.t('template'),
                to: `/templates/template/${templateId}/${versionId}/tasks`,
            },
            { name: i18next.t('connection') },
        ]);
    }

    openDeleteConnectionModal(e) {
        e.preventDefault();
        this.setState({
            deleteConnectionModalIsOpen: true,
            connectionId: this.refs.templateConnection.state.selectedRow.rowId,
        });
    }

    deleteConnection() {
        const { templateId, versionId } = this.props.match.params;
        const id = this.state.connectionId;
        this.closeDeleteConnectionModal();
        const alertId = guid();
        const table = this.refs.templateConnection;

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrDeleting'),
        });

        TemplateApi.deleteTemplateLink(id, templateId, versionId)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrConnectionDeleted'),
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrConnectionDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    }

    closeDeleteConnectionModal() {
        this.setState({ deleteConnectionModalIsOpen: false });
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        const { templateId, versionId } = this.props.match.params;
        browserHistory.push(
            `/templates/template/${templateId}/${versionId}/tasks`,
        );
    }

    handleKeyDown(event) {
        if (event.which === 46) {
            // delete key
            this.openDeleteConnectionModal(event);
        }
    }

    handleEditLink(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id =
            this.refs.templateConnection.state.selectedRow.rowId || rowId;
        const { templateId, versionId } = this.props.match.params;

        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/templates/template/${templateId}/${versionId}/template-link/${id}`;
            }
            browserHistory.push({
                pathname: `/templates/template/${templateId}/${versionId}/template-link/${id}`,
                state: {
                    breadPrevPath: this.state.breadPrevPath,
                    closePrevPath: location.pathname,
                },
            });
        }
    }

    render() {
        const { templateId, versionId } = this.props.match.params;
        const connectionTypeChoices = [
            { value: 'AND', title: 'AND' },
            { value: 'OR', title: 'OR' },
            { value: 'ELSE', title: 'ELSE' },
        ];
        const connectionMandatoryChoices = [
            { value: 'Y', title: i18next.t('yes') },
            { value: 'N', title: i18next.t('no') },
        ];

        return (
            <DocumentTitle title={i18next.t('connection')}>
                <TabsWrapper>
                    <Heading title={i18next.t('connection')}>
                        <MainButtons>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="templateConnection"
                            title={i18next.t('connection')}
                            tabName="connection"
                            name="tabTemplateConnection"
                        >
                            <TabsButtonsTable
                                key="buttonsTemplateConnection"
                                boundTableName={`templateConnection${templateId}-${versionId}`}
                            >
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    tooltipCode="ttEditConnection"
                                    onClick={this.handleEditLink}
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="delete"
                                    icon="icon-bin-2"
                                    onClick={this.openDeleteConnectionModal}
                                    tooltipCode="ttDelConnection"
                                >
                                    {i18next.t('delete')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="templateConnection"
                                ref="templateConnection"
                                name={`templateConnection${templateId}-${versionId}`}
                                columnsWidthsGroup="templateConnectionGroup"
                                onKeyDown={this.handleKeyDown}
                                onDoubleClick={this.handleEditLink}
                                canOpenNewTab
                                apiUrl={
                                    templateId !== 'new'
                                        ? `/templates/template/${templateId}/${versionId}/template-link`
                                        : ''
                                }
                                defaultSort={{
                                    column: 'ttasklink_from_ttask_name',
                                    order: 'asc',
                                }}
                            >
                                <Column
                                    title={i18next.t('sourceTask')}
                                    name="ttasklink_from_ttask_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('targetTask')}
                                    name="ttasklink_to_ttask_name"
                                    type="text"
                                />
                                <Column
                                    title={i18next.t('type')}
                                    name="ttasklink_type"
                                    type="text"
                                    filterChoices={connectionTypeChoices}
                                    width="100"
                                    renderer={(value, row) => {
                                        const choice = _.find(
                                            connectionTypeChoices,
                                            ['value', value],
                                        );
                                        return typeof choice !== 'undefined'
                                            ? choice.title
                                            : value;
                                    }}
                                />
                                <Column
                                    title={i18next.t('required')}
                                    name="ttasklink_is_mandatory"
                                    type="text"
                                    width="100"
                                    filterChoices={connectionMandatoryChoices}
                                    renderer={(value, row) => {
                                        const choice = _.find(
                                            connectionMandatoryChoices,
                                            ['value', value],
                                        );
                                        return typeof choice !== 'undefined'
                                            ? choice.title
                                            : value;
                                    }}
                                />
                                <Column
                                    title={i18next.t('priority')}
                                    name="ttasklink_priority"
                                    type="number"
                                    width="80"
                                    className="center"
                                />
                                <Column
                                    title={i18next.t('conditions')}
                                    name="cond_count"
                                    type="number"
                                    className="center"
                                    width="100"
                                    allowInlineFiltering={false}
                                    allowSorting={false}
                                />
                            </Table>
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        isOpen={this.state.deleteConnectionModalIsOpen}
                        width="tiny"
                        text={i18next.t('confirmDeleteDialog', {
                            variable: '$t(connectionVar)',
                        })}
                        onClose={this.closeDeleteConnectionModal}
                        onConfirm={this.deleteConnection}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplateConnection.displayName = 'TemplateConnection';

TemplateConnection.propTypes = {
    location: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default TemplateConnection;
