import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { TextArea } from '../components/form/textArea.react';
import { guid } from '../common/utils';
import browserHistory from '../common/history';
import { Text } from '../components/form/text.react';
import { CodeArea } from '../components/form/codeArea.react';
import ApiRequest from '../api/apiRequest';

import React from 'react';
import createReactClass from 'create-react-class';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import MainButton from '../components/tabs/mainButton.react';
import Form from '../components/form/form.react';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import AltManagerMixin from '../flux/altManagerMixin';
import CKEditor from '../components/form/ckEditor.react';
import EmptyComponent from '../components/form/emptyComponent.react';
import alt from '../flux/alt';
import ConfirmModal from './modals/confirmModal.react';

const ReportSettings = createReactClass({
    displayName: 'ReportSettings',
    mixins: [AltManagerMixin],

    getInitialState() {
        return {
            report: {},
            formReportIsPristine: true,
            loading: this.props.match.params.reportId !== 'new',
            confirmModalIsOpen: false,
        };
    },

    componentDidMount() {
        const { reportId } = this.props.match.params;

        if (reportId !== 'new') {
            this.loadReport(reportId);
        } else {
            BreadcrumbActions.changeBread([
                { name: i18next.t('reports'), to: '/reports' },
                {
                    name: `${i18next.t('reports')} - ${i18next.t('new')}`,
                    to: location.pathname,
                },
            ]);

            i18next.on('languageChanged', (options) => {
                BreadcrumbActions.changeBread.defer([
                    { name: i18next.t('reports'), to: '/reports' },
                    {
                        name: `${i18next.t('reports')} - ${i18next.t('new')}`,
                        to: location.pathname,
                    },
                ]);
            });
        }

        this.watchReportFormBeforeLeave(reportId);
    },

    componentWillUnmount() {
        this.unlistenForm();
    },

    watchReportFormBeforeLeave(id) {
        this.unlistenForm = browserHistory.listenBefore(
            (location, callback) => {
                if (
                    !this.state.formReportIsPristine &&
                    location.pathname !== `/reports/report/${id}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = (data) => {
                        this.postData(data, callback);
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    },

    confirmSaving() {
        // save and leave
        this.refs.formReport.submit();
        this.setState({ confirmModalIsOpen: false });
    },

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formReportIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    },

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    },

    loadReport(id) {
        // /template-processes/2/prints/1
        ApiRequest.get(`/template-processes/2/prints/${id}`)
            .then((payload) => {
                this.setState({
                    report: payload,
                    loading: false,
                });

                BreadcrumbActions.changeBread([
                    { name: i18next.t('reports'), to: '/reports' },
                    {
                        name: `${i18next.t('report')} - ${payload.prnt_name}`,
                        to: location.pathname,
                    },
                ]);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedData'),
                    serverError: errorMessage,
                });
            });
    },

    handleValidSubmit(data) {
        this.onFormAction(data);
    },

    saveReport(e) {
        e.preventDefault();

        this.onFormAction = (data) => {
            this.postData(data);
        };

        this.refs.formReport.submit();
    },

    postData(data, callback) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = {
            /* name: data.name,
            content: data.content,
            js: data.js, */
            prnt_content: data.content,
            prnt_js: data.js,
            prnt_name: data.name,
            tproc_id: 2,
        };

        const { reportId } = this.props.match.params;
        if (reportId !== 'new') {
            // obj.report_id = reportId;
            obj.prnt_id = reportId;
        }

        ApiRequest.post('/template-processes/prints', obj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });

                this.setState(
                    {
                        formReportIsPristine: true,
                    },
                    () => {
                        if (callback) {
                            callback();
                        } else {
                            this.goBack();
                        }
                    },
                );
            })
            .catch((errorMessage) => {
                if (
                    errorMessage &&
                    errorMessage.error &&
                    errorMessage.error.codeName === 'UNIQUE_CONSTRAINT'
                ) {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrReportSaveSameNameFailed'),
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrSaveFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    },

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        this.refs.CKEditor.minimize();
        browserHistory.push('/reports');
    },

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formReportIsPristine) {
            this.setState({ formReportIsPristine: false });
        } else if (isChanged === false && !this.state.formReportIsPristine) {
            this.setState({ formReportIsPristine: true });
        }
    },

    render() {
        const { report } = this.state;
        let title;
        if (this.props.match.params.reportId === 'new') {
            title = `${i18next.t('report')} - ${i18next.t('new')}`;
        } else {
            title = !this.state.loading
                ? `${i18next.t('report')} - ${report.prnt_name}`
                : '';
        }

        return (
            <DocumentTitle title={i18next.t('report')}>
                <TabsWrapper>
                    <Heading title={title}>
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                enableOn={!this.state.loading}
                                onClick={this.saveReport}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs>
                        <Tabs.Tab
                            key="user"
                            title={i18next.t('report')}
                            name="overviewsSettings"
                            showLoader
                            loaded={!this.state.loading}
                        >
                            <Form
                                ref="formReport"
                                name="formReport"
                                onValidSubmit={this.handleValidSubmit}
                                onChange={this.formChanged}
                            >
                                <Text
                                    key="name"
                                    label={`${i18next.t('name')}:`}
                                    side="left"
                                    value={report.prnt_name}
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <CKEditor
                                    key="content"
                                    ref="CKEditor"
                                    label={`${i18next.t('html')}:`}
                                    side="left"
                                    value={report.prnt_content || ''}
                                    /* dataId={this.props.match.params.templatePrintId} */
                                    templateId={
                                        this.props.match.params.templateId
                                    }
                                    mainButtonsInMaximizedEditor
                                    height="500"
                                />
                                <EmptyComponent
                                    key="empty"
                                    side="right"
                                    smallHide
                                />
                                <CodeArea
                                    key="js"
                                    name="js"
                                    label="JS:"
                                    side="right"
                                    upperLabel
                                    value={report.prnt_js || ''}
                                    hints="templatePrint"
                                    helperText={i18next.t('ideHelp')}
                                />
                            </Form>
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    },
});

export default ReportSettings;
