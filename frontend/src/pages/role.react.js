import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { guid, checkLangMutation } from '../common/utils';
import { SelectBox } from '../components/form/selectBox.react';
import { Text } from '../components/form/text.react';
import { TextNum } from '../components/form/textNum.react';
import { TextArea } from '../components/form/textArea.react';
import browserHistory from '../common/history';
import { saveAs } from '../../assets/libs/filesaver';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';
import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import MainButton from '../components/tabs/mainButton.react';
import Form from '../components/form/form.react';
import MultiBox from '../components/form/multiBox.react';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import LoggedUserStore from '../flux/loggedUser.store';
import ConfirmModal from './modals/confirmModal.react';
import TabsButtonsTable from '../components/tabs/tabsButtonsTable.react';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import TabsButton from '../components/tabs/tabsButton.react';
import Table from '../components/table/table.react';
import Column from '../components/table/column.react';

class Role extends React.Component {

    constructor(props) {
        super();
        this.state = {
            role: {},
            users: [],
            categories: [],
            formRoleIsPristine: true,
            loading: true,
            multiBoxesLoaded: false,
            confirmModalIsOpen: false,
            closePrevPath: null,
            maxUsersCountInRole: null,
        };

        this.tableCompetencesRef = React.createRef();

        this.savePrevPath = this.savePrevPath.bind(this);
        this.loadRoleData = this.loadRoleData.bind(this);
        this.watchRoleFormBeforeLeave =
            this.watchRoleFormBeforeLeave.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.saveRole = this.saveRole.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.postData = this.postData.bind(this);
        this.close = this.close.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.pushMultiBox = this.pushMultiBox.bind(this);
        this.handleCompetenceDetail = this.handleCompetenceDetail.bind(this);
        this.usersToCsv = this.usersToCsv.bind(this);
        this.handleMaxAssignsChange = this.handleMaxAssignsChange.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin
        PageRights.checkUserRights([-1]);
    }

    componentDidMount() {
        const locationState = this.props.location.state;
        if (_.has(locationState, 'closePrevPath')) {
            this.savePrevPath(locationState.closePrevPath);
        }

        const { roleId } = this.props.match.params;

        if (roleId != 'new') {
            this.loadRoleData(roleId);
        } else {
            BreadcrumbActions.changeBread([
                { name: i18next.t('roles'), to: '/roles' },
                {
                    name: i18next.t('newShe'),
                    to: '/roles/role/new',
                    title: i18next.t('newShe'),
                },
            ]);

            i18next.on('languageChanged', (options) => {
                BreadcrumbActions.changeBread.defer([
                    { name: i18next.t('roles'), to: '/roles' },
                    {
                        name: i18next.t('newShe'),
                        to: '/roles/role/new',
                        title: i18next.t('newShe'),
                    },
                ]);
            });

            this.setState({ loading: false });
        }

        ApiRequest.get(`/roles/structure?limit=${config.restLimit}`)
            .then((payload) => {
                const obj = payload.items;
                const arr = [];
                for (const key in obj) {
                    const newObj = {};
                    if (obj.hasOwnProperty(key)) {
                        newObj.title = key;
                        newObj.children = obj[key];
                        arr.push(newObj);
                    }
                }

                arr.forEach((item) => {
                    item.value = item.title;
                    item.children.forEach((child) => {
                        child.title = child.role_name;
                    });
                });

                this.setState({ categories: arr });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrRolesNotLoaded'),
                    serverError: errorMessage,
                });
            });

        this.watchRoleFormBeforeLeave();
    }

    componentWillUnmount() {
        this.unlistenForm();
    }

    savePrevPath(path) {
        this.setState({ closePrevPath: path });
    }

    loadRoleData(id) {
        ApiRequest.get(`/roles/${id}`)
            .then((payload) => {
                const usersArr = [];
                payload.users.forEach((user) => {
                    if (user.user_status === 'A') {
                        // t3f-1339 Možnost odebrat odcházející zaměstnance z oddělení (Dr. Max)
                        usersArr.push({
                            value: user.id,
                            title: user.user_display_name,
                        });
                    }
                });

                this.setState({
                    role: payload,
                    users: usersArr,
                    maxUsersCountInRole: payload.role_max_assigns,
                    loading: false,
                });

                BreadcrumbActions.changeBread([
                    { name: i18next.t('roles'), to: '/roles' },
                    { name: payload.role_name, to: `/roles/role/${id}` },
                ]);
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrRoleDataLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    watchRoleFormBeforeLeave() {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const { roleId } = this.props.match.params;

                if (
                    !this.state.formRoleIsPristine &&
                    location.pathname != `/roles/role/${roleId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = function (data) {
                        // click on one of other tabs in role
                        if (
                            location.pathname.indexOf(
                                `/roles/role/${roleId}/`,
                            ) !== -1
                        ) {
                            this.postData(data, callback, true);
                        } else {
                            this.postData(data, callback);
                        }
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.refs.formRole.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formRoleIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    saveRole(e, justSave) {
        e.preventDefault();

        let callback;
        let watchSaveTab = false;
        if (justSave) {
            callback = () => {};
            watchSaveTab = true;
        }

        this.onFormAction = function (data) {
            this.postData(data, callback, watchSaveTab);
        };

        this.refs.formRole.submit();
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    postData(data, callback, watchSaveTab = false) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrRoleSaving'),
        });

        let users = data.usersAssign;
        if (!_.isEmpty(users) && typeof users[0] === 'object') {
            users = users.map((user) => user.value);
        }

        const obj = {
            role_name: data.roleName,
            role_category: data.category,
            role_note: data.desc,
            users: users,
            role_max_assigns: data.maxAssigns,
        };

        if (this.props.match.params.roleId !== 'new') {
            obj.role_id = this.props.match.params.roleId;
        }

        if (LoggedUserStore.isSuperAdmin()) {
            obj.role_access_role_id =
                data.roleGrantId && data.roleGrantId.value
                    ? data.roleGrantId.value
                    : data.roleGrantId;
        }

        ApiRequest.post('/roles', JSON.stringify(obj))
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrRoleSaved'),
                });

                this.setState({ formRoleIsPristine: true }, function () {
                    if (callback && watchSaveTab) {
                        // save role before switch tab
                        callback();

                        if (this.props.match.params.roleId === 'new') {
                            this.props.history.push(
                                window.location.pathname.replace(
                                    'new',
                                    payload.id,
                                ),
                            );
                        }

                        this.loadRoleData(payload.id);
                    } else if (callback) {
                        callback();
                    } else {
                        this.close();
                    }
                });
            })
            .catch((errorMessage) => {
                if (
                    _.get(errorMessage, 'error.codeName') ===
                    'ROLE_ASSIGNS_EXCEEDED_ERROR'
                ) {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'warning',
                        show: true,
                        message: i18next.t('alrRoleSaveFailed'),
                        serverError: errorMessage,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrRoleSaveFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    }

    close(e) {
        if (e) {
            e.preventDefault();
        }
        const path = this.state.closePrevPath || '/roles';
        this.props.history.push(path);
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formRoleIsPristine) {
            this.setState({ formRoleIsPristine: false });
        } else if (isChanged === false && !this.state.formRoleIsPristine) {
            this.setState({ formRoleIsPristine: true });
        }
    }

    pushMultiBox(multiBox) {
        if (multiBox) {
            this.setState({ multiBoxesLoaded: multiBox.checked });
        }
    }

    handleCompetenceDetail(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id =
            this.tableCompetencesRef.current.state.selectedRow.rowId || rowId;
        if (!isNaN(id) && id != null) {
            if (inNewTab) {
                return `/roles/competences/competence/${id}`;
            }

            browserHistory.push({
                pathname: `/roles/competences/competence/${id}`,
                state: { closePrevPath: window.location.pathname },
            });
        }
    }

    usersToCsv() {
        const alertId = guid();

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrExportPreparing'),
        });

        // active users only (t3f-1592)
        const filter =
            '?filter=user_status%3Ceq%3E%22A%22&order=user_name&sort=asc&limit=2147483647';
        const url = `/csv/users/role/${this.props.match.params.roleId}${filter}`;
        const data = {
            columns: [
                { id: 'id', name: 'ID' },
                { id: 'user_name', name: i18next.t('userName') },
                { id: 'user_first_name', name: i18next.t('firstName') },
                { id: 'user_last_name', name: i18next.t('lastName') },
                { id: 'user_display_name', name: i18next.t('displayName') },
                { id: 'user_email', name: i18next.t('email') },
                {
                    id: 'user_external_source',
                    name: i18next.t('externalSource'),
                },
                { id: 'external_id', name: i18next.t('externalId') },
            ],
        };

        ApiRequest.post(url, JSON.stringify(data), true)
            .then((payload) => {
                if (payload) {
                    AlertsActions.removeButtonAlert(alertId);
                    const name = prompt(
                        i18next.t('fileName'),
                        `${i18next.t('users')}-${this.state.role.role_name}.csv`,
                    );

                    const saveAsFile = (fileName) => {
                        saveAs(
                            new Blob([payload], {
                                type: 'text/plain;charset=utf-8',
                            }),
                            fileName,
                        );
                        AlertsActions.addAlert({
                            id: alertId,
                            type: 'success',
                            message: i18next.t('alrCsvDownloaded'),
                        });
                    };

                    if (name) {
                        saveAsFile(name);
                    }
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrNoDataToPrint'),
                    });
                }
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrExportFailed'),
                    serverError: errorMessage,
                });
            });
    }

    handleMaxAssignsChange(name, val) {
        this.setState({ maxUsersCountInRole: val });
    }

    render() {
        const { role, maxUsersCountInRole } = this.state;
        const { roleId } = this.props.match.params;

        return (
            <DocumentTitle title={i18next.t('roleSg')}>
                <TabsWrapper>
                    <Heading
                        title={
                            this.props.match.params.roleId != 'new'
                                ? role.role_name
                                    ? role.role_name
                                    : ''
                                : i18next.t('addRole')
                        }
                    >
                        <MainButtons>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.close}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="role"
                            tabLink={`/roles/role/${roleId}`}
                            name="tabRole"
                            showLoader
                            loaded={!this.state.loading}
                            title={
                                this.props.match.params.roleId != 'new'
                                    ? i18next.t('editRole')
                                    : i18next.t('addRole')
                            }
                        >
                            <TabsButtonsOther>
                                <TabsButton
                                    key="save"
                                    icon="icon-floppy-disk"
                                    onClick={this.saveRole}
                                    tooltipCode="ttSave"
                                    enableOn={
                                        !this.state.loading &&
                                        this.state.multiBoxesLoaded
                                    }
                                >
                                    {i18next.t('save')}
                                </TabsButton>
                                <TabsButton
                                    key="justSave"
                                    icon="icon-download-5"
                                    onClick={(e) => this.saveRole(e, true)}
                                    tooltipCode="ttJustSave"
                                    enableOn={
                                        !this.state.loading &&
                                        this.state.multiBoxesLoaded
                                    }
                                >
                                    {i18next.t('justSave')}
                                </TabsButton>
                            </TabsButtonsOther>
                            <Form
                                ref="formRole"
                                name="formRole"
                                onValidSubmit={this.handleValidSubmit}
                                onChange={this.formChanged}
                            >
                                <Text
                                    key="roleName"
                                    label={`${i18next.t('roleName')}:`}
                                    side="left"
                                    value={
                                        typeof role !== 'undefined'
                                            ? role.role_name
                                            : ''
                                    }
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <Text
                                    key="category"
                                    label={`${i18next.t('category')}:`}
                                    side="left"
                                    value={
                                        typeof role !== 'undefined'
                                            ? role.role_category
                                            : ''
                                    }
                                    meta={{
                                        suggestBox: {
                                            apiUrl: '/roles/structure',
                                            prop: 'role_category',
                                        },
                                    }}
                                    dataRenderer={(data) => {
                                        const optionsArr = [];
                                        for (const category in data) {
                                            optionsArr.push({
                                                value: category,
                                                title: category,
                                            });
                                        }
                                        return optionsArr;
                                    }}
                                />
                                <TextArea
                                    key="desc"
                                    label={`${i18next.t('description')}:`}
                                    side="left"
                                    rows={3}
                                    value={
                                        typeof role !== 'undefined'
                                            ? role.role_note
                                            : ''
                                    }
                                />
                                <SelectBox
                                    key="roleGrantId"
                                    label={`${i18next.t('restrictRoleAssignment')}:`}
                                    side="left"
                                    value={role.role_access_role_id}
                                    defaultValue={role.role_name_access}
                                    selectBoxType="DLR"
                                    readonly={!LoggedUserStore.isSuperAdmin()}
                                />
                                <MultiBox
                                    key="usersAssign"
                                    label={i18next.t('addUserSomething', {
                                        variable: '$t(role)',
                                    })}
                                    side="right"
                                    multiBoxType="DLU"
                                    value={this.state.users}
                                    valueIsArrayOfObjects
                                    changeableValue
                                    showSearchField
                                    showSearchFieldOnRight
                                    pushMultiBox={this.pushMultiBox}
                                    isResizeable
                                    bottomButtonOnClick={this.usersToCsv}
                                    showAssignmentsCount
                                    possibleAssignmentsCount={
                                        maxUsersCountInRole
                                    }
                                />
                                <TextNum
                                    key="maxAssigns"
                                    label={`${i18next.t('maxUsersCountRole')}:`}
                                    side="right"
                                    value={role.role_max_assigns}
                                    helperText={i18next.t(
                                        'unlimitedAssignLeaveBlankInfo',
                                    )}
                                    onChange={this.handleMaxAssignsChange}
                                    placeholder={i18next.t('unrestricted')}
                                />
                            </Form>
                        </Tabs.Tab>
                        <Tabs.Tab
                            key="competences"
                            title={i18next.t('competences')}
                            tabName="competences"
                            tabLink={`/roles/role/${roleId}/competences`}
                            name="tabCompetences"
                        >
                            <TabsButtonsTable
                                key="buttonsCompetences"
                                boundTableName={`tableRoleCompetences-${roleId}`}
                            >
                                <TabsButton
                                    key="detail"
                                    icon="icon-preview-1"
                                    onClick={this.handleCompetenceDetail}
                                >
                                    {i18next.t('detail')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key={`tableRoleCompetences-${roleId}`}
                                ref={this.tableCompetencesRef}
                                name={`tableRoleCompetences-${roleId}`}
                                apiUrl={`/competences/for-role/${roleId}`}
                                defaultFilter={'competence_status<ne>"D"'}
                                defaultSort={{
                                    column: 'competence_name',
                                    order: 'asc',
                                }}
                                onDoubleClick={this.handleCompetenceDetail}
                                canOpenNewTab
                                canLoad={roleId !== 'new'}
                            >
                                <Column
                                    title={i18next.t('id')}
                                    name="id"
                                    type="text"
                                    className="align-right"
                                    width="80"
                                />
                                <Column
                                    title={i18next.t('name')}
                                    name="competence_name"
                                    type="text"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'competence_name',
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('description')}
                                    name="competence_description"
                                    type="text"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'competence_description',
                                        );
                                    }}
                                />
                            </Table>
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

Role.displayName = 'Role';

Role.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default Role;
