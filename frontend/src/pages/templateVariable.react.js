import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import ReactDOM from 'react-dom';
import { Text } from '../components/form/text.react';
import { TextNum } from '../components/form/textNum.react';
import { TextArea } from '../components/form/textArea.react';
import { SelectBox } from '../components/form/selectBox.react';
import { Calendar } from '../components/form/calendar.react';
import { Checkbox } from '../components/form/checkbox.react';
import {
    guid,
    isMobile,
    checkLangMutation,
    getDrColumnMeta,
} from '../common/utils';
import { CodeArea } from '../components/form/codeArea.react';
import ideHints from '../components/form/ide/ideHints';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';
import React from 'react';
import cx from 'classnames';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import MainButton from '../components/tabs/mainButton.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import Form from '../components/form/form.react';
import Label from '../components/form/label.react';
import MultiBoxSingle from '../components/form/multiBoxSingle.react';
import Filter from '../components/form/tempVarFilter.react';
import WrapComponent from '../components/form/wrapComponent.react';
import ShowHideComponent from '../components/form/showHideComponent.react';
import TemplateVariableStore from '../flux/templateVariable.store';
import TemplateVariableActions from '../flux/templateVariable.actions';
import TemplateVariableApi from '../flux/templateVariable.api';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import alt from '../flux/alt';
import DynamicRows from '../components/form/dynamicRows/dynamicRows.react';
import Column from '../components/table/column.react';
import CKEditor from '../components/form/ckEditor.react';
import LoggedUserStore from '../flux/loggedUser.store';
import Helper from '../components/form/componentHelper.react';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import TabsButton from '../components/tabs/tabsButton.react';
import EmptyComponent from '../components/form/emptyComponent.react';
import TextListRows from '../components/form/textListRows/textListRows.react';
import ColorInput from '../components/form/colorInput.react';
import MultiBox from '../components/form/multiBox.react';
import ConfirmModal from './modals/confirmModal.react';
import ImportTemplateModal from './modals/importTemplateModal.react';
import { getVariableTypes } from '../components5.0/consts/variableTypes';

class TemplateVariable extends React.Component {

    constructor(props) {
        super();
        this.state = _.extend(
            {
                formIsPristine: true,
                urlHelperIsVisible: false,
                columnsDef: {},
                dynRowsHelperIsVisible: false,
                multiBoxesArr: [],
                multiBoxesLoaded: false,
                confirmModalIsOpen: false,
                importModalIsOpen: false,
                menuBoxIsOpen: false,
                menuBoxColumnsIsOpen: false,
                menuBoxTopOrientation: false,
                menuBoxColumnsTopOrientation: false,
            },
            TemplateVariableStore.getState(),
        );

        this._onChange = this._onChange.bind(this);
        this.watchVariableFormBeforeLeave =
            this.watchVariableFormBeforeLeave.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.saveVariable = this.saveVariable.bind(this);
        this.postData = this.postData.bind(this);
        this.goBack = this.goBack.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.refreshTablePreview = this.refreshTablePreview.bind(this);
        this.changeDef = this.changeDef.bind(this);
        this.changeDynTable = this.changeDynTable.bind(this);
        this.enableSuggestBox = this.enableSuggestBox.bind(this);
        this.getDtFilterActualVals = this.getDtFilterActualVals.bind(this);
        this.showHideDynRowsHelper = this.showHideDynRowsHelper.bind(this);
        this.showUrlHelper = this.showUrlHelper.bind(this);
        this.changeVarType = this.changeVarType.bind(this);
        this.changeNumberOfDecimals = this.changeNumberOfDecimals.bind(this);
        this.enableCkEditor = this.enableCkEditor.bind(this);
        this.enableAdvancedEditor = this.enableAdvancedEditor.bind(this);
        this.dynamicListChange = this.dynamicListChange.bind(this);
        this.varMultiChange = this.varMultiChange.bind(this);
        this.checkWithSuggester = this.checkWithSuggester.bind(this);
        this.enableCustomization = this.enableCustomization.bind(this);
        this.pushMultiBox = this.pushMultiBox.bind(this);
        this.changeVersion = this.changeVersion.bind(this);
        this.insertSnippet = this.insertSnippet.bind(this);
        this.openMenuBox = this.openMenuBox.bind(this);
        this.documentClick = this.documentClick.bind(this);
        this.handleResize = this.handleResize.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
        this.isMobileDevice = isMobile(
            navigator.userAgent || navigator.vendor || window.opera,
        );
    }

    componentDidMount() {
        TemplateVariableStore.listen(this._onChange);

        const { templateId, versionId, templateVariableId } =
            this.props.match.params;

        // location state for previous breadcrumb path and close button
        const locationState = this.props.location.state;

        if (locationState) {
            TemplateVariableActions.savePrevPath(this.props.location.state);
        }

        // Get template heading
        ApiRequest.get(`/template-processes/${templateId}/${versionId}`)
            .then((payload) => {
                TemplateVariableActions.setTemplateHeading(
                    checkLangMutation(payload, 'tproc_name'),
                );
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTempNameLoadFailed'),
                    serverError: errorMessage,
                });
            });

        if (templateVariableId != 'new') {
            const urlArr = this.props.match.url.split('/');

            // Fetch components, get template variable heading
            TemplateVariableActions.fetchComponents({
                templateId: templateId,
                versionId: versionId,
                templateVariableId: templateVariableId,
                varIsCloned: urlArr[urlArr.length - 1] === 'clone',
            });
        } else {
            TemplateVariableActions.fetchTemplateTasks(templateId, versionId); // Template tasks to customize variable
            TemplateVariableActions.setVariableHeading(i18next.t('newShe'));
            i18next.on('languageChanged', (options) => {
                TemplateVariableActions.setVariableHeading(i18next.t('newShe'));
            });
            TemplateVariableActions.setLoading(false);
        }

        TemplateVariableActions.fetchDynListOptions();
        TemplateVariableActions.fetchDynTableOptions();

        this.watchVariableFormBeforeLeave(templateId);

        document.addEventListener('click', this.documentClick);
        window.addEventListener('resize', this.handleResize);
    }

    UNSAFE_componentWillUpdate(nextProps, nextState) {
        // load variable data after saving new variable without closing
        const { templateVariableId } = nextProps.match.params;
        if (
            templateVariableId !== 'new' &&
            templateVariableId !== this.props.match.params.templateVariableId
        ) {
            TemplateVariableActions.setVariableHeading('');
            TemplateVariableActions.fetchComponents({
                templateId: this.props.match.params.templateId,
                versionId: this.props.match.params.versionId,
                templateVariableId: templateVariableId,
            });
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevState.templateHeading !== this.state.templateHeading ||
            prevState.variableHeading !== this.state.variableHeading
        ) {
            const { templateId, versionId, templateVariableId } =
                this.props.match.params;

            // set breadcrumb
            BreadcrumbActions.changeBread([
                {
                    name: i18next.t('templates'),
                    to:
                        this.state.breadPrevPath !== null
                            ? this.state.breadPrevPath
                            : '/templates',
                },
                {
                    name: this.state.templateHeading,
                    to:
                        this.state.closePrevPath !== null
                            ? this.state.closePrevPath
                            : `/templates/template/${templateId}/${versionId}/variables`,
                },
                {
                    name: `${i18next.t('var-')} ${this.state.variableHeading}`,
                    to: `/templates/template/${templateId}/${versionId}/template-variable/${templateVariableId}`,
                },
            ]);
        }
    }

    componentWillUnmount() {
        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        this.unlistenForm();
        TemplateVariableStore.unlisten(this._onChange);
        alt.recycle(TemplateVariableStore);

        // resave previous paths after store is recycled
        TemplateVariableActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });

        document.removeEventListener('click', this.documentClick);
        window.removeEventListener('resize', this.handleResize);
    }

    _onChange(state) {
        this.setState(state);
    }

    watchVariableFormBeforeLeave(templateId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const { versionId, templateVariableId } =
                    this.props.match.params;

                if (
                    !this.state.formIsPristine &&
                    location.pathname !==
                        `/templates/template/${templateId}/${versionId}/template-variable/${templateVariableId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = function (data) {
                        this.postData(data, callback);
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.refs.formVariable.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    saveVariable(close, e) {
        e.preventDefault();

        this.onFormAction = function (data) {
            this.postData(data, null, close);
        };

        this.refs.formVariable.submit();
    }

    postData(data, callback, close) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        let tvarType;
        let dlistName = null;
        let tvarAttribute = null;
        let colIndex = null;

        if (typeof data.dynList !== 'undefined') {
            if (typeof data.dynList.value !== 'undefined') {
                if (data.dynList.value.substring(0, 3) == 'DL|') {
                    dlistName = data.dynList.value.substring(
                        3,
                        data.dynList.length,
                    );
                    tvarAttribute = null;
                } else {
                    dlistName = null;
                    tvarAttribute = data.dynList.value;
                }
            } else if (data.dynList.substring(0, 3) == 'DL|') {
                dlistName = data.dynList.substring(3, data.dynList.length);
                tvarAttribute = null;
            } else {
                dlistName = null;
                tvarAttribute = data.dynList;
            }
        }

        // Long text (textArea)
        if (data.type.value ? data.type.value == 'MT' : data.type == 'MT') {
            tvarAttribute = 'M';
            tvarType = 'T';
        }
        // Sequence
        if (data.type.value ? data.type.value == 'NS' : data.type == 'NS') {
            tvarAttribute = 'S';
            tvarType = 'N';
        }
        // Attachments list
        if (data.type.value ? data.type.value == 'TF' : data.type == 'TF') {
            tvarAttribute = 'F';
            tvarType = 'T';
        }
        // Dynamic table
        if (data.type.value ? data.type.value == 'DT' : data.type == 'DT') {
            dlistName = data.dynTable.value
                ? data.dynTable.value
                : data.dynTable;
            colIndex = data.dynTableCol.value
                ? data.dynTableCol.value
                : data.dynTableCol;
            if (data.stateVar) {
                tvarAttribute = 'V';
            }
        }

        // dynamic rows values
        const model =
            typeof this.refs.dynRowTable !== 'undefined'
                ? this.refs.dynRowTable.state.model
                : {};
        let dynamicRowsValues = null;

        if (!_.isEmpty(model) && !_.isEmpty(data.columnsValue)) {
            dynamicRowsValues = {};
            for (var key in model) {
                if (model.hasOwnProperty(key) && key.indexOf('-total') === -1) {
                    // do not save total values
                    dynamicRowsValues[key] = [];
                    if (Array.isArray(model[key])) {
                        model[key].forEach((value) => {
                            if (typeof value !== 'undefined') {
                                dynamicRowsValues[key].push(value);
                            } else {
                                dynamicRowsValues[key].push(null);
                            }
                        });
                    }
                }
            }
        }

        const obj = {
            tvar_name: data.name,
            tvar_type:
                typeof tvarType !== 'undefined'
                    ? tvarType
                    : data.type.value
                        ? data.type.value
                        : data.type,
            dlist_name: dlistName,
            tvar_attribute: tvarAttribute,
            tvar_multi: data.tvarLovMulti == true ? 'X' : null,
            lovs: data.tvarLovDates || data.tvarLovNumbers,
            tvar_col_index: colIndex,
            tvar_tooltip: data.tvarTooltip,
            tvar_meta: null,
            tvar_is_shared: data.isShared.value
                ? data.isShared.value
                : data.isShared,
            tvar_copy_snapshot: data.copyToMultiinstances === true ? 'Y' : 'N',
            updateInstances: data.updateInstances,
            tvar_business: data.tvarBusiness === true ? 'Y' : 'N',
            tvar_design_version:
                typeof data.varVersion === 'object'
                    ? data.varVersion.value
                    : data.varVersion, // || 0
            tvar_date_without_time: data.dateIsWithoutTime === true ? 'Y' : 'N',
            tvar_is_shredable: data.tvarIsShredderable === true ? 'Y' : 'N',
            tvar_mm_class: data.tvar_mm_class,
            tvar_mm_class_attr: data.tvar_mm_class_attr,
        };

        const { languages } = LoggedUserStore.getState();
        languages.forEach((lang) => {
            obj[`tvar_name_${lang}`] = data[`tvar_name_${lang}`];
            obj[`tvar_tooltip_${lang}`] = data[`tvar_tooltip_${lang}`];
        });

        // text with suggestBox, text number, textArea with CKEditor
        if (obj.tvar_type == 'T') {
            if (data.suggestBox === true) {
                obj.tvar_meta = {
                    suggestBox: {
                        apiUrl: data.suggestBoxUrl,
                        prop: data.suggestBoxProp,
                    },
                };
            } else if (data.textTypePassword === true) {
                if (obj.tvar_meta) {
                    obj.tvar_meta.textTypePassword = true;
                } else {
                    obj.tvar_meta = { textTypePassword: true };
                }
            }

            if (data.numberBox === true) {
                if (obj.tvar_meta) obj.tvar_meta.onlyNumbers = true;
                else obj.tvar_meta = { onlyNumbers: true };
            }
            if (data.ckEditor === true) {
                obj.tvar_meta = { ckEditor: true };

                if (this.state.advancedCkEditor) {
                    obj.tvar_meta.ckEditorAdvanced = true;
                }
            }
            if (data.isPreview === true) {
                obj.tvar_meta = { isPreview: true };
            }

            if (
                data.type.value ? data.type.value === 'TF' : data.type === 'TF'
            ) {
                if (obj.tvar_meta) {
                    obj.tvar_meta.fixedPosition = data.fixedPosition;
                    obj.tvar_meta.defaultZoom = data.zoom;
                    obj.tvar_meta.fixedHeight = data.fixedHeight;
                } else {
                    obj.tvar_meta = {
                        fixedPosition: data.fixedPosition,
                        defaultZoom: data.zoom,
                        fixedHeight: data.fixedHeight,
                    };
                }
            }
        }
        // number
        else if (obj.tvar_type == 'N') {
            obj.tvar_meta = {
                numberOfDecimals: data.nrOfDecimalDigits,
                alignment: (
                    data.alignment?.value
                        ? data.alignment.value
                        : data.alignment
                ),
            };
        } else if (obj.tvar_type === 'CB') {
            obj.tvar_type = 'N'; // checkbox is technically number
            obj.tvar_meta = { isCheckbox: true };
        }
        // dynamic table filter and sort
        else if (obj.tvar_type == 'DT') {
            let order;

            if (
                typeof data.dtOrder === 'undefined' ||
                data.dtOrder === null ||
                data.dtOrder.value === null
            ) {
                order = null;
            } else if (data.dtOrder !== null && data.dtOrder.value) {
                order = data.dtOrder.value;
            } else {
                order = data.dtOrder;
            }

            let sort;
            if (
                typeof data.dtSort === 'undefined' ||
                data.dtSort === null ||
                data.dtSort.value === null
            ) {
                sort = null;
            } else if (data.dtSort !== null && data.dtSort.value) {
                sort = data.dtSort.value;
            } else {
                sort = data.dtSort;
            }

            if (order === null) {
                sort = null;
            }

            let index = 0;
            const filterArr = [];
            while (typeof data[`dtFilter-array${index}`] !== 'undefined') {
                const subFilter = {};
                subFilter.array =
                    typeof data[`dtFilter-array${index}`].value !== 'undefined'
                        ? data[`dtFilter-array${index}`].value
                        : data[`dtFilter-array${index}`];
                subFilter.condition = data[`dtFilter-condition${index}`];
                subFilter.operator =
                    typeof data[`dtFilter-operator${index}`].value !==
                    'undefined'
                        ? data[`dtFilter-operator${index}`].value
                        : data[`dtFilter-operator${index}`];

                filterArr.push(subFilter);

                index++;
            }

            const dynTable = {};

            if (order !== null) {
                dynTable.order = `col_${order}`;
            }
            if (sort !== null) {
                dynTable.sort = sort;
            }

            if (!_.isEmpty(filterArr)) {
                const filters = [];
                let filtersParams;

                filterArr.forEach((subFilter) => {
                    const condition = subFilter.condition || '';

                    if (
                        subFilter.operator == 'like' ||
                        subFilter.operator == 'nlike'
                    ) {
                        filters.push(
                            `col_${subFilter.array}<${subFilter.operator}>"%${condition}%"`,
                        );
                    } else if (
                        subFilter.operator == 'isn' ||
                        subFilter.operator == 'isnn'
                    ) {
                        filters.push(
                            `col_${subFilter.array}<${subFilter.operator}>` +
                                '""',
                        );
                    } else {
                        filters.push(
                            `col_${subFilter.array}<${subFilter.operator}>"${condition}"`,
                        );
                    }

                    filtersParams = filters.join('<and>');
                });

                dynTable.filter = filtersParams;
            }

            if (data.urlParams) {
                dynTable.urlParams = data.urlParams;
            }

            if (!_.isEmpty(dynTable)) {
                obj.tvar_meta = { dynTable };
            } else {
                obj.tvar_meta = null;
            }
        }
        // date
        else if (obj.tvar_type == 'D') {
            obj.tvar_meta = {
                useOnlyFutureDates: data.futureDatesSettings,
                onlyWorkingDays: data.onlyWorkingDays,
                datesDisabled: data.datesDisabled,
                showTime: data.showTime,
                startDate: data.startDate,
                endDate: data.endDate,
            };
        } else if (obj.tvar_type === 'DL') {
            if (data.dynListUrl && data.dynListUrl.trim() !== '') {
                obj.tvar_meta = {
                    dlUrl:
                        data.dynListUrl.charAt(0) !== '/'
                            ? `/${data.dynListUrl}`
                            : data.dynListUrl,
                };
            }
            if (data.structuredList) {
                obj.tvar_meta = {
                    structuredList: data.structuredList,
                };
            }
        } else {
            obj.tvar_meta = null;
        }

        // text list rows - tvar_type: "LT"
        const textListRows = this.textListRowsRef;

        if (textListRows) {
            const tlModel = textListRows.state.model;

            if (!textListRows.runValidation()) {
                return AlertsActions.changeAlert({
                    id: alertId,
                    type: 'warning',
                    message: i18next.t('alrFillRequiredItems'),
                    show: true,
                    allowCountdown: true,
                });
            }

            if (!_.isEmpty(tlModel)) {
                const defaultArr = tlModel.default;

                obj.lovs = defaultArr.map((val) => {
                    return { value: val, title: val };
                });

                languages.forEach((lang) => {
                    obj[`lovs_${lang}`] = [];

                    tlModel[lang].forEach((val, i) => {
                        if (val !== null) {
                            obj[`lovs_${lang}`].push({
                                value: defaultArr[i],
                                title: val,
                            });
                        }
                    });
                });
            }
        }

        // 'textList', 'dateList', 'numberList', 'dynList', 'dynTable'
        if (data.selectAll === true) {
            if (obj.tvar_meta) {
                obj.tvar_meta.selectAll = true;
            } else {
                obj.tvar_meta = { selectAll: true };
            }
        }
        // use Select multiple
        if (data.useSelectMultiple === true) {
            if (obj.tvar_meta) {
                obj.tvar_meta.useSelectMultiple = true;
            } else {
                obj.tvar_meta = { useSelectMultiple: true };
            }
        }
        // multiple selection search in assignment
        if (data.allowMultiselectSearchRight === true) {
            if (obj.tvar_meta) {
                obj.tvar_meta.allowMultiselectSearchRight = true;
            } else {
                obj.tvar_meta = { allowMultiselectSearchRight: true };
            }
        }
        // multiselect doubleHeight
        if (data.doubleHeight === true) {
            if (obj.tvar_meta) {
                obj.tvar_meta.doubleHeight = true;
            } else {
                obj.tvar_meta = { doubleHeight: true };
            }
        }
        // Customization
        if (data.customization) {
            // const fontSize = data.fontSize.value
            //     ? data.fontSize.value
            //     : data.fontSize;
            const customization = {};

            if (this.elementColorRef) {
                const elementColorVal = this.elementColorRef.state.color;

                if (
                    !_.isEmpty(elementColorVal) &&
                    elementColorVal !== '#ffffff'
                ) {
                    customization.elementColor = elementColorVal;
                }
            }
            if (this.fontColorRef) {
                const fontColorVal = this.fontColorRef.state.color;

                if (!_.isEmpty(fontColorVal) && fontColorVal !== '#555') {
                    customization.fontColor = fontColorVal;
                }
            }
            // if (fontSize !== 'M') {
            //     // !default medium size
            //     customization.fontSize = fontSize;
            // }
            // if (data.cursive) {
            //     customization.cursive = data.cursive;
            // }
            // if (data.upperLabel) {
            //     customization.upperLabel = data.upperLabel;
            // }
            if (!_.isEmpty(data.tasks)) {
                customization.tasks = data.tasks;
            }

            if (obj.tvar_meta) {
                obj.tvar_meta.customization = !_.isEmpty(customization)
                    ? customization
                    : null;
            } else {
                obj.tvar_meta = !_.isEmpty(customization)
                    ? { customization }
                    : null;
            }
        }

        // dynamic rows
        try {
            if (
                typeof data.scriptValue !== 'undefined' ||
                typeof data.columnsValue !== 'undefined'
            ) {
                JSON.parse(data.columnsValue);

                const script =
                    typeof data.scriptValue !== 'undefined'
                        ? data.scriptValue
                        : '';
                const tableDefinition =
                    typeof data.columnsValue !== 'undefined'
                        ? data.columnsValue
                        : '';

                if (obj.tvar_meta) {
                    obj.tvar_meta.script = script;
                    obj.tvar_meta.tableDefinition = tableDefinition;
                } else {
                    obj.tvar_meta = {
                        script: script,
                        tableDefinition: tableDefinition,
                    };
                }

                obj.value = dynamicRowsValues;
            } else {
                obj.value = data.value === '' ? null : data.value;
                // Number with decimals "10.00" -> 10
                if (obj.tvar_type === 'N') {
                    if (data.value === null) {
                        obj.value = null;
                    } else {
                        obj.value = Number(data.value);
                    }
                }
            }

            if (obj.tvar_meta) {
                obj.tvar_meta = JSON.stringify(obj.tvar_meta);
            }

            const { templateId, versionId, templateVariableId } =
                this.props.match.params;

            if (templateVariableId !== 'new' && !this.state.variableIsCloned) {
                obj.tvar_id = templateVariableId;
            }

            TemplateVariableApi.saveVariable({
                templateId: templateId,
                versionId: versionId,
                data: obj,
            })
                .then((payload) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrSaved'),
                    });

                    this.setState({ formIsPristine: true }, function () {
                        if (callback) {
                            callback();
                        } else if (close) {
                            this.goBack();
                        } else if (
                            templateVariableId === 'new' ||
                            this.state.variableIsCloned
                        ) {
                            // just save
                            if (this.state.variableIsCloned) {
                                TemplateVariableActions.setVariableIsCloned(
                                    false,
                                );
                            }

                            this.props.history.push(
                                `/templates/template/${templateId}/${versionId}/template-variable/${payload.id}`,
                            );
                        }
                    });
                })
                .catch((errorMessage) => {
                    if (
                        _.get(errorMessage, 'error.codeName') ===
                        'UNIQUE_CONSTRAINT'
                    ) {
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'warning',
                            show: true,
                            message: i18next.t('alrTempVarSaveSameNameFailed'),
                        });
                    } else {
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'alert',
                            message: i18next.t('alrTempVarSaveFailed'),
                            serverError: errorMessage,
                        });
                    }
                });
        } catch (e) {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'alert',
                message: i18next.t('alrJsonFailed'),
                serverError: e,
            });
        }
    }

    showUrlHelper(e) {
        if (e) {
            e.preventDefault();
        }
        this.setState({ urlHelperIsVisible: !this.state.urlHelperIsVisible });
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        const { templateId, versionId } = this.props.match.params;

        this.props.history.push(
            this.state.closePrevPath !== null
                ? this.state.closePrevPath
                : `/templates/template/${templateId}/${versionId}/variables`,
        );
    }

    codeAreaDynRowsColumns() {
        // TODO keep an eye on it during redesign
        // tas-1990 Hodnota nově vytvořených DŘ
        document.getElementById('beside-tabs-nav').querySelector('a.button-green')?.classList.toggle('no-active', true);
        document.getElementById('beside-tabs-nav').querySelector('a.button-white')?.classList.toggle('no-active', true);
    }

    // refresh preview of dynamic rows table
    refreshTablePreview() {
        try {
            const columnsValues = JSON.parse(
                this.refs.dynRowsColumns.getValue() || '{}',
            );
            TemplateVariableActions.setDynRowsColumnsValues(columnsValues);

            // wait for dynamic rows to be rendered
            setTimeout(() => {
                if (typeof this.refs.dynRowTable !== 'undefined') {
                    const script = this.refs.dynRowsScript.getValue();
                    ApiRequest.post(
                        `${document.location.origin}/babel-transform`,
                        JSON.stringify({ js: script }),
                    )
                        .then((payload) => {
                            this.refs.dynRowTable.performScript(payload.js, true);
                        })
                        .catch((errorMessage) => {
                            AlertsActions.addAlert({
                                type: 'alert',
                                message: `${i18next.t('thumbnail')} - (Babel transform error. Position: ${JSON.stringify(errorMessage.loc)})`,
                            });
                        });
                }
            });
        } catch (e) {
            AlertsActions.addAlert({
                type: 'alert',
                message: i18next.t('alrJsonFailed'),
                serverError: e,
            });
        }
    }

    changeDef(newDef) {
        if (newDef) {
            this.setState((prevState) => ({
                columnsDef: _.merge({}, prevState.columnsDef, newDef),
            }));
        }
    }

    changeDynTable(name, value) {
        TemplateVariableActions.changeDynamicTable(value.value);

        // reset column order and filter
        this.refs.dynTableCol.setValue(null);
        this.refs.dtOrder.setValue(null);
        this.refs.dtFilter.resetFilter();
    }

    // display inputs for text suggest box settings
    enableSuggestBox(name, value) {
        TemplateVariableActions.enableSuggestBox(value);

        if (value) {
            if (this.refs.numberBox.getValue()) {
                this.refs.numberBox.setValue(false);
            }
        }
    }

    getDtFilterActualVals() {
        const formData =
            this.refs.formVariable.refs.frmformVariable.getCurrentValues();
        let index = 0;
        const filterArr = [];

        while (typeof formData[`dtFilter-array${index}`] !== 'undefined') {
            const subFilter = {};
            subFilter.array = formData[`dtFilter-array${index}`];
            subFilter.condition = formData[`dtFilter-condition${index}`];
            subFilter.operator = formData[`dtFilter-operator${index}`];

            filterArr.push(subFilter);

            index++;
        }

        return filterArr;
    }

    showHideDynRowsHelper(e) {
        if (e) e.preventDefault();
        this.setState({
            dynRowsHelperIsVisible: !this.state.dynRowsHelperIsVisible,
        });
    }

    changeVarType(name, value) {
        TemplateVariableActions.changeVariableType(value.value);
    }

    changeNumberOfDecimals(componentName, nativeValue) {
        let decimals = nativeValue || 0;
        if (nativeValue > config.floatingPointPrecision) decimals = config.floatingPointPrecision;
        if (nativeValue < 0) decimals = 0;
        decimals = parseInt(decimals);
        TemplateVariableActions.changeNrOfDecimals(decimals);
    }

    enableCkEditor(name, value) {
        TemplateVariableActions.enableCkEditor(value);
        TemplateVariableActions.updateItemValue(this.refs.longText.getValue());
    }

    async enableAdvancedEditor(name, value) {
        await TemplateVariableActions.updateItemValue(
            this.refs.longText.getValue(),
        );
        TemplateVariableActions.enableAdvancedCkEditor(value);
    }

    dynamicListChange(name, value) {
        TemplateVariableActions.setDlAttribute(value.value);
    }

    varMultiChange(name, value) {
        TemplateVariableActions.setVarMulti(value);
    }

    useSelectMultipleChange(name, value) {
        TemplateVariableActions.setUseSelectMultiple(value);
    }

    setTextTypePassword(name, value) {
        TemplateVariableActions.setTextTypePassword(value);
    }

    checkWithSuggester(name, value) {
        if (value) {
            if (this.refs.suggestBox.getValue()) {
                TemplateVariableActions.enableSuggestBox(false);
                this.refs.suggestBox.setValue(false);
            }
        }
    }

    enableCustomization(name, value) {
        if (_.isEmpty(this.state.templateTasksOptions)) {
            const { templateId, versionId } = this.props.match.params;

            TemplateVariableActions.fetchTemplateTasks(templateId, versionId);
        }
        TemplateVariableActions.enableCustomization(value);
    }

    handleErrors(err) {
        if (err) {
            console.log(err);
        }
    }

    pushMultiBox(multiBox) {
        const multiBoxesClone = _.cloneDeep(this.state.multiBoxesArr);
        const findMultiBox = _.find(multiBoxesClone, ['name', multiBox.name]);

        if (!findMultiBox) {
            multiBoxesClone.push(multiBox);
        } else {
            findMultiBox.checked = multiBox.checked;
        }

        this.setState({ multiBoxesArr: multiBoxesClone }, () => {
            this.setState({
                multiBoxesLoaded: !_.find(multiBoxesClone, ['checked', false]),
            });
        });
    }

    changeVersion(name, newVersion) {
        this.setState({ varVersion: newVersion.value });
    }

    openImportTemplateModal = (e) => {
        e.preventDefault();
        this.setState({ importModalIsOpen: true });
    };

    closeImportModal = () => {
        this.setState({ importModalIsOpen: false });
    };

    onLoad = (fileContent) => {
        try {
            const rows = JSON.parse(fileContent || '{}').gfxTasks || [];
            const names = rows.map((r) => r.mmo_name);
            const textListRowsModel = _.cloneDeep(this.textListRowsRef.state.model);
            textListRowsModel.default = textListRowsModel.default.concat(names);
            TemplateVariableActions.updateTextListRows(textListRowsModel);
        } catch (e) {
            AlertsActions.addAlert({
                type: 'alert',
                message: i18next.t('notInRightormat'),
            });
        }
    };

    insertSnippet(e, ref) {
        const value = e.target.getAttribute('data-value');

        if (this.refs[ref] && this.refs[ref].insertSnippet) {
            this.refs[ref].insertSnippet(value);
        }

        this.setState({
            menuBoxIsOpen: false,
            menuBoxColumnsIsOpen: false,
        });
    }

    openMenuBox(ref) {
        const inputNode = ReactDOM.findDOMNode(this.refs[ref]);
        const componentBottomPos = inputNode.getBoundingClientRect().bottom; // bottom edge from the top

        const callback = () => {
            if (this.state.menuBoxIsOpen) {
                const menuBoxHeight = this.menuBox.offsetHeight;

                this.setState({
                    menuBoxHeight: menuBoxHeight,
                    menuBoxTopOrientation:
                        componentBottomPos + menuBoxHeight + 10 >
                        this.state.windowHeight,
                });
            }

            if (this.state.menuBoxColumnsIsOpen) {
                const menuBoxHeight = this.menuBoxColumns.offsetHeight;

                this.setState({
                    menuBoxColumnsHeight: menuBoxHeight,
                    menuBoxColumnsTopOrientation:
                        componentBottomPos + menuBoxHeight + 10 >
                        this.state.windowHeight,
                });
            }
        };

        if (ref === 'dynRowsScript') {
            this.setState(
                { menuBoxIsOpen: !this.state.menuBoxIsOpen },
                callback,
            );
        } else {
            this.setState(
                { menuBoxColumnsIsOpen: !this.state.menuBoxColumnsIsOpen },
                callback,
            );
        }
    }

    documentClick(event) {
        if (!this.menuBoxButton?.contains(event.target)) {
            this.setState({ menuBoxIsOpen: false });
        }
        if (!this.menuBoxButtonColumns?.contains(event.target)) {
            this.setState({ menuBoxColumnsIsOpen: false });
        }
    }

    handleResize() {
        this.setState({ windowHeight: window.innerHeight });

        // menuBox
        if (this.refs.dynRowsScript) {
            const inputNode = ReactDOM.findDOMNode(this.refs.dynRowsScript);
            const componentBottomPos = inputNode.getBoundingClientRect().bottom; // bottom edge from the top

            this.setState({
                menuBoxTopOrientation:
                    componentBottomPos + this.state.menuBoxHeight + 10 >
                    window.innerHeight,
            });
        }

        // menuBoxColumns
        if (this.refs.dynRowsColumns) {
            const inputNode = ReactDOM.findDOMNode(this.refs.dynRowsColumns);
            const componentBottomPos = inputNode.getBoundingClientRect().bottom; // bottom edge from the top

            this.setState({
                menuBoxColumnsTopOrientation:
                    componentBottomPos + this.state.menuBoxColumnsHeight + 10 >
                    window.innerHeight,
            });
        }
    }

    render() {
        const { templateVariableId } = this.props.match.params;

        const sharingOptions = [
            { value: 'N', title: i18next.t('no') },
            { value: 'Y', title: i18next.t('forReading') },
            { value: 'W', title: i18next.t('forReadWrite') },
        ];

        const fontSizeOptions = [
            { value: 'S', title: i18next.t('small') },
            {
                value: 'M',
                title: `${i18next.t('medium')} (${i18next.t('default')})`,
            },
            { value: 'L', title: i18next.t('large') },
        ];

        const { items } = this.state;

        let dynListValue;
        if (typeof items !== 'undefined' && items !== null) {
            if (items.tvar_attribute !== null) {
                dynListValue = items.tvar_attribute;
            } else if (items.dlist_name !== null) {
                dynListValue = `DL|${items.dlist_name}`;
            } else {
                dynListValue = this.state.dynListOptions[0];
            }
        }

        const getValue = function (type, attr, meta) {
            if (attr === 'F' || attr === 'S') {
                // attachment list, sequence
                return type + attr;
            }
            if (attr === 'M' || attr === 'T') {
                // long text, table
                return attr + type;
            }
            if (type === 'N' && JSON.parse(meta || '{}').isCheckbox) {
                return 'CB';
            }
            return type;
        };

        const getDynColsValue = function (value, key) {
            if (typeof value !== 'undefined' && value !== null) {
                if (key == 'script') {
                    return JSON.parse(value || '{}').script || '';
                }
                return (
                    JSON.parse(value || '{}').tableDefinition ||
                    '{"table":{}, "columns": []}'
                );
            }
            if (key == 'script') {
                return '';
            }
            return '{"table":{}, "columns": []}';
        };

        const getDatesSettings = function (tvarMeta, settingsKey) {
            if (typeof tvarMeta !== 'undefined' && tvarMeta !== null) {
                return JSON.parse(tvarMeta || '{}')[settingsKey];
            }
        };

        const getDatesDisabled = function (tvarMeta, settingsKey) {
            if (typeof tvarMeta !== 'undefined' && tvarMeta !== null) {
                const dates = JSON.parse(tvarMeta || '{}')[settingsKey] || [];
                const datesObjects = [];
                dates.map((d) => {
                    if (typeof d !== 'object') {
                        datesObjects.push({ title: d, value: d });
                    }
                });
                return !_.isEmpty(datesObjects) ? datesObjects : dates;
            }
        };

        const getDateTimeSettings = function (value, key) {
            if (typeof value !== 'undefined' && value !== null) {
                let showTimeInOverview = JSON.parse(value || '{}').showTime;

                if (typeof showTimeInOverview === 'undefined') {
                    showTimeInOverview = true; // defaultly show timestamp
                }
                return showTimeInOverview;
            }
            return true; // defaultly show timestamp
        };

        const { columnsDef } = this.state;
        const tableDef = columnsDef['table'] || {};
        const createDynamicRows = (i) => {
            const columns = [];
            const cDef = i.columns;
            const tDef = i.table || {};
            const language = LoggedUserStore.getState().userLocale;

            for (const key in cDef) {
                if (cDef.hasOwnProperty(key)) {
                    const c = cDef[key];
                    const cParams = columnsDef[c.name]; // ze skriptu z changeDef
                    let columnTitle = c.translations
                        ? c.translations[language.substring(0, 2)] || c.caption
                        : c.caption;

                    if (cParams && Object.prototype.hasOwnProperty.call(cParams, 'caption')) {
                        columnTitle = cParams.caption;
                    }
                    if (cParams && Object.prototype.hasOwnProperty.call(cParams, 'translations')) {
                        columnTitle = cParams.translations[language.substring(0, 2)] || columnTitle;
                    }

                    let readOnly =
                        cParams && cParams.hasOwnProperty('readOnly')
                            ? cParams.readOnly
                            : c.readOnly;
                    readOnly = c.type === 'file' ? true : readOnly;

                    columns.push(
                        <Column
                            key={`col-${c.name}`}
                            title={columnTitle}
                            name={c.name}
                            type={c.type}
                            thCss={
                                cParams && cParams.hasOwnProperty('thCss')
                                    ? cParams.thCss
                                    : c.thCss
                            }
                            css={
                                cParams && cParams.hasOwnProperty('css')
                                    ? cParams.css
                                    : c.css
                            }
                            isVisible={
                                cParams && cParams.hasOwnProperty('isVisible')
                                    ? cParams.isVisible
                                    : c.isVisible
                            }
                            filterChoices={
                                cParams && cParams.hasOwnProperty('choices')
                                    ? cParams.choices
                                    : c.choices
                            }
                            duty={
                                cParams && cParams.hasOwnProperty('duty')
                                    ? cParams.duty
                                    : c.duty
                            }
                            value={
                                cParams && cParams.hasOwnProperty('value')
                                    ? cParams.value
                                    : c.value
                            }
                            readOnly={readOnly}
                            dataFilter={
                                cParams && cParams.hasOwnProperty('urlFilter')
                                    ? cParams.urlFilter
                                    : c.urlFilter
                            }
                            dataUrl={
                                cParams && cParams.hasOwnProperty('dataUrl')
                                    ? cParams.dataUrl
                                    : c.dataUrl
                            }
                            dataStructure={
                                cParams &&
                                cParams.hasOwnProperty('dataStructure')
                                    ? cParams.dataStructure
                                    : c.dataStructure
                            }
                            dataStructureRenderer={
                                cParams &&
                                cParams.hasOwnProperty('dataStructureRenderer')
                                    ? cParams.dataStructureRenderer
                                    : c.dataStructureRenderer
                            }
                            currencySymbol={
                                cParams && cParams.hasOwnProperty('symbol')
                                    ? cParams.symbol
                                    : c.symbol
                            }
                            nrOfDecimals={
                                cParams &&
                                cParams.hasOwnProperty('nrOfDecimals')
                                    ? cParams.nrOfDecimals
                                    : c.nrOfDecimals
                            }
                            colIndex={
                                cParams && cParams.hasOwnProperty('colIndex')
                                    ? cParams.colIndex
                                    : c.colIndex
                            }
                            meta={getDrColumnMeta(cParams, c)}
                            suggestId={
                                cParams && cParams.hasOwnProperty('suggestId')
                                    ? cParams.suggestId
                                    : c.suggestId
                            }
                            rows={
                                cParams && cParams.hasOwnProperty('rows')
                                    ? cParams.rows
                                    : c.rows
                            }
                            startDate={
                                cParams && cParams.hasOwnProperty('startDate')
                                    ? cParams.startDate
                                    : c.startDate
                            }
                            endDate={
                                cParams && cParams.hasOwnProperty('endDate')
                                    ? cParams.endDate
                                    : c.endDate
                            }
                            defaultViewDate={
                                cParams &&
                                cParams.hasOwnProperty('defaultViewDate')
                                    ? cParams.defaultViewDate
                                    : c.defaultViewDate
                            }
                            onlyWorkingDays={
                                cParams &&
                                cParams.hasOwnProperty('onlyWorkingDays')
                                    ? cParams.onlyWorkingDays
                                    : c.onlyWorkingDays
                            }
                            datesDisabled={
                                cParams &&
                                cParams.hasOwnProperty('datesDisabled')
                                    ? cParams.datesDisabled
                                    : c.datesDisabled
                            }
                            addToTotal={
                                cParams && cParams.hasOwnProperty('addToTotal')
                                    ? cParams.addToTotal
                                    : c.addToTotal
                            }
                            dateWithoutTime={c.dateWithoutTime || false} // only from definition, default false
                            copy={
                                cParams && cParams.hasOwnProperty('copy')
                                    ? cParams.copy
                                    : c.copy
                            }
                            selectOnlyHours={c.selectOnlyHours || false} // only from definition, default false
                        />,
                    );
                }
            }

            let dynRowsKey;
            if (isNaN(templateVariableId)) {
                // this.props.match.params.templateVariableId == 'new'
                dynRowsKey = guid();
            } else {
                dynRowsKey = templateVariableId;
            }

            const side = tableDef.side || tDef.side || 'center';

            const dr = (
                <DynamicRows
                    side={side}
                    key={dynRowsKey}
                    name={dynRowsKey}
                    usage="W"
                    showTotals={
                        tableDef.hasOwnProperty('showTotals')
                            ? tableDef.showTotals
                            : tDef.showTotals
                    }
                    changeDef={this.changeDef}
                    ref="dynRowTable"
                    onChange={this.formChanged}
                    defaultValues={this.state.dynRowsDefultValues}
                    script={this.state.dynRowsScript}
                    applyDuty={false}
                    applyHidingColumns={false}
                    applyShowAddRemove={false}
                    showAddRemove={tDef.showAddRemove}
                    showRemove={tDef.showRemove}
                    showClone={tDef.showClone}
                    showAdd={tDef.showAdd}
                    visibilityGroups={['dynRows']}
                    formIsPristine={this.state.formIsPristine}
                    columnsDef={cDef}
                    handleErrors={this.handleErrors}
                    tvarName={this.state.variableHeading}
                    isButtonsEnabled
                    isTemplateDr
                    dynCondsScriptIsRunning={false}
                    canRunDrScripts
                    language={language.substring(0, 2)}
                    resizable={tDef.resizable}
                >
                    {columns}
                </DynamicRows>
            );

            if (side === 'right') {
                return (
                    <WrapComponent
                        key="dynRowsWrapper"
                        side="center"
                        visibilityGroups={['dynRows']}
                    >
                        <EmptyComponent side="left" key="emptyLeftComponent" />
                        {dr}
                    </WrapComponent>
                );
            }
            return (
                <WrapComponent
                    key="dynRowsWrapper"
                    side="center"
                    visibilityGroups={['dynRows']}
                >
                    {dr}
                </WrapComponent>
            );
        };

        const { languages } = LoggedUserStore.getState();

        const getTextListRowsColumns = () => {
            const textListRowsColumns = languages.map((lang, i) => {
                return (
                    <Column
                        key={`col-${lang}`}
                        title={lang}
                        name={lang}
                        type="text"
                    />
                );
            });

            textListRowsColumns.unshift(
                <Column
                    key="col-def"
                    title={`${i18next.t('default')} ${i18next.t('value')}`}
                    name="default"
                    type="text"
                    duty="mandatory"
                />,
            );

            return textListRowsColumns;
        };

        const { variableType } = this.state;
        const showDynListUrl =
            variableType === 'DL' &&
            (this.state.dlAttribute === 'U' ||
                this.state.dlAttribute === 'R') &&
            !this.state.varIsMulti;
        const { customization } = this.state;

        function getCustomizationVal(key, valueIfKeyNotExist) {
            if (customization !== null && customization[key]) {
                return customization[key];
            }

            return valueIfKeyNotExist;
        }

        const saveButtonIsEnabled = !_.isEmpty(this.state.multiBoxesArr)
            ? !this.state.loading &&
              this.state.multiBoxesLoaded &&
              this.state.customizationTasksLoaded
            : !this.state.loading;

        const tvarMeta = JSON.parse(items.tvar_meta || '{}');

        let { varVersion } = this.state;
        if (typeof varVersion === 'undefined') {
            varVersion = items.tvar_design_version;
        }

        return (
            <DocumentTitle title={i18next.t('varTemp')}>
                <TabsWrapper>
                    {this.state.dynRowsHelperIsVisible && (
                        <Helper
                            type="dynamicRows"
                            closeHelper={this.showHideDynRowsHelper}
                        />
                    )}
                    {this.state.urlHelperIsVisible && (
                        <Helper
                            type="urlDocs"
                            closeHelper={this.showUrlHelper}
                        />
                    )}
                    <Heading
                        title={
                            templateVariableId !== 'new'
                                ? this.state.variableHeading
                                : i18next.t('addVariable')
                        }
                    >
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                enableOn={saveButtonIsEnabled}
                                onClick={this.saveVariable.bind(null, true)}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-download-5"
                                buttonColor="white"
                                enableOn={saveButtonIsEnabled}
                                onClick={this.saveVariable.bind(null, false)}
                                tooltipCode="ttJustSave"
                            >
                                {i18next.t('justSave')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs>
                        <Tabs.Tab
                            key="templateVariable"
                            title={i18next.t('variable')}
                            name="tabTemplateVariable"
                            showLoader
                            loaded={!this.state.loading}
                        >
                            <TabsButtonsOther key="buttonsSettings">
                                {variableType == 'DR' && (
                                    <TabsButton
                                        key="help"
                                        link="/"
                                        icon="icon-bubble-ask-2"
                                        isActive
                                        onClick={this.showHideDynRowsHelper}
                                    >
                                        {i18next.t('help')}
                                    </TabsButton>
                                )}
                            </TabsButtonsOther>
                            <TabsButtonsOther key="buttonsUrlDetail">
                                {variableType === 'T' &&
                                    this.state.suggestBoxIsEnabled && (
                                    <TabsButton
                                        icon="icon-bubble-ask-2"
                                        key="showUrlDetails"
                                        isActive
                                        onClick={this.showUrlHelper}
                                    >
                                        {i18next.t('help')}
                                    </TabsButton>
                                )}
                            </TabsButtonsOther>
                            <Form
                                key={`formVariable-${templateVariableId}`} // re-mount form after saving new variable without closing
                                ref="formVariable"
                                name="formVariable"
                                onValidSubmit={this.handleValidSubmit}
                                onChange={this.formChanged}
                                data-classes={cx(
                                    {
                                        'with-dynamic-rows-template':
                                            !_.isEmpty(
                                                this.state.dynRowsColumnsValues,
                                            ),
                                    },
                                    { 'is-mobile': this.isMobileDevice },
                                )}
                                addForm5Class // due to DR
                            >
                                <Text
                                    key="name"
                                    label={`${i18next.t('defaultVariableName')}:`}
                                    value={items.tvar_name}
                                    side="left"
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <Label
                                    key="info"
                                    label=" "
                                    side="left"
                                    value={i18next.t('doNotShowVariablesWith_')}
                                />
                                <ShowHideComponent key="langLabel" side="left">
                                    {languages.map((lang, i) => {
                                        return (
                                            <Text
                                                key={`tvarName${lang}`}
                                                name={`tvar_name_${lang}`}
                                                label={`${i18next.t('variableName')}:`}
                                                value={
                                                    items[`tvar_name_${lang}`]
                                                }
                                                lblLang={lang}
                                            />
                                        );
                                    })}
                                </ShowHideComponent>
                                <TextArea
                                    key="tvarTooltip"
                                    label={`${i18next.t('defaultLblShe', { label: '$t(fsTooltip)' })}:`}
                                    side="left"
                                    rows={3}
                                    value={items.tvar_tooltip}
                                    showComponentLabel
                                />
                                <ShowHideComponent key="langDesc" side="left">
                                    {languages.map((lang, i) => {
                                        return (
                                            <TextArea
                                                key={`tvarTooltip${lang}`}
                                                name={`tvar_tooltip_${lang}`}
                                                label={`${i18next.t('tooltip')}:`}
                                                side="left"
                                                rows={3}
                                                value={
                                                    items[
                                                        `tvar_tooltip_${lang}`
                                                    ]
                                                }
                                                lblLang={lang}
                                                showComponentLabel
                                            />
                                        );
                                    })}
                                </ShowHideComponent>
                                <SelectBox
                                    key="type"
                                    ref="varType"
                                    label={`${i18next.t('variableType')}:`}
                                    value={getValue(
                                        items.tvar_type,
                                        items.tvar_attribute,
                                        items.tvar_meta,
                                    )}
                                    options={getVariableTypes()}
                                    side="left"
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                    toShowGroups={{
                                        T: 'text',
                                        MT: 'longText',
                                        D: 'date',
                                        N: 'number',
                                        CB: 'checkbox',
                                        LT: 'textList',
                                        LD: 'dateList',
                                        LN: 'numberList',
                                        TF: 'attach',
                                        DL: 'dynList',
                                        DT: 'dynTable',
                                        DR: 'dynRows',
                                    }}
                                    onComponentChange={this.changeVarType}
                                    nullable={false}
                                />
                                {templateVariableId !== 'new' && (
                                    <Checkbox
                                        key="updateInstances"
                                        label={i18next.t('updateInstances')}
                                    />
                                )}
                                <SelectBox
                                    key="isShared"
                                    label={`${i18next.t('sharedVar')}:`}
                                    options={sharingOptions}
                                    value={
                                        items.tvar_is_shared ||
                                        sharingOptions[0].value
                                    }
                                    side="left"
                                    nullable={false}
                                    showComponentLabel
                                />
                                <Checkbox
                                    key="tvarBusiness"
                                    label={i18next.t('businessVariable')}
                                    value={items.tvar_business === 'Y'}
                                    showComponentLabel
                                />
                                <Checkbox
                                    key="tvarIsShredderable"
                                    label={i18next.t('shredableVar')}
                                    value={items.tvar_is_shredable === 'Y'}
                                    showComponentLabel
                                />
                                <Checkbox
                                    key="copyToMultiinstances"
                                    label={i18next.t('copyToMultiinstances')}
                                    value={
                                        templateVariableId !== 'new'
                                            ? items.tvar_copy_snapshot === 'Y'
                                            : false
                                    }
                                    showComponentLabel
                                />
                                <Label
                                    key="dynRowsInfo"
                                    label=" "
                                    visibilityGroups={['dynRows']}
                                    side="left"
                                    value={i18next.t('defChangeVarInfluence')}
                                />
                                <Text
                                    key="value"
                                    label={`${i18next.t('text')}:`}
                                    side="left"
                                    value={items.tvar_value}
                                    visibilityGroups={['text']}
                                />
                                {!this.state.suggestBoxIsEnabled && (
                                    <Checkbox
                                        key="textTypePassword"
                                        label={`${i18next.t('displayAsPassword')}:`}
                                        value={this.state.textTypePasswordIsEnabled}
                                        visibilityGroups={['text']}
                                        onChange={this.setTextTypePassword}
                                        showComponentLabel
                                    />
                                )}
                                {!this.state.textTypePasswordIsEnabled && (
                                    <Checkbox
                                        key="suggestBox"
                                        ref="suggestBox"
                                        label={`${i18next.t('textSuggest')}:`}
                                        value={this.state.textSuggestBox !== null}
                                        visibilityGroups={['text']}
                                        onChange={this.enableSuggestBox}
                                        showComponentLabel
                                    />
                                )}
                                {this.state.suggestBoxIsEnabled && (
                                    <WrapComponent
                                        key="suggestBox-sett"
                                        side="left"
                                        visibilityGroups={['text']}
                                    >
                                        <Text
                                            key="suggestBoxUrl"
                                            name="suggestBoxUrl"
                                            label={`${i18next.t('url')}:`}
                                            value={
                                                this.state.textSuggestBox !==
                                                null
                                                    ? this.state.textSuggestBox
                                                        .apiUrl
                                                    : ''
                                            }
                                            required
                                            validationErrors={{
                                                isDefaultRequiredValue:
                                                    i18next.t('isRequired'),
                                            }}
                                            showComponentLabel
                                        />
                                        <Text
                                            key="suggestBoxProp"
                                            name="suggestBoxProp"
                                            label={`${i18next.t('property')}:`}
                                            value={
                                                this.state.textSuggestBox !==
                                                null
                                                    ? this.state.textSuggestBox
                                                        .prop
                                                    : ''
                                            }
                                            required
                                            validationErrors={{
                                                isDefaultRequiredValue:
                                                    i18next.t('isRequired'),
                                            }}
                                            showComponentLabel
                                        />
                                    </WrapComponent>
                                )}

                                <Checkbox
                                    key="numberBox"
                                    ref="numberBox"
                                    label={`${i18next.t('onlyNumbers')}:`}
                                    value={this.state.textNumberBox}
                                    visibilityGroups={['text']}
                                    showComponentLabel
                                    onChange={this.checkWithSuggester}
                                />
                                {!this.state.ckEditorIsEnabled && (
                                    <TextArea
                                        key="longText"
                                        name="value"
                                        ref="longText"
                                        label={`${i18next.t('textMultipleLines')}:`}
                                        side="left"
                                        value={items.tvar_value}
                                        rows={3}
                                        visibilityGroups={['longText']}
                                    />
                                )}
                                {this.state.ckEditorIsEnabled && (
                                    <CKEditor
                                        key={`longText${this.state.ckEditorKey}`}
                                        name="value"
                                        ref="longText"
                                        label={`${i18next.t('textMultipleLines')}:`}
                                        side="left"
                                        value={items.tvar_value || ''}
                                        visibilityGroups={['longText']}
                                        advancedEditor={
                                            this.state.advancedCkEditor
                                        }
                                    />
                                )}
                                <Checkbox
                                    key="ckEditor"
                                    label={`${i18next.t('editor')}:`}
                                    value={this.state.ckEditorIsEnabled}
                                    visibilityGroups={['longText']}
                                    showComponentLabel
                                    onChange={this.enableCkEditor}
                                />
                                {this.state.ckEditorIsEnabled && (
                                    <Checkbox
                                        key="advancedEditor"
                                        label={`${i18next.t('advancedEditor')}:`}
                                        value={this.state.advancedCkEditor}
                                        visibilityGroups={['longText']}
                                        showComponentLabel
                                        onChange={this.enableAdvancedEditor}
                                    />
                                )}

                                <WrapComponent
                                    key="dynRowsDefinition"
                                    side="center"
                                    visibilityGroups={['dynRows']}
                                >
                                    <CodeArea
                                        key="dynRowsScript"
                                        ref="dynRowsScript"
                                        name="scriptValue"
                                        label={`${i18next.t('script')}:`}
                                        side="left"
                                        height="450px"
                                        value={getDynColsValue(
                                            items.tvar_meta,
                                            'script',
                                        )}
                                        upperLabel
                                        helperText={`${i18next.t('ideHelp')}`}
                                        hints="dynRowsScript"
                                        visibilityGroups={['dynRows']}
                                        readonly={
                                            (
                                                this.state.dynRowsColumnsValues
                                                    .table || {}
                                            ).attachScriptToConds
                                        }
                                        htmlLabelButton={
                                            <span
                                                className="change-button menu-box-button icon snippet"
                                                ref={(c) =>
                                                    (this.menuBoxButton = c)}
                                                onClick={() =>
                                                    this.openMenuBox(
                                                        'dynRowsScript',
                                                    )}
                                                title={i18next.t(
                                                    'insertSnippet',
                                                )}
                                            >
                                                <i className="icon icon2-script" />
                                                <div
                                                    ref={(c) =>
                                                        (this.menuBox = c)}
                                                    className={cx(
                                                        'menu-box',
                                                        {
                                                            open: this.state
                                                                .menuBoxIsOpen,
                                                        },
                                                        {
                                                            'open-up':
                                                                this.state
                                                                    .menuBoxTopOrientation,
                                                        },
                                                    )}
                                                >
                                                    <ul>
                                                        {ideHints.dynRowsScript.map(
                                                            (option, i) => (
                                                                <li key={i}>
                                                                    <a
                                                                        ref={(
                                                                            c,
                                                                        ) =>
                                                                            (this.option =
                                                                                c)}
                                                                        data-value={
                                                                            option.text
                                                                        }
                                                                        onClick={(
                                                                            e,
                                                                        ) =>
                                                                            this.insertSnippet(
                                                                                e,
                                                                                'dynRowsScript',
                                                                            )}
                                                                    >
                                                                        {
                                                                            option.displayText
                                                                        }
                                                                    </a>
                                                                </li>
                                                            ),
                                                        )}
                                                    </ul>
                                                </div>
                                            </span>
                                        }
                                    />
                                    <CodeArea
                                        key="dynRowsColumns"
                                        ref="dynRowsColumns"
                                        name="columnsValue"
                                        label={`${i18next.t('dynRowsDefinition')}:`}
                                        side="right"
                                        height="450px"
                                        value={getDynColsValue(items.tvar_meta)}
                                        onFocus={this.codeAreaDynRowsColumns}
                                        onBlur={this.refreshTablePreview}
                                        upperLabel
                                        helperText={`${i18next.t('ideHelp')}`}
                                        hints="dynRowsTable"
                                        required
                                        validationErrors={{
                                            isDefaultRequiredValue:
                                                i18next.t('isRequired'),
                                        }}
                                        language="json"
                                        htmlLabelButton={
                                            <span
                                                className="change-button menu-box-button icon snippet"
                                                ref={(c) =>
                                                    (this.menuBoxButtonColumns =
                                                        c)}
                                                onClick={() =>
                                                    this.openMenuBox(
                                                        'dynRowsColumns',
                                                    )}
                                                title={i18next.t(
                                                    'insertSnippet',
                                                )}
                                            >
                                                <i className="icon icon2-script" />
                                                <div
                                                    ref={(c) =>
                                                        (this.menuBoxColumns =
                                                            c)}
                                                    className={cx(
                                                        'menu-box',
                                                        {
                                                            open: this.state
                                                                .menuBoxColumnsIsOpen,
                                                        },
                                                        {
                                                            'open-up':
                                                                this.state
                                                                    .menuBoxColumnsTopOrientation,
                                                        },
                                                    )}
                                                >
                                                    <ul>
                                                        {ideHints.dynRowsTable.map(
                                                            (option, i) => (
                                                                <li key={i}>
                                                                    <a
                                                                        ref={(
                                                                            c,
                                                                        ) =>
                                                                            (this.option =
                                                                                c)}
                                                                        data-value={
                                                                            option.text
                                                                        }
                                                                        onClick={(
                                                                            e,
                                                                        ) =>
                                                                            this.insertSnippet(
                                                                                e,
                                                                                'dynRowsColumns',
                                                                            )}
                                                                    >
                                                                        {
                                                                            option.displayText
                                                                        }
                                                                    </a>
                                                                </li>
                                                            ),
                                                        )}
                                                    </ul>
                                                </div>
                                            </span>
                                        }
                                    />
                                </WrapComponent>
                                <Label
                                    key="dynRow"
                                    label={`${i18next.t('thumbnail')}:`}
                                    visibilityGroups={['dynRows']}
                                    side="center"
                                />
                                <WrapComponent
                                    key="refrButton"
                                    visibilityGroups={['dynRows']}
                                    side="center"
                                >
                                    <span
                                        key="refrButtonSpan"
                                        className="in-form-button icon icon-sync-2"
                                        onClick={this.refreshTablePreview}
                                    >
                                        <span>{i18next.t('refresh')}</span>
                                    </span>
                                </WrapComponent>
                                {!_.isEmpty(this.state.dynRowsColumnsValues) &&
                                    createDynamicRows(
                                        this.state.dynRowsColumnsValues,
                                    )}
                                <Calendar
                                    key="value"
                                    label={`${i18next.t('date')}:`}
                                    side="left"
                                    value={items.tvar_value}
                                    visibilityGroups={['date']}
                                />

                                <Checkbox
                                    key="dateIsWithoutTime"
                                    label={`${i18next.t('dateWithoutTime')}:`}
                                    side="left"
                                    showComponentLabel
                                    value={items.tvar_date_without_time === 'Y'}
                                    visibilityGroups={['date']}
                                />

                                <Calendar
                                    key="startDate"
                                    label={`${i18next.t('startCalDate')}:`}
                                    side="right"
                                    showComponentLabel
                                    value={tvarMeta.startDate}
                                    visibilityGroups={['date']}
                                />
                                <Calendar
                                    key="endDate"
                                    label={`${i18next.t('endCalDate')}:`}
                                    side="right"
                                    showComponentLabel
                                    value={tvarMeta.endDate}
                                    visibilityGroups={['date']}
                                />
                                <Checkbox
                                    key="futureDatesSettings"
                                    label={`${i18next.t('useOnlyFutureDates')}:`}
                                    side="right"
                                    showComponentLabel
                                    value={getDatesSettings(
                                        items.tvar_meta,
                                        'useOnlyFutureDates',
                                    )}
                                    visibilityGroups={['date']}
                                />
                                <Checkbox
                                    key="onlyWorkingDays"
                                    label={`${i18next.t('onlyWorkingDays')}:`}
                                    side="right"
                                    showComponentLabel
                                    value={getDatesSettings(
                                        items.tvar_meta,
                                        'onlyWorkingDays',
                                    )}
                                    visibilityGroups={['date']}
                                />
                                <MultiBoxSingle
                                    key="datesDisabled"
                                    label={`${i18next.t('datesDisabled')}:`}
                                    side="right"
                                    showComponentLabel
                                    visibilityGroups={['date']}
                                    value={
                                        items.tvar_type === 'D'
                                            ? getDatesDisabled(
                                                items.tvar_meta,
                                                'datesDisabled',
                                            )
                                            : []
                                    }
                                    multiBoxType="LD"
                                />
                                {/* <SelectBox key="datesDisabledDynTable" label={i18next.t('table') + ":"} side="right" visibilityGroups={['date']}
                                           options={this.state.dynTablesOptions} nullable={false}
                                           value={items.dlist_name} onChange={this.changeDynTable} required
                                           validationErrors={{isDefaultRequiredValue: i18next.t('isRequired')}} />
                                <SelectBox key="datesDisabledDynTableCol" ref="dynTableCol" label={i18next.t('column') + ":"} side="right"
                                           visibilityGroups={['date']}
                                           options={this.state.dynTableColumnsOptions} nullable={false}
                                           value={items.tvar_col_index} required
                                           validationErrors={{isDefaultRequiredValue: i18next.t('isRequired')}} /> */}
                                <Checkbox
                                    key="showTime"
                                    label={`${i18next.t('showTime')}:`}
                                    side="left"
                                    showComponentLabel
                                    value={getDateTimeSettings(items.tvar_meta)}
                                    visibilityGroups={['date']}
                                />
                                <TextNum
                                    key="value"
                                    name="value"
                                    label={`${i18next.t('number')}:`}
                                    side="left"
                                    value={items.tvar_value}
                                    validations={{ isNumeric: true }}
                                    validationErrors={{
                                        isNumeric: i18next.t('notNumber'),
                                    }}
                                    visibilityGroups={['number']}
                                    numberOfDecimals={
                                        this.state.numberOfDecimals
                                    }
                                />
                                <TextNum
                                    key="nrOfDecimalDigits"
                                    name="nrOfDecimalDigits"
                                    label={`${i18next.t('nrOfDecimalDigits')}:`}
                                    value={
                                        this.state.nrOfDecimals !== null
                                            ? this.state.nrOfDecimals
                                            : 0
                                    }
                                    visibilityGroups={['number']}
                                    side="left"
                                    validations={{
                                        isInt: true,
                                        maxValue: config.floatingPointPrecision,
                                    }}
                                    validationErrors={{
                                        isInt: i18next.t('notIntNumber'),
                                        maxValue: `${i18next.t('maxNumberOfDecimals')}: ${config.floatingPointPrecision}`,
                                    }}
                                    onChange={this.changeNumberOfDecimals}
                                    showComponentLabel
                                />
                                <SelectBox
                                    key="alignment"
                                    label={`${i18next.t('variableAlignment')}:`}
                                    helperText={i18next.t('variableAlignmentHelp')}
                                    options={[
                                        {
                                            title: i18next.t('variableAlignmentRight'),
                                            value: 'R',
                                        },
                                        {
                                            title: i18next.t('variableAlignmentLeft'),
                                            value: 'L',
                                        },
                                    ]}
                                    nullable={false}
                                    value={tvarMeta.alignment || 'R'}
                                    showComponentLabel
                                    visibilityGroups={['number']}
                                />
                                <Checkbox
                                    key="value"
                                    label={`${i18next.t('value')}:`}
                                    value={!!items.tvar_value}
                                    visibilityGroups={['checkbox']}
                                />
                                <TextListRows
                                    key="tvarLovTexts"
                                    ref={(r) => {
                                        this.textListRowsRef = r;
                                    }}
                                    name="tvarLovTexts"
                                    label={`${i18next.t('textList')}:`}
                                    side="center"
                                    visibilityGroups={['textList']}
                                    defaultValues={this.state.textListRowsValue}
                                    onChange={this.formChanged}
                                >
                                    {getTextListRowsColumns()}
                                </TextListRows>
                                <MultiBoxSingle
                                    key="tvarLovDates"
                                    label={`${i18next.t('dateList')}:`}
                                    side="left"
                                    visibilityGroups={['dateList']}
                                    value={
                                        items.tvar_type == 'LD'
                                            ? items.tvar_lov
                                            : []
                                    }
                                    multiBoxType="LD"
                                />
                                <MultiBoxSingle
                                    key="tvarLovNumbers"
                                    label={`${i18next.t('numberList')}:`}
                                    side="left"
                                    visibilityGroups={['numberList']}
                                    value={
                                        items.tvar_type == 'LN'
                                            ? items.tvar_lov
                                            : []
                                    }
                                    multiBoxType="LN"
                                />
                                <Text
                                    key="attach"
                                    label={`${i18next.t('attachmentsList')}:`}
                                    side="left"
                                    visibilityGroups={['attach']}
                                    value={items.tvar_value}
                                />
                                <Checkbox
                                    key="isPreview"
                                    ref="isPreview"
                                    label={`${i18next.t('showAsPreview')}:`}
                                    value={tvarMeta.isPreview}
                                    showComponentLabel
                                    visibilityGroups={['attach']}
                                />
                                <SelectBox
                                    key="varVersion"
                                    label={`${i18next.t('displayForm')}:`}
                                    options={[
                                        {
                                            title: i18next.t(
                                                'separatedPreview',
                                            ),
                                            value: 0,
                                        },
                                        {
                                            title: i18next.t('lastVersion'),
                                            value: 1,
                                        },
                                    ]}
                                    onChange={this.changeVersion}
                                    nullable={false}
                                    value={items.tvar_design_version || 0}
                                    showComponentLabel
                                    visibilityGroups={['attach']}
                                />
                                {variableType === 'TF' && varVersion > 0 && (
                                    <TextNum
                                        key="zoom"
                                        name="zoom"
                                        label={`${i18next.t('defaultZoom')}:`}
                                        value={tvarMeta.defaultZoom}
                                        tooltipCode="ttSave"
                                        validations={{
                                            maxValue: 5,
                                            minValue: 0,
                                        }}
                                        validationErrors={{
                                            maxValue:
                                                i18next.t('percentInterval'),
                                            minValue:
                                                i18next.t('percentInterval'),
                                        }}
                                        showComponentLabel
                                    />
                                )}
                                {variableType === 'TF' && varVersion > 0 && (
                                    <TextNum
                                        key="fixedHeight"
                                        name="fixedHeight"
                                        label={`${i18next.t('fixedHeight')}:`}
                                        value={tvarMeta.fixedHeight}
                                        validations={{
                                            isInt: true,
                                            minValue: 0,
                                        }}
                                        validationErrors={{
                                            isInt: i18next.t('notIntNumber'),
                                            minValue:
                                                i18next.t('notPositiveNumber'),
                                        }}
                                        showComponentLabel
                                    />
                                )}
                                {variableType === 'TF' && varVersion > 0 && (
                                    <Checkbox
                                        key="fixedPosition"
                                        ref="fixedPosition"
                                        label={`${i18next.t('fixedPosition')}:`}
                                        value={tvarMeta.fixedPosition}
                                        showComponentLabel
                                    />
                                )}
                                <SelectBox
                                    key="dynList"
                                    label={`${i18next.t('dynamicList')}:`}
                                    side="left"
                                    visibilityGroups={['dynList']}
                                    options={this.state.dynListOptions}
                                    nullable={false}
                                    value={dynListValue}
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                    onChange={this.dynamicListChange}
                                />
                                {showDynListUrl && (
                                    <Text
                                        key="dynListUrl"
                                        label={`${i18next.t('url')}:`}
                                        value={this.state.dlUrl}
                                        showComponentLabel
                                    />
                                )}
                                <Checkbox
                                    key="tvarLovMulti"
                                    label={i18next.t('allowMultiple')}
                                    visibilityGroups={[
                                        'textList',
                                        'dateList',
                                        'numberList',
                                        'dynList',
                                        'dynTable',
                                    ]}
                                    value={items.tvar_multi === 'X'}
                                    onChange={this.varMultiChange}
                                />
                                <SelectBox
                                    key="dynTable"
                                    label={`${i18next.t('table')}:`}
                                    side="left"
                                    visibilityGroups={['dynTable']}
                                    options={this.state.dynTablesOptions}
                                    nullable={false}
                                    value={items.dlist_name}
                                    onChange={this.changeDynTable}
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <SelectBox
                                    key="dynTableCol"
                                    ref="dynTableCol"
                                    label={`${i18next.t('column')}:`}
                                    side="left"
                                    visibilityGroups={['dynTable']}
                                    options={this.state.dynTableColumnsOptions}
                                    nullable={false}
                                    value={items.tvar_col_index}
                                    required
                                    validationErrors={{
                                        isDefaultRequiredValue:
                                            i18next.t('isRequired'),
                                    }}
                                />
                                <SelectBox
                                    key="dtOrder"
                                    ref="dtOrder"
                                    side="left"
                                    label={`${i18next.t('sortByColumn')}:`}
                                    visibilityGroups={['dynTable']}
                                    showComponentLabel
                                    value={this.state.dynTableOrder}
                                    options={this.state.dynTableColumnsOptions}
                                />
                                <SelectBox
                                    key="dtSort"
                                    ref="dtSort"
                                    side="left"
                                    label={`${i18next.t('sorting')}:`}
                                    visibilityGroups={['dynTable']}
                                    value={this.state.dynTableSort}
                                    nullable={false}
                                    options={[
                                        {
                                            title: i18next.t('asc'),
                                            value: 'asc',
                                        },
                                        {
                                            title: i18next.t('desc'),
                                            value: 'desc',
                                        },
                                    ]}
                                    showComponentLabel
                                />
                                <Filter
                                    key="dtFilter"
                                    ref="dtFilter"
                                    side="left"
                                    label={`${i18next.t('filterTitle')}:`}
                                    visibilityGroups={['dynTable']}
                                    showComponentLabel
                                    checkboxIsVisible={false}
                                    textMenuBox
                                    getFilterActualVals={
                                        this.getDtFilterActualVals
                                    }
                                    value={this.state.dtFilter}
                                    variablesOptions={
                                        this.state.dtFilterColumnsOptions
                                    }
                                    textMenuBoxOptions={[
                                        {
                                            title: i18next.t('userName'),
                                            value: '#userName#',
                                        },
                                        {
                                            title: i18next.t('userId'),
                                            value: '#userId#',
                                        },
                                    ]}
                                />
                                <Text
                                    key="urlParams"
                                    label={`${i18next.t('params')} (URL):`}
                                    visibilityGroups={['dynTable']}
                                    side="left"
                                    value={this.state.dynTableUrlParams}
                                    showComponentLabel
                                    helperMenu
                                    helperMenuOptions={[
                                        {
                                            value: 'distinct=true',
                                            title: i18next.t('distinct=true'),
                                        },
                                    ]}
                                />
                                {this.state.varIsMulti && (
                                    <Checkbox
                                        key="useSelectMultiple"
                                        label={`${i18next.t('displaySelectionAsTags')}:`}
                                        visibilityGroups={[
                                            'textList',
                                            'dateList',
                                            'numberList',
                                            'dynList',
                                            'dynTable',
                                        ]}
                                        value={tvarMeta.useSelectMultiple}
                                        onChange={this.useSelectMultipleChange}
                                        showComponentLabel
                                    />
                                )}
                                {this.state.varIsMulti && !this.state.useSelectMultiple && (
                                    <Checkbox
                                        key="selectAll"
                                        label={`${i18next.t('allowSelectAll')}:`}
                                        visibilityGroups={[
                                            'textList',
                                            'dateList',
                                            'numberList',
                                            'dynList',
                                            'dynTable',
                                        ]}
                                        value={tvarMeta.selectAll}
                                        showComponentLabel
                                    />
                                )}
                                {this.state.varIsMulti && !this.state.useSelectMultiple && (
                                    <Checkbox
                                        key="allowMultiselectSearchRight"
                                        label={i18next.t('allowMultiselectSearchRight')}
                                        visibilityGroups={[
                                            'textList',
                                            'dateList',
                                            'numberList',
                                            'dynList',
                                            'dynTable',
                                        ]}
                                        value={tvarMeta.allowMultiselectSearchRight}
                                        showComponentLabel
                                    />
                                )}
                                {this.state.varIsMulti && !this.state.useSelectMultiple && (
                                    <Checkbox
                                        key="doubleHeight"
                                        label={i18next.t('doubleHeightForContent')}
                                        visibilityGroups={[
                                            'textList',
                                            'dateList',
                                            'numberList',
                                            'dynList',
                                            'dynTable',
                                        ]}
                                        value={tvarMeta.doubleHeight}
                                        showComponentLabel
                                    />
                                )}
                                {this.state.dlAttribute === 'O' &&
                                    this.state.varIsMulti && (
                                    <Checkbox
                                        key="structuredList"
                                        label={`${i18next.t('structuredList')}:`}
                                        value={tvarMeta.structuredList}
                                        showComponentLabel
                                    />
                                )}
                                <Checkbox
                                    key="customization"
                                    label={`${i18next.t('customization')}:`}
                                    value={customization !== null}
                                    onChange={this.enableCustomization}
                                    showComponentLabel
                                />
                                {this.state.customizationIsEnabled && (
                                    <WrapComponent
                                        key="customization-sett"
                                        side="left"
                                    >
                                        <ColorInput
                                            key="elementColor"
                                            ref={(r) => {
                                                this.elementColorRef = r;
                                            }}
                                            name="elementColor"
                                            label={`${i18next.t('backgroundColor')}:`}
                                            value={getCustomizationVal(
                                                'elementColor',
                                                '',
                                            )}
                                            defaultValue="#ffffff"
                                            showComponentLabel
                                        />
                                        <ColorInput
                                            key="fontColor"
                                            ref={(r) => {
                                                this.fontColorRef = r;
                                            }}
                                            name="fontColor"
                                            label={`${i18next.t('fontColor')}:`}
                                            value={getCustomizationVal(
                                                'fontColor',
                                                '',
                                            )}
                                            defaultValue="#555"
                                            showComponentLabel
                                        />
                                        {/* <SelectBox
                                            key="fontSize"
                                            name="fontSize"
                                            label={`${i18next.t('fontSize')}:`}
                                            value={getCustomizationVal(
                                                'fontSize',
                                                fontSizeOptions[1],
                                            )}
                                            nullable={false}
                                            options={fontSizeOptions}
                                            showComponentLabel
                                        /> */}
                                        {/* <Checkbox
                                            key="cursive"
                                            name="cursive"
                                            label={`${i18next.t('cursive')}:`}
                                            value={getCustomizationVal(
                                                'cursive',
                                                false,
                                            )}
                                            showComponentLabel
                                        /> */}
                                        {/* !this.state.ckEditorIsEnabled &&
                                            !this.state.varIsMulti &&
                                            variableType !== 'TF' &&
                                            variableType !== 'DR' && (
                                            <Checkbox
                                                key="upperLabel"
                                                name="upperLabel"
                                                label={`${i18next.t('upperLabel')}:`}
                                                value={getCustomizationVal(
                                                    'upperLabel',
                                                    false,
                                                )}
                                                showComponentLabel
                                            />
                                        ) */}
                                        <MultiBox
                                            key="tasks"
                                            name="tasks"
                                            label={`${i18next.t('applyInTasks')}:`}
                                            options={
                                                this.state.templateTasksOptions
                                            }
                                            value={getCustomizationVal(
                                                'tasks',
                                                [],
                                            )}
                                            pushMultiBox={this.pushMultiBox}
                                            showComponentLabel
                                            changeableOptions
                                            changeableValue
                                            emptyValueText={i18next.t(
                                                'applyInAllTasks',
                                            )}
                                            selectionModalValueTypeString
                                        />
                                    </WrapComponent>
                                )}

                                <WrapComponent key="class-diagram" side="right">
                                    <Label
                                        key="classDiagramLabel"
                                        name="classDiagramLabel"
                                        label={i18next.t('classDiagram')}
                                        fontSize="1rem"
                                        fullLabel
                                        noValueHeight
                                    />
                                    <Text
                                        key="tvar_mm_class"
                                        name="tvar_mm_class"
                                        label={`${i18next.t('class')}:`}
                                        value={items.tvar_mm_class}
                                        showComponentLabel
                                    />
                                    <Text
                                        key="tvar_mm_class_attr"
                                        name="tvar_mm_class_attr"
                                        label={`${i18next.t('attribute')}:`}
                                        value={items.tvar_mm_class_attr}
                                        showComponentLabel
                                    />
                                </WrapComponent>
                                {variableType === 'DT' && (
                                    <WrapComponent
                                        key="object-state"
                                        side="right"
                                    >
                                        <Label
                                            key="objectStateLabel"
                                            name="objectStateLabel"
                                            label={i18next.t('objectState')}
                                            fontSize="1rem"
                                            fullLabel
                                            noValueHeight
                                        />
                                        <Checkbox
                                            key="stateVar"
                                            name="stateVar"
                                            label={`${i18next.t('stateVar')}:`}
                                            value={items.tvar_attribute === 'V'}
                                            showComponentLabel
                                        />
                                    </WrapComponent>
                                )}
                            </Form>
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                    <ImportTemplateModal
                        isOpen={this.state.importModalIsOpen}
                        onClose={this.closeImportModal}
                        onLoad={this.onLoad}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplateVariable.displayName = 'TemplateVariable';

TemplateVariable.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default TemplateVariable;
