import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import PropTypes from 'prop-types';
import cx from 'classnames';
import ReactDOM from 'react-dom';
import { guid } from '../common/utils';
import { Text } from '../components/form/text.react';
import { TextNum } from '../components/form/textNum.react';
import { TextArea } from '../components/form/textArea.react';
import { Checkbox } from '../components/form/checkbox.react';
import { SelectBox } from '../components/form/selectBox.react';
import { Switch } from '../components/form/switch.react';
import { CodeArea } from '../components/form/codeArea.react';
import ShowHideComponent from '../components/form/showHideComponent.react';
import PageRights from '../common/pageRights';
import ApiRequest from '../api/apiRequest';
import React from 'react';
import Heading from '../components/heading.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import MainButton from '../components/tabs/mainButton.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import Form from '../components/form/form.react';
import CKEditor from '../components/form/ckEditor.react';
import EmptyComponent from '../components/form/emptyComponent.react';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import AlertsActions from '../components/alerts/alerts.actions';
import HiddenTabs from '../components/tabs/hiddenTabs.react';
import HiddenTab from '../components/tabs/hiddenTab.react';
import LoggedUserStore from '../flux/loggedUser.store';
import HiddenTabContent from '../components/tabs/hiddenTabContent.react';
import TemplatePrintPreview from '../components/templatePrintPreview.react';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import TabsButton from '../components/tabs/tabsButton.react';
import alt from '../flux/alt';
import Factory from '../flux/factory';
import CasePrintStore from '../flux/casePrint.store';
import CasePrintActions from '../flux/casePrint.actions';
import ConfirmModal from './modals/confirmModal.react';
import ideHints from '../components/form/ide/ideHints';

const caseOverviewName = 'CASE OVERVIEW';

class TemplatePrint extends React.Component {

    constructor(props) {
        super();
        this.state = {
            print: {},
            formPrintIsPristine: true,
            loading: true,
            printNameOrig: '',
            isCaseOverview: false,
            printReact: null,
            previewIsVisible: false,
            varsOptionsToInsert: [],
            variablesLoaded: false,
            confirmModalIsOpen: false,
            menuBoxIsOpen: false,
            menuBoxReactIsOpen: false,
            menuBoxTopOrientation: false,
            menuBoxReactTopOrientation: false,
        };

        this.watchUserFormBeforeLeave =
            this.watchUserFormBeforeLeave.bind(this);
        this.loadPrintData = this.loadPrintData.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleValidSubmitWithoutRequire =
            this.handleValidSubmitWithoutRequire.bind(this);
        this.savePrint = this.savePrint.bind(this);
        this.postData = this.postData.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.goBack = this.goBack.bind(this);
        this.enableCaseOverview = this.enableCaseOverview.bind(this);
        this.changePrintName = this.changePrintName.bind(this);
        this.showPreview = this.showPreview.bind(this);
        this.switchPrintType = this.switchPrintType.bind(this);
        this.insertVar = this.insertVar.bind(this);
        this.insertSnippet = this.insertSnippet.bind(this);
        this.loadVariables = this.loadVariables.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
        this.openMenuBox = this.openMenuBox.bind(this);
        this.documentClick = this.documentClick.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        const { templateId, versionId, templatePrintId } =
            this.props.match.params;

        // default breadcrumb when refresh
        BreadcrumbActions.setDefaultBread([
            { name: i18next.t('templates'), to: '/templates' },
            {
                name: i18next.t('template'),
                to: `/templates/template/${templateId}/${versionId}/print`,
            },
        ]);

        i18next.on('languageChanged', (options) => {
            BreadcrumbActions.setDefaultBread.defer([
                { name: i18next.t('templates'), to: '/templates' },
                {
                    name: i18next.t('template'),
                    to: `/templates/template/${templateId}/${versionId}/print`,
                },
            ]);
        });

        if (templatePrintId !== 'new') {
            this.loadPrintData(templateId, versionId, templatePrintId);
        } else {
            this.setState({
                loading: false,
                printReact: false,
            });
        }

        this.watchUserFormBeforeLeave(templateId);
        this.printPreviewId = guid();
        const casePrintObj = Factory.registerFlux(
            CasePrintStore,
            CasePrintActions,
            `casePrint-preview-${this.printPreviewId}`,
        );
        this.casePrintStore = casePrintObj.store;

        document.addEventListener('click', this.documentClick);
    }

    UNSAFE_componentWillUpdate(nextProps, nextState) {
        // load print data after new print is saved without close
        const nextPrintId = nextProps.match.params.templatePrintId;
        if (
            nextPrintId !== 'new' &&
            nextPrintId !== this.props.match.params.templatePrintId
        ) {
            this.loadPrintData(
                this.props.match.params.templateId,
                this.props.match.params.versionId,
                nextPrintId,
            );
        }

        // load variables for react print
        if (
            this.state.printReact === false &&
            nextState.printReact === true &&
            !this.state.variablesLoaded
        ) {
            this.loadVariables(this.props.match.params.templateId);
        }
    }

    componentWillUnmount() {
        this.unlistenForm();
        alt.recycle(this.casePrintStore);

        document.removeEventListener('click', this.documentClick);
    }

    watchUserFormBeforeLeave(templateId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const printId = this.props.match.params.templateId;

                if (
                    !this.state.formPrintIsPristine &&
                    location.pathname !==
                        `/templates/template/${templateId}/template-print/${printId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = (data) => {
                        this.postData(data, callback, true);
                    };

                    this.onFormActionWithoutRequire = () => {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18next.t('alrFillRequiredItems'),
                            show: true,
                            allowCountdown: true,
                        });
                    };
                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.refs.formPrint.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formPrintIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    loadPrintData(templateId, versionId, printId) {
        this.setState({ loading: true });

        ApiRequest.get(
            `/template-processes/${templateId}/${versionId}/prints/${printId}`,
        )
            .then((payload) => {
                if (payload.prnt_react === 'Y' && !this.state.variablesLoaded) {
                    this.loadVariables(this.props.match.params.templateId);
                }

                this.setState({
                    print: payload,
                    loading: false,
                    isCaseOverview: payload.prnt_name === caseOverviewName,
                    printReact: payload.prnt_react === 'Y',
                });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrFailedLoad'),
                    serverError: errorMessage,
                });
            });
    }

    loadVariables(templateId) {
        ApiRequest.get(
            `/template-processes/${templateId}/1/template-variables?order=tvar_name&sort=asc&limit=${config.restLimit}`,
        )
            .then((variables) => {
                const varsArr = [];
                variables.items.forEach((item) => {
                    varsArr.push({
                        value: `getVar('${item.tvar_name}')`,
                        title: item.tvar_name,
                    });
                });

                const sysVars = [
                    { value: 'getVar(\'CaseName\')', title: 'CaseName' },
                    { value: 'getVar(\'CaseStatus\')', title: 'CaseStatus' },
                    { value: 'getVar(\'CaseStart\')', title: 'CaseStart' },
                    { value: 'getVar(\'CaseDeadline\')', title: 'CaseDeadline' },
                    { value: 'getVar(\'CasePriority\')', title: 'CasePriority' },
                    {
                        value: 'getVar(\'CasePriorityText\')',
                        title: 'CasePriorityText',
                    },
                    {
                        value: 'getVar(\'CaseDescription\')',
                        title: 'CaseDescription',
                    },
                    { value: 'getVar(\'CaseOwner\')', title: 'CaseOwner' },
                    {
                        value: 'getVar(\'CaseOwnerName\')',
                        title: 'CaseOwnerName',
                    },
                    { value: 'getVar(\'CaseNotes\')', title: 'CaseNotes' },
                    { value: 'getVar(\'CaseLink\')', title: 'CaseLink' },
                    {
                        value: 'getVar(\'CaseVisRoleId\')',
                        title: 'CaseVisRoleId',
                    },
                ];

                this.setState({
                    varsOptionsToInsert: varsArr.concat(sysVars),
                    variablesLoaded: true,
                });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTempVarsLoadFailed'),
                    serverError: errorMessage,
                });
            });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleValidSubmitWithoutRequire() {
        this.onFormActionWithoutRequire();
    }

    savePrint(close, e) {
        e.preventDefault();

        this.onFormAction = (data) => {
            this.postData(data, null, close);
        };

        this.onFormActionWithoutRequire = function () {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillRequiredItems'),
                show: true,
                allowCountdown: true,
            });
        };

        this.refs.formPrint.submit();
    }

    postData(data, callback, close) {
        const alertId = guid();
        const { templatePrintId } = this.props.match.params;

        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSavingPrint'),
        });

        const obj = {
            prnt_name: data.prnt_name,
            prnt_content: data.prnt_content,
            tproc_id: this.props.match.params.templateId,
            tproc_version: this.props.match.params.versionId,
            prnt_js: data.printReact ? data.prnt_react_js : data.prnt_js,
            prnt_status: data.isDeveloped ? 'D' : 'A',
            prnt_order: data.prnt_order,
            prnt_apply_styles: data.disableFrontendStyles ? 'N' : 'Y',
            prnt_css: data.prnt_css || null,
            prnt_timeout: data.prnt_timeout || null,
            prnt_ready_in_js: data.printIsReadyInJs
                ? data.printIsReadyInJs.value || data.printIsReadyInJs
                : 'N',
            prnt_react: data.printReact ? 'Y' : 'N',
        };

        if (templatePrintId !== 'new') {
            obj.prnt_id = templatePrintId;
        }

        const { languages } = LoggedUserStore.getState();
        languages.forEach((lang) => {
            obj[`prnt_name_${lang}`] = data[`prnt_name_${lang}`];
        });

        ApiRequest.post('/template-processes/prints', JSON.stringify(obj))
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrPrintSaved'),
                });

                this.setState(
                    {
                        formPrintIsPristine: true,
                    },
                    () => {
                        if (callback) {
                            callback();
                        } else if (close) {
                            this.goBack();
                        } else if (templatePrintId === 'new') {
                            // just save
                            const { templateId, versionId } =
                                this.props.match.params;

                            this.props.history.push(
                                `/templates/template/${templateId}/${versionId}/template-print/${payload.id}`,
                            );
                        }
                    },
                );
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrPrintSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formPrintIsPristine) {
            this.setState({ formPrintIsPristine: false });
        } else if (isChanged === false && !this.state.formPrintIsPristine) {
            this.setState({ formPrintIsPristine: true });
        }
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        if (this.refs.CKEditor) {
            this.refs.CKEditor.minimize();
        }

        const { templateId, versionId } = this.props.match.params;

        this.props.history.push(
            `/templates/template/${templateId}/${versionId}/print`,
        );
    }

    enableCaseOverview(name, value) {
        const printNameRef = this.refs.prnt_name;
        const printName = printNameRef.getValue();

        if (printName !== caseOverviewName) {
            this.setState({
                printNameOrig: printName,
                isCaseOverview: true,
            });
        } else {
            this.setState({
                isCaseOverview: false,
            });
        }

        printNameRef.setValue(
            value ? caseOverviewName : this.state.printNameOrig,
        );
    }

    changePrintName(name, value) {
        this.refs.isCaseOverview.setValue(!(value !== caseOverviewName));
        this.setState({
            isCaseOverview: value === caseOverviewName,
        });
    }

    showPreview(e) {
        if (e) e.preventDefault();

        this.setState({
            previewIsVisible: !this.state.previewIsVisible,
            prntContentPreview: this.refs.prntContent.getValue(),
            prntCssPreview: this.refs.prntCss.getValue(),
            prntJsPreview: this.refs.prntReactJs.getValue(),
        });
    }

    switchPrintType(name, value) {
        this.setState({ printReact: value });
    }

    insertVar(name, value) {
        if (this.refs.prntReactJs) {
            this.refs.prntReactJs.insertSnippet(value.value);
            this.refs.insertVar.setValue(null); // to clear selectbox and enabling new selection of the same variable
        }
    }

    insertSnippet(e, ref) {
        const value = e.target.getAttribute('data-value');

        if (this.refs[ref] && this.refs[ref].insertSnippet) {
            this.refs[ref].insertSnippet(value);
        }

        this.setState({
            menuBoxIsOpen: false,
            menuBoxReactIsOpen: false,
        });
    }

    documentClick(event) {
        if (!this.menuBoxButton?.contains(event.target)) {
            this.setState({ menuBoxIsOpen: false });
        }
        if (!this.menuBoxButtonReact?.contains(event.target)) {
            this.setState({ menuBoxReactIsOpen: false });
        }
    }

    openMenuBox(ref) {
        if (ref === 'prntJs') {
            this.setState({ menuBoxIsOpen: !this.state.menuBoxIsOpen });
        } else {
            this.setState({
                menuBoxReactIsOpen: !this.state.menuBoxReactIsOpen,
            });
        }
    }

    render() {
        const { templateId, templatePrintId } = this.props.match.params;
        const { print, isCaseOverview } = this.state;
        const { languages } = LoggedUserStore.getState();
        const printIsReadyOptions = [
            { title: i18next.t('default'), value: 'N' },
            { title: i18next.t('inJs'), value: 'Y' },
        ];

        return (
            <DocumentTitle title={i18next.t('printTemplate')}>
                <TabsWrapper>
                    {this.state.previewIsVisible && (
                        <TemplatePrintPreview
                            print={this.state.prntContentPreview}
                            css={this.state.prntCssPreview}
                            js={this.state.prntJsPreview}
                            closePreview={this.showPreview}
                            printPreviewId={this.printPreviewId}
                        />
                    )}
                    <Heading
                        title={
                            templatePrintId !== 'new'
                                ? print.prnt_name
                                : i18next.t('addPrintTemplate')
                        }
                    >
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                enableOn={!this.state.loading}
                                onClick={this.savePrint.bind(null, true)}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-download-5"
                                buttonColor="white"
                                enableOn={!this.state.loading}
                                onClick={this.savePrint.bind(null, false)}
                                tooltipCode="ttJustSave"
                            >
                                {i18next.t('justSave')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs params={this.props.match.params}>
                        {this.state.printReact === null && (
                            <Tabs.Tab
                                key="templatePrint"
                                title={i18next.t('print')}
                                tabLink={`/templates/template/${templateId}/template-print/${templatePrintId}`}
                                name="tabTemplatePrint"
                                showLoader
                                loaded={!this.state.loading}
                            />
                        )}
                        {this.state.printReact === false && (
                            <Tabs.Tab
                                key="templatePrint"
                                title={i18next.t('print')}
                                tabLink={`/templates/template/${templateId}/template-print/${templatePrintId}`}
                                name="tabTemplatePrint"
                                showLoader
                                loaded={!this.state.loading}
                            >
                                <Form
                                    key={`formPrint-${templatePrintId}`} // re-mount form after saving new print without closing
                                    ref="formPrint"
                                    name="formPrint"
                                    onValidSubmit={this.handleValidSubmit}
                                    onChange={this.formChanged}
                                >
                                    <Switch
                                        key="printReact"
                                        fontSize="1rem"
                                        fontWeight="400"
                                        label={`${i18next.t('print')}/${i18next.t('print')} - React`}
                                        value={this.state.printReact}
                                        onChange={this.switchPrintType}
                                    />
                                    <Checkbox
                                        key="isCaseOverview"
                                        ref="isCaseOverview"
                                        label={`${i18next.t('caseOverview')}:`}
                                        value={
                                            print.prnt_name === caseOverviewName
                                        }
                                        onChange={this.enableCaseOverview}
                                    />
                                    <Text
                                        key="prnt_name"
                                        ref="prnt_name"
                                        label={`${i18next.t('defaultLbl', { label: '$t(fsName)' })}:`}
                                        value={print.prnt_name}
                                        side="left"
                                        required
                                        validationErrors={{
                                            isDefaultRequiredValue:
                                                i18next.t('isRequired'),
                                        }}
                                        onChange={this.changePrintName}
                                    />
                                    <ShowHideComponent
                                        key="langName"
                                        side="left"
                                    >
                                        {!isCaseOverview &&
                                            languages.map((lang, i) => {
                                                return (
                                                    <Text
                                                        key={`prnt_name${lang}`}
                                                        name={`prnt_name_${lang}`}
                                                        label={`${i18next.t('name')}:`}
                                                        value={
                                                            print[
                                                                `prnt_name_${lang}`
                                                            ]
                                                        }
                                                        lblLang={lang}
                                                    />
                                                );
                                            })}
                                    </ShowHideComponent>
                                    <TextNum
                                        key="prnt_order"
                                        ref="prnt_order"
                                        label={`${i18next.t('order')}:`}
                                        value={print.prnt_order}
                                        side="left"
                                        validations={{ isInt: true }}
                                        validationErrors={{
                                            isInt: i18next.t('notIntNumber'),
                                        }}
                                    />
                                    <Checkbox
                                        key="isDeveloped"
                                        ref="isDeveloped"
                                        label={`${i18next.t('inDevelopment')}:`}
                                        value={print.prnt_status === 'D'}
                                    />
                                    {!isCaseOverview && (
                                        <Checkbox
                                            key="disableFrontendStyles"
                                            label={`${i18next.t('disableFrontendStyles')}:`}
                                            value={
                                                print.prnt_apply_styles === 'N'
                                            }
                                        />
                                    )}
                                    <TextNum
                                        key="prnt_timeout"
                                        label="Timeout (ms):"
                                        value={print.prnt_timeout}
                                        side="left"
                                        validations={{ isInt: true }}
                                        validationErrors={{
                                            isInt: i18next.t('notIntNumber'),
                                        }}
                                    />
                                    <CKEditor
                                        key="prnt_content"
                                        ref="CKEditor"
                                        label={`${i18next.t('customPrint')}:`}
                                        side="left"
                                        value={print.prnt_content || ''}
                                        dataId={templatePrintId}
                                        templateId={templateId}
                                        mainButtonsInMaximizedEditor
                                        height="500"
                                    />
                                    <EmptyComponent
                                        key="empty"
                                        side="right"
                                        smallHide
                                    />
                                    <EmptyComponent
                                        key="empty2"
                                        side="right"
                                        smallHide
                                    />
                                    <EmptyComponent
                                        key="empty3"
                                        side="right"
                                        smallHide
                                    />
                                    <EmptyComponent
                                        key="empty4"
                                        side="right"
                                        smallHide
                                    />
                                    <EmptyComponent
                                        key="empty5"
                                        side="right"
                                        smallHide
                                    />
                                    {/* <EmptyComponent key="empty6" side="right" smallHide /> */}
                                    {!isCaseOverview && (
                                        <EmptyComponent
                                            key="emptyPrintName"
                                            side="right"
                                            smallHide
                                        />
                                    )}
                                    {!isCaseOverview && (
                                        <EmptyComponent
                                            key="emptyDisableStyles"
                                            side="right"
                                            smallHide
                                        />
                                    )}
                                    <CodeArea
                                        key="prnt_js"
                                        ref="prntJs"
                                        name="prnt_js"
                                        label="JS:"
                                        side="right"
                                        upperLabel
                                        value={print.prnt_js || ''}
                                        hints="templatePrint"
                                        helperText={`${i18next.t('ideHelp')} ${i18next.t('codeMirrorHelpJs')}`}
                                        htmlLabelButton={
                                            <span
                                                className="change-button menu-box-button icon snippet"
                                                ref={(c) =>
                                                    (this.menuBoxButton = c)}
                                                onClick={() =>
                                                    this.openMenuBox('prntJs')}
                                                title={i18next.t(
                                                    'insertSnippet',
                                                )}
                                            >
                                                <i className="icon icon2-script" />
                                                <div
                                                    ref={(c) =>
                                                        (this.menuBox = c)}
                                                    className={cx(
                                                        'menu-box',
                                                        {
                                                            open: this.state
                                                                .menuBoxIsOpen,
                                                        },
                                                        // { 'open-up': this.state.menuBoxTopOrientation },
                                                    )}
                                                >
                                                    <ul>
                                                        {ideHints.templatePrint.map(
                                                            (option, i) => (
                                                                <li key={i}>
                                                                    <a
                                                                        ref={(
                                                                            c,
                                                                        ) =>
                                                                            (this.option =
                                                                                c)}
                                                                        data-value={
                                                                            option.text
                                                                        }
                                                                        onClick={(
                                                                            e,
                                                                        ) =>
                                                                            this.insertSnippet(
                                                                                e,
                                                                                'prntJs',
                                                                            )}
                                                                    >
                                                                        {
                                                                            option.displayText
                                                                        }
                                                                    </a>
                                                                </li>
                                                            ),
                                                        )}
                                                    </ul>
                                                </div>
                                            </span>
                                        }
                                    />
                                </Form>
                            </Tabs.Tab>
                        )}
                        {this.state.printReact === true && (
                            <Tabs.Tab
                                key="templatePrint"
                                title={`${i18next.t('print')} - React`}
                                tabLink={`/templates/template/${templateId}/template-print/${templatePrintId}`}
                                name="tabTemplatePrint"
                                showLoader
                                loaded={!this.state.loading}
                            >
                                <TabsButtonsOther key="buttonsPrintReact">
                                    <TabsButton
                                        key="showProview"
                                        icon="icon-preview-1"
                                        isActive
                                        onClick={this.showPreview}
                                    >
                                        {i18next.t('thumbnail')}
                                    </TabsButton>
                                </TabsButtonsOther>
                                <HiddenTabs
                                    params={this.props.match.params}
                                    noTabLinks
                                >
                                    <HiddenTab key="printReact" title="HTML" />
                                    <HiddenTab
                                        key="printReactJs"
                                        title="JS"
                                        tabName="print-react-js"
                                    />
                                    <Form
                                        key={`formPrint-${templatePrintId}`} // re-mount form after saving new print without closing
                                        ref="formPrint"
                                        name="formPrint"
                                        onValidSubmit={this.handleValidSubmit}
                                        onValidSubmitIgnoreRequired={
                                            this.handleValidSubmitWithoutRequire
                                        }
                                        onChange={this.formChanged}
                                    >
                                        <HiddenTabContent key="HiddenTabContentHtml">
                                            <Switch
                                                key="printReact"
                                                fontSize="1rem"
                                                fontWeight="400"
                                                label={`${i18next.t('print')}/${i18next.t('print')} - React`}
                                                value={this.state.printReact}
                                                onChange={this.switchPrintType}
                                            />
                                            <Checkbox
                                                key="isCaseOverview"
                                                ref="isCaseOverview"
                                                label={`${i18next.t('caseOverview')}:`}
                                                value={
                                                    print.prnt_name ===
                                                    caseOverviewName
                                                }
                                                onChange={
                                                    this.enableCaseOverview
                                                }
                                                side="left"
                                            />
                                            <Text
                                                key="prnt_name"
                                                ref="prnt_name"
                                                label={`${i18next.t('name')}:`}
                                                value={print.prnt_name}
                                                side="left"
                                                required
                                                validationErrors={{
                                                    isDefaultRequiredValue:
                                                        i18next.t('isRequired'),
                                                }}
                                                onChange={this.changePrintName}
                                            />
                                            <TextNum
                                                key="prnt_order"
                                                ref="prnt_order"
                                                label={`${i18next.t('order')}:`}
                                                value={print.prnt_order}
                                                side="left"
                                                validations={{ isInt: true }}
                                                validationErrors={{
                                                    isInt: i18next.t(
                                                        'notIntNumber',
                                                    ),
                                                }}
                                            />
                                            <Checkbox
                                                key="isDeveloped"
                                                ref="isDeveloped"
                                                side="left"
                                                label={`${i18next.t('inDevelopment')}:`}
                                                value={
                                                    print.prnt_status === 'D'
                                                }
                                            />
                                            {!isCaseOverview && (
                                                <Checkbox
                                                    key="disableFrontendStyles"
                                                    label={`${i18next.t('disableFrontendStyles')}:`}
                                                    side="left"
                                                    value={
                                                        print.prnt_apply_styles ===
                                                        'N'
                                                    }
                                                />
                                            )}
                                            <SelectBox
                                                key="printIsReadyInJs"
                                                label={`${i18next.t('printIsReady')}:`}
                                                options={printIsReadyOptions}
                                                side="left"
                                                nullable={false}
                                                value={
                                                    print.prnt_ready_in_js ||
                                                    printIsReadyOptions[0].value
                                                }
                                                toShowGroups={{ N: 'default' }}
                                            />
                                            <TextNum
                                                key="prnt_timeout"
                                                label="Timeout (ms):"
                                                value={print.prnt_timeout}
                                                side="left"
                                                validations={{ isInt: true }}
                                                validationErrors={{
                                                    isInt: i18next.t(
                                                        'notIntNumber',
                                                    ),
                                                }}
                                                visibilityGroups={['default']}
                                            />
                                            <TextArea
                                                key="prnt_content"
                                                ref="prntContent"
                                                label="HTML:"
                                                side="left"
                                                value={print.prnt_content}
                                                rows={3}
                                            />
                                            <CodeArea
                                                key="prnt_css"
                                                ref="prntCss"
                                                name="prnt_css"
                                                label="CSS:"
                                                side="right"
                                                upperLabel
                                                value={print.prnt_css}
                                                language="css"
                                            />
                                        </HiddenTabContent>
                                        <HiddenTabContent
                                            key="HiddenTabContentJs"
                                            tabName="print-react-js"
                                        >
                                            <SelectBox
                                                key="insertVar"
                                                ref="insertVar"
                                                side="left"
                                                options={
                                                    this.state
                                                        .varsOptionsToInsert
                                                }
                                                onChange={this.insertVar}
                                                label={i18next.t('insertVar')}
                                            />
                                            <CodeArea
                                                key="prnt_react_js"
                                                ref="prntReactJs"
                                                name="prnt_react_js"
                                                label="JS:"
                                                side="center"
                                                upperLabel
                                                value={print.prnt_js || ''}
                                                hints="templatePrintReact"
                                                helperText={`${i18next.t('ideHelp')} ${i18next.t('codeMirrorHelpJs')}`}
                                                language="jsx"
                                                vars={
                                                    this.state
                                                        .varsOptionsToInsert
                                                }
                                                htmlLabelButton={
                                                    <span
                                                        className="change-button menu-box-button icon snippet"
                                                        ref={(c) =>
                                                            (this.menuBoxButtonReact =
                                                                c)}
                                                        onClick={() =>
                                                            this.openMenuBox(
                                                                'prntReactJs',
                                                            )}
                                                        title={i18next.t(
                                                            'insertSnippet',
                                                        )}
                                                    >
                                                        <i className="icon icon2-script" />
                                                        <div
                                                            ref={(c) =>
                                                                (this.menuBoxReact =
                                                                    c)}
                                                            className={cx(
                                                                'menu-box',
                                                                {
                                                                    open: this
                                                                        .state
                                                                        .menuBoxReactIsOpen,
                                                                },
                                                                // { 'open-up': this.state.menuBoxReactTopOrientation },
                                                            )}
                                                        >
                                                            <ul>
                                                                {ideHints.templatePrintReact.map(
                                                                    (
                                                                        option,
                                                                        i,
                                                                    ) => (
                                                                        <li
                                                                            key={
                                                                                i
                                                                            }
                                                                        >
                                                                            <a
                                                                                ref={(
                                                                                    c,
                                                                                ) =>
                                                                                    (this.option =
                                                                                        c)}
                                                                                data-value={
                                                                                    option.text
                                                                                }
                                                                                onClick={(
                                                                                    e,
                                                                                ) =>
                                                                                    this.insertSnippet(
                                                                                        e,
                                                                                        'prntReactJs',
                                                                                    )}
                                                                            >
                                                                                {
                                                                                    option.displayText
                                                                                }
                                                                            </a>
                                                                        </li>
                                                                    ),
                                                                )}
                                                            </ul>
                                                        </div>
                                                    </span>
                                                }
                                            />
                                        </HiddenTabContent>
                                    </Form>
                                </HiddenTabs>
                            </Tabs.Tab>
                        )}
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplatePrint.displayName = 'TemplatePrint';

TemplatePrint.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default TemplatePrint;
