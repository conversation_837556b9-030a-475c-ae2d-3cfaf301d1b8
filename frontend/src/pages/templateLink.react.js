import PropTypes from 'prop-types';
import { <PERSON> } from 'react-router-dom';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { guid } from '../common/utils';
import PageRights from '../common/pageRights';

import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import Tab from '../components/tabs/tab.react';
import Tabs from '../components/tabs/tabs.react';
import MainButtons from '../components/tabs/mainButtons.react';
import MainButton from '../components/tabs/mainButton.react';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import TabsButton from '../components/tabs/tabsButton.react';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import BreadcrumbStore from '../flux/breadcrumb.store';
import ConfirmModal from './modals/confirmModal.react';
import TemplateLinkStore from '../flux/templateLink.store';
import TemplateLinkActions from '../flux/templateLink.actions';
import TemplateLinkApi from '../flux/templateLink.api';
import AlertsActions from '../components/alerts/alerts.actions';
import alt from '../flux/alt';
import TemplateLinkForm from './templateLink/templateLinkForm.react';
import TemplateLinkFunctions from './templateLink/templateLinkFunctions.react';

class TemplateLink extends React.Component {

    constructor(props) {
        super();
        this.state = _.extend(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            TemplateLinkStore.getState(),
        );

        this.watchLinkFormBeforeLeave =
            this.watchLinkFormBeforeLeave.bind(this);
        this._onChange = this._onChange.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.saveLink = this.saveLink.bind(this);
        this.postData = this.postData.bind(this);
        this.handleAddVariable = this.handleAddVariable.bind(this);
        this.goBack = this.goBack.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        const { templateId, versionId, templateLinkId } =
            this.props.match.params;

        TemplateLinkStore.listen(this._onChange);
        TemplateLinkActions.loadTemplatesVariables(templateId, versionId);
        TemplateLinkActions.loadLinkData(templateLinkId);

        // location state for previous breadcrumb path and close button
        const locationState = this.props.location.state;

        if (locationState) {
            TemplateLinkActions.savePrevPath(this.props.location.state);
        }

        let prevUrl;
        if (
            locationState &&
            typeof locationState.breadPrevPath !== 'undefined' &&
            locationState.breadPrevPath !== null
        ) {
            prevUrl = locationState.breadPrevPath;
        } else if (this.state.breadPrevPath !== null) {
            prevUrl = this.state.breadPrevPath;
        } else {
            prevUrl = '/templates';
        }

        let closeUrl;
        if (
            locationState &&
            typeof locationState.closePrevPath !== 'undefined' &&
            locationState.closePrevPath !== null
        ) {
            closeUrl = locationState.closePrevPath;
        } else if (this.state.closePrevPath !== null) {
            closeUrl = this.state.closePrevPath;
        } else {
            closeUrl = `/templates/template/${templateId}/${versionId}/connection`;
        }

        // set breadcrumb
        BreadcrumbActions.changeBread([
            { name: i18next.t('templates'), to: prevUrl },
            {
                name: i18next.t('template'),
                to: `/templates/template/${templateId}/${versionId}/tasks`,
            },
            { name: i18next.t('connection'), to: closeUrl },
            { name: i18next.t('link') },
        ]);

        this.watchLinkFormBeforeLeave(templateId, versionId);
    }

    watchLinkFormBeforeLeave(templateId, versionId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const { templateLinkId } = this.props.match.params;

                if (
                    !this.state.formIsPristine &&
                    location.pathname !==
                        `/templates/template/${templateId}/${versionId}/template-link/${templateLinkId}`
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = function (data) {
                        this.postData(data, callback);
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.refs.templateLinkForm.refs.formLink.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    componentWillUnmount() {
        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        this.unlistenForm();
        TemplateLinkStore.unlisten(this._onChange);
        alt.recycle(TemplateLinkStore);

        // resave previous paths after store is recycled
        TemplateLinkActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });
    }

    _onChange(state) {
        this.setState(state);
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    saveLink(close, e) {
        e.preventDefault();

        this.onFormAction = function (data) {
            this.postData(data, null, close);
        };

        this.refs.templateLinkForm.refs.formLink.submit();
    }

    postData(data, callback, close) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const { templateId, versionId, templateLinkId } =
            this.props.match.params;
        const obj = TemplateLinkFunctions.getPostObject(
            data,
            templateId,
            versionId,
            templateLinkId,
            this.state.link,
        );

        TemplateLinkApi.saveTemplateLink(obj)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });

                this.setState({ formIsPristine: true }, function () {
                    if (callback) {
                        callback();
                    } else if (close) {
                        this.goBack();
                    }
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrConnectionSaveFailed'),
                    serverError: errorMessage,
                });
            });
    }

    handleAddVariable(e) {
        e.preventDefault();
        const { templateId, versionId } = this.props.match.params;

        this.props.history.push({
            pathname: `/templates/template/${templateId}/${versionId}/template-variable/new`,
            state: {
                breadPrevPath: this.state.breadPrevPath,
                closePrevPath: location.pathname,
            },
        });
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }
        const { templateId, versionId } = this.props.match.params;
        this.props.history.push(
            this.state.closePrevPath ||
                `/templates/template/${templateId}/${versionId}/connection`,
        );
    }

    render() {
        const { templateId, versionId, templateLinkId } =
            this.props.match.params;

        return (
            <DocumentTitle title={i18next.t('link')}>
                <TabsWrapper>
                    <Heading title={i18next.t('link')}>
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                enableOn={this.state.isMainButtonEnabled}
                                onClick={this.saveLink.bind(null, true)}
                                tooltipCode="ttSave"
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-download-5"
                                buttonColor="white"
                                enableOn={this.state.isMainButtonEnabled}
                                onClick={this.saveLink.bind(null, false)}
                                tooltipCode="ttJustSave"
                            >
                                {i18next.t('justSave')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="templateLinkConnection"
                            title={i18next.t('link')}
                            name="tabTemplateLink"
                            tabLink={`/templates/template/${templateId}/${versionId}/template-link/${templateLinkId}`}
                            showLoader
                            loaded={!this.state.loading}
                        >
                            <TabsButtonsOther>
                                <TabsButton
                                    key="add"
                                    icon="icon-add-1"
                                    onClick={this.handleAddVariable}
                                    isActive
                                    tooltipCode="ttAdd"
                                >
                                    {i18next.t('addVariable')}
                                </TabsButton>
                            </TabsButtonsOther>
                            <TemplateLinkForm
                                ref="templateLinkForm"
                                link={this.state.link}
                                formChanged={this.formChanged}
                                handleValidSubmit={this.handleValidSubmit}
                                conditions={this.state.conditions}
                                conditionsVariablesOptions={
                                    this.state.conditionsVariablesOptions
                                }
                                suggestList={this.state.linkSuggestionList}
                            />
                        </Tabs.Tab>
                    </Tabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplateLink.displayName = 'TemplateLink';

TemplateLink.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default TemplateLink;
