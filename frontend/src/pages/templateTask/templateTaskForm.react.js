import i18next from 'i18next';
import PropTypes from 'prop-types';
import cx from 'classnames';
import ReactDOM from 'react-dom';
import { TextArea } from '../../components/form/textArea.react';
import {
    SelectBox,
    SelectBoxPure,
} from '../../components/form/selectBox.react';
import { Checkbox } from '../../components/form/checkbox.react';
import { Text } from '../../components/form/text.react';
import { CalendarWithClockpicker } from '../../components/form/calendarWithClockpicker.react';
import Button from '../../components/form/button.react';
import Line from '../../components/form/line.react';
import ApiRequest from '../../api/apiRequest';
import AlertsActions from '../../components/alerts/alerts.actions';
import ideHints from '../../components/form/ide/ideHints';
import React from 'react';
import _ from 'lodash';
import Form from '../../components/form/form.react';
import HiddenTabContent from '../../components/tabs/hiddenTabContent.react';
import Label from '../../components/form/label.react';
import MultiBox from '../../components/form/multiBox.react.js';
import MultiBoxTriple from '../../components/form/multiBoxTriple.react.js';
import MultiBoxMulti from '../../components/form/multiBoxMulti.react.js';
import CKEditor from '../../components/form/ckEditor.react';
import WrapComponent from '../../components/form/wrapComponent.react';
import Filter from '../../components/form/tempVarFilter.react';
import ShowHideComponent from '../../components/form/showHideComponent.react.js';
import LoggedUserStore from '../../flux/loggedUser.store';
import TemplateTaskFunctions from './templateTaskFunctions.react';
import EmptyComponent from '../../components/form/emptyComponent.react';
import { CodeArea } from '../../components/form/codeArea.react';
import Calculations from '../../components/form/calculations.react';

class TemplateTaskForm extends React.Component {

    constructor(props) {
        super();

        this.state = {
            solverRestrictionsObj: {
                ttask_assesment_hierarchy: 'G',
                ttask_assesment_method: 'T',
            },
            menuBoxIsOpen: false,
            menuBoxConditionsIsOpen: false,
            menuBoxTopOrientation: false,
            // menuBoxConditionsTopOrientation: false,
            evaluateBtnIsActive: true,
            possibleSolversArr: [],
            showAllPossibleSolvers: false,
            showSolverWillBeInfo: null,
            showPossibleSolvers: false,
        };

        this.ttaskAssesmentHierarchyRef = React.createRef();

        this.getNewCalcActualVals = this.getNewCalcActualVals.bind(this);
        this.getFilterActualVals = this.getFilterActualVals.bind(this);
        this.componentWithRowsChanged =
            this.componentWithRowsChanged.bind(this);
        this.multiinstanceChange = this.multiinstanceChange.bind(this);
        this.insertVarIntoConditions = this.insertVarIntoConditions.bind(this);
        this.handleCheckValue = this.handleCheckValue.bind(this);
        this.handleTaskOwnersRestrictionsChange =
            this.handleTaskOwnersRestrictionsChange.bind(this);
        this.openMenuBox = this.openMenuBox.bind(this);
        this.documentClick = this.documentClick.bind(this);
        this.handleResize = this.handleResize.bind(this);
        this.loadPossibleSolvers = this.loadPossibleSolvers.bind(this);
        this.handleReferenceUserChange =
            this.handleReferenceUserChange.bind(this);
        this.showAllPossibleSolvers = this.showAllPossibleSolvers.bind(this);
        this.hideAllPossibleSolvers = this.hideAllPossibleSolvers.bind(this);
        this.evaluateSolvers = this.evaluateSolvers.bind(this);
        this.someCalculationChanged = this.someCalculationChanged.bind(this);
        this.insertSnippetIntoCodeArea =
            this.insertSnippetIntoCodeArea.bind(this);
        this.insertVarIntoTextArea = this.insertVarIntoTextArea.bind(this);
    }

    componentDidMount() {
        this.setTaskOwnersRestrictions();

        document.addEventListener('click', this.documentClick);
        window.addEventListener('resize', this.handleResize);
    }

    componentDidUpdate(prevProps, prevState) {
        if (!_.isEqual(prevProps.items, this.props.items)) {
            this.setTaskOwnersRestrictions();
        }
    }

    componentWillUnmount() {
        document.removeEventListener('click', this.documentClick);
        window.removeEventListener('resize', this.handleResize);
    }

    setTaskOwnersRestrictions() {
        const { items } = this.props;

        if (!_.isEmpty(items)) {
            this.setState({
                solverRestrictionsObj: {
                    ttask_assesment_role_id: items.ttask_assesment_role_id,
                    ttask_assesment_role_name: items.ttask_assesment_role_name,
                    ttask_assesment_orgstr_cnst:
                        items.ttask_assesment_orgstr_cnst,
                    ttask_assesment_orgstr_name:
                        items.ttask_assesment_orgstr_name,
                    ttask_assesment_hierarchy: items.ttask_assesment_hierarchy,
                    ttask_assesment_method: items.ttask_assesment_method,
                },
            });
        }
    }

    // calculations
    getNewCalcActualVals(calcValues) {
        const formData =
            this.refs.formTemplateTask.refs.frmformTemplateTask.getCurrentValues();
        return TemplateTaskFunctions.getNewCalculationsArr(formData);
    }

    // completion/cancellation filter
    getFilterActualVals(name) {
        const formData =
            this.refs.formTemplateTask.refs.frmformTemplateTask.getCurrentValues();

        return TemplateTaskFunctions.getConditionsArr(formData, name);
    }

    componentWithRowsChanged() {
        if (typeof this.props.componentWithRowsChanged === 'function') {
            this.props.componentWithRowsChanged();
        }
    }

    multiinstanceChange(name, value) {
        this.multiinstance = value;
    }

    insertVarIntoConditions(name, value) {
        const index = name.slice(-1);
        if (
            this.refs.conditionsScript &&
            this.refs.conditionsScript.insertSnippet
        ) {
            this.refs.conditionsScript.insertSnippet(value.value);
        }
        this.refs.insertVar.setValue(null); // to clear selectbox and enabling new selection of the same variable
    }

    convertMapping(mappingArr) {
        if (this.props.taskType === 'S') {
            const mappingObj = { read: [], write: [], must: [] };

            if (mappingArr && Array.isArray(mappingArr)) {
                mappingArr.forEach((tvar) => {
                    if (tvar.usage === 'R') {
                        mappingObj.read.push(tvar.tvar_id);
                    } else if (tvar.usage === 'W') {
                        mappingObj.write.push(tvar.tvar_id);
                    } else if (tvar.usage === 'M') {
                        mappingObj.must.push(tvar.tvar_id);
                    }
                });

                Object.keys(mappingObj).forEach((usg) => {
                    mappingObj[usg] = _.uniq(mappingObj[usg]);
                });
            }

            return mappingObj;
        }

        if (this.props.taskType == 'E') {
            return mappingArr?.map((tvar) => {
                return tvar.tvar_id;
            });
        }
    }

    insertSnippetIntoCodeArea(e, ref) {
        const value = e.target.getAttribute('data-value');

        if (this.refs[ref] && this.refs[ref].insertSnippet) {
            this.refs[ref].insertSnippet(value);
        }

        if (ref === 'conditionsScript') {
            this.setState({
                menuBoxConditionsIsOpen: false,
            });
        } else {
            this.setState({
                menuBoxIsOpen: false,
                evaluateBtnIsActive: true,
            });
        }
    }

    insertVarIntoTextArea(e) {
        const value = e.target.getAttribute('data-value');

        if (
            this.refs.ttask_reference_user &&
            this.refs.ttask_reference_user.insertVar
        ) {
            this.refs.ttask_reference_user.insertVar(value);
        }

        this.setState({
            menuBoxIsOpen: false,
            evaluateBtnIsActive: true,
        });
    }

    handleCheckValue() {
        let eventName = this.refs.ttask_event.getValue();
        eventName =
            eventName && eventName.hasOwnProperty('value')
                ? eventName.value
                : eventName;

        if (this.props.checkEvent) {
            this.props.checkEvent(eventName);
        }
    }

    handleTaskOwnersRestrictionsChange(name, val) {
        const oldState = this.state.solverRestrictionsObj;
        let newState = { [name]: val.value };

        if (name === 'ttask_assesment_role_id') {
            newState = {
                ttask_assesment_role_id: val.value,
                ttask_assesment_role_name: val.title,
            };
        } else if (name === 'ttask_assesment_orgstr_cnst') {
            newState = {
                ttask_assesment_orgstr_cnst: val.value,
                ttask_assesment_orgstr_name: val.title,
            };
        }

        // if assesment_method is "No one, task will be offered..." set value to "Unspecified"
        if (name === 'ttask_assesment_method' && val.value === 'A') {
            newState.ttask_assesment_hierarchy = 'L';
            this.ttaskAssesmentHierarchyRef.current.setValue('L');
        }

        this.setState({
            solverRestrictionsObj: { ...oldState, ...newState },
        });
    }

    getClassName(type, possibleSolversAllowed) {
        const { solverRestrictionsObj } = this.state;
        const onlyReferencePerson =
            solverRestrictionsObj.ttask_assesment_hierarchy === 'G';
        const selectedByComputer =
            solverRestrictionsObj.ttask_assesment_method === 'T';
        const restrictedGroupOfUsers =
            solverRestrictionsObj.ttask_assesment_method === 'A'; // task to pull
        const userWithFewestTasks =
            solverRestrictionsObj.ttask_assesment_method === 'W';
        // role, org. unit
        if (
            (type === 'ttask_assesment_role_id' ||
                type === 'ttask_assesment_orgstr_cnst') &&
            ((onlyReferencePerson && !restrictedGroupOfUsers) ||
                (!selectedByComputer &&
                    !restrictedGroupOfUsers &&
                    !userWithFewestTasks))
        ) {
            return 'gray-bg';
        }
        // relationship to reference person
        if (
            type === 'ttask_assesment_hierarchy' &&
            !selectedByComputer &&
            !restrictedGroupOfUsers &&
            !userWithFewestTasks
        ) {
            return 'gray-bg';
        }

        return '';
    }

    loadPossibleSolvers(showSolverWillBeInfo) {
        const val = this.refs.referenceUserForEval.getValue();

        if (val) {
            const restrictions = this.state.solverRestrictionsObj;
            const relationOptions = [
                { value: 'G', title: i18next.t('assHierarchyGuarantor') },
                { value: 'C', title: i18next.t('assHierarchyChildren') },
                { value: 'D', title: i18next.t('assHierarchyDescendants') },
                { value: 'P', title: i18next.t('assHierarchyParent') },
                { value: 'A', title: i18next.t('assHierarchyAncestors') },
                { value: 'S', title: i18next.t('assHierarchySiblings') },
                { value: 'L', title: i18next.t('unspecified') },
            ];
            const taskWillBeAssignOptions = [
                { value: 'T', title: i18next.t('selectedByComputer') },
                { value: 'S', title: i18next.t('selectedByTaskSupervisor') },
                { value: 'W', title: i18next.t('ownerWithLeastTasks') },
                { value: 'C', title: i18next.t('userByOwnerOfLastTask') },
                { value: 'L', title: i18next.t('lastOwnerOfTask') },
                { value: 'A', title: i18next.t('noOneBeOffered') },
                { value: 'V', title: i18next.t('TaskOwnerWhichInVar') },
            ];

            let ttaskAssesmentHierarchy =
                restrictions.ttask_assesment_hierarchy;

            // if assesment_method is "No one, task will be offered..." -> "Relationship to reference person" is "Unspecified"
            if (restrictions.ttask_assesment_method === 'A') {
                ttaskAssesmentHierarchy = 'L';
            }

            let query = `user_id=${val.value.userId}&orgstr_id=${val.value.orgstrId}&hiearchy=${ttaskAssesmentHierarchy}`;

            if (
                typeof restrictions.ttask_assesment_role_id !== 'undefined' &&
                restrictions.ttask_assesment_role_id !== null
            ) {
                query += `&role_id_constraint=${restrictions.ttask_assesment_role_id}`;
            }
            if (
                typeof restrictions.ttask_assesment_orgstr_cnst !==
                    'undefined' &&
                restrictions.ttask_assesment_orgstr_cnst !== null
            ) {
                query += `&orgstr_id_constraint=${restrictions.ttask_assesment_orgstr_cnst}`;
            }

            ApiRequest.get(`/users/with-orgstr?${query}`)
                .then((payload) => {
                    const possibleSolversArr = [];

                    payload.items.forEach((item) => {
                        possibleSolversArr.push(
                            `${item.user_display_name} (${item.manager_user_id === item.id ? i18next.t('managerIn', { orgUnit: item.orgstr_name }) : item.orgstr_name})`,
                        );
                    });

                    this.setState({
                        possibleSolversArr: possibleSolversArr || [],
                        evaluateBtnIsActive: true,
                        referenceUserSelected: true,
                        showPossibleSolvers: true,
                        showSolverWillBeInfo: showSolverWillBeInfo,
                    });
                })
                .catch((errorMessage) => {
                    this.setState({
                        evaluateBtnIsActive: true,
                    });
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrFailedData'),
                        serverError: errorMessage,
                    });
                });
        } else {
            this.setState({
                possibleSolversArr: [],
                evaluateBtnIsActive: true,
                referenceUserSelected: false,
                showPossibleSolvers: true,
                showSolverWillBeInfo: showSolverWillBeInfo,
            });
        }
    }

    evaluateSolvers(possibleSolversAllowed) {
        this.setState({ evaluateBtnIsActive: false });
        const restrictions = this.state.solverRestrictionsObj;
        let showSolverWillBeInfo = null;

        if (
            restrictions.ttask_assesment_hierarchy === 'G' &&
            (restrictions.ttask_assesment_method === 'T' ||
                restrictions.ttask_assesment_method === 'W')
        ) {
            showSolverWillBeInfo = i18next.t('referenceUser');
        } else if (restrictions.ttask_assesment_method === 'S') {
            showSolverWillBeInfo = i18next.t('selectedByTaskSupervisor');
        } else if (restrictions.ttask_assesment_method === 'L') {
            showSolverWillBeInfo = i18next.t('lastOwnerOfTask');
        } else if (restrictions.ttask_assesment_method === 'W') {
            showSolverWillBeInfo = i18next.t('ownerWithLeastTasks');
        } else if (restrictions.ttask_assesment_method === 'C') {
            showSolverWillBeInfo = i18next.t('userByOwnerOfLastTask');
        } else if (restrictions.ttask_assesment_method === 'V') {
            showSolverWillBeInfo = i18next.t('TaskOwnerWhichInVar');
        } else if (restrictions.ttask_assesment_method === 'A') {
            showSolverWillBeInfo = i18next.t('toPull');
        }

        if (possibleSolversAllowed) {
            this.loadPossibleSolvers(showSolverWillBeInfo);
        } else {
            this.setState({
                showPossibleSolvers: false,
                possibleSolversArr: [],
                showSolverWillBeInfo: showSolverWillBeInfo,
                evaluateBtnIsActive: true,
            });
        }
    }

    openMenuBox(ref) {
        const inputNode = ReactDOM.findDOMNode(this.refs[ref]);
        const componentBottomPos = inputNode.getBoundingClientRect().top; // top edge from the top

        const callback = () => {
            if (this.state.menuBoxIsOpen) {
                const menuBoxHeight = this.menuBox.offsetHeight;

                this.setState({
                    menuBoxHeight: menuBoxHeight,
                    menuBoxTopOrientation:
                        componentBottomPos + menuBoxHeight + 10 >
                        this.state.windowHeight,
                });
            }
        };

        if (ref === 'ttask_reference_user') {
            this.setState(
                { menuBoxIsOpen: !this.state.menuBoxIsOpen },
                callback,
            );
        } else {
            this.setState({
                menuBoxConditionsIsOpen: !this.state.menuBoxConditionsIsOpen,
            });
        }
    }

    documentClick(event) {
        if (!this.menuBoxButton?.contains(event.target)) {
            this.setState({ menuBoxIsOpen: false });
        }
        if (!this.menuBoxButtonConditions?.contains(event.target)) {
            this.setState({ menuBoxConditionsIsOpen: false });
        }
    }

    handleResize() {
        this.setState({ windowHeight: window.innerHeight });

        // menuBox
        if (this.refs.ttask_reference_user) {
            const inputNode = ReactDOM.findDOMNode(
                this.refs.ttask_reference_user,
            );
            const componentBottomPos = inputNode.getBoundingClientRect().bottom; // bottom edge from the top

            this.setState({
                menuBoxTopOrientation:
                    componentBottomPos + this.state.menuBoxHeight + 10 >
                    window.innerHeight,
            });
        }

        // menuBoxConditions always opens down
        // if (this.refs.conditionsScript) {
        //     const inputNode = ReactDOM.findDOMNode(this.refs.conditionsScript);
        //     const componentBottomPos = inputNode.getBoundingClientRect().bottom; // bottom edge from the top
        //
        //     this.setState({ menuBoxConditionsTopOrientation: componentBottomPos + this.state.menuBoxConditionsHeight + 10 > window.innerHeight });
        // }
    }

    handleReferenceUserChange(name, value) {
        if (value && !this.state.evaluateBtnIsActive) {
            this.setState({ evaluateBtnIsActive: true });
        } else if (!value && this.state.evaluateBtnIsActive) {
            this.setState({ evaluateBtnIsActive: false });
        }
    }

    showAllPossibleSolvers(e) {
        if (e) e.preventDefault();
        this.setState({ showAllPossibleSolvers: true });
    }

    hideAllPossibleSolvers(e) {
        if (e) e.preventDefault();
        this.setState({ showAllPossibleSolvers: false });
    }

    someCalculationChanged() {
        if (typeof this.props.someCalculationChanged === 'function') {
            this.props.someCalculationChanged();
        }
    }

    render() {
        const restrictions = this.state.solverRestrictionsObj;

        const typeOptions = [
            { value: 'S', title: i18next.t('taskSolverAssign') },
            { value: 'P', title: i18next.t('taskSubprocess') },
            { value: 'A', title: i18next.t('taskAutomatic') },
            { value: 'N', title: i18next.t('taskEmailNotification') },
            { value: 'E', title: i18next.t('taskEvent') },
            { value: 'W', title: i18next.t('taskEventWait') },
            { value: 'I', title: i18next.t('invitation') },
        ];

        const supervizorOptions = [
            { value: 'PO', title: i18next.t('caseOwner') },
            { value: 'SU', title: i18next.t('selectedUser') },
            { value: 'OS', title: i18next.t('managerOfOrgUnit') },
        ];

        const relatOptions = [
            { value: 'G', title: i18next.t('assHierarchyGuarantor') },
            { value: 'C', title: i18next.t('assHierarchyChildren') },
            { value: 'D', title: i18next.t('assHierarchyDescendants') },
            { value: 'P', title: i18next.t('assHierarchyParent') },
            { value: 'A', title: i18next.t('assHierarchyAncestors') },
            { value: 'S', title: i18next.t('assHierarchySiblings') },
            { value: 'L', title: i18next.t('unspecified') },
        ];

        let disabledRelatOptions = [];
        // if assesment_method is "No one, task will be offered..." only "Unspecified" option is active
        if (restrictions.ttask_assesment_method === 'A') {
            disabledRelatOptions = _.map(
                _.reject(relatOptions, { value: 'L' }),
                'value',
            );
        }

        const taskOptions = [
            { value: 'T', title: i18next.t('selectedByComputer') },
            { value: 'S', title: i18next.t('selectedByTaskSupervisor') },
            { value: 'W', title: i18next.t('ownerWithLeastTasks') },
            { value: 'C', title: i18next.t('userByOwnerOfLastTask') },
            { value: 'L', title: i18next.t('lastOwnerOfTask') },
            { value: 'A', title: i18next.t('noOneBeOffered') },
            { value: 'V', title: i18next.t('TaskOwnerWhichInVar') },
        ];

        const inputOptions = [
            { value: 'O', title: i18next.t('waitsForOne') },
            { value: 'N', title: i18next.t('waitsFor') },
            { value: 'A', title: i18next.t('waitsForAll') },
            // { value: 'D', title: i18next.t('waitsRunFirst') }, // t3b-1230 návrh na zrušení: chování na vstupu - čeká na všechny, spouští první
        ];

        const startOptions = [
            { value: 'no', title: i18next.t('tTaskDueOffsetNone') },
            { value: 'po', title: i18next.t('tTaskDueOffsetPO') },
            { value: 'ps', title: i18next.t('tTaskDueOffsetPS') },
            { value: 'ts', title: i18next.t('tTaskDueOffsetTS') },
            { value: 'vo', title: i18next.t('tTaskDueOffsetVO') },
            { value: 'vc', title: i18next.t('tTaskDueOffsetVC') },
        ];

        const termOptions = [
            { value: 'no', title: i18next.t('isNot') },
            { value: 'po', title: i18next.t('tTaskDueOffsetPO') },
            { value: 'ps', title: i18next.t('tTaskDueOffsetPS') },
            { value: 'ts', title: i18next.t('tTaskDueOffsetTS') },
            { value: 'vo', title: i18next.t('tTaskDueOffsetVO') },
            { value: 'vc', title: i18next.t('tTaskDueOffsetVC') },
        ];

        const condOptions = [
            { value: 'A', title: i18next.t('tTaskCompletionCOA') },
            { value: 'O', title: i18next.t('tTaskCompletionCOO') },
        ];

        const helperMenuOptions = [
            { value: 'supervisor()', title: i18next.t('supervisor') },
            { value: 'initiator()', title: i18next.t('caseOwner') },
            {
                value: 'manager("xy")',
                title: i18next.t('tTaskReferenceUserMan'),
            },
            { value: 'user("xy")', title: i18next.t('tTaskReferenceUserUser') },
            {
                value: 'lastSolverOf("xy")',
                title: i18next.t('tTaskReferenceUserLastSolver'),
            },
        ];

        const helperMenuIterateOptions = this.props.templateVarsAllOptions.map(
            (o) => {
                return { value: o.title, title: o.title };
            },
        );

        helperMenuIterateOptions.unshift({ value: null, title: null }); // first empty option

        const notificationOptions = [
            { value: 'O', title: i18next.t('caseOwner') },
            { value: 'G', title: i18next.t('supervisor') },
            { value: 'T', title: i18next.t('taskOwner') },
            { value: 'P', title: i18next.t('email') },
            { value: 'S', title: i18next.t('plnOrgUnit') },
            { value: 'U', title: i18next.t('OfVariable') },
            { value: 'R', title: i18next.t('roles') },
        ];

        const replyNotificationOptions = _.cloneDeep(notificationOptions);
        replyNotificationOptions.unshift({
            value: null,
            title: i18next.t('default'),
        });

        const blindCopyNotificationOptions = _.cloneDeep(notificationOptions);
        blindCopyNotificationOptions.unshift({
            value: null,
            title: i18next.t('no'),
        });

        const invitationOptionsClass = [
            { value: 'PUBL', title: i18next.t('tTaskInvClassPubl') },
            { value: 'PRIV', title: i18next.t('tTaskInvClassPriv') },
            { value: 'CONF', title: i18next.t('tTaskInvClassConf') },
        ];

        const invitationOptionsPriority = [
            { value: '0', title: i18next.t('unspecified') },
            { value: '1', title: i18next.t('tTaskInvPriority1') },
            { value: '2', title: i18next.t('tTaskInvPriority2') },
            { value: '3', title: i18next.t('tTaskInvPriority3') },
            { value: '4', title: i18next.t('tTaskInvPriority4') },
            { value: '5', title: i18next.t('tTaskInvPriority5') },
            { value: '6', title: i18next.t('tTaskInvPriority6') },
            { value: '7', title: i18next.t('tTaskInvPriority7') },
            { value: '8', title: i18next.t('tTaskInvPriority8') },
            { value: '9', title: i18next.t('tTaskInvPriority9') },
        ];

        const eventRunOptions = [
            { value: 'I', title: i18next.t('tTaskInvokeEventI') },
            { value: 'B', title: i18next.t('tTaskInvokeEventB') },
        ];

        const taskAgainOptions = [
            { value: 'N', title: i18next.t('continueSolving') },
            { value: 'A', title: i18next.t('reEvaluates') },
        ];

        const taskDutyOptions = [
            { value: 'Y', title: i18next.t('duty') },
            { value: 'N', title: i18next.t('right') },
        ];

        const { items } = this.props;
        const { taskType } = this.props;
        const showCaseVariables = taskType === 'P' || taskType === 'W';

        /**
         * getValue for form components
         * @param  {string, number, object} value
         * @param  {array} options
         * @return {string, number} value or first option value
         */
        const getValue = function (value, options) {
            if (typeof value === 'undefined' || value === null) {
                if (typeof options[0] !== 'undefined') {
                    return options[0].value;
                }
            } else {
                if (
                    typeof value !== 'undefined' &&
                    value !== null &&
                    typeof value.type !== 'undefined'
                ) {
                    return value.type;
                } /* else if (typeof value !== 'undefined' && value !== null && typeof value.value !== 'undefined') {
                 return value.value;
                 } */
                return String(value);
            }
        };

        /**
         * getObjectValue for form components
         * @param  {string, number, object} value
         * @param  {string} defaultValue
         * @param  {array} options
         * @return {string} value, defaultValue or first option value
         */
        const getObjectValue = function (value, defaultValue, options) {
            if (typeof value !== 'undefined' && value !== null) {
                return value.value;
            }
            if (defaultValue !== null) {
                return defaultValue;
            }
            if (typeof options[0] !== 'undefined') {
                return options[0].value;
            }
        };

        const getOrgUnitValue = function (item) {
            if (typeof item === 'undefined' || item === null || item == 0) {
                return { value: '0', title: 'Root' };
            }
            return item;
        };

        const getAttendees = function (item) {
            if (
                typeof item !== 'undefined' &&
                item !== null &&
                !_.isEmpty(item)
            ) {
                return item.split(';');
            }
            return [];
        };

        const convertCalculations = () => {
            let calcArr = [];
            // ttask_operations are from diagram store
            if (items.ttask_operations) {
                items.ttask_operations.forEach((calc) => {
                    calcArr.push({
                        id: calc.id,
                        calcSendEs6: calc.calcSendEs6,
                        ttjscalc_js: calc.ttjscalc_js,
                        ttjscalc_js_es6: calc.ttjscalc_js_es6,
                        ttjscalc_exec_start: calc.ttjscalc_exec_start,
                        ttjscalc_exec_end: calc.ttjscalc_exec_end,
                        ttjscalc_exec_hand: calc.ttjscalc_exec_hand,
                        ttjscalc_exec_recalc: calc.ttjscalc_exec_recalc,
                        ttjscalc_exec_pull: calc.ttjscalc_exec_pull,
                        ttjscalc_append_scripts: calc.ttjscalc_append_scripts,
                    });
                });
            } else {
                calcArr = items.ttask_calculations;
            }

            return calcArr;
        };

        const getTtcConcatOpValue = (filter, options) => {
            if (!_.isEmpty(filter)) {
                return filter[0].ttc_concat_operator;
            }
            return options[0].value;
        };

        const getEnotOfVariableValue = (value, options) => {
            if (typeof value === 'undefined' || value === null) {
                if (typeof options[0] !== 'undefined') {
                    return options[0];
                }

                return null;
            }

            return String(value);
        };

        const { languages } = LoggedUserStore.getState();
        const languageOptions = languages.map((l) => {
            return { title: l, value: l };
        });
        languageOptions.unshift({ value: null, title: i18next.t('default') });
        const daysDefValue = '00:00:00';

        // for edit calculation modal
        if (
            typeof this.multiinstance === 'undefined' &&
            typeof items.ttask_multiinstance_flag !== 'undefined'
        ) {
            this.multiinstance = items.ttask_multiinstance_flag === 'Y';
        }

        let { templateVarsAllOptions } = this.props;
        if (this.multiinstance) {
            templateVarsAllOptions = templateVarsAllOptions.filter((o) => {
                return o.tvar_copy_snapshot === 'Y';
            });
        }

        const getValueWithVersion = (id, version, options) => {
            if (typeof id === 'undefined' || id === null) {
                if (typeof options[0] !== 'undefined') {
                    return options[0].value;
                }
            } else if (id === -1) {
                return null;
            }

            return `${id}-${version || 1}`;
        };

        const condsVarOptions = templateVarsAllOptions.map((option) => {
            return {
                text: option.title,
                displayText: option.title,
            };
        });

        // reference user
        const {
            possibleSolversArr,
            showAllPossibleSolvers,
            showSolverWillBeInfo,
            referenceUserSelected,
            showPossibleSolvers,
        } = this.state;

        let possibleSolversToShow;
        if (showAllPossibleSolvers) {
            possibleSolversToShow = possibleSolversArr;
        } else {
            possibleSolversToShow = possibleSolversArr.slice(0, 4);
        }

        const solversToShowLeft =
            possibleSolversArr.length - possibleSolversToShow.length;

        let possibleSolversAllowed = false;

        if (
            restrictions.ttask_assesment_hierarchy === 'G' &&
            (restrictions.ttask_assesment_method === 'T' ||
                restrictions.ttask_assesment_method === 'W')
        ) {
            possibleSolversAllowed = false;
        } else if (
            restrictions.ttask_assesment_method === 'S' ||
            restrictions.ttask_assesment_method === 'C' ||
            restrictions.ttask_assesment_method === 'L' ||
            restrictions.ttask_assesment_method === 'V'
        ) {
            possibleSolversAllowed = false;
        } else {
            possibleSolversAllowed = true;
        }

        return (
            <Form
                ref="formTemplateTask"
                name="formTemplateTask"
                onValidSubmit={this.props.handleValidSubmit}
                onValidSubmitIgnoreRequired={
                    this.props.handleValidSubmitWithoutRequire
                }
                onInvalidSubmit={this.props.handleInvalidSubmit}
                onChange={this.props.formChanged}
            >
                <HiddenTabContent key="HiddenTabContentActivity">
                    <Label
                        key="task"
                        label={i18next.t('activity')}
                        side="left"
                        fontSize="1rem"
                        fullLabel
                        noValueHeight
                    />
                    <Text
                        key="ttask_name"
                        /* label={i18next.t('defaultLbl', {label:"$t(fsName)"}) + ":"} */
                        label={`${i18next.t('defaultTaskName')}:`}
                        value={items.ttask_name}
                        side="left"
                        required
                        validationErrors={{
                            isDefaultRequiredValue: i18next.t('isRequired'),
                        }}
                    />
                    <ShowHideComponent key="langMutName" side="left">
                        {languages.map((lang, i) => {
                            return (
                                <Text
                                    key={`ttaskName${lang}`}
                                    name={`ttask_name_${lang}`}
                                    label={`${i18next.t('tskName')}:`}
                                    value={items[`ttask_name_${lang}`]}
                                    lblLang={lang}
                                    showComponentLabel
                                />
                            );
                        })}
                    </ShowHideComponent>
                    <TextArea
                        key="ttask_description"
                        label={`${i18next.t('defaultLbl', { label: '$t(fsDescription)' })}:`}
                        value={items.ttask_description}
                        side="left"
                        rows={3}
                        showComponentLabel
                    />
                    <ShowHideComponent key="langMutDesc" side="left">
                        {languages.map((lang, i) => {
                            return (
                                <TextArea
                                    key={`ttaskDescription${lang}`}
                                    name={`ttask_description_${lang}`}
                                    label={`${i18next.t('description')}:`}
                                    side="left"
                                    rows={3}
                                    value={items[`ttask_description_${lang}`]}
                                    lblLang={lang}
                                    showComponentLabel
                                />
                            );
                        })}
                    </ShowHideComponent>
                    <SelectBox
                        key="ttask_type"
                        label={`${i18next.t('activityType')}:`}
                        options={typeOptions}
                        side="left"
                        nullable={false}
                        onChange={this.props.taskTypeChange}
                        value={taskType}
                        required
                        validationErrors={{
                            isDefaultRequiredValue: i18next.t('isRequired'),
                        }}
                    />
                    {taskType === 'P' && (
                        <SelectBox
                            key="ttask_subprocess_tproc_id"
                            label={`${i18next.t('subProcess')}:`}
                            options={this.props.templateProcessesOptions}
                            value={getValueWithVersion(
                                items.ttask_subprocess_tproc_id,
                                items.ttask_subprocess_tproc_version,
                                this.props.templateProcessesOptions,
                            )}
                            nullable={false}
                            side="left"
                            onChange={this.props.processChange}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType == 'E' && (
                        <SelectBox
                            key="ttask_event"
                            ref="ttask_event"
                            label={`${i18next.t('event')}:`}
                            nullable={false}
                            side="left"
                            options={this.props.eventsOptions}
                            value={items.ttask_event}
                            additionalButton={
                                <span
                                    className="icon icon-upload-2 evt-button button-pad"
                                    onClick={this.handleCheckValue}
                                />
                            }
                            onChange={this.props.eventChange}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType == 'W' && (
                        <SelectBox
                            key="ttask_event"
                            ref="ttask_event"
                            label={`${i18next.t('event')}:`}
                            nullable={false}
                            side="left"
                            options={this.props.eventsOptions}
                            value={items.ttask_event}
                            additionalButton={
                                <span
                                    className="icon icon-download-2 evt-button button-pad"
                                    onClick={this.handleCheckValue}
                                />
                            }
                            onChange={this.props.eventChange}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType == 'W' && (
                        <Checkbox
                            key="ttask_can_be_button"
                            label={`${i18next.t('enableButtonInTasks')}:`}
                            side="left"
                            value={
                                typeof items.ttask_can_be_button !== 'undefined'
                                    ? items.ttask_can_be_button == 'Y'
                                    : false
                            }
                            showComponentLabel
                        />
                    )}
                    <SelectBox
                        key="ttask_garant"
                        label={`${i18next.t('supervis')}:`}
                        nullable={false}
                        options={supervizorOptions}
                        side="left"
                        toShowGroups={{ SU: 'user', OS: 'unit' }}
                        value={getValue(items.ttask_garant, supervizorOptions)}
                    />
                    <SelectBox
                        key="ttask_assesment_user_id"
                        label={`${i18next.t('user')}:`}
                        side="left"
                        selectBoxType="DLU"
                        visibilityGroups={['user']}
                        nullable={false}
                        defaultValue={items.ttask_assesment_user_name}
                        value={items.ttask_assesment_user_id}
                        required
                        validationErrors={{
                            isDefaultRequiredValue: i18next.t('isRequired'),
                        }}
                    />
                    <Text
                        key="ttask_assesment_user"
                        label=""
                        value={items.ttask_assesment_user_name || ''}
                        side="left"
                        type="hidden"
                        visibilityGroups={['user']}
                    />
                    <SelectBox
                        key="ttask_assesment_orgstr_id"
                        label={`${i18next.t('orgUnitShe')}:`}
                        side="left"
                        selectBoxType="DLO"
                        visibilityGroups={['unit']}
                        nullable={false}
                        value={
                            typeof items.ttask_assesment_orgstr_id ===
                                'undefined' ||
                            items.ttask_assesment_orgstr_id === null ||
                            items.ttask_assesment_orgstr_id == 0
                                ? { value: '0', title: 'Root' }
                                : items.ttask_assesment_orgstr_id
                        }
                    />
                    <Checkbox
                        key="ttask_gen_history"
                        label={`${i18next.t('generateRecHistory')}:`}
                        side="left"
                        value={
                            typeof items.ttask_gen_history !== 'undefined'
                                ? items.ttask_gen_history == 'Y'
                                : true
                        }
                    />
                    <Checkbox
                        key="ttask_is_delegatable"
                        label={`${i18next.t('enableTaskDelegation')}:`}
                        side="left"
                        value={
                            typeof items.ttask_is_delegatable !== 'undefined'
                                ? items.ttask_is_delegatable == 'Y'
                                : true
                        }
                        showComponentLabel
                    />
                    <Checkbox
                        key="ttask_is_rejectable"
                        label={`${i18next.t('enableRejectTask')}:`}
                        side="left"
                        value={
                            typeof items.ttask_is_rejectable !== 'undefined'
                                ? items.ttask_is_rejectable == 'Y'
                                : false
                        }
                        showComponentLabel
                    />
                    {taskType === 'S' && (
                        <Checkbox
                            key="ttask_is_bulk_completable"
                            label={`${i18next.t('enableBulkCompletion')}:`}
                            side="left"
                            value={items.ttask_is_bulk_completable === 'Y'}
                            showComponentLabel
                            toShowGroups={{ true: 'bulkCompletionAssignments' }}
                        />
                    )}
                    {taskType != 'W' && (
                        <Label
                            key="planningLabel"
                            label={i18next.t('plans')}
                            side="right"
                            fontSize="1rem"
                            fullLabel
                            noValueHeight
                        />
                    )}
                    {taskType != 'W' && (
                        <SelectBox
                            key="ttask_petri_net_input"
                            label={`${i18next.t('inputParams')}:`}
                            side="right"
                            options={inputOptions}
                            toShowGroups={{ N: 'waitFor' }}
                            nullable={false}
                            value={getValue(
                                items.ttask_petri_net_input,
                                inputOptions,
                            )}
                        />
                    )}
                    {taskType != 'W' && (
                        <Text
                            key="ttask_petri_net_inputn"
                            label={`${i18next.t('waitForNumOfInputs')}:`}
                            side="right"
                            visibilityGroups={['waitFor']}
                            value={
                                items.ttask_petri_net_input == 'N' &&
                                items.ttask_petri_net_inputn
                                    ? items.ttask_petri_net_inputn
                                    : ''
                            }
                            required
                            validations={{ isInt: true }}
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                                isInt: i18next.t('notIntNumber'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <SelectBox
                            key="ttask_due_offset"
                            label={`${i18next.t('taskStart')}:`}
                            side="right"
                            options={startOptions}
                            toShowGroups={{
                                ps: 'day1',
                                ts: 'day2',
                                vo: 'var1',
                                vc: 'var2',
                            }}
                            nullable={false}
                            value={getValue(
                                items.ttask_due_offset === null
                                    ? 'no'
                                    : items.ttask_due_offset,
                                startOptions,
                            )}
                        />
                    )}
                    {taskType != 'W' && (
                        <Text
                            key="ttask_due_offset_days_ps"
                            label={`${i18next.t('daysDHM')}:`}
                            side="right"
                            visibilityGroups={['day1']}
                            value={
                                typeof items.ttask_due_offset !== 'undefined' &&
                                items.ttask_due_offset !== null &&
                                items.ttask_due_offset.type == 'ps'
                                    ? getObjectValue(
                                        items.ttask_due_offset,
                                        daysDefValue,
                                    )
                                    : daysDefValue
                            }
                            required
                            validations={{
                                matchesRegular:
                                    /^([0-9]\d|\d\d\d):([01]\d|2[0-3]):([0-5][0-9])$/,
                            }}
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                                matchesRegular: i18next.t('notInRightormat'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <Text
                            key="ttask_due_offset_days_ts"
                            label={`${i18next.t('daysDHM')}:`}
                            side="right"
                            visibilityGroups={['day2']}
                            value={
                                typeof items.ttask_due_offset !== 'undefined' &&
                                items.ttask_due_offset !== null &&
                                items.ttask_due_offset.type == 'ts'
                                    ? getObjectValue(
                                        items.ttask_due_offset,
                                        daysDefValue,
                                    )
                                    : daysDefValue
                            }
                            required
                            validations={{
                                matchesRegular:
                                    /^([0-9]\d|\d\d\d):([01]\d|2[0-3]):([0-5][0-9])$/,
                            }}
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                                matchesRegular: i18next.t('notInRightormat'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <SelectBox
                            key="ttask_due_offset_var"
                            label={`${i18next.t('variable')}:`}
                            side="right"
                            visibilityGroups={['var1', 'var2']}
                            nullable={false}
                            options={this.props.templateVarsDateOptions}
                            value={
                                typeof items.ttask_due_offset !== 'undefined' &&
                                items.ttask_due_offset !== null &&
                                (items.ttask_due_offset.type == 'vo' ||
                                    items.ttask_due_offset.type == 'vc')
                                    ? getObjectValue(
                                        items.ttask_due_offset,
                                        null,
                                        this.props.templateVarsDateOptions,
                                    )
                                    : this.props.templateVarsDateOptions[0]
                            }
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <SelectBox
                            key="ttask_duration"
                            label={`${i18next.t('dueDate')}:`}
                            side="right"
                            options={termOptions}
                            toShowGroups={{
                                ps: 'day3',
                                ts: 'day4',
                                vo: 'var3',
                                vc: 'var4',
                            }}
                            nullable={false}
                            value={getValue(
                                items.ttask_duration === null
                                    ? 'no'
                                    : items.ttask_duration,
                                termOptions,
                            )}
                        />
                    )}
                    {taskType != 'W' && (
                        <Text
                            key="ttask_duration_days_ps"
                            label={`${i18next.t('daysDHM')}:`}
                            side="right"
                            visibilityGroups={['day3']}
                            value={
                                typeof items.ttask_duration !== 'undefined' &&
                                items.ttask_duration !== null &&
                                items.ttask_duration.type == 'ps'
                                    ? getObjectValue(
                                        items.ttask_duration,
                                        daysDefValue,
                                    )
                                    : daysDefValue
                            }
                            required
                            validations={{
                                matchesRegular:
                                    /^([0-9]\d|\d\d\d):([01]\d|2[0-3]):([0-5][0-9])$/,
                            }}
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                                matchesRegular: i18next.t('notInRightormat'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <Text
                            key="ttask_duration_days_ts"
                            label={`${i18next.t('daysDHM')}:`}
                            side="right"
                            visibilityGroups={['day4']}
                            value={
                                typeof items.ttask_duration !== 'undefined' &&
                                items.ttask_duration !== null &&
                                items.ttask_duration.type == 'ts'
                                    ? getObjectValue(
                                        items.ttask_duration,
                                        daysDefValue,
                                    )
                                    : daysDefValue
                            }
                            required
                            validations={{
                                matchesRegular:
                                    /^([0-9]\d|\d\d\d):([01]\d|2[0-3]):([0-5][0-9])$/,
                            }}
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                                matchesRegular: i18next.t('notInRightormat'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <SelectBox
                            key="ttask_duration_var"
                            label={`${i18next.t('variable')}:`}
                            side="right"
                            visibilityGroups={['var3', 'var4']}
                            nullable={false}
                            options={this.props.templateVarsDateOptions}
                            value={
                                typeof items.ttask_duration !== 'undefined' &&
                                items.ttask_duration !== null &&
                                (items.ttask_duration.type == 'vo' ||
                                    items.ttask_duration.type == 'vc')
                                    ? getObjectValue(
                                        items.ttask_duration,
                                        null,
                                        this.props.templateVarsDateOptions,
                                    )
                                    : this.props.templateVarsDateOptions[0]
                            }
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType != 'W' && (
                        <Checkbox
                            key="ttask_sufficient_end"
                            label={`${i18next.t('tTaskSufficientEnd')}:`}
                            side="right"
                            value={items.ttask_sufficient_end == 'Y'}
                        />
                    )}
                    {taskType != 'W' && (
                        <Checkbox
                            key="ttask_run_only_once"
                            label={`${i18next.t('tTaskRunOnlyOnce')}:`}
                            side="right"
                            value={items.ttask_run_only_once == 'Y'}
                        />
                    )}
                    {taskType != 'W' && (
                        <Checkbox
                            key="ttask_multiinstance_flag"
                            label={`${i18next.t('multiInstance')}:`}
                            side="right"
                            value={items.ttask_multiinstance_flag == 'Y'}
                            toShowGroups={{ true: 'multiinstance' }}
                            onComponentChange={this.multiinstanceChange}
                        />
                    )}
                    {taskType != 'W' && (
                        <SelectBox
                            key="ttask_iterate_over"
                            label={`${i18next.t('iterateOverVars')}:`}
                            side="right"
                            visibilityGroups={['multiinstance']}
                            options={helperMenuIterateOptions}
                            nullable={false}
                            value={getValue(
                                items.ttask_iterate_over,
                                helperMenuIterateOptions,
                            )}
                        />
                    )}
                    {taskType === 'S' && (
                        <MultiBoxTriple
                            key="ttask_mass_mapping"
                            name="ttask_mass_mapping"
                            side="right"
                            label={`${i18next.t('bulkCompletionVars')}:`}
                            options={templateVarsAllOptions}
                            value={this.convertMapping(
                                items.ttask_mass_mapping,
                            )}
                            visibilityGroups={['bulkCompletionAssignments']}
                            additionalClass="smaller-size"
                            hideReadField
                        />
                    )}
                </HiddenTabContent>

                <HiddenTabContent key="HiddenTabContentCustom" tabName="custom">
                    {/* Solver */}
                    {taskType != 'A' && taskType != 'N' && (
                        <Label
                            key="solver"
                            label={i18next.t('restrictTaskOwners')}
                            side="left"
                            fontSize="1rem"
                            fullLabel
                            noValueHeight
                        />
                    )}
                    {taskType !== 'A' && taskType !== 'N' && (
                        <WrapComponent key="solverRole" side="left">
                            <SelectBox
                                key="ttask_assesment_role_id"
                                name="ttask_assesment_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                defaultValue={items.ttask_assesment_role_name}
                                value={items.ttask_assesment_role_id}
                                onChange={
                                    this.handleTaskOwnersRestrictionsChange
                                }
                                className={this.getClassName(
                                    'ttask_assesment_role_id',
                                )}
                            />
                            <Text
                                key="ttask_assesment_role_name"
                                name="ttask_assesment_role_name"
                                label=""
                                value={items.ttask_assesment_role_name || ''}
                                type="hidden"
                            />
                        </WrapComponent>
                    )}
                    {taskType !== 'A' && taskType !== 'N' && (
                        <SelectBox
                            key="ttask_assesment_orgstr_cnst"
                            label={`${i18next.t('onlyOrgUnit')}:`}
                            side="left"
                            selectBoxType="DLO"
                            value={
                                items.ttask_assesment_orgstr_cnst === 0
                                    ? { value: 0, title: 'Root' }
                                    : items.ttask_assesment_orgstr_cnst
                            }
                            onChange={this.handleTaskOwnersRestrictionsChange}
                            className={this.getClassName(
                                'ttask_assesment_orgstr_cnst',
                            )}
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <TextArea
                            name="ttask_reference_user"
                            ref="ttask_reference_user"
                            key="referenceUser"
                            label={`${i18next.t('referenceUser')}:`}
                            side="left"
                            allowResize={false}
                            rows={1}
                            onChange={this.handleReferenceUserChange}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                            value={getValue(
                                items.ttask_reference_user,
                                helperMenuOptions,
                            )}
                            labelButtonOnClick={this.props.showDocs}
                            labelButtonIcon="icon icon-bubble-ask-2"
                            labelButtonTitle="help"
                            additionalButton={
                                <div>
                                    <span
                                        className="change-button menu-box-button icon icon-setting-gear"
                                        ref={(c) => (this.menuBoxButton = c)}
                                        onClick={() =>
                                            this.openMenuBox(
                                                'ttask_reference_user',
                                            )}
                                    >
                                        <div
                                            ref={(c) => (this.menuBox = c)}
                                            className={cx(
                                                'menu-box',
                                                {
                                                    open: this.state
                                                        .menuBoxIsOpen,
                                                },
                                                {
                                                    'open-up':
                                                        this.state
                                                            .menuBoxTopOrientation,
                                                },
                                            )}
                                        >
                                            <ul>
                                                {helperMenuOptions.map(
                                                    (option, i) => (
                                                        <li key={i}>
                                                            <a
                                                                ref={(c) =>
                                                                    (this.option =
                                                                        c)}
                                                                data-value={
                                                                    option.value
                                                                }
                                                                onClick={
                                                                    this
                                                                        .insertVarIntoTextArea
                                                                }
                                                            >
                                                                {option.title}
                                                            </a>
                                                        </li>
                                                    ),
                                                )}
                                            </ul>
                                        </div>
                                    </span>
                                </div>
                            }
                        />
                    )}
                    {taskType !== 'A' && taskType !== 'N' && (
                        <SelectBox
                            key="ttask_assesment_hierarchy"
                            ref={this.ttaskAssesmentHierarchyRef}
                            label={`${i18next.t('assHierarchy')}:`}
                            side="left"
                            options={relatOptions}
                            nullable={false}
                            value={getValue(
                                items.ttask_assesment_hierarchy,
                                relatOptions,
                            )}
                            onChange={this.handleTaskOwnersRestrictionsChange}
                            className={this.getClassName(
                                'ttask_assesment_hierarchy',
                            )}
                            disabledOptions={disabledRelatOptions}
                        />
                    )}
                    {taskType !== 'A' && taskType !== 'N' && (
                        <SelectBox
                            key="ttask_assesment_method"
                            label={`${i18next.t('taskWillBeAssigned')}:`}
                            side="left"
                            options={taskOptions}
                            toShowGroups={{
                                C: 'task',
                                L: 'task2',
                                V: 'variable',
                            }}
                            nullable={false}
                            value={getValue(
                                items.ttask_assesment_method,
                                taskOptions,
                            )}
                            onComponentChange={
                                this.handleTaskOwnersRestrictionsChange
                            }
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <SelectBox
                            key="ttask_assesment_ttask_id"
                            label={`${i18next.t('tsk')}:`}
                            side="left"
                            options={this.props.templateTasksOptions}
                            visibilityGroups={['task', 'task2']}
                            nullable={false}
                            value={getValue(
                                items.ttask_assesment_ttask_id,
                                this.props.templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <SelectBox
                            key="ttask_assesment_tvar_id"
                            label={`${i18next.t('variable')}:`}
                            side="left"
                            visibilityGroups={['variable']}
                            nullable={false}
                            options={this.props.templateVarsUserOptions}
                            value={getValue(
                                items.ttask_assesment_tvar_id,
                                this.props.templateVarsUserOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <SelectBox
                            key="ttask_again"
                            label={`${i18next.t('tTaskAgain')}:`}
                            side="left"
                            options={taskAgainOptions}
                            nullable={false}
                            value={getValue(
                                items.ttask_again,
                                taskAgainOptions,
                            )}
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <SelectBox
                            key="ttask_duty"
                            label={`${i18next.t('rightOrDuty')}:`}
                            side="left"
                            nullable={false}
                            options={taskDutyOptions}
                            value={getValue(items.ttask_duty, taskDutyOptions)}
                        />
                    )}

                    {/* Solver Evaluation */}
                    {taskType != 'A' && taskType != 'N' && (
                        <EmptyComponent
                            key="solverEvaluationEmpty"
                            side="left"
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <Label
                            key="solverEvaluation"
                            label={i18next.t('solverEvaluation')}
                            side="left"
                            fontSize="1rem"
                            fullLabel
                            noValueHeight
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <SelectBoxPure
                            key="referenceUserForEval"
                            ref="referenceUserForEval"
                            label={`${i18next.t('referenceUserForEval')}:`}
                            selectBoxType="DLU"
                            dataUrl="/users/chairs"
                            nullable={false}
                            readonly={!possibleSolversAllowed}
                            className={cx({
                                'gray-bg': !possibleSolversAllowed,
                            })}
                            side="left"
                        />
                    )}
                    {taskType != 'A' && taskType != 'N' && (
                        <Button
                            key="evaluateSolver"
                            side="left"
                            label=" "
                            buttonText={i18next.t('evaluateSolver')}
                            onClick={() =>
                                this.evaluateSolvers(possibleSolversAllowed)}
                            isActive={this.state.evaluateBtnIsActive}
                            isLargeButton
                        />
                    )}
                    {showSolverWillBeInfo && (
                        <Label
                            key="solverWillBe"
                            side="left"
                            label={`${i18next.t('solverWillBe')} / ${i18next.t('taskWillBeAssigned')}:`}
                            value={showSolverWillBeInfo}
                        />
                    )}
                    {showPossibleSolvers && (
                        <div
                            key="possible-solvers"
                            className="possible-solvers row"
                            side="left"
                        >
                            <div className="medium-5 small-12 columns label-t ">
                                {`${i18next.t('possibleSolvers')}:`}
                            </div>
                            <div className="medium-7 small-12 columns info">
                                {!referenceUserSelected &&
                                    i18next.t('selectReferencePerson')}
                                {referenceUserSelected &&
                                    _.isEmpty(possibleSolversArr) &&
                                    i18next.t('empty')}
                            </div>
                            <div className="row scrollable">
                                <ul className="small-12 columns">
                                    {possibleSolversToShow.map((user, i) => {
                                        return (
                                            <li key={`user-${i}`}>{user}</li>
                                        );
                                    })}
                                    {!showAllPossibleSolvers &&
                                        solversToShowLeft !== 0 && (
                                        <li key="show">
                                            <a
                                                className="green"
                                                onClick={
                                                    this
                                                        .showAllPossibleSolvers
                                                }
                                            >
                                                {`${i18next.t('andOthers')} (${solversToShowLeft})`}
                                            </a>
                                        </li>
                                    )}
                                    {showAllPossibleSolvers &&
                                        possibleSolversToShow.length > 4 && (
                                        <li key="hide">
                                            <a
                                                className="green"
                                                onClick={
                                                    this
                                                        .hideAllPossibleSolvers
                                                }
                                            >
                                                {i18next.t('showLess')}
                                            </a>
                                        </li>
                                    )}
                                </ul>
                            </div>
                        </div>
                    )}

                    {/* Completion */}
                    {taskType != 'A' &&
                        taskType != 'N' &&
                        taskType != 'P' &&
                        taskType != 'E' &&
                        taskType != 'I' && (
                        <WrapComponent key="completion" side="right">
                            <Label
                                key="completionLabel"
                                label={i18next.t('completion')}
                                fontSize="1rem"
                                fullLabel
                                noValueHeight
                                showComponentLabel
                            />
                            <SelectBox
                                key="ttask_completion"
                                name="ttask_completion"
                                label={`${i18next.t('tTaskAutoCompleteCaption')}:`}
                                options={condOptions}
                                nullable={false}
                                value={getTtcConcatOpValue(
                                    this.props.completionFilter,
                                    condOptions,
                                )}
                            />
                            <Filter
                                key="completionFilter"
                                name="completionFilter"
                                ref="tempVarFilter"
                                label={`${i18next.t('conditions')}:`}
                                variablesOptions={
                                    this.props
                                        .completionFilterVariablesOptions
                                }
                                value={this.props.completionFilter}
                                conditionsOperators
                                onChange={this.componentWithRowsChanged}
                                getFilterActualVals={this.getFilterActualVals.bind(
                                    null,
                                    'completionFilter',
                                )}
                                checkboxIsVisible={false}
                                suggestList={this.props.suggestList}
                            />

                            <Label
                                key="cancellationLabel"
                                label={i18next.t('cancellation')}
                                fontSize="1rem"
                                fullLabel
                                noValueHeight
                                showComponentLabel
                            />
                            <SelectBox
                                key="ttask_cancellation"
                                name="ttask_cancellation"
                                label={`${i18next.t('tTaskAutoCancellCaption')}:`}
                                options={condOptions}
                                nullable={false}
                                value={getTtcConcatOpValue(
                                    this.props.cancellationFilter,
                                    condOptions,
                                )}
                            />
                            <Filter
                                key="cancellationFilter"
                                name="cancellationFilter"
                                ref="cancellationFilter"
                                label={`${i18next.t('conditions')}:`}
                                variablesOptions={
                                    this.props
                                        .completionFilterVariablesOptions
                                }
                                value={this.props.cancellationFilter}
                                onChange={this.componentWithRowsChanged}
                                getFilterActualVals={this.getFilterActualVals.bind(
                                    null,
                                    'cancellationFilter',
                                )}
                                conditionsOperators
                                checkboxIsVisible={false}
                                suggestList={this.props.suggestList}
                            />
                        </WrapComponent>
                    )}

                    {/* Event */}
                    {taskType == 'E' && (
                        <WrapComponent key="event" side="right">
                            <Label
                                key="eventLabel"
                                label={i18next.t('event')}
                                fontSize="1rem"
                                fullLabel
                                noValueHeight
                                showComponentLabel
                            />
                            <SelectBox
                                key="ttask_invoke_event"
                                name="ttask_invoke_event"
                                label={`${i18next.t('eventsRun')}:`}
                                options={eventRunOptions}
                                nullable={false}
                                value={getValue(
                                    items.ttask_invoke_event,
                                    eventRunOptions,
                                )}
                            />
                        </WrapComponent>
                    )}

                    {/* Variable assignments */}
                    {taskType === 'E' && (
                        <EmptyComponent key="varAssignEmpty" side="right" />
                    )}
                    {taskType === 'E' && (
                        <WrapComponent key="assignVars" side="right">
                            <Label
                                key="varias"
                                label={i18next.t('taskTabVariables')}
                                fontSize="1rem"
                                fullLabel
                                noValueHeight
                                showComponentLabel
                            />
                            <MultiBox
                                key="ttask_var_mapping"
                                name="ttask_var_mapping"
                                label={`${i18next.t('availableVars')}:`}
                                options={templateVarsAllOptions}
                                value={
                                    this.convertMapping(
                                        items.ttask_var_mapping,
                                    ) || []
                                }
                                changeableOptions
                                changeableValue
                                meta={{ selectAll: true }}
                            />
                        </WrapComponent>
                    )}

                    {/* Variables of sub-process */}
                    {taskType != 'A' &&
                        taskType != 'N' &&
                        showCaseVariables && (
                        <WrapComponent key="vars" side="center">
                            {taskType == 'P' && (
                                <WrapComponent
                                    key="mapvars"
                                    side="right"
                                    name="wrapComponent"
                                >
                                    <EmptyComponent key="emptyComp" />
                                    <Line key="divider" side="center" />
                                    <Label
                                        key="map"
                                        label={i18next.t(
                                            'mappingSubProcessVars',
                                        )}
                                        fontSize="1rem"
                                        fullLabel
                                        noValueHeight
                                        showComponentLabel
                                    />
                                    <MultiBoxMulti
                                        key="subp_mapping"
                                        ref="subpMapping"
                                        name="subp_mapping"
                                        label={`${i18next.t('targetVar')}:`}
                                        targetOptions={
                                            this.props
                                                .processMappingTargetVarsOptions
                                        }
                                        sourceOptions={
                                            this.props
                                                .templateVarsAllPlusSysOptions
                                        }
                                        value={this.props.subpMappingValues}
                                    />
                                </WrapComponent>
                            )}
                            <EmptyComponent key="emptyComp" />
                            <Line key="divider" side="center" />
                            <Label
                                key="return"
                                label={i18next.t('returnSubProcessVars')}
                                fontSize="1rem"
                                fullLabel
                                noValueHeight
                                showComponentLabel
                            />
                            <MultiBoxMulti
                                key="subr_mapping"
                                ref="subrMapping"
                                name="subr_mapping"
                                label={`${i18next.t('targetVar')}:`}
                                targetOptions={
                                    this.props
                                        .processReturnTargetVarsOptions
                                }
                                sourceOptions={
                                    taskType == 'P'
                                        ? this.props
                                            .processReturnSourceVarsOptions
                                        : this.props
                                            .eventSourceVariablesOptions
                                }
                                value={this.props.subpReturnValues}
                            />
                            <EmptyComponent key="emptyCompAfter" />
                        </WrapComponent>
                    )}

                    {/* Invitation */}
                    {taskType == 'I' && taskType != 'A' && taskType != 'N' && (
                        <WrapComponent key="invitation" side="right">
                            <Label
                                key="inv"
                                label={i18next.t('invitation')}
                                fontSize="1rem"
                                fullLabel
                                noValueHeight
                            />
                            <MultiBox
                                key="ttask_inv_attendees"
                                name="ttask_inv_attendees"
                                label={`${i18next.t('participants')}:`}
                                options={this.props.userOptions}
                                changeableValue
                                changeableOptions
                                value={getAttendees(items.ttask_inv_attendees)}
                                selectionModalValueTypeString
                            />
                            <Text
                                key="ttask_inv_summary"
                                name="ttask_inv_summary"
                                label={`${i18next.t('name')}:`}
                                value={items.ttask_inv_summary}
                            />
                            <TextArea
                                key="ttask_inv_description"
                                name="ttask_inv_description"
                                label={`${i18next.t('description')}:`}
                                rows={3}
                                value={items.ttask_inv_description}
                            />
                            <CalendarWithClockpicker
                                key="ttask_inv_dtstart"
                                name="ttask_inv_dtstart"
                                label={`${i18next.t('start')}:`}
                                value={items.ttask_inv_dtstart}
                            />
                            <CalendarWithClockpicker
                                key="ttask_inv_dtend"
                                name="ttask_inv_dtend"
                                label={`${i18next.t('end')}:`}
                                value={items.ttask_inv_dtend}
                            />
                            <Text
                                key="ttask_inv_location"
                                name="ttask_inv_location"
                                label={`${i18next.t('location')}:`}
                                value={items.ttask_inv_location}
                            />
                            <SelectBox
                                key="ttask_inv_class"
                                name="ttask_inv_class"
                                label={`${i18next.t('class')}:`}
                                options={invitationOptionsClass}
                                nullable={false}
                                value={getValue(
                                    items.ttask_inv_class,
                                    invitationOptionsClass,
                                )}
                            />
                            <SelectBox
                                key="ttask_inv_priority"
                                name="ttask_inv_priority"
                                label={`${i18next.t('priority')}:`}
                                options={invitationOptionsPriority}
                                nullable={false}
                                value={getValue(
                                    items.ttask_inv_priority,
                                    invitationOptionsPriority,
                                )}
                            />

                            <Text
                                key="ttask_inv_categories"
                                name="ttask_inv_categories"
                                label={`${i18next.t('category')}:`}
                                value={items.ttask_inv_categories}
                            />
                        </WrapComponent>
                    )}
                </HiddenTabContent>

                <HiddenTabContent
                    key="HiddenTabContentNotification"
                    tabName="notification"
                >
                    {/* recipient */}
                    {taskType == 'N' && (
                        <SelectBox
                            key="ttask_enot_tgt_type"
                            label={`${i18next.t('recipient')}:`}
                            side="left"
                            options={notificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'solver',
                                P: 'email',
                                S: 'org',
                                U: 'var',
                                R: 'role',
                            }}
                            value={getValue(
                                items.ttask_enot_tgt_type,
                                notificationOptions,
                            )}
                        />
                    )}
                    {taskType == 'N' && (
                        <SelectBox
                            key="ttask_enot_tgt_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['solver']}
                            nullable={false}
                            options={this.props.templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_tgt_ttask_id,
                                this.props.templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType == 'N' && (
                        <Text
                            key="ttask_enot_tgt"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_tgt}
                            visibilityGroups={['email']}
                            helperMenu
                            helperMenuOptions={
                                this.props.templateVarsTextOptions
                            }
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_external_language"
                            label={`${i18next.t('externalLang')}:`}
                            options={languageOptions}
                            visibilityGroups={['email']}
                            side="left"
                            value={getValue(
                                items.ttask_enot_external_language,
                                languageOptions,
                            )}
                            nullable={false}
                        />
                    )}
                    {taskType == 'N' && (
                        <SelectBox
                            key="ttask_enot_tgt_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['org']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_tgt_orgstr_id,
                            )}
                        />
                    )}
                    {taskType == 'N' && (
                        <SelectBox
                            key="ttask_enot_tgt_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['var']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_tgt_tvar_id,
                                this.props.templateVarsUserOptions,
                            )}
                            options={this.props.templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType == 'N' && (
                        <Text
                            key="ttask_enot_tgt_tvar_name"
                            type="hidden"
                            visibilityGroups={['var']}
                            side="left"
                            value={items.ttask_enot_tgt}
                        />
                    )}
                    {taskType == 'N' && (
                        <WrapComponent
                            key="notifRole"
                            side="left"
                            visibilityGroups={['role']}
                        >
                            <SelectBox
                                key="ttask_enot_tgt_role_id"
                                name="ttask_enot_tgt_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_tgt_role}
                                value={items.ttask_enot_tgt_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_tgt_role"
                                name="ttask_enot_tgt_role"
                                label=""
                                value={items.ttask_enot_tgt_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>
                    )}
                    {/* reply recipient */}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_reply_type"
                            label={`${i18next.t('replyRecipient')}:`}
                            side="left"
                            options={replyNotificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'replySolver',
                                P: 'replyEmail',
                                S: 'replyOrg',
                                U: 'replyVar',
                                R: 'replyRole',
                            }}
                            value={getValue(
                                items.ttask_enot_reply_type,
                                replyNotificationOptions,
                            )}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_reply_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['replySolver']}
                            nullable={false}
                            options={this.props.templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_reply_ttask_id,
                                this.props.templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <Text
                            key="ttask_enot_reply_target"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_reply_target}
                            visibilityGroups={['replyEmail']}
                            helperMenu
                            helperMenuOptions={
                                this.props.templateVarsTextOptions
                            }
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_reply_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['replyOrg']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_reply_orgstr_id,
                            )}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_reply_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['replyVar']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_reply_tvar_id,
                                this.props.templateVarsUserOptions,
                            )}
                            options={this.props.templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <Text
                            key="ttask_enot_reply_tvar_name"
                            type="hidden"
                            visibilityGroups={['replyVar']}
                            side="left"
                            value={items.ttask_enot_reply_target}
                        />
                    )}
                    {taskType === 'N' && (
                        <WrapComponent
                            key="notifRoleRelply"
                            side="left"
                            visibilityGroups={['replyRole']}
                        >
                            <SelectBox
                                key="ttask_enot_reply_role_id"
                                name="ttask_enot_reply_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_reply_role}
                                value={items.ttask_enot_reply_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_reply_role"
                                name="ttask_enot_reply_role"
                                label=""
                                value={items.ttask_enot_reply_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>
                    )}
                    {/* copy */}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_copy_type"
                            label={`${i18next.t('copyRecipient')}:`}
                            side="left"
                            options={blindCopyNotificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'copySolver',
                                P: 'copyEmail',
                                S: 'copyOrg',
                                U: 'copyVar',
                                R: 'copyRole',
                            }}
                            value={getValue(
                                items.ttask_enot_copy_type,
                                blindCopyNotificationOptions,
                            )}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_copy_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['copySolver']}
                            nullable={false}
                            options={this.props.templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_copy_ttask_id,
                                this.props.templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <Text
                            key="ttask_enot_copy_target"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_copy_target}
                            visibilityGroups={['copyEmail']}
                            helperMenu
                            helperMenuOptions={
                                this.props.templateVarsTextOptions
                            }
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_copy_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['copyOrg']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_copy_orgstr_id,
                            )}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_copy_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['copyVar']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_copy_tvar_id,
                                this.props.templateVarsUserOptions,
                            )}
                            options={this.props.templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <Text
                            key="ttask_enot_copy_tvar_name"
                            type="hidden"
                            visibilityGroups={['copyVar']}
                            side="left"
                            value={items.ttask_enot_copy_target}
                        />
                    )}
                    {taskType === 'N' && (
                        <WrapComponent
                            key="notifRoleCopy"
                            side="left"
                            visibilityGroups={['copyRole']}
                        >
                            <SelectBox
                                key="ttask_enot_copy_role_id"
                                name="ttask_enot_copy_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_copy_role}
                                value={items.ttask_enot_copy_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_copy_role"
                                name="ttask_enot_copy_role"
                                label=""
                                value={items.ttask_enot_copy_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>
                    )}

                    {/* blind copy */}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_blind_type"
                            label={`${i18next.t('bcRecipient')}:`}
                            side="left"
                            options={blindCopyNotificationOptions}
                            nullable={false}
                            toShowGroups={{
                                T: 'blindSolver',
                                P: 'blindEmail',
                                S: 'blindOrg',
                                U: 'blindVar',
                                R: 'blindRole',
                            }}
                            value={getValue(
                                items.ttask_enot_blind_type,
                                blindCopyNotificationOptions,
                            )}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_blind_ttask_id"
                            label={`${i18next.t('taskOwner')}:`}
                            side="left"
                            visibilityGroups={['blindSolver']}
                            nullable={false}
                            options={this.props.templateTasksOptions}
                            value={getValue(
                                items.ttask_enot_blind_ttask_id,
                                this.props.templateTasksOptions,
                            )}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <Text
                            key="ttask_enot_blind_target"
                            label={`${i18next.t('email')}:`}
                            side="left"
                            value={items.ttask_enot_blind_target}
                            visibilityGroups={['blindEmail']}
                            helperMenu
                            helperMenuOptions={
                                this.props.templateVarsTextOptions
                            }
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_blind_orgstr_id"
                            label={`${i18next.t('plnOrgUnit')}:`}
                            selectBoxType="DLO"
                            nullable={false}
                            visibilityGroups={['blindOrg']}
                            side="left"
                            value={getOrgUnitValue(
                                items.ttask_enot_blind_orgstr_id,
                            )}
                        />
                    )}
                    {taskType === 'N' && (
                        <SelectBox
                            key="ttask_enot_blind_tvar_id"
                            label={`${i18next.t('OfVariable')}:`}
                            visibilityGroups={['blindVar']}
                            side="left"
                            nullable={false}
                            value={getEnotOfVariableValue(
                                items.ttask_enot_blind_tvar_id,
                                this.props.templateVarsUserOptions,
                            )}
                            options={this.props.templateVarsUserOptions}
                            required
                            validationErrors={{
                                isDefaultRequiredValue: i18next.t('isRequired'),
                            }}
                        />
                    )}
                    {taskType === 'N' && (
                        <Text
                            key="ttask_enot_blind_tvar_name"
                            type="hidden"
                            visibilityGroups={['blindVar']}
                            side="left"
                            value={items.ttask_enot_blind_target}
                        />
                    )}
                    {taskType === 'N' && (
                        <WrapComponent
                            key="notifRoleBlind"
                            side="left"
                            visibilityGroups={['blindRole']}
                        >
                            <SelectBox
                                key="ttask_enot_blind_role_id"
                                name="ttask_enot_blind_role_id"
                                label={`${i18next.t('roles')}:`}
                                selectBoxType="DLR"
                                nullable={false}
                                defaultValue={items.ttask_enot_blind_role}
                                value={items.ttask_enot_blind_role_id}
                                required
                                validationErrors={{
                                    isDefaultRequiredValue:
                                        i18next.t('isRequired'),
                                }}
                            />
                            <Text
                                key="ttask_enot_blind_role"
                                name="ttask_enot_blind_role"
                                label=""
                                value={items.ttask_enot_blind_role || ''}
                                type="hidden"
                            />
                        </WrapComponent>
                    )}

                    {taskType == 'N' && (
                        <Text
                            key="ttask_enot_subject"
                            label={`${i18next.t('subject')}:`}
                            side="left"
                            value={items.ttask_enot_subject}
                        />
                    )}
                    {taskType === 'N' && (
                        <ShowHideComponent key="langMutEnotSubject" side="left">
                            {languages.map((lang, i) => {
                                return (
                                    <Text
                                        key={`ttaskEnotSubject${lang}`}
                                        name={`ttask_enot_subject_${lang}`}
                                        label={`${i18next.t('subject')}:`}
                                        value={
                                            items[`ttask_enot_subject_${lang}`]
                                        }
                                        lblLang={lang}
                                    />
                                );
                            })}
                        </ShowHideComponent>
                    )}
                    {taskType == 'N' && (
                        <CKEditor
                            key="ttask_enot_body2"
                            label={`${i18next.t('body')}:`}
                            side="center"
                            dataId={this.props.taskId}
                            value={
                                items.ttask_enot_body2 ||
                                items.ttask_enot_body ||
                                ''
                            }
                            templateId={this.props.templateId}
                        />
                    )}
                    {taskType === 'N' && (
                        <ShowHideComponent
                            key="langMutEnotSubject"
                            side="center"
                        >
                            {languages.map((lang, i) => {
                                return (
                                    <CKEditor
                                        key={`ttaskEnotBody2${lang}`}
                                        name={`ttask_enot_body2_${lang}`}
                                        label={`${i18next.t('body')}:`}
                                        dataId={this.props.taskId}
                                        value={
                                            items[`ttask_enot_body2_${lang}`] ||
                                            items[`ttask_enot_body_${lang}`] ||
                                            ''
                                        }
                                        templateId={this.props.templateId}
                                        lblLang={lang}
                                    />
                                );
                            })}
                        </ShowHideComponent>
                    )}
                </HiddenTabContent>

                <HiddenTabContent
                    key="HiddenTabContentCalculations"
                    tabName="calculations"
                >
                    <Calculations
                        key="calculations"
                        side="center"
                        getActualVals={this.getNewCalcActualVals}
                        options={this.props.templateVarsAllOptions}
                        calculations={convertCalculations()}
                        multiinstance={this.multiinstance}
                        onChange={this.componentWithRowsChanged}
                        someCalculationChanged={this.someCalculationChanged}
                        isInCurrentTab={this.props.tabName === 'calculations'}
                    />
                </HiddenTabContent>

                <HiddenTabContent
                    key="HiddenTabContentInstructions"
                    tabName="instructions"
                >
                    {taskType == 'S' && (
                        <CKEditor
                            key="ttask_instruction"
                            side="center"
                            showComponentLabel
                            label={`${i18next.t('defaultLblIt')}:`}
                            dataId={this.props.taskId}
                            value={items.ttask_instruction || ''}
                            templateId={this.props.templateId}
                        />
                    )}
                    {taskType == 'S' && (
                        <ShowHideComponent key="langInstr" side="center">
                            {languages.map((lang, i) => {
                                return (
                                    <CKEditor
                                        key={`ttaskInstruction${lang}`}
                                        name={`ttask_instruction_${lang}`}
                                        label=" "
                                        dataId={this.props.taskId}
                                        showComponentLabel
                                        value={
                                            items[
                                                `ttask_instruction_${lang}`
                                            ] || ''
                                        }
                                        templateId={this.props.templateId}
                                        lblLang={lang}
                                    />
                                );
                            })}
                        </ShowHideComponent>
                    )}
                </HiddenTabContent>

                <HiddenTabContent
                    key="HiddenTabContentConditions"
                    tabName="conditions"
                >
                    <SelectBox
                        ref="insertVar"
                        key="insertVar"
                        side="left"
                        dataStructure={{ value: 'text', title: 'displayText' }}
                        className="suggest-variables"
                        options={condsVarOptions}
                        onChange={this.insertVarIntoConditions}
                        label={i18next.t('insertVar')}
                    />
                    <CodeArea
                        key="ttask_js"
                        ref="conditionsScript"
                        name="ttask_js"
                        label={`${i18next.t('script')}:`}
                        side="center"
                        height="600px"
                        value={items.ttask_js || ''}
                        upperLabel
                        helperText={`${i18next.t('ideHelp')} ${i18next.t('codeMirrorHelpJs')}`}
                        hints="conditionsScript"
                        vars={condsVarOptions}
                        isInCurrentTab={this.props.tabName === 'conditions'}
                        htmlLabelButton={
                            <span
                                className="change-button menu-box-button icon snippet"
                                ref={(c) => (this.menuBoxButtonConditions = c)}
                                onClick={() =>
                                    this.openMenuBox('conditionsScript')}
                                title={i18next.t('insertSnippet')}
                            >
                                <i className="icon icon2-script" />
                                <div
                                    ref={(c) => (this.menuBoxConditions = c)}
                                    className={cx('menu-box', {
                                        open: this.state
                                            .menuBoxConditionsIsOpen,
                                    })}
                                >
                                    <ul>
                                        {ideHints.conditionsSnippets.map(
                                            (option, i) => (
                                                <li key={i}>
                                                    <a
                                                        ref={(c) =>
                                                            (this.option = c)}
                                                        data-value={option.text}
                                                        onClick={(e) =>
                                                            this.insertSnippetIntoCodeArea(
                                                                e,
                                                                'conditionsScript',
                                                            )}
                                                    >
                                                        {option.displayText}
                                                    </a>
                                                </li>
                                            ),
                                        )}
                                    </ul>
                                </div>
                            </span>
                        }
                    />
                </HiddenTabContent>
            </Form>
        );
    }

}

TemplateTaskForm.propTypes = {
    items: PropTypes.object.isRequired,
    taskType: PropTypes.string,
    handleValidSubmit: PropTypes.func.isRequired,
    handleValidSubmitWithoutRequire: PropTypes.func.isRequired,
    handleInvalidSubmit: PropTypes.func.isRequired,
    formChanged: PropTypes.func.isRequired,
    templateProcessesOptions: PropTypes.array,
    templateTasksOptions: PropTypes.array,
    templateVarsUserOptions: PropTypes.array,
    templateVarsTextOptions: PropTypes.array,
    templateVarsAllOptions: PropTypes.array,
    processMappingTargetVarsOptions: PropTypes.array,
    templateVarsAllPlusSysOptions: PropTypes.array,
    subpMappingValues: PropTypes.array,
    processReturnTargetVarsOptions: PropTypes.array,
    processReturnSourceVarsOptions: PropTypes.array,
    eventSourceVariablesOptions: PropTypes.array,
    subpReturnValues: PropTypes.array,
    userOptions: PropTypes.array,
    taskId: PropTypes.string.isRequired,
    templateId: PropTypes.string.isRequired,
    completionFilterVariablesOptions: PropTypes.arrayOf(PropTypes.object),
    taskTypeChange: PropTypes.func.isRequired,
    processChange: PropTypes.func.isRequired,
    eventChange: PropTypes.func.isRequired,
    checkEvent: PropTypes.func.isRequired,
    suggestList: PropTypes.object,
    customTabTitle: PropTypes.string,
    someCalculationChanged: PropTypes.func,
    tabName: PropTypes.string,
};

TemplateTaskForm.defaultProps = {
    someCalculationChanged: null,
    tabName: null,
};

export default TemplateTaskForm;
