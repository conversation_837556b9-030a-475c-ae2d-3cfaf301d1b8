import _ from 'lodash';
import LoggedUserStore from '../../flux/loggedUser.store';

export default {
    /**
     * (new calculations) Returns array of formatted calculations for save to store.
     * @param {object} formData
     * @returns {array}
     */
    getNewCalculationsArr: function (formData) {
        let index = 0;
        const calculationsArr = [];

        while (typeof formData[`calc_${index}`] !== 'undefined') {
            const calculation = {};
            calculation.id = formData[`calcId_${index}`];
            calculation.calcSendEs6 = formData[`sendCalcES6_${index}`] === true;
            calculation.ttjscalc_js_es6 = formData[`calcES6_${index}`];
            calculation.ttjscalc_js = formData[`calc_${index}`];
            calculation.ttjscalc_exec_start =
                formData[`start_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_end =
                formData[`end_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_hand =
                formData[`hand_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_recalc =
                formData[`recalc_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_pull =
                formData[`pull_${index}`] === true ? 'Y' : 'N';
            calculation.insertVar = formData[`var_${index}`];
            calculation.ttjscalc_append_scripts = JSON.stringify(
                formData[`script_${index}`],
            );

            calculationsArr.push(calculation);

            index += 1;
        }

        return calculationsArr;
    },

    /**
     * Returns array of formatted conditions for save to store.
     * @param {object} formData
     * @param {string} componentName
     * @returns {array}
     */
    getConditionsArr: function (formData, componentName) {
        let index = 0;
        const conditionsArr = [];

        while (
            typeof formData[`${componentName}-array${index}`] !== 'undefined'
        ) {
            const condition = {};
            condition.array = formData[`${componentName}-array${index}`];
            condition.condition =
                formData[`${componentName}-condition${index}`];
            condition.operator = formData[`${componentName}-operator${index}`];
            condition.type = formData[`${componentName}-array${index}`].type;

            conditionsArr.push(condition);

            index++;
        }

        return conditionsArr;
    },

    /**
     * Returns object for send
     * @param {object} data - form data
     * @param templateId
     * @param versionId
     * @param taskId
     * @param {string} taskType
     * @param {array} varsOrder
     * @param {boolean} forDiagram - template graph
     * @returns {object}
     */
    getPostObject: function (
        data,
        templateId,
        versionId,
        taskId,
        taskType,
        varsOrder,
        forDiagram,
    ) {
        const { languages } = LoggedUserStore.getState();
        // Calculations
        let index = 0;
        const calculationsArr = [];

        while (typeof data[`calc_${index}`] !== 'undefined') {
            const calculation = {};

            calculation.id = data[`calcId_${index}`];
            calculation.calcSendEs6 = data[`sendCalcEs6_${index}`] === true;

            if (forDiagram) {
                calculation.ttjscalc_js = data[`calc_${index}`];
                calculation.ttjscalc_js_es6 = data[`calcES6_${index}`];
            } else {
                calculation.ttjscalc_js = calculation.calcSendEs6
                    ? data[`calcES6_${index}`]
                    : data[`calc_${index}`];
            }

            calculation.ttjscalc_exec_start =
                data[`start_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_end =
                data[`end_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_hand =
                data[`hand_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_recalc =
                data[`recalc_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_exec_pull =
                data[`pull_${index}`] === true ? 'Y' : 'N';
            calculation.ttjscalc_append_scripts = JSON.stringify(
                data[`script_${index}`] || [],
            );

            calculationsArr.push(calculation);

            index++;
        }

        // Completion/cancellation
        const filterNameArr = ['completion', 'cancellation'];
        const completionsArr = [];

        filterNameArr.forEach((f) => {
            let i = 0;
            while (typeof data[`${f}Filter-array${i}`] !== 'undefined') {
                const completion = {};
                completion.tvar_id =
                    typeof data[`${f}Filter-array${i}`].value !== 'undefined'
                        ? data[`${f}Filter-array${i}`].value
                        : data[`${f}Filter-array${i}`];
                completion.ttc_value = data[`${f}Filter-condition${i}`];
                completion.ttc_operator =
                    typeof data[`${f}Filter-operator${i}`].value !== 'undefined'
                        ? data[`${f}Filter-operator${i}`].value
                        : data[`${f}Filter-operator${i}`];
                completion.ttc_concat_operator =
                    typeof data[`ttask_${f}`].value !== 'undefined'
                        ? data[`ttask_${f}`].value
                        : data[`ttask_${f}`];
                completion.tvar_type =
                    typeof data[`${f}Filter-array${i}`].type !== 'undefined'
                        ? data[`${f}Filter-array${i}`].type
                        : data[`${f}Filter-array${i}`].type;
                completion.ttc_cancel_flag = f == 'cancellation' ? 'Y' : 'N';

                completionsArr.push(completion);

                i++;
            }
        });

        // subp_mapping
        const getSubpMappValuesArr = function (arr) {
            const subpMappValuesArr = [];

            arr.forEach((item) => {
                subpMappValuesArr.push(item.value);
            });

            return subpMappValuesArr;
        };
        // subr_mapping
        const getSubrMappValuesArr = function (arr) {
            const subrMappValuesArr = [];

            arr.forEach((item) => {
                subrMappValuesArr.push(item.value);
            });

            return subrMappValuesArr;
        };

        /*
        {value: 'S', title: 'k přiřazení řešiteli'},
        {value: 'P', title: 'realizován podprocesem'},
        {value: 'A', title: 'automatický stav'},
        {value: 'N', title: 'E-mailová notifikace'},
        {value: 'E', title: 'spouští událost'},
        {value: 'W', title: 'čeká na událost'},
        {value: 'I', title: 'Pozvánka'}
        */

        const obj = {
            tproc_version: versionId,
        };

        // Task
        if (taskId != 'new') {
            obj.ttask_id = taskId;
        }

        obj.tproc_id = Number(templateId);
        obj.ttask_name = data.ttask_name;
        obj.ttask_description = data.ttask_description;
        obj.ttask_type =
            typeof data.ttask_type.value !== 'undefined'
                ? data.ttask_type.value
                : data.ttask_type;
        obj.ttask_garant =
            typeof data.ttask_garant.value !== 'undefined'
                ? data.ttask_garant.value
                : data.ttask_garant;
        obj.ttask_gen_history = data.ttask_gen_history === true ? 'Y' : 'N';
        obj.ttask_is_delegatable =
            data.ttask_is_delegatable === true ? 'Y' : 'N';
        obj.ttask_is_rejectable = data.ttask_is_rejectable === true ? 'Y' : 'N';
        obj.ttask_can_be_button = data.ttask_can_be_button === true ? 'Y' : 'N';
        obj.ttask_is_bulk_completable = 'N';
        obj.ttask_mass_mapping = [];

        // Subprocess
        if (taskType == 'P') {
            // ttask_subprocess_tproc_id = tproc_id-tproc_version
            const subProcessId = data.ttask_subprocess_tproc_id;
            let valueArr = [null, null];

            if (
                _.has(subProcessId, 'value') &&
                subProcessId.value.indexOf('-') !== -1
            ) {
                valueArr = subProcessId.value.split('-');
            } else if (subProcessId.indexOf('-') !== -1) {
                valueArr = subProcessId.split('-');
            }

            obj.ttask_subprocess_tproc_id = valueArr[0];
            obj.ttask_subprocess_tproc_version = valueArr[1];
            obj.subp_mapping = getSubpMappValuesArr(data.subp_mapping);
            obj.subr_mapping = getSubrMappValuesArr(data.subr_mapping);
        }

        // Waiting for an event
        if (taskType == 'W') {
            obj.subr_mapping = getSubrMappValuesArr(data.subr_mapping);
            obj.ttask_event =
                data.ttask_event !== null &&
                typeof data.ttask_event.value !== 'undefined'
                    ? data.ttask_event.value
                    : data.ttask_event;
        }

        // Planning
        if (taskType != 'W') {
            obj.ttask_petri_net_input =
                typeof data.ttask_petri_net_input.value !== 'undefined'
                    ? data.ttask_petri_net_input.value
                    : data.ttask_petri_net_input;
            obj.ttask_sufficient_end =
                data.ttask_sufficient_end === true ? 'Y' : 'N';
            obj.ttask_run_only_once =
                data.ttask_run_only_once === true ? 'Y' : 'N';
            obj.ttask_multiinstance_flag =
                data.ttask_multiinstance_flag === true ? 'Y' : 'N';
        }
        // Planning multiinstance Iterate over variables
        if (data.ttask_multiinstance_flag === true) {
            obj.ttask_iterate_over =
                data.ttask_iterate_over !== null &&
                typeof data.ttask_iterate_over.value !== 'undefined'
                    ? data.ttask_iterate_over.value
                    : data.ttask_iterate_over;
        } else {
            obj.ttask_iterate_over = null;
        }

        // Instruction
        if (taskType == 'S') {
            obj.ttask_instruction = data.ttask_instruction;
            obj.ttask_is_bulk_completable =
                data.ttask_is_bulk_completable === true ? 'Y' : 'N';
        }

        // Solver
        if (taskType != 'A' && taskType != 'N') {
            obj.ttask_assesment_role_id =
                data.ttask_assesment_role_id !== null &&
                typeof data.ttask_assesment_role_id !== 'undefined' &&
                typeof data.ttask_assesment_role_id.value !== 'undefined'
                    ? data.ttask_assesment_role_id.value
                    : data.ttask_assesment_role_id;
            obj.ttask_assesment_role_name =
                data.ttask_assesment_role_id !== null &&
                typeof data.ttask_assesment_role_id !== 'undefined' &&
                typeof data.ttask_assesment_role_id.title !== 'undefined'
                    ? data.ttask_assesment_role_id.title
                    : data.ttask_assesment_role_name;
            obj.ttask_assesment_orgstr_cnst =
                data.ttask_assesment_orgstr_cnst !== null &&
                typeof data.ttask_assesment_orgstr_cnst !== 'undefined' &&
                typeof data.ttask_assesment_orgstr_cnst.value !== 'undefined'
                    ? data.ttask_assesment_orgstr_cnst.value
                    : data.ttask_assesment_orgstr_cnst;
            obj.ttask_reference_user = data.ttask_reference_user;
            obj.ttask_assesment_hierarchy =
                typeof data.ttask_assesment_hierarchy.value !== 'undefined'
                    ? data.ttask_assesment_hierarchy.value
                    : data.ttask_assesment_hierarchy;
            obj.ttask_assesment_method =
                typeof data.ttask_assesment_method.value !== 'undefined'
                    ? data.ttask_assesment_method.value
                    : data.ttask_assesment_method;
            obj.ttask_again =
                typeof data.ttask_again.value !== 'undefined'
                    ? data.ttask_again.value
                    : data.ttask_again;
            obj.ttask_duty =
                typeof data.ttask_duty.value !== 'undefined'
                    ? data.ttask_duty.value
                    : data.ttask_duty;
        }

        // Completion
        if (taskType == 'S' || taskType == 'W') {
            /* obj.ttask_completion_co = typeof data.ttask_completion_co.value !== 'undefined'
                ? data.ttask_completion_co.value : data.ttask_completion_co; */
            obj.ttask_completions = completionsArr;
        }

        // Assignment of variables
        if (taskType === 'E') {
            const postMapping = [];
            const dataMapping = [];

            data.ttask_var_mapping.forEach((varId) => {
                dataMapping.push({
                    tvar_id: varId,
                    usage: 'R',
                    axis_x: null,
                    axis_y: null,
                });
            });
            // data.ttask_var_mapping.must.forEach(varId => {
            //     dataMapping.push({
            //         tvar_id: varId,
            //         usage: 'M',
            //         axis_x: null,
            //         axis_y: null,
            //     });
            // });
            // data.ttask_var_mapping.write.forEach(varId => {
            //     dataMapping.push({
            //         tvar_id: varId,
            //         usage: 'W',
            //         axis_x: null,
            //         axis_y: null,
            //     });
            // });

            // keep the original order, for old mapping without axis
            varsOrder.forEach((varId) => {
                const findIndex = _.findIndex(dataMapping, ['tvar_id', varId]);

                if (findIndex !== -1) {
                    const move = dataMapping.splice(findIndex, 1);
                    postMapping.push(move[0]);
                }
            });

            obj.ttask_var_mapping = postMapping.concat(dataMapping);
        }

        // Assignment of variables
        if (taskType === 'S' && data.ttask_mass_mapping) {
            const dataMapping = [];

            data.ttask_mass_mapping.must.forEach((varId) => {
                dataMapping.push({
                    tvar_id: varId,
                    usage: 'M',
                });
            });
            data.ttask_mass_mapping.write.forEach((varId) => {
                dataMapping.push({
                    tvar_id: varId,
                    usage: 'W',
                });
            });

            obj.ttask_mass_mapping = dataMapping;
        }

        // Invoke event
        if (taskType == 'E') {
            obj.ttask_invoke_event =
                typeof data.ttask_invoke_event.value !== 'undefined'
                    ? data.ttask_invoke_event.value
                    : data.ttask_invoke_event;
            obj.ttask_event =
                data.ttask_event !== null &&
                typeof data.ttask_event.value !== 'undefined'
                    ? data.ttask_event.value
                    : data.ttask_event;
        }

        // Email notification
        if (taskType === 'N') {
            obj.ttask_enot_tgt_type =
                typeof data.ttask_enot_tgt_type.value !== 'undefined'
                    ? data.ttask_enot_tgt_type.value
                    : data.ttask_enot_tgt_type;
            obj.ttask_enot_reply_type =
                data.ttask_enot_reply_type &&
                typeof data.ttask_enot_reply_type.value !== 'undefined'
                    ? data.ttask_enot_reply_type.value
                    : data.ttask_enot_reply_type;
            obj.ttask_enot_copy_type =
                data.ttask_enot_copy_type &&
                typeof data.ttask_enot_copy_type.value !== 'undefined'
                    ? data.ttask_enot_copy_type.value
                    : data.ttask_enot_copy_type;
            obj.ttask_enot_blind_type =
                data.ttask_enot_blind_type &&
                typeof data.ttask_enot_blind_type.value !== 'undefined'
                    ? data.ttask_enot_blind_type.value
                    : data.ttask_enot_blind_type;
            obj.ttask_enot_subject = data.ttask_enot_subject;
            obj.ttask_enot_body = data.ttask_enot_body2;

            languages.forEach((lang) => {
                obj[`ttask_enot_subject_${lang}`] =
                    data[`ttask_enot_subject_${lang}`];
                obj[`ttask_enot_body_${lang}`] =
                    data[`ttask_enot_body2_${lang}`];
            });

            obj.ttask_enot_external_language = null;
            if (data.ttask_enot_external_language) {
                obj.ttask_enot_external_language =
                    typeof data.ttask_enot_external_language.value !==
                    'undefined'
                        ? data.ttask_enot_external_language.value
                        : data.ttask_enot_external_language;
            }

            const setTaskEnotParams = (paramType) => {
                const dataType = data[`ttask_enot_${paramType}_type`];
                const target =
                    paramType === 'tgt'
                        ? 'ttask_enot_tgt'
                        : `ttask_enot_${paramType}_target`;
                const ttaskId = `ttask_enot_${paramType}_ttask_id`;
                const orgstrId = `ttask_enot_${paramType}_orgstr_id`;
                const tvarId = `ttask_enot_${paramType}_tvar_id`;
                const roleId = `ttask_enot_${paramType}_role_id`;

                if (dataType) {
                    if (typeof dataType.value !== 'undefined') {
                        if (dataType.value === 'T') {
                            obj[ttaskId] =
                                typeof data[ttaskId].value !== 'undefined'
                                    ? data[ttaskId].value
                                    : data[ttaskId];
                        } else if (dataType.value === 'P') {
                            obj[target] = data[target];
                        } else if (dataType.value === 'S') {
                            obj[orgstrId] =
                                typeof data[orgstrId].value !== 'undefined'
                                    ? data[orgstrId].value
                                    : data[orgstrId];
                        } else if (dataType.value === 'U') {
                            obj[tvarId] =
                                typeof data[tvarId].value !== 'undefined'
                                    ? data[tvarId].value
                                    : data[tvarId];
                            obj[target] =
                                typeof data[tvarId].title !== 'undefined'
                                    ? data[tvarId].title
                                    : data[`ttask_enot_${paramType}_tvar_name`];
                        } else if (dataType.value === 'R') {
                            obj[roleId] =
                                typeof data[roleId].value !== 'undefined'
                                    ? data[roleId].value
                                    : data[roleId];
                            obj[`ttask_enot_${paramType}_role`] =
                                typeof data[roleId].title !== 'undefined'
                                    ? data[roleId].title
                                    : data[`ttask_enot_${paramType}_role`];
                        }
                    } else if (dataType === 'T') {
                        obj[ttaskId] =
                            typeof data[ttaskId].value !== 'undefined'
                                ? data[ttaskId].value
                                : data[ttaskId];
                    } else if (dataType === 'P') {
                        obj[target] = data[target];
                    } else if (dataType === 'S') {
                        obj[orgstrId] =
                            typeof data[orgstrId].value !== 'undefined'
                                ? data[orgstrId].value
                                : data[orgstrId];
                    } else if (dataType === 'U') {
                        obj[tvarId] =
                            typeof data[tvarId].value !== 'undefined'
                                ? data[tvarId].value
                                : data[tvarId];
                        obj[target] =
                            typeof data[tvarId].title !== 'undefined'
                                ? data[tvarId].title
                                : data[`ttask_enot_${paramType}_tvar_name`];
                    } else if (dataType === 'R') {
                        obj[roleId] =
                            typeof data[roleId].value !== 'undefined'
                                ? data[roleId].value
                                : data[roleId];
                        obj[`ttask_enot_${paramType}_role`] =
                            typeof data[roleId].title !== 'undefined'
                                ? data[roleId].title
                                : data[`ttask_enot_${paramType}_role`];
                    }
                }
            };

            setTaskEnotParams('tgt');
            setTaskEnotParams('reply');
            setTaskEnotParams('copy');
            setTaskEnotParams('blind');
        }

        // Invitation
        if (taskType == 'I') {
            obj.ttask_inv_attendees = data.ttask_inv_attendees.join(';');
            obj.ttask_inv_summary = data.ttask_inv_summary;
            obj.ttask_inv_description = data.ttask_inv_description;
            obj.ttask_inv_dtstart = data.ttask_inv_dtstart;
            obj.ttask_inv_dtend = data.ttask_inv_dtend;
            obj.ttask_inv_location = data.ttask_inv_location;
            obj.ttask_inv_class =
                typeof data.ttask_inv_class.value !== 'undefined'
                    ? data.ttask_inv_class.value
                    : data.ttask_inv_class;
            obj.ttask_inv_priority =
                typeof data.ttask_inv_priority.value !== 'undefined'
                    ? data.ttask_inv_priority.value
                    : data.ttask_inv_priority;
            obj.ttask_inv_categories = data.ttask_inv_categories;
        }

        // ttask_garant
        if (typeof data.ttask_garant !== 'undefined') {
            if (typeof data.ttask_garant.value !== 'undefined') {
                if (data.ttask_garant.value == 'SU') {
                    obj.ttask_assesment_user_id =
                        typeof data.ttask_assesment_user_id.value !==
                        'undefined'
                            ? data.ttask_assesment_user_id.value
                            : data.ttask_assesment_user_id;
                    obj.ttask_assesment_user_name =
                        typeof data.ttask_assesment_user_id.title !==
                        'undefined'
                            ? data.ttask_assesment_user_id.title
                            : data.ttask_assesment_user;
                    obj.ttask_assesment_orgstr_id = null;
                } else if (data.ttask_garant.value == 'OS') {
                    obj.ttask_assesment_orgstr_id =
                        typeof data.ttask_assesment_orgstr_id.value !==
                        'undefined'
                            ? data.ttask_assesment_orgstr_id.value
                            : data.ttask_assesment_orgstr_id;
                    obj.ttask_assesment_user_id = null;
                } else {
                    obj.ttask_assesment_user_id = null;
                    obj.ttask_assesment_orgstr_id = null;
                }
            } else if (data.ttask_garant == 'SU') {
                obj.ttask_assesment_user_id =
                    typeof data.ttask_assesment_user_id.value !== 'undefined'
                        ? data.ttask_assesment_user_id.value
                        : data.ttask_assesment_user_id;
                obj.ttask_assesment_user_name =
                    typeof data.ttask_assesment_user_id.title !== 'undefined'
                        ? data.ttask_assesment_user_id.title
                        : data.ttask_assesment_user;
                obj.ttask_assesment_orgstr_id = null;
            } else if (data.ttask_garant == 'OS') {
                obj.ttask_assesment_orgstr_id =
                    typeof data.ttask_assesment_orgstr_id.value !== 'undefined'
                        ? data.ttask_assesment_orgstr_id.value
                        : data.ttask_assesment_orgstr_id;
                obj.ttask_assesment_user_id = null;
            } else {
                obj.ttask_assesment_user_id = null;
                obj.ttask_assesment_orgstr_id = null;
            }
        }

        // ttask_petri_net_input
        if (typeof data.ttask_petri_net_input !== 'undefined') {
            if (
                typeof data.ttask_petri_net_input.value !== 'undefined' &&
                data.ttask_petri_net_input.value == 'N'
            ) {
                obj.ttask_petri_net_inputn = data.ttask_petri_net_inputn;
            } else if (data.ttask_petri_net_input == 'N') {
                obj.ttask_petri_net_inputn = data.ttask_petri_net_inputn;
            }
        }

        // ttask_due_offset
        if (typeof data.ttask_due_offset !== 'undefined') {
            if (typeof data.ttask_due_offset.value !== 'undefined') {
                if (data.ttask_due_offset.value == 'ps') {
                    obj.ttask_due_offset = {
                        type: data.ttask_due_offset.value,
                        value: data.ttask_due_offset_days_ps,
                    };
                } else if (data.ttask_due_offset.value == 'ts') {
                    obj.ttask_due_offset = {
                        type: data.ttask_due_offset.value,
                        value: data.ttask_due_offset_days_ts,
                    };
                } else if (
                    data.ttask_due_offset.value == 'vo' ||
                    data.ttask_due_offset.value == 'vc'
                ) {
                    obj.ttask_due_offset = {
                        type: data.ttask_due_offset.value,
                        value:
                            typeof data.ttask_due_offset_var.value !==
                            'undefined'
                                ? data.ttask_due_offset_var.value
                                : data.ttask_due_offset_var,
                    };
                } else {
                    obj.ttask_due_offset = {
                        type: data.ttask_due_offset.value,
                        value: '',
                    };
                }
            } else if (data.ttask_due_offset == 'ps') {
                obj.ttask_due_offset = {
                    type: data.ttask_due_offset,
                    value: data.ttask_due_offset_days_ps,
                };
            } else if (data.ttask_due_offset == 'ts') {
                obj.ttask_due_offset = {
                    type: data.ttask_due_offset,
                    value: data.ttask_due_offset_days_ts,
                };
            } else if (
                data.ttask_due_offset == 'vo' ||
                data.ttask_due_offset == 'vc'
            ) {
                obj.ttask_due_offset = {
                    type: data.ttask_due_offset,
                    value:
                        typeof data.ttask_due_offset_var.value !== 'undefined'
                            ? data.ttask_due_offset_var.value
                            : data.ttask_due_offset_var,
                };
            } else {
                obj.ttask_due_offset = {
                    type: data.ttask_due_offset,
                    value: '',
                };
            }
        }

        // ttask_duration
        if (typeof data.ttask_duration !== 'undefined') {
            if (typeof data.ttask_duration.value !== 'undefined') {
                if (data.ttask_duration.value == 'ps') {
                    obj.ttask_duration = {
                        type: data.ttask_duration.value,
                        value: data.ttask_duration_days_ps,
                    };
                } else if (data.ttask_duration.value == 'ts') {
                    obj.ttask_duration = {
                        type: data.ttask_duration.value,
                        value: data.ttask_duration_days_ts,
                    };
                } else if (
                    data.ttask_duration.value == 'vo' ||
                    data.ttask_duration.value == 'vc'
                ) {
                    obj.ttask_duration = {
                        type: data.ttask_duration.value,
                        value:
                            typeof data.ttask_duration_var.value !== 'undefined'
                                ? data.ttask_duration_var.value
                                : data.ttask_duration_var,
                    };
                } else {
                    obj.ttask_duration = {
                        type: data.ttask_duration.value,
                        value: '',
                    };
                }
            } else if (data.ttask_duration == 'ps') {
                obj.ttask_duration = {
                    type: data.ttask_duration,
                    value: data.ttask_duration_days_ps,
                };
            } else if (data.ttask_duration == 'ts') {
                obj.ttask_duration = {
                    type: data.ttask_duration,
                    value: data.ttask_duration_days_ts,
                };
            } else if (
                data.ttask_duration == 'vo' ||
                data.ttask_duration == 'vc'
            ) {
                obj.ttask_duration = {
                    type: data.ttask_duration,
                    value:
                        typeof data.ttask_duration_var.value !== 'undefined'
                            ? data.ttask_duration_var.value
                            : data.ttask_duration_var,
                };
            } else {
                obj.ttask_duration = { type: data.ttask_duration, value: '' };
            }
        }

        // Operations/Calculations
        obj.ttask_operations = calculationsArr;

        // ttask_assesment_method
        if (typeof data.ttask_assesment_method !== 'undefined') {
            if (typeof data.ttask_assesment_method.value !== 'undefined') {
                if (
                    data.ttask_assesment_method.value == 'C' ||
                    data.ttask_assesment_method.value == 'L'
                ) {
                    obj.ttask_assesment_ttask_id =
                        typeof data.ttask_assesment_ttask_id.value !==
                        'undefined'
                            ? data.ttask_assesment_ttask_id.value
                            : data.ttask_assesment_ttask_id;
                } else if (data.ttask_assesment_method.value == 'V') {
                    obj.ttask_assesment_tvar_id =
                        typeof data.ttask_assesment_tvar_id.value !==
                        'undefined'
                            ? data.ttask_assesment_tvar_id.value
                            : data.ttask_assesment_tvar_id;
                }
            } else if (
                data.ttask_assesment_method == 'C' ||
                data.ttask_assesment_method == 'L'
            ) {
                obj.ttask_assesment_ttask_id =
                    typeof data.ttask_assesment_ttask_id.value !== 'undefined'
                        ? data.ttask_assesment_ttask_id.value
                        : data.ttask_assesment_ttask_id;
            } else if (data.ttask_assesment_method == 'V') {
                obj.ttask_assesment_tvar_id =
                    typeof data.ttask_assesment_tvar_id.value !== 'undefined'
                        ? data.ttask_assesment_tvar_id.value
                        : data.ttask_assesment_tvar_id;
            }
        }

        if (typeof data.ttask_js !== 'undefined') {
            obj.ttask_js = data.ttask_js;
        }

        languages.forEach((lang) => {
            obj[`ttask_name_${lang}`] = data[`ttask_name_${lang}`];
            obj[`ttask_description_${lang}`] =
                data[`ttask_description_${lang}`];
            obj[`ttask_instruction_${lang}`] =
                data[`ttask_instruction_${lang}`];
        });

        return obj;
    },
};
