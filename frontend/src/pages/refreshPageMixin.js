export default {
    UNSAFE_componentWillUpdate: function (nextProps) {
        // call refreshPage function if refreshPage state is set in browserHistory
        if (
            this.props.location !== nextProps.location &&
            nextProps.location.state !== null &&
            typeof nextProps.location.state !== 'undefined' &&
            nextProps.location.state.refreshPage === true
        ) {
            if (typeof this.refreshPage === 'function') {
                this.refreshPage(
                    nextProps.location.state.eventIdBeingSolved,
                    nextProps.location.state.taskBeingSolved,
                );
            }
        }
    },
};
