import PropTypes from 'prop-types';
import DocumentTitle from 'react-document-title';
import i18next from 'i18next';
import { Text } from '../components/form/text.react';
import { SelectBox } from '../components/form/selectBox.react';
import { checkValueInOptionsByTitle, guid } from '../common/utils';
import PageRights from '../common/pageRights';
import ShowHideComponent from '../components/form/showHideComponent.react';
import LoggedUserStore from '../flux/loggedUser.store';
import ApiRequest from '../api/apiRequest';
import React from 'react';
import _ from 'lodash';
import Heading from '../components/heading.react';
import HiddenTabs from '../components/tabs/hiddenTabs.react';
import HiddenTab from '../components/tabs/hiddenTab.react';
import HiddenTabContent from '../components/tabs/hiddenTabContent.react';
import MainButtons from '../components/tabs/mainButtons.react';
import TabsWrapper from '../components/tabs/tabsWrapper.react';
import MainButton from '../components/tabs/mainButton.react';
import Form from '../components/form/form.react';
import WrapComponent from '../components/form/wrapComponent.react';
import EmptyComponent from '../components/form/emptyComponent.react';
import AlertsActions from '../components/alerts/alerts.actions';
import BreadcrumbActions from '../flux/breadcrumb.actions';
import TemplateCopyImportStore from '../flux/templateCopyImport.store';
import TemplateCopyImportActions from '../flux/templateCopyImport.actions';
import TemplateCopyImportApi from '../flux/templateCopyImport.api';
import TabsButtonsOther from '../components/tabs/tabsButtonsOther.react';
import TabsButton from '../components/tabs/tabsButton.react';
import alt from '../flux/alt';
import ConfirmModal from './modals/confirmModal.react';

class TemplateCopyImport extends React.Component {

    constructor(props) {
        super(props);
        this.state = _.extend(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            TemplateCopyImportStore.getState(),
        );

        this._onChange = this._onChange.bind(this);
        this.watchFormBeforeLeave = this.watchFormBeforeLeave.bind(this);
        this.handleValidSubmit = this.handleValidSubmit.bind(this);
        this.handleValidSubmitWithoutRequire =
            this.handleValidSubmitWithoutRequire.bind(this);
        this.saveImport = this.saveImport.bind(this);
        this.postData = this.postData.bind(this);
        this.goBack = this.goBack.bind(this);
        this.skipTemplate = this.skipTemplate.bind(this);
        this.formChanged = this.formChanged.bind(this);
        this.changeAction = this.changeAction.bind(this);
        this.changeRoleAction = this.changeRoleAction.bind(this);
        this.confirmSaving = this.confirmSaving.bind(this);
        this.cancelSaving = this.cancelSaving.bind(this);
        this.closeConfirmModal = this.closeConfirmModal.bind(this);
    }

    UNSAFE_componentWillMount() {
        // Admin, PowerUser
        PageRights.checkUserRights([-1, -2]);
    }

    componentDidMount() {
        TemplateCopyImportStore.listen(this._onChange);

        const { templateId, versionId } = this.props.match.params;
        const { importDataArr } = this.state;

        // location state for previous breadcrumb path and close button
        const locationState = this.props.location.state;

        if (locationState) {
            TemplateCopyImportActions.savePrevPath(locationState);
        }

        // Copy template
        if (typeof templateId !== 'undefined') {
            this.exportRequest = TemplateCopyImportActions.exportTemplate(
                templateId,
                versionId,
            );
        }
        // Import template
        else if (!_.isEmpty(importDataArr)) {
            TemplateCopyImportActions.setTemplateToImport(importDataArr[0]);
        } else {
            AlertsActions.addAlert.defer({
                type: 'warning',
                message: i18next.t('templateImportNoData'),
                show: true,
            });
            this.goBack();
        }

        this.watchFormBeforeLeave(templateId, versionId);
    }

    componentDidUpdate(prevProps, prevState) {
        const { templateId } = this.props.match.params;

        if (typeof templateId !== 'undefined') {
            if (this.state.templateHeading) {
                // setTimeout because of Cannot dispatch in the middle of a dispatch error without it
                setTimeout(() => {
                    BreadcrumbActions.changeBread([
                        {
                            name: i18next.t('templates'),
                            to:
                                this.state.closePrevPath !== null
                                    ? this.state.closePrevPath
                                    : '/templates',
                        },
                        {
                            name: `${i18next.t('copyingTemplate')} - ${this.state.templateHeading}`,
                            to: `/templates/copy/${templateId}`,
                        },
                    ]);
                });
            }
        } else {
            setTimeout(() => {
                BreadcrumbActions.changeBread([
                    {
                        name: i18next.t('templates'),
                        to:
                            this.state.closePrevPath !== null
                                ? this.state.closePrevPath
                                : '/templates',
                    },
                    {
                        name: i18next.t('templateImport'),
                        to: '/templates/import/',
                    },
                ]);
            });
        }

        // if template is skipped, set formIsPristine true
        if (!_.isEqual(this.state.templates, prevState.templates)) {
            this.formChanged(null, false);
        }
    }

    componentWillUnmount() {
        this.unlistenForm();
        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        TemplateCopyImportStore.unlisten(this._onChange);
        alt.recycle(TemplateCopyImportStore);

        // resave previous paths after store is recycled
        TemplateCopyImportActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });

        if (typeof this.exportRequest !== 'undefined') {
            this.exportRequest.cancel();
        }
    }

    _onChange(state) {
        this.setState(state);
    }

    watchFormBeforeLeave(templateId, versionId) {
        this.unlistenForm = this.props.history.listenBefore(
            (location, callback) => {
                const pathArr = location.pathname.split('/');

                if (
                    pathArr.length > (typeof templateId !== 'undefined' ? 5 : 3)
                ) {
                    // !templateId = import
                    pathArr.pop();
                }
                const newPath = pathArr.join('/');

                if (
                    !this.state.formIsPristine &&
                    newPath !==
                        (typeof templateId !== 'undefined'
                            ? `/templates/copy/${templateId}/${versionId}`
                            : '/templates/import')
                ) {
                    this.setState({ confirmModalIsOpen: true });

                    this.onFormAction = function (data) {
                        this.postData(data, callback);
                    };

                    this.onFormActionWithoutRequire = function () {
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18next.t('alrFillRequiredItems'),
                            show: true,
                            allowCountdown: true,
                        });
                    };

                    this.leavePage = callback;
                } else {
                    // allow transition immediately
                    callback();
                }
            },
        );
    }

    confirmSaving() {
        // save and leave
        this.refs.formImport.submit();
        this.setState({ confirmModalIsOpen: false });
    }

    cancelSaving() {
        // cancel saving and leave
        this.setState(
            {
                formIsPristine: true,
                confirmModalIsOpen: false,
            },
            () => {
                this.leavePage();
            },
        );
    }

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    }

    handleValidSubmit(data) {
        this.onFormAction(data);
    }

    handleValidSubmitWithoutRequire(data) {
        this.onFormActionWithoutRequire(data);
    }

    saveImport(e) {
        e.preventDefault();
        this.onFormAction = function (data) {
            this.postData(data);
        };

        this.onFormActionWithoutRequire = function (data) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillRequiredItems'),
                show: true,
                allowCountdown: true,
            });
        };

        this.refs.formImport.submit();
    }

    postData(data, callback) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const postObj = {
            dynamic_tables: [],
            events: [],
            orgstrs: [],
            roles: [],
            template_processes: [],
            users: [],
            version: this.state.version,
            calculations_scripts: [],
        };

        // template_processes
        this.state.templates.forEach((template) => {
            const templateValueData = data[`templValue-${template.tproc_id}`];
            const templateValue = templateValueData.value
                ? templateValueData.value
                : templateValueData;

            if (template.import_rules.action == 'CREATE') {
                const { languages } = LoggedUserStore.getState();
                template.import_rules.data = { tproc_name: templateValue };
                languages.forEach((lang) => {
                    const langValue =
                        data[`templValue-${template.tproc_id}-${lang}`];
                    template.import_rules.data[`tproc_name_${lang}`] =
                        langValue?.value || langValue;
                });
            } else {
                template.import_rules.data = { id: templateValue };
            }

            postObj.template_processes.push(template);
        });

        // events
        this.state.events.forEach((event) => {
            if (!event.evedef_name.match(/(\$SUB[P|R]_)([0-9]*)/)) {
                const eventValueData = data[`eventValue-${event.evedef_id}`];
                const eventValue = eventValueData.value
                    ? eventValueData.value
                    : eventValueData;

                if (event.import_rules.action == 'CREATE') {
                    event.import_rules.data = { evedef_name: eventValue };
                } else {
                    event.import_rules.data = { evedef_id: eventValue };
                }
            }

            postObj.events.push(event);
        });

        if (!_.isEmpty(this.state.subEvents)) {
            // add subEvents to events arr
            postObj.events = [...postObj.events, ...this.state.subEvents];
        }

        // users
        this.state.users.forEach((user) => {
            const userValueData = data[`userValue-${user.user_id}`];
            const userValue = userValueData.value
                ? userValueData.value
                : userValueData;

            if (user.import_rules.action == 'CREATE') {
                user.import_rules.data = { user_name: userValue };
            } else {
                user.import_rules.data = { user_id: userValue };
            }

            postObj.users.push(user);
        });

        // roles
        this.state.roles.forEach((role) => {
            const roleValueData = data[`roleValue-${role.role_id}`];
            const roleValue = roleValueData.value
                ? roleValueData.value
                : roleValueData;

            if (role.import_rules.action == 'CREATE') {
                role.import_rules.data = { role_name: roleValue };
            } else {
                role.import_rules.data = { role_id: roleValue };
            }

            postObj.roles.push(role);
        });

        // org. units
        this.state.orgUnits.forEach((unit) => {
            const unitValueData = data[`orgUnitValue-${unit.orgstr_id}`];
            const unitValue = unitValueData.value
                ? unitValueData.value
                : unitValueData;

            if (unit.import_rules.action == 'CREATE') {
                const unitManagerData =
                    data[`orgUnitManager-${unit.orgstr_id}`];
                const unitManagerValue =
                    unitManagerData && unitManagerData.hasOwnProperty('value')
                        ? unitManagerData.value
                        : unitManagerData;
                const unitParentData = data[`orgUnitParent-${unit.orgstr_id}`];
                const unitParentValue =
                    unitParentData && unitParentData.hasOwnProperty('value')
                        ? unitParentData.value
                        : unitParentData;

                unit.import_rules.data = {
                    manager_user_id:
                        typeof unitManagerValue !== 'undefined'
                            ? unitManagerValue
                            : null,
                    parent_orgstr_id:
                        typeof unitParentValue !== 'undefined'
                            ? unitParentValue
                            : null,
                };

                if (unit.import_rules.action == 'CREATE') {
                    unit.import_rules.data.orgstr_name = unitValue;
                } else {
                    unit.import_rules.data.orgstr_id = unitValue;
                }
            } else {
                unit.import_rules.data = { orgstr_id: unitValue };
            }

            postObj.orgstrs.push(unit);
        });

        // dyn. tables
        this.state.dynTables.forEach((dynTable) => {
            const dynTableValueData = data[`dynTableValue-${dynTable.dll_id}`];
            const dynTableValue = dynTableValueData.value
                ? dynTableValueData.value
                : dynTableValueData;

            if (dynTable.import_rules.action == 'CREATE') {
                dynTable.import_rules.data = { dt_name: dynTableValue };
            } else {
                dynTable.import_rules.data = { dt_id: dynTableValue };
            }

            postObj.dynamic_tables.push(dynTable);
        });

        // calculations scripts
        this.state.calculationScripts.forEach((calc) => {
            const calcValueData = data[`calScriptsValue-${calc.js_id}`];
            const calcValue = calcValueData.value
                ? calcValueData.value
                : calcValueData;

            if (calc.import_rules.action == 'CREATE') {
                calc.import_rules.data = { name: calcValue };
            } else {
                calc.import_rules.data = { js_id: calcValue };
            }

            postObj.calculations_scripts.push(calc);
        });

        TemplateCopyImportApi.importTemplate(postObj) // blocking
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });

                this.setState({ formIsPristine: true }, function () {
                    if (!_.isEmpty(this.state.importDataArr)) {
                        this.props.history.push('/templates/import');

                        TemplateCopyImportActions.setTemplateToImport(
                            this.state.importDataArr[0],
                        );
                    } else if (callback) {
                        callback();
                    } else {
                        this.goBack();
                    }
                });
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('templateImportFailed'),
                    serverError: errorMessage,
                });
            });
    }

    goBack(e) {
        if (e) {
            e.preventDefault();
        }

        this.props.history.push(this.state.closePrevPath);
    }

    skipTemplate(e) {
        e.preventDefault();

        if (!_.isEmpty(this.state.importDataArr)) {
            this.props.history.push('/templates/import');

            TemplateCopyImportActions.setTemplateToImport(
                this.state.importDataArr[0],
            );
        }
    }

    formChanged(currentValues, isChanged) {
        if (isChanged === true && this.state.formIsPristine) {
            this.setState({ formIsPristine: false });
        } else if (isChanged === false && !this.state.formIsPristine) {
            this.setState({ formIsPristine: true });
        }
    }

    changeAction(type, id, name, value) {
        TemplateCopyImportActions.changeAction({
            type: type,
            id: id,
            action: value.value,
        });
    }

    changeRoleAction(index, name, value) {
        TemplateCopyImportActions.changeRoleAction({
            index: index,
            action: value.value,
        });
    }

    render() {
        const { templateId, versionId } = this.props.match.params;
        const { languages } = LoggedUserStore.getState();

        const templActionOptions = [
            { title: i18next.t('addNewTempl'), value: 'CREATE' },
            { title: i18next.t('useCompatibleTempl'), value: 'USE_COMPATIBLE' },
            { title: i18next.t('overwriteExistTempl'), value: 'REWRITE' },
        ];

        const eventActionOptions = [
            { title: i18next.t('addNewEvent'), value: 'CREATE' },
            { title: i18next.t('useCompatibleEvent'), value: 'USE_COMPATIBLE' },
        ];

        const userActionOptions = [
            { title: i18next.t('addNewUser'), value: 'CREATE' },
            { title: i18next.t('useCompatibleUser'), value: 'USE_COMPATIBLE' },
        ];

        const rolesActionOptions = [
            { title: i18next.t('addNewRole'), value: 'CREATE' },
            { title: i18next.t('useCompatibleRole'), value: 'USE_COMPATIBLE' },
        ];

        const orgUnitsActionOptions = [
            { title: i18next.t('addNewUnit'), value: 'CREATE' },
            { title: i18next.t('useCompatibleUnit'), value: 'USE_COMPATIBLE' },
        ];

        const dynTablesActionOptions = [
            { title: i18next.t('addNewDynTable'), value: 'CREATE' },
            {
                title: i18next.t('useCompatibleDynTable'),
                value: 'USE_COMPATIBLE',
            },
        ];

        const calculationScriptsActionOptions = [
            { title: i18next.t('addNewCalcScript'), value: 'CREATE' },
            {
                title: i18next.t('useCompatibleCalcScript'),
                value: 'USE_COMPATIBLE',
            },
        ];

        const {
            events,
            users,
            roles,
            orgUnits,
            dynTables,
            calculationScripts,
        } = this.state;

        let heading = '';
        if (typeof templateId !== 'undefined') {
            if (this.state.templateHeading) {
                heading = `${i18next.t('copyingTemplate')} - ${this.state.templateHeading}`;
            }
        } else {
            heading = i18next.t('templateImport');
        }

        return (
            <DocumentTitle title={i18next.t('copyingTemplate')}>
                <TabsWrapper>
                    <Heading title={heading}>
                        <MainButtons>
                            <MainButton
                                icon="icon-floppy-disk"
                                buttonColor="green"
                                onClick={this.saveImport}
                                tooltipCode="ttSave"
                                enableOn={!this.state.loading}
                            >
                                {i18next.t('save')}
                            </MainButton>
                            <MainButton
                                icon="icon-delete-1"
                                buttonColor="white"
                                onClick={this.goBack}
                                tooltipCode="ttClose"
                                title={i18next.t('close')}
                            />
                        </MainButtons>
                    </Heading>
                    <HiddenTabs
                        params={this.props.match.params}
                        showLoader
                        loaded={!this.state.loading}
                    >
                        <HiddenTab
                            key="template"
                            title={i18next.t('template')}
                            tabLink={
                                templateId
                                    ? `/templates/copy/${templateId}/${versionId}`
                                    : '/templates/import'
                            }
                        >
                            <TabsButtonsOther key="buttonsTemplate">
                                {!this.state.loading &&
                                    !_.isEmpty(this.state.importDataArr) && (
                                    <TabsButton
                                        key="skip"
                                        icon="icon-arrow-54"
                                        isActive
                                        onClick={this.skipTemplate}
                                        tooltipCode="ttSkipTemplate"
                                    >
                                        {i18next.t('skip')}
                                    </TabsButton>
                                )}
                            </TabsButtonsOther>
                        </HiddenTab>
                        {!_.isEmpty(events) && (
                            <HiddenTab
                                key="events"
                                title={i18next.t('events')}
                                tabName="events"
                                tabLink={
                                    templateId
                                        ? `/templates/copy/${templateId}/${versionId}/events`
                                        : '/templates/import/events'
                                }
                            >
                                <TabsButtonsOther key="buttonsEvents">
                                    {!_.isEmpty(this.state.importDataArr) && (
                                        <TabsButton
                                            key="skip"
                                            icon="icon-arrow-54"
                                            isActive
                                            onClick={this.skipTemplate}
                                            tooltipCode="ttSkipTemplate"
                                        >
                                            {i18next.t('skip')}
                                        </TabsButton>
                                    )}
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        {!_.isEmpty(users) && (
                            <HiddenTab
                                key="users"
                                title={i18next.t('users')}
                                tabName="users"
                                tabLink={
                                    templateId
                                        ? `/templates/copy/${templateId}/${versionId}/users`
                                        : '/templates/import/users'
                                }
                            >
                                <TabsButtonsOther key="buttonsUsers">
                                    {!_.isEmpty(this.state.importDataArr) && (
                                        <TabsButton
                                            key="skip"
                                            icon="icon-arrow-54"
                                            isActive
                                            onClick={this.skipTemplate}
                                            tooltipCode="ttSkipTemplate"
                                        >
                                            {i18next.t('skip')}
                                        </TabsButton>
                                    )}
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        {!_.isEmpty(roles) && (
                            <HiddenTab
                                key="roles"
                                title={i18next.t('roles')}
                                tabName="roles"
                                tabLink={
                                    templateId
                                        ? `/templates/copy/${templateId}/${versionId}/roles`
                                        : '/templates/import/roles'
                                }
                            >
                                <TabsButtonsOther key="buttonsRoles">
                                    {!_.isEmpty(this.state.importDataArr) && (
                                        <TabsButton
                                            key="skip"
                                            icon="icon-arrow-54"
                                            isActive
                                            onClick={this.skipTemplate}
                                            tooltipCode="ttSkipTemplate"
                                        >
                                            {i18next.t('skip')}
                                        </TabsButton>
                                    )}
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        {!_.isEmpty(orgUnits) && (
                            <HiddenTab
                                key="orgStructure"
                                title={i18next.t('orgStructure')}
                                tabName="org-stucture"
                                tabLink={
                                    templateId
                                        ? `/templates/copy/${templateId}/${versionId}/org-stucture`
                                        : '/templates/import/org-stucture'
                                }
                            >
                                <TabsButtonsOther key="buttonsStructure">
                                    {!_.isEmpty(this.state.importDataArr) && (
                                        <TabsButton
                                            key="skip"
                                            icon="icon-arrow-54"
                                            isActive
                                            onClick={this.skipTemplate}
                                            tooltipCode="ttSkipTemplate"
                                        >
                                            {i18next.t('skip')}
                                        </TabsButton>
                                    )}
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        {!_.isEmpty(dynTables) && (
                            <HiddenTab
                                key="dynTables"
                                title={i18next.t('dynTables')}
                                tabName="dyn-tables"
                                tabLink={
                                    templateId
                                        ? `/templates/copy/${templateId}/${versionId}/dyn-tables`
                                        : '/templates/import/dyn-tables'
                                }
                            >
                                <TabsButtonsOther key="buttonsDynTables">
                                    {!_.isEmpty(this.state.importDataArr) && (
                                        <TabsButton
                                            key="skip"
                                            icon="icon-arrow-54"
                                            isActive
                                            onClick={this.skipTemplate}
                                            tooltipCode="ttSkipTemplate"
                                        >
                                            {i18next.t('skip')}
                                        </TabsButton>
                                    )}
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        {!_.isEmpty(calculationScripts) && (
                            <HiddenTab
                                key="calculationScripts"
                                title={`${i18next.t('scripts')} (${i18next.t('calculations')})`}
                                tabName="calculations-scripts"
                                tabLink={
                                    templateId
                                        ? `/templates/copy/${templateId}/${versionId}/calculations-scripts`
                                        : '/templates/import/calculations-scripts'
                                }
                            >
                                <TabsButtonsOther key="buttonsCalculationScripts">
                                    {!_.isEmpty(this.state.importDataArr) && (
                                        <TabsButton
                                            key="skip"
                                            icon="icon-arrow-54"
                                            isActive
                                            onClick={this.skipTemplate}
                                            tooltipCode="ttSkipTemplate"
                                        >
                                            {i18next.t('skip')}
                                        </TabsButton>
                                    )}
                                </TabsButtonsOther>
                            </HiddenTab>
                        )}
                        <Form
                            ref="formImport"
                            name="formImport"
                            onValidSubmit={this.handleValidSubmit}
                            onChange={this.formChanged}
                            onValidSubmitIgnoreRequired={
                                this.handleValidSubmitWithoutRequire
                            }
                        >
                            <HiddenTabContent key="HiddenTabTemplate">
                                {this.state.templates.map((t, i) => {
                                    const { action } = t.import_rules;

                                    return (
                                        <WrapComponent key={`wrapTempl-${i}`}>
                                            <SelectBox
                                                key={`templAction-${t.tproc_id}`}
                                                name={`templAction-${t.tproc_id}`}
                                                label={t.tproc_name}
                                                options={templActionOptions}
                                                nullable={false}
                                                value={action}
                                                onChange={this.changeAction.bind(
                                                    null,
                                                    'templ',
                                                    t.tproc_id,
                                                )}
                                            />
                                            {action == 'CREATE' && (
                                                <Text
                                                    key={`newTempl-${t.tproc_id}`}
                                                    name={`templValue-${t.tproc_id}`}
                                                    label={`${i18next.t('addNewTempl')}:`}
                                                    value={t.tproc_name}
                                                    required
                                                    validationErrors={{
                                                        isDefaultRequiredValue:
                                                            i18next.t(
                                                                'isRequired',
                                                            ),
                                                    }}
                                                />
                                            )}
                                            {action == 'CREATE' &&
                                                t.tproc_id && (
                                                <ShowHideComponent
                                                    key={`langLabel-${t.tproc_id}`}
                                                    name={`langLabel-${t.tproc_id}`}
                                                >
                                                    {languages.map(
                                                        (lang) => {
                                                            return (
                                                                <Text
                                                                    key={`newTempl-${t.tproc_id}-${lang}`}
                                                                    name={`templValue-${t.tproc_id}-${lang}`}
                                                                    label={`${i18next.t('addNewTempl')}:`}
                                                                    value={
                                                                        t[
                                                                                `tproc_name_${lang}`
                                                                        ]
                                                                    }
                                                                    lblLang={
                                                                        lang
                                                                    }
                                                                />
                                                            );
                                                        },
                                                    )}
                                                </ShowHideComponent>
                                            )}
                                            {action == 'USE_COMPATIBLE' && (
                                                <SelectBox
                                                    key={`useTempl-${t.tproc_id}`}
                                                    name={`templValue-${t.tproc_id}`}
                                                    label={`${i18next.t('useCompatibleTempl')}:`}
                                                    nullable={false}
                                                    options={
                                                        this.state
                                                            .templatesOptions
                                                    }
                                                    value={checkValueInOptionsByTitle(
                                                        `${t.tproc_name} (v${t.tproc_version || 1})`,
                                                        this.state
                                                            .templatesOptions,
                                                    )}
                                                    required
                                                    validationErrors={{
                                                        isDefaultRequiredValue:
                                                            i18next.t(
                                                                'isRequired',
                                                            ),
                                                    }}
                                                />
                                            )}
                                            {action == 'REWRITE' && (
                                                <SelectBox
                                                    key={`overTempl-${t.tproc_id}`}
                                                    name={`templValue-${t.tproc_id}`}
                                                    label={`${i18next.t('overwriteExistTempl')}:`}
                                                    nullable={false}
                                                    options={
                                                        this.state
                                                            .templatesOptions
                                                    }
                                                    value={checkValueInOptionsByTitle(
                                                        `${t.tproc_name} (v${t.tproc_version || 1})`,
                                                        this.state
                                                            .templatesOptions,
                                                    )}
                                                    required
                                                    validationErrors={{
                                                        isDefaultRequiredValue:
                                                            i18next.t(
                                                                'isRequired',
                                                            ),
                                                    }}
                                                />
                                            )}
                                            <EmptyComponent
                                                key={`empTemp${t.tproc_id}`}
                                            />
                                        </WrapComponent>
                                    );
                                })}
                            </HiddenTabContent>
                            {!_.isEmpty(events) && (
                                <HiddenTabContent
                                    key="HiddenTabEvents"
                                    tabName="events"
                                >
                                    {events.map((e, i) => {
                                        const { action } = e.import_rules;

                                        return (
                                            <WrapComponent
                                                key={`wrapEvents-${i}`}
                                            >
                                                <SelectBox
                                                    key={`eventAction-${e.evedef_id}`}
                                                    name={`eventAction-${e.evedef_id}`}
                                                    label={e.evedef_name}
                                                    options={eventActionOptions}
                                                    nullable={false}
                                                    value={action}
                                                    onChange={this.changeAction.bind(
                                                        null,
                                                        'events',
                                                        e.evedef_id,
                                                    )}
                                                />
                                                {action == 'CREATE' && (
                                                    <Text
                                                        key={`newEvent_${e.evedef_id}`}
                                                        name={`eventValue-${e.evedef_id}`}
                                                        label={`${i18next.t('addNewEvent')}:`}
                                                        value={e.evedef_name}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                {action == 'USE_COMPATIBLE' && (
                                                    <SelectBox
                                                        key={`useEvent-${e.evedef_id}`}
                                                        name={`eventValue-${e.evedef_id}`}
                                                        label={`${i18next.t('useCompatibleEvent')}:`}
                                                        nullable={false}
                                                        options={
                                                            this.state
                                                                .eventsOptions
                                                        }
                                                        value={checkValueInOptionsByTitle(
                                                            e.evedef_name,
                                                            this.state
                                                                .eventsOptions,
                                                        )}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                <EmptyComponent
                                                    key={`empEvents${e.evedef_id}`}
                                                />
                                            </WrapComponent>
                                        );
                                    })}
                                </HiddenTabContent>
                            )}
                            {!_.isEmpty(users) && (
                                <HiddenTabContent
                                    key="HiddenTabUsers"
                                    tabName="users"
                                >
                                    {users.map((u, i) => {
                                        const findUser = _.find(
                                            this.state.foundedUsersArr,
                                            ['title', u.user_name],
                                        );
                                        const { action } = u.import_rules;

                                        return (
                                            <WrapComponent
                                                key={`wrapUsers-${i}`}
                                            >
                                                <SelectBox
                                                    key={`userAction-${u.user_id}`}
                                                    name={`userAction-${u.user_id}`}
                                                    label={`${u.user_name} (${u.user_display_name})`}
                                                    options={userActionOptions}
                                                    nullable={false}
                                                    value={action}
                                                    onChange={this.changeAction.bind(
                                                        null,
                                                        'users',
                                                        u.user_id,
                                                    )}
                                                />
                                                {action == 'CREATE' && (
                                                    <Text
                                                        key={`newUser-${u.user_id}`}
                                                        name={`userValue-${u.user_id}`}
                                                        label={`${i18next.t('addNewUser')}:`}
                                                        value={u.user_name}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                {action == 'USE_COMPATIBLE' && (
                                                    <SelectBox
                                                        key={`useUser-${u.user_id}`}
                                                        name={`userValue-${u.user_id}`}
                                                        label={`${i18next.t('useCompatibleUser')}:`}
                                                        defaultValue={`${u.user_name} (${u.user_display_name})`}
                                                        value={findUser || null}
                                                        nullable={false}
                                                        selectBoxType="DLU"
                                                        dataUrl="/users/non-deleted/"
                                                        dataStructureRenderer={function (
                                                            item,
                                                        ) {
                                                            return {
                                                                value: item.id,
                                                                title: `${item.user_name} (${item.user_display_name})`,
                                                            };
                                                        }}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                <EmptyComponent
                                                    key={`empUsers${u.user_id}`}
                                                />
                                            </WrapComponent>
                                        );
                                    })}
                                </HiddenTabContent>
                            )}
                            {!_.isEmpty(roles) && (
                                <HiddenTabContent
                                    key="HiddenTabRoles"
                                    tabName="roles"
                                >
                                    {roles.map((r, i) => {
                                        const { action } = r.import_rules;
                                        const findRole = _.find(
                                            this.state.foundedRolesArr,
                                            ['title', r.role_name],
                                        );

                                        return (
                                            <WrapComponent
                                                key={`wrapRoles-${i}`}
                                            >
                                                <SelectBox
                                                    key={`rolesAction-${r.role_id}`}
                                                    name={`rolesAction-${r.role_id}`}
                                                    label={r.role_name}
                                                    options={rolesActionOptions}
                                                    nullable={false}
                                                    value={action}
                                                    onChange={this.changeAction.bind(
                                                        null,
                                                        'roles',
                                                        r.role_id,
                                                    )}
                                                />
                                                {action == 'CREATE' && (
                                                    <Text
                                                        key={`addRole-${r.role_id}`}
                                                        name={`roleValue-${r.role_id}`}
                                                        label={`${i18next.t('addNewRole')}:`}
                                                        value={r.role_name}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                {action == 'USE_COMPATIBLE' && (
                                                    <SelectBox
                                                        key={`useRole-${r.role_id}`}
                                                        name={`roleValue-${r.role_id}`}
                                                        label={i18next.t(
                                                            'useCompatibleRole',
                                                        )}
                                                        nullable={false}
                                                        selectBoxType="DLR"
                                                        value={findRole || null}
                                                        defaultValue={
                                                            r.role_name
                                                        }
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                <EmptyComponent
                                                    key={`empRoles${r.role_id}`}
                                                />
                                            </WrapComponent>
                                        );
                                    })}
                                </HiddenTabContent>
                            )}
                            {!_.isEmpty(orgUnits) && (
                                <HiddenTabContent
                                    key="HiddenTabOrgStructure"
                                    tabName="org-stucture"
                                >
                                    {orgUnits.map((o, i) => {
                                        const { action } = o.import_rules;

                                        return (
                                            <WrapComponent
                                                key={`wrapOrgUnits-${i}`}
                                            >
                                                <SelectBox
                                                    key={`orgUnitsAction-${o.orgstr_id}`}
                                                    name={`orgUnitsAction-${o.orgstr_id}`}
                                                    label={o.orgstr_name}
                                                    options={
                                                        orgUnitsActionOptions
                                                    }
                                                    nullable={false}
                                                    value={action}
                                                    onChange={this.changeAction.bind(
                                                        null,
                                                        'orgUnits',
                                                        o.orgstr_id,
                                                    )}
                                                />
                                                {action == 'CREATE' && (
                                                    <Text
                                                        key={`addUnit-${o.orgstr_id}`}
                                                        name={`orgUnitValue-${o.orgstr_id}`}
                                                        label={`${i18next.t('addNewUnit')}:`}
                                                        value={o.orgstr_name}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                {action == 'USE_COMPATIBLE' && (
                                                    <SelectBox
                                                        key={`useUnit-${o.orgstr_id}`}
                                                        name={`orgUnitValue-${o.orgstr_id}`}
                                                        label={i18next.t(
                                                            'useCompatibleUnit',
                                                        )}
                                                        nullable={false}
                                                        options={
                                                            this.state
                                                                .orgUnitsOptions
                                                        }
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                        defaultValue={
                                                            o.orgstr_name
                                                        }
                                                        value={checkValueInOptionsByTitle(
                                                            o.orgstr_name,
                                                            this.state
                                                                .orgUnitsOptions,
                                                        )}
                                                    />
                                                )}

                                                {action == 'CREATE' && (
                                                    <SelectBox
                                                        key="manager"
                                                        name={`orgUnitManager-${o.orgstr_id}`}
                                                        label={`${i18next.t('manager')}:`}
                                                        selectBoxType="DLU"
                                                    />
                                                )}
                                                {action == 'CREATE' && (
                                                    <SelectBox
                                                        key="parentUnit"
                                                        name={`orgUnitParent-${o.orgstr_id}`}
                                                        label={`${i18next.t('parentUnit')}:`}
                                                        selectBoxType="DLO"
                                                        nullable={false}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                <EmptyComponent
                                                    key={`empUnits${o.orgstr_id}`}
                                                />
                                            </WrapComponent>
                                        );
                                    })}
                                </HiddenTabContent>
                            )}
                            {!_.isEmpty(dynTables) && (
                                <HiddenTabContent
                                    key="HiddenTabDynTables"
                                    tabName="dyn-tables"
                                >
                                    {dynTables.map((dt, i) => {
                                        const { action } = dt.import_rules;

                                        return (
                                            <WrapComponent
                                                key={`wrapDynTable-${i}`}
                                            >
                                                <SelectBox
                                                    key={`dynTableAction-${dt.dll_id}`}
                                                    name={`dynTableAction-${dt.dll_id}`}
                                                    label={dt.dll_name}
                                                    options={
                                                        dynTablesActionOptions
                                                    }
                                                    nullable={false}
                                                    value={action}
                                                    onChange={this.changeAction.bind(
                                                        null,
                                                        'dynTables',
                                                        dt.dll_id,
                                                    )}
                                                />
                                                {action == 'CREATE' && (
                                                    <Text
                                                        key={`addDynTable-${dt.dll_id}`}
                                                        name={`dynTableValue-${dt.dll_id}`}
                                                        label={`${i18next.t('addNewDynTable')}:`}
                                                        value={dt.dll_name}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                {action == 'USE_COMPATIBLE' && (
                                                    <SelectBox
                                                        key={`useDynTable-${dt.dll_id}`}
                                                        name={`dynTableValue-${dt.dll_id}`}
                                                        label={i18next.t(
                                                            'useCompatibleDynTable',
                                                        )}
                                                        nullable={false}
                                                        options={
                                                            this.state
                                                                .dynTablesOptions
                                                        }
                                                        value={checkValueInOptionsByTitle(
                                                            dt.dll_name,
                                                            this.state
                                                                .dynTablesOptions,
                                                        )}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                <EmptyComponent
                                                    key={`empDynTables${dt.dll_id}`}
                                                />
                                            </WrapComponent>
                                        );
                                    })}
                                </HiddenTabContent>
                            )}

                            {!_.isEmpty(calculationScripts) && (
                                <HiddenTabContent
                                    key="HiddenTabCalculationScripts"
                                    tabName="calculations-scripts"
                                >
                                    {calculationScripts.map((script, i) => {
                                        const { action } = script.import_rules;

                                        return (
                                            <WrapComponent
                                                key={`wrapCalScripts-${i}`}
                                            >
                                                <SelectBox
                                                    key={`calScriptsAction-${script.js_id}`}
                                                    name={`calScriptsAction-${script.js_id}`}
                                                    label={script.name}
                                                    options={
                                                        calculationScriptsActionOptions
                                                    }
                                                    nullable={false}
                                                    value={action}
                                                    onChange={this.changeAction.bind(
                                                        null,
                                                        'calculationScripts',
                                                        script.js_id,
                                                    )}
                                                />
                                                {action == 'CREATE' && (
                                                    <Text
                                                        key={`addCalScripts-${script.js_id}`}
                                                        name={`calScriptsValue-${script.js_id}`}
                                                        label={`${i18next.t('addNewCalcScript')}:`}
                                                        value={script.name}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                {action == 'USE_COMPATIBLE' && (
                                                    <SelectBox
                                                        key={`useCalScripts-${script.js_id}`}
                                                        name={`calScriptsValue-${script.js_id}`}
                                                        label={i18next.t(
                                                            'useCompatibleCalcScript',
                                                        )}
                                                        nullable={false}
                                                        options={
                                                            this.state
                                                                .calculationScriptsOptions
                                                        }
                                                        value={checkValueInOptionsByTitle(
                                                            script.name,
                                                            this.state
                                                                .calculationScriptsOptions,
                                                        )}
                                                        required
                                                        validationErrors={{
                                                            isDefaultRequiredValue:
                                                                i18next.t(
                                                                    'isRequired',
                                                                ),
                                                        }}
                                                    />
                                                )}
                                                <EmptyComponent
                                                    key={`empCalculationScripts${script.js_id}`}
                                                />
                                            </WrapComponent>
                                        );
                                    })}
                                </HiddenTabContent>
                            )}
                        </Form>
                    </HiddenTabs>
                    <ConfirmModal
                        text={i18next.t('confirmSaveChanges')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                </TabsWrapper>
            </DocumentTitle>
        );
    }

}

TemplateCopyImport.displayName = 'TemplateCopyImport';

TemplateCopyImport.propTypes = {
    history: PropTypes.objectOf(PropTypes.any).isRequired,
    match: PropTypes.objectOf(PropTypes.any).isRequired,
    location: PropTypes.objectOf(PropTypes.any).isRequired,
};

export default TemplateCopyImport;
