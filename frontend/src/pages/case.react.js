import PropTypes from 'prop-types';
import i18next from 'i18next';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    Stack,
    Typography,
} from '@mui/material';
import {
    guid,
    checkUserParams,
    lovValueCheckLangMutation,
    getParameterByName,
    recursivelyLoadFolders,
    roundNumber,
    momentFormatDate,
    popupPdf,
    isImage,
    isPdf,
    isText,
    isWord,
    isVideo,
} from '../common/utils';
import { checkRevision } from './uploadDocumentUtil';
import ServiceOperationsModal from './modals/serviceOperationsModal.react';
// components 5.0
import ModalConfirm from '../components5.0/Modals/ModalConfirm';
import ModalEvent from '../components5.0/Modals/modal-event/ModalEvent';
import ModalCaseContact from '../components5.0/Modals/ModalCaseContact';
import Button from '../components5.0/Button';
import MyIcon from '../components5.0/MyIcon';
import ModalPrintCase from '../components5.0/Modals/ModalPrintCase';
import ModalTaskHandover from '../components5.0/Modals/ModalTaskHandover';
import { CaseSkeleton } from '../components5.0/sections/cases/case-detail/CaseSkeletons';
import { useResponsive } from '../components5.0/hooks';
import ApiRequest from '../api/apiRequest';
import ModalCaseLogs from "../components5.0/Modals/modal-case-logs/ModalCaseLogs";
import React from 'react';
import _ from 'lodash';
import createReactClass from 'create-react-class';
import CaseTab from './case/caseTab.react';
import CaseOverviewTab from './case/caseOverviewTab.react';
import Column from '../components/table/column.react';
import alt from '../flux/alt';
import CaseStore from '../flux/case.store';
import CaseActions from '../flux/case.actions';
import CaseApi from '../flux/case.api';
import AlertsActions from '../components/alerts/alerts.actions';
import CaseEditVariableModal from './modals/caseEditVariableModal.react';
import TabActions from '../flux/tab.actions';
import TabStore from '../flux/tab.store';
import Factory from '../flux/factory';
import LoggedUserStore from '../flux/loggedUser.store';
import LoggedUserActions from '../flux/loggedUser.actions';
// https://github.com/eligrey/FileSaver.js/, http://purl.eligrey.com/github/FileSaver.js/blob/master/FileSaver.js
import { saveAs } from '../../assets/libs/filesaver';
import CasePrintStore from '../flux/casePrint.store';
import CasePrintActions from '../flux/casePrint.actions';
import FilePreviewModal from './modals/filePreviewModal.react';
import refreshPageMixin from '../pages/refreshPageMixin';

/* eslint-disable object-shorthand */
// eslint-disable-next-line react/prefer-es6-class
const Case = createReactClass({
    displayName: 'Case',

    propTypes: {
        history: PropTypes.objectOf(PropTypes.any).isRequired,
        match: PropTypes.objectOf(PropTypes.any).isRequired,
        location: PropTypes.objectOf(PropTypes.any).isRequired,
    },

    mixins: [refreshPageMixin],

    getInitialState: function () {
        return _.extend(
            {
                editVaribleModalIsOpen: false,
                caseContactModalIsOpen: false,
                deleteModalIsOpen: false,
                takeModalIsOpen: false,
                variableId: null,
                caseId: null,
                contactId: null,
                taskId: null,
                attachmentId: null,
                addAttachmentModalIsOpen: false,
                modalEditAttachment: false,
                revisionAttachmentModalIsOpen: false,
                printCaseModalState: false,
                casesEventModalIsOpen: false,
                // noteIsPristine: true,
                fileId: null,
                nodeId: 'root',
                foldersOptions: [],
                handoverModalIsOpen: false,
                handoverNoLimits: false,
                logDetailModalIsOpen: false,
                logId: null,
                choicesLogCategory: [],
                signModalIsOpen: false,
                documentSrc: null,
                documentFileType: null,
                previewModalIsOpen: false,
                // confirmModalIsOpen: false,
                confirmHandoverModalIsOpen: false,
                serviceModalIsOpen: false,
                confirmChangesModalIsOpen: false,
                serviceModalData: {},
                takeActualState: {},
                restrictTableSorting: LoggedUserStore.getConfig(
                    'tas.restrictTableSorting',
                ),
            },
            CaseStore.getState(),
        );
    },

    componentDidMount: function () {
        const objAttach = Factory.registerFlux(
            TabStore,
            TabActions,
            `tabCaseAttachments${this.props.match.params.caseId}`,
        );
        this.tabActionAttach = objAttach.action;
        /* var objNotes = Factory.registerFlux(
            TabStore,
            TabActions,
            'tabCaseNotes' + this.props.match.params.caseId
        );
        this.tabActionNotes = objNotes.action; */
        const objTasks = Factory.registerFlux(
            TabStore,
            TabActions,
            `tabCaseTasks${this.props.match.params.caseId}`,
        );
        this.tabActionTasks = objTasks.action;

        if (LoggedUserStore.isAdmin()) {
            const objErrorLogs = Factory.registerFlux(
                TabStore,
                TabActions,
                `tabCaseLogs-${this.props.match.params.caseId}`,
            );
            this.tabActionErrorLogs = objErrorLogs.action;
        }

        CaseStore.listen(this._onChange);

        // Case with vice - /cases/case/993?uv-id=25
        const uvId = getParameterByName('uv-id', window.location.search);

        if (uvId !== null) {
            const findVice = _.find(
                LoggedUserStore.getState().vices,
                ['uv_id', Number(uvId)],
            );

            if (findVice) {
                LoggedUserActions.setVice(findVice.id, uvId);
            } else {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrViceNotFound'),
                    show: true,
                    allowCountdown: true,
                });

                this.props.history.push(
                    `/cases/case/${this.props.match.params.caseId}`,
                );
                this.fetchAll(this.props);
            }
        } else {
            this.fetchAll(this.props);
        }

        // this.watchAddNoteBeforeLeave();
    },

    UNSAFE_componentWillUpdate: function (nextProps, nextState) {
        if (nextProps.match.params.caseId != this.props.match.params.caseId) {
            // connect new tab store after case change
            const objAttach = Factory.registerFlux(
                TabStore,
                TabActions,
                `tabCaseAttachments${nextProps.match.params.caseId}`,
            );

            this.tabActionAttach = objAttach.action;

            this.fetchAll(nextProps);
        }

        // refresh case overview after manual event
        if (
            (this.state.caseOverview || this.state.caseOverviewJs) &&
            this.props.location.state !== nextProps.location.state &&
            nextProps.location.state &&
            nextProps.location.state.refreshPage === true
        ) {
            const id = this.props.match.params.caseId;
            CaseActions.fetchComponents(
                id,
                this.state.breadPrevPath,
                this.state.closePrevPath,
            );
            CaseActions.fetchCustomPrints(id);

            // Events
            CaseActions.fetchEvents(id).then((events) => {
                this.setState({ events: events });
            });
        }
    },

    componentWillUnmount: function () {
        const { breadPrevPath } = this.state;
        const { closePrevPath } = this.state;

        // this.unlistenForm();
        CaseStore.unlisten(this._onChange);
        alt.recycle(CaseStore);

        try {
            const { caseId } = this.props.match.params;
            const { printId } = this.state;
            const casePrintObj = Factory.registerFlux(
                CasePrintStore,
                CasePrintActions,
                `casePrint-${caseId}-${printId}`,
            );
            const casePrintStore = casePrintObj.store;
            const casePrintState = casePrintStore.getState();

            if (casePrintState.clearOnExit) {
                alt.recycle(casePrintStore);
            }
        } catch (e) {
            // nothing
        }

        // resave previous paths after store is recycled
        CaseActions.savePrevPath({
            breadPrevPath: breadPrevPath,
            closePrevPath: closePrevPath,
        });
    },

    /**
     * Refresh page - function is called from refreshPageMixin
     * @param  {number} eventId - not used
     * @param  {number} taskId The id of the task being solved
     */
    refreshPage: function (eventId, taskId) {
        const tableRef = this.refs.caseActualTasks || this.refs.caseHistory;

        if (typeof tableRef !== 'undefined') {
            tableRef.handleFetchRows({ taskIdBeingSolved: taskId });
        }

        this.tabActionTasks.getLabelCount({
            apiUrl: `/processes/${this.props.match.params.caseId}/tasks`,
            isTasks: true,
        });
    },

    _onChange: function (state) {
        this.setState(state);
    },

    closeConfirmModal() {
        // cancel and stay
        this.setState({ confirmModalIsOpen: false });
    },

    /**
     * @return {Boolean}
     */
    /* noteIsNotEmpty: function (watchSave) {
        if (this.refs.notes) {
            var note = this.refs.notes.getValue();
            return (
                typeof note !== 'undefined' &&
                note !== null &&
                note.trim().length != 0
            );
        } else if (watchSave) {
            return true;
        }
    }, */

    /* formChanged: function (currentValues, isChanged) {
        if (
            isChanged === true &&
            this.noteIsNotEmpty() &&
            this.state.noteIsPristine
        ) {
            this.setState({ noteIsPristine: false });
        } else if (!this.noteIsNotEmpty() && !this.state.noteIsPristine) {
            this.setState({ noteIsPristine: true });
        }
    }, */

    handleValidSubmit: function (data) {
        this.onFormAction(data);
    },

    fetchAll: function (props) {
        const id = props.match.params.caseId;

        // get count of attachments
        this.tabActionAttach.getLabelCount({
            apiUrl: `/processes/${id}/attachments`,
        });
        // get count of notes
        /* this.tabActionNotes
            .getLabelCount({ apiUrl: `/processes/${id}/notes` })
            .then((totalCount) => {
                if (totalCount > 0) {
                    //Fetch notes to second tab
                    CaseActions.fetchNotes(id);
                }
            }); */
        // get count of case tasks
        /* this.tabActionTasks.getLabelCount({
            apiUrl: `/processes/${id}/tasks`,
            isTasks: true,
        }); */
        // get count of error logs
        if (LoggedUserStore.isAdmin()) {
            const filter = `iproc_id<eq>"${id}"<and>level<eq>"1"`;
            this.tabActionErrorLogs.getLabelCount({
                apiUrl: `/logs?filter=${encodeURIComponent(filter)}`,
                errMessage: `Logs count: ${i18next.t('alrFailedData')}`,
            });
        }

        // location state for previous breadcrumb path and close button
        const locationState = props.location.state;

        if (locationState) {
            CaseActions.savePrevPath(props.location.state);
        }

        let prevUrl;
        if (
            locationState &&
            typeof locationState.breadPrevPath !== 'undefined'
        ) {
            prevUrl = locationState.breadPrevPath;
        } else if (this.state.breadPrevPath !== null) {
            prevUrl = this.state.breadPrevPath;
        } else {
            prevUrl = '/cases';
        }

        let closePath = null;
        if (
            locationState &&
            typeof locationState.closePrevPath !== 'undefined'
        ) {
            closePath = locationState.closePrevPath;
        }

        // Fetch components
        CaseActions.fetchComponents(id, prevUrl, closePath);
        CaseActions.fetchCustomPrints(id);

        // Events
        CaseActions.fetchEvents(id).then((events) => {
            this.setState({ events: events });
        });
    },

    goBack: function (e) {
        e.preventDefault();

        this.props.history.push(this.state.closePrevPath);
    },

    // drag&drop over table
    onLoad: function (file) {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            hasSpinner: true,
            type: 'info',
            message: i18next.t('alrDocumentAdding'),
        });
        const { caseId } = this.props.match.params;

        const table = this.refs.caseAttachment;

        const formData = new FormData();

        // text parameters must be first! http://sailsjs.com/documentation/reference/request-req/req-file
        formData.append('filename', file.name);
        formData.append('iprocId', caseId);
        // the file itself must be the last appended param
        formData.append('file', file);

        CaseApi.storeFile(formData, file, caseId)
            .then((payload) => {
                const fileId = payload.id;

                this.tabActionAttach.getLabelCount({
                    apiUrl: `/processes/${caseId}/attachments`,
                });

                const folder =
                    this.state.nodeId != 'root' ? this.state.nodeId : 'current'; // složku lze měnit pouze z modálu

                checkRevision(fileId, alertId, null, folder).then(
                    (metadataArr) => {
                        this.postMetadata(
                            metadataArr,
                            alertId,
                            fileId,
                            payload.message,
                        );
                    },
                );
            })
            .catch((errorMessage) => {
                if (_.get(errorMessage, 'error.codeName') === 'FILE_TOO_BIG') {
                    const maxUploadSize =
                        LoggedUserStore.getConfig('dms.maxUploadSize') /
                        1024 /
                        1024;

                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachTooBig', {
                            maxUploadSize: maxUploadSize,
                        }),
                    });
                } else if (
                    _.get(errorMessage, 'error.codeName') ===
                    'VICE_VIEW_ONLY_ERROR'
                ) {
                    AlertsActions.removeButtonAlert(alertId);
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('alrNoPermsToAddDocInVice'),
                        show: true,
                        allowCountdown: true,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachSaveFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    },

    postMetadata(metadataArr, alertId, fileId, additionalMessage) {
        const table = this.refs.caseAttachment;

        CaseApi.storeFileMetadata(fileId, metadataArr)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachSaved'),
                });

                if (typeof table !== 'undefined') {
                    table.handleFetchRows();
                    table.handleClickRow(); // clear table store because previously selected revision is not visible in the table anymore
                }
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrAttachMetaFailed'),
                    serverError: errorMessage,
                });
            });
    },

    downloadFile: function (e, rowId, row) {
        e.preventDefault();

        let id = this.refs.caseAttachment.state.selectedRow.rowId || rowId;
        const rows = this.refs.caseAttachment.state.selectedRows;
        const isMultiple = !_.isEmpty(rows);
        if (isMultiple) {
            id = rows.map((row) => {
                return row.rowId;
            });
        }
        const attach = this.refs.caseAttachment.state.selectedRow.row || row;

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrAttachDownloading'),
        });

        const progress = (percent) => {
            AlertsActions.changeAlert({
                id: alertId,
                type: 'info',
                message: i18next.t('alrAttachDownloading'),
                progress: percent,
            });
        };

        const fileName = isMultiple ? 'documents.zip' : attach.name;

        CaseApi.getDownloadedFile(id, progress)
            .then((payload) => {
                saveAs(payload, fileName); // payload is Blob

                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachDownloaded'),
                });
            })
            .catch((errorMessage) => {
                if (
                    _.get(errorMessage, 'error.codeName') ===
                    'INACCESSIBLE_FILE'
                ) {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                } else if (
                    _.get(errorMessage, 'error.codeName') ===
                    'FILE_LACK_OF_PERMISSIONS'
                ) {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachDownloadLackOfPerms'),
                        serverError: errorMessage,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    },

    handleKeyDown: function (event) {
        if (event.which === 46) {
            // delete key
            this.openDeleteModal(event);
        }
    },

    handleTaskSolve(e, rowId, row, inNewTab) {
        e.preventDefault();
        const id = this.refs.caseActualTasks.state.selectedRow.rowId || rowId;
        if (!isNaN(id) && id !== null) {
            if (inNewTab) {
                return `/tasks/task/${id}`;
            }
            this.props.history.push({
                pathname: `/tasks/task/${id}`,
                state: {
                    breadPrevPath: '/tasks',
                    closePrevPath: location.pathname,
                },
            });
        }
    },

    openDeleteModal: function (e) {
        e.preventDefault();
        this.setState({
            deleteModalIsOpen: true,
            attachmentId: this.refs.caseAttachment.state.selectedRow.rowId,
        });
    },

    deleteAttachment: function () {
        const fileId = this.state.attachmentId;
        this.closeDeleteModal();

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrAttachDeleting'),
        });

        const table = this.refs.caseAttachment;

        CaseApi.deleteFile(fileId)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrAttachDeleted'),
                });
                this.tabActionAttach.getLabelCount({
                    apiUrl: `/processes/${this.props.match.params.caseId}/attachments`,
                });
                const row = table.getNextRow();
                table.handleClickRow(row);
                table.handleFetchRows();
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrAttachDeleteFailed'),
                    serverError: errorMessage,
                });
            });
    },

    closeDeleteModal: function () {
        this.setState({ deleteModalIsOpen: false });
    },

    openContactModal5: function (row, e) {
        e?.preventDefault();
        this.setState({
            caseContactModalIsOpen: true,
            contactId: row.itask_user_id || row.itaskh_user_id,
        });
    },

    openContactModal: function (tableRef, e) {
        e.preventDefault();
        const selectedRow = this.refs[tableRef].state.selectedRow.row;
        this.setState({
            caseContactModalIsOpen: true,
            contactId: selectedRow.itask_user_id || selectedRow.itaskh_user_id,
        });
    },

    closeContactModal: function () {
        this.setState({ caseContactModalIsOpen: false });
    },

    openTakeModal: function (row) {
        this.setState({
            takeModalIsOpen: true,
            takeActualState: row,
        });
    },

    closeTakeModal: function () {
        this.setState({
            takeModalIsOpen: false,
            takeActualState: {},
        });
    },

    takeActual: function () {
        this.closeTakeModal();
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrTskTakign'),
        });

        const rowId = this.state.takeActualState.id;
        const solverExist = this.state.takeActualState.itask_user_id !== null;

        CaseApi.takeActualTask(rowId, solverExist)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrTskTaken'),
                });

                // TODO: reload table rows

                if (payload.NEXT_ITASK_ID) {
                    this.props.history.push({
                        pathname: `/tasks/task/${payload.NEXT_ITASK_ID}`,
                        state: {
                            breadPrevPath: '/tasks',
                            closePrevPath: location.pathname,
                        },
                    });
                }
            })
            .catch((errorMessage) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrTskTakeFailed'),
                    serverError: errorMessage,
                });
            });
    },

    handleTaskDetail(e, rowId, row, inNewTab) {
        e.preventDefault();
        const { selectedRow } = this.refs.caseHistory.state;
        const itaskId = selectedRow.row
            ? selectedRow.row.itask_id
            : row.itask_id;
        const itaskhId = selectedRow.rowId || rowId;

        if (inNewTab) {
            return `/tasks/task/${itaskId}/historical/${itaskhId}`;
        }
        this.props.history.push({
            pathname: `/tasks/task/${itaskId}/historical/${itaskhId}`,
            state: {
                breadPrevPath: '/tasks',
                closePrevPath: location.pathname,
            },
        });
    },

    openModalEditAttachment: function (e) {
        e.preventDefault();
        this.setState({
            modalEditAttachment: true,
            attachmentId: this.refs.caseAttachment.state.selectedRow.rowId,
            attachment: this.refs.caseAttachment.state.selectedRow.row,
        });
    },

    closeModalEditAttachment: function () {
        this.setState({
            modalEditAttachment: false,
        });
    },

    openRevisionAttachmentModal: function (e) {
        e.preventDefault();
        this.setState({
            revisionAttachmentModalIsOpen: true,
            attachmentId: this.refs.caseAttachment.state.selectedRow.rowId,
        });
    },

    closeRevisionAttachmentModal: function () {
        this.setState({ revisionAttachmentModalIsOpen: false });
    },

    openAddAttachmentModal: function (e) {
        e.preventDefault();
        this.setState({
            addAttachmentModalIsOpen: true,
        });
    },

    closeAddAttachmentModal: function () {
        this.setState({ addAttachmentModalIsOpen: false });
    },

    /* addNote: function (note, event) {
        if (event) event.preventDefault();

        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrNoteSaving'),
        });
        CaseActions.setNotesLoader(true);
        const caseId = this.props.match.params.caseId;

        const newNote = { iproc_note: note, itask_id: this.state.taskId };

        CaseApi.addNote(caseId, newNote)
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrNoteAdded'),
                });

                // reset note
                CaseActions.saveNote('');

                CaseActions.fetchNotes(caseId);
                this.tabActionNotes.getLabelCount({
                    apiUrl: `/processes/${caseId}/notes`,
                });
            })
            .catch((errorMessage) => {
                CaseActions.setNotesLoader(false);

                if (
                    _.get(errorMessage, 'error.codeName') ===
                    'VICE_VIEW_ONLY_ERROR'
                ) {
                    AlertsActions.removeButtonAlert(alertId);
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('alrNoPermsToAddNoteInVice'),
                        show: true,
                        allowCountdown: true,
                    });
                } else {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'alert',
                        message: i18next.t('alrNoteSaveFailed'),
                        serverError: errorMessage,
                    });
                }
            });
    }, */

    /* handleAddNote: function (event, callback, watchSave) {
        if (event) event.preventDefault();

        if (this.noteIsNotEmpty(watchSave)) {
            var alertId = guid();
            AlertsActions.addAlert({
                id: alertId,
                type: 'info',
                message: i18next.t('alrNoteSaving'),
            });
            CaseActions.setNotesLoader(true);
            var caseId = this.props.match.params.caseId;
            var notes = this.refs.notes;
            var noteValue = this.refs.notes
                ? notes.getValue()
                : this.state.note;
            var note = { iproc_note: noteValue, itask_id: this.state.taskId };

            CaseApi.addNote(caseId, note)
                .then((payload) => {
                    AlertsActions.changeAlert({
                        id: alertId,
                        type: 'success',
                        message: i18next.t('alrNoteAdded'),
                    });

                    if (notes) {
                        notes.resetValue();
                    }

                    // reset note
                    CaseActions.saveNote('');

                    CaseActions.fetchNotes(caseId);
                    this.tabActionNotes.getLabelCount({
                        apiUrl: `/processes/${caseId}/notes`,
                    });
                    this.setState({ noteIsPristine: true }, function () {
                        if (callback) {
                            callback();
                        }
                    });
                })
                .catch((errorMessage) => {
                    CaseActions.setNotesLoader(false);

                    if (
                        _.get(errorMessage, 'error.codeName') ===
                        'VICE_VIEW_ONLY_ERROR'
                    ) {
                        AlertsActions.removeButtonAlert(alertId);
                        AlertsActions.addAlert({
                            type: 'warning',
                            message: i18next.t('alrNoPermsToAddNoteInVice'),
                            show: true,
                            allowCountdown: true,
                        });
                    } else {
                        AlertsActions.changeAlert({
                            id: alertId,
                            type: 'alert',
                            message: i18next.t('alrNoteSaveFailed'),
                            serverError: errorMessage,
                        });
                    }
                });
        } else {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrFillNote'),
                show: true,
                allowCountdown: true,
            });
        }
    }, */

    toParentCase(/* e, */ inNewTab) {
        /* e.preventDefault(); */

        const parentCaseId = this.state.items.iproc_main_iproc_id;
        if (inNewTab) {
            return `/cases/case/${parentCaseId}`;
        }
        this.props.history.push(`/cases/case/${parentCaseId}`);
    },

    toNestedCase(e, inNewTab) {
        e.preventDefault();

        const nestedCaseId =
            this.refs.caseActualTasks.state.selectedRow.row
                .itask_subprocess_iproc_id;
        if (inNewTab) {
            return `/cases/case/${nestedCaseId}`;
        }
        this.props.history.push(`/cases/case/${nestedCaseId}`);
    },

    toNestedCaseFromHistory(e, inNewTab) {
        e.preventDefault();

        const nestedCaseId =
            this.refs.caseHistory.state.selectedRow.row
                .itaskh_subprocess_iproc_id;
        if (inNewTab) {
            return `/cases/case/${nestedCaseId}`;
        }
        this.props.history.push(`/cases/case/${nestedCaseId}`);
    },

    // open print case modal
    printCase: function (/* event */) {
        /* event.preventDefault(); */
        this.setState({
            printCaseModalState: true,
        });
    },

    // close print case modal
    closeCaseModal: function () {
        this.setState({
            printCaseModalState: false,
        });
    },

    // open edit variables modal
    openEditVariableModal: function (row, event) {
        event?.preventDefault();
        this.setState({
            editVaribleModalIsOpen: true,
            variableId: row.id,
            caseId: this.props.match.params.caseId,
            row: row,
        });
    },

    // close edit variables modal
    closeEditVariableModal: function () {
        this.setState({
            editVaribleModalIsOpen: false,
        });
    },

    // open case event
    openEventModal: function (event) {
        event.preventDefault();
        this.setState({
            casesEventModalIsOpen: true,
            caseId: this.props.match.params.caseId,
        });
    },

    closeEventModal: function () {
        this.setState({
            casesEventModalIsOpen: false,
        });
    },

    openTimingTaskTab: function (e) {
        e.preventDefault();
        this.props.history.push({
            pathname: `/tasks/task/${this.refs.caseActualTasks.state.selectedRow.rowId}`,
            state: { closePrevPath: location.pathname, taskForTiming: true },
        });
    },

    showDiagram: function (/* e */) {
        /*   e.preventDefault(); */
        this.props.history.push(
            `/cases/case/${this.props.match.params.caseId}/graph`,
        );
    },

    loadTreeData: function (tree) {
        if (tree.state.treeState != null) {
            this.setState({ nodeId: tree.state.treeState.selected_node[0] });
        }

        CaseApi.fetchTree()
            .then((payload) => {
                const arrRoot = [
                    {
                        id: 'root',
                        label: i18next.t('allFiles'),
                        is_open: true,
                        selected: true,
                        children: payload.items,
                    },
                ];

                tree.loadTree(arrRoot);

                checkUserParams([], 'columnsWidths', 'caseAttachment', true);

                const arr = [];
                payload.items.forEach((item) => {
                    const obj = {
                        value: item.id,
                        title: item.label,
                    };
                    arr.push(obj);

                    recursivelyLoadFolders(arr, item);
                });

                this.setState({ foldersOptions: arr });
            })
            .catch((errorMessage) => {
                AlertsActions.addAlert({
                    type: 'alert',
                    message: i18next.t('alrTreeDataFailed'),
                    serverError: errorMessage,
                });
            });
    },

    treeClick: function (nodeId) {
        this.setState({ nodeId: nodeId });
    },

    treeEditable: function (tree) {
        if (tree.state.nodeId != 'root') {
            tree.setActiveEditButtons(true);
        } else {
            tree.setActiveEditButtons(false);
        }
    },

    openHandoverModal: function (row, e) {
        e?.preventDefault();
        this.setState({
            taskId: row.id,
            handoverModalIsOpen: true,
            handoverNoLimits: !!row.c_handover_no_limits,
        });
    },

    closeHandoverModal: function () {
        this.setState({ handoverModalIsOpen: false });
    },

    openConfirmModal(onConfirm, onReject) {
        this.onConfirm = onConfirm;
        this.onReject = onReject;
        this.setState({ confirmHandoverModalIsOpen: true });
    },

    confirmHandover() {
        // save and leave
        this.onConfirm();
    },

    cancelHandover() {
        // cancel saving and leave
        this.onReject();
    },

    closeConfirmHandoverModal() {
        // cancel and stay
        this.setState({ confirmHandoverModalIsOpen: false });
    },

    editHistoryTask: function (e) {
        e.preventDefault();
        const itaskId = this.refs.caseHistory.state.selectedRow.row.itask_id;
        const itaskhId = this.refs.caseHistory.state.selectedRow.rowId;

        this.props.history.push({
            pathname: `/tasks/task/${itaskId}/edit-historical/${itaskhId}`,
            state: {
                breadPrevPath: '/tasks',
                closePrevPath: location.pathname,
            },
        });
    },

    // toPermissionsPage(/* e */) {
    //     /*  e.preventDefault(); */
    //     const { caseId } = this.props.match.params;
    //
    //     this.props.history.push(`/cases/case/${caseId}/permissions`);
    // },

    actualTasksStatusRenderer: function (value, row) {
        const userName =
            row.itask_assesment_user_id !== null ||
            row.itask_assesment_user !== ' '
                ? row.itask_assesment_user
                : row.iproc_owner_display_name;
        let title = i18next.t(`taskStatus${value}`);

        if (value == 'W') {
            // To assign
            title = `${i18next.t('taskStatusW')}: ${userName}`;
        }

        if (
            row.itask_due_offset == 'po' ||
            (row.itask_duration == 'po' && row.itask_due_date_finish === null)
        ) {
            // To scheduling
            title = `${i18next.t('taskStatusWT')}: ${userName}`;
        }

        if (value == 'N' && row.itask_auto_start == 'Y') {
            // Scheduled
            let date = '';
            if (row.itask_due_date_start !== null) {
                date = momentFormatDate(row.itask_due_date_start, false, true);
            }

            title = `${i18next.t('taskStatusP')}: ${date}`;
        }

        if (row.iproc_status == 'S') {
            // Suspended
            title = i18next.t('taskStatusS');
        }

        if (value == 'A' && row.itask_type == 'P') {
            // Active sub process
            title = i18next.t('taskStatusAP');
        }

        /* if(row.sub_info == 'S') { // TODO nechodi sub_info
            // Suspended sub process
            title = i18next.t('taskStatusAS');
        } */

        return title;
    },

    canSolve: function (row) {
        if (
            LoggedUserStore.getState().taskIdBeingSolved.indexOf(row.id) !== -1
        ) {
            return false;
        }
        // timing
        if (
            row.iproc_status === 'A' &&
            row.itask_status === 'W' &&
            (row.itask_due_offset === 'po' || row.itask_duration === 'po')
        ) {
            return true;
        }
        // assign
        if (
            row.iproc_status === 'A' &&
            row.itask_status === 'W' &&
            row.itask_user_id === null &&
            (row.itask_assesment_method === 'S' ||
                row.itask_assesment_method === 'U' ||
                row.itask_assesment_method === 'P')
        ) {
            return true;
        }
        return row.c_solve === 1;
    },

    // check for lovs values
    checkValLangMutationRenderer: function (value, row) {
        if (row.ivar_type === 'LT') {
            if (row.ivar_multi === 'X' && Array.isArray(value)) {
                const translatedValsArr = [];

                value.forEach((val) => {
                    translatedValsArr.push(lovValueCheckLangMutation(row, val));
                });

                return translatedValsArr.join(', ');
            }

            return lovValueCheckLangMutation(row, value);
        }
        if (row.ivar_lov_value) {
            return row.ivar_lov_value;
        }

        return value;
    },

    openSignModal(action, e) {
        e.preventDefault();

        const { selectedRow } = this.refs.caseAttachment.state;

        if (selectedRow) {
            this.setState({
                signModalIsOpen: true,
                signModalAction: action,
                attachmentId: selectedRow.rowId,
                documentSrc: selectedRow.row.dmsf_src,
                documentFileType: selectedRow.row.file_type,
            });
        }
    },

    closeSignModal() {
        this.setState({ signModalIsOpen: false });
    },

    openPreview(file, e) {
        e.preventDefault();

        if (file.file_type === 'application/pdf') {
            return ApiRequest.getFile(`/dms/download/${file.id}`)
                .then((attach) => {
                    popupPdf(attach);
                })
                .catch((errorMessage) => {
                    AlertsActions.addAlert({
                        type: 'alert',
                        message: i18next.t('alrAttachDownloadFailed'),
                        serverError: errorMessage,
                    });
                });
        }
        this.setState({
            previewModalIsOpen: true,
            filePreview: file,
        });
    },
    canOpenPreview(name) {
        let style;
        if (
            isImage(name) ||
            isPdf(name) ||
            isText(name) ||
            isWord(name) ||
            isVideo(name)
        ) {
            return (style = {});
        }
        return (style = { pointerEvents: 'none', opacity: '0' });
    },

    closeFilePreviewModal() {
        this.setState({ previewModalIsOpen: false });
    },

    openServiceModal(e) {
        if (e) e.preventDefault();
        this.setState({ serviceModalIsOpen: true });
    },

    closeServiceModal() {
        this.setState({ serviceModalIsOpen: false });
    },

    openConfirmChangesModal(data) {
        this.setState({
            confirmChangesModalIsOpen: true,
            serviceModalData: data,
        });
    },

    closeConfirmChangesModal() {
        this.setState({ confirmChangesModalIsOpen: false });
    },

    confirmChanges() {
        const alertId = guid();
        AlertsActions.addAlert({
            id: alertId,
            type: 'info',
            message: i18next.t('alrSaving'),
        });

        const obj = _.clone(this.state.serviceModalData);
        let url = '/service-operations/processes/list';
        const filter = `iproc_id<eq>"${this.props.match.params.caseId}"`;
        url += `?filter=${encodeURIComponent(filter)}`;

        this.closeConfirmChangesModal();
        this.closeServiceModal();

        ApiRequest.post(url, JSON.stringify(obj || {}))
            .then((payload) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'success',
                    message: i18next.t('alrSaved'),
                });
            })
            .catch((error) => {
                AlertsActions.changeAlert({
                    id: alertId,
                    type: 'alert',
                    message: i18next.t('alrSaveFailed'),
                    serverError: error,
                });
            });
    },

    render: function () {
        let headingStatus;
        if (this.state.iproc_status == 'D') {
            headingStatus = i18next.t('finished');
        } else if (this.state.iproc_status == 'S') {
            headingStatus = i18next.t('suspended');
        } else {
            headingStatus = '';
        }

        const { restrictTableSorting } = this.state;
        let caseTab = <CaseSkeleton />;
        if (this.state.caseOverview || this.state.caseOverviewJs) {
            caseTab = (
                <CaseOverviewTab
                    caseOverview={this.state.caseOverview}
                    toParentCase={this.toParentCase}
                    goBack={this.goBack}
                    printCase={this.printCase}
                    showDiagram={this.showDiagram}
                    caseOverviewJs={this.state.caseOverviewJs}
                    items={this.state.items}
                    printCaseId={this.state.printCaseId}
                    printId={this.state.printId}
                    closePrevPath={this.state.closePrevPath}
                    // toPermissionsPage={this.toPermissionsPage}
                    location={this.props.location}
                    caseId={this.props.match.params.caseId}
                    caseOverviewCss={this.state.caseOverviewCss}
                    printReact={this.state.printReact}
                    openServiceModal={this.openServiceModal}
                    parent={this}
                    // new props
                    eventButtonOnClick={this.openEventModal}
                    eventButtonDisabled={
                        !(
                            this.state.iproc_status === 'A' ||
                            this.state.iproc_status === 'N'
                        ) || this.state.archived
                    }
                    closeButtonOnClick={this.goBack}
                    caseTitle={this.state.heading}
                    caseStatus={headingStatus}
                    openContactModal5={this.openContactModal5}
                    openTakeModal={this.openTakeModal}
                    openHandoverModal={this.openHandoverModal}
                    openEditVariableModal={this.openEditVariableModal}
                    archived={this.state.archived}
                />
            );
        } else if (!this.state.caseOverviewLoading) {
            caseTab = (
                <CaseTab
                    items={this.state.items}
                    toParentCase={this.toParentCase}
                    goBack={this.goBack}
                    printCase={this.printCase}
                    showDiagram={this.showDiagram}
                    // toPermissionsPage={this.toPermissionsPage}
                    openServiceModal={this.openServiceModal}
                    // new props
                    eventButtonOnClick={this.openEventModal}
                    eventButtonDisabled={
                        !(
                            this.state.iproc_status === 'A' ||
                            this.state.iproc_status === 'N'
                        ) || this.state.archived
                    }
                    closeButtonOnClick={this.goBack}
                    caseTitle={this.state.heading}
                    caseStatus={headingStatus}
                    parent={this}
                    openContactModal5={this.openContactModal5}
                    openTakeModal={this.openTakeModal}
                    openHandoverModal={this.openHandoverModal}
                    openEditVariableModal={this.openEditVariableModal}
                    archived={this.state.archived}
                />
            );
        }

        const { nodeId } = this.state;
        const tableFilter = nodeId != 'root' ? `dtf-4<eq>"${nodeId}"` : '';

        const { caseId } = this.props.match.params;
        const currentDate = new Date().setHours(24, 0, 0, 0); // next mignight

        const nyChoices = [
            { value: 'N', title: i18next.t('no') },
            { value: 'Y', title: i18next.t('yes') },
        ];

        const choices_0_1 = [
            { value: '0', title: i18next.t('yes') },
            { value: '1', title: i18next.t('no') },
        ];
        const documentsColumns = [
            <Column
                key="name"
                title={i18next.t('name')}
                name="name"
                type="text"
                renderer={(value, row) => {
                    return (
                        <>
                            <span
                                className="icon green icon-magnifier"
                                style={this.canOpenPreview(row.name)}
                                onClick={this.openPreview.bind(null, row)}
                            />
                            <span>{value}</span>
                        </>
                    );
                }}
            />,
            <Column
                key="folder"
                title={i18next.t('folder')}
                name="dtf-4"
                type="callback"
                filterChoices={this.state.foldersOptions}
                renderer={(value, row) => {
                    return row.folder_name;
                }}
            />,
            <Column
                key="owner"
                title={i18next.t('insertedBy')}
                name="dmsf_owner"
                type="text"
                renderer={function (value, row) {
                    if (row.dmsf_owner_vice && row.dmsf_owner_vice.trim()) {
                        return `${row.dmsf_owner_vice} (${i18next.t(
                            'insteadOf',
                        )}: ${row.dmsf_owner})`;
                    }
                    return row.dmsf_owner;
                }}
            />,
            <Column
                key="insert"
                title={i18next.t('insertedOn')}
                name="datetime_insert"
                type="datetime"
                width="135"
            />,
            <Column
                key="rev"
                title={i18next.t('revision')}
                name="revision"
                type="number"
                className="center"
                width="70"
            />,
            <Column
                key="case"
                title={i18next.t('case')}
                name="iproc_name"
                type="text"
            />,
            <Column
                key="indexed"
                title={i18next.t('isFullIndexed')}
                name="is_full_indexed"
                type="callback"
                filterChoices={nyChoices}
                renderer={(value, row) => {
                    const choice = _.find(nyChoices, ['value', value]);
                    return typeof choice !== 'undefined' ? choice.title : value;
                }}
            />,
        ];

        const { dmsColumns, modalFilterDmsColumnsOptions } =
            LoggedUserStore.getState();

        dmsColumns.forEach((column) => {
            if (column.id !== -2 && column.id !== -4) {
                // case and folder column is already displayed
                documentsColumns.push(
                    <Column
                        key={`dtf${column.id}`}
                        title={column.name}
                        name={`dtf${column.id}`}
                        type={column.type}
                        filterChoices={column.filterChoices}
                        renderer={(value, row) => {
                            const choice = _.find(
                                column.filterChoices,
                                ['value', value],
                            );
                            return typeof choice !== 'undefined'
                                ? choice.title
                                : value;
                        }}
                    />,
                );
            }
        });

        return (
            <>
                {/* <CaseDetailsLayout
            //     eventButtonOnClick={this.openEventModal}
            //     eventButtonDisabled={
            //         !(
            //             this.state.iproc_status === 'A' ||
            //             this.state.iproc_status === 'N'
            //         )
            //     }
            //     closeButtonOnClick={this.goBack}
            //     caseTitle={this.state.heading}
            //     caseStatus={headingStatus}
            // >
            // <DocumentTitle title={i18next.t('case')}> */}
                {/* <TabsWrapper> */}
                {/* <Heading
                            title={this.state.heading}
                            status={headingStatus}
                        > */}
                {/* <MainButtons>
                                <MainButton
                                    icon="icon-pulse"
                                    buttonColor="green"
                                    onClick={this.openEventModal}
                                    enableOn={
                                        this.state.iproc_status === 'A' ||
                                        this.state.iproc_status === 'N'
                                    }
                                    tooltipCode="ttEvent"
                                    data-cy={CypressConsts.ttEvent}
                                >
                                    {i18next.t('event')}
                                </MainButton>
                                <MainButton
                                    icon="icon-delete-1"
                                    buttonColor="white"
                                    onClick={this.goBack}
                                    tooltipCode="ttClose"
                                    title={i18next.t('close')}
                                    data-cy={CypressConsts.ttClose}
                                />
                            </MainButtons> */}
                {/* </Heading> */}
                {/* <Tabs params={this.props.match.params}>
                        <Tabs.Tab
                            key="case"
                            title={i18next.t('case')}
                            tabLink={'/cases/case/' + caseId}
                            name="tabCase"
                            showLoader
                            loaded={!this.state.caseOverviewLoading}
                            data-cy={CypressConsts.tabCase}
                        > */}
                {caseTab}
                {/* </Tabs.Tab> */}
                {/* <Tabs.Tab
                            key="notes"
                            title={i18next.t('notes')}
                            tabName="notes"
                            name={'tabCaseNotes' + caseId}
                            tabLink={'/cases/case/' + caseId + '/notes'}
                            data-cy={CypressConsts.tabNotes}
                        >
                            <Form
                                ref="formComments"
                                name="formComments"
                                onChange={this.formChanged}
                                onValidSubmit={this.handleValidSubmit}
                            >
                                <Notes
                                    key="comments"
                                    ref="notes"
                                    side="left"
                                    rows={3}
                                    value={this.state.note}
                                    options={this.state.notes}
                                    addNote={this.handleAddNote}
                                    loaded={!this.state.loadingNotes}
                                />
                            </Form>
                        </Tabs.Tab> */}
                {/* <Tabs.Tab
                            key="attachments"
                            title={i18next.t('attachments')}
                            tabName="attachments"
                            tabLink={'/cases/case/' + caseId + '/attachments'}
                            name={'tabCaseAttachments' + caseId}
                            data-cy={CypressConsts.tabAttachments}
                        >
                            <TabsButtonsTable
                                key={'buttonsCaseAttachment' + caseId + nodeId}
                                boundTableName={
                                    'caseAttachment' + caseId + nodeId
                                }
                            >
                                <TabsButton
                                    key="add"
                                    icon="icon-clip-1"
                                    isActive={true}
                                    onClick={this.openAddAttachmentModal}
                                    tooltipCode="ttAddAttach"
                                >
                                    {i18next.t('add')}
                                </TabsButton>
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    onClick={this.openModalEditAttachment}
                                    tooltipCode="ttEditAttach"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="revision"
                                    icon="icon-file-search"
                                    onClick={this.openRevisionAttachmentModal}
                                    tooltipCode="ttRevision"
                                >
                                    {i18next.t('revision')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonCaseAttachmentMore"
                                    boundTableName={
                                        'caseAttachment' + caseId + nodeId
                                    }
                                >
                                    <TabsButton
                                        key="down"
                                        icon="icon-file-download"
                                        onClick={this.downloadFile}
                                        hideTitle={false}
                                        tooltipCode="ttDownload"
                                        enableOnMultiple
                                    >
                                        {i18next.t('download')}
                                    </TabsButton>
                                    <TabsButton
                                        key="delete"
                                        icon="icon-bin-2"
                                        onClick={this.openDeleteModal}
                                        hideTitle={false}
                                        tooltipCode="ttDelAttach"
                                    >
                                        {i18next.t('delete')}
                                    </TabsButton>
                                </TabsButtonMore> */}
                {/* {zeaVisible && (
                                    <TabsButtonMore
                                        key="buttonsEarchive"
                                        boundTableName={`caseAttachment${caseId}${nodeId}`}
                                        title="elArchiv"
                                    >
                                        <TabsButton
                                            key="addToZea"
                                            icon="icon-download-5"
                                            onClick={this.openSignModal.bind(
                                                null,
                                                'addToZea'
                                            )}
                                            hideTitle={false}
                                            tooltipCode="ttAddToZea"
                                            enableOnRow={(row) => {
                                                return (
                                                    row.zea_is_not_in_archive ===
                                                        1 ||
                                                    row.zea_is_not_in_archive ===
                                                        null
                                                );
                                            }}
                                        >
                                            {i18next.t('addToZea')}
                                        </TabsButton>
                                        <TabsButton
                                            key="removeFromZea"
                                            icon="icon-upload-5"
                                            onClick={this.openZeaDeleteModal}
                                            hideTitle={false}
                                            tooltipCode="ttRemoveFromZea"
                                            enableOnRow={(row) => {
                                                return (
                                                    row.zea_is_not_in_archive ===
                                                    0
                                                );
                                            }}
                                        >
                                            {i18next.t('removeFromZea')}
                                        </TabsButton>
                                        {/*<TabsButton
                                            key="validation"
                                            icon="icon-information"
                                            onClick={this.validation}
                                            hideTitle={false}
                                            tooltipCode="ttZeaInfo"
                                        >
                                            {i18next.t('validation')}
                                        </TabsButton>
                                        <TabsButton
                                            key="sign"
                                            icon="icon-ink"
                                            onClick={this.openSignModal.bind(
                                                null,
                                                'sign'
                                            )}
                                            hideTitle={false}
                                            tooltipCode="ttSignZea"
                                        >
                                            {i18next.t('sign')}
                                        </TabsButton>
                                    </TabsButtonMore>
                                )} */}
                {/* <TabsButtonFilter
                                    key="filter"
                                    icon="icon-filter-1"
                                    groupType="documents"
                                    columnsOptions={
                                        modalFilterDmsColumnsOptions
                                    }
                                    parent={this}
                                    tableRef="caseAttachment"
                                >
                                    {i18next.t('filtrate')}
                                </TabsButtonFilter>
                            </TabsButtonsTable>
                            <TreeTable
                                key={'caseDocumentsAll' + caseId}
                                name={'caseDocumentsAll' + caseId}
                                dragAndDrop={false}
                                treeClick={this.treeClick}
                                loadTreeData={this.loadTreeData}
                                closed={true}
                                editable={this.treeEditable}
                                nodeDelete={{
                                    path: '/dms/folder',
                                    obj: { FOLDER_ID: nodeId },
                                }}
                            >
                                <Table
                                    key={'caseAttachment' + caseId + nodeId}
                                    apiUrl={
                                        '/processes/' + caseId + '/attachments'
                                    }
                                    ref="caseAttachment"
                                    name={'caseAttachment' + caseId + nodeId}
                                    onKeyDown={this.handleKeyDown}
                                    onDoubleClick={this.downloadFile}
                                    canOpenNewTab={false}
                                    dropFile={true}
                                    instructions={i18next.t('insertAttachTip')}
                                    onLoad={this.onLoad}
                                    defaultSort={{
                                        column: 'datetime_insert',
                                        order: 'desc',
                                    }}
                                    columnsWidthsGroup="caseAttachmentGroup"
                                    boundTreeName={'caseDocumentsAll' + caseId}
                                    defaultFilter={tableFilter}
                                    multipleSelectionOnCtrl
                                    multipleSelectionOnShift
                                    totalCount={false}
                                    disableSortingOfNondateColumns={
                                        restrictTableSorting
                                    }
                                >
                                    {documentsColumns}
                                </Table>
                            </TreeTable>
                        </Tabs.Tab> */}
                {/* <Tabs.Tab
                            key="tasks"
                            title={i18next.t('actualTsks')}
                            tabName="actual"
                            name={'tabCaseTasks' + caseId}
                            tabLink={'/cases/case/' + caseId + '/actual'}
                            data-cy={CypressConsts.ttTasks}
                        >
                            <TabsButtonsTable
                                key="buttonsCaseActualTasks"
                                boundTableName={'caseTasks' + caseId}
                            >
                                <TabsButton
                                    key="deal"
                                    icon="icon-pencil-2"
                                    enableOnRow={this.canSolve}
                                    onClick={this.handleTaskSolve}
                                    tooltipCode="ttSolve"
                                >
                                    {i18next.t('solve')}
                                </TabsButton>
                                <TabsButton
                                    key="nested"
                                    icon="icon-window-download-2"
                                    onClick={this.toNestedCase}
                                    enableOnRow={function (row) {
                                        return (
                                            row.itask_subprocess_iproc_id > 0
                                        );
                                    }}
                                    tooltipCode="ttSubprocess"
                                    onMouseWheelClick={this.toNestedCase}
                                >
                                    {i18next.t('nested')}
                                </TabsButton>
                                <TabsButtonMore
                                    key="buttonCaseActualTasksMore"
                                    boundTableName={'caseTasks' + caseId}
                                >
                                    <TabsButton
                                        key="contact"
                                        icon="icon-id-3"
                                        hideTitle={false}
                                        onClick={this.openContactModal.bind(
                                            null,
                                            'caseActualTasks'
                                        )}
                                        tooltipCode="ttContact"
                                        enableOnRow={function (row) {
                                            return row.itask_user_id != null
                                                ? true
                                                : false;
                                        }}
                                    >
                                        {i18next.t('contact')}
                                    </TabsButton>
                                    <TabsButton
                                        key="handover"
                                        icon="icon-user-edit-2"
                                        enableOnRow={function (row) {
                                            return row.c_handover == 1
                                                ? true
                                                : false;
                                        }}
                                        onClick={this.openHandoverModal}
                                        tooltipCode="ttHandover"
                                        hideTitle={false}
                                    >
                                        {i18next.t('handover')}
                                    </TabsButton>
                                    <TabsButton
                                        key="take"
                                        icon="icon-user-checked-2"
                                        hideTitle={false}
                                        enableOnRow={function (row) {
                                            return row.c_take == 1
                                                ? true
                                                : false;
                                        }}
                                        onClick={this.openTakeModal}
                                        tooltipCode="ttTakeTsk"
                                    >
                                        {i18next.t('takeover')}
                                    </TabsButton>
                                    <TabsButton
                                        key="timing"
                                        icon="icon-clock-2"
                                        hideTitle={false}
                                        enableOnRow={function (row) {
                                            return row.c_due == 1
                                                ? true
                                                : false;
                                        }}
                                        onClick={this.openTimingTaskTab}
                                        tooltipCode="ttTiming"
                                    >
                                        {i18next.t('scheduling')}
                                    </TabsButton>
                                </TabsButtonMore>
                            </TabsButtonsTable>
                            <Table
                                key="caseActualTasks"
                                ref="caseActualTasks"
                                name={'caseTasks' + caseId}
                                apiUrl={'/processes/' + caseId + '/tasks'}
                                defaultSort={{
                                    column: 'itask_actual_date_start',
                                    order: 'asc',
                                }}
                                onDoubleClick={this.handleTaskSolve}
                                canOpenNewTab
                                columnsWidthsGroup="caseTasksGroup"
                                allowDefaultAction={this.canSolve}
                                instructions={
                                    this.state.waitingTask
                                        ? i18next.t('waitsForEventTip', {
                                              event: this.state.waitingTask,
                                          })
                                        : null
                                }
                                rowStyleRenderer={function (row) {
                                    return new Date(
                                        Date.parse(row.itask_due_date_finish)
                                    ).setHours(24, 0, 0, 0) < currentDate
                                        ? 'red'
                                        : '';
                                }}
                                solvedTasksCheck
                                disableSortingOfNondateColumns={
                                    restrictTableSorting
                                }
                            >
                                <Column
                                    title={i18next.t('tskName')}
                                    name="itask_name"
                                    type="text"
                                    templInstSearchAndOrder
                                    renderer={(value, row) => {
                                        return checkLangMutationTemplInst(
                                            row,
                                            'task_name'
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('solver')}
                                    name="solver_user_display_name"
                                    type="text"
                                    width="160"
                                />
                                <Column
                                    title={i18next.t('startDate')}
                                    name="itask_actual_date_start"
                                    type="datetime"
                                    width="135"
                                />
                                <Column
                                    title={i18next.t('status')}
                                    name="itask_status"
                                    type="text"
                                    allowInlineFiltering={false}
                                    width="220"
                                    renderer={this.actualTasksStatusRenderer}
                                />
                            </Table>
                        </Tabs.Tab> */}
                {/* <Tabs.Tab
                            key="history"
                            title={i18next.t('history')}
                            tabName="history"
                            name="tabCaseHistory"
                            tabLink={'/cases/case/' + caseId + '/history'}
                            data-cy={CypressConsts.ttHistory}
                        >
                            <TabsButtonsTable
                                key="buttonsCaseHistory"
                                boundTableName={'caseHistory' + caseId}
                            >
                                <TabsButton
                                    key="contact"
                                    icon="icon-id-3"
                                    onClick={this.openContactModal.bind(
                                        null,
                                        'caseHistory'
                                    )}
                                    tooltipCode="ttContact"
                                    enableOnRow={function (row) {
                                        return row.itaskh_user_id != null
                                            ? true
                                            : false;
                                    }}
                                >
                                    {i18next.t('contact')}
                                </TabsButton>
                                <TabsButton
                                    key="detail"
                                    icon="icon-preview-1"
                                    onClick={this.handleTaskDetail}
                                    tooltipCode="ttDetailHistory"
                                    enableOnRow={(row) => {
                                        return row.itask_id !== null;
                                    }}
                                >
                                    {i18next.t('detail')}
                                </TabsButton>
                                {/* <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    enableOnRow={LoggedUserStore.isAdmin()}
                                    onClick={this.editHistoryTask}
                                    tooltipCode="ttEditTskVars"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                                <TabsButton
                                    key="nested"
                                    icon="icon-window-download-2"
                                    onClick={this.toNestedCaseFromHistory}
                                    enableOnRow={function (row) {
                                        return (
                                            row.itaskh_subprocess_iproc_id > 0
                                        );
                                    }}
                                    tooltipCode="ttSubprocess"
                                    onMouseWheelClick={
                                        this.toNestedCaseFromHistory
                                    }
                                >
                                    {i18next.t('nested')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="caseHistory"
                                ref="caseHistory"
                                name={`caseHistory${caseId}`}
                                apiUrl={`/processes/${caseId}/history`}
                                onDoubleClick={this.handleTaskDetail}
                                canOpenNewTab
                                allowDefaultAction={(row) => {
                                    return row.itask_id !== null;
                                }}
                                columnsWidthsGroup="caseHistoryGroup"
                                defaultSort={{
                                    column: 'itaskh_actual_date_finish',
                                    order: 'asc',
                                }}
                                disableSortingOfNondateColumns={
                                    restrictTableSorting
                                }
                            >
                                <Column
                                    title={i18next.t('tskName')}
                                    name="itask_name"
                                    type="text"
                                    templInstSearchAndOrder
                                    renderer={(value, row) => {
                                        return checkLangMutationTemplInst(
                                            row,
                                            'task_name'
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('solvedBy')}
                                    name="itaskh_finished_by_user_name"
                                    type="text"
                                    width="180"
                                    renderer={function (value, row) {
                                        if (
                                            row.itaskh_user_id === null ||
                                            row.itaskh_user_id ==
                                                row.itaskh_finished_by_user_id
                                        ) {
                                            return row.itaskh_finished_by_user_name;
                                        } else if (
                                            row.itaskh_finished_by_user_name &&
                                            row.itaskh_finished_by_user_name.trim()
                                        ) {
                                            return (
                                                row.itaskh_finished_by_user_name +
                                                ' (' +
                                                i18next.t('insteadOf') +
                                                ': ' +
                                                row.itask_user_name +
                                                ')'
                                            );
                                        }
                                    }}
                                />
                                <Column
                                    title={i18next.t('startDate')}
                                    name="itaskh_actual_date_start"
                                    type="datetime"
                                    width="135"
                                />
                                <Column
                                    title={i18next.t('actualEnd')}
                                    name="itaskh_actual_date_finish"
                                    type="datetime"
                                    width="135"
                                />
                                <Column
                                    title={i18next.t('detail')}
                                    name="itaskh_note"
                                    type="text"
                                />
                            </Table>
                        </Tabs.Tab> */}
                {/* <Tabs.Tab
                            key="variables"
                            title={i18next.t('vars')}
                            tabName="variables"
                            name="tabCaseVariables"
                            tabLink={'/cases/case/' + caseId + '/variables'}
                            data-cy={CypressConsts.ttVars}
                        >
                            <TabsButtonsTable
                                key="buttonsCaseVariables"
                                boundTableName={'caseVariables' + caseId}
                            >
                                <TabsButton
                                    key="edit"
                                    icon="icon-file-edit"
                                    enableOnRow={LoggedUserStore.isAdmin()}
                                    onClick={this.openEditVariableModal}
                                    tooltipCode="ttEdit"
                                >
                                    {i18next.t('edit')}
                                </TabsButton>
                            </TabsButtonsTable>
                            <Table
                                key="caseVariables"
                                ref="caseVariables"
                                name={`caseVariables${caseId}`}
                                apiUrl={`/processes/${caseId}/variables`}
                                onDoubleClick={
                                    LoggedUserStore.isAdmin()
                                        ? this.openEditVariableModal
                                        : () => {}
                                }
                                canOpenNewTab={false}
                                defaultSort={{
                                    column: 'ivar_name',
                                    order: 'asc',
                                }}
                                defaultSortLangMutation
                                columnsWidthsGroup="caseVariablesGroup"
                                defaultFilter={
                                    !LoggedUserStore.isAdmin()
                                        ? 'ivar_name<nlike>"_%"'
                                        : ''
                                }
                            >
                                <Column
                                    title={i18next.t('name')}
                                    name="ivar_name"
                                    type="text"
                                    width="40%"
                                    colWithLangMutation
                                    renderer={(value, row) => {
                                        return checkLangMutation(
                                            row,
                                            'ivar_name'
                                        );
                                    }}
                                />
                                <Column
                                    title={i18next.t('value')}
                                    name="ivar_value"
                                    type="miscellaneous"
                                    allowSorting={false}
                                    width="60%"
                                    renderer={this.checkValLangMutationRenderer}
                                    filterRenderer={(value, columnName) => {
                                        return `ivar_text_value<like>"%${value}%"<or>ivar_number_value<like>"%${value}%"`;
                                    }}
                                />
                            </Table>
                        </Tabs.Tab> */}
                {/* {LoggedUserStore.isAdmin() && (
                            <Tabs.Tab
                                key="logs"
                                title={i18next.t('logs')}
                                tabName="logs"
                                tabLink={`/cases/case/${caseId}/logs`}
                                name={`tabCaseLogs-${caseId}`}
                                labelColor="red"
                                data-cy={CypressConsts.ttLogs}
                            > */}

                {/* </Tabs.Tab>
                        )} */}
                {/* </Tabs> */}
                {/* <ConfirmModal
                    isOpen={this.state.zeaRemoveModalIsOpen}
                    width="tiny"
                    text={i18next.t('confirmRemoveFromZeaDialog', {
                        variable: i18next.t('documentVar'),
                    })}
                    onClose={this.closeZeaDeleteModal}
                    onConfirm={this.removeFromZea}
                /> */}
                <ModalCaseContact
                    isOpen={this.state.caseContactModalIsOpen}
                    contactId={this.state.contactId}
                    onClose={this.closeContactModal}
                />
                {/* <ConfirmModal
                    isOpen={this.state.deleteModalIsOpen}
                    width="tiny"
                    text={i18next.t('confirmAttachDeletion')}
                    onClose={this.closeDeleteModal}
                    onConfirm={this.deleteAttachment}
                /> */}
                <ModalConfirm
                    isOpen={this.state.takeModalIsOpen}
                    text={i18next.t('confirmTakeoverTsk')}
                    onClose={this.closeTakeModal}
                    onConfirm={this.takeActual}
                />
                {/* <ConfirmModal
                    isOpen={this.state.takeModalIsOpen}
                    width="tiny"
                    text={i18next.t('confirmTakeoverTsk')}
                    onClose={this.closeTakeModal}
                    onConfirm={this.takeActual}
                /> */}
                {/* {this.state.addAttachmentModalIsOpen && (
                    <AddAttachmentModal
                        isOpen={this.state.addAttachmentModalIsOpen}
                        width="medium"
                        iprocId={caseId}
                        onClose={this.closeAddAttachmentModal}
                        parent={this}
                        tableRef="caseAttachment"
                    />
                )} */}
                {/* {this.state.modalEditAttachment && (
                    <EditAttachmentModal
                        width="large"
                        isOpen={this.state.modalEditAttachment}
                        onClose={this.closeModalEditAttachment}
                        caseId={caseId}
                        attachmentId={this.state.attachmentId}
                        attachment={this.state.attachment}
                        parent={this}
                        tableRef="caseAttachment"
                    />
                )} */}
                {/* {this.state.revisionAttachmentModalIsOpen && (
                    <RevisionAttachmentModal
                        isOpen={this.state.revisionAttachmentModalIsOpen}
                        width="medium"
                        iprocId={caseId}
                        attachmentId={this.state.attachmentId}
                        onClose={this.closeRevisionAttachmentModal}
                        parent={this}
                        tableRef="caseAttachment"
                    />
                )} */}
                {/* <ConfirmModal
                    isOpen={this.state.deleteModalIsOpen}
                    width="tiny"
                    text={i18next.t('confirmAttachDeletion')}
                    onClose={this.closeDeleteModal}
                    onConfirm={this.deleteAttachment}
                /> */}
                {/* {this.state.printCaseModalState && (
                    <PrintCaseModal
                        width="medium"
                        isOpen={this.state.printCaseModalState}
                        onClose={this.closeCaseModal}
                        id={this.state.id}
                        fileName={this.state.heading}
                        parent={this}
                        caseId={caseId}
                    />
                )} */}
                {this.state.printCaseModalState && (
                    <ModalPrintCase
                        isOpen={this.state.printCaseModalState}
                        onClose={this.closeCaseModal}
                        id={this.state.id}
                        fileName={this.state.heading}
                        parent={this}
                        caseId={caseId}
                        archived={this.state.archived}
                        entityName="case"
                    />
                )}
                {this.state.editVaribleModalIsOpen && (
                    <CaseEditVariableModal
                        width="medium"
                        isOpen={this.state.editVaribleModalIsOpen}
                        onClose={this.closeEditVariableModal}
                        variableId={this.state.variableId}
                        caseId={this.state.caseId}
                        parent={this}
                        variables={this.state.variables}
                    />
                )}
                {this.state.casesEventModalIsOpen && (
                    <ModalEvent
                        width="medium"
                        isOpen={this.state.casesEventModalIsOpen}
                        onClose={this.closeEventModal}
                        caseId={this.state.caseId}
                    />
                    // <CasesEventModal
                    //     width="medium"
                    //     isOpen={this.state.casesEventModalIsOpen}
                    //     onClose={this.closeEventModal}
                    //     caseId={this.state.caseId}
                    //     parent={this}
                    // />
                )}
                {/* {this.state.handoverModalIsOpen && (
                    <TaskHandoverModal
                        taskId={this.state.taskId}
                        isOpen={this.state.handoverModalIsOpen}
                        width="small"
                        onClose={this.closeHandoverModal}
                        parent={this}
                        tableRef="caseActualTasks"
                        handoverType="handover"
                        handoverNoLimits={this.state.handoverNoLimits}
                        openConfirmModal={this.openConfirmModal}
                        closeConfirmModal={this.closeConfirmHandoverModal}
                    />
                )} */}
                {this.state.handoverModalIsOpen && (
                    <ModalTaskHandover
                        taskId={this.state.taskId}
                        isOpen={this.state.handoverModalIsOpen}
                        onClose={this.closeHandoverModal}
                        parent={this}
                        handoverNoLimits={this.state.handoverNoLimits}
                        openConfirmModal={this.openConfirmModal}
                        closeConfirmModal={this.closeConfirmHandoverModal}
                    />
                )}

                <ModalConfirm
                    isOpen={this.state.confirmHandoverModalIsOpen}
                    text={i18next.t('confirmSaveChanges')}
                    onClose={this.closeConfirmHandoverModal}
                    onConfirm={this.confirmHandover}
                    onDiscard={this.cancelHandover}
                />
                {/* <ConfirmModal
                    isOpen={this.state.confirmHandoverModalIsOpen}
                    width="tiny"
                    text={i18next.t('confirmSaveChanges')}
                    onClose={this.closeConfirmHandoverModal}
                    onConfirm={this.confirmHandover}
                    onDiscard={this.cancelHandover}
                /> */}
                {/* {this.state.signModalIsOpen && (
                    <ZeaSignModal
                        isOpen={this.state.signModalIsOpen}
                        onClose={this.closeSignModal}
                        documentId={this.state.attachmentId}
                        tableRef="caseAttachment"
                        parent={this}
                        caseId={this.props.match.params.caseId}
                        addToZea={this.state.signModalAction === 'addToZea'}
                        documentSrc={this.state.documentSrc}
                        documentFileType={this.state.documentFileType}
                    />
                )} */}
                {this.state.previewModalIsOpen && (
                    <FilePreviewModal
                        isOpen={this.state.previewModalIsOpen}
                        width="medium"
                        onClose={this.closeFilePreviewModal}
                        file={this.state.filePreview}
                        downloadFile={this.downloadFile}
                        draggable
                    />
                )}
                {/* {this.state.confirmModalIsOpen && (
                    <ConfirmModal
                        text={i18next.t('confirmSaveNote')}
                        width="tiny"
                        isOpen={this.state.confirmModalIsOpen}
                        onClose={this.closeConfirmModal}
                        onConfirm={this.confirmSaving}
                        onDiscard={this.cancelSaving}
                    />
                )} */}
                {this.state.serviceModalIsOpen && (
                    <ServiceOperationsModal
                        isOpen={this.state.serviceModalIsOpen}
                        onClose={this.closeServiceModal}
                        onSave={this.openConfirmChangesModal}
                        recordsType="cases"
                        selectedIds={this.props.match.params.caseId}
                    />
                )}
                {this.state.confirmChangesModalIsOpen && (
                    <ModalConfirm
                        text={i18next.t('confirmSaveChanges')}
                        isOpen={this.state.confirmChangesModalIsOpen}
                        onClose={this.closeConfirmChangesModal}
                        onConfirm={this.confirmChanges}
                    />
                    // <ConfirmModal
                    //     text={i18next.t('confirmSaveChanges')}
                    //     isOpen={this.state.confirmChangesModalIsOpen}
                    //     width="tiny"
                    //     onClose={this.closeConfirmChangesModal}
                    //     onConfirm={this.confirmChanges}
                    // />
                )}
                {/*  </TabsWrapper> */}
                {/*  </DocumentTitle> */}
                {/*  </CaseDetailsLayout> */}
            </>
        );
    },
});

export default Case;
