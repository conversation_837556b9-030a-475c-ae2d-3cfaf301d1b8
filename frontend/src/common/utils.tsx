/* eslint-disable */
// @ts-nocheck
import i18next from 'i18next';
import dayjs from 'dayjs';
import browserHistory from './history';
import ApiRequest from '../api/apiRequest';
import jschardet from 'jschardet';
import { isEmpty } from 'lodash';
import DOMPurify from 'dompurify';
import _ from 'lodash';
import moment from 'moment-timezone';
import React from 'react';
import sanitizeHtml from 'sanitize-html';
import LoggedUserStore from '../flux/loggedUser.store';
import LoggedUserActions from '../flux/loggedUser.actions';
import AlertsActions from '../components/alerts/alerts.actions';
import EmptyComponent from '../components/form/emptyComponent.react';
import mammoth from 'mammoth/mammoth.browser';
import { saveAs } from '../../assets/libs/filesaver';
import { getNavigate } from '../components5.0/routes/hooks/use-router';

/** Vytvořen<PERSON> přátelského URL
 * @param string řetězec, ze kterého se má vytvořit URL
 * @return string řetězec obsahující pouze čísla, znaky bez diakritiky, podtržítko a pomlčku
 * @copyright Jakub Vrána, http://php.vrana.cz/
 */
export function makeUrl(s) {
    const nodiac = {
        á: 'a',
        č: 'c',
        ď: 'd',
        é: 'e',
        ě: 'e',
        í: 'i',
        ň: 'n',
        ó: 'o',
        ř: 'r',
        š: 's',
        ť: 't',
        ú: 'u',
        ů: 'u',
        ý: 'y',
        ž: 'z',
    };
    s = s.toLowerCase();
    let s2 = '';
    for (let i = 0; i < s.length; i += 1) {
        s2 += typeof nodiac[s.charAt(i)] !== 'undefined' ? nodiac[s.charAt(i)] : s.charAt(i);
    }
    return s2.replace(/[^a-z0-9_]+/g, '-').replace(/^-|-$/g, '');
}

/**
 * Generates a GUID string.
 * @returns {String} The generated GUID.
 * @example af8a8416-6e18-a307-bd9c-f2c947bbb3aa
 * <AUTHOR> Meltser (<EMAIL>).
 * @link http://slavik.meltser.info/?p=142
 */
export function guid() {
    function _p8(s) {
        const p = `${Math.random().toString(16)}000000000`.substr(2, 8);
        return s ? `-${p.substr(0, 4)}-${p.substr(4, 4)}` : p;
    }
    return _p8() + _p8(true) + _p8(true) + _p8();
}

/**
 * Check if given object is empty
 * @param  {object} obj
 * @return {bool}
 */
export function objIsEmpty(obj) {
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            return false;
        }
    }
    return true;
}

/**
 * Returns value of a property of given object on the specific path.
 * @returns value of an object property
 * @example alert(deep_value(obj, 'process.sub.name'));
 * @link http://jsfiddle.net/tadeck/5Pt2q/13/
 */

export function deepValue(obj, path) {
    for (var i = 0, path = path.split('.'), len = path.length; i < len; i += 1) {
        obj = obj[path[i]];
    }
    return obj;
}

/**
 * Returns decoded html.
 * @returns decoded html
 * @example alert(decodeHtml("Entity:&amp;nbsp;Bad attempt at XSS:<script>alert('new\nline?')</script><br>"));
 * @link http://jsfiddle.net/k65s3/
 */
export function decodeHtml(html) {
    const txt = document.createElement('textarea');
    txt.innerHTML = html;
    return txt.value;
}

/**
 * Returns parameter from url query.
 * @param {string} name searched parameter
 * @param {string} urlSearch 'search' part of window.location
 * @param {boolean} decode
 * @returns {string}
 */
export function getParameterByName(name, urlSearch, decode? = true) {
    const match = new RegExp(`[?&]${name}=([^&]*)`).exec(urlSearch);
    // return match && decodeURIComponent(match[1].replace(/\+/g, ' '));

    if (decode) {
        return match && decodeURIComponent(match[1].replace(/\+/g, ' '));
    }

    return match && match[1].replace(/\+/g, ' ');
}

/**
 * Returns html <a> tag(s) with link(s) from the value.
 * @param value is a string containing url(s)
 * @returns {string}
 */
export function convertToUrl(value) {
    let aTag = value;
    if (typeof value === 'number') aTag = value.toString();
    if (typeof aTag !== 'string') {
        return aTag;
    }
    // do not convert links in quotation marks (<img src="http://.." />, ...)
    aTag = aTag.replace(
        /((?:^|[^"'>])(https?|ftps?|file):\/\/[-A-Ž0-9+&@#\/\\%?=~_|!:,.;]*[-A-Ž0-9+&@#\/%=~_|])|((?:^|[^:\S])(\/\/|\\\\)[-A-Ž0-9+&@#\/\\%?=~_|!:,.;]*[-A-Ž0-9+&@#\/%=~_|])/gi,
        (link) => {
            return `${_.startsWith(link, ' ') ? ' ' : ''}<a target="_blank" href="${link.trim()}">${link.trim()}</a>`;
        },
    );

    return aTag;
}

/**
 * check if object property exist in user language mutation
 * @param  {object} i is item in object
 * @param  {string} key is object key
 * @param  {bool} useDefaultVal - if true and ttask value is null return itask value or vice versa
 *                - ttask_description_en == null && ttask_description == null -> return itask_description
 * @param  {string} suffix string after lang (iproc_name_cs_original)
 * @param  {string} appendDefaultChar behind this character, the default value is added to parentheses ( name cs\n(name) )
 * @return {string} object value
 */
export function checkLangMutation(
    i,
    key,
    useDefaultVal? = false,
    suffix? = '',
    appendDefaultChar? = false,
) {
    const userLang =
        typeof LoggedUserStore.getState === 'function'
            ? LoggedUserStore.getState().userLanguage
            : 'cs'; // due to tests error

    if (!_.isEmpty(i[`${key}_${userLang}${suffix}`])) {
        let ret = i[`${key}_${userLang}${suffix}`];

        if (appendDefaultChar) {
            ret += `${appendDefaultChar}(${i[key]})`;
        }

        return ret;
    }
    if (useDefaultVal === true && _.isEmpty(i[key])) {
        return i[key.replace(key.charAt(0), key.charAt(0) === 't' ? 'i' : 't')]; // ttask -> itask || itask -> ttask
    }
    return i[key];
}

/**
 * returns translated template property, if exist, otherwise returns instance property
 * @param  {object} i
 * @param  {string} key is object property
 * @return {string}
 */
export function checkLangMutationTemplInst(i, key) {
    const userLang =
        typeof LoggedUserStore.getState === 'function'
            ? LoggedUserStore.getState().userLanguage
            : 'cs'; // due to tests error

    if (!_.isEmpty(i[`t${key}_${userLang}`])) {
        return i[`t${key}_${userLang}`];
    }

    return i[`i${key}`];
}

/**
 * check if iVarLov language mutation exist in variable object
 * @param  {object} varObj
 * @param  {string} type - i/t = ivar/tvar
 * @return {array} iVarLov with language mutations or ivarLov
 */
export function checkVarLovLangMutation(varObj, type? = 'i') {
    const userLang =
        typeof LoggedUserStore.getState === 'function'
            ? LoggedUserStore.getState().userLanguage
            : 'cs'; // due to tests error

    if (
        varObj[`${type}var_type`] === 'LT' &&
        _.has(varObj, `${type}var_lov_${userLang}`) &&
        !_.isEmpty(varObj[`${type}var_lov_${userLang}`])
    ) {
        return varObj[`${type}var_lov`].map((lov) => {
            const findTranslation = _.find(varObj[`${type}var_lov_${userLang}`], [
                'value',
                lov.value,
            ]);

            return {
                value: lov.value,
                title: findTranslation ? findTranslation.title : lov.title,
            };
        });
    }

    return varObj[`${type}var_lov`];
}

/**
 * check if varLov language mutation contains translated value
 * @param  {object} varObj
 * @param  {any} value
 * @param  {string} type - i/t = ivar/tvar
 * @return {any} translated value or value
 */
export function lovValueCheckLangMutation(varObj, value, type? = 'i') {
    const userLang =
        typeof LoggedUserStore.getState === 'function'
            ? LoggedUserStore.getState().userLanguage
            : 'cs'; // due to tests error

    if (
        varObj[`${type}var_type`] === 'LT' &&
        _.has(varObj, `${type}var_lov_${userLang}`) &&
        !_.isEmpty(varObj[`${type}var_lov_${userLang}`])
    ) {
        const findTranslation = _.find(varObj[`${type}var_lov_${userLang}`], ['value', value]);

        return findTranslation ? findTranslation.title : value;
    }

    return value;
}

/**
 * returns value of translation property, if exist, otherwise returns default property value
 * @param  {object} i
 * @param  {string} defProp Default property
 * @param  {bool} trProp Translation property
 * @return {string} Translated or default value
 */
export function checkLangMutationDiffNames(i, defProp, trProp? = 'cs_name') {
    const userLang =
        typeof LoggedUserStore.getState === 'function'
            ? LoggedUserStore.getState().userLanguage
            : 'cs'; // due to tests error

    if (!_.isEmpty(i[`${trProp}_${userLang}`])) {
        return i[`${trProp}_${userLang}`];
    }

    return i[defProp];
}

/**
 * Downloads and decodes a file.
 * @param {string} value is a string containing encoded file
 * @param {string} fileName is name of downloaded file
 * @param {string} fileType is optional file type
 * @link http://stackoverflow.com/questions/16245767/creating-a-blob-from-a-base64-string-in-javascript
 */
export function downloadEncoded(value, fileName, fileType) {
    const { saveAs } = require('../../assets/libs/filesaver');

    const blob = createBlob(value, fileType);

    saveAs(blob, fileName);
}

export function createBlob(value, fileType?: string) {
    const byteCharacters = atob(value);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i += 1) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    let blob;

    if (fileType) {
        blob = new Blob([byteArray], { type: fileType });
    } else {
        blob = new Blob([byteArray]);
    }

    return blob;
}

/**
 * Check if value exist in options
 * @param {number|string|null|undefined|object} value
 * @param {array} options
 * @return {number|string|null} if value exist in options return value else return null
 */
export function checkValueInOptions(value, options) {
    if (typeof value !== 'undefined' && value !== null) {
        let option;
        if (typeof value === 'object') {
            option = _.find(options, ['value', value.value]);
        } else {
            option = _.find(options, ['value', value]);
        }

        if (typeof option !== 'undefined') {
            option.title = option.title.trim();
        }

        if (
            typeof value === 'object' &&
            typeof option !== 'undefined' &&
            _.isEqual(option, value)
        ) {
            return value.value;
        }
        if (typeof value !== 'object' && typeof option !== 'undefined') {
            return value;
        }
        return null;
    }
    if (value === null) {
        return null;
    }
}

/**
 * Check if option exist by title
 * @param {string} title
 * @param {array} options
 * @return {object|null} returns option object or null
 */
export function checkValueInOptionsByTitle(title, options) {
    if (typeof title !== 'undefined' && title !== null) {
        const trimmedOptions = options.map((opt) => {
            return { title: opt.title.trim(), value: opt.value };
        });

        const option = _.find(trimmedOptions, ['title', title]);

        if (typeof option !== 'undefined') {
            option.title = option.title.trim();
            return option;
        }
        return null;
    }
    return null;
}

/**
 * Adjusts number value in case of incorrect rounding.
 * @param {number} num
 * @param scale
 * @return {number} fixed number
 */
export function roundNumber(num, scale) {
    const factor = Math.pow(10, scale);
    const correction = 1 / (factor * 1000);
    return Math.round((num + correction) * factor) / factor;
}

/**
 * Check user parameters (userFilters, tablesColumnsWidths).
 * If user parameter contains item which is not in the idsArr, item will be removed.
 * Or if groupUsed == true, remove all old items where columnsWidthsGroup is now used.
 * @param {array} idsArr
 * @param {string} type (userFilters, tablesColumnsWidths)
 * @param {string|array} toCheck (array for columnsWidths)
 * @param {bool} [groupUsed]
 */
export function checkUserParams(idsArr, type, toCheck, groupUsed) {
    let toDelete = false;

    if (!_.isEmpty(idsArr) || groupUsed === true) {
        if (type == 'userFilters') {
            const { userFilters } = LoggedUserStore.getState();

            for (const key in userFilters) {
                // cv1669 -> cv
                if (
                    userFilters.hasOwnProperty(key) &&
                    key.substring(0, toCheck.length) == toCheck
                ) {
                    // cv1669 -> 1669
                    const checkId = key.substring(toCheck.length, key.length);

                    if (idsArr.indexOf(checkId) == -1 && idsArr.indexOf(Number(checkId)) == -1) {
                        toDelete = true;

                        delete userFilters[key];
                    }
                }
            }

            if (toDelete) {
                LoggedUserActions.saveUserFilters(userFilters);
            }
        } else if (type == 'columnsWidths') {
            let { tablesColumnsWidths } = LoggedUserStore.getState();

            const removeItems = function (table) {
                const tablesColumnsWidthsClone = _.cloneDeep(tablesColumnsWidths);
                const index = _.findIndex(tablesColumnsWidths, (t) => {
                    return table == t.table;
                });

                if (index != -1) {
                    toDelete = true;
                    tablesColumnsWidthsClone.splice(index, 1);
                    tablesColumnsWidths = tablesColumnsWidthsClone;
                }
            };

            tablesColumnsWidths.forEach((t) => {
                const { table } = t;

                if (!Array.isArray(toCheck)) {
                    toCheck = [toCheck];
                }

                toCheck.forEach((check) => {
                    if (table.substring(0, check.length) == check) {
                        // delete all old items where columnsWidthsGroup is now used
                        if (groupUsed === true) {
                            if (t.table.indexOf('Group') == -1) {
                                removeItems(table);
                            }
                        } else {
                            // overviewsCases1519 -> 1519
                            const checkId = table.substring(check.length, table.length);

                            if (
                                idsArr.indexOf(checkId) == -1 &&
                                idsArr.indexOf(Number(checkId)) == -1
                            ) {
                                removeItems(table);
                            }
                        }
                    }
                });
            });

            if (toDelete) {
                LoggedUserActions.saveColumnsWidthsArr(tablesColumnsWidths);
                LoggedUserActions.saveTablesColumns(tablesColumnsWidths);
            }
        }
    }
}

/**
 * Sorts array of objects by given property
 * @param {Array} arr
 * @param {string} sortBy
 * @param {string} orSortBy
 * @return {array} sorted array
 */
export function sortArrayBy(arr, sortBy, orSortBy?) {
    return arr.sort((a, b) => {
        let aSort = a[sortBy] || a[orSortBy];
        let bSort = b[sortBy] || b[orSortBy];
        if (!aSort) {
            aSort = '';
        }
        if (!bSort) {
            bSort = '';
        }

        return aSort.localeCompare(bSort);
    });
}

/**
 * Sorts array
 * @param {Array} arr
 * @return {array} sorted array
 */
export function sortArray(arr) {
    return arr.sort((a, b) => {
        let aSort = a;
        let bSort = b;
        if (!aSort) {
            aSort = '';
        }
        if (!bSort) {
            bSort = '';
        }

        return aSort.localeCompare(bSort);
    });
}

export function sortNumbersArray(arr) {
    return arr.sort((a, b) => {
        return a - b;
    });
}

export function isMobile(a) {
    if (
        /(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(
            a,
        ) ||
        /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(
            a.substr(0, 4),
        )
    ) {
        return true;
    }

    return (
        /android|ipad|iphone|ipod|windows phone/i.test(navigator.userAgent) ||
        /ipad|iphone|ipod/i.test(navigator.platform) ||
        (/mac/i.test(navigator.platform) &&
            !!navigator.maxTouchPoints &&
            navigator.maxTouchPoints > 1) // fix - iPad
    );
}

/**
 * Parse guide JSON and run (hopscotch guides/tours)
 * @param {object} guideToRun
 * @param {bool} parse
 * @param {object} addOptions
 */
export function runGuide(guideToRun, parse? = true, addOptions? = null) {
    // State of hopscotch remained hanging
    // Session Storage - hopscotch.tour.state
    if (hopscotch.getState()) {
        hopscotch.endTour();
    }

    try {
        if (guideToRun.guide_redirect) {
            browserHistory.push(guideToRun.guide_redirect);
        }

        let guideParsed;
        if (parse) {
            guideParsed = JSON.parse(guideToRun.guide);
        } else {
            guideParsed = guideToRun.guide;
        }

        const guideObj = {
            id: `guide-${guideToRun.id}`,
            i18n: {
                nextBtn: i18next.t('next'),
                prevBtn: i18next.t('previous'),
                doneBtn: i18next.t('done'),
                skipBtn: i18next.t('skip'),
                closeTooltip: i18next.t('close'),
            },
        };

        if (addOptions) {
            _.assign(guideObj, addOptions);
        }

        const guide = _.assign(guideObj, guideParsed);

        // running without "steps" jam the hopscotch
        if (!guide.hasOwnProperty('steps') || _.isEmpty(guide.steps)) {
            AlertsActions.addAlert({
                type: 'alert',
                message: `${i18next.t('guide')}: ${i18next.t('alrInvalidData')}`,
            });
        } else {
            hopscotch.configure({ showPrevButton: true });
            hopscotch.startTour(guide);
        }
    } catch (error) {
        AlertsActions.addAlert({
            type: 'alert',
            message: `${i18next.t('guide')}: ${i18next.t('alrInvalidData')}`,
            serverError: error,
        });
    }
}

/**
 * getCustomizationProps
 * @param {object} metaC - tvarMeta.customization
 * @return {object} object of customization properties
 */
export function getCustomizationProps(metaC) {
    const customization = {};

    for (const key in metaC) {
        if ({}.hasOwnProperty.call(metaC, key)) {
            if (key === 'fontSize') {
                customization[key] = metaC[key] === 'L' ? '0.875rem' : '0.625rem';
            } else if (key === 'elementColor') {
                customization.bgColor = metaC[key];
            } else {
                customization[key] = metaC[key];
            }
        }
    }

    return customization;
}

export function recursivelyLoadFolders(arr, item) {
    const repeat = function (num) {
        return new Array(isNaN(num) ? 1 : ++num).join('\u00A0\u00A0\u00A0\u00A0');
    };
    const recursion = function (item) {
        const useSpace = repeat(item.tree_index + 1);
        if (typeof item.children !== 'undefined') {
            item.children.forEach((item) => {
                const obj = {
                    value: item.id,
                    title: useSpace + (item.label || item.name),
                    parentId: item.parent_id,
                };
                arr.push(obj);
                if (typeof item.tree_index !== 'undefined') {
                    recursion(item);
                }
            });
        }
    };
    recursion(item);
}

// https://github.com/apostrophecms/sanitize-html#readme
/**
 * htmlSanitize
 * @param  {string} html
 * @param  {number} level of sanitize
 * @param  {bool} noEscAmpersand - &amp; -> &
 * @return {string}
 */
export function utilSanitizeHtml(html, level? = 2, noEscAmpersand? = true) {
    // level 1
    let options = {
        allowedTags: [],
        allowedAttributes: {},
        disallowedTagsMode: 'recursiveEscape',
    };

    if (level === 2) {
        options = {
            allowedTags: [
                'h1',
                'h2',
                'h3',
                'h4',
                'h5',
                'h6',
                'blockquote',
                'p',
                'a',
                'ul',
                'ol',
                'nl',
                'li',
                'b',
                'i',
                'strong',
                'em',
                'strike',
                'code',
                'hr',
                'br',
                'div',
                'span',
                'table',
                'thead',
                'caption',
                'tbody',
                'tr',
                'th',
                'td',
                'pre',
                'span',
                'img',
            ],
            allowedAttributes: {
                a: ['href', 'name', 'target'],
                img: ['src'], // only src="data:" and src="/local" are allowed
                '*': ['style'],
            },
            selfClosing: ['img', 'br', 'hr', 'area', 'base', 'basefont', 'input', 'link', 'meta'],
            // URL schemes we permit
            allowedSchemes: ['http', 'https', 'ftp', 'mailto'],
            allowedSchemesByTag: { img: ['data'] },
            allowedSchemesAppliedToAttributes: ['href', 'src', 'cite'],
            allowProtocolRelative: true,
            allowedClasses: { '*': ['*'] },
            disallowedTagsMode: 'recursiveEscape',
        };
    }

    let sanitized = sanitizeHtml(html, options);

    if (noEscAmpersand) {
        sanitized = sanitized.replace(/&amp;/g, '&');
    }

    return sanitized;
}

export function sanitizeVariable(varData, prefix) {
    if (
        ['T', 'LT', 'DR', 'DT'].includes(varData[`${prefix}_type`]) ||
        (varData[`${prefix}_type`] === 'DL' && varData[`${prefix}_attribute`] === null)
    ) {
        if (varData[`${prefix}_value`]) {
            if (typeof varData[`${prefix}_value`] === 'string') {
                varData[`${prefix}_value`] = utilSanitizeHtml(varData[`${prefix}_value`]);
            } else if (Array.isArray(varData[`${prefix}_value`])) {
                varData[`${prefix}_value`].map((val) => utilSanitizeHtml(val));
            } else if (typeof varData[`${prefix}_value`] === 'object') {
                // DR
                const obj = varData[`${prefix}_value`];
                const keys = Object.keys(obj);
                // Iterate DR rows/cols.
                for (let i = 0; i < keys.length; i += 1) {
                    const drRow = obj[keys[i]];
                    if (Array.isArray(drRow) && drRow.length > 0) {
                        // Each item in row.
                        for (let j = 0; j < drRow.length; j += 1) {
                            const cell = drRow[j];
                            if (typeof cell === 'string') {
                                drRow[j] = utilSanitizeHtml(cell);
                            }
                        }
                    }
                }
            }
        }

        // /tasks/id/variables/history
        if (Array.isArray(varData.ivarh_value)) {
            varData.ivarh_value.map((val) => {
                val.ivarh_value = utilSanitizeHtml(val.ivarh_value);
            });
        }
        if (varData.ivar_text_value) {
            varData.ivar_text_value = utilSanitizeHtml(varData.ivar_text_value);
        }
        if (varData.ivar_big_value) {
            varData.ivar_big_value = utilSanitizeHtml(varData.ivar_big_value);
        }
    }
}

/**
 * Returns task component
 * @param {number|string} tTaskId - tvarMeta.customization
 * @param {object} item - task variable
 * @param {function} componentsCreate
 * @param {bool} addItemValidations
 * @return {object} task component
 */
export function getTaskComponent(tTaskId, item, componentsCreate, addItemValidations? = false) {
    const taskItem = item;

    if (taskItem.ivar_hidden !== true) {
        if (addItemValidations) {
            taskItem.validations = taskItem.validations || {};
            taskItem.validationErrors = taskItem.validationErrors || {};
        }

        // customization
        let tvarMeta = {};

        try {
            tvarMeta = JSON.parse(taskItem.tvar_meta || '{}');
        } catch (e) {
            tvarMeta = taskItem.tvar_meta;
        }

        if ({}.hasOwnProperty.call(tvarMeta, 'customization')) {
            const customizationProps = getCustomizationProps(tvarMeta.customization);

            if ({}.hasOwnProperty.call(customizationProps, 'tasks')) {
                if (customizationProps.tasks.indexOf(String(tTaskId)) !== -1) {
                    return React.cloneElement(componentsCreate(taskItem), customizationProps);
                }

                return componentsCreate(taskItem);
            }

            return React.cloneElement(componentsCreate(taskItem), customizationProps);
        }

        return componentsCreate(taskItem);
    }

    return (
        <EmptyComponent
            key={taskItem.id || taskItem.ivar_id}
            tvarId={taskItem.tvar_id}
            componentStyle={{ display: 'none' }}
            hidden
            axisX={item.axis_x}
            axisY={item.axis_y}
        />
    );
}

export function formatDate(date, withTime? = false) {
    const language = config.dateTimeFormat || LoggedUserStore.getState().userLocale;
    const dateFormat = {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
    };

    if (withTime) {
        dateFormat.hour = 'numeric';
        dateFormat.minute = 'numeric';
    }

    const nDate = new Date(date);
    return Intl.DateTimeFormat(language, dateFormat).format(Date.parse(nDate));
}

// get variable value for new prints
export function getVarValue(variables, varName) {
    if (!_.has(variables, varName)) {
        return `!variable "${varName}" not found!`;
    }

    const userLang = LoggedUserStore.getState().userLanguage.toLocaleUpperCase();
    const langValue = variables[`${varName}_${userLang}`];
    let value;

    if (typeof langValue !== 'undefined' && langValue !== null) {
        value = langValue;
    } else {
        value = variables[varName];
    }

    if (Array.isArray(value)) {
        value = value.join(', ');
    }

    return value;
}

// replace {variables} in url
export function replaceVariablesInUrl(url, component): Promise<any> {
    return new Promise((resolve, reject) => {
        const variablesNames = [];
        let copiedUrl = url;

        if (copiedUrl && copiedUrl.indexOf('{') !== -1 && copiedUrl.indexOf('}') !== -1) {
            // TODO - for old selectBox toggle - (because of double call loadDataService in dynamic rows)
            // TODO
            //component.setState({ variableInUrl: true });

            // get all {variables} in url
            const getVars = () => {
                const taskVarName = copiedUrl.substring(
                    copiedUrl.indexOf('{') + 1,
                    copiedUrl.indexOf('}'),
                );
                variablesNames.push(taskVarName);
                copiedUrl = copiedUrl.replace(new RegExp(`{${taskVarName}}`), '');

                if (copiedUrl.indexOf('{') !== -1 && copiedUrl.indexOf('}') !== -1) {
                    getVars();
                } else {
                    return variablesNames;
                }
            };

            getVars();

            /*if (typeof component.props.findTaskVarValue === 'function') {
                component.props
                    .findTaskVarValue(variablesNames)
                    .then((values) => {
                        let replacedUrl = url;
                        for (let i = 0; i < values.length; i += 1) {
                            replacedUrl = replacedUrl.replace(
                                new RegExp(`{${variablesNames[i]}}`),
                                values[i],
                            );
                        }
                        resolve(replacedUrl);
                    })
                    .catch((err) => {
                        reject(err);
                    });
            } else {
                // template
                resolve(url);
            }*/
        } else {
            resolve(url);
        }
    });
}

export function dateToServerZoneIso(date) {
    const { serverTimezone } = LoggedUserStore.getState();
    const userTimezone = moment.tz.guess();
    let retDate = date;

    // eslint-disable-next-line eqeqeq
    if (new Date(date) == 'Invalid Date') {
        return date;
    }

    if (serverTimezone !== userTimezone) {
        const offsetUser = moment.tz(retDate, userTimezone).utcOffset();
        const offsetServer = moment.tz(retDate, serverTimezone).utcOffset();
        retDate = moment(retDate)
            .utcOffset(offsetUser - offsetServer)
            .format('YYYY-MM-DDTHH:mm:ss.SSS\\Z');
    }

    return retDate;
}

export function dateToUserZoneIso(date) {
    const { serverTimezone } = LoggedUserStore.getState();
    const userTimezone = moment.tz.guess();
    let retDate = date;

    // eslint-disable-next-line eqeqeq
    if (new Date(date) == 'Invalid Date') {
        return date;
    }

    // eslint-disable-next-line eqeqeq
    if (serverTimezone !== userTimezone) {
        const offsetUser = moment.tz(retDate, userTimezone).utcOffset();
        const offsetServer = moment.tz(retDate, serverTimezone).utcOffset();
        retDate = moment(retDate)
            .utcOffset(offsetServer - offsetUser)
            .format('YYYY-MM-DDTHH:mm:ss.SSS\\Z');
    }

    return retDate;
}

export function dateToServerZoneIsoDayJs(date) {
    const { serverTimezone } = LoggedUserStore.getState();
    const userTimezone = dayjs.tz.guess();
    let retDate = date;

    // eslint-disable-next-line eqeqeq
    if (new Date(date).toString() === 'Invalid Date') {
        return date;
    }

    if (serverTimezone !== userTimezone) {
        const offsetUser = dayjs.tz(retDate, userTimezone).utcOffset();
        const offsetServer = dayjs.tz(retDate, serverTimezone).utcOffset();
        retDate = dayjs(retDate)
            .utcOffset(offsetUser - offsetServer)
            .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    } else if (retDate) {
        retDate = retDate.toISOString();
    }

    return retDate;
}

export function dateToUserZoneIsoDayJs(date) {
    const { serverTimezone } = LoggedUserStore.getState();
    const userTimezone = dayjs.tz.guess();
    let retDate = date;

    // eslint-disable-next-line eqeqeq
    if (new Date(date).toString() === 'Invalid Date') {
        return date;
    }

    // eslint-disable-next-line eqeqeq
    if (serverTimezone !== userTimezone) {
        const offsetUser = dayjs.tz(retDate, userTimezone).utcOffset();
        const offsetServer = dayjs.tz(retDate, serverTimezone).utcOffset();
        retDate = dayjs(retDate)
            .utcOffset(offsetServer - offsetUser)
            .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    }

    return retDate;
}

export function momentFormatDate(
    date,
    dateWithoutTime? = true,
    showTime? = false,
    showTimeWithSec? = false,
    onlyTime? = false,
) {
    const { serverTimezone, userLocale, dateFormat } = LoggedUserStore.getState();
    moment.locale(localStorage.getItem('userLang') || userLocale);

    // eslint-disable-next-line eqeqeq
    if (new Date(date) == 'Invalid Date') {
        return '';
    }

    let format = dateFormat || 'L';

    if (showTime) {
        format = `${dateFormat || 'L'} LT`;
    }

    if (showTimeWithSec) {
        format = `${dateFormat || 'L'} LTS`;
    }

    if (onlyTime) {
        format = showTimeWithSec ? 'LTS' : 'LT';
    }

    if (!dateWithoutTime) {
        return moment(date).format(format);
    }

    return moment.tz(date, serverTimezone).format(format);
}

export function isSupportedBrowser(ua) {
    if (ua.indexOf('Trident') != -1 || ua.indexOf('MSIE') != -1) {
        return false;
    }
    return true;
}

/**
 * Returns backend url, multitenant ready
 * @return {string}
 */
export function getBackendUrl() {
    let url = config.backendUrl;

    if (config.multitenantBackend) {
        url = `${window.location.origin}/api`;
    }

    return url;
}

/**
 * Returns backend url without suffix
 * @param backurl
 * @return {string}
 */
export function getBackendWithoutSuffix(backurl) {
    const url = new URL(backurl);
    return url.origin;
}

/**
 * Returns suffix of backend
 * @param backurl
 * @return {string}
 */
export function getBackendSuffix(backurl) {
    const url = new URL(backurl);
    const { pathname } = url;
    return pathname === '/' ? '' : pathname;
}

/**
 * returns true if backend runs on same domain
 * @param backurl
 */
export function isSameDomain(backurl) {
    const url = new URL(backurl);
    if (config.multitenantBackend) {
        // with multitenant, always runs on same domain
        return true;
    }

    return window.location.origin === url.origin;
}

export function getUrlParamValue(url, param) {
    const params = new Proxy(new URLSearchParams(url), {
        get: (searchParams, prop) => searchParams.get(prop),
    });

    return params[param];
}

export function getDrColumnMeta(cDef, c) {
    if (cDef && (cDef.hasOwnProperty('meta') || cDef.hasOwnProperty('tvar_meta'))) {
        if (cDef.hasOwnProperty('meta')) {
            // nechat kvuli zpetne kompatibilite
            return cDef.meta;
        }
        return cDef.tvar_meta;
    }
    return c.hasOwnProperty('meta') ? c.meta : c.tvar_meta;
}

/**
 * Completes the tree, or sets tree state and buttons - for trees where an ID in URL is used (overviews, reports)
 * @param {bool} isUrlId
 * @param {string} urlId
 * @param {array} treeArr
 * @param {array} flatArr
 * @param {object} tree
 * @param {function} treeEditable
 * @return {string}
 */
export function finishTree(isUrlId, urlId, treeArr, flatArr, tree, treeEditable) {
    if (!isUrlId && typeof treeArr[0] !== 'undefined') {
        if (_.isEmpty(treeArr[0].children)) {
            treeArr[0].selected = true;
        }
    } else if (urlId) {
        const findItem = _.find(flatArr, { id: parseInt(urlId, 10) });
        let openNodes = [];
        const { treeState } = tree.state;

        if (treeState) {
            openNodes = treeState.open_nodes;
        }

        const newTreeState = {
            open_nodes: openNodes,
            selected_node: [],
        };

        if (findItem) {
            const foldersToOpen = findItem.name.split('/');
            foldersToOpen.pop();
            newTreeState.open_nodes = openNodes.concat(foldersToOpen);
            newTreeState.selected_node.push(parseInt(urlId, 10));

            tree.updateTreeState(newTreeState);
            treeEditable(tree, urlId);
        } else {
            tree.updateTreeState(newTreeState);
            treeEditable(tree, null);
        }
    }
}

export function getExtension(filename) {
    const parts = filename.split('.');
    return parts[parts.length - 1].toLowerCase();
}

export function isImage(filename) {
    const ext = getExtension(filename);
    switch (ext) {
        case 'jpg':
        case 'jpeg':
        case 'gif':
        case 'bmp':
        case 'png':
            return true;
        default:
            return false;
    }
}

export function isPdf(filename) {
    return getExtension(filename) === 'pdf';
}

export function isText(filename) {
    const ext = getExtension(filename);
    switch (ext) {
        case 'txt':
        case 'csv':
        case 'xml':
            return true;
        default:
            return false;
    }
}

export function isHtml(filename) {
    const ext = getExtension(filename);
    switch (ext) {
        case 'html':
        case 'htm':
            return true;
        default:
            return false;
    }
}

export function isWord(filename) {
    return getExtension(filename) === 'docx';
}

export function isVideo(filename) {
    const ext = getExtension(filename);
    switch (ext) {
        case 'mp4':
        // case 'wmv':
        // case 'avi':
        case 'webm':
        case 'mov':
            return true;
        default:
            return false;
    }
}

export function printPreviewFile(fileName: string) {
    const content = document.getElementById('file-preview-new-modal');

    if (content !== null) {
        const iframe = document.querySelector('#print-frame');

        if (iframe && iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
        }

        const frame = document.createElement('iframe');
        frame.setAttribute('id', 'print-frame');
        frame.style.visibility = 'hidden';
        frame.name = 'print_iframe';
        document.body.appendChild(frame);

        if (frame?.contentDocument) {
            if (isText(fileName)) {
                const text = content.querySelector('textarea')?.innerHTML;
                if (text) {
                    frame.contentDocument.body.innerHTML = text.replace(/\n/g, '<br>');
                    frame.contentWindow?.print();
                }
            } else if (isImage(fileName)) {
                const img = content.querySelector('img')?.outerHTML;
                if (img) {
                    frame.contentDocument.body.innerHTML = img;
                    frame.contentWindow?.print();
                }
            } else if (isWord(fileName)) {
                const doc = content.querySelector('.docx-preview')?.innerHTML;
                if (doc) {
                    frame.contentDocument.body.innerHTML = doc;
                    frame.contentWindow?.print();
                }
            }
        }
    }
}

export function domPurifySanitize(html) {
    return DOMPurify.sanitize(html, {
        ALLOWED_TAGS: [
            'div',
            'span',
            'p',
            'a',
            'img',
            'b',
            'i',
            'u',
            'strong',
            'em',
            'br',
            'hr',
            'ul',
            'ol',
            'li',
            'table',
            'thead',
            'tbody',
            'tr',
            'th',
            'td',
        ],
        ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'width', 'height', 'style', 'class', 'id'],
    });
}

const getPopupPreviewDimensions = () => {
    const width = 0.75 * window.innerWidth;
    const height = 0.75 * window.innerHeight;
    const topPos = (window.innerHeight - height) / 2;
    const leftPos = (window.innerWidth - width) / 2;

    return { width, height, topPos, leftPos };
};

export function popupPdf(blob) {
    const { width, height, topPos, leftPos } = getPopupPreviewDimensions();
    const pdfReader = new FileReader();

    pdfReader.addEventListener('loadend', () => {
        const pdf = pdfReader.result;
        const blob = new Blob([pdf], { type: 'application/pdf' });
        const url = URL.createObjectURL(blob);
        const newWindow = window.open(
            url,
            '_blank',
            `toolbar=yes,scrollbars=yes,resizable=yes,top=${topPos},left=${leftPos},width=${width},height=${height}`,
        );

        if (!newWindow) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrBlockedPopups'),
                show: true,
            });
        }
    });

    pdfReader.readAsArrayBuffer(blob);
}

export function popupImage(blob) {
    const { width, height, topPos, leftPos } = getPopupPreviewDimensions();
    const reader = new FileReader();

    reader.addEventListener('load', () => {
        const url = URL.createObjectURL(blob);
        const newWindow = window.open(
            url,
            '_blank',
            `top=${topPos},left=${leftPos},width=${width},height=${height}`,
        );

        if (!newWindow) {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrBlockedPopups'),
                show: true,
            });
        }
    });

    reader.readAsDataURL(blob);
}

export function popupText(blob, title?) {
    const { width, height, topPos, leftPos } = getPopupPreviewDimensions();
    const reader = new FileReader();

    const detectAndRead = (result: any) => {
        const encoding = jschardet.detect(result);

        reader.onload = () => {
            const newWindow = window.open(
                '',
                '_blank',
                `top=${topPos},left=${leftPos},width=${width},height=${height}`,
            );

            if (newWindow) {
                newWindow.document.write(`
                    <html lang="en" translate="no" class="notranslate">
                        <head>
                            <meta charset="utf-8">
                            <title>${title || ''}</title>
                        </head>
                        <body>
                            <pre>${reader.result}</pre>
                        </body>
                    </html>
                `);
                newWindow.document.close();
            } else {
                AlertsActions.addAlert({
                    type: 'warning',
                    message: i18next.t('alrBlockedPopups'),
                    show: true,
                });
            }
        };

        reader.readAsText(blob, encoding.encoding);
    };

    reader.onload = () => {
        const { result } = reader;
        detectAndRead(result);
    };

    reader.readAsArrayBuffer(blob);
}

export function popupDocx(blob, title?) {
    const { width, height, topPos, leftPos } = getPopupPreviewDimensions();
    const reader = new FileReader();

    reader.onload = () => {
        const { result } = reader;

        mammoth
            .convertToHtml({ arrayBuffer: result })
            .then((result: any) => {
                if (!isEmpty(result.messages)) {
                    const error = { error: result.messages[0] };
                    console.warn('warning', error);
                }

                const sanitizedHtml = domPurifySanitize(result.value);
                const newWindow = window.open(
                    '',
                    '_blank',
                    `top=${topPos},left=${leftPos},width=${width},height=${height}`,
                );

                if (newWindow) {
                    newWindow.document.write(`
                        <html lang="en" translate="no" class="notranslate">
                            <head>
                                <meta charset="utf-8">
                                <title>${title || ''}</title>
                            </head>
                            <body>
                                ${sanitizedHtml}
                            </body>
                        </html>
                    `);
                    newWindow.document.close();
                } else {
                    AlertsActions.addAlert({
                        type: 'warning',
                        message: i18next.t('alrBlockedPopups'),
                        show: true,
                    });
                }
            })
            .done();
    };

    reader.readAsArrayBuffer(blob);
}

export function popupVideo(blob, title?) {
    const { width, height, topPos, leftPos } = getPopupPreviewDimensions();
    const reader = new FileReader();

    reader.addEventListener('load', () => {
        const url = URL.createObjectURL(blob);
        const newWindow = window.open(
            '',
            '_blank',
            `top=${topPos},left=${leftPos},width=${width},height=${height}`,
        );

        if (newWindow) {
            newWindow.document.write(`
            <video controls style="max-width: 100%; max-height: 100%;">
                <source src="${url}">
                ${i18next.t('embeddedVideoNotSupported')}
            </video>
        `);
            newWindow.document.title = title || '';
            newWindow.document.close();
        } else {
            AlertsActions.addAlert({
                type: 'warning',
                message: i18next.t('alrBlockedPopups'),
                show: true,
            });
        }
    });

    reader.readAsDataURL(blob);
}

export function getDefaultColors() {
    return ApiRequest.get(`${document.location.origin}/colors`)
        .then((payload) => {
            if (payload) {
                const colorsObj = {};
                const colorsProps = payload.split(/\r?\n/);

                colorsProps.forEach((row) => {
                    const trimmedRow = row.trim();

                    if (_.startsWith(trimmedRow, '--')) {
                        const rowArr = trimmedRow.split(':');
                        const name = rowArr[0].replace(/--/g, '');
                        colorsObj[name] = rowArr[1].trim().split(';')[0];
                    }
                });

                return colorsObj;
            }
        })
        .catch((errorMessage) => {
            AlertsActions.addAlert({
                type: 'alert',
                message: i18next.t('alrFailedTasColors'),
                serverError: errorMessage,
            });
        });
}

export function checkLogo(all? = false, type?: string) {
    const darkThemeLc = localStorage.getItem('darkTheme');
    const darkTheme = darkThemeLc === 'true';
    const types = ['logoLoginDarkTheme', 'logoLoginDefaultTheme', 'favicon'];
    let logoType = darkTheme ? types[0] : types[1];

    if (type) {
        logoType = type;
    }

    const body = {
        type: all ? types : logoType,
    };

    return ApiRequest.post(`${document.location.origin}/check-logo`, JSON.stringify(body))
        .then((payload) => {
            return payload;
        })
        .catch((errorMessage) => {
            AlertsActions.addAlert({
                type: 'alert',
                message: `${i18next.t('alrFailedData')} (check logo)`,
                serverError: errorMessage,
            });

            return null;
        });
}

export function getDefaultMenu() {
    const defaultButtonsArr = [];

    if (!LoggedUserStore.getConfig('tas.buttons.hideTasks')) {
        defaultButtonsArr.push({
            label: 'tasks',
            icon: 'icon2-tasks',
            type: 'GET',
            link: '/tasks',
            visible: '0',
            newWindow: false,
            iframe: false,
        });
    }
    if (!LoggedUserStore.getConfig('tas.buttons.hideProcesses')) {
        defaultButtonsArr.push({
            label: 'cases',
            icon: 'icon2-cases',
            type: 'GET',
            link: '/cases',
            visible: '0',
            newWindow: false,
            iframe: false,
        });
    }
    defaultButtonsArr.push({
        label: 'overviews',
        icon: 'icon2-overviews',
        type: 'GET',
        link: '/overviews',
        visible: '0',
        newWindow: false,
        iframe: false,
    });
    if (!LoggedUserStore.getConfig('tas.buttons.hideReports')) {
        defaultButtonsArr.push({
            label: 'statistics',
            icon: 'icon2-reports',
            type: 'GET',
            link: '/reports',
            visible: '0',
            newWindow: false,
            iframe: false,
        });
    }
    defaultButtonsArr.push(
        {
            label: 'templates',
            icon: 'icon2-templates',
            type: 'GET',
            link: '/templates',
            visible: '1',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'administration',
            icon: 'icon2-administrator',
            type: 'GET',
            link: '/administration-menu',
            visible: '2',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'plans',
            icon: 'icon2-planning',
            type: 'GET',
            link: '/plans',
            visible: '2',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'users',
            icon: 'icon2-users',
            type: 'GET',
            link: '/users',
            visible: '2',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'roles',
            icon: 'icon2-roles',
            type: 'GET',
            link: '/roles',
            visible: '2',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'orgStructure',
            icon: 'icon2-structure',
            type: 'GET',
            link: '/structure',
            visible: '2',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'events',
            icon: 'icon2-events',
            type: 'GET',
            link: '/events',
            visible: '2',
            newWindow: false,
            iframe: false,
        },
        {
            label: 'documents',
            icon: 'icon2-documents',
            type: 'GET',
            link: '/documents',
            visible: '0',
            newWindow: false,
            iframe: false,
        },
    );
    if (config.menuLink) {
        if (_.isArray(config.menuLink)) {
            _.forEach(config.menuLink, (menuLink) => {
                defaultButtonsArr.push(menuLink);
            });
        } else {
            defaultButtonsArr.push(config.menuLink);
        }
    }

    defaultButtonsArr.push({
        label: 'news',
        icon: 'icon2-news',
        type: 'GET',
        link: '/news',
        visible: '0',
        newWindow: false,
        iframe: false,
    });

    return defaultButtonsArr;
}

export function searchInOptions(options, searchTerm) {
    let filteredData = [];

    if (LoggedUserStore.getConfig('tas.disableAccentSensitivity')) {
        options.forEach((option) => {
            let optionLowered = option.title.toLowerCase();
            let searchTermLowered = searchTerm.toLowerCase();

            optionLowered = optionLowered?.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
            searchTermLowered = searchTermLowered?.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

            if (optionLowered?.includes(searchTermLowered)) {
                filteredData.push(option);
            }
        });
    } else {
        filteredData = _.filter(options, (obj) => {
            try {
                return obj.title.toLowerCase().indexOf(searchTerm.toLowerCase()) !== -1;
            } catch (err) {
                return false;
            }
        });
    }

    return filteredData;
}

export function getTableSort(order, sort, orderId) {
    let tableSort = sort;
    const commasCount = (order.match(/,/g) || []).length;

    if (commasCount > 0) {
        // ttask_name_cs,itask_name,id
        const orderArr = order.split(',');
        const tableSortArr = [sort];

        for (let i = 1; i <= commasCount; i += 1) {
            if (orderId && orderId === orderArr[i]) {
                // for orderId is sort always asc
                tableSortArr.push('asc'); // desc,desc,asc
            } else {
                tableSortArr.push(sort);
            }
        }

        tableSort = tableSortArr.join(',');
    }

    return tableSort;
}

export const browserStorage = {
    getItem: (key) => {
        const { useSessionStorage } = LoggedUserStore.getState();
        const storage = useSessionStorage ? sessionStorage : localStorage;
        return storage.getItem(key);
    },

    setItem: (key, value) => {
        const { useSessionStorage } = LoggedUserStore.getState();
        const storage = useSessionStorage ? sessionStorage : localStorage;
        storage.setItem(key, value);
    },

    removeItem: (key) => {
        const { useSessionStorage } = LoggedUserStore.getState();
        const storage = useSessionStorage ? sessionStorage : localStorage;
        storage.removeItem(key);
    },
};

export function storeNewTokens(tokensObj) {
    // Store the new token pair in the Storage
    const vice = JSON.parse(browserStorage.getItem('vice'));

    if (vice) {
        browserStorage.setItem(
            'vice',
            JSON.stringify({
                viceToken: tokensObj.accessToken,
                viceRefreshToken: tokensObj.refreshToken,
                viceName: vice.viceName,
            }),
        );
    }

    LoggedUserActions.setSessionTimeout();
}

export class RemCalculator {
    /**
     * Converts and/or scales px values utilizing the base font size (= browser font size at construction by default; uses 16 as base in case the browser font size can't be read)
     * @param {number} [base] custom base font size to use for conversions
     */
    constructor(base?) {
        this.base =
            base ||
            parseInt(
                window
                    .getComputedStyle(document.querySelector('html'))
                    .getPropertyValue('font-size')
                    .replace('px', ''),
            ) ||
            16;
    }

    /**
     * Converts px value to rem - based on the current base font size (= browser font size by default)
     * @param {number} pxValue px value to convert
     * @return {number} rem value
     */
    remCalc(pxValue) {
        return pxValue / this.base;
    }

    /**
     * Scales a px value by the browser/default font size ratio
     * @param {number} pxValue px value to convert
     * @param {number} [defaultFontSize=16] custom default browser font size (16 by default)
     * @return {number} scaled px value
     */
    pxScale(pxValue, defaultFontSize = 16) {
        const remScaleRatio = this.base / 16;
        return pxValue * remScaleRatio;
    }

    /**
     * Updates the rem calculator's base font size used for conversions - updates to current browser font size by default
     * @param {number} [base] custom base font size to update to
     */
    update(base) {
        this.base =
            base ||
            parseInt(
                window
                    .getComputedStyle(document.querySelector('html'))
                    .getPropertyValue('font-size')
                    .replace('px', ''),
            ) ||
            16;
    }
}

// Task - returns Multiselect options (lovs and DT readonly)
export function getMultiselectOptions(multiselectType, i, forceReadOnly?: boolean) {
    const callServiceTypes = ['DLU', 'DLO', 'DLR', 'DL']; // DL - dlist_name

    if (!forceReadOnly && i.ivar_usage && i.ivar_usage !== 'R') {
        callServiceTypes.push('DT');
    }

    if (callServiceTypes.indexOf(multiselectType) === -1) {
        const isReadOnly = i.ivar_usage === 'R' || forceReadOnly || !i.ivar_usage;

        let options = checkVarLovLangMutation(i);

        if (i.ivar_type === 'DT' && isReadOnly) {
            // after changeVarVal (dynamic conditions), ivar_dt_index i.ivar_value are the same
            // we do not want to create options
            if (!_.isEqual(i.ivar_dt_index, i.ivar_value)) {
                options = [];
                // options from ivar_dt_index ["index", ..] i.ivar_value ["value", ...]
                i.ivar_dt_index.forEach((val, index) => {
                    options.push({ value: val, title: i.ivar_value[index] });
                });
            }
        }

        return options;
    }

    return undefined;
}

// disable auto zoom on input click on iphone
export function disableIosTextFieldZoom() {
    const isIOS = () => {
        return (
            /ipad|iphone|ipod/i.test(navigator.userAgent) ||
            /ipad|iphone|ipod/i.test(navigator.platform) ||
            (/mac/i.test(navigator.platform) &&
                !!navigator.maxTouchPoints &&
                navigator.maxTouchPoints > 1)
        );
    };

    if (!isIOS()) {
        return;
    }

    const element = document.querySelector('meta[name=viewport]');

    if (element !== null) {
        let content = element.getAttribute('content');
        const scalePattern = /maximum-scale=[0-9.]+/g;

        if (scalePattern.test(content)) {
            content = content.replace(scalePattern, 'maximum-scale=1.0');
        } else {
            content = [content, 'maximum-scale=1.0'].join(', ');
        }

        element.setAttribute('content', content);
    }
}

// Function to convert hex to RGBA with the given opacity
export function hexToRgba(hex: string, opacity: number) {
    const bigint = parseInt(hex.slice(1, 7), 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

export function isISODateString(value) {
    const isoDatePattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(Z|[+-]\d{2}:\d{2})?$/;
    // date sometimes returns from BE as a DD.MM.YYYY string instead of regular ISO
    const ddMMYYYYPattern = /^(0?[1-9]|[12][0-9]|3[01])\.(0?[1-9]|1[012])\.\d{4}$/;
    return typeof value === 'string' && (isoDatePattern.test(value) || ddMMYYYYPattern.test(value));
}

export async function getFavouriteNamesObj(
    obj: any,
    prefix: string,
    nameKey: string,
): Promise<{ name: string } & Partial<Record<`name_${string}`, string>>> {
    const { languages } = LoggedUserStore.getState();
    const langsToLoad = languages.filter(
        (lang: string) => !i18next.hasResourceBundle(lang, 'common'),
    );

    if (langsToLoad.length > 0) {
        await new Promise<void>((resolve) => {
            i18next.loadLanguages(langsToLoad, () => resolve());
        });
    }

    const namesObj: Record<string, string> = {
        name: `${i18next.t(prefix)} - ${checkLangMutation(obj, nameKey)}`,
    };

    languages.forEach((lang: string) => {
        const tr = i18next.getFixedT(lang, 'common');
        if (obj[`${nameKey}_${lang}`]) {
            namesObj[`name_${lang}`] = `${tr(prefix)} - ${obj[`${nameKey}_${lang}`]}`;
        }
    });

    return namesObj;
}
