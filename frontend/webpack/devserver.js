'use strict';

const Webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const log = require('fancy-log');
const configReader = require('../src/common/configReader');

const config = configReader();

module.exports = webpackConfig => {
    return callback => {
        const devServerOptions = {
            /* proxy: {
                '*': `${tasConfig.localhostUrl}:${config.webpackPort}`,
            }, */
            port: config.webpackPort,
            host: '0.0.0.0',
            hot: true,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
                'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
            },
            client: {
                overlay: {
                    // don't show certain error messages
                    runtimeErrors: (error) => {
                        if (error?.message === 'ResizeObserver loop completed with undelivered notifications.') {
                            return false;
                        }

                        return true;
                    },
                },
            },
            ...config.webpackDevServerOptions,
        };
        const server = new WebpackDevServer(devServerOptions, Webpack(webpackConfig));

        server.startCallback(() => {
            log('[webpack-dev-server]', `${config.localhostUrl}:${config.webpackPort}/build/app.js`);
            log('[webpack-dev-server]', 'Wait for webpack to compile...');
            callback();
        });
    };
};
