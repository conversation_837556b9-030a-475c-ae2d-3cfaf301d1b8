const Webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const gutil = require('gulp-util');
const configReader = require('../src/common/configReader');

const config = configReader();

module.exports = webpackConfig => {
    return callback => {
        const devServerOptions = {
            /* proxy: {
                '*': `${tasConfig.localhostUrl}:${config.webpackPort}`,
            }, */
            port: config.webpackPort,
            host: '0.0.0.0',
            hot: false,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
                'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
            },
            /* client: {
                logging: 'error',
                progress: true,
                reconnect: 2,
                overlay: true,
            }, */
            ...config.webpackDevServerOptions,
        };
        const server = new WebpackDevServer(devServerOptions, Webpack(webpackConfig));

        server.startCallback(() => {
            gutil.log('[webpack-dev-server]', `${config.localhostUrl}:${config.webpackPort}/build/app.js`);
            gutil.log('[webpack-dev-server]', 'Wait for webpack to compile...');
            callback();
        });
    };
};
