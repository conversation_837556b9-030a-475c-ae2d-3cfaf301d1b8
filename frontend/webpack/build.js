'use strict';

const webpack = require('webpack');
const log = require('fancy-log');
const PluginError = require('plugin-error');

module.exports = function(webpackConfig) {
  return function(callback) {
    webpack(webpackConfig, function(fatalError, stats) {
        const jsonStats = stats.toJson();
        const buildError = fatalError || jsonStats.errors[0] || jsonStats.warnings[0];

      if (buildError) {
          throw new PluginError('webpack', buildError);
      }

      log('[webpack]', stats.toString({
        colors: true,
        version: false,
        hash: false,
        timings: false,
        chunks: false,
        chunkModules: false
      }));

      callback();
    });
  };
};
