'use strict';

const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const configReader = require('../src/common/configReader');

const tasConfig = configReader();

module.exports = (isDevelopment) => {
    // http://christianalfoni.github.io/javascript/2014/12/13/did-you-know-webpack-and-react-is-awesome.html
    return {
        watchOptions: {
            ignored: /node_modules/,
        },
        stats: 'errors-warnings',
        cache: isDevelopment,
        mode: isDevelopment ? 'development' : 'production',
        devtool: isDevelopment ? 'inline-source-map' : false,
        optimization: {
            splitChunks: {
                cacheGroups: {
                    appStyles: {
                        name: 'appStyles', // pozor pri zmene nazvu, viz t3b-1540 Ošklivý tisk pdf
                        test: /\.scss|\.sass|\.css$/,
                        enforce: true,
                        type: 'css/mini-extract',
                        chunks: 'all',
                    },
                },
            },
        },
        performance: {
            hints: false,
        },
        entry: {
            vendors: './src/app/browser.require.js',
            app: './src/main',
        },
        module: {
            rules: [
                {
                    test: /\.(gif|jpg|png|woff|woff2|eot|ttf|svg)$/,
                    type: 'asset/resource',
                },
                {
                    test: /pdf\.worker\.js$/, // special rule for pdf file-loader
                    type: 'asset/resource',
                },
                {
                    // babel takes care of react-transform
                    test: /\.(js|jsx)$/,
                    exclude: [/node_modules/, /assets/],
                    use: [
                        {
                            loader: 'babel-loader',
                            options: {
                                presets: ['@babel/preset-env', '@babel/preset-react'],
                                compact: false, // minimize will be with babel.optimize
                            },
                        },
                    ],
                },
                {
                    test: /\.tsx?$/,
                    loader: 'ts-loader',
                },
                {
                    test: /\.css/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        {
                            loader: 'css-loader',
                            options: {
                                url: false,
                                import: false,
                                sourceMap: true,
                                esModule: false,
                            },
                        },
                    ],
                },
                {
                    test: /\.scss|\.sass/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        {
                            loader: 'css-loader',
                            options: {
                                sourceMap: true,
                            },
                        },
                        {
                            loader: 'sass-loader',
                            options: {
                                // outputStyle: 'expanded',
                                sourceMap: true,
                                // sourceMapContents: true,
                            },
                        },
                    ],
                },
                {
                    test: require.resolve('jquery'),
                    use: [
                        {
                            loader: 'expose-loader',
                            options: {
                                exposes: ['jQuery', '$'],
                            },
                        },
                    ],
                },
                {
                    test: require.resolve('moment'),
                    use: [
                        {
                            loader: 'expose-loader',
                            options: {
                                exposes: {
                                    globalName: 'moment',
                                    override: true,
                                },
                            },
                        },
                    ],
                },
            ],
            // https://github.com/microsoft/TypeScript/issues/39436#issuecomment-817029140
            noParse: [require.resolve('typescript/lib/typescript.js')],
        },
        output: isDevelopment
            ? {
                path: path.join(__dirname, '/build/'),
                filename: '[name].js',
                publicPath: `${tasConfig.localhostUrl}:${tasConfig.webpackPort}/build/`,
            }
            : {
                path: `${path.resolve('.')}/dist/`,
                filename: '[name].js',
            },
        plugins: [
            new webpack.DefinePlugin({
                'process.env': {
                    NODE_ENV: JSON.stringify(isDevelopment ? 'development' : 'production'),
                    IS_BROWSER: true,
                },
            }),
            new webpack.ProvidePlugin({
                process: 'process/browser',
                Promise: 'bluebird', // Thanks Aaron (https://gist.github.com/Couto/b29676dd1ab8714a818f#gistcomment-1584602)
                // fetch: 'imports?this=>global!exports?global.fetch!whatwg-fetch',
            }),
            // Render styles into separate cacheable file to prevent FOUC and
            // optimize for critical rendering path.
            new MiniCssExtractPlugin({
                // Options similar to the same options in webpackOptions.output
                // both options are optional
                filename: 'appStyles.css',
            }),
            isDevelopment ? new ForkTsCheckerWebpackPlugin({
                typescript: {
                    memoryLimit: 4096,
                    configFile: path.resolve(__dirname, '../tsconfig.json'),
                },
            }) : new CompressionPlugin({
                filename: '[file].gz[query]',
                algorithm: 'gzip',
                test: /\.js$|\.css$|\.html$/,
                threshold: 10240,
                minRatio: 0.8,
            }),
        ].filter((item) => item !== undefined),
        resolve: {
            extensions: ['*', '.js', '.jsx', '.ts', '.tsx', '.json', '.mjs'],
            fallback: {
                path: require.resolve('path-browserify'),
                querystring: require.resolve('querystring-es3'),
                stream: require.resolve('stream-browserify'),
                util: require.resolve('util/'),
                assert: require.resolve('assert/'),
                buffer: require.resolve('buffer/'),

            },
        },
    };
};
