{"private": true, "dependencies": {"@babel/core": "7.26.10", "@babel/plugin-transform-class-properties": "7.25.9", "@babel/plugin-transform-modules-commonjs": "7.26.3", "@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@babel/preset-typescript": "7.26.0", "@babel/register": "7.25.9", "@cyntler/react-doc-viewer": "1.13.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@fontsource/inter": "5.0.5", "@jrblatt/light-script": "0.0.31", "@monaco-editor/loader": "1.4.0", "@monaco-editor/react": "4.5.1", "@mui/lab": "6.0.0-beta.12", "@mui/material": "6.1.5", "@mui/utils": "6.1.4", "@mui/x-data-grid-pro": "7.23.5", "@mui/x-date-pickers": "7.12.0", "@mui/x-date-pickers-pro": "7.22.2", "@mui/x-license-pro": "6.10.2", "@mui/x-tree-view": "7.23.6", "@storybook/addon-a11y": "8.2.9", "@storybook/addon-actions": "8.2.9", "@storybook/addon-designs": "8.0.3", "@storybook/addon-essentials": "8.2.9", "@storybook/addon-interactions": "8.2.9", "@storybook/addon-links": "8.2.9", "@storybook/addon-onboarding": "8.2.9", "@storybook/addon-styling-webpack": "1.0.1", "@storybook/addon-webpack5-compiler-babel": "3.0.5", "@storybook/blocks": "8.2.9", "@storybook/core-events": "8.2.9", "@storybook/manager-api": "8.2.9", "@storybook/react": "8.2.9", "@storybook/react-dom-shim": "8.2.9", "@storybook/react-webpack5": "8.2.9", "@storybook/test": "8.2.9", "@storybook/test-runner": "0.19.1", "@storybook/types": "8.2.9", "@tanstack/react-table": "8.20.1", "@tanstack/react-virtual": "3.10.6", "@types/autosuggest-highlight": "3.2.3", "@types/bluebird": "3.5.42", "@types/dompurify": "3.2.0", "@types/lint-staged": "13.3.0", "@types/lodash": "4.17.6", "@types/numeral": "2.0.5", "@types/react": "18.3.2", "@types/react-beautiful-dnd": "13.1.8", "@types/react-document-title": "2.0.10", "@types/react-dom": "18.3.0", "@types/react-dragula": "1.1.3", "@types/react-grid-layout": "1.3.5", "@types/react-loader": "2.4.8", "@types/react-router-dom": "5.3.3", "@vitalets/google-translate-api": "8.0.0", "acorn-loose": "8.3.0", "ajv": "8.12.0", "alt": "0.18.6", "assert": "2.0.0", "async": "3.2.6", "autosuggest-highlight": "3.3.4", "axios": "1.8.4", "babel-loader": "10.0.0", "bluebird": "3.7.2", "body-parser": "1.20.3", "bpmn-js": "7.3.1", "bpmn-js-bpmnlint": "0.15.0", "bpmn-js-properties-panel": "0.37.1", "bpmn-moddle": "7.0.3", "bpmnlint": "7.0.0", "bpmnlint-utils": "1.0.2", "buffer": "6.0.3", "chart.js": "4.4.8", "chartjs-adapter-dayjs": "1.0.0", "classnames": "2.5.1", "client-oauth2": "4.3.3", "clsx": "2.1.1", "compression-webpack-plugin": "11.1.0", "create-react-class": "15.7.0", "cron-parser": "5.0.4", "cronstrue": "2.56.0", "css-loader": "6.8.1", "css-vars-ponyfill": "2.4.9", "dayjs": "1.11.13", "diagram-js": "6.7.1", "diagram-js-origin": "1.3.1", "dom-helpers": "5.2.1", "dompurify": "3.2.4", "expose-loader": "4.1.0", "express": "4.21.2", "express-static-gzip": "2.2.0", "fancy-log": "2.0.0", "flux": "4.0.4", "fork-ts-checker-webpack-plugin": "9.0.2", "formsy-react": "git+https://github.com/teamassistant/formsy-react#tas-1.0.1", "framer-motion": "10.15.2", "gulp": "4.0.2", "gulp-bg": "0.0.9", "gulp-eslint": "6.0.0", "history": "4.7.2", "hopscotch": "0.3.1", "i18next": "24.2.2", "i18next-http-backend": "3.0.2", "immer": "10.1.1", "inherits": "2.0.4", "intl": "1.2.5", "jquery": "3.6.4", "jschardet": "3.1.4", "jwt-decode": "3.1.2", "lodash": "4.17.21", "mammoth": "1.9.0", "memoize-one": "6.0.0", "min-dom": "3.1.3", "mini-css-extract-plugin": "2.9.2", "mkdirp": "3.0.1", "moment": "2.30.1", "moment-timezone": "0.5.47", "msw": "2.7.3", "msw-storybook-addon": "2.0.4", "numeral": "2.0.6", "papaparse": "5.5.2", "overview-components": "1.0.23", "password-validator": "5.1.1", "path-browserify": "1.0.1", "pdfjs-dist": "3.6.172", "piping": "1.0.0-rc.4", "plugin-error": "2.0.1", "process": "0.11.10", "prop-types": "15.8.1", "querystring-es3": "0.2.1", "raw-loader": "4.0.2", "rc-tooltip": "6.0.1", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-chartjs-2": "5.2.0", "react-cropper": "2.3.3", "react-document-title": "2.0.3", "react-dom": "18.3.1", "react-dragula": "1.1.17", "react-dropzone": "14.3.8", "react-grid-layout": "1.5.0", "react-hook-form": "7.54.2", "react-i18next": "15.4.1", "react-input-color": "4.0.1", "react-json-tree": "0.20.0", "react-loader": "2.4.7", "react-modal": "3.16.3", "react-number-format": "5.4.3", "react-overlays": "5.2.1", "react-qr-code": "2.0.15", "react-rnd": "10.4.10", "react-router-dom": "5.3.4", "react-sparklines": "1.7.0", "react-waypoint": "10.3.0", "react-widgets": "5.8.6", "resolve-url-loader": "5.0.0", "sanitize-html": "2.14.0", "sass": "1.77.2", "sass-loader": "14.2.1", "simplebar-react": "3.3.0", "socket.io-client": "2.4.0", "storybook": "8.2.9", "stream-browserify": "3.0.0", "string-table": "0.1.5", "style-loader": "3.3.3", "superagent": "8.0.9", "tiny-svg": "2.2.2", "ts-loader": "9.5.2", "typescript": "5.8.2", "util": "0.12.5", "webpack": "5.98.0", "webpack-dev-server": "5.2.0", "xdomain": "0.7.3", "yargs": "17.7.2", "zustand": "5.0.3"}, "scripts": {"build": "./node_modules/.bin/gulp", "dev": "./node_modules/.bin/gulp build watch", "translateLocales": "node ./scripts/localeTranslation.js", "server": "gulp server --max_old_space_size=8192", "serverProd": "gulp server -p", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "pre-commit": "lint-staged", "eslint:fix": "eslint . --fix --quiet", "eslint": "eslint .", "prepare": "cd .. && husky"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "7.9.0", "@typescript-eslint/parser": "7.9.0", "eslint": "8.57.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-react": "7.34.1", "eslint-plugin-storybook": "0.8.0", "husky": "9.0.11", "lint-staged": "15.2.2", "prettier": "3.2.5", "typescript-eslint": "7.9.0"}, "lint-staged": {"**/*.ts": ["prettier --write", "eslint --quiet --fix"], "**/*.tsx": ["prettier --write", "eslint --quiet --fix"]}, "browser": {"fs": false, "jsdom": false}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "msw": {"workerDirectory": [".storybook\\msw\\public"]}}