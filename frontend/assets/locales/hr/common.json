{"1st": "1.", "2nd": "2.", "3rd": "3.", "4th": "4.", "AddToAllTasks": "Dodajte sve zadatke", "OfVariable": "Od varijable", "RemoveFromAllTasks": "Ukloni iz svih zadataka", "TaskOwnerWhichInVar": "<PERSON>las<PERSON><PERSON> zadat<PERSON>, koji je postavljen u varijablu", "action": "<PERSON><PERSON><PERSON>", "active": "Aktivan", "activeShe": "Aktivan", "activePl": "Aktivan", "activity": "<PERSON><PERSON><PERSON>", "activityType": "Vrsta radnje", "actualEnd": "Stvarno vrijeme završetka", "actualSolver": "Stvarni vlasnik zadatka", "actualStart": "Stvarno vrijeme početka", "actualTsks": "Trenutni zadaci", "actualize": "Osvježi", "add": "<PERSON><PERSON><PERSON>", "addAttribute": "<PERSON><PERSON><PERSON> atri<PERSON>", "addOrgUnit": "Dodaj org. jedinicu", "addPlan": "Dodaj plan", "addPrintTemplate": "<PERSON><PERSON><PERSON> is<PERSON>a", "addRole": "<PERSON><PERSON><PERSON> r<PERSON>u", "addRule": "<PERSON><PERSON><PERSON>", "addTemp": "<PERSON><PERSON><PERSON>", "addTsk": "<PERSON><PERSON><PERSON>", "addUser": "<PERSON><PERSON><PERSON>", "addUserSomething": "Dodijelite odabranom korisniku {{variable}}:", "addVariable": "<PERSON><PERSON><PERSON>", "after": "Na<PERSON>", "afterTermTasks": "<PERSON><PERSON> z<PERSON> nakon datuma dos<PERSON>", "all": "Svi", "allFiles": "<PERSON><PERSON> dato<PERSON>ke", "allMustBeMet": "Sve mora biti ispunjeno", "allMyTasks": "<PERSON><PERSON> moji zada<PERSON>", "allSubOfPlanGuar": "All subordinates of plan guarantor", "allTasksWithNoTerm": "Svi zadaci bez roka", "allTasksWithTerm": "<PERSON><PERSON> z<PERSON> s rokom", "allTemps": "<PERSON><PERSON>", "allowMultiple": "Dopusti odabir više opcija", "allowSelectAll": "Omogući odabir svih stavki", "allsupOfPlanGuar": "All superiors of plan guarantor", "alrBlockingAction": "Potrebno je da se radnja završi! Molim pričekajte...", "alrActionNameNotDefined": "<PERSON><PERSON><PERSON> \"{{actionName}}\" nije definirana.", "alrActionNotDefined": "<PERSON><PERSON><PERSON> nije defini<PERSON>.", "alrApiUrlMissing": "Nedostaje izvor podataka tablice.", "alrAssigningTsk": "<PERSON><PERSON><PERSON><PERSON> ...", "alrAssignmentFailed": "Spremanje dodjele nije us<PERSON>lo", "alrAtrFailed": "Brisanje atributa nije us<PERSON>", "alrAttachDeleteFailed": "Brisanje dokumenta nije us<PERSON>lo", "alrAttachDeleted": "Dokument je i<PERSON>!", "alrAttachDeleting": "Brisanje dokumenta ...", "alrAttachDownloadFailed": "Preuzimanje dokumenta nije uspjelo.", "alrAttachDownloaded": "Dokument je preuzet!", "alrAttachDownloading": "Preuzimanje dokumenta ...", "alrAttachMetaFailed": "<PERSON><PERSON> uspjelo spremanje metapodataka dokumenta.", "alrAttachOrNotesCountFailed": "Brojanje dokumenata ili bilješki nije uspjelo.", "alrAttachSaveFailed": "Prijenos dokumenta nije uspio.", "alrAttachSaved": "Dokument je priložen.", "alrAttachTooBig": "Dokument je prevelik! Dokument je premašio {{maxUploadSize}} MB.", "alrAttrDataFailed": "Učitavanje podataka o atributu nije uspjelo", "alrAttrFailed": "Učitavanje podataka o atributu nije uspjelo", "alrAttrSaveFailed": "Pogreška prilikom spremanja atributa!", "alrAttrsLoadFailed": "Učitavanje atributa nije uspjelo", "alrAttachRestored": "Dokument je vraćen.", "alrAttachRestoreFailed": "Obnavljanje dokumenta nije uspjelo.", "alrAttachRestoring": "Vraćanje ...", "alrAuthMethodsFailed": "Učitavanje metoda provjere autentičnosti nije uspjelo.", "alrBadLogin": "Netočno korisničko ime ili lozinka.", "alrBlockedPopups": "Vjerojatno su pop-up pro<PERSON><PERSON> b<PERSON><PERSON>.", "alrCaseDataLoadFailed": "Učitavanje podataka o slučaju nije uspjelo.", "alrCaseDeleteFailed": "Brisanje slučaja nije uspjelo.", "alrCaseDeleted": "Slučaj je izbri<PERSON>!", "alrCaseDeleting": "B<PERSON><PERSON><PERSON> s<PERSON> ...", "alrCaseNameLoadFailed": "Učitavanje naziva slučaja nije uspjelo.", "alrCaseNoRights": "Nemate <PERSON> za pregled slučaja br. {{id}}.", "alrCaseNotFound": "Slučaj nije pronađen – možda je izbrisan", "alrCaseOverviewFailed": "Učitavanje podataka o slučaju za čitanje (PREGLED SLUČAJA) nije uspjelo.", "alrCaseSuspended": "Slučaj je obustavljen!", "alrCaseVarsLoadFailed": "Učitavanje varijabli procesa nije uspjelo.", "alrCaseWakeUpFailed": "Probijanje slučaja nije uspjelo.", "alrCaseWakedUp": "Slučaj se probudio!", "alrCaseWakingUp": "<PERSON><PERSON><PERSON><PERSON> buđenja ...", "alrColsWidthsSettingsFailed": "Spremanje postavke širine stupaca nije uspjelo.", "alrConnToServerFailed": "Veza s poslužiteljem nije uspjela.", "alrConnectionDataLoadFailed": "Učitavanje podataka veze nije us<PERSON>jelo.", "alrConnectionDeleteFailed": "Brisanje veze nije us<PERSON>lo.", "alrConnectionDeleted": "Veza je i<PERSON>bri<PERSON>a!", "alrConnectionSaveFailed": "Spremanje veze nije uspjelo!", "alrContainerNotFound": "Container wasn't found.", "alrCsvDownloaded": "CSV datoteka je preuzeta!", "alrCvNotFound": "<PERSON><PERSON> nije pronađen.", "alrDashboardSettingsFailed": "<PERSON><PERSON> uspjelo spremanje postavki nadzorne ploče.", "alrDefaultDashboardLoadFailed": "Zadano opterećenje nadzorne ploče nije uspjelo.", "alrDefaultDashboardSaved": "Zadana nadzorna ploča je spremljena!", "alrDeleteFailed": "Došlo je do pogreške tijekom brisanja.", "alrDeleted": "Izbrisano!", "alrDeleting": "Brisanje ....", "alrDiagramDataLoadFailed": "Učitavanje podataka za izradu dijagrama nije uspjelo.", "alrDiagramEditToSave": "Dijagram se ne može spremiti i pretvoriti u predložak – sadrži više od jednog procesa! Ažurirajte dijagram tako da sadrži samo jedan proces ili uvezite drugu .bpmn datoteku.", "alrDiagramInitFailed": "Pokretanje dijagrama nije us<PERSON>jelo.", "alrDiagramMissingTaskName": "Ispunite nazive svih zadataka.", "alrDiagramErrors": "Dijagram sadrži pogreške. Popravite ih i pokušajte spremiti ponovo.", "alrDiagramNotValid": "XML nije važeći prema službenoj specifikaciji BPMN 2.0!", "alrDiagramProcessCount": "Dijagram se ne može spremiti i pretvoriti u predložak – on sadrži više od jednog procesa!", "alrDiagramSaveFailed": "<PERSON><PERSON><PERSON> je do pogreške prilikom spremanja predloška!", "alrDiagramTsksDeleteFailed": "<PERSON><PERSON><PERSON> je do pogreške prilikom brisanja zadataka!", "alrDiagramUnchanged": "Predložak ostaje nepromijenjen.", "alrDmsColsLoadFailed": "Stupce za DMS nije moguće učitati.", "alrDocsColumnsIdsFailed": "ID stupaca tablice dokumenata nije moguće učitati.", "alrDocumentAdding": "Spremanje dokumenta ...", "alrDynListsDataLoadFailed": "Učitavanje podataka dinamičkih popisa nije uspjelo.", "alrDynTableColsDataFailed": "Nije moguće učitati podatke u stupce dinamičke tablice.", "alrDynTableDataLoadFailed": "Učitavanje podataka dinamičke tablice nije uspjelo.", "alrDynTableNotFound": "Dinamička tablica nije pronađena.", "alrDynTablesDataLoadFailed": "Učitavanje podataka dinamičkih tablica nije uspjelo.", "alrCalcScriptsDataLoadFailed": "Globalni skripti za proračun nisu se mogli učitati.", "alrEditValues": "Ispravite pogrešno unesene vrijednosti.", "alrEventSaveFailed": "Spremanje događaja nije us<PERSON>lo.", "alrEventSaved": "Događaj je spremljen!", "alrEventSaving": "Spremanje događaja ...", "alrEventTriggered": "Događaj je pokrenut!", "alrExcelDownloaded": "Datoteka xlsx je preuzeta!", "alrExportCompleted": "Izvoz je dovršen.", "alrExportFailed": "Izvoz nije uspio.", "alrExportPreparing": "Priprema izvoza...", "alrFailed": "<PERSON><PERSON><PERSON> nije <PERSON>.", "alrFailedCalendarTask": "Učitavanje zadatka u kalendar nije uspjelo.", "alrFailedCreatePrint": "<PERSON><PERSON><PERSON> ispisa nije us<PERSON>.", "alrFailedDLTotalCount": "Na dinamičkom popisu nije bilo navedenog ukupnog broja {{label}}, stoga su učitani svi zapisi.", "alrFailedData": "Učitavanje podataka nije uspjelo.", "alrFailedEventStart": "Događaj se nije pok<PERSON>.", "alrFailedEventVariables": "<PERSON><PERSON> mogu<PERSON>e učitati varijable odabranog događaja.", "alrFailedEvents": "Učitavanje događaja nije us<PERSON>lo.", "alrFailedFoldersData": "<PERSON>je moguće učitati podatke mape.", "alrFailedFormData": "<PERSON><PERSON> mogu<PERSON>e učitati podatke obrasca. ", "alrFailedInitiatorName": "<PERSON><PERSON> uspjelo učitavanje naziva inicijatora.", "alrFailedLabelData": "Podaci o komponenti oznake nisu se učitali.", "alrFailedLoad": "<PERSON><PERSON> uspjelo učitavanje podataka za ispis.", "alrFailedLogicalType": "Logička vrsta nije se uspjela učitati.", "alrFailedMultiBoxData": "<PERSON><PERSON> mogu<PERSON>e učitati podatke za MultiBox.", "alrFailedNewCase": "Do<PERSON><PERSON> je do pogreške tijekom postavljanja novog slučaja!", "alrFailedNewFolder": "Promjena naziva mape nije uspjela.", "alrFailedNoticeData": "Učitavanje podataka upozorenja nije uspjelo.", "alrFailedOrgUnitUser": "Organizacijske jedinice korisnika nisu se učitale.", "alrFailedOverviewData": "Učitavanje nije us<PERSON>.", "alrFailedPlanData": "<PERSON>je uspjelo učitavanje podataka o planu.", "alrFailedPostData": "Slanje podataka nije us<PERSON>lo.", "alrFailedPrintData": "<PERSON><PERSON> mogu<PERSON>e učitati podatke za ispis.", "alrFailedRevisionInfo": "Učitavanje novih podataka o reviziji nije uspjelo.", "alrFailedSearchBoxData": "Podaci iz SearchBox-a nisu se učitali.", "alrFailedSelectBoxData": "Podaci komponente komponente SelectBox nisu se učitali.", "alrFailedSuggestBoxData": "Podaci sugestora nisu uspjeli učitati.", "alrFailedTasColors": "TAS boje se nisu uspjele učitati!", "alrFailedTaskHandOver": "Prenos zadatka nije uspio.", "alrFailedTemplateProcesses": "Predlošci slučajeva nisu se učitali.", "alrFailedVarData": "<PERSON><PERSON> mogu<PERSON>e učitati varijabilne podatke.", "alrFileAdded": "Datoteka je dodana!", "alrFileDeleteFailed": "<PERSON><PERSON><PERSON>je datoteke nije us<PERSON>.", "alrFileDonwload": "<PERSON>uz<PERSON>je da<PERSON> ...", "alrFileDownloaded": "Datoteka je preuzeta!", "alrFileInfoFailed": "Podaci o datoteci nisu se uspjeli učitati.", "alrFileMetaSaveFailed": "<PERSON><PERSON> mog<PERSON>e spremiti metapodatke datoteke.", "alrFileSavedLikeAttach": "Datoteka je spremljena kao dokument.", "alrFileUploadFailed": "Učitavanje datoteke nije us<PERSON>jelo.", "alrFillAllRequired": "Da biste dov<PERSON><PERSON><PERSON>, morate ispuniti sve potrebne podatke!", "alrFillData": "Da biste dodije<PERSON>, svi podaci moraju biti ispravno ispunjeni!", "alrFillDataInRightFormat": "Ispunite podatke u ispravnom formatu.", "alrFillDataToCompleteTsk": "Da biste dov<PERSON><PERSON><PERSON>, svi podaci moraju biti ispravno ispunjeni!", "alrFillNameAndPass": "<PERSON><PERSON><PERSON> unesite ime i lozinku.", "alrFillNote": "Ispunite tekst napomene.", "alrFillRequiredItems": "Ispunite potrebne stavke.", "alrFolderDataFailed": "Učitavanje podataka mape nije uspjelo.", "alrFolderDataLoadFailed": "Učitavanje podataka mape nije uspjelo.", "alrFolderFailed": "Učitavanje informacija o mapi nije uspjelo.", "alrFolderSaveFailed": "Mapa nije mogla biti spremljena!", "alrFoldersLoadFailed": "Mape nije moguće učitati.", "alrHelpSettingsSaveFailed": "Postavke pomoći nije moguće spremiti.", "alrHistoricalTskInfoFailed": "Učitavanje podataka o povijesnim zadacima nije uspjelo.", "alrHistoricalVarsSaveFailed": "Povijesne varijable nije moguće spremiti.", "alrHistoricalVarsSaved": "Spremljene su povijesne varijable zadatka.", "alrInvLogginHash": "Neispravna prijava.", "alrJsonFailed": "Nije važeći JSON!", "alrLackOfPermsToEdit": "Nemate dopuštenja za uređivanje! Vlasnik je", "alrLackOfPermsToSleepCase": "Nemate dopuštenja za obustavu postupka.", "alrLackOfPermsToWakeUpCase": "Nemate dopuštenja za buđenje slučaja.", "alrLastHistoricalTskIdFailed": "Id zadnjeg povijesnog zadatka nije se uspio u<PERSON>.", "alrLoadAttachmentsFailed": "Učitavanje dokumenata nije us<PERSON>jelo.", "alrLogOutFailed": "<PERSON><PERSON><PERSON><PERSON> nije <PERSON>.", "alrLoginExpired": "Sesija za prijavu je istekla, prijavite se ponovno.", "alrMappingFailed": "Mapiranje nije moguće spremiti.", "alrMappingTsksFailed": "<PERSON>je mogu<PERSON>e učitati mapiranje zadataka.", "alrNewCaseBased": "Novi slučaj postavljen!", "alrNewFolder": "St<PERSON>ena je nova mapa.", "alrNewFolderFailed": "Izrada nove mape nije uspjela.", "alrNextTskOpened": "Sljedeći zadatak je otvoren.", "alrNoDataToPrint": "Podaci za ispis nisu pronađeni.", "alrNoteAdded": "Bilješka je dodana!", "alrNoteSaveFailed": "Spremanje bilješke nije uspjelo.", "alrNoteSaving": "Spremanje bilješke ...", "alrNotesLoadFailed": "Učitavanje bilježaka slučaja nije uspjelo.", "alrOrgUnitDataFailed": "Učitavanje podataka organizacijske jedinice nije uspjelo.", "alrOrgUnitDeleteFailed": "Brisanje organizacijske jedinice nije uspjelo.", "alrOrgUnitDeleted": "Organizacijska jedinica je izbrisana!", "alrOrgUnitDeleting": "Brisanje org. jedinica ...", "alrOrgUnitSaveFailed": "Spremanje organizacijske jedinice nije uspjelo.", "alrOrgUnitSaved": "Organizacijska jedinica je spremljena!", "alrOrgUnitSaving": "Spremanje organizacijske jedinice ...", "alrOverviewDataLoadFailed": "Učitavanje podataka pregleda nije uspjelo.", "alrOverviewSaveFailed": "Spremanje pregleda nije uspjelo!", "alrOverviewSaveSameNameFailed": "<PERSON>v preglednosti već upotrebljavate vi ili drugi korisnik, odaberite drugo ime za pregled.", "alrGraphSaveSameNameFailed": "<PERSON>v preglednosti već upotrebljavate vi ili drugi korisnik, odaberite drugo ime za pregled.", "alrReportSaveSameNameFailed": "Naziv grafikona već upotrebljavate vi ili drugi korisnik, odaberite drugo ime za grafikona.", "alrOverviewsLoadFailed": "Pregledi učitavanja nisu us<PERSON>li.", "alrPassSaveFailed": "Spremanje zaporke nije uspjelo.", "alrPassSaved": "Zaporka je spremljena!", "alrPlanReqItems": "Da biste spremili plan, ispunite potrebne stavke.", "alrPlanSaveFailed": "Spremanje plana nije uspjelo.", "alrPlanSaved": "Plan je spremljen!", "alrPreparingPrint": "Priprema ispisa ...", "alrPrintDeleteFailed": "<PERSON><PERSON><PERSON><PERSON> ispisa nije us<PERSON>.", "alrPrintDeleted": "<PERSON><PERSON> je i<PERSON>!", "alrPrintSaveFailed": "Spremanje ispisa nije us<PERSON>lo.", "alrPrintSaved": "<PERSON><PERSON> je spremljen!", "alrReadOnlyCaseDataFailed": "Učitavanje podataka o slučaju za čitanje nije uspjelo.", "alrRecalcFailed": "Pogreška tijekom ponovnog izračuna!", "alrRecalculating": "Ponovno izračunavanje ...", "alrRestorTemplFailed": "Vraćanje predloška nije uspjelo.", "alrRoleDataLoadFailed": "Učitavanje podataka uloge nije uspjelo.", "alrRoleDeleteFailed": "<PERSON><PERSON><PERSON><PERSON> ul<PERSON>e nije us<PERSON>.", "alrRoleDeleted": "Uloga je izbri<PERSON>a!", "alrRoleDeleting": "<PERSON><PERSON><PERSON><PERSON> ...", "alrRoleSaveFailed": "Spremanje uloge nije us<PERSON>.", "alrRoleSaved": "Rolel je spremljen!", "alrRoleSaving": "S<PERSON><PERSON><PERSON><PERSON> ul<PERSON> ...", "alrRunEvent": "Pokretanje događaja ...", "alrSaveFailed": "Spremanje nije us<PERSON>.", "alrSaved": "Spremljeno!", "alrSaving": "Spremanje...", "alrSavingBeforeRecalcFailed": "Pogreška tijekom spremanja prije ponovnog izračuna!", "alrSavingFailed": "Pogreška tijekom spremanja!", "alrSavingPlan": "Spremanje plana ...", "alrSavingPrint": "Spremanje ispisa ...", "alrSearchNoResults": "Nema rezultata koji odgovaraju parametrima pretraživanja.", "alrSearchRequestFailed": "Pogreška tijekom slanja zahtjeva!", "alrSearching": "Traženje...", "alrSettFailed": "Postavke nije moguće spremiti.", "alrSettSaved": "Postavke su spremljene.", "alrSettingsLoadFailed": "<PERSON><PERSON> uspjelo učitavanje podataka o postavkama.", "alrSleepCaseFailed": "Obustavljanje postupka nije uspjelo.", "alrStoreNameNotDefined": "<PERSON><PERSON><PERSON> \"{{storeName}}\" nije definirana.", "alrStoreNotDefined": "<PERSON><PERSON><PERSON> nije definirana.", "alrSubActionNotDefined": "SubAction i sufiks moraju biti definirani.", "alrSubStoreNotDefined": "SubStore i sufiks moraju biti definirani.", "alrSuggestBoxDataNotContains": "Podaci iz predloška \"{{label}}\" ne sadr<PERSON>e \"{{prop}}\"!", "alrSuspendingCase": "Obustavljanje slučaja ...", "alrTableDataFailed": "Učitavanje podataka tablice nije uspjelo.", "alrTasNewVersion": "Pronađena je nova verzija aplikacije.", "alrRefresh": "<PERSON><PERSON><PERSON><PERSON> je {{- spanRefresh}} na stranicu u pregledniku.", "alrTasVersionLoadFailed": "Učitavanje verzije aplikacije nije uspjelo!", "alrTaskHandOver": "Predaja zadatka ...", "alrTaskHandedOver": "Zadatak je predan korisniku:", "alrTaskNoRights": "Nemate <PERSON> za pregled zadatka br. {{id}}.", "alrTaskNotFound": "Zadatak nije pronađen.", "alrTempDataLoadFailed": "<PERSON><PERSON> uspjelo učitavanje podataka predložaka.", "alrTempHeadersLoadFailed": "Zaglavlja predloška nisu se mogla učitati.", "alrTempDeleteFailed": "Brisanje predloška nije us<PERSON>jelo.", "alrTempDeleted": "Predložak je izbrisan!", "alrTempFoldersLoadFailed": "Učitavanje predložaka nije uspjelo.", "alrTempNameLoadFailed": "<PERSON><PERSON> uspjelo učitavanje naziva predloška.", "alrTempRestored": "Predložak je vraćen sa statusom Razvijeno.", "alrTempSaveFailed": "Spremanje predloška nije uspjelo.", "alrTempsLoadFailed": "Učitavanje predložaka nije uspjelo.", "alrTempVarDataLoadFailed": "Nije moguće učitati podatke o varijabli predloška.", "alrTempVarSaveFailed": "Ušteda varijable nije us<PERSON>la.", "alrTempVarsLoadFailed": "<PERSON><PERSON> mogu<PERSON>e učitati varijable predloška.", "alrTotalCountFailed": "Ukupno prebrojavanje zapisa u tablici nije uspjelo.", "alrTreeDataFailed": "Učitavanje podataka o stablu nije uspjelo.", "alrTskAddFailed": "Dodavanje zadatka nije us<PERSON>lo.", "alrTskAdded": "<PERSON>adatak je dodan!", "alrTskAdding": "Dodavanje zadatka ...", "alrTskAssignFailed": "Dodjela zadatka nije us<PERSON>.", "alrTskAssigned": "Zadatak je dodijeljen.", "alrTskCompleteFailed": "Pogreška tijekom dovršavanja zadatka.", "alrTskDataLoadFailed": "Podaci zadatka nisu se uspjeli učitati.", "alrTskDeleteFailed": "Brisanje zadatka nije us<PERSON>lo.", "alrTskDeleted": "Zadatak je izbrisan!", "alrTskNameLoadFailed": "<PERSON><PERSON> uspjelo učitavanje naziva zadatka.", "alrTskRecalculated": "Zadatak je ponovno izračunat!", "alrTskSaveFailed": "Pogreška tijekom spremanja zadatka.", "alrTskSaved": "Zadatak spremljen!", "alrTskSavedAndCompleted": "Zadatak spremljen i dovršen!", "alrTskScheduleFailed": "Raspoređivanje zadataka nije us<PERSON>jelo.", "alrTskScheduled": "Zadatak je zakazan.", "alrTskTakeFailed": "Preuzimanje zadatka nije uspjelo.", "alrTskTaken": "Zadatak je preuzet.", "alrTskTakign": "Prelazak na zadatak ...", "alrTsksMappingFailed": "Učitavanje zadataka mapiranja nije uspjelo.", "alrUserDataLoadFailed": "Učitavanje korisničkih podataka nije uspjelo.", "alrUserDeleteFailed": "Brisanje korisnika nije us<PERSON>.", "alrUserDeleted": "Korisnik je izbri<PERSON>!", "alrUserDeleting": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> ...", "alrUserIsNotActive": "Korisnik nije aktivan.", "alrUserNotLoaded": "<PERSON><PERSON>lo učitavanje korisnika.", "alrUserParamsLoadFailed": "<PERSON><PERSON> uspjelo učitavanje korisničkih parametara.", "alrUserSaveFailed": "Spremanje korisnika nije us<PERSON>jelo.", "alrUserSaved": "Korisnik je spremljen!", "alrUserSaving": "Spremanje korisnika ...", "alrUserStatusChangeFailed": "Promjena statusa korisnika nije uspjela.", "alrUserStatusChanged": "Status korisnika je promijenjen!", "alrUserStatusChanging": "Promjena statusa korisnika ...", "alrVarDeleteFailed": "<PERSON><PERSON><PERSON>je varijable nije us<PERSON>lo.", "alrVarDeleted": "Varijabla je izbrisana!", "alrVarSaveFailed": "Spremanje varijable nije uspjela.", "alrVarSaved": "Varijabla je spremljena.", "alrVarSaving": "Spremanje varijable ...", "alrVarsForModalFilterFailed": "Učitavanje varijabli modalnog filtra nije uspjelo.", "alrVarsLoadFailed": "<PERSON><PERSON> mogu<PERSON>e učitati varijable.", "alrVarsOrderLoadFailed": "Učitavanje naloga varijabli nije us<PERSON>jelo.", "alrVarsOrderSaveFailed": "<PERSON><PERSON> us<PERSON>lo spremanje narudžbe varijabli.", "alrViceDeleted": "Vice je o<PERSON><PERSON><PERSON>.", "alrViceFailed": "Vice nije <PERSON>.", "alrViceNotFound": "Vice nije nađeno!", "alrViceSaveFailed": "Vice nije mogu<PERSON>e spremiti.", "alrViceSaved": "Vice je spremljeno!", "alrViceSaving": "Spremanje vice...", "always": "Stalno", "annually": "Godišnje", "assHierarchy": "<PERSON><PERSON><PERSON> prema referent<PERSON><PERSON> o<PERSON>bi", "assHierarchyAncestors": "Svi nadređeni referentne o<PERSON>be", "assHierarchyChildren": "Izravni podređeni referentne osobe", "assHierarchyDescendants": "Svi podređeni referentne osobe", "assHierarchyGuarantor": "<PERSON><PERSON> referent<PERSON> o<PERSON>", "assHierarchyParent": "Izravno nadređena osoba", "assHierarchySiblings": "<PERSON><PERSON><PERSON> referent<PERSON>", "assMethodAutomatic": "Uz računalo", "assMethodLastSolver": "Zadnjeg vlasnika zadatka", "assMethodLastSolverChoice": "Vlasnika zadatka odabrao je najčešći vlasnik zadatka", "assMethodLeast": "Vlasnik zadatka s najmanjim brojem zadataka", "assMethodPull": "<PERSON><PERSON><PERSON>, zadatak će biti ponuđen", "assMethodSelect": "Vlasnike zadataka odabrao je nadzornik zadatka", "assMethodVariable": "Vlasnik zadatka, iz varijable", "assessmentOfConds": "Procjena u<PERSON>", "assign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignAttrs": "<PERSON><PERSON><PERSON><PERSON> atributa", "assignAttrsLogType": "Dodjela atributa logičkom tipu dokumenta", "assigned": "Dodijeljeno", "assigningRoles": "Dodjeljivanje ul<PERSON>", "assignments": "<PERSON><PERSON><PERSON>", "attachments": "Dokumenti", "attachmentsList": "Lista dokumenata", "attribute": "Atribut", "attributeNew": "Atribut – Novo", "availableVars": "Dost<PERSON><PERSON> varijable", "body": "Body", "borders": "Borders", "byFolders": "<PERSON>", "byOrganization": "Po organizaciji", "byRole": "<PERSON>", "calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculations": "<PERSON><PERSON><PERSON><PERSON>je", "calendar": "<PERSON><PERSON><PERSON>", "carriedIfNoOther": "Izvršit će se ako nema drugih", "case": "<PERSON><PERSON><PERSON><PERSON>", "caseCreation": "<PERSON><PERSON><PERSON><PERSON>", "caseGraph": "Dijagram uzorka", "caseNoEvents": "Slučaj ne sadrži događaje.", "caseNum": "<PERSON><PERSON><PERSON><PERSON> br.", "caseOwner": "Vlasnik slučaja", "caseStatus": "Status slučaja", "caseVar": "<PERSON><PERSON><PERSON><PERSON>", "cases": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casesWithProblem": "<PERSON><PERSON>i s <PERSON>om", "category": "Kategorija", "changeTaskSolver": "Promijeni rješavača zadatka", "changedBy": "Promijenjeno od", "changedWhen": "Promijenjeno (kada)", "checkbox": "Checkbox", "checkboxList": "Checkbox lista", "choosePrint": "Predložak ispisa", "chooseUserToAssignTsk": "Odaberite korisnika koji će biti dodijeljen zadatku", "choosenAttrs": "Odabrani atributi", "city": "Grad", "class": "Vrsta", "clickToClose": "Zatvorite klikom", "clickToRefresh": "Kliknite da biste ažurirali stranicu u pregledniku", "clickToRepeat": "Ponovite postupak klikom na", "clientLanguage": "<PERSON><PERSON><PERSON>", "cloneRow": "Dvostruka crta", "close": "Zatvori", "closeAll": "Zatvori sve", "coWorkersOfPlanGuar": "Suradnici plana", "color": "<PERSON><PERSON>", "colors": "<PERSON><PERSON>", "column": "Kolona", "columnName": "Naziv stupca", "comment": "Komentar", "complete": "Zav<PERSON>š<PERSON>", "completion": "Završetak", "componentDescription": "Opis komponente", "condition": "Uvjet", "conditions": "U<PERSON>jet<PERSON>", "confirmAttachDeletion": "Želite li zaista izbrisati dokument?", "confirmDeleteDialog": "Ž<PERSON>te li zaista izbrisati {{variable}}?", "confirmDialogEventSave": "Za prebacivanje potrebno je spremanje događaja. Želite li ga spremiti?", "confirmResetDashboard": "Želite li ponovno postaviti nadzornu ploču?", "confirmSaveChanges": "Spremite promjene?", "confirmSaveDiagramChanges": "Spremite promjene u dijagramu?", "confirmSaveTaskChanges": "Spremite promjene u zadatku?", "confirmRestoreDialog": "<PERSON><PERSON><PERSON> vratiti {{variable}}?", "confirmSaveNote": "Želite li spremiti bilješku?", "confirmSleepCase": "Želite li stvarno obustaviti slučaj?", "confirmTakeoverTsk": "Stvarno želite preuzeti zadatak?", "confirmWakeUpCase": "Želite li zaista prekinuti slučaj?", "connection": "<PERSON><PERSON><PERSON>", "connectionFailed": "Veza s poslužiteljem nije uspjela.", "connectionVar": "link", "constant": "Konstantno", "contact": "Kontakt", "contactTaskOwner": "Obratite se vlasniku zadatka", "containerSettings": "Postavke spremnika", "contains": "<PERSON><PERSON><PERSON><PERSON>", "continueSolving": "Nastaviti s rješenjem", "copied": "<PERSON><PERSON><PERSON>!", "copy": "<PERSON><PERSON><PERSON>", "copyShortcut": "Pritisni Ctrl+C", "copyToClipboard": "Kopirati u međuspremnik", "createForm": "Napravite obrazac", "csv": "csv", "csvFile": "CSV file", "customPrint": "Prilagođeni ispis", "daily": "Dnevno", "dashCvNoOverview": "<PERSON>ema oda<PERSON> pre<PERSON>a – odabirete ga u postavkama spremnika", "dashCvNoRights": "Nemate dopuštenja za priakz pregleda. Obratite se <PERSON>u.", "dashFavNoShortcut": "<PERSON>su odabrani nikakvi filteri – odabirete ih u postavkama spremnika", "dashboard": "Nadzorna ploča", "date": "Datum", "dateList": "LOV datuma", "day": "dan", "dayE": "<PERSON>", "daysDHM": "Dani: (dd:hh:mm)", "defChangeVarInfluence": "Ova promjena definicije varijable će se širiti u već izrađene slučajeve.", "defChangeInfluence": "Ta će se promjena definicije širiti u već izrađene slučajeve.", "defaultCaseName": "Zadani naziv slučaja", "defaultLbl": "Zadano {{label}}", "defaultLblShe": "Zadano {{label}}", "defaultLblIt": "Zadano {{label}}", "defaultPrintProcess": "Zadano – proces", "defaultPrintTask": "Zadano – zadatak", "defaultValue": "Zadana vrijednost", "delUser": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteCol": "Bris<PERSON>je s<PERSON>", "deleteRow": "<PERSON><PERSON><PERSON><PERSON>", "deleteSelected": "Obriši odabrano", "deleted": "Izbrisano", "deletedOn": "Izbrisano", "deletedShe": "Izbrisano", "description": "Opis", "deselect": "Poništite odabir", "detail": "<PERSON><PERSON><PERSON>", "developed": "U razvoju", "dial": "Nazvat<PERSON>", "dic": "PDV", "directSubOfPlanGuar": "Izravno podređeni", "directSupOfPlanGuar": "Izravno nadređeni", "disableFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dmsAssignAttrs": "Dodjela atributa DMS", "dmsAttribute": "DMS atribut", "dmsAttributes": "DMS atributi", "dmsColumns": "DMS – stupci", "dmsVisNull": "Samo u ovom procesu", "dmsVisSub": "U podprocesima", "dmsVisSup": "U nadređenom procesu", "dmsVisSupSub": "U roditeljskim i podređenim procesima", "dmsVisibility": "Dokumenti će se vidjeti", "doNotShowVariablesWith_": "<PERSON>v varijable koji počinje s `_` neće se prikazivati korisnicima", "document": "Dokument", "documentVar": "dokument", "documents": "Dokumenti", "doesNotContain": "ne <PERSON><PERSON><PERSON>i", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "dragAddFiles": "Dodajte datoteke povlačenjem i ispuštanjem ili klikom {{- here }} za odabir datoteka.", "dragAddFile": "Dodajte datoteke povlačenjem i ispuštanjem ili klikom {{- here }} za odabir datoteka.", "here": "ovdje", "dropContainer": "Ispustite spremnik", "dropzoneTip": "Ispustite datoteke ovdje", "dropZoneUserPhoto": "Ovdje ispustite sliku ili kliknite da biste odabrali datoteku za prijenos.", "dueDate": "<PERSON><PERSON>", "duty": "Dužnost", "dynList": "<PERSON><PERSON> popis", "dynRowsDefinition": "Definicija tablice i stupaca", "dynTableName": " Naziv din. tablice", "dynTable": "Dinamička tablica", "dynTables": "Dinamička tablice", "dynamicList": "Dinamički popis", "dynamicRows": "Dinamički redovi", "dynamicTable": "Dinamička tablica", "edit": "<PERSON><PERSON><PERSON>", "editAttribute": "Uredi atribut", "editOrgUnit": "Uredi org. jedinicu", "editRole": "<PERSON><PERSON><PERSON>", "editRule": "<PERSON><PERSON><PERSON>o", "editUser": "<PERSON><PERSON><PERSON>", "editor": "Urednik", "email": "E-mail", "emailsQueue": "E-mail red čekanja", "empty": "Prazno", "end": "<PERSON><PERSON>", "error": "Pogreška", "errored": "S greškom", "error404": "Pogreška 404 – stranica nije pronađena!", "event": "<PERSON><PERSON><PERSON><PERSON>", "events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventsRun": "Pokreni događ<PERSON>", "every": {"masc": "<PERSON><PERSON><PERSON>", "neutral": "<PERSON><PERSON><PERSON>", "repeat": "<PERSON><PERSON><PERSON><PERSON>"}, "everyWorkDay": "<PERSON><PERSON><PERSON> radni dan", "excel": "Excel", "favourites": "<PERSON><PERSON><PERSON><PERSON>", "fax": "Fax", "file": "Datoteka", "fileLogicalType": "Logička vrsta datoteke", "fileName": "<PERSON>v datoteke", "filePlacement": "Položaj u datoteci", "files": "Datoteke", "filter": "Filter", "filterFrom": "Filter od", "filterTitle": "Filter", "filtrate": "Filter", "finishTask": "Završi zadatak", "finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finishedBy": "Završeno od", "finishedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first": "Prvi", "firstLeft": "Prvo lijevo", "firstName": "Ime", "firstRight ": "Prvo <PERSON>", "firstRowColumnsName": "Prvi redak sadrži nazive stupaca", "folder": "Mapa", "folder-": "Mapa –", "folderExecRightsText": "Dodijelite uloge koje će moći pokretati slučajeve u mapi", "folderExecRightsTextOS": "Dodijelite organizacijske jedinice koje će moći pokretati slučajeve u mapi", "folderName": "<PERSON>me mape", "font": "Font", "fontMainHeader": "Font glavnog zaglavlja", "form": "Forma", "fourth": "Četvrti", "freeTsk": "Slobodan zadatak", "fri": "<PERSON><PERSON>", "from": "Od", "fsDescription": "opis", "fsName": "ime", "fsTooltip": "tooltip", "fullName": "<PERSON><PERSON> ime", "fullScreen": "Puni ekran", "getTotalCount": "<PERSON><PERSON><PERSON> broj", "graph": "Grafikon", "handExecutionTaskListEmpty": "Odaberite događ<PERSON>", "handOver": "<PERSON><PERSON><PERSON>", "handOverToUser": "Predajte korisniku", "handover": "<PERSON><PERSON><PERSON>", "headerDashboard": "Zaglavlje nadzorne ploče", "help": "<PERSON><PERSON><PERSON>", "hideLogout": "<PERSON><PERSON><PERSON><PERSON>", "hideNewProcess": "Sakrij 'Novi slučaj'", "hideProcs": "<PERSON><PERSON><PERSON><PERSON>", "hideTasks": "<PERSON><PERSON><PERSON><PERSON>", "historicalValues": "Povijesne vrijednosti", "currentValues": "Stvarne vrijednosti", "history": "<PERSON><PERSON><PERSON><PERSON>", "home": "Home", "html": "HTML", "ic": "ID tvrtke", "id": "ID", "inCasesNames": "U imenima slučajeva", "inTasksNames": "U imenima zadataka", "inDevelopment": "U razvoju", "inEvery": "u svakom", "inFiles": "U datotekama", "initiator": "<PERSON><PERSON><PERSON><PERSON>", "inTasks": "U zadacima", "inactive": "Neaktivan", "inactiveShe": "Neaktivan", "incidences": "opojave", "inclusion": "<PERSON><PERSON>jan<PERSON>", "info": "Info", "inputParams": "Ulazni parametri", "insert": "<PERSON><PERSON><PERSON><PERSON>", "insertAttachTip": "Povucite i ispustite da biste umetnuli dokument", "insertVar": "Umetni <PERSON>lu", "insertSnippet": "Umetnite is<PERSON>čak", "snippet": "Isječak koda", "insertedBy": "Prenio korisnik", "insertedOn": "dodano", "insteadOf": "<PERSON><PERSON><PERSON>", "instructions": "Instrukcije", "invitation": "Pozivnica", "isEmail": "Nije valjana e-pošta", "isEmpty": "prazno je", "isExisty": "<PERSON><PERSON> v<PERSON>", "isManagerOrgUnit": "Je upravitelj org. jedinice", "isNotEmpty": "nije p<PERSON>an", "isRequired": "ovo polje je obavezno", "justSave": "Samo spremi", "keepGlobalOrder": "Keep global order", "key": "Key", "last": "zadnje", "lastName": "Prezime", "lastOwnerOfTask": "Posljednji vlasnik zadatka", "licenceKey": "Licencni ključ", "link": "Link", "linkConditions": "Uvjeti veze", "list": "Lista", "listName": "naziv popisa", "listOfValues": "Popis vrijednosti", "listValue": "vrijednost popisa", "loading": "Učitavanje...", "location": "Lokacija", "locked": "Zak<PERSON><PERSON><PERSON><PERSON>", "logIn": "<PERSON><PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON><PERSON>", "logicalType": "Logički tip", "loginError": "Neispravna prijava.", "loginTimeout": "Vremensko ograničenje sesije (sec.)", "longText": "Dugačak tekst", "mailEscalation": "E-pošta s pregledom eskaliranih zadataka", "mailProcEscalation": "E-pošta s pregledom eskaliranih slučajeva", "mailPromptly": "E-mail obavijest o novom zadatku", "mailPull": "E-mail obavijest o novom zadatku za povlačenje", "mailTotal": "Sažetak e-pošta s pregledom zadatka", "mainButton": "Glavni gumb", "mainColor": "Glavna boja", "mainHeader": "Glavno zaglavlje", "mainLanguage": "<PERSON><PERSON><PERSON>", "manager": "Manager", "managerOfOrgUnit": "Manager org. jedinice", "mandatory": "Obavezno", "manualStartEvent": "Ručni početak događaja", "mapping": "Mapiranje", "mappingSubProcessVars": "Mapiranje varijabli podprocesa", "markAll": "Označi sve", "menu": "<PERSON><PERSON>", "mine": "<PERSON><PERSON>", "mobilePhone": "Mo<PERSON>el", "mon": "Ponedjeljak", "month1": "Siječanj", "month10": "Listopad", "month11": "<PERSON><PERSON><PERSON>", "month12": "Prosinac", "month2": "Vel<PERSON>ča", "month3": "<PERSON><PERSON><PERSON><PERSON>", "month4": "<PERSON><PERSON><PERSON><PERSON>", "month5": "Svibanj", "month6": "June", "month7": "Lipanj", "month8": "<PERSON><PERSON><PERSON>", "month9": "<PERSON><PERSON><PERSON>", "monthI": "mjesec", "monthly": "M<PERSON>seč<PERSON>", "months": "m<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON><PERSON>", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingle", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "Multi-instance", "myUnfinishedTasks": "<PERSON><PERSON>", "name": "Ime", "nested": "Uklopljeni", "never": "Nikad", "new": "Novi", "newCase": "Novi slučaj", "newFolder": "Mapa – Novo", "newForm": "Novi oblik", "newIt": "Novi", "newName": "Novo ime", "newShe": "Novi", "newSolver": "Novi vlasnik zadatka", "no": "Ne", "noAttach": "<PERSON><PERSON> (kliknite za dodavanje)", "clickToAddAttach": "Kliknite za dodavanje", "noName": "<PERSON><PERSON> imena", "noOneBeOffered": "<PERSON>t<PERSON>, zadatak će biti ponuđen ograničenoj skupini korisnika", "noPageRights": "Nemate dopuštenja za pregled ove stranice.", "node": "<PERSON><PERSON>", "notFound": "<PERSON><PERSON>", "notMatch": "Ne podudara se", "notNumber": "<PERSON><PERSON> broj", "notIntNumber": "<PERSON><PERSON> cijeli broj", "notValid": "Ne vrijedi", "notes": "Bilješke", "notesOnContacts": "Napomene o kontaktima", "notice": "Obavijest", "notification": "Obavijest", "nrOfItems": "<PERSON><PERSON><PERSON><PERSON>", "number": "<PERSON><PERSON><PERSON>", "numberList": "LOV of numbers", "ok": "OK", "oneMustBeMet": "<PERSON><PERSON> jedan mora biti ispunjen", "onlyOrgUnit": "Samo organizacijska jedinica", "onlyPlanGuarantor": "Jamac plana samo", "openAll": "Otvori sve", "operating": "Aktivan", "order": "<PERSON><PERSON><PERSON><PERSON>", "orderByColumn": "Redoslijed po stupcu", "orgName": "<PERSON><PERSON> predmeta", "orgStructure": "Org. struk<PERSON>", "orgUnit": "org. jedinica", "orgUnitE": "org. jedinica", "orgUnitName": "Naziv org. jedinice", "orgUnitShe": "Org. jedinica", "orgUnits": "Org. jedinice", "organization": "Organizacija", "overview": "Pregled", "overviewMapping": "Pre<PERSON> mapiranja", "overviewNew": "Pregled – novi", "overviewSetSharing": "Postavite pregled dijeljenja za svaku korisničku skupinu", "overviews": "<PERSON><PERSON><PERSON>", "owner": "Vlasnik", "ownerWithLeastTasks": "Vlasnik zadatka s najmanjim brojem zadataka", "pageNotFound": "Stranica nije pronađena", "parentFolder": "<PERSON><PERSON><PERSON><PERSON> mapa", "parentUnit": "<PERSON><PERSON><PERSON><PERSON>", "participants": "Sudionici", "password": "Šifra", "passwordChallenge": "Obavijest", "passwordChallengeText": "Želite li zaista obavijestiti sve korisnike da promijene zaporku?", "passwordChange": "Promijeni zaporku", "passwordCheck": "Šifra (provjera)", "passwordNew": "Nova zaporku", "passwordNewCheck": "Nova zaporku (provjera)", "paused": "Neaktivan", "personInOrgStr": "Dodijeljena od strane osobe u org. strukturi", "phone": "Telefon", "photo": "Slika", "plan": "Plan", "planGuarantor": "Jamac plana", "planTitle": "Plan", "plans": "Planiranje", "plnOffType": "Ponoviti", "plnOrgUnit": "Organizacijska jedinica", "plnTProc": "Predložak slučaja", "plnUser": "Sponzor plana", "plnUsersSelect": "Restriktivni uvjeti za odabir jednog ili više inicijatora", "prependTsk": "Početni zadatak", "prependedTsk": "Početni zadatak", "primaryKey": "Glavni ključ", "print": "Print", "printTemplate": "<PERSON><PERSON>", "printType": "<PERSON><PERSON><PERSON> ispisa", "printer": "Print – HTML", "priority": "Prioritet", "procDescription": "Opis procesa", "procDueDateFinish": "Rok za završetak slučaja", "procName": "<PERSON><PERSON>", "procOwner": "Vlasnik procesa", "procSummary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "process": "Proces", "processName": "Naziv procesa", "property": "Vlasnik", "quickFilter": "<PERSON><PERSON>i filter", "radioButtonList": "RadioButtonList", "reEvaluates": "Ponovno pro<PERSON>jeniti", "recalc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recipient": "Primatelj", "recipientsId": "ID primatelja", "records": "Zapisi", "referenceUser": "<PERSON><PERSON><PERSON><PERSON> osoba", "refresh": "Osvježi", "registered": "Zabilježen", "relatToPlanSponsor": "Odnos prema sponzoru plana", "remove": "Ukloniti", "removeVice": "Uklonite mane", "renameCols": "Preimenovati stu<PERSON>ce", "repeatLogin": "Ponovite prijavu ili odaberite drugu vrstu provjere autentičnosti.", "repeatOrReport": "Pokušajte ponovno kasnije ili se obratite administratoru.", "repetition": "Ponavljanje", "required": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Reset", "restrictTaskOwners": "Ograničenja za vlasnike zadataka", "restrictUsers": "Ograničite korisnike", "returnSubProcessVars": "Povratak podprocesnih varijabli", "revision": "Revizija", "right": "Pravo", "rightOrDuty": "Pravo / Dužnost", "role": "ul<PERSON>", "roleName": "<PERSON><PERSON>e", "roleSg": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>", "row": "red", "rule": "Pravil<PERSON>", "ruleCSVFile": "Naziv datoteke CSV", "ruleCSVHeader": "<PERSON><PERSON><PERSON> redak CSV datoteke je zaglavlje", "ruleCSVMask": "Naziv datoteke CSV", "ruleCSVSeparator": "Razdjelnik stupaca", "ruleNew": "Pravilo – Novo", "ruleParamsMap": "Mapiranje <PERSON>", "ruleProcOwnCSV": "Definirano u mapiranju", "ruleTypeCSVExpProcs": "Izvezi sve predloške predmeta u CSV", "ruleTypeCSVMrgProcs": "Prema CSV-u pokrenite slučajeve i ažurirajte varijable slučajeva", "ruleTypeCSVRunProcs": "Prema CSV-u pokrenite slučajeve", "ruleTypeCSVUpdProc": "Prema CSV ažurirati varijable slučaja", "ruleTypeCSVUpdProcs": "Prema CSV ažurirati varijable slu<PERSON>eva", "ruleTypeCSVUpdateList": "Ažurirajte dinamički popis prema CSV-u", "ruleTypeReturn": "<PERSON><PERSON><PERSON><PERSON>", "ruleTypeUpdateListOfProcesses": "Ažurirajte dinamički popis procesa", "rules": "<PERSON><PERSON><PERSON>", "run": "Pokreni", "runProcess": "Započnite postupak", "running": "Running", "sat": "Subota", "save": "Sp<PERSON>i", "saveAsAttachment": "Spremite ispis kao dokument u slučaju", "scheduling": "Zakazivanje", "scheme": "Vizualni identitet", "script": "Skripta", "scripts": "Skripte", "search": "Pretraga", "searchResult": "Rezultat pretrage", "second": "<PERSON><PERSON>", "secondLeft": "Drugo lijevo", "secondRight": "<PERSON><PERSON>", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "Računalo će automatski odabrati vlasnike zadataka", "selectedByTaskSupervisor": "Vlasnicima zadataka koje nadzornik odabere", "selectedPrint": "odabrani ispis", "selectedUser": "Odabrani korisnik", "send": "<PERSON><PERSON><PERSON>", "sendingFailed": "Pogreška", "sendOn": "Vrijeme za slanje", "sendTestMail": "Test email", "sequence": "Slijed", "setDefault": "<PERSON><PERSON> kao zadano", "setVice": "Set vice", "setViceAttachmentsNotes": "Pravo na prijenos dokumenata i bilješki", "settings": "Postavke", "shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "showAttachmentsClick": "Klikom ćete prikazati dokumente", "showCommentCol": "Prikaži stupac s komentarima", "skype": "Skype", "solve": "Riješite zadatak", "solvedBy": "Riješeno od", "solver": "Vlasnik zadatka", "sort": "<PERSON><PERSON><PERSON><PERSON>", "sortByColumn": "Poredaj po stupcu", "sorting": "Sortiranje", "sourceTask": "Izvorni zadatak", "sourceVar": "<PERSON>z<PERSON>na varija<PERSON>", "start": "Početak", "startDate": "Početni datum", "startCalDate": "Početni datum", "endCalDate": "<PERSON><PERSON>", "state": "Država", "stateAddress": "Država", "status": "Status", "street": "<PERSON><PERSON><PERSON> ulice", "subProcess": "Sub-proces", "subject": "Predmet", "substitute": "<PERSON><PERSON><PERSON><PERSON>", "sun": "<PERSON><PERSON><PERSON><PERSON>", "superior": "Pretpostavljeni", "supervis": "Nadglednik", "supervisor": "Nadglednik zadatka", "suspend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suspended": "Suspendiran", "suspendedx": "Suspendiran", "tTaskAgain": "Ponavljano ponašanje aktivacije", "tTaskAutoCompleteCaption": "Zadatak će se automatski ispuniti ako", "tTaskCompletionCOA": "ispunjeni su svi uvjeti u isto vrijeme", "tTaskCompletionCOO": "ispunjen je barem jedan uvjet", "tTaskDueOffsetNone": "odmah", "tTaskDueOffsetPO": "unesen od nadzornika", "tTaskDueOffsetPS": "u danima od početka slučaja", "tTaskDueOffsetTS": "unutar nekoliko dana od mogućeg početka aktivnosti", "tTaskDueOffsetVC": "varija<PERSON><PERSON> k<PERSON>", "tTaskDueOffsetVO": "varijabli pri pokretanju", "tTaskInvClassConf": "Tajn<PERSON>", "tTaskInvClassPriv": "Privatno", "tTaskInvClassPubl": "Javno", "tTaskInvPriority1": "1-naj<PERSON><PERSON><PERSON>", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-<PERSON><PERSON><PERSON><PERSON><PERSON>", "tTaskInvokeEventB": "u pozadini", "tTaskInvokeEventI": "odmah", "tTaskReferenceUserLastSolver": "Posljednji vlasnik zadatka", "tTaskReferenceUserMan": "Voditelj org. jedinica xy", "tTaskReferenceUserUser": "Korisnik xy", "tTaskRunOnlyOnce": "Pokreni samo jednom", "tTaskSufficientEnd": "Dovršenje cijelog slučaja", "tabName": "Naziv kartice", "table": "Tablica", "takeOnlyOrder": "Uzmi samo narudž<PERSON>", "takeover": "<PERSON><PERSON><PERSON>", "targetTask": "Ciljni zadatak", "targetVar": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>", "taskAutomatic": "automatski status", "taskEmailNotification": "Obavijest e-mailom", "taskEvent": "pokreće događaj", "taskEventWait": "čeka događaj", "taskOwner": "Vlasnik zadatka", "taskSolverAssign": "za dodjeljivanje vlasniku zadatka", "taskStart": "Start", "taskStatus": "Status", "taskStatusA": "Aktivan", "taskStatusAP": "Aktivni podproces", "taskStatusAS": "Podproces spavanja", "taskStatusD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskStatusL": "Čekanje", "taskStatusLEdit": "<PERSON><PERSON> moguće urediti zadatak na čekanju", "taskStatusN": "Novi", "taskStatusP": "<PERSON><PERSON><PERSON>", "taskStatusS": "Suspendiran", "taskStatusT": "<PERSON><PERSON><PERSON><PERSON>", "taskStatusW": "<PERSON><PERSON>", "taskStatusWT": "Za zakazivan<PERSON>", "taskSubprocess": "provodi se podprocesom", "taskTabVariables": "Do<PERSON><PERSON><PERSON><PERSON><PERSON> varijable", "taskType": "Vrsta zadatka", "taskWillBeAssigned": "Zadatak će biti dodijeljen", "tasks": "<PERSON><PERSON><PERSON>", "tasksToPull": "Zadaci za povlačenje", "taskstatusAD": "Aktivan i završen", "tempId": "ID predloška", "tempVar": "<PERSON>dl<PERSON><PERSON><PERSON>", "template": "Predložak", "templateDeleted": "Izbrisano", "templateStatus": "Status predloška", "templates": "Pre<PERSON><PERSON>š<PERSON>", "templatesFolder": "Predložak – mapa", "testForm": "Obrazac za testiranje", "tested": "Testirano", "text": "Tekst", "textList": "LOV of texts", "textMultipleLines": "Tekst s više redaka", "textSuggest": "Predlagatelj", "third": "<PERSON><PERSON><PERSON><PERSON>", "thirdCenter": "T<PERSON>ći centarr", "thu": "Četvrtak", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "to": "Do", "toHide": "<PERSON><PERSON><PERSON><PERSON>", "toInclusive": "Do (uključujući)", "toPull": "Zadaci za povlačenje", "tooltip": "Opis", "total": "total", "tprocName": "Predložak procesa ", "tsk": "Zadatak", "tskAssignDues": "Postavite vremenska ograničenja za taj zadatak", "tskName": "Naziv zadatka", "tskNum": "<PERSON><PERSON><PERSON>", "tskSolver": "Vlasnik zadatka", "tskTemplate": "Predložak zadatka", "tskVar": "zadatak", "tsksDone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsksSolvers": "Vlasnici zadataka", "ttAdd": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućuje dodavanje nove stavke ili novih parametara koji još nisu definirani."}, "ttAddActivity": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddAttach": {"heading": "Dodati dokument", "body": "Omogućuje dodavanje novog dokumenta."}, "ttAddAttribute": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddContainer": {"heading": "Dodajte spremnik", "body": "Dodaje spremnik s odabranim sadržajem"}, "ttAddFile": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddStructure": {"heading": "Dodajte stavku u organizacijsku strukturu", "body": "Omogućuje dodavanje nove stavke organizacijske strukture ili novih parametara koji još nisu definirani."}, "ttAddTemp": {"heading": "Do<PERSON>j novi predlo<PERSON>", "body": "Izrada novih predložaka slučajeva. Vlasnik predloška bit će trenutno prijavljeni korisnik. Predlošku se automatski dodjeljuje \"u razvoju\" status."}, "ttAddTsk": {"heading": "Dodaj novi zadatak", "body": "zrada novog zadatka unutar predloška procesa. Parametri zadatka mogu se specificirati ovisno o vrsti zadatka. Veze na druge zadatke mogu se dodati ili urediti na karticama Grafikon ili Link."}, "ttAddTskGraph": {"heading": "Dodaj novi zadatak", "body": "Izrada novog zadatka unutar predloška procesa. Parametri zadatka mogu se specificirati ovisno o vrsti zadatka. Veze na druge zadatke mogu se dodati ili urediti na karticama Grafikon ili Link."}, "ttAddUser": {"heading": "Dodaj novog korisnika", "body": "Dodaj novog korisnika. Svaki korisnik mora imati jedinstveno korisničko ime. Za korisnike se mogu postaviti osnovne informacije, kao i njihova dodjela organizacijskoj strukturi i dodjeli uloga. Novi korisnici automatski dobivaju status zaključanog."}, "ttAddVar": {"heading": "Dodaj novu varijablu", "body": "Izrada nove varijable unutar predloška slučaja. Svaka varijabla sadrži informacije s kojima mogu surađivati vlasnici zadataka. Moguće je odrediti ime, vrstu i zadane vrijednosti varijable."}, "ttAddVice": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAssignAttribute": {"heading": "Dodjela atributa logičkoj vrsti dokumenta", "body": ""}, "ttAssignTsk": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Omogućuje dodjelu zadataka određenom vlasniku zadatka ili dodavanje stavke u definiranu strukturu."}, "ttCases": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttOverviews": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttChangePass": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Uređivanje lozinki korisnika kojima se upravlja izravno u okruženju aplikacije. Ako korisnicima upravlja vanjska usluga (LDAP), njome se mora upravljati."}, "ttClose": {"heading": "Zatvori", "body": "Prozor će biti zatvoren bez spremanja promjena."}, "ttCloseTemp": {"heading": "Zatvori", "body": "Prozor s definicijom predloška bit će zatvoren."}, "ttCompleteTsk": {"heading": "Završi zadatak", "body": "Potvrđuje da je zadatak obavljen i šalje ga na daljnju obradu kao što je prethodno definirano."}, "ttContact": {"heading": "Kontakt", "body": "Prikazuje kontakte za nadzornika zadatka."}, "ttContainerSettings": {"heading": "Postavke", "body": "Omogućuje promjenu postavki za određeni spremnik."}, "ttCopyHdr": {"heading": "<PERSON><PERSON><PERSON>", "body": "Izrada kopije odabranog zaglavlja. Odabir obavlja se klikom na tablicu zaglavlja predložaka."}, "ttCopyTemp": {"heading": "<PERSON><PERSON><PERSON>", "body": "Izrada kopije odabranog predloška. Odabir predloška obavlja se klikom na tablicu procesnih predložaka."}, "ttCopyVar": {"heading": "<PERSON><PERSON><PERSON> varija<PERSON>", "body": "Definicija kopija za odabranu varijablu i spremanje varijable pod novim imenom. Varijable se odabiru klikom na tablicu varijabli."}, "ttDel": {"heading": "Izbrisati", "body": "Izbrisati odabranu stavku."}, "ttDelAttach": {"heading": "Brisanje dokumenta", "body": "Brisanje odabrani dokument."}, "ttDelConnection": {"heading": "Izbriši vezu", "body": "Izbrišite odabranu vezu između dva zadatka slučaja. Brisanje se mora potvrditi. Brisanje se vrši za odabranu vezu. Odaberite vezu klikom na nju u tablici veza."}, "ttDelFolder": {"heading": "<PERSON><PERSON><PERSON><PERSON> mape", "body": "Brisanje odabrane mape."}, "ttDelOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Brisanje odabranog pregleda."}, "ttDelTemp": {"heading": "Izbriši predložak", "body": "Dodjeljuje izbrisani status predlošku. <PERSON>o kada se zahtjev za brisanjem ponovi, predložak je fizički uklonjen. Radnja se primjenjuje na odabrani predložak. Odaberite predložak klikom na njega u tablici predložaka slučajeva."}, "ttDelTsk": {"heading": "Brisanje zadat<PERSON>", "body": "Uklanjanje odabranog zadatka. Brisanje se mora potvrditi. Zajedno s zadatkom uklonit će se sve povezane veze s drugim zadacima u predlošku procesa. Odaberite zadatak tako da ga kliknete u tablici zadataka."}, "ttDelTskOrConnection": {"heading": "Brisanje zadatka ili veze", "body": "Uklanjanje odabranog zadatka ili odabrane veze između dva zadatka procesa. Ova radnja mora biti potvrđena. Povezani linkovi s drugim procesnim zadacima bit će uklonjeni zajedno s zadatkom. Kliknite za odabir."}, "ttDelVar": {"heading": "<PERSON><PERSON><PERSON><PERSON> varija<PERSON>", "body": "Brisanje odabrane varijable. Ova radnja mora biti potvrđena. Varijabla više neće biti dostupna za pojedinačne procesne zadatke. Varijable se odabiru klikom na tablicu varijabli."}, "ttDelVice": {"heading": "Poništiti zamjenik", "body": ""}, "ttDetailCase": {"heading": "<PERSON><PERSON><PERSON>", "body": "Prikazuje pojedinosti odabranog slučaja."}, "ttDetailCertificate": {"heading": "<PERSON><PERSON><PERSON> certifikata", "body": "Prikazuje pojedinosti odabranog certifikata."}, "ttDetailHistory": {"heading": "<PERSON><PERSON><PERSON>", "body": "Prikazuje pojedinosti o odabranoj stavci."}, "ttDetailTsk": {"heading": "Pojedinosti o zadatku", "body": "Prikazuje pojedinosti odabranog zadatka."}, "ttDmsFolderAdd": {"heading": "Dodaj novu mapu", "body": "Dodavanje nove mape. Ako je odabrana jedna od mapa, nadređena mapa bit će unaprijed popunjena."}, "ttDmsFolderEdit": {"heading": "<PERSON><PERSON><PERSON> mapu", "body": "Uredite o<PERSON><PERSON><PERSON> mapu."}, "ttDocuments": {"heading": "<PERSON><PERSON><PERSON> do<PERSON>", "body": ""}, "ttDownload": {"heading": "<PERSON>uzimanje", "body": "<PERSON><PERSON><PERSON> oda<PERSON> da<PERSON>."}, "ttDropContainer": {"heading": "Izbaci", "body": "Izbaciti iz spremnika"}, "ttENotification": "E-mail obavijest", "ttEdit": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućuje uređivanje odabrane stavke."}, "ttEditAttach": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućuje prikaz i uređivanje atributa (metapodataka) prenesene datoteke."}, "ttEditConnection": {"heading": "<PERSON><PERSON><PERSON> veze", "body": "Izrada veze između dva zadatka. Moguće je uređivati parametre ponašanja veze i uvjete povezivanja. Radnja se primjenjuje na odabranu vezu. Linkovi se odabiru klikom na njih u tablici veza."}, "ttEditOverview": {"heading": "<PERSON><PERSON><PERSON> pregled", "body": "Omogućuje uređivanje odabranog pregleda."}, "ttCopyOverview": {"heading": "<PERSON><PERSON> kop<PERSON>", "body": "<PERSON><PERSON>rada kopije odabranog pregleda."}, "ttEditPath": {"heading": "<PERSON><PERSON><PERSON> pre<PERSON>", "body": "Omoguć<PERSON>je definiranje novog pregleda."}, "ttEditTemp": {"heading": "Uređivanje definicije predložaka", "body": "Uređivanje predloška slučaja. Bilo koji parametar predloška može se uređivati. Radnja se provodi za odabrani predložak. Odaberite predložak klikom na tablicu predložaka slučajeva."}, "ttEditTsk": {"heading": "Uređivanje zadatka", "body": "Uređivanje informacija o zadacima i parametara zadatka. Radnja se primjenjuje na odabrani zadatak. Odaberite zadatak klikom na tablicu zadataka."}, "ttEditTskOrConnection": {"heading": "Uređivanje zadataka ili veza", "body": "Uređivanje informacija o zadacima i parametara zadatka ili uređivanje veza između dva zadatka, njihovih parametara ponašanja i uvjeta povezivanja. Radnja se primjenjuje na odabrani zadatak ili vezu. Kliknite za odabir."}, "ttEditTskVars": {"heading": "<PERSON><PERSON><PERSON>", "body": "<PERSON>redi varijable zadatka"}, "ttEditUser": {"heading": "Uređivanje korisničkih podataka", "body": "Uređivanje osnovnih informacija o korisnicima, lozinke, dodjeljivanje organizacijskoj jedinici i dodjeljivanje uloga. Radnja se primjenjuje na odabranog korisnika. Korisnici su odabrani klikom na tablicu korisnika."}, "ttEditVar": {"heading": "Uređivanje varijable", "body": "Uređivanje imena, tipa i zadanih vrijednosti varijabli. Radnja se primjenjuje na odabranu varijablu. Varijable se odabiru klikom na tablicu varijabli."}, "ttEnotTgt": "<PERSON><PERSON><PERSON>", "ttEnotTgtG": "Nadzornik zadatka", "ttEnotTgtO": "Vlasnik slučaja", "ttEnotTgtP": "%s", "ttEnotTgtR": "Uloga %s", "ttEnotTgtS": "Organizacijska jedinica %s", "ttEnotTgtT": "Vlasnik zadatka %s", "ttEvent": {"heading": "Prilagođeni zadatak", "body": "Trenutačno pozivanje na događaj u ovom zadatku."}, "ttEvents": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Postavljanje poslovnih pravila za reagiranje na definirane unutarnje ili vanjske događaje u sustavu. Pristup zahtijeva ulogu $PowerUser."}, "ttFavourites": {"heading": "Popis favorita", "body": "Popis svih favorita s opcijom uređivanja ili uklanjanja s popisa."}, "ttFilter": {"heading": "Filter", "body": "Prikazuje samo one stavke koje zadovoljavaju definirane uvjete filtriranja."}, "ttFilterPrc": {"heading": "Filter", "body": "Prikazuje samo one slučajeve koji zadovoljavaju definirane uvjete filtriranja."}, "ttFilterTemp": {"heading": "Filter", "body": "Prikazuje samo one predloške koji zadovoljavaju definirane uvjete filtriranja."}, "ttFilterTsk": {"heading": "Filter", "body": "Prikazuje samo one predloške koji zadovoljavaju definirane uvjete filtriranja."}, "ttFilterUser": {"heading": "Filter", "body": "Prikazuje samo one predloške koji zadovoljavaju definirane uvjete filtriranja."}, "ttFullScreen": {"heading": "Puni z<PERSON>lon", "body": "Prikazuje sadržaj spremnika na cijelom zaslonu."}, "ttGraph": {"heading": "Grafikon", "body": "Grafički prikaz trenutnog statusa slučaja."}, "ttGraphActualFinish": "Stvarni završetak ", "ttGraphActualStart": "<PERSON><PERSON><PERSON> datum poč<PERSON>ka", "ttGraphCond": "U<PERSON>jet<PERSON>", "ttGraphCond1": "barem jedan mora biti ispunjen", "ttGraphCondAll": "sve mora biti ispunjeno", "ttGraphCondElse": "Osim ako nije ispunjen neki drugi uvjet", "ttGraphDeadlinePo": "Rok: unesen od strane vlasnika predmeta", "ttGraphDeadlinePs": "Rok: unutar % dana nakon pokretanja slučaja", "ttGraphDeadlineTs": "Rok: unutar % dana nakon pokretanja zadatka", "ttGraphDelayPo": "Pokretanje zadatka: uneseno od strane vlasnika predmeta", "ttGraphDelayPs": "Rok: unutar % dana nakon pokretanja slučaja", "ttGraphDelayTs": "Rok: unutar % dana nakon pokretanja zadatka", "ttGraphEnd": "Završetak zadatka završava cijeli slučaj", "ttGraphFinishedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> je", "ttGraphHiearchyA": "svih nadređenih nadzornika zadataka", "ttGraphHiearchyC": "izravno podređen nadzorniku zadatka", "ttGraphHiearchyD": "svih podređenih nadzornika zadatka", "ttGraphHiearchyG": "nadzornik zadatka", "ttGraphHiearchyL": "svi ", "ttGraphHiearchyP": "izravno nadređeni nadglednik zadatka", "ttGraphHiearchyS": "suradnici nadzornika zadataka", "ttGraphLinkFrom": "Od", "ttGraphLinkTo": "Do", "ttGraphMethodL": "do zadnjeg vlasnika zadatka %", "ttGraphMethodS": "do vlasnika zadatka koji je odabrao nadzornik", "ttGraphMethodT": "na automatski odabranog vlasnika zadatka", "ttGraphMethodV": "vlasniku zadatka dodijeljenoj varijabli %", "ttGraphMultiinstance": "Multi-instance", "ttGraphNoneMand": "Obvezan link", "ttGraphOnlyOnce": "Pokreni samo jednom", "ttGraphSave": {"heading": "Spremite dijagram i izradite predložak", "body": ""}, "ttGraphStart": "Zadatak će se automatski aktivirati nakon pokretanja slučaja", "ttGraphTaskHiearchy": "Vlasnik zadatka", "ttGraphTaskMethod": "Zadatak će biti dodijeljen", "ttGraphTaskOwner": "Nadzornik zadatka", "ttGraphTaskOwnerOS": "Voditelj organizacijske jedinice", "ttGraphTaskOwnerPO": "Vlasnik slučaja", "ttGraphTaskOwnerSU": "Odabrani korisnik", "ttGraphTaskRole": "s ulogom", "ttGraphTaskTypeA": "Automatski zadatak", "ttGraphTaskUser": "Vlasnik zadatka", "ttGraphWait1": "Ulazni parametri: čekanje jednog", "ttGraphWaitA": "Ulazni parametri: čekanje svih", "ttGraphWaitFirst": "Ulazni parametri: čeka<PERSON><PERSON> svih, prvi se pokreće", "ttGraphWaitN": "Ulazni parametri: čeka se %", "ttHandover": {"heading": "Predajte zadatak", "body": "Omogućuje prijenos zadatka drugom dostupnom korisniku."}, "ttDelegate": {"heading": "Delegirajte zadatak", "body": ""}, "ttReject": {"heading": "Odbaci zadatak", "body": ""}, "ttHelp": {"heading": "<PERSON><PERSON><PERSON>", "body": "Dopuštanje ili onemogućavanje trenutne pomoći. Pomoć se prikazuje u obliku mjehurića koji se pojavljuju s informacijama o korisničkom sučelju kada su značajke preklopljene."}, "ttHome": {"heading": "Početna stranica korisnika", "body": "Jedino mjesto sa svim informacijama za redovne korisnike. Nadzorna ploča pruža opći prikaz."}, "ttHtml": {"heading": "Generirajte dokumentaciju", "body": "Generiranje HTML dokumentacije procesa predloška. Ovisno o vrsti preglednika, dokument se može odmah prikazati ili spremiti na disk."}, "ttInclusion": {"heading": "Uključenje", "body": "Izvozi datoteku sa sažetkom autorizacije i uloga korisnika, svim ulogama korištenih potpisanih organizacija, gdje je član ili upravitelj, uključujući hijerarhiju zadataka gdje je on superizvor."}, "ttInvAttendees": "Sudionici", "ttInvDTEnd": "<PERSON><PERSON>", "ttInvDTStart": "Početak", "ttInvLocation": "Lokacija", "ttInvitation": "Pozivnica", "ttJustSave": {"heading": "Sp<PERSON>i", "body": "S<PERSON>remi promje<PERSON>."}, "ttLock": {"heading": "Zak<PERSON><PERSON><PERSON><PERSON>", "body": "Zaključaj ili otključaj odabir"}, "ttLockUser": {"heading": "Zak<PERSON><PERSON><PERSON><PERSON>", "body": "Zaključaj ili otključaj korisnika"}, "ttLogout": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Odjava korisnika. Nakon uspješnog završetka rada s aplikacijom prikazuje se početni dijalog za prijavu."}, "ttMapping": {"heading": "Mapping", "body": "<PERSON><PERSON><PERSON> pregled dodijeljenih varijabli za čitanje, pisanje i obveznog unosa u pojedinačnim zadacima s mogućnošću uređivanja zadatka."}, "ttNewCase": {"heading": "Novi slučaj", "body": "Stvaranje nove instance procesa – novi slučaj. Moguće je odabrati iz dostupnih procesnih predložaka ili stvoriti slučaj bez unaprijed definirane strukture zadataka."}, "ttNewOverview": {"heading": "<PERSON><PERSON><PERSON> p<PERSON>z", "body": "Omoguć<PERSON>je definiranje novog pregleda."}, "ttOrgStructure": {"heading": "Struktura organizacije", "body": ""}, "ttParent": {"heading": "Superior", "body": "Prebacite se na slučaj iz kojeg je prikazani slučaj kreiran kao podproces."}, "ttPhoto": {"heading": "Fotografije", "body": "Prijenos fotografija na korisnički profil. Podržava GIF, JPG i PNG formate. Veličina slike će se automatski podesiti."}, "ttPlans": {"heading": "Zakazivanje", "body": "Postavljanje pravila za automatsko lansiranje jednokratnih ili ponovljenih postupaka – slučajevi prema specificiranim parametrima. Pristup zahtijeva ulogu Administratora."}, "ttPrint": {"heading": "Print", "body": "<PERSON><PERSON><PERSON> ispis."}, "ttRecalc": {"heading": "Ponovni izračun", "body": "Ponovno izračunajte trenutne varijable."}, "ttRedirectToPrc": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttResetDash": {"heading": "Reset", "body": "Poništiti dovršene promjene."}, "ttResetSearch": {"heading": "Reset", "body": "Poništiti obrazac za pretraživanje."}, "ttRestoreTemp": {"heading": "Vraćanje predloška", "body": "Vraća obrisani predložak"}, "ttRevision": {"heading": "Revizija", "body": "Omogućuje prijenos nove inačice datoteke"}, "ttRoles": {"heading": "Uprav<PERSON><PERSON><PERSON>", "body": ""}, "ttRunEvent": {"heading": "Pokreni događ<PERSON>", "body": "Pozivanje događaja u ovom slučaju"}, "ttSave": {"heading": "Sp<PERSON>i", "body": "Spremanje promjena i zatvaranje prozora."}, "ttSaveDMSCols": {"heading": "<PERSON><PERSON><PERSON><PERSON> stu<PERSON>", "body": ""}, "ttSaveSettings": {"heading": "Sp<PERSON>i", "body": "S<PERSON>remi promje<PERSON>."}, "ttSaveTsk": {"heading": "Samo spremi", "body": "Otvoreni zadatak bit će spremljen tako da ga kasnije možete vratiti."}, "ttSearch": {"heading": "Pretraži", "body": "Počni pretraživanje"}, "ttSendNote": {"heading": "Dodajte bil<PERSON>ške", "body": "Omogućuje umetanje nove bilješke."}, "ttSetConnectionCond": {"heading": "<PERSON><PERSON>", "body": "Dodavanje ili uređivanje uvjeta veze. Uređivanje se primjenjuje na odabranu vezu. Kliknite na vezu ili simbol uvjeta za odabir."}, "ttSetDefaultDash": {"heading": "Postavi kao zadanu nadzornu ploču", "body": "Postavi trenutni raspored nadzorne ploče kao zadani"}, "ttShowHideBtn": {"heading": "Prikaži / sakrij", "body": "Djelomično skriva ili prikazuje glavni izbornik."}, "ttSleepCase": {"heading": "Obustavite slučaj", "body": "Označava slučaj kao suspendiran. Slučaj više neće biti prikazan među aktivnim zadacima, ali ako je potrebno moguće je vratiti status na aktivno i kasnije završiti cijeli slučaj."}, "ttSolve": {"heading": "Otvori zadatak", "body": "Prikazuje dijalog koji omogućuje rad na zadanom zadatku prema unaprijed definiranom predlošku."}, "ttStatePlan": {"heading": "Status", "body": "Definira status plana."}, "ttStatusHdr": {"heading": "Promjena statusa zaglavlja", "body": "Radnja se primjenjuje za odabrani zaglavlje. Postoje stanja \"aktivno\" i \"neaktivno\". Odabir obavlja se klikom na tablicu zaglavlja predložaka."}, "ttStatusTemp": {"heading": "Promjena statusa predloška", "body": "Upravljanje životnim ciklusom predloška provodi se postavljanjem njegovog stanja. Postoje stanja \"u razvoju\", \"aktivno\", \"neaktivno\" i \"izbrisano\". Radnja se primjenjuje za odabrani predložak. Odabir predloška vrši se klikom na tablicu predložaka predmeta."}, "ttSubprocess": {"heading": "Uklopljeno", "body": "Prebacuje na slučaj koji je stvoren kao pod-proces u procesu prikazanog slučaja."}, "ttTabsButtonMore": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Prikazuje više opcija."}, "ttTakeTsk": {"heading": "Preuzmi zadatak", "body": "Omogućuje preuzimanje zadatka od drugog vlasnika."}, "ttTemps": {"heading": "Predlošci procesa", "body": "Središnje mjesto za upravljanje predlošcima procesa. Pristup zahtijeva ulogu $PowerUser."}, "ttTiming": {"heading": "Zakazivanje", "body": "Unesite početak i kraj zadatka."}, "ttTsks": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttUploadSettings": {"heading": "Učitaj", "body": ""}, "ttUserSetting": {"heading": "Korisničke postavke", "body": "Postavljanje korisničkih podataka za kontakt, pristup lozinkama i korisničkim postavkama. Korisnici koji imaju ulogu $Administratora mogu dodatno upravljati informacijama o svojoj organizaciji i instancama aplikacije TeamAssistant."}, "ttUsers": {"heading": "Administracija korisnika", "body": "Centralna administracija korisnika, organizacijska struktura i korisničke uloge. Pristup zahtijeva ulogu $Administratora."}, "ttValidation": {"heading": "Potvrda", "body": "Potvrdite predložak i pregledajte sve postojeće petlje unutar predloška. Obavještava o neispunjavanju uvjeta i neiskorištenih varijabli."}, "ttViewFile": {"heading": "Pogled", "body": ""}, "ttWakeUpCase": {"heading": "Probudi se", "body": ""}, "ttActivateCase": {"heading": "Aktivirati", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Postavite zadane DMS stupce", "body": "Postavite dodjeljivanje DMS stupaca kao zadano."}, "ttResetDmsCols": {"heading": "Reset", "body": "Poništite dodjelu DMS stupaca."}, "ttRestoreDoc": {"heading": "<PERSON><PERSON><PERSON>", "body": "Vratiti izbrisani dokument."}, "ttSearchHeader": {"heading": "Pretraga", "body": ""}, "tue": "Utorak", "type": "Vrsta", "typeOfRepetition": "Vrsta ponavljanja", "unassignedSolvers": "Prema vlasnicima zadataka", "unassignedTaskSolvers": "Nespremljeni vlasnici zadataka", "uncategorized": "Nekategorizirane", "unfinishedProcesses": "Nedovršeni <PERSON>i", "unknown": "Nepoznato", "unknownUser": "Nepoznati korisnik", "unrestricted": "Neogra<PERSON><PERSON><PERSON>", "unspecified": "<PERSON><PERSON><PERSON><PERSON>", "upload": "Učitaj", "uploadFile": "Učitaj da<PERSON>", "uploadPhoto": "Učitaj fotografiju", "uploadCsv": "Učitaj csv", "url": "URL", "urlAddress": "URL adresa", "urlContent": "URL sadržaj", "use": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "userByOwnerOfLastTask": "Za korisnika kojeg je odabrao posljednji vlasnik zadatka.", "userE": "korisnik", "userFilters": "Korisnički filteri", "userLock": "Zak<PERSON><PERSON><PERSON><PERSON>", "userLockUnlockQ": "Želite li zaista promijeniti status korisnika {{username}}?", "userName": "Korisničko ime", "userId": "ID korisnika", "userOrgStruct": "Pripada organizacijskoj jedinici", "userVice": "Zamijenjen od", "userViced": "Zamijeniti", "users": "<PERSON><PERSON><PERSON><PERSON>", "usersDeleted": "Izbrisano", "validation": "Potvrda", "value": "Vrijednost", "var": "varijabla", "var-": "Varijabla –", "varChange": "Promjena varijable bit će objavljena svim sudionicima slučaja", "varTaskMap": "Mapping", "varTemp": "Predložak varijable", "variable": "Varijabla", "variableType": "<PERSON><PERSON><PERSON> varijable", "vars": "Varijable", "varsForMandatory": "Varijable za obvezni unos", "varsForReading": "Varijable za čitanje", "varsForWriting": "Varijable za pisanje", "vices": "<PERSON><PERSON><PERSON>", "viewCVFields": "Raspoloživa polja", "visForOrgStrMembers": "Vidljivi članovima organizacijske grupe", "visForRoleMembers": "Vidljivo članovima s ulogama", "headerVisForRole": "Slučaj vidljiv članovima koji imaju ulogu", "waitForNumOfInputs": "Čekanje na: (broj ulaza)", "waitsFor": "čeka", "waitsForAll": "čeka sve", "waitsForOne": "čeka jednog", "waitsForSending": "Čeka na slanj", "waitsRunFirst": "čeka sve, pokreće prvog", "wakeUp": "Ponovno uključeno", "warning": "Upozorenje", "wed": "<PERSON><PERSON><PERSON>", "weekIn": "u tjednu", "weekly": "<PERSON><PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "withConditions": "S uvjetima", "withoutCond": "Bez uvjeta", "year": "godina", "yes": "Da", "zip": "Poštanski broj", "move": "Pomicati", "alertClosing1": "obavijest će se automatski zatvoriti u:", "inDocuments": "U dokumentima", "inVariables": "U Varijablama", "headerTask": "Zaglavlje zadatka", "planName": "Naziv plana", "inBulk": "<PERSON><PERSON><PERSON>", "confirmResetDmsColumns": "Želite li doista poništiti DMS stupce?", "dmsColsUseDef": "Korištenje zadanih postavki", "dmsColsUseCust": "Upotreba pril<PERSON><PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON>", "alrPlanDeleteFailed": "Brisanje plana nije us<PERSON>jelo.", "notRunning": "Ne radi", "alrLackOfPermsToAddTask": "Nemate dopuštenja za dodavanje zadatka.", "dragTable": "Povucite tablicu", "alrDownloadCsvListFailed": "Preuzimanje popisa Csv datoteka nije uspjelo.", "alrCsvUploadWrongExtension": "Prijenos samo datoteka s nastavkom * .csv", "addToFav": "<PERSON><PERSON><PERSON> u <PERSON>", "renameItem": "<PERSON><PERSON><PERSON><PERSON>", "removeFromFav": "Ukloni iz favorita?", "alrAddedToFav": "<PERSON><PERSON><PERSON> u favorite.", "alrRemovedFromFav": "Uklonjeno iz favorita.", "tskSetAssignDues": "Postavite vremenska ograničenja za zadatak", "isNot": "nije", "alrTskScheduling": "Raspoređivanje zadataka ...", "alrFavouritesPageExist": "<PERSON>va je stranica već u favoritima.", "alrFavouritesActionExist": "Ta je radnja već u favoritima.", "alrFavouriteRenamed": "Favorit je preimenovan.", "autoFit": "Autofit", "passwordIsShort": "Šifra je prekratka.", "changeAttrComplCases": "Promijenite atribute dovršenih slučajeva", "iterateOverVars": "Ponavljajte varijable", "nrOfDecimalDigits": "<PERSON><PERSON>j de<PERSON>nih znamenki", "onlyNumbers": "<PERSON><PERSON> broje<PERSON>", "maxNumberOfDecimals": "<PERSON><PERSON><PERSON><PERSON> broj decimalnih znamenki je", "alrInsertCsv": "Umetnite CSV datoteku.", "addBefore": "Dodaj prije", "moveBefore": "Pomakni prije", "administration": "Administracija", "ttAdministration": {"heading": "Administracija", "body": ""}, "alrLogsLoadFailed": "Zapisi se nisu uspjeli učitati.", "logs": "Zapisi", "message": "<PERSON><PERSON><PERSON>", "useCompatibleTempl": "Koristi kompatibilni predložak", "overwriteExistTempl": "Prebrisati postojeći predložak", "addNewTempl": "Do<PERSON>j novi predlo<PERSON>", "import": "Uvoz", "export": "Izvoz", "confirmExportAllTempl": "Izvezi sve predloške?", "confirmExportSelTempl": "Izvezi odabrani predložak?", "newLogs": "Novi zapisi", "container": "Spremnik", "contents": "<PERSON><PERSON><PERSON><PERSON>", "confirmRemoveDialog": "Ž<PERSON>te li zaista ukloniti {{variable}}?", "allMyCases": "Svi moji sluč<PERSON>evi", "maintenanceMsg": "Planirano <span style=\"color: {{color}};\">održavanje</span> je u tijeku", "alrMaintenanceMsg": "Planirano održavanje je u tijeku, pokušajte kasnije.", "alrAttachDownloadLackOfPerms": "Nemate dopuštenja za preuzimanje dokumenta ili dokument nije pronađen.", "unableToConnect": "Nemogućnost spajanja na server", "tryLater": "Pokušajte kasnije ili se obratite administratoru.", "enableTaskDelegation": "Omogući delegiranje zadatka", "enableRejectTask": "Omogući odbijanje zadatka", "confirmRejectTask": "Želite li doista odbiti zadatak?", "rejectTask": "Odbaci zadatak", "delegateTask": "Povjeriti", "alrRejectingTask": "Odbijanje zadatka ...", "alrTaskRejected": "Zadatak je odbijen.", "alrTaskRejectFailed": "Zadatak se nije uspio odbiti.", "alrTaskDelegating": "Povjeravanje zadatka ...", "alrTaskDelegated": "Zadatak je povjeren korisniku:", "alrFailedTaskDelegate": "Povjerenje zadatka nije uspilo.", "delegateOnUser": "Delegirajte korisnika", "plnAssignmentCond": "<PERSON><PERSON> pol<PERSON> \"<PERSON><PERSON><PERSON>\" o<PERSON><PERSON>, izradit će se popis inicijatora ocjenjivanjem restriktivnih uvjeta u vrijeme izvršenja plana", "alrUserFiltersSettingsFailed": "<PERSON><PERSON> us<PERSON>lo spremanje postavki korisničkih filtara.", "general": "Obično", "alrUserPhotoLoadFailed": "Učitavanje korisničke fotografije nije uspjelo.", "publicDynTable": "Javna dinamička tablica", "isFullIndexed": "U potrazi", "datetimeIndexed": "Indeksirano na", "toIndex": "<PERSON>a <PERSON>dek<PERSON>", "toReindex": "Za ponovno indeksiranje", "solverChanged": "Vlasnik zadatka je promijenjen u {{count}} zadatke", "changeSolverFailed": "Promjena vlasnika zadatka nije uspjela.", "alrTikaParsingFailed": "Došlo je do pogreške prilikom raščlanjivanja dokumenta.", "alrIndexingFailed": "Indeksiranje dokumenta nije uspjelo.", "alrTikaNotRunning": "Usluga za raščlanjivanje dokumenata nije dostupna.", "alrIndexingServiceNotRunning": "Usluga indeksiranja nije dostu<PERSON>.", "alrFulltextNotSet": "Puni tekst nije postavljen.", "asc": "Uzlazni", "desc": "<PERSON><PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON>", "alrLogosLoadFailed": "Učitavanje logotipa nije us<PERSON>jelo.", "indexedDocsCount": "u {{count}} dokumentima", "alrIndexedCountLoadFailed": "Pretraživanje cijelog teksta trenutno nije dostupno.", "searchAll": "Pretraži sve", "searchActual": "<PERSON><PERSON> s<PERSON>", "runIndexing": "Pokreni indeksiranje", "alrDocumentIndexing": "Indeksiranje dokumenta...", "alrDocumentIndexed": "Dokument je indeksiran i može se pronaći pretraživanjem.", "alrDocumentIndexedWithMinMetadata": "Do<PERSON><PERSON> je indeksiran.", "alrDocumentIndexingFailed": "Indeksiranje dokumenta nije uspjelo.", "changingUserProfileForbidden": "Promjena korisničkog profila je zabranjena.", "uploadingPhotoForbidden": "Prijenos fotografije je zabranjen.", "alrValidationCalcError": "Pogreška pri provjeri izračuna", "maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maintenanceActivate": "Aktivirajte održavanje", "maintenanceInfoText": "Početak i kraj prikazat će se korisnicima nakon aktivacije održavanja.", "maintenanceMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrAvailableCalcFailed": "Dostupni izračuni nisu se uspjeli učitati.", "alrFillDataForSearch": "Ispunite parametre pretraživanja.", "youAreHere": "Vi ste ovdje", "invalidDate": "Nevažeći format datuma", "alrInvalidFileFormat": "Nevažeći format datoteke.", "alrEnter3characters": "Unesite najmanje tri znaka.", "changeCaseOwner": "Promijeni vlasnika slučaja", "actualCaseOwner": "Vlasnik stvarnog slučaja", "newCaseOwner": "Vlasnik novog slučaja", "alrCaseOwnerChanged": "Vlasnik slučaja je promijenjen.", "alrChangeCaseOwnerFailed": "Promjena vlasnika slučaja nije uspjela.", "alrCsvSaving": "Spremanje CSV datoteke ...", "alrCsvSaveFailed": "Prijenos CSV datoteke nije uspio.", "alrCsvSaved": "CSV datoteka je učitana.", "allTemplates": "<PERSON><PERSON>", "specifyCaseIds": "Navedite ID-ove <PERSON>", "caseIds": "<PERSON><PERSON><PERSON> s<PERSON>", "caseId": "ID slučaja", "separBySemicolon": "odvojene točka-zarezom", "alrAddCaseIds": "Navedite ID-ove <PERSON>", "headers": "Zaglavlja", "header": "Zaglavlje", "defaultHeaderName": "Zadano ime zaglavlja", "headerName": "Naziv zaglavlja", "addHeader": "Dodajte zaglavlje", "editHeader": "Uredi zaglavlje", "templateName": "<PERSON>v predloška", "rolesExecRightsText": "Dodijeliti uloge koje će moći pokrenuti slučajeve", "orgUnitsExecRightsText": "Dodijeliti organizacijske jedinice koje će moći pokrenuti slučajeve", "selectedHeader": "odabrano zaglavlje", "alrHeaderDeleted": "Zaglavlje je izbrisano!", "alrHeaderDeleteFailed": "Brisanje zaglavlja nije us<PERSON>lo.", "alrHeaderSaveFailed": "Spremanje zaglavlja nije uspjelo.", "alrHeaderSaved": "Zaglavlje je spremljeno.", "alrHeadersLoadFailed": "Učitavanje podataka zaglavlja nije uspjelo.", "identificator": "Kod zaglavlja", "includeDataSimilarProcesses": "Uključite podatke svih sličnih procesa", "confirmCopyCv": "Ž<PERSON>te li zaista kopirati odabrani pregled?", "alrCreatingCopyCv": "<PERSON><PERSON><PERSON> kopije pregleda ...", "alrCvCopied": "Pregled je kopiran.", "alrCopyCvFailed": "<PERSON><PERSON><PERSON> kopije pregleda nije us<PERSON>la.", "copyingTemplate": "Kopiran<PERSON> predloška", "alrCheckTempImportFailed": "Provjera uvoza predloška nije uspjela.", "warnings": "Upozorenja", "missingEventsFiles": "Datoteke nedostajućih događaja", "missingEventsFilesText": "{{- file}} Datoteka nije pronađena u {{- event}} doga<PERSON><PERSON>u", "printsOfTemplates": "<PERSON><PERSON><PERSON> predloška", "printsOfTemplatesText": "Obratite pozornost na ispis {{- print}} iz {{- template}} predloška. Vrijednost: {{- value}}", "dupliciteTaskNames": "<PERSON><PERSON><PERSON><PERSON> imena zada<PERSON>", "dupliciteTaskNamesText": "{{- template}} predl<PERSON>žak sadrži više zadataka s istim imenom {{- task}} {{- taskId}}, to će u<PERSON><PERSON><PERSON><PERSON> k<PERSON> linkova!", "dynTableUsed": "Dinamička tablica koja se koristi", "suspiciousCalc": "Sumnjivi izračuni", "suspiciousCalcText": "Moguća nedostajuća uloga / organizacija / korisnik u izračunu {{- calc}}", "missingEvents": "<PERSON><PERSON><PERSON><PERSON>", "missingEvent": "<PERSON><PERSON><PERSON><PERSON>", "wrongMappingDomains": "Pogrešno mapiranje domena", "wrongMappingDomainsText": "Opis {{- task}} zada<PERSON><PERSON> iz {{- template}} predlo<PERSON>ka sadrži lo<PERSON>e ime domene, trenutna domena je {{- actDom}}", "taskDescription": "Opis zadatka", "eventsUrl": "URL događaja", "eventsUrlText": "Moguća pogreška u {{- event}} u URL-u <PERSON><PERSON><PERSON><PERSON>, trenutna domena je {{- actDom}}", "param": "Parametar", "alrServiceNotForTable": "Podaci iz ove usluge nisu prikladni za gledanje u tablici.", "alrServiceDataFailedLoad": "Podaci o usluzi se nisu uspjeli učitati.", "alrServiceNoData": "Usluga ne sadrži nikakve podatke.", "tableColumns": "St<PERSON>ci tablice", "datetime": "Datum i vrijeme", "exactDatetime": "Točan datum i vrijeme", "dashRestNoColumns": "Stupci nisu postavljeni – odabirete ih u postavkama spremnika", "loadService": "Usluga učitavanja", "useCompatibleRole": "<PERSON><PERSON><PERSON> kom<PERSON><PERSON><PERSON><PERSON> ul<PERSON>u", "overwriteExistRole": "Prebrisati posto<PERSON>ć<PERSON> ulogu", "addNewRole": "Dodajte novu ulogu", "templateImportFailed": "Uvoz predloška nije uspio.", "templateImport": "Uvoz predloška", "templateImportNoData": "Za uvoz predloška nisu pronađeni podaci.", "variableImportNoData": "Za uvoz varijabli nisu pronađeni podaci.", "ttTemplateImport": {"heading": "Uvoz predloška", "body": "Odabrana je mapa s definicijama jednog ili više predlož<PERSON>, a zatim prenesena."}, "showUnfinishedProcesses": "Prikaži nedovrš<PERSON> s<PERSON>", "expMaintenanceEnd": "Očekivani kraj održavanja", "alrScriptSaveFailed": "Spremanje skripte nije uspjelo.", "editScript": "<PERSON><PERSON><PERSON>", "addScript": "<PERSON><PERSON><PERSON>", "alrRunScript": "Pokretanje skripte ...", "alrScriptCompleted": "Skripta je dovršena.", "alrFailedScriptStart": "Pokretanje skripte nije us<PERSON>jelo.", "alrScriptDocsLoadFailed": "Dokumentacija skripti nije uspjela učitati.", "alrScriptLoadFailed": "<PERSON><PERSON> mogu<PERSON>e učitati skripte.", "switchAdminUser": "Pro<PERSON><PERSON>na admin / korisnik", "ttSwitchAdminUser": {"heading": "Pro<PERSON><PERSON>na admin / korisnik", "body": ""}, "ttSwitchViewport": {"heading": "Prebacivanje mobilnog / PC prikaza", "body": ""}, "alrEventDataLoadFailed": "<PERSON>je moguće učitati podatke o događaju.", "alrEventRuleDataLoadFailed": "<PERSON>je uspjelo učitavanje podataka o pravilu događaja.", "cancellation": "Otkazivanje", "tTaskAutoCancellCaption": "Zadatak će se automatski otkazati ako", "codeMirrorHelp": "Kliknite bilo gdje u uređivaču i pritisnite Ctrl + Space da biste vidjeli pomoć.", "codeMirrorHelpJs": "Za pop<PERSON> s<PERSON>h z<PERSON>, kliknite <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "<PERSON><PERSON><PERSON>", "editEvent": "<PERSON><PERSON><PERSON>", "term": "<PERSON><PERSON><PERSON>", "columnOrder": "Redoslijed stupaca", "alrLoadEventsButtonsFailed": "Učitavanje gumba u tablici nije uspjelo.", "showButtonsCol": "Prikaži stupac radnje", "button": "<PERSON><PERSON><PERSON>", "enableButtonInTasks": "Pokaži kao gumb u popisu zadataka", "alrEventDoesntExist": "Odabrani događaj ne postoji.", "alrEventRuleSaveFailed": "<PERSON><PERSON> spremanje pravila događaja.", "variableNames": "<PERSON><PERSON><PERSON>", "fsEvent": "<PERSON><PERSON><PERSON><PERSON>", "alrEventDeleteFailed": "Brisanje događaja nije us<PERSON>.", "fsRule": "pravilo", "alrRuleDeleteFailed": "<PERSON><PERSON><PERSON><PERSON> pravila nije us<PERSON>.", "alrRuleStatusChangeFailed": "Promjena statusa pravila nije us<PERSON>la.", "ruleActivateDeactivateQ": "Želite li zaista promijeniti status pravila?", "docUploadedPrivate": "Dokument će se prenijeti kao privatni", "fileOwner": "Vlasnik datoteke", "planOk": "Ok", "userNotAuthToStartTempl": "Korisnik nije ovlašten pokrenuti slučaj prema ovom predlošku", "planStartDate": "Početni datum", "useOnlyFutureDates": "<PERSON><PERSON> bud<PERSON> da<PERSON>e", "alrGenerateHtmlFailed": "Generiranje HTML-a nije uspjelo.", "alrNoPermsToAddNoteInVice": "Nemate dopuštenja za dodavanje napomene kao zamjenik.", "alrNoPermsToAddDocInVice": "Nemate dopuštenja za dodavanje napomene kao zamjenik.", "current": "<PERSON><PERSON><PERSON><PERSON>", "indexation": "Indeksacija", "attemptToRestoreConnection": "Pokušajte vratiti vezu u", "loginWillExpire": "Prijava će isteći za", "unsavedDataWillBeLost": "Nespremljeni podaci bit će izgubljeni.", "alrFileSaveLikeAttachViceError": "Vaš zamjenik ima samo dozvolu za pregled!", "alrFileSaveLikeAttachStoreError": "Spremanje ispisa kao dokumenta u slučaj nije uspjelo.", "useCompatibleEvent": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> dog<PERSON>", "overwriteExistEvent": "Prepiši postojeći događaj", "addNewEvent": "Do<PERSON>j novi događaj", "useCompatibleUser": "<PERSON><PERSON><PERSON> kompatibilnog korisnika", "overwriteExistUser": "Prepisati postojećeg korisnika", "addNewUser": "Dodaj novog korisnika", "useCompatibleUnit": "Koristite kompatibilnu org. jedinicu", "overwriteExistUnit": "Prepisati postojeću org. jedinicu", "addNewUnit": "Dodajte novu org. jedinicu", "addNewDynTable": "Dodaj novu din. tablicu", "useCompatibleDynTable": "Koristite kompatibilnu din. tablicu", "enterDiffNameRoot": "Unesite drugo ime od Root.", "ttTemplatesExport": {"heading": "Izvezi predloš<PERSON>", "body": "Izvezite odabrane predloške u mapu. Moguće je odabrati ime i mjesto izvezene datoteke. Radnja se primjenjuje na odabrani predložak. Odaberite predložak klikom na njega u tablici predložaka slučajeva."}, "ttTemplatesExportAll": {"heading": "Izvezite sve predloške", "body": "Izvezite sve trenutno prikazane predloške u datoteku. Moguće je odabrati ime i mjesto izvezene datoteke. Odabir predložaka može se ograničiti postavljanjem odgovarajućih uvjeta filtriranja."}, "exportAll": "Izvezi sve", "noTemplatesToExport": "Nema predložaka za izvoz.", "skip": "Preskoči", "ttSkipTemplate": {"heading": "Preskočite predložak", "body": "Preskoči uvoz trenutnog predloška i prikaži sljedećeg."}, "alrInvalidImportData": "Nevažeći podaci o uvozu", "alrUsersNotLoaded": "Korisnici se nisu uspjeli učitati.", "caseOverview": "<PERSON><PERSON> s<PERSON>", "alrRolesNotLoaded": "<PERSON><PERSON> mogu<PERSON>e učitati uloge.", "changeLang": "Promijeni jez<PERSON>", "reactivatesPlan": "ponovno aktivira plan", "alrOrgUnitsNotLoaded": "Org. jedinice nisu se učitale.", "refreshPage": "Osvježi stranicu", "stayLogged": "Ostan<PERSON>", "showTime": "Prikaži vremenske oznake u pregledima", "managerIn": "<PERSON><PERSON><PERSON><PERSON> u {{orgUnit}}", "usageStats": "Statistika upotrebe", "month": "<PERSON><PERSON><PERSON><PERSON>", "alrUsageStatsLoadFailed": "<PERSON><PERSON> mogu<PERSON>e učitati statistiku upotrebe.", "accessLog": "Pristupni zapisnik", "durationInMs": "<PERSON><PERSON><PERSON><PERSON> (ms)", "task": "Zadatak", "operation": "Operacija", "active_users": "B<PERSON>j aktivnih korisnika", "active_template_processes": "B<PERSON>j aktivnih procesa", "active_headers": "Broj aktivnih zaglavlja", "active_users_able_to_create_a_process": "<PERSON><PERSON>j aktivnih korisnika koji mogu pokrenuti proces", "users_that_solved_a_task": "<PERSON><PERSON><PERSON>, koji su riješ<PERSON> barem jedan zadatak", "solvers_or_can_create_a_process": "B<PERSON>j korisnika koji su riješili zadatak ili mogu pokrenuti proces", "mobile_app_paired_users": "B<PERSON>j uparenih korisnika mobilne aplikacije", "calculationsLogs": "Dnevnici izračuna", "translatedScript": "<PERSON><PERSON><PERSON> sk<PERSON>ta", "originalScript": "<PERSON><PERSON> sk<PERSON>ta", "tskId": "ID zadatka", "alrCalculationsDocsLoadFailed": "Nije moguće učitati dokumente izračuna.", "alrCalculationsValidationFailed": "Provjera valjanosti izračuna nije uspjela.", "linkPriority": "Poveži prioritet", "dateFormat": "DD / MM / GGGG", "alrConvertErrorJsonNeon": "Pogreška tijekom pretvorbe json -> neon.", "alrInvalidData": "Nevažeći podaci.", "sharedVar": "<PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON>", "guide": "<PERSON><PERSON><PERSON><PERSON>", "guideFs": "v<PERSON><PERSON><PERSON>", "guides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrGuidesLoadFailed": "Vodiči se nisu učitali.", "language": "<PERSON><PERSON><PERSON>", "default": "Zadano", "next": "Sljedeći", "previous": "<PERSON><PERSON><PERSON><PERSON>", "targetElementNotFound": "Nije pronađen ciljni element", "documentation": "Dokumentacija", "matchesRegular": "Ne podudara se s pravilom", "secondAllowedValues": "dopuštene vrijednosti; 0 je vrh minute", "everySec": "svake sekunde", "listOfSec": "popis sekundi; npr. 0,30 bi bilo 0 i 30 sekundi", "rangeOfSec": "raspon sekundi; npr. 0–5 bi bilo sekundi 0, 1, 2, 3, 4 i 5 (mo<PERSON><PERSON> odrediti i popis raspona 0–5,30–35)", "slashSec": "vrijednosti koraka preskočit će navedeni broj unutar raspona; npr. * / 5 je svakih 5 sekundi, a 0–30 / 2 je svaka 2 sekunde između 0 i 30 sekundi", "minuteAllowedValues": "dopuštene vrijednosti; 0 je vrh sata", "everyMin": "svake minute", "listOfMin": "popis minuta; npr. 0,30 bi bilo 0 i 30 minuta", "rangeOfMin": "raspon minuta; npr. 0–5 bi bile minute 0, 1, 2, 3, 4 i 5 (mož<PERSON> odrediti i popis raspona 0–5,30–35)", "slashMin": "vrijednosti koraka preskočit će navedeni broj unutar raspona; tj. * / 5 je svakih 5 minuta, a 0–30 / 2 je svaka 2 minute između 0 i 30 minuta", "hourAllowedValues": "dopuštene vrijednosti; 0 je ponoć", "everyHour": "svaki sat", "listOfHour": "popis sati; npr. 0,12 bi bilo 0 i 12 sati", "rangeOfHour": "raspon sati; npr. <PERSON><PERSON> 19 do 23 će biti sati 19, 20, 21, 22 i 23 (mo<PERSON><PERSON> odrediti i popis raspona 0–5,12–16)", "slashHour": "vrijednosti koraka preskočit će navedeni broj unutar raspona; npr. * / 4 je svaka 4 sata, a 0–20 / 2 je svaka 2 sata između 0 i 20 sata", "dayAllowedValues": "dopuštene vrijednosti", "everyMonthDay": "svaki dan u mjesecu", "listOfDay": "popis dana; npr. 1,15 bi bio 1. i 15. dan u mje<PERSON>cu", "rangeOfDay": "raspon dana; npr. 1–5 bi bili dani 1, 2, 3, 4 i 5 (mož<PERSON> odrediti i popis raspona 1–5,14–30) ", "slashDay": "vrijednosti koraka preskočit će navedeni broj unutar raspona; * / 4 je svaka 4 dana, a 1–20 / 2 je svaka 2 dana između 1. i 20. dana u mjesecu", "allowedValues": "dopuštene vrijednosti", "everyMonth": "svaki mjesec", "listOfMonth": "popis m<PERSON>; npr. 1,6 će biti siječanj I lipanj", "rangeOfMonth": "raspon mjeseci; npr. 1–3 će biti siječanj, veljača i ožujak (možete odrediti i popis raspona 1–4,8–12)", "slashMonth": "vrijednosti koraka preskočit će navedeni broj unutar raspona; * / 4 je svaka 4 mjeseca, a 1–8 / 2 svaka 2 mjeseca između siječnja i kolovoza", "weekAllowedValues": "dopuštene vrijednosti; 0 = nedjelja, 1 = ponedjeljak, 2 = utorak, 3 = srijeda, 4 = četvrtak, 5 = petak, 6 = subota", "everyWeekDay": "svaki dan u tjednu", "listOfWeekDay": "popis dana; npr. 1,5 će biti ponedjeljak i petak", "rangeOfWeekDay": "raspon dana; npr. 1–5 bi bio mon, tue, wed, thu, i fri (možete odrediti i popis raspona 0–2,4–6)", "slashWeek": "vrijednosti koraka preskočit će navedeni broj unutar raspona; tj. * / 4 je svaka 4 dana, a 1–5 / 2 je svaka 2 dana između ponedjeljka i petka", "contrab": "Cron {{variable}} polje", "cSecond": "sekunda", "cMinute": "minuta", "cHour": "sat", "cDay": "dan", "cMonth": "mjesec", "cWeekDay": "dan u tjednu", "seconds": "sekunde", "minutes": "minute", "hours": "sati", "days": "dani", "weeks": "tjedni", "socketOk": "Tablica sadrži najnovije podatke", "socketBroken": "Vraćanje veze za ažuriranje podataka u tijeku", "newTask": "Novi zadatak", "report": "Izvješće", "ttCaseReport": {"heading": "Izvješće", "body": ""}, "usersRights": "<PERSON><PERSON><PERSON> koris<PERSON>a", "visPerRole": "Vidljivost po ulozi", "manualEvents": "Ručni događaji", "noTasks": "<PERSON>ema novih zadataka", "emptyFavs": "Popis favorita je prazan", "crons": "Crons", "cronsHistory": "<PERSON><PERSON> povijest", "redirBefStart": "P<PERSON>je <PERSON>, preusmjerite na", "lastRun": "Last run", "nextRun": "Next run", "syntax": "Sin<PERSON>ks<PERSON>", "alias": "Pseudonim", "stop": "Stop", "restart": "<PERSON><PERSON>", "restartCronProcess": "Ponovno pokrenite kontekst procesa", "ttRestartCron": {"heading": "<PERSON><PERSON> cron", "body": ""}, "ttRestartCronProcess": {"heading": "Ponovo pokrenite proces", "body": ""}, "ttResetCron": {"heading": "Reset cron", "body": ""}, "ttRunCron": {"heading": "<PERSON> cron", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "Kron je bio zaustavljen.", "alrCronStopFailed": "Zahtjev za zaustavljanje cron nije uspio.", "alrCronRunning": "<PERSON>ron je počeo.", "alrCronRunFailed": "Pokretanje Crona nije us<PERSON>jelo.", "alrCronReset": "<PERSON>ron je resetiran na zadano.", "alrCronResetFailed": "<PERSON><PERSON> reset nije uspio.", "alrCronRestart": "<PERSON><PERSON> je ponovno pokrenut.", "alrCronRestartFailed": "Zahtjev za ponovno pokretanje cron-a nije uspio.", "alrCronUpdated": "<PERSON><PERSON> je uspješno spremljen.", "alrCronUpdateFailed": "Cron zahtjev za ažuriranjem nije uspio.", "confirmRunCronDialog": "Jeste li sigurni da želite pokrenuti odabrani cron?", "confirmStopCronDialog": "Jeste li sigurni da želite zaustaviti odabrani cron?", "confirmResetCronDialog": "Jeste li sigurni da želite vratiti cron na tvorničke postavke?", "confirmRestartCronDialog": "Jeste li sigurni da želite ponovno pokrenuti odabrani cron?", "confirmUpdateCronDialog": "Jeste li sigurni da želite promijeniti cron status?", "alrProcessRestart": "<PERSON><PERSON> proces je ponovno pokrenut!", "alrProcessRestartFailed": "Zahtjev za ponovno pokretanje postupka nije uspio.", "confirmRestartProcessDialog": "Jeste li sigurni da želite ponovno pokrenuti cijeli cron proces? Čuvajte se potpunog ponovnog pokretanja svih cronova i cjelokupnog konteksta.", "cronParams": "Parametri", "alrPresetLogFiltersLoadFailed": "<PERSON>je mogu<PERSON>e učitati unaprijed postavljene filtere.", "timeRange": "Vremenski raspon", "presetFilters": "<PERSON><PERSON><PERSON><PERSON>", "params": "Parametri", "authentication": "<PERSON><PERSON><PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Naziv parametra", "authMethod": "metoda provjere autentičnosti", "taskAlreadyEdited": "Zadatak već uređuje drugi korisnik.", "taskEditedByAnotherUser": "Drugi je korisnik započeo uređivanje zadatka.", "tempAlreadyEdited": "Predložak već uređuje drugi korisnik.", "tempEditedByAnotherUser": "Drugi je korisnik započeo uređivanje predloška.", "test": "Test", "notInRightormat": "Nevažeći format", "ttTableExportExcel": {"heading": "Izvoz tablice", "body": "Izvozi tablicu u xlsx datoteku"}, "ttTableExportCsv": {"heading": "Izvoz tablice", "body": "Izvozi tablicu u csv datoteku"}, "searchInSuspended": "Pretražite i u suspendiranim slučajevima", "alrScriptDocsFailed": "<PERSON>je mogu<PERSON>e spremiti dokumentaciju skripte.", "currentlyRunning": "Trenutno je pokrenuto", "onStart": "Na početku", "onEnd": "Na kraju", "onHand": "R<PERSON><PERSON><PERSON>", "onRecalc": "Na ponovni izračun", "onPull": "<PERSON><PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "tomorrow": "Sutra", "replyRecipient": "<PERSON><PERSON><PERSON><PERSON> prima<PERSON>", "bcRecipient": "Primatelj slijepe kopije", "copyRecipient": "Primatelj kopije", "emptyHe": "Prazno", "archivedLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basicMode": "Osnovni način rada", "expertMode": "Stručna razina", "ttBasicMode": {"heading": "Osnovni način rada", "body": "Skriva neke stavke ili opcije u obrascu."}, "ttExpertMode": {"heading": "Stručna razina", "body": "Prikazuje skrivene stavke ili opcije u obrascu."}, "helpOverviewFolder": "You možete uključiti pregled u strukturu direktorija pomoću kose crte. <br /> <i> (npr. dostavnice / svi primljeni računi) </i>", "helpOverviewIncludeSimilar": "<PERSON><PERSON> o<PERSON>, prikazat će se i slučajevi iz drugih zaglavlja predloška.", "helpOverviewSysVars": "Polja označena sa (sys) su sistemska polja koja su dio svakog procesa.", "off": "Off", "toPlan": "Plan", "alrMaintenanceComing": "U {{time}} započet će planirano održavanje sustava. Sačuvajte svoju radnju.", "timeoutHMS": "Timeout: (hh:mm:ss)", "eventw": "\"{{task}}\" zadatak iz predloška \"{{template}}\" čeka ovaj događaj", "waitsForEventTip": "Slučaj čeka događaj: \"{{event}}\"", "copyToMultiinstances": "<PERSON><PERSON><PERSON> u Multi-instance", "showAsPreview": "<PERSON><PERSON><PERSON><PERSON> pregled", "alrPreviewAttachmentsFailed": "Prikazivanje pregleda nije us<PERSON>lo", "alrPreviewAttachmentsWrongFormat": "Prikazivanje pregleda nije uspjelo – nepodržani format datoteke", "previewNotAvailable": "Pretpregled dokumenta nije moguć zbog vrste dokumenta.", "configuration": "Konfiguracija", "values": "Vrijednosti", "defaultValues": "Zadane vrijednosti", "ttSubscribeCv": {"heading": "Pretplati pregled", "body": "<PERSON><PERSON><PERSON><PERSON> pregled bit će vam poslan svakim radnim danom u određeno vrijeme."}, "subscribe": "Pretplatiti", "time": "<PERSON><PERSON>je<PERSON>", "hdrStatusQ": "Želite li zaista promijeniti status zaglavlja?", "prevWorkDay": "<PERSON><PERSON><PERSON><PERSON> radni dan", "externalLang": "Vanjski jezik", "customization": "Prilagodbe", "elementColor": "<PERSON><PERSON>", "fontColor": "<PERSON><PERSON>a", "fontSize": "<PERSON><PERSON><PERSON><PERSON>", "bold": "Podebljani", "caseStatuses": "<PERSON>i s<PERSON>", "statuses": "Statusi", "Manuals": "Priručnici", "forReading": "Za čitanje", "forReadWrite": "Za čitanje i pisanje", "addVersion": "Nova verzija", "size": "Veličina", "ttCreateTempVersion": {"heading": "Izradite novu verziju predloška", "body": ""}, "version": "Verzija", "alrTempVersionsLoadFailed": "Verzija predloška nije se mogla učitati.", "alrChangeTempVersionFailed": "<PERSON><PERSON> promjenu verzije pre<PERSON>.", "alrCreateTempVersionFailed": "Stvaranje nove verzije predloška nije us<PERSON>jelo.", "confirmCreateTempVersion": "Jeste li sigurni da želite stvoriti novu verziju predloška?", "cursive": "Kurziv", "small": "Mali", "mandatoryVar": "Obavezna varijabla", "emptyRequiredVarMessage": "<PERSON>s, obavezna varijabla je prazna", "duration": "<PERSON><PERSON><PERSON><PERSON>", "alrDynConditionsFailed": "Nije moguće dovršiti zadatak. Pokušajte osvježiti stranicu ili se obratite Administratoru ili Helpdesk.", "caseActivation": "Aktivacija slučaja", "average": "Prosjek", "performanceLogs": "Dnevnici izvedbe", "displayingOverview": "<PERSON><PERSON><PERSON><PERSON>", "taskSolve": "Dovršenje zadatka", "displayingCO": "Prikazanje CASE OVERVIEW", "printCreation": "<PERSON><PERSON><PERSON><PERSON>", "entityId": "ID entiteta", "medium": "Srednji", "large": "<PERSON><PERSON><PERSON>", "alrTemplTsksLoadFailed": "Učitavanje zadataka predloška nije uspjelo.", "copyTask": "Kopiranje zadatka", "checkProcessCompletion": "Provjera završetak slučaja", "findingSolver": "Traženje rješavača", "applyInTasks": "Koristite u zadacima", "publicFiles": "<PERSON><PERSON><PERSON> da<PERSON>", "usage": "Upotreba", "applyInAllTasks": "Koristite u svim zadacima", "serviceConsole": "<PERSON><PERSON><PERSON> kon<PERSON>", "selectAll": "Označi sve", "logos": "<PERSON><PERSON><PERSON><PERSON>", "overviewWithTasks": "Pregled sa zadacima", "printIsReady": "<PERSON><PERSON> je spreman", "alrChangelogLoadFailed": "Učitavanje dnevnika promjene nije uspjelo.", "inJs": "U scripta", "ttCopyDtDefinition": {"heading": "Kopirajte definiciju tablice", "body": "<PERSON><PERSON><PERSON> definiciju odabrane dinamičke tablice."}, "confirmCopyTableDefinition": "Želite li zaista kopirati definiciju tablice?", "alrCopying": "Kopiranje...", "alrCopyFailed": "<PERSON><PERSON><PERSON><PERSON> nije <PERSON>.", "fallback": "Fallback", "syncEnabled": "Sinkronizacija", "systemGuideNote": "Sad<PERSON>žaj Vodiča za sustav nije moguće promijeniti. Za pregled drugog sadržaja učinite Vodič sustava neaktivnim i kopirajte njegov sadržaj u novi Vodič.", "alrAnotherUserLogged": "Drugi korisnik prijavljen je u drugom prozoru!", "userLocked": "Korisnik je zaključan", "visInternalUserOnly": "Vidljivo samo internim korisnicima", "showSelectedOnly": "Prikaži samo odabrano", "clickToSelect": "Kliknite za odabir", "restrictRoleAssignment": "Ograniči dodjelu uloga za ulogu", "restrictions": "Ograničenja", "restrictTableHandling": "Ograniči rukovanje tablice", "toRole": "<PERSON> ulogu", "inCalcToHeader": "U kalkulacije na zaglavlje", "loginBtnColor": "Boja gumba za prijavu", "certificates": "Potvrde", "certificate": "Potvrda", "certificateVar": "potvrda", "tspSources": "Vremenske oznake", "tspSource": "Vremenska oznaka", "confirmExpandDynRowsNewAssignments": "<PERSON><PERSON><PERSON>, novi dodjeljivanje! Varijable nemaju postavljene osi. Želite li rastegnuti sve dinamičke redove?", "confirmExpandDynRows": "Jeste li sigurni da želite razvući sve dinamičke redove?", "expandDynRows": "Istegnite dinamičke redove", "visible": "Vidljiv", "cvcDbColumn": "<PERSON><PERSON><PERSON> s<PERSON>", "cvTableSource": "Izvorna tablica", "uploadedFromFile": "Preneseno iz datoteke", "appStatus": "Status aplikacije", "loadAll": "Učitaj sve", "ignoredUsers": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "copyRolesFrom": "Kopiranje uloga od", "disableFrontendStyles": "Ne primjenjujte automatske stilove", "activate": "Aktivirati", "confirmActivateCase": "Želite li stvarno aktivirati slučaj?", "alrLackOfPerms": "<PERSON>emate <PERSON>.", "alrSending": "Slanje...", "sequences": "Sekvence", "seqName": "Sekvenca", "seqId": "ID sekvence", "seqLastRead": "Posljednje čitanje", "ttCopyRole": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON> o<PERSON> ul<PERSON>."}, "fromCase": "<PERSON><PERSON>", "includingData": "Uključujući podatke", "choose": "<PERSON><PERSON><PERSON><PERSON>", "valueChange": "Promjena vrijednosti", "updateInstances": "Uključite promjenu varijabli u varijablama primjerka", "addNewCalcScript": "Dodajte novu skriptu", "useCompatibleCalcScript": "Ko<PERSON><PERSON> kompatibilnu sk<PERSON>", "externalSource": "Korisnički izvor", "reports": "Izvještaji", "confirmCopyReport": "Jeste li sigurni da želite kopirati odabrano izvješće?", "graphs": "<PERSON><PERSON><PERSON>", "aggregation": "Agregacija", "graphNew": "Grafikon – novo", "confirmCopyGraph": "Jeste li sigurni da želite kopirati odabrani grafikon?", "alrCopyGraphFailed": "Kopiranje grafa nije us<PERSON>.", "label": "<PERSON><PERSON>", "pie": "<PERSON><PERSON><PERSON> graf", "line": "Linij<PERSON> graf", "dot": "Točkasti grafikon", "bar": "Trakasti grafikon", "barGroups": "Trakasti grafikon – sku<PERSON>", "alrFailedGraphData": "Učitavanje grafikona nije uspjelo.", "graphSetSharing": "Postavite dijeljenje grafova za svaku grupu korisnika", "alrGraphPointsLoadFailed": "Učitavanje točaka grafikona nije uspjelo.", "alrGraphNotFound": "Grafikon nije pronađen.", "graphData": "Podaci gra<PERSON>a", "pointsData": "<PERSON><PERSON><PERSON><PERSON>", "alrGraphSaveFailed": "Spremanje grafa nije us<PERSON>jelo!", "graphPoint": "Točka grafikona", "noOrder": "<PERSON><PERSON>", "refreshGraph": "<PERSON><PERSON><PERSON><PERSON> graf", "viewSwitcher": "<PERSON><PERSON>lta<PERSON>", "axisXglobalFilter": "X-os – globalni filtar", "axisXgroups": "X-os – skupine", "axisXdata": "X-os – podaci", "axisYvalues": "Y-os – vrijednosti", "axisYcolors": "Y-os – boje", "hrAgenda": "HR agenda", "userChange": "<PERSON><PERSON><PERSON><PERSON>", "newUser": "Novi korisnik", "usersCount": "<PERSON><PERSON><PERSON>", "confirmChangeUser": "Jeste li sigurni da želite promijeniti korisnika?", "businessVariable": "Poslovna varijabla", "casesCount": "<PERSON><PERSON><PERSON>", "selected": "Odabran", "selectedOnly": "Samo odabrano", "addCaseRightNewUser": "<PERSON><PERSON><PERSON> p<PERSON> s<PERSON>", "visFromTaskToPull": "Vidljivost od zadatka za povlačenje", "toChangeConfigInfo": "Da biste to promi<PERSON><PERSON><PERSON>, izbrišite vrijednost iz datoteke local.js", "clickToChange": "Kliknite za promjenu", "currentValue": "Trenutna vrijednost", "sign": "Znak", "validationProtocols": "Potvrđivanje", "plannedEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elArchiv": "E-arhiva", "deletedDocs": "Izbrisano", "signatures": "Po<PERSON><PERSON><PERSON>", "ttArchive": {"heading": "Elektronički arhiv", "body": ""}, "ttAddToZea": {"heading": "Dodaj u elektroničku arhivu", "body": ""}, "ttRemoveFromZea": {"heading": "Ukloni iz elektroničke arhive", "body": ""}, "ttZeaInfo": {"heading": "Potvrđivanje", "body": ""}, "ttSignZea": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "addToZea": "Dodajte u e-arhivu", "removeFromZea": "Ukloni iz e-arhive", "reTimestampAfter": "Valjanost generirane pečata (dana)", "alrLoadFailed": "Učitavanje nije us<PERSON>.", "replace": "Zamijeniti", "expireAt": "Is<PERSON><PERSON>i će", "result": "Rezultat validacije", "validatedAt": "Da<PERSON> validacije", "refType": "Objekt", "eventType": "Vrsta akcije", "errorMessage": "Poruka o pogrešci", "errorTimestamp": "<PERSON><PERSON> p<PERSON>", "errorCount": "<PERSON><PERSON><PERSON>", "inFuture": "<PERSON><PERSON><PERSON>", "path": "Put do potpisa", "signedAt": "<PERSON><PERSON> stvaranja <PERSON>a", "dropZoneZeaCertificate": "Ovdje ispustite potvrda ili kliknite da biste odabrali datoteku za prijenos.", "authType": "Vrsta provjere autentičnosti", "basic": "Korisničko ime i lozinku", "byCert": "Sa potvrdom", "alrMissingCertFile": "Umetnite potvrdu.", "replaceTo": "Zamijeniti na", "autoReTimestamp": "Automatski vrem. žig", "validate": "Potvrđivanje", "lastUse": "Posljednje generirano", "createdAt": "Stvoreno u", "updatedAt": "Obnovljeno", "certificateId": "ID potvrde", "expectedCreationTime": "Bit će dodano", "nextTSPSourceId": "ID sljedećeg pečata", "reTimestampAt": "Sljedeći vrem. žig", "timestampedAt": "Zadnji pečat", "level": "<PERSON><PERSON>", "signatureB": "<PERSON><PERSON><PERSON><PERSON>", "signatureT": "Potpis s vremenskim žigom", "signatureLt": "Potpis s dugoročnim potvrdama podataka", "signatureLta": "Potpis s dugoročnim potvrdama podataka i vremenskom žigom arhiva", "packaging": "Pakiranje", "enveloped": "Umotan", "enveloping": "Omotnica", "detached": "Odvojen", "algorithm": "Algoritam", "uploadAsRevision": "Prenesite kao reviziju", "externalDisable": "Aktivan samo za skupne potpise", "addToDms": "Dodaj u DMS", "autoConvert": "Automatski pretvoriti", "format": "Format", "signatureType": "<PERSON><PERSON><PERSON> pot<PERSON>a", "signature": "Potpis", "custom": "Vlastiti", "batchSignDisabled": "<PERSON>z potpisa", "tasId": "ID dokumenta", "hashValue": "Hash vrijednost", "hashType": "Vrsta hash funkcije", "confirmAddToArchive": "Ž<PERSON>te li zaista dodati u arhivu?", "independentSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "independentValidation": "Nezavisni potvrđivanje", "failureTrue": "S greškom", "failureFalse": "<PERSON>z gre<PERSON>ka", "confirmValidateDialog": "Želite li zaista potvrditi potpis?", "confirmRestartDialog": "Želite li zaista resetirati pogreške?", "verificationResult": "Rezultat provjere", "integrityMaintained": "Održavan integritet", "signatureFormat": "Format potpisa", "internalTimestampsList": "Popis internih vremenskih žigova", "signers": "Potpisnici", "exhibitedBy": "<PERSON><PERSON><PERSON><PERSON>", "signedBy": "Potpisao", "validFrom": "Vrijedi od", "validUntil": "Vrijedi do", "signitureType": "<PERSON><PERSON><PERSON> pot<PERSON>a", "signatureQualification": "Kvalifikacija potpisa", "signatureNoTimestamps": "Potpis ne sadrži vremenske oznake", "electronicSignature": "Elektronički potpis", "electronicSeal": "Elektronski pečat", "webSiteAuthentication": "Autentifikacija web mjesta", "QCP-n": "QCP-n: politika certifikata za EU kvalificirane certifikate izdane fizičkim osobama", "QCP-l": "QCP-l: politika certifikata za EU kvalificirane certifikate izdane pravnim osobama", "QCP-n-qscd": "QCP-n-qscd: politika certifikata za EU kvalificirane certifikate izdane fizičkim osobama s privatnim ključem koji se odnose na ovjereni javni ključ u QSCD", "QCP-l-qscd": "QCP-l-qscd: politika certifikata za EU kvalificirane certifikate izdane pravnim osobama s privatnim ključem koji se odnose na ovjereni javni ključ u QSCD", "QCP-w": "QCP-w: pravila o certifikatima za certifikaciju o vjerodostojnosti EU kvalificiranih web stranica", "formOfReStamping": "Oblik ponovnog žigosanja", "individually": "Pojedinačno", "archiveAsPdf": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "couldNotBeVerified": "<PERSON><PERSON> mog<PERSON> provjer<PERSON>", "uses": "<PERSON><PERSON><PERSON>", "countOfSignedDocuments": "<PERSON><PERSON><PERSON> do<PERSON>", "batchSignature": "<PERSON><PERSON><PERSON><PERSON> pot<PERSON>", "standaloneSign": "Pojedina<PERSON><PERSON>", "validateSignature": "Potvrda potpisa", "validateDoc": "Potvrda dokumenta", "containsSignature": "<PERSON><PERSON><PERSON><PERSON>", "reStamping": "Ponovnog žigosanja", "individualSignatures": "Pojedinačni potpisi", "signatureLevel": "<PERSON><PERSON><PERSON>", "simpleReport": "Jednostavno izvješće", "detailedReport": "Detaljno izv<PERSON>šće", "diagnosticReport": "Dijagnostičko izvješće", "etsiReport": "ETSI izvješće", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "Failed", "INDETERMINATE": "Indeterminate", "FORMAT_FAILURE": "Signature does not comply with one of the basic standards", "HASH_FAILURE": "The signed data object's hash does not match the hash in the signature", "SIG_CRYPTO_FAILURE": "The signature could not be verified with the signer's public key", "REVOKED": "The signature certificate has been revoked and there is evidence that the signature has been created after the revocation", "SIG_CONSTRAINTS_FAILURE": "One or more signature attributes do not match the validation rules", "CHAIN_CONSTRAINTS_FAILURE": "The certificate chain used in the validation process does not comply with the certificate validation rules", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "The set of certificates available for string verification caused an error for an unspecified reason", "CRYPTO_CONSTRAINTS_FAILURE": "One of the signature verification algorithms is below the required cryptographic security level and the signature was acquired after the algorithm lifetime", "EXPIRED": "The signature was created after the signature certificate expired", "NOT_YET_VALID": "The signing time lies before the issuance date of the signing certificate", "POLICY_PROCESSING_ERROR": "The validation policy file could not be processed", "SIGNATURE_POLICY_NOT_AVAILABLE": "The electronic document containing details about the validation policy is not available", "TIMESTAMP_ORDER_FAILURE": "Restrictions in signature timestamp order are not respected", "NO_SIGNING_CERTIFICATE_FOUND": "The signing certificate cannot be identifie", "NO_CERTIFICATE_CHAIN_FOUND": "No certificate chain was found for the identified signature certificate", "REVOKED_NO_POE": "The signing certificate was revoked at the validation date/time. However, the signature verification algorithm cannot detect that the signature time is before or after the revocation period", "REVOKED_CA_NO_POE": "At least one certificate chain was found, but a temporary CA certificate was revoked", "OUT_OF_BOUNDS_NOT_REVOKED": "The signing certificate is expired or not yet valid at the validation date/time and the Signature Validation Algorithm cannot ascertain that the signing time lies within the validity interval of the signing certificate. The certificate is known not to be revoked.", "OUT_OF_BOUNDS_NO_POE": "The signing certificate has expired or is not yet valid at the verification date/time", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "One of the signature verification algorithms is below the required cryptographic security level and there is no proof that it was produced before the algorithm/key was considered secure", "NO_POE": "There is no proof that the signed object was created before a compromising event", "TRY_LATER": "Not all validation rules can be met with the available information, but it may be possible to do so with additional revocation information that will be available later", "SIGNED_DATA_NOT_FOUND": "Signed data cannot be obtained", "GENERIC": "Other Reason", "signatureFile": "Datoteka s potpisom", "validityDays": "<PERSON>", "qualifiedHe": "Kvalificiran", "qualifiedIt": "Kvalificiran", "unqualifiedHe": "Nekvalificiran", "unqualifiedIt": "Nekvalificiran", "timeValid": "<PERSON><PERSON><PERSON><PERSON> vrije<PERSON>", "reason": "Razlog", "inTime": "u vremenu", "certificateQualification": "Kvalifikacija certifikata", "guaranteedHe": "Zajamčena", "guaranteedIt": "Zajamčene", "fromQualifiedCert": "<PERSON><PERSON> cert.", "basedOnQualifiedCertHe": "Na temelju kvalificiranog certifikata", "createdByQualifiedResHe": "Kreirao kvalificirani resurs", "basedOnQualifiedCertIt": "Na temelju kvalificiranog certifikata", "createdByQualifiedResIt": "Kreirao kvalificirani resurs", "qualification": "Kvalifikacije", "confirmRemoveFromZeaDialog": "Da li stvarno želite izbrisati {{variable}} iz elektroničke arhive?", "noValidationReports": "Nema rezultata provjere valjanosti", "noSignatures": "<PERSON><PERSON> po<PERSON><PERSON>", "isHigherOrEqualThan": "Mora biti veći od ili jednak", "isInZea": "U e-arhivu", "startStamping": "Počnite žigosanje", "reTimestampAfterMinutes": "minute", "reTimestampAfterDays": "dana", "reTimestampAfterAll": "Valjanost generirane pečata", "refId": "ID objekta", "docWithoutAutoTimestampInfo": "Dokument će biti potpisan jed<PERSON>m, bez automatskog umetanja vremenske oznake.", "validationReports": "Povijest provjere valjanosti", "docPath": "Put dokumenta", "addToArchiveInvalidSignatureError": "Datoteka se ne može arhivirati jer sadrži potpis koji se ne može provjeriti.", "signImmediately": "Potpisati odmah", "replaceInConfiguration": "Zamijenite u konfiguraciji", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bulk": "<PERSON><PERSON><PERSON><PERSON>", "bulkCompletion": "Skupni završetak", "enableBulkCompletion": "Omogući skupni završetak", "confirmCompleteTasks": "Želite li stvarno dovršiti zadatke?", "plannedMaintenance": "Planirano održavanje", "notSent": "<PERSON><PERSON> p<PERSON>", "notSpecified": "<PERSON><PERSON>", "bulkCompletionVars": "Varijable skupnog dovršenja", "alrBulkCompletionMultiTypeErr": "Skupno se mogu izvršiti samo zadaci iste vrste, možete koristiti filtar.", "notifications": "Obavijesti", "alrTskAlreadyTakenSomeone": "Netko je drugi već preuzeo zadatak.", "alrTskAlreadyTaken": "Zadatak je već preuzet.", "downloadBpmn": "preuzmi BPMN dijagram", "downloadSvg": "preuzmi kao SVG sliku", "displayForm": "<PERSON>rsta p<PERSON>", "selectedPreview": "prika<PERSON><PERSON> se pregled", "fixedHeight": "Fiksna visina (u pikselima)", "lastVersion": "Najnovija verzija", "separatedPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON> pregled", "defaultZoom": "<PERSON><PERSON><PERSON> zum", "fixedPosition": "Fiksni <PERSON>", "percentInterval": "Unesite cijeli broj između 0–5", "notPositiveNumber": "<PERSON>o pozitivne brojke", "zoomDown": "Se smanjiti", "zoomUp": "Uveličati", "rotate": "<PERSON><PERSON><PERSON><PERSON>", "logTooBig": "Evidencija je prevelika da bi se prikazala.", "downloadLog": "Zapisnik preuzimanja", "confirmCopyCron": "Ž<PERSON>te li zaista kopirati odabrani cron?", "ttCopyCron": {"heading": "<PERSON><PERSON><PERSON> cron", "body": ""}, "onlyWorkingDays": "<PERSON>o radni dani", "datesDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> datume", "useView": "Koristite View", "dateWithoutTime": "Datum bez vremena", "timezone": "Vremenska zona", "roleRestriction": "Ograničenje ul<PERSON>", "headerRestriction": "Ograničenje zaglavlja", "ttSwitchDarkmode": {"heading": "Prebacivanje svijetlog / tamnog načina rada", "body": ""}, "advancedEditor": "Napredni urednik", "externalId": "Vanjski ID", "passwordValidationMin": "Lozinka je prekratka. (<PERSON>na duljina: {{count}})", "passwordValidationMax": "Lozinka je predugačka. (maks<PERSON>lna duljina: {{count}})", "passwordValidationUppercase": "Lozinka mora sadržavati veliko slovo. {{atLeast}}", "passwordValidationLowercase": "Lozinka mora sadr<PERSON><PERSON><PERSON> malo slovo. {{atLeast}}", "passwordValidationSymbols": "Lozinka mora sadržavati simbol. {{atLeast}}", "passwordValidationDigits": "Lozinka mora sadržavati broj. {{atLeast}}", "passwordValidationLetters": "Lozinka mora sadr<PERSON><PERSON><PERSON> slovo. {{atLeast}}", "atLeast": "<PERSON><PERSON>", "passwordValidationServiceErr": "Lozinka se trenutno ne može promijeniti.", "enableTasksHandoverRole": "Korisnicima ove uloge uvijek dopustite prosljeđivanje zadataka i pokretanje događaja", "shredded": "<PERSON><PERSON><PERSON><PERSON>", "shredableVar": "Varijabla za usitnjavanje", "shredDocuments": "Uništite dokumente", "shredInDays": "<PERSON><PERSON><PERSON><PERSON> za (dana)", "fromBeginningOrendOfCase": "<PERSON><PERSON> p<PERSON>/kraja slu<PERSON>", "shredding": "Usitnjavanje", "addColumn": "<PERSON><PERSON><PERSON> stupa<PERSON>", "unsupportedBrowser": "Otvarate TeamAssistant u nepodržanom pregledniku Internet Explorer, neke značajke možda neće biti dostupne.", "ingoreProcessRights": "Ignoriranje prava na slučajeve", "cvHelpIngoreProcessRights": "Pregled uvijek prikazuje sve slučajeve, bez obzira na prava", "upperLabel": "Stavite varijablu pod njezin naziv", "darkMode": "<PERSON><PERSON> rada", "completedTasks": "Dovrš<PERSON>", "permissions": "Dopuštenja", "caseVisibility": "Vidljivost slučaja", "visPerOrg": "Vidljivost po org. jedinici", "entity": "Entitet", "staticRight": "Statički zakon", "dynamicRight": "Dinamički zakon", "treeNodesAll": "Svi", "treeNodesMy": "<PERSON><PERSON>", "activeQueries": "Aktivni upiti", "query": "Upit", "confirmCancelQuery": "Jeste li sigurni da želite otkazati upit?", "alrQueryNotFound": "Upit više nije pronađen.", "completeAgenda": "Kompletan agenda", "lockedBusinessUsers": "Zaključani poslovni korisnici", "structuredList": "Strukturirana lista", "ttCompetences": {"heading": "Upravljanje kompetencijama", "body": ""}, "competences": "Kompetencije", "competence": "Kompetencije", "competenceDelVar": "kompetenciju", "addCompetence": "Dodajte kompetenciju", "regularExpression": "Regularni izraz", "generationStatus": "Status generacije", "source": "<PERSON><PERSON><PERSON>", "historical": "Povijesni", "external": "<PERSON><PERSON><PERSON>", "nextDay": "s<PERSON><PERSON><PERSON><PERSON><PERSON> dan", "embeddedVideoNotSupported": "<PERSON><PERSON><PERSON><PERSON>, vaš preglednik ne podržava ugrađene videozapise.", "alrSendingTestMailFailed": "Test e-pošte nije uspio.", "sent": "Poslano.", "mainColorEmail": "Glavna boja e-pošte", "confirmResetColors": "Jeste li sigurni da želite resetirati boje?", "regularExpressions": "Regularni izrazi", "confirmDeleteLogo": "Jeste li sigurni da želite izbrisati logotip?", "loginLogoLightTheme": "Logo zaslona za prijavu (svjetlosni način rada)", "loginLogoDarkTheme": "Logo z<PERSON>lona za prijavu (tamni način rada)", "competenceRegexHelper": "<ul><li><b>%</b> može se koristiti kao N proizvoljnih znakova (ekvivalent *)</li><li><b>_</b> može se koristiti kao jedan proizvoljan znak (ekvivalent .)</li><li>Možete koristiti <b>^</b> za izbjegavanje ovih posebnih znakova (ekvivalent \\)</li></ul>", "headerFont": "Font zaglavlja", "evenRow": "Parni red", "logo": "Logo", "widthForLogo": "Širina za logo", "monthStart": "Početak mjeseca", "monthEnd": "<PERSON><PERSON>", "ttFavouriteType": "GET otvara vezu. POST šalje naredbu: na primjer, kada kreirate slučaj, kada se id zaglavlja predloška šalje u tijelu zahtjeva (možete spremiti u favorite putem New case).", "confirmEmptyMultiinstanceVariable": "Jeste li sigurni da ova višestruka instanca ne zahtijeva varijablu za ponavljanje?", "ttMenuPreview": "Konfiguracija izbornika prema korisničkim ulogama (značajnije uloge također vide gumbe za manje značajne uloge). Gumbi Novi slučaj i Nadzorna ploča su nepromijenjeni.", "menuPreview": "Pregled izbornika za odabranu ulogu", "confirmResetMenu": "Jeste li sigurni da želite resetirati izbornik?", "alrFailedTasMenu": "Učitavanje konfiguracije izbornika TAS nije uspjelo!", "security": "<PERSON><PERSON><PERSON><PERSON>", "userRestrictions": "Ograničenja korisnika (prikaz)", "userRestrictionsProcesses": "Ignorirajte korisnička ograničenja za zadatke", "roleRestrictions": "Ogranič<PERSON>ja <PERSON> (prikaz)", "orgUnitRestrictions": "Ograničenja org. jedinice (prikaz)", "everyone": "Svi", "colleaguesOnly": "<PERSON><PERSON> kolege", "directSubordinates": "Izravni podređeni", "allSubordinates": "Svi podređeni", "none": "<PERSON><PERSON><PERSON>", "generalDocument": "Opći dokument", "competenceRule": "Pravilo kompetencije", "competenceRules": "Pravila kompetencije", "ruleName": "<PERSON><PERSON>", "ttUseCompetenceRule": {"heading": "Primijenite pravilo", "body": "<PERSON><PERSON><PERSON> kompetenciju prema odabranom pravilu"}, "competenceText": "Tekst kompetencije", "competenceName": "Naziv kompetencije", "competenceReadOnlyInfo": "Kompetencija stvorena iz pravila ne može se mijenjati", "xmlProcessImport": "Uvoz XML procesa", "ttWidthForLogo": "Postavite širinu za logotip, a zatim umetnite logotip. <PERSON><PERSON> moguće promijeniti širinu za već umetnuti ili zadani logotip.", "openCase": "<PERSON><PERSON><PERSON><PERSON>", "importHistory": "Povijest uvoza", "plannedImports": "Planirani u<PERSON>", "filePath": "<PERSON><PERSON><PERSON>", "cronId": "ID crona", "taskResult": "Rezultat zadatka", "xmlFileSize": "Veličina XML datoteke", "attachmentSize": "Veličina privitka", "lastEdit": "Zadnja izmjena", "timeCreated": "<PERSON><PERSON><PERSON><PERSON>", "importId": "ID uvoza", "importAudit": "Povijest uvoza", "finishedImports": "Završeni u<PERSON>zi", "insertNote": "Umetnite belešku", "importXml": "Uvoz XML", "reImportXml": "Ponovni uvoz XML", "downloadXml": "Preuzmi XML", "downloadAttachment": "Preuzmi privitak", "skipXml": "Preskoči XML", "note": "Beleška", "attachmentName": "Naziv privitka", "importedCount": "<PERSON><PERSON><PERSON> u<PERSON>", "retryCount": "<PERSON><PERSON><PERSON>", "batchId": "ID doza", "copyPath": "<PERSON><PERSON><PERSON><PERSON> put", "cronRunId": "ID futtatásának", "cronRun": "Cron futás", "trace_id": "ID traga", "ttMenuItemLabel": "Univerzalni naziv ako nema prijevoda. Ako se koristi ključna riječ prijevoda, prevodi se automatski. Zadana imena: tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "Red čekanja zadataka", "dissolveQueue": "Raspustiti red", "taskQueueInitInfo": "Ova je radnja stvorila više zadataka koje je trebalo riješiti. Ovdje možete promijeniti redoslijed njihovog rješavanja ili ih potpuno ukloniti iz reda čekanja.", "tryDarkTheme": "Primijetili smo da preferirate tamni način rada. Kliknite da isprobate u TAS-u.", "alrInvalidURL": "Nevažeći format URL-a.", "alrInvalidHttps": "Nevažeći format URL-a, mora započeti s https://", "importVariables": "Uvozne varijable", "ttVariablesImport": {"heading": "Uvozne varijable", "body": "Odabrana je mapa s definicijama varijable, a zatim prenesena."}, "classDiagram": "Dijagram klasa", "createVar": "Stvoriti varijablu", "importObjectStates": "Uvoz stanja objekta", "unassigned": "Nedodijeljeno", "sortVars": "Sortiranje", "fillNames": "Popunite imena", "ttFillNames": {"heading": "Popunite imena", "body": "Upunjuje prazna imena svih novih varijabli u formatu \"Klasa.Atribut\" i sortira sve varijable."}, "ttSortVars": {"heading": "Sortiranje", "body": "Sortira varijable prema klasama i atributima."}, "ttRestore": {"heading": "Vraćanje", "body": "Vraća varijable u njihovo izvorno stanje kada se uvoze iz datoteke."}, "ttAddVarToBottom": {"heading": "<PERSON><PERSON><PERSON>", "body": "Dodaje novu varijablu na dno stranice."}, "confirmRestoreForm": "Želite li stvarno vratiti varijable u njihovo izvorno stanje?", "selectClass": "Označi k<PERSON>a", "importClassDiagram": "Uvoz dijagrama klasa", "continue": "<PERSON><PERSON><PERSON>", "templateVars": "Varija<PERSON> predloška", "newVars": "Nove varijable", "objectState": "<PERSON><PERSON> objekta", "alrDynTableExists": "Dinamička tablica već postoji!", "overwriteExistDynTable": "Prebriši postojeću din. tablicu", "confirmCancelImport": "Jeste li sigurni da želite prekinuti uvoz?", "alrDuplicateNames": "Podaci sadr<PERSON> duple nazive.", "stateVar": "Varijabla stanja", "importObjectStatesToDynTables": "Uvoz objekta stanja u dinamične tablice.", "defineObjectStatesVars": "Definirajte varijable koje drže stanja objekta.", "change": "Izmijeniti", "classAndAttr": "Klasa i atribut", "clearQueue": "Očisti red čekanja", "sharing": "Dijeljenje", "data": "<PERSON><PERSON><PERSON>", "open": "Otvorite", "dataSource": "<PERSON><PERSON><PERSON>", "dataPoints": "Podatke", "dataSeries": "Serija podataka", "valueCol": "Stupac vrijednosti", "aggregationCol": "Stupac združivanja", "timeDimension": "Vrem. dimenzija", "columns": "<PERSON><PERSON><PERSON>", "week": "t<PERSON><PERSON>", "weekday": "radni dan", "monthVar": "mjesec", "overviewFilter": "Filter pre<PERSON>a", "globalFilters": "<PERSON>ni filtri", "filterDefinition": "Definicija filtra", "newFilter": "<PERSON> filtar", "addFilter": "<PERSON><PERSON><PERSON> filter", "filterOptions": "Opcije filtra", "addOption": "<PERSON><PERSON><PERSON>", "graphPreview": "<PERSON><PERSON> grafi<PERSON>a", "alrGlobalFilterDownloadFailed": "Neuspješno preuzimanje globalnih filtara!", "alrGlobalFilterSaveFailed": "<PERSON><PERSON> us<PERSON>jelo spremanje globalnih filtara!", "filterOption": "Opcija filtra", "editFilter": "Uredite filter", "fillOptionsFromVar": "Ispunite opcije iz varijable", "fillOptionsDynamically": "Dinamičko popunjavanje opcija", "filterOptionsFilledDynamically": "Dinamički iz varijable", "dayOfMonth": "dan u mje<PERSON>cu", "dateVar": "datum", "group": "<PERSON><PERSON><PERSON>", "ttDataSource": "Odaberite \"Podatkovne točke\" ako želite zasebno unijeti svaku pojedinačnu točku grafikona. Ako želite da se točke generiraju na temelju odabrane dimenzije, odaberite \"Serije podataka\"", "ttDataSeriesAggregation": "Odaberite vrstu agregacije. Omogućuje vam stvaranje sažetih informacija iz zapisa (slučajevi).", "ttDataSeriesColumns": "Odaberite redom sve stupce prema kojima ćete stvoriti grupe (agregacije) za izračun sažetih vrijednosti.", "listOfFiltersIsEmpty": "Popis filtra je prazan.", "fromVariable": "<PERSON>z varijable", "showOptionsFromCount": "<PERSON><PERSON><PERSON><PERSON> op<PERSON> (od {{count}})", "sum": "<PERSON><PERSON><PERSON>", "minimum": "Minimum", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Statistike", "unfilled": "Neispunjeno", "globalFilterDescription": "Globalni filter pruža korisnicima grafikona opcije koje filtriraju ulazne podatke za grafikon. Sve opcije filtera mogu se definirati na ovom ekranu.", "ttDelGraph": {"heading": "Izbriši grafikon", "body": "B<PERSON>še odabrani grafikon."}, "ttEditGraph": {"heading": "<PERSON>redi gra<PERSON>", "body": "Omogućuje vam uređivanje odabranog grafikona."}, "ttCopyGraph": {"heading": "<PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON> o<PERSON> grafi<PERSON>."}, "ttAddGraph": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućuje definiranje novog grafikona."}, "axisXName": "Naziv osi X", "axisYName": "Naziv osi Y", "showValues": "Prikaži vrijednosti", "defaultOption": "Zadana opci<PERSON>", "yearStart": "Početak godine", "yearEnd": "<PERSON><PERSON> godine", "thisMonth": "<PERSON><PERSON><PERSON>", "lastMonth": "<PERSON><PERSON><PERSON>", "thisYear": "<PERSON>ve godine", "lastYear": "<PERSON><PERSON><PERSON>", "scheduledTasks": "<PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON>", "dueDateStart": "<PERSON>tum <PERSON>", "lastRescheduled": "Posljednje ponovno zakazano", "reschedule": "<PERSON><PERSON><PERSON> r<PERSON>", "alrTasksRescheduling": "<PERSON><PERSON><PERSON><PERSON> ras<PERSON>da zadataka...", "alrTasksRescheduled": "Zadaci su ponovno raspoređeni.", "alrTasksRescheduleFailed": "<PERSON><PERSON>jena rasporeda zadataka nije us<PERSON>la.", "onlyCurrentOrFutureDates": "<PERSON>o danas ili budući datumi", "passwordValidations": "Politika lozinke", "readonlyConfigInfo": "Vrijednost je samo za čitanje", "alrTasksCountFailed": "Brojenje zadataka nije uspjelo.", "confirmActivateTasks": "Jeste li sigurni da želite aktivirati odabrane zadatke?", "confirmSuspendTasks": "Jeste li sigurni da želite obustaviti odabrane zadatke?", "tskOffset": "Planska varijabla", "workWeek": "<PERSON><PERSON><PERSON>", "agenda": "Dnevni red", "noEventsInRange": "U ovom rasponu nema događaja", "activitiesDesc": "Opis aktivnosti", "allShort": "Svi", "numberOfEvents": "<PERSON><PERSON><PERSON>", "weekNumber": "t<PERSON><PERSON> broj", "cannotBeEdited": "Ne može se uređivati", "cannotBeMoved": "Ne može se premjestiti", "alrTempVarSaveSameNameFailed": "Varijabla s ovim zadanim nazivom već postoji, unesite drugi naziv.", "maxUsersCountRole": "Maksimalan broj korisnika u ulozi", "unlimitedAssignLeaveBlankInfo": "Za neograničene zadatke ostavite polje prazno.", "cvOwner": "Vlasnik pregleda", "changePassword": "Promijenite lozinku", "passwordExpired": "Vaša lo<PERSON> je istekla. Promijenite je.", "passwordWillExpire": "Vaša lozinka će uskoro isteći. Unesite novu lozinku.", "userParameters": "Korisnički parametri", "filterSortingHelper": "Le tri sur une ou plusieurs colonnes dans un filtre désactive l'option de tri manuel des colonnes directement dans le tableau.", "importUsers": "Uvezi koris<PERSON>", "importRoles": "<PERSON><PERSON><PERSON>", "existingEntityRows": "Redovi s već postojećim entitetima (mogu se prebrisati)", "fileRow": "Red datoteke", "existingEntityRowsMultiple": "Redovi s entitetima koji već postoje više puta (neće biti uvezeni)", "importOrgUnits": "Uvezi organizacijske jedinice", "structureImportExport": "Uvoz/izvoz strukture", "fillAttributes": "Popuni atribute", "structurePreview": "Pregled strukture", "invalidRowsForImport": "Nevažeći reci (nedostaju obavezni podaci)", "duplicateRowsForImport": "Redovi s duplikatima odgovarajućih podataka (neće biti uvezeni)", "newEntityRows": "Redovi s novim entitetima za uvoz", "existingNameRowsForImport": "Redovi s nazivima koji već postoje na drugim entitetima (neće biti uvezeni)", "overwriteExisting": "Prebriši <PERSON>", "structurePreviewHelper": "Pretpregled strukture prikazuje dvije različite situacije: uvoz samo novih organizacija ili uvoz novih i postojećih organizacija koje će biti prebrisane. Sve promjene u usporedbi s postojećom strukturom označene su crvenom bojom.", "validateAndShowPreview": "Provjerite i pogledajte pregled", "uploadNewFile": "Učitaj novu datoteku", "userStatus": "Status korisnika", "importedFile": "<PERSON><PERSON><PERSON>", "pairUsersBy": "Uparite korisnike po", "assignOrgBy": "Dodijeli organizaciji po", "pairRolesBy": "Uparite uloge po", "pairUnitsBy": "Uparite jedinice po", "unitHierarchyCol": "Stupac hijerarhije jedini<PERSON>", "dontAssign": "<PERSON><PERSON><PERSON><PERSON>", "infoImportDataValidated": "UPOZORENJE: Podaci su upravo potvrđeni zbog promjena u postavkama. Preporučujemo da se vratite i provjerite novi pregled uvoza.", "assignUserRolesMethod": "Metoda za dodjelu uloga korisnicima", "assignUserRolesMethodHelp": "Metoda dodjele uloga: dodajte već dodijeljenim ulogama ili potpuno zamijenite trenutno dodijeljene uloge novododijeljenim ulogama.", "existingRolesForImport": "<PERSON><PERSON><PERSON> postoje<PERSON>e uloge (mogu se prebrisati)", "existingRoleNamesForImport": "Uloge s imenima koja već postoje s drugim ul<PERSON>ma (neće biti uvezene)", "newRolesForImport": "Nove uloge za uvoz", "userRolesForImport": "Redovi s korisničkim ulogama za dodjelu", "nonExistentUsersForImport": "Redovi s nepostojeć<PERSON> korisnic<PERSON> (uloge neće biti dodijeljene)", "multipleExistingUsersForImport": "Redovi s više od jednog postojećeg korisnika (uloge neće biti dodijeljene)", "invalidOrgsForImport": "Nevažeći retci (nedostaju obvezni podaci ili pogrešna hijerarhija)", "keepOriginal": "Zadrži izvornik", "assignOrgByHelp": "Ako odaberete stupac iz datoteke, možete navesti klasifikaciju organizacije za nove i postojeće korisnike. Ako odaberete određenu organizaciju, svi uvezeni ili ažurirani korisnici bit će dodijeljeni ovoj organizaciji.", "creatingRoles": "<PERSON><PERSON><PERSON><PERSON>", "assigningRolesToUsers": "Dodjeljivanje uloga korisnicima", "newUsers": "Novi korisnici", "existingUsers": "Postojeći korisnici", "fromFile": "<PERSON><PERSON> da<PERSON><PERSON>ke", "alrCsvXlsxUploadWrongExtension": "Učitaj samo datoteke s ekstenzijom *.csv ili *.xlsx", "importNewAndExisting": "Uvezi nove entitete i prebriši postojeće", "importNewOnly": "Uvezi samo nove entitete", "importNewAndExistingRoles": "Uvezi nove uloge i prebriši postojeće uloge", "importNewRolesOnly": "Uvezi samo nove uloge", "importRolesHelper": "Postavke za uvoz samih uloga. Dodjeljivanje uloga korisnicima regulirano je onim što je postavljeno u \"Uparite korisnike po\" i uvijek se primjenjuje na nove i postojeće uloge.", "statisticsColorHelper": "Ako boje nisu odabrane ručno ili ako ima manje odabranih boja nego stupa<PERSON>, boje koje nedostaju generiraju se automatski. Generirane boje nikada ne sadrže tamne ili presvijetle nijan<PERSON>, one se mogu odabrati samo ručno.", "caseService": "Usluga slučaja", "taskService": "Usluga zadataka", "editTasks": "<PERSON><PERSON><PERSON>", "editCases": "<PERSON><PERSON><PERSON>", "deleteTasks": "Izbriši zadatke", "deleteCases": "<PERSON><PERSON><PERSON><PERSON>", "serviceOperationsInfo": "Označite i ispunite varijable koje želite promijeniti.", "erased": "<PERSON><PERSON>brisani", "statusErrored": "Greška", "serviceOperations": "Servisne operacije", "runCalcsOnStart": "Pokreni izračune na početku", "taskReactivation": "Ponovno aktiviranje zadataka", "taskCompletion": "Završetak zadataka", "caseReactivation": "Ponovno aktiviranje slučajeva", "caseCompletion": "Završetak slučajeva", "openTask": "Otvori zadatak", "changeEntity": "<PERSON><PERSON><PERSON><PERSON> en<PERSON>", "selectTableColumns": "Odaberi stupce tablice", "parentCase": "<PERSON><PERSON><PERSON><PERSON>", "ownerOrganization": "Vlasnička organizacija", "confirmTaskReactivation": "Jeste li sigurni da želite ponovno aktivirati odabrane zadatke?", "confirmCaseReactivation": "Jeste li sigurni da želite ponovno aktivirati odabrane slučajeve?", "confirmTaskCompletion": "Jeste li sigurni da želite dovršiti odabrane zadatke?", "confirmCaseCompletion": "Jeste li sigurni da želite dovršiti odabrane slučajeve?", "selectAllFilterMustBeActive": "<PERSON>j<PERSON><PERSON> jedan filtar mora biti aktivan za odabir svih stavki.", "changeEntities": "Promijeni en<PERSON>", "disabledDifferentTemplates": "<PERSON><PERSON> mogu<PERSON>e promijeniti jer entiteti nisu iz istog predloš<PERSON>.", "actions": "<PERSON><PERSON><PERSON><PERSON>", "taskTemplateId": "ID predloška zadataka", "caseTemplateId": "ID predloška slučaja", "actionInfoCheckLogs": "Akcija će se izvršiti u pozadini, provjerite zapise.", "alrServiceOperationsColumnsFailed": "Spremanje postavki stupaca servisnih operacija nije us<PERSON>lo.", "confirmResetSelectedCols": "Jeste li sigurni da želite poništiti spremljene stupce tablice?", "instanceVars": "Varijable instance", "usrId": "ID korisnika", "orgId": "ID organizacije", "titlePrefix": "Prefiks naslova", "titleSuffix": "Sufiks naslova", "accessRoleId": "ID pristupne ul<PERSON>e", "maxAssigns": "Maks<PERSON>lni broj dod<PERSON>", "client": "kli<PERSON><PERSON>", "bigValue": "Velika vrijednost", "unitId": "ID jedinice", "roleId": "<PERSON> uloge", "paramId": "ID parametra", "varId": "ID varijable", "parentId": "ID roditelja", "openUser": "Otvori korisnika", "openRole": "<PERSON><PERSON><PERSON><PERSON> ul<PERSON>", "openUnit": "<PERSON><PERSON>vor<PERSON> jed<PERSON>ca", "units": "<PERSON><PERSON><PERSON>", "managerId": "ID upravitelja", "externalStatus": "Vanjski status", "additionalId": "Dodatni ID", "parentIc": "IC roditelja", "companyIc": "IC tvrtke", "textValue": "Tekstualna vrijednost", "dateValue": "Datumska vrijednost", "numberValue": "Numerička vrijednost", "loginCount": "<PERSON><PERSON><PERSON>", "externalLogin": "Vanjska prijava", "badLoginCount": "Broj loših prijava", "passwordLastChange": "Posljednja promjena lozinke", "solverEvaluation": "Procjena vlasnika zadatka", "solverWillBe": "Vlasnik zadatka bit će", "possibleSolvers": "Mogući vlasnici zadataka", "selectReferencePerson": "Odaberite referentnu o<PERSON>bu", "evaluateSolver": "Procijeni <PERSON>", "referenceUserForEval": "Referentna osoba za procjenu", "andOthers": "...i drugi", "showLess": "...prika<PERSON><PERSON> manje", "alrSessionExpired": "<PERSON><PERSON>ša sesija je istekla, prijavite se ponovo.", "mailPromptlyInfo": "Korisnički kontinuirani prima jednokratne obavijesti o novim zadacima, gdje su oni rješivač. Ove će se upozorenja poslati samo ako zadatak nije riješen {{minutes}} minuta od njegove aktivacije.", "mailPullInfo": "Korisnik kontinuirano prima jednokratne obavijesti o novim zadacima koji su dostupni za pretplatu, a korisnik je njihov mogući rješavanje. Obavijest izlazi u trenutku aktiviranja zadanog zadatka u tijeku rada.", "mailTotalInfo": "Korisnik povremeno prima pregled sa zadacima koje treba dovršiti, od kojih su rješivač. Ako zadatak nema izravnog rješavanja, vlasnik postupka je obaviješten. Ako je korisnik zastupljen, obavijest prima njegov predstavnik.", "mailEscalationInfo": "Korisnik povremeno prima pregled sa zadacima koje treba dovršiti koji su nadmašili rok. Obaviješteni su jesu li nadzornik zadatka (a ne istovremeno njegov rješavanje) ili su izravni upravitelj korisnika koji je rješavatelj. Ako zadatak nema rješavanje, vlasnik procesa smatra se nadzornikom. Ako je korisnik zastupljen, obavijest spominje tko je trenutni predstavnik.", "calcSourceOverwriteWarning": "<PERSON><PERSON> spremanja, izvor se prepisuje ES6 sintaksom!", "changeStatus": "Promijenite status", "confirmChangeEmailStatus": "Želite li stvarno promijeniti status odabranih e-poruka?", "logInAgain": "Prijavite se ponovo", "migrations": "<PERSON><PERSON><PERSON><PERSON>", "launchDate": "<PERSON><PERSON> la<PERSON>", "stepName": "Naziv koraka", "runId": "Pokrenuti id", "clone": "Klon", "confirmDeleteCron": "Želite li stvarno izbrisati odabrani cron?", "alrCronDeleted": "<PERSON><PERSON> je i<PERSON>!", "wantToContinueQ": "Želite li nastaviti?", "valueCannotBeEntered": "Vrijednost se ne može unijeti", "processingQueues": "Obrada čekanja", "pause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fillOptionsFromVarHelper": "Opcije filtra mogu se ispuniti samo iz varijabli tipa DT, DL, LT, LD, LN i D, koje ne dopuštaju višestruki odabir.", "defaultTemplateName": "Zadani naziv predloška", "defaultTaskName": "Zadani naziv zadatka", "defaultVariableName": "Zadani naziv varijable", "variableName": "<PERSON><PERSON> varijable", "alrNoDataFound": "Nisu pronađeni podaci", "ttProcessingQueuesInfo": "Redovi čekanja za obradu su onemogućeni.\n<PERSON> biste o<PERSON>, post<PERSON>te barem jednu od konfiguracija \"scaling.queue.*.enabled\" na true.", "businessUsers": "Poslovni korisnici", "completeHrAgenda": "Kompletan HR plan", "usageStatsByHeader": "Statistika korištenja po zaglavlju", "usageStatsByOrgUnit": "Statistika korištenja po org. jedinici", "usageStatsByUser": "Statistika korištenja po korisniku", "completedTasksNum": "<PERSON><PERSON>j dov<PERSON><PERSON><PERSON><PERSON> zadataka", "startedProcessesNum": "B<PERSON>j započetih predmeta", "ideHelp": "Pritisnite Ctrl + razmaknica u uređivaču da biste vidjeli prijedloge, pritisnite ponovo za detaljniju pomoć. Pritisnite F1 da vidite sve dostupne naredbe i tipkovničke prečace. Za više pogledajte <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>dokumentaciju uređivača</a>.", "restHelp": "Unesite URL za jednu od usluga TAS tablice (npr. '/tasks/mine') i nakon učitavanja usluge odaberite stupce tablice koje želite prikazati u spremniku.", "defaultGraphName": "Zadani naziv grafikona", "graphName": "<PERSON><PERSON> grafi<PERSON>a", "ttStatistics": {"heading": "Statistika", "body": ""}, "defaultAxisXName": "Zadani naziv X osi", "defaultAxisYName": "Zadani naziv Y osi", "defaultFilterName": "Zadani naziv filtra", "filterName": "Naziv filtra", "defaultOptionName": "Zadani naziv opcije", "optionName": "Naziv opcije", "defaultOverviewName": "Zadani naziv pregleda", "overviewName": "<PERSON><PERSON> pre<PERSON>", "eventName": "<PERSON><PERSON>", "wantToOverrideEs6": "<PERSON><PERSON> prepisati, nap<PERSON>š<PERSON> <b>ES6</b>", "processArchivation": "arhi<PERSON><PERSON><PERSON> procesa", "processUnarchivation": "dearhivir<PERSON><PERSON> procesa", "resendEmail": "Proslijedi e-poštu", "alrFailedSendEmail": "Slanje e-pošte nije us<PERSON>lo", "ttResendEmail": {"heading": "Proslijedi e-poštu", "body": "Ponovno šalje prethodno poslanu obavijest putem e-pošte. Primatelji se mogu mijenjati ili dodavati."}, "addCurrentScreenToFavourite": "Dodajte trenutni zaslon u svoje favorite", "attachmentAdd": "Dodajte dokument", "createNewCase": "Stvaranje novog slučaja", "moreLanguage": "Ostale varijante jezika", "notesAdd": "Dodaj <PERSON>", "notesNew": "Nova bilješka", "removeCurrentScreenFromFavourite": "Uklonite trenutni zaslon s omiljenog", "setDashboard": "Uredite nadzornu ploču", "chooseFromCases": "Odaberite iz slučajeva", "folders": "Mape", "newFolderBtn": "Nova mapa", "documentInfo": "Informacije o dokumentu", "userInfo": "Informacije o korisniku", "deleteImage": "Izbriši sliku", "profilePhoto": "Fotografija profila", "profilePhotoCaption": "Koristite fotografiju u jpeg, jpg, png ili gif formatu.", "updatePhoto": "Ažuriraj fotografiju", "mailNotifications": "Obavijesti putem e-pošte", "userPreferences": "Korisničke preferencije", "userSettings": "Korisničke postavke", "allVices": "Sve zamjene", "createVice": "St<PERSON><PERSON> z<PERSON>jenu", "editVice": "<PERSON><PERSON><PERSON>", "viceTip": "Zamjena vam omogućuje da proslijedite svoj dnevni red kolegi", "emptyDataMessage": "<PERSON><PERSON> više ništa", "addFirstNote": "Dodaj prvu bilješku", "noResultsFor": "Nema rezultata za:", "noCurrentTasks": "<PERSON><PERSON> trenut<PERSON> zada<PERSON>", "checkYourSearch": "Provjerite svoju pretragu i pokušajte ponovo.", "noFavOverviews": "<PERSON><PERSON><PERSON>", "favOverviewsTip": "Dodaj<PERSON> pregled svojim favoritima sa zvjezdicom", "noHiddenOverviews": "<PERSON><PERSON><PERSON>a", "addOverview": "<PERSON><PERSON><PERSON> pre<PERSON>", "hidden": "Skriveno", "removeConfirm": "Ukloni", "removeItem": "Jeste li sigurni da želite ukloniti {{variable}}?", "changePicture": "Promijeni s<PERSON>", "saveFilter": "<PERSON><PERSON><PERSON><PERSON> filtar", "addAnotherVice": "<PERSON><PERSON><PERSON><PERSON>", "saveVice": "Štedite zamjenu", "firstLastName": "<PERSON><PERSON> i prezime", "taskInfo": "Informacije o zadatku", "emptyFavsTip": "Dodajte favorite pomo<PERSON>u gumba", "saveAndClose": "Spremi i zatvori", "usersCanEditOverview": "Korisnici mogu uređivati ​​pregled", "assignedUsers": "Dodijeljeni korisnici", "assignedOrgUnits": "Dodijeljene organizacijske jedinice", "assignedRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "otherLangVariants": "Druge jezične varijante", "moveToSharing": "Premjesti u dijeljenje", "insertDocumentsPerm": "Korisnik ima dopuštenje za umetanje dokumenata i bilješki", "saveNewPassword": "Spremi novu lozinku", "confirmSubscription": "Potvrdi Pretplatu", "subscriptionCaption": "<PERSON><PERSON><PERSON><PERSON> pregled će vam stići putem e-pošte u postavljenom vremenu.", "by": "Prema", "frequency": "Frekvencija", "termination": "Prekidanje", "ofTheMonth": "<PERSON> m<PERSON>", "endOnDate": "Završi na datum", "endAfter": "<PERSON><PERSON><PERSON><PERSON><PERSON>on", "onlyOnWorkingDays": "<PERSON><PERSON> radnim danima", "occurrences": "pojave", "dayOfWeekBy": "Dana u tjednu prema", "calendarDayBy": "Kalendarskog dana prema", "dateBy": "datuma", "byDate": "<PERSON><PERSON> datumu", "byOccurrenceCount": "Prema broju pojava", "infinitely": "Beskrajno", "dayOfMonthAdornment": ". dan u mje<PERSON>cu", "ordinalAdornment": ".", "toDateBeforeFromError": "Datum 'Do' ne može biti prije datuma 'Od'", "vice": "<PERSON><PERSON><PERSON><PERSON>", "previewShown": "<PERSON><PERSON> je prikazan", "duplicate": "Duplikat", "hideBtn": "<PERSON><PERSON><PERSON><PERSON>", "userView": "Prikaz korisnika", "adminView": "Prikaz administratora", "or": "<PERSON><PERSON>", "overlappingVicesError": "Zamjene se ne mogu preklapati", "fileVar": "dato<PERSON><PERSON>", "nodeVar": "<PERSON><PERSON>", "uploadDifferentFile": "Uč<PERSON><PERSON>u datoteku", "uploadedFile": "Uč<PERSON>na <PERSON>", "refreshPage2": "Osvježi stranicu", "refreshPageCaption": "Molimo osvježite stranicu u svom pregledniku za nastavak.", "ttCopy": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućuje vam kopiranje odabrane stavke uz mogućnost uređivanja nekih parametara."}, "alrError_INVALID_CSV_MAPPING": "Nije pronađen CSV stupac '%s' u mapiranju događaja. Obratite se administratoru prijave.", "documentPreview": "Pregled dokumenta", "moveUp": "Pomicanje gore", "moveDown": "Pomicanje dolje", "moveToFilter": "Premjesti u filtar", "moveToSorting": "Premjesti na sortiranje", "addSorting": "<PERSON><PERSON><PERSON>", "cancelFilters": "Poništi filtere", "docUploadedImmediately": "Dokument će biti odmah učitan", "moreOptions": "Više opcija", "docSearchPlaceholder": "Npr. faktura.pdf...", "tasksSearchPlaceholder": "Npr. Unesite novu fakturu...", "docUploadedImmediatelyPrivate": "Dokument će se odmah učitati kao privatni", "takeTsk": "Preuzmi zadatak", "tasksActive": "Aktivni zadaci", "subprocesses": "Sub-procesi", "cancelAuthorization": "Poništi autorizaciju", "cancelAuthorizationConfirm": "Jeste li sigurni da želite otkazati autorizaciju uređaja?", "linkMobileApp": "Poveži mobilnu aplikaciju", "mobileApp": "Mobilna aplikacija", "scanThisQr": "Skenirajte ovaj QR kod svojim mobilnim uređajem.", "scanningQr": "Skeniranje QR koda. Pričekajte.", "deviceName": "<PERSON><PERSON>", "newDeviceName": "Novi naziv uređaja", "registrationDate": "Datum registracije", "lastLogin": "Posljednja prijava", "mobileNotifications": "<PERSON><PERSON><PERSON>", "disableMobileNotification": "Isključivanje obavijesti na mobitelu", "newQrCode": "Novi QR kod", "inactiveScanQr": "Neaktivan - skenirajte QR kod.", "enableNotifications": "Omogući obavijesti", "tip": "Savjet: {{message}}", "alrFavContainerAlreadyExists": "Spremnik favorita već postoji.", "addGraph": "<PERSON><PERSON><PERSON>", "newRow": "Nova redak", "confirmSetDefaultDashboard": "Želite li zaista postaviti trenutnu nadzornu ploču kao zadanu postavku za sve korisnike?", "changeMayAffectAllUsers": "Ova promjena može utjecati na sve korisnike.", "noOverviewsTip": "Stvorite novi pregled pomo<PERSON>u gumba \"Dodaj pregled\"", "removeFromHidden": "Ukloni iz skrivenih", "last7Days": "Zadnjih 7 dana", "last14Days": "Zadnjih 14 dana", "last30Days": "Zadnjih 30 dana", "lastCalendarMonth": "Zadnji kalendar mjesec", "lastQuarter": "Zadnje trom<PERSON>", "last12Months": "Zadnjih 12 mjeseci", "lastCalendarYear": "Zadnja kalendarska godina", "noFilterSet": "<PERSON><PERSON> filter", "noSortingSet": "<PERSON><PERSON> nač<PERSON>", "deleteGroup": "Izbriši grupu", "newGroup": "Nova grupa", "operators": "Operatori", "withActiveTask": "S aktivnim zadatkom", "withoutActiveTask": "Bez aktivnog zadatka", "withNoTerm": "Bez roka", "withTerm": "S rokom", "securityAndAuthentication": "Sigurnost i autentifikacija", "dataIntegrationAndManagement": "Integracija i upravljanje podacima", "appManagementAndConfig": "Upravljanje i konfiguracija aplikacije", "monitoringAndMaintenance": "Praćenje i održavanje", "adminSearchPlaceholder": "<PERSON>, <PERSON><PERSON><PERSON>...", "authenticationAdminDescription": "Opcije prijave korisnika", "certificatesAdminDescription": "Certifikati za TAS", "elasticsearchAdminDescription": "Integracija s Elasticsearch", "xmlProcessImportAdminDescription": "Uvezi XML procese koristeći cron unos XMLProcessImport.js", "structureImportExportAdminDescription": "Uvoz/izvoz organizacijske strukture, korisnika i uloga", "dmsAttributesAdminDescription": "Popis atributa dokumenta u DMS-u", "dynTablesAdminDescription": "Pohrana podataka u dinamičkim tablicama", "csvAdminDescription": "Manipulacija CSV datotekama u aplikaciji", "configurationAdminDescription": "Konfiguracija aplikacije", "settingsAdminDescription": "Postavke identifikacije tvrtke i drugi administrativni zadaci", "logsAdminDescription": "Upravljanje i pregled zapisnika aplikacija", "migrationsAdminDescription": "Migracija podataka i konfiguracija aplikacije", "guidesAdminDescription": "Pomoć i vodiči za korisnike", "schemeAdminDescription": "<PERSON><PERSON>, logo i ostali elementi u aplikaciji", "sequencesAdminDescription": "Upravljanje sekvencama koje se koriste u predlošcima", "serviceConsoleAdminDescription": "Administrativne naredbe aplikacije putem servisne konzole", "serviceOperationsAdminDescription": "Složeno upravljanje operacijama usluga", "scriptsAdminDescription": "Upravljanje skriptama za višekratnu upotrebu u različitim predlošcima", "appStatusAdminDescription": "Informacije o trenutnom statusu aplikacije", "usageStatsAdminDescription": "Pogledajte statistiku korištenja aplikacije", "maintenanceAdminDescription": "Postavke održavanja i izvršavanje zadataka održavanja", "scheduledTasksAdminDescription": "Upravljaj svim planiranim zadacima", "publicFilesAdminDescription": "Upravljanje javnim datotekama i dokumentacijom", "cronsAdminDescription": "Automatiziranje redovnih zadataka", "hrAgendaAdminDescription": "Upravljanje korisničkim rasporedom unutar HR-a", "emailsQueueAdminDescription": "Upravljanje redom e-pošte i sva komunikacija e-poštom od TAS-a", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Dodavanje stavke u favorite nije us<PERSON>lo", "alrRemoveFavItemFailed": "Uklanjanje stavke iz favorita nije uspjelo", "alrAddHiddenItemFailed": "<PERSON><PERSON> us<PERSON>lo do<PERSON>vanje skrivene stavke", "alrRemoveHiddenItemFailed": "Uklanjanje skrivene stavke nije uspjelo", "display": "Prikaz", "compact": "Kompaktno", "standard": "Standardno", "comfortable": "Udobno", "exportTo": "Izvezi u", "adminMenuTip": "Dodajte svoje stavke u administraciju u omiljene. Klikom na zvjezdicu prikazat će se stavka baš ovdje.", "editorDocumentation": "Dokumentacija urednika", "addSection": "<PERSON><PERSON><PERSON>", "insertSection": "Umetni odjeljak", "section": "Od<PERSON><PERSON>jak", "sections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toTop": "Na početak", "toEnd": "Na kraj", "alrSectionNotBeEmpty": "Odjeljak ne smije biti prazan", "confirmDeleteSection": "Želite li zaista izbrisati odjeljak?", "sectionVarsMoveAllTasks": "Varijable u svim zadacima bit će premještene iz uklonjenog odjeljka u varijable bez odjeljka.", "sectionVarsMove": "Varijable će biti premještene iz uklonjenog dijela u varijable bez odjeljka.", "actionCannotUndone": "Ova se radnja ne može poništiti.", "overviewOfAllNews": "Pregled svih vijesti", "copyOverview": "<PERSON><PERSON><PERSON> pre<PERSON>", "create": "<PERSON><PERSON><PERSON>", "copyExistingOverview": "<PERSON><PERSON><PERSON> pre<PERSON>", "selectOverview": "<PERSON><PERSON><PERSON><PERSON> pregled", "chooseFromOverviews": "Odaberite iz pregleda...", "selectTemplate": "<PERSON><PERSON><PERSON>", "chooseFromAvailableTemplates": "Odaberite neki od dostupnih predložaka...", "loginWithUsernamePassword": "Prijava s korisničkim imenom i lozinkom", "signInWithCorporateIdentity": "Prijavite se s korporativnim identitetom", "whatsNewInTAS": "Što je novo u TAS-u?", "whatsNewInTASDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nove značajke, savjeti, trikovi i sve što trebate znati.", "justOpen": "Samo otvoreno", "editOverview": "<PERSON><PERSON><PERSON> pregled", "noGraphsTip": "Stvorite novi grafikon pomoću gumba \"Dodaj grafikon\"", "noDocumentsTip": "Dodajte dokument zadatku ili pomoću gumba \"Dodaj\"", "noFilesTip": "Dodajte novu datoteku pomoću gumba \"Dodaj\"", "less": "<PERSON><PERSON>", "notContains": "<PERSON>e sadrži", "factorySettings": "Tvorničke postavke", "previewCollapsedNavMenu": "Pregled pakiranog navigacijskog izbornika", "previewExpandedNavMenu": "Pregled nepakiranog navigacijskog izbornika", "logoForCollapsedNavMenu": "Logotip za pakirani navigacijski izbornik", "logoForExpandedNavMenu": "Logotip za raspakirani navigacijski izbornik", "organisationLogo": "Logotip organizacije", "pickLogoOrganisation": "Odabir logotipa za organizaciju", "addLogo": "Dodajte logotip", "clickForAddLogoOrDrop": "Kliknite za dodavanje datoteke logo ili pada ovdje", "useLogoSizeMin": "Koristite logotip minimalne velič<PERSON>", "logoForLightTheme": "Logo za svijetli način rada", "logoForDarkTheme": "Logo za tamni način rada", "notEquals": "<PERSON><PERSON> jednako", "sharedWithMe": "Podijeljeno sa mnom", "myOverview": "<PERSON><PERSON> pre<PERSON>", "getMobileAppText": "Preuzmite mobilnu aplikaciju iz trgovine aplikacija", "noDocuments": "<PERSON><PERSON>", "noNotes": "<PERSON><PERSON>", "noFiles": "<PERSON><PERSON>", "addFirstDocument": "Dodaj prvi dokument", "killed": "Ubijen", "chooseNewLogo": "Odaberite novi logotip", "function": "Funkcija", "groupFunction": "Funkcija između grupa", "mobileAppAuthFailed": "Provjera autentičnosti mobilne aplikacije nije us<PERSON>la.", "currentDocumentVersion": "Trenutačna verzija dokumenta", "csp": "Tartalombiz<PERSON><PERSON><PERSON>", "documentsDelete": "Izbriši dokumente", "confirmDocumentsDelete": "Jeste li sigurni da želite izbrisati odabrane dokumente?", "confirmDocumentsDownload": "Želite li preuzeti odabrane dokumente?", "firstNum": "prvih {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Preuzmi dokumente", "caseLogs": "Dnevnici slučajeva", "archiveCases": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unarchive": "<PERSON><PERSON><PERSON><PERSON>", "confirmArchiveCases": "Jeste li sigurni da želite arhivirati odabrane slučajeve?", "archiveInDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dane)", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archivedx": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrArchivingCase": "<PERSON><PERSON><PERSON><PERSON> se arhivira...", "alrCaseArchived": "<PERSON><PERSON><PERSON><PERSON> je arhivir<PERSON>.", "alrLackOfPermsToArchiveCase": "Nemate dovoljno dozvola za arhiviranje slučaja.", "alrArchiveCaseFailed": "Arhiviranje slučaja nije uspjelo.", "alrUnarchivingCase": "<PERSON><PERSON><PERSON><PERSON>...", "alrCaseUnarchived": "<PERSON><PERSON><PERSON><PERSON> je <PERSON>.", "alrLackOfPermsToUnarchiveCase": "Nemate dovoljno dozvola za dearhiviranje slučaja.", "alrUnarchiveCaseFailed": "Dearhivir<PERSON>je slučaja nije us<PERSON>.", "byUser": "Po korisniku", "byAgenda": "Po agendi", "agendaHandover": "Prijenos agende", "activeUsers": "Aktivni korisnici", "lockedUsers": "Zaključani korisnici", "allUsers": "<PERSON>vi k<PERSON>i", "inactiveUsers": "Neaktivni korisnici", "hrAgendaSearchPlaceholder": "Npr. <PERSON> ...", "completeAgendaHandover": "Dovrši primopredaju dnevnog reda", "handoverCases": "<PERSON><PERSON> za primopredaju", "handoverTasks": "Zadaci primopredaje", "handoverVars": "Varija<PERSON> primopredaje", "changeTaskOwner": "Promijeni rješavača zadataka", "confirmHandover": "Potvrdi primopredaju", "filterCasesByHeaderTip": "Možete filtrirati sve slučajeve pod istim zaglavljem u stupcu Zaglavlje.", "userAgendaSelectedHandover": "Prijenos <b style=\"color: {{color}};\">odabrane</b> korisničke agende", "userAgendaCompleteHandover": "Prijenos <b style=\"color: {{color}};\">potpune</b> korisničke agende", "confirmAgendaHandover": "Jeste li sigurni da želite predati odabranu agendu ({{selected}}) korisniku <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Jeste li sigurni da ž<PERSON>te predati <b>o<PERSON><PERSON><PERSON></b> agend<PERSON> koris<PERSON> <b>{{user}}</b> korisniku <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Jeste li sigurni da ž<PERSON>te predati <b>potpu<PERSON></b> agendu korisnik<PERSON> <b>{{user}}</b> korisniku <b>{{newUser}}</b>?", "refreshSessionTitle": "TAS sesija će biti prekinuta za {{minutes}} minuta.", "refreshSessionCaption": "<PERSON><PERSON><PERSON><PERSON> \"Nastavi s radom\" da nastavite bez prekida.", "continueWorking": "<PERSON><PERSON><PERSON> s radom", "sessionExpiredCaption": "Kliknite \"Prijavite se ponovo\" za povratak na ekran za prijavu.", "loginExpired": "<PERSON><PERSON><PERSON><PERSON> smo vas nakon dugog razdoblja neaktivnosti.", "confirmArchiveCase": "Jeste li sigurni da želite arhivirati odabrani slučaj?", "isLowerOrEqualThan": "<PERSON>ra biti manje od ili jednako", "confirmUnarchiveCase": "Jeste li sigurni da želite dearhivirati odabrani slučaj?", "addCaseRightNewUserTooltip": "<PERSON><PERSON> ne označite ovu opciju, novi korisnik će biti zamijenjen u poslovnoj varijabli, ali neće imati pristup slučaju.", "canBeViced": "Zamijenjen sam", "canVice": "Zamjenjujem", "backgroundColor": "<PERSON><PERSON>", "defaultDashboardView": "Pregled zadane nadzorne ploče", "colorScheme": "<PERSON><PERSON> boja", "displaySelectionAsTags": "<PERSON><PERSON><PERSON><PERSON> oda<PERSON> kao tagove", "displayAsPassword": "P<PERSON><PERSON><PERSON> kao lozinku", "sideBySide": "<PERSON><PERSON> pored drugoga", "copyAssignmentFromTask": "<PERSON><PERSON><PERSON>d<PERSON> iz zadatka", "toTask": "U zadatak", "copyTaskAssignmentWarning": "Dodjela u zadatku nije prazna, ž<PERSON><PERSON> li je prepisati?", "copyToOtherTasks": "Ko<PERSON>raj u druge zadatke", "noteScriptsNotApplied": "Napomena: skripte nisu primijen<PERSON>ne", "generateRecHistory": "Prikaži u aktivnim zadacima i povijesti", "leaveFormerRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "includeCompetences": "Uključi kompetencije", "copyRoles": "<PERSON><PERSON><PERSON>", "userIsActive": "Korisnik je aktivan", "systemUser": "Sistemski korisnik", "copyRolesFromUser": "<PERSON><PERSON><PERSON> od koris<PERSON>a", "assignedRolesOverview": "<PERSON><PERSON> do<PERSON> ul<PERSON>", "copyRolesInfo": "<PERSON>ko je navedeni korisnik dio kompetencija, ove kompetencije neće biti odmah kopirane. Generiranje će se dogoditi:", "notificationOn": "Prič<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationOff": "Gašenje", "onNotification": "Obavijest", "offNotification": "Obavijest", "page": "stranica", "fromTo": "Od - do", "isAnyOfValue": "Je bilo koja vrijednost od", "notcontains": "ne <PERSON><PERSON><PERSON>i", "notequals": "<PERSON><PERSON><PERSON>", "fromto": "od - do", "isanyofvalue": "je bilo koja vrijednost od", "alrNoteToggleVisibiltyFailed": "Skrivanje/otkrivanje bilješke nije us<PERSON>lo", "alrNoteHideOnEditFailed": "Skrivanje izvorne bilješke nije uspjelo", "hiddenShe": "Skrivena", "showHiddenNotes": "Prikaži skrivene bilješke", "alrNoteEdited": "Uređena verzija bilješke je spremljena", "notesEdit": "Uredi bilješku", "displayName": "Prikazano ime", "clientDateFormat": "Format datuma", "defaultByLanguage": "Zadano po jeziku", "restKeysOptionsNotUpToDate": "Zastarjeli odabir vrijednosti - ponovno učitajte uslugu.", "invalidValue": "Nevažeća vrijednost", "ended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportAllActive": "Izvezi sve aktivne", "alrScriptsLoadFailed": "Učitavanje skripti nije us<PERSON>jelo.", "scriptsImport": "U<PERSON>z skripti", "doImport": "Uvoz", "alrImportingScripts": "Uvoz skripti u tijeku...", "alrScriptsImported": "Skripte su uvezene.", "alrScriptsImportFailed": "Uvoz skripti nije uspio.", "removeAll": "Ukloni sve", "alrNoScriptsToImport": "<PERSON>ema sk<PERSON>ti za uvoz.", "activateAll": "Aktiviraj sve", "alrNoPermsToEditNoteInVice": "Nemate dopuštenja za uređivanje bilješke kao zamjenik.", "alrNoPermsToToggleNoteVisibilityInVice": "Nemate dopuštenja za skrivanje/otkrivanje bilješke kao zamjenik.", "plusMore": "vi<PERSON>e", "variableAlignment": "Poravnan<PERSON> varijable", "variableAlignmentHelp": "Utječe na poravnanje vrijednosti varijable unutar obrasca zadatka.", "variableAlignmentLeft": "Lijevo", "variableAlignmentRight": "Des<PERSON>", "tasksMineAndToPull": "Moji + Za povlačenje", "myDevice": "<PERSON><PERSON>", "deleteLogo": "Izbriši logotip", "namingFilter": "Naziv filtra", "exceptionsToRegularSchedule": "Iznimke od redovitog rasporeda", "noExceptions": "<PERSON><PERSON>", "specificDates": "Specifični datumi", "dateFromTo": "Datum od - do", "weekdayCap": "Dan u tjednu", "specificDayBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dan", "yearsBy": "godina", "timed": "<PERSON><PERSON><PERSON><PERSON>", "firstDayOfMonth": "Prvi dan u mje<PERSON>cu", "lastDayOfMonth": "Posljednji dan u mjesecu", "firstDayOfYear": "Prvi dan u godini", "lastDayOfYear": "Posljednji dan u godini", "addDate": "<PERSON><PERSON><PERSON> datum", "newPlan": "Novi plan", "addAnother": "<PERSON><PERSON><PERSON> j<PERSON> j<PERSON>", "startTime": "Vrijeme početka", "endTime": "Vrijeme završetka", "inTimeFromTo": "u vremenu od {{from}} do {{to}}", "dayOfMonthBy": "<PERSON> m<PERSON>", "cWorkDays": "radnih dana", "cWeeks": "t<PERSON><PERSON>a", "cMonths": "m<PERSON><PERSON><PERSON>", "cYears": "godina", "everyWeek": "svaki tjedan", "everyYear": "svake godine", "inMonth": "u mjesecu", "everyDay": "svaki dan", "seqIdEdit": "Uredi ID sekvence", "allowMultiselectSearchRight": "Dopustite pretraživanje u zadatku", "doubleHeightForContent": "Dvostruka visina za sadržaj", "alrNoVariablesMappingToImport": "Nema mapiranja varijabli za uvoz.", "alrVariablesMappingImportLoadFailed": "Učitavanje mapiranja varijabli za uvoz nije uspjelo.", "variablesMappingImport": "Uvoz mapiranja varijabli", "useAllMappings": "<PERSON><PERSON><PERSON> s<PERSON>", "doExportVariablesMapping": "Izvoz mapiranja varijabli", "alrImportingVariablesMapping": "Uvoz mapiranja varijabli u tijeku...", "alrVariablesMappingImported": "Mapiranje varijabli je uvezeno.", "alrVariablesMappingImportFailed": "Uvoz mapiranja varijabli nije uspio.", "alrVariablesMappingImportedPartially": "Mapiranje varijabli je djelomično uvezeno. Neke varijable nisu pronađene.", "alrEditorHintsLoadFailed": "<PERSON><PERSON> uspjelo učitavanje savjeta uređivača.", "addTable": "<PERSON><PERSON><PERSON> tab<PERSON>", "confirmDynTablesDelete": "Želite li zaista izbrisati odabrane dinamičke tablice?", "dynTablesDelete": "Izbriši dinamičke tablice", "addRow": "<PERSON><PERSON><PERSON>", "preview": "Pregled", "columnDelete": "Izbriši stupac", "editRow": "<PERSON><PERSON><PERSON>", "addingNewColumn": "Dodavanje novog stupca", "addingNewRow": "Dodavanje novog retka", "columnsRename": "<PERSON>ime<PERSON><PERSON> s<PERSON>", "rowCellValues": "Vrijednosti ćelija u retku", "saveDynTableName": "Spremi naziv dinamičke tablice", "saveDynTableNameQ": "Spremi naziv dinamičke tablice?", "saveDynTableNameWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON>, provjerite da promjena naziva tablice neće utjecati na postojeće izračune u predlošcima.", "rowMove": "Premjesti red", "alrCsvParsingErr": "Pogreška pri analizi CSV-a!", "addFirstTableColumn": "Dodaj prvi stupac tablice", "my": "<PERSON><PERSON>", "license": "Licenca", "licenses": "Licence", "addLicense": "<PERSON><PERSON><PERSON> lice<PERSON>", "licenseResult": "Rezultat licence", "alrLicenceResultLoadingFailed": "Nije uspjelo učitavanje rezultata licence.", "licensesAdminDescription": "Upravljanje licencom", "uploadByDragging": "Učitaj datoteku povlačenjem.", "uploadByDraggingAnywhere": "Učitajte datoteku povlačenjem bilo gdje u prostoru.", "assignVariable": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteSectionName": "<PERSON><PERSON> li sigurni da želite izbrisati odjeljak <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Upozorenje: od<PERSON><PERSON>jak će biti izbrisan za sve pogođene zadatke, uključujući varijable.", "tasksAffected": "Zahvaćeni z<PERSON>", "varSearchPlaceholder": "Npr. faktur<PERSON>...", "enlarge": "<PERSON><PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON><PERSON>", "shrink": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>", "doValidate": "Potvrdi", "phoneNumber": "Telefonski broj", "textLength": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>ta", "when": "kada", "to2": "do", "that": "da", "dynCondBuilderBlockFunctionDescShow": "Prikazuje varijablu ako je uvjet ispunjen.", "dynCondBuilderBlockFunctionDescHide": "Skriva varijablu ako je uvjet ispunjen.", "dynCondBuilderBlockFunctionDescChange": "Mijenja vrijednost varijable ako je uvjet ispunjen.", "dynCondBuilderBlockFunctionDescValidate": "Potvrđuje vrijednost varijable.", "addCondition": "Do<PERSON>j uvjet", "operator": "operator", "equals": "j<PERSON><PERSON><PERSON>", "greaterthan": "veće od", "greaterorequal": "ve<PERSON>e ili jednako", "lessthan": "manje od", "lessorequal": "manje ili jednako", "demoCode": "Demo kod", "code": "Kod", "confirmDeleteConditions": "Želite li zaista izbrisati sve uvjete (uključujući skriptu)?", "validationErrorMessage": "Poruka o pogrešci validacije", "alrScriptToStructuredBlockConversionFailed": "Pretvorba skripte u strukturirani blok nije uspjela.", "alrStructuredBlockToScriptConversionFailed": "Pretvorba strukturiranog bloka u skriptu nije uspjela.", "alrScriptToBuilderConversionFailed": "Pretvorba skripte u builder nije uspjela.", "alrBuilderToScriptConversionFailed": "Pretvorba iz buildera u skriptu nije uspjela.", "dynCondBuilderBlockFunctionDescScript": "Blok skripte dinamičkih uvjeta.", "convertToStructuredBlock": "Pretvori u strukturirani blok", "convertToScript": "Pretvori u skriptu", "dynCondBuilderBlockWatchVarsLabel": "<PERSON><PERSON><PERSON> (watchVars)", "variables": "Varijable", "copyToOthers": "<PERSON><PERSON><PERSON>", "sectionName": "<PERSON><PERSON> od<PERSON>a", "newSectionName": "<PERSON>me novog od<PERSON>a", "testIt": "Test", "addAdjacentSection": "Dodajte susjedni odjeljak", "addAdjacentSectionBelow": "Dodajte susjedni odjeljak ispod", "selectExistingSection": "Odaberite postojeći odjeljak", "renameSectionWarning": "Upozorenje: Odjeljak će biti preimenovan u sve zadatke predloška.", "warning2": "Upozorenje", "copyAssignmentToTask": "Kopirajte zadatak na zadatak", "copyAlsoConditions": "Kopiranje i uvjeti", "copyAssignmentToTaskWarning": "Upozorenje: Dodjela i eventualno dinamični uvjeti u odabranom zadatku bit će prepisani.", "importFromOtherTask": "Uvoz iz drugog zadatka", "startFromScratch": "Počnite od početka", "howToStartAssignments": "<PERSON><PERSON>te početi dodijeliti varijable?", "selectTaskToImport": "Odaberite zadatak za uvoz", "confirm": "Potvrditi", "selectTaskToTest": "Za odabir zadatka za testiranje", "toTestSaveChanges": "Promjene treba pohraniti za testiranje.", "variableAssignmentTest": "Ispitivanje varija<PERSON>", "viewAsMobile": "Pogledajte kao na mobilnom uređaju", "viewAsPc": "Pogledajte kao na PC -u", "emptySpace": "<PERSON><PERSON><PERSON>", "variableAssignments": "Dodjeljivanje <PERSON>", "allowCompletionOnChangeOf": "Dopusti završetak pri promjeni", "dynCondBuilderBlockFunctionDescRead": "Mijenja način rada varijable na \"samo za čitanje\" ako je uvjet ispunjen.", "dynCondBuilderBlockFunctionDescWrite": "Mijenja način rada varijable na \"za čitanje i pisanje\" ako je uvjet ispunjen.", "dynCondBuilderBlockFunctionDescMust": "Mijenja način rada varijable na \"obavezno\" ako je uvjet ispunjen.", "dynCondBuilderBlockFunctionDescSolve": "Omogućuje završetak zadatka pri promjeni određene varijable ako je uvjet ispunjen.", "newsManagement": "Upravljanje vijestima", "newsManagementAdminDescription": "Upravljanje vijestima u aplikaciji", "addNewsPost": "<PERSON><PERSON><PERSON> vije<PERSON>", "newPost": "Novi post", "news": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basicInfo": "Osnovne informacije", "publicationPlanning": "Planiranje objave", "displayToUsers": "Prikaz korisnicima", "displayLocation": "<PERSON><PERSON><PERSON>", "newsPostContent": "Sadrž<PERSON> vijesti", "postTitle": "Na<PERSON>lov objave", "newsManagementPostDetailPhoneNumberTooltip": "Telefonski broj za prikaz u detaljima vijesti", "newsManagementPostDetailEmailTooltip": "E-mail za prikaz u detaljima vijesti", "customUrlLink": "Prilagođeni URL link", "newsManagementPostDetailCustomUrlLinkTooltip": "Prilagođeni URL link za prikaz u detaljima vijesti", "stateAfterSaving": "Status nakon spremanja", "newsPostStateActive": "Aktivno", "newsPostStateInactive": "Neaktivno", "newsPostStatePlanned": "Planirano", "endNewsPostOnSpecificDate": "Završi vijest na određeni datum", "sendNewsPostViaEmail": "Pošalji vijest putem e-pošte", "priorityNewsPost": "Prioritetna vijest", "newsManagementPostDetailPriorityNewsTooltip": "<PERSON> primjer, za obavijest o održavanju ili promjeni radnog postupka", "newsPostEndDate": "Datum završetka vijesti", "pickNewsPostDisplayToOrgUnits": "Kojim org. jedinicama prikazati vijest?", "pickNewsPostDisplayToRoles": "<PERSON><PERSON><PERSON> ulogama prikazati vijest?", "pickNewsPostDisplayToUsers": "<PERSON>jim korisnicima prikazati vijest?", "pickNewsPostDisplayOnTemplate": "Na kojem predlošku prikazati vijest?", "pickNewsPostDisplayOnHeaders": "Na kojim zaglavljima prikazati vijest?", "pickNewsPostDisplayOnTasks": "Na kojim zadacima prikazati vijest?", "pickNewsPostDisplaySubOptionsHelperText": "Prvo odaberite predložak na kojem želite prikazati vijest.", "newsTagsManagement": "Upravljanje <PERSON> vijesti", "newsTagsManagementAdminDescription": "Upravljanje oznakama vijesti u aplikaciji", "addTag": "<PERSON><PERSON><PERSON>", "tags": "Oznake", "publicationDate": "<PERSON><PERSON> objave", "contacts": "Kontakti", "avaibleUntil": "Dostupno do", "published": "Objavljeno", "newsSinceLastVisitAmount": "Ukupno {{amount}} vijesti od posljednjeg posjeta", "noNews": "<PERSON><PERSON> vijesti", "createNewTag": "Stvorite novu oznaku", "tagName": "<PERSON>v oznake", "alrTagSaved": "Oznaka je spremljena.", "alrTagSaveFailed": "Spremanje oznake nije us<PERSON>.", "confirmDeleteTag": "Želite li zaista izbrisati oznaku \"{{tagName}}\"?", "alrPostSaved": "<PERSON><PERSON><PERSON><PERSON> je spremljena.", "alrPostSaveFailed": "Spremanje objave nije us<PERSON>jelo.", "alrLoadingTagsFailed": "Učitavanje oznaka nije us<PERSON>lo.", "confirmDeletePost": "Ž<PERSON>te li zaista izbrisati objavu \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "Želite li zaista izbrisati odabrane objave?", "post": "<PERSON><PERSON><PERSON><PERSON>", "alrPostLoadFailed": "Učitavanje objave nije us<PERSON>lo.", "alrTagDeleted": "Oznaka je izbrisana.", "alrTagDeleteFailed": "<PERSON><PERSON><PERSON>je oz<PERSON>ke nije us<PERSON>.", "alrPostDeleted": "Objava je izbrisana.", "alrPostDeleteFailed": "Brisanje objave nije us<PERSON>.", "alrPostsDeleted": "Odabrane objave su izbrisane.", "alrPostsDeleteFailed": "B<PERSON><PERSON>je odabranih objava nije us<PERSON>.", "alrTempTasksLoadFailed": "Učitavanje zadataka predloška nije uspjelo.", "rolesRestriction": "Ograničenje ul<PERSON>", "usersRestriction": "Ograničenje korisnika", "orgUnitsRestriction": "Ograničenje org. jedinica", "alrPriorityNewsLoadFailed": "Učitavanje prioritetnih vijesti nije uspjelo.", "moreInfo": "Više informacija", "tas5Info": "TAS 5.0 je ovdje ...", "totalNewsAmount": "U<PERSON><PERSON><PERSON> {{amount}} vijesti", "alrNewsContainerPostsLoadFailed": "Učitavanje objava za spremnik vijesti nije uspjelo.", "alrTaskNewsLoadFailed": "Učitavanje vijesti za zadatak nije uspjelo.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "Datum objave mora biti prije datuma završetka vijesti.", "alrNotificationsNewsLoadFailed": "Učitavanje vijesti za obavijesti nije uspjelo.", "moreNews": "Više vijesti", "newsManagementPostDetailConfirmSavingWillSendMail": "Spremanje objave uzrokovat će slanje e-pošte svim korisnicima kojima je objava namijenjena. Želite li zaista spremiti objavu?", "mailNewsNotification": "E-mail obavijest o vijestima", "mailNewsNotificationInfo": "Korisnik kontinuirano prima vijesti koje su mu namijenjene.", "alrRefreshingConfig": "Osvežavanje konfiguracije...", "alrConfigRefreshed": "Konfiguracija je uspješno osvežena.", "alrConfigRefreshFailed": "Osvežavanje konfiguracije nije uspjelo.", "ttRefreshConfig": {"heading": "Vrati konfiguraciju iz svih izvora", "body": ""}, "getMobileAppTextQr": "Preuzmite mobilnu aplikaciju iz trgovine aplikacija ili skenirajte QR kod", "dateStart": "<PERSON>tum <PERSON>", "dateEnd": "<PERSON><PERSON>", "tas_forms_generated": "Broj automatski generiranih obrazaca"}