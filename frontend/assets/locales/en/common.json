{"1st": "1st", "2nd": "2nd", "3rd": "3rd", "4th": "4th", "AddToAllTasks": "Add to all tasks", "OfVariable": "Of the variable", "RemoveFromAllTasks": "Remove from all tasks", "TaskOwnerWhichInVar": "To task owner, who is set in the variable", "action": "Action", "active": "Active", "activeShe": "Active", "activePl": "Active", "activity": "Activity", "activityType": "Activity type", "actualEnd": "Actual time of end", "actualSolver": "Actual task owner", "actualStart": "Actual time of start", "actualTsks": "Current tasks", "actualize": "refresh", "add": "Add", "addAttribute": "Add attribute", "addOrgUnit": "Add org. unit", "addPlan": "Add plan", "addPrintTemplate": "Add print template", "addRole": "Add role", "addRule": "Add rule", "addTemp": "Add template", "addTsk": "Add task", "addUser": "Add user", "addUserSomething": "Assign user to selected {{variable}}:", "addVariable": "Add variable", "after": "After", "afterTermTasks": "My tasks past due date", "all": "All", "allFiles": "All files", "allMustBeMet": "All must be met", "allMyTasks": "All my tasks", "allSubOfPlanGuar": "All subordinates of plan guarantor", "allTasksWithNoTerm": "All tasks with no due date", "allTasksWithTerm": "All tasks with due date", "allTemps": "All templates", "allowMultiple": "Allow choice of multiple options", "allowSelectAll": "Allow select all options", "allsupOfPlanGuar": "All superiors of plan guarantor", "alrBlockingAction": "There is an action to complete! Please wait a moment...", "alrActionNameNotDefined": "Action \"{{actionName}}\" is not defined.", "alrActionNotDefined": "Action has not been defined.", "alrApiUrlMissing": "Missing table data source.", "alrAssigningTsk": "Assigning task...", "alrAssignmentFailed": "Assignment saving failed", "alrAtrFailed": "Attribute deletion failed", "alrAttachDeleteFailed": "Document deletion failed", "alrAttachDeleted": "Document was deleted!", "alrAttachDeleting": "Deleting document...", "alrAttachDownloadFailed": "Document download failed.", "alrAttachDownloaded": "Document has been downloaded!", "alrAttachDownloading": "Downloading document...", "alrAttachMetaFailed": "Saving document metadata failed.", "alrAttachOrNotesCountFailed": "Documents or notes counting failed.", "alrAttachSaveFailed": "Document uploading failed.", "alrAttachSaved": "The document was attached.", "alrAttachTooBig": "The document is too big! The document exceeded {{maxUploadSize}} MB.", "alrAttrDataFailed": "Loading attribute data failed", "alrAttrFailed": "Loading attribute data failed", "alrAttrSaveFailed": "Error while saving attribute!", "alrAttrsLoadFailed": "Attributes loading failed", "alrAttachRestored": "Document restored.", "alrAttachRestoreFailed": "Restoring document failed.", "alrAttachRestoring": "Restoring...", "alrAuthMethodsFailed": "Authentication methods load failed.", "alrBadLogin": "Incorrect user name or password.", "alrBlockedPopups": "Probably your pop-up windows are blocked.", "alrCaseDataLoadFailed": "Case data loading failed.", "alrCaseDeleteFailed": "Case deletion failed.", "alrCaseDeleted": "Case was deleted!", "alrCaseDeleting": "Deleting case...", "alrCaseNameLoadFailed": "Case name loading failed.", "alrCaseNoRights": "You do not have permission to view the case nr. {{id}}.", "alrCaseNotFound": "Case not found – it might have been deleted", "alrCaseOverviewFailed": "Loading case data for reading (CASE OVERVIEW) failed.", "alrCaseSuspended": "The case was suspended!", "alrCaseVarsLoadFailed": "Loading of process variables failed.", "alrCaseWakeUpFailed": "Case waking-up failed.", "alrCaseWakedUp": "<PERSON> has been woken-up!", "alrCaseWakingUp": "Waking-up case...", "alrColsWidthsSettingsFailed": "Saving of columns width setting failed.", "alrConnToServerFailed": "Connection to server failed.", "alrConnectionDataLoadFailed": "Connection data load failed.", "alrConnectionDeleteFailed": "Connection deletion failed.", "alrConnectionDeleted": "Connection has been deleted!", "alrConnectionSaveFailed": "Connection saving failed!", "alrContainerNotFound": "Container wasn't found.", "alrCsvDownloaded": "CSV file has been downloaded!", "alrCvNotFound": "The overview was not found.", "alrDashboardSettingsFailed": "Saving dashboard settings failed.", "alrDefaultDashboardLoadFailed": "Default dashboard load failed.", "alrDefaultDashboardSaved": "Default dashboard was saved!", "alrDeleteFailed": "An error during delete occurred.", "alrDeleted": "Deleted!", "alrDeleting": "Deleting....", "alrDiagramDataLoadFailed": "Data load to build the diagram failed.", "alrDiagramEditToSave": "The diagram cannot be saved and transformed to template – it contains more than one process! Please update the diagram so that it contains one process only or import a different .bpmn file.", "alrDiagramInitFailed": "Diagram initialization failed.", "alrDiagramMissingTaskName": "Please fill in the names of all tasks.", "alrDiagramErrors": "The diagram contains errors. Please fix them and try saving again.", "alrDiagramNotValid": "XML is not valid according to official specification BPMN 2.0!", "alrDiagramProcessCount": "The diagram cannot be saved and transformed to a template – it contains more than one process!", "alrDiagramSaveFailed": "There was an error while saving the template!", "alrDiagramTsksDeleteFailed": "There was an error while deleting the tasks!", "alrDiagramUnchanged": "The template remains unchanged.", "alrDmsColsLoadFailed": "The columns for DMS could not be loaded.", "alrDocsColumnsIdsFailed": "The id for documents table columns could not be loaded.", "alrDocumentAdding": "Saving the document...", "alrDynListsDataLoadFailed": "Dynamic lists data load failed.", "alrDynTableColsDataFailed": "Data to the dynamic table columns could not be loaded.", "alrDynTableDataLoadFailed": "Dynamic table data load failed.", "alrDynTableNotFound": "Dynamic table was not found.", "alrDynTablesDataLoadFailed": "Dynamic tables data load failed.", "alrCalcScriptsDataLoadFailed": "Global calculation scripts load failed.", "alrEditValues": "Please correct wrongly entered values.", "alrEventSaveFailed": "Saving the event failed.", "alrEventSaved": "Event was saved!", "alrEventSaving": "Saving event...", "alrEventTriggered": "Event was launched!", "alrExcelDownloaded": "The xlsx file was downloaded!", "alrExportCompleted": "Export completed.", "alrExportFailed": "Export was not successful.", "alrExportPreparing": "Preparing export...", "alrFailed": "Action failed.", "alrFailedCalendarTask": "Task load into the calendar has failed.", "alrFailedCreatePrint": "Print-out creation failed.", "alrFailedDLTotalCount": "There was no total_count specified at the dynamic list {{label}}, therefore all records have been loaded.", "alrFailedData": "Data load failed.", "alrFailedEventStart": "Event failed to start.", "alrFailedEventVariables": "Variables of the selected event failed to load.", "alrFailedEvents": "Events load failed.", "alrFailedFoldersData": "Folders data failed to load.", "alrFailedFormData": "Form data failed to load.", "alrFailedInitiatorName": "Initiator name failed to load.", "alrFailedLabelData": "Label component data failed to load.", "alrFailedLoad": "Print data failed to load.", "alrFailedLogicalType": "Logical Type failed to load.", "alrFailedMultiBoxData": "MultiBox data failed to load.", "alrFailedNewCase": "An error occurred during setting up a new case!", "alrFailedNewFolder": "Changing of folder name failed.", "alrFailedNoticeData": "Warning data load failed.", "alrFailedOrgUnitUser": "Organization units of the user failed to load.", "alrFailedOverviewData": "Overview failed to load.", "alrFailedPlanData": "Plan data failed to load.", "alrFailedPostData": "Sending of data failed.", "alrFailedPrintData": "Not possible to load printing data.", "alrFailedRevisionInfo": "New revision information failed to load.", "alrFailedSearchBoxData": "SearchBox data failed to load.", "alrFailedSelectBoxData": "SelectBox component data failed to load.", "alrFailedSuggestBoxData": "The suggester data failed to load.", "alrFailedTasColors": "TAS colors failed to load!", "alrFailedTaskHandOver": "Task handover failed.", "alrFailedTemplateProcesses": "Case templates failed to load.", "alrFailedVarData": "Variable data failed to load.", "alrFileAdded": "File was added!", "alrFileDeleteFailed": "Deleting of file failed.", "alrFileDonwload": "Downloading file...", "alrFileDownloaded": "File was downloaded!", "alrFileInfoFailed": "File information failed to load.", "alrFileMetaSaveFailed": "File metadata could not be saved.", "alrFileSavedLikeAttach": "The file was saved as a document.", "alrFileUploadFailed": "Loading of the file failed.", "alrFillAllRequired": "To complete the task, all required data have to be filled in!", "alrFillData": "To assign the task, all data have to be correctly filled in!", "alrFillDataInRightFormat": "Please fill in the data in the correct format.", "alrFillDataToCompleteTsk": "To complete the task, all data have to be correctly filled in!", "alrFillNameAndPass": "Please fill in name and password.", "alrFillNote": "Please fill in the note text.", "alrFillRequiredItems": "Please fill in the required items.", "alrFolderDataFailed": "Loading of folders data failed.", "alrFolderDataLoadFailed": "Loading of folder data failed.", "alrFolderFailed": "Loading of folder information failed.", "alrFolderSaveFailed": "The folder could not be saved!", "alrFoldersLoadFailed": "The folders could not be loaded.", "alrHelpSettingsSaveFailed": "Help settings could not be saved.", "alrHistoricalTskInfoFailed": "Historical task information load failed.", "alrHistoricalVarsSaveFailed": "Historical variables could not be saved.", "alrHistoricalVarsSaved": "Historical variables of the task have been saved.", "alrInvLogginHash": "Invalid login.", "alrJsonFailed": "Invalid JSON!", "alrLackOfPermsToEdit": "You have no editing permissions! The owner is", "alrLackOfPermsToSleepCase": "You do not have permission to suspend the case.", "alrLackOfPermsToWakeUpCase": "You do not have permission for waking-up the case.", "alrLastHistoricalTskIdFailed": "Id of the last historical task failed to load.", "alrLoadAttachmentsFailed": "Load of documents failed.", "alrLogOutFailed": "Logout failed.", "alrLoginExpired": "The login session has expired, please log in again.", "alrMappingFailed": "The mapping could not be saved.", "alrMappingTsksFailed": "Tasks mapping could not be loaded.", "alrNewCaseBased": "New case set up!", "alrNewFolder": "New folder has been created.", "alrNewFolderFailed": "Creating of new folder failed.", "alrNextTskOpened": "Following task has been opened.", "alrNoDataToPrint": "No data for printing have been found.", "alrNoteAdded": "The note has been added!", "alrNoteSaveFailed": "The note saving failed.", "alrNoteSaving": "Saving the note...", "alrNotesLoadFailed": "Case notes loading failed.", "alrOrgUnitDataFailed": "Organization unit data loading failed.", "alrOrgUnitDeleteFailed": "Organization unit deletion failed.", "alrOrgUnitDeleted": "Organization unit has been deleted!", "alrOrgUnitDeleting": "Deleting org. unit ...", "alrOrgUnitSaveFailed": "Organization unit saving failed.", "alrOrgUnitSaved": "Organization unit has been saved!", "alrOrgUnitSaving": "Saving organization unit...", "alrOverviewDataLoadFailed": "Overview data loading failed.", "alrOverviewSaveFailed": "The overview saving failed!", "alrOverviewSaveSameNameFailed": "The overview name is already being used by you or another user, please choose a different overview name.", "alrGraphSaveSameNameFailed": "The graph name is already being used by you or another user, please choose a different graph name.", "alrReportSaveSameNameFailed": "The report name is already being used by you or another user, please choose a different report name.", "alrOverviewsLoadFailed": "Overviews loading failed.", "alrPassSaveFailed": "Password saving failed.", "alrPassSaved": "Password has been saved!", "alrPlanReqItems": "To save the plan, fill in the required items.", "alrPlanSaveFailed": "Saving of the plan failed.", "alrPlanSaved": "The plan has been saved!", "alrPreparingPrint": "Preparing print-out...", "alrPrintDeleteFailed": "Print deletion failed.", "alrPrintDeleted": "Print has been deleted!", "alrPrintSaveFailed": "Print save failed.", "alrPrintSaved": "Print was saved!", "alrReadOnlyCaseDataFailed": "The load of case data for reading failed.", "alrRecalcFailed": "Error during recalculation!", "alrRecalculating": "Recalculating...", "alrRestorTemplFailed": "Restoring template failed.", "alrRoleDataLoadFailed": "Role data loading failed.", "alrRoleDeleteFailed": "Role deletion failed.", "alrRoleDeleted": "Role has been deleted!", "alrRoleDeleting": "Deleting role...", "alrRoleSaveFailed": "Role saving failed.", "alrRoleSaved": "Role has been saved!", "alrRoleSaving": "Saving role...", "alrRunEvent": "Launching event...", "alrSaveFailed": "Save failed.", "alrSaved": "Saved!", "alrSaving": "Saving...", "alrSavingBeforeRecalcFailed": "Error during saving prior to recalculation!", "alrSavingFailed": "Error during saving!", "alrSavingPlan": "Saving plan...", "alrSavingPrint": "Saving print...", "alrSearchNoResults": "No results corresponding to the searching parameters.", "alrSearchRequestFailed": "Error during request sending!", "alrSearching": "Searching...", "alrSettFailed": "Settings could not be saved.", "alrSettSaved": "<PERSON><PERSON><PERSON> was saved.", "alrSettingsLoadFailed": "Settings data failed to load.", "alrSleepCaseFailed": "Case suspending failed.", "alrStoreNameNotDefined": "Store \"{{storeName}}\" is not defined.", "alrStoreNotDefined": "Store has not been defined.", "alrSubActionNotDefined": "SubAction and suffix has to be defined.", "alrSubStoreNotDefined": "SubStore and suffix has to be defined.", "alrSuggestBoxDataNotContains": "Data of suggest box \"{{label}}\" do not contain \"{{prop}}\"!", "alrSuspendingCase": "Suspending case...", "alrTableDataFailed": "Table data load failed.", "alrTasNewVersion": "New version of the application was found.", "alrRefresh": "It is necessary to {{- spanRefresh}} the page in the browser.", "alrTasVersionLoadFailed": "Application version load failed!", "alrTaskHandOver": "Handing over the task...", "alrTaskHandedOver": "The task has been handed over to user:", "alrTaskNoRights": "You do not have permission to view the task nr. {{id}}.", "alrTaskNotFound": "The task was not found.", "alrTempDataLoadFailed": "Templates data failed to load.", "alrTempHeadersLoadFailed": "Template headers failed to load.", "alrTempDeleteFailed": "Template deletion failed.", "alrTempDeleted": "Template has been deleted!", "alrTempFoldersLoadFailed": "Template folders failed to load.", "alrTempNameLoadFailed": "Template name failed to load.", "alrTempRestored": "The template was restored with status Developed.", "alrTempSaveFailed": "Template saving failed.", "alrTempsLoadFailed": "Templates loading failed.", "alrTempVarDataLoadFailed": "Template variable data failed to load.", "alrTempVarSaveFailed": "Variable saving failed.", "alrTempVarsLoadFailed": "Template variables failed to load.", "alrTotalCountFailed": "Total records counting in the table failed.", "alrTreeDataFailed": "Tree data loading failed.", "alrTskAddFailed": "Task adding failed.", "alrTskAdded": "Task has been added!", "alrTskAdding": "Adding task...", "alrTskAssignFailed": "Task assignment failed.", "alrTskAssigned": "Task has been assigned.", "alrTskCompleteFailed": "Error during task completion.", "alrTskDataLoadFailed": "Task data failed to load.", "alrTskDeleteFailed": "Task deletion failed.", "alrTskDeleted": "Task has been deleted!", "alrTskNameLoadFailed": "Task name failed to load.", "alrTskRecalculated": "Task recalculated!", "alrTskSaveFailed": "Error during task saving.", "alrTskSaved": "Task saved!", "alrTskSavedAndCompleted": "Task saved and completed!", "alrTskScheduleFailed": "Task scheduling failed.", "alrTskScheduled": "Task has been scheduled.", "alrTskTakeFailed": "Task take over failed.", "alrTskTaken": "Task has been taken over.", "alrTskTakign": "Taking the task over...", "alrTsksMappingFailed": "Mapping tasks loading failed.", "alrUserDataLoadFailed": "User data loading failed.", "alrUserDeleteFailed": "User deletion failed.", "alrUserDeleted": "User has been deleted!", "alrUserDeleting": "Deleting user...", "alrUserIsNotActive": "User is not active.", "alrUserNotLoaded": "User failed to load.", "alrUserParamsLoadFailed": "User parameters failed to load.", "alrUserSaveFailed": "User saving failed.", "alrUserSaved": "User has been saved!", "alrUserSaving": "Saving user...", "alrUserStatusChangeFailed": "User status change failed.", "alrUserStatusChanged": "User status has been changed!", "alrUserStatusChanging": "Changing user status...", "alrVarDeleteFailed": "Variable deletion failed.", "alrVarDeleted": "Variable has been deleted!", "alrVarSaveFailed": "Variable saving failed.", "alrVarSaved": "Variable has been saved.", "alrVarSaving": "Saving variable...", "alrVarsForModalFilterFailed": "Modal filter variables load failed.", "alrVarsLoadFailed": "The variables could not be loaded.", "alrVarsOrderLoadFailed": "Variables order load failed.", "alrVarsOrderSaveFailed": "Variables order saving failed.", "alrViceDeleted": "Substitute has been cancelled.", "alrViceFailed": "Substitution was not successful.", "alrViceNotFound": "Substitute was not found!", "alrViceSaveFailed": "Substitute could not be saved.", "alrViceSaved": "Substitute has been saved!", "alrViceSaving": "Saving substitute...", "always": "Always", "annually": "Annually", "assHierarchy": "Relationship to reference person", "assHierarchyAncestors": "All superiors of the reference person", "assHierarchyChildren": "Direct subordinates of the reference person", "assHierarchyDescendants": "All subordinates of the reference person", "assHierarchyGuarantor": "Only reference person", "assHierarchyParent": "Direct superior of the reference person", "assHierarchySiblings": "Colleagues of reference person", "assMethodAutomatic": "By the computer", "assMethodLastSolver": "To last task owner", "assMethodLastSolverChoice": "Task owner selected by the most recent task owner", "assMethodLeast": "Task owner with the least number of tasks", "assMethodPull": "No one, task will be offered", "assMethodSelect": "Task owners selected by the task supervisor", "assMethodVariable": "Task owner, from variable", "assessmentOfConds": "Assessment of conditions", "assign": "Assign", "assignAttrs": "Attributes assignment", "assignAttrsLogType": "Assignment of attributes to logical type of document", "assigned": "Assigned", "assigningRoles": "Assigning roles", "assignments": "Assignments", "attachments": "Documents", "attachmentsList": "Documents list", "attribute": "Attribute", "attributeNew": "Attribute – New", "availableVars": "Available variables", "body": "Body", "borders": "Borders", "byFolders": "By folders", "byOrganization": "By organization", "byRole": "By role", "calculation": "Calculation", "calculations": "Calculations", "calendar": "Calendar", "carriedIfNoOther": "Will be carried out if no other", "case": "Case", "caseCreation": "Case creation", "caseGraph": "Instance diagram", "caseNoEvents": "Case does not contain events.", "caseNum": "Case nr.", "caseOwner": "Case owner", "caseStatus": "Case status", "caseVar": "case", "cases": "Cases", "casesWithProblem": "My cases with a problem", "category": "Category", "changeTaskSolver": "Change task solver", "changedBy": "Changed by", "changedWhen": "Changed (when)", "checkbox": "Checkbox", "checkboxList": "CheckboxList", "choosePrint": "Template of print", "chooseUserToAssignTsk": "Choose the user to be assigned to the task", "choosenAttrs": "<PERSON><PERSON> attributes", "city": "City", "class": "Class", "clickToClose": "Close by clicking", "clickToRefresh": "Click to update the page in the browser", "clickToRepeat": "Repeat action by clicking", "clientLanguage": "Client language", "cloneRow": "Duplicate line", "close": "Close", "closeAll": "Close all", "coWorkersOfPlanGuar": "Co-workers of plan guarantor", "color": "Color", "colors": "Colors", "column": "Column", "columnName": "Column name", "comment": "Comment", "complete": "Complete", "completion": "Completion", "componentDescription": "Component description", "condition": "Condition", "conditions": "Conditions", "confirmAttachDeletion": "Do you really want to delete the document?", "confirmDeleteDialog": "Do you really want to delete the {{variable}}?", "confirmDialogEventSave": "To switch, it is needed to save the event. Do you want to save it?", "confirmResetDashboard": "Do you really want to reset the dashboard?", "confirmSaveChanges": "Save your changes?", "confirmSaveDiagramChanges": "Save your changes in diagram?", "confirmSaveTaskChanges": "Save your changes in task?", "confirmRestoreDialog": "Do you really want to restore the {{variable}}?", "confirmSaveNote": "Do you want to save a note?", "confirmSleepCase": "Do you really want to suspend the case?", "confirmTakeoverTsk": "Do you really want to take over the task?", "confirmWakeUpCase": "Do you really want to unsuspend the case?", "connection": "Links", "connectionFailed": "Connection to server failed.", "connectionVar": "link", "constant": "Constant", "contact": "Contact", "contactTaskOwner": "Contact the task owner", "containerSettings": "Container settings", "contains": "contains", "continueSolving": "Continue in solution", "copied": "Copied!", "copy": "Copy", "copyShortcut": "Press Ctrl+C", "copyToClipboard": "Copy to clipboard", "createForm": "Create form", "csv": "csv", "csvFile": "CSV file", "customPrint": "Custom print", "daily": "Daily", "dashCvNoOverview": "No overview selected – you choose it in container settings", "dashCvNoRights": "You do not have permission to view the overview, please contact the administrator.", "dashFavNoShortcut": "No substitutes selected – choose them in the container settings", "dashboard": "Dashboard", "date": "Date", "dateList": "LOV of dates", "day": "day", "dayE": "Day", "daysDHM": "Days: (dd:hh:mm)", "defChangeVarInfluence": "This variable definition change will propagate into already created cases.", "defChangeInfluence": "This definition change will propagate into already created cases.", "defaultCaseName": "Default case name", "defaultLbl": "Default {{label}}", "defaultLblShe": "Default {{label}}", "defaultLblIt": "Default {{label}}", "defaultPrintProcess": "Default – process", "defaultPrintTask": "Default – task", "defaultValue": "Default value", "delUser": "Delete user", "delete": "Delete", "deleteCol": "Delete column", "deleteRow": "Delete line", "deleteSelected": "Delete selected", "deleted": "Deleted", "deletedOn": "Deleted", "deletedShe": "Deleted", "description": "Description", "deselect": "Deselect", "detail": "Detail", "developed": "Under development", "dial": "<PERSON><PERSON>", "dic": "VAT", "directSubOfPlanGuar": "Direct subordinate of plan guarantor", "directSupOfPlanGuar": "Direct superior of plan guarantor", "disableFilter": "Disable filter", "dmsAssignAttrs": "DMS attribute assignment", "dmsAttribute": "DMS attribute", "dmsAttributes": "DMS attributes", "dmsColumns": "DMS – columns", "dmsVisNull": "Only in this process", "dmsVisSub": "In the sub-processes", "dmsVisSup": "In the parent process", "dmsVisSupSub": "In the parent and subordinate processes", "dmsVisibility": "Documents will be seen", "doNotShowVariablesWith_": "Variable name starting with '_' will not be shown to users", "document": "Document", "documentVar": "document", "documents": "Documents", "doesNotContain": "does not contain", "done": "Done", "download": "Download", "dragAddFiles": "Add files by drag and drop or click {{- here }} to select file.", "dragAddFile": "Add file by drag and drop or click {{- here }} to select file.", "here": "here", "dropContainer": "Drop container", "dropzoneTip": "Drop the files here", "dropZoneUserPhoto": "Drop an image here, or click to select a file to upload.", "dueDate": "Due date", "duty": "Duty", "dynList": "Dyn. list", "dynRowsDefinition": "Table and columns definition", "dynTableName": "Dynamic table name", "dynTable": "Dynamic table", "dynTables": "Dynamic tables", "dynamicList": "Dynamic list", "dynamicRows": "Dynamic rows", "dynamicTable": "Dynamic table", "edit": "Edit", "editAttribute": "Edit attribute", "editOrgUnit": "Edit org. unit", "editRole": "Edit role", "editRule": "Edit rule", "editUser": "Edit user", "editor": "Editor", "email": "E-mail", "emailsQueue": "E-mail queue", "empty": "Empty", "end": "End", "error": "Error", "errored": "Errored", "error404": "Error 404 – page not found!", "event": "Event", "events": "Events", "eventsRun": "Run event", "every": {"masc": "Every", "neutral": "Every", "repeat": "Every"}, "everyWorkDay": "Every work day", "excel": "Excel", "favourites": "Favourites", "fax": "Fax", "file": "File", "fileLogicalType": "Logical file type", "fileName": "File name", "filePlacement": "Placement into folder", "files": "Files", "filter": "filter", "filterFrom": "Filter from", "filterTitle": "Filter", "filtrate": "Filter", "finishTask": "Finish task", "finished": "Finished", "finishedBy": "Finished by", "finishedOn": "Finished", "first": "First", "firstLeft": "First left", "firstName": "First name", "firstRight ": "First right", "firstRowColumnsName": "First line contains column names", "folder": "Folder", "folder-": "Folder –", "folderExecRightsText": "Assign roles which will be able to initiate cases in folder", "folderExecRightsTextOS": "Assign organizational units which will be able to initiate cases in folder", "folderName": "Folder name", "font": "Font", "fontMainHeader": "Font of main header", "form": "Form", "fourth": "Fourth", "freeTsk": "Free task", "fri": "Friday", "from": "From", "fsDescription": "description", "fsName": "name", "fsTooltip": "tooltip", "fullName": "Full name", "fullScreen": "Full screen", "getTotalCount": "Total count", "graph": "Graph", "handExecutionTaskListEmpty": "Pick event", "handOver": "Hand over", "handOverToUser": "Hand over to user", "handover": "Handover", "headerDashboard": "Header of dashboard", "help": "Help", "hideLogout": "Hide logout", "hideNewProcess": "Hide 'New Case'", "hideProcs": "<PERSON><PERSON> Cases", "hideTasks": "Hide Tasks", "historicalValues": "Historical values", "currentValues": "Current values", "history": "History", "home": "Home", "html": "HTML", "ic": "Company ID", "id": "ID", "inCasesNames": "In the names of cases", "inTasksNames": "In the names of tasks", "inDevelopment": "Under development", "inEvery": "in every", "inFiles": "In files", "initiator": "Initiator", "inTasks": "In tasks", "inactive": "Inactive", "inactiveShe": "Inactive", "incidences": "occurrences", "inclusion": "Inclusion", "info": "Info", "inputParams": "Input parameters", "insert": "Insert", "insertAttachTip": "Drag and drop to insert a document", "insertVar": "Insert variable", "insertSnippet": "Insert snippet", "snippet": "Code snippet", "insertedBy": "Uploaded by", "insertedOn": "Added", "insteadOf": "instead", "instructions": "Instructions", "invitation": "Invitation", "isEmail": "Not a valid e-mail", "isEmpty": "is empty", "isExisty": "It is not valid", "isManagerOrgUnit": "Is the org. unit manager", "isNotEmpty": "is not empty", "isRequired": "This field is required", "justSave": "Just save", "keepGlobalOrder": "Keep global order", "key": "Key", "last": "last", "lastName": "Last name", "lastOwnerOfTask": "Last task owner of the task", "licenceKey": "License key", "link": "Link", "linkConditions": "Link conditions", "list": "List", "listName": "name of the list", "listOfValues": "List of values", "listValue": "value of list", "loading": "Loading...", "location": "Location", "locked": "Locked", "logIn": "Log in", "logOut": "Log out", "logicalType": "Logical type", "loginError": "Invalid login.", "loginTimeout": "Session timeout (sec.)", "longText": "Long text", "mailEscalation": "E-mail with overview of escalated tasks", "mailProcEscalation": "E-mail with overview of escalated cases", "mailPromptly": "E-mail notification of new task", "mailPull": "E-mail notification of task to pull", "mailTotal": "Summary e-mail with task overview", "mainButton": "Main button", "mainColor": "Main color", "mainHeader": "Main header", "mainLanguage": "Main language", "manager": "Manager", "managerOfOrgUnit": "Manager of organizational unit", "mandatory": "Mandatory", "manualStartEvent": "Manual event start", "mapping": "Mapping", "mappingSubProcessVars": "Mapping of sub-process variables", "markAll": "Mark all", "menu": "<PERSON><PERSON>", "mine": "Mine", "mobilePhone": "Mobile phone", "mon": "Monday", "month1": "January", "month10": "October", "month11": "November", "month12": "December", "month2": "February", "month3": "March", "month4": "April", "month5": "May", "month6": "June", "month7": "July", "month8": "August", "month9": "September", "monthI": "month", "monthly": "Monthly", "months": "months", "more": "More", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingle", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "Multi-instance", "myUnfinishedTasks": "My unfinished tasks", "name": "Name", "nested": "Nested", "never": "Never", "new": "New", "newCase": "New case", "newFolder": "Folder – New", "newForm": "New form", "newIt": "New", "newName": "New name", "newShe": "New", "newSolver": "New task owner", "no": "No", "noAttach": "No documents (Click to add)", "clickToAddAttach": "Click to add", "noName": "Without a name", "noOneBeOffered": "no one, task will be offered to a restricted group of users", "noPageRights": "You do not have permission to view this page.", "node": "Node", "notFound": "Not found", "notMatch": "Not match", "notNumber": "Not a number", "notIntNumber": "Not an integer", "notSent": "Not sent", "notValid": "Not valid", "notes": "Notes", "notesOnContacts": "Notes on contacts", "notice": "Notice", "notification": "Notification", "nrOfItems": "Number of items", "number": "Number", "numberList": "LOV of numbers", "ok": "OK", "oneMustBeMet": "At least one must be met", "onlyOrgUnit": "Organizational unit only", "onlyPlanGuarantor": "Plan guarantor only", "openAll": "Open all", "operating": "Active", "order": "Order", "orderByColumn": "Order by column", "orgName": "Subject name", "orgStructure": "Org. structure", "orgUnit": "org. unit", "orgUnitE": "org. unit", "orgUnitName": "Org. unit name", "orgUnitShe": "Org. unit", "orgUnits": "Org. units", "organization": "Organization", "overview": "Overview", "overviewMapping": "Overview mapping", "overviewNew": "Overview – new", "overviewSetSharing": "Set overview sharing for each user group", "overviews": "Overviews", "owner": "Owner", "ownerWithLeastTasks": "Task owner with the least number of tasks", "pageNotFound": "Page not found", "parentFolder": "Parent folder", "parentUnit": "Parent unit", "participants": "Participants", "password": "Password", "passwordChallenge": "Notification", "passwordChallengeText": "Do you really want to notify all users to change their password?", "passwordChange": "Change password", "passwordCheck": "Password (check)", "passwordNew": "New password", "passwordNewCheck": "New password (check)", "paused": "Inactive", "personInOrgStr": "Assigned by person in org. structure", "phone": "Telephone", "photo": "Photo", "plan": "plan", "planGuarantor": "Plan guarantor", "planTitle": "Plan", "plans": "Planning", "plnOffType": "Repeat", "plnOrgUnit": "Organizational unit", "plnTProc": "Case template", "plnUser": "Plan sponsor", "plnUsersSelect": "Restrictive conditions for selection of one or more Initiators", "prependTsk": "Prepend task", "prependedTsk": "Prepended task", "primaryKey": "Primary key", "print": "Print", "printTemplate": "Print template", "printType": "Print type", "printer": "Print – HTML", "priority": "Priority", "procDescription": "Process description", "procDueDateFinish": "Due date for finishing the case", "procName": "Case name", "procOwner": "Process owner", "procSummary": "Being solved", "process": "Process", "processName": "Name of process", "property": "Property", "quickFilter": "Fast filter", "radioButtonList": "RadioButtonList", "reEvaluates": "Re-evaluate", "recalc": "Recalculate", "recipient": "Recipient", "recipientsId": "Recipient's ID", "records": "Records", "referenceUser": "Reference person", "refresh": "Refresh", "registered": "Registered", "relatToPlanSponsor": "Relationship to plan sponsor", "remove": "Remove", "removeVice": "Remove substitute", "renameCols": "Rename columns", "repeatLogin": "Repeat login or choose other type of authentication.", "repeatOrReport": "Try again later or contact the administrator.", "repetition": "Repetition", "required": "Mandatory", "reset": "Reset", "restrictTaskOwners": "Restrictions for task owners", "restrictUsers": "Restrict users", "returnSubProcessVars": "Return of sub-process variables", "revision": "Revision", "right": "Right", "rightOrDuty": "Right / Duty", "role": "role", "roleName": "Role name", "roleSg": "Role", "roles": "Roles", "row": "row", "rule": "Rule", "ruleCSVFile": "CSV file Name", "ruleCSVHeader": "The first line of the CSV file is a header", "ruleCSVMask": "CSV file name mask", "ruleCSVSeparator": "The column separator", "ruleNew": "Rule – New", "ruleParamsMap": "Mapping variables", "ruleProcOwnCSV": "Defined in mapping", "ruleTypeCSVExpProcs": "CSV export of all template cases", "ruleTypeCSVMrgProcs": "According to CSV run the cases and update cases variables", "ruleTypeCSVRunProcs": "According to CSV run the cases", "ruleTypeCSVUpdProc": "According to CSV update the case variables", "ruleTypeCSVUpdProcs": "According to CSV update the cases variables", "ruleTypeCSVUpdateList": "Update dynamic list according to CSV", "ruleTypeReturn": "Event response", "ruleTypeUpdateListOfProcesses": "Update dynamic list of processes", "rules": "Rules", "run": "Run", "runProcess": "Start process", "running": "Running", "sat": "Saturday", "save": "Save", "saveAsAttachment": "Save print as document to case", "scheduling": "Scheduling", "scheme": "Visual identity", "script": "<PERSON><PERSON><PERSON>", "scripts": "<PERSON><PERSON><PERSON>", "search": "Search", "searchResult": "Search result", "second": "Second", "secondLeft": "Second left", "secondRight": "Second right", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "To task owners automatically selected by the computer", "selectedByTaskSupervisor": "To task owners selected by the supervisor", "selectedPrint": "selected print", "selectedUser": "Selected user", "send": "Send", "sendingFailed": "Failed", "sendOn": "Sending time", "sendTestMail": "Test e-mail", "sequence": "Sequence", "setDefault": "Set as default", "setVice": "Set substitute", "setViceAttachmentsNotes": "Rights to upload documents and notes", "settings": "Settings", "shortcuts": "Shortcuts", "showAttachmentsClick": "Show documents on click", "showCommentCol": "Show the comment column", "skype": "Skype", "solve": "Solve task", "solvedBy": "Solved by", "solver": "Task owner", "sort": "Sort", "sortByColumn": "Sort by column", "sorting": "Sorting", "sourceTask": "Source task", "sourceVar": "Source variable", "start": "Start", "startDate": "Start date", "startCalDate": "Start date", "endCalDate": "End date", "state": "State", "stateAddress": "State", "status": "Status", "street": "Street address", "subProcess": "Sub-process", "subject": "Subject", "substitute": "Substitute", "sun": "Sunday", "superior": "Superior", "supervis": "Supervisor", "supervisor": "Task supervisor", "suspend": "Suspend", "suspended": "Suspended", "suspendedx": "Suspended", "tTaskAgain": "Repeated activation behaviour", "tTaskAutoCompleteCaption": "The task will be fulfilled automatically if", "tTaskCompletionCOA": "all conditions are met at the same time", "tTaskCompletionCOO": "at least one condition is met", "tTaskDueOffsetNone": "immediately", "tTaskDueOffsetPO": "entered by supervisor", "tTaskDueOffsetPS": "within days of case start", "tTaskDueOffsetTS": "within days of possible activity start", "tTaskDueOffsetVC": "of variables continuously", "tTaskDueOffsetVO": "of variables at startup", "tTaskInvClassConf": "Secret", "tTaskInvClassPriv": "Private", "tTaskInvClassPubl": "Public", "tTaskInvPriority1": "1-highest", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-lowest", "tTaskInvokeEventB": "in background", "tTaskInvokeEventI": "immediately", "tTaskReferenceUserLastSolver": "The last owner of the task", "tTaskReferenceUserMan": "Manager of org. unit xy", "tTaskReferenceUserUser": "User xy", "tTaskRunOnlyOnce": "Run only once", "tTaskSufficientEnd": "Completing finishes the whole case", "tabName": "Tab name", "table": "Table", "takeOnlyOrder": "Take only order", "takeover": "Take over", "targetTask": "Target task", "targetVar": "Target variable", "taskAutomatic": "automatic status", "taskEmailNotification": "E-mail notification", "taskEvent": "runs event", "taskEventWait": "waits for event", "taskOwner": "Task owner", "taskSolverAssign": "for assignment to task owner", "taskStart": "Start", "taskStatus": "Status", "taskStatusA": "Active", "taskStatusAP": "Active sub-process", "taskStatusAS": "Sleeping sub-process", "taskStatusD": "Completed", "taskStatusL": "Waiting", "taskStatusLEdit": "Unable to edit pending task", "taskStatusN": "New", "taskStatusP": "Planned", "taskStatusS": "Suspended", "taskStatusT": "To pull", "taskStatusW": "For assignment", "taskStatusWT": "For scheduling", "taskSubprocess": "implemented by sub-process", "taskTabVariables": "Assigned variables", "taskType": "Task type", "taskWillBeAssigned": "Task will be assigned", "tasks": "Tasks", "tasksToPull": "Tasks to pull", "taskstatusAD": "Active and finished", "tempId": "Template ID", "tempVar": "template", "template": "Template", "templateDeleted": "Deleted", "templateStatus": "Template status", "templates": "Templates", "templatesFolder": "Template – folder", "testForm": "Test form", "tested": "Tested", "text": "Text", "textList": "LOV of texts", "textMultipleLines": "Text with multiple lines", "textSuggest": "Suggester", "third": "Third", "thirdCenter": "Third center", "thu": "Thursday", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title": "Title", "to": "To", "toHide": "<PERSON>de", "toInclusive": "To (inclusive)", "toPull": "Tasks to pull", "tooltip": "<PERSON><PERSON><PERSON>", "total": "total", "tprocName": "Template of process", "tsk": "Task", "tskAssignDues": "Set time restrictions for this task", "tskName": "Task name", "tskNum": "Task number", "tskSolver": "Task owner", "tskTemplate": "Task template", "tskVar": "task", "tsksDone": "Finished", "tsksSolvers": "Task owners", "ttAdd": {"heading": "Add", "body": "Allows to add a new item or new parameters which have not yet been defined."}, "ttAddActivity": {"heading": "Add", "body": ""}, "ttAddAttach": {"heading": "Add document", "body": "Allows to add a new document."}, "ttAddAttribute": {"heading": "Add", "body": ""}, "ttAddContainer": {"heading": "Add container", "body": "Adds container with selected contents"}, "ttAddFile": {"heading": "Add", "body": ""}, "ttAddStructure": {"heading": "Add item to organizational structure", "body": "Allows to add a new item of the organizational structure or new parameters which have not yet been defined."}, "ttAddTemp": {"heading": "Add new template", "body": "Creation of new case templates. The owner of the template will be the user currently logged in. The template is automatically assigned an \"under development\" status."}, "ttAddTsk": {"heading": "Add new task", "body": "Creation of a new task within the process template. Parameters of the task may be specified depending on the type of task. Links to other tasks may be added or edited in the Graph or Link tabs."}, "ttAddTskGraph": {"heading": "Add new task", "body": "Creating of new task within the process template. Parameters of the task may be specified depending on the type of task. Links to other tasks may be added or edited in the Graph or Link tabs."}, "ttAddUser": {"heading": "Add new user", "body": "Add new user. Each user must have a unique user name. Basic information can be set for users, along with their assignment to the organizational structure and role assignment. New users are automatically given locked status."}, "ttAddVar": {"heading": "Add new variable", "body": "Creation of new variable within the case template. Each variable bears information which can be worked with by the case task owners. It is possible to specify the name, type and default values of the variable."}, "ttAddVice": {"heading": "Add substitute", "body": ""}, "ttAssignAttribute": {"heading": "Attributes assignment to logical document type", "body": ""}, "ttAssignTsk": {"heading": "Assign", "body": "Enables task assignment to a specific task owner or adding an item to a defined structure."}, "ttCases": {"heading": "Cases", "body": ""}, "ttOverviews": {"heading": "Overviews", "body": ""}, "ttChangePass": {"heading": "Password change", "body": "Editing of the passwords of users, who are managed directly in the environment of the application.  If the users are managed by an external service (LDAP), the password needs to be managed there."}, "ttClose": {"heading": "Close", "body": "The window will be closed without saving changes."}, "ttCloseTemp": {"heading": "Close", "body": "The window with template definition will be closed."}, "ttCompleteTsk": {"heading": "Finish task", "body": "Confirms that the task is done and sends it for further processing as predefined."}, "ttContact": {"heading": "Contact", "body": "Displays contacts for the task supervisor."}, "ttContainerSettings": {"heading": "Settings", "body": "Allows to change settings for the given container."}, "ttCopyHdr": {"heading": "Copy header", "body": "Creating copy of the selected header. Header selection is done by clicking in the template headers table."}, "ttCopyTemp": {"heading": "Copy template", "body": "Creating copy of the selected template. Template selection is done by clicking in the process templates table."}, "ttCopyVar": {"heading": "Copy of variable", "body": "Definition copy for the selected variable and saving of the variable under a new name. Variables are selected by clicking in the table of variables."}, "ttDel": {"heading": "Delete", "body": "Deletes the selected item."}, "ttDelAttach": {"heading": "Delete document", "body": "Deletes the selected document."}, "ttDelConnection": {"heading": "Delete link", "body": "Delete selected link between two tasks of the case. Deletion needs to be confirmed. Deletion is done for selected link. Select the link by clicking it in the table of links."}, "ttDelFolder": {"heading": "Folder deletion", "body": "Delete the selected folder."}, "ttDelOverview": {"heading": "Delete overview", "body": "Deletes the selected overview."}, "ttDelTemp": {"heading": "Delete template", "body": "Assigns deleted status to a template. Only when the delete request is repeated, the template is physically removed. The action is applied to the selected template. Select the template by clicking on it in the table of case templates."}, "ttDelTsk": {"heading": "Task deletion", "body": "Removal of selected task. Deletion needs to be confirmed. Together with the task, all related links to other tasks in the process template will be removed. Select the task by clicking on it in the table of tasks."}, "ttDelTskOrConnection": {"heading": "Delete task or link", "body": "Removal of a selected task or selected link between two process tasks. This action must be confirmed. Related links to other process tasks will be removed along with the task. Click to select."}, "ttDelVar": {"heading": "Variable deletion", "body": "Deletion of selected variable. This action must be confirmed. Variable will no longer be available for individual process tasks. Variables are selected by clicking in the table of variables."}, "ttDelVice": {"heading": "<PERSON><PERSON> substitute", "body": ""}, "ttDetailCase": {"heading": "Detail", "body": "Displays the details of selected case."}, "ttDetailCertificate": {"heading": "Certificate detail", "body": "Displays the details of selected certificate."}, "ttDetailHistory": {"heading": "Detail", "body": "Displays details of selected item."}, "ttDetailTsk": {"heading": "Task detail", "body": "Displays the details of selected task."}, "ttDmsFolderAdd": {"heading": "Add new folder", "body": "Insert a new folder. If a folder is selected, the folder is preprefilled as the parent folder."}, "ttDmsFolderEdit": {"heading": "Edit folder", "body": "Edit the selected folder."}, "ttDocuments": {"heading": "Documents storage", "body": ""}, "ttDownload": {"heading": "Download", "body": "Downloads selected file."}, "ttDropContainer": {"heading": "Drop", "body": "Drops container from dashboard"}, "ttENotification": "E-mail notification", "ttEdit": {"heading": "Edit", "body": "Enables editing of the selected item."}, "ttEditAttach": {"heading": "Edit", "body": "Enables view and editing of attributes (metadata) of the uploaded file."}, "ttEditConnection": {"heading": "Edit links", "body": "Editing of link between two tasks. It is possible to edit parameters of link behaviour and link conditions. The action is applied to the selected link. Links are selected by clicking on them in the table of links."}, "ttEditOverview": {"heading": "Edit overview", "body": "Enables editing of a selected overview."}, "ttCopyOverview": {"heading": "Copy overview", "body": "Creating copy of the selected overview."}, "ttEditPath": {"heading": "Add overview", "body": "Enables the definition of a new overview."}, "ttEditTemp": {"heading": "Editing of template definition", "body": "Editing of case template. Any template parameter can be edited. The action is carried out for the selected template. Select the template by clicking in the table of case templates."}, "ttEditTsk": {"heading": "Edit task", "body": "Editing of task information and task parameters. The action is applied to the selected task. Select a task by clicking in the table of tasks."}, "ttEditTskOrConnection": {"heading": "Editing of tasks or links", "body": "Editing of task information and task parameters or editing links between two tasks, their behavioral parameters and link conditions. The action is applied to the selected task or link. Click to select."}, "ttEditTskVars": {"heading": "Edit", "body": "Edit task variables"}, "ttEditUser": {"heading": "Editing of user information", "body": "Editing of basic information about users, passwords, assignment to organizational unit and role assignment. The action is applied to the selected user. Users are selected by clicking in the table of users."}, "ttEditVar": {"heading": "Editing of variable", "body": "Editing the name, type and default values of variables. The action is applied to the selected variable. Variables are selected by clicking in the table of variables."}, "ttEnotTgt": "Recipient", "ttEnotTgtG": "Task supervisor", "ttEnotTgtO": "Case owner", "ttEnotTgtP": "%s", "ttEnotTgtR": "Role %s", "ttEnotTgtS": "Organizational unit %s", "ttEnotTgtT": "Task owner %s", "ttEvent": {"heading": "Custom task", "body": "Instant invocation of event in this task."}, "ttEvents": {"heading": "Events", "body": "Setting of business rules to react on defined internal or external events in the system. Access requires role of $PowerUser."}, "ttFavourites": {"heading": "Favourites list", "body": "A list of all your favourites with the option to edit or delete them from the list."}, "ttFilter": {"heading": "Filter", "body": "Displays only those items that meet the defined filter conditions."}, "ttFilterPrc": {"heading": "Filter", "body": "Displays only those cases that meet the defined filter conditions."}, "ttFilterTemp": {"heading": "Filter", "body": "Displays only those templates that meet the defined filter conditions."}, "ttFilterTsk": {"heading": "Filter", "body": "Displays only those tasks that meet the defined filter conditions."}, "ttFilterUser": {"heading": "Filter", "body": "Displays only those users that meet the defined filter conditions."}, "ttFullScreen": {"heading": "Fullscreen", "body": "Shows the container contents in fullscreen mode."}, "ttGraph": {"heading": "Graph", "body": "Graphical representation of the current case status."}, "ttGraphActualFinish": "Actual finish ", "ttGraphActualStart": "Actual start date", "ttGraphCond": "Conditions", "ttGraphCond1": "at least one must be met", "ttGraphCondAll": "all must be met", "ttGraphCondElse": "Unless another condition is met", "ttGraphDeadlinePo": "Deadline: entered by case owner", "ttGraphDeadlinePs": "Deadline: within %s days after case initiation", "ttGraphDeadlineTs": "Deadline: within %s days after task initiation", "ttGraphDelayPo": "Task initiation: entered by case owner", "ttGraphDelayPs": "Task initiation: %s days since case initiation", "ttGraphDelayTs": "Task initiation: %s days since task initiation", "ttGraphEnd": "Completing the task concludes the entire case", "ttGraphFinishedBy": "Finished by", "ttGraphHiearchyA": "all superiors of task supervisor", "ttGraphHiearchyC": "direct subordinate of task supervisor", "ttGraphHiearchyD": "all subordinates of task supervisor", "ttGraphHiearchyG": "task supervisor", "ttGraphHiearchyL": "all ", "ttGraphHiearchyP": "direct superior of task supervisor", "ttGraphHiearchyS": "task supervisor co-workers", "ttGraphLinkFrom": "From", "ttGraphLinkTo": "To", "ttGraphMethodL": "to last task owner %s", "ttGraphMethodS": "to task owner selected by a supervisor", "ttGraphMethodT": "to automatically selected task owner", "ttGraphMethodV": "to task owner assigned to variable %s", "ttGraphMultiinstance": "Multi-instance", "ttGraphNoneMand": "Mandatory link", "ttGraphOnlyOnce": "Run only once", "ttGraphSave": {"heading": "Save diagram and create template", "body": ""}, "ttGraphStart": "Task will be activated automatically after starting the case", "ttGraphTaskHiearchy": "Task owner", "ttGraphTaskMethod": "Task will be assigned", "ttGraphTaskOwner": "Task supervisor", "ttGraphTaskOwnerOS": "Organizational unit manager", "ttGraphTaskOwnerPO": "Case owner", "ttGraphTaskOwnerSU": "Selected user", "ttGraphTaskRole": "with role", "ttGraphTaskTypeA": "Automatic task", "ttGraphTaskUser": "Task owner", "ttGraphWait1": "Input parameters: waiting for one", "ttGraphWaitA": "Input parameters: waiting for all", "ttGraphWaitFirst": "Input parameters: waiting for all, running first", "ttGraphWaitN": "Input parameters: waiting for %s", "ttHandover": {"heading": "Hand over task", "body": "Allows to hand over task to another available user."}, "ttDelegate": {"heading": "Delegate task", "body": ""}, "ttReject": {"heading": "Reject task", "body": ""}, "ttHelp": {"heading": "Instant help", "body": "Allowing or disabling instant help. Help is displayed in the form of bubbles which pop up with information about the user interface when features are moused over."}, "ttHome": {"heading": "Initial user page", "body": "Single place with all information for regular users. Dashboard provides a general view."}, "ttHtml": {"heading": "Generate documentation", "body": "Generation of HTML documentation of the template process. Depending on the type of browser a document may be displayed immediately or saved to a disk."}, "ttInclusion": {"heading": "Inclusion", "body": "Exports a file with the summary of authorization and roles of the user, all roles of the signed used, organisation entities where he is a member or manager, including hierarchy of tasks where he is the supervisor."}, "ttInvAttendees": "Attendees", "ttInvDTEnd": "End", "ttInvDTStart": "Start", "ttInvLocation": "Location", "ttInvitation": "Invitation", "ttJustSave": {"heading": "Just save", "body": "Saves changes."}, "ttLock": {"heading": "Lock", "body": "Locks or unlocks the selection"}, "ttLockUser": {"heading": "Lock", "body": "Locks or unlocks the user"}, "ttLogout": {"heading": "Logout", "body": "User logout. After successful completion of work with the application, the initial login dialog is displayed."}, "ttMapping": {"heading": "Mapping", "body": "A general overview of assigned variables for reading(R), writing (W) and mandatory entry (M) in individual tasks with the option of editing their assignment."}, "ttNewCase": {"heading": "New case", "body": "Creating new process instance – new case. It is possible to select from available process templates or create a case without a pre-defined task structure."}, "ttNewOverview": {"heading": "Add view", "body": "Enables the definition of a new overview."}, "ttOrgStructure": {"heading": "Organization structure", "body": ""}, "ttParent": {"heading": "Superior", "body": "Switch to a case from which the displayed case was created as a sub process."}, "ttPhoto": {"heading": "Photographs", "body": "Photo upload to the user profile. Supports GIF, JPG and PNG formats. The size of the picture will be adjusted automatically."}, "ttPlans": {"heading": "Scheduling", "body": "Setting of rules for automatic one-time or repeated process instances launch – cases according to specified parameters. Access requires the role of $Administrator."}, "ttPrint": {"heading": "Print", "body": "Creates the print."}, "ttRecalc": {"heading": "Recalculation", "body": "Recalculate current variables."}, "ttRedirectToPrc": {"heading": "Case", "body": ""}, "ttResetDash": {"heading": "Reset", "body": "Resets completed changes."}, "ttResetSearch": {"heading": "Reset", "body": "Resets the search form."}, "ttRestoreTemp": {"heading": "Template restore", "body": "Restores deleted template"}, "ttRevision": {"heading": "Revision", "body": "Enables upload of a new file version"}, "ttRoles": {"heading": "Role management", "body": ""}, "ttRunEvent": {"heading": "Run event", "body": "Event invocation in this case"}, "ttSave": {"heading": "Save", "body": "Saves changes and closes the window."}, "ttSaveDMSCols": {"heading": "Save columns", "body": ""}, "ttSaveSettings": {"heading": "Save", "body": "Saves changes."}, "ttSaveTsk": {"heading": "Save only", "body": "Opened task will be saved so you can return to it later."}, "ttSearch": {"heading": "Search", "body": "Starts searching"}, "ttSendNote": {"heading": "Add note", "body": "Enables the insertion of a new note."}, "ttSetConnectionCond": {"heading": "Condition", "body": "Adding or editing link conditions. Editing is applied to the selected link. Click on the link or condition symbol to select."}, "ttSetDefaultDash": {"heading": "Set as default dashboard", "body": "Sets the current dashboard arrangement as default"}, "ttShowHideBtn": {"heading": "Show / hide", "body": "Partly hides or shows the main menu."}, "ttSleepCase": {"heading": "Suspend the case", "body": "Marks the case as suspended. The case will no longer be displayed among active tasks, but if needed it is possible to change the status back to active and finish the entire case later."}, "ttSolve": {"heading": "Open task", "body": "Displays the dialogue allowing work to start on the assigned task according to a pre-defined template."}, "ttStatePlan": {"heading": "Status", "body": "Defines the plan status."}, "ttStatusHdr": {"heading": "Header status change", "body": "The action is applied for the selected template. There are states of \"active\" and \"inactive\" available. Selection of the header is done by clicking in the template headers table."}, "ttStatusTemp": {"heading": "Template status change", "body": "Template life-cycle management is performed through setting its state. There are states of \"in development\", \"active\", \"inactive\" and \"deleted\" available. The action is applied for the selected template. Selection of the template is done by clicking in the case templates table."}, "ttSubprocess": {"heading": "Nested", "body": "Switches to a case which was created as a sub process in the process of the case displayed."}, "ttTabsButtonMore": {"heading": "More", "body": "Shows more options."}, "ttTakeTsk": {"heading": "Take over task", "body": "Allows task to be taken over by a different owner."}, "ttTemps": {"heading": "Process templates", "body": "Central place for process templates managing. Access requires the role of $PowerUser."}, "ttTiming": {"heading": "Scheduling", "body": "Enter the start and end of the task."}, "ttTsks": {"heading": "Tasks", "body": ""}, "ttUploadSettings": {"heading": "Upload", "body": ""}, "ttUserSetting": {"heading": "User settings", "body": "Setting of user contact information, access passwords, and user preferences. Users with role of $Administrator can further manage information about their organization and instances of the TeamAssistant application."}, "ttUsers": {"heading": "Administration of users", "body": "Central administration of users, organizational structure and user roles. Access requires role of $Administrator."}, "ttValidation": {"heading": "Validation", "body": "Validate template and view all existing loops within the template. Notifies on unfulfillable conditions and unused variables."}, "ttViewFile": {"heading": "View", "body": ""}, "ttWakeUpCase": {"heading": "Wake up", "body": ""}, "ttActivateCase": {"heading": "Activate", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Set default DMS columns", "body": "Sets assignment of DMS columns as default."}, "ttResetDmsCols": {"heading": "Reset", "body": "Reset assignment of DMS columns."}, "ttRestoreDoc": {"heading": "Rest<PERSON>", "body": "Restores deleted document."}, "ttSearchHeader": {"heading": "Search", "body": ""}, "tue": "Tuesday", "type": "Type", "typeOfRepetition": "Type of repetition", "unassignedSolvers": "According to task owners", "unassignedTaskSolvers": "Unassigned task owners", "uncategorized": "Uncategorized", "unfinishedProcesses": "Unfinished cases", "unknown": "Unknown", "unknownUser": "Unknown user", "unrestricted": "Unrestricted", "unspecified": "Unspecified", "upload": "Upload", "uploadFile": "Upload file", "uploadPhoto": "Upload photo", "uploadCsv": "Upload csv", "url": "URL", "urlAddress": "URL address", "urlContent": "URL content", "use": "Use", "user": "User", "userByOwnerOfLastTask": "To user chosen by the last task owner.", "userE": "user", "userFilters": "User filters", "userLock": "Lock", "userLockUnlockQ": "Do you really want to change the status of user {{username}}?", "userName": "Username", "userId": "User ID", "userOrgStruct": "Belongs to the organizational unit", "userVice": "Substitute", "userViced": "Substituted", "users": "Users", "usersDeleted": "Deleted", "validation": "Validation", "value": "Value", "var": "variable", "var-": "Variable –", "varChange": "Change of variable will be announced to all case participants", "varTaskMap": "Mapping", "varTemp": "Variable template", "variable": "Variable", "variableType": "Variable type", "vars": "Variables", "varsForMandatory": "Variables for mandatory entry", "varsForReading": "Variables for reading", "varsForWriting": "Variables for writing", "vices": "Substitutes", "viewCVFields": "Available fields", "visForOrgStrMembers": "Visible to organizational unit members", "visForRoleMembers": "Visible to members with roles", "headerVisForRole": "Case visible to members with role", "waitForNumOfInputs": "Waiting for: (number of inputs)", "waitsFor": "waits for", "waitsForAll": "waits for all", "waitsForOne": "waits for one", "waitsForSending": "Waits for sending", "waitsRunFirst": "waits for all, running first", "wakeUp": "Unsuspend", "warning": "Warning", "wed": "Wednesday", "weekIn": "week in", "weekly": "Weekly", "width": "<PERSON><PERSON><PERSON>", "withConditions": "With conditions", "withoutCond": "Without conditions", "year": "year", "yes": "Yes", "zip": "Postal code", "move": "Move", "alertClosing1": "notification will automatically close in:", "inDocuments": "In documents", "inVariables": "In Variables", "headerTask": "Header of task", "planName": "Plan name", "inBulk": "In bulk", "confirmResetDmsColumns": "Do you really want to reset DMS columns?", "dmsColsUseDef": "Using default settings", "dmsColsUseCust": "Using custom settings", "today": "Today", "alrPlanDeleteFailed": "Plan deletion failed.", "notRunning": "Not running", "alrLackOfPermsToAddTask": "You do not have permission to add the task.", "dragTable": "Drag table", "alrDownloadCsvListFailed": "Csv files list download failed.", "alrCsvUploadWrongExtension": "Upload only files with extension *.csv", "addToFav": "Add to favourites", "renameItem": "Rename the item", "removeFromFav": "Remove from favourites?", "alrAddedToFav": "Added to favourites.", "alrRemovedFromFav": "Removed from favourites.", "tskSetAssignDues": "Set time restrictions for task", "isNot": "is not", "alrTskScheduling": "Task scheduling...", "alrFavouritesPageExist": "This page is already in favourites.", "alrFavouritesActionExist": "This action is already in favourites.", "alrFavouriteRenamed": "The new name has been changed in favourites list.", "autoFit": "AutoFit", "passwordIsShort": "Password is too short.", "changeAttrComplCases": "Change attributes of completed cases", "iterateOverVars": "Iterate over variables", "nrOfDecimalDigits": "Number of decimal digits", "onlyNumbers": "Only numbers", "maxNumberOfDecimals": "Max number of decimal digits is", "alrInsertCsv": "Insert CSV file.", "addBefore": "Add before", "moveBefore": "Move before", "administration": "Administration", "ttAdministration": {"heading": "Administration", "body": ""}, "alrLogsLoadFailed": "Logs failed to load.", "logs": "Logs", "message": "Message", "useCompatibleTempl": "Use compatible template", "overwriteExistTempl": "Overwrite existing template", "addNewTempl": "Add new template", "import": "Import", "export": "Export", "confirmExportAllTempl": "Export all templates?", "confirmExportSelTempl": "Export selected template?", "newLogs": "New logs", "container": "container", "contents": "Contents", "confirmRemoveDialog": "Do you really want to remove the {{variable}}?", "allMyCases": "All my cases", "maintenanceMsg": "Scheduled <span style=\"color: {{color}};\">maintenance</span> is underway", "alrMaintenanceMsg": "Scheduled maintenance is underway, please try it later.", "alrAttachDownloadLackOfPerms": "You do not have permission to download the document or the document was not found.", "unableToConnect": "Unable to connect to server", "tryLater": "Try it later or contact the administrator.", "enableTaskDelegation": "Enable task delegation", "enableRejectTask": "Enable reject task", "confirmRejectTask": "Do you really want to reject the task?", "rejectTask": "Reject task", "delegateTask": "Delegate", "alrRejectingTask": "Rejecting task...", "alrTaskRejected": "Task was rejected.", "alrTaskRejectFailed": "Task failed to reject.", "alrTaskDelegating": "Delegating the task...", "alrTaskDelegated": "The task was delegated on user:", "alrFailedTaskDelegate": "Task delegation failed.", "delegateOnUser": "Delegate on user", "plnAssignmentCond": "If the field \"Assignments\" remains empty, a list of initiators will be created by evaluating restrictive conditions at the time of the plan execution", "alrUserFiltersSettingsFailed": "Saving of user filters setting failed.", "general": "General", "alrUserPhotoLoadFailed": "User photo loading failed.", "publicDynTable": "Public dynamic table", "isFullIndexed": "In search", "datetimeIndexed": "Indexed on", "toIndex": "To index", "toReindex": "To reindex", "solverChanged": "Task owner changed in {{count}} tasks", "changeSolverFailed": "Task owner change failed.", "alrTikaParsingFailed": "An error occurred while parsing the document.", "alrIndexingFailed": "Document indexing failed.", "alrTikaNotRunning": "Service for parsing documents not available.", "alrIndexingServiceNotRunning": "Indexing service not available.", "alrFulltextNotSet": "Fulltext was not set.", "asc": "Ascending", "desc": "Descending", "restore": "Rest<PERSON>", "alrLogosLoadFailed": "Logos loading failed.", "indexedDocsCount": "in {{count}} documents", "alrIndexedCountLoadFailed": "Fulltext search is not currently available.", "searchAll": "Search all", "searchActual": "Only actual", "runIndexing": "Run indexing", "alrDocumentIndexing": "Indexing document...", "alrDocumentIndexed": "Document was indexed and can be found by searching.", "alrDocumentIndexedWithMinMetadata": "Document was indexed.", "alrDocumentIndexingFailed": "Indexing document failed.", "changingUserProfileForbidden": "Changing user profile is forbidden.", "uploadingPhotoForbidden": "Uploading photo is forbidden.", "alrValidationCalcError": "Error validating calculations", "maintenance": "Maintenance", "maintenanceActivate": "Activate maintenance", "maintenanceInfoText": "Start and end will be displayed to users after maintenance activation.", "maintenanceMode": "Maintenance mode", "alrAvailableCalcFailed": "Available calculations failed to load.", "alrFillDataForSearch": "Please fill in the search parameters.", "youAreHere": "You are here", "invalidDate": "Invalid date format", "alrInvalidFileFormat": "Invalid file format.", "alrEnter3characters": "Please enter at least three characters.", "changeCaseOwner": "Change case owner", "actualCaseOwner": "Actual case owner", "newCaseOwner": "New case owner", "alrCaseOwnerChanged": "Case owner has been changed.", "alrChangeCaseOwnerFailed": "Case owner change failed.", "alrCsvSaving": "Saving the CSV file...", "alrCsvSaveFailed": "The CSV file uploading failed.", "alrCsvSaved": "The CSV file was uploaded.", "allTemplates": "All templates", "specifyCaseIds": "Specify case IDs", "caseIds": "Case IDs", "caseId": "Case ID", "separBySemicolon": "separated by semicolon", "alrAddCaseIds": "Please specify case IDs", "headers": "Headers", "header": "Header", "defaultHeaderName": "Default header name", "headerName": "Header name", "addHeader": "Add header", "editHeader": "Edit header", "templateName": "Template name", "rolesExecRightsText": "Assign roles which will be able to initiate cases", "orgUnitsExecRightsText": "Assign organizational units which will be able to initiate cases", "selectedHeader": "selected header", "alrHeaderDeleted": "Header has been deleted!", "alrHeaderDeleteFailed": "Header deletion failed.", "alrHeaderSaveFailed": "Header saving failed.", "alrHeaderSaved": "Head<PERSON> has been saved.", "alrHeadersLoadFailed": "Loading header data failed.", "identificator": "Header code", "includeDataSimilarProcesses": "Include data of all similar processes", "confirmCopyCv": "Do you really want to copy the selected overview?", "alrCreatingCopyCv": "Creating copy of the overview...", "alrCvCopied": "The overview has been copied.", "alrCopyCvFailed": "Creating copy of the overview failed.", "copyingTemplate": "Copying a template", "alrCheckTempImportFailed": "Check of template import failed.", "warnings": "Warnings", "missingEventsFiles": "Missing events files", "missingEventsFilesText": "The {{- file}} file was not found in the {{- event}} event", "printsOfTemplates": "Prints of templates", "printsOfTemplatesText": "Please pay attention to printing {{- print}} from the {{- template}} template. Value: {{- value}}", "dupliciteTaskNames": "Duplicate task names", "dupliciteTaskNamesText": "The {{- template}} template contains multiple tasks with the same name {{- task}} {{- taskId}}, it will cause the breakdown of the links!", "dynTableUsed": "Dynamic table used", "suspiciousCalc": "Suspicious calculations", "suspiciousCalcText": "Possible missing role/organization/user in calculation {{- calc}}", "missingEvents": "Missing events", "missingEvent": "Missing event", "wrongMappingDomains": "Wrong mapping of domains", "wrongMappingDomainsText": "Description of the {{- task}} task from the {{- template}} template contains a bad domain name, the current domain is {{- actDom}}", "taskDescription": "Task description", "eventsUrl": "Events URL", "eventsUrlText": "Possible error in the {{- event}} event URL, current domain is {{- actDom}}", "param": "Parameter", "alrServiceNotForTable": "Data from this service is not appropriate for viewing in the table.", "alrServiceDataFailedLoad": "Service data failed to load.", "alrServiceNoData": "The service does not contain any data.", "tableColumns": "Table columns", "datetime": "Date and time", "exactDatetime": "Exact date and time", "dashRestNoColumns": "No columns set – choose them in the container settings", "loadService": "Load service", "useCompatibleRole": "Use compatible role", "overwriteExistRole": "Overwrite existing role", "addNewRole": "Add new role", "templateImportFailed": "Template import failed.", "templateImport": "Template import", "templateImportNoData": "No data was found for importing the template.", "variableImportNoData": "No data was found for importing the variables.", "ttTemplateImport": {"heading": "Template import", "body": "A folder with the definitions of one or more templates is selected and then uploaded."}, "showUnfinishedProcesses": "Show unfinished cases", "expMaintenanceEnd": "Expected maintenance end", "alrScriptSaveFailed": "Saving script failed.", "editScript": "Edit script", "addScript": "Add script", "alrRunScript": "Launching script...", "alrScriptCompleted": "<PERSON><PERSON><PERSON> completed.", "alrFailedScriptStart": "<PERSON><PERSON><PERSON> failed to start.", "alrScriptDocsLoadFailed": "Documentation of scripts failed to load.", "alrScriptLoadFailed": "Scripts failed to load.", "switchAdminUser": "Switching admin/user", "ttSwitchAdminUser": {"heading": "Switching admin/user", "body": ""}, "ttSwitchViewport": {"heading": "Switching mobile/PC view", "body": ""}, "alrEventDataLoadFailed": "Event data failed to load.", "alrEventRuleDataLoadFailed": "Event rule data failed to load.", "cancellation": "Cancellation", "tTaskAutoCancellCaption": "The task will be automatically canceled if", "codeMirrorHelp": "Click anywhere in the editor and press Ctrl + Space to view the help.", "codeMirrorHelpJs": "For a list of all features, click <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "Add event", "editEvent": "Edit event", "term": "Term", "columnOrder": "Column order", "alrLoadEventsButtonsFailed": "Loading of buttons in table failed.", "showButtonsCol": "Show the column of actions", "button": "<PERSON><PERSON>", "enableButtonInTasks": "Show as a button in the tasks list", "alrEventDoesntExist": "Selected event does not exist.", "alrEventRuleSaveFailed": "Event rule saving failed.", "variableNames": "Variable names", "fsEvent": "event", "alrEventDeleteFailed": "Event deletion failed.", "fsRule": "rule", "alrRuleDeleteFailed": "Rule deletion failed.", "alrRuleStatusChangeFailed": "Rule status change failed.", "ruleActivateDeactivateQ": "Do you really want to change the status of the rule?", "docUploadedPrivate": "The document will be uploaded as private", "fileOwner": "File owner", "planOk": "Ok", "userNotAuthToStartTempl": "User is not authorized to start a case by this template", "planStartDate": "Start date", "useOnlyFutureDates": "Only future dates", "alrGenerateHtmlFailed": "Generating HTML failed.", "alrNoPermsToAddNoteInVice": "You do not have permission to add a note while substituting.", "alrNoPermsToAddDocInVice": "You do not have permission to add a document while substituting.", "current": "Current", "indexation": "Indexation", "attemptToRestoreConnection": "Attempt to restore the connection in", "loginWillExpire": "<PERSON>gin will expire in", "unsavedDataWillBeLost": "Unsaved data will be lost.", "alrFileSaveLikeAttachViceError": "You do not have permission to save a print as a case document while substituting!", "alrFileSaveLikeAttachStoreError": "Saving print as a case document failed.", "useCompatibleEvent": "Use compatible event", "overwriteExistEvent": "Overwrite existing event", "addNewEvent": "Add new event", "useCompatibleUser": "Use compatible user", "overwriteExistUser": "Overwrite existing user", "addNewUser": "Add new user", "useCompatibleUnit": "Use compatible org. unit", "overwriteExistUnit": "Overwrite existing org. unit", "addNewUnit": "Add new org. unit", "addNewDynTable": "Add new dyn. table", "useCompatibleDynTable": "Use compatible dyn. table", "enterDiffNameRoot": "Please enter a different name than <PERSON>.", "ttTemplatesExport": {"heading": "Export templates", "body": "Export selected templates to a folder.  It is possible to choose the name and location of the exported file.  The action is applied to the selected template.  Select a template by clicking on it in the table of case templates."}, "ttTemplatesExportAll": {"heading": "Export all templates", "body": "Export all currently displayed templates to a file.  It is possible to choose the name and location of the exported file.  The selection of templates may be restricted by setting suitable filter conditions."}, "exportAll": "Export all", "noTemplatesToExport": "No templates to export.", "skip": "<PERSON><PERSON>", "ttSkipTemplate": {"heading": "Skip the template", "body": "Skips import of the current template and displays the next."}, "alrInvalidImportData": "Invalid import data", "alrUsersNotLoaded": "Users failed to load.", "caseOverview": "Case overview", "alrRolesNotLoaded": "Roles failed to load.", "changeLang": "Change language", "reactivatesPlan": "reactivates the plan", "alrOrgUnitsNotLoaded": "Org. units failed to load.", "refreshPage": "Refresh page", "stayLogged": "Stay logged", "showTime": "Show timestamp in overviews", "managerIn": "manager in {{orgUnit}}", "usageStats": "Usage statistics", "month": "Month", "alrUsageStatsLoadFailed": "Unable to load usage statistics.", "accessLog": "Access log", "durationInMs": "Duration (ms)", "task": "Task", "operation": "Operation", "active_users": "Number of active users", "active_template_processes": "Number of active processes", "active_headers": "Number of active headers", "active_users_able_to_create_a_process": "Number of active users, who can run a process", "users_that_solved_a_task": "Number of users, who resolved at least one task", "solvers_or_can_create_a_process": "Number of task owners, who resolved a task or can run a process", "mobile_app_paired_users": "Number of paired mobile app users", "calculationsLogs": "Calculations logs", "translatedScript": "Translated script", "originalScript": "Original script", "tskId": "Task ID", "alrCalculationsDocsLoadFailed": "Unable to load calculations docs.", "alrCalculationsValidationFailed": "Calculations validation failed.", "linkPriority": "Link priority", "dateFormat": "MM/DD/YYYY", "alrConvertErrorJsonNeon": "Error while converting json -> neon.", "alrInvalidData": "Invalid data.", "sharedVar": "Shared variable", "guide": "Guide", "guideFs": "guide", "guides": "Guides", "alrGuidesLoadFailed": "Guides failed to load.", "language": "Language", "default": "<PERSON><PERSON><PERSON>", "next": "Next", "previous": "Previous", "targetElementNotFound": "Target element not found", "documentation": "Documentation", "matchesRegular": "Does not match the rule", "secondAllowedValues": "allowed values; 0 is the top of the minute", "everySec": "every second", "listOfSec": "a list of seconds; ie. 0,30 would be the 0 AND 30th seconds", "rangeOfSec": "a range of seconds; ie. 0–5 would be seconds 0, 1, 2, 3, 4, and 5 (you can also specify a list of ranges 0–5,30–35)", "slashSec": "step values will skip the specified number within a range; ie */5 is every 5 seconds, and 0–30/2 is every 2 seconds between 0 and 30 seconds", "minuteAllowedValues": "allowed values; 0 is the top of the hour", "everyMin": "every minute", "listOfMin": "a list of minutes; ie. 0,30 would be the 0 AND 30th minutes", "rangeOfMin": "a range of minutes; ie. 0–5 would be minutes 0, 1, 2, 3, 4, and 5 (you can also specify a list of ranges 0–5,30–35)", "slashMin": "step values will skip the specified number within a range; ie */5 is every 5 minutes, and 0–30/2 is every 2 minutes between 0 and 30 minutes", "hourAllowedValues": "allowed values; 0 is midnight", "everyHour": "every hour", "listOfHour": "a list of hours; ie. 0,12 would be the 0 AND 12th hours", "rangeOfHour": "a range of hours; ie. 19–23 would be hours 19, 20, 21, 22, and 23 (you can also specify a list of ranges 0–5,12–16)", "slashHour": "step values will skip the specified number within a range; ie */4 is every 4 hours, and 0–20/2 is every 2 hours between 0 and the 20th hour", "dayAllowedValues": "allowed values", "everyMonthDay": "every day of the month", "listOfDay": "a list of days; ie. 1,15 would be the 1st AND 15th day of the month", "rangeOfDay": "a range of days; ie. 1–5 would be days 1, 2, 3, 4, and 5 (you can also specify a list of ranges 1–5,14–30)", "slashDay": "step values will skip the specified number within a range; ie */4 is every 4 days, and 1–20/2 is every 2 days between 1st and the 20th day of the month", "allowedValues": "allowed values", "everyMonth": "every month", "listOfMonth": "a list of months; ie. 1,6 would be the january AND june", "rangeOfMonth": "a range of months; ie. 1–3 would be january, february, and march (you can also specify a list of ranges 1–4,8–12)", "slashMonth": "step values will skip the specified number within a range; ie */4 is every 4 months, and 1–8/2 is every 2 months between january and august", "weekAllowedValues": "allowed values; 0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday", "everyWeekDay": "every day of the week", "listOfWeekDay": "a list of days; ie. 1,5 would be the monday AND friday", "rangeOfWeekDay": "a range of days; ie. 1–5 would be mon, tue, wed, thu, and fri (you can also specify a list of ranges 0–2,4–6)", "slashWeek": "step values will skip the specified number within a range; ie */4 is every 4 days, and 1–5/2 is every 2 days between monday and friday", "contrab": "Cron {{variable}} field", "cSecond": "second", "cMinute": "minute", "cHour": "hour", "cDay": "day", "cMonth": "month", "cWeekDay": "day of week", "seconds": "seconds", "minutes": "minutes", "hours": "hours", "days": "days", "weeks": "weeks", "socketOk": "The table has the latest data", "socketBroken": "Restore connection for ongoing data update", "newTask": "New task", "report": "Report", "ttCaseReport": {"heading": "Report", "body": ""}, "usersRights": "User rights", "visPerRole": "Visibility per role", "manualEvents": "Manual events", "noTasks": "No new tasks", "emptyFavs": "Favorites list is empty", "crons": "Crons", "cronsHistory": "<PERSON><PERSON>' history", "redirBefStart": "Before start, redirect to", "lastRun": "Last run", "nextRun": "Next run", "syntax": "Syntax", "alias": "<PERSON><PERSON>", "stop": "Stop", "restart": "<PERSON><PERSON>", "restartCronProcess": "Restart process context", "ttRestartCron": {"heading": "<PERSON><PERSON> cron", "body": ""}, "ttRestartCronProcess": {"heading": "Restart process", "body": ""}, "ttResetCron": {"heading": "Reset cron", "body": ""}, "ttRunCron": {"heading": "<PERSON> cron", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "The cron was stopped.", "alrCronStopFailed": "The cron stop request failed.", "alrCronRunning": "The cron was started.", "alrCronRunFailed": "<PERSON><PERSON> run failed.", "alrCronReset": "The cron has been reset to default.", "alrCronResetFailed": "Cron reset failed.", "alrCronRestart": "The cron has been restarted.", "alrCronRestartFailed": "The request to restart the cron failed.", "alrCronUpdated": "The cron has been successfully saved.", "alrCronUpdateFailed": "The cron update request failed.", "confirmRunCronDialog": "Are you sure you want to run the selected cron?", "confirmStopCronDialog": "Are you sure you want to stop the selected cron?", "confirmResetCronDialog": "Are you sure you want to reset cron to factory settings?", "confirmRestartCronDialog": "Are you sure you want to restart the selected cron?", "confirmUpdateCronDialog": "Are you sure you want to change the cron status?", "alrProcessRestart": "Cron process has been restarted!", "alrProcessRestartFailed": "The request to restart the process failed.", "confirmRestartProcessDialog": "Are you sure you want to restart the entire cron process? Beware, complete restart of all crons and the entire context will happen.", "cronParams": "Parameters", "alrPresetLogFiltersLoadFailed": "Preset log filters failed to load.", "timeRange": "Time range", "presetFilters": "Preset filters", "params": "Parameters", "authentication": "Authentication", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Parameter name", "authMethod": "authentication method", "taskAlreadyEdited": "The task is already being edited by another user.", "taskEditedByAnotherUser": "Another user started to edit the task.", "tempAlreadyEdited": "The template is already being edited by another user.", "tempEditedByAnotherUser": "Another user started to edit the template.", "test": "Test", "notInRightormat": "Invalid format", "ttTableExportExcel": {"heading": "Export table", "body": "Exports the table to the xlsx file"}, "ttTableExportCsv": {"heading": "Export table", "body": "Exports the table to the csv file"}, "searchInSuspended": "Search also in suspended cases", "alrScriptDocsFailed": "Unable to save the script documentation.", "currentlyRunning": "Currently running", "onStart": "On start", "onEnd": "On end", "onHand": "By hand", "onRecalc": "On recalculation", "onPull": "Before taking over", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "replyRecipient": "Reply recipient", "bcRecipient": "Blind copy recipient", "copyRecipient": "Copy recipient", "emptyHe": "Empty", "archivedLogs": "Archived logs", "basicMode": "Basic mode", "expertMode": "Expert mode", "ttBasicMode": {"heading": "Basic mode", "body": "Hides some items or options in the form."}, "ttExpertMode": {"heading": "Expert mode", "body": "Displays hidden items or options in the form."}, "helpOverviewFolder": "You can include the overview in the directory structure by using slashes.<br /><i>(e.g. Invoices/All Received Invoices)</i>", "helpOverviewIncludeSimilar": "If you select, cases from other headers of a template will also be displayed.", "helpOverviewSysVars": "Fields marked with (sys) are system fields that are part of each process.", "customization": "Customization", "elementColor": "Element color", "fontColor": "Font color", "fontSize": "Font size", "bold": "Bold", "cursive": "Italics", "off": "Off", "toPlan": "Plan", "alrMaintenanceComing": "At {{time}} scheduled system maintenance will begin. Please save your work.", "timeoutHMS": "Timeout: (hh:mm:ss)", "eventw": "The \"{{task}}\" task from the \"{{template}}\" template waits for this event", "waitsForEventTip": "The case is waiting for an event: \"{{event}}\"", "copyToMultiinstances": "Copy to multi-instances", "showAsPreview": "Show a preview", "alrPreviewAttachmentsFailed": "Showing a preview failed", "alrPreviewAttachmentsWrongFormat": "Showing a preview failed – unsupported file format", "previewNotAvailable": "Preview of the document is not possible due to the document type.", "configuration": "Configuration", "values": "Values", "defaultValues": "Default values", "ttSubscribeCv": {"heading": "Subscribe overview", "body": "The selected overview will be emailed to you every weekday at the set time."}, "subscribe": "Subscribe", "time": "Time", "externalLang": "External language", "hdrStatusQ": "Do you really want to change the status of the header?", "small": "Small", "medium": "Medium", "large": "Large", "alrTemplTsksLoadFailed": "Template tasks failed to load.", "applyInTasks": "Apply in tasks", "caseStatuses": "Case statuses", "statuses": "Statuses", "Manuals": "Manuals", "forReading": "For reading", "forReadWrite": "For reading and writing", "addVersion": "New version", "size": "Size", "prevWorkDay": "Previous work day", "mandatoryVar": "Required variable", "emptyRequiredVarMessage": "Oops, empty required variable", "ttCreateTempVersion": {"heading": "Create a new version of the template", "body": ""}, "version": "Version", "alrTempVersionsLoadFailed": "Template versions failed to load.", "alrChangeTempVersionFailed": "Failed to change template version.", "alrCreateTempVersionFailed": "Creating a new version of the template failed.", "confirmCreateTempVersion": "Are you sure you want to create a new version of the template?", "applyInAllTasks": "Apply in all tasks", "duration": "Duration", "alrDynConditionsFailed": "Unable to complete the task. Please try refreshing the page, or contact the Administrator or Helpdesk.", "caseActivation": "Case activation", "average": "Average", "performanceLogs": "Performance logs", "displayingOverview": "Displaying the overview", "taskSolve": "Completing the task", "displayingCO": "Displaying the CASE OVERVIEW", "printCreation": "Print creation", "entityId": "Entity ID", "copyTask": "Copy task", "checkProcessCompletion": "Process completion check", "findingSolver": "Finding a task owner", "publicFiles": "Public files", "usage": "Usage", "serviceConsole": "Service console", "selectAll": "Select all", "logos": "Logos", "overviewWithTasks": "Overview with tasks", "printIsReady": "Print is ready", "alrChangelogLoadFailed": "Loading changelog failed.", "inJs": "In script", "ttCopyDtDefinition": {"heading": "Copy table definition", "body": "Copies the definition of the selected dynamic table."}, "confirmCopyTableDefinition": "Do you really want to copy the table definition?", "alrCopying": "Copying...", "alrCopyFailed": "Failed to copy.", "fallback": "Fallback", "syncEnabled": "Synchronization", "systemGuideNote": "The content of the System Guide cannot be changed. To view other content, make the System Guide inactive and copy its content to a new Guide.", "alrAnotherUserLogged": "Another user is logged in in another window!", "userLocked": "User is locked", "visInternalUserOnly": "Visible only to internal users", "showSelectedOnly": "Show selected only", "clickToSelect": "Click to select", "restrictRoleAssignment": "Restrict role assignment for role", "restrictions": "Restrictions", "restrictTableHandling": "Restrict table handling", "toRole": "To role", "inCalcToHeader": "In calculations to header", "loginBtnColor": "Login button color", "certificates": "Certificates", "certificate": "Certificate", "certificateVar": "certificate", "tspSources": "Timestamps", "tspSource": "Timestamp", "confirmExpandDynRowsNewAssignments": "Attention, new assignment! Variables do not have axes set. Do you want to stretch all dynamic rows?", "confirmExpandDynRows": "Are you sure you want to stretch all dynamic rows?", "expandDynRows": "Stretch dynamic rows", "visible": "Visible", "cvcDbColumn": "Source column", "cvTableSource": "Source table", "uploadedFromFile": "Uploaded from file", "appStatus": "App status", "loadAll": "Load all", "ignoredUsers": "Ignored users", "copyRolesFrom": "Copy roles from", "disableFrontendStyles": "Do not apply automatic styles", "activate": "Activate", "confirmActivateCase": "Do you really want to activate the case?", "alrLackOfPerms": "Lack of permissions.", "alrSending": "Sending...", "sequences": "Sequences", "seqName": "Sequence name", "seqId": "Sequence ID", "seqLastRead": "Last read", "ttCopyRole": {"heading": "Copy role", "body": "Creates a copy of the selected role."}, "fromCase": "From case", "includingData": "Including data", "updateInstances": "Update instance variables", "addNewCalcScript": "Add new script", "useCompatibleCalcScript": "Use compatible script", "choose": "<PERSON><PERSON>", "valueChange": "Value change", "externalSource": "User source", "reports": "Reports", "confirmCopyReport": "Are you sure you want to copy the selected report?", "graphs": "Graphs", "aggregation": "Aggregation", "graphNew": "Graph – new", "confirmCopyGraph": "Are you sure you want to copy the selected chart?", "alrCopyGraphFailed": "Failed to copy the graph.", "label": "Label", "pie": "Pie", "line": "Line", "dot": "Dots", "bar": "Bar", "barGroups": "Bar – groups", "alrFailedGraphData": "Graph failed to load.", "graphSetSharing": "Set graph sharing for each user group", "alrGraphPointsLoadFailed": "Graph points loading failed.", "alrGraphNotFound": "Graph not found.", "graphData": "Graph data", "pointsData": "Graph points", "alrGraphSaveFailed": "The graph saving failed!", "graphPoint": "Graph point", "noOrder": "No sorting", "refreshGraph": "Refresh graph", "viewSwitcher": "Global filter", "axisXglobalFilter": "Axis X – global filter", "axisXgroups": "Axis X – groups", "axisXdata": "Axis X – data", "axisYvalues": "Axis Y – values", "axisYcolors": "Axis Y – colors", "hrAgenda": "HR agenda", "userChange": "User change", "newUser": "New user", "usersCount": "Users count", "confirmChangeUser": "Are you sure you want to change user?", "businessVariable": "Business variable", "casesCount": "Cases count", "selected": "Selected", "selectedOnly": "Selected only", "addCaseRightNewUser": "Add access to the case", "visFromTaskToPull": "Visibility from a task to pull", "toChangeConfigInfo": "To change, delete the value from the local.js file", "clickToChange": "Click to change", "currentValue": "Current value", "sign": "Sign", "validationProtocols": "Validations", "plannedEvents": "Events", "elArchiv": "E-archive", "deletedDocs": "Deleted", "signatures": "Signatures", "ttArchive": {"heading": "Electronic archive", "body": ""}, "ttAddToZea": {"heading": "Add to electronic archive", "body": ""}, "ttRemoveFromZea": {"heading": "Remove from electronic archive", "body": ""}, "ttZeaInfo": {"heading": "Validation", "body": ""}, "ttSignZea": {"heading": "Externally sign", "body": ""}, "addToZea": "Add to e-archive", "removeFromZea": "Remove from e-archive", "reTimestampAfter": "Validity of generated timestamp (days)", "alrLoadFailed": "Loading failed.", "replace": "Replace", "expireAt": "Will expire", "result": "Validation result", "validatedAt": "Validated at", "refType": "Object", "eventType": "Type of action", "errorMessage": "Error message", "errorTimestamp": "Error timestamp", "errorCount": "Error count", "inFuture": "In future", "path": "Path to signature file", "signedAt": "Creation of signature", "dropZoneZeaCertificate": "Drop an certificate here, or click to select a file to upload.", "authType": "Authentication type", "basic": "With name and password", "byCert": "By certificate", "alrMissingCertFile": "Upload the certificate, please.", "replaceTo": "Replace to", "autoReTimestamp": "Auto timestamp", "validate": "Validate", "lastUse": "Last generated", "createdAt": "Created at", "updatedAt": "Updated at", "certificateId": "Certificate ID", "expectedCreationTime": "Will be added", "nextTSPSourceId": "Next timestamp ID", "reTimestampAt": "Next timestamp", "timestampedAt": "Last timestamp", "level": "Level", "signatureB": "Basic signature", "signatureT": "Signature with a timestamp", "signatureLt": "Signature with long term data certificates", "signatureLta": "Signature with long term data and archive timestamp", "packaging": "Packaging", "enveloped": "Enveloped", "enveloping": "Enveloping", "detached": "Detached", "algorithm": "Algorithm", "uploadAsRevision": "Upload as revision", "externalDisable": "Active just for batch signatures", "addToDms": "Add to DMS", "autoConvert": "Automatically convert", "format": "Format", "signatureType": "Signature type", "signature": "Signature", "custom": "Custom", "batchSignDisabled": "Sign disabled", "tasId": "Document ID", "hashValue": "Hash value", "hashType": "Hash function type", "confirmAddToArchive": "Do you really want to add to the archive?", "independentSignature": "Independent signature", "independentValidation": "Independent validation", "failureTrue": "With error", "failureFalse": "Without errors", "confirmValidateDialog": "Do you really want to validate the signature?", "confirmRestartDialog": "Do you really want to reset event errors?", "verificationResult": "Verification result", "integrityMaintained": "Integrity maintained", "signatureFormat": "Signature format", "internalTimestampsList": "List of internal time stamps", "signers": "Signers", "exhibitedBy": "Exhibited by", "signedBy": "Signed by", "validFrom": "<PERSON>id from", "validUntil": "Valid until", "signitureType": "Signature type", "signatureQualification": "Signature qualification", "signatureNoTimestamps": "Signature does not contain timestamps", "electronicSignature": "Electronic signature", "electronicSeal": "Electronic seal", "webSiteAuthentication": "Web site authentication", "QCP-n": "QCP-n: certificate policy for EU qualified certificates issued to natural persons", "QCP-l": "QCP-l: certificate policy for EU qualified certificates issued to legal persons", "QCP-n-qscd": "QCP-n-qscd: certificate policy for EU qualified certificates issued to natural persons with private key related to the certified public key in a QSCD", "QCP-l-qscd": "QCP-l-qscd: certificate policy for EU qualified certificates issued to legal persons with private key related to the certified public key in a QSCD", "QCP-w": "QCP-w: certificate policy for EU qualified website authentication certificates", "formOfReStamping": "Form of re-stamping", "individually": "Individually", "archiveAsPdf": "Archive as PDF", "couldNotBeVerified": "Could not be verified", "uses": "Nr. of uses", "countOfSignedDocuments": "Count of signed documents", "batchSignature": "Batch signature", "standaloneSign": "Individual signature", "validateSignature": "Signature validation", "validateDoc": "Document validation", "containsSignature": "Contains signature", "reStamping": "Re-stamping", "individualSignatures": "Individual signatures", "signatureLevel": "Signature level", "simpleReport": "Simple report", "detailedReport": "Detailed report", "diagnosticReport": "Diagnostic report", "etsiReport": "ETSI report", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "Failed", "INDETERMINATE": "Indeterminate", "FORMAT_FAILURE": "Signature does not comply with one of the basic standards", "HASH_FAILURE": "The signed data object's hash does not match the hash in the signature", "SIG_CRYPTO_FAILURE": "The signature could not be verified with the signer's public key", "REVOKED": "The signature certificate has been revoked and there is evidence that the signature has been created after the revocation", "SIG_CONSTRAINTS_FAILURE": "One or more signature attributes do not match the validation rules", "CHAIN_CONSTRAINTS_FAILURE": "The certificate chain used in the validation process does not comply with the certificate validation rules", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "The set of certificates available for string verification caused an error for an unspecified reason", "CRYPTO_CONSTRAINTS_FAILURE": "One of the signature verification algorithms is below the required cryptographic security level and the signature was acquired after the algorithm lifetime", "EXPIRED": "The signature was created after the signature certificate expired", "NOT_YET_VALID": "The signing time lies before the issuance date of the signing certificate", "POLICY_PROCESSING_ERROR": "The validation policy file could not be processed", "SIGNATURE_POLICY_NOT_AVAILABLE": "The electronic document containing details about the validation policy is not available", "TIMESTAMP_ORDER_FAILURE": "Restrictions in signature timestamp order are not respected", "NO_SIGNING_CERTIFICATE_FOUND": "The signing certificate cannot be identified", "NO_CERTIFICATE_CHAIN_FOUND": "No certificate chain was found for the identified signature certificate", "REVOKED_NO_POE": "The signing certificate was revoked at the validation date/time. However, the signature verification algorithm cannot detect that the signature time is before or after the revocation period", "REVOKED_CA_NO_POE": "At least one certificate chain was found, but a temporary CA certificate was revoked", "OUT_OF_BOUNDS_NOT_REVOKED": "The signing certificate is expired or not yet valid at the validation date/time and the Signature Validation Algorithm cannot ascertain that the signing time lies within the validity interval of the signing certificate. The certificate is known not to be revoked.", "OUT_OF_BOUNDS_NO_POE": "The signing certificate has expired or is not yet valid at the verification date/time", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "One of the signature verification algorithms is below the required cryptographic security level and there is no proof that it was produced before the algorithm/key was considered secure", "NO_POE": "There is no proof that the signed object was created before a compromising event", "TRY_LATER": "Not all validation rules can be met with the available information, but it may be possible to do so with additional revocation information that will be available later", "SIGNED_DATA_NOT_FOUND": "Signed data cannot be obtained", "GENERIC": "Other reason", "signatureFile": "Signature file", "validityDays": "Validity days", "qualifiedHe": "Qualified", "qualifiedIt": "Qualified", "unqualifiedHe": "Unqualified", "unqualifiedIt": "Unqualified", "timeValid": "Time valid", "reason": "Reason", "inTime": "in time", "certificateQualification": "Certificate qualification", "guaranteedHe": "<PERSON><PERSON><PERSON><PERSON>", "guaranteedIt": "<PERSON><PERSON><PERSON><PERSON>", "fromQualifiedCert": "From qualified cert.", "basedOnQualifiedCertHe": "Based on qualified certificate", "createdByQualifiedResHe": "Created by qualified resource", "basedOnQualifiedCertIt": "Based on qualified certificate", "createdByQualifiedResIt": "Created by qualified resource", "qualification": "Qualification", "confirmRemoveFromZeaDialog": "Do you really want to remove the {{variable}} from the electronic archive?", "noValidationReports": "No validation reports", "noSignatures": "No individual signatures", "isHigherOrEqualThan": "Must be greater than or equal to", "isInZea": "In e-archive", "startStamping": "Start stamping", "reTimestampAfterMinutes": "minutes", "reTimestampAfterDays": "days", "reTimestampAfterAll": "Validity of generated timestamp", "refId": "Object ID", "docWithoutAutoTimestampInfo": "The document will be signed once, without automatic timestamp insertion.", "validationReports": "Validation history", "docPath": "Path to document", "addToArchiveInvalidSignatureError": "The file could not be archived because it contains a signature that cannot be verified.", "signImmediately": "Sign immediately", "replaceInConfiguration": "Replace in configuration", "cancel": "Cancel", "bulk": "Bulk", "bulkCompletion": "Bulk completion", "enableBulkCompletion": "Enable bulk completion", "confirmCompleteTasks": "Do you really want to complete the tasks?", "plannedMaintenance": "Planned maintenance", "notSpecified": "Not specified", "bulkCompletionVars": "Bulk completion variables", "alrBulkCompletionMultiTypeErr": "Only tasks of the same type can be completed in bulk, you can use a filter.", "notifications": "Notifications", "alrTskAlreadyTakenSomeone": "Someone else has already taken on the task.", "alrTskAlreadyTaken": "The task has already been taken over.", "downloadBpmn": "download BPMN diagram", "downloadSvg": "download as SVG image", "displayForm": "Display type", "selectedPreview": "preview is displayed", "fixedHeight": "Fixed height (px)", "lastVersion": "Latest version", "separatedPreview": "Separated preview", "defaultZoom": "Default zoom", "fixedPosition": "Fixed position", "percentInterval": "Please fill integer between 0–5", "notPositiveNumber": "Please fill only positive numbers", "zoomDown": "Zoom down", "zoomUp": "Zoom up", "rotate": "Rotate", "logTooBig": "Log is too big to be rendered.", "downloadLog": "Download log", "confirmCopyCron": "Do you really want to copy the selected cron?", "ttCopyCron": {"heading": "Copy cron", "body": ""}, "onlyWorkingDays": "Only working days", "datesDisabled": "Disable dates", "useView": "Use View", "dateWithoutTime": "Date without time", "timezone": "Time zone", "roleRestriction": "Role restriction", "headerRestriction": "Header restriction", "ttSwitchDarkmode": {"heading": "Switching light/dark mode", "body": ""}, "advancedEditor": "Advanced editor", "externalId": "External ID", "passwordValidationMin": "Password is too short. (minimum length: {{count}})", "passwordValidationMax": "Password is too long. (maximum length: {{count}})", "passwordValidationUppercase": "Password must contain an uppercase letter. {{atLeast}}", "passwordValidationLowercase": "Password must contain a lowercase letter. {{atLeast}}", "passwordValidationSymbols": "Password must contain a symbol. {{atLeast}}", "passwordValidationDigits": "Password must contain a number. {{atLeast}}", "passwordValidationLetters": "Password must contain a letter. {{atLeast}}", "atLeast": "At least", "passwordValidationServiceErr": "Password cannot be changed at this time.", "enableTasksHandoverRole": "Always allow tasks handover and events running to users of this role", "shredded": "Shredded", "shredableVar": "Shredable variable", "shredDocuments": "Shred documents", "shredInDays": "Shred in (days)", "fromBeginningOrendOfCase": "From the beginning/end of the case", "shredding": "Shredding", "addColumn": "Add column", "unsupportedBrowser": "You are opening TeamAssistant in an unsupported Internet Explorer browser, some features may not be available.", "ingoreProcessRights": "Ignoring case rights", "cvHelpIngoreProcessRights": "The Overview always shows all cases, regardless of rights", "upperLabel": "Place variable just under its name", "darkMode": "Dark mode", "completedTasks": "Completed tasks", "permissions": "Permissions", "caseVisibility": "Case visibility", "visPerOrg": "Visibility per org. unit", "entity": "Entity", "staticRight": "Static right", "dynamicRight": "Dynamic right", "treeNodesAll": "All", "treeNodesMy": "My", "activeQueries": "Active queries", "query": "Query", "confirmCancelQuery": "Are you sure you want to cancel the query?", "alrQueryNotFound": "The query was no longer found.", "completeAgenda": "Complete agenda", "lockedBusinessUsers": "Locked business users", "structuredList": "Structured list", "ttCompetences": {"heading": "Competency management", "body": ""}, "competences": "Competences", "competence": "Competence", "competenceDelVar": "competence", "addCompetence": "Add competence", "regularExpression": "Regular expression", "generationStatus": "Generation status", "source": "Source", "historical": "Historical", "external": "External", "nextDay": "next day", "embeddedVideoNotSupported": "Sorry, your browser doesn't support embedded videos.", "alrSendingTestMailFailed": "Test e-mail sending failed.", "sent": "Sent.", "mainColorEmail": "E-mail main color", "confirmResetColors": "Are you sure you want to reset the colors?", "regularExpressions": "Regular expressions", "confirmDeleteLogo": "Are you sure you want to delete logo?", "logoForLightTheme": "Logo for light mode", "logoForDarkTheme": "Logo for dark mode", "loginLogoLightTheme": "Login screen logo (light mode)", "loginLogoDarkTheme": "Login screen logo (dark mode)", "competenceRegexHelper": "<ul><li>% can be used as N of any characters</b> (equivalent *)</li><li><b>_</b> can be used as one arbitrary character (equivalent .)</li><li>You can use <b>^</b> to escape these special characters (equivalent \\)</li></ul>", "headerFont": "Header font", "evenRow": "Even row", "logo": "Logo", "widthForLogo": "Width for logo", "monthStart": "Beginning of the month", "monthEnd": "End of the month", "ttFavouriteType": "GET opens the link. POST sends a command: for example, when creating a case, where the template header id is sent in the request body (save to favourites via New Case).", "confirmEmptyMultiinstanceVariable": "Are you sure this multi-instance does not require a variable to iterate over?", "ttMenuPreview": "Menu configuration by user roles (more significant roles also see buttons for less significant roles). The New Case and Dashboard buttons are unchanged.", "menuPreview": "<PERSON><PERSON> preview for the selected role", "confirmResetMenu": "Are you sure you want to reset the menu?", "alrFailedTasMenu": "TAS menu configuration failed to load!", "security": "Security", "userRestrictions": "User restrictions (show)", "userRestrictionsProcesses": "Ignore user restrictions on tasks", "roleRestrictions": "Role restrictions (show)", "orgUnitRestrictions": "Org. unit restrictions (show)", "everyone": "Everyone", "colleaguesOnly": "Colleagues only", "directSubordinates": "Direct subordinates", "allSubordinates": "All subordinates", "none": "None", "generalDocument": "General document", "competenceRule": "Competence rule", "competenceRules": "Competence rules", "ruleName": "Rule name", "ttUseCompetenceRule": {"heading": "Apply the rule", "body": "Creates an competence according to the selected rule"}, "competenceText": "Competence text", "competenceName": "Competence name", "competenceReadOnlyInfo": "The competence created from a rule cannot be modified", "xmlProcessImport": "XML process import", "ttWidthForLogo": "Set the width for logo and then insert a logo. It is not possible to change the width for an already inserted or default logo.", "openCase": "Open case", "importHistory": "Import history", "plannedImports": "Planned imports", "filePath": "File path", "cronId": "Cron ID", "taskResult": "Task result", "xmlFileSize": "XML file size", "attachmentSize": "Attachment size", "lastEdit": "Last edit", "timeCreated": "Time created", "importId": "Import ID", "importAudit": "Import audit", "finishedImports": "Finished imports", "insertNote": "Insert note", "importXml": "Import XML", "reImportXml": "Reimport XML", "downloadXml": "Download XML", "downloadAttachment": "Download attachment", "skipXml": "Skip XML", "note": "Note", "attachmentName": "Attachment name", "importedCount": "Number of imports", "retryCount": "The number of repetitions", "batchId": "Dose ID", "copyPath": "Copy path", "cronRunId": "Run ID", "cronRun": "<PERSON><PERSON> run", "trace_id": "Trace ID", "ttMenuItemLabel": "Universal name if no translation. If a translation keyword is used, it is translated automatically. Default names: tasks, cases, reports, reports, templates, plans, users, roles, orgStructure, events, documents, elArchive, Manuals", "taskQueue": "Task queue", "dissolveQueue": "Dissolve the queue", "taskQueueInitInfo": "This action created multiple tasks to be solved. Here you can change the order of their solving or remove them from the queue completely.", "tryDarkTheme": "We noticed that you prefer dark mode. <PERSON>lick to try it in TAS.", "alrInvalidURL": "Invalid URL format.", "alrInvalidHttps": "Invalid URL format, must begin with https://", "importVariables": "Import of variables", "ttVariablesImport": {"heading": "Variables import", "body": "A folder with the definition of variables is selected and then uploaded."}, "classDiagram": "Class diagram", "createVar": "Create variable", "importObjectStates": "Import object states", "unassigned": "Unassigned", "sortVars": "Sort", "fillNames": "Fill names", "ttFillNames": {"heading": "Fill names", "body": "Fills in empty names of all new variables in format \"Class.Attribute\" and sorts all variables."}, "ttSortVars": {"heading": "Sort", "body": "Sorts variables by classes and attributes."}, "ttRestore": {"heading": "Rest<PERSON>", "body": "Restores variables to their original state when imported from a file."}, "ttAddVarToBottom": {"heading": "Add variable", "body": "Adds new variable to the bottom of the page."}, "confirmRestoreForm": "Do you really want to restore the variables to their original state?", "selectClass": "Select class", "importClassDiagram": "Import a class diagram", "continue": "Continue", "templateVars": "Template variables", "newVars": "New variables", "objectState": "Object state", "alrDynTableExists": "Dynamic table already exists!", "overwriteExistDynTable": "Overwrite existing dyn. table", "confirmCancelImport": "Are you sure you want to cancel the import?", "alrDuplicateNames": "Data contains duplicate names.", "stateVar": "State variable", "importObjectStatesToDynTables": "Import object states into dynamic tables.", "defineObjectStatesVars": "Define the variables that hold the object states.", "change": "Alter", "classAndAttr": "Class and attribute", "clearQueue": "Clear queue", "sharing": "Sharing", "data": "Data", "open": "Open", "dataSource": "Data source", "dataPoints": "Data points", "dataSeries": "Data series", "valueCol": "Value column", "aggregationCol": "Aggregation column", "timeDimension": "Time dimension", "columns": "Columns", "week": "week", "weekday": "weekday", "monthVar": "month", "overviewFilter": "Overview filter", "globalFilters": "Global filters", "filterDefinition": "Filter definition", "newFilter": "New filter", "addFilter": "Add filter", "filterOptions": "Filter options", "addOption": "Add option", "graphPreview": "Graph preview", "alrGlobalFilterDownloadFailed": "Global filters failed to download!", "alrGlobalFilterSaveFailed": "Global filters failed to save!", "filterOption": "Filter", "editFilter": "Edit filter", "fillOptionsFromVar": "Fill options from variable", "fillOptionsDynamically": "Fill options dynamically", "filterOptionsFilledDynamically": "Dynamically from variable", "dayOfMonth": "day of month", "dateVar": "date", "group": "Group", "ttDataSource": "If you want to enter each separate chart point separately, select \"Data Points\". If you want to have the points generated based on the selected dimension, select \"Data series\"", "ttDataSeriesAggregation": "Choose the type of aggregation. Allows you to create summary information from records (cases).", "ttDataSeriesColumns": "Select in turn all columns by which to create groups (aggregations) to calculate summary values.", "listOfFiltersIsEmpty": "The list of filters is empty.", "fromVariable": "From variable", "showOptionsFromCount": "Show options (out of {{count}})", "sum": "Sum", "minimum": "Minimum", "maximum": "Maximum", "statistics": "Statistics", "unfilled": "Unfilled", "globalFilterDescription": "The global filter provides chart users with options that filter the input data for the chart. All filter options can be defined in this screen.", "ttDelGraph": {"heading": "Delete graph", "body": "Deletes the selected graph."}, "ttEditGraph": {"heading": "Edit graph", "body": "Allows you to edit the selected graph."}, "ttCopyGraph": {"heading": "Copy graph", "body": "Copies the selected graph."}, "ttAddGraph": {"heading": "Add graph", "body": "Allows you to define a new graph."}, "axisXName": "X axis name", "axisYName": "Y axis name", "showValues": "Show values", "defaultOption": "Default option", "yearStart": "Beginning of the year", "yearEnd": "End of the year", "thisMonth": "This month", "lastMonth": "Last month", "thisYear": "This year", "lastYear": "Last year", "scheduledTasks": "Scheduled tasks", "scheduled": "Scheduled", "dueDateStart": "Start date", "lastRescheduled": "Last rescheduled", "reschedule": "Reschedule", "alrTasksRescheduling": "Rescheduling tasks...", "alrTasksRescheduled": "Tasks have been rescheduled.", "alrTasksRescheduleFailed": "Failed to reschedule tasks.", "onlyCurrentOrFutureDates": "Only current or future dates", "passwordValidations": "Password Policy", "readonlyConfigInfo": "The value is read-only", "alrTasksCountFailed": "Tasks counting failed.", "confirmActivateTasks": "Are you sure you want to activate the selected tasks?", "confirmSuspendTasks": "Are you sure you want to suspend the selected tasks?", "tskOffset": "Planning variable", "workWeek": "Working week", "agenda": "Agenda", "noEventsInRange": "There are no events in this range", "activitiesDesc": "Activities description", "allShort": "All", "numberOfEvents": "Number of events", "weekNumber": "week number", "cannotBeEdited": "Cannot be edited", "cannotBeMoved": "Cannot be moved", "alrTempVarSaveSameNameFailed": "A variable with this default name already exists, please enter a different name.", "maxUsersCountRole": "The maximum number of users in the role", "unlimitedAssignLeaveBlankInfo": "For unlimited assignments, leave the field blank.", "cvOwner": "Overview owner", "changePassword": "Change password", "passwordExpired": "Your password has expired. Enter a new password.", "passwordWillExpire": "Your password will soon expire. Enter a new password.", "userParameters": "User parameters", "filterSortingHelper": "Sorting by one or more columns in a filter disables the ability to manually sort columns directly in the table.", "importUsers": "Import users", "importRoles": "Import roles", "existingEntityRows": "Rows with already existing entities (can be overwritten)", "fileRow": "File row", "existingEntityRowsMultiple": "Rows with entities that already exist more than once (will not be imported)", "importOrgUnits": "Import org. units", "structureImportExport": "Structure import/export", "fillAttributes": "Fill attributes", "structurePreview": "Structure preview", "invalidRowsForImport": "Invalid rows (missing mandatory data)", "duplicateRowsForImport": "Rows with duplicate matching data (will not be imported)", "newEntityRows": "Rows with new entities to import", "existingNameRowsForImport": "Rows with names that already exist on other entities (will not be imported)", "overwriteExisting": "Overwrite existing", "structurePreviewHelper": "The structure preview shows two different situations: importing only new organizations, or importing both new and existing organizations that will be overwritten. All changes compared to the current structure are marked in red.", "validateAndShowPreview": "Validate and show preview", "uploadNewFile": "Upload a new file", "userStatus": "User status", "importedFile": "Imported file", "pairUsersBy": "Pair users by", "assignOrgBy": "Assign to organization by", "pairRolesBy": "Pair roles by", "pairUnitsBy": "Pair units by", "unitHierarchyCol": "Unit hierarchy column", "dontAssign": "Don't assign", "infoImportDataValidated": "WARNING: The data has just been validated due to changes in settings. We recommend going back and checking the new import preview.", "assignUserRolesMethod": "Method to assign roles to users", "assignUserRolesMethodHelp": "Role assignment method: add to already assigned roles, or completely replace currently assigned roles with newly assigned roles.", "existingRolesForImport": "Already existing roles (can be overwritten)", "existingRoleNamesForImport": "Roles with names that already exist with other roles (will not be imported)", "newRolesForImport": "New roles to import", "userRolesForImport": "Rows with user roles to assign", "nonExistentUsersForImport": "Rows with non-existent users (roles will not be assigned)", "multipleExistingUsersForImport": "Rows with more than one existing user (roles will not be assigned)", "invalidOrgsForImport": "Invalid rows (missing mandatory data or wrong hierarchy)", "keepOriginal": "Keep original", "assignOrgByHelp": "If you select a column from the file, you can specify the organization to assign for new and existing users. If you select a specific organization, all imported or updated users will be assigned to this organization.", "creatingRoles": "Creating roles", "assigningRolesToUsers": "Assigning roles to users", "newUsers": "New users", "existingUsers": "Existing users", "fromFile": "From file", "alrCsvXlsxUploadWrongExtension": "Only upload files with *.csv or *.xlsx extension", "importNewAndExisting": "Import new entities and overwrite existing ones", "importNewOnly": "Import new entities only", "importNewAndExistingRoles": "Import new roles and overwrite existing roles", "importNewRolesOnly": "Import new roles only", "importRolesHelper": "Settings for importing the roles themselves. Assigning roles to users is governed by what is set in \"Pair users by\" and always applies to new and existing roles.", "statisticsColorHelper": "If colors are not selected manually, or if there are fewer selected colors than columns, the missing colors are generated automatically. Generated colors never contain dark or too light shades, these can only be selected manually.", "caseService": "Case service", "taskService": "Task service", "editTasks": "Edit tasks", "editCases": "Edit cases", "deleteTasks": "Delete tasks", "deleteCases": "Delete cases", "serviceOperationsInfo": "Mark and fill in the variables you want to change.", "erased": "Erased", "statusErrored": "Errored", "serviceOperations": "Service operations", "runCalcsOnStart": "Run calculations on start", "taskReactivation": "Task reactivation", "taskCompletion": "Task completion", "caseReactivation": "Case reactivation", "caseCompletion": "Case completion", "openTask": "Open task", "changeEntity": "Change entity", "selectTableColumns": "Select table columns", "parentCase": "Parent case", "ownerOrganization": "Owner organization", "confirmTaskReactivation": "Are you sure you want to reactivate the selected tasks?", "confirmCaseReactivation": "Are you sure you want to reactivate the selected cases?", "confirmTaskCompletion": "Are you sure you want to complete the selected tasks?", "confirmCaseCompletion": "Are you sure you want to complete the selected cases?", "selectAllFilterMustBeActive": "At least one filter must be active to select all items.", "changeEntities": "Change entities", "disabledDifferentTemplates": "Cannot be changed because the entities are not from the same template.", "actions": "Actions", "taskTemplateId": "Task template ID", "caseTemplateId": "Case template ID", "actionInfoCheckLogs": "The action will be performed in the background, please check the logs.", "alrServiceOperationsColumnsFailed": "Saving of service operations columns setting failed.", "confirmResetSelectedCols": "Are you sure you want to reset the saved table columns?", "instanceVars": "Instance variables", "usrId": "User ID", "orgId": "Organization ID", "titlePrefix": "Title prefix", "titleSuffix": "Title suffix", "accessRoleId": "Access role ID", "maxAssigns": "Maximum assignments", "client": "Client", "bigValue": "Big value", "unitId": "Unit ID", "roleId": "Role ID", "paramId": "Parameter ID", "varId": "Variable ID", "parentId": "Parent ID", "openUser": "Open user", "openRole": "Open role", "openUnit": "Open unit", "units": "Units", "managerId": "Manager ID", "externalStatus": "External status", "additionalId": "Additional ID", "parentIc": "Parent IC", "companyIc": "Company IC", "textValue": "Text value", "dateValue": "Date value", "numberValue": "Numeric value", "loginCount": "Login count", "externalLogin": "External login", "badLoginCount": "Bad login count", "passwordLastChange": "Last password change", "solverEvaluation": "Evaluation of the task owner", "solverWillBe": "The task owner will be", "possibleSolvers": "Possible task owners", "selectReferencePerson": "Select a reference person", "evaluateSolver": "Evaluate task owner", "referenceUserForEval": "Reference user for evaluation", "andOthers": "...and others", "showLess": "...show less", "alrSessionExpired": "Your session has expired, please log in again.", "mailPromptlyInfo": "The user continuously receives one-time notifications about new tasks, where they are the task owner. These alerts will be sent only if the task has not been solved for {{minutes}} minutes since its activation.", "mailPullInfo": "The user continuously receives one-time notifications about new tasks that are available for subscription, and the user is their possible task owner. The notification goes out at the moment of activation of the given task within the Workflow.", "mailTotalInfo": "The user periodically receives an overview with tasks to be completed, of which they are the task owner. If the task does not have a direct task owner, the owner of the process is notified. If the user is substituted, the notification is received by his substitute.", "mailEscalationInfo": "The user periodically receives an overview with tasks to be completed that have surpassed the deadline. They are notified if they are the supervisor of the task (and not its task owner at the same time) or they are the direct manager of the user who is the task owner. If the task has no task owner, the process owner is considered the supervisor. If the user is substituted, the notification mentions who is the current substitute.", "calcSourceOverwriteWarning": "After saving, the source is overwritten with ES6 syntax!", "changeStatus": "Change status", "confirmChangeEmailStatus": "Do you really want to change the status of selected emails?", "logInAgain": "Log in again", "migrations": "Migrations", "launchDate": "Launch date", "stepName": "Step name", "runId": "Run ID", "clone": "<PERSON><PERSON>", "confirmDeleteCron": "Do you really want to delete the selected cron?", "alrCronDeleted": "<PERSON><PERSON> has been deleted!", "wantToContinueQ": "Do you want to continue?", "valueCannotBeEntered": "The value cannot be entered", "processingQueues": "Processing queues", "pause": "Pause", "fillOptionsFromVarHelper": "Filter options can only be filled from variables of type DT, DL, LT, LD, LN, and D, which do not allow multiple choice.", "defaultTemplateName": "Default template name", "defaultTaskName": "Default task name", "defaultVariableName": "Default variable name", "variableName": "Variable name", "alrNoDataFound": "No data found", "ttProcessingQueuesInfo": "Processing queues are disabled.\nTo enable them, set at least one of the \"scaling.queue.*.enabled\" configurations to true.", "businessUsers": "Business users", "completeHrAgenda": "Complete HR agenda", "usageStatsByHeader": "Usage statistics by header", "usageStatsByOrgUnit": "Usage statistics by org. unit", "usageStatsByUser": "Usage statistics by user", "completedTasksNum": "Number of completed tasks", "startedProcessesNum": "Number of started cases", "ideHelp": "Press Ctrl + Space in the editor to see suggestions, press again for more detailed help. Press F1 to see all available commands and keyboard shortcuts. See the <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>editor's documentation</a> for more.", "restHelp": "Enter the URL for one of the TAS table services (eg '/tasks/mine') and after loading the service select the table columns you want to display in the container.", "defaultGraphName": "Default graph name", "graphName": "Graph name", "ttStatistics": {"heading": "Statistics", "body": ""}, "defaultAxisXName": "Default X axis name", "defaultAxisYName": "Default Y axis name", "defaultFilterName": "Default filter name", "filterName": "Filter name", "defaultOptionName": "Default option name", "optionName": "Option name", "defaultOverviewName": "Default overview name", "overviewName": "Overview name", "eventName": "Event name", "wantToOverrideEs6": "If you really want to rewrite, write <b>ES6</b>", "processArchivation": "Process archivation", "processUnarchivation": "Process unarchivation", "resendEmail": "Resend email", "alrFailedSendEmail": "Failed to send email", "ttResendEmail": {"heading": "Resend email", "body": "Resends a previously sent email notification. Recipients can be changed or added."}, "addCurrentScreenToFavourite": "Add the current screen to your favorites", "attachmentAdd": "Add a document", "createNewCase": "Creating a new case", "moreLanguage": "Other variants of languages", "notesAdd": "Add a note", "notesNew": "New note", "removeCurrentScreenFromFavourite": "Remove the current screen from the favorite", "setDashboard": "Edit the dashboard", "chooseFromCases": "Select from cases", "folders": "Folders", "newFolderBtn": "New folder", "documentInfo": "Document information", "userInfo": "User information", "deleteImage": "Delete image", "profilePhoto": "Profile photo", "profilePhotoCaption": "Use a photo in jpeg, jpg, png or gif format.", "updatePhoto": "Update photo", "mailNotifications": "Email notifications", "userPreferences": "User preferences", "userSettings": "User settings", "allVices": "All substitutes", "createVice": "Create substitute", "editVice": "Edit substitutes", "viceTip": "Substitution allows you to pass your agenda to a colleague", "emptyDataMessage": "There is nothing more", "addFirstNote": "Add first note", "noResultsFor": "No results for:", "noCurrentTasks": "No current tasks", "checkYourSearch": "Check your search and try again.", "noFavOverviews": "No favorite overviews", "favOverviewsTip": "Add an overview to your favorites with a star", "noHiddenOverviews": "You have no hidden overviews", "addOverview": "Add overview", "hidden": "Hidden", "removeConfirm": "Remove", "removeItem": "Are you sure you want to remove {{variable}}?", "changePicture": "Change picture", "saveFilter": "Save filter", "addAnotherVice": "Add another substitute", "saveVice": "Save substitution", "firstLastName": "First and last name", "taskInfo": "Task info", "emptyFavsTip": "Add favorites with the button", "saveAndClose": "Save and close", "usersCanEditOverview": "Users can edit overview", "assignedUsers": "Assigned users", "assignedOrgUnits": "Assigned organizational units", "assignedRoles": "Assigned roles", "otherLangVariants": "Other language variants", "moveToSharing": "Move to sharing", "insertDocumentsPerm": "User has permission to insert documents and notes", "saveNewPassword": "Save new password", "confirmSubscription": "Confirm subscription", "subscriptionCaption": "The selected overview will be sent to you by e-mail at the set time.", "by": "By", "frequency": "Frequency", "termination": "Termination", "ofTheMonth": "Of the month", "endOnDate": "End on date", "endAfter": "End after", "onlyOnWorkingDays": "Only on working days", "occurrences": "occurrences", "dayOfWeekBy": "Day of the week", "calendarDayBy": "Calendar day", "dateBy": "date", "byDate": "By date", "byOccurrenceCount": "By occurrence count", "infinitely": "Infinitely", "dayOfMonthAdornment": "th day of month", "ordinalAdornment": "th", "toDateBeforeFromError": "The 'To' date cannot be before the 'From' date", "vice": "Substitution", "previewShown": "Preview shown", "duplicate": "Duplicate", "hideBtn": "<PERSON>de", "userView": "User view", "adminView": "Admin view", "or": "Or", "overlappingVicesError": "Substitutes cannot overlap", "fileVar": "file", "nodeVar": "node", "uploadDifferentFile": "Upload different file", "uploadedFile": "Uploaded file", "refreshPage2": "Refresh Page", "refreshPageCaption": "Please refresh the page in your browser to continue.", "ttCopy": {"heading": "Copy", "body": "Allows you to copy the selected item with the option to edit some parameters."}, "alrError_INVALID_CSV_MAPPING": "Not found CSV column '%s' in the mapping of the event. Contact the application administrator.", "documentPreview": "Document preview", "moveUp": "Move up", "moveDown": "Move down", "moveToFilter": "Move to filter", "moveToSorting": "Move to sorting", "addSorting": "Add sorting", "cancelFilters": "Cancel filters", "docUploadedImmediately": "The document will be uploaded immediately", "moreOptions": "More options", "docSearchPlaceholder": "E.g. invoice.pdf...", "tasksSearchPlaceholder": "E.g. Enter a new invoice...", "docUploadedImmediatelyPrivate": "The document will be immediately uploaded as private", "takeTsk": "Take task", "tasksActive": "Active tasks", "subprocesses": "Sub-processes", "cancelAuthorization": "Cancel authorization", "cancelAuthorizationConfirm": "Are you sure you want to cancel device authorization?", "linkMobileApp": "Link mobile app", "mobileApp": "Mobile app", "scanThisQr": "Scan this QR code with your mobile device.", "scanningQr": "Scanning QR code. Please wait.", "deviceName": "Device name", "newDeviceName": "New device name", "registrationDate": "Registration date", "lastLogin": "Last login", "mobileNotifications": "Mobile notifications", "disableMobileNotification": "Turning off notifications on mobile", "newQrCode": "New QR code", "inactiveScanQr": "Inactive - scan QR code.", "enableNotifications": "Enable notifications", "tip": "Tip: {{message}}", "alrFavContainerAlreadyExists": "Favorites container already exists.", "addGraph": "Add graph", "newRow": "New row", "confirmSetDefaultDashboard": "Do you really want to set the current Dashboard as the default for all users?", "changeMayAffectAllUsers": "This change may affect all users.", "noOverviewsTip": "Create a new overview using the \"Add overview\" button", "removeFromHidden": "Remove from hidden", "last7Days": "Last 7 days", "last14Days": "Last 14 days", "last30Days": "Last 30 days", "lastCalendarMonth": "Last calendar month", "lastQuarter": "Last quarter", "last12Months": "Last 12 months", "lastCalendarYear": "Last calendar year", "noFilterSet": "No filter set", "noSortingSet": "No sorting set", "deleteGroup": "Delete group", "newGroup": "New group", "operators": "Operators", "withActiveTask": "With active task", "withoutActiveTask": "Without active task", "withNoTerm": "No due date", "withTerm": "With due date", "securityAndAuthentication": "Security and Authentication", "dataIntegrationAndManagement": "Data Integration and Management", "appManagementAndConfig": "App Management and Configuration", "monitoringAndMaintenance": "Monitoring and Maintenance", "adminSearchPlaceholder": "E.g., Public files...", "authenticationAdminDescription": "User login options", "certificatesAdminDescription": "Certificates for TAS", "elasticsearchAdminDescription": "Integration with Elasticsearch", "xmlProcessImportAdminDescription": "Import XML processes using cron record XMLProcessImport.js", "structureImportExportAdminDescription": "Import/export of organizational structure, users and roles", "dmsAttributesAdminDescription": "List of document attributes in DMS", "dynTablesAdminDescription": "Data storage in dynamic tables", "csvAdminDescription": "Manipulation of CSV files in the application", "configurationAdminDescription": "Application configuration", "settingsAdminDescription": "Company identification settings and other administrative tasks", "logsAdminDescription": "Manage and view application logs", "migrationsAdminDescription": "Data migration and application configuration", "guidesAdminDescription": "Help and guides for users", "schemeAdminDescription": "Color scheme, logo and other elements in the application", "sequencesAdminDescription": "Manage sequences used in templates", "serviceConsoleAdminDescription": "Application-administrative commands through the service console", "serviceOperationsAdminDescription": "Complex management of service operations", "scriptsAdminDescription": "Manage reusable scripts across different templates", "appStatusAdminDescription": "Information about the current status of the application", "usageStatsAdminDescription": "View application usage statistics", "maintenanceAdminDescription": "Maintenance settings and execution of maintenance tasks", "scheduledTasksAdminDescription": "Manage all scheduled tasks", "publicFilesAdminDescription": "Manage public files and documentation", "cronsAdminDescription": "Automating regular tasks", "hrAgendaAdminDescription": "User agenda management within HR", "emailsQueueAdminDescription": "Email queue management and all email communication from TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Adding item to favorites failed", "alrRemoveFavItemFailed": "Removing item from favorites failed", "alrAddHiddenItemFailed": "Failed to add hidden item", "alrRemoveHiddenItemFailed": "Failed to remove hidden item", "display": "Display", "compact": "Compact", "standard": "Standard", "comfortable": "Comfortable", "exportTo": "Export to", "adminMenuTip": "Add your items in administration to favorites. Clicking the star will display the item right here.", "editorDocumentation": "Editor documentation", "addSection": "Add section", "insertSection": "Insert section", "section": "Section", "sections": "Section", "toTop": "To the top", "toEnd": "To the end", "alrSectionNotBeEmpty": "The section must not be empty", "confirmDeleteSection": "Do you really want to delete the section?", "sectionVarsMoveAllTasks": "Variables in all tasks will be moved from the removed section to variables without section.", "sectionVarsMove": "Variables will be moved from the removed section to variables without a section.", "actionCannotUndone": "This action cannot be undone.", "overviewOfAllNews": "Overview of all news", "copyOverview": "Copy overview", "create": "Create", "copyExistingOverview": "Copy existing overview", "selectOverview": "Select overview", "chooseFromOverviews": "Choose from overviews...", "selectTemplate": "Select template", "chooseFromAvailableTemplates": "Choose from available templates...", "loginWithUsernamePassword": "Log in with username and password", "signInWithCorporateIdentity": "Sign in with corporate identity", "whatsNewInTAS": "What's new in TAS?", "whatsNewInTASDescription": "Updates, new features, tips, tricks and everything you need to know.", "justOpen": "Just open", "editOverview": "Edit overview", "noGraphsTip": "Create a new graph using the \"Add graph\" button", "noDocumentsTip": "Add a document to the task or using the \"Add\" button", "noFilesTip": "Add a new file using the \"Add\" button", "less": "Less", "notContains": "Does not contain", "factorySettings": "Factory settings", "previewCollapsedNavMenu": "Preview of collapsed navigation menu", "previewExpandedNavMenu": "Preview of expanded navigation menu", "logoForCollapsedNavMenu": "Logo for collapsed navigation menu", "logoForExpandedNavMenu": "Logo for expanded navigation menu", "organisationLogo": "Organisation logo", "pickLogoOrganisation": "Pick logo for organisation", "addLogo": "Add logo", "clickForAddLogoOrDrop": "Click to add logo or drop file here", "useLogoSizeMin": "Use logo size min.", "notEquals": "Does not equal", "sharedWithMe": "Shared with me", "myOverview": "My overview", "getMobileAppText": "Get the mobile app from the app store", "noDocuments": "No documents", "noNotes": "No Notes", "noFiles": "No files", "addFirstDocument": "Add first document", "killed": "Killed", "chooseNewLogo": "Select a new logo", "function": "Function", "groupFunction": "Function between groups", "mobileAppAuthFailed": "Mobile app authentication failed.", "currentDocumentVersion": "Current document version", "csp": "Content Security Policy", "documentsDelete": "Delete documents", "confirmDocumentsDelete": "Do you really want to delete the selected documents?", "confirmDocumentsDownload": "Do you want to download the selected documents?", "firstNum": "first {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Download documents", "caseLogs": "Case logs", "archiveCases": "Archive cases", "archive": "Archive", "unarchive": "Unarchive", "confirmArchiveCases": "Do you really want to archive the selected cases?", "archiveInDays": "Archive in (days)", "archived": "Archived", "archivedx": "Archived", "alrArchivingCase": "Case is being archived...", "alrCaseArchived": "Case has been archived.", "alrLackOfPermsToArchiveCase": "You do not have sufficient permissions to archive the case.", "alrArchiveCaseFailed": "Case archiving failed.", "alrUnarchivingCase": "Case is being unarchived...", "alrCaseUnarchived": "Case has been unarchived.", "alrLackOfPermsToUnarchiveCase": "You do not have sufficient permissions to unarchive the case.", "alrUnarchiveCaseFailed": "Case unarchiving failed.", "byUser": "By user", "byAgenda": "By agenda", "agendaHandover": "Agenda handover", "activeUsers": "Active users", "lockedUsers": "Locked users", "allUsers": "All users", "inactiveUsers": "Inactive users", "hrAgendaSearchPlaceholder": "E.g. <PERSON> ...", "completeAgendaHandover": "Complete agenda handover", "handoverCases": "Handover cases", "handoverTasks": "Handover tasks", "handoverVars": "Handover variables", "changeTaskOwner": "Change task solver", "confirmHandover": "Confirm handover", "filterCasesByHeaderTip": "You can filter all cases under the same header in the Header column.", "userAgendaSelectedHandover": "Handover of <b style=\"color: {{color}};\">selected</b> user agenda", "userAgendaCompleteHandover": "Handover of <b style=\"color: {{color}};\">complete</b> user agenda", "confirmAgendaHandover": "Are you sure you want to hand over the selected agenda ({{selected}}) to the user <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Are you sure you want to hand over the <b>selected</b> agenda of user <b>{{user}}</b> to user <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Are you sure you want to hand over the <b>complete</b> agenda of user <b>{{user}}</b> to user <b>{{newUser}}</b>?", "refreshSessionTitle": "TAS session will be terminated in {{minutes}} minutes.", "refreshSessionCaption": "Click \"Keep working\" to continue without interruptions.", "continueWorking": "Keep working", "sessionExpiredCaption": "Click \"Log in again\" to return to the login screen.", "loginExpired": "We have logged you out after a long period of inactivity.", "confirmArchiveCase": "Do you really want to archive the selected case?", "isLowerOrEqualThan": "Must be lower than or equal to", "confirmUnarchiveCase": "Are you sure you want to unarchive the selected case?", "addCaseRightNewUserTooltip": "If you do not check this option, the new user will be replaced in the business variable, but will not have access to the case.", "canBeViced": "Substituted by", "canVice": "Substituting", "backgroundColor": "Background color", "defaultDashboardView": "Preview of default dashboard", "colorScheme": "Color scheme", "displaySelectionAsTags": "Display selection as tags", "displayAsPassword": "Display as password", "sideBySide": "Side by side", "copyAssignmentFromTask": "Copy assignment from task", "toTask": "To task", "copyTaskAssignmentWarning": "The assignment in the task is not empty, do you want to overwrite it?", "copyToOtherTasks": "Copy to other tasks", "noteScriptsNotApplied": "Note: Scripts are not applied", "generateRecHistory": "Show in active tasks and history", "leaveFormerRoles": "Leave former roles", "includeCompetences": "Include competences", "copyRoles": "Copy roles", "userIsActive": "User is active", "systemUser": "System user", "copyRolesFromUser": "Copy roles from user", "assignedRolesOverview": "Overview of assigned roles", "copyRolesInfo": "If the user is part of the competencies, these competencies will not be copied immediately. They will be generated:", "notificationOn": "Fastened", "notificationOff": "Shutdown", "onNotification": "Notification", "offNotification": "Notification", "page": "page", "fromTo": "From - to", "isAnyOfValue": "Is any value from", "notcontains": "does not contain", "notequals": "uneven", "fromto": "from - to", "isanyofvalue": "is any value from", "alrNoteToggleVisibiltyFailed": "Failed to hide/unhide the note", "alrNoteHideOnEditFailed": "Failed to hide the original note", "hiddenShe": "Hidden", "showHiddenNotes": "Show hidden notes", "alrNoteEdited": "Edited version of the note has been saved", "notesEdit": "Edit note", "displayName": "Display name", "clientDateFormat": "Date format", "defaultByLanguage": "Default by language", "restKeysOptionsNotUpToDate": "Outdated selection of values - reload the service.", "invalidValue": "Invalid value", "ended": "Ended", "exportAllActive": "Export all active", "alrScriptsLoadFailed": "Scripts failed to load.", "scriptsImport": "Import scripts", "doImport": "Import", "alrImportingScripts": "Importing scripts...", "alrScriptsImported": "Scripts have been imported.", "alrScriptsImportFailed": "Scripts import failed.", "removeAll": "Remove all", "alrNoScriptsToImport": "No scripts to import.", "activateAll": "Activate all", "alrNoPermsToEditNoteInVice": "You do not have permission to edit a note while substituting.", "alrNoPermsToToggleNoteVisibilityInVice": "You do not have permission to hide/unhide a note while substituting.", "plusMore": "more", "variableAlignment": "Variable alignment", "variableAlignmentHelp": "Affects the alignment of the variable value within the task form.", "variableAlignmentLeft": "Left", "variableAlignmentRight": "Right", "tasksMineAndToPull": "My + To pull", "myDevice": "My device", "deleteLogo": "Delete logo", "namingFilter": "Filter name", "exceptionsToRegularSchedule": "Exceptions to regular schedule", "noExceptions": "No exceptions", "specificDates": "Specific dates", "dateFromTo": "Date from - to", "weekdayCap": "Day of the week", "specificDayBy": "Specific day", "yearsBy": "years", "timed": "Timed", "firstDayOfMonth": "First day of the month", "lastDayOfMonth": "Last day of the month", "firstDayOfYear": "First day of the year", "lastDayOfYear": "Last day of the year", "addDate": "Add Date", "newPlan": "New Plan", "addAnother": "Add another", "startTime": "Start time", "endTime": "End time", "inTimeFromTo": "in the time from {{from}} to {{to}}", "dayOfMonthBy": "Day of the month", "cWorkDays": "working days", "cWeeks": "weeks", "cMonths": "months", "cYears": "years", "everyWeek": "every week", "everyYear": "every year", "inMonth": "in the month", "everyDay": "every day", "seqIdEdit": "Edit sequence ID", "allowMultiselectSearchRight": "Allow search in assignment", "doubleHeightForContent": "Double height for content", "alrNoVariablesMappingToImport": "No variable mappings to import.", "alrVariablesMappingImportLoadFailed": "Failed to load variable mappings for import.", "variablesMappingImport": "Import variable mappings", "useAllMappings": "Use all mappings", "doExportVariablesMapping": "Export variable mappings", "alrImportingVariablesMapping": "Importing variable mappings...", "alrVariablesMappingImported": "Variable mappings have been imported.", "alrVariablesMappingImportFailed": "Failed to import variable mappings.", "alrVariablesMappingImportedPartially": "Variable mappings were only partially imported. Some variables were not found.", "alrEditorHintsLoadFailed": "Failed to load editor hints.", "addTable": "Add table", "confirmDynTablesDelete": "Do you really want to delete the selected dynamic tables?", "dynTablesDelete": "Delete dynamic tables", "addRow": "Add row", "preview": "Preview", "columnDelete": "Delete column", "editRow": "Edit row", "addingNewColumn": "Adding new column", "addingNewRow": "Adding new row", "columnsRename": "Renaming columns", "rowCellValues": "Row cell values", "saveDynTableName": "Save dynamic table name", "saveDynTableNameQ": "Save dynamic table name?", "saveDynTableNameWarning": "Warning, make sure that changing the table name will not affect existing calculations in templates.", "rowMove": "Move row", "alrCsvParsingErr": "Error parsing CSV!", "addFirstTableColumn": "Add first table column", "my": "Mine", "license": "License", "licenses": "Licenses", "addLicense": "Add license", "licenseResult": "License result", "alrLicenceResultLoadingFailed": "Failed to load license result.", "licensesAdminDescription": "License management", "uploadByDragging": "Upload a file by dragging.", "uploadByDraggingAnywhere": "Upload a file by dragging anywhere in the space.", "assignVariable": "Assign Variable", "confirmDeleteSectionName": "Are you sure you want to delete section <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Warning: Section will be deleted for all affected tasks including variables.", "tasksAffected": "Affected tasks", "varSearchPlaceholder": "Eg. billing …", "enlarge": "Enlarge", "show": "Show", "shrink": "Shrink", "hide": "<PERSON>de", "doValidate": "Validate", "phoneNumber": "Phone number", "textLength": "Text length", "when": "when", "to2": "to", "that": "that", "dynCondBuilderBlockFunctionDescShow": "Shows the variable if the condition is met.", "dynCondBuilderBlockFunctionDescHide": "Hides the variable if the condition is met.", "dynCondBuilderBlockFunctionDescChange": "Changes the value of the variable if the condition is met.", "dynCondBuilderBlockFunctionDescValidate": "Validates the value of the variable.", "addCondition": "Add condition", "operator": "operator", "equals": "equals", "greaterthan": "greater than", "greaterorequal": "greater or equal", "lessthan": "less than", "lessorequal": "less or equal", "demoCode": "Demo code", "code": "Code", "confirmDeleteConditions": "Do you really want to delete all conditions (including the script)?", "validationErrorMessage": "Validation error message", "alrScriptToStructuredBlockConversionFailed": "Script conversion to structured block failed.", "alrStructuredBlockToScriptConversionFailed": "Structured block conversion to script failed.", "alrScriptToBuilderConversionFailed": "Script conversion to builder failed.", "alrBuilderToScriptConversionFailed": "Builder conversion to script failed.", "dynCondBuilderBlockFunctionDescScript": "Dynamic conditions script block.", "convertToStructuredBlock": "Convert to structured block", "convertToScript": "Convert to script", "dynCondBuilderBlockWatchVarsLabel": "Run on change (watchVars)", "variables": "Variables", "copyToOthers": "Copy to others", "sectionName": "Section name", "newSectionName": "Name of the new section", "testIt": "Test it", "addAdjacentSection": "Add a neighboring section", "addAdjacentSectionBelow": "Add the neighboring section below", "selectExistingSection": "Pick an existing section", "renameSectionWarning": "Warning: The section will be renamed in all the template tasks.", "warning2": "Warning", "copyAssignmentToTask": "<PERSON>py the assignment to the task", "copyAlsoConditions": "Copy also conditions", "copyAssignmentToTaskWarning": "Warning: Assignment and possibly dynamic conditions in the selected task will be rewritten.", "importFromOtherTask": "Import from another task", "startFromScratch": "Start from the beginning", "howToStartAssignments": "How do you want to start assigning variables?", "selectTaskToImport": "Select the task for import", "confirm": "Confirm", "selectTaskToTest": "To select a task for testing", "toTestSaveChanges": "Changes should be stored for testing.", "variableAssignmentTest": "Test Assignment of Variables", "viewAsMobile": "View as on mobile", "viewAsPc": "View as on PC", "emptySpace": "Empty space", "variableAssignments": "Assigning variables", "allowCompletionOnChangeOf": "Allow completion on change", "dynCondBuilderBlockFunctionDescRead": "Changes the variable mode to \"read-only\" if the condition is met.", "dynCondBuilderBlockFunctionDescWrite": "Changes the variable mode to \"read and write\" if the condition is met.", "dynCondBuilderBlockFunctionDescMust": "Changes the variable mode to \"mandatory\" if the condition is met.", "dynCondBuilderBlockFunctionDescSolve": "Allows task completion on variable change if the condition is met.", "newsManagement": "News management", "newsManagementAdminDescription": "Management of news in the application", "addNewsPost": "Add news", "newPost": "New post", "news": "News", "basicInfo": "Basic information", "publicationPlanning": "Publication planning", "displayToUsers": "Display to users", "displayLocation": "Display location", "newsPostContentSingular": "News content", "postTitle": "Post title", "newsManagementPostDetailPhoneNumberTooltip": "Phone number to display in news detail", "newsManagementPostDetailEmailTooltip": "Email to display in news detail", "customUrlLink": "Custom URL link", "newsManagementPostDetailCustomUrlLinkTooltip": "Custom URL link to display in news detail", "stateAfterSaving": "Status after saving", "newsPostStateActive": "Active", "newsPostStateInactive": "Inactive", "newsPostStatePlanned": "Planned", "endNewsPostOnSpecificDate": "End news on a specific date", "sendNewsPostViaEmail": "Send news via email", "priorityNewsPost": "Priority news", "newsManagementPostDetailPriorityNewsTooltip": "For example, to announce a shutdown or change in work procedure", "newsPostEndDate": "News end date", "pickNewsPostDisplayToOrgUnits": "Which org. units to display the news to?", "pickNewsPostDisplayToRoles": "Which roles to display the news to?", "pickNewsPostDisplayToUsers": "Which users to display the news to?", "pickNewsPostDisplayOnTemplate": "On which template to display the news?", "pickNewsPostDisplayOnHeaders": "On which headers to display the news?", "pickNewsPostDisplayOnTasks": "On which tasks to display the news?", "pickNewsPostDisplaySubOptionsHelperText": "First, select the template on which you want to display the news.", "newsTagsManagement": "News tags management", "newsTagsManagementAdminDescription": "Management of news tags in the application", "addTag": "Add tag", "tags": "Tags", "publicationDate": "Publication date", "contacts": "Contacts", "avaibleUntil": "Available until", "published": "Published", "newsSinceLastVisitAmount": "Total {{amount}} news since last visit", "noNews": "No news", "createNewTag": "Create new tag", "tagName": "Tag name", "alrTagSaved": "<PERSON> was saved.", "alrTagSaveFailed": "Tag saving failed.", "confirmDeleteTag": "Do you really want to delete the tag \"{{tagName}}\"?", "alrPostSaved": "Post was saved.", "alrPostSaveFailed": "Post saving failed.", "alrLoadingTagsFailed": "Loading tags failed.", "confirmDeletePost": "Do you really want to delete the post \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "Do you really want to delete the selected posts?", "post": "Post", "alrPostLoadFailed": "Loading post failed.", "alrTagDeleted": "Tag was deleted.", "alrTagDeleteFailed": "Tag deletion failed.", "alrPostDeleted": "Post was deleted.", "alrPostDeleteFailed": "Post deletion failed.", "alrPostsDeleted": "Selected posts were deleted.", "alrPostsDeleteFailed": "Deletion of selected posts failed.", "alrTempTasksLoadFailed": "Template tasks failed to load.", "rolesRestriction": "Role restrictions", "usersRestriction": "User restrictions", "orgUnitsRestriction": "Org. unit restrictions", "alrPriorityNewsLoadFailed": "Failed to load priority news.", "moreInfo": "More info", "tas5Info": "TAS 5.0 is here ...", "totalNewsAmount": "Total {{amount}} news", "alrNewsContainerPostsLoadFailed": "Loading posts for the news container failed.", "alrTaskNewsLoadFailed": "Task news loading failed.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "The publication date must be before the news end date.", "alrNotificationsNewsLoadFailed": "Failed to load news for notifications.", "moreNews": "More news", "newsManagementPostDetailConfirmSavingWillSendMail": "Saving the post will cause an email to be sent to all users for whom the post is intended. Do you really want to save the post?", "mailNewsNotification": "E-mail news notifications", "mailNewsNotificationInfo": "The user continuously receives news intended for them.", "alrRefreshingConfig": "Refreshing configuration...", "alrConfigRefreshed": "Configuration refreshed successfully", "alrConfigRefreshFailed": "Failed to refresh configuration", "newsPostContent": "News content", "ttRefreshConfig": {"heading": "Restore configuration from all sources", "body": ""}, "getMobileAppTextQr": "Get the mobile app from the app store or scan the QR code", "dateStart": "Start date", "dateEnd": "End date", "tas_forms_generated": "Number of automatically generated forms"}