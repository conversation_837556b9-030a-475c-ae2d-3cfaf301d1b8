{"1st": "Prima", "2nd": "A 2-a", "3rd": "A 3-a", "4th": "A 4-a", "AddToAllTasks": "Adăugați la toate actiunile", "OfVariable": "Din variabilă", "RemoveFromAllTasks": "Eliminați din toate actiunile", "TaskOwnerWhichInVar": "Ai da sarcină proprietarului ce este setat în variabilă", "action": "Acțiune", "active": "Activează", "activity": "Activitate", "activityType": "Tip de activitate", "actualEnd": "Ora efectivă de sfârșit", "actualSolver": "Responsabillul de realizare al actiunii", "actualStart": "Ora reală de început", "actualTsks": "Actiune <PERSON>", "actualize": "Actualizează", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAttribute": "Adăugați atribut", "addOrgUnit": "Adăugați org.unit", "addPlan": "Adăugați plan", "addPrintTemplate": "Adăugați șablon de imprimare", "addRole": "Adăugați rol", "addRule": "Adăugați regulă", "addTemp": "Adăugați șablon", "addTsk": "Adăugați actiune", "addUser": "Adăugați utilizator", "addUserSomething": "Atribuie un utilizator la variabila selectata {{variable}}:", "addVariable": "Adăugați variabila", "after": "<PERSON><PERSON><PERSON>", "afterTermTasks": "Sarc<PERSON>le mele sunt scadente", "all": "Toate", "allFiles": "<PERSON><PERSON>", "allMustBeMet": "Toate trebuie să fie îndeplinite", "allMyTasks": "Toate sarcinile mele", "allSubOfPlanGuar": "Toți subordonații garanției planului", "allTasksWithNoTerm": "Toate actiunile fără scadență", "allTasksWithTerm": "Toate actiunile cu data scadenta", "allTemps": "<PERSON><PERSON><PERSON><PERSON>", "allowMultiple": "Permiteți alegerea mai multor opțiuni", "allowSelectAll": "Activați selectarea tuturor articolelor", "allsupOfPlanGuar": "Toți superiorii garanției de plan", "alrActionNameNotDefined": "Acțiune numele acțiunii \"{{actionName}}\" nu este definit.", "alrActionNotDefined": "Acțiunea nu a fost definită.", "alrApiUrlMissing": "Lipsă sursă de date din tabel.", "alrAssigningTsk": "Atribuirea unei actiuni...", "alrAssignmentFailed": "Salvarea alocarii a eșuat", "alrAtrFailed": "Ștergerea atributului nu a reușit", "alrAttachDeleteFailed": "Ștergerea documentului a eșuat", "alrAttachDeleted": "Documentul a fost șters!", "alrAttachDeleting": "Documentul se șterge...", "alrAttachDownloadFailed": "Descarcarea documentului a eșuat.", "alrAttachDownloaded": "Documentul a fost descărcat!", "alrAttachDownloading": "Descarc documentul...", "alrAttachMetaFailed": "Salvarea metadatelor pentru documente a eșuat.", "alrAttachOrNotesCountFailed": "Numerotarea documentului sau a notei a eșuat.", "alrAttachSaveFailed": "Încărcarea documentelor a eșuat.", "alrAttachSaved": "Documentul a fost atașat.", "alrAttachTooBig": "Documentul este prea mare! Documentul a trecut limita de {{maxUploadSize}} MB.", "alrAttrDataFailed": "Încărcarea datelor despre atribute nu a reușit", "alrAttrFailed": "Încărcarea datelor despre atribute nu a reușit", "alrAttrSaveFailed": "Eroare la salvarea atributului!", "alrAttrsLoadFailed": "Atribuirea încărcării a eșuat", "alrAttachRestored": "Documentul a fost restaurat.", "alrAttachRestoreFailed": "Restaurarea documentului a eșuat.", "alrAttachRestoring": "Restaurare...", "alrAuthMethodsFailed": "Modul de autentificare a eșuat.", "alrBadLogin": "Numele sau parola este incorectă.", "alrBlockedPopups": "Probabil ferestrele pop-up sunt blocate.", "alrCaseDataLoadFailed": "Încărcarea datelor din solicitare nu a reușit.", "alrCaseDeleteFailed": "Ștergerea solicitarii a eșuat.", "alrCaseDeleted": "Solicitarea a fost ștearsa!", "alrCaseDeleting": "Șterg solicitarea...", "alrCaseNameLoadFailed": "Încarcarea numelui solicitarii a eșuat.", "alrCaseNoRights": "Nu ai permisiunea de a vedea numărul solicitarii {{id}}.", "alrCaseNotFound": "Solicitarea nu a fost găsita – probabil că a fost ștearsa.", "alrCaseOverviewFailed": "Încărcarea datelor solicitarii, pentru citire(CASE OVERVIEW) a eșuat.", "alrCaseSuspended": "Solicitarea a fost suspendata!", "alrCaseVarsLoadFailed": "Încărcarea variabilelor procesului a eșuat.", "alrCaseWakeUpFailed": "Restaurarea solicitarii a eșuat.", "alrCaseWakedUp": "Solicitarea a fost restaurata!", "alrCaseWakingUp": "Restaurarea solicitarii...", "alrColsWidthsSettingsFailed": "<PERSON><PERSON><PERSON> setării pentru lățimea coloanelor a eșuat.", "alrConnToServerFailed": "Conectarea la server a eșuat.", "alrConnectionDataLoadFailed": "Conectarea la încărcarea datelor a eșuat.", "alrConnectionDeleteFailed": "Ștergerea cone<PERSON>i a eșuat.", "alrConnectionDeleted": "Conexiunea a fost ștearsă!", "alrConnectionSaveFailed": "<PERSON>var<PERSON> cone<PERSON>i a eș<PERSON>!", "alrContainerNotFound": "Containerul nu a fost găsit.", "alrCsvDownloaded": "Fișierul CSV a fost descărcat!", "alrCvNotFound": "Ansamblul nu a fost găsit.", "alrDashboardSettingsFailed": "<PERSON><PERSON><PERSON> set<PERSON><PERSON> tab<PERSON>i de bord a eșuat.", "alrDefaultDashboardLoadFailed": "Încarcarea implicită a tabloului de bord a eșuat.", "alrDefaultDashboardSaved": "<PERSON><PERSON><PERSON><PERSON> de bord implicit a fost salvat!", "alrDeleteFailed": "A intervenit o eroare în timpul ștergerii.", "alrDeleted": "Ș<PERSON>!", "alrDeleting": "<PERSON><PERSON><PERSON><PERSON>....", "alrDiagramDataLoadFailed": "Încărcarea datelor pentru a crea diagrama a eșuat.", "alrDiagramEditToSave": "Diagrama nu poate fi salvată și transformată în șablon – conține mai mult de un proces! Actualizați diagrama astfel încât să conțină doar un singur proces sau să importați alt fișier .bpmn .", "alrDiagramInitFailed": "Inițializarea diagramei a eșuat.", "alrDiagramMissingTaskName": "Vă rog să complectați numele tuturor sarcinilor.", "alrDiagramErrors": "Diagrama conține erori. Remediați-le și încercați să salvați din nou.", "alrDiagramNotValid": "XML nu este valabilă conform specificațiilor oficiale BPMN 2.0!", "alrDiagramProcessCount": "Diagrama nu poate fi salvată și transformată într-un șablon – conține mai mult de un proces!", "alrDiagramSaveFailed": "A apărut o eroare la salvarea șablonului!", "alrDiagramTsksDeleteFailed": "A apărut o eroare în timpul ștergerii sarcinilor!", "alrDiagramUnchanged": "Șablonul rămâne neschimbat.", "alrDmsColsLoadFailed": "Coloana pentru DMS nu a fost încarcată.", "alrDocsColumnsIdsFailed": "Codurile id pentru coloanele de documente nu au putut fi încărcate.", "alrDocumentAdding": "<PERSON><PERSON> documentul...", "alrDynListsDataLoadFailed": "Încărcarea datelor din listele dinamice a eșuat.", "alrDynTableColsDataFailed": "Datele din coloanele din tabelul dinamic nu au putut fi încărcate.", "alrDynTableDataLoadFailed": "Încărcarea datelor din tabelul dinamic a eșuat.", "alrDynTableNotFound": "Tabelul dinamic nu a fost găsit.", "alrDynTablesDataLoadFailed": "Încărcarea datelor din tabele dinamice a eșuat.", "alrCalcScriptsDataLoadFailed": "Nu s-au putut încărca scripturile de calcul global.", "alrEditValues": "Vă rog să corectați valorile introduse greșit.", "alrEventSaveFailed": "Salvarea evenimentului a eșuat.", "alrEventSaved": "Evenimentul a fost salvat!", "alrEventSaving": "<PERSON><PERSON> even<PERSON>...", "alrEventTriggered": "Evenimentul a fost lansat!", "alrExcelDownloaded": "Fișierul xlsx a fost descărcat!", "alrExportCompleted": "Exportul este complet.", "alrExportFailed": "Exportul a eșuat.", "alrExportPreparing": "Pregatirea exportului...", "alrFailed": "Activitatea a eșuat.", "alrFailedCalendarTask": "Încărcarea sarcinii în calendar a eșuat .", "alrFailedCreatePrint": "Printarea creației a eșuat.", "alrFailedDLTotalCount": "total_countul nu a fost specificat în lista dinamică {{label}}, prin urmare toate înregistrările au fost încărcate.", "alrFailedData": "Încărcarea datelor a eșuat.", "alrFailedEventStart": "Evenimentul a eșuat să pornească.", "alrFailedEventVariables": "Variabilele din evenimentul selectat au eșuat să se încarce.", "alrFailedEvents": "Încărcarea evenimentelor a eșuat.", "alrFailedFoldersData": "Încărcarea datelor din  fisiere a eșuat.", "alrFailedFormData": "Încărcarea datelor formularului a eșuat.", "alrFailedInitiatorName": "Numele inițiatorului a eșuat să se încarce.", "alrFailedLabelData": "Eticheta pentru componente a eșuat să se încarce.", "alrFailedLoad": "Nu s-au încărcat datele de imprimare.", "alrFailedLogicalType": "Logical Type a eșuat să se încarce.", "alrFailedMultiBoxData": "MultiBox data a eșuat să se încarce.", "alrFailedNewCase": "A intervenit o eroare în timpul setării cazului nou!", "alrFailedNewFolder": "Schimbarea numelui de fișier a eșuat.", "alrFailedNoticeData": "Atenție, încărcarea datelor a eșuat.", "alrFailedOrgUnitUser": "Structura organizationala a utilizatorului a eșuat să se încarce", "alrFailedOverviewData": "Overview a eșuat să se încarce.", "alrFailedPlanData": "Plan data a eșuat să se încarce.", "alrFailedPostData": "Trimiterea datelor a eșuat.", "alrFailedPrintData": "Nu este posibilă încărcarea datelor de imprimare.", "alrFailedRevisionInfo": "Noile informații de revizuire au eșuat să se încarce.", "alrFailedSearchBoxData": "Datele SearchBox au eșuat să se încarce.", "alrFailedSelectBoxData": "Componentele SelectBox au eșuat să se încarce.", "alrFailedSuggestBoxData": "<PERSON><PERSON> suggester au eșuat să se încarce.", "alrFailedTasColors": "Culorile TAS au eșuat să se încarce!", "alrFailedTaskHandOver": "Transmiterea actiunii a eșuat.", "alrFailedTemplateProcesses": "Șabloanele de solicitari nu au putut fi încărcate.", "alrFailedVarData": "Nu s-au încărcat date variabile.", "alrFavouriteRenamed": "Favorit a fost redenumit.", "alrFileAdded": "Fișierul a fost adăugat!", "alrFileDeleteFailed": "Ștergerea fișierului a eșuat.", "alrFileDonwload": "<PERSON><PERSON><PERSON>...", "alrFileDownloaded": "Fișierul a fost descărcat!", "alrFileInfoFailed": "Informațiile fișierului au eșuat să se încarce.", "alrFileMetaSaveFailed": "Metadatele fișierelor nu au putut fi salvate.", "alrFileSavedLikeAttach": "Fișierul a fost salvat ca document.", "alrFileUploadFailed": "Încărcarea fișierelor a eșuat.", "alrFillAllRequired": "Pentru a finaliza actiunea, trebuie completate toate datele necesare!", "alrFillData": "Pentru a atribui actiunea, toate datele trebuie completate corect!", "alrFillDataInRightFormat": "Completați datele în formatul corect.", "alrFillDataToCompleteTsk": "Pentru a finaliza actiunea, toate datele trebuie completate corect!", "alrFillNameAndPass": "Vă rugăm să completați numele și parola.", "alrFillNote": "Vă rugăm să completați textul notei.", "alrFillRequiredItems": "Vă rugăm completați elementele necesare.", "alrFolderDataFailed": "Încărcarea datelor din foldere  a eșuat.", "alrFolderDataLoadFailed": "Încărcarea datelor din folder a eșuat.", "alrFolderFailed": "Încărcarea informațiilor despre dosar a eșuat.", "alrFolderSaveFailed": "Do<PERSON>ul nu a putut fi salvat!", "alrFoldersLoadFailed": "Dosarele nu au putut fi încărcate.", "alrHelpSettingsSaveFailed": "Setările de ajutor nu au putut fi salvate.", "alrHistoricalTskInfoFailed": "Informație încărcătură sarcină istorică a eșuat.", "alrHistoricalVarsSaveFailed": "Variabilele istorice nu au putut fi salvate.", "alrHistoricalVarsSaved": "Variabilele istorice ale sarcinii au fost salvate.", "alrInvLogginHash": "Logare incorectă.", "alrJsonFailed": "JSON nu este valabil!", "alrLackOfPermsToEdit": "Nu ai permisiune de editare! Proprietarul are", "alrLackOfPermsToSleepCase": "Nu ai permisiunea de a suspenda solicitarea.", "alrLackOfPermsToWakeUpCase": "Nu ai permisiunea de a restaura solicitarea.", "alrLastHistoricalTskIdFailed": "Id de ultima actiune istorică a eșuat să se încarce.", "alrLoadAttachmentsFailed": "Încărcarea documentelor a eșuat.", "alrLogOutFailed": "Ieși din cont a e<PERSON><PERSON>t.", "alrLoginExpired": "Conexiunea a expirat, vă rog să vă reconectați.", "alrMappingFailed": "Maparea nu a putut fi salvată.", "alrMappingTsksFailed": "Activitățile de mapare nu au putut fi încărcate.", "alrNewCaseBased": "A fost creata o noua solicitare!", "alrNewFolder": "A fost creat un nou fisier.", "alrNewFolderFailed": "<PERSON><PERSON><PERSON> unui fisier nou a e<PERSON><PERSON>t.", "alrNextTskOpened": "Următoarea actiune a fost deschisă.", "alrNoDataToPrint": "Nu au fost găsite date pentru imprimare.", "alrNoteAdded": "Nota a fost adăugată!", "alrNoteSaveFailed": "<PERSON>var<PERSON> notei a eșuat.", "alrNoteSaving": "<PERSON><PERSON><PERSON> notei...", "alrNotesLoadFailed": "Încărcarea notelor de caz a eșuat.", "alrOrgUnitDataFailed": "Unitatea de încărcare a datelor organizației a eșuat.", "alrOrgUnitDeleteFailed": "Ștergerea unității organizației a eșuat.", "alrOrgUnitDeleted": "Unitatea organizației a fost ștearsă!", "alrOrgUnitDeleting": "Ștergerea unității org.  ...", "alrOrgUnitSaveFailed": "Salvarea unității organizației a eșuat.", "alrOrgUnitSaved": "Unitatea de organizare a fost salvată!", "alrOrgUnitSaving": "Salvarea organizației...", "alrOverviewDataLoadFailed": "Încărcarea datelor de ansamblu a eșuat.", "alrOverviewSaveFailed": "Salvarea an<PERSON>lului a eșuat", "alrOverviewSaveSameNameFailed": "Numele ansamblului este deja folosit de un alt utilizator, alegeți un nume de ansamblu diferit.", "alrGraphSaveSameNameFailed": "Numele diagramei este deja folosit de un alt utilizator, alegeți un nume de diagramă diferit.", "alrReportSaveSameNameFailed": "Numele raportului este deja folosit de un alt utilizator, alegeți un nume de raport diferit.", "alrOverviewsLoadFailed": "Încărcarea ansamblului a eșuat.", "alrPassSaveFailed": "Salvarea parolei a eșuat.", "alrPassSaved": "Parola a fost salvată!", "alrPlanReqItems": "Pentru a salva planul, completați elementele necesare.", "alrPlanSaveFailed": "Salvarea planului a eșuat.", "alrPlanSaved": "Planul a fost salvat!", "alrPreparingPrint": "Pregătirea <PERSON>ăririi...", "alrPrintDeleteFailed": "Ștergerea imprimarii a eșuat.", "alrPrintDeleted": "Imprimarea a fost ștearsă!", "alrPrintSaveFailed": "Salvarea imprimarii a eșuat.", "alrPrintSaved": "Se salvează Imprimarea a fost salvată!", "alrReadOnlyCaseDataFailed": "Încărcarea datelor din caz pentru citire a eșuat.", "alrRecalcFailed": "E<PERSON>re în timpul recalcularii!", "alrRecalculating": "Recalculare...", "alrRestorTemplFailed": "Restaurarea șablonului nu a reușit.", "alrRoleDataLoadFailed": "Încărcarea datelor referitoare la roluri eșuat.", "alrRoleDeleteFailed": "Ștergerea rolurilor a eșuat.", "alrRoleDeleted": "Rolul a fost șters!", "alrRoleDeleting": "Rolul se șterge ...", "alrRoleSaveFailed": "Salvarea rolului a eșuat.", "alrRoleSaved": "Rolul a folst salvat!", "alrRoleSaving": "Se salvează rolul...", "alrRunEvent": "Lansarea evenimentului...", "alrSaveFailed": "Salvarea a eșuat.", "alrSaved": "<PERSON>vat!", "alrSaving": "In curs de salvare...", "alrSavingBeforeRecalcFailed": "Eroare la salvare înainte de recalculare!", "alrSavingFailed": "Eroare intervenita în timpul salvării!", "alrSavingPlan": "Salvarea plan...", "alrSavingPrint": "<PERSON>var<PERSON>i...", "alrSearchNoResults": "Nu s-au găsit rezultate corespunzătoare parametrilor de căutare.", "alrSearchRequestFailed": "Eroare intervenită în timpul trimiterii!", "alrSearching": "<PERSON><PERSON><PERSON><PERSON>...", "alrSettFailed": "Setările pot să nu fie salvate.", "alrSettSaved": "Setarile au fost salvate.", "alrSettingsLoadFailed": "<PERSON><PERSON> de setare au eșuat să se încarce.", "alrSleepCaseFailed": "Suspendarea solicitarii a eșuat.", "alrStoreNameNotDefined": "Depozitul \"{{storeName}}\" nu este definit.", "alrStoreNotDefined": "Depozitul nu a fost definit.", "alrSubActionNotDefined": "SubAction și suffix au fost definite.", "alrSubStoreNotDefined": "SubStore și suffix trebuie să fie definite.", "alrSuggestBoxDataNotContains": "<PERSON><PERSON> din casuța de sugestii\"{{label}}\" nu conține \"{{prop}}\"!", "alrSuspendingCase": "Suspend solicitare...", "alrTableDataFailed": "<PERSON><PERSON><PERSON> datelor din tabel a e<PERSON><PERSON>t.", "alrTasNewVersion": "O noua versiune, a aplicației, a fost găsită .", "alrRefresh": "Dacă este necesar sa {{- spanRefresh}} pagina in browser.", "alrTasVersionLoadFailed": "Incarcarea versiunii aplicației a eșuat!", "alrTaskHandOver": "Predarea actiunii...", "alrTaskHandedOver": "Actiunea a fost predată utilizatorului:", "alrTaskNoRights": "Nu aveți permisiuni pentru a vedea actiunea nr. {{id}}.", "alrTaskNotFound": "Actiunea nu a fost găsită.", "alrTempDataLoadFailed": "Datele șabloanelor nu s-au încărcat.", "alrTempHeadersLoadFailed": "Antetele de șabloane nu s-au încărcat.", "alrTempDeleteFailed": "Ștergerea șablonului a eșuat.", "alrTempDeleted": "Șablonul a fost șters!", "alrTempFoldersLoadFailed": "Dosarele de șabloane nu s-au încărcat.", "alrTempNameLoadFailed": "Numele de șablon nu s-a încărcat.", "alrTempRestored": "Șablonul a fost restaurat cu starea In curs de dezvoltare.", "alrTempSaveFailed": "Salvarea modelului a eșuat.", "alrTempsLoadFailed": "Salvarea <PERSON>lor a eșuat.", "alrTempVarDataLoadFailed": "<PERSON>le variabilelor șablon nu s-au încărcat.", "alrTempVarSaveFailed": "Salvarea variabilă a eșuat.", "alrTempVarsLoadFailed": "Variabilele șablon nu s-au încărcat.", "alrTotalCountFailed": "Înregistrările totale înregistrate în tabel au eșuat.", "alrTreeDataFailed": "Încărcarea datelor cu arbore a eșuat.", "alrTskAddFailed": "Adaugarea actiunii a eșuat.", "alrTskAdded": "Actiunea a fost adaugată!", "alrTskAdding": "Ada<PERSON>re actiune...", "alrTskAssignFailed": "Activarea actiunilor a eșuat.", "alrTskAssigned": "S-a alocat o actiune.", "alrTskCompleteFailed": "Eroare la finalizarea actiunii.", "alrTskDataLoadFailed": "Datele actiunii nu au fost încărcate.", "alrTskDeleteFailed": "Ștergerea actiunii a eșuat", "alrTskDeleted": "Actiunea a fost ștearsă!", "alrTskNameLoadFailed": "Nu s-a putut încărca numele actiunii.", "alrTskRecalculated": "Numele actiunii nu s-a încărcat!", "alrTskSaveFailed": "Eroare la salvarea actiunii.", "alrTskSaved": "Actiune salvată!", "alrTskSavedAndCompleted": "Actiune salvata si completată!", "alrTskScheduleFailed": "Programarea actiunilor a eșuat.", "alrTskScheduled": "Actiunea a fost programată.", "alrTskTakeFailed": "Preluarea actiunilor a eșuat.", "alrTskTaken": "S-a preluat actiunea.", "alrTskTakign": "Se preia actiunea...", "alrTsksMappingFailed": "Lucrările de mapare nu au putut fi încărcate.", "alrUserDataLoadFailed": "Informațiile utilizatorului au eșuat.", "alrUserDeleteFailed": "Ștergerea utilizatorului a eșuat.", "alrUserDeleted": "Utilizatorul a fost șters!", "alrUserDeleting": "Ștergerea utilizatorului...", "alrUserIsNotActive": "Utilizatorul nu este activ.", "alrUserNotLoaded": "Utilizatorul a eșuat să se încarce.", "alrUserParamsLoadFailed": "Parametrii utilizatorului nu s-au încărcat.", "alrUserSaveFailed": "Salvarea utilizatorului a eșuat.", "alrUserSaved": "Utilizatorul a fost salvat!", "alrUserSaving": "Salvarea utilizatorului...", "alrUserStatusChangeFailed": "Modificarea statutului utilizatorului a eșuat.", "alrUserStatusChanged": "Statutul utilizatorului a fost schimbat!", "alrUserStatusChanging": "Modificarea statutului utilizatorului...", "alrVarDeleteFailed": "Ștergerea variabilei a eșuat.", "alrVarDeleted": "Variabila a fost ștearsă!", "alrVarSaveFailed": "Salvarea variabilei a eșuat.", "alrVarSaved": "Variabila a fost salvată.", "alrVarSaving": "Variabila se salvează...", "alrVarsForModalFilterFailed": "Modificarea încărcării variabilelor de filtrare a eșuat.", "alrVarsLoadFailed": "Variabilele nu au putut fi încărcate.", "alrVarsOrderLoadFailed": "Variabilele încărcării pentru comandă a eșuat", "alrVarsOrderSaveFailed": "Salvarea ordinii variabilelor a eșuat.", "alrViceDeleted": "Vice-ul a fost anulat.", "alrViceFailed": "Vice-ul nu a avut succes.", "alrViceNotFound": "Nu s-a g<PERSON><PERSON><PERSON> vice-ul!", "alrViceSaveFailed": "Vice nu a putut fi salvat.", "alrViceSaved": "Vice a fost salvat!", "alrViceSaving": "Vice-ul se salveaza...", "always": "<PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON>", "assHierarchy": "Relația cu persoana de referință", "assHierarchyAncestors": "Toți superiorii persoanei de referință", "assHierarchyChildren": "Subordonați direcți ai persoanei de referință", "assHierarchyDescendants": "Toți subordonații persoanei de referință", "assHierarchyGuarantor": "Numai persoana de referință", "assHierarchyParent": "Superiorul direct al persoanei de referință", "assHierarchySiblings": "Colegii persoanei de referință", "assMethodAutomatic": "De calculatorul meu", "assMethodLastSolver": "Pentru ultimul proprietar de sarcină", "assMethodLastSolverChoice": "Administrator de activități selectat de cel mai recent proprietar de sarcini", "assMethodLeast": "Administrator de actiuni cu cel mai mic număr de activitati", "assMethodPull": "<PERSON><PERSON><PERSON>, actiunile vor fi oferite", "assMethodSelect": "Responsabilii de actiuni selectați de supervizorul actiunilor", "assMethodVariable": "Responsabil de actiune, din variabilă", "assessmentOfConds": "Evaluarea condițiilor", "assign": "A atribui", "assignAttrs": "Alocare atribuite", "assignAttrsLogType": "Alocarea atributelor tipului de document logic", "assigned": "Alocare", "assigningRoles": "Alocarea rolurilor", "assignments": "<PERSON><PERSON><PERSON><PERSON>", "attachments": "Documente", "attachmentsList": "Lista documentelor", "attribute": "Atribut", "attributeNew": "Atribut – Nou", "availableVars": "Variabilele disponibile", "body": "Body/corp", "borders": "Graniţe/limite", "byFolders": "<PERSON><PERSON><PERSON>", "byOrganization": "După organizație", "byRole": "După rol", "calculation": "Calculare", "calculations": "Cal<PERSON>ții", "calendar": "Calendar", "carriedIfNoOther": "Se va efectua dacă nu sunt altele", "case": "Solicitare", "caseCreation": "<PERSON><PERSON><PERSON> solicitare", "caseGraph": "Diagrama instanțelor", "caseNoEvents": "Solicitarea nu conține evenimente.", "caseNum": "Num<PERSON><PERSON><PERSON> solicitarii", "caseOwner": "Deținatorul solicitarii", "caseStatus": "Status-ul solicitarii", "caseVar": "solicitare", "cases": "Solicitari", "casesWithProblem": "Solicitarea mea cu o problemă", "category": "Categorie", "changeTaskSolver": "Schimbă proprietarul actiunii", "changedBy": "<PERSON><PERSON><PERSON><PERSON> după", "changedWhen": "<PERSON><PERSON><PERSON><PERSON> (când)", "checkbox": "Căsuță de bifat", "checkboxList": "Lista căsuțelor de bifat ", "choosePrint": "Șablon de imprimare", "chooseUserToAssignTsk": "Alegeți utilizatorul care urmează să fie atribuit actiunii", "choosenAttrs": "Alege atributele", "city": "Oraș", "class": "Clasă", "clickToClose": "Închideți făcând clic", "clickToRefresh": "Faceți clic pentru a actualiza pagina în browser", "clickToRepeat": "Repetați acțiunea dând clic pe", "clientLanguage": "Limba clientului", "cloneRow": "Duplicați linia", "close": "<PERSON><PERSON><PERSON>", "closeAll": "<PERSON><PERSON><PERSON> tot", "coWorkersOfPlanGuar": "Colaboratori ai garantului de plan", "color": "<PERSON><PERSON><PERSON>", "colors": "<PERSON><PERSON><PERSON>", "column": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON>le colo<PERSON>i", "comment": "<PERSON><PERSON><PERSON><PERSON>", "complete": "Completat", "completion": "Completare", "componentDescription": "<PERSON><PERSON><PERSON><PERSON>", "condition": "Condiție", "conditions": "Condiții", "confirmAttachDeletion": "Sunteți sigur că doriți să ștergeți documentul?", "confirmDeleteDialog": "Sunteți sigur că doriți să ștergeți {{variable}}?", "confirmDialogEventSave": "Pentru a comuta, este necesar să salvați evenimentul. Vrei să-l salvezi?", "confirmResetDashboard": "Chiar doriți să resetați tabloul de bord?", "confirmSaveChanges": "Salvați modificările dvs.?", "confirmSaveDiagramChanges": "Salvați modificările în diagramă?", "confirmSaveTaskChanges": "Salvați modificările în actiune?", "confirmRestoreDialog": "Sunteți sigur că doriți să restabiliți {{variable}}?", "confirmSaveNote": "<PERSON><PERSON><PERSON>i să salvați o notă?", "confirmSleepCase": "Chiar vrei să suspenzi solicitarea?", "confirmTakeoverTsk": "Sunteți sigur că doriți să preluați actiunea?", "confirmWakeUpCase": "Sunteți sigur că doriți să  incetati suspendarea cazului?", "connection": "<PERSON><PERSON>", "connectionFailed": "Conectarea la server a eșuat.", "connectionVar": "link", "constant": "Constant", "contact": "A lua legatura", "contactTaskOwner": "Contactați proprietarul sarcinii", "containerSettings": "Setările pentru containere", "contains": "Condiție", "continueSolving": "Continuați în soluție", "copied": "Copiat!", "copy": "Copiază", "copyShortcut": "Apasă Ctrl+C", "copyToClipboard": "Copiază în clipboard", "createForm": "Crează formular", "csv": "csv", "csvFile": "Fișier CSV ", "customPrint": "Imprimare <PERSON>", "daily": "Zilnic", "dashCvNoOverview": "Nu este selectată nicio prezentare generală – o alegeți în setările pentru container", "dashCvNoRights": "Nu aveți permisiuni de vizualizare a ansamblul, vă rugăm să contactați administratorul.", "dashFavNoShortcut": "No vices selected – you choose them in the container settings", "dashboard": "<PERSON><PERSON><PERSON>ord", "date": "Dată", "dateList": "Date de LOV", "day": "<PERSON><PERSON>", "dayE": "<PERSON><PERSON>", "daysDHM": "Zile: (dd:hh:mm)", "defChangeVarInfluence": "Această schimbare de definiție variabilă se va propaga în solicitari deja create.", "defChangeInfluence": "Această modificare a definiției se va propaga în solicitari deja create.", "defaultCaseName": "Numele solicitarii implicite", "defaultLbl": "Mod implicit {{label}}", "defaultPrintProcess": "Mod implicit – proces", "defaultPrintTask": "Mod implicit – actiune", "defaultValue": "Valuare mod implicit", "delUser": "Șterge utilizatorul", "delete": "Șterge", "deleteCol": "<PERSON><PERSON><PERSON> coloana", "deleteRow": "Șterge linia", "deleteSelected": "Șterge ce este selectat", "deleted": "<PERSON><PERSON>", "deletedOn": "<PERSON><PERSON>", "deletedShe": "<PERSON><PERSON>", "description": "Des<PERSON><PERSON><PERSON>", "deselect": "Deselectează", "detail": "<PERSON><PERSON><PERSON>", "developed": "In curs de dezvoltare", "dial": "Cadran", "dic": "TVA", "directSubOfPlanGuar": "Subordonat direct al garantului planului", "directSupOfPlanGuar": "Superior direct al garantului planului", "disableFilter": "Dezactivați filtrul", "dmsAssignAttrs": "Atribuirea atributului DMS", "dmsAttribute": "Atributul DMS", "dmsAttributes": "Atribute DMS", "dmsColumns": "DMS – coloane", "dmsVisNull": "<PERSON>umai în acest proces", "dmsVisSub": "În subprocese", "dmsVisSup": "În procesul mamă", "dmsVisSupSub": "În procesele părinte și subordonate", "dmsVisibility": "Documentele vor fi văzute", "doNotShowVariablesWith_": "Nume variabilelor ce încep cu `_` nu vor fi afișate utilizatorilor", "document": "Document", "documentVar": "document", "documents": "Documente", "doesNotContain": "<PERSON><PERSON>", "done": "Realizat", "download": "Des<PERSON><PERSON><PERSON>", "dragAddFile": "Adăugați fișier prin glisare și plasare sau faceți clic pe {{- here}} pentru selectarea fișierelor.", "dragAddFiles": "Adăugați fișiere prin glisare și plasare sau faceți clic pe {{- here}} pentru selectarea fișierelor.", "dropContainer": "Trageți recipientul", "dropzoneTip": "Aruncați fișierele aici", "dropZoneUserPhoto": "<PERSON><PERSON><PERSON> o imagine aici sau faceți clic pentru a selecta un fișier pentru încărcare.", "dueDate": "Data scadentă", "duty": "Responsabilitate", "dynList": "Listă Dyn. ", "dynRowsDefinition": "Definiția tabelului și coloanelor", "dynTableName": "Numele dinamic al tabelului", "dynTable": "<PERSON><PERSON> dinamic", "dynTables": "<PERSON><PERSON><PERSON> dinamice", "dynamicList": "Listă dinamică", "dynamicRows": "<PERSON><PERSON><PERSON><PERSON>", "dynamicTable": "Table dinamice", "edit": "Editează", "editAttribute": "Editează atribute", "editOrgUnit": "Editează org.unit", "editRole": "Editează rol", "editRule": "Editează regula", "editUser": "Editează utilizator", "editor": "Editor", "email": "E-mail", "emailsQueue": "Coada de e-mailuri", "empty": "Gol", "end": "Sfârșit", "error": "Eroare", "errored": "Cu o eroare", "error404": "Eroare 404 – pagina nu a fost găsită!", "event": "Eveniment", "events": "Evenimente", "eventsRun": "Rulează eveniment", "every": {"masc": "<PERSON><PERSON><PERSON>", "neutral": "<PERSON><PERSON><PERSON>", "repeat": "<PERSON><PERSON><PERSON>"}, "everyWorkDay": "Toate zilele l<PERSON><PERSON>", "excel": "Excel", "favourites": "Favorite", "fax": "Fax", "file": "<PERSON><PERSON><PERSON>", "fileLogicalType": "Tip de <PERSON> logic", "fileName": "Numele fișierului", "filePlacement": "Plasarea în fișier", "files": "Fișiere", "filter": "filtru", "filterFrom": "Filtrează din", "filterTitle": "Filtre", "filtrate": "Filtru", "finishTask": "Termină actiune", "finished": "Terminat", "finishedBy": "Terminat de", "finishedOn": "Terminat", "first": "P<PERSON>ul", "firstLeft": "Primul stanga", "firstName": "Primul nume", "firstRight ": "P<PERSON><PERSON> dreapta", "firstRowColumnsName": "Prima linie conține nume de coloane", "folder": "<PERSON><PERSON><PERSON>", "folder-": "Dosar –", "folderExecRightsText": "Atribuiți roluri care vor putea iniția solicitari în dosar", "folderExecRightsTextOS": "Atribuiți unitățile organizaționale care vor putea iniția solicitari în dosar", "folderName": "<PERSON><PERSON>le dosarului", "font": "Font", "fontMainHeader": "Font-ul din antetul principal", "form": "Formă", "fourth": "Al patrulea", "freeTsk": "Sarcină liberă", "fri": "<PERSON><PERSON>", "from": "<PERSON>", "fsDescription": "desc<PERSON><PERSON>", "fsName": "nume", "fsTooltip": "tooltip", "fullName": "Numele complet", "fullScreen": "Ecran <PERSON>ntreg", "getTotalCount": "Num<PERSON><PERSON><PERSON><PERSON><PERSON>ă", "graph": "<PERSON><PERSON>", "handExecutionTaskListEmpty": "Alegeți evenimentul", "handOver": "Transfer", "handOverToUser": "Transfera utilizatorului", "handover": "Transfera", "headerDashboard": "Antetul tabloului de bord", "help": "<PERSON><PERSON><PERSON>", "hideLogout": "Ascundeți delogarea ", "hideNewProcess": "Ascundeți 'solicitare noua'", "hideProcs": "Ascundeți solicitareal", "hideTasks": "Ascundeți solicitarea", "historicalValues": "Valori istorice", "currentValues": "Valori reale", "history": "Istoric", "home": "Acasă", "html": "HTML", "ic": "ID-ul  companiei", "id": "ID", "inCasesNames": "În numele solicitarilor", "inTasksNames": "În numele actiunilor", "inDevelopment": "În dezvoltare", "inEvery": "În toate", "inFiles": "În <PERSON><PERSON>e", "initiator": "Iniţiator", "inTasks": "În actiuni", "inactive": "Inactiv", "incidences": "Evenimente", "inclusion": "Includere", "info": "Informație", "inputParams": "Parametrii de intrare", "insert": "Introduceți", "insertAttachTip": "Trageți și aruncati pentru a insera un document", "insertVar": "Introduceți variabila", "insertedBy": "încărcat de", "insertedOn": "Adă<PERSON><PERSON>", "insteadOf": "în schimbul a", "instructions": "Instrucțiuni", "invitation": "Invitație", "isEmail": "Nu este un e-mail valid", "isEmpty": "este gol", "isExisty": "Nu este valid", "isManagerOrgUnit": "Este manager-ul org. unit", "isNotEmpty": "nu este gol", "isRequired": "<PERSON>ste ne<PERSON>ar", "justSave": "<PERSON><PERSON>", "keepGlobalOrder": "Ține ordinea globală", "key": "<PERSON><PERSON><PERSON>", "last": "ultimul", "lastName": "Numele de familie", "lastOwnerOfTask": "Ultimul proprietar al actiunii", "licenceKey": "Cheie de licență", "link": "Link", "linkConditions": "Condițiile link-ului ", "list": "Listă", "listName": "Numele listei", "listOfValues": "Listă de valori", "listValue": "Valoarea listei", "loading": "încărcare...", "location": "Locație", "locked": "Blocat", "logIn": "<PERSON><PERSON><PERSON>", "logOut": "Ieși din cont", "logicalType": "Tipul logic", "loginError": "Logare incorectă.", "loginTimeout": "Se<PERSON><PERSON>a a expirat (sec.)", "longText": "Text lung", "mailEscalation": "E-mail cu prezentarea generală a actiunilor escaladate", "mailProcEscalation": "E-mail cu prezentarea generală a cazurilor escaladate", "mailPromptly": "Notificari prin e-mail pentru actiuni noi", "mailPull": "Notificare prin e-mail pentru actiuni noi de preluat", "mailTotal": "Rezumat e-mail cu prezentarea generală a actiunilor", "mainButton": "Butonul principal", "mainColor": "Culoare principala", "mainHeader": "Antetul principal", "mainLanguage": "Limba principală", "manager": "Manager", "managerOfOrgUnit": "Manager al unitatilor organizationale", "mandatory": "Obligator<PERSON>", "manualStartEvent": "Start manual de evenimente", "mapping": "Mapare", "mappingSubProcessVars": "Cartografierea de variabile sub-proces", "markAll": "Marchează-le pe toate", "menu": "<PERSON><PERSON>", "mine": "A mea", "mobilePhone": "Telefon mobil", "mon": "<PERSON><PERSON>", "month1": "<PERSON><PERSON><PERSON>", "month10": "<PERSON><PERSON><PERSON>", "month11": "Noiembrie", "month12": "Decembrie", "month2": "<PERSON><PERSON><PERSON><PERSON>", "month3": "<PERSON><PERSON>", "month4": "<PERSON><PERSON>", "month5": "<PERSON>", "month6": "<PERSON><PERSON><PERSON>", "month7": "<PERSON><PERSON><PERSON>", "month8": "August", "month9": "Septembrie", "monthI": "Lună", "monthly": "Lunar", "months": "<PERSON><PERSON> (adic<PERSON> luna Mai, <PERSON><PERSON><PERSON>, etc))", "more": "<PERSON> mult", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingur", "multiBoxTriple": "MultiBoxTriplu", "multiInstance": "Instanțe multiple", "myUnfinishedTasks": "Actiunile mele neterminate", "name": "Nume", "nested": "<PERSON><PERSON><PERSON><PERSON>", "never": "niciodată", "new": "Nou", "newCase": "Solicitare noua", "newFolder": "Dosar <PERSON>", "newForm": "Formular nou", "newIt": "Nou", "newName": "Nume nou", "newShe": "Nou", "newSolver": "Responsabil de actiune noua", "no": "<PERSON>u", "noAttach": "Nu există documente (face<PERSON>i clic pentru a adăuga)", "clickToAddAttach": "<PERSON><PERSON><PERSON> clic pentru a adăuga", "noName": "Fără nume", "noOneBeOffered": "<PERSON><PERSON><PERSON>, actiunea va fi oferită unui grup restrâns de utilizatori", "noPageRights": "Nu ai permisiunea de a vedea această pagină", "node": "Nod", "notFound": "Nu a fost găsit", "notMatch": "Nu se potrivesc", "notNumber": "Nu este un numar", "notIntNumber": "Nu este un număr întreg", "notValid": "Nu este valid", "notes": "Notițe", "notesOnContacts": "notițe pe contact", "notice": "Înștiințare", "notification": "Înști<PERSON><PERSON><PERSON><PERSON>", "nrOfItems": "<PERSON><PERSON><PERSON><PERSON><PERSON> de obiecte", "number": "<PERSON><PERSON><PERSON><PERSON>", "numberList": "LOV de numere", "ok": "<PERSON>e", "oneMustBeMet": "Trebuie să fie îndeplinită cel puțin una", "onlyOrgUnit": "Numai unitate organizațională", "onlyPlanGuarantor": "<PERSON><PERSON> garan<PERSON>l", "openAll": "Deschideți tot", "operating": "Activează", "order": "Ordine", "orderByColumn": "Ordonează după coloană", "orgName": "<PERSON><PERSON><PERSON> subiectului", "orgStructure": "Structura Org. ", "orgUnit": "unitate org. ", "orgUnitE": "unitate org.", "orgUnitName": "Numele unității Org.", "orgUnitShe": "Unitate Org.", "orgUnits": "Unități Org.", "organization": "Organizare", "overview": "Privire <PERSON> ansa<PERSON>lu", "overviewMapping": "Prezentare generală", "overviewNew": "Ansamb<PERSON> nou", "overviewSetSharing": "Seteaza partajarea ansamblului pentru fiecare grup de utilizatori", "overviews": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerWithLeastTasks": "Responsabil cu cel mai mic număr de actiuni", "pageNotFound": "Pagina nu a fost gasită", "parentFolder": "<PERSON><PERSON><PERSON> ma<PERSON>", "parentUnit": "Unitate mamă", "participants": "Participanți", "password": "Pa<PERSON><PERSON>", "passwordChallenge": "Notificari", "passwordChallengeText": "Chiar vrei să anunțe toți utilizatorii să-și schimbe parola?", "passwordChange": "Sc<PERSON>b<PERSON> parola", "passwordCheck": "<PERSON><PERSON><PERSON> (verifică)", "passwordNew": "Parolă nou<PERSON>", "passwordNewCheck": "<PERSON><PERSON><PERSON> nou<PERSON> (verifică)", "paused": "Inactiv", "personInOrgStr": "Atribuit de către perso<PERSON> in org. structură", "phone": "Telefon", "photo": "Fotografie", "plan": "plan", "planGuarantor": "Garantul planului", "planTitle": "Plan", "plans": "<PERSON><PERSON>", "plnOffType": "Repetă", "plnOrgUnit": "Unitate organizatională", "plnTProc": "Șablon de solicitare", "plnUser": "Planificați sponsorul", "plnUsersSelect": "Condiții restrictive pentru selectarea unuia sau mai multor inițiatori", "prependTsk": "Actiune pregatită", "prependedTsk": "Actiune pregatită", "primaryKey": "<PERSON><PERSON><PERSON> pre<PERSON>", "print": "Imprimă", "printTemplate": "Imprimați șablonul", "printType": "Tip de imprimare", "printer": "Imprima – HTML", "priority": "Prioritar", "procDescription": "Descrierea procesului", "procDueDateFinish": "Data scadentă pentru finalizarea solicitarii", "procName": "Numele solicitarii", "procOwner": "Deținătorul procesului", "procSummary": "În proces de rezolvare", "process": "Proces", "processName": "Numele procesului", "property": "Proprietate", "quickFilter": "Filtrare rapidă", "radioButtonList": "Listarea optiunilor pentru selectare", "reEvaluates": "<PERSON><PERSON><PERSON><PERSON>", "recalc": "Recalculează", "recipient": "Destinatar", "recipientsId": "ID-ul destinatarului", "records": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "referenceUser": "Persoană de referință", "refresh": "Actualizare", "registered": "inregistrat", "relatToPlanSponsor": "Relația cu planul sponsorului", "remove": "Elimină", "removeVice": "<PERSON><PERSON><PERSON> vice-ul", "renameCols": "Redenumiți coloanele", "repeatLogin": "Reintroduceți datele de conectare sau alegeți alt tip de autentificare.", "repeatOrReport": "Încercați din nou mai târziu sau contactați administratorul.", "repetition": "Repetare", "required": "Obligator<PERSON>", "reset": "Resetează", "restrictTaskOwners": "Restricții pentru responsabilii de actiuni", "restrictUsers": "Restricționați utilizatorii", "returnSubProcessVars": "Întoarcerea variabilelor subprocesului", "revision": "Revizie", "right": "<PERSON><PERSON><PERSON>", "rightOrDuty": "Dreapta / Responsabilitate", "role": "rol", "roleName": "Numele rolului", "roleSg": "Rol", "roles": "<PERSON><PERSON><PERSON>", "row": "<PERSON><PERSON><PERSON><PERSON>", "rule": "<PERSON><PERSON>", "ruleCSVFile": "Numele fișierului CSV", "ruleCSVHeader": "Prima linie a fișierului CSV este un antet", "ruleCSVMask": "Mască nume de fișier CSV", "ruleCSVSeparator": "Separatorul de coloană", "ruleNew": "Regulă – Nou", "ruleParamsMap": "Maparea variabilelor", "ruleProcOwnCSV": "Definit în mapare", "ruleTypeCSVExpProcs": "CSV Export toate șabloanele", "ruleTypeCSVMrgProcs": "În funcție de CSV, executați solicitarile și actualizați variabilele de solicitari", "ruleTypeCSVRunProcs": "Potrivit CSV rulați solicitarile", "ruleTypeCSVUpdProc": "Potrivit CSV actualizați variabilele solicitarii", "ruleTypeCSVUpdProcs": "Potrivit actualizării CSV, variabilele de solicitari", "ruleTypeCSVUpdateList": "Actualizați lista dinamică în funcție de CSV", "ruleTypeReturn": "Răspunsul evenimentului", "ruleTypeUpdateListOfProcesses": "Actualizați lista dinamică a proceselor", "rules": "<PERSON><PERSON>", "run": "A rula", "runProcess": "Pornește procesul", "running": "Rulează", "sat": "Sâmbătă", "save": "Salvează", "saveAsAttachment": "Salvați imprimarea ca document la caz", "scheduling": "Programare", "scheme": "Identitate vizuală", "script": "<PERSON><PERSON><PERSON><PERSON>", "scripts": "Sc<PERSON><PERSON><PERSON>", "search": "Caut<PERSON>", "searchResult": "Caută rezultatul", "second": "<PERSON>", "secondLeft": "Al doilea din stanga", "secondRight": "Al doilea din dreapta", "selectBox": "Căsuța selectată", "selectDrop": "Selectați drop", "selectedByComputer": "Pentru responsabilii de actiuni selectați automat de către computer", "selectedByTaskSupervisor": "Responsabilii de actiuni selectați de către supraveghetor", "selectedPrint": "selectați imprimarea", "selectedUser": "Utilizatorul selectat", "send": "Trimite", "sendingFailed": "Eroare", "sendOn": "Timpul să fie trimis", "sendTestMail": "Test e-mail", "sequence": "Secvenţă", "setDefault": "Setaţi ca predefinit", "setVice": "Setează vice", "setViceAttachmentsNotes": "Dreptul de a încărca documente și note", "settings": "<PERSON><PERSON><PERSON>", "shortcuts": "Scurtatură", "showAttachmentsClick": "Dacă dai clic pe el, vei arăta documente", "showCommentCol": "Afișați coloana comentariu", "skype": "Skype", "solve": "Rezolvă actiunea", "solvedBy": "Rezolvat de", "solver": "Responsabilul actiunii", "sort": "Sortează", "sortByColumn": "Sortați după coloană", "sorting": "Sortează", "sourceTask": "Sursa actiunii", "sourceVar": "Sursă variabilă", "start": "Start", "startDate": "Dată de start", "state": "Stat", "stateAddress": "Stat", "status": "Status", "street": "ad<PERSON><PERSON> s<PERSON>", "subProcess": "Subproces", "subject": "Subiect", "substitute": "Substitut", "sun": "<PERSON><PERSON><PERSON><PERSON>", "superior": "Superior", "supervis": "Supraveghetor", "supervisor": "Supraveghetor de actiune", "suspend": "Suspendat", "suspended": "Suspendat", "suspendedx": "Suspendat", "tTaskAgain": "Repetarea comportamentului de activare", "tTaskAutoCompleteCaption": "Actiunea va fi îndeplinită automat dacă", "tTaskCompletionCOA": "toate condi<PERSON><PERSON><PERSON> sunt îndeplinite în același timp", "tTaskCompletionCOO": "cel puțin o condiție este îndeplinită", "tTaskDueOffsetNone": "imediat", "tTaskDueOffsetPO": "înscris de supraveghetor", "tTaskDueOffsetPS": "în zilele de la initierea solicitarii", "tTaskDueOffsetTS": "în câteva zile de la începerea activității", "tTaskDueOffsetVC": "de variabile continuu", "tTaskDueOffsetVO": "de variabile la pornire", "tTaskInvClassConf": "Secret", "tTaskInvClassPriv": "Privat", "tTaskInvClassPubl": "Public", "tTaskInvPriority1": "1 cea mai mare", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9 cel mai sc<PERSON><PERSON>t", "tTaskInvokeEventB": "în fundal", "tTaskInvokeEventI": "imediat", "tTaskReferenceUserLastSolver": "Ultimul responsabil al actiunii", "tTaskReferenceUserMan": "Manager al org. unit xy", "tTaskReferenceUserUser": "Utilizator xy", "tTaskRunOnlyOnce": "Executați o singură dată", "tTaskSufficientEnd": "Completarea finalizează întregul caz", "tabName": "Numele filei", "table": "<PERSON><PERSON>", "takeOnlyOrder": "Lua<PERSON><PERSON> doar comanda", "takeover": "Preia", "targetTask": "Obiectivul țintă", "targetVar": "Variabila țintă", "taskAutomatic": "situatie automată", "taskEmailNotification": "Notificare e-mail", "taskEvent": "Rulează eveniment", "taskEventWait": "așteaptă pentru eveniment", "taskOwner": "Responsabil actiune", "taskSolverAssign": "pentru atribuirea proprietarului de activitate", "taskStart": "Start", "taskStatus": "Stare", "taskStatusA": "Activează", "taskStatusAP": "Subproces activ", "taskStatusAS": "subproces adormit", "taskStatusD": "Completat", "taskStatusL": "În as<PERSON><PERSON>e", "taskStatusLEdit": "Nu se poate modifica actiunea aflata în așteptare", "taskStatusN": "Nou", "taskStatusP": "Planificat", "taskStatusS": "Suspendat", "taskStatusT": "A trage", "taskStatusW": "Pen<PERSON>u atribuire", "taskStatusWT": "Pentru planificare", "taskSubprocess": "implementate prin subproces", "taskTabVariables": "Variabile atribuite", "taskType": "Tipul de actiune", "taskWillBeAssigned": "Se va aloca actiunea", "tasks": "Actiuni", "tasksToPull": "Actiuni de preluat", "taskstatusAD": "Activ și terminat", "tempId": "Format ID", "tempVar": "format", "template": "Format", "templateDeleted": "<PERSON><PERSON>", "templateStatus": "Statusul format-ului", "templates": "Format", "templatesFolder": "Format – Fisier", "testForm": "Formularul de testare", "tested": "Testat", "text": "Text", "textList": "LOV a textului", "textMultipleLines": "Text cu mai multe linii", "textSuggest": "Suggester", "third": "Al treilea", "thirdCenter": "Al treilea centru", "thu": "<PERSON><PERSON>", "thumbnail": "Miniatură", "title": "Titlu", "to": "La", "toHide": "Ascunde", "toInclusive": "La (inclusiv)", "toPull": "Actiuni de preluat", "tooltip": "<PERSON><PERSON><PERSON>", "total": "total", "tprocName": "Model de proces", "tsk": "<PERSON><PERSON><PERSON><PERSON>", "tskAssignDues": "Setați restricții de timp pentru această actiune", "tskName": "Numele actiunii", "tskNum": "<PERSON><PERSON><PERSON><PERSON> act<PERSON>i", "tskSolver": "Responsabilul actiunii", "tskTemplate": "Șablon de actiune", "tskVar": "actiune", "tsksDone": "Terminat", "tsksSolvers": "Responsabilul actiunii", "ttAdd": {"heading": "Adaugă", "body": "Permite adăugarea unui element nou sau a unor noi parametri care nu au fost încă definiți."}, "ttAddActivity": {"heading": "Adaugă", "body": ""}, "ttAddAttach": {"heading": "Adaugă document", "body": "Permite adăugarea unui document nou."}, "ttAddAttribute": {"heading": "Adaugă", "body": ""}, "ttAddContainer": {"heading": "Adaugă container", "body": "Adaugă recipientul cu conținutul selectat"}, "ttAddFile": {"heading": "Adaugă", "body": ""}, "ttAddStructure": {"heading": "Adăugați elementul la structura organizatorică", "body": "Permite adăugarea unui element nou din structura organizațională sau a unor parametri noi care nu au fost încă definiți."}, "ttAddTemp": {"heading": "Adăugați un nou șablon", "body": "Crearea de șabloane noi de cazuri. Proprietarul șablonului va fi utilizatorul conectat în prezent. Șablonul este atribuit automat unui \"under development\" status."}, "ttAddTsk": {"heading": "Adăugați o actiune nouă", "body": "Crearea unei noi actiuni în cadrul șablonului de proces. Parametrii actiunii pot fi specificați în funcție de tipul de activitate. Legăturile cu alte actiuni pot fi adăugate sau modificate în filele Graph sau Link."}, "ttAddTskGraph": {"heading": "Adăugați o actiune nouă", "body": "Crearea unei noi actiuni în cadrul șablonului de proces. Parametrii actiunii pot fi specificați în funcție de tipul de activitate. Legăturile cu alte actiuni pot fi adăugate sau modificate în filele Graph sau Link."}, "ttAddUser": {"heading": "Adăugați un utilizator nou", "body": "Adăugați un utilizator nou. Fiecare utilizator trebuie să aibă un nume de utilizator unic. Informațiile de bază pot fi stabilite pentru utilizatori, împreună cu alocarea acestora structurii organizaționale și alocării de roluri. Utilizatorilor noi li se acordă automat o stare de blocare. "}, "ttAddVar": {"heading": "Adăugați o nouă variabilă", "body": "Rearea unei variabile noi în șablonul de caz. Fiecare variabilă conține informații care pot fi rezolvate de către proprietarii de sarcini. Este posibil să specificați numele, tipul și valorile implicite ale variabilei."}, "ttAddVice": {"heading": "Adăugați viciu", "body": ""}, "ttAssignAttribute": {"heading": "Atribuiri atribuite tipului de document logic", "body": ""}, "ttAssignTsk": {"heading": "Atribuie", "body": "Permite asignarea actiunilor unui responsabil de actiuni sau adăugarea unui element la o structură definită."}, "ttCases": {"heading": "Solicitari", "body": ""}, "ttOverviews": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttChangePass": {"heading": "Schimbarea parolei", "body": "Editarea parolelor utilizatorilor care sunt gestionați direct în mediul aplicației. Dacă utilizatorii sunt gestionați de un serviciu extern (LDAP), parola trebuie gestionată acolo."}, "ttClose": {"heading": "<PERSON><PERSON><PERSON>", "body": "Fereastra va fi închisă fără salvarea modificărilor."}, "ttCloseTemp": {"heading": "<PERSON><PERSON><PERSON>", "body": "Fereastra cu definiția șablonului va fi închisă."}, "ttCompleteTsk": {"heading": "Finalizați actiune", "body": "Confirmă că actiunea se realizează și o trimite pentru o prelucrare ulterioară ca predefinită."}, "ttContact": {"heading": "Contact", "body": "Afișează contacte pentru supraveghetorul de actiuni."}, "ttContainerSettings": {"heading": "<PERSON><PERSON><PERSON>", "body": "Permite modificarea setărilor pentru containerul dat."}, "ttCopyHdr": {"heading": "Copiați antetul", "body": "Crearea copiei antetului selectat. Selectarea se face făcând clic pe tabelul de antetului șablonului."}, "ttCopyTemp": {"heading": "Copiați șablonul", "body": "Crearea copiei șablonului selectat. Selectarea șabloanelor se face făcând clic pe tabelul de șabloane de proces."}, "ttCopyVar": {"heading": "<PERSON><PERSON> variabilei", "body": "Definiți copia pentru variabila selectată și salvați variabila sub un nou nume. Variabilele sunt selectate făcând clic în tabelul variabilelor."}, "ttDel": {"heading": "Șterge", "body": "Șterge elementul selectat."}, "ttDelAttach": {"heading": "ștergeți documentul", "body": "Șterge documentul selectat."}, "ttDelConnection": {"heading": "Șterge link-ul", "body": "Ștergeți legătura selectată dintre două actiuni ale solicitarii. Eliminarea trebuie confirmată. Ștergerea se face pentru linkul selectat. Selectați link-ul dând clic pe el în tabelul de link-uri."}, "ttDelFolder": {"heading": "Șterger<PERSON>i", "body": "Ștergerea dosarului selectat."}, "ttDelOverview": {"heading": "Ștergeți ansamblul", "body": "Șterge ansamblul selectat."}, "ttDelTemp": {"heading": "Ștergeți șablonul", "body": "Alocă statutul șters la un șablon. Numai atunci când cererea de ștergere este repetată, șablonul este îndepărtat fizic. Acțiunea este aplicată șablonului selectat. Selectați șablonul făcând clic pe el în tabelul de șabloane pentru cazuri."}, "ttDelTsk": {"heading": "Ștergerea actiunii", "body": "Eliminarea actiunii selectate. Eliminarea trebuie confirmată. Împreună cu actiunea, toate linkurile legate de alte activități din șablonul de proces vor fi eliminate. Selectați actiunea făcând clic pe ea în tabelul de sarcini."}, "ttDelTskOrConnection": {"heading": "Ștergeți actiunea sau linkul", "body": "Eliminarea unei actiuni selectate sau a unei legături selectate între două actiuni din proces. Această acțiune trebuie confirmată. Link-uri conexe la alte actiuni din proces vor fi eliminate împreună cu sarcina. Faceți clic pentru a selecta."}, "ttDelVar": {"heading": "Șterger<PERSON> variabilei", "body": "Ștergerea variabilei selectate. Această acțiune trebuie confirmată. Variabila nu va mai fi disponibilă pentru sarcinile individuale de proces. Variabilele sunt selectate făcând clic în tabelul variabilelor."}, "ttDelVice": {"heading": "Anuleaz<PERSON> vicele", "body": ""}, "ttDetailCase": {"heading": "<PERSON><PERSON><PERSON>", "body": "Afișează detaliile solicitarii selectate."}, "ttDetailCertificate": {"heading": "Detaliu certificat", "body": "Afișează detaliile certificatului selectat."}, "ttDetailHistory": {"heading": "<PERSON><PERSON><PERSON>", "body": "Afișează detalii ale elementului selectat."}, "ttDetailTsk": {"heading": "Detaliu actiune", "body": "Afișează detaliile actiunii selectate."}, "ttDmsFolderAdd": {"heading": "Adăugați un dosar nou", "body": "Inserarea unui folder nou. Dacă unul dintre dosare este selectat, folderul părinte va fi prepopulat."}, "ttDmsFolderEdit": {"heading": "Editați <PERSON>", "body": "Editați dosarul selectat."}, "ttDocuments": {"heading": "Stocarea documentelor", "body": ""}, "ttDownload": {"heading": "Des<PERSON><PERSON><PERSON>", "body": "Se descarcă fișierul selectat."}, "ttDropContainer": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Aruncă containerul din tabloul de bord."}, "ttENotification": "Notificare e-mail", "ttEdit": {"heading": "Editează", "body": "Inițializează editarea obiectului selectat."}, "ttEditAttach": {"heading": "Editează", "body": "Activați vizualizarea S și editați atributele (metadata) fișierului încărcat."}, "ttEditConnection": {"heading": "Editeaza link-uri", "body": "Editare a legăturii dintre două sarcini. Este posibil să modificați parametrii comportamentului legăturii și condițiile de conectare. Acțiunea este aplicată linkului selectat. Link-urile sunt selectate făcând clic pe ele în tabelul de link-uri."}, "ttEditOverview": {"heading": "Editează ansamblul", "body": "Autorizează editarea unui ansamblu."}, "ttCopyOverview": {"heading": "Copiază ansamblul", "body": "Crează o copie a ansamblului selecrat."}, "ttEditPath": {"heading": "Adaug<PERSON> an<PERSON>lu", "body": "Permite definirea unui ansamblu nou."}, "ttEditTemp": {"heading": "Editarea definiției șablonului", "body": "Editarea șablonului cazului. Orice parametru de șablon poate fi editat. Acțiunea este efectuată pentru șablonul selectat. Selectați șablonul făcând clic pe tabelul de șabloane pentru cazuri."}, "ttEditTsk": {"heading": "Editați actiunea", "body": "Editarea informațiilor despre actiuni și parametrii actiunii. Acțiunea este aplicată sarcinii selectate. Selectați o activitate făcând clic în tabelul de actiuni."}, "ttEditTskOrConnection": {"heading": "Editarea actiunilor sau a link-urilor", "body": "Editarea informațiilor despre actiuni și a parametrilor actiunilor sau editarea legăturilor dintre două actiuni, parametrii comportamentali și condițiile de conectare. Acțiunea este aplicată actiunii sau linkului selectat. Faceți clic pentru a selecta."}, "ttEditTskVars": {"heading": "Modifică", "body": "Modificați variabilele de activitate"}, "ttEditUser": {"heading": "Editarea informațiilor despre utilizatori", "body": "Editarea informațiilor de bază despre utilizatori, parole, atribuirea unităților organizaționale și atribuirea de roluri. Acțiunea este aplicată utilizatorului selectat. Utilizatorii sunt selectați făcând clic în tabelul de utilizatori."}, "ttEditVar": {"heading": "Editarea variabilei", "body": "Editarea numelui, tipului și valorilor implicite ale variabilelor. Acțiunea este aplicată variabilei selectate. Variabilele sunt selectate făcând clic în tabelul variabilelor."}, "ttEnotTgt": "Destinatar", "ttEnotTgtG": "Responsabil de actiuni", "ttEnotTgtO": "Proprietarul solicitarii", "ttEnotTgtP": "%s", "ttEnotTgtR": "Rol %s", "ttEnotTgtS": "Unitate organizatională %s", "ttEnotTgtT": "Proprietarul solicitarii %s", "ttEvent": {"heading": "Actiune personalizată", "body": "Invitarea instantanee a evenimentului în această actiune."}, "ttEvents": {"heading": "Evenimente", "body": "Stabilirea regulilor de afaceri pentru a reacționa asupra evenimentelor interne sau externe definite în sistem. Accesul necesită rolul $PowerUser."}, "ttFavourites": {"heading": "Lista de favorite", "body": "Lista tuturor favorite cu opțiunea de a le edita sau șterge din listă."}, "ttFilter": {"heading": "Filtru", "body": "Afișează numai acele elemente care îndeplinesc condițiile de filtrare definite."}, "ttFilterPrc": {"heading": "Filtru", "body": "Afișează numai acele solicitari care îndeplinesc condițiile de filtrare definite."}, "ttFilterTemp": {"heading": "Filtru", "body": "Afișează numai acele șabloane care îndeplinesc condițiile de filtrare definite."}, "ttFilterTsk": {"heading": "Filtru", "body": "Afișează numai actiunile care îndeplinesc condițiile de filtrare definite."}, "ttFilterUser": {"heading": "Filtru", "body": "Afișează numai acei utilizatori care îndeplinesc condițiile de filtrare definite."}, "ttFullScreen": {"heading": "Ecran <PERSON>ntreg", "body": "Afișează conținutul containerului în modul ecran întreg."}, "ttGraph": {"heading": "<PERSON><PERSON>", "body": "Reprezentarea grafică a stării actuale a solicitarii."}, "ttGraphActualFinish": " Finisare reală ", "ttGraphActualStart": "Data de începere efectivă", "ttGraphCond": "Condiții", "ttGraphCond1": "cel puțin una trebuie îndeplinită", "ttGraphCondAll": "toate trebuie să fie îndeplinite", "ttGraphCondElse": "Dacă nu este îndeplinită altă condiție", "ttGraphDeadlinePo": "Termen limită: introdus de proprietarul solicitarii", "ttGraphDeadlinePs": "Termen limită: în termen de% s zile după inițierea solicitarii", "ttGraphDeadlineTs": "Termen limită: în% s zile după inițierea actiunii", "ttGraphDelayPo": "Inițierea actiunii: introdusă de proprietarul solicitarii", "ttGraphDelayPs": "Inițierea actiunii:% s zile de la inițierea solicitarii", "ttGraphDelayTs": "Inițierea actiunii:% s zile de la începerea actiunii", "ttGraphEnd": "Finalizarea actiunii încheie întregul caz", "ttGraphFinishedBy": "Terminat de", "ttGraphHiearchyA": "toți superiorii conducătorului de actiuni", "ttGraphHiearchyC": "subordonat direct al supraveghetorului actiunii", "ttGraphHiearchyD": "toți subordonații supraveghetorului actiunilor", "ttGraphHiearchyG": "supervizor de actiuni", "ttGraphHiearchyL": "toate", "ttGraphHiearchyP": "superior direct al supraveghetorului actiunilor", "ttGraphHiearchyS": "colegii de supraveghere a actiunilor", "ttGraphLinkFrom": "De <PERSON>", "ttGraphLinkTo": "La", "ttGraphMethodL": "la ultimul proprietar al actiunii% s", "ttGraphMethodS": "pentru proprietarul de activitate selectat de un supervizor", "ttGraphMethodT": "pentru a selecta automat proprietarul de activitate", "ttGraphMethodV": "pentru proprietarul sarcinii atribuit variabilei% s", "ttGraphMultiinstance": "Multi-instanță", "ttGraphNoneMand": "Legătură obligatorie", "ttGraphOnlyOnce": "Rulați o singură dată", "ttGraphSave": {"heading": "Salvați diagrama și creați șablonul", "body": ""}, "ttGraphStart": "Activitatea va fi activată automat după pornirea casetei ", "ttGraphTaskHiearchy": "Responsabilul actiunii", "ttGraphTaskMethod": "Activitatea va fi atribuită", "ttGraphTaskOwner": "Supervizor de actiuni", "ttGraphTaskOwnerOS": "Managerul unității organizaționale", "ttGraphTaskOwnerPO": "Supraveghetor", "ttGraphTaskOwnerSU": "Utilizator selectat", "ttGraphTaskRole": "cu rol", "ttGraphTaskTypeA": "<PERSON><PERSON>ina automat<PERSON>", "ttGraphTaskUser": "Proprietarul actiunii", "ttGraphWait1": "Parametrii de intrare: așteaptă unul", "ttGraphWaitA": "Parametrii de intrare: așteptare pentru toți", "ttGraphWaitFirst": "Parametrii de intrare: a<PERSON><PERSON><PERSON><PERSON> pentru to<PERSON>, care rulează mai înt<PERSON>i", "ttGraphWaitN": "Parametrii de intrare: așteptare %s", "ttHandover": {"heading": "Transferati actiunea", "body": "Permite transferul actiunii unui alt utilizator disponibil."}, "ttDelegate": {"heading": "Delegați actiunea", "body": ""}, "ttReject": {"heading": "Respingeți actiunea", "body": ""}, "ttHelp": {"heading": "A<PERSON>tor instantaneu", "body": "Permiterea sau dezactivarea ajutorului instant. Ajutorul este afișat sub formă de bule care afișează informații despre interfața cu utilizatorul atunci când funcțiile sunt introduse peste."}, "ttHome": {"heading": "Pagina inițială a utilizatorului", "body": "Locul unic cu toate informațiile pentru utilizatorii obișnuiți. Tabloul de bord oferă o imagine generală."}, "ttHtml": {"heading": "Generați documentația", "body": "Generarea documentației HTML a procesului de șablon. În funcție de tipul de browser, un document poate fi afișat imediat sau salvat pe un disc."}, "ttInclusion": {"heading": "Includere", "body": "Exportă un fișier cu rezumatul autorizației și rolurilor utilizatorului, toate rolurile entităților de organizare semnate utilizate, în care este membru sau manager, inclusiv ierarhia sarcinilor în care este superisorul."}, "ttInvAttendees": "Participanți", "ttInvDTEnd": "End", "ttInvDTStart": "Start", "ttInvLocation": "Locație", "ttJustSave": {"heading": "<PERSON><PERSON>", "body": "Salvează modificările."}, "ttLock": {"heading": "Blocare", "body": "Blochează sau deblochează selecția"}, "ttLockUser": {"heading": "Blocare", "body": "Blochează sau deblochează utilizatorul"}, "ttLogout": {"heading": "Del<PERSON><PERSON>", "body": "Înregistrarea unui utilizator. După finalizarea cu succes a lucrului cu aplicația, este afișat dialogul inițial de autentificare."}, "ttMapping": {"heading": "Maparea", "body": "O prezentare generală a variabilelor atribuite pentru citirea (R), s<PERSON><PERSON><PERSON> (W) și introducerea obligatorie (M) în actiunile individuale cu opțiunea de a edita cesiunea."}, "ttNewCase": {"heading": "Solicitare noua", "body": "Crearea unei noi instanțe de proces – solicitare noua. Este posibil să selectați din șabloanele proceselor disponibile sau să creați un caz fără o structură de sarcină predefinită."}, "ttNewOverview": {"heading": "Adăugați vizualizare", "body": "Permite definirea unui nou ansamblu."}, "ttOrgStructure": {"heading": "Structura organizatiei", "body": ""}, "ttParent": {"heading": "Superior", "body": "Treceți la o solicitare de la care solicitarea afișata a fost creata ca un subproces."}, "ttPhoto": {"heading": "fotografii", "body": "Încărcarea fotografiilor în profilul utilizatorului; Acceptă formatele GIF, JPG și PNG. Dimensiunea imaginii va fi ajustată automat."}, "ttPlans": {"heading": "Programare", "body": "Stabilirea regulilor pentru lansarea solicitarilor automate de o singură dată sau repetate, în funcție de parametrii specificați. Accesul necesită rolul lui $Administrator."}, "ttPrint": {"heading": "Imp<PERSON><PERSON>", "body": "Creează imprimarea."}, "ttRecalc": {"heading": "Recal<PERSON><PERSON>", "body": "Recalculați variabilele curente."}, "ttRedirectToPrc": {"heading": "Solicitare", "body": ""}, "ttResetDash": {"heading": "Resetează", "body": "Resetează modificările efectuate."}, "ttResetSearch": {"heading": "Resetează", "body": "Resetează formularul de căutare."}, "ttRestoreTemp": {"heading": "Repararea șablonului", "body": "Restabilește șablonul șters"}, "ttRevision": {"heading": "Revizuire", "body": "Permite încărcarea unei versiuni noi de fișier"}, "ttRoles": {"heading": "Gestionarea rolurilor", "body": ""}, "ttRunEvent": {"heading": "Desfășurați evenimentul", "body": "Invocarea evenimentului în acest caz"}, "ttSave": {"heading": "Salvează", "body": "Salvează modificările și închide fereastrei."}, "ttSaveDMSCols": {"heading": "Salvați coloanele", "body": ""}, "ttSaveSettings": {"heading": "Salvează", "body": "Salvează modificările."}, "ttSaveTsk": {"heading": "<PERSON>umai <PERSON>", "body": "Activitatea deschisă va fi salvată, astfel încât să puteți reveni mai târziu la aceasta."}, "ttSearch": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON>"}, "ttSendNote": {"heading": "Adauga notita", "body": "Permite inserarea unei noi note."}, "ttSetConnectionCond": {"heading": "Condiție", "body": "Adăugarea sau editarea condițiilor de legătură. Editarea se aplică liniei selectate. Faceți clic pe link-ul sau simbolul condiției pentru a selecta."}, "ttSetDefaultDash": {"heading": "Setați ca tablou de bord implicit", "body": "Setează setarea curentă a tabloului de bord ca implicită"}, "ttShowHideBtn": {"heading": "Arată / ascunde", "body": "Ascunde parțial sau afișează meniul principal."}, "ttSleepCase": {"heading": "Suspendați solicitarea", "body": "Marchează solicitarea suspendată. Solicitarea nu va mai fi afișata printre actiunile active, dar dacă este necesar, este posibil să se schimbe statusul înapoi la activ și să se termine întregul caz mai târziu."}, "ttSolve": {"heading": "Deschide actiune", "body": "Afișează dialogul care permite lucrului să înceapă pe sarcina atribuită conform unui șablon predefinit."}, "ttStatePlan": {"heading": "Statut", "body": "Definește statutul planului."}, "ttStatusHdr": {"heading": "Modificarea statutului antetului", "body": "Acțiunea este aplicată pentru antetul selectat. Există stări de \"active\" și \"inactive\". Selectarea se face făcând clic pe tabelul de antetului șablonului."}, "ttStatusTemp": {"heading": "Modificarea stării șablonului", "body": "Administrarea ciclului de viață al șablonului se realizează prin stabilirea stării sale. Există stări de \"în dezvoltare\", \"active\", \"inactive\" și \"șterse\" disponibile. Acțiunea este aplicată pentru șablonul selectat. Selectarea șablonului se face făcând clic pe tabelul de șabloane pentru cazuri."}, "ttSubprocess": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Comută la o solicitare care a fost creata ca un proces secundar în procesul solicitarii afișate."}, "ttTabsButtonMore": {"heading": "<PERSON> mult", "body": "Arată mai multe opțiuni."}, "ttTakeTsk": {"heading": "Preia actiunea", "body": "Permite ca actiunea să fie preluată de un alt responsabil."}, "ttTemps": {"heading": "Șabloane de proces", "body": "Locul central pentru gestionarea șabloanelor de proces. Accesul necesită rolul lui $PowerUser."}, "ttTiming": {"heading": "Programare", "body": "Introduceți începutul și sfârșitul actiunii."}, "ttTsks": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttUploadSettings": {"heading": "Încă<PERSON><PERSON><PERSON>", "body": ""}, "ttUserSetting": {"heading": "Setarile utilizatorului", "body": "Setarea informațiilor de contact ale utilizatorilor, a parolelor de acces și a preferințelor utilizatorilor. Utilizatorii cu rol de $ Administrator pot gestiona în continuare informații despre organizația și instanțele aplicației TeamAssistant."}, "ttUsers": {"heading": "Administratea utilizatorului", "body": "Administrarea centrală a utilizatorilor, structura organizațională și rolurile utilizatorilor. Accesul necesită rolul $Administrator."}, "ttValidation": {"heading": "Validare", "body": "Validați șablonul și vizualizați toate buclele existente în cadrul șablonului. Notifică asupra condițiilor care nu pot fi îndeplinite și a variabilelor neutilizate."}, "ttViewFile": {"heading": "A vizualiza", "body": ""}, "ttWakeUpCase": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttActivateCase": {"heading": "Activati", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Setează ca predefinit coloanele DMS ", "body": "Setează asignarea coloanelor DMS ca implicită."}, "ttResetDmsCols": {"heading": "Resetează", "body": "Resetați atribuirea coloanelor DMS."}, "ttRestoreDoc": {"heading": "Restabili", "body": "Restabilește documentul șters."}, "tue": "<PERSON><PERSON><PERSON>", "type": "Tip", "typeOfRepetition": "Tip de repetare ", "unassignedSolvers": "Potrivit responsabililor de actiuni", "unassignedTaskSolvers": "Responsabili de actiuni nealocați", "uncategorized": "Fără categorie", "unfinishedProcesses": "Solicitari nefinalizate", "unknown": "Necunoscut", "unknownUser": "Utilizator necunoscut", "unrestricted": "nerestricţionat", "unspecified": "Nespecificat", "upload": "Încă<PERSON><PERSON><PERSON>", "uploadFile": "Încărcați un fișier", "uploadPhoto": "Încărcați o poză", "uploadCsv": "Încărcați csv", "url": "URL", "urlAddress": "Adresa URL ", "urlContent": "Conținutul URL ", "use": "Utilizator", "user": "Utilizator", "userByOwnerOfLastTask": "Pentru utilizatorul ales de ultimul responsabil de actiune.", "userE": "Utilizator", "userFilters": "filtre de utilizator", "userLock": "Blochează", "userLockUnlockQ": "Chiar doriți să schimbați starea utilizatorului {{username}}?", "userName": "Username", "userId": "ID-ul utilizatorului", "userOrgStruct": "Aparține unității organizaționale", "userVice": "Înlocuit de", "userViced": "Pentru a fi înlocuit", "users": "Util<PERSON><PERSON><PERSON>", "usersDeleted": "<PERSON><PERSON>", "validation": "Validare", "value": "<PERSON><PERSON><PERSON>", "var": "variabilă", "var-": "variabilă –", "varChange": "Modificarea variabilei va fi anunțată tuturor participanților la caz", "varTaskMap": "Cartografierea", "varTemp": "Șablon variabil", "variable": "Variabilă", "variableType": "Tipul de variabilă", "vars": "Variabile", "varsForMandatory": "Variabile pentru introducerea obligatorie", "varsForReading": "Variabile pentru citire", "varsForWriting": "Variabile pentru scriere", "vices": "Vice", "viewCVFields": "Câmpuri disponibile", "visForOrgStrMembers": "Vizibil pentru membrii grupului organizațional ", "visForRoleMembers": "Vizibil pentru membrii cu roluri", "headerVisForRole": "Solicitare vizibila pentru rol", "waitForNumOfInputs": "Așteptare pentru: (numă<PERSON> <PERSON> intr<PERSON>)", "waitsFor": "așteaptă", "waitsForAll": "așteaptă pentru toți", "waitsForOne": "așteaptă unul", "waitsForSending": "Asteapta sa fie trimis", "waitsRunFirst": "așteaptă pentru toți, ce rulează mai întâi", "wakeUp": "Dezsuspendeze", "warning": "Atenție", "wed": "<PERSON><PERSON><PERSON><PERSON>", "weekIn": "săptămână în", "weekly": "Săptămânal", "width": "Lăţime", "withConditions": "Cu condiții", "withoutCond": "Fără condiții", "year": "An", "yes": "Da", "zip": "Cod poștal", "move": "Mi<PERSON><PERSON><PERSON>", "alertClosing1": "notificarea va fi închisă automat în:", "inDocuments": "În documente", "inVariables": "În variabile", "headerTask": "Titlu actiune", "planName": "Numele planului", "inBulk": "În bloc", "confirmResetDmsColumns": "Confirmi resetarea coloanelor DMS? ", "dmsColsUseDef": "Using default settings", "dmsColsUseCust": "Using custom settings", "today": "<PERSON><PERSON>", "alrPlanDeleteFailed": "Ștergere planului a eșuat.", "notRunning": "<PERSON><PERSON>", "alrLackOfPermsToAddTask": "Nu aveți permisiuni pentru a adăuga actiunea. ", "dragTable": "Trageți tabelul", "alrDownloadCsvListFailed": "Descărcarea fișierelor Csv nu a reușit.", "alrCsvUploadWrongExtension": "Încărcați numai fișiere cu extensie * .csv", "addToFav": "Adăugați la favorite", "renameItem": "Redenumiți", "removeFromFav": "<PERSON><PERSON><PERSON><PERSON> din favorite?", "alrAddedToFav": "<PERSON><PERSON><PERSON><PERSON> la favorite", "alrRemovedFromFav": "<PERSON><PERSON><PERSON> din favorite.", "tskSetAssignDues": "Setați restricții de timp pentru actiune", "isNot": "nu este", "alrTskScheduling": "Programarea actiunilor ... ", "alrFavouritesPageExist": "Această pagină este deja la favorite", "autoFit": "Autofit", "passwordIsShort": "Parola este prea scurtă.", "changeAttrComplCases": "Schimbarea atributelor solicitarii completate", "iterateOverVars": "Iterați peste variabile", "nrOfDecimalDigits": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "onlyNumbers": "<PERSON><PERSON><PERSON>", "maxNumberOfDecimals": "Numărul maxim de cifre zecimale este", "alrInsertCsv": "Introduceți fișierul CSV.", "addBefore": "<PERSON><PERSON><PERSON> du<PERSON>", "moveBefore": "Mută <PERSON> de", "administration": "Administrare", "ttAdministration": {"heading": "Administrare", "body": ""}, "alrLogsLoadFailed": "Nu s-au încărcat jurnalele.", "logs": "<PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON>", "useCompatibleTempl": "Utilizați șablonul compatibil ", "overwriteExistTempl": "Suprascrieți șablonul existent", "addNewTempl": "Adăugați un nou șablon", "import": "Importă", "export": "Exportă", "confirmExportAllTempl": "<PERSON><PERSON><PERSON><PERSON> toate șab<PERSON><PERSON><PERSON>?", "confirmExportSelTempl": "Exportați șablonul selectat?", "newLogs": "Înregistrare nouă", "container": "recipient", "contents": "<PERSON><PERSON><PERSON><PERSON>", "confirmRemoveDialog": "Chiar doriți să eliminați {{variable}}?", "allMyCases": "Toate solicitarile mele", "maintenanceMsg": "<span style=\"color: {{color}};\">Întreținerea</span> programată este în curs", "alrAttachDownloadLackOfPerms": "Nu aveți permisiuni de descărcare a documentului sau documentul nu a fost găsit.", "unableToConnect": "Imposibil de conectat la server", "tryLater": "Încercați mai târziu sau contactați administratorul", "enableTaskDelegation": "Activați delegarea sarcinilor", "enableRejectTask": "Activați actiunea de respingere", "confirmRejectTask": "Confirmi respingerea actiunii?", "rejectTask": "Respingeți actiunea", "delegateTask": "Deleaga", "alrRejectingTask": "Resping actiunea...", "alrTaskRejected": "Actiunea a fost respinsa.", "alrTaskRejectFailed": "Activitatea nu a reușit să respingă.", "alrTaskDelegating": "Delegarea actiunii ...", "alrTaskDelegated": "Actiunea a fost delegată utilizatorului:", "alrFailedTaskDelegate": "Delegarea actiunilor a eșuat.", "delegateOnUser": "Deleaga pentru utilizatorul", "plnAssignmentCond": "Dacă câ<PERSON><PERSON> \"Alocări\" r<PERSON><PERSON><PERSON><PERSON> gol, va fi creată o listă de inițiatori prin evaluarea condițiilor restrictive la momentul executării planului", "alrUserFiltersSettingsFailed": "<PERSON><PERSON><PERSON> set<PERSON><PERSON>or pentru filtrele utilizator nu a reușit.", "general": "General", "alrUserPhotoLoadFailed": "Încărcarea fotografiilor pentru utilizator a eșuat. ", "publicDynTable": "Masă dinamică publică", "isFullIndexed": "<PERSON>n c<PERSON><PERSON><PERSON>", "datetimeIndexed": "Indexat pe", "toIndex": "Pentru indexare", "toReindex": "Pentru reindex", "solverChanged": "Responsabilul actiunii s-a schimbat în {{count}}", "changeSolverFailed": "Schimbarea actiunii a eșuat.", "alrTikaParsingFailed": "A apărut o eroare la parsarea documentului", "alrIndexingFailed": "Indexarea documentelor a eșuat.", "alrTikaNotRunning": "Serviciul de analiză a documentelor nu este disponibil", "alrIndexingServiceNotRunning": "Serviciul de indexare nu este disponibil", "alrFulltextNotSet": "Textul complet nu a fost setat.", "asc": "Ascendent", "desc": "Descendentă", "restore": "Restabileste", "alrLogosLoadFailed": "Încărcarea logourilor a eșuat. ", "indexedDocsCount": "în {{count}} documente", "alrIndexedCountLoadFailed": "Căutarea fulltext nu este disponibilă momentan.", "searchAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> toate", "searchActual": "Numai real", "runIndexing": "Run indexing", "alrDocumentIndexing": "Indexarea documentului ...", "alrDocumentIndexed": "Documentul a fost indexat și poate fi găsit prin căutare.", "alrDocumentIndexedWithMinMetadata": "Documentul a fost indexat.", "alrDocumentIndexingFailed": "Indexarea documentului a esuat.", "changingUserProfileForbidden": "Schimbarea profilului de utilizator este interzisă", "uploadingPhotoForbidden": "Încărcarea fotografiei este interzisă", "alrCalculationsValidationFailed": "Validarea calculelor a eșuat.", "alrValidationCalcError": "Eroare validarea calculelor", "maintenance": "întreținere", "maintenanceActivate": "Activați întreținerea", "maintenanceInfoText": "Startul și sfârșitul vor fi afișate utilizatorilor după activarea întreținerii.", "maintenanceMode": "<PERSON><PERSON> <PERSON>", "alrAvailableCalcFailed": "Calculele disponibile nu au putut fi încărcate.", "alrFillDataForSearch": "Completați parametrii de căutare.", "youAreHere": "Ești aici", "invalidDate": "Format incorect de dată", "alrInvalidFileFormat": "Format de fișier nevalid.", "alrEnter3characters": "<PERSON>ă rugăm să <PERSON>ți cel puțin trei caractere.", "changeCaseOwner": "Schimbați proprietarul solicitarii", "actualCaseOwner": "Deținătorul real al solicitarii", "newCaseOwner": "Detinatorul unei noi solicitari", "alrCaseOwnerChanged": "Proprietarul solicitarii a fost schimbat", "alrChangeCaseOwnerFailed": "Schimbarea proprietarului solicitarii a eșuat.", "alrCsvSaving": "Salvarea fișierului CSV ...", "alrCsvSaveFailed": "Încărcarea fișierului CSV nu a reușit.", "alrCsvSaved": "Fișierul CSV a fost încărcat", "allTemplates": "<PERSON><PERSON><PERSON><PERSON>", "specifyCaseIds": "Specificați IDurile solicitarii", "caseIds": "IDul solicitarii", "separBySemicolon": "Separat de punct și virgulă", "alrAddCaseIds": "Specificați IDul solicitarii", "headers": "<PERSON><PERSON><PERSON><PERSON>", "header": "Antet", "defaultHeaderName": "Numele antetului implicit", "headerName": "<PERSON><PERSON>le antetului", "addHeader": "Adaugă antet", "editHeader": "Editează antet", "templateName": "Numele șablonului", "rolesExecRightsText": "Atribuiți roluri care vor putea iniția solicitari", "orgUnitsExecRightsText": "Atribuirea unităților organizaționale care vor putea iniția solicitari", "selectedHeader": "Antetul selectat", "alrHeaderDeleted": "Antetul a fost șters!", "alrHeaderDeleteFailed": "Ștergerea antetului a eșuat", "alrHeaderSaveFailed": "Salvarea antetului a eșuat", "alrHeaderSaved": "Antetul a fost salvat.", "alrHeadersLoadFailed": "Încărcarea datelor antetului a eșuat.", "identificator": "Cod-ul antetului", "includeDataSimilarProcesses": "Include<PERSON>i date despre toate procesele similare ", "confirmCopyCv": "Chiar vrei să copiezi lista de ansamblu selectată?", "alrCreatingCopyCv": "Crearea unei copii a prezentării ...", "alrCvCopied": "Prezentarea generală a fost copiată.", "alrCopyCvFailed": "Crearea copiei de ansamblu a eșuat.", "copyingTemplate": "Copierea unui șablon", "alrCheckTempImportFailed": "Verificarea importului de șabloane a eșuat.", "warnings": "Avert<PERSON>ente", "missingEventsFiles": "Lipsesc fișiere de evenimente", "missingEventsFilesText": "{{- file}} fișier nu a fost găsit în {{- event}} eveniment", "printsOfTemplates": "Tip<PERSON><PERSON><PERSON>", "printsOfTemplatesText": "Vă rugăm să acordați atenție imprimării {{- print}} din {{- template}} șablon. Valoare: {{- value}}", "dupliciteTaskNames": "Numele actiunii este duplicat", "dupliciteTaskNamesText": " {{- template}} șablon conține mai multe sarcini cu același nume {{- task}} {{- taskId}}, aceasta va cauza defalcarea link-urilor!", "dynTableUsed": "Tabelul dinamic folosit", "suspiciousCalc": "<PERSON><PERSON> suspecte", "suspiciousCalcText": "Este posibil ca rolul / organizația / utilizatorul să nu existe în calcul {{- calc}}", "missingEvents": "Lipsesc evenimente", "missingEvent": "Evenimentul lipsește", "wrongMappingDomains": "Domeniile au fost mapate greșit", "wrongMappingDomainsText": "Descrierea a {{- task}} actiunii din {{- template}} șablonul conține un nume de domeniu rău, domeniul actual este {{- actDom}}", "taskDescription": "Descrierea actiunii", "eventsUrl": "<PERSON><PERSON><PERSON><PERSON>", "eventsUrlText": "Posibilă eroare în {{- event}} URL-ul evenimentului, domeniul actual este {{- actDom}}", "param": "Parametru", "alrServiceNotForTable": "Datele din acest serviciu nu sunt potrivite pentru vizualizarea în tabel. ", "alrServiceDataFailedLoad": "<PERSON>le de serviciu nu au putut fi încărcate", "alrServiceNoData": "Serviciul nu conține date.", "tableColumns": "<PERSON><PERSON><PERSON>i", "datetime": "Dată și timp", "exactDatetime": "Dată și timp exactă", "dashRestNoColumns": "<PERSON>u sunt setate coloane – le alegeți în setările pentru containere ", "loadService": "<PERSON><PERSON><PERSON>", "useCompatibleRole": "Utilizați rol compatibil", "overwriteExistRole": "Suprascrieți rolul existent", "addNewRole": "Adaugă rol nou", "templateImportFailed": "Importul de șabloane nu a reușit. ", "templateImport": "Importul de șabloane", "templateImportNoData": "Nu au fost găsite date pentru importul șablonului.", "variableImportNoData": "Nu au fost găsite date pentru importul variabile.", "ttTemplateImport": {"heading": "Importul de șabloane", "body": "Se selectează un dosar cu definițiile unuia sau mai multor șabloane și apoi se încarcă."}, "showUnfinishedProcesses": "Arătați cazurile neterminate ", "expMaintenanceEnd": "Data de sfarsit a activitatilor de întreținere", "alrScriptSaveFailed": "Salvarea scriptului a eșuat.", "editScript": "Editare script", "addScript": "Adăugați script", "alrRunScript": "Lansarea scriptului ...", "alrScriptCompleted": "Script finalizat.", "alrFailedScriptStart": "Script nu a reușit să pornească.", "alrScriptDocsLoadFailed": "Documentația de script-uri nu a reușit să se încarce.", "switchAdminUser": "Comutarea administratorului / utilizatorului", "ttSwitchAdminUser": {"heading": "Comutarea administratorului / utilizatorului", "body": ""}, "ttSwitchViewport": {"heading": "Se comutează mobil/PC mod de a vedea", "body": ""}, "alrEventDataLoadFailed": "Datele evenimentului nu au putut fi încărcate. ", "alrEventRuleDataLoadFailed": "<PERSON>le regulii de eveniment nu au putut fi încărcate.", "cancellation": "<PERSON><PERSON><PERSON>", "tTaskAutoCancellCaption": "Actiunea va fi anulată automat dacă ", "codeMirrorHelp": "Faceți clic oriunde în editor și apăsați Ctrl + Space pentru a vizualiza ajutorul.", "codeMirrorHelpJs": "Pentru o listă cu toate caracteristicile, face<PERSON>i clic pe <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "<PERSON>ug<PERSON> eveniment", "editEvent": "Editeaz<PERSON> eveniment", "term": "Termen", "columnOrder": "Ordinea colo<PERSON>i", "alrLoadEventsButtonsFailed": "Încărcarea butoanelor din tabel nu a reușit. ", "showButtonsCol": "Afișează coloana de acțiuni", "button": "Buton", "enableButtonInTasks": "Arată ca un buton in lista actiunilor", "alrEventDoesntExist": "Evenimentul selectat nu există.", "alrEventRuleSaveFailed": "Modificarea regulii evenimentului a eșuat.", "variableNames": "N<PERSON>le variabilei", "fsEvent": "eveniment", "alrEventDeleteFailed": "Ștergerea evenimentului a eșuat.", "fsRule": "regul<PERSON>", "alrRuleDeleteFailed": "Ștergerea regulii a eșuat. ", "alrRuleStatusChangeFailed": "Modificarea stării regulii a eșuat.", "ruleActivateDeactivateQ": "Chiar vrei să schimbi starea regulii?", "docUploadedPrivate": "Documentul va fi încărcat ca privat", "fileOwner": "Proprietar de fișiere", "planOk": "Ok", "userNotAuthToStartTempl": "Utilizatorul nu este autorizat să initieze o solicitare prin intermediul acestui șablon", "planStartDate": "Data de începere", "useOnlyFutureDates": "<PERSON><PERSON> datele viitoare", "alrGenerateHtmlFailed": "Generarea HTML nu a reușit.", "alrNoPermsToAddNoteInVice": "Nu aveți permisiuni de adăugare a notei în calitate de deputat", "alrNoPermsToAddDocInVice": "Nu aveți permisiuni de adăugare a documentului în calitate de deputat.", "current": "Actual", "indexation": "Indexare", "attemptToRestoreConnection": "Încercarea de a restabili conexiunea în ", "loginWillExpire": "Logarea va expira în", "unsavedDataWillBeLost": "<PERSON>le nesalvate vor fi pierdute.", "alrFileSaveLikeAttachViceError": "Vice-ul tău are permisiunea de a vizualiza doar permisiunea! ", "alrFileSaveLikeAttachStoreError": "Salvarea tipăririi ca document în caz că nu a reușit.", "useCompatibleEvent": "Utilizați eveniment compatibil", "overwriteExistEvent": "Suprascrieți evenimentul existent", "addNewEvent": "Adăugați un nou eveniment", "useCompatibleUser": "Utilizare utilizator compatibil", "overwriteExistUser": "Suprascrieți utilizatorul existent", "addNewUser": "Adăugați un utilizator nou", "useCompatibleUnit": "Utilizați o unitate org. compatibilă", "overwriteExistUnit": "Suprascrieți unitatea orgă existentă", "addNewUnit": "Adăugați o nouă unitate org", "addNewDynTable": "Adăugați o nouă tabelă de date", "enterDiffNameRoot": "Introduceți alt nume decât Root.", "ttTemplatesExport": {"heading": "Șabloane de export", "body": "Exportă șabloanele selectate într-un dosar. Este posibil să alegeți numele și locația fișierului exportat. Acțiunea este aplicată șablonului selectat. Selectați un șablon dând clic pe el în tabelul de șabloane pentru cazuri."}, "ttTemplatesExportAll": {"heading": "<PERSON>rta<PERSON><PERSON> toate șab<PERSON><PERSON><PERSON>", "body": "Exportați toate șabloanele afișate curent într-un fișier. Este posibil să alegeți numele și locația fișierului exportat. Selectarea șabloanelor poate fi restricționată prin stabilirea unor condiții adecvate de filtrare."}, "exportAll": "Exportați tot", "noTemplatesToExport": "Nu există șabloane de exportat.", "skip": "Sari peste", "ttSkipTemplate": {"heading": "Săriți șablonul", "body": "Schimba importul șablonului curent și afișează următorul."}, "alrUsersNotLoaded": "încarce utilizatorului a eșuat.", "alrRolesNotLoaded": "R<PERSON><PERSON><PERSON> au eșuat să se încarce.", "alrOrgUnitsNotLoaded": "Unitățile Org. au eșuat să se încarce.", "stayLogged": "Rămâi înregistrat", "managerIn": "manager în {{orgUnit}}", "showTime": "Afișați marca de timp în prezentări de ansamblu", "accessLog": "Jurnal de acces", "active_headers": "Numărul de anteturi active ", "active_template_processes": "Numărul de procese active", "active_users": "Numărul de utilizatori activi", "active_users_able_to_create_a_process": "Numărul de utilizatori activi care pot executa un proces", "activePl": "Activ", "activeShe": "Activ", "alias": "Alias / poreclă", "allowedValues": "valori admise", "alrBlockingAction": "Există o acțiune pentru a finaliza! Vă rugăm să așteptați un moment ...", "alrCalculationsDocsLoadFailed": "Imposibil de încărcat documentele calculelor", "alrConvertErrorJsonNeon": "<PERSON><PERSON>re la conversia lui json -> neon.", "alrCronReset": "Cronul a fost resetat la implicit", "alrCronResetFailed": "Resetarea Cron nu a reușit.", "alrCronRestart": "Cronul a fost repornit", "alrCronRestartFailed": "Solicitarea de a reporni cronul nu a reușit.", "alrCronRunFailed": "Run Cron a eșuat.", "alrCronRunning": "Cronul a fost pornit.", "alrCronStopFailed": "Cererea de oprire cron nu a reușit.", "alrCronStopped": "Cronul a fost oprit.", "alrCronUpdated": "Cronul a fost salvat cu succes", "alrCronUpdateFailed": "Solicitarea de actualizare cron nu a reușit.", "alrFavouritesActionExist": "Această acțiune este deja favorită", "alrGuidesLoadFailed": "Ghidurile nu au putut fi încărcate", "alrInvalidData": "Date nevalide.", "alrInvalidImportData": "Datele de import nevalide", "alrMaintenanceMsg": "Întreținerea programată este în curs, încercați mai tâ<PERSON>.", "alrPresetLogFiltersLoadFailed": "Filtrele jurnal presetate nu au putut fi încărcate", "alrProcessRestart": "Procesul Cron a fost repornit!", "alrProcessRestartFailed": "Solicitarea de a reporni procesul nu a reușit.", "alrScriptDocsFailed": "Imposibil de salvat documentația de script.", "alrScriptLoadFailed": "Scripturile nu au putut fi încărcate", "alrUsageStatsLoadFailed": "Imposibil de încărcat statisticile de utilizare.", "authMethod": "Autentificare", "authentication": "metoda de autentificare", "calculationsLogs": "Jurnalul de calcul", "caseId": "ID-ul solicitarii", "caseOverview": "Prezentarea solicitarii", "cDay": "zi", "cMinute": "minute", "cMonth": "lună", "confirmResetCronDialog": "<PERSON><PERSON>r doriți să resetați cron la setările din fabrică?", "confirmRestartCronDialog": "<PERSON><PERSON>r doriți să reporniți cronul selectat?", "confirmRestartProcessDialog": "Sigur doriți să reporniți întregul proces cron? Feriți-vă de restartarea completă a tuturor cron-urilor și se va întâmpla întregul context.", "confirmRunCronDialog": "<PERSON><PERSON>r doriți să rulați cronul selectat?", "confirmStopCronDialog": "<PERSON><PERSON>r doriți să opriți cronul selectat?", "confirmUpdateCronDialog": "<PERSON><PERSON><PERSON> doriți să modificați starea cron?", "contrab": "Cron {{variable}} câmp", "cronParams": "Parameter<PERSON>", "crons": "Crons", "cronsHistory": "Istoricul crons", "cSecond": "secunde", "currentlyRunning": "În prezent rulează", "cWeekDay": "zi a săptămânii", "dateFormat": "LL/ZZ/AAAA", "dayAllowedValues": "valorile admise", "days": "zile", "default": "Mod implicit", "defaultLblIt": "Implicit {{label}}", "defaultLblShe": "Implicit {{label}}", "documentation": "Documentație", "durationInMs": "<PERSON><PERSON> (ms)", "endCalDate": "Data de încheiere", "everyMonthDay": "în fiecare zi a lunii", "everyHour": "fi<PERSON><PERSON> or<PERSON>", "everyMin": "fiecare minut", "everyMonth": "<PERSON><PERSON><PERSON> lună", "everySec": "fi<PERSON><PERSON> secund<PERSON>", "everyWeekDay": "fiecare zi a săptămânii", "guide": "<PERSON><PERSON><PERSON>", "guideFs": "<PERSON><PERSON><PERSON>", "guides": "<PERSON><PERSON><PERSON><PERSON>", "here": "<PERSON><PERSON>", "hourAllowedValues": "valorile permise; 0 este miezul nopții", "hours": "ore", "changeLang": "Schimbă limba", "cHour": "oră", "inactiveShe": "Inactiv", "insertSnippet": "Inserați un fragment", "language": "<PERSON><PERSON><PERSON><PERSON>", "lastRun": "<PERSON><PERSON><PERSON> rulare", "linkPriority": "Legătură prioritară", "listOfDay": "o listă de zile; adică. 1,15 ar fi prima și a 15-a zi a lunii ", "listOfHour": "o listă de ore, adică 0,12 ar fi 0 și 12 ore", "listOfMin": "o listă de minute, ad<PERSON><PERSON> 0,30 ar fi 0 și 30 minute", "listOfMonth": "o listă de luni, adic<PERSON> 1,6 ar fi ianuarie și iunie", "listOfSec": "o listă de secunde, adică 0,30 ar fi 0 și 30 de secunde", "listOfWeekDay": "o list<PERSON> de zile, adic<PERSON> 1,5 ar fi luni și vineri", "manualEvents": "Evenimente manuale", "matchesRegular": "Nu corespunde regulii", "minuteAllowedValues": "valori admise; 0 este partea de sus a orei", "minutes": "minute", "mobile_app_paired_users": "Numărul utilizatorilor aplicației mobile conectați", "module": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "newTask": "Sarcină nouă", "next": "Următorul", "nextRun": "<PERSON><PERSON>", "noTasks": "Fără actiune nouă", "emptyFavs": "Lista de favorite este goală", "notInRightormat": "Formular invalid", "onEnd": "La sfarșit", "onHand": "<PERSON>", "onRecalc": "La recalculare", "onStart": "La început", "operation": "operație", "originalScript": "Scenariul original ", "paramLabel": "Numele parametrului", "params": "Parametrii", "presetFilters": "Filtre presetate", "previous": "Anterior", "rangeOfDay": "o serie de zile; adică. 1–5 ar fi zilele 1, 2, 3, 4 și 5 (puteți specifica și o listă de intervale 1–5, 14–30) ", "rangeOfHour": "o serie de ore, ad<PERSON><PERSON> 19–23 ar fi orele 19, 20, 21, 22 și 23 (puteți specifica și o listă de intervale 0–5,12–16)", "rangeOfMin": "o serie de minute, ad<PERSON><PERSON> 0–5 va fi minute 0, 1, 2, 3, 4 și 5 (puteți specifica și o listă de intervale 0–5,30–35)", "rangeOfMonth": "un interval de luni, ad<PERSON><PERSON> 1–3, vor fi <PERSON><PERSON><PERSON><PERSON>, februarie și martie (puteți specifica și o listă de intervale 1–4,8–12)", "rangeOfSec": "un interval de secunde, ad<PERSON><PERSON> 0–5 ar fi secundele 0, 1, 2, 3, 4 și 5 (puteți specifica și o listă de intervale 0–5,30–35)", "rangeOfWeekDay": "o serie de zile, <PERSON><PERSON><PERSON> 1–5 <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (puteți specifica și o listă de intervale 0–2,4–6)", "reactivatesPlan": "reactivează planul", "redirBefStart": "Înainte de a începe, redirecționați către", "refreshPage": "Reîmprospătați pagina", "replyRecipient": "Răspunde destinatarului", "report": "<PERSON><PERSON>", "restart": "Reseteaztă", "restartCronProcess": "Resetați contextul procesului ", "searchInSuspended": "Căutați și în cazuri suspendate", "secondAllowedValues": "valori admise; 0 este partea de sus a minutelor", "seconds": "secunde", "sharedVar": "Variabila comună ", "slashDay": "valorile pasului vor sări peste numărul specificat într-un interval, adică * / 4 este la fiecare 4 zile, iar 1–20 / 2 este la fiecare 2 zile între prima și a douăzecea zi a lunii", "slashHour": "valorile pasului vor sări peste numărul specificat într-un interval, adică * / 4 este la fiecare 4 ore și 0–20 / 2 este la fiecare 2 ore între 0 și 20 de ore", "slashMin": "valorile pasului vor sări peste numărul specificat într-un interval, adică * / 5 este la fiecare 5 minute și 0–30 / 2 este la fiecare 2 minute între 0 și 30 de minute", "slashMonth": "valorile pasului vor sări peste numărul specificat într-un interval, adică * / 4 este la fiecare 4 luni și 1–8 / 2 este la fiecare 2 luni între ianuarie și august", "slashSec": "valorile pasului vor sări peste numărul specificat într-un interval, adică * / 5 este la fiecare 5 secunde și 0–30 / 2 este la fiecare 2 secunde între 0 și 30 de secunde", "slashWeek": "valorile pasului vor sări peste numărul specificat într-un interval, adică * / 4 este la fiecare 4 zile, iar 1–5 / 2 este la fiecare 2 zile între luni și vineri", "snippet": "Fragment de cod", "socketBroken": "Restaurați conexiunea pentru actualizarea actuală a datelor ", "socketOk": "Tabelul conține cele mai recente date", "solvers_or_can_create_a_process": "Numărul de utilizatori care au rezolvat o activitate sau pot executa un proces", "startCalDate": "Data de începere", "stop": "Stop", "syntax": "Sintaxă", "targetElementNotFound": "Elementul țintit nu a fost găsit", "task": "Actiune", "taskAlreadyEdited": "Actiunea este deja editată de un alt utilizator.", "taskEditedByAnotherUser": "Un alt utilizator a început să editeze actiunea.", "tempAlreadyEdited": "Șablonul este deja editat de un alt utilizator.", "tempEditedByAnotherUser": "Un alt utilizator a început să editeze șablonul.", "test": "Test", "timeRange": "Interval de timp", "tomorrow": "M<PERSON><PERSON>", "translatedScript": "<PERSON><PERSON><PERSON> tradus", "tskId": "ID-ul actiunii", "ttCaseReport": {"heading": "<PERSON><PERSON>", "body": ""}, "ttResetCron": {"heading": "Reset cron", "body": ""}, "ttRestartCron": {"heading": "Reseteaz<PERSON> cron", "body": ""}, "ttRestartCronProcess": {"heading": "Resetează procesele", "body": ""}, "ttRunCron": {"heading": "Ruleaz<PERSON> cron", "body": ""}, "ttSearchHeader": {"heading": "Caut<PERSON>", "body": ""}, "ttStatusCron": {"heading": "Statut", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttTableExportCsv": {"heading": "Tabel de export", "body": "Exportă tabelul în fișierul csv"}, "ttTableExportExcel": {"heading": "Tabel de export", "body": "Exportă tabelul în fișierul xlsx"}, "ttInvitation": "Invitație", "useCompatibleDynTable": "Utilizați tabela de date compatibilă", "usageStats": "Statistici de utilizare", "users_that_solved_a_task": "Numărul de utilizatori care au rezolvat cel puțin o actiune", "usersRights": "<PERSON><PERSON><PERSON><PERSON> utilizatorilor", "visPerRole": "Vizibilitate pe rol", "weekAllowedValues": "valori admise; 0=dumini<PERSON><PERSON>, 1=luni, 2=ma<PERSON><PERSON><PERSON>, 3=mi<PERSON><PERSON><PERSON>, 4=joi, 5=<PERSON><PERSON>, 6=sâmb<PERSON>tă ", "weeks": "Săptămâni", "yesterday": "<PERSON><PERSON>", "onPull": "Înainte de preluarea", "bcRecipient": "Destinatarul copiei ascunse", "copyRecipient": "Destinatarul copiei", "archivedLogs": "Arhivă jurnale", "basicMode": "<PERSON>d <PERSON>", "expertMode": "Mod expert", "ttBasicMode": {"heading": "<PERSON>d <PERSON>", "body": "Ascunde unele elemente sau opțiuni în formular."}, "ttExpertMode": {"heading": "Mod expert", "body": "Afișează elementele sau opțiunile ascunse în formular."}, "helpOverviewFolder": "Puteți include rapoartele în structura de directoare folosind șublețe.<br /><i>(de ex., Facturile/Toate facturile primite)</ i>", "helpOverviewIncludeSimilar": "Dacă select<PERSON>i, vor fi afișate și solicitari din alte antete ale unui șablon.", "emptyHe": "Gol", "helpOverviewSysVars": "Câmpurile marcate cu (sys) sunt câmpuri de sistem care fac parte din fiecare proces.", "customization": "Personalizare", "elementColor": "Culoare element", "fontColor": "Culoarea fontului", "fontSize": "<PERSON><PERSON><PERSON> fontului", "bold": "Ingrosat", "cursive": "Inclinate", "off": "Stop", "toPlan": "Planificare", "alrMaintenanceComing": "La {{time}} va începe întreținerea planificată a sistemului. Vă salvați munca.", "timeoutHMS": "Timeout: (hh:mm:ss)", "eventw": "<PERSON><PERSON><PERSON> \"{{task}}\" din șablonul \"{{template}}\" așteptând acest eveniment", "waitsForEventTip": "<PERSON>az în așteptare pentru eveniment: \"{{event}}\"", "copyToMultiinstances": "Copiați în Instanțe multiple", "showAsPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alrPreviewAttachmentsFailed": "Previzualizarea nu a reușit", "alrPreviewAttachmentsWrongFormat": "Previzualizarea nu a reușit – formatul de fișier neacceptat", "previewNotAvailable": "Vizualizarea documentului nu este posibilă din cauza tipului de document.", "configuration": "Configurație", "values": "Valori", "defaultValues": "Valori implicite", "ttSubscribeCv": {"heading": "Stabiliti vizualizarea generala", "body": "Prezentarea generală selectată vă va fi trimisa prin e-mail în fiecare zi a săptămânii, la ora stabilită."}, "subscribe": "Abonați-vă", "time": "<PERSON><PERSON>", "externalLang": "Limbă externă", "hdrStatusQ": "Chiar doriți să schimbați starea antetului?", "small": "Mic", "medium": "Me<PERSON>u", "large": "Mare", "alrTemplTsksLoadFailed": "Actiunile din șablon nu au putut fi încărcate.", "applyInTasks": "Aplicați în actiuni", "caseStatuses": "St<PERSON>rile solicitarii", "statuses": "<PERSON><PERSON><PERSON><PERSON>", "Manuals": "Manuale", "forReading": "Pentru citire", "forReadWrite": "Pentru citire și scriere", "addVersion": "noua versiune", "size": "Mărimea", "prevWorkDay": "Ziua de lucru anterioară", "ttCreateTempVersion": {"heading": "Creați o nouă versiune a șablonului", "body": ""}, "version": "Versiune", "alrTempVersionsLoadFailed": "Versiunea de șablon nu a putut fi încărcată.", "alrChangeTempVersionFailed": "Nu sa reușit modificarea versiunii șablonului.", "alrCreateTempVersionFailed": "Crearea unei noi versiuni a șablonului a eșuat.", "confirmCreateTempVersion": "Sigur doriți să creați o nouă versiune a șablonului?", "applyInAllTasks": "Aplicați în toate actiunile", "mandatoryVar": "Variabilă obligatorie", "emptyRequiredVarMessage": "<PERSON><PERSON>, variabilă obligatorie este goală", "duration": "<PERSON><PERSON><PERSON>", "alrDynConditionsFailed": "Imposibil de îndeplinit actiunea. Vă rugăm să încercați să reîmprospătați pagina sau contactați Administratorul sau Helpdesk.", "caseActivation": "Activarea solicitarii", "average": "Me<PERSON>", "performanceLogs": "Jurnalele de performanță", "displayingOverview": "Vizualizați prezentarea", "taskSolve": "Finalizarea actiunii", "displayingCO": "Vizualizați solicitarile", "printCreation": "Crearea de imprimare", "entityId": "ID entitate", "copyTask": "Copierea actiunii", "checkProcessCompletion": "Verificarea realizarii procesului", "findingSolver": "Identificarea unui responsabil de solutionarea actiunii", "publicFiles": "Fișiere publice", "usage": "<PERSON><PERSON><PERSON><PERSON>", "serviceConsole": "Consola de servicii", "selectAll": "<PERSON><PERSON><PERSON><PERSON> toate", "logos": "Logos", "overviewWithTasks": "Privire de ansamblu a actiunilor", "printIsReady": "Imprimarea este gata", "alrChangelogLoadFailed": "Changelog nu a reușit să se încarce.", "inJs": "În scenariu", "ttCopyDtDefinition": {"heading": "Copiază definiția tabelului", "body": "Copiază definiția tabelului dinamic selectat."}, "confirmCopyTableDefinition": "<PERSON><PERSON><PERSON> doriți să copiați definiția tabelului?", "alrCopying": "Copierea...", "alrCopyFailed": "Copierea eș<PERSON>tă.", "fallback": "Fallback", "syncEnabled": "Sincronizare", "systemGuideNote": "Conținutul Ghidului de sistem nu poate fi modificat. Pentru a vizualiza alt conținut, faceți ca ghidul de sistem să fie inactiv și copiați conținutul acestuia într-un nou ghid.", "alrAnotherUserLogged": "Un alt utilizator este conectat într-o altă fereastră!", "userLocked": "Utilizatorul este blocat", "visInternalUserOnly": "Vizibil numai pentru utilizatorii interni", "showSelectedOnly": "Afișați doar selectat", "clickToSelect": "<PERSON><PERSON><PERSON> clic pentru a selecta", "restrictRoleAssignment": "Restricţii alocarea rolului pentru rol", "restrictions": "Restricţie", "restrictTableHandling": "Restricţii manipularea tabelelor", "toRole": "La rol", "inCalcToHeader": "În calcule la antet", "loginBtnColor": "Culoarea butonului de autentificare", "certificates": "Certificate", "certificate": "Certificat", "certificateVar": "certificat", "tspSources": "<PERSON><PERSON><PERSON> de timp", "tspSource": "<PERSON><PERSON><PERSON><PERSON> de timp", "confirmExpandDynRowsNewAssignments": "At<PERSON><PERSON>, alocare noua! Variabilele nu au axe setate. <PERSON><PERSON><PERSON> să extinzi toate rândurile dinamice?", "confirmExpandDynRows": "Ești sigur că vrei să extinzi toate rândurile dinamice?", "expandDynRows": "Extindeti rându<PERSON> din<PERSON>", "visible": "Vizibil", "cvcDbColumn": "<PERSON><PERSON><PERSON>", "cvTableSource": "Tabelul sursă", "uploadedFromFile": "Înc<PERSON><PERSON>t dintr-un fișier", "appStatus": "Starea aplicației", "loadAll": "Încarcă totul", "ignoredUsers": "Utilizatorii ignorați", "copyRolesFrom": "Copiați rolurile din", "disableFrontendStyles": "<PERSON>u aplicați stiluri automate", "activate": "Activati", "confirmActivateCase": "Chiar vrei să activezi actiunea?", "alrLackOfPerms": "Nu ai permisiunea.", "alrSending": "Se trimite...", "sequences": "Secvențele", "seqName": "Secvenţă", "seqId": "Secvență ID", "seqLastRead": "Ultima citire", "ttCopyRole": {"heading": "Copie rol", "body": "Creează o copie a rolului selectat."}, "fromCase": "<PERSON> solicitare", "includingData": "Inclusiv date", "choose": "Alege", "valueChange": "Modificarea valorii", "updateInstances": "Includeți modificarea variabilelor în variabilele de instanță", "addNewCalcScript": "Adăugați un nou script", "useCompatibleCalcScript": "Utilizați un script compatibil", "externalSource": "Sursa utilizatorului", "reports": "Rapoarte", "confirmCopyReport": "<PERSON><PERSON>r doriți să copiați raportul selectat?", "graphs": "Grafice", "aggregation": "Agregare", "graphNew": "<PERSON><PERSON> – nou", "confirmCopyGraph": "<PERSON><PERSON>r doriți să copiați graficul selectat?", "alrCopyGraphFailed": "Copierea graficului nu a reușit.", "label": "Etiche<PERSON>", "pie": "<PERSON><PERSON>", "line": "<PERSON><PERSON> linia<PERSON>", "dot": "<PERSON><PERSON> de puncte", "bar": "<PERSON><PERSON>", "barGroups": "<PERSON><PERSON> grup<PERSON>", "alrFailedGraphData": "Graficul nu a putut fi încărcat.", "graphSetSharing": "Setați partajarea graficelor pentru fiecare grup de utilizatori", "alrGraphPointsLoadFailed": "Încărcarea punctelor grafice a eșuat.", "alrGraphNotFound": "Graficul nu a fost găsit.", "graphData": "Date grafice", "pointsData": "Puncte grafice", "alrGraphSaveFailed": "Salvarea graficului a eșuat!", "graphPoint": "Punct grafic", "noOrder": "<PERSON><PERSON><PERSON><PERSON> sortare", "refreshGraph": "Actualizați graficul", "viewSwitcher": "Filtru global", "axisXglobalFilter": "Axa X – filtru global", "axisXgroups": "Axa X – grupuri", "axisXdata": "Axa X – date", "axisYvalues": "Axa Y – valori", "axisYcolors": "Axa Y – culori", "hrAgenda": "Agenda HR", "userChange": "Schimbare de utilizator", "newUser": "Utilizator nou", "usersCount": "<PERSON><PERSON><PERSON><PERSON> utiliza<PERSON>i", "confirmChangeUser": "<PERSON><PERSON>r doriți să schimbați utilizatorul?", "businessVariable": "Variabilă de afaceri", "casesCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selected": "Selectat", "selectedOnly": "Numai selectat", "addCaseRightNewUser": "Adăugați acces la caz", "visFromTaskToPull": "Vizualizare de la o actiune de preluat", "toChangeConfigInfo": "Pentru a modifica, ștergeți valoarea din fișierul local.js", "clickToChange": "<PERSON><PERSON><PERSON> clic pentru a modifica", "currentValue": "Val<PERSON><PERSON>", "sign": "Semn", "validationProtocols": "<PERSON><PERSON><PERSON><PERSON>", "plannedEvents": "Evenimente", "elArchiv": "E-arhiva", "deletedDocs": "<PERSON><PERSON>", "signatures": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ttArchive": {"heading": "Arhivă electronică", "body": ""}, "ttAddToZea": {"heading": "Adăugați la arhiva electronică", "body": ""}, "ttRemoveFromZea": {"heading": "Eliminați din arhiva electronică", "body": ""}, "ttZeaInfo": {"heading": "Validare", "body": ""}, "ttSignZea": {"heading": "Semnați la <PERSON>", "body": ""}, "addToZea": "Adăugați la e-arhiva", "removeFromZea": "Ștergeți din e-arhiva", "reTimestampAfter": "Valabilitatea ștampilei generate (zile)", "alrLoadFailed": "Încărcarea a eșuat.", "replace": "<PERSON><PERSON><PERSON><PERSON>", "expireAt": "Expir<PERSON>", "result": "Rezultatul validării", "validatedAt": "Data validării", "refType": "Obiect", "eventType": "Tipul de acțiune", "errorMessage": "<PERSON><PERSON>", "errorTimestamp": "Data erorii", "errorCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inFuture": "Planificate", "path": "Calea de semnătură", "signedAt": "Data la care a fost creată semnătura", "dropZoneZeaCertificate": "Aruncați o certificat aici sau faceți clic pentru a selecta un fișier pentru încărcare.", "authType": "Tip de autentificare", "basic": "Numele de utilizator și parola", "byCert": "Cu certificat", "alrMissingCertFile": "<PERSON>ă rugăm să introduceți un certificat.", "replaceTo": "Înlocuiți la", "autoReTimestamp": "Ștampilă automată", "validate": "Validare", "lastUse": "Ultima generată", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Actualizat", "certificateId": "ID certificat", "expectedCreationTime": "Vor fi adăugate", "nextTSPSourceId": "ID-ul următoarei ștampile", "reTimestampAt": "<PERSON><PERSON><PERSON><PERSON>", "timestampedAt": "Ultima stampila", "level": "<PERSON><PERSON>", "signatureB": "Semnătura de bază", "signatureT": "Semnătură cu un marcaj de timp", "signatureLt": "Semnătură cu certificate de date pe termen lung", "signatureLta": "Semnătură cu certificate de date pe termen lung și timestamp de arhivă", "packaging": "Ambalare", "enveloped": "Înfășurat", "enveloping": "Plic", "detached": "Detașat", "algorithm": "Algoritmul", "uploadAsRevision": "Încărcați ca revizie", "externalDisable": "Activ doar pentru semnături multiple", "addToDms": "Adăugați la DMS", "autoConvert": "Convertiți automat", "format": "Format", "signatureType": "Tipul de semnătură", "signature": "Semnătură", "custom": "<PERSON><PERSON><PERSON><PERSON>", "batchSignDisabled": "<PERSON><PERSON><PERSON><PERSON> semn", "tasId": "ID document", "hashValue": "Valoarea hash", "hashType": "Tipul funcției de hash", "confirmAddToArchive": "Chiar vrei să adaugi la arhivă?", "independentSignature": "Semnătura independentă", "independentValidation": "Validare independentă", "failureTrue": "Cu o eroare", "failureFalse": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "confirmValidateDialog": "Chiar vrei să validați semnătura?", "confirmRestartDialog": "Chiar vrei să resetați erorile?", "verificationResult": "Rezultatul verificării", "integrityMaintained": "Integritatea menținută", "signatureFormat": "Format de semnătură", "internalTimestampsList": "Lista timbrelor interne", "signers": "<PERSON><PERSON><PERSON><PERSON>", "exhibitedBy": "Postat de", "signedBy": "Se<PERSON><PERSON> de", "validFrom": "Valabil de la", "validUntil": "Valabil până la", "signitureType": "Tipul de semnătură", "signatureQualification": "Incadrarea semn<PERSON>ii", "signatureNoTimestamps": "Semnătura nu conține timestampe", "electronicSignature": "Semnătura electronică", "electronicSeal": "Sigiliul electronic", "webSiteAuthentication": "Autentificarea site-ului web", "QCP-n": "QCP-n: politica certificatelor pentru certificatele UE calificate eliberate persoanelor fizice", "QCP-l": "QCP-l: politica certificatelor pentru certificatele calificate UE emise persoanelor juridice", "QCP-n-qscd": "QCP-n-qscd: politica certificatelor pentru certificatele UE calificate emise persoanelor fizice cu cheie privată referitoare la cheia publică certificată într-un QSCD", "QCP-l-qscd": "QCP-l-qscd: politica certificatelor pentru certificatele calificate UE emise persoanelor juridice cu cheie privată referitoare la cheia publică certificată într-un QSCD", "QCP-w": "QCP-w: politica certificatelor pentru certificatele de autentificare a site-ului web calificate UE", "formOfReStamping": "Forma de re-timbrare", "individually": "Individual", "archiveAsPdf": "Arhivați în format PDF", "couldNotBeVerified": "Imposibil de verificat", "uses": "Numă<PERSON><PERSON> u<PERSON>iz<PERSON>", "countOfSignedDocuments": "Numărul de documente semnate", "batchSignature": "Semnătura blocată", "standaloneSign": "Semnătura individuală", "validateSignature": "Validarea semnăturii", "validateDoc": "Validarea documentului", "containsSignature": "<PERSON><PERSON>ine semnă<PERSON>", "reStamping": "Ștampilă repetată", "individualSignatures": "Semnături individuale", "signatureLevel": "<PERSON>vel de semnătură", "simpleReport": "Raport simplu", "detailedReport": "Raport de<PERSON>", "diagnosticReport": "Raport de <PERSON>", "etsiReport": "Raport ETSI", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "Esuat", "INDETERMINATE": "Nedeterminat", "FORMAT_FAILURE": "Semnătura nu respectă unul dintre standardele de bază", "HASH_FAILURE": "Hash-ul obiectului de date semnat nu se potrivește cu hash-ul din semnătură", "SIG_CRYPTO_FAILURE": "Semnătura nu a putut fi verificată cu cheia publică a semnatarului", "REVOKED": "Certificatul de semnătură a fost revocat și există dovezi că semnătura a fost creată după revocare", "SIG_CONSTRAINTS_FAILURE": "Unul sau mai multe atribute de semnătură nu corespund regulilor de validare", "CHAIN_CONSTRAINTS_FAILURE": "Lista de certificate utilizata în procesul de validare nu respectă regulile de validare a certificatului", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "Setul de certificate disponibile pentru verificare string a generat o eroare fara un motiv specificat", "CRYPTO_CONSTRAINTS_FAILURE": "Unul dintre algoritmii de verificare a semnăturii este sub nivelul de securitate criptografic necesar și semnătura a fost dobândită după durata de viață a algoritmului", "EXPIRED": "Semnătura a fost creată după expirarea certificatului de semnătură", "NOT_YET_VALID": "Timpul de semnare este înainte de data emiterii certificatului de semnare", "POLICY_PROCESSING_ERROR": "Fișierul politicii de validare nu a putut fi procesat", "SIGNATURE_POLICY_NOT_AVAILABLE": "Documentul electronic care conține detalii despre politica de validare nu este disponibil", "TIMESTAMP_ORDER_FAILURE": "Restricțiile în ordinea semnării timpului semnăturii nu sunt respectate", "NO_SIGNING_CERTIFICATE_FOUND": "Certificatul de semnare nu poate fi identificat", "NO_CERTIFICATE_CHAIN_FOUND": "Nu a fost găsit niciun lanț de certificat pentru certificatul de semnătură identificat", "REVOKED_NO_POE": "Certificatul de semnare a fost revocat la data / ora validării. Cu toate acestea, algoritmul de verificare a semnăturii nu poate detecta că timpul semnării este înainte sau după perioada de revocare", "REVOKED_CA_NO_POE": "A fost găsit cel puțin un lanț de certificate, dar un certificat CA temporar a fost revocat", "OUT_OF_BOUNDS_NOT_REVOKED": "Certificatul de semnare este expirat sau nu este încă valabil la data / ora validării, iar algoritmul de validare a semnăturii nu poate stabili dacă timpul de semnare se află în intervalul de validitate al certificatului de semnare. Se știe că certificatul nu este revocat.", "OUT_OF_BOUNDS_NO_POE": "Certificatul de semnare a expirat sau nu este încă valid la data / ora verificării", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "Unul dintre algoritmii de verificare a semnăturii este sub nivelul de securitate criptografic necesar și nu există nicio dovadă că acesta a fost produs înainte ca algoritmul / cheia să fie considerat sigur.", "NO_POE": "Nu există nicio dovadă că obiectul semnat a fost creat înainte de un eveniment compromis", "TRY_LATER": "Nu toate regulile de validare pot fi îndeplinite cu informațiile disponibile, dar poate fi posibil să se facă acest lucru cu informații suplimentare de revocare care vor fi disponibile ulterior", "SIGNED_DATA_NOT_FOUND": "Datele semnate nu pot fi obținute", "GENERIC": "Other Reason", "signatureFile": "Fișier de semnătură", "validityDays": "Zile de valabilitate", "qualifiedHe": "Calificat", "qualifiedIt": "Calificat", "unqualifiedHe": "Necalificat", "unqualifiedIt": "Necalificat", "timeValid": "<PERSON><PERSON>", "reason": "Motiv", "inTime": "în timp", "certificateQualification": "Calificarea certificatului", "guaranteedHe": "Garantat", "guaranteedIt": "Garantat", "fromQualifiedCert": "De la cert. calificat", "basedOnQualifiedCertHe": "Pe baza unui certificat calificat", "createdByQualifiedResHe": "Creat de resurse calificate", "basedOnQualifiedCertIt": "Pe baza unui certificat calificat", "createdByQualifiedResIt": "Creat de resurse calificate", "qualification": "Cal<PERSON><PERSON><PERSON>", "confirmRemoveFromZeaDialog": "<PERSON><PERSON><PERSON> do<PERSON>i să eliminați {{variable}} din arhiva electronică?", "noValidationReports": "Nu există rezultate de validare", "noSignatures": "Nu există semnături individuale", "isHigherOrEqualThan": "Trebuie să fie mai mare sau egal cu", "isInZea": "În e-arhivă", "startStamping": "Începeți ștampila", "reTimestampAfterMinutes": "minute", "reTimestampAfterDays": "zile", "reTimestampAfterAll": "Valabilitatea ștampilei generate", "refId": "ID obiect", "docWithoutAutoTimestampInfo": "Documentul va fi semnat o singură dată, fără introducerea automată a timper-ului.", "validationReports": "Istoric de validare", "docPath": "Calea documentului", "addToArchiveInvalidSignatureError": "Fișierul nu a putut fi arhivat deoarece conține o semnătură care nu poate fi verificată.", "signImmediately": "Semnează imediat", "replaceInConfiguration": "Înlocuiți în configurație", "cancel": "<PERSON><PERSON><PERSON>", "bulk": "Multiplu", "bulkCompletion": "Finalizare multipla", "enableBulkCompletion": "Activați finalizarea multipla", "confirmCompleteTasks": "Doriți cu adevărat să finalizați actiunile?", "plannedMaintenance": "Intretinere programata", "notSent": "Nu trimis", "notSpecified": "Nespecificat", "bulkCompletionVars": "Variabile de finalizare multipla", "alrBulkCompletionMultiTypeErr": "Numai actiunile de același tip pot fi finalizate multiplu, puteți utiliza un filtru.", "notifications": "Notific<PERSON><PERSON>", "alrTskAlreadyTakenSomeone": "Altcineva și-a asumat deja actiunea.", "alrTskAlreadyTaken": "Actiunea a fost deja preluată.", "downloadBpmn": "descarca BPMN diagramă", "downloadSvg": "descărcați ca SVG imagine", "displayForm": "<PERSON><PERSON>", "selectedPreview": "este afișată previzualizarea", "fixedHeight": "Înălțime fixă ​​(în pixeli)", "lastVersion": "Ultima versiune", "separatedPreview": "Previzualizare separată", "defaultZoom": "Zoom implicit", "fixedPosition": "Pozi<PERSON><PERSON>", "percentInterval": "Vă rugăm să completați numărul întreg între 0–5", "notPositiveNumber": "Numai numere pozitive", "zoomDown": "Micșoreaza", "zoomUp": "Amplifica", "rotate": "<PERSON><PERSON><PERSON><PERSON>", "logTooBig": "Jurnalul este prea mare pentru a fi redat.", "downloadLog": "Jurnal de descărcare", "confirmCopyCron": "Chiar vrei să copiezi cron selectată?", "ttCopyCron": {"heading": "Copia cron", "body": ""}, "onlyWorkingDays": "<PERSON><PERSON><PERSON> z<PERSON> l<PERSON>", "datesDisabled": "Dezactivați datele", "useView": "Utilizați View", "dateWithoutTime": "<PERSON> fără timp", "timezone": "<PERSON><PERSON> orar", "roleRestriction": "Restricția rolului", "headerRestriction": "Restric<PERSON>ie antet", "ttSwitchDarkmode": {"heading": "Comutarea modului luminos / întunecat", "body": ""}, "advancedEditor": "Editor avansat", "externalId": "ID extern", "passwordValidationMin": "Parola este prea scurtă. (lungime minimă: {{count}})", "passwordValidationMax": "Parola este prea lungă. (lungime maxima: {{count}})", "passwordValidationUppercase": "Parola trebuie să conțină o literă mari. {{atLeast}}", "passwordValidationLowercase": "Parola trebuie să conțină o literă mică. {{atLeast}}", "passwordValidationSymbols": "Parola trebuie să conțină simbolul. {{atLeast}}", "passwordValidationDigits": "Parola trebuie să conțină un număr. {{atLeast}}", "passwordValidationLetters": "Parola trebuie să conțină o scrisoare. {{atLeast}}", "atLeast": "<PERSON>ar", "passwordValidationServiceErr": "Parola nu poate fi schimbată în acest moment.", "enableTasksHandoverRole": "Permiteți întotdeauna redirecționarea sarcinilor și declanșarea evenimentelor pentru utilizatorii acestui rol", "shredded": "Mărunțite", "shredableVar": "Variabilă mărunțită", "shredDocuments": "Distrugerea documentelor", "shredInDays": "Distrugere în (zile)", "fromBeginningOrendOfCase": "De la începutul/sfârșitul cazului", "shredding": "Mărunț<PERSON>", "addColumn": "Adăugați coloană", "unsupportedBrowser": "Deschideți TeamAssistant într-un browser Internet Explorer neacceptat, este posibil ca unele funcții să nu fie disponibile.", "ingoreProcessRights": "Ignorarea drepturilor ca<PERSON>i", "cvHelpIngoreProcessRights": "Raportul arată întotdeauna toate cazurile, indiferent de drepturi", "upperLabel": "Plasați variabila sub numele ei", "darkMode": "<PERSON><PERSON>", "completedTasks": "Sarcini finalizate", "permissions": "Per<PERSON><PERSON><PERSON>", "caseVisibility": "Vizibilitatea cazului", "visPerOrg": "Vizibilitate pe unitate org.", "entity": "Entitate", "staticRight": "Legea statică", "dynamicRight": "<PERSON><PERSON><PERSON>", "treeNodesAll": "Toate", "treeNodesMy": "<PERSON><PERSON>", "activeQueries": "Interogări active", "query": "Interogare", "confirmCancelQuery": "<PERSON><PERSON><PERSON> do<PERSON>i să anulați interogarea?", "alrQueryNotFound": "Interogarea nu a mai fost găsită.", "completeAgenda": "Agenda completă", "lockedBusinessUsers": "Utilizatori de afaceri blocați", "structuredList": "Lista structurata", "ttCompetences": {"heading": "Managementul competențelor", "body": ""}, "competences": "Competențe", "competence": "Competență", "competenceDelVar": "competența", "addCompetence": "Adaugă competență", "regularExpression": "Expresie uzuala", "generationStatus": "Starea <PERSON>i", "source": "Sursă", "historical": "Istorica", "external": "Extern", "nextDay": "ziua urma<PERSON>", "embeddedVideoNotSupported": "<PERSON><PERSON> pare rău, browserul dvs. nu acceptă videoclipuri încorporate.", "alrSendingTestMailFailed": "E-mailul de testare a eșuat.", "sent": "<PERSON><PERSON>.", "mainColorEmail": "Culoarea principală e-mailului", "confirmResetColors": "Sigur vrei să resetați culorile?", "regularExpressions": "Expresii obisnuite", "confirmDeleteLogo": "Si<PERSON>r doriți să ștergeți sigla?", "loginLogoLightTheme": "Sigla ecranului de conectare (mod luminos)", "loginLogoDarkTheme": "Sigla ecranului de conectare (mod întunecat)", "competenceRegexHelper": "<ul><li><b>%</b> poate fi folosit pentru N caractere arbitrare (echivalent *)</li><li><b>_</b> poate fi folosit ca un singur caracter arbitrar (echivalent .)</li><li>Puteți folosi <b>^</b> pentru a scăpa de aceste caractere speciale (echivalent \\)</li></ul>", "headerFont": "Font antet", "evenRow": "<PERSON>ar rând", "logo": "Siglă", "widthForLogo": "Latime pentru logo", "monthStart": "Începutul lunii", "monthEnd": "Sfarsi<PERSON>l lunii", "ttFavouriteType": "GET deschide link-ul. POST trimite o comandă: de exemplu, atunci când se creează un caz, unde ID-ul antetului șablonului este trimis în corpul cererii (salvare în favorite prin New Case).", "confirmEmptyMultiinstanceVariable": "Sunteți sigur că această multi-instanță nu necesită o variabilă pe care să o iterați?", "ttMenuPreview": "Configurarea meniului după rolurile utilizatorilor (rolurile mai semnificative văd și butoanele pentru rolurile mai puțin semnificative). Butoanele Caz nou și Tablou de bord sunt neschimbate.", "menuPreview": "Previzualizare meniu pentru rolul selectat", "confirmResetMenu": "Sigur vrei să resetați meniul?", "alrFailedTasMenu": "Configurația meniului TAS nu a reușit să se încarce!", "security": "Securitate", "userRestrictions": "Restricții utilizatori (afişa)", "userRestrictionsProcesses": "Ignorați restricțiile utilizatorilor privind sarcinile", "roleRestrictions": "Restricții de rol (afişa)", "orgUnitRestrictions": "Restricții unități org. (afişa)", "everyone": "Toată lumea", "colleaguesOnly": "<PERSON>umai colegii", "directSubordinates": "Subordonați direcți", "allSubordinates": "Toți subordonații", "none": "<PERSON><PERSON> unul", "generalDocument": "Document general", "competenceRule": "Regula de competență", "competenceRules": "Reguli de competență", "ruleName": "<PERSON><PERSON>le regulii", "ttUseCompetenceRule": {"heading": "Aplicați regula", "body": "Creează o competență conform regulii selectate"}, "competenceText": "Textul de competență", "competenceName": "Denumirea competenței", "competenceReadOnlyInfo": "Competența creată din regulă nu poate fi modificată", "xmlProcessImport": "Import de proces XML", "ttWidthForLogo": "Setați lățimea pentru logo și apoi inserați un logo. Nu este posibilă modificarea lățimii pentru un logo deja inserat sau implicit.", "openCase": "Deschide caz", "importHistory": "Istoria importărilor", "plannedImports": "Importări planificate", "filePath": "<PERSON>", "cronId": "ID cron", "taskResult": "Rezultatul sarcinii", "xmlFileSize": "Mărimea fișierului XML", "attachmentSize": "Mărimea fișierului atașat", "lastEdit": "Ultima modificare", "timeCreated": "Data creării", "importId": "ID import", "importAudit": "Audit import", "finishedImports": "Importări finalizate", "insertNote": "Introduceți o notă", "importXml": "Import XML", "reImportXml": "Reimport XML", "downloadXml": "Descarcă XML", "downloadAttachment": "Descarcă atașament", "skipXml": "Ignoră XML", "note": "Notă", "attachmentName": "<PERSON>ume at<PERSON>", "importedCount": "Numărul <PERSON>uri", "retryCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "batchId": "ID doză", "copyPath": "Copiați calea", "cronRunId": "Alerga ID", "cronRun": "Cron alerga", "trace_id": "Urmăriți ID", "ttMenuItemLabel": "Nume universal dacă nu există traducere. Dacă se folosește un cuvânt cheie de traducere, acesta este tradus automat. Nume implicite: tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "Coada de sarcini", "dissolveQueue": "<PERSON><PERSON><PERSON>va coada", "taskQueueInitInfo": "Această acțiune a creat mai multe sarcini care trebuie rezolvate. Aici puteți modifica ordinea rezolvării acestora sau le puteți elimina complet din coada de așteptare.", "tryDarkTheme": "Am observat că preferați modul întunecat. Faceți clic pentru a-l încerca în TAS.", "alrInvalidURL": "Format URL nevalid.", "alrInvalidHttps": "Format URL nevalid, trebuie să înceapă cu https://", "importVariables": "Variabile de import", "ttVariablesImport": {"heading": "Importul de variabile", "body": "Se selectează un dosar cu definițiile variabile și apoi se încarcă."}, "classDiagram": "Diagrama de clasă", "createVar": "Creați variabilă", "importObjectStates": "Importați stările obiectului", "unassigned": "Nealocat", "sortVars": "Sortează", "fillNames": "Completați numele", "ttFillNames": {"heading": "Completați numele", "body": "Completează numele goale ale tuturor variabilelor noi în formatul „Clasă.Atribut” și sortează toate variabilele."}, "ttSortVars": {"heading": "Sortează", "body": "Sortează variabilele după clase și atribute."}, "ttRestore": {"heading": "Restaurare", "body": "Restaurează variabilele la starea lor inițială atunci când sunt importate dintr-un fișier."}, "ttAddVarToBottom": {"heading": "Adăugați o variabilă", "body": "Adaugă o nouă variabilă în partea de jos a paginii."}, "confirmRestoreForm": "Chiar doriți să restaurați variabilele la starea inițială?", "selectClass": "Marcaț<PERSON> c<PERSON>a", "importClassDiagram": "Importul unei diagrame de clasă.", "continue": "Continua", "templateVars": "Variabile de șablon", "newVars": "Variabile noi", "objectState": "Starea obiectului", "alrDynTableExists": "Tabelul dinamic există deja!", "overwriteExistDynTable": "Suprascrieți tabelul din. existent", "confirmCancelImport": "Sigur vrei să anulezi importul?", "alrDuplicateNames": "<PERSON><PERSON> conțin nume duplicate.", "stateVar": "Variabilă de stare", "importObjectStatesToDynTables": "Importați state obiect în tabele dinamice.", "defineObjectStatesVars": "Definiți variabilele care dețin stările obiectului.", "change": "Modifica", "classAndAttr": "Clasa si atribut", "clearQueue": "Șterge coada", "sharing": "Partajarea", "data": "Date", "open": "Deschide<PERSON><PERSON>", "dataSource": "Sursă de date", "dataPoints": "Puncte de date", "dataSeries": "<PERSON>ia de date", "valueCol": "Coloana de valo<PERSON>", "aggregationCol": "Coloană de agregare", "timeDimension": "<PERSON><PERSON><PERSON><PERSON><PERSON> timp.", "columns": "<PERSON><PERSON><PERSON>", "week": "săptămână", "weekday": "zile sapta<PERSON>i", "monthVar": "lună", "overviewFilter": "Filtrul de vedere de ansamblu", "globalFilters": "Filtre globale", "filterDefinition": "Definiția filtrului", "newFilter": "Filtru nou", "addFilter": "Adăugați filtru", "filterOptions": "Opțiuni de filtrare", "addOption": "Adăugați o opțiune", "graphPreview": "Previzualizarea graficului", "alrGlobalFilterDownloadFailed": "Descărcarea filtrelor globale nu a reușit!", "alrGlobalFilterSaveFailed": "Nu s-au salvat filtrele globale!", "filterOption": "Opțiune de filtrare", "editFilter": "Editați filtrul", "fillOptionsFromVar": "Completați opțiunile din variabilă", "fillOptionsDynamically": "Completați opțiunile în mod dinamic", "filterOptionsFilledDynamically": "Dinamic din variabilă", "dayOfMonth": "ziua lunii", "dateVar": "dat<PERSON>", "group": "Grup", "ttDataSource": "Alegeți \"Puncte de date\" dacă doriți să introduceți fiecare punct individual de diagramă separat. Dacă doriți ca punctele să fie generate pe baza dimensiunii selectate, ale<PERSON><PERSON><PERSON> \"Seria de date\"", "ttDataSeriesAggregation": "Alegeți tipul de agregare. Vă permite să creați informații rezumative din înregistrări (cazuri).", "ttDataSeriesColumns": "Selectați pe rând toate coloanele prin care să creați grupuri (agregații) pentru a calcula valorile rezumative.", "listOfFiltersIsEmpty": "Lista de filtre este goală.", "fromVariable": "Din variabilă", "showOptionsFromCount": "Afișați opțiunile (din {{count}})", "sum": "Sumă", "minimum": "<PERSON><PERSON>", "maximum": "Maxim", "statistics": "Statistici", "unfilled": "Necompletat", "globalFilterDescription": "Filtrul global oferă utilizatorilor diagramei opțiuni care filtrează datele de intrare pentru diagramă. Toate opțiunile de filtrare pot fi definite în acest ecran.", "ttDelGraph": {"heading": "Ștergeți graficul", "body": "Șterge graficul selectat."}, "ttEditGraph": {"heading": "Editați graficul", "body": "Vă permite să editați graficul selectat."}, "ttCopyGraph": {"heading": "Copiați graficul", "body": "Copiază graficul selectat."}, "ttAddGraph": {"heading": "Adăugați grafic", "body": "Vă permite să definiți un nou grafic."}, "axisXName": "Numele axei X", "axisYName": "Numele axei Y", "showValues": "Afișați valorile", "defaultOption": "Opțiune implicită", "yearStart": "Începutul anului", "yearEnd": "Sfârșitul anului", "thisMonth": "Luna aceasta", "lastMonth": "<PERSON> trecuta", "thisYear": "<PERSON><PERSON>", "lastYear": "<PERSON><PERSON> trecut", "scheduledTasks": "Sarcini programate", "scheduled": "Programate", "dueDateStart": "Data de începere", "lastRescheduled": "Ultima reprogramată", "reschedule": "Reprogramați", "alrTasksRescheduling": "Reprogramează sarcini...", "alrTasksRescheduled": "Sarcinile au fost reprogramate.", "alrTasksRescheduleFailed": "Nu s-au reprogramat sarcinile.", "onlyCurrentOrFutureDates": "Numai azi sau data viitoare", "passwordValidations": "Politica de parolă", "readonlyConfigInfo": "Valoarea este doar pentru citire", "alrTasksCountFailed": "Numărarea sarcinilor a eșuat.", "confirmActivateTasks": "Sunteți sigur că doriți să activați sarcinile selectate?", "confirmSuspendTasks": "Sunteți sigur că doriți să suspendați sarcinile selectate?", "tskOffset": "Planificare variabilă", "workWeek": "Săptămâna de lucru", "agenda": "Agendă", "noEventsInRange": "Nu există evenimente în acest interval", "activitiesDesc": "Descrierea activităților", "allShort": "Toate", "numberOfEvents": "Num<PERSON><PERSON><PERSON> de evenimente", "weekNumber": "num<PERSON><PERSON>ân<PERSON>", "cannotBeEdited": "Nu poate fi editat", "cannotBeMoved": "Nu poate fi mutat", "alrTempVarSaveSameNameFailed": "Există deja o variabilă cu acest nume implicit, vă rugăm să introduceți un alt nume.", "maxUsersCountRole": "Numărul maxim de utilizatori în rol", "unlimitedAssignLeaveBlankInfo": "Pentru atribuiri nelimitate, lăsați câmpul gol.", "cvOwner": "Deținătorul unei imagini de ansamblu", "changePassword": "Schimbați parola", "passwordExpired": "<PERSON><PERSON><PERSON> a expirat, vă rugăm să o schimbați.", "passwordWillExpire": "Parola dvs. va expira în curând. Introduceți o nouă parolă.", "userParameters": "Parametrii utilizator", "filterSortingHelper": "Sortarea după una sau mai multe coloane într-un filtru dezactivează capacitatea de a sorta manual coloanele direct în tabel.", "importUsers": "Importați utilizatori", "importRoles": "<PERSON><PERSON><PERSON> roluri", "existingEntityRows": "Rânduri cu entități deja existente (pot fi suprascrise)", "fileRow": "<PERSON><PERSON><PERSON>", "existingEntityRowsMultiple": "Rânduri cu entități care există deja de mai multe ori (nu vor fi importate)", "importOrgUnits": "Importați unități organizaționale", "structureImportExport": "Import/export structura", "fillAttributes": "Atribute de umplere", "structurePreview": "Previzualizarea structurii", "invalidRowsForImport": "<PERSON><PERSON><PERSON><PERSON> neval<PERSON> (lipsesc date obligatorii)", "duplicateRowsForImport": "Rânduri cu date de potrivire duplicat (nu vor fi importate)", "newEntityRows": "Rânduri cu entități noi de importat", "existingNameRowsForImport": "Rânduri cu nume care există deja pe alte entități (nu vor fi importate)", "overwriteExisting": "Suprascrie existente", "structurePreviewHelper": "Previzualizarea structurii arată două situații diferite: importarea numai a organizațiilor noi sau importarea atât a organizațiilor noi, cât și a celor existente, care vor fi suprascrise. Toate modificările în comparație cu structura actuală sunt marcate cu roșu.", "validateAndShowPreview": "Validați și previzualizați", "uploadNewFile": "Încărcați un fișier nou", "userStatus": "Starea utilizatorului", "importedFile": "Fișier importat", "pairUsersBy": "Asociați utilizatorii după", "assignOrgBy": "Atribuiți organizației de către", "pairRolesBy": "<PERSON><PERSON><PERSON> rol<PERSON>", "pairUnitsBy": "Perechea unități după", "unitHierarchyCol": "Coloana ierarhiei unităților", "dontAssign": "<PERSON><PERSON> atri<PERSON>", "infoImportDataValidated": "AVERTISMENT: <PERSON>le tocmai au fost validate din cauza modificărilor în setări. Vă recomandăm să reveniți și să verificați noua previzualizare de import.", "assignUserRolesMethod": "Cum se atribuie roluri utilizatorilor", "assignUserRolesMethodHelp": "Cum să atribuiți roluri: adăugați roluri la rolurile deja atribuite sau înlocuiți complet rolurile atribuite în prezent cu roluri nou atribuite.", "existingRolesForImport": "<PERSON><PERSON><PERSON> existente (pot fi suprascrise)", "existingRoleNamesForImport": "Roluri cu nume care există deja pentru alte roluri (nu vor fi importate)", "newRolesForImport": "Roluri noi de importat", "userRolesForImport": "Rânduri cu roluri de utilizator de atribuit", "nonExistentUsersForImport": "Rânduri cu utilizatori inexistenți (rolurile nu vor fi atribuite)", "multipleExistingUsersForImport": "Rânduri cu mai mult de un utilizator existent (rolurile nu vor fi atribuite)", "invalidOrgsForImport": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON> (lipsesc date obligatorii sau ierarhie greșită)", "keepOriginal": "Păstrați originalul", "assignOrgByHelp": "Dacă selectați o coloană din fișier, puteți specifica clasificarea organizației pentru utilizatorii noi și existenți. Dacă selectați o anumită organizație, toți utilizatorii importați sau actualizați vor fi alocați acestei organizații.", "creatingRoles": "<PERSON><PERSON><PERSON>", "assigningRolesToUsers": "Atribuirea de roluri utilizatorilor", "newUsers": "Utilizatori noi", "existingUsers": "Utilizatori existenți", "fromFile": "<PERSON>", "alrCsvXlsxUploadWrongExtension": "Încărcați numai fișiere cu extensia *.csv sau *.xlsx", "importNewAndExisting": "Importați entități noi și suprascrieți pe cele existente", "importNewOnly": "Importați numai entități noi", "importNewAndExistingRoles": "Importați roluri noi și suprascrieți rolurile existente", "importNewRolesOnly": "Importați numai roluri noi", "statisticsColorHelper": "Dacă culorile nu sunt selectate manual sau dacă există mai puține culori selectate decât coloane, culorile lipsă sunt generate automat. Culorile generate nu conțin niciodată nuanțe întunecate sau prea deschise, acestea pot fi selectate doar manual.", "importRolesHelper": "Setări pentru importarea rolurilor în sine. Atribuirea rolurilor utilizatorilor este guvernată de ceea ce este setat în \"Asociați utilizatorii după\" și se aplică întotdeauna rolurilor noi și existente.", "caseService": "Servic<PERSON>l de caz", "taskService": "Servic<PERSON><PERSON> de sarcini", "editTasks": "Edita<PERSON><PERSON>rc<PERSON>", "editCases": "Editați ca<PERSON>", "deleteTasks": "Ștergeți sarcini", "deleteCases": "Ștergeți cazurile", "serviceOperationsInfo": "Marcați și completați variabilele pe care doriți să le modificați.", "erased": "<PERSON><PERSON>", "statusErrored": "Eroare", "serviceOperations": "Operațiuni de service", "runCalcsOnStart": "Executați calcule la început", "taskReactivation": "Reactivarea sarcinilor", "taskCompletion": "<PERSON><PERSON><PERSON> sarc<PERSON>", "caseReactivation": "Reactivarea cazurilor", "caseCompletion": "<PERSON><PERSON><PERSON> cazu<PERSON>", "openTask": "Deschideți sarcina", "changeEntity": "Schimbați entitatea", "selectTableColumns": "Selectați coloanele tabelului", "parentCase": "Cazul părinte", "ownerOrganization": "Organizație proprietară", "confirmTaskReactivation": "Sunteți sigur că doriți să reactivați sarcinile selectate?", "confirmCaseReactivation": "Sunteți sigur că doriți să reactivați cazurile selectate?", "confirmTaskCompletion": "Sunteți sigur că doriți să finalizați sarcinile selectate?", "confirmCaseCompletion": "Sunteți sigur că doriți să finalizați cazurile selectate?", "selectAllFilterMustBeActive": "Cel puțin un filtru trebuie să fie activ pentru a selecta toate elementele.", "changeEntities": "Schimbați entitățile", "disabledDifferentTemplates": "Nu poate fi schimbat deoarece entitățile nu sunt din același șablon.", "actions": "Acțiuni", "taskTemplateId": "ID șablon sarcină", "caseTemplateId": "ID șablon caz", "actionInfoCheckLogs": "Acțiunea va fi efectuată în fundal, vă rugăm să verificați jurnalele.", "alrServiceOperationsColumnsFailed": "<PERSON><PERSON><PERSON> setării coloanelor operațiunilor de serviciu a eșuat.", "confirmResetSelectedCols": "Sunteți sigur că doriți să resetați coloanele salvate din tabel?", "instanceVars": "Variabile de instanță", "usrId": "ID utilizator", "orgId": "ID organizație", "titlePrefix": "Prefixul titlului", "titleSuffix": "Sufixul titlului", "accessRoleId": "ID-ul rolului de acces", "maxAssigns": "Tesiuni maxime", "client": "Client", "bigValue": "Valoare mare", "unitId": "ID unitate", "roleId": "ID rol", "paramId": "ID parametru", "varId": "ID variabilă", "parentId": "ID-ul părintelui", "openUser": "Deschide utilizatorul", "openRole": "<PERSON><PERSON>", "openUnit": "Unitate deschisă", "units": "Unități", "managerId": "ID manager", "externalStatus": "Starea externă", "additionalId": "ID suplimentar", "parentIc": "IC al părintelui", "companyIc": "IC al companiei", "textValue": "Valoare text", "dateValue": "Valoarea datei", "numberValue": "Valoare numerică", "loginCount": "<PERSON><PERSON><PERSON><PERSON>", "externalLogin": "Autentificare externă", "badLoginCount": "Număr de autentificare greșit", "passwordLastChange": "Ultima modificare a parolei", "solverEvaluation": "Evaluarea rezolutorului", "solverWillBe": "Solverul va fi", "possibleSolvers": "Rezolvatori posibili", "selectReferencePerson": "Selectați o persoană de referință", "evaluateSolver": "Evaluați soluția", "referenceUserForEval": "Persoană de referință pentru evaluare", "andOthers": "...și alții", "showLess": "...arata mai putin", "alrSessionExpired": "Sesiunea dvs. a expirat, vă rugăm să vă conectați din nou.", "mailPromptlyInfo": "Utilizatorul primește continuu notificări unice despre noi sarcini, unde sunt soluționatorul. Aceste alerte vor fi trimise numai dacă sarcina nu a fost rezolvată timp de {{minutes}} minute de la activarea ei.", "mailPullInfo": "Utilizatorul primește continuu notificări unice despre noile sarcini disponibile pentru abonament, iar utilizatorul este posibilul lor soluționare. Notificarea iese în momentul activării sarcinii date în cadrul fluxului de lucru.", "mailTotalInfo": "Utilizatorul primește periodic o imagine de ansamblu cu sarcini care urmează să fie finalizate, din care sunt soluționatorul. Dacă sarcina nu are un rezolvator direct, proprietarul procesului este notificat. Dacă utilizatorul este reprezentat, notificarea este primită de reprezentantul său.", "mailEscalationInfo": "Utilizatorul primește periodic o imagine de ansamblu cu sarcini care trebuie finalizate care au depășit termenul limită. Aceștia sunt anunțați dacă sunt supraveghetorul sarcinii (și nu rezolvatorul acesteia în același timp) sau sunt managerul direct al utilizatorului care este solverul. Dacă sarcina nu are rezolvator, proprietarul procesului este considerat supraveghetorul. Dacă utilizatorul este reprezentat, notificarea menționează cine este reprezentantul actual.", "calcSourceOverwriteWarning": "Du<PERSON><PERSON> salvare, sursa este suprascrisă cu sintaxa ES6!", "changeStatus": "Schimbați starea", "confirmChangeEmailStatus": "Chiar doriți să schimbați starea e-mailurilor selectate?", "logInAgain": "Logați-vă din nou", "migrations": "Mi<PERSON><PERSON>ii", "launchDate": "Data lansării", "stepName": "Numele pasului", "runId": "Rulați ID", "clone": "Clona", "confirmDeleteCron": "Chiar doriți să ștergeți cronul selectat?", "alrCronDeleted": "Cron a fost șters!", "wantToContinueQ": "Do<PERSON>ți să continuați?", "valueCannotBeEntered": "Valoarea nu poate fi introdusă", "processingQueues": "Cozi de procesare", "pause": "Suspenda", "fillOptionsFromVarHelper": "Opțiunile de filtrare pot fi completate numai din variabile de tip DT, DL, LT, LD, LN și D, care nu permit selecția multiplă.", "defaultTemplateName": "Numele implicit al șablonului", "defaultTaskName": "Numele implicit al sarcinii", "defaultVariableName": "Numele implicit al variabilei", "variableName": "N<PERSON>le variabilei", "alrNoDataFound": "Nu s-au găsit date", "ttProcessingQueuesInfo": "Cozile de procesare sunt dezactivate.\nPentru a le activa, setați cel puțin una dintre configurațiile \"scaling.queue.*.enabled\" la true.", "businessUsers": "Utilizatori de afaceri", "completeHrAgenda": "Agendă HR completă", "usageStatsByHeader": "Statistici de utilizare după antet", "usageStatsByOrgUnit": "Statistici de utilizare după unitatea org.", "usageStatsByUser": "Statistici de utilizare în funcție de utilizator", "completedTasksNum": "Numărul de sarcini finalizate", "startedProcessesNum": "Num<PERSON><PERSON><PERSON> de cazu<PERSON>", "ideHelp": "Apăsați Ctrl + Spațiu în editor pentru a vedea sugestii, apăsați din nou pentru ajutor mai detaliat. Apăsați F1 pentru a vedea toate comenzile disponibile și comenzile rapide de la tastatură. Consultați <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>documentația editorului</a> pentru mai multe.", "restHelp": "Introduceți adresa URL pentru unul dintre serviciile de tabel TAS (de ex. „/tasks/mine”) și după încărcarea serviciului selectați coloanele din tabel pe care doriți să le afișați în container.", "defaultGraphName": "Numele implicit al graficului", "graphName": "Numele graficului", "ttStatistics": {"heading": "Statistici", "body": ""}, "defaultAxisXName": "Numele implicit al axei X", "defaultAxisYName": "Numele implicit al axei Y", "defaultFilterName": "Numele implicit al filtrului", "filterName": "<PERSON><PERSON>le filtrului", "defaultOptionName": "Numele implicit al opțiunii", "optionName": "Numele opțiunii", "defaultOverviewName": "Nume de prezentare generală implicit", "overviewName": "Nume general", "eventName": "Numele evenimentului", "wantToOverrideEs6": "Dacă doriți cu adevărat să rescrieți, scrieți <b>ES6</b>", "processArchivation": "arhivarea procesului", "processUnarchivation": "<PERSON>zarhivarea procesului", "resendEmail": "Retrimite e-mail", "alrFailedSendEmail": "Trimiterea e-mailului a eșuat", "ttResendEmail": {"heading": "Retrimite e-mail", "body": "Retrimite o notificare prin e-mail trimisă anterior. Destinatarii pot fi modificați sau adăugați."}, "addCurrentScreenToFavourite": "Adăugați ecranul curent la preferatele dvs.", "attachmentAdd": "Adăugați un document", "createNewCase": "<PERSON><PERSON><PERSON> unui caz nou", "moreLanguage": "Alte variante ale limbilor", "notesAdd": "Adaugă o notiță", "notesNew": "Notă nouă", "removeCurrentScreenFromFavourite": "Scoateți ecranul curent din favorit", "setDashboard": "Editați tabloul de bord", "chooseFromCases": "Selectați din cazuri", "folders": "<PERSON><PERSON><PERSON>", "newFolderBtn": "<PERSON><PERSON> nou", "documentInfo": "Informații despre document", "userInfo": "Informații despre utilizator", "deleteImage": "<PERSON><PERSON><PERSON>a", "profilePhoto": "Fotografie de profil", "profilePhotoCaption": "Utilizați o fotografie în format jpeg, jpg, png sau gif.", "updatePhoto": "Actualizează fotografia", "mailNotifications": "Notificări prin e-mail", "userPreferences": "Preferințele utilizatorului", "userSettings": "<PERSON><PERSON><PERSON> utilizator", "allVices": "Toți substituții", "createVice": "Crearea un substitut", "editVice": "Editați substitutul", "viceTip": "Substituire vă permite să transmiteți agenda unui coleg", "emptyDataMessage": "Nu mai este nimic", "addFirstNote": "Adăugați prima notă", "noResultsFor": "<PERSON><PERSON><PERSON> rezultat pentru:", "noCurrentTasks": "<PERSON><PERSON><PERSON><PERSON> sarcini curente", "checkYourSearch": "Verificați căutarea și încercați din nou.", "noFavOverviews": "Fără prezentare generală preferată", "favOverviewsTip": "Adăugați o prezentare generală la favorite cu o stea", "noHiddenOverviews": "Nu aveți nicio prezentare generală ascunsă", "addOverview": "Adăugați o prezentare generală", "hidden": "Ascuns", "removeConfirm": "Eliminați", "removeItem": "Sunteți sigur că doriți să eliminați {{variable}}?", "changePicture": "Schimbați imaginea", "saveFilter": "Salvați filtrul", "addAnotherVice": "Adăugați un alt substitut", "saveVice": "Salvați substitutul", "firstLastName": "Prenumele și numele de familie", "taskInfo": "Informații despre sarcină", "emptyFavsTip": "Ad<PERSON><PERSON>ți favorite cu butonul", "saveAndClose": "Salvați și închideți", "usersCanEditOverview": "Utilizatorii pot edita prezentarea generală", "assignedUsers": "Utilizatori alocați", "assignedOrgUnits": "Unități organizaționale atribuite", "assignedRoles": "<PERSON><PERSON><PERSON> atribuite", "otherLangVariants": "Alte variante de limbă", "moveToSharing": "Mutați la Partajare", "insertDocumentsPerm": "Utilizatorul are permisiunea de a introduce documente și note", "saveNewPassword": "Salvați noua parolă", "confirmSubscription": "Confirmați abonamentul", "subscriptionCaption": "Privirea selectată vă va fi trimisă prin e-mail la ora setată.", "by": "<PERSON><PERSON>", "frequency": "Frecvența", "termination": "<PERSON><PERSON><PERSON><PERSON>", "ofTheMonth": "În luna", "endOnDate": "Încheiere la data de", "endAfter": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "onlyOnWorkingDays": "<PERSON>ar în zilele luc<PERSON>", "occurrences": "apariții", "dayOfWeekBy": "Zi a săptămânii", "calendarDayBy": "Zi a calendarului", "dateBy": "dat<PERSON>", "byDate": "<PERSON><PERSON><PERSON> da<PERSON>", "byOccurrenceCount": "După numărul de apariții", "infinitely": "Infinit", "dayOfMonthAdornment": "-a zi a lunii", "ordinalAdornment": ".", "toDateBeforeFromError": "Data 'Până' nu poate fi înainte de data 'De la'", "vice": "Substitut", "previewShown": "Previ<PERSON><PERSON><PERSON><PERSON>", "duplicate": "Dup<PERSON><PERSON><PERSON>", "hideBtn": "Ascunde", "userView": "Vizualizare utilizator", "adminView": "Vizualizare administrator", "or": "Sau", "overlappingVicesError": "Supleanții nu trebuie să se suprapună", "fileVar": "<PERSON><PERSON><PERSON>", "nodeVar": "nod", "uploadDifferentFile": "Încărcați un fișier diferit", "uploadedFile": "<PERSON><PERSON><PERSON>", "refreshPage2": "Actualizează pagina", "refreshPageCaption": "<PERSON>ă rugăm să reîmprospătați pagina în browser pentru a continua.", "ttCopy": {"heading": "Copiați", "body": "Vă permite să copiați elementul selectat cu posibilitatea de a edita unii parametri."}, "alrError_INVALID_CSV_MAPPING": "Nu a fost găsit coloana CSV „%s” în cartografierea evenimentului. Contactați administratorul de aplicație.", "documentPreview": "Previzualizare document", "moveUp": "Mutați -vă în sus", "moveDown": "Mutați -vă în jos", "moveToFilter": "Mutați la filtru", "moveToSorting": "Mutați la sortare", "addSorting": "Adăugați sortare", "cancelFilters": "Anulează filtrele", "docUploadedImmediately": "Documentul va fi încărcat imediat", "moreOptions": "Mai multe opțiuni", "docSearchPlaceholder": "De exemplu invoice.pdf...", "tasksSearchPlaceholder": "De exemplu, introduceți o nouă factură...", "docUploadedImmediatelyPrivate": "Documentul va fi încărcat imediat ca privat", "takeTsk": "<PERSON><PERSON> sarcina", "tasksActive": "Sarcini active", "subprocesses": "Subprocese", "cancelAuthorization": "Anulați autorizarea", "cancelAuthorizationConfirm": "Sunteți sigur că doriți să anulați autorizarea dispozitivului?", "linkMobileApp": "Conectați aplicația mobilă", "mobileApp": "Aplicație mobilă", "scanThisQr": "Scanați acest cod QR cu dispozitivul dvs. mobil.", "scanningQr": "Se scanează codul QR. Vă rugăm să așteptați.", "deviceName": "Numele dispozitivului", "newDeviceName": "Nume dispozitiv nou", "registrationDate": "Data de înregistrare", "lastLogin": "Ultima conectare", "mobileNotifications": "Notificări mobile", "disableMobileNotification": "Dezactivarea notificărilor pe mobil", "newQrCode": "Cod QR nou", "inactiveScanQr": "Inactiv - scanați codul QR.", "enableNotifications": "Activare notificări", "tip": "Sfat: {{message}}", "alrFavContainerAlreadyExists": "Containerul de favorite există deja.", "addGraph": "Adăugați graficul", "newRow": "<PERSON><PERSON><PERSON> nou", "confirmSetDefaultDashboard": "Chiar doriți să setați tabloul de bord curent ca implicit pentru toți utilizatorii?", "changeMayAffectAllUsers": "Această modificare poate afecta toți utilizatorii.", "noOverviewsTip": "Creați o nouă prezentare generală folosind butonul \"Adăugați o prezentare generală\"", "removeFromHidden": "Eliminați din ascuns", "last7Days": "Ultimele 7 zile", "last14Days": "Ultimele 14 zile", "last30Days": "Ultimele 30 zile", "lastCalendarMonth": "Ultima lună calendaristică", "lastQuarter": "Ultimul trimestru", "last12Months": "Ultimele 12 luni", "lastCalendarYear": "<PERSON><PERSON><PERSON><PERSON> an calendaristic", "noFilterSet": "Niciun filtru setat", "noSortingSet": "<PERSON><PERSON>un sortare setată", "deleteGroup": "Șterge grupul", "newGroup": "Grup nou", "operators": "Operatori", "withActiveTask": "Cu sarcină activă", "withoutActiveTask": "Fără sarcină activă", "withNoTerm": "<PERSON><PERSON><PERSON><PERSON> termen", "withTerm": "<PERSON>u termen", "securityAndAuthentication": "Securitate și autentificare", "dataIntegrationAndManagement": "Integrarea și gestionarea datelor", "appManagementAndConfig": "Gestionarea și configurarea aplicației", "monitoringAndMaintenance": "Monitorizare și întreținere", "adminSearchPlaceholder": "De exemplu, Fișiere publice...", "authenticationAdminDescription": "Opțiuni de conectare a utilizatorului", "certificatesAdminDescription": "Certificate pentru TAS", "elasticsearchAdminDescription": "Integrare cu Elasticsearch", "xmlProcessImportAdminDescription": "Importați procese XML folosind intrarea cron XMLProcessImport.js", "structureImportExportAdminDescription": "Import/export al structurii organizaționale, utilizatorilor și rolurilor", "dmsAttributesAdminDescription": "Lista atributelor documentului în DMS", "dynTablesAdminDescription": "Stocarea datelor în tabele dinamice", "csvAdminDescription": "Manipularea fișierelor CSV în aplicație", "configurationAdminDescription": "Configurarea aplicației", "settingsAdminDescription": "Setări de identificare a companiei și alte sarcini administrative", "logsAdminDescription": "Gestionați și vizualizați jurnalele aplicațiilor", "migrationsAdminDescription": "Migrarea datelor și configurarea aplicației", "guidesAdminDescription": "Ajutor și ghiduri pentru utilizatori", "schemeAdminDescription": "<PERSON><PERSON><PERSON> de culori, logo-ul și alte elemente din aplicație", "sequencesAdminDescription": "Gestionați secvențele utilizate în șabloane", "serviceConsoleAdminDescription": "Comenzi de administrare a aplicației prin consola de servicii", "serviceOperationsAdminDescription": "Gestionarea complexă a operațiunilor de servicii", "scriptsAdminDescription": "Gestionați scripturile reutilizabile în diferite șabloane", "appStatusAdminDescription": "Informații despre starea curentă a aplicației", "usageStatsAdminDescription": "Vedeți statisticile de utilizare a aplicației", "maintenanceAdminDescription": "Setări de întreținere și executarea sarcinilor de întreținere", "scheduledTasksAdminDescription": "Gestionați toate sarcinile programate", "publicFilesAdminDescription": "Gestionați fișierele publice și documentația", "cronsAdminDescription": "Automatizarea sarcinilor obișnuite", "hrAgendaAdminDescription": "Gestionarea agendei utilizatorilor în HR", "emailsQueueAdminDescription": "Gestionarea cozii de e-mail și toate comunicările prin e-mail de la TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Adăugarea articolului la favorite nu a reușit", "alrRemoveFavItemFailed": "Eliminarea articolului din favorite nu a reușit", "alrAddHiddenItemFailed": "Nu s-a putut adăuga elementul ascuns", "alrRemoveHiddenItemFailed": "Eșuarea de a elimina elementul ascuns", "display": "Afișarea", "compact": "Compactă", "standard": "Standard", "comfortable": "Confortabilă", "exportTo": "Export c<PERSON>tre", "adminMenuTip": "Adăugați elementele dvs. în administrare la favorite. Făcând clic pe stea, elementul va fi afișat chiar aici.", "editorDocumentation": "Documentația editorului", "addSection": "Adăugați o secțiune", "insertSection": "Inserați secțiunea", "section": "Secțiune", "sections": "Secțiuni", "toTop": "La început", "toEnd": "La sfârșit", "alrSectionNotBeEmpty": "Secțiunea nu trebuie să fie goală", "confirmDeleteSection": "Chiar vrei să ștergi secțiunea?", "sectionVarsMoveAllTasks": "Variabilele din toate sarcinile vor fi mutate de la secțiunea eliminată la variabile fără secțiune.", "sectionVarsMove": "Variabilele vor fi mutate de la secțiunea eliminată la variabile fără secțiune.", "actionCannotUndone": "Această acțiune nu poate fi anulată.", "overviewOfAllNews": "Prezentare generală a tuturor știrilor", "copyOverview": "Copiere prezentare generală", "create": "<PERSON><PERSON><PERSON><PERSON>", "copyExistingOverview": "Copiați prezentarea generală existentă", "selectOverview": "Selectați Prezentare generală", "chooseFromOverviews": "Alegeți din prezentări de ansamblu...", "selectTemplate": "Utilizați șablonul", "chooseFromAvailableTemplates": "Alegeți dintre șabloanele disponibile...", "loginWithUsernamePassword": "Autentificare cu numele de utilizator și parola", "signInWithCorporateIdentity": "Conectați-vă cu identitatea corporativă", "whatsNewInTAS": "Ce este nou în TAS?", "whatsNewInTASDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, fun<PERSON><PERSON><PERSON> noi, s<PERSON><PERSON><PERSON>, trucuri și tot ce trebuie să știți.", "justOpen": "<PERSON><PERSON><PERSON> numa<PERSON>", "editOverview": "Editați prezentarea generală", "noGraphsTip": "Creați un nou grafic folosind butonul \"Adăugare grafic\"", "noDocumentsTip": "Adăugați un document la sarcină sau folosind butonul \"Adăugare\"", "noFilesTip": "Adăugați un fișier nou folosind butonul \"Adăugare\"", "less": "<PERSON>", "notContains": "<PERSON>u contine", "factorySettings": "<PERSON><PERSON><PERSON> din fabrică", "previewCollapsedNavMenu": "Previzualizarea meniului de navigare la pachet", "previewExpandedNavMenu": "Previzualizarea meniului de navigare nepachetat", "logoForCollapsedNavMenu": "Logo pentru meniul de navigare la pachet", "logoForExpandedNavMenu": "Logo -ul pentru meniul de navigare nepachetat", "organisationLogo": "Logo -ul organizației", "pickLogoOrganisation": "Selectarea logo -ului pentru organizare", "addLogo": "Adăugați logo -ul", "clickForAddLogoOrDrop": "Faceți clic pentru a adăuga logo -ul sau aruncarea fișierului aici", "useLogoSizeMin": "Utilizați un logo de dimensiuni minime", "logoForLightTheme": "Logo pentru modul Bright", "logoForDarkTheme": "Logo pentru modul întunecat", "notEquals": "Nu este egal", "sharedWithMe": "Partajat cu mine", "myOverview": "Prezentarea mea de ansamblu", "getMobileAppText": "Obțineți aplicația mobilă din magazinul de aplicații", "noDocuments": "Fără documente", "noNotes": "Făr<PERSON> note", "noFiles": "<PERSON><PERSON><PERSON><PERSON>", "addFirstDocument": "Adăugați primul document", "killed": "<PERSON><PERSON>", "chooseNewLogo": "Selectați un nou logo", "function": "<PERSON><PERSON><PERSON><PERSON>", "groupFunction": "Funcție între grup<PERSON>", "mobileAppAuthFailed": "Autentificarea aplicației mobile nu a reușit.", "currentDocumentVersion": "Versiunea cure<PERSON> a documentului", "csp": "Politica de securitate a conținutului", "documentsDelete": "Șterge documentele", "confirmDocumentsDelete": "Sunteți sigur că doriți să ștergeți documentele selectate?", "confirmDocumentsDownload": "Doriți să descărcați documentele selectate?", "firstNum": "primele {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Descărcați documentele", "caseLogs": "Jurnale de caz", "archiveCases": "Arhivează cazurile", "archive": "Arhivează", "unarchive": "Dezarhivează", "confirmArchiveCases": "Doriți cu adevărat să arhivați cazurile selectate?", "archiveInDays": "Arhivează în (zile)", "archived": "<PERSON><PERSON><PERSON><PERSON>", "archivedx": "<PERSON><PERSON><PERSON><PERSON>", "alrArchivingCase": "Cazul se arhivează...", "alrCaseArchived": "Cazul a fost arhivat.", "alrLackOfPermsToArchiveCase": "Nu aveți permisiunile necesare pentru a arhiva cazul.", "alrArchiveCaseFailed": "<PERSON><PERSON><PERSON><PERSON> ca<PERSON> a eșuat.", "alrUnarchivingCase": "Cazul se dezarhivează...", "alrCaseUnarchived": "Cazul a fost dezarhivat.", "alrLackOfPermsToUnarchiveCase": "Nu aveți permisiunile necesare pentru a dezarhiva cazul.", "alrUnarchiveCaseFailed": "Dezarhivarea cazului a eșuat.", "byUser": "După utilizator", "byAgenda": "<PERSON><PERSON><PERSON>", "agendaHandover": "Predar<PERSON> age<PERSON>i", "activeUsers": "Utilizatori activi", "lockedUsers": "Utilizatori blocați", "allUsers": "Toți utilizatorii", "inactiveUsers": "Utilizatori inactivi", "hrAgendaSearchPlaceholder": "De exemplu Ion <PERSON> ...", "completeAgendaHandover": "Predarea completă a agendei", "handoverCases": "<PERSON><PERSON><PERSON>", "handoverTasks": "Sarcini de transfer", "handoverVars": "Variabile de transfer", "changeTaskOwner": "Schimbați soluția de sarcini", "confirmHandover": "Confirma<PERSON><PERSON> predar<PERSON>", "filterCasesByHeaderTip": "Puteți filtra toate cazurile sub același antet în coloana Antet.", "userAgendaSelectedHandover": "Predarea agendei <b style=\"color: {{color}};\">selectate</b> a utilizatorului", "userAgendaCompleteHandover": "Predarea agendei <b style=\"color: {{color}};\">complete</b> a utilizatorului", "confirmAgendaHandover": "Sunteți sigur că doriți să transferați agenda selectată ({{selected}}) utilizatorului <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Sunteți sigur că doriți să transferați agenda <b>selectată</b> a utilizatorului <b>{{user}}</b> utilizatorului <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Sunteți sigur că doriți să transferați agenda <b>completă</b> a utilizatorului <b>{{user}}</b> utilizatorului <b>{{newUser}}</b>?", "refreshSessionTitle": "Sesiunea TAS va fi încheiată în {{minutes}} de minute.", "refreshSessionCaption": "<PERSON><PERSON><PERSON> clic pe \"Continuați lucrul\" pentru a continua fără întreruperi.", "continueWorking": "Continuați lucrul", "sessionExpiredCaption": "<PERSON><PERSON><PERSON> clic pe \"Logați-vă din nou\" a reveni la ecranul de conectare.", "loginExpired": "V-am deconectat după o perioadă lungă de inactivitate.", "confirmArchiveCase": "Doriți cu adevărat să arhivați cazul selectat?", "isLowerOrEqualThan": "Trebuie să fie mai mic sau egal cu", "confirmUnarchiveCase": "Sunteți sigur că doriți să dezarhivați cazul selectat?", "addCaseRightNewUserTooltip": "Dacă nu bifați această opțiune, noul utilizator va fi înlocuit în variabila business, dar nu va avea acces la caz.", "canBeViced": "Sunt în<PERSON>", "canVice": "<PERSON><PERSON> în<PERSON>ui<PERSON>", "backgroundColor": "Culoare de fundal", "defaultDashboardView": "Previzualizarea tabloului de bord implicit", "colorScheme": "Schema de culori", "displaySelectionAsTags": "Afișează selecția ca taguri", "displayAsPassword": "Afișează ca parolă", "sideBySide": "Unul lângă altul", "copyAssignmentFromTask": "Copiați alocarea din sarcină", "toTask": "La sarcină", "copyTaskAssignmentWarning": "Alocarea din sarcină nu este goală, dori<PERSON>i să o suprascrieți?", "copyToOtherTasks": "Copiază în alte sarcini", "noteScriptsNotApplied": "Notă: Sc<PERSON>turile nu sunt aplicate", "generateRecHistory": "Afișați sarcinile active și istoricul", "leaveFormerRoles": "Păstrează rolurile anterioare", "includeCompetences": "Include competențele", "copyRoles": "Copiaz<PERSON> rolurile", "userIsActive": "Utilizatorul este activ", "systemUser": "Utilizator de sistem", "copyRolesFromUser": "Copiază rolurile de la utilizator", "assignedRolesOverview": "Prezentare generală a rolurilor atribuite", "copyRolesInfo": "Dacă utilizatorul dat face parte din competențe, aceste competențe nu vor fi copiate imediat. Generarea va avea loc:", "notificationOn": "Fixat", "notificationOff": "<PERSON><PERSON><PERSON><PERSON>", "onNotification": "Notificare", "offNotification": "Notificare", "page": "pagina", "fromTo": "De la - la", "isAnyOfValue": "Este orice valoare din", "notcontains": "nu conține", "notequals": "inegal", "fromto": "de la - la", "isanyofvalue": "este orice valoare din", "alrNoteToggleVisibiltyFailed": "Ascunderea/afișarea notei a eș<PERSON>t", "alrNoteHideOnEditFailed": "Ascunderea notei originale a eșuat", "hiddenShe": "Ascunsă", "showHiddenNotes": "Afișează notele ascunse", "alrNoteEdited": "Versiunea editată a notei a fost salvată", "notesEdit": "Editează nota", "displayName": "Nume afișat", "clientDateFormat": "Formatul datei", "defaultByLanguage": "Implicit după limbă", "restKeysOptionsNotUpToDate": "Selecție de valori învechită - reîncărcați serviciul.", "invalidValue": "Valoare nevalidă", "ended": "Încheiat", "exportAllActive": "Exportați toate active", "alrScriptsLoadFailed": "Încărcarea scripturilor a eșuat.", "scriptsImport": "Import scripturi", "doImport": "Importa<PERSON>i", "alrImportingScripts": "Se importă scripturile...", "alrScriptsImported": "Scripturile au fost importate.", "alrScriptsImportFailed": "Importul scripturilor a eșuat.", "removeAll": "Eliminați tot", "alrNoScriptsToImport": "Nu există scripturi de importat.", "activateAll": "Activează tot", "alrNoPermsToEditNoteInVice": "Nu aveți permisiuni de editare a notei în calitate de deputat.", "alrNoPermsToToggleNoteVisibilityInVice": "Nu aveți permisiunea de a ascunde/afișa nota în calitate de deputat.", "plusMore": "mai mult", "variableAlignment": "Alinier<PERSON> variabilei", "variableAlignmentHelp": "Afectează alinierea valorii variabilei în cadrul formularului sarcinii.", "variableAlignmentLeft": "Stânga", "variableAlignmentRight": "<PERSON><PERSON><PERSON>", "tasksMineAndToPull": "Ale mele + De preluat", "myDevice": "Dispozitivul meu", "deleteLogo": "<PERSON><PERSON><PERSON> sigla", "namingFilter": "<PERSON><PERSON>le filtrului", "exceptionsToRegularSchedule": "Excepții de la programul regulat", "noExceptions": "Fără excepții", "specificDates": "Date specifice", "dateFromTo": "Data de la - la", "weekdayCap": "<PERSON><PERSON>a <PERSON>", "specificDayBy": "<PERSON>i specific<PERSON>", "yearsBy": "ani", "timed": "Programat", "firstDayOfMonth": "Prima zi a lunii", "lastDayOfMonth": "Ultima zi a lunii", "firstDayOfYear": "Prima zi a anului", "lastDayOfYear": "Ultima zi a anului", "addDate": "Adăugați o dată", "newPlan": "Nou plan", "addAnother": "Adăugați un alt", "startTime": "Ora de început", "endTime": "<PERSON><PERSON>â<PERSON>", "inTimeFromTo": "în intervalul de la {{from}} la {{to}}", "dayOfMonthBy": "Ziua din lună", "cWorkDays": "zile luc<PERSON>", "cWeeks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cMonths": "luni", "cYears": "ani", "everyWeek": "în fiecare s<PERSON>ân<PERSON>", "everyYear": "în fiecare an", "inMonth": "în luna", "everyDay": "în fiecare zi", "seqIdEdit": "Editați ID-ul secvenței", "allowMultiselectSearchRight": "Permiteți căutarea în alocare", "doubleHeightForContent": "Înălțime dublă pentru conținut", "alrNoVariablesMappingToImport": "Nu există mapări de variabile pentru import.", "alrVariablesMappingImportLoadFailed": "Încărcarea mapărilor de variabile pentru import a eșuat.", "variablesMappingImport": "Import mapări variabile", "useAllMappings": "<PERSON><PERSON><PERSON><PERSON><PERSON> toate map<PERSON>rile", "doExportVariablesMapping": "Exportați mapările variabilelor", "alrImportingVariablesMapping": "Se importă mapările variabilelor...", "alrVariablesMappingImported": "Mapările variabilelor au fost importate.", "alrVariablesMappingImportFailed": "Importul mapărilor variabilelor a eșuat.", "alrVariablesMappingImportedPartially": "Mapările variabilelor au fost importate doar parțial. Unele variabile nu au fost găsite.", "alrEditorHintsLoadFailed": "Nu s-au încărcat sugestiile de calcul.", "addTable": "<PERSON><PERSON><PERSON> tabel", "confirmDynTablesDelete": "<PERSON><PERSON><PERSON> <PERSON><PERSON>ți să ștergeți tabelele dinamice selectate?", "dynTablesDelete": "Șterge tabelele dinamice", "addRow": "<PERSON><PERSON><PERSON> rând", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnDelete": "<PERSON><PERSON><PERSON> coloana", "editRow": "Editează rândul", "addingNewColumn": "<PERSON><PERSON><PERSON><PERSON> coloană nouă", "addingNewRow": "<PERSON><PERSON><PERSON><PERSON> rând nou", "columnsRename": "Redenumește coloanele", "rowCellValues": "Valorile celulelor din rând", "saveDynTableName": "Salvează numele tabelului dinamic", "saveDynTableNameQ": "Salvează numele tabelului dinamic?", "saveDynTableNameWarning": "Atenție, asigurați-vă că schimbarea numelui tabelului nu va afecta calculele existente în șabloane.", "rowMove": "<PERSON><PERSON><PERSON>", "alrCsvParsingErr": "Eroare la analizarea CSV!", "addFirstTableColumn": "Adăugați prima coloană a tabelului", "my": "<PERSON><PERSON>", "license": "Licență", "licenses": "Licențe", "addLicense": "Adăugați licență", "licenseResult": "Rezultatul licenței", "alrLicenceResultLoadingFailed": "Nu s-a încărcat rezultatul licenței.", "licensesAdminDescription": "Gestionarea licențelor", "uploadByDragging": "Încărcați fișierul prin glisare.", "uploadByDraggingAnywhere": "Încărcați un fișier trăgând oriunde în spațiu.", "assignVariable": "Atribuiți variabila", "confirmDeleteSectionName": "Sunteți sigur că doriți să ștergeți secțiunea <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Avertisment: secțiunea va fi ștearsă pentru toate sarcinile afectate, inclusiv variabile.", "tasksAffected": "Sarcini afectate", "varSearchPlaceholder": "De exemplu, facturarea...", "enlarge": "M<PERSON>reș<PERSON>", "show": "Arată", "shrink": "Micșorează", "hide": "Ascunde", "doValidate": "Validează", "phoneNumber": "<PERSON><PERSON><PERSON><PERSON> telefon", "textLength": "Lungimea textului", "when": "când", "to2": "la", "that": "că", "dynCondBuilderBlockFunctionDescShow": "Afișează variabila dacă condiția este îndeplinită.", "dynCondBuilderBlockFunctionDescHide": "Ascunde variabila dacă condiția este îndeplinită.", "dynCondBuilderBlockFunctionDescChange": "Schimbă valoarea variabilei dacă condiția este îndeplinită.", "dynCondBuilderBlockFunctionDescValidate": "Validează valoarea variabilei.", "addCondition": "Adaugă condiție", "operator": "operator", "equals": "egal cu", "greaterthan": "mai mare dec<PERSON>t", "greaterorequal": "mai mare sau egal cu", "lessthan": "mai mic decât", "lessorequal": "mai mic sau egal cu", "demoCode": "Cod demo", "code": "Cod", "confirmDeleteConditions": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>i să ștergeți toate condițiile (inclusiv scriptul)?", "validationErrorMessage": "<PERSON>j de eroare la validare", "alrScriptToStructuredBlockConversionFailed": "Conversia scriptului în bloc structurat a eșuat.", "alrStructuredBlockToScriptConversionFailed": "Conversia blocului structurat în script a eșuat.", "alrScriptToBuilderConversionFailed": "Conversia scriptului în builder a eșuat.", "alrBuilderToScriptConversionFailed": "Conversia din builder în script a eșuat.", "dynCondBuilderBlockFunctionDescScript": "Bloc de script pentru condiții dinamice.", "convertToStructuredBlock": "Convertiți în bloc structurat", "convertToScript": "Convertiți în script", "dynCondBuilderBlockWatchVarsLabel": "Porniți la schimbare (watchVars)", "variables": "Variabile", "copyToOthers": "Copiați către alții", "sectionName": "Numele secțiunii", "newSectionName": "Numele noii secțiuni", "testIt": "Test", "addAdjacentSection": "Adăugați o secțiune vecină", "addAdjacentSectionBelow": "Adăugați secțiunea vecină de mai jos", "selectExistingSection": "Alegeți o secțiune existentă", "renameSectionWarning": "Atenție: Secțiunea va fi redenumită în toate sarcinile șablonului.", "warning2": "Atenție", "copyAssignmentToTask": "Copiați misiunea la sarcină", "copyAlsoConditions": "Copie și condiții", "copyAssignmentToTaskWarning": "Atenție: atribu<PERSON><PERSON> <PERSON>i, eventual, condițiile dinamice în sarcina selectată vor fi rescrise.", "importFromOtherTask": "Importă dintr -o altă sarcină", "startFromScratch": "Începeți de la început", "howToStartAssignments": "Cum doriți să începeți să atribuiți variabile?", "selectTaskToImport": "Selectați sarcina pentru import", "confirm": "Confirma", "selectTaskToTest": "Pentru a selecta o sarcină de testare", "toTestSaveChanges": "Modificările trebuie stocate pentru testare.", "variableAssignmentTest": "Testarea alocării variabilelor", "viewAsMobile": "Vizualizați ca pe mobil", "viewAsPc": "Vizualizați ca pe PC", "emptySpace": "<PERSON><PERSON><PERSON> gol", "variableAssignments": "Atribuirea variabilelor", "allowCompletionOnChangeOf": "Permite finalizarea la schimbare", "dynCondBuilderBlockFunctionDescRead": "Schimbă modul variabilei în \"doar pentru citire\" dacă condiția este îndeplinită.", "dynCondBuilderBlockFunctionDescWrite": "Schimbă modul variabilei în \"pentru citire și scriere\" dacă condiția este îndeplinită.", "dynCondBuilderBlockFunctionDescMust": "Schimbă modul variabilei în \"obligatoriu\" dacă condiția este îndeplinită.", "dynCondBuilderBlockFunctionDescSolve": "Permite finalizarea sarcinii la schimbarea variabilei date, dacă condiția este îndeplinită.", "newsManagement": "Gestionarea știrilor", "newsManagementAdminDescription": "Gestionarea știrilor în aplicație", "addNewsPost": "Adăugați știre", "newPost": "Postare nouă", "news": "<PERSON><PERSON><PERSON>", "basicInfo": "Informații de bază", "publicationPlanning": "Planificarea publicării", "displayToUsers": "Afișare utilizatorilor", "displayLocation": "Locația afișării", "newsPostContent": "Conținutul știrii", "postTitle": "<PERSON><PERSON><PERSON><PERSON>", "newsManagementPostDetailPhoneNumberTooltip": "Num<PERSON>r de telefon pentru afișare în detaliile știrii", "newsManagementPostDetailEmailTooltip": "<PERSON>ail pentru afișare în detaliile știrii", "customUrlLink": "Link URL personalizat", "newsManagementPostDetailCustomUrlLinkTooltip": "Link URL personalizat pentru afișare în detaliile știrii", "stateAfterSaving": "Stare după salvare", "newsPostStateActive": "Activ", "newsPostStateInactive": "Inactiv", "newsPostStatePlanned": "Planificat", "endNewsPostOnSpecificDate": "Încheierea știrii la o anumită dată", "sendNewsPostViaEmail": "Trimiteți știrea prin e-mail", "priorityNewsPost": "Știre prioritară", "newsManagementPostDetailPriorityNewsTooltip": "De exemplu, pentru a anunța o întrerupere sau o schimbare a fluxului de lucru", "newsPostEndDate": "Data de încheiere a știrii", "pickNewsPostDisplayToOrgUnits": "Căror unități org. să se afișeze știrea?", "pickNewsPostDisplayToRoles": "<PERSON><PERSON><PERSON><PERSON> roluri să se afișeze știrea?", "pickNewsPostDisplayToUsers": "<PERSON><PERSON><PERSON>r utilizatori să se afișeze știrea?", "pickNewsPostDisplayOnTemplate": "Pe ce șablon să se afișeze știrea?", "pickNewsPostDisplayOnHeaders": "Pe ce anteturi să se afișeze știrea?", "pickNewsPostDisplayOnTasks": "Pe ce sarcini să se afișeze știrea?", "pickNewsPostDisplaySubOptionsHelperText": "Mai întâi selectați șablonul pe care doriți să afișați știrea.", "newsTagsManagement": "Gestionarea etichetelor de știri", "newsTagsManagementAdminDescription": "Gestionarea etichetelor de știri în aplicație", "addTag": "Adăugați etichetă", "tags": "Etichete", "publicationDate": "Data publicării", "contacts": "Contacte", "avaibleUntil": "Disponibil până la", "published": "Publicat", "newsSinceLastVisitAmount": "Total {{amount}} de știri de la ultima vizită", "noNews": "Nu există știri", "createNewTag": "Creați o etichetă nouă", "tagName": "<PERSON><PERSON>le et<PERSON>i", "alrTagSaved": "Eticheta a fost salvată.", "alrTagSaveFailed": "Salvarea etichetei a eșuat.", "confirmDeleteTag": "<PERSON><PERSON><PERSON> <PERSON>i să ștergeți eticheta \"{{tagName}}\"?", "alrPostSaved": "Postarea a fost salvată.", "alrPostSaveFailed": "Salvarea post<PERSON>i a eșuat.", "alrLoadingTagsFailed": "Încărcarea etichetelor a eșuat.", "confirmDeletePost": "<PERSON><PERSON><PERSON> <PERSON>i să ștergeți postarea \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "<PERSON><PERSON><PERSON> do<PERSON>ți să ștergeți postările selectate?", "post": "Postare", "alrPostLoadFailed": "Încărcarea postării a eșuat.", "alrTagDeleted": "Eticheta a fost ștearsă.", "alrTagDeleteFailed": "Ștergerea etichetei a eșuat.", "alrPostDeleted": "Postarea a fost ștearsă.", "alrPostDeleteFailed": "Ștergerea postării a eșuat.", "alrPostsDeleted": "Postările selectate au fost șterse.", "alrPostsDeleteFailed": "Ștergerea postărilor selectate a eșuat.", "alrTempTasksLoadFailed": "Încărcarea sarcinilor din șablon a eșuat.", "rolesRestriction": "Restricții pentru roluri", "usersRestriction": "Restricții pentru utilizatori", "orgUnitsRestriction": "Restricții pentru unități organizaționale", "alrPriorityNewsLoadFailed": "Încărcarea știrilor prioritare a eșuat.", "moreInfo": "Mai multe informații", "tas5Info": "TAS 5.0 este aici ...", "totalNewsAmount": "Total {{amount}} de <PERSON><PERSON><PERSON>", "alrNewsContainerPostsLoadFailed": "Încărcarea postărilor pentru containerul de știri a eșuat.", "alrTaskNewsLoadFailed": "Încărcarea știrilor pentru sarcină a eșuat.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "Data publicării trebuie să fie înainte de data de încheiere a știrii.", "alrNotificationsNewsLoadFailed": "Încărcarea știrilor pentru notificări a eșuat.", "moreNews": "<PERSON> multe <PERSON>", "newsManagementPostDetailConfirmSavingWillSendMail": "Salvarea postării va trimite un e-mail tuturor utilizatorilor cărora le este destinată postarea. Sigur doriți să salvați postarea?", "mailNewsNotification": "Notificare prin e-mail pentru ș<PERSON>ri", "mailNewsNotificationInfo": "Utilizatorul primește continuu știri care îi sunt destinate.", "alrRefreshingConfig": "Actualizarea configurării...", "alrConfigRefreshed": "Configurarea a fost actualizată cu succes.", "alrConfigRefreshFailed": "Actualizarea configurării a <PERSON>șuat.", "ttRefreshConfig": {"heading": "Restabiliți configurația din toate sursele", "body": ""}, "getMobileAppTextQr": "Obțineți aplicația mobilă din magazinul de aplicații sau scanați codul QR", "dateStart": "Data de începere", "dateEnd": "Data de încheiere", "tas_forms_generated": "Numărul de formulare generate automat"}