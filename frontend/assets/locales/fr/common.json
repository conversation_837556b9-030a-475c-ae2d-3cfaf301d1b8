{"1st": "1er", "2nd": "2ème", "3rd": "3ème", "4th": "4ème", "AddToAllTasks": "Ajouter à toutes les tâches", "OfVariable": "De la variable", "RemoveFromAllTasks": "Supprimer de toutes les tâches", "TaskOwnerWhichInVar": "Au solveur de la tâche, qui est défini dans la variable", "action": "Action", "active": "Actif", "activeShe": "Actif", "activePl": "Actif", "activity": "Activité", "activityType": "Type d'activité", "actualEnd": "<PERSON><PERSON> réelle de fin", "actualSolver": "Sol<PERSON>ur réel de la tâche", "actualStart": "Heure réelle de démarrage", "actualTsks": "Tâches en cours", "actualize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "Ajouter", "addAttribute": "Ajouter un attribut", "addOrgUnit": "Ajouter une unité org.", "addPlan": "Ajouter un forfait", "addPrintTemplate": "Ajouter un modèle d'impression", "addRole": "Ajouter un rôle", "addRule": "Ajouter une règle", "addTemp": "Ajouter un modèle", "addTsk": "Ajouter une tâche", "addUser": "Ajouter un utilisateur", "addUserSomething": "Attribuez un utilisateur à la {{variable}} sélectionnée :", "addVariable": "Ajouter une variable", "after": "<PERSON><PERSON>", "afterTermTasks": "Mes tâches expirées", "all": "<PERSON>ut", "allFiles": "To<PERSON> les fichiers", "allMustBeMet": "Tout doit être fait", "allMyTasks": "Toutes mes tâches", "allSubOfPlanGuar": "Tous les subordonnés du garant du régime", "allTasksWithNoTerm": "Toutes les tâches sans date d'échéance", "allTasksWithTerm": "Toutes les tâches avec date d'échéance", "allTemps": "Tous les modèles", "allowMultiple": "Autoriser le choix de plusieurs options", "allowSelectAll": "Autoriser sélectionner toutes les options", "allsupOfPlanGuar": "Tous les supérieurs du plan garant", "alrBlockingAction": "Il y a une action à terminer ! Patientez s'il-vous-plait...", "alrActionNameNotDefined": "L'action « {{actionName}} » n'est pas définie.", "alrActionNotDefined": "L'action n'a pas été définie.", "alrApiUrlMissing": "Source de données de table manquante.", "alrAssigningTsk": "Attribuer une tâche...", "alrAssignmentFailed": "L'attribution a échoué", "alrAtrFailed": "Échec de la suppression de l'attribut", "alrAttachDeleteFailed": "Échec de la suppression du document", "alrAttachDeleted": "Le document a été supprimé !", "alrAttachDeleting": "Suppression du document...", "alrAttachDownloadFailed": "Le téléchargement du document a échoué.", "alrAttachDownloaded": "Le document a été téléchargé !", "alrAttachDownloading": "Téléchargement du document...", "alrAttachMetaFailed": "Échec de l'enregistrement des métadonnées du document.", "alrAttachOrNotesCountFailed": "Le comptage des documents ou des notes a échoué.", "alrAttachSaveFailed": "Le téléchargement du document a échoué.", "alrAttachSaved": "Le document était joint.", "alrAttachTooBig": "Le document est trop gros ! Le document a dépassé {{maxUploadSize}} Mo.", "alrAttrDataFailed": "Échec du chargement des données d'attribut", "alrAttrFailed": "Échec du chargement des données d'attribut", "alrAttrSaveFailed": "Erreur lors de l'enregistrement de l'attribut !", "alrAttrsLoadFailed": "Échec du chargement des attributs", "alrAttachRestored": "Document restauré.", "alrAttachRestoreFailed": "La restauration du document a échoué.", "alrAttachRestoring": "Restauration...", "alrAuthMethodsFailed": "Le chargement des méthodes d'authentification a échoué.", "alrBadLogin": "Identifiant ou mot de passe incorrect.", "alrBlockedPopups": "Vos fenêtres pop-up sont probablement bloquées.", "alrCaseDataLoadFailed": "Le chargement des données de dossier a échoué.", "alrCaseDeleteFailed": "Échec de la suppression du dossier.", "alrCaseDeleted": "Le dossier a été supprimé !", "alrCaseDeleting": "Suppression du dossier...", "alrCaseNameLoadFailed": "Échec du chargement du nom du dossier.", "alrCaseNoRights": "Vous n'êtes pas autorisé à afficher le dossier nr. {{id}}.", "alrCaseNotFound": "dossier introuvable - il a peut-être été supprimé", "alrCaseOverviewFailed": "Le chargement des données du dossier pour la lecture (CASE OVERVIEW) a échoué.", "alrCaseSuspended": "Le dossier a été suspendu !", "alrCaseVarsLoadFailed": "Le chargement des variables de processus a échoué.", "alrCaseWakeUpFailed": "Le réveil du dossier a échoué.", "alrCaseWakedUp": "Le dossier a été réveillé !", "alrCaseWakingUp": "Dossier de réveil...", "alrColsWidthsSettingsFailed": "Échec de l'enregistrement du paramètre de largeur des colonnes.", "alrConnToServerFailed": "La connexion au serveur a échoué.", "alrConnectionDataLoadFailed": "Le chargement des données de connexion a échoué.", "alrConnectionDeleteFailed": "La suppression de la connexion a échoué.", "alrConnectionDeleted": "La connexion a été supprimée !", "alrConnectionSaveFailed": "Échec de l'enregistrement de la connexion !", "alrContainerNotFound": "Le conteneur n'a pas été trouvé.", "alrCsvDownloaded": "Le fichier CSV a été téléchargé !", "alrCvNotFound": "L'aperçu n'a pas été trouvé.", "alrDashboardSettingsFailed": "Échec de l'enregistrement des paramètres du tableau de bord.", "alrDefaultDashboardLoadFailed": "Le chargement du tableau de bord par défaut a échoué.", "alrDefaultDashboardSaved": "Le tableau de bord par défaut a été enregistré !", "alrDeleteFailed": "Une erreur s'est produite lors de la suppression.", "alrDeleted": "Supprimé !", "alrDeleting": "Suppression....", "alrDiagramDataLoadFailed": "Le chargement des données pour créer le diagramme a échoué.", "alrDiagramEditToSave": "Le diagramme ne peut pas être enregistré et transformé en modèle - il contient plus d'un processus ! Veuillez mettre à jour le diagramme afin qu'il ne contienne qu'un seul processus ou importer un autre fichier .bpmn.", "alrDiagramInitFailed": "L'initialisation du diagramme a échoué.", "alrDiagramMissingTaskName": "<PERSON><PERSON><PERSON>z remplir les noms de toutes les tâches.", "alrDiagramErrors": "Le schéma contient des erreurs. Veuillez les corriger et réessayer d'enregistrer.", "alrDiagramNotValid": "XML n'est pas valide selon la spécification officielle BPMN 2.0 !", "alrDiagramProcessCount": "Le diagramme ne peut pas être enregistré et transformé en modèle - il contient plus d'un processus !", "alrDiagramSaveFailed": "Une erreur s'est produite lors de l'enregistrement du modèle !", "alrDiagramTsksDeleteFailed": "Une erreur s'est produite lors de la suppression des tâches !", "alrDiagramUnchanged": "Le modèle reste inchangé.", "alrDmsColsLoadFailed": "Les colonnes pour DMS n'ont pas pu être chargées.", "alrDocsColumnsIdsFailed": "L'ID des colonnes du tableau des documents n'a pas pu être chargé.", "alrDocumentAdding": "Enregistrement du document...", "alrDynListsDataLoadFailed": "Échec du chargement des données des listes dynamiques.", "alrDynTableColsDataFailed": "Les données des colonnes de la table dynamique n'ont pas pu être chargées.", "alrDynTableDataLoadFailed": "Échec du chargement des données de la table dynamique.", "alrDynTableNotFound": "La table dynamique est introuvable.", "alrDynTablesDataLoadFailed": "Échec du chargement des données des tables dynamiques.", "alrCalcScriptsDataLoadFailed": "Le chargement des scripts de calcul global a échoué.", "alrEditValues": "<PERSON><PERSON><PERSON><PERSON> corriger les valeurs saisies par erreur.", "alrEventSaveFailed": "Échec de l'enregistrement de l'événement.", "alrEventSaved": "L'événement a été enregistré !", "alrEventSaving": "Enregistrement de l'événement...", "alrEventTriggered": "L'événement était lancé !", "alrExcelDownloaded": "Le fichier xlsx a été téléchargé !", "alrExportCompleted": "Exportation terminée.", "alrExportFailed": "L'exportation n'a pas réussi.", "alrExportPreparing": "Préparation de l'exportation...", "alrFailed": "Action: <PERSON><PERSON><PERSON>.", "alrFailedCalendarTask": "Le chargement de la tâche dans le calendrier a échoué.", "alrFailedCreatePrint": "La création de l'impression a échoué.", "alrFailedDLTotalCount": "Il n'y avait pas de total_count spécifié dans la liste dynamique {{label}}, donc tous les enregistrements ont été chargés.", "alrFailedData": "Le chargement des données a échoué.", "alrFailedEventStart": "L'événement n'a pas pu dé<PERSON>.", "alrFailedEventVariables": "Échec du chargement des variables de l'événement sélectionné.", "alrFailedEvents": "Le chargement des événements a échoué.", "alrFailedFoldersData": "Les données des dossiers n'ont pas pu être chargées.", "alrFailedFormData": "Les données du formulaire n'ont pas pu être chargées.", "alrFailedInitiatorName": "Le nom de l'initiateur n'a pas pu être chargé.", "alrFailedLabelData": "Échec du chargement des données du composant d'étiquette.", "alrFailedLoad": "Les données d'impression n'ont pas pu être chargées.", "alrFailedLogicalType": "Échec du chargement du type logique.", "alrFailedMultiBoxData": "Les données MultiBox n'ont pas pu être chargées.", "alrFailedNewCase": "Une erreur s'est produite lors de la configuration d'un nouveau cas !", "alrFailedNewFolder": "La modification du nom du dossier a échoué.", "alrFailedNoticeData": "Échec du chargement des données d'avertissement.", "alrFailedOrgUnitUser": "Échec du chargement des unités organisationnelles de l'utilisateur.", "alrFailedOverviewData": "Échec du chargement de l'aperçu.", "alrFailedPlanData": "Échec du chargement des données du plan.", "alrFailedPostData": "L'envoi des données a échoué.", "alrFailedPrintData": "Impossible de charger les données d'impression.", "alrFailedRevisionInfo": "Échec du chargement des nouvelles informations de révision.", "alrFailedSearchBoxData": "Échec du chargement des données SearchBox.", "alrFailedSelectBoxData": "Les données du composant SelectBox n'ont pas pu être chargées.", "alrFailedSuggestBoxData": "Les données de l'outil de suggestion n'ont pas pu être chargées.", "alrFailedTasColors": "Les couleurs de TAS n'ont pas pu être chargées !", "alrFailedTaskHandOver": "Le transfert de tâche a échoué.", "alrFailedTemplateProcesses": "Les modèles de cas n'ont pas pu être chargés.", "alrFailedVarData": "Échec du chargement des données variables.", "alrFileAdded": "Le fichier a été ajouté !", "alrFileDeleteFailed": "La suppression du fichier a échoué.", "alrFileDonwload": "Téléchargement du fichier...", "alrFileDownloaded": "Le fichier a été téléchargé !", "alrFileInfoFailed": "Les informations sur le fichier n'ont pas pu être chargées.", "alrFileMetaSaveFailed": "Les métadonnées du fichier n'ont pas pu être enregistrées.", "alrFileSavedLikeAttach": "Le fichier a été enregistré en tant que document.", "alrFileUploadFailed": "Le chargement du fichier a échoué.", "alrFillAllRequired": "Pour terminer la tâche, toutes les données requises doivent être renseignées !", "alrFillData": "Pour attribuer la tâche, toutes les données doivent être correctement renseignées !", "alrFillDataInRightFormat": "Veuillez remplir les données dans le bon format.", "alrFillDataToCompleteTsk": "Pour terminer la tâche, toutes les données doivent être correctement renseignées !", "alrFillNameAndPass": "Veuillez saisir le nom et le mot de passe.", "alrFillNote": "<PERSON><PERSON><PERSON>z remplir le texte de la note.", "alrFillRequiredItems": "Veuillez remplir les éléments requis.", "alrFolderDataFailed": "Le chargement des données des dossiers a échoué.", "alrFolderDataLoadFailed": "Le chargement des données du dossier a échoué.", "alrFolderFailed": "Le chargement des informations du dossier a échoué.", "alrFolderSaveFailed": "Le dossier n'a pas pu être enregistré !", "alrFoldersLoadFailed": "Les dossiers n'ont pas pu être chargés.", "alrHelpSettingsSaveFailed": "Les paramètres d'aide n'ont pas pu être enregistrés.", "alrHistoricalTskInfoFailed": "Échec du chargement des informations sur la tâche historique.", "alrHistoricalVarsSaveFailed": "Les variables historiques n'ont pas pu être enregistrées.", "alrHistoricalVarsSaved": "Les variables historiques de la tâche ont été enregistrées.", "alrInvLogginHash": "Erreur d'identification.", "alrJsonFailed": "JSON non valide !", "alrLackOfPermsToEdit": "Vous n'avez aucune autorisation de modification ! Le propriétaire est", "alrLackOfPermsToSleepCase": "Vous n'êtes pas autorisé à suspendre le dossier.", "alrLackOfPermsToWakeUpCase": "Vous n'avez aucune autorisation pour réveiller le cas.", "alrLastHistoricalTskIdFailed": "Échec du chargement de l'ID de la dernière tâche historique.", "alrLoadAttachmentsFailed": "Le chargement des documents a échoué.", "alrLogOutFailed": "La déconnexion a échoué.", "alrLoginExpired": "La session de connexion a expiré, veuillez vous reconnecter.", "alrMappingFailed": "Le mappage n'a pas pu être enregistré.", "alrMappingTsksFailed": "Le mappage des tâches n'a pas pu être chargé.", "alrNewCaseBased": "Nouvelle affaire mise en place !", "alrNewFolder": "Un nouveau dossier a été créé.", "alrNewFolderFailed": "La création du nouveau dossier a échoué.", "alrNextTskOpened": "La tâche suivante a été ouverte.", "alrNoDataToPrint": "Aucune donnée pour l'impression n'a été trouvée.", "alrNoteAdded": "La note a été ajoutée !", "alrNoteSaveFailed": "L'enregistrement de la note a échoué.", "alrNoteSaving": "Enregistrement de la note...", "alrNotesLoadFailed": "Le chargement des notes de cas a échoué.", "alrOrgUnitDataFailed": "Échec du chargement des données de l'unité d'organisation.", "alrOrgUnitDeleteFailed": "Échec de la suppression de l'unité organisationnelle.", "alrOrgUnitDeleted": "L'unité organisationnelle a été supprimée !", "alrOrgUnitDeleting": "Suppression de l'unité org. ...", "alrOrgUnitSaveFailed": "Échec de l'enregistrement de l'unité organisationnelle.", "alrOrgUnitSaved": "L'unité d'organisation a été enregistrée !", "alrOrgUnitSaving": "Enregistrement de l'unité d'organisation...", "alrOverviewDataLoadFailed": "Échec du chargement des données d'aperçu.", "alrOverviewSaveFailed": "L'enregistrement de la vue d'ensemble a échoué !", "alrOverviewSaveSameNameFailed": "Le nom de la vue d'ensemble est déjà utilisé par vous ou un autre utilisateur, veuillez choisir un autre nom de vue d'ensemble.", "alrGraphSaveSameNameFailed": "Le nom du graphique est déjà utilisé par vous ou un autre utilisateur, veuillez choisir un autre nom de graphique.", "alrReportSaveSameNameFailed": "Le nom du rapport est déjà utilisé par vous ou un autre utilisateur, veuillez choisir un nom de rapport différent.", "alrOverviewsLoadFailed": "Échec du chargement des aperçus.", "alrPassSaveFailed": "L'enregistrement du mot de passe a échoué.", "alrPassSaved": "Le mot de passe a été enregistré !", "alrPlanReqItems": "Pour enregistrer le plan, remplissez les éléments requis.", "alrPlanSaveFailed": "L'enregistrement du plan a échoué.", "alrPlanSaved": "Le plan a été enregistré !", "alrPreparingPrint": "Préparation de l'impression...", "alrPrintDeleteFailed": "La suppression de l'impression a échoué.", "alrPrintDeleted": "L'impression a été supprimée !", "alrPrintSaveFailed": "L'enregistrement de l'impression a échoué", "alrPrintSaved": "L'impression a été enregistrée !", "alrReadOnlyCaseDataFailed": "Le chargement des données de cas pour la lecture a échoué.", "alrRecalcFailed": "Erreur lors du recalcul !", "alrRecalculating": "Recalcul...", "alrRestorTemplFailed": "La restauration du modèle a échoué.", "alrRoleDataLoadFailed": "Le chargement des données de rôle a échoué.", "alrRoleDeleteFailed": "Échec de la suppression du rôle.", "alrRoleDeleted": "Le rôle a été supprimé !", "alrRoleDeleting": "Suppression du rôle...", "alrRoleSaveFailed": "Échec de l'enregistrement du rôle.", "alrRoleSaved": "Le rôle a été enregistré !", "alrRoleSaving": "Enregistrement d'un rôle...", "alrRunEvent": "Événement de lancement...", "alrSaveFailed": "L'enregistrement a échoué.", "alrSaved": "Enregistré !", "alrSaving": "Économie...", "alrSavingBeforeRecalcFailed": "Erreur lors de l'enregistrement avant le recalcul !", "alrSavingFailed": "Erreur lors de l'enregistrement !", "alrSavingPlan": "Plan d'épargne...", "alrSavingPrint": "Enregistrement de l'impression...", "alrSearchNoResults": "Aucun résultat correspondant aux paramètres de recherche.", "alrSearchRequestFailed": "Erreur lors de l'envoi de la demande !", "alrSearching": "Recherche...", "alrSettFailed": "Les paramètres n'ont pas pu être enregistrés.", "alrSettSaved": "Les paramètres ont été enregistrés.", "alrSettingsLoadFailed": "Les données de paramètres n'ont pas pu être chargées.", "alrSleepCaseFailed": "Échec de la suspension du dossier.", "alrStoreNameNotDefined": "<PERSON> magasin « {{storeName}} » n'est pas défini.", "alrStoreNotDefined": "Le magasin n'a pas été défini.", "alrSubActionNotDefined": "La sous-action et le suffixe doivent être définis.", "alrSubStoreNotDefined": "Le sous-magasin et le suffixe doivent être définis.", "alrSuggestBoxDataNotContains": "Les données de la boîte de suggestion « {{label}} » ne contiennent pas « {{prop}} » !", "alrSuspendingCase": "Etui suspendu...", "alrTableDataFailed": "Échec du chargement des données de la table.", "alrTasNewVersion": "Une nouvelle version de l'application a été trouvée.", "alrRefresh": "Il est nécessaire de {{- spanRefresh}} la page dans le navigateur.", "alrTasVersionLoadFailed": "Le chargement de la version de l'application a échoué !", "alrTaskHandOver": "Transmettre la tâche...", "alrTaskHandedOver": "La tâche a été confiée à l'utilisateur :", "alrTaskNoRights": "Vous n'êtes pas autorisé à afficher la tâche n°. {{id}}.", "alrTaskNotFound": "La tâche n'a pas été trouvée.", "alrTempDataLoadFailed": "Les données des modèles n'ont pas pu être chargées.", "alrTempHeadersLoadFailed": "Échec du chargement des en-têtes de modèle.", "alrTempDeleteFailed": "La suppression du modèle a échoué.", "alrTempDeleted": "Le modèle a été supprimé !", "alrTempFoldersLoadFailed": "Les dossiers de modèles n'ont pas pu être chargés.", "alrTempNameLoadFailed": "Le nom du modèle n'a pas pu être chargé.", "alrTempRestored": "Le modèle a été restauré avec le statut Développé.", "alrTempSaveFailed": "Échec de l'enregistrement du modèle.", "alrTempsLoadFailed": "Le chargement des modèles a échoué.", "alrTempVarDataLoadFailed": "Échec du chargement des données variables du modèle.", "alrTempVarSaveFailed": "L'enregistrement de la variable a échoué.", "alrTempVarsLoadFailed": "Échec du chargement des variables de modèle.", "alrTotalCountFailed": "Échec du comptage du nombre total d'enregistrements dans la table.", "alrTreeDataFailed": "Le chargement des données de l'arborescence a échoué.", "alrTskAddFailed": "Échec de l'ajout de la tâche.", "alrTskAdded": "La tâche a été ajoutée !", "alrTskAdding": "Ajout d'une tâche...", "alrTskAssignFailed": "L'attribution de la tâche a échoué.", "alrTskAssigned": "La tâche a été attribuée.", "alrTskCompleteFailed": "Erreur lors de l'achèvement de la tâche.", "alrTskDataLoadFailed": "Les données de la tâche n'ont pas pu être chargées.", "alrTskDeleteFailed": "La suppression de la tâche a échoué.", "alrTskDeleted": "La tâche a été supprimée !", "alrTskNameLoadFailed": "Le nom de la tâche n'a pas pu être chargé.", "alrTskRecalculated": "Tâche recalculée !", "alrTskSaveFailed": "Erreur lors de l'enregistrement de la tâche.", "alrTskSaved": "Tâche enregistrée !", "alrTskSavedAndCompleted": "Tâche enregistrée et terminée !", "alrTskScheduleFailed": "La planification de la tâche a échoué.", "alrTskScheduled": "La tâche a été planifiée.", "alrTskTakeFailed": "La reprise de la tâche a échoué.", "alrTskTaken": "La tâche a été reprise.", "alrTskTakign": "Reprendre la tâche...", "alrTsksMappingFailed": "Le chargement des tâches de mappage a échoué.", "alrUserDataLoadFailed": "Le chargement des données utilisateur a échoué.", "alrUserDeleteFailed": "La suppression de l'utilisateur a échoué.", "alrUserDeleted": "L'utilisateur a été supprimé !", "alrUserDeleting": "Suppression de l'utilisateur...", "alrUserIsNotActive": "L'utilisateur n'est pas actif.", "alrUserNotLoaded": "L'utilisateur n'a pas réussi à charger.", "alrUserParamsLoadFailed": "Les paramètres utilisateur n'ont pas pu être chargés.", "alrUserSaveFailed": "L'enregistrement de l'utilisateur a échoué.", "alrUserSaved": "L'utilisateur a été enregistré !", "alrUserSaving": "Enregistrement de l'utilisateur...", "alrUserStatusChangeFailed": "La modification du statut de l'utilisateur a échoué.", "alrUserStatusChanged": "Le statut d'utilisateur a été modifié !", "alrUserStatusChanging": "Modification du statut de l'utilisateur...", "alrVarDeleteFailed": "La suppression de la variable a échoué.", "alrVarDeleted": "La variable a été supprimée !", "alrVarSaveFailed": "L'enregistrement de la variable a échoué.", "alrVarSaved": "La variable a été enregistrée.", "alrVarSaving": "Enregistrement des variables...", "alrVarsForModalFilterFailed": "Échec du chargement des variables de filtre modal.", "alrVarsLoadFailed": "Les variables n'ont pas pu être chargées.", "alrVarsOrderLoadFailed": "Le chargement de l'ordre des variables a échoué.", "alrVarsOrderSaveFailed": "L'enregistrement de l'ordre des variables a échoué.", "alrViceDeleted": "La suppléance a été annulée.", "alrViceFailed": "La suppléance n'a pas réussi.", "alrViceNotFound": "La suppléance n'a pas été trouvée !", "alrViceSaveFailed": "La suppléance n'a pas pu être enregistrée.", "alrViceSaved": "La suppléance a été enregistrée !", "alrViceSaving": "Enregistrement de la suppléance...", "always": "Toujours", "annually": "<PERSON><PERSON><PERSON>", "assHierarchy": "Relation avec la personne de référence", "assHierarchyAncestors": "Tous les supérieurs de la personne de référence", "assHierarchyChildren": "Subordonnés directs de la personne de référence", "assHierarchyDescendants": "Tous les subordonnés de la personne de référence", "assHierarchyGuarantor": "<PERSON><PERSON> personne de référence", "assHierarchyParent": "Supérieur direct de la personne de référence", "assHierarchySiblings": "Collègues de la personne de référence", "assMethodAutomatic": "Par l'ordinateur", "assMethodLastSolver": "Au dernier solveur de la tâche", "assMethodLastSolverChoice": "Solveur de la tâche sélectionné par le solveur de la tâche la plus récente", "assMethodLeast": "Solveur de la tâche avec le moins de tâches", "assMethodPull": "<PERSON><PERSON>, la tâche ne sera proposée", "assMethodSelect": "Solveurs de tâche sélectionnés par le superviseur de tâche", "assMethodVariable": "Sol<PERSON>ur de la tâche, à partir d'une variable", "assessmentOfConds": "Évaluation des conditions", "assign": "Attribuer", "assignAttrs": "Attribution d'attributs", "assignAttrsLogType": "Attribution d'attributs au type logique de document", "assigned": "Attribué", "assigningRoles": "Attribuer des rôles", "assignments": "L'attributions", "attachments": "Documents", "attachmentsList": "Liste des documents", "attribute": "Attribut", "attributeNew": "Attribut – Nouveau", "availableVars": "Variables disponibles", "body": "Corps", "borders": "Les frontières", "byFolders": "Par dossiers", "byOrganization": "Par organisation", "byRole": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "calculation": "Calcul", "calculations": "<PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON>", "carriedIfNoOther": "Sera effectué si aucun autre", "case": "Cas", "caseCreation": "Création de cas", "caseGraph": "Diagramme d'instance", "caseNoEvents": "Le cas ne contient pas d'événements.", "caseNum": "Cas n°", "caseOwner": "Proprié<PERSON> du dossier", "caseStatus": "État de cas", "caseVar": "cas", "cases": "Cas", "casesWithProblem": "Mes cas avec un problème", "category": "<PERSON><PERSON><PERSON><PERSON>", "changeTaskSolver": "Changement de solveur de tâche", "changedBy": "Changé par", "changedWhen": "<PERSON><PERSON> (quand)", "checkbox": "Case à cocher", "checkboxList": "Liste des cases à cocher", "choosePrint": "Mo<PERSON><PERSON><PERSON> d'impression", "chooseUserToAssignTsk": "Choisissez l'utilisateur à attribuer à la tâche", "choosenAttrs": "Attributs choisis", "city": "Ville", "class": "Classe", "clickToClose": "Fermer en cliquant", "clickToRefresh": "Cliquez pour mettre à jour la page dans le navigateur", "clickToRepeat": "Répétez l'action en cliquant", "clientLanguage": "<PERSON>ue du client", "cloneRow": "Ligne en double", "close": "<PERSON><PERSON><PERSON>", "closeAll": "<PERSON><PERSON>e tout", "coWorkersOfPlanGuar": "Collaborateurs du garant du régime", "color": "<PERSON><PERSON><PERSON>", "colors": "Couleurs", "column": "Colonne", "columnName": "Nom de colonne", "comment": "<PERSON><PERSON><PERSON>", "complete": "Complet", "completion": "Achèvement", "componentDescription": "Descriptif du composant", "condition": "Condition", "conditions": "Conditions", "confirmAttachDeletion": "Voulez-vous vraiment supprimer le document ?", "confirmDeleteDialog": "Voulez-vous vraiment supprimer la {{variable}} ?", "confirmDialogEventSave": "Pour basculer, il faut enregistrer l'événement. Souhaitez-vous l'enregistrer ?", "confirmResetDashboard": "Voulez-vous vraiment réinitialiser le tableau de bord ?", "confirmSaveChanges": "Enregistrer vos modifications ?", "confirmSaveDiagramChanges": "Enregistrer vos modifications dans le diagramme ?", "confirmSaveTaskChanges": "Enregistrer vos modifications dans la tâche ?", "confirmRestoreDialog": "Voulez-vous vraiment restaurer la {{variable}} ?", "confirmSaveNote": "Vou<PERSON>z-vous enregistrer une note ?", "confirmSleepCase": "Voulez-vous vraiment suspendre l'affaire ?", "confirmTakeoverTsk": "Voulez-vous vraiment reprendre la tâche?", "confirmWakeUpCase": "Voulez-vous vraiment lever l'affaire ?", "connection": "<PERSON><PERSON>", "connectionFailed": "La connexion au serveur a échoué.", "connectionVar": "lien", "constant": "<PERSON><PERSON><PERSON>", "contact": "<PERSON>er", "contactTaskOwner": "<PERSON><PERSON> le solveur de la tâche", "containerSettings": "Paramètres du conteneur", "contains": "contient", "continueSolving": "Continuer en solution", "copied": "Copié !", "copy": "<PERSON><PERSON>", "copyShortcut": "Appuyez sur Ctrl+C", "copyToClipboard": "Copier dans le presse-papier", "createForm": "Créer un formulaire", "csv": "CSV", "csvFile": "Fichier CSV", "customPrint": "Impression personnalisée", "daily": "Quotidien", "dashCvNoOverview": "Aucun aperçu sélectionné - vous le choisissez dans les paramètres du conteneur", "dashCvNoRights": "Vous n'êtes pas autorisé à afficher l'aperçu, ve<PERSON><PERSON>z contacter l'administrateur.", "dashFavNoShortcut": "Aucun suppléant sélectionné – choisissez-les dans les paramètres du conteneur", "dashboard": "Tableau de bord", "date": "Date", "dateList": "LDV des dates", "day": "journ<PERSON>", "dayE": "Jour", "daysDHM": "Jours : (jj:hh:mm)", "defChangeVarInfluence": "Ce changement de définition de variable se propagera dans les cas déjà créés.", "defChangeInfluence": "Ce changement de définition se propagera dans les cas déjà créés.", "defaultCaseName": "Nom de cas par défaut", "defaultLbl": "{{label}} par défaut", "defaultLblShe": "{{label}} par défaut", "defaultLblIt": "{{label}} par défaut", "defaultPrintProcess": "Par d<PERSON> – processus", "defaultPrintTask": "Par d<PERSON> tâche", "defaultValue": "Valeur par défaut", "delUser": "Supprimer l'utilisateur", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteCol": "Supprimer la colonne", "deleteRow": "Supprimer la ligne", "deleteSelected": "Su<PERSON><PERSON>er sélectionnée", "deleted": "Supprimé", "deletedOn": "Supprimé", "deletedShe": "Supprimé", "description": "La description", "deselect": "Désé<PERSON><PERSON>ner", "detail": "Détail", "developed": "En cours de développement", "dial": "Cadran", "dic": "T.V.A.", "directSubOfPlanGuar": "Subordonné direct du garant du régime", "directSupOfPlanGuar": "Supérieur direct du garant du régime", "disableFilter": "Désactiver le filtre", "dmsAssignAttrs": "Attribution d'attribut DMS", "dmsAttribute": "Attribut DMS", "dmsAttributes": "Attributs DMS", "dmsColumns": "DMS – colonnes", "dmsVisNull": "Seulement dans ce processus", "dmsVisSub": "Dans les sous-processus", "dmsVisSup": "<PERSON>s le processus parent", "dmsVisSupSub": "Dans les processus parent et subordonné", "dmsVisibility": "Les documents seront vus", "doNotShowVariablesWith_": "Le nom de la variable commençant par `_` ne sera pas montré aux utilisateurs", "document": "Document", "documentVar": "document", "documents": "Documents", "doesNotContain": "ne contient pas", "done": "Fait", "download": "Télécharger", "dragAddFiles": "Ajoutez des fichiers par glisser-déposer ou cliquez sur {{- here }} pour sélectionner les fichiers.", "dragAddFile": "Ajoutez un fichier par glisser-déposer ou cliquez sur {{- here }} pour sélectionner le fichier.", "here": "ici", "dropContainer": "Conteneur <PERSON>", "dropzoneTip": "Déposez les fichiers ici", "dropZoneUserPhoto": "Déposez une image ici ou cliquez pour sélectionner un fichier à télécharger.", "dueDate": "Date d'échéance", "duty": "Devoir", "dynList": "Liste dyn.", "dynRowsDefinition": "Définition de table et de colonnes", "dynTableName": " Nom de table dynamique", "dynTable": "Tableau dynamique", "dynTables": "Tableaux dynamiques", "dynamicList": "Liste dynamique", "dynamicRows": "Lignes dynamiques", "dynamicTable": "Tableau dynamique", "edit": "Modifier", "editAttribute": "Modifier l'attribut", "editOrgUnit": "Modifier l'unité organisationnelle", "editRole": "Modifier le rôle", "editRule": "Modifier la règle", "editUser": "Modifier l'utilisateur", "editor": "<PERSON><PERSON><PERSON>", "email": "E-mail", "emailsQueue": "File d'attente d'emails", "empty": "Vider", "end": "Finir", "error": "<PERSON><PERSON><PERSON>", "errored": "<PERSON><PERSON><PERSON>", "error404": "<PERSON><PERSON><PERSON> 404 - Page non trouvée !", "event": "Événement", "events": "Événements", "eventsRun": "Exécuter l'événement", "every": {"masc": "Tous", "neutral": "Tous", "repeat": "Toutes"}, "everyWorkDay": "Chaque jour de travail", "excel": "Excel", "favourites": "<PERSON><PERSON><PERSON>", "fax": "Fax", "file": "Dossier", "fileLogicalType": "Type de fichier logique", "fileName": "Nom de fi<PERSON>er", "filePlacement": "Placement dans le dossier", "files": "Des dossiers", "filter": "filtre", "filterFrom": "Filtrer à partir de", "filterTitle": "Filtre", "filtrate": "Filtre", "finishTask": "<PERSON><PERSON><PERSON> la tâche", "finished": "<PERSON><PERSON><PERSON><PERSON>", "finishedBy": "Terminé par", "finishedOn": "<PERSON><PERSON><PERSON><PERSON>", "first": "Première", "firstLeft": "Première à gauche", "firstName": "Prénom", "firstRight ": "Première à droite", "firstRowColumnsName": "La première ligne contient les noms des colonnes", "folder": "Dossier", "folder-": "Dossier -", "folderExecRightsText": "Attribuez des rôles qui pourront initier des cas dans le dossier", "folderExecRightsTextOS": "Attribuer des unités organisationnelles qui pourront initier des cas dans le dossier", "folderName": "Nom de dossier", "font": "Police de caractère", "fontMainHeader": "Police de l'en-tête principal", "form": "Formulaire", "fourth": "Quatrième", "freeTsk": "T<PERSON>che gratuite", "fri": "<PERSON><PERSON><PERSON><PERSON>", "from": "<PERSON><PERSON><PERSON>", "fsDescription": "la description", "fsName": "nom", "fsTooltip": "info-bulle", "fullName": "Nom complet", "fullScreen": "Plein écran", "getTotalCount": "Le compte total", "graph": "Graphique", "handExecutionTaskListEmpty": "Choisir un événement", "handOver": "Remettre", "handOverToUser": "Remettre à l'utilisateur", "handover": "Remettre", "headerDashboard": "En-tête du tableau de bord", "help": "Aider", "hideLogout": "Masquer la déconnexion", "hideNewProcess": "Masquer « Nouveau cas »", "hideProcs": "Masquer les cas", "hideTasks": "Masquer les tâches", "historicalValues": "Valeurs historiques", "currentValues": "Valeurs actuelles", "history": "Histoire", "home": "Accueil", "html": "HTML", "ic": "ID de l'entreprise", "id": "ID", "inCasesNames": "Au nom des cas", "inTasksNames": "Dans les noms de tâches", "inDevelopment": "En cours de développement", "inEvery": "dans chaque", "inFiles": "Dans les fichiers", "initiator": "Initiateur", "inTasks": "Dans les tâches", "inactive": "Inactif", "inactiveShe": "Inactif", "incidences": "occurrences", "inclusion": "Inclusion", "info": "Info", "inputParams": "Paramètres d'entrée", "insert": "<PERSON><PERSON><PERSON><PERSON>", "insertAttachTip": "Glisser-déposer pour insérer un document", "insertVar": "Insérer une variable", "insertSnippet": "Insérer un extrait", "snippet": "Extrait de code", "insertedBy": "telechargé par", "insertedOn": "<PERSON><PERSON><PERSON><PERSON>", "insteadOf": "Au lieu", "instructions": "Des instructions", "invitation": "Invitation", "isEmail": "Pas un e-mail valide", "isEmpty": "est vide", "isExisty": "Ce n'est pas valide", "isManagerOrgUnit": "Est le gestionnaire unité org.", "isNotEmpty": "n'est pas vide", "isRequired": "Ce champ est obligatoire", "justSave": "Enregistrer simplement", "keepGlobalOrder": "Gardez l'ordre mondial", "key": "Clé", "last": "<PERSON><PERSON><PERSON>", "lastName": "Nom de famille", "lastOwnerOfTask": "Dernier solveur de la tâche", "licenceKey": "Clé de licence", "link": "<PERSON><PERSON>", "linkConditions": "Conditions du lien", "list": "Liste", "listName": "nom de la liste", "listOfValues": "Liste de valeurs", "listValue": "valeur de la liste", "loading": "Chargement...", "location": "Emplacement", "locked": "Fermé à clé", "logIn": "Se connecter", "logOut": "Se déconnecter", "logicalType": "Type logique", "loginError": "La connexion a échoué", "loginTimeout": "D<PERSON>lai d'expiration de la session (sec.)", "longText": "Texte long", "mailEscalation": "E-mail avec aperçu des tâches escaladées", "mailProcEscalation": "E-mail avec aperçu des cas escaladés", "mailPromptly": "Notification par e-mail d'une nouvelle tâche", "mailPull": "Notification par e-mail de la tâche à extraire", "mailTotal": "E-mail récapitulatif avec aperçu des tâches", "mainButton": "Bouton principal", "mainColor": "Couleur principale", "mainHeader": "En-tête principal", "mainLanguage": "Langage principal", "manager": "Gestionnaire", "managerOfOrgUnit": "Responsable d'unité organisationnelle", "mandatory": "Obligatoire", "manualStartEvent": "Démarrage manuel de l'événement", "mapping": "Mappage", "mappingSubProcessVars": "Mappage des variables de sous-processus", "markAll": "<PERSON><PERSON> tout", "menu": "<PERSON><PERSON>", "mine": "<PERSON><PERSON>", "mobilePhone": "Téléphone portable", "mon": "<PERSON><PERSON>", "month1": "<PERSON><PERSON>", "month10": "Octobre", "month11": "Novembre", "month12": "Décembre", "month2": "<PERSON><PERSON><PERSON><PERSON>", "month3": "Mars", "month4": "Avril", "month5": "<PERSON>", "month6": "Juin", "month7": "<PERSON><PERSON><PERSON>", "month8": "Août", "month9": "Septembre", "monthI": "mois", "monthly": "<PERSON><PERSON><PERSON>", "months": "mois", "more": "Suite", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSimple", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "Multi-instance", "myUnfinishedTasks": "Mes tâches inachevées", "name": "Nom", "nested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "never": "<PERSON><PERSON>", "new": "Nouveau", "newCase": "Nouveau cas", "newFolder": "Dossier – Nouveau", "newForm": "Nouvelle forme", "newIt": "Nouveau", "newName": "Nouveau nom", "newShe": "Nouveau", "newSolver": "Nouveau solveur de la tâche", "no": "Non", "noAttach": "Aucun document (Cliquez pour ajouter)", "clickToAddAttach": "Cliquez pour ajouter", "noName": "Sans nom", "noOneBeOffered": "personne, la tâche sera proposée à un groupe restreint d'utilisateurs", "noPageRights": "Vous n'êtes pas autorisé à afficher cette page.", "node": "<PERSON><PERSON><PERSON>", "notFound": "<PERSON><PERSON> trouvé", "notMatch": "Ne pas correspondre", "notNumber": "Pas un numéro", "notIntNumber": "Pas un entier", "notSent": "Non envoyé", "notValid": "Pas valide", "notes": "<PERSON><PERSON><PERSON>", "notesOnContacts": "Remarques sur les contacts", "notice": "<PERSON><PERSON>", "notification": "Notification", "nrOfItems": "Nombre d'objets", "number": "<PERSON><PERSON><PERSON><PERSON>", "numberList": "LDV des nombres", "ok": "D’accord", "oneMustBeMet": "Au moins un doit être rencontré", "onlyOrgUnit": "Unité organisationnelle uniquement", "onlyPlanGuarantor": "Garant du régime seulement", "openAll": "<PERSON><PERSON>vre tout", "operating": "Actif", "order": "Ordre", "orderByColumn": "Trier par colonne", "orgName": "Nom du sujet", "orgStructure": "Structure org.", "orgUnit": "unité org.", "orgUnitE": "unité org.", "orgUnitName": "Nom de l'unité org.", "orgUnitShe": "Unité org.", "orgUnits": "Unités org.", "organization": "Organization", "overview": "<PERSON><PERSON><PERSON><PERSON>", "overviewMapping": "Mappage de l'aperçu", "overviewNew": "Aperçu – nouveau", "overviewSetSharing": "Définir le partage de la vue d'ensemble pour chaque groupe d'utilisateurs", "overviews": "<PERSON><PERSON><PERSON><PERSON>", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerWithLeastTasks": "Solveur de la tâche avec le moins de tâches", "pageNotFound": "Page non trouvée", "parentFolder": "Dossier parent", "parentUnit": "<PERSON><PERSON>", "participants": "Intervenants", "password": "Mot de passe", "passwordChallenge": "Un appel", "passwordChallengeText": "Voulez-vous vraiment demander à tous les utilisateurs de changer leur mot de passe ?", "passwordChange": "Changer le mot de passe", "passwordCheck": "<PERSON>t de passe (vérifier)", "passwordNew": "Nouveau mot de passe", "passwordNewCheck": "Nouveau mot de passe (vérifier)", "paused": "Inactif", "personInOrgStr": "Attribué par personne dans structure org.", "phone": "Téléphone", "photo": "Photo", "plan": "plan", "planGuarantor": "<PERSON><PERSON><PERSON> du régime", "planTitle": "Plan", "plans": "Planification", "plnOffType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plnOrgUnit": "Unité organisationnelle", "plnTProc": "<PERSON><PERSON><PERSON><PERSON>s", "plnUser": "Promoteur du régime", "plnUsersSelect": "Conditions restrictives de sélection d'un ou plusieurs Initiateurs", "prependTsk": "Préfixer la tâche", "prependedTsk": "<PERSON><PERSON><PERSON> a<PERSON>", "primaryKey": "Clé primaire", "print": "<PERSON><PERSON><PERSON><PERSON>", "printTemplate": "Mo<PERSON><PERSON><PERSON> d'impression", "printType": "Type d'impression", "printer": "Impression – HTML", "priority": "Priorité", "procDescription": "Description du processus", "procDueDateFinish": "Date limite pour terminer le dossier", "procName": "Nom du cas", "procOwner": "Propriétaire du processus", "procSummary": "En cours de résolution", "process": "Processus", "processName": "Nom du processus", "property": "Biens", "quickFilter": "Filtre rapide", "radioButtonList": "RadioButtonList", "reEvaluates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recalc": "Recalculer", "recipient": "<PERSON><PERSON><PERSON>", "recipientsId": "ID du bénéficiaire", "records": "Dossiers", "referenceUser": "<PERSON><PERSON> référen<PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registered": "Inscrit", "relatToPlanSponsor": "Relation avec le promoteur du régime", "remove": "<PERSON><PERSON><PERSON>", "removeVice": "Supprimer la suppléance", "renameCols": "Renommer les colonnes", "repeatLogin": "Répétez la connexion ou choisissez un autre type d'authentification.", "repeatOrReport": "Réessayez plus tard ou contactez l'administrateur.", "repetition": "Répétition", "required": "Obligatoire", "reset": "Réinitialiser", "restrictTaskOwners": "Restrictions pour les solveurs de tâches", "restrictUsers": "Restreindre les utilisateurs", "returnSubProcessVars": "Retour des variables de sous-processus", "revision": "Révision", "right": "À droite", "rightOrDuty": "Droit / Devoir", "role": "r<PERSON><PERSON>", "roleName": "Nom de rôle", "roleSg": "R<PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>", "row": "<PERSON><PERSON>", "rule": "<PERSON><PERSON><PERSON>", "ruleCSVFile": "Nom du fichier CSV", "ruleCSVHeader": "La première ligne du fichier CSV est un en-tête", "ruleCSVMask": "Masque de nom de fichier CSV", "ruleCSVSeparator": "Le séparateur de colonnes", "ruleNew": "Règle – Nouveau", "ruleParamsMap": "Variables de mappage", "ruleProcOwnCSV": "Défini dans le mappage", "ruleTypeCSVExpProcs": "CSV Exporter tous les modèles de cas", "ruleTypeCSVMrgProcs": "Selon le CSV, exécutez les cas et mettez à jour les variables des cas", "ruleTypeCSVRunProcs": "Selon CSV exécuter les cas", "ruleTypeCSVUpdProc": "Selon CSV, mettez à jour les variables de cas", "ruleTypeCSVUpdProcs": "Selon CSV, mettez à jour les variables de cas", "ruleTypeCSVUpdateList": "Mettre à jour la liste dynamique selon CSV", "ruleTypeReturn": "Réponse à l'événement", "ruleTypeUpdateListOfProcesses": "Mettre à jour la liste dynamique des processus", "rules": "<PERSON><PERSON><PERSON>", "run": "Cours", "runProcess": "<PERSON><PERSON><PERSON><PERSON> le processus", "running": "Fonctionnement", "sat": "<PERSON><PERSON>", "save": "Enregistrer", "saveAsAttachment": "Enregistrer l'impression en tant que document dans le cas", "scheduling": "Planification", "scheme": "Identité visuelle", "script": "<PERSON><PERSON><PERSON>", "scripts": "Scénarios", "search": "Recherche", "searchResult": "Résultat de la recherche", "second": "Seconde", "secondLeft": "Deuxième à gauche", "secondRight": "Deuxième à droite", "selectBox": "SelectBox", "selectDrop": "SélectionnerDéposer", "selectedByComputer": "Aux solveurs de tâches sélectionnés automatiquement par l'ordinateur", "selectedByTaskSupervisor": "Aux solveurs de tâches sélectionnés par le superviseur", "selectedPrint": "impression sélectionnée", "selectedUser": "Utilisateur sélectionné", "send": "Envoyer", "sendingFailed": "<PERSON><PERSON><PERSON>", "sendOn": "Temps d'envoi", "sendTestMail": "E-mail test", "sequence": "S<PERSON><PERSON>", "setDefault": "Définir par défaut", "setVice": "Définir la suppléance", "setViceAttachmentsNotes": "Droit de télécharger des documents et des notes", "settings": "Réglages", "shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "showAttachmentsClick": "En cliquant, vous afficherez les documents", "showCommentCol": "Afficher la colonne des commentaires", "skype": "Skype", "solve": "Résoudre la tâche", "solvedBy": "Résolu par", "solver": "Solveur de la tâche", "sort": "<PERSON><PERSON>", "sortByColumn": "Trier par colonne", "sorting": "Tri", "sourceTask": "Tâche source", "sourceVar": "Variable source", "start": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "Date de début", "startCalDate": "Date de début", "endCalDate": "Date de fin", "state": "État", "stateAddress": "État", "status": "Statut", "street": "<PERSON><PERSON>e de la rue", "subProcess": "Sous-processus", "subject": "<PERSON><PERSON>", "substitute": "Suppléant", "sun": "<PERSON><PERSON><PERSON>", "superior": "<PERSON><PERSON><PERSON><PERSON>", "supervis": "Superviseur", "supervisor": "Superviseur de tâches", "suspend": "<PERSON><PERSON><PERSON><PERSON>", "suspended": "Suspendu", "suspendedx": "Suspendu", "tTaskAgain": "Comportement d'activation répétée", "tTaskAutoCompleteCaption": "La tâche sera exécutée automatiquement si", "tTaskCompletionCOA": "toutes les conditions sont remplies en même temps", "tTaskCompletionCOO": "au moins une condition est remplie", "tTaskDueOffsetNone": "immédiatement", "tTaskDueOffsetPO": "entré par le superviseur", "tTaskDueOffsetPS": "dans les jours suivant le début de l'affaire", "tTaskDueOffsetTS": "dans les jours suivant le début possible de l'activité", "tTaskDueOffsetVC": "de variables en continu", "tTaskDueOffsetVO": "de variables au démarrage", "tTaskInvClassConf": "Secret", "tTaskInvClassPriv": "Priv<PERSON>", "tTaskInvClassPubl": "Public", "tTaskInvPriority1": "1-plus <PERSON><PERSON><PERSON>", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-le plus bas", "tTaskInvokeEventB": "en arrière-plan", "tTaskInvokeEventI": "immédiatement", "tTaskReferenceUserLastSolver": "Le dernier solveur de la tâche", "tTaskReferenceUserMan": "Responsable d'unité org. xy", "tTaskReferenceUserUser": "Utilisateur xy", "tTaskRunOnlyOnce": "Exécuter une seule fois", "tTaskSufficientEnd": "Remplir termine l'ensemble du cas", "tabName": "Nom de l'onglet", "table": "Table", "takeOnlyOrder": "Ne prendre que la commande", "takeover": "Reprendre", "targetTask": "Tâche cible", "targetVar": "Variable cible", "taskAutomatic": "état automatique", "taskEmailNotification": "Notification par e-mail", "taskEvent": "exécute l'événement", "taskEventWait": "attend l'événement", "taskOwner": "Solveur de la tâche", "taskSolverAssign": "pour l'attribution au solveur de tâches", "taskStart": "<PERSON><PERSON><PERSON><PERSON>", "taskStatus": "Statut", "taskStatusA": "Actif", "taskStatusAP": "Sous-processus actif", "taskStatusAS": "Sous-processus de sommeil", "taskStatusD": "<PERSON><PERSON><PERSON><PERSON>", "taskStatusL": "<PERSON><PERSON><PERSON>", "taskStatusLEdit": "Impossible de modifier la tâche en attente", "taskStatusN": "Nouveau", "taskStatusP": "Prévu", "taskStatusS": "Suspendu", "taskStatusT": "<PERSON><PERSON><PERSON>", "taskStatusW": "Pour l'attribution", "taskStatusWT": "Pour la planification", "taskSubprocess": "mise en œuvre par sous-processus", "taskTabVariables": "Variables attribuées", "taskType": "Type de tâche", "taskWillBeAssigned": "La tâche sera attribuée", "tasks": "Tâches", "tasksToPull": "Tâches à tirer", "taskstatusAD": "Actif et terminé", "tempId": "ID de modèle", "tempVar": "mod<PERSON><PERSON>", "template": "<PERSON><PERSON><PERSON><PERSON>", "templateDeleted": "Supprimé", "templateStatus": "État du modèle", "templates": "<PERSON><PERSON><PERSON><PERSON>", "templatesFolder": "<PERSON><PERSON><PERSON><PERSON> – dossier", "testForm": "Formulaire d'essai", "tested": "<PERSON><PERSON>", "text": "Texte", "textList": "LOV des textes", "textMultipleLines": "Texte avec plusieurs lignes", "textSuggest": "Suggesteur", "third": "Troisième", "thirdCenter": "Troisième centre", "thu": "<PERSON><PERSON>", "thumbnail": "Vignette", "title": "Titre", "to": "Pour", "toHide": "<PERSON><PERSON>", "toInclusive": "À (inclus)", "toPull": "Tâches à tirer", "tooltip": "Info-bulle", "total": "total", "tprocName": "<PERSON><PERSON><PERSON><PERSON> process<PERSON>", "tsk": "<PERSON><PERSON><PERSON>", "tskAssignDues": "Définir des restrictions de temps pour cette tâche", "tskName": "Nom de la tâche", "tskNum": "Numéro de <PERSON>", "tskSolver": "Solveur de la tâche", "tskTemplate": "<PERSON><PERSON><PERSON><PERSON>", "tskVar": "tâche", "tsksDone": "<PERSON><PERSON><PERSON>", "tsksSolvers": "Solveurs de tâches", "ttAdd": {"heading": "Ajouter", "body": "Permet d'ajouter un nouvel élément ou de nouveaux paramètres qui n'ont pas encore été définis."}, "ttAddActivity": {"heading": "Ajouter", "body": ""}, "ttAddAttach": {"heading": "Ajouter un document", "body": "Permet d'ajouter un nouveau document."}, "ttAddAttribute": {"heading": "Ajouter", "body": ""}, "ttAddContainer": {"heading": "Ajouter un conteneur", "body": "Ajoute un conteneur avec le contenu sélectionné."}, "ttAddFile": {"heading": "Ajouter", "body": ""}, "ttAddStructure": {"heading": "Ajouter un élément à la structure organisationnelle", "body": "Permet d'ajouter un nouvel élément de la structure organisationnelle ou de nouveaux paramètres qui n'ont pas encore été définis."}, "ttAddTemp": {"heading": "Ajouter un nouveau modèle", "body": "Création de nouveaux modèles de cas. Le propriétaire du modèle sera l'utilisateur actuellement connecté. Le modèle se voit automatiquement attribuer un statut « en cours de développement »."}, "ttAddTsk": {"heading": "Ajouter une nouvelle tâche", "body": "Création d'une nouvelle tâche dans le modèle de processus. Les paramètres de la tâche peuvent être spécifiés en fonction du type de tâche. Des liens vers d'autres tâches peuvent être ajoutés ou modifiés dans les onglets Graphique ou Lien."}, "ttAddTskGraph": {"heading": "Ajouter une nouvelle tâche", "body": "Création d'une nouvelle tâche dans le modèle de processus. Les paramètres de la tâche peuvent être spécifiés en fonction du type de tâche. Des liens vers d'autres tâches peuvent être ajoutés ou modifiés dans les onglets Graphique ou Lien."}, "ttAddUser": {"heading": "Ajouter un nouvel utilisateur", "body": "Ajouter un nouvel utilisateur. Chaque utilisateur doit avoir un nom d'utilisateur unique. Des informations de base peuvent être définies pour les utilisateurs, ainsi que leur attribution à la structure organisationnelle et l'attribution de rôles. Les nouveaux utilisateurs reçoivent automatiquement un statut verrouillé."}, "ttAddVar": {"heading": "Ajouter une nouvelle variable", "body": "Création d'une nouvelle variable dans le modèle de cas. Chaque variable contient des informations qui peuvent être utilisées par les solveurs de tâches de cas. Il est possible de spécifier le nom, le type et les valeurs par défaut de la variable."}, "ttAddVice": {"heading": "Ajouter une suppléance", "body": ""}, "ttAssignAttribute": {"heading": "Attribution d'attributs au type de document logique", "body": ""}, "ttAssignTsk": {"heading": "Attribuer", "body": "Permet d'attribuer une tâche à un solveur de tâche spécifique ou d'ajouter un élément à une structure définie."}, "ttCases": {"heading": "Cas", "body": ""}, "ttOverviews": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttChangePass": {"heading": "Modification du mot de passe", "body": "Édition des mots de passe des utilisateurs, qui sont gérés directement dans l'environnement de l'application. Si les utilisateurs sont gérés par un service externe (LDAP), le mot de passe doit y être géré."}, "ttClose": {"heading": "<PERSON><PERSON><PERSON>", "body": "La fenêtre sera fermée sans enregistrer les modifications."}, "ttCloseTemp": {"heading": "<PERSON><PERSON><PERSON>", "body": "La fenêtre avec la définition du modèle sera fermée."}, "ttCompleteTsk": {"heading": "<PERSON><PERSON><PERSON> la tâche", "body": "Confirme que la tâche est terminée et l'envoie pour un traitement ultérieur tel que prédéfini."}, "ttContact": {"heading": "Contact", "body": "Affiche les contacts du superviseur de la tâche."}, "ttContainerSettings": {"heading": "Paramètres", "body": "Permet de modifier les paramètres du conteneur donné."}, "ttCopyHdr": {"heading": "<PERSON><PERSON><PERSON> l'en-tête", "body": "Création d'une copie de l'en-tête sélectionné. La sélection de l'en-tête se fait en cliquant dans le tableau des en-têtes du modèle."}, "ttCopyTemp": {"heading": "<PERSON><PERSON><PERSON> le modèle", "body": "Création d'une copie du modèle sélectionné. La sélection du modèle se fait en cliquant dans le tableau des modèles de processus."}, "ttCopyVar": {"heading": "Copie de variable", "body": "Copie de la définition pour la variable sélectionnée et enregistrement de la variable sous un nouveau nom. Les variables sont sélectionnées en cliquant dans le tableau des variables."}, "ttDel": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Supprime l'élément sélectionné."}, "ttDelAttach": {"heading": "Supprimer le document", "body": "Supprime le document sélectionné."}, "ttDelConnection": {"heading": "Supprimer le lien", "body": "Supprimer le lien sélectionné entre deux tâches du dossier. La suppression doit être confirmée. La suppression est effectuée pour le lien sélectionné. Sélectionnez le lien en cliquant dessus dans le tableau des liens."}, "ttDelFolder": {"heading": "Suppression de dossier", "body": "Suppression du dossier sélectionné."}, "ttDelOverview": {"heading": "Supprimer l'aperçu", "body": "Supprime l'aperçu sélectionné."}, "ttDelTemp": {"heading": "Supp<PERSON><PERSON> le modèle", "body": "Attribue le statut supprimé à un modèle. Ce n'est que lorsque la demande de suppression est répétée que le modèle est physiquement supprimé. L'action est appliquée au modèle sélectionné. Sélectionnez le modèle en cliquant dessus dans le tableau des modèles de cas."}, "ttDelTsk": {"heading": "Suppression de tâche", "body": "Suppression de la tâche sélectionnée. La suppression doit être confirmée. Avec la tâche, tous les liens associés vers d'autres tâches dans le modèle de processus seront supprimés. Sélectionnez la tâche en cliquant dessus dans le tableau des tâches."}, "ttDelTskOrConnection": {"heading": "Supprimer une tâche ou un lien", "body": "Suppression d'une tâche sélectionnée ou d'un lien sélectionné entre deux tâches de processus. Cette action doit être confirmée. Les liens associés vers d'autres tâches de processus seront supprimés avec la tâche. Cliquez pour sélectionner."}, "ttDelVar": {"heading": "Suppression de variable", "body": "Suppression de la variable sélectionnée. Cette action doit être confirmée. La variable ne sera plus disponible pour les tâches de processus individuelles. Les variables sont sélectionnées en cliquant dans le tableau des variables."}, "ttDelVice": {"heading": "Supprimer la suppléance", "body": ""}, "ttDetailCase": {"heading": "Détail", "body": "Affiche les détails du cas sélectionné."}, "ttDetailCertificate": {"heading": "Détail du certificat", "body": "Affiche les détails du certificat sélectionné."}, "ttDetailHistory": {"heading": "Détail", "body": "Affiche les détails de l'élément sélectionné."}, "ttDetailTsk": {"heading": "<PERSON><PERSON><PERSON>", "body": "Affiche les détails de la tâche sélectionnée."}, "ttDmsFolderAdd": {"heading": "Ajouter un nouveau dossier", "body": "Ajout d'un nouveau dossier. Si un dossier est sélectionné, il est prérempli comme dossier parent."}, "ttDmsFolderEdit": {"heading": "Modifier le dossier", "body": "Modifier un dossier sélectionné"}, "ttDocuments": {"heading": "Stockage des documents", "body": ""}, "ttDownload": {"heading": "Télécharger", "body": "Télécharge le fichier sélectionné."}, "ttDropContainer": {"heading": "<PERSON><PERSON><PERSON>r", "body": "<PERSON><PERSON><PERSON> le conteneur du tableau de bord"}, "ttENotification": "Notification par e-mail", "ttEdit": {"heading": "Modifier", "body": "Active l'édition de l'élément sélectionné."}, "ttEditAttach": {"heading": "Modifier", "body": "Permet d'afficher et de modifier les attributs (métadonnées) du fichier téléchargé."}, "ttEditConnection": {"heading": "Modifier les liens", "body": "Edition de lien entre deux tâches. Il est possible d'éditer des paramètres de comportement et de conditions de lien. L'action est appliquée au lien sélectionné. Les liens sont sélectionnés en cliquant dessus dans le tableau des liens."}, "ttEditOverview": {"heading": "Modifier l'aperçu", "body": "Permet la modification d'un aperçu sélectionné."}, "ttCopyOverview": {"heading": "Vue d'ensemble de la copie", "body": "Création d'une copie de l'aperçu sélectionné."}, "ttEditPath": {"heading": "Ajouter un aperçu", "body": "Permet la définition d'un nouvel aperçu."}, "ttEditTemp": {"heading": "Modification de la définition du modèle", "body": "Édition du modèle de cas. Tout paramètre du modèle peut être modifié. L'action est effectuée pour le modèle sélectionné. Sélectionnez le modèle en cliquant dans le tableau des modèles de cas."}, "ttEditTsk": {"heading": "Modifier la tâche", "body": "Modification des informations de la tâche et des paramètres de la tâche. L'action est appliquée à la tâche sélectionnée. Sélectionnez une tâche en cliquant dans le tableau des tâches."}, "ttEditTskOrConnection": {"heading": "Edition de tâches ou de liens", "body": "Modification des informations de tâche et des paramètres de tâche ou modification des liens entre deux tâches, leurs paramètres de comportement et les conditions de lien. L'action est appliquée à la tâche ou au lien sélectionné. Cliquez pour sélectionner."}, "ttEditTskVars": {"heading": "Modifier", "body": "Modifier les variables de tâche"}, "ttEditUser": {"heading": "Modification des informations utilisateur", "body": "Modification des informations de base sur les utilisateurs, les mots de passe, l'attribution à l'unité organisationnelle et l'attribution des rôles. L'action est appliquée à l'utilisateur sélectionné. Les utilisateurs sont sélectionnés en cliquant dans le tableau des utilisateurs."}, "ttEditVar": {"heading": "Edition de variable", "body": "Modification du nom, du type et des valeurs par défaut des variables. L'action est appliquée à la variable sélectionnée. Les variables sont sélectionnées en cliquant dans le tableau des variables."}, "ttEnotTgt": "<PERSON><PERSON><PERSON>", "ttEnotTgtG": "Superviseur de tâches", "ttEnotTgtO": "Proprié<PERSON> du dossier", "ttEnotTgtP": "%s", "ttEnotTgtR": "<PERSON><PERSON><PERSON>", "ttEnotTgtS": "Unité organisationnelle %s", "ttEnotTgtT": "Solveur de la tâche %s", "ttEvent": {"heading": "Tâche personnalis<PERSON>", "body": "Invocation instantanée d'un événement dans cette tâche."}, "ttEvents": {"heading": "Événements", "body": "Définition de règles métier pour réagir à des événements internes ou externes définis dans le système. L'accès nécessite le rôle de $PowerUser."}, "ttFavourites": {"heading": "Liste des favoris", "body": "Permet de voir la liste complète des favoris, ainsi que d'en supprimer et de renommer des éléments."}, "ttFilter": {"heading": "Filtre", "body": "Affiche uniquement les éléments qui répondent aux conditions de filtre définies."}, "ttFilterPrc": {"heading": "Filtre", "body": "Affiche uniquement les cas qui répondent aux conditions de filtre définies."}, "ttFilterTemp": {"heading": "Filtre", "body": "Affiche uniquement les modèles qui répondent aux conditions de filtre définies."}, "ttFilterTsk": {"heading": "Filtre", "body": "Affiche uniquement les tâches qui répondent aux conditions de filtre définies."}, "ttFilterUser": {"heading": "Filtre", "body": "Affiche uniquement les utilisateurs qui remplissent les conditions de filtre définies."}, "ttFullScreen": {"heading": "Plein écran", "body": "Affiche le contenu du conteneur en mode plein écran."}, "ttGraph": {"heading": "Graphique", "body": "Représentation graphique de l'état actuel du cas."}, "ttGraphActualFinish": "Finition réelle", "ttGraphActualStart": "Date de début réelle", "ttGraphCond": "Conditions", "ttGraphCond1": "au moins un doit être rencontré", "ttGraphCondAll": "tout doit être satisfait", "ttGraphCondElse": "Sauf si une autre condition est remplie", "ttGraphDeadlinePo": "Date limite : saisie par le propriétaire du dossier", "ttGraphDeadlinePs": "Date limite : dans les %s jours suivant l'ouverture du dossier", "ttGraphDeadlineTs": "Échéance : dans les %s jours suivant le lancement de la tâche", "ttGraphDelayPo": "Lancement de la tâche : saisi par le propriétaire du dossier", "ttGraphDelayPs": "Lancement de la tâche : %s jours depuis le lancement du cas", "ttGraphDelayTs": "Lancement de la tâche : %s jours depuis le lancement de la tâche", "ttGraphEnd": "L'achèvement de la tâche met fin à l'ensemble du cas", "ttGraphFinishedBy": "Terminé par", "ttGraphHiearchyA": "tous les supérieurs du superviseur de tâche", "ttGraphHiearchyC": "subordonné direct du chef de projet", "ttGraphHiearchyD": "tous les subordonnés du superviseur de tâche", "ttGraphHiearchyG": "superviseur de tâche", "ttGraphHiearchyL": "tout", "ttGraphHiearchyP": "supérieur direct du superviseur de tâche", "ttGraphHiearchyS": "superviseur de tâches collègues", "ttGraphLinkFrom": "<PERSON><PERSON><PERSON>", "ttGraphLinkTo": "Pour", "ttGraphMethodL": "au dernier solveur de tâche %s", "ttGraphMethodS": "au solveur de la tâche sélectionné par un superviseur", "ttGraphMethodT": "au solveur de la tâche sélectionné automatiquement", "ttGraphMethodV": "au solveur de la tâche attribuée à la variable %s", "ttGraphMultiinstance": "Multi-instance", "ttGraphNoneMand": "Lien obligatoire", "ttGraphOnlyOnce": "Exécuter une seule fois", "ttGraphSave": {"heading": "Enregistrer le diagramme et créer un modèle", "body": ""}, "ttGraphStart": "La tâche sera activée automatiquement après le démarrage du cas", "ttGraphTaskHiearchy": "Solveur de la tâche", "ttGraphTaskMethod": "La tâche sera attribuée", "ttGraphTaskOwner": "Superviseur de tâches", "ttGraphTaskOwnerOS": "Responsable d'unité organisationnelle", "ttGraphTaskOwnerPO": "Proprié<PERSON> du dossier", "ttGraphTaskOwnerSU": "Utilisateur sélectionné", "ttGraphTaskRole": "avec rôle", "ttGraphTaskTypeA": "Tâche automatique", "ttGraphTaskUser": "Solveur de la tâche", "ttGraphWait1": "Paramètres d'entrée : en attente d'un", "ttGraphWaitA": "Paramètres d'entrée : en attente de tous", "ttGraphWaitFirst": "Paramètres d'entrée : en attente de tous, en cours d'exécution en premier", "ttGraphWaitN": "Paramètres d'entrée : en attente de %s", "ttHandover": {"heading": "Remettre la tâche", "body": "Permet de passer la tâche à un autre utilisateur disponible."}, "ttDelegate": {"heading": "Déléguer une tâche", "body": ""}, "ttReject": {"heading": "<PERSON><PERSON><PERSON>che", "body": ""}, "ttHelp": {"heading": "Aide instantanée", "body": "Autoriser ou désactiver l'aide instantanée. L'aide est affichée sous la forme de bulles qui apparaissent avec des informations sur l'interface utilisateur lorsque les fonctionnalités sont survolées."}, "ttHome": {"heading": "Page utilisateur initiale", "body": "Endroit unique avec toutes les informations pour les utilisateurs réguliers. Le tableau de bord fournit une vue générale."}, "ttHtml": {"heading": "Générer la documentation", "body": "Génération de la documentation HTML du processus de modèle. Selon le type de navigateur, un document peut être affiché immédiatement ou enregistré sur un disque."}, "ttInclusion": {"heading": "Inclusion", "body": "Exporte un fichier avec le résumé des autorisations et des rôles de l'utilisateur, tous les rôles des entités signées utilisées, de l'organisation dont il est membre ou gestionnaire, y compris la hiérarchie des tâches dont il est le supérieur."}, "ttInvAttendees": "Participants", "ttInvDTEnd": "Finir", "ttInvDTStart": "<PERSON><PERSON><PERSON><PERSON>", "ttInvLocation": "Emplacement", "ttInvitation": "Invitation", "ttJustSave": {"heading": "Enregistrez simplement", "body": "Enregistre les modifications."}, "ttLock": {"heading": "Verrouiller", "body": "Verrouille ou déverrouille la sélection"}, "ttLockUser": {"heading": "Verrouiller", "body": "Verrouille ou déverrouille l'utilisateur"}, "ttLogout": {"heading": "Déconnexion", "body": "Déconnexion d'un utilisateur. Après avoir terminé avec succès le travail avec l'application, la boîte de dialogue de connexion initiale s'affiche."}, "ttMapping": {"heading": "Mappage", "body": "Un aperçu général des variables attribuées pour la lecture (R), l'écriture (W) et la saisie obligatoire (M) dans les tâches individuelles avec la possibilité de modifier leur attribution."}, "ttNewCase": {"heading": "Nouveau cas", "body": "Création d'une nouvelle instance de processus - nouveau cas. Il est possible de sélectionner parmi les modèles de processus disponibles ou de créer un cas sans structure de tâche prédéfinie."}, "ttNewOverview": {"heading": "Ajouter une vue", "body": "Permet la définition d'un nouvel aperçu."}, "ttOrgStructure": {"heading": "Structure organisationnelle", "body": ""}, "ttParent": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Basculer vers un cas à partir duquel le cas affiché a été créé en tant que sous-processus."}, "ttPhoto": {"heading": "Photographies", "body": "Téléchargement de la photo sur le profil de l'utilisateur. Prend en charge les formats GIF, JPG et PNG. La taille de l'image sera ajustée automatiquement."}, "ttPlans": {"heading": "Planification", "body": "Définition de règles pour le lancement automatique d'instances de processus uniques ou répétées - cas selon les paramètres spécifiés. L'accès nécessite le rôle de $Administrator."}, "ttPrint": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON>e l'impression."}, "ttRecalc": {"heading": "Recalcul", "body": "Recalculer les variables actuelles."}, "ttRedirectToPrc": {"heading": "Cas", "body": ""}, "ttResetDash": {"heading": "Réinitialiser", "body": "Réinitialise les modifications terminées."}, "ttResetSearch": {"heading": "Réinitialiser", "body": "Réinitialise le formulaire de recherche."}, "ttRestoreTemp": {"heading": "Restauration du modèle", "body": "Rest<PERSON><PERSON> le modèle supprimé"}, "ttRevision": {"heading": "Révision", "body": "Active le téléchargement d'une nouvelle version de fichier"}, "ttRoles": {"heading": "Gestion des rôles", "body": ""}, "ttRunEvent": {"heading": "Exécuter l'événement", "body": "Invocation d'événement dans ce cas"}, "ttSave": {"heading": "Enregistrer", "body": "Enregistre les modifications et ferme la fenêtre."}, "ttSaveDMSCols": {"heading": "Enregistrer les colonnes", "body": ""}, "ttSaveSettings": {"heading": "Enregistrer", "body": "Enregistre les modifications."}, "ttSaveTsk": {"heading": "Enregistrer uniquement", "body": "La tâche ouverte sera enregistrée afin que vous puissiez y revenir plus tard."}, "ttSearch": {"heading": "<PERSON><PERSON><PERSON>", "body": "Démarre la recherche"}, "ttSendNote": {"heading": "Ajouter une note", "body": "Permet l'insertion d'une nouvelle note."}, "ttSetConnectionCond": {"heading": "Condition", "body": "Ajout ou modification de conditions de lien. La modification est appliquée au lien sélectionné. Cliquez sur le lien ou le symbole de condition pour sélectionner."}, "ttSetDefaultDash": {"heading": "Définir comme tableau de bord par défaut", "body": "Définit la disposition actuelle du tableau de bord par défaut"}, "ttShowHideBtn": {"heading": "Afficher / masquer", "body": "Masque ou affiche partiellement le menu principal."}, "ttSleepCase": {"heading": "Suspendre l'affaire", "body": "Marque le cas comme suspendu. Le cas ne sera plus affiché parmi les tâches actives, mais si nécessaire, il est possible de changer le statut en actif et de terminer le cas entier plus tard."}, "ttSolve": {"heading": "<PERSON><PERSON><PERSON><PERSON>r la tâche", "body": "Affiche le dialogue permettant de démarrer le travail sur la tâche attribuée selon un modèle prédéfini."}, "ttStatePlan": {"heading": "Status", "body": "Définit le statut du plan."}, "ttStatusHdr": {"heading": "Changement de statut de l'en-tête", "body": "L'action est appliquée pour le modèle sélectionné. Des états « actif » et « inactif » sont disponibles. La sélection de l'en-tête se fait en cliquant dans le tableau des en-têtes de modèle."}, "ttStatusTemp": {"heading": "Changement de statut du modèle", "body": "La gestion du cycle de vie du modèle est effectuée en définissant son état. Il existe des états de « en développement », « actif », « inactif » et « supprimé » disponibles. L'action est appliqué pour le modèle sélectionné. La sélection du modèle se fait en cliquant dans le tableau des modèles de cas."}, "ttSubprocess": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Passe à un cas qui a été créé en tant que sous-processus dans le processus du cas affiché."}, "ttTabsButtonMore": {"heading": "Plus", "body": "Affiche plus d'options."}, "ttTakeTsk": {"heading": "Reprendre la tâche", "body": "Permet à la tâche d'être prise en charge par un autre solveur."}, "ttTemps": {"heading": "<PERSON><PERSON><PERSON><PERSON> processus", "body": "Endroit central pour la gestion des modèles de processus. L'accès nécessite le rôle de $PowerUser."}, "ttTiming": {"heading": "Planification", "body": "Entrez le début et la fin de la tâche."}, "ttTsks": {"heading": "Tâches", "body": ""}, "ttUploadSettings": {"heading": "Télécharger", "body": ""}, "ttUserSetting": {"heading": "Paramètres utilisateur", "body": "Configuration des informations de contact de l'utilisateur, des mots de passe d'accès et des préférences de l'utilisateur. Les utilisateurs ayant le rôle de $Administrator peuvent gérer davantage les informations sur leur organisation et les instances de l'application TeamAssistant."}, "ttUsers": {"heading": "Administration des utilisateurs", "body": "Administration centrale des utilisateurs, structure organisationnelle et rôles des utilisateurs. L'accès nécessite le rôle de $Administrator."}, "ttValidation": {"heading": "Validation", "body": "Validez le modèle et affichez toutes les boucles existantes dans le modèle. Notifie les conditions non remplies et les variables inutilisées."}, "ttViewFile": {"heading": "<PERSON><PERSON>", "body": ""}, "ttWakeUpCase": {"heading": "Réveillez-vous", "body": ""}, "ttActivateCase": {"heading": "Activer", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Définir les colonnes DMS par défaut", "body": "Définit l'attribution des colonnes DMS par défaut."}, "ttResetDmsCols": {"heading": "Réinitialiser", "body": "Réinitialiser l'attribution des colonnes DMS."}, "ttRestoreDoc": {"heading": "<PERSON><PERSON><PERSON>", "body": "Restaurer le document supprimé."}, "ttSearchHeader": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "tue": "<PERSON><PERSON>", "type": "Type", "typeOfRepetition": "Type de répétition", "unassignedSolvers": "Selon les solveurs de tâches", "unassignedTaskSolvers": "Solveurs de tâches non attribués", "uncategorized": "Non classé", "unfinishedProcesses": "Cas inachevés", "unknown": "Inconnue", "unknownUser": "Utilisateur inconnu", "unrestricted": "Libre", "unspecified": "Non spécifié", "upload": "Télécharger", "uploadFile": "Télécharger un fichier", "uploadPhoto": "Envoyer la photo", "uploadCsv": "Importer un fichier CSV", "url": "URL", "urlAddress": "Adresse URL", "urlContent": "Contenu de l'URL", "use": "Utiliser", "user": "Utilisa<PERSON>ur", "userByOwnerOfLastTask": "À l'utilisateur choisi par le dernier solveur de la tâche.", "userE": "utilisateur", "userFilters": "Filtres utilisateur", "userLock": "<PERSON><PERSON><PERSON> à clé", "userLockUnlockQ": "Voulez-vous vraiment changer le statut de l'utilisateur {{username}} ?", "userName": "Nom d'utilisateur", "userId": "ID d'utilisateur", "userOrgStruct": "Appartient à l'unité organisationnelle", "userVice": "Suppléant", "userViced": "Suppléé", "users": "Utilisateurs", "usersDeleted": "Supprimé", "validation": "Validation", "value": "<PERSON><PERSON>", "var": "variable", "var-": "Variable –", "varChange": "Le changement de variable sera annoncé à tous les participants au cas", "varTaskMap": "Mappage", "varTemp": "Modèle variable", "variable": "Variable", "variableType": "Type de variable", "vars": "Variables", "varsForMandatory": "Variables à saisie obligatoire", "varsForReading": "Variables de lecture", "varsForWriting": "Variables d'écriture", "vices": "Suppléants", "viewCVFields": "Champs disponibles", "visForOrgStrMembers": "Visible par les membres du groupe organisationnel", "visForRoleMembers": "Visible par les membres avec des rôles", "headerVisForRole": "Cas visible pour les membres avec rôle", "waitForNumOfInputs": "En attente de : (nombre d'entrées)", "waitsFor": "attend", "waitsForAll": "attend tout", "waitsForOne": "attend un", "waitsForSending": "En attente d'envoi", "waitsRunFirst": "attend tout le monde, courant le premier", "wakeUp": "Annuler la suspension", "warning": "Avertissement", "wed": "<PERSON><PERSON><PERSON><PERSON>", "weekIn": "semaine dans", "weekly": "Hebdomadaire", "width": "<PERSON><PERSON>", "withConditions": "Sous conditions", "withoutCond": "Sans condition", "year": "an", "yes": "O<PERSON>", "zip": "Code Postal", "move": "<PERSON><PERSON><PERSON><PERSON>", "alertClosing1": "notification se fermera automatiquement dans :", "inDocuments": "Dans les documents", "inVariables": "Dans Variables", "headerTask": "<PERSON>-tê<PERSON> de tâche", "planName": "Nom du régime", "inBulk": "En masse", "confirmResetDmsColumns": "Voulez-vous vraiment réinitialiser les colonnes DMS ?", "dmsColsUseDef": "Utilisation des paramètres par défaut", "dmsColsUseCust": "Utilisation des paramètres personnalisés", "today": "<PERSON><PERSON><PERSON>'hui", "alrPlanDeleteFailed": "Échec de la suppression du plan.", "notRunning": "Ne pas courrir", "alrLackOfPermsToAddTask": "Vous n'êtes pas autorisé à ajouter la tâche.", "dragTable": "Faire glisser le tableau", "alrDownloadCsvListFailed": "Le téléchargement de la liste des fichiers CSV a échoué.", "alrCsvUploadWrongExtension": "Télécharger uniquement les fichiers avec l'extension *.csv", "addToFav": "Ajouter aux Favoris", "renameItem": "Renommer l'élément", "removeFromFav": "Supprimer des favoris ?", "alrAddedToFav": "Ajouté aux favoris.", "alrRemovedFromFav": "Supprimé des favoris.", "tskSetAssignDues": "Définir des restrictions de temps pour la tâche", "isNot": "n'est pas", "alrTskScheduling": "Planification des tâches...", "alrFavouritesPageExist": "Cette page est déjà dans les favoris.", "alrFavouritesActionExist": "Cette action est déjà dans les favoris.", "alrFavouriteRenamed": "Le nouveau nom a été modifié dans la liste des favoris.", "autoFit": "Ajustement automatique", "passwordIsShort": "Le mot de passe est trop court.", "changeAttrComplCases": "Modifier les attributs des cas terminés", "iterateOverVars": "Itérer sur les variables", "nrOfDecimalDigits": "Nombre de chiffres déci<PERSON>ux", "onlyNumbers": "Seulement les chiffres", "maxNumberOfDecimals": "Le nombre maximum de chiffres décimaux est", "alrInsertCsv": "Ins<PERSON>rez le fichier CSV.", "addBefore": "ajouter avant", "moveBefore": "<PERSON><PERSON><PERSON><PERSON> avant", "administration": "Administration", "ttAdministration": {"heading": "Administration", "body": ""}, "alrLogsLoadFailed": "Échec du chargement des journaux.", "logs": "<PERSON><PERSON><PERSON>", "message": "Message", "useCompatibleTempl": "Utiliser un modèle compatible", "overwriteExistTempl": "Remplacer le modèle existant", "addNewTempl": "Ajouter un nouveau modèle", "import": "Importer", "export": "Exporter", "confirmExportAllTempl": "Exporter tous les modèles ?", "confirmExportSelTempl": "Exporter le modèle sélectionné ?", "newLogs": "Nouveaux journaux", "container": "récipient", "contents": "Contenu", "confirmRemoveDialog": "Voulez-vous vraiment supprimer la {{variable}} ?", "allMyCases": "Tous mes cas", "maintenanceMsg": "Une <span style=\"color: {{color}};\">maintenance</span> programmée est en cours", "alrMaintenanceMsg": "Maintenance planifiée en cours, veuillez réessayer plus tard.", "alrAttachDownloadLackOfPerms": "Vous n'êtes pas autorisé à télécharger le document ou le document est introuvable.", "unableToConnect": "Incapable de se connecter au serveur", "tryLater": "Essayez-le plus tard ou contactez l'administrateur.", "enableTaskDelegation": "Activer la délégation de tâches", "enableRejectTask": "Activer la tâche de rejet", "confirmRejectTask": "Voulez-vous vraiment rejeter la tâche ?", "rejectTask": "<PERSON><PERSON><PERSON>che", "delegateTask": "D<PERSON><PERSON>guer", "alrRejectingTask": "Rejet de la tâche...", "alrTaskRejected": "La tâche a été rejetée.", "alrTaskRejectFailed": "La tâche n'a pas pu être rejetée.", "alrTaskDelegating": "Déléguer la tâche...", "alrTaskDelegated": "La tâche a été déléguée à l'utilisateur :", "alrFailedTaskDelegate": "La délégation de tâche a échoué.", "delegateOnUser": "Déléguer sur l'utilisateur", "plnAssignmentCond": "<PERSON> le champ « Attributions » reste vide, une liste d'initiateurs sera créée en évaluant les conditions restrictives au moment de l'exécution du plan", "alrUserFiltersSettingsFailed": "Échec de l'enregistrement des paramètres des filtres utilisateur.", "general": "Général", "alrUserPhotoLoadFailed": "Le chargement de la photo de l'utilisateur a échoué.", "publicDynTable": "Table dynamique publique", "isFullIndexed": "À la recherche", "datetimeIndexed": "Indexé sur", "toIndex": "Indexer", "toReindex": "<PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>", "solverChanged": "Le solveur a été modifié dans {{count}} tâches", "changeSolverFailed": "La modification du solveur de tâche a échoué.", "alrTikaParsingFailed": "Une erreur s'est produite lors de l'analyse du document.", "alrIndexingFailed": "L'indexation du document a échoué.", "alrTikaNotRunning": "Service d'analyse de documents non disponible.", "alrIndexingServiceNotRunning": "Service d'indexation non disponible.", "alrFulltextNotSet": "Le texte intégral n'a pas été défini.", "asc": "Ascendant", "desc": "Descendant", "restore": "<PERSON><PERSON><PERSON>", "alrLogosLoadFailed": "Le chargement des logos a échoué.", "indexedDocsCount": "dans {{count}} documents", "alrIndexedCountLoadFailed": "La recherche en texte intégral n'est pas disponible actuellement.", "searchAll": "<PERSON><PERSON><PERSON> tout", "searchActual": "Uniquement réel", "runIndexing": "Exécuter l'indexation", "alrDocumentIndexing": "Document d'indexation...", "alrDocumentIndexed": "Le document a été indexé et peut être trouvé en effectuant une recherche.", "alrDocumentIndexedWithMinMetadata": "Le document a été indexé.", "alrDocumentIndexingFailed": "L'indexation du document a échoué.", "changingUserProfileForbidden": "Le changement de profil utilisateur est interdit.", "uploadingPhotoForbidden": "Le téléchargement de photo est interdit.", "alrValidationCalcError": "Erreur de validation des calculs", "maintenance": "Maintenance", "maintenanceActivate": "<PERSON><PERSON> <PERSON>'en<PERSON>tien", "maintenanceInfoText": "Le début et la fin seront affichés aux utilisateurs après l'activation de la maintenance.", "maintenanceMode": "Mode de Maintenance", "alrAvailableCalcFailed": "Les calculs disponibles n'ont pas pu être chargés.", "alrFillDataForSearch": "<PERSON><PERSON><PERSON><PERSON> remplir les paramètres de recherche.", "youAreHere": "Tu es là", "invalidDate": "Format de date invalide", "alrInvalidFileFormat": "Format de fichier invalide.", "alrEnter3characters": "Veuillez entrer au moins trois caractères.", "changeCaseOwner": "Changement de propriétaire du dossier", "actualCaseOwner": "Propri<PERSON><PERSON> réel du dossier", "newCaseOwner": "Nouveau propriétaire du dossier", "alrCaseOwnerChanged": "Le propriétaire du dossier a été modifié.", "alrChangeCaseOwnerFailed": "Échec du changement de propriétaire du dossier.", "alrCsvSaving": "Enregistrement du fichier CSV...", "alrCsvSaveFailed": "Le téléchargement du fichier CSV a échoué.", "alrCsvSaved": "Le fichier CSV a été téléchargé.", "allTemplates": "Tous les modèles", "specifyCaseIds": "Spécifier les ID de cas", "caseIds": "ID de cas", "caseId": "ID de cas", "separBySemicolon": "séparés par un point-virgule", "alrAddCaseIds": "Veuillez spécifier les ID de cas", "headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON><PERSON>", "defaultHeaderName": "Nom d'en-tête par défaut", "headerName": "Nom de l'en-tête", "addHeader": "A<PERSON>ter un en-tête", "editHeader": "Modifier l'en-tête", "templateName": "Nom du modèle", "rolesExecRightsText": "Attribuez des rôles qui pourront initier des cas", "orgUnitsExecRightsText": "Attribuer des unités organisationnelles qui pourront initier des cas", "selectedHeader": "en-tête s<PERSON>", "alrHeaderDeleted": "L'en-tête a été supprimé !", "alrHeaderDeleteFailed": "Échec de la suppression de l'en-tête.", "alrHeaderSaveFailed": "Échec de l'enregistrement de l'en-tête.", "alrHeaderSaved": "L'en-tête a été enregistré.", "alrHeadersLoadFailed": "Le chargement des données d'en-tête a échoué.", "identificator": "Code d'en-tête", "includeDataSimilarProcesses": "Inclure les données de tous les processus similaires", "confirmCopyCv": "Voulez-vous vraiment copier l'aperçu sélectionné ?", "alrCreatingCopyCv": "Création d'une copie de l'aperçu...", "alrCvCopied": "L'aperçu a été copié.", "alrCopyCvFailed": "Échec de la création d'une copie de la vue d'ensemble.", "copyingTemplate": "Copier un modèle", "alrCheckTempImportFailed": "La vérification de l'importation du modèle a échoué.", "warnings": "Mises en garde", "missingEventsFiles": "Fichiers d'événements manquants", "missingEventsFilesText": "Le fichier {{- file}} n'a pas été trouvé dans l'événement {{- event}}", "printsOfTemplates": "Impressions de modèles", "printsOfTemplatesText": "Veuillez faire attention à imprimer {{- print}} à partir du modèle {{- template}}. Valeur : {{- value}}", "dupliciteTaskNames": "Noms de tâche en double", "dupliciteTaskNamesText": "Le modèle {{- template}} contient plusieurs tâches avec le même nom {{- task}} {{- taskId}}, cela provoquera la rupture des liens !", "dynTableUsed": "Table dynamique utilisée", "suspiciousCalc": "Cal<PERSON>ls suspects", "suspiciousCalcText": "Possible rôle/organisation/utilisateur manquant dans le calcul {{- calc}}", "missingEvents": "Événements manquants", "missingEvent": "<PERSON>v<PERSON><PERSON> manquant", "wrongMappingDomains": "Mauvais mappage des domaines", "wrongMappingDomainsText": "La description de la tâche {{- task}} du modèle {{- template}} contient un mauvais nom de domaine, le domaine actuel est {{- actDom}}", "taskDescription": "Description de la tâche", "eventsUrl": "URL des événements", "eventsUrlText": "Erreur possible dans l'URL de l'événement {{- event}}, le domaine actuel est {{- actDom}}", "param": "Paramètre", "alrServiceNotForTable": "Les données de ce service ne sont pas appropriées pour être affichées dans le tableau.", "alrServiceDataFailedLoad": "Les données de service n'ont pas pu être chargées.", "alrServiceNoData": "Le service ne contient aucune donnée.", "tableColumns": "Colonnes du tableau", "datetime": "Date et l'heure", "exactDatetime": "Date et heure exactes", "dashRestNoColumns": "Aucune colonne n'est définie - vous les choisissez dans les paramètres du conteneur", "loadService": "Service de chargement", "useCompatibleRole": "Utiliser un rôle compatible", "overwriteExistRole": "Remplacer le rôle existant", "addNewRole": "Ajouter un nouveau rôle", "templateImportFailed": "L'importation du modèle a échoué.", "templateImport": "Importation de modèles", "templateImportNoData": "Aucune donnée n'a été trouvée pour l'importation du modèle.", "variableImportNoData": "Aucune donnée n'a été trouvée pour l'importation des variables.", "ttTemplateImport": {"heading": "Importation de modèles", "body": "Un dossier contenant les définitions d'un ou plusieurs modèles est sélectionné puis téléchargé."}, "showUnfinishedProcesses": "Afficher les cas inachevés", "expMaintenanceEnd": "Fin de maintenance prévue", "alrScriptSaveFailed": "Échec de l'enregistrement du script.", "editScript": "Modifier le scénario", "addScript": "Ajouter un scénario", "alrRunScript": "Lancement du script...", "alrScriptCompleted": "Scénario terminé.", "alrFailedScriptStart": "Le script n'a pas pu démarrer.", "alrScriptDocsLoadFailed": "La documentation des scripts n'a pas pu être chargée.", "alrScriptLoadFailed": "Échec du chargement des scripts.", "switchAdminUser": "Changer d'admin/utilisateur", "ttSwitchAdminUser": {"heading": "Changer d'admin/utilisateur", "body": ""}, "ttSwitchViewport": {"heading": "Changer de vue mobile/PC", "body": ""}, "alrEventDataLoadFailed": "Les données d'événement n'ont pas pu être chargées.", "alrEventRuleDataLoadFailed": "Échec du chargement des données de la règle d'événement.", "cancellation": "Annulation", "tTaskAutoCancellCaption": "La tâche sera automatiquement annulée si", "codeMirrorHelp": "Cliquez n'importe où dans l'éditeur et appuyez sur Ctrl + Espace pour afficher l'aide.", "codeMirrorHelpJs": "Pour une liste de toutes les fonctionnalités, cliquez sur <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "Ajouter un évènement", "editEvent": "Modifier l'événement", "term": "Terme", "columnOrder": "Ordre des colonnes", "alrLoadEventsButtonsFailed": "Le chargement des boutons dans le tableau a échoué.", "showButtonsCol": "Afficher la colonne d'actions", "button": "Bouton", "enableButtonInTasks": "Afficher sous forme de bouton dans la liste des tâches", "alrEventDoesntExist": "L'événement sélectionné n'existe pas.", "alrEventRuleSaveFailed": "Échec de l'enregistrement de la règle d'événement.", "variableNames": "Noms des variables", "fsEvent": "un événement", "alrEventDeleteFailed": "Échec de la suppression de l'événement.", "fsRule": "<PERSON><PERSON><PERSON>", "alrRuleDeleteFailed": "La suppression de la règle a échoué.", "alrRuleStatusChangeFailed": "La modification de l'état de la règle a échoué.", "ruleActivateDeactivateQ": "Voulez-vous vraiment modifier le statut de la règle ?", "docUploadedPrivate": "Le document sera téléchargé en privé", "fileOwner": "Proprié<PERSON> fi<PERSON>er", "planOk": "D'accord", "userNotAuthToStartTempl": "L'utilisateur n'est pas autorisé à démarrer un dossier avec ce modèle", "planStartDate": "Date de début", "useCompatibleEvent": "Utiliser un événement compatible", "overwriteExistEvent": "Remplacer l'événement existant", "addNewEvent": "Ajouter un nouvel événement", "useCompatibleUser": "Utiliser un utilisateur compatible", "overwriteExistUser": "Remplacer l'utilisateur existant", "addNewUser": "Ajouter un nouvel utilisateur", "useOnlyFutureDates": "Uniquement les dates futures", "alrGenerateHtmlFailed": "La génération de HTML a échoué.", "alrNoPermsToAddNoteInVice": "Vous n'avez pas l'autorisation d'ajouter une note lors de la suppléance.", "alrNoPermsToAddDocInVice": "Vous n'avez pas l'autorisation d'ajouter un document lors de la suppléance.", "current": "<PERSON><PERSON><PERSON>", "indexation": "Indexation", "attemptToRestoreConnection": "Essayez de rétablir la connexion dans", "loginWillExpire": "La connexion expirera dans", "unsavedDataWillBeLost": "Les données non enregistrées seront perdues.", "alrFileSaveLikeAttachViceError": "Vous n'avez pas l'autorisation d'enregistrer une impression en tant que document de cas lors de la suppléance !", "alrFileSaveLikeAttachStoreError": "Échec de l'enregistrement de l'impression en tant que document dans le cas.", "useCompatibleUnit": "Utilisez une unité org. compatible.", "overwriteExistUnit": "Remplacer l'unité org. existante.", "addNewUnit": "Ajouter une nouvelle unité org.", "addNewDynTable": "Ajouter une nouvelle table dyn.", "useCompatibleDynTable": "Utilisez table dyn. compatible.", "enterDiffNameRoot": "Veuillez entrer un nom différent de Root.", "ttTemplatesExport": {"heading": "Modèles d'exportation", "body": "Exportez les modèles sélectionnés vers un dossier. Il est possible de choisir le nom et l'emplacement du fichier exporté. L'action est appliquée au modèle sélectionné. Sélectionnez un modèle en cliquant dessus dans le tableau des modèles de cas."}, "ttTemplatesExportAll": {"heading": "Exporter tous les modèles", "body": "Exportez tous les modèles actuellement affichés dans un fichier. Il est possible de choisir le nom et l'emplacement du fichier exporté. La sélection de modèles peut être restreinte en définissant des conditions de filtre appropriées."}, "exportAll": "Tout exporter", "noTemplatesToExport": "Aucun modèle à exporter.", "skip": "Sauter", "ttSkipTemplate": {"heading": "Ignorer le modèle", "body": "Ignore l'importation du modèle actuel et affiche le suivant."}, "alrInvalidImportData": "Données d'importation invalides", "alrUsersNotLoaded": "Les utilisateurs n'ont pas réussi à charger.", "caseOverview": "Aperçu du cas", "alrRolesNotLoaded": "Échec du chargement des rôles.", "changeLang": "Changer de langue", "reactivatesPlan": "réactive le plan", "alrOrgUnitsNotLoaded": "Les unités org. n'ont pas pu être chargées.", "refreshPage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stayLogged": "<PERSON><PERSON> connect<PERSON>", "showTime": "Afficher l'horodatage dans les aperçus", "managerIn": "gestionnaire dans {{orgUnit}}", "usageStats": "Statistiques d'utilisation", "month": "<PERSON><PERSON>", "alrUsageStatsLoadFailed": "Impossible de charger les statistiques d'utilisation.", "accessLog": "Journal d'accès", "durationInMs": "<PERSON><PERSON><PERSON> (ms)", "task": "<PERSON><PERSON><PERSON>", "operation": "Opération", "active_users": "Nombre d'utilisateurs actifs", "active_template_processes": "Nombre de processus actifs", "active_headers": "Nombre d'en-têtes actifs", "active_users_able_to_create_a_process": "Nombre d'utilisateurs actifs pouvant exécuter un processus", "users_that_solved_a_task": "Nombre de solveurs, qui ont résolu au moins une tâche", "solvers_or_can_create_a_process": "Nombre d'utilisateurs qui ont résolu une tâche ou peuvent exécuter un processus", "mobile_app_paired_users": "Nombre d'utilisateurs app mobile jumelés", "calculationsLogs": "Journaux de calculs", "translatedScript": "Scénario traduit", "originalScript": "Scénario original", "tskId": "ID de tâche", "alrCalculationsDocsLoadFailed": "Impossible de charger les documents de calculs.", "alrCalculationsValidationFailed": "La validation des calculs a échoué.", "linkPriority": "Priorité de lien", "dateFormat": "JJ/MM/AAAA", "alrConvertErrorJsonNeon": "Erreur lors de la conversion de json -> néon.", "alrInvalidData": "Donn<PERSON> invalides.", "sharedVar": "Variable partagée", "guide": "Guide", "guideFs": "guide", "guides": "Guides", "alrGuidesLoadFailed": "Les guides n'ont pas pu être chargés.", "language": "<PERSON><PERSON>", "default": "Défaut", "next": "Suivant", "previous": "Précédent", "targetElementNotFound": "Élément cible introuvable", "documentation": "Documentation", "matchesRegular": "Ne correspond pas à la règle", "secondAllowedValues": "valeurs autorisées ; 0 est le début de la minute", "everySec": "chaque seconde", "listOfSec": "une liste de secondes ; c'est à dire. 0,30 serait les 0 ET 30ème secondes", "rangeOfSec": "une plage de secondes ; c'est à dire. 0–5 seraient les secondes 0, 1, 2, 3, 4 et 5 (vous pouvez également spécifier une liste de plages 0–5, 30–35)", "slashSec": "les valeurs de pas sauteront le nombre spécifié dans une plage ; c'est-à-dire */5 est toutes les 5 secondes, et 0–30/2 est toutes les 2 secondes entre 0 et 30 secondes", "minuteAllowedValues": "valeurs autorisées ; 0 est le début de l'heure", "everyMin": "chaque minute", "listOfMin": "une liste de procès-verbaux ; c'est à dire. 0,30 serait les 0 ET 30ème minutes", "rangeOfMin": "une plage de minutes ; c'est à dire. 0–5 serait les minutes 0, 1, 2, 3, 4 et 5 (vous pouvez également spécifier une liste de plages 0–5, 30–35)", "slashMin": "les valeurs de pas sauteront le nombre spécifié dans une plage ; c'est-à-dire */5 est toutes les 5 minutes, et 0–30/2 est toutes les 2 minutes entre 0 et 30 minutes", "hourAllowedValues": "valeurs autorisées ; 0 est minuit", "everyHour": "Toutes les heures", "listOfHour": "une liste d'heures; c'est à dire. 0,12 serait les 0 ET 12ème heures", "rangeOfHour": "une plage d'heures ; c'est à dire. 19–23 seraient les heures 19, 20, 21, 22 et 23 (vous pouvez également spécifier une liste de plages 0–5, 12–16)", "slashHour": "les valeurs de pas sauteront le nombre spécifié dans une plage ; c'est-à-dire */4 est toutes les 4 heures, et 0–20/2 est toutes les 2 heures entre 0 et la 20e heure", "dayAllowedValues": "valeurs autorisées", "everyMonthDay": "tous les jours du mois", "listOfDay": "une liste de jours; c'est à dire. 1,15 serait le 1er ET le 15ème jour du mois", "rangeOfDay": "une plage de jours ; c'est à dire. 1 à 5 seraient les jours 1, 2, 3, 4 et 5 (vous pouvez également spécifier une liste de plages 1 à 5, 14 à 30)", "slashDay": "les valeurs de pas sauteront le nombre spécifié dans une plage ; c'est-à-dire */4 est tous les 4 jours, et 1–20/2 est tous les 2 jours entre le 1er et le 20e jour du mois", "allowedValues": "valeurs autorisées", "everyMonth": "chaque mois", "listOfMonth": "une liste de mois; c'est à dire. 1,6 serait le janvier ET juin", "rangeOfMonth": "une plage de mois ; c'est à dire. 1–3 serait janvier, février et mars (vous pouvez également spécifier une liste de plages 1–4, 8–12)", "slashMonth": "les valeurs de pas sauteront le nombre spécifié dans une plage ; c'est-à-dire */4 est tous les 4 mois, et 1–8/2 est tous les 2 mois entre janvier et août", "weekAllowedValues": "valeurs autorisées ; 0=dimanche, 1=lundi, 2=mardi, 3=mercredi, 4=jeudi, 5=vendredi, 6=samedi", "everyWeekDay": "<PERSON><PERSON> jour de la sema<PERSON>", "listOfWeekDay": "une liste de jours; c'est à dire. 1,5 serait le lundi ET le vendredi", "rangeOfWeekDay": "une plage de jours ; c'est à dire. 1–5 serait lun, mar, mer, jeu et ven (vous pouvez également spécifier une liste de plages 0–2, 4–6)", "slashWeek": "les valeurs de pas sauteront le nombre spécifié dans une plage ; c'est-à-dire */4 est tous les 4 jours, et 1–5/2 est tous les 2 jours entre le lundi et le vendredi", "contrab": "Cron {{variable}} champ", "cSecond": "seconde", "cMinute": "minute", "cHour": "heure", "cDay": "journ<PERSON>", "cMonth": "mois", "cWeekDay": "jour de la semaine", "seconds": "secondes", "minutes": "minutes", "hours": "les heures", "days": "jours", "weeks": "semaines", "socketOk": "Le tableau contient les dernières données", "socketBroken": "Restaurer la connexion pour une mise à jour continue des données", "newTask": "Nouvelle tâche", "report": "Signaler", "ttCaseReport": {"heading": "Reporter", "body": ""}, "usersRights": "Droits de l'utilisateur", "visPerRole": "Visibilité par rôle", "manualEvents": "Événements manuels", "noTasks": "Pas de nouvelles tâches", "emptyFavs": "La liste des favoris est vide", "crons": "Crons", "cronsHistory": "Histoire de crons", "redirBefStart": "Avant de commencer, redirigez vers", "lastRun": "Dernier tour", "nextRun": "Prochaine exécution", "syntax": "Syntaxe", "alias": "<PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "restart": "<PERSON><PERSON><PERSON><PERSON>", "restartCronProcess": "Redémarrer le contexte du processus", "ttRestartCron": {"heading": "Redé<PERSON><PERSON> le cron", "body": ""}, "ttRestartCronProcess": {"heading": "Redémarrer le processus", "body": ""}, "ttResetCron": {"heading": "Réinitialiser le cron", "body": ""}, "ttRunCron": {"heading": "Exécutez le cron", "body": ""}, "ttStopCron": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttStatusCron": {"heading": "Statut", "body": ""}, "alrCronStopped": "Le cron a été arrêté.", "alrCronStopFailed": "La requête d'arrêt du cron a échoué.", "alrCronRunning": "Le cron a été lancé.", "alrCronRunFailed": "L'exécution cron a échoué.", "alrCronReset": "Le cron a été réinitialisé par défaut.", "alrCronResetFailed": "La réinitialisation du cron a échoué.", "alrCronRestart": "Le cron a été redémarré.", "alrCronRestartFailed": "La demande de redémarrage du cron a échoué.", "alrCronUpdated": "Le cron a été enregistré avec succès.", "alrCronUpdateFailed": "La demande de mise à jour cron a échoué.", "confirmRunCronDialog": "Êtes-vous sûr de vouloir exécuter le cron sélectionné ?", "confirmStopCronDialog": "Voulez-vous vraiment arrêter le cron sélectionné ?", "confirmResetCronDialog": "Êtes-vous sûr de vouloir rétablir les paramètres d'usine de cron ?", "confirmRestartCronDialog": "Voulez-vous vraiment redémarrer le cron sélectionné ?", "confirmUpdateCronDialog": "Voulez-vous vraiment modifier le statut de cron ?", "alrProcessRestart": "Le processus de cron a été redémarré !", "alrProcessRestartFailed": "La demande de redémarrage du processus a échoué.", "confirmRestartProcessDialog": "Êtes-vous sûr de vouloir redémarrer l'intégralité du processus de cron ? Attention, redémarrage complet de tous les crons et tout le contexte se produira.", "cronParams": "Paramètres", "alrPresetLogFiltersLoadFailed": "Les filtres de journal prédéfinis n'ont pas pu être chargés.", "timeRange": "Intervalle de temps", "presetFilters": "Filtres prédéfinis", "params": "Paramètres", "authentication": "Authentification", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Le nom du paramètre", "authMethod": "Méthode d'authentification", "taskAlreadyEdited": "La tâche est déjà en cours de modification par un autre utilisateur.", "taskEditedByAnotherUser": "Un autre utilisateur a commencé à modifier la tâche.", "tempAlreadyEdited": "Le modèle est déjà en cours de modification par un autre utilisateur.", "tempEditedByAnotherUser": "Un autre utilisateur a commencé à modifier le modèle.", "test": "Test", "notInRightormat": "Format invalide", "ttTableExportExcel": {"heading": "Tableau d'exportation", "body": "Exporte le tableau vers le fichier xlsx"}, "ttTableExportCsv": {"heading": "Tableau d'exportation", "body": "Exporte le tableau vers le fichier csv"}, "searchInSuspended": "Recherche aussi dans les cas suspendus", "alrScriptDocsFailed": "Impossible d'enregistrer la documentation du script.", "currentlyRunning": "En cours d'exécution", "onStart": "Au démarrage", "onEnd": "À la fin", "onHand": "Par la main", "onRecalc": "<PERSON><PERSON> du recalcul", "onPull": "Avant de prendre le relais", "yesterday": "<PERSON>er", "tomorrow": "<PERSON><PERSON><PERSON>", "replyRecipient": "Destinataire de la réponse", "bcRecipient": "Destinataire de la copie cachée", "copyRecipient": "<PERSON><PERSON><PERSON> le destinataire", "emptyHe": "Vider", "archivedLogs": "Journaux archivés", "basicMode": "Mode simple", "expertMode": "Mode expert", "ttBasicMode": {"heading": "Mode simple", "body": "Masque certains éléments ou options du formulaire."}, "ttExpertMode": {"heading": "Mode expert", "body": "Affiche les éléments ou options masqués dans le formulaire."}, "helpOverviewFolder": "V<PERSON> pouvez inclure l'aperçu dans la structure du répertoire en utilisant des barres obliques.<br /><i>(par exemple, Factures/Toutes les factures reçues)</i>", "helpOverviewIncludeSimilar": "Si vous sélection<PERSON>, les cas des autres en-têtes d'un modèle seront également affichés.", "helpOverviewSysVars": "Les champs marqués d'un (sys) sont des champs système qui font partie de chaque processus.", "customization": "Personnalisation", "elementColor": "Couleur de l'élément", "fontColor": "Couleur de la police", "fontSize": "Taille de police", "bold": "Gras", "cursive": "Italique", "off": "Désactivé", "toPlan": "Plan", "alrMaintenanceComing": "À {{time}} la maintenance planifiée du système commencera. Veuillez enregistrer votre travail.", "timeoutHMS": "Délai : (hh:mm:ss)", "eventw": "La tâche « {{task}} » du modèle « {{template}} » attend cet événement", "waitsForEventTip": "Le cas attend un événement : « {{event}} »", "copyToMultiinstances": "Copier vers plusieurs instances", "showAsPreview": "Afficher un aperçu", "alrPreviewAttachmentsFailed": "L'affichage d'un aperçu a échoué", "alrPreviewAttachmentsWrongFormat": "L'affichage d'un aperçu a échoué - format de fichier non pris en charge", "previewNotAvailable": "L'aperçu du document n'est pas possible en raison du type de document.", "configuration": "Configuration", "values": "Valeurs", "defaultValues": "Les valeurs par défaut", "ttSubscribeCv": {"heading": "Présentation de l'abonnement", "body": "L'aperçu sélectionné vous sera envoyé par e-mail tous les jours de la semaine à l'heure définie."}, "subscribe": "<PERSON>'abonner", "time": "Temps", "externalLang": "Langue externe", "hdrStatusQ": "Voulez-vous vraiment changer le statut de l'en-tête ?", "small": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "large": "Grande", "alrTemplTsksLoadFailed": "Les tâches du modèle n'ont pas pu être chargées.", "applyInTasks": "Appliquer dans les tâches", "caseStatuses": "Statuts des cas", "statuses": "Statuts", "Manuals": "<PERSON><PERSON>", "forReading": "À lire", "forReadWrite": "Pour lire et écrire", "addVersion": "Nouvelle version", "size": "<PERSON><PERSON>", "prevWorkDay": "Jour de travail précédent", "ttCreateTempVersion": {"heading": "<PERSON><PERSON>er une nouvelle version du modèle", "body": ""}, "version": "Version", "alrTempVersionsLoadFailed": "Échec du chargement des versions de modèle.", "alrChangeTempVersionFailed": "Échec de la modification de la version du modèle.", "alrCreateTempVersionFailed": "La création d'une nouvelle version du modèle a échoué.", "confirmCreateTempVersion": "Voulez-vous vraiment créer une nouvelle version du modèle ?", "applyInAllTasks": "Appliquer dans toutes les tâches", "mandatoryVar": "Variable requise", "emptyRequiredVarMessage": "Oups, variable requise vide", "duration": "<PERSON><PERSON><PERSON>", "alrDynConditionsFailed": "Impossible de terminer la tâche. Veuillez essayer d'actualiser la page ou contactez l'administrateur ou le service d'assistance.", "caseActivation": "Activation du cas", "average": "<PERSON><PERSON><PERSON>", "performanceLogs": "Journaux de performances", "displayingOverview": "Affichage de la vue d'ensemble", "taskSolve": "<PERSON><PERSON><PERSON> la tâche", "displayingCO": "Affichage de la VUE D'ENSEMBLE DU CAS", "printCreation": "Création d'impression", "entityId": "ID d'entité", "copyTask": "<PERSON><PERSON><PERSON> la tâche", "checkProcessCompletion": "Vérification de l'achèvement du processus", "findingSolver": "Trouver un solveur", "publicFiles": "Fichiers publics", "usage": "Usage", "serviceConsole": "Console de services", "selectAll": "<PERSON><PERSON>", "logos": "Logos", "overviewWithTasks": "Aperçu avec tâches", "printIsReady": "L'impression est prête", "alrChangelogLoadFailed": "Échec du chargement du journal des modifications.", "inJs": "En script", "ttCopyDtDefinition": {"heading": "Copier la définition de table", "body": "Copie la définition de la table dynamique sélectionnée."}, "confirmCopyTableDefinition": "Voulez-vous vraiment copier la définition de table ?", "alrCopying": "Copier...", "alrCopyFailed": "Échec de la copie.", "fallback": "Se retirer", "syncEnabled": "Synchronisation", "systemGuideNote": "Le contenu du Guide système ne peut pas être modifié. Pour afficher d'autres contenus, rendez le guide système inactif et copiez son contenu dans un nouveau guide.", "alrAnotherUserLogged": "Un autre utilisateur est connecté dans une autre fenêtre !", "userLocked": "L'utilisateur est verrouillé", "visInternalUserOnly": "Visible uniquement pour les utilisateurs internes", "showSelectedOnly": "Afficher uniquement la sélection", "clickToSelect": "Cliquez pour sélectionner", "restrictRoleAssignment": "Restreindre l'attribution de rôle pour le rôle", "restrictions": "Restrictions", "restrictTableHandling": "Restreindre la manipulation des tables", "toRole": "R<PERSON><PERSON>", "inCalcToHeader": "Dans les calculs à l'en-tête", "loginBtnColor": "Couleur du bouton de connexion", "certificates": "Certificats", "certificate": "Certificat", "certificateVar": "certificat", "tspSources": "Horodatages", "tspSource": "Horodatage", "confirmExpandDynRowsNewAssignments": "Attention, nouvelle attribution ! Les variables n'ont pas d'axe défini. Voulez-vous étirer toutes les lignes dynamiques ?", "confirmExpandDynRows": "Voulez-vous vraiment étirer toutes les lignes dynamiques ?", "expandDynRows": "<PERSON><PERSON>rer les lignes dynamiques", "visible": "Visible", "cvcDbColumn": "Colonne Source", "cvTableSource": "Tableau des sources", "uploadedFromFile": "Téléchargé à partir du fichier", "appStatus": "État de l'application", "loadAll": "Charger tout", "ignoredUsers": "Utilisateurs ignorés", "copyRolesFrom": "<PERSON><PERSON>r les rôles de", "disableFrontendStyles": "Ne pas appliquer de styles automatiques", "activate": "Activer", "confirmActivateCase": "Voulez-vous vraiment activer la coque ?", "alrLackOfPerms": "Manque d'autorisations.", "alrSending": "Envoi en cours...", "sequences": "Séquences", "seqName": "Nom de la séquence", "seqId": "ID de séquence", "seqLastRead": "<PERSON><PERSON><PERSON> lecture", "ttCopyRole": {"heading": "<PERSON><PERSON><PERSON> rô<PERSON>", "body": "Crée une copie du rôle sélectionné."}, "fromCase": "De cas", "includingData": "Y compris les données", "updateInstances": "Mettre à jour les variables d'instance", "addNewCalcScript": "Ajouter un nouveau scénario", "useCompatibleCalcScript": "Utiliser un script compatible", "choose": "Choi<PERSON>", "valueChange": "Changement de valeur", "externalSource": "Source d'utilisateurs", "reports": "Rapports", "confirmCopyReport": "Voulez-vous vraiment copier le rapport sélectionné ?", "graphs": "Graphiques", "aggregation": "Agrégation", "graphNew": "Graphique – nouveau", "confirmCopyGraph": "Voulez-vous vraiment copier le graphique sélectionné ?", "alrCopyGraphFailed": "Échec de la copie du graphique.", "label": "<PERSON><PERSON><PERSON><PERSON>", "pie": "Tarte", "line": "Doubler", "dot": "Points", "bar": "Bar", "barGroups": "Bar – groupes", "alrFailedGraphData": "Échec du chargement du graphique.", "graphSetSharing": "Définir le partage de graphique pour chaque groupe d'utilisateurs", "alrGraphPointsLoadFailed": "Le chargement des points du graphique a échoué.", "alrGraphNotFound": "Graphique introuvable.", "graphData": "Données graphiques", "pointsData": "Points du graphique", "alrGraphSaveFailed": "L'enregistrement du graphique a échoué !", "graphPoint": "Point de graphique", "noOrder": "Pas de tri", "refreshGraph": "Actualiser le graphique", "viewSwitcher": "Filtre global", "axisXglobalFilter": "Axe X – filtre global", "axisXgroups": "Axe X – groupes", "axisXdata": "Axe X – données", "axisYvalues": "Axe Y – valeurs", "axisYcolors": "Axe Y – couleurs", "hrAgenda": "Agenda RH", "userChange": "Changement d'utilisateur", "newUser": "Nouvel utilisateur", "usersCount": "Nombre d'utilisateurs", "confirmChangeUser": "Voulez-vous vraiment changer d'utilisateur ?", "businessVariable": "variable d'entreprise", "casesCount": "Les cas comptent", "selected": "<PERSON><PERSON>", "selectedOnly": "Sélectionné uniquement", "addCaseRightNewUser": "Ajouter un accès au dossier", "visFromTaskToPull": "Visibilité d'une tâche à extraire", "toChangeConfigInfo": "Pour changer, supprimez la valeur du fichier local.js", "clickToChange": "Cliquez pour changer", "currentValue": "Valeur actuelle", "sign": "<PERSON>e", "validationProtocols": "Validations", "plannedEvents": "Événements", "elArchiv": "Archives électroniques", "deletedDocs": "Supprimé", "signatures": "Signatures", "ttArchive": {"heading": "Archives électroniques", "body": ""}, "ttAddToZea": {"heading": "Ajouter aux archives électroniques", "body": ""}, "ttRemoveFromZea": {"heading": "Retirer des archives électroniques", "body": ""}, "ttZeaInfo": {"heading": "Validation", "body": ""}, "ttSignZea": {"heading": "Signe externe", "body": ""}, "addToZea": "Ajouter aux archives électroniques", "removeFromZea": "Supprimer des archives électroniques", "reTimestampAfter": "Validité de l'horodatage généré (jours)", "alrLoadFailed": "Le chargement a échoué.", "replace": "<PERSON><PERSON>lace<PERSON>", "expireAt": "Va expirer", "result": "Résultat de validation", "validatedAt": "<PERSON><PERSON><PERSON>", "refType": "Objet", "eventType": "Type d'action", "errorMessage": "Message d'erreur", "errorTimestamp": "Horodatage de l'erreur", "errorCount": "Nombre d'erreurs", "inFuture": "<PERSON><PERSON> le futur", "path": "Chemin d'accès au fichier de signature", "signedAt": "Création de signature", "dropZoneZeaCertificate": "Déposez un certificat ici ou cliquez pour sélectionner un fichier à télécharger.", "authType": "Type d'identification", "basic": "Avec nom et mot de passe", "byCert": "Par certificat", "alrMissingCertFile": "Télécha<PERSON>z le certificat, s'il vous plaît.", "replaceTo": "Remplacer par", "autoReTimestamp": "Horodatage automatique", "validate": "Valider", "lastUse": "Dernière génération", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour à", "certificateId": "ID de certificat", "expectedCreationTime": "<PERSON><PERSON>", "nextTSPSourceId": "ID d'horodatage suivant", "reTimestampAt": "Horodatage suivant", "timestampedAt": "<PERSON>nier horodatage", "level": "Niveau", "signatureB": "Signature de base", "signatureT": "Signature avec horodatage", "signatureLt": "Signature avec certificats de données à long terme", "signatureLta": "Signature avec données à long terme et horodatage d'archivage", "packaging": "Emballage", "enveloped": "Enveloppé", "enveloping": "Enveloppant", "detached": "Détaché", "algorithm": "Algorithme", "uploadAsRevision": "Télécharger en tant que révision", "externalDisable": "Actif uniquement pour les signatures de lot", "addToDms": "Ajouter au DMS", "autoConvert": "Convertir automatiquement", "format": "Format", "signatureType": "Type de signature", "signature": "Signature", "custom": "Coutume", "batchSignDisabled": "<PERSON><PERSON><PERSON>", "tasId": "ID du document", "hashValue": "<PERSON><PERSON> de ha<PERSON>ge", "hashType": "Type de fonction de hachage", "confirmAddToArchive": "Voulez-vous vraiment ajouter à l'archive ?", "independentSignature": "Signature indépendante", "independentValidation": "Validation indépendante", "failureTrue": "Avec erreur", "failureFalse": "Sans fautes", "confirmValidateDialog": "Voulez-vous vraiment valider la signature ?", "confirmRestartDialog": "Voulez-vous vraiment réinitialiser les erreurs d'événement ?", "verificationResult": "Résultat de la vérification", "integrityMaintained": "Intégrité maintenue", "signatureFormat": "Format des signatures", "internalTimestampsList": "Liste des horodatages internes", "signers": "Signataires", "exhibitedBy": "Exposé par", "signedBy": "Signé par", "validFrom": "Valide à partir de", "validUntil": "Valable jusque", "signitureType": "Type de signature", "signatureQualification": "Qualification de signature", "signatureNoTimestamps": "La signature ne contient pas d'horodatage", "electronicSignature": "Signature électronique", "electronicSeal": "Scellé électronique", "webSiteAuthentication": "Authentification du site Web", "QCP-n": "QCP-n : politique de certification pour les certificats qualifiés UE délivrés aux personnes physiques", "QCP-l": "QCP-l : politique de certification pour les certificats qualifiés UE délivrés aux personnes morales", "QCP-n-qscd": "QCP-n-qscd : politique de certification pour les certificats qualifiés de l'UE délivrés à des personnes physiques avec une clé privée liée à la clé publique certifiée dans un QSCD", "QCP-l-qscd": "QCP-l-qscd : politique de certification pour les certificats qualifiés de l'UE délivrés à des personnes morales avec une clé privée liée à la clé publique certifiée dans un QSCD", "QCP-w": "QCP-w : politique de certification pour les certificats d'authentification de site Web qualifiés de l'UE", "formOfReStamping": "Forme de re-tamponnage", "individually": "Individuellement", "archiveAsPdf": "Archiver au format PDF", "couldNotBeVerified": "Impossible de vérifier", "uses": "Nr. d'utilisations", "countOfSignedDocuments": "Nombre de documents signés", "batchSignature": "Signature de lot", "standaloneSign": "Signature individuelle", "validateSignature": "Validation des signatures", "validateDoc": "Validation des documents", "containsSignature": "Contient la signature", "reStamping": "Ré-estampage", "individualSignatures": "Signatures individuelles", "signatureLevel": "Niveau signature", "simpleReport": "Rapport simple", "detailedReport": "Rapport détaillé", "diagnosticReport": "Rapport diagnostique", "etsiReport": "Rapport ETSI", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "<PERSON><PERSON><PERSON>", "INDETERMINATE": "Indéterminé", "FORMAT_FAILURE": "La signature ne respecte pas l'une des normes de base", "HASH_FAILURE": "Le hachage de l'objet de données signé ne correspond pas au hachage de la signature", "SIG_CRYPTO_FAILURE": "La signature n'a pas pu être vérifiée avec la clé publique du signataire", "REVOKED": "Le certificat de signature a été révoqué et il existe des preuves que la signature a été créée après la révocation", "SIG_CONSTRAINTS_FAILURE": "Un ou plusieurs attributs de signature ne correspondent pas aux règles de validation", "CHAIN_CONSTRAINTS_FAILURE": "La chaîne de certificats utilisée dans le processus de validation n'est pas conforme aux règles de validation des certificats", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "L'ensemble de certificats disponibles pour la vérification de chaîne a provoqué une erreur pour une raison non spécifiée", "CRYPTO_CONSTRAINTS_FAILURE": "L'un des algorithmes de vérification de signature est inférieur au niveau de sécurité cryptographique requis et la signature a été acquise après la durée de vie de l'algorithme", "EXPIRED": "La signature a été créée après l'expiration du certificat de signature", "NOT_YET_VALID": "L'heure de signature est antérieure à la date d'émission du certificat de signature", "POLICY_PROCESSING_ERROR": "Le fichier de stratégie de validation n'a pas pu être traité", "SIGNATURE_POLICY_NOT_AVAILABLE": "Le document électronique contenant les détails de la politique de validation n'est pas disponible", "TIMESTAMP_ORDER_FAILURE": "Les restrictions dans l'ordre d'horodatage des signatures ne sont pas respectées", "NO_SIGNING_CERTIFICATE_FOUND": "Le certificat de signature ne peut pas être identifié", "NO_CERTIFICATE_CHAIN_FOUND": "Aucune chaîne de certificat n'a été trouvée pour le certificat de signature identifié", "REVOKED_NO_POE": "Le certificat de signature a été révoqué à la date/heure de validation. Cependant, l'algorithme de vérification de signature ne peut pas détecter que l'heure de signature est antérieure ou postérieure à la période de révocation", "REVOKED_CA_NO_POE": "Au moins une chaîne de certificats a été trouvée, mais un certificat CA temporaire a été révoqué", "OUT_OF_BOUNDS_NOT_REVOKED": "Le certificat de signature a expiré ou n'est pas encore valide à la date/heure de validation et l'algorithme de validation de signature ne peut pas vérifier que l'heure de signature se situe dans l'intervalle de validité du certificat de signature. Le certificat est connu pour ne pas être révoqué.", "OUT_OF_BOUNDS_NO_POE": "Le certificat de signature a expiré ou n'est pas encore valide à la date/heure de vérification", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "L'un des algorithmes de vérification de signature est en dessous du niveau de sécurité cryptographique requis et il n'y a aucune preuve qu'il a été produit avant que l'algorithme/la clé ne soit considéré(e) comme sécurisé(e)", "NO_POE": "Il n'y a aucune preuve que l'objet signé a été créé avant un événement compromettant", "TRY_LATER": "Toutes les règles de validation ne peuvent pas être satisfaites avec les informations disponibles, mais il peut être possible de le faire avec des informations de révocation supplémentaires qui seront disponibles ultérieurement.", "SIGNED_DATA_NOT_FOUND": "Les données signées ne peuvent pas être obtenues", "GENERIC": "Autre raison", "signatureFile": "<PERSON><PERSON><PERSON>", "validityDays": "Jours de validité", "qualifiedHe": "Qualifié", "qualifiedIt": "Qualifié", "unqualifiedHe": "Non-qualifié", "unqualifiedIt": "Non-qualifié", "timeValid": "Temps valide", "reason": "<PERSON>son", "inTime": "à l'heure", "certificateQualification": "Certificat de qualification", "guaranteedHe": "<PERSON><PERSON><PERSON>", "guaranteedIt": "<PERSON><PERSON><PERSON>", "fromQualifiedCert": "De cert qualifié.", "basedOnQualifiedCertHe": "Basé sur un certificat qualifié", "createdByQualifiedResHe": "Créé par une ressource qualifiée", "basedOnQualifiedCertIt": "Basé sur un certificat qualifié", "createdByQualifiedResIt": "Créé par une ressource qualifiée", "qualification": "Qualification", "confirmRemoveFromZeaDialog": "Voulez-vous vraiment supprimer la {{variable}} de l'archive électronique ?", "noValidationReports": "Aucun rapport de validation", "noSignatures": "Pas de signatures individuelles", "isHigherOrEqualThan": "Doit être supérieur ou égal à", "isInZea": "Dans les archives électroniques", "startStamping": "Commencez à tamponner", "reTimestampAfterMinutes": "minutes", "reTimestampAfterDays": "jours", "reTimestampAfterAll": "Validité de l'horodatage généré", "refId": "ID d'objet", "docWithoutAutoTimestampInfo": "Le document sera signé une seule fois, sans insertion automatique d'horodatage.", "validationReports": "Historique des validations", "docPath": "Chemin d'accès au document", "addToArchiveInvalidSignatureError": "Le fichier n'a pas pu être archivé car il contient une signature qui ne peut pas être vérifiée.", "signImmediately": "<PERSON>ez immédiatement", "replaceInConfiguration": "Remplacer dans la configuration", "cancel": "Annuler", "bulk": "Masse", "bulkCompletion": "Achèvement en masse", "enableBulkCompletion": "Activer l'achèvement en bloc", "confirmCompleteTasks": "Voulez-vous vraiment terminer les tâches ?", "plannedMaintenance": "Maintenance planifiée", "notSpecified": "Non précisé", "bulkCompletionVars": "Variables d'achèvement en masse", "alrBulkCompletionMultiTypeErr": "Seules les tâches du même type peuvent être complétées en masse, vous pouvez utiliser un filtre.", "notifications": "Notifications", "alrTskAlreadyTakenSomeone": "Quelqu'un d'autre s'est déjà chargé de la tâche.", "alrTskAlreadyTaken": "La tâche a déjà été prise en charge.", "downloadBpmn": "télécharger le diagramme BPMN", "downloadSvg": "télécharger en tant qu'image SVG", "displayForm": "Type d'affichage", "selectedPreview": "l'aperçu s'affiche", "fixedHeight": "Hauteur fixe (px)", "lastVersion": "Dernière version", "separatedPreview": "Aperçu séparé", "defaultZoom": "Zoom par défaut", "fixedPosition": "Emploi stable", "percentInterval": "Veuillez remplir un entier entre 0 et 5", "notPositiveNumber": "Veuillez ne remplir que des nombres positifs", "zoomDown": "Zoom vers le bas", "zoomUp": "<PERSON>mer", "rotate": "<PERSON><PERSON>", "logTooBig": "Le journal est trop volumineux pour être rendu.", "downloadLog": "Télécharger le journal", "confirmCopyCron": "Voulez-vous vraiment copier le cron sélectionné ?", "ttCopyCron": {"heading": "<PERSON><PERSON><PERSON> le cron", "body": ""}, "onlyWorkingDays": "Seuls les jours ouvrables", "datesDisabled": "Désactiver les dates", "useView": "Utiliser la vue", "dateWithoutTime": "Date sans heure", "timezone": "<PERSON><PERSON> ho<PERSON>", "roleRestriction": "Restriction de rôle", "headerRestriction": "Restriction d'en-tête", "ttSwitchDarkmode": {"heading": "Commutation du mode clair/sombre", "body": ""}, "advancedEditor": "<PERSON><PERSON><PERSON> a<PERSON>", "externalId": "ID externe", "passwordValidationMin": "Le mot de passe est trop court. (longueur minimale : {{count}})", "passwordValidationMax": "Le mot de passe est trop long. (longueur maximale : {{count}})", "passwordValidationUppercase": "Le mot de passe doit contenir une lettre majuscule. {{atLeast}}", "passwordValidationLowercase": "Mot de passe doit contenir une lettre minuscule. {{atLeast}}", "passwordValidationSymbols": "Le mot de passe doit contenir un symbole. {{atLeast}}", "passwordValidationDigits": "Le mot de passe doit contenir un nombre. {{atLeast}}", "passwordValidationLetters": "Le mot de passe doit contenir une lettre. {{atLeast}}", "atLeast": "Au moins", "passwordValidationServiceErr": "Le mot de passe ne peut pas être modifié pour le moment.", "enableTasksHandoverRole": "Toujours autoriser le transfert de tâches et l'exécution d'événements aux utilisateurs de ce rôle", "shredded": "D<PERSON><PERSON><PERSON><PERSON>", "shredableVar": "Variable broyable", "shredDocuments": "Déchiqueter des documents", "shredInDays": "Déchiquetage en (jours)", "fromBeginningOrendOfCase": "De<PERSON><PERSON> le début/la fin de l'affaire", "shredding": "Déchiquetage", "addColumn": "Ajouter une colonne", "unsupportedBrowser": "Vous ouvrez TeamAssistant dans un navigateur Internet Explorer non pris en charge, Il est possible que certaines fonctions ne soient pas disponibles.", "ingoreProcessRights": "Ignorer les droits de cas", "cvHelpIngoreProcessRights": "L'Aperçu affiche toujours tous les cas, quels que soient les droits", "upperLabel": "Placer la variable juste sous son nom", "darkMode": "Mode sombre", "completedTasks": "Tâches terminées", "permissions": "Autorisations", "caseVisibility": "Visibilité des cas", "visPerOrg": "Visibilité par unité org.", "entity": "Entité", "staticRight": "Droite statique", "dynamicRight": "Droite dynamique", "treeNodesAll": "<PERSON>ut", "treeNodesMy": "Mon", "activeQueries": "Req<PERSON>êtes actives", "query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmCancelQuery": "Voulez-vous vraiment annuler la requête ?", "alrQueryNotFound": "La requête n'a plus été trouvée.", "completeAgenda": "Ordre du jour complet", "lockedBusinessUsers": "Utilisateurs professionnels verrouillés", "structuredList": "Liste structurée", "ttCompetences": {"heading": "Gestion des compétences", "body": ""}, "competences": "Compétences", "competence": "Compétence", "competenceDelVar": "compétence", "addCompetence": "Ajouter une compétence", "regularExpression": "Expression régulière", "generationStatus": "Statut de génération", "source": "La source", "historical": "Historique", "external": "Externe", "nextDay": "le prochain jour", "embeddedVideoNotSupported": "<PERSON><PERSON><PERSON><PERSON>, votre navigateur ne prend pas en charge les vidéos intégrées.", "alrSendingTestMailFailed": "Le test d'envoi d'e-mail a échoué.", "sent": "Expédié.", "mainColorEmail": "Couleur principale des e-mails", "confirmResetColors": "Voulez-vous vraiment réinitialiser les couleurs ?", "regularExpressions": "Expressions régulières", "confirmDeleteLogo": "Voulez-vous vraiment supprimer le logo ?", "loginLogoLightTheme": "Logo de l'écran de connexion (mode clair)", "loginLogoDarkTheme": "Logo de l'écran de connexion (mode sombre)", "competenceRegexHelper": "<ul><li>% peut être utilisé comme N de n'importe quel caractère</b> (équivalent *)</li><li><b>_</b> peut être utilisé comme un caractère arbitraire (équivalent .)< /li><li>Vous pouvez utiliser <b>^</b> pour échapper ces caractères spéciaux (équivalent \\)</li></ul>", "headerFont": "Police d'en-tête", "evenRow": "Ligne paire", "logo": "Logo", "widthForLogo": "Largeur du logo", "monthStart": "<PERSON>é<PERSON> du mois", "monthEnd": "<PERSON> du mois", "ttFavouriteType": "GET ouvre le lien. POST envoie une commande : par exemple, lors de la création d'un cas, où l'ID d'en-tête du modèle est envoyé dans le corps de la requête (enregistrer dans les favoris via Nouveau cas).", "confirmEmptyMultiinstanceVariable": "Êtes-vous sûr que cette multi-instance ne nécessite pas de variable pour itérer ?", "ttMenuPreview": "Configuration du menu par rôles d'utilisateur (les rôles plus significatifs voient également les boutons pour les rôles moins significatifs). Les boutons Nouveau cas et Tableau de bord sont inchangés.", "menuPreview": "Aperçu du menu pour le rôle sélectionné", "confirmResetMenu": "Voulez-vous vraiment réinitialiser le menu ?", "alrFailedTasMenu": "La configuration du menu TAS n'a pas pu être chargée !", "security": "Sécurité", "userRestrictions": "Restrictions de l'utilisateur (afficher)", "userRestrictionsProcesses": "Ignorer les restrictions des utilisateurs sur les tâches", "roleRestrictions": "Restrictions de rôle (afficher)", "orgUnitRestrictions": "Restrictions d'unité d'organisation (afficher)", "everyone": "Tous", "colleaguesOnly": "Collègues seulement", "directSubordinates": "Subordonnés directs", "allSubordinates": "Tous les subordonnés", "none": "Aucune", "generalDocument": "Document général", "competenceRule": "<PERSON><PERSON><PERSON> de compétence", "competenceRules": "<PERSON><PERSON><PERSON> de compétence", "ruleName": "Nom de la règle", "ttUseCompetenceRule": {"heading": "Appliquer la règle", "body": "Établir la compétence selon la règle sélectionnée"}, "competenceText": "Texte de compétence", "competenceName": "Nom de la compétence", "competenceReadOnlyInfo": "La compétence créée à partir d'une règle ne peut pas être modifiée", "xmlProcessImport": "Importation de processus XML", "ttWidthForLogo": "Définissez la largeur du logo, puis insérez un logo. Il n'est pas possible de modifier la largeur d'un logo déjà inséré ou par défaut.", "openCase": "Ouv<PERSON>r un cas", "importHistory": "Historique d'importation", "plannedImports": "Imports planifiés", "filePath": "<PERSON><PERSON><PERSON> du <PERSON>", "cronId": "ID de cron", "taskResult": "Résultat de la tâche", "xmlFileSize": "<PERSON><PERSON>", "attachmentSize": "<PERSON><PERSON> joint", "lastEdit": "Dernière modification", "timeCreated": "Date de création", "importId": "ID d'importation", "importAudit": "Audit d'importation", "finishedImports": "Imports terminés", "insertNote": "Insérer une note", "importXml": "Importer XML", "reImportXml": "Reimporter XML", "downloadXml": "Télécharger XML", "downloadAttachment": "Télécharger fichier joint", "skipXml": "Ignorer XML", "note": "Note", "attachmentName": "Nom du fichier joint", "importedCount": "Nombre d'importations", "retryCount": "Le nombre de répétitions", "batchId": "ID de dose", "copyPath": "<PERSON><PERSON><PERSON> le chemin", "cronRunId": "ID d'exécution", "cronRun": "Exécution cron", "trace_id": "ID de trace", "ttMenuItemLabel": "Nom universel si pas de traduction. Si un mot-clé de traduction est utilisé, il est traduit automatiquement. Noms par défaut : tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "File d'attente des tâches", "dissolveQueue": "Dissoudre la file d'attente", "taskQueueInitInfo": "Cette action a créé plusieurs tâches à résoudre. <PERSON><PERSON>, vous pouvez modifier l'ordre de leur résolution ou les supprimer complètement de la file d'attente.", "tryDarkTheme": "Nous avons remarqué que vous préférez le mode sombre. Cliquez pour l'essayer dans TAS.", "alrInvalidURL": "Format d'URL invalide.", "alrInvalidHttps": "Format d'URL invalide, doit commencer par https://", "importVariables": "Importation de variables", "ttVariablesImport": {"heading": "Importation de variables", "body": "Un dossier avec la définition des variables est sélectionné puis téléchargé."}, "classDiagram": "Diagramme de classe", "createVar": "<PERSON><PERSON><PERSON> une variable", "importObjectStates": "Importation des états d'objet", "unassigned": "Non attribué", "sortVars": "<PERSON><PERSON>", "fillNames": "Remplir les noms", "ttFillNames": {"heading": "Remplir les noms", "body": "Remplit les noms vides de toutes les nouvelles variables au format « Classe.Attribut » et trie toutes les variables."}, "ttSortVars": {"heading": "<PERSON><PERSON>", "body": "Trie les variables par classes et attributs."}, "ttRestore": {"heading": "<PERSON><PERSON><PERSON>", "body": "Restaure les variables à leur état d'origine lorsqu'elles sont importées depuis un fichier."}, "ttAddVarToBottom": {"heading": "Ajouter une variable", "body": "Ajoute une nouvelle variable au bas de la page."}, "confirmRestoreForm": "Voulez-vous vraiment restaurer les variables à leur état d'origine ?", "selectClass": "Sélectionnez la classe", "importClassDiagram": "Importation d'un diagramme de classes", "continue": "<PERSON><PERSON><PERSON>", "templateVars": "Variables de modèle", "newVars": "Nouvelles variables", "objectState": "État d'objet", "alrDynTableExists": "La table dynamique existe déjà !", "overwriteExistDynTable": "Ecraser la table dyn. existante", "confirmCancelImport": "Voulez-vous vraiment annuler l'importation ?", "alrDuplicateNames": "Les données contiennent des noms en double.", "stateVar": "Variable d'état", "importObjectStatesToDynTables": "Importez des états d'objet dans des tables dynamiques.", "defineObjectStatesVars": "Définissez les variables qui détiennent des états d'objet.", "change": "Modifier", "classAndAttr": "Classe et attribut", "clearQueue": "Effacer la file d'attente", "sharing": "Partage", "data": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataSource": "La source de données", "dataPoints": "Points de données", "dataSeries": "Séries de données", "valueCol": "Colonne des valeurs", "aggregationCol": "Colonne d'agrégation", "timeDimension": "Dimension temp.", "columns": "Colonnes", "week": "semaine", "weekday": "jour de la semaine", "monthVar": "mois", "overviewFilter": "Filtre d'aperçu", "globalFilters": "Filtres globaux", "filterDefinition": "Définition du filtre", "newFilter": "Nouveau filtre", "addFilter": "Ajouter un filtre", "filterOptions": "Options de filtrage", "addOption": "Ajouter une option", "graphPreview": "Aperçu du graphique", "alrGlobalFilterDownloadFailed": "Échec du téléchargement des filtres globaux !", "alrGlobalFilterSaveFailed": "Échec de l'enregistrement des filtres globaux !", "filterOption": "Option de filtre", "editFilter": "Modifier le filtre", "fillOptionsFromVar": "Options de remplissage à partir de la variable", "fillOptionsDynamically": "Remplir les options dynamiquement", "filterOptionsFilledDynamically": "Dynamiquement à partir de la variable ", "dayOfMonth": "jour du mois", "dateVar": "date", "group": "Groupe", "ttDataSource": "Choisissez « Points de données » si vous souhaitez entrer chaque point de graphique séparément. Si vous souhaitez que les points soient générés en fonction de la dimension sélectionnée, choisissez « Séries de données »", "ttDataSeriesAggregation": "Choisissez le type d'agrégation. Vous permet de créer des informations récapitulatives à partir d'enregistrements (cas).", "ttDataSeriesColumns": "Sélectionnez tour à tour toutes les colonnes par lesquelles créer des groupes (agrégations) pour calculer les valeurs récapitulatives.", "listOfFiltersIsEmpty": "La liste des filtres est vide.", "fromVariable": "De la variable", "showOptionsFromCount": "Afficher les options (sur {{count}})", "sum": "Somme", "minimum": "Minimum", "maximum": "Maximum", "statistics": "Statistiques", "unfilled": "Vide", "globalFilterDescription": "Le filtre global fournit aux utilisateurs du graphique des options qui filtrent les données d'entrée du graphique. Toutes les options de filtre peuvent être définies dans cet écran.", "ttDelGraph": {"heading": "Supprimer le graphique", "body": "Supprime le graphique sélectionné."}, "ttEditGraph": {"heading": "Modifier le graphique", "body": "Vous permet de modifier le graphique sélectionné."}, "ttCopyGraph": {"heading": "Co<PERSON>r le graphique", "body": "Copie le graphique sélectionné."}, "ttAddGraph": {"heading": "Ajouter un graphique", "body": "Vous permet de définir un nouveau graphique."}, "axisXName": "Nom de l'axe X", "axisYName": "Nom de l'axe Y", "showValues": "Aff<PERSON>r les valeurs", "defaultOption": "Option par défaut", "yearStart": "Début de l'année", "yearEnd": "Fin de l'année", "thisMonth": "Ce mois-ci", "lastMonth": "Le mois dernier", "thisYear": "<PERSON><PERSON> an<PERSON>", "lastYear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduledTasks": "Tâches planifiées", "scheduled": "Planifiées", "dueDateStart": "Date de début", "lastRescheduled": "<PERSON><PERSON> replani<PERSON>", "reschedule": "Replanifier", "alrTasksRescheduling": "Replanification des tâches...", "alrTasksRescheduled": "Les tâches ont été replanifiées.", "alrTasksRescheduleFailed": "Échec de replanification des tâches.", "onlyCurrentOrFutureDates": "Seulement aujourd'hui ou des dates futures.", "passwordValidations": "Politique de mot de passe", "readonlyConfigInfo": "La valeur est en lecture seule", "alrTasksCountFailed": "Le comptage des tâches a échoué.", "confirmActivateTasks": "Êtes-vous sûr de vouloir activer les tâches sélectionnées ?", "confirmSuspendTasks": "Êtes-vous sûr de vouloir suspendre les tâches sélectionnées ?", "tskOffset": "Variable de planification", "workWeek": "<PERSON><PERSON><PERSON> de travail", "agenda": "<PERSON><PERSON> du jour", "noEventsInRange": "Il n'y a pas d'événements dans cette gamme", "activitiesDesc": "Description des activités", "allShort": "Tous", "numberOfEvents": "Nombre d'événements", "weekNumber": "numéro de se<PERSON>", "cannotBeEdited": "Ne peut pas être ajusté", "cannotBeMoved": "Ne peut pas être déplacé", "alrTempVarSaveSameNameFailed": "Une variable avec ce nom par défaut existe déjà, veuillez entrer un nom différent.", "maxUsersCountRole": "Le nombre maximum d'utilisateurs dans le rôle", "unlimitedAssignLeaveBlankInfo": "Pour les attributions illimitées, laissez le champ vide.", "cvOwner": "Propriétaire de la vue d'ensemble", "changePassword": "Changer le mot de passe", "passwordExpired": "Votre mot de passe a expiré. Veuillez le changer.", "passwordWillExpire": "Votre mot de passe va bi<PERSON><PERSON><PERSON> expirer. Entrez un nouveau mot de passe.", "userParameters": "Paramètres utilisateur", "filterSortingHelper": "Le tri sur une ou plusieurs colonnes dans un filtre désactive la possibilité de trier manuellement les colonnes directement dans le tableau.", "importUsers": "Importer des utilisateurs", "importRoles": "Importer des rôles", "existingEntityRows": "Lignes avec des entités déjà existantes (peuvent être écrasées)", "fileRow": "Ligne du fichier", "existingEntityRowsMultiple": "Lignes avec des entités qui existent déjà plus d'une fois (ne seront pas importées)", "importOrgUnits": "Importer des unités d'org.", "structureImportExport": "Importation/exportation de structures", "fillAttributes": "Remplir les attributs", "structurePreview": "Aperçu de la structure", "invalidRowsForImport": "Lignes invalides (données obligatoires manquantes)", "duplicateRowsForImport": "Les lignes avec des données correspondantes en double (ne seront pas importées)", "newEntityRows": "Lignes avec de nouvelles entités à importer", "existingNameRowsForImport": "Les lignes avec des noms qui existent déjà sur d'autres entités (ne seront pas importées)", "overwriteExisting": "Ecraser l'existant", "structurePreviewHelper": "L'aperçu de la structure montre deux situations différentes : importer uniquement de nouvelles organisations ou importer à la fois des organisations nouvelles et existantes qui seront écrasées. Toutes les modifications par rapport à la structure existante sont marquées en rouge.", "validateAndShowPreview": "Valider et prévisualiser", "uploadNewFile": "Télécharger un nouveau fichier", "userStatus": "Statut de l'utilisateur", "importedFile": "Fichier importé", "pairUsersBy": "Associer les utilisateurs par", "assignOrgBy": "Attribuer à l'organisation par", "pairRolesBy": "Appairer les rôles par", "pairUnitsBy": "Appairer les unités par", "unitHierarchyCol": "Colonne de hiérarchie d'unités", "dontAssign": "Ne pas attribuer", "infoImportDataValidated": "ATTENTION : Les données viennent d'être validées en raison de changements dans les paramètres. Nous vous recommandons de revenir en arrière et de vérifier le nouvel aperçu d'importation.", "assignUserRolesMethod": "Comment attribuer des rôles aux utilisateurs", "assignUserRolesMethodHelp": "Comment attribuer des rôles : ajouter aux rôles déjà attribués, ou remplacer complètement les rôles actuellement attribués par des rôles nouvellement attribués.", "existingRolesForImport": "Rôles existants (peuvent être écrasés)", "existingRoleNamesForImport": "Rôles avec des noms qui existent déjà pour d'autres rôles (ne seront pas importés)", "newRolesForImport": "Nouveaux rôles à importer", "userRolesForImport": "Lignes avec des rôles d'utilisateur à attribuer", "nonExistentUsersForImport": "Lignes avec des utilisateurs inexistants (les rôles ne seront pas attribués)", "multipleExistingUsersForImport": "Lignes avec plus d'un utilisateur existant (les rôles ne seront pas attribués)", "invalidOrgsForImport": "Lignes invalides (données obligatoires manquantes ou mauvaise hiérarchie)", "keepOriginal": "Conserver l'original", "assignOrgByHelp": "Si vous sélectionnez une colonne du fichier, vous pouvez spécifier l'attribution de l'organisation pour les utilisateurs nouveaux et existants. Si vous sélectionnez une organisation spécifique, tous les utilisateurs importés ou mis à jour seront attribués à cette organisation.", "creatingRoles": "Création de rôles", "assigningRolesToUsers": "Attribuer des rôles aux utilisateurs", "newUsers": "Nouveaux utilisateurs", "existingUsers": "Utilisateurs existants", "fromFile": "<PERSON><PERSON><PERSON> le fi<PERSON>er", "alrCsvXlsxUploadWrongExtension": "Téléchargez uniquement les fichiers avec l'extension *.csv ou *.xlsx", "importNewAndExisting": "Importer de nouvelles entités et écraser celles existantes", "importNewOnly": "Importer uniquement les nouvelles entités", "importNewAndExistingRoles": "Importer de nouveaux rôles et écraser les rôles existants", "importNewRolesOnly": "Importer uniquement les nouveaux rôles", "importRolesHelper": "Paramètres d'importation des rôles eux-mêmes. L'attribution de rôles aux utilisateurs est régie par ce qui est défini dans « Associer les utilisateurs par » et s'applique toujours aux rôles nouveaux et existants.", "statisticsColorHelper": "Si les couleurs ne sont pas sélectionnées manuellement, ou s'il y a moins de couleurs sélectionnées que de colonnes, les couleurs manquantes sont générées automatiquement. Les couleurs générées ne contiennent jamais de nuances sombres ou trop claires, celles-ci ne peuvent être sélectionnées que manuellement.", "caseService": "Service de cas", "taskService": "Service de tâches", "editTasks": "Modifier les tâches", "editCases": "Modifier les cas", "deleteTasks": "Supprimer des tâches", "deleteCases": "Supprimer des cas", "serviceOperationsInfo": "<PERSON><PERSON> et remplissez les variables que vous souhaitez modifier.", "erased": "<PERSON><PERSON><PERSON><PERSON>", "statusErrored": "<PERSON><PERSON><PERSON>", "serviceOperations": "Opérations de service", "runCalcsOnStart": "Exécuter les calculs au démarrage", "taskReactivation": "Réactivation des tâches", "taskCompletion": "Achèvement des tâches", "caseReactivation": "Réactivation des cas", "caseCompletion": "Achèvement des cas", "openTask": "<PERSON><PERSON><PERSON><PERSON>r la tâche", "changeEntity": "Modifier l'entité", "selectTableColumns": "Sélectionner les colonnes du tableau", "parentCase": "Cas parent", "ownerOrganization": "Organisation propriétaire", "confirmTaskReactivation": "Êtes-vous sûr de vouloir ré<PERSON>r les tâches sélectionnées ?", "confirmCaseReactivation": "Êtes-vous sûr de vouloir ré<PERSON>r les cas sélectionnés ?", "confirmTaskCompletion": "Êtes-vous sûr de vouloir terminer les tâches sélectionnées ?", "confirmCaseCompletion": "Êtes-vous sûr de vouloir terminer les cas sélectionnés ?", "selectAllFilterMustBeActive": "Au moins un filtre doit être actif pour sélectionner tous les éléments.", "changeEntities": "Modifier les entités", "disabledDifferentTemplates": "Ne peut pas être modifié car les entités ne proviennent pas du même modèle.", "actions": "Actions", "taskTemplateId": "ID du modèle de tâche", "caseTemplateId": "ID du modèle de cas", "actionInfoCheckLogs": "L'action sera effectuée en arrière-plan, veuillez vérifier les journaux.", "alrServiceOperationsColumnsFailed": "L'enregistrement du paramètre des colonnes d'opérations de service a échoué.", "confirmResetSelectedCols": "Êtes-vous sûr de vouloir réinitialiser les colonnes de table enregistrées ?", "instanceVars": "Variables d'instance", "usrId": "ID de l'utilisateur", "orgId": "ID de l'organisation", "titlePrefix": "Préfixe du titre", "titleSuffix": "Suffixe du titre", "accessRoleId": "ID du rôle d'accès", "maxAssigns": "Attributions maximales", "client": "Client", "bigValue": "Grande valeur", "unitId": "ID de l'unité", "roleId": "ID de rôle", "paramId": "ID du paramètre", "varId": "ID de la variable", "parentId": "ID du parent", "openUser": "<PERSON><PERSON><PERSON><PERSON>r un utilisateur", "openRole": "<PERSON><PERSON><PERSON><PERSON><PERSON> le rôle", "openUnit": "Ouvrir l'unité", "units": "Unités", "managerId": "ID du gestionnaire", "externalStatus": "Statut externe", "additionalId": "ID supplémentaire", "parentIc": "CI du parent", "companyIc": "IC de l'entreprise", "textValue": "<PERSON>ur du texte", "dateValue": "Valeur de date", "numberValue": "Valeur numérique", "loginCount": "Nombre de connexions", "externalLogin": "Connexion externe", "badLoginCount": "Nombre de connexions incorrectes", "passwordLastChange": "Dernier changement de mot de passe", "solverEvaluation": "Évaluation du solveur", "solverWillBe": "Le solveur sera", "possibleSolvers": "Solveurs possibles", "selectReferencePerson": "Sélectionnez une personne de référence", "evaluateSolver": "É<PERSON>uer le solveur", "referenceUserForEval": "Personne de référence pour l'évaluation", "andOthers": "...et autres", "showLess": "...afficher moins", "alrSessionExpired": "Votre session a expiré, veuillez vous connecter à nouveau.", "mailPromptlyInfo": "L'utilisateur reçoit continuellement des notifications uniques sur les nouvelles tâches, où ils sont le solveur. Ces alertes ne seront envoyées que si la tâche n'a pas été résolue pendant {{minutes}} minutes depuis son activation.", "mailPullInfo": "L'utilisateur reçoit en permanence des notifications uniques sur les nouvelles tâches disponibles pour l'abonnement, et l'utilisateur est son possible solveur. La notification sort au moment de l'activation de la tâche donnée dans le workflow.", "mailTotalInfo": "L'utilisateur reçoit périodiquement un aperçu avec des tâches à accomplir, dont ils sont le solveur. Si la tâche n'a pas de solveur direct, le propriétaire du processus est informé. Si l'utilisateur est représenté, la notification est reçue par son représentant.", "mailEscalationInfo": "L'utilisateur reçoit périodiquement un aperçu avec des tâches à accomplir qui ont dépassé la date limite. Ils sont informés s'ils sont le superviseur de la tâche (et non son solveur en même temps) ou si ils sont le gestionnaire direct de l'utilisateur qui est le solveur. Si la tâche n'a pas de solveur, le propriétaire du processus est considéré comme le superviseur. Si l'utilisateur est représenté, la notification mentionne qui est le représentant actuel.", "calcSourceOverwriteWarning": "Après l'enregistrement, la source est remplacée par la syntaxe ES6 !", "changeStatus": "Modifier le statut", "confirmChangeEmailStatus": "Voulez-vous vraiment changer le statut des e-mails sélectionnés ?", "logInAgain": "Se reconnecter", "migrations": "Migrations", "launchDate": "Date de début", "stepName": "Nom de pas", "runId": "ID de course", "clone": "<PERSON><PERSON>", "confirmDeleteCron": "Voulez-vous vraiment supprimer le cron sélectionné?", "alrCronDeleted": "Cron a été supprimé!", "wantToContinueQ": "Voulez-vous continuer ?", "valueCannotBeEntered": "La valeur ne peut pas être entrée", "processingQueues": "Files d'attente de traitement", "pause": "<PERSON><PERSON><PERSON><PERSON>", "fillOptionsFromVarHelper": "Les options de filtre ne peuvent être remplies qu'à partir de variables de type DT, DL, LT, LD, LN et D, qui n'autorisent pas la sélection multiple.", "defaultTemplateName": "Nom du modèle par défaut", "defaultTaskName": "Nom de la tâche par défaut", "defaultVariableName": "Nom de la variable par défaut", "variableName": "Nom de la variable", "alrNoDataFound": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "ttProcessingQueuesInfo": "Les files d'attente de traitement sont désactivées.\nPour les activer, définissez au moins l'une des configurations « scaling.queue.*.enabled » sur true.", "businessUsers": "Utilisateurs professionnels", "completeHrAgenda": "Agenda RH complet", "usageStatsByHeader": "Statistiques d'utilisation par en-tête", "usageStatsByOrgUnit": "Statistiques d'utilisation par unité org.", "usageStatsByUser": "Statistiques d'utilisation par utilisateur", "completedTasksNum": "Nombre de tâches terminées", "startedProcessesNum": "Nombre de cas commencés", "ideHelp": "Appuyez sur Ctrl + Espace dans l'éditeur pour voir les suggestions, appuyez à nouveau pour une aide plus détaillée. Appuyez sur F1 pour voir toutes les commandes et raccourcis clavier disponibles. Consultez <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>la documentation de l'éditeur</a> pour en savoir plus.", "restHelp": "Entrez l'URL de l'un des services de table TAS (par exemple, '/tasks/mine') et après avoir chargé le service, sélectionnez les colonnes de table que vous souhaitez afficher dans le conteneur.", "defaultGraphName": "Nom du graphique par défaut", "graphName": "Nom du graphique", "ttStatistics": {"heading": "Statistiques", "body": ""}, "defaultAxisXName": "Nom de l'axe X par défaut", "defaultAxisYName": "Nom de l'axe Y par défaut", "defaultFilterName": "Nom du filtre par défaut", "filterName": "Nom du filtre", "defaultOptionName": "Nom de l'option par défaut", "optionName": "Nom de l'option", "defaultOverviewName": "Nom de la vue d'ensemble par défaut", "overviewName": "Nom de l'aperçu", "eventName": "Nom de l'événement", "wantToOverrideEs6": "Si vous voulez vraiment r<PERSON>, écrivez <b>ES6</b>", "processArchivation": "archivage des processus", "processUnarchivation": "processus de désarchivage", "resendEmail": "Renvoyer l'e-mail", "alrFailedSendEmail": "Échec de l'envoi de l'e-mail", "ttResendEmail": {"heading": "Renvoyer l'e-mail", "body": "Renvoie une notification par e-mail précédemment envoyée. Les destinataires peuvent être modifiés ou ajoutés."}, "addCurrentScreenToFavourite": "Ajoutez l'écran actuel à vos favoris", "attachmentAdd": "Ajouter un document", "createNewCase": "<PERSON><PERSON>er un nouveau cas", "moreLanguage": "Autres variantes de langues", "notesAdd": "Ajouter une note", "notesNew": "Nouvelle note", "removeCurrentScreenFromFavourite": "<PERSON><PERSON>rez l'écran actuel du favori", "setDashboard": "Modifier le tableau de bord", "chooseFromCases": "Sélectionner dans les cas", "folders": "Dossiers", "newFolderBtn": "Nouveau dossier", "documentInfo": "Informations sur le document", "userInfo": "Informations utilisateur", "deleteImage": "Supprimer l'image", "profilePhoto": "Photo de profil", "profilePhotoCaption": "Utiliser une photo au format jpeg, jpg, png ou gif.", "updatePhoto": "Mettre à jour la photo", "mailNotifications": "Notifications par courrier électronique", "userPreferences": "Préférences de l'utilisateur", "userSettings": "Paramètres utilisateur", "allVices": "Tous les suppléants", "createVice": "<PERSON><PERSON>er un suppléant", "editVice": "Modifier la suppléance", "viceTip": "La suppléance vous permet de transmettre votre agenda à un collègue", "emptyDataMessage": "Il n'y a rien de plus", "addFirstNote": "Ajouter la première note", "noResultsFor": "Aucun résultat pour :", "noCurrentTasks": "Aucune tâche en cours", "checkYourSearch": "Vérifiez votre recherche et réessayez.", "noFavOverviews": "Aucun aperçu favori", "favOverviewsTip": "Ajoutez un aperçu à vos favoris avec une étoile", "noHiddenOverviews": "Vous n'avez aucun aperçu masqué", "addOverview": "Ajouter un aperçu", "hidden": "Caché", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON>", "removeItem": "Êtes-vous sûr de vouloir supprimer {{variable}} ?", "changePicture": "Changer l'image", "saveFilter": "Enregistrer le filtre", "addAnotherVice": "Ajouter un autre suppléant", "saveVice": "Enregistrer les suppléants", "firstLastName": "Nom et prénom", "taskInfo": "Informations sur la tâche", "emptyFavsTip": "Ajoutez des favoris avec le bouton", "saveAndClose": "Enregistrer et fermer", "usersCanEditOverview": "Les utilisateurs peuvent modifier l'aperçu", "assignedUsers": "Utilisateurs attribués", "assignedOrgUnits": "Unités organisationnelles attribuées", "assignedRoles": "Rôles attribués", "otherLangVariants": "Autres variantes linguistiques", "moveToSharing": "Passer au partage", "insertDocumentsPerm": "L'utilisateur est autorisé à insérer des documents et des notes", "saveNewPassword": "Enregistrer le nouveau mot de passe", "confirmSubscription": "Confirmer l'abonnement", "subscriptionCaption": "La vue sélectionnée vous sera envoyée par e-mail à l'heure définie.", "by": "Par", "frequency": "<PERSON><PERSON><PERSON>", "termination": "Terminaison", "ofTheMonth": "<PERSON> mois", "endOnDate": "Se terminant à la date", "endAfter": "Se terminant après", "onlyOnWorkingDays": "Uniquement les jours ouvrables", "occurrences": "Occurrences", "dayOfWeekBy": "Jour de la semaine par", "calendarDayBy": "Jour du calendrier par", "dateBy": "Date", "byDate": "Par date", "byOccurrenceCount": "Par nombre d'occurrences", "infinitely": "Infiniment", "dayOfMonthAdornment": ". jour du mois", "ordinalAdornment": ".", "toDateBeforeFromError": "La date 'À' ne peut pas être antérieure à la date 'De'", "vice": "Suppléant", "previewShown": "<PERSON><PERSON><PERSON><PERSON> affich<PERSON>", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "hideBtn": "Masquer", "userView": "Vue utilisateur", "adminView": "Vue admin", "or": "Ou", "overlappingVicesError": "Les suppléants ne peuvent pas se chevaucher", "fileVar": "<PERSON><PERSON><PERSON>", "nodeVar": "nœud", "uploadDifferentFile": "Télécharger un autre fichier", "uploadedFile": "<PERSON><PERSON><PERSON>", "refreshPage2": "Actualiser la page", "refreshPageCaption": "Veuillez actualiser la page dans votre navigateur pour continuer.", "ttCopy": {"heading": "<PERSON><PERSON><PERSON>", "body": "Permet de copier l'élément sélectionné avec possibilité de modifier certains paramètres."}, "alrError_INVALID_CSV_MAPPING": "Colonne csv '%s' introuvable dans le mappage des événements. Contacter l'administrateur de l'application.", "documentPreview": "Aperçu du document", "moveUp": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "moveDown": "Dé<PERSON>ler vers le bas", "moveToFilter": "<PERSON><PERSON><PERSON><PERSON> vers le filtre", "moveToSorting": "Passer au tri", "addSorting": "Ajouter un tri", "cancelFilters": "Annuler les filtres", "docUploadedImmediately": "Le document sera téléchargé immédiatement", "moreOptions": "Plus d'options", "docSearchPlaceholder": "Par exemple, facture.pdf...", "tasksSearchPlaceholder": "Par exemple, saisissez une nouvelle facture...", "docUploadedImmediatelyPrivate": "Le document sera instantanément téléchargé en tant que privé", "takeTsk": "<PERSON><PERSON><PERSON>", "tasksActive": "Tâches actives", "subprocesses": "Sous-processus", "cancelAuthorization": "Annuler l'autorisation", "cancelAuthorizationConfirm": "Êtes-vous sûr de vouloir annuler l'autorisation de l'appareil ?", "linkMobileApp": "Lier l'application mobile", "mobileApp": "Application mobile", "scanThisQr": "Scannez ce code QR avec votre appareil mobile.", "scanningQr": "Scanning du code QR. Veuillez patienter.", "deviceName": "Nom du périphérique", "newDeviceName": "Nouveau nom de périphérique", "registrationDate": "Date d'inscription", "lastLogin": "Dernière connexion", "mobileNotifications": "Notifications mobiles", "disableMobileNotification": "Désactivation des notifications sur le mobile", "newQrCode": "Nouveau QR Code", "inactiveScanQr": "Inactif - scannez le code QR.", "enableNotifications": "Activer les notifications", "tip": "Conseil : {{message}}", "alrFavContainerAlreadyExists": "Le conteneur de favoris existe déjà.", "addGraph": "Ajouter un graphique", "newRow": "Nouvelle ligne", "confirmSetDefaultDashboard": "Êtes-vous sûr de vouloir faire du tableau de bord actuel le tableau de bord par défaut pour tous les utilisateurs ?", "changeMayAffectAllUsers": "Ce changement peut affecter tous les utilisateurs.", "noOverviewsTip": "C<PERSON>ez une nouvelle vue en utilisant le bouton « Ajouter un aperçu »", "removeFromHidden": "Retirer des éléments masqués", "last7Days": "7 derniers jours", "last14Days": "14 derniers jours", "last30Days": "30 derniers jours", "lastCalendarMonth": "<PERSON><PERSON> mois calendrier", "lastQuarter": "Dernier trimestre", "last12Months": "12 derniers mois", "lastCalendarYear": "<PERSON><PERSON><PERSON> ann<PERSON> calen<PERSON>", "noFilterSet": "Aucun filtre défini", "noSortingSet": "Aucun tri défini", "deleteGroup": "Supprimer le groupe", "newGroup": "Nouveau groupe", "operators": "Opérateurs", "withActiveTask": "Avec tâche active", "withoutActiveTask": "Sans tâche active", "withNoTerm": "Sans terme", "withTerm": "Avec terme", "securityAndAuthentication": "Sécurité et authentification", "dataIntegrationAndManagement": "Intégration et gestion des données", "appManagementAndConfig": "Gestion et configuration des applications", "monitoringAndMaintenance": "Surveillance et maintenance", "adminSearchPlaceholder": "Par exemple, Fichiers publics...", "authenticationAdminDescription": "Options de connexion utilisateur", "certificatesAdminDescription": "Certificats pour TAS", "elasticsearchAdminDescription": "Intégration avec Elasticsearch", "xmlProcessImportAdminDescription": "Importer des processus XML à l'aide de l'entrée cron XMLProcessImport.js", "structureImportExportAdminDescription": "Import/export de la structure organisationnelle, des utilisateurs et des rôles", "dmsAttributesAdminDescription": "Liste des attributs du document dans DMS", "dynTablesAdminDescription": "Stockage des données dans des tables dynamiques", "csvAdminDescription": "Manipulation des fichiers CSV dans l'application", "configurationAdminDescription": "Configuration de l'application", "settingsAdminDescription": "Paramètres d'identification de l'entreprise et autres tâches administratives", "logsAdminDescription": "Gérer et afficher les journaux des applications", "migrationsAdminDescription": "Migration des données et configuration des applications", "guidesAdminDescription": "Aide et guides pour les utilisateurs", "schemeAdminDescription": "Palette de couleurs, logo et autres éléments de l'application", "sequencesAdminDescription": "<PERSON><PERSON><PERSON> les séquences utilisées dans les modèles", "serviceConsoleAdminDescription": "Commandes d'administration d'application via la console de service", "serviceOperationsAdminDescription": "Gestion complexe des opérations de service", "scriptsAdminDescription": "<PERSON><PERSON>rer les scripts réutilisables dans différents modèles", "appStatusAdminDescription": "Informations sur l'état actuel de la candidature", "usageStatsAdminDescription": "Afficher les statistiques d'utilisation de l'application", "maintenanceAdminDescription": "Paramètres de maintenance et exécution des tâches de maintenance", "scheduledTasksAdminDescription": "<PERSON><PERSON><PERSON> toutes les tâches planifiées", "publicFilesAdminDescription": "<PERSON><PERSON>rer les fichiers publics et la documentation", "cronsAdminDescription": "Automatisation des tâches régulières", "hrAgendaAdminDescription": "Gestion de l'agenda des utilisateurs au sein des RH", "emailsQueueAdminDescription": "Gestion de la file d'attente des e-mails et de toutes les communications par e-mail de TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Échec de l'ajout d'un élément aux favoris", "alrRemoveFavItemFailed": "Échec de la suppression de l'élément des favoris", "alrAddHiddenItemFailed": "Échec de l'ajout d'un élément masqué", "alrRemoveHiddenItemFailed": "Échec de la suppression de l'élément masqué", "display": "Affichage", "compact": "Compact", "standard": "Standard", "comfortable": "Confortable", "exportTo": "Exporter vers", "adminMenuTip": "Ajoutez vos éléments à la liste de favoris dans l'administration. En cliquant sur l'étoile, l'élément s'affichera ici même.", "editorDocumentation": "Documentation de l'éditeur", "addSection": "Ajouter une section", "insertSection": "Insérer une section", "section": "Section", "sections": "Sections", "toTop": "Au début", "toEnd": "À la fin", "alrSectionNotBeEmpty": "La section ne doit pas être vide", "confirmDeleteSection": "Êtes-vous sûr de vouloir supprimer la section ?", "sectionVarsMoveAllTasks": "Les variables de toutes les tâches seront déplacées de la section supprimée vers les variables sans section.", "sectionVarsMove": "Les variables seront déplacées de la section supprimée vers les variables sans section.", "actionCannotUndone": "Cette action ne peut pas être annulée.", "overviewOfAllNews": "Aperçu de toutes les actualités", "copyOverview": "Copier l'aperçu", "create": "<PERSON><PERSON><PERSON>", "copyExistingOverview": "Copier l'aperçu existant", "selectOverview": "Sélectionner un aperçu", "chooseFromOverviews": "Choisissez parmi les aperçus...", "selectTemplate": "Utiliser le modèle", "chooseFromAvailableTemplates": "Choisissez parmi les modèles disponibles...", "loginWithUsernamePassword": "Connexion avec nom d'utilisateur et mot de passe", "signInWithCorporateIdentity": "Connectez-vous avec l'identité d'entreprise", "whatsNewInTAS": "Quoi de neuf dans TAS ?", "whatsNewInTASDescription": "Mises à jour, nouvelles fonctionnalités, conseils, astuces et tout ce que vous devez savoir.", "justOpen": "Ouvert seulement", "editOverview": "Modifier l'aperçu", "noGraphsTip": "Créez un nouveau graphique à l'aide du bouton « Ajouter un graphique »", "noDocumentsTip": "Ajouter un document à la tâche ou en utilisant le bouton « Ajouter »", "noFilesTip": "Ajou<PERSON>z un nouveau fichier à l'aide du bouton « Ajouter »", "less": "<PERSON>ins", "notContains": "Ne contient pas", "notEquals": "N'est pas égal à", "factorySettings": "Paramètres d'usine", "previewCollapsedNavMenu": "Aperçu du menu de navigation réduit", "previewExpandedNavMenu": "Aperçu du menu de navigation étendu", "logoForCollapsedNavMenu": "Logo pour le menu de navigation réduit", "logoForExpandedNavMenu": "Logo pour le menu de navigation étendu", "organisationLogo": "Logo de l'organisation", "pickLogoOrganisation": "Choisir le logo pour l'organisation", "addLogo": "Ajouter un logo", "clickForAddLogoOrDrop": "Cliquez pour ajouter un logo ou déposez le fichier ici", "useLogoSizeMin": "Utiliser la taille minimale du logo.", "logoForLightTheme": "Logo pour le thème clair", "logoForDarkTheme": "Logo pour le mode sombre", "sharedWithMe": "Partagé avec moi", "myOverview": "Mon aperçu", "getMobileAppText": "Obtenez l'application mobile depuis l'App Store", "noDocuments": "Aucun document", "noNotes": "Aucune note", "noFiles": "<PERSON><PERSON><PERSON>", "addFirstDocument": "Ajouter le premier document", "killed": "<PERSON><PERSON>", "chooseNewLogo": "Choisissez un nouveau logo", "function": "Fonction", "groupFunction": "Fonctions intergroupes", "mobileAppAuthFailed": "Échec de l'authentification de l'application mobile.", "currentDocumentVersion": "Version actuelle du document", "csp": "Politique de sécurité du contenu", "documentsDelete": "Supprimer les documents", "confirmDocumentsDelete": "Voulez-vous vraiment supprimer les documents sélectionnés?", "confirmDocumentsDownload": "Vou<PERSON>z-vous télécharger les documents sélectionnés?", "firstNum": "premier {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Télécharger les documents", "caseLogs": "Journaux de cas", "archiveCases": "<PERSON><PERSON> les cas", "archive": "Archiver", "unarchive": "Désarchiver", "confirmArchiveCases": "Voulez-vous vraiment archiver les cas sélectionnés?", "archiveInDays": "Archiver en (jours)", "archived": "Archivé", "archivedx": "Archivés", "alrArchivingCase": "Le cas est en cours d'archivage...", "alrCaseArchived": "Le cas a été archivé.", "alrLackOfPermsToArchiveCase": "Vous n'avez pas les permissions suffisantes pour archiver le cas.", "alrArchiveCaseFailed": "L'archivage du cas a échoué.", "alrUnarchivingCase": "Le cas est en cours de désarchivage...", "alrCaseUnarchived": "Le cas a été désarchivé.", "alrLackOfPermsToUnarchiveCase": "Vous n'avez pas les permissions suffisantes pour désarchiver le cas.", "alrUnarchiveCaseFailed": "La désarchivage du cas a échoué.", "byUser": "Par utilisateur", "byAgenda": "Par agenda", "agendaHandover": "Remise de l'ordre du jour", "activeUsers": "Utilisateurs actifs", "lockedUsers": "Utilisateurs verrouillés", "allUsers": "Tous les utilisateurs", "inactiveUsers": "Utilisateurs inactifs", "hrAgendaSearchPlaceholder": "Par exemple <PERSON> ...", "completeAgendaHandover": "Transfert complet de l'agenda", "handoverCases": "Cas de transfert", "handoverTasks": "Tâches de transfert", "handoverVars": "Variables de transfert", "changeTaskOwner": "Changer le solveur de tâches", "confirmHandover": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>t", "filterCasesByHeaderTip": "Vous pouvez filtrer tous les cas sous le même en-tête dans la colonne En-tête.", "userAgendaSelectedHandover": "Remise de l'agenda <b style=\"color: {{color}};\">sélectionné</b> de l'utilisateur", "userAgendaCompleteHandover": "Remise de l'agenda <b style=\"color: {{color}};\">complet</b> de l'utilisateur", "confirmAgendaHandover": "Êtes-vous sûr de vouloir remettre l'agenda sélectionné ({{selected}}) à l'utilisateur <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Êtes-vous sûr de vouloir remettre l'agenda <b>sélectionné</b> de l'utilisateur <b>{{user}}</b> à l'utilisateur <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Êtes-vous sûr de vouloir remettre l'agenda <b>complet</b> de l'utilisateur <b>{{user}}</b> à l'utilisateur <b>{{newUser}}</b>?", "refreshSessionTitle": "La session TAS sera terminée dans {{minutes}} minutes.", "refreshSessionCaption": "C<PERSON>z sur \"Continuer à travailler\" pour continuer sans interruption.", "continueWorking": "Continuer à travailler", "sessionExpiredCaption": "Cliquez sur \"Se reconnecter\" pour revenir à l'écran de connexion.", "loginExpired": "Nous vous avons déconnecté après une longue période d'inactivité.", "confirmArchiveCase": "Voulez-vous vraiment archiver le cas sélectionné?", "isLowerOrEqualThan": "Doit être inférieur ou égal à", "confirmUnarchiveCase": "Êtes-vous sûr de vouloir désarchiver le cas sélectionné ?", "addCaseRightNewUserTooltip": "Si vous ne cochez pas cette option, le nouvel utilisateur sera remplacé dans la variable métier, mais n'aura pas accès au dossier.", "canBeViced": "Je suis remplacé", "canVice": "Je remplace", "backgroundColor": "<PERSON><PERSON><PERSON> de fond", "defaultDashboardView": "Vue par défaut du tableau de bord", "colorScheme": "<PERSON><PERSON> de couleurs", "displaySelectionAsTags": "Afficher la sélection comme tags", "displayAsPassword": "Afficher comme mot de passe", "sideBySide": "Côte à côte", "copyAssignmentFromTask": "Copier l'attribution depuis la tâche", "toTask": "Vers la tâche", "copyTaskAssignmentWarning": "L'attribution dans la tâche n'est pas vide, voulez-vous la remplacer ?", "copyToOtherTasks": "<PERSON><PERSON>r vers d'autres tâches", "noteScriptsNotApplied": "Remarque : Les scripts ne sont pas appliqués", "generateRecHistory": "Afficher dans les tâches actives et l'historique", "leaveFormerRoles": "Conserver les anciens rôles", "includeCompetences": "Inclure les compétences", "copyRoles": "<PERSON><PERSON><PERSON> les rôles", "userIsActive": "L'utilisateur est actif", "systemUser": "Utilisateur système", "copyRolesFromUser": "<PERSON><PERSON>r les rôles de l'utilisateur", "assignedRolesOverview": "Aperçu des rôles assignés", "copyRolesInfo": "Si l'utilisateur donné fait partie de compétences, ces compétences ne seront pas copiées immédiatement. La génération aura lieu :", "notificationOn": "<PERSON><PERSON><PERSON><PERSON>", "notificationOff": "<PERSON><PERSON><PERSON>", "onNotification": "Notification", "offNotification": "Notification", "page": "page", "fromTo": "<PERSON> <PERSON> <PERSON>", "isAnyOfValue": "Est une valeur de", "notcontains": "ne contient pas", "notequals": "inégal", "fromto": "<PERSON> <PERSON> <PERSON>", "isanyofvalue": "est une valeur de", "alrNoteToggleVisibiltyFailed": "Échec de la bascule de visibilité de la note", "alrNoteHideOnEditFailed": "Échec de la dissimulation de la note d'origine", "hiddenShe": "Cachée", "showHiddenNotes": "Afficher les notes cachées", "alrNoteEdited": "La version modifiée de la note a été enregistrée", "notesEdit": "Modifier la note", "displayName": "Nom affiché", "clientDateFormat": "Format de date", "defaultByLanguage": "Par défaut par langue", "restKeysOptionsNotUpToDate": "Sélection de valeurs obsolète - rechargez le service.", "invalidValue": "<PERSON><PERSON> invalide", "ended": "<PERSON><PERSON><PERSON><PERSON>", "exportAllActive": "Exporter tous les actifs", "alrScriptsLoadFailed": "Le chargement des scripts a échoué.", "scriptsImport": "Importation de scripts", "doImport": "Importer", "alrImportingScripts": "Importation des scripts en cours...", "alrScriptsImported": "Les scripts ont été importés.", "alrScriptsImportFailed": "L'importation des scripts a échoué.", "removeAll": "Supp<PERSON>er tout", "alrNoScriptsToImport": "Aucun script à importer.", "activateAll": "Activer tout", "alrNoPermsToEditNoteInVice": "Vous n'avez pas la permission de modifier une note pendant la suppléance.", "alrNoPermsToToggleNoteVisibilityInVice": "Vous n'avez pas la permission de masquer/afficher une note pendant la suppléance.", "plusMore": "de plus", "variableAlignment": "Alignement de la variable", "variableAlignmentHelp": "Affecte l'alignement de la valeur de la variable dans le formulaire de tâche.", "variableAlignmentLeft": "À gauche", "variableAlignmentRight": "À droite", "tasksMineAndToPull": "Mes + <PERSON> tirer", "myDevice": "Mon appareil", "deleteLogo": "Supprimer le logo", "namingFilter": "Nom du filtre", "exceptionsToRegularSchedule": "Exceptions à l'horaire régulier", "noExceptions": "Pas d'exceptions", "specificDates": "Dates spécifiques", "dateFromTo": "Date de - à", "weekdayCap": "<PERSON><PERSON> <PERSON>", "specificDayBy": "<PERSON>ur s<PERSON><PERSON><PERSON><PERSON><PERSON>", "yearsBy": "ans", "timed": "Programmé", "firstDayOfMonth": "Premier jour du mois", "lastDayOfMonth": "<PERSON><PERSON> jour du mois", "firstDayOfYear": "Premier jour de l'année", "lastDayOfYear": "<PERSON><PERSON> jour de l'année", "addDate": "Ajouter une date", "newPlan": "Nouveau plan", "addAnother": "Ajouter un autre", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "inTimeFromTo": "de {{from}} à {{to}}", "dayOfMonthBy": "<PERSON>ur du mois", "cWorkDays": "jours ouvrables", "cWeeks": "semaines", "cMonths": "mois", "cYears": "ann<PERSON>", "everyWeek": "chaque semaine", "everyYear": "chaque ann<PERSON>", "inMonth": "dans le mois", "everyDay": "chaque jour", "seqIdEdit": "Modifier l'ID de séquence", "allowMultiselectSearchRight": "Autoriser la recherche en affectation", "doubleHeightForContent": "Hauteur doublée pour le contenu", "alrNoVariablesMappingToImport": "Aucun mappage de variables à importer.", "alrVariablesMappingImportLoadFailed": "Le chargement du mappage des variables à importer a échoué.", "variablesMappingImport": "Importation du mappage des variables", "useAllMappings": "Utiliser tous les mappages", "doExportVariablesMapping": "Exporter le mappage des variables", "alrImportingVariablesMapping": "Importation du mappage des variables en cours...", "alrVariablesMappingImported": "Le mappage des variables a été importé.", "alrVariablesMappingImportFailed": "L'importation du mappage des variables a échoué.", "alrVariablesMappingImportedPartially": "Le mappage des variables a été partiellement importé. Certaines variables n'ont pas été trouvées.", "alrEditorHintsLoadFailed": "Échec du chargement de l'aide de l'éditeur.", "addTable": "Ajouter une table", "confirmDynTablesDelete": "Voulez-vous vraiment supprimer les tables dynamiques sélectionnées?", "dynTablesDelete": "Supprimer les tables dynamiques", "addRow": "Ajouter une ligne", "preview": "<PERSON><PERSON><PERSON><PERSON>", "columnDelete": "Supprimer la colonne", "editRow": "Modifier la ligne", "addingNewColumn": "Ajout d'une nouvelle colonne", "addingNewRow": "Ajout d'une nouvelle ligne", "columnsRename": "Renommer les colonnes", "rowCellValues": "Valeurs des cellules de la ligne", "saveDynTableName": "Enregistrer le nom de la table dynamique", "saveDynTableNameQ": "Enregistrer le nom de la table dynamique?", "saveDynTableNameWarning": "Attention, assurez-vous que le changement de nom de la table n'affecte pas les calculs existants dans les modèles.", "rowMove": "<PERSON>é<PERSON>r la ligne", "alrCsvParsingErr": "Erreur lors de l'analyse du fichier CSV !", "addFirstTableColumn": "Ajouter la première colonne du tableau", "my": "Mon", "license": "Licence", "licenses": "Licences", "addLicense": "Ajouter une licence", "licenseResult": "Résultat de la licence", "alrLicenceResultLoadingFailed": "Échec du chargement du résultat de la licence.", "licensesAdminDescription": "Gestion des licences", "uploadByDragging": "Télécharger le fichier en le faisant glisser.", "uploadByDraggingAnywhere": "Téléchargez un fichier en le faisant glisser n'importe où dans l'espace.", "assignVariable": "Attribuer une variable", "confirmDeleteSectionName": "Êtes-vous sûr de vouloir supprimer la section <b>\"{{section}}\"</b> ?", "deleteSectionWarning": "Attention : la section sera supprimée pour toutes les tâches concernées, y compris les variables.", "tasksAffected": "Tâches affectées", "varSearchPlaceholder": "Par exemple, la facturation…", "enlarge": "<PERSON><PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON><PERSON>", "shrink": "<PERSON><PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON>", "doValidate": "Valider", "phoneNumber": "Numéro de téléphone", "textLength": "Longueur du texte", "when": "quand", "to2": "à", "that": "que", "dynCondBuilderBlockFunctionDescShow": "Affiche la variable si la condition est remplie.", "dynCondBuilderBlockFunctionDescHide": "Cache la variable si la condition est remplie.", "dynCondBuilderBlockFunctionDescChange": "Change la valeur de la variable si la condition est remplie.", "dynCondBuilderBlockFunctionDescValidate": "Valide la valeur de la variable.", "addCondition": "Ajouter une condition", "operator": "opérateur", "equals": "égal à", "greaterthan": "supérieur à", "greaterorequal": "supérieur ou égal à", "lessthan": "inférieur à", "lessorequal": "inférieur ou égal à", "demoCode": "Code de démonstration", "code": "Code", "confirmDeleteConditions": "Voulez-vous vraiment supprimer toutes les conditions (y compris le script) ?", "validationErrorMessage": "Message d'erreur de validation", "alrScriptToStructuredBlockConversionFailed": "La conversion du script en bloc structuré a échoué.", "alrStructuredBlockToScriptConversionFailed": "La conversion du bloc structuré en script a échoué.", "alrScriptToBuilderConversionFailed": "La conversion du script en générateur a échoué.", "alrBuilderToScriptConversionFailed": "La conversion du générateur en script a échoué.", "dynCondBuilderBlockFunctionDescScript": "Bloc de script des conditions dynamiques.", "convertToStructuredBlock": "Convertir en bloc structuré", "convertToScript": "Convertir en script", "dynCondBuilderBlockWatchVarsLabel": "Exécuter lors du changement (watchVars)", "variables": "Variables", "copyToOthers": "Copie pour les autres", "sectionName": "Nom de la section", "newSectionName": "Nom de la nouvelle section", "testIt": "Test", "addAdjacentSection": "Ajouter une section voisine", "addAdjacentSectionBelow": "Ajouter la section voisine ci-dessous", "selectExistingSection": "Choisissez une section existante", "renameSectionWarning": "Avertissement: La section sera renommée dans toutes les tâches de modèle.", "warning2": "Avertissement", "copyAssignmentToTask": "Copiez l'affectation à la tâche", "copyAlsoConditions": "Copie et conditions", "copyAssignmentToTaskWarning": "Avertissement: L'attribution et éventuellement les conditions dynamiques dans la tâche sélectionnée seront réécrites.", "importFromOtherTask": "Importer d'une autre tâche", "startFromScratch": "Commencez depuis le début", "howToStartAssignments": "Comment souhaitez-vous commencer à affecter des variables?", "selectTaskToImport": "Sélectionnez la tâche d'importation", "confirm": "Confirmer", "selectTaskToTest": "Pour sélectionner une tâche de test", "toTestSaveChanges": "Les modifications doivent être stockées pour les tests.", "variableAssignmentTest": "Affectation de test des variables", "viewAsMobile": "Afficher comme sur mobile", "viewAsPc": "Afficher comme sur PC", "emptySpace": "Vide", "variableAssignments": "Attribution de variables", "allowCompletionOnChangeOf": "Autoriser l'achèvement lors du changement de", "dynCondBuilderBlockFunctionDescRead": "Change le mode de la variable en \"lecture seule\" si la condition est remplie.", "dynCondBuilderBlockFunctionDescWrite": "Change le mode de la variable en \"lecture et écriture\" si la condition est remplie.", "dynCondBuilderBlockFunctionDescMust": "Change le mode de la variable en \"obligatoire\" si la condition est remplie.", "dynCondBuilderBlockFunctionDescSolve": "Permet l'achèvement de la tâche lors du changement de la variable donnée, si la condition est remplie.", "newsManagement": "Gestion des actualités", "newsManagementAdminDescription": "Gestion des actualités dans l'application", "addNewsPost": "Ajouter une nouvelle", "newPost": "Nouvel article", "news": "Actualités", "basicInfo": "Informations de base", "publicationPlanning": "Planification de la publication", "displayToUsers": "Affichage aux utilisateurs", "displayLocation": "Lieu d'affichage", "newsPostContent": "Contenu de la nouvelle", "postTitle": "Titre du post", "newsManagementPostDetailPhoneNumberTooltip": "Numéro de téléphone à afficher dans le détail de la nouvelle", "newsManagementPostDetailEmailTooltip": "E-mail à afficher dans le détail de la nouvelle", "customUrlLink": "Lien URL personnalisé", "newsManagementPostDetailCustomUrlLinkTooltip": "Lien URL personnalisé à afficher dans le détail de la nouvelle", "stateAfterSaving": "Statut après enregistrement", "newsPostStateActive": "Actif", "newsPostStateInactive": "Inactif", "newsPostStatePlanned": "Planifié", "endNewsPostOnSpecificDate": "Terminer la nouvelle à une date spécifique", "sendNewsPostViaEmail": "Envoyer la nouvelle par e-mail", "priorityNewsPost": "Nouvelle prioritaire", "newsManagementPostDetailPriorityNewsTooltip": "Par exemple, pour annoncer une maintenance ou un changement de procédure", "newsPostEndDate": "Date de fin de la nouvelle", "pickNewsPostDisplayToOrgUnits": "À quelles unités org. afficher la nouvelle ?", "pickNewsPostDisplayToRoles": "À quels rôles afficher la nouvelle ?", "pickNewsPostDisplayToUsers": "À quels utilisateurs afficher la nouvelle ?", "pickNewsPostDisplayOnTemplate": "Sur quel modèle afficher la nouvelle ?", "pickNewsPostDisplayOnHeaders": "Sur quels en-têtes afficher la nouvelle ?", "pickNewsPostDisplayOnTasks": "Sur quelles tâches afficher la nouvelle ?", "pickNewsPostDisplaySubOptionsHelperText": "Sélectionnez d'abord le modèle sur lequel vous souhaitez afficher la nouvelle.", "newsTagsManagement": "Gestion des tags des actualités", "newsTagsManagementAdminDescription": "Gestion des tags des actualités dans l'application", "addTag": "Ajouter un tag", "tags": "Tags", "publicationDate": "Date de publication", "contacts": "Contacts", "avaibleUntil": "Disponible jusqu'à", "published": "<PERSON><PERSON><PERSON>", "newsSinceLastVisitAmount": "Total de {{amount}} nouvelles depuis la dernière visite", "noNews": "Pas de nouvelles", "createNewTag": "C<PERSON>er un nouveau tag", "tagName": "Nom du tag", "alrTagSaved": "Le tag a été enregistré.", "alrTagSaveFailed": "L'enregistrement du tag a échoué.", "confirmDeleteTag": "Voulez-vous vraiment supprimer le tag \"{{tagName}}\"?", "alrPostSaved": "Le post a été enregistré.", "alrPostSaveFailed": "L'enregistrement du post a échoué.", "alrLoadingTagsFailed": "Le chargement des tags a échoué.", "confirmDeletePost": "Voulez-vous vraiment supprimer le post \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "Voulez-vous vraiment supprimer les posts sélectionnés?", "post": "Post", "alrPostLoadFailed": "Le chargement du post a échoué.", "alrTagDeleted": "Le tag a été supprimé.", "alrTagDeleteFailed": "La suppression du tag a échoué.", "alrPostDeleted": "Le post a été supprimé.", "alrPostDeleteFailed": "La suppression du post a échoué.", "alrPostsDeleted": "Les posts sélectionnés ont été supprimés.", "alrPostsDeleteFailed": "La suppression des posts sélectionnés a échoué.", "alrTempTasksLoadFailed": "Échec du chargement des tâches du modèle.", "rolesRestriction": "Restriction de rôle", "usersRestriction": "Restriction d'utilisateur", "orgUnitsRestriction": "Restriction d'unité organisationnelle", "alrPriorityNewsLoadFailed": "Le chargement des nouvelles prioritaires a échoué.", "moreInfo": "Plus d'informations", "tas5Info": "TAS 5.0 est là ...", "totalNewsAmount": "Total de {{amount}} nouvelles", "alrNewsContainerPostsLoadFailed": "Le chargement des publications pour le conteneur de nouvelles a échoué.", "alrTaskNewsLoadFailed": "Le chargement des nouvelles pour la tâche a échoué.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "La date de publication doit être antérieure à la date de fin de la nouvelle.", "alrNotificationsNewsLoadFailed": "Le chargement des nouvelles pour les notifications a échoué.", "moreNews": "Plus de nouvelles", "newsManagementPostDetailConfirmSavingWillSendMail": "Enregistrer le post entraînera l'envoi d'un e-mail à tous les utilisateurs auxquels le post est destiné. Voulez-vous vraiment enregistrer le post ?", "mailNewsNotification": "Notification par e-mail des actualités", "mailNewsNotificationInfo": "L'utilisateur reçoit continuellement des notifications sur les actualités qui lui sont destinées.", "alrRefreshingConfig": "Actualisation de la configuration...", "alrConfigRefreshed": "La configuration a été actualisée avec succès.", "alrConfigRefreshFailed": "L'actualisation de la configuration a échoué.", "ttRefreshConfig": {"heading": "Restaurer la configuration de toutes les sources", "body": ""}, "getMobileAppTextQr": "Obtenez l'application mobile depuis l'App Store ou scannez le code QR", "dateStart": "Date de début", "dateEnd": "Date de fin", "tas_forms_generated": "Nombre de formulaires générés automatiquement"}