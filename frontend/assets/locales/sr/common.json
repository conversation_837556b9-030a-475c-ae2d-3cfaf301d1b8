{"1st": "prvi", "2nd": "drugi", "3rd": "tre<PERSON><PERSON>", "4th": "četvrti", "AddToAllTasks": "Dodajte svim zada<PERSON>", "OfVariable": "<PERSON><PERSON> pro<PERSON>", "RemoveFromAllTasks": "Ukloni iz svih zadataka", "TaskOwnerWhichInVar": "Vlas<PERSON><PERSON> zadat<PERSON>, koji je postavljen u promenljivoj", "action": "<PERSON><PERSON><PERSON>", "active": "Aktivan", "activeShe": "Aktivna", "activePl": "Aktivan", "activity": "Aktivnost", "activityType": "Vrsta aktivnosti", "actualEnd": "Stvarno vreme završetka", "actualSolver": "Stvarni vlasnik zadatka", "actualStart": "Stvarno vreme početka", "actualTsks": "Trenutni zadaci", "actualize": "Osveži", "add": "<PERSON><PERSON><PERSON>", "addAttribute": "<PERSON><PERSON><PERSON> atri<PERSON>", "addOrgUnit": "Dodaj org. <PERSON><PERSON>", "addPlan": "Dodaj plan", "addPrintTemplate": "Dodaj šablon za štampanje", "addRole": "<PERSON><PERSON><PERSON> r<PERSON>u", "addRule": "<PERSON><PERSON><PERSON>", "addTemp": "<PERSON><PERSON><PERSON>", "addTsk": "<PERSON><PERSON><PERSON>", "addUser": "<PERSON><PERSON><PERSON>", "addUserSomething": "Dodelite koris<PERSON> {{variable}}:", "addVariable": "Do<PERSON>j pro<PERSON>", "after": "Posle", "afterTermTasks": "<PERSON><PERSON> z<PERSON> posle roka", "all": "Sve", "allFiles": "<PERSON><PERSON> dato<PERSON>ke", "allMustBeMet": "Sve mora biti ispunjeno", "allMyTasks": "<PERSON><PERSON> moji zadaci", "allSubOfPlanGuar": "Svi podređeni garanta plana", "allTasksWithNoTerm": "Svi zadaci bez roka", "allTasksWithTerm": "Svi zadaci sa datumom dospeća", "allTemps": "<PERSON><PERSON>", "allowMultiple": "Dozvoli izbor više opcija", "allowSelectAll": "Omogućite izbor svih stavki", "allsupOfPlanGuar": "Svi nadređeni garanta plana", "alrBlockingAction": "Potrebno je da se radnja završi. Molimo vas sačekajte trenutak…", "alrActionNameNotDefined": "<PERSON><PERSON><PERSON> \"{{actionName}}\" nije definisana.", "alrActionNotDefined": "<PERSON><PERSON><PERSON> nije defini<PERSON>a.", "alrApiUrlMissing": "Nedostaje izvor podataka tabele.", "alrAssigningTsk": "Dodeljivanje zadat<PERSON>…", "alrAssignmentFailed": "Čuvanje zadatka nije uspelo", "alrAtrFailed": "Brisanje atributa nije uspelo", "alrAttachDeleteFailed": "Brisanje dokumenta nije uspelo", "alrAttachDeleted": "Dokument je izbrisan", "alrAttachDeleting": "Brisanje dokumenta…", "alrAttachDownloadFailed": "Preuzimanje dokumenta nije uspelo.", "alrAttachDownloaded": "Dokument je preuzet!", "alrAttachDownloading": "Preuzimanje dokumenta…", "alrAttachMetaFailed": "Čuvanje metapodataka dokumenta nije uspelo", "alrAttachOrNotesCountFailed": "Prebrojavanje dokumenata ili beleški nije uspelo.", "alrAttachSaveFailed": "Otpremanje dokumenta nije uspelo.", "alrAttachSaved": "Dokument je priložen.", "alrAttachTooBig": "Dokument je prevelik! Dokument prelazi {{maxUploadSize}} MB.", "alrAttrDataFailed": "Učitavanje podataka atributa nije uspelo", "alrAttrFailed": "Učitavanje podataka atributa nije uspelo", "alrAttrSaveFailed": "Greška pri čuvanju atributa!", "alrAttrsLoadFailed": "Učitavanje atributa nije uspelo", "alrAttachRestored": "Dokument je vraćen", "alrAttachRestoreFailed": "Vraćanje dokumenta nije uspelo.", "alrAttachRestoring": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "alrAuthMethodsFailed": "Učitavanje metoda potvrde identiteta nije uspelo.", "alrBadLogin": "Nepravilno korisničko ime ili lozinka.", "alrBlockedPopups": "Verovatno su vam pop-up pro<PERSON>i b<PERSON><PERSON>.", "alrCaseDataLoadFailed": "Učitavanje podataka o slučaju nije uspelo.", "alrCaseDeleteFailed": "Brisanje slučaja nije uspelo.", "alrCaseDeleted": "Slučaj je izbrisan.", "alrCaseDeleting": "<PERSON><PERSON><PERSON><PERSON>…", "alrCaseNameLoadFailed": "Učitavanje naziva slučaja nije uspelo.", "alrCaseNoRights": "Nemate dozvolu za pregled slučaja br. {{id}}.", "alrCaseNotFound": "Slučaj nije pronađen – možda je izbrisan", "alrCaseOverviewFailed": "Učitavanje podataka o slučaju za čitanje (PREGLED SLUČAJA) nije uspelo.", "alrCaseSuspended": "Slučaj je obustavljen!", "alrCaseVarsLoadFailed": "Učitavanje promenljivih procesa nije uspelo.", "alrCaseWakeUpFailed": "Buđenje slučaja nije uspelo.", "alrCaseWakedUp": "Slučaj je probuđen!", "alrCaseWakingUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "alrColsWidthsSettingsFailed": "Čuvanje podešavanja širine kolona nije uspelo.", "alrConnToServerFailed": "Povezivanje sa serverom nije uspelo.", "alrConnectionDataLoadFailed": "Učitavanje podataka o vezi nije uspelo.", "alrConnectionDeleteFailed": "Brisanje veze nije uspelo.", "alrConnectionDeleted": "Veza je i<PERSON>bri<PERSON>a!", "alrConnectionSaveFailed": "Čuvanje veze nije uspelo!", "alrContainerNotFound": "Sadržalac nije pronađen.", "alrCsvDownloaded": "CSV datoteka je preuzeta!", "alrCvNotFound": "<PERSON><PERSON> nije pronađen.", "alrDashboardSettingsFailed": "Čuvanje postavki kontrolne table nije uspelo.", "alrDefaultDashboardLoadFailed": "Učitavanje podrazumevane kontrolne table nije uspelo.", "alrDefaultDashboardSaved": "Podrazumevana kontrolna table je sa<PERSON>vana!", "alrDeleteFailed": "<PERSON><PERSON><PERSON> je do greške tokom brisanja", "alrDeleted": "Izbrisano!", "alrDeleting": "<PERSON><PERSON><PERSON><PERSON>…", "alrDiagramDataLoadFailed": "Učitavanje podataka za izradu dijagrama nije uspelo.", "alrDiagramEditToSave": "Dijagram se ne može sačuvati i transformisati u šablon – on sadrži više od jednog procesa! Ažurirajte dijagram tako da sadrži samo jedan proces ili unesite drugu .bpmn datoteku.", "alrDiagramInitFailed": "Inicijalizacija dijagrama nije uspela.", "alrDiagramMissingTaskName": "<PERSON><PERSON><PERSON> vas da unesete nazive svih zadataka.", "alrDiagramErrors": "Dijagram sadrži greške. Popravite ih i pokušajte ponovo da sačuvate.", "alrDiagramNotValid": "XML nije važeći prema službenoj specifikaciji BPMN 2.0!", "alrDiagramProcessCount": "Dijagram se ne može sačuvati i transformisati u šablon – on sadrži više od jednog procesa!", "alrDiagramSaveFailed": "<PERSON><PERSON><PERSON> je do greške prilikom čuvanja šablona!", "alrDiagramTsksDeleteFailed": "<PERSON><PERSON><PERSON> je do greške pri brisanju zadataka!", "alrDiagramUnchanged": "Šablon ostaje nepromenjen.", "alrDmsColsLoadFailed": "Nije moguće učitati kolone za DMS.", "alrDocsColumnsIdsFailed": "ID kolone tabele dokumenata nije moguće učitati.", "alrDocumentAdding": "Čuvanje dokumenta ...", "alrDynListsDataLoadFailed": "Učitavanje podataka dinamičkih lista nije uspelo.", "alrDynTableColsDataFailed": "Učitavanje podataka u kolone dinamičke tabele nije uspelo.", "alrDynTableDataLoadFailed": "Učitavanje podataka dinamičke tabele nije uspelo.", "alrDynTableNotFound": "Dinamička tabela nije pronađena.", "alrDynTablesDataLoadFailed": "Učitavanje podataka dinamičkih tabela nije uspelo.", "alrCalcScriptsDataLoadFailed": "Učitavanje globalnih skripti za proračun nije uspelo.", "alrEditValues": "Ispravite pogrešno unete vrednosti.", "alrEventSaveFailed": "Čuvanje događaja nije uspelo.", "alrEventSaved": "Događaj je sa<PERSON>uvan!", "alrEventSaving": "<PERSON>uvanje dog<PERSON> …", "alrEventTriggered": "Događaj je pokrenut!", "alrExcelDownloaded": "Datoteka .xlsx je preuzeta!", "alrExportCompleted": "Eksport je završen.", "alrExportFailed": "Eksport nije bio uspešan.", "alrExportPreparing": "Priprema eksporta…", "alrFailed": "<PERSON><PERSON><PERSON> nije us<PERSON>a.", "alrFailedCalendarTask": "Učitavanje zadatka u kalendar nije uspelo,", "alrFailedCreatePrint": "Kreiranje štampanja nije uspelo.", "alrFailedDLTotalCount": "", "alrFailedData": "Na dinamičkoj listi {{label}} nije naveden ukupan broj, stoga su svi zapisi učitani.", "alrFailedEventStart": "Učitavanje podataka nije uspelo.", "alrFailedEventVariables": "Učitavanje promenljivih izabranog događaja nije uspelo.", "alrFailedEvents": "Učitavanje događaja nije uspelo.", "alrFailedFoldersData": "Učitavanje podataka direktorijuma nije uspelo.", "alrFailedFormData": "Učitavanje podataka obrasca nije uspelo.", "alrFailedInitiatorName": "Učitavanje imena inicijatora nije uspelo.", "alrFailedLabelData": "Učitavanje podataka o komponenti oznake nije uspelo.", "alrFailedLoad": "Učitavanje podataka za štampanje nije uspelo.", "alrFailedLogicalType": "Učitavanje logičkog tipa nije uspelo.", "alrFailedMultiBoxData": "Učitavanje podataka MultiBox-a nije uspelo.", "alrFailedNewCase": "Došlo je do greške pri postavljanju novog slučaja.", "alrFailedNewFolder": "Promena imena direktorijuma nije uspela.", "alrFailedNoticeData": "Učitavanje podataka upozorenja nije uspelo.", "alrFailedOrgUnitUser": "Učitavanje organizacionih jedinica korisnika nije uspelo.", "alrFailedOverviewData": "Učitavanje pregleda nije uspelo.", "alrFailedPlanData": "Učitavanje podataka plana nije uspelo.", "alrFailedPostData": "Slanje podataka nije uspelo.", "alrFailedPrintData": "<PERSON>je moguće učitati podatke za štampanje.", "alrFailedRevisionInfo": "Učitavanje novih informacija o reviziji nije uspjelo.", "alrFailedSearchBoxData": "Učitavanje SearchBox podataka nije uspelo.", "alrFailedSelectBoxData": "Učitavanje podataka komponente SelectBox nije uspelo.", "alrFailedSuggestBoxData": "Učitavanje podataka predlagača nije uspelo.", "alrFailedTasColors": "Učitavanje TAS boja nije uspelo!", "alrFailedTaskHandOver": "Predaja zadatka nije uspela.", "alrFailedTemplateProcesses": "Učitavanje šablona slučaja nije uspelo.", "alrFailedVarData": "Učitvanje podataka promenljive nije uspelo", "alrFileAdded": "Datoteka je dodata!", "alrFileDeleteFailed": "B<PERSON>anje datoteke nije uspelo.", "alrFileDonwload": "<PERSON>uz<PERSON><PERSON> da<PERSON>…", "alrFileDownloaded": "Datoteka je preuzeta!", "alrFileInfoFailed": "Učitavanje informacija o datoteci nije uspelo.", "alrFileMetaSaveFailed": "<PERSON><PERSON> moguc<PERSON>e sačuvati metapodatke o datoteci.", "alrFileSavedLikeAttach": "Datoteka je sa<PERSON>uvana kao dokument.", "alrFileUploadFailed": "Učitavanje datoteke nije uspelo.", "alrFillAllRequired": "Da biste dov<PERSON><PERSON><PERSON>, morate popuniti sve potrebne podatke!", "alrFillData": "Da biste dodelili <PERSON>, svi podaci moraju biti ispravno popunjeni!", "alrFillDataInRightFormat": "Molimo vas da podatke popunite u ispravnom formatu.", "alrFillDataToCompleteTsk": "Da biste dov<PERSON><PERSON><PERSON>, svi podaci moraju biti ispravno popunjeni!", "alrFillNameAndPass": "<PERSON><PERSON><PERSON> unesite ime i lozinku.", "alrFillNote": "Molimo vas da popunite tekst beleške.", "alrFillRequiredItems": "Molimo vas da popunite potrebne stavke.", "alrFolderDataFailed": "Učitavanje podataka o direktorijumima nije uspelo.", "alrFolderDataLoadFailed": "Učitavanje podataka o direktorijumu nije uspelo.", "alrFolderFailed": "Učitavanje informacija o direkorijumu nije uspelo.", "alrFolderSaveFailed": "<PERSON><PERSON> moguc<PERSON>e sačuvati direktorijum!", "alrFoldersLoadFailed": "Učitavanje direktorijuma nije uspelo.", "alrHelpSettingsSaveFailed": "<PERSON><PERSON> mog<PERSON>e sačuvati podešavanja pomoći.", "alrHistoricalTskInfoFailed": "Učitavanje podataka o istorijskim zadacima nije uspelo.", "alrHistoricalVarsSaveFailed": "Istorijske promenljive nije moguće sačuvati.", "alrHistoricalVarsSaved": "Istorijske promenljive zadatka su sačuvane.", "alrInvLogginHash": "Nepravilno prijavljivanje.", "alrJsonFailed": "Nevažeći JSON!", "alrLackOfPermsToEdit": "Nemate dozvolu za uređivanje! Vlasnik je", "alrLackOfPermsToSleepCase": "Nemate dozvolu da obustavite slučaj.", "alrLackOfPermsToWakeUpCase": "Nemate dozvolu za buđenje slučaja.", "alrLastHistoricalTskIdFailed": "Učitavanje ID-a poslednjeg istorijskog zadatka nije uspelo.", "alrLoadAttachmentsFailed": "Učitavanje dokumenata nije uspelo.", "alrLogOutFailed": "Odjavljivanje nije uspelo.", "alrLoginExpired": "Isteklo je vreme za prijavljivanje, prijavite se ponovo.", "alrMappingFailed": "Mapiranje nije moguće sačuvati.", "alrMappingTsksFailed": "<PERSON><PERSON> mog<PERSON>e učitati mapiranje zadataka.", "alrNewCaseBased": "Novi slučaj postavljen!", "alrNewFolder": "Novi direktorijum je napravljen.", "alrNewFolderFailed": "Kreiranje novog direktorijuma nije uspelo.", "alrNextTskOpened": "Sledeći zadatak je otvoren.", "alrNoDataToPrint": "Nisu pronađeni podaci za štampanje.", "alrNoteAdded": "Beleška je dodana!", "alrNoteSaveFailed": "Čuvanje beleške nije uspelo.", "alrNoteSaving": "Čuvanje beleške je u toku…", "alrNotesLoadFailed": "Učitavanje beleški o slučaju nije uspelo.", "alrOrgUnitDataFailed": "Učitavanje podataka organizacione jedinice nije uspelo.", "alrOrgUnitDeleteFailed": "Brisanje organizacione jedinice nije uspelo.", "alrOrgUnitDeleted": "Organizaciona jedinica je izbrisana!", "alrOrgUnitDeleting": "Brisanje org. jedinice…", "alrOrgUnitSaveFailed": "Čuvanje organizacione jedinice nije uspelo.", "alrOrgUnitSaved": "Organizaciona jedinica je sačuvana!", "alrOrgUnitSaving": "Čuvanje organizacione jedinice ...", "alrOverviewDataLoadFailed": "Učitavanje podataka pregleda nije uspelo.", "alrOverviewSaveFailed": "Čuvanje pregleda nije uspelo!", "alrOverviewSaveSameNameFailed": "Vi ili drugi korisnik već koristite naziv pregleda, molimo vas da odaberete drugo ime pregleda.", "alrGraphSaveSameNameFailed": "Vi ili drugi korisnik već koristite naziv pregleda, molimo vas da odaberete drugo ime pregleda.", "alrReportSaveSameNameFailed": "Vi ili drugi korisnik već koristite naziv grafikona, molimo vas da odaberete drugo ime grafikona.", "alrOverviewsLoadFailed": "Učitavanje pregleda nije uspelo.", "alrPassSaveFailed": "Čuvanje lozinke nije uspelo.", "alrPassSaved": "Lozinka je sa<PERSON>!", "alrPlanReqItems": "Da biste sačuvali plan, popunite potrebne stavke.", "alrPlanSaveFailed": "Čuvanje plana nije uspelo.", "alrPlanSaved": "Plan je sa<PERSON>van!", "alrPreparingPrint": "Priprema za štampanje ...", "alrPrintDeleteFailed": "Brisanje štampanja nije uspelo.", "alrPrintDeleted": "Štampanje je izbrisano!", "alrPrintSaveFailed": "Čuvanje štampe nije uspelo.", "alrPrintSaved": "Štampanje je sa<PERSON>u<PERSON>o!", "alrReadOnlyCaseDataFailed": "Učitavanje podataka o slučaju za čitanje nije uspelo.", "alrRecalcFailed": "Greška prilikom ponovnog izračuna!", "alrRecalculating": "Ponovno izračunavanje ...", "alrRestorTemplFailed": "Vraćanje šablona nije uspelo.", "alrRoleDataLoadFailed": "Učitavanje podataka o rolama nije uspelo.", "alrRoleDeleteFailed": "<PERSON><PERSON><PERSON><PERSON> role nije uspelo.", "alrRoleDeleted": "Rola je izbrisana.", "alrRoleDeleting": "<PERSON><PERSON><PERSON><PERSON> role…", "alrRoleSaveFailed": "<PERSON><PERSON><PERSON><PERSON> role nije uspelo.", "alrRoleSaved": "<PERSON>ola je sa<PERSON>!", "alrRoleSaving": "<PERSON><PERSON><PERSON><PERSON> role…", "alrRunEvent": "Pokretanje događaja ...", "alrSaveFailed": "Čuvanje nije uspelo.", "alrSaved": "Sačuvano!", "alrSaving": "<PERSON><PERSON><PERSON><PERSON>…", "alrSavingBeforeRecalcFailed": "Greška tokom čuvanja pre ponovnog izračuna!", "alrSavingFailed": "Greška tokom čuvanja!", "alrSavingPlan": "Čuvanje plana…", "alrSavingPrint": "Čuvanje štampanja ...", "alrSearchNoResults": "Nema rezultata koji odgovaraju parametrima pretraživanja.", "alrSearchRequestFailed": "Greška prilikom slanja zahteva!", "alrSearching": "Pretraga…", "alrSettFailed": "Podešavanja nije moguće sačuvati.", "alrSettSaved": "Podešavanja su sačuvana.", "alrSettingsLoadFailed": "Učitavanje podataka o podešavanjima nije uspelo.", "alrSleepCaseFailed": "Suspendovanje slučaja nije uspelo.", "alrStoreNameNotDefined": "Prodavnica \"{{storeName}}\" nije definisana.", "alrStoreNotDefined": "Prodavnica nije definisana.", "alrSubActionNotDefined": "SubAction i sufiks moraju biti definisani.", "alrSubStoreNotDefined": "SubStore i sufiks moraju biti definisani.", "alrSuggestBoxDataNotContains": "Podaci u polju \"{{label}}\" ne sad<PERSON><PERSON>e \"{{prop}}\"!", "alrSuspendingCase": "Obustavljanje slučaja…", "alrTableDataFailed": "Učitavanje podataka tabele nije uspelo.", "alrTasNewVersion": "Pronađena je nova verzija aplikacije.", "alrRefresh": "<PERSON><PERSON><PERSON><PERSON> je {{- spanRefresh}} stranicu u pregledaču.", "alrTasVersionLoadFailed": "Učitavanje verzije aplikacije nije uspelo!", "alrTaskHandOver": "Predaja zadatka ...", "alrTaskHandedOver": "Zadatak je predat korisniku:", "alrTaskNoRights": "Nemate dozvolu za pregled zadatka br. {{id}}.", "alrTaskNotFound": "Zadatak nije pronađen.", "alrTempDataLoadFailed": "Učitavanje podataka šablona nije uspelo.", "alrTempHeadersLoadFailed": "Učitavanje zaglavlja šablona nije uspelo.", "alrTempDeleteFailed": "Brisanje šablona nije uspelo.", "alrTempDeleted": "Šablon je obrisan!", "alrTempFoldersLoadFailed": "Učitavanje direktorijuma šablona nije uspelo.", "alrTempNameLoadFailed": "Učitavanje naziva šablona nije uspelo.", "alrTempRestored": "Šablon je vraćen sa statusom Razvijeno.", "alrTempSaveFailed": "Čuvanje šablona nije uspelo.", "alrTempsLoadFailed": "Učitavanje šablona nije uspelo.", "alrTempVarDataLoadFailed": "Učitavanje podataka promenljive šablona nije uspelo.", "alrTempVarSaveFailed": "Čuvanje promenljive nije uspelo.", "alrTempVarsLoadFailed": "Učitavanje promenljivih šablone nije uspelo.", "alrTotalCountFailed": "Prebrojavanje ukupnih zapisa u tabeli nije uspelo.", "alrTreeDataFailed": "Učitavanje podataka o stablu nije uspelo.", "alrTskAddFailed": "Dodavanje zadatka nije uspelo.", "alrTskAdded": "Zadatak je dodat!", "alrTskAdding": "Dodavanje zadatka ...", "alrTskAssignFailed": "Dodeljivanje zadatka nije uspelo.", "alrTskAssigned": "Zadatak je dodeljen.", "alrTskCompleteFailed": "Greška tokom izvršavanja zadatka.", "alrTskDataLoadFailed": "Učitavanje podataka o zadatku nije uspelo.", "alrTskDeleteFailed": "Brisanje zadatka nije uspelo.", "alrTskDeleted": "Zadatak je izbrisan!", "alrTskNameLoadFailed": "Učitavanje naziva zadatka nije uspelo.", "alrTskRecalculated": "Zadatak je preračunat!", "alrTskSaveFailed": "Greška tokom čuvanja zadatka.", "alrTskSaved": "Zadatak je sa<PERSON>u<PERSON>!", "alrTskSavedAndCompleted": "Zadatak je sa<PERSON>uvan i završen!", "alrTskScheduleFailed": "Zakazivanje zadatka nije uspelo.", "alrTskScheduled": "Zadatak je zakazan.", "alrTskTakeFailed": "Preuzimanje zadatka nije uspelo.", "alrTskTaken": "Zadatak je preuzet.", "alrTskTakign": "Preuzimanje zadatka ...", "alrTsksMappingFailed": "Učitavanje zadataka mapiranja nije uspelo.", "alrUserDataLoadFailed": "Učitavanje korisničkih podataka nije uspelo.", "alrUserDeleteFailed": "Brisanje korisnika nije uspelo.", "alrUserDeleted": "Korisnik je izbri<PERSON>!", "alrUserDeleting": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> ...", "alrUserIsNotActive": "Korisnik nije aktivan.", "alrUserNotLoaded": "Učitavanje korisnika nije uspelo.", "alrUserParamsLoadFailed": "Učitavanje korisničkih parametara nije uspjelo.", "alrUserSaveFailed": "Čuvanje korisnika nije uspelo.", "alrUserSaved": "Korisnik je sa<PERSON>uvan!", "alrUserSaving": "Čuvanje korisnika ...", "alrUserStatusChangeFailed": "Promena statusa korisnika nije uspela.", "alrUserStatusChanged": "Status korisnika je promenjen!", "alrUserStatusChanging": "Promena statusa korisnika ...", "alrVarDeleteFailed": "Brisanje promenljive nije uspelo.", "alrVarDeleted": "Promenljiva je izbri<PERSON>a!", "alrVarSaveFailed": "Čuvanje promenljive nije uspelo.", "alrVarSaved": "Promenljiva je sa<PERSON>.", "alrVarSaving": "Čuvanje promenljive ...", "alrVarsForModalFilterFailed": "Učitavanje promenljivih modalnih filtera nije uspelo.", "alrVarsLoadFailed": "<PERSON>je mog<PERSON>e učitati promenljive.", "alrVarsOrderLoadFailed": "Učitavanje redosleda promenljivih nije uspelo.", "alrVarsOrderSaveFailed": "Čuvanje redosleda promenljivih nije uspelo.", "alrViceDeleted": "Zamena je otkazana.", "alrViceFailed": "Zamena nije bila us<PERSON>šna.", "alrViceNotFound": "Zamena nije pronađena!", "alrViceSaveFailed": "Zamena se ne može sačuvati.", "alrViceSaved": "Zamena je sa<PERSON>!", "alrViceSaving": "Čuvanje zamene ...", "always": "Uvek", "annually": "Godišnje", "assHierarchy": "<PERSON><PERSON><PERSON> prema referent<PERSON><PERSON> o<PERSON>bi", "assHierarchyAncestors": "Svi nadređeni referentne o<PERSON>be", "assHierarchyChildren": "Direktni podređeni referentne osobe", "assHierarchyDescendants": "Svi podređeni referentne osobe", "assHierarchyGuarantor": "<PERSON><PERSON> referent<PERSON> o<PERSON>", "assHierarchyParent": "Direktni nadređeni referentne osobe", "assHierarchySiblings": "<PERSON><PERSON><PERSON> referent<PERSON>", "assMethodAutomatic": "Automatski pomoću računara", "assMethodLastSolver": "Poslednjem vlasniku zadatka", "assMethodLastSolverChoice": "Vlasnik zadatka je izabran od strane poslednjeg vlasnika zadatka", "assMethodLeast": "Vlasnik zadatka sa najmanjim brojem zadataka", "assMethodPull": "<PERSON><PERSON>, zadatak će biti ponuđen", "assMethodSelect": "Vlasnike zadataka bira nadzornik zadatka", "assMethodVariable": "Vlasnik zadatka, iz promenljive", "assessmentOfConds": "<PERSON><PERSON><PERSON>", "assign": "<PERSON><PERSON><PERSON>", "assignAttrs": "<PERSON>dela atributa", "assignAttrsLogType": "Dodela atributa logičkoj vrsti dokumenta", "assigned": "Dodeljeno", "assigningRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON> role", "assignments": "<PERSON><PERSON><PERSON>", "attachments": "Dokumenti", "attachmentsList": "Lista dokumenata", "attribute": "Atribut", "attributeNew": "Atribut – Novi", "availableVars": "Dostupne promenljive", "body": "Telo", "borders": "Ivice", "byFolders": "Po direktorijumima", "byOrganization": "Po organizaciji", "byRole": "Po roli", "calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculations": "<PERSON><PERSON><PERSON><PERSON>je", "calendar": "<PERSON><PERSON><PERSON>", "carriedIfNoOther": "Izvršiće se ako nema drugih", "case": "<PERSON><PERSON><PERSON><PERSON>", "caseCreation": "<PERSON><PERSON><PERSON><PERSON>", "caseGraph": "Dijagram instance", "caseNoEvents": "Slučaj ne sadrži događaje.", "caseNum": "<PERSON><PERSON><PERSON><PERSON> br.", "caseOwner": "Vlasnik slučaja", "caseStatus": "Status slučaja", "caseVar": "<PERSON><PERSON><PERSON><PERSON>", "cases": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "casesWithProblem": "<PERSON>ji slučajevi sa problemom", "category": "Kategorija", "changeTaskSolver": "Promenite rešavača zadatka", "changedBy": "Promenio / la", "changedWhen": "Promenjeno (kada)", "checkbox": "Checkbox", "checkboxList": "Checkbox lista", "choosePrint": "Šablon za štampanje", "chooseUserToAssignTsk": "Odaberite korisnika kome će biti dodeljen zadatak", "choosenAttrs": "Odabrani atributi", "city": "Grad", "class": "<PERSON><PERSON><PERSON>", "clickToClose": "Zatvorite klikom", "clickToRefresh": "Kliknite da biste ažurirali stranicu u pregledaču", "clickToRepeat": "Ponovite radnju klikom", "clientLanguage": "<PERSON><PERSON><PERSON>", "cloneRow": "Duplikat linije", "close": "Zatvori", "closeAll": "Zatvori sve", "coWorkersOfPlanGuar": "Saradnici garanta plana", "color": "<PERSON><PERSON>", "colors": "<PERSON><PERSON>", "column": "Kolona", "columnName": "Naziv kolone", "comment": "Komentar", "complete": "Zav<PERSON>š<PERSON>", "completion": "Završetak", "componentDescription": "Opis komponente", "condition": "<PERSON><PERSON>", "conditions": "<PERSON><PERSON><PERSON>", "confirmAttachDeletion": "Da li zaista želite da izbrišete dokument?", "confirmDeleteDialog": "Da li zaista želite da izbrišete {{variable}}?", "confirmDialogEventSave": "Za prebacivanje potrebno je sačuvati događaj. Da li želite da ga sačuvate?", "confirmResetDashboard": "Da li zaista želite da resetujete kontrolnu tablu?", "confirmSaveChanges": "Sačuvati promene?", "confirmSaveDiagramChanges": "Sačuvati promene u dijagramu?", "confirmSaveTaskChanges": "Sačuvati promene u zadatku?", "confirmRestoreDialog": "Da li zaista želite da vratite {{variable}}?", "confirmSaveNote": "Da li želite da sačuvate belešku?", "confirmSleepCase": "Da li zaista želite da obustavite slučaj?", "confirmTakeoverTsk": "Da li zaista želite da preuzmete zadatak?", "confirmWakeUpCase": "Da li zaista želite da probudite slučaj?", "connection": "<PERSON><PERSON><PERSON>", "connectionFailed": "Povezivanje sa serverom nije uspelo.", "connectionVar": "link", "constant": "Konstantno", "contact": "Kontakt", "contactTaskOwner": "Obratite se vlasniku zadatka", "containerSettings": "Postavke sadržaoca", "contains": "<PERSON><PERSON><PERSON><PERSON>", "continueSolving": "Nastavite u rešenju", "copied": "<PERSON><PERSON><PERSON>!", "copy": "<PERSON><PERSON><PERSON>", "copyShortcut": "Pritisnite Ctrl+C", "copyToClipboard": "Kopiraj u klipbord", "createForm": "<PERSON><PERSON><PERSON><PERSON>", "csv": "csv", "csvFile": "CSV datoteka", "customPrint": "Štampanje po meri", "daily": "Dnevno", "dashCvNoOverview": "<PERSON><PERSON> izabran pregled - birate ga u postavkama sadržaoca", "dashCvNoRights": "Nemate dozvolu za prikazivanje pregleda, obra<PERSON> se <PERSON>u.", "dashFavNoShortcut": "Nisu izabrane zamene - birate ih u postavkama sadržaoca", "dashboard": "Kontrolna tabla", "date": "Datum", "dateList": "Lista datuma", "day": "dan", "dayE": "<PERSON>", "daysDHM": "Dani: (dd:hh:mm)", "defChangeVarInfluence": "Ova promena definicije promenljive će se proširiti u već kreirane slučajeve.", "defChangeInfluence": "Ova promena definicije će se proširiti u već kreirane slučajeve.", "defaultCaseName": "Podrazumevani naziv slučaja", "defaultLbl": "Podrazumevani {{label}}", "defaultLblShe": "Po<PERSON><PERSON><PERSON>vana {{label}}", "defaultLblIt": "Podrazumevano {{label}}", "defaultPrintProcess": "Podrazumevano - proces", "defaultPrintTask": "Podrazumevano - zadatak", "defaultValue": "Podrazumevana vrednost", "delUser": "Izbriši korisnika", "delete": "Izbriši", "deleteCol": "Izbriši kolonu", "deleteRow": "Izbriši liniju", "deleteSelected": "Izbriši izabrano", "deleted": "Izbrisano", "deletedOn": "<PERSON><PERSON><PERSON>san", "deletedShe": "Izbrisana", "description": "Opis", "deselect": "Poništite izbor", "detail": "<PERSON><PERSON><PERSON>", "developed": "U razvoju", "dial": "Nazvat<PERSON>", "dic": "PDV", "directSubOfPlanGuar": "Direktni podređeni garanta plana", "directSupOfPlanGuar": "Direktni nadređeni garanta plana", "disableFilter": "Onemogućite filter", "dmsAssignAttrs": "Dodela DMS atributa", "dmsAttribute": "DMS atribut", "dmsAttributes": "DMS atributi", "dmsColumns": "DMS - kolone", "dmsVisNull": "Samo u ovom procesu", "dmsVisSub": "U podprocesima", "dmsVisSup": "U matičnom procesu", "dmsVisSupSub": "U matičnim i podređenim procesima", "dmsVisibility": "Dokumenti će se videti", "doNotShowVariablesWith_": "Ime promenljive koja počinje sa `_` neće biti prikazano korisnicima", "document": "Dokument", "documentVar": "dokument", "documents": "Dokumenti", "doesNotContain": "ne <PERSON><PERSON><PERSON>i", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "dragAddFiles": "Dodajte datoteke prevlačenjem i puštanjem ili kliknite {{- here}} za izbor datoteka.", "dragAddFile": "Dodajte datoteku prevlačenjem i puštanjem ili kliknite {{- here}} za izbor datoteke.", "here": "ovde", "dropContainer": "Ispustite sadržaoca", "dropzoneTip": "Prevucite datoteke ovde", "dropZoneUserPhoto": "Ovde prevucite sliku ili kliknite da biste izabrali datoteku za otpremanje.", "dueDate": "Rok predaje", "duty": "Dužnost", "dynList": "<PERSON>. lista", "dynRowsDefinition": "Definicija tabele i kolone", "dynTableName": "Naziv dinamičke tabele", "dynTable": "Dinamička tabela", "dynTables": "Dinamičke tabele", "dynamicList": "Dinamička lista", "dynamicRows": "Dinamički redovi", "dynamicTable": "Dinamička tabela", "edit": "Urediti", "editAttribute": "Izmenite atribut", "editOrgUnit": "Izmenite org. jedinicu", "editRole": "Izmenite rolu", "editRule": "Izmenite pravilo", "editUser": "Izmenite korisnika", "editor": "Urednik", "email": "E-mail", "emailsQueue": "E-mail red", "empty": "Prazno", "end": "<PERSON><PERSON>", "error": "Greška", "errored": "S greškom", "error404": "Greška 404 - stranica nije pronađena!", "event": "<PERSON><PERSON><PERSON><PERSON>", "events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventsRun": "Pokreni događ<PERSON>", "every": {"masc": "<PERSON><PERSON><PERSON>", "neutral": "Svake", "repeat": "<PERSON><PERSON><PERSON><PERSON>"}, "everyWorkDay": "<PERSON><PERSON><PERSON> radni dan", "excel": "Excel", "favourites": "<PERSON><PERSON><PERSON><PERSON>", "fax": "Faks", "file": "Datoteka", "fileLogicalType": "Logički tip datoteke", "fileName": "<PERSON>v datoteke", "filePlacement": "Postavljanje u direktorijum", "files": "Datoteke", "filter": "Filter", "filterFrom": "Filter od", "filterTitle": "Filter", "filtrate": "Filter", "finishTask": "Završi zadatak", "finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finishedBy": "Završeno od", "finishedOn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "first": "Prvi", "firstLeft": "Prvo levo", "firstName": "Ime", "firstRight ": "Prvo <PERSON>", "firstRowColumnsName": "<PERSON><PERSON><PERSON> <PERSON><PERSON>i imena kolona", "folder": "Direktorijum", "folder-": "Direktorijum -", "folderExecRightsText": "Dodelite role koje c<PERSON>e moći da pokreću slučajeve u direktorijumu", "folderExecRightsTextOS": "Dodelite organizacione jedinice koje će moći da pokreću slučajeve u direktorijumu", "folderName": "<PERSON><PERSON>", "font": "Font", "fontMainHeader": "Font glavnog zaglavlja", "form": "Forma", "fourth": "Četvrti", "freeTsk": "Slobodan zadatak", "fri": "<PERSON><PERSON>", "from": "Od", "fsDescription": "opis", "fsName": "ime", "fsTooltip": "objašnjenje", "fullName": "<PERSON><PERSON> ime", "fullScreen": "<PERSON><PERSON>", "getTotalCount": "<PERSON><PERSON><PERSON> broj", "graph": "Grafikon", "handExecutionTaskListEmpty": "Izaberite događaj", "handOver": "<PERSON><PERSON><PERSON>", "handOverToUser": "Predajte korisniku", "handover": "<PERSON><PERSON><PERSON>", "headerDashboard": "Zaglavlje kontrolne table", "help": "<PERSON><PERSON><PERSON>", "hideLogout": "<PERSON><PERSON><PERSON><PERSON>", "hideNewProcess": "Sakrij 'Novi slučaj'", "hideProcs": "<PERSON><PERSON><PERSON><PERSON>", "hideTasks": "<PERSON><PERSON><PERSON><PERSON>", "historicalValues": "Istorijske vrednosti", "currentValues": "Trenutne vrednosti", "history": "Istorija", "home": "Početna", "html": "HTML", "ic": "ID kompanije", "id": "ID", "inCasesNames": "U nazivima slučajeva", "inTasksNames": "U nazivima zadataka", "inDevelopment": "U razvoju", "inEvery": "U svakom", "inFiles": "U datotekama", "initiator": "Inicijator", "inTasks": "U zadacima", "inactive": "Neaktivan", "inactiveShe": "Neaktivna", "incidences": "pojave", "inclusion": "<PERSON><PERSON>jan<PERSON>", "info": "Info", "inputParams": "Ulazni parametri", "insert": "<PERSON><PERSON><PERSON>", "insertAttachTip": "Prevucite i pustite da biste stavili dokument", "insertVar": "Unesite promenljivu", "insertSnippet": "Unesite is<PERSON>", "snippet": "Fragment koda", "insertedBy": "Postavio / la", "insertedOn": "<PERSON><PERSON><PERSON>", "insteadOf": "umesto", "instructions": "Uputstva", "invitation": "Pozivnica", "isEmail": "Nevažeća e-mail adresa", "isEmpty": "prazno", "isExisty": "Ne važi", "isManagerOrgUnit": "Je upravnikom org. jedinicie", "isNotEmpty": "nije p<PERSON>an", "isRequired": "Polje je obavezno", "justSave": "<PERSON><PERSON>", "keepGlobalOrder": "Održavajte globalni redosled", "key": "K<PERSON><PERSON>č", "last": "poslednji", "lastName": "Prezime", "lastOwnerOfTask": "Poslednji vlasnik zadatka", "licenceKey": "Licencni ključ", "link": "Link", "linkConditions": "<PERSON><PERSON><PERSON> veze", "list": "Lista", "listName": "naziv liste", "listOfValues": "Lista vrednosti", "listValue": "vrednost liste", "loading": "Učitavanje ...", "location": "Lokacija", "locked": "Zak<PERSON><PERSON><PERSON><PERSON>", "logIn": "<PERSON><PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON><PERSON>", "logicalType": "Logički tip", "loginError": "Nevažeći podaci za prijavljivanje.", "loginTimeout": "Vreme za prijavljivanje (sek.)", "longText": "Dug tekst", "mailEscalation": "E-mail sa pregledom eskaliranih zadataka", "mailProcEscalation": "E-mail sa pregledom eskaliranih slučajeva", "mailPromptly": "E-mail notifikacija o novom zadatku", "mailPull": "E-mail notifikacija o novom zadatku za povlačenje", "mailTotal": "Rezime e-mail sa pregledom zadataka", "mainButton": "Glavno dugme", "mainColor": "Glavna boja", "mainHeader": "Glavno zaglavlje", "mainLanguage": "<PERSON><PERSON><PERSON>", "manager": "<PERSON><PERSON><PERSON><PERSON>", "managerOfOrgUnit": "Menadžer organizacione jedinice", "mandatory": "Obavezno", "manualStartEvent": "Ručno pokretanje događaja", "mapping": "Mapiranje", "mappingSubProcessVars": "Mapiranje promenljivih podprocesa", "markAll": "Označi sve", "menu": "<PERSON><PERSON>", "mine": "<PERSON><PERSON>", "mobilePhone": "Mobilni telefon", "mon": "Ponedeljak", "month1": "<PERSON><PERSON><PERSON>", "month10": "Oktobar", "month11": "Novembar", "month12": "Decembar", "month2": "<PERSON><PERSON><PERSON>", "month3": "Mart", "month4": "April", "month5": "Maj", "month6": "Jun", "month7": "Jul", "month8": "<PERSON><PERSON><PERSON><PERSON>", "month9": "Septembar", "monthI": "mesec dana", "monthly": "Mesečno", "months": "meseci", "more": "<PERSON><PERSON><PERSON><PERSON>", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingle", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "<PERSON><PERSON><PERSON><PERSON> instanci", "myUnfinishedTasks": "<PERSON><PERSON>", "name": "Ime", "nested": "Ugnježdeni", "never": "Nikad", "new": "Novi", "newCase": "Novi slučaj", "newFolder": "Direktorijum – Novo", "newForm": "Nova forma", "newIt": "Novi", "newName": "Novo ime", "newShe": "Nova", "newSolver": "Novi vlasnik zadatka", "no": "Ne", "noAttach": "<PERSON><PERSON> (kliknite da biste dodali)", "clickToAddAttach": "Klikni<PERSON> da dodate", "noName": "<PERSON><PERSON> imena", "noOneBeOffered": "niko, zadatak će biti ponuđen ograničenoj grupi korisnika", "noPageRights": "Nemate dozvolu za pregled ove stranicu.", "node": "<PERSON><PERSON>", "notFound": "<PERSON><PERSON>", "notMatch": "<PERSON>e poklapaju se", "notNumber": "<PERSON><PERSON> broj", "notIntNumber": "<PERSON>je ceo broj", "notValid": "Ne važi", "notes": "Beleške", "notesOnContacts": "Beleške o kontaktima", "notice": "Na<PERSON>men<PERSON>", "notification": "Obaveštenje", "nrOfItems": "<PERSON><PERSON><PERSON>ta<PERSON>", "number": "<PERSON><PERSON><PERSON>", "numberList": "Spisak vrednosti brojeva", "ok": "OK", "oneMustBeMet": "<PERSON><PERSON> da se ispuni bar jedan", "onlyOrgUnit": "Samo organizaciona jedinica", "onlyPlanGuarantor": "Samo garant plana", "openAll": "Otvori sve", "operating": "Aktivan", "order": "<PERSON><PERSON><PERSON><PERSON>", "orderByColumn": "Sortiraj po kolonama", "orgName": "<PERSON><PERSON> predmeta", "orgStructure": "Org. struk<PERSON>", "orgUnit": "org. jedinica", "orgUnitE": "org. jedinica", "orgUnitName": "Naziv org. jedinice", "orgUnitShe": "Org. jedinica", "orgUnits": "Org. jedinice", "organization": "Organizacija", "overview": "Pregled", "overviewMapping": "Mapiran<PERSON>", "overviewNew": "Pregled – novi", "overviewSetSharing": "Podesite deljenje pregleda za svaku grupu korisnika", "overviews": "<PERSON><PERSON><PERSON>", "owner": "Vlasnik", "ownerWithLeastTasks": "Vlasnik zadatka s najmanjim brojem zadataka", "pageNotFound": "Stranica nije pronađena", "parentFolder": "Matični direktorijum", "parentUnit": "Matična jedini<PERSON>", "participants": "Učesnici", "password": "Lozinka", "passwordChallenge": "Obaveštenje", "passwordChallengeText": "Da li zaista želite da obavestite sve korisnike da promene lozinku?", "passwordChange": "<PERSON><PERSON><PERSON> lo<PERSON>", "passwordCheck": "Lozinka (provera)", "passwordNew": "Nova lozinka", "passwordNewCheck": "Nova lozinka (provera)", "paused": "Neaktivan", "personInOrgStr": "Dodeljena od strane osobe u org. strukturi", "phone": "Telefon", "photo": "Slika", "plan": "Plan", "planGuarantor": "G<PERSON><PERSON> plana", "planTitle": "Plan", "plans": "Planiranje", "plnOffType": "Ponoviti", "plnOrgUnit": "Organizaciona jedinica", "plnTProc": "Šablon slučaja", "plnUser": "Sponzor plana", "plnUsersSelect": "Ograničavajući uslovi za izbor jednog ili više inicijatora", "prependTsk": "Dodaj zadatak na početak", "prependedTsk": "Početni zadatak", "primaryKey": "Primarni ključ", "print": "Štampaj", "printTemplate": "Odštam<PERSON><PERSON>", "printType": "<PERSON>rst<PERSON> š<PERSON>", "printer": "Štampa - HTML", "priority": "Prioritet", "procDescription": "Opis procesa", "procDueDateFinish": "Rok za završetak slučaja", "procName": "<PERSON><PERSON>", "procOwner": "Vlasnik procesa", "procSummary": "<PERSON><PERSON><PERSON> se", "process": "Proces", "processName": "Naziv procesa", "property": "Vlasnik", "quickFilter": "<PERSON><PERSON>i filter", "radioButtonList": "RadioButtonList", "reEvaluates": "Ponovo procenite", "recalc": "Ponovo izračunajte", "recipient": "<PERSON><PERSON><PERSON>", "recipientsId": "ID primaoca", "records": "Zapisi", "referenceUser": "<PERSON><PERSON><PERSON><PERSON> osoba", "refresh": "Osveži", "registered": "Registrovan", "relatToPlanSponsor": "Odnos prema sponzoru plana", "remove": "Ukloniti", "removeVice": "Uklonite zamenu", "renameCols": "Preimenuj kolone", "repeatLogin": "Ponovite prijavu ili odaberite drugu vrstu autentifikacije.", "repeatOrReport": "Pokušajte ponovo kasnije ili se obratite administratoru.", "repetition": "Ponavljanje", "required": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Resetovanje", "restrictTaskOwners": "Ograničenja za vlasnike zadataka", "restrictUsers": "Ograničite korisnike", "returnSubProcessVars": "Vraćanje promenljivih podprocesa", "revision": "Revizija", "right": "Pravo", "rightOrDuty": "Pravo / Dužnost", "role": "rola", "roleName": "Naziv role", "roleSg": "Rola", "roles": "Role", "row": "red", "rule": "Pravil<PERSON>", "ruleCSVFile": "Naziv CSV datoteke", "ruleCSVHeader": "Prvi red CSV datoteke je zaglavlje", "ruleCSVMask": "Maska imena CSV datoteke", "ruleCSVSeparator": "Odvajač kolona", "ruleNew": "Pravilo – Novo", "ruleParamsMap": "Mapiranje promenljivih", "ruleProcOwnCSV": "Definisano u mapiranju", "ruleTypeCSVExpProcs": "Izvezite sve slučajeve u CSV", "ruleTypeCSVMrgProcs": "Prema CSV-u, pokrenite slučajeve i ažurirajte promenljive slučajeva", "ruleTypeCSVRunProcs": "Pokrenite slučajeve prema CSV-u", "ruleTypeCSVUpdProc": "Ažurirajte promenljive slučaja prema CSV-u ", "ruleTypeCSVUpdProcs": "Ažurirajte promenljive slučajeva prema CSV-u", "ruleTypeCSVUpdateList": "Ažurirajte dinamičku listu prema CSV -u", "ruleTypeReturn": "Odgovor na događaj", "ruleTypeUpdateListOfProcesses": "Ažurirajte dinamičku listu procesa", "rules": "<PERSON><PERSON><PERSON>", "run": "Pokreni", "runProcess": "Pokrenite proces", "running": "U toku", "sat": "Subota", "save": "Sa<PERSON><PERSON>vat<PERSON>", "saveAsAttachment": "Sačuvaj štampu kao dokument u slučaju", "scheduling": "Zakazivanje", "scheme": "Vizuelni identitet", "script": "Skripta", "scripts": "Skripte", "search": "Pretraga", "searchResult": "Rezultat pretrage", "second": "Drugo", "secondLeft": "Drugo levo", "secondRight": "<PERSON><PERSON>", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "Vlasnicima zadataka koje će računar automatski izabrati", "selectedByTaskSupervisor": "Vlasnicima zadataka koje bira nadzornik", "selectedPrint": "<PERSON><PERSON><PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "sendingFailed": "Greška", "sendOn": "Vreme za slanje", "sendTestMail": "Test", "selectedUser": "Izabrani korisnik", "sequence": "<PERSON>z", "setDefault": "<PERSON><PERSON> kao pod<PERSON>o", "setVice": "<PERSON><PERSON>", "setViceAttachmentsNotes": "Pravo na prenos dokumenata i beleški", "settings": "Podešavanja", "shortcuts": "Prečice", "showAttachmentsClick": "Klikom ćete prikazati dokumente", "showCommentCol": "Prikažite kolonu s komentarima", "skype": "Skype", "solve": "Reš<PERSON> zada<PERSON>", "solvedBy": "Rešeno od", "solver": "Vlasnik zadatka", "sort": "<PERSON><PERSON><PERSON><PERSON>", "sortByColumn": "Sortirati po koloni", "sorting": "Sortiranje", "sourceTask": "Izvorni zadatak", "sourceVar": "Izvorna promenljiva", "start": "Početak", "startDate": "<PERSON>tum <PERSON>", "startCalDate": "<PERSON>tum <PERSON>", "endCalDate": "<PERSON><PERSON>", "state": "Država", "stateAddress": "Država", "status": "Status", "street": "Ulica i broj", "subProcess": "Podproces", "subject": "Predmet", "substitute": "Zamena", "sun": "<PERSON><PERSON><PERSON>", "superior": "Pretpostavljeni", "supervis": "Nadzornik", "supervisor": "Nadzornik zadatka", "suspend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "suspended": "Obustavljen", "suspendedx": "Obustavljen", "tTaskAgain": "Ponašanje pri ponovnoj aktivaciji", "tTaskAutoCompleteCaption": "Zadatak će se automatski ispuniti ako", "tTaskCompletionCOA": "svi uslovi su ispunjeni istovremeno", "tTaskCompletionCOO": "ispunjen je bar jedan uslov", "tTaskDueOffsetNone": "odmah", "tTaskDueOffsetPO": "dodeljuje pre<PERSON>postavl<PERSON>ni", "tTaskDueOffsetPS": "u roku od nekoliko dana od početka slučaja", "tTaskDueOffsetTS": "u roku od nekoliko dana od mogućeg početka aktivnosti", "tTaskDueOffsetVC": "od promen<PERSON><PERSON><PERSON><PERSON>", "tTaskDueOffsetVO": "od promenljivih pri pokretanju", "tTaskInvClassConf": "Tajn<PERSON>", "tTaskInvClassPriv": "Privatno", "tTaskInvClassPubl": "Javno", "tTaskInvPriority1": "1-naj<PERSON><PERSON><PERSON>", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-<PERSON><PERSON><PERSON><PERSON><PERSON>", "tTaskInvokeEventB": "u pozadini", "tTaskInvokeEventI": "odmah", "tTaskReferenceUserLastSolver": "Poslednji vlasnik zadatka", "tTaskReferenceUserMan": "Menadžer org. jedinice XY", "tTaskReferenceUserUser": "Korisnik XY", "tTaskRunOnlyOnce": "Pokreni samo jednom", "tTaskSufficientEnd": "Završetak će okončati slučaj", "tabName": "Naziv kartice", "table": "<PERSON><PERSON><PERSON>", "takeOnlyOrder": "Uzmite samo redosled", "takeover": "<PERSON><PERSON><PERSON>", "targetTask": "Ciljni zadatak", "targetVar": "<PERSON><PERSON><PERSON>", "taskAutomatic": "automatski status", "taskEmailNotification": "E-mail notifikacija", "taskEvent": "pok<PERSON><PERSON><PERSON><PERSON> doga<PERSON>aj", "taskEventWait": "čeka događaj", "taskOwner": "Vlasnik zadatka", "taskSolverAssign": "za dodelu vlasniku zadatka", "taskStart": "Početak", "taskStatus": "Status", "taskStatusA": "Aktivan", "taskStatusAP": "Aktivni podproces", "taskStatusAS": "Uspavan podproces", "taskStatusD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskStatusL": "Čekanje", "taskStatusLEdit": "<PERSON><PERSON> mog<PERSON>e urediti zadatak na čekanju", "taskStatusN": "Novi", "taskStatusP": "<PERSON><PERSON><PERSON>", "taskStatusS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskStatusT": "Povući", "taskStatusW": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "taskStatusWT": "Za zakazivan<PERSON>", "taskSubprocess": "implementirano podprocesom", "taskTabVariables": "Dodeljene promenljive", "taskType": "Vrsta zadatka", "taskWillBeAssigned": "Zadatak će biti do<PERSON>n", "tasks": "<PERSON><PERSON><PERSON>", "tasksToPull": "Zadaci za povlačenje", "taskstatusAD": "Aktivan i završen", "tempId": "<PERSON> šablone", "tempVar": "šablona", "template": "Šablona", "templateDeleted": "Izbrisano", "templateStatus": "Status šablona", "templates": "<PERSON><PERSON><PERSON>", "templatesFolder": "Šablon - direktorijum", "testForm": "Obrazac za testiranje", "tested": "Testirano", "text": "Tekst", "textList": "Spisak tekstualnih vrednosti", "textMultipleLines": "Tekst sa više redova", "textSuggest": "Predl<PERSON>č", "third": "<PERSON><PERSON><PERSON><PERSON>", "thirdCenter": "Treći centar", "thu": "Četvrtak", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "to": "Do", "toHide": "<PERSON><PERSON><PERSON><PERSON>", "toInclusive": "Do (uključujući)", "toPull": "Zadaci za povlačenje", "tooltip": "Objašnjenje", "total": "ukupno", "tprocName": "Šablon procesa", "tsk": "Zadatak", "tskAssignDues": "Podesite vremenska ograničenja za ovaj zadatak", "tskName": "Naziv zadatka", "tskNum": "<PERSON><PERSON><PERSON>", "tskSolver": "Vlasnik zadatka", "tskTemplate": "Šablon zadatka", "tskVar": "zadatak", "tsksDone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tsksSolvers": "Vlasnici zadataka", "ttAdd": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućava dodavanje nove stavke ili novih parametara koji još nisu definisani."}, "ttAddActivity": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddAttach": {"heading": "Dodati dokument", "body": "Omogućava dodavanje novog dokumenta."}, "ttAddAttribute": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddContainer": {"heading": "<PERSON><PERSON><PERSON>", "body": "Dodaje sadržalac sa izabranim sadržajem"}, "ttAddFile": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddStructure": {"heading": "Dodati stavku u organizacionu strukturu", "body": "Omogućava dodavanje nove stavke organizacione strukture ili novih parametara koji još nisu definisani."}, "ttAddTemp": {"heading": "<PERSON><PERSON><PERSON> novi <PERSON>", "body": "Kreiranje novih šablona slučajeva. Vlasnik šablona će biti korisnik koji je trenutno prijavljen. Šablonu se automatski dodeljuje status \"u razvoju\"."}, "ttAddTsk": {"heading": "Dodati novi zadatak", "body": "Kreiranje novog zadatka u okviru šablone procesa. Parametri zadatka se mogu navesti u zavisnosti od vrste zadatka. Veze do drugih zadataka mogu se dodavati ili uređivati u karticama Grafikon ili Link."}, "ttAddTskGraph": {"heading": "Dodati novi zadatak", "body": "Kreiranje novog zadatka u okviru šablone procesa. Parametri zadatka se mogu navesti u zavisnosti od vrste zadatka. Veze do drugih zadataka mogu se dodavati ili uređivati u karticama Grafikon ili Link."}, "ttAddUser": {"heading": "Dodati novog korisnika", "body": "Dodati novog korisnika. Svaki korisnik mora imati jedinstveno korisničko ime. Korisnicima se mogu postaviti osnovne informacije, zajedno sa njihovim dodeljivanjem organizacionoj strukturi i dodeljivanjem roli. Novim korisnicima se automatski daje zaključan status."}, "ttAddVar": {"heading": "Dodati novu promenljivu", "body": "Kreiranje nove promenljive u šablonu slučaja. Svaka promenljiva sadrži informacije sa kojima mogu raditi vlasnici zadataka slučaja. Moguće je navesti ime, tip i podrazumevane vrednosti promenljive."}, "ttAddVice": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAssignAttribute": {"heading": "Dodela atributa logičkom tipu dokumenta", "body": ""}, "ttAssignTsk": {"heading": "Dodeliti", "body": "Omogućava dodeljivanje zadatka određenom vlasniku zadatka ili dodavanje stavke u definisanu strukturu."}, "ttCases": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttOverviews": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttChangePass": {"heading": "<PERSON><PERSON><PERSON> lo<PERSON>", "body": "Uređivanje lozinki korisnika, kojima se upravlja direktno u okruženju aplikacije. Ako korisnicima upravlja spoljna usluga (LDAP), lozinkom se mora upravljati tamo."}, "ttClose": {"heading": "Zatvoriti", "body": "Prozor će biti zatvoren bez čuvanja promena."}, "ttCloseTemp": {"heading": "Zatvoriti", "body": "Prozor sa definicijom šablona će biti zatvoren."}, "ttCompleteTsk": {"heading": "Završiti zadatak", "body": "Potvrđuje da je zadatak obavljen i šalje ga na dalju obradu kao što je unapred definisano."}, "ttContact": {"heading": "Kontakt", "body": "Prikazuje kontakte za nadzornika zadataka."}, "ttContainerSettings": {"heading": "Podešavanja", "body": "Omogućava promenu postavki za dati sadržalac."}, "ttCopyHdr": {"heading": "Kopirati zaglavlje", "body": "Kreira se kopija izabranog zaglavlja. Odabir zaglavlja vrši se klikom u tabeli zaglavlja šablone."}, "ttCopyTemp": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Pravljenje kopije izabranog šablona. Odabir šablona vrši se klikom u tabeli šablona procesa."}, "ttCopyVar": {"heading": "Kopija promenljive", "body": "Kopija definicije za izabranu promenljivu i čuvanje promenljive pod novim imenom. Promenljive se biraju klikom u tabeli promenljivih."}, "ttDel": {"heading": "Izbrisati", "body": "B<PERSON>š<PERSON> izabranu stavku."}, "ttDelAttach": {"heading": "Izbrisati dokument", "body": "Briše izabrani dokument."}, "ttDelConnection": {"heading": "Izbrisati vezu", "body": "Izbrišite izabranu vezu između dva zadatka slučaja. Brisanje mora da se potvrdi. Brisanje je izvršeno za izabranu vezu. Izaberite vezu klikom na nju u tabeli veza."}, "ttDelFolder": {"heading": "Brisanje <PERSON>ori<PERSON>", "body": "Brisanje izabranong direktorijuma."}, "ttDelOverview": {"heading": "Izbrisati pregled", "body": "<PERSON><PERSON><PERSON><PERSON> pregled."}, "ttDelTemp": {"heading": "Izbrisati šablon", "body": "Dodeljuje izbrisani status šablonu. <PERSON>o kada se zahtev za brisanje ponovi, šablon se fizički uklanja. Radnja se primenjuje na izabrani šablon. Izaberite šablon klikom na njega u tabeli šablona."}, "ttDelTsk": {"heading": "Brisanje zadat<PERSON>", "body": "Uklanjanje izabranog zadatka. Brisanje mora da se potvrdi. Zajedno sa zadatkom biće uklonjene sve povezane veze do drugih zadataka u šablonu procesa. Izaberite zadatak klikom na njega u tabeli zadataka."}, "ttDelTskOrConnection": {"heading": "Izbrisati zadatak ili vezu", "body": "Uklanjanje izabranog zadatka ili izabrane veze između dva procesna zadatka. Ova radnja mora biti potvrđena. Srodne veze sa drugim procesnim zadacima biće uklonjene zajedno sa zadatkom. Kliknite da biste izabrali. "}, "ttDelVar": {"heading": "Brisanje promenljive", "body": "Brisanje izabrane promenljive. Ova radnja mora biti potvrđena. Promenljiva više neće biti dostupna za pojedinačne zadatke procesa. Promenljive se biraju klikom u tabeli promenljivih."}, "ttDelVice": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttDetailCase": {"heading": "<PERSON><PERSON><PERSON>", "body": "Prikazuje detalje izabranog slučaja."}, "ttDetailCertificate": {"heading": "<PERSON><PERSON><PERSON> sert<PERSON>", "body": "Prikazuje detalje izabranog sertifikata."}, "ttDetailHistory": {"heading": "<PERSON><PERSON><PERSON>", "body": "Prikazuje detalje o izabranoj stavci."}, "ttDetailTsk": {"heading": "<PERSON><PERSON>j <PERSON>", "body": "Prikazuje detalje izabranog zadatka."}, "ttDmsFolderAdd": {"heading": "Dodati novi direktorijum", "body": "Umetnite novu fasciklu. Ako je izabrana fascikla, fascikla je unapred popunjena kao nadređena fascikla."}, "ttDmsFolderEdit": {"heading": "Izmeniti direktorijum", "body": "Uredite izabrani direktorijum."}, "ttDocuments": {"heading": "Skladištenje dokumenata", "body": ""}, "ttDownload": {"heading": "<PERSON>uzimanje", "body": "Preuzima izabranu da<PERSON>."}, "ttDropContainer": {"heading": "Izba<PERSON><PERSON>", "body": "Izbacuje sadržaoca sa komandne table"}, "ttENotification": "E-mail notifikacija", "ttEdit": {"heading": "Urediti", "body": "Omogućava uređivanje izabrane stavke."}, "ttEditAttach": {"heading": "Urediti", "body": "Omogućava pregled i uređivanje atributa (metapodataka) otpremljene datoteke."}, "ttEditConnection": {"heading": "Uređivanje veza", "body": "Uređivanje veze između dva zadatka. Moguće je urediti parametre ponašanja veze i uslove veze. Radnja se primenjuje na izabranu vezu. Veze se biraju klikom na njih u tabeli veza."}, "ttEditOverview": {"heading": "Uređivan<PERSON> pregleda", "body": "Omogućava uređivanje izabranog pregleda."}, "ttCopyOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Pravljenje kopije izabranog pregleda."}, "ttEditPath": {"heading": "<PERSON><PERSON><PERSON> pre<PERSON>", "body": "Omogućava definiciju novog pregleda."}, "ttEditTemp": {"heading": "Uređivanje definicije šablona", "body": "Uređivanje šablone slučaja. Bilo koji parametar šablone se može urediti. Radnja se vrši za izabrani šablon. Izaberite šablon klikom u tabeli šablona slučajeva."}, "ttEditTsk": {"heading": "Uređivanje zadatka", "body": "Uređivanje informacija o zadacima i parametara zadatka. Radnja se primenjuje na izabrani zadatak. Izaberite zadatak klikom u tabeli zadataka."}, "ttEditTskOrConnection": {"heading": "Uređivanje zadataka ili veza", "body": "Uređivanje informacija o zadacima i parametara zadatka ili uređivanje veza između dva zadatka, njihovih parametara ponašanja i uslova veze. Radnja se primenjuje na izabrani zadatak ili vezu. Kliknite da biste izabrali."}, "ttEditTskVars": {"heading": "Urediti", "body": "Uređivanje promenljivih zadataka"}, "ttEditUser": {"heading": "Uređivanje podataka o korisniku", "body": "Uređivanje osnovnih podataka o korisnicima, lozinki, dodeljivanju organizacionoj jedinici i dodeli rola. Radnja se primenjuje na izabranog korisnika. Korisnici se biraju klikom u tabeli korisnika."}, "ttEditVar": {"heading": "Uređivanje promenljive", "body": "Uređivanje naziva, vrste i podrazumevanih vrednosti promenljivih. Radnja se primenjuje na izabranu promenljivu. Promenljive se biraju klikom u tabeli promenljivih."}, "ttEnotTgt": "<PERSON><PERSON><PERSON>", "ttEnotTgtG": "Nadzornik zadatka", "ttEnotTgtO": "Vlasnik slučaja", "ttEnotTgtP": "%s", "ttEnotTgtR": "Rola %s", "ttEnotTgtS": "Organizaciona jedinica %s", "ttEnotTgtT": "Vlasnik zadatka %s", "ttEvent": {"heading": "Prilagođeni zadatak", "body": "Trenutno pozivanje događaja u ovom zadatku."}, "ttEvents": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Postavljanje poslovnih pravila za reagovanje na definisane interne ili eksterne događaje u sistemu. Pristup zahteva ulogu $PowerUser."}, "ttFavourites": {"heading": "Lista favorita", "body": "Lista svih favorita sa opcijom da ih uredite ili izbrišete sa liste."}, "ttFilter": {"heading": "Filter", "body": "Prikazuje samo one stavke koje ispunjavaju definisane uslove filtera."}, "ttFilterPrc": {"heading": "Filter", "body": "Prikazuje samo one slučajeve koje ispunjavaju definisane uslove filtera."}, "ttFilterTemp": {"heading": "Filter", "body": "Prikazuje samo one šablone koje ispunjavaju definisane uslove filtera."}, "ttFilterTsk": {"heading": "Filter", "body": "Prikazuje samo one zadatke koji ispunjavaju definisane uslove filtera."}, "ttFilterUser": {"heading": "Filter", "body": "Prikazuje samo one korisnike koji ispunja<PERSON>ju definisane uslove filtera."}, "ttFullScreen": {"heading": "<PERSON><PERSON>", "body": "Prikazuje elemente sadržaoca u režimu celog ekrana."}, "ttGraph": {"heading": "Grafikon", "body": "Grafički prikaz trenutnog statusa slučaja."}, "ttGraphActualFinish": "Stvarni završetak", "ttGraphActualStart": "<PERSON><PERSON><PERSON> datum poč<PERSON>ka", "ttGraphCond": "<PERSON><PERSON><PERSON>", "ttGraphCond1": "bar jedan mora biti ispunjen", "ttGraphCondAll": "svi moraju biti ispunjeni", "ttGraphCondElse": "Osim ako nije ispunjen drugi uslov", "ttGraphDeadlinePo": "Rok: unesen od strane vlasnika slučaja", "ttGraphDeadlinePs": "Rok: u roku od %s dana od pokretanja slučaja", "ttGraphDeadlineTs": "Rok: u roku od %s dana od pokretanja zadatka", "ttGraphDelayPo": "Pokretanje zadatka: uneseno od strane vlasnika slučaja", "ttGraphDelayPs": "Pokretanje zadatka: %s dana od pokretanja slučaja", "ttGraphDelayTs": "Pokretanje zadatka: %s dana od pokretanja zadatka", "ttGraphEnd": "Dovršavanjem zadatka završava se čitav slučaj", "ttGraphFinishedBy": "Završen od strane", "ttGraphHiearchyA": "svi nadređeni supervizora zadatka", "ttGraphHiearchyC": "direktni podređeni supervizora zadatka", "ttGraphHiearchyD": "svi podređeni supervizora zadatka", "ttGraphHiearchyG": "Supervizor zadatka", "ttGraphHiearchyL": "svi", "ttGraphHiearchyP": "direktni nadređeni supervizora zadatka", "ttGraphHiearchyS": "saradnici supervizora zadatka", "ttGraphLinkFrom": "Od", "ttGraphLinkTo": "Do", "ttGraphMethodL": "poslednjem vlasniku zadatka %s", "ttGraphMethodS": "vlasniku zadatka po izboru supervizora", "ttGraphMethodT": "vlasniku zadatka koji će biti izabran automatski", "ttGraphMethodV": "vlasniku zadatka dodeljenom promenljivoj %s", "ttGraphMultiinstance": "Multi-instance", "ttGraphNoneMand": "Obavezan link", "ttGraphOnlyOnce": "Pokrenuti samo jednom", "ttGraphSave": {"heading": "Sačuvaj dijagram i napravi šablon", "body": ""}, "ttGraphStart": "Zadatak će se automatski aktivirati nakon pokretanja slučaja", "ttGraphTaskHiearchy": "Vlasnik zadatka", "ttGraphTaskMethod": "Zadatak će biti dodeljen", "ttGraphTaskOwner": "Supervizor zadatka", "ttGraphTaskOwnerOS": "Menadžer organizacione jedinice", "ttGraphTaskOwnerPO": "Vlasnik slučaja", "ttGraphTaskOwnerSU": "Izabrani korisnik", "ttGraphTaskRole": "sa rolom", "ttGraphTaskTypeA": "Automatski zadatak", "ttGraphTaskUser": "Vlasnik zadatka", "ttGraphWait1": "Ulazni parametri: čeka se jedan", "ttGraphWaitA": "Ulazni parametri: čekaju se svi", "ttGraphWaitFirst": "Ulazni parametri: <PERSON><PERSON><PERSON> se svi, prvi se pokreću", "ttGraphWaitN": "Ulazni parametri: čeka se %s", "ttHandover": {"heading": "Predajte zadatak", "body": "Omogućava predaju zadatka drugom dostupnom korisniku."}, "ttDelegate": {"heading": "Delegirajte zadatak", "body": ""}, "ttReject": {"heading": "Odbacite zadatak", "body": ""}, "ttHelp": {"heading": "<PERSON><PERSON><PERSON><PERSON> pomo<PERSON>", "body": "Dozvoljavanje ili onemogućavanje trenutne pomoći. Pomoć je prikazana u obliku balončića koji iskaču sa informacijama o korisničkom interfejsu kada se pređe mišem preko stavke."}, "ttHome": {"heading": "Početna stranica korisnika", "body": "Jedno mesto sa svim informacijama za redovne korisnike. Kontrolna tabla pruža opšti prikaz."}, "ttHtml": {"heading": "Generišite dokumentaciju", "body": "Generisanje HTML dokumentacije za proces šablona. U zavisnosti od vrste pregledača, dokument se može odmah prikazati ili sačuvati na disk."}, "ttInclusion": {"heading": "Uključivanje", "body": "Izvozi datoteku sa sažetkom ovlašćenja i ulogama korisnika, svim korišćenim potpisima, entitetima organizacije u kojima je član ili menadžer, uključujući hijerarhiju zadataka u kojima je on supervizor."}, "ttInvAttendees": "Učesnici", "ttInvDTEnd": "<PERSON><PERSON>", "ttInvDTStart": "Početak", "ttInvLocation": "Lokacija", "ttInvitation": "Pozivnica", "ttJustSave": {"heading": "<PERSON><PERSON>", "body": "Sačuvati promene."}, "ttLock": {"heading": "Zaključati", "body": "Zaključaj ili otključaj izbor"}, "ttLockUser": {"heading": "Zaključati", "body": "Zaključaj ili otključaj korisnika"}, "ttLogout": {"heading": "Odjavljivanje", "body": "Odjavljivanje korisnika. Nakon uspešnog završetka rada sa aplikacijom prikazuje se početni dijalog za prijavljivanje."}, "ttMapping": {"heading": "Mapiranje", "body": "Opšti pregled dodeljenih promenljivih za čitanje (R), pisanje (W) i obavezan unos (M) u pojedinačne zadatke sa mogućnošću uređivanja njihovog dodeljivanja."}, "ttNewCase": {"heading": "Novi slučaj", "body": "Kreiranje nove instance procesa - novi slučaj. Moguće je izabrati jedan od dostupnih šablona procesa ili kreirati slučaj bez unapred definisane strukture zadataka."}, "ttNewOverview": {"heading": "<PERSON><PERSON><PERSON> pre<PERSON>", "body": "Omogućava definiciju novog pregleda."}, "ttOrgStructure": {"heading": "Organizaciona struktura", "body": ""}, "ttParent": {"heading": "Superior", "body": "Pređite na slučaj iz kojeg je prikazani slučaj kreiran kao podproces."}, "ttPhoto": {"heading": "Fotografije", "body": "Otpremanje fotografija na korisnički profil. Podržava GIF, JPG i PNG formate. Veličina slike će se automatski prilagoditi."}, "ttPlans": {"heading": "Zakazivanje", "body": "Postavljanje pravila za automatsko pokretanje jednokratnih ili ponovljenih instanci procesa-slučajeva prema navedenim parametrima. Pristup zahteva rolu $Administrator."}, "ttPrint": {"heading": "Štampati", "body": "Kreiranje š<PERSON>."}, "ttRecalc": {"heading": "Ponovno izračunavanje", "body": "Ponovo izračunajte trenutne promenljive."}, "ttRedirectToPrc": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttResetDash": {"heading": "Resetovanje", "body": "Resetuje izvršene promene."}, "ttResetSearch": {"heading": "Resetovanje", "body": "Resetuje obrazac za pretragu."}, "ttRestoreTemp": {"heading": "Vrać<PERSON><PERSON>", "body": "Vraća izbrisani šablon."}, "ttRevision": {"heading": "Revizija", "body": "Omogućava otpremanje nove verzije datoteke"}, "ttRoles": {"heading": "<PERSON>ra<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "body": ""}, "ttRunEvent": {"heading": "Pokreni događ<PERSON>", "body": "Pozivanje događaja u ovom slučaju"}, "ttSave": {"heading": "Sa<PERSON><PERSON>vat<PERSON>", "body": "Čuvanje promena i zatvaranje prozora."}, "ttSaveDMSCols": {"heading": "Sačuvati kolone", "body": ""}, "ttSaveSettings": {"heading": "Sa<PERSON><PERSON>vat<PERSON>", "body": "Sačuvati promene."}, "ttSaveTsk": {"heading": "<PERSON><PERSON>", "body": "Otvoreni zadatak će biti sačuvan tako da se kasnije možete vratiti na njega."}, "ttSearch": {"heading": "Pretraživanje", "body": "Počinje pretraživanje"}, "ttSendNote": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućava dodavanje nove beleške."}, "ttSetConnectionCond": {"heading": "<PERSON><PERSON>", "body": "Dodavanje ili uređivanje uslova veze. Uređivanje se primenjuje na izabranu vezu. Kliknite na vezu ili simbol uslova da biste izabrali.."}, "ttSetDefaultDash": {"heading": "Podesi kao podrazumevanu komandnu tablu", "body": "Postavlja trenutni raspored komandne table kao podrazumevani"}, "ttShowHideBtn": {"heading": "Prikaži / sakrij", "body": "Delimično skriva ili prikazuje glavni meni."}, "ttSleepCase": {"heading": "Obustaviti sluč<PERSON>", "body": "Obeležava slučaj kao suspendovan. Slučaj se više neće prikazivati među aktivnim zadacima, ali ako je potre<PERSON>, moguće je vratiti status na aktivan i završiti ceo slučaj kasnije."}, "ttSolve": {"heading": "Otvori zadatak", "body": "Prikazuje dijalog koji omogućava rad na dodeljenom zadatku prema unapred definisanom šablonu."}, "ttStatePlan": {"heading": "Status", "body": "Definiše status plana."}, "ttStatusHdr": {"heading": "Promena statusa zaglavlja", "body": "Radnja se primenjuje na izabrani šablon. Dostupna su stanja \"aktivni\" i \"neaktivni\". Odabir zaglavlja se vrši klikom u tabeli zaglavlja šablona."}, "ttStatusTemp": {"heading": "Promena statusa šablona", "body": "Upravljanje životnim ciklusom šablona se vrši podešavanjem njegovog stanja. Postoje stanja \"u razvoju\", \"aktivno\", \"neaktivno\" i \"izbrisano\". Radnja se primenjuje na izabrani šablon. Odabir šablona se vrši klikom na tabelu šablona."}, "ttSubprocess": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Prelazi na slučaj koji je kreiran kao podproces u procesu prikazanog slučaja."}, "ttTabsButtonMore": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Prikazuje više opcija."}, "ttTakeTsk": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućava da zadatak preuzme drugi vlasnik."}, "ttTemps": {"heading": "Šablone procesa", "body": "Centralno mesto za upravljanje šablonima procesa. Pristup zahteva rolu $PowerUser."}, "ttTiming": {"heading": "Zakazivanje", "body": "Unesite početak i kraj zadatka."}, "ttTsks": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttUploadSettings": {"heading": "Otpremiti", "body": ""}, "ttUserSetting": {"heading": "Korisnička podešavanja", "body": "Podešavanje podataka o kontaktu korisnika, lozinki za pristup i korisničkih preferencija. Korisnici sa ulogom $Administrator mogu dalje upravljati podacima o svojoj organizaciji i instancama aplikacije TeamAssistant."}, "ttUsers": {"heading": "Administracija korisnika", "body": "Centralna administracija korisnika, organizacionih struktura i rola korisnika. Pristup zahteva ulogu $Administrator."}, "ttValidation": {"heading": "Validacija", "body": "Potvrdite šablon i pogledajte sve postojeće petlje unutar šablona. Obaveštava o neispunjenim uslovima i neiskorišćenim promenljivim."}, "ttViewFile": {"heading": "Pogled", "body": ""}, "ttWakeUpCase": {"heading": "Probuditi", "body": ""}, "ttActivateCase": {"heading": "Aktivirati", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Podesite podrazumevane DMS kolone", "body": "Postavlja dodeljivanje DMS kolona kao podrazumevano."}, "ttResetDmsCols": {"heading": "Resetovanje", "body": "Resetovanje dodeljivanja DMS kolona."}, "ttRestoreDoc": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Povratiti izbrisani dokument."}, "ttSearchHeader": {"heading": "Pretraga", "body": ""}, "tue": "Utorak", "type": "Type", "typeOfRepetition": "Vrsta ponavljanja", "unassignedSolvers": "Prema vlasnicima zadataka", "unassignedTaskSolvers": "Nedodeljeni vlasnici zadatka", "uncategorized": "Nekategorisano", "unfinishedProcesses": "Nedovršeni <PERSON>i", "unknown": "Nepoznato", "unknownUser": "Nepoznati korisnik", "unrestricted": "Neogra<PERSON><PERSON><PERSON>", "unspecified": "Unspecified", "upload": "Otpremiti", "uploadFile": "Otpremiti datoteku", "uploadPhoto": "Otpremiti fotografiju", "uploadCsv": "Otpremiti CSV", "url": "URL", "urlAddress": "URL adresa", "urlContent": "URL sadržaj", "use": "Upotreba", "user": "<PERSON><PERSON><PERSON>", "userByOwnerOfLastTask": "Korisniku po izboru poslednjeg vlasnika zadatka.", "userE": "korisnik", "userFilters": "Korisnički filteri", "userLock": "Zaključati", "userLockUnlockQ": "Da li zaista želite da promenite status korisnika {{username}}?", "userName": "Korisničko ime", "userId": "ID korisnika", "userOrgStruct": "Pripada organizacionoj j<PERSON>", "userVice": "Zamenjen sa", "userViced": "<PERSON><PERSON><PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON>", "usersDeleted": "Izbrisano", "validation": "Validacija", "value": "Vrednost", "var": "pro<PERSON><PERSON><PERSON><PERSON>", "var-": "Promenljiva -", "varChange": "Promena promenljive biće objavljena svim učesnicima slučaja", "varTaskMap": "Mapiranje", "varTemp": "Šablon promenljive", "variable": "Promen<PERSON><PERSON><PERSON>", "variableType": "Tip promenljive", "vars": "Promenljive", "varsForMandatory": "Promenljive za obavezan unos", "varsForReading": "Promenljive za čitanje", "varsForWriting": "Promenljive za zapis", "vices": "Zamene", "viewCVFields": "Dostupna polja", "visForOrgStrMembers": "Vidljivo članovima organizacione grupe", "visForRoleMembers": "Vidljivo članovima sa rolama", "headerVisForRole": "Slučaj je vidljiv članovima sa rolom", "waitForNumOfInputs": "Čeka se: (broj un<PERSON>)", "waitsFor": "čeka", "waitsForAll": "čeka sve", "waitsForOne": "čeka jedan", "waitsForSending": "čeka na slanje", "waitsRunFirst": "čeka sve, kreće prvi", "wakeUp": "Otkazati suspenziju", "warning": "Upozorenje", "wed": "<PERSON><PERSON>", "weekIn": "u nedelji", "weekly": "<PERSON><PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON>", "withConditions": "S uslovima", "withoutCond": "Bez uslova", "year": "godina", "yes": "Da", "zip": "Poštanski broj", "move": "Pomeriti", "alertClosing1": "obaveštenje će se automatski zatvoriti za:", "inDocuments": "U dokumentima", "inVariables": "U Promenljivim", "headerTask": "Zaglavlje zadatka", "planName": "Naziv plana", "inBulk": "<PERSON><PERSON><PERSON>", "confirmResetDmsColumns": "Da li zaista želite da resetujete DMS kolone?", "dmsColsUseDef": "Korišćenje podrazumevanih postavki", "dmsColsUseCust": "Korišćenje prilagođenih postavki", "today": "<PERSON><PERSON>", "alrPlanDeleteFailed": "Brisanje plana nije uspelo.", "notRunning": "Ne radi", "alrLackOfPermsToAddTask": "Nemate dozvole za dodavanje zadatka.", "dragTable": "Prevucite tabelu", "alrDownloadCsvListFailed": "Preuzimanje liste datoteka CSV nije uspelo.", "alrCsvUploadWrongExtension": "Otpremite samo datoteke sa nastavkom *.csv", "addToFav": "<PERSON><PERSON><PERSON> u <PERSON>", "renameItem": "<PERSON><PERSON><PERSON><PERSON>", "removeFromFav": "Ukloni iz favorita?", "alrAddedToFav": "<PERSON><PERSON><PERSON> u favorite.", "alrRemovedFromFav": "Uklonjeno iz favorita.", "tskSetAssignDues": "Podesite vremenska ograničenja za zadatak", "isNot": "nije", "alrTskScheduling": "Zakazivanje zadata<PERSON>…", "alrFavouritesPageExist": "<PERSON>va stranica je već u favoritima.", "alrFavouritesActionExist": "<PERSON><PERSON> akcija je već među favoritima.", "autoFit": "Autofit", "alrFavouriteRenamed": "Favorit je preimenovan.", "passwordIsShort": "Lozinka je prekratka.", "changeAttrComplCases": "Promenite atribute završenih slučajeva", "iterateOverVars": "Ponovite preko promenljivih", "nrOfDecimalDigits": "<PERSON><PERSON><PERSON> de<PERSON><PERSON> cifara", "onlyNumbers": "<PERSON><PERSON> broje<PERSON>", "maxNumberOfDecimals": "<PERSON><PERSON><PERSON><PERSON> broj de<PERSON><PERSON>h cifara je", "alrInsertCsv": "Dodajte CSV datoteku.", "addBefore": "Dodajte pre", "moveBefore": "Pomerite pre", "administration": "Administracija", "ttAdministration": {"heading": "Administracija", "body": ""}, "alrLogsLoadFailed": "Učitavanje log zapisa nije uspelo.", "logs": "<PERSON>g zapisi", "message": "<PERSON><PERSON><PERSON>", "useCompatibleTempl": "Koristite kompatibilni šablon", "overwriteExistTempl": "Prepisati postojec<PERSON> š<PERSON>lon", "addNewTempl": "Dodajte novi <PERSON>", "import": "Import", "export": "Export", "confirmExportAllTempl": "Exportovati sve šablone?", "confirmExportSelTempl": "Exportovati izabrani šablon?", "newLogs": "Novi log zapisi", "container": "Spremnik", "contents": "<PERSON><PERSON><PERSON><PERSON>", "confirmRemoveDialog": "Da li zaista želite da uklonite {{variable}}?", "allMyCases": "Svi moji sluč<PERSON>evi", "maintenanceMsg": "Planirano <span style=\"color: {{color}};\">održavanje</span> je u toku", "alrMaintenanceMsg": "Planirano održavanje je u toku, pokušajte kasnije.", "alrAttachDownloadLackOfPerms": "Nemate dozvole za preuzimanje dokumenta ili dokument nije pronađen.", "unableToConnect": "Nemoguće je povezati se sa serverom", "tryLater": "Probajte kasnije ili se obratite administratoru.", "enableTaskDelegation": "Omogući poveru/predaju zadatka", "enableRejectTask": "Omogući odbijanje zadatka", "confirmRejectTask": "Da li zaista želite da odbijete zadatak?", "rejectTask": "Odbijte zadatak", "delegateTask": "Poveriti", "alrRejectingTask": "Odbijanje z<PERSON>…", "alrTaskRejected": "Zadatak je odbijen.", "alrTaskRejectFailed": "Odbijanje zadatka nije uspelo.", "alrTaskDelegating": "Poveravanje zadatka ...", "alrTaskDelegated": "Zadatak je poveren korisniku:", "alrFailedTaskDelegate": "Poveravanje zadatka nije uspelo.", "delegateOnUser": "Poverite korisnika", "plnAssignmentCond": "<PERSON><PERSON> pol<PERSON> \"<PERSON><PERSON><PERSON>\" o<PERSON>e <PERSON>, biće napravljena lista inicijatora procenom restriktivnih uslova u vreme izvršenja plana", "alrUserFiltersSettingsFailed": "Čuvanje podešavanja korisničkih filtera nije uspelo.", "general": "Opšte", "alrUserPhotoLoadFailed": "Učitavanje korisničke fotografije nije uspelo.", "publicDynTable": "<PERSON><PERSON><PERSON> tabela", "isFullIndexed": "U potrazi", "datetimeIndexed": "Indeksirano na", "toIndex": "<PERSON>a <PERSON>dek<PERSON>", "toReindex": "Za ponovno indeksiranje", "solverChanged": "Vlasnik zadatka je promenjen u {{count}} zadataka", "changeSolverFailed": "Promena vlasnika zadatka nije uspela.", "alrTikaParsingFailed": "Došlo je do greške pri raščlanjivanju dokumenta.", "alrIndexingFailed": "Indeksiranje dokumenata nije uspelo.", "alrTikaNotRunning": "Usluga raščlanjivanja dokumenata nije dostupna.", "alrIndexingServiceNotRunning": "Usluga indeksiranja nije dostu<PERSON>.", "alrFulltextNotSet": "<PERSON>o tekst nije post<PERSON>.", "asc": "Uzlazni", "desc": "<PERSON><PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON><PERSON>", "alrLogosLoadFailed": "Učitavanje logotipa nije uspelo.", "indexedDocsCount": "u {{count}} dokumenata", "alrIndexedCountLoadFailed": "Pretraživanje celog teksta trenutno nije dostupno.", "searchAll": "Pretraži sve", "searchActual": "Samo aktuelni", "runIndexing": "Pokrenite indeksiranje", "alrDocumentIndexing": "Indeksiranje dokumenta ...", "alrDocumentIndexed": "Dokument je indeksiran i može se pronaći pretraživanjem.", "alrDocumentIndexedWithMinMetadata": "Do<PERSON><PERSON> je indeksiran.", "alrDocumentIndexingFailed": "Indeksiranje dokumenta nije uspelo.", "changingUserProfileForbidden": "Promena k<PERSON>č<PERSON>g profila je zabranjena.", "uploadingPhotoForbidden": "Zabranjeno je postavljanje fotografija.", "alrValidationCalcError": "Greška pri potvrđivanju proračuna", "maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maintenanceActivate": "Aktivirajte održavanje", "maintenanceInfoText": "očetak i kraj će biti prikazani korisnicima nakon aktiviranja održavanja.", "maintenanceMode": "<PERSON><PERSON><PERSON>", "alrAvailableCalcFailed": "Učitavanje dostupnih proračuna nije uspelo.", "alrFillDataForSearch": "Molimo unesite parametre za pretragu.", "youAreHere": "Vi ste ovde", "invalidDate": "Nevažeći format datuma", "alrInvalidFileFormat": "Nevažeći format datoteke.", "alrEnter3characters": "Unesite najmanje tri znaka.", "changeCaseOwner": "Promenite vlasnika slučaja", "actualCaseOwner": "Aktuelni vlasnik slučaja", "newCaseOwner": "Vlasnik novog slučaja", "alrCaseOwnerChanged": "Vlasnik slučaja je promenjen.", "alrChangeCaseOwnerFailed": "Promena vlasnika slučaja nije uspela.", "alrCsvSaving": "Čuvanje CSV datoteke…", "alrCsvSaveFailed": "Otpremanje CSV datoteke nije uspelo.", "alrCsvSaved": "CSV datoteka je otpremljena.", "allTemplates": "<PERSON><PERSON>", "specifyCaseIds": "Navedite ID-ove <PERSON>", "caseIds": "ID-ovi <PERSON>", "caseId": "ID slučaja", "separBySemicolon": "odvojene tačkom i zarezom", "alrAddCaseIds": "Navedite ID slučaja", "headers": "Zaglavlja", "header": "Zaglavlje", "defaultHeaderName": "Podrazumevano ime zaglavlja", "headerName": "Naziv zaglavlja", "addHeader": "Dodajte zaglavlje", "editHeader": "Izmenite zaglavlje", "templateName": "<PERSON><PERSON>", "rolesExecRightsText": "Dodelite role koje c<PERSON>e moći da pokreću slu<PERSON>eve", "orgUnitsExecRightsText": "Odredite organizacione jedinice koje će moći da pokreću slučajeve", "selectedHeader": "odabrano zaglavlje", "alrHeaderDeleted": "Zaglavlje je izbrisano!", "alrHeaderDeleteFailed": "Brisanje zaglavlja nije uspelo.", "alrHeaderSaveFailed": "Čuvanje zaglavlja nije uspelo.", "alrHeaderSaved": "Zaglavlje je sa<PERSON>o.", "alrHeadersLoadFailed": "Učitavanje podataka zaglavlja nije uspelo.", "identificator": "Kod zaglavlja", "includeDataSimilarProcesses": "Uključite podatke o svim sličnim procesima", "confirmCopyCv": "Da li zaista želite da kopirate izabrani pregled?", "alrCreatingCopyCv": "Pravljenje kopije pregleda ...", "alrCvCopied": "Pregled je kopiran.", "alrCopyCvFailed": "Kreiranje kopije pregleda nije uspelo.", "copyingTemplate": "Ko<PERSON><PERSON><PERSON>", "alrCheckTempImportFailed": "Provera uvoza šablona nije uspela.", "warnings": "Upozorenja", "missingEventsFiles": "Datoteke nedostajućih događaja", "missingEventsFilesText": "Datoteka {{- file}} nije pronađena u događaju {{- event}}", "printsOfTemplates": "<PERSON><PERSON><PERSON>", "printsOfTemplatesText": "Obratite pažnju na štampanje {{- print}} iz šab<PERSON>a {{- template}}. Vrednost: {{-  value}}", "dupliciteTaskNames": "<PERSON><PERSON><PERSON><PERSON> nazivi z<PERSON>", "dupliciteTaskNamesText": "Šablon {{- template}} sad<PERSON><PERSON>i više zadataka sa istim imenom {{- task}} {{- taskId}}, izazvaće prekidanje veza između zadataka!", "dynTableUsed": "Dinamička tabela u upotrebi", "suspiciousCalc": "<PERSON><PERSON><PERSON><PERSON>", "suspiciousCalcText": "Moguća nedostajuća rola/organizacija/korisnik u proračunu {{- calc}}", "missingEvents": "<PERSON><PERSON><PERSON><PERSON>", "missingEvent": "<PERSON><PERSON><PERSON><PERSON>", "wrongMappingDomains": "Pogrešno mapiranje domena", "wrongMappingDomainsText": "Opis zadatka {{- task}} iz <PERSON><PERSON> {{- template} sad<PERSON><PERSON>i pogrešan naziv domena, trenutni domen je {{- actDom}}", "taskDescription": "Opis zadatka", "eventsUrl": "URL događaja", "eventsUrlText": "<PERSON><PERSON><PERSON>́a greška u URL-u događaja {{- event}}, trenutni domen je {{- actDom}}", "param": "Parametar", "alrServiceNotForTable": "Podaci iz ove usluge nisu prikladni za pregled u tabeli.", "alrServiceDataFailedLoad": "Učitavanje podataka usluge nije uspelo.", "alrServiceNoData": "Usluga ne sadrži nikakve podatke.", "tableColumns": "<PERSON><PERSON> tabele", "datetime": "Datum i vreme", "exactDatetime": "Tačan datum i vreme", "dashRestNoColumns": "Nisu postavljene kolone - birate ih u postavkama kontejnera", "loadService": "Usluga učitavanja", "useCompatibleRole": "<PERSON><PERSON><PERSON> kompati<PERSON> rolu", "overwriteExistRole": "Zamenite postojeću rolu", "addNewRole": "Dodajte novu rolu", "templateImportFailed": "Uvoz šablona nije uspeo.", "templateImport": "Uvoz šablona", "templateImportNoData": "Nisu pronađeni podaci za uvoz šablona.", "variableImportNoData": "Nisu pronađeni podaci za uvoz varijabli.", "ttTemplateImport": {"heading": "Uvoz šablona", "body": "Direktorijum sa definicijama jednog ili više šablona je izabran i zatim učitan."}, "showUnfinishedProcesses": "Pokažite nedovršene s<PERSON>", "expMaintenanceEnd": "Očekivani završetak održavanja", "alrScriptSaveFailed": "Čuvanje skripte nije uspelo.", "editScript": "Izmenite skriptu", "addScript": "Dodaj<PERSON> s<PERSON>", "alrRunScript": "Pokretanje skripte ...", "alrScriptCompleted": "Skripta <PERSON>avrš<PERSON>.", "alrFailedScriptStart": "Pokretanje skripte nije uspelo.", "alrScriptDocsLoadFailed": "Učitavanje dokumentacije skripti nije uspelo.", "alrScriptLoadFailed": "Učitavanje skripti nije uspelo.", "switchAdminUser": "Zamena administrator/korisnik", "ttSwitchAdminUser": {"heading": "Zamena administrator/korisnik", "body": ""}, "ttSwitchViewport": {"heading": "Prebacivanje prikaza sa mobilnog uređaja/računara", "body": ""}, "alrEventDataLoadFailed": "Učitavanje podataka o događaju nije uspelo.", "alrEventRuleDataLoadFailed": "Učitavanje podataka pravila događaja nije uspelo.", "cancellation": "Otkazivanje", "tTaskAutoCancellCaption": "Zadatak će se automatski otkazati ako", "codeMirrorHelp": "Kliknite bilo gde u editoru i pritisnite Ctrl + Space da biste videli pomoć.", "codeMirrorHelpJs": "Za listu svih funkcija kliknite na <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "Do<PERSON>j<PERSON>", "editEvent": "Izmenite događaj", "term": "<PERSON><PERSON><PERSON>", "columnOrder": "Redosled kolona", "alrLoadEventsButtonsFailed": "Učitavanje dugmadi u tabeli nije uspelo.", "showButtonsCol": "Prikažite kolonu radnje", "button": "<PERSON><PERSON><PERSON>", "enableButtonInTasks": "Prikaži kao dugme na listi zadataka", "alrEventDoesntExist": "Izabrani događaj ne postoji.", "alrEventRuleSaveFailed": "Čuvanje pravila događaja nije uspelo.", "variableNames": "<PERSON><PERSON>a pro<PERSON><PERSON>", "fsEvent": "<PERSON><PERSON><PERSON><PERSON>", "alrEventDeleteFailed": "Brisanje događaja nije uspelo.", "fsRule": "pravilo", "alrRuleDeleteFailed": "Brisanje pravila nije uspelo.", "alrRuleStatusChangeFailed": "Promena statusa pravila nije uspela.", "ruleActivateDeactivateQ": "Da li zaista želite da promenite status pravila?", "docUploadedPrivate": "Dokument će biti postavljen kao privatan", "fileOwner": "Vlasnik datoteke", "planOk": "U redu", "userNotAuthToStartTempl": "Korisnik nije ovlašćen da pokreće slučaj u ovom šablonu", "planStartDate": "<PERSON>tum <PERSON>", "useOnlyFutureDates": "<PERSON><PERSON> bud<PERSON><PERSON>i datumi", "alrGenerateHtmlFailed": "Generisanje HTML -a nije uspelo.", "alrNoPermsToAddNoteInVice": "emate <PERSON><PERSON><PERSON><PERSON> da dodate belešku kao zamenik.", "alrNoPermsToAddDocInVice": "Nemate dozvolu da dodate dokument kao zamenik.", "current": "Aktuelan", "indexation": "Indeksacija", "attemptToRestoreConnection": "Po<PERSON><PERSON><PERSON> da se povrati veza u", "loginWillExpire": "<PERSON><PERSON>ja<PERSON> će isteći za", "unsavedDataWillBeLost": "Nesačuvani podaci će biti izgubljeni.", "alrFileSaveLikeAttachViceError": "<PERSON><PERSON><PERSON> zamena ima dozvolu samo za pregled!", "alrFileSaveLikeAttachStoreError": "Čuvanje ispisa kao dokumenta u slučaju nije uspelo.", "useCompatibleEvent": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON> dog<PERSON>", "overwriteExistEvent": "Zamenite postojeći događaj", "addNewEvent": "Dodajte novi događaj", "useCompatibleUser": "Koristite kompatibilnog korisnika", "overwriteExistUser": "Zamenite postojećeg korisnika", "addNewUser": "Dodajte novog korisnika", "useCompatibleUnit": "Koristite kompatibilnu org. jedinicu", "overwriteExistUnit": "Zamenite postojeću org. jedinicu", "addNewUnit": "Dodajte novu org. jedinicu", "addNewDynTable": "Dodajte novu din. tablicu", "useCompatibleDynTable": "Koristite kompatibilnu din. tablicu", "enterDiffNameRoot": "Unesite drugačije ime od Root.", "ttTemplatesExport": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Izvezi izabrane šablone u direktorijum. Moguće je izabrati ime i lokaciju izvezene datoteke. Radnja se primenjuje na izabrani šablon. Izaberite šablon klikom na njega u tabeli šablona."}, "ttTemplatesExportAll": {"heading": "Izvezi sve šablone", "body": "Izvezi sve trenutno prikazane šablone u datoteku. Moguće je izabrati ime i lokaciju izvezene datoteke. Izbor <PERSON> može biti ograničen postavljanjem odgovarajućeg filtera."}, "exportAll": "Izvezi sve", "noTemplatesToExport": "Nema šablona za izvoz.", "skip": "Preskočiti", "ttSkipTemplate": {"heading": "Preskoči šablon", "body": "Preskače uvoz trenutnog šablona i prikazuje sledeći."}, "alrInvalidImportData": "Nevažeći podaci o uvozu", "alrUsersNotLoaded": "Učitavanje korisnika nije uspelo", "caseOverview": "<PERSON><PERSON> s<PERSON>", "alrRolesNotLoaded": "Učitavanje rola nije uspelo.", "changeLang": "Promenite jezik", "reactivatesPlan": "ponovo aktivira plan", "alrOrgUnitsNotLoaded": "Org. jedinice se nisu učitale.", "refreshPage": "Osveži stranicu", "stayLogged": "Ostan<PERSON>", "showTime": "Prikaz vremenske oznake u pregledima", "managerIn": "<PERSON><PERSON><PERSON><PERSON> u {{orgUnit}}", "usageStats": "Statistika korišćenja", "month": "Mesec", "alrUsageStatsLoadFailed": "<PERSON><PERSON> mog<PERSON>e učitati statistiku korišćenja.", "accessLog": "Pristupni log podaci", "durationInMs": "<PERSON><PERSON><PERSON><PERSON> (ms)", "task": "Zadatak", "operation": "Operacija", "active_users": "B<PERSON>j aktivnih korisnika", "active_template_processes": "B<PERSON>j aktivnih procesa", "active_headers": "Broj aktivnih zaglavlja", "active_users_able_to_create_a_process": "<PERSON><PERSON>j aktivnih korisnika koji mogu pokrenuti proces", "users_that_solved_a_task": "B<PERSON>j korisnika koji su rešili bar jedan zadatak", "solvers_or_can_create_a_process": "<PERSON><PERSON>j rešavača koji su rešili zadatak ili mogu pokrenuti proces", "mobile_app_paired_users": "B<PERSON>j uparenih korisnika mobilne aplikacije", "calculationsLogs": "Log podaci proračuna", "translatedScript": "<PERSON><PERSON><PERSON> sk<PERSON>ta", "originalScript": "Original skripta", "tskId": "ID zadatka", "alrCalculationsDocsLoadFailed": "Nije moguće učitati dokumente proračuna.", "alrCalculationsValidationFailed": "Provera proračuna nije uspela.", "linkPriority": "Prioritet veze", "dateFormat": "DD.MM.GGGG", "alrConvertErrorJsonNeon": "Greška pri pretvaranju JSON -> neon.", "alrInvalidData": "Nevažeći podaci.", "sharedVar": "Zajednička promenljiva", "guide": "<PERSON><PERSON><PERSON><PERSON>", "guideFs": "v<PERSON><PERSON><PERSON>", "guides": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrGuidesLoadFailed": "Učitavanje vodiča nije uspelo.", "language": "<PERSON><PERSON><PERSON>", "default": "Podra<PERSON>mevan<PERSON>", "next": "Sledeći", "previous": "<PERSON><PERSON><PERSON><PERSON>", "targetElementNotFound": "Ciljni element nije pronađen", "documentation": "Dokumentacija", "matchesRegular": "Ne podudara se sa pravilom", "secondAllowedValues": "dozvoljene vrednosti - 0 je vrh minuta", "everySec": "svake sekunde", "listOfSec": "lista sekundi; tj. 0,30 bi bilo 0 i 30. sekundi", "rangeOfSec": "opseg sekundi; tj. 0–5 bi bile sekunde 0, 1, 2, 3, 4 i 5 (možete odrediti i listu opsega 0–5,30–35)", "slashSec": "vrednosti koraka će preskočiti navedeni broj unutar opsega; tj. */5 je svakih 5 sekundi, a 0–30/2 je svaka 2 sekunde između 0 i 30 sekundi", "minuteAllowedValues": "dozvoljene vrednosti - 0 je vrh sata", "everyMin": "svake minute", "listOfMin": "spisak minuta; tj. 0,30 bi bilo 0 i 30. minuta", "rangeOfMin": "raspon minuta; tj. 0–5 bilo bi minuta 0, 1, 2, 3, 4 i 5 (možete odrediti i listu opsega 0–5,30–35)", "slashMin": "vrednosti koraka će preskočiti navedeni broj unutar opsega; tj. */5 je svakih 5 minuta, a 0–30/2 je svaka 2 minuta između 0 i 30 minuta", "hourAllowedValues": "dozvoljene vrednosti - 0 je ponoć", "everyHour": "svaki sat", "listOfHour": "spisak sati; tj. 0,12 bi bilo 0 i 12 sati", "rangeOfHour": "raspon sati; tj. 19–23 bi bili sati 19, 20, 21, 22 i 23 (mož<PERSON> navesti i listu opsega 0–5,12–16)", "slashHour": "vrednosti koraka će preskočiti navedeni broj unutar opsega; tj. */4 je svaka 4 sata, a 0–20/2 je svaka 2 sata između 0 i 20. sata", "dayAllowedValues": "dozvoljene vrednosti", "everyMonthDay": "svakog dana u mesecu", "listOfDay": "spisak dana; tj. 1,15 bi bio 1. i 15. dan u mesecu", "rangeOfDay": "niz dana; tj. 1–5 bi bili dani 1, 2, 3, 4 i 5 (mož<PERSON> navesti i listu opsega 1–5,14–30)", "slashDay": "vrednosti koraka će preskočiti navedeni broj unutar opsega; tj. */4 je svaka 4 dana, a 1–20/2 je svaka 2 dana između 1. i 20. dana u mesecu", "allowedValues": "dozvoljene vrednosti", "everyMonth": "svakog meseca", "listOfMonth": "spisak meseci; tj. 1,6 bi bio januar i jun", "rangeOfMonth": "niz mese<PERSON>; tj. 1–3 c<PERSON>e biti januar, februar i mart (takođe možete navesti listu opsega 1–4,8–12)", "slashMonth": "vrednosti koraka će preskočiti navedeni broj unutar opsega; tj. */4 je svaka 4 meseca, a 1–8/2 je svaka 2 meseca između januara i avgusta", "weekAllowedValues": "dozvoljene vrednosti; 0 = nedelja, 1 = ponedeljak, 2 = utorak, 3 = sreda, 4 = četvrtak, 5 = petak, 6 = subota", "everyWeekDay": "svaki dan u nedelji", "listOfWeekDay": "spisak dana; tj. 1,5 bi bio ponedeljak i petak", "rangeOfWeekDay": "niz dana; tj. 1–5 bi bilo pon, u<PERSON>, s<PERSON><PERSON>, <PERSON>et i pet (takođe možete navesti listu opsega 0–2,4–6)", "slashWeek": "vrednosti koraka će preskočiti navedeni broj unutar opsega; tj. */4 je svaka 4 dana, a 1–5/2 je svaka 2 dana između ponedeljka i petka", "contrab": "Cron {{variable}} polje", "cSecond": "sekunda", "cMinute": "minuta", "cHour": "sat", "cDay": "dan", "cMonth": "mesec", "cWeekDay": "dan u nedelji", "seconds": "sekunde", "minutes": "minute", "hours": "sati", "days": "dani", "weeks": "<PERSON><PERSON><PERSON>", "socketOk": "Tabela sadrži najnovije podatke", "socketBroken": "Vratite vezu za stalno ažuriranje podataka", "newTask": "Novi zadatak", "report": "Izveštaj", "ttCaseReport": {"heading": "Izveštaj", "body": ""}, "usersRights": "<PERSON><PERSON><PERSON> koris<PERSON>a", "visPerRole": "Vidljivost po roli", "manualEvents": "Ručni događaji", "noTasks": "<PERSON>ema novih zadataka", "emptyFavs": "Lista favorita je prazna", "crons": "Crons", "cronsHistory": "Istorija crons", "redirBefStart": "Pre početka, preusmerite na", "lastRun": "Prethodno pokretanje", "nextRun": "Sledeće pokretanje", "syntax": "Sin<PERSON>ks<PERSON>", "alias": "<PERSON><PERSON>", "stop": "Stop", "restart": "<PERSON><PERSON>", "restartCronProcess": "Ponovo pokrenite kontekst procesa", "ttRestartCron": {"heading": "Ponovo pokreni Cron", "body": ""}, "ttRestartCronProcess": {"heading": "Ponovo pokrenite proces", "body": ""}, "ttResetCron": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttRunCron": {"heading": "Pokrenite Cron", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "<PERSON><PERSON> je zaustavljen.", "alrCronStopFailed": "Zahtev za zaustavljanje Cron-a nije uspeo.", "alrCronRunning": "<PERSON><PERSON> je pokrenut.", "alrCronRunFailed": "Pokretanje Crona nije uspelo.", "alrCronReset": "<PERSON>ron je vraćen na podrazumevana podešavanja.", "alrCronResetFailed": "<PERSON><PERSON> reset nije uspeo.", "alrCronRestart": "<PERSON><PERSON> je ponovo pokrenut.", "alrCronRestartFailed": "Zahtev za ponovno pokretanje Cron-a nije uspeo.", "alrCronUpdated": "<PERSON><PERSON> je us<PERSON>.", "alrCronUpdateFailed": "Zahtev za ažuriranje Cron-a nije uspeo.", "confirmRunCronDialog": "Jeste li sigurni da želite da pokrenete izabrani Cron?", "confirmStopCronDialog": "Jeste li sigurni da želite da zaustavite izabrani Cron?", "confirmResetCronDialog": "Da li ste sigurni da želite da resetujete cron na fabrička podešavanja?", "confirmRestartCronDialog": "Jeste li sigurni da želite ponovo pokrenuti izabrani Cron?", "confirmUpdateCronDialog": "Jeste li sigurni da želite da promenite status Cron-a?", "alrProcessRestart": "<PERSON><PERSON> proces je ponovo pokrenut!", "alrProcessRestartFailed": "Zahtev za ponovno pokretanje procesa nije uspeo.", "confirmRestartProcessDialog": "Jeste li sigurni da želite ponovo pokrenuti ceo <PERSON><PERSON> proces? Budite oprezni, doći će do potpunog ponovnog pokretanja svih Cron-ova i celog konteksta.", "cronParams": "Parametri", "alrPresetLogFiltersLoadFailed": "Učitavanje unapred podešenih filtera log podataka nije uspelo.", "timeRange": "Vremenski period", "presetFilters": "<PERSON><PERSON><PERSON>", "params": "Parametri", "authentication": "Autentikacija", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Naziv parametra", "authMethod": "na<PERSON><PERSON>", "taskAlreadyEdited": "Zadatak već uređuje drugi korisnik.", "taskEditedByAnotherUser": "Drugi korisnik je počeo da uređuje zadatak.", "tempAlreadyEdited": "Šablon već uređuje drugi korisnik.", "tempEditedByAnotherUser": "Drugi korisnik je počeo da uređuje šablon.", "test": "Test", "notInRightormat": "Nevažeći format", "ttTableExportExcel": {"heading": "<PERSON><PERSON><PERSON><PERSON> tabelu", "body": "Izvozi tabelu u .xlsx datoteku"}, "ttTableExportCsv": {"heading": "<PERSON><PERSON><PERSON><PERSON> tabelu", "body": "Izvozi tabelu u CSV datoteku"}, "searchInSuspended": "Pretražujte i suspendovane slučajeve", "alrScriptDocsFailed": "<PERSON><PERSON> mog<PERSON>e sačuvati dokumentaciju skripte.", "currentlyRunning": "Trenutno u toku", "onStart": "Na startu", "onEnd": "Na kraju", "onHand": "R<PERSON><PERSON><PERSON>", "onRecalc": "Na preračun", "onPull": "Pre preuzimanja", "yesterday": "<PERSON><PERSON><PERSON>", "tomorrow": "Sutra", "replyRecipient": "Primalac odgovora", "bcRecipient": "Primalac slepe kopije", "copyRecipient": "Primalac kopije", "emptyHe": "Prazno", "archivedLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basicMode": "Osnovni način rada", "expertMode": "<PERSON><PERSON><PERSON>", "ttBasicMode": {"heading": "Osnovni <PERSON>", "body": "Sakriva neke stavke ili opcije u obrascu."}, "ttExpertMode": {"heading": "<PERSON><PERSON><PERSON>", "body": "Prikazuje skrivene stavke ili opcije u obrascu."}, "helpOverviewFolder": "Pregled možete uvrstiti u strukturu direktorijuma pomoću kosih crta. <br/> <i> (npr. Fakture/Sve primljene fakture) </i>", "helpOverviewIncludeSimilar": "<PERSON><PERSON>, prikazaće se i slučajevi iz drugih zaglavlja šablone.", "helpOverviewSysVars": "Polja označena sa (sys) su sistemska polja koja su deo svakog procesa.", "customization": "Prilagođavanje", "elementColor": "<PERSON><PERSON>", "fontColor": "<PERSON><PERSON>a", "fontSize": "<PERSON><PERSON><PERSON><PERSON>", "bold": "Podebljano", "cursive": "Kurziv", "off": "Isključeno", "toPlan": "Plan", "alrMaintenanceComing": "U {{time}} planirano održavanje sistema će početi. Sačuvajte svoj rad.", "timeoutHMS": "Vremensko ograničenje: (hh:mm:ss)", "eventw": "Zadatak \"{{task}}\" iz š<PERSON>lona \"{{template}}\" čeka ovaj događaj", "waitsForEventTip": "Slučaj čeka događaj: \"{{event}}\"", "copyToMultiinstances": "Kopirajte u više instanci", "showAsPreview": "<PERSON><PERSON><PERSON><PERSON> pregled", "alrPreviewAttachmentsFailed": "Prikaz pregleda nije uspeo", "alrPreviewAttachmentsWrongFormat": "Prikaz pregleda nije uspeo - format datoteke nije podržan", "previewNotAvailable": "Pregled dokumenta nije moguć zbog vrste dokumenta.", "configuration": "Konfiguracija", "values": "<PERSON><PERSON><PERSON><PERSON>", "defaultValues": "Podrazumevane v<PERSON>", "ttSubscribeCv": {"heading": "Pretplatiti se na pregled", "body": "<PERSON><PERSON><PERSON><PERSON> pregled će vam biti poslan e -poštom svakog radnog dana u postavljeno vreme."}, "subscribe": "Pretplatiti se", "time": "Vreme", "externalLang": "<PERSON><PERSON><PERSON><PERSON> jezik", "hdrStatusQ": "Da li zaista želite da promenite status zaglavlja?", "small": "Mali", "medium": "Srednji", "large": "<PERSON><PERSON><PERSON>", "alrTemplTsksLoadFailed": "Učitavanje zadataka šablona nije uspelo.", "applyInTasks": "Primenite u zadacima", "caseStatuses": "<PERSON>i s<PERSON>", "statuses": "Statusi", "Manuals": "Priručnici", "forReading": "Za čitanje", "forReadWrite": "Za čitanje i pisanje", "addVersion": "Nova verzija", "size": "Veličina", "prevWorkDay": "<PERSON><PERSON><PERSON><PERSON> radni dan", "mandatoryVar": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON>", "emptyRequiredVarMessage": "<PERSON>s, neophodna promenljiva je prazna", "ttCreateTempVersion": {"heading": "Napravite novu verziju <PERSON>", "body": ""}, "version": "Verzija", "alrTempVersionsLoadFailed": "Učitavanje verzija šablona nije uspelo.", "alrChangeTempVersionFailed": "Promena verzije <PERSON>a nije uspela.", "alrCreateTempVersionFailed": "Kreiranje nove verzije šablona nije uspelo.", "confirmCreateTempVersion": "Jeste li sigurni da želite da kreirate novu verziju <PERSON>?", "applyInAllTasks": "Primenite u svim zadacima", "duration": "<PERSON><PERSON><PERSON><PERSON>", "alrDynConditionsFailed": "<PERSON>je moguće dovršiti zadatak. Pokušajte da osvežite stranicu ili se obratite administratoru ili službi za pomoć.", "caseActivation": "Aktivacija slučaja", "average": "Prosek", "performanceLogs": "Evidenc<PERSON>", "displayingOverview": "P<PERSON><PERSON> pregleda", "taskSolve": "Dovršavanje zadat<PERSON>", "displayingCO": "Prikaz PREGLEDA SLUČAJA", "printCreation": "Kreiranje <PERSON>", "entityId": "ID entiteta", "copyTask": "Kopirajte zadatak", "checkProcessCompletion": "Provera završetka procesa", "findingSolver": "Pronalaženje <PERSON>", "publicFiles": "<PERSON><PERSON><PERSON> da<PERSON>", "usage": "Upotreba", "serviceConsole": "<PERSON><PERSON><PERSON> kon<PERSON>", "selectAll": "Označi sve", "logos": "<PERSON><PERSON><PERSON><PERSON>", "overviewWithTasks": "Pregled sa zadacima", "printIsReady": "Štampanje je spremno", "alrChangelogLoadFailed": "Učitavanje loga izmena nije uspelo.", "inJs": "U skripti", "ttCopyDtDefinition": {"heading": "<PERSON><PERSON><PERSON> defini<PERSON> tabele", "body": "<PERSON><PERSON><PERSON> definiciju izabrane dinamičke tabele."}, "confirmCopyTableDefinition": "Da li zaista želite da kopirate definiciju tabele?", "alrCopying": "Kopiranje...", "alrCopyFailed": "<PERSON><PERSON><PERSON><PERSON> nije uspelo.", "fallback": "Fallback", "syncEnabled": "Sinhronizacija", "systemGuideNote": "<PERSON><PERSON><PERSON><PERSON> Sistemskog vodiča ne može se menjati. Da biste videli drugi sadržaj, učinite Sistemski vodič neaktivnim i kopirajte njegov sadržaj u novi Vodič.", "alrAnotherUserLogged": "Drugi korisnik je prijavljen u drugom prozoru!", "userLocked": "Korisnik je zaključan", "visInternalUserOnly": "Vidljivo samo internim korisnicima", "showSelectedOnly": "Prikaži samo i<PERSON>ne", "clickToSelect": "Kliknite da biste izabrali", "restrictRoleAssignment": "Ograničite dodeljivanje rola za rolu", "restrictions": "Ograničenja", "restrictTableHandling": "Ograničite rukovanje tabelama", "toRole": "<PERSON>a rolu", "inCalcToHeader": "U proračunima do zaglavlja", "loginBtnColor": "Boja dugmeta za prijavljivanje", "certificates": "Sertifikati", "certificate": "Ser<PERSON><PERSON><PERSON>", "certificateVar": "sertif<PERSON><PERSON>", "tspSources": "Vremenske oznake", "tspSource": "Vremenska oznaka", "confirmExpandDynRowsNewAssignments": "<PERSON><PERSON><PERSON>, novi zadatak! Promenljive nemaju podešene ose. Da li želite da rastegnete sve dinamičke redove?", "confirmExpandDynRows": "Jeste li sigurni da želite da rastegnete sve dinamičke redove?", "expandDynRows": "Rastegnite dinamičke redove", "visible": "Vidljiv", "cvcDbColumn": "<PERSON>z<PERSON>na kolona", "cvTableSource": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>a", "uploadedFromFile": "Otpremljeno iz datoteke", "appStatus": "Status aplikacije", "loadAll": "Učitaj sve", "ignoredUsers": "I<PERSON>ris<PERSON> k<PERSON>", "copyRolesFrom": "<PERSON><PERSON><PERSON><PERSON> role iz", "disableFrontendStyles": "Ne primenjujte automatske stilove", "activate": "Aktivirati", "confirmActivateCase": "Da li zaista želite da aktivirate slučaj?", "alrLackOfPerms": "Nedostatak dozvola.", "alrSending": "Slanje ...", "sequences": "Sekvence", "seqName": "Sekvenca", "seqId": "ID sekvence", "seqLastRead": "Poslednje čitanje", "ttCopyRole": {"heading": "<PERSON><PERSON><PERSON><PERSON> rolu", "body": "<PERSON><PERSON><PERSON> k<PERSON> role."}, "fromCase": "<PERSON><PERSON>", "includingData": "Uključujući podatke", "updateInstances": "Ažurirajte promenljive instance", "addNewCalcScript": "Dodajte novu skriptu", "useCompatibleCalcScript": "Ko<PERSON><PERSON> kompatibilnu sk<PERSON>", "choose": "<PERSON><PERSON><PERSON><PERSON>", "valueChange": "<PERSON><PERSON><PERSON>", "externalSource": "Korisnički izvor", "reports": "Izveštaji", "confirmCopyReport": "Jeste li sigurni da želite kopirati izabrani izveštaj?", "graphs": "<PERSON><PERSON><PERSON>", "aggregation": "Agregacija", "graphNew": "Grafikon - nov", "confirmCopyGraph": "Jeste li sigurni da želite kopirati izabrani grafikon?", "alrCopyGraphFailed": "Kopiranje grafikona nije uspelo.", "label": "<PERSON><PERSON>", "pie": "Tortni grafikon", "line": "Linijski grafikon", "dot": "Tačka grafikon", "bar": "Trakasti grafikon", "barGroups": "Trakasti grafikon – grupe", "alrFailedGraphData": "Učitavanje grafikona nije uspelo.", "graphSetSharing": "Podesite deljenje grafikona za svaku grupu korisnika", "alrGraphPointsLoadFailed": "Učitavanje tačaka grafikona nije uspelo.", "alrGraphNotFound": "Grafikon nije pronađen.", "graphData": "Podaci gra<PERSON>a", "pointsData": "Grafičke tač<PERSON>", "alrGraphSaveFailed": "Čuvanje grafikona nije uspelo!", "graphPoint": "Grafička tačka", "noOrder": "<PERSON><PERSON>", "refreshGraph": "Osveži grafikon", "viewSwitcher": "Globalni filter", "axisXglobalFilter": "Osa X - globalni filter", "axisXgroups": "Osa X - grupe", "axisXdata": "Osa X - podaci", "axisYvalues": "Osa <PERSON>", "axisYcolors": "<PERSON><PERSON>", "hrAgenda": "HR agenda", "userChange": "<PERSON><PERSON><PERSON>", "newUser": "Novi korisnik", "usersCount": "<PERSON><PERSON><PERSON>", "confirmChangeUser": "Jeste li sigurni da želite da promenite korisnika?", "businessVariable": "Poslovna promenljiva", "casesCount": "<PERSON><PERSON><PERSON>", "selected": "Izabran", "selectedOnly": "Samo odabrano", "addCaseRightNewUser": "Do<PERSON>j<PERSON> prist<PERSON> s<PERSON>", "visFromTaskToPull": "Vidljivost od zadatka za povlačenje", "toChangeConfigInfo": "Da biste promenili, izbrišite vrednost iz datoteke local.js", "clickToChange": "Kliknite da biste promenili", "currentValue": "Trenutna vrednost", "sign": "Znak", "validationProtocols": "Validacije", "plannedEvents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "elArchiv": "E-arhiva", "deletedDocs": "<PERSON><PERSON>brisani", "signatures": "Po<PERSON><PERSON><PERSON>", "ttArchive": {"heading": "Elektronska arhiva", "body": ""}, "ttAddToZea": {"heading": "Dodati u elektronsku arhivu", "body": ""}, "ttRemoveFromZea": {"heading": "Ukloniti iz elektronske arhive", "body": ""}, "ttZeaInfo": {"heading": "Validacija", "body": ""}, "ttSignZea": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "addToZea": "<PERSON><PERSON><PERSON> u e-a<PERSON><PERSON><PERSON>", "removeFromZea": "Ukloniti iz e-arhive", "reTimestampAfter": "Važenje generisane vremenske oznake (dana)", "alrLoadFailed": "Učitavanje nije uspelo.", "replace": "<PERSON><PERSON><PERSON><PERSON>", "expireAt": "Ističe", "result": "Rezultat validacije", "validatedAt": "Da<PERSON> validacije", "refType": "Objekt", "eventType": "Vrsta radnje", "errorMessage": "Poruka o grešci", "errorTimestamp": "V<PERSON><PERSON> g<PERSON>", "errorCount": "<PERSON><PERSON><PERSON>", "inFuture": "Planirano", "path": "Put do datoteke sa potpisom", "signedAt": "<PERSON><PERSON><PERSON><PERSON>", "dropZoneZeaCertificate": "Ovde prevucite sertifikat ili kliknite da biste izabrali datoteku za otpremanje.", "authType": "<PERSON><PERSON> auten<PERSON>", "basic": "Sa imenom i lozinkom", "byCert": "Po sertifikatu", "alrMissingCertFile": "Otpremite sertifikat, molim.", "replaceTo": "Zameniti na", "autoReTimestamp": "Automatska vremenska oznaka", "validate": "Potvrditi", "lastUse": "Poslednje generisano", "createdAt": "Kreirano u", "updatedAt": "Ažurirano u", "certificateId": "ID sertifikata", "expectedCreationTime": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "nextTSPSourceId": "ID sledeće vremenske oznake", "reTimestampAt": "Sledeća vremenska oznaka", "timestampedAt": "Poslednja vremenska oznaka", "level": "<PERSON><PERSON>", "signatureB": "<PERSON><PERSON><PERSON><PERSON>", "signatureT": "Potpis sa vremenskom oznakom", "signatureLt": "Potpis sa dugoročnim sertifikatima podataka", "signatureLta": "Potpis sa dugoročnim podacima i vremenskom oznakom arhive", "packaging": "Pakovanje", "enveloped": "Umotan", "enveloping": "<PERSON><PERSON><PERSON>č", "detached": "Odvojen", "algorithm": "Algoritam", "uploadAsRevision": "Otpremite kao reviziju", "externalDisable": "Aktivan samo za grupne potpise", "addToDms": "Dodaj u DMS", "autoConvert": "Automatski konvertovati", "format": "Format", "signatureType": "<PERSON><PERSON><PERSON> pot<PERSON>a", "signature": "Potpis", "custom": "Vlastiti", "batchSignDisabled": "<PERSON>z potpisa", "tasId": "ID dokumenta", "hashValue": "Vrednost heša", "hashType": "Tip funkcije <PERSON>", "confirmAddToArchive": "Da li zaista želite da dodate u arhivu?", "independentSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "independentValidation": "Nezavisna validacija", "failureTrue": "Sa greškom", "failureFalse": "<PERSON>z gre<PERSON>ka", "confirmValidateDialog": "Da li zaista želite da potvrdite potpis?", "confirmRestartDialog": "Da li zaista želite da resetujete greške događaja?", "verificationResult": "Rezultat verifikacije", "integrityMaintained": "<PERSON><PERSON><PERSON><PERSON> integrite<PERSON>", "signatureFormat": "Format potpisa", "internalTimestampsList": "Spisak internih vremenskih oznaka", "signers": "Potpisnici", "exhibitedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signedBy": "Potpisao", "validFrom": "Važi od", "validUntil": "Važi do", "signitureType": "<PERSON><PERSON><PERSON> pot<PERSON>a", "signatureQualification": "Kvalifikacija potpisa", "signatureNoTimestamps": "Potpis ne sadrži vremenske oznake", "electronicSignature": "Elektronski potpis", "electronicSeal": "Elektronski pečat", "webSiteAuthentication": "Autentifikacija web stranice", "QCP-n": "QCP-n: politika sertifikata za kvalifikovane sertifikate EU izdate fizičkim licima", "QCP-l": "QCP-l: politika sertifikata za kvalifikovane sertifikate EU izdate pravnim licima", "QCP-n-qscd": "QCP-n-qscd: smernica sertifikata za kvalifikovane sertifikate EU izdate fizičkim licima sa privatnim ključem u vezi sa sertifikovanim javnim ključem u QSCD-u", "QCP-l-qscd": "QCP-l-qscd: politika sertifikata za kvalifikovane sertifikate EU izdate pravnim licima sa privatnim ključem u vezi sa sertifikovanim javnim ključem u QSCD-u", "QCP-w": "QCP-w: smernice za sertifikate za autentifikaciju web stranica kvalifikovanih za EU", "formOfReStamping": "Oblik ponovnog žigosanja", "individually": "Pojedinačno", "archiveAsPdf": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kao <PERSON>", "couldNotBeVerified": "<PERSON><PERSON> m<PERSON> veri<PERSON>", "uses": "<PERSON><PERSON><PERSON>", "countOfSignedDocuments": "<PERSON><PERSON><PERSON> do<PERSON>", "batchSignature": "<PERSON><PERSON><PERSON><PERSON> potpis", "standaloneSign": "<PERSON><PERSON>", "validateSignature": "Potvrda potpisa", "validateDoc": "Validacija dokumenta", "containsSignature": "<PERSON><PERSON><PERSON><PERSON>", "reStamping": "Ponovno žigosanje", "individualSignatures": "Pojedinačni potpisi", "signatureLevel": "<PERSON><PERSON>", "simpleReport": "Jednostavan izveštaj", "detailedReport": "Detaljan izveštaj", "diagnosticReport": "Dijagnostički izveštaj", "etsiReport": "ETSI izveštaj", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "Failed", "INDETERMINATE": "Indeterminate", "FORMAT_FAILURE": "Signature does not comply with one of the basic standards", "HASH_FAILURE": "The signed data object's hash does not match the hash in the signature", "SIG_CRYPTO_FAILURE": "The signature could not be verified with the signer's public key", "REVOKED": "The signature certificate has been revoked and there is evidence that the signature has been created after the revocation", "SIG_CONSTRAINTS_FAILURE": "One or more signature attributes do not match the validation rules", "CHAIN_CONSTRAINTS_FAILURE": "The certificate chain used in the validation process does not comply with the certificate validation rules", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "The set of certificates available for string verification caused an error for an unspecified reason", "CRYPTO_CONSTRAINTS_FAILURE": "One of the signature verification algorithms is below the required cryptographic security level and the signature was acquired after the algorithm lifetime", "EXPIRED": "The signature was created after the signature certificate expired", "NOT_YET_VALID": "The signing time lies before the issuance date of the signing certificate", "POLICY_PROCESSING_ERROR": "The validation policy file could not be processed", "SIGNATURE_POLICY_NOT_AVAILABLE": "The electronic document containing details about the validation policy is not available", "TIMESTAMP_ORDER_FAILURE": "Restrictions in signature timestamp order are not respected", "NO_SIGNING_CERTIFICATE_FOUND": "The signing certificate cannot be identifie", "NO_CERTIFICATE_CHAIN_FOUND": "No certificate chain was found for the identified signature certificate", "REVOKED_NO_POE": "The signing certificate was revoked at the validation date/time. However, the signature verification algorithm cannot detect that the signature time is before or after the revocation period", "REVOKED_CA_NO_POE": "At least one certificate chain was found, but a temporary CA certificate was revoked", "OUT_OF_BOUNDS_NOT_REVOKED": "The signing certificate is expired or not yet valid at the validation date/time and the Signature Validation Algorithm cannot ascertain that the signing time lies within the validity interval of the signing certificate. The certificate is known not to be revoked.", "OUT_OF_BOUNDS_NO_POE": "The signing certificate has expired or is not yet valid at the verification date/time", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "One of the signature verification algorithms is below the required cryptographic security level and there is no proof that it was produced before the algorithm/key was considered secure", "NO_POE": "There is no proof that the signed object was created before a compromising event", "TRY_LATER": "Not all validation rules can be met with the available information, but it may be possible to do so with additional revocation information that will be available later", "SIGNED_DATA_NOT_FOUND": "Signed data cannot be obtained", "GENERIC": "Other Reason", "signatureFile": "Datoteka sa potpisom", "validityDays": "Dani važenja", "qualifiedHe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qualifiedIt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unqualifiedHe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unqualifiedIt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeValid": "Period važenja", "reason": "Razlog", "inTime": "na vreme", "certificateQualification": "Kvalifikacija sertifikata", "guaranteedHe": "<PERSON><PERSON><PERSON><PERSON>", "guaranteedIt": "<PERSON><PERSON><PERSON><PERSON>", "fromQualifiedCert": "<PERSON><PERSON> sertifika<PERSON>.", "basedOnQualifiedCertHe": "Na osnovu kvalifikovanog sertifikata", "createdByQualifiedResHe": "<PERSON><PERSON><PERSON> resurs", "basedOnQualifiedCertIt": "Na osnovu kvalifikovanog sertifikata", "createdByQualifiedResIt": "<PERSON><PERSON><PERSON> resurs", "qualification": "Kvalifikacija", "confirmRemoveFromZeaDialog": "Da li zaista želite da uklonite {{variable}} iz elektronske arhive?", "noValidationReports": "Nema izveštaja o validaciji", "noSignatures": "Bez pojedinač<PERSON>h potpisa", "isHigherOrEqualThan": "<PERSON>ra biti veće ili jednako", "isInZea": "U e-arhivi", "startStamping": "Počnite sa pečaćenjem", "reTimestampAfterMinutes": "minuta", "reTimestampAfterDays": "dana", "reTimestampAfterAll": "Važenje generisane vremenske oznake", "refId": "ID objekta", "docWithoutAutoTimestampInfo": "Dokument će biti pot<PERSON>an jednom, bez automatskog umetanja vremenske oznake.", "validationReports": "Istorija validacije", "docPath": "Put do dokumenta", "addToArchiveInvalidSignatureError": "Datoteka se ne može arhivirati jer sadrži potpis koji se ne može proveriti.", "signImmediately": "Potpisati odmah", "replaceInConfiguration": "Zamenite u konfiguraciji", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bulk": "<PERSON><PERSON><PERSON>", "bulkCompletion": "Grupno završavanje", "enableBulkCompletion": "Omogući grupno dovršavanje", "confirmCompleteTasks": "Da li zaista želite da završite zadatke?", "plannedMaintenance": "Planirano održavanje", "notSent": "<PERSON><PERSON> p<PERSON>", "notSpecified": "<PERSON><PERSON>", "bulkCompletionVars": "Promenljive grupnog dovršavanja", "alrBulkCompletionMultiTypeErr": "Samo zadaci iste vrste mogu se obaviti grupno, možete koristiti filter.", "notifications": "Obaveštenja", "alrTskAlreadyTakenSomeone": "Neko drugi je već preuzeo zadatak.", "alrTskAlreadyTaken": "Zadatak je već preuzet.", "downloadBpmn": "preuzmite BPMN dijagram", "downloadSvg": "preuzmite kao SVG sliku", "displayForm": "<PERSON>rsta p<PERSON>", "selectedPreview": "prika<PERSON><PERSON> se pregled", "fixedHeight": "Fiksna visina (u pikselima)", "lastVersion": "Najnovija verzija", "separatedPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON> pregled", "defaultZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fixedPosition": "Fiksni <PERSON>", "percentInterval": "Molimo popunite ceo broj između 0–5", "notPositiveNumber": "Molimo vas da popunite samo pozitivne brojeve", "zoomDown": "<PERSON><PERSON><PERSON><PERSON>", "zoomUp": "Uvećajte", "rotate": "<PERSON><PERSON><PERSON><PERSON>", "logTooBig": "Log zapisi su preveliki da bi se mogli prikazati.", "downloadLog": "Log zapisi preuzimanja", "confirmCopyCron": "Da li zaista želite da kopirate izabrani Cron?", "ttCopyCron": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "onlyWorkingDays": "<PERSON>o radni dani", "datesDisabled": "Onemogući datume", "useView": "Koristite pregled", "dateWithoutTime": "Datum bez vremena", "timezone": "Vremenska zona", "roleRestriction": "Ograničenje roli", "headerRestriction": "Ograničenje zaglavlja", "ttSwitchDarkmode": {"heading": "Prebacivanje svetlog/tamnog režima", "body": ""}, "advancedEditor": "Napredni urednik", "externalId": "Spoljni ID", "passwordValidationMin": "Lozinka je prekratka. (<PERSON>na duž<PERSON>: {{count}})", "passwordValidationMax": "Lozinka je predugačka. (maks<PERSON>lna dužina: {{count}})", "passwordValidationUppercase": "Lozinka mora da sadrži veliko slovo. {{atLeast}}", "passwordValidationLowercase": "Lozinka mora da sadr<PERSON>i malo slovo. {{atLeast}}", "passwordValidationSymbols": "Lozinka mora da sadrži simbol. {{atLeast}}", "passwordValidationDigits": "Lozinka mora da sadrži broj. {{atLeast}}", "passwordValidationLetters": "Lozinka mora da sadr<PERSON>i slovo. {{atLeast}}", "atLeast": "<PERSON><PERSON>", "passwordValidationServiceErr": "Lozinka se trenutno ne može promeniti.", "enableTasksHandoverRole": "Uvek dozvoli prosleđivanje zadataka i pokretanje događaja za korisnike ove uloge", "shredded": "<PERSON><PERSON><PERSON>", "shredableVar": "Varijabla za usitnjavanje", "shredDocuments": "Uništite dokumente", "shredInDays": "Isec<PERSON> za (dana)", "fromBeginningOrendOfCase": "<PERSON><PERSON> p<PERSON>/kraja slu<PERSON>", "shredding": "Usitnjavanje", "addColumn": "<PERSON><PERSON><PERSON><PERSON> kolonu", "unsupportedBrowser": "Otvarate TeamAssistant u nepodržanom pretraživaču Internet Explorer, neke funkcije možda neće biti dostupne.", "ingoreProcessRights": "Ignorisanje prava na slučajeve", "cvHelpIngoreProcessRights": "Pregled uvek prikazuje sve slučajeve, bez obzira na prava", "upperLabel": "Stavite promenljivu ispod njenog imena", "darkMode": "<PERSON><PERSON>", "completedTasks": "Završeni <PERSON>", "permissions": "Dozvole", "caseVisibility": "Vidljivost slučaja", "visPerOrg": "Vidljivost po org. jedinice", "entity": "Entitet", "staticRight": "Statički zakon", "dynamicRight": "Dinamički zakon", "treeNodesAll": "Sve", "treeNodesMy": "<PERSON><PERSON>", "activeQueries": "Aktivni upiti", "query": "Upit", "confirmCancelQuery": "Da li ste sigurni da želite da otkažete upit?", "alrQueryNotFound": "Upit više nije pronađen.", "completeAgenda": "Kompletan agenda", "lockedBusinessUsers": "Zaključani poslovni korisnici", "structuredList": "Strukturisana lista", "ttCompetences": {"heading": "Upravljanje kompetencijama", "body": ""}, "competences": "Kompetencije", "competence": "Kompetencije", "competenceDelVar": "kompetenciju", "addCompetence": "Dodajte kompetenciju", "regularExpression": "Regularni izraz", "generationStatus": "Status generacije", "source": "<PERSON><PERSON><PERSON>", "historical": "Istorijski", "external": "Eksterni", "nextDay": "narednog dana", "embeddedVideoNotSupported": "<PERSON><PERSON> na<PERSON> je, va<PERSON> ne podržava ugrađene video zapise.", "alrSendingTestMailFailed": "Test e-pošte nije uspio.", "sent": "Poslano.", "mainColorEmail": "Glavna boja e-pošte", "confirmResetColors": "Da li ste sigurni da želite da resetujete boje?", "regularExpressions": "Regularni izrazi", "confirmDeleteLogo": "Da li ste sigurni da želite da izbrišete logo?", "loginLogoLightTheme": "Logo na ekranu za prijavu (svetli režim)", "loginLogoDarkTheme": "Logo na ekranu za prijavu (tamni režim)", "competenceRegexHelper": "<ul><li><b>%</b> može se koristiti kao N proizvoljnih znakova (ekvivalent *)</li><li><b>_</b> može se koristiti kao jedan proizvol<PERSON> ka<PERSON> (ekvivalent .)</li><li>Možete koristiti <b>^</b> da izbegnete ove specijalne znakove (ekvivalent \\)</li></ul>", "headerFont": "Font zaglavlja", "evenRow": "<PERSON>vna linija", "logo": "Logo", "widthForLogo": "Širina za logo", "monthStart": "Početak meseca", "monthEnd": "Na kraju meseca", "ttFavouriteType": "GET otvara vezu. POST šalje komandu: na primer, kada kreirate slučaj, kada se id zaglavlja šablona šalje u telu zahteva (možete da sačuvate u favorite preko <PERSON> case).", "confirmEmptyMultiinstanceVariable": "Da li ste sigurni da ova višestruka instanca ne zahteva promenljivu za ponavljanje?", "ttMenuPreview": "Konfiguracija menija prema korisničkim ulogama (značajnije uloge takođe vide dugmad za manje značajne uloge). Dugmad Novi slučaj i Kontrolna tabla su nepromenjena.", "menuPreview": "Pregled menija za izabranu ulogu", "confirmResetMenu": "Da li ste sigurni da želite da resetujete meni?", "alrFailedTasMenu": "Učitavanje konfiguracije TAS menija nije uspelo!", "security": "Bezbednost", "userRestrictions": "Ograničenja korisnika (prikaz)", "userRestrictionsProcesses": "Ignorirajte korisnička ograničenja za zadatke", "roleRestrictions": "Ogranič<PERSON>ja <PERSON> (prikaz)", "orgUnitRestrictions": "Ograničenja org. jedinice (prikaz)", "everyone": "Svi", "colleaguesOnly": "<PERSON><PERSON> kolege", "directSubordinates": "Direktni podređeni", "allSubordinates": "Svi podređeni", "none": "<PERSON><PERSON><PERSON>", "generalDocument": "Opšti dokument", "competenceRule": "Pravilo kompetencije", "competenceRules": "Pravila kompetencije", "ruleName": "<PERSON><PERSON>", "ttUseCompetenceRule": {"heading": "Primenite pravilo", "body": "<PERSON><PERSON>ira kompetenciju prema izabranom pravilu"}, "competenceText": "Tekst kompetencije", "competenceName": "Naziv kompetencije", "competenceReadOnlyInfo": "Kompetencija stvorena iz pravila ne može se menjati", "xmlProcessImport": "Uvoz XML procesa", "ttWidthForLogo": "Podesite širinu logotipa, a zatim umetnite logo. <PERSON><PERSON> moguc<PERSON>e promeniti širinu za već umetnut ili podrazumevani logotip.", "openCase": "<PERSON><PERSON><PERSON><PERSON>", "importHistory": "Istorija uvoza", "plannedImports": "Planirani u<PERSON>", "filePath": "Putanja do datoteke", "cronId": "ID crona", "taskResult": "Rezultat zadatka", "xmlFileSize": "Veličina XML datoteke", "attachmentSize": "Veličina priloga", "lastEdit": "Poslednja izmena", "timeCreated": "Vreme kreiranja", "importId": "ID uvoza", "importAudit": "Audit uvoza", "finishedImports": "Završeni u<PERSON>zi", "insertNote": "Umetnite belešku", "importXml": "Uvoz XML", "reImportXml": "Ponovo uvoz XML", "downloadXml": "Preuzmi XML", "downloadAttachment": "Preuzmi prilog", "skipXml": "Preskoči XML", "note": "Beleška", "attachmentName": "Naziv priloga", "importedCount": "<PERSON><PERSON><PERSON> u<PERSON>", "retryCount": "<PERSON><PERSON><PERSON>", "batchId": "ID doze", "copyPath": "<PERSON><PERSON><PERSON><PERSON> put", "cronRunId": "ID pokretanja", "cronRun": "Pokretanje crona", "trace_id": "ID traga", "ttMenuItemLabel": "Univerzalni naziv ako nema prevoda. Ako se koristi ključna reč za prevod, ona se prevodi automatski. Podrazumevana imena: tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "Red zadataka", "dissolveQueue": "Raspusti red", "taskQueueInitInfo": "Ova akcija je stvorila više zadataka za rešavanje. Ovde možete promeniti redosled njihovog rešavanja ili ih potpuno ukloniti iz reda.", "tryDarkTheme": "Primetili smo da više volite tamni režim. Kliknite da biste probali u TAS-u.", "alrInvalidURL": "Nevažeći format URL-a.", "alrInvalidHttps": "Nevažeći format URL adrese, mora da počinje sa https://", "importVariables": "Uvoz varijable", "ttVariablesImport": {"heading": "Uvoz varijable", "body": "Direktorijum sa definicijama varijable je izabran i zatim učitan."}, "classDiagram": "Dijagram klasa", "createVar": "<PERSON><PERSON><PERSON><PERSON>", "importObjectStates": "Uvoz stanja objekta", "unassigned": "Nedodeljen", "sortVars": "Sortiranje", "fillNames": "Popunite imena", "ttFillNames": {"heading": "Popunite imena", "body": "Popunjava prazna imena svih novih promenljivih u formatu \"Klasa, Atribut\" i sortira sve promenljive."}, "ttSortVars": {"heading": "Sortiranje", "body": "Sortira promenljive prema klasama i atributima."}, "ttRestore": {"heading": "Obnoviti", "body": "Vraća promenljive u prvobitno stanje kada se uvezu iz datoteke."}, "ttAddVarToBottom": {"heading": "Do<PERSON>j pro<PERSON>", "body": "Dodaje promenljivu na dno stranice."}, "confirmRestoreForm": "Da li ste sigurni da želite da vratite promenljive u njihovo prvobitno stanje?", "selectClass": "Označi k<PERSON>a", "importClassDiagram": "Uvezite dijagram klasa", "continue": "<PERSON><PERSON><PERSON>", "templateVars": "Varija<PERSON>", "newVars": "Nove varijable", "objectState": "<PERSON><PERSON> objekta", "alrDynTableExists": "Dinamička tabela već postoji!", "overwriteExistDynTable": "Zameni post<PERSON>́u din. tabelu", "confirmCancelImport": "Da li ste sigurni da želite da prekinete uvoz?", "alrDuplicateNames": "Podaci <PERSON><PERSON> du<PERSON> imena.", "stateVar": "Varijabla stanja", "importObjectStatesToDynTables": "Uvezite stanja objekta u dinamične tablice.", "defineObjectStatesVars": "Definišite promenljive koje drže stanja objekta.", "change": "Izmeniti", "classAndAttr": "Klasa i atribut", "clearQueue": "<PERSON>č<PERSON>i red", "sharing": "<PERSON>je<PERSON><PERSON>", "data": "<PERSON><PERSON><PERSON>", "open": "Otvorite", "dataSource": "<PERSON><PERSON><PERSON>", "dataPoints": "Podaci <PERSON>", "dataSeries": "Serija podataka", "valueCol": "Kolona v<PERSON>nosti", "aggregationCol": "Stupac agregacije", "timeDimension": "Vrem. dimenzija", "columns": "<PERSON><PERSON>", "week": "sedmica", "weekday": "radni dan", "monthVar": "mesec", "overviewFilter": "Filter pre<PERSON>a", "globalFilters": "<PERSON><PERSON>", "filterDefinition": "Definicija filtera", "newFilter": "Novi filter", "addFilter": "<PERSON><PERSON><PERSON> filter", "filterOptions": "Opcije filtera", "addOption": "<PERSON><PERSON><PERSON>", "graphPreview": "<PERSON><PERSON> grafi<PERSON>a", "alrGlobalFilterDownloadFailed": "Preuzimanje globalnih filtera nije uspelo!", "alrGlobalFilterSaveFailed": "Snimanje globalnih filtera nije uspelo!", "filterOption": "Opcija filtera", "editFilter": "Uredite filter", "fillOptionsFromVar": "Popunite opcije iz promenljive", "fillOptionsDynamically": "Dinamički popunjavajte opcije", "filterOptionsFilledDynamically": "Dinamički iz promenljive", "dayOfMonth": "dan u mesecu", "dateVar": "datum", "group": "Grupa", "ttDataSource": "Izaberite \"Tačke podataka\" ako želite da unesete svaku pojedinačnu tačku grafikona posebno. Ako želite da se tačke generišu na osnovu izabrane dimenzije, izaberite \"Serija podataka\"", "ttDataSeriesAggregation": "Izaberite tip agregacije. Omogućava vam da kreirate zbirne informacije od zapisa (slučajeva).", "ttDataSeriesColumns": "Izaberite redom sve kolone pomoću kojih ćete kreirati grupe (agregacije) za izračunavanje zbirnih vrednosti.", "listOfFiltersIsEmpty": "Lista filtera je prazna.", "fromVariable": "Iz promenljive", "showOptionsFromCount": "<PERSON><PERSON><PERSON><PERSON> op<PERSON> (od {{count}})", "sum": "Zbir", "minimum": "Minimum", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Statistike", "unfilled": "Nepopunjen", "globalFilterDescription": "Globalni filter pruža korisnicima grafikona opcije koje filtriraju ulazne podatke za grafikon. Sve opcije filtera se mogu definisati na ovom ekranu.", "ttDelGraph": {"heading": "Izbriši grafikon", "body": "Briše izabrani grafikon."}, "ttEditGraph": {"heading": "Izmeni grafikon", "body": "Omogućava vam da uređujete izabrani grafikon."}, "ttCopyGraph": {"heading": "<PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON> gra<PERSON>."}, "ttAddGraph": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućava vam da definišete novi grafikon."}, "axisXName": "Naziv ose X", "axisYName": "Naziv ose Y", "showValues": "Prikaži vrednosti", "defaultOption": "Podrazume<PERSON> opcija", "yearStart": "Početak godine", "yearEnd": "<PERSON><PERSON> godine", "thisMonth": "Ovog meseca", "lastMonth": "<PERSON><PERSON><PERSON> me<PERSON>c", "thisYear": "<PERSON>ve godine", "lastYear": "<PERSON><PERSON><PERSON> god<PERSON>", "scheduledTasks": "<PERSON><PERSON><PERSON><PERSON>", "scheduled": "<PERSON><PERSON><PERSON><PERSON>", "dueDateStart": "<PERSON>tum <PERSON>", "lastRescheduled": "Poslednji raspored", "reschedule": "<PERSON><PERSON><PERSON> r<PERSON>", "alrTasksRescheduling": "Promena rasporeda zadataka...", "alrTasksRescheduled": "Zadaci su ponovo raspoređeni.", "alrTasksRescheduleFailed": "Promena rasporeda zadataka nije uspela.", "onlyCurrentOrFutureDates": "<PERSON>o danas ili budući datum", "passwordValidations": "Politika lozinke", "readonlyConfigInfo": "Vrednost je samo za čitanje", "alrTasksCountFailed": "Brojanje zadataka nije uspelo.", "confirmActivateTasks": "Da li ste sigurni da želite da aktivirate izabrane zadatke?", "confirmSuspendTasks": "Da li ste sigurni da želite da obustavite izabrane zadatke?", "tskOffset": "Varijabilno <PERSON>", "workWeek": "<PERSON><PERSON><PERSON>", "agenda": "Dnevni red", "noEventsInRange": "U ovom rasponu nema događaja", "activitiesDesc": "Opis aktivnosti", "allShort": "Sve", "numberOfEvents": "<PERSON><PERSON><PERSON>", "weekNumber": "<PERSON><PERSON><PERSON>", "cannotBeEdited": "Ne može se uređivati", "cannotBeMoved": "Ne može se pomeriti", "alrTempVarSaveSameNameFailed": "Promenljiva sa ovim podrazumevanim imenom već postoji, unesite drugo ime.", "maxUsersCountRole": "Maksimalan broj korisnika u ulozi", "unlimitedAssignLeaveBlankInfo": "Za neograničene zadatke, ostavite polje prazno.", "cvOwner": "Vlasnik pregleda", "changePassword": "Promenite lozinku", "passwordExpired": "<PERSON><PERSON>ša lozinka je istekla. Molimo vas da je promenite.", "passwordWillExpire": "<PERSON><PERSON>ša lozinka će uskoro isteći. Unesite novu lozinku.", "userParameters": "Korisnički parametri", "filterSortingHelper": "Sortiranje po jednoj ili više kolona u filteru onemogućava opciju za ručno sortiranje kolona direktno u tabeli.", "importUsers": "Uvezi koris<PERSON>", "importRoles": "<PERSON><PERSON><PERSON>", "existingEntityRows": "Redovi sa već postojećim entitetima (mogu se prepisati)", "fileRow": "Red datoteke", "existingEntityRowsMultiple": "Redovi sa entitetima koji već postoje više od jednom (neće biti uvezeni)", "importOrgUnits": "Uvezi organizacione jedinice", "structureImportExport": "Uvoz/izvoz strukture", "fillAttributes": "Popuni atribute", "structurePreview": "Pregled strukture", "invalidRowsForImport": "Nevažeći redovi (nedostaju obavezni podaci)", "duplicateRowsForImport": "Redovi sa dupliranim podudarnim podacima (neće biti uvezeni)", "newEntityRows": "Redovi sa novim entitetima za uvoz", "existingNameRowsForImport": "Redovi sa imenima koji već postoje na drugim entitetima (neće biti uvezeni)", "overwriteExisting": "Zameni <PERSON>", "structurePreviewHelper": "Pregled strukture prikazuje dve različite situacije: uvoz samo novih organizacija, ili uvoz i novih i postojećih organizacija koje će biti prepisane. Sve promene u poređenju sa trenutnom strukturom su označene crvenom bojom.", "validateAndShowPreview": "Potvrdite i pregledajte", "uploadNewFile": "Otpremi novu datoteku", "userStatus": "Status korisnika", "importedFile": "<PERSON><PERSON><PERSON>", "pairUsersBy": "Upari korisnike po", "assignOrgBy": "Dodeli organizaciji od", "pairRolesBy": "Upari uloge po", "pairUnitsBy": "Upari jedinice po", "unitHierarchyCol": "Kolona hijerarhije jed<PERSON>", "dontAssign": "<PERSON><PERSON><PERSON><PERSON>", "infoImportDataValidated": "UPOZORENJE: Podaci su upravo potvrđeni zbog promena u podešavanjima. Preporučujemo da se vratite i proverite novi pregled uvoza.", "assignUserRolesMethod": "Metod za dodeljivanje uloga korisnicima", "assignUserRolesMethodHelp": "Metod dodeljivanja uloga: dodajte već dodeljenim ulogama ili potpuno zamenite trenutno dodeljene uloge novim dodeljenim ulogama.", "existingRolesForImport": "<PERSON><PERSON><PERSON> posto<PERSON><PERSON>e ul<PERSON>e (mogu se prepisati)", "existingRoleNamesForImport": "Uloge sa imenima koja već postoje sa drugim ulogama (neće biti uvezena)", "newRolesForImport": "Nove uloge za uvoz", "userRolesForImport": "Redovi sa korisničkim ulogama za dodeljivanje", "nonExistentUsersForImport": "Redovi sa nepostojećim koris<PERSON> (uloge neće biti dodelje<PERSON>)", "multipleExistingUsersForImport": "Redovi sa više od jednog postojećeg korisnika (uloge neće biti dodeljene)", "invalidOrgsForImport": "Nevažeći redovi (nedostaju obavezni podaci ili pogrešna hijerarhija)", "keepOriginal": "Zadrži original", "assignOrgByHelp": "Ako izaberete kolonu iz datoteke, možete odrediti klasifikaciju organizacije za nove i postojeće korisnike. Ako izaberete određenu organizaciju, svi uvezeni ili ažurirani korisnici će biti dodeljeni ovoj organizaciji.", "creatingRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assigningRolesToUsers": "Dodeljivanje uloga korisnicima", "newUsers": "Novi korisnici", "existingUsers": "Postojeći korisnici", "fromFile": "<PERSON><PERSON> da<PERSON><PERSON>ke", "alrCsvXlsxUploadWrongExtension": "Otpremajte samo datoteke sa ekstenzijom *.csv ili *.xlsx", "importNewAndExisting": "Uvezite nove entitete i zamenite postojeće", "importNewOnly": "Uvezite samo nove entitete", "importNewAndExistingRoles": "Uvezite nove uloge i zamenite postojeće", "importNewRolesOnly": "Samo uvoz novih uloga", "importRolesHelper": "Podešavanja za uvoz samih uloga. Dodeljivanje uloga korisnicima je regulisano onim što je podešeno u \"Upari korisnike po\" i uvek se primenjuje na nove i postojeće uloge.", "statisticsColorHelper": "<PERSON>ko boje nisu i<PERSON>ne ru<PERSON>, ili ako ima manje izabranih boja od kolona, boje koje nedostaju se generišu automatski. Generisane boje nikada ne sadrže tamne ili previše svetle ni<PERSON>, one se mogu izabrati samo ručno.", "caseService": "Usluga slučaja", "taskService": "Task Service", "editTasks": "<PERSON>z<PERSON>i zadatke", "editCases": "<PERSON><PERSON><PERSON>", "deleteTasks": "Izbriši zadatke", "deleteCases": "Izbriši slučajeve", "serviceOperationsInfo": "Označite i popunite promenljive koje želite da promenite.", "erased": "<PERSON><PERSON>brisani", "statusErrored": "Greška", "serviceOperations": "Uslužne operacije", "runCalcsOnStart": "Pokreni proračune na startu", "taskReactivation": "Ponovno aktiviranje zadataka", "taskCompletion": "Završetak zadataka", "caseReactivation": "Ponovno aktiviranje slučajeva", "caseCompletion": "Završetak slučajeva", "openTask": "Otvori zadatak", "changeEntity": "<PERSON><PERSON><PERSON> en<PERSON>t", "selectTableColumns": "<PERSON>zaberi kolone tabele", "parentCase": "Roditeljski slučaj", "ownerOrganization": "Organizacija vlasnika", "confirmTaskReactivation": "Da li ste sigurni da želite ponovo da aktivirate izabrane zadatke?", "confirmCaseReactivation": "Da li ste sigurni da želite da ponovo aktivirate izabrane slučajeve?", "confirmTaskCompletion": "Da li ste sigurni da ž<PERSON>te da dovršite izabrane zadatke?", "confirmCaseCompletion": "Da li ste sigurni da želite da završite izabrane slučajeve?", "selectAllFilterMustBeActive": "Najmanje jedan filter mora biti aktivan da biste izabrali sve stavke.", "changeEntities": "Promeni entitete", "disabledDifferentTemplates": "Ne može se promeniti jer entiteti nisu iz istog <PERSON>.", "actions": "<PERSON><PERSON><PERSON><PERSON>", "taskTemplateId": "ID šablona zadatka", "caseTemplateId": "ID šablona slučaja", "actionInfoCheckLogs": "Akcija će se izvršiti u pozadini, molimo vas da proverite evidenciju.", "alrServiceOperationsColumnsFailed": "Čuvanje podešavanja kolona uslužnih operacija nije uspelo.", "confirmResetSelectedCols": "Da li ste sigurni da želite da resetujete sačuvane kolone tabele?", "instanceVars": "Promenljive instance", "usrId": "ID korisnika", "orgId": "ID organizacije", "titlePrefix": "Prefiks naslova", "titleSuffix": "Sufiks naslova", "accessRoleId": "<PERSON> uloge pristupa", "maxAssigns": "Maks<PERSON>lni z<PERSON>", "client": "<PERSON><PERSON><PERSON><PERSON>", "bigValue": "Velika vrednost", "unitId": "ID jedinice", "roleId": "<PERSON> uloge", "paramId": "ID parametra", "varId": "ID promenljive", "parentId": "ID roditelja", "openUser": "Otvori korisnik", "openRole": "<PERSON><PERSON><PERSON><PERSON> ul<PERSON>", "openUnit": "<PERSON><PERSON>vor<PERSON> jed<PERSON>ca", "units": "<PERSON><PERSON><PERSON>", "managerId": "ID menadžera", "externalStatus": "Spoljni status", "additionalId": "Dodatni ID", "parentIc": "IC od roditelja", "companyIc": "IC of compani", "textValue": "Vrednost teksta", "dateValue": "Vrednost datuma", "numberValue": "Numerička vrednost", "loginCount": "<PERSON><PERSON><PERSON>", "externalLogin": "S<PERSON>jn<PERSON> prijava", "badLoginCount": "<PERSON><PERSON> broj prijava", "passwordLastChange": "Poslednja promena lo<PERSON>ke", "solverEvaluation": "Evaluac<PERSON>", "solverWillBe": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>e biti", "possibleSolvers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectReferencePerson": "Izaberite referentnu o<PERSON>bu", "evaluateSolver": "Evaluate solver", "referenceUserForEval": "Referentna osoba za evaluaciju", "andOthers": "...i drugi", "showLess": "...prika<PERSON><PERSON> manje", "alrSessionExpired": "<PERSON><PERSON>ša sesija je istekla, prijavite se ponovo.", "mailPromptlyInfo": "Korisnik kontinuira jednokratna obaveštenja o novim zadacima, gde su rešetki. Ova upozorenja će se slati samo ako zadatak nije rešen {{minutes}} minuta od njene aktivacije.", "mailPullInfo": "Korisnik kontinuirano prima jednokratna obaveštenja o novim zadacima koji su dostupni za pretplatu, a korisnik je njihov mogući rešivač. Obaveštenje se izlazi u trenutku aktiviranja datog zadatka u okviru rada.", "mailTotalInfo": "Korisnik je povremeno primao pregled sa zadacima koji će se završiti, od čega su rešili. Ako zadatak nema direktan reš<PERSON>č, vlasnik procesa je obavešten. Ako je korisnik predstavljen, obaveštenje primiće njegov predstavnik.", "mailEscalationInfo": "Korisnik je povremeno primao pregled sa zadacima koji su se završili koji su naširili rok. Oni su obavešteni da li su supervizor zadatka (a ne istovremeno i ne njegov rešenje) ili su direktni menadžer korisnika koji je rešenje. Ako zadatak nema rešenje, vlasnik procesa se smatra nadzornikom. Ako je korisnik predstavljen, obaveštenje spominje ko je trenutni zastupnik.", "calcSourceOverwriteWarning": "<PERSON><PERSON>, izvor se prepisuje ES6 sintaksom!", "changeStatus": "Promenite status", "confirmChangeEmailStatus": "Da li zaista želite da promenite status izabranih imejlova?", "logInAgain": "Prijavite se ponovo", "migrations": "<PERSON><PERSON><PERSON><PERSON>", "launchDate": "<PERSON><PERSON> la<PERSON>", "stepName": "Naziv koraka", "runId": "Pokreni ID", "clone": "Klon", "confirmDeleteCron": "Da li zaista želite da izbrišete izabrani cron?", "alrCronDeleted": "<PERSON><PERSON> je obrisan!", "wantToContinueQ": "Da li želite da nastavite?", "valueCannotBeEntered": "Vrednost se ne može uneti", "processingQueues": "Redovi za obradu", "pause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fillOptionsFromVarHelper": "Opcije filtera se mogu popuniti samo iz promenljivih tipa DT, DL, LT, LD, LN i D, koje ne dozvoljavaju višestruki izbor.", "defaultTemplateName": "Podrazumevano ime šablona", "defaultTaskName": "Podrazumevano ime zadatka", "defaultVariableName": "Podrazumevano ime promenljive", "variableName": "Naziv promenljive", "alrNoDataFound": "Podaci nisu pronađeni", "ttProcessingQueuesInfo": "Redovi za obradu su onemogućeni. Da biste omogu<PERSON>, podesite bar jednu od konfiguracija \"scaling.queue.*.enabled\" na true.", "businessUsers": "Poslovni korisnici", "completeHrAgenda": "Kompletan dnevni red HR", "usageStatsByHeader": "Statistika korišćenja prema zaglavlju", "usageStatsByOrgUnit": "Statistika korišćenja prema org. jedinici", "usageStatsByUser": "Statistika korištenja po korisniku", "completedTasksNum": "B<PERSON>j završ<PERSON>h zadataka", "startedProcessesNum": "B<PERSON>j započetih predmeta", "ideHelp": "Pritisnite Ctrl + razmak u uređivaču da vidite predloge, pritisnite ponovo za detaljniju pomoć. Pritisnite F1 da vidite sve dostupne komande i prečice na tastaturi. Za više informacija pogledajte <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>dokumentaciju urednika</a>.", "restHelp": "Unesite URL za jednu od usluga TAS tabela (npr. '/tasks/mine') i nakon učitavanja usluge izaberite kolone tabele koje želite da prikažete u kontejneru.", "defaultGraphName": "Podrazumevano ime grafikona", "graphName": "<PERSON><PERSON> grafi<PERSON>a", "ttStatistics": {"heading": "Statistika", "body": ""}, "defaultAxisXName": "Podrazumevano ime Ks ose", "defaultAxisYName": "Podrazumevano ime I ose", "defaultFilterName": "Podrazumevano ime filtera", "filterName": "<PERSON><PERSON><PERSON>", "defaultOptionName": "Podrazumevano ime opcije", "optionName": "Naziv opcije", "defaultOverviewName": "Podrazumevano ime pregleda", "overviewName": "<PERSON><PERSON> pre<PERSON>", "eventName": "<PERSON><PERSON>", "wantToOverrideEs6": "<PERSON><PERSON> z<PERSON> da prepiš<PERSON>, napiš<PERSON> <b>ES6</ b>", "processArchivation": "<PERSON><PERSON> a<PERSON><PERSON>", "processUnarchivation": "<PERSON><PERSON> <PERSON><PERSON>", "resendEmail": "Prosledi e-poštu", "alrFailedSendEmail": "Slanje e-pošte nije uspelo", "ttResendEmail": {"heading": "Prosledi e-poštu", "bodi": "Ponovo šalje prethodno poslato obaveštenje putem e-pošte. Primaoci se mogu promeniti ili dodati."}, "addCurrentScreenToFavourite": "Dodajte trenutni ekran u svoje favorite", "attachmentAdd": "Dodajte dokument", "createNewCase": "Stvaranje novog slučaja", "moreLanguage": "Ostale varijante jezika", "notesAdd": "Do<PERSON><PERSON><PERSON>", "notesNew": "Nova beleška", "removeCurrentScreenFromFavourite": "Izvadite trenutni ekran od favorita", "setDashboard": "Uredite kontrolnu tablu", "chooseFromCases": "Izaberite iz slučajeva", "folders": "<PERSON><PERSON><PERSON><PERSON>", "newFolderBtn": "Nova fascikla", "documentInfo": "Informacije o dokumentu", "userInfo": "Informacije o korisniku", "deleteImage": "Izbriši sliku", "profilePhoto": "Fotografija profila", "profilePhotoCaption": "Koristite fotografiju u jpeg, jpg, png ili gif formatu.", "updatePhoto": "Ažuriraj fotografiju", "mailNotifications": "Obaveštenja e-poštom", "userPreferences": "Korisnička preference", "userSettings": "Korisnička podešavanja", "allVices": "Sve zamene", "createVice": "<PERSON><PERSON><PERSON><PERSON>", "editVice": "Uredite zamenu", "viceTip": "Zamena vam omogućava da prosledite svoj dnevni red kolegi", "emptyDataMessage": "Nema više ničega ovde", "addFirstNote": "Dodaj prvu napomenu", "noResultsFor": "Nema rezultata za:", "noCurrentTasks": "<PERSON><PERSON> trenut<PERSON> zada<PERSON>", "checkYourSearch": "Proverite pretragu i pokušajte ponovo.", "noFavOverviews": "<PERSON><PERSON><PERSON> pre<PERSON>", "favOverviewsTip": "Dodajte pregled u svoje favorite sa zvezdicom", "noHiddenOverviews": "<PERSON><PERSON><PERSON> s<PERSON>rive<PERSON> pre<PERSON>e", "addOverview": "<PERSON><PERSON><PERSON> pre<PERSON>", "hidden": "Skriven", "removeConfirm": "Ukloni", "removeItem": "Da li ste sigurni da želite da uklonite {{variable}}?", "changePicture": "<PERSON><PERSON><PERSON> s<PERSON>u", "saveFilter": "Sačuvaj filter", "addAnotherVice": "<PERSON><PERSON><PERSON><PERSON>", "saveVice": "Sačuvaj<PERSON> zamenu", "firstLastName": "<PERSON><PERSON> i prezime", "taskInfo": "Informacije o zadatku", "emptyFavsTip": "Dodajte favorite pomo<PERSON><PERSON><PERSON>a", "saveAndClose": "Sačuvaj i zatvori", "usersCanEditOverview": "Korisnici mogu da uređuju pregled", "assignedUsers": "Dodeljeni korisnici", "assignedOrgUnits": "Dodeljene organizacione jedinice", "assignedRoles": "<PERSON><PERSON><PERSON><PERSON>", "otherLangVariants": "Druge jezičke varijante", "moveToSharing": "Premesti u deljenje", "insertDocumentsPerm": "Korisnik ima dozvolu da ubacuje dokumente i beleške", "saveNewPassword": "Sačuvaj novu lozinku", "confirmSubscription": "Potvrdi Pretplatu", "subscriptionCaption": "<PERSON><PERSON><PERSON><PERSON> pregled će vam stići putem e-pošte u postavljenom vremenu.", "by": "Prema", "frequency": "Frekvencija", "termination": "Prekidanje", "ofTheMonth": "U mesecu", "endOnDate": "Završi na datum", "endAfter": "<PERSON><PERSON><PERSON><PERSON><PERSON>on", "onlyOnWorkingDays": "<PERSON><PERSON> radnim danima", "occurrences": "pojave", "dayOfWeekBy": "<PERSON> u ne<PERSON> prema", "calendarDayBy": "Kalendarskog dana prema", "dateBy": "datuma", "byDate": "<PERSON><PERSON> datumu", "byOccurrenceCount": "Prema broju pojava", "infinitely": "Beskrajno", "dayOfMonthAdornment": ". dan meseca", "ordinalAdornment": ".", "toDateBeforeFromError": "Datum 'Do' ne može biti pre datuma 'Od'", "vice": "Zamena", "previewShown": "<PERSON><PERSON> prikazan", "duplicate": "Duplikat", "hideBtn": "<PERSON><PERSON><PERSON><PERSON>", "userView": "Prikaz korisnika", "adminView": "Prikaz administratora", "or": "<PERSON><PERSON>", "overlappingVicesError": "Zamene se ne mogu preklapati", "fileVar": "dato<PERSON><PERSON>", "nodeVar": "<PERSON><PERSON>", "uploadDifferentFile": "Otpremi drugu datoteku", "uploadedFile": "<PERSON>tp<PERSON><PERSON><PERSON><PERSON>", "refreshPage2": "Osveži stranicu", "refreshPageCaption": "Osvežite stranicu u svom pretraživaču da biste nastavili.", "ttCopy": {"heading": "<PERSON><PERSON><PERSON>", "body": "Omogućava vam da kopirate izabranu stavku sa mogućnošću izmene nekih parametara."}, "alrError_INVALID_CSV_MAPPING": "Nije pronađen CSV kolona \"%s\" u mapiranju događaja. Kontaktirajte administratora aplikacija.", "documentPreview": "Pregled dokumenta", "moveUp": "Pomeranje nagore", "moveDown": "Pomerite se nadole", "moveToFilter": "<PERSON><PERSON><PERSON> u filter", "moveToSorting": "Pređi na sortiranje", "addSorting": "<PERSON><PERSON><PERSON>", "cancelFilters": "Otkaži filtere", "docUploadedImmediately": "Dokument će biti učitan odmah", "moreOptions": "<PERSON><PERSON>", "docSearchPlaceholder": "Npr. faktura.pdf...", "tasksSearchPlaceholder": "Npr. Unesite novu fakturu...", "docUploadedImmediatelyPrivate": "Dokument će odmah biti učitan kao privatan", "takeTsk": "Preuzmi zadatak", "tasksActive": "Aktivni zadaci", "subprocesses": "Podprocesi", "cancelAuthorization": "Otkaži ovlašćenje", "cancelAuthorizationConfirm": "Da li ste sigurni da želite da otkažete autorizaciju uređaja?", "linkMobileApp": "Link Mobile App", "mobileApp": "Mobilna aplikacija", "scanThisQr": "Skenirajte ovaj KR kod svojim mobilnim uređajem.", "scanningQr": "Skenira se KR kod. Molimo sačekajte.", "deviceName": "<PERSON><PERSON>", "newDeviceName": "Naziv novog uređaja", "registrationDate": "Datum registracije", "lastLogin": "Poslednja prijava", "mobileNotifications": "Mobile Notifications", "disableMobileNotification": "Turning off notifications on mobile", "newQrCode": "Novi KR kod", "inactiveScanQr": "Neaktivan - skenirajte KR kod.", "enableNotifications": "Omogući obaveštenja", "tip": "Savet: {{message}}", "alrFavContainerAlreadyExists": "Kontejner favorita već postoji.", "addGraph": "<PERSON><PERSON><PERSON>", "newRow": "Nova red", "confirmSetDefaultDashboard": "Da li stvarno želite da postavite trenutnu kontrolnu tablu kao podrazumevanu za sve korisnike?", "changeMayAffectAllUsers": "Ova promena može uticati na sve korisnike.", "noOverviewsTip": "Napra<PERSON>te novi pregled koriste<PERSON>i dugme \"Dodaj pregled\"", "removeFromHidden": "Ukloni iz skrivenih", "last7Days": "Poslednjih 7 dana", "last14Days": "Poslednjih 14 dana", "last30Days": "Poslednjih 30 dana", "lastCalendarMonth": "Poslednji kalendar mesec", "lastQuarter": "Poslednji kvartal", "last12Months": "Poslednjih 12 meseci", "lastCalendarYear": "Poslednja kalendar godina", "noFilterSet": "<PERSON><PERSON> filter", "noSortingSet": "<PERSON><PERSON>", "deleteGroup": "Obriši grupu", "newGroup": "Nova grupa", "operators": "Operatori", "withActiveTask": "Sa aktivnim zadatkom", "withoutActiveTask": "Bez aktivnog zadatka", "withNoTerm": "Bez roka", "withTerm": "Sa rokom", "securityAndAuthentication": "Bezbednost i autentifikacija", "dataIntegrationAndManagement": "Integracija i upravljanje podacima", "appManagementAndConfig": "Upravljanje i konfiguracija aplikacije", "monitoringAndMaintenance": "Nadgledanje i održavanje", "adminSearchPlaceholder": "Na primer, <PERSON><PERSON><PERSON> da<PERSON>...", "authenticationAdminDescription": "Opcije autentifikacije korisnika", "certificatesAdminDescription": "Sertifikati za TAS", "elasticsearchAdminDescription": "Integracija sa Elasticsearch-om", "xmlProcessImportAdminDescription": "Uvoz XML procesa korišćenjem cron zapisa XMLProcessImport.js", "structureImportExportAdminDescription": "Uvoz/izvoz organizacione strukture, korisnika i uloga", "dmsAttributesAdminDescription": "Spisak atributa dokumenata u DMS-u", "dynTablesAdminDescription": "Čuvanje podataka u dinamičkim tabelama", "csvAdminDescription": "Manipulacija CSV fajlovima u aplikaciji", "configurationAdminDescription": "Konfiguracija aplikacije", "settingsAdminDescription": "Postavke identifikacije kompanije i ostali administrativni zadaci", "logsAdminDescription": "Upravljanje i pregled logova aplikacije", "migrationsAdminDescription": "Migracija podataka i konfiguracija aplikacije", "guidesAdminDescription": "Pomoć i vodiči za korisnike", "schemeAdminDescription": "<PERSON><PERSON> b<PERSON>, logotip i drugi elementi u aplikaciji", "sequencesAdminDescription": "Upravljanje sekvencama korišćenim u šablonima", "serviceConsoleAdminDescription": "Administrativne komande aplikacije putem servisne konzole", "serviceOperationsAdminDescription": "Kompletno upravljanje servisnim operacijama", "scriptsAdminDescription": "Upravljanje često korišćenim skriptama preko različitih šablona", "appStatusAdminDescription": "Informacije o trenutnom stanju aplikacije", "usageStatsAdminDescription": "Prikaz statistika korišćenja aplikacije", "maintenanceAdminDescription": "Postavke održavanja i izvršavanje održavajućih zadataka", "scheduledTasksAdminDescription": "Upravljanje svim zakazanim z<PERSON>", "publicFilesAdminDescription": "Upravljanje javnim datotekama i dokumentacijom", "cronsAdminDescription": "Automatizacija redovnih zadataka", "hrAgendaAdminDescription": "Upravljanje dnevnim redom korisnika unutar HR", "emailsQueueAdminDescription": "Upravljanje redom e-pošte i sva komunikacija putem e-pošte od TAS-a", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Dodavanje stavke u favorite nije uspelo", "alrRemoveFavItemFailed": "Uklanjanje stavke iz omiljenih nije uspelo", "alrAddHiddenItemFailed": "Dodavanje skrivene stavke nije uspelo", "alrRemoveHiddenItemFailed": "Uklanjanje skrivene stavke nije uspelo", "display": "Prikaz", "compact": "Kompaktno", "standard": "Standardno", "comfortable": "Udobno", "exportTo": "Izvezi u", "adminMenuTip": "Dodajte svoje stavke u administraciju u omiljene. Klikom na zvezdicu prikazaće se stavka baš ovde.", "editorDocumentation": "Dokumentacija editora", "addSection": "<PERSON><PERSON><PERSON>", "insertSection": "<PERSON><PERSON><PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON>", "sections": "Sek<PERSON><PERSON>", "toTop": "Na početak", "toEnd": "Na kraju", "alrSectionNotBeEmpty": "<PERSON><PERSON><PERSON> ne sme biti prazan", "confirmDeleteSection": "Da li zaista želite da izbrišete odeljak?", "sectionVarsMoveAllTasks": "Promenljive u svim zadacima će biti premeštene iz uklonjenog odeljka u promenljive bez sekcije.", "sectionVarsMove": "Promenljive će biti premeštene iz uklonjenog odeljka u promenljive bez sekcije.", "actionCannotUndone": "<PERSON>va radnja se ne može poništiti.", "overviewOfAllNews": "Pregled svih vesti", "copyOverview": "<PERSON><PERSON> kop<PERSON>", "create": "<PERSON><PERSON><PERSON><PERSON>", "copyExistingOverview": "<PERSON><PERSON><PERSON> pre<PERSON>", "selectOverview": "<PERSON><PERSON><PERSON><PERSON> pregled", "chooseFromOverviews": "Izaberite iz pregleda...", "selectTemplate": "<PERSON><PERSON><PERSON>", "chooseFromAvailableTemplates": "Izaberite neki od dostupnih šablona...", "loginWithUsernamePassword": "Prijavite se sa korisničkim imenom i lozinkom", "signInWithCorporateIdentity": "Prijavite se sa korporativnim identitetom", "whatsNewInTAS": "Šta je novo u TAS-u?", "whatsNewInTASDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nove funkcije, saveti, trikovi i sve što treba da znate.", "justOpen": "Samo otvoreno", "editOverview": "<PERSON><PERSON><PERSON><PERSON> pregled", "noGraphsTip": "Kreirajte novi grafikon pomoću dugmeta \"Dodaj grafikon\"", "noDocumentsTip": "Dodajte dokument u zadatak ili pomoću dugmeta \"Dodaj\"", "noFilesTip": "Dodajte novu datoteku pomoću dugmeta \"Dodaj\"", "less": "<PERSON><PERSON>", "notContains": "<PERSON>e sadrži", "notcontains": "ne <PERSON><PERSON><PERSON>i", "notEquals": "<PERSON><PERSON>", "notequals": "nije jed<PERSON>", "factorySettings": "Fabrička podešavanja", "previewCollapsedNavMenu": "Pregled prekupanog navigacionog menija", "previewExpandedNavMenu": "Pregled nepakiranog navigacionog menija", "logoForCollapsedNavMenu": "Logo za pakovanje navigacionog menija", "logoForExpandedNavMenu": "Logo za raspakirani meni za navigaciju", "organisationLogo": "Logotip organizacija", "pickLogoOrganisation": "Izbor logotipa za organizaciju", "addLogo": "Dodaj logotip", "clickForAddLogoOrDrop": "Kliknite da biste ovde dodali logotip ili pad datoteka ovde", "useLogoSizeMin": "Koristite logotip minimalne velič<PERSON>", "logoForLightTheme": "Logo za svetli re<PERSON>im", "logoForDarkTheme": "Logo za tamni režim", "sharedWithMe": "Deljeno sa mnom", "myOverview": "<PERSON><PERSON> pre<PERSON>", "getMobileAppText": "Preuzmite mobilnu aplikaciju iz prodavnice aplikacija", "noDocuments": "<PERSON><PERSON>", "noNotes": "<PERSON><PERSON>", "noFiles": "<PERSON><PERSON>", "addFirstDocument": "Dodaj prvi dokument", "killed": "Ubijen", "chooseNewLogo": "Izaberite novi logotip", "function": "Funkcija", "groupFunction": "Funkcija između grupa", "mobileAppAuthFailed": "Potvrda identiteta mobilne aplikacije nije uspela.", "currentDocumentVersion": "Trenutna verzija dokumenta", "csp": "Politika bezbednosti sadržaja", "documentsDelete": "Obriši dokumente", "confirmDocumentsDelete": "Da li ste sigurni da želite da obrišete izabrane dokumente?", "confirmDocumentsDownload": "Da li želite da preuzmete izabrane dokumente?", "firstNum": "prvih {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Preuzmi dokumente", "caseLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archiveCases": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unarchive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmArchiveCases": "Da li ste sigurni da želite da arhivirate izabrane slučajeve?", "archiveInDays": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dane)", "archived": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "archivedx": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrArchivingCase": "<PERSON><PERSON><PERSON><PERSON> se arhivira...", "alrCaseArchived": "<PERSON><PERSON><PERSON><PERSON> je arhivir<PERSON>.", "alrLackOfPermsToArchiveCase": "Nemate dovoljno dozvola za arhiviranje slučaja.", "alrArchiveCaseFailed": "Arhiviranje slučaja nije uspelo.", "alrUnarchivingCase": "<PERSON><PERSON><PERSON><PERSON> se de-arhivira...", "alrCaseUnarchived": "<PERSON><PERSON><PERSON><PERSON> je de-a<PERSON><PERSON><PERSON><PERSON>.", "alrLackOfPermsToUnarchiveCase": "Nemate dovoljno dozvola za de-arhiviranje slučaja.", "alrUnarchiveCaseFailed": "De-arhiviranje slučaja nije uspelo.", "byUser": "Po korisniku", "byAgenda": "Po agendi", "agendaHandover": "Prenos agende", "activeUsers": "Aktivni korisnici", "lockedUsers": "Zaključani korisnici", "allUsers": "<PERSON>vi k<PERSON>i", "inactiveUsers": "Neaktivni korisnici", "hrAgendaSearchPlaceholder": "Npr. <PERSON><PERSON> ...", "completeAgendaHandover": "Kompletna primopredaja dnevnog reda", "handoverCases": "Slučaji za primopredaju", "handoverTasks": "Zadaci primopredaje", "handoverVars": "Handover Variables", "changeTaskOwner": "<PERSON><PERSON><PERSON>", "confirmHandover": "Potvrdi primopredaju", "filterCasesByHeaderTip": "Možete filtrirati sve slučajeve pod istim zaglavljem u koloni Zaglavlje.", "userAgendaSelectedHandover": "Prenos <b style=\"color: {{color}};\">odabrane</b> k<PERSON><PERSON><PERSON><PERSON> agende", "userAgendaCompleteHandover": "Prenos <b style=\"color: {{color}};\">kompletne</b> korisnič<PERSON> agende", "confirmAgendaHandover": "Da li ste sigurni da želite da prenesete odabranu agendu ({{selected}}) korisniku <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Da li ste sigurni da želite da prenesete <b>o<PERSON><PERSON><PERSON></b> agendu korisnika <b>{{user}}</b> korisniku <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Da li ste sigurni da želite da prenesete <b>kompletnu</b> agendu korisnika <b>{{user}}</b> korisniku <b>{{newUser}}</b>?", "refreshSessionTitle": "TAS sesija će biti prekinuta za {{minutes}} minuta.", "refreshSessionCaption": "<PERSON><PERSON><PERSON><PERSON> \"Nastavi sa radom\" da biste nastavili bez prekida.", "continueWorking": "Nastavite sa radom", "sessionExpiredCaption": "Kliknite \"Prijavite se ponovo\" da biste se vratili na ekran za prijavu.", "loginExpired": "<PERSON><PERSON><PERSON><PERSON> smo vas nakon dužeg perioda neaktivnosti.", "confirmArchiveCase": "Da li ste sigurni da želite da arhivirate izabrani slučaj?", "isLowerOrEqualThan": "<PERSON>ra biti manje od ili jednako", "confirmUnarchiveCase": "Da li ste sigurni da želite da poništite arhiviranje izabranog slučaja?", "addCaseRightNewUserTooltip": "<PERSON><PERSON> ne označite ovu opciju, novi korisnik će biti zamenjen u poslovnoj promenljivoj, ali neće imati pristup slučaju.", "canBeViced": "<PERSON>a sam zame<PERSON>n", "canVice": "Zamenjujem", "backgroundColor": "<PERSON><PERSON>", "defaultDashboardView": "Pregled zadane kontrolne table", "colorScheme": "<PERSON><PERSON> boja", "displaySelectionAsTags": "Prikaži izbor kao tagove", "displayAsPassword": "P<PERSON><PERSON><PERSON> kao lozinku", "sideBySide": "<PERSON><PERSON> pored drugog", "copyAssignmentFromTask": "<PERSON><PERSON><PERSON> do<PERSON> iz zadat<PERSON>", "toTask": "U zadatak", "copyTaskAssignmentWarning": "Dodela u zadatku nije prazna, da li želite da je prepišete?", "copyToOtherTasks": "Ko<PERSON>raj u druge zadatke", "noteScriptsNotApplied": "Napomena: sk<PERSON><PERSON> nisu <PERSON>", "generateRecHistory": "Prikaži u aktivnim zadacima i istoriji", "leaveFormerRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "includeCompetences": "Uključi kompetencije", "copyRoles": "<PERSON><PERSON><PERSON>", "userIsActive": "Korisnik je aktivan", "systemUser": "Sistemski korisnik", "copyRolesFromUser": "<PERSON><PERSON><PERSON> od koris<PERSON>a", "assignedRolesOverview": "<PERSON><PERSON> do<PERSON> ul<PERSON>", "copyRolesInfo": "Ako je dati korisnik deo kompetencija, ove kompetencije se neće odmah kopirati. Generisanje će se desiti:", "notificationOn": "Prič<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationOff": "Isključivanje", "onNotification": "Obaveštenje", "offNotification": "Obaveštenje", "page": "stranica", "fromTo": "Od - do", "fromto": "od - do", "isAnyOfValue": "Je svaka vrednost od", "isanyofvalue": "je svaka vrednost od", "alrNoteToggleVisibiltyFailed": "Skrivanje/otkrivanje beleške nije uspelo", "alrNoteHideOnEditFailed": "Skrivanje originalne beleške nije uspelo", "hiddenShe": "Skrivena", "showHiddenNotes": "Prikaži skrivene beleške", "alrNoteEdited": "Izmenjena verzija beleš<PERSON> je sačuvana", "notesEdit": "<PERSON>zmeni <PERSON>š<PERSON>", "displayName": "Prikazano ime", "clientDateFormat": "Format datuma", "defaultByLanguage": "Podrazumevano po jeziku", "restKeysOptionsNotUpToDate": "Zastareli izbor vrednosti - ponovo učitajte uslugu.", "invalidValue": "Nevažeća vrednost", "ended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportAllActive": "Izvezi sve aktivne", "alrScriptsLoadFailed": "Učitavanje skripti nije uspelo", "scriptsImport": "U<PERSON>z skripti", "doImport": "Uvoz", "alrImportingScripts": "U<PERSON>z skripti je u toku...", "alrScriptsImported": "Skripte su uvezene.", "alrScriptsImportFailed": "Uvoz skripti nije uspeo.", "removeAll": "Ukloni sve", "alrNoScriptsToImport": "<PERSON>ema sk<PERSON>ti za uvoz.", "activateAll": "Aktiviraj sve", "alrNoPermsToEditNoteInVice": "Nemate dozvolu da izmenite belešku kao zamenik.", "alrNoPermsToToggleNoteVisibilityInVice": "Nemate dozvolu da sakrijete/otkrijete belešku kao zamenik.", "plusMore": "vi<PERSON>e", "variableAlignment": "Poravnanje promenljive", "variableAlignmentHelp": "Uticaj na poravnanje vrednosti promenljive u okviru obrasca zadatka.", "variableAlignmentLeft": "Levo", "variableAlignmentRight": "Des<PERSON>", "tasksMineAndToPull": "Moji + Za povlačenje", "myDevice": "<PERSON><PERSON>", "deleteLogo": "Izbriši logo", "namingFilter": "<PERSON><PERSON><PERSON>", "exceptionsToRegularSchedule": "Izuzeci od redovnog rasporeda", "noExceptions": "<PERSON><PERSON>", "specificDates": "Specifični datumi", "dateFromTo": "Datum od - do", "weekdayCap": "<PERSON> u ne<PERSON>", "specificDayBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dan", "yearsBy": "godina", "timed": "<PERSON><PERSON><PERSON><PERSON>", "firstDayOfMonth": "Prvi dan u mesecu", "lastDayOfMonth": "Poslednji dan u mesecu", "firstDayOfYear": "Prvi dan u godini", "lastDayOfYear": "Poslednji dan u godini", "addDate": "<PERSON><PERSON><PERSON> datum", "newPlan": "Novi plan", "addAnother": "<PERSON><PERSON><PERSON> j<PERSON> j<PERSON>", "startTime": "Vreme početka", "endTime": "Vreme završetka", "inTimeFromTo": "u vremenu od {{from}} do {{to}}", "dayOfMonthBy": "Dan u mesecu", "cWorkDays": "radnih dana", "cWeeks": "<PERSON><PERSON><PERSON>", "cMonths": "meseci", "cYears": "godina", "everyWeek": "svake nedelje", "everyYear": "svake godine", "inMonth": "u mesecu", "everyDay": "svaki dan", "seqIdEdit": "Izmeni ID sekvence", "allowMultiselectSearchRight": "Dozvoli pretragu u zadatku", "doubleHeightForContent": "Dvostruka visina za sadržaj", "alrNoVariablesMappingToImport": "Nema mapiranja promenljivih za uvoz.", "alrVariablesMappingImportLoadFailed": "Učitavanje mapiranja promenljivih za uvoz nije uspelo.", "variablesMappingImport": "Uvoz mapiranja promenljivih", "useAllMappings": "<PERSON><PERSON><PERSON> s<PERSON>", "doExportVariablesMapping": "Izvezi mapiranje promenljivih", "alrImportingVariablesMapping": "Uvoz mapiranja promenljivih je u toku...", "alrVariablesMappingImported": "Mapiranje promenljivih je uvezeno.", "alrVariablesMappingImportFailed": "Uvoz mapiranja promenljivih nije uspeo.", "alrVariablesMappingImportedPartially": "Mapiranje promenljivih je delimično uvezeno. Neke promenljive nisu pronađene.", "alrEditorHintsLoadFailed": "Učitavanje saveta za uređivač nije uspelo.", "addTable": "<PERSON><PERSON><PERSON> ta<PERSON>", "confirmDynTablesDelete": "Da li zaista želite da obrišete izabrane dinamičke tabele?", "dynTablesDelete": "Obriši dinamičke tabele", "addRow": "<PERSON><PERSON>j red", "preview": "Pregled", "columnDelete": "<PERSON><PERSON><PERSON><PERSON> kolonu", "editRow": "Izmeni red", "addingNewColumn": "Dodavanje nove kolone", "addingNewRow": "Dodavanje novog reda", "columnsRename": "Preimenuj kolone", "rowCellValues": "Vrednosti ćelija u redu", "saveDynTableName": "Sačuvaj naziv dinamičke tabele", "saveDynTableNameQ": "Sačuvaj naziv dinamičke tabele?", "saveDynTableNameWarning": "Upoz<PERSON>n<PERSON>, uverite se da promena naziva tabele neće uticati na postojeće proračune u šablonima.", "rowMove": "Premesti red", "alrCsvParsingErr": "Greška pri raščlanjivanju CSV-a!", "addFirstTableColumn": "Dodaj prvu kolonu tabele", "my": "<PERSON><PERSON>", "license": "licenca", "licenses": "licence", "addLicense": "<PERSON><PERSON><PERSON> lice<PERSON>", "licenseResult": "Rezultat licence", "alrLicenceResultLoadingFailed": "Učitavanje rezultata licence nije uspelo.", "licensesAdminDescription": "Upravljanje licenc<PERSON>", "uploadByDragging": "Otpremi datoteku prevlačenjem.", "uploadByDraggingAnywhere": "Otpremite datoteku prevlačenjem bilo gde u prostoru.", "assignVariable": "<PERSON><PERSON><PERSON>", "confirmDeleteSectionName": "Da li ste sigurni da želite da izbrišete odeljak <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Upozorenje: o<PERSON><PERSON> će biti izbrisan za sve zadatke na koje utiče, uključujući promenljive.", "tasksAffected": "Pogođeni <PERSON>", "varSearchPlaceholder": "Npr. fakturisanje...", "enlarge": "<PERSON><PERSON><PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON><PERSON>", "shrink": "<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>", "doValidate": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Broj telefona", "textLength": "Dužina teksta", "when": "kada", "to2": "do", "that": "da", "dynCondBuilderBlockFunctionDescShow": "Prikazuje promenljivu ako je uslov ispunjen.", "dynCondBuilderBlockFunctionDescHide": "Skriva promenljivu ako je uslov ispunjen.", "dynCondBuilderBlockFunctionDescChange": "Menja vrednost promenljive ako je uslov ispunjen.", "dynCondBuilderBlockFunctionDescValidate": "Validira vrednost promenljive.", "addCondition": "<PERSON><PERSON><PERSON>", "operator": "operator", "equals": "j<PERSON><PERSON><PERSON>", "greaterthan": "veće od", "greaterorequal": "ve<PERSON>e ili jednako", "lessthan": "manje od", "lessorequal": "manje ili jednako", "demoCode": "Demo kod", "code": "Kod", "confirmDeleteConditions": "Da li zaista želite da izbrišete sve uslove (uključujući skriptu)?", "validationErrorMessage": "Poruka o grešci validacije", "alrScriptToStructuredBlockConversionFailed": "Konverzija skripte u strukturirani blok nije uspela.", "alrStructuredBlockToScriptConversionFailed": "Konverzija strukturiranog bloka u skriptu nije uspela.", "alrScriptToBuilderConversionFailed": "Konverzija skripte u graditelj nije uspela.", "alrBuilderToScriptConversionFailed": "Konverzija iz graditelja u skriptu nije uspela.", "dynCondBuilderBlockFunctionDescScript": "Blok skripte za dinamičke uslove.", "convertToStructuredBlock": "Pretvori u strukturirani blok", "convertToScript": "Pretvori u skriptu", "dynCondBuilderBlockWatchVarsLabel": "Prati pri promeni (watchVars)", "variables": "Promenljive", "copyToOthers": "<PERSON><PERSON><PERSON> u druge", "sectionName": "<PERSON>v sekcije", "newSectionName": "Naziv nove sekcije", "testIt": "<PERSON><PERSON><PERSON>", "addAdjacentSection": "<PERSON><PERSON><PERSON> su<PERSON>", "addAdjacentSectionBelow": "<PERSON><PERSON><PERSON> su<PERSON><PERSON> se<PERSON> ispod", "selectExistingSection": "Izaberi posto<PERSON>ć<PERSON> se<PERSON>", "renameSectionWarning": "Upozorenje: sekcija će biti preimenovana u svim zadacima šablona.", "warning2": "Upozorenje", "copyAssignmentToTask": "<PERSON><PERSON><PERSON> dodel<PERSON> u zadatak", "copyAlsoConditions": "<PERSON><PERSON><PERSON> i uslove", "copyAssignmentToTaskWarning": "Upozorenje: dodela i eventualno dinamički uslovi u izabranom zadatku biće zamenjeni.", "importFromOtherTask": "Uvezi iz drugog zadatka", "startFromScratch": "Počni od početka", "howToStartAssignments": "<PERSON><PERSON> da započnete dodelu promenljivih?", "selectTaskToImport": "Izaberite zadatak za uvoz", "confirm": "Potvrdi", "selectTaskToTest": "Izbor zadatka za testiranje", "toTestSaveChanges": "Za testiranje je potrebno sačuvati izmene.", "variableAssignmentTest": "Test dodele promenlji<PERSON>h", "viewAsMobile": "Prikaži kao na mobilnom", "viewAsPc": "Prikaži kao na računaru", "emptySpace": "<PERSON><PERSON><PERSON> prostor", "variableAssignments": "<PERSON><PERSON><PERSON> pro<PERSON>", "allowCompletionOnChangeOf": "Dozvoli završetak pri promeni", "dynCondBuilderBlockFunctionDescRead": "Menja režim promenljive na \"samo za čitanje\" ako je uslov ispunjen.", "dynCondBuilderBlockFunctionDescWrite": "Menja režim promenljive na \"za čitanje i pisanje\" ako je uslov ispunjen.", "dynCondBuilderBlockFunctionDescMust": "Menja režim promenljive na \"obavezno\" ako je uslov ispunjen.", "dynCondBuilderBlockFunctionDescSolve": "Omogućava završetak zadatka pri promeni date promenljive ako je uslov ispunjen.", "newsManagement": "Upravl<PERSON><PERSON> vestima", "newsManagementAdminDescription": "Upravljanje vestima u aplikaciji", "addNewsPost": "<PERSON><PERSON><PERSON> <PERSON>", "newPost": "Novi post", "news": "<PERSON><PERSON><PERSON>", "basicInfo": "Osnovne informacije", "publicationPlanning": "Planiranje publikacije", "displayToUsers": "Prikaz korisnicima", "displayLocation": "Mesto prikaza", "newsPostContent": "<PERSON><PERSON><PERSON><PERSON>i", "postTitle": "Na<PERSON>lov objave", "newsManagementPostDetailPhoneNumberTooltip": "Telefonski broj za prikaz u detaljima vesti", "newsManagementPostDetailEmailTooltip": "Email za prikaz u detaljima vesti", "customUrlLink": "Prilagođeni URL link", "newsManagementPostDetailCustomUrlLinkTooltip": "Prilagođeni URL link za prikaz u detaljima vesti", "stateAfterSaving": "Status nakon čuvanja", "newsPostStateActive": "Aktivno", "newsPostStateInactive": "Neaktivno", "newsPostStatePlanned": "Planirano", "endNewsPostOnSpecificDate": "Završite vest na određeni datum", "sendNewsPostViaEmail": "Pošaljite vest putem e-pošte", "priorityNewsPost": "Prioritetna vest", "newsManagementPostDetailPriorityNewsTooltip": "Na primer, za najavu održavanja ili promenu radnog toka", "newsPostEndDate": "Datum završetka vesti", "pickNewsPostDisplayToOrgUnits": "Kojim org. jedinicama prikazati vest?", "pickNewsPostDisplayToRoles": "<PERSON><PERSON>m ulogama prikazati vest?", "pickNewsPostDisplayToUsers": "<PERSON>jim korisnicima prikazati vest?", "pickNewsPostDisplayOnTemplate": "Na kojoj <PERSON>abloni prikazati vest?", "pickNewsPostDisplayOnHeaders": "Na kojim zaglavljima prikazati vest?", "pickNewsPostDisplayOnTasks": "Na kojim zadacima prikazati vest?", "pickNewsPostDisplaySubOptionsHelperText": "Prvo izaberite šablon na kojem želite da prikažete vest.", "newsTagsManagement": "Uprav<PERSON><PERSON><PERSON> vesti", "newsTagsManagementAdminDescription": "Upravljanje oznakama vesti u aplikaciji", "addTag": "<PERSON><PERSON><PERSON>", "tags": "Oznake", "publicationDate": "<PERSON>tum o<PERSON>", "contacts": "Kontakti", "avaibleUntil": "Dostupno do", "published": "Objavljeno", "newsSinceLastVisitAmount": "Ukupno {{amount}} vesti od poslednje posete", "noNews": "<PERSON><PERSON> vesti", "createNewTag": "Kreiraj novu oznaku", "tagName": "<PERSON>v oznake", "alrTagSaved": "Oznaka je saču<PERSON>.", "alrTagSaveFailed": "Čuvanje oznake nije uspelo.", "confirmDeleteTag": "Da li zaista želite da izbrišete oznaku \"{{tagName}}\"?", "alrPostSaved": "Vest je sa<PERSON>vana.", "alrPostSaveFailed": "Čuvanje vesti nije uspelo.", "alrLoadingTagsFailed": "Učitavanje oznaka nije uspelo.", "confirmDeletePost": "Da li zaista želite da izbrišete vest \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "Da li zaista želite da izbrišete izabrane vesti?", "post": "Vest", "alrPostLoadFailed": "Učitavanje vesti nije uspelo.", "alrTagDeleted": "Oznaka je izbrisana.", "alrTagDeleteFailed": "B<PERSON><PERSON>je oznake nije uspelo.", "alrPostDeleted": "Vest je izbrisana.", "alrPostDeleteFailed": "Brisanje vesti nije uspelo.", "alrPostsDeleted": "Izabrane vesti su izbrisane.", "alrPostsDeleteFailed": "Brisanje izabranih vesti nije uspelo.", "alrTempTasksLoadFailed": "Učitavanje zadataka šablona nije uspelo.", "rolesRestriction": "Ograničenja uloga", "usersRestriction": "Ograničenja korisnika", "orgUnitsRestriction": "Ograničenja org. jedinica", "alrPriorityNewsLoadFailed": "Učitavanje prioritetnih vesti nije uspelo.", "moreInfo": "Više informacija", "tas5Info": "TAS 5.0 je ovde ...", "totalNewsAmount": "U<PERSON><PERSON><PERSON> {{amount}} vesti", "alrNewsContainerPostsLoadFailed": "Učitavanje postova za kontejner vesti nije uspelo.", "alrTaskNewsLoadFailed": "Učitavanje vesti za zadatak nije uspelo.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "Datum objavljivanja mora biti pre datuma završetka vesti.", "alrNotificationsNewsLoadFailed": "Učitavanje vesti za obaveštenja nije uspelo.", "moreNews": "<PERSON><PERSON>š<PERSON> vesti", "newsManagementPostDetailConfirmSavingWillSendMail": "Čuvanje objave će izazvati slanje e-pošte svim korisnicima kojima je objava namenjena. Da li zaista želite da sačuvate objavu?", "mailNewsNotification": "E-mail obaveštenje o vestima", "mailNewsNotificationInfo": "Korisnik kontinuirano prima vesti koje su mu namenjene.", "alrRefreshingConfig": "Osvežavanje konfiguracije...", "alrConfigRefreshed": "Konfiguracija je uspešno osvežena.", "alrConfigRefreshFailed": "Osvežavanje konfiguracije nije uspelo.", "ttRefreshConfig": {"heading": "Vrati konfiguraciju iz svih izvora", "body": ""}, "getMobileAppTextQr": "Preuzmite mobilnu aplikaciju iz prodavnice aplikacija ili skenirajte KR kod", "dateStart": "<PERSON>tum <PERSON>", "dateEnd": "<PERSON><PERSON>", "tas_forms_generated": "Broj automatski generisanih obrazaca"}