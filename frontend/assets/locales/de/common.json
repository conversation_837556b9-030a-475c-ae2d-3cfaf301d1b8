{"1st": "1.", "2nd": "2.", "3rd": "3.", "4th": "4.", "AddToAllTasks": "<PERSON>ben hinzufügen", "OfVariable": "Aus der Variable", "RemoveFromAllTasks": "Aus allen Aufgaben entfernen", "TaskOwnerWhichInVar": "<PERSON> den Löser, der in der Variable genannt ist", "action": "Aktion", "active": "Aktiv", "activeShe": "Aktiv", "activePl": "Aktiv", "activity": "Aktivität", "activityType": "Typ der Aktivität", "actualEnd": "Tatsächliches Ende", "actualSolver": "<PERSON><PERSON><PERSON>", "actualStart": "Tatsächlicher Beginn", "actualTsks": "Aktuelle Aufgaben", "actualize": "aktualisieren", "add": "Hinzufügen", "addAttribute": "Attribut hinzufügen", "addOrgUnit": "Org.-Einheit hinzufügen", "addPlan": "Plan hinzufügen", "addPrintTemplate": "Druckvorlage hinzufügen", "addRole": "<PERSON><PERSON> hinzufügen", "addRule": "Regel hinzufügen", "addTemp": "<PERSON><PERSON><PERSON>", "addTsk": "Aufgabe hinzufügen", "addUser": "Benutzer hinzufügen", "addUserSomething": "Benutzer der Variablen {{variable}} zuordnen:", "addVariable": "Variable hinzufügen", "after": "Nach", "afterTermTasks": "Meine Aufgaben nach Ablauf der Frist", "all": "Alle ", "allFiles": "Alle Dateien", "allMustBeMet": "Alle müssen erfüllt sein", "allMyTasks": "Alle meine Aufgaben", "allSubOfPlanGuar": "Alle Untergeordneten des Plan-Garanten", "allTasksWithNoTerm": "Alle Aufgaben ohne Termin", "allTasksWithTerm": "Alle Aufgaben mit Termin", "allTemps": "<PERSON><PERSON>", "allowMultiple": "Auswahl von mehreren Posten genehmigen", "allowSelectAll": "Auswahl aller Elemente aktivieren", "allsupOfPlanGuar": "Alle Vorgesetzten des Plan-Garanten", "alrBlockingAction": "Es verläuft gerade eine Aktion, die abzuschließen ist. Bitte warten…", "alrActionNameNotDefined": "Die Aktion \"{{actionName}}\" ist nicht definiert.", "alrActionNotDefined": "Die Aktion wurde nicht definiert.", "alrApiUrlMissing": "Es fehlt die Datenquelle zur Tabelle.", "alrAssigningTsk": "Zuordnen der Aufgabe...", "alrAssignmentFailed": "Die Zuordnung konnte nicht gespeichert werden.", "alrAtrFailed": "Das Attribut konnte nicht gelöscht werden.", "alrAttachDeleteFailed": "Das Dokument konnte nicht gelöscht werden.", "alrAttachDeleted": "Das Dokument wurde gelöscht!", "alrAttachDeleting": "Löschen des Dokumentes…", "alrAttachDownloadFailed": "Das Dokument konnte nicht heruntergeladen werden.", "alrAttachDownloaded": "Das Dokument wurde heruntergeladen!", "alrAttachDownloading": "Herunterladen des Dokumentes…", "alrAttachMetaFailed": "Die Metadaten des Dokumentes konnten nicht gespeichert werden.", "alrAttachOrNotesCountFailed": "Die Anzahl der Dokumente oder der Anmerkungen konnte nicht festgestellt werden.", "alrAttachSaveFailed": "Das Dokument konnte nicht hochgeladen werden.", "alrAttachSaved": "Das Dokument wurde hinzugefügt.", "alrAttachTooBig": "Das Dokument ist zu groß! Es überschritt die Größe von {{maxUploadSize}} MB.", "alrAttrDataFailed": "Die Attributdaten konnten nicht eingelesen werden.", "alrAttrFailed": "Die Attributdaten konnten nicht eingelesen werden.", "alrAttrSaveFailed": "<PERSON>im <PERSON>ichern des Attributs ist ein Fehler aufgetreten!", "alrAttrsLoadFailed": "Die Attribute konnten nicht eingelesen werden.", "alrAttachRestored": "Das Dokument wurde wiederhergestellt.", "alrAttachRestoreFailed": "Die Wiederherstellung des Dokumentes ist nicht gelungen.", "alrAttachRestoring": "Wiederherstellung des Dokumentes...", "alrAuthMethodsFailed": "Die Authentisierungsmethoden konnten nicht eingelesen werden.", "alrBadLogin": "Sie haben einen falschen Benutzernamen oder ein falsches Passwort eingegeben.", "alrBlockedPopups": "Wahrscheinlich werden Ihre Pop-up-Fenster blockiert.", "alrCaseDataLoadFailed": "Die Falldaten konnten nicht eingelesen werden.", "alrCaseDeleteFailed": "Der Fall konnte nicht gelöscht werden.", "alrCaseDeleted": "Der Fall wurde gelöscht!", "alrCaseDeleting": "Löschen des Falles…", "alrCaseNameLoadFailed": "Der Name des Falles konnte nicht eingelesen werden.", "alrCaseNoRights": "Sie haben keine Berechtigung zum Anzeigen des Falles Nr. {{id}}.", "alrCaseNotFound": "Der Fall wurde nicht gefunden - er wurde wahrscheinlich gelöscht.", "alrCaseOverviewFailed": "Die Falldaten zum Lesen (CASE OVERVIEW) konnten nicht eingelesen werden.", "alrCaseSuspended": "Der Fall wurde in den Schlaf-Modus versetzt!", "alrCaseVarsLoadFailed": "Die Prozessvariablen konnten nicht eingelesen werden.", "alrCaseWakeUpFailed": "Der Fall konnte nicht aufgewacht werden.", "alrCaseWakedUp": "Der Fall wurde aufgewacht!", "alrCaseWakingUp": "Aufwecken des Falles...", "alrColsWidthsSettingsFailed": "Die Einstellung der Spaltenbreite konnte nicht gespeichert werden.", "alrConnToServerFailed": "Die Verbindung zum Server ist fehlgeschlagen.", "alrConnectionDataLoadFailed": "Die Daten der Verbindung konnten nicht eingelesen werden.", "alrConnectionDeleteFailed": "Die Verbindung konnte nicht gelöscht werden.", "alrConnectionDeleted": "Die Verbindung wurde gelöscht!", "alrConnectionSaveFailed": "Die Verbindung konnte nicht gespeichert werden!", "alrContainerNotFound": "Der Container wurde nicht gefunden.", "alrCsvDownloaded": "Die SCV-<PERSON><PERSON> wurde heruntergel<PERSON>n!", "alrCvNotFound": "Die Übersicht wurde nicht gefunden.", "alrDashboardSettingsFailed": "Die Dashboard-Einstellung konnte nicht gespeichert werden.", "alrDefaultDashboardLoadFailed": "Das Default-Dashboard konnte nicht eingelesen werden.", "alrDefaultDashboardSaved": "Das Default-Dashboard wurde gespeichert!", "alrDeleteFailed": "<PERSON><PERSON> ist ein Fehler aufgetreten.", "alrDeleted": "Gelöscht!", "alrDeleting": "<PERSON><PERSON><PERSON>…", "alrDiagramDataLoadFailed": "Die Daten zum Erstellen des Diagrammes konnten nicht eingelesen werden.", "alrDiagramEditToSave": "Das Diagramm kann nicht gespeichert und auf eine Schablone umgestellt werden - es enthält mehr als einen Prozess! Bearbeiten Sie bitte das Diagramm so, dass es einen einzigen Prozess enthält, oder importieren Sie eine andere .bpmn-Datei.", "alrDiagramInitFailed": "Das Diagramm konnte nicht initialisiert werden.", "alrDiagramMissingTaskName": "Ergänzen Sie bitte die Namen bei allen Aufgaben.", "alrDiagramErrors": "Das Diagramm enthält Fehler. Bitte behebe sie und versuche erneut zu speichern.", "alrDiagramNotValid": "XML ist nach der offiziellen Spezifikation BPMN 2.0 nicht valid!", "alrDiagramProcessCount": "Es wird nicht möglich sein, das Diagramm zu speichern und auf eine Schablone umzustellen - es enthält mehr als einen Prozess!", "alrDiagramSaveFailed": "<PERSON><PERSON>ichern der Schablone ist ein Fehler aufgetreten!", "alrDiagramTsksDeleteFailed": "Beim Löschen der Aufgaben ist ein Fehler aufgetreten!", "alrDiagramUnchanged": "<PERSON> Schablone blieb unverändert.", "alrDmsColsLoadFailed": "Die Spalten für DMS konnten nicht eingelesen werden.", "alrDocsColumnsIdsFailed": "ID für die Tabellenspalten konnten nicht eingelesen werden.", "alrDocumentAdding": "Hinzufügen des Dokumentes...", "alrDynListsDataLoadFailed": "Die Daten der dynamischen Blätter konnten nicht eingelesen werden.", "alrDynTableColsDataFailed": "Die Daten zu den Spalten der dynamischen Tabelle konnten nicht eingelesen werden.", "alrDynTableDataLoadFailed": "Die Daten der dynamischen Tabelle konnten nicht eingelesen werden. ", "alrDynTableNotFound": "Die dynamische Tabelle wurde nicht gefunden.", "alrDynTablesDataLoadFailed": "Die Daten der dynamischen Tabellen konnten nicht eingelesen werden.", "alrCalcScriptsDataLoadFailed": "Die globalen Berechnungsskripten konnten nicht eingelesen werden.", "alrEditValues": "Korrigieren Sie bitte die falsch ausgefüllten Werte.", "alrEventSaveFailed": "Das Ereignis konnte nicht gespeichert werden.", "alrEventSaved": "Das Ereignis wurde ges<PERSON>ichert!", "alrEventSaving": "Speicherung des Ereignisses…", "alrEventTriggered": "Das Ereignis wurde ausgelöst!", "alrExcelDownloaded": "XLSX-<PERSON><PERSON> wurde heruntergeladen!", "alrExportCompleted": "Der Export ist abgeschlossen.", "alrExportFailed": "Der Export ist fehlgeschlagen.", "alrExportPreparing": "Vorbereitung des Exportes…", "alrFailed": "Die Aktion ist fehlgeschlagen.", "alrFailedCalendarTask": "Die Aufgaben konnten in den Kalender nicht hochgeladen werden.", "alrFailedCreatePrint": "Die Auflistung zum Ausdrucken konnte nicht erstellt werden.", "alrFailedDLTotalCount": "Bei dem dynamischen Blatt {{label}} wurde die Gesamtzahl (total_count) nicht definiert, daher wurden alle Eintragungen eingelesen.", "alrFailedData": "Die Daten konnten nicht eingelesen werden.", "alrFailedEventStart": "Das Ereignis konnte nicht gestartet werden.", "alrFailedEventVariables": "Die Variablen des ausgewählten Ereignisses konnten nicht eingelesen werden.", "alrFailedEvents": "Die Ereignisse konnten nicht eingelesen werden.", "alrFailedFoldersData": "Die Dateidaten konnten nicht eingelesen werden.", "alrFailedFormData": "Die Formulardaten konnten nicht eingelesen werden. ", "alrFailedInitiatorName": "Der Name des Initiators konnte nicht eingelesen werden.", "alrFailedLabelData": "Die Daten der Label Komponente konnten nicht eingelesen werden.", "alrFailedLoad": "Die Druckdaten konnten nicht eingelesen werden.", "alrFailedLogicalType": "Der logische Typ konnte nicht eingelesen werden. ", "alrFailedMultiBoxData": "Die MultiBox Daten konnten nicht eingelesen werden. ", "alrFailedNewCase": "<PERSON><PERSON> des neuen Falles ist ein Fehler aufgetreten!", "alrFailedNewFolder": "Der Verzeichnisname konnte nicht geändert werden.", "alrFailedNoticeData": "Die Hinweisdaten konnten nicht eingelesen werden.", "alrFailedOrgUnitUser": "Die Organisationseinheiten des Benutzers konnten nicht eingelesen werden. ", "alrFailedOverviewData": "Die Übersicht konnte nicht eingelesen werden.", "alrFailedPlanData": "Die Plandaten konnten nicht eingelesen werden.", "alrFailedPostData": "Die Daten konnten nicht gesendet werden.", "alrFailedPrintData": "Die Druckdaten können nicht eingelesen werden.", "alrFailedRevisionInfo": "Die Informationen über die neue Revision konnten nicht eingelesen werden. ", "alrFailedSearchBoxData": "Die SearchBox Daten konnten nicht eingelesen werden.", "alrFailedSelectBoxData": "Die Daten der SelectBox Komponente konnten nicht eingelesen werden.", "alrFailedSuggestBoxData": "Die Daten der Autovervollständigung konnten nicht eingelesen werden.", "alrFailedTasColors": "Die TAS-Farben konnten nicht eingelesen werden!", "alrFailedTaskHandOver": "Die Aufgabe konnte nicht übergeben werden.", "alrFailedTemplateProcesses": "Die Fallschablonen konnten nicht eingelesen werden.", "alrFailedVarData": "Die Daten der Variablen konnten nicht eingelesen werden.", "alrFileAdded": "Die Datei wurde hinzugefügt!", "alrFileDeleteFailed": "Die Datei konnte nicht gelöscht werden", "alrFileDonwload": "<PERSON><PERSON><PERSON><PERSON><PERSON> der Datei…", "alrFileDownloaded": "Die Datei wurde heruntergeladen!", "alrFileInfoFailed": "Die Informationen über die Datei konnten nicht eingelesen werden.", "alrFileMetaSaveFailed": "Die Metadaten der Datei konnten nicht gespeichert werden.", "alrFileSavedLikeAttach": "Die Datei wurde als Dokument gespeichert. ", "alrFileUploadFailed": "Die Datei konnte nicht hoch<PERSON>aden werden", "alrFillAllRequired": "Um die Aufgabe zu erfüllen, sind alle obligatorischen Daten auszufüllen!", "alrFillData": "Um die Aufgabe zu speichern, sind alle Daten korrekt auszufüllen!", "alrFillDataInRightFormat": "Füllen Sie die Daten bitte im richtigen Format aus.", "alrFillDataToCompleteTsk": "Um die Aufgabe zu erfüllen, sind alle Daten korrekt auszufüllen!", "alrFillNameAndPass": "Füllen Sie bitte den Namen und das Passwort aus.", "alrFillNote": "Füllen Sie bitte den Text der Anmerkung aus.", "alrFillRequiredItems": "Füllen Sie bitte die erforderlichen Positionen aus.", "alrFolderDataFailed": "Die Daten der Verzeichnisse konnten nicht eingelesen werden.", "alrFolderDataLoadFailed": "Die Daten des Verzeichnisses konnten nicht eingelesen werden.", "alrFolderFailed": "Die Informationen über das Verzeichnis konnten nicht eingelesen werden.", "alrFolderSaveFailed": "Das Verzeichnis konnte nicht gespeichert werden!", "alrFoldersLoadFailed": "Die Verzeichnisse konnten nicht eingelesen werden.", "alrHelpSettingsSaveFailed": "Die Einstellung der Hilfe konnte nicht gespeichert werden.", "alrHistoricalTskInfoFailed": "Die Informationen der historischen Aufgabe konnten nicht eingelesen werden. ", "alrHistoricalVarsSaveFailed": "Die historischen Variablen konnten nicht gespeichert werden.", "alrHistoricalVarsSaved": "Die historischen Variablen der Aufgabe wurden gespeichert.", "alrInvLogginHash": "Ungültige Anmeldung.", "alrJsonFailed": "JSON ist nicht valid!", "alrLackOfPermsToEdit": "Sie haben kein Bearbeitungsrecht! Der Eigentümer ist", "alrLackOfPermsToSleepCase": "Sie haben keine ausreichende Berechtigung für das Versetzen des Falles in den Ruhezustand.", "alrLackOfPermsToWakeUpCase": "Sie haben keine ausreichende Berechtigung für das Aufwecken des Falles. ", "alrLastHistoricalTskIdFailed": "ID der letzten historischen Aufgabe konnte nicht eingelesen werden.", "alrLoadAttachmentsFailed": "Die Dokumente konnten nicht eingelesen werden.", "alrLogOutFailed": "Das Abmelden ist fehlgeschlagen. ", "alrLoginExpired": "Die Anmeldefrist ist abgeleufen, melden Si<PERSON> sich bitte erneut an.", "alrMappingFailed": "Das Mapping konnte nicht gespeichert werden.", "alrMappingTsksFailed": "Das Mapping der Aufgaben konnte nicht eingelesen werden.", "alrNewCaseBased": "Neuer Fall angelegt!", "alrNewFolder": "Es wurde ein neues Verzeichnis angelegt!", "alrNewFolderFailed": "Das neue Verzeichnis konnte nicht angelegt werden.", "alrNextTskOpened": "Es wurde die nächste Aufgabe geöffnet", "alrNoDataToPrint": "<PERSON>s wurden keine Druckdaten gefunden.", "alrNoteAdded": "Die Anmerkung wurde hinzugefügt!", "alrNoteSaveFailed": "Die Anmerkung konnte nicht gespeichert werden.", "alrNoteSaving": "Hinzufügen der Anmerkung…", "alrNotesLoadFailed": "Die Anmerkungen zum Fall konnten nicht eingelesen werden.", "alrOrgUnitDataFailed": "Die Daten zur Organisationseinheit konnten nicht eingelesen werden.", "alrOrgUnitDeleteFailed": "Die Organisationseinheit konnte nicht gelöscht werden.", "alrOrgUnitDeleted": "Die Organisationseinheit wurde gelöscht!", "alrOrgUnitDeleting": "Löschen der Organisationseinheit…", "alrOrgUnitSaveFailed": "Die Organisationseinheit konnte nicht gespeichert werden.", "alrOrgUnitSaved": "Die Organisationseinheit wurde gespeichert!", "alrOrgUnitSaving": "Speicherung der Organisationseinheit...", "alrOverviewDataLoadFailed": "Die Daten der Übersicht konnten nicht eingelesen werden.", "alrOverviewSaveFailed": "Die Übersicht konnte nicht gespeichert werden!", "alrOverviewSaveSameNameFailed": "Der Name der Übersicht wird bereits von Ihnen oder von einem anderen Benutzer verwendet, wählen Si<PERSON> bitte einen anderen Namen der Übersicht.", "alrGraphSaveSameNameFailed": "Der Name der Graph wird bereits von Ihnen oder von einem anderen Benutzer verwendet, wählen Si<PERSON> bitte einen anderen Namen der Graph.", "alrReportSaveSameNameFailed": "Der Name der Report wird bereits von I<PERSON>en oder von einem anderen <PERSON>utzer verwendet, wählen Sie bitte einen anderen Namen der Report.", "alrOverviewsLoadFailed": "Die Übersichten konnten nicht eingelesen werden.", "alrPassSaveFailed": "Das Passwort konnte nicht gespeichert werden.", "alrPassSaved": "Das Passwort wurde gespeichert!", "alrPlanReqItems": "Um den Plan zu speichern, füllen Si<PERSON> alle erforderlichen Positionen aus. ", "alrPlanSaveFailed": "Der Plan konnte nicht gespeichert werden.", "alrPlanSaved": "Der Plan wurde gespeichert!", "alrPreparingPrint": "Vorbereitung der Auflistung zum Ausdrucken…", "alrPrintDeleteFailed": "Der Druck konnte nicht gelöscht werden.", "alrPrintDeleted": "Der Druck wurde gelöscht!", "alrPrintSaveFailed": "Der Druck konnte nicht gespeichert werden", "alrPrintSaved": "Der Druck wurde ges<PERSON>ichert!", "alrReadOnlyCaseDataFailed": "Die Falldaten zum Lesen konnten nicht eingelesen werden.", "alrRecalcFailed": "Bei der Umrechnung ist ein Fehler aufgetreten!", "alrRecalculating": "Die Umrechnung läuft...", "alrRestorTemplFailed": "Wiederherstellen der Schablone fehlgeschlagen.", "alrRoleDataLoadFailed": "Die Daten über die Rolle konnten nicht eingelesen werden.", "alrRoleDeleteFailed": "Die Rolle konnte nicht gelöscht werden.", "alrRoleDeleted": "Die Rolle wurde nicht gelöscht!", "alrRoleDeleting": "Löschen der Rolle…", "alrRoleSaveFailed": "Die Rolle konnte nicht gespeichert werden.", "alrRoleSaved": "Die Rolle wurde nicht gespeichert!", "alrRoleSaving": "Speicherung der Rolle…", "alrRunEvent": "Auslösen des Ereignisses…", "alrSaveFailed": "Die Speicherung ist fehlgeschlagen.", "alrSaved": "Gespeichert!", "alrSaving": "Speichern…", "alrSavingBeforeRecalcFailed": "<PERSON>im <PERSON>rn vor der Umrechnung ist ein Fehler aufgetreten!", "alrSavingFailed": "<PERSON><PERSON> ist ein Fehler aufgetreten!", "alrSavingPlan": "Speicherung des Plans..", "alrSavingPrint": "Speicherung des Druckes…", "alrSearchNoResults": "Den Suchparametern entsprechen keine Ergebnisse.", "alrSearchRequestFailed": "Beim Senden der Anforderung ist ein Fehler aufgetreten!", "alrSearching": "<PERSON>en…", "alrSettFailed": "Die Einstellung konnte nicht gespeichert werden.", "alrSettSaved": "Die Einstellung wurde gespeichert.", "alrSettingsLoadFailed": "Die Daten der Einstellung konnten nicht eingelesen werden.", "alrSleepCaseFailed": "Der Fall konnte nicht in den Schlaf-Modus versetzt werden.", "alrStoreNameNotDefined": "Store \"{{storeName}}\" ist nicht definiert.", "alrStoreNotDefined": "Store wurde nicht definiert.", "alrSubActionNotDefined": "SubAction und Suffix müssen definiert werden.", "alrSubStoreNotDefined": "SubStore und Suffix müssen definiert werden.", "alrSuggestBoxDataNotContains": "Die Daten der Autovervollständigung \"{{label}}\" enthalten keine \"{{prop}}\"!", "alrSuspendingCase": "Versetzen des Falles in den Schlaf-Modus…", "alrTableDataFailed": "Die Daten für die Tabelle konnten nicht eingelesen werden.", "alrTasNewVersion": "<PERSON>s wurde eine neue Version der App gefunden.", "alrRefresh": "Für die korrekte Funktion ist die Seite im Browser zu {{- spanRefresh}}.", "alrTasVersionLoadFailed": "Die Version der App konnte nicht eingelesen werden!", "alrTaskHandOver": "Weiterleiten der Aufgabe…", "alrTaskHandedOver": "Die Aufgabe wurde an den Benutzer weitergeleitet:", "alrTaskNoRights": "Sie haben keine Berechtigung zur Anzeige der Aufgabe Nr. {{id}}.", "alrTaskNotFound": "Die Aufgabe wurde nicht gefunden.", "alrTempDataLoadFailed": "Die Daten für die Schablone konnten nicht eigelesen werden.", "alrTempHeadersLoadFailed": "Die Kopfzeilen der Schablone konnten nicht eingelesen werden.", "alrTempDeleteFailed": "Die Schablone konnte nicht gelöscht werden.", "alrTempDeleted": "Die Schablone wurde gelöscht!", "alrTempFoldersLoadFailed": "Die Verzeichnisse der Schablone konnten nicht eingelesen werden.", "alrTempNameLoadFailed": "Der Name der Schablone konnte nicht eingelesen werden.", "alrTempRestored": "Die Schablone wurde mit dem Status Entwickelt wiederhergestellt.", "alrTempSaveFailed": "Die Schablone konnte nicht gespeichert werden.", "alrTempsLoadFailed": "Die Schablonen konnten nicht eingelesen werden.", "alrTempVarDataLoadFailed": "Die Daten zur Schablone der Variablen konnten nicht eingelesen werden.", "alrTempVarSaveFailed": "Die Variable konnte nicht gespeichert werden.", "alrTempVarsLoadFailed": "Die Variablen der Schablone konnten nicht eingelesen werden.", "alrTotalCountFailed": "Die Gesamtzahl der Eintragungen in der Tabelle konnte nicht festgestellt werden.", "alrTreeDataFailed": "Die Baum-Daten konnten nicht eingelesen werden.", "alrTskAddFailed": "Die Aufgabe konnte nicht hinzugefügt werden.", "alrTskAdded": "Die Aufgabe wurde hinzugefügt!", "alrTskAdding": "Hinzufügen der Aufgabe…", "alrTskAssignFailed": "Die Aufgabe konnte nicht hinzugefügt werden.", "alrTskAssigned": "Die Aufgabe wurde hinzugefügt.", "alrTskCompleteFailed": "Bei der Erfüllung der Aufgabe ist ein Fehler aufgetreten.", "alrTskDataLoadFailed": "Die Daten der Aufgabe konnten nicht eingelesen werden.", "alrTskDeleteFailed": "Die Aufgabe konnte nicht gelöscht werden.", "alrTskDeleted": "Die Aufgabe wurde gelöscht!", "alrTskNameLoadFailed": "Der Name der Aufgabe konnte nicht eingelesen werden.", "alrTskRecalculated": "Die Aufgabe wurde umgerechnet!", "alrTskSaveFailed": "<PERSON>im Speichern der Aufgabe ist ein Fehler aufgetreten.", "alrTskSaved": "Die Aufgabe wurde gespeichert!", "alrTskSavedAndCompleted": "Die Aufgabe wurde gespeichert und erfüllt!", "alrTskScheduleFailed": "Die Aufgabe konnte zeitlich nicht festgelegt werden.", "alrTskScheduled": "Die Aufgabe wurde zeitlich festgelegt.", "alrTskTakeFailed": "Die Aufgabe konnte nicht übernommen werden.", "alrTskTaken": "Die Aufgabe wurde übernommen.", "alrTskTakign": "Übernehmen der Aufgabe…", "alrTsksMappingFailed": "Die Mapping-Aufgaben konnten nicht eingelesen werden.", "alrUserDataLoadFailed": "Die Benutzerdaten konnten nicht eingelesen werden.", "alrUserDeleteFailed": "Der Benutzer konnte nicht gelöscht werden.", "alrUserDeleted": "Der Benutzer wurde gelöscht!", "alrUserDeleting": "Löschen des Benutzers...", "alrUserIsNotActive": "Der Benutzer ist nicht aktiv", "alrUserNotLoaded": "Der Benutzer konnte nicht eingelesen werden.", "alrUserParamsLoadFailed": "Die Benutzerparameter konnten nicht eingelesen werden.", "alrUserSaveFailed": "Der Benutzer konnte nicht gespeichert werden.", "alrUserSaved": "Der Benutzer wurde gespeichert!", "alrUserSaving": "Speicherung des Benutzers…", "alrUserStatusChangeFailed": "Der Benutzerstatus konnte nicht geändert werden.", "alrUserStatusChanged": "Der Benutzerstatus wurde geändert!", "alrUserStatusChanging": "Änderung des Benutzerstatus..", "alrVarDeleteFailed": "Die Variable konnte nicht gelöscht werden.", "alrVarDeleted": "Die Variable wurde gelöscht!", "alrVarSaveFailed": "Die Variable konnte nicht gespeichert werden.", "alrVarSaved": "Die Variable wurde gespeichert.", "alrVarSaving": "Speicherung der Variable…", "alrVarsForModalFilterFailed": "Die Variablen für den modalen Filter konnten nicht eingelesen werden.", "alrVarsLoadFailed": "Die Variablen konnten nicht eingelesen werden.", "alrVarsOrderLoadFailed": "Die Reihenfolge der Variablen konnte nicht eingelesen werden.", "alrVarsOrderSaveFailed": "Die Reihenfolge der Variablen konnte nicht gespeichert werden.", "alrViceDeleted": "Die Vertretung wurde gelöscht.", "alrViceFailed": "Die Vertretung ist fehlgeschlagen.", "alrViceNotFound": "Die Vertretung wurde nicht gefunden.", "alrViceSaveFailed": "Die Vertretung konnte nicht gespeichert werden.", "alrViceSaved": "Die Vertretung wurde gespeichert!", "alrViceSaving": "Speicherung der Vertretung…", "always": "Immer", "annually": "<PERSON><PERSON><PERSON><PERSON>", "assHierarchy": "Verhältnis zur Referenzperson", "assHierarchyAncestors": "Alle Vorgesetzten der Referenzperson", "assHierarchyChildren": "Direkte Untergeordnete der Referenzperson", "assHierarchyDescendants": "Alle Untergeordneten der Referenzperson", "assHierarchyGuarantor": "<PERSON><PERSON> die Referenzperson", "assHierarchyParent": "Direkter Vorgesetzte der Referenzperson", "assHierarchySiblings": "Kollegen der Referenzperson", "assMethodAutomatic": "Wird durch den Computer ausgewählt", "assMethodLastSolver": "<PERSON><PERSON>r den letzten Löser der Aufgabe", "assMethodLastSolverChoice": "Wird vom letzten Löser der Aufgabe ausgewählt", "assMethodLeast": "Hat die wenigsten Aufgaben", "assMethodPull": "<PERSON><PERSON><PERSON>; es wird angeboten", "assMethodSelect": "Wird vom Supervisor der Aufgabe ausgewählt", "assMethodVariable": "Ist in der Variablen aufgeführt", "assessmentOfConds": "Auswertung der Bedingungen", "assign": "<PERSON><PERSON><PERSON><PERSON>", "assignAttrs": "Zuordnung der Attribute", "assignAttrsLogType": "Zuordnung der Attribute dem logischen Typ des Dokumentes", "assigned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assigningRoles": "Zuordnung der Rollen", "assignments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachments": "Dokumente", "attachmentsList": "Liste der Dokumente", "attribute": "Attribut", "attributeNew": "Attribut - Neu", "availableVars": "Verfügbare Variablen", "body": "<PERSON><PERSON><PERSON><PERSON>", "borders": "<PERSON><PERSON><PERSON>", "byFolders": "<PERSON><PERSON> den Verzeichnissen", "byOrganization": "Nach der Organisation", "byRole": "<PERSON><PERSON> den Rollen", "calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculations": "Berechnungen", "calendar": "<PERSON><PERSON><PERSON>", "carriedIfNoOther": "Wird nur dann durchgeführt, wenn keine andere", "case": "Fall", "caseCreation": "<PERSON><PERSON><PERSON><PERSON> Fall<PERSON>", "caseGraph": "Instanzdiagramm", "caseNoEvents": "Der Fall enthält keine Ereignisse.", "caseNum": "Fall Nr.", "caseOwner": "Falleigentümer", "caseStatus": "<PERSON><PERSON><PERSON>", "caseVar": "Fall", "cases": "<PERSON><PERSON><PERSON>", "casesWithProblem": "<PERSON>ne Fälle mit Problem", "category": "<PERSON><PERSON><PERSON>", "changeTaskSolver": "Änderung des Aufgabenlösers", "changedBy": "G<PERSON><PERSON>ndert", "changedWhen": "<PERSON><PERSON><PERSON><PERSON><PERSON> (wann)", "checkbox": "Checkbox", "checkboxList": "CheckboxList", "choosePrint": "Druckschablone", "chooseUserToAssignTsk": "<PERSON>utz<PERSON> auswählen, dem Sie die Aufgabe zuweisen", "choosenAttrs": "Ausgewählte Attribute", "city": "Stadt", "class": "Klass<PERSON>", "clickToClose": "Durch Klicken schließen", "clickToRefresh": "Durch Klicken die Seite im Browser aktualisieren ", "clickToRepeat": "Durch Klicken die Aktion wiederholen", "clientLanguage": "Sprache des Kunden", "cloneRow": "<PERSON><PERSON><PERSON>", "close": "Schließen", "closeAll": "Alles sch<PERSON>ßen", "coWorkersOfPlanGuar": "Kollegen des Plan-Garanten", "color": "Farbe", "colors": "<PERSON><PERSON>", "column": "<PERSON>lt<PERSON>", "columnName": "Spaltenname", "comment": "Kommentar", "complete": "Erfüllen", "completion": "Beendigung", "componentDescription": "Beschreibung der Komponente", "condition": "Bedingung", "conditions": "Bedingungen", "confirmAttachDeletion": "Möchten Sie das Dokument wirklich löschen?", "confirmDeleteDialog": "<PERSON><PERSON>lich löschen {{variable}}?", "confirmDialogEventSave": "<PERSON><PERSON>r das Umschalten muss das Ereignis gespeichert werden. Möchten Si<PERSON> es speichern?", "confirmResetDashboard": "Möchten Sie das Dashboard wirklich zurücksetzen?", "confirmSaveChanges": "Durchgeführte Änderungen speichern?", "confirmSaveDiagramChanges": "Durchgeführte Änderungen im ganzen Diagramm speichern?", "confirmSaveTaskChanges": "Durchgeführte Änderungen in der Aufgabe speichern?", "confirmRestoreDialog": "<PERSON><PERSON><PERSON> wiederherstellen {{variable}}?", "confirmSaveNote": "Möchten Sie die Anmerkung speichern?", "confirmSleepCase": "<PERSON>öchten Si<PERSON> den Fall in den Schlaf-Modus versetzen?", "confirmTakeoverTsk": "Möchten Sie die Aufgabe wirklich übernehmen?", "confirmWakeUpCase": "Möchten Sie den Fall wirklich aufwecken?", "connection": "Verbindung", "connectionFailed": "Die Verbindung zum Server ist fehlgeschlagen.", "connectionVar": "Verbindung", "constant": "<PERSON><PERSON>ante", "contact": "Kontakt", "contactTaskOwner": "<PERSON><PERSON><PERSON> kont<PERSON>", "containerSettings": "Einstellung des Containers", "contains": "<PERSON>th<PERSON><PERSON>", "continueSolving": "Lösung fortsetzen", "copied": "Kopiert!", "copy": "<PERSON><PERSON><PERSON>", "copyShortcut": "Ctrl+C drücken", "copyToClipboard": "In die Zwischenablage kopieren", "createForm": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "csv": "csv", "csvFile": "CSV-Datei", "customPrint": "<PERSON><PERSON>ner Druck", "daily": "Tä<PERSON><PERSON>", "dashCvNoOverview": "Es wurde keine Übersicht ausgewählt – diese wird in der Einstellung des Containers ausgewählt", "dashCvNoRights": "Sie haben keine Berechtigung zur Anzeige der Übersicht, kontaktieren Sie den Administrator", "dashFavNoShortcut": "<PERSON>s wurden keine Vertreter ausgewählt – diese werden in der Einstellung des Containers ausgewählt", "dashboard": "Dashboard", "date": "Datum", "dateList": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "day": "Tag", "dayE": "am Tag", "daysDHM": "Tage: (dd:hh:mm)", "defChangeVarInfluence": "Die Änderung der Definition dieser Variablen wird sich auch in den bereits erstellten Fällen widerspiegeln.", "defChangeInfluence": "Die Änderung der Definition wird sich auch in den bereits erstellten Fällen widerspiegeln.", "defaultCaseName": "Vorgegebener Name des Falles", "defaultLbl": "Voreingestellt {{label}}", "defaultLblShe": "Voreingestellt {{label}}", "defaultLblIt": "Voreingestellt {{label}}", "defaultPrintProcess": "Voreingestellt – Fall", "defaultPrintTask": "Voreingestellt – Aufgabe", "defaultValue": "De<PERSON>ult-<PERSON><PERSON>", "delUser": "Benutzer löschen", "delete": "Löschen", "deleteCol": "Spalte löschen", "deleteRow": "Zeile löschen", "deleteSelected": "Markierte löschen", "deleted": "Gelöscht", "deletedOn": "Gelöscht", "deletedShe": "Gelöscht", "description": "Beschreibung  ", "deselect": "Auswahl stornieren", "detail": "Detail", "developed": "Entwickelt", "dial": "Codeliste", "dic": "Ust.-Id.-Nr.", "directSubOfPlanGuar": "Direkte Untergeordnete des Plan-Garanten", "directSupOfPlanGuar": "Direkter Vorgesetzte des Plan-Garanten", "disableFilter": "<PERSON><PERSON>", "dmsAssignAttrs": "DMS Zuordnung der Attribute", "dmsAttribute": "DMS Attribut", "dmsAttributes": "DMS Attribute", "dmsColumns": "DMS - Spalten", "dmsVisNull": "<PERSON>ur in diesem Prozess", "dmsVisSub": "Auch in den untergeordneten Prozessen", "dmsVisSup": "Auch in den übergeordneten Prozessen", "dmsVisSupSub": "<PERSON>ch in den untergeordneten sowie in den übergeordneten Prozessen", "dmsVisibility": "Die Dokumente werden sichtbar sein", "doNotShowVariablesWith_": "Die Variablen mit der Bezeichnung, die mit dem Zeichen `_` beginnen, werden nicht den Benutzern angezeigt", "document": "Dokument", "documentVar": "Dokument", "documents": "Dokumente", "doesNotContain": "enthält nicht", "done": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragAddFiles": "<PERSON><PERSON> durch Z<PERSON>hen e<PERSON>, oder {{- here }} klicken, um die Dateien auszuwählen.", "dragAddFile": "<PERSON>i durch <PERSON> e<PERSON>, oder {{- here }} klicken, um die Dateien auszuwählen.", "here": "hier", "dropContainer": "Container en<PERSON><PERSON><PERSON>", "dropzoneTip": "<PERSON><PERSON> hi<PERSON>her ziehen", "dropZoneUserPhoto": "Foto hierher ziehen oder hier klicken, um die Datei auszuwählen.", "dueDate": "<PERSON><PERSON><PERSON>", "duty": "<PERSON><PERSON><PERSON><PERSON>", "dynList": "<PERSON><PERSON><PERSON>", "dynRowsDefinition": "Definition der Tabelle und der Spalten", "dynTableName": "Name der dynamischen Tabelle", "dynTable": "Dynamische Tabelle", "dynTables": "Dynamische Tabellen", "dynamicList": "Dynamische Liste", "dynamicRows": "Dynamische Zeilen", "dynamicTable": "Dynamische Tabelle", "edit": "<PERSON><PERSON><PERSON>", "editAttribute": "Attribut bearbeiten", "editOrgUnit": "Organisationseinheit bearbeiten", "editRole": "<PERSON><PERSON> bearbeiten", "editRule": "<PERSON>el bear<PERSON>ten", "editUser": "<PERSON><PERSON><PERSON> bearbeiten", "editor": "Editor", "email": "E-Mail", "emailsQueue": "E-Mail-Warteschlange", "empty": "<PERSON><PERSON>", "end": "<PERSON><PERSON>", "error": "<PERSON><PERSON>", "errored": "<PERSON><PERSON>", "error404": "Fehler 404 - Seite nicht gefunden!", "event": "<PERSON><PERSON><PERSON><PERSON>", "events": "<PERSON><PERSON><PERSON><PERSON>", "eventsRun": "<PERSON><PERSON><PERSON><PERSON> starten", "every": {"masc": "<PERSON><PERSON>", "neutral": "<PERSON><PERSON>", "repeat": "Alle"}, "everyWorkDay": "Jeden Arbeitstag", "excel": "Excel", "favourites": "<PERSON><PERSON>", "fax": "Fax", "file": "<PERSON><PERSON>", "fileLogicalType": "Logis<PERSON>", "fileName": "Dateiname", "filePlacement": "Platzierung im Verzeichnis", "files": "<PERSON><PERSON>", "filter": "Filter  ", "filterFrom": "<PERSON><PERSON> <PERSON>", "filterTitle": "Filter", "filtrate": "Filtern", "finishTask": "Aufgabe erfüllen", "finished": "<PERSON><PERSON><PERSON>", "finishedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "finishedOn": "Fertiggestellt am", "first": "<PERSON><PERSON><PERSON> ", "firstLeft": "erste links", "firstName": "Name  ", "firstRight ": "erste rechts", "firstRowColumnsName": "Die erste Zeile sind Spaltennamen", "folder": "Verzeichnis", "folder-": "Verzeichnis -", "folderExecRightsText": "<PERSON><PERSON>, die  die Fälle im Verzeichnis starten können", "folderExecRightsTextOS": "Organisationseinheiten zuordnen, die die Fälle im Verzeichnis starten können", "folderName": "Verzeichnisname", "font": "<PERSON><PERSON><PERSON><PERSON>", "fontMainHeader": "Schriftart der Hauptkopfzeile", "form": "Formular  ", "fourth": "Vierte", "freeTsk": "Freie Aufgabe", "fri": "Freitag", "from": "<PERSON>", "fsDescription": "Beschreibung", "fsName": "Name", "fsTooltip": "<PERSON><PERSON><PERSON>", "fullName": "Voller Name", "fullScreen": "Gesamter Bildschirm", "getTotalCount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "graph": "Diagramm", "handExecutionTaskListEmpty": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>", "handOver": "Weiterleiten  ", "handOverToUser": "An den Benutzer weiterleiten", "handover": "Weiterleiten", "headerDashboard": "Dashboard-Kopfzeile", "help": "<PERSON><PERSON><PERSON>", "hideLogout": "Abmelden' ausblenden", "hideNewProcess": "Neuer Fall' ausblenden", "hideProcs": "Fälle' ausblenden", "hideTasks": "Aufgaben' ausblenden", "historicalValues": "Historische Werte", "currentValues": "Aktuelle Werte", "history": "Historie", "home": "Home", "html": "HTML", "ic": "Id.-Nr.", "id": "ID", "inCasesNames": "In den Namen der Fälle", "inTasksNames": "In den Namen der Fälle", "inDevelopment": "In der Entwicklung", "inEvery": "in allen", "inFiles": "in den Dateien", "initiator": "Initiator", "inTasks": "in den Aufgaben", "inactive": "Nicht aktiv", "inactiveShe": "Nicht aktiv", "incidences": "Ereignissen", "inclusion": "Einordnung", "info": "Info", "inputParams": "Verhalten am Eingang", "insert": "Einfügen", "insertAttachTip": "Dokument durch Ziehen einfügen", "insertVar": "Variable einfügen", "insertSnippet": "Snippet einfügen", "snippet": "Codefragment", "insertedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "insertedOn": "Eingefügt am", "insteadOf": "für ", "instructions": "Anweisungen", "invitation": "Einladung", "isEmail": "E-Mail ist nicht valid", "isEmpty": "ist leer", "isExisty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isManagerOrgUnit": "Ist Manager der Organisationseinheiten", "isNotEmpty": "ist nicht leer", "isRequired": "Obligatorisch", "justSave": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "keepGlobalOrder": "Globale Anordnung belassen", "key": "Schlüssel", "last": "letzte", "lastName": "Nachname", "lastOwnerOfTask": "Dem letzten Löser der Aufgabe", "licenceKey": "Lizenzschlüssel", "link": "Verbindung", "linkConditions": "Verbindungsbedingungen", "list": "Liste", "listName": "Name der Liste", "listOfValues": "Liste der Werte", "listValue": "<PERSON><PERSON> der Liste", "loading": "Einlesen läuft…", "location": "Ort", "locked": "<PERSON><PERSON><PERSON><PERSON>", "logIn": "Anmelden", "logOut": "Abmelden ", "logicalType": "Logischer Typ", "loginError": "Die Anmeldung ist fehlgeschlagen.", "loginTimeout": "Ablauf der Anmeldezeit (s)", "longText": "Langer Text", "mailEscalation": "E-Mail mit einem Überblick über die eskalierten Aufgaben", "mailProcEscalation": "E-Mail mit einem Überblick über die eskalierten Fälle", "mailPromptly": "E-Mail-Hinweis auf eine neue Aufgabe", "mailPull": "E-Mail-Hinweis auf eine neue Aufgabe zur Abnahme", "mailTotal": "E-Mail mit einem Überblick über die Aufgaben", "mainButton": "<PERSON><PERSON><PERSON><PERSON>", "mainColor": "Hauptfarbe", "mainHeader": "Hauptkopfzeile", "mainLanguage": "Hauptsprache", "manager": "Manager", "managerOfOrgUnit": "Manager der Organisationseinheit", "mandatory": "Obligatorisch", "manualStartEvent": "Manueller Start des Ereignisses", "mapping": "Mapping", "mappingSubProcessVars": "Mapping der Variablen des Subprozesses", "markAll": "Alle markieren", "menu": "<PERSON><PERSON>", "mine": "<PERSON><PERSON>", "mobilePhone": "Handy", "mon": "Montag", "month1": "<PERSON><PERSON><PERSON>", "month10": "Oktober", "month11": "November", "month12": "Dezember", "month2": "<PERSON><PERSON><PERSON>", "month3": "<PERSON><PERSON><PERSON>", "month4": "April", "month5": "<PERSON>", "month6": "<PERSON><PERSON>", "month7": "<PERSON><PERSON>", "month8": "August", "month9": "September", "monthI": "<PERSON><PERSON>", "monthly": "<PERSON><PERSON><PERSON>", "months": "Monate", "more": "<PERSON><PERSON>", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingle", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "Multiinstanz", "myUnfinishedTasks": "Meine nicht erledigten Aufgaben", "name": "Name", "nested": "Verschachtelt", "never": "<PERSON><PERSON><PERSON>", "new": "<PERSON>eu ", "newCase": "Neuer Fall ", "newFolder": "Verzeichnis - neu", "newForm": "Neues Formular", "newIt": "Neue", "newName": "Neuer Name", "newShe": "Neue", "newSolver": "<PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON>", "noAttach": "<PERSON><PERSON> Dokumente (hinzufügen durch Klicken)", "clickToAddAttach": "Hinzufügen durch Klicken", "noName": "<PERSON><PERSON>", "noOneBeOffered": "<PERSON><PERSON><PERSON><PERSON>, wird einer begrenzten Nutzergruppe zum Lösen angeboten", "noPageRights": "Sie haben keine Berechtigung zur Anzeige dieser Seite.", "node": "Knoten", "notFound": "Nicht gefunden", "notMatch": "<PERSON><PERSON>", "notNumber": "<PERSON><PERSON>", "notIntNumber": "<PERSON><PERSON> gan<PERSON>", "notSent": "<PERSON>cht gesendet", "notValid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notes": "Anmerkungen", "notesOnContacts": "Anmerkungen zu den Kontakten", "notice": "<PERSON><PERSON><PERSON><PERSON>", "notification": "Notifikation", "nrOfItems": "Anzahl der Eintragungen", "number": "<PERSON><PERSON><PERSON>", "numberList": "Nummernliste", "ok": "OK", "oneMustBeMet": "Mindestens eine muss erfüllt sein", "onlyOrgUnit": "Nur Organisationseinheit", "onlyPlanGuarantor": "Nur Garant des Plans", "openAll": "<PERSON><PERSON>", "operating": "Betriebs-", "order": "<PERSON><PERSON><PERSON><PERSON>", "orderByColumn": "Anordnung nach Spalte", "orgName": "Name des Subjektes", "orgStructure": "<PERSON><PERSON><PERSON>", "orgUnit": "Organisationseinheit", "orgUnitE": "Organisationseinheit", "orgUnitName": "Name der Organisationseinheit", "orgUnitShe": "Organisationseinheit", "orgUnits": "Organisationseinheiten  ", "organization": "Organisation", "overview": "Übersicht", "overviewMapping": "Übersicht Mapping", "overviewNew": "Übersicht - neu", "overviewSetSharing": "Teilung der Übersicht für die einzelnen Nutzergruppen einstellen", "overviews": "Übersichten", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerWithLeastTasks": "<PERSON> den Löser, der die wenigsten Aufgaben hat", "pageNotFound": "Seite nicht gefunden", "parentFolder": "Übergeordnetes Verzeichnis", "parentUnit": "Übergeordnete Einheit", "participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "passwordChallenge": "Aufforderung", "passwordChallengeText": "Möchten Si<PERSON> wirklich alle Benutzer zur Änderung des Passwortes auffordern?", "passwordChange": "Änderung des Passwortes", "passwordCheck": "Passwort (Kontrolle)", "passwordNew": "Neues Passwort", "passwordNewCheck": "Neues Passwort (Kontrolle)", "paused": "Gestoppt", "personInOrgStr": "Auftraggeber in der Organisationsstruktur", "phone": "Telefon", "photo": "Foto", "plan": "Plan", "planGuarantor": "Garant des Plans", "planTitle": "Plan", "plans": "Planung", "plnOffType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plnOrgUnit": "Organisationseinheit", "plnTProc": "<PERSON><PERSON><PERSON><PERSON>", "plnUser": "Garant des Plans", "plnUsersSelect": "Einschränkende Bedingungen für die Auswahl eines oder mehrerer Initiatoren", "prependTsk": "Aufgabe voranstellen", "prependedTsk": "Vorangestellte Aufgabe", "primaryKey": "Primärschlüssel", "print": "Druck", "printTemplate": "Druckschablone", "printType": "Art des Druckes", "printer": "Druck - HTML", "priority": "Priorität", "procDescription": "Prozessbeschreibung", "procDueDateFinish": "Frist für den Abschluss des Falles", "procName": "Name des Fall<PERSON>", "procOwner": "Prozesseigentümer", "procSummary": "In Lösung", "process": "Prozess", "processName": "Prozessname", "property": "Eigenschaft", "quickFilter": "<PERSON><PERSON><PERSON>", "radioButtonList": "RadioButtonList", "reEvaluates": "<PERSON><PERSON>", "recalc": "Nachberechnen", "recipient": "<PERSON><PERSON><PERSON><PERSON>", "recipientsId": "ID des Empfängers", "records": "Eintragungen", "referenceUser": "Refer<PERSON>z<PERSON>", "refresh": "Aktualisieren", "registered": "Registrierte", "relatToPlanSponsor": "Verhältnis zum Garanten des Plans", "remove": "Entfernen", "removeVice": "Vertretung löschen", "renameCols": "Spalten umbenennen", "repeatLogin": "Anmeldung wiederholen oder einen anderen Typ wählen.", "repeatOrReport": "<PERSON>te später wiederholen oder den Administrator kontaktieren.", "repetition": "Wiederholung", "required": "Obligatorisch", "reset": "Reset", "restrictTaskOwners": "Einschränkung für den Löser", "restrictUsers": "Benutzer einschränken", "returnSubProcessVars": "Rückkehr der Variablen des Subprozesses", "revision": "Revision", "right": "<PERSON><PERSON>", "rightOrDuty": "Re<PERSON>/<PERSON>t", "role": "<PERSON><PERSON>", "roleName": "Name der <PERSON>e", "roleSg": "<PERSON><PERSON>", "roles": "<PERSON><PERSON>", "row": "<PERSON><PERSON><PERSON>", "rule": "Regel", "ruleCSVFile": "Name der CSV-<PERSON><PERSON>", "ruleCSVHeader": "Die erste Zeile der CSV-Datei ist die Kopfzeile", "ruleCSVMask": "Namensmaske der CSV-Datei", "ruleCSVSeparator": "Trennzeichen der Spalten", "ruleNew": "Regel - neu", "ruleParamsMap": "Mapping der Variablen  ", "ruleProcOwnCSV": "Im <PERSON> definiert", "ruleTypeCSVExpProcs": "CSV-Export aller Schablonenfälle", "ruleTypeCSVMrgProcs": "Je nach CSV die Fälle starten und die Variablen der Fälle aktualisieren", "ruleTypeCSVRunProcs": "Je nach CSV die Fälle starten ", "ruleTypeCSVUpdProc": "Je nach CSV die Variablen der Fälle aktualisieren", "ruleTypeCSVUpdProcs": "Je nach CSV die Variablen der Fälle aktualisieren ", "ruleTypeCSVUpdateList": "Update der dynamischen Liste nach CSV", "ruleTypeReturn": "Antwort auf das Ereignis", "ruleTypeUpdateListOfProcesses": "Update der dynamischen Liste der Fälle", "rules": "Regeln", "run": "Starten", "runProcess": "Prozess starten", "running": "<PERSON><PERSON><PERSON>", "sat": "Samstag", "save": "Speichern", "saveAsAttachment": "Druck als Falldokument speichern", "scheduling": "Zeitplanung", "scheme": "Visuelle Identität", "script": "S<PERSON><PERSON><PERSON>", "scripts": "Skripte", "search": "<PERSON><PERSON>", "searchResult": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "second": "Zweite", "secondLeft": "zweite links", "secondRight": "zweite rechts", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "<PERSON> den Löser, der vom Computer automatisch ausgewählt wird ", "selectedByTaskSupervisor": "<PERSON> Löser, der vom Supervisor ausgewählt wird", "selectedPrint": "ausgewählter Druck", "selectedUser": "Ausgewählter Benutzer", "send": "Senden", "sendTestMail": "Test-E-Mail", "sequence": "Sequenz", "setDefault": "Als Default einstellen", "setVice": "Vertretung einstellen", "setViceAttachmentsNotes": "Rechte zum Einfügen von Dokumenten und Anmerkungen", "settings": "Einstellung", "shortcuts": "<PERSON><PERSON><PERSON><PERSON>", "showAttachmentsClick": "Dokumente durch Klicken anzeigen", "showCommentCol": "Spalte Kommentar anzeigen", "skype": "Skype", "solve": "<PERSON><PERSON><PERSON>", "solvedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "solver": "<PERSON><PERSON><PERSON>", "sort": "Ordnen", "sortByColumn": "Nach der Spalte ordnen", "sorting": "Ordnen", "sourceTask": "Quellaufgabe", "sourceVar": "Quellvariable", "start": "Begin<PERSON>", "startDate": "Anfangsdatum", "startCalDate": "Frühestmögliches Datum", "endCalDate": "Letztmögliches Datum", "state": "Zustand", "stateAddress": "Staa<PERSON>", "status": "Status", "street": "Straße und Hausnummer", "subProcess": "Subprozess", "subject": "Gegenstand", "substitute": "Vertretung", "sun": "Sonntag", "superior": "Übergeordnet", "supervis": "Supervisor", "supervisor": "Supervisor der Aufgabe", "suspend": "In den Schlaf-<PERSON><PERSON>en", "suspended": "<PERSON><PERSON>", "suspendedx": "<PERSON><PERSON><PERSON><PERSON>", "tTaskAgain": "Verhalten bei wiederholter Aktivierung", "tTaskAutoCompleteCaption": "Die Aufgabe wird automatisch erfüllt, wenn", "tTaskCompletionCOA": "alle Bedingungen gleichzeitig gegeben sind", "tTaskCompletionCOO": "mindestens eine Bedingung gegeben ist", "tTaskDueOffsetNone": "unverzüglich", "tTaskDueOffsetPO": "gibt der Supervisor vor", "tTaskDueOffsetPS": "innerhalb von x Tagen nach dem Beginn des Falles", "tTaskDueOffsetTS": "innerhalb von x Tagen nach dem Beginn der Aktivität", "tTaskDueOffsetVC": "aus der Variablen kontinui<PERSON>lich", "tTaskDueOffsetVO": "aus der Variablen beim <PERSON>", "tTaskInvClassConf": "Geheim", "tTaskInvClassPriv": "Privat", "tTaskInvClassPubl": "<PERSON><PERSON><PERSON><PERSON>", "tTaskInvPriority1": "1-höchste", "tTaskInvPriority2": 2, "tTaskInvPriority3": 3, "tTaskInvPriority4": 4, "tTaskInvPriority5": 5, "tTaskInvPriority6": 6, "tTaskInvPriority7": 7, "tTaskInvPriority8": 8, "tTaskInvPriority9": "9-ni<PERSON><PERSON><PERSON>", "tTaskInvokeEventB": "im Hintergrund", "tTaskInvokeEventI": "sofort", "tTaskReferenceUserLastSolver": "Letzter Löser der Aufgabe", "tTaskReferenceUserMan": "Manager der Organisationseinheit xy", "tTaskReferenceUserUser": "Benutzer xy", "tTaskRunOnlyOnce": "<PERSON><PERSON> ein<PERSON> starten", "tTaskSufficientEnd": "Die Erfüllung wird den ganzen Fall beenden", "tabName": "Bookmark-Name", "table": "<PERSON><PERSON><PERSON>", "takeOnlyOrder": "<PERSON><PERSON> die Reihenfolge nehmen", "takeover": "Übernehmen", "targetTask": "Zielaufgabe", "targetVar": "Zielvariable", "taskAutomatic": "automatischer Zustand", "taskEmailNotification": "E-Mail-Notifikation", "taskEvent": "startet das Ereignis", "taskEventWait": "wartet auf das Ereignis", "taskOwner": "Löser der Aufgabe", "taskSolverAssign": "zum Z<PERSON>rdnen zu dem <PERSON>öser", "taskStart": "Begin<PERSON>", "taskStatus": "Status", "taskStatusA": "Aktiv", "taskStatusAP": "Aktiver Subprozess", "taskStatusAS": "Subprozess im Schlaf-Modus", "taskStatusD": "<PERSON><PERSON><PERSON><PERSON>", "taskStatusL": "Wartend", "taskStatusLEdit": "Die wartende Aufgabe kann nicht bearbeitet werden", "taskStatusN": "<PERSON>eu", "taskStatusP": "<PERSON><PERSON><PERSON>", "taskStatusS": "Schlafend", "taskStatusT": "<PERSON><PERSON>", "taskStatusW": "<PERSON><PERSON>  ", "taskStatusWT": "Zur zeitlichen Planung", "taskSubprocess": "durch den Subprozess realisiert", "taskTabVariables": "Zuordnen der Variablen", "taskType": "Art der Aufgabe", "taskWillBeAssigned": "Die Aufgabe wird vergeben", "tasks": "Aufgaben ", "tasksToPull": "Aufgaben zur Abnahme", "taskstatusAD": "Aktiv sowie beendet", "tempId": "ID Schablonen", "tempVar": "<PERSON><PERSON><PERSON>", "template": "<PERSON><PERSON><PERSON>", "templateDeleted": "Gelöschte", "templateStatus": "<PERSON><PERSON><PERSON>", "templates": "<PERSON><PERSON><PERSON><PERSON>", "templatesFolder": "Schablonen - Verzeichnis", "testForm": "Testformular", "tested": "Getestet", "text": "Text", "textList": "Textliste", "textMultipleLines": "Text mit mehreren Zeilen", "textSuggest": "Autovervollständigung", "third": "Dritte", "thirdCenter": "dritter Center", "thu": "Don<PERSON><PERSON>", "thumbnail": "<PERSON><PERSON><PERSON>", "title": "Titel", "to": "Bis", "toHide": "Ausblenden", "toInclusive": "Bis (e<PERSON><PERSON><PERSON><PERSON>lich)", "toPull": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON>", "total": "insgesamt", "tprocName": "Prozessschablone", "tsk": "Aufgabe", "tskAssignDues": "Zeitliche Beschränkung für diese Aufgabe einstellen", "tskName": "Name der Aufgabe", "tskNum": "Aufgabe Nr.", "tskSolver": "Löser der Aufgabe", "tskTemplate": "Schablone der Aufgabe", "tskVar": "Aufgabe", "tsksDone": "<PERSON><PERSON><PERSON>", "tsksSolvers": "<PERSON><PERSON> den Lösern", "ttAdd": {"heading": "Hinzufügen", "body": "Ermöglicht das Hinzufügen einer neuen Position bzw. neuer Paramater, die noch nicht definiert wurden."}, "ttAddActivity": {"heading": "Hinzufügen", "body": ""}, "ttAddAttach": {"heading": "Dokument hinzufügen", "body": "Ermöglicht das Hinzufügen eines neuen Dokumentes."}, "ttAddAttribute": {"heading": "Hinzufügen", "body": ""}, "ttAddContainer": {"heading": "Container <PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON> einen Container mit bestimmten Inhalt hinzu."}, "ttAddFile": {"heading": "Hinzufügen", "body": ""}, "ttAddStructure": {"heading": "Position der Organisationsstruktur hinzufügen", "body": "Ermöglicht das Hinzufügen einer neuen Position der Organisationsstruktur bzw. neuer Parameter, die noch nicht definiert wurden."}, "ttAddTemp": {"heading": "Neue Schablone hinzufügen", "body": "Anlegen einer neuen Fallschablone. Der Eigentümer der Schablone ist der aktuell angemeldete Benutzer. Der Schablone wird automatisch der Zustand \"in Entwicklung\" zugeordnet."}, "ttAddTsk": {"heading": "Neue Aufgabe hinzufügen", "body": "Anlegen einer neuen Aufgabe im Rahmen der Prozessschablone. Je nach der Art der Aufgabe können seine Parameter spezifiziert werden. Die Verbindung mit anderen Aufgaben kann im Bookmark Diagramm oder Verbindung hinzugefügt oder bearbeitet werden."}, "ttAddTskGraph": {"heading": "Neue Aufgabe hinzufügen", "body": "Anlegen einer neuen Aufgabe im Rahmen der Prozessschablone. Je nach der Art der Aufgabe können seine Parameter spezifiziert werden. Die Verbindung mit anderen Aufgaben kann im Bookmark Diagramm oder Verbindung hinzugefügt oder bearbeitet werden."}, "ttAddUser": {"heading": "Neuen Benutzer hinzufügen", "body": "Anlegen eines neuen Benutzers. <PERSON><PERSON>utzer muss einen einmaligen Benutzernamen haben. beim <PERSON> können die grundlegenden Informationen, die Zuordnung zur Organisationsstruktur und die Rollenzuweisung eingestellt werden. Be<PERSON> wird automatisch der Zustand \"gesperrt\" eingestellt."}, "ttAddVar": {"heading": "Neue Variable hinzufügen", "body": "Anlegen einer neuen Variablen im Rahmen der Fallschablone. Jede Variable ist ein Träger von Informationen, mit denen die Löser der Fallaufgaben arbeiten können. Es können der Name, der Typ und die Default-Werte der Variablen spezifiziert werden."}, "ttAddVice": {"heading": "Vertretung hinzufügen", "body": ""}, "ttAssignAttribute": {"heading": "Attribute dem logischen Typ des Dokumentes hinzufügen", "body": ""}, "ttAssignTsk": {"heading": "Hinzufügen", "body": "Ermöglicht das Hinzufügen der Aufgabe dem konkreten Löser oder die Einordnung der Position in die definierte Struktur."}, "ttCases": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttOverviews": {"heading": "Übersichten", "body": ""}, "ttChangePass": {"heading": "Passwortänderung", "body": "Bearbeitung des Benutzerpasswortes der Benutzer, die direkt in der Anwendnungsumgebung verwaltet werden. Werden die Benutzer von einem anderen Dienst verwaltet, ist ein Passwort von diesem externen Dienst - z.B. LDAP Server notwendig."}, "ttClose": {"heading": "Schließen", "body": "Das Fenster wird ohne Speicherung der durchgeführten Änderungen geschlossen."}, "ttCloseTemp": {"heading": "Schließen", "body": "Das Fenster mit der Definition der Schablone wird geschlossen."}, "ttCompleteTsk": {"heading": "Aufgabe erfüllen", "body": "Bestätigt die Erfüllung der Aufgabe und schickt nach dem vordefinierten Prozess zur weiteren Bearbeitung."}, "ttContact": {"heading": "Kontakt", "body": "Zeigt die Kontaktdaten des Supervisors der Aufgabe an."}, "ttContainerSettings": {"heading": "Einstellung", "body": "Ermöglicht eine Änderung der Einstellung für den gegebenen Container."}, "ttCopyHdr": {"heading": "Kopfzeile kopieren", "body": "Erstellung einer Kopie der markierten Kopfzeile. Die Markierung wird durch das Anklicken in der Tabelle der Schablonenkopfzeilen durchgeführt."}, "ttCopyTemp": {"heading": "<PERSON><PERSON><PERSON> kopieren", "body": "Erstellung einer Kopie der markierten Schablone. Die Markierung wird durch das Anklicken in der Tabelle der Prozessschablonen durchgeführt."}, "ttCopyVar": {"heading": "Variable kopieren", "body": "Kopieren der Definition der markierten Variable und ihre Speicherung unter einem neuen Namen. Die Markierung wird durch das Anklicken in der Tabelle der Variablen durchgeführt."}, "ttDel": {"heading": "Löschen", "body": "Löscht die markierte Position."}, "ttDelAttach": {"heading": "Dokument löschen", "body": "Löscht das markierte Dokument."}, "ttDelConnection": {"heading": "Verbindung löschen", "body": "Löschen der markierten Verbindung zwischen zwei Fallaufgaben. Die Aktion muss noch bestätigt werden. Die Aktion wird für die markierte Verbindung. Die Markierung wird durch das Anklicken in der Tabelle der Verbindungen durchgeführt."}, "ttDelFolder": {"heading": "Verzeichnis löschen", "body": "Löschen des markierten Verzeichnisses."}, "ttDelOverview": {"heading": "Übersicht löschen", "body": "Löscht die markierte Übersicht."}, "ttDelTemp": {"heading": "<PERSON><PERSON><PERSON> löschen", "body": "<PERSON>ügt der Schablone den Zustand \"gelösch<PERSON>\" hinzu. Erst bei einer wiederholten Anforderung an das Löschen kommt es zur physischen Beseitigung der Schablone. Die Aktion wird für die markierte Schablone durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Fallschablonen  durchgeführt."}, "ttDelTsk": {"heading": "Aufgabe löschen", "body": "Löschen der markierten Aufgabe. Die Aktion muss noch bestätigt werden. Zusammen mit der Aufgabe werden die zusammenhängenden Verbindungen zu weiteren Aufgaben in der Prozessschablone beseitigt. Die Markierung wird durch das Anklicken in der Tabelle der Aufgaben durchgeführt."}, "ttDelTskOrConnection": {"heading": "Löschen der Aufgabe oder der Verbindung", "body": "Beseitigung der markierten Aufgabe oder der markierten Verbindung zwischen zwei Prozessaufgaben. Die Aktion muss noch bestätigt werden. Zusammen mit der Aufgabe werden die zusammenhängenden Verbindungen zu weiteren Prozessaufgaben beseitigt. Die Markierung wird durch das Anklicken durchgeführt."}, "ttDelVar": {"heading": "Variable löschen", "body": "Beseitigung der markierten Variable. Die Aktion muss noch bestätigt werden. Die Variable wird ferner nicht mehr in den einzelnen Prozessaufgaben zur Verfügung stehen. Die Markierung wird durch das Anklicken in der Tabelle der Variablen durchgeführt."}, "ttDelVice": {"heading": "Vertretung löschen", "body": ""}, "ttDetailCase": {"heading": "Detail", "body": "Zeigt die Details des markierten Falles an."}, "ttDetailCertificate": {"heading": "Zertifikat-Details", "body": "Zeigt die Details des ausgewählten Zertifikats an."}, "ttDetailHistory": {"heading": "Detail", "body": "Zeigt die Details der markierten Position an."}, "ttDetailTsk": {"heading": "Detail der Aufgabe", "body": "Zeigt die Details der markierten Aufgabe an."}, "ttDmsFolderAdd": {"heading": "Neues Verzeichnis hinzufügen", "body": "Einfügen eines neuen Ordners. Wenn ein Ordner ausgewählt ist, wird der Ordner als übergeordneter Ordner vorausgefüllt."}, "ttDmsFolderEdit": {"heading": "Verzeichnis bearbeiten", "body": "Bearbeiten Sie den markierten Ordner."}, "ttDocuments": {"heading": "Dokumentenspeicher", "body": "Es kommen hier alle Dokumente der Fälle + weitere manuell hochgeladenen Dateien vor. Ermöglicht das Suchen von Dateien nach dem Namen oder nach den eingestellten Attributen."}, "ttDownload": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON>dt die markierte Datei herunter."}, "ttDropContainer": {"heading": "Entfernen", "body": "Entfernt den Container aus Dashboard."}, "ttENotification": "E-Mail-Notifikation", "ttEdit": {"heading": "<PERSON><PERSON><PERSON>", "body": "Ermöglicht das Bearbeiten der markierten Position."}, "ttEditAttach": {"heading": "<PERSON><PERSON><PERSON>", "body": "Ermöglicht das Anzeigen und Bearbeiten von Attributen (Metadaten) der hochgeladenen Datei."}, "ttEditConnection": {"heading": "Verbindung bearbeiten", "body": "Bearbeitung der Verbindung zwischen zwei Aufgaben. Es können die Parameter des Verbindungsverhaltens und die Verbindungsbedingung bearbeitet werden. Die Aktion wird für die markierte Verbindung durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Verbindungen durchgeführt."}, "ttEditOverview": {"heading": "Übersicht bearbeiten", "body": "Ermöglicht das Bearbeiten der markierten Übersicht."}, "ttCopyOverview": {"heading": "Übersicht kopieren", "body": "Kopiert die markierte Übersicht."}, "ttEditPath": {"heading": "Übersicht hinzufügen", "body": "Ermöglicht die Definition einer neuen Übersicht."}, "ttEditTemp": {"heading": "Definition der Schablone bearbeiten", "body": "Bearbeitung der Fallschablone. Es können alle Parameter der Schablone bearbeitet werden. Die Aktion wird für die markierte Schablone durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Fallschablonen durchgeführt."}, "ttEditTsk": {"heading": "Aufgabe bearbeiten", "body": "Bearbeitung der Informationen über die Aufgabe und ihrer Parameter. Die Aktion wird für die markierte Aufgabe durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Aufgaben durchgeführt."}, "ttEditTskOrConnection": {"heading": "Aufgabe oder Verbindung bearbeiten", "body": "Bearbeitung der Informationen über die Aufgabe und ihrer Parameter oder Bearbeitung der Verbindung zwischen zwei Aufgaben, ihrer Verhaltensparameter und der Verbindungsbedingung. Die Aktion wird für die markierte Aufgabe oder markierte Verbindung durchgeführt. Die Markierung wird durch das Anklicken durchgeführt."}, "ttEditTskVars": {"heading": "<PERSON><PERSON><PERSON>", "body": "Variablen der Aufgabe bearbeiten."}, "ttEditUser": {"heading": "Benutzerinformationen bearbeiten", "body": "Bearbeitung der grundlegenden Benutzerinformationen, des Passwortes, der Zuordnung zur Organisationseinheit und der Rollenzuweisung. Die Aktion wird für den markierten Benutzer durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Benutzer durchgeführt."}, "ttEditVar": {"heading": "Variable bearbeiten", "body": "Bearbeitung des Namens, des Typs und der Default-Werte der Variablen. Die Aktion wird für die markierte Variable durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Variablen durchgeführt."}, "ttEnotTgt": "<PERSON><PERSON><PERSON><PERSON>", "ttEnotTgtG": "Supervisor der Aufgabe", "ttEnotTgtO": "Eigentümer des Falles", "ttEnotTgtP": "%s", "ttEnotTgtR": "Rolle %s", "ttEnotTgtS": "Organisationseinheit %s", "ttEnotTgtT": "Löser der Aufgabe %s", "ttEvent": {"heading": "<PERSON><PERSON>", "body": "Sofortiges Aufrufen des Ereignisses in diesem Fall."}, "ttEvents": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Einstellung der Business-Regeln für die Reaktion auf definierte interne oder externe Ereignisse im System. Für den Zugang ist die Rolle $PowerUser notwendig."}, "ttFavourites": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Liste aller Favoriten mit der Möglichkeit diese zu bearbeiten oder aus der Liste zu löschen."}, "ttFilter": {"heading": "Filtern", "body": "Zeigt nur die Positionen an, die die definierten Filterbedingungen erfüllen."}, "ttFilterPrc": {"heading": "Filtern", "body": "Zeigt nur die Fälle an, die die definierten Filterbedingungen erfüllen."}, "ttFilterTemp": {"heading": "<PERSON><PERSON><PERSON> filtern", "body": "Zeigt nur die Schablonen an, die die definierten Filterbedingungen erfüllen."}, "ttFilterTsk": {"heading": "Filtern", "body": "<PERSON>eigt nur Aufgaben an, die die definierten Filterbedingungen erfüllen."}, "ttFilterUser": {"heading": "Filtern", "body": "Zeigt nur die Benutzer an, die die definierten Filterbedingungen erfüllen."}, "ttFullScreen": {"heading": "Ganzer Bildschirm", "body": "<PERSON><PERSON><PERSON> den Inhalt des Containers auf dem ganzen Bildschirm an."}, "ttGraph": {"heading": "Graph", "body": "Graphische Darstellung des Zustandes des aktuelles Falles."}, "ttGraphActualFinish": "Tatsächliches Ende", "ttGraphActualStart": "Datum der Eingabe", "ttGraphCond": "Bedingungen", "ttGraphCond1": "mindestens eine muss erfüllt werden", "ttGraphCondAll": "alle müssen erfüllt werden", "ttGraphCondElse": "Falls keine andere Bedingung erfüllt ist", "ttGraphDeadlinePo": "Frist: gibt der Falleigentümer ein", "ttGraphDeadlinePs": "Frist: inner<PERSON>b von %s Tagen nach dem Beginn des Falles", "ttGraphDeadlineTs": "Frist: innerhalb von %s Tagen nach dem Beginn der Aufgabe", "ttGraphDelayPo": "Beginn der Aufgabe: gibt der Falleigentümer ein", "ttGraphDelayPs": "Beginn der Aufgabe: %s Tage nach dem Beginn des Falles", "ttGraphDelayTs": "Beginn der Aufgabe: %s Tage nach dem Beginn der Aufgabe", "ttGraphEnd": "Die Erfüllung der Aufgabe beendet den ganzen Fall", "ttGraphFinishedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "ttGraphHiearchyA": "alle Vorgesetzten des Aufgaben-Supervisors", "ttGraphHiearchyC": "direkte Untergeordnete des Aufgaben-Supervisors", "ttGraphHiearchyD": "alle Untergeordneten des Aufgaben-Supervisors", "ttGraphHiearchyG": "Supervisor der Aufgabe", "ttGraphHiearchyL": "alle", "ttGraphHiearchyP": "direkter Vorgesetzte des Aufgaben-Supervisors", "ttGraphHiearchyS": "Kollegen des Aufgaben-Supervisors", "ttGraphLinkFrom": "<PERSON>", "ttGraphLinkTo": "Bis", "ttGraphMethodL": "dem letzten Löser der Aufgabe %s", "ttGraphMethodS": "dem <PERSON>, der vom Aufgaben-Supervisor ausgewählt wurde", "ttGraphMethodT": "dem automatisch ausgewählten Löser", "ttGraphMethodV": "dem Löser, der in der Variablen: %s genannt ist", "ttGraphMultiinstance": "Multiinstanz", "ttGraphNoneMand": "Pflichtverbindung", "ttGraphOnlyOnce": "<PERSON><PERSON> ein<PERSON> starten", "ttGraphSave": {"heading": "Graph speichern und Schablone anlegen", "body": ""}, "ttGraphStart": "Die Aufgabe wird nach dem Start der Aufgabe automatisch aktiviert", "ttGraphTaskHiearchy": "<PERSON><PERSON><PERSON>", "ttGraphTaskMethod": "Die Aufgabe wird vergeben", "ttGraphTaskOwner": "Supervisor der Aufgabe", "ttGraphTaskOwnerOS": "Manager der Organisationseinheit ", "ttGraphTaskOwnerPO": "Falleigentümer", "ttGraphTaskOwnerSU": "ausgewählter Benutzer", "ttGraphTaskRole": "mit der Rolle", "ttGraphTaskTypeA": "Automatische Aufgabe", "ttGraphTaskUser": "<PERSON><PERSON><PERSON>", "ttGraphWait1": "Verhalten am Eingang: wartet auf eine", "ttGraphWaitA": "Verhalten am Eingang: wartet auf alle", "ttGraphWaitFirst": "Verhalten am Eingang: wartet auf alle, startet die erste", "ttGraphWaitN": "Verhalten am Eingang: wartet auf %s", "ttHandover": {"heading": "Aufgabe weiterleiten", "body": "Ermöglicht das Weiterleiten der Aufgabe an einen anderen Benutzer aus dem Angebot."}, "ttDelegate": {"heading": "Aufgabe delegieren", "body": "Ermöglicht die Auswahl eines anderen Lösers der Aufgabe im Rahmen seiner Untergeordneten."}, "ttReject": {"heading": "Aufgabe ablehnen", "body": "Die Ablehnung ordnet die Aufgabe dem Aufgaben-Supervisor zu, der einen anderen Löser auswählt."}, "ttHelp": {"heading": "<PERSON><PERSON><PERSON>", "body": "Zulassung und Untersagung der instanten Hilfe. Die Hilfe wird in Form von informativen Wolken zu den Elementen der Benutzerschnittstelle angezeigt, bei denen der Benutzer mit dem Mauszeiger stehen bleibt."}, "ttHome": {"heading": "Homepage des Benutzers", "body": "Einheitlicher Ort mit allen Informationen für den üblichen Benutzer. Es steht ein Gesamtüberblick in Form von Dashboard zur Verfügung."}, "ttHtml": {"heading": "Generierung der Dokumentation", "body": "Generierung der HTML Dokumentation der Prozessschablone. Je nach dem Typ des Browsers kann das Dokument sofort angezeigt oder auf der Platte gespeichert werden."}, "ttInclusion": {"heading": "Einordnung", "body": "Exportiert eine Datei mit zusammengefassten Berechtigungen und Zuordnungen des Benutzers, d.h. alle Rollen des angemeldeten Benutzers, Organisationseinheiten, in denen er ein Mitglied oder Manager ist, einschließlich der Hierarchie der Vorgesetzten und Aufgaben, bei denen der Benutzer ein Supervisor ist."}, "ttInvAttendees": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ttInvDTEnd": "<PERSON><PERSON>", "ttInvDTStart": "Start", "ttInvLocation": "Ort", "ttInvitation": "Einladung", "ttJustSave": {"heading": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "body": "Speichert die Änderungen."}, "ttLock": {"heading": "<PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON>rt oder entsperrt die Position."}, "ttLockUser": {"heading": "<PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON> oder entsperrt den Benutzer."}, "ttLogout": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Abmeldung des Benutzers. Nach erfolgreicher Beendigung der Arbeit mit der Anwendung wird das Anmeldedialogfenster angezeigt."}, "ttMapping": {"heading": "Mapping", "body": "Gesamtüberblick über die Zuordnung der Variablen zum Lesen (R), Aufzeichnen (W) und pflichtmäßigen Aufzeichnen (M) in den einzelnen Aufgaben mit der Bearbeitungsmöglichkeit der Zuordnung."}, "ttNewCase": {"heading": "Neuer Fall", "body": "Anleg<PERSON> einer neuen Instanz des Prozesses - eines neuen Falles. Man kann aus den verfügbaren Prozessschablonen auswählen oder einen Fall ohne die im Voraus definierte Aufgabenstruktur erstellen."}, "ttNewOverview": {"heading": "Übersicht hinzufügen", "body": "Ermöglicht die Definition einer neuen Übersicht."}, "ttOrgStructure": {"heading": "Organisationsstruktur", "body": ""}, "ttParent": {"heading": "Übergeordnet", "body": "Umschalten auf einen Fall, aus dem der dargestellte Fall als ein Subprozess erstellt wurde."}, "ttPhoto": {"heading": "Fotografie", "body": "Hochladen des Fotos des Benutzers in das Benutzerpofil. Unterstütze Formate: GIF, JPG und PNG. Die Bildgröße wird automatisch angepasst."}, "ttPlans": {"heading": "Planung", "body": "Einstellung der Regeln für den automatischen einmaligen oder wiederholten Start der Prozessinstanzen – Fälle nach den vorgegebenen Parametern. Für den Zugang ist die Rolle $Administrator notwendig."}, "ttPrint": {"heading": "Druck", "body": "Erstellt eine Auflistung zum Ausdrucken."}, "ttRecalc": {"heading": "Neuberechnung", "body": "Berechnet erneut alle Variablen nach."}, "ttRedirectToPrc": {"heading": "Fall", "body": ""}, "ttResetDash": {"heading": "Reset", "body": "Stellt alle durchgeführten Änderungen zurück."}, "ttResetSearch": {"heading": "Reset", "body": "Stellt das Suchformular zurück."}, "ttRestoreTemp": {"heading": "Wiederherstellung der Schablone", "body": "Erneuert die gelöschte Schablone."}, "ttRevision": {"heading": "Revision", "body": "Ermöglicht das Hochladen einer neuen Dateiversion."}, "ttRoles": {"heading": "Rollenverwaltung", "body": ""}, "ttRunEvent": {"heading": "Starten", "body": "Aufrufen des Ereignisses in diesem Fall."}, "ttSave": {"heading": "Speichern", "body": "Speichert Änderungen und schließt das Fenster."}, "ttSaveDMSCols": {"heading": "Spalten speichern", "body": ""}, "ttSaveSettings": {"heading": "Speichern", "body": "Speichert Änderungen."}, "ttSaveTsk": {"heading": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "body": "Die halb fertige Aufgabe wird nur gespeichert und Si<PERSON> können zu dieser später zurückkehren."}, "ttSearch": {"heading": "<PERSON><PERSON>", "body": "Startet die Suche."}, "ttSendNote": {"heading": "Anmerkung hinzufügen", "body": "Ermöglicht das Hinzufügen einer neuen Anmerkung."}, "ttSetConnectionCond": {"heading": "Bedingung", "body": "Hinzufügen oder Bearbeiten der Verbindungsbedingung. Das Bearbeiten wird für die markierte Verbindung durchgeführt. Die Markierung wird durch das Klicken auf die Verbindung oder auf das Symbol der Bedingung durchgeführt."}, "ttSetDefaultDash": {"heading": "Als Default-Dashboard einstellen", "body": "Stellt die aktuelle Dashboard-Anordnung als Ausgangsanordnung ein."}, "ttShowHideBtn": {"heading": "Öffnen / Schließen", "body": "Öffnet oder schließt teilweise das Hauptmenü."}, "ttSleepCase": {"heading": "Fall in den Schlaf-<PERSON><PERSON> versetzen", "body": "Markiert den Fall als in den Schlaf-Modus versetzt. Der Fall wird weiterhin nicht mehr unter den aktiven Aufgaben abgebildet, aber bei Bedarf kann er wieder aktiviert werden und der ganze Fall kann später beendet werden."}, "ttSolve": {"heading": "Aufgabe lösen", "body": "<PERSON><PERSON><PERSON> den Dialog an, der die Lösung der zugeordneten Aufgabe nach der im Voraus definierten Schablone ermöglicht."}, "ttStatePlan": {"heading": "Zustand", "body": "Definiert den Zustand des Plans."}, "ttStatusHdr": {"heading": "Änderung des Kopfzeilenzustandes", "body": "Die Aktion wird für die markierte Kopfzeile durchgeführt. Es stehen die Zustände \"aktiv\" und \"nicht aktiv\" zur Verfügung. Die Markierung wird durch das Anklicken in der Tabelle der Schablonenkopfzeilen durchgeführt."}, "ttStatusTemp": {"heading": "Änderung des Schablonenzustandes", "body": "Die Verwaltung des Lebenszyklus der Schablone wird durch die Einstellung ihres Zustandes durchgeführt. es stehen die Zustände \"in Entwicklung\", \"aktiv\", \"nicht aktiv\" und \"gelöscht\" zur Verfügung. Die Aktion wird für die markierte Schablone durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Fallschablonen durchgeführt."}, "ttSubprocess": {"heading": "Verschachtelt", "body": "Umschalten auf einen Fall, der als ein Subprozess im Prozess des dargestellten Falles erstellt wurde."}, "ttTabsButtonMore": {"heading": "<PERSON><PERSON><PERSON>", "body": "Ö<PERSON>net weitere Möglichkeiten."}, "ttTakeTsk": {"heading": "Aufgabe übernehmen", "body": "Ermöglicht die Übernahme der Aufgabe zur Lösung."}, "ttTemps": {"heading": "Prozessschablonen", "body": "Zentrale Stelle für die Verwaltung der Prozessschablonen. Für den Zugang ist die Rolle $PowerUser notwendig."}, "ttTiming": {"heading": "Zeitplanung", "body": "<PERSON>eb<PERSON> Sie den Beginn und das Ende der Aufgabe ein."}, "ttTsks": {"heading": "Aufgaben", "body": ""}, "ttUploadSettings": {"heading": "Hochladen", "body": ""}, "ttUserSetting": {"heading": "Benutzereinstellungen", "body": "Einstellung der Kontaktinformationen des Benutzers, des Passwortes und der Benutzerpräferenzen. Der Benutzer mit der Rolle $Administrator kann ferner die Informationen über seine Organisation und Instanz der Anwendung TeamAssistant verwalten."}, "ttUsers": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "body": "Zentrale Verwaltung der Benutzer, Organisationsstruktur und Benutzerrollen. Für den Zugang ist die Rolle $Administrator notwendig."}, "ttValidation": {"heading": "Validierung", "body": "Validiert die Schablone und zeigt alle bestehenden Schleifen in der Schablone an, macht auf nicht erfüllbare Bedingungen und nicht verwendete Variablen aufmerksam."}, "ttViewFile": {"heading": "Anzeigen", "body": ""}, "ttWakeUpCase": {"heading": "Au<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttActivateCase": {"heading": "Aktivieren", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Defaulte DMS Spalten einstellen", "body": "Stellt die Zuordnung der DMS Spalten als Ausgangszuordnung ein."}, "ttResetDmsCols": {"heading": "Reset", "body": "Stellt die Zuordnung der DMS Spalten zurück."}, "ttRestoreDoc": {"heading": "Wiederherstellen", "body": "Erneuert die gelöschte Datei."}, "ttSearchHeader": {"heading": "<PERSON><PERSON>", "body": ""}, "tue": "Dienstag", "type": "<PERSON><PERSON>", "typeOfRepetition": "Typ der Wiederholung", "unassignedSolvers": "<PERSON><PERSON> den Lösern", "unassignedTaskSolvers": "<PERSON>cht zugeordnete Löser", "uncategorized": "<PERSON><PERSON> e<PERSON>uft", "unfinishedProcesses": "Unvollendete Fälle", "unknown": "Unbekannt", "unknownUser": "<PERSON><PERSON><PERSON><PERSON>", "unrestricted": "Uneingeschränkt", "unspecified": "Nicht spezifiziert", "upload": "Hochladen", "uploadFile": "<PERSON><PERSON> ho<PERSON>n", "uploadPhoto": "Foto hochladen", "uploadCsv": "CSV-<PERSON><PERSON>", "url": "URL", "urlAddress": "URL-Adresse", "urlContent": "URL-Inhalt", "use": "Verwenden", "user": "<PERSON><PERSON><PERSON>  ", "userByOwnerOfLastTask": "<PERSON><PERSON>, der vom letzten Löser der Aufgabe ausgewählt wird", "userE": "<PERSON><PERSON><PERSON>", "userFilters": "Benutzerfilter", "userLock": "<PERSON><PERSON><PERSON>", "userLockUnlockQ": "Möchten Sie wirklich den Zustand der Benutzer {{username}} ändern?", "userName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "Benutzer-ID", "userOrgStruct": "Gehört in die Organisationseinheit", "userVice": "<PERSON><PERSON><PERSON><PERSON>", "userViced": "Der Vertretene", "users": "<PERSON><PERSON><PERSON>", "usersDeleted": "Gelöschte", "validation": "Validierung", "value": "Wert", "var": "Variable", "var-": "Variable -", "varChange": "Die Änderung der Variablen wird allen Fallteilnehmern angekündigt.", "varTaskMap": "Mapping", "varTemp": "Schablone der Variablen", "variable": "Variable", "variableType": "Art der Variablen", "vars": "Variablen", "varsForMandatory": "Variablen zur pflichtmäßigen Eintragung", "varsForReading": "Variablen zum Lesen", "varsForWriting": "Variablen zur Eintragung", "vices": "Vertretungen", "viewCVFields": "Verfügbar<PERSON>", "visForOrgStrMembers": "Sichtbar für die Mitglieder der Organisationsgruppe", "visForRoleMembers": "Sichtbar für  die Benutzer mit Rolle", "headerVisForRole": "Fall sichtbar für die Rolle", "waitForNumOfInputs": "Wartet auf: (Anzahl der Eingänge)", "waitsFor": "wartet auf", "waitsForAll": "wartet auf alle", "waitsForOne": "wartet auf eine", "waitsForSending": "Wartet auf das Versenden", "waitsRunFirst": "wartet auf alle, startet die erste", "wakeUp": "Au<PERSON><PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "wed": "Mittwoch", "weekIn": "<PERSON><PERSON>e in", "weekly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "width": "Breite", "withConditions": "<PERSON><PERSON>", "withoutCond": "<PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON>", "yes": "<PERSON>a ", "zip": "PLZ", "move": "Verschieben", "alertClosing1": "die Warnung schließt sich automatisch in:", "inDocuments": "In den Dokumenten", "inVariables": "In den Variablen", "headerTask": "Kopfzeile der Aufgabe", "planName": "Name des Plans", "inBulk": "Massenweise", "confirmResetDmsColumns": "<PERSON>öchten Si<PERSON> wirklich die DMS Spalten rücksetzen?", "dmsColsUseDef": "Sie verwenden die Standardeinstellung", "dmsColsUseCust": "Sie verwenden die eigene Einstellung", "today": "<PERSON><PERSON>", "alrPlanDeleteFailed": "Der Plan konnte nicht gelöscht werden", "notRunning": "<PERSON><PERSON> lauf<PERSON>e", "alrLackOfPermsToAddTask": "Sie haben keine ausreichende Berechtigung für das Hinzufügen des Aufgabe.", "dragTable": "<PERSON><PERSON><PERSON> ve<PERSON>", "alrDownloadCsvListFailed": "Die CSV-<PERSON><PERSON> konnten nicht heruntergeladen werden.", "alrCsvUploadWrongExtension": "Laden Sie nur Dateien mit der Dateiendung *.csv hoch", "addToFav": "Den Favoriten hinzufügen", "renameItem": "Umbenennen", "removeFromFav": "Von den Favoriten entfernen?", "alrAddedToFav": "Den Favoriten hinzugefügt", "alrRemovedFromFav": "Aus Favoriten entfernt.", "tskSetAssignDues": "Stellen Sie die zeitliche Beschränkung für die Aufgabe ein", "isNot": "ist nicht", "alrTskScheduling": "Einplanen der Aufgabe…", "alrFavouritesPageExist": "Diese Seite ist bereits in den Favoriten.", "alrFavouritesActionExist": "Diese Aktion ist bereits in den Favoriten.", "alrFavouriteRenamed": "Der Favorit wurde umbenannt.", "autoFit": "Autofit", "passwordIsShort": "Das Passwort ist zu kurz.", "changeAttrComplCases": "Änderung der Attribute der beendeten Fälle", "iterateOverVars": "Über Variablen iterieren", "nrOfDecimalDigits": "Anzahl der Dezimalstellen", "onlyNumbers": "<PERSON><PERSON>", "maxNumberOfDecimals": "Die maximale Anzahl der Dezimalstellen ist", "alrInsertCsv": "CSV-<PERSON><PERSON> e<PERSON>fügen.", "addBefore": "Hinzufügen vor", "moveBefore": "Verschieben vor", "administration": "Verwaltung", "ttAdministration": {"heading": "Verwaltung", "body": ""}, "alrLogsLoadFailed": "Die Logdateien konnten nicht eingelesen werden.", "logs": "Logdateien", "message": "Nachricht", "useCompatibleTempl": "Kompatible Schablone verwenden", "overwriteExistTempl": "Bestehende Schablone überschreiben", "addNewTempl": "Neue Schablone hinzufügen", "import": "Import", "export": "Export", "confirmExportAllTempl": "Alle Schablonen exportieren?", "confirmExportSelTempl": "Ausgewählte Schablone exportieren?", "newLogs": "Neue Logdateien", "container": "Container", "contents": "Inhalt", "confirmRemoveDialog": "<PERSON><PERSON><PERSON> entfernen {{variable}}?", "allMyCases": "Alle meine Fälle", "maintenanceMsg": "<PERSON>s werden planmäßige <span style=\"color: {{color}};\">Wartungsarbeiten</span> durchgeführt", "alrMaintenanceMsg": "Es werden planmäßige Wartungsarbeiten durchgeführt., versuchen Sie es bitte später.", "alrAttachDownloadLackOfPerms": "Sie haben keine ausreichende Berechtigung zum Herunterladen oder die Datei wurde nicht gefunden.", "unableToConnect": "Die Verbindung zum Server ist fehlgeschlagen", "tryLater": "Versuchen Sie es bitte später oder kontaktieren Sie den Administrator.", "enableTaskDelegation": "Delegierung der Aufgabe freigeben", "enableRejectTask": "Ablehnung der Aufgabe freigeben", "confirmRejectTask": "<PERSON>öchten Sie wirklich die Aufgabe ablehnen?", "rejectTask": "Aufgabe ablehnen", "delegateTask": "Delegieren", "alrRejectingTask": "Ablehnung der Aufgabe…", "alrTaskRejected": "Die Aufgabe wurde abgelehnt.", "alrTaskRejectFailed": "Die Aufgabe konnte nicht abgelehnt werden.", "alrTaskDelegating": "Delegierung der Aufgabe…", "alrTaskDelegated": "Die Aufgabe wurde an den Benutzer delegiert:", "alrFailedTaskDelegate": "Die Aufgabe konnte nicht delegiert werden.", "delegateOnUser": "An den Benutzer delegieren", "plnAssignmentCond": "<PERSON>n Si<PERSON> im Feld \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" keinen Benutzer auswählen, wird die Liste der Initiatoren erst mit der Auswertung der einschränkenden Bedingungen zum Zeitpunkt des Planstartes erstellt", "alrUserFiltersSettingsFailed": "Die Einstellung der Benutzerfilter konnte nicht gespeichert werden.", "general": "Allgemeine", "alrUserPhotoLoadFailed": "Das Benutzerfoto konnte nicht eingelesen werden.", "publicDynTable": "Öffentliche dynamische Tabelle", "isFullIndexed": "In der Suche", "datetimeIndexed": "Indexiert (wann)", "toIndex": "Wartet auf das Indizieren", "toReindex": "Wartet auf das Reindizieren", "solverChanged": "<PERSON> Löser wurde in den {{count}} Aufgaben geändert.", "changeSolverFailed": "Der Löser konnte nicht geändert werden.", "alrTikaParsingFailed": "<PERSON><PERSON> ist jedoch ein Fehler aufgetreten.", "alrIndexingFailed": "Das Indexieren des Dokumentes ist fehlgeschlagen.", "alrTikaNotRunning": "Die Dienstleistung für die Metadatenausbeute ist nicht verfügbar.", "alrIndexingServiceNotRunning": "Die Dienstleistung für das Indizieren der Dokumente ist nicht verfügbar.", "alrFulltextNotSet": "Fulltext ist nicht eingestellt.", "asc": "Aufwärts", "desc": "Abwärts", "restore": "Wiederherstellen", "alrLogosLoadFailed": "Die Logos konnten nicht eingelesen werden.", "indexedDocsCount": "in insgesamt {{count}} Dokumenten", "alrIndexedCountLoadFailed": "Die Fulltext-Suche ist momentan nicht verfügbar.", "searchAll": "Alles durchsu<PERSON>", "searchActual": "Nur aktuelle", "runIndexing": "Indexieren", "alrDocumentIndexing": "Indexieren des Dokumentes...", "alrDocumentIndexed": "Das Dokument wurde indexiert und es kann in der Suche gefunden werden.", "alrDocumentIndexedWithMinMetadata": "Das Dokument wurde indexiert.", "alrDocumentIndexingFailed": "Die Indexierung des Dokumentes ist fehlgeschlagen.", "changingUserProfileForbidden": "Die Änderung des Benutzerprofils ist verboten.", "uploadingPhotoForbidden": "Das Hochladen des Fotos ist verboten.", "alrValidationCalcError": "Fehler in der Validierung der Berechnungen", "maintenance": "Wartung", "maintenanceActivate": "Wartung aktivieren", "maintenanceInfoText": "Der Beginn und das Ende werden den Benutzern nach der Aktivierung der Wartung angezeigt. ", "maintenanceMode": "Wartungsmodus", "alrAvailableCalcFailed": "Die verfügbaren Berechnungen konnten nicht eingelesen werden.", "alrFillDataForSearch": "Füllen Sie bitte die Parameter für die Suche aus.", "youAreHere": "Sie befinden sich hier", "invalidDate": "Ungültiges Datum", "alrInvalidFileFormat": "Ungültiges Dateiformat", "alrEnter3characters": "Bitte mindestens drei Zeichen eingeben.", "changeCaseOwner": "Änderung des Eigentümers", "actualCaseOwner": "Bestehender Eigentümer", "newCaseOwner": "<PERSON><PERSON><PERSON>", "alrCaseOwnerChanged": "Der Falleigentümer wurde geändert.", "alrChangeCaseOwnerFailed": "Der Falleigentümer konnte nicht geändert werden.", "alrCsvSaving": "Hochladen der CSV-Datei…", "alrCsvSaveFailed": "Die CSV-<PERSON><PERSON> konnte nicht hochgeladen werden.", "alrCsvSaved": "Die CSV-<PERSON><PERSON> wurde hoch<PERSON>aden.", "allTemplates": "<PERSON><PERSON>  ", "specifyCaseIds": "ID der Fälle eingeben", "caseIds": "ID der Fälle  ", "caseId": "ID des Falls", "separBySemicolon": "durch Semikolon getrennt", "alrAddCaseIds": "Bitte die ID der Schablonen eingeben", "headers": "Kopfzeilen", "header": "Kopfzeile", "defaultHeaderName": "Standardname der Kopfzeile", "headerName": "Name der Kopfzeile", "addHeader": "Kopfzeile hinzufügen", "editHeader": "Kopfzeile bearbeiten", "templateName": "Name der <PERSON>", "rolesExecRightsText": "<PERSON><PERSON> hinzufügen, die die Fälle starten können", "orgUnitsExecRightsText": "Organisationseinheiten hinzufügen, die die Fälle starten können", "selectedHeader": "ausgewählte Kopfzeile", "alrHeaderDeleted": "Die Kopfzeile wurde gelöscht!", "alrHeaderDeleteFailed": "Die Kopfzeile konnte nicht gelöscht werden.", "alrHeaderSaveFailed": "Die Kopfzeile konnte nicht gespeichert werden.", "alrHeaderSaved": "Die Kopfzeile wurde gespeichert.", "alrHeadersLoadFailed": "Die Daten der Kopfzeile konnten nicht eingelesen werden.", "identificator": "Code der Kopfzeile", "includeDataSimilarProcesses": "Daten in alle ähnlichen Prozesse einbeziehen", "confirmCopyCv": "Möchten Sie wirklich die ausgewählte Übersicht kopieren?", "alrCreatingCopyCv": "Kopieren der Übersicht…", "alrCvCopied": "Die Übersicht wurde kopiert.", "alrCopyCvFailed": "Die Übersicht konnte nicht kopiert werden.", "copyingTemplate": "<PERSON><PERSON><PERSON>lone", "alrCheckTempImportFailed": "Die Kontrolle des Schablonenimportes ist fehlgeschlagen.", "warnings": "<PERSON><PERSON><PERSON>", "missingEventsFiles": "Fehlende Ereignisdateien", "missingEventsFilesText": "Datei {{- file}} aus dem Ereignis {{- event}} nicht gefunden", "printsOfTemplates": "Drucke der Schablonen", "printsOfTemplatesText": "<PERSON><PERSON> bitte den Druck {{- print}} aus der Schablone {{- template}}. Wert: {{- value}}", "dupliciteTaskNames": "Dupliziernamen der Aufgaben", "dupliciteTaskNamesText": "In der Schablone {{- template}} befindet sich mehrmals eine gleichnamige Aufgabe {{- task}} {{- taskId}}, dies verursacht einen Zerfall der Links!", "dynTableUsed": "Es wurde die dynamische Tabelle angewendet", "suspiciousCalc": "Verdächtige Berechnungen", "suspiciousCalcText": "Die Möglichkeit der fehlenden Rolle/der fehlenden Organisation/des fehlenden Benutzers in der Berechnung {{- calc}}", "missingEvents": "<PERSON><PERSON><PERSON><PERSON> Ereignisse", "missingEvent": "<PERSON>s fehlt das Ereignis", "wrongMappingDomains": "Falsches Mapping der Domänen", "wrongMappingDomainsText": "Die Beschreibung der Aufgabe {{- task}} aus der Schablone {{- template}} enthält einen falschen Namen der Domäne, die aktuelle Domäne ist {{-actDom}}", "taskDescription": "Beschreibung der Aufgabe", "eventsUrl": "URL der Ereignisse", "eventsUrlText": "Möglicher Fehler im URL des Ereignisses {{- event}}, die aktuelle Domäne ist {{- actDom}}", "param": "Parameter", "alrServiceNotForTable": "Die Daten dieser Dienstleistung sind nicht für die Anzeige in der Tabelle geeignet.", "alrServiceDataFailedLoad": "Die Daten der Dienstleistung konnten nicht eingelesen werden.", "alrServiceNoData": "Die Dienstleistung enthält keine Daten.", "tableColumns": "Spalten der Tabelle", "datetime": "Datum und Uhrzeit", "exactDatetime": "Genaues Datum und Uhrzeit", "dashRestNoColumns": "Es sind keine Spalten eingestellt – diese sind in der Einstellung des Containers auszuwählen", "loadService": "Dienstleistung einlesen", "useCompatibleRole": "Kompatible Rolle verwenden", "overwriteExistRole": "Bestehende Rolle überschreiben", "addNewRole": "Neue Rolle hinzufügen", "templateImportFailed": "Der Import der Schablone ist fehlgeschlagen.", "templateImport": "Import <PERSON>", "templateImportNoData": "<PERSON>s wurden keine Daten für den Import der Schablone gefunden.", "variableImportNoData": "<PERSON>s wurden keine Daten für den Import der Variablen gefunden.", "ttTemplateImport": {"heading": "Import <PERSON>", "body": "Nach der Auswahl der Datei mit definierter Schablone oder mit mehreren definierten Schablonen erfolgt ihr Hochladen."}, "showUnfinishedProcesses": "Unvollendete Fälle anzeigen", "expMaintenanceEnd": "Voraussichtliches Ende der Wartung", "alrScriptSaveFailed": "Das Skript konnte nicht gespeichert werden.", "editScript": "<PERSON><PERSON><PERSON>t bearbeiten", "addScript": "Skript hinzufügen", "alrRunScript": "Skript starten...", "alrScriptCompleted": "<PERSON> Skript wurde beendet.", "alrFailedScriptStart": "Das Skript konnte nicht gestartet werden.", "alrScriptDocsLoadFailed": "Die Skript-Dokumentation konnte nicht eingelesen werden.", "alrScriptLoadFailed": "Die Skripts konnten nicht eingelesen werden.", "switchAdminUser": "Umschalten Admin/Benutzer", "ttSwitchAdminUser": {"heading": "Umschalten Admin/Benutzer", "body": ""}, "ttSwitchViewport": {"heading": "Umschalten Anzeige Handy/PC", "body": ""}, "alrEventDataLoadFailed": "Die Daten des Ereignisses konnten nicht eingelesen werden.", "alrEventRuleDataLoadFailed": "Die Daten der Ereignisregeln konnten nicht eingelesen werden.", "cancellation": "Stornierung", "tTaskAutoCancellCaption": "Die Aufgabe wird automatische storniert wenn", "codeMirrorHelp": "Um die Hilfe anzuzeigen, im Editor klicken und Crtl + Leertaste drücken.", "codeMirrorHelpJs": "Um die Liste aller Funktionen anzuzeigen, auf <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a> klicken", "addEvent": "<PERSON><PERSON><PERSON><PERSON>", "editEvent": "<PERSON><PERSON><PERSON><PERSON> bear<PERSON>", "term": "Frist", "columnOrder": "Reihenfolge der Spalte", "alrLoadEventsButtonsFailed": "Die Tasten konnten nicht eingelesen werden", "showButtonsCol": "Spalte mit Aktionen anzeigen", "button": "Taste", "enableButtonInTasks": "Als Taste in der Aufgabenübersicht anzeigen", "alrEventDoesntExist": "Das gewünschte Ereignis existiert nicht.", "alrEventRuleSaveFailed": "Die Ereignisregel konnte nicht gespeichert werden.", "variableNames": "Namen der Variablen", "fsEvent": "<PERSON><PERSON><PERSON><PERSON>", "alrEventDeleteFailed": "Das Ereignis konnte nicht gelöscht werden.", "fsRule": "Regel", "alrRuleDeleteFailed": "Die Regel konnte nicht gelöscht werden.", "alrRuleStatusChangeFailed": "Der Zustand der Regel konnte nicht geändert werden.", "ruleActivateDeactivateQ": "Möchten Si<PERSON> wirklich den Zustand der Regel ändern?", "docUploadedPrivate": "Das Dokument wird als privates Dokument hochgeladen", "fileOwner": "<PERSON><PERSON><PERSON>nt<PERSON><PERSON>", "planOk": "In Ordnung", "userNotAuthToStartTempl": "Der Benutzer hat keine Berechtigung zum Starten der Schablone", "planStartDate": "Gestartet", "useCompatibleEvent": "Kompati<PERSON> E<PERSON>ign<PERSON> verwen<PERSON>", "overwriteExistEvent": "Bestehendes Ereignis überschreiben", "addNewEvent": "Neues Ereignis hi<PERSON>", "useCompatibleUser": "Kompatiblen Benutzer verwenden", "overwriteExistUser": "Bestehenden Benutzer überschreiben", "addNewUser": "Neuen Benutzer hinzufügen", "useOnlyFutureDates": "Nur künftiges Datum", "alrGenerateHtmlFailed": "Die Generierung von HTML ist fehlgeschlagen.", "alrNoPermsToAddNoteInVice": "Sie sind nicht berechtigt, die Anmerkung in der Vertretung hinzuzufügen.", "alrNoPermsToAddDocInVice": "Sie sind nicht berechtigt, das Dokument in der Vertretung hinzuzufügen.", "current": "Aktuelle", "indexation": "Indexierung", "attemptToRestoreConnection": "Versuch um die Wiederherstellung der Verbindung in", "loginWillExpire": "Ablauf der Anmeldungsfrist in ", "unsavedDataWillBeLost": "Nicht gespeicherte Daten gehen verloren.", "alrFileSaveLikeAttachViceError": "Sie haben keine Berechtigung zur Speicherung des Druckes als eines Falldokumentes in der Vertretung!", "alrFileSaveLikeAttachStoreError": "Der Druck zu den Falldokumenten konnte nicht hochgeladen werden.", "useCompatibleUnit": "Kompatible Organisationseinheit verwenden", "overwriteExistUnit": "Bestehende Organisationseinheit überschreiben", "addNewUnit": "Neue Organisationseinheit hinzufügen", "addNewDynTable": "Neue dynamische Tabelle hinzufügen", "useCompatibleDynTable": "Kompatible dynamische Tabelle verwenden", "addNewCalcScript": "Neues Skript hinzufügen", "useCompatibleCalcScript": "Kompatibles Skript verwenden", "enterDiffNameRoot": "<PERSON>te einen anderen Namen als Root eingeben.", "ttTemplatesExport": {"heading": "Export der Schablone", "body": "Export der markierten Schablone in die Datei. Es können der Name und der Ort der exportierten Datei gewählt werden. Die Aktion wird für die markierte Schablone durchgeführt. Die Markierung wird durch das Anklicken in der Tabelle der Fälle durchgeführt."}, "ttTemplatesExportAll": {"heading": "Export aller <PERSON>", "body": "Export aller aktuell angezeigten Schablonen in die Datei. Es können der Name und der Ort der exportierten Datei gewählt werden. Die Auswahl der Schablonen kann durch eine geeignete Einstellung der Filterbedingungen eingeschränkt werden."}, "exportAll": "Alles exportieren", "noTemplatesToExport": "<PERSON><PERSON> zum Export.", "skip": "Überspringen", "ttSkipTemplate": {"heading": "Schablone überspringen", "body": "Überspringt den Import der aktuellen Schablone und zeigt die nächste an."}, "alrInvalidImportData": "Ungültige Daten für den Import", "alrUsersNotLoaded": "Der Benutzer konnte nicht eingelesen werden.", "caseOverview": "Fallübersicht", "alrRolesNotLoaded": "Die Rollen konnten nicht eingelesen werden.", "changeLang": "Sprache ändern", "reactivatesPlan": "reaktiviert den Plan", "alrOrgUnitsNotLoaded": "Die Organisationseinheiten konnten nicht eingelesen werden.", "refreshPage": "<PERSON>ite er<PERSON>n", "stayLogged": "Ang<PERSON><PERSON><PERSON> bleiben", "showTime": "Zeit in den Übersichten anzeigen", "managerIn": "Manager in {{orgUnit}}", "usageStats": "Verwendungsstatistiken", "month": "<PERSON><PERSON>", "alrUsageStatsLoadFailed": "Die Verwendungsstatistiken konnten nicht eingelesen werden.", "accessLog": "<PERSON><PERSON><PERSON><PERSON>", "durationInMs": "Länge der Anforderung (ms)", "task": "Aufgabe  ", "operation": "Operation", "active_users": "Anzahl der aktiven Benutzer", "active_template_processes": "Anzahl der aktiven Prozesse", "active_headers": "Anzahl der aktiven Kopfzeilen", "active_users_able_to_create_a_process": "<PERSON><PERSON><PERSON> der aktiven Benutzer, die einen Prozess starten können", "users_that_solved_a_task": "<PERSON><PERSON><PERSON>utz<PERSON>, die mindestens eine Aufgabe gelöst haben", "solvers_or_can_create_a_process": "<PERSON><PERSON><PERSON> <PERSON>ö<PERSON>, die eine Aufgabe gelöst haben oder einen Prozess starten können ", "mobile_app_paired_users": "Anzahl der gekoppelten mobilen App-Benutzer", "calculationsLogs": "Berechnungslogs", "translatedScript": "Übersetztes Skript", "originalScript": "Originelles Skript", "tskId": "Aufgabe-ID", "alrCalculationsDocsLoadFailed": "Die Dokumentation zu den Berechnungen konnte nicht eingelesen werden.", "alrCalculationsValidationFailed": "Die Validierung der Berechnungen ist fehlgeschlagen.", "linkPriority": "Priorität der Verbindung", "dateFormat": "TT.MM.JJJJ", "alrConvertErrorJsonNeon": "Fehler bei der Umsetzung Json -> Neon.", "alrInvalidData": "Ungültige Daten.", "sharedVar": "Geteilte Variable", "guide": "Leitfaden", "guideFs": "Leitfaden", "guides": "Leitfaden", "alrGuidesLoadFailed": "Der Leitfaden konnte nicht eingelesen werden.", "language": "<PERSON><PERSON><PERSON>  ", "default": "<PERSON><PERSON><PERSON>", "next": "Nächste", "previous": "<PERSON><PERSON><PERSON><PERSON>", "targetElementNotFound": "Das Zielelement wurde nicht gefunden", "documentation": "Dokumentation", "matchesRegular": "<PERSON><PERSON><PERSON><PERSON>t nicht die Regel", "secondAllowedValues": "zugelassene Werte; 0 ist Beginn der Minute", "everySec": "jede <PERSON>", "listOfSec": "Liste der Sekunden; z.B. 0,30 wird die 0. und 30. <PERSON>kunde sein", "rangeOfSec": "Se<PERSON><PERSON>bereich; z.B. 0–5 sind die 0., 1., 2., 3., 4. und 5. Sekunde  (es kann auch eine Liste der Bereiche von 0–5,30–35 spezifiziert werden)", "slashSec": "Vielfaches; z.B. */5 bedeutet alle 5 Sekunden, 0–30/2 bedeutet alle 2 Sekunden zwischen der 0. und 30. Sekunde", "minuteAllowedValues": "zugelassene Werte; 0 ist Beginn der Stunde", "everyMin": "jede <PERSON>", "listOfMin": "Liste der Minuten; z.B. 0,30 wird die 0. und 30. Minute sein", "rangeOfMin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; z.B. 0–5 sind die 0., 1., 2., 3., 4. und 5. Minute  (es kann auch eine Liste der Bereiche von 0–5,30–35 spezifiziert werden)", "slashMin": "Vielfaches; z.B. */5 bedeutet alle 5 Minuten, 0–30/2 bedeutet alle 2 Minuten zwischen der 0. und 30. Minute", "hourAllowedValues": "zugelassene Werte; 0 ist Mitternacht", "everyHour": "jede <PERSON>", "listOfHour": "Liste der Stunden; z.B. 0,12 wird die 0. und 12. Stund<PERSON> sein", "rangeOfHour": "Stundenbereich; z.B. 19–23 sind die Stunden 19, 20, 21, 22 und 23 (es kann auch die Liste der Bereiche von 0–5,12–16 spezifiziert werden)", "slashHour": "Vielfaches; z.B. */4 bedeutet alle 4 Stunden, 0–20/2 bedeutet alle 2 Stunden zwischen der 0. und 20. Stunde", "dayAllowedValues": "zugelassene Werte", "everyMonthDay": "jeden Tag im Monat", "listOfDay": "Liste der Tage; z.B. 1,15 wird der 1. und 15. Tag im Monat sein", "rangeOfDay": "<PERSON><PERSON><PERSON> der Tage; z.B. 1–5 sind der 1., 2., 3., 4. und 5. Tag  (es kann auch die Liste der Bereiche von 1–5,14–30 spezifiziert werden)", "slashDay": "Vielfaches; z.B. */4 bedeutet alle 4 Tage, 1–20/2 bedeutet alle 2 Tage zwischen dem 1. und 20. Tag des Monats", "allowedValues": "zugelassene Werte", "everyMonth": "jede<PERSON>", "listOfMonth": "Liste der Monate; z.B. 1,6 wird der Januar und <PERSON><PERSON> sein", "rangeOfMonth": "Bereich der Monate; z.B. 1–3 sind <PERSON> Januar, <PERSON><PERSON><PERSON> und März (es kann auch die Liste der Bereiche von 1–4,8–12 spezifiziert werden)", "slashMonth": "Vielfaches; z.B. */4 bedeutet alle 4 Monate, 1–8/2 bedeutet alle 2 Monate zwischen dem Januar und August", "weekAllowedValues": "zugelassene Werte; 0=Sonntag, 1=Montag, 2=Dienstag, 3=Mittwoch, 4=Donnerstag, 5=Freitag, 6=Samstag", "everyWeekDay": "jeden Tag in der Woche", "listOfWeekDay": "Liste der Tage; z.B. 1,5 wird der Montag und Freitag sein", "rangeOfWeekDay": "Bereich der Tage; z.B. 1–5 sind der Montag, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Donnerstag und Freitag (es kann auch die Liste der Bereiche von 0–2,4–6 spezifiziert werden)", "slashWeek": "Vielfaches; z.B. */4 bedeutet alle 4 Tage, 1–5/2 bedeutet alle 2 Tage zwischen dem Montag und Freitag", "contrab": "<PERSON>ron – Feld {{variable}}", "cSecond": "Sekunden ", "cMinute": "Minuten ", "cHour": "Stunden ", "cDay": "Tage", "cMonth": "Monate", "cWeekDay": "Tage pro Woche", "seconds": "Sekunden", "minutes": "Minuten", "hours": "Stunden", "days": "Tage", "weeks": "<PERSON><PERSON><PERSON>", "socketOk": "Der Tabelle stehen aktuelle Daten zur Verfügung", "socketBroken": "Wiederherstellung der Verbindung für die kontinuierliche Aktualisierung der Daten", "newTask": "Neue Aufgabe", "report": "Report", "ttCaseReport": {"heading": "Report", "body": ""}, "usersRights": "Benutzerrechte", "visPerRole": "Sichtbarkeit in der Rolle", "manualEvents": "<PERSON><PERSON>", "noTasks": "<PERSON><PERSON> Aufgaben", "emptyFavs": "Favoritenliste ist leer", "crons": "Crons", "cronsHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redirBefStart": "<PERSON>or dem <PERSON> umleiten zu", "lastRun": "Letzter Start", "nextRun": "Nächster Start", "syntax": "Syntax", "alias": "<PERSON><PERSON>", "stop": "Stopp", "restart": "<PERSON><PERSON>", "restartCronProcess": "Kontext neustarten", "ttRestartCron": {"heading": "<PERSON><PERSON>", "body": ""}, "ttRestartCronProcess": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttResetCron": {"heading": "<PERSON><PERSON>", "body": ""}, "ttRunCron": {"heading": "<PERSON><PERSON> starten", "body": ""}, "ttStopCron": {"heading": "Stopp", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "<PERSON> C<PERSON> wurde gestoppt.", "alrCronStopFailed": "Die Anforderung an die Cron-Einstellung ist fehlgeschlagen.", "alrCronRunning": "<PERSON> Cron wurde gestartet.", "alrCronRunFailed": "Der Cron-Start ist fehlgeschlagen.", "alrCronReset": "Der Cron wurde in den Default-Zustand zurückgesetzt.", "alrCronResetFailed": "Die Anforderung an das Cron-Zurücksetzen ist fehlgeschlagen.", "alrCronRestart": "Der Cron wurde neugestartet.", "alrCronRestartFailed": "Die Anforderung an den Cron-Neustart ist fehlgeschlagen. ", "alrCronUpdated": "Der Cron wurde erfolgreich gespeichert.", "alrCronUpdateFailed": "Die Anforderung an das Cron-Update ist fehlgeschlagen. ", "confirmRunCronDialog": "Möchten Sie wirklich den ausgewählten Cron starten?", "confirmStopCronDialog": "Möchten Sie wirklich den ausgewählten Cron stoppen?", "confirmResetCronDialog": "Wollen Sie cron wirklich auf die Werkseinstellungen zurücksetzen?", "confirmRestartCronDialog": "Möchten Sie den ausgewählten Cron wirklich neustarten?", "confirmUpdateCronDialog": "Möchten Si<PERSON> wirklich den Cron-Status ändern?", "alrProcessRestart": "Der Cron-Pozess wurde neugestartet!", "alrProcessRestartFailed": "Die Anforderung an den Neustart des Prozesses ist fehlgeschlagen.", "confirmRestartProcessDialog": "Möchten Sie wirklich den ganzen Cron-Prozess neustarten? <PERSON><PERSON><PERSON>t, es erfolgt der komplette Neustart aller Crons und des ganzen Kontextes. ", "cronParams": "Parameter", "alrPresetLogFiltersLoadFailed": "Die voreingestellten Logs-Filter konnten nicht eingelesen werden.", "timeRange": "Zeitlicher Rahmen", "presetFilters": "Voreingestellte Filter", "params": "Parameter", "authentication": "Authentisierung", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Parametername", "authMethod": "Authentisierungsmodul", "taskAlreadyEdited": "Die Aufgabe wird bereits von einem anderen Benutzer bearbeitet.", "taskEditedByAnotherUser": "Die Aufgabe wird von einem weiteren Benutzer bearbeitet.", "tempAlreadyEdited": "Die Schablone wird bereits von einem anderen Benutzer bearbeitet.", "tempEditedByAnotherUser": "Die Schablone wird von einem weiteren Benutzer bearbeitet.", "test": "Test", "notInRightormat": "Nicht im richtigen Format", "ttTableExportExcel": {"heading": "Export der Tabelle", "body": "Exportiert die Tabelle in die xlsx-Datei"}, "ttTableExportCsv": {"heading": "Export der Tabelle", "body": "Exportiert die Tabelle in die csv-Datei"}, "searchInSuspended": "Auch in den im Schlaf-Modus befindlichen Fällen suchen", "alrScriptDocsFailed": "Die Dokumentation zum Skript konnte nicht gespeichert werden.", "currentlyRunning": "Aktuell laufend", "onStart": "<PERSON><PERSON>", "onEnd": "<PERSON>", "onHand": "<PERSON><PERSON>", "onRecalc": "<PERSON><PERSON>", "onPull": "Vor der Übernahmen", "yesterday": "Gestern", "tomorrow": "<PERSON><PERSON>", "replyRecipient": "Empfänger für die Antwort", "bcRecipient": "Empfänger der Blindkopie", "copyRecipient": "Empfänger der Kopie", "emptyHe": "<PERSON><PERSON>", "archivedLogs": "Archivierte Logs", "basicMode": "<PERSON><PERSON><PERSON><PERSON>", "expertMode": "Expertenmodus", "ttBasicMode": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Blendet einige Positionen oder Möglichkeiten im Formular aus."}, "ttExpertMode": {"heading": "Expertenmodus", "body": "Zeigt ausgeblendete Positionen oder Möglichkeiten im Formular an."}, "helpOverviewFolder": "Die Übersicht kann in die Verzeichnisstruktur mit Hilf<PERSON> von Bruchstrichen aufgenommen werden<br /><i>(z.<PERSON>. Rechnungen/Alle empfangenen Rechnungen)</i>", "helpOverviewIncludeSimilar": "<PERSON><PERSON>n werden auch Fälle aus anderen Schablonenkopfzeilen angezeigt.", "helpOverviewSysVars": "Die markierten (sys) Felder sind Systemfelder, die ein Bestandteil jedes Prozesses sind.", "customization": "Anpassung", "elementColor": "Farbe des Elementes", "fontColor": "Schriftfarbe", "fontSize": "Schriftgröße", "bold": "Fettschrift", "cursive": "Kursivschrift", "off": "Aus", "toPlan": "Planen", "alrMaintenanceComing": "Um {{time}} beginnt die planmäßige Wartung des Systems. Speichern Sie bitte Ihre Arbeit.", "timeoutHMS": "Timeout: (hh:mm:ss)", "eventw": "Auf dieses Ereignis wartet die Aufgabe \"{{task}}\" aus der Schablone\"{{template}}\"", "waitsForEventTip": "Der Fall wartet auf das Ereignis: \"{{event}}\"", "copyToMultiinstances": "In Multiinstanzen kopieren", "showAsPreview": "Ansicht anzeigen", "alrPreviewAttachmentsFailed": "Die Anzeige der Ansicht ist fehlgeschlagen", "alrPreviewAttachmentsWrongFormat": "Die Anzeige der Ansicht ist fehlgeschlagen - nicht unterstütztes Dateiformat", "previewNotAvailable": "Eine Vorschau des Dokuments ist aufgrund des Dokumenttyps nicht möglich.", "configuration": "Konfiguration", "values": "<PERSON><PERSON>", "defaultValues": "Ausgangswerte", "ttSubscribeCv": {"heading": "Übersicht beziehen", "body": "Die ausgewählte Übersicht wird Ihnen jeden Werktag in der vorgegebenen Zeit per E-Mail zugestellt."}, "subscribe": "<PERSON><PERSON><PERSON>", "time": "Uhrzeit", "externalLang": "Extern Sprache", "hdrStatusQ": "Möchten Si<PERSON> wirklich den Zustand der Kopfzeile ändern?", "small": "<PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "large": "Große", "alrTemplTsksLoadFailed": "Die Aufgaben der Schablone konnten nicht eingelesen werden.", "applyInTasks": "In den Aufgaben verwenden", "caseStatuses": "Fallzustände", "statuses": "Zustände", "Manuals": "<PERSON><PERSON><PERSON><PERSON>", "forReading": "<PERSON><PERSON>", "forReadWrite": "Zum Lesen sowie zur Eintragung", "addVersion": "Neue Version", "size": "Größe", "prevWorkDay": "Voriger Arbeitstag", "ttCreateTempVersion": {"heading": "Neue Schablonenversion erstellen", "body": ""}, "version": "Version", "alrTempVersionsLoadFailed": "Die Schablonenversionen konnten nicht eingelesen werden.", "alrChangeTempVersionFailed": "Die Schablonenversion konnte nicht geändert werden.", "alrCreateTempVersionFailed": "Die Erstellung der neuen Schablonenversion ist fehlgeschlagen.", "confirmCreateTempVersion": "<PERSON><PERSON>cht<PERSON> Si<PERSON> wirklich eine neue Schablonenversion erstellen?", "applyInAllTasks": "In allen Aufgaben verwenden", "mandatoryVar": "Pflichtvariable", "emptyRequiredVarMessage": "Oh, leere Pflichtvariable", "duration": "<PERSON><PERSON><PERSON><PERSON>", "alrDynConditionsFailed": "Die Aufgabe kann nicht erfüllt werden. Versuchen Sie bitte die Seite zu erneuern oder kontaktieren Sie den Administrator oder Helpdesk.", "caseActivation": "<PERSON>leg<PERSON> des Falles", "average": "Durchschnitt", "performanceLogs": "Leistungslogs", "displayingOverview": "Anzeige der Übersicht", "taskSolve": "Erfüllung der Aufgabe", "displayingCO": "Anzeige von CASE OVERVIEW", "printCreation": "Druckerstellung", "entityId": "Instanz-ID", "copyTask": "Kopieren der Aufgabe", "checkProcessCompletion": "Kontrolle der Aufgabenfertigstellung", "findingSolver": "Suchen des Lösers", "publicFiles": "Öffentliche Dateien", "usage": "<PERSON><PERSON><PERSON><PERSON>", "serviceConsole": "Servicekonsole", "selectAll": "Alles markieren", "logos": "Logos", "overviewWithTasks": "Übersicht mit Aufgaben", "printIsReady": "Der Druck ist vorbereitet", "alrChangelogLoadFailed": "Das Einlesen von Changelog ist fehlgeschlagen.", "inJs": "<PERSON><PERSON>", "ttCopyDtDefinition": {"heading": "Definition der Tabelle kopieren", "body": "Ko<PERSON>rt die Definition der markierten dynamischen Tabelle."}, "confirmCopyTableDefinition": "<PERSON>öchten Sie wirklich die Definition der Tabelle kopieren?", "alrCopying": "<PERSON><PERSON><PERSON><PERSON>", "alrCopyFailed": "Der Kopiervorgang ist fehlgeschlagen.", "fallback": "Fallback", "syncEnabled": "Synchronisierung", "systemGuideNote": "Der Inhalt der Systemleitfäden kann nicht geändert werden. Falls Si<PERSON> einen anderen Inhalt anzeigen möchten, stellen Sie die Aktivität des Systemleitfadens auf nicht aktiv ein und kopieren Sie seinen Inhalt in einen neuen Leitfaden. ", "alrAnotherUserLogged": "Im anderen Fenster ist ein anderer Benutzer angemeldet!", "userLocked": "Der Benutzer ist gesperrt", "visInternalUserOnly": "Nur für interne Benutzer sichtbar", "showSelectedOnly": "Nur ausgewählte anzeigen", "clickToSelect": "Klicken um die Auswahl durchzuführen", "restrictRoleAssignment": "Die Zuordnung der Rolle zur Rolle einschränken", "restrictions": "Einschränkung", "restrictTableHandling": "Den Umgang mit der Tabelle einschränken", "toRole": "<PERSON><PERSON>", "inCalcToHeader": "In den Berechnungen auf die Kopfzeile", "loginBtnColor": "Farbe der Anmeldetaste", "certificates": "Zertifikate", "certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateVar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tspSources": "Zeitstempel", "tspSource": "Zeitstempel", "confirmExpandDynRowsNewAssignments": "<PERSON><PERSON><PERSON><PERSON>, neue Z<PERSON>rdnung! Bei den Variablen sind keine Achsen eingestellt. Möchten Sie alle dynamischen Zeilen ausdehnen?", "confirmExpandDynRows": "Möchten Si<PERSON> wirklich alle dynamischen Zeilen ausdehnen?", "expandDynRows": "Dynamische Zeilen ausdehnen", "visible": "<PERSON><PERSON><PERSON> ", "cvcDbColumn": "Quellspalte", "cvTableSource": "Quelltabelle", "uploadedFromFile": "Aus der Datei hochgeladen", "appStatus": "Zust<PERSON> der Anwendung", "loadAll": "Alles e<PERSON>lesen", "ignoredUsers": "Ignorier<PERSON>", "copyRolesFrom": "<PERSON><PERSON> von <PERSON>op<PERSON>", "disableFrontendStyles": "Keine automatischen Stile anwenden", "activate": "Aktivieren", "confirmActivateCase": "Möchten Sie wirklich den Fall aktivieren?", "alrLackOfPerms": "Sie haben keine ausreichenden Berechtigungen.", "alrSending": "Senden...", "sequences": "Sequenz", "seqName": "Sequenz", "seqId": "Sequenz-ID", "seqLastRead": "Zuletzt verwendet", "ttCopyRole": {"heading": "<PERSON><PERSON> kopieren", "body": "Kopiert die markierte Rolle."}, "fromCase": "<PERSON><PERSON> dem Fall", "includingData": "Einschließlich der Daten", "choose": "Auswählen", "valueChange": "Änderung des Wertes", "updateInstances": "Die Änderung der Variablen in die Instanzvariablen einbeziehen", "externalSource": "Quelle des Benutzers", "reports": "Berichte", "confirmCopyReport": "Möchten Sie wirklich den ausgewählten Bericht kopieren?", "graphs": "Diagramme", "aggregation": "Aggregation", "graphNew": "Diagramm - neu", "confirmCopyGraph": "Möchten Sie wirklich das ausgewählte Diagramm kopieren?", "alrCopyGraphFailed": "Das Diagramm konnte nicht kopiert werden.", "label": "Label", "pie": "Kreisdiagramm", "line": "Liniendiagramm", "dot": "Punktdiagramm", "bar": "Balkendiagramm", "barGroups": "Balkendiagramm - Gruppen", "alrFailedGraphData": "Die Diagrammdaten konnten nicht heruntergeladen werden.", "graphSetSharing": "Stellen Sie die Teilung des Diagramms für die einzelnen Benutzergruppen ein", "alrGraphPointsLoadFailed": "Die Daten der Punkte konnten nicht heruntergeladen werden.", "alrGraphNotFound": "Das Diagramm wurde nicht gefunden.", "graphData": "Diagrammdaten", "pointsData": "Diagrammpunkte", "alrGraphSaveFailed": "Das Diagramm konnte nicht gespeichert werden!", "graphPoint": "Punkt des Diagramms", "noOrder": "<PERSON><PERSON>", "refreshGraph": "Diagrammdaten wiederherstellen", "viewSwitcher": "<PERSON><PERSON>", "axisXglobalFilter": "Achse X – globaler Filter", "axisXgroups": "Achse X – Gruppen", "axisXdata": "Achse X – Daten", "axisYvalues": "Achse Y – Werte", "axisYcolors": "Achse Y – Farben", "hrAgenda": "HR-Agenda", "userChange": "Änderung des Benutzers", "newUser": "<PERSON><PERSON><PERSON>", "usersCount": "<PERSON><PERSON><PERSON> der Benutzer ", "confirmChangeUser": "Möchten Si<PERSON> wirklich den Benutzer ändern?", "businessVariable": "Businessvariable", "casesCount": "Anzahl der Fälle", "selected": "Ausgewählt", "selectedOnly": "Nur ausgewählte  ", "addCaseRightNewUser": "Fallzugriff hinzufügen", "visFromTaskToPull": "Sichtbarkeit aus der Aufgabe zur Abnahme", "toChangeConfigInfo": "Um die Änderung durchzuführen, ist der Wert aus der Datei local.js zu entfernen", "clickToChange": "Klicken um die Änderung durchzuführen", "currentValue": "Aktueller Wert", "sign": "Unterzeichnen", "validationProtocols": "Validierung", "plannedEvents": "Aktion", "elArchiv": "E-Archiv", "deletedDocs": "Gelöschte", "signatures": "Unterschriften", "ttArchive": {"heading": "Elektronisches Archiv", "body": ""}, "ttAddToZea": {"heading": "Dem elektronischen Archiv hinzufügen", "body": ""}, "ttRemoveFromZea": {"heading": "Aus dem elektronischen Archiv entfernen", "body": ""}, "ttZeaInfo": {"heading": "Validierung", "body": ""}, "ttSignZea": {"heading": "Extern unterzeichnen", "body": ""}, "addToZea": "Dem E-Archiv hinzufügen", "removeFromZea": "Aus dem E-Archiv entfernen", "reTimestampAfter": "Gültigkeit des generierten Stempels (Tage)", "alrLoadFailed": "Das Hochladen ist fehlgeschlagen.", "replace": "<PERSON><PERSON><PERSON><PERSON>", "expireAt": "Läuft ab", "result": "Ergebnis der Validierung", "validatedAt": "Datum der Validierung", "refType": "Objekt", "eventType": "Art der Aktion", "errorMessage": "Fehlermeldung", "errorTimestamp": "Datum des Fehlers", "errorCount": "<PERSON><PERSON><PERSON>hler", "inFuture": "Planmäßige", "path": "Weg zur Signatur", "signedAt": "Datum der Signaturerstellung", "dropZoneZeaCertificate": "Die Datei mit dem Zertifikat hierher verschieben oder hier klicken, um die Datei auszuwählen.", "authType": "Authentisierungsweise", "basic": "Mit dem Namen und Passwort", "byCert": "<PERSON><PERSON> dem Zertifikat", "alrMissingCertFile": "Zertifikat bitte einfügen.", "replaceTo": "Ersetzen für", "autoReTimestamp": "Automatischer Zeitstempel", "validate": "Validierung", "lastUse": "<PERSON>ulet<PERSON><PERSON> generiert", "createdAt": "Erstellt am", "updatedAt": "Aktualisiert am", "certificateId": "Zertifikat-ID", "expectedCreationTime": "Wir<PERSON> hi<PERSON>", "nextTSPSourceId": "ID des nächsten Stempels", "reTimestampAt": "Nächster Zeitstempel", "timestampedAt": "Letzter Zeitstempel", "level": "Niveau", "signatureB": "Basissignatur", "signatureT": "Signatur mit Zeitstempel", "signatureLt": "Signatur mit langfristigen Datenzertifikaten", "signatureLta": "Signatur mit langfristigen Datenzertifikaten und einem Zeitstempel des Archivs", "packaging": "Verpackung", "enveloped": "Mit Umschlag versehen", "enveloping": "Umschlag", "detached": "Getrennt", "algorithm": "<PERSON><PERSON><PERSON><PERSON>", "uploadAsRevision": "Als Revision hochladen", "externalDisable": "Aktiv nur für massenweise", "addToDms": "Dem DMS hinzufügen", "autoConvert": "Automatisch konvertieren", "format": "Format", "signatureType": "Art der Signatur", "signature": "Signatur", "custom": "<PERSON><PERSON><PERSON>", "batchSignDisabled": "<PERSON>ne Signatur", "tasId": "Dokument-ID", "hashValue": "Hash-<PERSON><PERSON>", "hashType": "Art der Hash-Funktion", "confirmAddToArchive": "Möchten Sie wirklich dem Archiv hinzufügen?", "independentSignature": "Unabhängige Signatur", "independentValidation": "Unabhängige Validierung", "failureTrue": "<PERSON><PERSON>", "failureFalse": "<PERSON><PERSON>", "confirmValidateDialog": "Möchten Sie wirklich die Signatur validieren?", "confirmRestartDialog": "Möchten Sie wirklich die Fehler zurücksetzen?", "verificationResult": "Ergebnis der Überprüfung", "integrityMaintained": "Behaltene Integrität", "signatureFormat": "Format der Signatur", "internalTimestampsList": "Liste der internen Zeitstempel", "signers": "Unterzeichner", "exhibitedBy": "Ausgestellt von", "signedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "validFrom": "Gültigkeit von", "validUntil": "Gültigkeit bis", "signitureType": "Art der Signatur", "signatureQualification": "Qualifizierung der Signatur", "signatureNoTimestamps": "Die Signatur enthält keine Zeitstempel", "electronicSignature": "Elektronische Signatur", "electronicSeal": "Elektronisches Siegel", "webSiteAuthentication": "Authentisierung der Website", "QCP-n": "QCP-n: Zertifikatpolitik für EU-qualifizierte Zertifikate, die natürlichen Personen ausgestellt werden", "QCP-l": "QCP-l: Zertifikatpolitik für EU-qualifizierte Zertifikate, die juristischen Personen ausgestellt werden", "QCP-n-qscd": "QCP-n-qscd: Zertifikatpolitik für EU-qualifizierte Zertifikate, die natürlichen Personen mit einem Privatschlüssel im Zusammenhang mit zertifiziertem öffentlichem Schlüssel in QSCD ausgestellt werden ", "QCP-l-qscd": "QCP-l-qscd: Zertifikatpolitik für EU-qualifizierte Zertifikate, die juristischen Personen mit einem Privatschlüssel im Zusammenhang mit zertifiziertem öffentlichem Schlüssel in QSCD ausgestellt werden", "QCP-w": "QCP-w: Zertifikatpolitik für die Überprüfung der Webseiten mit einer EU-Qualifizierung", "formOfReStamping": "Form der erneuten Stempelung", "individually": "Individuell", "archiveAsPdf": "Als PDF archivieren", "couldNotBeVerified": "Ist nicht überprüfbar", "uses": "<PERSON><PERSON><PERSON>", "countOfSignedDocuments": "Anzahl der unterzeichneten Dokumente", "batchSignature": "Kollektivunterschrift", "standaloneSign": "Individuelle Unterschrift", "validateSignature": "Validierung der Signatur", "validateDoc": "Validierung des Dokumentes", "containsSignature": "Enthält die Signatur", "reStamping": "Erneute Stempelung", "individualSignatures": "Individuelle Unterschriften", "signatureLevel": "Signaturniveau", "simpleReport": "Einfacher Bericht", "detailedReport": "Detailbericht", "diagnosticReport": "Diagnosebericht", "etsiReport": "ETSI-Bericht", "TOTAL_PASSED": "In Ordnung", "TOTAL_FAILED": "Fehlgeschlagen", "INDETERMINATE": "Unbestimmt", "FORMAT_FAILURE": "Die Signatur stimmt mit einem der grundlegenden Standards nicht überein", "HASH_FAILURE": "Hash des unterzeichneten Datenobjektes entspricht nicht dem Hash in der Signatur", "SIG_CRYPTO_FAILURE": "Die Signatur konnte nicht mit Hilfe des öffentlichen Schlüssels des Unterzeichners überprüft werden", "REVOKED": "Das Zertifikat für die Signatur wurde aufgehoben und gleichzeitig liegt der Beweis vor, dass die Signatur nach der Revokation entstanden ist", "SIG_CONSTRAINTS_FAILURE": "Ein oder mehrere Signaturattribute entsprechen nicht den Überprüfungsregeln", "CHAIN_CONSTRAINTS_FAILURE": "Die im Validierungsprozess verwendete Zertifikatkette entspricht nicht den zertifikatbezogenen Validierungsregeln ", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "Die für die Überprüfung der Kette verfügbare Zertifikatgruppe verursachte einen Fehler aus nicht spezifizierten Gründen", "CRYPTO_CONSTRAINTS_FAILURE": "<PERSON><PERSON> von den Algorithmen, die sich an der Überprüfung der Signatur beteiligen, liegt unter dem verlangten Niveau der kryptographischen Sicherung und die Signatur wurde nach der maßgebenden Zeitdauer der Algorithmusanwendung angefertigt", "EXPIRED": "Die Signatur wurde nach Ablauf der Gültigkeitsfrist des Zeichnungszertifikates erstellt ", "NOT_YET_VALID": "Der Zeitpunkt der Signatur liegt vor dem Ausstellungsdatum des Zeichnungszertifikates", "POLICY_PROCESSING_ERROR": "Die Datei der Validierungspolitik konnte nicht bearbeitet werden", "SIGNATURE_POLICY_NOT_AVAILABLE": "Das elektronische Dokument mit den Einzelheiten zur Validierungspolitik steht nicht zur Verfügung", "TIMESTAMP_ORDER_FAILURE": "Die Einschränkungen in der Reihenfolge der Signaturzeitstempel werden nicht eingehalten", "NO_SIGNING_CERTIFICATE_FOUND": "Das Zeichnungszertifikat kann nicht identifiziert werden", "NO_CERTIFICATE_CHAIN_FOUND": "Es wurde keine Zertifikatkette für das identifizierte Zeichnungszertifikat gefunden", "REVOKED_NO_POE": "Das Zeichnungszertifikat wurde am Datum /Uhrzeit der Überprüfung revoziert. Der Überprüfungsalgorithmus kann jedoch nicht feststellen, ob der Zeitpunkt der Signatur vor oder nach der Revokation liegt.", "REVOKED_CA_NO_POE": "Es wurde mindestens eine Zertifikatkette gefunden, aber es wurde ein CA Übergangszertifikat revoziert ", "OUT_OF_BOUNDS_NOT_REVOKED": "Die Gültigkeit des Zeichnungszertifikates wird ablaufen oder ist zum Zeitpunkt der Überprüfung noch nicht gültig und der Überprüfungsalgorithmus kann nicht überprüfen, ob der Signaturzeitpunkt im Gültigkeitsintervall des Zeichnungszertifikates liegt. E<PERSON> ist bekannt, dass das Zertifikat nicht revoziert wurde. ", "OUT_OF_BOUNDS_NO_POE": "Die Gültigkeit des Zeichnungszertifikates ist abgelaufen oder ist zum Zeitpunkt der Überprüfung noch nicht gültig", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "<PERSON><PERSON> von den Algorithmen, die sich an der Überprüfung der Signatur beteiligen, liegt unter dem verlangten Niveau der kryptographischen Sicherung und es liegt kein Beweis vor, dass er vor dem Zeitpunkt erste<PERSON>t wurde, zu dem dieser Algorithmus/Schlüssel für sicher gehalten wurde", "NO_POE": "Es fehlt der Beweis, dass das unterzeichnete Objekt vor einem kompromittierenden Ereignis erstellt wurde", "TRY_LATER": "<PERSON><PERSON> <PERSON><PERSON><PERSON> von verfügbaren Informationen können nicht alle Validierungsregeln erfüllt werden. Dies kann jedoch mit Hilf<PERSON> von weiteren Informationen über den Widerruf durchgeführt werden, die später zur Verfügung stehen werden. ", "SIGNED_DATA_NOT_FOUND": "Die unterzeichneten Daten können nicht gewonnen werden", "GENERIC": "<PERSON><PERSON>", "signatureFile": "<PERSON>i mit Signatur", "validityDays": "Gültigkeit Tage", "qualifiedHe": "Qualifizier<PERSON>", "qualifiedIt": "Qualifizierte", "unqualifiedHe": "<PERSON><PERSON> qualifiziert", "unqualifiedIt": "Nicht qualifizierte", "timeValid": "Zeitlich gültig", "reason": "<PERSON><PERSON><PERSON>", "inTime": "rechtzeitig", "certificateQualification": "Qualifizierung des Zertifikates", "guaranteedHe": "<PERSON><PERSON><PERSON><PERSON>", "guaranteedIt": "Garantierte", "fromQualifiedCert": "Aus qualifiziertem Zertifikat", "basedOnQualifiedCertHe": "Bestehend im qualifizierten Zertifikat", "createdByQualifiedResHe": "<PERSON><PERSON> Hilfe eines qualifizierten Mittels erstellt", "basedOnQualifiedCertIt": "Bestehend im qualifizierten Zertifikat", "createdByQualifiedResIt": "<PERSON><PERSON> Hilfe eines qualifizierten Mittels erstellt", "qualification": "Qualifizierung", "confirmRemoveFromZeaDialog": "<PERSON><PERSON><PERSON><PERSON> wirklich die {{variable}} aus dem elektronischen Archiv entfernen?", "noValidationReports": "<PERSON>ine Ergebnisse der Validierung", "noSignatures": "keine individuellen Unterschriften", "isHigherOrEqualThan": "<PERSON>ss größer oder gleich sein", "isInZea": "Im E-Archiv", "startStamping": "Mit dem Stempel beginnen", "reTimestampAfterMinutes": "Minuten", "reTimestampAfterDays": "Tage", "reTimestampAfterAll": "Gültigkeit des generierten Stempels  ", "refId": "Objekt-ID", "docWithoutAutoTimestampInfo": "Das Dokument wird einmalig ohne automatisches Einfügen des Zeitstempels unterzeichnet.", "validationReports": "Historie der Validierungen", "docPath": "Weg zum Dokument", "addToArchiveInvalidSignatureError": "Die Datei kann in das Archiv nicht eingefügt werden, weil sie eine Signatur enthält, die nicht überprüft werden kann.", "signImmediately": "<PERSON><PERSON> un<PERSON>ze<PERSON>", "replaceInConfiguration": "In der Konfiguration ersetzen", "cancel": "Stornieren", "bulk": "Massenweise", "bulkCompletion": "Massenweise erfüllen", "enableBulkCompletion": "Massenweise Erfüllung genehmigen", "confirmCompleteTasks": "Möchten Sie wirklich die Aufgaben erfüllen?", "plannedMaintenance": "planmäßige Wartung", "notSpecified": "Nicht spezifiziert", "bulkCompletionVars": "Variablen der massenweisen Erfüllung", "alrBulkCompletionMultiTypeErr": "Massenweise können nur Aufgaben gleicher Art erfüllt werden, <PERSON><PERSON> können einen Filter verwenden. ", "notifications": "Mitteilung", "alrTskAlreadyTakenSomeone": "Die Aufgabe wurde bereits von jemandem anderen übernommen.", "alrTskAlreadyTaken": "Die Aufgabe wurde schon übernommen.", "downloadBpmn": "BPMN Diagramm herunterladen", "downloadSvg": "als SVG-<PERSON><PERSON><PERSON>", "displayForm": "Darstellungsform", "selectedPreview": "die Ansicht wird dargestellt", "fixedHeight": "Fixe Höhe (in Pixel)", "lastVersion": "Letzte Version", "separatedPreview": "Get<PERSON>nte <PERSON>", "defaultZoom": "Ausgangszoom", "fixedPosition": "Position fixieren", "percentInterval": "Bitte eine ganze Zahl zwischen 0-5 eingeben", "notPositiveNumber": "<PERSON><PERSON> <PERSON>", "zoomDown": "Verkleinern", "zoomUp": "Vergrößern", "rotate": "<PERSON><PERSON><PERSON>", "logTooBig": "Der Log ist zu groß, um angezeigt zu werden.", "downloadLog": "Log herunterladen", "confirmCopyCron": "Möchten Sie wirklich die ausgewählte Cron kopieren?", "ttCopyCron": {"heading": "<PERSON><PERSON>en", "body": ""}, "onlyWorkingDays": "Nur Arbeitstage", "datesDisabled": "<PERSON><PERSON>", "useView": "Verwenden View", "dateWithoutTime": "Datum ohne Zeit", "timezone": "Zeitzone", "roleRestriction": "Rollen-Einschränkung", "headerRestriction": "Header-Einschränkung", "ttSwitchDarkmode": {"heading": "Hell/Dunkel-Mo<PERSON> um<PERSON>ten", "body": ""}, "advancedEditor": "Erweiterter Editor", "externalId": "Externe ID", "passwordValidationMin": "Passwort ist zu kurz. (minimale Länge: {{count}})", "passwordValidationMax": "Passwort ist zu lang. (maximale Länge: {{count}})", "passwordValidationUppercase": "Passwort muss einen Großbuchstaben enthalten. {{atLeast}}", "passwordValidationLowercase": "Passwort muss einen Kleinbuchstaben enthalten.. {{atLeast}}", "passwordValidationSymbols": "Passwort muss ein Symbol enthalten. {{atLeast}}", "passwordValidationDigits": "Passwort muss eine Zahl enthalten. {{atLeast}}", "passwordValidationLetters": "Passwort muss einen Buchstaben enthalten. {{atLeast}}", "atLeast": "Mindestens", "passwordValidationServiceErr": "Passwort kann derzeit nicht geändert werden.", "enableTasksHandoverRole": "Erlauben Sie immer Aufgabenweiterleitung und Ereignisauslösung für Benutzer dieser Rolle", "shredded": "Geschredderte", "shredableVar": "Shredderbare Variable", "shredDocuments": "Dokumente vernichten", "shredInDays": "Shred in (Tage)", "fromBeginningOrendOfCase": "<PERSON>/<PERSON><PERSON> Falles", "shredding": "Schreddern", "addColumn": "Spalte hinzufügen", "unsupportedBrowser": "Sie öffnen TeamAssistant in einem nicht unterstützten Internet Explorer-<PERSON>rowser, einige Funktionen sind möglicherweise nicht verfügbar.", "ingoreProcessRights": "Fallrechte ignorieren", "cvHelpIngoreProcessRights": "Der Übersicht zeigt immer alle Fälle, unab<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "upperLabel": "Platziere die Variable unter ihrem Namen", "darkMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "completedTasks": "Abgeschlossene Aufgaben", "permissions": "Berechtigungen", "caseVisibility": "Fallsichtbarkeit", "visPerOrg": "Sichtbarkeit für die Organisationseinheit", "entity": "Entity", "staticRight": "Statisches Gesetz", "dynamicRight": "Dynamisches Gesetz", "treeNodesAll": "Alles", "treeNodesMy": "<PERSON><PERSON>", "activeQueries": "Aktive Abfragen", "query": "Anfrage", "confirmCancelQuery": "Möchten Sie die Abfrage wirklich abbrechen?", "alrQueryNotFound": "Die Abfrage wurde nicht mehr gefunden.", "completeAgenda": "Vollständige Agenda", "lockedBusinessUsers": "Gesperrte Geschäftsbenutzer", "structuredList": "Strukturierte Liste", "ttCompetences": {"heading": "Kompetenzmanagement", "body": ""}, "competences": "Kompetenzen", "competence": "Kompetenz", "competenceDelVar": "Kompetenz", "addCompetence": "Kompetenz hinzufügen", "regularExpression": "Reg<PERSON><PERSON>ren <PERSON>", "generationStatus": "Generationsstatus", "source": "<PERSON><PERSON>", "historical": "Historisch", "external": "Extern", "nextDay": "nächster Tag", "embeddedVideoNotSupported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> Browser unterstützt keine eingebetteten Videos.", "alrSendingTestMailFailed": "Test-E-Mail fehlgeschlagen.", "sent": "Gesendet.", "sendingFailed": "<PERSON><PERSON>", "sendOn": "Zeit gesendet werden", "mainColorEmail": "E-Mail Hauptfarbe", "confirmResetColors": "Möchten Sie die Farben wirklich zurücksetzen?", "regularExpressions": "Reguläre Ausdrücke", "confirmDeleteLogo": "Möchten Sie das Logo wirklich löschen?", "loginLogoLightTheme": "Anmeldebildschirm-Logo (heller Modus)", "loginLogoDarkTheme": "Anmeldebildschirm-Logo (dunkel Modus)", "competenceRegexHelper": "<ul><li><b>%</b> kann für N beliebige Zeichen verwendet werden  (ähnlich *)</li><li><b>_</b> kann als ein beliebiges Zeichen verwendet werden (ähnlich .)</li><li><PERSON><PERSON> könne<PERSON> <b>^</b> verwen<PERSON>, um diese Sonderzeichen zu maskieren (ähnlich \\)</li></ul>", "headerFont": "<PERSON><PERSON><PERSON><PERSON> der Header ", "evenRow": "Gerade <PERSON>", "logo": "Logo", "widthForLogo": "Breite für Logo", "monthStart": "<PERSON><PERSON><PERSON>ts", "monthEnd": "<PERSON><PERSON> des Monats", "ttFavouriteType": "GET öffnet den Link. POST sendet einen Befehl: z. B. beim Anlegen eines Falls, bei dem die Kopfzeile der Vorlage im Textkörper der Anfrage gesendet wird (Speichern in den Favoriten über Neuer Fall).", "confirmEmptyMultiinstanceVariable": "Sind <PERSON> sicher, dass diese Multiinstanz keine Variable zum Iterieren benö<PERSON>gt?", "ttMenuPreview": "Menükonfiguration nach Benutzerrollen (bedeutendere Rollen sehen auch Schaltflächen für weniger bedeutende Rollen). Die Schaltflächen Neuer Fall und Dashboard bleiben unverändert.", "menuPreview": "Menüvorschau für die ausgewählte Rolle", "confirmResetMenu": "Möchten Sie das Menü wirklich zurücksetzen?", "alrFailedTasMenu": "Die Konfiguration des Tas-Menüs konnte nicht geladen werden!", "security": "Sicherheit", "userRestrictions": "Benutzereinschränkungen (Anzeigen)", "userRestrictionsProcesses": "Ignorieren Sie Benutzereinschränkungen für Aufgaben", "roleRestrictions": "Rolleneinschränkungen (Anzeigen)", "orgUnitRestrictions": "Einschränkungen der Organisationseinheit (Anzeigen)", "everyone": "<PERSON><PERSON>", "colleaguesOnly": "Nur Kollegen", "directSubordinates": "Direkte Untergebene", "allSubordinates": "Alle Untergebenen", "none": "<PERSON><PERSON>", "generalDocument": "Allgemeines Dokument", "competenceRule": "Kompetenzregel", "competenceRules": "Kompetenzregeln", "ruleName": "Name der Regel", "ttUseCompetenceRule": {"heading": "Regel anwenden", "body": "Erstellt eine Kompetenz gemäß der ausgewählten Regel"}, "competenceText": "Kompetenztext", "competenceName": "Kompetenzname", "competenceReadOnlyInfo": "Die aus der Regel erstellte Kompetenz kann nicht geändert werden", "xmlProcessImport": "XML-Prozessimport", "ttWidthForLogo": "Legen Sie die Breite für das Logo fest und fügen Sie dann das Logo ein. Es ist nicht möglich, die Breite für ein bereits eingefügtes oder Standardlogo zu ändern.", "openCase": "Fall öffnen", "importHistory": "Importver<PERSON><PERSON>", "filePath": "Dateipfad", "cronId": "Cron-ID", "taskResult": "Aufgabenergebnis", "xmlFileSize": "Größe des XML-Dokuments", "attachmentSize": "Dateigröße", "lastEdit": "Letzte Änderung", "timeCreated": "Erstellungszeit", "importId": "Import-ID", "importAudit": "Import-Audit", "finishedImports": "Abgeschlossene Importe", "insertNote": "<PERSON>iz einfügen", "importXml": "XML importieren", "reImportXml": "XML reimportieren", "downloadXml": "<PERSON> herunterladen", "downloadAttachment": "<PERSON><PERSON>", "skipXml": "XML überspringen", "note": "Notiz", "attachmentName": "<PERSON><PERSON><PERSON>", "plannedImports": "Geplante Importe", "importedCount": "Anzahl der Importe", "retryCount": "Die Anzahl der Wiederholungen", "batchId": "Dosis id", "copyPath": "Pfad kopieren", "trace_id": "Trace ID", "cronRunId": "<PERSON><PERSON>-<PERSON>", "cronRun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ttMenuItemLabel": "Universalname, falls keine Übersetzung. Wenn ein Übersetzungsschlüsselwort verwendet wird, wird es automatisch übersetzt. Standardnamen: tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "Aufgabenwarteschlange", "dissolveQueue": "Warteschlange auflösen", "taskQueueInitInfo": "Durch diese Aktion wurden mehrere zu lösende Aufgaben erstellt. Hier können Sie die Reihenfolge ihrer Lösung ändern oder sie ganz aus der Warteschlange entfernen.", "tryDarkTheme": "Wir haben festgestellt, dass Sie den dunklen Modus bevorzugen. Klick<PERSON> Si<PERSON> hier, um es in TAS auszuprobieren.", "alrInvalidURL": "Ungültiges URL-Format.", "alrInvalidHttps": "Ungültiges URL-Format, muss mit https:// beginnen", "importVariables": "Variablen importieren", "ttVariablesImport": {"heading": "Import der Variablen", "body": "Nach der Auswahl der Datei mit definierten Variablen erfolgt ihr Hochladen."}, "classDiagram": "Klassen Diagramm", "createVar": "Variable erstellen", "importObjectStates": "Objektstatus importieren", "unassigned": "<PERSON>cht zugewiesen", "sortVars": "<PERSON><PERSON><PERSON><PERSON>", "fillNames": "<PERSON>n ausfüllen", "ttFillNames": {"heading": "<PERSON>n ausfüllen", "body": "Fü<PERSON>t leere Namen aller neuen Variablen im Format \"Klasse.Attribut\" und sortiert alle Variablen."}, "ttSortVars": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Sortiert Variablen nach Klassen und Attributen."}, "ttRestore": {"heading": "Wiederherstellen", "body": "Stellt Variablen in ihrem ursprünglichen Zustand wieder her, wenn sie aus einer Datei importiert wurden."}, "ttAddVarToBottom": {"heading": "Variable hinzufügen", "body": "<PERSON>ügt eine neue Variable am unteren Rand der Seite hinzu."}, "confirmRestoreForm": "Möchten Sie die Variablen wirklich in ihren ursprünglichen Zustand zurückversetzen?", "selectClass": "Klasse markieren", "importClassDiagram": "Klassendiagramm importieren", "continue": "<PERSON><PERSON>", "templateVars": "Vorlagenvariablen", "newVars": "Neue Variablen", "objectState": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alrDynTableExists": "Dynamische Tabelle existiert bereits!", "overwriteExistDynTable": "Vorhandene dyn. Tabelle überschreiben", "confirmCancelImport": "Möchten Sie den Import abbrechen?", "alrDuplicateNames": "Daten enthalten doppelte Namen.", "stateVar": "Zustandsvariable", "importObjectStatesToDynTables": "Objektzustände in dynamische Tabellen importieren.", "defineObjectStatesVars": "Definieren Sie die Variablen, die den Objektstatus enthalten.", "change": "Ändern", "classAndAttr": "Klasse und Attribut", "clearQueue": "Warteschlange löschen", "sharing": "Teilen", "data": "Daten", "open": "<PERSON><PERSON><PERSON>", "dataSource": "<PERSON><PERSON><PERSON><PERSON>", "dataPoints": "Datenpunkte", "dataSeries": "Datenreihe", "valueCol": "Wertespalte", "aggregationCol": "Aggregationsspalte", "timeDimension": "Zeit Abmessungen", "columns": "Spalten", "week": "<PERSON><PERSON><PERSON>", "weekday": "Wochentag", "monthVar": "<PERSON><PERSON>", "overviewFilter": "Übersichtsfilter", "globalFilters": "<PERSON><PERSON> Filter", "filterDefinition": "Filterdefinition", "newFilter": "<PERSON><PERSON><PERSON>", "addFilter": "<PERSON><PERSON>", "filterOptions": "Filteroptionen", "addOption": "Option hinzufügen", "graphPreview": "<PERSON>ik<PERSON><PERSON><PERSON>", "alrGlobalFilterDownloadFailed": "<PERSON><PERSON> beim Herunterladen globaler Filter!", "alrGlobalFilterSaveFailed": "Globale Filter konnten nicht gespeichert werden!", "filterOption": "Filteroption", "editFilter": "Filter bearbeiten", "fillOptionsFromVar": "Optionen aus Variable füllen", "fillOptionsDynamically": "Optionen dynamisch füllen", "filterOptionsFilledDynamically": "Dynamisch aus Variable", "dayOfMonth": "Monatstag", "dateVar": "Datum", "group": "Gruppe", "ttDataSource": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> \"Datenpunk<PERSON>\", wenn <PERSON>e jeden einzelnen Diagrammpunkt separat eingeben möchten. Wen<PERSON> <PERSON> möchten, dass die Punkte basierend auf der ausgewählten Dimension generiert werden, w<PERSON>hl<PERSON> Si<PERSON> \"Datenreihen\"", "ttDataSeriesAggregation": "Wählen Sie die Art der Aggregation. Ermöglicht das Erstellen von zusammenfassenden Informationen aus Datensätzen (Fällen).", "ttDataSeriesColumns": "W<PERSON>hlen Sie nacheinander alle Spalten aus, anhand derer Gruppen (Aggregationen) erstellt werden sollen, um zusammenfassende Werte zu berechnen.", "listOfFiltersIsEmpty": "Die Filterliste ist leer.", "fromVariable": "Aus einer Variablen", "showOptionsFromCount": "Optionen anzeigen (von {{count}})", "sum": "Summe", "minimum": "Minimum", "maximum": "Maximum", "statistics": "Statistiken", "unfilled": "Ungefüllt", "globalFilterDescription": "Der globale Filter bietet Diagrammbenutzern Optionen zum Filtern der Eingabedaten für das Diagramm. Alle Filteroptionen können in diesem Bildschirm definiert werden.", "ttDelGraph": {"heading": "Diagramm löschen", "body": "Löscht das ausgewählte Diagramm."}, "ttEditGraph": {"heading": "Diagramm bearbeiten", "body": "Ermöglicht es Ihnen, das ausgewählte Diagramm zu bearbeiten."}, "ttCopyGraph": {"heading": "Diagramm kopieren", "body": "Kopiert das ausgewählte Diagramm."}, "ttAddGraph": {"heading": "Diagramm hinzufügen", "body": "Ermöglicht es Ihnen, ein neues Diagramm zu definieren."}, "axisXName": "Name der X-Achse", "axisYName": "Name der Y-Achse", "showValues": "Werte anzeigen", "defaultOption": "Standardoption", "yearStart": "Anfang des Jahres", "yearEnd": "Ende des Jahres", "thisMonth": "<PERSON><PERSON>", "lastMonth": "Letzter Monat", "thisYear": "<PERSON><PERSON>", "lastYear": "Letztes Jahr", "scheduledTasks": "Geplante Aufgaben", "scheduled": "Geplante", "dueDateStart": "Startdatum", "lastRescheduled": "Zuletzt neu geplant", "reschedule": "<PERSON>eu planen", "alrTasksRescheduling": "Aufgaben neu planen...", "alrTasksRescheduled": "Aufgaben wurden neu geplant.", "alrTasksRescheduleFailed": "Aufgaben konnten nicht neu geplant werden.", "onlyCurrentOrFutureDates": "Nur heute oder zukünftige Daten.", "passwordValidations": "Passwortrichtlinie", "readonlyConfigInfo": "Der Wert ist schreibgeschützt", "alrTasksCountFailed": "Aufgabenzählung fehlgeschlagen.", "confirmActivateTasks": "Sind <PERSON> sicher, dass Sie die ausgewählten Aufgaben aktivieren möchten?", "confirmSuspendTasks": "Sind <PERSON> sicher, dass Sie die ausgewählten Aufgaben in den Schlaf-Modus versetzen möchten?", "tskOffset": "Planungsvariable", "workWeek": "Arbeitswoche", "agenda": "Agenda", "noEventsInRange": "<PERSON>s gibt keine Ereignisse in diesem Bereich", "activitiesDesc": "Beschreibung der Aktivitäten", "allShort": "Alle", "numberOfEvents": "<PERSON><PERSON><PERSON> der Ereignisse", "weekNumber": "wochennummer", "cannotBeEdited": "Kann nicht eingestellt werden", "cannotBeMoved": "Kann nicht bewegt werden", "alrTempVarSaveSameNameFailed": "Eine Variable mit diesem Voreingestellt namen existiert bereits, bitte geben Si<PERSON> einen anderen Namen ein.", "maxUsersCountRole": "Die maximale An<PERSON>hl von Benutzern in der Rolle", "unlimitedAssignLeaveBlankInfo": "<PERSON><PERSON>r unbegrenzte Zuweisungen lassen Sie das Feld leer.", "cvOwner": "Übersicht Eigentümer", "changePassword": "Passwort ändern", "passwordExpired": "Ihr Passwort ist abgelaufen. Bitte ändern Sie Ihr Passwort.", "passwordWillExpire": "Ihr Passwort läuft bald ab. Geben Sie ein neues Passwort ein.", "userParameters": "Benutzerparameter", "filterSortingHelper": "Das Sortieren nach einer oder mehreren Spalten in einem Filter deaktiviert die Möglichkeit, Spalten direkt in der Tabelle manuell zu sortieren.", "importUsers": "Benutzer importieren", "importRoles": "Rollen importieren", "existingEntityRows": "Zeilen mit bereits existierenden Entitäten (können überschrieben werden)", "fileRow": "Dateizeile", "existingEntityRowsMultiple": "Zeilen mit Entitäten, die bereits mehrfach vorhanden sind (werden nicht importiert)", "importOrgUnits": "Org. Einheiten importieren", "structureImportExport": "Strukturimport/-export", "fillAttributes": "Attribute füllen", "structurePreview": "Strukturvorschau", "invalidRowsForImport": "Ungültige Zeilen (fehlende Pflichtdaten)", "duplicateRowsForImport": "Zeilen mit doppelt übereinstimmenden Daten (werden nicht importiert)", "newEntityRows": "<PERSON><PERSON>en mit neuen zu importierenden Entitäten", "existingNameRowsForImport": "<PERSON><PERSON><PERSON> mit Namen, die bereits auf anderen Entitäten existieren (werden nicht importiert)", "overwriteExisting": "Vorhandenes überschreiben", "structurePreviewHelper": "Die Strukturvorschau zeigt zwei unterschiedliche Situationen: nur neue Organisationen importieren oder sowohl neue als auch bestehende Organisationen importieren, die überschrieben werden. Alle Änderungen gegenüber der aktuellen Struktur sind rot markiert.", "validateAndShowPreview": "Validieren und Vorschau", "uploadNewFile": "<PERSON>e neue Datei hochladen", "userStatus": "Benutzerstatus", "importedFile": "Importier<PERSON>", "pairUsersBy": "Benutzer paaren nach", "assignOrgBy": "Der Organisation zuweisen von", "pairRolesBy": "<PERSON>en paaren nach", "pairUnitsBy": "Einheiten paaren nach", "unitHierarchyCol": "Einheitenhierarchiespalte", "dontAssign": "<PERSON><PERSON>", "infoImportDataValidated": "WARNUNG: Die Daten wurden gerade aufgrund von Änderungen in den Einstellungen validiert. Wir empfehlen, zurückzugehen und die neue Importvorschau zu überprüfen.", "assignUserRolesMethod": "Wie man <PERSON>utzern Rollen zuweist", "assignUserRolesMethodHelp": "So weisen Sie Rollen zu: Ergänzen Sie bereits zugewiesene Rollen oder ersetzen Sie aktuell zugewiesene Rollen vollständig durch neu zugewiesene Rollen.", "existingRolesForImport": "Vorhandene Rollen (können überschrieben werden)", "existingRoleNamesForImport": "<PERSON><PERSON> mit Namen, die bereits für andere Rollen existieren (werden nicht importiert)", "newRolesForImport": "Neue zu importierende Rollen", "userRolesForImport": "Zeilen mit zuzuweisenden Benutzerrollen", "nonExistentUsersForImport": "<PERSON>eilen mit nicht existierenden Benutzern (Rollen werden nicht zugewiesen)", "multipleExistingUsersForImport": "<PERSON><PERSON><PERSON> mit mehr als einem vorhandenen Benutzer (Rollen werden nicht zugewiesen)", "invalidOrgsForImport": "Ungültige Zeilen (fehlende Pflichtdaten oder falsche Hierarchie)", "keepOriginal": "Original behalten", "assignOrgByHelp": "Wenn Sie eine Spalte aus der Datei auswählen, können Sie die Organisationsklassifizierung für neue und vorhandene Benutzer festlegen. Wenn Sie eine bestimmte Organisation auswählen, werden alle importierten oder aktualisierten Benutzer dieser Organisation zugewiesen.", "creatingRoles": "<PERSON><PERSON> erstellen", "assigningRolesToUsers": "Benutzern Rollen zuweisen", "newUsers": "<PERSON><PERSON><PERSON>", "existingUsers": "<PERSON><PERSON><PERSON><PERSON>", "fromFile": "<PERSON><PERSON> Datei", "alrCsvXlsxUploadWrongExtension": "Nur Dateien mit der Erweiterung *.csv oder *.xlsx hochladen", "importNewAndExisting": "Neue Entitäten importieren und vorhandene überschreiben", "importNewOnly": "Nur neue Entitäten importieren", "importNewAndExistingRoles": "Neue Rollen importieren und vorhandene Rollen überschreiben", "importNewRolesOnly": "Nur neue Rollen importieren", "importRolesHelper": "Einstellungen zum Importieren der Rollen selbst. <PERSON> Zuweisen von Rollen zu Benutzern wird durch die Einstellungen in \"Benutzer paaren nach\" geregelt und gilt immer für neue und vorhandene Rollen.", "statisticsColorHelper": "<PERSON>n Farben nicht manuell ausgewählt werden oder wenn weniger Farben als Spalten ausgewählt sind, werden die fehlenden Farben automatisch generiert. Generierte Farben enthalten niemals dunkle oder zu helle Farbtöne, diese können nur manuell ausgewählt werden.", "caseService": "Fallservice", "taskService": "Aufgabendienst", "editTasks": "Aufgaben bearbeiten", "editCases": "<PERSON><PERSON><PERSON> bear<PERSON>", "deleteTasks": "Aufgaben löschen", "deleteCases": "<PERSON><PERSON><PERSON>", "serviceOperationsInfo": "Markieren und füllen Sie die Variablen aus, die Si<PERSON> ändern möchten.", "erased": "Gelöscht", "statusErrored": "<PERSON><PERSON>", "serviceOperations": "Service-Operationen", "runCalcsOnStart": "Be<PERSON>chnungen beim Start ausführen", "taskReactivation": "Aufgabenreaktivierung", "taskCompletion": "Aufgabenabschluss", "caseReactivation": "Fallreaktivierung", "caseCompletion": "Fallabschluss", "openTask": "Aufgabe öffnen", "changeEntity": "Entität ändern", "selectTableColumns": "Tabellenspalten auswählen", "parentCase": "Übergeordneter Fall", "ownerOrganization": "Eigentümerorganisation", "confirmTaskReactivation": "Sind <PERSON> sicher, dass Sie die ausgewählten Aufgaben reaktivieren möchten?", "confirmCaseReactivation": "Sind <PERSON> sicher, dass Sie die ausgewählten Fälle reaktivieren möchten?", "confirmTaskCompletion": "Sind <PERSON> sicher, dass Sie die ausgewählten Aufgaben abschließen möchten?", "confirmCaseCompletion": "Sind <PERSON> sicher, dass Sie die ausgewählten Fälle abschließen möchten?", "selectAllFilterMustBeActive": "Mindestens ein Filter muss aktiv sein, um alle Elemente auszuwählen.", "changeEntities": "Entitäten ändern", "disabledDifferentTemplates": "<PERSON>nn nicht geändert werden, da die Entitäten nicht aus derselben Vorlage stammen.", "actions": "Aktionen", "taskTemplateId": "Aufgabenvorlagen-ID", "caseTemplateId": "Fallvorlagen-ID", "actionInfoCheckLogs": "Die Aktion wird im Hintergrund ausgeführt, bitte überprüfen Sie die Protokolle.", "alrServiceOperationsColumnsFailed": "Speichern der Spalteneinstellung für Dienstvorgänge fehlgeschlagen.", "confirmResetSelectedCols": "Möchten Sie die gespeicherten Tabellenspalten wirklich zurücksetzen?", "instanceVars": "Instanzvariablen", "usrId": "Benutzer-ID", "orgId": "Organisations-ID", "titlePrefix": "Titelpräfix", "titleSuffix": "Titelsuffix", "accessRoleId": "Zugriffsrollen-ID", "maxAssigns": "Maximale Zuweisungen", "client": "Kunde", "bigValue": "Großer Wert", "unitId": "Einheiten-ID", "roleId": "Rollen-ID", "paramId": "Parameter-ID", "varId": "Variablen-ID", "parentId": "ID des Elternteils", "openUser": "<PERSON><PERSON><PERSON> ö<PERSON>", "openRole": "<PERSON><PERSON>", "openUnit": "Ein<PERSON><PERSON>", "units": "Einheiten", "managerId": "Manager-ID", "externalStatus": "Externer Status", "additionalId": "Zusätzliche ID", "parentIc": "IC des Elternteils", "companyIc": "IC der Firma", "textValue": "Textwert", "dateValue": "Datumswert", "numberValue": "Numerischer Wert", "loginCount": "<PERSON><PERSON><PERSON><PERSON>", "externalLogin": "<PERSON>tern<PERSON>", "badLoginCount": "<PERSON><PERSON>hl ungültiger Anmeldungen", "passwordLastChange": "Letzte Passwortänderung", "solverEvaluation": "Bewertung des Lösers", "solverWillBe": "<PERSON> Löser wird sein", "possibleSolvers": "Mögliche Löser", "selectReferencePerson": "Wählen Sie eine Referenzperson aus", "evaluateSolver": "<PERSON><PERSON><PERSON> auswerten", "referenceUserForEval": "Referenzperson zur Bewertung", "andOthers": "...und andere", "showLess": "...weniger anzeigen", "alrSessionExpired": "Ihre Sitzung ist abgelaufen, bitte melden Sie sich erneut an.", "mailPromptlyInfo": "Der Benutzer erhält kontinuierlich einmalige Benachrichtigungen über neue Aufgaben, wo sie der Löser sind. Diese Warnungen werden nur gesendet, wenn die Aufgabe seit ihrer Aktivierung {{minutes}} Minuten lang nicht gelöst wurde.", "mailPullInfo": "Der Benutzer erhält kontinuierlich einmalige Benachrichtigungen über neue Aufgaben, die für das Abonnement verfügbar sind, und der Benutzer ist sein möglicher Solver. Die Benachrichtigung geht im Moment der Aktivierung der gegebenen Aufgabe im Workflow aus.", "mailTotalInfo": "Der Benutzer erhält regelmäßig einen Überblick über die Erledigung von Aufgaben, von denen er der Löser ist. Wenn die Aufgabe keinen direkten Solver hat, wird der Eigentümer des Prozesses benachrichtigt. Wenn der Benutzer vertreten ist, wird die Benachrichtigung von seinem Vertreter empfangen.", "mailEscalationInfo": "Der Benutzer erhält regelmäßig einen Überblick über die Erledigung von Aufgaben, die die Frist überschritten haben. Sie werden benachrichtigt, wenn sie der Vorgesetzte der Aufgabe sind (und nicht gleichzeitig ihr Löser) oder sie der direkte Manager des Benutzer, der der Löser ist. Wenn die Aufgabe keinen Löser hat, gilt der Prozessbesitzer als Vorgesetzter. Wenn der Benutzer vertreten ist, erwähnt die Benachrichtigung, wer der derzeitige Vertreter ist.", "calcSourceOverwriteWarning": "Nach dem Speichern wird der Quelltext mit ES6-Syntax überschrieben!", "changeStatus": "Status ändern", "confirmChangeEmailStatus": "Möchten Sie den Status der ausgewählten E-Mails wirklich ändern?", "logInAgain": "Erneut anmelden", "migrations": "<PERSON><PERSON><PERSON>", "launchDate": "Erscheinungsdatum", "stepName": "Schrittname", "runId": "<PERSON><PERSON>-<PERSON>", "clone": "Klon", "confirmDeleteCron": "Möchten Sie den ausgewählten Cron wirklich löschen?", "alrCronDeleted": "<PERSON><PERSON> wurde gelöscht!", "wantToContinueQ": "Möchtest du fortfahren?", "valueCannotBeEntered": "Der Wert kann nicht eingegeben werden", "processingQueues": "Verarbeitungswarteschlangen", "pause": "<PERSON><PERSON>tz<PERSON>", "fillOptionsFromVarHelper": "Filteroptionen können nur aus Variablen des Typs DT, DL, LT, LD, LN und D gefüllt werden, die keine Mehrfachauswahl zulassen.", "defaultTemplateName": "Standard-Vorlagenname", "defaultTaskName": "Standard-Aufgabenname", "defaultVariableName": "Standard-Variablenname", "variableName": "Varia<PERSON><PERSON><PERSON>", "alrNoDataFound": "<PERSON><PERSON> Daten gefunden", "ttProcessingQueuesInfo": "Die Verarbeitungswarteschlangen sind deaktiviert.\nUm sie zu aktivieren, setzen Sie mindestens eine der Konfigurationen \"scaling.queue.*.enabled\" auf true.", "businessUsers": "Geschäftsbenutzer", "completeHrAgenda": "Komplette HR-Agenda", "usageStatsByHeader": "Nutzungsstatistik nach Header", "usageStatsByOrgUnit": "Nutzungsstatistik nach Organisationseinheit", "usageStatsByUser": "Nutzungsstatistiken nach Benutzer", "completedTasksNum": "Anzahl der abgeschlossenen Aufgaben", "startedProcessesNum": "Anzahl der gestarteten Fälle", "ideHelp": "Drücken Sie im Editor Strg + <PERSON><PERSON><PERSON>, um Vorschläge anzuzeigen. Drücken Si<PERSON> erneut, um detailliertere Hilfe zu erhalten. Drücken Sie F1, um alle verfügbaren Befehle und Tastaturkürzel anzuzeigen. Weitere Informationen finden Sie in <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>der Editor-Dokumentation</a>.", "restHelp": "Geben Sie die URL für einen der TAS-Tabellendienste ein (z. B. ‚/tasks/mine‘) und wählen Sie nach dem Laden des Dienstes die Tabellenspalten aus, die Sie im Container anzeigen möchten.", "defaultGraphName": "Standard-Graphname", "graphName": "<PERSON><PERSON><PERSON><PERSON>", "ttStatistics": {"heading": "Statistik", "body": ""}, "defaultAxisXName": "Standardname der X-Achse", "defaultAxisYName": "Standardname der Y-Achse", "defaultFilterName": "Standard-Filtername", "filterName": "Filtername", "defaultOptionName": "Standard-Optionsname", "optionName": "Optionsname", "defaultOverviewName": "Standard-Übersichtsname", "overviewName": "Übersichtsname", "eventName": "Ereignisname", "wantToOverrideEs6": "<PERSON>n <PERSON> wirklich neu schreiben möchten, schreiben Si<PERSON> <b>ES6</b>", "processArchivation": "Prozessarchivierung", "processUnarchivation": "Dearchivierungsprozess", "resendEmail": "E-Mail erneut senden", "alrFailedSendEmail": "E-Mail konnte nicht gesendet werden", "ttResendEmail": {"heading": "E-Mail erneut senden", "body": "Sendet eine zuvor gesendete E-Mail-Benachrichtigung erneut. Empfänger können geändert oder hinzugefügt werden."}, "addCurrentScreenToFavourite": "Fügen Sie Ihren Favoriten den aktuellen Bildschirm hinzu", "attachmentAdd": "Fügen Si<PERSON> ein Dokument hinzu", "createNewCase": "<PERSON><PERSON><PERSON><PERSON> eines neuen Falls", "moreLanguage": "Andere Sprachenvarianten", "notesAdd": "<PERSON>üg ein Notiz hinzu", "notesNew": "Neue Note", "removeCurrentScreenFromFavourite": "Entfernen Sie den aktuellen Bildschirm vom Favoriten", "setDashboard": "Dashboard bearbeiten", "chooseFromCases": "Aus Fällen auswählen", "folders": "<PERSON><PERSON><PERSON>", "newFolderBtn": "<PERSON><PERSON><PERSON> Ordner", "documentInfo": "Informationen dokumentieren", "userInfo": "Benutzerinformationen", "deleteImage": "Image löschen", "profilePhoto": "Profilfoto", "profilePhotoCaption": "Verwenden Sie ein Foto im JPEG-, JPG-, PNG- oder GIF-Format.", "updatePhoto": "Foto aktualisieren", "mailNotifications": "E-Mail-Benachrichtigungen", "userPreferences": "Benutzervorlieben", "userSettings": "Benutzereinstellungen", "allVices": "Alle Vertreter", "createVice": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "editVice": "Vertreter ändern", "viceTip": "Mit der Vertretungsfunktion können Sie Ihre Agenda an einen Kollegen weitergeben", "emptyDataMessage": "<PERSON>hr gibt es nicht.", "addFirstNote": "Erste Notiz hinzufügen", "noResultsFor": "<PERSON><PERSON> für:", "noCurrentTasks": "Keine aktuellen Aufgaben", "checkYourSearch": "Überprüfen Sie Ihre Suche und versuchen Sie es erneut.", "noFavOverviews": "<PERSON><PERSON>", "favOverviewsTip": "<PERSON>ügen Sie eine Übersicht mit einem Stern zu Ihren Favoriten hinzu", "noHiddenOverviews": "Sie haben keine ausgeblendeten Übersichten", "addOverview": "Übersicht hinzufügen", "hidden": "Versteckt", "removeConfirm": "Entfernen", "removeItem": "<PERSON>d <PERSON> sic<PERSON>, dass Sie {{variable}} entfernen möchten?", "changePicture": "Bild ändern", "saveFilter": "<PERSON>lter speichern", "addAnotherVice": "Einen weiteren Vertreter hinzufügen", "saveVice": "Vertreter speichern", "firstLastName": "Vor- und Nachname", "taskInfo": "Aufgabeninfo", "emptyFavsTip": "Favoriten über den Button hinzufügen", "saveAndClose": "Speichern und schließen", "usersCanEditOverview": "Benutzer können die Übersicht bearbeiten", "assignedUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assignedOrgUnits": "Zugewiesene Organisationseinheiten", "assignedRoles": "Zugewiesene Rollen", "otherLangVariants": "Andere Sprachvarianten", "moveToSharing": "Zur Freigabe verschieben", "insertDocumentsPerm": "Benutzer hat die Berechtigung zum Einfügen von Dokumenten und Notizen", "saveNewPassword": "Neues Passwort speichern", "confirmSubscription": "Abonnement bestätigen", "subscriptionCaption": "Die ausgewählte Übersicht wird Ihnen zur festgelegten Zeit per E-Mail zugesandt.", "by": "Durch", "frequency": "<PERSON><PERSON><PERSON><PERSON>", "termination": "Beendigung", "ofTheMonth": "<PERSON><PERSON>", "endOnDate": "Ende am Datum", "endAfter": "Beenden nach", "onlyOnWorkingDays": "Nur an Werktagen", "occurrences": "Vorkommen", "dayOfWeekBy": "Tag der Woche durch", "calendarDayBy": "Ka<PERSON>der<PERSON> durch", "dateBy": "Datum", "byDate": "<PERSON><PERSON>", "byOccurrenceCount": "<PERSON><PERSON> Anzahl der Vorkommen", "infinitely": "<PERSON><PERSON><PERSON>", "dayOfMonthAdornment": ". Tag des Monats", "ordinalAdornment": ".", "toDateBeforeFromError": "Das 'Bis' Datum darf nicht vor dem 'Von' Datum liegen", "vice": "<PERSON><PERSON><PERSON><PERSON>", "previewShown": "Vorschau angezeigt", "duplicate": "Duplizieren", "hideBtn": "Verstecken", "userView": "Benutzeransicht", "adminView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "or": "<PERSON><PERSON>", "overlappingVicesError": "Die Vertreter dürfen sich nicht überschneiden", "fileVar": "<PERSON><PERSON>", "nodeVar": "Node", "uploadDifferentFile": "Andere Datei hochladen", "uploadedFile": "Hochgeladene Datei", "refreshPage2": "Seite aktualisieren", "refreshPageCaption": "Bitte aktualisieren Sie die Seite in Ihrem Browser, um fortzufahren.", "ttCopy": {"heading": "<PERSON><PERSON><PERSON>", "body": "Ermöglicht das Kopieren des ausgewählten Elements mit der Möglichkeit, einige Parameter zu bearbeiten."}, "alrError_INVALID_CSV_MAPPING": "Keine CSV -Spalte '%s' bei der Zuordnung des Ereignisses. <PERSON><PERSON> sich an den Anwendungsadministrator.", "documentPreview": "Dokumentvorschau", "moveUp": "Aufwärts bewegen", "moveDown": "Abwärts bewegen", "moveToFilter": "Zum Filter verschieben", "moveToSorting": "Zur Sortierung verschieben", "addSorting": "Sortierung hinzufügen", "cancelFilters": "Filter abbrechen", "docUploadedImmediately": "Das Dokument wird sofort hochgeladen.", "moreOptions": "Mehr Optionen", "docSearchPlaceholder": "Z. B. Rechnung.pdf …", "tasksSearchPlaceholder": "Z. B. eine neue Rechnung e<PERSON>ben …", "docUploadedImmediatelyPrivate": "Das Dokument wird sofort als private Dokument hochgeladen", "takeTsk": "Aufgabe übernehmen", "tasksActive": "Aktive Aufgaben", "subprocesses": "Subprozesse", "cancelAuthorization": "Autorisierung abbrechen", "cancelAuthorizationConfirm": "Sind <PERSON> sic<PERSON>, dass Sie die Geräteautorisierung abbrechen möchten?", "linkMobileApp": "Mobile App verknüpfen", "mobileApp": "Mobile App", "scanThisQr": "Scannen Sie diesen QR-Code mit Ihrem Mobilgerät.", "scanningQr": "QR-Code scannen. Bitte warten.", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "newDeviceName": "<PERSON><PERSON>er Gerätename", "registrationDate": "Registrierungsdatum", "lastLogin": "Letzte Anmeldung", "mobileNotifications": "Mobile Benachrichtigungen", "disableMobileNotification": "Benachrichtigungen auf dem Handy ausschalten", "newQrCode": "Neuer QR-Code", "inactiveScanQr": "Inaktives – QR-Code scannen.", "enableNotifications": "Benachrichtigungen aktivieren", "tip": "Tipp: {{message}}", "alrFavContainerAlreadyExists": "Der Favoriten-Container existiert bereits.", "addGraph": "<PERSON>", "newRow": "Neue Zeile", "confirmSetDefaultDashboard": "Möchten Sie das aktuelle Dashboard wirklich als Standard für alle Benutzer festlegen?", "changeMayAffectAllUsers": "Diese Änderung kann alle Benutzer beeinflussen.", "noOverviewsTip": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> eine neue Übersicht mit der Schaltfläche \"Übersicht hinzufügen\"", "removeFromHidden": "Aus den ausgeblendeten entfernen", "last7Days": "Letzte 7 Tage", "last14Days": "Letzte 14 Tage", "last30Days": "Letzte 30 Tage", "lastCalendarMonth": "Letzter Kalendermonat", "lastQuarter": "Letztes Quartal", "last12Months": "Letzte 12 Monate", "lastCalendarYear": "Letztes Kalenderjahr", "noFilterSet": "<PERSON><PERSON> f<PERSON>", "noSortingSet": "<PERSON>ine Sortierung festgelegt", "deleteGroup": "Gruppe löschen", "newGroup": "Neue Gruppe", "operators": "Operatoren", "withActiveTask": "Mit aktiver Aufgabe", "withoutActiveTask": "Ohne aktive Aufgabe", "withNoTerm": "<PERSON><PERSON>", "withTerm": "<PERSON><PERSON>", "securityAndAuthentication": "Sicherheit und Authentifizierung", "dataIntegrationAndManagement": "Datenintegration und -verwaltung", "appManagementAndConfig": "App-Verwaltung und Konfiguration", "monitoringAndMaintenance": "Überwachung und Wartung", "adminSearchPlaceholder": "<PERSON><PERSON> Be<PERSON>l, Öffentliche Dateien...", "authenticationAdminDescription": "Optionen zur Benutzerauthentifizierung", "certificatesAdminDescription": "Zertifikate für TAS", "elasticsearchAdminDescription": "Integration mit Elasticsearch", "xmlProcessImportAdminDescription": "Import von XML-Prozessen unter Verwendung des XMLProcessImport.js-Cron-Eintrags", "structureImportExportAdminDescription": "Import/Export von Organisationsstruktur, Benutzern und Rollen", "dmsAttributesAdminDescription": "Liste der Dokumentattribute in DMS", "dynTablesAdminDescription": "Datenspeicherung in dynamischen Tabellen", "csvAdminDescription": "Manipulation mit CSV-Dateien in der Anwendung", "configurationAdminDescription": "Anwendungs-Konfiguration", "settingsAdminDescription": "Einstellungen zur Unternehmensidentifikation und andere administrative Aufgaben", "logsAdminDescription": "Verwaltung und Anzeige von Anwendungsprotokollen", "migrationsAdminDescription": "<PERSON><PERSON> <PERSON> und Anwendungskonfigurationen", "guidesAdminDescription": "Hilfe und Anleitungen für Benutzer", "schemeAdminDescription": "Farbschema, Logo und andere Elemente in der Anwendung", "sequencesAdminDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>, die in Vorlagen verwendet werden", "serviceConsoleAdminDescription": "Anwendungsadministrative Befehle über die Serviskonsole", "serviceOperationsAdminDescription": "Umfassendes Management von Serviceoperationen", "scriptsAdminDescription": "Verwaltung wiederholt verwendeter Skripte über verschiedene Vorlagen hinweg", "appStatusAdminDescription": "Informationen zum aktuellen Status der Anwendung", "usageStatsAdminDescription": "<PERSON><PERSON><PERSON> von Statistiken zur Anwendungsverwendung", "maintenanceAdminDescription": "Wartungseinstellungen und Ausführung von Wartungsaufgaben", "scheduledTasksAdminDescription": "Verwaltung aller geplanten Aufgaben", "publicFilesAdminDescription": "Öffentliche Dateien und Dokumentation verwalten", "cronsAdminDescription": "Regelmäßige Aufgaben automatisieren", "hrAgendaAdminDescription": "Benutzeragendenverwaltung innerhalb der Personalabteilung", "emailsQueueAdminDescription": "E-Mail-Warteschlangenverwaltung und gesamte E-Mail-Kommunikation von TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Element zu Favoriten hinzufügen fehlgeschlagen", "alrRemoveFavItemFailed": "Element aus Favoriten entfernen fehlgeschlagen", "alrAddHiddenItemFailed": "Verstecktes Element konnte nicht hinzugefügt werden", "alrRemoveHiddenItemFailed": "Ausgeblendetes Element konnte nicht entfernt werden", "display": "Anzeige", "compact": "Kompakt", "standard": "Standard", "comfortable": "Komfortabel", "exportTo": "Exportieren nach", "adminMenuTip": "Fügen Sie Ihre Elemente in der Verwaltung zu Favoriten hinzu. Durch Klicken auf den Stern wird das Element hier direkt angezeigt.", "editorDocumentation": "Editor-<PERSON><PERSON><PERSON>", "addSection": "Abschnitt hinzufügen", "insertSection": "Abschnitt einfügen", "section": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sections": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toTop": "<PERSON>", "toEnd": "<PERSON>", "alrSectionNotBeEmpty": "<PERSON> Abschnitt darf nicht leer sein", "confirmDeleteSection": "Möchten Sie den Abschnitt wirklich löschen?", "sectionVarsMoveAllTasks": "Variablen in allen Aufgaben werden vom entfernten Abschnitt auf Variablen ohne Abschnitt verschoben.", "sectionVarsMove": "Die Variablen werden ohne Abschnitt vom entfernten Abschnitt in die Variablen verschoben.", "actionCannotUndone": "Diese Aktion kann nicht rückgängig gemacht werden.", "overviewOfAllNews": "Übersicht aller Nachrichten", "copyOverview": "Übersicht kopieren", "create": "<PERSON><PERSON><PERSON><PERSON>", "copyExistingOverview": "Vorhandene Übersicht kopieren", "selectOverview": "Übersicht auswählen", "chooseFromOverviews": "Aus Übersichten auswählen…", "selectTemplate": "Vor<PERSON> verwenden", "chooseFromAvailableTemplates": "Aus verfügbaren Vorlagen auswählen ...", "loginWithUsernamePassword": "Mit Benutzername und Passwort anmelden", "signInWithCorporateIdentity": "Mit Corporate Identity anmelden", "whatsNewInTAS": "Was ist neu in TAS?", "whatsNewInTASDescription": "Updates, neue <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> und alles, was <PERSON><PERSON> wissen mü<PERSON>.", "justOpen": "<PERSON><PERSON>", "editOverview": "Übersicht bearbeiten", "noGraphsTip": "<PERSON><PERSON><PERSON><PERSON> ein neues Diagramm mit der Schaltfläche „Diagramm hinzufügen“", "noDocumentsTip": "Fügen Sie ein Dokument zur Aufgabe hinzu oder verwenden Sie die Schaltfläche „Hinzufügen“", "noFilesTip": "Fügen Sie eine neue Datei über die Schaltfläche „Hinzufügen“ hinzu", "less": "<PERSON><PERSON>", "notContains": "Beinhaltet nicht", "factorySettings": "Werkseinstellungen", "previewCollapsedNavMenu": "Vorschau des gepackten Navigationsmenüs", "previewExpandedNavMenu": "Vorschau des ausgepackten Navigationsmenüs", "logoForCollapsedNavMenu": "Logo für gepacktes Navigationsmenü", "logoForExpandedNavMenu": "Logo für ausgepackte Navigationsmenü", "organisationLogo": "Organisationslogo", "pickLogoOrganisation": "Auswahl des Logos für die Organisation", "addLogo": "<PERSON><PERSON>", "clickForAddLogoOrDrop": "<PERSON><PERSON><PERSON> hier, um hier Logo oder Dropdatei hinzuzufügen", "useLogoSizeMin": "Verwenden Sie ein Logo mit Mindestgröße", "logoForLightTheme": "Logo für den hellen Modus", "logoForDarkTheme": "Logo für den Dunklen Modus", "notEquals": "<PERSON>t nicht gleich", "sharedWithMe": "<PERSON>t mir get<PERSON>t", "myOverview": "<PERSON><PERSON>", "getMobileAppText": "Holen Si<PERSON> sich die mobile App aus dem App Store", "noDocuments": "<PERSON><PERSON>", "noNotes": "<PERSON><PERSON>", "noFiles": "<PERSON><PERSON>", "addFirstDocument": "Erstes Dokument hinzufügen", "killed": "Getötet", "chooseNewLogo": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein neues Logo aus", "function": "Funktion", "groupFunction": "Funktion zwischen Gruppen", "mobileAppAuthFailed": "Authentifizierung der mobilen App fehlgeschlagen.", "currentDocumentVersion": "Aktuelle Dokumentversion", "csp": "Inhaltssicherheitsrichtlinie", "documentsDelete": "Dokumente löschen", "confirmDocumentsDelete": "Möchten Sie die ausgewählten Dokumente wirklich löschen?", "confirmDocumentsDownload": "<PERSON><PERSON>chten Sie die ausgewählten Dokumente herunterladen?", "firstNum": "erste {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Dokumente herunterladen", "caseLogs": "Falllogs", "archiveCases": "<PERSON><PERSON><PERSON> archivieren", "archive": "Archivieren", "unarchive": "Dearchivieren", "confirmArchiveCases": "Möchten Sie die ausgewählten Fälle wirklich archivieren?", "archiveInDays": "Archivieren in (Tagen)", "archived": "<PERSON><PERSON><PERSON><PERSON>", "archivedx": "Archivierte", "alrArchivingCase": "Fall wird archiviert...", "alrCaseArchived": "Fall wurde archiviert.", "alrLackOfPermsToArchiveCase": "Sie haben nicht genügend Berechtigungen, um den Fall zu archivieren.", "alrArchiveCaseFailed": "Archivierung des Falls fehlgeschlagen.", "alrUnarchivingCase": "Fall wird dearchiviert...", "alrCaseUnarchived": "Fall wurde dearchiviert.", "alrLackOfPermsToUnarchiveCase": "Sie haben nicht genügend Berechtigungen, um den Fall zu dearchivieren.", "alrUnarchiveCaseFailed": "Dearchivierung des Falls fehlgeschlagen.", "byUser": "<PERSON><PERSON>", "byAgenda": "<PERSON><PERSON>", "agendaHandover": "Agenda-Übergabe", "activeUsers": "Aktive Benutzer", "lockedUsers": "<PERSON><PERSON><PERSON><PERSON>", "allUsers": "<PERSON><PERSON>", "inactiveUsers": "Inaktive Benutzer", "hrAgendaSearchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> ...", "completeAgendaHandover": "Agenda-Übergabe abschließen", "handoverCases": "Übergabefälle", "handoverTasks": "Übergabeaufgaben", "handoverVars": "Übergabevariablen", "changeTaskOwner": "Aufgabenlöser ändern", "confirmHandover": "Übergabe bestätigen", "filterCasesByHeaderTip": "Sie können alle Fälle unter demselben Header in der Spalte Header filtern.", "userAgendaSelectedHandover": "Übergabe der <b style=\"color: {{color}};\">ausgewählten</b> Benutzeragenda", "userAgendaCompleteHandover": "Übergabe der <b style=\"color: {{color}};\">vollständigen</b> Benutzeragenda", "confirmAgendaHandover": "<PERSON>d <PERSON> sic<PERSON>, dass Sie die ausgewählte Agenda ({{selected}}) an den Benutzer <b>{{newUser}}</b> übergeben möchten?", "confirmUserAgendaSelectedHandover": "<PERSON>d <PERSON> sic<PERSON>, dass <PERSON> die <b>ausgewählte</b> Agenda des Benutzers <b>{{user}}</b> an den Benutzer <b>{{newUser}}</b> übergeben möchten?", "confirmUserAgendaCompleteHandover": "<PERSON>d <PERSON><PERSON> sic<PERSON>, dass Si<PERSON> die <b>vollständige</b> Agenda des Benutzers <b>{{user}}</b> an den Benutzer <b>{{newUser}}</b> übergeben möchten?", "refreshSessionTitle": "TAS-<PERSON><PERSON>ng wird in {{minutes}} Minuten beendet.", "refreshSessionCaption": "Klicken Sie auf \"Weiterarbeiten\", um ohne Unterbrechungen fortzufahren.", "continueWorking": "Weiterarbeiten", "sessionExpiredCaption": "Klicken Sie auf \"Erneut anmelden\", um zum Anmeldebildschirm zurückzukehren.", "loginExpired": "Wir haben Si<PERSON> nach einer langen Zeit der Inaktivität abgemeldet.", "confirmArchiveCase": "Möchten Sie den ausgewählten Fall wirklich archivieren?", "isLowerOrEqualThan": "<PERSON>ss kleiner oder gleich sein", "confirmUnarchiveCase": "Möchten Sie den ausgewählten Fall wirklich dearchivieren?", "addCaseRightNewUserTooltip": "<PERSON>n Si<PERSON> diese Option nicht aktivieren, wird der neue Benutzer in der Geschäftsvariablen ersetzt, hat aber keinen Zugriff auf den Fall.", "canBeViced": "Ich werde vertreten", "canVice": "Vertreten", "backgroundColor": "Hintergrundfarbe", "defaultDashboardView": "Vorschau des Standard -Dashboards", "colorScheme": "Farbschema", "displaySelectionAsTags": "Auswahl als Tags anzeigen", "displayAsPassword": "Als Passwort anzeigen", "sideBySide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyAssignmentFromTask": "Zuweisung aus Aufgabe kopieren", "toTask": "Zur Aufgabe", "copyTaskAssignmentWarning": "Die Zuweisung in der Aufgabe ist nicht leer, möchten Sie sie überschreiben?", "copyToOtherTasks": "In andere Aufgaben kopieren", "noteScriptsNotApplied": "Hinweis: Skripte sind nicht angewendet", "generateRecHistory": "In aktiven Aufgaben und im Verlauf anzeigen", "leaveFormerRoles": "<PERSON><PERSON><PERSON> beibe<PERSON>en", "includeCompetences": "Kompetenzen einbeziehen", "copyRoles": "<PERSON><PERSON> kopieren", "userIsActive": "Benutzer ist aktiv", "systemUser": "<PERSON><PERSON><PERSON><PERSON>", "copyRolesFromUser": "Rollen vom Benutzer kopieren", "assignedRolesOverview": "Übersicht der zugewiesenen Rollen", "copyRolesInfo": "Wenn der angegebene Benutzer Teil von Kompetenzen ist, werden diese Kompetenzen werden nicht sofort kopiert. Sie werden generiert:", "notificationOn": "Befestigt", "notificationOff": "<PERSON><PERSON><PERSON><PERSON>", "onNotification": "Benachrichtigung", "offNotification": "Benachrichtigung", "page": "Seite", "fromTo": "<PERSON> <PERSON> <PERSON>", "isAnyOfValue": "Ist ein Wert von einem Wert von", "notcontains": "enthält nicht", "notequals": "ungleichmäßig", "fromto": "<PERSON> <PERSON> <PERSON>", "isanyofvalue": "ist ein Wert von einem Wert von", "alrNoteToggleVisibiltyFailed": "Das Ausblenden/Einblenden der Anmerkung ist fehlgeschlagen", "alrNoteHideOnEditFailed": "Das Ausblenden der ursprünglichen Anmerkung ist fehlgeschlagen", "hiddenShe": "Versteckt", "showHiddenNotes": "Versteckte Anmerkungen anzeigen", "alrNoteEdited": "Die bearbeitete Version der Anmerkung wurde gespeichert", "notesEdit": "Anmerkung bearbeiten", "displayName": "Anzeigename", "clientDateFormat": "Datumsformat", "defaultByLanguage": "Standardmäßig nach Sprache", "restKeysOptionsNotUpToDate": "Veraltete Auswahl der Werte - Dienst neu laden.", "invalidValue": "Ungültiger Wert", "ended": "<PERSON><PERSON>", "exportAllActive": "Alle aktiven exportieren", "alrScriptsLoadFailed": "Die Skripts konnten nicht eingelesen werden.", "scriptsImport": "<PERSON><PERSON><PERSON>", "doImport": "Importieren", "alrImportingScripts": "Import<PERSON><PERSON> von <PERSON>...", "alrScriptsImported": "Skripte wurden importiert.", "alrScriptsImportFailed": "<PERSON><PERSON><PERSON> von Skripten fehlgeschlagen.", "removeAll": "<PERSON><PERSON> ent<PERSON>", "alrNoScriptsToImport": "<PERSON><PERSON> Skrip<PERSON> zum Importieren.", "activateAll": "Alle aktivieren", "alrNoPermsToEditNoteInVice": "Sie haben keine Berechtigung, die Anmerkung in der Vertretung zu bearbeiten.", "alrNoPermsToToggleNoteVisibilityInVice": "Sie haben keine Berechtigung, die Anmerkung in der Vertretung auszublenden/einzublenden.", "plusMore": "weitere", "variableAlignment": "Variable Ausrichtung", "variableAlignmentHelp": "Beeinflusst die Ausrichtung des Variablenwerts im Aufgabenformular.", "variableAlignmentLeft": "Links", "variableAlignmentRight": "<PERSON><PERSON><PERSON>", "tasksMineAndToPull": "Meine + <PERSON><PERSON>", "myDevice": "<PERSON><PERSON>", "deleteLogo": "Logo löschen", "namingFilter": "Filtername", "exceptionsToRegularSchedule": "Ausnahmen vom regulären Zeitplan", "noExceptions": "<PERSON><PERSON>", "specificDates": "Bestimmte Daten", "dateFromTo": "<PERSON><PERSON> von - bis", "weekdayCap": "Wochentag", "specificDayBy": "Bestimmter Tag", "yearsBy": "Jahre", "timed": "Zeitgesteuert", "firstDayOfMonth": "Erster Tag des Monats", "lastDayOfMonth": "Letzter Tag des Monats", "firstDayOfYear": "Erster Tag im Jahr", "lastDayOfYear": "Letzter Tag im Jahr", "addDate": "<PERSON><PERSON> hinz<PERSON>", "newPlan": "Neuer Plan", "addAnother": "<PERSON><PERSON><PERSON> hinzufügen", "startTime": "Startzeit", "endTime": "Endzeit", "inTimeFromTo": "in der Zeit von {{from}} bis {{to}}", "dayOfMonthBy": "Tag des Monats", "cWorkDays": "Arbeitstage", "cWeeks": "<PERSON><PERSON><PERSON>", "cMonths": "Monate", "cYears": "Jahre", "everyWeek": "jede <PERSON>", "everyYear": "j<PERSON><PERSON>", "inMonth": "im Monat", "everyDay": "jeden <PERSON>", "seqIdEdit": "Sequenz-ID bearbeiten", "allowMultiselectSearchRight": "<PERSON><PERSON> in der Zuordnung zulassen", "doubleHeightForContent": "Doppelte Höhe für Inhalt", "alrNoVariablesMappingToImport": "<PERSON><PERSON> Variablenzuordnungen zum Importieren.", "alrVariablesMappingImportLoadFailed": "Das Laden der Variablenzuordnungen zum Importieren ist fehlgeschlagen.", "variablesMappingImport": "Import der Variablenzuordnungen", "useAllMappings": "Alle Zuordnungen verwenden", "doExportVariablesMapping": "Variablenzuordnungen exportieren", "alrImportingVariablesMapping": "Import der Variablenzuordnungen läuft...", "alrVariablesMappingImported": "Variablenzuordnungen wurden importiert.", "alrVariablesMappingImportFailed": "Der Import der Variablenzuordnungen ist fehlgeschlagen.", "alrVariablesMappingImportedPartially": "Die Variablenzuordnungen wurden nur teilweise importiert. Einige Variablen wurden nicht gefunden.", "alrEditorHintsLoadFailed": "Editorhinweise konnten nicht geladen werden.", "addTable": "<PERSON><PERSON><PERSON>", "confirmDynTablesDelete": "Möchten Sie die ausgewählten dynamischen Tabellen wirklich löschen?", "dynTablesDelete": "Dynamische Tabellen löschen", "addRow": "<PERSON><PERSON><PERSON>", "preview": "Vorschau", "columnDelete": "Spalte löschen", "editRow": "<PERSON><PERSON><PERSON> bear<PERSON>", "addingNewColumn": "Neue Spalte hinzufügen", "addingNewRow": "Neue Zeile hinzufügen", "columnsRename": "Spalten umbenennen", "rowCellValues": "Zellenwerte der Zeile", "saveDynTableName": "Dynamischen Tabellennamen speichern", "saveDynTableNameQ": "Dynamischen Tabellennamen speichern?", "saveDynTableNameWarning": "<PERSON><PERSON><PERSON>, stel<PERSON>, dass die Änderung des Tabellennamens keine Auswirkungen auf bestehende Berechnungen in Vorlagen hat.", "rowMove": "<PERSON><PERSON>e versch<PERSON>", "alrCsvParsingErr": "Fehler beim Parsen der CSV-Datei!", "addFirstTableColumn": "Füge die erste Tabellenspalte hinzu", "my": "<PERSON><PERSON>", "license": "<PERSON><PERSON><PERSON>", "licenses": "<PERSON><PERSON><PERSON>", "addLicense": "<PERSON><PERSON><PERSON>", "licenseResult": "Lizenzergebnis", "alrLicenceResultLoadingFailed": "Lizenzergebnis konnte nicht geladen werden.", "licensesAdminDescription": "Lizenzverwaltung", "uploadByDragging": "Datei durch Z<PERSON>hen hochladen.", "uploadByDraggingAnywhere": "Laden Si<PERSON> eine Date<PERSON> hoch, indem Sie sie an eine beliebige Stelle im Raum z<PERSON>hen.", "assignVariable": "Variable zu<PERSON>sen", "confirmDeleteSectionName": "Möchten Sie Abschnitt <b>\"{{section}}\"</b> wirk<PERSON> löschen?", "deleteSectionWarning": "Warnung: Der Abschnitt wird für alle betroffenen Aufgaben einschließlich Variablen gelöscht.", "tasksAffected": "Betroffene Aufgaben", "varSearchPlaceholder": "Zum Beispiel Rechnungsstellung …", "enlarge": "Vergrößern", "show": "Anzeigen", "shrink": "Verkleinern", "hide": "Verstecken", "doValidate": "Validieren", "phoneNumber": "Telefonnummer", "textLength": "Textlänge", "when": "wenn", "to2": "zu", "that": "dass", "dynCondBuilderBlockFunctionDescShow": "Zeigt die Variable an, wenn die Bedingung erfüllt ist.", "dynCondBuilderBlockFunctionDescHide": "Versteckt die Variable, wenn die Bedingung erfüllt ist.", "dynCondBuilderBlockFunctionDescChange": "<PERSON><PERSON>t den Wert der Variable, wenn die Bedingung erfüllt ist.", "dynCondBuilderBlockFunctionDescValidate": "Validiert den Wert der Variable.", "addCondition": "Bedingung hinzufügen", "operator": "Operator", "equals": "gle<PERSON>", "greaterthan": "gr<PERSON><PERSON><PERSON> als", "greaterorequal": "gr<PERSON><PERSON><PERSON> oder gleich", "lessthan": "kleiner als", "lessorequal": "kleiner oder gleich", "demoCode": "Democode", "code": "Code", "confirmDeleteConditions": "Möchten Sie wirklich alle Bedingungen (einschließlich Skript) löschen?", "validationErrorMessage": "Fehlermeldung bei Validierung", "alrScriptToStructuredBlockConversionFailed": "Die Konvertierung des Skripts in einen strukturierten Block ist fehlgeschlagen.", "alrStructuredBlockToScriptConversionFailed": "Die Konvertierung des strukturierten Blocks in ein Skript ist fehlgeschlagen.", "alrScriptToBuilderConversionFailed": "Die Konvertierung des Skripts in den Builder ist fehlgeschlagen.", "alrBuilderToScriptConversionFailed": "Die Konvertierung vom Builder in ein Skript ist fehlgeschlagen.", "dynCondBuilderBlockFunctionDescScript": "Skriptblock für dynamische Bedingungen.", "convertToStructuredBlock": "In strukturierten Block umwandeln", "convertToScript": "In Skript umwandeln", "dynCondBuilderBlockWatchVarsLabel": "Bei Änderung ausführen (watchVars)", "variables": "Variablen", "copyToOthers": "<PERSON><PERSON>", "sectionName": "Abschnittsname", "newSectionName": "Name des neuen Abschnitts", "testIt": "Prüfen", "addAdjacentSection": "<PERSON>ügen Si<PERSON> einen benachbarten Abschnitt hinzu", "addAdjacentSectionBelow": "Fügen Sie den benachbarten Abschnitt unten hinzu", "selectExistingSection": "Wählen Si<PERSON> einen vorhandenen Abschnitt aus", "renameSectionWarning": "Hinweis: Der Abschnitt wird in allen Vorlagenaufgaben umbenannt.", "warning2": "<PERSON><PERSON>", "copyAssignmentToTask": "Kopieren Sie die Zuordnung zur Aufgabe", "copyAlsoConditions": "Kopie und Bedingungen", "copyAssignmentToTaskWarning": "Hinweis: Zuordnung und möglicherweise dynamische Bedingungen in der ausgewählten Aufgabe werden umgeschrieben.", "importFromOtherTask": "Von einer anderen Aufgabe importieren", "startFromScratch": "Beginnen Sie <PERSON> an", "howToStartAssignments": "Wie möchten <PERSON> anfangen, Variablen zuzu<PERSON>?", "selectTaskToImport": "Wählen Sie die Aufgabe für den Import aus", "confirm": "Bestätigen", "selectTaskToTest": "Um eine Aufgabe zum Testen auszuwählen", "toTestSaveChanges": "Änderungen sollten zum Testen gespeichert werden.", "variableAssignmentTest": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "viewAsMobile": "Ansi<PERSON> wie auf dem Handy", "viewAsPc": "Ansicht wie auf dem <PERSON>", "emptySpace": "<PERSON><PERSON>", "variableAssignments": "Variable<PERSON>", "allowCompletionOnChangeOf": "Erlauben Sie die Erfüllung bei Änderung von", "dynCondBuilderBlockFunctionDescRead": "<PERSON><PERSON><PERSON> den Modus der Variablen auf \"nur lesen\", wenn die Bedingung erfüllt ist.", "dynCondBuilderBlockFunctionDescWrite": "<PERSON><PERSON>t den Modus der Variablen auf \"lesen und schreiben\", wenn die Bedingung erfüllt ist.", "dynCondBuilderBlockFunctionDescMust": "<PERSON><PERSON><PERSON> den Modus der Variablen auf \"erforderlich\", wenn die Bedingung erfüllt ist.", "dynCondBuilderBlockFunctionDescSolve": "Ermöglicht die Erfüllung der Aufgabe bei Änderung der Variablen, wenn die Bedingung erfüllt ist.", "newsManagement": "Nachrichtenverwaltung", "newsManagementAdminDescription": "<PERSON>er<PERSON><PERSON><PERSON> von Nachrichten in der Anwendung", "addNewsPost": "Nachricht hinzufügen", "newPost": "<PERSON>euer Beitrag", "news": "Nachrichten", "basicInfo": "Grundinformation", "publicationPlanning": "Veröffentlichungsplanung", "displayToUsers": "Benutzern anzeigen", "displayLocation": "Anzeigeposition", "newsPostContent": "Nachrichteninhalt", "postTitle": "Beitragstitel", "newsManagementPostDetailPhoneNumberTooltip": "Telefonnummer zur Anzeige im Nachrichten-Detail", "newsManagementPostDetailEmailTooltip": "E-Mail zur Anzeige im Nachrichten-Detail", "customUrlLink": "Benutzerdefinierter URL-Link", "newsManagementPostDetailCustomUrlLinkTooltip": "Benutzerdefinierter URL-Link zur Anzeige im Nachrichten-Detail", "stateAfterSaving": "Status nach dem Speichern", "newsPostStateActive": "Aktiv", "newsPostStateInactive": "Nicht aktiv", "newsPostStatePlanned": "<PERSON><PERSON><PERSON>", "endNewsPostOnSpecificDate": "<PERSON><PERSON><PERSON><PERSON> zu einem bestimmten Datum beenden", "sendNewsPostViaEmail": "Nachricht per E-Mail senden", "priorityNewsPost": "Prioritätsnachricht", "newsManagementPostDetailPriorityNewsTooltip": "Zum Beispiel zur Ankündigung von Wartungsarbeiten oder Änderungen im Arbeitsablauf", "newsPostEndDate": "Enddatum der Nachricht", "pickNewsPostDisplayToOrgUnits": "Welchen Organisationseinheiten soll die Nachricht angezeigt werden?", "pickNewsPostDisplayToRoles": "Welchen Rollen soll die Nachricht angezeigt werden?", "pickNewsPostDisplayToUsers": "Welchen Benutzern soll die Nachricht angezeigt werden?", "pickNewsPostDisplayOnTemplate": "Auf welcher Vorlage soll die Nachricht angezeigt werden?", "pickNewsPostDisplayOnHeaders": "Auf welchen Kopfzeilen soll die Nachricht angezeigt werden?", "pickNewsPostDisplayOnTasks": "Auf welchen Aufgaben soll die Nachricht angezeigt werden?", "pickNewsPostDisplaySubOptionsHelperText": "<PERSON><PERSON><PERSON>en Sie zu<PERSON>t die Vorlage aus, auf der die Nachricht angezeigt werden soll.", "newsTagsManagement": "Verwal<PERSON><PERSON> von Nachrichtentags", "newsTagsManagementAdminDescription": "Verwaltung von Nachrichtentags in der Anwendung", "addTag": "Tag hinzufügen", "tags": "Tags", "publicationDate": "Veröffentlichungsdatum", "contacts": "Kontakte", "avaibleUntil": "Verfügbar bis", "published": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newsSinceLastVisitAmount": "Insgesamt {{amount}} Nachrichten seit dem letzten Besuch", "noNews": "<PERSON><PERSON>", "createNewTag": "Neuen Tag erstellen", "tagName": "Tag-Name", "alrTagSaved": "Das Tag wurde gespeichert.", "alrTagSaveFailed": "Das Tag konnte nicht gespeichert werden.", "confirmDeleteTag": "Möchten Sie das Tag \"{{tagName}}\" wirklich löschen?", "alrPostSaved": "Der Beitrag wurde gespeichert.", "alrPostSaveFailed": "Das Speichern des Beitrags ist fehlgeschlagen.", "alrLoadingTagsFailed": "Das Laden der Tags ist fehlgeschlagen.", "confirmDeletePost": "Möchten Sie den Beitrag \"{{postTitle}}\" wirklich löschen?", "confirmDeleteMultiplePosts": "Möchten Sie die ausgewählten Beiträge wirklich löschen?", "post": "Beitrag", "alrPostLoadFailed": "Das Laden des Beitrags ist fehlgeschlagen.", "alrTagDeleted": "Das Tag wurde gelöscht.", "alrTagDeleteFailed": "Das Tag konnte nicht gelöscht werden.", "alrPostDeleted": "Der Beitrag wurde gelöscht.", "alrPostDeleteFailed": "Das Löschen des Beitrags ist fehlgeschlagen.", "alrPostsDeleted": "Die ausgewählten Beiträge wurden gelöscht.", "alrPostsDeleteFailed": "Das Löschen der ausgewählten Beiträge ist fehlgeschlagen.", "alrTempTasksLoadFailed": "Die Aufgaben der Schablone konnten nicht eingelesen werden.", "rolesRestriction": "Einschränkung für Rollen", "usersRestriction": "Einschränkung für Benutzer", "orgUnitsRestriction": "Einschränkung für Organisationseinheiten", "alrPriorityNewsLoadFailed": "Das Laden der Prioritätsnachrichten ist fehlgeschlagen.", "moreInfo": "Mehr Informationen", "tas5Info": "TAS 5.0 ist da ...", "totalNewsAmount": "Insgesamt {{amount}} Nachrichten", "alrNewsContainerPostsLoadFailed": "Das Laden der Beiträge für den Nachrichtencontainer ist fehlgeschlagen.", "alrTaskNewsLoadFailed": "Das Laden der Neuigkeiten für die Aufgabe ist fehlgeschlagen.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "Das Veröffentlichungsdatum muss vor dem Enddatum der Nachricht liegen.", "alrNotificationsNewsLoadFailed": "Das Laden der Neuigkeiten für Benachrichtigungen ist fehlgeschlagen.", "moreNews": "Weitere Neuigkeiten", "newsManagementPostDetailConfirmSavingWillSendMail": "Das Speichern des Beitrags führt dazu, dass eine E-Mail an alle Benutzer gesendet wird, für die der Beitrag bestimmt ist. Möchten Sie den Beitrag wirklich speichern?", "mailNewsNotification": "E-Mail-Benachrichtigung über Neuigkeiten", "mailNewsNotificationInfo": "Der Benutzer erhält kontinuierlich Neuigkeiten, die für ihn bestimmt sind.", "alrRefreshingConfig": "Die Konfiguration wird aktualisiert ...", "alrConfigRefreshed": "Die Konfiguration wurde erfolgreich aktualisiert.", "alrConfigRefreshFailed": "Die Aktualisierung der Konfiguration ist fehlgeschlagen.", "ttRefreshConfig": {"heading": "<PERSON><PERSON><PERSON> Sie die Konfiguration aus allen Quellen wieder her", "body": ""}, "getMobileAppTextQr": "Holen Si<PERSON> sich die mobile App aus dem App Store oder scannen Sie den QR-Code", "dateStart": "Startdatum", "dateEnd": "Enddatum", "tas_forms_generated": "Anzahl der automatisch generierten Formulare"}