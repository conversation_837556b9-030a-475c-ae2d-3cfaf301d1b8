{"1st": "1.", "2nd": "2.", "3rd": "3.", "4th": "4.", "AddToAllTasks": "Dodaj do wszystkich zadań", "OfVariable": "Zmiennej", "RemoveFromAllTasks": "Usuń ze wszystkich zadań", "TaskOwnerWhichInVar": "Do właściciela zadania, ustalonego w zmiennej", "action": "<PERSON><PERSON><PERSON><PERSON>", "active": "Aktywny", "activeShe": "Aktywny", "activePl": "Aktywny", "activity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "activityType": "<PERSON>p <PERSON>", "actualEnd": "Rzeczywisty czas na końcu", "actualSolver": "Rzeczywisty właściciel zadania", "actualStart": "Rzeczywisty czas rozpoczęcia", "actualTsks": "Obecne zadanie", "actualize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON>", "addAttribute": "<PERSON><PERSON><PERSON>", "addOrgUnit": "Dodaj jednostkę org.", "addPlan": "Dodaj plan", "addPrintTemplate": "<PERSON><PERSON><PERSON> w<PERSON>ruk<PERSON>", "addRole": "<PERSON><PERSON><PERSON> role", "addRule": "<PERSON><PERSON><PERSON>", "addTemp": "<PERSON><PERSON><PERSON>", "addTsk": "<PERSON><PERSON><PERSON>", "addUser": "Dodaj użytkownika", "addUserSomething": "Przypisz użytkownika do wybranego {{variable}}:", "addVariable": "<PERSON><PERSON><PERSON>", "after": "Po", "afterTermTasks": "Moje zadania po terminie ukończenia", "all": "Wszystkie", "allFiles": "Wszystkie pliki", "allMustBeMet": "Wszystkie musi być spełnione", "allMyTasks": "Wszystkie moje zadania", "allSubOfPlanGuar": "Wszyscy podwładni gwaranta planu", "allTasksWithNoTerm": "Wszystkie zadania bez terminu zakończenia", "allTasksWithTerm": "Wszystkie zadania z terminem zakończenia", "allTemps": "Wszystkie szablony", "allowMultiple": "Zezwól na wybór wielu opcji", "allowSelectAll": "Włącz wybór wszystkich elementów", "allsupOfPlanGuar": "Wszyscy przełożenia gwaranta planu", "alrBlockingAction": "Wykonuję akcje! Proszę czekać...", "alrActionNameNotDefined": "<PERSON><PERSON><PERSON><PERSON> „{{actionName}}“ nie jest zdefiniowana.", "alrActionNotDefined": "Działanie nie zostało zdefiniowane.", "alrApiUrlMissing": "Brak danych tabeli źródłowej.", "alrAssigningTsk": "Przypisywanie zadania...", "alrAssignmentFailed": "Zapisywnaie przypisania nie powisodło się", "alrAtrFailed": "Usunięcie atrybuu nie powiodło się", "alrAttachDeleteFailed": "Usunięcie dokumentu nie powiodło się", "alrAttachDeleted": "Dokument został usunięty!", "alrAttachDeleting": "<PERSON><PERSON><PERSON><PERSON> dokumentu...", "alrAttachDownloadFailed": "Pobieranie dokumentu nie powiodło się.", "alrAttachDownloaded": "Dokument został pobrany!", "alrAttachDownloading": "Pobieranie dokumentu...", "alrAttachMetaFailed": "Zapisywanie metadanych dokumentu nie powiodło się.", "alrAttachOrNotesCountFailed": "Przeliczanie dokumentów bądź notatek nie powiodło się.", "alrAttachSaveFailed": "Upload dokuemntu nie powiódł się.", "alrAttachSaved": "Dokument został załączony", "alrAttachTooBig": "Dokument jest za duży! Dokument przekroczył {{maxUploadSize}} MB.", "alrAttrDataFailed": "Wczytywanie danych atrybutu nie powiodło się", "alrAttrFailed": "Wczytywanie danych atrybutu nie powiodło się", "alrAttrSaveFailed": "Błąd w trakcie zapisywania atrybutu!", "alrAttrsLoadFailed": "Wczytywanie atrybutu nie powiodło się", "alrAttachRestored": "Dokument został przywrucony.", "alrAttachRestoreFailed": "Przywrócenie dokumentu nie powiodło się.", "alrAttachRestoring": "Przywracanie...", "alrAuthMethodsFailed": "<PERSON>e udało sie załadować metod uwierzytelniania.", "alrBadLogin": "Błedna nazwa użytkownika bądź hasło.", "alrBlockedPopups": "Prawdopodob<PERSON> Twoje okno pop-up jest z<PERSON><PERSON><PERSON><PERSON><PERSON>.", "alrCaseDataLoadFailed": "Ładowanie danycg sprawy nie powiodło się.", "alrCaseDeleteFailed": "Usuwanie sprawy nie powiodło się.", "alrCaseDeleted": "Sprawa została usunięta!", "alrCaseDeleting": "Usuwanie sprawy...", "alrCaseNameLoadFailed": "Wczytywanie nazwy sprawy nie powiodło się.", "alrCaseNoRights": "NIe posiadasz uprawnień do przeglądania sprawy nr {{id}}.", "alrCaseNotFound": "Sprawa nie została znaleziona – mogła zostać usunięta", "alrCaseOverviewFailed": "Wczytywanie danych sprawy tylko do odczytu (CASE OVERVIEW) nie powiodło się.", "alrCaseSuspended": "Sprawa została zawieszona!", "alrCaseVarsLoadFailed": "Wczytywanie zmiennych procesowcyh nie powiodło się.", "alrCaseWakeUpFailed": "Wybudzanie sprawy nie powiodło się.", "alrCaseWakedUp": "Sprawa została wybudzona!", "alrCaseWakingUp": "Wybudzanie sprawy...", "alrColsWidthsSettingsFailed": "Zapisywanie ustawienia szerokości kolumn nie powiodło się.", "alrConnToServerFailed": "Połoczenie z serwerem nie powiodło się.", "alrConnectionDataLoadFailed": "Ładowanie danych połączenia nie powiodło się.", "alrConnectionDeleteFailed": "Usunięcie połączenia nie powiodło się.", "alrConnectionDeleted": "Połączenie zostało usunięted!", "alrConnectionSaveFailed": "Zapisywanie połącznenia nie powiodło się!", "alrContainerNotFound": "Zasobnik nie został znaleziony.", "alrCsvDownloaded": "Plik CSV został pobrany!", "alrCvNotFound": "Przegląd nie zostal znaleziony.", "alrDashboardSettingsFailed": "Zapis pulpitu nie powiódł się.", "alrDefaultDashboardLoadFailed": "Wczytywanie domyślnego pulpitu nie powoiodło się.", "alrDefaultDashboardSaved": "Domyślny pulpit został zapisany!", "alrDeleteFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas usuwania.", "alrDeleted": "Usunięto!", "alrDeleting": "Usuwanie....", "alrDiagramDataLoadFailed": "Wczytywanie danychg do budowy diagramu nie powiodło się.", "alrDiagramEditToSave": "Diagram nie może być zapisany i przekształcony w szablon – zawiera wiecej niz jeden prosces! Prosze zaktualizuj diagram aby zawierał tylko jeden proces lub zaimportuj inny plik .bpmn file.", "alrDiagramInitFailed": "Zainicjiowanie diagramu nie powiodło się.", "alrDiagramMissingTaskName": "Proszę wyspełnić nazwę wszystkich zadań.", "alrDiagramErrors": "Schemat zawiera błędy. Popraw je i spróbuj ponownie zapisać.", "alrDiagramNotValid": "XML jest nieprawidłowy zgodnie z oficjalną specyfikacją BPMN 2.0!", "alrDiagramProcessCount": "Diagram nie może być zapisany i przekształcony w szablon – zawiera wiecej niz jeden prosces!", "alrDiagramSaveFailed": "Wys<PERSON>ą<PERSON>ł bład w trakcie zapisywania szablonu!", "alrDiagramTsksDeleteFailed": "Wystąpił błąd w trakcjie usuwania zadania!", "alrDiagramUnchanged": "Szablon pozostał niezmieniony.", "alrDmsColsLoadFailed": "Nie można załadować kolumny dla DMS.", "alrDocsColumnsIdsFailed": "Nie można załadować ID kolumn tabel dokumentów.", "alrDocumentAdding": "Zapisywanie dokumentu...", "alrDynListsDataLoadFailed": "Wczytywanie danych listy dynamicznej nie powiodło się.", "alrDynTableColsDataFailed": "<PERSON>e można załadować danych kolumn tabeli dynamicznej.", "alrDynTableDataLoadFailed": "Wczytywanie danych tabeli dynamicznej nie powiodło się.", "alrDynTableNotFound": "Nie odnaleziono tabeli dynamicznej.", "alrDynTablesDataLoadFailed": "Wczytywanie danych tabeli dynamicznej nie powiodło się.", "alrCalcScriptsDataLoadFailed": "Nie można załadować globalnych skryptów obliczeniowych.", "alrEditValues": "Prosz<PERSON> por<PERSON><PERSON> błednie wprowadzone wartości.", "alrEventSaveFailed": "Zapisywanie wydarzenia nie powiodło się.", "alrEventSaved": "Wydarzenie zostało zapisane!", "alrEventSaving": "Zapisywanie wydarzenia...", "alrEventTriggered": "Wydarzenie zostało uruchomione!", "alrExcelDownloaded": "Plik xlsx został pobrany!", "alrExportCompleted": "Eksport ukonczony.", "alrExportFailed": "Eksport nie powiódł się.", "alrExportPreparing": "Przygotowanie eksportu...", "alrFailed": "Akcja nie powdiodła się.", "alrFailedCalendarTask": "Wczytanie zadania do kalendarza nie powdiodło się.", "alrFailedCreatePrint": "Tworzenie wydruku nie powiodło się.", "alrFailedDLTotalCount": "Nie określono sumy całkowitej w liscie dynamicznej {{label}}, w związku z czym wszystkie rekordy zostały wczytane.", "alrFailedData": "Wczytywanie danych nie powiodło się.", "alrFailedEventStart": "Rozpoczecie wydarzenia nie powiodło się.", "alrFailedEventVariables": "Wczytywanie zmiennych wybranego wydarzenia nie powiodło się.", "alrFailedEvents": "Wczytywanie wydarzenia nie powiodło się.", "alrFailedFoldersData": "Wczytywanie folderów danych nie powiodło się.", "alrFailedFormData": "<PERSON>e udało się załadować danych formularza.", "alrFailedInitiatorName": "Nazwa inicjatora nie została załadowana.", "alrFailedLabelData": "Wczytywanie danych etykiet komponentów nie powiodło się.", "alrFailedLoad": "Wczytywanie danych wydruku nie powiodło się.", "alrFailedLogicalType": "Wczytywanie typu logicznego nie powiodło się.", "alrFailedMultiBoxData": "Wczytywanie danych MultiBox nie powiodło się.", "alrFailedNewCase": "Wystąpił błąd podczas konfigurowania nowej sprawy!", "alrFailedNewFolder": "Zmiana nazwy folderu nie powiodła się.", "alrFailedNoticeData": "Wczytywanie danych ostrzegawczych nie powiodło się.", "alrFailedOrgUnitUser": "Wczyttywanie jednostki organizacyjnej użytkownika nie powiodło się.", "alrFailedOverviewData": "Wczytywanie podglądu nie powdiodło się.", "alrFailedPlanData": "Wczytywanie danycg planu nie powiodło się.", "alrFailedPostData": "Wysyłanie danych nie powiodło się.", "alrFailedPrintData": "<PERSON><PERSON> mo<PERSON><PERSON> za<PERSON><PERSON> danych wydruku.", "alrFailedRevisionInfo": "Wczytywanie informacji o nowej wersji nie powiodło się.", "alrFailedSearchBoxData": "Wczytywanie danych komponentu SearchBox nie powiodło się.", "alrFailedSelectBoxData": "Wczytywanie danych komponentu SelectBox nie powiodło się.", "alrFailedSuggestBoxData": "Wczytywanie danych sugestii nie powiodło się.", "alrFailedTasColors": "Wczytywanie kolorów TAS nie powdiodło się!", "alrFailedTaskHandOver": "Przazanie zaadania nie powdiodło się.", "alrFailedTemplateProcesses": "Wczytywanie szablonu sprawy nie powiodło się.", "alrFailedVarData": "Wczytywanie danych zmmiennej nie powiodło się.", "alrFileAdded": "Plik został dodany!", "alrFileDeleteFailed": "Usuwanie pliku nie powiodło się.", "alrFileDonwload": "Pobieranie pliku...", "alrFileDownloaded": "Plik został porbany!", "alrFileInfoFailed": "Wczytywanie informacji pliku nie powiodło sie.", "alrFileMetaSaveFailed": "Metadane pliku nie mogą zostać zapisane.", "alrFileSavedLikeAttach": "Plik został zapisany jako dokument.", "alrFileUploadFailed": "Wczytywanie pliku nie powiodło się.", "alrFillAllRequired": "<PERSON><PERSON> zak<PERSON><PERSON> zadanie, na<PERSON><PERSON>y wypełnić wszystkie wymagane dane!", "alrFillData": "Aby przy<PERSON> zadnie, należy poprawnie wypełnić wszystkie dane!", "alrFillDataInRightFormat": "Prosz<PERSON> wypełnić dane w poprawnym formacie.", "alrFillDataToCompleteTsk": "Aby zak<PERSON><PERSON> zadanie, należy poprawnie wypełnić wszystkie dane!", "alrFillNameAndPass": "<PERSON><PERSON><PERSON> wypełnić imię oraz hasło.", "alrFillNote": "Proszę wypełnić tekst notatki.", "alrFillRequiredItems": "Proszę wypełnić wymagane pozycje.", "alrFolderDataFailed": "Wczytywanie danych folderów nie powiodło się.", "alrFolderDataLoadFailed": "Wczytywanie danych folderu nie powiodło się.", "alrFolderFailed": "Wczytywanie informacji o folderze nie powiodło się.", "alrFolderSaveFailed": "Folder nie mógł zostać zapisany!", "alrFoldersLoadFailed": "Folder nie mógł zostać wczytany.", "alrHelpSettingsSaveFailed": "Ustawnie pomocy nie mogły zostać zapisane.", "alrHistoricalTskInfoFailed": "Wczytywanie informacji zadania historycznego nie powiodło się.", "alrHistoricalVarsSaveFailed": "Zminne historyczne nie mogły zostać zapisane.", "alrHistoricalVarsSaved": "Zmienne historyczne zadania nie moggły zostać zapisane.", "alrInvLogginHash": "Bł<PERSON><PERSON><PERSON> login.", "alrJsonFailed": "Brak poprawnych JSON!", "alrLackOfPermsToEdit": "Nie masz uprawnień do edycji! Właścicielem jest", "alrLackOfPermsToSleepCase": "Nie masz uprawnień do zawieszania sprawy.", "alrLackOfPermsToWakeUpCase": "Nie masz uprawnień do wybudzania sprawy.", "alrLastHistoricalTskIdFailed": "Wczytywanie ID ostatniego zadania nie powiodło się.", "alrLoadAttachmentsFailed": "Wczytywanie dokumenty nie powiodło się.", "alrLogOutFailed": "Wylogowanie nie powiodło sie.", "alrLoginExpired": "<PERSON><PERSON><PERSON>, pro<PERSON><PERSON> zaloguj sie ponownie.", "alrMappingFailed": "Mapowanie nie może zostać zapisane.", "alrMappingTsksFailed": "Zadania mapowania nie mogą zostać wczytane.", "alrNewCaseBased": "Nowa sprawa zosała skonfigurowana!", "alrNewFolder": "Now folder został stworzony.", "alrNewFolderFailed": "Tworzenie nowego folderu nie powiodło się.", "alrNextTskOpened": "Następne zadanie zostało otwarte.", "alrNoDataToPrint": "Nie znaleziono danych do wydruku.", "alrNoteAdded": "Notatka została dodana!", "alrNoteSaveFailed": "Zapisywanie notatki nie powiodło się.", "alrNoteSaving": "Zapisywanie notatki...", "alrNotesLoadFailed": "Wczytywanie notatek sprawy nie powiodło się.", "alrOrgUnitDataFailed": "Wczytywanie danych jednostki organizacyjnej nie powiodło się.", "alrOrgUnitDeleteFailed": "Usuwanie jednosti organizacyjnej nie powiodło się.", "alrOrgUnitDeleted": "Jednostka organizacyjnia została usunięta!", "alrOrgUnitDeleting": "Usuwanie jednostki organizacyjnej...", "alrOrgUnitSaveFailed": "Zapisywanie jednostki organizacyjnej nie powiodło się.", "alrOrgUnitSaved": "Jednostka organizacyjna została zapisana!", "alrOrgUnitSaving": "Zapisywanie jednostki organizacyjnej...", "alrOverviewDataLoadFailed": "Wczytywanie danych podglądu nie powiodło się.", "alrOverviewSaveFailed": "Zapisywanie podglądu nie powiodło się!", "alrOverviewSaveSameNameFailed": "Nazwa podglądu jest już wykorzystywana przez Ciebie bądź innego uzytkownika, proszę wybierz inną nazwę podglądu.", "alrGraphSaveSameNameFailed": "Nazwa wykresu jest już wykorzystywana przez Ciebie bądź innego uzytkownika, proszę wybierz inną nazwę wykresu.", "alrReportSaveSameNameFailed": "Nazwa raportu jest już wykorzystywana przez Ciebie bądź innego uzytkownika, proszę wybierz inną nazwę raportu.", "alrOverviewsLoadFailed": "Wczytywanie podglądu nie powiodło się.", "alrPassSaveFailed": "Zapisywanie hasła nie powiodło się.", "alrPassSaved": "Hasło sotało zapisane!", "alrPlanReqItems": "Aby zapisa<PERSON> plan, wypełnij wymagane pozycje.", "alrPlanSaveFailed": "Zapisywanie planu nie powiodło się.", "alrPlanSaved": "Plan został zapisany!", "alrPreparingPrint": "Przyhotowywanie wydruku...", "alrPrintDeleteFailed": "Usuwanie wydruku nie powiodło się.", "alrPrintDeleted": "Wydruk został usunięty!", "alrPrintSaveFailed": "Zapisanie wydruku nie powiodło się.", "alrPrintSaved": "Wydruk został zapisany", "alrReadOnlyCaseDataFailed": "Wczytywanie danych sprawy do odczytu nie powiodło się.", "alrRecalcFailed": "Błąd podczas przeliczania!", "alrRecalculating": "Przeliczanie...", "alrRestorTemplFailed": "<PERSON>e udało się przywr<PERSON><PERSON>ć szablonu.", "alrRoleDataLoadFailed": "Wczytywanie danych roli nie powiodło się.", "alrRoleDeleteFailed": "Usuwanie roli nie powiodło się.", "alrRoleDeleted": "Rola została usunięta!", "alrRoleDeleting": "<PERSON><PERSON>wanie roli...", "alrRoleSaveFailed": "Zapisywanie roli nie powiodło się.", "alrRoleSaved": "Rola została zapisana!", "alrRoleSaving": "Zapisywanie roli...", "alrRunEvent": "Uruchamianie wydarzenia...", "alrSaveFailed": "Zapis nie powiódł się.", "alrSaved": "Zapisano!", "alrSaving": "Zapisywanie...", "alrSavingBeforeRecalcFailed": "Błąd podczas zapisywania przed ponownym obliczaniem!", "alrSavingFailed": "Błąd w tracie zapisywania!", "alrSavingPlan": "Zapisywanie planu...", "alrSavingPrint": "Zapisywan<PERSON> w<PERSON>...", "alrSearchNoResults": "Brak wyników odpowiadającym kryteriom wyszukiwania.", "alrSearchRequestFailed": "Bład w trakie żądania wysyłania!", "alrSearching": "Wyszukiwanie...", "alrSettFailed": "Ustawienie nie mogą zostać zapisane.", "alrSettSaved": "Ustawienia zostały zaspisane.", "alrSettingsLoadFailed": "Wczytywanie danych ustawień nie powiodło się.", "alrSleepCaseFailed": "Zawiesznie sprawy nie powiodło się.", "alrStoreNameNotDefined": "Store „{{storeName}}“ nie jest zdtefiniowany.", "alrStoreNotDefined": "Store nie został zdefiniowany.", "alrSubActionNotDefined": "Należy zdefiniować SubAction oraz przedrostek.", "alrSubStoreNotDefined": "Należy zdefiniować SubStore oraz przedrostek.", "alrSuggestBoxDataNotContains": "<PERSON> pola sugestii „{{label}}“ nie z<PERSON> „{{prop}}“!", "alrSuspendingCase": "Zawieszanie sprawy", "alrTableDataFailed": "Wczytywanie daty tabeli nie powiodło się.", "alrTasNewVersion": "Znaleziono nową wersje oprogramowania.", "alrRefresh": "<PERSON><PERSON><PERSON><PERSON> jest {{- spanRefresh}} strony w przeglądarce.", "alrTasVersionLoadFailed": "Wczytywanie wersji aplikacji nie powiodło się!", "alrTaskHandOver": "Przekazywanie zadania...", "alrTaskHandedOver": "Zadanie zostało przekazane do użytkownika:", "alrTaskNoRights": "Nie masz uprawnieni do przeglądania zadania nr {{id}}.", "alrTaskNotFound": "Zadanie nie zostało znalezione.", "alrTempDataLoadFailed": "Wczytywanie szablonów danych nie powiodło się.", "alrTempHeadersLoadFailed": "Wczytywanie nagłówków szablonów nie powiodło się.", "alrTempDeleteFailed": "Usuwanie szablonów nie powiodło się.", "alrTempDeleted": "Szablon został usunięty!", "alrTempFoldersLoadFailed": "Wczytywanie folderów szablonów nie powiodło się.", "alrTempRestored": "Szablon został przywrócony ze statusem W budowie.", "alrTempNameLoadFailed": "Wczytywanie nazwy szablonu nie powiodło się.", "alrTempSaveFailed": "Zapisywanie szablonu nie powiodło się.", "alrTempsLoadFailed": "Wczytywanie szablonu nie powiodło się.", "alrTempVarDataLoadFailed": "Wczytywanie zmiennych szablonu nie powiodło się.", "alrTempVarSaveFailed": "Zapisywanie zmiennych nie powiodło się.", "alrTempVarsLoadFailed": "Wczytywanie zmiennych szablonu nie powiodło się.", "alrTotalCountFailed": "Łączne zliczanie rekordów w tabeli nie powiodło się.", "alrTreeDataFailed": "Wczytywanie danych drzewa nie powiodło się.", "alrTskAddFailed": "Dodawanie zadania nie powiodło się.", "alrTskAdded": "Zadanie zostało dodane!", "alrTskAdding": "Dodawania zadania...", "alrTskAssignFailed": "Pryzpisywanie zadania nie powiodło się.", "alrTskAssigned": "Zadnaie zostało przypisane.", "alrTskCompleteFailed": "Bład w trakcie ukańczania.", "alrTskDataLoadFailed": "Wczytywanie danych zadania nie powiodło się.", "alrTskDeleteFailed": "Usuwanie zadania nie powiodło się.", "alrTskDeleted": "Zadanie zostało usunięte!", "alrTskNameLoadFailed": "Wczytywanie nazwy zadnaia nie powiodło się.", "alrTskRecalculated": "Zadanie zostało przeliczone!", "alrTskSaveFailed": "Błąd podczas zapisywania zadania.", "alrTskSaved": "Zadanie zapisane!", "alrTskSavedAndCompleted": "Zadania zapisane i ukończone!", "alrTskScheduleFailed": "Planowanie zadań nie powiodło się.", "alrTskScheduled": "Zadanie zostało zaplanowaned.", "alrTskTakeFailed": "Przejęcie zadania nie powiodło się", "alrTskTaken": "Zanie zostało przejęte.", "alrTskTakign": "Podejmowanie zadania...", "alrTsksMappingFailed": "Wczytywanie mapowania zadań nie powiodło się.", "alrUserDataLoadFailed": "Wczytywanie danych użytkownika nie powiodło się.", "alrUserDeleteFailed": "Usuwanie użytkownika nie powiodło się.", "alrUserDeleted": "Użytkownik został usunięty!", "alrUserDeleting": "Usuwanie użytkownika...", "alrUserIsNotActive": "Użytkownik jest nieaktywny.", "alrUserNotLoaded": "Wczytywanie użytkownika nie powiodło się.", "alrUserParamsLoadFailed": "Wczytywanie parametrów użytkownika nie powiodło się.", "alrUserSaveFailed": "Zapisywanie użytkownika nie powiodło się.", "alrUserSaved": "Użytkownik został zapisany!", "alrUserSaving": "Zapisywanie użytkownika...", "alrUserStatusChangeFailed": "Zmiana statusu użytkownika nie powiodła się.", "alrUserStatusChanged": "Status użytkownika został zmieniony!", "alrUserStatusChanging": "Zmienianie statusu użytkownika...", "alrVarDeleteFailed": "Usuwanie zmienny nie powiodło się.", "alrVarDeleted": "Zmienne zostały usunięte!", "alrVarSaveFailed": "Zapisywanie zmienny nie powiodło się.", "alrVarSaved": "Zmienne zostały zapisane.", "alrVarSaving": "Zapisywanie zmiennych...", "alrVarsForModalFilterFailed": "Wczytywanie filtru modalnego zmiennych nie powiodło się.", "alrVarsLoadFailed": "Zmiennie nie mogą być wcz<PERSON>.", "alrVarsOrderLoadFailed": "Wczytywanie kolejności zmiennych nie powiodło się.", "alrVarsOrderSaveFailed": "Zapisywanie kolejności zmiennych nie powiodło się.", "alrViceDeleted": "Zastępstwo zostało anulowane.", "alrViceFailed": "Zastąpienie nie powiodło się.", "alrViceNotFound": "Nie znaleziono zastąpienia!", "alrViceSaveFailed": "Zastąpienie nie mogło zostać zapisane.", "alrViceSaved": "Zastąpienie zostało zapisane!", "alrViceSaving": "Zapis zastępstwa...", "always": "<PERSON><PERSON><PERSON>", "annually": "Rocznie", "assHierarchy": "Związek z osobą referencyjną", "assHierarchyAncestors": "Wszyscy przełożeni osoby referencyjnej", "assHierarchyChildren": "Bezpośredni podwładni osoby referencyjnej", "assHierarchyDescendants": "Wszyscy podwładni osoby referencyjnej", "assHierarchyGuarantor": "<PERSON><PERSON><PERSON> o<PERSON>ba referency<PERSON>a", "assHierarchyParent": "Bezpośredni przełożony osoby referencyjnej", "assHierarchySiblings": "Współpraconiwcy osoby referencyjnej", "assMethodAutomatic": "Przez komputer", "assMethodLastSolver": "Do ostatniego właściciel zadania", "assMethodLastSolverChoice": "Właśćiciel zadania wybierany przez najnowszego właściciela zadania", "assMethodLeast": "Właściciel zadnia z najmniejszą ilośćia zadań", "assMethodPull": "<PERSON><PERSON>, zadanie będzie oferowane", "assMethodSelect": "Właściciel zadania wybierani przez przełożonego", "assMethodVariable": "Właściciel ze zmiennej", "assessmentOfConds": "<PERSON><PERSON><PERSON> stanu", "assign": "Przydzielenie", "assignAttrs": "Przypisanie atrybutów", "assignAttrsLogType": "Przypisanie atrybutów do typu logicznego dokumentu", "assigned": "Przydzielono", "assigningRoles": "Przypisanie roli", "assignments": "Zadania", "attachments": "Dokumenty", "attachmentsList": "Lista dokumentow", "attribute": "<PERSON>ry<PERSON>", "attributeNew": "Atrybut – Nowy", "availableVars": "Dostepne zmienne", "body": "<PERSON><PERSON><PERSON>", "borders": "Obramowanie/Granice", "byFolders": "Według folderów", "byOrganization": "Według organizacji", "byRole": "Według roli", "calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculations": "O<PERSON><PERSON>zen<PERSON>", "calendar": "Kalendarz", "carriedIfNoOther": "Zostanie przeprowadzona jeżeli nie będzie innej", "case": "Sprawa", "caseCreation": "<PERSON>rz<PERSON>e sprawy", "caseGraph": "Instan<PERSON><PERSON> diagram", "caseNoEvents": "Spawa nie zawiera wydarzeń.", "caseNum": "Numer sprawy.", "caseOwner": "Właściciel sprawy", "caseStatus": "Status sprawy", "caseVar": "sprawa", "cases": "Sprawy", "casesWithProblem": "<PERSON>ja sprawa z problemem", "category": "Kategoria", "changeTaskSolver": "Zmień właściciela zadania", "changedBy": "Zmieniene przez", "changedWhen": "Zmienione (kiedy)", "checkbox": "Checkbox", "checkboxList": "CheckboxList", "choosePrint": "Szablon wydruku", "chooseUserToAssignTsk": "Wybierz użytkownika, który zostanie przypisany do zadnia", "choosenAttrs": "Wybrane atrybuty", "city": "<PERSON><PERSON>", "class": "<PERSON><PERSON><PERSON>", "clickToClose": "Zamknij klikając", "clickToRefresh": "Kliknij aby zaktualizować strone w przeglądarce", "clickToRepeat": "Powtórz akcje k<PERSON>", "clientLanguage": "Język klienta", "cloneRow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "Zamknij", "closeAll": "Zamknij wszystkie", "coWorkersOfPlanGuar": "Współpraconicy gwaranta planu", "color": "<PERSON><PERSON>", "colors": "<PERSON><PERSON><PERSON>", "column": "<PERSON><PERSON><PERSON>", "columnName": "Nazwa kolumny", "comment": "Komentarz", "complete": "Kompletne", "completion": "Ukończenie", "componentDescription": "Opis komponentu", "condition": "Warunek", "conditions": "Warunki", "confirmAttachDeletion": "<PERSON>zy na pewno chcesz usunąć ten dokument?", "confirmDeleteDialog": "<PERSON>zy na pewno chcesz usunąć tą {{variable}}?", "confirmDialogEventSave": "Aby prz<PERSON><PERSON><PERSON><PERSON><PERSON>ć konieczne jest zapisanie wydarzenia. <PERSON><PERSON> ch<PERSON>z go zapisać?", "confirmResetDashboard": "<PERSON>zy na pewno chces zresetować pulpit?", "confirmSaveChanges": "<PERSON><PERSON><PERSON><PERSON>?", "confirmSaveDiagramChanges": "<PERSON><PERSON><PERSON><PERSON> zmiany w diagramie?", "confirmSaveTaskChanges": "Zapisać zmiany w zadaniu?", "confirmRestoreDialog": "Czy na pewno chcesz przywrócić {{variable}}?", "confirmSaveNote": "<PERSON>zy na pewno chcesz zapisać notatkę?", "confirmSleepCase": "<PERSON>zy na pewno chcesz zawiesić sprawę?", "confirmTakeoverTsk": "<PERSON>zy na pewno chcesz przejąc sprawę?", "confirmWakeUpCase": "<PERSON>zy na pewno chcesz odwiesić sprawę?", "connection": "<PERSON><PERSON>", "connectionFailed": "Połączenie do serwera nie powiodło się", "connectionVar": "link", "constant": "Stały", "contact": "Kontakt", "contactTaskOwner": "Skontaktuj się z właścicielem zadania", "containerSettings": "Ustawienia zasobnika", "contains": "Zawiera", "continueSolving": "Kontynuuj rozwiązanie", "copied": "Skopiowano!", "copy": "<PERSON><PERSON><PERSON><PERSON>", "copyShortcut": "Wciśnij Ctrl+C", "copyToClipboard": "Kopiuj do schowka", "createForm": "Stwórz forme", "csv": "csv", "csvFile": "Plik CSV", "customPrint": "Wydruk standardowy", "daily": "<PERSON><PERSON><PERSON>", "dashCvNoOverview": "Nie wybrano żadnego przeglądu – wybierasz go w ustawieniach zasobnika", "dashCvNoRights": "You have no permissions to view the overview, please contact the administrator.", "dashFavNoShortcut": "Brak vices wybranych – wy<PERSON><PERSON>z go w ustawieniach zasobnika", "dashboard": "Pulpit", "date": "Data", "dateList": "LOV dat", "day": "Dzień", "dayE": "Dzień", "daysDHM": "Dni: (dd:hh:mm)", "defChangeVarInfluence": "Ta zmiana definicji zmiennej zagnieżdozna została w już utowrzonych przypadkach.", "defChangeInfluence": "Ta zmiana definicji zagnieżdozna została w już utowrzonych przypadkach.", "defaultCaseName": "Domyślna nazwa sprawy", "defaultLbl": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{label}}", "defaultLblShe": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{label}}", "defaultLblIt": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{label}}", "defaultPrintProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> – proces", "defaultPrintTask": "Domyślne – zadanie", "defaultValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delUser": "Usuń użytkownika", "delete": "Usuń", "deleteCol": "<PERSON><PERSON><PERSON> kolumne", "deleteRow": "<PERSON><PERSON><PERSON> wiersz", "deleteSelected": "Usuń zaznaczone", "deleted": "<PERSON><PERSON><PERSON><PERSON>", "deletedOn": "Usunięty", "deletedShe": "<PERSON><PERSON>ę<PERSON>", "description": "Opis", "deselect": "Odznacz", "detail": "Szczegóły", "developed": "<PERSON> budo<PERSON>e", "dial": "<PERSON><PERSON><PERSON><PERSON>", "dic": "VAT", "directSubOfPlanGuar": "Bezpośredni podwładny gwaranta planu", "directSupOfPlanGuar": "Bezpośredni przełożony gwaranta planu", "disableFilter": "Wyłącz filtry", "dmsAssignAttrs": "Przypisanie atrybutu DMS", "dmsAttribute": "Atrybut DMS", "dmsAttributes": "Atrybuty DMS", "dmsColumns": "DMS – kolumny", "dmsVisNull": "<PERSON><PERSON><PERSON><PERSON> w tym procesie", "dmsVisSub": "<PERSON>", "dmsVisSup": "W procesie macierzystym", "dmsVisSupSub": "W procesach macierzystych i podrzędnych", "dmsVisibility": "Dokumenty będą widoczne", "doNotShowVariablesWith_": "Nazwa zmiennej zaczynająca się od `_` nie będzoe pokazywana użytkownikom", "document": "Dokument", "documentVar": "dokument", "documents": "Dokumenty", "doesNotContain": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "dragAddFiles": "Dodaj pliki porzez ''Przeciągnij i upuść'' lub kliknij {{- here }} dla wyboru plików.", "dragAddFile": "Dodaj plik porzez ''Przeciągnij i upuść'' lub kliknij {{- here }} dla wyboru pliku.", "here": "<PERSON><PERSON><PERSON>", "dropContainer": "Upuśc zasobnik", "dropzoneTip": "Upuść plik tutaj", "dropZoneUserPhoto": "Upuść obraz tutaj lub kliknij aby wybrac plik do uploadu.", "dueDate": "Do daty", "duty": "Duty", "dynList": "Lista dynamiczna", "dynRowsDefinition": "Definicja tabeli i kolumn", "dynTableName": "Nazwa tabeli <PERSON>j", "dynTable": "<PERSON><PERSON><PERSON>", "dynTables": "<PERSON><PERSON><PERSON>", "dynamicList": "Lista dynamiczna", "dynamicRows": "W<PERSON><PERSON> dynamiczne", "dynamicTable": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "editAttribute": "<PERSON><PERSON><PERSON><PERSON>", "editOrgUnit": "Edytuj jednostke organizacyjną", "editRole": "<PERSON><PERSON><PERSON><PERSON> role", "editRule": "<PERSON><PERSON><PERSON><PERSON>", "editUser": "Edytuj użytkownika", "editor": "<PERSON><PERSON><PERSON>", "email": "E-mail", "emailsQueue": "Kolejka e-maili", "empty": "<PERSON><PERSON><PERSON>", "end": "Koniec", "error": "Błąd", "errored": "Z błędem", "error404": "Błąd 404 –strony nie znaleziono!", "event": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "events": "Wyd<PERSON>zen<PERSON>", "eventsRun": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>arzenie", "every": {"masc": "Ka<PERSON><PERSON>", "neutral": "Ka<PERSON><PERSON>", "repeat": "Co"}, "everyWorkDay": "Każdy dzień roboczy", "excel": "Excel", "favourites": "Ulubione", "fax": "Faks", "file": "Plik", "fileLogicalType": "Typ logiczny pliku", "fileName": "Nazwa pliku", "filePlacement": "Umieszczenie w folderze", "files": "Pliki", "filter": "Filtr", "filterFrom": "Filtr z", "filterTitle": "Filtr", "filtrate": "Filtr", "finishTask": "Zakończ zadanie", "finished": "Zakończono", "finishedBy": "Zakończono przez", "finishedOn": "Zakończono", "first": "<PERSON><PERSON><PERSON>", "firstLeft": "<PERSON><PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "firstRight ": "<PERSON><PERSON><PERSON> prawy", "firstRowColumnsName": "Pierwsza linia zawierająca nazwę kolumny", "folder": "Folder", "folder-": "Folder –", "folderExecRightsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> role, k<PERSON><PERSON><PERSON> pozwoli za zainicjowanie sprawy w folderze", "folderExecRightsTextOS": "Przypisz jednostkę organizacyjną, która pozwoli na zainicjowanie sprawy w folderze", "folderName": "<PERSON><PERSON><PERSON>", "font": "Czcionka", "fontMainHeader": "Czcionka głównego nagłówka", "form": "Forma", "fourth": "<PERSON><PERSON><PERSON><PERSON>", "freeTsk": "<PERSON><PERSON><PERSON> z<PERSON>nie", "fri": "Piątek", "from": "Od", "fsDescription": "opis", "fsName": "nazwa", "fsTooltip": "pod<PERSON>wiedź", "fullName": "Pełna nazwa", "fullScreen": "Pełny ekran", "getTotalCount": "Liczba całkowita", "graph": "<PERSON>", "handExecutionTaskListEmpty": "<PERSON><PERSON><PERSON><PERSON> wydarzenie", "handOver": "Przekazanie", "handover": "Prz<PERSON><PERSON>", "headerDashboard": "Nagłówek pulpitu", "help": "Pomoc", "hideLogout": "Ukyj wylogowanie", "hideNewProcess": "<PERSON>k<PERSON><PERSON> '<PERSON>a sprawa'", "hideProcs": "<PERSON><PERSON><PERSON><PERSON>", "hideTasks": "Ukryj <PERSON>adania", "historicalValues": "Wartosci historyczne", "currentValues": "Rzeczywiste wartości", "history": "Historia", "home": "Start", "html": "HTML", "ic": "ID Firmy", "id": "ID", "inCasesNames": "W nazwach spraw", "inTasksNames": "W nazwach zadań", "inDevelopment": "<PERSON> trakcie budowy", "inEvery": "W każdym", "inFiles": "<PERSON> p<PERSON>ach", "initiator": "<PERSON><PERSON><PERSON><PERSON>", "inTasks": "<PERSON>", "inactive": "Nieaktywny", "inactiveShe": "Nieaktywny", "incidences": "zdarzenia", "inclusion": "Włączenie", "info": "Info", "inputParams": "Parametry wejsciowe", "insert": "Wstaw", "insertAttachTip": "Przeciągnij i upuśc aby wstawić dokument", "insertVar": "<PERSON><PERSON><PERSON>ć", "insertSnippet": "Wstaw skrawek", "snippet": "<PERSON><PERSON><PERSON>", "insertedBy": "Upload'owane przec", "insertedOn": "Dodan<PERSON>", "insteadOf": "Zamiast", "instructions": "Instrukcje", "invitation": "Zap<PERSON><PERSON><PERSON>", "isEmail": "Błędny adres e-mail", "isEmpty": "jest pusty", "isExisty": "<PERSON>e jest popr<PERSON>y", "isManagerOrgUnit": "Jest managerem jednostki organizacyjnej", "isNotEmpty": "nie jest pusty", "isRequired": "To pole jest wymagane", "justSave": "<PERSON><PERSON><PERSON>", "keepGlobalOrder": "Zachowaj globalny porządek", "key": "<PERSON><PERSON>cz", "last": "ostatni", "lastName": "Nazwisko", "lastOwnerOfTask": "Ostatni właściciel zadania", "licenceKey": "Klucz licencyjny", "link": "Link", "linkConditions": "<PERSON>runki łącza", "list": "Lista", "listName": "Nazwa listy", "listOfValues": "<PERSON><PERSON>", "listValue": "<PERSON><PERSON><PERSON>y", "loading": "Wczytywanie...", "location": "<PERSON><PERSON><PERSON>", "locked": "Zablokowany", "logIn": "<PERSON><PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logicalType": "Typ <PERSON>zny", "loginError": "Bł<PERSON><PERSON><PERSON> login.", "loginTimeout": "<PERSON>it c<PERSON><PERSON> se<PERSON> (sec.)", "longText": "Długi tekst", "mailEscalation": "E-mail z podglądem eskalowanych zadań", "mailProcEscalation": "E-mail z podglądem eskalowanych spraw", "mailPromptly": "Powiadomienie e-mail o nowym zada<PERSON>u", "mailPull": "Powiadomienie e-mail o nowym zadaniu do ściagnięcia", "mailTotal": "Podsumowanie e-mail z przeglądem zadań", "mainButton": "Główny przycisk", "mainColor": "Główny kolor", "mainHeader": "Główny nagłówek", "mainLanguage": "Główny język", "manager": "Manager", "managerOfOrgUnit": "Manager jednostki organizacyjnej", "mandatory": "Obowiązkowy", "manualStartEvent": "Ręczny start wydarzenia", "mapping": "Mapowanie", "mappingSubProcessVars": "Mapowanie zmiennych podprocesu", "markAll": "Zaznacz wszystkie", "menu": "<PERSON><PERSON>", "mine": "<PERSON><PERSON>", "mobilePhone": "Telefon komórkowy", "mon": "Poniedziałęk", "month1": "Styczeń", "month10": "Październik", "month11": "Listopad", "month12": "Grudzień", "month2": "<PERSON><PERSON>", "month3": "Marzec", "month4": "Kwiecień", "month5": "Maj", "month6": "Czerwiec", "month7": "Lipiec", "month8": "Sierpień", "month9": "Wrzesień", "monthI": "<PERSON><PERSON><PERSON><PERSON>", "monthly": "Mi<PERSON>ę<PERSON><PERSON>", "months": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON><PERSON><PERSON>j", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingle", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "W wielu instancjach", "myUnfinishedTasks": "Nie nieukończone zadania", "name": "Nazwa", "nested": "Znik<PERSON><PERSON>ł", "never": "<PERSON><PERSON><PERSON>", "new": "Nowy", "newCase": "Nowa sprawa", "newFolder": "Folder – Nowy", "newForm": "Nowa forma", "newIt": "Nowy", "newName": "Nowa nazwa", "newShe": "Nowy", "newSolver": "Nowy właściciel zadania", "no": "<PERSON><PERSON>", "noAttach": "Brak dokumentów (kliknij aby dodać)", "clickToAddAttach": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "noName": "Bez nazwy", "noOneBeOffered": "<PERSON><PERSON>, zadanie bedzie przeznaczone ścisłej grupie użytkowników", "noPageRights": "<PERSON>e masz uprawnień do oglądania tej strony.", "node": "<PERSON><PERSON><PERSON><PERSON>", "notFound": "Nie znaleziono", "notMatch": "<PERSON><PERSON> pas<PERSON>je", "notNumber": "<PERSON><PERSON>", "notIntNumber": "Nie jest liczbą całkowitą", "notValid": "Nieważny", "notes": "Notatki", "notesOnContacts": "Notatki na temat kontaktów", "notice": "Obwieszczenie", "notification": "Powiadomienie", "nrOfItems": "Liczba przedmiotów", "number": "Numer", "numberList": "LOV numerów", "ok": "OK", "oneMustBeMet": "Conajmniej jeden musi zostać spełniony", "onlyOrgUnit": "Tylko jednostka organizacyjna", "onlyPlanGuarantor": "<PERSON><PERSON><PERSON> gwarant planu", "openAll": "Otwórz wszystkie", "operating": "Aktywny", "order": "Porządkuj", "orderByColumn": "Porządky przez kolumny", "orgName": "Nazwa podmiotu", "orgStructure": "Org. struk<PERSON>", "orgUnit": "jednostka org.", "orgUnitE": "jednostka org.", "orgUnitName": "Nazwa jednostki organizacyjnej", "orgUnitShe": "Jednostka organizacyjna", "orgUnits": "Jednostki organizacyjne", "organization": "Organizacja", "overview": "Przegląd", "overviewMapping": "Mapowanie przeglądu", "overviewNew": "Przegląd – nowy", "overviewSetSharing": "Ustaw udostępnianie przeglądu dla każdej gurpy użytkowników", "overviews": "Prz<PERSON><PERSON>ądy", "owner": "Właściciel", "ownerWithLeastTasks": "Właściciel zadania z najmniejszą ilością zadań", "pageNotFound": "Nie znaleziono strony", "parentFolder": "Folder nadrzędny", "parentUnit": "Jednostka nadrzędna", "participants": "Uczestnicy", "password": "<PERSON><PERSON><PERSON>", "passwordChallenge": "Powiadomienie", "passwordChallengeText": "Czy na pewno chcesz powiadomic wszystkich użytkowników o zmianie hasła?", "passwordChange": "<PERSON><PERSON><PERSON> hasło", "passwordCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "passwordNew": "Nowe hasło ", "passwordNewCheck": "Powtórz nowe hasło", "paused": "Nieaktywny", "personInOrgStr": "Przypisano przez osobw w strukturze organizacyjnej", "phone": "Telefone", "photo": "Zdjęcie", "plan": "Plan", "planGuarantor": "<PERSON><PERSON><PERSON>u", "planTitle": "Plan", "plans": "Planowanie", "plnOffType": "Powtórz", "plnOrgUnit": "Jednostka organizacjna", "plnTProc": "Szablon sprawy", "plnUser": "Sponsor planu", "plnUsersSelect": "Warunki ograniczające wybór jednegogu lub więcej inicjatorów", "prependTsk": "Przedrostek", "prependedTsk": "Gotow<PERSON> zadanie", "primaryKey": "Klucz glówny", "print": "<PERSON><PERSON><PERSON>", "printTemplate": "Szablon wydruku", "printType": "<PERSON><PERSON> w<PERSON>", "printer": "Drukuj – HTML", "priority": "Priorytet", "procDescription": "Opis procesu", "procDueDateFinish": "<PERSON><PERSON><PERSON> sprawy", "procName": "Nazwa sprawy", "procOwner": "Właściciel procesu", "procSummary": "Rozwiązane", "process": "Process", "processName": "Nazwa procesu", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "quickFilter": "Szybki filtr", "radioButtonList": "RadioButtonList", "reEvaluates": "Oceń pownownie", "recalc": "<PERSON><PERSON><PERSON><PERSON>", "recipient": "Odbiorca", "recipientsId": "ID odbiorców", "records": "Dokumentacja", "referenceUser": "Osoba referencyjna", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registered": "Zarejestrowany", "relatToPlanSponsor": "Związek ze sponsorem planu", "remove": "Usuń", "removeVice": "Usuń vice", "renameCols": "Zmień nazwy kolumn", "repeatLogin": "Powt<PERSON>rz login lub wybierz inny typ autoryzacji.", "repeatOrReport": "Spróbuj później lub skontaktuj się z administratorem.", "repetition": "Powtórzenie", "required": "Obowiązkowy", "reset": "Reset", "restrictTaskOwners": "Ograniczenia dla właściciela zadania", "restrictUsers": "Ograniczeni użytwnicy", "returnSubProcessVars": "Powrót do zmniennych podprocesu", "revision": "<PERSON><PERSON><PERSON><PERSON>", "right": "Right", "rightOrDuty": "Right / Duty", "role": "Rola", "roleName": "Nazwa roli", "roleSg": "Rola", "roles": "Role", "row": "w<PERSON>z", "rule": "Reguła", "ruleCSVFile": "Nazwa pliku CSV", "ruleCSVHeader": "Pier<PERSON><PERSON> wiersz pliku CSV jako nagłówek", "ruleCSVMask": "Nazwa maski pliku CSV", "ruleCSVSeparator": "Separator kolumny", "ruleNew": "Reguła – Nowa", "ruleParamsMap": "Mapowanie zmiennych", "ruleProcOwnCSV": "Zdefiniowane w mapowaniu", "ruleTypeCSVExpProcs": "Eksportuj wszystkie szablony spraw do CSV", "ruleTypeCSVMrgProcs": "Uruchom sprawy według CSV i zaktualizuj zmienne spraw", "ruleTypeCSVRunProcs": "Uruchom sprawy według CSV", "ruleTypeCSVUpdProc": "Zaktualizuj zmienne spraw według CSV", "ruleTypeCSVUpdProcs": "Zaktualizuj zmienne spraw według CSV", "ruleTypeCSVUpdateList": "Zaktualizuj liste dynamiczną według CSV", "ruleTypeReturn": "Odpowiedź wydarzenia", "ruleTypeUpdateListOfProcesses": "Zaktualizuje dynamiczną liste procesów", "rules": "Reg<PERSON>ł<PERSON>", "run": "Uruchom", "runProcess": "Rozpocznij proces", "running": "Uruchomiony", "sat": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON>", "saveAsAttachment": "Zapisz wydruk jako dokument do sprawy", "scheduling": "Planowanie", "scheme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wizualna", "script": "Skrypt", "scripts": "Skrypty", "search": "Wyszukaj", "searchResult": "Wynik wyszukiwania", "second": "<PERSON><PERSON>", "secondLeft": "<PERSON><PERSON>", "secondRight": "<PERSON><PERSON> p<PERSON>y", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "Do właścicieli zadania automatycznie wybieranych przez komputer", "selectedByTaskSupervisor": "Do właścicieli zadań wybranych przez przełożonego", "selectedPrint": "wybrany wydruk", "selectedUser": "Wybrany użytkownik", "send": "<PERSON><PERSON><PERSON><PERSON>", "sendingFailed": "Pomyłka", "sendOn": "Czas do wysłania", "sendTestMail": "Testowy e-mail", "sequence": "Sekwen<PERSON><PERSON>", "setDefault": "Ustaw jako <PERSON>", "setVice": "Ustaw vice", "setViceAttachmentsNotes": "Prawo do uploadu dokumentów i notatek", "settings": "Ustawienia", "shortcuts": "Skróty", "showAttachmentsClick": "Przez klinienięce pokażesz dokument", "showCommentCol": "Pokaż kolumne komentarza", "skype": "Skype", "solve": "Rozwiąż zadanie", "solvedBy": "Rozwiązane przez", "solver": "Właściciel zadania", "sort": "<PERSON><PERSON><PERSON><PERSON>", "sortByColumn": "Sort<PERSON>j przez kolumne", "sorting": "Sort<PERSON>nie", "sourceTask": "Zadanie źródłowe", "sourceVar": "Zmienna źródłowa", "start": "Start", "startDate": "Data rozpoczęcia", "startCalDate": "Data rozpoczęcia", "endCalDate": "Data zakończenia", "state": "<PERSON>", "stateAddress": "<PERSON>", "status": "Status", "street": "<PERSON><PERSON> ul<PERSON>", "subProcess": "Podproces", "subject": "Podmiot", "substitute": "Zastępstwo", "sun": "<PERSON><PERSON><PERSON><PERSON>", "superior": "Przeł<PERSON><PERSON><PERSON>", "supervis": "Kierownik", "supervisor": "Kierownik zadania", "suspend": "<PERSON><PERSON><PERSON><PERSON>", "suspended": "Zawieszony", "suspendedx": "Zawieszony", "tTaskAgain": "Powtazrające się zachowanie aktywacyjne", "tTaskAutoCompleteCaption": "Zadanie będzię spełnione automatycznie jeżeli", "tTaskCompletionCOA": "Wszystkie warunki są spełnione jednocześnie", "tTaskCompletionCOO": "Przynajmniej jeden warunek jest spełniony", "tTaskDueOffsetNone": "Natychmiast", "tTaskDueOffsetPO": "Wprowadzone przez kierownika", "tTaskDueOffsetPS": "W ciągu kilku dni od rozpoczęcia sprawy", "tTaskDueOffsetTS": "W ciągu kilku dnio do możliwego rozpoczęcia aktywości", "tTaskDueOffsetVC": "Zmiennych w sposób ciagły", "tTaskDueOffsetVO": "Zmiennych przy starcie", "tTaskInvClassConf": "<PERSON><PERSON><PERSON>", "tTaskInvClassPriv": "<PERSON><PERSON><PERSON><PERSON>", "tTaskInvClassPubl": "Publiczne", "tTaskInvPriority1": "1-najwyższy", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tTaskInvokeEventB": "W tle", "tTaskInvokeEventI": "Natychmiast", "tTaskReferenceUserLastSolver": "Ostatni właściciel zadnaia", "tTaskReferenceUserMan": "Manager jednostki organizacyjnej xy", "tTaskReferenceUserUser": "Użytkownik xy", "tTaskRunOnlyOnce": "Uruchom tylko raz", "tTaskSufficientEnd": "Ukończenie kończy całą sprawe", "tabName": "Nazwa karty", "table": "<PERSON><PERSON><PERSON>", "takeOnlyOrder": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "takeover": "Przejmij", "targetTask": "<PERSON><PERSON><PERSON> do<PERSON>e", "targetVar": "Zmienna <PERSON>", "taskAutomatic": "Automatyczny status", "taskEmailNotification": "Powiadomienie e-mail", "taskEvent": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>arzenie", "taskEventWait": "Czeka na wydarzenie", "taskOwner": "Właściciel zadania", "taskSolverAssign": "do przypisania do właściciela zadania", "taskStart": "Start", "taskStatus": "Status", "taskStatusA": "Aktywny", "taskStatusAP": "Aktywny podproces", "taskStatusAS": "Uśpiony podproces", "taskStatusD": "Zakończony", "taskStatusL": "Oczekiwanie", "taskStatusLEdit": "<PERSON>e można ed<PERSON> oczekującego zadania", "taskStatusN": "Nowy", "taskStatusP": "Zaplanowany", "taskStatusS": "Zawieszony", "taskStatusT": "Do ściągnię<PERSON>", "taskStatusW": "Do przydzielenia", "taskStatusWT": "Do planowania", "taskSubprocess": "wdrożone przez podproces", "taskTabVariables": "Przypisane zmienne", "taskType": "Typ zadania", "taskWillBeAssigned": "Zadanie zostanie przypisane", "tasks": "Zadania", "tasksToPull": "Zadanie do przejęcia", "taskstatusAD": "Aktywny i gotowy", "tempId": "Szablon ID", "tempVar": "s<PERSON><PERSON><PERSON>", "template": "Szablon", "templateDeleted": "Usunięty", "templateStatus": "Status szablonu", "templates": "S<PERSON><PERSON><PERSON><PERSON>", "templatesFolder": "Szablon – folder", "testForm": "<PERSON>a testowa", "tested": "Przetestowany", "text": "Tekst", "textList": "LOV of tekstów", "textMultipleLines": "Tekst z wieloma liniami", "textSuggest": "Wnioskowdawca", "third": "<PERSON><PERSON><PERSON><PERSON>", "thirdCenter": "Trzecie centrum", "thu": "<PERSON><PERSON><PERSON><PERSON>", "thumbnail": "Miniaturka", "title": "<PERSON><PERSON><PERSON>", "to": "Do", "toHide": "<PERSON><PERSON><PERSON><PERSON>", "toInclusive": "Do (włącznie)", "toPull": "Zadania do przejęcia", "tooltip": "Podpowiedź", "total": "Total", "tprocName": "Szablon procesu", "tsk": "<PERSON><PERSON><PERSON>", "tskAssignDues": "Ustaw ograniczenia czasowe dla tego zadaniak", "tskName": "Nazwa zadania", "tskNum": "Numer zadnia", "tskSolver": "Właściciel zadania", "tskTemplate": "Szablon zadania", "tskVar": "zadanie", "tsksDone": "Ukończony", "tsksSolvers": "Waściciele zadania", "ttAdd": {"heading": "<PERSON><PERSON><PERSON>", "body": "Pozwala na dodawnie nowych przedmiotów czy parametrów, które nie zostały jeszcze zdefiniowane."}, "ttAddActivity": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddAttach": {"heading": "Dodaj dokument", "body": "Pozwala na dodawanie nowych dokumentów."}, "ttAddAttribute": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddContainer": {"heading": "<PERSON><PERSON><PERSON>", "body": "Dodaj zasobnik z wybraną zawartością"}, "ttAddFile": {"heading": "<PERSON><PERSON><PERSON>", "body": ""}, "ttAddStructure": {"heading": "Dodaj przedmiot do struktury organizacyjnej", "body": "Pozwala na dodawnia nowego przedmiotu struktury organizacyjne bądź nowego parametru, który nie został jeszcze zdefiniowany."}, "ttAddTemp": {"heading": "<PERSON><PERSON><PERSON> s<PERSON>", "body": "Tworzenie nowych szablonów spraw. Właścicielem szablonu będzie aktualnie zalogowany użytkownik. Szablon zostanie automatycznie przypisany „under development“ status."}, "ttAddTsk": {"heading": "Do<PERSON>j nowe zadanie", "body": "Tworzenie nowego zadania w szablonie procesu. Parametr zadania może byc określony w zależności od typu zadnania. Linki do innych zadań mogą zostać dodane bądź edytowane w Grafie bądź zakładce linków."}, "ttAddTskGraph": {"heading": "Do<PERSON>j nowe zadanie", "body": "Tworzenie nowego zadania w szablonie procesu. Parametr zadania może byc określony w zależności od typu zadnania. Linki do innych zadań mogą zostać dodane bądź edytowane w Grafie bądź zakładce linków."}, "ttAddUser": {"heading": "Dodaj nowego użytkownika", "body": "Dodaj nowego użytkownika. Każdy użytkownik musi posiadać unikatową nazwę użytkownika. Podstawowe informacje mogą być ustawione dla użytkowników, wraz z ich przypisaniem do struktury organizacyjnej i przypisaniem ról. Nowi użytkownicy otrzymują automatycznie status zablokowany."}, "ttAddVar": {"heading": "Dodaj nową zmienną", "body": "Tworzenie nowej zmiennej w szablonie sprawy. Każda zmienna zawiera informacje, którymi mogą zarząd<PERSON>ć właściciele zadań. Możliwe jest określenie nazwy, typu i wartości domyślnych zmiennej."}, "ttAddVice": {"heading": "<PERSON><PERSON><PERSON> vice", "body": ""}, "ttAssignAttribute": {"heading": "Przypisanie atrybutów do typu dokumentu logicznego", "body": ""}, "ttAssignTsk": {"heading": "Przypisanie", "body": "Umożliwia przypisanie zadania do określonego właściciela zadania lub dodanie elementu do określonej struktury."}, "ttCases": {"heading": "Sprawy", "body": ""}, "ttOverviews": {"heading": "Podglądy", "body": ""}, "ttChangePass": {"heading": "<PERSON><PERSON><PERSON> hasła", "body": "<PERSON><PERSON><PERSON><PERSON> has<PERSON>ł <PERSON>, którzy są zarządzani bezpośrednio w środowisku aplikacji. Jeśli użytkownicy są zarządzani przez usługę zewnętrzną (LDAP), należy zarządzać hasłem w usłudze zewnętrznej ”."}, "ttClose": {"heading": "Zamknij", "body": "Okno zostanie zamknięte bez zapisywania zmian"}, "ttCloseTemp": {"heading": "Zamknik", "body": "Okno z definicjami szablonów zostanie zamknięte."}, "ttCompleteTsk": {"heading": "Zakończ zadanie", "body": "Potwierdza, że zadanie zostało wykonane i wysyła je do dalszego przetwarzania zgodnie ze wstępnie zdefiniowaniem."}, "ttContact": {"heading": "Kontakt", "body": "Wyświetla kontakty dla kierownika zadania."}, "ttContainerSettings": {"heading": "Ustawienia", "body": "Pozwala zmienić ustawienia dla danego zasobnika."}, "ttCopyHdr": {"heading": "Sko<PERSON>u<PERSON>ł<PERSON>", "body": "Tworzy kopię zaznaczonego nagłówka. Wyboru nagłówka dokonuje się poprzez kliknięcie w tabeli nagłówków szablonów.."}, "ttCopyTemp": {"heading": "<PERSON><PERSON><PERSON>", "body": "Tworzy kopie zaznaczonego szablonu. Wyboru szablonu dokonuje się, klikając tabelę szablonów procesów."}, "ttCopyVar": {"heading": "<PERSON><PERSON><PERSON><PERSON>nn<PERSON>", "body": "Kopia definicji dla wybranej zmiennej i zapis zmiennej pod nową nazwą. Zmienne są wybierane przez kliknięcie w tabeli zmiennych."}, "ttDel": {"heading": "Usuń", "body": "Usuwa zaznaczone przedmioty."}, "ttDelAttach": {"heading": "Usuń dokument", "body": "Usuwa zaznaczony dokument."}, "ttDelConnection": {"heading": "Usuń link", "body": "Usuń wybrane łącze między dwoma zadaniami sprawy. Usunięcie wymaga potwierdzenia. Usuwanie odbywa się dla wybranego łącza. W<PERSON><PERSON>rz link, klikając go w tabeli linków."}, "ttDelFolder": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON><PERSON> wybranego folderu."}, "ttDelOverview": {"heading": "Usunięcie przegląd", "body": "Usuwa zaznaczony przegląd."}, "ttDelTemp": {"heading": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "body": "Przypisuje usunięty status do szablonu. <PERSON><PERSON><PERSON>, gdy żądanie usunięcia zostanie powtórzone, szablon zostanie fizycznie usunięty. Akcja zostanie zastosowana do wybranego szablonu. <PERSON><PERSON><PERSON><PERSON>, klikając go w tabeli szablonów spraw. "}, "ttDelTsk": {"heading": "Usunięcie zadnia", "body": "Usunięcie wybranego zadania. Usunięcie wymaga potwierdzenia. Wraz z zadaniem wszystkie powiązane linki do innych zadań w szablonie procesu zostaną usunięte. <PERSON><PERSON><PERSON>rz <PERSON>ada<PERSON>, klikając je w tabeli zadań. "}, "ttDelTskOrConnection": {"heading": "Us<PERSON>ń zadanie lub link", "body": "Usuwanie wybranego zadania lub wybranego łącza między dwoma zadaniami procesu. To działanie musi zostać potwierdzone. Powiązane linki do innych zadań procesu zostaną usunięte wraz z zadaniem. Kliknij aby wybrać."}, "ttDelVar": {"heading": "Usunięcie zmiennej", "body": "Usunięcie wybranej zmiennej. To działanie musi zostać potwierdzone. Zmienna nie będzie już dostępna dla poszczególnych zadań procesu. Zmienne wybiera się, klikając tabelę zmiennych."}, "ttDelVice": {"heading": "<PERSON><PERSON><PERSON> vice", "body": ""}, "ttDetailCase": {"heading": "Szczegóły", "body": "Wyświetla szczegóły wybranej sprawy."}, "ttDetailCertificate": {"heading": "Szczegóły certyfikatu", "body": "Wyświetla szczegóły wybranego certyfikatu."}, "ttDetailHistory": {"heading": "Szczegóły", "body": "Wyświetla szczegóły dla wybranego podmiotu."}, "ttDetailTsk": {"heading": "Szczegóły zadnia", "body": "Wyświetla szczegóły wybranego zadnaia."}, "ttDmsFolderAdd": {"heading": "<PERSON><PERSON><PERSON> nowy folder", "body": "Wstawianie nowego folderu. Jeśli wybrany zostanie jeden z folderów, folder nadrzędny zostanie wstępnie wypełniony."}, "ttDmsFolderEdit": {"heading": "<PERSON><PERSON><PERSON><PERSON> folder", "body": "<PERSON><PERSON><PERSON><PERSON> wy<PERSON>ny folder."}, "ttDocuments": {"heading": "Przechowywanie dokumentów", "body": ""}, "ttDownload": {"heading": "<PERSON><PERSON><PERSON>", "body": "Pobiesz wybrany plik."}, "ttDropContainer": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Upuść zadobnik z pulpitu"}, "ttENotification": "Powiadomienie e-mail", "ttEdit": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umożliwia edytowanie wybranych podmiotów."}, "ttEditAttach": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umożliwia przeglądani oraz edytowanie atrybutów (metadane) upload' owanego pliku."}, "ttEditConnection": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Edycja połączenia między dwoma zadaniami. Możliwe jest edytowanie parametrów zachowania łącza i warunków łącza. Akcja zostanie zastosowana do wybranego łącza. Linki wybiera się, klikając je w tabeli linków."}, "ttEditOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umożliwia edytowanie zaznaczonych przeglądów."}, "ttCopyOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Tworzy kobie zaznaczonego przeglądu."}, "ttEditPath": {"heading": "Dodaj p<PERSON>ląd", "body": "Umożliwia zdefiniowanie nowego przeglądu."}, "ttEditTemp": {"heading": "<PERSON><PERSON><PERSON><PERSON> definicji s<PERSON>blonu", "body": "Edycja szablonu sprawy. Dowolny parametr szablonu można edytować. Akcja jest wykonywana dla wybranego szablonu. <PERSON><PERSON><PERSON><PERSON>, klikaj<PERSON>c tabelę szablonów spraw."}, "ttEditTsk": {"heading": "<PERSON><PERSON><PERSON>j <PERSON>", "body": "Edycja informacji o zadaniu i parametrów zadania. Akcja zostanie zastosowana do wybranego zadania. <PERSON><PERSON><PERSON><PERSON>, klikając tabelę zadań."}, "ttEditTskOrConnection": {"heading": "Edyzja zadań lub linków", "body": "Edycja informacji o zadaniu i parametrów zadania lub edycja łączy między dwoma zadaniami, ich parametrami behawioralnymi i warunkami łącza. Akcja zostanie zastosowana do wybranego zadania lub łącza. Kliknij aby wybrać."}, "ttEditTskVars": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Edtuj zmienne zadania"}, "ttEditUser": {"heading": "Edytowanie informacji użytkownika", "body": "Edycja podstawowych informacji o użytkownikach, <PERSON><PERSON><PERSON>, przypisaniu do jednostki organizacyjnej i przypisaniu ról. Akcja zostanie zastosowana do wybranego użytkownika. Użytkownicy są wybierani przez kliknięcie tabeli użytkowników."}, "ttEditVar": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Edytowanie nazwy, typu i domyślnych wartości zmiennych. Akcja jest stosowana do wybranej zmiennej. Zmienne są wybierane przez kliknięcie w tabeli zmiennych."}, "ttEnotTgt": "Odbiorca", "ttEnotTgtG": "Kierownik zadania", "ttEnotTgtO": "Właściciel sprawy", "ttEnotTgtP": "%s", "ttEnotTgtR": "Rola %s", "ttEnotTgtS": "Jednostka organizacyjna %s", "ttEnotTgtT": "Właściciel zadnia %s", "ttEvent": {"heading": "Domyślne zadanie", "body": "Natychmiastowe wywołanie zdarzenia w tym zadaniu.."}, "ttEvents": {"heading": "Wyd<PERSON>zen<PERSON>", "body": "Ustawianie reguł biznesowych w celu reagowania na określone zdarzenia wewnętrzne lub zewnętrzne w systemie. Dostęp wymaga roli $PowerUser."}, "ttFavourites": {"heading": "Lista ulubionych", "body": "Lista wszystkich ulubionych z możliwością ich edycji lub usunięcia z listy."}, "ttFilter": {"heading": "Filtr", "body": "Wyświetla tylko te elementy, które spełniają określone warunki filtrowania."}, "ttFilterPrc": {"heading": "Filtr", "body": "Wyświetla tylko te sprawy, które spełniają określone warunki filtrowania."}, "ttFilterTemp": {"heading": "Filtr", "body": "Wyświetla tylko te szablony, które spełniają określone warunki filtrowania."}, "ttFilterTsk": {"heading": "Filtr", "body": "Wyświetla tylko te zadania, które spełniają określone warunki filtrowania."}, "ttFilterUser": {"heading": "Filtr", "body": "Wyświetla tylko tych użytkowników, którzy spełniają określone warunki filtrowania."}, "ttFullScreen": {"heading": "Pełny ekran", "body": "Pokazuje zawartość zasobnika w trybie pełnoekranowym."}, "ttGraph": {"heading": "<PERSON>", "body": "Graficzne przedstawienie odzwierciedlające obecny status sprawy."}, "ttGraphActualFinish": "Rzeczywiste zakończenie ", "ttGraphActualStart": "Rzeczywista data rozpoczęcia", "ttGraphCond": "Warunki", "ttGraphCond1": "Przynajmniej jeden musi zostać spełniony", "ttGraphCondAll": "wszystkie musi zostać spełnione", "ttGraphCondElse": "<PERSON><PERSON><PERSON>, <PERSON>e spełniony jest inny warunek", "ttGraphDeadlinePo": "<PERSON><PERSON>in wsprowadzony przez właściciela sprawy", "ttGraphDeadlinePs": "Termin: w ciagu %s dni po rozpoczęciu sprawy", "ttGraphDeadlineTs": "Termin: w ciągu %s dni po rozpoczęciu zadania", "ttGraphDelayPo": "Inicjacja zadania: wprowadzona przez właściciela sprawy", "ttGraphDelayPs": "Inicjacja zadania: %s dni od rozpoczęcia sprawy", "ttGraphDelayTs": "Inicacja zadania: %s dni od rozpoczęcia zadania", "ttGraphEnd": "Zakończenie zadania kończy całą sprawę", "ttGraphFinishedBy": "Zakończone przez", "ttGraphHiearchyA": "Wszyscy przełożeni kierownika zadania", "ttGraphHiearchyC": "bezpośredni podwładny kierownika zadania", "ttGraphHiearchyD": "wszyscy podwładni kierownika zadania", "ttGraphHiearchyG": "kierownik zadania", "ttGraphHiearchyL": "Wsz<PERSON><PERSON> ", "ttGraphHiearchyP": "Bezpośredni przełożony kierownika zadania", "ttGraphHiearchyS": "współpracownicy kierownika zadania", "ttGraphLinkFrom": "Z", "ttGraphLinkTo": "Do", "ttGraphMethodL": "Ostatniego właściciela zadnia %s", "ttGraphMethodS": "Do właściciela zadania wybranego przez przełożonego", "ttGraphMethodT": "Do automatycznie wybranego właściciela zadania", "ttGraphMethodV": "Do właścicciela zadania przypisanego do zmiennej %s", "ttGraphMultiinstance": "Multi-instancja", "ttGraphNoneMand": "Obowiązkowy link", "ttGraphOnlyOnce": "Uruchom tylko raz", "ttGraphSave": {"heading": "Zapisz diagram i stwórz szablon", "body": ""}, "ttGraphStart": "Zadanie zostanie aktywowane automatycz po rozpoczęciu sprawy", "ttGraphTaskHiearchy": "Właściciel zadania", "ttGraphTaskMethod": "Zadanie zostanie przypisane", "ttGraphTaskOwner": "Kierownik zadania", "ttGraphTaskOwnerOS": "Manager jednostki organizacyjnej", "ttGraphTaskOwnerPO": "Właściciel sprawy", "ttGraphTaskOwnerSU": "Wybrany użytkownik", "ttGraphTaskRole": "Z rolą", "ttGraphTaskTypeA": "Zadanie automatyczne", "ttGraphTaskUser": "Właściciel zadania", "ttGraphWait1": "Parametry wejściow: czekam na jeden", "ttGraphWaitA": "Parametry wejściow: czekam na wszystkie", "ttGraphWaitFirst": "Parametry wejściow: czekam na wszystkie, urucha<PERSON><PERSON> pierwszy", "ttGraphWaitN": "Parametry wejściow: czekam na %s", "ttHandover": {"heading": "Przekaż zadania", "body": "Zezwala na przekazanie zadania innemu dostępnemu użytkownikowi."}, "ttDelegate": {"heading": "Deleguje <PERSON>", "body": ""}, "ttReject": {"heading": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "body": ""}, "ttHelp": {"heading": "Natcyhmiastowa pomoc", "body": "Zezwalanie lub wyłączanie natychmiastowej pomocy. Pomoc jest wyświetlana w postaci bąbelków, które pojawiają się z informacjami o interfejsie użytkownika, gdy funkcje są przerzucane."}, "ttHome": {"heading": "Początkowa strona użytkownika", "body": "Jedno miejsce ze wszystkimi informacjami dla zwykłych użytkowników. Pulpit nawigacyjny zapewnia ogólny widok."}, "ttHtml": {"heading": "Wygeneruj dokumentację", "body": "Generowanie dokumentacji HTML procesu szablonu. W zależności od typu przeglądarki dokument może zostać natychmiast wyświetlony lub zapisany na dysku."}, "ttInclusion": {"heading": "Włączenie", "body": "Eksportuje plik z podsumowaniem autoryzacji i rolami użytkownika, wszystkimi rolami podpisanego użytkownika, jednostkami organizacyjnymi, w których jest członkiem lub menedżerem, w tym hierarchią zadań, w których jest nadzorcą."}, "ttInvAttendees": "Uczestnicy", "ttInvDTEnd": "Koniec", "ttInvDTStart": "Start", "ttInvLocation": "<PERSON><PERSON><PERSON>", "ttInvitation": "Zap<PERSON><PERSON><PERSON>", "ttJustSave": {"heading": "<PERSON><PERSON><PERSON>", "body": "Zapisz zmiany."}, "ttLock": {"heading": "Zablokuj", "body": "Zablokuj lub odblokuj wybór"}, "ttLockUser": {"heading": "Zablokuj", "body": "Zablokuj lub odblokuj użytkownika"}, "ttLogout": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Wylogowanie użytkownika. Po pomyślnym zakończeniu pracy z aplikacją wyświetlany jest początkowy dialog logowania."}, "ttMapping": {"heading": "Mapowanie", "body": "Ogólny przegląd przypisanych zmiennych do odczytu (R), zapisu (W) i obowiązkowego wpisu (M) w poszczególnych zadaniach z możliwością edycji ich przypisania."}, "ttNewCase": {"heading": "Nowa sprawa", "body": "Tworzenie nowej instancji procesu – nowa sprawa. Możliwe jest wybranie spośród dostępnych szablonów procesów lub utworzenie przypadku bez predefiniowanej struktury zadań."}, "ttNewOverview": {"heading": "<PERSON><PERSON><PERSON> w<PERSON>k", "body": "Umożliwia definicję nowego przeglądu."}, "ttOrgStructure": {"heading": "Struktura organizacyjna", "body": ""}, "ttParent": {"heading": "Przeł<PERSON><PERSON><PERSON>", "body": "Przełącz się na przypadek, z którego wyświetlana sprawa została utworzona jako podproces"}, "ttPhoto": {"heading": "Fotografie", "body": "Przesyłanie zdjęć do profilu użytkownika. Obsługuje formaty GIF, JPG i PNG. Rozmiar obrazu zostanie dostosowany automatycznie."}, "ttPlans": {"heading": "Planowanie", "body": "Ustawianie reguł automatycznego uruchamiania jednorazowych lub powtarzających się instancji procesów – przypadki według określonych parametrów. Dostęp wymaga roli $Administrator."}, "ttPrint": {"heading": "<PERSON><PERSON><PERSON>", "body": "Tworzy wydruk."}, "ttRecalc": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Przelicz bieżące zmienne"}, "ttRedirectToPrc": {"heading": "Sprawa", "body": ""}, "ttResetDash": {"heading": "Reset", "body": "Resetuje zakończone zmiany."}, "ttResetSearch": {"heading": "Reset", "body": "Resetuje formularz wyszukiwania."}, "ttRestoreTemp": {"heading": "Przywracanie szablonu", "body": "Przywraca usunięte szablony"}, "ttRevision": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Włącza przesyłanie nowej wersji pliku"}, "ttRoles": {"heading": "Zarządzanie rolami", "body": ""}, "ttRunEvent": {"heading": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>arzenie", "body": "Wywołanie zdarzenia w tej sprawie"}, "ttSave": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Zapisz sprawy i zamknij okno."}, "ttSaveDMSCols": {"heading": "Zapisz kolumny", "body": ""}, "ttSaveSettings": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Zapisz zmiany."}, "ttSaveTsk": {"heading": "<PERSON><PERSON><PERSON>", "body": "Otwarte zadanie zostanie zapisane, możesz do niego wrócić pózniej."}, "ttSearch": {"heading": "Wyszukaj", "body": "Rozpoczyna wyszukiwanie"}, "ttSendNote": {"heading": "<PERSON><PERSON><PERSON>", "body": "Zezwala na dodawanie nowych notatek."}, "ttSetConnectionCond": {"heading": "Warunek", "body": "Dodawanie lub edytowanie warunków łącza. Edycja jest stosowana do wybranego łącza. Kliknij symbol łącza lub warunku, aby go wybrać."}, "ttSetDefaultDash": {"heading": "Ustaw jako <PERSON> pulpit", "body": "Ustawia obecny układ pulpitu jako <PERSON>y"}, "ttShowHideBtn": {"heading": "Pokaż / Ukryj", "body": "Częściowo ukrywa lub pokazuje główne menu."}, "ttSleepCase": {"heading": "Zawieś sprawe", "body": "Oznacza sprawę jako zaw<PERSON>. Sprawa nie będzie już wyświetlana wśród aktywnych zadań, ale w razie potrzeby można zmienić status z powrotem na aktywny i zakończyć całą sprawę później."}, "ttSolve": {"heading": "Otw<PERSON><PERSON>", "body": "Wyświetla okno dialogowe umożliwiające rozpoczęcie pracy z przypisanym zadaniem zgodnie ze wstępnie zdefiniowanym szablonem."}, "ttStatePlan": {"heading": "Status", "body": "Definiuje status planu."}, "ttStatusHdr": {"heading": "Zmień status nagłówka", "body": "Akcja zostanie zastosowana do wybranego szablonu. Dostępne są stany „aktywne” i „nieaktywne”. Wyboru nagłówka dokonuje się poprzez kliknięcie w tabeli nagłówków szablonów."}, "ttStatusTemp": {"heading": "Zmiana statusu s<PERSON>blonu", "body": "Zarządzanie cyklem życia szablonów odbywa się poprzez ustawienie jego stanu. Istnieją stany „w rozwoju”, „aktywne”, „nieaktywne” i „usunięte”. Akcja zostanie zastosowana dla wybranego szablonu. Wybór szablonu odbywa się poprzez kliknięcie w tabeli szablonów spraw."}, "ttSubprocess": {"heading": "Zagnieżdżenie", "body": "Przełącza do sprawy, która została utworzona jako podproces w procesie wyświetlania sprawy."}, "ttTabsButtonMore": {"heading": "<PERSON><PERSON><PERSON><PERSON>j", "body": "Pokazuje więcej opcji."}, "ttTakeTsk": {"heading": "Przejmij zadnie", "body": "Pozwala na przejęcie zadania przez innego właściciela."}, "ttTemps": {"heading": "Szablony procesu", "body": "Centralne miejsce do zarządzania szablonami procesów. Dostęp wymaga roli $PowerUser."}, "ttTiming": {"heading": "Planowanie", "body": "Wprowadza start i koniec zakończneia zadania."}, "ttTsks": {"heading": "Zadania", "body": ""}, "ttUploadSettings": {"heading": "Upload", "body": ""}, "ttUserSetting": {"heading": "Ustawienia użytkownika", "body": "Ustawianie informacji kontaktowych użytkownika, haseł <PERSON> i preferencji użytkownika. Użytkownicy pełniący rolę $Administrator  ,można dalej zarządzać informacjami o swojej organizacji i wystąpieniach w aplikacji TeamAssistant. ”."}, "ttUsers": {"heading": "Administrowanie użytkownikami", "body": "Centralna administracja użytkowników, struktura organizacyjna i role użytkowników. Dostęp wymaga roli $Administrator."}, "ttValidation": {"heading": "Uprawomocnienie", "body": "Sprawdź poprawność szablonu i wyświetl wszystkie istniejące pętle w szablonie. Powiadamia o niespełnionych warunkach i nieużywanych zmiennych."}, "ttViewFile": {"heading": "Widok", "body": ""}, "ttWakeUpCase": {"heading": "Wybudź", "body": ""}, "ttActivateCase": {"heading": "Aktywuj", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Ustaw domyślne kolumny DMS", "body": "Ustawia przypisanie kolumn DMS jako domyślne."}, "ttResetDmsCols": {"heading": "Reset", "body": "Zresetuj przypisanie kolumn DMS."}, "ttRestoreDoc": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Przywraca usunięty dokument."}, "ttSearchHeader": {"heading": "Wyszukaj", "body": ""}, "tue": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "typeOfRepetition": "Typ powtórzenia", "unassignedSolvers": "Według właściciela zadania", "unassignedTaskSolvers": "Nieprzypisany właściciel zadania", "uncategorized": "Bez kategorii", "unfinishedProcesses": "Niezakońcozne sprawy", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unknownUser": "Nieznany użytkownik", "unrestricted": "Nieograniczony", "unspecified": "<PERSON>eok<PERSON><PERSON><PERSON><PERSON>", "upload": "Upload", "uploadFile": "Upload pliku", "uploadPhoto": "Upload zdj<PERSON><PERSON>", "uploadCsv": "Upload csv", "url": "URL", "urlAddress": "Adres URL ", "urlContent": "Zawartoś URL", "use": "Użyj", "user": "Użytkownik", "userByOwnerOfLastTask": "Do użytkownika wybranego przez ostatniego właściciela zadania.", "userE": "Użytkownik", "userFilters": "Filtr użytkownika", "userLock": "Zablokuj", "userLockUnlockQ": "Czy na pewno chcesz zmieniść status użytkownika {{username}}?", "userName": "<PERSON><PERSON>", "userId": "ID Użytkownika", "userOrgStruct": "Należy do jednostki organizacyjnej", "userVice": "Osoba zastępująca", "userViced": "Do zastąpienia", "users": "Użytkownicu", "usersDeleted": "Usunięte", "validation": "Uprawomocnienie", "value": "<PERSON><PERSON><PERSON><PERSON>", "var": "Zmienna", "var-": "Zmienna –", "varChange": "Zmiana zmiennej zostanie ogłoszona wszystkim uczestnikom sprawy", "varTaskMap": "Mapowanie", "varTemp": "<PERSON><PERSON><PERSON>", "variable": "Zmienna", "variableType": "<PERSON><PERSON><PERSON> typ", "vars": "Zmienne", "varsForMandatory": "Zmienne obowiązkowego wpisu", "varsForReading": "Zmienne dla odczytu", "varsForWriting": "Z<PERSON>nna d<PERSON> zapisu", "vices": "Zastępstwa", "viewCVFields": "Dostepne pola", "visForOrgStrMembers": "Widoczne dla członków grupy organizacyjnej", "visForRoleMembers": "Widoczne dla członków z rolą", "headerVisForRole": "Sprawa widoczna dla członków z rolą", "waitForNumOfInputs": "Czeka na: (number of inputs)", "waitsFor": "Czeka na", "waitsForAll": "Czeka na wszystkie", "waitsForOne": "Czeka na jeden", "waitsForSending": "czeka na przesłanie", "waitsRunFirst": "Czekana wszystkie, uruchamia pierwszy", "wakeUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "warning": "Ostrzeżenie", "wed": "Środa", "weekIn": "Tydzień w", "weekly": "Tygodniowo", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withConditions": "Z warunkami", "withoutCond": "Bez warunków", "year": "Rok", "yes": "Tak", "zip": "<PERSON><PERSON>", "move": "Przesuń", "alertClosing1": "Powiadomienie zostanie automatycznie zamknięte:", "inDocuments": "<PERSON> dokumentach", "inVariables": "<PERSON>", "headerTask": "Nagłówek zadania", "planName": "Nazwa planu", "inBulk": "W objęctości", "confirmResetDmsColumns": "Czy na pewno chcesz zresetować kolumny DMS?", "dmsColsUseDef": "Korzystanie z ustawień domyślnych", "dmsColsUseCust": "KOrzystanie z ustawień niestandadowych", "today": "D<PERSON>ś", "alrPlanDeleteFailed": "Usuwanie planu nie powiodło się.", "notRunning": "<PERSON><PERSON>", "alrLackOfPermsToAddTask": "<PERSON>e masz uprawnien do dodawania zadań.", "dragTable": "Przecią<PERSON><PERSON> tabele", "alrDownloadCsvListFailed": "Pobieranie listy plików CSV nie powiodło się.", "alrCsvUploadWrongExtension": "Upload tylko dokumentów z rozszerzeniem *.csv", "addToFav": "Dodaj do ulubionych", "renameItem": "P<PERSON>emia<PERSON><PERSON><PERSON>", "removeFromFav": "Usuń z ulubionych?", "alrAddedToFav": "Dodano do ulubionych.", "alrRemovedFromFav": "Usunięto z ulubionych.", "tskSetAssignDues": "Ustaw ograniczenia czasowe dla zadania", "isNot": "<PERSON>e jest", "alrTskScheduling": "Planowanie zadania...", "alrFavouritesPageExist": "Ta strona znajduje się juz w ulubionych.", "alrFavouritesActionExist": "Ta akcja znajduje sie już w ulubionych.", "alrFavouriteRenamed": "Nazwa ulubionego została zmieniona.", "autoFit": "Autodopasowanie", "passwordIsShort": "<PERSON>ło jest za krótkie.", "changeAttrComplCases": "Zmień atrybuty zakończonej sprawy", "iterateOverVars": "Powtarzanie nad zmiennymi", "nrOfDecimalDigits": "Liczba cyfr dziesiętnych", "onlyNumbers": "<PERSON><PERSON><PERSON>", "maxNumberOfDecimals": "Maks<PERSON>alna liczba cyfr dziesiętnych to", "alrInsertCsv": "Wstaw plik CSV.", "addBefore": "<PERSON><PERSON><PERSON> p<PERSON>ed", "moveBefore": "Przesuń przed", "administration": "Administracja", "ttAdministration": {"heading": "Administracja", "body": ""}, "alrLogsLoadFailed": "Wczytywanie logów nie powiodło się.", "logs": "Logo", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useCompatibleTempl": "Użyj zgodnego szablonu", "overwriteExistTempl": "Nadpisz istniejący szablon", "addNewTempl": "<PERSON><PERSON><PERSON> s<PERSON>", "import": "Imporuj", "export": "Eksportuj", "confirmExportAllTempl": "Eksportować wszystkie szablony?", "confirmExportSelTempl": "Eksportować wybrany szablon? ", "newLogs": "Nowe logi", "container": "Zasobnik", "contents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmRemoveDialog": "<PERSON>zy na pewno chcesz usunąć {{variable}}?", "allMyCases": "Wszystkie moje sprawy", "maintenanceMsg": "Trwają zaplanowane prace <span style=\"color: {{color}};\">konserwacyjne</span>", "alrMaintenanceMsg": "Trwają zaplanowane prace konserwacyjne., spróbuj później.", "alrAttachDownloadLackOfPerms": "Nie masz uprawnień do pobrania tego dokumenu lub dokumen nie został odnaleziony.", "unableToConnect": "Nie można połączyć się z serwerem", "tryLater": "Spróbuj później lub skontaktuj się z administratorem.", "enableTaskDelegation": "Włącz delegowanie zadań", "enableRejectTask": "Włącz odrzucanie zadańE", "confirmRejectTask": "<PERSON>zy na pewno chcesz odrzucić to zadanie?", "rejectTask": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "delegateTask": "Deleguj", "alrRejectingTask": "Odrzucanie zadania...", "alrTaskRejected": "Zadanie zostało odrzucone.", "alrTaskRejectFailed": "Odrzucanie zadania nie powiodło się.", "alrTaskDelegating": "Delegowanie zadania...", "alrTaskDelegated": "Zadanie zostało oddelegowane do użytkownika:", "alrFailedTaskDelegate": "Delegowanie zadania nie powiodło się.", "delegateOnUser": "Deleguj na użytkownika", "plnAssignmentCond": "<PERSON><PERSON><PERSON> pole „Assignments“ pozostaje puste, lista inicjatorów zostanie utworzona poprzez ocenę warunków ograniczających w momencie realizacji planu", "alrUserFiltersSettingsFailed": "Zapisywanie ustawień filtrów użytkownika nie powiodło się.", "general": "Ogólny", "alrUserPhotoLoadFailed": "Wczytywanie zdjęcia użytkownika nie powiodło się.", "publicDynTable": "Opublikuj tabele dynamiczną", "isFullIndexed": "<PERSON> wyszukiwaniu", "datetimeIndexed": "Indeksowane na", "toIndex": "Indeksowa c", "toReindex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "solverChanged": "Właśielie zadnia zmieniony w {{count}} zadaniach", "changeSolverFailed": "Zmiana właściciela zadania nie powiodła się.", "alrTikaParsingFailed": "Wystąpił błąd podczas analizowania dokumentu.", "alrIndexingFailed": "Indeksowanie dokumentów nie powiodło się.", "alrTikaNotRunning": "Usługa analizowania dokumentów jest niedostępna.", "alrIndexingServiceNotRunning": "Usługa indeksowania jest niedostępna.", "alrFulltextNotSet": "Pełny tekst nie został ustawiony.", "asc": "Rosnąco", "desc": "Malejąco", "restore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alrLogosLoadFailed": "Ładowanie logów nie powiodło się.", "indexedDocsCount": "W {{count}} doku<PERSON>ach", "alrIndexedCountLoadFailed": "Wyszukiwanie pełnotekstowe nie jest obecnie dostępne.", "searchAll": "Wyszykaj wszystkie", "searchActual": "TYlko rzeczywiste", "runIndexing": "Uruchom indeksowanie", "alrDocumentIndexing": "Indesowanie dokumentu...", "alrDocumentIndexed": "Dokument został zindeksowany i można go znaleźć, wyszukując.", "alrDocumentIndexedWithMinMetadata": "Dokument został zindeksowani.", "alrDocumentIndexingFailed": "Indeksowanie dokumentu nie powiodło się.", "changingUserProfileForbidden": "Zmienianie profilu uzytkownika jest zabronione.", "uploadingPhotoForbidden": "Upload zd<PERSON><PERSON>cia jest zabroniony.", "alrValidationCalcError": "Błąd sprawdzania poprawności obliczeń", "maintenance": "Ko<PERSON>r<PERSON><PERSON><PERSON>", "maintenanceActivate": "Aktywuj konserwację", "maintenanceInfoText": "Początek i koniec będą wyświetlane użytkownikom po aktywacji konserwacji.", "maintenanceMode": "<PERSON><PERSON>", "alrAvailableCalcFailed": "Dostępne obliczenia nie zostały załadowane.", "alrFillDataForSearch": "Please fill in the search parameters.", "youAreHere": "<PERSON><PERSON><PERSON> tutaj", "invalidDate": "Nieprawidłowy format daty", "alrInvalidFileFormat": "Nieprawidłowy format pliku.", "alrEnter3characters": "Prosze wpisać conajmniej trzy znaki.", "changeCaseOwner": "Zmień właściciela sprawy", "actualCaseOwner": "Rzeczywisty właściciel sprawy", "newCaseOwner": "Nowy właściciel sprawy", "alrCaseOwnerChanged": "Właściciel sprawy został zmieniony.", "alrChangeCaseOwnerFailed": "Zmiana właściciela sprawy nie powiodła się.", "alrCsvSaving": "Zapisywanie pliku CSV...", "alrCsvSaveFailed": "Upload pliku CSV nie powiódł się.", "alrCsvSaved": "Plik CSV został upload' owany.", "allTemplates": "Wszystkie szablony", "specifyCaseIds": "Wprowadź ID spraw", "caseIds": "ID spraw", "caseId": "ID sprawy", "separBySemicolon": "<PERSON><PERSON><PERSON>red<PERSON>", "alrAddCaseIds": "Proszę wprowadzić ID szablonów", "headers": "Nagłówki", "header": "Nagłówek", "defaultHeaderName": "Domyślna nazwa nagłówka", "headerName": "Nazwa nagłówka", "addHeader": "<PERSON><PERSON><PERSON>", "editHeader": "<PERSON><PERSON><PERSON><PERSON>", "templateName": "<PERSON><PERSON><PERSON>", "rolesExecRightsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> role, kt<PERSON><PERSON> będą mogły inicjować sprawy", "orgUnitsExecRightsText": "Przypisz jednostki organizacyjne, które będą mogły inicjować sprawy", "selectedHeader": "Wybrany nagłówek", "alrHeaderDeleted": "Nagłówek został usuniety!", "alrHeaderDeleteFailed": "Usuwanie nagłówka nie powiodło się.", "alrHeaderSaveFailed": "Zapisanie nagłówka nie powiodło się.", "alrHeaderSaved": "Nagłówek został zapisany.", "alrHeadersLoadFailed": "Wczytywanie danych nagłówka nie powiodo się.", "identificator": "Kod nagłówka", "includeDataSimilarProcesses": "Dołącz dane wszystkich podobnych procesów", "confirmCopyCv": "<PERSON>zy na pewno chcesz skopiować wybrany przegląd?", "alrCreatingCopyCv": "Tworzenie kopi przedglądu...", "alrCvCopied": "Przedgląd został skopiowany.", "alrCopyCvFailed": "Tworzenie kopi przeglądu nie powiodło się.", "copyingTemplate": "<PERSON><PERSON><PERSON><PERSON>", "alrCheckTempImportFailed": "<PERSON>e powiodło się sprawdzenie importu szablonu.", "warnings": "Ostrzeżenia", "missingEventsFiles": "Brakujące pliki zdarzeń", "missingEventsFilesText": "Nie znaleziono pliku {{- plik}} ze z<PERSON>zenia {{- zdarzenie}}", "printsOfTemplates": "Drukowanie szablonów", "printsOfTemplatesText": "<PERSON><PERSON><PERSON><PERSON><PERSON> zwr<PERSON>ć uwagę na drukowanie {{- print}} z szablonu {{- template}}. <PERSON><PERSON>ść: {{- value}}", "dupliciteTaskNames": "Duplikuj nazwę zadania", "dupliciteTaskNamesText": "Istnieje zadanie o tej samej nazwie {{- task}} {{- taskId}} wiele razy w szablonie {{- template}}, spowoduje to przerwanie linii!", "dynTableUsed": "Użyto tabele dynamiczną", "suspiciousCalc": "Podejrzane obliczenia", "suspiciousCalcText": "Możliwa brakująca rola / organizacja / użytkownik w obliczeniach {{- calc}}", "missingEvents": "Brakujące wydarzenia", "missingEvent": "Brakujące wydarzenia", "wrongMappingDomains": "Złe mapowanie domen ", "wrongMappingDomainsText": "Opis {{- task}} zadania z {{- template}} s<PERSON><PERSON><PERSON><PERSON>, zawiera złą nazwę domeny, obecn<PERSON> domeną jest {{- actDom}}", "taskDescription": "Opis zadania", "eventsUrl": "URL wydarzenia", "eventsUrlText": "Prawdopodobny błąd w {{- event}} URL wydarzenia, obecną domeną jest {{- actDom}}", "param": "Parametr", "alrServiceNotForTable": "Dane z tej usługi nie są odpowiednie do przeglądania w tabeli.", "alrServiceDataFailedLoad": "Wczytywanie danych serwisowych nie powiodło się.", "alrServiceNoData": "<PERSON><PERSON><PERSON> nie zaw<PERSON> żadnych danych.", "tableColumns": "<PERSON><PERSON><PERSON> ta<PERSON>i", "datetime": "Data i czas", "exactDatetime": "Dokładna data i czas", "dashRestNoColumns": "Brak ustawionych kolumn – wy<PERSON>rz je z ustawień zasobnika", "loadService": "Wczytywanie serwisu", "useCompatibleRole": "Użyj kompatybilnej roli", "overwriteExistRole": "<PERSON><PERSON><PERSON><PERSON> role", "addNewRole": "<PERSON><PERSON><PERSON> nową role", "templateImportFailed": "Importowanie szablonu nie powiodło się.", "templateImport": "Import szab<PERSON>u", "templateImportNoData": "Nie znaleziono danych do zaimportowania szablonu.", "variableImportNoData": "Nie znaleziono danych do zaimportowania zmiennych.", "ttTemplateImport": {"heading": "Import szab<PERSON>u", "body": "Wybrany zostanie folder z definicjami jednego lub więcej szablonów, a następnie upload' owany."}, "showUnfinishedProcesses": "Pokasz nieukończone sprawy", "expMaintenanceEnd": "Oczekiwany koniec konserwacji", "alrScriptSaveFailed": "Zapisywanie skryptu nie powiodło się.", "editScript": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "addScript": "<PERSON><PERSON>j sk<PERSON>pt", "alrRunScript": "Uruchamianie skryptu...", "alrScriptCompleted": "Skrypt ukończony.", "alrFailedScriptStart": "Uruchamianie skryptu nie powiodło się.", "alrScriptDocsLoadFailed": "Wczytywanie dokumentacji skryptów nie powiodło się.", "alrScriptLoadFailed": "Wczytywanie skryptów nie powiodło się.", "switchAdminUser": "Przełączanie administratora / użytkownika", "ttSwitchAdminUser": {"heading": "Przełączanie administratora / użytkownika", "body": ""}, "ttSwitchViewport": {"heading": "Przełączanie widoku mobilnego / komputera", "body": ""}, "alrEventDataLoadFailed": "Wczytywanie danych wydarzenia nie powiodło się.", "alrEventRuleDataLoadFailed": "Wczytywanie danych reguł nie powiodło się.", "cancellation": "<PERSON><PERSON><PERSON><PERSON>", "tTaskAutoCancellCaption": "Zadanie zostanie automatycznie anulowane jeżeli", "codeMirrorHelp": "Kliknij gdziekolwiek w edytorze i wcziśnij Ctrl+Space aby przejrzeć okno pomocy.", "codeMirrorHelpJs": "<PERSON><PERSON> <PERSON><PERSON><PERSON> listę wszyst<PERSON>, klik<PERSON>j <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "<PERSON><PERSON><PERSON>", "editEvent": "<PERSON><PERSON><PERSON><PERSON>", "term": "Warunek", "columnOrder": "Porządek kolumn", "alrLoadEventsButtonsFailed": "Wczytywanie przycisku tabeli nie powiodło się.", "showButtonsCol": "Pokaż kolumnę akcji", "button": "Przycisk", "enableButtonInTasks": "Pokasz jako przycisk w liście zadań", "alrEventDoesntExist": "Wybrane wydarzenie nie istnieje", "alrEventRuleSaveFailed": "Zapisywanie reguły wydarzenia nie podwiodło się.", "variableNames": "Nazwy zmiennych", "fsEvent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrEventDeleteFailed": "Usuwanie wydarzenia nie powiodło się.", "fsRule": "<PERSON><PERSON><PERSON>", "alrRuleDeleteFailed": "Usuwanie reguły nie powiodło się.", "alrRuleStatusChangeFailed": "Zmiana statusu refuły nie powiodła się.", "ruleActivateDeactivateQ": "Czy na pewno chcesz zmienic status reguły?", "docUploadedPrivate": "Dokument zostanie upload'o<PERSON>y jako p<PERSON>ny", "fileOwner": "Właściciel pliku", "planOk": "Ok", "userNotAuthToStartTempl": "Użytkownik nie ma autoryzacji do rozpoczęcia sprawy przez ten szablon", "planStartDate": "Data startu", "useOnlyFutureDates": "Tylko przyszłe daty", "alrGenerateHtmlFailed": "Generowanie HTML nie powiodło się.", "alrNoPermsToAddNoteInVice": "<PERSON>e masz uprawnien aby dodawać notatki jako zastęp<PERSON>.", "alrNoPermsToAddDocInVice": "<PERSON><PERSON> masz uprawnień aby dodawać dokumend jako zastę<PERSON>.", "current": "Obecny", "indexation": "IIndeksowanie", "attemptToRestoreConnection": "Próba przywrócenia połączenia w", "loginWillExpire": "Login wygaśnie w", "unsavedDataWillBeLost": "Niezapisane dane zostaną utracone.", "alrFileSaveLikeAttachViceError": "Twój zastępca ma uprawnienia tylko do przeglądania!", "alrFileSaveLikeAttachStoreError": "Zapisywanie wydruku jako dokument do sprawy nie powiodło się.", "useCompatibleEvent": "Użyj zgodnego wydarzenia", "overwriteExistEvent": "Nadpisz istniejące wydarzenie", "addNewEvent": "<PERSON><PERSON>j nowe wydarzenie", "useCompatibleUser": "Użyj zgodnego użytkownika", "overwriteExistUser": "Nadpisz itniejącego użytkownika", "addNewUser": "Dodaj nowego użytkownika", "useCompatibleUnit": "Użyj zgodnej jednostki organizacyjnej", "overwriteExistUnit": "Nadpisz istniejącą jednostkę organizacyjną", "addNewUnit": "Dodaj nową jednostkę organizacyjną", "addNewDynTable": "<PERSON><PERSON><PERSON> nową tabelę <PERSON>ą", "useCompatibleDynTable": "Użyj zdgosnej tabeli dynamicznej", "enterDiffNameRoot": "Proszę wprowadź inna nazwę niż Root.", "ttTemplatesExport": {"heading": "Eksportuj szablony", "body": "Eksportuj wybrane szablony do folderu. Możliwe jest wybranie nazwy i lokalizacji eksportowanego pliku. Akcja zostanie zastosowana do wybranego szablonu. <PERSON><PERSON><PERSON><PERSON> s<PERSON>, klikając go w tabeli szablonów spraw."}, "ttTemplatesExportAll": {"heading": "Eksportuj wszystkie szablony", "body": "Eksportuj wszystkie aktualnie wyświetlane szablony do pliku. Możliwe jest wybranie nazwy i lokalizacji eksportowanego pliku. Wybór szablonów może być ograniczony przez ustawienie odpowiednich warunków filtrowania."}, "exportAll": "Eksportuj wszystkie", "noTemplatesToExport": "Brak szablonów do eksportu.", "skip": "Pomiń", "ttSkipTemplate": {"heading": "Pomiń szablon", "body": "Pomiń import obecnego szablonu i wyświetl następny."}, "alrInvalidImportData": "Nieprawidłowe dane importu", "alrUsersNotLoaded": "Wszytywanie użytkownika nie powiodło się.", "caseOverview": "Przegląd spraw", "alrRolesNotLoaded": "Wczytywanie roli nie powiodło się.", "changeLang": "Zmień język", "reactivatesPlan": "reaktywuje plan", "alrOrgUnitsNotLoaded": "Wczytywanie jednostek organizacyjnych nie powiodło się.", "refreshPage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> strone", "stayLogged": "Zostań zalogowany", "showTime": "Pokaż znacznik czasu w przeglądach", "managerIn": "manager w {{orgUnit}}", "usageStats": "Statystyki użycia", "month": "<PERSON><PERSON><PERSON><PERSON>", "alrUsageStatsLoadFailed": "Nie można załadować statystyk użytkowania.", "accessLog": "<PERSON>zienn<PERSON> dostepu", "durationInMs": "<PERSON>zas trwania (ms)", "task": "<PERSON><PERSON><PERSON>", "operation": "<PERSON><PERSON><PERSON>", "active_users": "Liczba aktywnych użytkowników", "active_template_processes": "Liczba aktywnych procesów", "active_headers": "Liczba aktywnych nagłówków", "active_users_able_to_create_a_process": "Liczba aktywnych użytkowników, którzy uruchomili proces", "users_that_solved_a_task": "Liczba użytkowników, którzy rozwiązali przynajmniej jedno zadanie", "solvers_or_can_create_a_process": "Liczba użytkowników, którzy rozwiązali zadnie lub są w stanie uruchomić proces", "mobile_app_paired_users": "Liczba sparowanych użytkowników aplikacji mobilnej", "calculationsLogs": "Dziennik obliczeń", "translatedScript": "Przetłumaczone skrypty", "originalScript": "Skrypty oryginalne", "tskId": "ID zadania", "alrCalculationsDocsLoadFailed": "Nie można załadować dokumentów do obliczeń.", "alrCalculationsValidationFailed": "Sprawdzanie poprawności obliczeń nie powiodło się.", "linkPriority": "Priorytety linków", "dateFormat": "DD/MM/RRRR", "alrConvertErrorJsonNeon": "Błąd podczas konwersji json -> neon.", "alrInvalidData": "Błędna data.", "sharedVar": "Wspólna zmienna", "guide": "Przewodnik", "guideFs": "Przewodnik", "guides": "Przewodniki", "alrGuidesLoadFailed": "Wczytywanie przewodników nie powiodło się.", "language": "Język", "default": "Domyślny", "next": "Nastepny", "previous": "Poprzedni", "targetElementNotFound": "Nie znaleziono elementu docelowego", "documentation": "Documentacja", "matchesRegular": "Nie pasuje do reguły", "secondAllowedValues": "Dozwolona wartość ; 0 to szczyt minuty", "everySec": "co sekundę", "listOfSec": "lista sekund; to znaczy 0,30 byłoby 0 i 30 sekund", "rangeOfSec": "zakres sekund; to znaczy. 0–5 to sekundy 0, 1, 2, 3, 4 i 5 (moż<PERSON>z również <PERSON><PERSON> z<PERSON> 0–5,30–35)", "slashSec": "wartości kroku pominą określoną liczbę w zakresie; tj. * / 5 jest co 5 sekund, a 0–30 / 2 co 2 seku<PERSON> mi<PERSON> 0 a 30 sekund", "minuteAllowedValues": "dozwolone wartości; 0 to s<PERSON><PERSON><PERSON> godziny", "everyMin": "co minute", "listOfMin": "lista minut; to znaczy. 0,30 to 0 i 30 minut", "rangeOfMin": "a range of minutes; ie. 0–5 would be minutes 0, 1, 2, 3, 4, and 5 (you can also specify a list of ranges 0–5,30–35)", "slashMin": "zakres minut; to znaczy. 0–5 to minuty 0, 1, 2, 3, 4 i 5 (moż<PERSON>z również podać <PERSON><PERSON> z<PERSON> 0–5,30–35)", "hourAllowedValues": "dozwolone wartości; 0 to północ", "everyHour": "co godzine", "listOfHour": "lista godzin; to znaczy. 0,12 będzie równe 0 i 12 godzinom", "rangeOfHour": "z<PERSON><PERSON> godzin; to znaczy. 19–23 to god<PERSON><PERSON> 19, 20, 21, 22 i 23 (moż<PERSON>z również pod<PERSON><PERSON> <PERSON><PERSON> z<PERSON> 0–5,12–16)", "slashHour": "wartości tep pominą określoną liczbę w zakresie; tzn. * / 4 jest co 4 godziny, a 0–20 / 2 co 2 godziny między 0 a 20 godziną", "dayAllowedValues": "<PERSON><PERSON><PERSON><PERSON> war<PERSON>", "everyMonthDay": "każdego dnia miesiąca", "listOfDay": "lista dni; to znaczy. 1,15 będzie 1 i 15 dniem miesiąca", "rangeOfDay": "zakres dni; to znaczy. 1–5 to dni 1, 2, 3, 4 i 5 (moż<PERSON>z również podać <PERSON><PERSON> z<PERSON> 1–5,14–30)", "slashDay": "wartości kroku pominą określoną liczbę w zakresie; tj. * / 4 jest co 4 dni, a 1–20 / 2 co 2 dni między 1 a 20 dniem miesiąca", "allowedValues": "<PERSON><PERSON><PERSON><PERSON> war<PERSON>", "everyMonth": "ka<PERSON>dego <PERSON>", "listOfMonth": "lista miesięcy; to znaczy. 1,6 to styczeń i czerwiec", "rangeOfMonth": "zakres miesi<PERSON>; to znaczy. 1–3 to s<PERSON><PERSON><PERSON><PERSON>, luty i marsz (możesz również podać listę zakresów 1–4,8–12)", "slashMonth": "wartości kroku pominą określoną liczbę w zakresie; tj. * / 4 jest co 4 miesi<PERSON><PERSON>, a 1–8 / 2 co 2 miesi<PERSON>ce między styczniem a sierpniem", "weekAllowedValues": "do<PERSON><PERSON><PERSON> warto<PERSON>ci; 0 = niedziela, 1 = poniedziałek, 2 = wtorek, 3 = środ<PERSON>, 4 = c<PERSON>wartek, 5 = piątek, 6 = sobota", "everyWeekDay": "każdy dzień tygodnia", "listOfWeekDay": "lista dni; to znaczy. 1,5 to poniedziałek I piątek", "rangeOfWeekDay": "zakres dni; to znaczy. 1–5 to PN, WT, ŚR, CZW i PT (możesz również podać listę zakresów 0–2,4–6)", "slashWeek": "wartości kroku pominą określoną liczbę w zakresie; tj. * / 4 jest co 4 dni, a 1–5 / 2 co 2 dni między poniedziałkiem a piątkiem", "contrab": "Cron - pole {{variable}} ", "cSecond": "sekund", "cMinute": "minut", "cHour": "godzin", "cDay": "dni", "cMonth": "<PERSON><PERSON><PERSON><PERSON>", "cWeekDay": "dni tygodnia", "seconds": "sekundy", "minutes": "minuty", "hours": "god<PERSON>y", "days": "dni", "weeks": "tygodnie", "socketOk": "<PERSON><PERSON><PERSON> zawie najnowsze dane", "socketBroken": "Przywróć połączenie do bieżącej aktualizacji danych", "newTask": "Nowe zadanie", "report": "<PERSON><PERSON>", "ttCaseReport": {"heading": "<PERSON><PERSON>", "body": ""}, "usersRights": "Prawa użytkownika", "visPerRole": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wedł<PERSON> roli", "manualEvents": "Reczne wydarzenia", "noTasks": "Brak nowych zadań", "emptyFavs": "Lista ulubionych jest pusta", "crons": "<PERSON><PERSON><PERSON>", "cronsHistory": "Historia cronów", "redirBefStart": "Przed rozpoczęciem przekieruj do", "lastRun": "Ostatniego uruchomienia", "nextRun": "Nastepnego uruchomienia", "syntax": "Składni", "alias": "Pseudonim", "stop": "Stop", "restart": "<PERSON><PERSON><PERSON><PERSON> ponownie", "restartCronProcess": "Uruchom ponownie kontekst procesu", "ttRestartCron": {"heading": "Uruchom ponownie crona", "body": ""}, "ttRestartCronProcess": {"heading": "Uruchom ponownie proces", "body": ""}, "ttResetCron": {"heading": "Zresetuj crona", "body": ""}, "ttRunCron": {"heading": "Uruchom crona", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "Cron został zatrzymany.", "alrCronStopFailed": "Zatrzymanie crona nie powiodło się.", "alrCronRunning": "Cron został uruchomiony.", "alrCronRunFailed": "Uruchomieni crona nie powiodło się.", "alrCronReset": "Cron zostal pryzwrócony do ustawień domyślnych.", "alrCronResetFailed": "Resetowanie crona nie powiodło się.", "alrCronRestart": "Cron został uruchomiony ponownie.", "alrCronRestartFailed": "Żądanie pownownego uruchomienia crona nie powiodło się.", "alrCronUpdated": "Cron został pomyślnie zapisany.", "alrCronUpdateFailed": "Żądanie aktualizacji crona nie powiodło się.", "confirmRunCronDialog": "<PERSON><PERSON><PERSON>, że ch<PERSON><PERSON> uru<PERSON> wybrany cron?", "confirmStopCronDialog": "<PERSON><PERSON><PERSON>, że ch<PERSON><PERSON> uru<PERSON> wybrany cron?", "confirmResetCronDialog": "Czy na pewno chcesz zresetować cron do ustawień fabrycznych?", "confirmRestartCronDialog": "<PERSON><PERSON><PERSON> pew<PERSON>, że chesz uruchomic pownosnie wybrany cron?", "confirmUpdateCronDialog": "<PERSON><PERSON><PERSON>, że chcesz zmienic status kron?", "alrProcessRestart": "Proces crona został ponowni uruchomiony!", "alrProcessRestartFailed": "Żądanie ponownego uruchomienia corn nie powiodło sięd.", "confirmRestartProcessDialog": "Czy na pewno chcesz ponownie uruchomić cały proces crona? Uważaj, wydarzy się pełny restart wszystkich cronów i cały kontekst ", "cronParams": "Parametry", "alrPresetLogFiltersLoadFailed": "Nie można załadować wstępnie ustawionych filtrów dziennika.", "timeRange": "<PERSON><PERSON><PERSON>", "presetFilters": "Wstępnie ustawione filtry", "params": "Parametery", "authentication": "Poświadczenie", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Nazwa parametru", "authMethod": "Metoda uwierzytelnienia", "taskAlreadyEdited": "Zadanie jest już edytowane przez innego użytkownika.", "taskEditedByAnotherUser": "Inny użytkownik rozpoczął edycję zadania.", "tempAlreadyEdited": "Szablon jest już edytowany przez innego użytkownika.", "tempEditedByAnotherUser": "Inny użytkownik zaczął edytować szablon.", "test": "Test", "notInRightormat": "Niepoprawny format", "notSent": "<PERSON><PERSON> w<PERSON>", "ttTableExportExcel": {"heading": "Eksportuj tabele", "body": "Eksportuje tabelę do pliku xlsx"}, "ttTableExportCsv": {"heading": "Eksportuj tabele", "body": "Eksportuje tabelę do pliku csv"}, "searchInSuspended": "Wyszukaj również w zawieszonych sprawach", "alrScriptDocsFailed": "Nie można zapisać dokumentacji skryptu.", "currentlyRunning": "Aktualnie działające", "onStart": "Na starcie", "onEnd": "Na koniec", "onHand": "<PERSON> ", "onRecalc": "Po ponownym przeliczeniu ", "onPull": "Przed przejęciem", "yesterday": "<PERSON><PERSON><PERSON><PERSON>", "tomorrow": "<PERSON><PERSON>", "replyRecipient": "Odpowiedz odbiorcy", "emptyHe": "<PERSON><PERSON><PERSON>", "archivedLogs": "Zarchiwizowane dzienniki", "basicMode": "<PERSON><PERSON> podstawowy", "expertMode": "<PERSON><PERSON>", "ttBasicMode": {"heading": "<PERSON><PERSON> podstawowy", "body": "Ukrywa niektóre elementy lub opcje w formularzu."}, "ttExpertMode": {"heading": "<PERSON><PERSON>", "body": "Wyświetla niektóre elementy lub opcje w formularzu."}, "helpOverviewFolder": "Możesz włączyć przegląd w strukturze katalogów za pomocą ukośników.<br /><i>(e.g. Invoices/All Received Invoices)</i>", "helpOverviewIncludeSimilar": "<PERSON><PERSON><PERSON> w<PERSON>, będą również wyświetlane sprawy z innych nagłówków szablonu.", "helpOverviewSysVars": "Pola oznaczone (sys) to pola systemowe, które są częścią każdego procesu.", "off": "Off", "toPlan": "Plan", "alrMaintenanceComing": "O {{time}} rozpocznie się zaplanowana konserwacja systemu. Zapisz swoją pracę.", "addVersion": "Nowa wersja", "alrCreateTempVersionFailed": "Utworzenie nowej wersji szablonu nie powiodło się.", "alrChangeTempVersionFailed": "<PERSON>e udało się zmienić wersji szablonu.", "alrPreviewAttachmentsFailed": "Podgląd załączników nie powiódł się", "alrPreviewAttachmentsWrongFormat": "Podgląd załączników nie powiódł się – nieobsługiwany format pliku", "alrTemplTsksLoadFailed": "<PERSON>e udało się załadować zadanie szablonu.", "alrTempVersionsLoadFailed": "Nie można załadować wersji szablonu.", "applyInAllTasks": "Zastosuj do wszystkich zadań", "applyInTasks": "Użyj w zadaniach", "bcRecipient": "Adresat ukrytej kopii", "copyRecipient": "Adresat kopii", "bold": "Pogrubienie", "configuration": "Konfiguracja", "confirmCreateTempVersion": "<PERSON>zy na pewno chcesz utworzyć nową wersję szablonu?", "copyToMultiinstances": "Skopiuj do MultiInstance", "cursive": "Ku<PERSON>ywa", "customization": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValues": "Domyś<PERSON><PERSON> war<PERSON>", "elementColor": "<PERSON><PERSON>", "eventw": "<PERSON><PERSON>nie czeka na to wyd<PERSON><PERSON><PERSON> „{{task}}“ z szablonu „{{template}}“", "externalLang": "Język zewnętrzny", "fontColor": "<PERSON><PERSON>", "fontSize": "Rozmiar <PERSON>ki", "handOverToUser": "Przekaż użytkownikowi", "large": "<PERSON><PERSON><PERSON>", "medium": "Średni", "previewNotAvailable": "Podgląd dokumentu nie jest możliwy ze względu na typ dokumentu.", "showAsPreview": "Wyświetl podgląd", "small": "Ma<PERSON><PERSON>", "subscribe": "Subskrybuj", "time": "Czas", "hdrStatusQ": "Czy na pewno chcesz zmienić status nagłówka?", "timeoutHMS": "Timeout: (hh:mm:ss)", "ttCreateTempVersion": {"body": "", "heading": "Utwórz nową wersję szab<PERSON>u"}, "ttSubscribeCv": {"body": "Wybrany raport zostanie wysłany do Ciebie w każdy dzień tygodnia o ustalonej godzinie.", "heading": "Raport subskrypcji"}, "values": "<PERSON><PERSON><PERSON><PERSON>", "version": "<PERSON><PERSON><PERSON>", "waitsForEventTip": "Sprawa oczekująca na wydarzenie: „{{event}}“", "caseStatuses": "Statusy sprawy", "statuses": "Statusy", "Manuals": "Podręczniki", "forReading": "D<PERSON> o<PERSON>", "forReadWrite": "Dla odczytu i zapisu", "size": "Rozmiar", "mandatoryVar": "<PERSON><PERSON><PERSON><PERSON>", "emptyRequiredVarMessage": "<PERSON>s, pusta wymagana zmienna", "duration": "<PERSON><PERSON><PERSON><PERSON>", "alrDynConditionsFailed": "Nie można wykonać zadania. Spróbuj odświeżyć stronę lub skontaktuj się z Administratorem lub działem Helpdesk.", "caseActivation": "Aktywacja sprawy", "average": "Prosjek", "performanceLogs": "Dzienniki wydajności", "displayingOverview": "Wyświetlanie przeglądu", "taskSolve": "Zakończenie zadania", "displayingCO": "Wyświetlanie CASE OVERVIEW", "printCreation": "<PERSON>rz<PERSON><PERSON> wydruku", "entityId": "ID jednostki", "copyTask": "Kopiowanie zadania", "checkProcessCompletion": "Sprawdzanie zakończenia sprawy", "findingSolver": "Znalezienie właściciela zadania", "publicFiles": "Pliki publiczne", "usage": "Stosowanie", "serviceConsole": "<PERSON><PERSON><PERSON>", "logos": "Loga", "prevWorkDay": "Poprzedni dzień roboczy", "selectAll": "Zaznacz wszystkie", "overviewWithTasks": "Przegląd z zadaniami", "printIsReady": "<PERSON><PERSON><PERSON> jest gotowy", "alrChangelogLoadFailed": "Nie udało się załadować dziennika zmian.", "inJs": "W skry<PERSON>cie", "ttCopyDtDefinition": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON> defini<PERSON><PERSON> tabeli", "body": "<PERSON><PERSON><PERSON><PERSON> definicję wybranej tabeli dynamicznej."}, "confirmCopyTableDefinition": "<PERSON>zy na pewno chcesz skopiować definicję tabeli?", "alrCopying": "Kopiowanie...", "alrCopyFailed": "<PERSON><PERSON> udało się skopio<PERSON>.", "fallback": "Fallback", "syncEnabled": "Synchronizacja", "systemGuideNote": "Treści Przewodnika systemowego nie można zmienić. <PERSON><PERSON> wyświ<PERSON><PERSON>ć inną zawartość, wyłącz Przewodnik po systemie i skopiuj jego zawartość do nowego Przewodnika.", "alrAnotherUserLogged": "Inny użytkownik jest zalogowany w innym oknie!", "userLocked": "Użytkownik jest zablokowany", "visInternalUserOnly": "Widoczne tylko dla użytkowników wewnętrznych", "showSelectedOnly": "Pokaż tylko wybrane", "clickToSelect": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> w<PERSON>", "restrictRoleAssignment": "Ogranicz przypisanie roli dla roli", "restrictions": "Ograniczenia", "restrictTableHandling": "Ogranicz obsługę tabel", "toRole": "Do roli", "inCalcToHeader": "W obliczeniach do nagłówka", "loginBtnColor": "<PERSON><PERSON> przycisku logowan<PERSON>", "certificates": "Certyfikaty", "certificate": "Certy<PERSON><PERSON>", "certificateVar": "certy<PERSON><PERSON>", "tspSources": "Znaczniki czasu", "tspSource": "Znak <PERSON>", "confirmExpandDynRowsNewAssignments": "Uwaga, nowe przypisanie! Zmienne nie mają ustawionych osi. <PERSON><PERSON> chcesz rozciągnąć wszystkie dynamiczne rzędy?", "confirmExpandDynRows": "Czy na pewno chcesz rozciągnąć wszystkie dynamiczne rzędy?", "expandDynRows": "Rozciągnij dynamiczne rzędy", "visible": "W<PERSON>czne", "cvcDbColumn": "<PERSON><PERSON><PERSON>", "cvTableSource": "Tabela źródłowa", "uploadedFromFile": "Przesłane z pliku", "appStatus": "Status aplikacji", "loadAll": "Załaduj wszystkie", "ignoredUsers": "Ignorowani użytkownicy", "copyRolesFrom": "<PERSON><PERSON><PERSON><PERSON><PERSON> role od", "disableFrontendStyles": "<PERSON>e stosuj automatycznych stylów", "activate": "Aktywuj", "confirmActivateCase": "<PERSON><PERSON> chcesz aktywować obudowę?", "alrLackOfPerms": "<PERSON><PERSON> masz <PERSON>.", "alrSending": "Wysyłanie...", "sequences": "Se<PERSON>wen<PERSON><PERSON>", "seqName": "Sekwen<PERSON><PERSON>", "seqId": "<PERSON> sekwen<PERSON>ji", "seqLastRead": "Ostatnio używane", "ttCopyRole": {"heading": "Sko<PERSON><PERSON><PERSON>ol<PERSON>", "body": "Ko<PERSON>uje wybraną rolę."}, "fromCase": "Z przypadku", "includingData": "W tym dane", "choose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "valueChange": "<PERSON><PERSON><PERSON>", "updateInstances": "Uwzględnij zmianę zmiennej w zmiennych instancji", "addNewCalcScript": "Do<PERSON>j nowy skrypt", "useCompatibleCalcScript": "Użyj zgodnego skryptu", "externalSource": "Źródło użytkownika", "reports": "<PERSON><PERSON><PERSON>", "confirmCopyReport": "<PERSON>zy na pewno chcesz skopiować wybrany raport?", "graphs": "Wykresy", "aggregation": "Zbiór", "graphNew": "W<PERSON><PERSON> – nowy", "confirmCopyGraph": "<PERSON>zy na pewno chcesz skopiować wybrany wykres?", "alrCopyGraphFailed": "<PERSON>e udało się skopiować wykresu.", "label": "Etykieta", "pie": "Wykres kołowy", "line": "Wykres liniowy", "dot": "<PERSON><PERSON><PERSON> punk<PERSON>wy", "bar": "Wykres słupkowy", "barGroups": "Wykres słupkowy – grupy", "alrFailedGraphData": "Nie udało się załadować wykresu.", "graphSetSharing": "Ustaw udostępnianie wykresów dla każdej grupy użytkowników", "alrGraphPointsLoadFailed": "Ładowanie punktów wykresu nie powiodło się.", "alrGraphNotFound": "Nie znaleziono wykresu.", "graphData": "<PERSON>", "pointsData": "Punkty na wykresie", "alrGraphSaveFailed": "Zapisywanie wykresu nie powiodło się!", "graphPoint": "<PERSON><PERSON> w<PERSON>u", "noOrder": "Bez sortowania", "refreshGraph": "Odś<PERSON><PERSON>ż wykres", "viewSwitcher": "Filtr globalny", "axisXglobalFilter": "Oś X – filtr globalny", "axisXgroups": "Oś X – grupy", "axisXdata": "<PERSON>ś X – dane", "axisYvalues": "Oś Y <PERSON> wartości", "axisYcolors": "Oś Y – zabarwienie", "hrAgenda": "Agenda HR", "userChange": "Zmiana użytkownika", "newUser": "Nowy użytkownik", "usersCount": "Liczba użytkowników", "confirmChangeUser": "Czy na pewno chcesz zmienić użytkownika?", "businessVariable": "Zmienna biznesowa", "casesCount": "Liczba spraw", "selected": "Wybrany", "selectedOnly": "<PERSON><PERSON><PERSON> w<PERSON>ne", "addCaseRightNewUser": "Dodaj dostęp do sprawy", "visFromTaskToPull": "Widoczność ze zadania do przejęcia", "toChangeConfigInfo": "<PERSON><PERSON>, usuń wartość z pliku local.js", "clickToChange": "Klik<PERSON>j, aby zmienić", "currentValue": "<PERSON><PERSON><PERSON><PERSON>", "sign": "Znak", "validationProtocols": "W<PERSON>da<PERSON>je", "plannedEvents": "Wyd<PERSON>zen<PERSON>", "elArchiv": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "deletedDocs": "<PERSON><PERSON><PERSON><PERSON>", "signatures": "Podpisy", "ttArchive": {"heading": "<PERSON><PERSON><PERSON><PERSON>ek<PERSON>", "body": ""}, "ttAddToZea": {"heading": "Dodaj do archiwum elektronicznego", "body": ""}, "ttRemoveFromZea": {"heading": "Usuń z archiwum elektronicznego", "body": ""}, "ttZeaInfo": {"heading": "W<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttSignZea": {"heading": "Znak zewnętrzny", "body": ""}, "addToZea": "Dodaj do e-archiwum", "removeFromZea": "Usuń z e-archiwum", "reTimestampAfter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wygenerowanego stempla (dni)", "alrLoadFailed": "Ładowanie nie powiodło się.", "replace": "<PERSON><PERSON><PERSON><PERSON>", "expireAt": "Wygaśnie", "result": "<PERSON><PERSON><PERSON>", "validatedAt": "<PERSON> wa<PERSON>", "refType": "Obiekt", "eventType": "Rodzaj działania", "errorMessage": "Komunikat o błędzie", "errorTimestamp": "Data błędu", "errorCount": "Liczba błędów", "inFuture": "Planowane", "path": "Ścieżka podpisu", "signedAt": "Data utworzenia podpisu", "dropZoneZeaCertificate": "Up<PERSON>ść certyfikat tutaj lub kliknij aby wybrac plik do uploadu.", "authType": "Typ u<PERSON>zy<PERSON>nienia", "basic": "Nazwa użytkownika i hasło", "byCert": "Z certyfikatem", "alrMissingCertFile": "<PERSON><PERSON><PERSON> pod<PERSON> certyfikat.", "replaceTo": "Zamień na", "autoReTimestamp": "Automatyczny znacznik czasu", "validate": "W<PERSON><PERSON><PERSON><PERSON>", "lastUse": "Ostat<PERSON> w<PERSON>", "createdAt": "Utworzono", "updatedAt": "Zak<PERSON>ali<PERSON>wan<PERSON>", "certificateId": "<PERSON> certyfikatu", "expectedCreationTime": "Zost<PERSON><PERSON>", "nextTSPSourceId": "ID następującego znaczka", "reTimestampAt": "Następny znacznik czasu", "timestampedAt": "Ostatni znacznik czasu", "level": "Poziom", "signatureB": "Podstawowy podpis", "signatureT": "Podpis ze znacznikiem czasu", "signatureLt": "Podpis z długoterminowymi certyfikatami danych", "signatureLta": "Podpis z długoterminowymi certyfikatami danych i datownikiem archiwalnym", "packaging": "Pak<PERSON>nie", "enveloped": "<PERSON><PERSON><PERSON><PERSON>", "enveloping": "<PERSON><PERSON><PERSON>", "detached": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "algorithm": "Algorytm", "uploadAsRevision": "Prześ<PERSON><PERSON> jako wersję", "externalDisable": "Aktywne tylko dla podpisów zbiorczych", "addToDms": "Dodaj do DMS", "autoConvert": "Automatycznie konwertuj", "format": "Format", "signatureType": "<PERSON><PERSON><PERSON>", "signature": "Podpis", "custom": "Własny", "batchSignDisabled": "Bez podpisu", "tasId": "ID dokumentu", "hashValue": "<PERSON><PERSON><PERSON><PERSON> hash", "hashType": "<PERSON><PERSON> <PERSON><PERSON>ji hash", "confirmAddToArchive": "<PERSON>zy na pewno chcesz dodać do archiwum?", "independentSignature": "Niezależny podpis", "independentValidation": "Niezależna walidac<PERSON>", "failureTrue": "Z błędem", "failureFalse": "Bez błędów", "confirmValidateDialog": "<PERSON>zy na pewno chcesz potwierdzić podpis?", "confirmRestartDialog": "<PERSON>zy na pewno chcesz zresetować błędy?", "verificationResult": "W<PERSON>ik weryfi<PERSON>ji", "integrityMaintained": "<PERSON><PERSON><PERSON>", "signatureFormat": "Format podpisu", "internalTimestampsList": "Lista wewnętrznych znaczników czasu", "signers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exhibitedBy": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ez", "signedBy": "Podpisano przez", "validFrom": "Obowiązuje od", "validUntil": "Ważne do", "signitureType": "<PERSON><PERSON><PERSON>", "signatureQualification": "Kwalifika<PERSON>ja podpisu", "signatureNoTimestamps": "Podpis nie zawiera znaczników czasu", "electronicSignature": "Podpis elektroniczny", "electronicSeal": "<PERSON><PERSON><PERSON><PERSON> elektroniczna", "webSiteAuthentication": "Uwierzytelnianie strony internetowej", "QCP-n": "QCP-n: polityka certyfikatów dla certyfikatów kwalifikowanych UE wydawanych osobom fizycznym", "QCP-l": "QCP-l: polityka certyfikatów dla certyfikatów kwalifikowanych UE wydawanych osobom prawnym", "QCP-n-qscd": "QCP-n-qscd: polityka certyfikatów dla certyfikatów kwalifikowanych UE wydawanych osobom fizycznym z kluczem prywatnym związanym z certyfikowanym kluczem publicznym na QSCD", "QCP-l-qscd": "QCP-l-qscd: polityka certyfikatów dla kwalifikowanych certyfikatów UE wydawanych osobom prawnym z kluczem prywatnym związanym z certyfikowanym kluczem publicznym na QSCD", "QCP-w": "QCP-w: polityka certyfikatów dla certyfikatów uwierzytelniania witryn UE kwalifikowanych", "formOfReStamping": "Forma ponownego <PERSON>", "individually": "Indywidualnie", "archiveAsPdf": "Archiwizuj jako PDF", "couldNotBeVerified": "<PERSON><PERSON> można zweryfikować", "uses": "Liczba zastosowań", "countOfSignedDocuments": "Liczba podpisanych dokumentów", "batchSignature": "<PERSON><PERSON><PERSON> ma<PERSON>", "standaloneSign": "Indywidualny podpis", "validateSignature": "Weryfikacja podpisu", "validateDoc": "Sprawdzanie poprawności dokumentu", "containsSignature": "Zawiera podpis", "reStamping": "Powtarzająca się pieczątka", "individualSignatures": "Indywidualne podpisy", "signatureLevel": "Poziom podpisu", "simpleReport": "Prosty raport", "detailedReport": "Raport szczegółowy", "diagnosticReport": "Raport diagnostyczny", "etsiReport": "Raport ETSI", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "Failed", "INDETERMINATE": "Indeterminate", "FORMAT_FAILURE": "Signature does not comply with one of the basic standards", "HASH_FAILURE": "The signed data object's hash does not match the hash in the signature", "SIG_CRYPTO_FAILURE": "The signature could not be verified with the signer's public key", "REVOKED": "The signature certificate has been revoked and there is evidence that the signature has been created after the revocation", "SIG_CONSTRAINTS_FAILURE": "One or more signature attributes do not match the validation rules", "CHAIN_CONSTRAINTS_FAILURE": "The certificate chain used in the validation process does not comply with the certificate validation rules", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "The set of certificates available for string verification caused an error for an unspecified reason", "CRYPTO_CONSTRAINTS_FAILURE": "One of the signature verification algorithms is below the required cryptographic security level and the signature was acquired after the algorithm lifetime", "EXPIRED": "The signature was created after the signature certificate expired", "NOT_YET_VALID": "The signing time lies before the issuance date of the signing certificate", "POLICY_PROCESSING_ERROR": "The validation policy file could not be processed", "SIGNATURE_POLICY_NOT_AVAILABLE": "The electronic document containing details about the validation policy is not available", "TIMESTAMP_ORDER_FAILURE": "Restrictions in signature timestamp order are not respected", "NO_SIGNING_CERTIFICATE_FOUND": "The signing certificate cannot be identifie", "NO_CERTIFICATE_CHAIN_FOUND": "No certificate chain was found for the identified signature certificate", "REVOKED_NO_POE": "The signing certificate was revoked at the validation date/time. However, the signature verification algorithm cannot detect that the signature time is before or after the revocation period", "REVOKED_CA_NO_POE": "At least one certificate chain was found, but a temporary CA certificate was revoked", "OUT_OF_BOUNDS_NOT_REVOKED": "The signing certificate is expired or not yet valid at the validation date/time and the Signature Validation Algorithm cannot ascertain that the signing time lies within the validity interval of the signing certificate. The certificate is known not to be revoked.", "OUT_OF_BOUNDS_NO_POE": "The signing certificate has expired or is not yet valid at the verification date/time", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "One of the signature verification algorithms is below the required cryptographic security level and there is no proof that it was produced before the algorithm/key was considered secure", "NO_POE": "There is no proof that the signed object was created before a compromising event", "TRY_LATER": "Not all validation rules can be met with the available information, but it may be possible to do so with additional revocation information that will be available later", "SIGNED_DATA_NOT_FOUND": "Signed data cannot be obtained", "GENERIC": "Other Reason", "signatureFile": "Plik podpisu", "validityDays": "Dni waż<PERSON>ści", "qualifiedHe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "qualifiedIt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unqualifiedHe": "Niewykwalifikowany", "unqualifiedIt": "Niewykwalifikowany", "timeValid": "Ważny czas", "reason": "<PERSON><PERSON><PERSON><PERSON>", "inTime": "z czasem", "certificateQualification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "guaranteedHe": "Gwarantowana", "guaranteedIt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromQualifiedCert": "Z <PERSON>walifikowanego cert.", "basedOnQualifiedCertHe": "Na podstawie kwalifikowanego certyfikatu", "createdByQualifiedResHe": "Utworzony przez wykwalifikowany zasób", "basedOnQualifiedCertIt": "Na podstawie kwalifikowanego certyfikatu", "createdByQualifiedResIt": "Utworzony przez wykwalifikowany zasób", "qualification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmRemoveFromZeaDialog": "<PERSON><PERSON> ch<PERSON>z <PERSON> {{variable}} z archiwum elektronicznego?", "noValidationReports": "Brak wyników sprawdzania poprawności", "noSignatures": "Brak indywidualnych podpisów", "isHigherOrEqualThan": "<PERSON><PERSON> <PERSON> wię<PERSON><PERSON> lub równy", "isInZea": "W e-archivum", "startStamping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reTimestampAfterMinutes": "minuty", "reTimestampAfterDays": "dni", "reTimestampAfterAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wygenerowanego stempla", "refId": "ID obiektu", "docWithoutAutoTimestampInfo": "Dokument zostanie podpisany jeden raz, bez automatycznego wstawiania znacznika czasu.", "validationReports": "<PERSON> walid<PERSON>ji", "docPath": "Ścieżka dokumentu", "addToArchiveInvalidSignatureError": "Nie można zarchiwizować pliku, poni<PERSON><PERSON><PERSON>aw<PERSON> pod<PERSON>, którego nie można zweryfikować.", "signImmediately": "Podpisz natychmiast", "replaceInConfiguration": "Wymień w konfiguracji", "cancel": "<PERSON><PERSON><PERSON>", "bulk": "<PERSON><PERSON><PERSON>", "bulkCompletion": "Zakończenie zbiorcze", "enableBulkCompletion": "Włącz uzupełnianie zbiorcze", "confirmCompleteTasks": "<PERSON><PERSON> nap<PERSON> ch<PERSON>z wykonać zadania?", "plannedMaintenance": "Zaplanowana konserwacja", "notSpecified": "<PERSON>eok<PERSON><PERSON><PERSON><PERSON>", "bulkCompletionVars": "Zmienne zbiorczego uzupełniania", "alrBulkCompletionMultiTypeErr": "Zbiorczo można wykonywać tylko zadania tego samego typu, możesz użyć filtra.", "notifications": "Powiadomienia", "alrTskAlreadyTakenSomeone": "Ktoś inny już podjął się tego zadania.", "alrTskAlreadyTaken": "Zadanie zostało już przejęte.", "downloadBpmn": "pobierz BPMN diagram", "downloadSvg": "pobierz jako SVG obraz", "displayForm": "Typ wyświetlacza", "selectedPreview": "zostanie wyświetlony podgląd", "fixedHeight": "St<PERSON><PERSON> wys<PERSON>ć (w pikselach)", "lastVersion": "Najn<PERSON><PERSON> wersja", "separatedPreview": "Oddzielny podgląd", "defaultZoom": "Powiększenie domyślne", "fixedPosition": "Naprawiono pozycję", "percentInterval": "Wpisz liczbę całkowitą od 0 do 5", "notPositiveNumber": "<PERSON><PERSON><PERSON> l<PERSON> dodatnie", "zoomDown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rotate": "<PERSON><PERSON><PERSON><PERSON> się", "logTooBig": "Dziennik jest zbyt du<PERSON>, aby go <PERSON>.", "downloadLog": "Dziennik pobierania", "confirmCopyCron": "<PERSON>zy na pewno chcesz skopiować wybranego crona?", "ttCopyCron": {"heading": "Skopiuj crona", "body": ""}, "onlyWorkingDays": "<PERSON><PERSON><PERSON> dni robocze", "datesDisabled": "Wyłącz daty", "useView": "Użyj View", "dateWithoutTime": "Data bez godziny", "timezone": "<PERSON><PERSON><PERSON>", "roleRestriction": "Ograniczenie roli", "headerRestriction": "Ograniczenie nagłówka", "ttSwitchDarkmode": {"heading": "Przełączanie trybu jasny / ciemny", "body": ""}, "advancedEditor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "externalId": "ID zewnętrzny", "passwordValidationMin": "<PERSON><PERSON>o jest za krótkie. (<PERSON><PERSON> dług<PERSON>: {{count}})", "passwordValidationMax": "<PERSON><PERSON><PERSON> jest za długie. (ma<PERSON><PERSON><PERSON><PERSON> długoś<PERSON>: {{count}})", "passwordValidationUppercase": "Hasło musi zawierać wielką literę. {{atLeast}}", "passwordValidationLowercase": "Hasło musi zawierać małą literę. {{atLeast}}", "passwordValidationSymbols": "<PERSON><PERSON><PERSON> musi z<PERSON> symbol. {{atLeast}}", "passwordValidationDigits": "<PERSON>ł<PERSON> musi z<PERSON> cyfrę. {{atLeast}}", "passwordValidationLetters": "<PERSON>ło musi zawierać literę. {{atLeast}}", "atLeast": "Przynajmniej", "passwordValidationServiceErr": "W tej chwili nie można zmie<PERSON> hasła.", "enableTasksHandoverRole": "Zawsze zezwalaj na przekazywanie zadań i wyzwalanie zdarzeń użytkownikom tej roli", "shredded": "Rozdrobnione", "shredableVar": "Zmienna do niszczenia", "shredDocuments": "Niszcz dokumenty", "shredInDays": "Niszcz w (dni)", "fromBeginningOrendOfCase": "Od p<PERSON>/końca sprawy", "shredding": "Rozdrabnianie", "addColumn": "<PERSON><PERSON><PERSON>", "unsupportedBrowser": "Otwierasz TeamAssistant w nieobsługiwanej przeglądarce Internet Explorer, niektóre funkcje mogą być niedostępne.", "ingoreProcessRights": "Ignorowanie praw do spraw", "cvHelpIngoreProcessRights": "Przegląd zawsze pokazuje wszystkie sprawy, niezależnie od praw", "upperLabel": "Umieść zmienną pod jej nazwą", "darkMode": "<PERSON><PERSON> c<PERSON>ny", "completedTasks": "Ukończone zadania", "permissions": "Uprawnienia", "caseVisibility": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sprawy", "visPerOrg": "Widoczność na jednostkę organizacyjną", "entity": "Jednostka", "staticRight": "Prawo statyczne", "dynamicRight": "Prawo dynamiczne", "treeNodesAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "treeNodesMy": "<PERSON><PERSON><PERSON>", "activeQueries": "Aktywne zapytania", "query": "Zapytanie", "confirmCancelQuery": "<PERSON>zy na pewno chcesz anulować zapytanie?", "alrQueryNotFound": "Zapytanie nie zostało już znalezione.", "completeAgenda": "Kompletna agenda", "lockedBusinessUsers": "Zablokowani użytkownicy biznesowi", "structuredList": "Lista uporządkowana", "ttCompetences": {"heading": "Zarządzanie kompetencjami", "body": ""}, "competences": "Ko<PERSON>etenc<PERSON>", "competence": "Kompetencja", "competenceDelVar": "kompetencje", "addCompetence": "<PERSON><PERSON><PERSON> kom<PERSON>", "regularExpression": "Wyrażenie regularne", "generationStatus": "<PERSON>", "source": "Źródło", "historical": "Historyczne", "external": "Zewnętrzny", "nextDay": "następny dzień", "embeddedVideoNotSupported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Twoja przeglądarka nie obsługuje osadzonych filmów.", "alrSendingTestMailFailed": "Testowy e-mail nie powiódł się.", "sent": "Wysłano.", "mainColorEmail": "Główny kolor e-maila", "confirmResetColors": "<PERSON>zy na pewno chcesz zresetować kolory?", "regularExpressions": "Wyrażenia regularne", "confirmDeleteLogo": "<PERSON>zy na pewno chcesz usunąć logo?", "loginLogoLightTheme": "Logo ekranu <PERSON> (tryb jasny)", "loginLogoDarkTheme": "Logo ekranu <PERSON> (tryb ciemny)", "competenceRegexHelper": "<ul><li>% może być użyty jako N dowolnych znaków</b> (r<PERSON><PERSON><PERSON><PERSON><PERSON> *)</li><li><b>_</b> może być używany jako jeden dowolny znak (równowartość .)</li><li><PERSON><PERSON><PERSON><PERSON>ż<PERSON> <b>^</b>, aby uciec od tych znaków specjalnych (równowartość \\)</li></ul>", "headerFont": "Czcionka nagłówka", "evenRow": "<PERSON><PERSON>", "logo": "Logo", "widthForLogo": "Szerokość dla logo", "monthStart": "Początek miesiąca", "monthEnd": "Koniec miesiąca", "ttFavouriteType": "GET otwiera łącze. POST wysyła polecenie: na przykład podczas tworzenia sprawy, gdy w treści żądania wysyłany jest ID nagłówka szablonu (zapisz do ulubionych za pomocą opcji Nowa sprawa).", "confirmEmptyMultiinstanceVariable": "<PERSON><PERSON>, że ta wieloinstancyjność nie wymaga zmiennej do iteracji?", "ttMenuPreview": "Konfiguracja menu według ról użytkowników (bard<PERSON><PERSON> znaczą<PERSON> role widzą również przyciski dla mniej znaczących ról). Przyciski Nowa sprawa i Pulpit są niezmienne.", "menuPreview": "Podgląd menu dla wybranej roli", "confirmResetMenu": "<PERSON>zy na pewno chcesz zresetować menu?", "alrFailedTasMenu": "Nie udało się załadować konfiguracji menu Tas!", "security": "Bezpieczeństwo", "userRestrictions": "Ograniczenia użytkowników (pokazać)", "userRestrictionsProcesses": "Ignoruj ​​ograniczenia użytkownika dotyczące zadań", "roleRestrictions": "Ograniczenia ról (pokazać)", "orgUnitRestrictions": "Ograniczenia jednostek org. (pokazać)", "everyone": "Wsz<PERSON><PERSON>", "colleaguesOnly": "<PERSON><PERSON><PERSON> k<PERSON>", "directSubordinates": "Bezpośredni podwładni", "allSubordinates": "Wszyscy podwładni", "none": "Brak", "generalDocument": "Dokument ogólny", "competenceRule": "Reguła kompetencji", "competenceRules": "Zasady kompetencji", "ruleName": "Nazwa reguły", "ttUseCompetenceRule": {"heading": "Użyj reguły", "body": "Tworzy kompetencję zgodnie z wybraną regułą"}, "competenceText": "Tekst kompetencji", "competenceName": "Nazwa kompetencji", "competenceReadOnlyInfo": "Kompetencja utworzona z reguły nie może być modyfi<PERSON>", "xmlProcessImport": "Import procesu XML", "ttWidthForLogo": "Ustaw szerokość dla logo, a następnie wstaw logo. Nie ma możliwości zmiany szerokości już wstawionego lub domyślnego logo.", "openCase": "Otw<PERSON>rz sprawę", "importHistory": "Historia importu", "plannedImports": "Planowane importy", "filePath": "Ścieżka do pliku", "cronId": "ID crona", "taskResult": "Wynik zadania", "xmlFileSize": "Rozmiar pliku XML", "attachmentSize": "Rozmiar załącznika", "lastEdit": "Ostatnia edycja", "timeCreated": "Czas utworzenia", "importId": "ID importu", "importAudit": "Audyt importu", "finishedImports": "Zakończone importy", "insertNote": "Wstaw notatkę", "importXml": "Importuj XML", "reImportXml": "Reimportuj XML", "downloadXml": "Pobierz XML", "downloadAttachment": "Pobierz <PERSON>", "skipXml": "Pomiń XML", "note": "Notatka", "attachmentName": "Nazwa załącznika", "importedCount": "Liczba importu", "retryCount": "Liczba powtórzeń", "batchId": "<PERSON> dawki", "copyPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cronRunId": "ID przebiegu", "cronRun": "Bieg crona", "trace_id": "Trace ID", "ttMenuItemLabel": "Na<PERSON>wa uniwersalna, je<PERSON>li nie ma tłumaczenia. <PERSON><PERSON><PERSON> używane jest słowo kluczowe tłumaczenia, jest ono tłumaczone automatycznie. Domyślne nazwy: tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "<PERSON><PERSON><PERSON><PERSON>", "dissolveQueue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kole<PERSON>", "taskQueueInitInfo": "W wyniku tej akcji powstało wiele zadań do rozwiązania. Tutaj możesz zmienić kolejność ich rozwiązywania lub całkowicie usunąć je z kolejki.", "tryDarkTheme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>e wolisz tryb ciemny. Klik<PERSON>j, aby w<PERSON><PERSON><PERSON><PERSON><PERSON> w TAS.", "alrInvalidURL": "Nieprawidłowy format URL.", "alrInvalidHttps": "Nieprawidłowy format adresu URL, musi zaczynać się od https://", "importVariables": "Importuj zmienne", "ttVariablesImport": {"heading": "Importuj zmienne", "body": "Wybrany zostanie folder z definicjami zmienne, a następnie uploadowany."}, "classDiagram": "Diagram klas", "createVar": "Utwórz z<PERSON>nną", "importObjectStates": "Importuj stany obiektów", "unassigned": "Nieprzypisany", "sortVars": "<PERSON><PERSON><PERSON><PERSON>", "fillNames": "Wypełnij nazwy", "ttFillNames": {"heading": "Wypełnij nazwy", "body": "Wypełnia puste nazwy wszystkich nowych zmiennych w formacie „Klasa.Atrybut“ i sortuje wszystkie zmienne."}, "ttSortVars": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Sortuje zmienne według klas i atrybutów."}, "ttRestore": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Przywraca zmienne do ich pierwotnego stanu po zaimportowaniu z pliku."}, "ttAddVarToBottom": {"heading": "<PERSON><PERSON><PERSON>", "body": "Dodaje nową zmienną na dole strony."}, "confirmRestoreForm": "Czy na pewno chcesz przywrócić zmienne do ich pierwotnego stanu?", "selectClass": "Zaznacz klasę", "importClassDiagram": "Import diagramu klas", "continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "templateVars": "Zmienne szablonu", "newVars": "Nowe zmienne", "objectState": "<PERSON> obiektu", "alrDynTableExists": "Tabela dynamiczna już istnieje!", "overwriteExistDynTable": "Zastąp istniejącą tabelę dyn.", "confirmCancelImport": "Czy na pewno chcesz anulować import?", "alrDuplicateNames": "<PERSON> z<PERSON> zduplikowane nazwy.", "stateVar": "<PERSON><PERSON><PERSON> stanu", "importObjectStatesToDynTables": "Importuj stany obiektów do tabel dynamicznych.", "defineObjectStatesVars": "Zdefiniuj zmienne, które utrzymują stany obiektów.", "change": "<PERSON><PERSON><PERSON><PERSON>", "classAndAttr": "Klasa i atrybut", "clearQueue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kolejk<PERSON>", "sharing": "Dzielenie się", "data": "<PERSON>", "open": "Otwórz", "dataSource": "Źródło danych", "dataPoints": "<PERSON><PERSON> danych", "dataSeries": "<PERSON><PERSON>", "valueCol": "<PERSON><PERSON><PERSON>", "aggregationCol": "<PERSON><PERSON><PERSON>", "timeDimension": "Czas. wymiar", "columns": "<PERSON><PERSON><PERSON>", "week": "tydzień", "weekday": "dzień tygodnia", "monthVar": "<PERSON><PERSON><PERSON><PERSON>", "overviewFilter": "Filtr przeglądu", "globalFilters": "Filtry globalne", "filterDefinition": "Defin<PERSON><PERSON> filtra", "newFilter": "Nowy filtr", "addFilter": "<PERSON><PERSON><PERSON> filtr", "filterOptions": "<PERSON><PERSON><PERSON> fi<PERSON>", "addOption": "<PERSON><PERSON><PERSON>", "graphPreview": "Podgląd wykresu", "alrGlobalFilterDownloadFailed": "Nie udało się pobrać globalnych filtrów!", "alrGlobalFilterSaveFailed": "Nie udało się zapisać globalnych filtrów!", "filterOption": "<PERSON><PERSON><PERSON> filtra", "editFilter": "<PERSON><PERSON><PERSON><PERSON> filt<PERSON>", "fillOptionsFromVar": "Wypełnij opcje ze zmiennej", "fillOptionsDynamically": "Wypełnij opcje <PERSON>znie", "filterOptionsFilledDynamically": "Dynamicznie ze zmiennej", "dayOfMonth": "<PERSON><PERSON><PERSON> mi<PERSON>", "dateVar": "data", "group": "Grupa", "ttDataSource": "<PERSON><PERSON><PERSON><PERSON> „Punkty danych“, jeśli chcesz wprowadzić każdy punkt na wykresie osobno. <PERSON><PERSON><PERSON> chcesz, aby punkty były generowane na podstawie wybranego wymiaru, wy<PERSON><PERSON> „Seria danych“", "ttDataSeriesAggregation": "Wybierz typ agregacji. Pozwala tworzyć informacje podsumowujące z rekordów (przypadków).", "ttDataSeriesColumns": "<PERSON><PERSON><PERSON><PERSON> kolejno wszystkie kolumny, <PERSON><PERSON><PERSON> których chcesz tworzyć grupy (agregacje) w celu obliczenia wartości podsumowania.", "listOfFiltersIsEmpty": "Lista filtrów jest pusta.", "fromVariable": "Ze zmiennej", "showOptionsFromCount": "<PERSON><PERSON><PERSON> opcje (z {{count}})", "sum": "<PERSON><PERSON>", "minimum": "Minimum", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "statistics": "Statystyki", "unfilled": "Niewypełnione", "globalFilterDescription": "Filtr globalny zapewnia użytkownikom wykresu opcje filtrowania danych wejściowych dla wykresu. Wszystkie opcje filtra można zdefiniować na tym ekranie.", "ttDelGraph": {"heading": "<PERSON><PERSON><PERSON> wykres", "body": "Usuwa wybrany wykres."}, "ttEditGraph": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Pozwala na edycję wybranego wykresu."}, "ttCopyGraph": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Kopiuje wybrany wykres."}, "ttAddGraph": {"heading": "<PERSON><PERSON><PERSON>", "body": "Pozwala na zdefiniowanie nowego wykresu."}, "axisXName": "Nazwa osi X", "axisYName": "Nazwa osi Y", "showValues": "<PERSON><PERSON><PERSON>", "defaultOption": "Domyślna opcja", "yearStart": "Początek roku", "yearEnd": "Koniec roku", "thisMonth": "Bieżą<PERSON>", "lastMonth": "Ostatni <PERSON>", "thisYear": "W tym roku", "lastYear": "Ostatni rok", "scheduledTasks": "Zaplanowane zadania", "scheduled": "Zaplanowane", "dueDateStart": "Data rozpoczęcia", "lastRescheduled": "Ostatnia zmiana terminu", "reschedule": "Zmień termin", "alrTasksRescheduling": "Zmiana harmonogramu zadań...", "alrTasksRescheduled": "Zadania zostały przełożone.", "alrTasksRescheduleFailed": "<PERSON>e udało się zmienić harmonogramu zadań.", "onlyCurrentOrFutureDates": "<PERSON><PERSON><PERSON> dzisiejsze lub przyszłe daty.", "passwordValidations": "Zasady tworzenia haseł", "readonlyConfigInfo": "Wartość jest tylko do odczytu", "alrTasksCountFailed": "Liczenie zadań nie powiodło się.", "confirmActivateTasks": "<PERSON>zy na pewno chcesz aktywować wybrane zadania?", "confirmSuspendTasks": "<PERSON>zy na pewno chcesz zawiesić wybrane zadania?", "tskOffset": "Planowanie zmienne", "workWeek": "Tydzień pracy", "agenda": "<PERSON><PERSON><PERSON><PERSON> obrad", "noEventsInRange": "W tym zakresie nie ma wydarzeń", "activitiesDesc": "Opis działań", "allShort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numberOfEvents": "Liczba wydarzeń", "weekNumber": "numer tygodnia", "cannotBeEdited": "<PERSON><PERSON> m<PERSON>", "cannotBeMoved": "<PERSON>e można p<PERSON>eni<PERSON>", "alrTempVarSaveSameNameFailed": "Zmienna o tej domyślnej nazwie już istnieje, proszę wpisać inną nazwę.", "maxUsersCountRole": "Maksymalna liczba użytkowników w tej roli", "unlimitedAssignLeaveBlankInfo": "W przypadku nieograniczonych przydziałów pozostaw to pole puste.", "cvOwner": "Właściciel przeglądu", "changePassword": "<PERSON><PERSON><PERSON> hasło", "passwordExpired": "Twoje hasło wygasło. Wprowadź nowe hasło.", "passwordWillExpire": "Twoje hasło wkrótce wygaśnie. Wprowadź nowe hasło.", "userParameters": "Parametry użytkownika", "filterSortingHelper": "Sortowanie według jednej lub więcej kolumn w filtrze wyłącza możliwość ręcznego sortowania kolumn bezpośrednio w tabeli.", "importUsers": "Importuj użytkowników", "importRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON> role", "existingEntityRows": "Wiersze z już istniejącymi elementami (można nadpisać)", "fileRow": "Wiersz pliku", "existingEntityRowsMultiple": "<PERSON><PERSON><PERSON> z elementami, które istnieją już więcej niż jeden raz (nie zostaną zaimportowane)", "importOrgUnits": "Importuj jednostki organizacyjne", "structureImportExport": "Import/export struktury", "fillAttributes": "Wypełnij atrybuty", "structurePreview": "Podgląd struktury", "invalidRowsForImport": "Nieprawidłowe wiersze (brak danych obowiązkowych)", "duplicateRowsForImport": "Wiersze ze zduplikowanymi pasującymi danymi (nie zostaną zaimportowane)", "newEntityRows": "Wiersze z nowymi jednostkami do zaimportowania", "existingNameRowsForImport": "<PERSON><PERSON><PERSON> z <PERSON>zwami, które już istnieją w innych jednostkach (nie zostaną zaimportowane)", "overwriteExisting": "Nadpisz istniejący", "structurePreviewHelper": "Podgląd struktury pokazuje dwie różne sytuacje: importowanie tylko nowych organizacji lub importowanie zarówno nowych, jak i istniejących organizacji, które zostaną nadpisane. Wszystkie zmiany w porównaniu z obecną strukturą są zaznaczone na czerwono.", "validateAndShowPreview": "Zatwierdź i wyświetl podgląd", "uploadNewFile": "Prześlij nowy plik", "userStatus": "Status użytkownika", "importedFile": "Importowany plik", "pairUsersBy": "Paruj użytkowników według", "assignOrgBy": "Przypisz do organizacji według", "pairRolesBy": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "pairUnitsBy": "Sparuj jednostki według", "unitHierarchyCol": "Kolumna hierarchii j<PERSON>", "dontAssign": "<PERSON><PERSON>", "infoImportDataValidated": "OSTRZEŻENIE: Dane <PERSON> właśnie zweryfikowane ze względu na zmiany w ustawieniach. Zalecamy cofnięcie się i sprawdzenie nowego podglądu importu.", "assignUserRolesMethod": "Jak przypisać role do użytkowników", "assignUserRolesMethodHelp": "<PERSON><PERSON> role: dodaj do już przypis<PERSON>ch ról lub całkowicie zastąp obecnie przypisane role nowo przypisanymi rolami.", "existingRolesForImport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> role (mog<PERSON> <PERSON><PERSON> nadpisane)", "existingRoleNamesForImport": "<PERSON> z nazwami, kt<PERSON><PERSON> już istnieją dla innych ról (nie będą importowane)", "newRolesForImport": "Nowe role do zaimportowania", "userRolesForImport": "Wiersze z rolami użytkowników do przypisania", "nonExistentUsersForImport": "Wiersze z nieistniejącymi użytkownikami (role nie zostaną przy<PERSON>ane)", "multipleExistingUsersForImport": "Wiersze z więcej niż jednym istniejącym użytkownikiem (role nie zostaną przy<PERSON>)", "invalidOrgsForImport": "Nieprawidłowe wiersze (brak danych obowiązkowych lub niewłaściwa hierarchia)", "keepOriginal": "<PERSON><PERSON><PERSON>", "assignOrgByHelp": "<PERSON><PERSON><PERSON> wybierzesz kolumnę z pliku, moż<PERSON><PERSON> określić klasyfikację organizacji dla nowych i istniejących użytkowników. Jeśli wybierzesz konkretną organizację, wszyscy importowani lub zaktualizowani użytkownicy zostaną przypisani do tej organizacji.", "creatingRoles": "<PERSON>rz<PERSON>e ról", "assigningRolesToUsers": "Przypisywanie ról użytkownikom", "newUsers": "Nowi użytkownicy", "existingUsers": "Istniejący użytkownicy", "fromFile": "Z pliku", "alrCsvXlsxUploadWrongExtension": "Przesyłaj tylko pliki z rozszerzeniem *.csv lub *.xlsx", "importNewAndExisting": "Importuj nowe podmioty i nadpisz istniejące", "importNewOnly": "Importuj tylko nowe podmioty", "importNewAndExistingRoles": "Importuj nowe role i nadpisz istniejące", "importNewRolesOnly": "<PERSON><PERSON><PERSON><PERSON><PERSON> role", "importRolesHelper": "Ustawienia importowania samych ról. Przypisywanie ról użytkownikom jest regulowane przez ustawienia „Paruj użytkowników według“ i zawsze dotyczy nowych i istniejących ról.", "statisticsColorHelper": "<PERSON><PERSON><PERSON> kolory nie są wybierane ręcznie lub jeśli jest mniej wybranych kolorów niż kolumn, braku<PERSON>ące kolory są generowane automatycznie. Wygenerowane kolory nigdy nie zawierają ciemnych lub zbyt jasnych odcieni, można je wybrać tylko ręcznie.", "caseService": "Obsługa sprawy", "taskService": "Usługa zadania", "editTasks": "<PERSON><PERSON><PERSON>j <PERSON>", "editCases": "<PERSON><PERSON><PERSON><PERSON>", "deleteTasks": "Usuń zadania", "deleteCases": "Usuń sprawy", "serviceOperationsInfo": "Zaznacz i wypełnij zmienne, które chcesz zmienić.", "erased": "<PERSON><PERSON><PERSON><PERSON>", "statusErrored": "Błąd", "serviceOperations": "<PERSON><PERSON><PERSON>", "runCalcsOnStart": "Uruchom obliczenia na starcie", "taskReactivation": "Reaktywacja zadań", "taskCompletion": "Zakończenie zadań", "caseReactivation": "Reaktywa<PERSON><PERSON> spraw", "caseCompletion": "Zakończenie spraw", "openTask": "Otw<PERSON><PERSON>", "changeEntity": "Zmień podmiot", "selectTableColumns": "Wybierz kolumny tabeli", "parentCase": "Sprawa nadrzędna", "ownerOrganization": "Organizacja właściciela", "confirmTaskReactivation": "<PERSON>zy na pewno chcesz reaktywować wybrane zadania?", "confirmCaseReactivation": "<PERSON>zy na pewno chcesz reaktywować wybrane sprawy?", "confirmTaskCompletion": "<PERSON>zy na pewno chcesz ukończyć wybrane zadania?", "confirmCaseCompletion": "<PERSON>zy na pewno chcesz zakończyć wybrane sprawy?", "selectAllFilterMustBeActive": "<PERSON><PERSON> w<PERSON><PERSON> wszystkie elementy, musi być aktywny co najmniej jeden filtr.", "changeEntities": "Zmień jednostki", "disabledDifferentTemplates": "<PERSON>e m<PERSON>, ponieważ elementy nie pochodzą z tego samego szablonu.", "actions": "<PERSON><PERSON><PERSON><PERSON>", "taskTemplateId": "ID szablonu zadania", "caseTemplateId": "ID szablonu sprawy", "actionInfoCheckLogs": "Akcja zostanie wykonana w tle, sprawdź logi.", "alrServiceOperationsColumnsFailed": "Zapisanie ustawień kolumn operacji serwisowych nie powiodło się.", "confirmResetSelectedCols": "Czy na pewno chcesz zresetować zapisane kolumny tabeli?", "instanceVars": "Zmienne instancji", "usrId": "ID użytkownika", "orgId": "ID organizacji", "titlePrefix": "Prefiks tytułu", "titleSuffix": "Sufiks tytułu", "accessRoleId": "ID roli dostępu", "maxAssigns": "Maksymalna liczba przypisań", "client": "Klient", "bigValue": "<PERSON><PERSON><PERSON>", "unitId": "ID jednostki", "roleId": "ID roli", "paramId": "ID parametru", "varId": "ID zmiennej", "parentId": "ID jednostki nadrzędnej", "openUser": "Otwórz użytkownika", "openRole": "Otwarta rola", "openUnit": "Otw<PERSON>rz j<PERSON>ę", "units": "Jednostki", "managerId": "ID menedżera", "externalStatus": "Status zewnętrzny", "additionalId": "Dodatkowy ID", "parentIc": "IC jednostki nadrzędnej", "companyIc": "IC firmy", "textValue": "<PERSON><PERSON><PERSON>ć tekstowa", "dateValue": "<PERSON><PERSON><PERSON><PERSON> daty", "numberValue": "<PERSON><PERSON><PERSON><PERSON> liczbowa", "loginCount": "Liczba logowań", "externalLogin": "Logowanie zewnętrzne", "badLoginCount": "Liczba błędnych logowań", "passwordLastChange": "Ostatnia zmiana hasła", "solverEvaluation": "Ocena właściciela zadania", "solverWillBe": "Właścicielem zadania będzie", "possibleSolvers": "Możliwi właściciele zadania", "selectReferencePerson": "<PERSON><PERSON><PERSON><PERSON> osobę referencyjn<PERSON>", "evaluateSolver": "Oceń właściciela", "referenceUserForEval": "Osoba referencyjna do oceny", "andOthers": "...i inni", "showLess": "...poka<PERSON> mniej", "alrSessionExpired": "<PERSON><PERSON> se<PERSON>ja w<PERSON>, z<PERSON>uj się ponownie.", "mailPromptlyInfo": "Użytkownik ciągle otrzymuje jednorazowe powiadomienia o nowych zadaniach, w których są właściciel. Te alerty zostaną wysyłane tylko wtedy, gdy zadanie nie zostało rozwiązane przez {{minutes}} minut od jego aktywacji.", "mailPullInfo": "Użytkownik nieustannie otrzymuje jednorazowe powiadomienia o nowych zadaniach, które są dostępne do subskrypcji, a użytkownik jest ich możliwym rozwiązaniem. Powiadomienie wychodzi w momencie aktywacji danego zadania w przepływie pracy.", "mailTotalInfo": "Użytkownik okresowo otrzymuje przegląd z zadaniami, które mają zostać wykonane, kt<PERSON><PERSON><PERSON> są właściciel. <PERSON><PERSON><PERSON> zadanie nie ma bezpośredniego rozwiązania, właściciel procesu jest powiadomiony. Je<PERSON>li użytkownik jest reprezentowany, powiadomienie jest odbierane przez jego przedstawiciela.", "mailEscalationInfo": "Użytkownik okresowo otrzymuje przegląd z zadaniami, które zostały wykonane, które przekroczyły termin. Są one powiadomione, jeśli są nadzorcą zadania (a nie jego właściciel w tym samym czasie) lub są bezpośrednim menedżerem użytkownika, który jest właściciel. Jeśli zadanie nie ma rozwiązania, właściciel procesu jest uważany za przełożonego. Jeśli użytkownik jest reprezentowany, powiadomienie wspomina, kto jest obecnym przedstawicielem.", "calcSourceOverwriteWarning": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jest nadpisywane składnią ES6!", "changeStatus": "Zmień status", "confirmChangeEmailStatus": "Czy na pewno chcesz zmienić status wybranych e-maili?", "logInAgain": "Zaloguj się jeszcze raz", "migrations": "<PERSON><PERSON><PERSON><PERSON>", "launchDate": "Data uruchomienia", "stepName": "<PERSON><PERSON>wa kroku", "runId": "ID biegu", "clone": "Klon", "confirmDeleteCron": "<PERSON>zy na pewno chcesz usunąć wybranego crona?", "alrCronDeleted": "Cron został usunięty!", "wantToContinueQ": "<PERSON><PERSON> ch<PERSON> k<PERSON>?", "valueCannotBeEntered": "Nie można wp<PERSON><PERSON><PERSON><PERSON> warto<PERSON>", "processingQueues": "Kolejki przetwarzania", "pause": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillOptionsFromVarHelper": "Opcje filtrów można wypełnić tylko ze zmiennych typu DT, DL, LT, LD, LN i D, które nie pozwalają na wielokrotny wybór.", "defaultTemplateName": "Domyślna nazwa szablonu", "defaultTaskName": "Domyślna nazwa zadania", "defaultVariableName": "Domyślna nazwa zmiennej", "variableName": "Nazwa zmiennej", "alrNoDataFound": "Nie znaleziono danych", "ttProcessingQueuesInfo": "Kolejki przetwarzania są wyłączone.\nA<PERSON> je w<PERSON>, na<PERSON><PERSON>y ustawić co najmniej jedną z konfiguracji „scaling.queue.*.enabled“ na wartość true.", "businessUsers": "Użytkownicy biznesowi", "completeHrAgenda": "Pełna agenda HR", "usageStatsByHeader": "Statystyki użytkowania według nagłówka", "usageStatsByOrgUnit": "Statystyki użytkowania według jednostki org.", "usageStatsByUser": "Statystyki użytkowania według użytkownika", "completedTasksNum": "Liczba ukończonych zadań", "startedProcessesNum": "Liczba rozpoczętych spraw", "ideHelp": "Naciśnij Ctrl + <PERSON><PERSON><PERSON> w edyt<PERSON><PERSON>, aby z<PERSON><PERSON><PERSON><PERSON> sugestie, naci<PERSON><PERSON><PERSON> ponownie, aby u<PERSON><PERSON><PERSON> bardziej szczegółową pomoc. Naciśnij F1, aby wy<PERSON>wi<PERSON><PERSON><PERSON> listę wszystkich dostępnych poleceń i skrótów klawiszowych. Więcej informacji można znaleźć w <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>dokumentacji edytora</a>.", "restHelp": "Wprowadź adres URL jednej z usług tabel TAS (np. '/tasks/mine') i po załadowaniu usługi wybierz kolumny tabeli, które chcesz wyświetlić w kontenerze.", "defaultGraphName": "Domyślna nazwa wykresu", "graphName": "Nazwa wykresu", "ttStatistics": {"heading": "Statystyki", "body": ""}, "defaultAxisXName": "Domyślna nazwa osi X", "defaultAxisYName": "Domyślna nazwa osi Y", "defaultFilterName": "Domyślna nazwa filtra", "filterName": "Nazwa filtra", "defaultOptionName": "Domyślna nazwa opcji", "optionName": "Nazwa opcji", "defaultOverviewName": "Domyślna nazwa przeglądu", "overviewName": "Nazwa przeglądu", "eventName": "Nazwa zdarzenia", "wantToOverrideEs6": "<PERSON><PERSON><PERSON> nap<PERSON> ch<PERSON>, napisz <b>ES6</b>", "processArchivation": "archiwizacja procesów", "processUnarchivation": "proces unarchi<PERSON><PERSON><PERSON>", "resendEmail": "<PERSON><PERSON><PERSON><PERSON>j ponownie e-mail", "alrFailedSendEmail": "Nie udało się wysłać e-maila", "ttResendEmail": {"heading": "<PERSON><PERSON><PERSON><PERSON>j ponownie e-mail", "body": "Ponownie wysyła wcześniej wysłane powiadomienie e-mail. Odbiorców można zmienić lub do<PERSON>."}, "addCurrentScreenToFavourite": "Dodaj bieżący ekran do swoich ulubionych", "attachmentAdd": "Dodaj dokument", "createNewCase": "Tworzenie nowej sprawy", "moreLanguage": "Inne warianty języków", "notesAdd": "<PERSON><PERSON><PERSON>", "notesNew": "Nowa notatka", "removeCurrentScreenFromFavourite": "Usuń bieżący ekran z ulubionego", "setDashboard": "<PERSON><PERSON><PERSON><PERSON>", "chooseFromCases": "Wybierz z przypadków", "folders": "Foldery", "newFolderBtn": "Nowy folder", "documentInfo": "Informacje o dokumencie", "userInfo": "Informacje o użytkowniku", "deleteImage": "<PERSON><PERSON><PERSON> obraz", "profilePhoto": "<PERSON><PERSON><PERSON><PERSON><PERSON> profilow<PERSON>", "profilePhotoCaption": "Użyj zdjęcia w formacie jpeg, jpg, png lub gif.", "updatePhoto": "Aktualizuj zdjęcie", "mailNotifications": "Powiadomienia e-mailowe", "userPreferences": "Preferencje użytkownika", "userSettings": "Ustawienia użytkownika", "allVices": "Wszystkie zastępstwa", "createVice": "Utwórz zastę<PERSON>two", "editVice": "Edytuj zastępstwo", "viceTip": "Zastępstwo pozwala na przekazanie agendy współpracownikowi", "emptyDataMessage": "Nie ma nic więcej", "addFirstNote": "<PERSON><PERSON><PERSON> notatk<PERSON>", "noResultsFor": "Brak wyników dla:", "noCurrentTasks": "Brak bieżących zadań", "checkYourSearch": "Sprawdź wyszukiwanie i spróbuj ponownie.", "noFavOverviews": "Brak ulubionych przeglądów", "favOverviewsTip": "Dodaj przegląd do ulubionych za pomocą gwiazdki", "noHiddenOverviews": "Nie masz żadnych ukrytych przeglądów", "addOverview": "Dodaj p<PERSON>ląd", "hidden": "<PERSON>k<PERSON><PERSON>", "removeConfirm": "Usuń", "removeItem": "<PERSON>zy na pewno chcesz usunąć {{variable}}?", "changePicture": "Zmień zdjęcie", "saveFilter": "Zapisz filtr", "addAnotherVice": "Dodaj kolejne zastępstwo", "saveVice": "Zapisz zastępstwo", "firstLastName": "Imię i nazwisko", "taskInfo": "Informacje o zadaniu", "emptyFavsTip": "Dodaj ulubione za pomocą przycisku", "saveAndClose": "Zapisz i zamknij", "usersCanEditOverview": "Użytkownicy mogą edytować przegląd", "assignedUsers": "Przypisani Użytkownicy", "assignedOrgUnits": "Przypisane jednostki organizacyjne", "assignedRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON> role", "otherLangVariants": "Inne warianty językowe", "moveToSharing": "Przejdź do udostępniania", "insertDocumentsPerm": "Użytkownik ma uprawnienia do wstawiania dokumentów i notatek", "saveNewPassword": "Zapisz nowe hasło", "confirmSubscription": "Potwierdź subskrypcję", "subscriptionCaption": "Wybrany przegląd zostanie wysłany drogą e-mailową o ustawionej godzinie.", "by": "<PERSON><PERSON><PERSON>", "frequency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "termination": "Zakończenie", "ofTheMonth": "<PERSON>", "endOnDate": "Zakończ w dniu", "endAfter": "Zakończ po", "onlyOnWorkingDays": "<PERSON><PERSON><PERSON> w dni robocze", "occurrences": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayOfWeekBy": "Dnia tygodnia", "calendarDayBy": "Dnia kalendarzowego", "dateBy": "daty", "byDate": "<PERSON>d<PERSON><PERSON> daty", "byOccurrenceCount": "Według liczby wystąpień", "infinitely": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayOfMonthAdornment": ". d<PERSON><PERSON>", "ordinalAdornment": ".", "toDateBeforeFromError": "Data 'Do' nie może być wcześniejsza niż data 'Od'", "vice": "Zastępstwo", "previewShown": "Podgląd pokazany", "duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideBtn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userView": "Widok użytkownika", "adminView": "Widok administratora", "or": "<PERSON><PERSON>", "overlappingVicesError": "Zastępstwa nie mogą się pokrywać", "fileVar": "plik", "nodeVar": "w<PERSON><PERSON>ł", "uploadDifferentFile": "Prześlij inny plik", "uploadedFile": "Przesłany plik", "refreshPage2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ę", "refreshPageCaption": "<PERSON><PERSON>, o<PERSON><PERSON><PERSON><PERSON><PERSON> stronę w przeglądarce.", "ttCopy": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Pozwala skopiować wybrany element z możliwością edycji niektórych parametrów."}, "alrError_INVALID_CSV_MAPPING": "Nie znaleziono kolumny CSV „%s” w mapowaniu zdarzenia. Skontaktuj się z administratorem aplikacji.", "documentPreview": "Podgląd dokumentu", "moveUp": "Przewiń do góry", "moveDown": "Przewiń w dół", "moveToFilter": "Przenieś do filtra", "moveToSorting": "Przejdź do sortowania", "addSorting": "<PERSON><PERSON><PERSON>", "cancelFilters": "<PERSON><PERSON><PERSON> filtry", "docUploadedImmediately": "Dokument zostanie przesłany natychmiast", "moreOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>ji", "docSearchPlaceholder": "Np. faktura.pdf...", "tasksSearchPlaceholder": "Np. wprow<PERSON>ź nową fakturę...", "docUploadedImmediatelyPrivate": "Dokument zostanie natychmiast przesłany jako prywatny", "takeTsk": "Podejmij <PERSON>", "tasksActive": "Aktywne zadania", "subprocesses": "Podprocesy", "cancelAuthorization": "<PERSON><PERSON><PERSON>", "cancelAuthorizationConfirm": "Czy na pewno chcesz anulować autoryzację urządzenia?", "linkMobileApp": "Połącz aplikację mobilną", "mobileApp": "Aplikacja mobilna", "scanThisQr": "Zeskanuj ten kod QR za pomocą swojego urządzenia mobilnego.", "scanningQr": "Skanowanie kodu QR. Proszę cze<PERSON>ć.", "deviceName": "Nazwa urządzenia", "newDeviceName": "Nazwa nowego urządzenia", "registrationDate": "Data rejestracji", "lastLogin": "Ostatnie logowanie", "mobileNotifications": "Powiadomienia mobilne", "disableMobileNotification": "Wyłączenie powiadomień na telefonie", "newQrCode": "Nowy kod QR", "inactiveScanQr": "Nieaktywne - zeskanuj kod QR.", "enableNotifications": "Włącz powiadomienia", "tip": "Wskazówka: {{message}}", "alrFavContainerAlreadyExists": "Kontener z ulubionymi przedmiotami już istnieje.", "addGraph": "<PERSON><PERSON><PERSON>", "newRow": "<PERSON><PERSON> wiersz", "confirmSetDefaultDashboard": "<PERSON><PERSON> chcesz ustawić bieżący pulpit nawigacyjny jako domyślny dla wszystkich użytkowników?", "changeMayAffectAllUsers": "Ta zmiana może wpłynąć na wszystkich użytkowników.", "noOverviewsTip": "Utwórz nowy przegląd za pomocą przycisku „Dodaj przegląd“", "removeFromHidden": "Usuń z ukrytych", "last7Days": "Ostatnie 7 dni", "last14Days": "Ostatnie 14 dni", "last30Days": "Ostatnie 30 dni", "lastCalendarMonth": "Ostatni miesiąc kalendarzowy", "lastQuarter": "Ostatni kwartał", "last12Months": "Ostatnie 12 miesięcy", "lastCalendarYear": "Ostatni rok kalendarzowy", "noFilterSet": "Brak ustawionego filtru", "noSortingSet": "Brak ustawienia sortowania", "deleteGroup": "Usuń grupę", "newGroup": "Nowa grupa", "operators": "Operatory", "withActiveTask": "Z aktywnym zadaniem", "withoutActiveTask": "Bez aktywnego zadania", "withNoTerm": "<PERSON><PERSON> terminu", "withTerm": "Z terminem", "securityAndAuthentication": "Bezpieczeństwo i uwierzytelnianie", "dataIntegrationAndManagement": "Integracja i zarządzanie danymi", "appManagementAndConfig": "Zarządzanie i konfiguracja aplikacji", "monitoringAndMaintenance": "Monitorowanie i konserwacja", "adminSearchPlaceholder": "Na przykład, Pliki publiczne...", "authenticationAdminDescription": "Opcje logowania użytkownika", "certificatesAdminDescription": "Certyfikaty dla TAS", "elasticsearchAdminDescription": "Integracja z Elasticsearch", "xmlProcessImportAdminDescription": "Importuj procesy XML przy użyciu rekordu cron XMLProcessImport.js", "structureImportExportAdminDescription": "Import/eksport struktury organizacyjnej, użytkowników i ról", "dmsAttributesAdminDescription": "Lista atrybutów dokumentów w DMS", "dynTablesAdminDescription": "Przechowywanie danych w tabelach dynamicznych", "csvAdminDescription": "Manipulacja plikami CSV w aplikacji", "configurationAdminDescription": "Konfiguracja aplikacji", "settingsAdminDescription": "Ustawienia identyfikacji firmy i inne zadania administracyjne", "logsAdminDescription": "Zarządzaj i przeglądaj dzienniki aplikacji", "migrationsAdminDescription": "Migracja danych i konfiguracja aplikacji", "guidesAdminDescription": "Pomoc i poradniki dla użytkowników", "schemeAdminDescription": "Schemat kolorów, logo i inne elementy aplikacji", "sequencesAdminDescription": "Zarządzaj sekwencjami używanymi w szablonach", "serviceConsoleAdminDescription": "Polecenia aplikacyjne-administracyjne poprzez konsolę serwisową", "serviceOperationsAdminDescription": "Kompleksowe zarządzanie operacjami serwisowymi", "scriptsAdminDescription": "Zarządzaj skryptami wielokrotnego użytku w różnych szablonach", "appStatusAdminDescription": "Informacja o aktualnym statusie aplikacji", "usageStatsAdminDescription": "Wyświetl statystyki użytkowania aplikacji", "maintenanceAdminDescription": "Ustawienia konserwacji i wykonywanie zadań konserwacyjnych", "scheduledTasksAdminDescription": "Zarządzaj wszystkimi zaplanowanymi zadaniami", "publicFilesAdminDescription": "Zarządzaj publicznymi plikami i dokumentacją", "cronsAdminDescription": "Automatyzacja zwykłych zadań", "hrAgendaAdminDescription": "Zarządzanie agendą użytkowników w ramach HR", "emailsQueueAdminDescription": "Zarządzanie kolejką poczty e-mail i całą komunikacją e-mail z TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Nie udało się dodać elementu do ulubionych", "alrRemoveFavItemFailed": "Usunięcie elementu z ulubionych nie powiodło się", "alrAddHiddenItemFailed": "<PERSON>e udało się dodać ukrytego elementu", "alrRemoveHiddenItemFailed": "<PERSON>e udało się usunąć ukrytego elementu", "display": "Wyświetlanie", "compact": "Zwarta", "standard": "Standardowa", "comfortable": "Komfortowa", "exportTo": "Eksportuj do", "adminMenuTip": "Dodaj swoje elementy w administracji do ulubionych. Klikając gwiazdkę, wyświetlisz element tutaj.", "editorDocumentation": "Dokumentacja edytora", "addSection": "<PERSON><PERSON><PERSON>", "insertSection": "Wstaw sekcję", "section": "<PERSON><PERSON><PERSON><PERSON>", "sections": "<PERSON><PERSON><PERSON><PERSON>", "toTop": "Na początek", "toEnd": "Na koniec", "alrSectionNotBeEmpty": "Sek<PERSON>ja nie może by<PERSON> pusta", "confirmDeleteSection": "<PERSON><PERSON> chcesz usunąć sekcję?", "sectionVarsMoveAllTasks": "Zmienne we wszystkich zadaniach zostaną przeniesione z usuniętej sekcji do zmiennych bez sekcji.", "sectionVarsMove": "Zmienne zostaną przeniesione z usuniętej sekcji do zmiennych bez sekcji.", "actionCannotUndone": "Tego działania nie można co<PERSON>.", "overviewOfAllNews": "Przegląd wszystkich aktualności", "copyOverview": "<PERSON><PERSON><PERSON><PERSON> przegląd", "create": "Utwórz", "copyExistingOverview": "<PERSON><PERSON><PERSON><PERSON> istniejący przegląd", "selectOverview": "<PERSON><PERSON><PERSON><PERSON> prz<PERSON>ląd", "chooseFromOverviews": "Wybierz z przeglądów...", "selectTemplate": "<PERSON><PERSON><PERSON><PERSON>", "chooseFromAvailableTemplates": "Wybierz z dostępnych szablonów...", "loginWithUsernamePassword": "Zaloguj się podając nazwę użytkownika i hasło", "signInWithCorporateIdentity": "Zaloguj się przy użyciu tożsamości korporacyjnej", "whatsNewInTAS": "Co nowego w TAS?", "whatsNewInTASDescription": "A<PERSON><PERSON><PERSON><PERSON><PERSON>, nowe funkcje, porady, triki i wszystko, co musisz w<PERSON>zieć.", "justOpen": "Tylko otwórz", "editOverview": "Edyt<PERSON>j prz<PERSON>ląd", "noGraphsTip": "Utwórz nowy wykres za pomocą przycisku „Dodaj wykres“", "noDocumentsTip": "Dodaj dokument do zadania lub za pomocą przycisku „Dodaj“", "noFilesTip": "Dodaj nowy plik za pomocą przycisku „Dodaj“", "less": "Mniej", "notContains": "<PERSON><PERSON>", "factorySettings": "Ustawienia fabryczne", "previewCollapsedNavMenu": "Podgląd zapakowanego menu nawigacji", "previewExpandedNavMenu": "Podgląd menu nieupakowanego nawigacji", "logoForCollapsedNavMenu": "Logo do zapakowanego menu nawigacji", "logoForExpandedNavMenu": "Logo do rozpakowanego menu nawigacji", "organisationLogo": "Logo organizacji", "pickLogoOrganisation": "Wybór logo dla organizacji", "addLogo": "Dodaj logo", "clickForAddLogoOrDrop": "K<PERSON><PERSON><PERSON>, a<PERSON> do<PERSON><PERSON> logo lub upuść plik tutaj", "useLogoSizeMin": "Użyj logo minimalnego rozmiaru", "logoForLightTheme": "Logo dla trybu jasnego", "logoForDarkTheme": "Logo dla trybu ciemnego", "notEquals": "<PERSON><PERSON> równa się", "sharedWithMe": "Udostępniono mi", "myOverview": "M<PERSON>j przegląd", "getMobileAppText": "Pobierz aplikację mobilną ze sklepu z aplikacjami", "noDocuments": "Brak dokumentów", "noNotes": "Brak notatek", "noFiles": "Brak plików", "addFirstDocument": "<PERSON><PERSON><PERSON> doku<PERSON>", "killed": "<PERSON><PERSON><PERSON>", "chooseNewLogo": "Wybierz nowe logo", "function": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groupFunction": "Funkcja między grupami", "mobileAppAuthFailed": "Uwierzytelnianie aplikacji mobilnej nie powiodło się.", "currentDocumentVersion": "Aktualna wersja dokumentu", "csp": "Polityka bezpieczeństwa treści", "documentsDelete": "Usuń dokumenty", "confirmDocumentsDelete": "<PERSON>zy na pewno chcesz usunąć wybrane dokumenty?", "confirmDocumentsDownload": "<PERSON><PERSON> ch<PERSON>z pobra<PERSON> wybrane dokumenty?", "firstNum": "pierwsze {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Pobierz dokumenty", "caseLogs": "<PERSON><PERSON> sprawy", "archiveCases": "Archiwizuj przypadki", "archive": "Archiwizuj", "unarchive": "De-archiwi<PERSON>j", "confirmArchiveCases": "<PERSON>zy na pewno chcesz zarchiwizować wybrane sprawy?", "archiveInDays": "Archiwizuj za (dni)", "archived": "Zarchiwizowany", "archivedx": "Zarchiwizowane", "alrArchivingCase": "Przypadek jest archiwizowany...", "alrCaseArchived": "Przypadek został zarchiwizowany.", "alrLackOfPermsToArchiveCase": "Nie masz wystarczających uprawnień do archiwizacji przypadku.", "alrArchiveCaseFailed": "Archiwizacja przypadku nie powiodła się.", "alrUnarchivingCase": "Przypadek jest de-archiwizowany...", "alrCaseUnarchived": "Przypadek został de-archiwizowany.", "alrLackOfPermsToUnarchiveCase": "Nie masz wystarczających uprawnień do de-archiwizacji przypadku.", "alrUnarchiveCaseFailed": "De-archiwizacja przypadku nie powiodła się.", "byUser": "Według użytkownika", "byAgenda": "<PERSON>d<PERSON><PERSON> agendy", "agendaHandover": "Przeka<PERSON><PERSON> agendy", "activeUsers": "Aktywni użytkownicy", "lockedUsers": "Zablokowani użytkownicy", "allUsers": "Wszyscy użytkownicy", "inactiveUsers": "Nieaktywni użytkownicy", "hrAgendaSearchPlaceholder": "<PERSON><PERSON><PERSON> <PERSON> ...", "completeAgendaHandover": "Kompletne przekazanie agendy", "handoverCases": "Sprawy przekazania", "handoverTasks": "Zadania przekazania", "handoverVars": "Zmienne przekazania", "changeTaskOwner": "Zmień narzędzie do rozwiązywania zadań", "confirmHandover": "Potwierdź przekazanie", "filterCasesByHeaderTip": "Możesz filtrować wszystkie przypadki pod tym samym nagłówkiem w kolumnie Nagłówek.", "userAgendaSelectedHandover": "Przekazanie <b style=\"color: {{color}};\">wybranej</b> <PERSON><PERSON>", "userAgendaCompleteHandover": "Przekazanie <b style=\"color: {{color}};\">kompletnej</b> <PERSON><PERSON>", "confirmAgendaHandover": "Czy na pewno chcesz przekazać wybraną agendę ({{selected}}) użytkownikowi <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Czy na pewno chcesz przekazać <b>wybraną</b> agend<PERSON> użytkownika <b>{{user}}</b> użytkownikowi <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Czy na pewno chcesz przekazać <b>kompletną</b> agendę użytkownika <b>{{user}}</b> użytkownikowi <b>{{newUser}}</b>?", "refreshSessionTitle": "Sesja TAS zostanie zakończona za {{minutes}} minut.", "refreshSessionCaption": "Klik<PERSON>j \"<PERSON><PERSON><PERSON>j dalej\", aby k<PERSON><PERSON><PERSON><PERSON> bez przerw.", "continueWorking": "<PERSON><PERSON><PERSON><PERSON>", "sessionExpiredCaption": "K<PERSON><PERSON>j \"Zaloguj się ponownie\", aby p<PERSON><PERSON><PERSON><PERSON><PERSON> do ekranu logowania.", "loginExpired": "Wylogowaliśmy Cię po długim okresie bezczynności.", "confirmArchiveCase": "<PERSON>zy na pewno chcesz zarchiwizować wybraną sprawę?", "isLowerOrEqualThan": "<PERSON><PERSON> <PERSON><PERSON> mniejszy lub równy", "confirmUnarchiveCase": "<PERSON>zy na pewno chcesz rozpakować wybraną sprawę?", "addCaseRightNewUserTooltip": "Je<PERSON><PERSON> nie zaznaczysz tej opcji, nowy użytkownik zostanie zastąpiony w zmiennej biznesowej, ale nie będzie miał dostępu do sprawy.", "canBeViced": "Jestem zastąpiony", "canVice": "Zastępuję", "backgroundColor": "<PERSON><PERSON>", "defaultDashboardView": "Podgląd domyślnego pulpitu nawigacyjnego", "colorScheme": "Kolorystyka", "displaySelectionAsTags": "Wyświetl wybór jako tagi", "displayAsPassword": "Wyświ<PERSON><PERSON> jako hasło", "sideBySide": "Obok siebie", "copyAssignmentFromTask": "Skopiuj przydział z zadania", "toTask": "Do zadania", "copyTaskAssignmentWarning": "<PERSON>rzyd<PERSON>ł w zadaniu nie jest pusty, czy ch<PERSON>z go nad<PERSON>ć?", "copyToOtherTasks": "Skopiuj do innych zadań", "noteScriptsNotApplied": "Uwaga: skry<PERSON>y nie są zastosowane", "generateRecHistory": "Pokaż w aktywnych zadaniach i historii", "leaveFormerRoles": "Pozostaw poprzednie role", "includeCompetences": "Uwzględnij kompetencje", "copyRoles": "<PERSON><PERSON><PERSON><PERSON> role", "userIsActive": "Użytkownik jest aktywny", "systemUser": "Użytkownik systemowy", "copyRolesFromUser": "<PERSON><PERSON><PERSON><PERSON> <PERSON> od użytkownika", "assignedRolesOverview": "Przegląd przypisanych ról", "copyRolesInfo": "<PERSON><PERSON><PERSON> dany użytkownik jest częścią kompetencji, kompetencje te nie zostaną od razu skopiowane. Zostaną wygenerowane:", "notificationOn": "Przytwierdzony", "notificationOff": "Zamknięcie", "onNotification": "Powiadomienie", "offNotification": "Powiadomienie", "page": "strona", "fromTo": "Od - do", "isAnyOfValue": "Jest dowolną wartością od", "notcontains": "nie zaw<PERSON>", "notequals": "nierówny", "fromto": "Od - do", "isanyofvalue": "jest dowolną wartością od", "alrNoteToggleVisibiltyFailed": "Ukrywanie/odkrywanie notatki nie powiodło się", "alrNoteHideOnEditFailed": "Ukrywanie oryginalnej notatki nie powiodło się", "hiddenShe": "Ukryta", "showHiddenNotes": "Pokaż ukryte notatki", "alrNoteEdited": "Zmieniona wersja notatki została zapisana", "notesEdit": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "Nazwa wyświetlana", "clientDateFormat": "Format daty", "defaultByLanguage": "Domyślnie według języka", "restKeysOptionsNotUpToDate": "Nieaktualny wybór wartości - ponownie załaduj usługę.", "invalidValue": "Nieprawidłowa wartość", "ended": "Zakończone", "exportAllActive": "Eksportuj wszystkie aktywne", "alrScriptsLoadFailed": "Wczytywanie skryptów nie powiodło się.", "scriptsImport": "Import skryptów", "doImport": "Import<PERSON>j", "alrImportingScripts": "Importowanie skryptów...", "alrScriptsImported": "Skrypty zostały zaimportowane.", "alrScriptsImportFailed": "Import skryptów nie powiódł się.", "removeAll": "Us<PERSON>ń wszystko", "alrNoScriptsToImport": "Brak skryptów do zaimportowania.", "activateAll": "Aktywuj wszystko", "alrNoPermsToEditNoteInVice": "Nie masz uprawnień do edytowania notatki jako zastępca.", "alrNoPermsToToggleNoteVisibilityInVice": "Nie masz uprawnień do ukrywania/odkrywania notatki jako zastę<PERSON>.", "plusMore": "wię<PERSON>j", "variableAlignment": "Wyrównanie zmiennej", "variableAlignmentHelp": "Wpływa na wyrównanie wartości zmiennej w formularzu zadania.", "variableAlignmentLeft": "<PERSON><PERSON>", "variableAlignmentRight": "Prawo", "tasksMineAndToPull": "Moje + Do przejęcia", "myDevice": "<PERSON><PERSON>", "deleteLogo": "Usuń logo", "namingFilter": "Nazwa filtra", "exceptionsToRegularSchedule": "Wyjątki od regularnego harmonogramu", "noExceptions": "Brak wyjątków", "specificDates": "<PERSON><PERSON><PERSON><PERSON> daty", "dateFromTo": "Data od - do", "weekdayCap": "Dzień tygodnia", "specificDayBy": "Określony dzień", "yearsBy": "lat", "timed": "Czasowy", "firstDayOfMonth": "Pierwszy dzień miesią<PERSON>", "lastDayOfMonth": "Ostatni dzień miesiąca", "firstDayOfYear": "Pierwszy dzień roku", "lastDayOfYear": "Ostatni dzień roku", "addDate": "Data dodania", "newPlan": "Nowy plan", "addAnother": "<PERSON><PERSON><PERSON>", "startTime": "<PERSON><PERSON> roz<PERSON>", "endTime": "Czas zakończenia", "inTimeFromTo": "w czasie od {{from}} do {{to}}", "dayOfMonthBy": "Dzień miesiąca", "cWorkDays": "dni robocze", "cWeeks": "tygodnie", "cMonths": "<PERSON><PERSON><PERSON><PERSON>", "cYears": "lata", "everyWeek": "co tydzień", "everyYear": "co roku", "inMonth": "w mi<PERSON><PERSON><PERSON>", "everyDay": "codziennie", "seqIdEdit": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "allowMultiselectSearchRight": "Zezwalaj na wyszukiwanie w przypisaniu", "doubleHeightForContent": "Podwójna wys<PERSON>ć dla zawartości", "alrNoVariablesMappingToImport": "Brak mapowania zmiennych do zaimportowania.", "alrVariablesMappingImportLoadFailed": "Wczytywanie mapowania zmiennych do importu nie powiodło się.", "variablesMappingImport": "Import mapowania zmiennych", "useAllMappings": "Użyj wszystkich mapowań", "doExportVariablesMapping": "Eksportuj mapowanie zmiennych", "alrImportingVariablesMapping": "Importowanie mapowania zmiennych...", "alrVariablesMappingImported": "Mapowanie zmiennych zostało zaimportowane.", "alrVariablesMappingImportFailed": "Import mapowania zmiennych nie powiódł się.", "alrVariablesMappingImportedPartially": "Mapowanie zmiennych zostało zaimportowane tylko częściowo. Niektóre zmienne nie zostały znalezione.", "alrEditorHintsLoadFailed": "Nie udało się załadować wskazówek edytora.", "addTable": "<PERSON><PERSON><PERSON>", "confirmDynTablesDelete": "<PERSON>zy na pewno chcesz usunąć wybrane dynamiczne tabele?", "dynTablesDelete": "<PERSON><PERSON><PERSON> dynamiczne tabele", "addRow": "<PERSON><PERSON><PERSON>", "preview": "Podgląd", "columnDelete": "<PERSON><PERSON>ń kolumnę", "editRow": "<PERSON><PERSON><PERSON><PERSON>", "addingNewColumn": "Dodawanie nowej kolumny", "addingNewRow": "Do<PERSON><PERSON><PERSON> nowego w<PERSON>za", "columnsRename": "Zmień nazwę kolumn", "rowCellValues": "Wartości komórek w wierszu", "saveDynTableName": "Zapisz nazwę dynamicznej tabeli", "saveDynTableNameQ": "Zapisz nazwę dynamicznej tabeli?", "saveDynTableNameWarning": "<PERSON><PERSON><PERSON>, upew<PERSON>j się, że zmiana nazwy tabeli nie wpłynie na istniejące obliczenia w szablonach.", "rowMove": "Przenieś wiersz", "alrCsvParsingErr": "Błąd podczas parsowania pliku CSV!", "addFirstTableColumn": "<PERSON><PERSON><PERSON> kolumnę tabeli", "my": "<PERSON><PERSON><PERSON>", "license": "Licencja", "licenses": "Licencje", "addLicense": "<PERSON><PERSON><PERSON> l<PERSON>", "licenseResult": "Wynik licencji", "alrLicenceResultLoadingFailed": "<PERSON>e udało się załadować wyniku licencji.", "licensesAdminDescription": "Zarządzanie licencjami", "uploadByDragging": "Prześlij plik poprzez przeciągnięcie.", "uploadByDraggingAnywhere": "Prześ<PERSON>j plik, przeciągając w dowolne miejsce w przestrzeni.", "assignVariable": "Przypisz zmienną", "confirmDeleteSectionName": "<PERSON>zy na pewno chcesz usunąć sekcję <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Ostrzeżenie: sekcja zostanie usunięta dla wszystkich zadań, kt<PERSON><PERSON>ch to dotyczy, w tym z<PERSON>nnych.", "tasksAffected": "Zadania objęte problemem", "varSearchPlaceholder": "Np. fakturowanie ...", "enlarge": "Powię<PERSON><PERSON>", "show": "Po<PERSON><PERSON>", "shrink": "Zmniejsz", "hide": "<PERSON><PERSON><PERSON><PERSON>", "doValidate": "<PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Numer telefonu", "textLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu", "when": "kiedy", "to2": "do", "that": "że", "dynCondBuilderBlockFunctionDescShow": "<PERSON><PERSON><PERSON> z<PERSON>, je<PERSON><PERSON> warunek jest spełniony.", "dynCondBuilderBlockFunctionDescHide": "<PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON> warunek jest spełniony.", "dynCondBuilderBlockFunctionDescChange": "<PERSON><PERSON><PERSON> warto<PERSON> z<PERSON>j, je<PERSON><PERSON> warunek jest spełniony.", "dynCondBuilderBlockFunctionDescValidate": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>nne<PERSON>.", "addCondition": "<PERSON><PERSON><PERSON>", "operator": "operator", "equals": "równa się", "greaterthan": "wi<PERSON><PERSON><PERSON> niż", "greaterorequal": "wi<PERSON><PERSON><PERSON> lub równy", "lessthan": "mniejszy niż", "lessorequal": "mniejszy lub równy", "demoCode": "Kod demonstracyjny", "code": "Kod", "confirmDeleteConditions": "<PERSON>zy na pewno chcesz usunąć wszystkie warunki (w tym skrypt)?", "validationErrorMessage": "Komunikat o błędzie walidacji", "alrScriptToStructuredBlockConversionFailed": "Konwersja skryptu na blok strukturalny nie powiodła się.", "alrStructuredBlockToScriptConversionFailed": "Konwersja bloku strukturalnego na skrypt nie powiodła się.", "alrScriptToBuilderConversionFailed": "Konwersja skryptu do kreatora nie powiodła się.", "alrBuilderToScriptConversionFailed": "Konwersja z kreatora na skrypt nie powiodła się.", "dynCondBuilderBlockFunctionDescScript": "Blok skryptu dynamicznych warunków.", "convertToStructuredBlock": "Przekształć na blok strukturalny", "convertToScript": "Przekształć na skrypt", "dynCondBuilderBlockWatchVarsLabel": "Uruchom przy zmianie (watchVars)", "variables": "Zmienne", "copyToOthers": "Kopiowanie do innych", "sectionName": "<PERSON><PERSON><PERSON> se<PERSON>", "newSectionName": "Nazwa nowej sek<PERSON>ji", "testIt": "Test", "addAdjacentSection": "<PERSON><PERSON>j <PERSON>dnią sekcję", "addAdjacentSectionBelow": "Dodaj sąsiednią sekcję poniżej", "selectExistingSection": "<PERSON><PERSON><PERSON><PERSON> sekcję", "renameSectionWarning": "Uwaga: sekcja zostanie przemianowana na wszystkie zadania szablonów.", "warning2": "Ostrzeżenie", "copyAssignmentToTask": "Skopiuj zadanie do zadania", "copyAlsoConditions": "Kopia i warunki", "copyAssignmentToTaskWarning": "Uwaga: Przypisanie i prawdopodobnie dynamiczne warunki w wybranym zadaniu zostaną przepisane.", "importFromOtherTask": "Importować z innego zadania", "startFromScratch": "Zacznij od początku", "howToStartAssignments": "Jak ch<PERSON>z zacząć przypisywać zmienne?", "selectTaskToImport": "<PERSON><PERSON><PERSON><PERSON> zada<PERSON>u", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectTaskToTest": "<PERSON><PERSON> wy<PERSON>ć zadanie do testowania", "toTestSaveChanges": "Zmiany powinny być przechowywane do testowania.", "variableAssignmentTest": "Przypisanie testów zmiennych", "viewAsMobile": "Wyświetl jako na urządzeniach mobilnych", "viewAsPc": "Zobacz jak na <PERSON>", "emptySpace": "<PERSON><PERSON><PERSON>", "variableAssignments": "Przypisanie zmiennych", "allowCompletionOnChangeOf": "Zezwól na ukończenie przy zmianie", "dynCondBuilderBlockFunctionDescRead": "Zmień tryb zmiennej na \"tylko do odczytu\", je<PERSON><PERSON> warunek jest spełniony.", "dynCondBuilderBlockFunctionDescWrite": "Zmień tryb zmiennej na \"do odczytu i zapisu\", je<PERSON><PERSON> warunek jest spełniony.", "dynCondBuilderBlockFunctionDescMust": "Zmień tryb zmiennej na \"wymagana\", je<PERSON><PERSON> warunek jest spełniony.", "dynCondBuilderBlockFunctionDescSolve": "Pozwala na ukończenie zadania przy zmianie danej zmiennej, je<PERSON><PERSON> warunek jest spełniony.", "newsManagement": "Zarządzanie wiadomościami", "newsManagementAdminDescription": "Zarządzanie wiadomościami w aplikacji", "addNewsPost": "<PERSON><PERSON><PERSON>", "newPost": "Nowy post", "news": "Aktual<PERSON>ś<PERSON>", "basicInfo": "Podstawowe informacje", "publicationPlanning": "Planowanie publikacji", "displayToUsers": "Wyświetlanie użytkownikom", "displayLocation": "Miejsce wyświetlania", "newsPostContent": "<PERSON><PERSON><PERSON><PERSON>", "postTitle": "<PERSON><PERSON><PERSON>a", "newsManagementPostDetailPhoneNumberTooltip": "Numer telefonu do wyświetlenia w szczegółach nowości", "newsManagementPostDetailEmailTooltip": "E-mail do wyświetlenia w szczegółach nowości", "customUrlLink": "Niestandardowy link URL", "newsManagementPostDetailCustomUrlLinkTooltip": "Niestandardowy link URL do wyświetlenia w szczegółach nowości", "stateAfterSaving": "Status po zapisaniu", "newsPostStateActive": "Aktywny", "newsPostStateInactive": "Nieaktywny", "newsPostStatePlanned": "Zaplanowany", "endNewsPostOnSpecificDate": "Zakończ wiadomość w określonym dniu", "sendNewsPostViaEmail": "<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>dom<PERSON> e-mailem", "priorityNewsPost": "Priorytetowa wiadom<PERSON>ść", "newsManagementPostDetailPriorityNewsTooltip": "Na przykład do ogłoszenia przerwy w działaniu lub zmiany procedury", "newsPostEndDate": "Data zakończenia wiadomości", "pickNewsPostDisplayToOrgUnits": "Którym jednostkom organizacyjnym w<PERSON>wi<PERSON>ć wiadom<PERSON>?", "pickNewsPostDisplayToRoles": "<PERSON>t<PERSON><PERSON>m rolom wyświ<PERSON>ć wiadomoś<PERSON>?", "pickNewsPostDisplayToUsers": "Którym użytkownikom wyświetlić wiadomość?", "pickNewsPostDisplayOnTemplate": "Na jakim szablonie wyświ<PERSON>lić wiadomoś<PERSON>?", "pickNewsPostDisplayOnHeaders": "Na jakich nagłówkach wyświetlić wiadomość?", "pickNewsPostDisplayOnTasks": "Na jakich zadaniach wy<PERSON><PERSON><PERSON> wiadom<PERSON>ś<PERSON>?", "pickNewsPostDisplaySubOptionsHelperText": "Najpierw wybierz s<PERSON>blon, na którym chcesz wyświetlić wiadomość.", "newsTagsManagement": "Zarządzanie tagami wiadomości", "newsTagsManagementAdminDescription": "Zarządzanie tagami wiadomości w aplikacji", "addTag": "<PERSON><PERSON><PERSON> tag", "tags": "Tagi", "publicationDate": "Data publikacji", "contacts": "Kontakty", "avaibleUntil": "Dostępne do", "published": "Opublikowano", "newsSinceLastVisitAmount": "Łącznie {{amount}} nowości od ostatniej wizyty", "noNews": "<PERSON><PERSON>", "createNewTag": "Utwórz nowy tag", "tagName": "Nazwa tagu", "alrTagSaved": "Tag został zapisany.", "alrTagSaveFailed": "Zapisywanie tagu nie powiodło się.", "confirmDeleteTag": "<PERSON>zy na pewno chcesz usunąć tag \"{{tagName}}\"?", "alrPostSaved": "Post został zapisany.", "alrPostSaveFailed": "Zapisywanie postu nie powiodło się.", "alrLoadingTagsFailed": "Wczytywanie tagów nie powiodło się.", "confirmDeletePost": "<PERSON>zy na pewno chcesz usunąć post \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "<PERSON>zy na pewno chcesz usunąć wybrane posty?", "post": "Post", "alrPostLoadFailed": "Wczytywanie postu nie powiodło się.", "alrTagDeleted": "Tag został usunięty.", "alrTagDeleteFailed": "Usuwanie tagu nie powiodło się.", "alrPostDeleted": "Post został usunięty.", "alrPostDeleteFailed": "Usuwanie postu nie powiodło się.", "alrPostsDeleted": "Wybrane posty zostały usunięte.", "alrPostsDeleteFailed": "Usuwanie wybranych postów nie powiodło się.", "alrTempTasksLoadFailed": "Wczytywanie zadań szablonu nie powiodło się.", "rolesRestriction": "Ograniczenia ról", "usersRestriction": "Ograniczenia użytkowników", "orgUnitsRestriction": "Ograniczenia jednostek organizacyjnych", "alrPriorityNewsLoadFailed": "Wczytywanie priorytetowych wiadomości nie powiodło się.", "moreInfo": "Więcej informacji", "tas5Info": "TAS 5.0 jest tutaj ...", "totalNewsAmount": "Łącznie {{amount}} nowości", "alrNewsContainerPostsLoadFailed": "Wczytywanie postów kontenera wiadomości nie powiodło się.", "alrTaskNewsLoadFailed": "Wczytywanie wiadomości dla zadania nie powiodło się.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "Data publikacji musi być przed datą zakończenia wiadomości.", "alrNotificationsNewsLoadFailed": "Wczytywanie wiadomości dla powiadomień nie powiodło się.", "moreNews": "<PERSON><PERSON><PERSON><PERSON><PERSON> wiadom<PERSON>ści", "newsManagementPostDetailConfirmSavingWillSendMail": "Zapisanie postu spowoduje wysłanie e-maila do wszystkich użytkowników, do których post jest przeznaczony. Czy na pewno chcesz zapisać post?", "mailNewsNotification": "Powiadomienie e-mail o nowościach", "mailNewsNotificationInfo": "Użytkownik na bieżąco otrzymuje nowości, które są mu przeznaczone.", "alrRefreshingConfig": "Odświeżanie konfiguracji...", "alrConfigRefreshed": "Konfiguracja została pomyślnie odświeżona.", "alrConfigRefreshFailed": "Odświeżanie konfiguracji nie powiodło się.", "ttRefreshConfig": {"heading": "Przywr<PERSON>ć konfigurację ze wszystkich źródeł", "body": ""}, "getMobileAppTextQr": "Pobierz aplikację mobilną ze sklepu z aplikacjami lub zeskanuj kod QR", "dateStart": "Data rozpoczęcia", "dateEnd": "Data końcowa", "tas_forms_generated": "Liczba automatycznie wygenerowanych formularzy"}