{"1st": "1°", "2nd": "2°", "3rd": "3°", "4th": "4°", "AddToAllTasks": "Aggiungi a tutti i compiti", "OfVariable": "Della variabile", "RemoveFromAllTasks": "Rimuovere da tutti i compiti", "TaskOwnerWhichInVar": "Al proprietario dell'attività, impostato nella variabile", "action": "Azione", "active": "Attivo", "activeShe": "Attivo", "activePl": "Attivo", "activity": "Attività", "activityType": "Tipo di attività", "actualEnd": "Tempo effettivo della fine", "actualSolver": "Proprietario effettivo dell'attività", "actualStart": "Ora effettiva di inizio", "actualTsks": "Compiti attuali", "actualize": "rica<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAttribute": "Aggiungi attributo", "addOrgUnit": "Aggiungere unità org.", "addPlan": "Agg<PERSON><PERSON>i piano", "addPrintTemplate": "Aggiungi modello di stampa", "addRole": "Aggiungi ruolo", "addRule": "Aggiungi regola", "addTemp": "Aggiungi modello", "addTsk": "Aggiungi attività", "addUser": "Aggiungi utente", "addUserSomething": "Assegna un utente alla {{variable}} selezionata:", "addVariable": "Aggiungi variabile", "after": "<PERSON><PERSON>", "afterTermTasks": "I miei compiti hanno superato la data di scadenza", "all": "<PERSON><PERSON>", "allFiles": "Tutti i file", "allMustBeMet": "<PERSON><PERSON> de<PERSON>o es<PERSON>e sodd<PERSON>", "allMyTasks": "<PERSON>tti i miei compiti", "allSubOfPlanGuar": "<PERSON>tti i subordinati del garante del piano", "allTasksWithNoTerm": "Tutti i compiti senza data di scadenza", "allTasksWithTerm": "Tutti i compiti con data di scadenza", "allTemps": "<PERSON><PERSON> i <PERSON>", "allowMultiple": "Consentire la scelta di più opzioni", "allowSelectAll": "Consente di selezionare tutte le opzioni", "allsupOfPlanGuar": "Tutti i superiori del garante del piano", "alrBlockingAction": "C'è un'azione da completare! Attendere un momento...", "alrActionNameNotDefined": "L'azione \"{{actionName}}\" non è definita.", "alrActionNotDefined": "L'azione non è stata definita.", "alrApiUrlMissing": "Fonte dei dati della tabella mancante.", "alrAssigningTsk": "Assegnazione di compiti...", "alrAssignmentFailed": "Salvataggio dell'assegnazione non riuscito", "alrAtrFailed": "Eliminazione dell'attributo non riuscita", "alrAttachDeleteFailed": "Eliminazione del documento non riuscita", "alrAttachDeleted": "Il documento è stato cancellato!", "alrAttachDeleting": "Eliminazione del documento...", "alrAttachDownloadFailed": "Download del documento non riuscito.", "alrAttachDownloaded": "Il documento è stato scaricato!", "alrAttachDownloading": "Scaricare il documento...", "alrAttachMetaFailed": "Il salvataggio dei metadati del documento non è riuscito.", "alrAttachOrNotesCountFailed": "Il conteggio dei documenti o delle note è fallito.", "alrAttachSaveFailed": "Il caricamento del documento non è riuscito.", "alrAttachSaved": "Il documento è stato allegato.", "alrAttachTooBig": "Il documento è troppo grande! Il documento ha superato i {{maxUploadSize}} MB.", "alrAttrDataFailed": "Caricamento dei dati degli attributi non riuscito", "alrAttrFailed": "Caricamento dei dati degli attributi non riuscito", "alrAttrSaveFailed": "Errore durante il salvataggio dell'attributo!", "alrAttrsLoadFailed": "Caricamento degli attributi non riuscito", "alrAttachRestored": "Documento ripristinato.", "alrAttachRestoreFailed": "Il ripristino del documento non è riuscito.", "alrAttachRestoring": "Ripristino...", "alrAuthMethodsFailed": "Caricamento dei metodi di autenticazione non riuscito.", "alrBadLogin": "Nome utente o password errati.", "alrBlockedPopups": "Probabilmente le finestre pop-up sono bloccate.", "alrCaseDataLoadFailed": "Il caricamento dei dati del caso non è riuscito.", "alrCaseDeleteFailed": "L'eliminazione del caso non è riuscita.", "alrCaseDeleted": "Il caso è stato cancellato!", "alrCaseDeleting": "Eliminazione del caso...", "alrCaseNameLoadFailed": "Il caricamento del nome del caso non è riuscito.", "alrCaseNoRights": "Non hai i permessi per visualizzare il caso n. {{id}}.", "alrCaseNotFound": "Caso non trovato - potrebbe essere stato cancellato", "alrCaseOverviewFailed": "Il caricamento dei dati del caso per la lettura (CASE OVERVIEW) non è riuscito.", "alrCaseSuspended": "Il caso è stato sospeso!", "alrCaseVarsLoadFailed": "Il caricamento delle variabili di processo non è riuscito.", "alrCaseWakeUpFailed": "Il risveglio del caso è fallito.", "alrCaseWakedUp": "Il caso è stato svegliato!", "alrCaseWakingUp": "Caso di risveglio...", "alrColsWidthsSettingsFailed": "Il salvataggio dell'impostazione della larghezza delle colonne non è riuscito.", "alrConnToServerFailed": "Connessione al server fallita.", "alrConnectionDataLoadFailed": "Caricamento dei dati di connessione non riuscito.", "alrConnectionDeleteFailed": "Eliminazione della connessione non riuscita.", "alrConnectionDeleted": "La connessione è stata cancellata!", "alrConnectionSaveFailed": "Salvataggio della connessione non riuscito!", "alrContainerNotFound": "Il contenitore non è stato trovato.", "alrCsvDownloaded": "Il file CSV è stato scaricato!", "alrCvNotFound": "La panoramica non è stata trovata.", "alrDashboardSettingsFailed": "Il salvataggio delle impostazioni del dashboard non è riuscito.", "alrDefaultDashboardLoadFailed": "Caricamento del dashboard predefinito non riuscito.", "alrDefaultDashboardSaved": "Il dashboard predefinito è stato salvato!", "alrDeleteFailed": "Si è verificato un errore durante la cancellazione.", "alrDeleted": "Eliminato!", "alrDeleting": "Eliminazione di....", "alrDiagramDataLoadFailed": "Il caricamento dei dati per la creazione del diagramma non è riuscito.", "alrDiagramEditToSave": "Il diagramma non può essere salvato e trasformato in modello: contiene più di un processo! Aggiornare il diagramma in modo che contenga un solo processo o importare un file . bpmn diverso.", "alrDiagramInitFailed": "Inizializzazione del diagramma fallita.", "alrDiagramMissingTaskName": "Inserire i nomi di tutti i compiti.", "alrDiagramErrors": "Il diagramma contiene errori. Correggeteli e provate a salvare di nuovo.", "alrDiagramNotValid": "XML non è valido secondo la specifica ufficiale BPMN 2.0!", "alrDiagramProcessCount": "Il diagramma non può essere salvato e trasformato in un modello: contiene più di un processo!", "alrDiagramSaveFailed": "Si è verificato un errore durante il salvataggio del modello!", "alrDiagramTsksDeleteFailed": "Si è verificato un errore durante l'eliminazione delle attività!", "alrDiagramUnchanged": "Il modello rimane invariato.", "alrDmsColsLoadFailed": "Non è stato possibile caricare le colonne per DMS.", "alrDocsColumnsIdsFailed": "Non è stato possibile caricare gli id delle colonne della tabella documenti.", "alrDocumentAdding": "Salvataggio del documento...", "alrDynListsDataLoadFailed": "Il caricamento dei dati degli elenchi dinamici non è riuscito.", "alrDynTableColsDataFailed": "Non è stato possibile caricare i dati delle colonne della tabella dinamica.", "alrDynTableDataLoadFailed": "Caricamento dati tabella dinamica non riuscito.", "alrDynTableNotFound": "La tabella dinamica non è stata trovata.", "alrDynTablesDataLoadFailed": "Caricamento dati tabelle dinamiche non riuscito.", "alrCalcScriptsDataLoadFailed": "Caricamento degli script di calcolo globale non riuscito.", "alrEditValues": "Si prega di correggere i valori inseriti erroneamente.", "alrEventSaveFailed": "Il salvataggio dell'evento non è riuscito.", "alrEventSaved": "L'evento è stato salvato!", "alrEventSaving": "Salvataggio dell'evento...", "alrEventTriggered": "L'evento è stato lanciato!", "alrExcelDownloaded": "Il file xlsx è stato scaricato!", "alrExportCompleted": "Esportazione completata.", "alrExportFailed": "L'esportazione non è riuscita.", "alrExportPreparing": "Preparazione dell'esportazione...", "alrFailed": "Azione fallita.", "alrFailedCalendarTask": "Il caricamento dell'attività nel calendario non è riuscito.", "alrFailedCreatePrint": "La creazione della stampa non è riuscita.", "alrFailedDLTotalCount": "Non è stato specificato il numero totale nell'elenco dinamico {{label}}, quindi tutti i record sono stati caricati.", "alrFailedData": "Caricamento dei dati non riuscito.", "alrFailedEventStart": "L'evento non è stato avviato.", "alrFailedEventVariables": "Le variabili dell'evento selezionato non sono state caricate.", "alrFailedEvents": "Caricamento degli eventi non riuscito.", "alrFailedFoldersData": "I dati delle cartelle non sono stati caricati.", "alrFailedFormData": "I dati del modulo non sono stati caricati.", "alrFailedInitiatorName": "Il nome dell'iniziatore non è stato caricato.", "alrFailedLabelData": "I dati del componente etichetta non sono stati caricati.", "alrFailedLoad": "Il caricamento dei dati di stampa non è riuscito.", "alrFailedLogicalType": "Il caricamento del tipo logico non è riuscito.", "alrFailedMultiBoxData": "I dati di MultiBox non sono stati caricati.", "alrFailedNewCase": "Si è verificato un errore durante la creazione di un nuovo caso!", "alrFailedNewFolder": "La modifica del nome della cartella non è riuscita.", "alrFailedNoticeData": "Avviso di caricamento dati non riuscito.", "alrFailedOrgUnitUser": "Le unità organizzative dell'utente non sono state caricate.", "alrFailedOverviewData": "Panoramica non caricata.", "alrFailedPlanData": "I dati del piano non sono stati caricati.", "alrFailedPostData": "Invio di dati non riuscito.", "alrFailedPrintData": "Non è possibile caricare i dati di stampa.", "alrFailedRevisionInfo": "Non è stato possibile caricare le informazioni della nuova revisione.", "alrFailedSearchBoxData": "I dati della casella di ricerca non sono stati caricati.", "alrFailedSelectBoxData": "I dati del componente SelectBox non sono stati caricati.", "alrFailedSuggestBoxData": "I dati del suggeritore non sono stati caricati.", "alrFailedTasColors": "I colori TAS non sono stati caricati!", "alrFailedTaskHandOver": "Trasferimento del compito non riuscito.", "alrFailedTemplateProcesses": "I modelli dei casi non sono stati caricati.", "alrFailedVarData": "Il caricamento dei dati della variabile non è riuscito.", "alrFileAdded": "Il file è stato aggiunto!", "alrFileDeleteFailed": "L'eliminazione del file non è riuscita.", "alrFileDonwload": "Download del file...", "alrFileDownloaded": "Il file è stato scaricato!", "alrFileInfoFailed": "Il caricamento delle informazioni del file non è riuscito.", "alrFileMetaSaveFailed": "Non è stato possibile salvare i metadati del file.", "alrFileSavedLikeAttach": "Il file è stato salvato come documento.", "alrFileUploadFailed": "Il caricamento del file non è riuscito.", "alrFillAllRequired": "Per completare il compito, è necessario compilare tutti i dati richiesti!", "alrFillData": "Per assegnare l'incarico, tutti i dati devono essere compilati correttamente!", "alrFillDataInRightFormat": "Si prega di compilare i dati nel formato corretto.", "alrFillDataToCompleteTsk": "Per completare il compito, tutti i dati devono essere compilati correttamente!", "alrFillNameAndPass": "Inserire nome e password.", "alrFillNote": "Compilare il testo della nota.", "alrFillRequiredItems": "Compilare le voci richieste.", "alrFolderDataFailed": "Il caricamento dei dati delle cartelle non è riuscito.", "alrFolderDataLoadFailed": "Il caricamento dei dati della cartella non è riuscito.", "alrFolderFailed": "Il caricamento delle informazioni sulla cartella non è riuscito.", "alrFolderSaveFailed": "Non è stato possibile salvare la cartella!", "alrFoldersLoadFailed": "Non è stato possibile caricare le cartelle.", "alrHelpSettingsSaveFailed": "Non è stato possibile salvare le impostazioni della guida.", "alrHistoricalTskInfoFailed": "Il caricamento delle informazioni storiche dell'attività non è riuscito.", "alrHistoricalVarsSaveFailed": "Non è stato possibile salvare le variabili storiche.", "alrHistoricalVarsSaved": "Le variabili storiche dell'attività sono state salvate.", "alrInvLogginHash": "Accesso non valido.", "alrJsonFailed": "JSON non valido!", "alrLackOfPermsToEdit": "Non hai i permessi di modifica! Il proprietario è", "alrLackOfPermsToSleepCase": "Non avete il permesso di sospendere il caso.", "alrLackOfPermsToWakeUpCase": "Non avete i permessi per risvegliare il caso.", "alrLastHistoricalTskIdFailed": "Id dell'ultima attività storica non caricata.", "alrLoadAttachmentsFailed": "Caricamento dei documenti non riuscito.", "alrLogOutFailed": "Disconnessione non riuscita.", "alrLoginExpired": "La sessione di login è scaduta, si prega di effettuare nuovamente il login.", "alrMappingFailed": "Non è stato possibile salvare la mappatura.", "alrMappingTsksFailed": "Non è stato possibile caricare la mappatura dei compiti.", "alrNewCaseBased": "Nuovo caso creato!", "alrNewFolder": "È stata creata una nuova cartella.", "alrNewFolderFailed": "La creazione di una nuova cartella non è riuscita.", "alrNextTskOpened": "È stata aperta la seguente attività.", "alrNoDataToPrint": "Non sono stati trovati dati per la stampa.", "alrNoteAdded": "La nota è stata aggiunta!", "alrNoteSaveFailed": "Il salvataggio della nota non è riuscito.", "alrNoteSaving": "<PERSON><PERSON><PERSON> la nota...", "alrNotesLoadFailed": "Il caricamento delle note del caso non è riuscito.", "alrOrgUnitDataFailed": "Il caricamento dei dati dell'unità organizzativa non è riuscito.", "alrOrgUnitDeleteFailed": "L'eliminazione dell'unità organizzativa non è riuscita.", "alrOrgUnitDeleted": "L'unità organizzativa è stata cancellata!", "alrOrgUnitDeleting": "Eliminazione dell'unità organizzativa ...", "alrOrgUnitSaveFailed": "Il salvataggio dell'unità organizzativa non è riuscito.", "alrOrgUnitSaved": "L'unità organizzativa è stata salvata!", "alrOrgUnitSaving": "Salvataggio dell'unità organizzativa...", "alrOverviewDataLoadFailed": "Caricamento dei dati della panoramica non riuscito.", "alrOverviewSaveFailed": "Il salvataggio della panoramica è fallito!", "alrOverviewSaveSameNameFailed": "Il nome della panoramica è già utilizzato da voi o da un altro utente; scegliete un altro nome per la panoramica.", "alrGraphSaveSameNameFailed": "Il nome del grafico è già utilizzato dall'utente o da un altro utente; scegliere un altro nome per il grafico.", "alrReportSaveSameNameFailed": "Il nome del report è già stato utilizzato dall'utente o da un altro utente; si prega di scegliere un altro nome per il report.", "alrOverviewsLoadFailed": "Il caricamento delle panoramiche non è riuscito.", "alrPassSaveFailed": "Salvataggio della password non riuscito.", "alrPassSaved": "La password è stata salvata!", "alrPlanReqItems": "Per salvare il piano, compilare le voci richieste.", "alrPlanSaveFailed": "Il salvataggio del piano è fallito.", "alrPlanSaved": "Il piano è stato salvato!", "alrPreparingPrint": "Preparazione della stampa...", "alrPrintDeleteFailed": "L'eliminazione della stampa non è riuscita.", "alrPrintDeleted": "La stampa è stata cancellata!", "alrPrintSaveFailed": "Salvataggio della stampa non riuscito.", "alrPrintSaved": "La stampa è stata salvata!", "alrReadOnlyCaseDataFailed": "Il caricamento dei dati del caso per la lettura non è riuscito.", "alrRecalcFailed": "Errore durante il ricalcolo!", "alrRecalculating": "Ricalcolo...", "alrRestorTemplFailed": "Il ripristino del modello non è riuscito.", "alrRoleDataLoadFailed": "Caricamento dei dati del ruolo non riuscito.", "alrRoleDeleteFailed": "Eliminazione del ruolo non riuscita.", "alrRoleDeleted": "Il ruolo è stato cancellato!", "alrRoleDeleting": "Eliminazione del ruolo...", "alrRoleSaveFailed": "Salvataggio del ruolo non riuscito.", "alrRoleSaved": "Il ruolo è stato salvato!", "alrRoleSaving": "Salvo il ruolo...", "alrRunEvent": "Apro evento...", "alrSaveFailed": "Salvataggio non riuscito.", "alrSaved": "Salvato!", "alrSaving": "Salvo...", "alrSavingBeforeRecalcFailed": "Errore durante il salvataggio prima del ricalcolo!", "alrSavingFailed": "Errore durante il salvataggio!", "alrSavingPlan": "Salvataggio del piano...", "alrSavingPrint": "Salvataggio della stampa...", "alrSearchNoResults": "<PERSON><PERSON>un risultato corrispondente ai parametri di ricerca.", "alrSearchRequestFailed": "Errore durante l'invio della richiesta!", "alrSearching": "Cerco...", "alrSettFailed": "Non è stato possibile salvare le impostazioni.", "alrSettSaved": "Le impostazioni sono state salvate.", "alrSettingsLoadFailed": "I dati delle impostazioni non sono stati caricati.", "alrSleepCaseFailed": "La sospensione del caso è fallita.", "alrStoreNameNotDefined": "Lo Store \"{{storeName}}\" non è definito.", "alrStoreNotDefined": "Lo Store non è stato definito.", "alrSubActionNotDefined": "È necessario definire la sottoazione e il suffisso.", "alrSubStoreNotDefined": "È necessario definire il SubStore e il suffisso.", "alrSuggestBoxDataNotContains": "I dati della casella di suggerimento \"{{label}}\" non contengono \"{{prop}}\"!", "alrSuspendingCase": "Sospensione del caso...", "alrTableDataFailed": "Caricamento dati tabella fallito.", "alrTasNewVersion": "È stata trovata una nuova versione dell'applicazione.", "alrRefresh": "È necessario {{- spanRefresh}} la pagina nel browser.", "alrTasVersionLoadFailed": "Caricamento della versione dell'applicazione fallito!", "alrTaskHandOver": "Trasferimento il compito...", "alrTaskHandedOver": "Il compito è stato affidato all'utente:", "alrTaskNoRights": "Non hai i permessi per visualizzare l'attività n. {{id}}.", "alrTaskNotFound": "L'attività non è stata trovata.", "alrTempDataLoadFailed": "I dati dei modelli non sono stati caricati.", "alrTempHeadersLoadFailed": "Il caricamento delle intestazioni del modello non è riuscito.", "alrTempDeleteFailed": "L'eliminazione del modello non è riuscita.", "alrTempDeleted": "Il template è stato cancellato!", "alrTempFoldersLoadFailed": "Il caricamento delle cartelle dei modelli non è riuscito.", "alrTempNameLoadFailed": "Il nome del modello non è stato caricato.", "alrTempRestored": "Il modello è stato ripristinato con lo stato Sviluppato.", "alrTempSaveFailed": "Salvataggio del modello non riuscito.", "alrTempsLoadFailed": "Caricamento dei modelli non riuscito.", "alrTempVarDataLoadFailed": "Il caricamento dei dati delle variabili del modello non è riuscito.", "alrTempVarSaveFailed": "Salvataggio della variabile non riuscito.", "alrTempVarsLoadFailed": "Il caricamento delle variabili del template non è riuscito.", "alrTotalCountFailed": "Il conteggio dei record totali nella tabella non è riuscito.", "alrTreeDataFailed": "Caricamento del data tree non riuscito.", "alrTskAddFailed": "L'aggiunta di un'attività non è riuscita.", "alrTskAdded": "L'attività è stata aggiunta!", "alrTskAdding": "Aggiunta di attività...", "alrTskAssignFailed": "L'assegnazione del compito non è riuscita.", "alrTskAssigned": "Il compito è stato assegnato.", "alrTskCompleteFailed": "Errore durante il completamento dell'attività.", "alrTskDataLoadFailed": "I dati dell'attività non sono stati caricati.", "alrTskDeleteFailed": "L'eliminazione dell'attività non è riuscita.", "alrTskDeleted": "L'attività è stata cancellata!", "alrTskNameLoadFailed": "Il nome dell'attività non è stato caricato.", "alrTskRecalculated": "Compito r<PERSON>!", "alrTskSaveFailed": "Errore durante il salvataggio dell'attività.", "alrTskSaved": "Compito salvato!", "alrTskSavedAndCompleted": "Attività salvata e completata!", "alrTskScheduleFailed": "La pianificazione delle attività non è riuscita.", "alrTskScheduled": "L'attività è stata pianificata.", "alrTskTakeFailed": "L'acquisizione del compito è fallita.", "alrTskTaken": "Il compito è stato preso in carico.", "alrTskTakign": "Assumendo l'incarico...", "alrTsksMappingFailed": "Il caricamento delle attività di mappatura non è riuscito.", "alrUserDataLoadFailed": "Caricamento dei dati utente non riuscito.", "alrUserDeleteFailed": "L'eliminazione dell'utente non è riuscita.", "alrUserDeleted": "L'utente è stato cancellato!", "alrUserDeleting": "Eliminazione dell'utente...", "alrUserIsNotActive": "L'utente non è attivo.", "alrUserNotLoaded": "Il caricamento dell'utente non è riuscito.", "alrUserParamsLoadFailed": "Il caricamento dei parametri utente non è riuscito.", "alrUserSaveFailed": "Salvataggio dell'utente non riuscito.", "alrUserSaved": "L'utente è stato salvato!", "alrUserSaving": "Salvataggio dell'utente...", "alrUserStatusChangeFailed": "Modifica dello stato dell'utente non riuscita.", "alrUserStatusChanged": "Lo stato dell'utente è stato modificato!", "alrUserStatusChanging": "Modifica dello stato dell'utente...", "alrVarDeleteFailed": "L'eliminazione della variabile non è riuscita.", "alrVarDeleted": "La variabile è stata cancellata!", "alrVarSaveFailed": "Salvataggio della variabile non riuscito.", "alrVarSaved": "La variabile è stata salvata.", "alrVarSaving": "Salvataggio variabile...", "alrVarsForModalFilterFailed": "Caricamento delle variabili del filtro modale non riuscito.", "alrVarsLoadFailed": "Non è stato possibile caricare le variabili.", "alrVarsOrderLoadFailed": "Il caricamento dell'ordine delle variabili non è riuscito.", "alrVarsOrderSaveFailed": "Il salvataggio dell'ordine delle variabili non è riuscito.", "alrViceDeleted": "Il delegato è stato cancellato.", "alrViceFailed": "Il delegato non ha avuto successo.", "alrViceNotFound": "Il delegato non è stato trovato!", "alrViceSaveFailed": "Il delegato non è stato salvato.", "alrViceSaved": "Il delegato è stata salvato!", "alrViceSaving": "Salvataggio delegato...", "always": "Sempre", "annually": "Annualmente", "assHierarchy": "Rapporto con la persona di riferimento", "assHierarchyAncestors": "<PERSON>tti i superiori della persona di riferimento", "assHierarchyChildren": "I diretti subordinati della persona di riferimento", "assHierarchyDescendants": "<PERSON>tti i subordinati della persona di riferimento", "assHierarchyGuarantor": "Unica persona di riferimento", "assHierarchyParent": "Superiore diretto della persona di riferimento", "assHierarchySiblings": "Colleghi della persona di riferimento", "assMethodAutomatic": "Dal computer", "assMethodLastSolver": "Al proprietario dell'ultima attività", "assMethodLastSolverChoice": "Proprietario dell'attività selezionata dal proprietario dell'attività più recente", "assMethodLeast": "Proprietario dell'attività con il minor numero di attività", "assMethodPull": "A nessuno, il compito sarà offerto", "assMethodSelect": "Proprietari dell'attività selezionati dal supervisore dell'attività", "assMethodVariable": "Proprietario dell'attività, dalla variabile", "assessmentOfConds": "Valutazione delle condizioni", "assign": "Assegnazione", "assignAttrs": "Assegnazione degli attributi", "assignAttrsLogType": "Assegnazione di attributi al tipo logico di documento", "assigned": "<PERSON><PERSON><PERSON><PERSON>", "assigningRoles": "Assegnazione dei ruoli", "assignments": "<PERSON><PERSON><PERSON>", "attachments": "Documenti", "attachmentsList": "Elenco dei documenti", "attribute": "Attributo", "attributeNew": "Attributo - Nuovo", "availableVars": "Variabili disponibili", "body": "corpo", "borders": "<PERSON><PERSON><PERSON>", "byFolders": "Per cartelle", "byOrganization": "Per organizzazione", "byRole": "Per ruolo", "calculation": "<PERSON><PERSON><PERSON>", "calculations": "Cal<PERSON>li", "calendar": "Calendario", "carriedIfNoOther": "<PERSON><PERSON><PERSON><PERSON> effettuato se non ci sono altri", "case": "Caso", "caseCreation": "Creazione di un caso", "caseGraph": "Diagramma dell'istanza", "caseNoEvents": "Il caso non contiene eventi.", "caseNum": "Caso n.", "caseOwner": "Proprietario del caso", "caseStatus": "Stato del caso", "caseVar": "caso", "cases": "Casi", "casesWithProblem": "I miei casi con un problema", "category": "Categoria", "changeTaskSolver": "Cambiare il proprietario dell'attività", "changedBy": "Modificato da", "changedWhen": "Modificato (quando)", "checkbox": "Casella di controllo", "checkboxList": "Elenco di caselle di controllo", "choosePrint": "<PERSON><PERSON>", "chooseUserToAssignTsk": "Scegliere l'utente da assegnare all'attività", "choosenAttrs": "Attributi scelti", "city": "Città", "class": "Classe", "clickToClose": "<PERSON><PERSON>re facendo clic su", "clickToRefresh": "Fare clic per aggiornare la pagina nel browser", "clickToRepeat": "Ripetere l'azione facendo clic su", "clientLanguage": "Lingua del cliente", "cloneRow": "Linea duplicata", "close": "<PERSON><PERSON><PERSON>", "closeAll": "<PERSON><PERSON><PERSON> tutti", "coWorkersOfPlanGuar": "Collaboratori del garante del piano", "color": "Colore", "colors": "Colori", "column": "<PERSON>onna", "columnName": "Nome della colonna", "comment": "Commento", "complete": "Completo", "completion": "Completamento", "componentDescription": "Descrizione del componente", "condition": "Condizione", "conditions": "Le condizioni", "confirmAttachDeletion": "Volete davvero cancellare il documento?", "confirmDeleteDialog": "Si vuole davvero cancellare la {{variable}}?", "confirmDialogEventSave": "Per cambiare, è necessario salvare l'evento. Volete salvarlo?", "confirmResetDashboard": "Volete davvero resettare il dashboard?", "confirmSaveChanges": "<PERSON><PERSON><PERSON> le modifiche?", "confirmSaveDiagramChanges": "<PERSON><PERSON><PERSON> le modifiche nel diagramma?", "confirmSaveTaskChanges": "<PERSON><PERSON><PERSON> le modifiche nell'attività?", "confirmRestoreDialog": "Si vuole davvero ripristinare la {{variable}}?", "confirmSaveNote": "Volete salvare una nota?", "confirmSleepCase": "Vuole davvero sospendere il caso?", "confirmTakeoverTsk": "Volete davvero assumervi questo compito?", "confirmWakeUpCase": "Vuole davvero de-sospendere il caso?", "connection": "Collegamenti", "connectionFailed": "Connessione al server fallita.", "connectionVar": "link", "constant": "Costante", "contact": "Contat<PERSON>", "contactTaskOwner": "Contattare il proprietario dell'attività", "containerSettings": "Impostazioni del contenitore", "contains": "contiene", "contents": "Contenuti", "continueSolving": "Continua in soluzione", "copied": "Copiato!", "copy": "Copia", "copyShortcut": "Premere Ctrl+C", "copyToClipboard": "Copia negli appunti", "createForm": "Creare un modulo", "csv": "csv", "csvFile": "File CSV", "customPrint": "Stampa personalizzata", "daily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashCvNoOverview": "Non è stata selezionata alcuna panoramica: si sceglie nelle impostazioni del contenitore.", "dashCvNoRights": "Non hai i permessi per visualizzare la panoramica, contatta l'amministratore.", "dashFavNoShortcut": "Nessun delegato selezionato: sceglietelo nelle impostazioni del contenitore.", "dashboard": "Dashboard", "date": "Data", "dateList": "LOV di date", "day": "<PERSON>ior<PERSON>", "dayE": "<PERSON><PERSON><PERSON>", "daysDHM": "Giorni: (gg:hh:mm)", "defChangeVarInfluence": "Questa modifica della definizione della variabile si propagherà ai casi già creati.", "defChangeInfluence": "Questa modifica della definizione si propagherà ai casi già creati.", "defaultCaseName": "Nome del caso predefinito", "defaultLbl": "Predefinito {{label}}", "defaultLblShe": "Predefinito {{label}}", "defaultLblIt": "Predefinito {{label}}", "defaultPrintProcess": "Predefinito - processo", "defaultPrintTask": "Predefinito - attività", "defaultValue": "<PERSON><PERSON> predefinito", "delUser": "Eliminare l'utente", "delete": "Cancellare", "deleteCol": "Cancellare la colonna", "deleteRow": "Cancellare la linea", "deleteSelected": "Cancellare la voce selezionata", "deleted": "Cancellato", "deletedOn": "Cancellato", "deletedShe": "Cancellato", "description": "Descrizione", "deselect": "Deselezionare", "detail": "Dettaglio", "developed": "In fase di sviluppo", "dial": "Quadrante", "dic": "IVA", "directSubOfPlanGuar": "Subordinato diretto del garante del piano", "directSupOfPlanGuar": "Superiore diretto del garante del piano", "disableFilter": "Disattivare il filtro", "dmsAssignAttrs": "Assegnazione dell'attributo DMS", "dmsAttribute": "Attributo DMS", "dmsAttributes": "Attributi del DMS", "dmsColumns": "DMS - colonne", "dmsVisNull": "Solo in questo processo", "dmsVisSub": "<PERSON>ei sottoprocessi", "dmsVisSup": "Nel processo parente", "dmsVisSupSub": "Nei processi parenti e nei sottoprocessi", "dmsVisibility": "I documenti saranno visionati", "doNotShowVariablesWith_": "I nomi delle variabili che iniziano con `_` non saranno mostrati agli utenti.", "document": "Documento", "documentVar": "documento", "documents": "Documenti", "doesNotContain": "non contiene", "done": "<PERSON><PERSON>", "download": "Download", "dragAddFiles": "Aggiungere i file trascinandoli o facendo clic su {{- here }} per selezionarli.", "dragAddFile": "Aggiungere un file trascinandolo o facendo clic su {{- here }} per selezionarlo.", "here": "qui", "dropContainer": "Lasciare il contenitore", "dropzoneTip": "Lasciate i file qui", "dropZoneUserPhoto": "<PERSON><PERSON><PERSON> qui un'immagine o fare clic per selezionare un file da caricare.", "dueDate": "Data di scadenza", "duty": "<PERSON><PERSON>", "dynList": "Elenco dinamico", "dynRowsDefinition": "Definizione di tabelle e colonne", "dynTableName": " Nome dinamico della tabella", "dynTable": "<PERSON><PERSON> din<PERSON>", "dynTables": "<PERSON><PERSON><PERSON>", "dynamicList": "Elenco dinamico", "dynamicRows": "<PERSON><PERSON><PERSON> din<PERSON>", "dynamicTable": "<PERSON><PERSON> din<PERSON>", "edit": "Modifica", "editAttribute": "Modifica attributo", "editOrgUnit": "Modificare org.unit", "editRole": "Modifica ruolo", "editRule": "Modifica regola", "editUser": "Modifica utente", "editor": "Editore", "email": "E-mail", "emailsQueue": "Coda di posta elettronica", "empty": "<PERSON><PERSON><PERSON>", "end": "Fine", "error": "Errore", "errored": "<PERSON><PERSON><PERSON>", "error404": "Errore 404 - pagina non trovata!", "event": "Evento", "events": "Eventi", "eventsRun": "Avvia evento", "every": {"masc": "<PERSON>gni", "neutral": "<PERSON>gni", "repeat": "<PERSON>gni"}, "everyWorkDay": "Ogni giorno lavorativo", "excel": "Excel", "favourites": "I preferiti", "fax": "Fax", "file": "File", "fileLogicalType": "Tipo di file logico", "fileName": "Nome del file", "filePlacement": "Inserimento nella cartella", "files": "File", "filter": "filtro", "filterFrom": "<PERSON><PERSON><PERSON> <PERSON>", "filterTitle": "Filtro", "filtrate": "Filtro", "finishTask": "Terminare l'attività", "finished": "<PERSON><PERSON>", "finishedBy": "<PERSON><PERSON> da", "finishedOn": "<PERSON><PERSON>", "first": "Primo", "firstLeft": "Primo a sinistra", "firstName": "Nome", "firstRight ": "Primo a destra", "firstRowColumnsName": "La prima riga contiene i nomi delle colonne", "folder": "<PERSON><PERSON><PERSON>", "folder-": "Cartella -", "folderExecRightsText": "Assegnare i ruoli che potranno avviare i casi nella cartella", "folderExecRightsTextOS": "Assegnare le unità organizzative che potranno avviare i casi in cartella", "folderName": "Nome della cartella", "font": "<PERSON><PERSON><PERSON>", "fontMainHeader": "Font dell'intestazione principale", "form": "Forma", "fourth": "Quarto", "freeTsk": "<PERSON><PERSON><PERSON> gratuito", "fri": "<PERSON>ener<PERSON><PERSON>", "from": "Da", "fsDescription": "descrizione", "fsName": "nome", "fsTooltip": "tooltip", "fullName": "Nome e cognome", "fullScreen": "Schermo intero", "getTotalCount": "Conteggio totale", "graph": "Grafico", "handExecutionTaskListEmpty": "Scegliere l'evento", "handOver": "Consegnare", "handOverToUser": "Consegna all'utente", "handover": "Passaggio di consegne", "headerDashboard": "Intestazione del dashboard", "help": "<PERSON><PERSON>", "hideLogout": "Nascondere il logout", "hideNewProcess": "Nascondi 'Nuovo caso'", "hideProcs": "Nascondi casi", "hideTasks": "Nascondere le attività", "historicalValues": "Valori storici", "currentValues": "Valori attuali", "history": "La storia", "home": "Home", "html": "HTML", "ic": "ID azienda", "id": "ID", "inCasesNames": "Nei nomi dei casi", "inTasksNames": "Nei nomi dei compiti", "inDevelopment": "In fase di sviluppo", "inEvery": "in ogni", "inFiles": "nei files", "initiator": "Iniziatore", "inTasks": "In compiti", "inactive": "Inattivo", "inactiveShe": "Inattivo", "incidences": "eventi", "inclusion": "<PERSON><PERSON><PERSON>", "info": "Info", "inputParams": "Inserire parametri", "insert": "Inserire", "insertAttachTip": "Trascinare e rilasciare per inserire un documento", "insertVar": "Inserire la variabile", "insertSnippet": "Inserire lo snippet", "snippet": "Codificare lo snippet", "insertedBy": "Carica<PERSON> da", "insertedOn": "<PERSON><PERSON><PERSON><PERSON>", "insteadOf": "invece", "instructions": "Istruzioni", "invitation": "Invito", "isEmail": "Non è un'e-mail valida", "isEmpty": "è vuoto", "isExisty": "Non è valido", "isManagerOrgUnit": "Il manager è un'unità organizzativa", "isNotEmpty": "non è vuoto", "isRequired": "Questo campo è obbligatorio", "justSave": "Solo salvare", "keepGlobalOrder": "Mantenere l'ordine globale", "key": "Chiave", "last": "ultimo", "lastName": "Cognome", "lastOwnerOfTask": "Ultimo proprietario dell'attività", "licenceKey": "Chiave di licenza", "link": "Collegamento", "linkConditions": "Condizioni di collegamento", "list": "Elenco", "listName": "nome dell'elenco", "listOfValues": "Elenco dei valori", "listValue": "valore dell'elenco", "loading": "Caricamento...", "location": "Posizione", "locked": "Bloccato", "logIn": "Accedi", "logOut": "<PERSON>nne<PERSON><PERSON>", "logicalType": "Tipo logico", "loginError": "Accesso non valido.", "loginTimeout": "Timeout di sessione (sec.)", "longText": "<PERSON>o lungo", "mailEscalation": "E-mail con una panoramica dei compiti assegnati", "mailProcEscalation": "E-mail con una panoramica dei casi in corso", "mailPromptly": "Notifica via e-mail di un nuovo compito", "mailPull": "Notifica via e-mail dell'attività da estrarre", "mailTotal": "E-mail di riepilogo con panoramica delle attività", "mainButton": "Pulsante principale", "mainColor": "Colore principale", "mainHeader": "Intestazione principale", "mainLanguage": "Lingua principale", "manager": "Responsabile", "managerOfOrgUnit": "Responsabile dell'unità organizzativa", "mandatory": "Obbligatorio", "manualStartEvent": "Avvio manuale dell'evento", "mapping": "Mappatura", "mappingSubProcessVars": "Mappatura delle variabili di sottoprocesso", "markAll": "<PERSON>tras<PERSON><PERSON><PERSON> tutti", "menu": "<PERSON><PERSON>", "mine": "La mia", "mobilePhone": "Telefono cellulare", "mon": "Lunedì", "month1": "Gennaio", "month10": "Ottobre", "month11": "Novembre", "month12": "Dicembre", "month2": "<PERSON><PERSON><PERSON>", "month3": "<PERSON><PERSON>", "month4": "<PERSON>e", "month5": "Maggio", "month6": "<PERSON><PERSON><PERSON>", "month7": "<PERSON><PERSON><PERSON>", "month8": "Agosto", "month9": "Settembre", "monthI": "mese", "monthly": "<PERSON><PERSON><PERSON>", "months": "mesi", "more": "Di più", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingolo", "multiBoxTriple": "MultiBoxTriplo", "multiInstance": "Multi-istanza", "myUnfinishedTasks": "I miei compiti incompiuti", "name": "Nome", "nested": "<PERSON><PERSON><PERSON>", "never": "<PERSON>", "new": "Nuovo", "newCase": "Nuovo caso", "newFolder": "Cartella - Nuovo", "newForm": "Nuovo modulo", "newIt": "Nuovo", "newName": "Nuovo nome", "newShe": "Nuovo", "newSolver": "Nuovo proprietario dell'attività", "no": "No", "noAttach": "<PERSON><PERSON><PERSON> documento (Fare clic per aggiungere)", "clickToAddAttach": "Fare clic per aggiungere", "noName": "Senza nome", "noOneBeOffered": "nessuno, il compito sarà offerto a un gruppo ristretto di utenti", "noPageRights": "Non hai i permessi per visualizzare questa pagina.", "node": "Nodo", "notFound": "Non trovato", "notMatch": "Non corrisponde", "notNumber": "Non un numero", "notIntNumber": "Non è un numero intero", "notSent": "Non inviato", "notValid": "Non valido", "notes": "Note", "notesOnContacts": "Note sui contatti", "notice": "Avviso", "notification": "Notifica", "nrOfItems": "Numero di articoli", "number": "Numero", "numberList": "LOV di numeri", "ok": "OK", "oneMustBeMet": "Almeno uno deve essere soddisfatto", "onlyOrgUnit": "Solo unità organizzativa", "onlyPlanGuarantor": "Solo il garante del piano", "openAll": "<PERSON><PERSON> tutti", "operating": "Attivo", "order": "Ordine", "orderByColumn": "Ordine per colonna", "orgName": "Nome del soggetto", "orgStructure": "Struttura org.", "orgUnit": "unità org.", "orgUnitE": "unità org.", "orgUnitName": "Nome dell'unità organizzativa", "orgUnitShe": "Unità organizzativa", "orgUnits": "Unità organizzative", "organization": "Organizzazione", "overview": "Panoramica", "overviewMapping": "Mappatura generale", "overviewNew": "Panoramica - nuovo", "overviewSetSharing": "Impostare la condivisione della panoramica per ogni gruppo di utenti", "overviews": "Panoramica", "owner": "Proprietario", "ownerWithLeastTasks": "Proprietario dell'attività con il minor numero di attività", "pageNotFound": "Pagina non trovata", "parentFolder": "Cartella parente", "parentUnit": "Unità parente", "participants": "Partecipanti", "password": "Password", "passwordChallenge": "Notifica", "passwordChallengeText": "Vuole davvero notificare tutti gli utenti di cambiare la password?", "passwordChange": "Modifica della password", "passwordCheck": "Password (controllare)", "passwordNew": "Nuova password", "passwordNewCheck": "Nuova password (controllare)", "paused": "Inattivo", "personInOrgStr": "<PERSON><PERSON><PERSON><PERSON> dalla persona nella struttura organizzativa", "phone": "Telefono", "photo": "Foto", "plan": "piano", "planGuarantor": "<PERSON><PERSON><PERSON> del piano", "planTitle": "Piano", "plans": "Pianificazione", "plnOffType": "Ripetere", "plnOrgUnit": "Unità organizzativa", "plnTProc": "<PERSON><PERSON> di caso", "plnUser": "Sponsor del piano", "plnUsersSelect": "Condizioni restrittive per la selezione di uno o più Iniziatori", "prependTsk": "Attività di pre-posticipazione", "prependedTsk": "Compito preposto", "primaryKey": "Chiave primaria", "print": "Stampa", "printTemplate": "<PERSON><PERSON>", "printType": "<PERSON><PERSON><PERSON> di stampa", "printer": "Stampa - HTML", "priority": "Priorità", "procDescription": "Descrizione del processo", "procDueDateFinish": "Data di scadenza per la conclusione del caso", "procName": "Nome del caso", "procOwner": "Proprietario del processo", "procSummary": "<PERSON><PERSON><PERSON> r<PERSON>i", "process": "Processo", "processName": "Nome del processo", "property": "Proprietà", "quickFilter": "Filtro veloce", "radioButtonList": "Elenco di pulsanti", "reEvaluates": "<PERSON><PERSON><PERSON><PERSON>", "recalc": "<PERSON><PERSON><PERSON><PERSON>", "recipient": "Destinatario", "recipientsId": "ID del destinatario", "records": "Registrazioni", "referenceUser": "Persona di riferimento", "refresh": "Aggiornare", "registered": "Registrato", "relatToPlanSponsor": "Rapporto con lo sponsor del piano", "remove": "Rimuovere", "removeVice": "Rimuovere il delegato", "renameCols": "Rino<PERSON>re le colonne", "repeatLogin": "Ripetere il login o scegliere un altro tipo di autenticazione.", "repeatOrReport": "Riprovare più tardi o contattare l'amministratore.", "repetition": "Ripetizione", "required": "Obbligatorio", "reset": "Reset", "restrictTaskOwners": "Restrizioni per i proprietari delle attività", "restrictUsers": "<PERSON><PERSON>re gli utenti", "returnSubProcessVars": "Restituzione di variabili di sottoprocesso", "revision": "Revisione", "right": "<PERSON><PERSON><PERSON>", "rightOrDuty": "Diritto / Dovere", "role": "ruolo", "roleName": "Nome del ruolo", "roleSg": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>", "row": "riga", "rule": "Regola", "ruleCSVFile": "Nome del file CSV", "ruleCSVHeader": "La prima riga del file CSV è un'intestazione", "ruleCSVMask": "Maschera del nome del file CSV", "ruleCSVSeparator": "Il separatore di colonna", "ruleNew": "Regola - Nuovo", "ruleParamsMap": "Mappatura delle variabili", "ruleProcOwnCSV": "De<PERSON>ito nella mappatura", "ruleTypeCSVExpProcs": "CSV Esportazione di tutti i casi Modello", "ruleTypeCSVMrgProcs": "In base al CSV, eseguire i casi e aggiornare le variabili dei casi.", "ruleTypeCSVRunProcs": "Secondo il CSV, eseguire i casi", "ruleTypeCSVUpdProc": "Secondo il CSV aggiornare le variabili del caso", "ruleTypeCSVUpdProcs": "In base al CSV aggiornare le variabili dei casi", "ruleTypeCSVUpdateList": "Aggiornare l'elenco dinamico in base a CSV", "ruleTypeReturn": "Risposta all'evento", "ruleTypeUpdateListOfProcesses": "Aggiornare l'elenco dinamico dei processi", "rules": "Regole", "run": "<PERSON><PERSON><PERSON>", "runProcess": "<PERSON><PERSON>vio del processo", "running": "In corso", "sat": "Sabato", "save": "<PERSON><PERSON>", "saveAsAttachment": "<PERSON><PERSON><PERSON> la stampa come documento nel caso", "scheduling": "Programmazione", "scheme": "Identità visiva", "script": "<PERSON><PERSON><PERSON>", "scripts": "<PERSON><PERSON><PERSON>", "search": "Ricerca", "searchResult": "Risultato della ricerca", "second": "Secondo", "secondLeft": "Secondo a sinistra", "secondRight": "Secondo a destra", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "Ai proprietari di attività selezionati automaticamente dal computer", "selectedByTaskSupervisor": "Ai proprietari dei compiti selezionati dal supervisore", "selectedPrint": "stampa selezionata", "selectedUser": "Utente selezionato", "send": "Inviare", "sendingFailed": "Fallito", "sendOn": "Tempo di invio", "sendTestMail": "E-mail di prova", "sequence": "Sequenza", "setDefault": "Imposta come predefinito", "setVice": "Impostare il delegato", "setViceAttachmentsNotes": "Diritti di caricare documenti e note", "settings": "Impostazioni", "shortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showAttachmentsClick": "Facendo clic su di esso si visualizzeranno i documenti", "showCommentCol": "Mostra la colonna dei commenti", "skype": "Skype", "solve": "Risolvere il compito", "solvedBy": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "solver": "Proprietario dell'attività", "sort": "Ordinare", "sortByColumn": "Ordinare per colonna", "sorting": "<PERSON><PERSON><PERSON>", "sourceTask": "Attività di fonte", "sourceVar": "Variabile di fonte", "start": "<PERSON><PERSON><PERSON>", "startDate": "Data di inizio", "startCalDate": "Data di inizio", "endCalDate": "Data di conclusione", "state": "Stato", "stateAddress": "Stato", "status": "Stato", "street": "<PERSON><PERSON>izzo stradale", "subProcess": "Sottoprocesso", "subject": "<PERSON><PERSON><PERSON>", "substitute": "Sostituto", "sun": "Domenica", "superior": "Superiore", "supervis": "Supervisore", "supervisor": "Supervisore dei compiti", "suspend": "<PERSON><PERSON><PERSON><PERSON>", "suspended": "Sosp<PERSON>o", "suspendedx": "Sosp<PERSON>o", "tTaskAgain": "Comportamento di attivazione ripetuto", "tTaskAutoCompleteCaption": "Il compito verrà eseguito automaticamente se", "tTaskCompletionCOA": "tutte le condizioni sono soddisfatte contemporaneamente", "tTaskCompletionCOO": "almeno una condizione è soddisfatta", "tTaskDueOffsetNone": "immediatamente", "tTaskDueOffsetPO": "inserito dal supervisore", "tTaskDueOffsetPS": "entro pochi giorni dall'inizio del caso", "tTaskDueOffsetTS": "entro pochi giorni dal possibile inizio dell'attività", "tTaskDueOffsetVC": "di variabili in modo continuo", "tTaskDueOffsetVO": "delle variabili all'avvio", "tTaskInvClassConf": "Segret<PERSON>", "tTaskInvClassPriv": "Privato", "tTaskInvClassPubl": "Pubblico", "tTaskInvPriority1": "1-pi<PERSON> alto", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-p<PERSON><PERSON> basso", "tTaskInvokeEventB": "in background", "tTaskInvokeEventI": "immediatamente", "tTaskReferenceUserLastSolver": "L'ultimo proprietario dell'attività", "tTaskReferenceUserMan": "Responsabile dell'unità organizzativa xy", "tTaskReferenceUserUser": "Utente xy", "tTaskRunOnlyOnce": "Eseguire una sola volta", "tTaskSufficientEnd": "Il completamento termina l'intero caso", "tabName": "Nome della scheda", "table": "<PERSON><PERSON>", "takeOnlyOrder": "Accetta solo l'ordine", "takeover": "<PERSON><PERSON><PERSON><PERSON>", "targetTask": "Compito target", "targetVar": "Variabile target", "taskAutomatic": "stato automatico", "taskEmailNotification": "Notifica via e-mail", "taskEvent": "gestisce l'evento", "taskEventWait": "attende l'evento", "taskOwner": "Proprietario dell'attività", "taskSolverAssign": "per l'assegnazione al proprietario del compito", "taskStart": "<PERSON><PERSON><PERSON>", "taskStatus": "Stato", "taskStatusA": "Attivo", "taskStatusAP": "Sottoprocesso attivo", "taskStatusAS": "Sottoprocesso di sonno", "taskStatusD": "Completato", "taskStatusL": "In attesa", "taskStatusLEdit": "Impossibile modificare un'attività in sospeso", "taskStatusN": "Nuovo", "taskStatusP": "Pianificato", "taskStatusS": "Sosp<PERSON>o", "taskStatusT": "Da assegnare", "taskStatusW": "Per l'assegnazione", "taskStatusWT": "Per la programmazione", "taskSubprocess": "implementato dal sottoprocesso", "taskTabVariables": "Variabili assegnate", "taskType": "Tipo di compito", "taskWillBeAssigned": "Il compito sarà assegnato", "tasks": "Compiti", "tasksToPull": "Compiti da prendere", "taskstatusAD": "Attivo e finito", "tempId": "ID modello", "tempVar": "modello", "template": "<PERSON><PERSON>", "templateDeleted": "Eliminato", "templateStatus": "Stato del modello", "templates": "<PERSON><PERSON>", "templatesFolder": "Modello - cartella", "testForm": "Mo<PERSON>lo di prova", "tested": "<PERSON>ato", "text": "<PERSON><PERSON>", "textList": "LOV di testi", "textMultipleLines": "Testo con più righe", "textSuggest": "<PERSON><PERSON><PERSON><PERSON>", "third": "<PERSON><PERSON><PERSON>", "thirdCenter": "Terzo centro", "thu": "<PERSON><PERSON><PERSON><PERSON>", "thumbnail": "Miniatura", "title": "<PERSON><PERSON>", "to": "A", "toHide": "Nascondere", "toInclusive": "A (compreso)", "toPull": "Compiti da prendere", "tooltip": "Suggerimento per gli strumenti", "total": "totale", "tprocName": "Modello di processo", "tsk": "Compito", "tskAssignDues": "Impostate delle limitazioni di tempo per questa attività", "tskName": "Nome del compito", "tskNum": "Numero del compito", "tskSolver": "Proprietario dell'attività", "tskTemplate": "<PERSON>lo di compito", "tskVar": "compito", "tsksDone": "<PERSON><PERSON>", "tsksSolvers": "Proprietari dei compiti", "ttAdd": {"heading": "Add", "body": "Permette di aggiungere un nuovo elemento o nuovi parametri non ancora definiti"}, "ttAddActivity": {"heading": "Add", "body": ""}, "ttAddAttach": {"heading": "Aggiungi documento", "body": "Permette di aggiungere un nuovo documento"}, "ttAddAttribute": {"heading": "Add", "body": ""}, "ttAddContainer": {"heading": "Aggiungi contenitore", "body": "Aggiunge contenitore con contenuto selezionato"}, "ttAddFile": {"heading": "Add", "body": ""}, "ttAddStructure": {"heading": "Aggiungi elemento alla struttura organizzativa", "body": "Permette di aggiungere un nuovo elemento della struttura organizzativa o nuovi parametri non ancora definiti"}, "ttAddTemp": {"heading": "Aggiungi nuovo modello", "body": "Creazione di nuovi modelli di casi. Il proprietario del modello sarà l'utente attualmente connesso. Al modello viene automaticamente assegnato lo stato \"in fase di sviluppo\""}, "ttAddTsk": {"heading": "Aggiungi nuova attività", "body": "Creazione di una nuova attività all'interno del modello di processo. I parametri dell'attività possono essere specificati a seconda del tipo di attività. I collegamenti ad altre attività possono essere aggiu"}, "ttAddTskGraph": {"heading": "Aggiungi nuova attività", "body": "Creazione di una nuova attività nel modello di processo. I parametri dell'attività possono essere specificati a seconda del tipo di attività. I collegamenti ad altre attività possono essere aggiunti o modificati nelle schede Grafico o Collegamento"}, "ttAddUser": {"heading": "Aggiungi nuovo utente", "body": "Aggiungi nuovo utente. Ogni utente deve avere un nome univoco. È possibile impostare le informazioni di base degli utenti, nonché la loro assegnazione alla struttura organizzativa e l'attribuzione dei ruoli. Ai nuovi utenti viene automaticamente assegnato lo stato di blocco"}, "ttAddVar": {"heading": "Aggiungi nuova variabile", "body": "Creazione di una nuova variabile all'interno del modello di caso. Ogni variabile contiene informazioni che possono essere elaborate dai proprietari delle attività del caso. È possibile specificare il nome, il tipo e i valori predefiniti della variabile"}, "ttAddVice": {"heading": "Aggiungi delegato", "body": ""}, "ttAssignAttribute": {"heading": "Attributi assegnati al tipo di documento logico", "body": ""}, "ttAssignTsk": {"heading": "Assign", "body": "Consente di assegnare un'attività a un proprietario specifico o di aggiungere un elemento a una struttura definita"}, "ttCases": {"heading": "Cases", "body": ""}, "ttOverviews": {"heading": "Overviews", "body": ""}, "ttChangePass": {"heading": "Password change", "body": "Modifica delle password degli utenti gestiti direttamente nell'ambiente dell'applicazione.  Se gli utenti sono gestiti da un servizio esterno (LDAP), la password deve essere gestita in tale sede."}, "ttClose": {"heading": "Close", "body": "La finestra verrà chiusa senza salvare le modifiche"}, "ttCloseTemp": {"heading": "Close", "body": "La finestra con la definizione del modello verrà chiusa"}, "ttCompleteTsk": {"heading": "<PERSON><PERSON><PERSON>", "body": "Conferma che l'attività è stata completata e la invia per un'ulteriore elaborazione come predefinito"}, "ttContact": {"heading": "Contact", "body": "Visualizza i contatti del supervisore dell'attività"}, "ttContainerSettings": {"heading": "Impostazioni", "body": "Permette di modificare le impostazioni del contenitore indicato"}, "ttCopyHdr": {"heading": "Copia intestazione", "body": "Creazione di una copia dell'intestazione selezionata. La selezione dell'intestazione viene effettuata facendo clic nella tabella delle intestazioni del modello."}, "ttCopyTemp": {"heading": "Copy template", "body": "Creazione di una copia del modello selezionato. La selezione del modello viene effettuata facendo clic nella tabella dei modelli di processo."}, "ttCopyVar": {"heading": "Copia di una variabile", "body": "Copia della definizione della variabile selezionata e salvataggio della variabile con un nuovo nome. Le variabili si selezionano facendo clic nella tabella delle variabili."}, "ttDel": {"heading": "Delete", "body": "Elimina l'elemento selezionato"}, "ttDelAttach": {"heading": "Elimina documento", "body": "Elimina il documento selezionato"}, "ttDelConnection": {"heading": "Elimina collegamento", "body": "Elimina il collegamento selezionato tra due attività del caso. L'eliminazione deve essere confermata. L'eliminazione viene eseguita per il collegamento selezionato. Selezionare il collegamento facendo clic su di esso nella tabella dei collegamenti"}, "ttDelFolder": {"heading": "Eliminazione cartella", "body": "Eliminazione della cartella selezionata."}, "ttDelOverview": {"heading": "Elimina panoramica", "body": "Elimina la panoramica selezionata"}, "ttDelTemp": {"heading": "Elimina modello", "body": "Assegna lo stato di eliminazione a un modello. Solo quando la richiesta di eliminazione viene ripetuta, il modello viene fisicamente rimosso. L'azione viene applicata al modello selezionato. Selezionare il modello facendo clic su di esso nella tabella dei modelli di casi."}, "ttDelTsk": {"heading": "Eliminazione attività", "body": "Rimozione dell'attività selezionata. L'eliminazione deve essere confermata. Insieme all'attività, verranno rimossi tutti i collegamenti ad altre attività del modello di processo. Selezionare l'attività facendo"}, "ttDelTskOrConnection": {"heading": "Elimina attività o collegamento", "body": "Rimozione di un'attività selezionata o di un collegamento selezionato tra due attività di processo. Questa azione deve essere confermata. I collegamenti ad altre attività di processo verranno rimossi"}, "ttDelVar": {"heading": "Eliminazione variabile", "body": "Eliminazione della variabile selezionata. Questa azione deve essere confermata. La variabile non sarà più disponibile per le singole attività di processo. Le variabili vengono selezionate facendo clic nella tabella delle variabili."}, "ttDelVice": {"heading": "Cancel vice", "body": ""}, "ttDetailCase": {"heading": "Dettaglio", "body": "Visualizza i dettagli del caso selezionato"}, "ttDetailCertificate": {"heading": "Dettaglio certificato", "body": "Visualizza i dettagli del certificato selezionato"}, "ttDetailHistory": {"heading": "Detail", "body": "Visualizza i dettagli dell'elemento selezionato"}, "ttDetailTsk": {"heading": "Det<PERSON><PERSON>ti<PERSON>", "body": "Visualizza i dettagli dell'attività selezionata"}, "ttDmsFolderAdd": {"heading": "Aggiungi nuova cartella", "body": "Aggiunta di una nuova cartella. Se una delle cartelle è selezionata, la cartella principale verrà precompilata."}, "ttDmsFolderEdit": {"heading": "Modifica cartella", "body": "Modifica della cartella contrassegnata."}, "ttDocuments": {"heading": "Archiviazione documenti", "body": ""}, "ttDownload": {"heading": "Download", "body": "Scarica il file selezionato"}, "ttDropContainer": {"heading": "Drop", "body": "Getta il contenitore dal pannello di controllo"}, "ttENotification": "Notifica via e-mail", "ttEdit": {"heading": "Edit", "body": "Consente di modificare l'elemento selezionato"}, "ttEditAttach": {"heading": "Edit", "body": "Consente di visualizzare e modificare gli attributi (metadati) del file caricato"}, "ttEditConnection": {"heading": "Modifica collegamenti", "body": "Modifica del collegamento tra due attività. È possibile modificare i parametri del comportamento e delle condizioni del collegamento. L'azione viene applicata al collegamento selezionato. I collegamenti vengono selezionati facendo clic su di essi nella tabella dei collegamenti"}, "ttEditOverview": {"heading": "Modifica panoramica", "body": "Consente di modificare una panoramica selezionata"}, "ttCopyOverview": {"heading": "Copia della panoramica", "body": "Creazione di una copia della panoramica selezionata"}, "ttEditPath": {"heading": "Aggiungi panoramica", "body": "Consente di definire una nuova panoramica"}, "ttEditTemp": {"heading": "Modifica della definizione del modello", "body": "Modifica del modello del caso. È possibile modificare qualsiasi parametro del modello. L'azione viene eseguita per il modello selezionato. Selezionare il modello facendo clic nella tabella dei modelli di casi."}, "ttEditTsk": {"heading": "Modifica attività", "body": "Modifica delle informazioni e dei parametri dell'attività. L'azione viene applicata all'attività selezionata. Selezionare un'attività facendo clic nella tabella delle attività"}, "ttEditTskOrConnection": {"heading": "Modifica di attività o collegamenti", "body": "Modifica delle informazioni sull'attività e dei parametri dell'attività o modifica dei collegamenti tra due attività, dei loro parametri comportamentali e delle condizioni di collegamento. L'azione viene applicata all'attività o al collegamento selezionato. Fare clic per selezionare."}, "ttEditTskVars": {"heading": "Modifica", "body": "Modifica variabili attività"}, "ttEditUser": {"heading": "Modifica delle informazioni sull'utente", "body": "Modifica delle informazioni di base sugli utenti, delle password, dell'assegnazione all'unità organizzativa e del ruolo. L'azione viene applicata all'utente selezionato. Gli utenti vengono selezionati facendo clic nella tabella degli utenti."}, "ttEditVar": {"heading": "Modifica di una variabile", "body": "Modifica del nome, del tipo e dei valori predefiniti delle variabili. L'azione viene applicata alla variabile selezionata. Le variabili vengono selezionate facendo clic nella tabella delle variabili."}, "ttEnotTgt": "Destinatario", "ttEnotTgtG": "Supervisore dei compiti", "ttEnotTgtO": "Proprietario del caso", "ttEnotTgtP": "%s", "ttEnotTgtR": "Ruolo %s", "ttEnotTgtS": "Unità organizzativa %s", "ttEnotTgtT": "Proprietario dell'attività %s", "ttEvent": {"heading": "Attività personalizzata", "body": "Invocazione immediata dell'evento in questa attività"}, "ttEvents": {"heading": "Events", "body": "Impostazione di regole aziendali per reagire a eventi interni o esterni definiti nel sistema. L'accesso richiede il ruolo di $PowerUser"}, "ttFavourites": {"heading": "Elenco dei preferiti", "body": "Un elenco di tutti i preferiti con la possibilità di modificarli o eliminarli dall'elenco"}, "ttFilter": {"heading": "Filtro", "body": "Visualizza solo gli elementi che soddisfano le condizioni di filtro definite"}, "ttFilterPrc": {"heading": "Filtro", "body": "Visualizza solo i casi che soddisfano le condizioni di filtro definite"}, "ttFilterTemp": {"heading": "Filtro", "body": "Visualizza solo i modelli che soddisfano le condizioni di filtro definite"}, "ttFilterTsk": {"heading": "Filtro", "body": "Visualizza solo le attività che soddisfano le condizioni di filtro definite"}, "ttFilterUser": {"heading": "Filtro", "body": "Visualizza solo gli utenti che soddisfano le condizioni di filtro definite"}, "ttFullScreen": {"heading": "Fullscreen", "body": "Mostra il contenuto del contenitore in modalità fullscreen"}, "ttGraph": {"heading": "Grafico", "body": "Rappresentazione grafica dello stato attuale del caso"}, "ttGraphActualFinish": "Finitura effettiva ", "ttGraphActualStart": "Data di inizio effettiva", "ttGraphCond": "Condizioni ", "ttGraphCond1": "almeno uno deve essere soddisfatto", "ttGraphCondAll": "tutti devono essere sodd<PERSON>", "ttGraphCondElse": "A meno che non sia soddisfatta un'altra condizione", "ttGraphDeadlinePo": "Scadenza: inserita dal proprietario del caso", "ttGraphDeadlinePs": "Scadenza: entro %s giorni dall'avvio del caso", "ttGraphDeadlineTs": "Scadenza: entro %s giorni dall'inizio dell'attività", "ttGraphDelayPo": "Avvio dell'attività: inserito dal proprietario del caso", "ttGraphDelayPs": "Inizio attività: %s giorni dall'inizio del caso", "ttGraphDelayTs": "Inizio attività: %s giorni dall'inizio dell'attività", "ttGraphEnd": "Il completamento dell'attività conclude l'intero caso", "ttGraphFinishedBy": "<PERSON><PERSON> da", "ttGraphHiearchyA": "tutti i superiori del supervisore del compito", "ttGraphHiearchyC": "subordinato diretto del supervisore dell'incarico", "ttGraphHiearchyD": "tutti i subordinati del supervisore dell'incarico", "ttGraphHiearchyG": "supervisore dei compiti", "ttGraphHiearchyL": "tutti ", "ttGraphHiearchyP": "superiore diretto del supervisore della mansione", "ttGraphHiearchyS": "supervisore dei compiti collaboratori", "ttGraphLinkFrom": "Da", "ttGraphLinkTo": "A", "ttGraphMethodL": "al proprietario dell'ultimo compito %s", "ttGraphMethodS": "al proprietario del compito selezionato da un supervisore", "ttGraphMethodT": "al proprietario dell'attività selezionata automaticamente", "ttGraphMethodV": "al proprietario del compito assegnato alla variabile %s", "ttGraphMultiinstance": "Multi-istanza", "ttGraphNoneMand": "Collegamento obbligatorio", "ttGraphOnlyOnce": "Eseguire una sola volta", "ttGraphSave": {"heading": "Salva diagramma e crea modello", "body": ""}, "ttGraphStart": "L'attività verrà attivata automaticamente dopo l'avvio del caso.", "ttGraphTaskHiearchy": "Proprietario dell'attività", "ttGraphTaskMethod": "Il compito sarà assegnato", "ttGraphTaskOwner": "Supervisore dei compiti", "ttGraphTaskOwnerOS": "Responsabile dell'unità organizzativa", "ttGraphTaskOwnerPO": "Proprietario del caso", "ttGraphTaskOwnerSU": "Utente selezionato", "ttGraphTaskRole": "con ruolo", "ttGraphTaskTypeA": "Attività automatica", "ttGraphTaskUser": "Proprietario dell'attività", "ttGraphWait1": "Parametri di ingresso: in attesa di uno", "ttGraphWaitA": "Parametri di ingresso: attesa per tutti", "ttGraphWaitFirst": "Parametri di ingresso: attesa per tutti, esecuzione per prima", "ttGraphWaitN": "Parametri di ingresso: in attesa di %s", "ttHandover": {"heading": "Passa l'attività", "body": "Permette di passare l'attività a un altro utente disponibile"}, "ttDelegate": {"heading": "Delega attività", "body": ""}, "ttReject": {"heading": "Reject task", "body": ""}, "ttHelp": {"heading": "<PERSON><PERSON> immediato", "body": "Consentire o disabilitare l'aiuto immediato. La guida viene visualizzata sotto forma di bolle che compaiono con informazioni sull'interfaccia utente quando si passa il mouse sulle funzioni."}, "ttHome": {"heading": "Pagina iniziale dell'utente", "body": "Luogo unico con tutte le informazioni per gli utenti abituali. La dashboard offre una visione generale."}, "ttHtml": {"heading": "Genera documentazione", "body": "Generazione della documentazione HTML del processo del modello. A seconda del tipo di browser, il documento può essere visualizzato immediatamente o salvato su disco"}, "ttInclusion": {"heading": "Inclusion", "body": "Esporta un file con il riepilogo delle autorizzazioni e dei ruoli dell'utente, tutti i ruoli firmati utilizzati, le entità dell'organizzazione di cui è membro o responsabile, compresa la gerarchia dei compiti di cui è il supervisore"}, "ttInvAttendees": "Partecipanti", "ttInvDTEnd": "Fine", "ttInvDTStart": "<PERSON><PERSON><PERSON>", "ttInvLocation": "Posizione", "ttInvitation": "Invito", "ttJustSave": {"heading": "<PERSON><PERSON>", "body": "Salva le modifiche"}, "ttLock": {"heading": "Blocca", "body": "Blocca o sblocca la selezione"}, "ttLockUser": {"heading": "Blocco", "body": "Blocca o sblocca l'utente"}, "ttLogout": {"heading": "Disconnessione", "body": "Disconnessione di un utente. Dopo aver completato con successo il lavoro con l'applicazione, viene visualizzata la finestra di dialogo iniziale per il login."}, "ttMapping": {"heading": "Mapping", "body": "Una panoramica generale delle variabili assegnate per la lettura (R), la scrittura (W) e l'inserimento obbligatorio (M) nei singoli compiti con la possibilità di modificare il proprio compito."}, "ttNewCase": {"heading": "Nuovo caso", "body": "Creazione di una nuova istanza di processo - nuovo caso. È possibile scegliere tra i modelli di processo disponibili o creare un caso senza una struttura di attività predefinita"}, "ttNewOverview": {"heading": "Aggiungi vista", "body": "Consente di definire una nuova panoramica"}, "ttOrgStructure": {"heading": "Struttura organizzativa", "body": ""}, "ttParent": {"heading": "Superiore", "body": "Passa a un caso da cui il caso visualizzato è stato creato come sottoprocesso"}, "ttPhoto": {"heading": "Fotografie", "body": "Caricamento di foto nel profilo dell'utente. Supporta i formati GIF, JPG e PNG. La dimensione dell'immagine verrà regolata automaticamente."}, "ttPlans": {"heading": "Scheduling", "body": "Impostazione di regole per l'avvio automatico, una tantum o ripetuto, di istanze di processo - casi in base a parametri specificati. L'accesso richiede il ruolo di $Amministratore"}, "ttPrint": {"heading": "Stampa", "body": "<PERSON><PERSON> la stampa"}, "ttRecalc": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Ricalcolo delle variabili correnti"}, "ttRedirectToPrc": {"heading": "Case", "body": ""}, "ttResetDash": {"heading": "Reset", "body": "R<PERSON><PERSON><PERSON> le modifiche completate"}, "ttResetSearch": {"heading": "Reset", "body": "Ripristina il modulo di ricerca"}, "ttRestoreTemp": {"heading": "Ripristino template", "body": "Ripristina template cancellato"}, "ttRevision": {"heading": "Revisione", "body": "Consente di caricare una nuova versione del file"}, "ttRoles": {"heading": "Gestione dei ruoli", "body": "Gestione dei ruoli"}, "ttRunEvent": {"heading": "Esegui evento", "body": "Invocazione evento in questo caso"}, "ttSave": {"heading": "<PERSON><PERSON>", "body": "Salva le modifiche e chiude la finestra"}, "ttSaveDMSCols": {"heading": "Salva colonne", "body": ""}, "ttSaveSettings": {"heading": "<PERSON><PERSON>", "body": "Salva le modifiche"}, "ttSaveTsk": {"heading": "Solo salvataggio", "body": "L'attività aperta verrà salvata per potervi tornare in seguito"}, "ttSearch": {"heading": "Ricerca", "body": "Avvia la ricerca"}, "ttSendNote": {"heading": "Aggiu<PERSON>i nota", "body": "Consente di inserire una nuova nota"}, "ttSetConnectionCond": {"heading": "Condition", "body": "Aggiunta o modifica delle condizioni del collegamento. La modifica viene applicata al collegamento selezionato. Fare clic sul simbolo del collegamento o della condizione per selezionarlo"}, "ttSetDefaultDash": {"heading": "Imposta come dashboard predefinito", "body": "Imposta la disposizione del dashboard corrente come predefinita"}, "ttShowHideBtn": {"heading": "Mostra/nascondi", "body": "Nasconde o mostra parzialmente il menu principale"}, "ttSleepCase": {"heading": "Sospendi il caso", "body": "Contrassegna il caso come sospeso. Il caso non sarà più visualizzato tra le attività attive, ma se necessario è possibile cambiare lo stato in attivo e terminare l'intero caso in un secondo momento"}, "ttSolve": {"heading": "<PERSON><PERSON>", "body": "Visualizza la finestra di dialogo che consente di avviare il lavoro sull'attività assegnata secondo un modello predefinito"}, "ttStatePlan": {"heading": "Stato", "body": "Definisce lo stato del piano"}, "ttStatusHdr": {"heading": "Modifica dello stato dell'intestazione", "body": "L'azione viene applicata al modello selezionato. Sono disponibili gli stati \"attivo\" e \"inattivo\". La selezione dell'intestazione viene effettuata facendo clic nella tabella delle intestazioni del modello."}, "ttStatusTemp": {"heading": "Modifica dello stato del template", "body": "La gestione del ciclo di vita del template viene eseguita tramite l'impostazione del suo stato. Sono disponibili gli stati \"in sviluppo\", \"attivo\", \"inattivo\" e \"cancellato\". L'azione viene applicata al modello selezionato. La selezione del modello avviene facendo clic nella tabella dei modelli di caso."}, "ttSubprocess": {"heading": "<PERSON><PERSON><PERSON>", "body": "Passa a un caso creato come sottoprocesso nel processo del caso visualizzato"}, "ttTabsButtonMore": {"heading": "More", "body": "Mostra altre opzioni"}, "ttTakeTsk": {"heading": "Prendi in carico l'attività", "body": "Consente di prendere in carico l'attività con un altro proprietario"}, "ttTemps": {"heading": "Modelli di processo", "body": "Luogo centrale per la gestione dei modelli di processo. L'accesso richiede il ruolo di $PowerUser"}, "ttTiming": {"heading": "Pianificazione", "body": "Immettere l'inizio e la fine dell'attività"}, "ttTsks": {"heading": "Tasks", "body": ""}, "ttUploadSettings": {"heading": "Upload", "body": ""}, "ttUserSetting": {"heading": "Impostazioni utente", "body": "Impostazione delle informazioni di contatto dell'utente, delle password di accesso e delle preferenze dell'utente. Gli utenti con il ruolo di $Amministratore possono gestire ulteriormente le informazioni sulla propria organizzazione e sulle istanze dell'applicazione TeamAssistant"}, "ttUsers": {"heading": "Amministrazione degli utenti", "body": "Amministrazione centrale degli utenti, struttura organizzativa e ruoli degli utenti. L'accesso richiede il ruolo di $Amministratore"}, "ttValidation": {"heading": "Convalida", "body": "Convalida il modello e visualizza tutti i cicli esistenti nel modello. Notifica le condizioni non soddisfacenti e le variabili non utilizzate."}, "ttViewFile": {"heading": "View", "body": ""}, "ttWakeUpCase": {"heading": "Wake up", "body": ""}, "ttActivateCase": {"heading": "Activate", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Imposta colonne DMS predefinite", "body": "Imposta l'assegnazione delle colonne DMS come predefinite"}, "ttResetDmsCols": {"heading": "Reimposta", "body": "Reimposta l'assegnazione delle colonne DMS"}, "ttRestoreDoc": {"heading": "R<PERSON><PERSON><PERSON>", "body": "Ripristina il documento eliminato"}, "ttSearchHeader": {"heading": "Search", "body": ""}, "tue": "Martedì", "type": "Tipo", "typeOfRepetition": "Tipo di ripetizione", "unassignedSolvers": "Secondo i proprietari dei compiti", "unassignedTaskSolvers": "Proprietari di attività non assegnate", "uncategorized": "Senza categoria", "unfinishedProcesses": "Casi incompiuti", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknownUser": "Utente sconosciuto", "unrestricted": "Non vincolato", "unspecified": "Non specificato", "upload": "Caricare", "uploadFile": "Caricare un file", "uploadPhoto": "Carica foto", "uploadCsv": "Carica csv", "url": "URL", "urlAddress": "Indirizzo URL", "urlContent": "Contenuto dell'URL", "use": "<PERSON><PERSON><PERSON><PERSON>", "user": "Utente", "userByOwnerOfLastTask": "All'utente scelto dall'ultimo proprietario dell'attività.", "userE": "utente", "userFilters": "Filtri utente", "userLock": "Blocco", "userLockUnlockQ": "Si vuole davvero cambiare lo stato dell'utente {{username}}?", "userName": "Nome utente", "userId": "ID utente", "userOrgStruct": "Appartiene all'unità organizzativa", "userVice": "Sostituito da", "userViced": "Da sostituire", "users": "<PERSON><PERSON><PERSON>", "usersDeleted": "Eliminato", "validation": "Convalida", "value": "Valore", "var": "variabile", "var-": "Variabile -", "varChange": "La modifica della variabile sarà annunciata a tutti i partecipanti al caso.", "varTaskMap": "Mappatura", "varTemp": "<PERSON><PERSON> variabile", "variable": "Variabile", "variableType": "Tipo di variabile", "vars": "Variabili", "varsForMandatory": "Variabili per l'inserimento obbligatorio", "varsForReading": "Variabili di lettura", "varsForWriting": "Variabili per la scrittura", "vices": "Delegati", "viewCVFields": "Campi disponibili", "visForOrgStrMembers": "Visibile ai membri dell'unità organizzativa", "visForRoleMembers": "Visibile ai membri con un ruolo", "headerVisForRole": "Caso visibile ai membri con ruolo", "waitForNumOfInputs": "In attesa di: (numero di ingressi)", "waitsFor": "attende", "waitsForAll": "aspetta tutti", "waitsForOne": "aspetta uno", "waitsForSending": "Attende l'invio", "waitsRunFirst": "attende tutti, esegue per primo", "wakeUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warning": "Avviso", "wed": "Mercoledì", "weekIn": "<PERSON><PERSON><PERSON> in", "weekly": "<PERSON><PERSON><PERSON><PERSON>", "width": "<PERSON><PERSON><PERSON><PERSON>", "withConditions": "Con condizioni", "withoutCond": "Senza condizioni", "year": "anno", "yes": "Sì", "zip": "Codice postale", "move": "<PERSON><PERSON><PERSON>", "alertClosing1": "si chiuderà automaticamente:", "inDocuments": "In documenti", "inVariables": "In Variabili", "headerTask": "Intestazione del compito", "planName": "Nome del piano", "inBulk": "In massa", "confirmResetDmsColumns": "Volete davvero reimpostare le colonne del DMS?", "dmsColsUseDef": "Utilizzo delle impostazioni predefinite", "dmsColsUseCust": "Utilizzo delle impostazioni personalizzate", "today": "<PERSON><PERSON><PERSON>", "alrPlanDeleteFailed": "L'eliminazione del piano non è riuscita.", "notRunning": "Non in esecuzione", "alrLackOfPermsToAddTask": "Non si hanno le autorizzazioni per aggiungere l'attività.", "dragTable": "<PERSON><PERSON><PERSON><PERSON> tabella", "alrDownloadCsvListFailed": "Il download dell'elenco dei file Csv non è riuscito.", "alrCsvUploadWrongExtension": "Caricare solo file con estensione *. csv", "addToFav": "Aggiungi ai preferiti", "renameItem": "Rinominare l'elemento", "removeFromFav": "Rimuovere dai preferiti?", "alrAddedToFav": "Aggiunto ai preferiti.", "alrRemovedFromFav": "<PERSON><PERSON><PERSON> dai preferiti.", "tskSetAssignDues": "Impostare restrizioni temporali per le attività", "isNot": "non è", "alrTskScheduling": "Pianificazione dei compiti...", "alrFavouritesPageExist": "Questa pagina è già tra i preferiti.", "alrFavouritesActionExist": "Questa azione è già in corso.", "alrFavouriteRenamed": "Il nuovo nome è stato modificato nell'elenco dei preferiti.", "autoFit": "AutoFit", "passwordIsShort": "La password è troppo corta.", "changeAttrComplCases": "Modifica degli attributi dei casi completati", "iterateOverVars": "Iterare sulle variabili", "nrOfDecimalDigits": "Numero di cifre decimali", "onlyNumbers": "Solo numeri", "maxNumberOfDecimals": "Il numero massimo di cifre decimali è", "alrInsertCsv": "Inserire il file CSV.", "addBefore": "Aggiungere prima", "moveBefore": "Spostare prima", "administration": "Amministrazione", "ttAdministration": {"heading": "Amministrazione", "body": ""}, "alrLogsLoadFailed": "Il caricamento dei registri non è riuscito.", "logs": "<PERSON><PERSON>", "message": "Messaggio", "useCompatibleTempl": "Utilizzare un modello compatibile", "overwriteExistTempl": "Sovrascrivere il modello esistente", "addNewTempl": "Aggiungere un nuovo modello", "import": "Importazione", "export": "Esportazione", "confirmExportAllTempl": "Esportare tutti i modelli?", "confirmExportSelTempl": "Esportare il modello selezionato?", "newLogs": "Nuovi registri", "container": "contenitore", "confirmRemoveDialog": "Si vuole davvero rimuovere la {{variable}}?", "allMyCases": "<PERSON><PERSON> i miei casi", "maintenanceMsg": "La <span style=\"color: {{color}};\">manutenzione</span> programmata è in corso", "alrMaintenanceMsg": "Manutenzione programmata in corso, provare più tardi.", "alrAttachDownloadLackOfPerms": "Non avete i permessi per scaricare il documento o il documento non è stato trovato.", "unableToConnect": "Impossibile connettersi al server", "tryLater": "Provate più tardi o contattate l'amministratore.", "enableTaskDelegation": "Abilitare la delega dei compiti", "enableRejectTask": "Abilita il rifiuto dell'attività ", "confirmRejectTask": "Volete davvero rifiutare il compito?", "rejectTask": "Rifiutare compito", "delegateTask": "Delegare  ", "alrRejectingTask": "Rifiuto del compito...", "alrTaskRejected": "Il compito è stato rifiutato.", "alrTaskRejectFailed": "L'attività non è stata rifiutata.", "alrTaskDelegating": "Delegare il compito...", "alrTaskDelegated": "Il compito è stato delegato all'utente:", "alrFailedTaskDelegate": "La delega dell'attività non è riuscita.", "delegateOnUser": "Delega all'utente", "plnAssignmentCond": "Se il campo \"Assegnazioni\" rim<PERSON> vuoto, verrà creato un elenco di iniziatori valutando le condizioni restrittive al momento dell'esecuzione del piano.", "alrUserFiltersSettingsFailed": "Il salvataggio dell'impostazione dei filtri utente non è riuscito.", "general": "Generale", "alrUserPhotoLoadFailed": "Caricamento foto utente non riuscito.", "publicDynTable": "Tabella dinamica pubblica", "isFullIndexed": "In ricerca", "datetimeIndexed": "Indicizzato su", "toIndex": "Per in<PERSON>e", "toReindex": "Per reindicizzare", "solverChanged": "Il proprietario dell'attività è stato modificato nelle attività di {{count}}.", "changeSolverFailed": "Modifica del proprietario dell'attività non riuscita.", "alrTikaParsingFailed": "Si è verificato un errore durante l'analisi del documento.", "alrIndexingFailed": "L'indicizzazione del documento non è riuscita.", "alrTikaNotRunning": "Servizio di analisi dei documenti non disponibile.", "alrIndexingServiceNotRunning": "Servizio di indicizzazione non disponibile.", "alrFulltextNotSet": "Il testo completo non è stato impostato.", "asc": "In salita", "desc": "In discesa", "restore": "<PERSON><PERSON><PERSON><PERSON>", "alrLogosLoadFailed": "Caricamento dei loghi non riuscito.", "indexedDocsCount": "in {{count}} documenti", "alrIndexedCountLoadFailed": "La ricerca full-text non è attualmente disponibile.", "searchAll": "Cerca tutti", "searchActual": "Solo attuali", "runIndexing": "Eseguire l'indicizzazione", "alrDocumentIndexing": "Indicizzazione dei documenti...", "alrDocumentIndexed": "Il documento è stato indicizzato e può essere trovato con una ricerca.", "alrDocumentIndexedWithMinMetadata": "Il documento è stato indicizzato.", "alrDocumentIndexingFailed": "L'indicizzazione del documento non è riuscita.", "changingUserProfileForbidden": "La modifica del profilo utente è vietata.", "uploadingPhotoForbidden": "Il caricamento di foto è vietato.", "alrValidationCalcError": "Errore nella convalida dei calcoli", "maintenance": "Manutenzione", "maintenanceActivate": "Attivare la manutenzione", "maintenanceInfoText": "L'inizio e la fine saranno visualizzati dagli utenti dopo l'attivazione della manutenzione.", "maintenanceMode": "Modalità di manutenzione", "alrAvailableCalcFailed": "Non è stato possibile caricare i calcoli disponibili.", "alrFillDataForSearch": "Inserire i parametri di ricerca.", "youAreHere": "Sei qui", "invalidDate": "Formato della data non valido", "alrInvalidFileFormat": "Formato file non valido.", "alrEnter3characters": "Inserire almeno tre caratteri.", "changeCaseOwner": "Cambiare il proprietario del caso", "actualCaseOwner": "Proprietario effettivo del caso", "newCaseOwner": "Nuovo proprietario del caso", "alrCaseOwnerChanged": "Il proprietario del caso è stato cambiato.", "alrChangeCaseOwnerFailed": "La modifica del proprietario del caso non è riuscita.", "alrCsvSaving": "Salvataggio del file CSV...", "alrCsvSaveFailed": "Il caricamento del file CSV non è riuscito.", "alrCsvSaved": "Il file CSV è stato caricato.", "allTemplates": "<PERSON><PERSON> i <PERSON>", "specifyCaseIds": "Specificare gli ID dei casi", "caseIds": "ID del caso", "caseId": "ID caso", "separBySemicolon": "separati da un punto e virgola", "alrAddCaseIds": "Specificare gli ID dei casi", "headers": "Intestazioni", "header": "Intestazione", "defaultHeaderName": "Nome predefinito dell'intestazione", "headerName": "Nome dell'intestazione", "addHeader": "Aggiungi intestazione", "editHeader": "Modifica intestazione", "templateName": "Nome del modello", "rolesExecRightsText": "Assegnare i ruoli che potranno avviare i casi", "orgUnitsExecRightsText": "Assegnare le unità organizzative in grado di avviare i casi.", "selectedHeader": "intestazione selezionata", "alrHeaderDeleted": "L'intestazione è stata cancellata!", "alrHeaderDeleteFailed": "Eliminazione dell'intestazione non riuscita.", "alrHeaderSaveFailed": "Salvataggio dell'intestazione non riuscito.", "alrHeaderSaved": "L'intestazione è stata salvata.", "alrHeadersLoadFailed": "Caricamento dei dati di intestazione non riuscito.", "identificator": "Codice dell'intestazione", "includeDataSimilarProcesses": "Includere i dati di tutti i processi simili", "confirmCopyCv": "Volete davvero copiare la panoramica selezionata?", "alrCreatingCopyCv": "Creare una copia della panoramica...", "alrCvCopied": "La panoramica è stata copiata.", "alrCopyCvFailed": "La creazione di una copia della panoramica non è riuscita.", "copyingTemplate": "Copia di un modello", "alrCheckTempImportFailed": "Verifica dell'importazione del modello fallita.", "warnings": "Avvisi", "missingEventsFiles": "File eventi mancanti", "missingEventsFilesText": "Il file {{- file}} non è stato trovato nell'evento {{- event}}.", "printsOfTemplates": "Stampe di modelli", "printsOfTemplatesText": "Prestare attenzione alla stampa di {{- print}} dal modello {{- template}}. Valore: {{- value}}", "dupliciteTaskNames": "Nomi di attività duplicati", "dupliciteTaskNamesText": "Il modello {{- template}} contiene più compiti con lo stesso nome {{- task}} {{- taskId}}, causerà la rottura dei collegamenti!", "dynTableUsed": "<PERSON>bella dinamica utilizzata", "suspiciousCalc": "<PERSON><PERSON><PERSON>", "suspiciousCalcText": "Possibile mancanza di ruolo/organizzazione/utente nel calcolo {{- calc}}.", "missingEvents": "<PERSON>i mancanti", "missingEvent": "Evento mancante", "wrongMappingDomains": "Mappatura errata dei domini", "wrongMappingDomainsText": "La descrizione dell'attività {{- task}} del modello {{- template}} contiene un nome di dominio errato, il dominio corrente è {{- actDom}}.", "taskDescription": "Descrizione del compito", "eventsUrl": "URL eventi", "eventsUrlText": "Possibile errore nell'url dell'evento {{- event}}, il dominio corrente è {{- actDom}}.", "param": "Parametro", "alrServiceNotForTable": "I dati di questo serdelegatonon possono essere visualizzati nella tabella.", "alrServiceDataFailedLoad": "I dati di servizio non sono stati caricati.", "alrServiceNoData": "Il servizio non contiene dati.", "tableColumns": "<PERSON><PERSON><PERSON>bella", "datetime": "Data e ora", "exactDatetime": "Data e ora esatta", "dashRestNoColumns": "Nessuna colonna impostata: sceglierla nelle impostazioni del contenitore", "loadService": "<PERSON><PERSON><PERSON> servizio", "useCompatibleRole": "Utilizzare un ruolo compatibile", "overwriteExistRole": "Sovrascrivere il ruolo esistente", "addNewRole": "Aggiungere un nuovo ruolo", "templateImportFailed": "Importazione del modello non riuscita.", "templateImport": "Importazione di modelli", "templateImportNoData": "Non sono stati trovati dati per l'importazione del modello.", "variableImportNoData": "<PERSON><PERSON><PERSON> dato trovato per importare le variabili.", "ttTemplateImport": {"heading": "Importazione di modelli", "body": "Viene selezionata e poi caricata una cartella con le definizioni di uno o più modelli"}, "showUnfinishedProcesses": "Mostra i casi non finiti", "expMaintenanceEnd": "Fine prevista della manutenzione", "alrScriptSaveFailed": "Il salvataggio dello script non è riuscito.", "editScript": "Modifica dello script", "addScript": "Aggiungi script", "alrRunScript": "Avvio dello script...", "alrScriptCompleted": "Sceneggiatura completata.", "alrFailedScriptStart": "L'avvio dello script è fallito.", "alrScriptDocsLoadFailed": "La documentazione degli script non è stata caricata.", "alrScriptLoadFailed": "Il caricamento degli script non è riuscito.", "switchAdminUser": "Cambio amministratore/utente", "ttSwitchAdminUser": {"heading": "Cambio amministratore/utente", "body": ""}, "ttSwitchViewport": {"heading": "Commutazione visualizzazione mobile/PC", "body": ""}, "alrEventDataLoadFailed": "I dati dell'evento non sono stati caricati.", "alrEventRuleDataLoadFailed": "I dati della regola evento non sono stati caricati.", "cancellation": "Cancellazione", "tTaskAutoCancellCaption": "L'attività verrà annullata automaticamente se", "codeMirrorHelp": "<PERSON>e clic in un punto qualsiasi dell'editor e premere Ctrl + Spazio per visualizzare la guida.", "codeMirrorHelpJs": "Per un elenco di tutte le funzionalità, fare clic su <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "Aggiungi evento", "editEvent": "Modifica evento", "term": "Termine", "columnOrder": "Ordine delle colonne", "alrLoadEventsButtonsFailed": "Il caricamento dei pulsanti nella tabella non è riuscito.", "showButtonsCol": "Mostra la colonna delle azioni", "button": "Pulsante", "enableButtonInTasks": "Mostra come pulsante nell'elenco delle attività", "alrEventDoesntExist": "L'evento selezionato non esiste.", "alrEventRuleSaveFailed": "Il salvataggio della regola dell'evento non è riuscito.", "variableNames": "Nomi delle variabili", "fsEvent": "evento", "alrEventDeleteFailed": "L'eliminazione dell'evento non è riuscita.", "fsRule": "regola", "alrRuleDeleteFailed": "L'eliminazione della regola non è riuscita.", "alrRuleStatusChangeFailed": "Modifica dello stato della regola non riuscita.", "ruleActivateDeactivateQ": "Volete davvero cambiare lo stato della regola?", "docUploadedPrivate": "Il documento verrà caricato come privato", "fileOwner": "Proprietario del file", "planOk": "Ok", "userNotAuthToStartTempl": "L'utente non è autorizzato ad avviare un caso da questo modello", "planStartDate": "Data di inizio", "useCompatibleEvent": "Utilizzare un evento compatibile", "overwriteExistEvent": "Sovrascrivere l'evento esistente", "addNewEvent": "Aggiungi un nuovo evento", "useCompatibleUser": "Utilizzare un utente compatibile", "overwriteExistUser": "Sovrascrivere l'utente esistente", "addNewUser": "Aggiungi un nuovo utente", "useOnlyFutureDates": "Solo date future", "alrGenerateHtmlFailed": "Generazione di HTML non riuscita.", "alrNoPermsToAddNoteInVice": "Non si hanno i permessi per aggiungere la nota come sostituto.", "alrNoPermsToAddDocInVice": "Non avete i permessi per aggiungere il documento come sostituto.", "current": "Attuale", "indexation": "Indicizzazione", "attemptToRestoreConnection": "Tentare di ripristinare la connessione in", "loginWillExpire": "Il login scadrà tra", "unsavedDataWillBeLost": "I dati non salvati and<PERSON>no persi.", "alrFileSaveLikeAttachViceError": "Il tuo delegato ha l'autorizzazione alla sola visualizzazione!", "alrFileSaveLikeAttachStoreError": "Il salvataggio della stampa come documento nel caso non è riuscito.", "useCompatibleUnit": "Utilizzare un'unità organizzativa compatibile", "overwriteExistUnit": "Sovrascrivere l'unità organizzativa esistente", "addNewUnit": "Aggiungere una nuova unità organizzativa", "addNewDynTable": "Aggiunta di una nuova tabella dyn.", "useCompatibleDynTable": "Utilizzare una tabella di dinamicità compatibile", "addNewCalcScript": "Aggiungere un nuovo script", "useCompatibleCalcScript": "Utilizzare uno script compatibile", "enterDiffNameRoot": "Inserire un nome diverso da Root.", "ttTemplatesExport": {"heading": "Esporta modelli", "body": "Esporta i modelli selezionati in una cartella.  È possibile scegliere il nome e la posizione del file esportato.  L'azione viene applicata al modello selezionato.  Selezionare un modello facendo clic su di esso nella tabella dei modelli di casi"}, "ttTemplatesExportAll": {"heading": "Esporta tutti i modelli", "body": "Esporta in un file tutti i modelli attualmente visualizzati.  È possibile scegliere il nome e la posizione del file esportato.  La selezione dei modelli può essere limitata impostando condizioni di filtro adeguate."}, "exportAll": "Esporta tutto", "noTemplatesToExport": "<PERSON><PERSON><PERSON>lo da esportare.", "skip": "Salta", "ttSkipTemplate": {"heading": "Salta il modello", "body": "Salta l'importazione del modello corrente e visualizza il successivo"}, "alrInvalidImportData": "Dati di importazione non validi", "alrUsersNotLoaded": "Il caricamento degli utenti non è riuscito.", "caseOverview": "Panoramica del caso", "alrRolesNotLoaded": "Il caricamento dei ruoli non è riuscito.", "changeLang": "Cambiare la lingua", "reactivatesPlan": "ria<PERSON><PERSON> il piano", "alrOrgUnitsNotLoaded": "Le unità dell'organizzazione non sono state caricate.", "refreshPage": "Aggiorna la pagina", "stayLogged": "Rimani registrato", "showTime": "Mostra il timestamp nelle panoramiche", "managerIn": "manager in {{orgUnit}}", "usageStats": "Statistiche d'uso", "month": "Mese", "alrUsageStatsLoadFailed": "Impossibile caricare le statistiche di utilizzo.", "accessLog": "Registro di accesso", "durationInMs": "<PERSON><PERSON> (ms)", "task": "Compito", "operation": "Operazione", "active_users": "Numero di utenti attivi", "active_template_processes": "Numero di processi attivi", "active_headers": "Numero di intestazioni attive", "active_users_able_to_create_a_process": "Numero di utenti attivi che possono eseguire un processo", "users_that_solved_a_task": "Numero di utenti che hanno risolto almeno un compito", "solvers_or_can_create_a_process": "Numero di risolutori che hanno risolto un'attività o possono eseguire un processo", "mobile_app_paired_users": "Numero di utenti associati all'app mobile", "calculationsLogs": "Registri di calcolo", "translatedScript": "<PERSON><PERSON> tradotto", "originalScript": "Sceneggiatura originale", "tskId": "ID compito", "alrCalculationsDocsLoadFailed": "Impossibile caricare i documenti di calcolo.", "alrCalculationsValidationFailed": "La convalida dei calcoli non è riuscita.", "linkPriority": "Priorità di collegamento", "dateFormat": "GG/MM/AAAA", "alrConvertErrorJsonNeon": "Errore durante la conversione json -> neon.", "alrInvalidData": "Dati non validi.", "sharedVar": "Variabile condivisa", "guide": "<PERSON><PERSON><PERSON>", "guideFs": "guida", "guides": "Guide", "alrGuidesLoadFailed": "Le guide non sono state caricate.", "language": "<PERSON><PERSON>", "default": "Predefinito", "next": "<PERSON><PERSON>", "previous": "Precedente", "targetElementNotFound": "Elemento di destinazione non trovato", "documentation": "Documentazione", "matchesRegular": "Non corrisponde alla regola", "secondAllowedValues": "valori consentiti; 0 è la parte superiore del minuto", "everySec": "ogni secondo", "listOfSec": "un elenco di secondi; ad esempio, 0,30 sarebbero i secondi 0 e 30", "rangeOfSec": "un intervallo di secondi; ad esem<PERSON>, 0-5 sa<PERSON><PERSON><PERSON> i secondi 0, 1, 2, 3, 4 e 5 (è anche possibile specificare un elenco di intervalli 0-5,30-35)", "slashSec": "I valori dei passi saltano il numero specificato all'interno di un intervallo; ad esempio, */5 è ogni 5 secondi e 0-30/2 è ogni 2 secondi tra 0 e 30 secondi.", "minuteAllowedValues": "valori consentiti; 0 è l'ora precisa", "everyMin": "ogni minuto", "listOfMin": "un elenco di minuti; ad esempio, 0,30 sarebbero i minuti 0 e 30", "rangeOfMin": "un intervallo di minuti; ad esem<PERSON>, 0-5 sa<PERSON><PERSON><PERSON> i minuti 0, 1, 2, 3, 4 e 5 (è anche possibile specificare un elenco di intervalli 0-5,30-35)", "slashMin": "I valori dei passi saltano il numero specificato all'interno di un intervallo; ad esempio, */5 è ogni 5 minuti e 0-30/2 è ogni 2 minuti tra 0 e 30 minuti.", "hourAllowedValues": "valori consentiti; 0 è mezzanotte", "everyHour": "ogni ora", "listOfHour": "un elenco di ore; ad esempio, 0,12 sarebbe l'ora 0 e 12.", "rangeOfHour": "un intervallo di ore; ad esempio, 19-23 sarebbe<PERSON> le ore 19, 20, 21, 22 e 23 (è anche possibile specificare un elenco di intervalli 0-5,12-16)", "slashHour": "I valori dei passi saltano il numero specificato all'interno di un intervallo; ad esempio, */4 è ogni 4 ore e 0-20/2 è ogni 2 ore tra lo 0 e la 20a ora.", "dayAllowedValues": "valori consentiti", "everyMonthDay": "ogni giorno del mese", "listOfDay": "un elenco di giorni; ad esempio, 1,15 sarebbe il primo e il quindicesimo giorno del mese.", "rangeOfDay": "un intervallo di giorni; ad esem<PERSON>, 1-5 corrisponderebbe ai giorni 1, 2, 3, 4 e 5 (è anche possibile specificare un elenco di intervalli 1-5,14-30)", "slashDay": "I valori dei passi saltano il numero specificato all'interno di un intervallo; ad esempio, */4 è ogni 4 giorni e 1-20/2 è ogni 2 giorni tra il 1° e il 20° giorno del mese.", "allowedValues": "valori consentiti", "everyMonth": "ogni mese", "listOfMonth": "un elenco di mesi; ad esempio, 1,6 sarebbero i mesi di gennaio e giugno.", "rangeOfMonth": "un intervallo di mesi, ad esempio 1-3 <PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>, feb<PERSON><PERSON> e marzo (è possibile anche specificare un elenco di intervalli 1-4, 8-12)", "slashMonth": "I valori dei passi saltano il numero specificato all'interno di un intervallo; ad esempio, */4 è ogni 4 mesi e 1-8/2 è ogni 2 mesi tra gennaio e agosto.", "weekAllowedValues": "valori ammessi; 0=domenica, 1=lunedì, 2=martedì, 3=mercoledì, 4=giovedì, 5=venerdì, 6=sabato", "everyWeekDay": "<PERSON><PERSON> giorno della settimana", "listOfWeekDay": "un elenco di giorni; ad esempio, 1,5 sarebbero il lunedì e il venerdì.", "rangeOfWeekDay": "un intervallo di giorni; ad esem<PERSON>, 1-5 sa<PERSON><PERSON> lun, mar, merc, gio e ven (si può anche specificare un elenco di intervalli 0-2,4-6)", "slashWeek": "I valori dei passi saltano il numero specificato all'interno di un intervallo; ad esempio, */4 è ogni 4 giorni e 1-5/2 è ogni 2 giorni tra lunedì e venerdì.", "contrab": "Campo Cron {{variable}}", "cSecond": "secondo", "cMinute": "minuto", "cHour": "ora", "cDay": "<PERSON>ior<PERSON>", "cMonth": "mese", "cWeekDay": "<PERSON>ior<PERSON> settimana", "seconds": "secondi", "minutes": "minuti", "hours": "ore", "days": "<PERSON>ior<PERSON>", "weeks": "<PERSON><PERSON><PERSON><PERSON>", "socketOk": "La tabella contiene i dati più recenti", "socketBroken": "Ripristino della connessione per l'aggiornamento dei dati in corso", "newTask": "Nuovo compito", "report": "Rapporto", "ttCaseReport": {"heading": "Report", "body": ""}, "usersRights": "Diritti dell'utente", "visPerRole": "Visibilità per ruolo", "manualEvents": "Eventi manuali", "noTasks": "Nessun nuovo compito", "emptyFavs": "L'elenco dei preferiti è vuoto", "crons": "Crons", "cronsHistory": "La storia di crons", "redirBefStart": "Prima di iniziare, reindirizzare a", "lastRun": "Ultima corsa", "nextRun": "Prossima corsa", "syntax": "Sin<PERSON><PERSON>", "alias": "<PERSON><PERSON>", "stop": "Fermo", "restart": "<PERSON><PERSON><PERSON><PERSON>", "restartCronProcess": "Riavviare il contesto del processo", "ttRestartCron": {"heading": "R<PERSON>v<PERSON> cron", "body": ""}, "ttRestartCronProcess": {"heading": "Riavvia il processo", "body": ""}, "ttResetCron": {"heading": "Reset cron", "body": ""}, "ttRunCron": {"heading": "<PERSON> cron", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "Il cron è stato interrotto.", "alrCronStopFailed": "La richiesta di arresto di cron non è riuscita.", "alrCronRunning": "Il cron è stato avviato.", "alrCronRunFailed": "L'esecuzione di Cron non è riuscita.", "alrCronReset": "Il cron è stato ripristinato ai valori predefiniti.", "alrCronResetFailed": "Il reset di Cron non è riuscito.", "alrCronRestart": "Il cron è stato riavviato.", "alrCronRestartFailed": "La richiesta di riavviare il cron è fallita.", "alrCronUpdated": "Il cron è stato salvato con successo.", "alrCronUpdateFailed": "La richiesta di aggiornamento di cron è fallita.", "confirmRunCronDialog": "Si è sicuri di voler eseguire il cron selezionato?", "confirmStopCronDialog": "Si è sicuri di voler interrompere il cron selezionato?", "confirmResetCronDialog": "Volete davvero ripristinare le impostazioni di fabbrica di Cron?", "confirmRestartCronDialog": "Si è sicuri di voler riavviare il cron selezionato?", "confirmUpdateCronDialog": "Siete sicuri di voler cambiare lo stato di cron?", "alrProcessRestart": "Il processo Cron è stato riavviato!", "alrProcessRestartFailed": "La richiesta di riavvio del processo è fallita.", "confirmRestartProcessDialog": "Siete sicuri di voler riavviare l'intero processo cron? Attenzione, avverrà il riavvio completo di tutti i cron e dell'intero contesto.", "cronParams": "Parametri", "alrPresetLogFiltersLoadFailed": "Il caricamento dei filtri di registro preimpostati non è riuscito.", "timeRange": "Intervallo di tempo", "presetFilters": "<PERSON><PERSON><PERSON> preimpostati", "params": "Parametri", "authentication": "Autenticazione", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Nome del parametro", "authMethod": "metodo di autenticazione", "taskAlreadyEdited": "L'attività è già stata modificata da un altro utente.", "taskEditedByAnotherUser": "Un altro utente ha iniziato a modificare l'attività.", "tempAlreadyEdited": "Il modello è già stato modificato da un altro utente.", "tempEditedByAnotherUser": "Un altro utente ha iniziato a modificare il modello.", "test": "Test", "notInRightormat": "Formato non valido", "ttTableExportExcel": {"heading": "Esporta tabella", "body": "Esporta la tabella nel file xlsx"}, "ttTableExportCsv": {"heading": "Esporta tabella", "body": "Esporta la tabella nel file csv"}, "searchInSuspended": "Ricerca anche nei casi sospesi", "alrScriptDocsFailed": "Impossibile salvare la documentazione dello script.", "currentlyRunning": "Attualmente in esecuzione", "onStart": "All'avvio", "onEnd": "Alla fine", "onHand": "A mano", "onRecalc": "Al momento del ricalcolo", "onPull": "Prima di assumere il controllo", "yesterday": "<PERSON><PERSON>", "tomorrow": "<PERSON><PERSON>", "replyRecipient": "Destinatario della risposta", "bcRecipient": "Destinatar<PERSON> della blind copy", "copyRecipient": "Destinatario della copia", "emptyHe": "<PERSON><PERSON><PERSON>", "archivedLogs": "Registri archiviati", "basicMode": "Modalità di base", "expertMode": "Modalità esperto", "ttBasicMode": {"heading": "Modalità base", "body": "Nasconde alcuni elementi o opzioni del modulo"}, "ttExpertMode": {"heading": "Modalità esperto", "body": "Visualizza gli elementi o le opzioni nascoste nel modulo"}, "helpOverviewFolder": "È possibile includere la panoramica nella struttura della directory utilizzando degli slash.< br /><i>(ad es. Fatture/Tutte le fatture ricevute)</i>", "helpOverviewIncludeSimilar": "Se si seleziona, verranno visualizzati anche i casi di altre intestazioni di un modello.", "helpOverviewSysVars": "I campi contrassegnati con (sys) sono campi di sistema che fanno parte di ogni processo.", "customization": "Personalizzazione", "elementColor": "Colore dell'elemento", "fontColor": "Colore del carattere", "fontSize": "Dimensione del carattere", "bold": "In grassetto", "cursive": "Corsivo", "off": "Spent<PERSON>", "toPlan": "Piano", "alrMaintenanceComing": "All'ora {{time}} inizierà la manutenzione programmata del sistema. Si prega di salvare il proprio lavoro.", "timeoutHMS": "Timeout: (hh:mm:ss)", "eventw": "L'attività \"{{task}}\" del modello \"{{template}}\" attende questo evento", "waitsForEventTip": "Il caso è in attesa di un evento: \"{{event}}\"", "copyToMultiinstances": "Copia su più istanze", "showAsPreview": "Mostra un'anteprima", "alrPreviewAttachmentsFailed": "La visualizzazione di un'anteprima non è riuscita", "alrPreviewAttachmentsWrongFormat": "La visualizzazione di un'anteprima non è riuscita - formato di file non supportato", "previewNotAvailable": "L'anteprima del documento non è possibile a causa del tipo di documento.", "configuration": "Configurazione", "values": "Valori", "defaultValues": "Valori predefiniti", "ttSubscribeCv": {"heading": "Abbonamento panoramica", "body": "La panoramica selezionata vi sarà inviata via e-mail ogni giorno della settimana all'ora stabilita"}, "subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "Tempo", "externalLang": "Lingua esterna", "hdrStatusQ": "Si vuole davvero cambiare lo stato dell'intestazione?", "small": "<PERSON><PERSON><PERSON>", "medium": "Medio", "large": "Grande", "alrTemplTsksLoadFailed": "Il caricamento delle attività del modello non è riuscito.", "applyInTasks": "Applicare in attività", "caseStatuses": "Stato dei casi", "statuses": "Stati", "Manuals": "Manuali", "forReading": "Per la lettura", "forReadWrite": "Per leggere e scrivere", "addVersion": "Nuova versione", "size": "Dimensione", "prevWorkDay": "Giornata di lavoro precedente", "ttCreateTempVersion": {"heading": "Crea una nuova versione del modello", "body": ""}, "version": "Versione", "alrTempVersionsLoadFailed": "Le versioni dei modelli non sono state caricate.", "alrChangeTempVersionFailed": "Impossibile cambiare la versione del modello.", "alrCreateTempVersionFailed": "La creazione di una nuova versione del modello non è riuscita.", "confirmCreateTempVersion": "Siete sicuri di voler creare una nuova versione del modello?", "applyInAllTasks": "Applicare in tutti i compiti", "mandatoryVar": "Variabile richiesta", "emptyRequiredVarMessage": "Ops, variabile obbligatoria vuota", "duration": "<PERSON><PERSON>", "alrDynConditionsFailed": "Impossibile completare l'attività. Provare ad aggiornare la pagina o contattare l'Amministratore o l'Helpdesk.", "caseActivation": "Attivazione del caso", "average": "Media", "performanceLogs": "Registri delle prestazioni", "displayingOverview": "Visualizzazione della panoramica", "taskSolve": "Completamento del compito", "displayingCO": "VISUALIZZAZIONE DEL CASO", "printCreation": "Creazione di stampe", "entityId": "ID entità", "copyTask": "Copiare l'attività", "checkProcessCompletion": "Controllo del completamento del processo", "findingSolver": "Trovare un risolutore", "publicFiles": "File pubblici", "usage": "<PERSON><PERSON><PERSON><PERSON>", "serviceConsole": "Console di servizio", "selectAll": "Se<PERSON><PERSON>na tutti", "logos": "<PERSON><PERSON><PERSON>", "overviewWithTasks": "Panoramica con i compiti", "printIsReady": "La stampa è pronta", "alrChangelogLoadFailed": "Caricamento del changelog fallito.", "inJs": "In script", "ttCopyDtDefinition": {"heading": "Copia definizione tabella", "body": "Copia la definizione della tabella dinamica selezionata"}, "confirmCopyTableDefinition": "Si vuole davvero copiare la definizione della tabella?", "alrCopying": "Copiare...", "alrCopyFailed": "Copia non riuscita.", "fallback": "Fallback", "syncEnabled": "Sincronizzazione", "systemGuideNote": "Il contenuto della Guida del sistema non può essere modificato. Per visualizzare altri contenuti, rendere inattiva la Guida del sistema e copiarne il contenuto in una nuova Guida.", "alrAnotherUserLogged": "Un altro utente è connesso in un'altra finestra!", "userLocked": "L'utente è bloccato", "visInternalUserOnly": "Visibile solo agli utenti interni", "showSelectedOnly": "Mostra solo i selezionati", "clickToSelect": "Fare clic per selezionare", "restrictRoleAssignment": "Limitare l'assegnazione del ruolo per il ruolo", "restrictions": "Restrizioni", "restrictTableHandling": "Limitare la gestione delle tabelle", "toRole": "Al ruolo", "inCalcToHeader": "Nei calcoli per l'intestazione", "loginBtnColor": "Colore del pulsante di accesso", "certificates": "Certificati", "certificate": "Certificato", "certificateVar": "certificato", "tspSources": "Timestamp", "tspSource": "Timestamp", "confirmExpandDynRowsNewAssignments": "Attenzione, nuovo incarico! Le variabili non hanno assi impostati. Si vogliono allungare tutte le righe dinamiche?", "confirmExpandDynRows": "Siete sicuri di voler allungare tutte le file dinamiche?", "expandDynRows": "File dinamiche di allungamento", "visible": "Visibile", "cvcDbColumn": "Colonna delle fonti", "cvTableSource": "<PERSON>bella delle fonti", "uploadedFromFile": "<PERSON><PERSON><PERSON> da file", "appStatus": "Stato dell'app", "loadAll": "<PERSON><PERSON><PERSON> tutti", "ignoredUsers": "Utenti ignorati", "copyRolesFrom": "Co<PERSON>re i ruoli da", "disableFrontendStyles": "Non applicare stili automatici", "activate": "<PERSON><PERSON><PERSON><PERSON>", "confirmActivateCase": "Vuoi davvero attivare il caso?", "alrLackOfPerms": "Mancanza di autorizzazioni.", "alrSending": "Invio...", "sequences": "Sequenze", "seqName": "Nome della sequenza", "seqId": "ID sequenza", "seqLastRead": "Ultima lettura", "ttCopyRole": {"heading": "<PERSON><PERSON> ruolo", "body": "Crea una copia del ruolo selezionato"}, "fromCase": "<PERSON> caso", "includingData": "Compresi i dati", "choose": "Scegliere", "valueChange": "Variazione di valore", "updateInstances": "Aggiornare le variabili di istanza", "externalSource": "Fonte utente", "reports": "Rapporti", "confirmCopyReport": "Siete sicuri di voler copiare il rapporto selezionato?", "graphs": "<PERSON><PERSON>", "aggregation": "Aggregazione", "graphNew": "Grafico - nuovo", "confirmCopyGraph": "Siete sicuri di voler copiare il grafico selezionato?", "alrCopyGraphFailed": "Impossibile copiare il grafico.", "label": "<PERSON><PERSON><PERSON><PERSON>", "pie": "Pie", "line": "Linea", "dot": "<PERSON><PERSON><PERSON>", "bar": "Bar", "barGroups": "Bar - gruppi", "alrFailedGraphData": "Il grafico non è stato caricato.", "graphSetSharing": "Impostare la condivisione del grafico per ogni gruppo di utenti", "alrGraphPointsLoadFailed": "Caricamento dei punti grafici non riuscito.", "alrGraphNotFound": "Grafico non trovato.", "graphData": "<PERSON><PERSON> gra<PERSON>i", "pointsData": "Punti del grafico", "alrGraphSaveFailed": "Il salvataggio del grafico è fallito!", "graphPoint": "Punto grafico", "noOrder": "<PERSON><PERSON>un ordinamento", "refreshGraph": "Aggiorna il grafico", "viewSwitcher": "Filtro globale", "axisXglobalFilter": "Asse X - filtro globale", "axisXgroups": "Asse X - Gruppi", "axisXdata": "<PERSON><PERSON>", "axisYvalues": "<PERSON><PERSON>alori", "axisYcolors": "Asse Y - colori", "hrAgenda": "Agenda delle risorse umane", "userChange": "Modifica dell'utente", "newUser": "Nuovo utente", "usersCount": "Conteggio degli utenti", "confirmChangeUser": "Sei sicuro di voler cambiare utente?", "businessVariable": "Variabile commerciale", "casesCount": "Conteggio dei casi", "selected": "Selezionato", "selectedOnly": "Solo selezionati", "addCaseRightNewUser": "Aggiungi l'accesso al caso", "visFromTaskToPull": "Visibilità da un'attività da prendere", "toChangeConfigInfo": "Per modificare, eliminare il valore dal file local.js", "clickToChange": "Fare clic per cambiare", "currentValue": "<PERSON><PERSON> at<PERSON>", "sign": "Se<PERSON>", "validationProtocols": "Convalide", "plannedEvents": "Eventi", "elArchiv": "Archivio elettronico", "deletedDocs": "Eliminato", "signatures": "Firme", "ttArchive": {"heading": "Archivio elettronico", "body": ""}, "ttAddToZea": {"heading": "Aggiungi all'archivio elettronico", "body": ""}, "ttRemoveFromZea": {"heading": "<PERSON><PERSON><PERSON><PERSON> dall'archivio elettronico", "body": ""}, "ttZeaInfo": {"heading": "Validation", "body": ""}, "ttSignZea": {"heading": "Firma esterna", "body": ""}, "addToZea": "Aggiungi all'archivio elettronico", "removeFromZea": "Rimuovere dall'archivio elettronico", "reTimestampAfter": "Validità del timestamp generato (giorni)", "alrLoadFailed": "Caricamento fallito.", "replace": "Sost<PERSON><PERSON><PERSON>", "expireAt": "Scadrà", "result": "Risultato della convalida", "validatedAt": "Convalidato a", "refType": "<PERSON><PERSON><PERSON>", "eventType": "Tipo di azione", "errorMessage": "Messaggio di errore", "errorTimestamp": "Timestamp dell'errore", "errorCount": "Conteggio degli errori", "inFuture": "In futuro", "path": "Percorso del file di firma", "signedAt": "Creazione della firma", "dropZoneZeaCertificate": "Lasciate qui un certificato o fate clic per selezionare un file da caricare.", "authType": "Tipo di autenticazione", "basic": "Con nome e password", "byCert": "Per certificato", "alrMissingCertFile": "Caricare il certificato, per favore.", "replaceTo": "Sostituire a", "autoReTimestamp": "Timestamp automatico", "validate": "Convalidare", "lastUse": "Ult<PERSON> generato", "createdAt": "Creato presso", "updatedAt": "Aggiornato a", "certificateId": "ID certificato", "expectedCreationTime": "<PERSON><PERSON><PERSON>à aggiunto", "nextTSPSourceId": "ID timestamp successivo", "reTimestampAt": "Data e ora successiva", "timestampedAt": "Ultimo timestamp", "level": "<PERSON><PERSON>", "signatureB": "Firma di base", "signatureT": "Firma con data e ora", "signatureLt": "Firma con certificati di dati a lungo termine", "signatureLta": "Firma con dati a lungo termine e timestamp dell'archivio", "packaging": "Imballaggio", "enveloped": "Avvolte", "enveloping": "Imballaggio", "detached": "Indipendente", "algorithm": "Algoritmo", "uploadAsRevision": "Carica come revisione", "externalDisable": "Attivo solo per le firme in batch", "addToDms": "Aggiungi al DMS", "autoConvert": "Conversione automatica", "format": "Formato", "signatureType": "Tipo di firma", "signature": "Firma", "custom": "<PERSON><PERSON><PERSON><PERSON>", "batchSignDisabled": "Segno disabilitato", "tasId": "ID documento", "hashValue": "<PERSON><PERSON> hash", "hashType": "Tipo di funzione Hash", "confirmAddToArchive": "Volete davvero aggiungere qualcosa all'archivio?", "independentSignature": "Firma indipendente", "independentValidation": "Convalida indipendente", "failureTrue": "<PERSON> <PERSON>e", "failureFalse": "<PERSON><PERSON>", "confirmValidateDialog": "Si vuole davvero convalidare la firma?", "confirmRestartDialog": "Volete davvero azzerare gli errori degli eventi?", "verificationResult": "Risultato della verifica", "integrityMaintained": "Integrità mantenuta", "signatureFormat": "Formato della firma", "internalTimestampsList": "Elenco delle marche temporali interne", "signers": "<PERSON><PERSON><PERSON><PERSON>", "exhibitedBy": "Esposto da", "signedBy": "<PERSON><PERSON><PERSON> da", "validFrom": "<PERSON><PERSON>", "validUntil": "Valido fino a", "signitureType": "Tipo di firma", "signatureQualification": "Qualifica della firma", "signatureNoTimestamps": "La firma non contiene timestamp", "electronicSignature": "Firma elettronica", "electronicSeal": "Sigillo elettronico", "webSiteAuthentication": "Autenticazione del sito web", "QCP-n": "QCP-n: politica dei certificati per i certificati qualificati UE rilasciati a persone fisiche", "QCP-l": "QCP-l: politica dei certificati per i certificati qualificati UE rilasciati a persone giuridiche", "QCP-n-qscd": "QCP-n-qscd: politica di certificazione per i certificati qualificati UE rilasciati a persone fisiche con chiave privata correlata alla chiave pubblica certificata in un QSCD", "QCP-l-qscd": "QCP-l-qscd: politica dei certificati per i certificati qualificati UE rilasciati a persone giuridiche con chiave privata correlata alla chiave pubblica certificata in un QSCD", "QCP-w": "QCP-w: politica dei certificati per i certificati di autenticazione dei siti web qualificati dell'UE", "formOfReStamping": "Forma di ristampa", "individually": "Individualmente", "archiveAsPdf": "Archivio in formato PDF", "couldNotBeVerified": "Non è stato possibile verificare", "uses": "Numero di utilizzi", "countOfSignedDocuments": "Conteggio dei documenti firmati", "batchSignature": "Firma del lotto", "standaloneSign": "Firma individuale", "validateSignature": "Convalida della firma", "validateDoc": "Convalida del documento", "containsSignature": "Con<PERSON>e la firma", "reStamping": "Ristampa", "individualSignatures": "Firme individuali", "signatureLevel": "Livello di firma", "simpleReport": "Rapporto semplice", "detailedReport": "Rapporto dettagliato", "diagnosticReport": "Rapporto diagnostico", "etsiReport": "Rapporto ETSI", "TOTAL_PASSED": "OK", "TOTAL_FAILED": "Fallito", "INDETERMINATE": "Indeterminato", "FORMAT_FAILURE": "La firma non è conforme a uno degli standard di base.", "HASH_FAILURE": "L'hash dell'oggetto di dati firmato non corrisponde all'hash della firma.", "SIG_CRYPTO_FAILURE": "Non è stato possibile verificare la firma con la chiave pubblica del firmatario.", "REVOKED": "Il certificato di firma è stato revocato e vi sono prove che la firma è stata creata dopo la revoca.", "SIG_CONSTRAINTS_FAILURE": "Uno o più attributi della firma non corrispondono alle regole di convalida.", "CHAIN_CONSTRAINTS_FAILURE": "La catena di certificati utilizzata nel processo di convalida non è conforme alle regole di convalida dei certificati.", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "L'insieme dei certificati disponibili per la verifica delle stringhe ha causato un errore per un motivo non specificato.", "CRYPTO_CONSTRAINTS_FAILURE": "Uno degli algoritmi di verifica della firma è al di sotto del livello di sicurezza crittografica richiesto e la firma è stata acquisita dopo la durata dell'algoritmo.", "EXPIRED": "La firma è stata creata dopo la scadenza del certificato di firma", "NOT_YET_VALID": "Il momento della firma è precedente alla data di emissione del certificato di firma.", "POLICY_PROCESSING_ERROR": "Non è stato possibile elaborare il file dei criteri di convalida", "SIGNATURE_POLICY_NOT_AVAILABLE": "Il documento elettronico contenente i dettagli della politica di convalida non è disponibile.", "TIMESTAMP_ORDER_FAILURE": "Le restrizioni nell'ordine dei timestamp delle firme non vengono rispettate", "NO_SIGNING_CERTIFICATE_FOUND": "Il certificato di firma non può essere identificato", "NO_CERTIFICATE_CHAIN_FOUND": "Non è stata trovata alcuna catena di certificati per il certificato di firma identificato.", "REVOKED_NO_POE": "Il certificato di firma è stato revocato alla data/ora di convalida. Tuttavia, l'algoritmo di verifica della firma non è in grado di rilevare se l'ora della firma è precedente o successiva al periodo di revoca.", "REVOKED_CA_NO_POE": "È stata trovata almeno una catena di certificati, ma un certificato CA temporaneo è stato revocato.", "OUT_OF_BOUNDS_NOT_REVOKED": "Il certificato di firma è scaduto o non ancora valido alla data/ora di convalida e l'algoritmo di convalida della firma non può accertare che l'ora di firma rientri nell'intervallo di validità del certificato di firma. È noto che il certificato non è stat", "OUT_OF_BOUNDS_NO_POE": "Il certificato di firma è scaduto o non è ancora valido alla data/ora di verifica.", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "Uno degli algoritmi di verifica della firma è al di sotto del livello di sicurezza crittografica richiesto e non vi è alcuna prova che sia stato prodotto prima che l'algoritmo/chiave fosse considerato sicuro", "NO_POE": "Non vi è alcuna prova che l'oggetto firmato sia stato creato prima di un evento compromettente.", "TRY_LATER": "Non tutte le regole di convalida possono essere soddisfatte con le informazioni disponibili, ma potrebbe essere possibile farlo con informazioni aggiuntive sulla revoca che saranno disponibili in seguito.", "SIGNED_DATA_NOT_FOUND": "Non è possibile ottenere dati firmati", "GENERIC": "Altro motivo", "signatureFile": "File di firma", "validityDays": "<PERSON><PERSON><PERSON> di validità", "qualifiedHe": "Qualificato", "qualifiedIt": "Qualificato", "unqualifiedHe": "Non qualificato", "unqualifiedIt": "Non qualificato", "timeValid": "Tempo valido", "reason": "Motivo", "inTime": "nel tempo", "certificateQualification": "Certificato di qualifica", "guaranteedHe": "<PERSON><PERSON><PERSON><PERSON>", "guaranteedIt": "<PERSON><PERSON><PERSON><PERSON>", "fromQualifiedCert": "Da un certificato qualificato.", "basedOnQualifiedCertHe": "Sulla base di un certificato qualificato", "createdByQualifiedResHe": "<PERSON><PERSON><PERSON> da una risorsa qualificata", "basedOnQualifiedCertIt": "Sulla base di un certificato qualificato", "createdByQualifiedResIt": "<PERSON><PERSON><PERSON> da una risorsa qualificata", "qualification": "Qualifica", "confirmRemoveFromZeaDialog": "Si vuole davvero rimuovere la {{variable}} dall'archivio elettronico?", "noValidationReports": "Nessun rapporto di convalida", "noSignatures": "Nessuna firma individuale", "isHigherOrEqualThan": "Deve essere maggiore o uguale a", "isInZea": "Nell'archivio elett<PERSON>o", "startStamping": "Iniziare a timbrare", "reTimestampAfterMinutes": "minuti", "reTimestampAfterDays": "<PERSON>ior<PERSON>", "reTimestampAfterAll": "Validità del timestamp generato", "refId": "ID oggetto", "docWithoutAutoTimestampInfo": "Il documento verrà firmato una sola volta, senza inserimento automatico del timestamp.", "validationReports": "Storia della convalida", "docPath": "Percorso del documento", "addToArchiveInvalidSignatureError": "Non è stato possibile archiviare il file perché contiene una firma che non può essere verificata.", "signImmediately": "Firma immediatamente", "replaceInConfiguration": "Sostituire nella configurazione", "cancel": "Annullamento", "bulk": "In massa", "bulkCompletion": "Completamento in massa", "enableBulkCompletion": "Abilita il completamento in massa", "confirmCompleteTasks": "Volete davvero portare a termine i compiti?", "plannedMaintenance": "Manutenzione programmata", "notSpecified": "Non specificato", "bulkCompletionVars": "Variabili di completamento in serie", "alrBulkCompletionMultiTypeErr": "Solo i compiti dello stesso tipo possono essere completati in massa; è possibile utilizzare un filtro.", "notifications": "Notifiche", "alrTskAlreadyTakenSomeone": "Qualcun altro si è già assunto il compito.", "alrTskAlreadyTaken": "Il compito è già stato assunto.", "downloadBpmn": "scaricare il diagramma BPMN", "downloadSvg": "scaricare come immagine SVG", "displayForm": "Tipo di visuale", "selectedPreview": "viene visualizzata l'anteprima", "fixedHeight": "Altezza fissa (px)", "lastVersion": "Ultima versione", "separatedPreview": "Anteprima separata", "defaultZoom": "Zoom predefinito", "fixedPosition": "Posizione fissa", "percentInterval": "Inserire un numero intero da 0 a 5", "notPositiveNumber": "Inserire solo numeri positivi", "zoomDown": "Riduzione dello zoom", "zoomUp": "Aumentare lo zoom", "rotate": "<PERSON><PERSON><PERSON><PERSON>", "logTooBig": "Il registro è troppo grande per essere renderizzato.", "downloadLog": "Scarica il log", "confirmCopyCron": "Si vuole davvero copiare il cron selezionato?", "ttCopyCron": {"heading": "Copy cron", "body": ""}, "onlyWorkingDays": "Solo giorni lavorativi", "datesDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON> le date", "useView": "Vista d'uso", "dateWithoutTime": "Data senza ora", "timezone": "<PERSON><PERSON> orario", "roleRestriction": "Limitazione del ruolo", "headerRestriction": "Limitazione dell'intestazione", "ttSwitchDarkmode": {"heading": "Commutazione modalità luce/buio", "body": ""}, "advancedEditor": "Editor <PERSON><PERSON><PERSON><PERSON>", "externalId": "ID esterno", "passwordValidationMin": "La password è troppo corta. (lunghe<PERSON> minima: {{count}})", "passwordValidationMax": "La password è troppo lunga. (lunghezza massima: {{count}})", "passwordValidationUppercase": "La password deve contenere una lettera maiuscola. {{atLeast}}", "passwordValidationLowercase": "La password deve contenere una lettera minuscola. {{atLeast}}", "passwordValidationSymbols": "La password deve contenere un simbolo. {{atLeast}}", "passwordValidationDigits": "La password deve contenere un numero. {{atLeast}}", "passwordValidationLetters": "La password deve contenere una lettera. {{atLeast}}", "atLeast": "Almeno", "passwordValidationServiceErr": "La password non può essere modificata in questo momento.", "enableTasksHandoverRole": "Consentire sempre il passaggio di compiti e l'esecuzione di eventi agli utenti di questo ruolo.", "shredded": "<PERSON><PERSON><PERSON><PERSON>", "shredableVar": "Variabile distruttibile", "shredDocuments": "Distruggere i documenti", "shredInDays": "Distruzione in (giorni)", "fromBeginningOrendOfCase": "Dall'inizio alla fine del caso", "shredding": "Distruzione  ", "addColumn": "Aggiungi colonna", "unsupportedBrowser": "Se si apre TeamAssistant in un browser Internet Explorer non supportato, alcune funzioni potrebbero non essere disponibili.", "ingoreProcessRights": "Ignorare i diritti del caso", "cvHelpIngoreProcessRights": "La panoramica mostra sempre tutti i casi, indipendentemente dai diritti.", "upperLabel": "Posizionare la variabile proprio sotto il suo nome", "darkMode": "Modalità scura", "completedTasks": "Attività completate", "permissions": "<PERSON><PERSON><PERSON>", "caseVisibility": "Visibilità del caso", "visPerOrg": "Visibilità per unità organizzativa", "entity": "Entità", "staticRight": "<PERSON><PERSON><PERSON>", "dynamicRight": "<PERSON><PERSON><PERSON> din<PERSON>", "treeNodesAll": "<PERSON><PERSON>", "treeNodesMy": "Il mio", "activeQueries": "Interrogazioni attive", "query": "Interrogazione", "confirmCancelQuery": "È sicuro di voler annullare la query?", "alrQueryNotFound": "La query non è più stata trovata.", "completeAgenda": "Ordine del giorno completo", "lockedBusinessUsers": "Utenti aziendali b<PERSON>i", "structuredList": "<PERSON><PERSON><PERSON> strutturato", "ttCompetences": {"heading": "Gestione delle competenze", "body": ""}, "competences": "Competenze", "competence": "Competenza", "competenceDelVar": "competenza", "addCompetence": "Aggiungi competenza", "regularExpression": "<PERSON><PERSON><PERSON><PERSON> regol<PERSON>", "generationStatus": "Stato della generazione", "source": "Fonte", "historical": "Storico", "external": "Esterno", "nextDay": "il giorno successivo", "embeddedVideoNotSupported": "Spiacente, il vostro browser non supporta i video incorporati.", "alrSendingTestMailFailed": "L'invio di un'e-mail di prova non è riuscito.", "sent": "Inviato.", "mainColorEmail": "Colore principale dell'e-mail", "confirmResetColors": "È sicuro di voler ripristinare i colori?", "regularExpressions": "Espressioni regolari", "confirmDeleteLogo": "È sicuro di voler eliminare il logo?", "loginLogoLightTheme": "Logo della schermata di accesso (modalità luminosa)", "loginLogoDarkTheme": "Logo della schermata di accesso (modalità scura)", "competenceRegexHelper": "<ul>< li>% può essere usato come N di qualsiasi carattere</b> (equivalente *)< /li>< li><b>_</b> può essere usato come un carattere arbitrario (equivalente .)</li>< li> Si può usare <b>^</b> per sfuggire a questi caratteri speciali (equivalente \\)</li></u", "headerFont": "Font dell'intestazione", "evenRow": "Riga pari", "logo": "Logo", "widthForLogo": "Larghezza per il logo", "monthStart": "Inizio del mese", "monthEnd": "Fine del mese", "ttFavouriteType": "GET apre il link. POST invia un comando: ad esempio, quando si crea un caso, dove l'ID dell'intestazione del modello viene inviato nel corpo della richiesta (salvataggio nei preferiti tramite Nuovo caso).", "confirmEmptyMultiinstanceVariable": "Si è sicuri che questa multistanza non richieda una variabile su cui iterare?", "ttMenuPreview": "Configurazione del menu per ruoli utente (i ruoli più significativi vedono anche i pulsanti per i ruoli meno significativi). I pulsanti Nuovo caso e Dashboard sono invariati.", "menuPreview": "Anteprima del menu per il ruolo selezionato", "confirmResetMenu": "È sicuro di voler reimpostare il menu?", "alrFailedTasMenu": "La configurazione del menu TAS non è stata caricata!", "security": "<PERSON><PERSON><PERSON>", "userRestrictions": "Restrizioni per gli utenti (mostra)", "userRestrictionsProcesses": "Ignora le restrizioni utente sulle attività", "roleRestrictions": "Restrizioni di ruolo (mostra)", "orgUnitRestrictions": "Restrizioni dell'unità organizzativa (mostra)", "everyone": "<PERSON><PERSON>", "colleaguesOnly": "Solo colleghi", "directSubordinates": "<PERSON><PERSON><PERSON><PERSON>", "allSubordinates": "<PERSON>tti i subordinati", "none": "<PERSON><PERSON><PERSON>", "generalDocument": "Documento generale", "competenceRule": "Regola della competenza", "competenceRules": "Regole di competenza", "ruleName": "Nome della regola", "ttUseCompetenceRule": {"heading": "Applica la regola", "body": "Crea una competenza in base alla regola selezionata"}, "competenceText": "Testo di competenza", "competenceName": "Nome della competenza", "competenceReadOnlyInfo": "La competenza creata da una regola non può essere modificata", "xmlProcessImport": "Importazione del processo XML", "ttWidthForLogo": "Impostare la larghezza del logo e quindi inserire un logo. Non è possibile modificare la larghezza di un logo già inserito o predefinito.", "openCase": "Caso aperto", "importHistory": "Storia dell'importazione", "plannedImports": "Importazioni previste", "filePath": "Percorso del file", "cronId": "ID Cron", "taskResult": "Risultato del compito", "xmlFileSize": "Dimensione del file XML", "attachmentSize": "Dimensione dell'allegato", "lastEdit": "Ultima modifica", "timeCreated": "Te<PERSON> creato", "importId": "ID di importazione", "importAudit": "Importazione di audit", "finishedImports": "Importazioni finite", "insertNote": "Inserire la nota", "importXml": "Importazione di XML", "reImportXml": "Reimportare XML", "downloadXml": "Scarica XML", "downloadAttachment": "Scarica l'allegato", "skipXml": "Saltare l'XML", "note": "<PERSON>a", "attachmentName": "Nome dell'allegato", "importedCount": "Numero di importazioni", "retryCount": "Il numero di ripetizioni", "batchId": "ID dose", "copyPath": "Percorso di copia", "trace_id": "ID traccia", "cronRunId": "ID corsa", "taskQueue": "Task queue", "dissolveQueue": "Dissolve the queue", "taskQueueInitInfo": "This action created multiple tasks to be solved. Here you can change the order of their solving or remove them from the queue completely.", "cronRun": "Esecuzione di Cron", "ttMenuItemLabel": "Nome universale se non c'è traduzione. Se viene utilizzata una parola chiave di traduzione, questa viene tradotta automaticamente. Nomi predefiniti: compiti, casi, rapporti, relazioni, modelli, piani, utenti, ruoli, orgStructure, eventi, documenti, elArch", "tryDarkTheme": "Abbiamo notato che preferite la modalità scura. Fare clic per provarla in TAS.", "alrInvalidURL": "Formato URL non valido.", "alrInvalidHttps": "Formato URL non valido, deve iniziare con https://", "importVariables": "Importazione di variabili", "ttVariablesImport": {"heading": "Importazione di variabili", "body": "Viene selezionata e caricata una cartella con la definizione delle variabili"}, "classDiagram": "Diagramma delle classi", "createVar": "C<PERSON>re una variabile", "importObjectStates": "Importazione degli stati dell'oggetto", "unassigned": "Non assegnato", "sortVars": "Ordinamento", "fillNames": "Riempire i nomi", "ttFillNames": {"heading": "Riempi i nomi", "body": "Riempie i nomi vuoti di tutte le nuove variabili nel formato \"Classe.Attributo\" e ordina tutte le variabili"}, "ttSortVars": {"heading": "Sort", "body": "Ordina le variabili per classi e attributi"}, "ttRestore": {"heading": "R<PERSON><PERSON><PERSON>", "body": "Ripristina le variabili allo stato originale quando vengono importate da un file"}, "ttAddVarToBottom": {"heading": "Aggiungi variabile", "body": "Aggiunge una nuova variabile alla parte inferiore della pagina"}, "confirmRestoreForm": "Si vuole davvero ripristinare le variabili allo stato originale?", "selectClass": "Selezionare la classe", "importClassDiagram": "Importare un diagramma di classe", "continue": "Continua", "templateVars": "Variabili del modello", "newVars": "Nuove variabili", "objectState": "Stato dell'oggetto", "alrDynTableExists": "La tabella dinamica esiste già!", "overwriteExistDynTable": "Sovrascrivere la tabella dyn. esistente", "confirmCancelImport": "È sicuro di voler annullare l'importazione?", "alrDuplicateNames": "I dati contengono nomi duplicati.", "stateVar": "Variabile di stato", "importObjectStatesToDynTables": "Importare gli stati degli oggetti in tabelle dinamiche.", "defineObjectStatesVars": "Definire le variabili che contengono gli stati degli oggetti.", "change": "Cambiare", "classAndAttr": "Class and attribute", "clearQueue": "Clear queue", "sharing": "Condivisione", "data": "<PERSON><PERSON>", "open": "Aperto", "dataSource": "Fonte dei dati", "dataPoints": "<PERSON>unti dati", "dataSeries": "Serie di dati", "valueCol": "Colonna valore", "aggregationCol": "Colonna di aggregazione", "timeDimension": "Dimensione temporale", "columns": "Colonne", "week": "<PERSON><PERSON><PERSON>", "weekday": "giorno feriale", "monthVar": "mese", "overviewFilter": "Filtro generale", "globalFilters": "Filtri globali", "filterDefinition": "Definizione di filtro", "newFilter": "Nuovo filtro", "addFilter": "Aggiungi filtro", "filterOptions": "Opzioni di filtro", "addOption": "Aggiungi opzione", "graphPreview": "Anteprima del grafico", "alrGlobalFilterDownloadFailed": "I filtri globali non sono stati scaricati!", "alrGlobalFilterSaveFailed": "I filtri globali non sono riusciti a salvare!", "filterOption": "Filtro", "editFilter": "Modifica del filtro", "fillOptionsFromVar": "Riempire le opzioni dalla variabile", "fillOptionsDynamically": "Riempire le opzioni in modo dinamico", "filterOptionsFilledDynamically": "Dinamicamente dalla variabile", "dayOfMonth": "giorno del mese", "dateVar": "data", "group": "Gruppo", "ttDataSource": "Se si desidera inserire ogni punto del grafico separatamente, selezionare \"Punti dati\". Se si desidera che i punti vengano generati in base alla dimensione selezionata, selezionare \"Serie di dati\".", "ttDataSeriesAggregation": "Scegliere il tipo di aggregazione. Consente di creare informazioni di riepilogo dai record (casi).", "ttDataSeriesColumns": "Selezionare a turno tutte le colonne in base alle quali creare gruppi (aggregazioni) per calcolare i valori di sintesi.", "listOfFiltersIsEmpty": "L'elenco dei filtri è vuoto.", "fromVariable": "Da variabile", "showOptionsFromCount": "Mostra le opzioni (su {{count}})", "sum": "Somma", "minimum": "Minimo", "maximum": "<PERSON><PERSON>", "statistics": "Statistiche", "unfilled": "Non riempito", "globalFilterDescription": "Il filtro globale fornisce agli utenti del grafico opzioni per filtrare i dati di input del grafico. Tutte le opzioni di filtro possono essere definite in questa schermata.", "ttDelGraph": {"heading": "Elimina grafico", "body": "Elimina il grafico selezionato"}, "ttEditGraph": {"heading": "Modifica grafico", "body": "Consente di modificare il grafico selezionato"}, "ttCopyGraph": {"heading": "Copia grafico", "body": "Copia il grafico selezionato"}, "ttAddGraph": {"heading": "Aggiungi grafico", "body": "Consente di definire un nuovo grafico"}, "axisXName": "Nome asse X", "axisYName": "Nome asse Y", "showValues": "Mostra i valori", "defaultOption": "Opzione predefinita", "yearStart": "<PERSON><PERSON><PERSON> anno", "yearEnd": "Fine anno", "thisMonth": "<PERSON>o mese", "lastMonth": "Il mese scorso", "thisYear": "Quest'anno", "lastYear": "<PERSON>'anno scorso", "scheduledTasks": "Attività programmate", "scheduled": "In programma", "dueDateStart": "Data di inizio", "lastRescheduled": "Ultima riprogrammazione", "reschedule": "Riprogrammazione", "alrTasksRescheduling": "Riprogrammare le attività...", "alrTasksRescheduled": "I compiti sono stati riprogrammati.", "alrTasksRescheduleFailed": "Impossibile riprogrammare le attività.", "onlyCurrentOrFutureDates": "Solo date at<PERSON><PERSON> o future", "passwordValidations": "Politica sulle <PERSON>", "readonlyConfigInfo": "Il valore è di sola lettura", "alrTasksCountFailed": "Il numero di compiti non è riuscito a scoprirlo.", "confirmActivateTasks": "Sei sicuro di voler attivare le attività selezionate?", "confirmSuspendTasks": "Sei sicuro di voler sospendere le attività selezionate?", "tskOffset": "Variabile di pianificazione", "workWeek": "Settimana lavorativa", "agenda": "Agenda", "noEventsInRange": "Non ci sono eventi in questa gamma", "activitiesDesc": "Descrizione delle attività", "allShort": "<PERSON><PERSON>", "numberOfEvents": "Numero di eventi", "weekNumber": "numero della settimana", "cannotBeEdited": "Non può essere modificato", "cannotBeMoved": "Non può essere spostato", "alrTempVarSaveSameNameFailed": "Esiste già una variabile con questo nome predefinito, inserisci un nome diverso.", "maxUsersCountRole": "Il numero massimo di utenti nel ruolo", "unlimitedAssignLeaveBlankInfo": "Per le assegnazioni illimitate, lasciare il campo vuoto.", "cvOwner": "Proprietario della panoramica", "changePassword": "Cambia la password", "passwordExpired": "La password è scaduta. Cambiare la password.", "passwordWillExpire": "La tua password scadrà presto. Inserisci una nuova password.", "userParameters": "Parametri utente", "filterSortingHelper": "L'ordinamento in base a una o più colonne in un filtro disabilita la possibilità di ordinare manualmente le colonne direttamente nella tabella.", "importUsers": "<PERSON><PERSON><PERSON> u<PERSON>", "importRoles": "<PERSON><PERSON><PERSON> ruoli", "existingEntityRows": "Righe con entità già esistenti (possono essere sovrascritte)", "fileRow": "Riga file", "existingEntityRowsMultiple": "Righe con entità che esistono già più di una volta (non verranno importate)", "importOrgUnits": "Importa unità organizzative", "structureImportExport": "Importazione/esportazione struttura", "fillAttributes": "Riempi attributi", "structurePreview": "Anteprima della struttura", "invalidRowsForImport": "Righe non valide (dati obbligatori mancanti)", "duplicateRowsForImport": "<PERSON>igh<PERSON> con dati corrispondenti duplicati (non verranno importate)", "newEntityRows": "Righe con nuove entità da importare", "existingNameRowsForImport": "<PERSON>ighe con nomi già esistenti su altre entità (non verranno importate)", "overwriteExisting": "Sovras<PERSON>ri<PERSON> es<PERSON>", "structurePreviewHelper": "L'anteprima della struttura mostra due diverse situazioni: importare solo nuove organizzazioni o importare organizzazioni sia nuove che esistenti che verranno sovrascritte. Tutte le modifiche rispetto alla struttura corrente sono contrassegnate in rosso.", "validateAndShowPreview": "Convalida e visualizza in anteprima", "uploadNewFile": "Carica un nuovo file", "userStatus": "Stato utente", "importedFile": "File importato", "pairUsersBy": "As<PERSON>cia utenti per", "assignOrgBy": "Assegna all'organizzazione da", "pairRolesBy": "Acco<PERSON>ia ruoli per", "pairUnitsBy": "Accoppia unità per", "unitHierarchyCol": "Colonna gerarchia unità", "dontAssign": "Non assegnare", "infoImportDataValidated": "ATTENZIONE: i dati sono stati appena convalidati a causa di modifiche alle impostazioni. Ti consigliamo di tornare indietro e controllare la nuova anteprima di importazione.", "assignUserRolesMethod": "Come assegnare i ruoli agli utenti", "assignUserRolesMethodHelp": "Come assegnare i ruoli: aggiungere ai ruoli già assegnati o sostituire completamente i ruoli attualmente assegnati con nuovi ruoli.", "existingRolesForImport": "<PERSON><PERSON><PERSON> esistenti (possono essere sovrascritti)", "existingRoleNamesForImport": "Ruoli con nomi già esistenti per altri ruoli (non saranno importati)", "newRolesForImport": "Nuovi ruoli da importare", "userRolesForImport": "<PERSON><PERSON><PERSON> con ruoli utente da assegnare", "nonExistentUsersForImport": "<PERSON><PERSON><PERSON> con utenti inesistenti (i ruoli non saranno assegnati)", "multipleExistingUsersForImport": "<PERSON>ighe con più di un utente esistente (i ruoli non saranno assegnati)", "invalidOrgsForImport": "Righe non valide (dati obbligatori mancanti o gerarchia errata)", "keepOriginal": "Mantieni originale", "assignOrgByHelp": "Se selezioni una colonna dal file, puoi specificare la classificazione dell'organizzazione per utenti nuovi ed esistenti. Se selezioni un'organizzazione specifica, tutti gli utenti importati o aggiornati verranno assegnati a questa organizzazione.", "creatingRoles": "<PERSON><PERSON><PERSON> ruoli", "assigningRolesToUsers": "Assegnazione di ruoli agli utenti", "newUsers": "<PERSON><PERSON>vi utenti", "existingUsers": "Utenti esistenti", "fromFile": "Da file", "alrCsvXlsxUploadWrongExtension": "Carica solo file con estensione *.csv o *.xlsx", "importNewAndExisting": "Importa nuove entità e sovrascrivi quelle esistenti", "importNewOnly": "Importa solo nuove entità", "importNewAndExistingRoles": "Importa nuovi ruoli e sovrascrivi quelli esistenti", "importNewRolesOnly": "Importa solo nuovi ruoli", "importRolesHelper": "Impostazioni per l'importazione dei ruoli stessi. L'assegnazione dei ruoli agli utenti è regolata da quanto impostato in \"Associa utenti per\" e si applica sempre ai ruoli nuovi ed esistenti.", "statisticsColorHelper": "Se i colori non vengono selezionati manualmente, o se ci sono meno colori selezionati rispetto alle colonne, i colori mancanti vengono generati automaticamente. I colori generati non contengono mai sfumature scure o troppo chiare, queste possono essere selezionate solo manualmente.", "caseService": "<PERSON><PERSON><PERSON>", "taskService": "<PERSON><PERSON><PERSON>", "editTasks": "Modifica attività", "editCases": "Modifica casi", "deleteTasks": "Elimina attività", "deleteCases": "Elimina casi", "serviceOperationsInfo": "Segna e compila le variabili che vuoi modificare.", "erased": "Cancellato", "statusErrored": "Errore", "serviceOperations": "Operazioni di servizio", "runCalcsOnStart": "Esegui calcoli all'avvio", "taskReactivation": "Riattivazione delle attività", "taskCompletion": "Completamento delle attività", "caseReactivation": "Riattivazione dei casi", "caseCompletion": "Completamento dei casi", "openTask": "<PERSON><PERSON>", "changeEntity": "Cambia entità", "selectTableColumns": "Seleziona le colonne della tabella", "parentCase": "<PERSON><PERSON>o padre", "ownerOrganization": "Organizzazione proprietaria", "confirmTaskReactivation": "Sei sicuro di voler riattivare le attività selezionate?", "confirmCaseReactivation": "Sei sicuro di voler riattivare i casi selezionati?", "confirmTaskCompletion": "Sei sicuro di voler completare le attività selezionate?", "confirmCaseCompletion": "Sei sicuro di voler completare i casi selezionati?", "selectAllFilterMustBeActive": "Almeno un filtro deve essere attivo per selezionare tutti gli elementi.", "changeEntities": "Cambia entità", "disabledDifferentTemplates": "Impossibile modificare perché le entità non provengono dallo stesso modello.", "actions": "Azioni", "taskTemplateId": "ID modello attività", "caseTemplateId": "ID modello caso", "actionInfoCheckLogs": "L'azione verrà eseguita in background, controlla i log.", "alrServiceOperationsColumnsFailed": "Salvataggio dell'impostazione delle colonne delle operazioni di servizio non riuscito.", "confirmResetSelectedCols": "Sei sicuro di voler reimpostare le colonne della tabella salvata?", "instanceVars": "Variabili di istanza", "usrId": "ID utente", "orgId": "ID organizzazione", "titlePrefix": "<PERSON><PERSON><PERSON> titolo", "titleSuffix": "Suffisso titolo", "accessRoleId": "ID ruolo di accesso", "maxAssigns": "Numero massimo di assegnazioni", "client": "Cliente", "bigValue": "Grande valore", "unitId": "ID unità", "roleId": "ID ruolo", "paramId": "ID parametro", "varId": "ID variabile", "parentId": "ID genitore", "openUser": "Apri utente", "openRole": "<PERSON>i ruolo", "openUnit": "Unità aperta", "units": "Unità", "managerId": "ID gestore", "externalStatus": "Stato esterno", "additionalId": "ID aggiuntivo", "parentIc": "IC genitore", "companyIc": "IC aziendale", "textValue": "<PERSON><PERSON> testo", "dateValue": "Valore data", "numberValue": "Valore numerico", "loginCount": "Conteggio accessi", "externalLogin": "Accesso esterno", "badLoginCount": "Numero di accessi errati", "passwordLastChange": "Ultima modifica della password", "solverEvaluation": "Valutazione del solutore", "solverWillBe": "Il risolutore sarà", "possibleSolvers": "Possibili risolutori", "selectReferencePerson": "Seleziona una persona di riferimento", "evaluateSolver": "Valuta il risolutore", "referenceUserForEval": "Persona di riferimento per la valutazione", "andOthers": "...e altri", "showLess": "...mostra meno", "alrSessionExpired": "La sessione è scaduta, si prega di effettuare nuovamente il login.", "mailPromptlyInfo": "L'utente riceve continuamente notifiche una tantum su nuove attività, dove sono il risolutore. Questi avvisi verranno inviati solo se l'attività non è stata risolta per {{minutes}} minuti dalla sua attivazione.", "mailPullInfo": "L'utente riceve continuamente notifiche una tantum su nuove attività disponibili per l'abbonamento e l'utente è il loro possibile risolutore. La notifica si spegne al momento dell'attivazione dell'attività data all'interno del flusso di lavoro.", "mailTotalInfo": "L'utente riceve periodicamente una panoramica con le attività da completare, di cui sono il risolutore. Se l'attività non ha un risolutore diretto, il proprietario del processo viene avvisato. Se l'utente è rappresentato, la notifica viene ricevuta dal suo rappresentante.", "mailEscalationInfo": "L'utente riceve periodicamente una panoramica con le attività da completare che hanno superato la scadenza. Vengono informati se sono il supervisore dell'attività (e non il suo risolutore allo stesso tempo) o sono il direttore diretto dell'utente che è il risolutore. Se l'attività non ha un risolutore, il proprietario del processo è considerato il supervisore. Se l'utente è rappresentato, la notifica menziona chi è l'attuale rappresentante.", "calcSourceOverwriteWarning": "Dopo il salvataggio, il sorgente viene sovrascritto con la sintassi ES6!", "changeStatus": "Cambia stato", "confirmChangeEmailStatus": "Vuoi davvero cambiare lo stato delle email selezionate?", "logInAgain": "Accedi di nuovo", "migrations": "Migrazioni", "launchDate": "Data di inizio", "stepName": "Nome passo", "runId": "Esegui ID", "clone": "<PERSON><PERSON>", "confirmDeleteCron": "Vuoi davvero eliminare il cron selezionato?", "alrCronDeleted": "Cron è stato eliminato!", "wantToContinueQ": "Vuoi continuare?", "valueCannotBeEntered": "Il valore non può essere inserito", "processingQueues": "Code di elaborazione", "pause": "<PERSON><PERSON><PERSON><PERSON>", "fillOptionsFromVarHelper": "Le opzioni di filtro possono essere compilate solo da variabili di tipo DT, DL, LT, LD, LN e D, che non consentono la selezione multipla.", "defaultTemplateName": "Nome modello predefinito", "defaultTaskName": "Nome attività predefinito", "defaultVariableName": "Nome variabile predefinito", "variableName": "Nome variabile", "alrNoDataFound": "<PERSON><PERSON><PERSON> dato trovato", "ttProcessingQueuesInfo": "Le code di elaborazione sono disabilitate.\n<PERSON> at<PERSON>, impostare almeno una delle configurazioni \"scaling.queue.*.enabled\" su true.", "businessUsers": "Utenti aziendali", "completeHrAgenda": "Agenda HR completa", "usageStatsByHeader": "Statistiche di utilizzo per intestazione", "usageStatsByOrgUnit": "Statistiche di utilizzo per unità org.", "usageStatsByUser": "Statistiche di utilizzo per utente", "completedTasksNum": "Numero di attività completate", "startedProcessesNum": "Numero di casi avviati", "ideHelp": "Premi Ctrl + <PERSON><PERSON> nell'editor per vedere i suggerimenti, premi di nuovo per una guida più dettagliata. Premi F1 per visualizzare tutti i comandi e le scorciatoie da tastiera disponibili. Per ulteriori informazioni, consultare <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>la documentazione dell'editor</a>.", "restHelp": "Inserisci l'URL per uno dei servizi tabella TAS (es. '/tasks/mine') e dopo aver caricato il servizio seleziona le colonne della tabella che vuoi visualizzare nel contenitore.", "defaultGraphName": "Nome grafico predefinito", "graphName": "Nome grafico", "ttStatistics": {"heading": "Statistiche", "body": ""}, "defaultAxisXName": "Nome asse X predefinito", "defaultAxisYName": "Nome asse Y predefinito", "defaultFilterName": "Nome filtro predefinito", "filterName": "Nome filtro", "defaultOptionName": "Nome opzione predefinito", "optionName": "Nome opzione", "defaultOverviewName": "Nome predefinito della panoramica", "overviewName": "Nome panoramica", "eventName": "Nome evento", "wantToOverrideEs6": "Se vuoi davvero risc<PERSON>e, scrivi <b>ES6</b>", "processArchivation": "archiviazione del processo", "processUnarchivation": "processo di annullamento dell'archiviazione", "resendEmail": "Inoltra email", "alrFailedSendEmail": "Impossibile inviare l'email", "ttResendEmail": {"heading": "Inoltra email", "body": "Invia di nuovo una notifica email inviata in precedenza. I destinatari possono essere modificati o aggiunti."}, "addCurrentScreenToFavourite": "Aggiungi la schermata corrente ai tuoi preferiti", "attachmentAdd": "Aggiungi un documento", "createNewCase": "Creazione di un nuovo caso", "moreLanguage": "Altre varianti di lingue", "notesAdd": "Aggiungi una nota", "notesNew": "Nuova nota", "removeCurrentScreenFromFavourite": "R<PERSON><PERSON><PERSON> lo schermo corrente dal preferito", "setDashboard": "Modifica la dashboard", "chooseFromCases": "Seleziona tra i casi", "folders": "<PERSON><PERSON><PERSON>", "newFolderBtn": "Nuova cartella", "documentInfo": "Informazioni sul documento", "userInfo": "Informazioni utente", "deleteImage": "Elimina immagine", "profilePhoto": "Foto del profilo", "profilePhotoCaption": "Utilizza una foto in formato jpeg, jpg, png o gif.", "updatePhoto": "Aggiorna foto", "mailNotifications": "Notifiche e-mail", "userPreferences": "Preferenze utente", "userSettings": "Impostazioni utente", "allVices": "<PERSON>tti i sostituti", "createVice": "<PERSON><PERSON><PERSON> sostit<PERSON>", "editVice": "Modificare i sostituti", "viceTip": "Il sostituto consente di passare l'agenda a un collega", "emptyDataMessage": "Non c'è altro qui", "addFirstNote": "Aggiungi la prima nota", "noResultsFor": "<PERSON><PERSON><PERSON> ris<PERSON>ato per:", "noCurrentTasks": "Nessuna attività corrente", "checkYourSearch": "Controlla la tua ricerca e riprova.", "noFavOverviews": "Nessuna panoramiche preferite", "favOverviewsTip": "Aggiungi una panoramica ai tuoi preferiti con una stella", "noHiddenOverviews": "Nessuna panoramiche nascoste", "addOverview": "Aggiungi panoramica", "hidden": "Nascosto", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON>", "removeItem": "Sei sicuro di voler rimuovere {{variable}}?", "changePicture": "Cambia immagine", "saveFilter": "<PERSON>va filtro", "addAnotherVice": "Aggiungi un altro sostituto", "saveVice": "Salva i sostituto", "firstLastName": "Nome e cognome", "taskInfo": "Informazioni sull'attività", "emptyFavsTip": "Aggiungi i preferiti con il pulsante", "saveAndClose": "Salva e chiudi", "usersCanEditOverview": "Gli utenti possono modificare la panoramica", "assignedUsers": "Utenti assegnati", "assignedOrgUnits": "Unità organizzative assegnate", "assignedRoles": "<PERSON><PERSON><PERSON> assegnati", "otherLangVariants": "Altre varianti linguistiche", "moveToSharing": "Sposta in condivisione", "insertDocumentsPerm": "L'utente ha il permesso di inserire documenti e note", "saveNewPassword": "Salva nuova password", "confirmSubscription": "Conferma dell'iscrizione", "subscriptionCaption": "La panoramica selezionata ti sarà inviata via email all'orario impostato.", "by": "Da", "frequency": "Frequenza", "termination": "Terminazione", "ofTheMonth": "Del mese", "endOnDate": "Termina il", "endAfter": "Termina dopo", "onlyOnWorkingDays": "Solo nei giorni lavorativi", "occurrences": "occorrenze", "dayOfWeekBy": "<PERSON><PERSON><PERSON> setti<PERSON>", "calendarDayBy": "<PERSON>ior<PERSON> del calendario", "dateBy": "data", "byDate": "Per data", "byOccurrenceCount": "Per numero di occorrenze", "infinitely": "All'infinito", "dayOfMonthAdornment": ". giorno del mese", "ordinalAdornment": ".", "toDateBeforeFromError": "La data 'A' non può essere precedente alla data 'Da'", "vice": "Sostituto", "previewShown": "Anteprima mostrata", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "hideBtn": "Nascondi", "userView": "Vista utente", "adminView": "Vista amministratore", "or": "O", "overlappingVicesError": "I sostituti non possono sovrapporsi", "fileVar": "file", "nodeVar": "nodo", "uploadDifferentFile": "Carica un file diverso", "uploadedFile": "File caricato", "refreshPage2": "Aggiorna pagina", "refreshPageCaption": "Aggiorna la pagina nel tuo browser per continuare.", "ttCopy": {"heading": "Copia", "body": "Permette di copiare l'elemento selezionato con la possibilità di modificare alcuni parametri."}, "alrError_INVALID_CSV_MAPPING": "Non trovata colonna CSV '%s' nella mappatura dell'evento. Contattare l'amministratore delle applicazioni.", "documentPreview": "Anteprima del documento", "moveUp": "<PERSON><PERSON>rere verso l'alto", "moveDown": "<PERSON><PERSON><PERSON><PERSON> verso il basso", "moveToFilter": "Sposta nel filtro", "moveToSorting": "Sposta all'ordinamento", "addSorting": "Aggiungi ordinamento", "cancelFilters": "<PERSON><PERSON><PERSON> filt<PERSON>", "docUploadedImmediately": "Il documento verrà caricato immediatamente", "moreOptions": "Altre opzioni", "docSearchPlaceholder": "Es. fattura.pdf...", "tasksSearchPlaceholder": "<PERSON><PERSON><PERSON> Inser<PERSON>ci una nuova fattura...", "docUploadedImmediatelyPrivate": "Il documento verrà caricato immediatamente come privato", "takeTsk": "Accedi compito", "tasksActive": "Compiti attivi", "subprocesses": "Sottoprocessi", "cancelAuthorization": "Annulla autorizzazione", "cancelAuthorizationConfirm": "Sei sicuro di voler annullare l'autorizzazione del dispositivo?", "linkMobileApp": "Collega l'app mobile", "mobileApp": "App mobile", "scanThisQr": "Scansiona questo codice QR con il tuo dispositivo mobile.", "scanningQr": "Scansione codice QR in corso. Attendi prego.", "deviceName": "Nome dispositivo", "newDeviceName": "Nuovo nome dispositivo", "registrationDate": "Data di registrazione", "lastLogin": "Ultimo accesso", "mobileNotifications": "Notifiche cellulare", "disableMobileNotification": "Disattivazione delle notifiche sul cellulare", "newQrCode": "Nuovo codice QR", "inactiveScanQr": "Inattivo: scansiona il codice QR.", "enableNotifications": "Abilita le notifiche", "tip": "Suggerimento: {{message}}", "alrFavContainerAlreadyExists": "Il contenitore dei preferiti esiste già.", "addGraph": "Aggiungi grafico", "newRow": "Nuova riga", "confirmSetDefaultDashboard": "Sei sicuro di voler rendere la dashboard corrente quella predefinita per tutti gli utenti?", "changeMayAffectAllUsers": "Questa modifica può influire su tutti gli utenti.", "noOverviewsTip": "Crea una nuova panoramica utilizzando il pulsante \"Aggiungi panoramica\"", "removeFromHidden": "<PERSON><PERSON><PERSON><PERSON> da nascosto", "last7Days": "Ultimi 7 giorni", "last14Days": "Ultimi 14 giorni", "last30Days": "Ultimi 30 giorni", "lastCalendarMonth": "Ultimo mese calendario", "lastQuarter": "Ultimo trimestre", "last12Months": "Ultimi 12 mesi", "lastCalendarYear": "Ultimo anno calendario", "noFilterSet": "<PERSON><PERSON><PERSON> filtro impostato", "noSortingSet": "Nessun ordinamento impostato", "deleteGroup": "Elimina gruppo", "newGroup": "Nuovo gruppo", "operators": "Operatori", "withActiveTask": "Con attività attiva", "withoutActiveTask": "<PERSON>za attività attiva", "withNoTerm": "Senza termine", "withTerm": "Con termine", "securityAndAuthentication": "Sicurezza e autenticazione", "dataIntegrationAndManagement": "Integrazione e gestione dei dati", "appManagementAndConfig": "Gestione e configurazione delle app", "monitoringAndMaintenance": "Monitoraggio e manutenzione", "adminSearchPlaceholder": "Ad esempio, File pubblici...", "authenticationAdminDescription": "Opzioni di autenticazione dell'utente", "certificatesAdminDescription": "Certificati per TAS", "elasticsearchAdminDescription": "Integrazione con Elasticsearch", "xmlProcessImportAdminDescription": "Importazione dei processi XML utilizzando il record cron XMLProcessImport.js", "structureImportExportAdminDescription": "Import/export della struttura organizzativa, degli utenti e dei ruoli", "dmsAttributesAdminDescription": "Elenco degli attributi dei documenti in DMS", "dynTablesAdminDescription": "Archiviazione dei dati in tabelle dinamiche", "csvAdminDescription": "Manipolazione con file CSV nell'applicazione", "configurationAdminDescription": "Configurazione dell'applicazione", "settingsAdminDescription": "Impostazioni di identificazione dell'azienda e altre attività amministrative", "logsAdminDescription": "Gestione e visualizzazione dei log dell'applicazione", "migrationsAdminDescription": "Migrazioni di dati e configurazioni dell'applicazione", "guidesAdminDescription": "<PERSON><PERSON> e guide per gli utenti", "schemeAdminDescription": "Schema di colori, logo e altri elementi nell'applicazione", "sequencesAdminDescription": "Gestione delle sequenze utilizzate nei modelli", "serviceConsoleAdminDescription": "Comandi amministrativi dell'applicazione tramite la console del servizio", "serviceOperationsAdminDescription": "Gestione completa delle operazioni di servizio", "scriptsAdminDescription": "Gestione degli script utilizzati ripetutamente attraverso vari modelli", "appStatusAdminDescription": "Informazioni sullo stato attuale dell'applicazione", "usageStatsAdminDescription": "Visualizzazione delle statistiche sull'utilizzo dell'applicazione", "maintenanceAdminDescription": "Impostazioni di manutenzione ed esecuzione di attività di manutenzione", "scheduledTasksAdminDescription": "Gestione di tutti i compiti pianificati", "publicFilesAdminDescription": "Gestisci file e documentazione pubblici", "cronsAdminDescription": "Automazione delle attività regolari", "hrAgendaAdminDescription": "Gestione dell'agenda utente all'interno delle risorse umane", "emailsQueueAdminDescription": "Gestione code email e tutte le comunicazioni email da TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Aggiunta elemento ai preferiti non riuscita", "alrRemoveFavItemFailed": "Rimozione dell'elemento dai preferiti non riuscita", "alrAddHiddenItemFailed": "Impossibile aggiungere l'elemento nascosto", "alrRemoveHiddenItemFailed": "Impossibile rimuovere l'elemento nascosto", "display": "Visualizzazione", "compact": "Compatta", "standard": "Standard", "comfortable": "<PERSON><PERSON><PERSON><PERSON>", "exportTo": "Esporta in", "adminMenuTip": "Aggiungi i tuoi elementi preferiti nell'amministrazione. Cliccando sulla stella verrà visualizzato l'elemento proprio qui.", "editorDocumentation": "Documentazione dell'editor", "addSection": "Aggiungi sezione", "insertSection": "Inserisci sezione", "section": "Sezione", "sections": "Sezioni", "toTop": "In alto", "toEnd": "Alla fine", "alrSectionNotBeEmpty": "La sezione non deve essere vuota", "confirmDeleteSection": "Vuoi davvero eliminare la sezione?", "sectionVarsMoveAllTasks": "Le variabili in tutte le attività verranno spostate dalla sezione rimossa a variabili senza sezione.", "sectionVarsMove": "Le variabili verranno spostate dalla sezione rimossa alle variabili senza una sezione.", "actionCannotUndone": "Questa azione non può essere annullata.", "overviewOfAllNews": "Panoramica di tutte le notizie", "copyOverview": "Panoramica copia", "create": "<PERSON><PERSON>", "copyExistingOverview": "Copia panoramica esistente", "selectOverview": "Seleziona Panoramica", "chooseFromOverviews": "<PERSON><PERSON><PERSON> dalle panoramiche...", "selectTemplate": "<PERSON>a modello", "chooseFromAvailableTemplates": "Scegli tra i modelli disponibili...", "loginWithUsernamePassword": "Accedi con nome utente e password", "signInWithCorporateIdentity": "Accedi con l'identità aziendale", "whatsNewInTAS": "Cosa c'è di nuovo in TAS?", "whatsNewInTASDescription": "Aggiornamenti, nuove funzionalità, suggerimenti, trucchi e tutto ciò che devi sapere.", "justOpen": "Solo aperto", "editOverview": "Modifica panoramica", "noGraphsTip": "Crea un nuovo grafico utilizzando il pulsante \"Aggiungi grafico\"", "noDocumentsTip": "Aggiungi un documento all'attività o utilizzando il pulsante \"Aggiungi\"", "noFilesTip": "Aggiungi un nuovo file utilizzando il pulsante \"Aggiungi\"", "less": "<PERSON><PERSON>", "notContains": "Non contiene", "factorySettings": "Impostazioni di fabbrica", "previewCollapsedNavMenu": "Anteprima del menu di navigazione confezionato", "previewExpandedNavMenu": "Anteprima del menu di navigazione disimballato", "logoForCollapsedNavMenu": "Logo per il menu di navigazione confezionato", "logoForExpandedNavMenu": "Logo per menu di navigazione disimballato", "organisationLogo": "Logo dell'organizzazione", "pickLogoOrganisation": "Selezione del logo per l'organizzazione", "addLogo": "Aggiungi logo", "clickForAddLogoOrDrop": "Fare clic per aggiungere il logo o rilasciare il file qui", "useLogoSizeMin": "Usa un logo di dimensioni minime", "logoForLightTheme": "Logo per modalità luminosa", "logoForDarkTheme": "Logo per la modalità oscura", "notEquals": "Non è uguale", "sharedWithMe": "Condiviso con me", "myOverview": "La mia panoramica", "getMobileAppText": "Scarica l'app mobile dall'app store", "noDocuments": "<PERSON><PERSON><PERSON> documento", "noNotes": "<PERSON><PERSON><PERSON> nota", "noFiles": "Nessun file", "addFirstDocument": "Aggiungi il primo documento", "killed": "<PERSON><PERSON><PERSON>", "chooseNewLogo": "Seleziona un nuovo logo", "function": "Funzione", "groupFunction": "Funzione tra i gruppi", "mobileAppAuthFailed": "Autenticazione dell'app mobile non riuscita.", "currentDocumentVersion": "Versione attuale del documento", "csp": "Politica sulla sicurezza dei contenuti", "documentsDelete": "Elimina documenti", "confirmDocumentsDelete": "Sei sicuro di voler eliminare i documenti selezionati?", "confirmDocumentsDownload": "Vuoi scaricare i documenti selezionati?", "firstNum": "primo {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Scarica documenti", "caseLogs": "Registri dei casi", "archiveCases": "<PERSON><PERSON><PERSON> casi", "archive": "Archivia", "unarchive": "De-archivia", "confirmArchiveCases": "Vuoi davvero archiviare i casi selezionati?", "archiveInDays": "Archivia in (giorni)", "archived": "Archiviato", "archivedx": "<PERSON><PERSON><PERSON><PERSON>", "alrArchivingCase": "Il caso sta venendo archiviato...", "alrCaseArchived": "Il caso è stato archiviato.", "alrLackOfPermsToArchiveCase": "Non hai i permessi sufficienti per archiviare il caso.", "alrArchiveCaseFailed": "Archiviazione del caso fallita.", "alrUnarchivingCase": "Il caso sta venendo de-archiviato...", "alrCaseUnarchived": "Il caso è stato de-archiviato.", "alrLackOfPermsToUnarchiveCase": "Non hai i permessi sufficienti per de-archiviare il caso.", "alrUnarchiveCaseFailed": "De-archiviazione del caso fallita.", "byUser": "Per utente", "byAgenda": "Per agenda", "agendaHandover": "Passaggio agenda", "activeUsers": "<PERSON><PERSON><PERSON> attivi", "lockedUsers": "<PERSON><PERSON><PERSON> b<PERSON>i", "allUsers": "<PERSON><PERSON> gli utenti", "inactiveUsers": "Utenti inattivi", "hrAgendaSearchPlaceholder": "<PERSON><PERSON><PERSON> <PERSON> ...", "completeAgendaHandover": "Consegna completa dell'agenda", "handoverCases": "Casi di consegna", "handoverTasks": "Attività di passaggio", "handoverVars": "Variabili di passaggio", "changeTaskOwner": "Cambia risolutore di attività", "confirmHandover": "Conferma il passaggio", "filterCasesByHeaderTip": "Puoi filtrare tutti i casi sotto lo stesso header nella colonna Intestazione.", "userAgendaSelectedHandover": "Trasferimento dell'agenda <b style=\"color: {{color}};\">selezionata</b> dell'utente", "userAgendaCompleteHandover": "Trasferimento dell'agenda <b style=\"color: {{color}};\">completa</b> dell'utente", "confirmAgendaHandover": "Sei sicuro di voler trasferire l'agenda selezionata ({{selected}}) all'utente <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Sei sicuro di voler trasferire l'agenda <b>selezionata</b> dell'utente <b>{{user}}</b> all'utente <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Sei sicuro di voler trasferire l'agenda <b>completa</b> dell'utente <b>{{user}}</b> all'utente <b>{{newUser}}</b>?", "refreshSessionTitle": "La sessione TAS verrà terminata tra {{minutes}} minuti.", "refreshSessionCaption": "Fai clic su \"Continua a lavorare\" per continuare senza interruzioni.", "continueWorking": "Continua a lavorare", "sessionExpiredCaption": "Fai clic su \"Accedi di nuovo\" per tornare alla schermata di accesso.", "loginExpired": "Ti abbiamo disconnesso dopo un lungo periodo di inattività.", "confirmArchiveCase": "Vuoi davvero archiviare il caso selezionato?", "isLowerOrEqualThan": "Deve essere inferiore o uguale a", "confirmUnarchiveCase": "Sei sicuro di voler annullare l'archiviazione del caso selezionato?", "addCaseRightNewUserTooltip": "Se non selezioni questa opzione, il nuovo utente verrà sostituito nella variabile aziendale, ma non avrà accesso al caso.", "canBeViced": "Sostituito da", "canVice": "Sostituendo", "backgroundColor": "Colore di sfondo", "defaultDashboardView": "Anteprima della dashboard predefinita", "colorScheme": "Schema di colori", "displaySelectionAsTags": "Visualizza la selezione come tag", "displayAsPassword": "Visualizza come password", "sideBySide": "Fianco a fianco", "copyAssignmentFromTask": "Copia assegnazione dal compito", "toTask": "Al compito", "copyTaskAssignmentWarning": "L'assegnazione nel compito non è vuota, vuoi sovrascriverla?", "copyToOtherTasks": "Copia in altri compiti", "noteScriptsNotApplied": "Nota: gli script non sono applicati", "generateRecHistory": "Mostra nelle attività attive e nella cronologia", "leaveFormerRoles": "Mantenere i ruoli precedenti", "includeCompetences": "Includere le competenze", "copyRoles": "Co<PERSON><PERSON> i ruoli", "userIsActive": "L'utente è attivo", "systemUser": "Utente di sistema", "copyRolesFromUser": "Co<PERSON><PERSON> i ruoli dall'utente", "assignedRolesOverview": "Panoramica dei ruoli assegnati", "copyRolesInfo": "L'utente fa parte delle competenze.< br />Queste competenze non saranno copiate immediatamente, ma saranno generate:", "notificationOn": "Fissato", "notificationOff": "<PERSON><PERSON><PERSON>", "onNotification": "Notifica", "offNotification": "Notifica", "page": "pagina", "fromTo": "Da - a", "isAnyOfValue": "È un valore da", "notcontains": "non contiene", "notequals": "irregolare", "fromto": "da - a", "isanyofvalue": "è un valore da", "alrNoteToggleVisibiltyFailed": "Nascondi/mostra nota non riuscito", "alrNoteHideOnEditFailed": "Nascondere la nota originale non è riuscito", "hiddenShe": "Nascosta", "showHiddenNotes": "Mostra note nascoste", "alrNoteEdited": "La versione modificata della nota è stata salvata", "notesEdit": "Modifica nota", "displayName": "Nome visualiz<PERSON>", "clientDateFormat": "Formato data", "defaultByLanguage": "Predefinito per lingua", "restKeysOptionsNotUpToDate": "Selezione dei valori obsoleta - ricaricare il servizio.", "invalidValue": "Valore non valido", "ended": "Terminato", "exportAllActive": "Esporta tutti gli attivi", "alrScriptsLoadFailed": "Caricamento degli script non riuscito.", "scriptsImport": "Importa script", "doImport": "Importare", "alrImportingScripts": "Importazione degli script in corso...", "alrScriptsImported": "Gli script sono stati importati.", "alrScriptsImportFailed": "L'importazione degli script non è riuscita.", "removeAll": "Rimuovere tutto", "alrNoScriptsToImport": "Nessuno script da importare.", "activateAll": "Attiva tutto", "alrNoPermsToEditNoteInVice": "Non si hanno i permessi per modificare la nota come sostituto.", "alrNoPermsToToggleNoteVisibilityInVice": "Non si hanno i permessi per nascondere/mostrare la nota come sostituto.", "plusMore": "altri", "variableAlignment": "Allineamento variabile", "variableAlignmentHelp": "Influisce sull'allineamento del valore della variabile all'interno del modulo dell'attività.", "variableAlignmentLeft": "Sinistra", "variableAlignmentRight": "Destra", "tasksMineAndToPull": "<PERSON><PERSON> + <PERSON> prendere", "myDevice": "Il mio dispositivo", "deleteLogo": "Elimina logo", "namingFilter": "Nome filtro", "exceptionsToRegularSchedule": "Eccezioni al programma regolare", "noExceptions": "Nessuna eccezione", "specificDates": "Date specifiche", "dateFromTo": "Data da - a", "weekdayCap": "<PERSON><PERSON><PERSON> setti<PERSON>", "specificDayBy": "<PERSON><PERSON><PERSON>", "yearsBy": "anni", "timed": "Programmato", "firstDayOfMonth": "Primo giorno del mese", "lastDayOfMonth": "<PERSON><PERSON><PERSON> giorno del mese", "firstDayOfYear": "Primo giorno dell'anno", "lastDayOfYear": "Ultimo giorno dell'anno", "addDate": "Aggiungi data", "newPlan": "Nuovo piano", "addAnother": "Aggiungi un altro", "startTime": "Ora di inizio", "endTime": "<PERSON>a di fine", "inTimeFromTo": "dalle {{from}} alle {{to}}", "dayOfMonthBy": "<PERSON><PERSON><PERSON>", "cWorkDays": "gior<PERSON>", "cWeeks": "<PERSON><PERSON><PERSON><PERSON>", "cMonths": "mesi", "cYears": "anni", "everyWeek": "<PERSON><PERSON> set<PERSON>", "everyYear": "ogni anno", "inMonth": "nel mese", "everyDay": "<PERSON><PERSON> giorno", "seqIdEdit": "Modifica ID sequenza", "allowMultiselectSearchRight": "Consenti la ricerca nell'assegnazione", "doubleHeightForContent": "Altezza doppia per il contenuto", "alrNoVariablesMappingToImport": "Nessuna mappatura delle variabili da importare.", "alrVariablesMappingImportLoadFailed": "Caricamento della mappatura delle variabili da importare non riuscito.", "variablesMappingImport": "Importazione della mappatura delle variabili", "useAllMappings": "Usa tutte le mappature", "doExportVariablesMapping": "Esporta mappatura delle variabili", "alrImportingVariablesMapping": "Importazione della mappatura delle variabili in corso...", "alrVariablesMappingImported": "La mappatura delle variabili è stata importata.", "alrVariablesMappingImportFailed": "Importazione della mappatura delle variabili non riuscita.", "alrVariablesMappingImportedPartially": "La mappatura delle variabili è stata importata solo parzialmente. Alcune variabili non sono state trovate.", "alrEditorHintsLoadFailed": "Impossibile caricare i suggerimenti dell'editor.", "addTable": "Aggiu<PERSON>i tabella", "confirmDynTablesDelete": "Vuoi davvero eliminare le tabelle dinamiche selezionate?", "dynTablesDelete": "Elimina tabelle dinamiche", "addRow": "Aggiungi riga", "preview": "Anteprima", "columnDelete": "Elimina colonna", "editRow": "Modifica riga", "addingNewColumn": "Aggiunta nuova colonna", "addingNewRow": "Aggiunta nuova riga", "columnsRename": "Rinomina colonne", "rowCellValues": "Valori delle celle della riga", "saveDynTableName": "Salva nome tabella dinamica", "saveDynTableNameQ": "Salva nome tabella dinamica?", "saveDynTableNameWarning": "Attenzione, assicurati che la modifica del nome della tabella non influisca sui calcoli esistenti nei modelli.", "rowMove": "Sposta riga", "alrCsvParsingErr": "Errore durante l'analisi del CSV!", "addFirstTableColumn": "Aggiungi la prima colonna della tabella", "my": "<PERSON><PERSON>", "license": "Licenza", "licenses": "Licenze", "addLicense": "Aggiungi licenza", "licenseResult": "Risultato licenza", "alrLicenceResultLoadingFailed": "Impossibile caricare il risultato della licenza.", "licensesAdminDescription": "Gestione licenze", "uploadByDragging": "Carica file trascinando.", "uploadByDraggingAnywhere": "Carica un file trascinandolo ovunque nello spazio.", "assignVariable": "Assegna variabile", "confirmDeleteSectionName": "Sei sicuro di voler eliminare la sezione <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Attenzione: la sezione verrà eliminata per tutte le attività interessate, comprese le variabili.", "tasksAffected": "Attività interessate", "varSearchPlaceholder": "Ad esempio fatturazione …", "enlarge": "Ingrandire", "show": "Mostrare", "shrink": "<PERSON><PERSON><PERSON><PERSON>", "hide": "Nascondere", "doValidate": "Convalidare", "phoneNumber": "Numero di telefono", "textLength": "Lunghezza del testo", "when": "quando", "to2": "a", "that": "che", "dynCondBuilderBlockFunctionDescShow": "Mostra la variabile se la condizione è soddisfatta.", "dynCondBuilderBlockFunctionDescHide": "Nasconde la variabile se la condizione è soddisfatta.", "dynCondBuilderBlockFunctionDescChange": "Cambia il valore della variabile se la condizione è soddisfatta.", "dynCondBuilderBlockFunctionDescValidate": "Convalida il valore della variabile.", "addCondition": "Aggiungi condizione", "operator": "operatore", "equals": "uguale a", "greaterthan": "maggiore di", "greaterorequal": "maggiore o uguale a", "lessthan": "minore di", "lessorequal": "minore o uguale a", "demoCode": "Codice demo", "code": "Codice", "confirmDeleteConditions": "Vuoi davvero eliminare tutte le condizioni (incluso lo script)?", "validationErrorMessage": "Messaggio di errore di validazione", "alrScriptToStructuredBlockConversionFailed": "La conversione dello script in blocco strutturato non è riuscita.", "alrStructuredBlockToScriptConversionFailed": "La conversione del blocco strutturato in script non è riuscita.", "alrScriptToBuilderConversionFailed": "La conversione dello script in builder non è riuscita.", "alrBuilderToScriptConversionFailed": "La conversione da builder a script non è riuscita.", "dynCondBuilderBlockFunctionDescScript": "Blocco script per condizioni dinamiche.", "convertToStructuredBlock": "<PERSON><PERSON><PERSON> in blocco strutturato", "convertToScript": "<PERSON><PERSON><PERSON> in script", "dynCondBuilderBlockWatchVarsLabel": "Esegui al cambiamento (watchVars)", "variables": "Variabili", "copyToOthers": "Copia ad altri", "sectionName": "Nome della sezione", "newSectionName": "Nome della nuova sezione", "testIt": "Test", "addAdjacentSection": "Aggiungi una sezione vicina", "addAdjacentSectionBelow": "Aggiungi la sezione vicina di seguito", "selectExistingSection": "Scegli una sezione esistente", "renameSectionWarning": "Avviso: la sezione verrà rinominata in tutte le attività del modello.", "warning2": "Avviso", "copyAssignmentToTask": "Copia l'assegnazione all'attività", "copyAlsoConditions": "Copia e condizioni", "copyAssignmentToTaskWarning": "Avviso: le condizioni di assegnazione e possibilmente dinamiche nell'attività selezionata saranno riscritte.", "importFromOtherTask": "Importa da un'altra attività", "startFromScratch": "Inizia dall'inizio", "howToStartAssignments": "Come vuoi iniziare ad assegnare variabili?", "selectTaskToImport": "Seleziona l'attività per l'importazione", "confirm": "<PERSON><PERSON><PERSON>", "selectTaskToTest": "Per selezionare un'attività per il test", "toTestSaveChanges": "Le modifiche dovrebbero essere conservate per i test.", "variableAssignmentTest": "Assegnazione del test delle variabili", "viewAsMobile": "Visualizza come sul cellulare", "viewAsPc": "Visualizza come sul PC", "emptySpace": "<PERSON><PERSON><PERSON>", "variableAssignments": "<PERSON><PERSON><PERSON><PERSON> variabili", "allowCompletionOnChangeOf": "Consenti il completamento al cambiamento di", "dynCondBuilderBlockFunctionDescRead": "Cambia la modalità della variabile in \"solo lettura\" se la condizione è soddisfatta.", "dynCondBuilderBlockFunctionDescWrite": "Cambia la modalità della variabile in \"lettura e scrittura\" se la condizione è soddisfatta.", "dynCondBuilderBlockFunctionDescMust": "Cambia la modalità della variabile in \"obbligatoria\" se la condizione è soddisfatta.", "dynCondBuilderBlockFunctionDescSolve": "Consente il completamento dell'attività al cambiamento della variabile specificata, se la condizione è soddisfatta.", "newsManagement": "Gestione delle notizie", "newsManagementAdminDescription": "Gestione delle notizie nell'applicazione", "addNewsPost": "Aggiungi notizia", "newPost": "Nuovo post", "news": "Notizie", "basicInfo": "Informazioni di base", "publicationPlanning": "Pianificazione della pubblicazione", "displayToUsers": "Visualizzazione agli utenti", "displayLocation": "Posizione di visualizzazione", "newsPostContent": "Contenuto della notizia", "postTitle": "Titolo del post", "newsManagementPostDetailPhoneNumberTooltip": "Numero di telefono da visualizzare nei dettagli della notizia", "newsManagementPostDetailEmailTooltip": "Email da visualizzare nei dettagli della notizia", "customUrlLink": "Link URL personalizzato", "newsManagementPostDetailCustomUrlLinkTooltip": "Link URL personalizzato da visualizzare nei dettagli della notizia", "stateAfterSaving": "Stato dopo il salvataggio", "newsPostStateActive": "Attivo", "newsPostStateInactive": "Non attivo", "newsPostStatePlanned": "Pianificato", "endNewsPostOnSpecificDate": "Termina la notizia a una data specifica", "sendNewsPostViaEmail": "Invia notizia via email", "priorityNewsPost": "Notizia prioritaria", "newsManagementPostDetailPriorityNewsTooltip": "Ad esempio, per annunciare una manutenzione o un cambiamento nel flusso di lavoro", "newsPostEndDate": "Data di fine notizia", "pickNewsPostDisplayToOrgUnits": "A quali unità organizzative mostrare la notizia?", "pickNewsPostDisplayToRoles": "A quali ruoli mostrare la notizia?", "pickNewsPostDisplayToUsers": "A quali utenti mostrare la notizia?", "pickNewsPostDisplayOnTemplate": "Su quale modello mostrare la notizia?", "pickNewsPostDisplayOnHeaders": "Su quali intestazioni mostrare la notizia?", "pickNewsPostDisplayOnTasks": "Su quali attività mostrare la notizia?", "pickNewsPostDisplaySubOptionsHelperText": "Seleziona prima il modello su cui vuoi mostrare la notizia.", "newsTagsManagement": "Gestione dei tag delle notizie", "newsTagsManagementAdminDescription": "Gestione dei tag delle notizie nell'applicazione", "addTag": "Aggiungi tag", "tags": "Tag", "publicationDate": "Data di pubblicazione", "contacts": "<PERSON><PERSON><PERSON>", "avaibleUntil": "Disponibile fino a", "published": "Pubblicato", "newsSinceLastVisitAmount": "Totale {{amount}} notizie dall'ultima visita", "noNews": "Nessuna notizia", "createNewTag": "Crea nuovo tag", "tagName": "Nome del tag", "alrTagSaved": "Il tag è stato salvato.", "alrTagSaveFailed": "Il salvataggio del tag non è riuscito.", "confirmDeleteTag": "Vuoi davvero eliminare il tag \"{{tagName}}\"?", "alrPostSaved": "Il post è stato salvato.", "alrPostSaveFailed": "Il salvataggio del post non è riuscito.", "alrLoadingTagsFailed": "Il caricamento dei tag non è riuscito.", "confirmDeletePost": "Vuoi davvero eliminare il post \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "Vuoi davvero eliminare i post selezionati?", "post": "Post", "alrPostLoadFailed": "Il caricamento del post non è riuscito.", "alrTagDeleted": "Il tag è stato cancellato.", "alrTagDeleteFailed": "L'eliminazione del tag non è riuscita.", "alrPostDeleted": "Il post è stato cancellato.", "alrPostDeleteFailed": "L'eliminazione del post non è riuscita.", "alrPostsDeleted": "I post selezionati sono stati cancellati.", "alrPostsDeleteFailed": "L'eliminazione dei post selezionati non è riuscita.", "alrTempTasksLoadFailed": "Il caricamento delle attività del modello non è riuscito.", "rolesRestriction": "Restrizioni di ruolo", "usersRestriction": "Restrizioni per gli utenti", "orgUnitsRestriction": "Restrizioni dell'unità organizzativa", "alrPriorityNewsLoadFailed": "Il caricamento delle notizie prioritarie non è riuscito.", "moreInfo": "Maggiori informazioni", "tas5Info": "TAS 5.0 è qui ...", "totalNewsAmount": "Totale {{amount}} notizie", "alrNewsContainerPostsLoadFailed": "Caricamento dei post per il contenitore delle notizie non riuscito.", "alrTaskNewsLoadFailed": "Caricamento delle notizie per l'attività non riuscito.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "La data di pubblicazione deve essere precedente alla data di fine della notizia.", "alrNotificationsNewsLoadFailed": "Caricamento delle notizie per le notifiche non riuscito.", "moreNews": "Altre notizie", "newsManagementPostDetailConfirmSavingWillSendMail": "Il salvataggio del post comporterà l'invio di un'e-mail a tutti gli utenti a cui è destinato il post. Sei sicuro di voler salvare il post?", "mailNewsNotification": "Notifica e-mail sulle notizie", "mailNewsNotificationInfo": "L'utente riceve continuamente notizie che gli sono destinate.", "alrRefreshingConfig": "Aggiornamento della configurazione...", "alrConfigRefreshed": "Configurazione aggiornata con successo", "alrConfigRefreshFailed": "Aggiornamento della configurazione non riuscito", "ttRefreshConfig": {"heading": "Ripristina la configurazione da tutte le fonti", "body": ""}, "getMobileAppTextQr": "Scarica l'app mobile dall'app store o scansiona il codice QR", "dateStart": "Data di inizio", "dateEnd": "Data di fine", "tas_forms_generated": "Numero di moduli generati automaticamente"}