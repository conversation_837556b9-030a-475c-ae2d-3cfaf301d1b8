{"1st": "1.", "2nd": "2.", "3rd": "3.", "4th": "4.", "AddToAllTasks": "Přidat do všech úkolů", "OfVariable": "Z proměnné", "RemoveFromAllTasks": "Odstranit ze všech úkolů", "TaskOwnerWhichInVar": "Řešiteli, který je uveden v proměnné", "action": "Ak<PERSON>", "active": "Aktivní", "activeShe": "Aktivní", "activePl": "Aktivní", "activity": "Aktivita", "activityType": "Typ aktivity", "actualEnd": "Skutečný konec", "actualSolver": "Stávající <PERSON>", "actualStart": "Skutečný začátek", "actualTsks": "Aktuální úkoly", "actualize": "aktualizovat", "add": "<PERSON><PERSON><PERSON><PERSON>", "addAttribute": "Přidat atribut", "addCurrentScreenToFavourite": "Přidat aktuální obrazovku do oblíbených", "addOrgUnit": "Přidat org. jednotku", "addPlan": "Přidat plán", "addPrintTemplate": "<PERSON><PERSON><PERSON><PERSON> tisku", "addRole": "Přidat roli", "addRule": "<PERSON><PERSON><PERSON><PERSON>", "addTemp": "<PERSON><PERSON><PERSON><PERSON>", "addTsk": "Přidat <PERSON>", "addUser": "Přidat <PERSON>", "addUserSomething": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> už<PERSON> vybrané {{variable}}:", "addVariable": "<PERSON><PERSON><PERSON><PERSON>", "after": "Po", "afterTermTasks": "<PERSON><PERSON> po term<PERSON>", "all": "Všechny", "allFiles": "Všechny soubory", "allMustBeMet": "Všechny musí být splněné", "allMyTasks": "Všechny mé ú<PERSON>ly", "allSubOfPlanGuar": "Všichni podřízení garanta plánu", "allTasksWithNoTerm": "Všechny úkoly bez termínu", "allTasksWithTerm": "Všechny úkoly s termínem", "allTemps": "Všechny šablony", "allowMultiple": "Povolit výběr více položek", "allowSelectAll": "Povolit výběr všech položek", "allsupOfPlanGuar": "Všichni nadřízení garanta plánu", "alrBlockingAction": "Práv<PERSON> pro<PERSON><PERSON><PERSON> a<PERSON>, kterou je nutné dokončit! Vydržte prosím...", "alrActionNameNotDefined": "<PERSON><PERSON><PERSON> „{{actionName}}“ nen<PERSON> def<PERSON>.", "alrActionNotDefined": "<PERSON><PERSON><PERSON>.", "alrApiUrlMissing": "Chybí zdroj dat k tabulce.", "alrAssigningTsk": "Přiřazuji úkol...", "alrAssignmentFailed": "Přiřazení se nepodařilo ul<PERSON>ž<PERSON>.", "alrAtrFailed": "Atribut se nepodařilo s<PERSON>.", "alrAttachDeleteFailed": "Dokument se nepoda<PERSON>.", "alrAttachDeleted": "Dokument byl smaz<PERSON>!", "alrAttachDeleting": "Ma<PERSON>u dokument...", "alrAttachDownloadFailed": "Dokument se nepodařilo s<PERSON>.", "alrAttachDownloaded": "Dokument byl sta<PERSON>en!", "alrAttachDownloading": "Stahuji dokument...", "alrAttachMetaFailed": "Metadata dokumentu se nepodařilo <PERSON>.", "alrAttachOrNotesCountFailed": "Počet dokumentů nebo poznámek se nepodařilo zjistit.", "alrAttachSaveFailed": "Dokument se nepoda<PERSON>.", "alrAttachSaved": "Do<PERSON>ment byl p<PERSON>án!", "alrAttachTooBig": "Dokument je p<PERSON> velký! Přesáhl velikost {{maxUploadSize}} MB.", "alrAttrDataFailed": "Data atributu se nepodařilo nač<PERSON>t.", "alrAttrFailed": "Data atributu se nepodařilo <PERSON>", "alrAttrSaveFailed": "Při ukládání atributu se vyskytla chyba!", "alrAttrsLoadFailed": "Atributy se nepodařilo načíst.", "alrAttachRestored": "Dokument obnoven.", "alrAttachRestoreFailed": "Obnovení dokumentu se nezdařilo.", "alrAttachRestoring": "Obnovuji dokument...", "alrAuthMethodsFailed": "Autentizační způsoby se nepodařilo načíst.", "alrBadLogin": "Zadali jste špatné j<PERSON>no nebo he<PERSON>lo.", "alrBlockedPopups": "Zřejmě máte blokovaná vyskakovací okna.", "alrCaseDataLoadFailed": "Data případu se nepodařilo nač<PERSON>t.", "alrCaseDeleteFailed": "Případ se nepodař<PERSON>.", "alrCaseDeleted": "<PERSON><PERSON><PERSON><PERSON> byl s<PERSON>!", "alrCaseDeleting": "<PERSON><PERSON><PERSON> případ...", "alrCaseNameLoadFailed": "Nepodařilo se načíst název případu.", "alrCaseNoRights": "Nemáte oprávnění zobrazit případ č. {{id}}.", "alrCaseNotFound": "Pří<PERSON> – byl pravděpo<PERSON>b<PERSON>ě s<PERSON>.", "alrCaseOverviewFailed": "Data případu pro čtení (CASE OVERVIEW) se nepodařilo načíst.", "alrCaseSuspended": "<PERSON><PERSON><PERSON><PERSON> byl us<PERSON>!", "alrCaseVarsLoadFailed": "Proměnné procesu se nepodařilo <PERSON>.", "alrCaseWakeUpFailed": "Nepodařilo se probudit případ.", "alrCaseWakedUp": "<PERSON><PERSON><PERSON><PERSON> byl probuzen!", "alrCaseWakingUp": "Probouzím případ...", "alrColsWidthsSettingsFailed": "Nastavení šířky sloupců se nepodařilo uložit.", "alrConnToServerFailed": "Sel<PERSON>o připojení k <PERSON>u.", "alrConnectionDataLoadFailed": "Data spojení se nepodařilo načíst.", "alrConnectionDeleteFailed": "Spojení se nepodařilo s<PERSON>.", "alrConnectionDeleted": "Spojení bylo s<PERSON>áno!", "alrConnectionSaveFailed": "Spojení se nepodařilo ulož<PERSON>!", "alrContainerNotFound": "Kontejner nebyl nalezen.", "alrCsvDownloaded": "Soubor csv byl stažen!", "alrCvNotFound": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>.", "alrDashboardSettingsFailed": "Nastavení dashboardu se nepoda<PERSON>ilo <PERSON>.", "alrDefaultDashboardLoadFailed": "Defaultní dashboard se nepoda<PERSON><PERSON>.", "alrDefaultDashboardSaved": "Defaultní dashboard byl ul<PERSON><PERSON><PERSON>!", "alrDeleteFailed": "Při mazání se vyskytla chyba.", "alrDeleted": "Smazáno!", "alrDeleting": "Mažu...", "alrDiagramDataLoadFailed": "Nepodařilo se načíst data k sestavení grafu.", "alrDiagramEditToSave": "Diagram není možné uložit a převést na šablonu – obsahuje více jak jeden proces! Upravte prosím diagram tak, aby o<PERSON><PERSON><PERSON><PERSON> jed<PERSON> proces, nebo importujte jiný .bpmn soubor.", "alrDiagramInitFailed": "Nepodařilo se inicializovat diagram.", "alrDiagramMissingTaskName": "Doplňte prosím názvy ke všem úkolům.", "alrDiagramErrors": "Diagram obsahu<PERSON> ch<PERSON>. Prosím opravte je a zkuste jej uložit znovu.", "alrDiagramNotValid": "Není validní XML podle oficiální specifikace BPMN 2.0!", "alrDiagramProcessCount": "Diagram nebude možné uložit a převést na šablonu – obsahuje více jak jeden proces!", "alrDiagramSaveFailed": "Při ukládání šablony se vyskytla chyba!", "alrDiagramTsksDeleteFailed": "Při mazání úkolů se vyskytla chyba!", "alrDiagramUnchanged": "Šablona zůstala nezměněna.", "alrDmsColsLoadFailed": "Sloupce pro DMS se nepodařilo načíst.", "alrDocsColumnsIdsFailed": "ID pro sloupce tabulky dokumenty se nepodařilo načíst.", "alrDocumentAdding": "Přidávám dokument...", "alrDynListsDataLoadFailed": "Data dynamických listů se nepodařilo načíst.", "alrDynTableColsDataFailed": "Data k Sloupcům dynamické tabulky se nepodařilo nač<PERSON>.", "alrDynTableDataLoadFailed": "Data dynamické <PERSON> se nepodařilo <PERSON>.", "alrDynTableNotFound": "Dynamická tabulka nebyla nalezena.", "alrDynTablesDataLoadFailed": "Data dynamických tabulek se nepodařilo načíst.", "alrCalcScriptsDataLoadFailed": "Globální skripty výpočtů se nepodařilo načíst.", "alrEditValues": "Opravte prosím nesprávně vyplněné hodnoty.", "alrEventSaveFailed": "Událost se nepodařilo <PERSON>.", "alrEventSaved": "Událost byla uložena!", "alrEventSaving": "Ukládám událost...", "alrEventTriggered": "Událost byla s<PERSON>štěna!", "alrExcelDownloaded": "Soubor xlsx byl stažen!", "alrExportCompleted": "Export byl dokon<PERSON>en.", "alrExportFailed": "Export se nezdařil.", "alrExportPreparing": "Připravuji export...", "alrFailed": "Akce se nezdařila.", "alrFailedCalendarTask": "Nepodařilo se nahrát úkoly do kalendáře.", "alrFailedCreatePrint": "Nepodařilo se vytvořit sestavu pro tisk.", "alrFailedDLTotalCount": "U dynamického listu {{label}} nebyl specifikován total_count, proto byly načteny všechny záznamy.", "alrFailedData": "Data se nepodařilo <PERSON>.", "alrFailedEventStart": "Událost se nepodařilo s<PERSON>.", "alrFailedEventVariables": "Proměnné vybrané události se nepodařilo <PERSON>.", "alrFailedEvents": "Události se nepodařilo na<PERSON>.", "alrFailedFoldersData": "Data složek se nepodařilo načíst.", "alrFailedFormData": "Data formuláře se nepodařilo na<PERSON>.", "alrFailedInitiatorName": "Jméno iniciátora se nepodařilo načíst.", "alrFailedLabelData": "Data komponenty Label se nepodařilo načíst.", "alrFailedLoad": "Data tisku se nepoda<PERSON>ilo <PERSON>", "alrFailedLogicalType": "Logický typ se nepodařilo nač<PERSON>t.", "alrFailedMultiBoxData": "Data multiBoxu se nepodařilo nač<PERSON>t.", "alrFailedNewCase": "<PERSON>ři zakládání nového případu se vyskytla chyba!", "alrFailedNewFolder": "Název složky se nepodařilo změnit.", "alrFailedNoticeData": "Data upozornění se nepodařilo na<PERSON>.", "alrFailedOrgUnitUser": "Org. jednotky uživatele se nepodařilo načíst.", "alrFailedOverviewData": "<PERSON><PERSON><PERSON><PERSON> se nepodařilo <PERSON>.", "alrFailedPlanData": "Data plánu se nepodařilo na<PERSON>.", "alrFailedPostData": "Data se nepoda<PERSON><PERSON> o<PERSON>.", "alrFailedPrintData": "Nelze načíst data k tisku.", "alrFailedRevisionInfo": "Informace o nové revizi se nepodařilo načíst.", "alrFailedSearchBoxData": "Data searchBoxu se nepodařilo na<PERSON>.", "alrFailedSelectBoxData": "Data komponenty SelectBox se nepodařilo načíst.", "alrFailedSuggestBoxData": "Data našeptávače se nepodařilo načíst.", "alrFailedTasColors": "<PERSON><PERSON> se nepodař<PERSON>!", "alrFailedTaskHandOver": "Úkol se nepodařilo předat.", "alrFailedTemplateProcesses": "Šablony případů se nepodařilo načíst.", "alrFailedVarData": "Data proměnné se nepodařilo <PERSON>.", "alrFileAdded": "So<PERSON>or byl <PERSON>!", "alrFileDeleteFailed": "<PERSON><PERSON>or se nepo<PERSON><PERSON><PERSON>t", "alrFileDonwload": "<PERSON><PERSON><PERSON> so<PERSON>...", "alrFileDownloaded": "So<PERSON>or byl sta<PERSON>!", "alrFileInfoFailed": "Informace o souboru se nepodařilo načíst.", "alrFileMetaSaveFailed": "Metadata souboru se nepoda<PERSON>ilo <PERSON>.", "alrFileSavedLikeAttach": "<PERSON><PERSON><PERSON> byl uložen jako dokument.", "alrFileUploadFailed": "<PERSON><PERSON>or se nepo<PERSON><PERSON>", "alrFillAllRequired": "Pro splnění úkolu musí být vyplněna všechna povinná data!", "alrFillData": "Pro uložení úkolu musí být správně vyplněna všechna data!", "alrFillDataInRightFormat": "Vyplňte prosím data ve správném formátu.", "alrFillDataToCompleteTsk": "Pro splnění úkolu musí být správně vyplněna všechna data!", "alrFillNameAndPass": "Vyplňte prosím jméno a heslo.", "alrFillNote": "Vyplňte prosím text poznámky.", "alrFillRequiredItems": "Vyplňte prosím vyžadované položky.", "alrFolderDataFailed": "Data složek se nepodařilo načíst.", "alrFolderDataLoadFailed": "Data složky se nepodařilo načíst.", "alrFolderFailed": "Nepodařilo se načíst informace o složce.", "alrFolderSaveFailed": "Složku se nepodařilo uložit!", "alrFoldersLoadFailed": "Složky se nepodařilo načíst.", "alrHelpSettingsSaveFailed": "Nastavení nápovědy se nepodařilo ul<PERSON>ž<PERSON>.", "alrHistoricalTskInfoFailed": "Informace historického úkolu se nepodařilo načíst.", "alrHistoricalVarsSaveFailed": "Historické proměnné se nepodařilo <PERSON>.", "alrHistoricalVarsSaved": "<PERSON><PERSON><PERSON> promě<PERSON><PERSON> byly ul<PERSON>.", "alrInvLogginHash": "Neplatné <PERSON>í.", "alrJsonFailed": "Není validní JSON!", "alrLackOfPermsToEdit": "Nemáte právo na editaci! Vlastníkem je", "alrLackOfPermsToSleepCase": "Nemáte dostatečná oprávnění na uspání případu.", "alrLackOfPermsToWakeUpCase": "Nemáte dostatečná oprávnění na probuzení případu.", "alrLastHistoricalTskIdFailed": "ID posledního historického úkolu se nepodařilo načíst.", "alrLoadAttachmentsFailed": "Dokumenty se nepodařilo na<PERSON>.", "alrLogOutFailed": "Nepodařilo se odhlásit.", "alrLoginExpired": "Vypršel limit přihl<PERSON>šení, přihlašte se prosím znovu.", "alrMappingFailed": "Mapování se nepodařilo <PERSON>.", "alrMappingTsksFailed": "Mapování úkolů se nepodařilo načíst.", "alrNewCaseBased": "Nový případ založen!", "alrNewFolder": "Byla založena nová složka!", "alrNewFolderFailed": "Nepodařilo se založit novou složku.", "alrNextTskOpened": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>ledují<PERSON>í <PERSON>", "alrNoDataToPrint": "Nebyla nalezena žádná data k tisku.", "alrNoteAdded": "Poznámka byla přidána!", "alrNoteSaveFailed": "Poznámku se nepodařilo ul<PERSON>ž<PERSON>.", "alrNoteSaving": "Přidávám poznámku...", "alrNotesLoadFailed": "Poznámky k případu se nepodařilo načíst.", "alrOrgUnitDataFailed": "Nepodařilo se načíst data o org. jednotce.", "alrOrgUnitDeleteFailed": "Org. jednotku se nepodař<PERSON>.", "alrOrgUnitDeleted": "Org. jednotka byla s<PERSON>ána!", "alrOrgUnitDeleting": "Mažu org. jednotku...", "alrOrgUnitSaveFailed": "Org. jednotku se nepodařilo <PERSON>.", "alrOrgUnitSaved": "Org. jednotka byla uložena!", "alrOrgUnitSaving": "Ukládám org. jednotku...", "alrOverviewDataLoadFailed": "Data přehledu se nepodařilo nač<PERSON>.", "alrOverviewSaveFailed": "<PERSON><PERSON><PERSON><PERSON> se nepodařilo <PERSON>!", "alrOverviewSaveSameNameFailed": "Název přehledu je již v<PERSON><PERSON><PERSON><PERSON> Vámi nebo jiným uživatelem, prosím zvolte jiný název přehledu.", "alrGraphSaveSameNameFailed": "Název grafu je již v<PERSON><PERSON><PERSON><PERSON> Vámi nebo jiným uživatelem, prosím zvolte jiný název grafu.", "alrReportSaveSameNameFailed": "Název reportu je již v<PERSON><PERSON><PERSON><PERSON> Vámi nebo jiným uživatelem, prosím zvolte jiný název reportu.", "alrOverviewsLoadFailed": "Nepodařilo se načíst přehledy.", "alrPassSaveFailed": "Heslo se nepodařilo <PERSON>.", "alrPassSaved": "<PERSON><PERSON><PERSON> bylo ul<PERSON>!", "alrPlanReqItems": "Pro uložení plánu vyplňte vyžadované položky.", "alrPlanSaveFailed": "Plán se nepoda<PERSON>ilo <PERSON>.", "alrPlanSaved": "Plán byl uložen!", "alrPreparingPrint": "Připravuji sestavu pro tisk...", "alrPrintDeleteFailed": "Tisk se nepo<PERSON><PERSON><PERSON> s<PERSON>.", "alrPrintDeleted": "Tisk byl smazán!", "alrPrintSaveFailed": "Tisk se nepoda<PERSON>ilo <PERSON>", "alrPrintSaved": "Tisk byl uložen!", "alrReadOnlyCaseDataFailed": "Data k případu pro čtení se nepodařilo načíst.", "alrRecalcFailed": "Při přepočítávání se vyskytla chyba!", "alrRecalculating": "Přepočítávám...", "alrRestorTemplFailed": "Šablonu se nepodařilo obnovit.", "alrRoleDataLoadFailed": "Nepodařilo se načíst data o roli.", "alrRoleDeleteFailed": "Roli se nepoda<PERSON>ilo <PERSON>.", "alrRoleDeleted": "Role by<PERSON> s<PERSON>!", "alrRoleDeleting": "Mažu roli...", "alrRoleSaveFailed": "Roli se nepodařilo <PERSON>.", "alrRoleSaved": "Role by<PERSON> <PERSON>!", "alrRoleSaving": "Ukládám roli...", "alrRunEvent": "Spouštím událost...", "alrSaveFailed": "Nepodařilo se uložit.", "alrSaved": "Uloženo!", "alrSaving": "Ukládám...", "alrSavingBeforeRecalcFailed": "Při ukládání před přepočítáním se vyskytla chyba!", "alrSavingFailed": "Při ukládání se vyskytla chyba!", "alrSavingPlan": "Ukládám plán...", "alrSavingPrint": "Ukládám tisk...", "alrSearchNoResults": "Parametr<PERSON><PERSON> h<PERSON>í<PERSON><PERSON><PERSON><PERSON>.", "alrSearchRequestFailed": "Při odesílání požadavku se vyskytla chyba!", "alrSearching": "Vyhledávání...", "alrSettFailed": "Nastavení se nepodařilo <PERSON>.", "alrSettSaved": "Nastavení bylo <PERSON>.", "alrSettingsLoadFailed": "Data nastavení se nepodařilo nač<PERSON>t.", "alrSleepCaseFailed": "Nepodařilo se uspat případ.", "alrStoreNameNotDefined": "Store „{{storeName}}“ is not defined.", "alrStoreNotDefined": "Store has not been defined.", "alrSubActionNotDefined": "SubAction and suffix has to be defined.", "alrSubStoreNotDefined": "SubStore and suffix has to be defined.", "alrSuggestBoxDataNotContains": "Data naš<PERSON><PERSON> „{{label}}“ <PERSON><PERSON><PERSON><PERSON><PERSON> „{{prop}}“!", "alrSuspendingCase": "Uspávám případ...", "alrTableDataFailed": "Data pro tabulku se nepodařilo načíst.", "alrTasNewVersion": "Byla nalezena nová verze aplikace.", "alrRefresh": "Pro správné fungování je nutné {{- spanRefresh}} stránku v prohlížeči.", "alrTasVersionLoadFailed": "Verzi aplikace se nepodařilo načíst!", "alrTaskHandOver": "Předávám ú<PERSON>l...", "alrTaskHandedOver": "Úkol byl p<PERSON><PERSON>:", "alrTaskNoRights": "Nemáte oprávnění zobrazit úkol č. {{id}}.", "alrTaskNotFound": "Úkol nebyl nalezen.", "alrTempDataLoadFailed": "Data pro šablonu se nepodařilo načíst.", "alrTempHeadersLoadFailed": "Hlavičky šablony se nepodařilo načíst.", "alrTempDeleteFailed": "Šablonu se nepodařilo <PERSON>.", "alrTempDeleted": "Šablona byla s<PERSON>ána!", "alrTempFoldersLoadFailed": "Složky šablony se nepodařilo načíst.", "alrTempNameLoadFailed": "Název šablony se nepodařilo načíst.", "alrTempRestored": "Šablona byla obnovena jako Vyví<PERSON>.", "alrTempSaveFailed": "Šablonu se nepodařilo <PERSON>.", "alrTempsLoadFailed": "Šablony se nepodařilo na<PERSON>.", "alrTempVarDataLoadFailed": "Data k šabloně proměnné se nepodařilo na<PERSON>.", "alrTempVarSaveFailed": "Proměnnou se nepodař<PERSON>.", "alrTempVarsLoadFailed": "Proměnné <PERSON> se nepodařilo nač<PERSON>.", "alrTotalCountFailed": "Nepodařilo se zjistit celkový počet záznamů v tabulce.", "alrTreeDataFailed": "Data ke stromečku se nepodařilo načíst.", "alrTskAddFailed": "Úkol se nepodařilo přidat.", "alrTskAdded": "Úkol byl přidán!", "alrTskAdding": "Přidávám úkol...", "alrTskAssignFailed": "Úkol se nepodařilo přiřadit.", "alrTskAssigned": "Úkol byl přiřazen.", "alrTskCompleteFailed": "Při plnění úkolu se vyskytla chyba.", "alrTskDataLoadFailed": "Data úkolu se nepodařilo nač<PERSON>t.", "alrTskDeleteFailed": "Úkol se nepodařilo s<PERSON>.", "alrTskDeleted": "Úkol byl smazán!", "alrTskNameLoadFailed": "Název úkolu se nepodařilo načíst.", "alrTskRecalculated": "Úkol přepočítán!", "alrTskSaveFailed": "Při ukládání úkolu se vyskytla chyba.", "alrTskSaved": "Úkol uložen!", "alrTskSavedAndCompleted": "Úkol uložen a splněn!", "alrTskScheduleFailed": "Úkol se nepodařilo nač<PERSON>ovat.", "alrTskScheduled": "Úkol byl načasován.", "alrTskTakeFailed": "Úkol se nepodařilo převzít.", "alrTskTaken": "Úkol byl přev<PERSON>t.", "alrTskTakign": "Přebírám <PERSON>...", "alrTsksMappingFailed": "Úkoly mapování se nepodařilo načíst.", "alrUserDataLoadFailed": "Uživatelská data se nepodařilo načíst.", "alrUserDeleteFailed": "Uživatele se nepodařilo <PERSON>.", "alrUserDeleted": "Uživatel byl s<PERSON>zán!", "alrUserDeleting": "<PERSON><PERSON><PERSON>...", "alrUserIsNotActive": "Uživatel není ativní", "alrUserNotLoaded": "Uživatele se nepodařilo na<PERSON>t.", "alrUserParamsLoadFailed": "Uživatelské parametry se nepodařilo načíst.", "alrUserSaveFailed": "Uživatele se nepodařilo <PERSON>.", "alrUserSaved": "Uživatel byl uložen!", "alrUserSaving": "Ukládá<PERSON> u<PERSON>...", "alrUserStatusChangeFailed": "Stav uživatele se nepodařilo změnit.", "alrUserStatusChanged": "Stav uživatele byl změněn!", "alrUserStatusChanging": "Měním stav už<PERSON>le...", "alrVarDeleteFailed": "Proměnnou se nepoda<PERSON>.", "alrVarDeleted": "Proměnná byla s<PERSON>ána!", "alrVarSaveFailed": "Proměnnou se nepodař<PERSON>.", "alrVarSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON> byla <PERSON>.", "alrVarSaving": "Ukládám proměnnou...", "alrVarsForModalFilterFailed": "Proměnné pro modální filtr se nepodařilo na<PERSON>.", "alrVarsLoadFailed": "Proměnné se nepodař<PERSON>.", "alrVarsOrderLoadFailed": "Pořadí proměnných se nepodařilo načíst.", "alrVarsOrderSaveFailed": "Pořadí proměnných se nepodařilo ul<PERSON>ž<PERSON>.", "alrViceDeleted": "Zástup by<PERSON>.", "alrViceFailed": "Zástup se nezdařil.", "alrViceNotFound": "Zástup nebyl nalezen!", "alrViceSaveFailed": "Zástup se nepodařilo ul<PERSON>ž<PERSON>.", "alrViceSaved": "Zástup byl uložen!", "alrViceSaving": "Ukládám zástup...", "always": "Vž<PERSON>", "annually": "Ročně", "assHierarchy": "Vztah k referenční osobě", "assHierarchyAncestors": "Všichni nadřízení referenční osoby", "assHierarchyChildren": "Přímí podřízení referenč<PERSON><PERSON> o<PERSON>by", "assHierarchyDescendants": "Všichni podřízení referenční osoby", "assHierarchyGuarantor": "<PERSON><PERSON> referenč<PERSON><PERSON> o<PERSON>ba", "assHierarchyParent": "Přímý nadřízený referenční osoby", "assHierarchySiblings": "<PERSON><PERSON><PERSON><PERSON> refer<PERSON>č<PERSON>í osoby", "assMethodAutomatic": "Vybere p<PERSON>č<PERSON>č", "assMethodLastSolver": "Poslednímu řešiteli úkolu", "assMethodLastSolverChoice": "Vybere poslední řešitel úkolu", "assMethodLeast": "<PERSON><PERSON> nejmé<PERSON>", "assMethodPull": "<PERSON><PERSON><PERSON>; bude nabí<PERSON>ut", "assMethodSelect": "Vybere supervizor úkolu", "assMethodVariable": "Uveden v proměnné", "assessmentOfConds": "Vyhodnocení podmínek", "assign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assignAttrs": "Přiřazení atributů", "assignAttrsLogType": "Přiřazení atributů logickému typu dokumentu", "assigned": "<PERSON><PERSON><PERSON><PERSON>", "assigningRoles": "Přiřazení rolí", "assignments": "Přiřazení", "attachmentAdd": "Přidat dokument", "attachments": "Dokumenty", "attachmentsList": "Seznam dokumentů", "attribute": "Atribut", "attributeNew": "Atribut – Nový", "availableVars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON>", "borders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "byFolders": "<PERSON><PERSON>", "byOrganization": "Dle organizace", "byRole": "<PERSON><PERSON>", "calculation": "Výpočet", "calculations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calendar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "carriedIfNoOther": "Provede se v případě, že žádná jiná", "case": "Případ", "caseCreation": "Vytvoření případu", "caseGraph": "Instančn<PERSON> graf", "caseNoEvents": "Případ <PERSON>bsahu<PERSON> u<PERSON>.", "caseNum": "Případ č.", "caseOwner": "Vlastník případu", "caseStatus": "<PERSON>av p<PERSON>", "caseVar": "případ", "cases": "Případy", "casesWithProblem": "<PERSON><PERSON> s problémem", "category": "<PERSON><PERSON><PERSON>", "changeTaskSolver": "Změna řešitele úkolu", "changedBy": "Změnil", "changedWhen": "Změnil (kdy)", "checkbox": "Checkbox", "checkboxList": "CheckboxList", "choosePrint": "Šablona tisku", "chooseUserToAssignTsk": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> př<PERSON><PERSON><PERSON><PERSON><PERSON>", "choosenAttrs": "Vybrané atributy", "city": "<PERSON><PERSON><PERSON>", "class": "Třída", "clickToClose": "Kliknutím zavřete", "clickToRefresh": "Kliknutím aktualizujete stránku v prohlížeči", "clickToRepeat": "Kliknutím akci zopakujete", "clientLanguage": "Jazyk klienta", "cloneRow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "closeAll": "Zavřít vše", "coWorkersOfPlanGuar": "<PERSON><PERSON><PERSON><PERSON> garan<PERSON> p<PERSON>", "color": "<PERSON><PERSON>", "colors": "Barvy", "column": "Sloupec", "columnName": "<PERSON><PERSON><PERSON><PERSON> slou<PERSON>", "comment": "<PERSON><PERSON><PERSON><PERSON>", "complete": "Splnit", "completion": "Dokončení", "componentDescription": "<PERSON>isek komponenty", "condition": "Podmínka", "conditions": "Podm<PERSON><PERSON>", "confirmAttachDeletion": "Opravdu chcete smazat dokument?", "confirmDeleteDialog": "Opravdu chcete smazat {{variable}}?", "confirmDialogEventSave": "Pro přepnutí je potřeba uložit událost. Chcete jí uložit?", "confirmResetDashboard": "Opravdu chcete resetovat dashboard?", "confirmSaveChanges": "Uložit provedené změny?", "confirmSaveDiagramChanges": "Uložit provedené změny v celém diagramu?", "confirmSaveTaskChanges": "Uložit provedené změny v úkolu?", "confirmRestoreDialog": "Opravdu chcete obnovit {{variable}}?", "confirmSaveNote": "Chcete uložit poznámku?", "confirmSleepCase": "Opravdu chcete uspat případ?", "confirmTakeoverTsk": "Opravdu chcete převzít úkol?", "confirmWakeUpCase": "Opravdu chcete probudit případ?", "connection": "Propojení", "connectionFailed": "Nezdařilo se připojení k serveru.", "connectionVar": "propojení", "constant": "Konstanta", "contact": "Kontakt", "contactTaskOwner": "Kontaktovat řešitele", "containerSettings": "Nastavení kontejneru", "contains": "obs<PERSON><PERSON>", "contents": "<PERSON><PERSON><PERSON>", "continueSolving": "Pokračovat v řešení", "copied": "Zkopírováno!", "copy": "Kopírovat", "copyShortcut": "Stiskněte Ctrl+C", "copyToClipboard": "Zkopírovat do schránky", "createForm": "Vytvořit formulář", "createNewCase": "Vytvoření nového případu", "csv": "csv", "csvFile": "CSV soubor", "customPrint": "Vlastní tisk", "daily": "Denně", "dashCvNoOverview": "Není vybrán žádný přehled – zvolíte ho v nastavení kontejneru", "dashCvNoRights": "Nemáte právo zobrazit přehled, kontaktujte administrátora", "dashFavNoShortcut": "Nejsou vybraní žádní zástupci – zvolíte je v nastavení kontejneru", "dashboard": "Dashboard", "date": "Datum", "dateList": "Číselník datumů", "day": "den", "dayE": "Dne", "daysDHM": "Dnů: (dd:hh:mm)", "defChangeVarInfluence": "Změna definice této proměnné se promítne i do již vytvořených případů.", "defChangeInfluence": "Změna definice se promítne i do již vytvořených případů.", "defaultCaseName": "Výchozí název případu", "defaultLbl": "Výchozí {{label}}", "defaultLblShe": "Výchozí {{label}}", "defaultLblIt": "Výchozí {{label}}", "defaultPrintProcess": "Výchozí – případ", "defaultPrintTask": "Výchozí – úkol", "defaultValue": "Výchozí hodnota", "delUser": "Odstranit uživatele", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteCol": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "deleteRow": "<PERSON><PERSON><PERSON><PERSON>", "deleteSelected": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "<PERSON><PERSON><PERSON><PERSON>", "deletedOn": "S<PERSON>z<PERSON><PERSON>", "deletedShe": "Smazaná", "description": "<PERSON><PERSON>", "deselect": "Zrušit výběr", "detail": "Detail", "developed": "Vyvíjená", "dial": "Číselník", "dic": "DIČ", "directSubOfPlanGuar": "Přímí podřízení garanta plánu", "directSupOfPlanGuar": "Přímý nadřízený garanta plánu", "disableFilter": "Vypnout filtr", "dmsAssignAttrs": "DMS přiřazení atributů", "dmsAttribute": "DMS atribut", "dmsAttributes": "DMS atributy", "dmsColumns": "DMS – sloupce", "dmsVisNull": "Pouze v tomto procesu", "dmsVisSub": "I v podřízených procesech", "dmsVisSup": "I v nadřazených procesech", "dmsVisSupSub": "I v nadřazených i podřízených procesech", "dmsVisibility": "<PERSON><PERSON><PERSON><PERSON> budo<PERSON> v<PERSON>", "doNotShowVariablesWith_": "Proměnné s názvem začínajícím znakem `_` nebudou zobrazovány uživatelům", "document": "Dokument", "documentVar": "dokument", "documents": "Dokumenty", "doesNotContain": "<PERSON><PERSON><PERSON><PERSON>", "done": "Hotovo", "download": "<PERSON><PERSON><PERSON><PERSON>", "dragAddFiles": "Přetažením v<PERSON> so<PERSON>, nebo k<PERSON> {{- here }} pro vyb<PERSON><PERSON><PERSON> so<PERSON>.", "dragAddFile": "Přetažením v<PERSON> soubor, nebo k<PERSON> {{- here }} pro vybrání souboru.", "here": "zde", "dropContainer": "Odstranit kontejner", "dropzoneTip": "<PERSON><PERSON> so<PERSON>ory", "dropZoneUserPhoto": "Sem přetáhněte fotku nebo klikněte pro výběr souboru.", "dueDate": "Termín", "duty": "Povinnost", "dynList": "Dyn. seznam", "dynRowsDefinition": "Definice tabulky a sloupců", "dynTableName": "<PERSON><PERSON><PERSON><PERSON>", "dynTable": "Dynamickou tabulku", "dynTables": "Dynamick<PERSON>", "dynamicList": "Dynamický seznam", "dynamicRows": "Dynamick<PERSON>", "dynamicTable": "Dynamická tabulka", "edit": "<PERSON><PERSON><PERSON><PERSON>", "editAttribute": "Upravit atribut", "editOrgUnit": "Upravit org. jednotku", "editRole": "Upravit roli", "editRule": "<PERSON><PERSON><PERSON><PERSON> pra<PERSON>", "editUser": "Upravi<PERSON>", "editor": "Editor", "email": "E-mail", "emailsQueue": "Fronta emailů", "empty": "Prázdné", "end": "Konec", "error": "Chyba", "errored": "Chybové", "error404": "Chyba 404 – s<PERSON><PERSON><PERSON> nenale<PERSON>!", "event": "Ud<PERSON>lost", "events": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventsRun": "Událost spustit", "every": {"masc": "<PERSON><PERSON><PERSON><PERSON>", "neutral": "<PERSON><PERSON><PERSON><PERSON>", "repeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "everyWorkDay": "Každý pracovní den", "excel": "Excel", "favourites": "Oblíben<PERSON>", "fax": "Fax", "file": "<PERSON><PERSON><PERSON>", "fileLogicalType": "Logický typ souboru", "fileName": "<PERSON><PERSON><PERSON><PERSON>", "filePlacement": "Zařazení do složky", "files": "<PERSON><PERSON><PERSON>", "filter": "filtr", "filterFrom": "Filtr z", "filterTitle": "Filtr", "filtrate": "Filtrovat", "finishTask": "Splnit úkol", "finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finishedBy": "Splnil", "finishedOn": "Dokončeno", "first": "První", "firstLeft": "prvn<PERSON> levá", "firstName": "Jméno", "firstRight ": "první pravá", "firstRowColumnsName": "Prvn<PERSON> j<PERSON>u n<PERSON> slou<PERSON>", "folder": "Složka", "folder-": "Složka –", "folderExecRightsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> role, k<PERSON><PERSON> budou moci spoušt<PERSON>t případy ve složce", "folderExecRightsTextOS": "P<PERSON><PERSON><PERSON><PERSON>ďte organizační <PERSON>, k<PERSON><PERSON> budou moci spouštět případy ve složce", "folderName": "Název složky", "font": "Písmo", "fontMainHeader": "Písmo hlavní <PERSON>", "form": "Formulář", "fourth": "Čtvrtá", "freeTsk": "Volný <PERSON>", "fri": "P<PERSON><PERSON>k", "from": "Od", "fsDescription": "popis", "fsName": "<PERSON><PERSON><PERSON><PERSON>", "fsTooltip": "nápověda", "fullName": "<PERSON><PERSON>", "fullScreen": "<PERSON><PERSON><PERSON> o<PERSON>zo<PERSON>", "getTotalCount": "Celkový počet", "graph": "<PERSON>", "handExecutionTaskListEmpty": "Vyberte <PERSON>", "handOver": "Předat", "handOverToUser": "Předat uživateli", "handover": "Předat", "headerDashboard": "Hlavička dashboardu", "help": "Nápověda", "hideLogout": "Schovat 'Odhlásit'", "hideNewProcess": "Schovat 'Nový případ'", "hideProcs": "Schovat 'Případy'", "hideTasks": "Schovat 'Úkoly'", "historicalValues": "<PERSON><PERSON><PERSON> ho<PERSON>", "currentValues": "Aktuální hodnoty", "history": "Historie", "home": "<PERSON><PERSON>", "html": "HTML", "ic": "IČ", "id": "ID", "inCasesNames": "V názvech případů", "inTasksNames": "V názvech úkolů", "inDevelopment": "Ve vývoji", "inEvery": "v každém", "inFiles": "v souborech", "initiator": "Inici<PERSON>tor", "inTasks": "v úkolech", "inactive": "Neaktivní", "inactiveShe": "Neaktivní", "incidences": "výskytech", "inclusion": "Zařazení", "info": "Info", "inputParams": "Chování na vstupu", "insert": "Vložit", "insertAttachTip": "Přetažením vložte dokument", "insertVar": "Vložit proměnnou", "insertSnippet": "<PERSON><PERSON><PERSON><PERSON> snippet", "snippet": "Fragment kódu", "insertedBy": "Vložil", "insertedOn": "Vloženo", "insteadOf": "za", "instructions": "Instrukce", "invitation": "Pozvánka", "isEmail": "Není validní e-mail", "isEmpty": "je <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isExisty": "Neplatné", "isManagerOrgUnit": "Je manažerem org. jednotek", "isNotEmpty": "<PERSON><PERSON><PERSON>", "isRequired": "<PERSON><PERSON><PERSON><PERSON>", "justSave": "<PERSON>", "keepGlobalOrder": "Ponechat globální řazení", "key": "<PERSON><PERSON><PERSON><PERSON>", "killed": "<PERSON><PERSON><PERSON><PERSON>", "last": "poslední", "lastName": "Příjmení", "lastOwnerOfTask": "Poslednímu řešiteli úkolu", "licenceKey": "Licenční klíč", "link": "Spojení", "linkConditions": "Podmínky prop<PERSON>", "list": "Seznam", "listName": "Název seznamu", "listOfValues": "Seznam hodnot", "listValue": "Hodnota seznamu", "loading": "Načítá se...", "location": "<PERSON><PERSON><PERSON>", "locked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logIn": "Přihlásit", "logOut": "Odhlásit se", "logicalType": "Logický typ", "loginError": "Přihlášení se nezdařilo.", "loginTimeout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lace (s)", "longText": "Dlouhý text", "mailEscalation": "E-mail s přehledem eskalovaných úkolů", "mailProcEscalation": "E-mail s přehledem eskalovaných případů", "mailPromptly": "E-mail<PERSON><PERSON>ornění na nový úkol", "mailPull": "E-mailové <PERSON>ozornění na úkol k odběru", "mailTotal": "E-mail s p<PERSON><PERSON><PERSON><PERSON>lů", "mainButton": "Hlavní <PERSON>", "mainColor": "Hlavn<PERSON>", "mainHeader": "Hlavní h<PERSON>č<PERSON>", "mainLanguage": "Hlavní jazyk", "manager": "<PERSON><PERSON><PERSON><PERSON>", "managerOfOrgUnit": "Manažer organizační <PERSON>", "mandatory": "<PERSON><PERSON><PERSON><PERSON>", "manualStartEvent": "Ruční spuštění ud<PERSON>ti", "mapping": "Mapování", "mappingSubProcessVars": "Mapování proměnných podprocesu", "markAll": "Označit vše", "menu": "<PERSON><PERSON>", "mine": "<PERSON><PERSON>", "mobilePhone": "Mobil", "mon": "Pondělí", "month1": "<PERSON><PERSON>", "month10": "Říjen", "month11": "Listopad", "month12": "Prosinec", "month2": "Únor", "month3": "Březen", "month4": "<PERSON><PERSON>", "month5": "<PERSON><PERSON><PERSON><PERSON>", "month6": "Červen", "month7": "Červenec", "month8": "<PERSON><PERSON>", "month9": "Září", "monthI": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthly": "Měsíčně", "months": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more": "V<PERSON>ce", "moreLanguage": "<PERSON>š<PERSON> j<PERSON>", "multiBox": "MultiBox", "multiBoxSingle": "MultiBoxSingle", "multiBoxTriple": "MultiBoxTriple", "multiInstance": "Multiinstance", "myUnfinishedTasks": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "nested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "never": "<PERSON><PERSON>", "new": "Nový", "newCase": "Nový případ", "newFolder": "Složka – Nová", "newForm": "Nov<PERSON> formulář", "newIt": "Nové", "newName": "Nový název", "newShe": "Nová", "newSolver": "Nový řešitel", "no": "Ne", "noAttach": "<PERSON><PERSON><PERSON><PERSON> (Klikněte pro přidání)", "clickToAddAttach": "Klikněte pro přidání", "noName": "Bez názvu", "noOneBeOffered": "<PERSON><PERSON><PERSON>, bude nabídnut k řešení vymezené sku<PERSON> už<PERSON>ů", "noPageRights": "Nemáte oprávnění zobrazit tuto stránku.", "node": "<PERSON><PERSON>", "notFound": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notMatch": "<PERSON><PERSON><PERSON><PERSON><PERSON> se", "notNumber": "<PERSON><PERSON><PERSON>", "notIntNumber": "<PERSON><PERSON><PERSON> cel<PERSON>", "notSent": "Neodesláno", "notValid": "Neplatné", "notesAdd": "Přidat poznámku", "notesNew": "Nová poznámka", "notes": "Poznámky", "notesOnContacts": "Poznámky ke kontaktům", "notice": "Upozornění", "notification": "<PERSON><PERSON><PERSON><PERSON>", "nrOfItems": "Počet záznamů", "number": "<PERSON><PERSON><PERSON>", "numberList": "Číselník čísel", "ok": "OK", "oneMustBeMet": "Alespoň jedna musí být splněná", "onlyOrgUnit": "Jen organizač<PERSON>í <PERSON>", "onlyPlanGuarantor": "<PERSON><PERSON> garant p<PERSON>u", "openAll": "Otevřít vše", "operating": "Provozní", "order": "Řazení", "orderByColumn": "Řadit dle sloupce", "orgName": "Název subjektu", "orgStructure": "Org. struk<PERSON>", "orgUnit": "org. jednotku", "orgUnitE": "org. jednotce", "orgUnitName": "Název org. jednotky", "orgUnitShe": "<PERSON><PERSON><PERSON> j<PERSON>", "orgUnits": "<PERSON><PERSON><PERSON> j<PERSON>", "organization": "Organizace", "overview": "<PERSON><PERSON><PERSON><PERSON>", "overviewMapping": "<PERSON><PERSON><PERSON><PERSON> mapování", "overviewNew": "P<PERSON><PERSON>led – nový", "overviewSetSharing": "Nastavte sdílení přehledu pro jednotlivé skupiny uživatelů", "overviews": "P<PERSON><PERSON><PERSON>y", "owner": "Vlastník", "ownerWithLeastTasks": "Řešiteli, který má nejméně <PERSON>ů", "pageNotFound": "<PERSON><PERSON><PERSON><PERSON>", "parentFolder": "Nadřazená složka", "parentUnit": "Nadřazená j<PERSON>not<PERSON>", "participants": "Účastníci", "password": "He<PERSON><PERSON>", "passwordChallenge": "Výzva", "passwordChallengeText": "Opravdu vyzvat všechny uživatele ke změně hesla?", "passwordChange": "<PERSON><PERSON><PERSON><PERSON>", "passwordCheck": "Heslo (kontrola)", "passwordNew": "<PERSON><PERSON>", "passwordNewCheck": "<PERSON><PERSON> (kontrola)", "paused": "Pozastavená", "personInOrgStr": "Zadavatel v org. struktuře", "phone": "Telefon", "photo": "Foto", "plan": "plán", "planGuarantor": "<PERSON><PERSON><PERSON> p<PERSON>", "planTitle": "Plán", "plans": "Plánování", "plnOffType": "Opakovat", "plnOrgUnit": "Organizační <PERSON>", "plnTProc": "Šablona případu", "plnUser": "<PERSON><PERSON><PERSON> p<PERSON>", "plnUsersSelect": "Omezující podmínky pro výběr jednoho nebo více iniciátorů", "prependTsk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prependedTsk": "Předřazený úkol", "primaryKey": "Primárn<PERSON>", "print": "Tisk", "printTemplate": "Šablona tisku", "printType": "<PERSON><PERSON> tisku", "printer": "Tisk – HTML", "priority": "Priorita", "procDescription": "<PERSON><PERSON> procesu", "procDueDateFinish": "<PERSON><PERSON><PERSON> dokončení případu", "procName": "Název případu", "procOwner": "Vlastník procesu", "procSummary": "V řešení", "process": "Proces", "processName": "Jméno procesu", "property": "Vlastnost", "quickFilter": "R<PERSON><PERSON><PERSON> filtr", "radioButtonList": "RadioButtonList", "reEvaluates": "Znovu vyhodnotit", "recalc": "Přepočítat", "recipient": "<PERSON><PERSON><PERSON><PERSON>", "recipientsId": "ID Adresáta", "records": "<PERSON><PERSON><PERSON><PERSON>", "referenceUser": "Referenční o<PERSON>ba", "refresh": "Obnovit", "registered": "Evidovaní", "relatToPlanSponsor": "Vztah ke garantovi plánu", "remove": "<PERSON><PERSON><PERSON><PERSON>", "removeCurrentScreenFromFavourite": "Odebrat aktuální obrazovku z oblíbených", "removeVice": "Zrušit zástup", "renameCols": "Přejmenovat slou<PERSON>ce", "repeatLogin": "Opakujte nebo zvolte jiný typ přihlášení.", "repeatOrReport": "Opakujte prosím později nebo kontaktujte administrátora.", "repetition": "Opakování", "required": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Reset", "restrictTaskOwners": "Omezení pro řešitele", "restrictUsers": "<PERSON><PERSON><PERSON><PERSON>", "returnSubProcessVars": "Návrat proměnných podprocesu", "revision": "Revize", "right": "Právo", "rightOrDuty": "Právo/povinnost", "role": "roli", "roleName": "<PERSON><PERSON><PERSON><PERSON> role", "roleSg": "Role", "roles": "Role", "row": "<PERSON><PERSON><PERSON>", "rule": "<PERSON><PERSON><PERSON><PERSON>", "ruleCSVFile": "Název CSV souboru", "ruleCSVHeader": "První <PERSON> souboru CSV je hlavička", "ruleCSVMask": "Maska názvu CSV souboru", "ruleCSVSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> sloupc<PERSON>", "ruleNew": "Pravidlo – Nové", "ruleParamsMap": "Mapování <PERSON>", "ruleProcOwnCSV": "Definován v mapování", "ruleTypeCSVExpProcs": "CSV Export všech případů šablony", "ruleTypeCSVMrgProcs": "Podle CSV spustit případy a aktualizovat proměnné případů", "ruleTypeCSVRunProcs": "Podle CSV spustit případy", "ruleTypeCSVUpdProc": "Podle CSV aktualizovat proměnné případu", "ruleTypeCSVUpdProcs": "Podle CSV aktualizovat proměnné případů", "ruleTypeCSVUpdateList": "Update dynamick<PERSON><PERSON> seznamu podle CSV", "ruleTypeReturn": "Odpověď na událost", "ruleTypeUpdateListOfProcesses": "Update <PERSON><PERSON><PERSON><PERSON>", "rules": "Pravid<PERSON>", "run": "<PERSON><PERSON><PERSON><PERSON>", "runProcess": "<PERSON><PERSON><PERSON><PERSON> proces", "running": "Běžící", "sat": "<PERSON><PERSON><PERSON>", "save": "Uložit", "saveAsAttachment": "Uložit tisk jako dokument případu", "scheduling": "Časování", "scheme": "Vizuální identita", "script": "S<PERSON><PERSON><PERSON>", "scripts": "Skripty", "search": "Hledat", "searchResult": "<PERSON><PERSON><PERSON>dek hledání", "second": "<PERSON><PERSON><PERSON>", "secondLeft": "d<PERSON><PERSON><PERSON> levá", "secondRight": "dru<PERSON><PERSON> pravá", "selectBox": "SelectBox", "selectDrop": "SelectDrop", "selectedByComputer": "Řešiteli, kterého automaticky vybere počítač", "selectedByTaskSupervisor": "Řešiteli, kterého vybere supervizor", "selectedPrint": "vybraný tisk", "selectedUser": "Vybraný uživatel", "send": "<PERSON><PERSON><PERSON>", "sendingFailed": "Chyba", "sendOn": "Čas k odeslání", "sendTestMail": "Poslat test", "sequence": "Sekvence", "setDashboard": "Upravit dashboard", "setDefault": "Nastavit jako výchozí", "setVice": "Nastavit z<PERSON>", "setViceAttachmentsNotes": "Práva vkládat dokumenty a poznámky", "settings": "Nastavení", "shortcuts": "Zástupci", "showAttachmentsClick": "Kliknutím zobrazíte dokumenty", "showCommentCol": "Zobrazit sloupec komentář", "skype": "Skype", "solve": "Řešit", "solvedBy": "<PERSON><PERSON>ř<PERSON><PERSON><PERSON>", "solver": "Řešitel", "sort": "Řadit", "sortByColumn": "Řadit podle sloupce", "sorting": "Řazení", "sourceTask": "Zdrojový úkol", "sourceVar": "Zdr<PERSON>jová proměnná", "start": "Začátek", "startDate": "<PERSON><PERSON>", "startCalDate": "První mo<PERSON><PERSON> datum", "endCalDate": "Poslední možné datum", "state": "Stav", "stateAddress": "<PERSON><PERSON><PERSON>", "status": "Status", "street": "Ulice a č.p.", "subProcess": "Podproces", "subject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "substitute": "Zástup", "sun": "<PERSON><PERSON><PERSON>", "superior": "Nadřazený", "supervis": "Supervizor", "supervisor": "Supervizor úkolu", "suspend": "Uspat", "suspended": "<PERSON><PERSON><PERSON>", "suspendedx": "Uspané", "tTaskAgain": "Chování při opakované aktivaci", "tTaskAutoCompleteCaption": "Úkol bude automaticky splněn pokud", "tTaskCompletionCOA": "nastanou všechny podmínky zároveň", "tTaskCompletionCOO": "nastane <PERSON> jedna podmínka", "tTaskDueOffsetNone": "ihned", "tTaskDueOffsetPO": "zadá supervizor", "tTaskDueOffsetPS": "do dnů od zahájení případu", "tTaskDueOffsetTS": "do dnů od možného zahájení aktivity", "tTaskDueOffsetVC": "z proměnné průběžně", "tTaskDueOffsetVO": "z proměnné při startu", "tTaskInvClassConf": "<PERSON><PERSON><PERSON>", "tTaskInvClassPriv": "<PERSON><PERSON><PERSON><PERSON>", "tTaskInvClassPubl": "Veřejné", "tTaskInvPriority1": "1-<PERSON><PERSON><PERSON>šší", "tTaskInvPriority2": "2", "tTaskInvPriority3": "3", "tTaskInvPriority4": "4", "tTaskInvPriority5": "5", "tTaskInvPriority6": "6", "tTaskInvPriority7": "7", "tTaskInvPriority8": "8", "tTaskInvPriority9": "9-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tTaskInvokeEventB": "na pozadí", "tTaskInvokeEventI": "okamžitě", "tTaskReferenceUserLastSolver": "Poslední řešitel úkolu", "tTaskReferenceUserMan": "Manažer org. jednotky xy", "tTaskReferenceUserUser": "Uživatel xy", "tTaskRunOnlyOnce": "<PERSON><PERSON><PERSON><PERSON> pouze jednou", "tTaskSufficientEnd": "Splnění ukončí celý případ", "tabName": "Název záložky", "table": "Tabulka", "takeOnlyOrder": "<PERSON><PERSON><PERSON><PERSON> jen po<PERSON>", "takeover": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetTask": "Cílový úkol", "targetVar": "Cílová proměnná", "taskAutomatic": "automatický stav", "taskEmailNotification": "E-mail<PERSON>", "taskEvent": "spouští událost", "taskEventWait": "čeká na událost", "taskOwner": "Řešitel úkolu", "taskSolverAssign": "k přiřazení řešiteli", "taskStart": "Zahájení", "taskStatus": "Status", "taskStatusA": "Aktivní", "taskStatusAP": "Aktivní podproces", "taskStatusAS": "Uspaný podproces", "taskStatusD": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "taskStatusL": "Čekající", "taskStatusLEdit": "<PERSON><PERSON>ze editovat čekající úkol", "taskStatusN": "Nový", "taskStatusP": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "taskStatusS": "Spící", "taskStatusT": "K odběru", "taskStatusW": "K přiřazení", "taskStatusWT": "K načasování", "taskSubprocess": "realizován podprocesem", "taskTabVariables": "Přiřazení proměnných", "taskType": "Typ <PERSON>", "taskWillBeAssigned": "Úkol bude zadán", "tasks": "Úkoly", "tasksToPull": "Úkoly k odběru", "taskstatusAD": "Aktivní i dokončený", "tempId": "ID šablony", "tempVar": "šablonu", "template": "Šablona", "templateDeleted": "<PERSON><PERSON><PERSON><PERSON>", "templateStatus": "<PERSON><PERSON>", "templates": "Šablony", "templatesFolder": "Šablony – složka", "testForm": "Testovací <PERSON>", "tested": "Testovaná", "text": "Text", "textList": "Číselník textů", "textMultipleLines": "Text s v<PERSON><PERSON>", "textSuggest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "third": "Třetí", "thirdCenter": "třetí center", "thu": "Čtvrtek", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "title": "Titulek", "to": "Do", "toHide": "Schovat", "toInclusive": "Do (včetně)", "toPull": "K odběru", "tooltip": "Nápověda", "total": "celkem", "tprocName": "Šablona procesu", "tsk": "Úkol", "tskAssignDues": "Nastavte časová omezení pro tento úkol", "tskName": "Název úkolu", "tskNum": "Úkol č.", "tskSolver": "Řešitel úkolu", "tskTemplate": "Šablona úkolu", "tskVar": "úkol", "tsksDone": "Do<PERSON><PERSON><PERSON><PERSON>", "tsksSolvers": "<PERSON><PERSON> ř<PERSON>ů", "ttAdd": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umožní přidat novou položku, př<PERSON><PERSON><PERSON><PERSON> nové parametry, k<PERSON><PERSON> ne<PERSON> na<PERSON>."}, "ttAddActivity": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttAddAttach": {"heading": "Přidat dokument", "body": "Umožní přidat nový dokument."}, "ttAddAttribute": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttAddContainer": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Přidá kontejner s vybraným obsahem."}, "ttAddFile": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttAddStructure": {"heading": "Přidat položku do organizační struktury", "body": "Umožní přidat novou položku organizační struk<PERSON>, případn<PERSON> nové parametry, k<PERSON><PERSON> ne<PERSON> dos<PERSON> na<PERSON>."}, "ttAddTemp": {"heading": "Přidání nové <PERSON>", "body": "Založení nové šablony případu. Vlastníkem šablony bude aktuálně přihlášený uživatel. Šabloně je automaticky přiřazen stav „ve vývoji“."}, "ttAddTsk": {"heading": "Přidání nového ú<PERSON>lu", "body": "Založení nového úkolu v rámci šablony procesu. Podle typu úkolu lze specifikovat jeho parametry. Vazbu na ostatní úkoly lze přidat nebo editovat na záložce Graf nebo Propojení."}, "ttAddTskGraph": {"heading": "Přidání nového ú<PERSON>lu", "body": "Založení nového úkolu v rámci šablony procesu. Podle typu úkolu lze specifikovat jeho parametry. Vazbu na ostatní úkoly lze přidat nebo editovat na záložce Graf nebo Propojení."}, "ttAddUser": {"heading": "Přidání nového už<PERSON>", "body": "Založení nového uživatele. Každý uživatel musí mít jedinečné uživatelské jméno. Uživateli lze nastavit základní informace, přiřazení do organizační struktury a přiřazení rolí. Uživateli je automaticky nastaven stav „uzamčen“."}, "ttAddVar": {"heading": "Přidání nové proměnné", "body": "Založení nové proměnné v rámci šablony případu. Každá proměnná je nositelkou informace, se kterou mohou pracovat řešitelé úkolů případu. <PERSON>ze specifikovat jméno, typ a defaultní hodnoty proměnné."}, "ttAddVice": {"heading": "Přidat zástup", "body": ""}, "ttAssignAttribute": {"heading": "Přiřazení atributů logickému typu dokumentu", "body": ""}, "ttAssignTsk": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Umožňuje přiřadit úkol konkrétnímu řešiteli nebo zařadit položku do definované struktury."}, "ttCases": {"heading": "Případy", "body": ""}, "ttOverviews": {"heading": "P<PERSON><PERSON><PERSON>y", "body": ""}, "ttChangePass": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Editace uživatelského přístupového hesla <PERSON>, kteří jsou spravování přímo v prostředí aplikace. Jsou-li uživatelé spravováni jinou službou, je nutné he<PERSON>lo prostřednictvím této externí služby – např. LDAP serveru."}, "ttClose": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Okno bude uzavřeno bez uložení provedených změn."}, "ttCloseTemp": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Okno s definicí <PERSON>y bude uzavřeno."}, "ttCompleteTsk": {"heading": "Splnit úkol", "body": "Potvrdí splnění úkolu a podle předdefinovaného procesu pošle k dalšímu zpracování."}, "ttContact": {"heading": "Kontakt", "body": "Zobrazí kontakty na supervizora úkolu."}, "ttContainerSettings": {"heading": "Nastavení", "body": "Umožní změnit nastavení pro daný kontejner."}, "ttCopyHdr": {"heading": "Kopírovat hlavičku", "body": "Vytvoření kopie označené hlavičky. Označení se provede kliknutím v tabulce hlaviček šablony."}, "ttCopyTemp": {"heading": "Kopírovat šablonu", "body": "Vytvoření kopie označené šablony. Označení se provede kliknutím v tabulce šablon procesů."}, "ttCopyVar": {"heading": "Ko<PERSON><PERSON>rov<PERSON><PERSON> proměnné", "body": "Zkopírování definice označené proměnné a její uložení pod novým názvem. Označení se provede kliknutím v tabulce proměnných."}, "ttDel": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Smaže označenou položku."}, "ttDelAttach": {"heading": "Smazat dokument", "body": "Smaže označený dokument."}, "ttDelConnection": {"heading": "Smazání <PERSON>", "body": "Odstranění označeného propojení mezi dvěma úkoly případu. Akci je potřeba ještě potvrdit. Akce se provádí pro označenou vazbu. Označení se provede kliknutím v tabulce propojení."}, "ttDelFolder": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Smaz<PERSON><PERSON>."}, "ttDelOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Smaže označený přehled."}, "ttDelTemp": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Přiřadí šabloně stav „smazaná“. Teprve při opakovaném požadavku na smazání dojde k fyzickému odstranění šablony. Akce se provádí pro označenou šablonu. Označení se provede kliknutím v tabulce šablon případů."}, "ttDelTsk": {"heading": "Smazání <PERSON>", "body": "Odstranění označeného úkolu. Akci je potřeba ještě potvrdit. Spolu s úkolem budou odstraněny související vazby na další úkoly v šabloně procesu. Označení se provede kliknutím v tabulce úkolů."}, "ttDelTskOrConnection": {"heading": "Smazání úkolu nebo vazby", "body": "Odstranění označeného úkolu nebo označené vazby mezi dvěma úkoly procesu. Akci je potřeba ještě potvrdit. Spolu s úkolem budou odstraněny související vazby na další úkoly procesu. Označení se provede kliknutím."}, "ttDelVar": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON> proměnn<PERSON>", "body": "Odstranění označené proměnné. Akci je potřeba ještě potvrdit. Proměnné už nebude dále k dispozici v jednotlivých úkolech procesu. Označení se provede kliknutím v tabulce proměnných."}, "ttDelVice": {"heading": "Zrušit zástup", "body": ""}, "ttDetailCase": {"heading": "Detail", "body": "Zobrazí detaily označeného případu."}, "ttDetailCertificate": {"heading": "Detail certifikátu", "body": "Zobrazí podrobnosti vybraného certifikátu."}, "ttDetailHistory": {"heading": "Detail", "body": "Zobrazí detaily označené <PERSON>."}, "ttDetailTsk": {"heading": "Detail <PERSON>lu", "body": "Zobrazí detaily označeného <PERSON>."}, "ttDmsFolderAdd": {"heading": "Přidání nové s<PERSON>", "body": "Vložení nové složky. Pokud je vybrána některá ze složek, předvyplní se složka jako nadřazená."}, "ttDmsFolderEdit": {"heading": "Upravit složku", "body": "<PERSON><PERSON>."}, "ttDocuments": {"heading": "<PERSON><PERSON>ž<PERSON>š<PERSON><PERSON> dokumentů", "body": "Vyskytují se zde všechny dokumenty případů + dal<PERSON><PERSON> ručně nahrané soubory. Umožňuje vyhledávat mezi soubory podle názvu nebo nastavených atributů."}, "ttDownload": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON><PERSON><PERSON><PERSON> oz<PERSON>ý soubor."}, "ttDropContainer": {"heading": "Odstranit", "body": "Odstraní kontejner z dashboardu."}, "ttENotification": "E-mail<PERSON>", "ttEdit": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umožní upravit označenou položku."}, "ttEditAttach": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umožní zobrazit a upravit atributy(metadata) nahraného souboru."}, "ttEditConnection": {"heading": "Úprava propojení", "body": "Editace vazby mezi dvěma úkoly. Lze editovat parametry chování vazby a vazební podmínku. Akce se provádí pro označenou vazbu. Označení se provede kliknutím v tabulce propojení."}, "ttEditOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umožní upravit označený přehled."}, "ttCopyOverview": {"heading": "Kopírovat přehled", "body": "Zkopíruje oz<PERSON>čený přehled."}, "ttEditPath": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umožní nadefinovat nový přehled."}, "ttEditTemp": {"heading": "Úprava definice <PERSON>", "body": "Editace šablony případu. Editovat lze jakýkoliv parametr šablony. Akce se provádí pro označenou šablonu. Označení se provede kliknutím v tabulce šablon případů."}, "ttEditTsk": {"heading": "Úprava úkolu", "body": "Editace informací o úkolu a jeho parametrů. Akce se provádí pro označený úkol. Označení se provede kliknutím v tabulce úkolů."}, "ttEditTskOrConnection": {"heading": "Editace <PERSON> nebo vazby", "body": "Editace informací o úkolu a jeho parametrů nebo editace vazby mezi dvěma <PERSON>ly, jejich parametrů chování a vazební podmínky. Akce se provádí pro označený úkol nebo vazbu. Označení se provede kliknutím."}, "ttEditTskVars": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "<PERSON>ravit pro<PERSON><PERSON><PERSON><PERSON>."}, "ttEditUser": {"heading": "Úprava informací o uživateli", "body": "Editace základních informací o už<PERSON>, <PERSON><PERSON>, přiřazení do organizační jednotky a přiřazení rolí. Akce se provádí pro označeného uživatele. Označení se provede kliknutím v tabulce uživatelů."}, "ttEditVar": {"heading": "Úprava proměnné", "body": "Editace <PERSON>, typu a defaultních hodnot proměnné. Akce se provádí pro označenou proměnnou. Označení se provede kliknutím v tabulce proměnných."}, "ttEnotTgt": "<PERSON><PERSON><PERSON><PERSON>", "ttEnotTgtG": "Supervizor úkolu", "ttEnotTgtO": "Vlastník případu", "ttEnotTgtP": "%s", "ttEnotTgtR": "Role %s", "ttEnotTgtS": "Organizační jednotka %s", "ttEnotTgtT": "Řešitel úkolu %s", "ttEvent": {"heading": "Ruční událost", "body": "Okamžité vyvolání události v tomto případu."}, "ttEvents": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Nastavování business pravidel pro reakci na definované interní nebo externí události v systému. Pro přístup je nutná role $PowerUser."}, "ttFavourites": {"heading": "Seznam oblíbených", "body": "Seznam všech oblíbených položek s možností jejich úpravy nebo smazání ze seznamu."}, "ttFilter": {"heading": "Filtrovat", "body": "Zobrazí pouze položky splňující nadefinované filtrovací podmínky."}, "ttFilterPrc": {"heading": "Filtrovat", "body": "Zobrazí pouze případy splňující nadefinované filtrovací <PERSON>ínky."}, "ttFilterTemp": {"heading": "Filtrovat šablony", "body": "Zobrazí pouze šablony splňující nadefinované filtrovací <PERSON>ínky."}, "ttFilterTsk": {"heading": "Filtrovat", "body": "Zobrazí pouze úkoly splňující nadefinované filtrovací <PERSON>ínky."}, "ttFilterUser": {"heading": "Filtrovat", "body": "Zobrazí pouze uživatele splňující nadefinované filtrovací <PERSON>ínky."}, "ttFullScreen": {"heading": "<PERSON><PERSON><PERSON> o<PERSON>zo<PERSON>", "body": "Zobrazí obsah kontejneru přes celou obrazovku."}, "ttGraph": {"heading": "<PERSON>", "body": "Grafické zobrazení stavu aktuálního případu."}, "ttGraphActualFinish": "Skutečný konec", "ttGraphActualStart": "<PERSON><PERSON>", "ttGraphCond": "Podm<PERSON><PERSON>", "ttGraphCond1": "alespoň jedna musí být splněna", "ttGraphCondAll": "všechny musí být splněné", "ttGraphCondElse": "V případě, že není splněna jiná podmínka", "ttGraphDeadlinePo": "Termín: zadá vlastník případu", "ttGraphDeadlinePs": "Termín: do %s dnů od zahájení případu", "ttGraphDeadlineTs": "Termín: do %s dnů od zahájení ú<PERSON>lu", "ttGraphDelayPo": "Zahájení <PERSON>lu: zadá vlastník případu", "ttGraphDelayPs": "Zahájení <PERSON>: %s dnů od zahájení případu", "ttGraphDelayTs": "Zahájení <PERSON>: %s dnů od zahájení ú<PERSON>lu", "ttGraphEnd": "Splnění úkolu ukončí celý případ", "ttGraphFinishedBy": "<PERSON><PERSON>ř<PERSON><PERSON><PERSON>", "ttGraphHiearchyA": "všichni nadřízení supervizora úkolu", "ttGraphHiearchyC": "přímí podřízení supervizora úkolu", "ttGraphHiearchyD": "všichni podřízení supervizora úkolu", "ttGraphHiearchyG": "supervizor úkolu", "ttGraphHiearchyL": "<PERSON>š<PERSON>ni", "ttGraphHiearchyP": "přímý nadřízený supervizora úkolu", "ttGraphHiearchyS": "kolegové supervizora úkolu", "ttGraphLinkFrom": "Z", "ttGraphLinkTo": "Do", "ttGraphMethodL": "poslednímu řešiteli úkolu %s", "ttGraphMethodS": "řešiteli vybranému supervizorem úkolu", "ttGraphMethodT": "automaticky vybranému <PERSON>", "ttGraphMethodV": "<PERSON><PERSON><PERSON><PERSON><PERSON>, který je uveden v proměnné: %s", "ttGraphMultiinstance": "Multiinstance", "ttGraphNoneMand": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "ttGraphOnlyOnce": "<PERSON><PERSON><PERSON><PERSON> pouze jednou", "ttGraphSave": {"heading": "Uložit graf a založit šablonu", "body": ""}, "ttGraphStart": "Úkol bude aktivován automaticky po spuštění případu", "ttGraphTaskHiearchy": "Řešitel", "ttGraphTaskMethod": "Úkol bude zadán", "ttGraphTaskOwner": "Supervizor úkolu", "ttGraphTaskOwnerOS": "Manažer organizační <PERSON>", "ttGraphTaskOwnerPO": "Vlastník případu", "ttGraphTaskOwnerSU": "Vybraný uživatel", "ttGraphTaskRole": " s rolí", "ttGraphTaskTypeA": "Automatický úkol", "ttGraphTaskUser": "Řešitel", "ttGraphWait1": "Chování na vstupu: č<PERSON><PERSON> na jeden", "ttGraphWaitA": "Chování na vstupu: čeká na všechny", "ttGraphWaitFirst": "Chování na vstupu: čeká na všechny, spouští první", "ttGraphWaitN": "Chování na vstupu: čeká na %s", "ttHandover": {"heading": "Předat úkol", "body": "Umožní úkol předat na jiného uživatele z nabídky."}, "ttDelegate": {"heading": "Delegovat úkol", "body": "Umožní vybrat jiného řešitele úkolu v rámci svých podřízených."}, "ttReject": {"heading": "Odmítnout úkol", "body": "Odmítnutí přiřadí úkol supervizorovi úkolu, který vybere jiného řešitele."}, "ttHelp": {"heading": "Instantn<PERSON>", "body": "Povolení nebo zakázání instantní nápovědy. Nápověda je zobrazována ve formě informačních obláčků k prvkům uživatelského rozhraní, nad kterými se uživatel zastaví s kurzorem myši."}, "ttHome": {"heading": "Úvodní stránka u<PERSON>", "body": "Jednotné místo se všemi informacemi pro běžného uživatele. K dispozici je celkový přehled ve formě dashboardu."}, "ttHtml": {"heading": "Generování dokumentace", "body": "Vygenerování HTML dokumentace šablony procesu. V závislosti na typu prohlížeče je možné dokument ihned zobrazit nebo uložit na disk."}, "ttInclusion": {"heading": "Zařazení", "body": "Vyexportuje soubor se souhrnem opravnění a zařazení uživatele, tj. vš<PERSON><PERSON> role př<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, organiza<PERSON><PERSON><PERSON>, kde je členem nebo man<PERSON>, včetně hierarchie nadřízených a úkolů, kde je uživatele supervizorem(kou)."}, "ttInvAttendees": "Účastníci", "ttInvDTEnd": "Konec", "ttInvDTStart": "Start", "ttInvLocation": "<PERSON><PERSON><PERSON>", "ttInvitation": "Pozvánka", "ttJustSave": {"heading": "<PERSON>", "body": "Uloží změny."}, "ttLock": {"heading": "Zámek", "body": "Zamkne nebo odemkne položku."}, "ttLockUser": {"heading": "Zámek", "body": "Zamkne nebo odemkne uživatele."}, "ttLogout": {"heading": "Odhlášení", "body": "Odhlášení uživatele. Po úspěšném ukončení práce s aplikací je zobrazen úvodní přihlašovací dialog."}, "ttMapping": {"heading": "Mapování", "body": "Celkový přehled přiřazení proměnných pro čtení (R), <PERSON><PERSON><PERSON> (W) a povinný zápis (M) v jednotlivých úkolech s možností editace přiřazení."}, "ttNewCase": {"heading": "Nový případ", "body": "Založení nové instance procesu – nového případu. Lze vybírat z dostupných šablon procesů nebo vytvořit případ bez předem definované struktury úkolů."}, "ttNewOverview": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Umožní nadefinovat nový přehled."}, "ttOrgStructure": {"heading": "Organizační struktura", "body": ""}, "ttParent": {"heading": "Nadřazený", "body": "Přepnutí na případ, ze kterého byl zobrazený případ vytvořen jako podproces."}, "ttPhoto": {"heading": "Fotografie", "body": "Nahrání fotografie uživatele do uživatelského profilu. Podporované formáty: GIF, JPG a PNG. Velikost obrázku bude automaticky upravena."}, "ttPlans": {"heading": "Plánování", "body": "Nastavování pravidel pro automatické jednorázové nebo opakované spouštění instancí procesů – případů podle zadaných parametrů. Pro přístup je nutná role $Administrator."}, "ttPrint": {"heading": "Tisk", "body": "Vytvoří sestavu pro tisk."}, "ttRecalc": {"heading": "Přepočet", "body": "Znovu přepočítá všechny proměnné."}, "ttRedirectToPrc": {"heading": "Případ", "body": ""}, "ttResetDash": {"heading": "Reset", "body": "<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "ttResetSearch": {"heading": "Reset", "body": "Resetuje vyhledávací formulář."}, "ttRestoreTemp": {"heading": "Obnovení <PERSON>lon<PERSON>", "body": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>."}, "ttRevision": {"heading": "Revize", "body": "Umožní nahrát novou verzi souboru."}, "ttRoles": {"heading": "Správa rolí", "body": ""}, "ttRunEvent": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Vyvolání události v tomto případu."}, "ttSave": {"heading": "Uložit", "body": "Uloží změny a zavře okno."}, "ttSaveDMSCols": {"heading": "Ulož<PERSON> s<PERSON>", "body": ""}, "ttSaveSettings": {"heading": "Uložit", "body": "Uloží změny."}, "ttSaveTsk": {"heading": "<PERSON>", "body": "Rozpracovaný úkol bude jen uložen a můžete se k němu později vrátit."}, "ttSearch": {"heading": "Hledat", "body": "<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "ttSendNote": {"heading": "Přidat poznámku", "body": "Umožní přidat novou poznámku."}, "ttSetConnectionCond": {"heading": "Podmínka", "body": "Přidání nebo editace vazební podmínky. Editace se provádí pro označenou vazbu. Označení se provede kliknutím na vazbu nebo na symbol podmínky."}, "ttSetDefaultDash": {"heading": "Nastavit jako výchozí dashboard", "body": "Nastaví aktuální uspořádání dashboardu jako výchozí."}, "ttShowHideBtn": {"heading": "Rozbalit / zabalit", "body": "Částečně schová nebo rozbalí hlavní menu."}, "ttSleepCase": {"heading": "Uspat případ", "body": "Označí případ jako us<PERSON>. Případ nebude dále zobrazován mezi aktivními ú<PERSON>ly, ale v případě potřeby je možno změnit stav opět na aktivní a celý případ dokončit později."}, "ttSolve": {"heading": "Řešit úkol", "body": "Zobrazí dialog umožňující řešení přiřazeného úkolu podle předdefinované <PERSON>."}, "ttStatePlan": {"heading": "Stav", "body": "Definuje stav plánu."}, "ttStatusHdr": {"heading": "Změna sta<PERSON>", "body": "Akce se provádí pro označenou hlavičku. K dispozici jsou stavy „aktivní“ a „neaktivní“. Označení se provede kliknutím v tabulce hlaviček šablony."}, "ttStatusTemp": {"heading": "<PERSON><PERSON><PERSON><PERSON> sta<PERSON>", "body": "Správa životního cyklu šablony se provádí nastavením jejího stavu. K dispozici jsou stavy „ve vývoji“, „aktivní“, „neaktivní“ a „smazaná“. Akce se provádí pro označenou šablonu. Označení se provede kliknutím v tabulce šablon případů."}, "ttSubprocess": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "body": "Přepnutí na případ, který byl vytvořen jako podproces v procesu zobrazeného případu."}, "ttTabsButtonMore": {"heading": "V<PERSON>ce", "body": "Rozbalí da<PERSON>š<PERSON> m<PERSON>."}, "ttTakeTsk": {"heading": "Převz<PERSON><PERSON>", "body": "Umožní převzetí úkolu k řešení."}, "ttTemps": {"heading": "Šablony procesů", "body": "Centrální místo pro správu šablon procesů. Pro přístup je nutná role $PowerUser."}, "ttTiming": {"heading": "Časování", "body": "Zadejte začátek a konec úkolu."}, "ttTsks": {"heading": "Úkoly", "body": ""}, "ttUploadSettings": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": ""}, "ttUserSetting": {"heading": "Uživatelská nastavení", "body": "Nastavení kontaktních informací <PERSON>, přístupov<PERSON><PERSON> hesla a uživatelských preferencí. Uživatel s rolí $Administrator mů<PERSON><PERSON> dále spravovat informace o své organizaci a instanci aplikace TeamAssistant."}, "ttUsers": {"heading": "Administrace uživatelů", "body": "Centrální sprá<PERSON>, organizační struktury a uživatelských rolí. Pro přístup je nutná role $Administrator."}, "ttValidation": {"heading": "Validace", "body": "Zvaliduje šablonu a zobrazí všechny existující smyčky v šabloně, upozorní na nesplnitelné podmínky a nepoužité proměnné."}, "ttViewFile": {"heading": "Zobrazit", "body": ""}, "ttWakeUpCase": {"heading": "Probudit", "body": ""}, "ttActivateCase": {"heading": "Aktivovat", "body": ""}, "ttSetDefaultDmsCols": {"heading": "Nastavit výchozí DMS sloupce", "body": "Nastaví přiřazení DMS sloupců jako výchozí."}, "ttResetDmsCols": {"heading": "Reset", "body": "Resetuje přiřazení DMS sloupců."}, "ttRestoreDoc": {"heading": "Obnovit", "body": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> so<PERSON>."}, "ttSearchHeader": {"heading": "Vyhledávání", "body": ""}, "tue": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON>", "typeOfRepetition": "<PERSON><PERSON>ní", "unassignedSolvers": "<PERSON><PERSON> ř<PERSON>ů", "unassignedTaskSolvers": "Nepřiřazení řešitelé", "uncategorized": "Nezařazeno", "unfinishedProcesses": "Nedokončené <PERSON>", "unknown": "Neznámý", "unknownUser": "Neznámý už<PERSON>l", "unrestricted": "Neomezeně", "unspecified": "Nespecifikován", "upload": "<PERSON><PERSON><PERSON><PERSON>", "uploadFile": "<PERSON><PERSON><PERSON><PERSON>", "uploadPhoto": "<PERSON><PERSON><PERSON><PERSON> fotku", "uploadCsv": "Nahrát csv soubor", "url": "URL", "urlAddress": "Adresa URL", "urlContent": "URL obsah", "use": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user": "<PERSON>živatel", "userByOwnerOfLastTask": "<PERSON><PERSON><PERSON><PERSON>, kterého vybere poslední řešitel úkolu", "userE": "<PERSON><PERSON><PERSON><PERSON>", "userFilters": "Uživatelské filtry", "userLock": "Zámek", "userLockUnlockQ": "Opravdu chcete změnit stav uživatele {{username}}?", "userName": "Uživatelské jméno", "userId": "Uživatelské ID", "userOrgStruct": "Patří do organizační j<PERSON>", "userVice": "Zástupce", "userViced": "Z<PERSON><PERSON>ova<PERSON>ý", "users": "Uživatelé", "usersDeleted": "Smazaní", "validation": "Validace", "value": "Hodnota", "var": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "var-": "Proměnná –", "varChange": "Změna proměnné bude ohlášena všem účastníkům případu.", "varTaskMap": "Mapování", "varTemp": "Šablona proměnné", "variable": "Proměnná", "variableType": "<PERSON><PERSON> prom<PERSON><PERSON>", "vars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "varsForMandatory": "Proměnné pro povinný z<PERSON>", "varsForReading": "Promě<PERSON><PERSON> pro <PERSON>", "varsForWriting": "Proměnné pro zápis", "vices": "Zástupy", "viewCVFields": "<PERSON><PERSON><PERSON><PERSON> pole", "visForOrgStrMembers": "Viditelné pro členy org. jednotky", "visForRoleMembers": "Viditelné pro uživatele s rolí", "headerVisForRole": "Případ viditelný pro roli", "waitForNumOfInputs": "Čeká na: (poč<PERSON> vstupů)", "waitsFor": "čeká na", "waitsForAll": "čeká na všechny", "waitsForOne": "čeká na jeden", "waitsForSending": "Čeká na odeslání", "waitsRunFirst": "čeká na všechny, spouští první", "wakeUp": "Probudit", "warning": "Varování", "wed": "Středa", "weekIn": "t<PERSON>den v", "weekly": "Týdně", "width": "Šířka", "withConditions": "S podmínkou", "withoutCond": "<PERSON><PERSON> pod<PERSON>ínky", "year": "rok", "yes": "<PERSON><PERSON>", "zip": "PSČ", "move": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alertClosing1": "upozornění se <PERSON>ky zavře za:", "inDocuments": "V dokumentech", "inVariables": "V proměnných", "headerTask": "Hlavička úkolu", "planName": "Název plánu", "inBulk": "Hromadně", "confirmResetDmsColumns": "Opravdu chcete resetovat DMS sloupce?", "dmsColsUseDef": "Používáte výchozí nastavení", "dmsColsUseCust": "Používáte vlastní nastavení", "today": "Dnes", "alrPlanDeleteFailed": "Plán se nepoda<PERSON>.", "notRunning": "Neběžící", "alrLackOfPermsToAddTask": "Nemáte dostatečná oprávnění na přidání úkolu.", "dragTable": "Posouvat tabulku", "alrDownloadCsvListFailed": "Nepodařilo se stáhnout csv soubory.", "alrCsvUploadWrongExtension": "Nahrávejte jenom soubory s koncovkou *.csv", "addToFav": "Přidat do oblíbených", "renameItem": "Př<PERSON>menovat", "removeFromFav": "Odebrat z oblíbených?", "alrAddedToFav": "Přidáno do oblíbených.", "alrRemovedFromFav": "Odebráno z oblíbených.", "tskSetAssignDues": "Nastavte časová omezení pro úkol", "isNot": "nen<PERSON>", "alrTskScheduling": "Čas<PERSON>ji <PERSON>...", "alrFavouritesPageExist": "Tato stránka již je v oblíbených.", "alrFavouritesActionExist": "Tato akce již je v oblíbených.", "alrFavouriteRenamed": "Položka oblíbených byla přej<PERSON>ována.", "autoFit": "Přizpůsobit sloupce", "passwordIsShort": "<PERSON><PERSON><PERSON> je p<PERSON><PERSON> kr<PERSON>.", "changeAttrComplCases": "Změna atributů dokončených případů", "iterateOverVars": "Iterovat přes proměnn<PERSON>", "nrOfDecimalDigits": "Počet desetinných míst", "onlyNumbers": "<PERSON>", "maxNumberOfDecimals": "Maximální počet desetinných míst je", "alrInsertCsv": "Vložte CSV soubor.", "addBefore": "<PERSON><PERSON><PERSON><PERSON>", "moveBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON> před", "administration": "Administrace", "ttAdministration": {"heading": "Administrace", "body": ""}, "alrLogsLoadFailed": "Logy se nepodařilo <PERSON>.", "logs": "<PERSON><PERSON>", "message": "Zpráva", "useCompatibleTempl": "Použít kompatibilní šablonu", "overwriteExistTempl": "Přepsat existující š<PERSON>lonu", "addNewTempl": "Přidat novou šablonu", "import": "Import", "export": "Export", "confirmExportAllTempl": "Exportovat všechny šablony?", "confirmExportSelTempl": "Exportovat vybranou šablonu?", "newLogs": "Nové logy", "container": "<PERSON><PERSON><PERSON><PERSON>", "confirmRemoveDialog": "Opravdu chcete odstranit {{variable}}?", "allMyCases": "Všechny mé případy", "maintenanceMsg": "Probíhá plánovaná <span style=\"color: {{color}};\">údržba</span>", "alrMaintenanceMsg": "Probíhá pl<PERSON><PERSON><PERSON>, zkuste to prosím později.", "alrAttachDownloadLackOfPerms": "Nemáte dostatečná oprávnění ke stažení nebo soubor nebyl na<PERSON>zen.", "unableToConnect": "Připojení k <PERSON>u se nezdařilo", "tryLater": "Zkuste to prosím později nebo kontaktujte administrátora.", "enableTaskDelegation": "Povolit delegování <PERSON>", "enableRejectTask": "Povolit odmítnutí <PERSON>", "confirmRejectTask": "Opravdu chcete odmítnout úkol?", "rejectTask": "Odmítnout úkol", "delegateTask": "Delegovat", "alrRejectingTask": "Odmítám <PERSON>...", "alrTaskRejected": "Úkol byl odmítnut.", "alrTaskRejectFailed": "Úkol se nepodařilo odmítnout.", "alrTaskDelegating": "Deleg<PERSON>ji <PERSON>...", "alrTaskDelegated": "Úkol byl delegován na uživatele:", "alrFailedTaskDelegate": "Úkol se nepodařilo delegovat.", "delegateOnUser": "Delegovat na uživatele", "plnAssignmentCond": "Pokud v poli „Přiřazení“ nevyberete žádného uživatele, vytvoří se seznam iniciátorů až vyhodnocením omezujících podmínek v okamžiku spuštění plánu", "alrUserFiltersSettingsFailed": "Nastavení uživatelských filtrů se nepodařilo ul<PERSON>ž<PERSON>.", "general": "Obecné", "alrUserPhotoLoadFailed": "Uživatelskou fotku se nepodařilo načíst.", "publicDynTable": "Veřejná dynamická tabulka", "isFullIndexed": "Ve vyhledávání", "datetimeIndexed": "Indexován (kdy)", "toIndex": "Čeká na indexaci", "toReindex": "Čeká na reindexaci", "solverChanged": "Řešitel byl změněn v {{count}} úkolech.", "changeSolverFailed": "Řešitele se nepo<PERSON><PERSON>ilo <PERSON>.", "alrTikaParsingFailed": "Při parsování dokumentu však došlo k chybě.", "alrIndexingFailed": "Indexování dokumentu selhalo.", "alrTikaNotRunning": "Služba pro vytěžení metadat není dostupná.", "alrIndexingServiceNotRunning": "Služba pro indexaci dokumentů není dos<PERSON>ná.", "alrFulltextNotSet": "Fulltext nen<PERSON> nastaven.", "asc": "Vzestupně", "desc": "Sestupně", "restore": "Obnovit", "alrLogosLoadFailed": "Nepodařilo se načíst loga.", "indexedDocsCount": "v celkem {{count}} dokumentech", "alrIndexedCountLoadFailed": "Fulltextové vyhledávání není momentálně k dispozici.", "searchAll": "Prohledat vše", "searchActual": "Jen aktuální", "runIndexing": "Indexovat", "alrDocumentIndexing": "Indexuji dokument...", "alrDocumentIndexed": "Dokument byl zaindexován a lze jej nalézt ve v<PERSON>hledávání.", "alrDocumentIndexedWithMinMetadata": "Do<PERSON><PERSON> by<PERSON>.", "alrDocumentIndexingFailed": "Indexování dokumentu se nezdařilo.", "changingUserProfileForbidden": "Změna uživatelského profilu je zakázána.", "uploadingPhotoForbidden": "Nahrání fotky je zakázáno.", "alrValidationCalcError": "Chyba ve validaci vý<PERSON>ů", "maintenance": "Údržba", "maintenanceActivate": "Aktivovat údržbu", "maintenanceInfoText": "Začátek a konec se zobrazí uživatelům po aktivaci údržby.", "maintenanceMode": "<PERSON><PERSON><PERSON>", "alrAvailableCalcFailed": "Dostupné výpočty se nepodařilo načíst.", "alrFillDataForSearch": "Vyplňte prosím parametry pro vyhledávání.", "youAreHere": "Nacházíte se zde", "invalidDate": "Neplat<PERSON><PERSON> datum", "alrInvalidFileFormat": "Neplatný form<PERSON>t so<PERSON>.", "alrEnter3characters": "Zadejte prosím alespoň tři znaky.", "changeCaseOwner": "Změna vlastníka případu", "actualCaseOwner": "Stávající vlastník", "newCaseOwner": "Nový vlastník", "alrCaseOwnerChanged": "Vlastník případu byl změněn.", "alrChangeCaseOwnerFailed": "Vlastníka případu se nepodařilo změnit.", "alrCsvSaving": "Nahrávám CSV soubor...", "alrCsvSaveFailed": "CSV soubor se nepoda<PERSON>.", "alrCsvSaved": "CSV soubor byl nahrán.", "allTemplates": "Všechny šablony", "specifyCaseIds": "Zadat ID případů", "caseIds": "ID případů", "caseId": "ID případu", "separBySemicolon": "oddě<PERSON><PERSON> středníkem", "alrAddCaseIds": "Zadejte prosím ID šablon", "headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header": "Hlavička", "defaultHeaderName": "Výchozí název hlavičky", "headerName": "<PERSON><PERSON><PERSON><PERSON>", "addHeader": "Přidat hlavičku", "editHeader": "Upravit hlavičku", "templateName": "<PERSON><PERSON><PERSON><PERSON>", "rolesExecRightsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> role, k<PERSON><PERSON> budou moci spou<PERSON><PERSON><PERSON><PERSON> p<PERSON>y", "orgUnitsExecRightsText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>te organizační <PERSON>, k<PERSON><PERSON> budou moci spoušt<PERSON>t případy", "selectedHeader": "vybranou hlavičku", "alrHeaderDeleted": "Hlavička byla s<PERSON>zána!", "alrHeaderDeleteFailed": "Hlavičku se nepodařilo s<PERSON>.", "alrHeaderSaveFailed": "Hlavičku se nepodařilo uložit.", "alrHeaderSaved": "Hlavi<PERSON><PERSON> byla ul<PERSON>.", "alrHeadersLoadFailed": "Data hlavičky se nepodařilo načíst.", "identificator": "<PERSON><PERSON><PERSON>", "includeDataSimilarProcesses": "Zahrnout data všech obdobných procesů", "confirmCopyCv": "Opravdu chcete zkopírovat vybraný přehled?", "alrCreatingCopyCv": "Ko<PERSON><PERSON><PERSON><PERSON>...", "alrCvCopied": "<PERSON><PERSON><PERSON><PERSON> byl zko<PERSON>írov<PERSON>.", "alrCopyCvFailed": "P<PERSON><PERSON>led se nepodařilo zkopírovat.", "copyingTemplate": "Kopírov<PERSON><PERSON>", "alrCheckTempImportFailed": "Kontrola importu šablony se nezdařila.", "warnings": "Varování", "missingEventsFiles": "Chybějící soubory ud<PERSON>í", "missingEventsFilesText": "<PERSON><PERSON><PERSON><PERSON> soubor {{- file}} z ud<PERSON><PERSON><PERSON> {{- event}}", "printsOfTemplates": "<PERSON><PERSON><PERSON>", "printsOfTemplatesText": "Věnujte prosím pozornost tisku {{- print}} z šablony {{- template}}. Hodnota: {{- value}}", "dupliciteTaskNames": "Dup<PERSON><PERSON><PERSON> n<PERSON>", "dupliciteTaskNamesText": "V šabloně {{- template}} je v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stejn<PERSON><PERSON> úkol {{- task}} {{- taskId}}, to způsobí rozpad linků!", "dynTableUsed": "Po<PERSON>žita dynamic<PERSON> ta<PERSON>", "suspiciousCalc": "Podezřelé v<PERSON>", "suspiciousCalcText": "Možnost chybějící role/organizace/uživatele ve výpočtu {{- calc}}", "missingEvents": "Chybějící u<PERSON>", "missingEvent": "Chybí událost", "wrongMappingDomains": "Chybné <PERSON>í <PERSON>", "wrongMappingDomainsText": "<PERSON><PERSON> {{- task}} z šablony {{- template}} obsahu<PERSON> š<PERSON>tný název domény, aktuální doména je {{-actDom}}", "taskDescription": "<PERSON><PERSON>", "eventsUrl": "URL událostí", "eventsUrlText": "Možná chyba v URL události {{- event}}, aktuální doména je {{- actDom}}", "param": "Parametr", "alrServiceNotForTable": "Data této služby nejsou vhodná pro zobrazení v tabulce.", "alrServiceDataFailedLoad": "Data služby se nepodařilo načíst.", "alrServiceNoData": "Služba neobsahuje žádná data.", "tableColumns": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "datetime": "Datum a čas", "exactDatetime": "Přesný datum a čas", "dashRestNoColumns": "Nejsou nastaveny žádné sloupce – zvolíte je v nastavení kontejneru", "loadService": "Načíst službu", "useCompatibleRole": "Použít kompatibilní roli", "overwriteExistRole": "Přepsat existující roli", "addNewRole": "Přidat novou roli", "templateImportFailed": "Import šablony se nezdařil.", "templateImport": "<PERSON><PERSON><PERSON>", "templateImportNoData": "Nebyla nalezena data pro import šablony.", "variableImportNoData": "Nebyla nalezena data pro import proměnných.", "ttTemplateImport": {"heading": "<PERSON><PERSON><PERSON>", "body": "Po výběru souboru s definicí jedné nebo více <PERSON> je <PERSON>eno jejich <PERSON>."}, "showUnfinishedProcesses": "Zobrazit nedokončené případy", "expMaintenanceEnd": "Předpokládaný konec údržby", "alrScriptSaveFailed": "Skript se nepodařilo <PERSON>.", "editScript": "Up<PERSON><PERSON>t skript", "addScript": "<PERSON><PERSON><PERSON><PERSON> sk<PERSON>t", "alrRunScript": "Spouštím skript...", "alrScriptCompleted": "Skript dokončen.", "alrFailedScriptStart": "Skript se nepodařilo s<PERSON>.", "alrScriptDocsLoadFailed": "Dokumentaci skriptů se nepodařilo načíst.", "alrScriptLoadFailed": "Skripty se nepodařilo na<PERSON>.", "switchAdminUser": "Přepínání admin/už<PERSON>l", "ttSwitchAdminUser": {"heading": "Přepínání admin/už<PERSON>l", "body": ""}, "ttSwitchViewport": {"heading": "Přepínání zobrazení mobil/PC", "body": ""}, "alrEventDataLoadFailed": "Data události se nepodařilo načíst.", "alrEventRuleDataLoadFailed": "Data pravidla události se nepodařilo načíst.", "cancellation": "Stornování", "tTaskAutoCancellCaption": "Úkol bude automaticky stornován pokud", "codeMirrorHelp": "Pro zobrazení nápovědy klikněte kdekoli v editoru a stiskněte Ctrl + mezerník.", "codeMirrorHelpJs": "Pro seznam všech funkcí klikněte na <a href='https://lodash.com/docs/4.17.15' target='_blank'>https://lodash.com/docs/</a>", "addEvent": "Přidat událost", "editEvent": "Upravit událost", "term": "Termín", "columnOrder": "Pořad<PERSON> slou<PERSON>ce", "alrLoadEventsButtonsFailed": "Nepodařilo se načíst tlačítka", "showButtonsCol": "Zobrazit sloupec s akcemi", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enableButtonInTasks": "Zobrazit jako tlačítko v přehledu úkolů", "alrEventDoesntExist": "Požadovaná událost neexistuje.", "alrEventRuleSaveFailed": "Pravidlo události se nepodařilo ulož<PERSON>.", "variableNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fsEvent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrEventDeleteFailed": "Událost se nepodařilo <PERSON>.", "fsRule": "pra<PERSON><PERSON>", "alrRuleDeleteFailed": "Pravidlo se nepoda<PERSON>.", "alrRuleStatusChangeFailed": "Stav pravidla se nepodařilo změnit.", "ruleActivateDeactivateQ": "Opravdu chcete změnit stav pravidla?", "docUploadedPrivate": "<PERSON><PERSON><PERSON> bude nahrán jako <PERSON>", "fileOwner": "Vlastník souboru", "planOk": "V pořádku", "userNotAuthToStartTempl": "Uživatel nemá právo pro spuštění šablony", "planStartDate": "Spuštěno", "useCompatibleEvent": "Použít kompatibilní událost", "overwriteExistEvent": "Přepsat existující událost", "addNewEvent": "Přidat novou událost", "useCompatibleUser": "Použít kompatibilního <PERSON>", "overwriteExistUser": "Přepsat existujícího <PERSON>", "addNewUser": "Přidat nového už<PERSON>le", "useOnlyFutureDates": "<PERSON><PERSON> b<PERSON><PERSON><PERSON> datum", "alrGenerateHtmlFailed": "Vygenerování HTML selhalo.", "alrNoPermsToAddNoteInVice": "Nemáte právo na přidání poznámky v zástupu.", "alrNoPermsToAddDocInVice": "Nemáte právo na přidání dokumentu v zástupu.", "current": "Aktuální", "indexation": "Indexace", "attemptToRestoreConnection": "Pokus o obnovení spojení za", "loginWillExpire": "Přihlášení vyprší za", "unsavedDataWillBeLost": "Neuložená data budou ztracena.", "alrFileSaveLikeAttachViceError": "Nemáte právo na uložení tisku jako dokumentu případu v zástupu!", "alrFileSaveLikeAttachStoreError": "Nepodařilo se nahrát tisk k dokumentům případu.", "useCompatibleUnit": "Použít kompatibilní org. jednotku", "overwriteExistUnit": "Přepsat existující org. jednotku", "addNewUnit": "Přidat novou org. jednotku", "addNewDynTable": "Přidat novou dyn. tabulku", "useCompatibleDynTable": "Použít kompatibilní dyn. tabulku", "addNewCalcScript": "Přidat nový skript", "useCompatibleCalcScript": "Použít kompatibilní skript", "enterDiffNameRoot": "Zadejte prosím jiný název než Root.", "ttTemplatesExport": {"heading": "Export <PERSON>", "body": "Export označené šablony do souboru. Lze zvolit jméno a umístění exportního souboru. Akce se provádí pro označenou šablonu. Označení se provede kliknutím v tabulce šablon případů."}, "ttTemplatesExportAll": {"heading": "Export všech šablon", "body": "Export všech aktuálně zobrazených šablon do souboru. Lze zvolit jméno a umístění exportního souboru. Výběr šablon lze omezit vhodným nastavením filtrovacích podmínek."}, "exportAll": "Exportovat vše", "noTemplatesToExport": "Žádné <PERSON>ablony k <PERSON>u.", "skip": "Přeskočit", "ttSkipTemplate": {"heading": "Přeskočit šablonu", "body": "Přeskočí import aktuální šablony a zobrazí další."}, "alrInvalidImportData": "Nevalidní data pro import", "alrUsersNotLoaded": "Uživatele se nepodařilo na<PERSON>t.", "caseOverview": "<PERSON><PERSON><PERSON><PERSON>", "alrRolesNotLoaded": "Role se se nepo<PERSON><PERSON><PERSON>.", "changeLang": "Změnit jazyk", "reactivatesPlan": "reaktivuje plán", "alrOrgUnitsNotLoaded": "Org. jednotky se nepodařilo na<PERSON>.", "refreshPage": "Obnovit stránku", "stayLogged": "Zůstat přihlášen", "showTime": "Zobrazovat čas v přehledech", "managerIn": "man<PERSON>ž<PERSON> v {{orgUnit}}", "usageStats": "Statistiky použití", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrUsageStatsLoadFailed": "Statistiky použití se nepodařilo načíst.", "accessLog": "Přístupy", "durationInMs": "Délka požadavku (ms)", "task": "Úkol", "operation": "Operace", "active_users": "Počet aktivních uživatelů", "active_template_processes": "Počet aktivních procesů", "active_headers": "Počet aktivní<PERSON>", "active_users_able_to_create_a_process": "Počet aktivních <PERSON>, k<PERSON><PERSON><PERSON> mohou spustit nějaký proces", "users_that_solved_a_task": "Počet uživatelů, kteří vyřešili alespoň jeden úkol", "solvers_or_can_create_a_process": "Počet řeš<PERSON>ů, kte<PERSON><PERSON> vyř<PERSON><PERSON><PERSON>l nebo mohou spustit proces", "mobile_app_paired_users": "Počet spárovaných uživatelů mobilní aplikace", "calculationsLogs": "Logy výpočtů", "translatedScript": "Přeložený skript", "originalScript": "Originální skript", "tskId": "ID úkolu", "alrCalculationsDocsLoadFailed": "Dokumentaci k výpočtům se nepodařilo načíst.", "alrCalculationsValidationFailed": "Validace výpočtů se nezdařila.", "linkPriority": "Priorita spojení", "dateFormat": "DD.MM.RRRR", "alrConvertErrorJsonNeon": "Chyba při kon<PERSON>zi json -> neon.", "alrInvalidData": "Nevalidní data.", "sharedVar": "Sdílená proměnná", "guide": "Průvodce", "guideFs": "průvodce", "guides": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alrGuidesLoadFailed": "Průvodce se nepodařilo načíst.", "language": "Jazyk", "default": "Výchozí", "next": "Dalš<PERSON>", "previous": "Předchozí", "targetElementNotFound": "Cílový prvek nebyl nalezen", "documentation": "Dokumentace", "matchesRegular": "Nesplňuje pra<PERSON>lo", "secondAllowedValues": "povolené hodnoty; 0 je začátek minuty", "everySec": "ka<PERSON><PERSON><PERSON> sekundu", "listOfSec": "seznam sekund; např. 0,30 bude 0. a 30. sekunda", "rangeOfSec": "rozsah sekund; např. 0–5 bude 0., 1., 2., 3., 4. a 5. sekunda (lze také specifikovat seznam rozsahů 0–5,30–35)", "slashSec": "násobky; např. */5 znamená každých 5 sekund, 0–30/2 znamená každé 2 sekundy mezi 0. a 30. sekundou", "minuteAllowedValues": "povolené hodnoty; 0 je začátek hodiny", "everyMin": "<PERSON><PERSON><PERSON><PERSON> minutu", "listOfMin": "vý<PERSON>et minut; např. 0,30 bude 0. a 30. minuta", "rangeOfMin": "rozsah minut; např. 0–5 bude 0., 1., 2., 3., 4. a 5. minuta (lze také specifikovat seznam rozsahů 0–5,30–35)", "slashMin": "násobky; např. */5 znamená každých 5 minut, 0–30/2 znamená každé 2 minuty mezi 0. a 30. minutou", "hourAllowedValues": "povolené hodnoty; 0 je půlnoc", "everyHour": "<PERSON><PERSON><PERSON><PERSON>", "listOfHour": "v<PERSON><PERSON><PERSON> hodin; např. 0,12 bude 0. a 12. hodina", "rangeOfHour": "r<PERSON><PERSON><PERSON> hodin; nap<PERSON>. 19–23 budo<PERSON> hodiny 19, 20, 21, 22 a 23 (lze tak<PERSON> specifiko<PERSON> sez<PERSON> roz<PERSON> 0–5,12–16)", "slashHour": "násobky; např. */4 znamená každé 4 hodiny, 0–20/2 znamená každé 2 hodiny mezi 0. a 20. hodinou", "dayAllowedValues": "povolené hodnoty", "everyMonthDay": "každý den v měsíci", "listOfDay": "seznam dnů; např. 1,15 bude 1. a 15. den v <PERSON>ě<PERSON><PERSON>", "rangeOfDay": "roz<PERSON>h dní; např. 1–5 bude 1., 2., 3., 4. a 5. den (lze také specifikovat seznam rozsah<PERSON> 1–5,14–30)", "slashDay": "násobky; např. */4 znamená každé 4 dny, 1–20/2 znamená každé 2 dny mezi 1. a 20. dnem měsíce", "allowedValues": "povolené hodnoty", "everyMonth": "<PERSON><PERSON><PERSON><PERSON>", "listOfMonth": "v<PERSON><PERSON><PERSON> m<PERSON>; např. 1,6 bude leden a červen", "rangeOfMonth": "roz<PERSON><PERSON>; nap<PERSON>. 1–3 b<PERSON> leden, <PERSON><PERSON> a březen (lze také specifikovat seznam roz<PERSON>ů 1–4,8–12)", "slashMonth": "násobky; např. */4 znamená každé 4 měsíce, 1–8/2 znamená každé 2 měsíce mezi lednem a srpnem", "weekAllowedValues": "povolené hodnoty; 0=<PERSON><PERSON><PERSON><PERSON>, 1=pond<PERSON><PERSON><PERSON>, 2=<PERSON><PERSON><PERSON>, 3=st<PERSON><PERSON>, 4=čtvrtek, 5=pátek, 6=sobota", "everyWeekDay": "každý den v týdnu", "listOfWeekDay": "seznam dní; např. 1,5 bude pondělí a pátek", "rangeOfWeekDay": "rozsah dní; např. 1–5 bude <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, čtvrtek a pátek (lze také specifikovat seznam rozsahů 0–2,4–6)", "slashWeek": "násobky; např. */4 znamená každé 4 dny, 1–5/2 znamená každé 2 dny mezi pondělím a pátkem", "contrab": "<PERSON>ron – pole {{variable}}", "cSecond": "sekund", "cMinute": "minut", "cHour": "hodin", "cDay": "dní", "cMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cWeekDay": "dní v týdnu", "seconds": "sekundy", "minutes": "minuty", "hours": "hodiny", "days": "dny", "weeks": "týdny", "socketOk": "Tabulka má k dispozici aktuální data", "socketBroken": "Obnovit spojení pro průběžnou aktualizaci dat", "newTask": "Nový úkol", "report": "Report", "ttCaseReport": {"heading": "Report", "body": ""}, "usersRights": "Uživatelská práva", "visPerRole": "Viditelnost na roli", "manualEvents": "Ruční u<PERSON>", "noTasks": "Žádné nové <PERSON>", "emptyFavs": "Seznam oblíbených je prázdný", "crons": "<PERSON><PERSON><PERSON>", "cronsHistory": "Historie kronů", "redirBefStart": "<PERSON><PERSON><PERSON> s<PERSON>štěním přesměrovat na", "lastRun": "Poslední spuštění", "nextRun": "Následující spuštění", "syntax": "Syntaxe", "alias": "<PERSON><PERSON>", "stop": "Stop", "restart": "<PERSON><PERSON>", "restartCronProcess": "Restartovat kontext", "ttRestartCron": {"heading": "Restart<PERSON>t cron", "body": ""}, "ttRestartCronProcess": {"heading": "Restartovat proces", "body": ""}, "ttResetCron": {"heading": "<PERSON><PERSON><PERSON><PERSON> cron", "body": ""}, "ttRunCron": {"heading": "<PERSON><PERSON><PERSON><PERSON> cron", "body": ""}, "ttStopCron": {"heading": "Stop", "body": ""}, "ttStatusCron": {"heading": "Status", "body": ""}, "alrCronStopped": "<PERSON><PERSON> byl zastaven.", "alrCronStopFailed": "Požadavek na zastavení cronu selhal.", "alrCronRunning": "<PERSON><PERSON> byl <PERSON>.", "alrCronRunFailed": "Spuštění cronu sel<PERSON>o.", "alrCronReset": "<PERSON><PERSON> byl resetován do defaultn<PERSON>ho stavu.", "alrCronResetFailed": "Požadavek na resetování cronu selhal.", "alrCronRestart": "<PERSON><PERSON> byl <PERSON>.", "alrCronRestartFailed": "Požadavek na restartování cronu selhal.", "alrCronUpdated": "<PERSON><PERSON> byl <PERSON> ul<PERSON>.", "alrCronUpdateFailed": "Požadavek na updatování cronu selhal.", "confirmRunCronDialog": "Opravdu chcete spustit vybraný cron?", "confirmStopCronDialog": "Opravdu chcete zastavit vybraný cron?", "confirmResetCronDialog": "Opravdu chcete resetovat cron do továrního nastavení?", "confirmRestartCronDialog": "Opravdu chcete restartovat vybraný cron?", "confirmUpdateCronDialog": "Opravdu chcete změnit status cronu?", "alrProcessRestart": "<PERSON><PERSON> proces byl <PERSON><PERSON>!", "alrProcessRestartFailed": "Požadavek na restartování procesu selhal.", "confirmRestartProcessDialog": "Opravdu chcete restartovat celý cron proces? <PERSON><PERSON>, nastane kompletní restart všech cronů a celého kontextu.", "cronParams": "Parametry", "alrPresetLogFiltersLoadFailed": "Přednastavené filtry logů se nepodařilo načíst.", "timeRange": "Časový rozsah", "presetFilters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "params": "Parametry", "authentication": "Autentizace", "module": "<PERSON><PERSON><PERSON>", "paramLabel": "Název parametru", "authMethod": "modul autentizace", "taskAlreadyEdited": "Úkol je již editován jiným uživatelem.", "taskEditedByAnotherUser": "Úkol začal editovat další uživatel.", "tempAlreadyEdited": "Šablona je již editována jiným uživatelem.", "tempEditedByAnotherUser": "Šablonu začal editovat další uživatel.", "test": "Test", "notInRightormat": "Není ve správném form<PERSON>", "ttTableExportExcel": {"heading": "Export tabulky", "body": "Exportuje tabulku do souboru xlsx"}, "ttTableExportCsv": {"heading": "Export tabulky", "body": "Exportuje tabulku do souboru csv"}, "searchInSuspended": "Hledat i v uspaných případech", "alrScriptDocsFailed": "Dokumentaci ke skriptu se nepodařilo uložit.", "currentlyRunning": "Aktuálně běží", "onStart": "<PERSON><PERSON><PERSON>", "onEnd": "Na konci", "onHand": "R<PERSON>č<PERSON><PERSON>", "onRecalc": "<PERSON><PERSON><PERSON> p<PERSON>čítán<PERSON>", "onPull": "<PERSON><PERSON><PERSON>", "yesterday": "Včera", "tomorrow": "<PERSON>í<PERSON>", "replyRecipient": "Adresát pro odpověď", "bcRecipient": "Adresát s<PERSON> kopie", "copyRecipient": "Adres<PERSON>t kop<PERSON>", "emptyHe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "archivedLogs": "Archivní logy", "basicMode": "Zák<PERSON><PERSON><PERSON> mód", "expertMode": "Expertní mód", "ttBasicMode": {"heading": "Zák<PERSON><PERSON><PERSON> mód", "body": "Skryje některé položky nebo možnosti ve formuláři."}, "ttExpertMode": {"heading": "Expertní mód", "body": "Zobrazí skryté položky nebo možnosti ve formuláři."}, "helpOverviewFolder": "<PERSON><PERSON><PERSON>led můžete zařadit do adresářové struktury pomocí lomítek.<br /><i>(např. Faktury/Všechny faktury přijaté)</i>", "helpOverviewIncludeSimilar": "Pokud z<PERSON><PERSON><PERSON>, budou se zobrazovat i případy z ostatních hlaviček šablony.", "helpOverviewSysVars": "Pole označená (sys) jsou systémo<PERSON> pole, k<PERSON><PERSON> jsou součástí každého procesu.", "customization": "Přizpůsobení", "elementColor": "Barva prvku", "fontColor": "<PERSON><PERSON>", "fontSize": "Velikost písma", "bold": "Tučn<PERSON>", "cursive": "<PERSON><PERSON><PERSON><PERSON>", "off": "Vypnuto", "toPlan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alrMaintenanceComing": "V {{time}} začne plánovaná údržba systému. Uložte si prosím svoji práci.", "timeoutHMS": "Timeout: (hh:mm:ss)", "eventw": "Na tuto událos<PERSON> čeká <PERSON> „{{task}}“ z šablony „{{template}}“", "waitsForEventTip": "Případ čeká na událost: „{{event}}“", "copyToMultiinstances": "Kopírovat do multiinstancí", "showAsPreview": "Zobrazit náhled", "alrPreviewAttachmentsFailed": "Zobrazení náhledu se<PERSON>halo", "alrPreviewAttachmentsWrongFormat": "Zobrazení náhledu selhalo – nepodporovaný formát souboru", "previewNotAvailable": "Náhled dokumentu není možný vzhledem k typu dokumentu.", "configuration": "Konfigurace", "values": "Hodnoty", "defaultValues": "Výchozí hodnoty", "ttSubscribeCv": {"heading": "Odebírat p<PERSON>", "body": "Vybraný přehled Vám každý všední den v nastavený čas přijde e-mailem."}, "subscribe": "Odebírat", "time": "Čas", "externalLang": "<PERSON><PERSON><PERSON>", "hdrStatusQ": "Opravdu chcete změnit stav hlavičky?", "small": "<PERSON><PERSON>", "medium": "Střední", "large": "<PERSON><PERSON><PERSON><PERSON>", "alrTemplTsksLoadFailed": "Úkoly šablony se nepodařilo načíst.", "applyInTasks": "Použít v úkolech", "caseStatuses": "Stavy případu", "statuses": "Stavy", "Manuals": "<PERSON><PERSON><PERSON><PERSON>", "forReading": "Pro čtení", "forReadWrite": "Pro čtení i zápis", "addVersion": "Nová verze", "size": "Velikost", "prevWorkDay": "Předchozí pracovní den", "ttCreateTempVersion": {"heading": "Vytvořit novou verzi šablony", "body": ""}, "version": "Verze", "alrTempVersionsLoadFailed": "Verze šablony se nepodařilo načíst.", "alrChangeTempVersionFailed": "Nepodařilo se změnit verzi šablony.", "alrCreateTempVersionFailed": "Vytvoření nové verze šablony se nezdařilo.", "confirmCreateTempVersion": "Opravdu chcete vytvořit novou verzi šablony?", "applyInAllTasks": "Použít ve všech úkolech", "mandatoryVar": "Povinná proměnná", "emptyRequiredVarMessage": "<PERSON><PERSON><PERSON>, p<PERSON>áz<PERSON><PERSON> povinná proměnná", "duration": "Doba trvání", "alrDynConditionsFailed": "Úkol nelze splnit. Zkuste prosím obnovit stránku, případně kontaktujte Administrátora nebo Helpdesk.", "caseActivation": "Založení případu", "average": "Pr<PERSON><PERSON>ě<PERSON>", "performanceLogs": "Logy výkonu", "displayingOverview": "Zobrazení přehledu", "taskSolve": "Splnění <PERSON>lu", "displayingCO": "Zobrazení CASE OVERVIEW", "printCreation": "Vytvoření tisku", "entityId": "ID entity", "copyTask": "Kopírování <PERSON>", "checkProcessCompletion": "Kontrola dokončení případu", "findingSolver": "<PERSON><PERSON><PERSON><PERSON>", "publicFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "usage": "Použití", "serviceConsole": "Servisní konzole", "selectAll": "Označit vše", "logos": "Loga", "overviewWithTasks": "<PERSON><PERSON><PERSON><PERSON> s <PERSON>ly", "printIsReady": "Tisk je připraven", "alrChangelogLoadFailed": "Načtení changelogu selhalo.", "inJs": "<PERSON><PERSON> sk<PERSON>", "ttCopyDtDefinition": {"heading": "Kopírovat definici tabulky", "body": "Zkopíruje definici označené dynamick<PERSON>."}, "confirmCopyTableDefinition": "Opravdu chcete zkopírovat definici tabulky {{tableName}}?", "alrCopying": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "alrCopyFailed": "Kopírování se nepodařilo.", "fallback": "Fallback", "syncEnabled": "Synchronizace", "systemGuideNote": "<PERSON><PERSON>ah systémových Průvodců nelze měnit. Pokud chcete zobrazovat jiný obsah, upravte Aktivitu systémového Průvodce na neaktivní a zkopírujte jeho obsah do nového Průvodce.", "alrAnotherUserLogged": "V jiném okně je přihlášen jiný uživatel!", "userLocked": "Uživatel je zamčený", "visInternalUserOnly": "Viditelné pouze pro interní <PERSON>", "showSelectedOnly": "Zobrazit jen v<PERSON>bra<PERSON>é", "clickToSelect": "Klikněte pro výběr", "restrictRoleAssignment": "<PERSON><PERSON><PERSON><PERSON>lování role pro roli", "restrictions": "Omezení", "restrictTableHandling": "Omezení z<PERSON>zení s tabulkou", "toRole": "Na roli", "inCalcToHeader": "Ve výpočtech na hlavičku", "loginBtnColor": "Barva přihlašovacího tlačítka", "certificates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "certificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateVar": "certif<PERSON><PERSON><PERSON>", "tspSources": "<PERSON><PERSON><PERSON> razítka", "tspSource": "<PERSON><PERSON><PERSON><PERSON>", "confirmExpandDynRowsNewAssignments": "<PERSON><PERSON>, nové přiřazení! Proměnné nemají nastaveny osy. Chcete roztáhnout všechny dynamické řádky?", "confirmExpandDynRows": "Opravdu chcete roztáhnout všechny dynamické řádky?", "expandDynRows": "Roztáhnout <PERSON>", "visible": "Viditelné", "cvcDbColumn": "Zdrojov<PERSON> sloupec", "cvTableSource": "Zdrojová tabulka", "uploadedFromFile": "<PERSON>r<PERSON><PERSON> ze souboru", "appStatus": "Stav aplikace", "loadAll": "Načíst vše", "ignoredUsers": "Ignorovaní <PERSON>", "copyRolesFrom": "Zkopírovat role od", "disableFrontendStyles": "Neaplikovat automatické styly", "activate": "Aktivovat", "confirmActivateCase": "Opravdu chcete aktivovat případ?", "alrLackOfPerms": "Nemáte dostatečná oprávnění.", "alrSending": "Odesílám...", "sequences": "Sekvence", "seqName": "Sekvence", "seqId": "ID sekvence", "seqLastRead": "<PERSON><PERSON><PERSON><PERSON>", "ttCopyRole": {"heading": "Kopírovat roli", "body": "Zkopíruje označenou roli."}, "fromCase": "Z případu", "includingData": "Vč<PERSON><PERSON><PERSON> dat", "choose": "<PERSON><PERSON><PERSON><PERSON>", "chooseFromCases": "Vyberte z případů", "valueChange": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "updateInstances": "Zahrnout změnu proměnné do instančních proměnných", "externalSource": "<PERSON><PERSON><PERSON><PERSON>", "reports": "Reporty", "confirmCopyReport": "Opravdu chcete zkopírovat vybraný report?", "graphs": "<PERSON><PERSON>", "aggregation": "Agregace", "graphNew": "<PERSON> nový", "confirmCopyGraph": "Opravdu chcete zkopírovat vybraný graf?", "alrCopyGraphFailed": "<PERSON> se ne<PERSON><PERSON><PERSON><PERSON>.", "label": "Label", "pie": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "line": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dot": "<PERSON><PERSON><PERSON><PERSON>", "bar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barGroups": "<PERSON><PERSON><PERSON><PERSON>vý – skupiny", "alrFailedGraphData": "Data grafu se nepoda<PERSON><PERSON>.", "graphSetSharing": "Nastavte sdílení grafu pro jednotlivé skupiny uživatelů", "alrGraphPointsLoadFailed": "Data bodů grafu se nepo<PERSON><PERSON><PERSON>.", "alrGraphNotFound": "<PERSON>.", "graphData": "Data grafu", "pointsData": "Body grafu", "alrGraphSaveFailed": "<PERSON> se nepo<PERSON><PERSON><PERSON>!", "graphPoint": "Bod grafu", "noOrder": "Bez řazení", "refreshGraph": "Obnovit data grafu", "viewSwitcher": "Globální filtr", "axisXglobalFilter": "Osa X – globální filtr", "axisXgroups": "Osa X – skupiny", "axisXdata": "Osa X – data", "axisYvalues": "Osa Y <PERSON> hodnoty", "axisYcolors": "Osa <PERSON> barvy", "hrAgenda": "HR agenda", "userChange": "Změna <PERSON>", "newUser": "Nový uživatel", "usersCount": "Počet uživatelů", "confirmChangeUser": "Opravdu chcete změnit uživatele?", "businessVariable": "<PERSON>z<PERSON><PERSON>", "casesCount": "Počet případů", "selected": "Vybrán<PERSON>", "selectedOnly": "<PERSON>", "addCaseRightNewUser": "Přidat přístup k případu", "visFromTaskToPull": "Viditelnost z úkolu k odběru", "toChangeConfigInfo": "Pro změnu odstraňte hodnotu ze souboru local.js", "clickToChange": "Klikněte pro změnu", "currentValue": "Aktuální hodnota", "sign": "Podepsat", "validationProtocols": "Validace", "plannedEvents": "Ak<PERSON>", "elArchiv": "E-archiv", "deletedDocs": "<PERSON><PERSON><PERSON><PERSON>", "signatures": "Podpisy", "ttArchive": {"heading": "Elektronický archiv", "body": ""}, "ttAddToZea": {"heading": "Přidat do elektronického archivu", "body": ""}, "ttRemoveFromZea": {"heading": "Odebrat z elektronického archivu", "body": ""}, "ttZeaInfo": {"heading": "Validace", "body": ""}, "ttSignZea": {"heading": "Externě podepsat", "body": ""}, "addToZea": "Přidat do e-archivu", "removeFromZea": "Odebrat z e-archivu", "reTimestampAfter": "Platnost generovaného <PERSON> (dnů)", "alrLoadFailed": "Nahrání se nezdařilo.", "replace": "Nahradit", "expireAt": "Vyprší", "result": "<PERSON><PERSON><PERSON><PERSON> validace", "validatedAt": "<PERSON><PERSON> validace", "refType": "Objekt", "eventType": "<PERSON><PERSON> akce", "errorMessage": "Chybová hláška", "errorTimestamp": "<PERSON><PERSON> ch<PERSON>by", "errorCount": "Počet chyb", "inFuture": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "Cesta k podpisu", "signedAt": "Datum vytvoření podpisu", "dropZoneZeaCertificate": "Sem přetáhněte soubor s certifikátem nebo klikněte pro výběr souboru.", "authType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "basic": "Jménem a heslem", "byCert": "Certifikátem", "alrMissingCertFile": "Vložte prosím certifikát.", "replaceTo": "Nahradit za", "autoReTimestamp": "Automatické čas. razítko", "validate": "Validace", "lastUse": "Poslední vygenerované", "createdAt": "Vytvořeno", "updatedAt": "Aktualizováno", "certificateId": "ID certifikátu", "expectedCreationTime": "<PERSON><PERSON> přidáno", "nextTSPSourceId": "ID následujícího razítka", "reTimestampAt": "Následující čas. razítko", "timestampedAt": "Poslední čas. razítko", "level": "Úroveň", "signatureB": "Zák<PERSON><PERSON><PERSON>", "signatureT": "Podpis s časovým razítkem", "signatureLt": "Podpis s dlouhodob<PERSON><PERSON> datovými certifikáty", "signatureLta": "Podpis s dlouhodobými datovými certifikáty a časovým razítkem archivu", "packaging": "Balení", "enveloped": "<PERSON><PERSON><PERSON><PERSON>", "enveloping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "detached": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "Algor<PERSON><PERSON>", "uploadAsRevision": "<PERSON><PERSON><PERSON><PERSON> jako revizi", "externalDisable": "Aktivní jen pro hromadné", "addToDms": "Přidat do DMS", "autoConvert": "Automaticky zkonvertovat", "format": "<PERSON><PERSON><PERSON>", "signatureType": "Typ podpis<PERSON>", "signature": "Podpis", "custom": "Vlastní", "batchSignDisabled": "Bez podpisu", "tasId": "ID dokumentu", "hashValue": "Hodnota hashe", "hashType": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "confirmAddToArchive": "Chcete opravdu přidat do archivu?", "independentSignature": "Nezávislý podpis", "independentValidation": "Nezávislá validace", "failureTrue": "S chybou", "failureFalse": "Bez chyb", "confirmValidateDialog": "Opravdu chcete validovat podpis?", "confirmRestartDialog": "Opravdu chcete vynulovat chyby?", "verificationResult": "Výsledek ověření", "integrityMaintained": "Integrita zachována", "signatureFormat": "<PERSON><PERSON><PERSON>", "internalTimestampsList": "Seznam interních časových razítek", "signers": "Podepisovatelé", "exhibitedBy": "Vystavil", "signedBy": "Podepsal", "validFrom": "Platnost od", "validUntil": "Platnost do", "signitureType": "Typ podpis<PERSON>", "signatureQualification": "Kvalifikace podpisu", "signatureNoTimestamps": "Podpis neobs<PERSON> razí<PERSON>", "electronicSignature": "Elektronický podpis", "electronicSeal": "Elektronická pečeť", "webSiteAuthentication": "Autentizace webový<PERSON> s<PERSON>", "QCP-n": "QCP-n: politika certifikátů pro certifikáty kvalifikované EU vydané fyzickým osobám", "QCP-l": "QCP-l: politika certifikátů pro certifikáty kvalifikované EU vydané právnickým osobám", "QCP-n-qscd": "QCP-n-qscd: politika certifikátů pro certifikáty kvalifikované EU vydané fyzickým osobám se soukromým klíčem související s certifikovaným veřejným klíčem v QSCD", "QCP-l-qscd": "QCP-l-qscd: politika certifikátů pro certifikáty kvalifikované EU vydané právnickým osobám se soukromým klíčem souvisejícím s certifikovaným veřejným klíčem v QSCD", "QCP-w": "QCP-w: politika certifikátů pro ověřování webových stránek s kvalifikací EU", "formOfReStamping": "Forma přerazítkování", "individually": "Individuálně", "archiveAsPdf": "Archivovat jako PDF", "couldNotBeVerified": "<PERSON><PERSON>ze ověř<PERSON>", "uses": "Počet použití", "countOfSignedDocuments": "Počet podepsaných dokumentů", "batchSignature": "Hromadný podpis", "standaloneSign": "Individuální podpis", "validateSignature": "Validace podpisu", "validateDoc": "Validace dokumentu", "containsSignature": "<PERSON><PERSON><PERSON><PERSON>", "reStamping": "Přerazítkovávání", "individualSignatures": "Individuální podpisy", "signatureLevel": "Úroveň podpisu", "simpleReport": "Jed<PERSON>du<PERSON><PERSON> report", "detailedReport": "Detailní report", "diagnosticReport": "Diagnostický report", "etsiReport": "ETSI report", "TOTAL_PASSED": "V pořádku", "TOTAL_FAILED": "<PERSON><PERSON><PERSON><PERSON>", "INDETERMINATE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FORMAT_FAILURE": "Podpis není v souladu s jedním ze základních standardů", "HASH_FAILURE": "Hash podepsaného datového objektu neodpovídá hashi v podpisu", "SIG_CRYPTO_FAILURE": "Podpis ne<PERSON>hl být ověřen pomocí veřejného klíče podpisovatele", "REVOKED": "Certifikát pro podpis byl zrušen a zároveň existuje důkaz, že podpis zvnikl po revokaci", "SIG_CONSTRAINTS_FAILURE": "Jeden nebo více atributů podpisu neodpovídá pravidlům ověření", "CHAIN_CONSTRAINTS_FAILURE": "Řetěz certifikátů použitý v procesu validace neodpovídá pravidlům validace vztahujícím se k certifikátu", "CERTIFICATE_CHAIN_GENERAL_FAILURE": "Sada certifiká<PERSON><PERSON> dostupných pro ověření řetězce způsobila chybu z nespecifikovaného důvodu", "CRYPTO_CONSTRAINTS_FAILURE": "Jeden z algoritmů podílející se na ověření podpisu je pod požadovanou úrovní kryptografického zabezpečení a podpis byl pořízen po rozhodné době použitelnosti algoritmu", "EXPIRED": "Podpis byl vytvořen po uplynutí doby platnosti podpisového certifikátu", "NOT_YET_VALID": "Čas podpisu je před datem vydání podpisového certifikátu", "POLICY_PROCESSING_ERROR": "<PERSON><PERSON>or validační politiky nemohl být zpracován", "SIGNATURE_POLICY_NOT_AVAILABLE": "Elektronický dokument obsahující podrobnosti o validační politice není k dispozici", "TIMESTAMP_ORDER_FAILURE": "Omezení v pořadí časových razítek podpisu nejsou respektována", "NO_SIGNING_CERTIFICATE_FOUND": "Podpisový certifikát nelze identifikovat", "NO_CERTIFICATE_CHAIN_FOUND": "Nebyl nalezen žádný řetěz certifikátů pro identifikovaný podpisový certifikát", "REVOKED_NO_POE": "Podpisový certifikát byl revokován v den/čas ověření. Algoritmus ověření podpisu však nemůže zjistit, že čas podpisu je před nebo po době revokace.", "REVOKED_CA_NO_POE": "Byl nalezen alespoň jeden řetěz certifikátů, ale byl revokován přechodný certifikát CA", "OUT_OF_BOUNDS_NOT_REVOKED": "Podpisový certifikát vyprší nebo ještě není platný k datu/času ověření a algoritmus ověření podpisu nemůže ověřit, že podpisový čas je v intervalu platnosti podpisového certifikátu. <PERSON>, že certifikát není revokov<PERSON>.", "OUT_OF_BOUNDS_NO_POE": "Platnost podpisového certifikátu vyprš<PERSON> nebo dosud není platná k datu/času ověření", "CRYPTO_CONSTRAINTS_FAILURE_NO_POE": "Jeden z algoritmů podílející se na ověření podpisu je pod požadovanou úrovní kryptografického zabezpečení a není d<PERSON>, že byl vyroben před časem, do kterého byl tento algoritmus/kl<PERSON><PERSON> považován za bezpečný", "NO_POE": "<PERSON><PERSON><PERSON><PERSON>, že podepsaný objekt byl vytvořen před nějakou kompromitující událostí", "TRY_LATER": "Pomocí dostupných informací nelze splnit všechna pravidla validace. Může však být možné tak učinit pomocí dalších informací o odvolání, které budou k dispozici později.", "SIGNED_DATA_NOT_FOUND": "Podepsaná data nelze získat", "GENERIC": "<PERSON><PERSON>", "signatureFile": "<PERSON><PERSON><PERSON> s <PERSON>pisem", "validityDays": "Platnost dnů", "qualifiedHe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "qualifiedIt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unqualifiedHe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unqualifiedIt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeValid": "<PERSON><PERSON><PERSON><PERSON> platný", "reason": "Důvod", "inTime": "v čase", "certificateQualification": "Kvalifikace certifikátu", "guaranteedHe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guaranteedIt": "<PERSON>aru<PERSON><PERSON><PERSON>", "fromQualifiedCert": "Z kvalif<PERSON>vaného cert.", "basedOnQualifiedCertHe": "Založený na kvalifikovaném certifikátu", "createdByQualifiedResHe": "Vytvořen <PERSON>ým prostředkem", "basedOnQualifiedCertIt": "Založené na kvalifikovaném certifikátu", "createdByQualifiedResIt": "Vytvořeno kvalifikovaným prostředkem", "qualification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmRemoveFromZeaDialog": "Opravdu chcete {{variable}} odstranit z elektronického archivu?", "noValidationReports": "Žádné v<PERSON>dky validace", "noSignatures": "Žádné individuální podpisy", "isHigherOrEqualThan": "<PERSON><PERSON><PERSON> být větší nebo rovno", "isInZea": "V e-archivu", "startStamping": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reTimestampAfterMinutes": "minut", "reTimestampAfterDays": "dn<PERSON>", "reTimestampAfterAll": "Platnost generovaného razítka", "refId": "ID objektu", "docWithoutAutoTimestampInfo": "Dokument bude jednorázově podepsán bez automatického vkládání časového razítka.", "validationReports": "Histor<PERSON>", "docPath": "Cesta k dokumentu", "addToArchiveInvalidSignatureError": "Soubor nelze vložit do archivu, pro<PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON> pod<PERSON>, k<PERSON><PERSON> nelze ověř<PERSON>.", "signImmediately": "<PERSON><PERSON><PERSON> pod<PERSON>", "replaceInConfiguration": "Nahradit v konfiguraci", "cancel": "Zrušit", "bulk": "Hromadné", "bulkCompletion": "Splnit hromadně", "enableBulkCompletion": "Povolit hromadné splnění", "confirmCompleteTasks": "Opravdu chcete splnit úkoly?", "plannedMaintenance": "Plánovaná údržba", "notSpecified": "Neuvedeno", "bulkCompletionVars": "Promě<PERSON><PERSON> h<PERSON>dn<PERSON>ho plnění", "alrBulkCompletionMultiTypeErr": "Hromadně lze plnit pouze úkoly s<PERSON>jného typu, můžete použít filtr.", "notifications": "Oznámení", "alrTskAlreadyTakenSomeone": "Úkol si již odebral někdo jiný.", "alrTskAlreadyTaken": "Úkol byl již odebrán.", "downloadBpmn": "stáhnout BPMN diagram", "downloadSvg": "stáhnout jako SVG obrázek", "displayForm": "Forma zobrazení", "selectedPreview": "zobrazuje se náhled", "fixedHeight": "Fixní výška (v pixelech)", "lastVersion": "Poslední verze", "separatedPreview": "Odd<PERSON><PERSON><PERSON>", "defaultZoom": "Výchozí zoom", "fixedPosition": "Zafixovat pozici", "percentInterval": "Zadejte prosím celé číslo mezi 0–5", "notPositiveNumber": "Pouze kladná čísla", "zoomDown": "Zmenšit", "zoomUp": "Zvětš<PERSON>", "rotate": "O<PERSON>č<PERSON>", "logTooBig": "Log je p<PERSON><PERSON>š obsáhlý na to, aby byl zobrazen.", "downloadLog": "<PERSON><PERSON><PERSON><PERSON> log", "confirmCopyCron": "Opravdu chcete zkopírovat vybraný cron?", "ttCopyCron": {"heading": "Kopírovat cron", "body": ""}, "onlyWorkingDays": "Pouze pracovní dny", "datesDisabled": "Vynechat data", "useView": "Použít View", "dateWithoutTime": "<PERSON><PERSON> bez <PERSON>u", "timezone": "Časové p<PERSON>", "roleRestriction": "Omezení na roli", "headerRestriction": "Omezení na hlavičku", "ttSwitchDarkmode": {"heading": "Přepínání svě<PERSON>ý/tmavý režim", "body": ""}, "advancedEditor": "Rozš<PERSON>ř<PERSON>ý editor", "externalId": "Externí ID", "passwordValidationMin": "<PERSON><PERSON><PERSON> je p<PERSON><PERSON><PERSON> kr<PERSON>tké. (minimální délka: {{count}})", "passwordValidationMax": "<PERSON><PERSON><PERSON> je <PERSON><PERSON><PERSON> d<PERSON>. (maximální d<PERSON>lk<PERSON>: {{count}})", "passwordValidationUppercase": "<PERSON><PERSON><PERSON> musí obsahovat velké písmeno. {{atLeast}}", "passwordValidationLowercase": "Heslo musí obsahovat malé písmeno. {{atLeast}}", "passwordValidationSymbols": "Heslo musí obsahovat symbol. {{atLeast}}", "passwordValidationDigits": "<PERSON><PERSON>lo musí obsahovat číslici. {{atLeast}}", "passwordValidationLetters": "Heslo musí obsahovat písmeno. {{atLeast}}", "atLeast": "Alespoň", "passwordValidationServiceErr": "<PERSON><PERSON>lo nyní nelze změnit.", "enableTasksHandoverRole": "<PERSON><PERSON><PERSON> povolit předávání úkolů a spouštění událostí uživatelům této role", "shredded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shredableVar": "Skartovatelná proměnná", "shredDocuments": "Skartovat dokumenty", "shredInDays": "Skartovat za (dny)", "fromBeginningOrendOfCase": "Od z<PERSON>/konce případu", "shredding": "Skar<PERSON><PERSON>", "addColumn": "<PERSON><PERSON><PERSON><PERSON>", "unsupportedBrowser": "Otevíráte aplikaci TeamAssistant v nepodporovaném prohlížeči Internet Explorer, některé funkce nemusí být dostupné.", "ingoreProcessRights": "Ignorace práv na případ", "cvHelpIngoreProcessRights": "V přehledu se vždy zobrazí všechny případy, bez ohledu na práva", "upperLabel": "Umístit proměnnou pod její název", "darkMode": "<PERSON><PERSON><PERSON><PERSON>", "completedTasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permissions": "Oprávnění", "caseVisibility": "Viditelnost případu", "visPerOrg": "Viditelnost na org. jednotku", "entity": "Entita", "staticRight": "Statické právo", "dynamicRight": "Dynamické právo", "treeNodesAll": "<PERSON><PERSON><PERSON>", "treeNodesMy": "<PERSON><PERSON>", "activeQueries": "Aktivní dotazy", "query": "Dotaz", "confirmCancelQuery": "Opravdu chcete zrušit dotaz?", "alrQueryNotFound": "Dotaz již nebyl nalezen.", "completeAgenda": "Kompletní agenda", "lockedBusinessUsers": "Zamčení byznys <PERSON>", "structuredList": "Strukturovaný seznam", "ttCompetences": {"heading": "Správa kompetencí", "body": ""}, "competences": "Kompetence", "competence": "Kompetence", "competenceDelVar": "kompetenci", "addCompetence": "Přidat kompetenci", "regularExpression": "Regulární výraz", "generationStatus": "Status generování", "source": "<PERSON><PERSON><PERSON><PERSON>", "historical": "Historický", "external": "Externí", "nextDay": "následující den", "embeddedVideoNotSupported": "<PERSON> ná<PERSON> lí<PERSON>, ale v<PERSON><PERSON> nepodporuje videa.", "alrSendingTestMailFailed": "Odeslání testovacího e-mailu sel<PERSON>o.", "sent": "Odesláno.", "mainColorEmail": "Hlavní barva e-mailu", "confirmResetColors": "Opravdu chcete resetovat barvy?", "regularExpressions": "Regulární výrazy", "confirmDeleteLogo": "Opravdu chcete smazat logo?", "loginLogoLightTheme": "Logo přihlašovací obrazovky (světlý režim)", "loginLogoDarkTheme": "Logo přihlašovací obrazovky (tmavý režim)", "competenceRegexHelper": "<ul><li><PERSON>ako N libovoných znaků lze použít <b>%</b> (obdoba *)</li><li>Jako jeden libovolný znak lze použít <b>_</b> (obdoba .)</li><li>Pro escapování těchto speciálních znaků lze použít <b>^</b> (obdoba \\)</li></ul>", "headerFont": "<PERSON><PERSON><PERSON>", "evenRow": "<PERSON><PERSON><PERSON>", "logo": "Logo", "widthForLogo": "Šířka pro logo", "monthStart": "Začátek měsíce", "monthEnd": "<PERSON><PERSON><PERSON> m<PERSON>", "ttFavouriteType": "GET otevře odkaz. POST odešle příkaz: např<PERSON>lad při zakládání případu, kdy se v těle požadavku posílá ID hlavičky šablony (uložit do oblíbených lze přes Nový případ).", "confirmEmptyMultiinstanceVariable": "J<PERSON> si jisti, že tato multiinstance nevyžaduje proměnnou pro iteraci?", "ttMenuPreview": "Konfigurace menu podle uživ. rolí (významn<PERSON><PERSON><PERSON><PERSON> role vidí také tla<PERSON>ka pro méně významné role). Tlačítka Nový případ a Dashboard jsou neměnná.", "menuPreview": "Náhled menu pro vybranou roli", "confirmResetMenu": "Opravdu chcete resetovat menu?", "alrFailedTasMenu": "Konfiguraci menu <PERSON> se nepodařilo načíst!", "security": "Zabezpečení", "userRestrictions": "Omezení <PERSON> (zobrazit)", "userRestrictionsProcesses": "Ignorovat omezení uživatelů na úkolech", "roleRestrictions": "Omezení rol<PERSON> (zobrazit)", "orgUnitRestrictions": "Omezení org. jednotek (zobrazit)", "everyone": "<PERSON><PERSON><PERSON><PERSON>", "colleaguesOnly": "<PERSON>", "directSubordinates": "Přímí podřízení", "allSubordinates": "Všichni podřízení", "none": "<PERSON><PERSON><PERSON><PERSON>", "generalDocument": "Obecný dokument", "competenceRule": "<PERSON><PERSON><PERSON><PERSON> kompetence", "competenceRules": "Pravidla kompetencí", "ruleName": "Název pravidla", "ttUseCompetenceRule": {"heading": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "body": "Založí kompetenci podle vybraného pravidla"}, "competenceText": "Text kompetence", "competenceName": "Název kompetence", "competenceReadOnlyInfo": "Kompetenci vytvořenou z pravidla nelze upravit", "xmlProcessImport": "Import procesů XML", "ttWidthForLogo": "Nastavte šířku pro logo a pak vložte logo. Měnit šířku pro již vložené nebo defaultní logo není možné.", "openCase": "Otevřít p<PERSON>", "importHistory": "Historie importů", "plannedImports": "Plánované importy", "filePath": "Cesta k souboru", "cronId": "ID cronu", "taskResult": "<PERSON><PERSON><PERSON><PERSON>", "xmlFileSize": "Velikost XML souboru", "attachmentSize": "Velikost přílohy", "lastEdit": "Poslední úprava", "timeCreated": "Čas vytvoření", "importId": "ID importu", "importAudit": "Audit importu", "finishedImports": "Hotové importy", "insertNote": "Vložit poznámku", "importXml": "Importovat XML", "reImportXml": "Reimportovat XML", "downloadXml": "Stáhnout XML", "downloadAttachment": "Stáhnout přílohu", "skipXml": "Přeskočit XML", "note": "Poznámka", "attachmentName": "Název přílohy", "importedCount": "Počet importů", "retryCount": "Počet opakování", "batchId": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "copyPath": "Zkopírovat cestu", "trace_id": "Trace ID", "cronRunId": "ID běhu", "cronRun": "<PERSON><PERSON><PERSON> cronu", "ttMenuItemLabel": "Univerzá<PERSON><PERSON> n<PERSON>, pokud nen<PERSON> překlad. Je<PERSON><PERSON>i pou<PERSON><PERSON> klíčové slovo z překladů, překlá<PERSON><PERSON> se <PERSON>. Výchozí názvy: tasks, cases, overviews, reports, templates, plans, users, roles, orgStructure, events, documents, elArchiv, Manuals", "taskQueue": "Fronta úkolů", "dissolveQueue": "Rozpustit frontu", "taskQueueInitInfo": "<PERSON>uto akcí vzniklo naráz více úkolů ke splnění. Zde můžete změnit pořadí jejich plnění, či je z fronty zcela odebrat.", "tryDarkTheme": "Vš<PERSON><PERSON> jsme si, že preferujete temný režim. Klikněte pro jeho vyzkoušení v TASu.", "alrInvalidURL": "URL není ve správném formátu", "alrInvalidHttps": "URL není ve správném form<PERSON>, musí za<PERSON>at https://", "importVariables": "Import prom<PERSON><PERSON><PERSON><PERSON>", "ttVariablesImport": {"heading": "Import prom<PERSON><PERSON><PERSON><PERSON>", "body": "Po výběru souboru s definicí proměnných je <PERSON>eno jej<PERSON>."}, "classDiagram": "Diagram t<PERSON>í<PERSON>", "createVar": "Založit proměnnou", "importObjectStates": "Import stav<PERSON> objektů", "unassigned": "Nepřiřazené", "sortVars": "<PERSON><PERSON><PERSON><PERSON>", "fillNames": "Vyplnit názvy", "ttFillNames": {"heading": "Vyplnit názvy", "body": "Vyplní prázdné názvy všech nových proměnných podle formátu „Třída.Atribut“ a seřadí proměnné."}, "ttSortVars": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Seřadí proměnné podle tříd a atributů."}, "ttRestore": {"heading": "Obnovit", "body": "Obnoví proměnné do původního stavu při importu ze souboru."}, "ttAddVarToBottom": {"heading": "<PERSON><PERSON><PERSON><PERSON>", "body": "Přidá proměnnou na konec stránky."}, "confirmRestoreForm": "Opravdu si přejete obnovit proměnné do původního stavu?", "selectClass": "<PERSON><PERSON><PERSON><PERSON>", "importClassDiagram": "Import diagramu tříd", "continue": "Po<PERSON><PERSON><PERSON><PERSON>", "templateVars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newVars": "<PERSON>é proměnné", "objectState": "Stav objektu", "alrDynTableExists": "Dynamická tabulka již existuje!", "overwriteExistDynTable": "Přepsat existující dyn. tabulku", "confirmCancelImport": "Opravdu si přejete zrušit import?", "alrDuplicateNames": "Data obsahují duplicitní n<PERSON>.", "stateVar": "<PERSON><PERSON><PERSON> proměnná", "importObjectStatesToDynTables": "Importujte stavy objektů do dynamických tabulek.", "defineObjectStatesVars": "<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> d<PERSON><PERSON> stav objektů.", "change": "Změnit", "classAndAttr": "Třída a atribut", "clearQueue": "Vymazat frontu", "sharing": "Sdílení", "data": "Data", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataSource": "<PERSON><PERSON><PERSON><PERSON> dat", "dataPoints": "Datové body", "dataSeries": "Datová řada", "valueCol": "<PERSON><PERSON><PERSON> hodnot", "aggregationCol": "Sloupec agregace", "timeDimension": "Čas. dimenze", "columns": "<PERSON><PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON><PERSON>", "weekday": "den v týdnu", "monthVar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overviewFilter": "Filtr přehledu", "globalFilters": "Globální filtry", "filterDefinition": "Definice filtru", "newFilter": "Nový filtr", "addFilter": "<PERSON><PERSON><PERSON><PERSON> filtr", "filterOptions": "Možnosti filtru", "addOption": "Přidat možnost", "graphPreview": "<PERSON><PERSON><PERSON><PERSON> grafu", "alrGlobalFilterDownloadFailed": "Globální filtry se nepodařilo stáhnout!", "alrGlobalFilterSaveFailed": "Globální filtry se nepodařilo ulož<PERSON>!", "filterOption": "Možnost filtru", "editFilter": "Upravit filtr", "fillOptionsFromVar": "Vyplnit možnosti z proměnné", "fillOptionsDynamically": "Vyplňovat možnosti dynamicky", "filterOptionsFilledDynamically": "Dynamicky z proměnné", "dayOfMonth": "den v měsíci", "dateVar": "datum", "group": "<PERSON><PERSON><PERSON>", "ttDataSource": "Pokud chcete zadat každý separátní bod grafu zvl<PERSON>, zvolte možnost „Datové body“. Pokud chcete nechat body vygenerovat na základě zvolené dimenze, zvolte „Datová řada“", "ttDataSeriesAggregation": "Zvolte typ agregace. Umožňuje ze záznamů (případů) vytvořit souhrnné informace.", "ttDataSeriesColumns": "Zvolte postupně všechny sloupce, podle kterých se vyt<PERSON>ří skupiny (agregace) pro vypočtení souhrnných hodnot.", "listOfFiltersIsEmpty": "Seznam filtrů je pr<PERSON>ý.", "fromVariable": "Z proměnné", "showOptionsFromCount": "Zobrazit možnosti (z {{count}})", "sum": "Součet", "minimum": "Minimum", "maximum": "Maximum", "statistics": "Statistiky", "unfilled": "Nevyplněno", "globalFilterDescription": "Globální filtr poskytuje uživatelům grafu mo<PERSON>, které filtrují vstupní data pro graf. V této obrazovce lze všechny možnosti filtru nadefinovat.", "ttDelGraph": {"heading": "<PERSON><PERSON><PERSON><PERSON> graf", "body": "Smaže označený graf."}, "ttEditGraph": {"heading": "<PERSON><PERSON><PERSON><PERSON> graf", "body": "Umožní upravit označený graf."}, "ttCopyGraph": {"heading": "Kopírovat graf", "body": "Zkopíruje oz<PERSON>ený graf."}, "ttAddGraph": {"heading": "<PERSON><PERSON><PERSON><PERSON> graf", "body": "Umožní nadefinovat nový graf."}, "axisXName": "Název osy X", "axisYName": "Název osy Y", "showValues": "Zobrazovat hodnoty", "defaultOption": "Výchozí možnost", "yearStart": "Začátek roku", "yearEnd": "Konec roku", "thisMonth": "<PERSON><PERSON>", "lastMonth": "<PERSON><PERSON><PERSON>", "thisYear": "<PERSON>to rok", "lastYear": "Minulý rok", "scheduledTasks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scheduled": "Na<PERSON><PERSON><PERSON>ované", "dueDateStart": "<PERSON><PERSON>ín s<PERSON>štění", "lastRescheduled": "Naposledy přeplán<PERSON>", "reschedule": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "alrTasksRescheduling": "Přeplánováv<PERSON><PERSON>...", "alrTasksRescheduled": "<PERSON><PERSON><PERSON> by<PERSON>.", "alrTasksRescheduleFailed": "Úkoly se nepodařilo p<PERSON>t.", "onlyCurrentOrFutureDates": "<PERSON><PERSON> dnešní nebo budo<PERSON><PERSON> datum", "passwordValidations": "Heslová politika", "readonlyConfigInfo": "Hodnota je pouze ke čtení", "alrTasksCountFailed": "Počet úkolů se nepodařilo zjistit.", "confirmActivateTasks": "Opravdu chcete aktivovat vybrané úkoly?", "confirmSuspendTasks": "Opravdu chcete uspat v<PERSON>brané <PERSON>?", "tskOffset": "Proměnná plánování", "workWeek": "Pracovní t<PERSON>den", "agenda": "Agenda", "noEventsInRange": "V tomto roz<PERSON> nej<PERSON>u žádn<PERSON> ud<PERSON>", "activitiesDesc": "Popis aktivit", "allShort": "<PERSON><PERSON><PERSON>", "numberOfEvents": "Počet údálostí", "weekNumber": "<PERSON><PERSON><PERSON>", "cannotBeEdited": "<PERSON><PERSON><PERSON><PERSON>t", "cannotBeMoved": "<PERSON><PERSON>ze př<PERSON>unout", "alrTempVarSaveSameNameFailed": "Proměnná s tímto výchozím názvem již existuje, prosím zadejte jiný název.", "maxUsersCountRole": "Maximální počet uživatelů v roli", "unlimitedAssignLeaveBlankInfo": "Pro neomezené přiřazení nechte pole prázdné.", "cvOwner": "Vlastník přehledu", "changePassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "passwordExpired": "Platnost vašeho hesla vypršela. Zadejte nové heslo.", "passwordWillExpire": "Vaše heslo brzy vyprší. Zadejte prosím nové heslo.", "userParameters": "Parametry uživatele", "importUsers": "Import uživatelů", "importRoles": "Import rolí", "existingEntityRows": "Řádky s již existujícími entitami (lze přepsat)", "fileRow": "Řádek souboru", "existingEntityRowsMultiple": "Řádky s entitami, k<PERSON><PERSON> již <PERSON>ují více než jedno<PERSON> (nebudou naimportovány)", "importOrgUnits": "Import org. jednotek", "structureImportExport": "Import/export struktury", "fillAttributes": "Vyplnit atributy", "structurePreview": "<PERSON><PERSON><PERSON><PERSON> struk<PERSON>y", "invalidRowsForImport": "<PERSON><PERSON><PERSON><PERSON><PERSON> (chybějící povinn<PERSON>)", "duplicateRowsForImport": "Řádky s duplicit<PERSON><PERSON><PERSON> (nebudou naimportovány)", "newEntityRows": "Řádky s novými entitami k <PERSON>ování", "existingNameRowsForImport": "Řádky s <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> již <PERSON>ují u jiných entit (nebudou naimportovány)", "overwriteExisting": "Přepsat existující", "structurePreviewHelper": "Náhled struktury ukazuje dvě různé situace: import jen nových organizací, nebo import nových i již existujících organizací, které budou přepsány. Veškeré změny oproti stávající struktuře jsou označeny červeně.", "validateAndShowPreview": "Validovat a zobrazit náhled", "uploadNewFile": "<PERSON>rá<PERSON> nov<PERSON> soubor", "userStatus": "<PERSON><PERSON>", "importedFile": "Importova<PERSON><PERSON> so<PERSON>", "pairUsersBy": "Párovat uživatele dle", "assignOrgBy": "Zařadit do organizace dle", "pairRolesBy": "<PERSON><PERSON><PERSON><PERSON> role dle", "pairUnitsBy": "Párovat jednotky dle", "unitHierarchyCol": "Sloupec hierarchie jed<PERSON>k", "dontAssign": "Nepřiřazovat", "infoImportDataValidated": "POZOR: Data byla právě zvalidována kvůli změnám v nastavení. Doporučujeme vrátit se zpět a zkontrolovat nový náhled importu.", "assignUserRolesMethod": "Způsob přiřazení rolí už<PERSON>ů<PERSON>", "assignUserRolesMethodHelp": "Způsob přiřazení rolí: přidat k již přiřazeným rolím, nebo nově přiřazenými rolemi zcela nahradit aktuálně přiřazené role.", "existingRolesForImport": "<PERSON><PERSON><PERSON> role (lze přepsat)", "existingRoleNamesForImport": "<PERSON> s <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> již <PERSON>ují u jiných rolí (nebudou naimportovány)", "newRolesForImport": "Nové role k importování", "userRolesForImport": "Řádky s uživatelskými rolemi k přiřazení", "nonExistentUsersForImport": "Řádky s neexist<PERSON><PERSON><PERSON><PERSON><PERSON> (role nebudou př<PERSON>ř<PERSON>y)", "multipleExistingUsersForImport": "Řádky s více než jedním existujícím uživatelem (role nebudou přiřazeny)", "invalidOrgsForImport": "Nevalid<PERSON><PERSON> (chybějící povinné údaje nebo špatná hierarchie)", "keepOriginal": "<PERSON><PERSON>t původní", "assignOrgByHelp": "V případě výběru sloupce ze souboru můžete upřesnit zařazení do organizace pro nové a pro již existující uživatele. V případě vyběru konkrétní organizace budou všichni naimportovaní nebo aktualizovaní uživatelé zařazeni do této organizace.", "creatingRoles": "Vytvoření rolí", "assigningRolesToUsers": "Přiřazení rolí <PERSON>", "newUsers": "Nov<PERSON> už<PERSON>lé", "existingUsers": "Existující <PERSON>", "fromFile": "<PERSON><PERSON> souboru", "alrCsvXlsxUploadWrongExtension": "Nahrávejte jenom soubory s koncovkou *.csv nebo *.xlsx", "importNewAndExisting": "Importovat nové entity a přepsat již existující", "importNewOnly": "Importovat pouze nové entity", "importNewAndExistingRoles": "Importovat nové role a přepsat již existující", "importNewRolesOnly": "I<PERSON>rt<PERSON>t pouze nové role", "filterSortingHelper": "Řazení podle jednoho či více sloupců ve filtru deaktivuje možnost řadit sloupce ručně přímo v tabulce.", "statisticsColorHelper": "Nejsou-li vy<PERSON><PERSON><PERSON><PERSON> barvy man<PERSON>, nebo je-li v<PERSON><PERSON><PERSON><PERSON><PERSON> barev méně ne<PERSON>, jsou chybějí<PERSON>í barvy generovány automaticky. Vygenerované barvy nikdy neobsahují tmavé nebo př<PERSON> svě<PERSON> o<PERSON>, ty lze vybrat jen manu<PERSON>ln<PERSON>.", "importRolesHelper": "Nastavení importu samotných rolí. Přiřazování rolí uživatelům se řídí tím, co je nastaveno v „Párovat uživatele dle“, a vž<PERSON> se týká nových i již existujících rolí.", "caseService": "<PERSON><PERSON>", "taskService": "<PERSON><PERSON>", "editTasks": "<PERSON><PERSON><PERSON><PERSON>", "editCases": "Upravit případy", "deleteTasks": "<PERSON><PERSON><PERSON><PERSON>", "deleteCases": "Smazat případy", "serviceOperationsInfo": "Označte a v<PERSON>plňte ty proměnné, kter<PERSON> chcete změnit.", "erased": "<PERSON><PERSON><PERSON><PERSON>", "statusErrored": "Chybový", "serviceOperations": "Servisní operace", "runCalcsOnStart": "Spustit výpočty při spuštění", "taskReactivation": "Reaktivovat úkoly", "taskCompletion": "Splnit úkoly", "caseReactivation": "Reaktivovat případy", "caseCompletion": "Splnit případy", "openTask": "<PERSON>tev<PERSON><PERSON><PERSON>", "changeEntity": "Změna entity", "selectTableColumns": "<PERSON><PERSON><PERSON><PERSON> slou<PERSON> ta<PERSON>ky", "parentCase": "Nadřazený případ", "ownerOrganization": "Organizace vlastníka", "confirmTaskReactivation": "Opravdu chcete reaktivovat vybrané úkoly?", "confirmCaseReactivation": "Opravdu chcete reaktivovat vybrané případy?", "confirmTaskCompletion": "Opravdu chcete splnit vybrané ú<PERSON>ly?", "confirmCaseCompletion": "Opravdu chcete splnit vybrané případy?", "selectAllFilterMustBeActive": "Pro výběr všech položek musí být aktivní alespoň jeden filtr.", "changeEntities": "Změna entit", "disabledDifferentTemplates": "Nelze změnit, protože entity nepochází ze stejné šablony.", "actions": "Ak<PERSON>", "taskTemplateId": "ID šablony úkolu", "caseTemplateId": "ID šablony případu", "actionInfoCheckLogs": "<PERSON><PERSON><PERSON> se <PERSON>e na pozadí, prosím zkontrolujte logy.", "alrServiceOperationsColumnsFailed": "Uložení nastavení sloupců servisních operací se nezdařilo.", "confirmResetSelectedCols": "Opravdu chcete resetovat uložené sloupce tabulky?", "instanceVars": "Instanč<PERSON><PERSON> proměnn<PERSON>", "usrId": "ID uživatele", "orgId": "ID organizace", "titlePrefix": "Prefix názvu", "titleSuffix": "Suffix názvu", "accessRoleId": "<PERSON> přístupové role", "maxAssigns": "Maximum přiřazení", "client": "Klient", "bigValue": "Velká hodnota", "unitId": "<PERSON> jed<PERSON>ky", "roleId": "ID role", "paramId": "ID parametru", "varId": "ID proměnné", "parentId": "ID nadřazené j<PERSON>", "openUser": "<PERSON>tev<PERSON><PERSON><PERSON>", "openRole": "Otevřít roli", "openUnit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "units": "<PERSON><PERSON><PERSON>", "managerId": "ID manažera", "externalStatus": "Externí stav", "additionalId": "Další ID", "parentIc": "IC nadřazené jednotky", "companyIc": "IC společnosti", "textValue": "<PERSON><PERSON> hodnota", "dateValue": "<PERSON><PERSON><PERSON> ho<PERSON>", "numberValue": "Číselná hodnota", "loginCount": "Počet přihlášení", "externalLogin": "Externí <PERSON>š<PERSON>í", "badLoginCount": "Počet špatných přihlášení", "passwordLastChange": "Poslední změna hesla", "solverEvaluation": "Vyhodnocení řešitele", "solverWillBe": "Řešitelem bude", "possibleSolvers": "Možní <PERSON>", "selectReferencePerson": "<PERSON><PERSON><PERSON><PERSON> refer<PERSON>č<PERSON>í <PERSON>", "evaluateSolver": "Vyhodnotit řešitele", "referenceUserForEval": "Referenční osoba pro vyhodnocení", "andOthers": "...a další", "showLess": "...zobrazit méně", "alrSessionExpired": "<PERSON><PERSON><PERSON><PERSON> <PERSON>lace v<PERSON><PERSON>, př<PERSON>laste se prosím znovu.", "mailPromptlyInfo": "Uživatel průběžně odebírá jednorázové upozornění na nový úkol ke splnění, jehož je řešitelem. Upozornění odejde pouze v případě, že úkol nebyl splněn {{minutes}} minut od jeho aktivace.", "mailPullInfo": "Uživatel průběžně odebírá jednorázové upozornění na nový úkol, který je k odběru a je jeho možným řešitelem. Upozornění odchází v okamžiku aktivace daného úkolu v rámci Workflow.", "mailTotalInfo": "Uživatel pravidelně odebír<PERSON> přehled s úkoly ke splnění, jej<PERSON><PERSON> je řešitelem. V př<PERSON><PERSON>, že úkol přímého řešitele nemá, je upozorněn vlastník procesu. Pokud je uživatel zastup<PERSON>n, upozornění odebírá jeho zastupitel.", "mailEscalationInfo": "Uživatel pravidelně odebír<PERSON> přehled s úkoly ke splnění po termínu. Upozornění mu chodí v případě, že je supervizorem úkolu (a není zároveň jeho řešitelem) a nebo je přímým manažerem uživatele, který je řešitelem. Pokud úkol nemá řešitele, je za supervizora považován vlastník procesu. Pokud je uživatel zastupován, v notifikaci je zminěno, kdo je aktuální zastupující.", "calcSourceOverwriteWarning": "Po uložení se zdroj přepíše ES6 syntaxí!", "changeStatus": "Změnit status", "confirmChangeEmailStatus": "Opravdu chcete změnit status vybraných e-mailů?", "logInAgain": "Znovu přihlásit", "migrations": "<PERSON><PERSON><PERSON>", "launchDate": "<PERSON>tum s<PERSON>", "stepName": "Název kroku", "runId": "ID běhu", "clone": "Klon", "confirmDeleteCron": "Opravdu chcete smazat vybraný cron?", "alrCronDeleted": "<PERSON><PERSON> byl s<PERSON>z<PERSON>!", "wantToContinueQ": "Chcete pokračovat?", "valueCannotBeEntered": "Hodnotu není mož<PERSON> zadat", "processingQueues": "<PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fillOptionsFromVarHelper": "Možnosti filtru lze vyplnit jen z proměnných typu DT, DL, LT, LD, LN, a D, které nemají povolen výběr více možností.", "defaultTemplateName": "Výchozí název šablony", "defaultTaskName": "Výchozí název úkolu", "defaultVariableName": "Výchozí název proměnné", "variableName": "<PERSON><PERSON><PERSON><PERSON> proměnné", "alrNoDataFound": "Nebyla nalezena žádná data", "ttProcessingQueuesInfo": "Fronty zpracování jsou vypnuté.\nPro zapnutí nastavte alespoň jednu z konfigurací „scaling.queue.*.enabled“ na true.", "businessUsers": "<PERSON><PERSON><PERSON><PERSON>", "completeHrAgenda": "Kompletní HR agenda", "usageStatsByHeader": "Statistiky použití podle hlavičky", "usageStatsByOrgUnit": "Statistiky použití podle org. jednotky", "usageStatsByUser": "Statistiky použití podle uživatele", "completedTasksNum": "Počet splněných úkolů", "startedProcessesNum": "Počet spuštěných případů", "ideHelp": "Pro zobrazení našeptávače stiskněte v editoru Ctrl + mezerník, opětovným stisknutím se zobrazí podrobněj<PERSON><PERSON> nápověda. Stisknutím F1 zobrazíte seznam všech dostupných příkazů a klávesových zkratek. Více viz <a href='https://code.visualstudio.com/docs/editor/codebasics' target='_blank'>dokumentace editoru</a>.", "restHelp": "Zadejte URL pro některou ze služeb pro tabulky TASu (např. '/tasks/mine') a po načtení služby vyberte sloupce tabulky, které chcete v kontejneru zobrazit.", "defaultGraphName": "Výchozí název grafu", "graphName": "Název grafu", "ttStatistics": {"heading": "Statistiky", "body": ""}, "defaultAxisXName": "Výchozí název osy X", "defaultAxisYName": "Výchozí název osy Y", "defaultFilterName": "Výchozí název filtru", "filterName": "Název filtru", "defaultOptionName": "Výchozí název možnosti", "optionName": "Název možnosti", "defaultOverviewName": "Výchozí název přehledu", "overviewName": "Název přehledu", "eventName": "<PERSON><PERSON><PERSON><PERSON>", "wantToOverrideEs6": "Pokud opravdu chcete přepsat, napište <b>ES6</b>", "processArchivation": "Archivace procesu", "processUnarchivation": "Dearchivace procesu", "resendEmail": "Přeposlat e-mail", "alrFailedSendEmail": "E-mail se nepoda<PERSON><PERSON> o<PERSON>", "ttResendEmail": {"heading": "Přeposlat e-mail", "body": "Znovu odešle dříve odeslanou e-mailovou notifikaci. Je možné změnit či přidat příjemce."}, "folders": "S<PERSON>žky", "newFolderBtn": "Nová složka", "documentInfo": "Informace o dokumentu", "userInfo": "Informace o uživateli", "deleteImage": "Odstranit snímek", "profilePhoto": "Profilová fotografie", "profilePhotoCaption": "Použijte fotografii ve formátu jpeg, jpg, png nebo gif.", "updatePhoto": "Aktualizovat fotku", "mailNotifications": "<PERSON><PERSON><PERSON>orně<PERSON>", "userPreferences": "Preference uživatele", "userSettings": "Nastavení už<PERSON>le", "allVices": "Všechny zástupy", "createVice": "Vytvořit zástup", "editVice": "Upravit zástup", "viceTip": "Zástup vám umožňuje předat vaši agendu kolegovi", "emptyDataMessage": "<PERSON><PERSON> tu není", "addFirstNote": "Přidejte první poznámku", "noResultsFor": "Ž<PERSON><PERSON><PERSON> výsledky pro:", "noCurrentTasks": "Žádné aktuální úkoly", "checkYourSearch": "Zkontrolujte vaše vyhledávání a zkuste to znovu.", "noFavOverviews": "Žádné oblíbené p<PERSON>", "favOverviewsTip": "Přidejte přehled do oblíbených pomocí hvězdičky", "noHiddenOverviews": "Nem<PERSON>te <PERSON>dn<PERSON> s<PERSON> přehledy", "addOverview": "<PERSON><PERSON><PERSON><PERSON>", "hidden": "Sk<PERSON><PERSON>", "removeConfirm": "Odstranit", "removeItem": "Opravdu chcete odstranit {{variable}}?", "changePicture": "Změnit obrázek", "saveFilter": "Uložit filtr", "addAnotherVice": "Přidat další z<PERSON>p", "saveVice": "Uložit zástup", "firstLastName": "Jméno a příjmení", "taskInfo": "Informace o úkolu", "emptyFavsTip": "Přidejte si oblíbené pomocí tlačítka", "saveAndClose": "Uložit a zavřít", "usersCanEditOverview": "<PERSON><PERSON><PERSON><PERSON> mohou edit<PERSON>t p<PERSON>", "assignedUsers": "Přiřazení uživatelé", "assignedOrgUnits": "Přiřazené organizační j<PERSON>", "assignedRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> role", "otherLangVariants": "<PERSON>š<PERSON> j<PERSON>", "moveToSharing": "Přejít na sdílení", "insertDocumentsPerm": "Uživatel má oprávnění vkládat dokumenty a poznámky", "saveNewPassword": "Uložit nové he<PERSON>lo", "confirmSubscription": "Potvrdit odběr", "subscriptionCaption": "Vybraný přehled Vám přijde e-mailem v nastavený čas.", "by": "Podle", "frequency": "Frekvence", "termination": "Ukončení", "ofTheMonth": "V měsíci", "endOnDate": "Ukončit dne", "endAfter": "Ukončit po", "onlyOnWorkingDays": "Pouze v pracovní dny", "occurrences": "výskytech", "dayOfWeekBy": "Dne v týdnu", "calendarDayBy": "Kalendářního dne", "dateBy": "data", "byDate": "Podle data", "byOccurrenceCount": "Podle počtu výskytů", "infinitely": "Neomezeně", "dayOfMonthAdornment": ". den v měsíci", "ordinalAdornment": ".", "toDateBeforeFromError": "Datum '<PERSON>' nem<PERSON><PERSON><PERSON> být před datem 'Od'", "vice": "Zástup", "previewShown": "Zobrazen n<PERSON>hled", "duplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hideBtn": "Skr<PERSON><PERSON>", "userView": "<PERSON><PERSON><PERSON>", "adminView": "<PERSON><PERSON><PERSON> admin", "or": "Nebo", "overlappingVicesError": "Zástupy se nesmí překrývat", "fileVar": "soubor", "nodeVar": "uzel", "uploadDifferentFile": "<PERSON><PERSON><PERSON><PERSON> jin<PERSON> so<PERSON>", "uploadedFile": "<PERSON><PERSON><PERSON>", "refreshPage2": "Aktualizovat stránku", "refreshPageCaption": "Pro pokračování prosím aktualizujte stránku v prohlížeči.", "ttCopy": {"heading": "Kopírovat", "body": "Umožní zkopírovat vybranou položku s možností editace některých parametrů."}, "alrError_INVALID_CSV_MAPPING": "Nenalezen csv sloupec '%s' v mapování události. Kontaktujte administrátora aplikace.", "documentPreview": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>", "moveUp": "<PERSON><PERSON><PERSON>", "moveDown": "<PERSON><PERSON><PERSON>", "moveToFilter": "Přejít na filtr", "moveToSorting": "Přejít na řazení", "addSorting": "Přidat ř<PERSON>ení", "cancelFilters": "Zrušit filtry", "docUploadedImmediately": "Doku<PERSON> bude okamžitě nahrán", "moreOptions": "Více možností", "docSearchPlaceholder": "Např. faktura.pdf…", "tasksSearchPlaceholder": "Např<PERSON> Zadat novou fakturu…", "docUploadedImmediatelyPrivate": "Doku<PERSON> bude okamžitě nahrán jako <PERSON>", "takeTsk": "Převz<PERSON><PERSON>", "tasksActive": "Aktivní <PERSON>", "subprocesses": "Podprocesy", "cancelAuthorization": "Zrušit autorizaci", "cancelAuthorizationConfirm": "Opravdu chcete zrušit autorizaci zařízení?", "linkMobileApp": "Propojení mobilní aplikace", "mobileApp": "Mobilní aplikace", "scanThisQr": "Naskenujte tento QR kód mobilním zařízením.", "scanningQr": "Načítám QR kód. Čekejte prosím.", "deviceName": "Název zařízení", "newDeviceName": "Nový název zařízení", "registrationDate": "Datum registrace", "lastLogin": "Poslední přihlášení", "mobileNotifications": "Notifikace v mobilu", "disableMobileNotification": "Vypnutí notifikáce v mobile", "newQrCode": "Nový QR kód", "inactiveScanQr": "Neaktivní - naskenujte QR kód.", "enableNotifications": "Povolit notifikace", "tip": "Tip: {{message}}", "alrFavContainerAlreadyExists": "Kontejner oblíbených položek již existuje.", "addGraph": "<PERSON><PERSON><PERSON><PERSON> graf", "newRow": "<PERSON><PERSON>", "confirmSetDefaultDashboard": "Opravdu chcete nastavit aktuální dashboard jako výchozí pro všechny uživatele?", "changeMayAffectAllUsers": "Tato změna může ovlivnit všechny uživatele.", "noOverviewsTip": "Vytvořte nový přehled pomocí tlačítka „Přidat přehled“", "removeFromHidden": "Odebrat ze skrytých", "last7Days": "Posledních 7 dnů", "last14Days": "Posledních 14 dní", "last30Days": "Posledních 30 dní", "lastCalendarMonth": "Poslední kalendář<PERSON><PERSON> m<PERSON>", "lastQuarter": "Poslední kvartál", "last12Months": "Posledních 12 měsíců", "lastCalendarYear": "Poslední kalendářní rok", "noFilterSet": "Není nastaven ž<PERSON>dný filtr", "noSortingSet": "Není nastavené žádné řazení", "deleteGroup": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "newGroup": "<PERSON><PERSON> skupina", "operators": "Operátory", "withActiveTask": "S aktivním úkolem", "withoutActiveTask": "Bez aktivního <PERSON>", "withNoTerm": "<PERSON><PERSON>", "withTerm": "S termínem", "securityAndAuthentication": "Bezpečnost a autentizace", "dataIntegrationAndManagement": "Integrace a správa dat", "appManagementAndConfig": "Správa aplikace a konfigurace", "monitoringAndMaintenance": "Monitorování a údržba", "adminSearchPlaceholder": "Na<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "authenticationAdminDescription": "Možnosti přihlášení uživatele", "certificatesAdminDescription": "Certifikáty pro TAS", "elasticsearchAdminDescription": "Integrace s Elasticsearch", "xmlProcessImportAdminDescription": "Import procesů XML pomocí záznamu z cronu XMLProcessImport.js", "structureImportExportAdminDescription": "Import/export organizační struktury, uživatelů a rolí", "dmsAttributesAdminDescription": "Seznam atributů dokumentů v DMS", "dynTablesAdminDescription": "Úložiště dat v dynamických tabulkách", "csvAdminDescription": "Manipulace s CSV soubory v aplikaci", "configurationAdminDescription": "<PERSON>n<PERSON><PERSON><PERSON>", "settingsAdminDescription": "Nastavení identifikace společnosti a další administrativní úkony", "logsAdminDescription": "Správa a prohlížení logů aplikace", "migrationsAdminDescription": "Migrace dat a konfigurace aplikace", "guidesAdminDescription": "Nápověda a průvodci pro uživatele", "schemeAdminDescription": "<PERSON><PERSON><PERSON><PERSON>, logo a další prvky v aplikaci", "sequencesAdminDescription": "Správa sekvencí využívaných v šablonách", "serviceConsoleAdminDescription": "Aplikačně-administrativní příkazy prostřednictvím servisní konzole", "serviceOperationsAdminDescription": "Komplexní správa servisních operací", "scriptsAdminDescription": "Správa opakovaně využívaných skriptů napří<PERSON> různými šablonami", "appStatusAdminDescription": "Informace o aktuálním stavu aplikace", "usageStatsAdminDescription": "Zobrazení statistik využití aplikace", "maintenanceAdminDescription": "Nastavení údržby a provádění údržbov<PERSON>ch úkonů", "scheduledTasksAdminDescription": "Správa všech plánovaných úkolů", "publicFilesAdminDescription": "Správa veřejných souborů a dokumentace", "cronsAdminDescription": "Automatizace pravidelných úkolů", "hrAgendaAdminDescription": "Řízení agendy uživatelů v rámci HR", "emailsQueueAdminDescription": "Správa fronty emailů a veškerá e-mailová komunikace z TAS", "processingQueuesAdminDescription": "", "alrAddFavItemFailed": "Přidání položky do oblíbených se nezdařilo", "alrRemoveFavItemFailed": "Odebrání položky z oblíbených se nezdařilo", "alrAddHiddenItemFailed": "Přidání položky do skrytých se nezdařilo", "alrRemoveHiddenItemFailed": "Odebrání položky ze skrytých se nezdařilo", "display": "Zobrazení", "compact": "Kompaktní", "standard": "Standardní", "comfortable": "Komfortní", "exportTo": "Export do", "adminMenuTip": "Přidejte si položky v administraci do oblíbených. Kliknutím na hvězdičku zobrazíte položku přímo tady.", "editorDocumentation": "Dokumentace editoru", "addSection": "<PERSON><PERSON><PERSON><PERSON>", "insertSection": "Vložit se<PERSON>", "section": "Sek<PERSON>", "sections": "Sek<PERSON>", "toTop": "Na začátek", "toEnd": "Na konec", "alrSectionNotBeEmpty": "<PERSON><PERSON><PERSON> ne<PERSON> být prázdná", "confirmDeleteSection": "Opravdu chcete smazat sekci?", "sectionVarsMoveAllTasks": "Proměnné ve všech úkolech budou přesunuty z odstraněné sekce k proměnným bez sekce.", "sectionVarsMove": "Promě<PERSON>é budou přesunuty z odstraněné sekce k proměnným bez sekce.", "actionCannotUndone": "<PERSON><PERSON> a<PERSON> nel<PERSON> vrátit zpět.", "overviewOfAllNews": "Přehled všech novinek", "copyOverview": "Kopírovat přehled", "create": "Vytvořit", "copyExistingOverview": "Kopírovat existující přehled", "selectOverview": "<PERSON><PERSON><PERSON><PERSON>", "chooseFromOverviews": "Vyberte z přehledů...", "selectTemplate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chooseFromAvailableTemplates": "Vyberte z dostupných šablon...", "loginWithUsernamePassword": "Přihlášení uživatelským jménem a heslem", "signInWithCorporateIdentity": "Přihlášení firemní identitou", "whatsNewInTAS": "Co je nového v TASu?", "whatsNewInTASDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nov<PERSON>, tipy, triky a v<PERSON>e, co potřebujete vědět.", "justOpen": "<PERSON><PERSON>", "editOverview": "<PERSON><PERSON><PERSON><PERSON>", "noGraphsTip": "Vytvořte nový graf pomocí tlačítka „Přidat graf“", "noDocumentsTip": "Přidejte dokument pomocí tlačítka „Přidat“", "noFilesTip": "Přidejte nový soubor pomocí tlačítka „Přidat“", "less": "Méně", "notContains": "<PERSON><PERSON><PERSON><PERSON>", "notcontains": "<PERSON><PERSON><PERSON><PERSON>", "notEquals": "Nerov<PERSON> se", "notequals": "nerovná se", "factorySettings": "Tovární nastavení", "previewCollapsedNavMenu": "<PERSON><PERSON><PERSON><PERSON> sbaleného navigačního menu", "previewExpandedNavMenu": "<PERSON><PERSON><PERSON>ed rozbaleného navigačního menu", "logoForCollapsedNavMenu": "Logo pro sbalené navigační menu", "logoForExpandedNavMenu": "Logo pro rozbalené navigační menu", "organisationLogo": "Logo organizace", "pickLogoOrganisation": "Výběr loga pro organizaci", "addLogo": "Přidat logo", "clickForAddLogoOrDrop": "Klikněte pro přidání nebo přetáhněte zde", "useLogoSizeMin": "Použij logo minimální velikosti", "logoForLightTheme": "Logo pro světlý režim", "logoForDarkTheme": "Logo pro tmavý režim", "sharedWithMe": "Sdílené se mnou", "myOverview": "<PERSON><PERSON><PERSON>", "getMobileAppText": "Mobilní aplikaci získáte v obchodě s aplikacemi", "noDocuments": "Žádné dokument<PERSON>", "noNotes": "<PERSON><PERSON><PERSON><PERSON>", "noFiles": "<PERSON><PERSON><PERSON><PERSON>", "addFirstDocument": "Přidejte první dokument", "chooseNewLogo": "Vyberte nové logo", "function": "Funkce", "groupFunction": "Funkce mezi skupinami", "mobileAppAuthFailed": "Ověření mobilní aplikace se nezdařilo.", "currentDocumentVersion": "Aktuální verze dokumentu", "csp": "<PERSON><PERSON><PERSON>í obsahu", "documentsDelete": "Smazán<PERSON>", "confirmDocumentsDelete": "Opravdu chcete smazat vybrané dokumenty?", "confirmDocumentsDownload": "Chcete stáhnout vybrané dokumenty?", "firstNum": "prvn<PERSON>ch {{num}}", "maximumNum": "max. {{num}}", "documentsDownload": "Stažení dokumentů", "caseLogs": "Logy případu", "archiveCases": "Archivovat případy", "archive": "Archivovat", "unarchive": "Dearchivovat", "confirmArchiveCases": "Opravdu chcete archivovat vybrané případy?", "archiveInDays": "Archivovat za (dny)", "archived": "Archivovaný", "archivedx": "Archivované", "alrArchivingCase": "Případ se archivuje...", "alrCaseArchived": "Případ byl archivován.", "alrLackOfPermsToArchiveCase": "Nemáte dostatečná oprávnění k archivaci případu.", "alrArchiveCaseFailed": "Archivace případu se nezdařila.", "alrUnarchivingCase": "Případ se dearchivuje...", "alrCaseUnarchived": "Pří<PERSON><PERSON>.", "alrLackOfPermsToUnarchiveCase": "Nemáte dostatečná oprávnění k dearchivaci případu.", "alrUnarchiveCaseFailed": "Dearchivace případu se nezdařila.", "byUser": "Podle uživatele", "byAgenda": "Podle agendy", "agendaHandover": "<PERSON><PERSON><PERSON><PERSON><PERSON> age<PERSON>", "activeUsers": "Aktivní uživatelé", "lockedUsers": "Zamčení uživatelé", "allUsers": "Všichni uživatelé", "inactiveUsers": "Neaktivní uživatelé", "hrAgendaSearchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> …", "completeAgendaHandover": "Předání kompletní agendy", "handoverCases": "Předat případy", "handoverTasks": "Předat <PERSON>", "handoverVars": "<PERSON><PERSON><PERSON><PERSON> proměnn<PERSON>", "changeTaskOwner": "Změna řešitele úkolu", "confirmHandover": "Potvrdit předání", "filterCasesByHeaderTip": "Všechny případy pod stejnou hlavičkou můžete vyfiltrovat ve sloupci Hlavička.", "userAgendaSelectedHandover": "Předání <b style=\"color: {{color}};\">vybrané</b> <PERSON><PERSON> u<PERSON>", "userAgendaCompleteHandover": "Předání <b style=\"color: {{color}};\">kompletní</b> age<PERSON> u<PERSON>le", "confirmAgendaHandover": "Opravdu chcete předat vybranou agendu ({{selected}}) uživateli <b>{{newUser}}</b>?", "confirmUserAgendaSelectedHandover": "Opravdu chcete předat <b>vybranou</b> agendu už<PERSON> <b>{{user}}</b> uživateli <b>{{newUser}}</b>?", "confirmUserAgendaCompleteHandover": "Opravdu chcete předat <b>kompletní</b> agendu u<PERSON> <b>{{user}}</b> uživateli <b>{{newUser}}</b>?", "refreshSessionTitle": "<PERSON><PERSON> T<PERSON>u bude za {{minutes}} minut ukon<PERSON>en<PERSON>.", "refreshSessionCaption": "Kliknutím na \"Pokračovat v práci\" budete pokračovat bez přerušení.", "continueWorking": "Pokračovat v práci", "sessionExpiredCaption": "Kliknutím na \"Znovu přihlásit\" se vrátíte na přihlašovací obrazovku.", "loginExpired": "Odhlásili jsme vás po delší době nečinnosti.", "confirmArchiveCase": "Opravdu chcete archivovat vybraný případ?", "isLowerOrEqualThan": "<PERSON><PERSON><PERSON> b<PERSON>t men<PERSON> nebo rovno", "confirmUnarchiveCase": "Opravdu chcete dearchivovat vybraný případ?", "addCaseRightNewUserTooltip": "Pokud tuto možnost nezaškrtnete, nový uživatel bude nahrazen v business proměnné, ale nebude mít přístup k případu.", "canBeViced": "Jsem zastupován", "canVice": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "<PERSON>va p<PERSON>adí", "defaultDashboardView": "<PERSON><PERSON><PERSON><PERSON> výchozího dashboardu", "colorScheme": "<PERSON><PERSON><PERSON><PERSON>", "displaySelectionAsTags": "Zobrazit výběr jako tagy", "displayAsPassword": "Zobrazit jako heslo", "sideBySide": "<PERSON><PERSON><PERSON> sebe", "copyAssignmentFromTask": "Zkopírovat přiřazení z úkolu", "toTask": "Do <PERSON>", "copyTaskAssignmentWarning": "Přiřazení v úkolu není prázdné, chcete ho přepsat?", "copyToOtherTasks": "Kopírovat do dalších úkolů", "noteScriptsNotApplied": "Poznámka: sk<PERSON><PERSON> nejsou aplikovány", "tasksMineAndToPull": "Mé + K odběru", "generateRecHistory": "Zobrazovat v aktivních úkolech a historii", "leaveFormerRoles": "Ponechat i původní role", "includeCompetences": "Zahrnout kompetence", "copyRoles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> role", "userIsActive": "Uživatel je aktivní", "systemUser": "Systémový uživatel", "copyRolesFromUser": "Zkopírovat role od už<PERSON>", "assignedRolesOverview": "<PERSON><PERSON><PERSON><PERSON> přiřazených rolí", "copyRolesInfo": "Pokud je daný už<PERSON>l součástí kompetencí, tyto kompetence nebudou zkopírovány okamžitě. Ke generování dojde:", "notificationOn": "Zapnut<PERSON>", "notificationOff": "Vypnuté", "onNotification": "Zapnout notifikace", "offNotification": "Vypnout notifikace", "page": "strana", "fromTo": "Od - Do", "fromto": "od - do", "isanyofvalue": "je libovolná hodnota z", "isAnyOfValue": "Je libovolná hodnota z", "alrNoteToggleVisibiltyFailed": "Skrytí/odkrytí poznámky se nezdařilo", "alrNoteHideOnEditFailed": "Skrytí původní poznámky se nezdařilo", "hiddenShe": "Skrytá", "showHiddenNotes": "Zobrazit skryté poznámky", "alrNoteEdited": "Upravená verze poznámky byla uložena", "notesEdit": "Upravit poznámku", "displayName": "Zobrazované <PERSON>", "clientDateFormat": "Formát data", "defaultByLanguage": "Výchozí podle jaz<PERSON>a", "restKeysOptionsNotUpToDate": "Zastaralý výběr hodnot - znovu načtěte službu.", "invalidValue": "Neplatná hodnota", "ended": "Ukon<PERSON><PERSON><PERSON>", "exportAllActive": "Exportovat všechny aktivní", "alrScriptsLoadFailed": "Načtení skriptů se nezdařilo.", "scriptsImport": "I<PERSON>rt skriptů", "doImport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "alrImportingScripts": "Probíhá import skriptů...", "alrScriptsImported": "Skripty byly import<PERSON><PERSON>.", "alrScriptsImportFailed": "Import skriptů se nezdařil.", "removeAll": "Odebrat vše", "alrNoScriptsToImport": "Žádné skripty k <PERSON>u.", "activateAll": "Aktivovat vše", "alrNoPermsToEditNoteInVice": "Nemáte právo na upravení poznámky v zástupu.", "alrNoPermsToToggleNoteVisibilityInVice": "Nemáte právo na skrytí/odkrytí poznámky v zástupu.", "plusMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variableAlignment": "Zarovnání proměnné", "variableAlignmentHelp": "Ovlivňuje zarovnání hodnoty proměnné v rámci formuláře úkolu.", "variableAlignmentLeft": "<PERSON><PERSON><PERSON>", "variableAlignmentRight": "Vpravo", "myDevice": "Moje z<PERSON>řízení", "deleteLogo": "Smazat logo", "namingFilter": "Pojmenování filtru", "exceptionsToRegularSchedule": "Výjimky z pravidelného plánu", "noExceptions": "Žádné výjimky", "specificDates": "Konkrétní data", "dateFromTo": "Datum od - do", "weekdayCap": "Den v týdnu", "specificDayBy": "specifického dne", "yearsBy": "let", "timed": "Časově", "firstDayOfMonth": "První den v měsíci", "lastDayOfMonth": "Poslední den v měsíci", "firstDayOfYear": "První den v roce", "lastDayOfYear": "Poslední den v roce", "addDate": "<PERSON><PERSON><PERSON><PERSON> datum", "newPlan": "Nový plán", "addAnother": "<PERSON><PERSON><PERSON><PERSON>", "startTime": "Čas zahájení", "endTime": "<PERSON>as <PERSON>", "inTimeFromTo": "v čase od {{from}} do {{to}}", "dayOfMonthBy": "Dne v měsíci", "cWorkDays": "pracovních dní", "cWeeks": "<PERSON><PERSON><PERSON><PERSON>", "cMonths": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cYears": "let", "everyWeek": "<PERSON><PERSON><PERSON><PERSON> týden", "everyYear": "každý rok", "inMonth": "v měsíci", "everyDay": "každý den", "seqIdEdit": "Úprava ID sekvence", "allowMultiselectSearchRight": "Povolit vyhledávání v přiřazení", "doubleHeightForContent": "Dvojnásobná výška pro obsah", "alrNoVariablesMappingToImport": "Žádné přiřazení proměnných k importu.", "alrVariablesMappingImportLoadFailed": "Načtení přiřazení proměnných k <PERSON>u se nezdařilo.", "variablesMappingImport": "Import přiřazení proměnných", "useAllMappings": "Použít všechna přiřazení", "doExportVariablesMapping": "Exportovat přiřazení proměnných", "alrImportingVariablesMapping": "Probíhá import přiřazení proměnných...", "alrVariablesMappingImported": "Přiřazení proměnn<PERSON>ch bylo <PERSON>.", "alrVariablesMappingImportFailed": "Import přiřazení proměnných se nezdařil.", "alrVariablesMappingImportedPartially": "Přiřazení proměnných bylo import<PERSON><PERSON> pouze částečně. Některé proměnné ne<PERSON>.", "addTable": "Přidat ta<PERSON>", "confirmDynTablesDelete": "Opravdu chcete smazat v<PERSON>brané dynamic<PERSON> ta<PERSON>?", "dynTablesDelete": "Smazán<PERSON>", "addRow": "<PERSON><PERSON><PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "columnDelete": "Smaz<PERSON><PERSON> slou<PERSON>", "editRow": "<PERSON><PERSON><PERSON><PERSON>", "addingNewColumn": "Přidání nového sloupce", "addingNewRow": "Přidání nového řádku", "columnsRename": "Přejmenování slou<PERSON>ců", "rowCellValues": "Hodnoty buněk v řádku", "saveDynTableName": "Uložit název dynamické <PERSON>", "saveDynTableNameQ": "Uložit název dynamické ta<PERSON>?", "saveDynTableNameWarning": "<PERSON><PERSON>, u<PERSON><PERSON><PERSON><PERSON> se, že změna názvu tabulky nebude mít vliv na existující výpočty v šablonách.", "rowMove": "Přesunutí <PERSON>", "alrCsvParsingErr": "Chyba při parsování CSV!", "addFirstTableColumn": "Přidejte první sloupec tabulky", "license": "Licence", "licenses": "Licence", "addLicense": "Přidat licenci", "licenseResult": "Výsledek licencí", "alrLicenceResultLoadingFailed": "Načtení výsledku licencí se nezdařilo.", "my": "<PERSON><PERSON><PERSON>", "licensesAdminDescription": "Správa licencí", "uploadByDragging": "Nahrajte soubor přetažením.", "uploadByDraggingAnywhere": "Nahrajte soubor přetažením kamkoli do prostoru.", "assignVariable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmDeleteSectionName": "Opravdu chcete odstranit sekci <b>\"{{section}}\"</b>?", "deleteSectionWarning": "Upozornění: sekce bude odstraněna za všech dotčených úkolů včetně proměnných.", "tasksAffected": "<PERSON><PERSON><PERSON><PERSON>", "varSearchPlaceholder": "Nap<PERSON><PERSON> fak<PERSON> …", "enlarge": "Zvětš<PERSON>", "show": "Zobrazit", "shrink": "Zmenšit", "hide": "Skr<PERSON><PERSON>", "doValidate": "Val<PERSON><PERSON>", "phoneNumber": "Telefonní číslo", "textLength": "<PERSON><PERSON><PERSON><PERSON> textu", "when": "když", "to2": "na", "that": "že", "dynCondBuilderBlockFunctionDescShow": "Zobrazí promě<PERSON><PERSON>, pokud je podmínka splněna.", "dynCondBuilderBlockFunctionDescHide": "<PERSON><PERSON><PERSON><PERSON>, pokud je podmínka splněna.", "dynCondBuilderBlockFunctionDescChange": "Změ<PERSON><PERSON> hodnotu proměnn<PERSON>, pokud je podmínka splněna.", "dynCondBuilderBlockFunctionDescValidate": "<PERSON><PERSON><PERSON><PERSON> hodnotu proměnn<PERSON>.", "addCondition": "Přidat podmínku", "operator": "oper<PERSON><PERSON>", "equals": "rovná se", "greaterthan": "větš<PERSON> než", "greaterorequal": "větší nebo rovno", "lessthan": "<PERSON><PERSON><PERSON>", "lessorequal": "menš<PERSON> nebo rovno", "demoCode": "<PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "confirmDeleteConditions": "Opravdu chcete smazat všechny podmínky (včetně skriptu)?", "validationErrorMessage": "Hláška při chybě validace", "alrScriptToStructuredBlockConversionFailed": "Konverze skriptu na strukturovaný blok se nezdařila.", "alrStructuredBlockToScriptConversionFailed": "Konverze strukturovaného bloku na skript se nezdařila.", "alrScriptToBuilderConversionFailed": "Konverze skriptu do builderu se nezdařila.", "alrBuilderToScriptConversionFailed": "Konverze z builderu na skript se nezdařila.", "dynCondBuilderBlockFunctionDescScript": "Blok skriptu dynamických podmínek.", "convertToStructuredBlock": "Převést na strukturovaný blok", "convertToScript": "Převést na skript", "dynCondBuilderBlockWatchVarsLabel": "Spustit při změně (watchVars)", "variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sectionName": "N<PERSON><PERSON>v sekce", "newSectionName": "Název nové sekce", "copyToOthers": "Kopírovat do dalších", "testIt": "<PERSON><PERSON><PERSON><PERSON>", "addAdjacentSection": "Přidat sousední sekci", "addAdjacentSectionBelow": "Přidat sousední sekci pod", "selectExistingSection": "<PERSON>ybrat <PERSON><PERSON> se<PERSON>", "renameSectionWarning": "Upozornění: sekce bude př<PERSON>na ve všech úkolech šablony.", "warning2": "Upozornění", "copyAssignmentToTask": "Zkopírovat přiřazení do úkolu", "copyAlsoConditions": "Kopírovat i podmínky", "copyAssignmentToTaskWarning": "Upozornění: přiřazení a případně i dynamické podmínky ve vybraném úkolu budou přepsány.", "importFromOtherTask": "Importovat z jiného úkolu", "startFromScratch": "Začít od začátku", "howToStartAssignments": "Jak chcete začít s přiřazením proměnných?", "selectTaskToImport": "Vyberte úkol pro import", "confirm": "Potvrdit", "selectTaskToTest": "Výběr ú<PERSON>lu pro otestování", "toTestSaveChanges": "Pro otestování je potřeba uložit změny.", "variableAssignmentTest": "Test přiřazení proměnných", "viewAsMobile": "Zobrazit jako na mobilu", "viewAsPc": "Zobrazit jako na PC", "emptySpace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "variableAssignments": "Přiřazení proměnných", "allowCompletionOnChangeOf": "Povolit splnění při změně", "dynCondBuilderBlockFunctionDescRead": "Změní režim proměnn<PERSON> na \"pouze pro čtení\", pokud je podmínka splněna.", "dynCondBuilderBlockFunctionDescWrite": "Změní režim proměnné na \"pro čtení i zápis\", pokud je podmínka splněna.", "dynCondBuilderBlockFunctionDescMust": "Změn<PERSON> režim proměnn<PERSON> na \"povinná\", pokud je podmínka splněna.", "dynCondBuilderBlockFunctionDescSolve": "Umožní splnění úkolu při změně dané promě<PERSON>, pokud je podmínka splněna.", "alrEditorHintsLoadFailed": "Načtení nápovědy k editoru se nezdařilo.", "newsManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newsManagementAdminDescription": "Správa novinek v aplikaci", "addNewsPost": "Přidat novinku", "newPost": "Nový příspěvek", "news": "Novi<PERSON>", "basicInfo": "Základní informace", "publicationPlanning": "Plánování publikace", "displayToUsers": "Zobrazení uživatelům", "displayLocation": "<PERSON><PERSON>to <PERSON>", "newsPostContent": "<PERSON><PERSON><PERSON>", "postTitle": "Název příspěvku", "newsManagementPostDetailPhoneNumberTooltip": "Telefonní číslo pro zobrazení v detailu novinky", "newsManagementPostDetailEmailTooltip": "Email pro zobrazení v detailu novinky", "customUrlLink": "Vlastní URL odkaz", "newsManagementPostDetailCustomUrlLinkTooltip": "Vlastní URL odkaz pro zobrazení v detailu novinky", "stateAfterSaving": "Stav po uložení", "newsPostStateActive": "Aktivní", "newsPostStateInactive": "Neaktivní", "newsPostStatePlanned": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "endNewsPostOnSpecificDate": "Ukončit novinku k určitému datu", "sendNewsPostViaEmail": "Rozeslat novinku emailem", "priorityNewsPost": "Prioritní novinka", "newsManagementPostDetailPriorityNewsTooltip": "Například k oznámení odstávky nebo změně pracovního postupu", "newsPostEndDate": "<PERSON><PERSON>", "pickNewsPostDisplayToOrgUnits": "Kterým org. jednotkám novinku zobrazit?", "pickNewsPostDisplayToRoles": "<PERSON><PERSON>ým rolím novinku zobrazit?", "pickNewsPostDisplayToUsers": "Kterým uživatelům novinku zobrazit?", "pickNewsPostDisplayOnTemplate": "Na jaké <PERSON> novinku zobrazit?", "pickNewsPostDisplayOnHeaders": "Na jakých hlavičkách novinku zobrazit?", "pickNewsPostDisplayOnTasks": "Na jakých úkolech novinku zobrazit?", "pickNewsPostDisplaySubOptionsHelperText": "Nejprve v<PERSON>bert<PERSON>, na které chcete novinku zobrazit.", "newsTagsManagement": "Správa <PERSON> novinek", "newsTagsManagementAdminDescription": "Správa tagů novinek v aplikaci", "addTag": "Přidat tag", "tags": "Tagy", "publicationDate": "<PERSON><PERSON> publik<PERSON>", "contacts": "Kontakty", "avaibleUntil": "Dostupné do", "published": "Publikováno", "newsSinceLastVisitAmount": "<PERSON><PERSON><PERSON> {{amount}} novinek od poslední návštěvy", "noNews": "<PERSON><PERSON><PERSON><PERSON>", "createNewTag": "Vytvořit nový tag", "tagName": "Název tagu", "alrTagSaved": "Tag byl uložen.", "alrTagSaveFailed": "Uložení tagu se nezdařilo.", "confirmDeleteTag": "Opravdu chcete smazat tag \"{{tagName}}\"?", "alrPostSaved": "Příspěvek byl uložen.", "alrPostSaveFailed": "Uložení příspěvku se nezdařilo.", "alrLoadingTagsFailed": "Načtení tagů se nezdařilo.", "confirmDeletePost": "Opravdu chcete smazat příspěvek \"{{postTitle}}\"?", "confirmDeleteMultiplePosts": "Opravdu chcete smazat vybrané příspěvky?", "post": "Příspěvek", "alrPostLoadFailed": "Načtení příspěvku se nezdařilo.", "alrTagDeleted": "Tag byl s<PERSON>.", "alrTagDeleteFailed": "Smazání tagu se nezdařilo.", "alrPostDeleted": "Příspěvek byl smazán.", "alrPostDeleteFailed": "Smazání příspěvku se nezdařilo.", "alrPostsDeleted": "Vybrané p<PERSON>vky by<PERSON> s<PERSON>.", "alrPostsDeleteFailed": "Smazání vybraných příspěvků se nezdařilo.", "alrTempTasksLoadFailed": "Úkoly šablony se nepodařilo načíst.", "rolesRestriction": "Omezení na role", "usersRestriction": "Omezení na uživatele", "orgUnitsRestriction": "Omezení na organizační jednotky", "alrPriorityNewsLoadFailed": "Načtení prioritních novinek se nezdařilo.", "moreInfo": "Více informací", "tas5Info": "TAS 5.0 je tu ...", "totalNewsAmount": "<PERSON><PERSON><PERSON> {{amount}} novin<PERSON>", "alrNewsContainerPostsLoadFailed": "Načtení příspěvků pro kontejner novinek se nezdařilo.", "alrTaskNewsLoadFailed": "Načtení novinek pro úkol se nezdařilo.", "newsManagementPostDetailPublicationDateMustBeforeEndDate": "Datum publikace musí být před datem ukončení novinky.", "alrNotificationsNewsLoadFailed": "Načtení novinek pro notifikace se nezdařilo.", "moreNews": "<PERSON><PERSON><PERSON>", "newsManagementPostDetailConfirmSavingWillSendMail": "Uložení příspěvku způsobí odeslání emailu všem uživatelům, kterým je příspěvek určen. Opravdu chcete příspěvek uložit?", "mailNewsNotification": "E-mail<PERSON>é <PERSON>ornění na novinky", "mailNewsNotificationInfo": "Už<PERSON>l pr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ode<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> jsou mu určeny.", "alrRefreshingConfig": "Ob<PERSON><PERSON>ji konfiguraci...", "alrConfigRefreshed": "Konfigurace by<PERSON> obnov<PERSON>", "alrConfigRefreshFailed": "Obnovení konfigurace se nezdařilo", "ttRefreshConfig": {"heading": "Obnovit konfiguraci ze všech zdrojů", "body": ""}, "getMobileAppTextQr": "Mobilní aplikaci získáte v obchodě s aplikacemi nebo naskenujte QR kód", "dateStart": "Datum z<PERSON>čá<PERSON>", "dateEnd": "<PERSON><PERSON>", "tas_forms_generated": "Počet automaticky vygenerovaných formulářů"}