# Changelog

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).
Types of changes: "Added" for new features, "Changed" for changes in existing functionality, "Deprecated" for soon-to-be removed features, "Removed" for now removed features, "Fixed" for any bug fixes, "Security" in case of vulnerabilities, "Performance" for improved loading times.
All addons can be categorized in one of [ZEA], [Calculations], [Emails], [DMS], [AD], [Workflow], [Templates], [Prints], [BPMN], [Forms], [API], [Export].

## [5.10.2] - Fill-in
### Fixed
- tas-1978 Template import - email notification - incorrect template task reference
- Email sent log
- tas-1849.2 E-mail queue - CleanupCron extended filter
- tas-1984 always show case drag&drop illustration in the middle of the screen
- fix tests (integration)
- fix dms search partial and case insensitive
- tas-2129 Logout console error
- support - incidental "buffer" in storage.getDmsContent protection
- tas-2136 updateSequenceForProcess - sequence fix
- tas-2103 usage statistics - TAS Forms generated
- cron help update
- tas-2111 add NotFound exception for DynamicTableValuesController
- tas-2148 Config subscription fix
- tas-1699 Activation should be forced - option is depracated
- tas-2184 token secrets are overwritten by migration
- tas-2196 JSON.stringify in Arango DeleteLogs filter

### Added
- tas-2067 TAS powered by Syca (>v5.9)
- tas-2131 EmailTransporter configuration options
- tas-2014 Maintenance logs
- tas-2073 Plugins - package.lock + custom node_modules support
- tas-1776 login page - add QR code and edit
- tas-2138 Dashboard table (overviews + tasks) - fix right click
- tas-2106 system for detailed logging
- tas-2130 Maintenance modals - redesign
- tas-2092 Dynamic conditions builder - help
- tas-2074 tmp folders cleanup
- tas-2142 Variable assignment - possibility to change the language in the task preview

### Chore
- tas-1979 TaskContext in the logger – adjustments
- tas-2071 Remove EWS
- tas-2070 remove sharepoint
- Update node.js version to 20.19.2 and Apline version to 3.22
- plugin compilation - fix relative paths

## [5.10.1] - 2025-05-30
### Fixed
- tas-2095 Events - usage tasks incorrect link
- tas-2065 fix mssql subquery
- cronGet hotfix
- tas-2093 resizable DR affects the width of the task form
- tas-2099 case overview load fix
- tas-1953 EWS / MsGraph with .eml files
- tas-2110 Template import - not creating new users
- tas-2051 TAS 5.8 - My + To Pull
- tas-1826 Extend statistics with mobile app user count
- Better cookie expiration timeout management
- hotfix: cookie setup on logout
- tas-2098 Hiding the cookie and token in the log

### Added
- tas-2072 Cron configuration in cron class
- add SIGUSR2 config console dump
- tas-2069 Monaco documenation for plugins
- tas-2096 config refresh - GUI button, PubSub

### Chore
- plugin compilation script
- tas-2100 Tighten biome to unused imports
- tas-2115 Notifications in the mobile app enabled by default

## [5.10.0] - 2025-05-26
### Fixed
- tas-1889 Rename "Solve" button in task detail modal of another user's task
- tas-1991 can't log out while substituting
- tas-2048 log download from GUI
- tas-2065 fix flat-tree endpoint
- tas-2056 Opening the mobile app after clicking on a link
- tas-2066 Dynamic rows - problems with rounding numbers
- tas-2012 Security - Option to insert HTML into username
- tas-1950 Scheduled task - manual activation - fix user context
- pdfToText function fix
- tas-1642 better dyn table csv preview column names
- tas-1930 Unable to save configuration - TAS 5.8 (dev)
- tas-2081 Axios lost of information in error msg
- tas-1964 View repeated task history
- tas-1968 fix label/button colors to match selected scheme color
- tas-2082 Archivation - can't load variables TAS 5.7
- tas-2021 remove metadata retrieval from getLogs
- tas-2088 TAS 5.9 - Fix SelectFormsy
- tas-2076 user's organizational unit data cannot load
- tas-2034 org unit update when case owner changed
- tas-1910 Dynamic rows - broken connection with SuggestBox variable
- tas-1725 dt vars - global value shown in multi-instance task(non-snaps behavior)

### Added
- tas-1796 history record when changing variable value via API

### Chore
- tas-2003 - remove SOAP lib
- tas-2002 - move isDoc to plugin
- update packages
- tas-2022 xmlProcessImport controller refactor
- tas-2084 Fontastic replaced by IcoMoon
- tas-617 get users with organization structure, fixed calculations docs
- tas-2056 Opening the mobile app after clicking on a link - refactoring

## [5.9.0] - 2025-05-16
### Fixed
- tas-2039 Events - missing activation and process owner reference
- tas-2044 Org. structure - disabling assignment of parent to child org. structure
- tas-2005 fix loading calculation scripts and defaults into editor context
- tas-1675 LOVs vars removal from templates
- tas-1376 case summary update after changing task solver
- tas-2038 Selected rows in tables with checkboxes
- fix sending agenda row id when handing over agenda from a single row
- HR BE fixes
- tas-2026 Favourites - Adding a new case - language mutations
- tas-1697 fix chart data labels for production

### Added
- tas-617 get users with organization
- tas-1984 Drag&drop documents to case/task
- tas-838 function for getting list of subprocesses on the case
- tas-1946 T5 - Task search and selection of values from a list
- tas-2046 Bulk download of documents from a case in TAS5
- tas-1838 Add postgresql support
- tas-1814 Cron for parallel activation of scheduled task and events (PoC)
- tas-1718 Redesign of variable assignment - Drag&Drop
- tas-1342 News/Announcements Feature
- tas-1896 DataGrid2

### Chore
- tas-2022 variablesController refactor
- tas-2062 refactoring of favourites

## [5.8.18] - 2025-05-07
### Fixed
- tas-2006 default value of ttjscalc_append_scripts
- tas-1992 template import - graph coordinates not imported
- tas-867 missing template email notif
- hotfix: refactor calculations.ts evaluate method from promise hell to async/await to handle error properly
- tas-1945 T5 Task - closing the calendar for a date variable
- tas-2039 Events - create process from csv

### Added
- Added getNext auth test

### Chore
- tas-1882 ServiceOperationsController refactor

## [5.8.17] - 2025-05-02
### Fixed
- minor backend fixes
- tas-1848 minor fix (import of two non-existant non-empty sections with the same name)
- tas-2032 cannot change template status
- tas-2025.2 Process graph - query error

### Added
- tas-1962 - prints refactoring

## [5.8.16] - 2025-04-30
### Fixed
- tas-2005 "Cannot redeclare block-scoped variable" error in calculations editor
- tas-2020 Handle redirect to Local password for authentication
- Added tprocVersion to template-processes routes
- tas-2025 Process graph - query error
- tas-1722 Option to delete cases with Delete

### Chore
- tas-1593 Folder Structure removal

## [5.8.15] - 2025-04-28
### Fixed
- tas-1832 add translations for licenses
- tas-1990 Dynamic rows - value of newly created
- tas-1994 HR agenda - Input file is missing
- tas-1998 User detail - unit manager
- saml controller hooks fix
- fix planning migration for MikroORM
- tas-1642 fix dyn table csv preview, table loading state when switching tabs
- tas-1842 TAS cannot handle "/" in the index for DT endpoints - fix after dyn tables redesign
- tas-1849 E-mail queue - only active processes
- tas-2023 lib.createNote - unable to create note

### Added
- tas-1941 Add badge for /tasks/mine-to-pull
- tas-1968 change appearance of filters above tables

### Chore
- gitignore - licences
- tas-1872 TS propagace - template(X) controllers

## [5.8.14] - 2025-04-17
### Fixed
- tas-1782 additional fix - DataGrid compact view - date label & event buttons alignment
- tas-1971 Dashboard overview - doesn't show name translation
- Replace refresh-access token pairing from cache to random uuid which should match
- tas-1977 Request header - Authorization
- tas-1980 Disable "Source" option in template notes (CkEditor)
- tas-1947 Fix of non-functional document search
- tas-1986 updateProcessVariablesFromCsvRow - whereIn SQL fix

### Added
- tas-1642 Dynamic Tables Redesign

### Chore
- optimize git packages pull during Docker build phase
- tas-1869 FE Refactoring
- tas-1654 Remove Mocha package from TAS
- fix frontend prettier config file
- tas-1593 template import controller refactor
- tas-1761 mailQ, kerberos, io controllers refactor

## [5.8.13] - 2025-04-10
### Fixed
- tas-1965 Add case for case owner as recepient email notification

### Added
- tas-1832 licenses

## [5.8.12] - 2025-04-10
### Fixed
- tas-1942 Calculation hints don't show up for existing calculations
- tas-1952 - ElasticSearch - delete filters loader fix
- missing "as const" on some required fields in jsonSchema
- redirect informations in case of expired token
- tas-1817 fix substitution translations in account popover
- tas-1926 Deleted folder still shows up in documents modal
- tas-1961 Errors in statistics - bar groups
- tas-1659 Dynamic rows - task completion despite unfilled required columns
- tas-1960 wrong id in case options - added resume endpoint
- pluginManager suffix import
- tas-1817 change dark violet color
- tas-1957 Copying names
- tas-1939 scheduled tasks table - default sort
- tas-1963 LDAP sync USER_EXTERNAL_SOURCE update value fix

### Chore
- tas-1922 Increase of the default timeout
- tas-1039 changeAssessmentRole - removal of fallback when the role does not exist
- tas-1588 Devel + DynamicConfig + DynamicList + DynamicTableCols Controllers - refactor
- tas-1594 - GlobalJsCalculation + Guides + Header Controller

### Added
- tas-1826 Adding the number of mobile app users to statistics

## [5.8.11] - 2025-04-02
### Fixed
- tas-1940 fixed conversion from string to number
- dmsFileRepo - fix knex.withWrapped - dms files all
- tas-1918 Alignment of variables in task form
- tas-1917 Fix SAML
- ES log - empty message
- tas-1921 Check the /token-refresh call and subsequent redirect
- Console calculation plugin bug
- tas-1932 Change the value of a variable of type number using an event
- tas-1927 DMS columns modal doesn't close after save
- tas-1932-fix Change the value of a variable of type number using an event
- tas-1879 Duplicate event execution
- fix proxy unauthorized error
- tas-1931 Add Substitution to user menu
- tas-1919 dmsFileRevisionGet error
- tas-1938 Change link to Changelog
- tas-1933 - progress/actual - revert solver_user_full_name removal
- .env configuration
- DeleteLogs ES filter fix
- ES logs - iproc_id display fixes
- tas-1893 - Puppeteer - Argument Configuration
- tas-1728 fix Substitutes - editing and deleting

### Chore
- tas-1860 reply passing from new controller
- tas-1904 DMS repositories - no .then
- tas-1766 Proxy + queue + realProgress controllers refactor
- references to const values
- tas-1848 - Creating export and import of variable usage

### Added
- tas-1795 excel number formatting based on decimals
- tas-1913 Multiselect - component height setting
- tas-1848 - Creating export and import of variable usage
- tas-1908 Multiselect - search in filtered values
- tas-1801 crons - cronjob restart, restart context removal

## [5.8.10] - 2025-03-13
### Fixed
- tas-1593 events removed required from property in schema
- tas-1417 lib.extendDR - nevyužité sloupce naplnit null dle počtu položek
- tas-1880 HrAgenda - filter by value
- tas-1883 Case - document count loader after closing the current task preview
- tas-1648 Tisk sestavy přes tlačítko Tisk z CO
- tas-1892 lib.getUserApiFromInput - user_display_name
- CustomViews - filter number values
- tas-1885 Odstranit zbytky pdftk
- tas-1877 Highlight case on mouse hover
- tas-1900 DataGrid Croatian translation
- tas-1902 new plugin support
- tas-1538 updateDTFromCsv removes data when catching error in try catch
- ES logger filter and select fix
- zobrazenie opravnenia pripadov
- tas-1907 White screen when saving a sequence type variable
- tas-1905 competences controllers fix

### Added
- tas-1222 Add DataGrid component to Storybook + other adjustments/fixes
- tas-1890 Loading rows for cases and tasks from configuration
- tas-1834 - oauth2 connector for SMTP
- tas-1887 Search in multichoice variable

### Chore
- tas-1593 export import controller refactor
- tas-1593 Export controller refactor
- tas-1759 Process-history controller refactor
- tas-1809 sequence controller refactor
- devops - upload images after tests
- tas-1593 Events controller refactor
- tas-1861 task endpoint optimization
- tas-1762 TS propagace - Migration + mutex + neon controllers
- Bump versions of libs to latest

## [5.8.9] - 2025-03-03
### Fixed
- tas-1499 Fix transpilation of Array.from callback
- tas-1864 elastic logger bug fixes
- fix azureCallback + azureAdAuthority callback
- tas-256 Resolving last solver when finished by vice
- tempDirPath in ews cronJob
- tas-1871 Export to excel - list of texts localization
- editing LoV variable in case view
- exports - case status localization
- tas-1855 Change documentation link in TAS
- AzureAdAuth with turned off profile pics

### Added
- tas-1876 MobApp - Unable to download files on iOS - add method

### Chore
- tas-1841 INSTANCE_TASK_VAR_USAGE removal
- tas-1767 Registered mobile device controller refactor

## [5.8.8] - 2025-02-27
### Chore
- tas-1760 Refactor instance task controller
- tas-1765 Process(X) controllers refactor

### Added
- tas-1090 New Planning
- tas-1831 Sequences Redesign

### Fixed
- devops update qa workflows to support tas 5.8
- competences.test fix
- tas-1835 default value CRON_SYNTAX in scheduler
- tas-1866 LDAP not creating user

## [5.8.7] - 2025-02-21
### Added
- tas-1845 hotjar for instances

### Fixed
- tas-1587 requiredID in dmsTag fix
- tas-1728 Substitutes - editing and deleting
- E2E tests - updated test-db script generation readme for GH Actions
- tas-1850 Duplicate row in dataGrid
- tas-1836-fix Dynamic rows - combination this.changeDef & this.changeCell is not executed on initial load
- tas-1852 Incorrect task width in the mobile app
- tas-1851 documents all - white screen
- tas-1842 TAS cannot handle "/" in the index for DT endpoints
- tas-1704 Excel export - system variables localization
- tas-1853 Fix plugins import
- tas-1858 Fix error due to empty colors object from /org-colors

### Chore
- tas-1763 Organization controllers refactor

### Added
- tas-984 MikroORM - migration improvements
- tas-984 MikroORM - migration improvements 2
- tas-984 MikroORM - migration improvements 3
- log on cronJob server start
- tas-1853 logs for plugins

## [5.8.6] - 2025-02-18
### Added
- tas-984 MikroORM
- tas-1837 Add init arango & elastic migrations
- tas-1839 Add migration tool
- devops-kb-10494 Add E2E test workflow
- tas-1820 MobApp - Unable to download files on iOS
- tas-1839 MikroOrm migration tool -> improvements + automatization

### Fixed
- tas-1693 Template processes import - subprocess and event
- event rule endpoint - default values
- tas-1629 Disabling to create (overwrite) a user with the same username
- tas-1587 Wrong placed required ids in DMS controllers
- tas-1833 Dynamic rows - evaluation of isVisible column value
- tas-1494 fix filtering by translated system variables in overviews
- tas-1846 Regular user does not see translations of LOV variables starting with _ in overviews
- tas-1836 Dynamic rows - combination this.changeDef & this.changeCell is not executed on initial load

### Chore
- tas-1711 xregexp packages removal
- tas-1711 UUID package removal

## [5.8.5] - 2025-02-13
### Fixed
- tas-1715 fix scrolling with arrow keys in multiselect
- tas-1824 pairing new device - translations
- tas-1825 saving filter in table - missing translation
- tas-1403 Logs of created substitutions appear as errors
- tas-1723 inconsistent width of a column with checkboxes in datagrids with bulk actions

### Added
- tas-1828 Configuration of loading overview rows on FE
- tas-1830 Task sections endpoint - performance
- libreMacroConvertDmsFile - fix macroFile path
- completely ignore masterUrl parameter

### Chore
- tas-1711 Get parameter names package removal
- tas-1587 DMS controllers refactor
- tas-1711 Pino package removal
- tas-1769 RoleUsers + RuleDefinition + Saml2 controllers - refactor
- tas-1711 String replace async package removal
- tas-1711 Sanitize-filename package removal
- tas-1711 String to stream package removal
- tas-1441 EWS client connector
- tas-1441 SharePoint client/connector

### Added
- tas-1798 Implement CO components to Tas

## [5.8.4] - 2025-02-10
### Added
- tas-1787 Add link to TAS website to Powered by Team Assistant
- Default config for container image
- tas-1494 fix filter parsing in custom views

### Fixed
- tas-1697 fix a bug in pie charts with datalabels
- tas-1589 DynamicTableValuesController - bug fix in URL
- tas-1810 fix localStorageStore
- tas-1422 rename and change layout of user preferences in user settings
- Default config for container image - fix startup diagnostic for frontendUrl because of default value
- tas-1812 fix user settings and other issues with user id while substituting a user
- tas-1815 Dynamic rows - saving dynamically calculated values
- tas-1817 Rename vice to substitute
- fix file download stability
- tas-1546 fix udpateDTfromCSV merge update date

### Chore
- tas-1678 linux TLS to native TLS
- tas-1757 InstanceMassTask controller - refactor
- tas-1768 ReportGraphs controller - refactor
- tas-1711 Blocked package removal
- tas-1711 File system cache package removal
- tas-1756 Hr Controller - refactor
- tas-1711 Flatted package removal


## [5.8.3] - 2024-02-03
### Added
- tas-759 Drag and drop in multiselect component
- tas-1751 Change multiselect row selection logic
- tas-1738 Option to choose number variable alignment in the task form
- tas-1529 change Tas favicon
- tas-1802 Open the last opened overview when opening overviews (new tab or refresh)

### Fixed
- tas-1739 Hide option to suspend or delete case in overview
- tas-1750 Task instructions not working
- tas-1717 Additional design fixes in new case modal
- tas-1777 - ability to turn off tmp cleanup on bootstrap in configuration
- tas-1715 Move items in multiselect with enter
- tas-1544 fix long form component labels pushing tooltips out of visible area
- tas-1599 keep tree expanded after adding new org. unit
- tas-1745 Hide buttons for roles without rights to use them
- tas-1752 TAS 5.7 - filter in CV - operator ne
- tas-1583 Not saving the change of order in DR
- tas-1780 Incorrect logout as a delegated user
- Azure ad missing redirect
- tas-1781 Moving vertical sections TAS 5.7
- logger in pdfExporter
- orstr import entity typo
- tas-1747 Cache invalidation by pattern
- tas-1750 - task height fix
- tas-1749-fix DataGrid - scroll top when filtering
- tas-1782 Change button size in tasks when in compact view
- tas-1797 - totalCount in onlyCount on customView
- mobile auth - wrong accessTokenName
- tas-1799 Display name in progress history
- tas-1800 Can't click on the first row in calculations
- tas-1788 Calculations editor doesn't show jsdocs for functions from scripts
- tas-1793 Variable of type number and value "0"

### Chore
- tas-1771 - remove fortunaDMS event
- tas-1441 - TasApi axios to client
- tas-1592 EntityImport Controller - refactor
- tas-1764 Pdf + Performance Controllers - refactor
- tas-1794 Renaming the shared overview to tasks + to pull
- tas-1711 Async package removal

## [5.8.2] - Fill-in
### Added
- tas-1315 cookies settings configurable

### Fixed
- tas-1740 fix showing/hiding columns in data grids
- tas-1395 edit admin menu search to search also in category names
- tas-1610 Task preview - is not 100% functional
- tas-1709 Template - HTML export
- fix - download of bulky logs
- tas-1744 UsageStatisticCron
- tas-1747 Rozdilne-chovani-skriptu-vypoctu-TAS-5.7-vs-TAS-5.3
- tas-1749 DataGrid - scroll top when filtering
- tas-1730 Disable the button for editing an overview on someone else's overview
- tas-1772 Migrace - ivar_big_value_insecure funkce pro LOB
- azure AD login fix
- tas-1755 DR - cell key generated from title can cause problems
- tas-1717 Template description is not displaying
- tas-1746 add versioning for cookies
- tas-1773 Error in event rule editing
- tas-1743 - MailTaskCron null check for emailAddres

### Chore
- tas-1589 DynamicTable Controller - refactor
- tas-1590 DynamicTableValues Controller - refactor
- tas-1591 Remove ElasticSearch Controller and ES GUI, EventDefinition Controller - refacto
- tas-1591 Remove ElasticSearch Controller and ES GUI, EventDefinition Controller - refactor
- tas-1771 - clean customer specific named parts

## [5.8.1] - 2024-01-14
### Fixed
- Fix rotated condition on onlyCount
- tas-1735 Preview of edited overview - changes are not applied
- fix EwsCheckUnprocessedMailsCron - result log
- tas-1244 Notes - fixes
- tas-1555 TAS 5 - dynamic conditions (document list) - change attribute isPreview
- tas-1455 fix - export did not do anything on CO React & Calculations tabs
- tas-1748 Mobile app - deeplinks for ios fix
- tas-1719 Mobile app - remember filtering

## [5.8.0] - 2024-01-12
### Fixed
- tas-1679 Redesign components (reduce spacing) - gap height in task form
- tas-1720 Document preview - selection changes when evaluating dynamic conditions
- tas-1736 Option to reset the width of the overview tree
- customViewMail - format.replace is not a function at expandFormat
- fix calculation logs
- fix fulltext in documents
- tas-1694 Structure import users - default notifications configuration
- fix validateIn while storing entity
- fix tas-1486 Duplicating overviews
- fix application monitoring typo
- fix CV list length error

### Chore
- remove operation logs
- refactor ES connector/client

## [5.6.19] - 2025-01-09
### Added
- tas-1732 Limit the maximum width of the task container
- redis keyPrefix with autogen

## [5.6.18] - 2025-01-07
### Fixed
- Fix cron.js run
- tas-1487 Dynamic rows - changeDef - validation message and caption
- Remove token refresh from requests in manuals

### Added
- tas-1455 Added import and export scripts functionality
- tas-1673 Swagger autogeneration
- tas-1649 - Axios API
- tas-1710 rename Open to Solve in task modal
- tas-1658 Add Powered by Team Assistant
- tas-1620 TAS 5 - change value of variable typed DT in GUI

### Fixed
- tas-1664 Can't choose dynamic rows in overview columns
- tas-1599 refresh tree in organization structure
- tas-1618 TAS 5 - column width in the overview is not applied
- tas-1679 Redesign components (reduce spacing)
- tas-1486 Duplicating overviews
- tas-1712 Tasks - open every task within the "All" tab in a window
- tas-1468 Logger reader and Logger filter move to TasLogger and other functionality
- tas-1468 Logger controler move to new architecture
- tas-1664 fix missing cv_name when saving overview after changing columns
- fix administration menu card height
- tas-1714 Configuring string properties in schemas as nullable, adding cv_name_cs to the body
- tas-1716 Boolean in config is saved as 0/1 instead of false/true
- tas-1701 Restore mass task solving
- tas-1721 Confirmation modal pops up below the entity change modal when changing an entity from a case
- Fix locking in manual cron run start
- tas-1669 Duplicate creation of organization units + other organization unit editing & creation fixes

### Chore
- tas-1652 await-exec -> exec-sh
- tas-1532 Remove openTelemetry
- tas-1651 string-table -> table
- tas-1684 remove ETL lib + audit + overfluous dependencies
- tas-1683 Fix bug in TemplateProcessShredding Cron
- tas-1586 CustomViewsMail Controller - refactor
- modify CI/CD test process
- tas-1708 Switched to Alpine based Docker image, increased node.js version

## [5.6.17] - 2024-12-19
### Added
- tas-1705 Add default task name column to case history modal
- tas-1687 Document preview - behavior change (opening in new window)

### Fixed
- fix converted print endpoint
- fix user photo download
- fix competence rules endpoint
- fix dms tags response formating
- fix hr-business endpoint
- tas-1129 User - display name field, replacing user full name (fix in mails, prints, variables, system vars, logs)
- tas-1697 Pie chart in statistics does not fit the screen

## [5.6.16] - 2024-12-16
### Fixed
- tas-914 HR Agenda (remove complete agenda handover button in vice tab)
- tas-1501 Option to change the date format in user settings (fix prints, date format options)
- tas-1660 TAS 5.6 - logout when switching admin/regular user view
- tas-1695 fix email attachments
- tas-1496 Sorting overviews (add localization compare)
- tas-1690 click on events in the permissions tab - error
- tas 1700 nested arrays in adminScript in cache module
- fix: FCM notification muted condition
- fix: Default monaco documentation endpoint
- tas-1647 CaseStatusDeleteController - fix schema
- fix: Mobile app notifications with badge

### Chore
- tas-1585 customView controller - refactor
- Removed some vulnerable packages during Docker image build
- devops-kb-10949 connect gh actions to use new container registry
- tas-1441 - evalMath client connector
- tas-1441 - rest client connector

### Added
- tas-1579 Display vice history

## [5.6.15] - 2024-12-12
### Added
- tas-1685 BiomeJs
- tas-1129 User - display name field, replacing user full name (view, sort, filter)

### Fixed
- tas-1691 5.3.38 - white screen na dashboardu
- tas-1695 Attachment parsing for email notifications
- tas-1604 Dynamic Table - disable forced date conversion in exports
- tas-1605 Unable to edit org. units with many members

## [5.6.14] - 2024-12-10
### Added
- tas-1607 Add template info to modal of case variables
- tas-1501 Option to change the date format in user settings

### Fixed
- tas-1677 ZFO - nosigs parameter
- tas-1680 Redirect to task/case from link when using SSO
- tas-1539 Added validation for empty tasksData in iTaskReport
- Fix admin scripts
- tas-1686 Fix archiving rights so that archived case is visible to users
- tas-1688 Mobile app - deeplinks for iOS

### Chore
- tas-1061 require->import
- tas-1653 SAML to latest
- tas-1657 Packages to latest
- Add package-lock.json to TAS Backend Repo
- tas-1441 - elastic indices client connector
- tas-1441 - idoklad client connector

### Chore
- tas-1558 Dashboard Refactoring

## [5.6.13] - 2024-12-07
### Fixed
- mobile app

## [5.6.12] - 2024-12-06
### Added
- tas-1126 Restrict changing password/settings of system users
- add changelog check on github actions workflow
- tas-1676 Mobile app - loss of connection to BE + deeplinks

### Fixed
- tas-1667 Rename Home button to Dashboard
- setDeviceToken via request body
- tas-1625 Dynamic conditions - click on Complete from text variable
- tas-1671 Change mail button color - old Outlook

### Chore
- tas-1644 update getCaseNotes() function
- tas-1386 - eliminace promise.map - controllers, repositories

## [5.6.11] - 2024-12-05
### Fixed
- tas-1572 setEmailProcessedOnError in ews/ms graph
- tas-1662 Incorrect filtering in overviews
- newest commit version of tas/knex 1.0.3
- tas-1572 setEmailProcessedOnError cron help editing HTML
- tas-1507 Disabled OCR processing in Tika, correct contentType for ZIP files, metadata processing for strict OOXML files
- tas-1046 isDocPdf parsing
- tas-1665 case status bug

### Added
- tas-1529 change TAS icon
- Template - fixed certain saving inconsistencies in the new template and case statuses forms & the graph
- tas-1603 Dynamic rows - configuration parameters "showAdd" and "showClone"

### Chore
- tas-1638 - Cron overhaul


## [5.6.10] - 2024-11-28
### Fixed
- tas-1247 path fix from DMSF_SRC
- tas-1602 Manuals: repeated access token calls + error jwt expired
- tas-1430 config ms graph cron
- tas-1639 Removed obfuscation from PdfExporter and Print files

### Chore
- tas-1469 Replacing console.log with a tasLogger
- tas-1441 - azureAdAuthority client connector
- tas-1441 - githubAuth client connector
- tas-1320 Documents preview

### Added
- tas-1244 Added edit & hide functionality for notes
- tas-1645 Pridat mobile app redirect modal + deeplinks - mobile web - update


## [5.6.9] - 2024-11-25
### Added
- tas-1635 Mobile app - add domain whitelist

## [5.6.8] - 2024-11-22
### Fixed
- tas-1611 Sorting by Submission Date does not work
- tas-1569 MANAGED_USERS sql query - fix parametrization
- tas-1573 DR under hidden section
- tas-1415 Alignment of variables in task form
- tas-1604 Dynamic Table - excel export data types
- tas-1623 csv/excel not working
- tas-1626 Vertical scroll of the page jumps after opening a filled Select input
- tas-1630 Csv/excel export - export system variables
- tas-1551 Remove the option to search in the overview in the variable type DR
- tas-1465 Quick filters - negative operators for multilingual attributes
- tas-1503 Inactive Save button on quick filter in the overview
- tas-1633 CaseOverview - export null number
- tas-1634 Task definition broken on opening from graph
- tas-1247 path fix from DMSF_SRC

### Chore
- tas-1386 - eliminace promise.map - utils, workflow, calculation, auth, repos
- tas-1386 - eliminace promise.map - crons
- tas-1584 - connections + console + cronRunsHistory controllers refactor

### Added
- tas-1509 Selection of multiple options in the inline filter
- tas-1514 Ability to filter date ranges

## [5.6.7] - 2024-11-21
### Fixed
- tas-1580 - add missing frontend endpoint

## [5.6.6] - 2024-11-20
### Fixed
- tas-1541-new Helper in Service Console (sys tags)
- tas-1436 fix lock error without redis
- tas-1581 Editor in a dark theme
- tas-1601 Dynamic conditions - repeatScript doesn't work
- tas-1600 - add AuthException to auth warning logs from error logs
- tas-1606 Add default task name column to the active tasks modal
- tas-1609 Unable to take over task via specific path
- tas-1617 Monaco Editor - unreadable selected row in commands list
- tas-1619 Switching overviews does not change the overview name in the header
- tas-1492 Update tedious(13.2.0) & knex(3.1.0)


### Chore
- tas-1436 - total overhaul of cache, pubsub, locks
- tas-1386 - eliminace promise.map - tests

### Added
- tas-1406 DataGrid component for react prints update3
- tas-1582 Add mobile app redirect modal + deeplinks - mobile web

## [5.6.5] - 2024-11-13
### Fixed
- tas-1575 Migrace convert-varchar-to-nvarchar - JS_SCRIPTS constraint
- tas-1561 Task to pull from email - open modal
- tas-1505 additional fixes - template save confirm dialog appears incorrectly

### Added
- tas-1541 Helper in Service Console

### Chore
- tas-1444 TS Propagace - Azure Controller
- tas-1446 TS Propagace - CertificatesController
- tas-1448 TS Propagace - ChromePluginController
- tas-1386 Eliminace promise.map - migration
- tas-1556 Delete old components from FE repository

## [5.6.4] - 2024-11-08
### Added
- tas-1454 Variable assignment - vertical sections
- tas-1576 MsGraphCreateProcessesFromMailCron - fix tmp path
- tas-1574 Added _.find regex into calculationValidation
- tas-1560 MailUsageStatisticsCron - fix mail sending
- tas-1541 Helper in Service Console

### Chore
- tas-1449 TS Propagace - Competence+CompetenceRule Controllery
- tas-1550 Created functions to find functions within scripts
- tas-1444 TS Propagace - Azure Controller
- tas-1446 TS Propagace - CertificatesController
- tas-1448 TS Propagace - ChromePluginController
- tas-1386 Eliminace promise.map - migration
- tas-1556 Delete old components from FE repository

### Fixed
- tas-1100 Copy DT with one column

## [5.6.3] - 2024-11-01
### Added
- tas-1265 mobile settings notifications
- DevOps-10623: Automatic generation of /config/build.json containing version information for frontend and backend
- DevOps-10623: Automatic versioning for loggedUserStore.ts file
- tas-1536 Redesign Users - password in new user modal
- tas-1553 All tasks do not include tasks to pull - point 2

### Fixed
- tas-1515 Sorting events alphabetically
- tas-1520 Email templates
- tas-1513 Future date in date variable
- tas-1508 for-of for adsync cronjob in LDAPSync
- tas-1475 Fix meta in Arango Handler
- tas-1531 Extract axios - tika timeout parameter
- tas-1531 DmsFileIndexingCron - parseMetadata parameters fix
- tas-1438 Lowercase for globalThis.orm.repo
- tas-1533 Cron history - endpoint method
- tas-1537 Values added by in documents
- tas-1382 Misalignment of variables when the screen is resized
- tas-1483 Finished tasks - errors when opening and closing
- tas-1421 - fix excel/csv export
- tas-1496 Sorting overviews
- tas-1543 Manuals - change of table size after clicking on a row
- tas-1493 Confirm modals with enter
- tas-1544 Variable DL multichoice - incorrect tooltip display
- tas-1542 Misaligned buttons in the administration App Status
- tas-1511 Setting iproc_summary to NULL instead of empty string
- fix container access in migration file: init-es-index
- fix DMS controller - downloadMultiple - zip file handling

### Chore
- tighten eslint rules
- tas-1386 Eliminace promise.map - repositories
- tas-1534 remove ZEA
- tas-1435 remove unused src/api/responses
- tas-1490 Update Material UI to 6.1.x
- tas-1441 tika client / connector
- tas-1386 Eliminace promise.map - repo, utils, workflow, systemApi.ts jsDocs


## [5.6.2] - 2024-10-18
### Added
- tas-1422 Users - Modal for new user

### Fixed
- tas-1477 - warning printed on a react case overview print
- tas-1478 variable assignment - error after Lodash update
- tas-1472 Overview not showing values after saving
- tas-1264 vice - highlight status
- tas-1481 instance process versions primary key migration - repeated execution
- tas-1412 Select (list) scroll when using arrow keys
- Incorrect TemplateVariableLov repository import
- tas-1489 Update Mui DataGrid version to @latest
- tas-1211 user restrictions configuration - extentsion for other FE components
- tas-1502 Overview owner name not displayed
- tas-1505 Closing an unchanged template - confirm dialog appears when archivation/shredding is turned on
- tas-1518 Added HR_ROLE_ID remap in TemplateProcessImport
- tas-1485 Opening a link to an overview should open its folder

### Chore
- tas-1260 axios -> elasticSearch
- tas-1392 - Rewrite authorization controller
- tas-1434 - uninstall package debug
- tas-1438 - globalThis.orm.repo always starts with lowerCase

### Added
- tas-1046 storageApi parsing of isDocs pdf files

## [5.6.1] - 2024-10-14
### Added
- tas-1429 - support of encrypted zip files in msgraph/ews processing cronJobs
- tas-1395 administration > search

### Fixed
- error in index creation
- error in migration step history conversion
- tas-1421 disable websockets
- tas-1375 HR Agenda - can't change task owner
- es6-scripts-and-calculation-transpilation-probe failing
- tas-1421 log level warning - AuthBadLoginException
- tas-1463 PasswordModule - default login (auth_id)
- tas-1458 Cron - update name references
- tas-1421 user parameters controller fixes
- tas-1419 change vice notifications
- tas-1467 TAS 5 - sharing of CV changes filter operators
- tas-1383 Rename the Generate record to history button
- tas-1432 user photos migration - check CLOB length
- tas-1466 Email notifications - HTML in task description
- tas-1462 Displaying variables in some archived cases
- tas-1407 overview duplication - the path before "/" is not copied
- tas-1471 export number columns
- tas-1421 dynamic table sql queries execution handling
- tas-1476 TAS 5 - dynamic conditions - varDefOn on DR disables the DR script

### Chore
- tas-1445 CaseStatus controller TS propagation
- tas-1386 Eliminace promise.map - controllers continuation
- tas-1057 - added detailed bug reporter a.k.a. Debugr

## [5.4.8] - 2024-10-04
### Added
- tas-1428 overview search - no found favorites (UI edit)
- tas-1424 add calculation validation service + lodash validation
- tas-1374 Dashboard - component dashboard Production
- tas-1194 New dashboard component - mine and to pull tasks combined

## Fixed
- tas-1302 Monaco editor suggestions
- tas-1400 fix Elasticsearch indexation and search query
- tas-1350 Updating records in tables in T5
- tas-1027 Change favicon via GUI
- tas-1314 fastify request body size limit
- tas-1369 Diagram saving - template remained unchanged
- tas-1452 fix sql query syntax in getDbMajorVersion
- tas-1259 Reload the table after changing a case variable
- tas-1388 Default sorting of task tables
- tas-1414 filter in the table - error
- tas-1424 Promise.map and Promise.mapSeries in cron context
- tas-1457 Display documents (list, number) from another case
- tas-1443 Email error handling
- tas-1379 Archivation - bulky process
- tas-1298 TAS 5 - case variables modal - fixes
- tas-1248 Archivation - attachments

## Chore
- tas-1447 Changelog controller TS propagation
- tas-1386 Eliminace promise.map - controllers
- tas-1450 Config controller TS propagation
- tas-1391 Archivation controller refactor

### Added
- tas-1398 DataGrid component for react print update2
- tas-1397 renamed administration > schema to "visual identity"
- tas-1323 Variable assignment - facelift
- tas-676 Variable of type text - option to display as password
- tas-1274 Plugin Support
- tas-1427 change css table thead background

## Fixed
- tas-1370 fix template process import - concurrency
- tas-1389 Unable to save modification in organizational unit
- tas-1411 Tree id bug 2
- tas-1408 Searching in the overview tree includes hidden overviews in the "All" tree
- tas-1413 Link in readonly variable (DT, LT)
- tas-1339 Manipulation of overview columns and switching from/to expert edit mode
- tas-1370 fix template process import - export event rules
- tas-1394 UserParametersPost Body Validation
- tas-1420 Inconsistent behavior of folders in overview trees

### Chore
- tas-1274 Refactor and cleanup Calculations
- tas-1060 Refactor Emails
- tas-1393 Cron Controller refactor
- tas-1169 Update frontend lodash to 4.17.21

## [5.4.6] - 20.9.2024
### Added
- tas-1372 Dynamic conditions - functions to obtain a value as an array of objects (multichoice variables)
- tas-748 tree structure in tables - have the ability to expand the "column" of the tree
- tas-1385 Mui new license key
- tas 1378 DataGridTanstack component for react print update1
- tas-1197 favourites - order changing
- tas-1211 user restrictions process security configuration exception

### Fixed
- fix restore db init sql scripts
- fix insert cron files into crons table migration
- tas-1387 Dynamic conditions - changeVarVal on dynamic rows - watchVars in combination with promise

### Chore
- tas-1313 UserRole, UserPhoto, UserParameters controllery
- tas-1386 Eliminace Promise.map
- tas-900 no-undef as error

### Chore
- tas-1169 - update lodash to 4.17.7

## [5.4.4] - 2024-09-06
### Added
- tas-1348 - default dashboard preview on reset
- tas-1253 - Odeslani push notifikace na iTaskToPull
- tas-959 Task variable "multiple items" can be set as Select multiple
### Fixed
- tas-1261 Fix superfluous warning log in crons
- tas-1356 ZipApi - unencrypted files in archive
- Date in excel export
- tas-1279 User photos migration - filter null string
- tas-1360 References passing in the task detail
- tas-1343 deletion of text searched within overviews
- Process print endpoint - enabled archivation check
- tas-1170 fix tmp deadlock on cron init
- tas-1365 Nezobrazovat error u chybejici profilove fotky
- tas-1333 DataGridTanstack component for react prints

### Chore
- CronJob cleanup + new arch in container
- tas-1355 move mssqlForceTypes to DB
- response error log format
- tas-1358 Exceptions full refactor
- tas-1345 storage/mutable/externals cleanup + config cleanup
- tas-1345 rename mutable/external to storage/config, move docs to dist + postbuild rutine
- update oracleDb to 6.6.0

## [5.4.3] - 2024-08-26
### Added
- tas-1308 Ability to move modals when editing an overview
- tas-961 Added user photos (main & vice) to user menu
- tas-1346 - tests-cy - use Chromium for local test runs

### Fixed
- tas-1034 TAS 5 - migration of org. unit logos from TAS 4
- tas-1066 dynamic rows - documents
- tas-1205 TAS 5 - unshared overview error message
- tas-1131 Add the ability to delete a document within the context of an active task in the Documents section.
- tas-1325 Fix vices modal in version 5.4
- tas-1295 Button to download a document in the variable Document List
- tas-1300 TAS 5 - overview on the dashboard - sorting by source overview
- tas-1275 TAS 5 - customization of variables in the task detail
- tas-1330 fix label and input element link for date input
- tas-1335 Token refresh - handling undefined
- tas-1337 Tree id bug
- tas-1192 TAS 5 - document preview in case documents
- tas-1137 component Select in task on mobile - search is disabled for < 15 choices
- tas-1332 TAS 4 table component - bulk selection (service operations)
- tas-1338 Overviews - duplicate rows
- tas-1341 Shorten constraints names under 30 chars for oracle db
- tas-1008 Cypress tests for overviews
- tas-1289 Fastify - response stream
- tas-1250 lib.getUserWithRole option to filter inactive users
- fix CronRunner initialization
- tas-1347 fixed default favourites duplicating on dashboard reset
- tas-1293 Dynamic rows - highlighting validation and message placement
- tas-1352 - date columns in overviews - (not) displaying the time

## Added
- tas-1326 Redesign Users - Data Grid

## [5.4.2] - 2024-08-19
## Added
- tas-922 SIEM Extended logging

### Fixed
- tas-1248 Archivation - case/task detail doesn't work
- tas-1234 TAS5 compatibility - changeVarVal for document list
- tas-1304 minor frontend fixes
- tas-1306 TAS 5 - lib.libreConvertDmsFile accented file names
- tas-1268 TAS 5 - lib.getFileStream accented file names
- tas-1213 Movement in DR using the arrow keys
- tas-845 Movement in tables/dynamic rows using "shift+tab"
- tas-1084 fix code transpiler for null inputs
- tas-1303 Deleting documents from variable value after deletion in attachments

### Chore
- tas-1235 refactor of UsersController + tests for users
- tas-1154 update javascript-obfuscator to latest version

## [5.4.1] - 2024-08-16
### Added
- tas-1264 vice - highlight status

### Fixed
- tas-1210 - date variable and movement with arrow keys
- date variable error highlight
- tas-1226 TAS 5 - buttons on the dashboard (Actions) are stuck, even column resizing doesn't help
- tas-1081-hotfix - fix Notification before session expiration
- tas-1276 - fixed support of multi-middleware filters
- tas-1283 Monaco editor - move to TAS from an external URL
- tas-1284 tasks - switching file preview for doc files
- tas-1277 redesign tree structure
- tas-1270 HTML template export - expand conditions and definitions
- tas-1287 Incorrect behavior of file column validation in DR variable
- tas-1276 migrate utility - add args capability
- tas-1286 Modal for selecting document files in the DR variable column
- tas-914 HR Agenda various small fixes
- tas-1291 Mobile app in 5.3 - instance expires
- tas-1280 Calculation context - user undefined
- tas-1256 rest tests fix
- tas-1255 GHA LDAP port fix

### Chore
- tas-1058 refactor of auth directory

## [5.4.0] - 2024-08-07
### Added
- tas-1184 mobile application - add information to the web application
- tas-1263 DataGrid + TreeView update
- tas-1267 Mob. App android back button function
- tas-1273 dyn. conditions - find variables in vSync or procVSync

### Fixed
- tas-1081-new - fix Notification before session expiration
- fix unnecessary rerendering of app.react.js

### Chore
- tas-1218 refactor of DocxApi, DynamicRowApi, DynamicTableApi
- tas-1062 refactor of Queue
- tas-983 sails removal

## [5.3.0] - 2024-07-29
### Added
- tas-1019 dynamic conditions - refactoring

### Fixed
- tas-1163 DR is inexplicably filled with historical value
- tas-1182 DR after entering a value into a cell, the value is reset to zero


## [5.2.6] - 2024-07-22
### Added
- tas-914 HR Agenda
- tas-1223 Mobile configuration visibility config based, usage statistics extended with mobile app usage
- tas-1045-new Archivation Frontend UI

### Fixed
- hotfix - cronJob page loading
- tas-1004-fix fix TAS5 - Actual Tasks - hide technical tasks for use
- tas-1117-fix fix Document preview - mobile version
- tas-1004-fix fix TAS5 - Actual Tasks - hide technical tasks for user
- tas-1208 Arrow navigation in dataGrids
- tas-1228 - closing a note deletes the written content
- tas-1229 Assigning roles to users - the plus sign is not displayed on the second and other pages
- tas-1230 TAS 5 - display of date variable values
- tas-1102 dynamic configuration change of logger.performanceCategory
- tas-1201 overview sorting
- tas-1238 typesafe in refresh & logout for missing tokens
- fix broken scroll in document detail preview
- tas-1014-fix Case overview - case status accent sensitivity
- tas-1245 Unable to scan a QR code in dark theme - app
- tas-1243 Remove option to link mobile app as a vice

### Chore
- tas-1187 refactor of StorageApi
- tas-1233 refactor of excel + holder + html API

## [5.2.5] - 2024-07-15
### Fixed
- tas-1215 Mobile logout
- tas-1206 Copy an existing overview - shows default names
- tas-1202 Opening a "Favorite" item with a mouse wheel - won't open in a new tab
- tas-1081-fix fix Notification before session expiration
- tas-1215 move notification trigger to mailQ
- tas-1215 Notification Title
- tas-754 table - move the header filter icon inside the input
- tas-840 table - moving in column header filters using tab
- tas-1221: Refactor TikaParser + fix file upload + msgraph workflow constructor

### Chore
- tas-1203 refactor of BaseController
- Removed some vulnerable packages during Docker image build
- Fixed image build process
- Updated MUI DataGrid to version 7.10.0

## [5.2.4] - 2024-07-01
### Added
- tas-1162 FE as a proxy to BE for Flutter
- tas-1032 section filter in user settings
- tas-1196 substitute - make it visible already in the user's image on all screens
- tas-1196 make vice visible on the user's image
- tas-1081 Notification before session expiration
- tas-1098 Integration plugin for Gantt diagrams on CO

### Fixed
- tas-1004 TAS5 - Actual Tasks - hide technical tasks for user
- tas-1174 TAS 5 - check solveTaskOn, throws an error even when the condition reaches the return statement
- tas-1190 FE proxy to BE for Flutter - fix
- Dynamic config loading order caused fail on start of application
- tas-1171 Broken export of case variables in TAS5
- tas-1155 Task instructions - redesign
- tas-1112 Overviews on a new TAS5 instance end with an error
- Send message to Flutter if invalid token on mobile device
- tas-1150 Unable to create user filters in service operations
- tas-1214 Organization/display of favorites in the dashboard container
- User photo in the header was not displayed on mobile devices
- tas-1173 TAS 5 - auto redirect to an external IAM from the main page
- tas-1191 TAS 5 - error messages after token expiration and logout
- tas-1212 Fixes in MS Graph API + CronJob
- Send 'authorized' message to Flutter on authorization

### Chore
- tas-1048 Remove phantomjs
- Clean old FCM code
- tas-1177 Remove bull-board deps
- tas-1178 update package systeminformation
- tas-1179 update package ini
- tas-1180 remove superagent + superagent-proxy
- tas-1195 refactor kodu na TS authorizationController
- tas-1185 refactor of competence controllers
- tas-1064-dependecies
- tas-1064 backend package.json cleanup
- tas-1186 refactor of DebuggerApi and CurlApi
- tas-1063 refactor of policies

## [5.2.3] - 2024-06-18
### Added
- tas-1113 - administration - alphabetically sorted
- tas-1106 Clicking on the TAS logo does nothing
- tas-1131 Ability to delete a document within the context of an active task in the Documents section
- tas-1132 Date picker component - mobile device
- tas-1153 mobile device - display of tasks

### Fixed
- tas-1041 Bulk download/delete documents
- tas-1014 Case overview - accent sensitivity on system variables
- tas-990 Profile photos migration - directory check
- tas-1156 dashboard favorites container  - scroll
- tas-1138 Side scroll case overview on mobile device
- tas-1152 Renaming favorites always renames the first one
- tas-1161 Filtering a date variable in overviews throws an error
- tas-1168 New Case button is visible even when hidden in configuration
- tas-1123 fix Workflow class import shadowing in Cron's runScheduledTasks function

## [5.2.2] - 2024-05-28
### Added
- tas-1124 - Firebase Cloud Messaging
- tas-1052 logs in task - admin
- tas-1041 bulk download/delete documents
- tas-1108 - ms-graph connector + consultant api
- tas-1109 - ms-graph cronJob
- tas-990 Profile photos stored in file system
- tas-1120 Hiding green buttons - side scroll
- tas-1119 Display of overviews - mobile version
- tas-1020 update DataGrid version 7.6.0
- tas-758 tabulky - při úpravě šířky sloupce se automaticky neroztáhne i šířka inline filtru
- tas-1028 Report unprocessed EWS mails
- tas-1147 CaseOverview components - add dataGrid

### Fixed
- tas-1118 Scroll side-side - mobile version
- tas-1116 Displaying documents - mobile version
- tas-1117 Document preview - mobile version
- SAML2 authority
- tas-1140 Migration - cron default configs
- tas-1141 Unable to load completed task when DR definition is changed by dynamic conditions

## Chore
- tas-1115 - Types - Workflow

## [5.2.1] - 2024-05-20
### Fixed
- SharepointApi - file field options fix
- tas-1072 Overviews - expert mode, react hook form
- tas-1074 Variables mapping in events timeout
- tas-1075 Overviews - search in overviews does not take into account translations
- tas-782 exclude current organization unit from list
- tas-1089 Attachments performance
- tas-1119 Display of overviews - mobile version


### Added
- tas-1078 - authentication and cookies for mobile app
- tas-1047 remake document preview
- tas-1043 - CSP headers with configs
- tas-1093 - CrossPlatform calls & identification of flutter app
### Chore
- tas-1069 delete old files, update libraries

## [5.2.0] - 2024-05-06

### Fixed
- Force Cookies for SSO
- Fix bug when sso fails on missing header
- fix wrong token format in pdf print
- tas-1018 Mobile app settings screen (styling fix)
- PhantomJs evaluate fix
- tas-975 Broken preview of doc/docx files within the document list variable
- tas-976 Document list shows a preview of a document even after it has been deleted
- tas-742 fix nav buttons - focusVisible not black
- tas-792 custom-view export - 2100 parameter limit on whereIn
- tas-1011 document upload - finished case
- tas-880 case - permissions - type
- tas-1055 administration - third header
- tas-892 system notifications - tasks report (edit)
- tas-981 edit no data/not found messages
- tas-1073 ews 'quoted-printable' encoding
- tas-712 Refactored statuses in cron run history (stuck on running)
- tas-995 tasks and cases order by iproc_id desc, removed default order by iproc_actual_start_date + dashboard tasks table allow to set order to undefined
- tas-736 task button on the dashboard gets cut off when scrolling

### Added
-   tas-1031 Preview of unsupported documents
### Chore
-   tas-901 Pre-commit hooks
-   tas-986 Remove Babel

## [5.1.4] - 2024-06-17

### Fixed

- tas-1134 TAS 5 - events - creating a new event

## [5.1.3] - 2024-05-30

### Fixed

- tas-1099 manuals tab doesn't work

## [5.1.2] - 2024-05-15

### Fixed

- tas-1068 - Error while saving a new template - modal

## [5.1.1] - 2024-05-06

### Added

-   tas-1040 - Config to hide 'TAS 5.0 is here...' in header

## [5.1.0] - 2024-05-03

### Fixed

-   tas-794 - Attachment performance
-   tas-909 - Incorrect login redirect using AAD
-   tas-966 - Fix display of red alerts
-   tas-937 ES6 transpilation - chaining .map
-   tas-900 Tighten linting rules
-   Remove Bluebird package
-   tas-981 - Remove phrase "We are sorry"
-   tas-893 Rename and move cron reset button
-   tas-889 Dashboard - expanding reports columns
-   tas-964 creating new overview - missing language mutations
-   tas-799 mapping variables in template task solver/variables/completion tab
-   tas-760 print case - preselected value of print template by priority
-   tas-1005 return screen after task completion
-   tas-1009 overviews - display of numbers
-   tas-715 fixed lib.addStaticRights to work with arrays
-   tas-1016 Remove the "Add Overview" button on mobile devices
-   tas-1021 datagrid header filter operators for string column, fix english language mutation
-   tas-761 improved and updated datePicker
-   tas-890 fix scroll top arrow button in TAS5
-   tas-907 Monaco hints - generate defaults
-   tas-1017 Logging object from lib.xmlToJson
-   tas-1013 Filtering "starts with"
-   tas-996 fixed template import, conflicts when importing sections
-   tas-1023 DataGrid loading rows
-   tas-1024 Scale 90%

### Added

-   tas-542 - debug messages order change
-   tas-630 - Total count for Logs
-   tas-1002 bulk action buttons for data grid
-   tas-740 - schema and logo - points for improvement
-   tas-753 - login page news - connect with newsletter
-   tas-741 - Folders in overviews and the entire overview selection on mobile devices
-   tas-1015 Sequential dashboard loading
-   tas-731 - Added upload limit for bodyParser into frontend config
-   tas-717 - Added handover information into task history
-   tas-714 - Added cookies authorization
-   tas-1018 Mobile app settings screen
-   tas-960 Graphically separate overview ownership
-   tas-1007 Task - task ID in history
-   tas-994 clustered primary key on instance_process_versions

## [5.0.18] - 2024-04-09

### Fixed

-   tas-732 - CSV import fix (bad encoding)
-   tas-801 ES6 transpilation - Class method - Constructor can't be an async method
-   tsec values fix
-   fix link to task in new task email
-   minor backend upgrades & linting
-   t3b-2911 variable name update in ES6
-   t3b-2915 ES6 transpilation - Array methods callback
-   tas-853 mail queue desc order
-   tas-735 table - filters - text - missing operator "Does not contain", "Not equal to"
-   tas-749 selectbox component with multichoice - missing tooltip
-   tas-803 overviews - subscribe
-   tas-831 dynamic conditions - this.references returns {}
-   tas-847 task refresh
-   tas-856 table column widths - autofit
-   tas-874 narrowed left menu - click through to administration
-   DocumentsPopover - skeleton below
-   Datepicker dateWithoutTime fix
-   fix Monaco Editor in template task modal
-   fix nav menu arrow/version container blocking menu items on hover

### Added

-   tas-542 multilang debug error messages
-   tas-733 e-mail notification - default
-   tas-739 save table column width settings
-   tas-745 document upload - be able to drag&drop into the document roll-up/modal

## [5.0.17] - 2024-04-05

### Fixed

-   tas-729 ES6 transpilation - value assignment
-   t3f-1683 ES6 transpilation - function constructors
-   migration - check if FK exists
-   px to rem
-   fix daily report email subject
-   fix searching and expanding folders in Overviews and Documents
-   make use view columns draggable in ModalEditOverview
-   tas-737 + pro dynamické řádky v dark mode - neviditelné
-   t3f-1671 práce s novým visual code studiem
-   tas-749 komponenta selectbox s multichoice - chybí tooltip
-   tas-763 dynamické řádky - názvy sloupců
-   tas-755 dopracovat instrukce k úkolu
-   replace search placeholders in DataGridCards, DocumentsAll
-   don't hide header icons on mobile in Manuals, Reports, Admin Menu, User Settings
-   fix old table horizontal scroll overflowing on xs/md size screens
-   dataGrid column max width

### Added

-   tas-740 - barevné schéma a loga
-   tas-659 tabulky - pokud kliknu na rychlý filtr, po 2. kliknutí na rychlý filtr, odmažou se všechny filtr, tzn. budeme duplikovat v tomto případě tlačítko "Zrušit filtry"
    t3f-1671 pokud dám "Uložit" není nutné abych v modálu musela potvrdit, že změny chci uložit
-   tas-659 zástup - "datum do", vybrat nejdřívější datum podle "datum od" (viz např. proces nepřítomnosti na neitu)
    t3f-1684 Přehledy - tlačítko pro editování
    tas-659 - v případě více než 5 dokumentů zobrazovat seznam dokumentů v selectboxu, FileUpload refactoring
-   add search to manuals
-   není možný horizontální scroll v DT (starý design)

## [5.0.16] - 2024-03-20

### Fixed

-   t3b-2918 oracle db migration exception
-   overview on dashboard - translations of lov values fix
-   hide service operations card in administration menu if not super admin
-   fix - LT readonly value in dynamic conditions
-   fix - triggering dynamic conditions in ModalTaskDetail
-   tas-659 - oprava input multiselectu (select multiple)
-   tas-659 - date component on mobile - the entire calendar is not visible (if the phone is rotated horizontally, the selected value overlaps the calendar)
-   event modal - if the selected event contains a wysiwyg editor, the modal needs to be expanded further
-   tas-659 text highlighting - have a default color
-   The inner indentation of the dropdown should be 12px from left to right and 8px from top to bottom, but currently it is 8px from all sides.
-   px to rem
-   tas-659 mobile devices - case - missing "Logs"
-   tas-659 missing display of current version of TAS in the left expanded menu
-   tas-659 document preview in "Read-only" state on task according to ... meaning not having the document upload component at the top
-   tas-659 system emails - the spacing between the button does not match Figma on PC
-   t3b-2906 - fix for template graph
-   tas-659 - make task detail buttons always active
-   tas-659 In the table on the Dashboard, column header titles and text in column cells are not properly aligned.
-   fix - closePrevPath when taking a task from a case
-   administration menu - the favorites menu should be on hover instead of click, I want to go directly to the entire administration on click
-   translate competence rule name col in user settings

### Added

-   language mutation for copy overview
-   Task modal, for tasks where I am not the assignee - in the case of a subprocess or a scheduled task, I cannot have the options "Take Over" and "Hand Over", for a subprocess, I would expect the "eye" to take me to the related case.
-   for the subscription task, redesign the modal so that it is possible to only open the task, harmonize taskId types
-   org. structure dataGrid

## [5.0.15] - 2024-03-14

### Fixed

-   t3b-2918 oracle db migration exception

## [5.0.14] - 2024-03-18

### Added

-   t3b-2919 Switched base image to Debian 12

## [5.0.13] - 2024-03-18

### Added

-   t3b-2919 evalMathApi generateAsymmeticKeyPair method

### Fixed

-   t3b-2918 config repository saveConfig method store encrypted values

## [5.0.12] - 2024-03-14

### Fixed

-   tas-720 DmsController.uploadFileJson - generate unique file names using tmp library
-   tas-727 Broken saml auth module with @node-saml/passport-saml 4.0.3
-   tas-659 - redesign of email templates
-   tas-659 - opening the task after taking over
-   tas-659 - modal task detail buttons for mobile layout
-   tas-659 - block padding adjustment
-   tas-659 - mobile device - bell and favorites overlap with the user's name
-   tas-659 - missing "Completed" task tag

### Added

-   t3b-2906 Sections with variables in tasks
-   tas-659 - copying overview with language mutations
-   tas-659 - dataGrid column width depend on content
-   overview modal for add new overview fill name overview input when I choose copy cv and select exist overview

## [5.0.11] - 2024-03-11

### Fixed

-   fix tas-713 transpilation of synchronous calculation code
-   tas-659 - actual tasks - if there are more than 100 tasks on the case
-   tas-659 - overlay tooltip error
-   administration - different link to documentation for CZ and other languages
-   t3f-1671 the close button does not have confirmation
-   t3f-1671 UI - help icon overlaps the "Just Save" button
-   fix css editor in template print

### Added

-   t3b-2899 Calculation to set DMS_FILE_TAG value
-   split login page to login and authenticate route
-   add config for showNews on login page
-   version link to changelog in wiki
-   tas-659 - textarea option to expand in height
-   tas-659 - document list - sort from newest to oldest
-   tas-659 - document list - "eye" icon to display revision
-   modal for tasks where I am not the assignee - buttons "Open Task", "Take Over", and "Hand Over" should be secondary buttons

## [5.0.10] - 2024-03-07

### Fixed

-   fix tas-713 transpilation of synchronous calculation code

## [5.0.9] - 2024-03-06

### Added

-   color theme
-   add TeamAssistant logo in svg and change color of logo depend on color theme
-   add broadcrumb to administration sections

### Fixed

-   fix ModalDocuments filter by folder
-   fix height new case button in sideBar nav menu
-   t3f-1671 working with new Visual Studio Code study
-   fix localStorage darkMode

## [5.0.8] - 2024-03-04

### Added

-   overviews - add modal for new overview
-   administration redesign
-   Displaying a task that is assigned to another user - let's add an active button for Take Over and Hand Over to the modal
-   fix error in event modal

## [5.0.7] - 2024-02-29

### Fixed

-   t3f-1674 case overview - print dt multichoice
-   tas-659 saving table view (compact, standard, ...)
-   tas-659 overviews - tree structure on a mobile device
-   tas-659 opening a case/task/etc. via the right mouse button/mouse wheel
-   tas-659 Firefox bug
-   task variable selectors
-   task, case - buttons id
-   dynamic conditions - dataStructureRenderer
-   incorrect redirection when creating new case without task

## [5.0.6] - 2024-02-27

### Fixed

-   tas-659 template task - fixed search in calculations
-   document title correction
-   dynamic conditions fix, unnecessary variable gap height
-   event modal - correction of variables
-   case variable modal - error when ivar_value is not string

## [5.0.5] - 2024-02-23

### Added

-   tas-659 administration - edit left sidebar main menu
-   tas-659 documents - link to case from document witch belong to case
-   tas-659 dataGrid - density save to localStorage
-   tas-659 change tooltip in dataGrid (system tooltip -> tas tooltip)
-   tas-659 if Iam not owner of overview, I can not modify the overview
-   tas-659 documents - d&d to document dropDown (roll-up)

### Fixed

-   tas-659 dataGrid - header filtering
-   tas-659 dynamicTable - export button is hide when i have a lot of columns in DT
-   tas-659 oldTables - set width of columns does not work
-   tas-659 logs - copy of logs does not work
-   tas-659 cases - change variable in administration GUI does not work
-   tas-659 profileSettings - permissions for profile
-   tas-659 dataGrid - empty rows

## [5.0.4] - 2024-02-21

### Fixed

-   overview expert mode filtering when choose date type column
-   tas-659 dataGrid unique rows id in dashboard overview
-   tas-659 overview expert mod save db view option
-   delete document button visibility

## [5.0.3] - 2024-02-20

### Fixed

-   tas-659 dataGrid unique rows id in overview
-   tas-659 dataGrid sorting
-   tas-659 dataGrid expert mode filtering

## [5.0.2] - 2024-02-19

### Added

-   tas-689 overview tree default open all section and favorite section depend on length of items

### changed

-   remove console logs

### Fixed

-   tas-659 save custom filters in overview dataGrid
-   cases rewrite variable in administration GUI

## [5.0.1] - 2024-02-19

### Added

-   tas-689 dataGrid date header filter, change operator when i click to shortcuts
-   tas-689 add new reports interface
-   tas-689 save custom filters to database
-   tas-689 dashboard overview table new design
-   tas-689 add overview id to url

### changed

-   tas-659 hide layout header search panel

### Fixed

-   tas-659 overview config mod add columns

## [5.0.0] - 2024-02-14

### Changed

-   User interface redesign

### Fixed

-   t3b-2246 Dynamic rights for managers

## [4.15.99] - 2024-02-16

### Fixed

-   tas-1566 - fix template task mass mapping

## [4.15.98] - 2024-02-14

### Fixed

-   tas-706 - Clear AD competences from locked users after AD sync

## [4.15.97] - 2024-02-14

### Fixed

-   tas-707 - Showing duplicate users in role - users allocation

## [4.15.96] - 2024-02-07

### Fixed

-   tas-704 - Oracle DB invalid query identifier - fix for t3b-2888

## [4.15.95] - 2024-02-06

### Fixed

-   t3b-2891 More understandable error message from events

## [4.15.94] - 2024-02-01

### Fixed

-   tas-678 - Fixed - Cron.js timeout when case owner is LOCKED

## [4.15.93] - 2024-02-01

### Fixed

-   t3b-2889 Fixed - 'Copy roles from' copied also AD competences

## [4.15.92] - 2024-02-01

### Fixed

-   t3b-2888 Fixed - CompetenceGeneratorCron.js generating roles for locked users

## [4.15.91] - 2024-01-22

### Fixed

-   t3b-2885 Fixed - Changes in competence rules temporarily broke existing competences

## [4.15.90] - 2024-01-22

### Fixed

-   t3b-2884 Fixed duplicates in tasks to pull

## [4.15.89] - 2024-01-19

### Added

-   t3f-1658 Copy row - Dynamic tables

## [4.15.88] - 2024-01-17

### Added

-   tas-689 Sharepoint download file

## [4.15.87] - 2024-01-08

### Added

-   t3b-2784 Curl Api multipar/form-data builder

## [4.15.86] - 2024-01-04

### Fixed

-   t3b-2880 Fixed CompetenceGeneratorCron.js filling pool

## [4.15.85] - 2024-01-03

### Fixed

-   t3b-2879 Fixed the inability to create new competence rules

## [4.15.84] - 2023-12-20

### Fixed

-   tas-669 security weakness, user enumeration

## [4.15.83] - 2023-12-14

### Added

-   tas-668 SFTP protocol in CurlApi

## [4.15.82] - 2023-12-14

### Fixed

-   t3b-2873 zip.unzipDmsFile(x) does not work with lib.copySharedFileLink files

## [4.15.81] - 2023-12-14

### Fixed

-   tas-679 invalid token for Administrator between updates

## [4.15.80] - 2023-12-13

### Fixed

-   tas-677 undici library rollback

## [4.15.79] - 2023-12-11

### Fixed

-   tas-675 Dynamic configuration for workaround t3b-2656

## [4.15.78] - 2023-12-07

### Fixed

-   t3b-2851 Mark competences for rebuild after rule change

## [4.15.77] - 2023-11-28

### Fixed

-   t3f-1653 Fix formatting of decimal places on number type inputs for some languages

## [4.15.76] - 2023-11-22

### Changed

-   ops-51 Added MS fonts to the Docker image

## [4.15.75] - 2023-11-22

### Fixed

-   t3b-2848 Service operations no longer limited by defaultRequestItemLimit

## [4.15.74] - 2023-11-20

### Fixed

-   ops-49 fixed error on startup 'Callback was already called.'

## [4.15.73] - 2023-11-16

### Fixed

-   tas-665 authentication token is valid across versions

## [4.15.72] - 2023-11-14

### Fixed

-   t3b-2656 Incomprehensibly stuck case workflow for old (non templated processes)

## [4.15.71] - 2023-11-09

### Fixed

-   t3b-2656 Incomprehensibly stuck case workflow

## [4.15.70] - 2023-11-08

### Fixed

-   fixed obfuscation script

## [4.15.69] - 2023-11-08

### Changed

-   tas-660 role_category definition for DTRoleSync cron

## [4.15.68] - 2023-11-07

### Changed

-   tas-658 Limitation of console logs

## [4.15.67] - 2023-11-03

### Changed

-   Swaped temp libraries in LO.js

## [4.15.66] - 2023-11-03

### Changed

-   t3b-2840 Extended login logs and config option

## [4.15.65] - 2023-11-02

### Changed

-   tas-656 Limitation of migration script logs

## [4.15.64] - 2023-10-30

### Fixed

-   t3f-1648 docx file preview - fix of error display

## [4.15.63] - 2023-10-27

### Changed

-   Refactor of slim image creation

## [4.15.62] - 2023-10-24

### Fixed

-   tas-653 soap/storeCsv does not work

## [4.15.61] - 2023-10-18

### Fixed

-   tas-648 temp is allowed for dms

## [4.15.60] - 2023-10-17

### Fixed

-   javascript-obfuscator library upgrade

## [4.15.59] - 2023-10-17

### Fixed

-   tag fixed

## [4.15.58] - 2023-10-17

### Fixed

-   tas-648 temp is allowed for dms
    tas-647 temp paths.js fixed

## [4.15.57] - 2023-10-17

### Fixed

-   tas-641 Attachment rights assessment from sub/master processes

## [4.15.56] - 2023-10-13

### Fixed

-   tas-646 Redis connection

## [4.15.55] - 2023-10-13

### Removed

-   t3b-2834 Broken HealthStatus activeSessions removed from default configuration for now

## [4.15.54] - 2023-10-12

### Fixed

-   t3b-2833 Fix Docker image build for 4.15

## [4.15.53] - 2023-10-11

### Changed

-   Increased authentication token expiration time.

## [4.15.52] - 2023-10-11

### Fixed

-   t3b-2828 storage.getTmpLibrary() is now using path.tmp configuration

## [4.15.51] - 2023-10-05

### Fixed

-   tas-642 Csv upload route order

## [4.15.50] - 2023-09-29

### Fixed

-   tas-629 synchronisation script fix

## [4.15.49] - 2023-09-22

### Fixed

-   t3b-2825 workaround for PhantomJS in bullseye container

## [4.15.48] - 2023-09-21

### Fixed

-   t3b-2823 remove leftovers from image build

## [4.15.47] - 2023-09-21

### Fixed

-   pagination does not work in documents

## [4.15.46] - 2023-09-21

### Fixed

-   tas-635 dt.orderBy DTV_ORDER_INDEX does not work

## [4.15.45] - 2023-09-20

### Fixed

-   t3b-2804 Process summary translated in overview

## [4.15.44] - 2023-09-11

### Changed

-   tas-628 changed version of docxtemplater to support processLineBreaksAsNewText

## [4.15.43] - 2023-09-07

### Added

-   tas-627 Endless cycle of EWS

## [4.15.42] - 2023-09-07

### Fixed

-   t3f-381 Task queue to solve - fix case activation error after page refresh

## [4.15.41] - 2023-09-06

### Fixed

-   NIN-16 Dynamic table public mode can not be updated (saved)

## [4.15.40] - 2023-09-06

### Added

-   revert getPdfFileAnnotations removal t3b-2757

## [4.15.39] - 2023-09-05

### Added

-   tas-624 lib.csvToJson has changed its default value for flatKeys from false to true

## [4.15.38] - 2023-09-04

### Added

-   t3b-2809 backport docker build to 4.15

## [4.15.37] - 2023-08-31

### Fixed

fix - affecting TAS styles from ckeditor

## [4.15.36] - 2023-08-08

### Added

-   t3b-2805 req object available in authorization flows

## [4.15.35] - 2023-08-08

### Fixed

t3b-2807 Added function storeAttachmentAsRevision

## [4.15.34] - 2023-07-27

### Fixed

t3f-1620 Clicking on Change entity from a form ends with a white screen

## [4.15.33] - 2023-07-21

### Fixed

t3b-2575 Redirect after login

## [4.15.32] - 2023-07-19

### Fixed

t3b-2778 process summary is not updated (in dashboard cases)

## [4.15.31] - 2023-07-19

### Fixed

tas-612 Bad redirection back to the login screen after login

## [4.15.30]

### Fixed

t3b-2794 [Calculations] `storage.setDmsFileFolder` returns better error when the file cannot be found

## [4.15.29] - 2023-07-18

### Fixed

t3b-2791 When moving multiple tasks in a graph using lasso, some attributes may be overwritten

## [4.15.28] - 2023-07-17

### Fixed

t3b-2793 [Knex] Rollback varchar columns conversion for user sensitive columns

## [4.15.27] - 2023-07-14

### Fixed

t3f-1610 Fix for tracking changes between dynamic rows

## [4.15.26] - 2023-07-10

### Fixed

t3f-1610 Tracking changes between dynamic rows

## [4.15.25] - 2023-07-03

### Fixed

t3b-2789 non-text LOVs are now fetched correctly

## [4.15.24] - 2023-07-03

### Fixed

t3b-2788 Route `/api/status/db` now responds correctly

## [4.15.23] - 2023-06-29

### Changed

t3f-1522 Blocking time intervals for days of the week

## [4.15.22] - 2023-06-21

### Fixed

t3f-1522 Option to enter midnight in the calendar range

## [4.15.21] - 2023-06-20

### Fixed

t3b-2783 Unwanted cache error log in WorkFlow

## [4.15.20] - 2023-06-19

### Fixed

t3b-2781 proc.warn does not work with Crons engine

## [4.15.19] - 2023-06-19

### Fixed

t3b-2778 process summary is not updated

## [4.15.17] - 2023-06-15

### Fixed

t3b-2780 logoutParams attribute in auth modules now works as expected
t3b-2776 Performance optimization in Variable sandbox initialization

## [4.15.16] - 2023-06-12

### Added

tas-607 Hand event loading optimisation

## [4.15.15] - 2023-06-12

### Removed

-   t3b-2773 It's no longer mandatory (nor desirable) to have Redis JSON installed in order to use Redis cache

## [4.15.14] - 2023-06-12

### Fixed

-   t3b-2763 Fix filters in service operations

## [4.15.13] - 2023-06-09

### Fixed

-   t3b-2620 assets folder configuration fix

## [4.15.12] - 2023-06-09

### Fixed

-   t3b-2769 Usage statistics report timezone fix

## [4.15.11] - 2023-06-09

### Fixed

-   t3b-2772 [EWS] Duplicate filenames overwrite in `.tmp`

## [4.15.10] - 2023-06-08

### Fixed

-   t3b-2771 Undesired async behavior in sandbox setup

## [4.15.9] - 2023-06-07

### Fixed

-   t3b-2703 Keep the correct mutation text value of LOVs in the sandbox

## [4.15.8] - 2023-06-07

### Fixed

-   t3f-1602 Add ID column to org. structure screen

## [4.15.7] - 2023-06-05

### Fixed

-   t3f-1533 Rounding of chart values in reports

## [4.15.6] - 2023-06-02

### Fixed

-   t3b-2764 Task rescheduling updated non selected tasks variables

## [4.15.5] - 2023-06-02

### Changed

-   t3f-1522 Change requirements for calendar functionality (addition)

## [4.15.4] - 2023-06-01

### Fixed

-   t3b-2766 Invalid maintenance status on Organization updates

## [4.15.3] - 2023-06-01

### Added

-   t3b-2669 Extended attributes allowed in AzureAD auth module

## [4.15.2] - 2023-05-30

### Fixed

-   t3b-2760 Active queries monitoring now correctly reports Cron queries on non-scaled instances
-   t3b-2762 EWS NTLM auth backwards compatibility

## [4.15.1] - 2023-05-29

### Changed

-   t3f-1522 Change requirements for calendar functionality

## [4.15.0] - 2023-04-14

### Removed

-   t3b-2369 Windows SSO (NTLM\Kerberos) The current SSO implementation has been reworked and needs to be set up manually as a plugin

### Fixed

-   t3b-2731 [Calculations] Fixed lib.copySharedFileLink duplicates
-   t3b-2672 package dependencies' security updates
-   t3b-2020 StorageApi did not allow files from sub/master process
-   t3b-2621 Incorrect descritpion for GROUP_NAME column on MSSQL
-   t3b-2633 Competences - findings, suggestions for improvement
-   t3b-2667 Limit for scheduled tasks to 1000 records
-   t3b-2673 Value CRON_START not updated
-   t3b-2677 Button "Complete" is not visible for next task if task is multi-instance
-   t3b-2681 Findings 4.14 - collection ticket
-   t3b-2687 Event "CSV variable update from DMS attachment"
-   t3b-2694 Competence is not deleted with competence rule deletion
-   t3b-2695 if role is removed from competence rule it is not removed from competence itself
-   t3b-2696 Competence is not deleted only disabled - changed
-   t3b-2701 Competence rules not working on Oracle DB
-   t3b-2705 lib.libreConvert did not work with encrypted files
-   t3b-2369 Windows SSO (NTLM\Kerberos) improvements
-   t3f-1578 Fix option list restriction via varDefOn for multichoice variable (LOV)
-   t3f-1573 DR default value while copying
-   t3f-1588 New Variable is not multi-instance by default

### Added

-   t3b-2716 Added calc. examples configuration
-   tas-541 Distributed application cache using RedisJSON module
-   t3b-1842 Remove global language in settings
-   t3b-2143 Edit overview by non-owner user
-   t3b-2205 Get instance link via calculations
-   t3b-2285 Limit number of users in role
-   t3b-2372 Insert planned and failed tasks to cron next run
-   t3b-2464 UserApi, VariableApi toString in debug.log
-   t3b-2573 Servis operations for administrator
-   t3b-2608 Copy variable in templete
-   t3b-2615 API for overview returns variable names mapping
-   t3b-2620 Configure the path for non-source files (cache, logs, ..)
-   t3b-2652 Synch script - expansion
-   t3b-2658 XMLProcessImport_clone
-   t3b-2670 AzureAD with SSO
-   t3b-2685 Translate iproc_summary
-   t3f-1523 Evaluation of the task solver in the template
-   t3f-1546 Single/mass changes in tasks and case entity
-   t3f-1566 Template tab solver/variables/finish - redesign
-   t3f-1568 View of DT variable multichoice on tablet
-   t3b-2119 Request to change password per defined admin period
-   t3b-2562 Unique constraint USR_PARAM for user
-   t3b-2690 IT.once doe not implement async callback
-   t3f-1522 Interactive calendar
-   t3f-1539 Check user settings with $Administrator or $SuperAdministrator role
-   t3f-1541 Accidentaly deleted roles while new user creation
-   t3f-1564 Limited color scheme for reports
-   tas-597 LibreOffice additional logs
-   t3b-2744 Administrator's DB Connection utility has extended metadata
-   t3b-2736 Administrator's DB Connection utility now works with ORA DB

## [4.14.0] - 2022-01-27

### Fixed

-   t3f-1504 Option to disallow change password (for ex. in case of external identity authority).
-   t3f-1454 'AllowedExternalSources' is readable in administration.
-   t3b-2367 More parameters for background DMS indexing.
-   t3b-2592 Searching DMS without tags invokes error
-   t3b-1878 Reduction logging of check completition
-   t3b-2583 Auto-create Elasticsearch index for fulltext search
-   t3b-2584 Not completing tasks with an error without logging error and warning message
-   t3f-1499, t3f-1503 Invoking scripts in DynamicRows of previous task while new process is created
-   t3b-2574 EWS missing CONTENT-DISPOSITION
-   t3b-2610 Error while using dms.cronIndexing
-   t3b-2618 Changing variable with included changes to existing varibles
-   tas-569 Converting into PDF (based on React) is now more reliable

### Added

-   t3b-1696 [Calculations] New crypto methods lib.md5(), lib.sha1(), lib.sha256(), lib.sha512().
-   t3b-2603 Added Sharepoint addin authentication
-   t3f-1482 Total count of current tasks
-   t3f-1460 Role detail with export, search
-   t3f-381 Task queue to solve
-   t3f-1469 Positive and negative filtration of logs
-   t3f-1516 Security option that allows use of session storage

## Changed

-   t3b-1842 - Unification of client languages parameters

## [4.13.1] - 2023-02-15

### Added

-   t3b-2635 New configurations to control and monitor the Cron process more transparently

### Fixed

-   tas-578 Fix language mutations in email notifications
-   t3f-1513 Increased the resolution of language flags
-   t3b-2640 Reduced the default value of concurrent calls to a mail server
-   t3b-2636 Fixed the visibility rights of subprocesses in the Documents tab
-   t3b-2644 Fixed PdfParser non-responsive edge case in [Calculations]
-   t3b-2648 Fixed searching in DMS without DMS folder tag included

## [4.13.0] - 2022-11-22

### Fixed

-   t3f-1456 Filtering on case status (with language mutations)
-   t3f-1450 Template name is not displayed while importing a template
-   t3f-1478 Overviews - sharing permissions are not stored
-   t3f-1489 DynamicRows do not respect the set text color via customizing
-   t3f-1491 Adding a column in DT sometimes ends with white screen
-   t3f-1496 Exporting variables to csv/excel puts wrong Default variable name
-   t3b-2427 Loading tasks into Dashboard - gets stuck on 10 new tasks
-   t3f-1488 Calendar sometimes overlays the date imput itself
-   t3f-1486 Display business variables only in filters in reports for normal users
-   t3f-1484 Checkbox Email notification for new task is unchecked after refresh
-   t3f-1477 Button "Enter" in DynamicRows of the textarea type does not insert a new line after the text
-   t3b-2505 Editing 1000+ roles for user
-   t3b-2527 Fix of assigning a task to any user
-   t3b-2510 Shredding - physically deleting a file
-   t3b-2492 EWS - Mail extraction and invalid attachment format
-   t3b-2530 Filtering numbers in Overviews
-   t3b-2575 Redirecting after AD login clicking on a link from an email
-   t3b-2567 lib.jsonSumArray returns valid sum in everytime
-   t3f-1497 DMS removes the comma from the file name while uploading, but it remains in DynamicRows
-   t3f-1501 DynamicConditions don't recalculate after taking task
-   t3f-1502 Detail of history task doesn't open
-   t3f-1509 Running DynamicConditions in finished tasks

### Added

-   Importing variables from EnterpriseAssistant
-   t3b-2378 New Graph reports - Statistics
-   t3f-1438 Translations for Favorites in Dashboard
-   t3b-2476 Optional diacritic suppression when filtering
-   t3f-1442 Status of "Scheduled Task" included time
-   t3f-1449 DynamicRows - conditional copying of lines
-   Configuration for hiding search button
-   t3f-1444 New Button to take over task in detail of task
-   t3f-1495 Removing the sorting option in some columns - configurable
-   t3f-1316 Detection of preferred color theme
-   t3b-2378 New Graphs data source (renamed to Statistics also)
-   t3b-2515 Supporting MS Sharepoint 365
-   t3b-2511 Logging of shredding
-   t3b-2324 Added calclucation lib.removeStaticRightsUser
-   t3b-2176 Updating filtered rows in DynamicTable
-   t3b-2547 Shredding physically deletes case with all dependencies
-   t3b-2372 Option to rescheduling errored tasks
-   t3b-2418 Option EWS processing email without storing it to TAS
-   t3b-2539 Add option to delete DMS files to role based on headers
-   t3b-1303 EWS chaining emails
-   t3f-1470 DynamicRows can change colors of columns.

### Changed

-   t3f-1366 Refactored table slider
-   tas-547 HR - changing solver for more templates
-   t3f-1467 Fulltext search in searchbox starting with 2 characters
-   t3f-1498 Remove the thousand separator in tables ID columns
-   tas-578 New template of email notifications
-   t3b-2533 EWS default crons starting time
-   t3b-2551 Global search omits big texts
-   t3b-2544 Maximum subject length of emails increased to 250 chars
-   t3b-2230 [Calculations] lib.getSharedVariables allows filtering by name

### Performance

-   t3b-2465 Optimization HR agenda
-   t3b-2461 Cancellation of unnecessary requests
-   t3f-1420 [Forms] Unnecessary request in file Variable

### Security

-   t3f-1472 Minor - Dashboard fix
-   t3f-1474 Minor security fix
-   t3b-2546 Enable/disable sanitize export to CSV

## [4.12.7] - 2022-12-19

### Fixed

-   t3b-2598 Big logs can not be downloaded

## [4.12.6] - 2022-12-16

### Fixed

-   t3b-2602 Fixed competences GUI issue on Oracle instances

## [4.12.5] - 2022-12-15

### Fixed

-   Custom role synchronisation fix

## [4.12.4] - 2022-12-14

### Fixed

-   t3b-2599 Manipulation with Cron clones

## [4.12.3] - 2022-12-12

### Fixed

-   IsLoggedIn transaction issues
-   Searching in DMS documents with invalid tags, use org. default tags

### Added

-   t3f-1513 Language flag icons & support for Italian language

## [4.12.0] - 2022-08-24

### Fixed

-   t3b-2046 Missing translation of DMS section (logical document)
-   t3f-1377 Suggestion of variables in link conditions
-   t3b-2395 [Calculations] Lib.getFileContents/lib.storeAttachment doesn't respect encryption
-   t3b-2411 Error while copying roles from user to another user
-   t3b-2424 Importing processes via events (csv) in encrypted mode doesn't work
-   t3b-2440 Improved calculating start date and term of task
-   t3b-2438 Competence rule doesn't work after editing
-   t3b-2477 [Emails] Tasks notifications are sending with wrong header
-   t3b-2485 [Prints] Output for print doesn't show variable
-   t3b-2455 Order of variable LOV is not persist
-   t3b-2357 Better dealing of EWS with errors
-   t3b-2505 Editing users with more than 1000 roles
-   t3f-1395 Better status of finishing task in table
-   t3f-1393 Broken styles in window with favorites
-   t3f-1390 History of process - order of tasks by finishing date
-   t3f-1401 Take selected folder from tree when uploading file
-   t3f-1408 Login button can be multiple clicked
-   t3f-1412 Window of authentication detail doesn't load directives properly
-   t3f-1415 Tasks don't show up in dashboard calendar
-   t3f-1414 Order of documents in case by date of insert
-   t3f-1425 [BPMN] Links between tasks in diagram is not movable
-   t3f-1429 [Forms] Aligning numbers in tasks
-   t3f-1440 Bad syntax resolving in CodeEditor
-   t3f-1252 Renaming of cloned Crons

### Added

-   t3b-2532 Generování BarCode/QRCode + jeho vložení do pdf
-   t3b-2381 Competences
-   t3b-2379 Custom user attribute
-   t3f-1388 Table of outgoing emails
-   t3f-1375 Configuration of main menu
-   t3b-2413 Option to set AD atributes before synchronization
-   t3b-2406 XMLProcessImport - new GUI, logging, estimate importing processes
-   t3b-2431 [AD] Recursive synchronisation of AD memberOf
-   t3b-2476 Option to disable diacritics when filtering
-   t3b-2399 Run hisotry of crons
-   t3f-1407 New button in Performance logs to move directly into related case
-   t3f-1398 [BPMN] More detail informations in workflow diagrams

## [4.11.0] - 2022-04-15

### Fixed

-   t3f-1295 Better management of favorites
-   t3f-1347 Vložení nového úkolu do grafu prebíra linky
-   t3f-1042 DynamicRows changeVarVal fixed coexisting with DynamicConditions
-   t3f-1350 Prekladani TASu browserem - èásteèné potlaèení
-   t3b-2325 Security fixes
-   t3f-1355 Changing visibility of DynamicRows column with DynamicConditions
-   t3f-1363 Dark mode sometimes invokes black screen
-   t3f-1351 DynamicRows failed request of its subpages
-   t3f-1356 Changing variable value of case can't be empty
-   t3f-1365 Fixed scrollbar
-   t3f-1361 Logo for dark-mode
-   t3f-1301 Blank multibox after refresh /settings/dms-columns
-   t3f-1299 Doesn't refresh values after save edited event
-   t3f-1369 [Forms] Showing empty DynamicRows
-   t3f-1342 List of attachements - fixed margin
-   t3b-1757 Inactive delete button in Templates
-   t3f-1379 Clickable avatar
-   t3f-1381 Preview of new file revision
-   t3f-1378 Required item for iteration over variable
-   t3f-1371 [Templates] Global find optimalisation
-   t3f-1190 DynamicConditions don't reflect current values
-   t3f-1020 [Templates] Separate variable - checkbox
-   t3b-2348 Notification of locked users are disabled
-   t3b-2364 Right count of indexed documents if there are none
-   t3b-2375 Searching in vice list
-   t3b-2374 Copying role
-   t3b-2377 Error while deleting DynamicTable
-   t3b-2384 Slow DynamicTable with big history
-   t3b-2409 Crash while saving print
-   t3f-1399 Proper previews of multi-layer PDFs
-   tas-531 Option to change DynamicRows width
-   t3f-1451 WatchVariables() doesn't work with all variables
-   t3f-1380 Fixed variable DynamicTable multichoice - removing more items at once
-   t3f-1417 Admin can't see hidden files in Public Files
-   t3b-2526 Authenticate response doesn't show valid expired times
-   t3b-2527 Security fix - assigning user
-   t3b-1117 Statistics of solving tasks - bug
-   t3b-2525 Security fix
-   t3b-2528 Global search security fix

### Added

-   t3f-1322 Graph improvements
-   t3f-1348 Preview in documentation
-   t3f-1360 Maintanance end time on login page
-   t3f-1358 ID of DynamicTable in list
-   t3f-1359 Changing DynamicTable column name
-   t3f-1362 Customizing color-scheme of email notifications
-   t3b-1522 Managing certificates via GUI
-   t3b-2267 [Templates] Filtering option for last or previous/next months
-   t3f-1383 Option to hide delete buttons for DynamicRows
-   t3b-2335 Resetting sequences via GUI
-   t3b-2356 Option to hide user, roles, org.structures for other users based on rights
-   t3b-2359 Documents shredding
-   t3b-2362 [Calculations] ParseHtml to parse html content
-   t3b-2367 DmsFileIndexingCron has more options
-   t3b-2371 [Calculations] Generating DynamicRows into docx
-   t3b-2410 EWS supports email with signature
-   t3b-2445 EWS supports S/MIME security atachments
-   t3b-2504 Added ElasticSearch 8+ compatibility
-   t3f-1392 Variable Date with support of future dates
-   t3f-1435 Scheme configuration allows change color of loading spinner
-   t3f-1453 Downloading file directly in its preview

## [4.10.1] - 2022-05-11

### Fixed

-   t3b-2359 Shredding is enabled regardless of the setting
-   t3b-2364 Right count of indexed documents if there are none
-   t3b-2375 Searching in vice list
-   t3b-2374 Copying role
-   t3b-2377 Error while deleting DynamicTable
-   t3f-1190 DynamicConditions don't reflect current values
-   t3f-1385 Fix variable: dynamic table multichoice
-   t3b-2201 Fix processing of big zfo files
-   t3f-1391 HTML preview removed

### Added

-   t3b-2367 DmsFileIndexingCron has more options

## [4.10.0] - 2022-03-21

### Fixed

-   t3b-2214 The variable DynamicRows under CustomViews is missing when length is over 65536 chars
-   Security fix of User parameters
-   t3b-2003 Authentication for Health status
-   t3b-2244 Precision scale fix only available for manual run
-   t3f-1330 Shared GraphReport is not loading for editing
-   t3b-1942 Right name in mail notification
-   t3b-1928 Incorrect logging of saving variable
-   t3f-1341 If the document is not found, no error info is returned
-   t3b-2292 Import with mass solving task
-   t3b-2296 Filtering by date in CustomViews
-   t3b-2307 Updating tags sometimes crashes
-   t3b-2308 Consistent sorting of planned tasks
-   t3b-2316 Improved searching in documents
-   t3f-1157 Dynamic height for DynamicConditions
-   t3f-1283 Dealing with different system font size
-   t3f-1307 When you open a CustomView via "favourites" the "tree" of previews is not modified
-   t3f-1237 More prominent display of "empty" variable DocumentList
-   t3f-1335 After deleting a user's first or last name, it is not possible to log in
-   t3f-1323 Fixed some Safari bugs
-   t3f-1357 DynamicRows vertical scrollbar fix
-   t3b-2325 Security fixes
-   t3f-1354 Live schema color change via configuration
-   t3b-2160 Bad PDF DPI in print on Linux
-   t3b-1953 MailTasksCron sometimes locks table
-   t3f-1381 Preview of inserted revision
-   t3b-2346 [Calculations] EportAttachment sometimes has delay

### Added

-   t3b-2225 [Calculations] New calculations to set DMS folder for file
-   t3b-2249 Filter for CustomViews
-   tas-543 New translation Serbian
-   t3b-2248 Possibility to create excel (CaseOverview, Calculations)
-   t3b-2255 Competencies
-   t3f-1275 Added option to disable notification of Tasks to pull
-   t3b-2270 Increased default of max exported rows
-   t3f-1310 Distinguish notifications by the environment from which the notification is sent
-   t3b-2254 HR agenda - filtered report
-   t3f-1240 Auto-fit columns in tables
-   t3f-1021 Optimalisation of DynamicRows
-   t3f-1324 Filtered CustomView via URL
-   t3f-1277 Improvements of the document preview component
-   t3f-1125 Allowing the save only button to be used if a change is made to the form
-   t3b-2260 Structured multiselect variable org. structure
-   t3f-1339 Ability to remove employees from org.units
-   t3f-1337 Ordering templates names while creating new case
-   t3f-1333, t3f-1326 Better support of middle mouse button
-   tas-553 Change of favicon via configuration
-   t3f-1346 Change of logo in email notifications
-   tas-543 Change schema color in emails via configuration
-   t3b-2322 Additional filters for vicing users
-   t3b-2349, t3b-2312 New calculations setOrganization, assignManagers, createOrganizationStructure, setUserParams
-   tas-553 Colors in emails can be changed via configuration

## [4.9.0] - 2022-01-17

### Fixed

-   t3b-2217 Users without a Case visibility can no longer run its Waiting Events
-   t3b-2200 Reworked the visibility report of a Case
-   t3b-2166 Rights persist for manager when his position is changed
-   t3b-2174 Prints don't allow using scripts with ES6
-   t3b-2175 CSV event: it only creates new processes instead of changing existing ones
-   t3f-1271 After changing the task solver via HR, the tasks are still displayed on dashboard
-   t3b-2003 Minor security fixes
-   t3b-2199 TAS doesn't allow login with extremely slow DB
-   t3b-2195 [Calculations] Fixed lib.finishSubProcessTree
-   t3b-1854 Show Task history if the User has Process visibility
-   t3b-2209 Changing variable names doesn't affect calculations (multiinstance)
-   t3b-2194 [Forms] Numeric Variable value sometimes returns an inaccurate value
-   t3f-1283 Proper font size with browser zoom
-   t3f-1280 [Forms] Confirmation while leaving task
-   t3f-757 iOS fixes
-   tas-541 Logout redirect
-   t3f-1292 [Forms] Marking checkbox when moving with tab on task form
-   t3f-1293 Dark mode improved
-   t3f-1305 Finished task is not visibly marked
-   t3b-2200 Improved visibility report
-   t3f-1239 [Forms] Showing suggested values from browser
-   t3b-2273 Logging performance improvements
-   t3b-2280 [Calculations] DynamicTableApi context fix
-   t3b-2098 [Calculations] lib.changeCaseStatus transaction fix
-   tas-533 Database driver parametrized query improvements
-   t3b-2229 [Emails] duplicate 'to-pull' Task notification
-   t3f-1334 Error after clicked Enter in input
-   t3f-1333 Open subprocess from task history

### Added

-   t3b-2233 added Azure AD authentication connector
-   t3b-1877 Added a new Cron that can instantiate Processes
-   t3b-2141 Reworked the visibility report of a Case
-   t3f-1168 Configuration for disabled welcome screen
-   t3f-1246 [Forms] DynamicTables allowed add new column
-   t3b-2184 [Calculations] Added 'lib.setSecret(...)'
-   t3b-2196 [Calculations] Added 'docx.generate(...)'
-   t3b-2216 [Calculations] .whereCol() filtration
-   t3f-1098 Warning to IE-incompatible users
-   t3f-1251 [Forms] Possibility "Select all" in multichoice vars
-   t3f-1309 [Forms] Checkbox in DynamicRows
-   t3b-2272 SQL queries management
-   t3b-2238 Apache Kerberos authentication module
-   t3b-2193 [Calculations] added `storage.getCsvFile` calculation
-   t3b-2245 XMLProcessImport - doc/docx support
-   t3b-2258 [Calculations] DataType of column for ordering in DtApi

### Changed

-   Application health status improved

## [4.8.3] - 2021-10-04

### Fixed

-   t3b-2138 Limited list of users in HR
-   bug which prevents IE to load scripts
-   t3f-1304 RO translations corrections
-   t3f-1298 Allow the use of the **moment** library in frontend scripts

### Added

-   t3b-2187 [Calculations] Add `proc.getTemplateTasksInfo` calculation

## [4.8.2]

### Added

-   t3f-1298 Allow the use of the **moment** library in frontend scripts

## [4.8.1]

### Fixed

-   t3b-1955 Tasks Handover + waiting Events for HR and a Role
-   t3f-1289 [Calculations] 'varDefOn' bad functioning with dyn.conditions
-   t3f-1288 Unnecessary request in tree component
-   t3f-1281 [BPMN] Saving diagram with errors
-   t3f-757 Multichoice iOS bad functioning
-   t3b-2162 DynamicTable right use in some scripts
-   t3b-2165 Guide saving causes delete
-   t3f-1021 [Forms] DynamicRows - some conditions call twice
-   t3f-1270 Fix double-click doesn't download file
-   [AD] AD synchronisation crashes sometimes

### Added

-   t3b-2010 Nastavení MS OAuth2 pro EWS
-   tas-548 Overview with option to ignore Process rights

## [4.8.0]

### Fixed

-   t3b-2014 Incorrect number value - solving task
-   t3b-1091 Security fix for querying logs
-   t3b-2063 Suspending case with subprocesses
-   t3b-2076 Event - Merging cases
-   t3b-2062 DynamicTable export doesn't contain column names
-   t3b-2086 [Calculations] 'removeRoleFromUser' throws error
-   t3b-2071 [Calculations] 'usersToFullName' throws error when user not found
-   t3b-2037 DynamicTable doesn't store uploaded file name
-   t3b-2087 Uploading file sometimes throws error
-   t3b-2112 Incorrect evaluating rights in tasks to pull
-   t3f-1247 Copying variable ListOfValues doesn't preserve values
-   t3b-2094 Calculations logs swaped columns
-   t3b-1955 Handover of tasks based on role
-   t3b-2144 [Calculations] LibreOffice connection timeout
-   t3f-1263 Multichoice variables of DynamicTable can't be changed
-   t3f-1265 Default ordering of hand events
-   t3f-1270 Double-click doesn't download file

### Added

-   t3b-2021 Configurable limit of CSV/Excel export
-   t3b-2099 New parameter 'mustHaveFile' in XMLProcessImport
-   t3b-2067 New function 'dmsDeletePermanently'
-   t3f-1248 External id for organisation unit
-   t3b-2003 Adding password rules
-   t3b-2140 Shredding cases
-   t3f-1225 Showing tooltips while validating DynamicRows
-   t3f-1244 Button for changing variable names in mapping varibales

## [4.7.6] - 2021-07-15

### Fixed

-   t3b-2130 Copying a Template Variable now resets its TVAR_ALIAS

## [4.7.5] - 2021-07-12

### Fixed

-   t3b-1976 Case status translations in exported files from Overviews

### Added

-   t3f-1256 Allowing change some parameters for DynamicRows

## [4.7.4] - 2021-07-02

### Fixed

-   t3b-2109 Lack of permission for HrManager
-   New DynamicTable starts with COL_0 fix

### Added

-   t3f-1242 Advanced toolbar for ckEditor

### Changed

-   t3b-1984 Documents - case name is not visible for documents without rights

## [4.7.3] - 2021-06-23

### Fixed

-   Cannot add attachements into DynamicRows
-   t3f-1257 Fixed filtered list of DL variables in manual event
-   t3f-1258 Fixed error in attachment column in DynamicRows

## [4.7.2] - 2021-06-22

### Fixed

-   t3b-2104 [Workflow] DT with skipped columns retains its format when overwritten
-   t3b-2112 [Workflow] Task 'to Pull' now correctly ignores User chairs
-   t3b-2084 Table filtering fix
-   t3f-1253 DT visible column specification in DynamicForms (change colIndex on the fly)
-   t3f-1107 Fixed tvar_meta merge in DynamicForms

### Added

-   t3f-1249 New column in template variables (shared variable)

## [4.7.1] - 2021-06-08

### Fixed

-   t3b-2086 [Calculations] 'removeRoleFromUser' fixed
-   t3f-1235 [Forms] Performance optimization in DynamicsConditions
-   t3b-2064 Fixed events for updating variables
-   t3b-1950 Refresh after pulling task
-   t3b-2064 Missing column names in export to excel
-   t3b-2071 [Calculations] 'usersToFullName' doesn't throw error on missing user
-   t3b-2087 Uploading file sometimes throws error

### Added

-   t3b-2055 [Calculations] DocuSign integration

## [4.7.0] - 2021-xx-xx

### Fixed

-   t3b-2003 Security fixes
-   t3b-1632 Allows filtering for variable values in process
-   t3b-1724 [Workflow] Event parameter value limit enlarged
-   t3b-1836 Optimized export to excel

### Added

-   t3b-1731 Added option to allow use DB view in Overviews
-   tas-521 Middle mouse click in tables open detail in new tab
-   tas-508 Copy template variable
-   t3b-1950 [Workflow] Add button to pull task in actual process tasks
-   t3f-1169 Dark Mode
-   t3b-2034 [Calculations] New removeUserFromRole() method

## [4.6.2] - 2021-05-14

### Fixed

-   t3b-2013 Performance tweaks in VariableLov
-   t3f-1226 Rendering DynamicRows before DynamicConditions
-   t3b-2026 Log size limitation
-   t3b-2032 [Calculations] jsonToCsv no longer throws Error with an empty Array
-   t3b-2054 [MSSQL] Fixed filtering in Organization Structure
-   t3b-2051 [MSSQL] Fixed ordering in Custom Views in case of a duplicate column

### Added

-   t3f-1214 DtApi in global scope
-   t3f-1229 DynamicTable API in DynamicConditions
-   t3b-1693 Allowing run TAS in cluster mode

### Changed

-   tas-525 New Print engine

## [4.6.1] - 2021-04-23

### Fixed

-   t3b-1978 dynamicTableGetCell nevrací null při colId=0
-   t3b-1396 TAS sometimes doesn't start while .locks file missing
-   t3b-1967 [Workflow] Warning for non-existing variable for iterating
-   t3b-1940 Planned task doesn't include global script
-   t3b-1976 Variable LOV isn't exported to Excel
-   t3b-1987 Debug.error doesn't work
-   t3b-1992 [Calculations] Esaping '\' characters in lib.jsonToCsv
-   tas-525 Performance tweaks
-   t3f-1185 Input of calendar fixed

### Added

-   t3b-1918 [Calculations] Added 'getUsersWithRole' calculation
-   t3f-1221 New subtype for variable Date as 'without time' (timezone safe)
-   t3b-1998 [Calculations] Allows select columns in in DtApi.from
-   t3b-1999 [Workflow] Deadline for Tasks to pull
-   t3b-1911 [Prints] It can be used default (non-translated) value of LOVs with suffix \_default.
-   t3b-2017 Reset sequence when upload DynamicTable

## [4.6.0] - 2021-02-14

### Fixed

-   t3b-1725 Updating big DynamicTable
-   t3b-1514 [Workflow] Auto canceling depended on CaseStatus didn't work
-   t3b-1745 EWS parses files with no extension as .eml
-   t3b-1807 Appearing 'ITASK_ID' of undefined
-   t3b-1812 Missing multi-languages names while copying overviews
-   t3b-1801 [Calculations] Function 'getDMSFiles' returns deleted files
-   t3f-1148 [API] Sometimes tables return results in different order
-   t3b-1901 Mass update of tasks instances via changing templates is faster
-   t3b-1193 [Export] Global scripts didn't included in template exports
-   t3b-1787 [Calculations] Function 'defDynListOrg' returns incorrect result
-   t3b-1814 Filtering by date is inaccurately in overviews (MSSQL)
-   t3b-1894 Saving variable LOV with 0 value throws error
-   t3b-1902 [Calculations] Function exportAttachment didn't return last revision of file
-   t3b-1876 Plan started one hour earlier
-   t3f-1167 [Prints][Calculations] Alias for using Lodash library are '\_' and 'lodash'
-   t3f-1142 Better information when trying to solve already solved to-pull task
-   t3b-1850 Name and date of last change after updating Templates
-   t3b-1977 Upload DynamicTables with different structure

### Added

-   t3b-495 [Workflow] Logging of changed CaseOwner
-   t3b-1883 Plugins
-   t3b-1774 [Calculations] Sharepoint integration via Calculations
-   t3b-1829 Resolving freezed crons
-   t3b-1819 [Calculations] New package for file manipulations
-   t3f-1141 [Forms] Multichoice allowed for DynamicTables
-   t3f-1155 [Forms] New preview mode for variable File
-   t3b-1830 [Calculations] New function 'setHeaderByCode'
-   t3b-1113 [Calculations] New functions 'fileExists', 'dmsFileExists'
-   t3f-1186 Back button in guides
-   t3f-1217 [Forms] Allowing date inputs to insert only working days
-   t3f-1216 Cloning crons
-   t3b-1911 [Prints] Variables values are translated to user language

### Changed

-   t3b-1728 New way of saving DynamicTables internally
-   t3f-1090 Planned maintenance is now visible in notification
-   t3b-1754 Admin rights don't delegate to vice users even if they have same rights
-   t3b-1802 [Forms] Duplicate values are not allowed for variable ListOfValues
-   t3b-1846 [Calculations] Default timeout for CurlApi
-   t3f-1229 [Forms] Simpler interface to call DynamicTables

## [4.5.4] - 2020-02-23

### Fixed

-   t3b-1904 Fixed limitations of records for graphs
-   t3b-1913 [Workflow] Process ends even if hand event is active
-   t3b-1837 [Calculations] addMailNotifsAttachment() ignores sub-processes
-   t3b-1912 Missing translates for new Variables
-   t3b-1947 Deadlock while updating DMS tags
-   t3f-1182 [Forms] Variable List of numbers - fixed when starts with 0
-   t3f-1183 [BPMN] Doesn't allow to create links from events
-   t3f-1198 Jpg preview for DynamicRows

### Added

-   t3b-1872 [AD] Adding roles to users in AD synchronisation
-   t3b-1885 [Prints][Calculations] New function 'modifyPdfAddImage' to add image to existing PDF
-   t3b-1900 Notification email on failing XMLProcessImport
-   t3b-1833 Cloning cron jobs
-   t3b-1933 New configurable timeout for requests
-   t3f-1203 [Forms] Allowing insert negative numbers

## [4.5.3] - 2020-12-11

### Fixed

-   t3f-865 [Forms] Sometimes form data don't refresh
-   t3f-1178 [API] Missing 'user_full_name' in api service

## [4.5.2] - 2020-12-01

### Fixed

-   t3b-1839 Sometimes an ENOT Mail 'plain copy' did not go out
-   t3f-1163 [BPMN] Sometimes links don't store in graph
-   t3f-1165 [Forms] DynamicRows validation fixed

### Added

-   t3b-1847 [Calculations] Added 'iconv' library
-   t3b-1825 [Calculations] Added 'gunzip' function

## [4.5.1] - 2020-11-27

### Fixed

-   t3b-1723 Mulitple AD synchronisation locks users each other
-   t3f-1159 Changing language in settings don't persist
-   t3b-1768 Validation DynamicRows
-   t3f-1162, t3b-1845 [Forms] Cannot add user in multibox

### Added

-   t3b-1772 [Calculations] Added 'path' library

## [4.5.0] - 2020-11-12

### Added

-   [ZEA] Added support for electronic signing of documents
-   t3b-1497 Solver is choosen automatically when is only one solver for task to pull
-   t3b-1633 [Calculations] New 'copyFile()' function
-   t3f-956 Cloning DynamicTables
-   t3f-1262 Checkbox to include changes in template variables to instances
-   t3b-1664 [Emails] In EmailTasks added 'CC'
-   t3b-1675 [Calculations] WinRM implementation
-   t3b-1680 [Calculations] In 'exportAttachment' added encoding
-   t3b-1210 HR agenda to change deleted users in tasks
-   t3b-1356 [Calculations] New 'getCell()' function
-   t3b-1707 [Emails] Added process name to emails (task to pull)
-   t3b-1539 New 'changeCaseStatus()' function
-   t3b-1708 Template description
-   t3b-1700 Reports - Graphs
-   t3b-1736 Mass solving tasks
-   t3f-1112 [Forms] New 'validateVarOn()' method for DynamicForms
-   t3f-1059 Missing filtr '=','<>' in Overviews
-   t3f-1124 [Forms] Added '+' in DynamicRows to bottom left

### Fixed

-   t3b-1002 [DMS] Internal error: An expression services limit has been reached
-   t3b-1611 Copy of Overview failed when languages labels erased
-   t3b-672 Improve mapping of variables in Events
-   t3b-1587 Fixed exported name of Templates
-   t3b-1193 Exporting scripts with templates
-   t3b-1683 Improve of loading attachments
-   t3b-1720 Report of rights - task to pull
-   t3b-1648 ListNumbers variable doesn't accept 0,-1
-   t3f-1116 Native autofilling forms overlaps form units

### Changed

-   t3b-1694 Events with 'button' are not displayed as regular hand events
-   t3b-1635 [Emails] Sanitizing Task description in emails
-   t3b-947 Uploading DynamicTable numeric column names are not allowed
-   t3b-1692 Uploading DynamicTable with duplicit values of indices are not allowed
-   t3b-1687 New view 'All tasks'
-   t3b-1703 [Calculations] defDynList allows filtering by user status

## [4.4.2] - 2020-08-19

### Fixed

-   t3b-1714 'undefined' word because of PdfExporter

## [4.4.1] - 2020-08-12

### Fixed

-   t3b-1704 [DMS] Errors in DMS calculations

## [4.4.0] - 2020-08-11

### Added

-   t3b-1516 [Calculations] English documentation for calculations
-   t3b-1396 Server and TAS health state
-   t3b-872 [AD] User photo from Active directory
-   t3b-1548 Sequence numbers can be changed in administration
-   t3b-1626 Windows Remote Management interoperability
-   t3b-1602 Roles can be cloned
-   t3f-1058 Roles can be exported to csv
-   t3b-1594 [Prints] Implicit CSS styles can be disabled in prints
-   t3b-1575 [Calculations] Can run macro while exporting to office documents
-   t3b-1576 EWS Cron can now handle 'smime.p7m' files
-   t3b-1567 [AD] Origin groups of users are stored when synchronising
-   t3b-1563 [Calculations] New 'lib.customView' calculation
-   t3b-1568 Dynamic Table file source history
-   t3b-1574 Isdocx import
-   t3b-1573 CustomView translations
-   t3b-1549 New way using Overviews as overview of existing(external) DB tables
-   Logs 'erasing' cases
-   t3f-923 [Prints] New way of creating prints with React and TAS components
-   t3b-1500 [Calculations] New functions 'lib.moveFile()' and 'lib.getFileLink()'
-   t3b-1542 [Calculations] New Event and calculations object 'Curl' can call external services
-   t3b-1457 [AD] Differentiation for logging users (intranet, extranet) with ADFS
-   t3b-1523 [Calculations] New 'lib.pdfToText()' to convert PDF to text
-   t3b-1552 [Calculations] New 'dms.getFileContent()' and 'dms.getAbsolutePath()'
-   t3b-1525 [Calculations] New 'lib.getUserByAttr()' to search user by attribute
-   t3f-1069 DynamicRow has new column 'attachments'
-   t3f-1048 Copying roles from one user to another
-   t3f-1044 Possibility to change behavior of 'Solve' and 'Save' buttons
-   t3f-1086 DefaultViewDate for variable type Date

### Changed

-   tas-477 Existing entities can be edited via their template (applies to links, mapping variables to tasks, auto-cancel and auto-completition of tasks)
-   t3b-706 Rework of authentication module (added ADFS)
-   t3b-578 Users who have task to pull also have rights to see whole process
-   t3b-243 Invalid start of process causes that appears in 'Errored cases'
-   t3b-1565 Case Supervisor can't change variables in task history now
-   t3b-1554 Rest timeout is configurable now
-   t3b-1595 [Prints] Values of LOV variables will be translated in Case Overview based on client language
-   t3f-1019 Reference user can be multiline calculation
-   t3f-865 Replaced button 'Save' to 'Save only' in form task
-   t3f-669 [BPMN] Automatically doesn't connect links to moved tasks
-   t3b-1556 [BPMN] Doubled tasks links are discovered

### Fixed

-   t3b-1610 EWS extracting email from attachment (eml,zip)
-   t3b-1211 Change 'LACK_OF_PERMISSIONS' hint on disabled template process in dashboard
-   t3b-1560 Default null DL/LN multi variable throws error
-   t3b-1593 DMS filename disallows unsupported characters
-   t3f-1062 'debug.error' freezes form task when used
-   t3b-1509 Truncating variables in Multiinstance tasks
-   t3b-1550 Fixed filtration by Date for MSSQL
-   t3b-1592 Variable LOV doesn't accept '0'
-   t3f-1031 Spinner in Dashboard hangs (Firefox)
-   t3b-1670 Simultaneously updating DynamicTable causes inconsistency

### Removed

-   t3f-1067 Folder of Templates

## [4.3.6] - 2020-09-03

### Fixed

-   t3b-1572 [Calculations] CopySharedFileLink throws error

## [4.3.5] - 2020-08-19

### Fixed

-   t3b-1667 [Calculations] Delete DynamicList multi variable with setValue("");
-   t3b-1671 "Lack of permissions !" for logs
-   t3b-1670 Updating DynamicTable is asynchronous

## [4.3.4] - 2020-06-24

### Fixed

-   t3b-1642 "ReferenceError: Can't find variable: PDFPager TRACE: -> undefined: 2 -> : 3" while creating PDF

## [4.3.3] - 2020-06-16

### Fixed

-   t3b-1592 Cannot use '0' as a value in NumberList variable

## [4.3.2] - 2020-05-28

### Fixed

-   t3b-1579 EWS Error "Property 'CcRecipients.Mailbox.EmailAddress' was not found in the provided data!"

## [4.3.1] - 2020-04-29

### Fixed

-   t3b-1570 'allowedExternalSources' => '/' vs '\\' compatibility

## [4.3.0] - 2020-04-28

### Fixed

-   t3b-1348 Increased length of DynamicTable index column
-   t3b-1341 Error when loading Org.structure
-   t3b-1323 Filtering by vice in Vice table
-   t3b-1351 Dynamic configuration was skipped
-   t3b-1355 Cron - Error when startTime is null
-   t3b-387 Vice user should not inherit admin roles
-   t3b-1476 EWS force server version and NTLM auth
-   t3b-1485 Unable to reload CronRunner after syntax error
-   t3b-1494 Error 'Constraint violation'
-   t3b-1326 Cron - Error when starting on weekday
-   t3b-1372 Concurrence sequence generation was stringent
-   t3f-991 Error in table Task history
-   t3b-1417 Fixed calculation of savePrintForm()
-   t3b-1415 Fault AD synchronisation when 0 users to lock
-   t3b-1429 Fault calc lib.getOrgByAttr()
-   t3b-1434 Fault calc lib.hasOrgChild()
-   t3b-1433, t3b-1345 Calculations security improved
-   t3b-1477 CaseNote assigning to variable
-   t3b-1022 Decimal places in Overviews
-   t3b-1459 Double encoding socket connection
-   t3b-1422 DMS_FILE_TAG - Process attribute change not detected
-   t3b-1489 Sending more mails to one address
-   t3b-1450 HTML <\style\> tag in EWS conflicts with TAS
-   t3b-1501 Non-translated task name, subjects in emails
-   t3f-982 New style of chosen item in selectboxes
-   t3f-985 Error while uploading manuals
-   Dynamic rows were draggable in read-only mode
-   t3b-1420 Uploading public files
-   t3f-1027 Bad color reference in green icons
-   t3f-977 Working arrow keys in SuggestBox
-   t3b-1517 allow updateDTFromCsv in global js
-   t3b-1491 Performance issue with high user Role count (custom view list)
-   t3b-1524 EWS NTLM auth mutability
-   t3b-1526 Fault calc lib.fnStrIndexOf with numeric parameter
-   t3b-1532 MSSQL - error when ASSESSMENT_HIERARCHY is 'null'
-   t3b-1535 .getValue() with DL returns invalid value
-   t3b-1533 'nullsLast' ordering fails on date/time columns
-   t3b-1506 User cannot delete DMS doc after 'Just saving' Task
-   t3b-1543 'To pull notification' does not include TTASK_NAME mutations
-   t3b-1559 Continuously 'from variable' Tasks not detected on MSSQL
-   t3b-1571 [Mail] Translations in mail 'Tasks overview'

### Added

-   t3b-1243 Zfo format extension
-   t3b-1358 Extended configuration possibilities for crons scripts
-   t3f-1007 New parameter to distinct result in Dynamic table
-   t3b-1349 Showing canceled tasks in Task history
-   t3b-1169 New system role SuperAdministrator
-   t3b-1438 New Table filtering parameter nullsLast
-   t3b-1488 Paging in generating PDF
-   t3b-1282 Mass downloading documents
-   t3b-1040 Copying Template header
-   t3b-1505 Conversion pdf to doc file format
-   t3f-802 New way of designing forms (variables) with fixed positions
-   t3b-1510 Page orientation when generating PDF
-   Chart.js updated to new version
-   t3f-862 Mass editable Multi Text
-   t3f-1305 Gzip compression enabled
-   t3b-1426 Showing automatic tasks in Finished tasks
-   t3f-1008 Evaluate dynamic conditions when opening task, recalculating
-   t3b-1519 New DMS Files calculations 'getDMSFileIds(), getDMSFileNames(), getDMSFiles(), getDMSFile()'
-   t3b-1500 Add new 'moveFile()' calculation
-   t3b-1541 Add new 'convertDmsFile()' calculation

### Changed

-   t3f-1013 User can't see suspended processes
-   t3b-1424 Updating default User Guides can't be done
-   "\<style\>" tag is now forbidden for variables expect TextEditor

## [4.2.0]

### Fixed

-   t3b-1365, t3b-1370, t3f-984, t3b-1368, t3b-1367, t3b-1387 Security fixes
-   t3b-1355 Cron startTime wrong time fix
-   t3b-1380 Cron failed with 'Cannot read property getTime of null'
-   t3b-1467 Fix - Saving cron huge parameters

### Added

-   Unsafe helper for insecure output

## [4.1.7] - 2020-03-05

### Fixed

-   t3f-1026 CKeditor fix: editor doesn't load

## [4.1.6] - 2020-02-26

### Fixed

-   t3b-1467 Allows large crons definitions

## [4.1.5] - 2020-01-27

### Fixed

-   t3b-1454 EWS fix for old version

## [4.1.4] - 2020-01-08

### Fixed

-   Fixed version of libraries - IE bug

## [4.1.3] - 2019-11-14

### Fixed

-   t3b-1261 MSSQL fix
-   t3b-1351 Configuration not loaded properly in some cases
-   t3b-1341 Organisation structure throws error while loading

## [4.1.2] - 2019-11-11

### Fixed

-   t3b-1337 Export time adjusts time-zone of client
-   Dashboard's OverviewsTasks crashes

## [4.1.1] - 2019-11-08

### Fixed

-   t3b-1339 Access log invalid migration
-   t3b-1337 xlxs export with invalid time (-1hour)

### Added

-   t3b-1338 Changelog si reachable via gui

## [4.1.0] - 2019-11-02

### Added

-   t3f-966 New container "Overviews and tasks" in dashboard
-   New calculation method hasOrgChild
-   t3b-1305 New calculation function "findShareVariables"
-   t3b-1285 EWS embedded images in mail.
-   t3f-900 Hlídání nevyhodnocení dynamických podmínek
-   Calculations are templated (not instanciated)
-   t3f-857 DynamicRows improvements (DnD, copying row,...)
-   t3f-939 New variable type - checkbox

### Changed

-   Node 12 engine required

### Fixed

-   t3b-1055 ttask_assesment_tvar_id not changed on task change
-   Delete logs fixed
-   t3f-647 In hand events is not load variable DynamicTable
-   t3f-969 Translations in "alarm" notifications
-   t3b-1296 Ordering in variable history
-   t3f-945 Twice solving task
-   t3b-1293 Rest db hangs
-   t3b-1292 Better detect calculation errors
-   t3b-520 Inspector can't see task history
-   t3b-1308 Mail to vices
-   t3b-1289 Validation on change event name
-   t3b-1306 Calculations on multichoice variables (defDynList a fillList)
-   CustomView deletion with active subscriptions
-   t3b-1129 Crons hangs in background after restart
-   t3b-1300 DynamicList from csv trigger
-   t3b-1277 Function getAresData returns nothing if record not found
-   t3b-1288 lib.assignRightsToSubprocesses() not working
-   t3b-1284 Displaying ueser status in Users by role and orgstr units
-   t3b-1279 Automatic cancel/finish ignores sys.variables
-   t3b-1271 Effective metadata logging
-   t3b-1273 Errored planned tasks doesn't run again
-   Events fix (business days, immediate start on plan creation)
-   t3f-617 DynamicRows with multirows cell - fix readonly textarea background
-   Multiple codemirror - missing row numbers and content
-   IE babel polyfill fix (SCRIPT438: Object doesn't support property or method assign.)
-   t3f-962 Error after delete overview
-   t3f-961 Graph - moving links doubled
-   t3f-932 Highlighte links of chosen task in Graph
-   t3f-953 Logging DynamicRows improved
-   t3f-954 Error displaying Graph
-   t3f-930 Event list in case detail
-   t3f-943 Help for dyn.conditions and scripts
-   t3f-646 Showing date variables in overviews
-   t3f-900 Watching not runned Dyn.conditions (task script - variable not found cb)
