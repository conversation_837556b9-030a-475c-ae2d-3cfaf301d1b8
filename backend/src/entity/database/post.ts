import {
    BaseEntity,
    Cascade,
    Collection,
    Entity,
    Enum,
    ManyToMany,
    ManyToOne,
    OneToMany,
    PrimaryKey,
    PrimaryKeyProp,
    Property,
    Ref,
} from "@mikro-orm/core";
import { PostTag } from "./postTag";
import { PostVisibility } from "./postVisibility";
import { Users } from "./users";

export enum PostState {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    PLANNED = "PLANNED",
}

export enum PostPriority {
    REGULAR = 0,
    IMPORTANT = 1,
}

@Entity({ tableName: "POST" })
export class Post extends BaseEntity {
    [PrimaryKeyProp]?: "postId";

    @PrimaryKey({
        fieldName: "POST_ID",
        type: "integer",
        autoincrement: true,
    })
    postId!: number;

    @Property({
        fieldName: "POST_TITLE",
        type: "string",
        length: 255,
    })
    postTitle!: string;

    @Property({
        fieldName: "POST_TITLE_CS",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleCs?: string;

    @Property({
        fieldName: "POST_TITLE_DE",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleDe?: string;

    @Property({
        fieldName: "POST_TITLE_EN",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleEn?: string;

    @Property({
        fieldName: "POST_TITLE_FR",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleFr?: string;

    @Property({
        fieldName: "POST_TITLE_HR",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleHr?: string;

    @Property({
        fieldName: "POST_TITLE_IT",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleIt?: string;

    @Property({
        fieldName: "POST_TITLE_PL",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitlePl?: string;

    @Property({
        fieldName: "POST_TITLE_RO",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleRo?: string;

    @Property({
        fieldName: "POST_TITLE_RU",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleRu?: string;

    @Property({
        fieldName: "POST_TITLE_SK",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleSk?: string;

    @Property({
        fieldName: "POST_TITLE_SR",
        type: "string",
        length: 255,
        nullable: true,
    })
    postTitleSr?: string;

    @Property({
        fieldName: "POST_CONTENT",
        type: "string",
        length: 4000,
    })
    postContent!: string;

    @Property({
        fieldName: "POST_CONTENT_CS",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentCs?: string;

    @Property({
        fieldName: "POST_CONTENT_DE",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentDe?: string;

    @Property({
        fieldName: "POST_CONTENT_EN",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentEn?: string;

    @Property({
        fieldName: "POST_CONTENT_FR",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentFr?: string;

    @Property({
        fieldName: "POST_CONTENT_HR",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentHr?: string;

    @Property({
        fieldName: "POST_CONTENT_IT",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentIt?: string;

    @Property({
        fieldName: "POST_CONTENT_PL",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentPl?: string;

    @Property({
        fieldName: "POST_CONTENT_RO",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentRo?: string;

    @Property({
        fieldName: "POST_CONTENT_RU",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentRu?: string;

    @Property({
        fieldName: "POST_CONTENT_SK",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentSk?: string;

    @Property({
        fieldName: "POST_CONTENT_SR",
        type: "string",
        length: 4000,
        nullable: true,
    })
    postContentSr?: string;

    @Property({
        fieldName: "CREATED_AT",
        type: "datetime",
    })
    createdAt: Date = new Date();

    @Property({
        fieldName: "UPDATED_AT",
        type: "datetime",
        onUpdate: () => new Date(),
    })
    updatedAt: Date = new Date();

    @Property({
        fieldName: "POST_PHONE",
        type: "string",
        length: 100,
        nullable: true,
    })
    postPhone?: string | null = null;

    @Property({
        fieldName: "POST_EMAIL",
        type: "string",
        length: 255,
        nullable: true,
    })
    postEmail?: string | null = null;

    //MAX URL LENGTH IS 2048
    @Property({
        fieldName: "POST_CUSTOM_URL",
        type: "string",
        length: 2048,
        nullable: true,
    })
    postCustomUrl?: string | null = null;

    @Enum({ items: () => PostState, nullable: true, fieldName: "POST_STATE" })
    postState?: string;

    @Property({
        fieldName: "POST_PUBLICATION_DATE",
        type: "datetime",
        length: 0,
        nullable: true,
    })
    postPublicationDate: Date | null = null;

    @Property({
        fieldName: "POST_PUBLICATION_END_DATE",
        type: "datetime",
        length: 0,
        nullable: true,
    })
    postPublicationEndDate: Date | null = null;

    @Property({
        fieldName: "POST_SEND_EMAIL",
        type: "boolean",
    })
    postSendEmail: boolean = false;

    @Enum({
        items: () => PostPriority,
        default: PostPriority.REGULAR,
        fieldName: "POST_PRIORITY",
    })
    postPriority: number = 0;

    @ManyToOne({
        entity: () => Users,
        ref: true,
        deleteRule: "no action",
        updateRule: "no action",
        fieldName: "POST_CREATED_BY",
    })
    postCreatedBy: Ref<Users> | null = null;

    @ManyToOne({
        entity: () => Users,
        ref: true,
        deleteRule: "no action",
        updateRule: "no action",
        fieldName: "POST_UPDATED_BY",
    })
    postUpdatedBy: Ref<Users> | null = null;

    @ManyToMany({
        entity: () => PostTag,
        pivotTable: "POSTS_TAGS",
        joinColumn: "POST_ID",
        inverseJoinColumn: "TAG_ID",
        cascade: [Cascade.REMOVE],
    })
    postsTags = new Collection<PostTag>(this);

    @OneToMany({ entity: () => PostVisibility, mappedBy: "postId" })
    postVisibilityCollection = new Collection<PostVisibility>(this);
}
