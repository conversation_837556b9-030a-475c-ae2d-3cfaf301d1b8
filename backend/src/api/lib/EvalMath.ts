import moment from "moment";
import { DateBusinessUtils } from "../utils/DateBusinessUtils";
import { AuthorizedUser } from "../../service/authorization/AuthorizedUser";

export class EvalMath {
    private req: any;

    constructor(req: any) {
        this.req = req;
    }

    /**
     * DEPRECATED — Use TaskScript instead
     * Evaluates string expressions with dynamic placeholders (e.g., #now#, #user#, etc.)
     */
    async evaluate(calc: string): Promise<string | Date> {
        if (!calc || typeof calc !== "string" || !calc.match) {
            return calc;
        }

        let out: string | Date = calc;

        // Replace placeholders using user context
        if (this.req) {
            const user: AuthorizedUser =
                await globalThis.container.service.auth.getUserData(this.req);

            if (/#user#/gi.test(out)) {
                out = out.replace(/#user#/gi, user.USER_DISPLAY_NAME);
            }

            if (/#orgUnit#/gi.test(out)) {
                out = out.replace(/#orgUnit#/gi, user.ORGANIZATION.ORGSTR_NAME);
            }
        }

        const du = new DateBusinessUtils(
            globalThis.dynamicConfig.tas.calculations,
        );

        // Simple Date Tokens
        switch (out) {
            case "#now#":
                return new Date();
            case "#yesterday#":
                return new Date(moment().subtract(1, "days").toDate());
            case "#tomorrow#":
                return new Date(moment().add(1, "days").toDate());
            case "#prevWorkDay#":
                return du.substractBussinesDays(new Date(), 1);
        }

        // Regex-based expressions
        const nowPlusRegExp = /^(#now)([+-])(\d+)#$/i;
        const monthStartEndRegExp = /^#(monthStart|monthEnd)([+-]\d+)?#$/i;
        const yearStartEndRegExp = /^#(yearStart|yearEnd)([+-]\d+)?#$/i;

        const nowPlusMatch = out.match(nowPlusRegExp);
        const monthMatch = out.match(monthStartEndRegExp);
        const yearMatch = out.match(yearStartEndRegExp);

        if (nowPlusMatch) {
            const [, , sign, amountStr] = nowPlusMatch;
            const amount = parseInt(amountStr, 10);

            if (sign === "+") {
                return new Date(moment().add(amount, "days").toDate());
            } else {
                return new Date(moment().subtract(amount, "days").toDate());
            }
        }

        if (monthMatch) {
            const [, type, offsetStr] = monthMatch;
            const offset = parseInt(offsetStr ?? "0", 10);

            return type === "monthStart"
                ? du.getMonthFirstDay(new Date(), offset)
                : du.getMonthLastDay(new Date(), offset);
        }

        if (yearMatch) {
            const [, type, offsetStr] = yearMatch;
            const offset = parseInt(offsetStr ?? "0", 10);

            return type === "yearStart"
                ? du.getYearFirstDay(new Date(), offset)
                : du.getYearLastDay(new Date(), offset);
        }

        return out;
    }
}
