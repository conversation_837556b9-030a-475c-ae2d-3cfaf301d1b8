// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { IdokladClient } from "../../client/IdkoladClient";

export class Idoklad {
    private readonly client: IdokladClient;

    constructor(clientId: string, clientSecret: string) {
        this.client =
            globalThis.container.client.idokladFactory.createIdokladClient(
                clientId,
                clientSecret,
            );
    }

    /**
     * // TODO: Is error throwing/handling okay?
     *
     * @param {String|Number} dynamicTableIdentificator - This must be DLL_ID or DLL_NAME of an existing DynamicListList
     * @param {Object} mapping - Key/Value object assigning DT columns iDoklad values.
     * // https://app.idoklad.cz/developer/Help/v2/cs/ResourceModel?modelName=IssuedInvoiceApiModel
     *
     * For example:
     *      const mapping = {
     *          Cislo_dokladu: 'DocumentNumber',
     *          Odberatel: 'PurchaserId',
     *          Popis: 'Description',
     *          DUZP: 'DateOfTaxing',
     *          Dat_vystaveni: 'DateOfIssue',
     *          Dat_splatnosti: 'DateOfMaturity',
     *          Celkem_bez_dph: 'TotalWithoutVat',
     *          Celkem_s_dph: 'TotalWithVat',
     *          Mena: 'CurrencyId',
     *          Kurz: 'ExchangeRate',
     *          Cislo_zakazky: 'Note',
     *          Cena_v_CZK: 'TotalWithoutVatHc',
     *          Platba: 'PaymentStatus',
     *          Dobropis_v_CZK: 'CreditNoteTotalWithoutVatHc,
     *      };
     * @param {String} pairBy - Unique column value that will be used to determine UPDATE/INSERT operations
     * @param {Boolean} includeCreditNotes - Subtract CreditNote values from Invoice values?
     * @param {Boolean} includeProformaInvoices - Should the complete list of invoices also include Proforma? => 'invoice.InvoiceType'
     * @returns {Promise<void>}
     */
    async synchronizeWithTas(
        dynamicTableIdentificator,
        mapping,
        pairBy = "DocumentNumber",
        includeCreditNotes,
        includeProformaInvoices,
    ) {
        await globalThis.database.transaction(async (trx) => {
            globalThis.tasLogger.info(
                "Started: Connecting to TAS, getting DynamicTable information.",
                {
                    DynamicTable: dynamicTableIdentificator,
                },
            );
            const dtRepo = globalThis.orm.repo("dynamicTable", trx);
            const dtvRepo = globalThis.orm.repo("dynamicTableValue", trx);

            const table = await dtRepo.getTable(dynamicTableIdentificator);
            globalThis.tasLogger.info(
                "Started: Connecting to iDoklad, getting available records.",
            );

            const invoices = await this.getInvoices(
                dynamicTableIdentificator,
                mapping,
                pairBy,
                includeCreditNotes,
                includeProformaInvoices,
            );

            const dtPairBy = this.getDtPairBy(pairBy, mapping);
            await dtvRepo.mergeCsv(
                table.DT_ID,
                invoices,
                ";",
                true,
                true,
                false,
                dtPairBy,
            );
            globalThis.tasLogger.info("Finished: iDoklad.");
        });
    }

    /**
     * @param dynamicTableIdentificator
     * @param mapping
     * @param pairBy
     * @param includeCreditNotes
     * @param includeProformaInvoices
     * @returns {Promise<Array>}
     */
    async getInvoices(
        _dynamicTableIdentificator,
        mapping,
        pairBy = "DocumentNumber",
        includeCreditNotes,
        includeProformaInvoices,
    ) {
        await this.client.authenticate();
        const contacts = await this.client.getAllRecordsRecursively("Contacts");
        const currencies =
            await this.client.getAllRecordsRecursively("Currencies");
        const issuedInvoices =
            await this.client.getAllRecordsRecursively("IssuedInvoices");
        const creditNotes =
            await this.client.getAllRecordsRecursively("CreditNotes/Expand");
        const proformaInvoices = includeProformaInvoices
            ? await this.client.getAllRecordsRecursively("ProformaInvoices")
            : [];
        globalThis.tasLogger.info(
            "Finished: Connecting to iDoklad, getting available records.",
        );

        globalThis.tasLogger.info("Started: Mapping objects");
        return this.createObjectsByMapping(
            contacts,
            currencies,
            issuedInvoices,
            mapping,
            pairBy,
            creditNotes,
            includeCreditNotes,
            proformaInvoices,
        );
    }

    /**
     *
     * @param pairBy
     * @param mapping
     * @returns {string}
     */
    getDtPairBy(pairBy, mapping) {
        const keys = Object.keys(mapping);
        for (let i = 0; i < keys.length; i += 1) {
            const key = keys[i];
            if (mapping[key] === pairBy) {
                return key;
            }
        }
        throw new UserException(`pairBy '${pairBy}' not found in mapping.`, {
            pairBy,
            mapping,
        });
    }

    /**
     *
     * @param {Array<{}>} contacts
     * @param {Array<{}>} currencies
     * @param {Array<{}>} issuedInvoices
     * @param {{}} mapping
     * @param {String} [pairBy=DocumentNumber]
     * @param creditNotes
     * @param {Boolean} includeCreditNotes
     * @param {Array<{}>} proformaInvoices
     * @returns {Array}
     */
    createObjectsByMapping(
        contacts,
        currencies,
        issuedInvoices,
        mapping,
        _pairBy = "DocumentNumber",
        creditNotes,
        includeCreditNotes,
        proformaInvoices,
    ) {
        // Change name of object's properties to Dynamic table's properties
        const objectsToImportToTas = [];

        // Add property to detect invoice type
        issuedInvoices.map((item) => {
            item.InvoiceType = "Vydaná";
            return item;
        });

        proformaInvoices.map((item) => {
            item.InvoiceType = "Zálohová";
            return item;
        });

        const invoices = [].concat(issuedInvoices).concat(proformaInvoices);
        invoices.forEach((invoice) => {
            // Change CurrencyId into Code
            invoice.CurrencyId = _.find(
                currencies,
                (currency) => invoice.CurrencyId === currency.Id,
            ).Code;

            // Change PurchaserId into CompanyName
            invoice.PurchaserId = _.find(
                contacts,
                (contact) => invoice.PurchaserId === contact.Id,
            ).CompanyName;
            const invoiceToImport = {};

            // Change PaymentStatus into human-readable values
            switch (invoice.PaymentStatus) {
                case 0:
                    invoice.PaymentStatus = "Nezaplaceno";
                    break;
                case 1:
                    invoice.PaymentStatus = "Zaplaceno";
                    break;
                case 2:
                    invoice.PaymentStatus = "Částečně";
                    break;
                case 3:
                    invoice.PaymentStatus = "Přeplaceno";
                    break;
                default:
                    break;
            }

            // Include Credit Notes
            const attributes = [
                "TotalWithVat",
                "TotalWithoutVat",
                "TotalWithVatHc",
                "TotalWithoutVatHc",
            ];
            invoice.CreditNotes = [];
            attributes.forEach((attribute) => {
                invoice[`CreditNote${attribute}`] = 0;
            });
            const notes = _.filter(
                creditNotes,
                (note) => invoice.Id === note.CreditNoteForDocumentId,
            );

            // Check if Invoice and Credit Note are both in the same currency
            let invoiceCurrencyMismatch = false;
            notes.forEach((note) => {
                // Detect mismatch
                if (note.Currency.Code !== invoice.CurrencyId) {
                    globalThis.tasLogger.warning(
                        "Invoice and Credit Note currency mismatch detected.",
                        {
                            invoiceId: invoice.id,
                            creditNoteId: note.id,
                            invoiceCurrency: invoice.CurrencyId,
                            noteCurrency: note.Currency.Code,
                        },
                    );
                    invoiceCurrencyMismatch = true;
                    note.invoiceCurrencyMismatch = true;

                    invoice.CreditNotes.push(note.Id);
                }
            });

            // Mismatch detected, replace values with Errors
            if (invoiceCurrencyMismatch) {
                const replaceValueWithText = (
                    attribute,
                    text = "Chyba při párování faktury s dobropisem (rozdílná měna). Více informací v logu.",
                ) => {
                    invoice[attribute] = text;
                };
                attributes.forEach((attribute) => {
                    replaceValueWithText(attribute);
                    replaceValueWithText(`CreditNote${attribute}`);
                });
            }

            // Notes exist and there is not mismatch, add Credit Notes
            if (notes.length && !invoiceCurrencyMismatch) {
                const addCreditNote = (attribute) => {
                    const reducer = (accumulator, currentValue) =>
                        accumulator + currentValue;
                    invoice[`CreditNote${attribute}`] = notes
                        .map((note) => note[attribute])
                        .reduce(reducer);

                    // Add Credit Note sum to Invoice sum?
                    if (includeCreditNotes) {
                        // Don't let the sum be negative (currency ratio etc.)
                        const sum =
                            invoice[attribute] +
                            invoice[`CreditNote${attribute}`];
                        invoice[attribute] = sum < 0 || !sum ? "0" : sum;
                    }
                };

                attributes.forEach((attribute) => {
                    addCreditNote(attribute);
                });
            }

            // Translate mapped properties
            Object.keys(mapping).forEach((key) => {
                invoiceToImport[key] = invoice[mapping[key]];
            });

            // Push to array
            objectsToImportToTas.push(invoiceToImport);
        });
        return objectsToImportToTas;
    }
}
