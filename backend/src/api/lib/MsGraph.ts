import _ from "lodash";
import path from "path";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import iconv from "iconv-lite";
import { Client, ResponseType } from "@microsoft/microsoft-graph-client";
import { simpleParser } from "mailparser";
import { TokenCredentialAuthenticationProvider } from "@microsoft/microsoft-graph-client/authProviders/azureTokenCredentials";
import {
    ClientSecretCredential,
    InteractiveBrowserCredential,
    TokenCredential,
} from "@azure/identity";
import {
    IParsedAttachment,
    MsGraphAttachment,
    MsGraphFileAttachment,
    MsGraphFolder,
    MsGraphItemAttachmentExpanded,
    MsGraphMail,
} from "./MsGraphTypes";

import { UtilsService } from "../services/UtilsService";

export interface MsGraphCredentialsSecret {
    emailAddress: string;
    scope?: string[];
    tenantId: string;
    clientId: string;
    clientSecret: string;
    type: "secret";
}

export interface MsGraphCredentialsDedicated {
    emailAddress: string;
    scope?: string[];
    tenantId: string;
    clientId: string;
    type: "dedicated";
}

export type MsGraphCredentials =
    | MsGraphCredentialsSecret
    | MsGraphCredentialsDedicated;

export class MsGraphClient {
    private readonly credentials: MsGraphCredentials;

    private readonly client: Client;

    constructor(credentials: MsGraphCredentials) {
        this.credentials = credentials;

        let credential: TokenCredential;
        if (this.credentials.type === "secret") {
            credential = new ClientSecretCredential(
                this.credentials.tenantId,
                this.credentials.clientId,
                this.credentials.clientSecret,
            );
        } else if (this.credentials.type === "dedicated") {
            credential = new InteractiveBrowserCredential({
                tenantId: this.credentials.tenantId,
                clientId: this.credentials.clientId,
            });
        } else {
            throw new Error("Unknown credentials type");
            // TODO: credential v komunikaci s FE
        }

        this.client = Client.initWithMiddleware({
            debugLogging: true,
            authProvider: new TokenCredentialAuthenticationProvider(
                credential,
                {
                    scopes: this.credentials.scope || [
                        "https://graph.microsoft.com/.default",
                    ],
                },
            ),
        });
    }

    async listFolders(skip: number = 0): Promise<{
        "@odata.context": string;
        value: MsGraphFolder[];
    }> {
        const response = await this.client
            .api(`/users/${this.credentials.emailAddress}/mailFolders`)
            .skip(skip)
            .get();

        return response;
    }

    async listChildFolders(
        id: string = "inbox",
        skip: number = 0,
    ): Promise<{
        "@odata.context": string;
        value: MsGraphFolder[];
    }> {
        const response = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/mailFolders/${id}/childFolders`,
            )
            .skip(skip)
            .get();

        return response;
    }

    async getFolder(
        id: string = "inbox",
    ): Promise<MsGraphFolder & { "@odata.context": string }> {
        const response: MsGraphFolder & { "@odata.context": string } =
            await this.client
                .api(
                    `/users/${this.credentials.emailAddress}/mailFolders/${id}`,
                )
                .get();

        return response;
    }

    async setEmailAsUnread(mailId: string): Promise<{ isRead: boolean }> {
        const response = await this.client
            .api(`/users/${this.credentials.emailAddress}/messages/${mailId}`)
            .update({
                isRead: false,
            });

        return response;
    }

    async setEmailAsRead(mailId: string): Promise<{ isRead: boolean }> {
        const response = await this.client
            .api(`/users/${this.credentials.emailAddress}/messages/${mailId}`)
            .update({
                isRead: true,
            });

        return response;
    }

    async moveMail(mailId: string, folder: string) {
        const response = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/messages/${mailId}/move`,
            )
            .post({
                destinationId: folder,
            });

        return response;
    }

    async getFileAttachment(
        mailId: string,
        attachmentId: string,
    ): Promise<MsGraphFileAttachment> {
        const response = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/messages/${mailId}/attachments/${attachmentId}`,
            )
            .get();

        return response;
    }
    async getItemAttachment(
        mailId: string,
        attachmentId: string,
    ): Promise<MsGraphItemAttachmentExpanded> {
        const attachment = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/messages/${mailId}/attachments/${attachmentId}`,
            )
            .expand("microsoft.graph.itemattachment/item")
            .get();
        const emlContent = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/messages/${mailId}/attachments/${attachmentId}/$value`,
            )
            .responseType(ResponseType.ARRAYBUFFER)
            .get();
        const contentBytes = Buffer.from(emlContent).toString("base64");
        return {
            ...attachment,
            contentBytes,
        };
    }

    async listAttachments(mailId: string): Promise<IParsedAttachment[]> {
        const attachments = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/messages/${mailId}/attachments`,
            )
            .get();
        const graphAttachments: IParsedAttachment[] = [];
        for (const attachment of attachments.value) {
            if (
                attachment["@odata.type"] === "#microsoft.graph.fileAttachment"
            ) {
                const parsed = await simpleParser(
                    Buffer.from(attachment.contentBytes, "base64"),
                );
                const nestedFiles = parsed.attachments.map((att) => ({
                    name: att.filename ? att.filename : att.contentDisposition,
                    contentBytes: att.content.toString("base64"),
                }));
                graphAttachments.push(attachment);
                graphAttachments.push(...nestedFiles);
            }
            if (
                attachment["@odata.type"] === "#microsoft.graph.itemAttachment"
            ) {
                const itemAttachment = await this.getItemAttachment(
                    mailId,
                    attachment.id,
                );
                graphAttachments.push(itemAttachment);
                const itemFileAttachments: MsGraphFileAttachment[] =
                    itemAttachment.item?.attachments ?? [];
                const nestedFiles = itemFileAttachments.map(
                    (att: MsGraphFileAttachment) => ({
                        name: att.name,
                        contentBytes: att.contentBytes,
                    }),
                );
                graphAttachments.push(...nestedFiles);
            }
        }
        return graphAttachments;
    }

    async getMail(mailId: string): Promise<MsGraphMail> {
        const response = await this.client
            .api(`/users/${this.credentials.emailAddress}/messages/${mailId}`)
            .get();

        return response;
    }

    async listMailsFromFolder(
        folderId: string,
        selectFields: string = "id",
        pagination: { skip: number; top: number } = { skip: 0, top: 10 },
    ): Promise<{
        "@odata.context": string;
        value: {
            "@odata.etag": string;
            id: string;
            // and others from selectFields
        }[];
    }> {
        const response = await this.client
            .api(
                `/users/${this.credentials.emailAddress}/mailFolders/${folderId}/messages`,
            )
            .select(selectFields)
            .skip(pagination.skip)
            .top(pagination.top)
            .get();

        return response;
    }

    public async getEmailAndDecodeAttachments(mailId: string): Promise<{
        email: MsGraphMail;
        attachments: IParsedAttachment[];
    }> {
        const email = await this.getMail(mailId);
        const attachments = await this.listAttachments(mailId);
        let decodedAttachments: Pick<
            MsGraphAttachment,
            "name" | "contentBytes"
        >[] = [];
        for (const fileAttachment of attachments) {
            const decoded = await this.decodeFile(fileAttachment);
            decodedAttachments = decodedAttachments.concat(decoded);
        }
        return {
            email,
            attachments: decodedAttachments,
        };
    }

    async decodeFile(
        attachment: IParsedAttachment,
    ): Promise<IParsedAttachment[]> {
        const { ext } = path.parse(attachment.name);
        const endsWithNumericSequence = /[\d]+$/;
        const extension =
            !ext ||
            UtilsService.isNumericString(ext) ||
            ext.match(endsWithNumericSequence) ||
            ext.includes(" ")
                ? ".eml"
                : ext;
        switch (extension) {
            case ".p7m":
                return await this.decodeP7mFile(attachment);
            case ".zip":
                return await this.decodeZipFile(attachment);
            default:
                return [attachment];
        }
    }

    async decodeP7mFile(
        attachment: IParsedAttachment,
    ): Promise<IParsedAttachment[]> {
        const parsed = await simpleParser(attachment.contentBytes);

        return parsed.attachments
            .filter(
                (parsedAttachment) => parsedAttachment.filename !== "smime.p7s",
            )
            .map((parsedAttachment) => ({
                name: parsedAttachment.filename || "",
                contentBytes: parsedAttachment.content,
            }));
    }

    async decodeZipFile(
        attachment: IParsedAttachment,
    ): Promise<IParsedAttachment[]> {
        const zip = new JSZip();
        try {
            const { files } = await zip.loadAsync(
                typeof attachment.contentBytes === "string"
                    ? Buffer.from(attachment.contentBytes, "base64")
                    : attachment.contentBytes,
                {
                    decodeFileName: (fileName) => {
                        const isValid = !new TextDecoder("utf-8")
                            // @ts-expect-error unions...
                            .decode(fileName)
                            .includes("�");
                        return isValid
                            ? // @ts-expect-error unions...
                              iconv.decode(fileName, "utf-8")
                            : // @ts-expect-error unions...
                              iconv.decode(fileName, "cp852");
                    },
                },
            );

            let attachments: IParsedAttachment[] = [];
            for (const fileName in files) {
                if (fileName.endsWith("/")) {
                    continue;
                }

                // fileName includes the whole path, ie. 867909/BPDP/2.pdf
                // Needs fixing so it creates to a valid 'tmp' folder
                const fileNameFixed = _.last(fileName.split("/"));

                if (fileNameFixed!.startsWith(".")) {
                    continue;
                }

                const content = await zip.file(fileName)?.async("nodebuffer");

                if (!content) {
                    continue;
                }

                const decodedFiles = await this.decodeFile({
                    name: fileNameFixed!,
                    contentBytes: content,
                });
                attachments = attachments.concat(decodedFiles);
            }

            return attachments;
        } catch (err: any) {
            globalThis.tasLogger.error("Could not process file", err);
            if (err.message.includes("Encrypted zip are not supported")) {
                return [attachment];
            }
            throw err;
        }
    }
}
