// @ts-nocheck
// @ts-nocheck
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { JwtException } from "../../utils/errorHandling/exceptions/jwtException";
import jwt, { JwtPayload } from "jsonwebtoken";

export class JsonWebToken {
    static async sign(payload, secretOrPrivateKey, options): Promise<string> {
        const defaultOptions = {
            algorithm: "HS256",
            issuer: `TAS-TeamAssistant @ ${globalThis.dynamicConfig.hostname} @ ${globalThis.dynamicConfig.frontendUrl}`,
            audience: globalThis.dynamicConfig.hostname || "TAS",
        };

        if (!options.expiresIn) {
            throw new InternalException(
                `Attribute 'expiresIn' must be defined!`,
                "BAD_INPUT",
                { options },
            );
        }

        if (!options.subject && !payload.sub) {
            throw new InternalException(
                `Attribute 'subject' must be defined!`,
                "BAD_INPUT",
                { options },
            );
        }

        try {
            return jwt.sign(payload, secretOrPrivate<PERSON>ey, {
                ...defaultOptions,
                ...options,
            });
        } catch (err) {
            throw new JwtException(err.message, "SIGN_FAILURE", err);
        }
    }

    static async verify(
        token: string,
        secretOrPrivateKey,
        options: Record<string, any>,
    ): Promise<JwtPayload> {
        const defaultOptions = {
            algorithms: ["HS256"],
            issuer: `TAS-TeamAssistant @ ${globalThis.dynamicConfig.hostname} @ ${globalThis.dynamicConfig.frontendUrl}`,
            audience: globalThis.dynamicConfig.hostname || "TAS",
        };

        if (!options.subject) {
            throw new InternalException(
                "Subject attribute must be defined!",
                "BAD_INPUT",
                { options },
            );
        }

        try {
            return jwt.verify(token, secretOrPrivateKey, {
                ...defaultOptions,
                ...options,
            });
        } catch (err) {
            throw new JwtException(err.message, err.name || "VERIFY_FAILURE", {
                err,
            });
        }
    }

    static decode(
        token: string,
        options: Record<string, any> = { complete: false },
    ): JwtPayload {
        return jwt.decode(token, options);
    }
}
