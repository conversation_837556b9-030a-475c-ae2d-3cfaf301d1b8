export interface MsGraphFolder {
    id: string;
    displayName: string;
    parentFolderId: string;
    childFolderCount: number;
    unreadItemCount: number;
    totalItemCount: number;
    sizeInBytes: number;
    isHidden: boolean;
}

export interface Item {
    "@odata.type": string;
    id: string;
    createdDateTime: string;
    attachments: MsGraphFileAttachment[];
    lastModifiedDateTime: string;
    receivedDateTime: string;
    sentDateTime: string;
    hasAttachments: boolean;
    internetMessageId: string;
    subject: string;
    bodyPreview: string;
    importance: string;
    conversationId: string;
    conversationIndex: string;
    isDeliveryReceiptRequested: boolean;
    isReadReceiptRequested: boolean;
    isRead: boolean;
    isDraft: boolean;
    webLink: string;
    internetMessageHeaders: any[];
    body: Body;
    sender: Recipient;
    from: Recipient;
    toRecipients: Recipient[];
    ccRecipients: Recipient[];
    flag: Flag;
}

export interface MsGraphAttachment {
    "@odata.context": string;
    "@odata.type": string;
    id: string;
    lastModifiedDateTime: string;
    name: string;
    contentType: any;
    size: number;
    isInline: boolean;
    contentBytes: string | Buffer;
}

export interface MsGraphItemAttachment extends MsGraphAttachment {
    "@odata.type": "#microsoft.graph.itemAttachment";
}

export interface MsGraphItemAttachmentExpanded extends MsGraphItemAttachment {
    "@odata.type": "#microsoft.graph.itemAttachment";
    item: Item;
}

export interface MsGraphReferenceAttachment extends MsGraphAttachment {
    "@odata.type": "#microsoft.graph.referenceAttachment";
}

export interface MsGraphFileAttachment extends MsGraphAttachment {
    "@odata.type": "#microsoft.graph.fileAttachment";
    contentId: any;
    contentLocation: any;
}

export interface MsGraphMail {
    "@odata.context": string;
    "@odata.etag": string;
    id: string;
    createdDateTime: string;
    lastModifiedDateTime: string;
    changeKey: string;
    categories: any[];
    receivedDateTime: string;
    sentDateTime: string;
    hasAttachments: boolean;
    internetMessageId: string;
    subject: string;
    bodyPreview: string;
    importance: string;
    parentFolderId: string;
    conversationId: string;
    conversationIndex: string;
    isDeliveryReceiptRequested: boolean | null;
    isReadReceiptRequested: boolean;
    isRead: boolean;
    isDraft: boolean;
    webLink: string;
    inferenceClassification: string;
    body: Body;
    sender: Recipient;
    from: Recipient;
    toRecipients: Recipient[];
    ccRecipients: Recipient[];
    bccRecipients: Recipient[];
    replyTo: Recipient[];
    flag: Flag;
}

export interface UpdateEmailRequestBody {
    bccRecipients: Recipient;
    body: Body;
    categories: string[];
    ccRecipients: Recipient[];
    flag: Flag;
    from: Recipient;
    importance: string;
    inferenceClassification: string;
    internetMessageId: string;
    isDeliveryReceiptRequested: boolean;
    isRead: boolean;
    isReadReceiptRequested: boolean;
    multiValueExtendedProperties: any[];
    replyTo: Recipient[];
    sender: Recipient;
    singleValueExtendedProperties: any[];
    subject: string;
    toRecipients: Recipient[];
}

export interface Body {
    contentType: string;
    content: string;
}

export interface EmailAddress {
    name: string;
    address: string;
}

export interface Recipient {
    emailAddress: EmailAddress;
}

export interface Flag {
    flagStatus: string;
}

export interface IParsedAttachment {
    name: string;
    contentBytes: string | Buffer;
}
