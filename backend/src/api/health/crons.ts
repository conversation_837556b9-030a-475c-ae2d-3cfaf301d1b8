// @ts-nocheck
// @ts-nocheck

export function crons() {
    const cronsEnabled = globalThis.dynamicConfig.crons.runOnStart;

    const outObj = {
        name: "crons",
        value: [],
    };

    if (cronsEnabled) {
        return globalThis.database
            .select(
                "CRON_NAME",
                "CRON_ID",
                "CRON_STATUS",
                "CRON_LAST_RUN",
                "CRON_LAST_END",
                "CRON_NEXT_RUN",
                "CRON_LAST_RUN_ERROR",
            )
            .from("CRONS")
            .where("CRON_STATUS", "A")
            .then((result) => {
                result.forEach((res) => {
                    // currently running crown
                    const getLastDuration = () => {
                        if (res.CRON_LAST_RUN && res.CRON_LAST_END) {
                            const durationMs =
                                res.CRON_LAST_END.getTime() -
                                res.CRON_LAST_RUN.getTime();
                            return Math.round(durationMs / 1000); // duration in seconds
                        }

                        return null;
                    };

                    const cronName = res.CRON_NAME;
                    outObj.value.push({
                        name: cronName,
                        [`${cronName},error`]: res.CRON_LAST_RUN_ERROR === "Y",
                        [`${cronName},lastRun`]: res.CRON_LAST_RUN,
                        [`${cronName},nextRun`]: res.CRON_NEXT_RUN,
                        [`${cronName},lastEnd`]: res.CRON_LAST_END,
                        [`${cronName},duration`]: getLastDuration(),
                        [`${cronName},runningSince`]: null,
                    });
                });

                return outObj;
            });
    }

    return outObj;
}
