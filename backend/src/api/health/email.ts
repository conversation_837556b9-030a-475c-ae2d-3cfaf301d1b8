// @ts-nocheck
// @ts-nocheck
import nodemailer from "nodemailer";

export async function email() {
    const mailConfig = globalThis.dynamicConfig.mail;
    const outObj = {
        name: "email",
        value: {
            enabled: mailConfig.sendingEnabled,
            usingSMTP: mailConfig.useSmtp,
            error: false,
            errorMessage: null,
        },
    };

    if (mailConfig.sendingEnabled && mailConfig.useSmtp) {
        const { smtp } = mailConfig;
        smtp.auth =
            !smtp.auth || (!smtp.auth.user && !smtp.auth.pass)
                ? null
                : smtp.auth;
        smtp.secure = globalThis.dynamicConfig.mail.smtp.secure;
        smtp.tls = globalThis.dynamicConfig.mail.smtp.tls;

        const transporter = nodemailer.createTransport(smtp);
        try {
            await transporter.verify();
        } catch (error) {
            outObj.value.error = true;
            outObj.value.errorMessage = error;
        }

        return outObj;
    } else {
        return outObj;
    }
}
