// @ts-nocheck
// @ts-nocheck
import { UserException } from "../../utils/errorHandling/exceptions/userException";

export class DataSender {
    constructor() {
        this.interval = null;
        this.errorCount = 0;
    }

    async sendData(param, throwError = false) {
        const repo = globalThis.orm.repo("healthStatusParam");
        let statusData = await repo.getStatus();

        try {
            statusData = JSON.parse(statusData);
        } catch (err) {
            const errorText = `Bad JSON for sending Health status: "${err.message}" - adjust it in the Health status setting`;
            if (throwError) {
                throw new UserException(errorText, "HEALTH_STATUS", err);
            }

            return globalThis.tasLogger.error(errorText, {
                err,
            });
        }

        const options = {
            url: param.HS_URL,
            data: statusData,
        };

        try {
            await globalThis.container.client.rest.post({
                url: param.HS_URL,
                data: statusData,
            });
        } catch (err) {
            this.errorCount += 1;

            if (this.errorCount > 2) {
                clearInterval(this.interval);
                this.errorCount = 0;
            }

            globalThis.tasLogger.error(
                `HealthStatus data sender error: ${err.message}`,
                {
                    options,
                },
            );

            if (throwError) {
                throw new UserException(
                    `Test sending - failed to send data: ${err.message}`,
                    "HEALTH_STATUS",
                    options,
                );
            }
        }
        return "Data sent";
    }

    async initSending() {
        const repo = globalThis.orm.repo("healthStatusParam");
        const param = await repo.getCollection().fetchOne();

        if (!param) {
            throw new Error("No record found in table HEALTH_STATUS_PARAM.");
        }

        // redial or stop sending after saving the healthStatus settings
        if (this.interval && !this.interval._destroyed) {
            clearInterval(this.interval);
            this.errorCount = 0;
        }

        if (param.HS_ENABLED === "Y" && param.HS_INTERVAL) {
            this.interval = setInterval(async () => {
                this.sendData(param);
            }, param.HS_INTERVAL * 60000); // HS_INTERVAL in minutes
        }
    }
}
