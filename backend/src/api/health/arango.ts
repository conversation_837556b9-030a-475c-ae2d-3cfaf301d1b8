// @ts-nocheck
import { Database } from "arangojs";

export async function arangoStats() {
    const arangoUrl = globalThis.dynamicConfig.logger.arango.host;

    const outObj: Record<string, any> = {
        name: "arangoStats",
        value: {
            url: arangoUrl,
            running: null,
            clientConnections: "",
            numberOfThreads: "",
            residentSize: "",
            virtualSize: "",
        },
    };

    if (arangoUrl === null) {
        outObj.value.running = false;
        return outObj;
    }

    try {
        const db = new Database(globalThis.dynamicConfig.logger.arango.host);
        if (globalThis.dynamicConfig.logger.arango.auth) {
            if (globalThis.dynamicConfig.logger.arango.auth.jwt) {
                db.useBearerAuth(
                    globalThis.dynamicConfig.logger.arango.auth.jwt,
                );
            } else {
                db.useBasicAuth(
                    globalThis.dynamicConfig.logger.arango.auth.user,
                    globalThis.dynamicConfig.logger.arango.auth.pass,
                );
            }
        }

        const dbSystem = db.database("_system");
        const statsReq = await dbSystem._connection.request({
            path: "/_admin/aardvark/statistics/short",
        });
        const { body } = statsReq;

        outObj.value.running = true;
        outObj.value.clientConnections = body.clientConnectionsCurrent;
        outObj.value.numberOfThreads = body.numberOfThreadsCurrent;
        outObj.value.residentSize = (
            body.residentSizeCurrent /
            1024 ** 3
        ).toFixed(2); // GB
        outObj.value.virtualSize = (
            body.virtualSizeCurrent /
            1024 ** 3
        ).toFixed(2); // GB
    } catch (err) {
        outObj.value.running = false;
        outObj.value.error = err.message;
    }

    return outObj;
}
