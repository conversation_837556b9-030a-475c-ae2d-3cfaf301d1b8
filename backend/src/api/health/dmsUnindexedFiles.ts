import * as FILE from "../orm/entity/const/dmsFileConsts";

export async function dmsUnindexedFiles() {
    const { elasticUrl } = globalThis.dynamicConfig.dms;
    const { fulltext } = globalThis.dynamicConfig.dms;

    const outObj = {
        name: "dmsUnindexedFiles",
        value: null,
    };

    if (elasticUrl && fulltext) {
        const result: any = await globalThis.database
            .count("* as value")
            .from("DMS_FILE")
            .where("IS_DELETED", "=", FILE.IS_NOT_DELETED)
            .where("IS_CURRENT", "=", FILE.CURRENT_YES)
            .where("IS_FULL_INDEXED", "=", "N");
        outObj.value = result[0].value;
        return outObj;
    }

    return outObj;
}
