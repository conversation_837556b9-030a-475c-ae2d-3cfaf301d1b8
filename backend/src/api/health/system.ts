import si from "systeminformation";

// CPU Load
export async function cpuLoad(): Promise<{
    load: number;
    coresLoad: number[];
}> {
    const currentLoad = await si.currentLoad();
    return {
        load: Math.round(currentLoad.currentLoad),
        coresLoad: currentLoad.cpus.map((cpu) => Math.round(cpu.load)),
    };
}

// Memory Usage
export async function memoryUsage(): Promise<{
    total: string;
    used: string;
    free: string;
}> {
    const memory = await si.mem();
    return {
        total: (memory.total / 1024 ** 3).toFixed(1),
        used: (memory.used / 1024 ** 3).toFixed(1),
        free: (memory.available / 1024 ** 3).toFixed(1),
    };
}

// HDD Usage
export async function hddUsage(): Promise<
    Array<{ mount: string; size: string; used: string; use: string }>
> {
    const fsSize = await si.fsSize();
    return fsSize
        .filter((item) => item.type !== undefined)
        .map((item) => ({
            mount: item.mount,
            size: (item.size / 1024 ** 3).toFixed(1),
            used: (item.used / 1024 ** 3).toFixed(1),
            use: item.use.toFixed(1),
        }));
}

// Network Transfer
export async function networkTransfer(): Promise<
    Array<{ iface: string; rxSec: string; txSec: string }>
> {
    await si.networkStats(); // first call - ignored
    await new Promise((res) => setTimeout(res, 1000));
    const networkStats = await si.networkStats();

    return networkStats.map((stat) => ({
        iface: stat.iface,
        rxSec: (stat.rx_sec / 1024).toFixed(1),
        txSec: (stat.tx_sec / 1024).toFixed(1),
    }));
}

// Filesystem Transfer
export async function fsTransfer(): Promise<{ rxSec: string; wxSec: string }> {
    await si.fsStats(); // first call - ignored
    await new Promise((res) => setTimeout(res, 1000));
    const fsStats = await si.fsStats();

    return {
        rxSec: ((fsStats?.rx_sec || 0) / 1024).toFixed(1),
        wxSec: ((fsStats?.wx_sec || 0) / 1024).toFixed(1),
    };
}

// TAS Processes
export async function tasProcesses(): Promise<{
    app: { cpu?: string; ram?: string; pid?: number };
    arango: { cpu?: string; ram?: string; pid?: number };
    cronRunner: Record<string, any>;
    zombieCronRunners: any[];
}> {
    const out = {
        app: {} as { cpu?: string; ram?: string; pid?: number },
        arango: {} as { cpu?: string; ram?: string; pid?: number },
        cronRunner: {},
        zombieCronRunners: [],
    };

    const processes = await si.processes();

    // App process
    const appProc = processes.list.find((p) => p.pid === process.pid);
    if (appProc) {
        out.app = {
            cpu: appProc.cpu.toFixed(1),
            ram: (appProc.memRss / 1024).toFixed(1),
            pid: appProc.pid,
        };
    }

    // Arango process
    const arangoProc = processes.list.find((p) => p.name.includes("arangod"));
    if (arangoProc) {
        out.arango = {
            cpu: arangoProc.cpu.toFixed(1),
            ram: (arangoProc.memRss / 1024).toFixed(1),
            pid: arangoProc.pid,
        };
    }

    return out;
}

// Zombie Cron Check
export async function tasZombieCron(): Promise<boolean | null> {
    try {
        return globalThis.dynamicConfig?.crons?.runOnStart ? false : null;
    } catch {
        return null;
    }
}
