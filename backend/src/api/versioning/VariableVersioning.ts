// @ts-nocheck
// @ts-nocheck
export class VariableVersioning {
    constructor(connection) {
        if (!connection) {
            // No transaction here..
            this.connection = globalThis.database;
        } else {
            this.connection = connection;
        }
    }

    static fillInstancesIndependently(connection, tvarId) {
        const vv = new VariableVersioning(connection, tvarId);
        return vv.fillInstances(tvarId).catch(async (err) => {
            globalThis.tasLogger.error(
                `Can not fill variables to instance processes. ${err.message}`,
                {
                    err,
                    tvarId,
                },
            );
        });
    }

    /**
     * Add variables to all live processes.
     * @param tvarId
     */
    async fillInstances(tvarId: number) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `VERSIONING:VARIABLE_INSTANCES-${tvarId}`,
            24 * 60 * 60 * 1000,
        );

        try {
            const tvar = await globalThis.orm
                .repo("TemplateVariable", this.connection)
                .get(tvarId);

            // Get all processes with no instance variable.
            const versionedProcesses = await this.connection
                .distinct("IP.IPROC_ID", "IV.TVAR_ID")
                .from("INSTANCE_PROCESSES as IP")
                .leftJoin("INSTANCE_TASKS as IT", "IT.IPROC_ID", "IP.IPROC_ID")
                .leftJoin("INSTANCE_VARIABLES as IV", function () {
                    this.on("IV.IPROC_ID", "IP.IPROC_ID").andOn(
                        "IV.TVAR_ID",
                        globalThis.database.raw("?", tvar.TVAR_ID),
                    );
                })
                .where("IP.IPROC_STATUS", "A")
                .where("IP.TPROC_ID", tvar.TPROC_ID)
                .whereNull("IV.TVAR_ID");

            globalThis.tasLogger.info(
                `Filling '${tvar.TVAR_NAME}' (${versionedProcesses.length}) variables to instance processes.`,
            );
            const entities = [];

            if (!versionedProcesses.length) {
                return;
            }

            const varRepo = globalThis.orm.repo("variable", this.connection);
            await varRepo.alterSeqIncrement(
                varRepo._sequenceName,
                versionedProcesses.length,
            );
            const id = await varRepo.genID(varRepo._sequenceName);
            await varRepo.alterSeqIncrement(varRepo._sequenceName, 1);

            const ivarIds = [];
            for (let i = 0; i < versionedProcesses.length; i += 1) {
                const row = versionedProcesses[i];

                const ivarId = id - i;
                ivarIds.push(ivarId);
                entities.push(
                    Object.assign(
                        {
                            IVAR_ID: ivarId,
                            IPROC_ID: row.IPROC_ID,
                            IVAR_NAME: tvar.TVAR_NAME,
                            IVAR_TYPE: tvar.TVAR_TYPE,
                            IVAR_MULTITASK_BEHAVIOUR:
                                tvar.TVAR_MULTITASK_BEHAVIOUR,
                            IVAR_TEXT_VALUE: tvar.TVAR_TEXT_VALUE,
                            IVAR_NUMBER_VALUE: tvar.TVAR_NUMBER_VALUE,
                            IVAR_DATE_VALUE: tvar.TVAR_DATE_VALUE,
                            IVAR_ATTRIBUTE: tvar.TVAR_ATTRIBUTE,
                            TVAR_ID: tvar.TVAR_ID,
                            DLIST_NAME: tvar.DLIST_NAME,
                            IVAR_MULTI: tvar.TVAR_MULTI,
                            IVAR_BIG_VALUE: tvar.TVAR_BIG_VALUE,
                            ORG_ID: 1,
                            IVAR_COL_INDEX: tvar.TVAR_COL_INDEX,
                        },
                        ...globalThis.dynamicConfig.langs.map((lang) => ({
                            [`IVAR_NAME_${lang.toUpperCase()}`]:
                                tvar[`TVAR_NAME_${lang.toUpperCase()}`],
                        })),
                    ),
                );
            }

            await this.connection
                .batchInsert("INSTANCE_VARIABLES", entities, 10)
                .transacting(this.connection);

            // Generate tlovs.
            const varLovRepo = globalThis.orm.repo(
                "VariableLov",
                this.connection,
            );
            const tvarLovRepo = globalThis.orm.repo(
                "TemplateVariableLov",
                this.connection,
            );

            const tlovs = await tvarLovRepo
                .getForTemplateVariables("*", [tvarId])
                .fetchAll();
            if (tlovs.length > 0) {
                await varLovRepo.alterSeqIncrement(
                    varLovRepo._sequenceName,
                    tlovs.length * versionedProcesses.length + 1,
                );
                let lovId = await varLovRepo.genID(varLovRepo._sequenceName);
                await varLovRepo.alterSeqIncrement(varLovRepo._sequenceName, 1);
                const ilovs = [];

                for (const ivarId of ivarIds) {
                    for (const lov of tlovs) {
                        const ilov = {
                            ORG_ID: 1,
                            IVARLOV_ID: lovId,
                            IVAR_ID: ivarId,
                            IVARLOV_IS_SELECTED: lov.TVARLOV_IS_SELECTED,
                            IVARLOV_TEXT_VALUE: lov.TVARLOV_TEXT_VALUE,
                            IVARLOV_NUMBER_VALUE: lov.TVARLOV_NUMBER_VALUE,
                            IVARLOV_DATE_VALUE: lov.TVARLOV_DATE_VALUE,
                        };
                        lovId -= 1;
                        if (
                            Array.isArray(globalThis.dynamicConfig.langs) &&
                            globalThis.dynamicConfig.langs.length > 0
                        ) {
                            for (
                                let l = 0;
                                l < globalThis.dynamicConfig.langs.length;
                                l += 1
                            ) {
                                ilov[
                                    `IVARLOV_TEXT_VALUE_${globalThis.dynamicConfig.langs[l].toUpperCase()}`
                                ] =
                                    lov[
                                        `TVARLOV_TEXT_VALUE_${globalThis.dynamicConfig.langs[l].toUpperCase()}`
                                    ];
                            }
                        }
                        // Add translations
                        ilovs.push(ilov);
                    }
                }

                await this.connection
                    .batchInsert("INSTANCE_VARIABLE_LOV", ilovs, 10)
                    .transacting(this.connection);
            }

            globalThis.tasLogger.info(
                `Filling '${tvar.TVAR_NAME}' variables to instance processes done.`,
            );
        } finally {
            await lock.release();
        }
    }

    /**
     * Update instance variable definition by new tvar
     * @param {number} tvarId TvarId
     * @returns {}
     */
    async updateDefinition(tvarId) {
        const tvar = await globalThis.orm
            .repo("TemplateVariable", this.connection)
            .get(tvarId);
        return this.connection
            .select()
            .from("INSTANCE_VARIABLES")
            .where("TVAR_ID", tvarId)
            .whereIn("IPROC_ID", (it) => {
                it.select("IPROC_ID").from("INSTANCE_PROCESS_VARIABLES");
            })
            .update({
                IVAR_NAME: tvar.TVAR_NAME,
                IVAR_TYPE: tvar.TVAR_TYPE,
                IVAR_ATTRIBUTE: tvar.TVAR_ATTRIBUTE,
                DLIST_NAME: tvar.DLIST_NAME,
                IVAR_MULTI: tvar.TVAR_MULTI,
                IVAR_COL_INDEX: tvar.TVAR_COL_INDEX,
            });
    }
}
