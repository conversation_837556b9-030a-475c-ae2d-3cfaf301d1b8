// @ts-nocheck
// @ts-nocheck
import * as processConst from "../orm/entity/const/processConst";
import * as eventConst from "../orm/entity/const/eventConst";
import * as taskConst from "../orm/entity/const/taskConst";

export class TaskVersioning {
    constructor(connection) {
        if (!connection) {
            // No transaction here..
            this.connection = globalThis.database;
        } else {
            this.connection = connection;
        }
    }

    static fillInstancesIndependently(connection, ttaskId) {
        const vv = new TaskVersioning(connection, ttaskId);
        return vv.fillInstances(ttaskId).catch(async (err) => {
            globalThis.tasLogger.error(
                `Can not fill variables to instance processes. ${err.message}`,
                {
                    err,
                    ttaskId,
                },
            );
        });
    }

    /**
     * Add variables to all live processes.
     * @param ttaskId
     */
    async fillInstances(ttaskId) {
        const ttask = await globalThis.orm
            .repo("TemplateTask", this.connection)
            .get(ttaskId);

        const versionedProcesses = await this.connection
            .select("IP.IPROC_ID")
            .from("TEMPLATE_TASKS AS TT")
            .join("INSTANCE_PROCESSES AS IP", "IP.TPROC_ID", "TT.TPROC_ID")
            .leftJoin("INSTANCE_TASKS AS IT", (builder) => {
                builder
                    .on("IP.IPROC_ID", "IT.IPROC_ID")
                    .andOn("IT.TTASK_ID", "TT.TTASK_ID");
            })
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            )
            .where("TT.TTASK_ID", ttaskId)
            .whereNotNull("IPV.IPROC_ID")
            .whereNull("IT.ITASK_ID")
            .where("IP.IPROC_STATUS", processConst.STATUS_ACTIVE);

        globalThis.tasLogger.info(
            `Filling '${ttask.TTASK_NAME}' (${versionedProcesses.length}) variables to instance processes.`,
        );
        const itaskRepo = globalThis.orm.repo("instanceTask", this.connection);

        if (versionedProcesses.length === 0) {
            return true;
        }
        await itaskRepo.alterSeqIncrement(
            itaskRepo._sequenceName,
            versionedProcesses.length,
        );
        const id = await itaskRepo.genID(itaskRepo._sequenceName);
        await itaskRepo.alterSeqIncrement(itaskRepo._sequenceName, 1);

        const taskEntities = [];
        for (let i = 0; i < versionedProcesses.length; i += 1) {
            const row = versionedProcesses[i];
            const entity = {
                IPROC_ID: row.IPROC_ID,
                ORG_ID: 1,
                ITASK_ID: id - i,
                TTASK_ID: ttaskId,
                ITASK_NAME: ttask.TTASK_NAME,
                ITASK_DESCRIPTION: ttask.TTASK_DESCRIPTION,
                ITASK_DUE_OFFSET: ttask.TTASK_DUE_OFFSET,
                ITASK_DURATION: ttask.TTASK_DURATION,
                ITASK_ASSESMENT_ROLE_ID: ttask.TTASK_ASSESMENT_ROLE_ID,
                ITASK_ASSESMENT_HIERARCHY: ttask.TTASK_ASSESMENT_HIERARCHY,
                ITASK_ASSESMENT_METHOD:
                    ttask.TTASK_ASSESMENT_METHOD ||
                    taskConst.ASSESMENT_METHOD_OWNER,
                ITASK_ASSESMENT_USER_ID: ttask.TTASK_ASSESMENT_USER_ID,
                ITASK_ASSESMENT_ORGSTR_ID: ttask.TTASK_ASSESMENT_ORGSTR_ID,
                ITASK_ASSESMENT_TTASK_ID: ttask.TTASK_ASSESMENT_TTASK_ID,
                ITASK_PETRI_NET_INPUT: ttask.TTASK_PETRI_NET_INPUT,
                ITASK_SUFFICIENT_END: ttask.TTASK_SUFFICIENT_END,
                ITASK_TYPE: ttask.TTASK_TYPE,
                ITASK_ASSESMENT_ORGSTR_CNST: ttask.TTASK_ASSESMENT_ORGSTR_CNST,
                ITASK_EVENT: ttask.TTASK_EVENT,
                ITASK_EVENT_WAIT: ttask.TTASK_EVENT_WAIT,
                ITASK_RUN_ONLY_ONCE: ttask.TTASK_RUN_ONLY_ONCE,
                ITASK_DISC_FLAG: ttask.TTASK_DISC_FLAG,
                ITASK_MULTIINSTANCE_FLAG: ttask.TTASK_MULTIINSTANCE_FLAG,
                ITASK_ASSESMENT_TVAR_ID: ttask.TTASK_ASSESMENT_TVAR_ID,
                ITASK_DUTY: ttask.TTASK_DUTY,
                ITASK_GEN_HISTORY: ttask.TTASK_GEN_HISTORY,
                ITASK_INVOKE_EVENT:
                    ttask.TTASK_INVOKE_EVENT || eventConst.INVOKE_EVENT_DEFAULT,
                ITASK_ITERATE_OVER: ttask.TTASK_ITERATE_OVER,
                ITASK_REFERENCE_USER: ttask.TTASK_REFERENCE_USER,
                ITASK_AGAIN: ttask.TTASK_AGAIN,
                ITASK_AUTO_START: taskConst.AUTOSTART_NO,
                ITASK_STATUS: taskConst.STATUS_NEW,
            };
            taskEntities.push(entity);
        }

        // Create instance_tasks
        await this.connection
            .batchInsert("INSTANCE_TASKS", taskEntities, 10)
            .transacting(this.connection);

        // Assign rights
        await this.assignRights(ttask.TTASK_ID);

        // Copy graph
        const graphRepo = globalThis.orm.repo("instanceGraph", this.connection);
        await graphRepo.copyGraphForTTaskId(ttask.TTASK_ID);

        // Copy Invitation and Mail notif
        for (const itask of taskEntities) {
            // Copy MAIL NOTIFICATIONS
            if (itask.ITASK_TYPE === taskConst.TYPE_EMAIL_NOTIFICATION) {
                const map = {};
                map[ttaskId] = {
                    ITASK_ID: itask.ITASK_ID,
                    ITASK_TYPE: taskConst.TYPE_EMAIL_NOTIFICATION,
                };
                await globalThis.orm
                    .repo("InstanceTaskEmailNotifs", this.connection)
                    .copyForTemplateTasks(map);
            }

            // Copy MAIL INVITATIONS
            if (itask.ITASK_TYPE === taskConst.TYPE_INVITATION) {
                const map = {};
                map[ttaskId] = {
                    ITASK_ID: itask.ITASK_ID,
                    ITASK_TYPE: taskConst.TYPE_INVITATION,
                };
                await globalThis.orm
                    .repo("InstanceTaskInvitation", this.connection)
                    .copyForTemplateTasks(map);
            }
        }
        globalThis.tasLogger.info(
            `Filling '${ttask.TTASK_NAME}' variables to instance processes done.`,
        );
    }

    assignRights(ttaskId) {
        return globalThis.container.client.database.callKnexRaw(
            `
            INSERT INTO "INSTANCE_PROCESS_STATIC_RIGHTS" ("IPROC_ID", "USER_ID")
SELECT ip1."IPROC_ID",
       ip1."USER_ID"
FROM (
    SELECT "IPROC_ID",
           "ITASK_ASSESMENT_USER_ID" AS "USER_ID"
    FROM "INSTANCE_TASKS"
    WHERE "ITASK_ASSESMENT_USER_ID" IS NOT NULL
      AND "TTASK_ID" = :ttask_id
) AS ip1
WHERE NOT EXISTS (
    SELECT 1
    FROM "INSTANCE_PROCESS_STATIC_RIGHTS" AS ip2
    WHERE ip2."IPROC_ID" = ip1."IPROC_ID"
      AND ip2."USER_ID" = ip1."USER_ID"
);
 `,
            { ttask_id: ttaskId },
            this.connection,
        );
    }
}
