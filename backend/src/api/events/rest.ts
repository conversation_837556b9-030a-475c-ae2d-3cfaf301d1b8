// @ts-nocheck
// @ts-nocheck
import { EvalMath<PERSON><PERSON> } from "../../entrypoint/calculation/EvalMathApi";
import { IVariableApi } from "../../entrypoint/calculation/IVariableApi";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import request from "request";
import { parseStringPromise } from "xml2js";
import iconv from "iconv-lite";
import zlib from "zlib";
import { EventHandler } from "./eventHandler";
import { CalcBinding } from "../utils/CalcBinding";
import { LogCategory } from "../../utils/logger/logConsts";

export class Rest extends EventHandler {
    constructor(orm, currentUser) {
        super(orm, currentUser);

        this.parameters = {
            url: null,
            method: null,
            body: null,
            headers: null,
            binding:
                "vars['error'].setValue(@error)\nvars['myTestVar'].setValue(Holder.getTextProperty('definitions.types.0.xsd'))",
        };
    }

    getDescription() {
        return {
            name: "Rest connector",
            description: "Generic Rest connector for any RestService.",
        };
    }

    getParameterList() {
        return {
            url: "Rest url",
            method: "Rest method get or post",
            body: "Rest body in json format",
            headers: "Rest headers in json string",
            binding: "Calc binding. ",
        };
    }

    async call() {
        return globalThis.tasLogger.runTask(async () => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_EVENTS,
            );
            globalThis.tasLogger.info("Rest event call.", {
                iproc_id: this.process.IPROC_ID,
            });

            // Fill soap params with real process variables.
            let result = null;
            try {
                const headers = JSON.parse(await this.getParameter("headers"));
                let body = null;

                // XML or JSON ?
                const contentType = this.getContentType(headers);
                if (contentType === "xml") {
                    body = await this.getParameter("body");
                    const vars = this.getProcessVariables();
                    if (body) {
                        // Replace {vars} in body
                        for (const variable of vars) {
                            const { value } = variable;
                            // You should sanitize your variable via calculations before insert as param.

                            // Replace {variable} values.
                            if (body.replace) {
                                body = body.replace(
                                    new RegExp(`{${variable.IVAR_NAME}}`, "g"),
                                    value,
                                );
                            } else {
                                throw new InternalException(
                                    `SoapParams (body parameter) has not replace method. SoapParams=${body}`,
                                );
                            }
                        }
                    }

                    // Search for files ...
                    body = await this.attachDocuments(body, headers); // Add files from DMS if somewhere in body is @file[variable_name]
                } else {
                    const bodyFromEventConfig = await this.getParameter("body");
                    body = bodyFromEventConfig ? bodyFromEventConfig : {};
                }

                body = body || ""; // Always be at least string

                // Call rest.
                const url = await this.getParameter("url", true);
                const method = await this.getParameter("method");

                try {
                    result = await this.callRest(url, method, body, headers);
                    globalThis.tasLogger.info("Rest event call result.", {
                        iproc_id: this.process.IPROC_ID,
                        url,
                        method,
                    });
                    globalThis.tasLogger.debug(
                        "Rest event call result - more details",
                        {
                            iproc_id: this.process.IPROC_ID,
                            url,
                            method,
                            body,
                            headers,
                            result,
                        },
                    );
                } catch (err) {
                    globalThis.tasLogger.error(
                        `Rest event call result error: ${err.message}`,
                        {
                            iproc_id: this.process.IPROC_ID,
                            url,
                            method,
                            body,
                            headers,
                            err,
                        },
                    );
                }

                let responseHeaders = {};
                if (Array.isArray(result) && result.length === 2) {
                    responseHeaders = result[1];
                    result = result[0];
                }

                let multipart = false;
                if (
                    responseHeaders["content-type"] &&
                    responseHeaders["content-type"].indexOf("boundary") !== -1
                ) {
                    multipart = true;
                }

                // Response is XML ?
                const isErrorMessage =
                    typeof result === "object" && result && result.soap_error;
                if (this.getAcceptType(headers) === "xml" && !isErrorMessage) {
                    if (multipart) {
                        // TODO multipart-parser (this is botas fix)
                        const data = result.split("\r\n");
                        result = data[data.length - 2];
                    }
                    result = await parseStringPromise(result);
                }
            } catch (err) {
                globalThis.tasLogger.error(
                    `Rest event call error: '${err.message}'`,
                    {
                        iproc_id: this.process.IPROC_ID,
                        err,
                    },
                );

                result = {
                    soap_error: err.message,
                };
            }

            // Process event binding.
            try {
                const calcBinding = new CalcBinding(
                    this.orm,
                    this.currentUser,
                    null,
                );
                calcBinding.process = this.process;
                calcBinding.setVariables(this.getProcessVariables());
                await calcBinding.calculate(
                    await this.getParameter("binding"),
                    result,
                ); // Assign result into process variables.
            } catch (err) {
                globalThis.tasLogger.error(
                    `Rest event binding error: ${err.message}`,
                    {
                        iproc_id: this.process.IPROC_ID,
                        err,
                        binding: await this.getParameter("binding"),
                        result,
                    },
                );
                throw err;
            }
            return result;
        });
    }

    async attachDocuments(body, headers) {
        try {
            if (!headers || !headers["Content-Type"]) {
                return body;
            }
            const boundary = this.getHeaderBoundary(headers["Content-Type"]);
            if (!boundary) {
                // No boundary no multipart
                return body;
            }

            if (typeof body !== "string") {
                return body;
            }

            const regexMatchFile = /@file\[(.*?)\]/;
            const matches = body.match(regexMatchFile);
            if (!matches || matches.length < 2) {
                return body;
            }

            // TODO handle multi attachment.
            const fileVarName = matches[1];
            const fileVar = this.getProcessVariableByname(fileVarName);
            if (!fileVar) {
                globalThis.tasLogger.warning(
                    `Can not find variable '${fileVarName}' in Rest event.`,
                    {
                        iproc_id: this.process.IPROC_ID,
                    },
                );
                return body;
            }

            const fileName = fileVar.value[0];
            const regexFileName = /@file-name\[(.*?)\]/g;
            body = body.replace(regexFileName, fileName);

            body = body.replace(regexMatchFile, "");
            const fileStream = await this.getFileStream(fileVar);
            if (!fileStream) {
                globalThis.tasLogger.warning(
                    `No file attached in Rest. There is only empty variable '${fileVarName}'. Please select at least one file to append as multipart.`,
                    {
                        iproc_id: this.process.IPROC_ID,
                    },
                );
                return body;
            }

            // octet-stream as default, TODO use attachment type...
            // const type = await this.getAttachmentContentType(fileVar);

            body += `--${boundary}\r\n`;
            body += `Content-Disposition: attachment; name="file"; filename="${fileName}"\r\n`;
            body += "Content-Id: <123>\r\n";
            body += "Content-Type:application/octet-stream\r\n\r\n";

            const payload = Buffer.concat([
                Buffer.from(body, "utf8"),
                Buffer.from(fileStream, "binary"),
                Buffer.from(`\r\n--${boundary}--\r\n`, "utf8"), // TODO last -- only on last separator.
            ]);

            globalThis.tasLogger.info(
                "Rest payload changed bcs of attaching DMS file.",
                {
                    fileName,
                    iproc_id: this.process.IPROC_ID,
                },
            );
            headers["Content-Length"] = payload.length;

            return payload;
        } catch (err) {
            globalThis.tasLogger.warning(
                "Error while attaching file to payload.",
                {
                    iproc_id: this.process.IPROC_ID,
                    err,
                },
            );
            throw err;
        }
    }

    async callRest(url, method, body, headers) {
        const timeoutValue =
            (globalThis.dynamicConfig.tas.restEventTimeout || 60) * 1000;

        // Timeout method used for terminating a Rest request that exceeds its time limit
        const timeoutPromise = new Promise((_resolve, reject) => {
            const ms = timeoutValue;
            setTimeout(
                reject,
                ms,
                new Error(
                    `Rest request exceeded its timeout of ${ms} milliseconds and has been terminated.`,
                ),
            );
        });

        const contentType = this.getContentType(headers);
        const callPromise = new Promise(async (resolve) => {
            try {
                const data = [];
                const options = {
                    body,
                    headers,
                    timeout: timeoutValue,
                };
                if (contentType === "json") {
                    options.json = true;
                }

                request[method](url, options)
                    .on("response", (response) => {
                        response
                            .on("data", (chunk) => {
                                data.push(chunk);
                            })
                            .on("end", async () => {
                                let content = null;
                                const buffer = Buffer.concat(data);

                                if (
                                    this.getAcceptType(headers) ===
                                    "gzip,deflate"
                                ) {
                                    content = await new Promise((r1) => {
                                        zlib.gunzip(buffer, (e1, d1) => {
                                            if (e1) {
                                                globalThis.tasLogger.error(
                                                    "gunzip error on Rest event.",
                                                    {
                                                        e1,
                                                    },
                                                );
                                                return r1(
                                                    buffer.toString("utf-8"),
                                                );
                                            }
                                            return r1(d1.toString("utf-8"));
                                        });
                                    });
                                } else if (
                                    this.getCharsetType(headers) === "CP1250"
                                ) {
                                    const buf = iconv.decode(buffer, "CP1250");
                                    content = iconv
                                        .encode(buf, "UTF8")
                                        .toString();
                                } else {
                                    content = buffer.toString("utf8");
                                }

                                // Response is not OK.
                                if (response.statusCode !== 200) {
                                    const err = new Error(
                                        `Response is not 200 (OK). Failed with statusCode=${response.statusCode}`,
                                    );
                                    globalThis.tasLogger.error(
                                        `Rest event binding error: ${err.message}`,
                                        {
                                            iproc_id: this.process.IPROC_ID,
                                            err,
                                            url,
                                            body,
                                            response: content,
                                            method,
                                            headers,
                                        },
                                    );

                                    return resolve({
                                        soap_error: err.message,
                                    });
                                }

                                return resolve([content, response.headers]);
                            })
                            .on("error", async (err) => {
                                globalThis.tasLogger.error(
                                    `Rest event binding error: ${err.message}`,
                                    {
                                        iproc_id: this.process.IPROC_ID,
                                        err,
                                        url,
                                        body,
                                        method,
                                        headers,
                                        response,
                                    },
                                );
                                return resolve({
                                    soap_error: err.message,
                                });
                            });
                    })
                    .on("error", async (err) => {
                        globalThis.tasLogger.error(
                            `Rest event binding error: ${err.message}`,
                            {
                                iproc_id: this.process.IPROC_ID,
                                err,
                                url,
                                body,
                                method,
                                headers,
                            },
                        );

                        return resolve({
                            soap_error: err.message,
                        });
                    });
            } catch (err) {
                globalThis.tasLogger.error(
                    `Rest event binding error: ${err.message}`,
                    {
                        iproc_id: this.process.IPROC_ID,
                        err,
                        url,
                        body,
                        method,
                        headers,
                    },
                );
                return resolve({
                    soap_error: err.message,
                });
            }
        });

        // The first promise to resolve/reject wins
        return await Promise.race([timeoutPromise, callPromise]).catch(
            async (err) => {
                globalThis.tasLogger.error(
                    `Rest event binding error: ${err.message}`,
                    {
                        iproc_id: this.process.IPROC_ID,
                        err,
                        url,
                        body,
                        method,
                        headers,
                    },
                );
                return await {
                    soap_error: err.message,
                };
            },
        );
    }

    getFileStream(variable) {
        // const foo = async(() => {
        const api = new IVariableApi(this.orm.connection, {
            variable,
        });
        return api.getFileStream();
        // });
        // return foo();
    }

    getAttachmentContentType(variable) {
        // const foo = async(() => {
        const api = new EvalMathApi(globalThis.dynamicConfig.knex, {});
        return api.getFileType(variable.value[0]);
        // });
        // return foo();
    }

    getContentType(headers) {
        if (this.headerContains(headers, "content-type", "xml")) {
            return "xml";
        }
        if (this.headerContains(headers, "content-type", "json")) {
            return "json";
        }
        return null;
    }

    getCharsetType(headers) {
        if (this.headerContains(headers, "accept", "CP1250")) {
            return "CP1250";
        }
        return "utf-8";
    }

    getAcceptType(headers) {
        if (this.headerContains(headers, "accept", "gzip,deflate")) {
            return "gzip,deflate";
        }
        if (this.headerContains(headers, "accept", "xml")) {
            return "xml";
        }
        if (this.headerContains(headers, "accept", "json")) {
            return "json";
        }
        return null;
    }

    headerContains(headers, headerName, substring) {
        let contentType = null;
        if (headers && Object.keys(headers).length > 0) {
            Object.keys(headers).forEach((header) => {
                // Content type if defined
                if (header.toLowerCase().indexOf(headerName) !== -1) {
                    if (
                        headers[header]
                            .toLowerCase()
                            .indexOf(substring.toLowerCase()) !== -1
                    ) {
                        contentType = substring;
                    }
                }
            });
        }
        return contentType;
    }

    getHeaderBoundary(contentType) {
        // 'multipart/related; type="application/xop+xml"; start="rootpart"; start-info="text/xml"; boundary="----=_Part_0_7145370.1075485514903"'
        if (!typeof contentType === "string") {
            return null;
        }

        const regex = /boundary="(.*?)"/;
        const result = contentType.match(regex);
        if (result && result.length > 1) {
            const boundary = result[1];
            return boundary;
        }
        return null;
    }
}
