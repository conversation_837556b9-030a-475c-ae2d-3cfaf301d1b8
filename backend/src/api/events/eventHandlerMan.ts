import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import beautify from "js-beautify";
import { Rest } from "./rest";
import { EventHandler } from "./eventHandler";
import { IEventParam } from "../orm/entity/EventParam";

export class EventHandlerMan {
    public parameters: any;

    public orm: any;

    public currentUser: any;

    public moduleName?: string;

    public methodName: string = "call";

    public currentEventHandler?: EventHandler;

    constructor(orm: any, currentUser: any) {
        this.parameters = {};
        this.orm = orm;
        this.currentUser = currentUser;
    }

    /**
     * Load responsible module for current external event.
     * @param {string} externalEvent External module name including called method separated by :. See eventHGetHandlerList.
     */
    eventHSelectEventHandler(externalEvent: string): void {
        // Find module and method externalEvent.
        const arr = externalEvent.split(":");
        this.moduleName = `${arr[0]}`;

        switch (this.moduleName) {
            case "Rest":
                this.currentEventHandler = new Rest(this.orm, this.currentUser);
                break;
            default:
                throw new Error("Unsupported event");
        }
    }

    /**
     * Set rule variables to event context.
     * @param ruleVariables
     */
    eventHSetRuleVariables(ruleVariables: any) {
        if (Array.isArray(ruleVariables) && ruleVariables.length > 0) {
            for (const ruleVar of ruleVariables) {
                if (ruleVar.RDEFVAR_NAME === "binding") {
                    // Format js param.
                    ruleVar.RDEFVAR_VALUE = beautify.js(ruleVar.RDEFVAR_VALUE);
                }
                this.currentEventHandler!.setParameter(
                    ruleVar.RDEFVAR_NAME,
                    ruleVar.RDEFVAR_VALUE,
                );
            }
        }
    }

    /**
     * Set event params.
     * @param {Array<EventParam>} eventParams
     */
    eventHSetEventParams(eventParams: IEventParam[]) {
        this.currentEventHandler?.setEventParams(eventParams);
    }

    /**
     * Set variables from process to event context.
     * @param processVariables
     */
    eventHSetProcessVariables(processVariables: any) {
        this.currentEventHandler?.setProcessVariables(processVariables);
    }

    /**
     * Set process to event context.
     * @param process
     */
    eventHSetProcess(process: any) {
        this.currentEventHandler!.process = process;
    }

    /**
     * Get parameter list with their values.
     * @returns {Object} {values: {param: value}, parameters: {param1: description1}}
     */
    async eventHGetParameterList() {
        const out: any = {};

        out.values = {};
        out.parameters = this.currentEventHandler?.getParameterList();
        const paramKeys = Object.keys(out.parameters);
        if (Array.isArray(paramKeys) && paramKeys.length > 0) {
            for (let i = 0; i < paramKeys.length; i += 1) {
                const paramName = paramKeys[i];
                out.values[paramName] =
                    await this.currentEventHandler?.getParameter(paramName);
            }
        }
        return out;
    }

    /**
     * Call method previosly defined by eventHSelectEventHandler
     */
    async eventHCall() {
        // Check if handler was selected.
        if (!this.currentEventHandler) {
            throw new InternalException(
                "You must set event handler via eventHSelectEventHandler.",
                "NO_EVENT_HANDLER",
            );
        }

        // Check if defined method exists. Default is 'call'
        // @ts-expect-error
        if (!this.currentEventHandler[this.methodName]) {
            throw new InternalException(
                `${this.moduleName} does not have ${this.methodName} method.`,
                "UNKNOWN_EVENT_METHOD",
            );
        }

        // Call external events method.
        try {
            // @ts-expect-error
            return await this.currentEventHandler[this.methodName]();
        } catch (err: any) {
            globalThis.tasLogger.error(err.message, {
                err,
            });
            return null;
        }
    }
}
