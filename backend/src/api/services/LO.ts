// @ts-nocheck
// @ts-nocheck
import fs from "fs";
import path from "path";
import exec from "exec-sh";

import tmp from "tmp";
import { UtilsService } from "./UtilsService";

function generateRandomId(length) {
    const characters =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let randomId = "";
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        randomId += characters.charAt(randomIndex);
    }
    return randomId;
}

export const convert = async (document, format, filter, callback) => {
    const convertProcessId = generateRandomId(20);

    try {
        let paths = [];
        switch (process.platform) {
            case "darwin":
                paths = [
                    "/Applications/LibreOffice.app/Contents/MacOS/soffice",
                ];
                break;
            case "linux":
                paths = ["/usr/bin/libreoffice", "/usr/bin/soffice"];
                break;
            case "win32":
                paths = [
                    path.join(
                        process.env["PROGRAMFILES(X86)"],
                        "LIBREO~1/program/soffice.com",
                    ),
                    path.join(
                        process.env["PROGRAMFILES(X86)"],
                        "LibreOffice/program/soffice.com",
                    ),
                    path.join(
                        process.env.PROGRAMFILES,
                        "LibreOffice/program/soffice.com",
                    ),
                ];
                break;
            default:
                throw new Error(
                    `Operating system not yet supported: ${process.platform}`,
                );
        }

        const soffice = await findPath(paths);

        const [tempDir, cleanupCallback] = await new Promise(
            (resolve, reject) => {
                tmp.dir({ unsafeCleanup: true }, (err, path, cleanup) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve([path, cleanup]);
                    }
                });
            },
        );

        try {
            const sourcePath = path.join(tempDir, "source");
            await fs.promises.writeFile(sourcePath, document, "utf8");

            globalThis.tasLogger.info(
                `LibreOffice process with id ${convertProcessId} is now preparing.`,
            );

            let command = `${soffice} --headless --convert-to ${format}`;
            if (filter !== undefined) {
                command += `:"${filter}"`;
            }
            command += ` --outdir ${tempDir} ${sourcePath}`;

            const tmpLoInstanceProfile = await UtilsService.tmpDir({
                unsafeCleanup: true,
            });
            const loInstanceProfile = path.resolve(tmpLoInstanceProfile.path);
            let loProfileParam = `-env:UserInstallation=file:///${loInstanceProfile}`;
            loProfileParam = loProfileParam.replace(/\\/g, "/");
            loProfileParam = UtilsService.quoteExecParam(loProfileParam);
            command += ` --norestore ${loProfileParam} --safe-mode`;

            globalThis.tasLogger.info(
                `LibreOffice process with id ${convertProcessId} is now converting.`,
                { command },
            );

            const result = await exec.promise(command, {
                timeout:
                    globalThis.dynamicConfig.tas.calculations.libreOffice
                        .timeout || 30000,
            });

            if (
                result.stderr &&
                !result.stderr.includes(
                    "libpng warning: iCCP: known incorrect sRGB profile libpng warning",
                )
            ) {
                throw new Error(result.stderr);
            }

            globalThis.tasLogger.info(
                `LibreOffice process with id ${convertProcessId} conversion complete.`,
                { result },
            );

            const destinationPath = path.join(tempDir, `source.${format}`);
            const data = await retry(
                () => fs.promises.readFile(destinationPath),
                3,
                200,
            );

            cleanupCallback();
            callback(null, data);
        } catch (err) {
            globalThis.tasLogger.error(
                `LibreOffice failed with id ${convertProcessId}`,
                err,
            );
            cleanupCallback();
            callback(err);
        }
    } catch (err) {
        globalThis.tasLogger.error(
            `LibreOffice failed with id ${convertProcessId}`,
            err,
        );
        callback(err);
    }
};

async function findPath(paths) {
    for (const p of paths) {
        try {
            await fs.promises.access(p);
            return process.platform === "win32" ? `"${p}"` : p;
        } catch (err) {
            throw err;
        }
    }
    throw new Error("Could not find soffice binary");
}

async function retry(fn, attempts = 3, delay = 200) {
    for (let i = 0; i < attempts; i++) {
        try {
            return await fn();
        } catch (err) {
            if (i === attempts - 1) {
                throw err;
            }
            await new Promise((r) => setTimeout(r, delay));
        }
    }
}
