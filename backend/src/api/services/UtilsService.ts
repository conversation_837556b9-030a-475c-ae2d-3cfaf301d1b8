// @ts-nocheck
import fs from "fs";
import path from "path";
import PDFParser from "pdf2json";
import tmp from "tmp";
import child_process from "child_process";
import * as babel from "@babel/core";
import https from "https";
import { AsyncParser } from "@json2csv/node";
import _ from "lodash";
import truncate from "truncate-utf8-bytes";
import { constants } from "os";
import moment from "moment";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { AuthException } from "../../utils/errorHandling/exceptions/authException";
import { BaseApi } from "../../entrypoint/calculation/BaseApi";
import { FastifyRequest } from "fastify";

const sleep = async (ms: number) =>
    await new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Return a random int, used by `utils.uid()`
 *
 * @param {Number} min
 * @param {Number} max
 * @return {Number}
 * @api private
 */

function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

export class UtilsService {
    static cancelActiveQuery = async (
        requestId,
        backendApplicationSequence,
    ) => {
        if (!backendApplicationSequence) {
            throw new UserException(
                "The mandatory parameter backendApplicationSequence was not supplied!",
                "BAD_INPUT",
            );
        }
        // Is a non-scaled instance without REDIS with a possible CRON process
        if (process.env.REDIS_BASE === "false" || !process.env.REDIS_BASE) {
            try {
                if (
                    globalThis.backendApplicationSequence !==
                    backendApplicationSequence
                ) {
                    return;
                }
                const activeRequests = globalThis.database.client.pool.used.map(
                    ({ resource }) => ({
                        resource,
                    }),
                );

                const requestToDestroy = _.find(activeRequests, {
                    resource: { __knexUid: requestId },
                });
                if (requestToDestroy) {
                    switch (globalThis.dynamicConfig.db.client) {
                        case "postgresql":
                        case "mssql":
                            /*
                            Pool release does not work properly on MSSQL, need to cancel the request directly.
                            It removes the request from the pool, but does not cancel it in the database

                            See: https://tediousjs.github.io/tedious/api-connection.html#function_cancel
                        */
                            if (requestToDestroy.resource.request) {
                                // Request is active and SQL is running, attempt to cancel it
                                requestToDestroy.resource.request.cancel();
                            } else {
                                // There is no active request, just an open transaction. Release it from the pool.
                                globalThis.database.client.pool.release(
                                    requestToDestroy.resource,
                                );
                            }
                            break;
                        default:
                            throw new InternalException(
                                `Unsupported DB dialect for cancelActiveQuery: '${globalThis.dynamicConfig.db.client}'`,
                            );
                    }
                    return true;
                }
                globalThis.tasLogger.warning(
                    "No such active query was found!",
                    {
                        requestId,
                        activeRequests,
                        pid: process.pid,
                        backendApplicationSequence:
                            globalThis.backendApplicationSequence,
                    },
                );
                return false;
            } catch (err) {
                globalThis.tasLogger.warning(err.message, {
                    err,
                });
                return false;
            }
        } else {
            await globalThis.container.client.cache.pubSub.publish(
                "tas.scaling.health.database.activeRequests.cancel",
                { requestId },
            );
            return;
        }
    };

    static getActiveQueries = async () => {
        const info = {
            handlerCount: 0,
            queries: [],
        };
        // Is a non-scaled instance with a possible CRON process
        if (process.env.REDIS_BASE === "false" || !process.env.REDIS_BASE) {
            const requests = globalThis.database.client.pool.used;
            const activeRequests = requests.map(
                ({
                    resource: {
                        __knexUid,
                        request: {
                            parameters,
                            parametersByName,
                            sqlTextOrProcedure,
                        } = {},
                        __meta = {},
                    },
                    timestamp,
                }) => ({
                    parameters,
                    parametersByName,
                    query:
                        _.get(parametersByName, "statement.value") ||
                        sqlTextOrProcedure,
                    originProcessPid: process.pid,
                    backendApplicationSequence:
                        globalThis.backendApplicationSequence,
                    __knexUid,
                    timestamp,
                    iprocId: __meta.iproc_id,
                    itaskId: __meta.itask_id,
                }),
            );

            info.queries.push(...activeRequests);
            info.handlerCount += 1;
        } else {
            await globalThis.container.client.cache.pubSub.subscribe(
                "tas.scaling.health.database.activeRequests.response",
                async (queriesInfo) => {
                    info.queries.push(...queriesInfo);
                    info.handlerCount += 1;
                },
            );
            await globalThis.container.client.cache.pubSub.publish(
                "tas.scaling.health.database.activeRequests",
            );

            await new Promise(async (resolve) => {
                await sleep(1000);
                return resolve(info.handlerCount);
            });

            // Remove response handler
            await globalThis.container.client.cache.pubSub.unsubscribe(
                "tas.scaling.health.database.activeRequests.response",
            );
        }

        return info;
    };

    static isBase64 = (string) =>
        Buffer.from(string, "base64").toString("base64") === string;

    static exists = (path) =>
        new Promise((resolve) => {
            fs.access(path, (err) => resolve(!err));
        });

    static uid = function uid(len) {
        const buf = [];
        const chars =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        const charlen = chars.length;

        for (let i = 0; i < len; i += 1) {
            buf.push(chars[getRandomInt(0, charlen - 1)]);
        }

        return buf.join("");
    };

    static uidLight = function uidLight(len) {
        const buf = [];
        const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        const charlen = chars.length;

        for (let i = 0; i < len; i += 1) {
            buf.push(chars[getRandomInt(0, charlen - 1)]);
        }

        return buf.join("");
    };

    static genPassc = function genPassc(len) {
        const buf = [];
        const chars = "abcdefghjkmnopqrstuvwxyz";
        const charlen = chars.length;

        for (let i = 0; i < len; i += 1) {
            buf.push(chars[getRandomInt(0, charlen - 1)]);
        }

        return buf.join("");
    };

    static /**
     * Returns true if the string is valid date.
     *
     * @param dateObject
     */ validateDate = function validateDate(dateObject) {
        if (Object.prototype.toString.call(dateObject) === "[object Date]") {
            // it is a date
            if (isNaN(dateObject.getTime())) {
                // date is not valid
                return false;
            }

            // date is valid
            return true;
        }

        // not a date
        return false;
    };

    static recursivelyLowercaseJSONKeys = function recursivelyLowercaseJSONKeys(
        input,
    ) {
        const _typeof =
            typeof Symbol === "function" && typeof Symbol.iterator === "symbol"
                ? function (obj) {
                      return typeof obj;
                  }
                : function (obj) {
                      return obj &&
                          typeof Symbol === "function" &&
                          obj.constructor === Symbol
                          ? "symbol"
                          : typeof obj;
                  };

        const isArray = function isArray(obj) {
            return Object.prototype.toString.call(obj) === "[object Array]";
        };

        if (
            (typeof input === "undefined" ? "undefined" : _typeof(input)) !==
                "object" ||
            input === null
        ) {
            return input;
        }
        if (input instanceof Date) {
            return input;
        }
        if (isArray(input)) {
            return input.map((o) =>
                UtilsService.recursivelyLowercaseJSONKeys(o),
            );
        }

        return Object.keys(input).reduce((prev, curr) => {
            const param = prev;
            param[curr.toLowerCase()] =
                UtilsService.recursivelyLowercaseJSONKeys(input[curr]);
            return param;
        }, {});
    };

    static isIOSAgent = function isIOSAgent(agent) {
        if (!agent) {
            return false;
        }

        const iDevices = ["iPad", "iPhone", "iPod"];

        for (let i = 0; i < iDevices.length; i += 1) {
            if (agent.indexOf(iDevices[i]) !== -1) {
                return true;
            }
        }

        return false;
    };

    static trimChar = function trimChar(string, charToRemove) {
        const trimChars = charToRemove.split("");
        for (let i = 0; i < trimChars.length; i += 1) {
            trimChars[i] = trimChars[i].replace(/[.?*+^$[\]\\(){}|-]/g, "\\$&");
        }
        const trimString = trimChars.join("|"); // Add or operator !
        const regex = new RegExp(`^[${trimString}]+|[${trimString}]+$`, "g");

        return string.replace(regex, "");
    };

    static isAndroidAgent = function (agent) {
        if (!agent) {
            return false;
        }

        return /(android)/i.test(agent);
    };

    static /** matchRecursiveRegExp
    Accepts a string to search, a left and right format delimiter
    as regex patterns, and optional regex flags. Returns an array
    of matches, allowing nested instances of left/right delimiters.
    Use the "g" flag to return all matches, otherwise only the
    first is returned. Be careful to ensure that the left and
    right format delimiters produce mutually exclusive matches.
    Backreferences are not supported within the right delimiter
    due to how it is internally combined with the left delimiter.
    When matching strings whose format delimiters are unbalanced
    to the left or right, the output is intentionally as a
    conventional regex library with recursion support would
    produce, e.g. "<<x>" and "<x>>" both produce ["x"] when using
    "<" and ">" as the delimiters (both strings contain a single,
    balanced instance of "<x>").

    examples:
        matchRecursiveRegExp("test", "\\(", "\\)")
            returns: []
        matchRecursiveRegExp("<t<<e>><s>>t<>", "<", ">", "g")
            returns: ["t<<e>><s>", ""]
        matchRecursiveRegExp("<div id=\"x\">test</div>", "<div\\b[^>]*>", "</div>", "gi")
            returns: ["test"]

*/ matchRecursiveRegExp = // http://blog.stevenlevithan.com/archives/javascript-match-recursive-regexp // MIT License // (c) 2007 Steven Levithan <stevenlevithan.com>
        function matchRecursiveRegExp(str, left, right, flags) {
            const f = flags || "";
            const g = f.indexOf("g") > -1;
            const x = new RegExp(`${left}|${right}`, f);
            const l = new RegExp(left, f.replace(/g/g, ""));
            const a = [];
            let t;
            let s;
            let m;

            do {
                t = 0;
                while ((m = x.exec(str))) {
                    if (l.test(m[0])) {
                        if (!t++) {
                            s = x.lastIndex;
                        }
                    } else if (t) {
                        if (!--t) {
                            a.push(str.slice(s, m.index));
                            if (!g) {
                                return a;
                            }
                        }
                    }
                }
            } while (t && (x.lastIndex = s));

            return a;
        };

    static /**
     * Adjusts number value in case of incorrect rounding.
     * @param {number} num
     * @param scale
     * @return {number} fixed number
     */ roundNumber = function roundNumber(num, scale) {
        const factor = Math.pow(10, scale);
        const correction = 1 / (factor * 1000);
        return Math.round((num + correction) * factor) / factor;
    };

    static isMobilePhoneAgent = function (agent) {
        if (!agent) {
            return false;
        }

        return /(windows phone)/i.test(agent);
    };

    static walk = (dir, recursively = true): Array<string> => {
        let results = [];
        const list = fs.readdirSync(dir);
        list.forEach((file) => {
            const filePath = `${dir}/${file}`;
            const stat = fs.statSync(filePath);
            if (stat && stat.isDirectory() && recursively) {
                results = results.concat(UtilsService.walk(filePath));
            } else {
                results.push(filePath);
            }
        });
        return results;
    };

    static /*
     * PHP => moment.js
     *
     * http://www.php.net/manual/en/function.date.php
     * http://momentjs.com/docs/#/displaying/format/
     * https://gist.github.com/NTICompass/9375143
     * https://gist.github.com/phpmypython/f97c5f5f59f2a934599d
     * usage example: moment(date).formatPHP(phpFormat);
     */ phpDateTimeToMomentFormat = function phpDateTimeToMomentFormat(
        date,
        phpFormat,
    ) {
        (function (m) {
            const formatMap = {
                d: "DD",
                D: "ddd",
                j: "D",
                l: "dddd",
                N: "E",
                S() {
                    return `[${this.format("Do").replace(/\d*/g, "")}]`;
                },
                w: "d",
                z() {
                    return this.format("DDD") - 1;
                },
                W: "W",
                F: "MMMM",
                m: "MM",
                M: "MMM",
                n: "M",
                t() {
                    return this.daysInMonth();
                },
                L() {
                    return this.isLeapYear() ? 1 : 0;
                },
                o: "GGGG",
                Y: "YYYY",
                y: "YY",
                a: "a",
                A: "A",
                B() {
                    const thisUTC = this.clone().utc();
                    // Shamelessly stolen from http://javascript.about.com/library/blswatch.htm
                    const swatch =
                        ((thisUTC.hours() + 1) % 24) +
                        thisUTC.minutes() / 60 +
                        thisUTC.seconds() / 3600;
                    return Math.floor((swatch * 1000) / 24);
                },
                g: "h",
                G: "H",
                h: "hh",
                H: "HH",
                i: "mm",
                s: "ss",
                u: "[u]", // not sure if moment has this
                e: "[e]", // moment does not have this
                I() {
                    return this.isDST() ? 1 : 0;
                },
                O: "ZZ",
                P: "Z",
                T: "[T]", // deprecated in moment
                Z() {
                    return parseInt(this.format("ZZ"), 10) * 36;
                },
                c: "YYYY-MM-DD[T]HH:mm:ssZ",
                r: "ddd, DD MMM YYYY HH:mm:ss ZZ",
                U: "X",
            };
            const formatEx = /[dDjlNSwzWFmMntLoYyaABgGhHisueIOPTZcrU]/g;

            m.fn.formatPHP = function (format) {
                const that = this;

                return this.format(
                    format.replace(formatEx, (phpStr) =>
                        typeof formatMap[phpStr] === "function"
                            ? formatMap[phpStr].call(that)
                            : formatMap[phpStr],
                    ),
                );
            };
        })(moment);

        return moment(date).formatPHP(phpFormat);
    };

    static /**
     * Counts real Oracle char bytes
     * @param  {[type]} str [description]
     * @return {[type]}     [description]
     */ byteLength = function byteLength(str) {
        if (!str) {
            return 0;
        }
        if (!isNaN(str)) {
            // Is number.
            str = `${str}`; // To string.
        }
        if (!str.charCodeAt) {
            globalThis.tasLogger.warning(
                "Invalid string in UtilsService.byteLength. Converting toString().",
                { str },
            );
            str = `${str}`;
        }
        // returns the byte length of an utf8 string
        let s = str.length;
        for (let i = str.length - 1; i >= 0; i--) {
            const code = str.charCodeAt(i);
            if (code > 0x7f && code <= 0x7ff) {
                s++;
            } else if (code > 0x7ff && code <= 0xffff) {
                s += 2;
            }
            if (code >= 0xdc00 && code <= 0xdfff) {
                i--;
            } // trail surrogate
        }
        return s;
    };

    static leftPad = function (str, len, ch) {
        const cache = [
            "",
            " ",
            "  ",
            "   ",
            "    ",
            "     ",
            "      ",
            "       ",
            "        ",
            "         ",
        ];

        // convert `str` to a `string`
        str += "";
        // `len` is the `pad`'s length now
        len -= str.length;
        // doesn't need to pad
        if (len <= 0) {
            return str;
        }
        // `ch` defaults to `' '`
        if (!ch && ch !== 0) {
            ch = " ";
        }
        // convert `ch` to a `string` cuz it could be a number
        ch += "";
        // cache common use cases
        if (ch === " " && len < 10) {
            return cache[len] + str;
        }
        // `pad` starts with an empty string
        let pad = "";
        // loop
        while (true) {
            // add `ch` to `pad` if `len` is odd
            if (len & 1) {
                pad += ch;
            }
            // divide `len` by 2, ditch the remainder
            len >>= 1;
            // "double" the `ch` so this operation count grows logarithmically on `len`
            // each time `ch` is "doubled", the `len` would need to be "doubled" too
            // similar to finding a value in binary search tree, hence O(log(n))
            if (len) {
                ch += ch;
            } else {
                // `len` is 0, exit the loop
                break;
            }
        }
        // pad `str`!
        return pad + str;
    };

    static isNumericString = function (val) {
        if (val === "0" || val === 0) {
            return true;
        }

        if (val === true || val === false) {
            return false;
        }

        if (!val || typeof val === "object") {
            return false;
        }

        if (typeof val === "string") {
            if (val.trim().length !== val.length) {
                return false;
            }
            const num = Number(val);
            return !isNaN(num) && num !== Infinity
                ? num == val &&
                      (val.charAt(0) !== "0" || `${num}`.length === val.length)
                : false; // compare e.g. '3.0' == 3
        }

        return !isNaN(val) && val !== Infinity;
    };

    static number_format = function (
        number,
        decimals,
        dec_point,
        thousands_sep,
    ) {
        const n = !isFinite(+number) ? 0 : +number;
        const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
        const sep = typeof thousands_sep === "undefined" ? "," : thousands_sep;
        const dec = typeof dec_point === "undefined" ? "." : dec_point;
        const toFixedFix = function (n, prec) {
            // Fix for IE parseFloat(0.55).toFixed(0) = 0;
            const k = 10 ** prec;
            return Math.round(n * k) / k;
        };
        const s = (prec ? toFixedFix(n, prec) : Math.round(n))
            .toString()
            .split(".");
        if (s[0].length > 3) {
            s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
        }
        if ((s[1] || "").length < prec) {
            s[1] = s[1] || "";
            s[1] += new Array(prec - s[1].length + 1).join("0");
        }
        return s.join(dec);
    };

    static objectGUID2string = function (e) {
        return e
            .toString("hex")
            .replace(
                /^(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)(..)$/,
                "{$4$3$2$1-$6$5-$8$7-$9$10-$11$12$13$14$15$16}",
            )
            .toUpperCase();
    };

    static bin2hex = function (s) {
        //  discuss at: http://locutus.io/php/bin2hex/
        // original by: Kevin van Zonneveld (http://kvz.io)
        // bugfixed by: Onno Marsman (https://twitter.com/onnomarsman)
        // bugfixed by: Linuxworld
        // improved by: ntoniazzi (http://locutus.io/php/bin2hex:361#comment_177616)
        //   example 1: bin2hex('Kev')
        //   returns 1: '4b6576'
        //   example 2: bin2hex(String.fromCharCode(0x00))
        //   returns 2: '00'

        let i;
        let l;
        let o = "";
        let n;

        s += "";

        for (i = 0, l = s.length; i < l; i++) {
            n = s.charCodeAt(i).toString(16);
            o += n.length < 2 ? `0${n}` : n;
        }

        return o;
    };

    static differenceArrays = (a, b) => {
        const out = [];

        const contains = (arr, item) => {
            for (const row of arr) {
                if (row.toString() === item.toString()) {
                    return true;
                }
            }
            return false;
        };

        a.forEach((aItem) => {
            if (!contains(b, aItem)) {
                out.push(aItem);
            }
        });

        return out;
    };

    static uniqueArrays = (a) => {
        const out = [];

        const contains = (arr, item) => {
            for (const row of arr) {
                if (row.toString() === item.toString()) {
                    return true;
                }
            }
            return false;
        };

        a.forEach((aItem) => {
            if (!contains(out, aItem)) {
                out.push(aItem);
            }
        });

        return out;
    };

    static depassword = function (req: FastifyRequest) {
        if (req.body) {
            for (const pro in req.body) {
                if (
                    pro === "password" ||
                    pro === "pass" ||
                    pro === "credentials"
                ) {
                    req.body[pro] = "******";
                }
            }
        }
    };

    static /**
     * Removes slashes from a string
     *
     * @param string
     * @returns {String}
     */ removeSlashes = (string, backslash = true) => {
        const slashesRegex = backslash ? /[\\,/]/g : /\//g;
        return string.replace(slashesRegex, "");
    };

    static demax = function (req: FastifyRequest) {
        if (req.body) {
            for (const pro in req.body) {
                if (
                    !_.isEmpty(req.body[pro]) &&
                    _.isString(req.body[pro]) &&
                    req.body[pro].length > 1000
                ) {
                    req.body[pro] =
                        `${req.body[pro].substring(0, 100)}... (too big to log)`;
                }
            }
        }
    };

    static isFileAllowed = (pathToFile, throwError = false) => {
        const pathToFileNormalized = path.normalize(pathToFile);
        // @t3b-1570 allowedExternalSources Chybné vyhodnocení '/' vs '\'
        const rawPathToFile = UtilsService.removeSlashes(pathToFileNormalized);
        const allowedSources: string[] = [
            ...globalThis.dynamicConfig.dms.security.allowedExternalSources,
            globalThis.dynamicConfig.paths.tmp,
        ];

        const isAllowed = allowedSources.some((source) => {
            const rawSource = UtilsService.removeSlashes(source);
            return rawPathToFile.indexOf(rawSource) === 0;
        });

        if (!isAllowed && throwError) {
            throw new InternalException(
                "Accessing this source is not allowed!",
                "BANNED_SOURCE",
                { source: pathToFileNormalized },
            );
        }

        return isAllowed;
    };

    static /**
     *
     * @param fileName
     * @returns {string}
     */ sanitizeFileName = (fileName) => {
        const patterns = {
            illegal: /[\/\?<>\\:\*\|"]/g,
            control: /[\x00-\x1f\x80-\x9f]/g,
            reserved: /^\.+$/,
            windowsReserved: /^(con|prn|aux|nul|com[0-9]|lpt[0-9])(\..*)?$/i,
            windowsTrailing: /[\. ]+$/,
            commas: /,/g,
            unicodeReplacement: /\uFFFD/g,
        };

        const sanitized = Object.values(patterns).reduce(
            (str, pattern) => str.replace(pattern, ""),
            fileName,
        );
        return truncate(sanitized, 255);
    };

    static basicAuth = (req) => {
        // Is there Authorization header in request ?
        const authHeader = req.headers.authorization;
        if (!authHeader) {
            throw new AuthException(
                "Missing Basic Authorization header in request.",
                "MISS_AUTH_HEADER",
            );
        }

        // Is there token in header
        if (authHeader.indexOf("Basic ") !== 0) {
            throw new AuthException(
                "Missing Basic Authorization header in request.",
                "MISS_AUTH_HEADER",
            );
        }

        // Check token
        const credentials64 = authHeader.substr(6);
        const credentials = Buffer.from(credentials64, "base64").toString();
        const credentialsArray = credentials.split(":");
        if (credentialsArray.length < 2) {
            throw new AuthException(
                "Missing Basic Authorization header in request.",
                "MISS_AUTH_HEADER",
            );
        }
        return credentialsArray;
    };

    static deleteFolderRecursive = function (folder) {
        if (fs.existsSync(folder)) {
            fs.readdirSync(folder).forEach((file) => {
                const curPath = path.join(folder, file);
                if (fs.lstatSync(curPath).isDirectory()) {
                    // recurse
                    UtilsService.deleteFolderRecursive(curPath);
                } else {
                    // delete file
                    fs.unlinkSync(curPath);
                }
            });
            fs.rmdirSync(folder);
        }
    };

    tmpFile = (options = {}) =>
        new Promise((resolve, reject) =>
            tmp.file(
                {
                    tmpdir: path.join(
                        globalThis.dynamicConfig.paths.tmp,
                        "generated",
                    ),
                    ...options,
                },
                (err, filePath, fd, cleanupCallback) => {
                    if (err) {
                        reject(err);
                    }
                    resolve({
                        path: filePath,
                        fd,
                        cleanupCallback,
                    });
                },
            ),
        );

    static tmpDir = (options = {}) =>
        new Promise((resolve, reject) =>
            tmp.dir(options, (err, dirPath, cleanupCallback) => {
                if (err) {
                    reject(err);
                }
                resolve({
                    path: dirPath,
                    cleanupCallback,
                });
            }),
        );

    static pdfToJson = (file) => {
        const pdfParser = new PDFParser();
        return Promise.race([
            new Promise((resolve, reject) => {
                pdfParser.on("pdfParser_dataError", reject);
                pdfParser.on("pdfParser_dataReady", resolve);
                pdfParser.loadPDF(file);
            }),
            new Promise((_resolve, reject) => {
                setTimeout(() => {
                    pdfParser.destroy();
                    reject(
                        new Error(
                            `UtilsService.pdfToJson method timeout of ${globalThis.dynamicConfig.tas.calculations.pdfToText.timeout}ms`,
                        ),
                    );
                }, globalThis.dynamicConfig.tas.calculations.pdfToText.timeout);
            }),
        ]);
    };

    static exec = (command, timeout) =>
        new Promise((resolve, reject) => {
            child_process.exec(command, { timeout }, (err, stdout) => {
                if (err) {
                    return reject(err);
                }
                return resolve(stdout);
            });
        });

    static tmpFile = (options = {}): any =>
        new Promise((resolve, reject) =>
            tmp.file(options, (err, filePath, fd, cleanupCallback) => {
                if (err) {
                    reject(err);
                }
                resolve({
                    path: filePath,
                    fd,
                    cleanupCallback,
                });
            }),
        );

    static quoteExecParam = (param) => {
        let quotedParam = "";
        switch (process.platform) {
            case "darwin":
            case "linux":
                quotedParam = param.replace(/'/g, "'\"'\"'");
                quotedParam = `'${quotedParam}'`;
                break;
            case "win32":
                // pod Win se v argumentu nesmi vyskytovat uvozovka a nelze ji nijak spolehlive escapovat
                // problem nastava u prikazu pro volani makra, kde nelze kombinovat uvozovky pro argumenty a mezery v argumentech
                // windows vysledny cmd argument rozdeli na nekolik dilcich argumentu, ktere LO nezpracuje
                // workaround je odebrani uvozovek v makru, protoze windows stejne uvozovky do LO nepreda, at jsou zadane jakkoli
                // z toho plyne, ze pod windows nelze v LO makru pouzit carku uvnitr argumentu makra, protoze slouzi jako oddelovac
                // slozitejsi konstrukce je tedy nutno resit custom makrem
                quotedParam = param.replace(/"/g, ""); // strip ";
                quotedParam = `"${quotedParam}"`;
                break;
            default:
                throw new Error(
                    `Operating system not yet supported: ${process.platform}`,
                );
        }
        return quotedParam;
    };

    static babelTransform = (data) =>
        new Promise((resolve, reject) => {
            babel.transform(
                data,
                {
                    presets: [
                        [
                            "@babel/preset-env",
                            {
                                targets: {
                                    // base on browserlist defaults+yPhantomJS 2.1
                                    android: "67",
                                    chrome: "73",
                                    edge: "17",
                                    firefox: "60",
                                    ie: "11",
                                    ios: "11.3",
                                    opera: "60",
                                    safari: "6", // due to Phantom 2.1
                                    // safari: '12', without Phantom
                                    samsung: "8.2",
                                },
                                modules: false,
                            },
                        ],
                        "@babel/preset-react",
                    ],
                },
                (err, compiled) => {
                    if (err) {
                        return reject(err);
                    }
                    return resolve(compiled.code);
                },
            );
        });

    /**
     * Returns translated column value
     * @param  {String}  userLang          e.g. 'cs' or 'en'
     * @param  {Object}  i                 entity
     * @param  {String}  key               column name
     * @param  {Boolean} useDefaultVal
     * @param  {String}  suffix
     * @param  {Boolean} appendDefaultChar
     * @return {String}
     */
    static checkLangMutation = (
        userLang,
        i,
        key,
        useDefaultVal,
        suffix = "",
        appendDefaultChar = false,
    ) => {
        if (
            typeof i[`${key}_${userLang}${suffix}`] !== "undefined" &&
            i[`${key}_${userLang}${suffix}`] !== null
        ) {
            let ret = i[`${key}_${userLang}${suffix}`];

            if (appendDefaultChar) {
                ret += `${appendDefaultChar}(${i[key]})`;
            }

            return ret;
        }
        if (
            useDefaultVal === true &&
            (typeof i[key] === "undefined" || i[key] === null)
        ) {
            return i[
                key.replace(key.charAt(0), key.charAt(0) === "t" ? "i" : "t")
            ]; // ttask -> itask || itask -> ttask
        }
        return i[key];
    };

    static remapAttrs = (obj, map) => {
        if (Array.isArray(obj)) {
            const out = [];
            for (let i = 0; i < obj.length; i += 1) {
                const item = UtilsService.remapAttrs(obj[i], map);
                out.push(item);
            }
            return out;
        }

        const keys = Object.keys(obj);
        for (let i = 0; i < keys.length; i += 1) {
            const key = keys[i];
            const prefix = key.substr(0, key.indexOf("_") + 1);
            if (map[prefix]) {
                const newKey = key.replace(prefix, map[prefix]);

                obj[newKey] = obj[key];
                delete obj[key];
            }
        }
        return obj;
    };

    static toUpperCase = function toUpperCase(obj) {
        const keys = Object.keys(obj);
        const out = {};
        for (let i = 0; i < keys.length; i += 1) {
            out[keys[i].toUpperCase()] = obj[keys[i]];
        }
        return out;
    };

    static timeoutPromise = (msTimeout, description = "not defined") =>
        new Promise((_resolve, reject) => {
            setTimeout(
                reject,
                msTimeout,
                new Error(
                    `Job (${description}) exceeded its timeout of ${msTimeout} milliseconds and has been terminated.`,
                ),
            );
        });

    static timeout = (job, msTimeout, description) =>
        Promise.race([
            UtilsService.timeoutPromise(msTimeout, description),
            job,
        ]);

    static httpsGet = function (options) {
        return new Promise((resolve, reject) => {
            try {
                const data = [];
                https.get(options, (res) => {
                    res.on("data", (chunk) => {
                        data.push(chunk);
                    });
                    res.on("end", () => {
                        const body = Buffer.concat(data);
                        return resolve(body);
                    });
                    res.on("error", (err) => {
                        reject(err);
                    });
                });
            } catch (e) {
                return reject(e);
            }
        });
    };

    static killProcess = async (pid) => {
        // Windows
        if (process.platform === "win32") {
            await UtilsService.exec(`taskkill /F /PID ${pid}`);
        } else {
            // Kill the child process, no mercy on Linux
            await UtilsService.exec(
                `kill -${constants.signals.SIGKILL} ${pid}`,
            );
        }
    };

    static json2csv = (data, opts) => {
        const transformOpts = {};
        const asyncOpts = {};
        const parser = new AsyncParser(opts, transformOpts, asyncOpts);
        return parser.parse(data).promise();
    };

    static getSecret = (key) => {
        // local.js secret
        const secret = globalThis.dynamicConfig.secrets[key];
        if (typeof secret !== "undefined") {
            return secret;
        }

        // dynamicSecret saved by lib.setSecret
        try {
            const dynamicSecrets = JSON.parse(
                globalThis.dynamicConfig.dynamicSecrets,
            );
            const dynamicSecret = dynamicSecrets[key];
            if (typeof dynamicSecret !== "undefined") {
                return dynamicSecret;
            }
        } catch (_err) {
            //
        }
        throw new Error(`Secret '${key}' not found.`);
    };

    static /**
     * Checks if the file extension is allowed by a regex in dynamic configuration
     * @param fileName
     * @returns {boolean}
     */ checkFileExtension = (fileName) => {
        const { fileExtensionsRegex } = globalThis.dynamicConfig.dms;

        if (!fileExtensionsRegex) {
            return true;
        }

        const regex = new RegExp(fileExtensionsRegex, "i");
        return regex.test(fileName);
    };

    static copyFile = (src, dst) => {
        if (!fs.existsSync(src)) {
            throw new Error(`File '${src}' not found.`);
        }

        return new Promise((resolve, reject) => {
            const stream = fs.createReadStream(src);
            stream.on("end", resolve);
            stream.on("error", reject);
            stream.pipe(fs.createWriteStream(dst));
        });
    };

    static prepareWheres = // DynamicTableApi
        (builder, wheres) => {
            wheres.forEach((where) => {
                const whereMethod = where.or ? "orWhere" : "where";
                if (where.operator === "null") {
                    builder[`${whereMethod}Null`](where.left);
                } else if (where.operator === "not null") {
                    builder[`${whereMethod}Null`](where.left);
                } else {
                    builder[whereMethod](
                        where.left,
                        where.operator,
                        where.right,
                    );
                }
            });
        };

    static flattenArrayTo1D(arr) {
        // Helper function for ObjectToLogMessage
        if (arr.every((entry) => !Array.isArray(entry))) {
            return arr;
        }
        return UtilsService.flattenArrayTo1D(arr.flat());
    }

    static buildStringFromArray(array) {
        const elements = [];
        elements.push("[");
        array.forEach((el, index, arr) => {
            elements.push(el);
            if (index !== arr.length - 1) {
                elements.push(", ");
            }
        });
        elements.push("]");
        return elements.join("");
    }

    static stringifyObject(obj) {
        if (obj === null) {
            return "null";
        }
        if (obj === undefined) {
            return "undefined";
        }
        if (typeof obj === "string") {
            return obj;
        }

        if (
            Object.getPrototypeOf(obj) !== null &&
            !obj.toString().includes("object Object")
        ) {
            return obj.toString();
        }

        const newObj = {};
        Object.entries(obj).forEach(([key, value]) => {
            if (value instanceof BaseApi) {
                newObj[key] = value.toString();
            } else {
                newObj[key] = value;
            }
        });

        return JSON.stringify(newObj);
    }

    static stringifyArray(messageArr) {
        const oneDimensionalArr = UtilsService.flattenArrayTo1D(messageArr);
        const stringifiedElements = oneDimensionalArr.map((msg) =>
            UtilsService.stringifyObject(msg),
        );
        return UtilsService.buildStringFromArray(stringifiedElements);
    }

    static objectToLogMessage = // Function used in DebuggerApi to transform object to log message
        (message) => {
            if (Array.isArray(message)) {
                return UtilsService.stringifyArray(message);
            }
            return UtilsService.stringifyObject(message);
        };

    static isFirstDayOfMonth = // Check if it is the first day of the month
        (date) =>
            moment(date).isSame(moment(date).clone().startOf("month"), "day");

    static convertData = (data, fromFormat, toFormat) => {
        const bufferData = Buffer.from(data, fromFormat);
        return bufferData.toString(toFormat);
    };

    static isSubDir = (parent: string, dir: string): boolean => {
        const relative = path.relative(parent, dir);
        return (
            relative && !relative.startsWith("..") && !path.isAbsolute(relative)
        );
    };
}
