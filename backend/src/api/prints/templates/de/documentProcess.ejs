<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><%= template.TPROC_NAME %></title>
        <%- include('../process') %>
        <%- include('../script') %>
        <%- include('../functions') %>
    </head>
    <body>
        <%
            moment.locale('de');
            let usedRoles = [];
            let usedUsers = [];
            tasks.forEach((task) => {
                if (task.TTASK_ASSESMENT_ROLE_NAME) usedRoles.push(task.TTASK_ASSESMENT_ROLE_NAME);
                if (task.TTASK_ASSESMENT_USER_ID) usedUsers.push(_.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME);
            });
        %>
        <h1><%= template.TPROC_NAME %></h1>
        <div><span>Beschreibung: </span><%= template.TPROC_DESCRIPTION %></div>
        <div><span>Sichtbar für Mitglieder der Organisationseinheit: </span><%= (_.find(orgUnits, {ORGSTR_ID: template.TPROC_VIS_ORGSTR_ID}) || {}).ORGSTR_NAME %></div>
        <div><span>Sichtbar für Benutzer mit Rollen: </span><%= template.raw.TPROC_VIS_ROLE_NAME %></div>
        <div><span>Autor der letzten Änderung: </span><%= template.raw.TPROC_LAST_CHANGED_BY_USER %></div>
        <div><span>Verwendete Rollen: </span><%= _.uniq(usedRoles).join(', ') %></div>
        <div><span>Verwendung durch Benutzer: </span><%= _.uniq(usedUsers).join(', ') %></div>
        <div><span>Datum der letzten Änderung: </span><%= moment(template.TPROC_LAST_CHANGED_DATE, 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><span>Datum der Dokumenterstellung: </span><%= moment(new Date(), 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><button id="toggle-all" onclick="openCloseAll()">Bedingungen und Definitionen erweitern/schließen <span>&#9660;</span></button></div>
        <br>

        <!-- Headers -->
        <div class="headers">
            <h2>Kopfzeilen</h2>
            <% headers.forEach((header) => { %>
                <div><span>Kopfzeilencode: </span><%= header.HEADER_CODE %></div>
                <div><span>Standardname der Kopfzeile: </span><%= header.HEADER_NAME %></div>
                <div><span>Aktive: </span><%= globalThis.__({phrase: 'yesNo' + header.HEADER_ENABLED, locale: 'de'}) %></div>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'HEADER_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= header.raw[translatedName] %></div>
                <% });
                } %>
                <div><span>Org. Einheiten: </span><%= header.HEADER_ORGSTRS.length %></div>
                <div><span>Rolle: </span><%= header.HEADER_ROLES && header.HEADER_ROLES.length %></div>
                <div><span>Fall sichtbar für Rolle: </span><%= header.raw.HDR_VIS_ROLE_NAME %></div>
                <br>
            <% }); %>
        </div>

        <!-- Variables -->
        <div class="variables">
            <h2>Variablen</h2>
            <% variables.forEach((variable) => { %>
                <h3><a name=<%= "var" + variable.TVAR_ID %>><%= variable.TVAR_NAME %></h3>
                <div><span>Typ: </span><%= globalThis.__({phrase: 'tvarType' + variable.TVAR_TYPE, locale: 'de'}) %></div>
                <div><span>Werte: </span><%= getVariableValue(variable, dateFormat) %></div>
                <%
                    const meta = JSON.parse(variable.TVAR_META || "{}");
                    if (variable.TVAR_TYPE === 'DR') {
                        const definition = meta.tableDefinition;
                        const selector = "var-def" + variable.TVAR_ID; %>
                        <div><span>Definition: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <pre id=<%= selector %> class="openable closed"><%= definition %></pre>
                        </div>
                <% }
                    else if (variable.TVAR_TYPE === 'T' && meta.suggestBox) { %>
                    <div><span>Url: </span><%= meta.suggestBox.apiUrl %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'säule: ' + meta.suggestBox.prop %></div>
                <% }
                    else if (variable.TVAR_TYPE === 'N') {
                        const definition = meta.numberOfDecimals; %>
                        <div><span>Anzahl Nachkommastellen: </span><%= definition %></div>
                <% }
                %>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'TVAR_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= variable.raw[translatedName] %></div>
                <% });
                } %>
            <% }); %>
        </div>

        <!-- Tasks -->
        <div class="tasks">
            <h2>Aufgaben</h2>
            <% tasks.forEach((task) => { %>
                <div class="task">
                    <a name=<%= task.TTASK_ID %>></a>
                    <h3><%= task.TTASK_NAME %></h3>
                    <div><span>Aufgabentyp: </span><%= globalThis.__({phrase: 'taskType' + task.TTASK_TYPE, locale: 'de'}) %>
                        <% if (task.TTASK_TYPE === 'E') { %>
                            <span> (<%= task.TTASK_EVENT %>)</span>
                        <% } %>
                    </div>
                    <% if (task.TTASK_TYPE === 'P') {
                        const subp = _.find(processes, {TPROC_ID: task.TTASK_SUBPROCESS_TPROC_ID}) || {}; %>
                    <div><span>Thread-Informationen: </span><%= subp.TPROC_DESCRIPTION ? '(' + subp.TPROC_DESCRIPTION + ')' : subp.TPROC_NAME %></div>
                    <% if (task.MAPPING && task.MAPPING.subp) { %>
                        <% task.MAPPING.subp.forEach((subp) => {
                            let subpTitle;
                            if (subp.evepar_name === '$CONSTANT' || subp.evepar_name === '$CALC') {
                                subpTitle = subp.rdefpar_name + ' <- ' + (subp.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subp.rdefpar_value + (subp.evepar_name === '$CONSTANT' ? '"' : '');
                            } else {
                                subpTitle = subp.rdefpar_name + ' <- ' + subp.evepar_name;
                            }
                        %>
                        <div><span>&nbsp; &nbsp; Dateneingabe: </span><%= subpTitle %></div>
                        <% }); %>
                    <% } %>
                    <% if (task.MAPPING && task.MAPPING.subr) { %>
                        <% task.MAPPING.subr.forEach((subr) => {
                            let subrTitle;
                            if (subr.evepar_name === '$CONSTANT' || subr.evepar_name === '$CALC') {
                                subrTitle = subr.rdefpar_name + ' -> ' + (subr.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subr.rdefpar_value + (subr.evepar_name === '$CONSTANT' ? '"' : '');
                            } else {
                                subrTitle = subr.rdefpar_name + ' -> ' + subr.evepar_name;
                            }
                        %>
                            <div><span>&nbsp; &nbsp; Datenausgabe: </span><%= subrTitle %></div>
                        <% }); %>
                    <% } %>
                    <% } %>
                    <div><span>Verlaufseintrag generieren: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_GEN_HISTORY, locale: 'de'}) %></div>
                    <%
                        let supervizor = 'falleigentümer';
                        if (task.TTASK_ASSESMENT_USER_ID) supervizor = _.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME;
                        if (task.TTASK_ASSESMENT_ORGSTR_ID) {
                            supervizor = "manager der organisationseinheit " + (_.find(orgUnits, {ORGSTR_ID: task.TTASK_ASSESMENT_ORGSTR_ID}) || {}).ORGSTR_NAME;
                        }
                    %>
                    <div><span>Aufgabenbetreuer: </span><%= supervizor %></div>
                    <div><span>Bezugsperson: </span><%= task.TTASK_REFERENCE_USER %></div>
                    <% if (task.TTASK_DESCRIPTION) { %>
                        <div><span>Aufgabenbeschreibung: </span><%= task.TTASK_DESCRIPTION %></div>
                    <% } %>

                    <!-- Solver -->
                    <% if (task.TTASK_TYPE !== 'A') { %>
                        <br>Löser<br>
                        <div class="indent"><span>Rolle: </span><%= task.TTASK_ASSESMENT_ROLE_NAME %></div>
                        <div class="indent"><span>Nur Organisationseinheit: </span><%= task.TTASK_ASSESMENT_ORGSTR_NAME %></div>
                        <div class="indent"><span>Beziehung zur Bezugsperson: </span><%= globalThis.__({phrase: 'assesmentHierarchy' + task.TTASK_ASSESMENT_HIERARCHY, locale: 'de'}) %></div>
                        <div class="indent"><span>Die Aufgabe wird vergeben: </span><%= globalThis.__({phrase: 'assesmentMethod' + task.TTASK_ASSESMENT_METHOD, locale: 'de'}) + ' ' %>
                            <% if (task.TTASK_ASSESMENT_METHOD === 'L') { %>
                                <a href=<%= '#' + task.TTASK_ASSESMENT_TTASK_ID %>><%= _.find(tasks, {TTASK_ID: task.TTASK_ASSESMENT_TTASK_ID}).TTASK_NAME %></a>
                            <% } %>
                        </div>
                        <% if (task.TTASK_ASSESMENT_METHOD === 'V') { %>
                            <div class="indent"><span>Variablennamen: </span><%= _.find(variables, {TVAR_ID: task.TTASK_ASSESMENT_TVAR_ID}).TVAR_NAME %></div>
                        <% } %>
                        <div class="indent"><span>Recht / Pflicht: </span><%= globalThis.__({phrase: 'rightDuty' + task.TTASK_DUTY, locale: 'de'}) %></div>
                    <% } %>

                    <!-- Planning -->
                    <br>Planung<br>
                    <%
                        let dueOffset = task.TTASK_DUE_OFFSET || globalThis.__({phrase: 'dueOffsetno', locale: 'de'});
                        let duration = task.TTASK_DURATION || globalThis.__({phrase: 'durationno', locale: 'de'});

                        const dueOffsetStart = dueOffset.substring(0,2);
                        const durationStart = duration.substring(0,2);

                        if (dueOffsetStart === 'P+' || dueOffsetStart === 'T+') {
                            dueOffset = task.TTASK_DUE_OFFSET + ' ' + globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'de'});
                        }
                        else if (dueOffsetStart === 'vo' || dueOffsetStart === 'vc') {
                            const tvarId = Number(dueOffset.substring(2));
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'de'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }

                        if (durationStart === 'P+' || durationStart === 'T+') {
                            duration = task.TTASK_DURATION + ' ' + globalThis.__({phrase: 'duration' + durationStart, locale: 'de'});
                        }
                        else if (durationStart === 'vo' || durationStart === 'vc') {
                            const tvarId = Number(duration.substring(2));
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'de'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }
                    %>
                    <div class="indent"><span>Warten auf Eingaben: </span><%= task.TTASK_PETRI_NET_INPUT %></div>
                    <div class="indent"><span>Aufgabenstart: </span><%= dueOffset %></div>
                    <div class="indent"><span>Dauer: </span><%= duration %></div>
                    <div class="indent"><span>Das Abschließen der Aufgabe beendet den gesamten Fall: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_SUFFICIENT_END, locale: 'de'}) %></div>
                    <div class="indent"><span>Führen Sie die Aufgabe nur einmal aus: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_RUN_ONLY_ONCE, locale: 'de'}) %></div>
                    <div class="indent"><span>Wartet auf alle, startet zuerst: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_DISC_FLAG, locale: 'de'}) %></div>
                    <div class="indent"><span>Multiinstanz: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_MULTIINSTANCE_FLAG, locale: 'de'}) %></div>
                    <% if (task.TTASK_MULTIINSTANCE_FLAG === 'Y') { %>
                        <div class="indent"><span>Über Variablen iterieren: </span><%= task.TTASK_ITERATE_OVER %></div>
                    <% } %>

                    <!-- Task variables -->
                    <% if (task.TTASK_TYPE !== 'A' && Array.isArray(variables) && variables.length > 0) { %>
                        <br>Variablen<br>
                        <% varUsage.forEach((u) => { %>
                            <% if (u.TTASK_ID === task.TTASK_ID) { %>
                                <div class="usage">
                                    <a href=<%= "#var" + u.TVAR_ID %>><%= (_.find(variables, {TVAR_ID: u.TVAR_ID}) || {}).TVAR_NAME + ' (' +
                                    globalThis.__({phrase: 'duty' + u.TTASKVARUSG_USAGE, locale: 'de'}) + ')' %></a>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Calculations -->
                    <% if (task.TTASK_CALC_COUNT > 0 && Array.isArray(calculations) && calculations.length > 0) { %>
                        <br>Berechnungen (<%= task.TTASK_NAME %>)<br>
                        <% calculations.forEach((calc) => { %>
                            <% if (calc.TTASK_ID === task.TTASK_ID) { %>
                                <div class="indent">
                                    <%
                                        let run = calc.TTJSCALC_EXEC_START === 'Y' ? globalThis.__({phrase: 'runStart', locale: 'de'}) + ', ': '';
                                        run = run + (calc.TTJSCALC_EXEC_END === 'Y' ? globalThis.__({phrase: 'runEnd', locale: 'de'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_HAND === 'Y' ? globalThis.__({phrase: 'runHand', locale: 'de'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_RECALC === 'Y' ? globalThis.__({phrase: 'runCalc', locale: 'de'}) + ', ' : '');
                                        run = run.slice(0, -2);
                                    %>
                                    <%= calc.TTJSCALC_JS %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'starten: ' + run %>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Links -->
                    <% if (Array.isArray(taskLinks) && taskLinks.length > 0) { %>
                        <br>Links<br>
                        <% taskLinks.forEach((link) => { %>
                            <% if (link.TTASKLINK_FROM_TTASK_ID === task.TTASK_ID) { %>
                                <div class="link">
                                    <%
                                        let linkType = globalThis.__({phrase: 'linkType' + link.TTASKLINK_TYPE, locale: 'de'});
                                        if(link.raw.COND_COUNT === 0 && link.TTASKLINK_TYPE !== 'ELSE') linkType = globalThis.__({phrase: 'linkTypeNULL', locale: 'de'});
                                    %>

                                    <a href=<%= "#" + link.TTASKLINK_TO_TTASK_ID %>><%= (_.find(tasks, {TTASK_ID: link.TTASKLINK_TO_TTASK_ID}) || {}).TTASK_NAME +
                                        ' (' + linkType + ')' %></a>
                                </div>
                                <ul>
                                    <% for(let i = 0; i < conditions.length; i++) { %>
                                        <% if (conditions[i].TTASKLINK_ID === link.TTASKLINK_ID) { %>
                                            <li>
                                                <% const condValue = conditions[i].TCOND_VALUE || ''; %>
                                                <div class="condition"><%- conditions[i].TCOND_VARIABLE +
                                                ' ' + globalThis.__({phrase: 'operator' + conditions[i].TCOND_OPERATOR, locale: 'de'}) + ' ' + condValue %></div>
                                            </li>
                                        <% } %>
                                    <% } %>
                                </ul>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Instructions -->
                    <% if (task.TTASK_INSTRUCTION) { %>
                        <br>Instruktion<br>
                        <div class="indent"><%- task.TTASK_INSTRUCTION %></div>
                    <% } %>

                    <!-- E-mail -->
                    <% if (task.TTASK_TYPE === 'N') { %>
                        <br>E-mail<br>
                        <div class="indent"><span>Adressat: </span><%= globalThis.__({phrase: 'recipient' + task.TTASK_ENOT_TGT_TYPE, locale: 'de'}) + ': ' + task.TTASK_ENOT_TGT %></div>
                        <div class="indent"><span>Betreff: </span><%= task.TTASK_ENOT_SUBJECT %></div>
                        <% const selector = "email-body" + task.TTASK_ID; %>
                        <div class="indent"><span>Körper: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <div id=<%= selector %> class="email openable closed"><%- task.TTASK_ENOT_BODY2 %></div>
                </div>
            <% } %>

                    <!-- Conditions - task JS -->
                    <% if (task.TTASK_JS) {
                        const selector = "conds" + task.TTASK_ID; %>
                        <br>Bedingungen <button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button><br>
                        <div class="indent">
                            <pre id=<%= selector %> class="openable closed"><%= task.TTASK_JS %></pre>
                        </div>
                    <% } %>
                </div>
            <% }); %>
        </div>
    </body>
</html>