<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><%= template.TPROC_NAME %></title>
        <%- include('../process') %>
        <%- include('../script') %>
        <%- include('../functions') %>
    </head>
    <body>
        <%
            moment.locale('ro');
            let usedRoles = [];
            let usedUsers = [];
            tasks.forEach((task) => {
                if (task.TTASK_ASSESMENT_ROLE_NAME) usedRoles.push(task.TTASK_ASSESMENT_ROLE_NAME);
                if (task.TTASK_ASSESMENT_USER_ID) usedUsers.push(_.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME);
            });
        %>
        <h1><%= template.TPROC_NAME %></h1>
        <div><span>Descriere: </span><%= template.TPROC_DESCRIPTION %></div>
        <div><span>Vizibilă membrilor grupului organizațional: </span><%= (_.find(orgUnits, {ORGSTR_ID: template.TPROC_VIS_ORGSTR_ID}) || {}).ORGSTR_NAME %></div>
        <div><span>Vizibil pentru membrii cu roluri: </span><%= template.raw.TPROC_VIS_ROLE_NAME %></div>
        <div><span>Autorul ultimei modificări: </span><%= template.raw.TPROC_LAST_CHANGED_BY_USER %></div>
        <div><span>Rol folosit: </span><%= _.uniq(usedRoles).join(', ') %></div>
        <div><span>Utilizatori utilizați: </span><%= _.uniq(usedUsers).join(', ') %></div>
        <div><span>Data ultimei modificări: </span><%= moment(template.TPROC_LAST_CHANGED_DATE, 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><span>Data creării documentului: </span><%= moment(new Date(), 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><button id="toggle-all" onclick="openCloseAll()">Extindeți/închideți condițiile și definițiile <span>&#9660;</span></button></div>
        <br>

        <!-- Headers -->
        <div class="headers">
            <h2>Anteturi</h2>
            <% headers.forEach((header) => { %>
                <div><span>Codul antet: </span><%= header.HEADER_CODE %></div>
                <div><span>Numele antetului implicit: </span><%= header.HEADER_NAME %></div>
                <div><span>Activ: </span><%= globalThis.__({phrase: 'yesNo' + header.HEADER_ENABLED, locale: 'ro'}) %></div>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'HEADER_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= header.raw[translatedName] %></div>
                    <% });
                } %>
                <div><span>Org. Unități: </span><%= header.HEADER_ORGSTRS.length %></div>
                <div><span>Roluri: </span><%= header.HEADER_ROLES && header.HEADER_ROLES.length %></div>
                <div><span>Caz vizibil pentru membrii cu rol: </span><%= header.raw.HDR_VIS_ROLE_NAME %></div>
                <br>
            <% }); %>
        </div>

        <!-- Variables -->
        <div class="variables">
            <h2>Variabile</h2>
            <% variables.forEach((variable) => { %>
                <h3><a name=<%= "var" + variable.TVAR_ID %>><%= variable.TVAR_NAME %></h3>
                <div><span>Tip: </span><%= globalThis.__({phrase: 'tvarType' + variable.TVAR_TYPE, locale: 'ro'}) %></div>
                <div><span>Valori: </span><%= getVariableValue(variable, dateFormat) %></div>
                <%
                    const meta = JSON.parse(variable.TVAR_META || "{}");
                    if (variable.TVAR_TYPE === 'DR') {
                        const definition = meta.tableDefinition;
                        const selector = "var-def" + variable.TVAR_ID; %>
                        <div><span>Definiție: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <pre id=<%= selector %> class="openable closed"><%= definition %></pre>
                        </div>
                    <% }
                    else if (variable.TVAR_TYPE === 'T' && meta.suggestBox) { %>
                        <div><span>Url: </span><%= meta.suggestBox.apiUrl %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'column: ' + meta.suggestBox.prop %></div>
                    <% }
                    else if (variable.TVAR_TYPE === 'N') {
                        const definition = meta.numberOfDecimals; %>
                        <div><span>Zecimale: </span><%= definition %></div>
                    <% }
                %>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'TVAR_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= variable.raw[translatedName] %></div>
                    <% });
                } %>
            <% }); %>
        </div>

        <!-- Tasks -->
        <div class="tasks">
            <h2>Sarcini</h2>
            <% tasks.forEach((task) => { %>
                <div class="task">
                    <a name=<%= task.TTASK_ID %>></a>
                    <h3><%= task.TTASK_NAME %></h3>
                    <div><span>Tipul sarcinii: </span><%= globalThis.__({phrase: 'taskType' + task.TTASK_TYPE, locale: 'ro'}) %>
                        <% if (task.TTASK_TYPE === 'E') { %>
                            <span> (<%= task.TTASK_EVENT %>)</span>
                        <% } %>
                    </div>
                    <% if (task.TTASK_TYPE === 'P') {
                        const subp = _.find(processes, {TPROC_ID: task.TTASK_SUBPROCESS_TPROC_ID}) || {}; %>
                        <div><span>Informații subproces: </span><%= subp.TPROC_DESCRIPTION ? '(' + subp.TPROC_DESCRIPTION + ')' : subp.TPROC_NAME %></div>
                        <% if (task.MAPPING && task.MAPPING.subp) { %>
                            <% task.MAPPING.subp.forEach((subp) => {
                                let subpTitle;
                                if (subp.evepar_name === '$CONSTANT' || subp.evepar_name === '$CALC') {
                                    subpTitle = subp.rdefpar_name + ' <- ' + (subp.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subp.rdefpar_value + (subp.evepar_name === '$CONSTANT' ? '"' : '');
                                } else {
                                    subpTitle = subp.rdefpar_name + ' <- ' + subp.evepar_name;
                                }
                            %>
                                <div><span>&nbsp; &nbsp; Intrare: </span><%= subpTitle %></div>
                            <% }); %>
                        <% } %>
                        <% if (task.MAPPING && task.MAPPING.subr) { %>
                            <% task.MAPPING.subr.forEach((subr) => {
                                let subrTitle;
                                if (subr.evepar_name === '$CONSTANT' || subr.evepar_name === '$CALC') {
                                    subrTitle = subr.rdefpar_name + ' -> ' + (subr.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subr.rdefpar_value + (subr.evepar_name === '$CONSTANT' ? '"' : '');
                                } else {
                                    subrTitle = subr.rdefpar_name + ' -> ' + subr.evepar_name;
                                }
                            %>
                                <div><span>&nbsp; &nbsp; Ieșire: </span><%= subrTitle %></div>
                            <% }); %>
                        <% } %>
                    <% } %>
                    <div><span>Generați înregistrarea în istoric: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_GEN_HISTORY, locale: 'ro'}) %></div>
                    <%
                        let supervizor = 'deținatorul cazului';
                        if (task.TTASK_ASSESMENT_USER_ID) supervizor = _.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME;
                        if (task.TTASK_ASSESMENT_ORGSTR_ID) {
                            supervizor = "manager al unitatilor organizationale " + (_.find(orgUnits, {ORGSTR_ID: task.TTASK_ASSESMENT_ORGSTR_ID}) || {}).ORGSTR_NAME;
                        }
                    %>
                    <div><span>Responsabil de sarcini: </span><%= supervizor %></div>
                    <div><span>Persoana de referinta: </span><%= task.TTASK_REFERENCE_USER %></div>
                    <% if (task.TTASK_DESCRIPTION) { %>
                        <div><span>Descrierea sarcinii: </span><%= task.TTASK_DESCRIPTION %></div>
                    <% } %>

                    <!-- Solver -->
                    <% if (task.TTASK_TYPE !== 'A') { %>
                        <br>Proprietar de activități<br>
                        <div class="indent"><span>Rol: </span><%= task.TTASK_ASSESMENT_ROLE_NAME %></div>
                        <div class="indent"><span>Numai organizație: </span><%= task.TTASK_ASSESMENT_ORGSTR_NAME %></div>
                        <div class="indent"><span>Relația cu persoana de referință: </span><%= globalThis.__({phrase: 'assesmentHierarchy' + task.TTASK_ASSESMENT_HIERARCHY, locale: 'ro'}) %></div>
                        <div class="indent"><span>Sarcina va fi atribuită: </span><%= globalThis.__({phrase: 'assesmentMethod' + task.TTASK_ASSESMENT_METHOD, locale: 'ro'}) + ' ' %>
                            <% if (task.TTASK_ASSESMENT_METHOD === 'L') { %>
                                <a href=<%= '#' + task.TTASK_ASSESMENT_TTASK_ID %>><%= _.find(tasks, {TTASK_ID: task.TTASK_ASSESMENT_TTASK_ID}).TTASK_NAME %></a>
                            <% } %>
                        </div>
                        <% if (task.TTASK_ASSESMENT_METHOD === 'V') { %>
                            <div class="indent"><span>Numele variabilei: </span><%= _.find(variables, {TVAR_ID: task.TTASK_ASSESMENT_TVAR_ID}).TVAR_NAME %></div>
                        <% } %>
                        <div class="indent"><span>Dreapta / Datorie: </span><%= globalThis.__({phrase: 'rightDuty' + task.TTASK_DUTY, locale: 'ro'}) %></div>
                    <% } %>

                    <!-- Planning -->
                    <br>Planificare<br>
                    <%
                        let dueOffset = task.TTASK_DUE_OFFSET || globalThis.__({phrase: 'dueOffsetno', locale: 'ro'});
                        let duration = task.TTASK_DURATION || globalThis.__({phrase: 'durationno', locale: 'ro'});

                        const dueOffsetStart = dueOffset.substring(0,2);
                        const durationStart = duration.substring(0,2);

                        if (dueOffsetStart === 'P+' || dueOffsetStart === 'T+') {
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'ro'}, {variable: task.TTASK_DUE_OFFSET});
                        }
                        else if (dueOffsetStart === 'vo' || dueOffsetStart === 'vc') {
                            const tvarId = Number(dueOffset.substring(2));
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'ro'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }

                        if (durationStart === 'P+' || durationStart === 'T+') {
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'ro'}, {variable: task.TTASK_DURATION});
                        }
                        else if (durationStart === 'vo' || durationStart === 'vc') {
                            const tvarId = Number(duration.substring(2));
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'ro'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }
                    %>
                    <div class="indent"><span>Se așteaptă intrările: </span><%= task.TTASK_PETRI_NET_INPUT %></div>
                    <div class="indent"><span>Începutul sarcinii: </span><%= dueOffset %></div>
                    <div class="indent"><span>Durată: </span><%= duration %></div>
                    <div class="indent"><span>Sarcina încheie întregul caz: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_SUFFICIENT_END, locale: 'ro'}) %></div>
                    <div class="indent"><span>Executați sarcina o singură dată: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_RUN_ONLY_ONCE, locale: 'ro'}) %></div>
                    <div class="indent"><span>Așteaptă pentru toți, început de prima: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_DISC_FLAG, locale: 'ro'}) %></div>
                    <div class="indent"><span>Multi-exemplu: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_MULTIINSTANCE_FLAG, locale: 'ro'}) %></div>
                    <% if (task.TTASK_MULTIINSTANCE_FLAG === 'Y') { %>
                        <div class="indent"><span>Iterați peste variabile: </span><%= task.TTASK_ITERATE_OVER %></div>
                    <% } %>

                    <!-- Task variables -->
                    <% if (task.TTASK_TYPE !== 'A' && Array.isArray(variables) && variables.length > 0) { %>
                        <br>Variabile<br>
                        <% varUsage.forEach((u) => { %>
                            <% if (u.TTASK_ID === task.TTASK_ID) { %>
                                <div class="usage">
                                    <a href=<%= "#var" + u.TVAR_ID %>><%= (_.find(variables, {TVAR_ID: u.TVAR_ID}) || {}).TVAR_NAME + ' (' +
                                    globalThis.__({phrase: 'duty' + u.TTASKVARUSG_USAGE, locale: 'ro'}) + ')' %></a>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Calculations -->
                    <% if (task.TTASK_CALC_COUNT > 0 && Array.isArray(calculations) && calculations.length > 0) { %>
                        <br>Calculele (<%= task.TTASK_NAME %>)<br>
                        <% calculations.forEach((calc) => { %>
                            <% if (calc.TTASK_ID === task.TTASK_ID) { %>
                                <div class="indent">
                                    <%
                                        let run = calc.TTJSCALC_EXEC_START === 'Y' ? globalThis.__({phrase: 'runStart', locale: 'ro'}) + ', ': '';
                                        run = run + (calc.TTJSCALC_EXEC_END === 'Y' ? globalThis.__({phrase: 'runEnd', locale: 'ro'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_HAND === 'Y' ? globalThis.__({phrase: 'runHand', locale: 'ro'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_RECALC === 'Y' ? globalThis.__({phrase: 'runCalc', locale: 'ro'}) + ', ' : '');
                                        run = run.slice(0, -2);
                                    %>
                                    <%= calc.TTJSCALC_JS %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'run: ' + run %>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Links -->
                    <% if (Array.isArray(taskLinks) && taskLinks.length > 0) { %>
                        <br>Links<br>
                        <% taskLinks.forEach((link) => { %>
                            <% if (link.TTASKLINK_FROM_TTASK_ID === task.TTASK_ID) { %>
                                <div class="link">
                                    <%
                                        let linkType = globalThis.__({phrase: 'linkType' + link.TTASKLINK_TYPE, locale: 'ro'});
                                        if(link.raw.COND_COUNT === 0 && link.TTASKLINK_TYPE !== 'ELSE') linkType = globalThis.__({phrase: 'linkTypeNULL', locale: 'ro'});
                                    %>

                                    <a href=<%= "#" + link.TTASKLINK_TO_TTASK_ID %>><%= (_.find(tasks, {TTASK_ID: link.TTASKLINK_TO_TTASK_ID}) || {}).TTASK_NAME +
                                    ' (' + linkType + ')' %></a>
                                </div>
                                <ul>
                                    <% for(let i = 0; i < conditions.length; i++) { %>
                                        <% if (conditions[i].TTASKLINK_ID === link.TTASKLINK_ID) { %>
                                            <li>
                                                <% const condValue = conditions[i].TCOND_VALUE || ''; %>
                                                <div class="condition"><%- conditions[i].TCOND_VARIABLE +
                                                ' ' + globalThis.__({phrase: 'operator' + conditions[i].TCOND_OPERATOR, locale: 'ro'}) + ' ' + condValue %></div>
                                            </li>
                                        <% } %>
                                    <% } %>
                                </ul>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Instructions -->
                    <% if (task.TTASK_INSTRUCTION) { %>
                        <br>Instrucțiuni<br>
                        <div class="indent"><%- task.TTASK_INSTRUCTION %></div>
                    <% } %>

                    <!-- E-mail -->
                    <% if (task.TTASK_TYPE === 'N') { %>
                        <br>E-mail<br>
                        <div class="indent"><span>Destinatar: </span><%= globalThis.__({phrase: 'recipient' + task.TTASK_ENOT_TGT_TYPE, locale: 'ro'}) + ': ' + task.TTASK_ENOT_TGT %></div>
                        <div class="indent"><span>Subiect: </span><%= task.TTASK_ENOT_SUBJECT %></div>
                        <% const selector = "email-body" + task.TTASK_ID; %>
                        <div class="indent"><span>Corp: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <div id=<%= selector %> class="email openable closed"><%- task.TTASK_ENOT_BODY2 %></div>
                        </div>
                    <% } %>

                    <!-- Conditions - task JS -->
                    <% if (task.TTASK_JS) {
                        const selector = "conds" + task.TTASK_ID; %>
                        <br>Condiţii <button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button><br>
                        <div class="indent">
                            <pre id=<%= selector %> class="openable closed"><%= task.TTASK_JS %></pre>
                        </div>
                    <% } %>
                </div>
            <% }); %>
        </div>
    </body>
</html>