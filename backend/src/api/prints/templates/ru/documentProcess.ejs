<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><%= template.TPROC_NAME %></title>
        <%- include('../process') %>
        <%- include('../script') %>
        <%- include('../functions') %>
    </head>
    <body>
        <%
            moment.locale('ru');
            let usedRoles = [];
            let usedUsers = [];
            tasks.forEach((task) => {
                if (task.TTASK_ASSESMENT_ROLE_NAME) usedRoles.push(task.TTASK_ASSESMENT_ROLE_NAME);
                if (task.TTASK_ASSESMENT_USER_ID) usedUsers.push(_.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME);
            });
        %>
        <h1><%= template.TPROC_NAME %></h1>
        <div><span>Description: </span><%= template.TPROC_DESCRIPTION %></div>
        <div><span>Visible to organizational group members: </span><%= (_.find(orgUnits, {ORGSTR_ID: template.TPROC_VIS_ORGSTR_ID}) || {}).ORGSTR_NAME %></div>
        <div><span>Visible to members with roles: </span><%= template.raw.TPROC_VIS_ROLE_NAME %></div>
        <div><span>Author of the last change: </span><%= template.raw.TPROC_LAST_CHANGED_BY_USER %></div>
        <div><span>Used roles: </span><%= _.uniq(usedRoles).join(', ') %></div>
        <div><span>Used users: </span><%= _.uniq(usedUsers).join(', ') %></div>
        <div><span>Date of the last change: </span><%= moment(template.TPROC_LAST_CHANGED_DATE, 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><span>Date document creation: </span><%= moment(new Date(), 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><button id="toggle-all" onclick="openCloseAll()">Expand/close conditions and definitions <span>&#9660;</span></button></div>
        <br>

        <!-- Headers -->
        <div class="headers">
            <h2>Headers</h2>
            <% headers.forEach((header) => { %>
                <div><span>Header code: </span><%= header.HEADER_CODE %></div>
                <div><span>Default header name: </span><%= header.HEADER_NAME %></div>
                <div><span>Active: </span><%= globalThis.__({phrase: 'yesNo' + header.HEADER_ENABLED, locale: 'ru'}) %></div>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'HEADER_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= header.raw[translatedName] %></div>
                    <% });
                } %>
                <div><span>Org. units: </span><%= header.HEADER_ORGSTRS.length %></div>
                <div><span>Roles: </span><%= header.HEADER_ROLES && header.HEADER_ROLES.length %></div>
                <div><span>Case visible to members with role: </span><%= header.raw.HDR_VIS_ROLE_NAME %></div>
                <br>
            <% }); %>
        </div>

        <!-- Variables -->
        <div class="variables">
            <h2>Variables</h2>
            <% variables.forEach((variable) => { %>
                <h3><a name=<%= "var" + variable.TVAR_ID %>><%= variable.TVAR_NAME %></h3>
                <div><span>Type: </span><%= globalThis.__({phrase: 'tvarType' + variable.TVAR_TYPE, locale: 'ru'}) %></div>
                <div><span>Values: </span><%= getVariableValue(variable, dateFormat) %></div>
                <%
                    const meta = JSON.parse(variable.TVAR_META || "{}");
                    if (variable.TVAR_TYPE === 'DR') {
                        const definition = meta.tableDefinition;
                        const selector = "var-def" + variable.TVAR_ID; %>
                        <div><span>Definition: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <pre id=<%= selector %> class="openable closed"><%= definition %></pre>
                        </div>
                    <% }
                    else if (variable.TVAR_TYPE === 'T' && meta.suggestBox) { %>
                        <div><span>Url: </span><%= meta.suggestBox.apiUrl %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'column: ' + meta.suggestBox.prop %></div>
                    <% }
                    else if (variable.TVAR_TYPE === 'N') {
                        const definition = meta.numberOfDecimals; %>
                        <div><span>Decimals: </span><%= definition %></div>
                    <% }
                %>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'TVAR_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= variable.raw[translatedName] %></div>
                    <% });
                } %>
            <% }); %>
        </div>

        <!-- Tasks -->
        <div class="tasks">
            <h2>Tasks</h2>
            <% tasks.forEach((task) => { %>
                <div class="task">
                    <a name=<%= task.TTASK_ID %>></a>
                    <h3><%= task.TTASK_NAME %></h3>
                    <div><span>Task type: </span><%= globalThis.__({phrase: 'taskType' + task.TTASK_TYPE, locale: 'ru'}) %>
                        <% if (task.TTASK_TYPE === 'E') { %>
                            <span> (<%= task.TTASK_EVENT %>)</span>
                        <% } %>
                    </div>
                    <% if (task.TTASK_TYPE === 'P') {
                        const subp = _.find(processes, {TPROC_ID: task.TTASK_SUBPROCESS_TPROC_ID}) || {}; %>
                        <div><span>Subprocess information: </span><%= subp.TPROC_DESCRIPTION ? '(' + subp.TPROC_DESCRIPTION + ')' : subp.TPROC_NAME %></div>
                        <% if (task.MAPPING && task.MAPPING.subp) { %>
                            <% task.MAPPING.subp.forEach((subp) => {
                                let subpTitle;
                                if (subp.evepar_name === '$CONSTANT' || subp.evepar_name === '$CALC') {
                                    subpTitle = subp.rdefpar_name + ' <- ' + (subp.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subp.rdefpar_value + (subp.evepar_name === '$CONSTANT' ? '"' : '');
                                } else {
                                    subpTitle = subp.rdefpar_name + ' <- ' + subp.evepar_name;
                                }
                            %>
                                <div><span>&nbsp; &nbsp; In: </span><%= subpTitle %></div>
                            <% }); %>
                        <% } %>
                        <% if (task.MAPPING && task.MAPPING.subr) { %>
                            <% task.MAPPING.subr.forEach((subr) => {
                                let subrTitle;
                                if (subr.evepar_name === '$CONSTANT' || subr.evepar_name === '$CALC') {
                                    subrTitle = subr.rdefpar_name + ' -> ' + (subr.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subr.rdefpar_value + (subr.evepar_name === '$CONSTANT' ? '"' : '');
                                } else {
                                    subrTitle = subr.rdefpar_name + ' -> ' + subr.evepar_name;
                                }
                            %>
                                <div><span>&nbsp; &nbsp; Out: </span><%= subrTitle %></div>
                            <% }); %>
                        <% } %>
                    <% } %>
                    <div><span>Generate record to history: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_GEN_HISTORY, locale: 'ru'}) %></div>
                    <%
                        let supervizor = 'case owner';
                        if (task.TTASK_ASSESMENT_USER_ID) supervizor = _.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME;
                        if (task.TTASK_ASSESMENT_ORGSTR_ID) {
                            supervizor = "manager of organizational unit " + (_.find(orgUnits, {ORGSTR_ID: task.TTASK_ASSESMENT_ORGSTR_ID}) || {}).ORGSTR_NAME;
                        }
                    %>
                    <div><span>Task supervisor: </span><%= supervizor %></div>
                    <div><span>Reference person: </span><%= task.TTASK_REFERENCE_USER %></div>
                    <% if (task.TTASK_DESCRIPTION) { %>
                        <div><span>Task description: </span><%= task.TTASK_DESCRIPTION %></div>
                    <% } %>

                    <!-- Solver -->
                    <% if (task.TTASK_TYPE !== 'A') { %>
                        <br>Task owner<br>
                        <div class="indent"><span>Role: </span><%= task.TTASK_ASSESMENT_ROLE_NAME %></div>
                        <div class="indent"><span>Organizational unit only: </span><%= task.TTASK_ASSESMENT_ORGSTR_NAME %></div>
                        <div class="indent"><span>Relationship to reference person: </span><%= globalThis.__({phrase: 'assesmentHierarchy' + task.TTASK_ASSESMENT_HIERARCHY, locale: 'ru'}) %></div>
                        <div class="indent"><span>Task will be assigned: </span><%= globalThis.__({phrase: 'assesmentMethod' + task.TTASK_ASSESMENT_METHOD, locale: 'ru'}) + ' ' %>
                            <% if (task.TTASK_ASSESMENT_METHOD === 'L') { %>
                                <a href=<%= '#' + task.TTASK_ASSESMENT_TTASK_ID %>><%= _.find(tasks, {TTASK_ID: task.TTASK_ASSESMENT_TTASK_ID}).TTASK_NAME %></a>
                            <% } %>
                        </div>
                        <% if (task.TTASK_ASSESMENT_METHOD === 'V') { %>
                            <div class="indent"><span>Variable name: </span><%= _.find(variables, {TVAR_ID: task.TTASK_ASSESMENT_TVAR_ID}).TVAR_NAME %></div>
                        <% } %>
                        <div class="indent"><span>Right / Duty: </span><%= globalThis.__({phrase: 'rightDuty' + task.TTASK_DUTY, locale: 'ru'}) %></div>
                    <% } %>

                    <!-- Planning -->
                    <br>Planning<br>
                    <%
                        let dueOffset = task.TTASK_DUE_OFFSET || globalThis.__({phrase: 'dueOffsetno', locale: 'ru'});
                        let duration = task.TTASK_DURATION || globalThis.__({phrase: 'durationno', locale: 'ru'});

                        const dueOffsetStart = dueOffset.substring(0,2);
                        const durationStart = duration.substring(0,2);

                        if (dueOffsetStart === 'P+' || dueOffsetStart === 'T+') {
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'ru'}, {variable: task.TTASK_DUE_OFFSET});
                        }
                        else if (dueOffsetStart === 'vo' || dueOffsetStart === 'vc') {
                            const tvarId = Number(dueOffset.substring(2));
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'ru'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }

                        if (durationStart === 'P+' || durationStart === 'T+') {
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'ru'}, {variable: task.TTASK_DURATION});
                        }
                        else if (durationStart === 'vo' || durationStart === 'vc') {
                            const tvarId = Number(duration.substring(2));
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'ru'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }
                    %>
                    <div class="indent"><span>Waits for inputs: </span><%= task.TTASK_PETRI_NET_INPUT %></div>
                    <div class="indent"><span>Start of task: </span><%= dueOffset %></div>
                    <div class="indent"><span>Duration: </span><%= duration %></div>
                    <div class="indent"><span>The task finishes the whole case: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_SUFFICIENT_END, locale: 'ru'}) %></div>
                    <div class="indent"><span>Run task only once: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_RUN_ONLY_ONCE, locale: 'ru'}) %></div>
                    <div class="indent"><span>Waits for all, started by first: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_DISC_FLAG, locale: 'ru'}) %></div>
                    <div class="indent"><span>Multi-instance: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_MULTIINSTANCE_FLAG, locale: 'ru'}) %></div>
                    <% if (task.TTASK_MULTIINSTANCE_FLAG === 'Y') { %>
                        <div class="indent"><span>Iterate over variables: </span><%= task.TTASK_ITERATE_OVER %></div>
                    <% } %>

                    <!-- Task variables -->
                    <% if (task.TTASK_TYPE !== 'A' && Array.isArray(variables) && variables.length > 0) { %>
                        <br>Variables<br>
                        <% varUsage.forEach((u) => { %>
                            <% if (u.TTASK_ID === task.TTASK_ID) { %>
                                <div class="usage">
                                    <a href=<%= "#var" + u.TVAR_ID %>><%= (_.find(variables, {TVAR_ID: u.TVAR_ID}) || {}).TVAR_NAME + ' (' +
                                    globalThis.__({phrase: 'duty' + u.TTASKVARUSG_USAGE, locale: 'ru'}) + ')' %></a>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Calculations -->
                    <% if (task.TTASK_CALC_COUNT > 0 && Array.isArray(calculations) && calculations.length > 0) { %>
                        <br>Calculations (<%= task.TTASK_NAME %>)<br>
                        <% calculations.forEach((calc) => { %>
                            <% if (calc.TTASK_ID === task.TTASK_ID) { %>
                                <div class="indent">
                                    <%
                                        let run = calc.TTJSCALC_EXEC_START === 'Y' ? globalThis.__({phrase: 'runStart', locale: 'ru'}) + ', ': '';
                                        run = run + (calc.TTJSCALC_EXEC_END === 'Y' ? globalThis.__({phrase: 'runEnd', locale: 'ru'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_HAND === 'Y' ? globalThis.__({phrase: 'runHand', locale: 'ru'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_RECALC === 'Y' ? globalThis.__({phrase: 'runCalc', locale: 'ru'}) + ', ' : '');
                                        run = run.slice(0, -2);
                                    %>
                                    <%= calc.TTJSCALC_JS %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'run: ' + run %>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Links -->
                    <% if (Array.isArray(taskLinks) && taskLinks.length > 0) { %>
                        <br>Links<br>
                        <% taskLinks.forEach((link) => { %>
                            <% if (link.TTASKLINK_FROM_TTASK_ID === task.TTASK_ID) { %>
                                <div class="link">
                                    <%
                                        let linkType = globalThis.__({phrase: 'linkType' + link.TTASKLINK_TYPE, locale: 'ru'});
                                        if(link.raw.COND_COUNT === 0 && link.TTASKLINK_TYPE !== 'ELSE') linkType = globalThis.__({phrase: 'linkTypeNULL', locale: 'ru'});
                                    %>

                                    <a href=<%= "#" + link.TTASKLINK_TO_TTASK_ID %>><%= (_.find(tasks, {TTASK_ID: link.TTASKLINK_TO_TTASK_ID}) || {}).TTASK_NAME +
                                    ' (' + linkType + ')' %></a>
                                </div>
                                <ul>
                                    <% for(let i = 0; i < conditions.length; i++) { %>
                                        <% if (conditions[i].TTASKLINK_ID === link.TTASKLINK_ID) { %>
                                            <li>
                                                <% const condValue = conditions[i].TCOND_VALUE || ''; %>
                                                <div class="condition"><%- conditions[i].TCOND_VARIABLE +
                                                ' ' + globalThis.__({phrase: 'operator' + conditions[i].TCOND_OPERATOR, locale: 'ru'}) + ' ' + condValue %></div>
                                            </li>
                                        <% } %>
                                    <% } %>
                                </ul>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Instructions -->
                    <% if (task.TTASK_INSTRUCTION) { %>
                        <br>Instructions<br>
                        <div class="indent"><%- task.TTASK_INSTRUCTION %></div>
                    <% } %>

                    <!-- E-mail -->
                    <% if (task.TTASK_TYPE === 'N') { %>
                        <br>E-mail<br>
                        <div class="indent"><span>Recipient: </span><%= globalThis.__({phrase: 'recipient' + task.TTASK_ENOT_TGT_TYPE, locale: 'ru'}) + ': ' + task.TTASK_ENOT_TGT %></div>
                        <div class="indent"><span>Subject: </span><%= task.TTASK_ENOT_SUBJECT %></div>
                        <% const selector = "email-body" + task.TTASK_ID; %>
                        <div class="indent"><span>Body: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <div id=<%= selector %> class="email openable closed"><%- task.TTASK_ENOT_BODY2 %></div>
                        </div>
                    <% } %>

                    <!-- Conditions - task JS -->
                    <% if (task.TTASK_JS) {
                        const selector = "conds" + task.TTASK_ID; %>
                        <br>Conditions <button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button><br>
                        <div class="indent">
                            <pre id=<%= selector %> class="openable closed"><%= task.TTASK_JS %></pre>
                        </div>
                    <% } %>
                </div>
            <% }); %>
        </div>
    </body>
</html>