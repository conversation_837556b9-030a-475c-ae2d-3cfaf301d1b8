<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><%= template.TPROC_NAME %></title>
        <%- include('../process') %>
        <%- include('../script') %>
        <%- include('../functions') %>
    </head>
    <body>
        <%
            moment.locale('hr');
            let usedRoles = [];
            let usedUsers = [];
            tasks.forEach((task) => {
                if (task.TTASK_ASSESMENT_ROLE_NAME) usedRoles.push(task.TTASK_ASSESMENT_ROLE_NAME);
                if (task.TTASK_ASSESMENT_USER_ID) usedUsers.push(_.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME);
            });
        %>
        <h1><%= template.TPROC_NAME %></h1>
        <div><span>Opis: </span><%= template.TPROC_DESCRIPTION %></div>
        <div><span>Vidljivo clanovima organizacijske grupe: </span><%= (_.find(orgUnits, {ORGSTR_ID: template.TPROC_VIS_ORGSTR_ID}) || {}).ORGSTR_NAME %></div>
        <div><span>Vidljivo clanovima s ulogama: </span><%= template.raw.TPROC_VIS_ROLE_NAME %></div>
        <div><span>Autor posljednje izmjene: </span><%= template.raw.TPROC_LAST_CHANGED_BY_USER %></div>
        <div><span>Upotrijebljene uloge: </span><%= _.uniq(usedRoles).join(', ') %></div>
        <div><span>Upotrijebljeni korisnici: </span><%= _.uniq(usedUsers).join(', ') %></div>
        <div><span>Datum zadnje promjene: </span><%= moment(template.TPROC_LAST_CHANGED_DATE, 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><span>Izrada datuma dokumenta: </span><%= moment(new Date(), 'YYYY-MM-DDTHH:mm:ss').format("LLL") %></div>
        <br>
        <div><button id="toggle-all" onclick="openCloseAll()">Proširi/zatvori uvjete i definicije <span>&#9660;</span></button></div>
        <br>

        <!-- Headers -->
        <div class="headers">
            <h2>Zaglavlja</h2>
            <% headers.forEach((header) => { %>
                <div><span>Kod zaglavlja: </span><%= header.HEADER_CODE %></div>
                <div><span>Zadano ime glave: </span><%= header.HEADER_NAME %></div>
                <div><span>Aktivan: </span><%= globalThis.__({phrase: 'yesNo' + header.HEADER_ENABLED, locale: 'hr'}) %></div>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'HEADER_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= header.raw[translatedName] %></div>
                    <% });
                } %>
                <div><span>Org. jedinice: </span><%= header.HEADER_ORGSTRS.length %></div>
                <div><span>Uloge: </span><%= header.HEADER_ROLES && header.HEADER_ROLES.length %></div>
                <div><span>Slucaj vidljiv clanovima s ulogom: </span><%= header.raw.HDR_VIS_ROLE_NAME %></div>
                <br>
            <% }); %>
        </div>

        <!-- Variables -->
        <div class="variables">
            <h2>Varijable</h2>
            <% variables.forEach((variable) => { %>
                <h3><a name=<%= "var" + variable.TVAR_ID %>><%= variable.TVAR_NAME %></h3>
                <div><span>Tip: </span><%= globalThis.__({phrase: 'tvarType' + variable.TVAR_TYPE, locale: 'hr'}) %></div>
                <div><span>Vrijednosti: </span><%= getVariableValue(variable, dateFormat) %></div>
                <%
                    const meta = JSON.parse(variable.TVAR_META || "{}");
                    if (variable.TVAR_TYPE === 'DR') {
                        const definition = meta.tableDefinition;
                        const selector = "var-def" + variable.TVAR_ID; %>
                        <div><span>Definicija: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <pre id=<%= selector %> class="openable closed"><%= definition %></pre>
                        </div>
                    <% }
                    else if (variable.TVAR_TYPE === 'T' && meta.suggestBox) { %>
                        <div><span>Url: </span><%= meta.suggestBox.apiUrl %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'column: ' + meta.suggestBox.prop %></div>
                    <% }
                    else if (variable.TVAR_TYPE === 'N') {
                        const definition = meta.numberOfDecimals; %>
                        <div><span>Decimale: </span><%= definition %></div>
                    <% }
                %>
                <% if (Array.isArray(globalThis.dynamicConfig.langs)) {
                    globalThis.dynamicConfig.langs.forEach(lang => {
                        lang = lang.toUpperCase();
                        var translatedName = 'TVAR_NAME_' + lang; %>
                        <div><span><%= lang + ': ' %></span><%= variable.raw[translatedName] %></div>
                    <% });
                } %>
            <% }); %>
        </div>

        <!-- Tasks -->
        <div class="tasks">
            <h2>Zadaci</h2>
            <% tasks.forEach((task) => { %>
                <div class="task">
                    <a name=<%= task.TTASK_ID %>></a>
                    <h3><%= task.TTASK_NAME %></h3>
                    <div><span>Vrsta zadatka: </span><%= globalThis.__({phrase: 'taskType' + task.TTASK_TYPE, locale: 'hr'}) %>
                        <% if (task.TTASK_TYPE === 'E') { %>
                            <span> (<%= task.TTASK_EVENT %>)</span>
                        <% } %>
                    </div>
                    <% if (task.TTASK_TYPE === 'P') {
                        const subp = _.find(processes, {TPROC_ID: task.TTASK_SUBPROCESS_TPROC_ID}) || {}; %>
                        <div><span>Podprocesni podaci: </span><%= subp.TPROC_DESCRIPTION ? '(' + subp.TPROC_DESCRIPTION + ')' : subp.TPROC_NAME %></div>
                        <% if (task.MAPPING && task.MAPPING.subp) { %>
                            <% task.MAPPING.subp.forEach((subp) => {
                                let subpTitle;
                                if (subp.evepar_name === '$CONSTANT' || subp.evepar_name === '$CALC') {
                                    subpTitle = subp.rdefpar_name + ' <- ' + (subp.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subp.rdefpar_value + (subp.evepar_name === '$CONSTANT' ? '"' : '');
                                } else {
                                    subpTitle = subp.rdefpar_name + ' <- ' + subp.evepar_name;
                                }
                            %>
                                <div><span>&nbsp; &nbsp; Ulaz: </span><%= subpTitle %></div>
                            <% }); %>
                        <% } %>
                        <% if (task.MAPPING && task.MAPPING.subr) { %>
                            <% task.MAPPING.subr.forEach((subr) => {
                                let subrTitle;
                                if (subr.evepar_name === '$CONSTANT' || subr.evepar_name === '$CALC') {
                                    subrTitle = subr.rdefpar_name + ' -> ' + (subr.evepar_name === '$CONSTANT' ? '"' : '')
                                        + subr.rdefpar_value + (subr.evepar_name === '$CONSTANT' ? '"' : '');
                                } else {
                                    subrTitle = subr.rdefpar_name + ' -> ' + subr.evepar_name;
                                }
                            %>
                                <div><span>&nbsp; &nbsp; Izlaz: </span><%= subrTitle %></div>
                            <% }); %>
                        <% } %>
                    <% } %>
                    <div><span>Generiraj zapis u povijest: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_GEN_HISTORY, locale: 'hr'}) %></div>
                    <%
                        let supervizor = 'vlasnik slučaja';
                        if (task.TTASK_ASSESMENT_USER_ID) supervizor = _.find(users, {USER_ID: task.TTASK_ASSESMENT_USER_ID}).USER_NAME;
                        if (task.TTASK_ASSESMENT_ORGSTR_ID) {
                            supervizor = "voditelj organizacijske jedinice " + (_.find(orgUnits, {ORGSTR_ID: task.TTASK_ASSESMENT_ORGSTR_ID}) || {}).ORGSTR_NAME;
                        }
                    %>
                    <div><span>Nadzornik zadatka: </span><%= supervizor %></div>
                    <div><span>Referentna osoba: </span><%= task.TTASK_REFERENCE_USER %></div>
                    <% if (task.TTASK_DESCRIPTION) { %>
                        <div><span>Opis zadatka: </span><%= task.TTASK_DESCRIPTION %></div>
                    <% } %>

                    <!-- Solver -->
                    <% if (task.TTASK_TYPE !== 'A') { %>
                        <br>Vlasnik zadatka<br>
                        <div class="indent"><span>Uloga: </span><%= task.TTASK_ASSESMENT_ROLE_NAME %></div>
                        <div class="indent"><span>Organizacijska jedinica: </span><%= task.TTASK_ASSESMENT_ORGSTR_NAME %></div>
                        <div class="indent"><span>Odnos prema referentnoj osobi: </span><%= globalThis.__({phrase: 'assesmentHierarchy' + task.TTASK_ASSESMENT_HIERARCHY, locale: 'hr'}) %></div>
                        <div class="indent"><span>Zadatak ce biti dodijeljen: </span><%= globalThis.__({phrase: 'assesmentMethod' + task.TTASK_ASSESMENT_METHOD, locale: 'hr'}) + ' ' %>
                            <% if (task.TTASK_ASSESMENT_METHOD === 'L') { %>
                                <a href=<%= '#' + task.TTASK_ASSESMENT_TTASK_ID %>><%= _.find(tasks, {TTASK_ID: task.TTASK_ASSESMENT_TTASK_ID}).TTASK_NAME %></a>
                            <% } %>
                        </div>
                        <% if (task.TTASK_ASSESMENT_METHOD === 'V') { %>
                            <div class="indent"><span>Naziv varijable: </span><%= _.find(variables, {TVAR_ID: task.TTASK_ASSESMENT_TVAR_ID}).TVAR_NAME %></div>
                        <% } %>
                        <div class="indent"><span>Pravo / Dužnost: </span><%= globalThis.__({phrase: 'rightDuty' + task.TTASK_DUTY, locale: 'hr'}) %></div>
                    <% } %>

                    <!-- Planning -->
                    <br>Planiranje<br>
                    <%
                        let dueOffset = task.TTASK_DUE_OFFSET || globalThis.__({phrase: 'dueOffsetno', locale: 'hr'});
                        let duration = task.TTASK_DURATION || globalThis.__({phrase: 'durationno', locale: 'hr'});

                        const dueOffsetStart = dueOffset.substring(0,2);
                        const durationStart = duration.substring(0,2);

                        if (dueOffsetStart === 'P+' || dueOffsetStart === 'T+') {
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'hr'}, {variable: task.TTASK_DUE_OFFSET});
                        }
                        else if (dueOffsetStart === 'vo' || dueOffsetStart === 'vc') {
                            const tvarId = Number(dueOffset.substring(2));
                            dueOffset = globalThis.__({phrase: 'dueOffset' + dueOffsetStart, locale: 'hr'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }

                        if (durationStart === 'P+' || durationStart === 'T+') {
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'hr'}, {variable: task.TTASK_DURATION});
                        }
                        else if (durationStart === 'vo' || durationStart === 'vc') {
                            const tvarId = Number(duration.substring(2));
                            duration = globalThis.__({phrase: 'duration' + durationStart, locale: 'hr'}, {variable: (_.find(variables, {TVAR_ID: tvarId}) || {}).TVAR_NAME});
                        }
                    %>
                    <div class="indent"><span>Ceka ulaze: </span><%= task.TTASK_PETRI_NET_INPUT %></div>
                    <div class="indent"><span>Pocetak zadatka: </span><%= dueOffset %></div>
                    <div class="indent"><span>Trajanje: </span><%= duration %></div>
                    <div class="indent"><span>Zadatak završava cijeli slucaj: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_SUFFICIENT_END, locale: 'hr'}) %></div>
                    <div class="indent"><span>Pokreni zadatak samo jednom: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_RUN_ONLY_ONCE, locale: 'hr'}) %></div>
                    <div class="indent"><span>Ceka sve, pocela prva: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_DISC_FLAG, locale: 'hr'}) %></div>
                    <div class="indent"><span>Multi-instance: </span><%= globalThis.__({phrase: 'yesNo' + task.TTASK_MULTIINSTANCE_FLAG, locale: 'hr'}) %></div>
                    <% if (task.TTASK_MULTIINSTANCE_FLAG === 'Y') { %>
                        <div class="indent"><span>Ponavljajte varijable: </span><%= task.TTASK_ITERATE_OVER %></div>
                    <% } %>

                    <!-- Task variables -->
                    <% if (task.TTASK_TYPE !== 'A' && Array.isArray(variables) && variables.length > 0) { %>
                        <br>Varijable<br>
                        <% varUsage.forEach((u) => { %>
                            <% if (u.TTASK_ID === task.TTASK_ID) { %>
                                <div class="usage">
                                    <a href=<%= "#var" + u.TVAR_ID %>><%= (_.find(variables, {TVAR_ID: u.TVAR_ID}) || {}).TVAR_NAME + ' (' +
                                    globalThis.__({phrase: 'duty' + u.TTASKVARUSG_USAGE, locale: 'hr'}) + ')' %></a>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Calculations -->
                    <% if (task.TTASK_CALC_COUNT > 0 && Array.isArray(calculations) && calculations.length > 0) { %>
                        <br>Izracuni (<%= task.TTASK_NAME %>)<br>
                        <% calculations.forEach((calc) => { %>
                            <% if (calc.TTASK_ID === task.TTASK_ID) { %>
                                <div class="indent">
                                    <%
                                        let run = calc.TTJSCALC_EXEC_START === 'Y' ? globalThis.__({phrase: 'runStart', locale: 'hr'}) + ', ': '';
                                        run = run + (calc.TTJSCALC_EXEC_END === 'Y' ? globalThis.__({phrase: 'runEnd', locale: 'hr'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_HAND === 'Y' ? globalThis.__({phrase: 'runHand', locale: 'hr'}) + ', ' : '');
                                        run = run + (calc.TTJSCALC_EXEC_RECALC === 'Y' ? globalThis.__({phrase: 'runCalc', locale: 'hr'}) + ', ' : '');
                                        run = run.slice(0, -2);
                                    %>
                                    <%= calc.TTJSCALC_JS %>&nbsp; &nbsp; &nbsp; &nbsp;<%= 'run: ' + run %>
                                </div>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Links -->
                    <% if (Array.isArray(taskLinks) && taskLinks.length > 0) { %>
                        <br>Linkovi<br>
                        <% taskLinks.forEach((link) => { %>
                            <% if (link.TTASKLINK_FROM_TTASK_ID === task.TTASK_ID) { %>
                                <div class="link">
                                    <%
                                        let linkType = globalThis.__({phrase: 'linkType' + link.TTASKLINK_TYPE, locale: 'hr'});
                                        if(link.raw.COND_COUNT === 0 && link.TTASKLINK_TYPE !== 'ELSE') linkType = globalThis.__({phrase: 'linkTypeNULL', locale: 'hr'});
                                    %>

                                    <a href=<%= "#" + link.TTASKLINK_TO_TTASK_ID %>><%= (_.find(tasks, {TTASK_ID: link.TTASKLINK_TO_TTASK_ID}) || {}).TTASK_NAME +
                                    ' (' + linkType + ')' %></a>
                                </div>
                                <ul>
                                    <% for(let i = 0; i < conditions.length; i++) { %>
                                        <% if (conditions[i].TTASKLINK_ID === link.TTASKLINK_ID) { %>
                                            <li>
                                                <% const condValue = conditions[i].TCOND_VALUE || ''; %>
                                                <div class="condition"><%- conditions[i].TCOND_VARIABLE +
                                                ' ' + globalThis.__({phrase: 'operator' + conditions[i].TCOND_OPERATOR, locale: 'hr'}) + ' ' + condValue %></div>
                                            </li>
                                        <% } %>
                                    <% } %>
                                </ul>
                            <% } %>
                        <% }); %>
                    <% } %>

                    <!-- Instructions -->
                    <% if (task.TTASK_INSTRUCTION) { %>
                        <br>Instrukcije<br>
                        <div class="indent"><%- task.TTASK_INSTRUCTION %></div>
                    <% } %>

                    <!-- E-mail -->
                    <% if (task.TTASK_TYPE === 'N') { %>
                        <br>E-mail<br>
                        <div class="indent"><span>Primatelj: </span><%= globalThis.__({phrase: 'recipient' + task.TTASK_ENOT_TGT_TYPE, locale: 'hr'}) + ': ' + task.TTASK_ENOT_TGT %></div>
                        <div class="indent"><span>Predmet: </span><%= task.TTASK_ENOT_SUBJECT %></div>
                        <% const selector = "email-body" + task.TTASK_ID; %>
                        <div class="indent"><span>Tijelo: </span><button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button>
                            <div id=<%= selector %> class="email openable closed"><%- task.TTASK_ENOT_BODY2 %></div>
                        </div>
                    <% } %>

                    <!-- Conditions - task JS -->
                    <% if (task.TTASK_JS) {
                        const selector = "conds" + task.TTASK_ID; %>
                        <br>Uvjeti <button class="opening-btn" onclick="openClose(this, '<%= selector %>')"><span>&#9660;</span></button><br>
                        <div class="indent">
                            <pre id=<%= selector %> class="openable closed"><%= task.TTASK_JS %></pre>
                        </div>
                    <% } %>
                </div>
            <% }); %>
        </div>
    </body>
</html>