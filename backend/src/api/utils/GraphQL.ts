// @ts-nocheck
// @ts-nocheck
import { GraphQLClient, gql } from "graphql-request";
import { UtilsService } from "../services/UtilsService";

export class GraphQL {
    static async getCalculationExamples() {
        // Stop if disabled
        if (!globalThis.dynamicConfig.tas.calculations.getExamples) {
            return [];
        }

        // Wiki.js endpoint
        // https://wiki.teamassistant.app/graphql
        const endpoint =
            globalThis.dynamicConfig.tas.calculations.getExamplesRequestURL;

        const graphQLClient = new GraphQLClient(endpoint, {
            headers: {
                // Authorization: Bearer token with a limited 'read-only' group access
                // https://wiki.teamassistant.app/a/groups/5
                ...JSON.parse(
                    globalThis.dynamicConfig.tas.calculations
                        .getExamplesRequestHeaders,
                ),
            },
        });

        // Retrieve all examples by locale and tag
        /* {
            pages {
                list(locale: "cs", tags: ["calculation-example"]) {
                    path
                    render
                }
            }
        } */
        const query = gql`
            ${globalThis.dynamicConfig.tas.calculations.getExamplesRequestGQL}
        `;

        // Wait max. 10 seconds, then return an empty Array
        const {
            pages: { list },
        } = await UtilsService.timeout(
            graphQLClient.request(query),
            10000,
        ).catch(async (err) => {
            globalThis.tasLogger.error("Error while getting WikiJs data", {
                err,
            });
            return { pages: [] };
        });

        return list;
    }
}
