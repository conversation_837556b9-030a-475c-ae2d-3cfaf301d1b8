// @ts-nocheck
// @ts-nocheck
import { Holder<PERSON><PERSON> } from "../../entrypoint/calculation/HolderApi";
import { TaskJsCalculations } from "../../infrastructure/calculation/TaskJsCalculations";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import * as calcConst from "../orm/entity/const/calcConsts";

/**
 * This class can parse soap binding language.
 * Binding looks like:
 * VarName={soap.result.property} // Can be property of result object.
 * VarName2 = {soap.result.property} * 2 // Can be calculation
 */
export class CalcBinding {
    constructor(orm, currentUser, task) {
        this.orm = orm;
        this.currentUser = currentUser;
        this.task = task;

        // Ini
        this.variables = [];
    }

    /**
     * Set variables to later parsing.
     * @param {Array<Variable>}variables
     */
    setVariables(variables) {
        if (!Array.isArray(variables) || variables.length === 0) {
            return;
        }
        this.variables = variables;
    }

    /**
     * Parse binding. This will affect setVariables().
     * @param {string} calc Calculation to assign variables.
     * @param {object} soapResult Result data from soap call. Source data for binding.
     * @returns {*}
     */
    calculate(calc, soapResult) {
        // Nothing to parse.
        if (!calc) {
            return calc;
        }

        // Check type
        if (typeof calc !== "string") {
            throw new InternalException("Binding must be typeof string.");
        }

        const soapResultHolder = new HolderApi(this.orm.connection, {
            value: soapResult,
        });
        soapResultHolder.setPropertyMap({
            "@result": "@result",
            result: "@result",
            "@response": "@result",
            response: "@result",
            "@error": "?soap_error",
            error: "?soap_error",
        });

        const calcs = new TaskJsCalculations(this.orm, globalThis.tasLogger);
        calcs.setSandboxApi(calcConst.HOLDER, soapResultHolder);
        calcs.setVariables(this.variables);
        return calcs.evaluateJS(
            this.currentUser,
            this.task,
            this.process,
            calc,
        );
    }
}
