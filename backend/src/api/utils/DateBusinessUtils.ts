// @ts-nocheck
// @ts-nocheck
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";

import momentHours from "moment-business-time";
import { DateUtils } from "./DateUtils";

/**
 * visit
 * https://www.npmjs.com/package/moment-business-hours
 */
export class DateBusinessUtils extends DateUtils {
    /**
     * Use this class in cases you need date calculations with working/holiday days.
     *
     * @param params Usuallly globalThis.dynamicConfig.tas.calculations
     * @param startWork Force start work time. default=params.workingHours
     * @param endWork Force end work time. default=params.workingHours
     */
    constructor(params, startWork?, endWork?) {
        super();

        if (!params) {
            throw new InternalException(
                "DateBusinnesUtils must have locale configuration while instancing.",
            );
        }

        // Are working hours defined ? or take it from base config.
        const workingHours =
            startWork && endWork
                ? {
                      0: null,
                      1: [startWork, endWork],
                      2: [startWork, endWork],
                      3: [startWork, endWork],
                      4: [startWork, endWork],
                      5: [startWork, endWork],
                      6: null,
                  }
                : null;

        this.hollidays = params.hollidays;
        this.workingHours = workingHours || params.workingHours;

        momentHours.updateLocale(params.locale, {
            holidays: this.hollidays.concat(this.repeatedHolidays()),
            workinghours: this.workingHours,
        });
    }

    addBussinesDays(date, daysAdd) {
        return momentHours(date).addWorkingTime(Number(daysAdd), "days")._d;
    }

    substractBussinesDays(date, daysSubstract) {
        return momentHours(date).subtractWorkingTime(
            Number(daysSubstract),
            "days",
        )._d;
    }

    /**
     * Add/Substract working hours.
     * @param {Date} date
     * @param {integer} hoursAdd can be negative.
     * @param {string} startWork
     * @param {string} endWork
     * @return {Date}
     */
    addWorkingHours(date, hoursAdd) {
        if (Number(hoursAdd) === 0) {
            return date;
        }

        if (hoursAdd > 0) {
            return momentHours(date).addWorkingTime(Number(hoursAdd), "hours")
                ._d;
        }
        return momentHours(date).subtractWorkingTime(Number(hoursAdd), "hours")
            ._d;
    }

    /**
     * Get working hours between d1 and d2
     * @param {Date} d1
     * @param {Date} d2
     * @param {string} startWork
     * @param {string} endWork
     * @return {number}
     */
    workingHoursDiff(d1, d2) {
        return momentHours(d2).workingDiff(momentHours(d1), "hours");
    }

    /**
     * Get the working time between d1 and d2 in the specified unit
     * @param {Date} d1
     * @param {Date} d2
     * @param {string} unit  milliseconds, seconds, minutes, hours, days, weeks, months, years
     * @return {number}
     */
    workingTimeDiff(d1, d2, unit = "minutes") {
        return momentHours(d2).workingDiff(momentHours(d1), unit);
    }

    /**
     * Get business days between d1 and d2
     * @param {Date} d1
     * @param {Date} d2
     * @return {number}
     */
    businessDaysDiff(d1, d2) {
        const diff = momentHours(d1).workingDiff(momentHours(d2), "days");
        return Math.ceil(Math.abs(diff)); // positive number rounded up
    }

    repeatedHolidays() {
        return [
            "*-01-01", // 1.leden - Den obnovy samostatného českého státu',
            "*-05-08", // 8. květen - Den vítězství',
            "*-07-05", // 5. červenec - Den slovanských věrozvěstů Cyrila a Metoděje
            "*-07-06", // 6. červenec - Den upálení mistra Jana Husa',
            "*-09-28", // 28. září - Den české státnosti\',',
            "*-10-28", // 28. říjen - Den vzniku samostatného československého státu
            "*-11-17", // 17. listopad - Den boje za svobodu a demokracii
            "*-05-01", // 1. květen - Svátek práce',
            "*-12-24", // 24. prosinec - Štědrý den
            "*-12-25", // 25. prosinec - 1. svátek vánoční
            "*-12-26", // 26. prosinec - 2. svátek vánoční',*/
            "2019-04-19", // Velký pátek 2019
            "2020-04-13", // Velikonoční pondělí 2020
            "2020-04-10", // Velký pátek 2020
            "2021-04-05", // Velikonoční pondělí 2021
            "2021-04-02", // Velký pátek 2021
            "2022-04-18", // Velikonoční pondělí 2022
            "2022-04-15", // Velký pátek 2022
            "2023-04-10", // Velikonoční pondělí 2023
            "2023-04-07", // Velký pátek 2023
            "2024-04-01", // Velikonoční pondělí 2024
            "2024-03-29", // Velký pátek 2024
            "2025-04-21", // 'Velikonoční pondělí 2025
            "2026-04-06", // Velikonoční pondělí 2026
            "2027-03-29", // 'Velikonoční pondělí 2027
        ];
    }
}
