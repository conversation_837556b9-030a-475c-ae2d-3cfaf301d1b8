// @ts-nocheck
// @ts-nocheck
import { parse } from "csv-parse/sync";
import { TextFileReader } from "./TextFileReader";

export class CsvIterator {
    /**
     * if returnAsObject & firstLineHeaders then mapped object returned. If no headers index of column used as key.
     * @param firstLineHeaders
     * @param {boolean} returnAsObject Each row is returned as array or object.
     * @param {string} delimiter
     * @param {string} quotes
     */
    constructor(
        firstLineHeaders = true,
        returnAsObject = true,
        delimiter = ";",
        quotes = '"',
    ) {
        this.delimiter = delimiter;
        this.quotes = quotes;
        this.firstLineHeaders = firstLineHeaders;
        this.returnAsObject = returnAsObject;
        this.currentIndex = 0;
    }

    async fromFile(filePath, encoding = "utf-8") {
        const csvContent = await TextFileReader.readFile(filePath, encoding);
        return await this.fromString(csvContent);
    }

    async fromString(csvContent) {
        const result = parse(csvContent, {
            delimiter: this.delimiter,
            quote: this.quotes,
            bom: true,
            skip_empty_lines: true,
        });
        this.jsonData = result;

        if (this.firstLineHeaders) {
            this.headers = result.shift();
        }
        if (this.returnAsObject) {
            this.headers = this.getHeaders();
        }

        return result;
    }

    /**
     * Returns true or false if there is next row in csv.
     * @returns {boolean}
     */
    hasNext() {
        return (this.jsonData || []).length > this.currentIndex;
    }

    getAllRows() {
        const rows = [];
        while (this.hasNext()) {
            rows.push(this.getNext());
        }
        return rows;
    }

    /**
     * Returns first row if firstLineHeaders otherwise returns array from [0, 1, .., colsCount].
     * @returns {*}
     */
    getHeaders() {
        if (this.firstLineHeaders) {
            return this.headers;
        }
        const firstRow = this.jsonData[0];
        const headers = [];
        for (let i = 0; i < firstRow.length; i += 1) {
            headers.push(i);
        }
        return headers;
    }

    /**
     * Returns array of columns from current row.
     * @returns {Array<string>}
     */
    getNext() {
        const row = this.jsonData[this.currentIndex];
        // Map row with headers.
        if (this.returnAsObject) {
            const mappedRow = {};
            for (let i = 0; i < this.headers.length; i += 1) {
                const header = this.headers[i];
                mappedRow[header] = row[i];
            }

            this.currentIndex += 1;
            return mappedRow;
        }

        this.currentIndex += 1;
        return row;
    }

    /**
     * Get row from data. Index = 0 never return header. Use getHeaders instead !
     * This does not move current pointer index !
     * @param {number} index
     * @returns {*}
     */
    getRow(index) {
        // Keep currentIndex to restore on end.
        const prevIndex = this.currentIndex;
        this.currentIndex = index;

        // Read row
        const row = this.getNext();

        // Restore cursor index
        this.currentIndex = prevIndex;
        return row;
    }

    /**
     * Move pointer to first row.
     */
    reset() {
        if (this.firstLineHeaders) {
            this.currentIndex = 1;
            return;
        }
        this.currentIndex = 0;
    }

    /**
     * Return rows count. Header is not counted.
     */
    count() {
        return this.jsonData.length;
    }
}
