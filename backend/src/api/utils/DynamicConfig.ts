// @ts-nocheck
// @ts-nocheck
import { LogCategory } from "../../utils/logger/logConsts";

export class DynamicConfig {
    async onChange(path, value) {
        // Find and change configuration via globalThis.globalThis....
        await this.tasLogger.runTask(async () => {
            this.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_CONFIGURATION,
            );
            const parts = path.split(".");
            let curVal = global;
            for (let i = 0; i < parts.length - 1; i += 1) {
                curVal = curVal[parts[i]];
            }
            const prevValue = curVal[parts[parts.length - 1]];
            curVal[parts[parts.length - 1]] = value;
            globalThis.tasLogger.info(
                `Configuration (${path}) has changed from ${prevValue} -> ${globalThis.dynamicConfig.logger.logLevel}.`,
            );
        });
    }
}
