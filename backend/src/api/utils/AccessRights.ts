// @ts-nocheck
// @ts-nocheck
import { Knex } from "@mikro-orm/knex";
import { UserException } from "../../utils/errorHandling/exceptions/userException";

export interface IAccessRights {
    R: string;
    W: string;
    D: string;
}

export class AccessRights {
    constructor(trx: Knex) {
        this.connection = trx;
    }

    public async getAccessRight(
        tableName,
        columnId?: number,
        userId,
    ): Promise<IAccessRights> {
        let acc = {
            R: "N",
            W: "N",
            D: "N",
        };

        const rights = await this.getUsersRolesRights(tableName, userId);
        acc = rights;

        tableName = tableName.toUpperCase();
        const self = this;

        const isOwner = await (async () => {
            if (!columnId || typeof columnId === "undefined") {
                return false;
            }
            if (tableName === "TEMPLATE_GRAPH") {
                const result = await this.getAttribute(
                    "TEMPLATE_GRAPH",
                    "TGRAPH_ID",
                    columnId,
                    "TPROC_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result,
                    "TPROC_OWNER_USER_ID",
                );
                return result2 === userId;
            }

            if (tableName === "TEMPLATE_LINK_CONDITIONS") {
                const result = await this.getAttribute(
                    "TEMPLATE_LINK_CONDITIONS",
                    "TCOND_ID",
                    columnId,
                    "TTASKLINK_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_TASK_LINKS",
                    "TTASKLINK_ID",
                    result,
                    "TPROC_ID",
                );
                const result3 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result2,
                    "TPROC_OWNER_USER_ID",
                );
                return result3 === userId;
            }

            if (tableName === "TEMPLATE_PROCESSES") {
                const result = await this.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    columnId,
                    "TPROC_OWNER_USER_ID",
                );
                return result === userId;
            }

            if (tableName === "TEMPLATE_TASK_LINKS") {
                const result = await this.getAttribute(
                    "TEMPLATE_TASK_LINKS",
                    "TTASKLINK_ID",
                    columnId,
                    "TPROC_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result,
                    "TPROC_OWNER_USER_ID",
                );
                return result2 === userId;
            }

            if (tableName === "TEMPLATE_TASK_VAR_USAGE") {
                const result = await this.getAttribute(
                    "TEMPLATE_TASK_VAR_USAGE",
                    "TTASKVARUSG_ID",
                    columnId,
                    "TPROC_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result,
                    "TPROC_OWNER_USER_ID",
                );
                return result2 === userId;
            }

            if (tableName === "TEMPLATE_TASKS") {
                const result = await this.getAttribute(
                    "TEMPLATE_TASKS",
                    "TTASK_ID",
                    columnId,
                    "TPROC_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result,
                    "TPROC_OWNER_USER_ID",
                );
                return result2 === userId;
            }

            if (tableName === "TEMPLATE_VARIABLE_LOV") {
                const result = await this.getAttribute(
                    "TEMPLATE_VARIABLE_LOV",
                    "TVARLOV_ID",
                    columnId,
                    "TVAR_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_VARIABLES",
                    "TVAR_ID",
                    result,
                    "TPROC_ID",
                );
                const result3 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result2,
                    "TPROC_OWNER_USER_ID",
                );
                return result3 === userId;
            }

            if (tableName === "TEMPLATE_VARIABLES") {
                const result = await this.getAttribute(
                    "TEMPLATE_VARIABLES",
                    "TVAR_ID",
                    columnId,
                    "TPROC_ID",
                );
                const result2 = await self.getAttribute(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    result,
                    "TPROC_OWNER_USER_ID",
                );
                return result2 === userId;
            }

            if (tableName === "TEMPLATE_TASK_VAR_PROC_MAP") {
                const taskId = columnId[0];
                const tvarIdProc = columnId[1];
                const access1 = await self.getAccessRight(
                    "TEMPLATE_TASKS",
                    taskId,
                    userId,
                );
                const access2 = await self.getAccessRight(
                    "TEMPLATE_VARIABLES",
                    tvarIdProc,
                    userId,
                );
                if (access1.W === "Y" && access2.W === "Y") {
                    return true;
                }
                return false;
            }

            if (tableName === "INSTANCE_GRAPH") {
                return false;
            }

            if (tableName === "INSTANCE_LINK_CONDITIONS") {
                return false;
            }

            if (tableName === "INSTANCE_PROCESSES") {
                const result = await this.getAttribute(
                    "INSTANCE_PROCESSES",
                    "IPROC_ID",
                    columnId,
                    "IPROC_INST_OWNER_USER_ID",
                );

                if (result === userId) {
                    return true;
                }
                acc.R = "Y";
                acc.W = "Y";
                acc.D = "N";

                return false;
            }

            if (tableName === "INSTANCE_TASK_LINKS") {
                return false;
            }

            if (tableName === "INSTANCE_TASKS") {
                const procId = await self.getAttribute(
                    "INSTANCE_TASKS",
                    "ITASK_ID",
                    columnId,
                    "IPROC_ID",
                );
                const access = await self.getAccessRight(
                    "INSTANCE_PROCESSES",
                    procId,
                    userId,
                );
                return !!access.W;
            }

            if (tableName === "INSTANCE_VARIABLE_LOV") {
                return false;
            }

            if (tableName === "INSTANCE_VARIABLES") {
                return false;
            }

            if (tableName === "INSTANCE_VARIABLES_SNAP") {
                return false;
            }

            if (tableName === "USERS") {
                acc.R = "Y";
                return false;
            }

            if (tableName === "CUSTOM_VIEWS") {
                const res = await self.getAttribute(
                    "CUSTOM_VIEWS",
                    "CV_ID",
                    columnId,
                    "CV_USER_ID",
                );
                if (userId === res) {
                    return true;
                }
                acc.R = "Y";
                return false;
            }

            if (tableName === "REPORT_GRAPHS") {
                const res = await self.getAttribute(
                    "REPORT_GRAPHS",
                    "GRAPH_ID",
                    columnId,
                    "GRAPH_USER_ID",
                );
                if (userId === res) {
                    return true;
                }
                acc.R = "Y";
                return false;
            }

            return false;
        })();

        if (isOwner) {
            acc.R = "Y";
            acc.W = "Y";
            acc.D = "Y";

            return acc;
        }
        const res = await this.getUserObjectRights(tableName, columnId, userId);
        this.combineAccessVectors(res, acc);
        return rights;
    }

    private async getAttribute(tableName, columnId, value, attr) {
        const res = await this.connection
            .select(attr)
            .from(tableName)
            .where(columnId, value);
        if (res && Array.isArray(res) && res.length > 0) {
            return res[0][attr];
        }
        throw new UserException(
            `Object with ${columnId} = ${value} from ${tableName} not found!`,
        );
    }

    private async getUsersRolesRights(
        tableName: string,
        userId: number,
    ): Promise<IAccessRights> {
        const res = await this.connection
            .select([
                this.connection.raw(`MAX("RACC_READ") "RACC_READ"`),
                this.connection.raw(`MAX("RACC_WRITE") "RACC_WRITE"`),
                this.connection.raw(`MAX("RACC_DELETE") "RACC_DELETE"`),
            ])
            .from("ROLE_ACCESS_RIGHTS as RAR")
            .joinRaw(`JOIN "ROLES" "R" ON "R"."ROLE_ID" = "RAR"."ROLE_ID"`)
            .leftJoin("USER_ROLES as UR", "UR.ROLE_ID", "R.ROLE_ID")
            .where("RAR.RACC_TABLE_NAME", tableName)
            .where("UR.USER_ID", userId);
        const defs = {
            R: "N",
            W: "N",
            D: "N",
        };

        if (res && Array.isArray(res) && res.length > 0) {
            defs.R = res[0].RACC_READ ?? "N";
            defs.W = res[0].RACC_WRITE ?? "N";
            defs.D = res[0].RACC_DELETE ?? "N";
        }
        return defs;
    }

    private async getUserObjectRights(
        tableName: string,
        columnId: number,
        userId: number,
    ): IAccessRights {
        if (typeof columnId === "undefined") {
            return {
                R: "N",
                W: "N",
                D: "N",
            };
        }

        const res = await this.connection
            .select(["UACC_READ", "UACC_WRITE", "UACC_DELETE"])
            .from("USER_ACCESS_RIGHTS")
            .where("UACC_TABLE_NAME", tableName)
            .where("UACC_COLUMN_ID", columnId)
            .where("UACC_USER_ID", userId);
        const defs = {
            R: "N",
            W: "N",
            D: "N",
        };
        if (res && Array.isArray(res) && res.length > 0) {
            defs.R = res[0].UACC_READ;
            defs.W = res[0].UACC_WRITE;
            defs.D = res[0].UACC_DELETE;
        }

        return res;
    }

    private combineAccessVectors(
        acc1: IAccessRights,
        acc2: IAccessRights,
    ): IAccessRights {
        const out = {
            R: "N",
            W: "N",
            D: "N",
        };

        if (acc1.R === "Y" || acc2.R === "Y") {
            out.R = "Y";
        }
        if (acc1.W === "Y" || acc2.W === "Y") {
            out.W = "Y";
        }
        if (acc1.D === "Y" || acc2.D === "Y") {
            out.D = "Y";
        }

        return out;
    }
}
