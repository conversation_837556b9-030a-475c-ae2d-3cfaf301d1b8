import path from "path";
import fs from "fs";
import _ from "lodash";
import * as configConsts from "./configConsts";
import { Config } from "./Config";
import { configVars } from "../../config/vars/config";
import * as localBase from "../../config/local.base";
import { Config as ConfigEntity } from "../orm/entity/Config";
import { Knex } from "knex";
import { Service } from "typedi";

const CUSTOM_CONFIG_ENV_NAME = "TAS_BACKEND_CONFIG_PATH";

@Service()
export class ConfigReader {
    getConfigSync(sources: string[]): Config {
        if (!Array.isArray(sources) || sources.length === 0) {
            throw new Error("No sources defined.");
        }

        const config = new Config();
        for (const source of sources) {
            switch (source) {
                case configConsts.CONFIG_SOURCE_LOCAL_BASE_JS:
                    config.setSource(this.getLocalBaseConfig());
                    break;

                case configConsts.CONFIG_SOURCE_FALLBACK:
                    config.setSource(this.getFallbackConfig());
                    break;
                case configConsts.CONFIG_SOURCE_LOCAL_JS:
                    config.setSource(this.getLocalConfig());
                    break;
                case configConsts.CONFIG_SOURCE_ENV:
                    config.setSource(this.getEnvConfig());
                    config.setSource(
                        this.getCustomFileConfig(
                            process.env[CUSTOM_CONFIG_ENV_NAME],
                        ),
                    );
                    break;
                default:
                    throw new Error(
                        `Unknown config source '${JSON.stringify(
                            source,
                        ).substring(0, 128)}'`,
                    );
            }
        }
        return config;
    }

    async getConfig(
        sources = [
            configConsts.CONFIG_SOURCE_ENV,
            configConsts.CONFIG_SOURCE_LOCAL_JS,
            configConsts.CONFIG_SOURCE_DB,
            configConsts.CONFIG_SOURCE_FALLBACK,
            configConsts.CONFIG_SOURCE_LOCAL_BASE_JS,
        ],
        connection?: Knex | undefined,
    ): Promise<Config> {
        if (!Array.isArray(sources) || sources.length === 0) {
            throw new Error("No sources defined.");
        }

        const config = new Config();
        for (const source of sources) {
            switch (source) {
                case configConsts.CONFIG_SOURCE_LOCAL_BASE_JS:
                    config.setSource(this.getLocalBaseConfig());
                    break;
                case configConsts.CONFIG_SOURCE_FALLBACK:
                    config.setSource(this.getFallbackConfig());
                    break;
                case configConsts.CONFIG_SOURCE_DB:
                    config.setSource(await this.getDbConfig(connection));
                    break;
                case configConsts.CONFIG_SOURCE_LOCAL_JS:
                    config.setSource(this.getLocalConfig());
                    break;
                case configConsts.CONFIG_SOURCE_ENV:
                    config.setSource(this.getEnvConfig());
                    break;
                default:
                    throw new Error(
                        `Unknown config source '${JSON.stringify(
                            source,
                        ).substring(0, 128)}'`,
                    );
            }
        }

        return config;
    }

    getLocalBaseConfig(): typeof localBase {
        return _.cloneDeep<typeof localBase>(localBase);
    }

    getFallbackConfig() {
        const fallbackData = [];
        for (const newConf of configVars) {
            const entConf = new ConfigEntity();
            entConf.fill(newConf, false);
            fallbackData.push(entConf);
        }

        return fallbackData.filter(
            (item) =>
                (item._raw.VAR_VALUE !== null &&
                    item._raw.VAR_VALUE !== undefined &&
                    item._raw.VAR_VALUE !== "") ||
                (item._raw.VAR_DEFAULT !== null &&
                    item._raw.VAR_DEFAULT !== ""),
        );
    }

    async getDbConfig(
        connection: Knex | undefined,
    ): Promise<ConfigEntity[] | false> {
        const exists = await connection?.schema.hasTable("CONFIG");
        if (!exists) {
            return false;
        }

        const data = await globalThis.orm
            .repo("config")
            .getAllVars()
            .collectAll();

        return data.filter(
            (item) =>
                (item._raw.VAR_VALUE !== null &&
                    item._raw.VAR_VALUE !== undefined &&
                    item._raw.VAR_VALUE !== "") ||
                (item._raw.VAR_DEFAULT !== null &&
                    item._raw.VAR_DEFAULT !== ""),
        );
    }

    getLocalConfig(): Record<string, any> {
        const filePath = path.resolve("../config/config/local.js");
        const filePathTs = path.resolve("../config/config/local.ts");

        if (
            !fs.existsSync(filePath) &&
            !fs.existsSync(filePathTs) &&
            !process.env[CUSTOM_CONFIG_ENV_NAME]
        ) {
            // biome-ignore lint/suspicious/noConsole: <explanation>
            console.error(`Config 'local.js' not found.`);
            return {};
        }

        return require("../../../config/config/local");
    }

    getCustomFileConfig(file: string | undefined): Record<string, any> {
        if (!file) {
            return [];
        }
        const filePath = path.resolve(file);
        if (!fs.existsSync(filePath)) {
            // biome-ignore lint/suspicious/noConsole: <explanation>
            console.error(`Config ${file} not found.`);
            return {};
        }
        return require(filePath);
    }

    getEnvConfig(): Record<string, any> {
        require("dotenv").config();
        const envs = Object.keys(process.env);
        const tasEnvConfig = [];
        for (const envName of envs) {
            if (
                envName.startsWith("tas.") &&
                envName !== CUSTOM_CONFIG_ENV_NAME
            ) {
                tasEnvConfig.push({
                    VAR_NAME: envName.substring(4),
                    VAR_DEFAULT: process.env[envName],
                });
            }
        }

        return tasEnvConfig;
    }
}
