// @ts-nocheck
// @ts-nocheck
import path from "path";
import fs from "fs";
import { UtilsService } from "../services/UtilsService";
import * as xmlProcessImportConsts from "../../utils/logger/xmlProcessImportConsts";

export class XmlProcessImportCronUtil {
    async listAllProcessedCronFiles() {
        const crons = await this.getEnabledCrons();

        const scheduled = [];
        for (const cron of crons) {
            const filesWithMeta = await this.getProcessedXmlCronFiles(cron);
            scheduled.push(...filesWithMeta);
        }
        return scheduled;
    }

    async listAllEnabledCronFiles() {
        const crons = await this.getEnabledCrons();

        const scheduled = [];
        for (const cron of crons) {
            const filesWithMeta = await this.getXmlCronFiles(cron);
            scheduled.push(...filesWithMeta);
        }

        return scheduled;
    }

    async findDmsFile(cron, filePath) {
        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];
        return xmlImport.findDmsFile(filePath);
    }

    async findDmsFileForProcessedImport(cron, filePath) {
        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];

        return await xmlImport.findProcessedDmsFile(filePath);
    }

    async getXmlCronFiles(cron) {
        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];
        const files = xmlImport.listXmlFiles();

        const fileInfo = [];

        for (const filePath of files) {
            const size = await fs.promises.stat(filePath).size;
            const info = {
                xml_file_path: path.normalize(filePath),
                dms_file_path: path.normalize(filePath),
                xml_file_name: path.basename(filePath),
                xml_file_size: size,
                dms_file_size: 0,
                cron_id: cron.CRON_ID,
            };
            try {
                const stats = await fs.promises.stat(filePath);
                info.mtime = stats.mtime;
                info.ctime = stats.ctime;
            } catch (err) {
                globalThis.tasLogger.error("Can't get file stats.", {
                    err,
                });
            }
            fileInfo.push(info);
        }
        return fileInfo;
    }

    async getProcessedXmlCronFiles(cron) {
        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];

        if (!xmlImport.config.copyXmlDestination) {
            return [];
        }
        const allFiles = UtilsService.walk(xmlImport.config.copyXmlDestination);
        const files = allFiles.filter(
            (file: any) =>
                xmlImport.config.processAllFiles ||
                file
                    .toLowerCase()
                    .endsWith(xmlImport.config.processFileExt || ".xml"),
        );

        const fileInfo = [];

        for (const filePath of files) {
            const size = await fs.promises.stat(filePath).size;
            const info = {
                xml_file_path: path.normalize(filePath),
                xml_file_name: path.basename(filePath),
                xml_file_size: size,
                cron_id: cron.CRON_ID,
            };
            try {
                const stats = await fs.promises.stat(filePath);
                info.mtime = stats.mtime;
                info.ctime = stats.ctime;
            } catch (err) {
                globalThis.tasLogger.error("Can't get file stats.", {
                    err,
                });
            }
            fileInfo.push(info);
        }
        return fileInfo;
    }

    async getEnabledCrons() {
        const cronsCollection = await globalThis.orm.repo("cron").getAllJobs();
        cronsCollection.knex
            .where("CRON_FILE", "XmlProcessImport")
            .where("CRON_STATUS", "A");
        return await cronsCollection.collectAll();
    }

    async skip(cron, xmlPath) {
        globalThis.tasLogger.info(`❗ Manually skipping file ${xmlPath}.`, {
            xmlPath,
            cron_id: cron.CRON_ID,
        });

        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];
        await xmlImport.init();

        await xmlImport.skip(xmlPath);
    }

    async process(cron, xmlPath) {
        globalThis.tasLogger.info(`❗ Manually processing file ${xmlPath}.`, {
            xmlPath,
            cron_id: cron.CRON_ID,
        });

        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];
        await xmlImport.init();

        await globalThis.orm
            .repo("XmlProcessImport")
            .setStatusByPath(
                path.resolve(xmlPath),
                xmlProcessImportConsts.STATUS_QUEUED,
            );
        return xmlImport.processFiles([xmlPath]);
    }

    async reimport(cron, xmlPath) {
        globalThis.tasLogger.info(`❗ Manually reimporting file ${xmlPath}.`, {
            xmlPath,
            cron_id: cron.CRON_ID,
        });

        const xmlImport = globalThis.container.cron.xMLProcessImport();
        const params = JSON.parse(cron.CRON_PARAMETERS);
        xmlImport.config = params.items[0];
        await xmlImport.init();

        await globalThis.orm
            .repo("XmlProcessImport")
            .setStatusByPath(
                path.resolve(xmlPath),
                xmlProcessImportConsts.STATUS_QUEUED,
            );

        // Copy xml to import folder.
        const reImportedFileName = path.resolve(
            `${xmlImport.config.xmlPath}${path.sep}${path.basename(xmlPath)}`,
        );
        globalThis.tasLogger.info(
            `Copying ${xmlPath} to ${reImportedFileName} to be reimported.`,
            {
                xmlPath,
                cron_id: cron.CRON_ID,
            },
        );
        await UtilsService.copyFile(xmlPath, reImportedFileName);
        globalThis.tasLogger.info(
            `Copying ${xmlPath} to ${reImportedFileName} finished.`,
            {
                xmlPath,
                cron_id: cron.CRON_ID,
            },
        );

        // Copy dms to import folder.
        const dmsFile = await this.findDmsFileForProcessedImport(cron, xmlPath);
        if (dmsFile && fs.existsSync(dmsFile)) {
            const reImportedDmsFileName = await this.findDmsFile(
                cron,
                reImportedFileName,
            );
            globalThis.tasLogger.info(
                `Copying dms file ${dmsFile} to ${reImportedDmsFileName}.`,
                {
                    xmlPath,
                    cron_id: cron.CRON_ID,
                },
            );
            await UtilsService.copyFile(dmsFile, reImportedDmsFileName);
            globalThis.tasLogger.info(
                `Copying dms file ${dmsFile} to ${reImportedDmsFileName} finished.`,
                {
                    xmlPath,
                    cron_id: cron.CRON_ID,
                },
            );
        } else {
            globalThis.tasLogger.warning(
                `Copying dms file failed. File not found.`,
                {
                    dmsFile,
                    cron_id: cron.CRON_ID,
                },
            );
        }
        await globalThis.orm
            .repo("XmlProcessImport")
            .setStatusByPath(
                path.resolve(reImportedFileName),
                xmlProcessImportConsts.STATUS_QUEUED,
            );

        return xmlImport.processFiles([xmlPath]);
    }
}
