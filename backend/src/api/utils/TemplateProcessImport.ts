import _ from "lodash";
import { UtilsService } from "../services/UtilsService";
import * as ROLE from "../orm/entity/const/roleConst";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { AsyncAwaitTranspiler } from "../../infrastructure/calculation/utils/AsyncAwaitTranspiler";
import { Knex } from "@mikro-orm/knex";
import { TemplateTaskLink } from "../orm/entity/TemplateTaskLink";
import { TemplateGraph } from "../orm/entity/TemplateGraph";
export class TemplateProcessImport {
    private readonly connection: Knex;

    private version: number | null = null;

    private map: Record<string, any> = {};

    private remap: Record<string, any> = {};

    private losts: Record<string, any> = {};

    private oldExport: boolean = false;

    constructor(connection: Knex) {
        this.connection = connection;

        // Map entity will contain id relations between imported entity and new/compatible etity id.
        // MOST IMPORTANT part of import !!
        this.map = {
            users: {},
            user_names: {},
            roles: {},
            orgstrs: {},
            dynamic_tables: {},
            dynamic_table_names: {},
            template_process: {},
            template_variables: {},
            template_tasks: {},
            links: {},
            conds: {},
            tvar_names: {},
            event_definition: {},
            rule_definition: {},
            event_definition_names: {},
            conditions: {},
            calculations_scripts: {},
            calculations_scripts_names: {},
            template_sections: {},
        };

        // Map with later updates. e.g ttask_event. Can not update because TTASK can point to EVENT and EVENT can point to TTASK.
        // Both must be imported before TTASK_EVENT is mapped
        this.remap = {
            ttask_event: [],
            ttask_assesment: {},
            email_enot_ttask_id: {},
        };

        // Map with ignored/losts attributes. Cannot be exported or is infinite recursive.
        this.losts = {
            email_notifs: [], // Lost variable names
        };
    }

    addTprocMap(idStr: string, newId: number[] | string[]) {
        if (typeof idStr !== "string") {
            throw new Error(
                `Invalid params in addTprocMap (1) idStr=${idStr}, newId=${newId}`,
            );
        }
        const buf = idStr.split(",");
        if (buf.length !== 2) {
            throw new Error(
                `Invalid params in addTprocMap (2) idStr=${idStr}, newId=${newId}`,
            );
        }

        // Importing non versioning export.
        if (this.oldExport && (buf[1] === "undefined" || !buf[1])) {
            buf[1] = "1";
        }

        if (
            buf.length !== 2 ||
            !UtilsService.isNumericString(buf[0]) ||
            !UtilsService.isNumericString(buf[1])
        ) {
            throw new Error(
                `Invalid params in addTprocMap (2) idStr=${idStr}, newId=${newId}`,
            );
        }
        if (
            newId.length !== 2 ||
            !UtilsService.isNumericString(newId[0]) ||
            !UtilsService.isNumericString(newId[1])
        ) {
            throw new Error(
                `Invalid params in addTprocMap (3) idStr=${idStr}, newId=${newId}`,
            );
        }

        this.map.template_process[`${buf[0]},${buf[1]}`] = newId;
    }

    // *********************************************************************************************************************
    // ************************************************ UTILS **************************************************************
    // *********************************************************************************************************************

    remapUserIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            if (entity[attr] === 1) {
                // Skip ADMIN remap.
                return entity[attr];
            }

            const newUserId = this.map.users[entity[attr]];
            if (!newUserId) {
                throw new UserException(
                    `Can not remap ${attr}. User ${entity[attr]} not found in user map.`,
                );
            }
            return newUserId;
        }
        return entity[attr];
    }

    remapDynamicTableIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newDTId = this.map.dynamic_tables[entity[attr]];
            if (!newDTId) {
                throw new UserException(
                    `Can not remap ${attr}. User ${entity[attr]} not found in dynamic_tables map.`,
                );
            }
            return newDTId;
        }
        return entity[attr];
    }

    remapCalcScriptIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const attrValue = JSON.parse(entity[attr]);
            if (Array.isArray(attrValue)) {
                const newScriptIdsArray: number[] = [];
                attrValue.forEach((id) => {
                    const newScriptId = this.map.calculations_scripts[id];
                    if (!newScriptId) {
                        // TODO => script is missing in export (4.x => 4.5) offer compatibility
                        globalThis.tasLogger.warning(
                            `Can not remap '${attr}'. Script '${id}' not found in calculations_scripts map.`,
                        );
                    } else {
                        newScriptIdsArray.push(newScriptId);
                    }
                });
                return JSON.stringify(newScriptIdsArray);
            }
        }
        return entity[attr];
    }

    remapOrgstrAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            if (entity[attr] === 1) {
                // Do not remap root organization.
                return entity[attr];
            }

            const newOrgstrId = this.map.orgstrs[entity[attr]];
            if (!newOrgstrId) {
                throw new UserException(
                    `Can not remap ${attr}. Orgstr ${entity[attr]} not found in orgstrs map.`,
                );
            }
            return newOrgstrId;
        }
        return entity[attr];
    }

    remapRoleAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            if (entity[attr] < 0) {
                // Skip System Roles.
                return entity[attr];
            }

            const newRoleId = this.map.roles[entity[attr]];
            if (!newRoleId) {
                throw new UserException(
                    `Can not remap ${attr}. Role ${entity[attr]} not found in roles map.`,
                );
            }
            return newRoleId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created tprocId
     * @param {object} entity
     * @param tprocIdAttr
     * @param tprocVersionAttr
     * @return {number}
     */
    remapTprocIdAttr(
        entity: Record<string, any>,
        tprocIdAttr: string,
        tprocVersionAttr: string = "",
    ): number | any[] | null {
        if (this.oldExport && !entity[tprocVersionAttr]) {
            return [
                this.remapTprocIdAttrWithAnyVersion(entity, tprocIdAttr),
                1,
            ];
        }

        // Check
        if (!entity[tprocVersionAttr]) {
            throw new UserException(
                "There is no valid tproc_version in entity",
                "INVALID_TPROC_VERSION",
                {
                    entity,
                    tprocVersionAttr,
                },
            );
        }

        if (
            entity[tprocIdAttr] !== null &&
            typeof entity[tprocIdAttr] !== "undefined" &&
            entity[tprocVersionAttr] !== null &&
            typeof entity[tprocVersionAttr] !== "undefined"
        ) {
            if (entity[tprocIdAttr] === -1) {
                // TEMPLATE_PROCESS can be -1. It means no selected process. Just like illegal index. Do not remap !
                return [-1, entity[tprocVersionAttr]];
            }

            const newTprocId: any =
                this.map.template_process[
                    `${entity[tprocIdAttr]},${entity[tprocVersionAttr]}`
                ];
            if (!newTprocId) {
                throw new UserException(
                    `Can not remap TPROC_ID & TPROC_VERSION ${entity[tprocIdAttr]},${entity[tprocVersionAttr]} not found in export.`,
                );
            }
            return newTprocId;
        }
        return null;
    }

    /**
     * Remap entity attribute to new imported compatible/created tprocId
     * @param {object} entity
     * @param tprocIdAttr
     * @param tprocVersionAttr
     * @return {number}
     */
    remapTprocIdAttrWithAnyVersion(
        entity: Record<string, any>,
        attr: string,
    ): number {
        for (let version = 1; version < 20; version += 1) {
            try {
                entity.TPROC_VERSION = version;
                const id: any = this.remapTprocIdAttr(
                    entity,
                    attr,
                    "TPROC_VERSION",
                );
                return id[0];
            } catch (_e) {
                // Version not found ?
            }
        }
        throw new UserException(`Can not remap TPROC_ID ${entity[attr]}`);
    }

    /**
     * Remap entity attribute to new imported compatible/created dt_name
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapDynTableNameAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            if (entity[attr] === -1) {
                // TEMPLATE_PROCESS can be -1. It means no selected process. Just like illegal index. Do not remap !
                return entity[attr];
            }

            const newDTName = this.map.dynamic_table_names[entity[attr]];
            if (!newDTName) {
                throw new UserException(
                    `Can not remap ${attr}. Dynamic Table name ${entity[attr]} not found in template_process map.`,
                );
            }
            return newDTName;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created tvar
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapTvarIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            if (entity[attr] < 0) {
                return entity[attr]; // < 0 TVAR_ID is special variables like ttask_name, tproc_name etc.
            }

            const newTVarId = this.map.template_variables[entity[attr]];
            if (!newTVarId) {
                throw new UserException(
                    `Can not remap ${attr}. TVAR_ID ${entity[attr]} not found in template_variables map.`,
                );
            }
            return newTVarId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created ttask
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapTTaskIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newTTaskId = this.map.template_tasks[entity[attr]];
            if (!newTTaskId) {
                throw new UserException(
                    `Can not remap ${attr}. TTASK_ID ${entity[attr]} not found in template_tasks map.`,
                );
            }
            return newTTaskId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created section
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapTSecIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newSectionId = this.map.template_sections[entity[attr]];
            if (!newSectionId) {
                throw new UserException(
                    `Can not remap ${attr}. TSEC_ID ${entity[attr]} not found in template_sections map.`,
                );
            }
            return newSectionId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created tlink
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapTLinkIdAttr(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newTLinkId = this.map.links[entity[attr]];
            if (!newTLinkId) {
                throw new UserException(
                    `Can not remap ${attr}. Link_ID ${entity[attr]} not found in template_tasks map.`,
                );
            }
            return newTLinkId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created event
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapEveDefId(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newEvedefId = this.map.event_definition[entity[attr]];
            if (!newEvedefId) {
                throw new UserException(
                    `Can not remap ${attr}. EVEDEF_ID ${entity[attr]} not found in event_definition map.`,
                );
            }
            return newEvedefId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created rule
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapRuleDefId(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newRdefId = this.map.rule_definition[entity[attr]];
            if (!newRdefId) {
                throw new UserException(
                    `Can not remap ${attr}. RDEF_ID ${entity[attr]} not found in rule_definition map.`,
                );
            }
            return newRdefId;
        }
        return entity[attr];
    }

    /**
     * Remap entity attribute to new imported compatible/created event
     * @param {object} entity
     * @param {string} attr
     * @return {number}
     */
    remapEveDefname(entity: Record<string, any>, attr: string) {
        if (entity[attr] !== null && typeof entity[attr] !== "undefined") {
            const newEvedefName = this.map.event_definition_names[entity[attr]];
            if (!newEvedefName) {
                throw new UserException(
                    `Can not remap ${attr}. EVEDEF_NAME ${entity[attr]} not found in event_definition_names map.`,
                );
            }
            return newEvedefName;
        }
        return entity[attr];
    }

    remapEventSubpSubrTTaskId(subpSubr: string) {
        if (!subpSubr || !subpSubr.match) {
            // Is string and has match method
            return subpSubr;
        }

        const m = subpSubr.match(/(\$SUB[P|R]_)([0-9]*)/);
        if (m) {
            // match
            const mapType = m[1]; // $SUBP_ or $SUBR_
            const oldTTaskId = m[2];
            const newTTaskId = this.remapTTaskIdAttr(
                { TTASK_ID: oldTTaskId },
                "TTASK_ID",
            );
            subpSubr = `${mapType}${newTTaskId}`;
        }
        return subpSubr;
    }

    remapEventWait(evew: string) {
        if (!evew || !evew.match) {
            // Is string and has match method
            return evew;
        }

        const m = evew.match(/(\$EVEW_)([0-9]*)/);
        if (m) {
            // match
            const mapType = m[1]; // $EVEW_
            const oldTTaskId = m[2];
            const newTTaskId = this.remapTTaskIdAttr(
                { TTASK_ID: oldTTaskId },
                "TTASK_ID",
            );
            evew = `${mapType}${newTTaskId}`;
        }
        return evew;
    }

    // *********************************************************************************************************************
    // *************************************************  ROLES  ***********************************************************
    // *********************************************************************************************************************

    /**
     * Create new role entity.
     * @param roleData
     */
    async createRole(roleData: Record<string, any>) {
        const roleRepo = globalThis.orm.repo("role", this.connection);
        const newRole = roleRepo.getEntity();

        const attrs = newRole.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === newRole.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = roleData.import_rules.data[attr]
                ? roleData.import_rules.data[attr]
                : roleData[attr];
            if (typeof attrValue !== "undefined") {
                newRole[upperAttr] = attrValue;
            }
        });
        const id = await roleRepo.store(newRole);
        this.map.roles[
            roleData[(newRole.primaryColumn as string).toLowerCase()]
        ] = id;
    }

    /**
     * Rewrite existing roleData.
     * @param roleData
     */
    async rewriteRole(roleData: Record<string, any>) {
        if (!roleData.import_rules.data.role_id) {
            throw new UserException(
                "Role id not defined in REWRITE role import.",
                "INVALID_IMPORT_STATE",
            );
        }

        const roleRepo = globalThis.orm.repo("role", this.connection);
        // Find roleData and rewrite its entity
        const rewritedRole = await roleRepo.get(
            roleData.import_rules.data.role_id,
        );
        const attrs = rewritedRole.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === rewritedRole.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = roleData.import_rules.data[attr]
                ? roleData.import_rules.data[attr]
                : roleData[attr];
            if (typeof attrValue !== "undefined") {
                rewritedRole[upperAttr] = attrValue;
            }
        });
        await roleRepo.store(rewritedRole);

        this.map.roles[roleData.role_id] = roleData.import_rules.data.role_id;
    }

    /**
     * Use compatible user entity.
     * @param roleData
     */
    useCompatibleRole(roleData: Record<string, any>) {
        if (!roleData.import_rules.data.role_id) {
            throw new UserException(
                "ROLE_ID not defined in USE_COMPATIBLE ROLES import.",
                "INVALID_IMPORT_STATE",
            );
        }

        this.map.roles[roleData.role_id] = roleData.import_rules.data.role_id;
        return;
    }

    /**
     * Import role
     * @param role
     */
    async importRole(role: Record<string, any>) {
        if (!role || !role.import_rules || !role.import_rules.action) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = role.import_rules;

        if (importRule.action === "CREATE") {
            return await this.createRole(role);
        }
        if (importRule.action === "USE_COMPATIBLE") {
            return this.useCompatibleRole(role);
        }
        if (importRule.action === "REWRITE") {
            return await this.rewriteRole(role);
        }

        throw new UserException(
            `Unknown import action ${role.import_rules.action}.`,
        );
    }

    async importRoles(roles: Record<string, any>) {
        if (!roles || !Array.isArray(roles)) {
            return;
        }
        for (const role of roles) {
            await this.importRole(role);
        }
    }

    // *********************************************************************************************************************
    // *************************************************  USERS  ***********************************************************
    // *********************************************************************************************************************

    /**
     * Create new user entity.
     * @param userData
     */
    /**
     * Create new user entity.
     * @param userData
     */
    async createUser(userData: Record<string, any>) {
        const userRepo = globalThis.orm.repo("user", this.connection);
        const newUser = userRepo.getEntity();

        const attrs = newUser.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === newUser.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = userData.import_rules.data[attr]
                ? userData.import_rules.data[attr]
                : userData[attr];
            if (typeof attrValue !== "undefined") {
                newUser[upperAttr] = attrValue;
            }
        });
        const id = await userRepo.store(newUser);
        this.map.users[
            userData[(newUser.primaryColumn as string).toLowerCase()]
        ] = id;
        this.map.user_names[userData.user_name] = newUser.USER_NAME;
        newUser.USER_ID = id;
        await userRepo.assignRoles(
            newUser.USER_ID,
            [-8],
            null,
            null,
            ROLE.SOURCE_API,
        );
        return newUser;
    }

    /**
     * Use compatible user entity.
     * @param userData
     */
    useCompatibleUser(userData: Record<string, any>) {
        if (!userData.import_rules.data.user_id) {
            throw new UserException(
                "User id not defined in USE_COMPATIBLE user import.",
                "INVALID_IMPORT_STATE",
            );
        }

        this.map.users[userData.user_id] = userData.import_rules.data.user_id;
        this.map.user_names[userData.user_name] = userData.user_name;
    }

    /**
     * Rewrite existing userData.
     * @param userData
     */
    async rewriteUser(userData: Record<string, any>) {
        if (!userData.import_rules.data.user_id) {
            throw new UserException(
                "User id not defined in REWRITE userData import.",
                "INVALID_IMPORT_STATE",
            );
        }

        const userRepo = globalThis.orm.repo("user", this.connection);
        // Find userData and rewrite its entity
        let oldName = null;
        const rewritedUser = await userRepo.get(
            userData.import_rules.data.user_id,
        );
        oldName = rewritedUser.USER_NAME;
        const attrs = rewritedUser.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === rewritedUser.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = userData.import_rules.data[attr]
                ? userData.import_rules.data[attr]
                : userData[attr];
            if (typeof attrValue !== "undefined") {
                rewritedUser[upperAttr] = attrValue;
            }
        });

        this.map.user_names[oldName] = rewritedUser.USER_NAME;
        await userRepo.store(rewritedUser);
        this.map.users[userData.user_id] = userData.import_rules.data.user_id;
    }

    /**
     * Import user
     * @param user
     */
    async importUser(user: Record<string, any>) {
        if (!user || !user.import_rules || !user.import_rules.action) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = user.import_rules;

        if (importRule.action === "CREATE") {
            return await this.createUser(user);
        }
        if (importRule.action === "USE_COMPATIBLE") {
            return this.useCompatibleUser(user);
        }
        if (importRule.action === "REWRITE") {
            return await this.rewriteUser(user);
        }
        throw new UserException(
            `Unknown import action ${user.import_rules.action}.`,
        );
    }

    async importUsers(users: Record<string, any>) {
        if (!users || !Array.isArray(users)) {
            return;
        }

        for (const user of users) {
            await this.importUser(user);
        }
    }

    // *********************************************************************************************************************
    // *****************************************  ORGANIZATION_STRUCTURE  **************************************************
    // *********************************************************************************************************************

    async createOrgstr(orgstrData: Record<string, any>) {
        if (
            !orgstrData.import_rules ||
            typeof orgstrData.import_rules.data.manager_user_id === "undefined"
        ) {
            throw new UserException(
                "You must define MANAGER_USER_ID in import rules due to possible loss of this user attribute. MANAGER_USER_ID can be null but not undefined.",
                "INVALID_IMPORT_STATE",
            );
        }
        orgstrData.manager_user_id = orgstrData.import_rules.manager_user_id; // Must be overriden. MANAGER_USER_ID user is not in export.

        const orgstrRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        const newOrgstr = orgstrRepo.getEntity();

        const attrs = newOrgstr.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === newOrgstr.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue =
                orgstrData.import_rules.data[attr] !== null &&
                typeof orgstrData.import_rules.data[attr] !== "undefined"
                    ? orgstrData.import_rules.data[attr]
                    : orgstrData[attr];
            if (typeof attrValue !== "undefined") {
                newOrgstr[upperAttr] = attrValue;
            }
        });
        const id = await orgstrRepo.store(newOrgstr);
        this.map.orgstrs[
            orgstrData[(newOrgstr.primaryColumn as string).toLowerCase()]
        ] = id;
    }

    /**
     * Use compatible user entity.
     * @param data
     */
    useCompatibleOrgstr(data: Record<string, any>) {
        if (
            !data.import_rules.data.orgstr_id &&
            data.import_rules.data.orgstr_id !== 0
        ) {
            throw new UserException(
                "Orgstr id not defined in USE_COMPATIBLE organization import.",
                "INVALID_IMPORT_STATE",
            );
        }

        this.map.orgstrs[data.orgstr_id] = data.import_rules.data.orgstr_id;
    }

    /**
     * Rewrite existing orgstrData.
     * @param orgstrData
     */
    async rewriteOrgstr(orgstrData: Record<string, any>) {
        if (!orgstrData.import_rules.data.orgstr_id) {
            throw new UserException(
                "Orgstr id not defined in REWRITE userData import.",
                "INVALID_IMPORT_STATE",
            );
        }

        if (
            !orgstrData.import_rules ||
            typeof orgstrData.import_rules.data.manager_user_id === "undefined"
        ) {
            throw new UserException(
                "You must define MANAGER_USER_ID in import rules due to possible loss of this user attribute. MANAGER_USER_ID can be null but not undefined.",
                "INVALID_IMPORT_STATE",
            );
        }
        orgstrData.manager_user_id = orgstrData.import_rules.manager_user_id; // Must be overriden. MANAGER_USER_ID user is not in export.

        const orgstrRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        // Find orgstrData and rewrite its entity
        const rewritedOrgstr = await orgstrRepo.get(
            orgstrData.import_rules.data.orgstr_id,
        );
        const attrs = rewritedOrgstr.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === rewritedOrgstr.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue =
                orgstrData.import_rules.data[attr] !== null &&
                typeof orgstrData.import_rules.data[attr] !== "undefined"
                    ? orgstrData.import_rules.data[attr]
                    : orgstrData[attr];
            if (typeof attrValue !== "undefined") {
                rewritedOrgstr[upperAttr] = attrValue;
            }
        });
        await orgstrRepo.store(rewritedOrgstr);
        this.map.orgstrs[orgstrData.orgstr_id] =
            orgstrData.import_rules.data.orgstr_id;
    }

    async importOrgstr(orgstr: Record<string, any>) {
        if (!orgstr || !orgstr.import_rules || !orgstr.import_rules.action) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = orgstr.import_rules;

        if (importRule.action === "CREATE") {
            return await this.createOrgstr(orgstr);
        }
        if (importRule.action === "USE_COMPATIBLE") {
            return this.useCompatibleOrgstr(orgstr);
        }
        if (importRule.action === "REWRITE") {
            return await this.rewriteOrgstr(orgstr);
        }
        throw new UserException(
            `Unknown import action ${orgstr.import_rules.action}.`,
        );
    }

    async importOrgstrs(orgstrs: Record<string, any>) {
        if (!orgstrs || !Array.isArray(orgstrs)) {
            return;
        }

        for (const orgstr of orgstrs) {
            await this.importOrgstr(orgstr);
        }
    }

    // *********************************************************************************************************************
    // ********************************************  DYNAMIC_TABLE  ********************************************************
    // *********************************************************************************************************************

    async createDynamicTable(dt: Record<string, any>) {
        if (
            !dt.import_rules ||
            !dt.import_rules.data ||
            !dt.import_rules.data.dt_name
        ) {
            throw new UserException(
                "DT_NAME not defined in CREATE dynamic table import.",
                "INVALID_IMPORT_STATE",
            );
        }

        // Create DT
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const newDtId = await dtRepo.create(
            dt.import_rules.data.dt_name,
            dt.dll_public,
            null,
            null,
        );

        this.map.dynamic_tables[dt.dll_id] = newDtId;
        this.map.dynamic_table_names[dt.dll_name] =
            dt.import_rules.data.dt_name;

        // Create DT cols.
        const dlcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        for (const col of dt.dll_cols) {
            await dlcRepo.createCol({
                DT_ID: newDtId,
                DTC_ID: col.dlc_id,
                DTC_NAME: col.dlc_name,
            });
        }

        const dlvRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );

        const insertedMap = new Map();
        for (let i = 0; i < dt.dll_values.length; i += 1) {
            const row = dt.dll_values[i];

            const map = {
                dlv_: "dtv_",
            };

            const dtvRow: Record<string, any> = UtilsService.toUpperCase(
                UtilsService.remapAttrs(row, map),
            );
            const key = `${newDtId}.${dtvRow.DTV_INDEX}`;
            if (insertedMap.has(key)) {
                globalThis.tasLogger.warning(
                    `Template import ignoring same index for ${dt.import_rules.data.dt_name}`,
                    {
                        ignored: dtvRow,
                        existing: insertedMap.get(key),
                    },
                );
            } else {
                delete dtvRow.DTV_VALUE;
                await dlvRepo.addRow(newDtId, dtvRow);
                insertedMap.set(key, dtvRow);
            }
        }
    }

    /**
     * Use compatible user entity.
     * @param dt
     */
    async useCompatibleDynamicTable(dt: Record<string, any>) {
        if (!dt.import_rules.data.dt_id) {
            throw new UserException(
                "DLL_ID not defined in USE_COMPATIBLE dynamic table import.",
                "INVALID_IMPORT_STATE",
            );
        }
        const compatibleDT = await globalThis.orm
            .repo("dynamicTable", this.connection)
            .getTable(dt.import_rules.data.dt_id);

        this.map.dynamic_tables[dt.dll_id] = compatibleDT.DT_ID;
        this.map.dynamic_table_names[dt.dll_name] = compatibleDT.DT_NAME;
    }

    /**
     * Import dt
     * @param dt
     */
    async importDynamicTable(dt: Record<string, any>) {
        if (!dt || !dt.import_rules || !dt.import_rules.action) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = dt.import_rules;

        if (importRule.action === "CREATE") {
            return await this.createDynamicTable(dt);
        }
        if (importRule.action === "USE_COMPATIBLE") {
            return await this.useCompatibleDynamicTable(dt);
        }
        if (importRule.action === "REWRITE") {
            throw new UserException(
                "REWRITE is not implemented in DynamicTable import.",
            );
        }
        throw new UserException(
            `Unknown import action ${dt.import_rules.action}.`,
        );
    }

    async importDynamicTables(dts: Record<string, any>) {
        if (!dts || !Array.isArray(dts)) {
            return;
        }
        for (const dt of dts) {
            await this.importDynamicTable(dt);
        }
    }

    // *********************************************************************************************************************
    // ********************************************  GLOBAL CALCULATIONS_SCRIPTS  ******************************************
    // *********************************************************************************************************************

    async createCalculationScript(script: Record<string, any>): Promise<void> {
        if (
            !script.import_rules ||
            !script.import_rules.data ||
            !script.import_rules.data.name
        ) {
            throw new UserException(
                "NAME not defined in CREATE calculations script import.",
                "INVALID_IMPORT_STATE",
            );
        }

        const repo = globalThis.orm.repo("jsScript", this.connection);
        const entity = repo.getEntity();
        const attrs = entity.attributes();
        const attrKeys = Object.keys(attrs);

        // Fill user entity
        attrKeys.forEach((key) => {
            const lk = key.toLowerCase();

            if (typeof script[lk] !== "undefined") {
                entity[key] = script[lk];
            }
            if (lk === "js_es6" && script.js) {
                const transpiler = new AsyncAwaitTranspiler(script.js);
                transpiler.parse();
                entity[key] = transpiler.generateCode();
            }
        });
        entity.JS_ID = null;
        entity.NAME = script.import_rules.data.name;

        this.map.calculations_scripts[script.js_id] = await repo.store(entity);
        this.map.calculations_scripts_names[script.name] =
            script.import_rules.data.name;

        await globalThis.container.service.temporary.cacheModule.renewAdminScripts();
    }

    async useCompatibleCalculationScript(script: Record<string, any>) {
        if (!script.import_rules.data.js_id) {
            throw new UserException(
                "JS_ID not defined in USE_COMPATIBLE global calculation script import.",
                "INVALID_IMPORT_STATE",
            );
        }
        const compatibleScript = await globalThis.orm
            .repo("jsScript", this.connection)
            .get(script.import_rules.data.js_id);

        this.map.calculations_scripts[script.js_id] = compatibleScript.JS_ID;
        this.map.calculations_scripts_names[script.name] =
            compatibleScript.NAME;
    }

    async importCalculationScript(script: Record<string, any>) {
        if (!script || !script.import_rules || !script.import_rules.action) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = script.import_rules;

        if (importRule.action === "CREATE") {
            return await this.createCalculationScript(script);
        }
        if (importRule.action === "USE_COMPATIBLE") {
            return await this.useCompatibleCalculationScript(script);
        }
        if (importRule.action === "REWRITE") {
            throw new UserException(
                "REWRITE is not implemented in CalculationScript import.",
            );
        }
        throw new UserException(
            `Unknown import action ${script.import_rules.action}.`,
        );
    }

    async importCalculationsScripts(scripts: Record<string, any>) {
        if (!scripts || !Array.isArray(scripts)) {
            return;
        }

        for (const script of scripts) {
            await this.importCalculationScript(script);
        }
    }

    // *********************************************************************************************************************
    // ******************************************* TEMPLATE_PRINTS *********************************************************
    // *********************************************************************************************************************

    useCompatiblePrint() {
        // Do nothing, template process already exists with all varaibles.
        return;
    }

    async createPrint(print: Record<string, any>) {
        const tprintRepo = globalThis.orm.repo(
            "templatePrint",
            this.connection,
        );
        const entity = tprintRepo.getEntity();
        const attrs = entity.getColumnNames();
        attrs.forEach((attrName) => {
            const lowerAttr = attrName.toLowerCase();
            if (attrName === entity.primaryColumn) {
                return;
            }

            entity[attrName] = print[lowerAttr];
        });

        // Remap template process
        entity.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            print,
            "tproc_id",
        );

        return await tprintRepo.store(entity, false);
    }

    async rewritePrint(print: Record<string, any>) {
        const newTprocId: any = this.remapTprocIdAttr(print, "tproc_id");

        const tprintRepo = globalThis.orm.repo(
            "templatePrint",
            this.connection,
        );
        // Get tvar by name or create new one !!
        const existingPrint = await tprintRepo.getByNameForProcess(
            newTprocId[0],
            print.prnt_name,
        );
        if (existingPrint === null) {
            // Not found, create new !
            return await this.createPrint(print);
        }
        const updatedPrint = await tprintRepo.get(existingPrint.PRNT_ID);
        const attrs = updatedPrint.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === updatedPrint.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = print[lowerAttr];
            if (typeof attrValue !== "undefined") {
                updatedPrint[upperAttr] = attrValue;
            }
        });

        updatedPrint.TPROC_ID = newTprocId[0];
        return await tprintRepo.store(updatedPrint, false);
    }

    async useCompatiblePrints(prints: Record<string, any>) {
        if (!Array.isArray(prints) || prints.length === 0) {
            return;
        }
    }

    async createPrints(prints: Record<string, any>) {
        if (!Array.isArray(prints) || prints.length === 0) {
            return;
        }

        for (const print of prints) {
            await this.createPrint(print);
        }
    }

    async rewritePrints(prints: Record<string, any>) {
        if (!Array.isArray(prints) || prints.length === 0) {
            return;
        }

        for (const print of prints) {
            await this.rewritePrint(print);
        }
    }

    // *********************************************************************************************************************
    // ***************************************** TEMPLATE_VARIABLES ********************************************************
    // *********************************************************************************************************************

    lovsToEntity(lovs: Record<string, any>) {
        if (!Array.isArray(lovs) || lovs.length === 0) {
            return lovs;
        }

        const lovRepo = globalThis.orm.repo(
            "templateVariableLov",
            this.connection,
        );
        const out: Record<string, any> = [];
        lovs.forEach((lov) => {
            const data: Record<string, any> = {
                ORG_ID: 1,
                TVARLOV_TEXT_VALUE: lov.tvarlov_text_value,
                TVARLOV_NUMBER_VALUE: lov.tvarlov_number_value,
                TVARLOV_DATE_VALUE: lov.tvarlov_date_value,
                TVARLOV_IS_SELECTED: lov.tvarlov_is_selected,
            };

            // t3b-1176 export šablony neobsahuje překlady číselníků
            globalThis.dynamicConfig.langs.forEach((lang: string) => {
                data[`TVARLOV_TEXT_VALUE_${lang.toUpperCase()}`] =
                    lov[`tvarlov_text_value_${lang.toLowerCase()}`];
            });

            out.push(lovRepo.getEntity(data));
        });
        return out;
    }

    useCompatibleVariable(variable: Record<string, any>) {
        // Do nothing, template process already exists with all varaibles.
        this.map.template_variables[variable.tvar_id] = variable.tvar_id;
        return;
    }

    remapTVarMetaDataUrl(tvarMetadata: string) {
        try {
            const tvarMeta = JSON.parse(tvarMetadata);
            if (tvarMeta.tableDefinition) {
                const tableDefinition = JSON.parse(tvarMeta.tableDefinition);
                if (Array.isArray(tableDefinition.columns)) {
                    for (let i = 0; i < tableDefinition.columns.length; i++) {
                        const { dataUrl } = tableDefinition.columns[i];
                        // Data url is /dyn-table/:id/values
                        const m = dataUrl.match(/\/dyn-table\/(\d*)\/values/);
                        if (m) {
                            // TODO rewrite .. m[1]
                            const newDTId = this.remapDynamicTableIdAttr(
                                { DT_ID: m[1] },
                                "DT_ID",
                            );
                            tableDefinition.columns[i].dataUrl =
                                dataUrl.replace(m[1], newDTId);
                        }
                    }
                }
                return JSON.stringify(tvarMeta);
            }
        } catch (_err) {
            //
        }
        return tvarMetadata;
    }

    async createVariable(
        variable: Record<string, any>,
        lovs: Record<string, any>,
    ) {
        const tvarRepo = globalThis.orm.repo(
            "templateVariable",
            this.connection,
        );
        const entity = tvarRepo.getEntity();
        const attrs = entity.getColumnNames();

        attrs.forEach((attrName) => {
            const lowerAttr = attrName.toLowerCase();
            if (attrName === entity.primaryColumn) {
                return;
            }

            entity[attrName] = variable[lowerAttr];
        });
        entity.TVAR_ALIAS = null; //

        // Remap template process
        entity.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            variable,
            "tproc_id",
        );

        // Remap dynamic table name.
        if (entity.TVAR_TYPE === "DT") {
            entity.DLIST_NAME = this.remapDynTableNameAttr(
                variable,
                "dlist_name",
            );
        }

        entity.TVAR_META = this.remapTVarMetaDataUrl(entity.TVAR_META);

        entity._lovs = this.lovsToEntity(lovs);
        const newTvarId = await tvarRepo.store(entity, false);
        this.map.template_variables[variable.tvar_id] = newTvarId;
        return newTvarId;
    }

    async rewriteVariable(
        variable: Record<string, any>,
        lovs: Record<string, any>,
    ) {
        const newTprocId: any = this.remapTprocIdAttr(variable, "tproc_id");

        const tvarRepo = globalThis.orm.repo(
            "templateVariable",
            this.connection,
        );
        // Get tvar by name or create new one !!
        const existingVariable = await tvarRepo.getByName(
            newTprocId[0],
            variable.tvar_name,
        );
        if (existingVariable === null) {
            // Not found, create new !
            return await this.createVariable(variable, lovs);
        }

        // Found, rewrite !
        const updatedVariable = await tvarRepo.get(existingVariable.TVAR_ID);
        const attrs = updatedVariable.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (
                upperAttr === updatedVariable.primaryColumn ||
                upperAttr === "TVAR_ALIAS"
            ) {
                return; // Skip primary key changing.
            }

            // Set attribute
            const attrValue = variable[lowerAttr];
            if (typeof attrValue !== "undefined") {
                updatedVariable[upperAttr] = attrValue;
            }
        });

        updatedVariable.TPROC_ID = newTprocId[0];
        updatedVariable._lovs = this.lovsToEntity(lovs); // Fill lovs.
        const tvarId = await tvarRepo.store(updatedVariable, false);
        this.map.template_variables[variable.tvar_id] = tvarId;
        return tvarId;
    }

    async useCompatibleVariables(variables: Record<string, any>) {
        if (!Array.isArray(variables) || variables.length === 0) {
            return;
        }

        for (const variable of variables) {
            await this.useCompatibleVariable(variable);
        }
    }

    async createVariables(
        variables: Record<string, any>,
        lovs: Record<string, any>,
    ) {
        if (!Array.isArray(variables) || variables.length === 0) {
            return;
        }

        for (const variable of variables) {
            await this.createVariable(variable, lovs[variable.tvar_id]);
        }
    }

    async rewriteVariables(
        variables: Record<string, any>,
        lovs: Record<string, any>,
    ) {
        if (!Array.isArray(variables) || variables.length === 0) {
            return;
        }

        for (const variable of variables) {
            await this.rewriteVariable(variable, lovs[variable.tvar_id]);
        }
    }

    // *********************************************************************************************************************
    // ******************************************** TEMPLATE_TASKS *********************************************************
    // *********************************************************************************************************************

    useCompatibleTask(task: Record<string, any>) {
        // this.map.template_tasks[task.ttask_id] = task.ttask_id;
        if (
            ["W", "P", "E"].indexOf(task.ttask_type) !== -1 &&
            task.ttask_event
        ) {
            // Add later update, just after events are imported !
            this.remap.ttask_event.push({
                ttask_event: task.ttask_event,
                ttask_id: task.ttask_id,
            });
        }
        this.map.template_tasks[task.ttask_id] = task.ttask_id;
        return;
    }

    async createTask(
        task: Record<string, any>,
        tprocData: Record<string, any> = {},
    ) {
        const ttaskRepo = globalThis.orm.repo("templateTask", this.connection);
        const newTTask = ttaskRepo.getEntity();

        const attrs = newTTask.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newTTask.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = task[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newTTask[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newTTask.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            newTTask,
            "TPROC_ID",
        );
        newTTask.TTASK_ASSESMENT_ROLE_ID = this.remapRoleAttr(
            newTTask,
            "TTASK_ASSESMENT_ROLE_ID",
        );
        newTTask.TTASK_ASSESMENT_USER_ID = this.remapUserIdAttr(
            newTTask,
            "TTASK_ASSESMENT_USER_ID",
        );
        newTTask.TTASK_ASSESMENT_ORGSTR_ID = this.remapOrgstrAttr(
            newTTask,
            "TTASK_ASSESMENT_ORGSTR_ID",
        );
        newTTask.TTASK_ASSESMENT_ORGSTR_CNST = this.remapOrgstrAttr(
            newTTask,
            "TTASK_ASSESMENT_ORGSTR_CNST",
        );
        newTTask.TTASK_ASSESMENT_TVAR_ID = this.remapTvarIdAttr(
            newTTask,
            "TTASK_ASSESMENT_TVAR_ID",
        );
        newTTask.TPROC_VERSION = tprocData.tproc_version;

        const id: any = this.remapTprocIdAttr(
            newTTask,
            "TTASK_SUBPROCESS_TPROC_ID",
            "TPROC_VERSION",
        );
        newTTask.TTASK_SUBPROCESS_TPROC_ID = id[0];
        newTTask.TTASK_SUBPROCESS_TPROC_VERSION = id[1];

        // Remap duration and due_offset
        newTTask.TTASK_DUE_OFFSET = this.remapTaskPlanning(
            newTTask.TTASK_DUE_OFFSET,
        );
        newTTask.TTASK_DURATION = this.remapTaskPlanning(
            newTTask.TTASK_DURATION,
        );

        //  TTASK_ASSESMENT_TTASK_ID is remaped later. Target task may be imported later so not found exception throwed.
        const assesmentTTaskId = newTTask.TTASK_ASSESMENT_TTASK_ID;
        newTTask.TTASK_ASSESMENT_TTASK_ID = null;

        // Events are not imported, this attribute must be changed after event import is done !!
        newTTask.TTASK_EVENT = null;
        const subprocessTprocId = newTTask.TTASK_SUBPROCESS_TPROC_ID;
        newTTask.TTASK_SUBPROCESS_TPROC_ID = -1; // Skip, repository will not create subprocess event automatically. Must be imported later due to mapping !!

        const newTask = await ttaskRepo.store(newTTask, false);
        this.map.template_tasks[task.ttask_id] = newTask.TTASK_ID;
        if (assesmentTTaskId) {
            // Add to later import.
            this.remap.ttask_assesment[newTask.TTASK_ID] = assesmentTTaskId;
        }
        if (
            ["W", "P", "E"].indexOf(task.ttask_type) !== -1 &&
            task.ttask_event
        ) {
            // Add later update, just after events are imported !
            this.remap.ttask_event.push({
                ttask_event: task.ttask_event,
                ttask_id: task.ttask_id,
            });
        }

        if (subprocessTprocId > -1) {
            // Later update to ignore auto event creation (by ttaskRepo store)!!
            return await this.connection
                .select()
                .from("TEMPLATE_TASKS")
                .where("TTASK_ID", newTask.TTASK_ID)
                .update({
                    TTASK_SUBPROCESS_TPROC_ID: subprocessTprocId,
                });
        }
    }

    /**
     * Remap planning value like vo661 to new tvar id.
     * @param value
     * @returns {*}
     */
    remapTaskPlanning(value: string) {
        if (typeof value !== "string") {
            return value;
        }

        const type = value.substr(0, 2); // Get type from xx661
        const tvarId = value.substr(2); // Get number from xx661

        // Must be tvarid
        if (isNaN(Number(tvarId)) || _.isEmpty(tvarId)) {
            return value;
        }

        const newTvar = this.remapTvarIdAttr(
            { TVAR_ID: Number(tvarId) },
            "TVAR_ID",
        );
        return `${type}${newTvar}`;
    }

    async rewriteTask(task: Record<string, any>) {
        const newTprocId: any = this.remapTprocIdAttr(task, "tproc_id");
        const taskRepo = globalThis.orm.repo("templateTask", this.connection);

        // Get task by name or create new one !!
        const existingTask = await taskRepo.getByName(
            newTprocId[0],
            task.ttask_name,
        );
        if (existingTask === null) {
            // Not found, create new !
            return await this.createTask(task);
        }
        const updatedTaskEntity: Record<string, any> = await taskRepo.get(
            existingTask.TTASK_ID,
        );
        const updateArray: Record<string, any> = {};
        const attrs: string[] = updatedTaskEntity.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === updatedTaskEntity.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = task[lowerAttr];
            if (typeof attrValue !== "undefined") {
                updateArray[upperAttr] = attrValue;
            }
        });

        updateArray.TTASK_ASSESMENT_ROLE_ID = this.remapRoleAttr(
            updateArray,
            "TTASK_ASSESMENT_ROLE_ID",
        );
        updateArray.TTASK_ASSESMENT_USER_ID = this.remapUserIdAttr(
            updateArray,
            "TTASK_ASSESMENT_USER_ID",
        );
        updateArray.TTASK_ASSESMENT_ORGSTR_ID = this.remapOrgstrAttr(
            updateArray,
            "TTASK_ASSESMENT_ORGSTR_ID",
        );
        updateArray.TTASK_ASSESMENT_ORGSTR_CNST = this.remapOrgstrAttr(
            updateArray,
            "TTASK_ASSESMENT_ORGSTR_CNST",
        );
        const subProc: any = this.remapTprocIdAttr(
            _.cloneDeep(updateArray),
            "TTASK_SUBPROCESS_TPROC_ID",
        );
        updateArray.TTASK_SUBPROCESS_TPROC_ID = subProc[0];
        updateArray.TTASK_SUBPROCESS_TPROC_VERSION = subProc[1];
        updateArray.TTASK_ASSESMENT_TVAR_ID = this.remapTvarIdAttr(
            updateArray,
            "TTASK_ASSESMENT_TVAR_ID",
        );
        // Remap duration and due_offset
        updateArray.TTASK_DUE_OFFSET = this.remapTaskPlanning(
            updateArray.TTASK_DUE_OFFSET,
        );
        updateArray.TTASK_DURATION = this.remapTaskPlanning(
            updateArray.TTASK_DURATION,
        );

        //  TTASK_ASSESMENT_TTASK_ID is remaped later. Target task may be imported later so not found exception throwed.
        const assesmentTTaskId = updateArray.TTASK_ASSESMENT_TTASK_ID;
        updateArray.TTASK_ASSESMENT_TTASK_ID = null;
        if (assesmentTTaskId) {
            // Add to later import.
            this.remap.ttask_assesment[existingTask.TTASK_ID] =
                assesmentTTaskId;
        }

        updateArray.TPROC_ID = newTprocId[0];
        return await taskRepo.connection
            .select()
            .from("TEMPLATE_TASKS")
            .where("TTASK_ID", existingTask.TTASK_ID)
            .update(updateArray)
            .then(() => {
                this.map.template_tasks[task.ttask_id] = existingTask.TTASK_ID;
                if (
                    ["W", "P", "E"].indexOf(task.ttask_type) !== -1 &&
                    task.ttask_event
                ) {
                    // Add later update, just after events are imported !
                    this.remap.ttask_event.push({
                        ttask_event: task.ttask_event,
                        ttask_id: task.ttask_id,
                    });
                }
            });
    }

    async useCompatibleTasks(templateTasks: Record<string, any>) {
        if (!Array.isArray(templateTasks) || templateTasks.length === 0) {
            return;
        }

        for (const task of templateTasks) {
            await this.useCompatibleTask(task);
            await this.useCompatibleJSCalcs(task.calculations);
            await this.useCompatibleCompletitions(task.completition);
            await this.useCompatibleVarUsages(task.variable_usages);
            await this.useCompatibleMassUsages(task.mass_usages);
            await this.useCompatibleEmailNotifs(task.email_notifss);
            await this.useCompatibleInvitations(task.invitation);
        }
    }

    async rewriteTasks(
        templateTasks: Record<string, any>,
        variables: Record<string, any>,
    ) {
        if (!Array.isArray(templateTasks) || templateTasks.length === 0) {
            return;
        }

        for (const task of templateTasks) {
            // Do not halt query channel
            await this.rewriteTask(task);
            await this.rewriteJSCalcs(task.js_calculations, variables);
            await this.rewriteCompletitions(task.completition);
            await this.rewriteVarUsages(task.variable_usages);
            await this.rewriteMassUsages(task.mass_usages);
            await this.rewriteEmailNotifs(task.email_notifs, variables);
            await this.rewriteInvitations(task.invitation);
            await this.remapTTAskAssesmentTTaskId();
            await this.remapEnotTgtTTaskId();
        }
    }

    async createTasks(
        templateTasks: Record<string, any>,
        variables: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        if (!Array.isArray(templateTasks) || templateTasks.length === 0) {
            return;
        }

        for (const task of templateTasks) {
            // Do not halt query channel
            this.message("Importuji js úkoly");
            await this.createTask(task, tprocData);
            this.message("Importuji js výpočty");
            await this.createJSCalcs(task.js_calculations, variables);
            this.message("Importuji automatické dokončení");
            await this.createCompletitions(task.completition);
            this.message("Importuji mapování proměnných");
            await this.createVarUsages(task.variable_usages);
            this.message("Importuji mapování hromadných úkolů a proměnných");
            await this.createMassUsages(task.mass_usages);
            this.message("Importuji mail notifikací");
            await this.createEmailNotifs(task.email_notifs, variables);
            this.message("Importuji pozvánky");
            await this.createInvitations(task.invitation);
        }

        await this.remapTTAskAssesmentTTaskId();
        await this.remapEnotTgtTTaskId();
    }

    async remapTTAskAssesmentTTaskId() {
        const keys = Object.keys(this.remap.ttask_assesment);
        if (keys.length === 0) {
            return;
        }
        const ttaskRepo = globalThis.orm.repo("templateTask", this.connection);
        for (const taskId of keys) {
            const assesmentTaskId = this.remap.ttask_assesment[taskId];

            const newTtaskId = this.remapTTaskIdAttr(
                { TTASK_ASSESMENT_TTASK_ID: assesmentTaskId },
                "TTASK_ASSESMENT_TTASK_ID",
            );
            await ttaskRepo.connection
                .select()
                .from("TEMPLATE_TASKS")
                .where("TTASK_ID", taskId)
                .update({ TTASK_ASSESMENT_TTASK_ID: newTtaskId });
        }
    }

    async remapEnotTgtTTaskId() {
        const keys = Object.keys(this.remap.email_enot_ttask_id);
        if (keys.length === 0) {
            return;
        }
        const ttaskRepo = globalThis.orm.repo("templateTask", this.connection);
        for (const taskId of keys) {
            const enoTtaskId = this.remap.email_enot_ttask_id[taskId];
            const newTtaskId = this.remapTTaskIdAttr(
                { TTASK_ENOT_TGT_TTASK_ID: enoTtaskId },
                "TTASK_ENOT_TGT_TTASK_ID",
            );
            await ttaskRepo.connection
                .select()
                .from("TEMPLATE_TASK_EMAIL_NOTIFS")
                .where("TTASK_ID", taskId)
                .update({ TTASK_ENOT_TGT_TTASK_ID: newTtaskId });
        }
    }

    // *********************************************************************************************************************
    // ***************************************** TEMPLATE_CALCULATIONS *****************************************************
    // *********************************************************************************************************************

    async createJSCalc(
        calc: Record<string, any>,
        variables: Record<string, any>,
    ) {
        const calcRepo = globalThis.orm.repo(
            "templateTaskJSCalculation",
            this.connection,
        );
        const newCalc = calcRepo.getEntity();

        const attrs = newCalc.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newCalc.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = calc[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newCalc[upperAttr] = attrValue;
            }
            if (lowerAttr === "ttjscalc_js_es6" && calc.ttjscalc_js) {
                const transpiler = new AsyncAwaitTranspiler(calc.ttjscalc_js);
                transpiler.parse();
                newCalc[upperAttr] = transpiler.generateCode();
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newCalc.TTASK_ID = this.remapTTaskIdAttr(newCalc, "TTASK_ID");
        newCalc.TTJSCALC_VERSION = 1;
        newCalc.TTJSCALC_APPEND_SCRIPTS = this.remapCalcScriptIdAttr(
            newCalc,
            "TTJSCALC_APPEND_SCRIPTS",
        );

        // Remap calculations -> string = regex {oldId} -> {newId}
        if (Array.isArray(variables) && variables.length > 0) {
            const ids = _.map(variables, "tvar_id");
            ids.forEach((id) => {
                const newVarId = this.remapTvarIdAttr(
                    { TVAR_ID: id },
                    "TVAR_ID",
                );
                const pattern = new RegExp(`\\[${id}\\]`, "g");
                newCalc.TTJSCALC_JS =
                    newCalc.TTJSCALC_JS === null ? "" : newCalc.TTJSCALC_JS;
                newCalc.TTJSCALC_JS = newCalc.TTJSCALC_JS.replace(
                    pattern,
                    `[${newVarId}]`,
                ); // [oldId] -> [newId]
                newCalc.TTJSCALC_JS = this.replaceKnownJSCalculationParams(
                    newCalc.TTJSCALC_JS,
                );
            });
        }

        await calcRepo.store(newCalc, false);
    }

    replaceKnownJSCalculationParams(jsCalc: Record<string, any>) {
        // REPLACE CHANGED DYNAMIC_TABLE names.
        let jsCalcResult = jsCalc;
        const dtNames = Object.keys(this.map.dynamic_table_names);
        if (Array.isArray(dtNames) && dtNames.length > 0) {
            dtNames.forEach((dtName) => {
                const newDtName = this.map.dynamic_table_names[dtName];
                const regs = [
                    `(dynamicTableUpdateCell\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                    `(dynamicTableGetRowCounts\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                    `(dynamicTableGetCell\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                    `(dynamicTableUpdateCellCond\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                    `(dynamicTableGetRowId\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                    `(dynamicTableGetNewRowId\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                    `(dynamicTableGetNewRowIdCond\\(["\' ]{0,})(${dtName})([\'"]{1})`,
                ];
                regs.forEach((regPattern) => {
                    jsCalcResult = jsCalc.replace(
                        new RegExp(regPattern, "g"),
                        (
                            _fullMatch: string,
                            rec1: string,
                            _rec2: string,
                            rec3: string,
                        ) => rec1 + newDtName + (rec3 || ""),
                    );
                });
            });
        }
        return jsCalcResult;
    }

    async useCompatibleJSCalcs(calcs: Record<string, any>) {
        if (!Array.isArray(calcs) || calcs.length === 0) {
            return;
        }
    }

    async rewriteJSCalcs(
        calcs: Record<string, any>,
        variables: Record<string, any>,
    ) {
        if (!Array.isArray(calcs) || calcs.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const ttaskIds = _.map(calcs, "ttask_id");
        const counts: Record<string, any> = {};
        ttaskIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid calculations for template task. Please review export. task.template_calculations mus have same TTASK_ID for all calc.",
                "INVALID_CALC_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const calcRepo = globalThis.orm.repo(
            "templateTaskJSCalculation",
            this.connection,
        );
        const newTaskId = this.remapTTaskIdAttr(
            { ttask_id: ttaskIds[0] },
            "ttask_id",
        );
        await calcRepo.deleteForTTask(newTaskId);
        for (const calc of calcs) {
            await this.createJSCalc(calc, variables);
        }
    }

    async createJSCalcs(
        calcs: Record<string, any>,
        variables: Record<string, any>,
    ) {
        if (!Array.isArray(calcs) || calcs.length === 0) {
            return;
        }

        for (const calc of calcs) {
            await this.createJSCalc(calc, variables);
        }
    }

    // *********************************************************************************************************************
    // ********************************************** COMPLETITION *********************************************************
    // *********************************************************************************************************************

    async createCompletition(completition: Record<string, any>) {
        const completitionRepo = globalThis.orm.repo(
            "templateTaskCompletition",
            this.connection,
        );
        const newCompletition = completitionRepo.getEntity();

        const attrs = newCompletition.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newCompletition.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = completition[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newCompletition[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newCompletition.TTASK_ID = this.remapTTaskIdAttr(
            newCompletition,
            "TTASK_ID",
        );
        newCompletition.TVAR_ID = this.remapTvarIdAttr(
            newCompletition,
            "TVAR_ID",
        );
        newCompletition.TTC_VERSION = 1;

        await completitionRepo.store(newCompletition, false);
        return true;
    }

    async rewriteCompletitions(completitions: Record<string, any>) {
        if (!Array.isArray(completitions) || completitions.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const ttaskIds = _.map(completitions, "ttask_id");
        const counts: Record<string, any> = {};
        ttaskIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            new UserException(
                "Invalid completitions for template task. Please review export. task.completition mus have same TTASK_ID for all calc.",
                "INVALID_CALC_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const calcRepo = globalThis.orm.repo(
            "templateTaskCompletition",
            this.connection,
        );
        const newTaskId = this.remapTTaskIdAttr(
            { ttask_id: ttaskIds[0] },
            "ttask_id",
        );
        await calcRepo.deleteForTTask(newTaskId);
        for (const calc of completitions) {
            this.createCompletition(calc);
        }
    }

    async useCompatibleCompletitions(completitions: Record<string, any>) {
        if (!Array.isArray(completitions) || completitions.length === 0) {
            return;
        }
    }

    async createCompletitions(completitions: Record<string, any>) {
        if (!Array.isArray(completitions) || completitions.length === 0) {
            return;
        }

        for (const completition of completitions) {
            await this.createCompletition(completition);
        }
    }

    // *********************************************************************************************************************
    // ******************************************* TEMPLATE_SECTIONS *******************************************************
    // *********************************************************************************************************************

    async createSections(sections: Record<string, any>) {
        if (Array.isArray(sections) && sections.length > 0) {
            for (let i = 0; i < sections.length; i += 1) {
                await this.createSection(sections[i]);
            }
        }
    }

    async createSection(section: Record<string, any>) {
        const sectionsRepo = globalThis.orm.repo(
            "templateSections",
            this.connection,
        );
        const entity = sectionsRepo.getEntity();
        const attrs = entity.getColumnNames();
        attrs.forEach((attrName) => {
            const lowerAttr = attrName.toLowerCase();
            if (attrName === entity.primaryColumn) {
                return;
            }

            entity[attrName] = section[lowerAttr];
        });

        // Remap template process
        entity.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            entity,
            "TPROC_ID",
        );

        const newSectionId = await sectionsRepo.store(entity);
        this.map.template_sections[section.tsec_id] = newSectionId;
        return newSectionId;
    }

    async useCompatibleSections(sections: Record<string, any>) {
        if (Array.isArray(sections) && sections.length > 0) {
            for (let i = 0; i < sections.length; i += 1) {
                await this.useCompatibleSection(sections[i]);
            }
        }
    }

    async useCompatibleSection(section: Record<string, any>) {
        this.map.template_sections[section.tsec_id] = section.tsec_id;
    }

    async rewriteSections(sections: Record<string, any>) {
        if (Array.isArray(sections) && sections.length > 0) {
            for (let i = 0; i < sections.length; i += 1) {
                await this.rewriteSection(sections[i]);
            }
        }
    }

    async rewriteSection(section: Record<string, any>) {
        const newTprocId: any = this.remapTprocIdAttr(section, "tproc_id");
        const sectionsRepo = globalThis.orm.repo(
            "templateSections",
            this.connection,
        );
        const existingSection = await sectionsRepo.getFromTemplateByName(
            newTprocId[0],
            section.tsec_name,
        );

        if (existingSection && !_.isEmpty(existingSection)) {
            const sectionEntity = await sectionsRepo.get(
                existingSection.TSEC_ID,
            );
            await sectionsRepo.delete(sectionEntity);
        }
        return await this.createSection(section);
    }

    // *********************************************************************************************************************
    // ****************************************** VARIABLE_USAGES *********************************************************
    // *********************************************************************************************************************

    async createVarUsage(varUsage: Record<string, any>) {
        const varUsageRepo = globalThis.orm.repo(
            "templateTaskVarUsage",
            this.connection,
        );
        const newVarUsage = varUsageRepo.getEntity();

        const attrs = newVarUsage.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newVarUsage.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = varUsage[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newVarUsage[upperAttr] = attrValue;
            }

            // compatibility - old tsec_order to new TSEC_Y
            if (_.has(varUsage, "tsec_order")) {
                newVarUsage.TSEC_Y = varUsage.tsec_order + 1;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newVarUsage.TSEC_ID = this.remapTSecIdAttr(newVarUsage, "TSEC_ID");
        newVarUsage.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            newVarUsage,
            "TPROC_ID",
        );
        newVarUsage.TTASK_ID = this.remapTTaskIdAttr(newVarUsage, "TTASK_ID");
        newVarUsage.TVAR_ID = this.remapTvarIdAttr(newVarUsage, "TVAR_ID");
        newVarUsage.TTASKVARUSG_VERSION = 1;

        await varUsageRepo.store(newVarUsage, false);
        return true;
    }

    async rewriteVarUsages(varUsages: Record<string, any>) {
        if (!Array.isArray(varUsages) || varUsages.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const ttaskIds = _.map(varUsages, "ttask_id");
        const counts: Record<string, any> = {};
        ttaskIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid varUsages for template task. Please review export. task.completition mus have same TTASK_ID for all calc.",
                "INVALID_CALC_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const varUsgRepo = globalThis.orm.repo(
            "templateTaskVarUsage",
            this.connection,
        );
        const newTaskId = this.remapTTaskIdAttr(
            { ttask_id: ttaskIds[0] },
            "ttask_id",
        );
        await varUsgRepo.deleteForTTask(newTaskId);
        for (const mapping of varUsages) {
            await this.createVarUsage(mapping);
        }
    }

    async useCompatibleVarUsages(varUsages: Record<string, any>) {
        if (!Array.isArray(varUsages) || varUsages.length === 0) {
            return;
        }
    }

    async createVarUsages(varUsages: Record<string, any>) {
        if (!Array.isArray(varUsages) || varUsages.length === 0) {
            return;
        }
        for (const varUsage of varUsages) {
            await this.createVarUsage(varUsage);
        }
    }

    // *********************************************************************************************************************
    // ****************************************** TASK_MASS_USAGES *********************************************************
    // *********************************************************************************************************************

    async createMassUsage(varUsage: Record<string, any>) {
        const varUsageRepo = globalThis.orm.repo(
            "templateTaskMassUsage",
            this.connection,
        );
        const newVarUsage = varUsageRepo.getEntity();

        const attrs = newVarUsage.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newVarUsage.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = varUsage[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newVarUsage[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newVarUsage.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            { TPROC_ID: newVarUsage.TPROC_ID[0] },
            "TPROC_ID",
        );
        newVarUsage.TTASK_ID = this.remapTTaskIdAttr(newVarUsage, "TTASK_ID");
        newVarUsage.TVAR_ID = this.remapTvarIdAttr(newVarUsage, "TVAR_ID");
        newVarUsage.TSKMSS_VERSION = 1;

        await varUsageRepo.store(newVarUsage, false);
        return true;
    }

    async rewriteMassUsages(varUsages: Record<string, any>) {
        if (!Array.isArray(varUsages) || varUsages.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const ttaskIds = _.map(varUsages, "ttask_id");
        const counts: Record<string, any> = {};
        ttaskIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid massUsages for template task. Please review export. task.completition mus have same TTASK_ID for all calc.",
                "INVALID_CALC_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const varUsgRepo = globalThis.orm.repo(
            "templateTaskMassUsage",
            this.connection,
        );
        const newTaskId = this.remapTTaskIdAttr(
            { ttask_id: ttaskIds[0] },
            "ttask_id",
        );
        await varUsgRepo.deleteForTTask(newTaskId);
        for (const mapping of varUsages) {
            await this.createVarUsage(mapping);
        }
    }

    async useCompatibleMassUsages(varUsages: Record<string, any>) {
        if (!Array.isArray(varUsages) || varUsages.length === 0) {
            return;
        }
    }

    async createMassUsages(varUsages: Record<string, any>) {
        if (!Array.isArray(varUsages) || varUsages.length === 0) {
            return;
        }
        for (const varUsage of varUsages) {
            await this.createMassUsage(varUsage);
        }
    }

    // *********************************************************************************************************************
    // ****************************************** EMAIL_NOTIFS *********************************************************
    // *********************************************************************************************************************

    async createEmailNotif(
        notif: Record<string, any>,
        variables: Record<string, any>,
    ) {
        const notifRepo = globalThis.orm.repo(
            "templateTaskEmailNotifs",
            this.connection,
        );
        const newNotif = notifRepo.getEntity();

        const attrs = newNotif.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = notif[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newNotif[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        if (
            newNotif.TTASK_ENOT_TGT !== null &&
            typeof newNotif.TTASK_ENOT_TGT !== "undefined"
        ) {
            variables.forEach((variable: Record<string, any>) => {
                if (variable.tvar_name === newNotif.TTASK_ENOT_TGT) {
                    try {
                        // Throw error if not found
                        this.remapTvarIdAttr(variable, "TVAR_ID"); // Variable found by name, not necessary to change !
                    } catch (_e) {
                        this.losts.email_notifs.push(newNotif.TTASK_ENOT_TGT);
                    }
                }
            });
        }

        const attributes = Object.keys(newNotif.attributes());
        for (const attr of attributes.filter((attr) =>
            attr.endsWith("TTASK_ID"),
        )) {
            newNotif[attr] = this.remapTTaskIdAttr(newNotif, attr);
        }

        for (const attr of attributes.filter((attr) =>
            attr.endsWith("ORGSTR_ID"),
        )) {
            newNotif[attr] = this.remapOrgstrAttr(newNotif, attr);
        }

        for (const attr of attributes.filter((attr) =>
            attr.endsWith("ROLE_ID"),
        )) {
            newNotif[attr] = this.remapRoleAttr(newNotif, attr);
        }

        if (newNotif.TTASK_ENOT_TGT_TTASK_ID) {
            this.remap.email_enot_ttask_id[newNotif.TTASK_ID] =
                newNotif.TTASK_ENOT_TGT_TTASK_ID;
            newNotif.TTASK_ENOT_TGT_TTASK_ID = null;
        }

        return await notifRepo.store(newNotif, false);
    }

    async rewriteEmailNotifs(
        notifs: Record<string, any>,
        variables: Record<string, any>,
    ) {
        if (!Array.isArray(notifs) || notifs.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const ttaskIds = _.map(notifs, "ttask_id");
        const counts: Record<string, any> = {};
        ttaskIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid notifs for template task. Please review export. task.completition mus have same TTASK_ID for all calc.",
                "INVALID_CALC_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const notifRepo = globalThis.orm.repo(
            "templateTaskEmailNotifs",
            this.connection,
        );
        const newTaskId = this.remapTTaskIdAttr(
            { ttask_id: ttaskIds[0] },
            "ttask_id",
        );
        await notifRepo.deleteForTTask(newTaskId);
        for (const notif of notifs) {
            await this.createEmailNotif(notif, variables);
        }
    }

    async useCompatibleEmailNotifs(notifs: Record<string, any>) {
        if (!Array.isArray(notifs) || notifs.length === 0) {
            return;
        }
    }

    async createEmailNotifs(
        notifs: Record<string, any>,
        variables: Record<string, any>,
    ) {
        if (!Array.isArray(notifs) || notifs.length === 0) {
            return;
        }
        for (const notif of notifs) {
            await this.createEmailNotif(notif, variables);
        }
    }

    // *********************************************************************************************************************
    // *********************************************** INVITATION **********************************************************
    // *********************************************************************************************************************

    async createInvitation(inv: Record<string, any>) {
        const invRepo = globalThis.orm.repo(
            "templateTaskInvitation",
            this.connection,
        );
        const newInv = invRepo.getEntity();

        const attrs = newInv.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = inv[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newInv[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        // newInv.TTASK_INV_ATTENDEES can contain invalid names | emails
        newInv.TTASK_ID = this.remapTTaskIdAttr(newInv, "TTASK_ID");

        return await invRepo.store(newInv, false);
    }

    async rewriteInvitations(invs: Record<string, any>) {
        if (!Array.isArray(invs) || invs.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const ttaskIds = _.map(invs, "ttask_id");
        const counts: Record<string, any> = {};
        ttaskIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid invs for template task. Please review export. task.completition mus have same TTASK_ID for all calc.",
                "INVALID_CALC_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const notifRepo = globalThis.orm.repo(
            "templateTaskInvitation",
            this.connection,
        );
        const newTaskId = this.remapTTaskIdAttr(
            { ttask_id: ttaskIds[0] },
            "ttask_id",
        );
        await notifRepo.deleteForTTask(newTaskId);
        for (const inv of invs) {
            await this.createInvitation(inv);
        }
    }

    async useCompatibleInvitations(invs: Record<string, any>) {
        if (!Array.isArray(invs) || invs.length === 0) {
            return;
        }
    }

    async createInvitations(invs: Record<string, any>) {
        if (!Array.isArray(invs) || invs.length === 0) {
            return;
        }
        for (const inv of invs) {
            await this.createInvitation(inv);
        }
    }

    // *********************************************************************************************************************
    // ******************************************** TEMPLATE_LINKS *********************************************************
    // *********************************************************************************************************************

    useCompatibleLink(link: Record<string, any>) {
        // TODO compatible graph, links, etc.
        this.map.links[link.ttasklink_id] = link.ttasklink_id;
        return;
    }

    async createLink(link: Record<string, any>) {
        const linkRepo = globalThis.orm.repo(
            "templateTaskLink",
            this.connection,
        );
        const newLink: TemplateTaskLink = linkRepo.getEntity();

        const attrs = newLink.getColumnNames();
        attrs.forEach((attr: string) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newLink.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = link[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newLink[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newLink.TTASK_ID = this.remapTTaskIdAttr(newLink, "TTASK_ID");
        newLink.TTASKLINK_FROM_TTASK_ID = this.remapTTaskIdAttr(
            newLink,
            "TTASKLINK_FROM_TTASK_ID",
        );
        newLink.TTASKLINK_TO_TTASK_ID = this.remapTTaskIdAttr(
            newLink,
            "TTASKLINK_TO_TTASK_ID",
        );

        const id: any = this.remapTprocIdAttr(
            newLink,
            "TPROC_ID",
            "TTASKLINK_VERSION",
        );
        newLink.TPROC_ID = id[0];
        newLink.TTASKLINK_VERSION = id[1];

        this.map.links[link.ttasklink_id] = await linkRepo.store(
            newLink,
            false,
        );
        return true;
    }

    async rewriteLinks(links: Record<string, any>) {
        if (!Array.isArray(links) || links.length === 0) {
            return;
        }

        const tprocIds = _.map(links, "tproc_id");
        const counts: Record<string, any> = {};
        tprocIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid links for template process. Please review export. task.links mus have same TPROC_ID for all links.",
                "INVALID_LINK_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const linkRepo = globalThis.orm.repo(
            "templateTaskLink",
            this.connection,
        );
        const newTprocId: any = this.remapTprocIdAttr(
            { tproc_id: tprocIds[0] },
            "tproc_id",
        );
        await linkRepo.deleteForTemplateProcess(newTprocId[0], newTprocId[1]);
        for (const link of links) {
            link.TPROC_ID = newTprocId[0];
            link.TTASKLINK_FROM_TTASK_ID = this.remapTTaskIdAttr(
                link,
                "TTASKLINK_FROM_TTASK_ID",
            );
            link.TTASKLINK_TO_TTASK_ID = this.remapTTaskIdAttr(
                link,
                "TTASKLINK_TO_TTASK_ID",
            );

            await this.createLink(link);
            await this.createLinkConditions(link.conditions);
        }
    }

    async useCompatibleLinks(links: Record<string, any>) {
        if (!Array.isArray(links) || links.length === 0) {
            return;
        }

        for (const link of links) {
            await this.useCompatibleLink(link);
            await this.useCompatibleLinkConditions(link.conditions);
        }
    }

    async createLinks(links: Record<string, any>) {
        if (!Array.isArray(links) || links.length === 0) {
            return;
        }
        for (const link of links) {
            await this.createLink(link);
            await this.createLinkConditions(link.conditions);
        }
    }

    // *********************************************************************************************************************
    // ******************************************** TEMPLATE_LINKS *********************************************************
    // *********************************************************************************************************************

    useCompatibleLinkContidion(condition: Record<string, any>) {
        // TODO compatible graph, links, etc.
        this.map.conditions[condition.tcond_id] = condition.tcond_id;
        return;
    }

    async createLinkCondition(cond: Record<string, any>) {
        const linkRepo = globalThis.orm.repo(
            "templateLinkCondition",
            this.connection,
        );
        const newLinkCond = linkRepo.getEntity();

        const attrs = newLinkCond.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newLinkCond.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = cond[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newLinkCond[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newLinkCond.TTASKLINK_ID = this.remapTLinkIdAttr(
            newLinkCond,
            "TTASKLINK_ID",
        );
        newLinkCond.TVAR_ID = this.remapTvarIdAttr(newLinkCond, "TVAR_ID"); // TVAR_ID is always null, prepared to future change !!

        const id = await linkRepo.store(newLinkCond, false);
        this.map.conds[cond.tcond_id] = id;
    }

    async useCompatibleLinkConditions(conds: Record<string, any>) {
        if (!Array.isArray(conds) || conds.length === 0) {
            return;
        }
        for (const cond of conds) {
            await this.useCompatibleLinkContidion(cond);
        }
    }

    async createLinkConditions(conds: Record<string, any>) {
        if (!Array.isArray(conds) || conds.length === 0) {
            return;
        }
        for (const cond of conds) {
            await this.createLinkCondition(cond);
        }
    }

    // *********************************************************************************************************************
    // ******************************************** TEMPLATE_GRAPH *********************************************************
    // *********************************************************************************************************************

    async createGraph(
        graph: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        const graphRepo = globalThis.orm.repo("templateGraph", this.connection);
        const newGraph: TemplateGraph = graphRepo.getEntity();

        const attrs = newGraph.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newGraph.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = graph[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newGraph[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        try {
            newGraph.TGRAPH_VERSION = tprocData.tproc_version;
            const id: any = this.remapTprocIdAttr(
                newGraph,
                "TPROC_ID",
                "TGRAPH_VERSION",
            );
            newGraph.TPROC_ID = id[0];
            newGraph.TGRAPH_VERSION = id[1];

            newGraph.TTASK_ID = this.remapTTaskIdAttr(newGraph, "TTASK_ID");
            newGraph.TGRAPH_FROM_TTASK_ID = this.remapTTaskIdAttr(
                newGraph,
                "TGRAPH_FROM_TTASK_ID",
            );
            newGraph.TGRAPH_TO_TTASK_ID = this.remapTTaskIdAttr(
                newGraph,
                "TGRAPH_TO_TTASK_ID",
            );
            newGraph.TGRAPH_TTASKCON_ID = this.remapTLinkIdAttr(
                newGraph,
                "TGRAPH_TTASKCON_ID",
            ); // Do not change TGRAPH_TTASKCON_ID == remap link. Some TasOld bug!!!

            await graphRepo.store(newGraph, false);
        } catch (e: any) {
            // Graph is often broken, ignored no remap !
            globalThis.tasLogger.error(`Error on logErrorLine ${e.message}`, {
                e,
            });
        }

        return true;
    }

    async useCompatibleGraphs(graphs: Record<string, any>) {
        if (!Array.isArray(graphs) || graphs.length === 0) {
            return;
        }
    }

    async createGraphs(
        graphs: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        if (!Array.isArray(graphs) || graphs.length === 0) {
            return;
        }

        for (const graph of graphs) {
            await this.createGraph(graph, tprocData);
        }
    }

    async rewriteGraphs(
        graphs: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        if (!Array.isArray(graphs) || graphs.length === 0) {
            return;
        }

        // Array of calculations must have all TTASK_ID same, check it !
        const tprocIds = _.map(graphs, "tproc_id");
        const counts: Record<string, any> = {};
        tprocIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid graph for template process. Please review export. task.template_graph mus have same tproc_id for all graph.",
                "INVALID_GRAPH_IMPORT",
            );
        }

        const newTprocId: any = this.remapTprocIdAttr(
            { tproc_id: tprocIds[0] },
            "tproc_id",
        );
        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const graphRepo = globalThis.orm.repo("templateGraph", this.connection);
        await graphRepo.deleteForTPRoc(newTprocId[0], newTprocId[1]);
        for (const graph of graphs) {
            await this.createGraph(graph, tprocData);
        }
    }

    // *********************************************************************************************************************
    // *********************************************** HEADERS *************************************************************
    // *********************************************************************************************************************

    async createHeaderRoles(headerId: number, roles: Record<string, any>) {
        const roleRepo = globalThis.orm.repo("headerRole", this.connection);
        await roleRepo.removeForHeader(headerId);

        if (Array.isArray(roles) && roles.length > 0) {
            for (const role of roles) {
                const newRoleId = this.remapRoleAttr(role, "role_id");

                const entity = roleRepo.getEntity({
                    ROLE_ID: newRoleId,
                    HEADER_ID: headerId,
                });
                await roleRepo.store(entity);
            }
        }
        return headerId;
    }

    async createHeaderOrgstrs(headerId: number, orgstrs: Record<string, any>) {
        const orgstrRepo = globalThis.orm.repo("headerOrgstr", this.connection);
        await orgstrRepo.removeForHeader(headerId);

        if (Array.isArray(orgstrs) && orgstrs.length > 0) {
            for (const orgstr of orgstrs) {
                const newOrgstrId = this.remapOrgstrAttr(orgstr, "orgstr_id");

                const entity = orgstrRepo.getEntity({
                    ORGSTR_ID: newOrgstrId,
                    HEADER_ID: headerId,
                });
                await orgstrRepo.store(entity);
            }
        }
        return headerId;
    }

    async createHeader(
        header: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        const headerRepo = globalThis.orm.repo("header", this.connection);
        const entity = headerRepo.getEntity();
        const attrs = entity.getColumnNames();
        attrs.forEach((attrName) => {
            const lowerAttr = attrName.toLowerCase();
            if (attrName === entity.primaryColumn) {
                return;
            }

            entity[attrName] = header[lowerAttr];
        });

        // Remap template process
        entity.TPROC_VERSION = tprocData.tproc_version;
        entity.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            entity,
            "TPROC_ID",
        );

        // Remap default header name! Default header is created with template_process filled with $AllUsers role.
        if (
            tprocData.tproc_name === entity.HEADER_NAME &&
            tprocData.import_rules.data.tproc_name
        ) {
            entity.HEADER_NAME = tprocData.import_rules.data.tproc_name;
        }

        // Remap roles
        entity.HDR_VIS_ROLE_ID = this.remapRoleAttr(entity, "HDR_VIS_ROLE_ID");
        entity.HEADER_HR_ROLE_ID = this.remapRoleAttr(
            entity,
            "HEADER_HR_ROLE_ID",
        );

        const newHeaderId: any = await headerRepo.store(entity, false);
        await this.createHeaderRoles(newHeaderId, header.header_roles);
        await this.createHeaderOrgstrs(newHeaderId, header.header_orgstrs);

        return newHeaderId;
    }

    /**
     *
     * @param header
     * @param tprocData
     * @return {Promise.<*>|Promise<any>}
     */
    async rewriteHeader(
        header: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        header.TPROC_VERSION = tprocData.TPROC_VERSION;
        const newTprocId: any = this.remapTprocIdAttr(
            header,
            "tproc_id",
            "TPROC_ID",
        );

        // Remap roles
        header.hdr_vis_role_id = this.remapRoleAttr(header, "hdr_vis_role_id");
        header.header_hr_role_id = this.remapRoleAttr(
            header,
            "header_hr_role_id",
        );

        const headerRepo = globalThis.orm.repo("header", this.connection);
        // Get header by name or create new one !!
        const newHeader = await headerRepo.getByName(
            newTprocId[0],
            header.header_name,
        );

        if (newHeader === null) {
            // Not found, create new !
            return await this.createHeader(header, tprocData);
        }
        const newHeaderEntity = await headerRepo.get(newHeader.HEADER_ID);
        const attrs = newHeaderEntity.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newHeaderEntity.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = header[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newHeaderEntity[upperAttr] = attrValue;
            }
        });

        newHeaderEntity.TPROC_ID = newTprocId[0];
        const updatedHeaderId: any = await headerRepo.store(
            newHeaderEntity,
            false,
        );
        await this.createHeaderRoles(updatedHeaderId, header.header_roles);
        await this.createHeaderOrgstrs(updatedHeaderId, header.header_orgstrs);
        return updatedHeaderId;
    }

    async useCompatibleHeaders(headers: Record<string, any>) {
        if (!Array.isArray(headers) || headers.length === 0) {
            return;
        }
    }

    async createHeaders(
        headers: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        if (!Array.isArray(headers) || headers.length === 0) {
            return;
        }

        for (const header of headers) {
            await this.createHeader(header, tprocData);
        }
    }

    async rewriteHeaders(
        headers: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        if (!Array.isArray(headers) || headers.length === 0) {
            return;
        }

        for (const header of headers) {
            await this.rewriteHeader(header, tprocData);
        }
    }

    async useCompatibleStatuses(
        statuses: Record<string, any>,
        tprocData: Record<string, any>,
    ) {
        if (!Array.isArray(statuses) || statuses.length === 0) {
            return;
        }

        for (const status of statuses) {
            try {
                await this.createCaseStatus(status, tprocData);
            } catch (_err) {
                // Already exists, could not insert => ignore
            }
        }
    }

    async createCaseStatuses(
        statuses: Record<string, any>,
        tprocEntity: Record<string, any>,
    ) {
        if (!Array.isArray(statuses) || !statuses.length) {
            return;
        }

        for (const status of statuses) {
            await this.createCaseStatus(status, tprocEntity);
        }
    }

    async createCaseStatus(
        status: Record<string, any>,
        tprocData: Record<string, any> = {},
    ) {
        const caseStatusRepo = globalThis.orm.repo(
            "caseStatus",
            this.connection,
        );
        const entity = caseStatusRepo.getEntity();
        const attrs = entity.getColumnNames();

        attrs.forEach((attrName) => {
            const lowerAttr = attrName.toLowerCase();
            entity[attrName] = status[lowerAttr];
        });

        // Remap template process
        entity.TPROC_VERSION = tprocData.tproc_version;
        entity.TPROC_ID = this.remapTprocIdAttrWithAnyVersion(
            entity,
            "TPROC_ID",
        );
        entity.forceToInsert();
        return await caseStatusRepo.store(entity);
    }

    async rewriteStatuses(statuses: Record<string, any>) {
        if (!Array.isArray(statuses) || statuses.length === 0) {
            return;
        }

        for (const status of statuses) {
            await this.rewriteStatus(status);
        }
    }

    async rewriteStatus(status: Record<string, any>) {
        const newTprocId: any = this.remapTprocIdAttr(status, "tproc_id");
        const repo = globalThis.orm.repo("caseStatus", this.connection);

        const oldStatuses = await repo.getByValueForTemplate(
            status.cs_name,
            newTprocId[0],
        );
        const entity = repo.getEntity();

        if (oldStatuses.length === 0) {
            // Nothing exists, create a new record.
            return await this.createCaseStatus(status);
        }
        const oldStatus = oldStatuses[0];

        // Exists, rewrite.
        const attrs = entity.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            // Set attribute name
            // No need to skip primary key attr because of multi-key
            const attrValue = status[lowerAttr];
            if (typeof attrValue !== "undefined" && attrValue !== null) {
                oldStatus[upperAttr] = attrValue;
            }
        });

        oldStatus.TPROC_ID = newTprocId[0];

        return await repo.store(repo.getEntity(oldStatus));
    }

    // *********************************************************************************************************************
    // ******************************************* TEMPLATE_PROCESS  *******************************************************
    // *********************************************************************************************************************

    /**
     * Use compatible user entity.
     * @param dt
     */
    useCompatibleTemplateProcess(dt: Record<string, any>) {
        if (!dt.import_rules.data.id) {
            throw new UserException(
                "TPROC_ID not defined in USE_COMPATIBLE TEMPLATE_PROCESS import.",
                "INVALID_IMPORT_STATE",
            );
        }

        this.addTprocMap(`${dt.tproc_id},${dt.tproc_version}`, [
            dt.import_rules.data.id.tproc_id,
            dt.import_rules.data.id.tproc_version,
        ]);
    }

    /**
     * Create new template_process entity.
     * @param tproc
     */
    async createTemplateProcess(tproc: Record<string, any>): Promise<void> {
        const tprocRepo = globalThis.orm.repo(
            "templateProcess",
            this.connection,
        );
        const newTproc = tprocRepo.getEntity();

        const attrs = newTproc.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = tproc.import_rules.data[attr]
                ? tproc.import_rules.data[attr]
                : tproc[attr];
            if (typeof attrValue !== "undefined") {
                newTproc[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        newTproc.TPROC_OWNER_USER_ID = this.remapUserIdAttr(
            newTproc,
            "TPROC_OWNER_USER_ID",
        );
        newTproc.TPROC_LOCKED_BY_USER_ID = this.remapUserIdAttr(
            newTproc,
            "TPROC_LOCKED_BY_USER_ID",
        );
        newTproc.TPROC_LAST_CHANGED_BY_USER_ID = this.remapUserIdAttr(
            newTproc,
            "TPROC_LAST_CHANGED_BY_USER_ID",
        );
        newTproc.TPROC_VIS_ROLE_ID = this.remapRoleAttr(
            newTproc,
            "TPROC_VIS_ROLE_ID",
        );
        newTproc.TPROC_VIS_ORGSTR_ID = this.remapOrgstrAttr(
            newTproc,
            "TPROC_VIS_ORGSTR_ID",
        );

        newTproc.TPROC_HR_ROLE_ID = this.remapRoleAttr(
            newTproc,
            "TPROC_HR_ROLE_ID",
        );

        newTproc.TPROC_VERSION = 1;
        newTproc.TPROC_ID = null;

        // Remap template_variables in TPROC_TVAR_ORDER.
        try {
            const order = JSON.parse(newTproc.TPROC_TVAR_ORDER);
            if (Array.isArray(order) && order.length > 0) {
                const newTprocTvarOrder = [];
                for (let i = 0; i < order.length; i++) {
                    const newTvarId = this.remapTvarIdAttr(
                        { TVAR_ID: order[i] },
                        "TVAR_ID",
                    );
                    newTprocTvarOrder.push(newTvarId);
                }
                newTproc.TPROC_TVAR_ORDER = JSON.stringify(newTprocTvarOrder);
            }
        } catch (_err) {
            // Ignore missing variables in mapping etc.
        }

        if (newTproc.TPROC_STATUS !== "E") {
            // if not erased
            newTproc.TPROC_STATUS = "D"; // Make every imported template as Developed
        }
        await tprocRepo.autoGeneratePrimary(newTproc);
        newTproc.forceToInsert();
        const id = await tprocRepo.store(newTproc, false);
        if (id != null && Array.isArray(id)) {
            this.addTprocMap(`${tproc.tproc_id},${tproc.tproc_version}`, id);
        }
    }

    /**
     * Rewrite existing tprocData.
     * @param tprocData
     */
    async rewriteTemplateProcess(tprocData: Record<string, any>) {
        if (!tprocData.import_rules.data.id) {
            throw new UserException(
                "TPROC_ID not defined in REWRITE TEMPLATE_PROCESS import.",
                "INVALID_IMPORT_STATE",
            );
        }

        const tprocRepo = globalThis.orm.repo(
            "templateProcess",
            this.connection,
        );
        // Find tprocData and rewrite its entity
        const rewritedTproc = await tprocRepo.get([
            tprocData.import_rules.data.id.tproc_id,
            tprocData.import_rules.data.id.tproc_version,
        ]);
        const attrs = rewritedTproc.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === rewritedTproc.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = tprocData.import_rules.data[attr]
                ? tprocData.import_rules.data[attr]
                : tprocData[attr];
            if (typeof attrValue !== "undefined") {
                rewritedTproc[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        rewritedTproc.TPROC_OWNER_USER_ID = this.remapUserIdAttr(
            rewritedTproc,
            "TPROC_OWNER_USER_ID",
        );
        rewritedTproc.TPROC_LOCKED_BY_USER_ID = this.remapUserIdAttr(
            rewritedTproc,
            "TPROC_LOCKED_BY_USER_ID",
        );
        rewritedTproc.TPROC_LAST_CHANGED_BY_USER_ID = this.remapUserIdAttr(
            rewritedTproc,
            "TPROC_LAST_CHANGED_BY_USER_ID",
        );
        rewritedTproc.TPROC_VIS_ROLE_ID = this.remapRoleAttr(
            rewritedTproc,
            "TPROC_VIS_ROLE_ID",
        );
        rewritedTproc.TPROC_VIS_ORGSTR_ID = this.remapOrgstrAttr(
            rewritedTproc,
            "TPROC_VIS_ORGSTR_ID",
        );

        this.addTprocMap(`${tprocData.tproc_id},${tprocData.tproc_version}`, [
            tprocData.import_rules.data.id.tproc_id,
            tprocData.import_rules.data.id.tproc_version,
        ]);
        return await tprocRepo.store(rewritedTproc, false);
    }

    /**
     * Import templateProcess entites (template tasks, headers, etc)
     * @param templateProcess
     */
    async importTemplateProcessEntites(templateProcess: Record<string, any>) {
        if (
            !templateProcess ||
            !templateProcess.import_rules ||
            !templateProcess.import_rules.action
        ) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = templateProcess.import_rules;

        // CREATE NEW TEMPLATE_PROCESS
        if (importRule.action === "CREATE") {
            await this.createHeaders(templateProcess.headers, templateProcess);
            await this.createSections(templateProcess.template_sections);
            await this.createVariables(
                templateProcess.template_variables,
                templateProcess.template_variable_lovs,
            );
            await this.createPrints(templateProcess.template_prints);
            await this.createTasks(
                templateProcess.template_tasks,
                templateProcess.template_variables,
                templateProcess,
            );
            await this.createLinks(templateProcess.template_links);
            await this.createGraphs(
                templateProcess.template_graph,
                templateProcess,
            );
            await this.createCaseStatuses(
                templateProcess.case_statuses,
                templateProcess,
            );
        } else if (importRule.action === "USE_COMPATIBLE") {
            await this.useCompatibleHeaders(templateProcess.headers);
            await this.useCompatibleSections(templateProcess.template_sections);
            await this.useCompatibleVariables(
                templateProcess.template_variables,
            );
            await this.useCompatiblePrints(templateProcess.template_prints);
            await this.useCompatibleTasks(templateProcess.template_tasks);
            await this.useCompatibleLinks(templateProcess.template_links);
            await this.useCompatibleGraphs(templateProcess.template_graph);
            await this.useCompatibleStatuses(
                templateProcess.case_statuses,
                templateProcess,
            );
        } else if (importRule.action === "REWRITE") {
            await this.rewriteHeaders(templateProcess.headers, templateProcess);
            await this.rewriteSections(templateProcess.template_sections);
            await this.rewriteStatuses(templateProcess.case_statuses);
            await this.rewriteVariables(
                templateProcess.template_variables,
                templateProcess.template_variable_lovs,
            );
            await this.rewritePrints(templateProcess.template_prints);
            await this.rewriteTasks(
                templateProcess.template_tasks,
                templateProcess.template_variables,
            );
            await this.rewriteLinks(templateProcess.template_links);
            await this.rewriteGraphs(
                templateProcess.template_graph,
                templateProcess,
            );
        } else {
            throw new UserException(
                `Unknown import action ${templateProcess.import_rules.action}.`,
            );
        }
    }

    /**
     * Import templateProcess
     * @param templateProcess
     */
    async importTemplateProcess(templateProcess: Record<string, any>) {
        if (
            !templateProcess ||
            !templateProcess.import_rules ||
            !templateProcess.import_rules.action
        ) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = templateProcess.import_rules;

        // Some values are not imported !
        // TODO clear TPROC_OWNER ?

        // CREATE NEW TEMPLATE_PROCESS
        if (importRule.action === "CREATE") {
            return await this.createTemplateProcess(templateProcess);
        }

        // USE COMPATIBLE TEMPLATE_PROCESS
        if (importRule.action === "USE_COMPATIBLE") {
            return this.useCompatibleTemplateProcess(templateProcess);
        }

        // REWRITE TEMPLATE_PROCESS
        if (importRule.action === "REWRITE") {
            return await this.rewriteTemplateProcess(templateProcess);
        }

        throw new UserException(
            `Unknown import action ${templateProcess.import_rules.action}.`,
        );
    }

    async importTemplateProcesses(templateProcesses: Record<string, any>) {
        if (!templateProcesses || !Array.isArray(templateProcesses)) {
            return;
        }

        // Založení šablony
        for (const tproc of templateProcesses) {
            // Do not halt
            // Import processes
            this.message(`Importuji šablonu ${tproc.tproc_name}`);
            await this.importTemplateProcess(tproc);
        }

        // Založení entit šablony. Úkoly, proměnné, ...
        for (const tproc of templateProcesses) {
            // Do not halt
            // Import processes
            this.message("Importuji data šablony");
            await this.importTemplateProcessEntites(tproc);
        }
    }

    // *********************************************************************************************************************
    // ***********************************************  EVENT_DEFINITION  **************************************************
    // *********************************************************************************************************************

    /**
     * Create new event entity.
     * @param event
     */
    async createEventDefinition(event: Record<string, any>) {
        const eventDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );
        const newEventDef = eventDefRepo.getEntity();

        const attrs = newEventDef.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            attr = attr.toLowerCase();

            if (upperAttr === newEventDef.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = event.import_rules.data[attr]
                ? event.import_rules.data[attr]
                : event[attr];
            if (typeof attrValue !== "undefined") {
                newEventDef[upperAttr] = attrValue;
            }
        });

        // Is rdef subprocess ? Like $SUBP_{id}, $SUBR_{id} -> Remap ttask id to imported ttask id !!
        newEventDef.EVEDEF_NAME = this.remapEventSubpSubrTTaskId(
            newEventDef.EVEDEF_NAME,
        );

        const id = await eventDefRepo.store(newEventDef);
        this.map.event_definition[event.evedef_id] = id;
        this.map.event_definition_names[event.evedef_name] =
            newEventDef.EVEDEF_NAME;
    }

    /**
     * Use compatible user entity.
     * @param event
     */
    useCompatibleEventDefinition(event: Record<string, any>) {
        if (!event.import_rules.data.evedef_id) {
            new UserException(
                "EVEDEF_ID not defined in USE_COMPATIBLE event import.",
                "INVALID_IMPORT_STATE",
            );
        }

        this.map.event_definition_names[event.evedef_name] = event.evedef_name;
        this.map.event_definition[event.evedef_id] =
            event.import_rules.data.evedef_id;
        return;
    }

    /**
     * Rewrite existing event.
     * @param event
     */
    async rewriteEventDefinition(event: Record<string, any>) {
        if (!event.import_rules.data.evedef_id) {
            throw new UserException(
                "EVEDEF_ID not defined in REWRITE event import.",
                "INVALID_IMPORT_STATE",
            );
        }

        const eveDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );
        // Find event and rewrite its entity
        let oldName: string;
        let newEvedefName: string;

        return eveDefRepo
            .get(event.import_rules.data.evedef_id)
            .then((rewritedEveDef) => {
                oldName = rewritedEveDef.EVEDEF_NAME;
                const attrs = rewritedEveDef.getColumnNames();
                attrs.forEach((attr) => {
                    const upperAttr = attr;
                    attr = attr.toLowerCase();

                    if (upperAttr === rewritedEveDef.primaryColumn) {
                        return; // Skip primary key changing.
                    }

                    // Not necessary to rewrite $SUBP, $SUBR bcs ttask_id will not change on template_process rewriting !

                    // Set attribute name, overriden attr from import_rules rather than original attr
                    const attrValue = event.import_rules.data[attr]
                        ? event.import_rules.data[attr]
                        : event[attr];
                    if (typeof attrValue !== "undefined") {
                        rewritedEveDef[upperAttr] = attrValue;
                    }
                });

                newEvedefName = rewritedEveDef.EVEDEF_NAME;
                return eveDefRepo.store(rewritedEveDef);
            })
            .then(() => {
                this.map.event_definition[event.evedef_id] =
                    event.import_rules.data.evedef_id;
                this.map.event_definition_names[oldName] = newEvedefName;
            });
    }

    /**
     * Import event
     * @param event
     */
    async importEventDefinition(event: Record<string, any>) {
        if (!event || !event.import_rules || !event.import_rules.action) {
            throw new UserException(
                "Invalid import state. Missing import_rules or its action.",
                "INVALID_IMPORT_STATE",
            );
        }

        const importRule = event.import_rules;

        if (importRule.action === "CREATE") {
            await this.createEventDefinition(event);
            return await this.createRuleDefinitions(event.rules);
        }
        if (importRule.action === "USE_COMPATIBLE") {
            this.useCompatibleEventDefinition(event);
            return await this.useCompatibleRuleDefinitions(event.rules);
        }
        if (importRule.action === "REWRITE") {
            await this.rewriteEventDefinition(event);
            return await this.rewriteRuleDefinitions(event.rules);
        }
        throw new UserException(
            `Unknown import action ${event.import_rules.action}.`,
        );
    }

    async importEvents(events: Record<string, any>) {
        if (!events || !Array.isArray(events)) {
            return;
        }

        for (const event of events) {
            await this.importEventDefinition(event);
        }
    }

    // *********************************************************************************************************************
    // ************************************************** RULES ************************************************************
    // *********************************************************************************************************************

    useCompatibleRuleDefinition(rule: Record<string, any>) {
        // TODO compatible graph, links, etc.
        this.map.rule_definition[rule.rdef_id] = rule.rdef_id;
        return;
    }

    async createRuleDefinition(rule: Record<string, any>) {
        const ruleRepo = globalThis.orm.repo("ruleDefinition", this.connection);
        const newRule = ruleRepo.getEntity();

        const attrs = newRule.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            if (upperAttr === newRule.primaryColumn) {
                return; // Skip primary key changing.
            }

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = rule[lowerAttr];
            if (typeof attrValue !== "undefined") {
                newRule[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        if (newRule.RDEF_VALUE) {
            // Remap rdef value if not empty
            // Is rdef_value tproc_id ??
            if (
                [
                    "PROCESS",
                    "CSV_RUN_PROCESSES",
                    "CSV_UPDATE_PROCESSES",
                    "CSV_MERGE_PROCESSES",
                    "CSV_UPDATE_PROCESS",
                    "CSV_EXPORT_PROCESSES",
                ].indexOf(newRule.RDEF_TYPE) !== -1
            ) {
                const id = newRule.RDEF_VALUE.split("."); // TPROC_ID.TPROC_VERSION
                const newId: any = this.remapTprocIdAttr(
                    { TPROC_ID: id[0], TPROC_VERSION: id[1] },
                    "TPROC_ID",
                    "TPROC_VERSION",
                );
                newRule.RDEF_VALUE = `${newId[0]}.${newId[1]}`;
            } else {
                // Is rdef subprocess ? Like $SUBP_{id}, $SUBR_{id} -> Remap ttask id to imported ttask id !!
                newRule.RDEF_VALUE = this.remapEventSubpSubrTTaskId(
                    newRule.RDEF_VALUE,
                );
                // Is rdef subprocess ? Like $EVEW_{id} -> Remap ttask id to imported ttask id !!
                newRule.RDEF_VALUE = this.remapEventWait(newRule.RDEF_VALUE);
            }
        }

        newRule.EVEDEF_ID = this.remapEveDefId(newRule, "EVEDEF_ID");

        const id = await ruleRepo.store(newRule, false);
        this.map.rule_definition[rule.rdef_id] = id;
    }

    async useCompatibleRuleDefinitions(rules: Record<string, any>) {
        if (!Array.isArray(rules) || rules.length === 0) {
            return;
        }

        for (const rule of rules) {
            await this.useCompatibleRuleDefinition(rule);
            await this.useCompatibleRuleParams(rule.rule_params);
            await this.useCompatibleRuleVariables(rule.rule_variables);
        }
    }

    async createRuleDefinitions(rules: Record<string, any>) {
        if (!Array.isArray(rules) || rules.length === 0) {
            return;
        }

        for (const rule of rules) {
            await this.createRuleDefinition(rule);
            await this.createRuleParams(rule.rule_params);
            await this.createRuleVariables(rule.rule_variables);
        }
    }

    async rewriteRuleDefinitions(rules: Record<string, any>) {
        if (!Array.isArray(rules) || rules.length === 0) {
            return;
        }

        // Array of rules must have all EVEDEF_ID same, check it !
        const evedefId = _.map(rules, "evedef_id");
        const counts: Record<string, any> = {};
        evedefId.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid rules for rules. Please review export. event.crules must have same EVEDEF_ID for all rules.",
                "INVALID_RULE_IMPORT",
            );
        }

        // Rewrite calc -> easier to remove all and create again. It fix calc order and its count.
        const rdefRepo = globalThis.orm.repo("ruleDefinition", this.connection);
        await rdefRepo.deleteForEvendDefinition(evedefId[0]);

        for (const rule of rules) {
            await this.createRuleDefinition(rule);
            await this.rewriteRuleParams(rule.rule_params);
            await this.rewriteRuleVariables(rule.rule_variables);
        }
    }

    // *********************************************************************************************************************
    // ************************************************ RULE_PARAMS ********************************************************
    // *********************************************************************************************************************

    async createRuleParam(ruleParam: Record<string, any>) {
        const ruleParamRepo = globalThis.orm.repo(
            "ruleDefinitionParam",
            this.connection,
        );
        const entity = ruleParamRepo.getEntity();

        const attrs = entity.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = ruleParam[lowerAttr];
            if (typeof attrValue !== "undefined") {
                entity[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        entity.RDEF_ID = this.remapRuleDefId(entity, "RDEF_ID");

        entity.TVAR_ID_DEST = this.remapTvarIdAttr(entity, "TVAR_ID_DEST");
        entity.TVAR_ID = this.remapTvarIdAttr(entity, "TVAR_ID");

        // Remap rule calculations !
        if (entity.RDEFPAR_VALUE) {
            const variables = Object.keys(this.map.template_variables);
            if (Array.isArray(variables) && variables.length > 0) {
                variables.forEach((id) => {
                    const newVarId = this.remapTvarIdAttr(
                        { TVAR_ID: id },
                        "TVAR_ID",
                    );
                    const pattern = new RegExp(`\\{${id}\\}`, "g");
                    entity.RDEFPAR_VALUE = entity.RDEFPAR_VALUE.replace(
                        pattern,
                        `{${newVarId}}`,
                    ); // {oldId} -> {newId}
                });
            }
        }

        return await ruleParamRepo.store(entity, false);
    }

    async rewriteRuleParams(ruleParams: Record<string, any>) {
        if (!Array.isArray(ruleParams) || ruleParams.length === 0) {
            return;
        }

        // Array of ruleParams must have all TTASK_ID same, check it !
        const rdefIds = _.map(ruleParams, "rdef_id");
        const counts: Record<string, any> = {};
        rdefIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid ruleParams for rule_variables. Please review export. rule.rule_variables mus have same RDEF_ID for all ruleParams.",
                "INVALID_RULE_IMPORT",
            );
        }

        for (const ruleParam of ruleParams) {
            await this.createRuleParam(ruleParam);
        }
    }

    async useCompatibleRuleParams(ruleVars: Record<string, any>) {
        if (!Array.isArray(ruleVars) || ruleVars.length === 0) {
            return;
        }
    }

    async createRuleParams(ruleParams: Record<string, any>) {
        if (!Array.isArray(ruleParams) || ruleParams.length === 0) {
            return;
        }

        for (const ruleParam of ruleParams) {
            await this.createRuleParam(ruleParam);
        }
    }

    // *********************************************************************************************************************
    // ************************************************ RULE_VARIABLES ********************************************************
    // *********************************************************************************************************************

    async createRuleVariable(ruleVar: Record<string, any>) {
        const ruleVarRepo = globalThis.orm.repo(
            "ruleDefinitionVariable",
            this.connection,
        );
        const entity = ruleVarRepo.getEntity();

        const attrs = entity.getColumnNames();
        attrs.forEach((attr) => {
            const upperAttr = attr;
            const lowerAttr = attr.toLowerCase();

            // Set attribute name, overriden attr from import_rules rather than original attr
            const attrValue = ruleVar[lowerAttr];
            if (typeof attrValue !== "undefined") {
                entity[upperAttr] = attrValue;
            }
        });

        // Remap attributes. Rewrite User id, Orgstr id etc ..
        entity.RDEF_ID = this.remapRuleDefId(entity, "RDEF_ID");
        // Scan rdefvar_value for some remap ??

        return await ruleVarRepo.store(entity, false);
    }

    async rewriteRuleVariables(ruleVars: Record<string, any>) {
        if (!Array.isArray(ruleVars) || ruleVars.length === 0) {
            return;
        }

        // Array of ruleVars must have all TTASK_ID same, check it !
        const rdefIds = _.map(ruleVars, "rdef_id");
        const counts: Record<string, any> = {};
        rdefIds.forEach((x) => {
            counts[x] = (counts[x] || 0) + 1;
        });
        if (Object.keys(counts).length > 1) {
            throw new UserException(
                "Invalid ruleVars for rule_variables. Please review export. rule.rule_variables mus have same RDEF_ID for all ruleVars.",
                "INVALID_RULE_IMPORT",
            );
        }

        // Rule variables are removed while rewriting rules.
        for (const ruleVar of ruleVars) {
            await this.createRuleVariable(ruleVar);
        }
    }

    async useCompatibleRuleVariables(ruleVars: Record<string, any>) {
        if (!Array.isArray(ruleVars) || ruleVars.length === 0) {
            return;
        }
    }

    async createRuleVariables(ruleVars: Record<string, any>) {
        if (!Array.isArray(ruleVars) || ruleVars.length === 0) {
            return;
        }

        for (const ruleVar of ruleVars) {
            await this.createRuleVariable(ruleVar);
        }
    }

    // *********************************************************************************************************************
    // *********************************************** REMAP  *************************************************************
    // *********************************************************************************************************************

    async remapTTaskEvents(eventMap: Record<string, any>) {
        if (Array.isArray(eventMap) && eventMap.length > 0) {
            for (const remap of eventMap) {
                const newEvedefName = this.remapEveDefname(
                    { TTASK_EVENT: remap.ttask_event },
                    "TTASK_EVENT",
                );
                const newTTaskId = this.remapTTaskIdAttr(
                    { TTASK_ID: remap.ttask_id },
                    "TTASK_ID",
                );
                await this.connection
                    .select()
                    .from("TEMPLATE_TASKS")
                    .where("TTASK_ID", newTTaskId)
                    .update({ TTASK_EVENT: newEvedefName });
            }
        }
    }

    getTemplateProcessImportRuleFromTTaskId(
        ttaskId: number,
        importData: Record<string, any>,
    ) {
        let found: any = false;
        if (
            Array.isArray(importData.template_processes) &&
            importData.template_processes.length > 0
        ) {
            importData.template_processes.forEach((tproc) => {
                if (found) {
                    return;
                }
                if (
                    Array.isArray(tproc.template_tasks) &&
                    tproc.template_tasks.length > 0
                ) {
                    tproc.template_tasks.forEach(
                        (ttask: Record<string, any>) => {
                            if (ttask.ttask_id === ttaskId) {
                                found = tproc;
                            }
                        },
                    );
                }
            });
        }

        if (!found) {
            throw new UserException(
                `Can not find import_rule for TTASK_ID=${ttaskId}`,
                "INVALID_IMPORT_STATE",
            );
        }
        return found.import_rules;
    }

    // Scan SUBP and SUBR -> based on TTASK_ID so rule_action must be same as imported template_process !!
    async setupSubpSubrImportRules(importData: Record<string, any>) {
        const eveDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );
        if (Array.isArray(importData.events) && importData.events.length > 0) {
            for (const event of importData.events) {
                // Scan all events
                if (event.evedef_name) {
                    // if evedef name not empty
                    const match = event.evedef_name.match(
                        /(\$SUB[P|R]_)([0-9]*)/,
                    ); // matching $SUBP_{id} or $SUBR_{id} ?
                    if (match) {
                        const ttaskId = Number(match[2]);
                        const newEvedefName =
                            match[1] +
                            this.remapTTaskIdAttr(
                                { ttask_id: ttaskId },
                                "ttask_id",
                            );
                        // Delete subprocess and create new.
                        event.import_rules = {
                            action: "CREATE",
                            data: {
                                // will be remapped in event_Definition import !!
                                evedef_name: event.evedef_name,
                            },
                        };
                        await eveDefRepo.removeByName(newEvedefName);
                    }
                }
            }
        }
    }

    message(text: string) {
        // TODO socket info, time log
        globalThis.tasLogger.info(text);
    }

    async importProcess(importData: Record<string, any>) {
        this.version = importData.VERSION || 3;

        // If missing TPROC_VERSION it's probably export from non versioning TAS instance.
        this.oldExport = false;
        for (const tproc of importData.template_processes) {
            if (!tproc.TPROC_VERSION) {
                this.oldExport = true;
            }
        }

        if (this.oldExport) {
            globalThis.tasLogger.warning(
                "Importing old template export. Using compatibility mode.",
            );
        }

        await this.importDynamicTables(importData.dynamic_tables);
        await this.importCalculationsScripts(importData.calculations_scripts);
        await this.importRoles(importData.roles);
        await this.importOrgstrs(importData.orgstrs);
        await this.importUsers(importData.users);
        await this.importTemplateProcesses(importData.template_processes);
        await this.setupSubpSubrImportRules(importData);
        await this.importEvents(importData.events);
        await this.remapTTaskEvents(this.remap.ttask_event);
    }
}
