// @ts-nocheck
// @ts-nocheck
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";

/**
 * Scan DB for know bugs and clear them.
 *
 */
export class TasDBIntegrity {
    constructor(connection) {
        if (!connection) {
            throw new InternalException(
                "You should use transaction and share connection.",
                "MISSING_TRANSACTION_CONNECTION",
            );
        }

        this.connection = connection;
    }

    fixAll() {
        return this.deleteEventWaitLeaks().then(() => this.deleteTGraphLeaks());
    }

    /**
     * Scan RULE_DEFINITION.RDEF_VALUE where RDEF_TYPE = "EVENTW" there can be invalid RDEF_VALUE containing missing TTASK_ID.
     */
    async deleteEventWaitLeaks() {
        // Get all waits
        const rows = await this.connection
            .select("RDEF_ID")
            .from("RULE_DEFINITION")
            .where("RDEF_TYPE", "EVENTW") // is event wait.
            .whereRaw(
                `${globalThis.orm.db.substr(`"RDEF_VALUE"`, 7, 100)}${globalThis.dynamicConfig.db.client === "postgresql" ? "::integer" : ""} NOT IN (SELECT "TTASK_ID" FROM "TEMPLATE_TASKS")`,
            ); // $EVEW_1064 -> 1064 not in tempalte tasks. Is missing !

        if (Array.isArray(rows) && rows.length > 0) {
            const rdefRepo = globalThis.orm.repo("rule");
            for (const rule of rows) {
                await rdefRepo.deleteTRule(rule.RDEF_ID);
            }
        }
    }

    /**
     * Scan TEMPLATE_GRAPH TTASK_ID and delete if ttask not exists.
     */
    deleteTGraphLeaks() {
        return this.connection
            .select()
            .from("TEMPLATE_GRAPH")
            .whereNotIn("TTASK_ID", function () {
                return this.select("TTASK_ID").from("TEMPLATE_TASKS");
            })
            .delete();
    }
}
