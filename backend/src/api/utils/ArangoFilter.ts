// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { aql } from "arangojs";
import { UtilsService } from "../services/UtilsService";
import { UserException } from "../../utils/errorHandling/exceptions/userException";

import { Filter } from "../orm/Filter";

export class ArangoFilter {
    constructor(Parser = Filter) {
        this.parser = new Parser();
    }

    filtersToArangoFiltersArray(rawFilters) {
        // 1. parse Raw String;
        const parsedFilters = this.parse(rawFilters);

        // 2. separate Expressions and Conjunctions
        const expressions = parsedFilters.filter(
            (filter) => filter.type === "expression",
        );
        const conjunctions = parsedFilters.filter(
            (filter) => filter.type === "conjunction",
        );

        // 3. format Filters To Suitable ArrangoString
        const formattedFilters = [];
        expressions.forEach((filter) => {
            const filters = this.keyToArrangoFilter(
                filter.key,
                filter.value,
                filter.operator,
                conjunctions,
            );
            formattedFilters.push(...filters);
        });

        if (formattedFilters.length <= 0) {
            return [];
        }
        return this.joinFilters(formattedFilters, conjunctions);
    }

    parse(rawFilters) {
        // workaround to allow backslash in value
        if (rawFilters === undefined) {
            return [];
        }
        const modifiedForParser = rawFilters.replace("\\", "\\\\");

        const parsedFilters = this.parser.parseRawFilter(modifiedForParser);
        if (parsedFilters === null) {
            return [];
        }
        return parsedFilters;
    }

    adjustAqlForOperator(operator, key, value) {
        switch (operator) {
            case "<in>":
                return aql`row.${key} == ${value}`;
            case "<nin>":
                return aql`row.${key} != ${value}`;
            case "<ain>":
                return aql`${value} IN row.${key}`;
            default:
                return aql`row.${key} == ${value}`;
        }
    }

    keyToArrangoFilter(key, value, operator, conjunctions = []) {
        // Common to [logs, calculation_logs]
        if (
            key === "cron_run_id" ||
            key === "xpi_id" ||
            key === "iproc_id" ||
            ((key === "category" || key === "level") && !value.includes(","))
        ) {
            value = `${value}`.replace(/%/g, "");
            if (!UtilsService.isNumericString(value)) {
                throw new UserException(
                    `Filter value for ${key} is not valid. Number is required. But is ${value}`,
                );
            }
            const parsedValue = parseInt(value, 10);

            if (key === "category" || key === "level") {
                return [this.adjustAqlForOperator(operator, key, parsedValue)];
            }

            if (key === "cron_run_id") {
                return [aql`row.meta.${key} == ${parsedValue}`];
            }

            return [aql`row.${key} == ${parsedValue}`];
        }
        if (key === "level" || key === "category") {
            const values = value.split(",");
            const filters = [];

            if (!values.every(UtilsService.isNumericString)) {
                throw new UserException(
                    `Filter value for ${key} is not valid. Comma separated numbers are required`,
                );
            }
            const parsedValues = values.map((val) => parseInt(val, 10));

            parsedValues.forEach((val) => {
                filters.push(this.adjustAqlForOperator(operator, key, val));
            });

            if (filters.length > 0) {
                const conjunctiveAccumFilterOperators = [
                    "<nin>",
                    "<nlike>",
                    "<isnn>",
                ];
                const joinedAql = filters.reduce(
                    (accumFilter, filter, index) => {
                        if (index > 0) {
                            return _.includes(
                                conjunctiveAccumFilterOperators,
                                operator,
                            )
                                ? aql`${accumFilter} AND ${filter}`
                                : aql`${accumFilter} OR ${filter}`;
                        }
                        return aql`${accumFilter} ${filter}`;
                    },
                    aql``,
                );
                return [joinedAql];
            }
            return [];
        }
        if (key === "time" || key === "insert_date") {
            const fullDate = new Date(value.replace(/%/g, ""));

            if (!UtilsService.validateDate(fullDate)) {
                throw new UserException(
                    `Filter value for ${key} is not valid. Date format required.`,
                );
            }

            const year = fullDate.getFullYear();
            const month = fullDate.getMonth();
            const date = fullDate.getDate();
            const hours = fullDate.getHours();

            const resultFilter = [];
            resultFilter.push(aql`DATE_YEAR(row.time) == ${year}`);
            resultFilter.push(aql`DATE_MONTH(row.time) == ${month + 1}`);
            resultFilter.push(aql`DATE_DAY(row.time) == ${date}`);

            // Midnight == filter to all day
            if (hours !== 0) {
                resultFilter.push(
                    aql`DATE_HOUR(row.time) == ${fullDate.getUTCHours()}`,
                );
            }

            for (let i = 1; i < resultFilter.length; i += 1) {
                conjunctions.push({ type: "conjunction", value: "<and>" });
            }

            return resultFilter;
        }
        if (key === "message") {
            return [aql`row.message like ${this.escapeLikePattern(value)}`];
        }

        // Specific to [calculation_logs]
        if (key === "js_var_value_line") {
            return [
                aql`row.meta.errLine like ${this.escapeLikePattern(value)}`,
            ];
        }
        if (key === "js_var_name_line") {
            return [aql`row.meta.js like ${this.escapeLikePattern(value)}`];
        }
        if (key === "itask_id") {
            const potentialInt = value.replace(/%/g, "");
            if (!UtilsService.isNumericString(potentialInt)) {
                throw new UserException(
                    `Filter value for ${key} is not valid. Number is required`,
                );
            }
            const parsedValue = parseInt(potentialInt, 10);
            return [aql`row.meta.${key} == ${parsedValue}`];
        }
        if (key === "trace_id") {
            return [aql`${value.replace(/%/g, "")} IN row.${key}`];
        }

        globalThis.tasLogger.warning(
            `Column '${key}' is not implemented in Arango inline filter.`,
        );
        return [];
    }

    // expecting pattern like %stringYouAreSearchingFor%
    escapeLikePattern(string) {
        const specialChars = ["_", "%", "\\\\"];
        const regexGen = Object.values(specialChars).join("|");
        const searchString = string.substring(1, string.length - 1);

        const result = searchString.replace(
            new RegExp(regexGen, "g"),
            (char) => `\\${char}`,
        );

        return `%${result}%`;
    }

    joinFilters(filters, conjunctions = []) {
        let checkedConjunctions = conjunctions;
        if (!conjunctions.length) {
            checkedConjunctions = _.fill([], "<and>", 0, filters.length - 1);
        }

        if (filters.length - 1 !== conjunctions.length) {
            throw new Error("Insufficent number of conjuctions or filters");
        }

        let joinedAql = aql`${filters[0]}`;
        const result = [];
        for (let i = 1; i < filters.length; i += 1) {
            if (checkedConjunctions[i - 1].value === "<or>") {
                joinedAql = aql`${joinedAql} OR ${filters[i]}`;
            }
            if (checkedConjunctions[i - 1].value === "<and>") {
                result.push(joinedAql);
                joinedAql = aql`${filters[i]}`;
            }
        }

        result.push(joinedAql);
        return result;
    }
}
