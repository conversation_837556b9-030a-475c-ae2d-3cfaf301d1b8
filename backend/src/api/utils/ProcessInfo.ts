// @ts-nocheck
// @ts-nocheck
import { Workflow } from "../workflow/Workflow";
import * as ROLE from "../orm/entity/const/roleConst";
import * as USER from "../orm/entity/const/userConst";
import { WorkflowProcess } from "../workflow/WorkflowProcess";
import { LogCategory } from "../../utils/logger/logConsts";

const TASK_TO_PULL = "pull";

export class ProcessInfo {
    constructor(connection) {
        this.connection = connection;
    }

    /**
     * Creates a report of Users, who are able to activate a waiting Event in the process
     * by meeting the ORG_ID, ROLE_ID and HIERARCHY conditions of waiting Event Tasks
     *
     * - The report does not include the source of the access right
     **/

    async handEventMap(user, iProcId, simpleView, archived = false) {
        // Create a WorkFlow with a user to access its methods
        return globalThis.tasLogger.runTask(async () => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_WORKFLOW,
            );
            const workflow = new Workflow(globalThis.orm, user);
            const workflowProcess = new WorkflowProcess(workflow);

            // Get Process info for later use
            const process = await globalThis.orm
                .repo(archived ? "archivedProcess" : "process", this.connection)
                .get(iProcId);
            const processVersion =
                await workflowProcess.processVersion(process);

            // Get all waiting Tasks
            const waitingTasks = await globalThis.orm
                .repo(
                    archived ? "archivedInstanceTask" : "instanceTask",
                    this.connection,
                )
                .getWaitingTasks(iProcId)
                .collectAll();

            // Fill the VariableList for later use
            // Variables
            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = globalThis.orm
                .repo(
                    archived ? "archivedVariable" : "variable",
                    this.connection,
                )
                .prepareCollectionsForVariableList(iProcId);
            workflow.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );

            // Variables mapping
            await workflow.context.variableList.setVariableUsages(
                await globalThis.orm
                    .repo("templateTaskVarUsage", this.connection)
                    .getUsagesForTemplateProcess(
                        process.TPROC_ID,
                        process.IPROC_ID,
                        processVersion.TTASKVARUSG_VERSION || 0,
                    )
                    .collectAll(),
            );

            const possibleUsersCollections = [];
            for (const task of waitingTasks) {
                const possibleUsers = await workflow.getPossibleUsersCollection(
                    task,
                    process,
                );
                possibleUsersCollections.push([task, possibleUsers]);
            }

            // Placeholder columns
            const placeholderColumns = [
                globalThis.database.raw(`null as "TTASK_NAME"`),
                ...globalThis.dynamicConfig.langs.map((lang) =>
                    globalThis.database.raw(
                        `null as "TTASK_NAME_${lang.toUpperCase()}"`,
                    ),
                ),
                globalThis.database.raw(`null as "ITASK_ID"`),
                globalThis.database.raw(`null as "ITASK_NAME"`),
                globalThis.database.raw(`null as "ITASK_ASSESMENT_HIERARCHY"`),
                globalThis.database.raw(
                    `null as "ITASK_ASSESMENT_ORGSTR_CNST"`,
                ),
                globalThis.database.raw(
                    `null as "REFERENCE_USER_DISPLAY_NAME"`,
                ),
                globalThis.database.raw(`null as "REFERENCE_USER_ID"`),
                globalThis.database.raw(`null as "ITASK_ASSESMENT_ROLE_ID"`),
            ];

            // HR users for this template/header/process able to run any event
            // @t3b-1955 HR práva ve WorkFlow
            const hrUsersConnection = this.connection
                .select([
                    "U.USER_ID",
                    "U.USER_NAME",
                    "U.USER_DISPLAY_NAME",
                    "HR_USERS.ITASK_ASSESMENT_ROLE_ID",
                ])
                .from((builder) => {
                    builder
                        // Users by Template Roles
                        .select([
                            "UR.USER_ID",
                            "TP.TPROC_HR_ROLE_ID as ITASK_ASSESMENT_ROLE_ID",
                        ])
                        .from("TEMPLATE_PROCESSES as TP")
                        .innerJoin(
                            "ROLES as R",
                            "R.ROLE_ID",
                            "TP.TPROC_HR_ROLE_ID",
                        )
                        .innerJoin(
                            "USER_ROLES as UR",
                            "UR.ROLE_ID",
                            "TP.TPROC_HR_ROLE_ID",
                        )
                        .where("TP.TPROC_ID", process.TPROC_ID)
                        // Users by Header Role
                        .union((unionBuilder) => {
                            unionBuilder
                                .select([
                                    "UR.USER_ID",
                                    "H.HEADER_HR_ROLE_ID as ITASK_ASSESMENT_ROLE_ID",
                                ])
                                .from("HEADERS as H")
                                .innerJoin(
                                    "USER_ROLES as UR",
                                    "UR.ROLE_ID",
                                    "H.HEADER_HR_ROLE_ID",
                                )
                                .where("H.TPROC_ID", process.TPROC_ID);
                        })
                        // Users by Process Role
                        .union((unionBuilder) => {
                            unionBuilder
                                .select([
                                    "UR.USER_ID",
                                    "IP.IPROC_HR_ROLE_ID as ITASK_ASSESMENT_ROLE_ID",
                                ])
                                .from(
                                    `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
                                )
                                .innerJoin(
                                    "ROLES as R",
                                    "R.ROLE_ID",
                                    "IP.IPROC_HR_ROLE_ID",
                                )
                                .innerJoin(
                                    "USER_ROLES as UR",
                                    "UR.ROLE_ID",
                                    "IP.IPROC_HR_ROLE_ID",
                                )
                                .where("IP.IPROC_ID", iProcId);
                        })
                        .as("HR_USERS");
                })
                .innerJoin("USERS as U", "U.USER_ID", "HR_USERS.USER_ID");

            const connection = this.connection
                .select([
                    globalThis.database.raw(`null as "USER_ID"`),
                    globalThis.database.raw(`null as "USER_DISPLAY_NAME"`),
                    globalThis.database.raw(`null as "USER_NAME"`),
                    ...placeholderColumns,
                ])
                .from("USERS");

            // Generate union queries from all the possible tasks solvers
            possibleUsersCollections.forEach(
                ([
                    task,
                    {
                        knex,
                        referenceUser: {
                            USER_ID: userId,
                            USER_DISPLAY_NAME: userDisplayName,
                        },
                    },
                ]) => {
                    // Nested union queries cannot have an 'order by' clause - and there is no use for it anyway
                    knex._clearGrouping("order");
                    // Reset select columns
                    knex.clearSelect();

                    // Define Task info columns
                    const taskColumns = [
                        globalThis.database.raw(
                            `"${task._raw.TTASK_NAME}" as "TTASK_NAME"`,
                        ),
                        ...globalThis.dynamicConfig.langs.map((lang) => {
                            const attributeName = `"TTASK_NAME_${lang.toUpperCase()}"`;
                            const attributeValue = task._raw[attributeName];
                            return globalThis.database.raw(
                                `${attributeValue ? `'${attributeValue}'` : null} as "${attributeName}"`,
                            );
                        }),
                        globalThis.database.raw(
                            `"${task.ITASK_ID}" as "ITASK_ID"`,
                        ),
                        globalThis.database.raw(
                            `"'{task.ITASK_NAME}' as "ITASK_NAME"`,
                        ),
                        globalThis.database.raw(
                            `'${task.ITASK_ASSESMENT_HIERARCHY}' as "ITASK_ASSESMENT_HIERARCHY"`,
                        ),
                        globalThis.database.raw(
                            `"${task.ITASK_ASSESMENT_ORGSTR_CNST}" as "ITASK_ASSESMENT_ORGSTR_CNST"`,
                        ),
                        globalThis.database.raw(
                            `'${userDisplayName}' as "REFERENCE_USER_DISPLAY_NAME"`,
                        ),
                        globalThis.database.raw(
                            `${userId} as "REFERENCE_USER_ID"`,
                        ),
                    ];

                    // Define User info columns
                    const userColumns = [
                        "U.USER_ID",
                        "U.USER_DISPLAY_NAME",
                        "U.USER_NAME",
                    ];

                    // Columns to be selected
                    const columns = [
                        ...userColumns,
                        ...taskColumns,
                        globalThis.database.raw(
                            `${task.ITASK_ASSESMENT_ROLE_ID} as "ITASK_ASSESMENT_ROLE_ID"`,
                        ),
                    ];
                    knex.select(columns)
                        // Need to include HR users in every task
                        .union((builder) => {
                            builder
                                .select([
                                    "HR_USERS.USER_ID",
                                    "HR_USERS.USER_DISPLAY_NAME",
                                    "HR_USERS.USER_NAME",
                                    ...taskColumns,
                                    "HR_USERS.ITASK_ASSESMENT_ROLE_ID",
                                ])
                                .from("HR_USERS");
                        });

                    // Union query to the top
                    connection.union(knex);
                },
            );

            // Simple columns
            const simpleViewColumns = [
                "REPORT.USER_DISPLAY_NAME",
                "REPORT.TTASK_NAME",
                ...globalThis.dynamicConfig.langs.map(
                    (lang) => `REPORT.TTASK_NAME_${lang.toUpperCase()}`,
                ),
                "REPORT.ITASK_ID",
            ];

            // Complex columns
            const complexViewColumns = [
                "REPORT.USER_ID",
                "REPORT.USER_DISPLAY_NAME",
                "REPORT.USER_NAME",
                "REPORT.REFERENCE_USER_DISPLAY_NAME",
                "REPORT.REFERENCE_USER_ID",
                "REPORT.TTASK_NAME",
                ...globalThis.dynamicConfig.langs.map(
                    (lang) => `REPORT.TTASK_NAME_${lang.toUpperCase()}`,
                ),
                "REPORT.ITASK_ID",
                "REPORT.ITASK_NAME",
                "REPORT.ITASK_ASSESMENT_HIERARCHY",
                "REPORT.ITASK_ASSESMENT_ROLE_ID",
                "REPORT.ITASK_ASSESMENT_ORGSTR_CNST",
                "R.ROLE_NAME",
                "OS.ORGSTR_NAME",
            ];

            // Determine columns by the view type
            const viewConnection = simpleView
                ? this.connection.distinct(simpleViewColumns)
                : this.connection.select(complexViewColumns);

            // Wrap the query and create a collection to enable filtering, ordering and totalCount
            const wrappedQuery = viewConnection
                .with("HR_USERS", hrUsersConnection)
                .from(connection.as("REPORT"))
                .leftJoin(
                    "ROLES as R",
                    "R.ROLE_ID",
                    "REPORT.ITASK_ASSESMENT_ROLE_ID",
                )
                .leftJoin(
                    "ORGANIZATION_STRUCTURE as OS",
                    "OS.ORGSTR_ID",
                    "REPORT.ITASK_ASSESMENT_ORGSTR_CNST",
                )
                .whereNotNull("REPORT.USER_ID");

            return globalThis.orm.collection("TemplateTask", wrappedQuery);
        });
    }

    /**
     * Creates a visibility report of a given Process
     * This includes all access rights, such as:
     * - Visibility by process
     * - Vysibility by roles
     * ... and others
     *
     * The result includes all Users, who are able to view the Process
     * as well as the source of the access right
     *
     * @param {number} iProcId
     * @returns {BaseCollection} collection
     */
    processRightsMap(iProcId, archived = false) {
        // TODO: limit by task type?
        // User columns
        const userColumns = ["U.USER_NAME", "U.USER_DISPLAY_NAME"];

        // Task columns
        const taskColumns = [
            "TT.TTASK_ID",
            "TT.TTASK_NAME",
            ...globalThis.dynamicConfig.langs.map(
                (lang) => `TT.TTASK_NAME_${lang.toUpperCase()}`,
            ),
        ];

        // All columns to be selected
        const columns = [
            ...userColumns,
            ...taskColumns,
            "REPORT.SOURCE_NAME",
            "REPORT.SOURCE_ID",
            "REPORT.SOURCE_TYPE",
        ];

        // Helper function for union selects, define only what is available in the current query
        // and the rest is filled with default values
        const unionColumns = ({
            userId = "U.USER_ID",
            name = "null",
            id = "null",
            type = "null",
        } = {}) => [
            `${userId} as USER_ID`,
            globalThis.database.raw(`${name} as "SOURCE_NAME"`),
            globalThis.database.raw(
                `${id}${globalThis.dynamicConfig.db.client === "postgresql" ? "::INTEGER" : ""} as "SOURCE_ID"`,
            ),
            globalThis.database.raw(
                `${type}${globalThis.dynamicConfig.db.client === "postgresql" ? "::TEXT" : ""} as "SOURCE_TYPE"`,
            ),
        ];

        const visibilityReportConnection = this.connection
            .select(columns)
            .from((builder) => {
                builder
                    // Instance Process Static Rights except Tasks to pull
                    .select(
                        unionColumns({
                            userId: "IPSR.USER_ID",
                            type: `'STATIC_RIGHT'`,
                            name: `"IPSR"."GROUP_NAME"`,
                        }),
                    )
                    .from(
                        `${archived ? "ARCH_INSTANCE_PROCESS_STATIC_RIGHTS" : "INSTANCE_PROCESS_STATIC_RIGHTS"} as IPSR`,
                    )
                    .whereNot("IPSR.GROUP_NAME", TASK_TO_PULL)
                    .where("IPSR.IPROC_ID", iProcId)
                    // Instance Process Static Rights Tasks to pull
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "IPSR.USER_ID",
                                    type: `'TASK_TO_PULL'`,
                                    name: `"IPSR"."GROUP_NAME"`,
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESS_STATIC_RIGHTS" : "INSTANCE_PROCESS_STATIC_RIGHTS"} as IPSR`,
                            )
                            .where("IPSR.GROUP_NAME", TASK_TO_PULL)
                            .where("IPSR.IPROC_ID", iProcId);
                    })
                    // Instance Process Dynamic Rights
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "IPDR.USER_ID",
                                    type: "'DYNAMIC_RIGHT'",
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESS_DYN_RIGHTS" : "INSTANCE_PROCESS_DYN_RIGHTS"} as IPDR`,
                            )
                            .where("IPDR.IPROC_ID", iProcId);
                    })
                    // Template by Organization Structure
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "URS.USER_ID",
                                    name: `"OS"."ORGSTR_NAME"`,
                                    id: `"TP"."TPROC_VIS_ORGSTR_ID"`,
                                    type: "'VIS_ORGSTR_ID'",
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
                            )
                            .innerJoin(
                                "TEMPLATE_PROCESSES as TP",
                                "TP.TPROC_ID",
                                "IP.TPROC_ID",
                            )
                            .leftJoin(
                                "ORGANIZATION_STRUCTURE as OS",
                                "OS.ORGSTR_ID",
                                "TP.TPROC_VIS_ORGSTR_ID",
                            )
                            .leftJoin(
                                "USER_ORGANIZATION_STRUCTURE as URS",
                                "URS.ORGSTR_ID",
                                "TP.TPROC_VIS_ORGSTR_ID",
                            )
                            .where("IP.IPROC_ID", iProcId);
                    })
                    // Template, Instance, Header by Role
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "UR.USER_ID",
                                    name: `"R"."ROLE_NAME"`,
                                    id: `"UR"."ROLE_ID"`,
                                    type: "'VIS_ROLE_ID'",
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
                            )
                            .innerJoin(
                                "HEADERS as HDR",
                                "HDR.HEADER_ID",
                                "IP.HEADER_ID",
                            )
                            .innerJoin(
                                "TEMPLATE_PROCESSES as TP",
                                "TP.TPROC_ID",
                                "IP.TPROC_ID",
                            )
                            .leftJoin("USER_ROLES as UR", (join) => {
                                join.on("UR.ROLE_ID", "IP.IPROC_VIS_ROLE_ID")
                                    .orOn("UR.ROLE_ID", "TP.TPROC_VIS_ROLE_ID")
                                    .orOn("UR.ROLE_ID", "HDR.HDR_VIS_ROLE_ID");
                            })
                            .leftJoin("ROLES as R", "R.ROLE_ID", "UR.ROLE_ID")
                            .where("IP.IPROC_ID", iProcId);
                    })
                    // Process owner
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "U.USER_ID",
                                    type: "'PROCESS_OWNER'",
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
                            )
                            .innerJoin(
                                "USERS as U",
                                "U.USER_ID",
                                "IP.IPROC_INST_OWNER_USER_ID",
                            )
                            .where("IP.IPROC_ID", iProcId);
                    })
                    // Task Solver
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "IT.ITASK_USER_ID",
                                    name: `"IT"."ITASK_NAME"`,
                                    id: `"IT"."ITASK_ID"`,
                                    type: "'TASK_SOLVER'",
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
                            )
                            .innerJoin(
                                `${archived ? "ARCH_INSTANCE_TASKS" : "INSTANCE_TASKS"} as IT`,
                                "IT.IPROC_ID",
                                "IP.IPROC_ID",
                            )
                            .whereNotNull("IT.ITASK_USER_ID")
                            .where("IP.IPROC_ID", iProcId);
                    })
                    // Task Assessment
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "IT.ITASK_ASSESMENT_USER_ID",
                                    name: `"IT"."ITASK_NAME"`,
                                    id: `"IT"."ITASK_ID"`,
                                    type: "'TASK_ASSESSMENT'",
                                }),
                            )
                            .from(
                                `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
                            )
                            .innerJoin(
                                `${archived ? "ARCH_INSTANCE_TASKS" : "INSTANCE_TASKS"} as IT`,
                                "IT.IPROC_ID",
                                "IP.IPROC_ID",
                            )
                            .whereNotNull("IT.ITASK_ASSESMENT_USER_ID")
                            .where("IP.IPROC_ID", iProcId);
                    })
                    // Inspector
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "UR.USER_ID",
                                    id: ROLE.INSPECTOR,
                                    name: "'$Inspector'",
                                    type: "'VIS_ROLE_ID'",
                                }),
                            )
                            .from("USER_ROLES as UR")
                            .where("ROLE_ID", ROLE.INSPECTOR);
                    })
                    // Global Supervisor
                    .union((query) => {
                        query
                            .select(
                                unionColumns({
                                    userId: "UR.USER_ID",
                                    id: ROLE.GLOBAL_SUPERVISOR,
                                    name: "'$GlobalSupervisor'",
                                    type: "'VIS_ROLE_ID'",
                                }),
                            )
                            .from("USER_ROLES as UR")
                            .where("ROLE_ID", ROLE.GLOBAL_SUPERVISOR);
                    })
                    .as("REPORT");
            })
            .innerJoin("USERS as U", "U.USER_ID", "REPORT.USER_ID")
            .leftJoin(
                `${archived ? "ARCH_INSTANCE_TASKS" : "INSTANCE_TASKS"} as IT`,
                (builder) => {
                    builder
                        .on("IT.ITASK_ID", "REPORT.SOURCE_ID")
                        .andOn((onBuilder) => {
                            onBuilder
                                .on(
                                    "REPORT.SOURCE_TYPE",
                                    globalThis.database.raw(
                                        "'TASK_ASSESSMENT'",
                                    ),
                                )
                                .orOn(
                                    "REPORT.SOURCE_TYPE",
                                    globalThis.database.raw("'TASK_SOLVER'"),
                                );
                        });
                },
            )
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .where("U.USER_STATUS", USER.STATUS_ACTIVE);

        return globalThis.orm.collection(
            "TemplateTask",
            visibilityReportConnection,
        );
    }
}
