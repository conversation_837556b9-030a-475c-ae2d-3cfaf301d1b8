// @ts-nocheck
// @ts-nocheck
import _ from "lodash";

export class InstanceProcessVariables {
    constructor(connection) {
        this.connection = connection;
    }

    async updateTvarAlias(tvarId) {
        const tvarRepo = globalThis.orm.repo(
            "templateVariable",
            this.connection,
        );

        const tvar = await tvarRepo.connection
            .select(
                "TVAR_ALIAS",
                "TVAR_ATTRIBUTE",
                "TPROC_ID",
                "TVAR_ID",
                "TVAR_TYPE",
                "TVAR_MULTI",
            )
            .from("TEMPLATE_VARIABLES")
            .where("TVAR_ID", tvarId);
        const alias = tvar[0].TVAR_ALIAS;
        const type = this.getPrimitiveType(tvar[0]);

        // Alias not defined or tvar type has changed
        if (!alias || alias.indexOf(type) === -1) {
            const usedAliases = await this.getUsedAliases(tvar[0].TPROC_ID);
            for (let i = 1; i < 230; i += 1) {
                const newAlias = `VAR_${type}_${i}`;
                if (usedAliases.indexOf(newAlias) === -1) {
                    return this.setTvarAlias(tvarId, alias, newAlias);
                }
            }
            globalThis.tasLogger.warning(
                `There is no available TVAR_ALIAS for Variable of type '${type}' in TEMPLATE_PROCESS '${tvar[0].TPROC_ID}'`,
            );
            return await tvarRepo.connection
                .select()
                .from("TEMPLATE_VARIABLES")
                .where("TVAR_ID", tvarId)
                .update({ TVAR_ALIAS: null });
        }
        return null;
    }

    setTvarAlias(tvarId, alias, newAlias) {
        return this.connection
            .raw(
                `
                UPDATE "INSTANCE_PROCESS_VARIABLES"
                SET "${newAlias}"   = NULL
                WHERE "IPROC_ID" IN (SELECT DISTINCT "IPROC_ID" FROM "INSTANCE_VARIABLES" WHERE "TVAR_ID" = ${tvarId})
            `,
            )
            .then(() =>
                this.saveValues(tvarId, alias, newAlias)
                    .then(() => {
                        const tvarRepo = globalThis.orm.repo(
                            "TemplateVariable",
                            this.connection,
                        );
                        return tvarRepo.connection
                            .select()
                            .from("TEMPLATE_VARIABLES")
                            .where("TVAR_ID", tvarId)
                            .update({ TVAR_ALIAS: newAlias });
                    })
                    .catch((err) => {
                        throw err;
                    }),
            )
            .catch((err) => {
                throw err;
            });
    }

    saveValues(tvarId, alias, newAlias) {
        return new Promise((resolve, reject) => {
            if (!tvarId || !newAlias || !alias) {
                return resolve();
            }

            if (newAlias.indexOf("N") !== -1 && alias.indexOf("T") !== -1) {
                return this.connection
                    .raw(
                        `
                    UPDATE "INSTANCE_PROCESS_VARIABLES"
                    SET "${newAlias}" =
                        (SELECT
                            CASE
                            WHEN LTRIM(RTRIM(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE("VAR_T_1", '0', ' '), '1', ' '), '2', ' '), '3', ' '), '4', ' '), '5', ' '), '6', ' '), '7', ' '), '8', ' '), '9', ' '))) IS NULL
                            THEN "VAR_T_1"${globalThis.dynamicConfig.db.client === "postgresql" ? "::numeric" : ""}
                            ELSE NULL
                            END
                        )
                    WHERE "IPROC_ID" IN
                      (SELECT "IP"."IPROC_ID"
                      FROM "TEMPLATE_VARIABLES" "TV"
                      LEFT JOIN "TEMPLATE_PROCESSES" "TP"
                      ON ("TV"."TPROC_ID" = "TP"."TPROC_ID")
                      LEFT JOIN "INSTANCE_PROCESSES" "IP"
                      ON ("TP"."TPROC_ID" = "IP"."TPROC_ID")
                      WHERE "TVAR_ID"   = ?)
                `,
                        [tvarId],
                    )
                    .then(() => resolve())
                    .catch((err) => reject(err));
            }
            if (newAlias.indexOf("T") !== -1 && alias.indexOf("N") !== -1) {
                return this.connection
                    .raw(
                        `
                    UPDATE "INSTANCE_PROCESS_VARIABLES"
                    SET "${newAlias}"     = ${alias}
                    WHERE "IPROC_ID" IN
                      (SELECT "IP"."IPROC_ID"
                      FROM "TEMPLATE_VARIABLES" "TV"
                      LEFT JOIN "TEMPLATE_PROCESSES" "TP"
                      ON ("TV"."TPROC_ID" = "TP"."TPROC_ID")
                      LEFT JOIN "INSTANCE_PROCESSES" "IP"
                      ON ("TP"."TPROC_ID" = "IP"."TPROC_ID")
                      WHERE "TVAR_ID"   = ?)
                `,
                        [tvarId],
                    )
                    .then(() => resolve())
                    .catch((err) => reject(err));
            }
            return resolve();
        }).catch((err) => {
            throw err;
        });
    }

    getUsedAliases(tprocId) {
        return this.connection
            .distinct("TVAR_ALIAS")
            .from("TEMPLATE_VARIABLES")
            .where("TPROC_ID", tprocId)
            .then((tvars) => {
                if (!Array.isArray(tvars) || tvars.length === 0) {
                    return [];
                }

                return _.map(tvars, "TVAR_ALIAS");
            });
    }

    getPrimitiveType(tvar) {
        if (tvar.TVAR_MULTI === "X") {
            return "T";
        }

        if (
            (tvar.TVAR_TYPE === "DL" && tvar.TVAR_ATTRIBUTE) ||
            tvar.TVAR_TYPE === "N" ||
            tvar.TVAR_TYPE === "LN"
        ) {
            return "N";
        }

        if (tvar.TVAR_TYPE === "D" || tvar.TVAR_TYPE === "LD") {
            return "D";
        }

        if (
            tvar.TVAR_TYPE === "T" ||
            tvar.TVAR_TYPE === "LT" ||
            tvar.TVAR_TYPE === "B" ||
            tvar.TVAR_TYPE === "DL" ||
            tvar.TVAR_TYPE === "DT"
        ) {
            return "T";
        }

        return "T";
    }
}
