// @ts-nocheck
import { Knex } from "knex";

export class DmsAccessLogger {
    public static consts = {
        VIEW_DELETED: "VIEW_DELETED",
        DELETE_FILE: "DELETE_FILE",
        VIEW_CURRENT: "VIEW_CURRENT",
        VIEW_PROCESS_FILES: "VIEW_PROCESS_FILES",
        VIEW_TASK_FILES: "VIEW_TASK_FILES",
        DOWNLOAD_FILE: "DOWNLOAD_FILE",
        UPLOAD_FILE: "UPLOAD_FILE",
        INDEX_DOCUMENT: "INDEX_DOCUMENT",
        ACCESS_LOG: "ACCESS_LOG",
        EMAIL_NOTIFICATION_ATTACHMENT: "EMAIL_NOTIFICATION_ATTACHMENT",
        EWS_COPY_ATTACHMENT: "EWS_COPY_ATTACHMENT",
        VIEW_ALL: "VIEW_ALL",
        DOCUMENT_SHREDED: "DOCUMENT_SHREDED",
    };

    constructor(connection: Knex | null = null) {
        this.start = process.hrtime();
        this.connection = connection;
    }

    /**
     * Log access for multiple users/files at once - much faster!
     *
     * @param {Array<Object>} objects - objects with ITASK_ID, IPROC_ID, DMSF_ID and USER_ID filled
     * @param {String} operation - name of operation being undertaken
     * @param {String} [status = OK]
     * @returns {Promise<any>}
     */
    batchLog(objects, operation, status = "OK") {
        const elapsed = process.hrtime(this.start)[1] / 1000000; // divide by a million to get nano to milli
        const statusMessage =
            status.length > 3900 ? status.substr(0, 3900) : status;
        const repo = globalThis.orm.repo("dmsFileAccessLog", this.connection);
        const date = new Date();

        // Add necessary information to the objects
        const filledObjects = objects.map((item) => {
            item.DMSFAL_OPERATION = operation;
            item.DATE_TIME = date;
            item.DMSFAL_STATUS = statusMessage;
            item.DMSFAL_DURATION = elapsed;
            return item;
        });

        return repo
            .bulkInsert(filledObjects)
            .then(() => {
                // Reset
                this.reset();
            })
            .catch((err) => {
                globalThis.tasLogger.error(err.message, err);
                this.reset();
            });
    }

    log(dmsfId, iprocId, itaskId, userId, operation, status = "OK") {
        const elapsed = process.hrtime(this.start)[1] / 1000000; // divide by a million to get nano to milli
        const statusMessage =
            status.length > 3900 ? status.substr(0, 3900) : status;

        const repo = globalThis.orm.repo("dmsFileAccessLog", this.connection);

        // solves freeze when non-existing file (undefined) is trying to be logged
        // don't log non-numbered files and non-null values
        if (isNaN(dmsfId) && dmsfId !== null) {
            this.reset();
            return false;
        }

        return repo
            .store(
                repo.getEntity({
                    ITASK_ID: itaskId,
                    IPROC_ID: iprocId,
                    DMSF_ID: dmsfId,
                    USER_ID: userId,
                    DMSFAL_OPERATION: operation,
                    DATE_TIME: new Date(),
                    DMSFAL_STATUS: statusMessage,
                    DMSFAL_DURATION: elapsed,
                }),
            )
            .then(() => {
                // Reset
                this.reset();
            })
            .catch((err) => {
                globalThis.tasLogger.error(err.message, {
                    err,
                });
                this.reset();
            });
    }

    reset() {
        this.start = process.hrtime();
    }
}
