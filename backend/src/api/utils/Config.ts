// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import EventEmitter from "events";
import { Config as ConfigEntity } from "../orm/entity/Config";

export class Config {
    constructor() {
        this.vars = {};
        this.rawData = [];

        this.events = new EventEmitter();
    }

    /**
     * Set source of config values. Use order of call to fallback. First has highest priority.
     * @param rawData (entities)
     */
    setSource(rawData) {
        if (Array.isArray(rawData) && rawData.length === 0) {
            return;
        }

        const newProps = {};
        if (_.isPlainObject(rawData)) {
            for (const key in rawData) {
                _.set(newProps, key, rawData[key]); // produces object from dot notaion => { a : { b : c }}
                // init only getter for migration or new non-db config
                if (!this.hasOwnProperty(key)) {
                    Object.defineProperty(this, key, {
                        get() {
                            return this.get(key);
                        },
                    });
                }
            }
        } else if (
            _.isArray(rawData) &&
            rawData.length &&
            rawData[0].VAR_NAME !== "undefined"
        ) {
            // accepts Config entities from db or array of pseudo Config object without values
            for (const key of rawData) {
                _.set(
                    newProps,
                    key.VAR_NAME,
                    key instanceof ConfigEntity
                        ? key.insecuredValue()
                        : key.VAR_DEFAULT,
                ); // produces object from dot notaion => { a : { b : c }}
                const firstProp = key.VAR_NAME.split(".")[0];
                if (!this.hasOwnProperty(firstProp)) {
                    Object.defineProperty(this, firstProp, {
                        get() {
                            return this.get(firstProp);
                        },
                    });
                }
            }
        } else {
            // biome-ignore lint/suspicious/noConsole: <explanation>
            console.log(
                `Unsupported config data raw format: ${JSON.stringify(rawData).substring(256)}`,
            );
            return;
        }
        this.rawData.push(rawData);

        /*
        Possible situations:
        1) new db value (immediate used while migrating)
        2) value (in file local.js and in db paralel)
        3) migrated value (in file local.js and in db) while migrating
        4) deleted dbdata
        */
        this.vars = _.merge(newProps, this.vars);
    }

    /**
     * Get value of config name. You can use dot notation (tas.dms.measure).
     * Be aware of exposed passwords when sending to gui
     * @param varName
     * @param throwError If true throws error instead of null on non-existing variable
     * @return {*}
     */
    get(varName, throwError = true) {
        if (_.has(this.vars, varName)) {
            // first usually retrieves DB value
            return _.get(this.vars, varName);
        }
        if (!throwError) {
            return null;
        }
        throw new Error(`Not defined Config var ${varName}.`);
    }

    /**
     * Set new value to emit event.
     * @param varName
     * @param varValue
     * @return {*|*}
     */
    set(varName, varValue) {
        this.events.emit(`change-${varName}`, varValue);
        // Only in the TAS process and if enabled
        return _.set(this.vars, varName, varValue);
    }

    /**
     * Pick all settings
     * Be aware of exposed passwords when sending to gui
     * @param pathPrefix Prefix for source of path
     */
    pick(arr, pathPrefix = "", throwError = true) {
        const result = {};
        for (const key of arr) {
            _.set(result, key, this.get(pathPrefix + key, throwError));
        }
        return result;
    }
}
