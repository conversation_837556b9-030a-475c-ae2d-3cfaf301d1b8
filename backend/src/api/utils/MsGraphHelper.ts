import { <PERSON><PERSON> } from "knex";
import { Ms<PERSON><PERSON>hClient, MsGraphCredentials } from "../lib/MsGraph";

export interface IMsGraphConfig {
    amountOfEmailByRun?: number;
    items: {
        auth: MsGraphCredentials;
        folders: {
            in: {
                id: string;
            };
            out: {
                id: string;
            };
        };
        process: {
            user_id: number;
            tproc_id: number;
            header_id: number;
            tproc_version: number;
        };
        config?: {
            setEmailProcessedOnError: boolean;
            errorEmailAddress: string;
        };
        ignoreAttachmentErrors: boolean;
        ignoreVariablesUpdateAndUseDataHolder?: boolean;
        useEmailObjectInDataHolder?: boolean;
        mapping: Record<
            string,
            | string
            | {
                  value: string;
                  option: string;
                  isConstant?: boolean;
              }
        >;
    }[];
}
export class MsGraphHelper {
    async getMsGraphClients(): Promise<MsGraphClient[]> {
        const config = await this.getCurrentConfiguration();

        const clients: MsGraphClient[] = [];
        config.items.forEach((item) =>
            clients.push(new MsGraphClient(item.auth)),
        );

        return clients;
    }

    async getMsGraphClientWithIndex(index: number): Promise<MsGraphClient> {
        const clients = await this.getMsGraphClients();

        const client = clients[index];

        if (!client) {
            throw new Error(`Client with index ${index} not found`);
        }

        return client;
    }

    private async getCurrentConfiguration(
        connection?: Knex,
    ): Promise<IMsGraphConfig> {
        const repo = globalThis.orm.repo("cron", connection);
        const { CRON_PARAMETERS } = await repo.getByName(
            "MsGraphCreateProcessesFromMailCron",
        );
        return JSON.parse(CRON_PARAMETERS);
    }
}
