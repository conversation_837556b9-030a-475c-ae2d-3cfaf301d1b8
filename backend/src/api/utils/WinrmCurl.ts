// @ts-nocheck
// @ts-nocheck
import fs from "fs";

import xml2jsparser from "xml2js";
import { UtilsService } from "../services/UtilsService";
import { JsonParser } from "./JsonParser";

const STATE_DONE =
    "http://schemas.microsoft.com/wbem/wsman/1/windows/shell/CommandState/Done";

const sleep = async (ms: number) =>
    await new Promise((resolve) => setTimeout(resolve, ms));

export class WinrmCurl {
    constructor(options) {
        this.host = UtilsService.quoteExecParam(options.host);
        this.auth = UtilsService.quoteExecParam(options.credentials);
        this.authMethod = UtilsService.quoteExecParam(options.authMethod);
    }

    parseXml(xml) {
        return new Promise((resolve, reject) => {
            xml2jsparser.parseString(xml, (err, result) => {
                if (err) {
                    return reject(err);
                }

                return resolve(result);
            });
        });
    }

    async callCurl(envelope) {
        const tmpFile = await UtilsService.tmpFile({ discardDescriptor: true });
        await fs.promises.writeFile(tmpFile.path, envelope, "utf-8");
        const command = `curl --header "Content-Type: application/soap+xml;charset=UTF-8" ${this.host} -v --${this.authMethod} -u ${this.auth} -d @${tmpFile.path}`;
        globalThis.tasLogger.info("Last WinRM call.", {
            command,
        });
        const responseXml = await UtilsService.exec(command, 5000); // Parse request, get ShellId
        return await this.parseXml(responseXml);
    }

    async doCreateShell() {
        const envelope = `<?xml version='1.0'?>
<s:Envelope xmlns:s='http://www.w3.org/2003/05/soap-envelope' xmlns:wsa='http://schemas.xmlsoap.org/ws/2004/08/addressing' xmlns:wsman='http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd' xmlns:p='http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd' xmlns:rsp='http://schemas.microsoft.com/wbem/wsman/1/windows/shell'>
    <s:Header>
        <wsa:To>http://windows-host:5985/wsman</wsa:To>
        <wsman:ResourceURI mustUnderstand='true'>http://schemas.microsoft.com/wbem/wsman/1/windows/shell/cmd</wsman:ResourceURI>
        <wsa:ReplyTo>
            <wsa:Address mustUnderstand='true'>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:Address>
        </wsa:ReplyTo>
        <wsman:MaxEnvelopeSize mustUnderstand='true'>153600</wsman:MaxEnvelopeSize>
        <wsa:MessageID>uuid:6ba7b811-9dad-11d1-80b4-00c04fd430c8</wsa:MessageID>
        <wsman:Locale mustUnderstand='false' xml:lang='en-US'/>
        <wsman:OperationTimeout>PT60S</wsman:OperationTimeout>
        <wsa:Action mustUnderstand='true'>http://schemas.xmlsoap.org/ws/2004/09/transfer/Create</wsa:Action>
        <wsman:OptionSet>
            <wsman:Option Name='WINRS_NOPROFILE'>FALSE</wsman:Option>
            <wsman:Option Name='WINRS_CODEPAGE'>437</wsman:Option>
        </wsman:OptionSet>
    </s:Header>
    <s:Body>
        <rsp:Shell>
            <rsp:InputStreams>stdin</rsp:InputStreams>
            <rsp:OutputStreams>stderr stdout</rsp:OutputStreams>
        </rsp:Shell>
    </s:Body>
</s:Envelope>`;

        const responseJson = await this.callCurl(envelope);

        if (responseJson["s:Envelope"]["s:Body"][0]["s:Fault"]) {
            throw new Error(
                responseJson["s:Envelope"]["s:Body"][0]["s:Fault"][0][
                    "s:Code"
                ][0]["s:Subcode"][0]["s:Value"][0],
            );
        }
        return responseJson["s:Envelope"]["s:Body"][0]["rsp:Shell"][0][
            "rsp:ShellId"
        ][0];
    }

    async doExecuteCommand(shellId, cmd) {
        const envelope = `<?xml version='1.0'?>
        <s:Envelope xmlns:s='http://www.w3.org/2003/05/soap-envelope' xmlns:wsa='http://schemas.xmlsoap.org/ws/2004/08/addressing' xmlns:wsman='http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd' xmlns:p='http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd' xmlns:rsp='http://schemas.microsoft.com/wbem/wsman/1/windows/shell'>
            <s:Header>
                <wsa:To>http://windows-host:5985/wsman</wsa:To>
                <wsman:ResourceURI mustUnderstand='true'>http://schemas.microsoft.com/wbem/wsman/1/windows/shell/cmd</wsman:ResourceURI>
                <wsa:ReplyTo>
                    <wsa:Address mustUnderstand='true'>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:Address>
                </wsa:ReplyTo>
                <wsman:MaxEnvelopeSize mustUnderstand='true'>153600</wsman:MaxEnvelopeSize>
                <wsa:MessageID>uuid:6ba7b811-9dad-11d1-80b4-00c04fd430c8</wsa:MessageID>
                <wsman:Locale mustUnderstand='false' xml:lang='en-US'/>
                <wsman:OperationTimeout>PT60S</wsman:OperationTimeout>
                <wsa:Action mustUnderstand='true'>http://schemas.microsoft.com/wbem/wsman/1/windows/shell/Command</wsa:Action>
                <wsman:SelectorSet>
                    <wsman:Selector Name='ShellId'>{ShellId}</wsman:Selector>
                </wsman:SelectorSet>
                <wsman:OptionSet>
                    <wsman:Option Name='WINRS_CONSOLEMODE_STDIN'>TRUE</wsman:Option>
                    <wsman:Option Name='WINRS_SKIP_CMD_SHELL'>FALSE</wsman:Option>
                </wsman:OptionSet>
            </s:Header>
            <s:Body>
                <rsp:CommandLine>
                    <rsp:Command>{Command}</rsp:Command>
                </rsp:CommandLine>
            </s:Body>
        </s:Envelope>`
            .replace("{ShellId}", shellId)
            .replace("{Command}", cmd);

        const responseJson = await this.callCurl(envelope);

        if (responseJson["s:Envelope"]["s:Body"][0]["s:Fault"]) {
            throw new Error(
                responseJson["s:Envelope"]["s:Body"][0]["s:Fault"][0][
                    "s:Code"
                ][0]["s:Subcode"][0]["s:Value"][0],
            );
        }
        return responseJson["s:Envelope"]["s:Body"][0][
            "rsp:CommandResponse"
        ][0]["rsp:CommandId"][0];
    }

    async doReceiveOutput(
        shellId,
        commandId,
        stdReadInterval = 1000,
        stdReadIterations = 5,
        std = { out: "", err: "" },
    ) {
        const envelope = `<?xml version='1.0'?>
                <s:Envelope xmlns:s='http://www.w3.org/2003/05/soap-envelope' xmlns:wsa='http://schemas.xmlsoap.org/ws/2004/08/addressing' xmlns:wsman='http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd' xmlns:p='http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd' xmlns:rsp='http://schemas.microsoft.com/wbem/wsman/1/windows/shell'>
                    <s:Header>
                        <wsa:To>http://windows-host:5985/wsman</wsa:To>
                        <wsman:ResourceURI mustUnderstand='true'>http://schemas.microsoft.com/wbem/wsman/1/windows/shell/cmd</wsman:ResourceURI>
                        <wsa:ReplyTo>
                            <wsa:Address mustUnderstand='true'>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:Address>
                        </wsa:ReplyTo>
                        <wsman:MaxEnvelopeSize mustUnderstand='true'>153600</wsman:MaxEnvelopeSize>
                        <wsa:MessageID>uuid:6ba7b811-9dad-11d1-80b4-00c04fd430c8</wsa:MessageID>
                        <wsman:Locale mustUnderstand='false' xml:lang='en-US'/>
                        <wsman:OperationTimeout>PT60S</wsman:OperationTimeout>
                        <wsa:Action mustUnderstand='true'>http://schemas.microsoft.com/wbem/wsman/1/windows/shell/Receive</wsa:Action>
                        <wsman:SelectorSet>
                            <wsman:Selector Name='ShellId'>{ShellId}</wsman:Selector>
                        </wsman:SelectorSet>
                    </s:Header>
                    <s:Body>
                        <rsp:Receive>
                            <rsp:DesiredStream CommandId='{CommandId}'>stdout stderr</rsp:DesiredStream>
                        </rsp:Receive>
                    </s:Body>
                </s:Envelope>`
            .replace("{ShellId}", shellId)
            .replace("{CommandId}", commandId);

        const responseJson = await this.callCurl(envelope);

        const CommandState =
            responseJson["s:Envelope"]["s:Body"][0]["rsp:ReceiveResponse"][0][
                "rsp:CommandState"
            ][0];
        const commandState = CommandState.$.State;

        if (responseJson["s:Envelope"]["s:Body"][0]["s:Fault"]) {
            return new Error(
                responseJson["s:Envelope"]["s:Body"][0]["s:Fault"][0][
                    "s:Code"
                ][0]["s:Subcode"][0]["s:Value"][0],
            );
        }
        let successOutput = "";
        let failedOutput = "";
        if (
            responseJson["s:Envelope"]["s:Body"][0]["rsp:ReceiveResponse"][0][
                "rsp:Stream"
            ]
        ) {
            for (const stream of responseJson["s:Envelope"]["s:Body"][0][
                "rsp:ReceiveResponse"
            ][0]["rsp:Stream"]) {
                if (
                    stream.$.Name === "stdout" &&
                    !stream.$.hasOwnProperty("End")
                ) {
                    successOutput += new Buffer(stream._, "base64").toString(
                        "ascii",
                    );
                }
                if (
                    stream.$.Name === "stderr" &&
                    !stream.$.hasOwnProperty("End")
                ) {
                    failedOutput += new Buffer(stream._, "base64").toString(
                        "ascii",
                    );
                }
            }
        }

        std.out += successOutput.trim();
        std.err += failedOutput.trim();

        if (commandState !== STATE_DONE && stdReadIterations > 0) {
            await sleep(stdReadInterval);
            return this.doReceiveOutput(
                shellId,
                commandId,
                stdReadInterval,
                stdReadIterations - 1,
                std,
            );
        }

        std.state = commandState;
        std.exitCode = JsonParser.getProperty(
            "s:Envelope.s:Body.0.rsp:ReceiveResponse.0.rsp:CommandState.0.rsp:ExitCode.0",
            responseJson,
        );

        return std;
    }

    async doDeleteShell(shellId) {
        const envelope = `<?xml version='1.0'?>
        <s:Envelope xmlns:s='http://www.w3.org/2003/05/soap-envelope' xmlns:wsa='http://schemas.xmlsoap.org/ws/2004/08/addressing' xmlns:wsman='http://schemas.dmtf.org/wbem/wsman/1/wsman.xsd' xmlns:p='http://schemas.microsoft.com/wbem/wsman/1/wsman.xsd' xmlns:rsp='http://schemas.microsoft.com/wbem/wsman/1/windows/shell'>
            <s:Header>
                <wsa:To>http://windows-host:5985/wsman</wsa:To>
                <wsman:ResourceURI mustUnderstand='true'>http://schemas.microsoft.com/wbem/wsman/1/windows/shell/cmd</wsman:ResourceURI>
                <wsa:ReplyTo>
                    <wsa:Address mustUnderstand='true'>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:Address>
                </wsa:ReplyTo>
                <wsman:MaxEnvelopeSize mustUnderstand='true'>153600</wsman:MaxEnvelopeSize>
                <wsa:MessageID>uuid:6ba7b811-9dad-11d1-80b4-00c04fd430c8</wsa:MessageID>
                <wsman:Locale mustUnderstand='false' xml:lang='en-US'/>
                <wsman:OperationTimeout>PT60S</wsman:OperationTimeout>
                <wsa:Action mustUnderstand='true'>http://schemas.xmlsoap.org/ws/2004/09/transfer/Delete</wsa:Action>
                <wsman:SelectorSet>
                    <wsman:Selector Name='ShellId'>{ShellId}</wsman:Selector>
                </wsman:SelectorSet>
            </s:Header>
            <s:Body/>
        </s:Envelope>`.replace("{ShellId}", shellId);

        const responseJson = await this.callCurl(envelope);

        if (responseJson["s:Envelope"]["s:Body"][0]["s:Fault"]) {
            throw new Error(
                responseJson["s:Envelope"]["s:Body"][0]["s:Fault"][0][
                    "s:Code"
                ][0]["s:Subcode"][0]["s:Value"][0],
            );
        }
    }

    async runCommand(command, stdReadInterval, stdReadIterations) {
        const shellId = await this.doCreateShell();
        const commandId = await this.doExecuteCommand(shellId, command);
        const output = await this.doReceiveOutput(
            shellId,
            commandId,
            stdReadInterval,
            stdReadIterations,
        );
        await this.doDeleteShell(shellId);

        return output;
    }
}
