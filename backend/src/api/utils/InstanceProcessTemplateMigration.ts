// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { TaskVersioning } from "../versioning/TaskVersioning";
import { VariableVersioning } from "../versioning/VariableVersioning";

class InstanceProcessTemplateMigration {
    /**
     * Get all instances from 1 template
     * @param {number} tProcId
     * @return {*}
     */
    async getInstancesByTemplate(tProcId) {
        return await globalThis.orm
            .repo("process")
            .getByTemplate(tProcId)
            .collectAll();
    }

    async getActiveInstancesByTemplate(
        tProcId,
        columns = ["IPROC_ID"],
        conn = globalThis.database,
    ) {
        return await conn("INSTANCE_PROCESSES")
            .select(...columns)
            .where("TPROC_ID", tProcId)
            .where("IPROC_STATUS", "A");
    }

    /**
     * Changes tProcId in one particular instance
     * @param {number} iProcId
     * @param {number} newTProcId
     * @param {number} newHeaderId
     * @param {knex} conn
     */

    async changeInstanceProcessTProcId(
        iProcId,
        newTProcId,
        newHeaderId = 1,
        conn = globalThis.database,
    ) {
        await conn("INSTANCE_PROCESSES").where("IPROC_ID", iProcId).update({
            TPROC_ID: newTProcId,
            HEADER_ID: newHeaderId,
        });
    }

    /**
     * Returns an array of objects, containing variables data from specified columns from one particular process
     * @param {number} iProcId
     * @param {array} columns
     * @param {knex} conn
     * @return [*]
     */
    async getDataFromAllInstanceVariables(
        iProcId,
        columns = ["IVAR_NAME", "IVAR_ID"],
        conn = globalThis.database,
    ) {
        return await conn("INSTANCE_VARIABLES")
            .select(...columns)
            .where("IPROC_ID", iProcId);
    }

    /**
     * Returns an array of objects, containing variables data from specified columns from one particular template
     * @param {number} iProcId
     * @param {array} columns
     * @param {knex} conn
     * @return [*]
     */
    async getDataFromAllTemplateVariables(
        tProcId,
        columns = ["TVAR_NAME", "TVAR_ID"],
        conn = globalThis.database,
    ) {
        return await conn("TEMPLATE_VARIABLES")
            .select(...columns)
            .where("TPROC_ID", tProcId);
    }

    /**
     * Updates column TVAR_ID in tables INSTANCE_VARIABLES and INSTANCE_VARIABLES_SNAP.
     * @param {object} iProcVariableDataRow
     * @param {number} newTProcId
     * @param {knex} conn
     * @return {*}
     */
    async updateVariableTVarId(
        iProcVariableDataRow,
        newTProcId,
        conn = globalThis.database,
    ) {
        const { IVAR_NAME } = iProcVariableDataRow;
        const { IVAR_ID } = iProcVariableDataRow;

        const tVarIdsRow = await conn.raw(
            `SELECT "TVAR_ID" FROM "TEMPLATE_VARIABLES" WHERE "TPROC_ID" = ${newTProcId} AND "TVAR_NAME" = '${IVAR_NAME}'`,
        );

        if (tVarIdsRow.length > 0) {
            const NEW_TVAR_ID = tVarIdsRow[0].TVAR_ID;
            await conn("INSTANCE_VARIABLES")
                .where({
                    IVAR_ID,
                })
                .update({
                    TVAR_ID: NEW_TVAR_ID,
                });
            await conn("INSTANCE_VARIABLES_SNAP")
                .where({
                    IVAR_ID,
                })
                .update({
                    TVAR_ID: NEW_TVAR_ID,
                });
            return {
                IVAR_NAME,
                TVAR_ID: NEW_TVAR_ID,
            };
        }
        return null;
    }

    /**
     * Returns an array of objects, containing tasks data from specified columns from one particular process
     * @param {number} iProcId
     * @param {array} columns
     * @param {knex} conn
     * @return [*]
     */
    async getDataFromAllInstanceTasks(
        iProcId,
        columns = ["ITASK_ID", "TTASK_ID"],
        conn = globalThis.database,
    ) {
        return await conn("INSTANCE_TASKS")
            .select(...columns)
            .where("IPROC_ID", iProcId);
    }

    /**
     * Fully deletes instance task and all it`s constraints
     * @param {number} iTaskId
     * @param {number} tTaskId
     * @param {number} iProcId
     * @param {knex} conn
     */
    async deleteInstanceTaskAndConstraints(
        iTaskId,
        tTaskId,
        iProcId,
        conn = globalThis.database,
    ) {
        // Graph
        await conn("INSTANCE_GRAPH")
            .where({
                IGRAPH_FROM_TTASK_ID: iTaskId,
            })
            .orWhere({
                IGRAPH_TO_TTASK_ID: iTaskId,
            })
            .del();

        // History
        await conn("INSTANCE_TASK_HISTORY")
            .where({
                ITASK_ID: iTaskId,
            })
            .del();

        // Links
        // TODO binding
        const linkIds = await conn.raw(
            `SELECT "ITASKLINK_ID" FROM "INSTANCE_TASK_LINKS" WHERE "ITASKLINK_FROM_TTASK_ID" = ${iTaskId} OR "ITASKLINK_TO_TTASK_ID" = ${iTaskId}`,
        );
        for (const row of linkIds) {
            const { ITASKLINK_ID } = row;
            await conn("INSTANCE_LINK_CONDITIONS")
                .where({
                    ITASKLINK_ID,
                })
                .del();
            await conn("INSTANCE_TASK_LINKS")
                .where({
                    ITASKLINK_ID,
                })
                .del();
        }

        // Mail Q
        await conn("MAIL_QUEUE")
            .where({
                ITASK_ID: iTaskId,
            })
            .del();

        // Task itself
        await conn("INSTANCE_TASKS")
            .where({
                TTASK_ID: tTaskId,
                IPROC_ID: iProcId,
            })
            .del();

        globalThis.tasLogger.info(`Task with TTASK_ID ${tTaskId} was deleted.`);
    }

    /**
     * Updates instance task based on a new template. Or deletes the task if it is not used anymore.
     * @param {number} iProcId
     * @param {number} newTProcId
     * @param {knex} conn
     * @param {map} tTaskCache
     */
    async updateOrDeleteInstanceTasks(
        iProcId,
        newTProcId,
        conn = globalThis.database,
        tTaskCache = new Map(),
    ) {
        const columns = [
            "ITASK_ID",
            "TTASK_ID",
            "ITASK_ASSESMENT_TTASK_ID",
            "ITASK_ASSESMENT_TVAR_ID",
            "ITASK_DUE_OFFSET",
            "ITASK_DURATION",
        ];
        const caseTasks = await this.getDataFromAllInstanceTasks(
            iProcId,
            columns,
            conn,
        );

        globalThis.tasLogger.info(`Updating ${caseTasks.length} case tasks.`);
        for (const row of caseTasks) {
            let templateTask;
            const oldTTaskId = row.TTASK_ID;
            const oldITaskId = row.ITASK_ID;

            if (!tTaskCache.has(oldTTaskId)) {
                tTaskCache.set(
                    oldTTaskId,
                    await conn
                        .pluck("TTASK_NAME")
                        .from("TEMPLATE_TASKS")
                        .where("TTASK_ID", oldTTaskId),
                );
            }
            const oldTTaskName = tTaskCache.get(oldTTaskId);

            // TODO test binding
            if (oldTTaskName.length > 0) {
                templateTask = await conn.raw(
                    `SELECT "TTASK_ID", "TTASK_DUE_OFFSET", "TTASK_DURATION", "TTASK_ASSESMENT_TTASK_ID", "TTASK_EVENT", "TTASK_ASSESMENT_TVAR_ID" FROM "TEMPLATE_TASKS" WHERE "TPROC_ID" = ? AND "TTASK_NAME" = ?`,
                    [newTProcId, oldTTaskName],
                );
            } else {
                templateTask = [];
            }

            if (templateTask.length > 0) {
                await conn("INSTANCE_TASKS")
                    .where({
                        TTASK_ID: oldTTaskId,
                        IPROC_ID: iProcId,
                    })
                    .update({
                        TTASK_ID: templateTask[0].TTASK_ID,
                        ITASK_DUE_OFFSET: String(
                            row.ITASK_DUE_OFFSET,
                        ).startsWith("vc")
                            ? templateTask[0].TTASK_DUE_OFFSET
                            : row.ITASK_DUE_OFFSET,
                        ITASK_DURATION: String(row.ITASK_DURATION).startsWith(
                            "vc",
                        )
                            ? templateTask[0].TTASK_DURATION
                            : row.ITASK_DURATION,
                        ITASK_ASSESMENT_TTASK_ID:
                            templateTask[0].TTASK_ASSESMENT_TTASK_ID,
                        ITASK_EVENT: templateTask[0].TTASK_EVENT,
                        ITASK_ASSESMENT_TVAR_ID:
                            templateTask[0].TTASK_ASSESMENT_TVAR_ID,
                    });

                globalThis.tasLogger.info(
                    `Task '${oldTTaskName}' was updated successfully.`,
                );
            } else {
                globalThis.tasLogger.info(
                    `Task with TTASK_ID ${oldTTaskId} no longer exists - deleting task.`,
                );
                await this.deleteInstanceTaskAndConstraints(
                    oldITaskId,
                    oldTTaskId,
                    iProcId,
                    conn,
                );
            }
        }
    }

    /**
     * Creates or updates instance tasks based on a new template.
     * @param {number} tProcId
     * @param {knex} conn
     */
    async fixTemplateTasks(tProcId, conn = globalThis.database) {
        const tTaskIds = await conn
            .pluck("TTASK_ID")
            .from("TEMPLATE_TASKS")
            .where("TPROC_ID", tProcId);

        let i = 0;
        for (const TTASK_ID of tTaskIds) {
            i++;
            globalThis.tasLogger.info(
                `Fixing task ${i} of ${tTaskIds.length}.`,
            );
            const tv = new TaskVersioning(conn, TTASK_ID);
            await tv.fillInstances(TTASK_ID);
        }
    }

    /**
     * Creates or updates instance variables based on a new template.
     * @param {number} tProcId
     * @param {knex} conn
     */
    async fixTemplateVariables(tProcId, conn = globalThis.database) {
        const tVarIds = await conn
            .pluck("TVAR_ID")
            .from("TEMPLATE_VARIABLES")
            .where("TPROC_ID", tProcId);
        let i = 0;
        for (const TVAR_ID of tVarIds) {
            i++;
            globalThis.tasLogger.info(
                `Fixing variable ${i} of ${tVarIds.length}.`,
            );
            const tv = new VariableVersioning(conn, TVAR_ID);
            await tv.fillInstances(TVAR_ID);
        }
    }

    /**
     * Returns differences between case variables and variable definitions in new template
     * @param {number} iProcId
     * @param {object} variableComparisonConfig
     * @param {transaction} conn
     */
    async compareIVarsWithTVars(
        iProcId,
        variableComparisonConfig,
        conn = globalThis.database,
    ) {
        const result = {};

        if (!_.isEmpty(variableComparisonConfig.joinConditions)) {
            for (const variation in variableComparisonConfig.tables) {
                // Get data
                const tableData = await conn
                    .select(variableComparisonConfig.tables[variation].columns)
                    .from("INSTANCE_VARIABLES AS IV")
                    .leftJoin("TEMPLATE_VARIABLES AS TV", function () {
                        this.on("IV.TVAR_ID", "=", "TV.TVAR_ID").andOn(
                            function () {
                                variableComparisonConfig.joinConditions.forEach(
                                    (condition) => condition.call(this),
                                );
                            },
                        );
                    })
                    .where("IV.IPROC_ID", iProcId)
                    .andWhere("TV.TPROC_ID", function () {
                        this.select("TPROC_ID")
                            .from("INSTANCE_PROCESSES")
                            .where("IPROC_ID", iProcId);
                    });

                // Format results
                tableData.forEach((row) => {
                    const { IVAR_ID, ...data } = row;
                    if (!result[IVAR_ID]) {
                        result[IVAR_ID] = {};
                    }
                    result[IVAR_ID][`${variation}_VARIABLES`] = {
                        IVAR_ID,
                        ...data,
                    };
                });
            }
        }

        return result;
    }

    /**
     * Returns differences between LOV of case variables and LOV of template variables.
     * @param {number} iProcId
     * @param {object} lovComparisonConfig
     * @param {knex} conn
     */
    async compareLOV(iProcId, lovComparisonConfig, conn = globalThis.database) {
        const formatedQueryResult = {};
        const diff = {};

        for (const variation in lovComparisonConfig.tables) {
            // Build query
            let tableDataQuery = conn
                .select(lovComparisonConfig.tables[variation].columns)
                .from("INSTANCE_VARIABLES AS IV");

            if (variation === "INSTANCE") {
                tableDataQuery = tableDataQuery
                    .leftJoin(
                        "INSTANCE_VARIABLE_LOV AS IVL",
                        "IV.IVAR_ID",
                        "IVL.IVAR_ID",
                    )
                    .where(function () {
                        this.where("IV.IVAR_TYPE", "=", "LT")
                            .orWhere("IV.IVAR_TYPE", "=", "LN")
                            .orWhere("IV.IVAR_TYPE", "=", "LD");
                    })
                    .andWhere((builder) => {
                        builder
                            .whereNotNull("IVL.IVARLOV_TEXT_VALUE")
                            .orWhereNotNull("IVL.IVARLOV_DATE_VALUE")
                            .orWhereNotNull("IVL.IVARLOV_NUMBER_VALUE");
                    });
            } else {
                tableDataQuery = tableDataQuery
                    .leftJoin(
                        "TEMPLATE_VARIABLES AS TV",
                        "IV.TVAR_ID",
                        "TV.TVAR_ID",
                    )
                    .leftJoin(
                        "TEMPLATE_VARIABLE_LOV AS TVL",
                        "IV.TVAR_ID",
                        "TVL.TVAR_ID",
                    )
                    .where(function () {
                        this.where("TV.TVAR_TYPE", "=", "LT")
                            .orWhere("TV.TVAR_TYPE", "=", "LN")
                            .orWhere("TV.TVAR_TYPE", "=", "LD");
                    })
                    .andWhere((builder) => {
                        builder
                            .whereNotNull("TVL.TVARLOV_TEXT_VALUE")
                            .orWhereNotNull("TVL.TVARLOV_DATE_VALUE")
                            .orWhereNotNull("TVL.TVARLOV_NUMBER_VALUE");
                    });
            }

            tableDataQuery = tableDataQuery
                .andWhere("IV.IPROC_ID", iProcId)
                .groupBy(...lovComparisonConfig.tables[variation].columns);

            // Get data
            const tableData = await tableDataQuery;

            // Format query results
            tableData.forEach((row) => {
                const { IVAR_ID, ...data } = row;
                if (!formatedQueryResult[IVAR_ID]) {
                    formatedQueryResult[IVAR_ID] = {
                        INSTANCE_VARIABLE_LOV: [],
                        TEMPLATE_VARIABLE_LOV: [],
                    };
                }
                formatedQueryResult[IVAR_ID][`${variation}_VARIABLE_LOV`].push({
                    ...data,
                });
            });
        }

        // Compare query results

        for (const key in formatedQueryResult) {
            const iLovLength =
                formatedQueryResult[key].INSTANCE_VARIABLE_LOV.length;
            const tLovLength =
                formatedQueryResult[key].TEMPLATE_VARIABLE_LOV.length;

            if (iLovLength === 0) {
                diff[key] = {
                    INSTANCE_VARIABLE_LOV: null,
                    TEMPLATE_VARIABLE_LOV:
                        formatedQueryResult[key].TEMPLATE_VARIABLE_LOV,
                };
            } else if (tLovLength === 0) {
                diff[key] = {
                    INSTANCE_VARIABLE_LOV:
                        formatedQueryResult[key].INSTANCE_VARIABLE_LOV,
                    TEMPLATE_VARIABLE_LOV: null,
                };
            } else if (iLovLength !== tLovLength) {
                diff[key] = {
                    INSTANCE_VARIABLE_LOV:
                        formatedQueryResult[key].INSTANCE_VARIABLE_LOV,
                    TEMPLATE_VARIABLE_LOV:
                        formatedQueryResult[key].TEMPLATE_VARIABLE_LOV,
                };
            } else {
                let isDifferent = false;
                for (const iVarRow of formatedQueryResult[key]
                    .INSTANCE_VARIABLE_LOV) {
                    const iVarDataHelper = JSON.stringify(iVarRow).replace(
                        /IVAR/g,
                        "TVAR",
                    );
                    const found = formatedQueryResult[
                        key
                    ].TEMPLATE_VARIABLE_LOV.findIndex(
                        (row) => JSON.stringify(row) === iVarDataHelper,
                    );
                    if (found === -1) {
                        isDifferent = true;
                        break;
                    }
                }
                if (isDifferent) {
                    diff[key] = {
                        INSTANCE_VARIABLE_LOV:
                            formatedQueryResult[key].INSTANCE_VARIABLE_LOV,
                        TEMPLATE_VARIABLE_LOV:
                            formatedQueryResult[key].TEMPLATE_VARIABLE_LOV,
                    };
                } else {
                    for (const tVarRow of formatedQueryResult[key]
                        .TEMPLATE_VARIABLE_LOV) {
                        const tVarDataHelper = JSON.stringify(tVarRow).replace(
                            /TVAR/g,
                            "IVAR",
                        );
                        const found = formatedQueryResult[
                            key
                        ].INSTANCE_VARIABLE_LOV.findIndex(
                            (row) => JSON.stringify(row) === tVarDataHelper,
                        );
                        if (found === -1) {
                            isDifferent = true;
                            break;
                        }
                    }
                    if (isDifferent) {
                        diff[key] = {
                            INSTANCE_VARIABLE_LOV:
                                formatedQueryResult[key].INSTANCE_VARIABLE_LOV,
                            TEMPLATE_VARIABLE_LOV:
                                formatedQueryResult[key].TEMPLATE_VARIABLE_LOV,
                        };
                    }
                }
            }
        }

        return diff;
    }

    /**
     * Returns differences between case tasks and tasks definitions in new template.
     * @param {number} iProcId
     * @param {object} taskComparisonConfig
     * @param {knex} conn
     */
    async compareITasksWithTTasks(
        iProcId,
        taskComparisonConfig,
        conn = globalThis.database,
    ) {
        const result = {};
        if (!_.isEmpty(taskComparisonConfig.joinConditions)) {
            for (const variation in taskComparisonConfig.tables) {
                // Get data
                const tableData = await conn
                    .select(taskComparisonConfig.tables[variation].columns)
                    .from("INSTANCE_TASKS AS IT")
                    .leftJoin("TEMPLATE_TASKS AS TT", function () {
                        this.on("IT.TTASK_ID", "=", "TT.TTASK_ID").andOn(
                            function () {
                                taskComparisonConfig.joinConditions.forEach(
                                    (condition) => condition.call(this),
                                );
                            },
                        );
                    })
                    .where("IT.IPROC_ID", iProcId)
                    .andWhere("TT.TPROC_ID", function () {
                        this.select("TPROC_ID")
                            .from("INSTANCE_PROCESSES")
                            .where("IPROC_ID", iProcId);
                    });

                // Format results
                tableData.forEach((row) => {
                    const { ITASK_ID, ...data } = row;
                    if (!result[ITASK_ID]) {
                        result[ITASK_ID] = {};
                    }
                    result[ITASK_ID][`${variation}_TASKS`] = {
                        ITASK_ID,
                        ...data,
                    };
                });
            }
        }

        return result;
    }

    async updateIVarToMatchTVar(varData, conn = globalThis.database) {
        const updateData = {};

        if (!varData.INSTANCE_VARIABLES || !varData.TEMPLATE_VARIABLES) {
            throw new Error(
                JSON.stringify({
                    message: "Wrong input data",
                    data: varData,
                }),
            );
        }

        for (const key in varData.INSTANCE_VARIABLES) {
            const keySanitized = key.split("_").slice(1).join("_");
            if (key === "DLIST_NAME") {
                if (
                    varData.INSTANCE_VARIABLES[key] !==
                    varData.TEMPLATE_VARIABLES[key]
                ) {
                    updateData[key] = varData.TEMPLATE_VARIABLES[key];
                }
            } else if (key !== "IVAR_ID" && key !== "TVAR_ID") {
                if (
                    varData.INSTANCE_VARIABLES[`IVAR_${keySanitized}`] !==
                    varData.TEMPLATE_VARIABLES[`TVAR_${keySanitized}`]
                ) {
                    updateData[key] =
                        varData.TEMPLATE_VARIABLES[`TVAR_${keySanitized}`];
                }
            }
        }

        return await conn("INSTANCE_VARIABLES")
            .where({ IVAR_ID: varData.INSTANCE_VARIABLES.IVAR_ID })
            .update(updateData);
    }

    async updateIVarLOVToMatchTVarLOV(
        IVAR_ID,
        LOVData,
        conn = globalThis.database,
    ) {
        // Delete instance LOV
        await conn("INSTANCE_VARIABLE_LOV").where({ IVAR_ID }).del();

        // Create new LOV
        if (LOVData.TEMPLATE_VARIABLE_LOV) {
            const insertData = await Promise.all(
                LOVData.TEMPLATE_VARIABLE_LOV.map(async (row) => {
                    const seqValue = await conn.raw(
                        `SELECT NEXT VALUE FOR "IVARLOV_ID_SEQ" AS "SEQ"`,
                    );
                    const columns = {
                        ORG_ID: 1,
                        IVARLOV_ID: seqValue[0].SEQ,
                        IVAR_ID,
                        IVARLOV_IS_SELECTED: "N",
                        IVARLOV_TEXT_VALUE: row.TVARLOV_TEXT_VALUE,
                        IVARLOV_NUMBER_VALUE: row.TVARLOV_NUMBER_VALUE,
                        IVARLOV_DATE_VALUE: row.TVARLOV_DATE_VALUE,
                    };
                    if (Array.isArray(globalThis.dynamicConfig.langs)) {
                        globalThis.dynamicConfig.langs.forEach((lang) => {
                            columns[
                                `IVARLOV_TEXT_VALUE_${lang.toUpperCase()}`
                            ] = row[`TVARLOV_TEXT_VALUE_${lang.toUpperCase()}`];
                        });
                    }
                    return columns;
                }),
            );

            await conn("INSTANCE_VARIABLE_LOV").insert(insertData);
        } else {
            return null;
        }
    }

    async updateITaskToMatchTTask(taskData, conn = globalThis.database) {
        const updateData = {};

        if (!taskData.INSTANCE_TASKS || !taskData.TEMPLATE_TASKS) {
            throw new Error(
                JSON.stringify({
                    message: "Wrong input data",
                    data: taskData,
                }),
            );
        }

        for (const key in taskData.INSTANCE_TASKS) {
            if (key !== "ITASK_ID" && key !== "TTASK_ID") {
                const keySanitized = key.split("_").slice(1).join("_");
                if (
                    taskData.INSTANCE_TASKS[`ITASK_${keySanitized}`] !==
                    taskData.TEMPLATE_TASKS[`TTASK_${keySanitized}`]
                ) {
                    updateData[key] =
                        taskData.TEMPLATE_TASKS[`TTASK_${keySanitized}`];
                }
            }
        }

        return await conn("INSTANCE_TASKS")
            .where({ ITASK_ID: taskData.INSTANCE_TASKS.ITASK_ID })
            .update(updateData);
    }

    async comparisonConfigUpdateLangs(comparisonConfig) {
        if (Array.isArray(globalThis.dynamicConfig.langs)) {
            globalThis.dynamicConfig.langs.forEach((lang) => {
                // INSTANCE_VARIABLES
                comparisonConfig.variables.tables.INSTANCE.columns.push(
                    `IVAR_NAME_${lang.toUpperCase()}`,
                );
                comparisonConfig.variables.tables.TEMPLATE.columns.push(
                    `TVAR_NAME_${lang.toUpperCase()}`,
                );

                // INSTANCE_VARIABLE_LOV
                comparisonConfig.variableLOV.tables.INSTANCE.columns.push(
                    `IVARLOV_TEXT_VALUE_${lang.toUpperCase()}`,
                );
                comparisonConfig.variableLOV.tables.TEMPLATE.columns.push(
                    `TVARLOV_TEXT_VALUE_${lang.toUpperCase()}`,
                );
            });
        }

        return comparisonConfig;
    }
}

module.exports = InstanceProcessTemplateMigration;
