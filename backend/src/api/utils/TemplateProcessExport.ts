import _ from "lodash";
import { UtilsService } from "../services/UtilsService";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { TasDBIntegrity } from "./TasDBIntegrity";
import { Knex } from "@mikro-orm/knex";
import { ICustomView } from "../orm/entity/CustomView";
import { ITemplateVariableAttributes } from "../orm/entity/TemplateVariable";

const DEFAULT_TPROC_ID = 1; // Some entities doesnt know tproc version. Use 1 as default.

// TODO export event variable
//   RDEFVAR_NAME: 'RDEF_PROC_OWNER',
//   RDEFVAR_VALUE: '1.1' user.orgstr

export class TemplateProcessExport {
    private readonly connection: Knex;
    constructor(connection: Knex) {
        this.connection = connection;
    }

    async exportRoles(ids: number | number[]) {
        const repo = globalThis.orm.repo("role", this.connection);
        const collection = repo.getCollection();
        if (ids) {
            collection.knex.whereIn(
                "ROLE_ID",
                Array.isArray(ids) ? ids : [ids],
            );
        }
        const users = await collection.collectAll();
        return _.map(users, "_raw");
    }

    async exportOrganizations(ids: number | number[]) {
        const repo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        const collection = repo.getByStatus("A");
        if (ids) {
            collection.knex.whereIn(
                "ORGSTR_ID",
                Array.isArray(ids) ? ids : [ids],
            );
        }

        const users = await collection.collectAll();
        return _.map(users, "_raw");
    }

    /**
     * Export all Active or Locked users.
     * @return {*}
     */
    async exportUsers(ids: number | Array<number | null>) {
        const repo = globalThis.orm.repo("user", this.connection);
        const collection = repo.getByStatus(["A", "L", "D"]);
        if (ids) {
            collection.knex.whereIn(
                "USER_ID",
                Array.isArray(ids) ? ids : [ids],
            );
        }

        const users = await collection.collectAll();
        return _.map(users, "_raw");
    }

    async exportLinks(id: number[]) {
        const repo = globalThis.orm.repo("templateTaskLink", this.connection);
        const collection = repo.forTemplateProcess(id[0], id[1]);
        const users = await collection.collectAll();
        const links = _.map(users, "_raw");
        return repo.fillConditions(links);
    }

    async exportSections(id: number[]) {
        const repo = globalThis.orm.repo("templateSections", this.connection);
        const sections = await repo.getForTemplateProcess(id[0]).collectAll();
        return _.map(sections, "_raw");
    }

    async exportCustomViews(id: number[]): Promise<ICustomView[]> {
        const cvs = await this.connection
            .select()
            .from("CUSTOM_VIEWS")
            .where("TPROC_ID", id[0]);
        for (const cv of cvs) {
            cv.columns = await this.connection
                .select()
                .from("CUSTOM_VIEW_COLUMNS")
                .where("CV_ID", cv.CV_ID);
        }
        return cvs;
    }

    async exportTemplateTasks(id: number[]): Promise<TemplateTask[]> {
        const tproc = await globalThis.orm
            .repo("templateProcess", this.connection)
            .get(id);

        const repo = globalThis.orm.repo("templateTask", this.connection);
        const collection = repo.getAll();
        collection.knex = collection.knex.where("TPROC_ID", id[0]);

        const tasks = await collection.collectAll();

        if (Array.isArray(tasks) && tasks.length > 0) {
            tasks.forEach((task) => {
                // Hofix.
                if (task.TTASK_ASSESMENT_TVAR_ID === 0) {
                    task.TTASK_ASSESMENT_TVAR_ID = null;
                }
            });
        }

        let processedTasks = _.map(tasks, "_raw");
        processedTasks = await repo.fillJSCalculations(processedTasks, tproc);
        processedTasks = await repo.fillCompletition(processedTasks, tproc);
        processedTasks = await repo.fillEmailNotifs(processedTasks);
        processedTasks = await repo.fillInvitation(processedTasks);
        processedTasks = await repo.fillVariableUsages(processedTasks, tproc);
        processedTasks = await repo.fillMassUsages(processedTasks, tproc);

        return processedTasks;
    }

    async exportTemplateVariables(id: number[]) {
        const repo = globalThis.orm.repo("templateVariable", this.connection);
        const collection = repo.getForTemplateProcess(id[0]);
        const variables = await collection.collectAll();
        return _.map(variables, "_raw");
    }

    async exportTemplatePrints(id: number[]) {
        const repo = globalThis.orm.repo("templatePrint", this.connection);
        const collection = repo.getTemplateProcessPrints(id[0]);
        const prints = await collection.collectAll();
        return _.map(prints, "_raw");
    }

    exportTemplateGraph(id: number[]) {
        const repo = globalThis.orm.repo("templateGraph", this.connection);
        return repo.getForTProcess(id[0], id[1]);
    }

    async exportHeaders(id: number[]): Promise<Record<string, any>[]> {
        const repo = globalThis.orm.repo("header", this.connection);
        let headers = await repo.getHeadersByTProcId(id[0]).collectAll();

        headers = _.map(headers, "_raw");
        headers = await repo.fillRoleRights(headers);
        headers = await repo.fillOrgstrRights(headers);

        return headers;
    }

    async exportCaseStatuses(tprocId: number[]) {
        const repo = globalThis.orm.repo("caseStatus", this.connection);
        const statuses = await repo.getForTemplate(tprocId[0]).collectAll();
        return _.map(statuses, "_raw");
    }

    async getTemplateVaraibleLOVs(
        templateVariables: ITemplateVariableAttributes[],
    ) {
        if (
            !Array.isArray(templateVariables) ||
            templateVariables.length === 0
        ) {
            return [];
        }

        const repo = globalThis.orm.repo(
            "templateVariableLov",
            this.connection,
        );
        const lovs = await repo.fetchAll("*");
        return repo.assoc(lovs, "VAR_ID");
    }

    /**
     * Get DT inside tvar_meta. It can be used in dataUrl
     */
    async retrieveDTNameFromTvarMeta(TVAR_META: string) {
        const dtIds = [];
        try {
            const tvarMeta = JSON.parse(TVAR_META);
            if (tvarMeta.tableDefinition) {
                const tableDefinition = JSON.parse(tvarMeta.tableDefinition);
                if (Array.isArray(tableDefinition.columns)) {
                    for (let i = 0; i < tableDefinition.columns.length; i++) {
                        const { dataUrl } = tableDefinition.columns[i];
                        const m = dataUrl.match(/\/dyn-table\/(\d*)\/values/);
                        if (m) {
                            dtIds.push(m[1]);
                        }
                    }
                }
            }
        } catch (_err) {
            //
        }

        if (Array.isArray(dtIds) && dtIds.length > 0) {
            const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
            const dts = await dtRepo.getById(dtIds);
            return _.map(dts, "DT_NAME");
        }

        return;
    }

    async getTemplateVariableDTs(exportData: ExportedProcess) {
        // Collect variables
        const processes = exportData.TEMPLATE_PROCESSES;
        if (!Array.isArray(processes) || processes.length === 0) {
            return [];
        }
        let templateVariables: ITemplateVariableAttributes[] = [];
        processes.forEach((process) => {
            if (
                Array.isArray(process.TEMPLATE_VARIABLES) &&
                process.TEMPLATE_VARIABLES.length > 0
            ) {
                templateVariables = templateVariables.concat(
                    process.TEMPLATE_VARIABLES,
                );
            }
        });
        templateVariables = _.uniq(templateVariables);

        // Collect all DTs used in template variables.
        const dtNames = [];

        for (const tvar of templateVariables) {
            // Variable is DynamicTable
            if (tvar.TVAR_TYPE === "DT") {
                dtNames.push(tvar.DLIST_NAME);
            } else if (tvar.TVAR_META) {
                // Variable can contain url to DT
                const dts = await this.retrieveDTNameFromTvarMeta(
                    tvar.TVAR_META,
                );
                if (Array.isArray(dts)) {
                    dtNames.push(...dts);
                }
            }
        }

        const uniqueDTNames = _.uniq(dtNames); // Distinct DTs
        const valuesRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dtColRepo = globalThis.orm.repo(
            "dynamicTableCol",
            this.connection,
        );

        const dtResults = [];
        for (const dtName of uniqueDTNames) {
            const table = await dtRepo.get(dtName);
            if (!table) {
                throw new UserException(
                    `Cannot find dynamic_table with dt_name=${dtName}`,
                );
            }

            const cols = await (
                await dtColRepo.getForTable(table.DT_ID)
            ).fetchAll();
            const colIds = _.map(cols, "DTC_ID");
            const values = await (
                await valuesRepo.getValues(table.DT_ID, colIds)
            ).fetchAll();

            const map = {
                DTV_: "DLV_",
                DT_: "DLL_",
                DTC_: "DLC_",
            };

            const out = UtilsService.remapAttrs(table._raw, map);
            out.DLL_VALUES = UtilsService.remapAttrs(values, map);
            out.DLL_COLS = UtilsService.remapAttrs(cols, map);
            dtResults.push(out);
        }
        return dtResults;
    }

    searchUsedUsers(data: ExportedProcess) {
        const tprocExports = data.TEMPLATE_PROCESSES;
        if (!Array.isArray(tprocExports) || tprocExports.length === 0) {
            return [];
        }

        let users: Array<number | null> = [];
        tprocExports.forEach((tprocExport) => {
            // Users from template process
            users.push(tprocExport.TPROC_OWNER_USER_ID);
            users.push(tprocExport.TPROC_LAST_CHANGED_BY_USER_ID);
            users.push(tprocExport.TPROC_LOCKED_BY_USER_ID);

            // Users from template tasks
            if (
                Array.isArray(tprocExport.TEMPLATE_TASKS) &&
                tprocExport.TEMPLATE_TASKS.length > 0
            ) {
                tprocExport.TEMPLATE_TASKS.forEach((ttask: TemplateTask) => {
                    users.push(ttask.TTASK_ASSESMENT_USER_ID);
                });
            }
        });

        // Add RDEF_PROC_OWNER user.
        if (Array.isArray(data.EVENTS) && data.EVENTS.length > 0) {
            data.EVENTS.forEach((event) => {
                if (Array.isArray(event.rules) && event.rules.length > 0) {
                    event.rules.forEach((rule: Rule) => {
                        if (
                            Array.isArray(rule.rule_variables) &&
                            rule.rule_variables.length > 0
                        ) {
                            rule.rule_variables.forEach((ruleVariable) => {
                                if (
                                    ruleVariable.RDEFVAR_NAME ===
                                    "RDEF_PROC_OWNER"
                                ) {
                                    const buf =
                                        ruleVariable.RDEFVAR_VALUE.split(".");
                                    if (buf.length === 2) {
                                        const userId = buf[0];
                                        if (
                                            UtilsService.isNumericString(userId)
                                        ) {
                                            users.push(Number(userId));
                                        }
                                    }
                                }
                            });
                        }
                    });
                }
            });
        }

        users = _.uniq(users); // Distinct values.
        users = users.filter(
            (val) => val !== null && val !== 1 /* Skip ADMIN export */,
        ); // Remove nulls

        if (Array.isArray(users) && users.length > 0) {
            return this.exportUsers(users);
        }
        return [];
    }

    async searchUsedRoles(tprocExports: TProcess[]) {
        if (!Array.isArray(tprocExports) || tprocExports.length === 0) {
            return [];
        }

        let roles = [];
        for (const tprocExport of tprocExports) {
            // Users from template process
            roles.push(tprocExport.TPROC_VIS_ROLE_ID);

            // Roles from template tasks
            for (const ttask of tprocExport.TEMPLATE_TASKS || []) {
                roles.push(ttask.TTASK_ASSESMENT_ROLE_ID);

                for (const notif of ttask.email_notifs || []) {
                    roles.push(notif.TTASK_ENOT_TGT_ROLE_ID);
                    roles.push(notif.TTASK_ENOT_BLIND_ROLE_ID);
                    roles.push(notif.TTASK_ENOT_REPLY_ROLE_ID);
                    roles.push(notif.TTASK_ENOT_COPY_ROLE_ID);
                }
            }

            // Roles from headers
            if (
                Array.isArray(tprocExport.HEADERS) &&
                tprocExport.HEADERS.length > 0
            ) {
                tprocExport.HEADERS.forEach((header: Header) => {
                    if (header.HDR_VIS_ROLE_ID) {
                        roles.push(header.HDR_VIS_ROLE_ID);
                    }

                    if (header.HEADER_HR_ROLE_ID) {
                        roles.push(header.HEADER_HR_ROLE_ID);
                    }

                    if (
                        Array.isArray(header.header_roles) &&
                        header.header_roles.length > 0
                    ) {
                        header.header_roles.forEach((role: HeaderRole) => {
                            roles.push(role.ROLE_ID);
                        });
                    }
                });
            }
        }

        roles = _.uniq(roles).filter((val) => val !== null && val > 0);

        if (roles.length > 0) {
            return await this.exportRoles(roles);
        }

        return [];
    }

    getAllOrganizationsForUser(userIds: number | number[]) {
        return this.connection
            .select("OS.ORGSTR_ID")
            .from("ORGANIZATION_STRUCTURE as OS")
            .leftJoin(
                "USER_ORGANIZATION_STRUCTURE as UOS",
                "OS.ORGSTR_ID",
                "UOS.ORGSTR_ID",
            )
            .whereIn(
                "UOS.USER_ID",
                Array.isArray(userIds) ? userIds : [userIds],
            )
            .union(function () {
                this.select("ORGSTR_ID")
                    .from("ORGANIZATION_STRUCTURE")
                    .whereIn(
                        "MANAGER_USER_ID",
                        Array.isArray(userIds) ? userIds : [userIds],
                    );
            });
    }

    async searchUsedOrganizations(tprocExports: ExportedProcess) {
        let orgs: number[] = [];

        const processes = tprocExports.TEMPLATE_PROCESSES;
        if (Array.isArray(processes) && processes.length > 0) {
            processes.forEach((tprocExport) => {
                // Users from template process
                orgs.push(tprocExport.TPROC_VIS_ORGSTR_ID);

                // Users from template tasks
                for (const ttask of tprocExport.TEMPLATE_TASKS || []) {
                    orgs.push(ttask.TTASK_ASSESMENT_ORGSTR_ID);
                    orgs.push(ttask.TTASK_ASSESMENT_ORGSTR_CNST);
                    orgs.push(ttask.TPROC_VIS_ORGSTR_ID);

                    for (const notif of ttask.email_notifs || []) {
                        orgs.push(notif.TTASK_ENOT_TGT_ORGSTR_ID);
                        orgs.push(notif.TTASK_ENOT_BLIND_ORGSTR_ID);
                        orgs.push(notif.TTASK_ENOT_REPLY_ORGSTR_ID);
                        orgs.push(notif.TTASK_ENOT_COPY_ORGSTR_ID);
                    }
                }

                // Roles from headers
                if (
                    Array.isArray(tprocExport.HEADERS) &&
                    tprocExport.HEADERS.length > 0
                ) {
                    tprocExport.HEADERS.forEach((header: Header) => {
                        if (
                            Array.isArray(header.header_orgstrs) &&
                            header.header_orgstrs.length > 0
                        ) {
                            header.header_orgstrs.forEach((role) => {
                                orgs.push(role.ORGSTR_ID);
                            });
                        }
                    });
                }
            });
        }

        // Add RDEF_PROC_OWNER user.
        if (
            Array.isArray(tprocExports.EVENTS) &&
            tprocExports.EVENTS.length > 0
        ) {
            tprocExports.EVENTS.forEach((event: Event) => {
                if (Array.isArray(event.rules) && event.rules.length > 0) {
                    event.rules.forEach((rule) => {
                        if (
                            Array.isArray(rule.rule_variables) &&
                            rule.rule_variables.length > 0
                        ) {
                            rule.rule_variables.forEach(
                                (ruleVariable: RuleVariable) => {
                                    if (
                                        ruleVariable.RDEFVAR_NAME ===
                                        "RDEF_PROC_OWNER"
                                    ) {
                                        const buf =
                                            ruleVariable.RDEFVAR_VALUE.split(
                                                ".",
                                            );
                                        if (buf.length === 2) {
                                            const userId = buf[0];
                                            const orgstrId = buf[1];
                                            if (
                                                UtilsService.isNumericString(
                                                    userId,
                                                )
                                            ) {
                                                orgs.push(Number(orgstrId));
                                            }
                                        }
                                    }
                                },
                            );
                        }
                    });
                }
            });
        }

        const users = Array.isArray(tprocExports.USERS)
            ? _.map(tprocExports.USERS, "USER_ID")
            : [];

        const orgstrs = await this.getAllOrganizationsForUser(users);
        const ids = _.map(orgstrs, "ORGSTR_ID");
        orgs = orgs.concat(ids);
        orgs = _.uniq(orgs); // Distinct values.
        orgs = orgs.filter(
            (val) => val !== null && val !== 1 && typeof val !== "undefined",
        ); // Remove nulls

        if (Array.isArray(orgs) && orgs.length > 0) {
            return this.exportOrganizations(orgs);
        }
        return [];
    }

    async getEventNamesFromTemplateProcess(
        id: Array<number[]>,
    ): Promise<string[]> {
        if (!Array.isArray(id)) {
            throw new UserException(
                "id in getEventNamesFromTemplateProcess be array",
            );
        }
        for (const row of id) {
            if (!Array.isArray(row) || row.length !== 2) {
                throw new UserException(
                    "id in getEventNamesFromTemplateProcess must have 2 items [TPROC_ID, TPROC_VERSION]",
                );
            }
        }

        const tprocIds = _.map(id, 0);
        const tasks = await this.connection
            .select("TTASK_TYPE", "TTASK_EVENT")
            .from("TEMPLATE_TASKS")
            .whereIn("TPROC_ID", tprocIds);

        let events: string[] = [];
        if (Array.isArray(tasks) && tasks.length > 0) {
            // Run event, wait event, ..
            tasks.forEach((ttask) => {
                if (
                    ["W", "P", "E"].indexOf(ttask.TTASK_TYPE) !== -1 &&
                    ttask.TTASK_EVENT
                ) {
                    events.push(ttask.TTASK_EVENT);
                }
            });
        }

        // Search events(subprocesses) like:
        // $SUBP_757
        // $SUBR_757
        if (Array.isArray(tasks) && tasks.length > 0) {
            const evedefRepo = globalThis.orm.repo(
                "eventDefinition",
                this.connection,
            );
            const subprocessEvents = [];

            for (const ttask of tasks) {
                const ev = await evedefRepo.getByTTaskId(ttask.TTASK_ID);
                const eveNames = ev.map((event) => event.EVEDEF_NAME);
                subprocessEvents.push(...eveNames);
            }

            events = events.concat(subprocessEvents);
        }
        if (Array.isArray(events) && events.length > 0) {
            const eveRepo = globalThis.orm.repo(
                "eventDefinition",
                this.connection,
            );

            for (const eventName of events) {
                const match = eventName.match(/^\$SUBP_(\d+)/);
                if (match) {
                    const newEve = `$SUBR_${match[1]}`;
                    const subpEvent = await eveRepo.getByName(newEve);
                    if (Array.isArray(subpEvent) && subpEvent.length > 0) {
                        events.push(newEve);
                    }
                }
            }
        }

        return events;
    }

    async addEventProcesses(
        id: Array<number[]>,
        out: Array<number[]> = [],
    ): Promise<Array<number[]>> {
        if (!id || id.length === 0) {
            return out;
        }

        let result = out.concat(id);

        const eveNames = await this.getEventNamesFromTemplateProcess(id);

        if (!Array.isArray(eveNames) || eveNames.length === 0) {
            return result;
        }

        const columns = globalThis.orm.getUniqueColumns([
            { alias: "RD", name: "ruleDefinition" },
            { alias: "ED", name: "eventDefinition" },
            { alias: "TT", name: "templateTask" },
            { alias: "TP", name: "templateProcess" },
        ]);

        const tprocs = await this.connection
            .select(columns)
            .from("RULE_DEFINITION as RD")
            .leftJoin("EVENT_DEFINITION as ED", "ED.EVEDEF_ID", "RD.EVEDEF_ID")
            .leftJoin(
                "TEMPLATE_TASKS as TT",
                "TT.TTASK_ID",
                this.connection.raw(
                    `${globalThis.orm.db.substr(`"RDEF_VALUE"`, 7, 100)}${globalThis.dynamicConfig.db.client === "postgresql" ? "::integer" : ""}`,
                ),
            )
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("TT.TPROC_ID", "TP.TPROC_ID")
                    .andOn("TP.TPROC_ID", globalThis.database.raw("1"));
            })
            .whereNot("TP.TPROC_STATUS", "E")
            .where("RD.RDEF_TYPE", "EVENTW")
            .whereIn("ED.EVEDEF_NAME", eveNames);

        const evewaitTprocIds = [];
        if (Array.isArray(tprocs) && tprocs.length > 0) {
            for (const tproc of tprocs) {
                evewaitTprocIds.push([tproc.TPROC_ID, tproc.TPROC_VERSION]);
            }
        }

        const tprocIds = _.map(id, 0);
        let foundTprocs = evewaitTprocIds;

        const rows = await this.connection
            .select(this.connection.raw(`"RDEF_VALUE"`))
            .from("RULE_DEFINITION as RDEF")
            .join(
                "EVENT_DEFINITION as EDEF",
                "EDEF.EVEDEF_ID",
                "RDEF.EVEDEF_ID",
            )
            .join(
                "TEMPLATE_TASKS as TTASK",
                "EDEF.EVEDEF_NAME",
                "TTASK.TTASK_EVENT",
            )
            .whereIn("TTASK.TTASK_TYPE", ["E", "W"])
            .whereIn("RDEF.RDEF_TYPE", [
                "PROCESS",
                "CSV_RUN_PROCESSES",
                "CSV_UPDATE_PROCESSES",
                "CSV_MERGE_PROCESSES",
                "CSV_UPDATE_PROCESS",
            ])
            .whereIn(
                "TTASK.TPROC_ID",
                Array.isArray(tprocIds) ? tprocIds : [tprocIds],
            );

        if (Array.isArray(rows) && rows.length > 0) {
            const rdefValues = _.map(rows, "RDEF_VALUE");

            rdefValues.forEach((rdefValue) => {
                if (rdefValue === null) {
                    globalThis.tasLogger.error(
                        `Not found TEMPLATE_PROCESS for TPROC_ID=${rdefValue}`,
                    );
                    return;
                }

                const ruleTprocId = rdefValue.split(".");
                foundTprocs.push([
                    Number(ruleTprocId[0]),
                    Number(ruleTprocId[1]),
                ]);
            });
        }

        if (foundTprocs.length > 0) {
            foundTprocs = UtilsService.differenceArrays(foundTprocs, result);
            result = result.concat(foundTprocs);
            return this.addEventProcesses(foundTprocs, result);
        }

        return UtilsService.uniqueArrays(result);
    }

    async addSubprocesses(
        id: Array<number[]>,
        out: Array<number[]> = [],
    ): Promise<Array<number[]>> {
        let result = out;
        if (!out) {
            result = [];
        }

        if (!id) {
            return result;
        }

        result = out.concat(id);

        const tprocIds = _.map(id, 0);
        const rows = await this.connection
            .select(
                "TTASK_SUBPROCESS_TPROC_ID",
                "TTASK_SUBPROCESS_TPROC_VERSION",
            )
            .from("TEMPLATE_TASKS")
            .whereIn(
                "TPROC_ID",
                Array.isArray(tprocIds) ? tprocIds : [tprocIds],
            )
            .where("TTASK_SUBPROCESS_TPROC_ID", ">", 0);

        if (Array.isArray(rows) && rows.length > 0) {
            let tprocs: Array<number[]> = [];
            rows.forEach((row) => {
                tprocs.push([
                    row.TTASK_SUBPROCESS_TPROC_ID,
                    row.TTASK_SUBPROCESS_TPROC_VERSION || 1,
                ]);
            });
            tprocs = UtilsService.differenceArrays(tprocs, result);

            if (tprocs.length > 0) {
                out.concat(tprocs);
                return this.addSubprocesses(tprocs, result);
            }
        }
        return _.uniq(result);
    }

    async exportProcess(id: number[]) {
        const repo = globalThis.orm.repo("templateProcess", this.connection);
        const tProcess: TProcess = (await repo.get(id))._raw;

        tProcess.CUSTOM_VIEWS = await this.exportCustomViews(id);
        tProcess.TEMPLATE_TASKS = await this.exportTemplateTasks(id);
        tProcess.TEMPLATE_LINKS = await this.exportLinks(id);
        tProcess.TEMPLATE_VARIABLES = await this.exportTemplateVariables(id);
        tProcess.TEMPLATE_PRINTS = await this.exportTemplatePrints(id);
        tProcess.TEMPLATE_GRAPH = await this.exportTemplateGraph(id);
        tProcess.HEADERS = await this.exportHeaders(id);
        tProcess.CASE_STATUSES = await this.exportCaseStatuses(id);
        tProcess.TEMPLATE_VARIABLE_LOVS = await this.getTemplateVaraibleLOVs(
            tProcess.TEMPLATE_VARIABLES,
        );
        tProcess.TEMPLATE_SECTIONS = await this.exportSections(id);
        return tProcess;
    }

    async exportEvents(tprocExports: TProcess[]): Promise<Record<string, any>> {
        if (!Array.isArray(tprocExports) || tprocExports.length === 0) {
            return [];
        }

        const ids: Array<number[]> = [];
        const templateTaskIds = tprocExports.reduce((acc: number[], tproc) => {
            if (
                Array.isArray(tproc.TEMPLATE_TASKS) &&
                tproc.TEMPLATE_TASKS.length
            ) {
                acc = [
                    ...acc,
                    ...tproc.TEMPLATE_TASKS.map(
                        (task: TemplateTask) => task.TTASK_ID,
                    ),
                ];
            }
            return acc;
        }, []);

        tprocExports.forEach((tproc: TProcess) => {
            ids.push([tproc.TPROC_ID, tproc.TPROC_VERSION]);
        });

        // Event names
        const eventNames = await this.getEventNamesFromTemplateProcess(ids);
        if (!Array.isArray(eventNames) || eventNames.length === 0) {
            return [];
        }

        const eveRepo = globalThis.orm.repo("eventDefinition", this.connection);
        const ruleDef = globalThis.orm.repo("ruleDefinition", this.connection);

        const events = await eveRepo.getByName(eventNames);
        await eveRepo.fillRules(events);

        // Find invalid rules.
        for (const event of events) {
            const validRules = [];
            for (const rule of event.rules) {
                if (rule.RDEF_TYPE !== "EVENTW" || !rule.RDEF_VALUE) {
                    validRules.push(rule);
                    continue;
                }

                const ttaskId = Number(rule.RDEF_VALUE.substr(6));

                const processes = await this.connection
                    .select("TP.TPROC_STATUS")
                    .from("TEMPLATE_TASKS as TT")
                    .leftJoin(
                        "TEMPLATE_PROCESSES as TP",
                        "TP.TPROC_ID",
                        "TT.TPROC_ID",
                    )
                    .where("TT.TTASK_ID", ttaskId);

                if (!processes[0]) {
                    globalThis.tasLogger.error(
                        `Not found process for ttask_id = ${ttaskId} RDEF_VALUE= ${rule.RDEF_VALUE} EVENT= ${event.EVEDEF_NAME} ${event.EVEDEF_ID}`,
                    );
                }

                // Check if process is not erased and is in template_processes
                if (
                    templateTaskIds.includes(ttaskId) &&
                    Array.isArray(processes) &&
                    processes[0]?.TPROC_STATUS !== undefined &&
                    processes[0]?.TPROC_STATUS !== "E"
                ) {
                    validRules.push(rule);
                }
            }

            event.rules = validRules;
        }

        //Find all variables and params for rules
        for (const event of events) {
            await ruleDef.fillVariables(event.rules);
            await ruleDef.fillParams(event.rules);
        }
        return events;
    }

    async getGlobalScripts(exportData: ExportedProcess) {
        const tprocExports = exportData.TEMPLATE_PROCESSES;
        if (!Array.isArray(tprocExports) || tprocExports.length === 0) {
            return [];
        }

        const ttjsCalcrepo = globalThis.orm.repo(
            "templateTaskJSCalculation",
            this.connection,
        );
        let ids: number[] = [];

        for (const tproc of tprocExports) {
            const calculation = await ttjsCalcrepo.getForProcess(
                tproc.TPROC_ID,
                tproc.TPROC_VERSION,
            );
            const calcs = await calculation.collectAll();

            ids = ids.concat(
                ...calcs.map((calc) =>
                    JSON.parse(calc.TTJSCALC_APPEND_SCRIPTS),
                ),
            );
        }

        const repo = globalThis.orm.repo("jsScript");

        return await repo.getByType("C").knex.whereIn("JS_ID", _.uniq(ids));
    }

    async addEventWaitProcesses(
        ids: Array<number[]>,
    ): Promise<Array<number[]>> {
        const rows = await this.connection
            .select("TVAR.TPROC_ID")
            .from("TEMPLATE_PROCESSES AS TP")
            .rightJoin("TEMPLATE_TASKS AS T", "TP.TPROC_ID", "T.TPROC_ID")
            .rightJoin(
                "EVENT_DEFINITION AS E",
                "E.EVEDEF_NAME",
                "T.TTASK_EVENT",
            )
            .rightJoin("RULE_DEFINITION AS RD", "RD.EVEDEF_ID", "E.EVEDEF_ID")
            .rightJoin(
                "RULE_DEFINITION_PARAM AS RDP",
                "RDP.RDEF_ID",
                "RD.RDEF_ID",
            )
            .rightJoin(
                "TEMPLATE_VARIABLES AS TVAR",
                "TVAR.TVAR_ID",
                "RDP.TVAR_ID",
            )
            .whereNotNull("T.TTASK_EVENT")
            .where("T.TTASK_EVENT_WAIT", 1)
            .whereNotNull("TVAR.TPROC_ID")
            .whereIn("TP.TPROC_ID", _.map(ids, 0));

        const newTProcs = _.map(rows, "TPROC_ID");
        newTProcs.forEach((tprocId) => {
            ids.push([tprocId, DEFAULT_TPROC_ID]);
        });
        return UtilsService.uniqueArrays(ids);
    }

    async addProcessesRecursively(ids: Array<number[]>): Promise<any> {
        if (!Array.isArray(ids) || ids.length === 0) {
            return;
        }

        let childIds = await this.addEventProcesses(ids);
        childIds = await this.addSubprocesses(childIds);
        childIds = await this.addEventWaitProcesses(childIds);

        if (childIds.length === ids.length) {
            return childIds;
        }
        // If any subprocess added, must recheck again. There can be n templates deeper and deeper like Proc->subproc->subproc->subproc->event(run tproc)->...
        return this.addProcessesRecursively(childIds);
    }

    async exportProcesses(id: number[]) {
        if (!id) {
            return [];
        }

        const tasDBIntegrity = new TasDBIntegrity(this.connection);
        await tasDBIntegrity.fixAll();

        const procs = await this.addProcessesRecursively([id]);

        // Export all necessary processes
        const templates: TProcess[] = [];
        for (const tid of procs) {
            const template = await this.exportProcess(tid);
            templates.push(template);
        }

        const exportedProcesses: ExportedProcess = {
            TEMPLATE_PROCESSES: templates,
            VERSION: globalThis.dynamicConfig.majorVersion,
            ROLES: null,
            EVENTS: null,
            USERS: null,
            ORGSTRS: null,
            DYNAMIC_TABLES: null,
            CALCULATIONS_SCRIPTS: null,
        };

        exportedProcesses.EVENTS = await this.exportEvents(
            exportedProcesses.TEMPLATE_PROCESSES,
        );

        exportedProcesses.ROLES = await this.searchUsedRoles(
            exportedProcesses.TEMPLATE_PROCESSES,
        );
        exportedProcesses.USERS = await this.searchUsedUsers(exportedProcesses);
        exportedProcesses.ORGSTRS =
            await this.searchUsedOrganizations(exportedProcesses);
        exportedProcesses.DYNAMIC_TABLES =
            await this.getTemplateVariableDTs(exportedProcesses);
        exportedProcesses.CALCULATIONS_SCRIPTS =
            await this.getGlobalScripts(exportedProcesses);

        return UtilsService.recursivelyLowercaseJSONKeys(exportedProcesses);
    }
}

interface ExportedProcess {
    TEMPLATE_PROCESSES: TProcess[];
    VERSION: number;
    ROLES: Record<string, any> | null;
    EVENTS: Record<string, any> | null;
    USERS: Record<string, any> | null;
    ORGSTRS: Record<string, any> | null;
    DYNAMIC_TABLES: Record<string, any> | null;
    CALCULATIONS_SCRIPTS: Record<string, any> | null;
}

interface TProcess {
    TPROC_ID: number;
    TPROC_VERSION: number;
    TPROC_NAME: string;
    TPROC_OWNER_USER_ID: number;
    TPROC_LOCKED_BY_USER_ID: number | null;
    TPROC_LAST_CHANGED_BY_USER_ID: number;
    TPROC_LAST_CHANGED_DATE: string;
    TPROC_STATUS: string;
    TPROC_DESCRIPTION: string;
    TPROC_URL: string | null;
    TPROC_TVAR_ORDER: any | null;
    TPROC_URL_TAB_NAME: any | null;
    TPROC_VIS_ORGSTR_ID: any | null;
    TPROC_VIS_ROLE_ID: any | null;
    TPROC_DMS_VISIBILITY: any | null;
    TPROC_DEFAULT_CASE_NAME: any | null;
    ORG_ID: number;
    TPROC_NOTE: any | null;
    TPROC_HR_ROLE_ID: any | null;
    TPROC_NAME_CS: string | null;
    TPROC_NAME_EN: string | null;
    TPROC_NAME_SK: string | null;
    TPROC_DESCRIPTION_CS: string | null;
    TPROC_DESCRIPTION_EN: string | null;
    TPROC_DESCRIPTION_SK: string | null;
    TPROC_DEFAULT_CASE_NAME_CS: string | null;
    TPROC_DEFAULT_CASE_NAME_EN: string | null;
    TPROC_DEFAULT_CASE_NAME_SK: string | null;
    CUSTOM_VIEWS: ICustomView[];
    TEMPLATE_TASKS: TemplateTask[];
    TEMPLATE_LINKS: any[];
    TEMPLATE_VARIABLES: any[];
    TEMPLATE_PRINTS: any[];
    TEMPLATE_GRAPH: any[];
    HEADERS: Record<string, any>;
    CASE_STATUSES: any[];
    TEMPLATE_VARIABLE_LOVS: Record<string, any>;
    TEMPLATE_SECTIONS: any[];
}

interface TemplateTask {
    TTASK_ASSESMENT_ROLE_ID: number;
    email_notifs: EmailNotif[];
    TTASK_ASSESMENT_USER_ID: number;
    TTASK_ASSESMENT_ORGSTR_ID: number;
    TTASK_ASSESMENT_ORGSTR_CNST: number;
    TPROC_VIS_ORGSTR_ID: number;
    TTASK_ID: number;
}

type EmailNotifEnotType = "O" | "G" | "T" | "P" | "S" | "R" | "U";

interface EmailNotif {
    TTASK_ID: number;
    TTASK_ENOT_SUBJECT: string;
    TTASK_ENOT_BODY2: string;
    TTASK_ENOT_EXTERNAL: string;

    TTASK_ENOT_TGT_TYPE: EmailNotifEnotType;
    TTASK_ENOT_TGT: string;
    TTASK_ENOT_TGT_ORGSTR_ID: number;
    TTASK_ENOT_TGT_TTASK_ID: number;
    TTASK_ENOT_TGT_ROLE_ID: number;

    TTASK_ENOT_BLIND_TYPE: EmailNotifEnotType;
    TTASK_ENOT_BLIND_TTASK_ID: number;
    TTASK_ENOT_BLIND_TARGET: string;
    TTASK_ENOT_BLIND_ORGSTR_ID: number;
    TTASK_ENOT_BLIND_ROLE_ID: number;

    TTASK_ENOT_REPLY_TYPE: EmailNotifEnotType;
    TTASK_ENOT_REPLY_TARGET: string;
    TTASK_ENOT_REPLY_ORGSTR_ID: number;
    TTASK_ENOT_REPLY_TTASK_ID: number;
    TTASK_ENOT_REPLY_ROLE_ID: number;

    TTASK_ENOT_COPY_TYPE: EmailNotifEnotType;
    TTASK_ENOT_COPY_TARGET: string;
    TTASK_ENOT_COPY_ORGSTR_ID: number;
    TTASK_ENOT_COPY_TTASK_ID: number;
    TTASK_ENOT_COPY_ROLE_ID: number;
}

interface Event {
    rules: Rule[];
}

interface Rule {
    rule_variables: Record<string, any>;
}

interface RuleVariable {
    RDEFVAR_NAME: string;
    RDEFVAR_VALUE: string;
}

interface Header {
    HDR_VIS_ROLE_ID: number;
    HEADER_HR_ROLE_ID: number;
    header_roles: HeaderRole[];
    header_orgstrs: HeaderOrgStrs[];
}

interface HeaderRole {
    ROLE_ID: number;
}

interface HeaderOrgStrs {
    ORGSTR_ID: number;
}
