// @ts-nocheck
// @ts-nocheck
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../services/UtilsService";

export class JsonParser {
    constructor(json) {
        if (json == null || typeof json === "undefined" || json == "") {
            return;
        }

        json = JsonParser.unicodeToChar(json);

        this.json = json.trim();
        this.parsed = JSON.parse(json);

        if (!Array.isArray(this.parsed)) {
            this.keys = Object.keys(this.parsed);
        }
    }

    static replaceFirst(startIndex, src, substr, newSubstr) {
        const left = src.substr(0, startIndex);
        const right = src.substr(startIndex);

        return left + right.replace(substr, newSubstr);
    }

    static unicodeToChar(text) {
        if (!text || !text.replace) {
            return text;
        }
        return text.replace(/\\u[\dA-F]{4}/gi, (match) =>
            String.fromCharCode(parseInt(match.replace(/\\u/g, ""), 16)),
        );
    }

    /**
     * Search 'property' inside object. So it call object[property1][property2]. Property splited by '.'
     * @param {string} property data.property.property.
     * @param {Object} object Source data
     * @param defaultValue
     */
    static getProperty(property, object, defaultValue = "") {
        if (property === "@result") {
            return object;
        }
        if (!property) {
            return null;
        }

        const properties = property.split(new RegExp(/(?<!\\)[\.]/)); // Split by . but not \.
        let current = object;
        for (let i = 0; i < properties.length; i += 1) {
            let currentProperty = properties[i].replace(/\\/g, "");

            let required = false;
            let optional = false;

            // ! = required parametr,  = optional parametr.
            // ! = throw Error,  = skip Error
            if (currentProperty.startsWith("!")) {
                required = true;
                currentProperty = currentProperty.substr(1);
            } else if (currentProperty.startsWith("?")) {
                optional = true;
                currentProperty = currentProperty.substr(1);
            }

            if (typeof current[currentProperty] === "undefined") {
                const message = `Property '${currentProperty}' not found in result object. Searched property ${property}.`;
                if (required) {
                    // Parametr is must-have. Throw error.
                    throw new UserException(message, "INVALID_PROPERTY", {
                        object,
                    });
                } else if (optional) {
                    //  Parametr is optional
                    // Do not log anything, parametr is optional
                } else {
                    globalThis.tasLogger.error(message, {
                        object,
                    });
                }

                return defaultValue;
            }
            current = current[currentProperty];
        }
        return current;
    }

    toArrayOfObjects() {
        if (this.parsed == null || typeof this.parsed === "undefined") {
            return null;
        }

        if (Array.isArray(this.parsed)) {
            return this.parsed;
        }

        const self = this;
        this.keys.forEach((key) => {
            const value = this.parsed[key];

            const keyPos = self.json.indexOf(`"${key}"`) + `{"${key}"`.length;
            self.json = self.json.replace(`"${key}"`, `{"${key}"`);
            self.json = JsonParser.replaceFirst(
                keyPos,
                self.json,
                `"${value}"`,
                `"${value}"}`,
            );
        });

        self.json = self.json.substr(1, self.json.length - 2);
        self.json = `[${self.json}]`;

        return self.format(JSON.parse(this.json));
    }

    format(arr) {
        const out = [];
        arr.forEach((obj) => {
            const key = Object.keys(obj)[0];
            out.push({
                value: key,
                title: obj[key],
            });
        });
        return out;
    }

    static jsonToCsv(json) {
        return UtilsService.json2csv(json);
    }

    expandRegexPattern(text, regexPattern, expandExpression, data) {
        const m = text.match(regexPattern);
        if (m) {
            const g = expandExpression.replace(
                /&(\d)/g,
                (_a, paramIndex) => m[paramIndex],
            );

            return this.expandPattern(g, data);
        }
        return null;
    }

    expandPattern(expandExpression, expandData) {
        if (!expandData) {
            return expandExpression;
        }

        const expandSinglePattern = (expression, data) => {
            const subPatterns = [];
            const reg = /{([a-zA-Z|\.]+)}/g; // Find all {id}
            const m = expression.match(reg);
            if (m && m.length > 0) {
                for (let j = 0; j < m.length; j++) {
                    // Start and ends with {something}
                    if (
                        m[j].lastIndexOf("{") === 0 &&
                        m[j].indexOf("}") === m[j].length - 1
                    ) {
                        const dataPath = m[j].substr(1, m[j].length - 2);
                        try {
                            let rows = JsonParser.getProperty(
                                dataPath.replace(/\./g, ".?"),
                                data,
                            );
                            if (!Array.isArray(rows) && rows) {
                                rows = [rows];
                            }
                            for (let k = 0; k < rows.length; k++) {
                                const reg2 = new RegExp(m[j], "g"); // Find all {id}
                                const subPattern = expression.replace(
                                    reg2,
                                    rows[k],
                                );
                                subPatterns.push(subPattern);
                            }
                            break;
                        } catch (_err) {
                            return null;
                        }
                    }
                }
            } else {
                return expression;
            }
            return subPatterns;
        };

        const expandPatterns = (expressions, data, out) => {
            for (let i = 0; i < expressions.length; i += 1) {
                const expanded = expandSinglePattern(expressions[i], data);
                if (Array.isArray(expanded)) {
                    expandPatterns(expanded, data, out);
                } else if (expanded) {
                    out.push(expanded);
                }
            }
            return out;
        };

        return expandPatterns([expandExpression], expandData, []);
    }
}
