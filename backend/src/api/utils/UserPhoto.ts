import { Jim<PERSON> } from "jimp";
import sharp from "sharp";
import path from "path";
import fs from "fs";
import crypto from "node:crypto";

export interface PhotoMeta {
    width: number;
    height: number;
    extension: string;
}

const SUPPORTED_EXTENSIONS = new Set([
    "jpg",
    "webp",
    "gif",
    "tiff",
    "png",
    "jpeg",
]);

export class UserPhoto {
    static async resizeImage(
        sourcePath: string,
        destinationPath: string,
        outputMeta: PhotoMeta,
    ): Promise<string> {
        const { width, height, extension } = outputMeta;

        if (!extension || !SUPPORTED_EXTENSIONS.has(extension.toLowerCase())) {
            throw new Error(
                `Missing extension or extension: ${extension} not supported`,
            );
        }

        await sharp(sourcePath)
            .resize(width, height)
            // @ts-expect-error ts(2345)
            .toFormat(extension)
            .toFile(destinationPath);

        return destinationPath;
    }

    static async convertToJpg(
        inPhotoPath: string,
        outPhotoPath: `${string}.${string}`,
        width: number,
        height: number,
    ): Promise<string> {
        const image = await Jimp.read(inPhotoPath);
        await image.resize({ w: width, h: height }).write(outPhotoPath);
        return outPhotoPath;
    }

    static async convertToWebp(
        inPhotoPath: string,
        outPhotoPath: string,
        width: number,
        height: number,
    ): Promise<string> {
        const isBmp = inPhotoPath.endsWith(".bmp");
        const tempJpgPath: `${string}.${string}` = `${outPhotoPath}.jpg`;

        if (isBmp) {
            await this.convertBmpToJpg(inPhotoPath, tempJpgPath);
        }

        await sharp(isBmp ? tempJpgPath : inPhotoPath)
            .resize(width, height)
            .toFormat("webp")
            .toFile(outPhotoPath);

        if (isBmp) {
            fs.unlinkSync(tempJpgPath);
        }
        return outPhotoPath;
    }

    static async convertBmpToJpg(
        inPhotoPath: string,
        outPhotoPath: `${string}.${string}`,
    ): Promise<string> {
        const image = await Jimp.read(inPhotoPath);
        await image.write(outPhotoPath);
        return outPhotoPath;
    }

    static async getImageDimensions(
        inPhotoPath: string,
    ): Promise<{ width: number; height: number; aspectRatio: number }> {
        const imageMeta = await sharp(inPhotoPath).metadata();
        const { width, height } = imageMeta;

        if (width === undefined || height === undefined) {
            throw new Error("Unable to determine image dimensions");
        }

        const aspectRatio = width / height;
        return { width, height, aspectRatio };
    }

    static async generatePhoto(
        sourcePath: string,
        userId: number,
        photoMeta: PhotoMeta,
    ): Promise<{ photoName: string; photoPath: string }> {
        const { width, height, extension } = photoMeta;
        const photoName = this.generatePhotoName(width, height, extension);
        const photoPath = this.getPhotoPath(photoName);
        globalThis.tasLogger.info(
            `Converting user photo from '${sourcePath}' to ${photoName}. Size [${width}, ${height}]`,
            { USER_ID: userId },
        );

        try {
            await this.resizeImage(sourcePath, photoPath, photoMeta);
        } catch (err) {
            globalThis.tasLogger.info(
                `Could not convert user photo to jpg. Input file ${photoPath}`,
                { USER_ID: userId, err },
            );
            throw err;
        }
        globalThis.tasLogger.info(
            `Converted user photo from '${photoPath}' to ${photoName}.`,
            { USER_ID: userId },
        );

        return { photoName, photoPath };
    }

    static getPhotoPath(photoName: string): string {
        return path.join(
            globalThis.dynamicConfig.paths.userPhotoPath,
            photoName,
        );
    }

    static generatePhotoName(
        width: number,
        height: number,
        extension: string,
    ): string {
        return path.join(
            `${crypto.randomUUID()}-${width}x${height}.${extension}`,
        );
    }

    static async getCappedPictureSizes(photoPath: string) {
        const { maxWidth } = globalThis.dynamicConfig.userPhoto;

        const { width, height, aspectRatio } =
            await this.getImageDimensions(photoPath);
        if (width <= maxWidth) {
            return [width, height];
        }
        return [maxWidth, Math.floor(maxWidth * aspectRatio)];
    }
}
