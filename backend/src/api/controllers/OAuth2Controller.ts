// @ts-nocheck
// @ts-nocheck
import { Service } from "typedi";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { OAuth2Authority } from "../../service/authorization/authorities/OAuth2Authority";
import { Auth } from "../../service/authorization/Auth";

@Service()
export class OAuth2Controller {
    constructor(private auth: Auth) {}

    async callback(req, res) {
        let authEntity;
        try {
            await globalThis.database.transaction(async (trx) => {
                const state = OAuth2Authority.decodeState(req.query.state);
                const authId = state.id || null;

                const authRepo = globalThis.orm.repo("authorization");
                authEntity = await authRepo.get(authId);
                const authModule = await this.auth.instantiateAuthModule(
                    authEntity,
                    trx,
                );
                await authModule.callback(req, res);
            });
        } catch (err) {
            this.tasLogger.error(`Unknown error in OAuth2. ${err.message}`, {
                err,
            });
            try {
                const params = JSON.parse(authEntity.AUTH_PARAMS);
                if (!params.frontend) {
                    throw new InternalException(
                        "No frontend url in AUTH_PARAMS",
                    );
                }
                res.redirect(
                    `${params.frontend}/authenticate?error=LOGIN_ERROR`,
                );
            } catch (error) {
                this.tasLogger.error(
                    `Unknown error in OAuth2. ${error.message}`,
                    { err: error },
                );
                res.send({
                    message: "Neznámá chyba, kontaktujte Administrátora.",
                });
            }
        }
    }
}
