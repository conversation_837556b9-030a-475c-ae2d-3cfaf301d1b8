// @ts-nocheck
import { Service } from "typedi";
import { MailOptionsParams } from "../../client/mail/BaseMailClient";
import { Auth } from "../../service/authorization/Auth";
import { TasLogger } from "../../utils/logger/TasLogger";
import { OIDCStrategy } from "passport-azure-ad";

@Service()
export class TestController {
    constructor(
        private auth: Auth,
        private tasLogger: TasLogger,
    ) {}

    get(_req, res) {
        this.tasLogger.info("GET");
        return res.send({ result: true });
    }

    async post(req, res) {
        this.tasLogger.info("POST");
        const azureAdModule = await this.auth.instantiateAuthModule(
            "AzureAd",
            globalThis.database,
        );
        new OIDCStrategy(azureAdModule.strategy, () => {
            //
        })._validateResponse(
            req.body,
            null,
            req,
            () => {
                //
            },
            (jwtStr, jwtClaims) => {
                this.tasLogger.info(jwtStr, "jwtStr");
                this.tasLogger.info(jwtClaims, "jwtClaims");
            },
        );
        return res.redirect(globalThis.dynamicConfig.frontendUrl);
    }

    unlimitedRequest() {
        //
    }

    async sendTestMail(req, res) {
        const user = await this.auth.getUserData(req);
        const { recipient, sub } = req.body; // user.USER_EMAIL
        let { content } = req.body;

        if (content) {
            try {
                content = content.replace(/(?<!>)\n/gi, "<br />");
            } catch (err) {
                return res.status((err as any).status || 500).send(err);
            }
        }

        const mailOptionsParams: MailOptionsParams = {
            addresses: recipient,
            subject: sub,
            bccOnMoreAddresses: true,
            ignoreError: true,
        };

        try {
            await globalThis.routerMail.sendEmailViaClient(
                "iTaskNotification",
                mailOptionsParams,
                content,
                user.LANGUAGE,
                user.DATE_FORMAT,
            );
            return res.send({ result: true });
        } catch (err) {
            return res.status((err as any).status || 500).send(err);
        }
    }
}
