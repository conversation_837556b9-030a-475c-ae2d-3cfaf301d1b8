// @ts-nocheck
import { FastifyRequest, FastifyReply } from "fastify";
import { Service } from "typedi";

function sendStatus(res: FastifyReply, isOnline: boolean) {
    res.send(isOnline ? "status:OK" : "status:CRITICAL");
}

function withTimeout(promise, timeoutMs) {
    let timeoutHandle;

    const timeoutPromise = new Promise((_resolve, reject) => {
        timeoutHandle = setTimeout(
            () => reject(new Error("Connection timed out")),
            timeoutMs,
        );
    });

    return Promise.race([promise, timeoutPromise]).finally(() =>
        clearTimeout(timeoutHandle),
    );
}

@Service()
export class StatusController {
    get(_req: FastifyRequest, res: FastifyReply) {
        return sendStatus(res, true);
    }

    async dbUsers(_req, res) {
        const dbUtils = globalThis.orm.db;
        const dbType = globalThis.dynamicConfig.db.client;
        const timeoutDuration =
            globalThis.dynamicConfig.database.getStatusTimeout;

        try {
            if (dbType === "mssql") {
                const dbStatus = await withTimeout(
                    dbUtils.getDbStatus(),
                    timeoutDuration,
                );
                sendStatus(res, dbStatus === "ONLINE");
            } else {
                await withTimeout(
                    globalThis.orm.repo("user").checkDb(),
                    timeoutDuration,
                );
                sendStatus(res, true);
            }
        } catch (error) {
            globalThis.tasLogger.warning("Get database status failed", {
                error,
            });
            sendStatus(res, false);
        }
    }
}
