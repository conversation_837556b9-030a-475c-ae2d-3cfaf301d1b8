import _ from "lodash";

import { BaseController } from "./BaseController";
import { VariableLovRepository } from "../orm/repository/VariableLovRepository";
import { Service } from "typedi";
import { Auth } from "../../service/authorization/Auth";
import { LockClient } from "../../client/cache/lockClient";
import { FastifyReply, FastifyRequest } from "fastify";
import { Rest } from "../services/Rest";

/**
 * Created by <PERSON><PERSON> on 7.9 2015.
 */

@Service()
export class TaskVariablesController {
    constructor(
        private auth: Auth,
        private readonly lockClient: LockClient,
    ) {}

    /**
     * For filling task form with variables.
     *
     * @param req
     * @param res
     * @returns {Promise<void>}
     */
    async get(
        req: FastifyRequest,
        res: FastifyReply,
        isArchived?: boolean,
    ): Promise<void> {
        try {
            const archived = isArchived === true;
            const user = await this.auth.getUserData(req);

            const repo = globalThis.orm.repo(
                archived ? "archivedVariable" : "variable",
            );
            repo.setLocalization(user.LANGUAGE);

            const varLovRepo = globalThis.orm.repo(
                archived ? "archivedVariableLov" : "variableLov",
            );
            const primaryCol = repo.entity.primaryColumn;
            // prepare data
            // @ts-expect-error fastify types
            const { itaskId } = req.params;
            const itask = await globalThis.orm
                .repo(archived ? "archivedInstanceTask" : "instanceTask")
                .get(itaskId, ["IPROC_ID"]);
            const version = await globalThis.orm
                .repo(
                    archived
                        ? "archivedInstanceProcessVersion"
                        : "instanceProcessVersion",
                )
                .getProcessVersion(itask.IPROC_ID);
            const coll = await repo.getForTask(
                "*",
                itaskId,
                version.TTASKVARUSG_VERSION || 0,
            );
            coll.knex
                .orderBy("AXIS_Y", "asc")
                .orderBy("AXIS_X", "asc")
                .orderBy("ITASKVARUSG_ID", "asc");

            const [items, totalCount, startTime] =
                await BaseController.handleGetRequest(req, coll, {
                    returnEntities: true,
                });
            // retrieving lov data
            const varLovs = await varLovRepo
                .getForVariables(
                    ["*"],
                    // @ts-expect-error types
                    (
                        await repo.getForTask(
                            "IVAR_ID",
                            itaskId,
                            version.TTASKVARUSG_VERSION || 0,
                        )
                    ).knex.whereIn("TVAR_TYPE", ["LT", "LD", "LN"]),
                )
                .collectAssoc("IVAR_ID"); // {56: [{row}, {row}]}

            const joinedItems = await VariableLovRepository.joinLov(
                items,
                varLovs,
            );

            const clonedItems = await varLovRepo.fillLovValues(joinedItems);

            // translate id to names (orgs, users, roles)
            const tvarRepo = globalThis.orm.repo("templateVariable");
            await tvarRepo.fillValues(clonedItems, ["TVAR_META"]);

            if (Array.isArray(clonedItems) && clonedItems.length > 0) {
                const dmsFileRepo = globalThis.orm.repo(
                    archived ? "archivedDmsFile" : "dmsFile",
                );
                for (const item of clonedItems) {
                    if (item.IVAR_ATTRIBUTE === "F") {
                        const fileNames = item.value;
                        const iprocId = item.IPROC_ID;
                        const files = await dmsFileRepo.getByName(
                            iprocId,
                            fileNames,
                        );

                        if (Array.isArray(files) && files.length > 0) {
                            item.IVAR_FILES = files.map((file) => ({
                                name: file.NAME,
                                dmsf_id: file.DMSF_ID,
                                file_type: file.FILE_TYPE,
                            }));
                        }
                    }
                }
            }
            const out: any[] = [];
            for (const item of clonedItems) {
                const outItem = await Rest.restifyEntity(
                    item,
                    primaryCol,
                    req.url,
                    (ent: any) => {
                        let filteredList = null;
                        if (ent.IVAR_BIG_VALUE && ent.IVAR_TYPE === "DL") {
                            try {
                                filteredList = JSON.parse(ent.IVAR_BIG_VALUE);
                            } catch (_ignored) {
                                filteredList = ent.IVAR_BIG_VALUE;
                            }
                        }

                        let ivarValue = ent.value;
                        let ivarDtIndex = ent.raw.IVAR_DT_INDEX;

                        if (ent.IVAR_TYPE === "DT" && ent.IVAR_MULTI === "X") {
                            let ivarMultiSelected;
                            try {
                                ivarMultiSelected = JSON.parse(
                                    ent.IVAR_MULTI_SELECTED,
                                );
                            } catch (_ignored) {
                                ivarMultiSelected = ent.IVAR_MULTI_SELECTED;
                            }

                            ivarValue = [];
                            ivarDtIndex = [];
                            if (Array.isArray(ivarMultiSelected)) {
                                ivarMultiSelected.forEach((obj) => {
                                    if (Object.entries(obj).length) {
                                        const [key, val] =
                                            Object.entries(obj)[0];
                                        ivarDtIndex.push(key);
                                        ivarValue.push(val);
                                    }
                                });
                            }
                        }

                        const out: Record<string, any> = {
                            ivar_id: ent.IVAR_ID,
                            ivar_name: ent.IVAR_NAME,
                            ivar_files: ent.IVAR_FILES,
                            ivar_filtered_list: filteredList,
                            ivar_type: ent.IVAR_TYPE,
                            ivar_attribute: ent.IVAR_ATTRIBUTE,
                            ivar_usage: ent.raw.IVAR_USAGE,
                            ivar_value: ivarValue,
                            ivar_group: "not implemented",
                            ivar_lov: ent.getLovData(),
                            ivar_lov_value: ent.lovValue,
                            dlist_name: ent.DLIST_NAME,
                            tvar_meta: ent.TVAR_META,
                            tvar_id: ent.TVAR_ID,
                            tvar_design_version: ent.raw.TVAR_DESIGN_VERSION,
                            tvar_date_without_time:
                                ent.raw.TVAR_DATE_WITHOUT_TIME,
                            tvar_name: ent.raw.TVAR_NAME,
                            tvar_label: ent.raw.TVAR_LABEL,
                            tvar_tooltip: ent.raw.TVAR_TOOLTIP,
                            ivar_multi: ent.raw.IVAR_MULTI,
                            ivar_dt_index: ivarDtIndex,
                            ivar_col_index: ent.raw.IVAR_COL_INDEX,
                            dt_id: ent.raw.DT_ID,
                            axis_x:
                                typeof ent.raw.AXIS_X !== "undefined" &&
                                ent.raw.AXIS_X !== null
                                    ? ent.raw.AXIS_X
                                    : null,
                            axis_y: ent.raw.AXIS_Y || null,
                        };

                        // IVAR translations
                        let attrs = ent.attributes();
                        const attrNames = Object.keys(attrs);
                        attrNames.forEach((attrName) => {
                            if (attrs[attrName].translated) {
                                out[attrName.toLowerCase()] = ent[attrName];
                            }
                        });

                        // TVAR translations
                        const tvarEntity = globalThis.orm
                            .repo("templateVariable")
                            .getEntity();
                        attrs = tvarEntity.attributes();
                        Object.keys(attrs).forEach((attrName) => {
                            if (attrs[attrName].translated) {
                                out[attrName] = ent.raw[attrName];
                            }
                        });

                        // LOV translations
                        globalThis.dynamicConfig.langs.forEach(
                            (lang: string) => {
                                out[`ivar_lov_${lang}`] = ent.getLovData(lang);
                            },
                        );

                        return out;
                    },
                );

                out.push(outItem);
            }

            const result = Rest.restifyData(out, {
                primaryCol,
                // @ts-expect-error fastify types
                single: !!req.params.id,
                totalCount,
                startTime,
                routePath: req.routeOptions.url,
                reqUrl: req.url,
                locale: user.LANGUAGE,
            });

            return BaseController.handleResponse(req, res, result);
        } catch (err) {
            return BaseController.handleErrorResponse(req, res, err);
        }
    }

    async getArchived(req: FastifyRequest, res: FastifyReply) {
        return await this.get(req, res, true);
    }

    async post(req: FastifyRequest, res: FastifyReply) {
        // @ts-expect-error fastify types
        const { itaskId } = req.params;
        const taskRepo = globalThis.orm.repo("instanceTask");
        const user = await this.auth.getUserData(req);
        let task = null;
        // check rigths before variable saving t3f-945
        try {
            task = await taskRepo.hasTaskRights(
                user.USER_ID,
                itaskId,
                "S",
                true,
            );
        } catch (err) {
            return await res.status((err as any).status || 500).send(err);
        }
        const lock = await this.lockClient.getLock(
            `PROCESS:FLOW-${task.IPROC_ID}`,
        );
        const entities: any[] = [];
        try {
            await globalThis.database.transaction(async (trx) => {
                const data = Array.isArray(req.body) ? req.body : [req.body];
                const snapRepo = globalThis.orm.repo("variableSnap", trx);
                const varRepo = globalThis.orm.repo("variable", trx);
                const itaskIds = _.map(data, "id");
                await varRepo.checkPostVariableRights(
                    task.IPROC_ID,
                    itaskId,
                    itaskIds,
                );
                // Update variables
                for (const item of data) {
                    // Update value
                    const varEntity = await varRepo.get(item.id);
                    varEntity.value = item.value;
                    await varRepo.store(varEntity);
                    entities.push(varEntity);
                    // Update snapshots ...
                    if (task.isMultiinstance()) {
                        await snapRepo.updateAllSnaps(varEntity, itaskId);
                    }
                }
                // Generate history
                await globalThis.orm
                    .repo("instanceVariableHistory", trx)
                    .generateForTask(
                        task,
                        entities,
                        "User updated variables",
                        user.USER_ID,
                        user.isViced() ? null : user.USER_ID,
                    );
            });
            await lock.release();
            return await res.send({ result: true });
        } catch (err) {
            await lock.release();
            return await res.status((err as any).status || 500).send(err);
        }
    }
}
