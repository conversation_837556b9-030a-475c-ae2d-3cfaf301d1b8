// @ts-nocheck
// @ts-nocheck
import { Service } from "typedi";
import { BaseController } from "./BaseController";
import { StatisticsFacade } from "../orm/facade/StatisticsFacade";
import { Rest } from "../services/Rest";

@Service()
export class StatisticsController {
    finishedTasksAggregated(req, res) {
        const statisticsFacade = new StatisticsFacade(globalThis.database);
        statisticsFacade
            .getFinishedTasksAggregated(req)
            .then((data) =>
                Rest.restifyData(data, {
                    primaryCol: null,
                    single: false,
                    totalCount: null,
                    startTime: new Date(),
                    routePath: req.routeOptions.url,
                    reqUrl: req.url,
                }),
            )
            .then((result) => {
                // make json
                BaseController.handleResponse(req, res, result);
            })
            .catch((err) => {
                // handle errors
                BaseController.handleErrorResponse(req, res, err);
            });
    }

    finishedTasks(req, res) {
        const statisticsFacade = new StatisticsFacade(globalThis.database);
        statisticsFacade
            .getFinishedTasks(req, res)
            .then((data) =>
                Rest.restifyData(data, {
                    primaryCol: null,
                    single: false,
                    totalCount: null,
                    startTime: new Date(),
                    routePath: req.routeOptions.url,
                    reqUrl: req.url,
                }),
            )
            .then((result) => {
                // make json
                BaseController.handleResponse(req, res, result);
            })
            .catch((err) => {
                // handle errors
                BaseController.handleErrorResponse(req, res, err);
            });
    }

    async uniqueItaskNames(req, res) {
        const statisticsFacade = new StatisticsFacade(globalThis.database);
        await statisticsFacade.getUniqueItaskNames(req, res).then(
            async (collection) =>
                // collection.filteringColumns = {};
                // collection.orderingColumns = _.cloneDeep(collection.filteringColumns);

                // prepare data
                await BaseController.handleGetRequest(req, collection)
                    .then((items) => items)
                    .then((result) => Promise.all(result))
                    .then(([items, _totalCount, startTime]) =>
                        // prepare result for response
                        Rest.restifyData(items, {
                            primaryCol: null,
                            single: false,
                            totalCount: null,
                            startTime,
                            routePath: req.routeOptions.url,
                            reqUrl: req.url,
                        }),
                    )
                    .then((result) =>
                        // make json
                        BaseController.handleResponse(req, res, result),
                    )
                    .catch((err) =>
                        // handle errors
                        BaseController.handleErrorResponse(req, res, err),
                    ),
        );
    }
}
