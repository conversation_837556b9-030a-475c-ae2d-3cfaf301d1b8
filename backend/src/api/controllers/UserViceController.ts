// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseController } from "./BaseController";
import { Service } from "typedi";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { MailOptionsParams } from "../../client/mail/BaseMailClient";
import { Auth } from "../../service/authorization/Auth";
import { Rest } from "../services/Rest";
import * as USER_PARAMETERS from "../orm/entity/const/userParameterConsts";
import { AuthorizedUser } from "../../service/authorization/AuthorizedUser";

@Service()
export class UserViceController {
    constructor(private auth: Auth) {}

    async usersToVice(req, res) {
        const user = await this.auth.getUserData(req);
        const collection = await globalThis.orm
            .repo("UserVice")
            .getUsersToVice(user.USER_ID);
        const primaryCol = globalThis.orm.repo("user").entity.primaryColumn;
        this.handleCollectionResponse(req, res, collection, primaryCol);
    }

    async enabled(req, res) {
        const user = await this.auth.getUserData(req);
        const collection = await this.getUserVicedCollection(user.USER_ID);
        return await this.get(req, res, collection);
    }

    async all_enabled(req, res) {
        const user = await this.auth.getUserData(req);

        if (!user.isHRManager()) {
            return res
                .status(400)
                .send(
                    new UserException(
                        "Lack of permissions!",
                        "LACK_OF_PERMISSIONS",
                    ),
                );
        }

        const collection = await this.getUserVicedCollection();
        return await this.get(req, res, collection);
    }

    async get(req, res, collection) {
        const primaryCol = globalThis.orm.repo("userVice").entity.primaryColumn;
        return await this.handleCollectionResponse(
            req,
            res,
            collection,
            primaryCol,
        );
    }

    async getUserVicedCollection(userId = null) {
        const collection = globalThis.orm
            .repo("UserVice")
            .getUsersViced(userId, false);
        collection.filteringColumns = {
            USER_VICE_FULL_NAME: {
                type: "string",
                key: `VICE.USER_LAST_NAME ${globalThis.orm.db.concatColumns()} VICE.USER_FIRST_NAME`,
            },
            USER_VICE_DISPLAY_NAME: {
                type: "string",
                key: "VICE.USER_DISPLAY_NAME",
            },
            USER_FULL_NAME: {
                type: "string",
                key: `U.USER_LAST_NAME ${globalThis.orm.db.concatColumns()} U.USER_FIRST_NAME`,
            },
            USER_DISPLAY_NAME: { type: "string", key: "U.USER_DISPLAY_NAME" },
        };
        collection.orderingColumns = _.cloneDeep(collection.filteringColumns);
        return collection;
    }

    async handleCollectionResponse(req, res, collection, primaryCol) {
        try {
            const [items, totalCount, startTime] =
                await BaseController.handleGetRequest(req, collection);
            const result = Rest.restifyData(items, {
                primaryCol,
                single: !!req.params.id,
                totalCount,
                startTime,
                routePath: req.routeOptions.url,
                reqUrl: req.url,
            });
            BaseController.handleResponse(req, res, result);
        } catch (err) {
            BaseController.handleErrorResponse(req, res, err);
        }
    }

    async post(req, res) {
        let user;
        try {
            user = await this.auth.getUserData(req);
            globalThis.tasLogger.info(
                `Updating substitutes of user '${user.USER_NAME} with id ${user.USER_ID}'`,
                {
                    actorUserId: user.USER_ID,
                },
            );

            const data = req.body;
            const dataArr = Array.isArray(data) ? data : [data];
            const useOldStyleWay = !Array.isArray(data);

            dataArr.map((dataItem) => this.validateRequestData(dataItem, user));
            const repo = globalThis.orm.repo("userVice");
            const userId =
                dataArr[0] && dataArr[0].user_id
                    ? dataArr[0].user_id
                    : user.USER_ID;
            const userVices = await repo.getUsersViced(userId).fetchAll();

            // items without uv_id are new vices to add
            const vicesToAdd = dataArr.filter((vice) => !vice.uv_id);
            const vicesToEdit = dataArr.filter((vice) => vice.uv_id);

            // remove vices that are still active and that don't have a corresponding uv_id in the incoming array
            const vicesToRemove = userVices.filter(
                (uv) =>
                    !dataArr.find(
                        (dataItem) =>
                            dataItem.uv_id === uv.UV_ID ||
                            dataItem.id === uv.UV_ID,
                    ) &&
                    new Date(uv.UV_TO) >=
                        new Date(new Date().setHours(0, 0, 0, 0)),
            );

            if (useOldStyleWay) {
                const { id } = await this.addUserViceAndSendEmail(
                    data,
                    user,
                    userId,
                );
                return res.send({ result: true, id });
            }

            for (const vice of vicesToRemove) {
                await this.removeUserViceAndSendEmails(user, vice.UV_ID);
            }

            const editedUvIds = [];
            for (const vice of vicesToEdit) {
                const foundVice = userVices.find(
                    (uv) => uv.UV_ID === vice.uv_id,
                );
                const hasChanged =
                    foundVice &&
                    (foundVice.USER_ID_VICE !== vice.user_id_vice ||
                        new Date(foundVice.UV_FROM).getTime() !==
                            new Date(vice.from).getTime() ||
                        new Date(foundVice.UV_TO).getTime() !==
                            new Date(vice.to).getTime());
                const onlyViewChanged =
                    !hasChanged &&
                    foundVice &&
                    foundVice.VIEW_ONLY !== (vice.view_only || "N");

                if (hasChanged || onlyViewChanged) {
                    const { id } = await this.editUserViceAndSendEmail(
                        vice,
                        user,
                        userId,
                        onlyViewChanged,
                    );
                    editedUvIds.push(id);
                }
            }

            const addedUvIds = [];
            for (const vice of vicesToAdd) {
                const { id } = await this.addUserViceAndSendEmail(
                    vice,
                    user,
                    userId,
                );
                addedUvIds.push(id);
            }
            globalThis.tasLogger.info(
                `Updated substitutes of user '${user.USER_NAME}' successfully`,
                {
                    userId: user.USER_ID,
                    removedUvIds: vicesToRemove.map((a) => a.UV_ID),
                    addedUvIds,
                    editedUvIds,
                },
            );

            return res.send({ result: true });
        } catch (err) {
            globalThis.tasLogger.error(
                `Updating substitutes of user '${user?.USER_NAME}' was not successful`,
                {
                    userId: user?.USER_ID,
                    err,
                },
            );

            return res.status((err as any).status || 500).send(err);
        }
    }

    async addHrVices(req: Request, res: Response): Promise<void> {
        let user;
        try {
            user = await this.auth.getUserData(req);
            const data = req.body;
            globalThis.tasLogger.info(`Creating substitutes by HR Manager`, {
                userId: user.USER_ID,
                data,
            });
            const dataArr = Array.isArray(data) ? data : [data];

            dataArr.map((dataItem) => this.validateRequestData(dataItem, user));

            const vices = [];
            for (const vice of dataArr) {
                const { id } = await this.addUserViceAndSendEmail(
                    vice,
                    user,
                    vice.user_id,
                );
                vices.push({
                    ...vice,
                    uv_id: id,
                });
            }

            globalThis.tasLogger.info(
                `Created substitutes by HR Manager successfully`,
                {
                    userId: user.USER_ID,
                    vices,
                },
            );
            res.send({ result: true });
        } catch (err) {
            globalThis.tasLogger.error(
                `Creating substitutes by HR Manager was not successful`,
                {
                    userId: user?.USER_ID,
                    err,
                },
            );
            res.status((err as any).status || 500).send(err);
        }
    }

    async editHrVice(req: Request, res: Response): Promise<void> {
        let user;
        try {
            user = await this.auth.getUserData(req);
            const data = req.body;
            globalThis.tasLogger.info(`Changing substitute by HR Manager`, {
                userId: user.USER_ID,
                data,
            });

            await this.validateRequestData(data, user);

            const repo = globalThis.orm.repo("userVice");
            const entity = await repo.get(data.uv_id);

            const userRepo = globalThis.orm.repo("user");
            const oldViced = await userRepo.get(entity.USER_ID);
            const oldVice = await userRepo.get(entity.USER_ID_VICE);
            const newViced = await userRepo.get(data.user_id);
            const newVice = await userRepo.get(data.user_id_vice);

            const { user_id: userId, from, to } = data;
            entity.USER_ID = data.user_id;
            entity.USER_ID_VICE = data.user_id_vice;
            entity.UV_FROM = new Date(data.from);
            entity.UV_TO = new Date(data.to);
            entity.VIEW_ONLY = data.view_only;
            entity.UV_ENABLED = data.uv_enabled;

            const conn = repo.getUsersViced(userId, false).knex;
            this.addDateRangeCondition(conn, from, to);
            const viced = await conn;
            const viceOrVicedChanged = viced.filter(
                (vc) =>
                    (vc.USER_ID === data.user_id ||
                        vc.USER_ID_VICE === data.user_id_vice) &&
                    vc.UV_ID !== data.uv_id,
            );

            if (
                Array.isArray(viceOrVicedChanged) &&
                viceOrVicedChanged.length > 0
            ) {
                throw new UserException(
                    "Substitute interval overlap.",
                    "BAD_REQUEST",
                );
            }

            const id = await repo.store(entity);

            globalThis.tasLogger.info(
                `Changed substitution ${user.isHRManager() ? "by HR manager with id " + user.USER_ID : "by user"}.
            User ${entity.USER_ID_VICE} substituting user ${entity.USER_ID}.`,
                {
                    actorUserId: user.USER_ID,
                    userId: entity.USER_ID,
                    viceUserId: entity.USER_ID_VICE,
                    from: entity.UV_FROM,
                    to: entity.UV_TO,
                },
            );

            const emailSettingsRemoveVice = {
                templateKey: "userViceRemove",
                emailSubject: "userViceRemove",
            };

            const emailSettingsRemoveViced = {
                templateKey: "userVicedRemove",
                emailSubject: "userViceRemove",
            };

            if (oldViced.USER_ID !== newViced.USER_ID) {
                await this.sendViceEmail(
                    user,
                    oldViced,
                    emailSettingsRemoveVice,
                    {
                        vice_from: from,
                        vice_to: to,
                        user_name_hr: user.isHRManager()
                            ? user.USER_DISPLAY_NAME
                            : null,
                        user_name: oldVice.USER_DISPLAY_NAME,
                    },
                );
            }

            if (oldVice.USER_ID !== newVice.USER_ID) {
                await this.sendViceEmail(
                    user,
                    oldVice,
                    emailSettingsRemoveViced,
                    {
                        vice_from: from,
                        vice_to: to,
                        user_name_hr: user.isHRManager()
                            ? user.USER_DISPLAY_NAME
                            : null,
                        user_name_vice: oldViced.USER_DISPLAY_NAME,
                    },
                );
            }

            const emailSettings = {
                templateKeyVice: "userViceChange",
                templateKeyViced: "userVicedChange",
                emailSubject: "userViceChange",
            };

            await this.sendViceEmails(
                user,
                data.user_id_vice,
                userId,
                { from, to },
                emailSettings,
            );

            globalThis.tasLogger.info(
                `Changed substitute by HR Manager successfully`,
                {
                    userId: user.USER_ID,
                    data,
                },
            );
            res.send({ result: true, id });
        } catch (err) {
            globalThis.tasLogger.error(
                `Changing substitute by HR Manager was not successful`,
                {
                    userId: user?.USER_ID,
                    err,
                },
            );
            res.status((err as any).status || 500).send(err);
        }
    }

    async editUserViceAndSendEmail(
        data: Record<string, any>,
        user: AuthorizedUser,
        userId: number,
        onlyViewChanged: boolean,
    ) {
        const repo = globalThis.orm.repo("userVice");
        const from = new Date(data.from);
        const to = new Date(data.to);

        const { entity } = repo;

        entity.UV_ID = data.uv_id;
        entity.USER_ID = userId;
        entity.USER_ID_VICE = data.user_id_vice;
        entity.UV_FROM = new Date(data.from);
        entity.UV_TO = new Date(data.to);
        entity.VIEW_ONLY = data.view_only ? data.view_only : "N";

        const conn = repo.getUsersViced(userId, false).knex;
        this.addDateRangeCondition(conn, from, to);
        const viced = await conn;
        if (
            Array.isArray(viced) &&
            viced.some((vc) => vc.UV_ID !== entity.UV_ID)
        ) {
            throw new UserException(
                "Substitute interval overlap.",
                "BAD_REQUEST",
            );
        }
        const id = await repo.store(entity);

        globalThis.tasLogger.info(
            `Changed substitution ${user.isHRManager() ? "by HR manager with id " + entity.USER_ID : "by user"}.
        User ${entity.USER_ID_VICE} substituting user ${entity.USER_ID}.`,
            {
                actorUserId: user.USER_ID,
                userId: entity.USER_ID,
                viceUserId: entity.USER_ID_VICE,
                from: entity.UV_FROM,
                to: entity.UV_TO,
                view_only: entity.VIEW_ONLY,
            },
        );

        // VIEW_ONLY value is not shown in the email so if it's the only change, no need to send a notification
        if (!onlyViewChanged) {
            const emailSettings = {
                templateKeyVice: "userViceChange",
                templateKeyViced: "userVicedChange",
                emailSubject: "userViceChange",
            };
            await this.sendViceEmails(
                user,
                data.user_id_vice,
                userId,
                { from, to },
                emailSettings,
            );
        }

        return { result: true, id };
    }

    async addUserViceAndSendEmail(data, user, userId) {
        const repo = globalThis.orm.repo("userVice");
        const from = new Date(data.from);
        const to = new Date(data.to);

        const { entity } = repo;
        entity.UV_ID = data.id ? data.id : null;
        entity.USER_ID = userId;
        entity.USER_ID_VICE = data.user_id_vice;
        entity.UV_FROM = from;
        entity.UV_TO = to;
        entity.VIEW_ONLY = data.view_only ? data.view_only : "N";

        const conn = repo.getUsersViced(userId, false).knex;
        this.addDateRangeCondition(conn, from, to);
        const viced = await conn;
        if (Array.isArray(viced) && viced.length > 0) {
            throw new UserException(
                "Substitute interval overlap.",
                "BAD_REQUEST",
            );
        }
        const id = await repo.store(entity);

        globalThis.tasLogger.info(
            `Created substitution ${user.isHRManager() ? "by HR manager with id " + user.USER_ID : "by user"}.
            User ${entity.USER_ID_VICE} substituting user ${entity.USER_ID}.`,
            {
                actorUserId: user.USER_ID,
                userId: entity.USER_ID,
                viceUserId: entity.USER_ID_VICE,
                from: entity.UV_FROM,
                to: entity.UV_TO,
                view_only: entity.VIEW_ONLY,
            },
        );

        const emailSettings = {
            templateKeyVice: "userViceSet",
            templateKeyViced: "userVicedSet",
            emailSubject: "userViceSet",
        };
        await this.sendViceEmails(
            user,
            data.user_id_vice,
            userId,
            { from, to },
            emailSettings,
        );

        return { result: true, id };
    }

    async remove(req, res) {
        const uvId = req.params.id;
        let user;
        try {
            user = await this.auth.getUserData(req);
            globalThis.tasLogger.info(
                `Deactivating substitution with id ${uvId}`,
                {
                    actorUserId: user.USER_ID,
                    uvId,
                },
            );
            await this.removeUserViceAndSendEmails(user, uvId);

            globalThis.tasLogger.info(
                `Deactivated substitution with id ${uvId} successfully`,
                {
                    actorUserId: user.USER_ID,
                    uvId,
                },
            );
            return res.send({ result: true });
        } catch (err) {
            globalThis.tasLogger.info(
                `Deactivating substitution with id ${uvId} was not successful`,
                {
                    actorUserId: user?.USER_ID,
                    uvId,
                },
            );
            return res.status((err as any).status || 500).send(err);
        }
    }

    async removeUserViceAndSendEmails(user, uvId) {
        const repo = globalThis.orm.repo("userVice");
        const userViceItem = await repo.get(uvId);

        if (!user.isHRManager() && user.USER_ID != userViceItem.USER_ID) {
            throw new UserException(
                "Lack of permissions!",
                "LACK_OF_PERMISSIONS",
            );
        }

        const entity = repo.getEntity();
        entity.UV_ID = uvId;
        const emailSettings = {
            templateKeyVice: "userViceRemove",
            templateKeyViced: "userVicedRemove",
            emailSubject: "userViceRemove",
        };
        await repo.delete(entity);

        globalThis.tasLogger.info(
            `Deactivated substitution ${user.isHRManager() ? "by HR manager with id " + user.USER_ID : "by user"}.
            User ${userViceItem.USER_ID_VICE} substituted user ${userViceItem.USER_ID}.`,
            {
                actorUserId: user.USER_ID,
                userId: userViceItem.USER_ID,
                viceUserId: userViceItem.USER_ID_VICE,
            },
        );

        await this.sendViceEmails(
            user,
            userViceItem.USER_ID_VICE,
            userViceItem.USER_ID,
            {
                from: new Date(userViceItem.UV_FROM),
                to: new Date(userViceItem.UV_TO),
            },
            emailSettings,
        );
    }

    async sendViceEmails(user, userViceId, userVicedId, dates, emailSettings) {
        const userRepo = globalThis.orm.repo("user");
        const userVice = await userRepo.get(userViceId);
        const userViced = await userRepo.get(userVicedId);
        const emailData = {
            vice_from: dates.from,
            vice_to: dates.to,
            user_name_hr: user.isHRManager() ? user.USER_DISPLAY_NAME : null,
        };

        // Send mail to vice
        await this.sendViceEmail(
            user,
            userVice,
            {
                ...emailSettings,
                templateKey: emailSettings.templateKeyVice,
            },
            {
                ...emailData,
                user_name: userViced.USER_DISPLAY_NAME,
            },
        );

        // Send mail to user being viced. Do not send to the person activating the vice
        if (!user.isHRManager() && userViced.USER_ID === user.USER_ID) {
            return;
        }

        await this.sendViceEmail(
            user,
            userViced,
            {
                ...emailSettings,
                templateKey: emailSettings.templateKeyViced,
            },
            {
                ...emailData,
                user_name_vice: userVice.USER_DISPLAY_NAME,
                user_name: userViced.USER_DISPLAY_NAME,
            },
        );
    }

    async sendViceEmail(user, targetUser, emailSettings, emailData) {
        if (targetUser.USER_EMAIL) {
            const targetLanguageParameter = await globalThis.orm
                .repo("userParameter")
                .getUserParameter(
                    targetUser.USER_ID,
                    USER_PARAMETERS.LANGUAGE_CLIENT,
                );
            const targetLanguage = targetLanguageParameter.length
                ? targetLanguageParameter[0].USRPAR_VALUE
                : globalThis.dynamicConfig.defaultLang;

            const targetDateFormatParameter = await globalThis.orm
                .repo("userParameter")
                .getUserParameter(targetUser.USER_ID, "DATE_FORMAT");
            const targetDateFormat = targetDateFormatParameter.length
                ? targetDateFormatParameter[0].USRPAR_VALUE
                : "L";

            const mailOptionsParams: MailOptionsParams = {
                addresses: targetUser.USER_EMAIL,
                subject: `${globalThis.__({ phrase: emailSettings.emailSubject, locale: targetLanguage })}`,
                bccOnMoreAddresses: true,
                ignoreError: true,
            };

            await globalThis.routerMail.sendEmailViaClient(
                user.isHRManager()
                    ? `${emailSettings.templateKey}ByHr`
                    : emailSettings.templateKey,
                mailOptionsParams,
                emailData,
                targetLanguage,
                targetDateFormat,
            );
        }
    }

    addDateRangeCondition(connection, from, to) {
        connection.where(function () {
            this.where(function () {
                this.where("UV_FROM", "<=", from).where("UV_TO", ">=", from); // from is between
            })
                .orWhere(function () {
                    this.where("UV_FROM", "<=", to).where("UV_TO", ">=", to); // to is between
                })
                .orWhere(function () {
                    this.where("UV_FROM", ">=", from).where("UV_TO", "<=", to); // from < FROM & to > TO
                });
        });
    }

    validateRequestData(data, user) {
        if (!data.from || !data.to || !data.user_id_vice) {
            const missingParam = !data.from
                ? "from"
                : !data.to
                  ? "to"
                  : "user_id_vice";
            throw new UserException(
                `Invalid request. Parameter '${missingParam}' is missing.`,
                "BAD_REQUEST",
            );
        }

        if (
            data.user_id &&
            data.user_id != user.USER_ID &&
            !user.isHRManager()
        ) {
            throw new UserException(
                "Lack of permissions!",
                "LACK_OF_PERMISSIONS",
            );
        }

        if (!this.areDatesValid(data.from, data.to)) {
            throw new UserException("Invalid date format.", "BAD_REQUEST");
        }

        if (this.doDatesOverlap(data.from, data.to)) {
            throw new UserException(
                "Substitute interval overlap.",
                "BAD_REQUEST",
            );
        }
    }

    areDatesValid(from, to) {
        const isValidDate = (date) => date instanceof Date && !isNaN(date);
        const parsedFrom = new Date(from);
        const parsedTo = new Date(to);

        if (!isValidDate(parsedFrom) || !isValidDate(parsedTo)) {
            return false;
        }
        return true;
    }

    doDatesOverlap(from, to) {
        const parsedFrom = new Date(from);
        const parsedTo = new Date(to);
        if (parsedFrom >= parsedTo || parsedTo < new Date()) {
            return true;
        }
        return false;
    }
}
