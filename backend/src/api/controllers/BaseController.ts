import { Service } from "typedi";
import { BaseCollection } from "../orm/BaseCollection";
import { BaseEntity } from "../orm/entity/BaseEntity";

@Service()
export class BaseController {
    static applyRequestFilter<T extends BaseEntity>(
        req: any,
        collection: BaseCollection<T>,
        allowAdditionalWildcards?: boolean | undefined,
    ): BaseCollection<T> {
        collection.knex
            .limit(
                req.query.limit
                    ? Number(req.query.limit)
                    : globalThis.dynamicConfig.defaultRequestItemLimit,
            ) // is subordinated to drivers maxRows
            .offset(req.query.offset ? Number(req.query.offset) : 0);

        const queryAccentOptionExists: boolean =
            typeof req.query.disable_accent !== "undefined";
        const queryAccentOptionValue: boolean = JSON.parse(
            req.query.disable_accent || "false",
        ); // Parsing to type convert 'string' to 'boolean'

        return collection.filter(req.query.filter, {
            allowAdditionalWildcards,
            disableAccentSensitivity: queryAccentOptionExists
                ? queryAccentOptionValue
                : globalThis.dynamicConfig.tas.disableAccentSensitivity,
        });
    }

    static applyRequestOrder<T extends BaseEntity>(
        req: any,
        collection: BaseCollection<T>,
    ): void {
        // Apply order
        if (req.query.order) {
            collection.orderBy(
                req.query.order,
                req.query.sort,
                req.query.nullsLast,
            );
        }
    }

    static async handleGetRequest<T extends BaseEntity>(
        req: any,
        collection: BaseCollection<T>,
        options: {
            returnEntities?: boolean;
            allowAdditionalWildcards?: boolean;
            ignoreRequestId?: boolean;
        } = {},
    ): Promise<[T[], number | null, Date]> {
        let { returnEntities, allowAdditionalWildcards, ignoreRequestId } =
            options;
        returnEntities =
            typeof returnEntities !== "undefined" ? returnEntities : false;

        const { id }: any = req.params;

        if (id && !ignoreRequestId) {
            if (Array.isArray(collection.baseEntity.primaryColumn)) {
                // Multiple primary cols.
                const cols: string | string[] =
                    collection.baseEntity.primaryColumn;
                for (let i = 0; i < cols.length; i += 1) {
                    const colName: string = cols[i];
                    collection.filter(`${colName}<eq>"${id[colName]}"`);
                }
            } else {
                collection.filter(
                    `${collection.baseEntity.primaryColumn}<eq>"${id}"`,
                );
            }

            collection.filter(req.query && req.query.filter);

            const data: T[] = returnEntities
                ? await collection.collectAll()
                : ((await collection.fetchAll()) as T[]);
            return [data, data && data.length, new Date()]; // return as array due to universality of handleGetRequest
        }
        return await this.handleMultiRequest(
            req,
            collection,
            returnEntities,
            allowAdditionalWildcards,
        );
    }

    static async handleMultiRequest<T extends BaseEntity>(
        req: any,
        collection: BaseCollection<T>,
        returnEntities?: boolean | undefined,
        allowAdditionalWildcards?: boolean | undefined,
    ): Promise<[T[], number | null, Date]> {
        returnEntities =
            typeof returnEntities !== "undefined" ? returnEntities : false;

        const totalCount: boolean =
            req.query.total_count === "undefined" ||
            req.query.total_count === "true" ||
            req.query.total_count === 1;

        this.applyRequestFilter(req, collection, allowAdditionalWildcards);

        if (req.query.only_count == "true") {
            const count: number = await collection.getTotalCount();
            return [[], count, new Date()];
        }
        if (totalCount) {
            this.applyRequestOrder(req, collection);
            const count: number = await collection.getTotalCount();
            const data: T[] = (await (returnEntities
                ? collection.collectAll()
                : collection.fetchAll())) as T[];
            return [data, count, new Date()];
        }
        this.applyRequestOrder(req, collection);
        const data: T[] = returnEntities
            ? await collection.collectAll()
            : ((await collection.fetchAll()) as T[]);
        return [data, null, new Date()];
    }

    static async handleSingleRequest<T extends BaseEntity>(
        collection: BaseCollection<T>,
        id: any,
        returnEntity: boolean | undefined,
    ): Promise<any> {
        returnEntity =
            typeof returnEntity !== "undefined" ? returnEntity : false;

        if (Array.isArray(collection.baseEntity.primaryColumn)) {
            // Multiple primary cols.
            const cols: string | string[] = collection.baseEntity.primaryColumn;
            for (let i = 0; i < cols.length; i += 1) {
                const colName: string = cols[i];
                collection.filter(`${colName}<eq>"${id[colName]}"`);
            }
        } else {
            collection.filter(
                `${collection.baseEntity.primaryColumn}<eq>"${id}"`,
            );
        }

        return returnEntity
            ? await collection.collectOne()
            : await collection.fetchOne();
    }

    static handleResponse(
        _req: any,
        res: any,
        result: Record<string, any>,
    ): void {
        res.send(result);
    }

    static handleErrorResponse(
        _req: any,
        res: any,
        err: unknown | Error,
    ): void {
        res.status((err as any).status || 500).send(err);
    }

    static getColumns(req: any): string[] {
        if (!req.query.columns) {
            return [];
        }

        return req.query.columns
            .split(",")
            .map(
                (column: string) =>
                    `${column.toUpperCase()} as ${column.toUpperCase()}`,
            );
    }
}
