import { BaseEntity } from "./BaseEntity";
import * as CRONS from "./const/cronConsts";

export class <PERSON><PERSON> extends BaseEntity {
    attributes() {
        return {
            CRON_ID: { type: "integer", primary: true },
            CRON_NAME: {
                type: "string",
                notNull: true,
                unique: true,
                name: true,
            },
            CRON_ALIAS: {
                type: "string",
                default: null,
                duplicateNulls: true,
                unique: true,
            },
            CRON_SYNTAX: {
                type: "string",
                default: null,
                duplicateNulls: true,
            }, // Should be unique because of => https://github.com/kelektiv/node-cron/issues/315
            CRON_STATUS: { type: "string", default: "N", notNull: true },
            CRON_DESCRIPTION: { type: "string", default: "To be described..." },
            CRON_PARAMETERS: { type: "text", default: null },
            CRON_START: { type: "dateTime", default: null },
            CRON_LAST_RUN: { type: "dateTime", default: null },
            CRON_NEXT_RUN: { type: "dateTime", default: null },
            CRON_TYPE: { type: "string" },
            CRON_TIMEOUT: { type: "integer", default: null },
            CRON_LAST_END: { type: "dateTime", default: null },
            CRON_LAST_RUN_ERROR: {
                type: "string",
                default: "N",
                notNull: true,
            },
            CRON_FILE: { type: "string", default: null },
            CRON_IS_CLONE: { type: "string", default: "N", notNull: true },
        };
    }

    get basename() {
        return this.CRON_FILE;
    }

    get path(): string {
        if (this.CRON_TYPE === CRONS.TYPE_SYSTEM) {
            const cronFile =
                this.CRON_FILE.charAt(0).toLowerCase() +
                this.CRON_FILE.slice(1);
            return cronFile.replace(".js", "").replace(".ts", "");
        } else {
            const cronFile =
                this.CRON_FILE.charAt(0).toLowerCase() +
                this.CRON_FILE.slice(1);
            return `customized.${cronFile.replace(".js", "").replace(".ts", "")}`;
        }
    }

    get fullName() {
        const alias = this.CRON_ALIAS ? `(${this.CRON_ALIAS})` : "";
        return `${this.CRON_NAME} ${alias}`;
    }
}
