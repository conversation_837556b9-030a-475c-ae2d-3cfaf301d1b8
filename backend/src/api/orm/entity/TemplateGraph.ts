import { BaseEntity } from "./BaseEntity";

export class TemplateGraph extends BaseEntity {
    attributes() {
        return {
            TGRAPH_ID: { primary: true },
            TGRAPH_COLOR: { type: "string", maxLength: 6 },
            TGRAPH_FONT_COLOR: { type: "string", maxLength: 6 },
            TGRAPH_LINE_TYPE: { type: "string" },
            TGRAPH_FONT: { type: "string" },
            TGRAPH_SIZE_X: { type: "integer" },
            TGRAPH_POSITION1_X: { type: "integer" },
            TGRAPH_POSITION1_Y: { type: "integer" },
            TGRAPH_POSITION2_Y: { type: "integer" },
            TGRAPH_POSITION2_X: { type: "integer" },
            TGRAPH_SIZE_Y: { type: "integer" },
            TGRAPH_LINKSRC_PORT: { type: "integer" },
            TGRAPH_LINKTGT_PORT: { type: "integer" },
            TTASK_ID: { type: "integer", constraint: "TemplateTask" },
            TGR<PERSON>H_FONT_SIZE: { type: "integer" },
            TGRAPH_FROM_TTASK_ID: {
                type: "integer",
                constraint: "TemplateTask",
            },
            TGRAPH_TO_TTASK_ID: { type: "integer", constraint: "TemplateTask" },
            TGRAPH_TTASKCON_ID: { type: "integer" },
            ORG_ID: { type: "integer", default: 1 },
            TPROC_ID: { type: "integer", constraint: "TemplateProcess" },
            TGRAPH_OBJECT_TYPE: { type: "string" },
            TGRAPH_BPMN_TYPE: { type: "string" },
            TGRAPH_BPMN_EVENT_DEF_TYPE: { type: "string" },
            TGRAPH_VERSION: { type: "string" },
        };
    }
}
