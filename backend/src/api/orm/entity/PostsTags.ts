import { BaseEntity } from "./BaseEntity";

export class PostsTags extends BaseEntity {
    attributes() {
        return {
            POST_ID: {
                type: "integer",
                notNull: true,
                foreign: "Post",
                references: "POST_ID",
                primary: true,
                onDelete: "CASCADE",
            },
            TAG_ID: {
                type: "integer",
                notNull: true,
                foreign: "PostTag",
                references: "TAG_ID",
                primary: true,
                onDelete: "CASCADE",
            },
            CREATED_AT: {
                type: "dateTime",
                notNull: true,
            },
            UPDATED_AT: {
                type: "dateTime",
                notNull: true,
            },
        };
    }
}
