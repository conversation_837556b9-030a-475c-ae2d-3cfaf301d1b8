import { BaseEntity } from "./BaseEntity";

export class PostVisibility extends BaseEntity {
    attributes() {
        return {
            POST_VISIBILITY_ID: { type: "integer", primary: true },
            POST_ID: {
                type: "integer",
                foreign: "Post",
                references: "POST_ID",
                onDelete: "CASCADE",
            },
            ORGSTR_ID: {
                type: "integer",
                foreign: "OrganizationStructure",
                references: "ORGSTR_ID",
                onDelete: "CASCADE",
            },
            ROLE_ID: {
                type: "integer",
                foreign: "Roles",
                references: "ROLE_ID",
                onDelete: "CASCADE",
            },
            USER_ID: {
                type: "integer",
                foreign: "Users",
                references: "USER_ID",
                onDelete: "CASCADE",
            },
            TPROC_ID: {
                type: "integer",
                foreign: "TemplateProcesses",
                references: "TPROC_ID,TPROC_VERSION",
                onDelete: "CASCADE",
            },
            TPROC_VERSION: {
                type: "integer",
                foreign: "TemplateProcesses",
                references: "TPROC_ID,TPROC_VERSION",
                onDelete: "CASCADE",
            },
            HEADER_ID: {
                type: "integer",
                foreign: "Headers",
                references: "HEADER_ID",
                onDelete: "CASCADE",
            },
            TTASK_ID: {
                type: "integer",
                foreign: "TemplateTasks",
                references: "TTASK_ID",
                onDelete: "CASCADE",
            },
            CREATED_AT: {
                type: "dateTime",
                notNull: true,
            },
            UPDATED_AT: {
                type: "dateTime",
                notNull: true,
            },
        };
    }
}
