import { BaseEntity } from "./BaseEntity";

export class RegisteredMobileDevice extends BaseEntity {
    attributes() {
        return {
            RMD_ID: { primary: true },
            RMD_NAME: { type: "string" },
            RMD_VERIFIED: { type: "string" },
            RMD_LOGIN_TOKEN: { type: "string" },
            RMD_USER_ID: { type: "number" },
            RMD_LOGIN_COUNT: { type: "number" },
            RMD_LAST_LOGIN: { type: "dateTime" },
            RMD_CREATED_AT: { type: "dateTime" },
            RMD_FCM_TOKEN: { type: "string" },
            RMD_MUTE_NOTIFICATION: { type: "boolean", default: false },
            BADGE_COUNTER: { type: "integer" },
        };
    }
}
