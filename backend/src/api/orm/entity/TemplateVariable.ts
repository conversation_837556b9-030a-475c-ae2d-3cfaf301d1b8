import _ from "lodash";
import { BaseEntity, LocalizedRecord } from "./BaseEntity";
import { UtilsService } from "../../services/UtilsService";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";

export interface ITemplateVariableAttributes {
    TVAR_ID: number;
    TPROC_ID: number;
    TVAR_NAME: string;
    TVAR_TYPE: string;
    TVAR_MULTITASK_BEHAVIOUR: string;
    TVAR_TEXT_VALUE: string;
    TVAR_NUMBER_VALUE: number;
    TVAR_DATE_VALUE: Date;
    TVAR_ATTRIBUTE: string;
    DLIST_NAME: string;
    TVAR_MULTI: string;
    ORG_ID: number;
    TVAR_BIG_VALUE: string;
    TVAR_META: string;
    TVAR_ALIAS: string;
    TVAR_COL_INDEX: number;
    TVAR_TOOLTIP: string;
    TVAR_IS_SHARED: string;
    TVAR_CLASS: string;
    TVAR_LABEL: string;
    TVAR_COPY_SNAPSHOT: string;
    TVAR_BUSINESS: string;
    TVAR_DESIGN_VERSION: number;
    TVAR_DATE_WITHOUT_TIME: string;
    TVAR_IS_SHREDABLE: string;
    TVAR_MM_CLASS: string;
    TVAR_MM_CLASS_ATTR: string;
}

interface ITemplateVariableMethods {
    attributes(): Record<string, any>;
    isMultichoice(): boolean;
    getValueColumn(): string;
    setLovData(data: any, lang?: string | null): void;
    getLovData(lang?: string | null): any;
}

export interface ITemplateVariable
    extends Partial<ITemplateVariableAttributes>,
        ITemplateVariableMethods,
        Partial<
            LocalizedRecord<"TVAR_NAME"> & LocalizedRecord<"TVAR_TOOLTIP">
        > {}

export class TemplateVariable extends BaseEntity implements ITemplateVariable {
    public static readonly consts = {
        TYPE_TEXT: "T",
        TYPE_TEXT_LIST: "LT",
        TYPE_DATE: "D",
        TYPE_DATE_LIST: "LD",
        TYPE_NUMBER: "N",
        TYPE_NUMBER_LIST: "LN",
        TYPE_DYNAMIC_LIST: "DL",
        TYPE_BIG: "B",

        ATTR_MULTILINE: "M",
        ATTR_USER_DEFINED: "S",
        ATTR_USER: "U",
        ATTR_ORG_STRUCT: "O",
        ATTR_ROLE: "R",
        ATTR_SEQUENCE: "S",
        ATTR_FILE: "F",

        SHARED_READABLE: "Y",
        SHARED_UNREADABLE: "N",
        SHARED_WRITABLE: "W",
    };

    attributes() {
        const attrs = {
            TVAR_ID: { primary: true },
            TPROC_ID: { type: "number", constraint: "TemplateProcess" },
            TVAR_NAME: {
                type: "string",
                notNull: true,
                uniqueIn: "TemplateProcess",
                translations: true,
            },
            TVAR_TYPE: {
                type: "string",
                in: ["N", "T", "D", "DL", "LN", "LT", "LD", "B", "DT", "DR"],
            },
            TVAR_MULTITASK_BEHAVIOUR: { type: "string" },
            TVAR_TEXT_VALUE: { type: "string" },
            TVAR_NUMBER_VALUE: { type: "number" },
            TVAR_DATE_VALUE: { type: "date" },
            TVAR_ATTRIBUTE: {
                type: "string",
                in: ["U", "R", "M", "O", "S", "F", "T", "V", null],
            }, // User, Role, Multiline, Organization, Sequence, File, Table, State variable(DT)
            DLIST_NAME: { type: "string" },
            TVAR_MULTI: { type: "string" },
            ORG_ID: { type: "number", default: 1 },
            TVAR_BIG_VALUE: { type: "text" },
            TVAR_META: { type: "text" },
            TVAR_ALIAS: { type: "string" },
            TVAR_COL_INDEX: { type: "number" },
            TVAR_TOOLTIP: { type: "string", translations: true },
            TVAR_IS_SHARED: { type: "string" },
            TVAR_CLASS: { type: "string" },
            TVAR_LABEL: { type: "string" },
            TVAR_COPY_SNAPSHOT: { type: "string" },
            TVAR_BUSINESS: { type: "string", in: ["Y", "N"], default: "N" },
            TVAR_DESIGN_VERSION: { type: "number", default: 0 },
            TVAR_DATE_WITHOUT_TIME: {
                type: "string",
                in: ["Y", "N"],
                default: "N",
            },
            TVAR_IS_SHREDABLE: { type: "string", in: ["Y", "N"], default: "N" },
            TVAR_MM_CLASS: { type: "string" },
            TVAR_MM_CLASS_ATTR: { type: "string" },
        };

        return super.extendTranslations(attrs);
    }

    /**
     * Use this to save variable.
     * @param value
     */
    set value(value) {
        // validate Date
        if (
            _.includes(
                [
                    TemplateVariable.consts.TYPE_DATE,
                    TemplateVariable.consts.TYPE_DATE_LIST,
                ],
                this.TVAR_TYPE,
            ) &&
            !(value instanceof Date) &&
            value !== null &&
            !this.isMultichoice() // validation ignores multichoice for now
        ) {
            const oldValue = value;
            try {
                value = new Date(value); // could be invalid until validation
                if (Object.prototype.toString.call(value) === "[object Date]") {
                    if (isNaN(value.getTime())) {
                        // date is not valid
                        throw new Error();
                    }
                    // date is valid
                } else {
                    throw new Error();
                }
            } catch (_err) {
                throw new UserException(
                    `Wrong format or incorrect date '${oldValue}'. Use ISO 8601 (2011-10-05T14:48:00.000Z) or Date object.`,
                );
            }
        }

        // is file - WTF ignore multichoice
        if (
            this.TVAR_ATTRIBUTE == TemplateVariable.consts.ATTR_FILE &&
            Array.isArray(value)
        ) {
            value = value.join(":");
        }

        // if multichoice jsonify the value
        if (this.isMultichoice()) {
            this.TVAR_MULTI_SELECTED = JSON.stringify(value);
        } else if (this.TVAR_TYPE === "DR") {
            this[this.getValueColumn()] = JSON.stringify(value);
        } else if (this.TVAR_TYPE === "T" && value) {
            const byteLength = UtilsService.byteLength(value);
            if (byteLength >= 4000) {
                const diff = byteLength - value.length;
                this.TVAR_BIG_VALUE = value;
                this.TVAR_TEXT_VALUE = `${value.substring(
                    0,
                    3950 - diff,
                )}... (text je příliš dlouhý)`;
            } else {
                this.TVAR_BIG_VALUE = null;
                this.TVAR_TEXT_VALUE = value;
            }
        } else {
            this[this.getValueColumn()] = value;
        }
    }

    get value() {
        if (this.isMultichoice()) {
            return null;
        }

        // is file - WTF ignore multichoice sign in DB
        if (
            this.TVAR_ATTRIBUTE == TemplateVariable.consts.ATTR_FILE &&
            this[this.getValueColumn()] !== null
        ) {
            return this[this.getValueColumn()].split(":");
        }

        if (this.TVAR_TYPE === "T") {
            if (this.TVAR_BIG_VALUE) {
                return this.TVAR_BIG_VALUE;
            }
            return this[this.getValueColumn()];
        } else if (this.TVAR_TYPE === "N") {
            return Number(this[this.getValueColumn()]);
        }

        return this[this.getValueColumn()];
    }

    isMultichoice() {
        return this.TVAR_MULTI == "X";
    }

    getValueColumn() {
        let colName;
        if (this.TVAR_MULTI == "X") {
            colName = "TVAR_TEXT_VALUE";
        } else if (this.TVAR_TYPE == "DT") {
            colName = "TVAR_TEXT_VALUE";
        } else if (this.TVAR_TYPE == "T" || this.TVAR_TYPE == "LT") {
            colName = "TVAR_TEXT_VALUE";
        } else if (this.TVAR_TYPE == "N" || this.TVAR_TYPE == "LN") {
            colName = "TVAR_NUMBER_VALUE";
        } else if (this.TVAR_TYPE == "D" || this.TVAR_TYPE == "LD") {
            colName = "TVAR_DATE_VALUE";
        } else if (this.TVAR_TYPE == "DL" && !this.TVAR_ATTRIBUTE) {
            colName = "TVAR_TEXT_VALUE";
        } else if (this.TVAR_TYPE == "DL") {
            colName = "TVAR_NUMBER_VALUE";
        } else if (
            (this.TVAR_TYPE == "B" && this.TVAR_ATTRIBUTE == "T") ||
            this.TVAR_TYPE == "DR"
        ) {
            colName = "TVAR_BIG_VALUE";
        } else {
            throw new UserException(
                `Unrecognized variable type. TVAR_TYPE=${this.TVAR_TYPE} TVAR_ATTRIBUTE=${this.TVAR_ATTRIBUTE}`,
            );
        }

        return colName;
    }

    /**
     * Sets data for lov for future use.
     *
     * @param data.title {string}
     * @param data.value {string}
     */
    setLovData(data: any, lang = null) {
        if (lang) {
            this[`_lovData_${lang}`] = data;
        } else {
            this._lovData = data;
        }
    }

    /**
     *
     * @returns {Array|*}
     */
    getLovData(lang: string | null = null) {
        return lang ? this[`_lovData_${lang}`] : this._lovData;
    }

    /**
     * @param value {null|string}
     */
    set lovValue(value) {
        this._lovValue = value;
    }

    /**
     * Return translated id to name.
     *
     * @returns {null|string}
     */
    get lovValue() {
        return this._lovValue;
    }
}
