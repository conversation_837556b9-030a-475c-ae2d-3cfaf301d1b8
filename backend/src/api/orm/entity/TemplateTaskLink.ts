import _ from "lodash";
import { BaseEntity } from "./BaseEntity";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";

interface ITemplateTaskLinkAttributes {
    TTASKLINK_ID: number;
    TTASKLINK_TYPE: string;
    ORG_ID: number;
    TTASKLINK_PRIORITY: number;
    TPROC_ID: number;
    TTASKLINK_TO_TTASK_ID: number;
    TTASKLINK_FROM_TTASK_ID: number;
    TTASKLINK_IS_MANDATORY: string;
    TTASKLINK_VERSION: number;
}

interface ITemplateTaskLinkMethods {
    attributes(): Record<string, any>;
    validations(repo: any): Record<string, any>;
}

export interface ITemplateTaskLink
    extends Partial<ITemplateTaskLinkAttributes>,
        ITemplateTaskLinkMethods {}

export class TemplateTaskLink extends BaseEntity implements ITemplateTaskLink {
    attributes() {
        return {
            TTASKLINK_ID: { primary: true },
            TTASKLINK_TYPE: {
                type: "string",
                default: "AND",
                in: ["AND", "ELSE", "OR"],
            },
            ORG_ID: { type: "integer", default: 1 },
            TTASKLINK_PRIORITY: { type: "integer" },
            TPROC_ID: { type: "integer", constraint: "TemplateProcess" },
            TTASKLINK_TO_TTASK_ID: {
                type: "integer",
                constraint: "TemplateTask",
            },
            TTASKLINK_FROM_TTASK_ID: {
                type: "integer",
                constraint: "TemplateTask",
            },
            TTASKLINK_IS_MANDATORY: {
                type: "string",
                default: "N",
                in: ["Y", "N"],
            },
            TTASKLINK_VERSION: { type: "integer" },
        };
    }

    validations(repo: any) {
        const baseValidations = super.validations(repo);

        const validations = {
            async oneElseBrach(): Promise<void> {
                //@ts-expect-error
                if (this.TTASKLINK_TYPE === "ELSE") {
                    const links = await repo.connection
                        .select()
                        .from(repo.tableName)
                        .where(
                            "TTASKLINK_FROM_TTASK_ID",
                            //@ts-expect-error
                            this.TTASKLINK_FROM_TTASK_ID,
                        )
                        .where("TTASKLINK_TYPE", "ELSE");
                    if (Array.isArray(links) && links.length > 0) {
                        throw new UserException(
                            `Only one else branch is possible. [${_.map(links, "TTASKLINK_ID").join(", ")}]`,
                            "TOO_MANY_ELSE_BRANCHES",
                        );
                    }
                }
            },
        };
        return _.merge(baseValidations, validations);
    }
}
