import { BaseEntity, LocalizedRecord } from "./BaseEntity";

interface ITemplateProcessAttributes {
    TPROC_ID: number;
    TPROC_VERSION: number;
    TPROC_NAME: string;
    TPROC_OWNER_USER_ID: number;
    TPROC_LOCKED_BY_USER_ID: number;
    TPROC_LAST_CHANGED_BY_USER_ID: number;
    TPROC_LAST_CHANGED_DATE: Date;
    TPROC_STATUS: string;
    TPROC_DESCRIPTION: string;
    TPROC_URL: string;
    TPROC_TVAR_ORDER: string;
    TPROC_URL_TAB_NAME: string;
    TPROC_VIS_ORGSTR_ID: number;
    TPROC_VIS_ROLE_ID: number;
    TPROC_DMS_VISIBILITY: string;
    TPROC_DEFAULT_CASE_NAME: string;
    ORG_ID: number;
    TPROC_NOTE: string;
    TPROC_HR_ROLE_ID: number;
}

interface ITemplateProcessMethods {
    attributes(): Record<string, object>;
    isDeleted(): boolean;
    get(primaryKey: string): Promise<any>;
    primaryOrder(): string[];
}

export interface ITemplateProcess
    extends Partial<ITemplateProcessAttributes>,
        ITemplateProcessMethods,
        Partial<
            LocalizedRecord<"TPROC_NAME"> &
                LocalizedRecord<"TPROC_DESCRIPTION"> &
                LocalizedRecord<"TPROC_DEFAULT_CASE_NAME">
        > {}

export class TemplateProcess extends BaseEntity implements ITemplateProcess {
    attributes() {
        const attrs = {
            TPROC_ID: { primary: true, seq: "TPROC_ID_SEQ" },
            TPROC_VERSION: { primary: true },
            TPROC_NAME: { type: "string", notNull: true, translations: true },
            TPROC_OWNER_USER_ID: { type: "integer", constraint: "User" },
            TPROC_LOCKED_BY_USER_ID: { type: "integer", constraint: "User" },
            TPROC_LAST_CHANGED_BY_USER_ID: {
                type: "integer",
                constraint: "User",
            },
            TPROC_LAST_CHANGED_DATE: { type: "date" },
            TPROC_STATUS: { type: "string", default: "D" },
            TPROC_DESCRIPTION: { type: "string", translations: true },
            TPROC_URL: { type: "string" },
            TPROC_TVAR_ORDER: { type: "text" },
            TPROC_URL_TAB_NAME: { type: "string" },
            TPROC_VIS_ORGSTR_ID: {
                type: "integer",
                constraint: "OrganizationStructure",
            },
            TPROC_VIS_ROLE_ID: { type: "integer", constraint: "Role" },
            TPROC_DMS_VISIBILITY: {
                type: "string",
                in: ["M", "S", "SM", null],
            },
            TPROC_DEFAULT_CASE_NAME: {
                type: "string",
                maxLength: 70,
                translations: true,
            },
            ORG_ID: { type: "integer", default: 1 },
            TPROC_NOTE: { type: "text" },
            TPROC_HR_ROLE_ID: { type: "integer" },
        };

        return super.extendTranslations(attrs);
    }

    isDeleted() {
        return this.TPROC_STATUS === "E";
    }

    async get(primaryKey: string) {
        return Array.isArray(primaryKey)
            ? super.get(primaryKey)
            : super.get([primaryKey, 1]);
    }

    /**
     * Properties order in objects is not guaranteed in JavaScript; you need to use an Array !
     * @returns {string[]}
     */
    primaryOrder() {
        return ["TPROC_ID", "TPROC_VERSION"];
    }
}
