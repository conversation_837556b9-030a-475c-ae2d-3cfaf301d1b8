// EXEC TYPE
export const EXEC_ON_START = "S";
export const EXEC_RECALC = "R";
export const EXEC_ON_END = "E";
export const EXEC_HAND = "H";
export const EXEC_PRE_PULL = "P";

// LIST OF ALL AVAILABLE CALCULATIONS fnTern is not included.
export const CALCULATION_LIST = [
    "wmd",
    "wdd",
    "alterDate",
    "wmh",
    "whd",
    "format",
    "nl2p",
    "random_text",
    "gen_passc",
    "getPrint",
    "countFiles",
    "storeAttachment",
    "dms",
    "fnEq",
    "fnTrim",
    "fnLt",
    "fnNot",
    "fnAnd",
    "fnAnd10",
    "fnOr",
    "fnOr10",
    "fnContain",
    "fnEmpty",
    "fnPregMatch",
    "fnRound",
    "fnFloor",
    "fnNumber",
    "fnArrayDiff",
    "fnArrayMerge",
    "fnArrayUnique",
    "fnToArray",
    "fnMcSum",
    "fnInArray",
    "fnFromArray",
    "fnDelFromArray",
    "iterator",
    "getAttr",
    "fillDLList",
    "csvToArray",
    "tblCreateFromArray",
    "tblCreate",
    "tblGetValue",
    "tblGetRow",
    "tblRemoveRow",
    "tblRemoveCol",
    "tblMerge",
    "tblGetRowNames",
    "tblGetColNames",
    "tblfilterByValue",
    "tblfilterByCol",
    "tblAddRow",
    "tblAddCol",
    "tblSort",
    "tblGetRowKeys",
    "tblGetColKeys",
    "tblMcSelect",
    "tblMcAddSelect",
    "tblMcUnselect",
    "fullList",
    "dumpList",
    "tblCreateRandom",
    "supervisor",
    "initiator",
    "solver",
    "manager",
    "user",
    "lastSolverOf",
    "extractFileTag",
    "roleToId",
    "idToRole",
    "setEventFilter",
    "orgUnitOfUser",
    "userInOrg",
    "userInOrgTree",
    "userHasRole",
    "userToFirstName",
    "userToLastName",
    "usersToFullName",
    "userIsManager",
    "userIsManagerTree",
    "orgManager",
    "getOrgByAttr",
    "defDynList",
    "defDynListOrg",
    "explodeLinks",
    "cfProject",
    "listFromProcesses",
    "setList",
    "setUserList",
    "fillList",
    "createNote",
    "createNonEmptyNote",
    "truncate",
    "getOrgAttr",
    "getUserAttr",
    "isUserInLevel",
    "isManagerInLevel",
    "dynamicTableGetCell",
    "dynamicTableUpdateCell",
    "dynamicTableUpdateCellCond",
    "dynamicTableGetRowId",
    "dynamicTableGetNewRowId",
    "dynamicTableGetNewRowIdCond",
    "setSupervisor",
    "getUserWithRole",
    "getUserWithOrganization",
    "iprocId",
    "hasOrgChild",
    "orgStrNameToId",
    "orgUnitInLevel",
    "setRoleToUser",
    "dynListToString",
    "getDMSFileIds",
    "getInvitationAttr",
    "dmsMergePdfs",
    "savePrintForm",
    "storeCsvFromDms",
    "copyDLList",
    "getMTSum",
    "jsonSumArray",
    "moveAttachments",
    "getUserFromOrgWithRole",
    "getAresData",
    "getCurrentIteration",
    "getDRCell",
    "getUserNameById",
    "dynamicTableGetRowCounts",
    "getExchangeRate",
    "printToFantomPdf",
    "addMailNotifsAttachment",
    "extendDR",
    "finishSubProcesses",
    "setCaseOwner",
    "getHeaderCode",
    "changeVariableDLName",
    "changeAssessmentRole",
    "changeVariableDLName",
    "setHeaderByName",
    "getChr",
    "getParentProcessStatus",
    "setDRAsIterator",
    "getIteratorIndex",
    "getCurrentRowOfDRCell",
    "fnSubstr",
    "itaskId",
    "initiator",
    "addRowsToDR",
    "to_date",
    "translateDynamicRowTemplate",
    "log",
    "generalApprover",
    "addStaticRights",
    "removeStaticRightsGroup",
    "getDRCol",
    "isDoc",
    "getSequenceNumberCond",
    "getSequenceNumber",
    "fnStrPad",
    "callTas3",
    "assignRightsToSubprocesses",
    "fnDelFromDR",
    "fnStrLength",
    "fnStrPadRight",
    "setDocumentLogicalType",
    "changeInvitationAttr",
    "changeITaskComment",
    "changeMailAttribute",
    "translateFileTemplate",
    "updateDtFromDb",
    "getSharedVariables",
    "hasOrgChild",
    "zfoGetFormType",
    "zfoLinkAttachments",
    "zfoUpdateVariables",
    "xmlGetContents",
    "getFileContents",
    "exportAttachment",
    "getDMSFileNames",
    "getDMSFiles",
    "moveFile",
];

export const INSTANCE_TASK = "task"; // namespace to task entity.
export const INSTANCE_PROCESS = "proc"; // namespace to process entity.
export const VARIABLE_SNAPSHOTS = "snaps"; // namespace to task snapshot array.
export const INSTANCE_VARIABLES = "vars"; // namespace to process variables array.
export const ZIP = "zip"; // namespace to process zip array.
export const HOLDER = "Holder"; // namespace to holder.
export const DATA_HOLDER = "DataHolder"; // namespace to holder for taskJsCalculations.
export const CURL = "curl"; // namespace to curl.
export const STORAGE = "storage"; // namespace to curl.
export const EVAL_MATH = "lib"; // namespace to all evalmath functions.
export const DEBUG = "debug"; // namespace to debug functions.
export const DT = "dt"; // namespace to dynamic table functions.
export const DR = "dr"; // namespace to dynamic rows functions.
export const JWT = "jwt"; // namespace to curl.
export const DOCX = "docx";
export const EXCEL = "excel";
export const IDENTITY = "identity";
export const HTML = "html";
export const AXIOS = "axios";
export const SYSTEM = "sys";
export const TAS = "tas";
export const SQL = "sql";

// SystemVariableName: ItaskApi.js attribute.
export const SYSTEM_VARIABLES = {
    "-9": `${INSTANCE_PROCESS}.IPROC_CASE_STATUS`,
    "-8": `${INSTANCE_TASK}.ITASK_USERJS`,
    "-6": `${INSTANCE_TASK}.ITASK_USERJS`,
    "-5": `${INSTANCE_TASK}.ITASK_DESCRIPTION`,
    "-4": `${INSTANCE_TASK}.ITASK_COMMENT`,
    "-3": `${INSTANCE_TASK}.ITASK_NAME`,
    "-2": `${INSTANCE_PROCESS}.IPROC_VIS_ROLE_ID`,
    "-1": `${INSTANCE_PROCESS}.IPROC_NAME`,
    TaskUserJS: `${INSTANCE_TASK}.ITASK_USERJS`,
    TaskDescription: `${INSTANCE_TASK}.ITASK_DESCRIPTION`,
    TaskComment: `${INSTANCE_TASK}.ITASK_COMMENT`,
    TaskName: `${INSTANCE_TASK}.ITASK_NAME`,
    CaseVisRoleId: `${INSTANCE_PROCESS}.IPROC_VIS_ROLE_ID`,
    CaseName: `${INSTANCE_PROCESS}.IPROC_NAME`,
    TaskStartAt: `${INSTANCE_TASK}.ITASK_DUE_DATE_START`,
    TaskDeadline: `${INSTANCE_TASK}.ITASK_DUE_DATE_FINISH`,
    TaskFinish: `${INSTANCE_TASK}.ITASK_ACTUAL_DATE_FINISH`,
    TaskStart: `${INSTANCE_TASK}.ITASK_ACTUAL_DATE_START`,
    TaskOwner: `${INSTANCE_TASK}.ITASK_USER_ID`,
    TaskOwnerName: `${INSTANCE_TASK}.getOwnerUserName()`,
    TaskSolvedBy: `${INSTANCE_TASK}.ITASK_FINISHED_BY_USER_ID`,
    TaskSolvedByName: `${INSTANCE_TASK}.getSolvedByUserName()`,
    TaskInstruction: `${INSTANCE_TASK}.ITASK_INSTRUCTION`,
    TaskLink: `${INSTANCE_TASK}.getTaskLink()`,
    CaseStart: `${INSTANCE_PROCESS}.IPROC_ACTUAL_START_DATE`,
    CaseDeadline: `${INSTANCE_PROCESS}.IPROC_DUE_DATE_FINISH`,
    CasePriority: `${INSTANCE_PROCESS}.IPROC_PRIORITY`,
    CasePriorityText: `${INSTANCE_PROCESS}.IPROC_PRIORITY`,
    CaseOwner: `${INSTANCE_PROCESS}.IPROC_INST_OWNER_USER_ID`,
    CaseOwnerName: `${INSTANCE_PROCESS}.getCaseOwnerName()`,
    CaseDescription: `${INSTANCE_PROCESS}.IPROC_DESCRIPTION`,
    CaseNotes: `${INSTANCE_PROCESS}.getCaseNotes()`,
    CaseLink: `${INSTANCE_PROCESS}.getCaseLink()`,
    FrontendLink: `${INSTANCE_PROCESS}.getFrontendLink()`,
    BackendLink: `${INSTANCE_PROCESS}.getBackendLink()`,
};

export enum API_CONTEXT {
    CONSOLE = "CC",
    GLOBAL_JS = "GJC",
    IDENTITY = "IDC",
    TASK_JS = "TJC",
    PROCESS_JS = "PJC",
    ANY = "ANY",
}
