export const AUTOSTART_YES = "Y";
export const AUTOSTART_NO = "N";

export const TYPE_AUTOMAT = "A";
export const TYPE_STANDARD = "S";
export const TYPE_SUBPROCESS = "P";
export const TYPE_EVENT = "E";
export const TYPE_EMAIL_NOTIFICATION = "N";
export const TYPE_EVENT_WAIT = "W";
export const TYPE_INVITATION = "I";

// STATUS P is reserved for planned, it is virtual, consisting of status : W and auto_start : Y
// task statuses
// export const STATUS_NOTPERFORMABLE = 'N';
export const STATUS_NEW = "N";
// export const STATUS_PERFORMABLE = 'P';
// export const STATUS_STARTED = 'A';
export const STATUS_ACTIVE = "A";
// export const STATUS_SUSPENDED = 'S';
// export const STATUS_RESUMED = 'R';
// export const STATUS_SKIPPED = 'K';
export const STATUS_DONE = "D";
// export const STATUS_CANCELLED = 'C';
// export const STATUS_FAILED = 'F';
export const STATUS_DELAYED = "L"; // delayed due to due_date_start
export const STATUS_WAITING = "W"; // waiting for user input
export const STATUS_PULL = "T";

// hierarchy selectors for users
export const ASSESMENT_HIERARCHY_REFERENCE = "G"; // former GUARANTOR
export const ASSESMENT_HIERARCHY_CHILDREN = "C";
export const ASSESMENT_HIERARCHY_DESCENDANTS = "D";
export const ASSESMENT_HIERARCHY_PARENT = "P";
export const ASSESMENT_HIERARCHY_ANCESTORS = "A";
export const ASSESMENT_HIERARCHY_SIBLINGS = "S";
export const ASSESMENT_HIERARCHY_ALL = "L";

// methods how to select task's solver
export const ASSESMENT_METHOD_SELECT = "S";
export const ASSESMENT_METHOD_SELECT_BY = "U";
export const ASSESMENT_METHOD_AUTOMATIC = "T"; // selected by random
export const ASSESMENT_METHOD_LAST_SOLVER = "L"; // last solver of ITASK_ASSESMENT_TTASK_ID task is new solver
export const ASSESMENT_METHOD_LEAST_WORKLOAD = "W"; // select solver with minimum opened tasks
export const ASSESMENT_METHOD_LAST_SOLVER_CHOICE = "C"; // last solver of ITASK_ASSESMENT_TTASK_ID task will choose new solver
export const ASSESMENT_METHOD_PULL = "A"; // adopt, task will be shown on heap and users can take it
export const ASSESMENT_METHOD_VARIABLE = "V"; // solver will be select from variable
export const ASSESMENT_METHOD_OWNER = "P";

export const MULTIINSTANCE_FLAG_NO = "N";
export const MULTIINSTANCE_FLAG_YES = "Y"; // multiinstance template
export const MULTIINSTANCE_FLAG_INST = "M"; // is actual multiinstance instance

export const SUFFICIENT_END_NO = "N";
export const SUFFICIENT_END_YES = "Y";

export const AGAIN = "A";
export const NOT_AGAIN = "N";

export const HISTORY_NOTE_LAST_SOLVER = "Task finished";

export const AUTO_RESOLVED_NOT = "N";
export const AUTO_RESOLVED_FINISHED = "F";
export const AUTO_RESOLVED_CANCELLED = "C";
