// @ts-nocheck
import * as VAR from "./const/variableConst";

import _ from "lodash";
import moment from "moment-timezone";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { BaseEntity, LocalizedRecord } from "./BaseEntity";
import { UtilsService } from "../../services/UtilsService";
import { TemplateVariable } from "./TemplateVariable";
import { htmlSanitize } from "../../utils/functions";

interface IVariableAttributes {
    readonly TYPE_TEXT: "T";
    readonly TYPE_TEXT_LIST: "LT";
    readonly TYPE_DATE: "D";
    readonly TYPE_DATE_LIST: "LD";
    readonly TYPE_NUMBER: "N";
    readonly TYPE_NUMBER_LIST: "LN";
    readonly TYPE_DYNAMIC_LIST: "DL";
    readonly TYPE_BIG: "B";
    readonly DYNAMIC_ROW: "DR";
    readonly ATTR_MULTILINE: "M";
    readonly ATTR_USER_DEFINED: "S";
    readonly ATTR_USER: "U";
    readonly ATTR_ORG_STRUCT: "O";
    readonly ATTR_ROLE: "R";
    readonly ATTR_SEQUENCE: "S";
    readonly ATTR_FILE: "F";
    readonly ATTR_STATE_VAR: "V";
    readonly LOV_IS_SELECTED_YES: "Y";
    readonly LOV_IS_SELECTED_NO: "N";
    readonly USAGE_NONE: "N";
    readonly USAGE_READ: "R";
    readonly USAGE_WRITE: "W";
    readonly USAGE_MUST: "M";
    readonly TYPE_LOV_TEXT: "LT";
    readonly TYPE_LOV_NUMBER: "LN";
    readonly TYPE_LOV_DATE: "LD";

    IVAR_ID: number;
    IPROC_ID: number;
    IVAR_NAME: string;
    IVAR_TYPE: string;
    IVAR_MULTITASK_BEHAVIOUR: string;
    IVAR_TEXT_VALUE: string;
    IVAR_NUMBER_VALUE: number;
    IVAR_DATE_VALUE: Date;
    IVAR_ATTRIBUTE: string;
    TVAR_ID: number;
    DLIST_NAME: string;
    IVAR_MULTI: string;
    IVAR_MULTI_SELECTED: string;
    IVAR_BIG_VALUE: string;
    IVAR_DT_INDEX: number;
    IVAR_COL_INDEX: number;
    ORG_ID: number;
    IVAR_CLASS: string;
}

interface IVariableMethods {
    attributes(): Record<string, object>;
    canBeSnapshot(): boolean;
    relations(): { template_variables: () => TemplateVariable };
    set value(value: any);
    get value(): any;
    getWithLovValue(): Promise<any>;
    getPrintValue(dateFormat: string): Promise<string>;
    getPrintSafeValue(
        dateFormat: string,
        noEscAmpersand: boolean,
    ): Promise<string>;
    joinMultiLovs(): string;
    getIndex(): number;
    isMultichoice(): boolean;
    getValueColumn(): string;
    setLovData(data: any, lang: any): void;
    getLovData(lang: any): any;
    setLovDataByBaseValue(baseValue: any): void;
    getLovDataByBaseValue(baseValue: any): any;
    set lovValue(value: string | null);
    get lovValue(): string | null;
    getLovFilter(): any[];
    setLOVFilter(filter: any): void;
    hasValidDate(): boolean;
    fillLovValues(): Promise<void>;
    copyFromVariable(otherVar: IVariableAttributes): void;
    isDynamicRowType(): boolean;
    type(): string[];
    getValueForIteration(): any;
    getDtMultiIndexesAndValues(values: any): { indexes: any[]; values: any[] };
}

export interface IVariable
    extends Partial<IVariableAttributes>,
        IVariableMethods,
        Partial<LocalizedRecord<"IVAR_NAME">> {}

export class Variable extends BaseEntity implements IVariable {
    public static readonly consts = {
        TYPE_TEXT: "T",
        TYPE_TEXT_LIST: "LT",
        TYPE_DATE: "D",
        TYPE_DATE_LIST: "LD",
        TYPE_NUMBER: "N",
        TYPE_NUMBER_LIST: "LN",
        TYPE_DYNAMIC_LIST: "DL",
        TYPE_BIG: "B",
        TYPE_DYNAMIC_TABLE: "DT",
        TYPE_DYNAMIC_ROW: "DR",

        ATTR_MULTILINE: "M",
        ATTR_USER_DEFINED: "S",
        ATTR_USER: "U",
        ATTR_ORG_STRUCT: "O",
        ATTR_ROLE: "R",
        ATTR_SEQUENCE: "S",
        ATTR_FILE: "F",
        ATTR_STATE_VAR: "V", // State variable(DT)

        LOV_IS_SELECTED_YES: "Y",
        LOV_IS_SELECTED_NO: "N",

        USAGE_NONE: "N",
        USAGE_READ: "R",
        USAGE_WRITE: "W",
        USAGE_MUST: "M",

        TYPE_LOV_TEXT: "LT",
        TYPE_LOV_NUMBER: "LN",
        TYPE_LOV_DATE: "LD",
    };

    constructor() {
        super();
        this._lovData = [];
        this._lovValue = null;
    }

    attributes() {
        const attrs = {
            IVAR_ID: { primary: true },
            IPROC_ID: {
                type: "number",
                foreign: "Process",
                alias: "IVIPI",
            },
            IVAR_NAME: { type: "string", translations: true },
            IVAR_TYPE: { type: "string" },
            IVAR_MULTITASK_BEHAVIOUR: { type: "string" },
            IVAR_TEXT_VALUE: { type: "string" },
            IVAR_NUMBER_VALUE: { type: "number" },
            IVAR_DATE_VALUE: { type: "date" },
            IVAR_ATTRIBUTE: { type: "string" },
            TVAR_ID: {
                type: "number",
                foreign: "TemplateVariable",
                alias: "IVTI",
            },
            DLIST_NAME: { type: "string" },
            IVAR_MULTI: { type: "string" },
            IVAR_MULTI_SELECTED: { type: "text" },
            IVAR_BIG_VALUE: { type: "text" },
            IVAR_DT_INDEX: { type: "number" },
            IVAR_COL_INDEX: { type: "number" },
            ORG_ID: { type: "number" },
            IVAR_CLASS: { type: "text", default: "''" },
        };

        return super.extendTranslations(attrs);
    }

    canBeSnapshot() {
        return this._raw.TVAR_COPY_SNAPSHOT === "Y";
    }

    relations() {
        // Used to orm filter and raw translation.
        return {
            template_variables: () => new TemplateVariable(),
        };
    }

    /**
     * Use this to save variable.
     * @param value
     */
    set value(value) {
        if (this.isMultichoice()) {
            if (!Array.isArray(value)) {
                if (value === "" || value === false || value === null) {
                    // resetting array with these values
                    value = [];
                } else {
                    value = [value]; // why, for historical reasons?
                }
            } else if (_.isEqual(value, [null]) || _.isEqual(value, [""])) {
                value = [];
            }
        }

        // empty string as null to prevent setting date to 1970-01-01
        const oldValue = value;
        value = value === "" ? null : value; // null and '' can clear date variable.

        // validate Date
        if (
            _.includes(
                [Variable.consts.TYPE_DATE, Variable.consts.TYPE_DATE_LIST],
                this.IVAR_TYPE,
            ) &&
            !(value instanceof Date) &&
            value !== null &&
            !this.isMultichoice() // validation ignores multichoice for now
        ) {
            try {
                value = new Date(value); // could be invalid until validation
                if (Object.prototype.toString.call(value) === "[object Date]") {
                    if (isNaN(value.getTime())) {
                        // date is not valid
                        throw new Error();
                    }
                    // date is valid
                } else {
                    throw new Error();
                }
            } catch (_err: any) {
                throw new UserException(
                    `Wrong format or incorrect date '${oldValue}'. Use ISO 8601 (2011-10-05T14:48:00.000Z) or Date object.`,
                );
            }
        }

        // is file - WTF ignore multichoice
        if (
            this.IVAR_ATTRIBUTE == Variable.consts.ATTR_FILE &&
            Array.isArray(value)
        ) {
            value = value.join(":");
        }

        // if multichoice jsonify the value
        if (this.IVAR_TYPE === "DR") {
            try {
                // Clear DR. Clear each column.
                if (!value) {
                    const keys = Object.keys(this.value);
                    const clearDR = {};
                    keys.forEach((key) => {
                        clearDR[key] = [null];
                    });
                    this[this.getValueColumn()] = JSON.stringify(clearDR);
                } else {
                    const json =
                        typeof value === "string" ? JSON.parse(value) : value;
                    const keys = Object.keys(json);
                    // Iterate DR rows/cols.
                    for (let i = 0; i < keys.length; i++) {
                        const drRow = json[keys[i]];
                        if (Array.isArray(drRow) && drRow.length > 0) {
                            // Each item in row.
                            for (let j = 0; j < drRow.length; j++) {
                                const cell = drRow[j];
                                // Is not number
                                if (
                                    isNaN(cell) &&
                                    moment(
                                        cell,
                                        moment.ISO_8601,
                                        true,
                                    ).isValid()
                                ) {
                                    // localise date 2016-05-11T00:00:00
                                    drRow[j] = moment(cell)
                                        .tz(globalThis.dynamicConfig.serverZone)
                                        .format("YYYY-MM-DDTHH:mm:ss");
                                }
                            }
                        }
                    }
                    this[this.getValueColumn()] = JSON.stringify(json);
                }
            } catch (err) {
                globalThis.tasLogger.error(err.message);
                this[this.getValueColumn()] = value;
            }
        } else if (this.isMultichoice()) {
            if (value && this.IVAR_TYPE == "LD") {
                const ivarMultiLocalised = [];
                try {
                    if (Array.isArray(value)) {
                        value.forEach((v) => {
                            const date = new Date(v);
                            if (date == "Invalid Date") {
                                throw new new UserException(
                                    `Wrong date format. Use ISO 8601 (2011-10-05T14:48:00.000Z).`,
                                )();
                            }
                            const localised = moment(date)
                                .tz(globalThis.dynamicConfig.serverZone)
                                .format("YYYY-MM-DDTHH:mm:ss"); // 2016-05-11T00:00:00
                            ivarMultiLocalised.push(localised);
                        });
                        value = ivarMultiLocalised;
                    }
                } catch (ignored) {
                    globalThis.tasLogger.error(ignored);
                }
            } else if (this.IVAR_TYPE === "LN") {
                if (value && value !== "" && !_.every(value, Number)) {
                    throw new UserException(
                        `LN multi variable '${this.IVAR_NAME}': Only numbers are allowed as a value`,
                        "BAD_INPUT",
                    );
                }
            }

            this.IVAR_MULTI_SELECTED = JSON.stringify(value);
        } else if (this.IVAR_TYPE === "T" && this.IVAR_BIG_VALUE) {
            this[this.getValueColumn()] = value;
            this.IVAR_BIG_VALUE = value;
        } else if (this.IVAR_TYPE === "N") {
            if (!UtilsService.isNumericString(value)) {
                this[this.getValueColumn()] = null;
            } else {
                this[this.getValueColumn()] = Number(value);
            }
        } else {
            this[this.getValueColumn()] = value;
        }
    }

    get value() {
        if (this.IVAR_TYPE === "DR" && this.IVAR_BIG_VALUE) {
            // Transform date to zulu format.
            try {
                const json = JSON.parse(this.IVAR_BIG_VALUE);
                if (json) {
                    const keys = Object.keys(json);
                    // Iterate DR rows/cols.
                    for (let i = 0; i < keys.length; i++) {
                        const drRow = json[keys[i]];
                        if (Array.isArray(drRow) && drRow.length > 0) {
                            // Each item in row.
                            for (let j = 0; j < drRow.length; j++) {
                                const cell = drRow[j];
                                // Is not number
                                if (
                                    moment(
                                        cell,
                                        "YYYY-MM-DDTHH:mm:ss",
                                        true,
                                    ).isValid()
                                ) {
                                    const _date = moment(
                                        cell,
                                        "YYYY-MM-DDTHH:mm:ss",
                                    );
                                    drRow[j] = new Date(_date.utc()); // convert to zulu time.
                                }
                            }
                        }
                    }
                }
                return json;
            } catch (err) {
                globalThis.tasLogger.error(err.message);
                return this[this.getValueColumn()];
            }
        } else if (this.isMultichoice()) {
            // Convert local timezone to ZULU time
            if (this.IVAR_MULTI_SELECTED && this.IVAR_TYPE == "LD") {
                try {
                    const val = JSON.parse(this.IVAR_MULTI_SELECTED);
                    const out = [];
                    if (Array.isArray(val)) {
                        val.forEach((item) => {
                            const _date = moment(item, "YYYY-MM-DDTHH:mm:ss");
                            const zuluTime = new Date(_date.utc());

                            out.push(zuluTime.toISOString());
                        });
                    }
                    return out;
                } catch (err) {
                    globalThis.tasLogger.error(err.message);
                }
            }

            try {
                if (this.IVAR_MULTI_SELECTED) {
                    return JSON.parse(this.IVAR_MULTI_SELECTED);
                }
                return []; // returns empty array as default
            } catch (err) {
                globalThis.tasLogger.error(err.message);
                throw err;
            }
        } else if (
            this.IVAR_TYPE === "T" &&
            this.IVAR_ATTRIBUTE !== Variable.consts.ATTR_FILE
        ) {
            if (this.IVAR_BIG_VALUE) {
                return this.IVAR_BIG_VALUE;
            }
            const tval = this[this.getValueColumn()];
            return tval;
            // return (tval === null ? '' : tval);
        }

        // is file - WTF ignore multichoice sign in DB
        if (
            this.IVAR_ATTRIBUTE == Variable.consts.ATTR_FILE &&
            this[this.getValueColumn()] !== null
        ) {
            const fileNames = this[this.getValueColumn()].split(":");
            if (fileNames.length > 0) {
                if (!fileNames[fileNames.length - 1]) {
                    fileNames.splice(-1, 1);
                }
            }
            return fileNames;
        }

        return this[this.getValueColumn()];
    }

    /**
     * Returns translated LOV value(s) into text. Intended to use
     * where is expected following calculations.
     * return {Promise<any|[]|[]|string>}
     */
    async getWithLovValue() {
        if (this.IVAR_TYPE === VAR.TYPE_DYNAMIC_LIST) {
            await this.fillLovValues();
            return this.isMultichoice() ? this.joinMultiLovs() : this.lovValue;
        }
        return this.value;
    }

    /**
     * Returns safe value to print
     * Not intended to use when real (non-sanitized) value is necessary
     * Needs TVAR_META to resolve numberOfDecimals.
     */
    async getPrintValue(dateFormat: string) {
        let variableValue = this.value;
        if (this.IVAR_TYPE === "DR" && typeof variableValue === "object") {
            variableValue = JSON.stringify(variableValue);
        } else if (variableValue && this.IVAR_TYPE === VAR.TYPE_DATE) {
            variableValue = moment(variableValue, "YYYY-MM-DDTHH:mm:ss").format(
                dateFormat,
            );
        } else if (
            this.IVAR_TYPE === VAR.TYPE_DATE_LIST &&
            this.isMultichoice() &&
            this.IVAR_MULTI_SELECTED
        ) {
            const arr = [];
            JSON.parse(this.IVAR_MULTI_SELECTED).forEach((val) => {
                arr.push(moment(val, "YYYY-MM-DDTHH:mm:ss").format(dateFormat));
            });
            variableValue = arr;
        } else if (this.IVAR_TYPE === VAR.TYPE_DYNAMIC_LIST) {
            variableValue = await this.getWithLovValue();
        } else if (this.IVAR_TYPE === "N") {
            const tvarMeta = this.raw.TVAR_META;
            const parsedMeta = JSON.parse(tvarMeta || "{}");
            if (
                typeof parsedMeta.numberOfDecimals !== "undefined" &&
                Number.isInteger(variableValue)
            ) {
                variableValue = variableValue.toFixed(
                    parsedMeta.numberOfDecimals,
                );
            }
        } else if (
            this.IVAR_TYPE === VAR.TYPE_DYNAMIC_TABLE &&
            this.isMultichoice()
        ) {
            variableValue = this.getDtMultiIndexesAndValues().values;
        }

        if (Array.isArray(variableValue)) {
            return JSON.stringify(variableValue);
        }

        if (variableValue === null) {
            return "";
        }

        return variableValue;
    }

    /**
     * Same as getPrintValue() method but returns sanitized values.
     * Needs TVAR_META to resolve numberOfDecimals.
     */
    async getPrintSafeValue(
        dateFormat: string = "L",
        noEscAmpersand: boolean = true,
    ) {
        let printSafeValue = htmlSanitize(
            await this.getPrintValue(dateFormat),
            noEscAmpersand,
        );
        if (this.IVAR_TYPE === VAR.TYPE_TEXT_LIST) {
            printSafeValue = printSafeValue.split(",").join(", ");
        }
        return printSafeValue;
    }

    joinMultiLovs() {
        if (!this.lovValues) {
            return "";
        }
        const names = [];
        Object.keys(this.lovValues).forEach((id) => {
            names.push(this.lovValues[id]);
        });
        return names.join(", ");
    }

    getIndex() {
        return this.IVAR_DT_INDEX;
    }

    isMultichoice() {
        return this.IVAR_MULTI == "X";
    }

    getValueColumn() {
        let colName;
        if (this.IVAR_MULTI == "X") {
            colName = "IVAR_MULTI_SELECTED";
        } else if (this.IVAR_TYPE == "DT") {
            colName = "IVAR_TEXT_VALUE";
        } else if (this.IVAR_TYPE == "T" || this.IVAR_TYPE == "LT") {
            colName = "IVAR_TEXT_VALUE";
        } else if (this.IVAR_TYPE == "N" || this.IVAR_TYPE == "LN") {
            colName = "IVAR_NUMBER_VALUE";
        } else if (this.IVAR_TYPE == "D" || this.IVAR_TYPE == "LD") {
            colName = "IVAR_DATE_VALUE";
        } else if (this.IVAR_TYPE == "DL" && !this.IVAR_ATTRIBUTE) {
            colName = "IVAR_TEXT_VALUE";
        } else if (this.IVAR_TYPE == "DL") {
            colName = "IVAR_NUMBER_VALUE";
        } else if (
            (this.IVAR_TYPE == "B" && this.IVAR_ATTRIBUTE == "T") ||
            this.IVAR_TYPE == "DR"
        ) {
            colName = "IVAR_BIG_VALUE";
        } else {
            throw new UserException(
                `Unrecognized variable type: ivar_type = ${this.IVAR_TYPE}, ivar_attribute = ${this.IVAR_ATTRIBUTE}`,
            );
        }

        return colName;
    }

    /**
     * Sets data for lov for future use.
     *
     * @param data.title {string}
     * @param data.value {string}
     */
    setLovData(data, lang = null) {
        if (lang) {
            this[`_lovData_${lang}`] = data;
        } else {
            this._lovData = data;
        }
    }

    /**
     *
     * @returns {Array|*}
     */
    getLovData(lang: string = null) {
        return lang ? this[`_lovData_${lang}`] : this._lovData;
    }

    setLovDataByBaseValue(baseValue) {
        Object.entries(this.getLovDataByBaseValue(baseValue)).forEach(
            ([lovAttribute, value]) => {
                this._raw[lovAttribute] = value;
            },
        );
    }

    getLovDataByBaseValue(baseValue) {
        return globalThis.dynamicConfig.langs.reduce((accumulator, lang) => {
            const matchedItem = (
                this[`_lovData_${lang.toLowerCase()}`] || []
            ).find((lov) => lov.value == baseValue);
            accumulator[`TVARLOV_TEXT_VALUE_${lang.toUpperCase()}`] =
                matchedItem ? matchedItem.title : null;
            return accumulator;
        }, {});
    }

    /**
     * @param value {null|string}
     */
    set lovValue(value) {
        this._lovValue = value;
    }

    /**
     * Return translated id to name.
     *
     * @returns {null|string}
     */
    get lovValue() {
        return this._lovValue;
    }

    /**
     * Get parsed IVAR_BIG_VALUE
     * @returns {Array}
     */
    getLOVFilter() {
        if (!this.IVAR_BIG_VALUE) {
            return [];
        }
        return JSON.parse(this.IVAR_BIG_VALUE);
    }

    setLOVFilter(filter) {
        this.IVAR_BIG_VALUE = JSON.stringify(filter);
    }

    /**
     * Returns true if variable has valid date as value
     * https://stackoverflow.com/a/1353711
     * @return {bool}
     */
    hasValidDate() {
        if (Object.prototype.toString.call(this.value) === "[object Date]") {
            // it is a date
            if (isNaN(this.value.getTime())) {
                // d.valueOf() could also work
                return false; // date is not valid
            }
            // date is valid
            return true;
        }
        // not a date
        return false;
    }

    fillLovValues() {
        if (this.value === null || this.IVAR_TYPE !== "DL") {
            return;
        }
        if (this.isMultichoice()) {
            const ids =
                typeof this.value === "string"
                    ? JSON.parse(this.value)
                    : this.value;

            // Multi users
            if (
                this.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER &&
                ids.length > 0
            ) {
                return globalThis.orm
                    .repo("user", this.connection)
                    .getMulti(ids)
                    .then((users) => {
                        this.lovValues = {};
                        users.forEach((user) => {
                            this.lovValues[user.USER_ID] =
                                user.USER_DISPLAY_NAME;
                        });

                        return this;
                    })
                    .catch((err) => {
                        globalThis.tasLogger.error(err);
                        this.lovValue = null;
                    });
            }

            // Multi organizations
            if (this.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT) {
                return globalThis.orm
                    .repo("organizationStructure", this.connection)
                    .getMulti(ids)
                    .then((orgs) => {
                        this.lovValues = {};
                        orgs.forEach((orgstr) => {
                            this.lovValues[orgstr.ORGSTR_ID] =
                                orgstr.ORGSTR_NAME;
                        });

                        return this;
                    })
                    .catch((err) => {
                        globalThis.tasLogger.error(err);
                        this.lovValue = null;
                    });
            }

            // Multi roles
            if (this.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE) {
                return globalThis.orm
                    .repo("role", this.connection)
                    .getMulti(ids)
                    .then((roles) => {
                        this.lovValues = {};
                        roles.forEach((role) => {
                            this.lovValues[role.ROLE_ID] = role.ROLE_NAME;
                        });

                        return this;
                    })
                    .catch((err) => {
                        globalThis.tasLogger.error(err);
                        this.lovValues = null;
                    });
            }
            return this;
        }
        // on empty or not dyn.list or is multichoice then exit
        if (
            this.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER &&
            this.value > 0
        ) {
            // skip non-filled value
            return globalThis.orm
                .repo("user", this.connection)
                .get(this.value)
                .then((user) => {
                    this.lovValue = user.USER_DISPLAY_NAME;
                    return this;
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    this.lovValue = null;
                });
        }
        if (this.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT) {
            return globalThis.orm
                .repo("organizationStructure", this.connection)
                .get(this.value)
                .then((org) => {
                    this.lovValue = org.ORGSTR_NAME;
                    return this;
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    this.lovValue = null;
                });
        }
        if (this.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE) {
            return globalThis.orm
                .repo("role", this.connection)
                .get(this.value)
                .then((role) => {
                    this.lovValue = role.ROLE_NAME;
                    return this;
                })
                .catch((err) => {
                    globalThis.tasLogger.error(
                        `${err} ivar_id = ${this.IVAR_ID}`,
                    );
                    this.lovValue = null;
                });
        }
        return this;
    }

    /**
     * Copy values from other variable.
     * @param otherVar
     */
    copyFromVariable(otherVar) {
        this.IVAR_TEXT_VALUE = otherVar.IVAR_TEXT_VALUE;
        this.IVAR_NUMBER_VALUE = otherVar.IVAR_NUMBER_VALUE;
        this.IVAR_DATE_VALUE = otherVar.IVAR_DATE_VALUE;
        this.DLIST_NAME = otherVar.DLIST_NAME;
        this.IVAR_MULTI = otherVar.IVAR_MULTI;
        this.IVAR_MULTI_SELECTED = otherVar.IVAR_MULTI_SELECTED;
        this.IVAR_BIG_VALUE = otherVar.IVAR_BIG_VALUE;
        this.IVAR_DT_INDEX = otherVar.IVAR_DT_INDEX;
        this.IVAR_COL_INDEX = otherVar.IVAR_COL_INDEX;
    }

    isDynamicRowType() {
        return this.IVAR_TYPE === "DR";
    }

    /**
     * type - returns type of the variable, first field in array is type. If type is DL then in second parameter is type of dynamic list
     * for historicaly reasons
     *
     * @return array
     */
    type() {
        const out = [this.IVAR_TYPE];
        if (this.IVAR_TYPE === VAR.TYPE_DYNAMIC_LIST) {
            if (this.IVAR_ATTRIBUTE === null) {
                out.push(this.DLIST_NAME);
            } else {
                out.push(`$${this.IVAR_ATTRIBUTE}`);
            }
        }
        return out;
    }

    /**
     * Returns prepared array for iteration independent of type
     * @return {Array|null}
     */
    getValueForIteration() {
        return this.isDynamicRowType()
            ? JSON.parse(this.IVAR_MULTI_SELECTED)
            : this.value;
    }

    /**
     * Returns DT Multi indexes and values
     * IVAR_MULTI_SELECTED for DT = [{"DTV_INDEX":"DTV_VALUE"}, ...]
     * @param values {array}
     * @return {object} { indexes: ["DTV_INDEX", ...], values: ["DTV_VALUE", ...] }
     */
    getDtMultiIndexesAndValues(values?) {
        const retObj = {
            indexes: [],
            values: [],
        };
        let ivarMultiSelected = values || this.IVAR_MULTI_SELECTED;

        try {
            ivarMultiSelected = JSON.parse(ivarMultiSelected);
        } catch (_ignored) {
            // nothing
        }

        if (Array.isArray(ivarMultiSelected)) {
            ivarMultiSelected.forEach((obj) => {
                const [index, val] = Object.entries(obj)[0];
                retObj.indexes.push(index);
                retObj.values.push(val);
            });
        }

        return retObj;
    }
}
