import _ from "lodash";
import events from "events";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

const { Loggable } = require("../../../utils/logger/Loggable");

export interface EntityAttribute {
    type?: string;
    translations?: boolean;
    name?: boolean;
    primary?: boolean;
    dirty?: boolean;
    value?: unknown;
    seq?: string;
    translated?: unknown;
    getDefaultAttr?: () => string;
    notNull?: boolean;
    uniqueIn?: unknown;
    unique?: boolean;
    duplicateNulls?: unknown;
    foreign?: unknown;
    alias?: string;
    in?: unknown;
    maxLength?: number;
    isNotNumeric?: boolean;
    default?: unknown;
}

type LocalizedLanguage = "CS" | "EN" | "SK" | "PL";
export type LocalizedRecord<K extends string> = Record<
    `${K}_${LocalizedLanguage}`,
    string
>;

export abstract class BaseEntity extends Loggable {
    public _raw: any;

    public readonly _attrs: Record<string, EntityAttribute>;

    private _lang?: string;

    private _toInsert: boolean;

    private _primaryColumn?: string | string[];

    private _relatedOne: Record<string, string>;

    private _relatedMany: Record<string, string>;

    constructor(id = null) {
        super();

        // prepare object properties
        this._raw = {};
        this._attrs = {};
        const attrs = this.attributes(); // call overriden method

        // merge objects, makes default wrap
        for (const attr in attrs) {
            if (attrs.hasOwnProperty(attr)) {
                this._attrs[attr] = _.assign(
                    {
                        //originValue: undefined, // value loaded from db/repository (null can be also dirty)
                        dirty: false,
                        validations: [],
                        type: null,
                        value: undefined, // real stored value
                        // not implemented(default isn't possible) defaultValue: undefined,
                        // default value (undefined = never insert/update if undefined, null+other = insert/update this value in all cases)
                        primary: false,
                    },
                    attrs[attr],
                );
            }
        }

        // Primary columns defined directly.
        const primary = this.primaryOrder();
        if (primary) {
            this._primaryColumn = primary;
        }

        // building-up properties
        Object.keys(this._attrs).forEach((attrName) => {
            //this[attrName] = undefined; // starts as default value (undefined)

            // store info about primaryColumn
            if (
                !primary &&
                this._attrs[attrName].primary &&
                this._attrs[attrName].translations !== false
            ) {
                if (this._primaryColumn) {
                    // if first primary is defined yet
                    this._primaryColumn = [
                        this._primaryColumn as string,
                        attrName,
                    ]; // multiple primary keys, must be in consistent order when using get(), store()
                    //throw new InternalException('Multiple primary column.');
                } else {
                    this._primaryColumn = attrName;
                }
            }

            // define magic setters and getters, but disallows inheritance :-(
            Object.defineProperty(this, attrName, {
                set(val) {
                    this.setAttr(attrName, val, true);
                },
                get() {
                    return this._attrs[attrName].value;
                },
            });
        });

        // sets primary id
        try {
            this.id = id !== null ? id : null;
        } catch (err: any) {
            // Ignore this for entites with no primary colum.;
            if (err.codeName !== "MISSING_PRIMARY_COLUMN_EXCEPTION") {
                throw err;
            }
        }

        this._related = {}; // holds pairs relation names with their entities
        this._relatedOne = {};
        this._relatedMany = {};
        this._toInsert = false;

        // callbacks which are bound to before store event
        this.events = new events.EventEmitter();
    }

    primaryOrder(): string[] | null {
        return null;
    }

    get id(): number | number[] {
        // supports only single and double primary columns
        return this.hasMultiPrimaryColumn()
            ? [this[this._primaryColumn![0]], this[this._primaryColumn![1]]]
            : this[this._primaryColumn as string];
    }

    set id(nid: number[] | number | null) {
        if (this.hasMultiPrimaryColumn()) {
            this[this._primaryColumn![0]] =
                nid === null ? null : (nid as number[])[0]; // null when resetting primary key
            this[this._primaryColumn![1]] =
                nid === null ? null : (nid as number[])[1];
        } else {
            this[this._primaryColumn as string] = nid;
        }
    }

    set primaryColumn(column: string | string[]) {
        this._primaryColumn = column;
    }

    get primaryColumn(): string | string[] {
        if (!this._primaryColumn) {
            throw new InternalException(
                "Primary column is not defined.",
                "MISSING_PRIMARY_COLUMN_EXCEPTION",
            );
        }
        return this._primaryColumn;
    }

    /**
     * Returns if the entity is marked as toInsert for ex. in multiple key environment.
     */
    get toInsert(): boolean {
        return this._toInsert;
    }

    hasMultiPrimaryColumn(): boolean {
        if (!this._primaryColumn) {
            return false;
        }

        return Array.isArray(this.primaryColumn);
    }

    /**
     * Returns true if attrName is primaryColumn or is a part of multiple primary column.
     */
    hasPrimaryColumn(attrName: string): boolean {
        // There is not primary column.
        if (!this._primaryColumn) {
            return false;
        }

        if (this.hasMultiPrimaryColumn()) {
            // not throw Error if primary doesn't exist
            return this._primaryColumn.indexOf(attrName) !== -1;
        } else {
            return this._primaryColumn === attrName;
        }
    }

    /**
     * Forces to insert when calling repository.store.
     * When changing multiple primary key for ex. in case of copying entity to insert with another multiple primary key.
     */
    forceToInsert(status: boolean = true) {
        this._toInsert = status;
        this.makeAllDirty();
    }

    /**
     * Sets raw data retrieved from db
     *
     * @param data
     */
    set raw(data: any) {
        this._raw = data;
    }

    /**
     * Contains raw values from database since last fetch.
     *
     * @returns {}
     */
    get raw() {
        return this._raw;
    }

    /**
     * Sets internal attribute with dirty status.
     *
     * @param attrName
     * @param value
     * @param dirty
     */
    setAttr(attrName: string, value: any, dirty: boolean = false): void {
        // entity with multiple key and mark as to insert make its primary key dirty
        dirty =
            dirty && (!this.hasPrimaryColumn(attrName) || this.toInsert)
                ? dirty
                : false;

        if (
            (`${this._attrs[attrName].type}` === "dateTime" ||
                `${this._attrs[attrName].type}` === "date") &&
            value != null
        ) {
            try {
                this._attrs[attrName].value = new Date(value);
            } catch (err) {
                // @ts-ignore
                globalThis.tasLogger.error((err as Error).message, {
                    err,
                });
            }
        } else {
            this._attrs[attrName].value = value;
        }

        this._attrs[attrName].dirty = dirty;
        this._raw[attrName] = this._attrs[attrName].value;
    }

    hasProperty(attrName: string): boolean {
        return this._attrs.hasOwnProperty(attrName);
    }

    validate(): void {
        for (const attr in this._attrs) {
            if (typeof this._attrs[attr].type !== "undefined") {
                continue; // Never returns ID (primary column) with dirty attributes.
            }
        }
    }

    /**
     * Fill data using setters.
     * @param data
     * @param dirty Are the attributes dirty? (default=false)
     * @param prefix Strip prefix from values (without _).
     */
    fill(data: Record<string, any>, dirty?: boolean, prefix?: string): this {
        dirty = typeof dirty !== "undefined" ? dirty : false;
        prefix = typeof prefix !== "undefined" ? prefix + "_" : undefined;

        // strip prefix from keys of data
        if (prefix) {
            data = _.mapKeys(data, (_val, key) => {
                if (_.startsWith(key, prefix)) {
                    return key?.replace(prefix, "");
                }
                return key;
            });
        }

        // can modify data to fill in entity
        this.events.emit("beforeFill", this, data);
        for (const prop in data) {
            if (this.hasProperty(prop)) {
                this.setAttr(prop, data[prop], dirty);
            }
        }
        this._raw = data;

        return this;
    }

    /**
     * Returns attributes if given onlyDirty status returns attrs which aren't stored in repository.
     */
    getAttributes(
        onlyDirty: boolean = false,
        withMeta: boolean = false,
    ): Record<string, any> {
        const result: Record<string, any> = {};
        for (const attr in this._attrs) {
            if (!onlyDirty) {
                result[attr] = withMeta ? this._attrs[attr] : this[attr];
                // Never returns ID (primary column) with dirty attributes except the situation toInsert == true
            } else if (
                this._attrs[attr].dirty &&
                (!this.hasPrimaryColumn(attr) || this.toInsert)
            ) {
                result[attr] = withMeta ? this._attrs[attr] : this[attr];
            }
        }
        return result;
    }

    /**
     * Returns column names. If filter specified, returns only columns on which filter callback returns true.
     */
    getColumnNames(
        filter?: (data: Record<string, any>, attrName: string) => boolean,
        alias?: string,
    ): string[] {
        const columns: string[] = [];
        const prefix = alias ? `${alias}.` : "";
        Object.keys(this._attrs).forEach((attrName) => {
            if (typeof filter === "function") {
                if (filter.apply(this, [this._attrs, attrName])) {
                    columns.push(prefix + attrName);
                }
            } else {
                columns.push(prefix + attrName);
            }
        });
        return columns;
    }

    isAttribute(name: string): boolean {
        return this._attrs.hasOwnProperty(name);
    }

    getColummnMeta(columnName: string): EntityAttribute {
        return this._attrs[columnName];
    }

    relatedOne(relName: string) {
        return this._relatedOne[relName];
    }

    relatedMany(relName: string) {
        return this._relatedMany[relName];
    }

    makeAllDirty(): void {
        const attrs = this.attributes();
        const keys = Object.keys(attrs);
        keys.forEach((key) => {
            this._attrs[key].dirty = true;
        });
    }

    clone() {
        // @ts-expect-error typing for constructor in TypeScript is messy https://github.com/microsoft/TypeScript/issues/3841
        const clone = new this.constructor();
        clone.fill(this.getAttributes(), true);
        clone.id = null; // resetting primary key
        return clone;
    }

    /******************************************************************************************************************/
    /* Methods to describe entity.
     /******************************************************************************************************************/

    abstract attributes(): Record<string, EntityAttribute>;

    relations(): Record<string, () => BaseEntity> {
        return {};
    }

    setLocalization(lang: string): void {
        if (lang) {
            this._lang = lang.toUpperCase();
        }
    }

    getTranslatedAttributes(): string[] {
        return Object.entries(this.attributes())
            .filter(([, translations]) => translations)
            .map(([attributeName]) => attributeName);
    }

    getTranslatedProperties(
        ignoredProperties: string[] = [],
        alias: string | null = null,
        addOriginalKey: boolean = false,
    ): string[] {
        const out: string[] = [];
        const attrs = this.attributes();
        Object.keys(attrs).forEach((name) => {
            if (
                attrs[name].translations === false ||
                (attrs[name].translations === true && addOriginalKey)
            ) {
                if (
                    !ignoredProperties.length ||
                    !ignoredProperties.some(
                        (ignored) => name.indexOf(ignored) > -1,
                    )
                ) {
                    out.push(alias ? `${alias}.${name}` : name);
                }
            }
        });

        return out;
    }

    extendTranslations(
        attributes: Record<string, EntityAttribute>,
    ): Record<string, EntityAttribute> {
        const _lang = this._lang ? this._lang.toUpperCase() : this._lang;

        const names = Object.keys(attributes);
        names.forEach((name) => {
            const attr = attributes[name];
            if (
                attr.translations &&
                // @ts-ignore
                Array.isArray(globalThis.dynamicConfig.langs) &&
                // @ts-ignore
                globalThis.dynamicConfig.langs.length > 0
            ) {
                // @ts-ignore
                globalThis.dynamicConfig.langs.forEach((lang) => {
                    lang = lang.toUpperCase();
                    const translatedName = name + "_" + lang;

                    if (_lang && lang.toUpperCase() != _lang) {
                        // Lang changed after attributes generated.
                        // @ts-expect-error ts(7053)
                        if (attr[translatedName]) {
                            // @ts-expect-error ts(7053)
                            delete attr[translatedName];
                            return;
                        }
                    }

                    // @ts-expect-error ts(7053)
                    if (typeof attr[translatedName] === "undefined") {
                        attributes[translatedName] = _.cloneDeep(attr);
                        attributes[translatedName].translations = false; // Avoid recursion.
                        attributes[translatedName].translated = lang;
                        attributes[translatedName].getDefaultAttr =
                            function () {
                                return name;
                            };
                        if (attributes[translatedName].notNull) {
                            attributes[translatedName].notNull = false;
                        }
                    }
                });
            }
        });
        return attributes;
    }

    hasEmptyPrimary(): boolean {
        const primary = this.primaryColumn;

        // Multiple primary keys, fill all missing.
        if (Array.isArray(primary)) {
            const attrs = this.attributes();
            for (const key of primary) {
                if (attrs[key].seq && !this[key]) {
                    return true;
                }
            }
            return false;
        }

        return !!this.id;
    }

    toLogObject(): Record<string, any> {
        if (this.logged) {
            return {
                id: this.hasMultiPrimaryColumn()
                    ? [
                          this[this._primaryColumn![0]],
                          this[this._primaryColumn![1]],
                      ]
                    : this[this._primaryColumn as string],
            };
        }
        this.logged = true;

        return _(this.raw).omit(_.isUndefined).omit(_.isNull).value();
    }

    setAttributeLegacy(key: string, value: any): void {
        this[key] = value;
    }
}
