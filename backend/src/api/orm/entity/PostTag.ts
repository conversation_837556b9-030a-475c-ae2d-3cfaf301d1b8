import { BaseEntity } from "./BaseEntity";

export class PostTag extends BaseEntity {
    attributes() {
        const attrs = {
            TAG_ID: { type: "integer", primary: true },
            TAG_NAME: {
                type: "string",
                maxLength: 255,
                notNull: true,
                translations: true,
                unique: true,
            },
            CREATED_AT: {
                type: "dateTime",
                notNull: true,
            },
            UPDATED_AT: {
                type: "dateTime",
                notNull: true,
            },
        };

        return super.extendTranslations(attrs);
    }
}
