import { BaseEntity } from "./BaseEntity";

export interface IPost extends Post {
    POST_VISIBILITY: {
        ROLES: number[];
        USERS: number[];
        ORGANIZATION_STRUCTURES: number[];
        TEMPLATES: number[];
        HEADERS: number[];
        TASKS: number[];
    };
}

export class Post extends BaseEntity {
    attributes() {
        const attrs = {
            POST_ID: { type: "integer", primary: true },
            POST_TITLE: {
                type: "string",
                translations: true,
                maxLength: 255,
                notNull: true,
            },
            POST_CONTENT: { type: "text", translations: true, notNull: true },
            CREATED_AT: {
                type: "dateTime",
                notNull: true,
            },
            UPDATED_AT: {
                type: "dateTime",
                notNull: true,
            },
            POST_PHONE: { type: "string", maxLength: 100 },
            POST_EMAIL: { type: "string", maxLength: 100 },
            //MAX URL LENGTH IS 2048
            POST_CUSTOM_URL: { type: "string", maxLength: 2048 },
            POST_STATE: {
                type: "string",
                maxLength: 100,
                in: ["ACTIVE", "INACTIVE", "PLANNED"],
            },
            POST_PUBLICATION_DATE: { type: "dateTime" },
            POST_PUBLICATION_END_DATE: { type: "dateTime" },
            POST_SEND_EMAIL: { type: "boolean", default: false, notNull: true },
            //POST PRIORITY BIGER NUMBER IS MORE IMPORTANT 0 - REGULAR, 1 - IMPORTANT
            POST_PRIORITY: { type: "integer", default: 0, notNull: true },
            //FOREIGN KEYS
            //M:M => POSTS_TAGS, POST_VISIBILITY
            POST_CREATED_BY: {
                type: "integer",
                foreign: "Users",
                references: "USER_ID",
            },
            POST_UPDATED_BY: {
                type: "integer",
                foreign: "Users",
                references: "USER_ID",
            },
        };

        return super.extendTranslations(attrs);
    }
}
