import { BaseEntity } from "./BaseEntity";
import { TemplateProcess } from "./TemplateProcess";
import { User } from "./User";
import * as processConst from "./const/processConst";

//Create interface
interface IProcessAttributes {
    IPROC_ID: number;
    TPROC_ID: number;
    IPROC_NAME: string;
    IPROC_NOTES: string;
    IPROC_PRIORITY: string;
    IPROC_STATUS: "A" | "N" | "S" | "D" | "E" | "X";
    IPROC_INST_OWNER_USER_ID: number;
    IPROC_INST_OWNER_ORGSTR_ID: number;
    IPROC_MAIN_IPROC_ID: number;
    IPROC_SUMMARY: string;
    IPROC_DUE_DATE_FINISH: Date;
    IPROC_ACTUAL_START_DATE: Date;
    IPROC_ACTUAL_FINISH_DATE: Date;
    ORG_ID: number;
    HEADER_ID: number;
    IPROC_VIS_ROLE_ID: number;
    IPROC_DESCRIPTION: string;
    IPROC_SUBPROCESS_FLAG: number;
    IPROC_EVENT_STARTER: number;
    IPROC_DMS_VISIBILITY: string;
    IPROC_MIXED: string;
    IPROC_URL: string;
    IPROC_URL_TAB_NAME: string;
    IPROC_CASE_STATUS: string;
    IPROC_HR_ROLE_ID: number;
    IPROC_SHREDDED: string;
}

interface IProcessRelations {
    template_process: () => TemplateProcess;
    owner: () => User;
}

interface IProcessMethods {
    relations(): IProcessRelations;
    setSubprocessReturn(): void;
}

export interface IProcess
    extends Partial<IProcessAttributes>,
        IProcessMethods {}

export class Process extends BaseEntity implements IProcess {
    public static readonly consts = {
        STATUS_ACTIVE: "A",
    };

    attributes() {
        const attrs = {
            IPROC_ID: { primary: true },
            TPROC_ID: {
                type: "integer",
                constraint: "TemplateProcess",
                foreign: "TemplateProcess",
                alias: "PTI",
            },
            IPROC_NAME: { type: "string" },
            IPROC_NOTES: { type: "text" },
            IPROC_PRIORITY: { type: "string" },
            IPROC_STATUS: {
                type: "string",
                in: [
                    processConst.STATUS_ACTIVE,
                    processConst.STATUS_NOT_ACTIVE,
                    processConst.STATUS_SUSPEND,
                    processConst.STATUS_DONE,
                    processConst.STATUS_ERASED,
                    processConst.STATUS_ERRORED,
                ],
            },
            IPROC_INST_OWNER_USER_ID: {
                type: "integer",
                foreign: "User",
                alias: "PIIOWUI",
            },
            IPROC_INST_OWNER_ORGSTR_ID: {
                type: "integer",
                foreign: "OrganizationStructure",
                alias: "PIIOOI",
            },
            IPROC_MAIN_IPROC_ID: {
                type: "integer",
                foreign: "Process",
                allowed: [-1],
                alias: "PIMII",
            },
            IPROC_SUMMARY: { type: "string", translations: true },
            IPROC_DUE_DATE_FINISH: { type: "date" },
            IPROC_ACTUAL_START_DATE: { type: "date" },
            IPROC_ACTUAL_FINISH_DATE: { type: "date" },
            ORG_ID: { type: "integer" },
            HEADER_ID: {
                type: "integer",
                foreign: "Header",
                alias: "PHI",
            },
            IPROC_VIS_ROLE_ID: {
                type: "integer",
                foreign: "Role",
                alias: "PIVRI",
            },
            IPROC_DESCRIPTION: { type: "string" },
            IPROC_SUBPROCESS_FLAG: { type: "integer" },
            IPROC_EVENT_STARTER: { type: "integer" },
            IPROC_DMS_VISIBILITY: { type: "string" },
            IPROC_MIXED: { type: "text" },
            IPROC_URL: { type: "string" },
            IPROC_URL_TAB_NAME: { type: "string" },
            IPROC_CASE_STATUS: {
                type: "string",
            },
            IPROC_HR_ROLE_ID: {
                type: "integer",
                foreign: "Role",
                alias: "IHRI",
            },
            IPROC_SHREDDED: { type: "string" },
        };
        return super.extendTranslations(attrs);
    }

    relations() {
        return {
            template_process: () => new TemplateProcess(),
            owner: () => new User(),
        };
    }

    setSubprocessReturn() {
        this.IPROC_SUBPROCESS_FLAG = 2;
    }
}
