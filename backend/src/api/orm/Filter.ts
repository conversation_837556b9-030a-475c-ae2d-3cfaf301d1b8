// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import moment from "moment";
import { WhereParser } from "../utils/whereParser";
import { EvalMath } from "../lib/EvalMath";
import { UtilsService } from "../services/UtilsService";
import { IllegalArgumentException } from "../../utils/errorHandling/exceptions/illegalArgumentException";

export class Filter {
    constructor(repository?, req?, options = {}) {
        this.consts = {
            AND: { rawValue: "<and>" },
            OR: { rawValue: "<or>" },
            EQ: { rawValue: "<eq>" },
            NE: { rawValue: "<ne>" },
            NOT_IN: { rawValue: "<nin>" },
            IN: { rawValue: "<in>" },
            LIKE: { rawValue: "<like>" },
            NLIKE: { rawValue: "<nlike>" },
            LE: { rawValue: "<le>" },
            GE: { rawValue: "<ge>" },
            LT: { rawValue: "<lt>" },
            GT: { rawValue: "<gt>" },
            ISNN: { rawValue: "<isnn>" },
            ISN: { rawValue: "<isn>" },
        };

        this.repository = repository;
        this.req = req;
        this.options = options;
    }

    apply(promise, filter) {
        let parsedFilter = this.parseRawFilter(filter);
        if (!parsedFilter) {
            return promise;
        }

        this.ini(promise);

        // Each filter is deeper filter. No filter can affect another
        parsedFilter = [parsedFilter];

        promise = this.chainMain(promise, parsedFilter);
        return promise;
    }

    ini(promise) {
        this._aliases = this.getAliases(promise);
        this._attrs = this.getAttrs();
    }

    chainMain(promise, parsed) {
        const self = this;
        parsed.forEach((item) => {
            if (item.type && item.type == "conjunction") {
                self._lastConjuction = item.value;
            } else if (item.type && item.type == "expression") {
                promise = self.chain(promise, item);
            } else if (Array.isArray(item) && item.length > 0) {
                promise = self.chainDeeper(promise, item);
            }
        });
        return promise;
    }

    chainDeeper(promise, deeper) {
        const self = this;
        promise[self.modifyByConjuction("where", self._lastConjuction)](
            function () {
                self.chainMain(this, deeper);
            },
        );
        return promise;
    }

    chain(promise, filter) {
        const self = this;

        let fk = filter.key;
        fk = fk.toUpperCase();
        // Allow filter by "ID"
        if (fk == "ID") {
            fk = this.repository.entity.primaryColumn;
        }
        // Sanitize name space and column name
        const colName = this.getColName(fk);

        if (this._attrs && !this._attrs[colName]) {
            this._attrs[colName] = {
                type: "string",
                key: colName,
            };
        }
        const attr = /* this._attrs[fk] || */ this._attrs[colName];

        let value = this.sanitizeValue(filter.value, filter.key);

        const sanitized = this.sanitizeKey(filter.key);
        let key =
            /* this._attrs[fk] && !sanitized.includes('.') && !this._filteringColumns[fk] ? fk : */ sanitized;

        const isDateColumn = attr.type == "dateTime" || attr.type == "date";
        if (
            isDateColumn &&
            filter.operator != self.consts.ISN.rawValue &&
            filter.operator != self.consts.ISNN.rawValue
        ) {
            if (
                filter.operator == self.consts.EQ.rawValue ||
                filter.operator == self.consts.GE.rawValue ||
                filter.operator == self.consts.LE.rawValue ||
                filter.operator == self.consts.NE.rawValue ||
                filter.operator == self.consts.GT.rawValue ||
                filter.operator == self.consts.LT.rawValue
            ) {
                const date = new Date(value);
                if (!UtilsService.validateDate(date)) {
                    throw new IllegalArgumentException(
                        `Invalid date (non-existing date or incorrect format). Try ISO 8601 2011-10-05T14:48:00.000Z instead ${value}.`,
                    );
                }
                const self = this;
                promise[self.modifyByConjuction("where", self._lastConjuction)](
                    function () {
                        const param = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
                        this[self.modifyByConjuction("whereRaw", null)](
                            `${globalThis.orm.db.trunc(key, moment(value))} ${self.getSimpleCondition(filter.operator)}  ${globalThis.orm.db.toDate()}`,
                            param,
                        );
                    },
                );
            }
        } else {
            let escape = this.options.allowAdditionalWildcards
                ? `escape '^'`
                : "";

            if (
                !this.options.allowAdditionalWildcards &&
                (filter.operator === self.consts.NLIKE.rawValue ||
                    filter.operator === self.consts.LIKE.rawValue)
            ) {
                value = value.replace(/_/g, "^_");
                value = value.replace(/(?!^%?)%(?!%{0}$)/g, "^%"); // except first and last %
                escape = `escape '^'`;
            }

            if (filter.operator === self.consts.ISN.rawValue) {
                promise = promise[
                    self.modifyByConjuction("whereNull", self._lastConjuction)
                ](globalThis.database.raw(key));
            } else if (filter.operator == self.consts.ISNN.rawValue) {
                promise = promise[
                    self.modifyByConjuction(
                        "whereNotNull",
                        self._lastConjuction,
                    )
                ](globalThis.database.raw(key));
                promise = promise[self.modifyByConjuction("where", "<and>")](
                    globalThis.database.raw(globalThis.orm.db.length(key)),
                    ">",
                    0,
                );
            } else if (filter.operator == self.consts.EQ.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](globalThis.database.raw(key), value);
            } else if (filter.operator == self.consts.NOT_IN.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](function () {
                    const values = value.split(",");
                    const chunked = _.chunk(values, 999);
                    chunked.forEach((piece) => {
                        if (chunked.length > 2) {
                            // If the array consists of strings, enclose it in simple quotes
                            if (typeof piece[0] === "string") {
                                piece = piece.map((item) => `'${item}'`);
                            }
                            this[
                                self.modifyByConjuction(
                                    "whereNotIn",
                                    self._lastConjuction,
                                )
                            ](
                                globalThis.database.raw(key),
                                globalThis.database.raw(piece),
                            );
                        } else {
                            this[
                                self.modifyByConjuction(
                                    "whereNotIn",
                                    self._lastConjuction,
                                )
                            ](globalThis.database.raw(key), piece);
                        }
                    });
                });
            } else if (filter.operator == self.consts.IN.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](function () {
                    const values = value.split(",");
                    const chunked = _.chunk(values, 999);
                    chunked.forEach((piece) => {
                        if (chunked.length > 2) {
                            // If the array consists of strings, enclose it in simple quotes
                            if (typeof piece[0] === "string") {
                                piece = piece.map((item) => `'${item}'`);
                            }

                            this[
                                self.modifyByConjuction(
                                    "orWhereIn",
                                    self._lastConjuction,
                                )
                            ](
                                globalThis.database.raw(key),
                                globalThis.database.raw(piece),
                            );
                        } else {
                            this[
                                self.modifyByConjuction(
                                    "orWhereIn",
                                    self._lastConjuction,
                                )
                            ](globalThis.database.raw(key), piece);
                        }
                    });
                });
            } else if (filter.operator == self.consts.NE.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](globalThis.database.raw(key), "<>", value);
            } else if (filter.operator == self.consts.GT.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](globalThis.database.raw(key), ">", value);
            } else if (filter.operator == self.consts.LT.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](globalThis.database.raw(key), "<", value);
            } else if (filter.operator == self.consts.GE.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](globalThis.database.raw(key), ">=", value);
            } else if (filter.operator == self.consts.LE.rawValue) {
                promise = promise[
                    self.modifyByConjuction("where", self._lastConjuction)
                ](globalThis.database.raw(key), "<=", value);
            } else if (filter.operator == self.consts.NLIKE.rawValue) {
                key = self.addQuotes(key, colName);
                const commaNumber = key.includes("NUMBER_VALUE")
                    ? value.replace(",", ".")
                    : value;
                const raw = this.getLikeExpression(
                    key,
                    escape,
                    this.options.disableAccentSensitivity,
                    "NOT LIKE",
                    commaNumber,
                );
                promise = promise[
                    self.modifyByConjuction("whereRaw", self._lastConjuction)
                ](raw, [commaNumber]);
            } else if (filter.operator == self.consts.LIKE.rawValue) {
                key = self.addQuotes(key, colName);
                const commaNumber = key.includes("NUMBER_VALUE")
                    ? value.replace(",", ".")
                    : value;
                const raw = this.getLikeExpression(
                    key,
                    escape,
                    this.options.disableAccentSensitivity,
                    globalThis.dynamicConfig.db.client === "postgresql"
                        ? "ILIKE"
                        : "LIKE",
                    commaNumber,
                );
                promise = promise[
                    self.modifyByConjuction("whereRaw", self._lastConjuction)
                ](raw, [commaNumber]);
            }
        }
        return promise;
    }

    getLikeExpression(key, escape, disableAccentSensitivity, operator, value) {
        // to avoid COLLATE on numbers
        if (key.includes("NUMBER_VALUE")) {
            return globalThis.orm.db.like(
                globalThis.dynamicConfig.db.client === "postgresql"
                    ? globalThis.orm.db.toString(key)
                    : key,
                escape,
                false,
                operator,
                value,
            );
        }

        return globalThis.orm.db.like(
            globalThis.dynamicConfig.db.client === "postgresql"
                ? globalThis.orm.db.toString(key)
                : key,
            escape,
            disableAccentSensitivity,
            operator,
            value,
        );
    }

    getColName(key) {
        const index = key.lastIndexOf(".");
        if (index == -1) {
            return key;
        }
        return key.substr(index + 1);
    }

    addQuotes(key, colName) {
        if (
            this._filteringColumns &&
            this._filteringColumns[colName] &&
            this._filteringColumns[colName].key === key
        ) {
            return key;
        }

        const index = key.lastIndexOf(".");
        if (
            index != -1 &&
            index == key.indexOf(".") &&
            key.indexOf("|") == -1
        ) {
            let newKey = key.substr(0, index);
            const colNew = key.substr(index + 1);
            if (!newKey.startsWith('"')) {
                newKey = `"${newKey}"`;
                key = `${newKey}.${colNew}`;
            }
        }

        return key;
    }

    sanitizeValue(value, key) {
        key = key.toUpperCase();

        // Allow filter by "ID"
        if (key == "ID") {
            key = this.repository.entity.primaryColumn;
        }
        // Sanitize name space and column name
        const colName = this.getColName(key);
        if (this._attrs && !this._attrs[colName]) {
            this._attrs[colName] = {
                type: "string",
                key: colName,
            };
        }

        const attr = this._attrs[colName];

        if (attr.value) {
            value = attr.value;
        }

        return value;
    }

    sanitizeKey(key) {
        const self = this;
        key = key.toUpperCase();

        // Allow filter by "ID"
        if (key == "ID") {
            key = this.repository.entity.primaryColumn;
        }
        const colName = this.getColName(key);

        const addAlias = function (k) {
            if (!k) {
                return k;
            }
            k = k.toUpperCase();

            const index = k.lastIndexOf(".");
            if (index == -1) {
                if (self._aliases[k]) {
                    return `${self._aliases[k]}.${k}`;
                }
            }
            return k;
        };
        key = addAlias(key);

        // Filtered column must be in Entity
        if (this._attrs && !this._attrs[colName]) {
            throw new IllegalArgumentException(
                `Unknown column name [${colName}].`,
            );
        }

        // Override virtual column name by sql e.g. user_full_name = USER_LAST_NAME || ' ' || USER_FIRST_NAME
        const attr = this._attrs[colName];
        if (attr.key) {
            key = attr.key;
        }

        return `"${key.split(".").join(`"."`)}"`;
    }

    getAliases(promise) {
        if (!promise._statements) {
            return;
        }

        const aliases = {};
        promise._statements.forEach((statement) => {
            if (
                statement.grouping == "columns" &&
                statement.value &&
                Array.isArray(statement.value)
            ) {
                statement.value.forEach((alias) => {
                    if (typeof alias === "string") {
                        const index = alias.indexOf(".");
                        if (index !== -1) {
                            const ns = alias.substr(0, index);
                            const columnName = alias.substr(index + 1);

                            aliases[columnName] = ns;
                        }
                    }
                });
            }
        });

        return aliases;
    }

    getAttrs() {
        const self = this;
        const attrs = this.repository.entity._attrs;
        const { relatedEntities } = this.repository;
        if (relatedEntities) {
            relatedEntities.forEach((entity) => {
                Object.keys(entity._attrs).forEach((attrName) => {
                    attrs[attrName] = entity._attrs[attrName];
                });
            });
        }
        if (this._filteringColumns) {
            Object.keys(this._filteringColumns).forEach((attrName) => {
                attrs[attrName] = self._filteringColumns[attrName];
            });
        }

        return attrs;
    }

    modifyByConjuction(fceName, conjuction) {
        if (!fceName) {
            throw new IllegalArgumentException("fceName can not be undefined.");
        }

        if (!conjuction) {
            return fceName;
        }
        if (conjuction == this.consts.AND.rawValue) {
            return fceName;
        }
        if (conjuction == this.consts.OR.rawValue) {
            fceName = `orW${fceName.substr(1)}`;
        }

        return fceName;
    }

    parseRawFilter(rawFilter) {
        if (!rawFilter) {
            return null;
        }

        const whereParser = new WhereParser();
        return whereParser.parse(rawFilter);
    }

    async parseRawFilterAndEvaluate(rawFilter) {
        if (!rawFilter) {
            return true;
        }

        const whereParser = new WhereParser();
        const parsed = whereParser.parse(rawFilter);
        const evalmath = new EvalMath(this.req);

        const recursion = async (filterPart) => {
            for (const item of filterPart) {
                if (Array.isArray(item)) {
                    await recursion(item);
                } else {
                    const value = await evalmath.evaluate(item.value);
                    item.value = value;
                }
            }
        };

        if (parsed && Array.isArray(parsed) && parsed.length > 0) {
            await recursion(parsed);

            return parsed;
        } else {
            return false;
        }
    }

    /**
     * Returns transformed condition (=, <, >, ..)
     *
     * Simple condition is any condition where you can use
     * select * from table where COLUMN_NAME <operator> VLAUE
     * so it's <eq>, <nq>, <lt>, <gt>, ...
     * so it's not <isnn>, <like>, <in>
     *
     * @param condition
     */
    getSimpleCondition(condition) {
        if (!condition) {
            return condition;
        }
        if (condition.match(/<eq>/)) {
            return "=";
        }
        if (condition.match(/<ne>/)) {
            return "<>";
        }
        if (condition.match(/<gt>/)) {
            return ">";
        }
        if (condition.match(/<lt>/)) {
            return "<";
        }
        if (condition.match(/<ge>/)) {
            return ">=";
        }
        if (condition.match(/<le>/)) {
            return "<=";
        }
        return null;
    }

    /**
     * Returns transformed condition (like or not like)
     *
     * Like condition is like or not like condition
     * select * from table where COLUMN_NAME <like> VLAUE
     * so it's <like>, <nlike>
     * so it's not <eq>, <isn>, <in>
     *
     * @param condition
     */
    getLikeCondition(condition) {
        if (!condition) {
            return condition;
        }
        if (condition.match(/<like>/)) {
            return "like";
        }
        if (condition.match(/<nlike>/)) {
            return "not like";
        }

        return null;
    }

    /**
     * Returns transformed condition (is null or is not null)
     *
     * Null condition is is null or is not null condition
     * select * from table where COLUMN_NAME <isn> VLAUE
     * so it's <isn>, <isnn>
     * so it's not <eq>, <like>, <in>
     *
     * @param condition
     */
    getNullCondition(condition) {
        if (!condition) {
            return condition;
        }
        if (condition.match(/<isn>/)) {
            return "is null";
        }
        if (condition.match(/<isnn>/)) {
            return "is not null";
        }

        return null;
    }

    set filteringColumns(filteringColumns) {
        this._filteringColumns = filteringColumns;
    }

    get filteringColumns() {
        return this._filteringColumns;
    }
}
