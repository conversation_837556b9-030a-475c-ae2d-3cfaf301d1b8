// @ts-nocheck
// @ts-nocheck
import { IllegalArgumentException } from "../../utils/errorHandling/exceptions/illegalArgumentException";

export class Order {
    constructor(repository) {
        this.repository = repository;
    }

    ini(promise) {
        this._aliases = this.getAliases(promise);
        this._attrs = this.getAttrs();
    }

    getColName(key) {
        const index = key.lastIndexOf(".");
        if (index == -1) {
            return key;
        }
        return key.substr(index + 1);
    }

    addQuotes(key) {
        const index = key.lastIndexOf(".");
        if (
            index != -1 &&
            index == key.indexOf(".") &&
            key.indexOf("|") == -1
        ) {
            let newKey = key.substr(0, index);
            const colNew = key.substr(index + 1);
            if (!newKey.startsWith('"')) {
                newKey = `"${newKey}"`;
                key = `${newKey}.${colNew}`;
            }
        }

        return key;
    }

    sanitizeValue(value, key) {
        key = key.toUpperCase();

        // Allow filter by "ID"
        if (key == "ID") {
            key = this.repository.entity.primaryColumn;
        }
        // Sanitize name space and column name
        const colName = this.getColName(key);
        if (this._attrs && !this._attrs[colName]) {
            this._attrs[colName] = {
                type: "string",
                key: colName,
            };
        }

        const attr = this._attrs[colName];

        if (attr.value) {
            value = attr.value;
        }

        return value;
    }

    sanitizeKey(key: string): string {
        const self = this;
        key = key.toUpperCase();

        // Allow filter by "ID"
        if (key == "ID") {
            key = this.repository.entity.primaryColumn;
        }
        const colName = this.getColName(key);

        const addAlias = function (k) {
            if (!k) {
                return k;
            }
            k = k.toUpperCase();

            const index = k.lastIndexOf(".");
            if (index == -1) {
                if (self._aliases[k]) {
                    return `${self._aliases[k]}.${k}`;
                }
            }
            return k;
        };
        key = addAlias(key);

        // Filtered column must be in Entity
        if (this._attrs && !this._attrs[colName] && !this._attrs[key]) {
            throw new IllegalArgumentException(`Unknown column name [${key}].`);
        }

        // Override virtual column name by sql e.g. user_full_name = USER_LAST_NAME || ' ' || USER_FIRST_NAME
        const attr = this._attrs[key] || this._attrs[colName];
        if (attr.key) {
            key = attr.key;
        }

        return key;
    }

    getAliases(promise) {
        if (!promise._statements) {
            return;
        }

        const aliases = {};
        promise._statements.forEach((statemant) => {
            if (
                statemant.grouping == "columns" &&
                statemant.value &&
                Array.isArray(statemant.value)
            ) {
                statemant.value.forEach((alias) => {
                    if (typeof alias === "string") {
                        const index = alias.indexOf(".");
                        if (index !== -1) {
                            const ns = alias.substr(0, index);
                            const columnName = alias.substr(index + 1);

                            aliases[columnName] = ns;
                        }
                    }
                });
            }
        });

        return aliases;
    }

    getAttrs() {
        const self = this;
        const attrs = this.repository.entity._attrs;
        const { relatedEntities } = this.repository;
        if (relatedEntities) {
            relatedEntities.forEach((entity) => {
                Object.keys(entity._attrs).forEach((attrName) => {
                    attrs[attrName] = entity._attrs[attrName];
                });
            });
        }

        if (this._orderingColumns) {
            Object.keys(this._orderingColumns).forEach((attrName) => {
                attrs[attrName] = self._orderingColumns[attrName];
            });
        }

        return attrs;
    }

    set orderingColumns(orderingColumns) {
        this._orderingColumns = orderingColumns;
    }

    get orderingColumns() {
        return this._orderingColumns;
    }
}
