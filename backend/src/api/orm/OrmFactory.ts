import _ from "lodash";
import { Knex } from "knex";

import { BaseCollection } from "./BaseCollection";
import { mssql_DatabaseUtilities } from "./database/mssql_DatabaseUtilities";
import { AbstractDatabaseUtilities } from "./database/AbstractDatabaseUtilities";
import { AdditionalObjectInfoRepository } from "./repository/AdditionalObjectInfoRepository";
import { AuthorizationRepository } from "./repository/AuthorizationRepository";
import { CaseStatusRepository } from "./repository/CaseStatusRepository";
import { CertificateRepository } from "./repository/CertificateRepository";
import { ClobRepository } from "./repository/ClobRepository";
import { CompetenceRepository } from "./repository/CompetenceRepository";
import { CompetenceRoleRegexRepository } from "./repository/CompetenceRoleRegexRepository";
import { CompetenceRoleRepository } from "./repository/CompetenceRoleRepository";
import { CompetenceRuleRepository } from "./repository/CompetenceRuleRepository";
import { CompetenceRuleRoleRegexRepository } from "./repository/CompetenceRuleRoleRegexRepository";
import { CompetenceRuleRoleRepository } from "./repository/CompetenceRuleRoleRepository";
import { CompetenceUserRepository } from "./repository/CompetenceUserRepository";
import { ConfigRepository } from "./repository/ConfigRepository";
import { CronRepository } from "./repository/CronRepository";
import { CronRunRepository } from "./repository/CronRunRepository";
import { CustomViewColumnRepository } from "./repository/CustomViewColumnRepository";
import { CustomViewMailRepository } from "./repository/CustomViewMailRepository";
import { CustomViewRepository } from "./repository/CustomViewRepository";
import { CustomViewShareRepository } from "./repository/CustomViewShareRepository";
import { DmsFileAccessLogRepository } from "./repository/DmsFileAccessLogRepository";
import { DmsFileRepository } from "./repository/DmsFileRepository";
import { DmsFileTagRepository } from "./repository/DmsFileTagRepository";
import { DmsFolderRepository } from "./repository/DmsFolderRepository";
import { DmsTagRepository } from "./repository/DmsTagRepository";
import { DmsTagTagRepository } from "./repository/DmsTagTagRepository";
import { DmsViewColRepository } from "./repository/DmsViewColRepository";
import { DmsViewRepository } from "./repository/DmsViewRepository";
import { DynamicListRepository } from "./repository/DynamicListRepository";
import { DynamicTableColRepository } from "./repository/DynamicTableColRepository";
import { DynamicTableRepository } from "./repository/DynamicTableRepository";
import { DynamicTableValueRepository } from "./repository/DynamicTableValueRepository";
import { EventDefinitionRepository } from "./repository/EventDefinitionRepository";
import { EventParamRepository } from "./repository/EventParamRepository";
import { EventRepository } from "./repository/EventRepository";
import { ExternalRightRepository } from "./repository/ExternalRightRepository";
import { GuidesRepository } from "./repository/GuidesRepository";
import { HeaderOrgstrRepository } from "./repository/HeaderOrgstrRepository";
import { HeaderRepository } from "./repository/HeaderRepository";
import { HeaderRoleRepository } from "./repository/HeaderRoleRepository";
import { HealthStatusParamRepository } from "./repository/HealthStatusParamRepository";
import { InstanceGraphRepository } from "./repository/InstanceGraphRepository";
import { InstanceLinkConditionRepository } from "./repository/InstanceLinkConditionRepository";
import { InstanceProcessHistoryRepository } from "./repository/InstanceProcessHistoryRepository";
import { InstanceProcessNoteRepository } from "./repository/InstanceProcessNoteRepository";
import { InstanceProcessStaticRightRepository } from "./repository/InstanceProcessStaticRightRepository";
import { InstanceProcessVersionRepository } from "./repository/InstanceProcessVersionRepository";
import { InstanceTaskCompletitionRepository } from "./repository/InstanceTaskCompletitionRepository";
import { InstanceTaskEmailNotifsRepository } from "./repository/InstanceTaskEmailNotifsRepository";
import { InstanceTaskHistoryRepository } from "./repository/InstanceTaskHistoryRepository";
import { InstanceTaskInvitationRepository } from "./repository/InstanceTaskInvitationRepository";
import { InstanceTaskJSCalculationRepository } from "./repository/InstanceTaskJSCalculationRepository";
import { InstanceTaskLinkDoneRepository } from "./repository/InstanceTaskLinkDoneRepository";
import { InstanceTaskLinkRepository } from "./repository/InstanceTaskLinkRepository";
import { InstanceTaskRepository } from "./repository/InstanceTaskRepository";
import { InstanceVariableHistoryRepository } from "./repository/InstanceVariableHistoryRepository";
import { InstanceVariableSequenceRepository } from "./repository/InstanceVariableSequenceRepository";
import { JsScriptRepository } from "./repository/JsScriptRepository";
import { MailQRepository } from "./repository/MailQRepository";
import { MigrationStepHistoryRepository } from "./repository/MigrationStepHistoryRepository";
import { MockMultiRepository } from "./repository/MockMultiRepository";
import { MockRepository } from "./repository/MockRepository";
import { MockTaskRepository } from "./repository/MockTaskRepository";
import { MockUserRepository } from "./repository/MockUserRepository";
import { mssql_CustomViewRepository } from "./repository/mssql_CustomViewRepository";
import { mssql_DynamicTableRepository } from "./repository/mssql_DynamicTableRepository";
import { mssql_ExternalRightRepository } from "./repository/mssql_ExternalRightRepository";
import { mssql_OrganizationStructureRepository } from "./repository/mssql_OrganizationStructureRepository";
import { mssql_ProcessRepository } from "./repository/mssql_ProcessRepository";
import { mssql_SequenceRepository } from "./repository/mssql_SequenceRepository";
import { mssql_UsageStatisticsRepository } from "./repository/mssql_UsageStatisticsRepository";
import { mssql_UserRepository } from "./repository/mssql_UserRepository";
import { mssql_UserRoleRepository } from "./repository/mssql_UserRoleRepository";
import { mssql_VariableLovRepository } from "./repository/mssql_VariableLovRepository";
import { mssql_VariableRepository } from "./repository/mssql_VariableRepository";
import { OrganizationAuthRepository } from "./repository/OrganizationAuthRepository";
import { OrganizationRepository } from "./repository/OrganizationRepository";
import { OrganizationStructureRepository } from "./repository/OrganizationStructureRepository";
import { PerformanceLogsRepository } from "./repository/PerformanceLogsRepository";
import { PlanProcessLogRepository } from "./repository/PlanProcessLogRepository";
import { PlanProcessRepository } from "./repository/PlanProcessRepository";
import { PlanUserRepository } from "./repository/PlanUserRepository";
import { PlztCfProjectRepository } from "./repository/PlztCfProjectRepository";
import { ProcessRepository } from "./repository/ProcessRepository";
import { RegisteredMobileDeviceRepository } from "./repository/RegisteredMobileDeviceRepository";
import { ReportGraphGlobalFilterDefRepository } from "./repository/ReportGraphGlobalFilterDefRepository";
import { ReportGraphGlobalFilterOptionRepository } from "./repository/ReportGraphGlobalFilterOptionRepository";
import { ReportGraphGlobalFilterRepository } from "./repository/ReportGraphGlobalFilterRepository";
import { ReportGraphRepository } from "./repository/ReportGraphRepository";
import { ReportGraphShareRepository } from "./repository/ReportGraphShareRepository";
import { ReportGraphsPointsRepository } from "./repository/ReportGraphsPointsRepository";
import { RoleRepository } from "./repository/RoleRepository";
import { RuleDefinitionParamRepository } from "./repository/RuleDefinitionParamRepository";
import { RuleDefinitionRepository } from "./repository/RuleDefinitionRepository";
import { RuleDefinitionVariableRepository } from "./repository/RuleDefinitionVariableRepository";
import { RuleRepository } from "./repository/RuleRepository";
import { RuleVariableRepository } from "./repository/RuleVariableRepository";
import { SequenceRepository } from "./repository/SequenceRepository";
import { TaskLinkStatusRepository } from "./repository/TaskLinkStatusRepository";
import { TaskRepository } from "./repository/TaskRepository";
import { TemplateGraphRepository } from "./repository/TemplateGraphRepository";
import { TemplateLinkConditionRepository } from "./repository/TemplateLinkConditionRepository";
import { TemplatePrintRepository } from "./repository/TemplatePrintRepository";
import { TemplateProcessRepository } from "./repository/TemplateProcessRepository";
import { TemplateProcessShreddingRepository } from "./repository/TemplateProcessShreddingRepository";
import { TemplateProcessVersionRepository } from "./repository/TemplateProcessVersionRepository";
import { TemplateSectionsRepository } from "./repository/TemplateSectionsRepository";
import { TemplateTaskCalculationRepository } from "./repository/TemplateTaskCalculationRepository";
import { TemplateTaskCompletionRepository } from "./repository/TemplateTaskCompletionRepository";
import { TemplateTaskCompletitionRepository } from "./repository/TemplateTaskCompletitionRepository";
import { TemplateTaskEmailNotifsRepository } from "./repository/TemplateTaskEmailNotifsRepository";
import { TemplateTaskInvitationRepository } from "./repository/TemplateTaskInvitationRepository";
import { TemplateTaskJSCalculationRepository } from "./repository/TemplateTaskJSCalculationRepository";
import { TemplateTaskLinkRepository } from "./repository/TemplateTaskLinkRepository";
import { TemplateTaskMassUsageRepository } from "./repository/TemplateTaskMassUsageRepository";
import { TemplateTaskRepository } from "./repository/TemplateTaskRepository";
import { TemplateTaskScriptRepository } from "./repository/TemplateTaskScriptRepository";
import { TemplateTaskVarUsageRepository } from "./repository/TemplateTaskVarUsageRepository";
import { TemplateVariableLovRepository } from "./repository/TemplateVariableLovRepository";
import { TemplateVariableRepository } from "./repository/TemplateVariableRepository";
import { UsageStatisticsRepository } from "./repository/UsageStatisticsRepository";
import { UserOrganizationStructureRepository } from "./repository/UserOrganizationStructureRepository";
import { UserParameterRepository } from "./repository/UserParameterRepository";
import { UserPhotoRepository } from "./repository/UserPhotoRepository";
import { UserRepository } from "./repository/UserRepository";
import { UserRoleRepository } from "./repository/UserRoleRepository";
import { UserViceOrgRestrictionRepository } from "./repository/UserViceOrgRestrictionRepository";
import { UserViceRepository } from "./repository/UserViceRepository";
import { VariableLovRepository } from "./repository/VariableLovRepository";
import { VariableRepository } from "./repository/VariableRepository";
import { ArchivedVariableRepository } from "./repository/ArchivedVariableRepository";
import { VariableSnapRepository } from "./repository/VariableSnapRepository";
import { XmlProcessImportRepository } from "./repository/XmlProcessImportRepository";
import { mssql_WfTaskRepository } from "./repository/mssql_WfTaskRepository";
import { WfLinkRepository } from "./repository/WfLinkRepository";
import { WfProcessRepository } from "./repository/WfProcessRepository";
import { WfTaskRepository } from "./repository/WfTaskRepository";
import { BaseRepository } from "./repository/BaseRepository";
import { mssql_InstanceVariableSequenceRepository } from "./repository/mssql_InstanceVariableSequenceRepository";
import { BaseEntity } from "./entity/BaseEntity";
import { DynamicTableValueCollection } from "./collection/DynamicTableValueCollection";
import { UserCollection } from "./collection/UserCollection";
import { ArchivedProcessRepository } from "./repository/ArchivedProcessRepository";
import { InstanceProcessDynRightsRepository } from "./repository/InstanceProcessDynRightsRepository";
import { TemplateProcessArchivationRepository } from "./repository/TemplateProcessArchivationRepository";
import { CompetenceRuleUserRepository } from "./repository/CompetenceRuleUserRepository";
import { ArchivedVariableLovRepository } from "./repository/ArchivedVariableLovRepository";
import { ArchivedInstanceTaskRepository } from "./repository/ArchivedInstanceTaskRepository";
import { ArchivedInstanceProcessNoteRepository } from "./repository/ArchivedInstanceProcessNoteRepository";
import { ArchivedInstanceProcessVersionRepository } from "./repository/ArchivedInstanceProcessVersionRepository";
import { ArchivedDmsFileAccessLogRepository } from "./repository/ArchivedDmsFileAccessLogRepository";
import { ArchivedDmsFileRepository } from "./repository/ArchivedDmsFileRepository";
import { ArchivedDmsFileTagRepository } from "./repository/ArchivedDmsFileTagRepository";
import { ArchivedInstanceProcessHistoryRepository } from "./repository/ArchivedInstanceProcessHistoryRepository";
import { mssql_ArchivedProcessRepository } from "./repository/mssql_ArchivedProcessRepository";
import { ArchivedInstanceTaskHistoryRepository } from "./repository/ArchivedInstanceTaskHistoryRepository";
import { ArchivedInstanceVariableHistoryRepository } from "./repository/ArchivedInstanceVariableHistoryRepository";
import { PostgresqlDatabaseUtilities } from "./database/postgresqlDatabaseUtilities";
import { PostRepository } from "./repository/PostRepository";
import { PostTagRepository } from "./repository/PostTagRepository";

export interface Repositories {
    additionalObjectInfo: typeof AdditionalObjectInfoRepository;
    archivedDmsFileAccessLog: typeof ArchivedDmsFileAccessLogRepository;
    archivedDmsFile: typeof ArchivedDmsFileRepository;
    archivedDmsFileTag: typeof ArchivedDmsFileTagRepository;
    archivedInstanceProcessHistory: typeof ArchivedInstanceProcessHistoryRepository;
    archivedInstanceProcessNote: typeof ArchivedInstanceProcessNoteRepository;
    archivedInstanceProcessVersion: typeof ArchivedInstanceProcessVersionRepository;
    archivedInstanceTask: typeof ArchivedInstanceTaskRepository;
    archivedInstanceTaskHistory: typeof ArchivedInstanceTaskHistoryRepository;
    archivedInstanceVariableHistory: typeof ArchivedInstanceVariableHistoryRepository;
    archivedProcess: typeof ArchivedProcessRepository;
    archivedVariable: typeof ArchivedVariableRepository;
    archivedVariableLov: typeof ArchivedVariableLovRepository;
    authorization: typeof AuthorizationRepository;
    caseStatus: typeof CaseStatusRepository;
    certificate: typeof CertificateRepository;
    clob: typeof ClobRepository;
    competence: typeof CompetenceRepository;
    competenceRoleRegex: typeof CompetenceRoleRegexRepository;
    competenceRole: typeof CompetenceRoleRepository;
    competenceRule: typeof CompetenceRuleRepository;
    competenceRuleRoleRegex: typeof CompetenceRuleRoleRegexRepository;
    competenceRuleRole: typeof CompetenceRuleRoleRepository;
    competenceUser: typeof CompetenceUserRepository;
    competenceRuleUser: typeof CompetenceRuleUserRepository;
    config: typeof ConfigRepository;
    cron: typeof CronRepository;
    cronRun: typeof CronRunRepository;
    customViewColumn: typeof CustomViewColumnRepository;
    customViewMail: typeof CustomViewMailRepository;
    customView: typeof CustomViewRepository;
    customViewShare: typeof CustomViewShareRepository;
    dmsFileAccessLog: typeof DmsFileAccessLogRepository;
    dmsFile: typeof DmsFileRepository;
    dmsFileTag: typeof DmsFileTagRepository;
    dmsFolder: typeof DmsFolderRepository;
    dmsTag: typeof DmsTagRepository;
    dmsTagTag: typeof DmsTagTagRepository;
    dmsViewCol: typeof DmsViewColRepository;
    dmsView: typeof DmsViewRepository;
    dynamicList: typeof DynamicListRepository;
    dynamicTableCol: typeof DynamicTableColRepository;
    dynamicTable: typeof DynamicTableRepository;
    dynamicTableValue: typeof DynamicTableValueRepository;
    eventDefinition: typeof EventDefinitionRepository;
    eventParam: typeof EventParamRepository;
    event: typeof EventRepository;
    externalRight: typeof ExternalRightRepository;
    guides: typeof GuidesRepository;
    headerOrgstr: typeof HeaderOrgstrRepository;
    header: typeof HeaderRepository;
    headerRole: typeof HeaderRoleRepository;
    healthStatusParam: typeof HealthStatusParamRepository;
    instanceGraph: typeof InstanceGraphRepository;
    instanceLinkCondition: typeof InstanceLinkConditionRepository;
    instanceProcessDynRights: typeof InstanceProcessDynRightsRepository;
    instanceProcessHistory: typeof InstanceProcessHistoryRepository;
    instanceProcessNote: typeof InstanceProcessNoteRepository;
    instanceProcessStaticRight: typeof InstanceProcessStaticRightRepository;
    instanceProcessVersion: typeof InstanceProcessVersionRepository;
    instanceTaskCompletition: typeof InstanceTaskCompletitionRepository;
    instanceTaskEmailNotifs: typeof InstanceTaskEmailNotifsRepository;
    instanceTaskHistory: typeof InstanceTaskHistoryRepository;
    instanceTaskInvitation: typeof InstanceTaskInvitationRepository;
    instanceTaskJSCalculation: typeof InstanceTaskJSCalculationRepository;
    instanceTaskLinkDone: typeof InstanceTaskLinkDoneRepository;
    instanceTaskLink: typeof InstanceTaskLinkRepository;
    instanceTask: typeof InstanceTaskRepository;
    instanceVariableHistory: typeof InstanceVariableHistoryRepository;
    instanceVariableSequence: typeof InstanceVariableSequenceRepository;
    jsScript: typeof JsScriptRepository;
    mailQ: typeof MailQRepository;
    migrationStepHistory: typeof MigrationStepHistoryRepository;
    mockMulti: typeof MockMultiRepository;
    mock: typeof MockRepository;
    mockTask: typeof MockTaskRepository;
    mockUser: typeof MockUserRepository;
    organizationAuth: typeof OrganizationAuthRepository;
    organization: typeof OrganizationRepository;
    organizationStructure: typeof OrganizationStructureRepository;
    performanceLogs: typeof PerformanceLogsRepository;
    planProcessLog: typeof PlanProcessLogRepository;
    planProcess: typeof PlanProcessRepository;
    planUser: typeof PlanUserRepository;
    plztCfProject: typeof PlztCfProjectRepository;
    process: typeof ProcessRepository;
    registeredMobileDevice: typeof RegisteredMobileDeviceRepository;
    reportGraphGlobalFilterDef: typeof ReportGraphGlobalFilterDefRepository;
    reportGraphGlobalFilterOption: typeof ReportGraphGlobalFilterOptionRepository;
    reportGraphGlobalFilter: typeof ReportGraphGlobalFilterRepository;
    reportGraph: typeof ReportGraphRepository;
    reportGraphShare: typeof ReportGraphShareRepository;
    reportGraphsPoints: typeof ReportGraphsPointsRepository;
    role: typeof RoleRepository;
    ruleDefinitionParam: typeof RuleDefinitionParamRepository;
    ruleDefinition: typeof RuleDefinitionRepository;
    ruleDefinitionVariable: typeof RuleDefinitionVariableRepository;
    rule: typeof RuleRepository;
    ruleVariable: typeof RuleVariableRepository;
    sequence: typeof SequenceRepository;
    taskLinkStatus: typeof TaskLinkStatusRepository;
    task: typeof TaskRepository;
    templateGraph: typeof TemplateGraphRepository;
    templateLinkCondition: typeof TemplateLinkConditionRepository;
    templatePrint: typeof TemplatePrintRepository;
    templateProcess: typeof TemplateProcessRepository;
    templateProcessArchivation: typeof TemplateProcessArchivationRepository;
    templateProcessShredding: typeof TemplateProcessShreddingRepository;
    templateProcessVersion: typeof TemplateProcessVersionRepository;
    templateSections: typeof TemplateSectionsRepository;
    templateTaskCalculation: typeof TemplateTaskCalculationRepository;
    templateTaskCompletion: typeof TemplateTaskCompletionRepository;
    templateTaskCompletition: typeof TemplateTaskCompletitionRepository;
    templateTaskEmailNotifs: typeof TemplateTaskEmailNotifsRepository;
    templateTaskInvitation: typeof TemplateTaskInvitationRepository;
    templateTaskJSCalculation: typeof TemplateTaskJSCalculationRepository;
    templateTaskLink: typeof TemplateTaskLinkRepository;
    templateTaskMassUsage: typeof TemplateTaskMassUsageRepository;
    templateTask: typeof TemplateTaskRepository;
    templateTaskScript: typeof TemplateTaskScriptRepository;
    templateTaskVarUsage: typeof TemplateTaskVarUsageRepository;
    templateVariableLov: typeof TemplateVariableLovRepository;
    templateVariable: typeof TemplateVariableRepository;
    usageStatistics: typeof UsageStatisticsRepository;
    userOrganizationStructure: typeof UserOrganizationStructureRepository;
    userParameter: typeof UserParameterRepository;
    user: typeof UserRepository;
    userPhoto: typeof UserPhotoRepository;
    userRole: typeof UserRoleRepository;
    userViceOrgRestriction: typeof UserViceOrgRestrictionRepository;
    userVice: typeof UserViceRepository;
    variableLov: typeof VariableLovRepository;
    variable: typeof VariableRepository;
    variableSnap: typeof VariableSnapRepository;
    xmlProcessImport: typeof XmlProcessImportRepository;
    mssql_archivedProcess: typeof mssql_ArchivedProcessRepository;
    mssql_customView: typeof mssql_CustomViewRepository;
    mssql_dynamicTable: typeof mssql_DynamicTableRepository;
    mssql_externalRight: typeof mssql_ExternalRightRepository;
    mssql_instanceVariableSequence: typeof mssql_InstanceVariableSequenceRepository;
    mssql_organizationStructure: typeof mssql_OrganizationStructureRepository;
    mssql_process: typeof mssql_ProcessRepository;
    mssql_sequence: typeof mssql_SequenceRepository;
    mssql_usageStatistics: typeof mssql_UsageStatisticsRepository;
    mssql_user: typeof mssql_UserRepository;
    mssql_userRole: typeof mssql_UserRoleRepository;
    mssql_variableLov: typeof mssql_VariableLovRepository;
    mssql_variable: typeof mssql_VariableRepository;
    mssql_wfTask: typeof mssql_WfTaskRepository;
    wfLink: typeof WfLinkRepository;
    wfProcess: typeof WfProcessRepository;
    wfTask: typeof WfTaskRepository;
    post: typeof PostRepository;
    postTag: typeof PostTagRepository;
}

export type RepositoryInstances = {
    [K in keyof Repositories]: InstanceType<Repositories[K]>;
};

export type RepositoryKey = Exclude<keyof RepositoryInstances, number | symbol>;

/**
 * In most cases (single DB) is used as singleton.
 */
export class OrmFactory {
    readonly connection: Knex;

    private readonly connDialect: "postgresql" | "mssql";

    public readonly repositories: Repositories;

    private readonly mssqlDatabaseUtilities: mssql_DatabaseUtilities;

    private readonly postgresqlDatabaseUtilities: PostgresqlDatabaseUtilities;

    private readonly specificCollections: {
        user: typeof UserCollection;
        dynamicTableValue: typeof DynamicTableValueCollection;
    };

    constructor(connection: Knex, connDialect: "postgresql" | "mssql") {
        this.connection = connection;
        this.connDialect = connDialect;
        this.repositories = {
            additionalObjectInfo: AdditionalObjectInfoRepository,
            archivedDmsFileAccessLog: ArchivedDmsFileAccessLogRepository,
            archivedDmsFile: ArchivedDmsFileRepository,
            archivedDmsFileTag: ArchivedDmsFileTagRepository,
            archivedInstanceProcessHistory:
                ArchivedInstanceProcessHistoryRepository,
            archivedInstanceProcessNote: ArchivedInstanceProcessNoteRepository,
            archivedInstanceProcessVersion:
                ArchivedInstanceProcessVersionRepository,
            archivedInstanceTask: ArchivedInstanceTaskRepository,
            archivedInstanceTaskHistory: ArchivedInstanceTaskHistoryRepository,
            archivedInstanceVariableHistory:
                ArchivedInstanceVariableHistoryRepository,
            archivedProcess: ArchivedProcessRepository,
            archivedVariable: ArchivedVariableRepository,
            archivedVariableLov: ArchivedVariableLovRepository,
            authorization: AuthorizationRepository,
            caseStatus: CaseStatusRepository,
            certificate: CertificateRepository,
            clob: ClobRepository,
            competence: CompetenceRepository,
            competenceRoleRegex: CompetenceRoleRegexRepository,
            competenceRole: CompetenceRoleRepository,
            competenceRule: CompetenceRuleRepository,
            competenceRuleRoleRegex: CompetenceRuleRoleRegexRepository,
            competenceRuleRole: CompetenceRuleRoleRepository,
            competenceUser: CompetenceUserRepository,
            competenceRuleUser: CompetenceRuleUserRepository,
            config: ConfigRepository,
            cron: CronRepository,
            cronRun: CronRunRepository,
            customViewColumn: CustomViewColumnRepository,
            customViewMail: CustomViewMailRepository,
            customView: CustomViewRepository,
            customViewShare: CustomViewShareRepository,
            dmsFileAccessLog: DmsFileAccessLogRepository,
            dmsFile: DmsFileRepository,
            dmsFileTag: DmsFileTagRepository,
            dmsFolder: DmsFolderRepository,
            dmsTag: DmsTagRepository,
            dmsTagTag: DmsTagTagRepository,
            dmsViewCol: DmsViewColRepository,
            dmsView: DmsViewRepository,
            dynamicList: DynamicListRepository,
            dynamicTableCol: DynamicTableColRepository,
            dynamicTable: DynamicTableRepository,
            dynamicTableValue: DynamicTableValueRepository,
            eventDefinition: EventDefinitionRepository,
            eventParam: EventParamRepository,
            event: EventRepository,
            externalRight: ExternalRightRepository,
            guides: GuidesRepository,
            headerOrgstr: HeaderOrgstrRepository,
            header: HeaderRepository,
            headerRole: HeaderRoleRepository,
            healthStatusParam: HealthStatusParamRepository,
            instanceGraph: InstanceGraphRepository,
            instanceLinkCondition: InstanceLinkConditionRepository,
            instanceProcessDynRights: InstanceProcessDynRightsRepository,
            instanceProcessHistory: InstanceProcessHistoryRepository,
            instanceProcessNote: InstanceProcessNoteRepository,
            instanceProcessStaticRight: InstanceProcessStaticRightRepository,
            instanceProcessVersion: InstanceProcessVersionRepository,
            instanceTaskCompletition: InstanceTaskCompletitionRepository,
            instanceTaskEmailNotifs: InstanceTaskEmailNotifsRepository,
            instanceTaskHistory: InstanceTaskHistoryRepository,
            instanceTaskInvitation: InstanceTaskInvitationRepository,
            instanceTaskJSCalculation: InstanceTaskJSCalculationRepository,
            instanceTaskLinkDone: InstanceTaskLinkDoneRepository,
            instanceTaskLink: InstanceTaskLinkRepository,
            instanceTask: InstanceTaskRepository,
            instanceVariableHistory: InstanceVariableHistoryRepository,
            instanceVariableSequence: InstanceVariableSequenceRepository,
            jsScript: JsScriptRepository,
            mailQ: MailQRepository,
            migrationStepHistory: MigrationStepHistoryRepository,
            mockMulti: MockMultiRepository,
            mock: MockRepository,
            mockTask: MockTaskRepository,
            mockUser: MockUserRepository,
            organizationAuth: OrganizationAuthRepository,
            organization: OrganizationRepository,
            organizationStructure: OrganizationStructureRepository,
            performanceLogs: PerformanceLogsRepository,
            planProcessLog: PlanProcessLogRepository,
            planProcess: PlanProcessRepository,
            planUser: PlanUserRepository,
            plztCfProject: PlztCfProjectRepository,
            process: ProcessRepository,
            registeredMobileDevice: RegisteredMobileDeviceRepository,
            reportGraphGlobalFilterDef: ReportGraphGlobalFilterDefRepository,
            reportGraphGlobalFilterOption:
                ReportGraphGlobalFilterOptionRepository,
            reportGraphGlobalFilter: ReportGraphGlobalFilterRepository,
            reportGraph: ReportGraphRepository,
            reportGraphShare: ReportGraphShareRepository,
            reportGraphsPoints: ReportGraphsPointsRepository,
            role: RoleRepository,
            ruleDefinitionParam: RuleDefinitionParamRepository,
            ruleDefinition: RuleDefinitionRepository,
            ruleDefinitionVariable: RuleDefinitionVariableRepository,
            rule: RuleRepository,
            ruleVariable: RuleVariableRepository,
            sequence: SequenceRepository,
            taskLinkStatus: TaskLinkStatusRepository,
            task: TaskRepository,
            templateGraph: TemplateGraphRepository,
            templateLinkCondition: TemplateLinkConditionRepository,
            templatePrint: TemplatePrintRepository,
            templateProcess: TemplateProcessRepository,
            templateProcessArchivation: TemplateProcessArchivationRepository,
            templateProcessShredding: TemplateProcessShreddingRepository,
            templateProcessVersion: TemplateProcessVersionRepository,
            templateSections: TemplateSectionsRepository,
            templateTaskCalculation: TemplateTaskCalculationRepository,
            templateTaskCompletion: TemplateTaskCompletionRepository,
            templateTaskCompletition: TemplateTaskCompletitionRepository,
            templateTaskEmailNotifs: TemplateTaskEmailNotifsRepository,
            templateTaskInvitation: TemplateTaskInvitationRepository,
            templateTaskJSCalculation: TemplateTaskJSCalculationRepository,
            templateTaskLink: TemplateTaskLinkRepository,
            templateTaskMassUsage: TemplateTaskMassUsageRepository,
            templateTask: TemplateTaskRepository,
            templateTaskScript: TemplateTaskScriptRepository,
            templateTaskVarUsage: TemplateTaskVarUsageRepository,
            templateVariableLov: TemplateVariableLovRepository,
            templateVariable: TemplateVariableRepository,
            usageStatistics: UsageStatisticsRepository,
            userOrganizationStructure: UserOrganizationStructureRepository,
            userParameter: UserParameterRepository,
            user: UserRepository,
            userPhoto: UserPhotoRepository,
            userRole: UserRoleRepository,
            userViceOrgRestriction: UserViceOrgRestrictionRepository,
            userVice: UserViceRepository,
            variableLov: VariableLovRepository,
            variable: VariableRepository,
            variableSnap: VariableSnapRepository,
            xmlProcessImport: XmlProcessImportRepository,
            mssql_archivedProcess: mssql_ArchivedProcessRepository,
            mssql_customView: mssql_CustomViewRepository,
            mssql_dynamicTable: mssql_DynamicTableRepository,
            mssql_externalRight: mssql_ExternalRightRepository,
            mssql_instanceVariableSequence:
                mssql_InstanceVariableSequenceRepository,
            mssql_organizationStructure: mssql_OrganizationStructureRepository,
            mssql_process: mssql_ProcessRepository,
            mssql_sequence: mssql_SequenceRepository,
            mssql_usageStatistics: mssql_UsageStatisticsRepository,
            mssql_user: mssql_UserRepository,
            mssql_userRole: mssql_UserRoleRepository,
            mssql_variableLov: mssql_VariableLovRepository,
            mssql_variable: mssql_VariableRepository,
            mssql_wfTask: mssql_WfTaskRepository,
            wfLink: WfLinkRepository,
            wfProcess: WfProcessRepository,
            wfTask: WfTaskRepository,
            post: PostRepository,
            postTag: PostTagRepository,
        };
        this.mssqlDatabaseUtilities = new mssql_DatabaseUtilities();
        this.postgresqlDatabaseUtilities = new PostgresqlDatabaseUtilities();
        this.specificCollections = {
            user: UserCollection,
            dynamicTableValue: DynamicTableValueCollection,
        };
    }

    databaseUtilities(): AbstractDatabaseUtilities {
        if (this.connDialect === "mssql") {
            return this.mssqlDatabaseUtilities;
        } else if (this.connDialect === "postgresql") {
            return this.postgresqlDatabaseUtilities;
        } else {
            throw new Error("Unknown connDialect");
        }
    }

    repo<Key extends RepositoryKey>(
        entityName: Key,
        connection?: Knex,
    ): RepositoryInstances[Key] {
        // entityName can also be a path, i.e. "wfLinkRepository"
        const connDialect: "mssql_" | "postgresql_" = `${this.connDialect}_`;
        // @ts-expect-error workaround because of lower case of first letter
        const entityNameCamelCase: Key = (
            entityName.charAt(0).toLowerCase() + entityName.slice(1)
        ).trim();

        let repo;
        try {
            // @ts-expect-error Impossible to type because of connDialect
            repo = new this.repositories[
                `${connDialect}${entityNameCamelCase}`
            ]();
        } catch (_err) {
            repo = new this.repositories[entityNameCamelCase]();
        }

        repo.setConnection(connection || this.connection);
        repo.orm = this;
        return repo;
    }

    collection<_Key extends RepositoryKey>(
        entityName: "user" | UserRepository,
        connection?: Knex.QueryBuilder,
    ): UserCollection;
    collection<_Key extends RepositoryKey>(
        entityName: "dynamicTableValue" | DynamicTableValueRepository,
        connection?: Knex.QueryBuilder,
    ): DynamicTableValueCollection;
    collection<Key extends RepositoryKey>(
        entityName: Key | RepositoryInstances[Key],
        connection?: Knex.QueryBuilder,
    ): BaseCollection<RepositoryInstances[Key]["entity"]>;
    collection<Key extends RepositoryKey>(
        entityName: Key | RepositoryInstances[Key],
        connection?: Knex.QueryBuilder,
    ):
        | BaseCollection<RepositoryInstances[Key]["entity"]>
        | UserCollection
        | DynamicTableValueCollection {
        let repo: RepositoryInstances[Key];
        if (typeof entityName === "string") {
            repo = this.repo(entityName);
        } else {
            repo = entityName;
        }

        if (repo.entityName.toLowerCase() === "user") {
            return new this.specificCollections.user(
                repo as UserRepository,
                connection || this.connection.select(),
            );
        } else if (repo.entityName.toLowerCase() === "dynamictablevalue") {
            return new this.specificCollections.dynamicTableValue(
                repo as DynamicTableValueRepository,
                connection || this.connection.select(),
            );
        }

        return new BaseCollection<any>(
            // @ts-expect-error types
            repo,
            connection || this.connection.select(),
        ); // select() starts new query instance
    }

    async listEntities(): Promise<BaseEntity[]> {
        const entities: BaseEntity[] = [];
        for (const repo of this.listRepositories()) {
            entities.push(repo.entityClass());
        }
        return entities;
    }

    listRepositories(): BaseRepository<any>[] {
        const repositories: BaseRepository<any>[] = [];
        for (const repo in this.repositories) {
            // @ts-expect-error const repo has string type implicitly
            repositories.push(new this.repositories[repo]());
        }
        return repositories;
    }

    /**
     * Use this function when you want to select ALL columns above one or more JOIN operations
     * If you don't use this function, MS SQL will duplicate the identical column values!
     *
     * ---------WRONG------------
     *
     * return this.connection
     *      .select()
     *      .from('USERS as U')
     *      .leftJoin('USER_PARAMETERS as UP', 'U.USER_ID', 'UP.USER_ID');
     *
     * ---------CORRECT----------
     *
     * const columns = globalThis.orm.getUniqueColumns([
     *      { alias: 'U', name: 'User' },
     *      { alias: 'UP', name: 'UserParameter}',
     * ]);
     *
     * return this.connection
     *      .select(columns)
     *      .from('USERS as U')
     *      .leftJoin('USER_PARAMETERS as UP', 'U.USER_ID', 'UP.USER_ID');
     *
     * --------------------------
     *
     * @param {Array<{ alias: alias, name: repository}>} repositories - Names of repositories to get columns from, MUST be with alias
     * @returns {Array<String>} column names without duplicates
     */
    getUniqueColumns(
        repositories: { alias: string; name: RepositoryKey }[],
    ): string[] {
        let columns: string[] = [];
        let columnsWithoutAlias: string[] = [];
        repositories.forEach((repository) => {
            const repo = this.repo(repository.name);
            const entity = repo.getEntity();
            const columnNames = entity.getColumnNames(
                undefined,
                repository.alias,
            );
            const columnNamesWithoutAlias = entity.getColumnNames();

            columns = columns.concat(columnNames);
            columnsWithoutAlias = columnsWithoutAlias.concat(
                columnNamesWithoutAlias,
            );
        });

        const columnsToRemove: string[] = [];
        const columnsToRemoveIndexes: number[] = [];
        const uniqueColumns = _.uniq(columnsWithoutAlias);
        uniqueColumns.forEach((column) => {
            const duplicateColumnCount = _.filter(
                columnsWithoutAlias,
                (col) => col === column,
            ).length;

            if (duplicateColumnCount > 1) {
                columnsToRemove.push(column);
            }
        });

        columnsWithoutAlias.forEach((column, index) => {
            if (
                columnsToRemove.indexOf(column) > -1 &&
                columnsWithoutAlias.indexOf(column) !== index
            ) {
                columnsToRemoveIndexes.push(index);
            }
        });

        let removedElementsCount = 0;
        columnsToRemoveIndexes.forEach((index) => {
            columns.splice(index - removedElementsCount, 1);
            removedElementsCount += 1;
        });
        return columns;
    }
}
