// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import * as PROCESS from "../entity/const/processConst";
import * as TASK from "../entity/const/taskConst";
import { BaseController } from "../../controllers/BaseController";
import { BaseFacade } from "./BaseFacade";

const queryLimit = 10000;

export class StatisticsFacade extends BaseFacade {
    getFinishedTasksAggregated(req) {
        const knex = this.connection;

        return knex
            .select([
                knex.raw(`count(*) as "TASKS_COUNT" `),
                "USER_ID",
                "USER_NAME",
                "USER_FIRST_NAME",
                "USER_LAST_NAME",
                "USER_FULL_NAME",
                "USER_DISPLAY_NAME",
            ])
            .from(function () {
                StatisticsFacade.getFinishedTasksAggregatedSubselect(
                    this,
                    knex,
                    req,
                );
            })
            .groupBy(
                "USER_ID",
                "USER_NAME",
                "USER_FIRST_NAME",
                "USER_LAST_NAME",
                "USER_FULL_NAME",
                "USER_DISPLAY_NAME",
            )
            .orderBy("TASKS_COUNT", "DESC");
    }

    static getFinishedTasksAggregatedSubselect(connection, knex, req) {
        const reqQuery = req.query;
        const columns = [
            "ITH.ITASKH_FINISHED_BY_USER_ID as USER_ID",
            "ITH.ITASKH_ID",
            "U.USER_NAME",
            "U.USER_FIRST_NAME",
            "U.USER_LAST_NAME",
            knex.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ];

        connection
            .distinct(columns)
            .from("INSTANCE_TASK_HISTORY as ITH")
            .innerJoin(
                "USERS as U",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "U.USER_ID",
            )
            .leftJoin("INSTANCE_TASKS as IT", "ITH.ITASK_ID", "IT.ITASK_ID")
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "ITH.IPROC_ID")
            .innerJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .innerJoin(
                "USER_ROLES as UR",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "UR.USER_ID",
            )
            .innerJoin("ROLES as R", "UR.ROLE_ID", "R.ROLE_ID")
            .innerJoin("TEMPLATE_PROCESSES as TP", "IP.TPROC_ID", "TP.TPROC_ID")
            .innerJoin(
                "USER_ORGANIZATION_STRUCTURE as UOS",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "UOS.USER_ID",
            )
            .innerJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "UOS.ORGSTR_ID",
                "OS.ORGSTR_ID",
            )
            .where("ITH.ITASKH_NOTE", "Task finished")
            .whereNotIn("IP.IPROC_STATUS", [
                PROCESS.STATUS_ERASED,
                PROCESS.STATUS_SUSPEND,
            ])
            .where("IT.ITASK_TYPE", TASK.TYPE_STANDARD)
            .as("ignored_alias");

        if (_.has(reqQuery, "afterDueDate")) {
            // tasks finished after due date
            connection.whereRaw(
                `"ITH"."ITASKH_DUE_DATE_FINISH" < "ITH"."ITASKH_ACTUAL_DATE_FINISH"`,
            );
        }

        const collection = globalThis.orm.collection(
            "InstanceTaskHistory",
            connection,
        );

        collection.filteringColumns = {
            ROLE_ID: { type: "string", key: "UR.ROLE_ID" },
            ROLE_NAME: { type: "string", key: "R.ROLE_NAME" },
            ORGSTR_ID: { type: "string", key: "UOS.ORGSTR_ID" },
            ORGSTR_NAME: { type: "string", key: "OS.ORGSTR_NAME" },
            TPROC_ID: { type: "string", key: "IP.TPROC_ID" },
            TPROC_NAME: { type: "string", key: "TP.TPROC_NAME" },
            TTASK_ID: { type: "string", key: "TT.TTASK_ID" },
            TTASK_NAME: { type: "string", key: "TT.TTASK_NAME" },
            IPROC_ID: { type: "string", key: "IP.IPROC_ID" },
            IPROC_NAME: { type: "string", key: "IP.IPROC_NAME" },
            ITASK_NAME: { type: "string", key: "IT.ITASK_NAME" },
            HEADER_ID: { type: "string", key: "TH.HEADER_ID" },
            HEADER_NAME: { type: "string", key: "TH.HEADER_NAME" },
            USER_ID: { type: "string", key: "ITH.ITASKH_FINISHED_BY_USER_ID" },
            USER_FULL_NAME: {
                type: "string",
                key: `U.USER_LAST_NAME ${globalThis.orm.db.concatColumns()} U.USER_FIRST_NAME`,
            },
            USER_DISPLAY_NAME: { type: "string", key: "U.USER_DISPLAY_NAME" },
            USER_NAME: { type: "string", key: "U.USER_NAME" },
            USER_FIRST_NAME: { type: "string", key: "U.USER_FIRST_NAME" },
            USER_LAST_NAME: { type: "string", key: "U.USER_LAST_NAME" },
        };

        collection.filter(req.query.filter);

        return collection.knex;
    }

    async getFinishedTasks(req, res) {
        const knex = this.connection;
        const user = await globalThis.container.service.auth.getUserData(req);
        const repo = globalThis.orm.repo("organizationStructure", knex);

        return await repo
            .getManagedUsers(user.USER_ID)
            .then((managedUsers) =>
                knex
                    .select([
                        "ITASKH_ID",
                        "IPROC_ID",
                        "ITASK_ID",
                        "ITASK_NAME",
                        "IPROC_NAME",
                        "HEADER_ID",
                        "HEADER_NAME",
                        "SOLVER_USER_ID",
                        "FINISHED_BY_USER_ID",
                        "FINISHED_BY_USER_FULL_NAME",
                        "FINISHED_BY_USER_NAME",
                        "ITASKH_ACTUAL_DATE_START",
                        "ITASKH_ACTUAL_DATE_FINISH",
                        "ITASKH_DUE_DATE_START",
                        "ITASKH_DUE_DATE_FINISH",
                    ])
                    .from(function () {
                        StatisticsFacade.getFinishedTasksSubselect(
                            this,
                            knex,
                            req,
                            managedUsers,
                            user,
                        );
                    }),
            )
            .catch((err) => res.status((err as any).status || 500).send(err));
    }

    static getFinishedTasksSubselect(
        connection,
        knex,
        req,
        managedUsers,
        user,
    ) {
        req.query.limit = req.query.limit || queryLimit;
        const reqQuery = req.query;
        const assesmentManagerCondition = { conds: [] };
        const procOwnerManagerCondition = { conds: [] };
        const solverManagerCondition = { conds: [] };

        // HR manager and Administrator can see all finished tasks
        if (!user.isHRManager() && !user.isAdministrator()) {
            const chunked = _.chunk(managedUsers, 999); // Prevent: ORA-01795: maximum number of expressions in a list is 1000

            chunked.forEach((ids) => {
                assesmentManagerCondition.conds.push(
                    `"IT"."ITASK_ASSESMENT_USER_ID" in (${ids.join(", ")})`,
                );
                procOwnerManagerCondition.conds.push(
                    `"IP"."IPROC_INST_OWNER_USER_ID" in (${ids.join(", ")})`,
                );
                solverManagerCondition.conds.push(
                    `"IT"."ITASK_USER_ID" in (${ids.join(", ")})`,
                );
            });

            assesmentManagerCondition.sql =
                assesmentManagerCondition.conds.length > 0
                    ? assesmentManagerCondition.conds.join(" or ")
                    : " 1 = 0 "; // supervizor ukolu
            procOwnerManagerCondition.sql =
                procOwnerManagerCondition.conds.length > 0
                    ? procOwnerManagerCondition.conds.join(" or ")
                    : " 1 = 0 "; // vlastnik pripadu
            solverManagerCondition.sql =
                solverManagerCondition.conds.length > 0
                    ? solverManagerCondition.conds.join(" or ")
                    : " 1 = 0 "; // resitel
        }

        const columns = [
            "ITH.ITASKH_ID",
            "ITH.ITASKH_ACTUAL_DATE_START",
            "ITH.ITASKH_ACTUAL_DATE_FINISH",
            "ITH.ITASKH_DUE_DATE_START",
            "ITH.ITASKH_DUE_DATE_FINISH",
            "ITU.USER_ID as SOLVER_USER_ID",
            "ITFBU.USER_ID as FINISHED_BY_USER_ID",
            "ITU.USER_DISPLAY_NAME as SOLVER_USER_FULL_NAME",
            "ITU.USER_DISPLAY_NAME as SOLVER_USER_DISPLAY_NAME",
            knex.raw(
                `"ITFBU"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "ITFBU"."USER_FIRST_NAME" as "FINISHED_BY_USER_FULL_NAME"`,
            ),
            "ITFBU.USER_DISPLAY_NAME as FINISHED_BY_USER_DISPLAY_NAME",
            "ITFBU.USER_NAME as FINISHED_BY_USER_NAME",
            "IP.IPROC_ID",
            "IT.ITASK_ID",
            "IT.ITASK_NAME",
            "IP.IPROC_NAME",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
        ];

        connection
            .distinct(columns)
            .from("INSTANCE_TASK_HISTORY as ITH")
            .innerJoin("USERS as ITU", "ITH.ITASKH_USER_ID", "ITU.USER_ID")
            .innerJoin(
                "USERS as ITFBU",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "ITFBU.USER_ID",
            )
            .leftJoin("INSTANCE_TASKS as IT", "ITH.ITASK_ID", "IT.ITASK_ID")
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "ITH.IPROC_ID")
            .innerJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .innerJoin(
                "USER_ROLES as UR",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "UR.USER_ID",
            )
            .innerJoin("ROLES as R", "UR.ROLE_ID", "R.ROLE_ID")
            .innerJoin("TEMPLATE_PROCESSES as TP", "IP.TPROC_ID", "TP.TPROC_ID")
            .innerJoin(
                "USER_ORGANIZATION_STRUCTURE as UOS",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "UOS.USER_ID",
            )
            .innerJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "UOS.ORGSTR_ID",
                "OS.ORGSTR_ID",
            )
            .innerJoin(
                "INSTANCE_PROCESS_STATIC_RIGHTS as IPSR",
                "ITH.IPROC_ID",
                "IPSR.IPROC_ID",
            )
            .where("ITH.ITASKH_NOTE", "Task finished")
            .whereNotIn("IP.IPROC_STATUS", [
                PROCESS.STATUS_ERASED,
                PROCESS.STATUS_SUSPEND,
            ])
            .where("IT.ITASK_TYPE", TASK.TYPE_STANDARD)
            .as("ignored_alias");

        // HR manager and Administrator can see all finished tasks
        if (!user.isHRManager() && !user.isAdministrator()) {
            connection.where(function () {
                this.whereRaw(
                    `(
                "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                or (${assesmentManagerCondition.sql})
                or ("IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerCondition.sql}))
                or (${solverManagerCondition.sql})
                or ("IPSR"."USER_ID" = ?))
            `,
                    [user.USER_ID, user.USER_ID, user.USER_ID],
                );
            });
        }

        if (_.has(reqQuery, "afterDueDate")) {
            // tasks finished after due date
            connection.whereRaw(
                `"ITH"."ITASKH_DUE_DATE_FINISH" < "ITH"."ITASKH_ACTUAL_DATE_FINISH"`,
            );
        }

        let collection = globalThis.orm.collection(
            "InstanceTaskHistory",
            connection,
        );

        collection.filteringColumns = {
            ROLE_ID: { type: "string", key: "UR.ROLE_ID" },
            ROLE_NAME: { type: "string", key: "R.ROLE_NAME" },
            ORGSTR_ID: { type: "string", key: "UOS.ORGSTR_ID" },
            ORGSTR_NAME: { type: "string", key: "OS.ORGSTR_NAME" },
            TPROC_ID: { type: "string", key: "IP.TPROC_ID" },
            TPROC_NAME: { type: "string", key: "TP.TPROC_NAME" },
            TTASK_ID: { type: "string", key: "TT.TTASK_ID" },
            TTASK_NAME: { type: "string", key: "TT.TTASK_NAME" },
            IPROC_ID: { type: "string", key: "IP.IPROC_ID" },
            IPROC_NAME: { type: "string", key: "IP.IPROC_NAME" },
            ITASK_NAME: { type: "string", key: "IT.ITASK_NAME" },
            HEADER_ID: { type: "string", key: "TH.HEADER_ID" },
            HEADER_NAME: { type: "string", key: "TH.HEADER_NAME" },
            SOLVER_USER_ID: { type: "string", key: "ITU.USER_ID" },
            FINISHED_BY_USER_ID: { type: "string", key: "ITFBU.USER_ID" },
            SOLVER_USER_DISPLAY_NAME: {
                type: "string",
                key: "ITU.USER_DISPLAY_NAME",
            },
            SOLVER_USER_FULL_NAME: {
                type: "string",
                key: "ITU.USER_DISPLAY_NAME",
            },
            FINISHED_BY_USER_FULL_NAME: {
                type: "string",
                key: `ITFBU.USER_LAST_NAME ${globalThis.orm.db.concatColumns()} ITFBU.USER_FIRST_NAME`,
            },
            FINISHED_BY_USER_DISPLAY_NAME: {
                type: "string",
                key: "ITFBU.USER_DISPLAY_NAME",
            },
            FINISHED_BY_USER_NAME: { type: "string", key: "ITFBU.USER_NAME" },
        };

        collection.orderingColumns = _.cloneDeep(collection.filteringColumns);
        collection = BaseController.applyRequestFilter(req, collection);

        if (req.query.order) {
            collection.orderBy(req.query.order, req.query.sort);
        }

        return collection.knex;
    }

    async getUniqueItaskNames(req, res) {
        req.query.limit = req.query.limit || queryLimit;
        const user = await globalThis.container.service.auth.getUserData(req);
        const repo = globalThis.orm.repo(
            "OrganizationStructure",
            this.connection,
        );

        return repo
            .getManagedUsers(user.USER_ID)
            .then((managedUsers) => {
                const assesmentManagerCondition = { conds: [] };
                const procOwnerManagerCondition = { conds: [] };
                const solverManagerCondition = { conds: [] };

                // HR manager and Administrator can see all finished tasks
                if (!user.isHRManager() && !user.isAdministrator()) {
                    const chunked = _.chunk(managedUsers, 999); // Prevent: ORA-01795: maximum number of expressions in a list is 1000

                    chunked.forEach((ids) => {
                        assesmentManagerCondition.conds.push(
                            `"IT"."ITASK_ASSESMENT_USER_ID" in (${ids.join(", ")})`,
                        );
                        procOwnerManagerCondition.conds.push(
                            `"IP"."IPROC_INST_OWNER_USER_ID" in (${ids.join(", ")})`,
                        );
                        solverManagerCondition.conds.push(
                            `"IT"."ITASK_USER_ID" in (${ids.join(", ")})`,
                        );
                    });

                    assesmentManagerCondition.sql =
                        assesmentManagerCondition.conds.length > 0
                            ? assesmentManagerCondition.conds.join(" or ")
                            : " 1 = 0 "; // supervizor ukolu
                    procOwnerManagerCondition.sql =
                        procOwnerManagerCondition.conds.length > 0
                            ? procOwnerManagerCondition.conds.join(" or ")
                            : " 1 = 0 "; // vlastnik pripadu
                    solverManagerCondition.sql =
                        solverManagerCondition.conds.length > 0
                            ? solverManagerCondition.conds.join(" or ")
                            : " 1 = 0 "; // resitel
                }

                const columns = ["IT.ITASK_NAME"];

                const knex = this.connection
                    .distinct(columns)
                    .from("INSTANCE_TASKS as IT")
                    .leftJoin(
                        "INSTANCE_PROCESSES as IP",
                        "IP.IPROC_ID",
                        "IT.IPROC_ID",
                    )
                    .innerJoin(
                        "INSTANCE_PROCESS_STATIC_RIGHTS as IPSR",
                        "IT.IPROC_ID",
                        "IPSR.IPROC_ID",
                    );

                // HR manager and Administrator can see all finished tasks
                if (!user.isHRManager() && !user.isAdministrator()) {
                    knex.where(function () {
                        this.whereRaw(
                            `(
                            "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                            or (${assesmentManagerCondition.sql})
                            or ("IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerCondition.sql}))
                            or (${solverManagerCondition.sql})
                            or ("IPSR"."USER_ID" = ?))
                        `,
                            [user.USER_ID, user.USER_ID, user.USER_ID],
                        );
                    });
                }

                return globalThis.orm.collection("InstanceTask", knex);
            })
            .catch((err) => res.status((err as any).status || 500).send(err));
    }
}
