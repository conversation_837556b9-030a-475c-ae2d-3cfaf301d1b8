import _ from "lodash";
import moment from "moment-timezone";
import { UtilsService } from "../../services/UtilsService";
import { Knex } from "knex";

export class SysVars {
    private connection: Knex;

    private iprocess: any;

    private lang: string;

    private dateFormat: string;

    private caseStatuses: any;

    constructor(iprocess: any, lang: string, dateFormat: string, conn: Knex) {
        this.connection = conn;
        this.iprocess = iprocess;
        this.lang = lang;
        this.dateFormat = dateFormat;
        this.caseStatuses = {};
        moment.locale(lang);
    }

    static getEmptyList(langs: string[] = []) {
        const csStatuses: Record<string, any> = {};
        langs.forEach((lang: string) => {
            csStatuses[`CaseStatus_${lang.toUpperCase()}`] = null;
        });

        return {
            ...csStatuses,
            CaseStatus: null,
            CaseStatus_default: null,
            CaseName: null,
            CaseStart: null,
            CaseDeadline: null,
            CasePriority: null,
            CasePriorityText: null,
            CaseDescription: null,
            CaseOwner: null,
            CaseOwnerName: null,
            CaseNotes: null,
            CaseLink: null,
            CaseVisRoleId: null,
            FrontendLink: null,
            BackendLink: null,
        };
    }

    async getSysValue(sysName: string) {
        if (
            sysName.startsWith("CaseStatus_") &&
            sysName !== "CaseStatus_default"
        ) {
            return await this.getCaseStatuses(sysName);
        }

        switch (sysName) {
            case "CaseStatus":
                return await this.getCaseStatuses(sysName, this.lang);
            case "CaseStatus_default":
                return this.iprocess.IPROC_CASE_STATUS;
            case "CaseName":
                return this.iprocess.IPROC_NAME;
            case "CaseStart":
                return this.iprocess.IPROC_ACTUAL_START_DATE
                    ? `${moment(this.iprocess.IPROC_ACTUAL_START_DATE).format(this.dateFormat)} ${moment(this.iprocess.IPROC_ACTUAL_START_DATE).format("LTS")}`
                    : "";
            case "CaseDeadline":
                return this.iprocess.IPROC_DUE_DATE_FINISH
                    ? `${moment(this.iprocess.IPROC_DUE_DATE_FINISH).format(this.dateFormat)} ${moment(this.iprocess.IPROC_DUE_DATE_FINISH).format("LTS")}`
                    : "";
            case "CasePriority":
                return this.iprocess.IPROC_PRIORITY;
            case "CasePriorityText":
                return globalThis.__({
                    phrase: `priority${this.iprocess.IPROC_PRIORITY}`,
                    locale: this.lang,
                });
            case "CaseDescription":
                return this.iprocess.IPROC_DESCRIPTION || "";
            case "CaseOwner":
                return this.iprocess.raw.IPROC_OWNER_USER_NAME;
            case "CaseOwnerName":
                return this.iprocess.raw.IPROC_OWNER_DISPLAY_NAME;
            case "CaseNotes":
                return await SysVars.printProcessLog(
                    this.connection,
                    this.iprocess.IPROC_ID,
                    null,
                    this.lang,
                    this.dateFormat,
                );
            case "CaseLink":
                return `${globalThis.dynamicConfig.frontendUrl}/cases/case/${this.iprocess.IPROC_ID}`;
            case "CaseVisRoleId":
                return this.iprocess.IPROC_VIS_ROLE_ID || "";
            case "FrontendLink":
                return `${globalThis.dynamicConfig.frontendUrl}`;
            case "BackendLink":
                return `${globalThis.dynamicConfig.hostname}`;
            default:
                return null;
        }
    }

    async getCaseStatuses(statusName: string, forceLang?: string) {
        if (forceLang) {
            statusName = `${statusName}_${forceLang.toUpperCase()}`;
        }

        if (Object.entries(this.caseStatuses).length > 0) {
            // is not empty
            return this.caseStatuses[statusName];
        }

        // Retrieve statuses
        const caseStatusCol = await globalThis.orm
            .repo("caseStatus", this.connection)
            .getForTemplate(this.iprocess.TPROC_ID);
        caseStatusCol.knex.where("CS_NAME", this.iprocess.IPROC_CASE_STATUS);
        const caseStatus = await caseStatusCol.fetchOne();

        if (caseStatus) {
            // CaseStatus translations
            globalThis.dynamicConfig.langs.forEach((lang: string) => {
                this.caseStatuses[`CaseStatus_${lang.toUpperCase()}`] =
                    caseStatus[`CS_NAME_${lang.toUpperCase()}`];
            });

            return this.caseStatuses[statusName];
        }

        return null;
    }

    static async printProcessLog(
        connection: Knex,
        iprocId: number,
        css: string | null,
        lang: string,
        dateFormat: string = "L",
        archived?: boolean,
    ) {
        const repo = globalThis.orm.repo(
            archived ? "archivedInstanceProcessNote" : "instanceProcessNote",
            connection,
        );
        // @ts-expect-error
        const collection = repo.userNotes(iprocId);

        return await collection.collectAll().then((log) => {
            let out = "";

            if (css) {
                out = `<style>${css}</style>`;
            }

            out += '<table class="notes">';
            let odd = true;
            log.forEach((row) => {
                const oddEven = odd ? "odd" : "even";
                odd = !odd;
                let username = row.raw.USER_DISPLAY_NAME;
                if (
                    row.raw.USER_VICE_DISPLAY_NAME &&
                    row.raw.USER_VICE_DISPLAY_NAME !== " "
                ) {
                    username += ` (${row.raw.USER_VICE_DISPLAY_NAME})`;
                }
                moment.locale(lang);
                const date = moment(
                    row.IPN_DATETIME,
                    "YYYY-MM-DDTHH:mm:ss",
                ).format(dateFormat);
                out += `<tr class="${oddEven} type${row.IPN_TYPE}"><td class="username">${username}</td><td class="date">${date}</td></tr>`;
                out += `<tr class="${oddEven} type${row.IPN_TYPE}"><td colspan="2" class="note">${row.IPN_NOTE}</td></tr>`;
            });

            out += "</table>";

            return out;
        });
    }

    static async printProcessHistory(
        connection: Knex,
        user: any,
        iprocId: number,
        css: string,
        lang: string,
        dateFormat: string = "L",
        archived?: boolean,
    ) {
        const administrator =
            user.isAdministrator() || user.isGlobalSupervisor();
        const repo = globalThis.orm.repo(
            archived ? "archivedInstanceTaskHistory" : "instanceTaskHistory",
            connection,
        );
        const collection = repo.forProcess(
            iprocId,
            administrator,
            administrator,
        );

        return await collection.collectAll().then((history) => {
            let out = "";

            if (css) {
                out = `<style>${css}</style>`;
            }
            const taskname = globalThis.__({
                phrase: "taskName",
                locale: lang,
            });
            const solved = globalThis.__({ phrase: "solvedBy", locale: lang });
            const finishDate = globalThis.__({
                phrase: "finishDate",
                locale: lang,
            });
            out += '<table class="history">';
            out += `<thead><tr><th>${taskname}</th><th>${solved}</th><th>${finishDate}</th></tr></thead><tbody>`;

            let odd = true;
            history.forEach((row) => {
                const oddEven = odd ? "odd" : "even";
                odd = !odd;
                // jméno, kdo, kdy ukončil
                moment.locale(lang);
                const date = moment(
                    row.ITASKH_ACTUAL_DATE_FINISH,
                    "YYYY-MM-DDTHH:mm:ss",
                ).format(dateFormat);
                out += `<tr class="${oddEven}"><td class="taskname">${row.raw.ITASK_NAME}</td><td class="finishedBy">${row.raw.ITASKH_FINISHED_BY_USER_NAME}</td><td class="date">${date}</td></tr>`;
            });

            out += "</tbody></table>";

            return out;
        });
    }

    static async printProcessAttachments(
        connection: Knex,
        iprocId: number,
        css: string,
        lang: string,
        dateFormat: string = "L",
        archived?: boolean,
    ) {
        return await globalThis.orm
            .repo(archived ? "archivedDmsFile" : "dmsFile", connection)
            // @ts-expect-error
            .getForProcess(iprocId)
            .then((collection) => {
                collection.knex.where(function () {
                    this.where("DF.IS_CURRENT", "Y").where(
                        "DF.IS_DELETED",
                        "N",
                    );
                });

                return collection.collectAll().then((files) => {
                    let out = "";
                    if (css) {
                        out = `<style>${css}</style>`;
                    }

                    const fileName = globalThis.__({
                        phrase: "fileName",
                        locale: lang,
                    });
                    const owner = globalThis.__({
                        phrase: "owner",
                        locale: lang,
                    });
                    const inserted = globalThis.__({
                        phrase: "inserted",
                        locale: lang,
                    });
                    out += '<table class="attachments">';
                    out += `<thead><tr><th>${fileName}</th><th>${owner}</th><th>${inserted}</th></tr></thead><tbody>`;

                    let odd = true;
                    files.forEach((row) => {
                        const oddEven = odd ? "odd" : "even";
                        odd = !odd;
                        out += `<tr class="${oddEven}">`;
                        out += `<td>${row.NAME}</td>`;
                        out += `<td>${row.raw.DMSF_OWNER}</td>`;
                        moment.locale(lang);
                        const date = moment(
                            row.DATETIME_INSERT,
                            "YYYY-MM-DDTHH:mm:ss",
                        ).format(dateFormat);
                        out += `<td>${date}</td>`;
                        out += "</tr>";
                    });

                    out += "</tbody></table>";

                    return out;
                });
            });
    }

    static async printCustomView(
        connection: Knex,
        userId: number,
        cvName: string,
        filter: string = "",
        css: string = "",
        params: any = {},
        modifiers: any,
        lang: string,
        dateFormat: string = "L",
    ) {
        const cvRepo = globalThis.orm.repo("customView", connection);
        // repo.setLocalization(user.LANGUAGE);
        const cvcRepo = globalThis.orm.repo("customViewColumn", connection);
        let cvId: number;

        let caseLinkColumn = -1;
        if (params.caseLinkColumn) {
            caseLinkColumn = Number(params.caseLinkColumn);
        }

        // setup filter
        let nfilter: string | null = null;
        const pFilter: any = SysVars.parseFilter(filter);

        return await cvRepo
            .findByName(cvName)
            .then((cv) => {
                cvId = cv[0] && cv[0].CV_ID;
                if (cvId) {
                    return cvcRepo.getForCvId(cvId).orderBy("CVC_ID");
                }
                throw new Error(`Custom view ${cvName} does not exist.`);
            })
            .then((columns) => {
                const ids: number[] = [];

                columns.forEach((column) => {
                    ids.push(column.TVAR_ID);
                    if (column.CVC_NAME === pFilter.column) {
                        nfilter = `v${column.TVAR_ID}${pFilter.op}${pFilter.filterValue}`;
                    }
                });

                const varRepo = globalThis.orm.repo(
                    "templateVariable",
                    connection,
                );
                const varColl = varRepo.getById(ids);

                return varColl
                    .collectAssoc("TVAR_ID")
                    .then((tvars) =>
                        cvRepo
                            // @ts-expect-error
                            .getList(
                                cvId,
                                userId,
                                nfilter,
                                null,
                                null,
                                0,
                                100000,
                                false,
                            ) // restLimit == 100000
                            .then((data) => {
                                let out = "";
                                if (css) {
                                    out = `<style>${css}</style>`;
                                }

                                out += `<table class="overview customView${cvId}" id="${cvName}"><thead><tr>`;
                                let colOrder = 1;
                                columns.forEach((column) => {
                                    let cvcName = column.CVC_NAME;
                                    switch (column.TVAR_ID) {
                                        case -1: // knex type
                                            cvcName = "Case name"; // 'Název případu'
                                            break;
                                        case -2:
                                            cvcName = "Description"; // 'Popis'
                                            break;
                                        case -3:
                                            cvcName = "Case owner"; // 'Vlastník případu'
                                            break;
                                        case -4:
                                            cvcName = "Assigned"; // 'Zadáno'
                                            break;
                                        case -5:
                                            cvcName = "Due date"; // 'Termín'
                                            break;
                                        case -6:
                                            cvcName = "Priority"; // 'Priorita'
                                            break;
                                        case -7:
                                            cvcName = "Being solved"; // 'V řešení'
                                            break;
                                        case -8:
                                            cvcName = "Finished"; // 'Dokončeno'
                                            break;
                                        case -9:
                                            cvcName = "Case status"; // 'status'
                                            break;
                                        default:
                                            cvcName = column.CVC_NAME;
                                    }

                                    const name = `V${column.TVAR_ID}`;
                                    out += `<th class="${name} column${colOrder}">${cvcName}</th>`;
                                    colOrder++;
                                });
                                out += "</tr></thead><tbody>";

                                let odd = true;
                                moment.locale(lang);

                                data.items.forEach((row: any) => {
                                    row["v-1"] = row.iproc_name;
                                    row["v-2"] = row.iproc_description;
                                    row["v-3"] = row.iproc_inst_owner_user;
                                    row["v-4"] = row.iproc_actual_start_date;
                                    row["v-5"] = row.iproc_due_date_finish;
                                    row["v-6"] = row.iproc_priority;
                                    row["v-7"] = row.iproc_summary;
                                    row["v-8"] = row.iproc_actual_finish_date;
                                    row["v-9"] = row.iproc_case_status;

                                    colOrder = 1;
                                    const oddEven = odd ? "odd" : "even";
                                    out += `<tr class="${oddEven}">`;

                                    columns.forEach((column) => {
                                        const name = `v${column.TVAR_ID}`;
                                        const type =
                                            tvars[column.TVAR_ID] &&
                                            tvars[column.TVAR_ID][0].TVAR_TYPE;
                                        let value = row[name] || "";
                                        // format date
                                        if (
                                            value &&
                                            (name === "v-4" || name === "v-5")
                                        ) {
                                            if (modifiers[column.CVC_NAME]) {
                                                // todo cvcName
                                                const format =
                                                    modifiers[
                                                        column.CVC_NAME
                                                    ].match(/\((.*?)\)/)[1];
                                                value = moment(
                                                    value,
                                                    "YYYY-MM-DDTHH:mm:ss",
                                                ).format(format); // todo phpDateTimeToMomentFormat
                                            } else {
                                                value = moment(value)
                                                    .format()
                                                    .substring(0, 19);
                                            }
                                        }
                                        if (value && type === "D") {
                                            value = moment(
                                                value,
                                                "YYYY-MM-DDTHH:mm:ss",
                                            ).format(dateFormat);
                                        }
                                        if (value && type === "LD") {
                                            if (Array.isArray(value)) {
                                                const arr = [];
                                                for (
                                                    let i = 0;
                                                    i < value.length;
                                                    i++
                                                ) {
                                                    arr.push(
                                                        moment(
                                                            value,
                                                            "DD-MMM-YY",
                                                        ).format(dateFormat),
                                                    );
                                                }
                                                value = arr.join(", ");
                                            } else {
                                                value = moment(
                                                    value,
                                                    "DD-MMM-YY",
                                                ).format(dateFormat);
                                            }
                                        }
                                        // add trailing zeros to keep the precision
                                        if (value && type === "N") {
                                            const tvarMeta =
                                                tvars[column.TVAR_ID] &&
                                                tvars[column.TVAR_ID][0]
                                                    .TVAR_META;
                                            const parsedMeta = JSON.parse(
                                                tvarMeta || "{}",
                                            );
                                            if (
                                                typeof parsedMeta.numberOfDecimals !==
                                                    "undefined" &&
                                                Number.isInteger(value)
                                            ) {
                                                value = value.toFixed(
                                                    parsedMeta.numberOfDecimals,
                                                );
                                            }
                                            if (modifiers[column.CVC_NAME]) {
                                                // todo cvcName
                                                const numberOfDecimals =
                                                    modifiers[
                                                        column.CVC_NAME
                                                    ].match(/\((.*?)\)/)[1];
                                                if (Number.isInteger(value)) {
                                                    value =
                                                        value.toFixed(
                                                            numberOfDecimals,
                                                        );
                                                } else {
                                                    value =
                                                        UtilsService.roundNumber(
                                                            value,
                                                            numberOfDecimals,
                                                        );
                                                }
                                            }
                                        }
                                        // add link to case
                                        if (caseLinkColumn === colOrder) {
                                            const iprocId = row.id;
                                            value = `<a onclick="showProcessInfo(${iprocId});" href="#">${value}</a>`;
                                        }
                                        out += `<td class="${name} type${type} column${colOrder}">${value}</td>`;
                                        colOrder++;
                                    });
                                    out += "</tr>";
                                    odd = !odd;
                                });
                                out += "</tbody></table>";

                                return out;
                            }),
                    )
                    .catch((e) => {
                        globalThis.tasLogger.error(e.message, {
                            e,
                        });
                        throw e;
                    });
            });
    }

    static parseFilter(userFilter: string) {
        const sanitized = userFilter
            .replace(/&lt;/g, "<")
            .replace(/&gt;/g, ">");
        let parsed = {};

        if (sanitized) {
            const matches = sanitized.match(
                /^([^<>=\!]*)(<eq>|<ne>|<in>|<nin>|<isn>|<isnn>|<like>|<nlike>|<le>|<ge>|<lt>|<gt>|>=|=>|==|<>|<=|=<|<|>|=|\!=)(.*)$/,
            );
            if (!_.isEmpty(matches)) {
                // @ts-expect-error
                const column = matches[1];
                // @ts-expect-error
                let op = matches[2];
                op = SysVars.translateOperator(op);
                // @ts-expect-error
                let filterValue = matches[3];
                filterValue = SysVars.sanitizeFilter(filterValue, op);

                parsed = { column, op, filterValue };
            }
        }

        return parsed;
    }

    static sanitizeFilter(filterValue: string, op: string) {
        let sanitized;

        switch (op) {
            case "<eq>":
                sanitized = `"${filterValue}"`;
                break;
            case "<ne>":
                sanitized = `"${filterValue}"`;
                break;
            case "<le>":
            case "<ge>":
            case "<lt>":
            case "<gt>":
                sanitized = `"${filterValue}"`;
                break;
            case "<nlike>":
                sanitized = `"%${filterValue}%"`;
                break;
            case "<like>":
                sanitized = `"%${filterValue}%"`;
                break;
            default:
                sanitized = `"${filterValue}"`;
        }

        return sanitized;
    }

    static translateOperator(op: string) {
        let top;
        switch (op) {
            case "=":
            case "==":
                top = "<eq>";
                break;
            case "!=":
            case "<>":
                top = "<ne>";
                break;
            case "<=":
            case "=<":
                top = "<le>";
                break;
            case ">=":
            case "=>":
                top = "<ge>";
                break;
            case "<":
                top = "<lt>";
                break;
            case ">":
                top = "<gt>";
                break;
            default:
                top = op;
        }

        return top;
    }
}
