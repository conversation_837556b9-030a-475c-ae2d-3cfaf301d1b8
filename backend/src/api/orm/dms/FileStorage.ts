// @ts-nocheck
// @ts-nocheck
/**
 * Created by Totas on 06.03.2017.
 */
import path from "path";
import crypto from "crypto";
import fs from "fs";
import _ from "lodash";
import { Crypto } from "../../utils/Crypto";
import { UtilsService } from "../../services/UtilsService";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class FileStorage {
    /**
     * Save file to hdd.
     *
     * @param filePath Filepath for storing file.
     * @param tmpFilePath temporary file source path (.tmp/uploads)
     * @parama fileSize
     */
    createFile(filePath, tmpFilePath, fileSize = null, unlinkTempFile = true) {
        const storageDir = globalThis.dynamicConfig.dms.storagePath;
        const absoluteFilePath = path.join(storageDir, filePath);

        return new Promise(async (resolve, reject) => {
            // absolute path to the dir to store file
            const dirPath = path.join(storageDir, path.dirname(filePath));

            if (!(await UtilsService.exists(dirPath))) {
                await fs.promises
                    .mkdir(dirPath, { recursive: true })
                    .catch((directoryCreationError) =>
                        reject(directoryCreationError),
                    );
            }

            if (await UtilsService.exists(absoluteFilePath)) {
                throw new InternalException(
                    `File exists. Cannot be rewritten ${absoluteFilePath}`,
                );
            }

            // Encrypt files before store to FileStorage.
            if (globalThis.dynamicConfig.dms.security.encryptContent) {
                const fileCrypto = new Crypto(
                    globalThis.dynamicConfig.dms.security.password,
                    globalThis.dynamicConfig.dms.security.algorythm,
                );
                return await fileCrypto
                    .encryptFile(tmpFilePath, absoluteFilePath)
                    .then(async () => {
                        // delete the file from .tmp/uploads
                        if (unlinkTempFile !== false) {
                            await fs.promises.unlink(tmpFilePath);
                        }
                        return resolve(absoluteFilePath);
                    })
                    .catch((err) => reject(err));
            }
            try {
                const data = await fs.promises.readFile(tmpFilePath);
                await fs.promises.writeFile(absoluteFilePath, data);

                if (unlinkTempFile !== false) {
                    await fs.promises.unlink(tmpFilePath);
                }

                if (
                    fileSize === null ||
                    (await fs.promises.stat(absoluteFilePath)).size === fileSize
                ) {
                    return resolve(absoluteFilePath);
                }
                return reject(
                    new InternalException(
                        "The file was not physically uploaded!",
                    ),
                );
            } catch (err) {
                return reject(err);
            }
        });
    }

    /**
     * Copy file using streams.
     * @param {String} src Source file
     * @param {String} dest Destination file
     */
    copyFile(src, dest) {
        return new Promise((resolve, reject) => {
            const readStream = fs.createReadStream(src);

            readStream.once("error", (err) => reject(err));

            readStream.once("end", () => resolve());

            readStream.pipe(fs.createWriteStream(dest));
        });
    }

    /**
     * Generates hashed file path for storing file.
     */
    generateFilePath(fileName, revision, iProcId) {
        let iprocId = iProcId || "";
        const dirDelim = path.sep;
        const fileNameHash = crypto
            .createHash("md5")
            .update(fileName)
            .digest("hex");

        if (typeof iprocId === "number") {
            iprocId = iprocId.toString();
        }

        let filePath = iprocId.split("").reverse().join(dirDelim);

        let subdir;
        if (filePath) {
            filePath += dirDelim;
            subdir = "";
        } else {
            subdir = fileNameHash.substring(0, 3);
        }

        filePath += `_${subdir}${iprocId}`;

        fileName = fileName.replace(/–/g, "-"); // dealing with dash
        fileName = fileName.replace(/[<>:"\/\\|?*%]+/g, "-"); // dealing with special characters
        const newFileName = `${_.deburr(
            fileName,
        )}.${fileNameHash}.${revision}.${new Date().getTime()}`;

        return path.join(filePath, newFileName);
    }

    async deleteFile(path) {
        try {
            await fs.promises.unlink(path);
        } catch (err) {
            globalThis.tasLogger.error(
                `Could not delete file ${path} with error ${err.message}`,
                { err, path },
            );
        }
    }
}
