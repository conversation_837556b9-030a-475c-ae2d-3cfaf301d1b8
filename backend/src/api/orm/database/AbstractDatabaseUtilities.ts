import { Knex } from "knex";

export abstract class AbstractDatabaseUtilities {
    abstract concat(): string;

    abstract concatColumns(character?: string): string;

    abstract sequence(sequenceName: string): string;

    abstract fromDual(): string;

    abstract char(number: number): string;

    abstract nvl(value: string, alternative: string): string;

    abstract sysDate(): string;

    abstract toInt(): string;

    abstract statistical(
        aggregation: string,
        round?: number,
        precision?: string,
    ): string;

    abstract substr(
        string: string,
        start: number | string,
        length?: number,
    ): string;

    abstract trunc(dateTime: string, inputDate?: string): string;

    abstract toDate(value?: string): string;

    abstract toDateTime(): string;

    abstract length(column: string): string;

    abstract clobLength(column: string): string;

    abstract blobLength(column: string): string;

    abstract extract(period: string, column: string): string;

    abstract datepart(
        part: string,
        column: string,
        columnAlias: string | null,
    ): (string | string[])[];

    abstract getIndexesForTable(tableName: string): Knex.QueryBuilder;

    abstract rebuildIndex(indexName: string, tableName: string): Promise<void>;

    abstract getInsensitiveCollation(): string;

    abstract like_CI_AI(column: string, comparator: string): string;

    abstract like_CI_AS(column: string, comparator: string): string;

    abstract like(
        column: string,
        escape: string,
        suppressAccentSensitivity: boolean,
        comparator: string,
        value: string | number,
    ): string;

    abstract upper(column: string, isColumn?: boolean): string;

    abstract toString(column: string): string;

    abstract foreignKeys(_column: string, table: string): string;

    abstract primaryKeys(table: string): string;

    abstract charIndex(where: string, what: string, startPos?: number): string;

    abstract getOptionStatuses(database: string): Promise<unknown | null>;

    abstract getDbMajorVersion(): Promise<number>;

    async dropConstraint(
        knex: Knex,
        tableName: string,
        foreignKeyName: string,
    ) {
        try {
            await knex.schema.alterTable(tableName, (table) => {
                table.dropForeign([], foreignKeyName);
            });
        } catch (err: any) {
            // Cannot find the object ... because it does not exist
            if (
                err.message.indexOf("S1000") === -1 &&
                err.message.indexOf("ORA-02443") === -1 &&
                err.message.indexOf("not drop constraint") === -1
            ) {
                return;
            }
            // Silently log error
            globalThis.tasLogger.error(
                `***Silently ignoring dropConstraint error [${tableName}.${foreignKeyName}]: ${err.message} `,
            );
        }
    }

    async createConstraint(
        knex: Knex,
        tableName: string,
        referenceTable: string,
        columns: string[],
        references: string[],
        foreignKeyName: string,
    ): Promise<void> {
        try {
            await knex.schema.alterTable(tableName, (table) => {
                table
                    .foreign(columns)
                    .references(references)
                    .inTable(referenceTable)
                    .withKeyName(foreignKeyName);
            });
        } catch (err: any) {
            // Cannot find the object ... because it does not exist
            // There is already an object named ... in the database.

            const errors = ([] as any[]).concat(
                err.precedingErrors || { message: "" },
                { message: err.message },
            );
            const ignoreError = errors.some(
                ({ message }) =>
                    message.includes("already") ||
                    message.includes("S1000") ||
                    message.includes("ORA-02443") ||
                    message.includes("previous errors"),
            );

            if (!ignoreError) {
                throw err;
            } else {
                // Silently log error
                globalThis.tasLogger.error(
                    `***Silently ignoring constraint error : ${err.message} `,
                );
            }
        }
    }

    async modifyIndex(
        knex: Knex,
        tableName: string,
        columns: string[],
        indexName: string,
        operation: string,
    ): Promise<void> {
        switch (operation) {
            case "CREATE": {
                return await knex.schema
                    .alterTable(tableName, (table) => {
                        table.index(columns, indexName);
                    })
                    .catch((err) => {
                        // Cannot drop the index ... because it does not exist
                        if (
                            err.message.indexOf("index") === -1 &&
                            err.message.indexOf("ORA-00955") === -1
                        ) {
                            throw err;
                        }
                        // Silently log error
                        globalThis.tasLogger.error(
                            `***Silently ignoring error: ${err.message}`,
                        );
                    });
            }
            case "DELETE": {
                return await knex.schema
                    .alterTable(tableName, (table) => {
                        table.dropIndex(columns, indexName);
                    })
                    .catch((err) => {
                        // Cannot find the object ... because it does not exist
                        if (
                            err.message.indexOf("exist") === -1 &&
                            err.message.indexOf("ORA-00955") === -1
                        ) {
                            throw err;
                        }
                        // Silently log error
                        globalThis.tasLogger.error(
                            `***Silently ignoring error: ${err.message}`,
                        );
                    });
            }
            default: {
                throw new Error(`Unknown operation '${operation}'`);
            }
        }
    }

    async dropUnique(
        knex: Knex,
        tableName: string,
        foreignKeyName: string,
    ): Promise<void> {
        try {
            await knex.schema.alterTable(tableName, (table) => {
                table.dropUnique([], foreignKeyName);
            });
        } catch (err: any) {
            // Cannot find the object ... because it does not exist
            if (
                err.message.indexOf("42S02") === -1 &&
                err.message.indexOf("ORA-01418") === -1
            ) {
                throw err;
            }
            // Silently log error
            globalThis.tasLogger.error(
                `***Silently ignoring dropUnique error [${tableName}.${foreignKeyName}]: ${err.message} `,
            );
        }
    }

    async notNull(
        knex: Knex,
        tableName: string,
        colName: string,
    ): Promise<void> {
        try {
            await knex.schema.alterTable(tableName, (table) => {
                table.integer(colName).notNullable().alter();
            });
        } catch (err: any) {
            // Cannot find the object ... because it does not exist
            if (err.message.indexOf("ORA-01442") === -1) {
                throw err;
            }
            // Silently log error
            globalThis.tasLogger.error(
                `***Silently ignoring notNull error [${tableName}.${colName}]: ${err.message} `,
            );
        }
    }

    async primary(
        knex: Knex,
        tableName: string,
        column: string[],
        primaryName: string,
    ): Promise<void> {
        try {
            await knex.schema.alterTable(tableName, (table) => {
                table.primary(column, primaryName);
            });
        } catch (err: any) {
            // ORA-02260: table can have only one primary key
            if (
                err.message.indexOf("ORA-02260") === -1 &&
                err.message.indexOf("already has") === -1 &&
                err.message.indexOf("create constraint") === -1
            ) {
                throw err;
            }
            globalThis.tasLogger.error(
                `***Silently ignoring primary error [${tableName}.${column}]: ${err.message} `,
            );
        }
    }

    async createIndex(
        knex: Knex,
        tableName: string,
        columns: string[],
        indexName: string,
    ): Promise<void> {
        try {
            await knex.schema.alterTable(tableName, (table) => {
                table.index(columns, indexName);
            });
        } catch (err: any) {
            // Cannot drop the index ... because it does not exist
            if (
                err.message.indexOf("index") === -1 &&
                err.message.indexOf("ORA-00955") === -1 &&
                err.message.indexOf("ORA-01408") === -1
            ) {
                throw err;
            }
            // Silently log error
            globalThis.tasLogger.error(
                `***Silently ignoring error: ${err.message}`,
            );
        }
    }

    getParametersChunkSize(entity: Record<string, any>): number {
        const paramsCount = Object.keys(entity).length;
        const dbParamsLimit = globalThis.dynamicConfig.db.maxParameters - 1;
        return Math.floor(dbParamsLimit / paramsCount) || 1;
    }
}
