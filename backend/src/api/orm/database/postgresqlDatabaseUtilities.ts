import moment from "moment";
import { AbstractDatabaseUtilities } from "./AbstractDatabaseUtilities";
import { Knex } from "knex";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class PostgresqlDatabaseUtilities extends AbstractDatabaseUtilities {
    concat(): string {
        return "||"; // PostgreSQL uses || for string concatenation
    }

    concatColumns(character: string = " "): string {
        return `${this.concat()} '${character}' ${this.concat()}`;
    }

    sequence(sequenceName: string): string {
        return `nextval('"${sequenceName}"')`;
    }

    fromDual(): string {
        return ""; // PostgreSQL does not need FROM DUAL
    }

    char(number: number): string {
        return `chr(${number})`; // chr is the equivalent of char in PostgreSQL
    }

    nvl(value: string, alternative: string): string {
        return `COALESCE(${value}, ${alternative})`;
    }

    sysDate(): string {
        return "CURRENT_TIMESTAMP"; // PostgreSQL equivalent of sysdatetime()
    }
    toInt(): string {
        return "CAST(? AS INTEGER)";
    }

    statistical(
        aggregation: string,
        round: number = 2,
        precision: string = "38, 7",
    ): string {
        // PostgreSQL: ROUND(AVG(column), 2)::DECIMAL(38, 7) AS alias
        return `ROUND(${aggregation}(??), ${round})::DECIMAL(${precision}) AS ??`;
    }

    substr(string: string, start: number, length: number = 2147483647): string {
        // PostgreSQL uses SUBSTRING with same syntax, but indexes start at 1 (like MSSQL)
        return `SUBSTRING(${string} FROM ${start || 1} FOR ${length})`;
    }

    /**
     * PostgreSQL handles time zones natively, but to emulate truncation to date (removing time),
     * we can cast to date directly. DST handling should be done outside DB ideally.
     */
    trunc(dateTimeColumn: string, inputDate?: string): string {
        if (typeof inputDate !== "undefined") {
            if (!dateTimeColumn.includes(`"`)) {
                dateTimeColumn = `"${dateTimeColumn.split(".").join(`"."`)}"`;
            }

            // DST logic can still be used outside if needed
            const isDaylightSavingsTime = inputDate
                ? moment(inputDate).isDST()
                : moment().isDST();
            const offset = isDaylightSavingsTime
                ? "INTERVAL '2 hours'"
                : "INTERVAL '1 hour'";
            return `(${dateTimeColumn} + ${offset})::DATE`;
        }
        throw new InternalException(
            `Argument 'inputDate' is mandatory. If you wish to ignore DST, supply 'null'`,
            "INVALID_INPUT",
            { dateTimeColumn },
        );
    }

    toDate(): string {
        // PostgreSQL parses ISO 8601 directly, no need to specify format
        return "CAST(? AS TIMESTAMP)";
    }
    toDateTime(): string {
        // PostgreSQL can cast ISO format strings to TIMESTAMP directly
        return "CAST(? AS TIMESTAMP)";
    }

    length(column: string): string {
        return `LENGTH(${column})`;
    }

    // CLOB equivalent in PostgreSQL is TEXT
    clobLength(column: string): string {
        return `LENGTH(${column})`;
    }

    // BLOBs are BYTEA in PostgreSQL
    blobLength(column: string): string {
        return `OCTET_LENGTH(${column})`;
    }

    extract(period: string, column: string): string {
        // PostgreSQL uses EXTRACT(period FROM column)
        return `EXTRACT(${period} FROM ${column})`;
    }

    datepart(
        part: string,
        column: string,
        columnAlias: string | null = null,
    ): (string | string[])[] {
        let sqlparts: (string | string[])[] = [];

        const allowedParts = [
            "YEAR",
            "MONTH",
            "DAY",
            "WEEKDAY",
            "WEEK",
            "HOUR",
            "MINUTE",
            "SECOND",
        ];

        if (!allowedParts.includes(part)) {
            throw new InternalException("Not allowed DB extract part.");
        }

        let pgPart = part;
        if (part === "WEEKDAY") {
            pgPart = "DOW";
        } // day of week (0=Sunday in PG)
        else if (part === "WEEK") {
            pgPart = "WEEK";
        }

        // Example DST logic: adjust timestamp before extracting
        // PostgreSQL assumes stored timestamps are in UTC unless marked
        let expr: string;

        if (["DAY", "MONTH", "YEAR"].includes(part)) {
            expr = `
                CASE 
                    WHEN EXTRACT(TIMEZONE FROM "${column}" AT TIME ZONE 'CET') = 3600 THEN EXTRACT(${pgPart} FROM ("${column}" + INTERVAL '1 hour'))
                    ELSE EXTRACT(${pgPart} FROM ("${column}" + INTERVAL '2 hour'))
                END
            `;
            sqlparts = [expr, []];
        } else if (part === "WEEKDAY") {
            expr = `
                CASE 
                    WHEN EXTRACT(TIMEZONE FROM "${column}" AT TIME ZONE 'CET') = 3600 
                    THEN EXTRACT(DOW FROM ("${column}" + INTERVAL '1 hour' - INTERVAL '1 day')) 
                    ELSE EXTRACT(DOW FROM ("${column}" + INTERVAL '2 hour' - INTERVAL '1 day')) 
                END
            `;
            sqlparts = [expr, []];
        } else if (part === "WEEK") {
            expr = `
                CASE 
                    WHEN EXTRACT(TIMEZONE FROM "${column}" AT TIME ZONE 'CET') = 3600 
                    THEN EXTRACT(WEEK FROM ("${column}" + INTERVAL '1 hour')) 
                    ELSE EXTRACT(WEEK FROM ("${column}" + INTERVAL '2 hour')) 
                END
            `;
            sqlparts = [expr, []];
        } else {
            expr = `EXTRACT(${pgPart} FROM "${column}")`;
            sqlparts = [expr, []];
        }

        if (columnAlias) {
            sqlparts[0] += ` AS ${columnAlias}`;
        }

        return sqlparts;
    }

    getIndexesForTable(tableName: string): Knex.QueryBuilder {
        return globalThis.database
            .distinct("indexname")
            .from("pg_indexes")
            .where("schemaname", "public")
            .andWhere("tablename", tableName)
            .andWhereNot("indexname", "like", "%_pkey")
            .andWhereNot("indexname", "like", "%_idx%unique%")
            .andWhereNot("indexname", "like", "%_uniq%");
    }

    async rebuildIndex(indexName: string, tableName: string): Promise<void> {
        globalThis.tasLogger.info(`Rebuilding index '${indexName}'`, {
            indexName,
            tableName,
        });

        await globalThis.container.client.database.callKnexRaw(
            `REINDEX INDEX "${indexName}"`,
        );

        globalThis.tasLogger.info(`Rebuilt index '${indexName}'`, {
            indexName,
            tableName,
        });
    }

    getInsensitiveCollation(): string {
        // Optional: Return ICU collation if needed
        return '"und-x-icu"'; // Unicode case- and accent-insensitive, if available
    }

    like_CI_AI(column: string, comparator: string): string {
        // For case-insensitive AND accent-insensitive search
        // Requires the 'unaccent' extension in PostgreSQL
        return `unaccent(${column}) ${comparator} unaccent(?)`;
    }

    like_CI_AS(column: string, comparator: string): string {
        // For case-insensitive (but accent-sensitive) search
        return `${column} ${comparator} ?`;
    }

    like(
        column: string,
        escape: string = "",
        suppressAccentSensitivity: boolean = false,
        comparator: string = "ILIKE",
        value: string,
    ): string {
        if (!column.includes(`"`)) {
            column = `"${column.split(".").join(`"."`)}"`;
        }
        const rawValue = value.replace(/%/g, "");
        const likeFunction =
            suppressAccentSensitivity && isNaN(Number(rawValue))
                ? this.like_CI_AI.bind(this)
                : this.like_CI_AS.bind(this);
        return `${likeFunction(column, comparator)} ${escape}`;
    }

    upper(column: string, isColumn: boolean = false): string {
        return `UPPER(${isColumn ? `"` : ""}${column}${isColumn ? `"` : ""})`;
    }

    toString(column: string): string {
        return `CAST(${column} AS TEXT)`;
    }

    foreignKeys(column: string): string {
        return `
            SELECT
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM 
                information_schema.table_constraints AS tc 
            JOIN 
                information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
            JOIN 
                information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
            WHERE 
                constraint_type = 'FOREIGN KEY' AND 
                kcu.column_name = '${column}'`;
    }

    primaryKeys(table: string): string {
        return `
            SELECT
                kcu.column_name
            FROM 
                information_schema.table_constraints AS tc
            JOIN 
                information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
            WHERE 
                tc.table_name = '${table}'
                AND tc.constraint_type = 'PRIMARY KEY'`;
    }

    charIndex(where: string, what: string, _startPos: number = 1): string {
        return `POSITION(${where} IN ${what})`;
    }

    async getOptionStatuses(database: string): Promise<unknown | null> {
        const statuses: {
            READ_COMMMITTED_SNAPSHOT?: unknown;
            COLLATION?: unknown;
        } = {};
        statuses.READ_COMMMITTED_SNAPSHOT =
            await this.getReadCommittedStatus(database);
        statuses.COLLATION = await this.getCurrentCollation(database);

        return statuses;
    }

    async getReadCommittedStatus(_database: string): Promise<unknown | null> {
        // PostgreSQL uses Read Committed by default, no system table for snapshot
        const result = await globalThis.container.client.database.callKnexRaw(
            `SHOW default_transaction_isolation`,
        );
        return result?.rows?.[0]?.default_transaction_isolation || null;
    }

    async getCurrentCollation(_database: string): Promise<unknown | null> {
        const result =
            await globalThis.container.client.database.callKnexRaw(
                `SHOW lc_collate`,
            );
        return result?.rows?.[0]?.lc_collate || null;
    }

    async getDbMajorVersion(): Promise<number> {
        const result =
            await globalThis.container.client.database.callKnexRaw(
                `SHOW server_version`,
            );
        const versionString = result?.rows?.[0]?.server_version || "";

        const majorVersion = parseInt(versionString.split(".")[0], 10);
        if (!isNaN(majorVersion)) {
            return majorVersion;
        }

        globalThis.tasLogger.warning(
            `Unable to get database version, defaulting to min supported version ${globalThis.dynamicConfig.db.minSqlVersion}`,
        );
        return globalThis.dynamicConfig.db.minSqlVersion;
    }

    castValueAsVarChar(length: string): string {
        return `CAST(? AS VARCHAR(${length}))`;
    }

    async getDbStatus(): Promise<string> {
        const result = await globalThis.container.client.database.callKnexRaw(
            `SELECT current_database()`,
        );
        const dbName = result?.rows?.[0]?.current_database;

        // Simulate "ONLINE" since PostgreSQL doesn't expose internal DB status like MSSQL
        if (dbName) {
            return "ONLINE";
        }

        globalThis.tasLogger.warning(`Database is not reachable or unknown.`);
        return "UNKNOWN";
    }
}
