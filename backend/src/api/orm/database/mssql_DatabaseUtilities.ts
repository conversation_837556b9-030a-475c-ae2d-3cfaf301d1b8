import moment from "moment";
import { AbstractDatabaseUtilities } from "./AbstractDatabaseUtilities";
import { Knex } from "knex";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class mssql_DatabaseUtilities extends AbstractDatabaseUtilities {
    concat(): string {
        return "+";
    }

    concatColumns(character: string = " "): string {
        return `${this.concat()} '${character}' ${this.concat()}`;
    }

    sequence(sequenceName: string): string {
        return `next value for ${sequenceName}`;
    }

    fromDual(): string {
        return "";
    }

    char(number: number): string {
        return `char(${number})`;
    }

    nvl(value: string, alternative: string): string {
        return `ISNULL(${value},${alternative})`;
    }

    sysDate(): string {
        return "sysdatetime()";
    }

    toInt(): string {
        return "CAST(? as INT)";
    }

    statistical(
        aggregation: string,
        round: number = 2,
        precision: string = "38, 7",
    ): string {
        return `CAST(ROUND(${aggregation}(??), ${round}) as DECIMAL(${precision})) AS ??`;
    }

    substr(string: string, start: number, length: number = 2147483647) {
        return `SUBSTRING(${string}, ${start || 1}, ${length})`;
    }

    trunc(dateTimeColumn: string, inputDate?: string): string {
        if (typeof inputDate !== "undefined") {
            const isDaylightSavingsTime = inputDate
                ? moment(inputDate).isDST()
                : moment().isDST();
            return `CAST(Dateadd(hh, ${isDaylightSavingsTime ? 2 : 1}, ${dateTimeColumn}) as Date)`;
        }
        throw new InternalException(
            `Argument 'inputDate' is mandatory. If you wish to ignore DST, supply 'null'`,
            "INVALID_INPUT",
            { dateTimeColumn },
        );
    }

    toDate(): string {
        // 120 because 105 doesn't work
        return "CONVERT(NVARCHAR, ?, 120)";
    }

    toDateTime(): string {
        // yyyy-mm-dd hh:mi:ss(24h)
        return "CONVERT(NVARCHAR, ?, 120)";
    }

    length(column: string): string {
        return `LEN(${column})`;
    }

    // CLOB equivalent is VARCHAR(MAX) / NVARCHAR(MAX)
    clobLength(column: string): string {
        return `LEN(${column})`;
    }

    // BLOB equivalent is VARBINARY(MAX)
    blobLength(column: string): string {
        return `DATALENGTH(${column})`;
    }

    extract(period: string, column: string): string {
        return `${period}(${column})`;
    }

    datepart(
        part: string,
        column: string,
        columnAlias: string | null = null,
    ): (string | string[])[] {
        let sqlparts = [];
        if (
            ![
                "YEAR",
                "MONTH",
                "DAY",
                "WEEKDAY",
                "WEEK",
                "HOUR",
                "MINUTE",
                "SECOND",
            ].includes(part)
        ) {
            throw new InternalException("Not allowed DB extract part.");
        }

        if (part === "DAY" || part === "MONTH" || part === "YEAR") {
            sqlparts = [
                `CASE DATEPART(tzoffset, ?? AT TIME ZONE 'Central European Standard Time') WHEN '60'
                THEN DATEPART(${part}, DATEADD(HOUR, 1, ??))
                ELSE DATEPART(${part}, DATEADD(HOUR, 2, ??)) END`,
                [column, column, column],
            ];
        } else if (part === "WEEKDAY") {
            sqlparts = [
                "CASE DATEPART(tzoffset, ?? AT TIME ZONE 'Central European Standard Time') WHEN '60'" +
                    "THEN DATEPART(WEEKDAY, DATEADD(DAY, -1, DATEADD(HOUR, 1, ??))) " +
                    "ELSE DATEPART(WEEKDAY, DATEADD(DAY, -1, DATEADD(HOUR, 2, ??))) END",
                [column, column, column],
            ];
        } else if (part === "WEEK") {
            sqlparts = [
                `CASE DATEPART(tzoffset, ?? AT TIME ZONE 'Central European Standard Time') WHEN '60'
                THEN DATEPART(ISO_WEEK, DATEADD(HOUR, 1, ??))
                ELSE DATEPART(ISO_WEEK, DATEADD(HOUR, 2, ??)) END`,
                [column, column, column],
            ];
        } else {
            sqlparts = [`DATEPART(${part}, ??)`, [column]];
        }

        if (columnAlias) {
            sqlparts[0] += ` as ${columnAlias}`;
        }
        return sqlparts;
    }

    getIndexesForTable(tableName: string): Knex.QueryBuilder {
        return globalThis.database
            .distinct("ind.name")
            .from("sys.indexes as ind")
            .innerJoin("sys.index_columns as ic", (onBuilder) => {
                onBuilder
                    .on("ind.object_id", "ic.object_id")
                    .andOn("ind.index_id", "ic.index_id");
            })
            .innerJoin("sys.tables as t", "ind.object_id", "t.object_id")
            .where("ind.is_primary_key", 0)
            .where("ind.is_unique", 0)
            .where("ind.is_unique_constraint", 0)
            .where("t.is_ms_shipped", 0)
            .where("t.name", tableName);
    }

    async rebuildIndex(indexName: string, tableName: string): Promise<void> {
        globalThis.tasLogger.info(`Rebuilding index '${indexName}'`, {
            indexName,
            tableName,
        });
        await globalThis.container.client.database.callKnexRaw(
            `ALTER INDEX [${indexName}] ON [${tableName}] REBUILD WITH (FILLFACTOR = 80)`,
        );
        globalThis.tasLogger.info(`Rebuilt index '${indexName}'`, {
            indexName,
            tableName,
        });
    }

    getInsensitiveCollation(): string {
        return "latin1_general_ci_ai";
    }

    like_CI_AI(column: string, comparator: string): string {
        return `${column} COLLATE ${this.getInsensitiveCollation()} ${comparator} ?`;
    }

    like_CI_AS(column: string, comparator: string): string {
        return `${column} ${comparator} ?`;
    }

    like(
        column: string,
        escape: string = "",
        suppressAccentSensitivity: boolean = false,
        comparator: string = "LIKE",
        value: string,
    ): string {
        const rawValue = value.replace(/%/g, "");

        const likeFunction =
            suppressAccentSensitivity && isNaN(Number(rawValue))
                ? this.like_CI_AI.bind(this)
                : this.like_CI_AS.bind(this);

        return `${likeFunction(column, comparator)} ${escape}`;
    }

    upper(column: string): string {
        return column;
    }

    toString(column: string): string {
        return `CONVERT(varchar, ${column}) `;
    }

    foreignKeys(column: string): string {
        return `SELECT
            *
            FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS AS RC
                     INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS KCU
                                ON KCU.CONSTRAINT_CATALOG = RC.CONSTRAINT_CATALOG
                                    AND KCU.CONSTRAINT_SCHEMA = RC.CONSTRAINT_SCHEMA
                                    AND KCU.CONSTRAINT_NAME = RC.CONSTRAINT_NAME
            WHERE
        KCU.COLUMN_NAME = '${column}'`;
    }

    primaryKeys(table: string): string {
        return `SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
                WHERE TABLE_NAME ='${table}' 
                and constraint_type = 'PRIMARY KEY'`;
    }

    charIndex(where: string, what: string, startPos: number = 1): string {
        return `CHARINDEX(${where}, ${what}, ${startPos})`;
    }

    async getOptionStatuses(database: string): Promise<unknown | null> {
        const statuses: {
            READ_COMMMITTED_SNAPSHOT?: unknown;
            COLLATION?: unknown;
        } = {};
        statuses.READ_COMMMITTED_SNAPSHOT =
            await this.getReadCommittedStatus(database);
        statuses.COLLATION = await this.getCurrentCollation(database);

        return statuses;
    }

    async getReadCommittedStatus(database: string): Promise<unknown | null> {
        const status = await globalThis.container.client.database.callKnexRaw(
            `SELECT
                name as "DB_NAME", is_read_committed_snapshot_on as READ_COMMMITTED_SNAPSHOT
            FROM sys.databases
            WHERE name = ?`,
            [database],
        );

        if (status.length <= 0) {
            throw new InternalException(
                `READ_COMMMITTED_SNAPSHOT status unknown. Database '${database}' was not found or User does not have permissions. `,
            );
        }

        return status[0].READ_COMMMITTED_SNAPSHOT;
    }

    async getCurrentCollation(database: string): Promise<unknown | null> {
        const status = await globalThis.container.client.database.callKnexRaw(
            `SELECT collation_name FROM sys.databases WHERE name = ?`,
            [database],
        );

        if (status.length <= 0) {
            globalThis.tasLogger.warning(
                `Collation of database ${database} is unknown. Could not find it or do not have permissions.`,
            );
            return null;
        }

        return status[0].collation_name;
    }

    async getDbMajorVersion(): Promise<number> {
        const result: any = globalThis.database.raw(
            `SELECT SERVERPROPERTY('ProductMajorVersion') AS VERSION`,
        );
        const version = result[0];

        if (version) {
            const majorVersion = parseInt(version.VERSION, 10);
            return majorVersion;
        }

        globalThis.tasLogger.warning(
            `Unable to get database version, defaulting to min supported version ${globalThis.dynamicConfig.db.minSqlVersion}`,
        );
        return globalThis.dynamicConfig.db.minSqlVersion;
    }

    castValueAsVarChar(length: string): string {
        return `CAST(? as varchar(${length}))`;
    }

    getDbStatus(): Promise<unknown> {
        const databaseName =
            globalThis.database.client.config.connection.database;
        return globalThis.database
            .raw("SELECT state_desc FROM sys.databases WHERE name = ?", [
                databaseName,
            ])
            .then((result) => {
                if (result[0] && result[0].state_desc !== "ONLINE") {
                    globalThis.tasLogger.warning(
                        `Database is not online. Status: ${result[0].state_desc}`,
                    );
                }
                return result[0] ? result[0].state_desc : "";
            });
    }
}
