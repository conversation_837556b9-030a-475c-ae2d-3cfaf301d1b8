// @ts-nocheck
import _ from "lodash";
import moment from "moment";
import { BaseEntity } from "./entity/BaseEntity";
import { BaseRepository } from "./repository/BaseRepository";
import { Knex } from "knex";
import { Filter } from "./Filter";
import { UtilsService } from "../services/UtilsService";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { Order } from "./Order";

export class BaseCollection<T extends BaseEntity> {
    repository: BaseRepository<T>;

    knex: Knex.QueryBuilder;

    private _lang: any;

    private _hasOne: object;

    public _totalCount: any;

    private _filteringColumns: any;

    private _orderingColumns: any;

    constructor(
        repository: BaseRepository<T>,
        knexConnection: Knex.QueryBuilder,
    ) {
        this.repository = repository; // Repository holds some metadata
        this.knex = knexConnection;
        this._hasOne = {}; // Holds info about relations
        this._totalCount = null;
        this.setLocalization(repository._lang);
    }

    /**
     *
     * @param rawFilter
     * @returns {BaseCollection}
     */
    filter(rawFilter, options?: any): BaseCollection<T> {
        const filter = new Filter(this.repository, null, options);
        filter.filteringColumns = this._filteringColumns;
        this.knex = filter.apply(this.knex, rawFilter);
        return this;
    }

    /**
     * Fetching only as array. Is same as calling .then(() => {}) on collection.
     * Ignores entity relations (don't create any entities)
     *
     * @returns Promise
     */
    fetchAll(): Promise<Knex.QueryBuilder> {
        return this.knex;
    }

    /**
     * Fetching only first row.
     *
     * @returns Promise
     */
    fetchOne(): Promise<any> {
        return this.knex.first();
    }

    async getByName(name) {
        const data = await this.repository.getByName(name);
        return this.repository.getEntity(data);
    }

    /**
     * Converts all rows to entities.
     * @returns Promise
     */
    async collectAll(): Promise<T[]> {
        const self = this;

        return (
            this.knex
                .then((result) => {
                    let rows;
                    if (result && result.rows) {
                        rows = result.rows;
                    } else if (Array.isArray(result)) {
                        rows = result;
                    } else if (result && result[0]) {
                        rows = result[0]; // Some MSSQL drivers wrap array inside object
                    } else {
                        rows = [];
                    }

                    return this.repository.castRows(
                        rows,
                        this.baseEntity.getAttributes(false, true),
                    );
                })
                // OR simply .then(this.repository.castRows.bind(this.repository))
                .then((rows) =>
                    rows.map((row) => {
                        const obj = this.repository.entityClass();
                        if (self._lang) {
                            obj.setLocalization(self._lang);
                        }
                        obj.raw = row; // set raw data
                        return obj.fill(row);
                    }),
                )
                // retrieving related entities
                .then((entities) => {
                    if (_.isEmpty(this._hasOne)) {
                        return entities;
                    } else {
                        return entities.map((ent: BaseEntity) => {
                            for (const relPrefix in this._hasOne) {
                                // rel = prefix
                                if (!_.has(this._hasOne, relPrefix)) {
                                    continue;
                                }
                                const relName = this._hasOne[relPrefix];
                                const relObj = ent.relations()[relName]();
                                if (self._lang) {
                                    relObj.setLocalization(self._lang);
                                }

                                ent._relatedOne[relName] = relObj.fill(
                                    // just pick only the prefixed attributes
                                    _.pick(ent.raw, (_val, key) =>
                                        _.startsWith(key, relPrefix),
                                    ),
                                    false,
                                    relPrefix,
                                );
                            }
                            return ent;
                        });
                    }
                })
        );
    }

    /**
     * Convert first row to entity.
     * Returns null when no rows found.
     *
     * @returns Promise
     */
    collectOne(): Promise<T> {
        const obj = this.repository.entityClass();
        if (this._lang) {
            obj.setLocalization(this._lang);
        }

        return this.knex
            .first()
            .then((item) => {
                if (!item) {
                    return null;
                }
                return this.repository.castRow(
                    item,
                    this.baseEntity.getAttributes(false, true),
                );
            })
            .then((row) => {
                if (row === null) {
                    return null;
                }
                obj.raw = row; // set raw data
                return obj.fill(row); // fill properties
            });

        // TODO: Can it be refactored like this?
        // Without .raw properties
        // return this.knex.first().then(item => {
        //     return !item ? null : this.repository.getEntity(item);
        // });
    }

    /**
     * Makes Object associated with attributes value. {attr-value => obj, attr-value => obj2}
     *
     * @param attr
     * @param simple If true then returns object where property is attribute value.
     * @returns {Promise.<TResult>|Promise<U>}
     */
    collectAssoc(attr, simple = false): Promise<any> {
        return this.collectAll().then((entities) => {
            const assocEntities = {};
            entities.map((item) => {
                if (item[attr] == "" || item[attr] == null) {
                    return; // skip empty values
                }
                if (simple) {
                    if (typeof assocEntities[item[attr]] !== "undefined") {
                        throw new InternalException(
                            "More then one value to convert to object.",
                        );
                    }
                    assocEntities[item[attr]] = item;
                } else {
                    if (typeof assocEntities[item[attr]] == "undefined") {
                        assocEntities[item[attr]] = [];
                    }
                    assocEntities[item[attr]].push(item);
                }
            });

            return assocEntities;
        });
    }

    /**
     * Makes Object associated with attributes value. {attr-value => obj, attr-value => obj2}
     *
     * @param attr
     * @param simple If true then returns object where property is attribute value.
     * @returns {Promise.<TResult>|Promise<U>}
     */
    fetchAssoc(attr, simple = false): Promise<any> {
        return this.fetchAll().then((items) => {
            const assocEntities = {};
            items.map((item) => {
                if (item[attr] == "" || item[attr] == null) {
                    return; // skip empty values
                }
                if (simple) {
                    if (typeof assocEntities[item[attr]] !== "undefined") {
                        throw new InternalException(
                            "More then one value to convert to object.",
                        );
                    }
                    assocEntities[item[attr]] = item;
                } else {
                    if (typeof assocEntities[item[attr]] === "undefined") {
                        assocEntities[item[attr]] = [];
                    }
                    assocEntities[item[attr]].push(item);
                }
            });

            return assocEntities;
        });
    }

    /**
     * Returns total count. Invokes immediately the query.
     *
     * @returns Promise
     */
    async getTotalCount(): Promise<any> {
        if (this._totalCount) {
            return this._totalCount;
        }

        // Apply filter but do not apply limit and order
        const clone = this.knex.clone();

        // reset limit
        clone.limit(2000000000);

        // reset offset
        clone.offset(null);

        // remove columns (not necessary for count)
        let distinctColumn = null;
        let isGroupBy = false;

        // Ignore 'mssql' with statements (they always need to be on the very top)
        const firstStatement = _.head(
            _.filter(
                clone._statements,
                (statement) => statement.grouping !== "with",
            ),
        );

        if (firstStatement && firstStatement.grouping === "columns") {
            // Is distinct? Keep for later
            if (firstStatement.distinct) {
                distinctColumn =
                    firstStatement.value[0].sql || firstStatement.value[0];
            }

            // keep columns for GROUP BY
            if (_.find(clone._statements, ["grouping", "group"])) {
                isGroupBy = true;
            } else {
                _.remove(clone._statements, firstStatement); // find and remove first el
            }
        }

        // remove all order by statements (not necessary for count)
        clone._clearGrouping("order");

        let count = [{ total: 0 }];

        // Is distinct? Need to apply distinctCount
        if (distinctColumn) {
            count = await clone.countDistinct(distinctColumn);
        } else if (isGroupBy) {
            count = await globalThis.database
                .select()
                .from(clone.as("ignored_alias2"))
                .count("* as total");
        } else {
            count = await clone.count("* as total");
        }

        // Distinct column can have an Alias, need to get key
        const key = Object.keys(count[0])[0];
        this._totalCount = Number(count[0][key]);

        // Cache and return
        return this._totalCount;
    }

    set filteringColumns(filteringColumns) {
        this._filteringColumns = filteringColumns;

        // Default the value of ordering columns to filtering columns
        if (!this.orderingColumns) {
            this.orderingColumns = filteringColumns;
        }
    }

    get filteringColumns() {
        return this._filteringColumns;
    }

    set orderingColumns(orderingColumns) {
        this._orderingColumns = orderingColumns;
    }

    get orderingColumns() {
        return this._orderingColumns;
    }

    get baseEntity() {
        return this.repository.entity;
    }

    /******************************************************************************************************************/
    /* Some methods for relations */
    /******************************************************************************************************************/

    hasOne(prefix, relationName) {
        this._hasOne[prefix] = relationName;
        return this;
    }

    hasMany(_prefix, _relatedEntity) {
        return this;
    }

    setLocalization(lang?: string) {
        if (lang) {
            this._lang = lang.toUpperCase();
        }
    }

    /******************************************************************************************************************/
    /* Some knex methods (only proxy) */
    /******************************************************************************************************************/

    limit(value) {
        if (value && !UtilsService.isNumericString(value)) {
            throw new UserException("Limit must be numeric.", "BAD_INPUT", {
                value,
            });
        }
        this.knex.limit(value);
        return this;
    }

    offset(value) {
        if (value && !UtilsService.isNumericString(value)) {
            throw new UserException("Offset must be numeric.", "BAD_INPUT", {
                value,
            });
        }

        this.knex.offset(value);
        return this;
    }

    /**
     *
     * @param {string} column Order by column. Multi ordering allowed with separator column1, column2
     * @param {string} direction asc, desc
     * @param {Boolean} nullsLast
     */
    orderBy(column: string, direction?: string, nullsLast?: boolean) {
        if (column) {
            // The expected input is string, convert into an Array
            const columns = column.split(",");
            const directions = direction ? direction.split(",") : [];

            // Convert the two input arguments into a single variable
            const inputColumns = columns.map((column, index) => ({
                value: column,
                direction: directions[index] || null,
            }));

            // @t3b-1862 MSSQL orderBy duplicitni sloupce
            // > A column has been specified more than once in the order by list. Columns in the order by list must be unique!
            const definedColumns = _.filter(
                Object.values(this.knex._statements),
                {
                    grouping: "order",
                },
            );

            /* Merged columns
                - must be unique
                - new columns must come before already defined columns
                - must respect the defined order (asc, desc)
                - case insensitive
            */
            const mergedColumns = _.chain(inputColumns.concat(definedColumns))
                .groupBy((item) => item.value.toUpperCase())
                .values()
                .value()
                .reduce(
                    (result, row) =>
                        // There may be multiple entries, but we don't want them
                        result.concat(row[0]),
                    [],
                );

            // Clear the old 'order' grouping
            this.knex._clearGrouping("order");

            // Create new orders based on the merged rows
            mergedColumns.forEach(({ value, direction }) => {
                this._orderBy(value, direction, nullsLast);
            });
        }
        return this;
    }

    /**
     *
     * @param column
     * @param direction
     * @param nullsLast
     * @returns {BaseCollection}
     * @private
     */
    _orderBy(column: string, direction: string, nullsLast: boolean) {
        const order = new Order(this.repository);
        order.orderingColumns = this._orderingColumns;
        order.ini(this.knex);
        let sanitizedColumn = order.sanitizeKey(column);

        let orderFirst = "'A'";
        let orderLast = "'ZZZZZ'";

        // @t3b-1533 nullsLast konci chybou pri razeni date/time sloupce
        if (sanitizedColumn.includes("DATE")) {
            orderFirst = `'${moment(new Date(-8640000000000)).format("YYYY-MM-DD")}'`;
            orderLast = `'${moment(new Date(86400000000000)).format("YYYY-MM-DD")}'`;
        }

        // @t3b-1438 Řazení nulls last
        if (nullsLast) {
            switch (globalThis.dynamicConfig.db.client) {
                case "mssql":
                    sanitizedColumn = `case when ${sanitizedColumn} is null then ${direction && direction === "asc" ? orderLast : orderFirst} else ${sanitizedColumn} end ${direction}`;
                    break;
                case "postgresql":
                    sanitizedColumn = `"${sanitizedColumn.split(".").join(`"."`)}" ${direction || ""} nulls last`;
                    break;
                default:
                    break;
            }
            this.knex.orderByRaw(sanitizedColumn);
        } else {
            this.knex.orderBy(sanitizedColumn, direction);
        }

        return this;
    }

    union(collection: BaseCollection<T>): BaseCollection<T> {
        const unionQuery = this.knex.clone().union(collection.knex.clone());
        this.resetQuery();
        this.knex.select("*").from(unionQuery.as("union_table"));
        return this;
    }

    resetQuery() {
        this.knex.clear("with");
        this.knex.clear("select");
        this.knex.clear("columns");
        this.knex.clear("hintComments");
        this.knex.clear("where");
        this.knex.clear("union");
        this.knex.clear("join");
        this.knex.clear("group");
        this.knex.clear("order");
        this.knex.clear("having");
        this.knex.clear("limit");
        this.knex.clear("offset");
        this.knex.clear("counter");
        this.knex.clear("counters");
        return this;
    }
}
