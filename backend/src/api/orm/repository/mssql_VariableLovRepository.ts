// @ts-nocheck
// @ts-nocheck
import { VariableLovRepository } from "./VariableLovRepository";

export class mssql_VariableLovRepository extends VariableLovRepository {
    copyVariableLovsFromTemplate(iprocId) {
        // Translations
        const extraColumns = [];
        const tprocEntity = globalThis.orm
            .repo("VariableLov", this.connection)
            .getEntity();
        const attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (
                attrs[attrName].translated &&
                attrName.indexOf("IVARLOV_TEXT_VALUE") > -1
            ) {
                extraColumns.push(attrName);
            }
        });

        const extraColumnsInto = extraColumns;
        const extraColumnsFrom = extraColumns.map(
            (col) => `tvl.T${col.substring(1)}`,
        );

        const columnsInto = [
            "org_id",
            "ivarlov_id",
            "ivar_id",
            "ivarlov_is_selected",
            "ivarlov_text_value",
            "ivarlov_number_value",
            "ivarlov_date_value",
        ].concat(extraColumnsInto);
        const columnsFrom = [
            "1",
            globalThis.orm.db.sequence("ivarlov_id_seq").toString(),
            "iv.ivar_id",
            "tvl.tvarlov_is_selected",
            "tvl.tvarlov_text_value",
            "tvl.tvarlov_number_value",
            "tvl.tvarlov_date_value",
        ].concat(extraColumnsFrom);

        const copyLovsSql = `
        INSERT INTO instance_variable_lov
            (
                ${columnsInto.join(",")}
            )
        SELECT  ${columnsFrom.slice(0, 2).join(",")}, se.*
        FROM
        (
            SELECT TOP 100 PERCENT ${columnsFrom.slice(2).join(",")} FROM instance_variables iv
            LEFT JOIN template_variable_lov tvl
            ON        (
                                tvl.tvar_id = iv.tvar_id)
            WHERE     iv.iproc_id = :IPROC_ID
            AND       iv.ivar_type IN ('LT',
                                    'LN',
                            'LD')
            ORDER BY tvl.tvarlov_id ASC
        ) AS se
       `;

        return globalThis.container.client.database.callKnexRaw(
            copyLovsSql,
            { IPROC_ID: iprocId },
            this.connection,
        );
    }
}
