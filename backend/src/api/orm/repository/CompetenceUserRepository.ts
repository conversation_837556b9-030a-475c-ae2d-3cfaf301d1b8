// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as competenceConsts from "../entity/const/competenceConsts";
import { CompetenceUser } from "../entity/CompetenceUser";

export class CompetenceUserRepository extends BaseRepository<CompetenceUser> {
    meta() {
        return {
            tableName: "COMPETENCE_USERS",
            entityName: "CompetenceUser",
            entity: () => new CompetenceUser(),
        };
    }

    async has(competenceId, userId, source) {
        let conn = this.connection
            .select()
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId)
            .where("USER_ID", userId);
        if (source) {
            conn = conn.where("COMPETENCE_SOURCE", source);
        }
        return (await conn).length > 0;
    }

    /**
     *
     * @param {Array<number>} userIds
     * @param {number} competenceId
     * @param {string} source
     * @returns {Promise<void>}
     */
    async addUsers(
        userIds: number[] = [],
        competenceId: number,
        source: string = null,
        competenceRuleId = null,
    ) {
        if (userIds.length) {
            await this.connection.into(this.tableName).insert(
                userIds.map((userId) => ({
                    COMPETENCE_ID: competenceId,
                    USER_ID: userId,
                    COMPETENCE_SOURCE: source,
                    COMPETENCE_RULE_ID: competenceRuleId,
                })),
            );

            // Mark the competence for rebuild
            await globalThis.orm
                .repo("competence", this.connection)
                .markForRebuild(competenceId);
        }
    }

    /**
     *
     * @param {Array<number>} userIds
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async removeUsers(userIds: any[] = [], competenceId) {
        // Delete all Users in the list
        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId)
            .whereIn("USER_ID", Array.isArray(userIds) ? userIds : [userIds])
            .del();

        // Mark the competence for rebuild
        await globalThis.orm
            .repo("competence", this.connection)
            .markForRebuild(competenceId);
    }

    /**
     *
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async deleteUsers(competenceId) {
        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId)
            .del();

        // Mark the competence for rebuild
        await globalThis.orm
            .repo("competence", this.connection)
            .markForRebuild(competenceId);
    }

    /**
     *
     * @param {Array<number>} userIds
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async setUsers(userIds: number[] = [], competenceId: number) {
        // Remove current Users
        await this.deleteUsers(competenceId);
        // Add new Users
        await this.addUsers(userIds, competenceId);
    }

    /**
     *
     * @param {number} competenceId
     * @returns {Promise<*>}
     */
    async getUsers(competenceId) {
        return await this.connection
            .pluck("USER_ID")
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId);
    }

    async getActiveUsers(competenceId) {
        const allCompUserIds = await this.getUsers(competenceId);
        return await this.connection
            .pluck("USER_ID")
            .from("USERS")
            .where("USER_STATUS", "A")
            .whereIn(
                "USER_ID",
                Array.isArray(allCompUserIds)
                    ? allCompUserIds
                    : [allCompUserIds],
            );
    }

    async getForCompetence(competenceId) {
        return await this.connection
            .select([
                "CU.USER_ID",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
            ])
            .from(`${this.tableName} as CU`)
            .innerJoin("USERS as U", "U.USER_ID", "CU.USER_ID")
            .where("CU.COMPETENCE_ID", competenceId);
    }

    /**
     *
     * @param {number} userId
     */
    deleteForUser(userId) {
        return this.connection
            .from(this.tableName)
            .where("USER_ID", userId)
            .delete();
    }

    /**
     *
     * @param {number} userId
     * @param {array} columns
     * @param {boolean} source 'ldap', 'local' or null for all
     */
    getForUser(
        userId,
        columns = ["COMPETENCE_ID"],
        source: null | string = null,
    ) {
        let competences = this.connection
            .select(columns)
            .from(this.tableName)
            .where("USER_ID", userId);

        if (source === "local") {
            competences = competences.whereNull("COMPETENCE_SOURCE");
        } else if (source === "ldap") {
            competences = competences.where(
                "COMPETENCE_SOURCE",
                competenceConsts.COMPETENCE_SOURCE_LDAP,
            );
        }

        return competences;
    }

    /**
     * Deletes and then clones Competences of the given Users
     *
     * @param sourceUserId
     * @param targetUserId
     * @returns {Promise<void>}
     */
    async cloneCompetences(sourceUserId, targetUserId) {
        // Delete Competences for the target User for exact match (and no insert conflicts)
        await this.deleteForUser(targetUserId);

        // Insert from Select (clone)
        await this.connection.table("COMPETENCE_USERS").insert((builder) => {
            builder
                .select([
                    "COMPETENCE_ID",
                    globalThis.database.raw("?", [targetUserId]), // USER_ID
                    "COMPETENCE_RULE_ID",
                    globalThis.database.raw("NULL"), // COMPETENCE_SOURCE
                ])
                .from(
                    this.getForUser(
                        sourceUserId,
                        ["COMPETENCE_ID", "COMPETENCE_RULE_ID"],
                        "local",
                    ).as("SOURCE_COMPETENCES"),
                );
        });

        // Rebuild affected Competences
        const competenceRepo = globalThis.orm.repo(
            "competence",
            this.connection,
        );
        await competenceRepo.markForRebuild(this.getForUser(sourceUserId));
    }
}
