// @ts-nocheck
// @ts-nocheck
import { ArchivedProcessRepository } from "./ArchivedProcessRepository";

export class mssql_ArchivedProcessRepository extends ArchivedProcessRepository {
    // Includes DIRECT_USER_PROCESSES, not only their SubProcesses
    withDirectSubProcesses() {
        return `
            select * from ARCH_INSTANCE_PROCESSES IP
            where IP.IPROC_ID in (select IPROC_ID from DIRECT_USER_PROCESSES)
                UNION ALL
            select SUB_PROCESSES_2.*
            from ARCH_INSTANCE_PROCESSES as SUB_PROCESSES_2, DIRECT_SUBPROCESSES
            where DIRECT_SUBPROCESSES.IPROC_ID = SUB_PROCESSES_2.IPROC_MAIN_IPROC_ID
        `;
    }

    withSubProcesses() {
        return `
        select IPROC_ID, IPROC_MAIN_IPROC_ID, IPROC_DMS_VISIBILITY from ARCH_INSTANCE_PROCESSES IP
        where IP.IPROC_ID = :IPROC_ID
            UNION ALL
        select SUB_PROCESSES_2.IPROC_ID, SUB_PROCESSES_2.IPROC_MAIN_IPROC_ID, SUB_PROCESSES_2.IPROC_DMS_VISIBILITY
        from ARCH_INSTANCE_PROCESSES as SUB_PROCESSES_2, SUB_PROCESSES
        where SUB_PROCESSES.IPROC_ID = SUB_PROCESSES_2.IPROC_MAIN_IPROC_ID
        `;
    }

    getListMainProcess(iproc_id) {
        // TODO: Implement this for array? See parent
        // if (iproc_id && Array.isArray(iproc_id)) {
        //     repo = this.connection.raw(``
        //
        //     )
        // }

        return this.connection
            .raw(
                `
            WITH "n"("IPROC_ID", "IPROC_MAIN_IPROC_ID") AS
            (SELECT "IPROC_ID", "IPROC_MAIN_IPROC_ID"
            FROM "ARCH_INSTANCE_PROCESSES" as "IP"
            WHERE "IPROC_ID" = :IPROC_ID
              UNION ALL
            SELECT "nPlus1"."IPROC_ID", "nPlus1"."IPROC_MAIN_IPROC_ID"
            FROM "ARCH_INSTANCE_PROCESSES" as "nPlus1", "n"
            WHERE "n"."IPROC_MAIN_IPROC_ID" = "nPlus1"."IPROC_ID")
        
            SELECT * from "n"
            where "IPROC_ID" <> :IPROC_ID
        `,
                { IPROC_ID: iproc_id },
            )
            .then((data) => {
                const ids = [];
                if (data && Array.isArray(data)) {
                    data.forEach((process) => {
                        ids.push(process.IPROC_ID);
                    });
                }
                return ids;
            });
    }

    withMainProcesses() {
        return `
        select IPROC_ID, IPROC_MAIN_IPROC_ID, IPROC_DMS_VISIBILITY from ARCH_INSTANCE_PROCESSES IP
        where IP.IPROC_ID = :IPROC_ID
            UNION ALL
        select MAIN_PROCESSES_2.IPROC_ID, MAIN_PROCESSES_2.IPROC_MAIN_IPROC_ID, MAIN_PROCESSES_2.IPROC_DMS_VISIBILITY
        from ARCH_INSTANCE_PROCESSES as MAIN_PROCESSES_2, MAIN_PROCESSES
        where MAIN_PROCESSES.IPROC_MAIN_IPROC_ID = MAIN_PROCESSES_2.IPROC_ID
        `;
    }
}
