// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { DynamicTableValue } from "../entity/DynamicTableValue";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../services/UtilsService";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { convertToCsv } from "../../utils/csvConverter";
import { BaseCollection } from "../BaseCollection";

export class DynamicTableValueRepository extends BaseRepository<DynamicTableValue> {
    meta() {
        return {
            tableName: "DYNAMIC_TABLE_VALUES",
            entityName: "DynamicTableValue",
            sequenceName: "none",
            entity: () => new DynamicTableValue(),
        };
    }

    /**
     * Create DYNAMIC_TABLE_VALUES_{DT_ID}. Table without columns.
     * @param {integer} dtId
     * @param {array} cols array of col id
     * @returns {Promise<void>}
     */
    async create(dtId) {
        // TODO create view ?
        await this.createSeq(dtId);
    }

    /**
     * Append db column to DYNAMIC_TABLE_VALUES_{DT_ID}
     * @param dtId
     * @param colId
     * @returns {Promise<void>}
     */
    async createCol() {
        // TODO alter view ?
    }

    /**
     * Delete columns from table.
     * @param dtId
     * @param dtcId
     * @returns {Promise<*>}
     */
    async deleteCols(dtId, dtcId) {
        const cols = Array.isArray(dtcId) ? dtcId : [dtcId];
        return await Promise.all(cols.map((col) => this.deleteCol(dtId, col)));
    }

    /**
     * Delete column from table.
     * @param {Number} dtId
     * @param {Number} dtcId
     * @returns {Promise<*>}
     */
    async deleteCol(dtId, dtcId) {
        // TODO alter view ?

        // Get not empty clob columns
        const clobColData = await this.connection
            .select(`CLOB_COL_${dtcId}`)
            .from(this.tableName)
            .where("DT_ID", dtId)
            .whereNotNull(`CLOB_COL_${dtcId}`);

        const clobRepo = globalThis.orm.repo("clob", this.connection);

        // delete clob records
        for (const item of clobColData) {
            await clobRepo.delete(
                clobRepo.getEntity({
                    CLOB_ID: item[`CLOB_COL_${dtcId}`],
                }),
            );
        }

        // Reset data
        await this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", dtId)
            .update({
                [`COL_${dtcId}`]: null,
                [`CLOB_COL_${dtcId}`]: null,
            });
    }

    /**
     * @param {number} dtId
     * @param {string} dtvIndex
     * @returns {Promise<void>}
     */
    async removeRow(dtId, dtvIndex) {
        return await this.removeWhereValue(dtId, dtvIndex);
    }

    /**
     * Remove row with index.
     * @param dtId
     * @param value
     * @param {string} byColumn Default DTV_INDEX
     * @returns {Promise<void>}
     */
    async removeWhereValue(dtId, value, byColumn = "DTV_INDEX") {
        if (byColumn !== "DTV_INDEX" && !/^COL_\d{0,}$/.test(byColumn)) {
            throw new InternalException(`Invalid col name ${byColumn}.`);
        }

        // Find all rows to be removed.
        const rows = await this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", dtId)
            .where(byColumn, `${value}`);

        // Find clobs and remove its value
        for (let i = 0; i < rows.length; i += 1) {
            const cols = Object.keys(rows[i]);
            for (let c = 0; c < cols.length; c += 1) {
                if (/^CLOB_COL_\d{0,}$/.test(cols[c]) && rows[i][cols[c]]) {
                    // is Clob col and is not empty
                    const clobId = rows[i][cols[c]];
                    const clobRepo = globalThis.orm.repo(
                        "Clob",
                        this.connection,
                    );
                    await clobRepo.delete(
                        clobRepo.getEntity({
                            CLOB_ID: clobId,
                        }),
                    );
                }
            }
        }

        // Finally remove values from dt
        await this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", dtId)
            .where(byColumn, `${value}`)
            .delete();
    }

    /**
     * Remap COL_NAMES into COL_{n}. Index, ID -> DTV_INDEX.
     * @param {number} dtId
     * @param {array} rows
     * @returns {Promise<[]>}
     */
    async csvToDbEntity(dtId, rows) {
        const dtcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        const headers = Object.keys(rows[0]);
        const colsMap = await dtcRepo.mapCols(dtId);
        const entites = [];
        for (let i = 0; i < rows.length; i += 1) {
            const entity = {};
            for (let h = 0; h < headers.length; h += 1) {
                // Remap csv col names to COL_{n}
                let dbColName = headers[h];
                if (["ID", "Index"].indexOf(dbColName) !== -1) {
                    dbColName = "DTV_INDEX";
                }
                if (!/^COL_\d+$/.test(headers[h]) && colsMap[headers[h]]) {
                    dbColName = colsMap[headers[h]];
                }

                entity[dbColName] = rows[i][headers[h]];
            }
            entity.DT_ID = dtId;

            entites.push(await this.clobify(entity));
        }
        return entites;
    }

    /**
     * Append rows to DT.
     * @param {number} dtId
     * @param {} rows
     * @returns {Promise<>}
     */
    async appendRows(dtId, rows) {
        const toInsert = rows && !Array.isArray(rows) ? [rows] : rows;
        if (toInsert.length === 0) {
            return dtId;
        }

        let orderIndex = (await this.getMaxOrderIndex(dtId)) + 1;

        // Prepare order index
        for (let i = 0; i < toInsert.length; i += 1) {
            if (!toInsert[i].DTV_ORDER_INDEX) {
                toInsert[i].DTV_ORDER_INDEX = orderIndex;
                orderIndex += 1;
            }
        }

        // Prepare inserts. Add order index, map db cols.
        const dbEntities = await this.csvToDbEntity(dtId, toInsert);

        await globalThis.database
            .batchInsert(this.tableName, dbEntities, 10)
            .transacting(this.connection);

        return dtId;
    }

    async updateRows(dtId, rows, byCol = "DTV_INDEX") {
        const toUpdate = rows && !Array.isArray(rows) ? [rows] : rows;
        if (toUpdate.length === 0) {
            return dtId;
        }

        // TODO batch update! But be careful about different values ...
        const dbEntities = await this.csvToDbEntity(dtId, toUpdate);
        for (let i = 0; i < dbEntities.length; i += 1) {
            await this.connection
                .select()
                .from(this.tableName)
                .where("DT_ID", dtId)
                .where(byCol, dbEntities[i][byCol])
                .update(dbEntities[i]);
        }

        return dtId;
    }

    /**
     * @param dtId
     * @param data knex update JSON { column: newValue, ... }
     * @param wheres based on DynamicTableApi wheres
     * @returns {dtId}
     */
    async updateRowsColumn(dtId, data, wheres) {
        await this.connection(this.tableName)
            .where("DT_ID", dtId)
            .where((builder) => {
                UtilsService.prepareWheres(builder, wheres);
            })
            .update(data);

        return dtId;
    }

    async updateByCol() {
        throw new Error("TODO");
    }

    /**
     * Store large values in CLOB table instead.
     * @param entity
     * @returns {*}
     */
    async clobify(entity) {
        const attrs = Object.keys(entity);
        for (let i = 0; i < attrs.length; i += 1) {
            const attr = attrs[i];
            if (/^COL_\d+$/.test(attr)) {
                // COL_1, .., COL_{n}
                const byteLength = UtilsService.byteLength(entity[attr]);
                if (byteLength >= 4000) {
                    const clob = entity[attr];
                    const clobRepo = globalThis.orm.repo(
                        "Clob",
                        this.connection,
                    );
                    entity[`CLOB_${attr}`] = await clobRepo.store(
                        clobRepo.getEntity({
                            CLOB_VALUE: clob,
                        }),
                    );
                    const diff = byteLength - entity[attr].length;
                    entity[attr] = entity[attr].substring(0, 3950 - diff);
                }
            }
        }
        return entity;
    }

    /**
     *
     * @param {number} dtId DT id
     * @param {Array} indexes array of indexes
     */
    async deleteRows(dtId, indexes) {
        indexes.forEach((part, index) => {
            indexes[index] = `${part}`;
        });

        const dllIndexesChunks = _.chunk(indexes, 500);
        for (const chunk of dllIndexesChunks) {
            await this.connection
                .select()
                .from(this.tableName)
                .where("DT_ID", dtId)
                .whereIn("DTV_INDEX", Array.isArray(chunk) ? chunk : [chunk])
                .delete();
        }
    }

    async addRow(dtId, data) {
        const row = _.clone(data);
        row.DTV_ORDER_INDEX = (await this.getMaxOrderIndex(dtId)) + 1;
        row.DT_ID = dtId;
        const toInsert = await this.clobify(row);
        await this.connection(this.tableName).insert(toInsert);
        if (data.DTV_ORDER_INDEX) {
            await this.moveRow(dtId, row.DTV_INDEX, data.DTV_ORDER_INDEX);
        }
    }

    /**
     * Merge csv.
     * @param {string} dt DT name
     * @param {string|array} csv DT data
     * @param {string} delimiter csv Delimiter
     * @param {boolean} firstLineColNames
     * @param {string} isPublic Table is public and downloadable for anybody
     * @param {boolean} deleteMissing Delete rows in DT but not in CSV.
     * @param fileName
     * @param {string} mergeBy csv col name or db col name or DTV_INDEX or ID or Index
     * @returns {Promise<*>}
     */
    async mergeCsv(
        dt,
        csv,
        delimiter,
        firstLineColNames,
        isPublic,
        deleteMissing = true,
        fileName,
        mergeBy = "DTV_INDEX",
    ) {
        const sanitizeMergeByDb = async (_dtId, mergeColumn) => {
            if (["ID", "Index", "DTV_INDEX"].indexOf(mergeColumn) !== -1) {
                return "DTV_INDEX";
            }

            if (/^COL_\d+$/.test(mergeColumn)) {
                return mergeColumn;
            }
            const dtcRepo = globalThis.orm.repo(
                "DynamicTableCol",
                this.connection,
            );
            const colMap = await dtcRepo.mapCols(dt);
            return colMap[mergeColumn];
        };
        const sanitizeMergeByCsv = async (_dtId, mergeColumn, data) => {
            if (["ID", "Index", "DTV_INDEX"].indexOf(mergeColumn) !== -1) {
                return typeof data[0].ID !== "undefined" ? "ID" : "Index";
            }
            if (!/^COL_\d+$/.test(mergeColumn)) {
                return mergeColumn;
            }
            const dtcRepo = globalThis.orm.repo(
                "DynamicTableCol",
                this.connection,
            );
            const colMap = await dtcRepo.mapCols(dt);
            return colMap[mergeColumn];
        };

        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dtcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        const dtId = await dtRepo.createIfNotExists(dt, isPublic);

        // Prepare data
        const csvData = await this.fromCsv(csv, delimiter);
        const dbData = await (await this.getValues(dtId)).collectAll();
        const csvMergeBy = await sanitizeMergeByCsv(dtId, mergeBy, csvData); // Some column name in csv
        const dbMergeBy = await sanitizeMergeByDb(dtId, mergeBy); // DTV_INDEX, COL_1, COL_2

        if (!Array.isArray(csvData) || csvData.length === 0) {
            throw new UserException("No data in csv.");
        }

        // Prepare indexes.
        const csvIndexes = _.map(csvData, csvMergeBy);
        const dtIndexes = _.map(dbData, dbMergeBy);

        // @t3b-2104 Pri prepsani DT zachovat pripadne preruseni ciselne sekvence
        const originalColumnsColl = await dtcRepo.getForTable(dtId);
        originalColumnsColl.knex.pluck("DTC_ID");
        const originalColumns = await originalColumnsColl.fetchAll();

        // Append missing columns
        const headers = Object.keys(csvData[0]);
        await dtcRepo.appendCols(dtId, headers, originalColumns);

        // No data in dt, nothing to merge or new column means new import.
        if (dtIndexes.length === 0) {
            return await this.importCsv(
                dtId,
                csvData,
                delimiter,
                false,
                firstLineColNames,
            );
        }

        // Import missing rows
        const missingIndexes = _.difference(csvIndexes, dtIndexes);
        const missingRows = _.filter(
            csvData,
            (o) => missingIndexes.indexOf(o[csvMergeBy]) !== -1,
        );
        await this.appendRows(
            dtId,
            await this.assignIndexes(dtId, missingRows),
        );

        // Delete in addition rows
        let toRemoveCount = 0;
        if (deleteMissing) {
            const toRemove = _.difference(dtIndexes, csvIndexes);
            toRemoveCount = toRemove.length;
            await this.deleteRows(dtId, toRemove);
        }
        // Update changed
        const same = _.intersection(dtIndexes, csvIndexes);
        let toUpdateCount = 0;
        if (same.length > 0) {
            // Find changed rows.
            const colsMap = await dtcRepo.mapCols(dtId);
            const toUpdate = [];
            for (let i = 0; i < same.length; i += 1) {
                const index = same[i];
                const dbRow = _.find(dbData, [dbMergeBy, index]);
                const csvRow = _.find(csvData, [csvMergeBy, index]);

                // Convert csv row to db entity
                const csvDbEntity = {
                    DTV_INDEX: dbRow.DTV_INDEX,
                };
                const keys = Object.keys(csvRow);
                for (let k = 0; k < keys.length; k += 1) {
                    const key = keys[k];
                    // if (key === csvMergeBy) {
                    //     continue;
                    // }
                    if (key === "ID" || key === "Index") {
                        csvDbEntity.DTV_INDEX = csvRow[key];
                    } else {
                        csvDbEntity[colsMap[key]] = csvRow[key];
                    }
                }

                const isSame = (r1, r2) => {
                    const r2Keys = Object.keys(r2);
                    for (let k = 0; k < r2Keys.length; k += 1) {
                        const r1Val =
                            r1[r2Keys[k]] === null ? "" : r1[r2Keys[k]];
                        const r2Val =
                            r2[r2Keys[k]] === null ? "" : r2[r2Keys[k]];
                        if (r1Val != r2Val) {
                            // Do not change !
                            return false;
                        }
                    }
                    return true;
                };

                if (!isSame(dbRow, csvDbEntity)) {
                    toUpdate.push(csvDbEntity);
                    toUpdateCount += 1;
                }
            }
            // Update db
            await this.updateRows(dtId, toUpdate, dbMergeBy);
        }

        const targetTable = await dtRepo.get(dtId);

        const dtEntity = dtRepo.getEntity(targetTable);
        dtEntity.history = fileName;
        await dtRepo.store(dtEntity);

        globalThis.tasLogger.info(`Merging DynamicTable(${dtId}).`, {
            inserted: missingRows.length,
            updated: toUpdateCount,
            deleted: toRemoveCount,
        });

        return dtId;
    }

    async assignIndexes(dtId, data) {
        let firstIndex;
        let nextId;

        // Assign DTV_INDEX if undefined.
        for (let i = 0; i < data.length; i += 1) {
            if (typeof data[i].ID !== "undefined") {
                data[i].DTV_INDEX = data[i].ID;
            }
            if (typeof data[i].Index !== "undefined") {
                data[i].DTV_INDEX = data[i].Index;
            }

            if (typeof data[i].DTV_INDEX === "undefined") {
                if (!firstIndex) {
                    firstIndex = await this.getNextIndex(dtId);
                    nextId = firstIndex;
                }
                data[i].DTV_INDEX = nextId;
                nextId += 1;
            }
        }

        if (firstIndex !== nextId) {
            await this.setSeq(dtId, Number(nextId));
        }

        return data;
    }

    async importCsv(
        dtId,
        data,
        delimiter = ",",
        append = false,
        firstLineColNames = true,
        fileName = "UNKNOWN_FILE",
    ) {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dtvRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );
        const dtcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        const targetTable = await dtRepo.get(dtId);

        // Prepare data
        const csvData = await this.fromCsv(data, delimiter, firstLineColNames);
        if (!Array.isArray(csvData) || csvData.length === 0) {
            throw new UserException("No data in csv.");
        }

        // Prepare headers. If no heareds in csv then field1, field2, .. used
        const headers = Object.keys(csvData[0]);

        const containDTVindex =
            headers.indexOf("ID") !== -1 || headers.indexOf("Index") !== -1;

        // validation for duplicates values in DTV_INDEX column
        if (containDTVindex) {
            const indexArray = csvData.map(
                (oneRow) => oneRow.ID || oneRow.Index,
            );
            if (new Set(indexArray).size !== indexArray.length) {
                throw new UserException(
                    "Your ID column contains duplicates values. Check the data before import.",
                );
            }
        }

        // @t3b-2104 Pri prepsani DT zachovat pripadne preruseni ciselne sekvence
        const originalColumnsColl = await dtcRepo.getForTable(dtId);
        originalColumnsColl.knex.pluck("DTC_ID");
        const originalColumns = await originalColumnsColl.fetchAll();

        if (!append) {
            // Remove rows only.
            await dtvRepo.clearTable(dtId);
            await dtcRepo.clearTable(dtId);
        }
        // Append missing columns
        await dtcRepo.appendCols(targetTable, headers, originalColumns);

        // No ID nor Index in csv, need to generate DTV_INDEX
        let maxDlvIndex;
        if (!containDTVindex) {
            maxDlvIndex = await this.getNextIndex(targetTable.DT_ID);
            headers.push("ID");
            for (let i = 0; i < csvData.length; i += 1) {
                maxDlvIndex += 1;
                csvData[i].ID = `${maxDlvIndex}`;
            }
        } else {
            maxDlvIndex = Math.max.apply(
                Math,
                csvData.map((o) => o.Index || o.ID),
            );
        }

        if (UtilsService.isNumericString(maxDlvIndex)) {
            await this.setSeq(dtId, Number(maxDlvIndex) + 1);
        }

        // Insert into db
        await this.appendRows(targetTable.DT_ID, csvData);

        // @t3b-1568 Nová položka k DT - zdroj
        const dtEntity = dtRepo.getEntity(targetTable);
        dtEntity.history = fileName;
        await dtRepo.store(dtEntity);

        return targetTable.DT_ID;
    }

    /**
     * Set row position.
     * @param {number} dtId
     * @param {string} dtvIndex
     * @param {number} dtvOrderIndex
     */
    async moveRow(dtId, dtvIndex, dtvOrderIndex) {
        if (!UtilsService.isNumericString(dtvOrderIndex)) {
            throw new InternalException("dtvOrderIndex is not number.");
        }

        await this.connection.raw(
            `UPDATE "${this.tableName}" SET "DTV_ORDER_INDEX" = "DTV_ORDER_INDEX" + 1 WHERE "DT_ID" = ? AND "DTV_ORDER_INDEX" >= ?`,
            [dtId, dtvOrderIndex],
        );
        await this.connection
            .select("DTV_ORDER_INDEX")
            .from(this.tableName)
            .where("DT_ID", dtId)
            .where("DTV_INDEX", `${dtvIndex}`)
            .update({
                DTV_ORDER_INDEX: Number(dtvOrderIndex),
            });
    }

    /**
     * Get table values.
     * @param dtId
     * @param {string|array} cols
     * @returns {BaseCollection}
     */
    async getValues(
        dtId,
        cols: string | string[] | null = null,
        distinct = false,
    ): Promise<BaseCollection<DynamicTableValue>> {
        // get only DT columns.
        if (
            !cols ||
            (Array.isArray(cols) && cols.length === 0) ||
            cols == "*"
        ) {
            cols = await globalThis.orm
                .repo("DynamicTableCol", this.connection)
                .getDbColNames(dtId);
        }

        // Extend columns with clobs
        let extendedCols;
        let distinctCol = null;
        if (Array.isArray(cols)) {
            extendedCols = ["DTV_INDEX", "DTV_ORDER_INDEX"];
            for (let i = 0; i < cols.length; i += 1) {
                const col = (
                    UtilsService.isNumericString(cols[i])
                        ? `COL_${cols[i]}`
                        : cols[i]
                ).toUpperCase(); // COL_{n}
                if (
                    /^COL_\d+$/.test(col) &&
                    cols.indexOf(`CLOB_${col}`) === -1
                ) {
                    extendedCols.push(`CLOB_${col}`);
                    extendedCols.push(col);
                    distinctCol = col;
                }
            }
        }

        // Distinct only one column.
        let connection;
        if (
            distinct &&
            distinctCol &&
            Array.isArray(cols) &&
            cols.length === 1 &&
            cols[0] !== "*"
        ) {
            connection = this.connection
                .select([
                    globalThis.database.raw(`min("DTV_INDEX") as "DTV_INDEX"`),
                    distinctCol,
                ])
                .groupBy(distinctCol)
                .from(this.tableName)
                .where("DT_ID", dtId);
        } else {
            connection = this.connection
                .select(extendedCols || cols)
                .from(this.tableName)
                .where("DT_ID", dtId);
        }
        return globalThis.orm.collection("DynamicTableValue", connection);
    }

    /**
     * Get current maximum order index.
     * @param dtId
     * @returns {Promise<number|*>}
     */
    async getMaxOrderIndex(dtId) {
        const rows = await globalThis.container.client.database.callKnexRaw(
            `SELECT MAX("DTV_ORDER_INDEX") as "NEXT_ORDER_INDEX" FROM "${this.tableName}" where "DT_ID" = ?`,
            [dtId],
            this.connection,
        );
        return rows.length === 0 || !rows[0].NEXT_ORDER_INDEX
            ? 0
            : rows[0].NEXT_ORDER_INDEX;
    }

    /**
     * Get current maximum index or 0 if not number.
     * @param dtId
     * @returns {Promise<number>}
     */
    async getNextIndex(dtId) {
        return await this.genID(this.getSeqName(dtId));
    }

    /**
     * Set sequence to specified number.
     * @param dtId
     * @param num
     * @returns {Promise<void>}
     */
    async setSeq(dtId, num) {
        if (!UtilsService.isNumericString(num)) {
            throw new InternalException(
                `Invalid argument for setSeq. Number must be integer but is ${num}`,
            );
        }

        await this.createSeq(dtId, num);
    }

    async dropSeq(dtId) {
        try {
            await globalThis.container.client.database.callKnexRaw(
                `DROP SEQUENCE IF EXISTS "${this.getSeqName(dtId)}"`,
            );
        } catch (err) {
            globalThis.tasLogger.log("Error on dropSeq", err);
        }
    }

    async createSeq(dtId, start = 1) {
        if (!UtilsService.isNumericString(start)) {
            throw new InternalException(
                `Can not start ${this.getSeqName(dtId)} sequence with number ${start}`,
            );
        }

        let startWith = Number(start);
        startWith =
            Math.round(startWith) !== startWith
                ? Math.round(startWith + 0.5)
                : startWith;

        await this.dropSeq(dtId);
        if (globalThis.dynamicConfig.db.client === "mssql") {
            await globalThis.container.client.database.callKnexRaw(
                `CREATE SEQUENCE "${this.getSeqName(dtId)}" START WITH ${startWith} INCREMENT BY 1`,
            );
        } else {
            await globalThis.container.client.database.callKnexRaw(
                `CREATE SEQUENCE "${this.getSeqName(dtId)}" START WITH ${startWith} MINVALUE 0;`,
            );
        }
    }

    /**
     * Import DT from csv.
     * @param csv
     * @param delimiter
     * @param firstLineColNames
     * @returns {Promise}
     */
    async fromCsv(csv, delimiter = ",", firstLineColNames = true) {
        if (Array.isArray(csv)) {
            return csv;
        }
        return await convertToCsv(csv, {
            trim: false,
            checkType: false,
            delimiter,
            noheader: !firstLineColNames,
        });
    }

    /**
     * Add new row id.
     * @param dtId
     * @returns {*}
     */
    getNewRowID(dtId) {
        return this.addEmptyRow(dtId, null);
    }

    /**
     * Add empty row. Deprecated, use addRow instead.
     * @param {number} dtId
     * @param {string|null} index
     * @param data
     * @returns {*}
     */
    async addEmptyRow(dtId, index: any = null, data: any = null) {
        if (typeof index === "undefined") {
            throw new InternalException("index must be number string or null");
        }

        let nextSeqIndex;
        // Alter sequence if index is out of current sequence.
        if (UtilsService.isNumericString(index) || index === null) {
            nextSeqIndex = await this.getNextIndex(dtId);

            if (index !== null && Number(index) > nextSeqIndex) {
                await this.setSeq(dtId, Number(index) + 1);
            }
        }

        const maxOrderIndex = await this.getMaxOrderIndex(dtId);
        const dtvIndex = index || nextSeqIndex;

        await this.connection(this.tableName)
            .insert({
                ...data,
                DT_ID: dtId,
                DTV_INDEX: dtvIndex,
                DTV_ORDER_INDEX: maxOrderIndex + 1,
            })
            .catch(async (err) => {
                globalThis.tasLogger.error("Error on addEmptyRow.", {
                    dtvIndex,
                });
                throw err;
            });
        return { NEXT_INDEX: dtvIndex };
    }

    async getForVariable(ivarId) {
        const variable = await globalThis.orm
            .repo("variable", this.connection)
            .get(ivarId, ["DLIST_NAME", "IVAR_COL_INDEX"]);
        const table = await globalThis.orm
            .repo("dynamicTable", this.connection)
            .get(variable.DLIST_NAME);
        if (!table) {
            throw new InternalException(
                `DynamicTable ${variable.DLIST_NAME} not exists.`,
            );
        }
        const collection = await this.getValues(table.DT_ID, [
            `COL_${Number(variable.IVAR_COL_INDEX)}`,
        ]);
        return collection;
    }

    async getForVariableColumn(ivarId, col) {
        const variable = await globalThis.orm
            .repo("variable", this.connection)
            .get(ivarId, ["DLIST_NAME"]);
        const table = await globalThis.orm
            .repo("dynamicTable", this.connection)
            .get(variable.DLIST_NAME);
        if (!table) {
            throw new InternalException(
                `DynamicTable ${variable.DLIST_NAME} not exists.`,
            );
        }

        const collection = await this.getValues(table.DT_ID, [`COL_${col}`]);
        return collection;
    }

    /**
     * Count rows.
     * @param dtId
     * @returns {Promise<void>}
     */
    async countRows(dtId) {
        const id = UtilsService.isNumericString(dtId)
            ? dtId
            : (
                  await globalThis.orm
                      .repo("DynamicTable", this.connection)
                      .get(dtId)
              ).DT_ID;

        const count = await this.connection
            .count("DTV_INDEX as CC")
            .where("DT_ID", id)
            .from(this.tableName);
        return Array.isArray(count) && count.length === 1 ? count[0].CC : 0;
    }

    /**
     * Get sequence name.
     * @param {number} dtId
     * @returns {string} seq name
     */
    getSeqName(dtId) {
        if (!UtilsService.isNumericString(dtId)) {
            throw new InternalException("dtId is not number.");
        }
        return `DTV_SEQ_${dtId}`;
    }

    /**
     * Clear table values.
     * @param dtId
     * @returns {Promise<*>}
     */
    async clearTable(dtId) {
        // Empty clobIds
        const clobIds = await this.getUsedClobIds(dtId);
        await globalThis.orm.repo("clob", this.connection).remove(clobIds);

        // Empty rows
        await this.connection
            .select("DTV_INDEX")
            .from(this.tableName)
            .where("DT_ID", dtId)
            .delete();

        // Reset sequence
        await this.setSeq(dtId, 0);
    }

    async getUsedClobIds(dtId) {
        const cols = await globalThis.orm
            .repo("DynamicTableCol", this.connection)
            .getDbColNames(dtId);

        if (!cols.length) {
            return [];
        }

        const conn = this.connection
            .select(_.map(cols, (col) => `CLOB_${col}`))
            .from(this.tableName)
            .where("DT_ID", dtId);

        conn.where(() => {
            for (const col of cols) {
                conn.orWhereNotNull(`CLOB_${col}`);
            }
        });

        const usedClobs = await conn;
        const clobIds = [];
        for (const row of usedClobs) {
            for (const col of cols) {
                if (row[`CLOB_${col}`] !== null) {
                    clobIds.push(row[`CLOB_${col}`]);
                }
            }
        }
        return clobIds;
    }

    async copyValues(fromId, toId) {
        const rows = await (await this.getValues(fromId)).collectAll();
        for (let i = 0; i < rows.length; i += 1) {
            delete rows[i].DLV_INDEX;
            delete rows[i].DTV_VALUE;
            rows[i].DT_ID = toId;
        }
        await this.appendRows(toId, rows);
    }

    async toCsv(dtId, delimiter = ";") {
        const dynTable = await globalThis.orm
            .repo("DynamicTable", this.connection)
            .getTable(dtId);
        const coll = await this.getValues(dynTable.DT_ID);
        coll.knex.orderBy("DTV_ORDER_INDEX");
        const rows = await coll.fetchAll();

        const cols = await globalThis.orm
            .repo("DynamicTableCol", this.connection)
            .getDbColNames(dynTable.DT_ID);
        cols.unshift("DTV_INDEX");

        return await UtilsService.json2csv(rows, {
            fields: cols,
            delimiter,
        });
    }

    async getRowIdFromValue(dlName, colIndex, rowValue) {
        if (!UtilsService.isNumericString(colIndex)) {
            throw new UserException(
                "colIndex in lib.getRowIdFromValue must be numeric",
            );
        }
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);

        const dynamicTable = await dtRepo.getTable(dlName);
        return await this.connection
            .select("DTV_INDEX")
            .from(this.tableName)
            .where("DT_ID", dynamicTable.DT_ID)
            .where(
                this.connection.raw(
                    globalThis.orm.db.upper(`COL_${colIndex}`, true),
                ),
                `${rowValue}`.toUpperCase(),
            )
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    return "";
                }
                return rows[0].DTV_INDEX;
            });
    }

    async getCell(dt, col, index) {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dynamicTable = await dtRepo.getTable(dt);

        const coll = await this.getValues(dynamicTable.DT_ID, [`COL_${col}`]);
        coll.knex.where("DTV_INDEX", `${index}`);

        const rows = await coll.collectAll();
        if (rows.length === 0) {
            return null;
        }
        return rows[0][`COL_${col}`];
    }

    async updateCell(dlName, colIndex, rowIndex, value) {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dynamicTable = await dtRepo.getTable(dlName);

        const obj = {
            DTV_INDEX: `${rowIndex}`,
        };
        obj[`COL_${colIndex}`] = value;
        await this.updateRows(dynamicTable.DT_ID, [obj]);
    }
}
