// @ts-nocheck
// @ts-nocheck
import { InstanceTaskLinkDone } from "../entity/InstanceTaskLinkDone";
import { BaseRepository } from "./BaseRepository";

export class InstanceTaskLinkDoneRepository extends BaseRepository<InstanceTaskLinkDone> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_LINK_DONE",
            entityName: "InstanceTaskLinkDone",
            entity: () => new InstanceTaskLinkDone(),
            archived: true,
        };
    }

    async resetTTask(ttaskId, iprocId) {
        globalThis.tasLogger.info("Resetting task links data.", {
            iproc_id: iprocId,
            ttask_id: ttaskId,
        });
        return await this.connection("INSTANCE_TASK_LINK_DONE")
            .whereIn(
                "INSTANCE_TASK_LINK_DONE.TTASKLINK_ID",
                this.connection
                    .select("TTASKLINK_ID")
                    .from("TEMPLATE_TASK_LINKS")
                    .where("TTASKLINK_TO_TTASK_ID", ttaskId),
            )
            .where("IPROC_ID", iprocId)
            .delete();
    }

    getCount(iprocId, ttasklinkId) {
        return this.connection
            .select("COUNT")
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("TTASKLINK_ID", ttasklinkId)
            .then((rows) => {
                if (Array.isArray(rows) && rows.length > 0) {
                    globalThis.tasLogger.info(rows);
                    return rows[0].COUNT;
                }
                return 0;
            });
    }

    setCount(iprocId, ttasklinkId, count) {
        return this.connection
            .select("IPROC_ID")
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("TTASKLINK_ID", ttasklinkId)
            .update({
                COUNT: count,
            });
    }
}
