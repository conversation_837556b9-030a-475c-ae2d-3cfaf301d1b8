// @ts-nocheck
// @ts-nocheck
import { TemplateTaskInvitation } from "../entity/TemplateTaskInvitation";
import { BaseRepository } from "./BaseRepository";

export class TemplateTaskInvitationRepository extends BaseRepository<TemplateTaskInvitation> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_INVITATIONS",
            entityName: "TemplateTaskInvitation",
            entity: () => new TemplateTaskInvitation(),
        };
    }

    getForTTask(ttaskId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId);
    }

    setTTaskInvitation(
        ttask_id,
        ttask_inv_attendees,
        ttask_inv_summary,
        ttask_inv_description,
        ttask_inv_dtstart,
        ttask_inv_dtend,
        ttask_inv_location,
        ttask_inv_class,
        ttask_inv_priority,
        ttask_inv_categories,
    ) {
        const { entity } = this;

        let anyChange = false;
        entity.TTASK_ID = ttask_id;
        if (
            typeof ttask_inv_dtstart !== "undefined" &&
            ttask_inv_dtstart != "Invalid Date"
        ) {
            entity.TTASK_INV_DTSTART = ttask_inv_dtstart;
            anyChange = true;
        }
        if (
            typeof ttask_inv_dtend !== "undefined" &&
            ttask_inv_dtend != "Invalid Date"
        ) {
            entity.TTASK_INV_DTEND = ttask_inv_dtend;
            anyChange = true;
        }
        if (typeof ttask_inv_location !== "undefined") {
            entity.TTASK_INV_LOCATION = ttask_inv_location;
            anyChange = true;
        }
        if (typeof ttask_inv_class !== "undefined") {
            entity.TTASK_INV_CLASS = ttask_inv_class;
            anyChange = true;
        }
        if (typeof ttask_inv_priority !== "undefined") {
            entity.TTASK_INV_PRIORITY = ttask_inv_priority;
            anyChange = true;
        }
        if (typeof ttask_inv_categories !== "undefined") {
            entity.TTASK_INV_CATEGORIES = ttask_inv_categories;
            anyChange = true;
        }
        if (typeof ttask_inv_attendees !== "undefined") {
            entity.TTASK_INV_ATTENDEES = ttask_inv_attendees;
            anyChange = true;
        }
        if (typeof ttask_inv_description !== "undefined") {
            entity.TTASK_INV_DESCRIPTION = ttask_inv_description;
            anyChange = true;
        }
        if (typeof ttask_inv_summary !== "undefined") {
            entity.TTASK_INV_SUMMARY = ttask_inv_summary;
            anyChange = true;
        }

        if (!anyChange) {
            return true;
        }

        return this.deleteForTTask(ttask_id).then(() =>
            this.connection(this.tableName)
                .insert(entity.getAttributes(true))
                .then((r) => r),
        );
    }

    deleteForTTask(ttaskId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId)
            .delete();
    }
}
