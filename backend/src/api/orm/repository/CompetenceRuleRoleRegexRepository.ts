// @ts-nocheck
import { CompetenceRuleRoleRegex } from "../entity/CompetenceRuleRoleRegex";
import { BaseRepository } from "./BaseRepository";

export class CompetenceRuleRoleRegexRepository extends BaseRepository<CompetenceRuleRoleRegex> {
    meta() {
        return {
            tableName: "COMPETENCE_RULE_ROLE_REGEX",
            entityName: "CompetenceRuleRoleRegex",
            entity: () => new CompetenceRuleRoleRegex(),
        };
    }

    /**
     * Assign competence roles from competence rules.
     * @param competenceId
     * @param competenceRuleId
     * @returns {Promise<void>}
     */
    async copyDefaultsForCompetence(competenceId, competenceRuleId) {
        const regexes = await this.connection
            .select(["REGEX_RULE_NAME", "REGEX_RULE_VALUE"])
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .whereNotIn("REGEX_RULE_VALUE", (b) => {
                b.select("REGEX_VALUE")
                    .from("COMPETENCE_ROLE_REGEX")
                    .where("COMPETENCE_ID", competenceId);
            });

        const competence = await globalThis.orm
            .repo("competence", this.connection)
            .get(competenceId);
        const competenceRule = await globalThis.orm
            .repo("competenceRule", this.connection)
            .get(competenceRuleId);

        const competenceRegexRepo = globalThis.orm.repo(
            "competenceRoleRegex",
            this.connection,
        );
        for (const ruleRegex of regexes) {
            let competenceRuleValue = ruleRegex.REGEX_RULE_VALUE;
            const originalCompetenceSource = (
                competence.COMPETENCE_SOURCE || ""
            ).replace(/^\d+:/, "");
            const match = new RegExp(competenceRule.COMPETENCE_RULE_REGEX).exec(
                originalCompetenceSource,
            );

            if (Array.isArray(match)) {
                for (let i = 1; i < match.length; i += 1) {
                    competenceRuleValue = competenceRuleValue.replace(
                        new RegExp(`\\$${i}`, "g"),
                        match[i],
                    );
                }
            }
            const ruleEntity = competenceRegexRepo.getEntity({
                COMPETENCE_ID: competenceId,
                REGEX_NAME: ruleRegex.REGEX_RULE_NAME,
                REGEX_VALUE: competenceRuleValue,
                COMPETENCE_RULE_ID: competenceRuleId,
            });
            await competenceRegexRepo.store(ruleEntity);
        }
    }

    getForCompetence(competenceId) {
        return this.connection
            .select(["REGEX_RULE_ID", "REGEX_RULE_NAME", "REGEX_RULE_VALUE"])
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceId);
    }

    regexPreview(regexId) {
        return this.connection
            .select([
                "ROLE_RULE_ID",
                "ROLE_RULE_NAME",
                "ROLE_RULE_CATEGORY",
                "ROLE_RULE_NOTE",
            ])
            .from("ROLES as R")
            .whereRaw(
                `"R"."ROLE_NAME" like (${this.connection
                    .select("CX.REGEX_RULE_VALUE")
                    .from("COMPETENCE_RULE_ROLE_REGEX as CX")
                    .where("CX.REGEX_RULE_ID", regexId)}) escape '^'`,
            );
    }

    async addRegexes(regexes = [], competenceRuleId) {
        // Add regexes
        for (const { REGEX_RULE_NAME, REGEX_RULE_VALUE } of regexes) {
            const entity = this.getEntity({
                COMPETENCE_RULE_ID: competenceRuleId,
                REGEX_RULE_NAME,
                REGEX_RULE_VALUE,
            });

            await this.store(entity);
        }
    }

    async deleteRegexes(competenceRuleId) {
        // Delete all for the Competence
        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .del();
    }

    async removeRegexes(regexIds) {
        // Delete all for the Competence
        await this.connection
            .from(this.tableName)
            .whereIn(
                "REGEX_RULE_ID",
                Array.isArray(regexIds) ? regexIds : [regexIds],
            )
            .del();
    }

    async setRegexes(regexes, competenceRuleId) {
        await this.deleteRegexes(competenceRuleId);
        await this.addRegexes(regexes, competenceRuleId);
    }
}
