import _ from "lodash";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../services/UtilsService";
import { BaseCollection } from "../BaseCollection";
import { OrmFactory, RepositoryInstances, RepositoryKey } from "../OrmFactory";
import { Knex } from "knex";
import { BaseEntity, EntityAttribute } from "../entity/BaseEntity";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export abstract class BaseRepository<T extends BaseEntity> {
    protected readonly _tableName: string;

    protected readonly _defaultAlias?: string;

    protected readonly _entityName: string;

    public readonly entityClass: () => T;

    protected readonly _sequenceName?: string;

    protected _relatedEntities?: BaseEntity[];

    protected _connection?: Knex;

    protected _entity?: T;

    public _lang?: string;

    public _archived?: boolean;

    protected readonly orm?: OrmFactory;

    constructor() {
        // set metadata
        const meta = this.meta();
        this._tableName = meta.tableName;
        this._defaultAlias = meta.defaultAlias;
        this._archived = meta.archived;
        this._entityName = meta.entityName;
        this.entityClass = meta.entity;
        this.orm = undefined; // holds single instnace of ormfactory

        try {
            this._sequenceName =
                meta.sequenceName || `${meta.entity().primaryColumn}_SEQ`;
        } catch (_ignored) {
            // has no primaryColumn, ignore this
        }

        this._connection = undefined;
        if (!this._entityName || !this._tableName) {
            throw new Error(
                "TableName or entityName of repository is not defined",
            );
        }
    }

    abstract meta(): {
        tableName: string;
        entityName: string;
        entity: () => T;
        defaultAlias?: string;
        sequenceName?: string;
        archived?: boolean;
        archParams?: {
            subQueryTable: string;
            subQueryColumn: string;
        };
    };

    async exists(attrName: string, attrValue: any): Promise<boolean> {
        const data = await this.connection
            .select(attrName)
            .from(this.tableName)
            .where(attrName, attrValue);

        if (Array.isArray(data) && data.length > 0) {
            return true;
        }
        return false;
    }

    async getId(attrName: string, attrValue: any): Promise<unknown | null> {
        const primaryKey = this.entity.primaryColumn;
        const data = await this.connection
            .select(primaryKey)
            .from(this.tableName)
            .where(attrName, attrValue);

        if (Array.isArray(data) && data.length > 0) {
            return data[0][primaryKey as string];
        }
        return null;
    }

    /**
     * Always returns the whole item (only first result) from database.
     */
    async get(
        id?: number | number[],
        columns?: string[] | string | ((item: any, key: any) => boolean),
        throwError: boolean = true,
    ): Promise<T> {
        const entity = this.entityClass();

        // filter columns, apply callback, (optional)
        if (columns) {
            if (typeof columns === "function") {
                columns = this.getEntity().getColumnNames(columns); // as filter callback
            }
        } else {
            columns = this.getEntity().getColumnNames();
        }

        /* const rawWhereClause =
                Array.isArray(entity.primaryColumn) && Array.isArray(id)
                    ? `${entity.primaryColumn[0]} = ${id[0]} AND ${entity.primaryColumn[1]} = ${id[1]}`
                    : `${entity.primaryColumn} = ${id}`; */
        const row = await this.connection
            .column(columns)
            .first()
            .from(this.tableName)
            /*  .whereRaw(rawWhereClause); */
            .where(this.primaryWhere(entity, id));

        if (typeof row === "undefined") {
            if (throwError) {
                throw new InternalException(
                    `Entity '${this._entityName}' with id '${id}' not found.`,
                    "ENTITY_NOT_FOUND",
                );
            }
        }

        const data = await this.castRow(row, entity.getAttributes(false, true)); // call parent(if implemented) or child

        const obj = this.entityClass();
        return obj.fill(data);
    }

    getAllByAttr(attrName: string, attrValue: any): Knex.QueryBuilder {
        return this.connection
            .select()
            .from(this.tableName)
            .where(attrName, attrValue);
    }

    async getByAttr(attrName: string, attrValue: any): Promise<T> {
        return await this.getAllByAttr(attrName, attrValue).first();
    }

    getByAttributes(
        attributeNames: string | string[],
        attributeValues: string | string[],
    ): Knex.QueryBuilder {
        const conn = this.connection.select().from(this.tableName).first();

        const wheres = _.zip(
            ([] as string[]).concat(attributeNames),
            ([] as string[]).concat(attributeValues),
        );

        wheres.forEach(([attributeName, attributeValue]) => {
            // @ts-expect-error dunno
            conn.where(attributeName, attributeValue);
        });

        return conn;
    }

    async getMulti(ids: number[] | number, columns?: any[]): Promise<T[]> {
        if (!Array.isArray(ids)) {
            ids = [ids];
        }

        const entity = this.entityClass();
        // filter columns, apply callback, (optional)
        if (columns) {
            if (typeof columns === "function") {
                columns = this.getEntity().getColumnNames(columns); // as filter callback
            }
        } else {
            columns = this.getEntity().getColumnNames();
        }

        if (ids.length > 1000) {
            globalThis.tasLogger.warning(
                "BaseRepository.getMulti - Binding over 1k values in inWhere clause. Potential perfomance issues.",
                { source: new Error().stack },
            );
        }
        // Prevent: ORA-01795: maximum number of expressions in a list is 1000
        const idsChunked = _.chunk(ids, 999);

        // Prevents situation when idsChunked is emtpy and retrieve all users
        if (!Array.isArray(idsChunked) || idsChunked.length === 0) {
            return [];
        }

        let conn = this.connection.select(columns).from(this.tableName);

        idsChunked.forEach((chunk) => {
            conn = conn.orWhereIn(
                // @ts-expect-error types
                entity.primaryColumn,
                globalThis.database.raw(chunk),
            );
        });

        return conn
            .then((rows) =>
                this.castRows(rows, entity.getAttributes(false, true)),
            )
            .then((rows) => {
                const out: T[] = [];
                rows.forEach((row) => {
                    const entity = this.entityClass();
                    entity.fill(row);
                    out.push(entity);
                });
                return out;
            });
    }

    assoc(data: any[], attr: string): Record<string, any> {
        if (!Array.isArray(data) || data.length === 0) {
            return data;
        }

        const out: Record<string, any> = {};
        data.forEach((row) => {
            if (!out[row[attr]]) {
                out[row[attr]] = [];
            }
            out[row[attr]].push(row);
        });

        return out;
    }

    getCollection(
        columns?: string[] | ((item: any, key: any) => boolean),
        alias?: string,
        extraColumns?: string[],
    ): BaseCollection<T> {
        return this.getBaseCollection(columns, alias, extraColumns);
    }

    getBaseCollection(
        columns?: string[] | ((item: any, key: any) => boolean),
        alias?: string,
        extraColumns?: string[],
    ): BaseCollection<T> {
        const coll = this.collection;

        // filter columns, apply callback, (optional)
        if (columns) {
            if (typeof columns === "function") {
                columns = this.getEntity().getColumnNames(columns, alias); // as filter callback
            }
        } else {
            // as default use entity columns
            columns = this.getEntity().getColumnNames(undefined, alias);
        }

        coll.knex.columns(
            extraColumns ? columns.concat(extraColumns) : columns,
        ); // always uses columns instead of *

        coll.knex.from(this.tableName + (alias ? ` AS ${alias}` : ""));

        if (!coll.filteringColumns) {
            coll.filteringColumns = {};
        }

        return coll;
    }

    createCollection(connection?: Knex.QueryBuilder): BaseCollection<T> {
        const collection = new BaseCollection(
            this,
            connection || this.connection.select(),
        ); // select() starts new query instance, takes connection from repository
        collection.setLocalization(this._lang);
        return collection;
    }

    async delete(entity: T): Promise<void> {
        await this.connection(this._tableName)
            .where(this.primaryWhere(entity, entity.id))
            .del();
    }

    /**
     * Dialect(adapter) specific function
     * Could be called separately.
     * @param row
     * @param attrs Attributes to convert.
     */
    async castRow(row: T, attrs: Record<string, EntityAttribute>): Promise<T> {
        await Promise.all(
            Object.keys(row).map(async (attName) => {
                // if not an attribute
                if (!attrs.hasOwnProperty(attName)) {
                    return;
                }

                const attType = attrs[attName].type;
                switch (attType) {
                    case "text": // knex type
                        return;
                    case "number":
                        if (
                            row[attName] == null ||
                            typeof row[attName] === "undefined" ||
                            isNaN(row[attName])
                        ) {
                            return;
                        }

                        /*
                         Floating point precision problem fix (e.g. 0.1 + 0.2 = 0.30000000000000004)
                         */
                        if (row[attName] % 1 != 0) {
                            // @ts-expect-error
                            row[attName] = parseFloat(
                                UtilsService.roundNumber(
                                    row[attName],
                                    globalThis.dynamicConfig
                                        .floatingPointPrecision,
                                ).toString(),
                            );
                        }

                        return;
                    case "dateTime": {
                        if (row[attName] == null) {
                            return;
                        }

                        const date = new Date(row[attName]);
                        if (date.toString() === "Invalid Date") {
                            throw new UserException(
                                `Wrong date format '${row[attName]}'. Use ISO 8601 (2011-10-05T14:48:00.000Z).`,
                            );
                        }
                        // @ts-expect-error
                        row[attName] = date;
                        return;
                    }
                    default:
                        return;
                }
            }),
        );
        return row;
    }

    castRows(
        rows: any[],
        attrs: Record<string, EntityAttribute>,
    ): Promise<T[]> {
        return Promise.all(rows.map((row) => this.castRow(row, attrs))).then(
            (data) => data,
        );
    }

    async storeMulti(
        entities: T[],
        ignoreEmpty: boolean = true,
    ): Promise<any[]> {
        if (
            !ignoreEmpty &&
            (!entities || !Array.isArray(entities) || entities.length === 0)
        ) {
            throw new InternalException("Nothing to update.");
        }

        const result: any[] = [];
        for (const entity of entities) {
            result.push(await this.store(entity));
        }
        return result;
    }

    async store(
        entity: T,
        _force: boolean = false,
    ): Promise<number | number[] | null> {
        await this.validate(entity);

        const dirtyAttributes = entity.getAttributes(true);
        entity.events.emit("beforeStore", entity, dirtyAttributes); // synchronous? / notice - pass only dirty attributes

        if (Object.keys(dirtyAttributes).length === 0) {
            return entity.id;
        }

        // UPDATE
        if (entity.id !== null && entity.id !== undefined && !entity.toInsert) {
            if (!_.isEmpty(dirtyAttributes)) {
                await this.connection(this._tableName)
                    .where(this.primaryWhere(entity, entity.id))
                    .update(dirtyAttributes);
            }
            // INSERT
        } else {
            let primaryColumn;
            try {
                primaryColumn = typeof this.getEntity().primaryColumn;
            } catch (_ignored) {
                // ignored
            }
            // inserting rows without primary key OR with multiple primary keys (or existing primary keys)
            if (typeof primaryColumn === "undefined" || entity.toInsert) {
                await this.connection(this._tableName).insert(dirtyAttributes);
                // inserting with generating id from sequence
            } else {
                const NID = await this.genID(this._sequenceName!);
                await this.connection(this._tableName).insert(
                    _.assign(dirtyAttributes, {
                        [entity.primaryColumn as string]: NID,
                    }),
                ); // Extending with primary ID for inserting
                entity.id = NID;
            }
        }

        return entity.id;
    }

    async alterSeqIncrement(seqName: string, increase: number): Promise<any> {
        return await globalThis.container.client.database.callKnexRaw(
            `ALTER SEQUENCE "${seqName}" INCREMENT BY ${increase <= 0 ? 1 : increase + 1}`,
        );
    }

    async genID(seqName: string): Promise<number> {
        try {
            return Number(
                (
                    await globalThis.container.client.database.callKnexRaw(
                        `select ${globalThis.orm.db.sequence(seqName)} as "nid" ${globalThis.orm.db.fromDual()}`,
                    )
                )[0].nid,
            );
        } catch (e) {
            throw e;
        }
    }

    async autoGeneratePrimary(entity: T): Promise<void> {
        const primary = entity.primaryColumn;

        // Multiple primary keys, fill all missing.
        if (Array.isArray(primary)) {
            const attrs = entity.attributes();
            for (const key of primary) {
                if (attrs[key].seq && !entity[key]) {
                    // @ts-expect-error ts(2862)
                    entity[key] = await this.genID(attrs[key].seq!);
                }
            }
            return;
        }

        // Single primary key, generate primary.
        if (!entity.id) {
            // Only if missin
            entity.id = await this.genID(this._sequenceName!);
        }
    }

    async create(entity: T, force?: boolean): Promise<any> {
        force = typeof force !== "undefined" ? force : false;
        const dirtyAttributes = entity.getAttributes(true);

        // Create
        if (entity.id !== null) {
            return await this.connection
                .select()
                .from(this.tableName)
                .where(entity.primaryColumn as string, entity.id)
                .then((payload) => {
                    if (Array.isArray(payload) && payload.length == 1) {
                        return this.store(entity, force);
                    }
                    return this.connection(this._tableName)
                        .insert(
                            _.assign(dirtyAttributes, {
                                [entity.primaryColumn as string]: entity.id,
                            }),
                        ) // Extending with primary ID for inserting
                        .then(
                            () => entity.id, // return new id from sequence (original version don't work properly.
                        );
                });
        }
        return await this.store(entity, force);
    }

    fetchAll(columns: string[] | string): Promise<any> {
        return this.connection.select(columns).from(this._tableName);
    }

    getEntity(data?: any): T {
        this._entity = this.entityClass();

        if (this._entity && this._lang) {
            this._entity.setLocalization(this._lang);
        }

        if (data) {
            Object.keys(data).forEach((key) => {
                if (typeof data[key] !== "undefined") {
                    // @ts-expect-error ts(2862)
                    this._entity[key] = data[key];
                }
            });
        }

        return this._entity;
    }

    getBigAttributes(): string[] {
        const entity = this.getEntity();
        const attrs = entity.attributes();
        const keys = Object.keys(attrs);
        const bigAttrs: string[] = [];

        keys.forEach((key) => {
            if (attrs[key].type == "text") {
                bigAttrs.push(key);
            }
        });

        return bigAttrs;
    }

    primaryWhere(entity: T, id?: number | number[]): Record<string, any> {
        const where: Record<string, any> = {};
        if (entity.hasMultiPrimaryColumn()) {
            where[entity.primaryColumn[0]] = (id as number[])[0];
            where[entity.primaryColumn[1]] = (id as number[])[1];
        } else {
            where[entity.primaryColumn as string] = id;
        }
        return where;
    }

    /** *****************************************************************************************************************
     * Setters and getters
     ***************************************************************************************************************** */

    set connection(connection: Knex) {
        this._connection = connection;
    }

    get connection(): Knex {
        if (!this._connection) {
            throw new Error(
                `Connection is not defined in repository '${this._entityName}'.`,
            );
        }
        return this._connection;
    }

    get collection(): BaseCollection<T> {
        return this.createCollection();
    }

    setConnection(connection: Knex) {
        this._connection = connection;
    }

    get entity(): T {
        const entity = this.getEntity();
        if (entity && this._lang) {
            entity.setLocalization(this._lang);
        }
        return entity;
    }

    get tableName(): string {
        return this._tableName;
    }

    get defaultAlias(): string | undefined {
        return this._defaultAlias;
    }

    get archived(): boolean {
        return this._archived ?? false;
    }

    get entityName(): string {
        return this._entityName;
    }

    get relatedEntities() {
        if (this._relatedEntities) {
            return this._relatedEntities;
        }

        if (!this._entity) {
            this._entity = this.entityClass();
        }

        const relations = this._entity.relations();
        if (!relations) {
            return null;
        }

        this._relatedEntities = [];
        for (const key in relations) {
            this._relatedEntities.push(relations[key]());
        }

        return this._relatedEntities;
    }

    copy(fromId: number | number[], toId: number | number[]): Promise<any> {
        const entity = this.getEntity();
        const attrs = entity.attributes();
        let cols = Object.keys(attrs);

        // Map all attrs, remove primary key.
        let primaryName = null;
        cols.forEach((col) => {
            if (attrs[col].primary) {
                primaryName = col;
                cols = cols.filter((item) => item !== col);
            }
        });
        const motherPrimary = Object.keys(fromId)[0];
        cols = cols
            .filter((item) => item !== motherPrimary)
            .map((item) => `"${item}"`);

        let attrsSqlArr = cols.concat([`"${motherPrimary}"`]);
        // @ts-expect-error ts(7053)
        let insertSqlArr = cols.concat(toId[motherPrimary]);

        if (primaryName) {
            attrsSqlArr = attrsSqlArr.concat([`"${primaryName}"`]);
            insertSqlArr = insertSqlArr.concat([
                globalThis.orm.db.sequence(this._sequenceName!),
            ]);
        }

        const attrsSql = attrsSqlArr.join(", ");
        const insertSql = insertSqlArr.join(", ");

        const sql = `insert into "${this.tableName}" (${attrsSql}) (select ${insertSql} from "${this.tableName}" where "${motherPrimary}" = ?)`;
        // @ts-expect-error ts(7053)
        const params = [fromId[motherPrimary]];

        return globalThis.container.client.database.callKnexRaw(
            sql,
            params,
            this.connection,
        );
    }

    setLocalization(lang: string): void {
        if (lang) {
            this._lang = lang.toUpperCase();
        }
    }

    repo<Key extends RepositoryKey>(
        repoName: Key,
        connection?: Knex,
    ): RepositoryInstances[Key] | undefined {
        return this.orm?.repo(repoName, connection);
    }

    async validate(entity: T): Promise<void> {
        try {
            await this.validateDefault(entity);
            await this.validateIn(entity);
            await this.validateUniqueIn(entity);
            await this.validateMaxLength(entity);
            await this.validateIsNotNumeric(entity);
            await this.validateNotNull(entity);
            await this.validateUnique(entity);
        } catch (err) {
            globalThis.tasLogger.error((err as Error).message, {
                err,
            });
            throw err;
        }
    }

    async validateUniqueIn(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);

        // Validate all attributes.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (
                attr.uniqueIn &&
                !attr.translated &&
                typeof entity[attrName] !== "undefined"
            ) {
                // Only if rule is set.
                const uniqueRepo = globalThis.orm.repo(
                    // @ts-expect-error types
                    attr.uniqueIn,
                    this.connection,
                ); // Get constrained repo.

                let conn = uniqueRepo.connection // Select rows with same attribute in constrained repo.
                    .select(entity.primaryColumn, attrName)
                    .from(this.tableName)
                    /**
                     * t3b-761 MS SQL - nelze vytvořit více proměnných, které mají název složený z mezer
                     * https://dba.stackexchange.com/questions/10510/behavior-of-varchar-with-spaces-at-the-end
                     */
                    .where(attrName, "like", entity[attrName]);

                // Primary where.
                // @ts-expect-error types
                if (attr.uniqueIn.toLowerCase() === "templateprocess") {
                    // Tempalte versioning fix. Check all templates with tproc_id.
                    conn = conn.where("TPROC_ID", entity.TPROC_ID);
                } else {
                    // Search entity by id.
                    conn = conn.where(
                        // @ts-expect-error types
                        uniqueRepo.entity.primaryColumn,
                        // @ts-expect-error types
                        entity[uniqueRepo.entity.primaryColumn],
                    );
                }

                // @ts-expect-error types
                conn = conn
                    .whereNot(
                        // @ts-expect-error types
                        entity.primaryColumn,
                        entity[entity.primaryColumn as string],
                    )
                    // @ts-expect-error types
                    .then((rs: Knex.QueryBuilder) => {
                        if (!Array.isArray(rs) || rs.length === 0) {
                            // No constraint
                            return;
                        }

                        const item = rs[0]; // throw Exception on first constraint.
                        if (
                            item[entity.primaryColumn as string] !==
                            entity[entity.primaryColum as string]
                        ) {
                            throw new UserException(
                                // @ts-expect-error types
                                `Constraint violation. Unique constraint in ${uniqueRepo.tableName}.${uniqueRepo.entity.primaryColumn}=${entity[uniqueRepo.entity.primaryColumn]}.
                            Please see ${attrName}='${entity[attrName]}' same as ${entity.primaryColumn}=${item[entity.primaryColumn as string]}, ${attrName}='${item[attrName]}'`,
                                "UNIQUE_CONSTRAINT",
                            );
                        }
                        return true;
                    });
                await conn;
            }
        }
    }

    async validateUnique(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);

        // Validate all attributes.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (
                attr.unique &&
                !attr.translated &&
                entity[attrName] === null &&
                !attr.duplicateNulls
            ) {
                // Only if rule is set.
                await this.connection // Select rows with same attribute in constrained repo.
                    .select(entity.primaryColumn)
                    .from(this.tableName)
                    .where(attrName, entity[attrName])
                    .whereNot(
                        entity.primaryColumn as string,
                        entity[entity.primaryColumn as string],
                    )
                    .then((rs) => {
                        if (!Array.isArray(rs) || rs.length === 0) {
                            // No constraint
                            return;
                        }

                        const item = rs[0]; // throw Exception on first constraint.
                        if (
                            item[entity.primaryColumn as string] !==
                            entity[entity.primaryColumn as string]
                        ) {
                            throw new UserException(
                                `Unique constraint violation. ${attrName} must be unique.`,
                                "UNIQUE_CONSTRAINT",
                            );
                        }
                        return true;
                    });
            }
        }
    }

    async validateIn(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);
        // Validate in.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (attr.in) {
                const validValues = Array.isArray(attr.in)
                    ? attr.in
                    : [attr.in]; // Make array from simple value.
                if (
                    typeof entity.id === "undefined" ||
                    (typeof entity[attrName] !== "undefined" &&
                        entity[attrName] !== null)
                ) {
                    // Check in validation if insert or modify attr.
                    // In value must be set while inserting.
                    if (validValues.indexOf(entity[attrName]) === -1) {
                        // Invalid index of in attribute.
                        throw new UserException(
                            `Constraint violation of 'IN' values ${entity.constructor.name}.${attrName} with value ${entity[attrName]}`,
                            "UNIQUE_CONSTRAINT",
                        );
                    }
                }
            }
        }
    }

    async validateMaxLength(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);
        // Validate in.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (attr.maxLength) {
                if (
                    typeof entity[attrName] !== "undefined" &&
                    entity[attrName] !== null
                ) {
                    // Validate only filled values.
                    // In value must be set while inserting.
                    if (entity[attrName].length > attr.maxLength) {
                        // Invalid index of in attribute.
                        throw new UserException(
                            `${attrName} is too long. Maximum length is ${attr.maxLength}.`,
                            "UNIQUE_CONSTRAINT",
                        );
                    }
                }
            }
        }
    }

    async validateIsNotNumeric(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);
        // Validate in.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (attr.isNotNumeric) {
                if (
                    typeof entity[attrName] !== "undefined" &&
                    entity[attrName] !== null &&
                    entity[attrName] !== ""
                ) {
                    // Validate only filled values.
                    // In value must be set while inserting.
                    if (!isNaN(entity[attrName])) {
                        // Invalid index of in attribute.
                        throw new UserException(
                            `${attrName} can not be numeric.`,
                            "UNIQUE_CONSTRAINT",
                        );
                    }
                }
            }
        }
    }

    async validateDefault(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);
        // Validate in.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (attr.default || attr.default === null) {
                if (
                    !entity.id &&
                    (typeof entity[attrName] === "undefined" ||
                        entity[attrName] === null)
                ) {
                    // @ts-expect-error ts(2862)
                    entity[attrName] = attr.default;
                }
            }
        }
    }

    async validateNotNull(entity: T): Promise<void> {
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);
        // Validate in.
        for (const attrName of attrNames) {
            const attr = attrs[attrName];
            if (attr.notNull && !attr.translated) {
                // Entity is updated but attr not changed.
                if (
                    typeof entity[attrName] === "undefined" &&
                    typeof entity.id !== "undefined" &&
                    entity.id !== null
                ) {
                    continue;
                }

                const attr = attrs[attrName];
                if (
                    attr.notNull &&
                    (typeof entity[attrName] === "undefined" ||
                        entity[attrName] === null)
                ) {
                    throw new UserException(
                        `NotNull field is empty : ${attrName}`,
                    );
                }
            }
        }
    }

    async findUniqueCloneName(
        nameColumn: string,
        name: string,
        number: number = 1,
    ): Promise<string> {
        const newName = `clone(${number})_${name}`;
        const newNameIsUsed = await this.getId(nameColumn, newName);

        // Name is used already, try again with increment
        if (newNameIsUsed) {
            return await this.findUniqueCloneName(nameColumn, name, number + 1);
        }

        return newName;
    }

    async bulkInsert(
        objects: any[],
        generateSequence: boolean = true,
        sequenceName: string | null = null,
    ): Promise<any> {
        // The query is empty
        if (!objects.length) {
            return;
        }

        const attrs = this.getEntity().getAttributes();
        const attributes = Object.keys(attrs);
        let toInsert = objects.map((item) =>
            Object.keys(item)
                .filter((key) => attributes.includes(key))
                .reduce((obj: Record<string, any>, key) => {
                    obj[key] = item[key];
                    return obj;
                }, {}),
        );

        if (generateSequence) {
            const primaryCol = this.getEntity().primaryColumn;
            sequenceName =
                sequenceName || `${(primaryCol as string).toUpperCase()}_SEQ`;
            await this.alterSeqIncrement(sequenceName, objects.length - 1);
            const maxId = await this.genID(sequenceName);

            toInsert = objects.map((item, index) => {
                item[primaryCol as string] = maxId - index;
                return item;
            });
            await this.alterSeqIncrement(sequenceName, 0);
        }

        return await this.connection(this.tableName).insert(toInsert);
    }

    async getByName(
        name: string,
        columns: string | string[] = "*",
    ): Promise<any> {
        const attrs = this.getEntity().getAttributes(false, true);
        let nameIdentifier = _.find(attrs, { name: true });

        for (const attr of Object.keys(attrs)) {
            if (attrs[attr].name) {
                nameIdentifier = attr;
                break;
            }
        }

        if (!nameIdentifier) {
            throw new InternalException(
                `function getByName() is not defined for Entity '${this.entityName}'`,
            );
        }

        return await this.connection
            .select(columns)
            .from(this.tableName)
            .where(nameIdentifier, name)
            .first();
    }

    getExtendedCollection(
        columns: string[],
        tableAlias?: string,
        extraColumns?: string[],
    ): BaseCollection<T> {
        columns = _.uniq(columns);
        const collection = this.getBaseCollection(
            columns,
            tableAlias,
            extraColumns,
        );

        const joined = new Map();
        for (const column of columns) {
            const [alias, columnName] = column.split(".");
            if (
                alias &&
                columnName &&
                tableAlias !== alias &&
                !joined.has(alias)
            ) {
                joined.set(alias, undefined);
                const [attr, attrName] = this.findAttrWithAlias(alias);
                if (!attr) {
                    throw new UserException(`Can not find alias ${alias}.`);
                }
                const joinedRepo = globalThis.orm.repo(
                    // @ts-expect-error types
                    (attr as EntityAttribute).foreign!,
                );
                const primaryColumn = Array.isArray(
                    joinedRepo.entity.primaryColumn,
                )
                    ? // @ts-expect-error types
                      joinedRepo.entity.primaryOrder()[0]
                    : joinedRepo.entity.primaryColumn;

                collection.knex.leftJoin(
                    `${joinedRepo.tableName} as ${alias}`,
                    `${tableAlias}.${attrName}`,
                    `${alias}.${primaryColumn}`,
                );
            }
        }
        return collection;
    }

    findAttrWithAlias(alias: string): (string | EntityAttribute | undefined)[] {
        const attributes = this.entity.attributes();
        const attrName = Object.keys(attributes).find(
            (attrName) => attributes[attrName].alias === alias,
        );
        if (!attrName) {
            return [undefined, undefined];
        }
        return [attributes[attrName], attrName];
    }

    async totalCount() {
        const [row] = await this.connection
            .count("* as CC")
            .from(this.tableName);

        if ("CC" in row) {
            return row.CC;
        }
        return 0;
    }

    async archive(iprocId: number) {
        const archTableName = this.getArchTableName(this.tableName);

        const dataToArchive = await this.getDataForArchivation(iprocId, true);

        if (dataToArchive.length === 0) {
            return;
        }

        const chunkSize = globalThis.orm.db.getParametersChunkSize(
            dataToArchive[0],
        );
        const chunks = _.chunk(dataToArchive, chunkSize);
        for (const chunk of chunks) {
            await this.connection(archTableName).insert(chunk);
        }
    }

    async unarchive(iprocId: number) {
        const dataToUnarchive = await this.getDataForArchivation(
            iprocId,
            false,
        );

        if (dataToUnarchive.length === 0) {
            return;
        }

        const chunkSize = globalThis.orm.db.getParametersChunkSize(
            dataToUnarchive[0],
        );
        const chunks = _.chunk(dataToUnarchive, chunkSize);
        for (const chunk of chunks) {
            await this.connection(this.tableName).insert(chunk);
        }
    }

    async getDataForArchivation(iprocId: number, isArchivation: boolean) {
        const queryBuilder = isArchivation
            ? this.connection(this.tableName).select()
            : this.connection(this.getArchTableName(this.tableName)).select();

        this.applyArchivationCondition(queryBuilder, iprocId); // queryBuilder is mutated
        return await queryBuilder;
    }

    applyArchivationCondition(
        queryBuilder: Knex.QueryBuilder,
        iprocId: number,
    ) {
        const meta = this.meta();

        if (meta.archParams) {
            queryBuilder.whereIn(meta.archParams.subQueryColumn, (ip) => {
                ip.select(meta.archParams!.subQueryColumn)
                    .from(meta.archParams!.subQueryTable)
                    .where("IPROC_ID", iprocId);
            });
        } else {
            queryBuilder.where("IPROC_ID", iprocId);
        }

        return queryBuilder;
    }

    async deleteForProcess(iprocId: number) {
        return await this.buildDeleteForArchivationQuery(iprocId, true);
    }

    async deleteArchivedForProcess(iprocId: number) {
        return await this.buildDeleteForArchivationQuery(iprocId, false);
    }

    async buildDeleteForArchivationQuery(
        iprocId: number,
        isArchivation: boolean,
    ) {
        const queryBuilder = isArchivation
            ? this.connection(this.tableName).select()
            : this.connection(this.getArchTableName(this.tableName)).select();

        this.applyArchivationCondition(queryBuilder, iprocId);
        return await queryBuilder.delete();
    }

    getArchTableName(tableName: string) {
        return `ARCH_${tableName}`;
    }
}
