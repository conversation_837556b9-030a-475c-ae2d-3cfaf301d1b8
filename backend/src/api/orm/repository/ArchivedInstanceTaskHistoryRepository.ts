import { BaseRepository } from "./BaseRepository";
import * as TASK from "../entity/const/taskConst";
import { InstanceTaskHistory } from "../entity/InstanceTaskHistory";
import { BaseCollection } from "../BaseCollection";

export class ArchivedInstanceTaskHistoryRepository extends BaseRepository<InstanceTaskHistory> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_TASK_HISTORY",
            entityName: "archivedInstanceTaskHistory",
            entity: () => new InstanceTaskHistory(),
        };
    }

    forProcess(
        iprocId: number,
        showHiddenTasks: boolean,
        showDetails: boolean,
    ): BaseCollection<InstanceTaskHistory> {
        const extraColumns = globalThis.dynamicConfig.langs.map(
            (lang: string) => `TT.TTASK_NAME_${lang.toUpperCase()}`,
        );

        const con = this.connection
            .select(
                [
                    "ITH.*",
                    "IT.ITASK_NAME",
                    this.connection.raw(
                        `ITU.USER_DISPLAY_NAME AS ITASK_USER_NAME`,
                    ),
                    this.connection.raw(
                        `ITFBU.USER_DISPLAY_NAME AS ITASKH_FINISHED_BY_USER_NAME`,
                    ),
                ].concat(extraColumns),
            )
            .from(`${this.tableName} as ITH`)
            .leftJoin("USERS as ITU", "ITH.ITASKH_USER_ID", "ITU.USER_ID")
            .leftJoin(
                "USERS as ITFBU",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "ITFBU.USER_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_TASKS as IT",
                "ITH.ITASK_ID",
                "IT.ITASK_ID",
            )
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .where("ITH.IPROC_ID", iprocId);

        if (!showHiddenTasks) {
            con.where("ITH.ITASKH_HIDDEN", "N");
        }

        if (!showDetails) {
            con.whereIn("ITH.ITASKH_NOTE", [TASK.HISTORY_NOTE_LAST_SOLVER]);
        }

        return globalThis.orm.collection("archivedInstanceTaskHistory", con);
    }
}
