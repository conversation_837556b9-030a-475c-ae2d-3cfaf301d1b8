// @ts-nocheck
// @ts-nocheck
import { ReportGraph } from "../entity/ReportGraph";
import { BaseRepository } from "./BaseRepository";

export class ReportGraphRepository extends BaseRepository<ReportGraph> {
    meta() {
        return {
            tableName: "REPORT_GRAPHS",
            entityName: "ReportGraph",
            entity: () => new ReportGraph(),
        };
    }

    getGraph(graphId) {
        // Translations
        const extraColumns = [];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            extraColumns.push(`RG.GRAPH_NAME_${lang.toUpperCase()}`);
            extraColumns.push(`RG.GRAPH_X_LABEL_${lang.toUpperCase()}`);
            extraColumns.push(`RG.GRAPH_Y_LABEL_${lang.toUpperCase()}`);
        });

        const columns = [
            "RG.GRAPH_ID",
            "RG.GRAPH_NAME",
            "RG.GRAPH_TYPE",
            "RG.GRAPH_META",
            "RG.GRAPH_USER_ID",
            "RG.GRAPH_DATA_TYPE",
            "RG.GRAPH_X_LABEL",
            "RG.GRAPH_Y_LABEL",
            "RG.GRAPH_SHOW_VAL_LABELS",
            globalThis.database.raw(
                `"U"."USER_DISPLAY_NAME" as "GRAPH_OWNER_NAME"`,
            ),
        ].concat(extraColumns);

        return this.connection
            .select(columns)
            .from("REPORT_GRAPHS as RG")
            .where("GRAPH_ID", graphId)
            .leftJoin("USERS as U", "GRAPH_USER_ID", "U.USER_ID");
    }

    getMine(userId, _roleIds, orgstrIds) {
        // Translations
        const extraColumns = [];
        const entity = this.getEntity();
        const attrs = entity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`RG.${attrName}`);
            }
        });
        const columns = [
            "RG.GRAPH_ID",
            "RG.GRAPH_NAME",
            "RG.GRAPH_TYPE",
            "RG.GRAPH_USER_ID",
            globalThis.database.raw(
                `"U"."USER_DISPLAY_NAME" as "GRAPH_OWNER_NAME"`,
            ),
            globalThis.database.raw(
                `"U"."USER_NAME" as "GRAPH_OWNER_USER_NAME"`,
            ),
        ].concat(extraColumns);

        return (
            this.connection
                .distinct(columns)
                .select()
                .from("REPORT_GRAPHS as RG")
                .leftJoin(
                    "REPORT_GRAPH_SHARE as RGS",
                    "RGS.RGS_RG_ID",
                    "RG.GRAPH_ID",
                )
                .leftJoin("USERS as U", "GRAPH_USER_ID", "U.USER_ID")
                // .where('RG.CV_IS_DELETED', CustomView.IS_NOT_DELETED)
                .where((builder) => {
                    builder
                        .where("RGS.RGS_USER_ID", userId)
                        .orWhere("GRAPH_USER_ID", userId)
                        .orWhereIn(
                            "RGS.RGS_OS_ID",
                            globalThis.database.raw(orgstrIds),
                        )
                        .orWhereIn("RGS.RGS_ROLE_ID", (builder) => {
                            builder
                                .select("UR.ROLE_ID")
                                .from("USER_ROLES as UR")
                                .where("UR.USER_ID", userId);
                        });

                    /*
                @t3b-1491 Uzivatel si nemuze zobrazit seznam prehledu kvuli vysokemu poctu roli

                Prevent: ORA-01795: maximum number of expressions in a list is 1000
                const chunked = _.chunk(roleIds, 999);
                chunked.forEach(chunk => {
                    builder.orWhereIn('RGS.RGS_ROLE_ID', globalThis.database.raw(chunk));
                }); */
                })
                .orderBy("RG.GRAPH_NAME")
                .orderBy("RG.GRAPH_ID")
                .limit(Number.MAX_SAFE_INTEGER)
        );
    }

    getGraphPoints(graphId) {
        const extraCols = [];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            extraCols.push(`RGP.POINT_LABEL_${lang.toUpperCase()}`);
            extraCols.push(`CV.CV_NAME_${lang.toUpperCase()}`);
        });

        return this.connection
            .select(
                [
                    "RGP.CV_ID",
                    "CV.TPROC_ID",
                    "CV.CV_NAME",
                    "RGP.POINT_LABEL",
                    "RGP.CV_FILTER",
                    "RGP.POINT_AGGREGATION",
                    "RGP.AGGREGATION_COLUMN",
                    "RGP.POINT_META",
                    "RGP.GROUP_COLUMNS",
                ].concat(extraCols),
            )
            .from("REPORT_GRAPHS_POINTS as RGP")
            .where("RGP.GRAPH_ID", graphId)
            .leftJoin("CUSTOM_VIEWS as CV", "RGP.CV_ID", "CV.CV_ID");
    }

    /**
     * Remove report graph
     * @param id
     * @returns {*}
     */
    async delete(id) {
        await this.connection
            .select()
            .from("REPORT_GRAPHS_POINTS")
            .where("GRAPH_ID", id)
            .delete();
        return await this.connection
            .select()
            .from("REPORT_GRAPH_SHARE")
            .where("RGS_RG_ID", id)
            .delete()
            .then(() =>
                this.connection
                    .select()
                    .from("REPORT_GRAPHS")
                    .where("GRAPH_ID", id)
                    .delete(),
            );
    }

    /**
     * Makes copy of report graph. New name is generated
     * automatically with `clone($index)_$oldName` where $index is count of previous copies.
     * @param id
     * @param ownerUserId
     * @returns {Promise.<Number>}
     */
    async clone(id, ownerUserId): Promise<number> {
        const oldGraph = await this.get(id);
        const oldId = oldGraph.GRAPH_ID;

        oldGraph.GRAPH_ID = null;
        oldGraph.makeAllDirty();
        oldGraph.GRAPH_NAME = await this.findUniqueCloneName(
            "GRAPH_NAME",
            oldGraph.GRAPH_NAME,
        );
        oldGraph.GRAPH_USER_ID = ownerUserId;

        const newId = await this.store(oldGraph);
        oldGraph.GRAPH_ID = newId;

        const rgpRepo = globalThis.orm.repo(
            "reportGraphsPoints",
            this.connection,
        );
        const graphPoints = await this.getGraphPoints(oldId);

        for (const point of graphPoints) {
            const entity = rgpRepo.getEntity();
            const attrs = entity.attributes();
            const attrKeys = Object.keys(attrs);
            attrKeys.forEach((key) => {
                entity[key] = point[key];
            });

            entity.GRAPH_ID = newId;
            await rgpRepo.store(entity);
        }

        const rgsRepo = globalThis.orm.repo(
            "reportGraphShare",
            this.connection,
        );
        await rgsRepo.copy({ RGS_RG_ID: oldId }, { RGS_RG_ID: newId });

        // Clone global filters
        const globalFilterRepo = globalThis.orm.repo(
            "reportGraphGlobalFilter",
            this.connection,
        );
        const globalFilterOptionRepo = globalThis.orm.repo(
            "reportGraphGlobalFilterOption",
            this.connection,
        );
        const globalFilterFilterDefRepo = globalThis.orm.repo(
            "reportGraphGlobalFilterDef",
            this.connection,
        );
        // global filters
        const graphFilters = await globalFilterRepo
            .getForGraph(oldId)
            .fetchAll();

        for (const filter of graphFilters) {
            const filterEntity = globalFilterRepo.getEntity();
            const filterAttrs = filterEntity.attributes();
            const filterAttrKeys = Object.keys(filterAttrs);
            filterAttrKeys.forEach((key) => {
                filterEntity[key] = filter[key];
            });

            filterEntity.RGGF_ID = null;
            filterEntity.GRAPH_ID = newId;

            const newFilterId = await globalFilterRepo.store(filterEntity);

            // global filter options
            const graphFilterOptions = await globalFilterOptionRepo
                .getForFilter(filter.RGGF_ID)
                .fetchAll();

            for (const option of graphFilterOptions) {
                const optionEntity = globalFilterOptionRepo.getEntity();
                const optionEntityAttrs = optionEntity.attributes();
                const optionAttrKeys = Object.keys(optionEntityAttrs);
                optionAttrKeys.forEach((key) => {
                    optionEntity[key] = option[key];
                });

                optionEntity.RGGFO_ID = null;
                optionEntity.RGGF_ID = newFilterId;

                const newOptionId =
                    await globalFilterOptionRepo.store(optionEntity);

                // global filter option definition
                const graphFilterOptionDef = await globalFilterFilterDefRepo
                    .getForOption(option.RGGFO_ID)
                    .fetchAll();

                for (const definition of graphFilterOptionDef) {
                    const defEntity = globalFilterFilterDefRepo.getEntity();
                    const defEntityAttrs = defEntity.attributes();
                    const defAttrKeys = Object.keys(defEntityAttrs);
                    defAttrKeys.forEach((key) => {
                        defEntity[key] = definition[key];
                    });

                    defEntity.RGGFD_ID = null;
                    defEntity.RGGFO_ID = newOptionId;

                    await globalFilterFilterDefRepo.store(defEntity);
                }
            }
        }
        return newId;
    }
}
