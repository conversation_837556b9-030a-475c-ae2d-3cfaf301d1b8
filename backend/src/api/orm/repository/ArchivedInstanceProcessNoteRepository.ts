import { InstanceProcessNote } from "../entity/InstanceProcessNote";
import { BaseRepository } from "./BaseRepository";
import { BaseCollection } from "../BaseCollection";

export class ArchivedInstanceProcessNoteRepository extends BaseRepository<InstanceProcessNote> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_PROCESS_NOTES",
            entityName: "archivedInstanceProcessNote",
            entity: () => new InstanceProcessNote(),
        };
    }

    all(iprocId: number): BaseCollection<InstanceProcessNote> {
        // Add translations
        const extraColumns: string[] = [];

        const iProcAttrs = globalThis.orm
            .repo("archivedProcess")
            .getEntity()
            .attributes();
        const iTaskAttrs = globalThis.orm
            .repo("archivedInstanceTask")
            .getEntity()
            .attributes();
        const tTaskAttrs = globalThis.orm
            .repo("templateTask")
            .getEntity()
            .attributes();

        // Instance Process translations
        Object.keys(iProcAttrs).forEach((attrName) => {
            if (iProcAttrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
                extraColumns.push(`IPI.${attrName} as ${attrName}_ORIGINAL`);
            }
        });

        // Instance Task translations
        Object.keys(iTaskAttrs).forEach((attrName) => {
            if (iTaskAttrs[attrName].translated) {
                extraColumns.push(`IT.${attrName}`);
            }
        });

        // Template Task translations
        Object.keys(tTaskAttrs).forEach((attrName) => {
            if (tTaskAttrs[attrName].translated) {
                extraColumns.push(`TT.${attrName}`);
            }
        });

        const columns = [
            "IPN.*",
            this.connection.raw(`"IPI"."IPROC_NAME" as "IPROC_NAME_ORIGINAL"`),
            "IP.IPROC_NAME",
            this.connection.raw(
                `"UV"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "UV"."USER_FIRST_NAME" AS "USER_VICE_FULL_NAME"`,
            ),
            "UV.USER_DISPLAY_NAME AS USER_VICE_DISPLAY_NAME",
            this.connection.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" AS "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ].concat(extraColumns);

        const con = this.connection
            .select(columns)
            .from(`${this.tableName} as IPN`)
            .leftJoin("USERS as U", "IPN.IPN_USER_ID", "U.USER_ID")
            .leftJoin("USERS as UV", "IPN.IPN_VICE_USER_ID", "UV.USER_ID")
            .leftJoin(
                "ARCH_INSTANCE_TASKS as IT",
                "IPN.IPN_ITASK_ID",
                "IT.ITASK_ID",
            )
            .leftJoin("TEMPLATE_TASKS as TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES as IP",
                "IPN.IPROC_ID",
                "IP.IPROC_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES as IPI",
                "IPN.IPROC_ID_INSERTED",
                "IPI.IPROC_ID",
            )
            .where("IPN.IPROC_ID", iprocId);

        return globalThis.orm.collection("archivedInstanceProcessNote", con);
    }

    userNotes(
        iprocId: number,
        includeDeleted?: boolean,
    ): BaseCollection<InstanceProcessNote> {
        const coll: BaseCollection<InstanceProcessNote> = this.all(iprocId);
        coll.knex.where("IPN.IPN_TYPE", "USER");
        if (!includeDeleted) {
            coll.knex.where("IPN.DELETED_AT", null);
        }
        return coll;
    }
}
