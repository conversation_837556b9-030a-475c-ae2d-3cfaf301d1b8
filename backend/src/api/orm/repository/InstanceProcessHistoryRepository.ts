import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as TASK from "../entity/const/taskConst";
import { InstanceProcessHistory } from "../entity/InstanceProcessHistory";

export class InstanceProcessHistoryRepository extends BaseRepository<InstanceProcessHistory> {
    meta() {
        return {
            tableName: "INSTANCE_PROCESS_HISTORY",
            entityName: "InstanceProcessHistory",
            entity: () => new InstanceProcessHistory(),
            archived: true,
        };
    }

    async generateRecord(
        iprocId: number,
        note: string,
        primaryUserId: number,
        finishedUserId: number,
        insertedDate: Date,
        hidden: boolean = false,
        category: string | null = null,
    ): Promise<number | number[] | null> {
        const values = {
            IPROC_ID: iprocId,
            IPH_USER_ID: finishedUserId,
            IPH_PRIMARY_USER_ID:
                primaryUserId !== finishedUserId ? primaryUserId : null,
            IPH_INSERTED: insertedDate,
            IPH_NOTE: note,
            IPH_HIDDEN: hidden ? "Y" : "N",
            IPH_CATEGORY: category,
        };

        const entity = this.getEntity(values);
        return await this.store(entity);
    }

    /**
     * Returns all history records for process joining hist.tasks and hist.process.
     *
     * @param iprocId
     * @param showHidden
     * @param showDetails
     * @param category optinal argument, only hist.process stores this
     * @return {BaseCollection}
     */
    wholeHistoryForProcess(
        iprocId: number,
        showHidden: boolean,
        showDetails: boolean,
        category?: any,
    ) {
        const extraColumns = globalThis.dynamicConfig.langs.map(
            (lang: any) => `TT.TTASK_NAME_${lang.toUpperCase()}`,
        );
        const knexConn = this.connection;
        const con = this.connection.select("*").from(function () {
            // @ts-expect-error incorrect knex use
            this.select(
                [
                    "ITH.ITASKH_ID as ID",
                    "ITH.IPROC_ID",
                    "ITH.ITASKH_ID as ITASKH_ID",
                    "ITH.ITASK_ID",
                    "ITH.ITASKH_ACTUAL_DATE_START",
                    "ITH.ITASKH_ACTUAL_DATE_FINISH",
                    "ITH.ITASKH_DUE_DATE_START",
                    "ITH.ITASKH_DUE_DATE_FINISH",
                    "ITH.ITASKH_FINISHED_BY_USER_ID",
                    "ITH.ITASKH_USER_ID",
                    "ITH.ITASKH_NOTE",
                    "ITH.ITASKH_SUBPROCESS_IPROC_ID",
                    "ITH.ITASKH_HIDDEN",
                    "ITH.ITASKH_PRIMARY_USER_ID",
                    "IT.ITASK_NAME",
                    "IT.ITASK_GEN_HISTORY",
                    "IT.ITASK_STATUS",
                    "IT.ITASK_AUTO_START",
                    "IT.ITASK_TYPE",
                    knexConn.raw(
                        `ITU.USER_DISPLAY_NAME AS ITASK_USER_DISPLAY_NAME`,
                    ),
                    knexConn.raw(
                        `ITFBU.USER_DISPLAY_NAME AS ITASKH_FINISHED_BY_USER_NAME`,
                    ),
                    knexConn.raw("NULL AS IPH_CATEGORY"),
                ].concat(extraColumns),
            )
                .from("INSTANCE_TASK_HISTORY as ITH")
                .leftJoin("USERS as ITU", "ITH.ITASKH_USER_ID", "ITU.USER_ID")
                .leftJoin(
                    "USERS as ITFBU",
                    "ITH.ITASKH_FINISHED_BY_USER_ID",
                    "ITFBU.USER_ID",
                )
                .leftJoin("INSTANCE_TASKS as IT", "ITH.ITASK_ID", "IT.ITASK_ID")
                .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
                .where("ITH.IPROC_ID", iprocId)
                .unionAll(function () {
                    // @ts-expect-error incorrect knex use
                    this.select(
                        [
                            "IPH.IPH_ID as ID",
                            "IPH.IPROC_ID",
                            knexConn.raw("NULL as ITASKH_ID"), // to show detail history
                            knexConn.raw("NULL as ITASK_ID"),
                            "IPH_INSERTED as ITASKH_ACTUAL_DATE_START",
                            "IPH_INSERTED as ITASKH_ACTUAL_DATE_FINISH",
                            knexConn.raw("NULL as ITASKH_DUE_DATE_START"),
                            knexConn.raw("NULL as ITASKH_DUE_DATE_FINISH"),
                            "IPH_USER_ID as ITASKH_FINISHED_BY_USER_ID",
                            "IPH.IPH_USER_ID as ITASKH_USER_ID",
                            "IPH_NOTE as ITASKH_NOTE",
                            knexConn.raw("NULL as ITASKH_SUBPROCESS_IPROC_ID"),
                            "IPH_HIDDEN as ITASKH_HIDDEN",
                            "IPH_PRIMARY_USER_ID as ITASKH_PRIMARY_USER_ID",
                            knexConn.raw("NULL as ITASK_NAME"),
                            knexConn.raw("NULL AS ITASK_USER_NAME"),
                            knexConn.raw("NULL AS ITASK_GEN_HISTORY"),
                            knexConn.raw("NULL AS ITASK_STATUS"),
                            knexConn.raw("NULL AS ITASK_AUTO_START"),
                            knexConn.raw("NULL AS ITASK_TYPE"),
                            knexConn.raw(
                                `ITU.USER_DISPLAY_NAME as ITASKH_FINISHED_BY_USER_NAME`,
                            ),
                            // finished_user belongs to finished user of task, but this is hack
                            "IPH.IPH_CATEGORY",
                        ].concat(
                            globalThis.dynamicConfig.langs.map((lg: any) =>
                                knexConn.raw(`NULL as TTASK_NAME_${lg}`),
                            ),
                        ),
                    )
                        .from("INSTANCE_PROCESS_HISTORY as IPH")
                        .leftJoin(
                            "USERS as ITU",
                            "IPH.IPH_USER_ID",
                            "ITU.USER_ID",
                        )
                        .where("IPH.IPROC_ID", iprocId);
                })
                .as("ignored_alias");
        });
        if (!showHidden) {
            con.where("ITASKH_HIDDEN", "N");
        }

        if (category) {
            con.where("IPH_CATEGORY", category);
        }

        if (!showDetails) {
            con.whereIn("ITASKH_NOTE", [TASK.HISTORY_NOTE_LAST_SOLVER]);
        }

        return globalThis.orm.collection("instanceTaskHistory", con);
    }

    forProcess(iprocId?: number, showHidden?: boolean, category?: any) {
        const con = this.connection
            .select([
                "IPH.*",
                this.connection.raw(`ITU.USER_DISPLAY_NAME AS IPH_USER_NAME`),
            ])
            .from(`${this.tableName} as IPH`)
            .leftJoin("USERS as ITU", "IPH.IPH_USER_ID", "ITU.USER_ID");

        if (iprocId) {
            con.where("IPH.IPH.IPROC_ID", iprocId);
        }

        if (!showHidden) {
            con.where("IPH.IPH_HIDDEN", "N");
        }

        if (category) {
            con.where("IPH_CATEGORY", category);
        }

        const coll = globalThis.orm.collection("instanceProcessHistory", con);

        coll.filteringColumns = {
            IPH_CATEGORY: { type: "string", key: "IPH.IPH_CATEGORY" },
            IPROC_ID: { type: "integer", key: "IPH.IPROC_ID" },
            IPH_USER_NAME: {
                type: "string",
                key: `ITU.USER_DISPLAY_NAME`,
            },
            IPH_NOTE: { type: "string", key: "IPH.IPH_NOTE" },
        };
        coll.orderingColumns = _.cloneDeep(coll.filteringColumns);
        return coll;
    }
}
