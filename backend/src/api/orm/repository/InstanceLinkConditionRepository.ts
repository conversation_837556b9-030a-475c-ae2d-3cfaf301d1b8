import { InstanceLinkCondition } from "../entity/InstanceLinkCondition";
import { IInstanceTaskLink } from "../entity/InstanceTaskLink";
import { BaseRepository } from "./BaseRepository";

export class InstanceLinkConditionRepository extends BaseRepository<InstanceLinkCondition> {
    meta() {
        return {
            tableName: "INSTANCE_LINK_CONDITIONS",
            entityName: "InstanceLinkCondition",
            entity: () => new InstanceLinkCondition(),
            archived: false,
        };
    }

    async copyConditionFromTemplate(linkMap: Record<string, any>) {
        const tlinkIds = Object.keys(linkMap);
        if (!Array.isArray(tlinkIds) || tlinkIds.length === 0) {
            return true;
        }

        const condMap: Record<string, any> = {};
        const tconds = await this.connection
            .select([
                "TCOND_ID",
                "TCOND_VARIABLE",
                "TCOND_OPERATOR",
                "TCOND_VALUE",
                "TTASKLINK_ID",
            ])
            .from("TEMPLATE_LINK_CONDITIONS")
            .whereIn("TTASKLINK_ID", tlinkIds);
        if (Array.isArray(tconds) && tconds.length > 0) {
            await Promise.all(
                tconds.map(async (tcond) => {
                    const condId = await this.store(
                        this.getEntity({
                            ICOND_VARIABLE: tcond.TCOND_VARIABLE,
                            ICOND_OPERATOR: tcond.TCOND_OPERATOR,
                            ICOND_VALUE: tcond.TCOND_VALUE,
                            ITASKLINK_ID: linkMap[tcond.TTASKLINK_ID],
                            TCOND_ID: tcond.TCOND_ID,
                        }),
                    );
                    condMap[tcond.TCOND_ID] = condId;
                }),
            );
        }
        return condMap;
    }

    /**
     * Returns conditions of the link
     *
     * @access public
     * @return array
     */
    getConditions(link: IInstanceTaskLink) {
        const collLinks = this.getCollection();
        collLinks.knex.where("ITASKLINK_ID", link.ITASKLINK_ID);
        return collLinks;
    }
}
