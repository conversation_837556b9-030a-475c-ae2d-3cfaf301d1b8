// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import * as PARAMETERS from "../entity/const/userParameterConsts";
import { UserParameter } from "../entity/UserParameter";

export class UserParameterRepository extends BaseRepository<UserParameter> {
    meta() {
        return {
            tableName: "USER_PARAMETERS",
            entityName: "UserParameter",
            defaultAlias: "UP",
            entity: () => new UserParameter(),
        };
    }

    getPrivateSettings() {
        return [
            "MAIL_ESCALATION",
            "HOME_STYLE",
            "USER_LAST_SEARCH",
            "MAIL_PROC_ESCALATION",
            "TOOLTIP",
            "CLIENT_LANGUAGE",
            "DATE_FORMAT",
            "MAIL_PROMPTLY",
            "MAIL_TOTAL",
            "MAIL_PULL",
            "NEWS_NOTIFICATION",
        ];
    }

    getDefaults() {
        const data = {
            MAIL_ESCALATION: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "MAIL_ESCALATION",
                USRPAR_CLIENT: "GLOBAL",
            },
            CONTACT_SKYPE: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CONTACT_SKYPE",
                USRPAR_CLIENT: "GLOBAL",
            },
            HOME_STYLE: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "HOME_STYLE",
                USRPAR_CLIENT: "GLOBAL",
            },
            USER_LAST_SEARCH: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "USER_LAST_SEARCH",
                USRPAR_CLIENT: "GLOBAL",
            },
            CONTACT_INFO: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CONTACT_INFO",
                USRPAR_CLIENT: "GLOBAL",
            },
            MAIL_PROC_ESCALATION: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "MAIL_PROC_ESCALATION",
                USRPAR_CLIENT: "GLOBAL",
            },
            CONTACT_PHONE: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CONTACT_PHONE",
                USRPAR_CLIENT: "GLOBAL",
            },
            CONTACT_ICQ: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CONTACT_ICQ",
                USRPAR_CLIENT: "GLOBAL",
            },
            TOOLTIP: {
                USRPAR_VALUE: "OFF",
                USRPAR_NAME: "TOOLTIP",
                USRPAR_CLIENT: "GLOBAL",
            },
            CONTACT_FAX: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CONTACT_FAX",
                USRPAR_CLIENT: "GLOBAL",
            },
            CLIENT_LANGUAGE: {
                USRPAR_VALUE: globalThis.dynamicConfig.defaultLang,
                USRPAR_NAME: "CLIENT_LANGUAGE",
                USRPAR_CLIENT: "GLOBAL",
            },
            DATE_FORMAT: {
                USRPAR_VALUE: "L",
                USRPAR_NAME: "DATE_FORMAT",
                USRPAR_CLIENT: "GLOBAL",
            },
            MAIL_PROMPTLY: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "MAIL_PROMPTLY",
                USRPAR_CLIENT: "GLOBAL",
            },
            MAIL_PULL: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "MAIL_PULL",
                USRPAR_CLIENT: "GLOBAL",
            },
            MAIL_TOTAL: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "MAIL_TOTAL",
                USRPAR_CLIENT: "GLOBAL",
            },
            CONTACT_MOBILE: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CONTACT_MOBILE",
                USRPAR_CLIENT: "GLOBAL",
            },
            DMS_COLUMNS: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "DMS_COLUMNS",
                USRPAR_CLIENT: "GLOBAL",
            },
            DASHBOARD: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "DASHBOARD",
                USRPAR_CLIENT: "GLOBAL",
            },
            TABLES_COLUMNS: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "TABLES_COLUMNS",
                USRPAR_CLIENT: "GLOBAL",
            },
            USER_FILTER: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "USER_FILTER",
                USRPAR_CLIENT: "GLOBAL",
            },
            HIDE_MAIN_MENU: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "HIDE_MAIN_MENU",
                USRPAR_CLIENT: "GLOBAL",
            },
            DISABLE_ADMIN: {
                USRPAR_VALUE: "N",
                USRPAR_NAME: "DISABLE_ADMIN",
                USRPAR_CLIENT: "GLOBAL",
            },
            SERVICE_OPERATIONS_COLUMNS: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "SERVICE_OPERATIONS_COLUMNS",
                USRPAR_CLIENT: "GLOBAL",
            },
            CUSTOM_VIEW_VISIBILITY: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "CUSTOM_VIEW_FAVORITES",
                USRPAR_CLIENT: "GLOBAL",
            },
            USER_FILTER_5: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "USER_FILTER_5",
                USRPAR_CLIENT: "GLOBAL",
            },
            ADMINISTRATION_FAVOURITES: {
                USRPAR_VALUE: null,
                USRPAR_NAME: "ADMINISTRATION_FAVOURITES",
                USRPAR_CLIENT: "GLOBAL",
            },
            NEWS_NOTIFICATION: {
                USRPAR_VALUE: "Y",
                USRPAR_NAME: "NEWS_NOTIFICATION",
                USRPAR_CLIENT: "GLOBAL",
            },
        };

        return data;
    }

    getAdditionalObjectInfo() {
        const repo = globalThis.orm.repo(
            "AdditionalObjectInfo",
            this.connection,
        );
        return this.connection
            .select()
            .from("ADDITIONAL_OBJECT_INFO")
            .then((data) =>
                repo.castRows(data, repo.entity.getAttributes(false, true)),
            )
            .then((data) => {
                const out = {
                    OBJECT_INFO: {
                        USRPAR_VALUE: data,
                        USRPAR_NAME: "OBJECT_INFO",
                        USRPAR_CLIENT: "ADDITIONAL",
                    },
                };
                return out;
            });
    }

    getOrganizationSettings() {
        return globalThis.orm
            .repo("Organization", this.connection)
            .get(1)
            .then((org) => {
                const attrs = org.attributes();

                const out = {};
                Object.keys(attrs).forEach((key) => {
                    if (key == "ORG_ID") {
                        return;
                    }
                    out[key] = {
                        USRPAR_NAME: key,
                        USRPAR_VALUE: org[key],
                        USRPAR_CLIENT: "ORG",
                    };
                });

                return out;
            });
    }

    /**
     * Get user params.
     * @param {Number} userId user id
     * @param {string} name Null or param name
     * @returns {*}
     */
    getForUser(userId, name) {
        let conn = this.connection
            .select()
            .from(this.tableName)
            .where("USER_ID", userId);
        if (name) {
            conn = conn.where("USRPAR_NAME", name);
        }
        return globalThis.orm.collection("UserParameter", conn);
    }

    getUserSettings(userId) {
        return globalThis.orm
            .repo("user", this.connection)
            .get(userId)
            .then((payload) => ({
                USER_FIRST_NAME: {
                    USRPAR_NAME: "USER_FIRST_NAME",
                    USRPAR_VALUE: payload.USER_FIRST_NAME,
                    USRPAR_CLIENT: "USER",
                },
                USER_LAST_NAME: {
                    USRPAR_NAME: "USER_LAST_NAME",
                    USRPAR_VALUE: payload.USER_LAST_NAME,
                    USRPAR_CLIENT: "USER",
                },
                USER_DISPLAY_NAME: {
                    USRPAR_NAME: "USER_DISPLAY_NAME",
                    USRPAR_VALUE: payload.USER_DISPLAY_NAME,
                    USRPAR_CLIENT: "USER",
                },
                USER_EMAIL: {
                    USRPAR_NAME: "USER_EMAIL",
                    USRPAR_VALUE: payload.USER_EMAIL,
                    USRPAR_CLIENT: "USER",
                },
                USER_PHOTO: {
                    USRPAR_NAME: "USER_PHOTO",
                    USRPAR_VALUE: payload.USER_PHOTO,
                    USRPAR_CLIENT: "USER",
                },
            }));
    }

    async getUserEnabledMailSettings(userId) {
        return await this.connection
            .select("USRPAR_NAME", "USRPAR_VALUE")
            .from(this.tableName)
            .where((builder) => {
                builder
                    .where("USER_ID", userId)
                    .where("USRPAR_VALUE", PARAMETERS.ENABLED_YES)
                    .whereIn("USRPAR_NAME", [
                        PARAMETERS.SEND_NEW_TASK_NOTIFICATION,
                        PARAMETERS.SEND_TASK_OVERVIEW_NOTIFICATION,
                        PARAMETERS.SEND_MAIL_ESCALATION_NOTIFICATION,
                        PARAMETERS.SEND_TASK_TO_PULL_NOTIFICATION,
                    ]);
            });
    }

    async updateOrgSettings(params) {
        const repo = globalThis.orm.repo("organization", this.connection);
        if (!params || !Array.isArray(params) || params.length == 0) {
            return 1;
        }

        const org = await repo.get(1);
        params.forEach((param) => {
            if (param.USRPAR_NAME == "ORG_ID") {
                return;
            }
            org[param.USRPAR_NAME] = param.USRPAR_VALUE;
        });

        return await repo.store(org);
    }

    async updateObjectInfo(params) {
        if (!params || !Array.isArray(params) || params.length == 0) {
            return;
        }

        const repo = globalThis.orm.repo(
            "additionalObjectInfo",
            this.connection,
        );
        const parsed = repo.parseRawObjectInfo(params[0].value);

        await this.connection
            .select()
            .from("ADDITIONAL_OBJECT_INFO")
            .truncate();

        for (const entity of parsed) {
            await repo.create(entity);
        }
    }

    updateUserSettings(params, userId) {
        const repo = globalThis.orm.repo("user", this.connection);
        if (!params || !Array.isArray(params) || params.length == 0) {
            return 1;
        }

        return repo.get(userId).then((user) => {
            params.forEach((param) => {
                user[param.USRPAR_NAME] = param.USRPAR_VALUE;
            });
            return repo.store(user);
        });
    }

    async updateGlobalSettings(params, userId) {
        if (!params || !Array.isArray(params)) {
            throw new UserException("Illegal argument.");
        }

        const repo = globalThis.orm.repo("userParameter", this.connection);

        for (const param of params) {
            const payload = await repo.connection
                .select("USRPAR_ID")
                .from(repo.tableName)
                .where("USER_ID", userId)
                .where("USRPAR_NAME", param.USRPAR_NAME);

            const { entity } = repo;
            entity.USRPAR_CLIENT = "GLOBAL";
            entity.USRPAR_NAME = param.USRPAR_NAME;

            if (param.USRPAR_VALUE && param.USRPAR_VALUE.length > 3999) {
                entity.USRPAR_BIG_VALUE = param.USRPAR_VALUE;
                entity.USRPAR_VALUE = null;
            } else {
                entity.USRPAR_VALUE = param.USRPAR_VALUE;
                entity.USRPAR_BIG_VALUE = null;
            }

            entity.USER_ID = userId;
            entity.ORG_ID = 1;

            // Find id
            let id = null;
            if (payload && Array.isArray(payload) && payload.length > 0) {
                id = payload[0].USRPAR_ID;
            }
            entity.USRPAR_ID = id;
            await repo.store(entity);
        }

        return 1;
    }

    getUserParameter(userIds, parameters, onlyEnabled?) {
        return this.connection
            .select("USER_ID", "USRPAR_NAME", "USRPAR_VALUE")
            .from(this.tableName)
            .where((builder) => {
                builder.whereIn(
                    "USRPAR_NAME",
                    Array.isArray(parameters) ? parameters : [parameters],
                );

                if (userIds) {
                    builder.whereIn(
                        "USER_ID",
                        Array.isArray(userIds) ? userIds : [userIds],
                    );
                }

                if (onlyEnabled) {
                    builder.where("USRPAR_VALUE", PARAMETERS.ENABLED_YES);
                }
            });
    }
}
