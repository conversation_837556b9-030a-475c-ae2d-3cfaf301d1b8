// @ts-nocheck
// @ts-nocheck
import { InstanceTaskInvitation } from "../entity/InstanceTaskInvitation";
import { BaseRepository } from "./BaseRepository";

export class InstanceTaskInvitationRepository extends BaseRepository<InstanceTaskInvitation> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_INVITATIONS",
            entityName: "InstanceTaskInvitation",
            entity: () => new InstanceTaskInvitation(),
        };
    }

    update(data, itaskId) {
        if (typeof data.ITASK_INV_DTSTART === "string") {
            data.ITASK_INV_DTSTART = new Date(data.ITASK_INV_DTSTART);
        }
        if (typeof data.ITASK_INV_DTEND === "string") {
            data.ITASK_INV_DTEND = new Date(data.ITASK_INV_DTEND);
        }
        return this.connection(this.tableName)
            .update(data)
            .where("ITASK_ID", itaskId);
    }

    async copyForTemplateTasks(ttaskMap) {
        const ttaskIds = Object.keys(ttaskMap);
        if (!Array.isArray(ttaskIds) || ttaskIds.length === 0) {
            return;
        }

        const taskInvitationQueries = [];
        for (const ttaskId of ttaskIds) {
            if (ttaskMap[ttaskId].ITASK_TYPE !== "I") {
                continue;
            }

            const insertQuery = this.connection.raw(
                `INSERT INTO "INSTANCE_TASK_INVITATIONS"
                    ("ITASK_ID", "ITASK_INV_ATTENDEES", "ITASK_INV_SUMMARY", "ITASK_INV_DESCRIPTION", 
                    "ITASK_INV_DTSTART", "ITASK_INV_DTEND", "ITASK_INV_LOCATION", "ITASK_INV_CLASS", "ITASK_INV_PRIORITY", "ITASK_INV_CATEGORIES")
                SELECT ?, "TTASK_INV_ATTENDEES", "TTASK_INV_SUMMARY", "TTASK_INV_DESCRIPTION", "TTASK_INV_DTSTART", 
                    "TTASK_INV_DTEND", "TTASK_INV_LOCATION", "TTASK_INV_CLASS", "TTASK_INV_PRIORITY", "TTASK_INV_CATEGORIES"
                FROM "TEMPLATE_TASK_INVITATIONS"
                WHERE "TTASK_ID" = ?`,
                [ttaskMap[ttaskId].ITASK_ID, ttaskId],
            );

            taskInvitationQueries.push(insertQuery);
        }
        await taskInvitationQueries;
    }

    /**
     * Returns invitation for task.
     *
     * @param itaskId
     * @return {BaseCollection}
     */
    get(itaskId) {
        const coll = this.getCollection();
        coll.knex.where("ITASK_ID", itaskId);
        return coll;
    }

    getForTask(itaskId) {
        return globalThis.orm
            .collection(
                "InstanceTaskInvitation",
                this.connection
                    .select()
                    .from(this.tableName)
                    .where("ITASK_ID", itaskId),
            )
            .collectOne();
    }
}
