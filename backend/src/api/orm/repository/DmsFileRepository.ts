// @ts-nocheck
import _ from "lodash";
import fs from "fs";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { ListTags } from "../dms/ListTags";
import * as DMS_TAGS from "../entity/const/dmsTagConsts";
import * as ROLES from "../entity/const/roleConst";
import { UtilsService } from "../../services/UtilsService";
import * as DmsFileConstants from "../entity/const/dmsFileConsts";
import { DmsFile } from "../entity/DmsFile";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { DMSRevisionsExceededException } from "../../../utils/errorHandling/exceptions/dmsRevisionsExceededException";
import { DmsException } from "../../../utils/errorHandling/exceptions/dmsException";
import { FileMetadata } from "../../../client/types/TikaTypes";
import path from "path";

export class DmsFileRepository extends BaseRepository<DmsFile> {
    meta() {
        return {
            tableName: "DMS_FILE",
            entityName: "DmsFile",
            entity: () => new DmsFile(),
            archived: true,
        };
    }

    /**
     * Get all user files visible to user.
     *
     * @param authUser User object from authentication
     * @returns {Promise<U>}
     */
    async getUserFiles(authUser?, tagsToFilter?) {
        const dmsTagRepo = globalThis.orm.repo("dmsTag");

        const tags = await dmsTagRepo.getColumns(["DMST_ID"]).fetchAll();

        // Prepare collection
        const collection = await this.getForUserOptimise(
            authUser,
            tags,
            tagsToFilter,
        );

        return this.prepareTagsFilter(collection, tags);
    }

    /**
     * Extends collection to allow filtering on tags.
     *
     * @param collection
     * @param tagsToFilter {Array.<{DMST_ID: valueOfId}>}
     * @returns BaseCollection
     */
    prepareTagsFilter(collection, tagsToFilter) {
        // Allow filtering by tag columns.
        collection.filteringColumns = {};
        if (
            tagsToFilter &&
            Array.isArray(tagsToFilter) &&
            tagsToFilter.length > 0
        ) {
            tagsToFilter.forEach((tag) => {
                const key = `DTF${tag.DMST_ID}`;
                collection.filteringColumns[key] = {
                    type: "string",
                    key: `"${key}".DMST_VALUE`,
                };

                if (tag.DMST_ID == -4) {
                    collection.filteringColumns.FOLDER_NAME = {
                        type: "string",
                        key: "DF4.FOLDER_NAME",
                    };
                }
            });
        }

        if (!collection.filteringColumns) {
            collection.filteringColumns = {};
        }
        collection.filteringColumns.DMSF_OWNER = {
            type: "string",
            key: `U.USER_DISPLAY_NAME`,
        };
        collection.filteringColumns.DMSF_ID = {
            type: "string",
            key: "DF.DMSF_ID",
        };
        collection.filteringColumns.id = {
            type: "string",
            key: "DF.DMSF_ID",
        };
        collection.filteringColumns.NAME = {
            type: "string",
            key: "DF.NAME",
        };

        collection.orderingColumns = _.cloneDeep(collection.filteringColumns);
        collection.orderingColumns.DMSF_OWNER = {
            type: "string",
            key: "DMSF_OWNER",
        };

        return collection;
    }

    /**
     * Returns last stored revision of file.
     *
     * @param file
     * @param user Is not mandatory.
     * @param lastRevisionId
     * @returns {Request|Promise.<DmsFile>|Promise|*}
     */
    async getLastRevision(file, user, lastRevisionId) {
        if (file.IPROC_ID === null) {
            // Not process file
            const collForUser = this.getCollection();
            // Known lastRevisionId. Revision may have a different name than the original file
            if (lastRevisionId !== null) {
                collForUser.knex.where("DMSF_ID", lastRevisionId);
            } else {
                collForUser.knex
                    .where({
                        USER_ID_INSERT: user.USER_ID,
                        NAME: file.NAME,
                        IS_CURRENT: "Y",
                        IPROC_ID: null,
                    })
                    .orderBy("REVISION", "DESC");
            }
            return collForUser.collectOne();
        }

        const colForProcess = await this.getForProcess(file.IPROC_ID);

        if (lastRevisionId !== null) {
            colForProcess.knex.where("DF.DMSF_ID", lastRevisionId);
        } else {
            colForProcess.knex
                .where("NAME", file.NAME)
                .andWhere("IS_CURRENT", "Y")
                .orderBy("REVISION", "DESC");
        }

        return colForProcess.collectOne();
    }

    /**
     * Filter by index in current files.
     *
     * @param status
     * @param onlyCurrent
     * @returns {BaseCollection}
     */
    getByToIndex(status, onlyCurrent = true) {
        const collection = this.getCollection();

        if (Array.isArray(status)) {
            collection.knex
                .from(this.tableName)
                .whereIn("TO_INDEX", Array.isArray(status) ? status : [status]);
        } else {
            collection.knex.from(this.tableName).where("TO_INDEX", status);
        }

        if (onlyCurrent) {
            collection.knex = collection.knex.where("IS_CURRENT", "Y");
        }

        return collection;
    }

    /**
     * Parse metadata from file with help of Tika.
     *
     * @param file
     * @param metadataOnly
     * @returns {Promise.<T>|Promise<U>}
     */
    async parseMetadata(file, metadataOnly?, isZip?) {
        try {
            let response: FileMetadata;
            const filePath = file.getAbsoluteFilePath(
                globalThis.dynamicConfig.dms.storagePath,
            );

            if (metadataOnly) {
                response =
                    await globalThis.container.client.tika.parseMetadataOnly(
                        filePath,
                        file.NAME,
                    );
            } else {
                response =
                    await globalThis.container.service.file.fileMetadataServiceModule.parse(
                        filePath,
                        file.NAME,
                        isZip,
                    );
            }

            if (response && response.contentType) {
                file.FILE_TYPE = response.contentType;
            }

            return response;
        } catch (error: any) {
            // Tika service is not running (accepted situation)
            if (error.code == "ECONNREFUSED") {
                throw new DmsException(
                    "Tika service is not running!",
                    "TIKA_NOT_RUNNING",
                );
            } else {
                // or parsing failed
                throw new DmsException(
                    `Tika parsing failed! ${error.message}`,
                    "TIKA_PARSING_FAILED",
                );
            }
        }
    }

    /**
     * Sets the current (new) file as a revision for the given (old) file
     *
     * @param oldFile
     * @param newFile
     */
    async storeAsRevision(oldFile, newFile) {
        if (newFile.DMSF_ID_PREV && newFile.DMSF_ID_PREV != oldFile.DMSF_ID) {
            throw new Error(
                "Previous revision file cannot be changed. It has another revision yet.",
            );
        }

        if (!oldFile.DMSF_ID || newFile.DMSF_ID == oldFile.DMSF_ID) {
            throw new Error(
                "Supplied file cannot be used as revision. Is the same file.",
            );
        }

        newFile.REVISION = oldFile.REVISION + 1;
        if (
            newFile.REVISION > globalThis.dynamicConfig.dms.maximumRevisionFiles
        ) {
            throw new DMSRevisionsExceededException();
        }
        newFile.DMSF_ID_PREV = oldFile.DMSF_ID;

        oldFile.IS_CURRENT = "N";

        await super.store(oldFile);

        return super.store(newFile);
    }

    async store(file, lastRevFile = null) {
        if (file.NAME) {
            // @t3b-1593 zakazani ukladani nepovolenych znaku v nazvu DMS souboru
            file.NAME = UtilsService.sanitizeFileName(file.NAME);
        }

        if (!file.DMSF_ID) {
            if (file.IPROC_ID) {
                const result = await this.connection
                    .select("IPROC_DMS_VISIBILITY")
                    .from("INSTANCE_PROCESSES")
                    .where({ IPROC_ID: file.IPROC_ID });

                const visibility = result[0]?.IPROC_DMS_VISIBILITY;

                if (visibility && visibility.indexOf("M") > -1) {
                    file.VIEW_IN_MAIN = "Y";
                }

                if (visibility && visibility.indexOf("S") > -1) {
                    file.VIEW_IN_SUB = "Y";
                }
            }
        }

        if (lastRevFile) {
            return this.storeAsRevision(lastRevFile, file);
        }

        await super.store(file);
        return file;
    }

    /**
     *
     * @param filename
     * @param user
     * @param iprocId
     * @param itaskId
     * @param uploadedFile
     * @param size
     * @param metadata
     * @param lastRevisionId  Explicitly fileId to mark as its revision
     * @param processVars Process variables to fill metadata if necessary
     * @param deleteSource
     * @param forceSource
     * @returns {Promise}
     */
    async saveFile(
        filename: string | undefined,
        user: any,
        iprocId: number | null = null,
        itaskId: number | null = null,
        uploadedFile: any,
        size: number | null,
        metadata: any[] = [],
        lastRevisionId: number | null = null,
        processVars: any[] = [],
        deleteSource: boolean = true,
        forceSource?: boolean,
    ): Promise<{
        result: boolean;
        id: any;
        name: any;
        message: any[];
    }> {
        globalThis.tasLogger.info(
            `Saving file ${filename} by ${user.USER_NAME}`,
            {
                iproc_id: iprocId,
                itaskId,
            },
        );
        const dmsFileRepo = globalThis.orm.repo("dmsFile", this.connection);
        const dmsTagRepo = globalThis.orm.repo("dmsTag", this.connection);
        const processRepo = globalThis.orm.repo("process", this.connection);
        const dmsFileTagRepo = globalThis.orm.repo(
            "dmsFileTag",
            this.connection,
        );
        const file = dmsFileRepo.getEntity();
        let parsingInfo;
        let indexingInfo;
        let process = null;
        const listTags = new ListTags();

        file.NAME = UtilsService.sanitizeFileName(filename);

        const fileExtensionIsAllowed = UtilsService.checkFileExtension(
            file.NAME,
        );

        if (!fileExtensionIsAllowed) {
            throw new UserException(
                "Illegal file extension",
                "LACK_OF_PERMISSIONS",
            );
        }

        file.USER_ID_INSERT = user.USER_ID;
        file.USER_ID_INSERT_VICE = user.isViced() ? user.PRIMARY_USER_ID : null;
        file.DATETIME_INSERT = new Date();
        file.REVISION = 1;
        file.IS_CURRENT = "Y";
        file.IPROC_ID = iprocId;
        file.ITASK_ID = itaskId;
        file.IS_ENCRYPTED =
            globalThis.dynamicConfig.dms.security.encryptContent === true
                ? "Y"
                : "N"; // Is file encrypted ?
        // Default visibility for included files. M - visible in main proc., S - visible in subproc., SM - both
        file.VIEW_IN_MAIN = "N";
        file.VIEW_IN_SUB = "N";

        // If no files were uploaded, respond with an error.
        if (!uploadedFile || typeof uploadedFile === "undefined") {
            throw new InternalException("No file was uploaded");
        }

        // throws error if missing process
        if (iprocId !== null) {
            // file is private without process
            process = await processRepo.collectProcess(iprocId, true);

            // check settings for inserting file
            if (process.IPROC_STATUS == "D") {
                if (
                    !globalThis.dynamicConfig.dms.insertWhenFinished &&
                    !user.isDmsAdmin()
                ) {
                    throw new UserException(
                        "Lack of permissions!",
                        "LACK_OF_PERMISSIONS",
                    );
                }
            }
        }

        let finalFileName;

        try {
            //check file type
            const type = await dmsFileRepo.getFileTypeFromExtension(
                file.NAME.split(".").pop(),
            );
            file.FILE_TYPE = type;
            const lastRevFile = await dmsFileRepo.getLastRevision(
                file,
                user,
                lastRevisionId,
            );

            //get revision if any
            if (lastRevFile) {
                const { REVISION, IPROC_ID } = lastRevFile;
                file.REVISION = REVISION + 1;
                // Don't change the IPROC_ID, it would break VIEW_IN_MAIN and VIEW_IN_SUB visibility
                file.IPROC_ID = IPROC_ID;

                try {
                    if (
                        globalThis.dynamicConfig.dms.fulltext !== null &&
                        iprocId !== null
                    ) {
                        await globalThis.container.client.elastic.elasticSearch.update(
                            file.DMSF_ID,
                            { doc: "N" },
                        );
                    }
                } catch (e) {
                    globalThis.tasLogger.error(e.message, {
                        e,
                    });
                }
            }

            //store file (without transaction)
            file.src =
                forceSource ||
                globalThis.fileStorage.generateFilePath(
                    file.NAME,
                    file.REVISION,
                    file.IPROC_ID,
                );

            try {
                const storedFile = await dmsFileRepo.store(file, lastRevFile);
                finalFileName = storedFile.NAME;
                const fileSize = typeof size !== "undefined" ? size : null;
                forceSource
                    ? true
                    : await globalThis.fileStorage.createFile(
                          file.src,
                          uploadedFile,
                          fileSize,
                          deleteSource,
                      );
            } catch (e) {
                const absoluteFilePath = file.getAbsoluteFilePath(
                    globalThis.dynamicConfig.dms.storagePath,
                );
                // delete the file from .tmp/uploads and from storage
                if (
                    deleteSource !== false &&
                    (await UtilsService.exists(uploadedFile))
                ) {
                    await fs.promises.unlink(uploadedFile);
                }

                if (await UtilsService.exists(absoluteFilePath)) {
                    await fs.promises.unlink(absoluteFilePath);
                }

                globalThis.tasLogger.error(e.message, {
                    e,
                });

                throw e;
            }

            //parsing and preparing tags
            if (
                globalThis.dynamicConfig.dms.tikaEnabled &&
                globalThis.dynamicConfig.dms.tikaUrl !== null
            ) {
                const metadataOnly = globalThis.dynamicConfig.dms.cronIndexing
                    ? true
                    : false;
                const isZip = file.FILE_TYPE === "application/zip";

                try {
                    const parsedMetadata = await dmsFileRepo.parseMetadata(
                        file,
                        metadataOnly,
                        isZip,
                    );
                    listTags.setParsedMetadata(parsedMetadata);
                } catch (e) {
                    if (
                        e.codeName == "TIKA_NOT_RUNNING" ||
                        e.codeName == "TIKA_PARSING_FAILED"
                    ) {
                        parsingInfo = e.codeName;
                    }

                    globalThis.tasLogger.error(e.message, {
                        e,
                    });

                    listTags.setParsedMetadata({});
                }
            }

            //set Tas metadata
            const tprocName = process ? process.raw.TPROC_NAME : null;
            const iprocName = process ? process.IPROC_NAME : null;
            listTags.setTasMetadata(
                processVars,
                tprocName,
                iprocName,
                user.ORGANIZATION.ORGSTR_NAME,
                user.USER_NAME,
            );

            //Choose base set of tags for file depends on revision of file
            if (lastRevFile) {
                listTags.setList(
                    await dmsFileTagRepo
                        .getForFile(lastRevFile.id)
                        .collectAll(),
                );

                const revLogicalTypeId = await dmsFileTagRepo.getLogicalTypeId(
                    lastRevFile.id,
                );

                listTags.mergeList(
                    await dmsFileTagRepo
                        .getBaseTagsForFile(revLogicalTypeId)
                        .collectAll(),
                );
            } else {
                const logicalTypeId = await dmsTagRepo.getDefaultLogicalTagId();
                listTags.setList(
                    await dmsFileTagRepo
                        .getBaseTagsForFile(logicalTypeId)
                        .collectAll(),
                );
            }

            //cron indexing
            if (globalThis.dynamicConfig.dms.cronIndexing && iprocId !== null) {
                file.TO_INDEX = "Y"; // index later with cron
                await this.store(file); // update file info about indexing
            }

            //Indexing file
            if (lastRevFile) {
                listTags.fillValues(
                    await dmsFileTagRepo.getForFile(lastRevFile.id).fetchAll(),
                ); // fill values from revision
            }
            listTags.fillDefaultValues(); // use default values
            listTags.fillWithMetadata(); // fill mined values
            listTags.fillValues(metadata); // if some metadata come from user, overwrite the defaults

            const listTagsToSave = listTags.exportTagsToSave(
                file.DMSF_ID,
                user.USER_ID,
            );

            // indexing
            for (const listTag of listTagsToSave) {
                await dmsFileTagRepo.store(listTag);
            }

            if (
                globalThis.dynamicConfig.dms.fulltext !== null &&
                !globalThis.dynamicConfig.dms.cronIndexing &&
                iprocId !== null
            ) {
                //indexing not allowed in private files
                try {
                    await dmsFileRepo.indexDocument(
                        listTags.exportTagsToIndex(file),
                        file,
                        parsingInfo,
                        lastRevFile,
                    );
                } catch (e) {
                    //ignore some type of errors (file uploaded, but not indexed, parsed)
                    if (
                        e.codeName == "INDEXING_SERVICE_NOT_RUNNING" ||
                        e.codeName == "INDEXING_FAILED"
                    ) {
                        indexingInfo = e.codeName;
                    }

                    globalThis.tasLogger.error(e.message, {
                        e,
                        iproc_id: iprocId,
                    });
                }
            }

            return {
                result: true,
                id: file.DMSF_ID,
                name: finalFileName,
                message: [parsingInfo, indexingInfo],
            };
        } catch (e) {
            globalThis.tasLogger.error(`Failed to file by ${user.USER_NAME}`, {
                iproc_id: iprocId,
                itaskId,
            });

            throw e;
        }
    }

    /**
     * getFileTypeFromExtension
     *
     * @param ext - extension without dot
     */
    async getFileTypeFromExtension(ext) {
        const first = await this.connection
            .select("FILE_TYPE")
            .from("DMS_FILE_TYPE")
            .where("EXTENSIONS", "like", `%${ext}%`)
            .first();

        return first ? first.FILE_TYPE : "application/octet-stream";
    }

    /**
     * Index document.
     * Not called async anymore.
     * Throws error while fails.
     *
     * @param metadata
     * @param file
     * @param parsingError
     * @param lastRevFile Last revision of file, if any
     */
    async indexDocument(metadata, file, parsingError?, lastRevFile = null) {
        try {
            await globalThis.container.client.elastic.elasticSearch.index(
                file.DMSF_ID,
                metadata,
            );

            file.IS_FULL_INDEXED = !parsingError ? "Y" : "N";
            file.TO_INDEX = "N";
            file.DATETIME_INDEXED = new Date();

            if (lastRevFile) {
                await globalThis.container.client.elastic.elasticSearch.update(
                    file.DMSF_ID,
                    { doc: "N" },
                );
            }

            return await this.store(file);
        } catch (e) {
            globalThis.tasLogger.error(
                `Failed to index document ${file.NAME}.`,
                {
                    iproc_id: file.IPROC_ID,
                    e,
                    file,
                },
            );

            file.IS_FULL_INDEXED = "N";
            file.TO_INDEX = "E";
            file.DATETIME_INDEXED = null;

            await this.store(file);
            throw e;
        }
    }

    /**
     * Checks user rights for file.
     * Including main- and sub- processes specific setting.
     *
     * @param dmsfId
     * @param user
     * @param {bool} throwError Throws error if user doesn't have right instead of return false.
     * @returns {boolean}
     */
    async hasFileRights(dmsfId, user, throwError = false) {
        const externalRightsRepo = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );
        let file;
        const { USER_ID: userId } = user;

        try {
            file = await this.get(dmsfId); // returns only basic columns
        } catch (_e) {
            throw new UserException("File doesn't exist.", "FILE_NOT_FOUND");
        }

        // file is private, not bound with process
        if (file.IPROC_ID === null) {
            if (userId === file.USER_ID_INSERT) {
                return true;
            }
            if (throwError) {
                throw new UserException(
                    "Lack of permissions.",
                    "LACK_OF_PERMISSIONS",
                );
            }
            return false;
        }

        if (userId === file.USER_ID_INSERT) {
            return true;
        }

        const hasElevatedRole = user.hasElevatedRole();

        // Check direct access
        const hasDirectAccess = await externalRightsRepo.hasProcessRights(
            userId,
            file.IPROC_ID,
            hasElevatedRole,
        );

        if (hasDirectAccess) {
            return true;
        }

        if (throwError) {
            throw new UserException(
                "Lack of permissions!",
                "LACK_OF_PERMISSIONS",
            );
        }

        return false;
    }

    async connectByPrior(tableName, idColName, parentColName, id) {
        const ids = [];

        const getParent = async (pid) => {
            try {
                const result = await globalThis.database
                    .select(parentColName)
                    .from(tableName)
                    .where(idColName, pid);

                if (result && Array.isArray(result) && result.length === 1) {
                    return result[0][parentColName];
                }
                return null;
            } catch (err) {
                globalThis.tasLogger.error(err);
            }
        };

        const recursive = async (startId) => {
            if (startId == null) {
                return ids;
            }

            ids.push(startId);
            const pid = await getParent(startId);
            return recursive(pid);
        };

        return await recursive(id);
    }

    /**
     * Get all revisions of file.
     *
     * @param dmsfId
     * @param {Array} tagsToSelect Array of DMST_ID
     * @param {Object} tagsToFilter Object of DMST_ID => value
     */
    async getListOfRevisions(dmsfId, tagsToSelect?, tagsToFilter?) {
        const ids = await this.connectByPrior(
            "DMS_FILE",
            "DMSF_ID",
            "DMSF_ID_PREV",
            dmsfId,
        );
        return this.getAll(tagsToSelect, tagsToFilter, ids);
    }

    /**
     * Method to retrieve all files ignoring user rights.
     * Suitable for internal methods without propagating to users or needs check rights first.
     *
     * @param {Array.<{DMST_ID: valueOfId}>} tagsToSelect Array of object DMST_ID => valueOfId
     * @param {Object} tagsToFilter Object of DMST_ID => value
     * @param {Array} dmsfId Array of dmsfId or single dmsfId to filter
     * @param {Number} userId
     * @returns {Promise.<BaseCollection>}
     */
    async getAll(tagsToSelect?, tagsToFilter?, dmsfId?, userId?) {
        const extraColumns = [];

        // Add translations
        const iProcAttrs = globalThis.orm
            .repo("Process")
            .getEntity()
            .attributes();
        const tProcAttrs = globalThis.orm
            .repo("TemplateProcess")
            .getEntity()
            .attributes();

        Object.keys(iProcAttrs).forEach((attrName) => {
            if (iProcAttrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
            }
        });

        Object.keys(tProcAttrs).forEach((attrName) => {
            if (tProcAttrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        const columns = [
            "DF.DMSF_ID",
            "DF.NAME",
            "DF.REVISION",
            "DT.DMST_VALUE as V-4",
            "DF.USER_ID_INSERT",
            "DF.USER_ID_INSERT_VICE",
            "DF.DMSF_SRC",
            "DF.IS_CURRENT",
            "DF.IS_DELETED",
            "DF.FILE_TYPE",
            "DF.VIEW_IN_MAIN",
            "DF.USER_ID_DELETED",
            "DF.VIEW_IN_SUB",
            "DF.DATETIME_DELETED",
            "DF.DATETIME_INSERT",
            "DF.TO_INDEX",
            "DF.IS_FULL_INDEXED",
            "DF.IS_ENCRYPTED",
            "DF.DATETIME_INDEXED",
            "DF.DMSF_ID_PREV",
            "IP.IPROC_ID",
            "IP.IPROC_NAME",
            "TP.TPROC_NAME",
            globalThis.database.raw(`"U"."USER_DISPLAY_NAME" as "DMSF_OWNER"`),
            globalThis.database.raw(
                `"VICE"."USER_DISPLAY_NAME" as "DMSF_OWNER_VICE"`,
            ),
        ].concat(extraColumns);

        if (Array.isArray(tagsToSelect)) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;
                const colName = `${alias}."DMST_VALUE" as ${alias}`;
                columns.push(globalThis.database.raw(colName));

                // rename folder tag
                if (tag.DMST_ID === DMS_TAGS.TYPE_FOLDER) {
                    columns.push(
                        globalThis.database.raw(`"DF4"."FOLDER_NAME"`),
                    );
                }
            });
        }

        let knex = this.connection;
        if (userId) {
            knex = knex.with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            });
        }

        knex = knex
            .select(columns)
            // .distinct() // This causes massive processing bottleneck, is it really needed? Find a different solution.
            .from(`${this.tableName} as DF`)
            .leftJoin("INSTANCE_PROCESSES as IP", "DF.IPROC_ID", "IP.IPROC_ID")
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .joinRaw(
                `left join "DMS_FILE_TAG" "DT" on ("DT"."DMSF_ID" = "DF"."DMSF_ID" and "DT"."DMST_ID" = -4)`,
            )
            .leftJoin("USERS as U", "U.USER_ID", "DF.USER_ID_INSERT")
            .leftJoin(
                "USERS as VICE",
                "VICE.USER_ID",
                "DF.USER_ID_INSERT_VICE",
            );

        // if array or DMSF_ID is specified
        if (dmsfId) {
            knex.whereIn(
                "DF.DMSF_ID",
                Array.isArray(dmsfId) ? dmsfId : [dmsfId],
            );
        }

        if (Array.isArray(tagsToSelect)) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;
                knex.joinRaw(
                    `left join "DMS_FILE_TAG" ${alias} on ${alias}."DMSF_ID" = "DF"."DMSF_ID" and ${alias}."DMST_ID" = ${tag.DMST_ID}`,
                );

                // rename folder tag
                if (tag.DMST_ID === DMS_TAGS.TYPE_FOLDER) {
                    knex.joinRaw(
                        `left join "DMS_FOLDER" "DF4" on ('' ${globalThis.orm.db.concat()} "DF4"."FOLDER_ID"${globalThis.dynamicConfig.db.client === "postgresql" ? "::varchar" : ""} = ${alias}."DMST_VALUE")`,
                    );
                }

                // filtering by tags
                if (tagsToFilter) {
                    const value = tagsToFilter[tag.DMST_ID];
                    if (value) {
                        knex.where(
                            `${alias}.DMST_VALUE`,
                            tagsToFilter[tag.DMST_ID],
                        );
                    }
                }
            });
        }

        return await this.createCollection(knex);
    }

    /**
     * Optimized with one-time sub- and processes retrieving.
     * It's more faster but creates non-cacheable statements.
     *
     * @param authUser User object from authentication
     * @param tagsToSelect {Array.<{DMST_ID: valueOfId}>}
     * @returns {*}
     */
    async getForUserOptimise(authUser, tagsToSelect, tagsToFilter) {
        const columns = [
            "DF.DMSF_ID",
            "IP.IPROC_ID",
            "IP.IPROC_NAME",
            "IP.IPROC_STATUS",
            "IP.IPROC_INST_OWNER_USER_ID",
            "TP.TPROC_NAME",
            "DF.NAME",
            "DF.REVISION",
            "DT.DMST_VALUE as V-4",
            "DF.USER_ID_INSERT",
            "DF.USER_ID_INSERT_VICE",
            "DF.DMSF_SRC",
            "DF.IS_CURRENT",
            "DF.IS_DELETED",
            "DF.FILE_TYPE",
            "DF.VIEW_IN_MAIN",
            "DF.USER_ID_DELETED",
            "DF.VIEW_IN_SUB",
            "DF.DATETIME_DELETED",
            "DF.DATETIME_INSERT",
            "DF.TO_INDEX",
            "DF.IS_FULL_INDEXED",
            "DF.DATETIME_INDEXED",
            "DF.DMSF_ID_PREV",
            globalThis.database.raw(`"U"."USER_DISPLAY_NAME" as "DMSF_OWNER"`),
            globalThis.database.raw(
                `"VICE"."USER_DISPLAY_NAME" as "DMSF_OWNER_VICE"`,
            ),
        ];
        if (tagsToSelect && Array.isArray(tagsToSelect)) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;
                const colName = `${alias}."DMST_VALUE" as ${alias}`;
                columns.push(globalThis.database.raw(colName));

                if (tag.DMST_ID == -4) {
                    columns.push(
                        globalThis.database.raw(`"DF4"."FOLDER_NAME"`),
                    );
                }
            });
        }

        const userRepo = globalThis.orm.repo("user", this.connection);
        const isInspector = await userRepo.hasRole(
            authUser.USER_ID,
            ROLES.INSPECTOR,
        );
        const isGlobalSupervisor = await userRepo.hasRole(
            authUser.USER_ID,
            ROLES.GLOBAL_SUPERVISOR,
        );

        const mainProcListKnexQuery = this.connection
            .pluck("IPROC_ID")
            .from("DMS_FILE")
            .whereNotNull("IPROC_ID");

        const dmsFileCollection = globalThis.orm.collection(
            "DmsFile",
            this.connection,
        );

        // Inspector and Supervisor ignore the Process rights
        if (!isInspector && !isGlobalSupervisor) {
            dmsFileCollection.knex = dmsFileCollection.knex.with(
                "SINGLE_USER_ROLES",
                (builder) => {
                    builder
                        .select("SUR.ROLE_ID")
                        .from("USER_ROLES as SUR")
                        .where("SUR.USER_ID", authUser.USER_ID);
                },
            );

            mainProcListKnexQuery.with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", authUser.USER_ID);
            });

            mainProcListKnexQuery.whereIn(
                "IPROC_ID",
                globalThis.orm
                    .repo("externalRight", this.connection)
                    .getUserProcesses(authUser.USER_ID),
            );
        }

        const mainProcListKnexQueryResult = await mainProcListKnexQuery;
        if (globalThis.dynamicConfig.dms.processRelationsEnabled) {
            dmsFileCollection.knex = dmsFileCollection.knex.withRecursive(
                "subprocesses",
                function (builder) {
                    builder
                        .select("IPROC_ID")
                        .from("INSTANCE_PROCESSES")
                        .whereNotNull("IPROC_ID")
                        .whereIn("IPROC_ID", mainProcListKnexQueryResult)
                        .unionAll(function (builderB) {
                            builderB
                                .select(
                                    globalThis.database.raw(
                                        `"subprocessesplus1"."IPROC_ID"`,
                                    ),
                                )
                                .from(
                                    globalThis.database.raw(
                                        `"INSTANCE_PROCESSES" "subprocessesplus1", "subprocesses"`,
                                    ),
                                )
                                .whereRaw(
                                    `"subprocessesplus1"."IPROC_MAIN_IPROC_ID" = "subprocesses"."IPROC_ID"`,
                                );
                        });
                },
            );
        }

        dmsFileCollection.knex = dmsFileCollection.knex
            .select(columns)
            .from(`${this.tableName} as DF`)
            .leftJoin("INSTANCE_PROCESSES as IP", "DF.IPROC_ID", "IP.IPROC_ID")
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            );
                    }); // Fallback to version = 1
            })
            .leftJoin("DMS_FILE_TAG as DT", function () {
                this.on("DT.DMSF_ID", "DF.DMSF_ID").andOn("DT.DMST_ID", -4);
            })
            .leftJoin("USERS as U", "U.USER_ID", "DF.USER_ID_INSERT")
            .leftJoin("USERS as VICE", "VICE.USER_ID", "DF.USER_ID_INSERT_VICE")
            .where(function () {
                this.where(function () {
                    // Main Processes
                    this.whereIn("DF.IPROC_ID", mainProcListKnexQueryResult);

                    if (globalThis.dynamicConfig.dms.processRelationsEnabled) {
                        // Sub Processes
                        this.orWhere((builder) => {
                            builder
                                .where(
                                    "DF.VIEW_IN_MAIN",
                                    DmsFileConstants.VIEW_IN_MAIN_YES,
                                )
                                .whereIn("DF.IPROC_ID", function () {
                                    this.select("IPROC_ID").from(
                                        globalThis.database.raw("subprocesses"),
                                    );
                                });
                        });
                        // Parent processes
                        this.orWhere((builder) => {
                            builder
                                .where(
                                    "DF.VIEW_IN_SUB",
                                    DmsFileConstants.VIEW_IN_SUB_YES,
                                )
                                .whereIn("DF.IPROC_ID", function () {
                                    this.select("IPROC_MAIN_IPROC_ID")
                                        .from("INSTANCE_PROCESSES")
                                        .whereIn(
                                            "IPROC_ID",
                                            mainProcListKnexQueryResult,
                                        );
                                });
                        });
                    }
                }).orWhere("DF.USER_ID_INSERT", authUser.USER_ID);
            });

        if (tagsToSelect) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;

                dmsFileCollection.knex = dmsFileCollection.knex.joinRaw(
                    `left join "DMS_FILE_TAG" ${alias} on ${alias}."DMSF_ID" = "DF"."DMSF_ID" and ${alias}."DMST_ID" = ${tag.DMST_ID}`,
                );

                // Join folder name for folder tag
                if (tag.DMST_ID == -4) {
                    dmsFileCollection.knex = dmsFileCollection.knex.joinRaw(
                        `left join "DMS_FOLDER" "DF4" on ("DF4"."FOLDER_ID"${globalThis.dynamicConfig.db.client === "postgresql" ? "::varchar" : ""} = ${alias}."DMST_VALUE")`,
                    );
                }

                // filtering by tags
                if (tagsToFilter) {
                    const value = tagsToFilter[tag.DMST_ID];
                    if (value) {
                        dmsFileCollection.knex.where(
                            `${alias}.DMST_VALUE`,
                            tagsToFilter[tag.DMST_ID],
                        );
                    }
                }
            });
        }

        return dmsFileCollection;
    }

    /**
     * Retrieve info about file with all tags without checking rights.
     *
     * @param dmsfId
     * @param userId
     * @returns {Promise.<DmsFile>}
     */
    async getFile(dmsfId, _userId) {
        const tagRepo = globalThis.orm.repo("dmsFileTag");

        const coll = await this.getAll("", null, dmsfId);
        const file = await coll.collectOne();

        if (!file || !file.DMSF_SRC || typeof file.DMSF_SRC !== "string") {
            throw new InternalException(
                "Invalid or missing file source.",
                "INVALID_FILE_SRC",
            );
        }

        const filePath = file.src;

        if (file.DMSF_SRC.startsWith(DmsFileConstants.FILE_PREFIX)) {
            file.DMSF_SRC = path.join(
                globalThis.dynamicConfig.dms.storagePath,
                filePath,
            );
        } else {
            file.DMSF_SRC = filePath;
        }

        const tags = await tagRepo.getForFile(dmsfId).fetchAll();

        const data = await tagRepo.castRows(tags, {
            LIST_OF_VALUES: { type: "text" },
        });

        if (Array.isArray(data) && data.length > 0) {
            data.forEach((item) => {
                try {
                    item.LIST_OF_VALUES = JSON.parse(item.LIST_OF_VALUES);
                } catch (_err) {
                    // ignored
                }
            });
        }
        file.TAGS = data;
        return file;
    }

    async copyFileLink(dmsFile, iprocId) {
        const iprocIds = await this.getAllVisibileIprocIds(iprocId);
        const coll = globalThis.orm.collection(
            "DmsFile",
            this.getByName(iprocIds, dmsFile.NAME),
        );
        const foundFile = await coll.collectOne();

        const sourceFileId = dmsFile.DMSF_ID;

        // Copy DMS link entity.
        dmsFile.DMSF_ID = null;
        dmsFile.DMSF_ID_PREV = null;
        dmsFile.IPROC_ID = iprocId;

        let storedFile;
        if (foundFile) {
            dmsFile.IPROC_ID = foundFile.IPROC_ID;
            storedFile = await this.store(dmsFile, foundFile); // save as revision
        } else {
            storedFile = await this.store(dmsFile); // save new file
        }

        const dmsfId = storedFile.DMSF_ID;

        // Copy file tags.
        const tagRepo = globalThis.orm.repo("dmsFileTag", this.connection);
        const fileTags = await tagRepo.getForFile(sourceFileId).collectAll();

        if (Array.isArray(fileTags) && fileTags.length > 0) {
            for (const tag of fileTags) {
                tag.DMSF_ID = dmsfId;
                await tagRepo.store(tag);
            }
            return storedFile;
        }
        return storedFile;
    }

    /**
     * All files uploaded into process (without main- and sub-processes).
     * Ignoring user rights.
     *
     * @param processId
     * @param tagsToSelect
     * @param tagsToFilter
     * @returns {Promise.<BaseCollection>}
     */
    async getForProcessOnly(processId, tagsToSelect?, tagsToFilter?) {
        const coll = await this.getAll(tagsToSelect, tagsToFilter);

        coll.knex.where("DF.IPROC_ID", processId);
        return coll;
    }

    /**
     * All files in process within its sub-processes and main-processes respecting iproc visibility settings.
     * Ignoring user rights.
     *
     * @param processId
     * @param tagsToSelect
     * @param tagsToFilter
     * @param userId
     * @returns {Promise.<BaseCollection>}
     */
    async getForProcess(processId, tagsToSelect, tagsToFilter, user) {
        const procRepo = globalThis.orm.repo("process", this.connection);
        const externalRightsRepo = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );

        const coll = await this.getAll(
            tagsToSelect,
            tagsToFilter,
            null,
            user ? user.USER_ID : null,
        );

        coll.knex
            .with(
                "SUB_PROCESSES",
                globalThis.database.raw(procRepo.withSubProcesses(), {
                    IPROC_ID: processId,
                }),
            )
            .with(
                "MAIN_PROCESSES",
                globalThis.database.raw(procRepo.withMainProcesses(), {
                    IPROC_ID: processId,
                }),
            )
            .where((mainBuilder) => {
                mainBuilder.where(function () {
                    this.where("DF.IPROC_ID", processId);
                });

                if (globalThis.dynamicConfig.dms.processRelationsEnabled) {
                    mainBuilder
                        .orWhere(function () {
                            this.where("DF.VIEW_IN_SUB", "Y").whereIn(
                                "DF.IPROC_ID",
                                (builder) => {
                                    builder
                                        // Includes self but it doesn't matter
                                        .select("IPROC_ID")
                                        .from("MAIN_PROCESSES");
                                    // TODO: Union instead of orWhere?
                                },
                            );
                        })
                        .orWhere(function () {
                            this.where("DF.VIEW_IN_MAIN", "Y").whereIn(
                                "DF.IPROC_ID",
                                (builder) => {
                                    builder
                                        // Includes self but it doesn't matter
                                        .select("IPROC_ID")
                                        .from("SUB_PROCESSES");
                                },
                            );
                        });
                }
            });

        if (user && globalThis.dynamicConfig.dms.processRelationsEnabled) {
            coll.knex.whereIn("DF.IPROC_ID", function () {
                this.select("IPROC_ID").from(
                    externalRightsRepo
                        .getUserProcesses(user.USER_ID, user.hasElevatedRole())
                        .as("ignored_alias"),
                );
            });
        }

        return coll;
    }

    /**
     * Checks if the file belongs to the provided Process
     *
     * @param {Number|String} identifier - Id or Name of the file
     * @param {Number} processId - Id of the Process
     * @param {Boolean} [onlyForProcess = false] - Should Main and Sub processes be ignored?
     * @param {Boolean} [throwError = false] - Should an Error be thrown if the File does not belong to the Process?
     * @returns {Promise<Number>} Id of the file
     */
    async belongsToProcess(
        identifier,
        processId,
        onlyForProcess = false,
        throwError = false,
    ) {
        // Check if the File belongs to the Process
        const coll = onlyForProcess
            ? await this.getForProcessOnly(processId)
            : await this.getForProcess(processId);
        const processFiles = await coll.collectAll();
        const processFilesIdentifier = UtilsService.isNumericString(identifier)
            ? "DMSF_ID"
            : "NAME";

        // The file does not belong to the Process
        const fileFound = _.find(processFiles, {
            [processFilesIdentifier]:
                processFilesIdentifier === "NAME"
                    ? identifier
                    : Number(identifier),
            IS_CURRENT: DmsFileConstants.CURRENT_YES,
        });
        if (!fileFound) {
            // Throw Error?
            if (throwError) {
                throw new UserException(
                    "The requested file does not belong to this Process or does not exist!",
                    "INVALID_FILE",
                    {
                        processFiles: processFiles.map((item) => ({
                            DMSF_ID: item.DMSF_ID,
                            NAME: item.NAME,
                        })),
                        file: identifier,
                    },
                );
            }
            return false;
        }
        return fileFound;
    }

    getByName(iprocId, name) {
        if (Array.isArray(name) && name.length == 0) {
            return [];
        }

        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("IS_CURRENT", "Y");

        if (Array.isArray(name)) {
            conn.whereIn("NAME", name);
        } else {
            conn.where("NAME", name);
        }

        if (Array.isArray(iprocId)) {
            conn.whereIn("IPROC_ID", iprocId);
        } else {
            conn.where("IPROC_ID", iprocId);
        }

        return conn;
    }

    remove(dmsfId, userId) {
        return this.connection(this.tableName).where("DMSF_ID", dmsfId).update({
            IS_CURRENT: "N",
            IS_DELETED: "Y",
            DATETIME_DELETED: new Date(),
            USER_ID_DELETED: userId,
            IS_FULL_INDEXED: "N",
            TO_INDEX: "N",
            DATETIME_INDEXED: null,
        });
    }

    removePermanently(dmsfId, userId) {
        return this.connection(this.tableName).where("DMSF_ID", dmsfId).update({
            IS_DELETED: "P",
            DATETIME_DELETED: new Date(),
            USER_ID_DELETED: userId,
        });
    }

    async removePhysically(dmsfId) {
        await this.connection("DMS_FILE_TAG")
            .whereIn("DMSF_ID", Array.isArray(dmsfId) ? dmsfId : [dmsfId])
            .delete();

        return this.connection(this.tableName)
            .where("DMSF_ID", dmsfId)
            .delete();
    }

    restore(dmsfId) {
        const toIndex = globalThis.dynamicConfig.dms.cronIndexing ? "Y" : "N";

        return this.connection(this.tableName).where("DMSF_ID", dmsfId).update({
            IS_CURRENT: "Y",
            IS_DELETED: "N",
            DATETIME_DELETED: null,
            USER_ID_DELETED: null,
            TO_INDEX: toIndex,
        });
    }

    /**
     * Returns file contents without checking rights.
     * @param {number} dmsfId
     * @param {string} [returnType = buffer]
     * @returns {*}
     */
    async getFileContents(dmsfId, returnType?: BufferEncoding) {
        const fileEntity = await this.get(dmsfId);
        return await globalThis.container.service.file.fileMetadataServiceModule.fileToBuffer(
            fileEntity.getAbsoluteFilePath(),
            returnType,
        );
    }

    async getAllVisibileIprocIds(iprocId) {
        const procRepo = globalThis.orm.repo("process", this.connection);

        const iprocIdExists = await procRepo.exists("IPROC_ID", iprocId);
        if (!iprocIdExists) {
            throw new UserException(
                `Process with id ${iprocId} doesn't exist.`,
                "PROCESS_NOT_FOUND",
                { iproc_id: iprocId },
            );
        }

        const mainProcs = await this.inMainProcesses(iprocId);
        const subProcs = await this.inSubProcesses(iprocId);
        return _.uniq([...mainProcs, ...subProcs, iprocId]);
    }

    inSubProcesses(iprocId, attachWithClause) {
        const procRepo = globalThis.orm.repo("process", this.connection);
        return procRepo
            .getSubProcesses(iprocId, attachWithClause)
            .whereIn("IPROC_DMS_VISIBILITY", ["M", "SM", "MS"])
            .pluck("IPROC_ID");
    }

    inMainProcesses(iprocId, attachWithClause) {
        const procRepo = globalThis.orm.repo("process", this.connection);
        return procRepo
            .getMainProcesses(iprocId, attachWithClause)
            .whereIn("IPROC_DMS_VISIBILITY", ["S", "SM", "MS"])
            .pluck("IPROC_ID");
    }

    getDmsFilesFromSubProcesses(iprocId, searchRevisions = false) {
        const conn = this.connection
            .select()
            .with(
                "SUB_PROCESSES",
                this.connection.raw(
                    globalThis.orm.repo("process").withSubProcesses(),
                    {
                        IPROC_ID: iprocId,
                    },
                ),
            )
            .from("DMS_FILE")
            .whereIn("IPROC_ID", this.inSubProcesses(iprocId, false));

        if (searchRevisions) {
            return conn
                .orderBy("IS_CURRENT", "desc")
                .orderBy("DATETIME_INSERT", "desc")
                .limit(1);
        }

        return conn.where("IS_CURRENT", "Y");
    }

    getDmsFilesFromMainProcesses(iprocId, searchRevisions = false) {
        const conn = this.connection
            .select()
            .with(
                "MAIN_PROCESSES",
                this.connection.raw(
                    globalThis.orm.repo("process").withMainProcesses(),
                    { IPROC_ID: iprocId },
                ),
            )
            .from("DMS_FILE")
            .whereIn("IPROC_ID", this.inMainProcesses(iprocId, false));

        if (searchRevisions) {
            return conn
                .orderBy("IS_CURRENT", "desc")
                .orderBy("DATETIME_INSERT", "desc")
                .limit(1);
        }

        return conn.where("IS_CURRENT", "Y");
    }

    getDmsEntityByNameFromSubProcess(iprocId, name, searchRevisions = false) {
        return this.getDmsFilesFromSubProcesses(iprocId, searchRevisions).where(
            "NAME",
            name,
        );
    }

    getDmsEntityByNameFromMainProcess(iprocId, name, searchRevisions = false) {
        return this.getDmsFilesFromMainProcesses(
            iprocId,
            searchRevisions,
        ).where("NAME", name);
    }

    async hasFileDeleteRights(dmsfId, userId) {
        const result = await this.connection
            .select(1)
            .from("DMS_FILE as DMSF")
            .leftJoin("INSTANCE_TASK_HISTORY as ITH", (builder) => {
                builder
                    .on("ITH.ITASK_ID", "=", "DMSF.ITASK_ID")
                    /*
                     * t3b-1506 Mazání souboru i po "Jen uložit"
                     */
                    .andOn(
                        "ITH.ITASKH_NOTE",
                        "=",
                        globalThis.database.raw("'Task finished'"),
                    );
            })
            .innerJoin(
                "INSTANCE_PROCESSES as IP",
                "IP.IPROC_ID",
                "DMSF.IPROC_ID",
            )
            .where((builder) => {
                builder
                    .where("DMSF.DMSF_ID", dmsfId)
                    .where("DMSF.USER_ID_INSERT", userId)
                    .where("DMSF.IS_CURRENT", DmsFileConstants.CURRENT_YES)
                    .where("DMSF.REVISION", 1)
                    /*
                     * t3b-788 Neměla by jít smazat příloha, která je přiložena pouze k případu
                     */
                    .whereNotNull("DMSF.ITASK_ID")
                    .where((builder) => {
                        builder
                            .whereNull("ITH.ITASKH_ACTUAL_DATE_FINISH")
                            .orWhereRaw(
                                `"ITH"."ITASKH_ACTUAL_DATE_FINISH" < "DMSF"."DATETIME_INSERT"`,
                            );
                    });
            });

        return result.length > 0;
    }
}
