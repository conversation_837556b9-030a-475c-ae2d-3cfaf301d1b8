// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { RuleDefinition } from "../entity/RuleDefinition";
import { TemplateTask } from "../entity/TemplateTask";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

import * as TPROC_CONSTS from "../entity/const/tprocConsts";
import { TaskVersioning } from "../../versioning/TaskVersioning";

export class TemplateTaskRepository extends BaseRepository<TemplateTask> {
    meta() {
        return {
            tableName: "TEMPLATE_TASKS",
            entityName: "TemplateTask",
            entity: () => new TemplateTask(),
        };
    }

    getTTask(ttaskId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskRepository.getTTask)",
            );
        }

        // Map tvar entities -
        const ttaskEntity = globalThis.orm
            .repo("TemplateTask", this.connection)
            .getEntity();
        const attrs = ttaskEntity.attributes();
        let ttaskCols = Object.keys(attrs);
        ttaskCols = ttaskCols.map((el) => `TT.${el}`);

        const enotMutations = globalThis.orm
            .repo("TemplateTaskEmailNotifs")
            .getEntity()
            .getTranslatedProperties();
        const conn = this.connection
            .select(
                ttaskCols.concat(
                    [
                        // Email notification target
                        "TTEN.TTASK_ENOT_TGT_TYPE",
                        "TTEN.TTASK_ENOT_TGT_TTASK_ID",
                        "TTEN.TTASK_ENOT_TGT",
                        "TTEN.TTASK_ENOT_TGT_ORGSTR_ID",
                        "TTEN.TTASK_ENOT_TGT_ROLE_ID",
                        "TV.TVAR_ID as TTASK_ENOT_TGT_TVAR_ID",
                        globalThis.database.raw(
                            `"ORG"."ORGSTR_NAME" AS "TTASK_ENOT_TGT_ORGSTR"`,
                        ),
                        globalThis.database.raw(
                            `"R"."ROLE_NAME" as "TTASK_ENOT_TGT_ROLE"`,
                        ),
                        // Email notification reply
                        "TTEN.TTASK_ENOT_REPLY_TYPE",
                        "TTEN.TTASK_ENOT_REPLY_TTASK_ID",
                        "TTEN.TTASK_ENOT_REPLY_TARGET",
                        "TTEN.TTASK_ENOT_REPLY_ORGSTR_ID",
                        "TTEN.TTASK_ENOT_REPLY_ROLE_ID",
                        "REPLY_TV.TVAR_ID as TTASK_ENOT_REPLY_TVAR_ID",
                        globalThis.database.raw(
                            `"REPLY_ORG"."ORGSTR_NAME" as "TTASK_ENOT_REPLY_ORGSTR"`,
                        ),
                        globalThis.database.raw(
                            `"REPLY_ROLE"."ROLE_NAME" as "TTASK_ENOT_REPLY_ROLE"`,
                        ),
                        // Email notification blind copy
                        "TTEN.TTASK_ENOT_BLIND_TYPE",
                        "TTEN.TTASK_ENOT_BLIND_TTASK_ID",
                        "TTEN.TTASK_ENOT_BLIND_TARGET",
                        "TTEN.TTASK_ENOT_BLIND_ORGSTR_ID",
                        "TTEN.TTASK_ENOT_BLIND_ROLE_ID",
                        "BLIND_TV.TVAR_ID as TTASK_ENOT_BLIND_TVAR_ID",
                        globalThis.database.raw(
                            `"BLIND_ORG"."ORGSTR_NAME" as "TTASK_ENOT_BLIND_ORGSTR"`,
                        ),
                        globalThis.database.raw(
                            `"BLIND_ROLE"."ROLE_NAME" as "TTASK_ENOT_BLIND_ROLE"`,
                        ),
                        // Email notification plain copy
                        "TTEN.TTASK_ENOT_COPY_TYPE",
                        "TTEN.TTASK_ENOT_COPY_TTASK_ID",
                        "TTEN.TTASK_ENOT_COPY_TARGET",
                        "TTEN.TTASK_ENOT_COPY_ORGSTR_ID",
                        "TTEN.TTASK_ENOT_COPY_ROLE_ID",
                        "COPY_TV.TVAR_ID as TTASK_ENOT_COPY_TVAR_ID",
                        globalThis.database.raw(
                            `"COPY_ORG"."ORGSTR_NAME" as "TTASK_ENOT_COPY_ORGSTR"`,
                        ),
                        globalThis.database.raw(
                            `"COPY_ROLE"."ROLE_NAME" as "TTASK_ENOT_COPY_ROLE"`,
                        ),
                        // General Email notification data
                        "TTEN.TTASK_ENOT_EXTERNAL_LANGUAGE",
                        "TTEN.TTASK_ENOT_SUBJECT",
                        "TTEN.TTASK_ENOT_BODY2",
                        // Invite
                        "TTIN.TTASK_INV_ATTENDEES",
                        "TTIN.TTASK_INV_SUMMARY",
                        "TTIN.TTASK_INV_DESCRIPTION",
                        "TTIN.TTASK_INV_DTSTART",
                        "TTIN.TTASK_INV_DTEND",
                        "TTIN.TTASK_INV_LOCATION",
                        "TTIN.TTASK_INV_CLASS",
                        "TTIN.TTASK_INV_PRIORITY",
                        "TTIN.TTASK_INV_CATEGORIES",
                        // Other
                        globalThis.database.raw(
                            `"ASSU"."USER_DISPLAY_NAME" as "TTASK_ASSESMENT_USER_NAME"`,
                        ),
                        "ASSR.ROLE_NAME as TTASK_ASSESMENT_ROLE_NAME",
                        "ASSESMENT_ORG.ORGSTR_NAME as TTASK_ASSESMENT_ORGSTR_NAME",
                        "TG.TGRAPH_BPMN_TYPE",
                        "TG.TGRAPH_POSITION1_Y",
                        "TG.TGRAPH_POSITION1_X",
                        "TG.TGRAPH_BPMN_EVENT_DEF_TYPE",
                        "TG.TGRAPH_COLOR",
                    ].concat(enotMutations),
                ),
            )
            .from(`${this.tableName} as TT`)
            .leftJoin(
                "TEMPLATE_TASK_EMAIL_NOTIFS as TTEN",
                "TT.TTASK_ID",
                "TTEN.TTASK_ID",
            )
            .leftJoin(
                "TEMPLATE_TASK_INVITATIONS as TTIN",
                "TT.TTASK_ID",
                "TTIN.TTASK_ID",
            )
            .leftJoin("ROLES as R", "R.ROLE_ID", "TTEN.TTASK_ENOT_TGT_ROLE_ID")
            .leftJoin(
                "ROLES as REPLY_ROLE",
                "REPLY_ROLE.ROLE_ID",
                "TTEN.TTASK_ENOT_REPLY_ROLE_ID",
            )
            .leftJoin(
                "ROLES as BLIND_ROLE",
                "BLIND_ROLE.ROLE_ID",
                "TTEN.TTASK_ENOT_BLIND_ROLE_ID",
            )
            .leftJoin(
                "ROLES as COPY_ROLE",
                "COPY_ROLE.ROLE_ID",
                "TTEN.TTASK_ENOT_COPY_ROLE_ID",
            )
            .leftJoin(
                "ORGANIZATION_STRUCTURE as ORG",
                "ORG.ORGSTR_ID",
                "TTEN.TTASK_ENOT_TGT_ORGSTR_ID",
            )
            .leftJoin(
                "ORGANIZATION_STRUCTURE as REPLY_ORG",
                "REPLY_ORG.ORGSTR_ID",
                "TTEN.TTASK_ENOT_REPLY_ORGSTR_ID",
            )
            .leftJoin(
                "ORGANIZATION_STRUCTURE as BLIND_ORG",
                "BLIND_ORG.ORGSTR_ID",
                "TTEN.TTASK_ENOT_BLIND_ORGSTR_ID",
            )
            .leftJoin(
                "ORGANIZATION_STRUCTURE as COPY_ORG",
                "COPY_ORG.ORGSTR_ID",
                "TTEN.TTASK_ENOT_COPY_ORGSTR_ID",
            )
            .leftJoin(
                "ORGANIZATION_STRUCTURE as ASSESMENT_ORG",
                "ASSESMENT_ORG.ORGSTR_ID",
                "TT.TTASK_ASSESMENT_ORGSTR_CNST",
            )
            .leftJoin("TEMPLATE_GRAPH as TG", (tgJoin) => {
                tgJoin
                    .on("TT.TTASK_ID", "TG.TTASK_ID")
                    .andOn(
                        "TG.TGRAPH_VERSION",
                        globalThis.database.raw("?", [version]),
                    );
            })
            .leftJoin("TEMPLATE_VARIABLES as TV", function () {
                this.on("TTEN.TTASK_ENOT_TGT", "TV.TVAR_NAME").andOn(
                    "TV.TPROC_ID",
                    "TT.TPROC_ID",
                );
            })
            .leftJoin("TEMPLATE_VARIABLES as REPLY_TV", function () {
                this.on(
                    "TTEN.TTASK_ENOT_REPLY_TARGET",
                    "REPLY_TV.TVAR_NAME",
                ).andOn("REPLY_TV.TPROC_ID", "TT.TPROC_ID");
            })
            .leftJoin("TEMPLATE_VARIABLES as BLIND_TV", function () {
                this.on(
                    "TTEN.TTASK_ENOT_BLIND_TARGET",
                    "BLIND_TV.TVAR_NAME",
                ).andOn("BLIND_TV.TPROC_ID", "TT.TPROC_ID");
            })
            .leftJoin("TEMPLATE_VARIABLES as COPY_TV", function () {
                this.on(
                    "TTEN.TTASK_ENOT_COPY_TARGET",
                    "COPY_TV.TVAR_NAME",
                ).andOn("COPY_TV.TPROC_ID", "TT.TPROC_ID");
            })
            .leftJoin(
                "USERS as ASSU",
                "TT.TTASK_ASSESMENT_USER_ID",
                "ASSU.USER_ID",
            )
            .leftJoin(
                "ROLES as ASSR",
                "TT.TTASK_ASSESMENT_ROLE_ID",
                "ASSR.ROLE_ID",
            )
            .where("TT.TTASK_ID", ttaskId);

        return globalThis.orm.collection(this, conn);
    }

    getAll() {
        return globalThis.orm.collection(
            this,
            this.connection
                .select([
                    "TT.*",
                    globalThis.database
                        .count("*")
                        .from("TEMPLATE_TASK_JS_CALCULATIONS as TTC")
                        .whereRaw(`"TTC"."TTASK_ID" = "TT"."TTASK_ID"`)
                        .as("TTASK_CALC_COUNT"),
                    "ASSR.ROLE_NAME as TTASK_ASSESMENT_ROLE_NAME",
                    "ORG.ORGSTR_NAME as TTASK_ASSESMENT_ORGSTR_NAME",
                    "TTEN.TTASK_ENOT_BODY2 as TTASK_ENOT_BODY2",
                    "TTEN.TTASK_ENOT_SUBJECT",
                    "TTEN.TTASK_ENOT_TGT_TYPE",
                    "TTEN.TTASK_ENOT_TGT",
                ])
                .from(`${this.tableName} as TT`)
                .leftJoin(
                    "ROLES as ASSR",
                    "TT.TTASK_ASSESMENT_ROLE_ID",
                    "ASSR.ROLE_ID",
                )
                .leftJoin(
                    "ORGANIZATION_STRUCTURE as ORG",
                    "TT.TTASK_ASSESMENT_ORGSTR_CNST",
                    "ORG.ORGSTR_ID",
                )
                .leftJoin(
                    "TEMPLATE_TASK_EMAIL_NOTIFS as TTEN",
                    "TT.TTASK_ID",
                    "TTEN.TTASK_ID",
                ),
        );
    }

    async doCreateTTask(entity, data, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskRepository.doCreateTTask)",
            );
        }

        const ttaskEntity = await this.store(entity, {
            subP: data.ttask_subp_map,
            subR: data.ttask_subr_map,
        });

        const tproc = await globalThis.orm
            .repo("templateProcess", this.connection)
            .get([entity.TPROC_ID, version], ["TPROC_ID", "TPROC_VERSION"]);
        await this.setTTask(tproc, ttaskEntity.TTASK_ID, data, version);

        // Copy instance tasks.
        const tv = new TaskVersioning(this.connection, entity.TTASK_ID);
        await tv.fillInstances(entity.TTASK_ID);

        return entity.TTASK_ID;
    }

    async setTTask(tproc, ttaskId, data, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskRepository.setTTask)",
            );
        }

        // EMAIL NOTIFS
        const templateTaskEmailNotifsRepo = globalThis.orm.repo(
            "templateTaskEmailNotifs",
            this.connection,
        );
        if (data.ttask_type === "N") {
            const attrs = {
                TTASK_ENOT_SUBJECT: data.ttask_enot_subject,
                TTASK_ENOT_BODY2: data.ttask_enot_body,

                // TARGET
                TTASK_ENOT_TGT_TYPE: data.ttask_enot_tgt_type,
                TTASK_ENOT_TGT: data.ttask_enot_tgt,
                TTASK_ENOT_TGT_ORGSTR_ID: data.ttask_enot_tgt_orgstr_id,
                TTASK_ENOT_TGT_ROLE_ID: data.ttask_enot_tgt_role_id,
                TTASK_ENOT_TGT_TTASK_ID: data.ttask_enot_tgt_ttask_id,
                TTASK_ENOT_TGT_TVAR_ID: data.ttask_enot_tgt_tvar_id,

                // REPLY
                TTASK_ENOT_REPLY_TYPE: data.ttask_enot_reply_type,
                TTASK_ENOT_REPLY_TARGET: data.ttask_enot_reply_target,
                TTASK_ENOT_REPLY_ORGSTR_ID: data.ttask_enot_reply_orgstr_id,
                TTASK_ENOT_REPLY_ROLE_ID: data.ttask_enot_reply_role_id,
                TTASK_ENOT_REPLY_TTASK_ID: data.ttask_enot_reply_ttask_id,
                TTASK_ENOT_REPLY_TVAR_ID: data.ttask_enot_reply_tvar_id,

                // BLIND
                TTASK_ENOT_BLIND_TYPE: data.ttask_enot_blind_type,
                TTASK_ENOT_BLIND_TARGET: data.ttask_enot_blind_target,
                TTASK_ENOT_BLIND_ORGSTR_ID: data.ttask_enot_blind_orgstr_id,
                TTASK_ENOT_BLIND_ROLE_ID: data.ttask_enot_blind_role_id,
                TTASK_ENOT_BLIND_TTASK_ID: data.ttask_enot_blind_ttask_id,
                TTASK_ENOT_BLIND_TVAR_ID: data.ttask_enot_blind_tvar_id,

                // COPIES
                TTASK_ENOT_COPY_TYPE: data.ttask_enot_copy_type,
                TTASK_ENOT_COPY_TARGET: data.ttask_enot_copy_target,
                TTASK_ENOT_COPY_ORGSTR_ID: data.ttask_enot_copy_orgstr_id,
                TTASK_ENOT_COPY_ROLE_ID: data.ttask_enot_copy_role_id,
                TTASK_ENOT_COPY_TTASK_ID: data.ttask_enot_copy_ttask_id,
                TTASK_ENOT_COPY_TVAR_ID: data.ttask_enot_copy_tvar_id,

                // LANGUAGE
                TTASK_ENOT_EXTERNAL_LANGUAGE: data.ttask_enot_external_language,
            };

            // Add mutations
            globalThis.dynamicConfig.langs.forEach((lang) => {
                attrs[`TTASK_ENOT_BODY2_${lang.toUpperCase()}`] =
                    data[`ttask_enot_body_${lang}`];
                attrs[`TTASK_ENOT_SUBJECT_${lang.toUpperCase()}`] =
                    data[`ttask_enot_subject_${lang}`];
            });

            await templateTaskEmailNotifsRepo.setTTaskEmailNotification(
                ttaskId,
                attrs,
            );
        } else {
            await templateTaskEmailNotifsRepo.deleteForTTask(ttaskId);
        }

        // INVITATION
        const templateTaskInvitationRepo = globalThis.orm.repo(
            "templateTaskInvitation",
            this.connection,
        );
        if (data.ttask_type === "I") {
            let start = null;
            let end = null;
            if (
                data.ttask_inv_dtstart !== null &&
                typeof data.ttask_inv_dtstart !== "undefined"
            ) {
                start = new Date(data.ttask_inv_dtstart);
                if (start === "Invalid Date") {
                    start = null;
                }
            }
            if (
                data.ttask_inv_dtend !== null &&
                typeof data.ttask_inv_dtend !== "undefined"
            ) {
                end = new Date(data.ttask_inv_dtend);
                if (end === "Invalid Date") {
                    end = null;
                }
            }

            const TTASK_INV_ATTENDEES = data.ttask_inv_attendees;
            const TTASK_INV_SUMMARY = data.ttask_inv_summary;
            const TTASK_INV_DESCRIPTION = data.ttask_inv_description;
            const TTASK_INV_DTSTART = start;
            const TTASK_INV_DTEND = end;
            const TTASK_INV_LOCATION = data.ttask_inv_location;
            const TTASK_INV_CLASS = data.ttask_inv_class;
            const TTASK_INV_PRIORITY = data.ttask_inv_priority;
            const TTASK_INV_CATEGORIES = data.ttask_inv_categories;

            await templateTaskInvitationRepo.setTTaskInvitation(
                ttaskId,
                TTASK_INV_ATTENDEES,
                TTASK_INV_SUMMARY,
                TTASK_INV_DESCRIPTION,
                TTASK_INV_DTSTART,
                TTASK_INV_DTEND,
                TTASK_INV_LOCATION,
                TTASK_INV_CLASS,
                TTASK_INV_PRIORITY,
                TTASK_INV_CATEGORIES,
            );
        } else {
            await templateTaskInvitationRepo.deleteForTTask(ttaskId);
        }

        // COMPLETION
        if (data.ttask_completions && Array.isArray(data.ttask_completions)) {
            const templateTaskCompletionRepo = globalThis.orm.repo(
                "TemplateTaskCompletion",
                this.connection,
            );
            const completion = [];
            data.ttask_completions.forEach((com) => {
                completion.push({
                    tvar_id: com.tvar_id,
                    operator: com.ttc_operator,
                    value: com.ttc_value,
                    ttc_concat_operator: com.ttc_concat_operator,
                    ttc_cancel_flag: com.ttc_cancel_flag,
                });
            });
            await templateTaskCompletionRepo.setTTaskCompletion(
                ttaskId,
                completion,
                version,
            );
        }

        // CALCULATIONS
        const templateTaskCalculationRepository = globalThis.orm.repo(
            "TemplateTaskJSCalculation",
            this.connection,
        );
        if (data.ttask_operations && Array.isArray(data.ttask_operations)) {
            await templateTaskCalculationRepository.create(
                ttaskId,
                data.tproc_id,
                data.ttask_operations,
                version,
            );
        }

        // TEMPLATE_VAR_USAGE
        const tvarUsageRepo = globalThis.orm.repo(
            "templateTaskVarUsage",
            this.connection,
        );
        if (!data.usages) {
            data.usages = [];
        }
        if (data.ttask_var_mapping && data.ttask_var_mapping) {
            await tvarUsageRepo.setTTaskVariableUsage(
                ttaskId,
                data.tproc_id,
                data.ttask_var_mapping,
                version,
            );
        }

        // TEMPLATE_TASK_MASS_USAGE
        const tvarMassRepo = globalThis.orm.repo(
            "templateTaskMassUsage",
            this.connection,
        );
        if (data.ttask_mass_mapping && data.ttask_mass_mapping) {
            await tvarMassRepo.setTTaskMassUsage(
                ttaskId,
                data.tproc_id,
                data.ttask_mass_mapping,
                version,
            );
        }

        // GRAPH
        const graphRepo = globalThis.orm.repo("templateGraph", this.connection);
        if (
            data.tgraph_bpmn_type ||
            data.tgraph_color ||
            data.tgraph_position1_x
        ) {
            // const ttask = await this.get(ttaskId);
            const gentity = graphRepo.entity;

            const attrs = gentity.attributes();
            const attrKeys = Object.keys(attrs);

            // Fill user entity
            attrKeys.forEach((key) => {
                const lk = key.toLowerCase();

                if (typeof data[lk] !== "undefined") {
                    gentity[key] = data[lk];
                }
            });

            gentity.TTASK_ID = ttaskId;
            gentity.TPROC_ID = tproc.TPROC_ID;
            gentity.TGRAPH_VERSION = version;

            const graphs = await graphRepo.connection
                .select()
                .from(graphRepo.tableName)
                .where("TTASK_ID", ttaskId)
                .where("TPROC_ID", tproc.TPROC_ID)
                .where("TGRAPH_VERSION", version);
            if (Array.isArray(graphs) && graphs.length > 0) {
                gentity.TGRAPH_ID = graphs[0].TGRAPH_ID;
            }
            return await graphRepo.store(gentity);
        }
    }

    async store(entity: Partial<TemplateTask>, subMapping) {
        if (entity.TTASK_ID !== undefined && entity.TTASK_ID !== null) {
            return this.update(entity, subMapping);
        }

        entity.TTASK_ID = await super.store(entity);

        if (entity.TTASK_SUBPROCESS_TPROC_ID > 0) {
            entity.TTASK_EVENT_WAIT = 2;
            entity.TTASK_EVENT = `$SUBP_${entity.TTASK_ID}`;
            await super.store(entity);
        }

        let event = {};
        if (entity.TTASK_SUBPROCESS_TPROC_ID > 0) {
            const subP = await this.connection
                .select("TPROC_ID", "TPROC_NAME")
                .from("TEMPLATE_PROCESSES")
                .whereIn("TPROC_ID", [
                    entity.TTASK_SUBPROCESS_TPROC_ID,
                    entity.TPROC_ID,
                ])
                .where("TPROC_VERSION", entity.TTASK_SUBPROCESS_TPROC_VERSION);

            const subArr = [];
            subP.forEach((sub) => {
                subArr[sub.TPROC_ID] = sub.TPROC_NAME;
            });

            event = {
                ttaskEvent: `$SUBP_${entity.TTASK_ID}`,
                ttaskEventReturn: `$SUBR_${entity.TTASK_ID}`,
                ttaskEventDescription: `$SubProcess ${subArr[entity.TPROC_ID]} --> ${subArr[entity.TTASK_SUBPROCESS_TPROC_ID]}`,
                ttaskEventDescReturn: `$SubReturn ${subArr[entity.TTASK_SUBPROCESS_TPROC_ID]} --> ${subArr[entity.TPROC_ID]}`,
            };
        }

        if (entity.TTASK_EVENT && entity.TTASK_EVENT_WAIT !== 1) {
            const eveRepo = globalThis.orm.repo("event", this.connection);
            if (entity.TTASK_SUBPROCESS_TPROC_ID > 0) {
                await eveRepo.createSubprocess(
                    entity.TTASK_EVENT,
                    event.ttaskEventDescription,
                    event.ttaskEventReturn,
                    event.ttaskEventDescReturn,
                    [
                        entity.TTASK_SUBPROCESS_TPROC_ID,
                        entity.TTASK_SUBPROCESS_TPROC_VERSION,
                    ],
                    subMapping.subP,
                    subMapping.subR,
                );
            } else {
                const eveDefRepo = globalThis.orm.repo(
                    "eventDefinition",
                    this.connection,
                );
                await eveDefRepo.createEvent(
                    entity.TTASK_EVENT,
                    event.ttaskEventDescription,
                    true,
                );
            }
        }

        await this.updateEventWait(entity, subMapping);
        return entity;
    }

    async update(entity, subMapping) {
        const eveRepo = globalThis.orm.repo("eventDefinition", this.connection);

        if (entity.TTASK_EVENT !== undefined && entity.TTASK_EVENT !== null) {
            const exists = await eveRepo.exists(
                "EVEDEF_NAME",
                entity.TTASK_EVENT,
            );
            if (!exists) {
                throw new UserException(
                    `Event name does not exist! ${entity.TTASK_EVENT} in ttaskid = ${entity.TTASK_ID}, 'INVALID_EVENT_NAME'`,
                );
            }
        } else {
            await eveRepo.deleteByTTask(entity.TTASK_ID);
        }

        let event = {};
        if (entity.TTASK_SUBPROCESS_TPROC_ID > 0) {
            const subP = await this.connection
                .select("TPROC_ID", "TPROC_NAME")
                .from("TEMPLATE_PROCESSES")
                .whereIn("TPROC_ID", [
                    entity.TTASK_SUBPROCESS_TPROC_ID,
                    entity.TPROC_ID,
                ])
                .where("TPROC_VERSION", 1); // TODO: dynamic version

            const subArr = [];
            subP.forEach((sub) => {
                subArr[sub.TPROC_ID] = sub.TPROC_NAME;
            });

            entity.TTASK_EVENT = `$SUBP_${entity.TTASK_ID}`;
            entity.TTASK_EVENT_WAIT = 2;

            event = {
                ttaskEvent: `$SUBP_${entity.TTASK_ID}`,
                ttaskEventReturn: `$SUBR_${entity.TTASK_ID}`,
                ttaskEventDescription: `$SubProcess ${subArr[entity.TPROC_ID]} --> ${subArr[entity.TTASK_SUBPROCESS_TPROC_ID]}`,
                ttaskEventDescReturn: `$SubReturn ${subArr[entity.TTASK_SUBPROCESS_TPROC_ID]} --> ${subArr[entity.TPROC_ID]}`,
            };
        }

        await super.store(entity);

        const eventRepo = globalThis.orm.repo("event", this.connection);
        await eventRepo.deleteSubprocess(entity.TTASK_ID);

        if (entity.TTASK_SUBPROCESS_TPROC_ID > 0) {
            await eventRepo.createSubprocess(
                entity.TTASK_EVENT,
                event.ttaskEventDescription,
                event.ttaskEventReturn,
                event.ttaskEventDescReturn,
                [
                    entity.TTASK_SUBPROCESS_TPROC_ID,
                    entity.TTASK_SUBPROCESS_TPROC_VERSION,
                ],
                subMapping.subP,
                subMapping.subR,
            );
        }

        await this.updateEventWait(entity, subMapping);

        if (entity.TTASK_EVENT !== undefined && entity.TTASK_EVENT !== null) {
            const eveDefRepo = globalThis.orm.repo(
                "eventDefinition",
                this.connection,
            );
            await eveDefRepo.createEvent(
                entity.TTASK_EVENT,
                event.ttaskEventDescription,
                true,
            );
        }

        return entity;
    }

    async updateEventWait(
        entity: Partial<TemplateTask>,
        subMapping,
    ): Promise<void> {
        const ruleRepo = globalThis.orm.repo("rule", this.connection);

        if (
            entity.TTASK_EVENT_WAIT !== 1 ||
            entity.TTASK_EVENT === undefined ||
            entity.TTASK_EVENT === null
        ) {
            // If TTASK_EVENT_WAIT is not set
            return await ruleRepo.deleteTRules(
                null,
                `$EVEW_${entity.TTASK_ID}`,
            );
        }

        const eveDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );
        const eveDefs = await eveDefRepo.getByName(entity.TTASK_EVENT);

        if (!Array.isArray(eveDefs) || eveDefs.length === 0) {
            throw new UserException("No event found.");
        }

        const eveDefId = eveDefs[0].EVEDEF_ID;
        const ruleDefRepo = globalThis.orm.repo(
            "ruleDefinition",
            this.connection,
        );

        const coll = ruleDefRepo.getTRuleList(eveDefId);
        coll.knex.whereIn("RDEF_STATUS", ["ACTIVE", "DISABLED"]);
        coll.knex.where("RDEF_VALUE", `$EVEW_${entity.TTASK_ID}`);
        coll.knex.where("RDEF_TYPE", "EVENTW");

        let rules = await coll.collectAll();

        let ruleId;

        if (rules.length > 1) {
            await ruleRepo.deleteTRules(eveDefId, `$EVEW_${entity.TTASK_ID}`);
            ruleId = await ruleDefRepo.create(
                eveDefId,
                -1,
                RuleDefinition.consts.TRULE_TYPE_EVENTW,
                `$EVEW_${entity.TTASK_ID}`,
                "ACTIVE",
            );
            rules = [];
        }

        if (rules.length === 0) {
            ruleId = await ruleDefRepo.create(
                eveDefId,
                -1,
                RuleDefinition.consts.TRULE_TYPE_EVENTW,
                `$EVEW_${entity.TTASK_ID}`,
                "ACTIVE",
            );
        } else {
            ruleId = rules[0].RDEF_ID;
        }

        const ruleDefParamRepo = globalThis.orm.repo(
            "ruleDefinitionParam",
            this.connection,
        );
        return await ruleDefParamRepo.updateRuleParams(ruleId, subMapping.subR);
    }

    async deleteTTask(ttaskId) {
        const linkRepo = globalThis.orm.repo(
            "templateTaskLink",
            this.connection,
        );
        const usageRepo = globalThis.orm.repo(
            "templateTaskVarUsage",
            this.connection,
        );
        const calcRepo = globalThis.orm.repo(
            "templateTaskCalculation",
            this.connection,
        );
        const calcJSRepo = globalThis.orm.repo(
            "templateTaskJSCalculation",
            this.connection,
        );
        const ttaskRepo = globalThis.orm.repo("templateTask", this.connection);
        const ruleRepo = globalThis.orm.repo("rule", this.connection);
        const eventRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );

        if (!Array.isArray(ttaskId)) {
            ttaskId = [ttaskId];
        }

        for (const id of ttaskId) {
            await linkRepo.deleteForTTask(id, TPROC_CONSTS.TPROC_ALL_VERSIONS);
            await usageRepo.deleteForTTask(id, TPROC_CONSTS.TPROC_ALL_VERSIONS);
            await this.connection
                .select()
                .from("TEMPLATE_TASK_VAR_PROC_MAP")
                .where("TTASK_ID", id)
                .delete();
            await this.connection
                .select()
                .from("TEMPLATE_TASK_COMPLETION")
                .where("TTASK_ID", id)
                .delete();
            await calcRepo.deleteForTTask(id);
            await calcJSRepo.deleteForTTask(
                id,
                TPROC_CONSTS.TPROC_ALL_VERSIONS,
            );
            const ttask = await ttaskRepo.get(id);
            if (ttask.TTASK_EVENT && ttask.TTASK_EVENT.startsWith("$")) {
                const events = await eventRepo.getByName(ttask.TTASK_EVENT);

                if (Array.isArray(events) && events.length > 0) {
                    const eveId = events[0].EVEDEF_ID;
                    await ruleRepo.deleteTRules(eveId, `$EVEW_${id}`);
                }
            }

            await this.connection
                .select()
                .from("TEMPLATE_TASKS")
                .where("TTASK_ID", id)
                .delete();
        }
    }

    getByName(tprocId, taskName) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_NAME", taskName)
            .where("TPROC_ID", tprocId)
            .then((ttask) => {
                if (!Array.isArray(ttask) || ttask.length === 0) {
                    return null;
                }
                return ttask[0];
            });
    }

    async fillJSCalculations(tasks, tproc) {
        if (!Array.isArray(tasks) && tasks.TTASK_ID) {
            tasks = [tasks];
        }

        const calcRepo = globalThis.orm.repo(
            "templateTaskJSCalculation",
            this.connection,
        );
        const out = [];

        for (const task of tasks) {
            const calcs = await calcRepo
                .getForTTask(task.TTASK_ID, tproc.TPROC_VERSION)
                .fetchAll();
            task.js_calculations = calcs;
            out.push(task);
        }

        return out;
    }

    async fillCompletition(tasks, tproc) {
        if (!Array.isArray(tasks) && tasks.TTASK_ID) {
            tasks = [tasks];
        }

        const calcRepo = globalThis.orm.repo(
            "templateTaskCompletition",
            this.connection,
        );
        const out = [];

        for (const task of tasks) {
            const completition = await calcRepo.getForTTask(
                task.TTASK_ID,
                tproc.TPROC_VERSION,
            );
            task.completition = completition;
            out.push(task);
        }

        return out;
    }

    async fillEmailNotifs(tasks) {
        if (!Array.isArray(tasks) && tasks.TTASK_ID) {
            tasks = [tasks];
        }

        const calcRepo = globalThis.orm.repo(
            "templateTaskEmailNotifs",
            this.connection,
        );
        const out = [];

        for (const task of tasks) {
            const notifs = await calcRepo
                .getForTTask(task.TTASK_ID)
                .collectAll();
            if (task.TTASK_TYPE === "N") {
                task.email_notifs = _.map(notifs, "_raw");
            }
            out.push(task);
        }

        return out;
    }

    async fillInvitation(tasks) {
        if (!Array.isArray(tasks) && tasks.TTASK_ID) {
            tasks = [tasks];
        }

        const invRepo = globalThis.orm.repo(
            "templateTaskInvitation",
            this.connection,
        );
        const out = [];

        for (const task of tasks) {
            const coll = globalThis.orm.collection(
                "templateTaskInvitation",
                invRepo.getForTTask(task.TTASK_ID),
            );
            const inv = await coll.collectAll();
            if (task.TTASK_TYPE === "I") {
                task.invitation = _.map(inv, "_raw");
            }
            out.push(task);
        }

        return out;
    }

    async fillVariableUsages(tasks, tproc) {
        if (!Array.isArray(tasks) && tasks.TTASK_ID) {
            tasks = [tasks];
        }

        const calcRepo = globalThis.orm.repo(
            "templateTaskVarUsage",
            this.connection,
        );
        const out = [];

        for (const task of tasks) {
            const usage = await calcRepo
                .getForTTask(tproc.TPROC_ID, task.TTASK_ID, tproc.TPROC_VERSION)
                .fetchAll();
            task.variable_usages = usage;
            out.push(task);
        }

        return out;
    }

    /**
     * For export purposes.
     * @param tasks
     * @param tproc
     * @return {*}
     */
    async fillMassUsages(tasks, tproc) {
        if (!Array.isArray(tasks) && tasks.TTASK_ID) {
            tasks = [tasks];
        }

        const massRepo = globalThis.orm.repo(
            "templateTaskMassUsage",
            this.connection,
        );
        const out = [];

        for (const task of tasks) {
            const usage = await massRepo
                .getForTTask(tproc.TPROC_ID, task.TTASK_ID, tproc.TPROC_VERSION)
                .fetchAll();
            task.mass_usages = usage;
            out.push(task);
        }

        return out;
    }

    getForTProcess(tprocId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("TPROC_ID", tprocId);
    }

    getByEvent(events) {
        const processEntity = globalThis.orm
            .repo("templateProcess", this.connection)
            .getEntity();

        const columns = Object.keys(processEntity.attributes())
            .filter((attr) => !["ORG_ID", "TPROC_ID"].includes(attr))
            .map((a) => `TP.${a}`);
        columns.push("TT.*");

        return this.connection
            .select(columns)
            .from(`${this.tableName} as TT`)
            .leftJoin("TEMPLATE_PROCESSES as TP", "TT.TPROC_ID", "TP.TPROC_ID")
            .whereIn("TTASK_EVENT", Array.isArray(events) ? events : [events]);
    }
}
