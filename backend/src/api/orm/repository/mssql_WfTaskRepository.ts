// @ts-nocheck
// @ts-nocheck
import * as TAS<PERSON> from "../entity/const/taskConst";

import { WfTaskRepository } from "./WfTaskRepository";

export class mssql_WfTaskRepository extends WfTaskRepository {
    /**
     * Returns last or actual (if missing) solver of specific task
     *
     * @param process
     * @param ttaskId
     * @return {Promise<null>}
     */
    async getLastSolver(process, ttaskId) {
        const rows = await globalThis.container.client.database.callKnexRaw(
            `SELECT ITASK_USER_ID, ITASK_ACTUAL_DATE_START, 'A' AS A FROM INSTANCE_TASKS
                    WHERE IPROC_ID = :IPROC_ID AND TTASK_ID = :TTASK_ID AND ITASK_STATUS in ('A', 'L')
                        UNION    ALL
                    SELECT ITASKH_USER_ID, ITASKH_ACTUAL_DATE_FINISH AS ITASK_ACTUAL_DATE_START, 'B' AS A FROM
                            (SELECT ITH.ITASKH_USER_ID, ITH.ITASKH_ACTUAL_DATE_FINISH FROM INSTANCE_TASK_HISTORY ITH
                                JOIN INSTANCE_TASKS IT ON ITH.ITASK_ID = IT.ITASK_ID
                                WHERE ITH.IPROC_ID = :IPROC_ID AND IT.TTASK_ID = :TTASK_ID
                                    AND ITH.ITASKH_USER_ID IS NOT NULL
                                    AND ITH.ITASKH_NOTE IN (:NOTES)
                                ORDER BY ITH.ITASKH_ACTUAL_DATE_FINISH DESC
                                OFFSET 0 ROWS)
                            as ignored_alias
                    ORDER BY 3 DESC, 2 DESC`,
            {
                IPROC_ID: process.IPROC_ID,
                TTASK_ID: ttaskId,
                NOTES: TASK.HISTORY_NOTE_LAST_SOLVER,
            },
            this.connection,
        );

        if (!Array.isArray(rows) || rows.length === 0) {
            return null;
        }
        return rows[0].ITASK_USER_ID;
    }
}
