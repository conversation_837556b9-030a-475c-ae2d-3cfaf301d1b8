// @ts-nocheck
// @ts-nocheck
import moment from "moment";
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import * as NOTIFICATION from "../entity/const/notificationConsts";
import * as USER_PARAMETERS from "../entity/const/userParameterConsts";
import { InstanceTaskEmailNotifs } from "../entity/InstanceTaskEmailNotifs";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class InstanceTaskEmailNotifsRepository extends BaseRepository<InstanceTaskEmailNotifs> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_EMAIL_NOTIFS",
            entityName: "InstanceTaskEmailNotifs",
            entity: () => new InstanceTaskEmailNotifs(),
            archived: true,
            archParams: {
                subQueryTable: "INSTANCE_TASKS",
                subQueryColumn: "ITASK_ID",
            },
        };
    }

    /**
     * Sorts targets by user language for later use
     * - also copy 'blind' into 'target', if target is empty and blind isn't
     * - also remove 'blind' addresses that are also in 'targets'
     *
     * @param targets
     * @param usersByLanguage
     * @returns { lang: { type: [<USER>] } }
     */
    sortTargetsByLanguage(targets, usersByLanguage) {
        const result = {};

        // cs, en, ...
        Object.keys(usersByLanguage).forEach((language) => {
            result[language] = {};

            // target, reply, blind
            Object.keys(targets).forEach((type) => {
                result[language][type] = [];

                // cs: [1, 5], en: [967, 968], ...
                usersByLanguage[language].forEach((userId) => {
                    // There could be multiple 'users' with id 'null' (external emails)
                    const users = _.filter(targets[type], { USER_ID: userId });
                    users.forEach((user) => {
                        result[language][type].push(user);
                    });
                });
            });

            // There could be 'plain' or 'blind' copies without 'target' address due to a different language
            const targetLanguage = result[language];
            if (
                !targetLanguage.target.length &&
                (targetLanguage.blind.length || targetLanguage.copy.length)
            ) {
                // No need to delete the 'duplicate' email addresses
                // If the same address is 'to' and 'cc' or 'bcc', multiple emails will not be sent
                const addresses = _.uniqBy(
                    [].concat(targetLanguage.blind, targetLanguage.copy),
                    "USER_EMAIL",
                );
                targetLanguage.target =
                    addresses.length === 1
                        ? addresses
                        : [
                              {
                                  USER_EMAIL:
                                      globalThis.dynamicConfig.mail.noReplyMail,
                              },
                          ];
            }
        });

        return result;
    }

    /**
     * Returns userId(s) sorted under language as key
     *
     * @param userIds
     * @param defaultLanguage
     * @returns {Promise<{ lang: [<Number>] }>}
     */
    async getUserLanguage(
        userIds,
        defaultLanguage = globalThis.dynamicConfig.defaultLang,
    ) {
        const userLanguages = {};
        const userParamRepo = globalThis.orm.repo(
            "UserParameter",
            this.connection,
        );

        // Get existing custom user configurations
        const customUserLanguages = await userParamRepo.getUserParameter(
            userIds,
            USER_PARAMETERS.LANGUAGE_CLIENT,
        );

        // Apply default values to user without custom language
        const defaultUsers = _.difference(
            userIds,
            customUserLanguages.map((item) => item.USER_ID),
        );
        const defaultUserLanguages = defaultUsers.map((userId) => ({
            USER_ID: userId,
            USRPAR_NAME: USER_PARAMETERS.LANGUAGE_CLIENT,
            USRPAR_VALUE: defaultLanguage,
        }));

        // Merge users
        const allUserLanguages = [].concat(
            defaultUserLanguages,
            customUserLanguages,
        );

        // Sort users by different languages
        const langs = globalThis.dynamicConfig.langs.length
            ? globalThis.dynamicConfig.langs
            : [defaultLanguage];

        // There may be Users with a language that has not been implemented by migration (yet), fallback to default
        const allUserLanguagesFallback = allUserLanguages.map((user) =>
            langs.includes(user.USRPAR_VALUE)
                ? user
                : {
                      ...user,
                      USRPAR_VALUE: defaultLanguage,
                  },
        );

        langs.forEach((language) => {
            userLanguages[language] = allUserLanguagesFallback
                .filter((item) => item.USRPAR_VALUE === language)
                .map((item) => item.USER_ID);
        });

        return userLanguages;
    }

    getSystemVariables(
        task,
        process,
        user,
        lastUser,
        solverUser,
        userLang: string,
        dateFormat: string = "L",
    ) {
        moment.locale(userLang);

        const sysVars = {
            CaseName: process.IPROC_NAME,
            CaseStatus: process.IPROC_CASE_STATUS,
            "-1": process.IPROC_NAME,
            "-9": process.IPROC_CASE_STATUS,
            CaseStart: process.IPROC_ACTUAL_START_DATE
                ? `${moment(process.IPROC_ACTUAL_START_DATE).format(dateFormat)} ${moment(process.IPROC_ACTUAL_START_DATE).format("LTS")}`
                : "",
            CaseDeadline: process.IPROC_DUE_DATE_FINISH
                ? `${moment(process.IPROC_DUE_DATE_FINISH).format(dateFormat)} ${moment(process.IPROC_DUE_DATE_FINISH).format("LTS")}` ||
                  ""
                : "",
            CasePriority: process.IPROC_PRIORITY,
            CasePriorityText: globalThis.__({
                phrase: `priority${process.IPROC_PRIORITY}`,
                locale: userLang,
            }),
            CaseDescription: process.IPROC_DESCRIPTION || "",
            CaseNotes: null, // You need to take care of this yourself => sysVariables.CaseNotes = await print.printProcessLog(process.IPROC_ID, null, globalThis.dynamicConfig.mail.mailLanguage);
            CaseOwner: user.USER_NAME,
            CaseOwnerName: user.USER_DISPLAY_NAME,
            CaseLink: `${globalThis.dynamicConfig.frontendUrl}/cases/case/${process.IPROC_ID}`,
            FrontendLink: `${globalThis.dynamicConfig.frontendUrl}`,
            BackendLink: `${globalThis.dynamicConfig.hostname}`,
            CaseVisRoleId: process.IPROC_VIS_ROLE_ID || "",
            "-2": process.IPROC_VIS_ROLE_ID || "",
            "-8": task.ITASK_USERJS || "",
            "-6": task.ITASK_USERJS || "",
            "-5": task.ITASK_DESCRIPTION || "",
            "-4": task.ITASK_COMMENT || "",
            "-3": task.ITASK_NAME || "",
            TaskUserJS: task.ITASK_USERJS || "",
            TaskDescription: task.ITASK_DESCRIPTION || "",
            TaskComment: task.ITASK_COMMENT || "",
            TaskName: task.ITASK_NAME || "",
            TaskStartAt: task.ITASK_DUE_DATE_START
                ? `${moment(task.ITASK_DUE_DATE_START).format(dateFormat)} ${moment(task.ITASK_DUE_DATE_START).format("LTS")}`
                : "",
            TaskDeadline: task.ITASK_DUE_DATE_FINISH
                ? `${moment(task.ITASK_DUE_DATE_FINISH).format(dateFormat)} ${moment(task.ITASK_DUE_DATE_FINISH).format("LTS")}`
                : "",
            TaskFinish: task.ITASK_ACTUAL_DATE_FINISH
                ? `${moment(task.ITASK_ACTUAL_DATE_FINISH).format(dateFormat)} ${moment(task.ITASK_ACTUAL_DATE_FINISH).format("LTS")}`
                : "",
            TaskStart: task.ITASK_ACTUAL_DATE_START
                ? `${moment(task.ITASK_ACTUAL_DATE_START).format(dateFormat)} ${moment(task.ITASK_ACTUAL_DATE_START).format("LTS")}`
                : "",
            TaskOwner: task.ITASK_USER_ID || "",
            TaskOwnerName: task.ITASK_USER_ID ? lastUser.USER_DISPLAY_NAME : "",
            TaskSolvedBy: task.ITASK_FINISHED_BY_USER_ID || "",
            TaskSolvedByName: task.ITASK_FINISHED_BY_USER_ID
                ? solverUser.USER_DISPLAY_NAME
                : "",
            TaskInstruction: task.ITASK_INSTRUCTION || "",
            TaskLink: `${globalThis.dynamicConfig.frontendUrl}/tasks/task/${task.ITASK_ID}`,
        };

        // Add ITASK_COMMENT mutations
        globalThis.dynamicConfig.langs.forEach((lang) => {
            const attrName = `ITASK_COMMENT_${lang.toUpperCase()}`;
            sysVars[attrName] = task[attrName] || "";
        });

        return sysVars;
    }

    /**
     * Returns email addresses and addressee ids based on ENOT task parameters
     *
     * @param task
     * @param process
     * @param emailTask
     * @param dlu
     * @param variables
     * @returns {Promise<{target: {emails: Array, ids: Array}, reply: {emails: Array, ids: Array}, blind: {emails: Array, ids: Array}}>}
     */
    async getNotificationTargets(task, process, emailTask, dlu, variables) {
        const aliases = {
            ITASK_ENOT_TGT: "target",
            ITASK_ENOT_REPLY: "reply",
            ITASK_ENOT_BLIND: "blind",
            ITASK_ENOT_COPY: "copy",
        };
        const targets = {
            target: [],
            reply: [],
            blind: [],
            copy: [],
        };
        const userRepo = globalThis.orm.repo("user", this.connection);
        const types = ["TGT", "REPLY", "BLIND", "COPY"].map(
            (item) => `ITASK_ENOT_${item}`,
        );

        // Iterate Target, Reply and Blind. Extract ids and emails
        for (const type of types) {
            let target;
            const alias = aliases[type];
            const typeAttr = `${type}_TYPE`;

            switch (emailTask[typeAttr]) {
                case NOTIFICATION.TYPE_PROCESS_OWNER:
                    target = await userRepo
                        .getById(process.IPROC_INST_OWNER_USER_ID)
                        .collectOne();
                    if (target && target.USER_EMAIL) {
                        targets[alias].push({
                            USER_ID: target.USER_ID,
                            USER_EMAIL: target.USER_EMAIL,
                        });
                    }
                    break;
                case NOTIFICATION.TYPE_PROCESS_GUARANTOR:
                    target = await this.wfSolver.getGuarantor(task, process);
                    if (target && target.USER_EMAIL) {
                        targets[alias].push({
                            USER_ID: target.USER_ID,
                            USER_EMAIL: target.USER_EMAIL,
                        });
                    }
                    break;
                case NOTIFICATION.TYPE_LAST_TASK_SOLVER:
                    target = await this.wfSolver.getLastSolver(process, {
                        TTASK_ID: emailTask[`${type}_TTASK_ID`],
                    });
                    target = await this.orm
                        .repo("user")
                        .get(target, ["USER_EMAIL", "USER_ID"], false);
                    if (target && target.USER_EMAIL) {
                        targets[alias].push({
                            USER_ID: target.USER_ID,
                            USER_EMAIL: target.USER_EMAIL,
                        });
                    }
                    break;
                case NOTIFICATION.TYPE_PLAIN_ADDRESS:
                    // User email is not unique, there might be more users. Also, there may be more mails split by ';'
                    target =
                        type.indexOf("TGT") !== -1
                            ? emailTask[type]
                            : emailTask[`${type}_TARGET`];
                    target = target.replace(/,/g, ";");
                    target = target.split(";").map((mail) => mail.trim());

                    // target can be {VARIABLE}, need to replace
                    target = target.map((item) =>
                        globalThis.lang.replaceTemplates(item, variables),
                    );

                    for (const mail of target) {
                        const userIds = await userRepo.getUserIdByEmail(mail);
                        // Is TAS user?
                        // t3b-1489 Chyba při odesílání e-mailu
                        if (userIds.length === 1) {
                            userIds.forEach((userId) => {
                                targets[alias].push({
                                    USER_ID: userId,
                                    USER_EMAIL: mail,
                                });
                            });
                        } else {
                            // Is external address or multiple Users with the same email address have been found
                            targets[alias].push({
                                USER_ID: null,
                                USER_EMAIL: mail,
                            });
                        }
                    }
                    break;
                case NOTIFICATION.TYPE_USER_NAME:
                    // ITASK_ENOT_TGT, ITASK_ENOT_REPLY_TARGET, ITASK_ENOT_BLIND_TARGET
                    target =
                        type.indexOf("TGT") !== -1
                            ? emailTask[type]
                            : emailTask[`${type}_TARGET`];

                    if (dlu[target]) {
                        const userIds = dlu[target];
                        const users = await userRepo
                            .getById(userIds)
                            .collectAll();
                        users.forEach((user) => {
                            if (user.USER_EMAIL) {
                                targets[alias].push({
                                    USER_ID: user.USER_ID,
                                    USER_EMAIL: user.USER_EMAIL,
                                });
                            }
                        });
                    } else {
                        this.log.error(
                            `Did not find any users by variable '${target}'`,
                        );
                    }
                    break;
                case NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE:
                    target = await userRepo.getUserListByOrgStructure({
                        ORGSTR_ID: emailTask[`${type}_ORGSTR_ID`],
                    });
                    target.forEach((user) => {
                        if (user.USER_EMAIL) {
                            targets[alias].push({
                                USER_ID: user.USER_ID,
                                USER_EMAIL: user.USER_EMAIL,
                            });
                        }
                    });
                    break;
                case NOTIFICATION.TYPE_ROLE:
                    target = await userRepo.getUserListByRole({
                        ROLE_ID: emailTask[`${type}_ROLE_ID`],
                    });
                    target.forEach((user) => {
                        if (user.USER_EMAIL) {
                            targets[alias].push({
                                USER_ID: user.USER_ID,
                                USER_EMAIL: user.USER_EMAIL,
                            });
                        }
                    });
                    break;
                case null:
                    break;
                default:
                    throw new InternalException(
                        `Unknown type '${emailTask[type]}'`,
                        "UNSUPPORTED_TYPE",
                    );
            }
        }

        return targets;
    }

    async copyForTemplateTasks(ttaskMap): Promise<void> {
        const ttaskIds = Object.keys(ttaskMap);

        if (ttaskIds.length === 0) {
            return;
        }

        for (const ttaskId of ttaskIds) {
            if (ttaskMap[ttaskId].ITASK_TYPE !== "N") {
                continue;
            }
            // TODO - ITASK_ENOT_ATTACHMENTS ?
            await this.connection.raw(
                `
                insert into 
                    "INSTANCE_TASK_EMAIL_NOTIFS" 
                    (
                    "ITASK_ID", 
                    "ITASK_ENOT_TGT_TYPE", 
                    "ITASK_ENOT_TGT_TTASK_ID", 
                    "ITASK_ENOT_REPLY_TTASK_ID", 
                    "ITASK_ENOT_BLIND_TTASK_ID",
                    "ITASK_ENOT_TGT", 
                    "ITASK_ENOT_TGT_ORGSTR_ID", 
                    "ITASK_ENOT_REPLY_ORGSTR_ID", 
                    "ITASK_ENOT_BLIND_ORGSTR_ID",
                    "ITASK_ENOT_TGT_ROLE_ID", 
                    "ITASK_ENOT_REPLY_ROLE_ID", 
                    "ITASK_ENOT_BLIND_ROLE_ID",
                    "ITASK_ENOT_SUBJECT", 
                    "ITASK_ENOT_REPLY_TYPE",
                    "ITASK_ENOT_BLIND_TYPE",
                    "ITASK_ENOT_REPLY_TARGET",
                    "ITASK_ENOT_BLIND_TARGET",
                    "ITASK_ENOT_COPY_TYPE",
                    "ITASK_ENOT_COPY_TTASK_ID",
                    "ITASK_ENOT_COPY_TARGET",
                    "ITASK_ENOT_COPY_ORGSTR_ID",
                    "ITASK_ENOT_COPY_ROLE_ID"
                    ) 
                select 
                    ?, 
                    "TTASK_ENOT_TGT_TYPE", 
                    "TTASK_ENOT_TGT_TTASK_ID", 
                    "TTASK_ENOT_REPLY_TTASK_ID", 
                    "TTASK_ENOT_BLIND_TTASK_ID",
                    "TTASK_ENOT_TGT", 
                    "TTASK_ENOT_TGT_ORGSTR_ID", 
                    "TTASK_ENOT_REPLY_ORGSTR_ID", 
                    "TTASK_ENOT_BLIND_ORGSTR_ID",
                    "TTASK_ENOT_TGT_ROLE_ID", 
                    "TTASK_ENOT_REPLY_ROLE_ID", 
                    "TTASK_ENOT_BLIND_ROLE_ID",
                    "TTASK_ENOT_SUBJECT", 
                    "TTASK_ENOT_REPLY_TYPE",
                    "TTASK_ENOT_BLIND_TYPE",
                    "TTASK_ENOT_REPLY_TARGET",
                    "TTASK_ENOT_BLIND_TARGET",
                    "TTASK_ENOT_COPY_TYPE",
                    "TTASK_ENOT_COPY_TTASK_ID",
                    "TTASK_ENOT_COPY_TARGET",
                    "TTASK_ENOT_COPY_ORGSTR_ID",
                    "TTASK_ENOT_COPY_ROLE_ID"
                from   
                    "TEMPLATE_TASK_EMAIL_NOTIFS" 
                where  
                    "TTASK_ID" = ?         
            `,
                [ttaskMap[ttaskId].ITASK_ID, ttaskId],
            );
        }
    }

    getByTask(taskId) {
        const coll = this.getCollection(null, "ITEN");
        coll.knex.where("ITEN.ITASK_ID", taskId);
        coll.knex.innerJoin(
            "INSTANCE_TASKS as IT",
            "IT.ITASK_ID",
            "ITEN.ITASK_ID",
        );
        coll.knex.innerJoin(
            "TEMPLATE_TASK_EMAIL_NOTIFS as TTEN",
            "TTEN.TTASK_ID",
            "IT.TTASK_ID",
        );

        // Add TEMPLATE language mutations
        const extraColumns = [
            "TTEN.TTASK_ENOT_BODY2",
            "TTEN.TTASK_ENOT_SUBJECT",
            "TTEN.TTASK_ENOT_EXTERNAL_LANGUAGE",
        ];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            extraColumns.push(`TTEN.TTASK_ENOT_BODY2_${lang.toUpperCase()}`);
            extraColumns.push(`TTEN.TTASK_ENOT_SUBJECT_${lang.toUpperCase()}`);
        });
        coll.knex.columns(extraColumns);

        return coll;
    }

    store(entity, force) {
        // 'TO' mail check
        switch (entity.ITASK_ENOT_TGT_TYPE) {
            case NOTIFICATION.TYPE_LAST_TASK_SOLVER:
                if (!entity.ITASK_ENOT_TGT_TTASK_ID) {
                    throw new UserException(
                        "Task has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_PLAIN_ADDRESS:
                if (!entity.ITASK_ENOT_TGT || entity.ITASK_ENOT_TGT === "") {
                    throw new UserException(
                        "Target email has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_USER_NAME:
                if (!entity.ITASK_ENOT_TGT) {
                    throw new UserException(
                        "Target username has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE:
                if (!entity.ITASK_ENOT_TGT_ORGSTR_ID) {
                    throw new UserException(
                        "Organization structure has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_ROLE:
                if (!entity.ITASK_ENOT_TGT_ROLE_ID) {
                    throw new UserException(
                        "Role has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_PROCESS_OWNER:
            case NOTIFICATION.TYPE_PROCESS_GUARANTOR:
            case null:
                break;
            default:
                throw new UserException(
                    `Unsupported TGT_TYPE '${entity.ITASK_ENOT_TGT_TYPE}'`,
                    "UNSUPPORTED_TYPE",
                );
        }

        // 'REPLY' mail check
        switch (entity.ITASK_ENOT_REPLY_TYPE) {
            case NOTIFICATION.TYPE_LAST_TASK_SOLVER:
                if (!entity.ITASK_ENOT_TGT_TTASK_ID) {
                    throw new UserException(
                        "Task has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_PLAIN_ADDRESS:
                if (
                    !entity.ITASK_ENOT_REPLY_TARGET ||
                    entity.TTASK_ENOT_REPLY_TARGET === ""
                ) {
                    throw new UserException(
                        "Target reply email has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_USER_NAME:
                if (!entity.ITASK_ENOT_REPLY_TARGET) {
                    throw new UserException(
                        "Target reply username has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE:
                if (!entity.ITASK_ENOT_REPLY_ORGSTR_ID) {
                    throw new UserException(
                        "Organization structure has to be set for this email notification",
                    );
                }
                break;
            case NOTIFICATION.TYPE_ROLE:
                if (!entity.ITASK_ENOT_REPLY_ROLE_ID) {
                    throw new UserException(
                        "Role has to be set for this email notification",
                    );
                }
                break;
            case null:
                break;
            default:
                throw new UserException(
                    `Unsupported REPLY_TYPE '${entity.ITASK_ENOT_REPLY_TYPE}'`,
                    "UNSUPPORTED_TYPE",
                );
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where("ITASK_ID", entity.ITASK_ID)
            .delete()
            .then(() => super.store(entity, force));
    }
}
