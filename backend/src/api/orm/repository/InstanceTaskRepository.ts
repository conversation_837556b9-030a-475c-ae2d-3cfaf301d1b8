// @ts-nocheck
// @ts-nocheck
import { TimeMeasure } from "../../utils/TimeMeasure";
import _ from "lodash";
import moment from "moment";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import * as TASK from "../entity/const/taskConst";
import * as PROCESS from "../entity/const/processConst";
import * as USER_PARAMETERS from "../entity/const/userParameterConsts";
import * as ROLE from "../entity/const/roleConst";
import { Task } from "../entity/Task";
import { MailOptionsParams } from "../../../client/mail/BaseMailClient";
import { LogCategory } from "../../../utils/logger/logConsts";
import { STATUS_ACTIVE as USER_STATUS_ACTIVE } from "../entity/const/userConst";
import { BaseCollection } from "../BaseCollection";

export class InstanceTaskRepository extends BaseRepository<Task> {
    meta() {
        return {
            tableName: "INSTANCE_TASKS",
            entityName: "Task",
            entity: () => new Task(),
            archived: true,
        };
    }

    /**
     * Return prepared connection to select all task in collection with managed users.
     *
     * @param userId
     * @param extraColumns
     * @return {BaseCollection}
     */
    async all(
        userId,
        extraColumns?,
        checkRoles = false,
        skipComputedValues = false,
    ) {
        const orgStrRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );

        // Add translations.
        if (!Array.isArray(extraColumns)) {
            extraColumns = [];
        }
        // iProc translations
        const iProcEntity = globalThis.orm.repo("process").getEntity();
        let attrs = iProcEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
            }
        });

        // Tproc translations
        const tprocEntity = globalThis.orm.repo("templateProcess").getEntity();
        attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        // Itask translations are in columns below

        // TTask translations
        const ttaskEntity = globalThis.orm.repo("templateTask").getEntity();
        attrs = ttaskEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TT.${attrName}`);
            }
        });

        // Header translations
        const headerEntity = globalThis.orm.repo("header").getEntity();
        attrs = headerEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TH.${attrName}`);
            }
        });

        let columns = this.getEntity().getColumnNames(null, "IT");
        columns = columns.concat([
            "TH.HEADER_NAME",
            "TH.HEADER_CODE",
            "TH.HEADER_ENABLED",
            "TH.TPROC_ID",
            "TH.HEADER_ID",
            "IP.IPROC_STATUS",
            "IT_FINISHED.USER_DISPLAY_NAME AS ITASK_FINISHED_BY_USER_DNAME",
            globalThis.database.raw(
                `"IT_SOLVER"."USER_DISPLAY_NAME" AS "ITASK_USER_NAME"`,
            ),
            "IT_SOLVER.USER_DISPLAY_NAME AS ITASK_USER_DISPLAY_NAME",
            globalThis.database.raw(
                `"IP_OWNER"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "IP_OWNER"."USER_FIRST_NAME" AS "IPROC_OWNER_FULL_NAME"`,
            ),
            "IP_OWNER.USER_DISPLAY_NAME AS IPROC_OWNER_DISPLAY_NAME",
            globalThis.database.raw(
                `"IT_ASS"."USER_DISPLAY_NAME" AS "ITASK_ASSESMENT_USER"`,
            ),
            "IT_ASS.USER_DISPLAY_NAME AS ITASK_ASSESMENT_DISPLAY_NAME",
            "IT_SOLVER.USER_DISPLAY_NAME AS SOLVER_USER_FULL_NAME",
            "IT_SOLVER.USER_DISPLAY_NAME AS SOLVER_USER_DISPLAY_NAME",
            globalThis.database.raw(
                `(select MAX("JS_VERSION") FROM "JS_SCRIPTS" WHERE "JS_TYPE" = 'F') as "JS_VERSION"`,
            ),
            "IP.IPROC_NAME",
            "IP.IPROC_PRIORITY",
            "IP.IPROC_MAIN_IPROC_ID",
            "TT.TTASK_NAME",
            "TT.TTASK_DESCRIPTION",
            "TT.TTASK_INSTRUCTION",
            "TT.TTASK_IS_REJECTABLE",
            "TT.TTASK_IS_DELEGATABLE",
            "TT.TTASK_JS",
            "TT.TTASK_CAN_BE_BUTTON",
            "TP.TPROC_NAME",
            "TP.TPROC_VERSION",
            // when active tasks are in done processes
            globalThis.database.raw(`
                CASE WHEN "IP"."IPROC_STATUS" = 'A'
                THEN "IT"."ITASK_STATUS"
                ELSE 'D' END "REAL_ITASK_STATUS"`),
            globalThis.database.raw(
                `(select COUNT("TTJSCALC_EXEC_RECALC") FROM "TEMPLATE_TASK_JS_CALCULATIONS" WHERE "TTASK_ID" = "TT"."TTASK_ID" and "TTJSCALC_EXEC_RECALC" LIKE 'Y') as "EXEC_RECALC"`,
            ),
        ]);

        // CAN I SOLVE ?
        columns = columns.concat([
            globalThis.database.raw(
                `
                CASE WHEN "IT"."ITASK_USER_ID" = ?
                AND "IP"."IPROC_STATUS" = 'A'
                AND "IT"."ITASK_TYPE" not in ('P','W')
                AND "IT"."ITASK_STATUS" <> 'L'
                THEN 1
                ELSE 0 END "C_SOLVE" `,
                userId,
            ),
        ]);

        if (!skipComputedValues) {
            columns = columns.concat(
                await this.getComputedColumns(userId, checkRoles, false),
            );
        }

        if (Array.isArray(extraColumns) && extraColumns.length > 0) {
            columns = columns.concat(extraColumns);
        }

        const conn = globalThis.orm
            .repo("task")
            .connection.withRecursive(
                "CHILDREN_UNITS",
                this.connection.raw(orgStrRepo.withChildrenUnitsForManager(), {
                    MANAGER_USER_ID: userId,
                }),
            )
            .with(
                "MANAGED_USERS",
                this.connection.raw(orgStrRepo.withManagedUsers(), {
                    USER_ID: userId,
                    USER_STATUS_ACTIVE,
                }),
            )
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(columns)
            .from("INSTANCE_TASKS AS IT")
            .leftJoin("INSTANCE_PROCESSES AS IP", "IT.IPROC_ID", "IP.IPROC_ID")
            .leftJoin("TEMPLATE_TASKS AS TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "IP.TPROC_ID")
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .leftJoin(
                "USERS AS IP_OWNER",
                "IP.IPROC_INST_OWNER_USER_ID",
                "IP_OWNER.USER_ID",
            )
            .leftJoin(
                "USERS AS IT_SOLVER",
                "IT.ITASK_USER_ID",
                "IT_SOLVER.USER_ID",
            )
            .leftJoin(
                "USERS AS IT_FINISHED",
                "IT.ITASK_FINISHED_BY_USER_ID",
                "IT_FINISHED.USER_ID",
            )
            .leftJoin(
                "USERS AS IT_ASS",
                "IT.ITASK_ASSESMENT_USER_ID",
                "IT_ASS.USER_ID",
            );

        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("header")
                .applyExternalLoginRights(conn, userId);
        }

        return this.createCollection(conn);
    }

    // Does not check any system Roles
    async hasHrRights(user, iProcId) {
        // Evaluate only if the User has Process visibility rights
        const hasProcessRights = await globalThis.orm
            .repo("externalRight", this.connection)
            .hasProcessRights(user.USER_ID, iProcId);
        if (!hasProcessRights) {
            return false;
        }

        const constraints = await this.connection
            .select([
                "TP.TPROC_HR_ROLE_ID",
                "IP.IPROC_HR_ROLE_ID",
                "H.HEADER_HR_ROLE_ID",
            ])
            .from("INSTANCE_PROCESSES as IP")
            .innerJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "IP.TPROC_ID")
            .innerJoin("HEADERS as H", "H.HEADER_ID", "IP.HEADER_ID")
            .where("IP.IPROC_ID", iProcId)
            .first();

        return Object.values(constraints).some(
            (roleId) => roleId && user.hasRole(roleId),
        );
    }

    async hasTaskRights(
        userId,
        taskId,
        op,
        throwError: false = false,
    ): Promise<Task | undefined>;
    async hasTaskRights(
        userId,
        taskId,
        op,
        throwError: true = true,
    ): Promise<Task>;
    async hasTaskRights(
        userId,
        taskId,
        op,
        throwError = false,
    ): Promise<Task | undefined> {
        const task = await this.all(userId).then((coll) => {
            coll.knex.where("IT.ITASK_ID", taskId);
            return coll.collectOne();
        });

        if (!task) {
            if (throwError) {
                throw new UserException("Task not found", "NOT_FOUND");
            }
            return;
        }

        /* const c_solve =  task.raw.C_SOLVE > 0;
        const c_handover = task.raw.C_HANDOVER > 0;
        const c_add = task.raw.C_ADD > 0;
        const c_take = task.raw.C_TAKE > 0;
        const isProcessOwner = task.IPROC_INST_OWNER_USER_ID === userId;
        const isGuarantor = task.raw.GUARANTOR_USER_ID === userId;
        const isSolver = task.ITASK_USER_ID === userId;
        const hasTemplate = task.TPROC_ID !== null;
        const isSolverManager = task.raw.IS_SOLVER_MANAGER > 0;
        const isGuarantorManager = task.raw.IS_GUARANTOR_MANAGER > 0;
        const isOwnerManager = task.raw.IS_OWNER_MANAGER > 0;
        const emptyGuarantor = task.ITASK_ASSESMENT_USER_ID === null;
        const suspended = task.IPROC_STATUS === 'S';
        const c_pull = task.raw.C_PULL > 0;
        const visRole = task.raw.VIS_ROLE === 1;
        const visOS = task.raw.VIS_ORGSTR === 1; */

        switch (op) {
            case "S":
                if (!task.raw.C_SOLVE && throwError) {
                    throw new UserException(
                        "User cannot solve task.",
                        "PERMISSION_DENIED",
                    );
                }
                return task.raw.C_SOLVE ? task : undefined;
            case "T": // takeover = task to pull
                if (!task.raw.C_TAKE && throwError) {
                    throw new UserException(
                        "User cannot take task.",
                        "PERMISSION_DENIED",
                    );
                }
                return task.raw.C_TAKE ? task : undefined;
            case "A": // assign to ... or assign to me
                if (!task.raw.C_HANDOVER && throwError) {
                    throw new UserException(
                        "User cannot assign task.",
                        "PERMISSION_DENIED",
                    );
                }
                return task.raw.C_HANDOVER ? task : undefined;
            case "N": // add new task
                if (!task.raw.C_ADD && throwError) {
                    throw new UserException(
                        "User cannot add task.",
                        "PERMISSION_DENIED",
                    );
                }
                return task.raw.C_ADD ? task : undefined;
            case "D": // set due date
                if (!task.raw.C_DUE && throwError) {
                    throw new UserException(
                        "User cannot set due date of task.",
                        "PERMISSION_DENIED",
                    );
                }
                return task.raw.C_DUE ? task : undefined;
            default:
                if (throwError) {
                    throw new UserException(
                        "Undefined permission.",
                        "PERMISSION_DENIED",
                    );
                }
                return undefined;
        }
    }

    /**
     *
     * @param userId
     * @return {Promise<BaseCollection>}
     */
    async toPull(userId) {
        return await this.all(userId).then(async (coll) => {
            const conn = coll.knex;

            const user =
                await globalThis.container.service.temporary.cacheModule.getCachedUser(
                    userId,
                );
            if (user.isGlobalSupervisor()) {
                conn.where("IP.IPROC_STATUS", "A").where(
                    "IT.ITASK_STATUS",
                    "T",
                );
                return coll;
            }

            conn.leftJoin(
                {
                    UR: globalThis
                        .database("USER_ROLES AS UR")
                        .select("UR.ROLE_ID", "UR.USER_ID")
                        .distinct(),
                },
                function () {
                    this.on(
                        "UR.ROLE_ID",
                        "=",
                        "IT.ITASK_ASSESMENT_ROLE_ID",
                    ).andOn(
                        "UR.USER_ID",
                        "=",
                        globalThis.database.raw("?", [userId]),
                    );
                },
            );

            conn.whereRaw(
                `(("IT"."ITASK_ASSESMENT_ROLE_ID" IS NOT NULL and "UR"."USER_ID" IS NOT NULL) or "IT"."ITASK_ASSESMENT_ROLE_ID" IS NULL)`,
            )
                .whereRaw(
                    `
                EXISTS (SELECT 1 FROM "USER_ORGANIZATION_STRUCTURE" "UOS"
              JOIN "ORGANIZATION_STRUCTURE" "OS" ON "OS"."ORGSTR_ID" = "UOS"."ORGSTR_ID"
              WHERE ("UOS"."USER_ID" = ? OR "OS"."MANAGER_USER_ID" = ?)
                AND ("UOS"."ORGSTR_ID" = "IT"."ITASK_ASSESMENT_ORGSTR_CNST" OR "IT"."ITASK_ASSESMENT_ORGSTR_CNST" IS NULL))
            `,
                    [userId, userId],
                )
                .where("IP.IPROC_STATUS", "A")
                .where("IT.ITASK_STATUS", "T");

            return coll;
        });
    }

    async mineAndToPull(userId) {
        const coll = await this.all(userId);
        const conn = coll.knex;

        conn.leftJoin(
            {
                UR: globalThis
                    .database("USER_ROLES AS UR")
                    .select("UR.ROLE_ID", "UR.USER_ID")
                    .distinct(),
            },
            function () {
                this.on("UR.ROLE_ID", "=", "IT.ITASK_ASSESMENT_ROLE_ID").andOn(
                    "UR.USER_ID",
                    "=",
                    globalThis.database.raw("?", [userId]),
                );
            },
        );

        conn.where(function () {
            this.where(function () {
                this.where(function () {
                    this.whereRaw(
                        `(("IT"."ITASK_ASSESMENT_ROLE_ID" IS NOT NULL and "UR"."USER_ID" IS NOT NULL) or "IT"."ITASK_ASSESMENT_ROLE_ID" IS NULL)`,
                    )
                        .whereRaw(
                            `EXISTS (SELECT 1 FROM "USER_ORGANIZATION_STRUCTURE" "UOS"
                        JOIN "ORGANIZATION_STRUCTURE" "OS" ON "OS"."ORGSTR_ID" = "UOS"."ORGSTR_ID"
                        WHERE ("UOS"."USER_ID" = ? OR "OS"."MANAGER_USER_ID" = ?)
                        AND ("UOS"."ORGSTR_ID" = "IT"."ITASK_ASSESMENT_ORGSTR_CNST" OR "IT"."ITASK_ASSESMENT_ORGSTR_CNST" IS NULL))`,
                            [userId, userId],
                        )
                        .where("IP.IPROC_STATUS", "A")
                        .where("IT.ITASK_STATUS", "T");
                });

                this.orWhere(function () {
                    this.where(function () {
                        this.where("IP.IPROC_STATUS", "A")
                            .where("IT.ITASK_STATUS", "W")
                            .where("IT.ITASK_ASSESMENT_METHOD", "S")
                            .whereNull("IT.ITASK_USER_ID")
                            .whereNot("IT.ITASK_TYPE", "P")
                            .whereRaw(`"IT"."ITASK_ASSESMENT_USER_ID" is null`);
                    });

                    this.orWhere(function () {
                        this.where("IP.IPROC_STATUS", "A")
                            .where("IT.ITASK_STATUS", "W")
                            .whereIn("IT.ITASK_ASSESMENT_METHOD", [
                                "S",
                                "U",
                                "P",
                            ])
                            .whereNull("IT.ITASK_USER_ID")
                            .whereNot("IT.ITASK_TYPE", "P")
                            .whereRaw(`"IT"."ITASK_ASSESMENT_USER_ID" = ?`, [
                                userId,
                            ]);
                    });

                    this.orWhere(function () {
                        this.where("IP.IPROC_STATUS", "A")
                            .where("IT.ITASK_USER_ID", userId)
                            .where("IT.ITASK_STATUS", "A")
                            .whereNotIn("ITASK_TYPE", ["P", "W"]);
                    });

                    this.orWhere(function () {
                        this.where("IP.IPROC_STATUS", "A")
                            .where("IT.ITASK_STATUS", "W")
                            .where(function () {
                                this.where("IT.ITASK_DUE_OFFSET", "po").orWhere(
                                    "IT.ITASK_DURATION",
                                    "po",
                                );
                            })
                            .where(function () {
                                this.where(function () {
                                    this.where(
                                        "IT.ITASK_USER_ID",
                                        userId,
                                    ).whereNull("IP.TPROC_ID");
                                }).orWhereRaw(
                                    `"IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)`,
                                    [userId, userId],
                                );
                            });
                    });
                });
            });
        });

        return coll;
    }

    mine(userId, checkRoles = false) {
        return this.all(userId, null, checkRoles).then((coll) => {
            coll.knex = this.applyActiveTasks(coll.knex, userId);
            return coll;
        });
    }

    applyActiveTasks(conn, userId) {
        return conn.where(function () {
            this.where(function () {
                this.where("IP.IPROC_STATUS", "A")
                    .where("IT.ITASK_STATUS", "W")
                    .whereIn("IT.ITASK_ASSESMENT_METHOD", ["S", "U", "P"])
                    .whereNull("IT.ITASK_USER_ID")
                    .whereNot("IT.ITASK_TYPE", "P")
                    .where(function () {
                        this.whereRaw(
                            `
                       "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                    `,
                            [userId, userId],
                        );
                    });
            })
                .orWhere(function () {
                    this.where("IP.IPROC_STATUS", "A")
                        .where("IT.ITASK_USER_ID", userId)
                        .where("IT.ITASK_STATUS", "A")
                        .whereNotIn("ITASK_TYPE", ["P", "W"]);
                })
                .orWhere(function () {
                    this.where("IP.IPROC_STATUS", "A")
                        .where("IT.ITASK_STATUS", "W")
                        .where(function () {
                            this.where("IT.ITASK_DUE_OFFSET", "po").orWhere(
                                "IT.ITASK_DURATION",
                                "po",
                            );
                        })
                        .where(function () {
                            this.where(function () {
                                this.where(
                                    "IT.ITASK_USER_ID",
                                    userId,
                                ).whereNull("IP.TPROC_ID");
                            }).orWhere(function () {
                                this.whereRaw(
                                    `
                           "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)`,
                                    [userId, userId],
                                );
                            });
                        });
                });
        });
    }

    getTaskMineAfterTerminList(userId) {
        return this.mine(userId).then((coll) => {
            coll.knex.where(function () {
                this.whereRaw(
                    `"IT"."ITASK_DUE_DATE_FINISH" < ${globalThis.orm.db.sysDate()}`,
                );
            });

            return coll;
        });
    }

    /**
     * Returns tasks to assign to user (handover) with empty user (not to pull)
     *
     * @param userId
     */
    async getToAssign(userId) {
        const coll = await this.all(userId);

        coll.knex.where(function () {
            this.where(function () {
                this.where("IP.IPROC_STATUS", "A")
                    .whereNull("IT.ITASK_USER_ID")
                    .where("IT.ITASK_STATUS", "W")
                    .whereIn("IT.ITASK_ASSESMENT_METHOD", ["S", "U", "P"])
                    .whereNot("IT.ITASK_TYPE", "P")
                    .where((builder) => {
                        const managedUsers = `(select "USER_ID" from "MANAGED_USERS")`;

                        builder
                            .where("IT.ITASK_ASSESMENT_USER_ID", userId)
                            .orWhere((builder) => {
                                builder
                                    .whereNull("IT.ITASK_ASSESMENT_USER_ID")
                                    .where(
                                        "IP.IPROC_INST_OWNER_USER_ID",
                                        userId,
                                    );
                            })
                            .orWhereIn(
                                "IT.ITASK_ASSESMENT_USER_ID",
                                globalThis.database.raw(managedUsers),
                            )
                            .orWhere((builder) => {
                                builder
                                    .whereNull("IT.ITASK_ASSESMENT_USER_ID")
                                    .whereIn(
                                        "IP.IPROC_INST_OWNER_USER_ID",
                                        globalThis.database.raw(managedUsers),
                                    );
                            })
                            .orWhereIn(
                                "IT.ITASK_USER_ID",
                                globalThis.database.raw(managedUsers),
                            );
                    });
            });
        });
        return coll;
    }

    /**
     * Task to pull
     *
     * @param userId
     * @param taskId
     * @return {Promise<BaseCollection>}
     */
    getToPull(userId, taskId: number | null = null) {
        const repo = globalThis.orm.repo("instanceTask", this.connection);
        return repo.toPull(userId).then((coll) => {
            if (taskId) {
                coll.knex.where("ITASK_ID", taskId);
            }
            return coll;
        });

        // return globalThis.orm.repo("task", this.connection).getTaskList(userId, sup).where(function(){
        //    this.whereRaw(`EXISTS(
        //        SELECT 1 FROM USER_ROLES UR
        //        WHERE (UR.USER_ID = ? AND UR.ROLE_ID = ITASK_ASSESMENT_ROLE_ID)
        //            OR ITASK_ASSESMENT_ROLE_ID IS NULL)
        //    `, [userId]).whereRaw(`
        //        EXISTS (SELECT 1 FROM USER_ORGANIZATION_STRUCTURE UOS
        //      JOIN ORGANIZATION_STRUCTURE OS ON OS.ORGSTR_ID = UOS.ORGSTR_ID
        //      WHERE (UOS.USER_ID = ? OR OS.MANAGER_USER_ID = ?)
        //        AND (UOS.ORGSTR_ID = TLSIT.ITASK_ASSESMENT_ORGSTR_CNST OR TLSIT.ITASK_ASSESMENT_ORGSTR_CNST IS NULL))
        //    `, [userId, userId])
        // }).as("IT");
    }

    async done(userId) {
        const user =
            await globalThis.container.service.temporary.cacheModule.getCachedUser(
                userId,
            );
        const globalSupervisor = user && user.isGlobalSupervisor() ? 1 : 0;
        const inspector = user && user.isInspector() ? 1 : 0;

        return await this.all(userId).then((coll) => {
            coll.knex.where(function () {
                if (inspector || globalSupervisor) {
                    this.where("IT.ITASK_STATUS", "D").whereNotIn(
                        "IT.ITASK_TYPE",
                        ["P", "W"],
                    );
                } else {
                    this.where("IT.ITASK_USER_ID", userId)
                        .where("IT.ITASK_STATUS", "D")
                        .whereNotIn("IT.ITASK_TYPE", ["P", "W"]);
                }
            });

            return coll;
        });
    }

    getToAssignDues(userId) {
        return this.all(userId).then((coll) => {
            coll.knex.where(function () {
                this.where("IP.IPROC_STATUS", "A")
                    .where("IT.ITASK_STATUS", "W")
                    .where(function () {
                        this.where("IT.ITASK_DUE_OFFSET", "po").orWhere(
                            "IT.ITASK_DURATION",
                            "po",
                        );
                    });
            });

            return coll;
        });
    }

    active(userId) {
        return this.all(userId).then((coll) => {
            coll.knex.where(function () {
                this.where("IT.ITASK_STATUS", "A");
            });

            return coll;
        });
    }

    nonActive(userId) {
        return this.all(userId).then((coll) => {
            coll.knex.where(function () {
                this.where("IT.ITASK_STATUS", "D");
            });

            return coll;
        });
    }

    /**
     * Get all tasks with rights by old way (without externalRights).
     *
     * @param userId
     * @return {*}
     */
    async getAll(userId) {
        const user =
            await globalThis.container.service.temporary.cacheModule.getCachedUser(
                userId,
            );
        const globalSupervisor = user && user.isGlobalSupervisor() ? 1 : 0;
        const inspector = user && user.isInspector() ? 1 : 0;

        return await this.all(userId).then((coll) => {
            if (!globalSupervisor && !inspector) {
                coll.knex.where((builder) => {
                    const managedUsersAndSelf = `(select "USER_ID" from "MANAGED_USERS" union select ${userId}${globalThis.orm.db.fromDual()})`;

                    builder
                        .whereIn(
                            "IT.ITASK_USER_ID",
                            globalThis.database.raw(managedUsersAndSelf),
                        )
                        .orWhereIn(
                            "IT.ITASK_ASSESMENT_USER_ID",
                            globalThis.database.raw(managedUsersAndSelf),
                        )
                        .orWhereIn(
                            "IP.IPROC_INST_OWNER_USER_ID",
                            globalThis.database.raw(managedUsersAndSelf),
                        );
                });
            }

            return coll;
        });
    }

    /**
     * Get all tasks with rights (with externalRights).
     *
     * @param userId
     * @return {*}
     */
    getBySolvers(userId, onlyActiveTasks = false) {
        return this.all(userId, []).then(async (coll) => {
            const user =
                await globalThis.container.service.temporary.cacheModule.getCachedUser(
                    userId,
                );
            const inspector = user?.isInspector();
            const globalSupervisor = user?.isGlobalSupervisor();

            const conn = this.connection;
            if (!inspector && !globalSupervisor) {
                coll.knex
                    // extends rights to all processes
                    .orWhereIn(
                        "IP.IPROC_ID",
                        globalThis.orm
                            .repo("externalRight", conn)
                            .getUserProcesses(userId),
                    )
                    .whereIn("IP.IPROC_STATUS", [
                        PROCESS.STATUS_ACTIVE,
                        PROCESS.STATUS_DONE,
                    ])
                    .whereIn("IT.ITASK_STATUS", [
                        TASK.STATUS_ACTIVE,
                        TASK.STATUS_DONE,
                    ])
                    .whereIn("IT.ITASK_TYPE", [TASK.TYPE_STANDARD]);
            } else {
                coll.knex
                    .whereIn("IP.IPROC_STATUS", [
                        PROCESS.STATUS_ACTIVE,
                        PROCESS.STATUS_DONE,
                    ])
                    .whereIn("IT.ITASK_STATUS", [
                        TASK.STATUS_ACTIVE,
                        TASK.STATUS_DONE,
                    ])
                    .whereIn("IT.ITASK_TYPE", [TASK.TYPE_STANDARD]);
            }

            if (onlyActiveTasks) {
                coll.knex.where("IT.ITASK_STATUS", "A");
            }

            return coll;
        });
    }

    getForProcess(userId, iprocId, getAll) {
        return this.all(userId).then((coll) => {
            coll.knex.where(function () {
                const self = this;
                self.where("IP.IPROC_ID", iprocId);

                if (!getAll) {
                    self.where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE).where(
                        function () {
                            this.whereIn("IT.ITASK_STATUS", [
                                TASK.STATUS_ACTIVE,
                                TASK.STATUS_WAITING,
                                TASK.STATUS_PULL,
                            ]).orWhere(function () {
                                this.where("IT.ITASK_STATUS", TASK.STATUS_NEW)
                                    .where(
                                        "IT.ITASK_AUTO_START",
                                        TASK.AUTOSTART_YES,
                                    )
                                    .where(
                                        "IP.IPROC_STATUS",
                                        PROCESS.STATUS_ACTIVE,
                                    );
                            });
                        },
                    );
                }
            });
            return coll;
        });
    }

    getITaskListByMainProcess(userId, iprocMainIprocId) {
        return this.all(userId).then((coll) => {
            coll.knex
                .where(function () {
                    this.where(function () {
                        this.where("IT.ITASK_USER_ID", userId).where(
                            "IT.ITASK_STATUS",
                            "A",
                        );
                    })
                        .orWhere(function () {
                            this.where(function () {
                                this.where("IP.IPROC_STATUS", "A")
                                    .where("IT.ITASK_STATUS", "W")
                                    .whereIn("IT.ITASK_ASSESMENT_METHOD", [
                                        "S",
                                        "U",
                                        "P",
                                    ])
                                    .whereNull("IT.ITASK_USER_ID");
                            });
                        })
                        .orWhere(function () {
                            this.where(function () {
                                this.where("IP.IPROC_STATUS", "A")
                                    .where("IT.ITASK_STATUS", "W")
                                    .where(function () {
                                        this.where(
                                            "IT.ITASK_DUE_OFFSET",
                                            "po",
                                        ).orWhere("IT.ITASK_DURATION", "po");
                                    });
                            });
                        });
                })
                .where("IP.IPROC_MAIN_IPROC_ID", iprocMainIprocId);

            return coll;
        });
    }

    /**
     * Returns task needs to show to user.
     *
     * @param task
     * @param user
     * @returns {BaseCollection}
     */
    getNotSeenTasks(task, user) {
        const collection = this.getCollection(null, "IT");

        // search for next task if exist
        collection.knex
            .innerJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IT.IPROC_ID")
            .where({
                ITASK_USER_ID: user.USER_ID,
                ITASK_STATUS: "A",
                "IP.IPROC_ID": task.IPROC_ID,
                IPROC_STATUS: PROCESS.STATUS_ACTIVE,
                ITASK_SEEN: "N",
            })
            .where("ITASK_TYPE", "<>", "P")
            .limit(1)
            .orderBy("ITASK_ID", "DESC");
        return collection;
    }

    async reject(itaskId, userId) {
        const meta = {
            itask_id: itaskId,
            user_id: userId,
        };

        const ttaskRepo = globalThis.orm.repo("templateTask", this.connection);
        await this.get(itaskId).then((itask) =>
            ttaskRepo.get(itask.TTASK_ID).then((ttask) => {
                if (ttask.TTASK_IS_REJECTABLE === "N") {
                    throw new UserException(
                        "Task is not in rejectable state.",
                        "TTASK_IS_NOT_REJECTABLE",
                        meta,
                    );
                }

                meta.itask_user_id = itask.ITASK_USER_ID;

                if (itask.ITASK_USER_ID !== userId) {
                    throw new UserException(
                        "You can not reject this task. You are not solver.",
                        "USER_IS_NOT_SOLVER",
                        meta,
                    );
                }

                if (!itask.ITASK_ASSESMENT_USER_ID) {
                    throw new UserException(
                        "This task has no supervisor",
                        "NO_SUPERVISOR",
                        meta,
                    );
                }

                const attrs = {
                    ITASK_USER_ID: null,
                    ITASK_ASSESMENT_METHOD: "S",
                    ITASK_STATUS: "W",
                    ITASK_TYPE: "S",
                };

                return this.connection
                    .select()
                    .from("INSTANCE_TASKS")
                    .where("ITASK_ID", itaskId)
                    .update(attrs);
            }),
        );
        // TODO: change user email? Or send direct email about task refusal?
    }

    async delegate(
        itaskId: number,
        userId: number,
        delegateUserId: number,
    ): Promise<void> {
        const meta = {
            itask_id: itaskId,
            user_id: userId,
            delegate_user_id: delegateUserId,
        };

        const ttaskRepo = globalThis.orm.repo("templateTask", this.connection);

        const itask = await this.get(itaskId);
        const ttask = await ttaskRepo.get(itask.TTASK_ID);

        if (ttask.TTASK_IS_DELEGATABLE === "N") {
            throw new UserException(
                "Task is not in delegatable state.",
                "TTASK_IS_NOT_DELEGATABLE",
                meta,
            );
        }

        meta.itask_user_id = itask.ITASK_USER_ID;

        if (itask.ITASK_USER_ID !== userId) {
            throw new UserException(
                "You can not delegate this task. You are not solver.",
                "USER_IS_NOT_SOLVER",
                meta,
            );
        }

        const organizationRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        const managedUserList = await organizationRepo.getManagedUsers(userId);

        if (!managedUserList.includes(delegateUserId)) {
            throw new UserException(
                `You can not delegate this task. You are not manager of delegated_user_id = ${delegateUserId}`,
                "NOT_USER_MANAGER",
                meta,
            );
        }

        const attrs = {
            ITASK_USER_ID: delegateUserId,
        };

        await this.connection
            .select()
            .from("INSTANCE_TASKS")
            .where("ITASK_ID", itaskId)
            .update(attrs);

        // Add static rights
        const exRights = globalThis.orm.repo("externalRight", this.connection);
        await exRights.assignUserRights(itask.IPROC_ID, delegateUserId);

        // Change 'newTask' notification addressee
        const mailRepo = globalThis.orm.repo("mailQ", this.connection);
        await mailRepo.changeMailRecipient(itask.ITASK_ID, delegateUserId);
    }

    /**
     *
     * Copy all tasks from template into process.
     * @param {Process} processEntity
     */
    copyTasksFromTemplate(processEntity) {
        const iprocId = processEntity.IPROC_ID;
        const tprocId = processEntity.TPROC_ID;

        if (!iprocId || !tprocId) {
            throw new UserException("Undefined iproc id or tproc id!");
        }

        const taskMap = {};
        const copySql = this.copyTasksFromTemplateSql(); // Due to optimise
        return this.connection
            .raw(copySql, {
                IPROC_ID: iprocId,
                TPROC_ID: processEntity.TPROC_ID,
            })
            .then(() =>
                // Copy ttask to itask from template
                // Get all instances
                this.connection
                    .select(["TTASK_ID", "ITASK_ID", "ITASK_TYPE"])
                    .from(this.tableName)
                    .where("IPROC_ID", iprocId)
                    .then((itasks) => {
                        if (Array.isArray(itasks) && itasks.length > 0) {
                            itasks.forEach((itask) => {
                                taskMap[itask.TTASK_ID] = itask;
                            });
                        }
                    }),
            )
            .then(() => {
                const notifRepo = globalThis.orm.repo(
                    "instanceTaskEmailNotifs",
                    this.connection,
                );
                return notifRepo.copyForTemplateTasks(taskMap);
            })
            .then(() => {
                const invRepo = globalThis.orm.repo(
                    "instanceTaskInvitation",
                    this.connection,
                );
                return invRepo.copyForTemplateTasks(taskMap);
            })
            .then(() => {
                const varRepo = globalThis.orm.repo(
                    "variable",
                    this.connection,
                );
                return varRepo.copyVariablesFromTemplate(
                    processEntity.IPROC_ID,
                );
            })
            .then(() => {
                const graphRepo = globalThis.orm.repo(
                    "instanceGraph",
                    this.connection,
                );
                return graphRepo.copyGraphFromTemplate(processEntity.IPROC_ID);
            })
            .catch((err) => {
                globalThis.tasLogger.error(err.message, {
                    err,
                });
                throw err;
            });
    }

    /**
     * Sql to select/insert tasks from template
     */
    copyTasksFromTemplateSql() {
        return `
            INSERT INTO "INSTANCE_TASKS" (
    "IPROC_ID",
    "ORG_ID",
    "ITASK_ID",
    "TTASK_ID",
    "ITASK_NAME",
    "ITASK_DESCRIPTION",
    "ITASK_DUE_OFFSET",
    "ITASK_DURATION",
    "ITASK_ASSESMENT_ROLE_ID",
    "ITASK_ASSESMENT_HIERARCHY",
    "ITASK_ASSESMENT_METHOD",
    "ITASK_ASSESMENT_USER_ID",
    "ITASK_ASSESMENT_ORGSTR_ID",
    "ITASK_ASSESMENT_TTASK_ID",
    "ITASK_PETRI_NET_INPUT",
    "ITASK_SUFFICIENT_END",
    "ITASK_TYPE",
    "ITASK_ASSESMENT_ORGSTR_CNST",
    "ITASK_EVENT",
    "ITASK_EVENT_WAIT",
    "ITASK_RUN_ONLY_ONCE",
    "ITASK_DISC_FLAG",
    "ITASK_MULTIINSTANCE_FLAG",
    "ITASK_ASSESMENT_TVAR_ID",
    "ITASK_DUTY",
    "ITASK_GEN_HISTORY",
    "ITASK_INVOKE_EVENT",
    "ITASK_ITERATE_OVER",
    "ITASK_REFERENCE_USER",
    "ITASK_AGAIN",
    "ITASK_AUTO_START",
    "ITASK_STATUS"
)
SELECT
    :IPROC_ID AS "IPROC_ID",
    1 AS "ORG_ID",
    ${globalThis.orm.db.sequence("ITASK_ID_SEQ")},
    "TTASK_ID",
    "TTASK_NAME",
    "TTASK_DESCRIPTION",
    "TTASK_DUE_OFFSET",
    "TTASK_DURATION",
    "TTASK_ASSESMENT_ROLE_ID",
    "TTASK_ASSESMENT_HIERARCHY",
    COALESCE("TTASK_ASSESMENT_METHOD", 'P'),
    "TTASK_ASSESMENT_USER_ID",
    "TTASK_ASSESMENT_ORGSTR_ID",
    "TTASK_ASSESMENT_TTASK_ID",
    "TTASK_PETRI_NET_INPUT",
    "TTASK_SUFFICIENT_END",
    "TTASK_TYPE",
    "TTASK_ASSESMENT_ORGSTR_CNST",
    "TTASK_EVENT",
    "TTASK_EVENT_WAIT",
    "TTASK_RUN_ONLY_ONCE",
    "TTASK_DISC_FLAG",
    "TTASK_MULTIINSTANCE_FLAG",
    "TTASK_ASSESMENT_TVAR_ID",
    "TTASK_DUTY",
    "TTASK_GEN_HISTORY",
    COALESCE("TTASK_INVOKE_EVENT", 'I'),
    "TTASK_ITERATE_OVER",
    "TTASK_REFERENCE_USER",
    "TTASK_AGAIN",
    'N', -- ITASK_AUTO_START
    'N'  -- ITASK_STATUS
FROM "TEMPLATE_TASKS"
WHERE "TPROC_ID" = :TPROC_ID;

        `;
    }

    /**
     * Create task.
     * @param ORG_ID
     * @param IPROC_ID
     * @param TTASK_ID
     * @param ITASK_NAME
     * @param ITASK_IS_IPROC_ID
     * @param ITASK_DESCRIPTION
     * @param ITASK_DUE_DATE_START
     * @param ITASK_DUE_DATE_FINISH
     * @param ITASK_ACTUAL_DATE_START
     * @param ITASK_ACTUAL_DATE_FINISH
     * @param ITASK_AUTO_START
     * @param ITASK_DUE_OFFSET
     * @param ITASK_DURATION
     * @param ITASK_USER_ID
     * @param ITASK_ASSESMENT_ROLE_ID
     * @param ITASK_ASSESMENT_HIERARCHY
     * @param ITASK_ASSESMENT_METHOD
     * @param ITASK_ASSESMENT_USER_ID
     * @param ITASK_ASSESMENT_ORGSTR_ID
     * @param ITASK_ASSESMENT_TTASK_ID
     * @param ITASK_PETRI_NET_INPUT
     * @param ITASK_SUFFICIENT_END
     * @param ITASK_TYPE
     * @param ITASK_ASSESMENT_ORGSTR_CNST
     * @param ITASK_EVENT
     * @param ITASK_EVENT_WAIT
     * @param ITASK_RUN_ONLY_ONCE
     * @param ITASK_DISC_FLAG
     * @param ITASK_MULTIINSTANCE_FLAG
     * @param ITASK_CUSTOM_PRINT
     * @param ITASK_ASSESMENT_TVAR_ID
     * @param ITASK_DUTY
     * @param ITASK_GEN_HISTORY
     * @param ITASK_INVOKE_EVENT
     * @param ITASK_ITERATE_OVER
     * @param ITASK_REFERENCE_USER
     * @param ITASK_AGAIN
     */
    create(
        ORG_ID,
        IPROC_ID,
        TTASK_ID,
        ITASK_NAME,
        ITASK_IS_IPROC_ID,
        ITASK_DESCRIPTION,
        ITASK_DUE_DATE_START,
        ITASK_DUE_DATE_FINISH,
        ITASK_ACTUAL_DATE_START,
        ITASK_ACTUAL_DATE_FINISH,
        ITASK_AUTO_START,
        ITASK_DUE_OFFSET,
        ITASK_DURATION,
        ITASK_USER_ID,
        ITASK_ASSESMENT_ROLE_ID,
        ITASK_ASSESMENT_HIERARCHY,
        ITASK_ASSESMENT_METHOD,
        ITASK_ASSESMENT_USER_ID,
        ITASK_ASSESMENT_ORGSTR_ID,
        ITASK_ASSESMENT_TTASK_ID,
        ITASK_PETRI_NET_INPUT,
        ITASK_SUFFICIENT_END,
        ITASK_TYPE,
        ITASK_ASSESMENT_ORGSTR_CNST,
        ITASK_EVENT,
        ITASK_EVENT_WAIT,
        ITASK_RUN_ONLY_ONCE,
        ITASK_DISC_FLAG,
        ITASK_MULTIINSTANCE_FLAG,
        ITASK_CUSTOM_PRINT,
        ITASK_ASSESMENT_TVAR_ID,
        ITASK_DUTY,
        ITASK_GEN_HISTORY,
        ITASK_INVOKE_EVENT,
        ITASK_ITERATE_OVER,
        ITASK_REFERENCE_USER,
        ITASK_AGAIN,
    ) {
        const entity = this.getEntity({
            ORG_ID,
            IPROC_ID,
            TTASK_ID,
            ITASK_NAME,
            ITASK_IS_IPROC_ID,
            ITASK_DESCRIPTION,
            ITASK_DUE_DATE_START,
            ITASK_DUE_DATE_FINISH,
            ITASK_ACTUAL_DATE_START,
            ITASK_ACTUAL_DATE_FINISH,
            ITASK_AUTO_START,
            ITASK_DUE_OFFSET,
            ITASK_DURATION,
            ITASK_USER_ID,
            ITASK_ASSESMENT_ROLE_ID,
            ITASK_ASSESMENT_HIERARCHY,
            ITASK_ASSESMENT_METHOD,
            ITASK_ASSESMENT_USER_ID,
            ITASK_ASSESMENT_ORGSTR_ID,
            ITASK_ASSESMENT_TTASK_ID,
            ITASK_PETRI_NET_INPUT,
            ITASK_SUFFICIENT_END,
            ITASK_TYPE,
            ITASK_ASSESMENT_ORGSTR_CNST,
            ITASK_EVENT,
            ITASK_EVENT_WAIT,
            ITASK_RUN_ONLY_ONCE,
            ITASK_DISC_FLAG,
            ITASK_MULTIINSTANCE_FLAG,
            ITASK_CUSTOM_PRINT,
            ITASK_ASSESMENT_TVAR_ID,
            ITASK_DUTY,
            ITASK_GEN_HISTORY,
            ITASK_INVOKE_EVENT,
            ITASK_ITERATE_OVER,
            ITASK_REFERENCE_USER,
            ITASK_AGAIN,
        });

        return this.store(entity)
            .then((id) => {
                entity.ITASK_ID = id;
                return entity;
            })
            .then((task) => {
                const rightRepo = globalThis.orm.repo(
                    "externalRight",
                    this.connection,
                );

                // Assign user rights.
                return Promise.all([
                    rightRepo.assignUserRights(
                        IPROC_ID,
                        ITASK_ASSESMENT_USER_ID,
                    ),
                    rightRepo.assignUserRights(IPROC_ID, ITASK_USER_ID),
                ]).then(() => task);
            });
    }

    /**
     * Return task by general name.
     *
     * @param iprocId
     * @param taskName
     * @return {BaseCollection}
     */
    getByName(iprocId, taskName) {
        const collection = this.getCollection();
        collection.knex
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("ITASK_NAME", taskName);
        return collection;
    }

    getById(id) {
        const collection = this.getCollection();

        if (Array.isArray(id)) {
            collection.knex.from(this.tableName).whereIn("ITASK_ID", id);
        } else {
            collection.knex.from(this.tableName).where("ITASK_ID", id);
        }

        return collection;
    }

    /**
     * @param {string} iprocId
     * @param {number} ttaskId
     * @return {BaseCollection}
     */
    getByTTaskId(iprocId, ttaskId) {
        const collection = this.getCollection();
        collection.knex
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("TTASK_ID", ttaskId);

        return collection;
    }

    async iTaskReport() {
        const extraCols = [];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            extraCols.push(`TP.TPROC_NAME_${lang.toUpperCase()}`);
            extraCols.push(`TT.TTASK_NAME_${lang.toUpperCase()}`);
        });
        const conn = this.connection
            .select(
                [
                    "IT.ITASK_ID",
                    "IT.ITASK_NAME",
                    "IT.ITASK_DUE_DATE_FINISH",
                    "IT.ITASK_USER_ID",
                    "IT.ITASK_ACTUAL_DATE_START",
                    "IT.ITASK_DUE_OFFSET",
                    "IT.ITASK_DURATION",
                    "IT.ITASK_STATUS",
                    "IT.ITASK_ASSESMENT_USER_ID",
                    "IT.ITASK_ASSESMENT_METHOD",
                    "IP.IPROC_ID",
                    "IP.IPROC_NAME",
                    "IP.IPROC_INST_OWNER_USER_ID",
                    "TP.TPROC_ID",
                    "TP.TPROC_NAME",
                    "UP.USER_FIRST_NAME",
                    "UP.USER_LAST_NAME",
                    "UP.USER_EMAIL",
                ].concat(extraCols),
            )
            .from("INSTANCE_TASKS as IT")
            .join("INSTANCE_PROCESSES as IP", "IT.IPROC_ID", "=", "IP.IPROC_ID")
            .join("TEMPLATE_PROCESSES as TP", "IP.TPROC_ID", "=", "TP.TPROC_ID")
            .join("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin("USERS as U", "U.USER_ID", "IT.ITASK_USER_ID")
            .leftJoin(
                "USERS as UP",
                "UP.USER_ID",
                "IP.IPROC_INST_OWNER_USER_ID",
            )
            .where("IP.IPROC_STATUS", "A")
            .andWhere(function () {
                this.where("U.USER_STATUS", "A")
                    .orWhere("UP.USER_STATUS", "A")
                    .andWhere(function () {
                        this.whereNull("IT.ITASK_USER_ID");
                    });
            })
            .andWhere(function () {
                this.whereNot("IT.ITASK_TYPE", "P"); // subprocess
            })
            .andWhere(function () {
                this.whereIn("IT.ITASK_STATUS", ["A", "W"]);
            })
            .orderBy("IT.ITASK_ACTUAL_DATE_START", "desc")
            .orderBy("IT.ITASK_DUE_DATE_FINISH", "asc");

        const entities = await this.createCollection(conn).collectAll();

        const assocEntities = {};
        entities.map((item) => {
            if (item.ITASK_USER_ID == "" || item.ITASK_USER_ID == null) {
                if (
                    item.ITASK_ASSESMENT_USER_ID == "" ||
                    item.ITASK_ASSESMENT_USER_ID == null
                ) {
                    if (
                        typeof assocEntities[
                            item.raw.IPROC_INST_OWNER_USER_ID
                        ] === "undefined"
                    ) {
                        assocEntities[item.raw.IPROC_INST_OWNER_USER_ID] = [];
                    }
                    assocEntities[item.raw.IPROC_INST_OWNER_USER_ID].push(item);
                } else {
                    if (
                        typeof assocEntities[item.ITASK_ASSESMENT_USER_ID] ===
                        "undefined"
                    ) {
                        assocEntities[item.ITASK_ASSESMENT_USER_ID] = [];
                    }
                    assocEntities[item.ITASK_ASSESMENT_USER_ID].push(item);
                }
            } else {
                if (typeof assocEntities[item.ITASK_USER_ID] === "undefined") {
                    assocEntities[item.ITASK_USER_ID] = [];
                }
                assocEntities[item.ITASK_USER_ID].push(item);
            }
        });

        // Send e-mail
        const targetUsers = await globalThis.orm
            .repo("user", this.connection)
            .getById(Object.keys(assocEntities))
            .collectAll();

        // Get all user (and vice) settings
        const users = {};
        const activeVices = await globalThis.orm
            .repo("userVice")
            .getUsersViced(
                targetUsers.map((user) => user.USER_ID),
                true,
            )
            .collectAll();
        const userIds = targetUsers
            .map((user) => user.USER_ID)
            .concat(activeVices.map((user) => user.USER_ID_VICE));
        const enabledLanguages = await globalThis.orm
            .repo("userParameter")
            .getUserParameter(userIds, USER_PARAMETERS.LANGUAGE_CLIENT);
        const enabledDateFormats = await globalThis.orm
            .repo("userParameter")
            .getUserParameter(userIds, "DATE_FORMAT");
        const enabledNotifications = await this.orm
            .repo("userParameter")
            .getUserParameter(
                userIds,
                USER_PARAMETERS.SEND_TASK_OVERVIEW_NOTIFICATION,
                true,
            );

        // Cache settings
        userIds.forEach((userId) => {
            const user =
                _.find(targetUsers, { USER_ID: userId }) ||
                _.find(activeVices, { USER_ID_VICE: userId });
            const userVice = _.find(activeVices, { USER_ID: userId });

            users[userId] = {
                USER_EMAIL: user.USER_EMAIL || user.raw.USER_VICE_EMAIL,
                USER_LANGUAGE: (
                    _.find(enabledLanguages, { USER_ID: userId }) || {
                        USRPAR_VALUE: globalThis.dynamicConfig.defaultLang,
                    }
                ).USRPAR_VALUE,
                DATE_FORMAT: (
                    _.find(enabledDateFormats, {
                        USER_ID: userId,
                    }) || {
                        USRPAR_VALUE: "L",
                    }
                ).USRPAR_VALUE,
                NOTIFICATION_ENABLED: !!_.find(enabledNotifications, {
                    USER_ID: userId,
                }),
                UV_ID: userVice ? userVice.UV_ID : null,
                VICE_USER_ID: userVice ? userVice.USER_ID_VICE : null,
                USER_FIRST_NAME: user.raw.USER_FIRST_NAME,
                USER_LAST_NAME: user.raw.USER_LAST_NAME,
                USER_DISPLAY_NAME: user.raw.USER_DISPLAY_NAME,
            };
        });

        // Send emails to each user - asynchronously, else it could take a long time
        for (const userId of targetUsers.map((user) => user.USER_ID)) {
            const user = users[userId];
            const userViceId = user.UV_ID ? user.VICE_USER_ID : null;

            // Does user have email and notification enabled?
            if (user.USER_EMAIL && user.NOTIFICATION_ENABLED) {
                // Prepare data to be rendered
                const tasksData = {
                    user_name: user.USER_DISPLAY_NAME,
                    tasks: assocEntities[userId],
                    uvId: null,
                };

                if (!tasksData.tasks || !tasksData) {
                    const error = new Error(
                        `No tasks available for user: ${user.USER_DISPLAY_NAME}.`,
                    );
                    globalThis.tasLogger.error(error);
                    throw error;
                }

                const mailOptionsParams: MailOptionsParams = {
                    addresses: user.USER_EMAIL,
                    subject: `${globalThis.__(
                        {
                            phrase: "iTaskDailyReport",
                            locale: user.USER_LANGUAGE,
                        },
                        { user: tasksData.user_name },
                    )}`,

                    bccOnMoreAddresses: true,
                    ignoreError: true,
                };

                await globalThis.tasLogger.runTask(async () => {
                    globalThis.tasLogger.setContextProperty(
                        "category",
                        LogCategory.CATEGORY_MAIL,
                    );
                    try {
                        await globalThis.routerMail.sendEmailViaClient(
                            "dailyReport",
                            mailOptionsParams,
                            tasksData,
                            user.USER_LANGUAGE,
                            user.DATE_FORMAT,
                        );
                    } catch (err) {
                        const meta = {
                            err,
                            address: user.USER_EMAIL,
                            subject: `${globalThis.__(
                                {
                                    phrase: "iTaskDailyReport",
                                    locale: user.USER_LANGUAGE,
                                },
                                { user: tasksData.user_name },
                            )}`,
                        };
                        globalThis.tasLogger.error(err.message, {
                            meta,
                        });
                        throw err;
                    }
                });
            }

            // Does the user have enabled vice?
            if (userViceId) {
                const vice = users[userViceId];
                // Does vice have email and notification enabled?
                if (vice.USER_EMAIL && vice.NOTIFICATION_ENABLED) {
                    // Prepare data to be rendered
                    const tasksData = {
                        user_name: user.USER_DISPLAY_NAME,
                        tasks: assocEntities[userId],
                        uvId: user.UV_ID,
                        viceUser: vice.USER_DISPLAY_NAME,
                    };

                    if (!tasksData.tasks || !tasksData) {
                        const error = new Error(
                            `No tasks available for user: ${user.USER_DISPLAY_NAME}.`,
                        );
                        globalThis.tasLogger.error(error);
                        throw error;
                    }

                    const mailOptionsParams: MailOptionsParams = {
                        addresses: vice.USER_EMAIL,
                        subject: `${globalThis.__(
                            {
                                phrase: "iTaskDailyReport",
                                locale: vice.USER_LANGUAGE,
                            },
                            { user: tasksData.viceUser },
                        )} ${globalThis.__({
                            phrase: "onBehalfOf",
                            locale: vice.USER_LANGUAGE,
                        })} ${tasksData.user_name}`,

                        bccOnMoreAddresses: true,
                        ignoreError: true,
                    };

                    await globalThis.tasLogger.runTask(async () => {
                        globalThis.tasLogger.setContextProperty(
                            "category",
                            LogCategory.CATEGORY_MAIL,
                        );
                        try {
                            await globalThis.routerMail.sendEmailViaClient(
                                "dailyReport",
                                mailOptionsParams,
                                tasksData,
                                user.USER_LANGUAGE,
                                user.DATE_FORMAT,
                            );
                        } catch (err) {
                            const meta = {
                                err,
                                address: vice.USER_EMAIL,
                                subject: `${globalThis.__(
                                    {
                                        phrase: "iTaskDailyReport",
                                        locale: vice.USER_LANGUAGE,
                                    },
                                    { user: tasksData.viceUser },
                                )} ${globalThis.__({
                                    phrase: "onBehalfOf",
                                    locale: vice.USER_LANGUAGE,
                                })} ${tasksData.user_name}`,
                            };
                            globalThis.tasLogger.error(err.message, {
                                meta,
                            });
                            throw err;
                        }
                    });
                }
            }
        }
    }

    async mailTaskDeadlines() {
        const extraCols = [];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            extraCols.push(`TT.TTASK_NAME_${lang.toUpperCase()}`);
        });

        const conn = this.connection
            .select(
                [
                    "IT.ITASK_ID",
                    "IT.ITASK_NAME",
                    "IP.IPROC_ID",
                    "IP.IPROC_NAME",
                    "IT.ITASK_USER_ID",
                    "SOLVER.USER_DISPLAY_NAME as SOLVER_USER_DISPLAY_NAME",
                    "IT.ITASK_ASSESMENT_USER_ID",
                    "IT.ITASK_DUE_DATE_FINISH",
                    "IP.IPROC_PRIORITY",
                    "IP.IPROC_INST_OWNER_USER_ID",
                ].concat(extraCols),
            )
            .from("INSTANCE_TASKS as IT")
            .join("INSTANCE_PROCESSES as IP", "IT.IPROC_ID", "=", "IP.IPROC_ID")
            .join("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin("USERS as SOLVER", "IT.ITASK_USER_ID", "SOLVER.USER_ID")
            .where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .andWhere(function () {
                this.whereIn("IT.ITASK_STATUS", [
                    TASK.STATUS_ACTIVE,
                    TASK.STATUS_WAITING,
                    TASK.STATUS_PULL,
                ]);
            })
            .andWhere(function () {
                // TODO: This might cause some mismatch behavior with DST, but f-it
                this.whereRaw(
                    `"IT"."ITASK_DUE_DATE_FINISH" < ${globalThis.orm.db.trunc(globalThis.orm.db.sysDate(), null)}`,
                );
            });

        const coll = await this.createCollection(conn).collectAll();

        const mailTo = {};
        const solverUsersIds = [];

        coll.forEach((iTask) => {
            solverUsersIds.push(iTask.ITASK_USER_ID);
        });

        const orgstrRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        const directManagers = await orgstrRepo
            .findManagerInUsersOrganization(conn, "ITASK_USER_ID")
            .collectAll();

        const managers = {};

        directManagers.forEach((org) => {
            managers[org.raw.USER_ID] = org.MANAGER_USER_ID;
        });

        // Cache mails to be sent into 'mailTo' variable
        coll.forEach((task) => {
            const userSolverId = task.ITASK_USER_ID;
            const directManagerId = directManagers[userSolverId];
            const supervisorId =
                task.ITASK_ASSESMENT_USER_ID ||
                task.raw.IPROC_INST_OWNER_USER_ID;
            [
                { id: directManagerId, property: "manager" },
                { id: supervisorId, property: "supervisor" },
            ].forEach((check) => {
                // If manager/supervisor exists and is not the Solver
                if (check.id && check.id !== userSolverId) {
                    // Check property existence
                    if (!mailTo[check.id]) {
                        mailTo[check.id] = { [check.property]: [] };
                    } else if (!mailTo[check.id][check.property]) {
                        mailTo[check.id][check.property] = [];
                    }
                    // Push Task
                    mailTo[check.id][check.property].push(task);
                }
            });
        });

        // Send emails
        const userRepo = globalThis.orm.repo("user", this.connection);
        const userColl = await userRepo
            .getById(Object.keys(mailTo))
            .collectAll();

        const activeVices = await globalThis.orm
            .repo("userVice")
            .getUsersViced(
                userColl.map((user) => user.USER_ID),
                true,
            )
            .collectAll();

        const userIds = userColl
            .map((user) => user.USER_ID)
            .concat(activeVices.map((user) => user.USER_ID_VICE));
        const enabledLanguages = await globalThis.orm
            .repo("userParameter")
            .getUserParameter(userIds, USER_PARAMETERS.LANGUAGE_CLIENT);
        const enabledDateFormats = await globalThis.orm
            .repo("userParameter")
            .getUserParameter(userIds, "DATE_FORMAT");
        const enabledNotifications = await this.orm
            .repo("userParameter")
            .getUserParameter(
                userIds,
                USER_PARAMETERS.SEND_MAIL_ESCALATION_NOTIFICATION,
                true,
            );

        const users = {};
        // Cache settings
        userIds.forEach((userId) => {
            const user =
                _.find(userColl, { USER_ID: userId }) ||
                _.find(activeVices, { USER_ID_VICE: userId });
            const isVice = !!user.USER_ID_VICE;
            users[userId] = {
                USER_ID: user.USER_ID,
                USER_EMAIL: isVice ? user.raw.USER_VICE_EMAIL : user.USER_EMAIL,
                USER_LANGUAGE: (
                    _.find(enabledLanguages, {
                        USER_ID: userId,
                    }) || {
                        USRPAR_VALUE: globalThis.dynamicConfig.defaultLang,
                    }
                ).USRPAR_VALUE,
                DATE_FORMAT: (
                    _.find(enabledDateFormats, {
                        USER_ID: userId,
                    }) || {
                        USRPAR_VALUE: "L",
                    }
                ).USRPAR_VALUE,
                UV_ID: isVice ? user.UV_ID : null,
                UV_USER: isVice ? `${user.raw.USER_VICE_DISPLAY_NAME}` : null,
                NOTIFICATION_ENABLED: !!_.find(enabledNotifications, {
                    USER_ID: userId,
                }),
                USER_FIRST_NAME: user.raw.USER_FIRST_NAME,
                USER_LAST_NAME: user.raw.USER_LAST_NAME,
                USER_DISPLAY_NAME: user.raw.USER_DISPLAY_NAME,
            };
        });

        const emailSendStatus = await Promise.all(
            userIds.map(async (userId) => {
                const user = users[userId];

                if (user.USER_EMAIL && user.NOTIFICATION_ENABLED) {
                    const tasksManager = _.cloneDeep(
                        mailTo[user.USER_ID].manager,
                    );
                    const tasksSupervisor = _.cloneDeep(
                        mailTo[user.USER_ID].supervisor,
                    );

                    await this.sendMailToManager(
                        user,
                        tasksManager,
                        user.USER_LANGUAGE,
                        user.DATE_FORMAT,
                    );
                    await this.sendMailToSupervisor(
                        user,
                        tasksSupervisor,
                        user.USER_LANGUAGE,
                        user.DATE_FORMAT,
                    );
                    return true;
                }
                return false;
            }),
        );
        return emailSendStatus;
    }

    async sendMailToManager(
        user,
        listOfTasks,
        language = globalThis.dynamicConfig.defaultLang,
        dateFormat = "L",
    ) {
        if (!_.isEmpty(listOfTasks)) {
            const tasksData = {
                user_name: user.USER_DISPLAY_NAME,
                uv_id: user.UV_ID,
                viceUser: user.UV_USER,
                tasks: listOfTasks,
            };

            const mailOptionsParams: MailOptionsParams = {
                addresses: user.USER_EMAIL,
                subject: globalThis.__({
                    phrase: "iTaskEscReportSub",
                    locale: language,
                }),

                bccOnMoreAddresses: true,
                ignoreError: false,
            };

            try {
                await globalThis.routerMail.sendEmailViaClient(
                    "escReportSub",
                    mailOptionsParams,
                    tasksData,
                    language,
                    dateFormat,
                );
            } catch (err) {
                globalThis.tasLogger.error(err.message, {
                    err,
                });
            }
        }
    }

    async sendMailToSupervisor(
        user,
        listOfTasks,
        language = globalThis.dynamicConfig.defaultLang,
        dateFormat = "L",
    ) {
        if (!_.isEmpty(listOfTasks)) {
            const tasksData = {
                user_name: user.USER_DISPLAY_NAME,
                uv_id: user.UV_ID,
                viceUser: user.UV_USER,
                tasks: listOfTasks,
            };

            const mailOptionsParams: MailOptionsParams = {
                addresses: user.USER_EMAIL,
                subject: globalThis.__({
                    phrase: "iTaskEscReportSup",
                    locale: language,
                }),

                bccOnMoreAddresses: true,
                ignoreError: false,
            };

            try {
                return await globalThis.routerMail.sendEmailViaClient(
                    "escReportSup",
                    mailOptionsParams,
                    tasksData,
                    language,
                    dateFormat,
                );
            } catch (err) {
                globalThis.tasLogger.error(err.message, {
                    err,
                });
                throw err;
            }
        }
    }

    getWaitingTasks(iprocId) {
        // Add TTASK_NAME mutations
        const extraColumns = ["TT.TTASK_NAME"];
        globalThis.dynamicConfig.langs.forEach((language) => {
            extraColumns.push(`TT.TTASK_NAME_${language.toUpperCase()}`);
        });

        if (!Array.isArray(iprocId)) {
            iprocId = [iprocId];
        }

        const columns = ["IT.*"].concat(extraColumns);

        const conn = this.connection
            .select(columns)
            .from(`${this.tableName} as IT`)
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .whereIn(
                "IT.IPROC_ID",
                Array.isArray(iprocId) ? iprocId : [iprocId],
            )
            .where("IT.ITASK_STATUS", TASK.STATUS_DELAYED)
            .where("IT.ITASK_TYPE", TASK.TYPE_EVENT_WAIT)
            .whereNotNull("IT.ITASK_EVENT");

        return globalThis.orm.collection<"instanceTask">("instanceTask", conn);
    }

    /**
     * Each process is checked if processId is null (used for cron).
     * @param processId
     * @returns {Promise.<*>}
     */
    async rescheduleDuration(processId = null) {
        // Reschedule tasks. Set ITASK_DURATION with current variable date value.
        let conn = this.connection
            .select(
                "IT.ITASK_ID",
                "IT.ITASK_NAME",
                "IV.IVAR_DATE_VALUE",
                "IT.ITASK_DURATION",
            )
            .from("INSTANCE_TASKS as IT")
            .leftJoin("INSTANCE_PROCESSES as IP", "IT.IPROC_ID", "IP.IPROC_ID")
            .leftJoin("INSTANCE_VARIABLES AS IV", function vars() {
                this.on("IP.IPROC_ID", "IV.IPROC_ID").andOn(
                    "IV.TVAR_ID",
                    globalThis.database.raw(
                        `${globalThis.orm.db.substr(`"ITASK_DURATION"`, 3)}`,
                    ),
                );
            })
            .where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .where("ITASK_DURATION", "like", "vc%")
            .where(
                "IT.ITASK_DUE_DATE_FINISH",
                "<>",
                globalThis.database.raw(`"IV"."IVAR_DATE_VALUE"`),
            )
            .whereNot("IT.ITASK_STATUS", TASK.STATUS_DONE);

        if (processId) {
            conn = conn.where("IPROC_ID", processId);
        }

        const rescheduledTasks = await conn;

        if (Array.isArray(rescheduledTasks) && rescheduledTasks.length > 0) {
            for (const taskToReschedule of rescheduledTasks) {
                globalThis.tasLogger.info(
                    `Rescheduling task duration '${taskToReschedule.ITASK_NAME}' (${taskToReschedule.ITASK_ID}) from ${taskToReschedule.ITASK_DUE_DATE_FINISH} to ${taskToReschedule.IVAR_DATE_VALUE}`,
                );
                await this.connection
                    .select("ITASK_ID")
                    .from("INSTANCE_TASKS")
                    .where("ITASK_ID", taskToReschedule.ITASK_ID)
                    .update(
                        "ITASK_DUE_DATE_FINISH",
                        taskToReschedule.IVAR_DATE_VALUE,
                    );
            }
        }

        return rescheduledTasks;
    }

    async getScheduledTasks(fromDate: Date | null = null, futureTasks = false) {
        // Reschedule tasks. Set ITASK_DUE_OFFSET with current variable date value.
        const columns = [
            "IT.ITASK_ID",
            "IT.ITASK_NAME",
            "IV.IVAR_DATE_VALUE",
            "IT.ITASK_DUE_DATE_START",
        ];
        const rescheduledTasks = await this.getTaskToRescheduleByOffset(
            columns,
        ).where(
            globalThis.database.raw(`"IT"."ITASK_DUE_DATE_START"`),
            "<>",
            globalThis.database.raw(`"IV"."IVAR_DATE_VALUE"`),
        );

        if (Array.isArray(rescheduledTasks) && rescheduledTasks.length > 0) {
            for (const taskToReschedule of rescheduledTasks) {
                globalThis.tasLogger.info(
                    `Rescheduling task due_date_offset ${taskToReschedule.ITASK_ID}:${taskToReschedule.ITASK_NAME} from ${taskToReschedule.ITASK_DUE_DATE_START} to ${taskToReschedule.IVAR_DATE_VALUE}`,
                );
                await this.connection
                    .select("ITASK_ID")
                    .from("INSTANCE_TASKS")
                    .where("ITASK_ID", taskToReschedule.ITASK_ID)
                    .update(
                        "ITASK_DUE_DATE_START",
                        taskToReschedule.IVAR_DATE_VALUE,
                    );
            }
        }

        // Task where ITASK_DUE_DATE_START < now
        const conn = this.getScheduledTasksConn(fromDate, futureTasks);
        return globalThis.orm.collection("instanceTask", conn);
    }

    getTaskToRescheduleByOffset(columns = []) {
        let conn;
        if (_.isEmpty(columns)) {
            conn = this.connection.select("IT.*");
        }
        if (!_.isEmpty(columns)) {
            conn = this.connection.select(columns);
        }

        return conn
            .from("INSTANCE_TASKS as IT")
            .leftJoin("INSTANCE_PROCESSES as IP", "IT.IPROC_ID", "IP.IPROC_ID")
            .leftJoin("INSTANCE_VARIABLES AS IV", function vars() {
                this.on("IP.IPROC_ID", "IV.IPROC_ID").andOn(
                    "IV.TVAR_ID",
                    globalThis.database.raw(
                        `${globalThis.orm.db.toInt()}`,
                        globalThis.database.raw(
                            `${globalThis.orm.db.substr(`"ITASK_DUE_OFFSET"`, 3)}`,
                        ),
                    ),
                );
            })
            .where("IT.ITASK_AUTO_START", TASK.AUTOSTART_YES)
            .where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .where("ITASK_DUE_OFFSET", "like", "vc%");
    }

    getScheduledTasksConn(fromDate, futureTasks, columns: string[] = []) {
        let conn;
        if (_.isEmpty(columns)) {
            conn = this.connection.select("IT.*");
        }
        if (!_.isEmpty(columns)) {
            conn = this.connection.select(columns.map((a) => `IT.${a}`));
        }

        return conn
            .from(`${this.tableName} as IT`)
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IT.IPROC_ID")
            .where("ITASK_STATUS", TASK.STATUS_NEW)
            .where("IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .where("ITASK_AUTO_START", TASK.AUTOSTART_YES)
            .where(function () {
                this.where("ITASK_TYPE", TASK.TYPE_EVENT_WAIT).orWhere(
                    (dateWhere) => {
                        if (!futureTasks) {
                            dateWhere.whereRaw(`"ITASK_DUE_DATE_START" < ?`, [
                                new Date(),
                            ]);
                        }
                        if (fromDate) {
                            dateWhere.where(
                                "ITASK_DUE_DATE_START",
                                ">",
                                fromDate,
                            );
                        }
                    },
                );
            });
    }

    getFailedScheduledTasksConn(lastRun, columns: string[] = []) {
        const fromDate = new Date(0);
        const conn = this.getScheduledTasksConn(fromDate, false, columns);
        return conn.where("ITASK_DUE_DATE_START", "<", lastRun);
    }

    async getFailedScheduledTasks() {
        const lastRun =
            await globalThis.container.service.cron.cronRunModule.getLastRunByName(
                "Cron",
            );
        const coll = await this.getScheduledTasks(new Date(0));
        coll.knex.where("ITASK_DUE_DATE_START", "<", lastRun);
        return coll;
    }

    getByProcess(iprocId, columns = null, alias = null) {
        const coll = this.getCollection(columns, alias);
        coll.knex.where("IPROC_ID", iprocId);
        return coll;
    }

    /**
     *
     * @param {InstanceTask} task
     */
    async delete(task) {
        await this.connection
            .from("INSTANCE_LINK_CONDITIONS")
            .whereIn("ITASKLINK_ID", function () {
                return this.select("ITASKLINK_ID")
                    .from("INSTANCE_TASK_LINKS")
                    .where("ITASKLINK_FROM_TTASK_ID", task.ITASK_ID)
                    .orWhere("ITASKLINK_TO_TTASK_ID", task.ITASK_ID);
            })
            .delete();

        await this.connection
            .from("INSTANCE_TASK_LINKS")
            .where("ITASKLINK_FROM_TTASK_ID", task.ITASK_ID)
            .orWhere("ITASKLINK_TO_TTASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("INSTANCE_TASK_COMPLETION")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("INSTANCE_TASK_EMAIL_NOTIFS")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("INSTANCE_TASK_HISTORY")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("INSTANCE_TASK_CALCULATIONS")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("INSTANCE_TASK_JS_CALCULATIONS")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("MAIL_QUEUE")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        await this.connection
            .from("DMS_FILE_ACCESS_LOG")
            .where("ITASK_ID", task.ITASK_ID)
            .delete();

        return await BaseRepository.prototype.delete.call(this, task); // workaround of super() in async
    }

    getMultiinstanceInstances(ttaskId, iprocId) {
        return this.connection
            .select("ITASK_ID")
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("TTASK_ID", ttaskId)
            .where("ITASK_MULTIINSTANCE_FLAG", "M")
            .where("ITASK_STATUS", "A");
    }

    getBulkCompletable(userId) {
        return this.mine(userId).then((coll) => {
            coll.knex.where("TT.TTASK_IS_BULK_COMPLETABLE", "Y");
            return coll;
        });
    }

    async rescheduleByVariable(iprocId, tvarId, dateValue) {
        if (!iprocId || !tvarId || !dateValue) {
            return;
        }

        const tm = new TimeMeasure();
        tm.start();

        const reschedulers = {
            ITASK_DUE_OFFSET: "ITASK_DUE_DATE_START",
            ITASK_DURATION: "ITASK_DUE_DATE_FINISH",
        };
        for (const column of Object.keys(reschedulers)) {
            const rescheduledTasks = await this.connection
                .select("ITASK_ID", "ITASK_NAME", "ITASK_DUE_DATE_FINISH")
                .from(this.tableName)
                .where("IPROC_ID", iprocId)
                .where(column, `vc${tvarId}`)
                .where(reschedulers[column], "<>", dateValue);

            if (
                Array.isArray(rescheduledTasks) &&
                rescheduledTasks.length > 0
            ) {
                for (const taskToReschedule of rescheduledTasks) {
                    globalThis.tasLogger.info(
                        `Rescheduling ${column} '${taskToReschedule.ITASK_NAME}' (${taskToReschedule.ITASK_ID}) from ${moment(
                            taskToReschedule[reschedulers[column]],
                        ).format(
                            "DD-MM-YYYY HH:mm:ss",
                        )} to ${moment(dateValue).format("DD-MM-YYYY HH:mm:ss")}`,
                        { iproc_id: iprocId },
                    );
                    await this.connection
                        .select("ITASK_ID")
                        .from("INSTANCE_TASKS")
                        .where("ITASK_ID", taskToReschedule.ITASK_ID)
                        .update(reschedulers[column], dateValue);
                }
            }
        }
        globalThis.tasLogger.info(
            `Rescheduling by variable ${tvarId} end in ${tm.endMs()}ms`,
            { iproc_id: iprocId },
        );
    }

    async rescheduleTasks(taskIds, newDate, userId: number = null) {
        const buildWhereIns = (builder, attr, items) => {
            // t3b-2764 - prevent select all
            if (_.isEmpty(items)) {
                return builder.where(false);
            }
            _.chunk(items, 999).forEach((chunk) => {
                builder.orWhereIn(attr, Array.isArray(chunk) ? chunk : [chunk]);
            });
        };

        const foundTasks = await this.connection(this.tableName)
            .select(["ITASK_ID", "IPROC_ID", "ITASK_NAME"])
            .where((builder) => {
                buildWhereIns(builder, "ITASK_ID", taskIds);
            });

        if (foundTasks.length !== taskIds.length) {
            throw new UserException(
                "Some tasks to reschedule not found",
                "NOT_FOUND",
                {
                    taskIds: _.difference(
                        taskIds,
                        foundTasks.map((a) => a.ITASK_ID),
                    ),
                },
            );
        }

        const columns = [
            "IV.IVAR_ID",
            "IV.IVAR_NAME",
            "IV.IVAR_DATE_VALUE",
            "IT.ITASK_ID",
            "IT.IPROC_ID",
            "IT.ITASK_NAME",
        ];
        const varConn = this.getTaskToRescheduleByOffset(columns).where(
            (builder) => {
                buildWhereIns(builder, "IT.ITASK_ID", taskIds);
            },
        );
        const vars = await varConn;
        const varIds = vars.map((a) => a.IVAR_ID);

        let relatedTasksByVar = [];
        if (!_.isEmpty(varIds)) {
            relatedTasksByVar = await this.getTasksByInstanceVariable(
                varIds,
                columns,
            );
        }
        const relatedTaskIds = relatedTasksByVar.map((a) => a.ITASK_ID);
        const joinedTaskIds = _.uniq([...relatedTaskIds, ...taskIds]);

        globalThis.tasLogger.info(
            `Found variables to reschedule. Total count: ${varIds.length}`,
            {
                ivarIds: varIds,
                ivarCount: varIds.length,
            },
        );
        if (!_.isEmpty(varIds)) {
            await this.connection("INSTANCE_VARIABLES")
                .where((builder) => {
                    buildWhereIns(builder, "IVAR_ID", varIds);
                })
                .update({ IVAR_DATE_VALUE: new Date(newDate) });
        }

        await this.connection(this.tableName)
            .where((builder) => {
                buildWhereIns(builder, "ITASK_ID", joinedTaskIds);
            })
            .update({
                ITASK_DUE_DATE_START: new Date(newDate),
                ITASK_LAST_RESCHEDULE: new Date(),
            });

        await this.logVariablesResheduling(
            [...relatedTasksByVar],
            newDate,
            userId,
        );

        const resheduledTasks = _.uniqBy(
            [...foundTasks, ...relatedTasksByVar],
            "ITASK_ID",
        );
        return await this.logTasksRescheduling(resheduledTasks, newDate);
    }

    async logVariablesResheduling(vars, newDate, userId = null) {
        const repo = globalThis.orm.repo(
            "instanceVariableHistory",
            this.connection,
        );
        let userName = "";
        if (userId) {
            const userRepo = globalThis.orm.repo("user", this.connection);
            const user = await userRepo.get(userId);
            userName = user.USER_DISPLAY_NAME;
        }

        for (const data of vars) {
            await repo.generateForTask(
                data,
                [data],
                `Variable ${data.IVAR_NAME} updated by rescheduling from ${data.IVAR_DATE_VALUE} to ${newDate}: by ${userName}`,
                userId,
                null,
                new Date(),
            );
        }
    }

    async logTasksRescheduling(tasks, newDate) {
        globalThis.tasLogger.info(
            `Rescheduling selected tasks due_date_start to ${newDate}`,
            {
                tasksIds: tasks.map((a) => a.ITASK_ID),
                newDate,
                tasksCount: tasks.length,
            },
        );

        for (const task of tasks) {
            globalThis.tasLogger.info(
                `Rescheduling task '${task.ITASK_NAME}' with id ${task.ITASK_ID} to ${newDate}`,
                {
                    itask_id: task.ITASK_ID,
                    iproc_id: task.IPROC_ID,
                    newDate,
                },
            );
        }
    }

    async getTasksByInstanceVariable(ivarIds, columns) {
        return await this.connection
            .select(columns)
            .from("INSTANCE_VARIABLES as IV")
            .leftJoin("INSTANCE_PROCESSES as IP", "IV.IPROC_ID", "IP.IPROC_ID")
            .leftJoin("INSTANCE_TASKS AS IT", function vars() {
                this.on("IT.IPROC_ID", "IV.IPROC_ID").andOn(
                    "IV.TVAR_ID",
                    globalThis.database.raw(
                        `${globalThis.orm.db.substr(`"ITASK_DUE_OFFSET"`, 3)}`,
                    ),
                );
            })
            .where("IT.ITASK_AUTO_START", TASK.AUTOSTART_YES)
            .where("IT.ITASK_STATUS", TASK.STATUS_NEW)
            .where((builder) => {
                if (_.isEmpty(ivarIds)) {
                    return builder.where(false);
                }
                _.chunk(ivarIds, 999).forEach((chunk) => {
                    builder.orWhereIn(
                        "IV.IVAR_ID",
                        Array.isArray(chunk) ? chunk : [chunk],
                    );
                });
            })
            .where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .where("ITASK_DUE_OFFSET", "like", "vc%");
    }

    getTaskSections(iTaskId) {
        return this.connection
            .select(["TS.*", "TTVU.TVAR_ID", "TTVU.TSEC_X", "TTVU.TSEC_Y"])
            .from("TEMPLATE_TASK_VAR_USAGE as TTVU")
            .leftJoin("TEMPLATE_SECTIONS as TS", "TS.TSEC_ID", "TTVU.TSEC_ID")
            .whereNotNull("TTVU.TSEC_ID")
            .where(
                "TTVU.TTASK_ID",
                "=",
                this.connection
                    .select("TTASK_ID")
                    .from("INSTANCE_TASKS")
                    .where("ITASK_ID", iTaskId),
            );
    }

    async getComputedValues(
        itaskId,
        userId,
        checkRoles = false,
    ): Promise<BaseCollection<Task>> {
        const orgStrRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );

        const columns = await this.getComputedColumns(userId, checkRoles, true);

        const conn = globalThis.orm
            .repo("task")
            .connection.withRecursive(
                "CHILDREN_UNITS",
                this.connection.raw(orgStrRepo.withChildrenUnitsForManager(), {
                    MANAGER_USER_ID: userId,
                }),
            )
            .with(
                "MANAGED_USERS",
                this.connection.raw(orgStrRepo.withManagedUsers(), {
                    USER_ID: userId,
                    USER_STATUS_ACTIVE,
                }),
            )
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(columns)
            .from("INSTANCE_TASKS AS IT")
            .leftJoin("INSTANCE_PROCESSES AS IP", "IT.IPROC_ID", "IP.IPROC_ID")
            .leftJoin("TEMPLATE_TASKS AS TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "IP.TPROC_ID")
            .leftJoin("HEADERS as TH", "TH.HEADER_ID", "IP.HEADER_ID")
            .where("IT.ITASK_ID", itaskId);

        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("header")
                .applyExternalLoginRights(conn, userId);
        }

        return this.createCollection(conn);
    }

    async getComputedColumns(userId, checkRoles, includeBasicAndSolve = true) {
        const user = !checkRoles
            ? await globalThis.container.service.temporary.cacheModule.getCachedUser(
                  userId,
              )
            : undefined;

        const inspector = user && user.isInspector() ? 1 : 0;
        const globalSupervisor = user && user.isGlobalSupervisor() ? 1 : 0;

        const assessmentManagerSql = `"IT"."ITASK_ASSESMENT_USER_ID" in (select "USER_ID" from "MANAGED_USERS")`;
        const procOwnerManagerSql = `"IP"."IPROC_INST_OWNER_USER_ID" in (select "USER_ID" from "MANAGED_USERS")`;
        const solverManagerSql = `"IT"."ITASK_USER_ID" in (select "USER_ID" from "MANAGED_USERS")`;
        const hasHrRightsSql = `
        -- Access by Template Process
        "TP"."TPROC_HR_ROLE_ID" IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
            OR
        -- Access by Instance Process
        "IP"."IPROC_HR_ROLE_ID" IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
            OR
        -- Access by Header
        "TH"."HEADER_HR_ROLE_ID" IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
        `;

        let columns = [];

        if (includeBasicAndSolve) {
            columns = columns.concat([
                "TT.TTASK_IS_REJECTABLE",
                "TT.TTASK_IS_DELEGATABLE",
                globalThis.database.raw(
                    `
                    CASE WHEN "IT"."ITASK_USER_ID" = ?
                    AND "IP"."IPROC_STATUS" = 'A'
                    AND "IT"."ITASK_TYPE" not in ('P','W')
                    AND "IT"."ITASK_STATUS" <> 'L'
                    THEN 1
                    ELSE 0 END "C_SOLVE" `,
                    userId,
                ),
                globalThis.database.raw(
                    `(select COUNT("TTJSCALC_EXEC_RECALC") FROM "TEMPLATE_TASK_JS_CALCULATIONS" WHERE "TTASK_ID" = "TT"."TTASK_ID" and "TTJSCALC_EXEC_RECALC" LIKE 'Y') as "EXEC_RECALC"`,
                ),
            ]);
        }

        columns = columns.concat([
            // CAN I TAKE ?
            globalThis.database.raw(
                `
                CASE
                when 1 = ${inspector} then 1
                when 1 = ${globalSupervisor} then 1
                when "IT"."ITASK_STATUS" = 'T' then 1
                WHEN (
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                            or (${assessmentManagerSql}) or
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerSql}))
                            or (${solverManagerSql})
                        )
                        OR
                        (${hasHrRightsSql})
                )
                AND "IP"."IPROC_STATUS" = 'A'
                AND "IT"."ITASK_TYPE" not in ('P','W')
                AND "IT"."ITASK_STATUS" <> 'L'
                AND "IT"."ITASK_TYPE" <> 'P'
                then 1 else 0 end "C_TAKE"`,
                [userId, userId],
            ),

            // CAN I HAND OVER ?
            globalThis.database.raw(
                `
                CASE
                when 1 = ${inspector} then 1
                when 1 = ${globalSupervisor} then 1
                WHEN (
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                            or (${assessmentManagerSql}) or
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerSql}))
                            or (${solverManagerSql})
                        )
                        OR
                        (${hasHrRightsSql})
                )
                AND "IP"."IPROC_STATUS" = 'A'
                AND "IT"."ITASK_TYPE" not in ('P','W')
                AND "IT"."ITASK_STATUS" <> 'L'
                AND "IT"."ITASK_TYPE" <> 'P'
                then 1 else 0 end "C_HANDOVER"`,
                [userId, userId],
            ),

            // CAN I HAND OVER WITHOUT LIMITATIONS?
            globalThis.database.raw(`
            CASE when
                (${hasHrRightsSql})
                or ${ROLE.SUPER_ADMINISTRATOR} IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
                or ${ROLE.ADMINISTRATOR} IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
                or ${ROLE.GLOBAL_SUPERVISOR} IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
            then 1 else 0 end "C_HANDOVER_NO_LIMITS"
            `),

            // CAN I ADD ?
            globalThis.database.raw(
                `
                CASE
                when ("IT"."ITASK_USER_ID" = ? or ("IP"."TPROC_ID" is null and ("IP"."IPROC_INST_OWNER_USER_ID" = ? or
                (${procOwnerManagerSql})
                )))
                and "IP"."IPROC_STATUS" = 'A'
                then 1 else 0 end "C_ADD"`,
                [userId, userId],
            ),

            // IS DUE ?
            globalThis.database.raw(
                `
                CASE
                WHEN (
                    "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                    or (${assessmentManagerSql}) or
                    ("IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerSql}))
                    or (${solverManagerSql})
                )
                AND ("IT"."ITASK_DUE_OFFSET" = 'po' OR "IT"."ITASK_DURATION" = 'po')
                AND "IP"."IPROC_STATUS" = 'A'
                then 1 else 0 end "C_DUE"`,
                [userId, userId],
            ),
        ]);

        return columns;
    }
}
