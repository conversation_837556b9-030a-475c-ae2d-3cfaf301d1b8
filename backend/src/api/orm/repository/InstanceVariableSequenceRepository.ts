// @ts-nocheck
// @ts-nocheck
import { InstanceVariableSequence } from "../entity/InstanceVariableSequence";
import { BaseRepository } from "./BaseRepository";

export class InstanceVariableSequenceRepository extends BaseRepository<InstanceVariableSequence> {
    meta() {
        return {
            tableName: "INSTANCE_VARIABLE_SEQUENCES",
            entityName: "InstanceVariableSequence",
            sequenceName: "IVARS_ID_SEQ",
            entity: () => new InstanceVariableSequence(),
        };
    }

    async createIfNotExists(entity) {
        // Find if exists.
        return await this.getByTvarId(entity.TVAR_ID).then((seq) => {
            if (!seq) {
                return this.store(entity);
            }
            return null;
        });
    }

    async getByTvarId(tvarId) {
        const coll = globalThis.orm.collection(
            "InstanceVariableSequence",
            this.connection.raw(
                `select * from "${this.tableName}" where "TVAR_ID" = :TVAR_ID FOR UPDATE`,
                { TVAR_ID: tvarId },
            ),
        );

        return await coll.collectAll().then((seqs) => {
            if (Array.isArray(seqs) && seqs.length > 0) {
                return seqs[0];
            }
            return null;
        });
    }

    async getSequenceNumber(tvarId) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `PROCESS:SEQUENCE:INCREMENT-${tvarId}`,
        );

        try {
            const seq = await this.getByTvarId(tvarId);
            const curr = seq.VALUE;
            seq.VALUE += 1;

            // Sequence increment must be stored immediately
            await this.store(seq);
            return curr;
        } finally {
            await lock.release();
        }
    }

    async updateSequenceForProcess(process) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `PROCESS:SEQUENCE:UPDATE-${process.TPROC_ID}`,
        );

        try {
            // Get all sequences in process.
            const seqs = await this.connection
                .select()
                .from(this.tableName)
                .where("TPROC_ID", process.TPROC_ID);
            if (Array.isArray(seqs) && seqs.length > 0) {
                for (const seq of seqs) {
                    // Get sequence number.
                    const currentNumber = await this.getSequenceNumber(
                        seq.TVAR_ID,
                    );

                    // Update process variable
                    globalThis.tasLogger.info(
                        `Updating sequence ${seq.TVAR_ID} in process '${process.IPROC_NAME}'`,
                        {
                            iproc_id: process.IPROC_ID,
                            seq,
                        },
                    );

                    await this.connection
                        .select()
                        .from("INSTANCE_VARIABLES")
                        .where("IPROC_ID", process.IPROC_ID)
                        .where("TVAR_ID", seq.TVAR_ID)
                        .update("IVAR_NUMBER_VALUE", currentNumber);
                }
            }
            return null;
        } finally {
            await lock.release();
        }
    }
}
