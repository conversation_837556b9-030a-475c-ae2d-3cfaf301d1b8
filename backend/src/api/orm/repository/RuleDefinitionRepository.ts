// @ts-nocheck
// @ts-nocheck
import _ from "lodash";

import { BaseRepository } from "./BaseRepository";
import { RuleDefinition } from "../entity/RuleDefinition";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { BaseCollection } from "../BaseCollection";

export class RuleDefinitionRepository extends BaseRepository<RuleDefinition> {
    meta() {
        return {
            tableName: "RULE_DEFINITION",
            entityName: "RuleDefinition",
            entity: () => new RuleDefinition(),
        };
    }

    getTRuleList(eveDefId): BaseCollection<RuleDefinition> {
        // Make transform from event name to event title via sql case
        const list = [
            {
                key: "Rest",
                title: "Rest Conector",
            },
        ];
        let mappEventNameToTitle = `case "RDEF"."RDEF_VALUE" `;
        Object.keys(list).forEach((ext) => {
            const title = list[ext];
            mappEventNameToTitle += ` when '${ext}' then '${title}' `;
        });
        mappEventNameToTitle += `else "RDEF"."RDEF_VALUE" end as "EXT_HANDLER_NAME"`;

        const conn = this.connection
            .select([
                "RDEF_ID",
                "RDEF_STATUS",
                "EVEDEF_ID",
                "EVEDEF_NAME",
                "EVEDEF_DESCRIPTION",
                "ORG_GLOBAL_ID",
                "ORG_NAME",
                "RDEF_TYPE",
                "RDEF_VALUE",
                "TPROC_NAME",
                "TTASK_NAME",
                "EXT_HANDLER_NAME",
            ])
            .from(function () {
                this.select([
                    "RDEF.RDEF_ID",
                    "RDEF.RDEF_STATUS",
                    "RDEF.EVEDEF_ID",
                    "EDEF.EVEDEF_NAME",
                    "EDEF.EVEDEF_DESCRIPTION",
                    "RDEF.ORG_GLOBAL_ID",
                    "ORG.ORG_NAME",
                    "RDEF.RDEF_TYPE",
                    "RDEF.RDEF_VALUE",
                    globalThis.database.raw(`null as "TTASK_NAME"`),
                    "TPROC.TPROC_NAME",
                    globalThis.database.raw(`null as "EXT_HANDLER_NAME"`),
                ])
                    .from("RULE_DEFINITION as RDEF")
                    .leftJoin(
                        "ORGANIZATION as ORG",
                        "RDEF.ORG_GLOBAL_ID",
                        "ORG.ORG_GLOBAL_ID",
                    )
                    .leftJoin(
                        "EVENT_DEFINITION as EDEF",
                        "RDEF.EVEDEF_ID",
                        "EDEF.EVEDEF_ID",
                    )
                    .leftJoin(
                        "TEMPLATE_PROCESSES as TPROC",
                        globalThis.database.raw(
                            globalThis.orm.db.toString(`"RDEF"."RDEF_VALUE"`),
                        ),
                        globalThis.database.raw(
                            globalThis.orm.db.toString(`"TPROC"."TPROC_ID"`) +
                                globalThis.orm.db.concat() +
                                globalThis.orm.db.toString(
                                    `"TPROC"."TPROC_VERSION"`,
                                ),
                        ),
                    )
                    .where("RDEF.RDEF_TYPE", "PROCESS")
                    .union(function () {
                        this.select([
                            "RDEF.RDEF_ID",
                            "RDEF.RDEF_STATUS",
                            "RDEF.EVEDEF_ID",
                            "EDEF.EVEDEF_NAME",
                            "EDEF.EVEDEF_DESCRIPTION",
                            "RDEF.ORG_GLOBAL_ID",
                            "ORG.ORG_NAME",
                            "RDEF.RDEF_TYPE",
                            "RDEF.RDEF_VALUE",
                            "TTASK.TTASK_NAME",
                            "TPROC.TPROC_NAME",
                            globalThis.database.raw(mappEventNameToTitle),
                        ])
                            .from("RULE_DEFINITION as RDEF")
                            .leftJoin(
                                "ORGANIZATION as ORG",
                                "RDEF.ORG_GLOBAL_ID",
                                "ORG.ORG_GLOBAL_ID",
                            )
                            .leftJoin(
                                "EVENT_DEFINITION as EDEF",
                                "RDEF.EVEDEF_ID",
                                "EDEF.EVEDEF_ID",
                            )
                            .leftJoin(
                                "TEMPLATE_TASKS as TTASK",
                                globalThis.database.raw(
                                    globalThis.orm.db.toString(
                                        globalThis.orm.db.substr(
                                            `"RDEF"."RDEF_VALUE"`,
                                            "$EVEW_".length + 1,
                                        ),
                                    ),
                                ),
                                globalThis.database.raw(
                                    globalThis.orm.db.toString(
                                        `"TTASK"."TTASK_ID"`,
                                    ),
                                ),
                            )
                            .leftJoin(
                                "TEMPLATE_PROCESSES as TPROC",
                                "TPROC.TPROC_ID",
                                "TTASK.TPROC_ID",
                            )
                            .whereIn("RDEF.RDEF_TYPE", [
                                "RETURN",
                                "EVENTW",
                                "EXT",
                            ])
                            .union(function () {
                                this.select([
                                    "RDEF.RDEF_ID",
                                    "RDEF.RDEF_STATUS",
                                    "RDEF.EVEDEF_ID",
                                    "EDEF.EVEDEF_NAME",
                                    "EDEF.EVEDEF_DESCRIPTION",
                                    "RDEF.ORG_GLOBAL_ID",
                                    "ORG.ORG_NAME",
                                    "RDEF.RDEF_TYPE",
                                    "RDEF.RDEF_VALUE",
                                    globalThis.database.raw(
                                        `null as "TTASK_NAME"`,
                                    ),
                                    "TPROC.TPROC_NAME",
                                    globalThis.database.raw(
                                        `"RDEF"."RDEF_VALUE" as "EXT_HANDLER_NAME"`,
                                    ),
                                ])
                                    .from("RULE_DEFINITION as RDEF")
                                    .leftJoin(
                                        "ORGANIZATION as ORG",
                                        "RDEF.ORG_GLOBAL_ID",
                                        "ORG.ORG_GLOBAL_ID",
                                    )
                                    .leftJoin(
                                        "EVENT_DEFINITION as EDEF",
                                        "RDEF.EVEDEF_ID",
                                        "EDEF.EVEDEF_ID",
                                    )
                                    .leftJoin(
                                        "TEMPLATE_PROCESSES as TPROC",
                                        globalThis.database.raw(
                                            globalThis.orm.db.toString(
                                                `"RDEF"."RDEF_VALUE"`,
                                            ),
                                        ),
                                        globalThis.database.raw(
                                            globalThis.orm.db.toString(
                                                `"TPROC"."TPROC_ID"`,
                                            ) +
                                                globalThis.orm.db.concat() +
                                                globalThis.orm.db.toString(
                                                    `"TPROC"."TPROC_VERSION"`,
                                                ),
                                        ),
                                    )
                                    .whereIn("RDEF.RDEF_TYPE", [
                                        "CSV_RUN_PROCESSES",
                                        "CSV_UPDATE_PROCESSES",
                                        "CSV_MERGE_PROCESSES",
                                        "CSV_UPDATE_PROCESS",
                                        "CSV_UPDATE_LIST",
                                        "CSV_EXPORT_PROCESSES",
                                        "UPDATE_LIST_OF_PROCESSES",
                                    ]);
                            });
                    })
                    .as("ignored_alias");
            })
            .where("EVEDEF_ID", eveDefId);

        return globalThis.orm.collection("RuleDefinition", conn);
    }

    /**
     *
     * @param {number} eveDef
     * @param {string} ruleStatus
     */
    getByEveDefName(eveDef, ruleStatus) {
        let conn = this.connection
            .select(
                "RDEF.RDEF_ID",
                "RDEF.EVEDEF_ID",
                "RDEF.ORG_GLOBAL_ID",
                "RDEF.RDEF_TYPE",
                "RDEF.RDEF_VALUE",
                "RDEF.ORG_ID",
                "RDEF.RDEF_STATUS",
                "EDEF.EVEDEF_NAME",
                "EDEF.EVEDEF_DESCRIPTION",
            )
            .from(`${this.tableName} as RDEF`)
            .leftJoin(
                "EVENT_DEFINITION as EDEF",
                "EDEF.EVEDEF_ID",
                "RDEF.EVEDEF_ID",
            )
            .where("EDEF.EVEDEF_NAME", eveDef);

        if (ruleStatus) {
            conn = conn.whereIn(
                "RDEF_STATUS",
                Array.isArray(ruleStatus) ? ruleStatus : [ruleStatus],
            );
        }
        return conn;
    }

    getForEventDefinition(evedefId) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .whereIn(
                "EVEDEF_ID",
                Array.isArray(evedefId) ? evedefId : [evedefId],
            );
        return globalThis.orm.collection(this, conn);
    }

    getForEvent(eveId) {
        const conn = this.connection
            .select(
                "RDEF.RDEF_ID",
                "RDEF.EVEDEF_ID",
                "RDEF.ORG_GLOBAL_ID",
                "RDEF.RDEF_TYPE",
                "RDEF.RDEF_VALUE",
                "RDEF.ORG_ID",
                "RDEF.RDEF_STATUS",
                "RUL.RULE_ID",
                "RUL.EVE_ID",
                "RUL.RULE_STATUS",
            )
            .from("RULE as RUL")
            .leftJoin("RULE_DEFINITION as RDEF", "RDEF.RDEF_ID", "RUL.RDEF_ID")
            .where("RUL.EVE_ID", eveId);

        return globalThis.orm.collection<"ruleDefinition">(this, conn);
    }

    async create(
        evedefId: number,
        orgGlobalId: number | undefined,
        rdefType: string,
        rdefValue: string,
        rdefStatus: string,
    ): Promise<number> {
        const entity = super.getEntity();

        entity.EVEDEF_ID = evedefId;
        entity.ORG_GLOBAL_ID = orgGlobalId;
        entity.RDEF_TYPE = rdefType;
        entity.RDEF_VALUE = rdefValue;
        entity.RDEF_STATUS = rdefStatus;

        // Validate rule type
        const validTypes = entity.getTypes();
        if (!validTypes.includes(entity.RDEF_TYPE)) {
            throw new UserException("Illegal rule type", "ILLEGAL_RULE_TYPE");
        }

        // Assign default orgGlobalId if missing
        if (
            entity.RDEF_TYPE !== -1 &&
            typeof entity.ORG_GLOBAL_ID === "undefined"
        ) {
            const orgRepo = globalThis.orm.repo(
                "Organization",
                this.connection,
            );
            const org = await orgRepo.get(1);
            entity.ORG_GLOBAL_ID = org.ORG_GLOBAL_ID;
        }

        return await super.store(entity);
    }

    async fillVariables(rules) {
        if (!Array.isArray(rules) && rules.EVEDEF_ID) {
            rules = [rules];
        }

        if (!rules) {
            return rules;
        }

        const ruleRepo = globalThis.orm.repo(
            "ruleDefinitionVariable",
            this.connection,
        );
        const out = [];

        for (const rule of rules) {
            const vars = await ruleRepo
                .getForRuleDefinition(rule.RDEF_ID)
                .collectAll();
            rule.rule_variables = _.map(vars, "_raw");
            out.push(rule);
        }

        return out;
    }

    async fillParams(rules) {
        if (!Array.isArray(rules) && rules.EVEDEF_ID) {
            rules = [rules];
        }

        if (!rules) {
            return rules;
        }

        const ruleRepo = globalThis.orm.repo(
            "RuleDefinitionParam",
            this.connection,
        );
        const out = [];

        for (const rule of rules) {
            const params = await ruleRepo
                .getForRuleDefinition(rule.RDEF_ID)
                .collectAll();
            rule.rule_params = _.map(params, "_raw");
            out.push(rule);
        }
    }

    async deleteForEvendDefinition(evedefId) {
        const rules = await this.connection
            .select()
            .from(this.tableName)
            .where("EVEDEF_ID", evedefId);

        // Delete all rules
        if (Array.isArray(rules) && rules.length > 0) {
            const ruleRepo = globalThis.orm.repo("rule", this.connection);
            for (const rule of rules) {
                await ruleRepo.deleteTRule(rule.RDEF_ID);
            }
        }
    }
}
