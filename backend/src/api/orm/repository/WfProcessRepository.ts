// @ts-nocheck
// @ts-nocheck
import * as TASK from "../entity/const/taskConst";

import { ProcessRepository } from "./ProcessRepository";

export class WfProcessRepository extends ProcessRepository {
    async checkActiveTasks(process, version) {
        const coll = globalThis.orm
            .repo("wfTask", this.connection)
            .createCollection();
        if (version > 0) {
            coll.knex
                .select(1)
                .from("INSTANCE_TASKS AS IT_SRC")
                .leftJoin(
                    "TEMPLATE_TASK_LINKS AS TTL",
                    "IT_SRC.TTASK_ID",
                    "TTL.TTASKLINK_FROM_TTASK_ID",
                )
                .leftJoin("INSTANCE_TASKS AS IT_TGT", (builder) => {
                    builder
                        .on("TTL.TTASKLINK_TO_TTASK_ID", "IT_TGT.TTASK_ID") // Find right version
                        .andOn(
                            globalThis.database.raw(`"IT_TGT"."IPROC_ID" = ?`, [
                                process.IPROC_ID,
                            ]),
                        ); // Fallback to version = 1
                })
                .where(function () {
                    this.where(function () {
                        this.where(
                            "IT_SRC.ITASK_STATUS",
                            TASK.STATUS_ACTIVE,
                        ).whereNull("TTL.TTASKLINK_ID");
                    })
                        .orWhere(function () {
                            this.where(
                                "IT_SRC.ITASK_STATUS",
                                TASK.STATUS_ACTIVE,
                            ).andWhere("IT_TGT.ITASK_STATUS", TASK.STATUS_NEW);
                        })
                        .orWhere(function () {
                            this.where(
                                "IT_SRC.ITASK_STATUS",
                                TASK.STATUS_NEW,
                            ).andWhere(
                                "IT_SRC.ITASK_AUTO_START",
                                TASK.AUTOSTART_YES,
                            );
                        })
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_WAITING)
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_ACTIVE)
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_PULL)
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_DELAYED);
                })
                .andWhere("IT_SRC.IPROC_ID", process.IPROC_ID);
        } else {
            coll.knex
                .select(1)
                .from("INSTANCE_TASKS AS IT_SRC")
                .leftJoin(
                    "INSTANCE_TASK_LINKS AS ITL",
                    "IT_SRC.ITASK_ID",
                    "ITL.ITASKLINK_FROM_TTASK_ID",
                )
                .leftJoin(
                    "INSTANCE_TASKS AS IT_TGT",
                    "ITL.ITASKLINK_TO_TTASK_ID",
                    "IT_TGT.ITASK_ID",
                )
                .where(function () {
                    this.where(function () {
                        this.where(
                            "IT_SRC.ITASK_STATUS",
                            TASK.STATUS_ACTIVE,
                        ).whereNull("ITL.ITASKLINK_ID");
                    })
                        .orWhere(function () {
                            this.where(
                                "IT_SRC.ITASK_STATUS",
                                TASK.STATUS_ACTIVE,
                            ).andWhere("IT_TGT.ITASK_STATUS", TASK.STATUS_NEW);
                        })
                        .orWhere(function () {
                            this.where(
                                "IT_SRC.ITASK_STATUS",
                                TASK.STATUS_NEW,
                            ).andWhere(
                                "IT_SRC.ITASK_AUTO_START",
                                TASK.AUTOSTART_YES,
                            );
                        })
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_WAITING)
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_ACTIVE)
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_PULL)
                        .orWhere("IT_SRC.ITASK_STATUS", TASK.STATUS_DELAYED);
                })
                .andWhere("IT_SRC.IPROC_ID", process.IPROC_ID);
        }

        return coll;
    }

    endTasks(processId) {
        const coll = globalThis.orm
            .repo("WfTask", this.connection)
            .createCollection();
        coll.update({
            ITASK_STATUS: TASK.STATUS_NEW,
            ITASK_AUTO_START: TASK.AUTOSTART_NO,
        })
            .where("IPROC_ID", processId)
            .whereIn("ITASK_STATUS", [TASK.STATUS_ACTIVE, TASK.STATUS_PULL]);
    }
}
