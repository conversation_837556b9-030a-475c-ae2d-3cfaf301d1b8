import { BaseRepository } from "./BaseRepository";
import * as configConst from "../entity/const/configConst";
import { Config } from "../entity/Config";
import { BaseCollection } from "../BaseCollection";

interface ConfigData {
    id?: string;
    var_value?: number | string | boolean | null;
    var_use_value?: string;
}

export class ConfigRepository extends BaseRepository<Config> {
    meta() {
        return {
            tableName: "CONFIG",
            entityName: "Config",
            entity: () => new Config(),
        };
    }

    getAllVars(): BaseCollection<Config> {
        const coll = this.getCollection();
        coll.knex.orderBy("VAR_NAME", "ASC");
        return coll;
    }

    /**
     *
     * @param {object} data config values
     * @param updateDb update db or cache only
     * @returns {*}
     */
    async saveConfig(
        data: ConfigData | ConfigData[],
        updateDb: boolean = true,
    ): Promise<void> {
        const dataArr = Array.isArray(data) ? data : [data];

        // Update variables
        for (const item of dataArr) {
            if (item.id === "dms.security.allowedExternalSources") {
                return; // Skip due to t3f-1454 allowedExternalSources vytáhnout do dyn configu v GUI jako read only informaci
            }

            // Update value
            // @ts-expect-error clasic
            const varEntity = await this.get(item.id);
            // change only affected vars -> changed if changed value OR changed VAR_USE_VALUE

            if (varEntity.VAR_TYPE === configConst.TYPE_JSON_SIMPLE) {
                item.var_value =
                    item.var_value !== null
                        ? // @ts-expect-error classic
                          JSON.parse(item.var_value)
                        : item.var_value; // do not parse null values
            }

            const isChanged =
                (varEntity.value !== item.var_value &&
                    item.var_use_value === "Y") ||
                varEntity.VAR_USE_VALUE !== item.var_use_value;
            varEntity.VAR_USE_VALUE = item.var_use_value;
            varEntity.value = item.var_value;
            if (isChanged) {
                if (updateDb) {
                    await this.store(varEntity);
                }
                globalThis.dynamicConfig.set(
                    varEntity.VAR_NAME,
                    varEntity.insecuredValue(),
                );

                globalThis.tasLogger.info(
                    `Setting configuration ${varEntity.VAR_NAME} to value: ${varEntity.value}`,
                    {
                        configPath: varEntity.VAR_NAME,
                        value: varEntity.value,
                    },
                );
            }
        }
    }
}
