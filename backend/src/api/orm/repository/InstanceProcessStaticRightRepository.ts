// @ts-nocheck
// @ts-nocheck
import { InstanceProcessStaticRight } from "../entity/InstanceProcessStaticRight";
import { BaseRepository } from "./BaseRepository";

export class InstanceProcessStaticRightRepository extends BaseRepository<InstanceProcessStaticRight> {
    meta() {
        return {
            tableName: "INSTANCE_PROCESS_STATIC_RIGHTS",
            entityName: "InstanceProcessStaticRight",
            entity: () => new InstanceProcessStaticRight(),
            archived: true,
        };
    }

    store(entity) {
        if (!entity.USER_ID || !entity.IPROC_ID) {
            return;
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where("USER_ID", entity.USER_ID)
            .where("IPROC_ID", entity.IPROC_ID)
            .then((rights) => {
                if (!Array.isArray(rights) || rights.length == 0) {
                    return super.store(entity);
                }
                return entity;
            });
    }
}
