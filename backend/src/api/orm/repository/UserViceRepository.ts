// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as ROLES from "../entity/const/roleConst";
import { UserParameter } from "../entity/UserVice";

export class UserViceRepository extends BaseRepository<UserParameter> {
    meta() {
        return {
            tableName: "USER_VICE",
            entityName: "UserVice",
            entity: () => new UserParameter(),
        };
    }

    getEnabledUsersVices(userId) {
        const date = new Date();

        const conn = this.connection
            .select([
                "U.USER_NAME",
                "U.USER_LAST_NAME",
                "U.USER_FIRST_NAME",
                "U.USER_ID",
                "UV.UV_TO",
                "UV.VIEW_ONLY",
                "UV.UV_FROM",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
                "UV.UV_ID",
            ])
            .from(`${this.tableName} as UV`)
            .leftJoin("USERS as U", "UV.USER_ID", "U.USER_ID")
            .where("UV.UV_FROM", "<=", date)
            .where("UV.UV_TO", ">=", date)
            .where("UV.USER_ID_VICE", userId);

        return globalThis.orm.collection("UserVice", conn);
    }

    canViceUser(userId, viceUserId) {
        return this.getEnabledUsersVices(viceUserId)
            .fetchAll()
            .then((vices) => {
                if (!Array.isArray(vices) || !vices.length) {
                    return false;
                }

                let viceFound = false;
                vices.forEach((vice) => {
                    if (vice.USER_ID == userId) {
                        viceFound = true;
                    }
                });

                return viceFound;
            });
    }

    getUsersViced(userIds, enabled) {
        const cols = [
            "U.USER_NAME",
            "U.USER_LAST_NAME",
            "U.USER_FIRST_NAME",
            "U.USER_ID",
            "UV.USER_ID_VICE",
            "UV.VIEW_ONLY",
            "UV.UV_TO",
            "UV.UV_FROM",
            "UV.UV_ID",
            "VICE.USER_LAST_NAME as USER_VICE_LAST_NAME",
            "VICE.USER_FIRST_NAME as USER_VICE_FIRST_NAME",
            "VICE.USER_EMAIL as USER_VICE_EMAIL",
            "VICE.USER_ID as USER_VICE_ID",
            globalThis.database.raw(
                `"VICE"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "VICE"."USER_FIRST_NAME" as "USER_VICE_FULL_NAME"`,
            ),
            "VICE.USER_DISPLAY_NAME as USER_VICE_DISPLAY_NAME",
            globalThis.database.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ];

        const conn = this.connection
            .select(cols)
            .from(`${this.tableName} as UV`)
            .leftJoin("USERS as U", "UV.USER_ID", "U.USER_ID")
            .leftJoin("USERS as VICE", "UV.USER_ID_VICE", "VICE.USER_ID");

        if (enabled) {
            const date = new Date();
            conn.where("UV.UV_FROM", "<=", date).where("UV.UV_TO", ">=", date);
        }

        if (userIds) {
            conn.whereIn(
                "UV.USER_ID",
                Array.isArray(userIds) ? userIds : [userIds],
            );
        }

        return this.createCollection(conn);
    }

    async getUsersToVice(userId) {
        const userRepo = globalThis.orm.repo("user", this.connection);

        const isHrManager = await userRepo.hasRole(userId, ROLES.HR_MANAGER);
        const isAdministrator = await userRepo.hasRole(
            userId,
            ROLES.ADMINISTRATOR,
        );
        if (isHrManager || isAdministrator) {
            return userRepo.getByStatus("A");
        }

        // Find user orgstr
        const orgstrs = await userRepo.getOrganization(userId);
        if (orgstrs.length === 0) {
            return globalThis.orm.collection(
                "User",
                this.connection
                    .select()
                    .from("USERS")
                    .whereNull("USER_ID") /* get empty collection */,
            );
        }
        const userOrgstrId = orgstrs[0];

        // Get all restrictions - but use only first.
        const restrictions = await globalThis.orm
            .repo("UserViceOrgRestriction", this.connection)
            .getRestrictions(userOrgstrId);

        const restriction = restrictions.length > 0 ? restrictions[0] : null;

        // Vicing list is not restricted
        if (
            !restriction ||
            restriction.ASSESMENT_HIERARCHY === "UNRESTRICTED"
        ) {
            return userRepo.getByStatus("A");
        }

        // Get restricted users
        const result = await globalThis.orm
            .repo("user", this.connection)
            .getUsersWithOrgStrConnection(
                userId,
                userOrgstrId,
                {
                    ITASK_ASSESMENT_HIERARCHY: restriction.ASSESMENT_HIERARCHY, // Style... C, D, P, A, S, G, L
                    ITASK_ASSESMENT_ROLE_ID: restriction.ASSESMENT_ROLE_ID,
                    ITASK_ASSESMENT_ORGSTR_CNST:
                        restriction.ASSESMENT_ORGSTR_CNST,
                    IPROC_ID: null,
                    ITASK_TYPE: null,
                },
                false,
            );

        const collection = globalThis.orm.collection("User", result.connection);

        // Different ordering & filtering columns
        collection.orderingColumns = null;
        collection.filteringColumns = {
            USER_FULL_NAME: {
                type: "string",
                key: `U.USER_LAST_NAME ${globalThis.orm.db.concatColumns()} U.USER_FIRST_NAME`,
            },
            USER_VICE_FULL_NAME: {
                type: "string",
                key: `VICE.USER_LAST_NAME ${globalThis.orm.db.concatColumns()} VICE.USER_FIRST_NAME`,
            },
            USER_VICE_DISPLAY_NAME: {
                type: "string",
                key: "VICE.USER_DISPLAY_NAME",
            },
        };

        return collection;
    }
}
