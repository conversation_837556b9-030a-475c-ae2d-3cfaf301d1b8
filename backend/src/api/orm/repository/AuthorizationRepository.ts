// @ts-nocheck
import { Authorization } from "../entity/Authorization";
import { BaseRepository } from "./BaseRepository";

export class AuthorizationRepository extends BaseRepository<Authorization> {
    meta() {
        return {
            tableName: "AUTHORIZATION",
            entityName: "Authorization",
            entity: () => new Authorization(),
        };
    }

    getCollection(columns?, alias = null) {
        const coll = super.getCollection(columns, alias);
        coll.knex.orderBy("AUTH_ORDER", "asc");
        return coll;
    }

    getByName(name) {
        const coll = this.getCollection();
        coll.knex.where("AUTH_NAME", name);
        return coll.collectOne();
    }

    getByModule(module) {
        const coll = this.getCollection();
        coll.knex.where("AUTH_MODULE", module);
        return coll.collectOne();
    }

    /**
     * Get authorization list with params (including ad password etc.).
     * @returns {BaseCollection}
     */
    getAuthorizationConfig() {
        return this.getCollection();
    }

    getFirstActive(moduleName) {
        const coll = this.getCollection();
        coll.knex.whereIn(
            "AUTH_MODULE",
            Array.isArray(moduleName) ? moduleName : [moduleName],
        );
        return coll.collectOne();
    }

    /**
     * Get authorization list with no params. Only enabled authorizations returned.
     */
    getAuthorizationList() {
        const coll = this.getCollection([
            "AUTH_ENABLED",
            "AUTH_NAME",
            "AUTH_MODULE",
            "AUTH_ID",
            "AUTH_IS_FALLBACK",
            "AUTH_META",
        ]);
        coll.knex.where("AUTH_ENABLED", "Y");
        return coll;
    }

    /**
     *
     * @param onlyFallback Only fallback=Y
     * @returns {BaseCollection}
     */
    getEnabledAuthorizationConfig(onlyFallback?) {
        const coll = this.getCollection();
        coll.knex.where("AUTH_ENABLED", "Y");
        if (onlyFallback) {
            coll.knex.where("AUTH_IS_FALLBACK", "Y");
        }
        return coll;
    }

    async migrateOrganizationAuth() {
        const rows = await this.getCollection().collectAll();
        if (rows.length > 0) {
            globalThis.tasLogger.info(
                "Authorization already migrated. Skipping migration.",
            );
            return true;
        }

        const oauthRepo = globalThis.orm.repo(
            "OrganizationAuth",
            this.connection,
        );

        let authConfig = [];
        const oauths = await oauthRepo.getAuthMethods(); // old ORGANIZATION_AUTH
        for (let i = 0; i < oauths.length; i += 1) {
            const auths = this.getCompatibleAuth(oauths[i]);
            authConfig = authConfig.concat(auths);
        }

        let i = 0;
        const used = [];
        const results = [];

        for (const auth of authConfig) {
            i += 1;
            if (used.indexOf(auth.AUTH_NAME) !== -1) {
                continue;
            }
            auth.AUTH_ORDER = i;
            used.push(auth.AUTH_NAME);

            const result = await this.store(auth);
            results.push(result);
        }
        return results;
    }

    /**
     * Get compatible Authorization from ORGANIZATION_AUTH
     * @param oauth
     * @returns {Promise<[]>}
     */
    getCompatibleAuth(oauth) {
        const out = [];

        if (oauth.OA_LOGIN_SRC !== "Y") {
            return out;
        }

        // Not implemented.
        if (["Google", "ALUCID"].indexOf(oauth.OA_MODULE) !== -1) {
            const msg = `Authorization ${oauth.OA_MODULE} is not supported. Not migrated but used in ORGANIZATION_AUTH.`;
            globalThis.tasLogger.error(msg, {
                oauth,
            });
            return out;
        }

        const name = `Migrated ${oauth.OA_NAME}`;
        const params = JSON.parse(oauth.OA_PARAMETERS);

        // Default authentication -> migrate to PasswordAuthenticator
        if (oauth.OA_MODULE === "Default") {
            // do nothing, password authenticator add always.
        }

        // MSAD -> migrate to LDAPAuthenticator and PasswordAuthenticator
        if (["MSAD"].indexOf(oauth.OA_MODULE) !== -1) {
            const config = {
                ldap: params,
                config: {
                    createUser: false,
                    updateOnLogin: false,
                },
            };
            out.push(
                this.getEntity({
                    AUTH_MODULE: "LDAP",
                    AUTH_NAME: name,
                    AUTH_PARAMS: JSON.stringify(config),
                    AUTH_ENABLED: "Y",
                    AUTH_IS_FALLBACK: "Y",
                }),
            );
        }

        // MSAD -> migrate to multi LDAPAthenticator and PasswordAuthenticator
        if (["MultiMSAD"].indexOf(oauth.OA_MODULE) !== -1) {
            const adParams = params;
            const { length } = adParams.server.split("|");

            // Get ad params.
            const keys = Object.keys(adParams);
            const singleADParams = {};
            for (let i = 0; i < length; i += 1) {
                keys.forEach((key) => {
                    singleADParams[key] = adParams[key].split("|")[i];
                });

                const config = {
                    ldap: singleADParams,
                    config: {
                        createUser: false,
                        updateOnLogin: false,
                    },
                };

                out.push(
                    this.getEntity({
                        AUTH_NAME: `${name} (${i})`,
                        AUTH_PARAMS: JSON.stringify(config),
                        AUTH_MODULE: "LDAP",
                        AUTH_ENABLED: "Y",
                        AUTH_IS_FALLBACK: "Y",
                    }),
                );
            }
        }

        if (oauth.OA_MODULE === "IIS_SSO") {
            const oaParams = JSON.parse(oauth.OA_PARAMETERS);
            const newParams = { url: oaParams.redirectUrl };

            out.push(
                this.getEntity({
                    AUTH_NAME: name,
                    AUTH_PARAMS: JSON.stringify(newParams),
                    AUTH_MODULE: "Foreign",
                    AUTH_ENABLED: "Y",
                    AUTH_IS_FALLBACK: "Y",
                }),
            );
        }

        // Every oauth has password fallback
        out.push(
            this.getEntity({
                AUTH_NAME: "Migrated Password",
                AUTH_PARAMS: "{}",
                AUTH_MODULE: "Password",
                AUTH_ENABLED: "Y",
                AUTH_IS_FALLBACK: "Y",
            }),
        );

        return out;
    }

    nextOrderIndex() {
        return this.connection
            .max("AUTH_ORDER as MAX_ORDER")
            .from(this.tableName)
            .then((rows) => rows[0].MAX_ORDER + 1);
    }
}
