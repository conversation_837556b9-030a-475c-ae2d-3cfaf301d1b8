import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import * as DMS_TAGS from "../entity/const/dmsTagConsts";
import * as ROLES from "../entity/const/roleConst";
import { UtilsService } from "../../services/UtilsService";
import * as DmsFileConstants from "../entity/const/dmsFileConsts";
import { DmsFile } from "../entity/DmsFile";
import { BaseCollection } from "../BaseCollection";
import { DmsException } from "../../../utils/errorHandling/exceptions/dmsException";
import { MetadataTemplate } from "../../../client/types/TikaTypes";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import path from "path";
import { LogCategory } from "../../../utils/logger/logConsts";

export class ArchivedDmsFileRepository extends BaseRepository<DmsFile> {
    meta() {
        return {
            tableName: "ARCH_DMS_FILE",
            entityName: "archivedDmsFile",
            entity: () => new DmsFile(),
        };
    }

    async getUserFiles(authUser: any, tagsToFilter: any) {
        // Prepare columns and tags
        const dmsTagRepo = globalThis.orm.repo("dmsTag");

        try {
            const cols = await dmsTagRepo.getColumns(["DMST_ID"]).fetchAll();
            // @ts-ignore
            const tags = cols;
            const collection = await this.getForUserOptimise(
                authUser,
                tags,
                tagsToFilter,
            );
            return this.prepareTagsFilter(collection, tags);
        } catch (error) {
            throw error;
        }
    }

    prepareTagsFilter(collection: any, tagsToFilter: any) {
        // Allow filtering by tag columns.
        collection.filteringColumns = {};
        if (
            tagsToFilter &&
            Array.isArray(tagsToFilter) &&
            tagsToFilter.length > 0
        ) {
            tagsToFilter.forEach((tag) => {
                const key = `DTF${tag.DMST_ID}`;
                collection.filteringColumns[key] = {
                    type: "string",
                    key: `"${key}".DMST_VALUE`,
                };

                if (tag.DMST_ID == -4) {
                    collection.filteringColumns.FOLDER_NAME = {
                        type: "string",
                        key: "DF4.FOLDER_NAME",
                    };
                }
            });
        }

        if (!collection.filteringColumns) {
            collection.filteringColumns = {};
        }
        collection.filteringColumns.DMSF_OWNER = {
            type: "string",
            key: `U.USER_DISPLAY_NAME`,
        };
        collection.filteringColumns.DMSF_ID = {
            type: "string",
            key: "DF.DMSF_ID",
        };
        collection.filteringColumns.id = {
            type: "string",
            key: "DF.DMSF_ID",
        };
        collection.filteringColumns.NAME = {
            type: "string",
            key: "DF.NAME",
        };

        collection.orderingColumns = _.cloneDeep(collection.filteringColumns);
        collection.orderingColumns.DMSF_OWNER = {
            type: "string",
            key: "DMSF_OWNER",
        };

        return collection;
    }

    async getLastRevision(file: any, user: any, lastRevisionId: number) {
        if (file.IPROC_ID === null) {
            // Not process file
            const collForUser = this.getCollection();
            // Known lastRevisionId. Revision may have a different name than the original file
            if (lastRevisionId !== null) {
                collForUser.knex.where("DMSF_ID", lastRevisionId);
            } else {
                collForUser.knex
                    .where({
                        USER_ID_INSERT: user.USER_ID,
                        NAME: file.NAME,
                        IS_CURRENT: "Y",
                        IPROC_ID: null,
                    })
                    .orderBy("REVISION", "DESC");
            }
            return collForUser.collectOne();
        }

        const colForProcess = await this.getForProcess(file.IPROC_ID);

        if (lastRevisionId !== null) {
            colForProcess.knex.where("DF.DMSF_ID", lastRevisionId);
        } else {
            colForProcess.knex
                .where("NAME", file.NAME)
                .andWhere("IS_CURRENT", "Y")
                .orderBy("REVISION", "DESC");
        }
        return colForProcess.collectOne();
    }

    getByToIndex(status: string | string[], onlyCurrent = true) {
        const collection = this.getCollection();

        if (Array.isArray(status)) {
            collection.knex.from(this.tableName).whereIn("TO_INDEX", status);
        } else {
            collection.knex.from(this.tableName).where("TO_INDEX", status);
        }

        if (onlyCurrent) {
            collection.knex = collection.knex.where("IS_CURRENT", "Y");
        }

        return collection;
    }

    async parseMetadata(file: any, metadataOnly: boolean) {
        try {
            return globalThis.tasLogger.runTask(async () => {
                globalThis.tasLogger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_DMS_FILE_INDEXING,
                );
                let response: MetadataTemplate;
                if (metadataOnly) {
                    response =
                        await globalThis.container.service.file.fileMetadataServiceModule.parseMetadataOnly(
                            file.getAbsoluteFilePath(
                                globalThis.dynamicConfig.dms.storagePath,
                            ),
                        );
                } else {
                    response =
                        await globalThis.container.service.file.fileMetadataServiceModule.parse(
                            file.getAbsoluteFilePath(
                                globalThis.dynamicConfig.dms.storagePath,
                            ),
                            file.NAME,
                        );
                }

                if (response && response.contentType) {
                    file.FILE_TYPE = response.contentType;
                }

                return response;
            });
        } catch (error: any) {
            // Tika service is not running (accepted situation)
            if (error.code == "ECONNREFUSED") {
                throw new DmsException(
                    "Tika service is not running!",
                    "TIKA_NOT_RUNNING",
                );
            } else {
                // or parsing failed
                throw new DmsException(
                    `Tika parsing failed! ${error.message}`,
                    "TIKA_PARSING_FAILED",
                );
            }
        }
    }

    async getFileTypeFromExtension(ext: any) {
        const first = await this.connection
            .select("FILE_TYPE")
            .from("DMS_FILE_TYPE")
            .where("EXTENSIONS", "like", `%${ext}%`)
            .first();

        return first ? first.FILE_TYPE : "application/octet-stream";
    }

    async hasFileRights(dmsfId: number, user: any, throwError = false) {
        const externalRightsRepo = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );
        let file;
        const { USER_ID: userId } = user;

        try {
            file = await this.get(dmsfId); // returns only basic columns
        } catch (_e) {
            throw new UserException("File doesn't exist.", "FILE_NOT_FOUND");
        }

        // file is private, not bound with process
        if (file.IPROC_ID === null) {
            if (userId === file.USER_ID_INSERT) {
                return true;
            }
            if (throwError) {
                throw new UserException(
                    "Lack of permissions.",
                    "LACK_OF_PERMISSIONS",
                );
            }
            return false;
        }

        if (userId === file.USER_ID_INSERT) {
            return true;
        }

        const hasElevatedRole = user.hasElevatedRole();

        // Check direct access
        // @ts-ignore
        const hasDirectAccess = await externalRightsRepo.hasProcessRights(
            userId,
            file.IPROC_ID,
            hasElevatedRole,
            true,
        );

        if (hasDirectAccess) {
            return true;
        }

        if (throwError) {
            throw new UserException(
                "Lack of permissions!",
                "LACK_OF_PERMISSIONS",
            );
        }

        return false;
    }

    async connectByPrior(
        tableName: any,
        idColName: any,
        parentColName: any,
        id: any,
    ) {
        const ids: any = [];

        const getParent = async (pid: any) => {
            try {
                const result = await globalThis.database
                    .select(parentColName)
                    .from(tableName)
                    .where(idColName, pid);

                if (result && Array.isArray(result) && result.length === 1) {
                    return result[0][parentColName];
                }
                return null;
            } catch (err: any) {
                globalThis.tasLogger.error(err);
            }
        };

        const recursive = async (startId: any) => {
            if (startId == null) {
                return ids;
            }

            ids.push(startId);
            const pid = await getParent(startId);
            return recursive(pid);
        };

        return await recursive(id);
    }

    async getListOfRevisions(
        dmsfId: number,
        tagsToSelect?: any,
        tagsToFilter?: any,
    ) {
        const ids: any = await this.connectByPrior(
            "ARCH_DMS_FILE",
            "DMSF_ID",
            "DMSF_ID_PREV",
            dmsfId,
        );

        return this.getAll(tagsToSelect, tagsToFilter, ids);
    }

    async collectFile(dmsfId: number, tagsToSelect?: any) {
        const coll = await this.getAll(tagsToSelect, null, dmsfId);
        return coll.collectOne();
    }

    async getAll(
        tagsToSelect: any,
        tagsToFilter: any,
        dmsfId?: number | null,
        userId?: number,
    ) {
        const extraColumns: any[] = [];

        // Add translations
        const iProcAttrs = globalThis.orm
            .repo("archivedProcess")
            .getEntity()
            .attributes();
        const tProcAttrs = globalThis.orm
            .repo("templateProcess")
            .getEntity()
            .attributes();

        Object.keys(iProcAttrs).forEach((attrName) => {
            if (iProcAttrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
            }
        });

        Object.keys(tProcAttrs).forEach((attrName) => {
            if (tProcAttrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        const columns = [
            "DF.DMSF_ID",
            "DF.NAME",
            "DF.REVISION",
            "DT.DMST_VALUE as V-4",
            "DF.USER_ID_INSERT",
            "DF.USER_ID_INSERT_VICE",
            "DF.DMSF_SRC",
            "DF.IS_CURRENT",
            "DF.IS_DELETED",
            "DF.FILE_TYPE",
            "DF.VIEW_IN_MAIN",
            "DF.USER_ID_DELETED",
            "DF.VIEW_IN_SUB",
            "DF.DATETIME_DELETED",
            "DF.DATETIME_INSERT",
            "DF.TO_INDEX",
            "DF.IS_FULL_INDEXED",
            "DF.IS_ENCRYPTED",
            "DF.DATETIME_INDEXED",
            "DF.DMSF_ID_PREV",
            "IP.IPROC_ID",
            "IP.IPROC_NAME",
            "TP.TPROC_NAME",
            globalThis.database.raw(`"U"."USER_DISPLAY_NAME" as "DMSF_OWNER"`),
            globalThis.database.raw(
                `"VICE"."USER_DISPLAY_NAME" as "DMSF_OWNER_VICE"`,
            ),
        ].concat(extraColumns);

        if (Array.isArray(tagsToSelect)) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;
                const colName = `${alias}."DMST_VALUE" as ${alias}`;
                columns.push(globalThis.database.raw(colName));

                // rename folder tag
                if (tag.DMST_ID === DMS_TAGS.TYPE_FOLDER) {
                    columns.push(globalThis.database.raw(`"F4"."FOLDER_NAME"`));
                }
            });
        }

        let knex = this.connection;
        if (userId) {
            // @ts-ignore
            knex = knex.with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            });
        }

        // @ts-ignore
        knex = knex
            .select(columns)
            // .distinct() // This causes massive processing bottleneck, is it really needed? Find a different solution.
            .from(`${this.tableName} as DF`)
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES as IP",
                "DF.IPROC_ID",
                "IP.IPROC_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .joinRaw(
                "left join ARCH_DMS_FILE_TAG DT on (DT.DMSF_ID = DF.DMSF_ID and DT.DMST_ID = -4)",
            )
            .leftJoin("USERS as U", "U.USER_ID", "DF.USER_ID_INSERT")
            .leftJoin(
                "USERS as VICE",
                "VICE.USER_ID",
                "DF.USER_ID_INSERT_VICE",
            );

        // if array or DMSF_ID is specified
        if (dmsfId) {
            // @ts-ignore
            knex.whereIn(
                "DF.DMSF_ID",
                Array.isArray(dmsfId) ? dmsfId : [dmsfId],
            );
        }

        if (Array.isArray(tagsToSelect)) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;
                knex.joinRaw(
                    `left join "ARCH_DMS_FILE_TAG" ${alias} on ${alias}."DMSF_ID" = "DF"."DMSF_ID" and ${alias}."DMST_ID" = ${tag.DMST_ID}`,
                );

                // rename folder tag
                if (tag.DMST_ID === DMS_TAGS.TYPE_FOLDER) {
                    knex.joinRaw(
                        `left join "DMS_FOLDER" "DF4" on ('' ${globalThis.orm.db.concat()} "DF4"."FOLDER_ID"${globalThis.dynamicConfig.db.client === "postgresql" ? "::varchar" : ""} = ${alias}."DMST_VALUE")`,
                    );
                }

                // filtering by tags
                if (tagsToFilter) {
                    const value = tagsToFilter[tag.DMST_ID];
                    if (value) {
                        knex.where(
                            `${alias}.DMST_VALUE`,
                            tagsToFilter[tag.DMST_ID],
                        );
                    }
                }
            });
        }

        // @ts-ignore
        return await this.createCollection(knex);
    }

    async getForUserOptimise(
        authUser: any,
        tagsToSelect: any,
        tagsToFilter: any,
    ) {
        const columns = [
            "DF.DMSF_ID",
            "IP.IPROC_ID",
            "IP.IPROC_NAME",
            "IP.IPROC_STATUS",
            "IP.IPROC_INST_OWNER_USER_ID",
            "TP.TPROC_NAME",
            "DF.NAME",
            "DF.REVISION",
            "DT.DMST_VALUE as V-4",
            "DF.USER_ID_INSERT",
            "DF.USER_ID_INSERT_VICE",
            "DF.DMSF_SRC",
            "DF.IS_CURRENT",
            "DF.IS_DELETED",
            "DF.FILE_TYPE",
            "DF.VIEW_IN_MAIN",
            "DF.USER_ID_DELETED",
            "DF.VIEW_IN_SUB",
            "DF.DATETIME_DELETED",
            "DF.DATETIME_INSERT",
            "DF.TO_INDEX",
            "DF.IS_FULL_INDEXED",
            "DF.DATETIME_INDEXED",
            "DF.DMSF_ID_PREV",
            globalThis.database.raw(`"U"."USER_DISPLAY_NAME" as "DMSF_OWNER"`),
            globalThis.database.raw(
                `"VICE"."USER_DISPLAY_NAME" as "DMSF_OWNER_VICE"`,
            ),
        ];
        if (tagsToSelect && Array.isArray(tagsToSelect)) {
            tagsToSelect.forEach((tag) => {
                const alias = `"DTF${tag.DMST_ID}"`;
                const colName = `${alias}."DMST_VALUE" as ${alias}`;
                columns.push(globalThis.database.raw(colName));

                if (tag.DMST_ID == -4) {
                    columns.push(
                        globalThis.database.raw(`"DF4"."FOLDER_NAME"`),
                    );
                }
            });
        }

        const userRepo = globalThis.orm.repo("user", this.connection);
        const isInspector = await userRepo.hasRole(
            authUser.USER_ID,
            ROLES.INSPECTOR,
        );
        const isGlobalSupervisor = await userRepo.hasRole(
            authUser.USER_ID,
            ROLES.GLOBAL_SUPERVISOR,
        );

        const mainProcListKnexQuery = this.connection
            .pluck("IPROC_ID")
            .from("ARCH_DMS_FILE")
            .whereNotNull("IPROC_ID");

        // @ts-ignore
        const dmsFileCollection = globalThis.orm.collection(
            "dmsFile",
            this.connection,
        );

        // Inspector and Supervisor ignore the Process rights
        if (!isInspector && !isGlobalSupervisor) {
            dmsFileCollection.knex = dmsFileCollection.knex.with(
                "SINGLE_USER_ROLES",
                (builder) => {
                    builder
                        .select("SUR.ROLE_ID")
                        .from("USER_ROLES as SUR")
                        .where("SUR.USER_ID", authUser.USER_ID);
                },
            );

            mainProcListKnexQuery.whereIn(
                "IPROC_ID",
                globalThis.orm
                    .repo("externalRight", this.connection)
                    .getUserProcesses(authUser.USER_ID, false, false, true),
            );
        }

        if (globalThis.dynamicConfig.dms.processRelationsEnabled) {
            dmsFileCollection.knex = dmsFileCollection.knex.with(
                // @ts-ignore
                globalThis.dynamicConfig.db.client === "mssql"
                    ? "subprocesses"
                    : globalThis.database.raw(`subprocesses ("IPROC_ID")`),
                function () {
                    // @ts-ignore
                    this.select("IPROC_ID")
                        .from("ARCH_INSTANCE_PROCESSES")
                        .whereNotNull("IPROC_ID")
                        .whereIn("IPROC_ID", mainProcListKnexQuery)
                        .unionAll(function () {
                            // @ts-ignore
                            this.select(
                                globalThis.database.raw(
                                    `"subprocessesplus1"."IPROC_ID"`,
                                ),
                            )
                                .from(
                                    globalThis.database.raw(
                                        `"ARCH_INSTANCE_PROCESSES" "subprocessesplus1", "subprocesses"`,
                                    ),
                                )
                                .whereRaw(
                                    `"subprocessesplus1"."IPROC_MAIN_IPROC_ID" = "subprocesses"."IPROC_ID"`,
                                );
                        });
                },
            );
        }

        dmsFileCollection.knex = dmsFileCollection.knex
            .select(columns)
            .from(`${this.tableName} as DF`)
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES as IP",
                "DF.IPROC_ID",
                "IP.IPROC_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            );
                    }); // Fallback to version = 1
            })
            .leftJoin("ARCH_DMS_FILE_TAG as DT", function () {
                // @ts-ignore
                this.on("DT.DMSF_ID", "DF.DMSF_ID").andOn("DT.DMST_ID", -4);
            })
            .leftJoin("USERS as U", "U.USER_ID", "DF.USER_ID_INSERT")
            .leftJoin("USERS as VICE", "VICE.USER_ID", "DF.USER_ID_INSERT_VICE")
            .where(function () {
                this.where(function () {
                    // Main Processes
                    this.whereIn("DF.IPROC_ID", mainProcListKnexQuery);

                    if (globalThis.dynamicConfig.dms.processRelationsEnabled) {
                        // Sub Processes
                        this.orWhere((builder) => {
                            builder
                                .where(
                                    "DF.VIEW_IN_MAIN",
                                    DmsFileConstants.VIEW_IN_MAIN_YES,
                                )
                                .whereIn("DF.IPROC_ID", function () {
                                    this.select("IPROC_ID").from(
                                        globalThis.database.raw("subprocesses"),
                                    );
                                });
                        });
                        // Parent processes
                        this.orWhere((builder) => {
                            builder
                                .where(
                                    "DF.VIEW_IN_SUB",
                                    DmsFileConstants.VIEW_IN_SUB_YES,
                                )
                                .whereIn("DF.IPROC_ID", function () {
                                    this.select("IPROC_MAIN_IPROC_ID")
                                        .from("ARCH_INSTANCE_PROCESSES")
                                        .whereIn(
                                            "IPROC_ID",
                                            mainProcListKnexQuery,
                                        );
                                });
                        });
                    }
                }).orWhere("DF.USER_ID_INSERT", authUser.USER_ID);
            });

        if (tagsToSelect) {
            tagsToSelect.forEach((tag: any) => {
                const alias = `"DTF${tag.DMST_ID}"`;

                dmsFileCollection.knex = dmsFileCollection.knex.joinRaw(
                    `left join "ARCH_DMS_FILE_TAG" ${alias} on ${alias}."DMSF_ID" = "DF"."DMSF_ID" and ${alias}."DMST_ID" = ${tag.DMST_ID}`,
                );

                // Join folder name for folder tag
                if (tag.DMST_ID == -4) {
                    dmsFileCollection.knex = dmsFileCollection.knex.joinRaw(
                        `left join "DMS_FOLDER" "DF4" on ("DF4"."FOLDER_ID"${globalThis.dynamicConfig.db.client === "postgresql" ? "::varchar" : ""} = ${alias}."DMST_VALUE")`,
                    );
                }

                // filtering by tags
                if (tagsToFilter) {
                    const value = tagsToFilter[tag.DMST_ID];
                    if (value) {
                        dmsFileCollection.knex.where(
                            `${alias}.DMST_VALUE`,
                            tagsToFilter[tag.DMST_ID],
                        );
                    }
                }
            });
        }

        return dmsFileCollection;
    }

    async getFile(dmsfId: number, _userId: number) {
        const tagRepo = globalThis.orm.repo("archivedDmsFileTag");

        const file = await this.collectFile(dmsfId);

        if (!file || !file.DMSF_SRC || typeof file.DMSF_SRC !== "string") {
            throw new InternalException(
                "Invalid or missing file source.",
                "INVALID_FILE_SRC",
            );
        }

        const filePath = file.src;

        if (file.DMSF_SRC.startsWith(DmsFileConstants.FILE_PREFIX)) {
            file.DMSF_SRC = path.join(
                globalThis.dynamicConfig.dms.storagePath,
                filePath,
            );
        } else {
            file.DMSF_SRC = filePath;
        }

        const tags = await tagRepo.getForFile(dmsfId).fetchAll();

        const data = await tagRepo.castRows(tags, {
            LIST_OF_VALUES: { type: "text" },
        });

        if (Array.isArray(data) && data.length > 0) {
            data.forEach((item) => {
                try {
                    item.LIST_OF_VALUES = JSON.parse(item.LIST_OF_VALUES);
                } catch (_err) {
                    // ignored
                }
            });
        }
        file.TAGS = data;
        return file;
    }

    async copyFileLink(dmsFile: DmsFile, iprocId: number) {
        const iprocIds: number[] = await this.getAllVisibileIprocIds(iprocId);
        // @ts-expect-error dunno
        const coll = globalThis.orm.collection(
            "dmsFile",
            this.getByName(iprocIds, dmsFile.NAME),
        );
        const foundFile = await coll.collectOne();

        const sourceFileId = dmsFile.DMSF_ID;

        // Copy DMS link entity.
        dmsFile.DMSF_ID = null;
        dmsFile.DMSF_ID_PREV = null;
        dmsFile.IPROC_ID = iprocId;

        let storedFile;
        if (foundFile) {
            dmsFile.IPROC_ID = foundFile.IPROC_ID;
            // @ts-expect-error dunno
            storedFile = await this.store(dmsFile, foundFile); // save as revision
        } else {
            storedFile = await this.store(dmsFile); // save new file
        }

        // @ts-ignore
        const dmsfId = storedFile.DMSF_ID;

        // Copy file tags.
        const tagRepo = globalThis.orm.repo(
            "archivedDmsFileTag",
            this.connection,
        );
        const fileTags = await tagRepo.getForFile(sourceFileId).collectAll();

        if (Array.isArray(fileTags) && fileTags.length > 0) {
            for (const tag of fileTags) {
                tag.DMSF_ID = dmsfId;
                await tagRepo.store(tag);
            }
            return storedFile;
        }
        return storedFile;
    }

    async getForProcessOnly(
        processId: number,
        tagsToSelect?: any,
        tagsToFilter?: any,
    ): Promise<BaseCollection<DmsFile>> {
        const coll = await this.getAll(tagsToSelect, tagsToFilter);

        coll.knex.where("DF.IPROC_ID", processId);
        return coll;
    }

    async getForProcess(
        processId: number,
        tagsToSelect?: any,
        tagsToFilter?: any,
        user?: any,
    ): Promise<BaseCollection<DmsFile>> {
        const procRepo = globalThis.orm.repo(
            "archivedProcess",
            this.connection,
        );
        const externalRightsRepo = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );

        const coll = await this.getAll(
            tagsToSelect,
            tagsToFilter,
            null,
            user ? user.USER_ID : null,
        );

        coll.knex
            .with(
                "SUB_PROCESSES",
                globalThis.database.raw(procRepo.withSubProcesses(), {
                    IPROC_ID: processId,
                }),
            )
            .with(
                "MAIN_PROCESSES",
                globalThis.database.raw(procRepo.withMainProcesses(), {
                    IPROC_ID: processId,
                }),
            )
            .where((mainBuilder) => {
                mainBuilder.where(function () {
                    this.where("DF.IPROC_ID", processId);
                });

                if (globalThis.dynamicConfig.dms.processRelationsEnabled) {
                    mainBuilder
                        .orWhere(function () {
                            this.where("DF.VIEW_IN_SUB", "Y").whereIn(
                                "DF.IPROC_ID",
                                (builder) => {
                                    builder
                                        // Includes self but it doesn't matter
                                        .select("IPROC_ID")
                                        .from("MAIN_PROCESSES");
                                    // TODO: Union instead of orWhere?
                                },
                            );
                        })
                        .orWhere(function () {
                            this.where("DF.VIEW_IN_MAIN", "Y").whereIn(
                                "DF.IPROC_ID",
                                (builder) => {
                                    builder
                                        // Includes self but it doesn't matter
                                        .select("IPROC_ID")
                                        .from("SUB_PROCESSES");
                                },
                            );
                        });
                }
            });

        if (user && globalThis.dynamicConfig.dms.processRelationsEnabled) {
            coll.knex.whereIn("DF.IPROC_ID", function () {
                this.select("IPROC_ID").from(
                    externalRightsRepo
                        .getUserProcesses(
                            user.USER_ID,
                            user.hasElevatedRole(),
                            false,
                            true,
                        )
                        .as("ignored_alias"),
                );
            });
        }

        return coll;
    }

    async belongsToProcess(
        identifier: number | string,
        processId: number,
        onlyForProcess = false,
        throwError = false,
    ): Promise<any> {
        // Check if the File belongs to the Process
        const coll = onlyForProcess
            ? await this.getForProcessOnly(processId)
            : await this.getForProcess(processId);
        const processFiles = await coll.collectAll();
        const processFilesIdentifier = UtilsService.isNumericString(identifier)
            ? "DMSF_ID"
            : "NAME";

        // The file does not belong to the Process
        const fileFound = _.find(processFiles, {
            [processFilesIdentifier]:
                processFilesIdentifier === "NAME"
                    ? identifier
                    : Number(identifier),
            IS_CURRENT: DmsFileConstants.CURRENT_YES,
        });
        if (!fileFound) {
            // Throw Error?
            if (throwError) {
                throw new UserException(
                    "The requested file does not belong to this Process or does not exist!",
                    "INVALID_FILE",
                    {
                        processFiles: processFiles.map((item: any) => ({
                            DMSF_ID: item.DMSF_ID,
                            NAME: item.NAME,
                        })),
                        file: identifier,
                    },
                );
            }
            return false;
        }
        return fileFound;
    }

    // @ts-ignore
    getByName(iprocId: number | number[], name: string | string[]) {
        if (Array.isArray(name) && name.length == 0) {
            return [];
        }

        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("IS_CURRENT", "Y");

        if (Array.isArray(name)) {
            conn.whereIn("NAME", name);
        } else {
            conn.where("NAME", name);
        }

        if (Array.isArray(iprocId)) {
            conn.whereIn("IPROC_ID", iprocId);
        } else {
            conn.where("IPROC_ID", iprocId);
        }

        return conn;
    }

    async getFileContents(dmsfId: number) {
        const fileEntity = await this.get(dmsfId);
        return await globalThis.container.service.file.fileMetadataServiceModule.fileToBuffer(
            fileEntity.getAbsoluteFilePath(),
        );
    }

    async getAllVisibileIprocIds(iprocId: number) {
        const procRepo = globalThis.orm.repo(
            "archivedProcess",
            this.connection,
        );

        const iprocIdExists = await procRepo.exists("IPROC_ID", iprocId);
        if (!iprocIdExists) {
            throw new UserException(
                `Process with id ${iprocId} doesn't exist.`,
                "PROCESS_NOT_FOUND",
                { iproc_id: iprocId },
            );
        }

        const mainProcs = await this.inMainProcesses(iprocId);
        const subProcs = await this.inSubProcesses(iprocId);
        // @ts-ignore
        return _.uniq([...mainProcs, ...subProcs, iprocId]);
    }

    inSubProcesses(iprocId: number, attachWithClause?: boolean) {
        const procRepo = globalThis.orm.repo(
            "archivedProcess",
            this.connection,
        );
        return procRepo
            .getSubProcesses(iprocId, attachWithClause)
            .whereIn("IPROC_DMS_VISIBILITY", ["M", "SM", "MS"])
            .pluck("IPROC_ID");
    }

    inMainProcesses(iprocId: number, attachWithClause?: boolean) {
        const procRepo = globalThis.orm.repo(
            "archivedProcess",
            this.connection,
        );
        return procRepo
            .getMainProcesses(iprocId, attachWithClause)
            .whereIn("IPROC_DMS_VISIBILITY", ["S", "SM", "MS"])
            .pluck("IPROC_ID");
    }

    getDmsFilesFromSubProcesses(iprocId: number, searchRevisions = false) {
        const conn = this.connection
            .select()
            .with(
                "SUB_PROCESSES",
                this.connection.raw(
                    globalThis.orm.repo("archivedProcess").withSubProcesses(),
                    { IPROC_ID: iprocId },
                ),
            )
            .from("ARCH_DMS_FILE")
            .whereIn("IPROC_ID", this.inSubProcesses(iprocId, false));

        if (searchRevisions) {
            return conn
                .orderBy("IS_CURRENT", "desc")
                .orderBy("DATETIME_INSERT", "desc")
                .limit(1);
        }

        return conn.where("IS_CURRENT", "Y");
    }

    getDmsFilesFromMainProcesses(iprocId: number, searchRevisions = false) {
        const conn = this.connection
            .select()
            .with(
                "MAIN_PROCESSES",
                this.connection.raw(
                    globalThis.orm.repo("archivedProcess").withMainProcesses(),
                    { IPROC_ID: iprocId },
                ),
            )
            .from("ARCH_DMS_FILE")
            .whereIn("IPROC_ID", this.inMainProcesses(iprocId, false));

        if (searchRevisions) {
            return conn
                .orderBy("IS_CURRENT", "desc")
                .orderBy("DATETIME_INSERT", "desc")
                .limit(1);
        }

        return conn.where("IS_CURRENT", "Y");
    }

    getDmsEntityByNameFromSubProcess(
        iprocId: number,
        name: string,
        searchRevisions = false,
    ) {
        return this.getDmsFilesFromSubProcesses(iprocId, searchRevisions).where(
            "NAME",
            name,
        );
    }

    getDmsEntityByNameFromMainProcess(
        iprocId: number,
        name: string,
        searchRevisions = false,
    ) {
        return this.getDmsFilesFromMainProcesses(
            iprocId,
            searchRevisions,
        ).where("NAME", name);
    }
}
