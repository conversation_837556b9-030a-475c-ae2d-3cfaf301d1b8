// @ts-nocheck
// @ts-nocheck
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import Bcrypt from "bcrypt";
import _ from "lodash";
import PasswordValidator from "password-validator";
import { ProcessInfo } from "../../utils/ProcessInfo";
import * as TASK from "../entity/const/taskConst";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { LDAPSync } from "../../../service/authorization/authorities/common/LDAPSync";
import { AuthorizedUser } from "../../../service/authorization/AuthorizedUser";
import * as organizationStructureConstants from "../entity/const/organizationStructureConsts";
import * as USER_PARAMETERS from "../entity/const/userParameterConsts";
import * as ROLES from "../entity/const/roleConst";
import * as userConstants from "../entity/const/userConst";
import { User } from "../entity/User";
import { AuthException } from "../../../utils/errorHandling/exceptions/authException";
import { MailOptionsParams } from "../../../client/mail/BaseMailClient";
import { OrganizationStructureRepository } from "./OrganizationStructureRepository";
import { UserRoleRepository } from "./UserRoleRepository";
import { RoleRepository } from "./RoleRepository";
import { UserOrganizationStructureRepository } from "./UserOrganizationStructureRepository";
import { ExternalRightRepository } from "./ExternalRightRepository";

export interface IUserWithOrgStr {
    USER_FULL_NAME: string;
    USER_DISPLAY_NAME: string;
    ORGSTR_NAME: string;
    USER_LAST_NAME: string;
    USER_FIRST_NAME: string;
    USER_ID: number;
    USER_EMAIL: string;
    ORGSTR_ID: number;
    MANAGER_USER_ID: number;
}

export class UserRepository extends BaseRepository<User> {
    meta() {
        return {
            tableName: "USERS",
            entityName: "User",
            defaultAlias: "U",
            entity: () => new User(),
        };
    }

    /**
     * Validate entity against all validations.
     * @param entity
     * @return {Promise<any[]> | Promise.<*[]>}
     */
    validate(entity) {
        return Promise.all([this.validateUniqueName(entity)]).then(() =>
            super.validate(entity),
        );
    }

    validateUniqueName(entity) {
        if (
            typeof entity.USER_NAME === "undefined" ||
            (typeof entity.USER_ID !== "undefined" && entity.USER_ID !== null)
        ) {
            return true;
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where(
                this.connection.raw(globalThis.orm.db.upper("USER_NAME", true)),
                entity.USER_NAME.toUpperCase(),
            ) // Couldn't make it work without raw. Is it a problem with MSSQL?
            .whereIn("USER_STATUS", ["A", "L"])
            .then((users) => {
                if (Array.isArray(users) && users.length > 0) {
                    throw new UserException(
                        `User name '${entity.USER_NAME.toUpperCase()}' already exists!`,
                        "UNIQUE_CONSTRAINT",
                    );
                }
                return true;
            });
    }

    async assignManagers(orgstrId, userId) {
        const repo: OrganizationStructureRepository = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );

        let orgs = orgstrId;
        if (!Array.isArray(orgstrId)) {
            orgs = [orgstrId];
        }
        try {
            await this.connection
                .select()
                .from(repo.tableName)
                .where("MANAGER_USER_ID", userId)
                .update({ MANAGER_USER_ID: null });

            for (const orgId of orgs) {
                const { entity } = repo;
                entity.ORGSTR_ID = orgId;
                entity.MANAGER_USER_ID = userId;

                await repo.store(entity);
            }

            return 1;
        } catch (err) {
            throw err;
        }
    }

    async addRole(userId, roleId, roleSource) {
        if (await this.hasRole(userId, roleId)) {
            return null;
        }

        const userRoleRepo: UserRoleRepository = globalThis.orm.repo(
            "userRole",
            this.connection,
        );
        const roleRepo: RoleRepository = globalThis.orm.repo(
            "role",
            this.connection,
        );

        const { entity } = userRoleRepo;
        entity.USER_ID = userId;
        entity.ROLE_ID = roleId;
        entity.ROLE_SOURCE = roleSource;

        const result = await userRoleRepo.store(entity);
        await roleRepo.checkRoleAssignsCount(roleId);
        return result;
    }

    hasRole(userId, roleId) {
        return this.connection
            .select()
            .from("USER_ROLES")
            .where("USER_ID", userId)
            .where("ROLE_ID", roleId)
            .then((roles) => Array.isArray(roles) && roles.length > 0);
    }

    async removeRole(userId, roleId) {
        await this.connection
            .from("USER_ROLES")
            .where({ USER_ID: userId, ROLE_ID: roleId })
            .whereNot("ROLE_SOURCE", ROLES.SOURCE_COMPETENCE)
            .delete();

        await globalThis.container.service.temporary.cacheModule.invalidateCachedUser(
            userId,
        );
    }

    /**
     *  Returns role map. {role_id1: true/false, role_id2: true/false, ..roleIds..}
     * @param {Number} userId User id
     * @param {Array<Number>} roleIds Role id array
     */
    getRoleRelation(userId, roleIds) {
        return this.connection
            .select()
            .from("USER_ROLES")
            .where("USER_ID", userId)
            .whereIn("ROLE_ID", Array.isArray(roleIds) ? roleIds : [roleIds])
            .then((roles) => {
                const out = {};
                roleIds.forEach((id) => {
                    out[id] = !!_.find(roles, { ROLE_ID: id });
                });
                return out;
            });
    }

    async assignRoles(
        userId,
        roleIds: number[] = [],
        withElevatedRights,
        _accessRoles,
        roleSource,
    ) {
        // Make sure Roles are Integers
        const newRoleIds = roleIds.map((roleId) => Number(roleId));

        // Get current Roles of the User
        const roleRepo: RoleRepository = globalThis.orm.repo(
            "role",
            this.connection,
        );
        const userRoleRepo: UserRoleRepository = globalThis.orm.repo(
            "userRole",
            this.connection,
        );
        const currentUserRolesCollection = roleRepo.getForUser(userId, [
            "RO.ROLE_ID",
        ]);

        // Do not include Competence
        currentUserRolesCollection.knex.whereNot(
            "UR.ROLE_SOURCE",
            ROLES.SOURCE_COMPETENCE,
        );

        const currentUserRoles = await currentUserRolesCollection.fetchAll();

        // Determine if a Role is to be deleted, inserted or ignored
        const roleIdsToInsert = _.difference(
            newRoleIds,
            currentUserRoles.map((item) => item.ROLE_ID),
        );
        const roleIdsToDelete = _.difference(
            currentUserRoles.map((item) => item.ROLE_ID),
            newRoleIds,
        );
        const modifiedRolesIds = [...roleIdsToInsert, ...roleIdsToDelete];
        const modifiedRoles = modifiedRolesIds.map((roleId) =>
            userRoleRepo.getEntity({ ROLE_ID: roleId }),
        );

        // Check if Admin rights are present
        if (!withElevatedRights) {
            // User should not be able to modify system roles
            if (modifiedRoles.some((role) => role.isSystemRole)) {
                throw new UserException(
                    "User does not have privileges to modify system Roles!",
                    "LACK_OF_PERMISSIONS",
                    {
                        systemRoles: modifiedRoles.filter(
                            (role) => role.isSystemRole,
                        ),
                    },
                );
            }
        }

        // Delete Roles
        await this.connection
            .returning("ROLE_ID")
            .from("USER_ROLES")
            .where("USER_ID", userId)
            .whereNot("ROLE_SOURCE", ROLES.SOURCE_COMPETENCE)
            .whereIn("ROLE_ID", roleIdsToDelete)
            .del();

        // Insert ALL_USERS role if not present
        if (
            ![
                ...modifiedRolesIds,
                ...currentUserRoles.map((item) => item.ROLE_ID),
            ].includes(ROLES.ALL_USERS)
        ) {
            roleIdsToInsert.push(ROLES.ALL_USERS);
        }

        // Insert Roles
        for (const roleId of roleIdsToInsert) {
            const userRoleEntity = userRoleRepo.getEntity({
                USER_ID: userId,
                ROLE_ID: roleId,
                ROLE_SOURCE: roleSource,
            });

            await userRoleRepo.store(userRoleEntity);
            await roleRepo.checkRoleAssignsCount(roleId);
        }
    }

    getForRole(roleId, distinct = false) {
        let conn = this.connection
            .select(
                "U.USER_FIRST_NAME",
                "U.USER_LAST_NAME",
                "U.USER_DISPLAY_NAME",
                "U.USER_NAME",
                "U.USER_ID",
                "U.USER_STATUS",
                "U.USER_EMAIL",
                "U.USER_EXTERNAL_SOURCE",
                "U.EXTERNAL_ID",
                "U.USER_SYSTEM",
                "R.ROLE_NAME",
            )
            .from("USER_ROLES as UR")
            .leftJoin("ROLES as R", "UR.ROLE_ID", "R.ROLE_ID")
            .leftJoin("USERS as U", "UR.USER_ID", "U.USER_ID")
            .whereIn("UR.ROLE_ID", Array.isArray(roleId) ? roleId : [roleId]);

        if (distinct) {
            conn = conn.distinct();
        }

        return conn;
    }

    getForOrganization(orgstrId) {
        const conn = this.connection
            .select(
                "U.USER_FIRST_NAME",
                "U.USER_LAST_NAME",
                "U.USER_DISPLAY_NAME",
                "U.USER_NAME",
                "U.USER_ID",
                "U.USER_STATUS",
                "U.USER_EMAIL",
                "U.USER_EXTERNAL_SOURCE",
                "U.EXTERNAL_ID",
                "U.USER_SYSTEM",
            )
            .from("USER_ORGANIZATION_STRUCTURE as UOS")
            .leftJoin("USERS as U", "UOS.USER_ID", "U.USER_ID")
            .where("UOS.ORGSTR_ID", orgstrId);

        return conn;
    }

    getOrganization(userId) {
        return this.connection
            .pluck("ORGSTR_ID")
            .from("USER_ORGANIZATION_STRUCTURE")
            .where("USER_ID", userId)
            .then((data) => data);
    }

    async setOrganization(users, orgstrId) {
        const orgRepo: UserOrganizationStructureRepository =
            globalThis.orm.repo("userOrganizationStructure", this.connection);
        const extRightsRepo: ExternalRightRepository = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );

        let formerManagerUserIds = [];
        const userIds = Array.isArray(users) ? users : [users];

        for (const userId of userIds) {
            const formerManagerUsers =
                await extRightsRepo.getManagerUsers(userId);
            formerManagerUserIds.push(
                ...formerManagerUsers.map((user) => user.MANAGER_USER_ID),
            );
        }
        formerManagerUserIds = _.uniq(formerManagerUserIds);

        // sync dyn rights after user assign/remove orgUnit in User window
        if (this.connection !== globalThis.database) {
            this.connection.once("committed", () => {
                // Sync former managers rights
                formerManagerUserIds.map((managerId) =>
                    globalThis.orm
                        .repo("externalRight")
                        .processSyncDynamicRightsByManagerId(managerId),
                );

                // Update current manager rights
                userIds.map((userId) =>
                    globalThis.orm
                        .repo("externalRight")
                        .onUserOrganizationChanged(userId, false),
                );
            });
        } else {
            globalThis.tasLogger.warning(
                "Dynamic Rights not recalculated upon setting OrgStrUnit of user. Connection is the same as globalThis.database. Not running in transaction",
            );
        }

        await orgRepo.connection
            .delete()
            .from("USER_ORGANIZATION_STRUCTURE")
            .whereIn("USER_ID", userIds);

        const orgUnitAssignments = [];
        for (const userId of userIds) {
            const entity = orgRepo.getEntity();
            entity.USER_ID = userId;
            entity.ORGSTR_ID = orgstrId;
            const assignment = await orgRepo.store(entity);
            orgUnitAssignments.push(assignment);
        }
        return orgUnitAssignments;
    }

    /**
     *
     * @param {Array<Number>} users
     * @param {Number} orgstrId
     * @returns {Knex.Builder & Promise<boolean>}
     */
    async updateOrganization(users, orgstrId) {
        // Prevent: ORA-01795: maximum number of expressions in a list is 1000
        const usersChunked = _.chunk(users, 999);

        const extRightsRepo: ExternalRightRepository = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );
        let formerManagerUserIds = [];
        const userIds = Array.isArray(users) ? users : [users];

        for (const userId of userIds) {
            const formerManagerUsers =
                await extRightsRepo.getManagerUsers(userId);
            formerManagerUserIds.push(
                ...formerManagerUsers.map((user) => user.MANAGER_USER_ID),
            );
        }
        formerManagerUserIds = _.uniq(formerManagerUserIds);

        // sync dyn rights after user assign/remove orgUnit in OrgUnit window
        if (this.connection !== globalThis.database) {
            this.connection.once("committed", () => {
                // Sync former managers rights
                formerManagerUserIds.map((managerId) =>
                    globalThis.orm
                        .repo("externalRight")
                        .processSyncDynamicRightsByManagerId(managerId),
                );

                // Update current manager rights
                users.map((userId) =>
                    globalThis.orm
                        .repo("externalRight")
                        .onUserOrganizationChanged(userId, false),
                );
            });
        } else {
            globalThis.tasLogger.warning(
                "Dynamic Rights not recalculated upon updating OrgStrUnit of user. Connection is the same as globalThis.database. Not running in transaction",
            );
        }

        let conn = this.connection("USER_ORGANIZATION_STRUCTURE").update(
            "ORGSTR_ID",
            orgstrId,
        );
        usersChunked.forEach((userIds) => {
            conn = conn.orWhereIn("USER_ID", globalThis.database.raw(userIds));
        });
        return users.length ? await conn : true;
    }

    /**
     * Get all users matching name.
     * @param userName
     * @returns BaseCollection
     */
    getByUserName(userName) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .whereRaw(
                `${globalThis.orm.db.upper("USER_NAME", true)} = ${globalThis.orm.db.upper("?")}`,
                userName,
            );
        return globalThis.orm.collection("User", conn);
    }

    lockUsers(usersIds) {
        // Unlock all, lock them next time
        return this.connection
            .select()
            .from(this.tableName)
            .where("USER_STATUS", "L")
            .orWhereNull("USER_NAME")
            .update({ USER_STATUS: "A" })
            .then(() => {
                let connection = this.connection.select().from(this.tableName);

                connection.where(-1, 1); // t3b-1415 prevents executing unwanted sql when chunks are empty
                // Prevent: ORA-01795: maximum number of expressions in a list is 1000
                const chunked = _.chunk(usersIds, 999);
                chunked.forEach((chunk) => {
                    connection = connection.orWhereIn(
                        "USER_ID",
                        globalThis.database.raw(chunk),
                    );
                });
                return connection.update({ USER_STATUS: "L" });
            });
    }

    /**
     * Get user matching name or displayName. Active first.
     * @param userName
     * @returns BaseCollection
     */
    async getByUserNameOrDisplayname(userName) {
        // Try to find by USER_NAME -> separated bcs USER_NAME should be matched firstly instead displayName.
        const userList = globalThis.orm.collection("User");
        userList.knex
            .from(this.tableName)
            .where((builder) => {
                builder.whereRaw(
                    `${globalThis.orm.db.upper("USER_NAME", true)} = ${globalThis.orm.db.upper("?")}`,
                    userName,
                );
                builder.orWhereRaw(
                    `${globalThis.orm.db.upper("USER_DISPLAY_NAME", true)} = ${globalThis.orm.db.upper("?")}`,
                    userName,
                );
                builder.orWhereRaw(
                    `${globalThis.orm.db.upper(`"USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "USER_FIRST_NAME"`)} = ${globalThis.orm.db.upper("?")}`,
                    userName,
                );
            })
            .whereNot("USER_STATUS", "D")
            .orderBy("USER_STATUS");
        return await userList.collectOne();
    }

    /**
     * Get by login. Only active user is returned.
     * @param {stirng} login
     */
    getByLogin(login) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .whereRaw(
                `${globalThis.orm.db.upper("USER_NAME", true)} = ${globalThis.orm.db.upper("?")}`,
                login,
            )
            .where("USER_STATUS", "A");
        return globalThis.orm.collection("User", conn);
    }

    getByStatus(status) {
        let conn = this.connection
            .select([
                "U.USER_ID",
                this.connection.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
                "U.USER_NAME",
                "U.USER_FIRST_NAME",
                "U.USER_LAST_NAME",
                "U.USER_STATUS",
                "U.USER_EMAIL",
                "U.ORG_ID",
                "U.USER_CHANGE_PASSWORD",
                "U.USER_PASSWORD_LAST_CHANGE",
                "U.USER_BAD_LOGIN_COUNT",
                "U.USER_TITLE_PREFIX",
                "U.USER_TITLE_SUFFIX",
                "U.USER_EXTERNAL_LOGIN",
                "U.USER_EXTERNAL_SOURCE",
                "U.USER_COMP",
                "U.USER_COMP_ID",
                "U.USER_COMP_CODE",
                "U.LOGIN_COUNT",
                "U.EXTERNAL_ID",
                "U.USER_SYSTEM",
            ])
            .from(`${this.tableName} as U`);

        conn = conn.whereIn(
            "U.USER_STATUS",
            Array.isArray(status) ? status : [status],
        );

        return globalThis.orm.collection("User", conn);
    }

    /**
     * Returns only active user(s).
     * Suitable for all workflow methods, where can act only active users.
     *
     * @param id {Integer|Array.<USER_ID>}
     * @return {BaseCollection}
     */
    getActiveById(id) {
        const coll = this.getById(id);
        coll.knex.where("USER_STATUS", userConstants.STATUS_ACTIVE);
        return coll;
    }

    /**
     * Returns only active user(s).
     * Suitable for all workflow methods, where can act only active users.
     *
     * @param id {Integer|Array.<USER_ID>}
     * @return {BaseCollection}
     */
    getNonDeletedById(id) {
        const coll = this.getById(id);
        coll.knex.whereIn("USER_STATUS", [
            userConstants.STATUS_ACTIVE,
            userConstants.STATUS_LOCKED,
        ]);
        return coll;
    }

    getById(id) {
        const collection = this.getCollection(
            (_item, key) => key !== "USER_PASSWORD" && key !== "USER_PHOTO", // ignores these columns
        );

        collection.knex.from(this.tableName);

        if (Array.isArray(id)) {
            if (id.length > 999) {
                const chunked = _.chunk(id, 999);
                chunked.forEach((chunk) => {
                    collection.knex.whereIn(
                        "USER_ID",
                        globalThis.database.raw(chunk),
                    );
                });
            } else {
                collection.knex.whereIn("USER_ID", id);
            }
        } else {
            collection.knex.where("USER_ID", id);
        }

        return collection;
    }

    async getUsersWithOrgStrConnection(
        userId,
        userOrgstrId,
        task,
        sort = true,
        activeUsersOnly = true,
        columns = null,
    ) {
        let conn;
        let cols = columns;
        let wrapAlias = null;

        if (!columns) {
            cols = [
                this.connection.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
                "OS.ORGSTR_NAME",
                "U.USER_LAST_NAME",
                "U.USER_FIRST_NAME",
                "U.USER_ID",
                "U.USER_EMAIL",
                "OS.ORGSTR_ID",
                "OS.MANAGER_USER_ID",
            ];
        }

        const {
            ITASK_ASSESMENT_HIERARCHY: style,
            ITASK_ASSESMENT_ROLE_ID: roleIdConstraint,
            ITASK_ASSESMENT_ORGSTR_CNST: orgstrIdConstraint,
            IPROC_ID: iProcId,
            ITASK_TYPE: type,
        } = task;

        switch (style) {
            case "C": // ASSESMENT_HIERARCHY_CHILDREN. přímí podřízení garanta.
                conn = this.connection
                    .select(cols)
                    .from("ORGANIZATION_STRUCTURE as OS")
                    .leftJoin(
                        "USER_ORGANIZATION_STRUCTURE AS UOS",
                        "OS.ORGSTR_ID",
                        "UOS.ORGSTR_ID",
                    )
                    .leftJoin("USERS AS U", "UOS.USER_ID", "U.USER_ID")
                    .where("OS.MANAGER_USER_ID", userId)
                    .where("UOS.ORGSTR_ID", userOrgstrId)
                    .whereNot("U.USER_ID", userId); // Not me.
                break;
            case "D": // ASSESMENT_HIERARCHY_DESCENDANTS. všichni podřízení garanta.
                conn = this.connection
                    .select(cols)
                    .from("ORGANIZATION_STRUCTURE as OS")
                    .leftJoin(
                        "USER_ORGANIZATION_STRUCTURE AS UOS",
                        "OS.ORGSTR_ID",
                        "UOS.ORGSTR_ID",
                    )
                    .leftJoin("USERS as U", function users() {
                        this.on("U.USER_ID", "OS.MANAGER_USER_ID").orOn(
                            "U.USER_ID",
                            "UOS.USER_ID",
                        ); // manager or user
                    })
                    .withRecursive("org_tree", (qb) => {
                        qb.select("ORGSTR_ID")
                            .from("ORGANIZATION_STRUCTURE")
                            .where("ORGSTR_ID", userOrgstrId)
                            .unionAll(function () {
                                this.select("child.ORGSTR_ID")
                                    .from("ORGANIZATION_STRUCTURE as child")
                                    .join(
                                        "org_tree as parent",
                                        "parent.ORGSTR_ID",
                                        "child.PARENT_ORGSTR_ID",
                                    );
                            });
                    })
                    .whereIn("OS.ORGSTR_ID", function () {
                        this.select("ORGSTR_ID").from("org_tree");
                    })
                    .whereNot("U.USER_ID", userId) // Not me
                    .orderBy("U.USER_LAST_NAME")
                    .orderBy("U.USER_FIRST_NAME");

                break;
            case "P": // ASSESMENT_HIERARCHY_PARENT Přímý nadřízený garanta
                // Get only first non null manager_user_id from lowest organization
                conn = this.connection
                    .select(cols)
                    .from("ORGANIZATION_STRUCTURE AS OS")
                    .whereIn(
                        "OS.ORGSTR_ID",
                        this.connection.raw(
                            `WITH RECURSIVE org_tree AS (
    -- Anchor: start with the user's organization
    SELECT
        "ORGSTR_ID",
        "PARENT_ORGSTR_ID",
        "MANAGER_USER_ID"
    FROM "ORGANIZATION_STRUCTURE"
    WHERE "ORGSTR_ID" = :ORGSTR_ID

    UNION ALL

    -- Recursive: traverse *up* toward ancestors
    SELECT
        parent."ORGSTR_ID",
        parent."PARENT_ORGSTR_ID",
        parent."MANAGER_USER_ID"
    FROM "ORGANIZATION_STRUCTURE" parent
    INNER JOIN org_tree child
        ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
)
SELECT "ORGSTR_ID"
FROM org_tree
WHERE "MANAGER_USER_ID" IS NOT NULL
  AND "MANAGER_USER_ID" <> :USER_ID
LIMIT 1
`,
                            { ORGSTR_ID: userOrgstrId, USER_ID: userId },
                        ),
                    )
                    .leftJoin("USERS AS U", "U.USER_ID", "OS.MANAGER_USER_ID")
                    .whereNot("U.USER_ID", userId); // Not me.
                break;
            case "A": // ASSESMENT_HIERARCHY_ANCESTORS Všichni nadřízení garanta
                conn = this.connection
                    .select(cols)
                    .from("ORGANIZATION_STRUCTURE AS OS")
                    .whereIn(
                        "OS.ORGSTR_ID",
                        this.connection.raw(
                            `WITH RECURSIVE org_tree AS (
                        -- Anchor: start at user's org unit
                        SELECT "ORGSTR_ID", "PARENT_ORGSTR_ID", "MANAGER_USER_ID"
                        FROM "ORGANIZATION_STRUCTURE"
                        WHERE "ORGSTR_ID" = :ORGSTR_ID

                        UNION ALL

                        -- Recursive: go up the tree via PARENT_ORGSTR_ID
                        SELECT parent."ORGSTR_ID", parent."PARENT_ORGSTR_ID", parent."MANAGER_USER_ID"
                        FROM "ORGANIZATION_STRUCTURE" parent
                        INNER JOIN org_tree child ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
                    )
                    SELECT "ORGSTR_ID"
                    FROM org_tree
                    WHERE "MANAGER_USER_ID" IS NOT NULL AND "MANAGER_USER_ID" <> :USER_ID`,
                            {
                                ORGSTR_ID: userOrgstrId,
                                USER_ID: userId,
                            },
                        ),
                    )
                    .leftJoin("USERS AS U", "U.USER_ID", "OS.MANAGER_USER_ID")
                    .whereNot("U.USER_ID", userId); // Not me
                break;
            case "S": // ASSESMENT_HIERARCHY_SIBLINGS Kolegové garanta plánu
                conn = this.connection
                    .select(cols)
                    .from("ORGANIZATION_STRUCTURE AS OS")
                    .leftJoin(
                        "USER_ORGANIZATION_STRUCTURE AS UOS",
                        "OS.ORGSTR_ID",
                        "UOS.ORGSTR_ID",
                    )
                    .leftJoin("USERS AS U", "UOS.USER_ID", "U.USER_ID")
                    .where("OS.ORGSTR_ID", userOrgstrId)
                    .whereRaw(
                        `EXISTS (SELECT "USER_ID" FROM "USER_ORGANIZATION_STRUCTURE" WHERE "USER_ID" = :USER_ID AND "ORGSTR_ID" = :ORGSTR_ID)`,
                        {
                            ORGSTR_ID: userOrgstrId,
                            USER_ID: userId,
                        },
                    )
                    .whereNot("U.USER_ID", userId); // Not me.
                break;
            case "G": // ASSESMENT_HIERARCHY_GUARANTOR  Pouze garant
                conn = this.connection
                    .select(cols)
                    .from("USERS as U")
                    .leftJoin(
                        "ORGANIZATION_STRUCTURE as OS",
                        "OS.ORGSTR_ID",
                        "OS.ORGSTR_ID",
                    )
                    .where("U.USER_ID", userId)
                    .where("OS.ORGSTR_ID", userOrgstrId);
                break;
            case null: // not pass style
            case "L": // ASSESMENT_HIERARCHY_ALL Nespecifikováno = všichni uživvatelé na všech židlích.
                conn = this.connection
                    .select(cols)
                    .from("USER_ORGANIZATION_STRUCTURE AS UOS")
                    .leftJoin(
                        "ORGANIZATION_STRUCTURE AS OS",
                        "OS.ORGSTR_ID",
                        "UOS.ORGSTR_ID",
                    )
                    .leftJoin("USERS AS U", function users() {
                        this.on("U.USER_ID", "UOS.USER_ID").orOn(
                            "U.USER_ID",
                            "OS.MANAGER_USER_ID",
                        );
                    }); // can include self

                // @t3b-2217 Omezení možnosti spustit událost pro uživatele, kteří mají viditelnost na případ
                if (type === TASK.TYPE_EVENT_WAIT) {
                    const processInfo = new ProcessInfo(this.connection);
                    const processRightsCollection =
                        processInfo.processRightsMap(iProcId);

                    // Select only USER_ID
                    processRightsCollection.knex
                        .clearSelect()
                        .select("U.USER_ID");

                    // Limit the possible users by visibility
                    conn.whereIn("U.USER_ID", processRightsCollection.knex);
                }
                break;
            case userConstants.RELATIONSHIP_DIRECT_SUBORDINATES_NO_CHAIR: // all direct subordinates of user, regardless of chair
                conn = this.connection
                    .select(cols)
                    .from("ORGANIZATION_STRUCTURE as OS")
                    .leftJoin(
                        "USER_ORGANIZATION_STRUCTURE AS UOS",
                        "OS.ORGSTR_ID",
                        "UOS.ORGSTR_ID",
                    )
                    .leftJoin("USERS AS U", "UOS.USER_ID", "U.USER_ID")
                    .where("OS.MANAGER_USER_ID", userId)
                    .whereNot("U.USER_ID", userId); // Not me.
                break;
            case userConstants.RELATIONSHIP_SUBORDINATES_NO_CHAIR: // all subordinates of user, regardless of chair
                if (activeUsersOnly) {
                    cols.push("U.USER_STATUS");
                }

                const select = this.connection
                    .select(cols)
                    .from(
                        this.connection.raw(
                            `(WITH RECURSIVE org_tree AS (
                            -- Anchor: Start with the given MANAGER_USER_ID
                            SELECT "ORGSTR_ID", "ORGSTR_NAME", "ORGANIZATION_STATUS", "MANAGER_USER_ID", "PARENT_ORGSTR_ID"
                            FROM "ORGANIZATION_STRUCTURE"
                            WHERE "MANAGER_USER_ID" = ?

                            UNION ALL

                            -- Recursive: Traverse the org structure hierarchy
                            SELECT child."ORGSTR_ID", child."ORGSTR_NAME", child."ORGANIZATION_STATUS", child."MANAGER_USER_ID", child."PARENT_ORGSTR_ID"
                            FROM "ORGANIZATION_STRUCTURE" child
                            INNER JOIN org_tree parent ON parent."ORGSTR_ID" = child."PARENT_ORGSTR_ID"
                        )
                        SELECT "ORGSTR_ID", "ORGSTR_NAME", "ORGANIZATION_STATUS", "MANAGER_USER_ID"
                        FROM org_tree) AS "OS"`,
                            [userId],
                        ),
                    )
                    .leftJoin("USERS as U", "U.USER_ID", "OS.MANAGER_USER_ID")
                    .where("OS.ORGANIZATION_STATUS", "A")
                    .whereNot("U.USER_ID", userId) // Not me
                    .union((builder) => {
                        builder
                            .select(cols)
                            .from(
                                globalThis.database.raw(
                                    `(WITH RECURSIVE org_tree AS (
                                    -- Anchor: Start with the given MANAGER_USER_ID
                                    SELECT "ORGSTR_ID", "ORGSTR_NAME", "ORGANIZATION_STATUS", "MANAGER_USER_ID", "PARENT_ORGSTR_ID"
                                    FROM "ORGANIZATION_STRUCTURE"
                                    WHERE "MANAGER_USER_ID" = ?

                                    UNION ALL

                                    -- Recursive: Traverse the org structure hierarchy
                                    SELECT child."ORGSTR_ID", child."ORGSTR_NAME", child."ORGANIZATION_STATUS", child."MANAGER_USER_ID", child."PARENT_ORGSTR_ID"
                                    FROM "ORGANIZATION_STRUCTURE" child
                                    INNER JOIN org_tree parent ON parent."ORGSTR_ID" = child."PARENT_ORGSTR_ID"
                                )
                                SELECT "ORGSTR_ID", "ORGSTR_NAME", "ORGANIZATION_STATUS", "MANAGER_USER_ID"
                                FROM org_tree) AS "OS"`,
                                    [userId],
                                ),
                            )
                            .leftJoin(
                                "USER_ORGANIZATION_STRUCTURE as UOS",
                                "OS.ORGSTR_ID",
                                "UOS.ORGSTR_ID",
                            )
                            .leftJoin("USERS as U", "U.USER_ID", "UOS.USER_ID")
                            .where("OS.ORGANIZATION_STATUS", "A")
                            .whereNot("U.USER_ID", userId); // Not me
                    });

                wrapAlias = "WRAP";
                conn = this.connection.select().from({ [wrapAlias]: select });
                break;
            default:
                throw new UserException(
                    "Invalid hiearchy type.",
                    "INVALID_HIEARCHY_TYPE",
                );
        }

        const userAlias = wrapAlias || "U";
        const orgAlias = wrapAlias || "OS";

        // Apply role constraint
        if (roleIdConstraint) {
            conn = conn.leftJoin(
                "USER_ROLES as UR",
                `${userAlias}.USER_ID`,
                "UR.USER_ID",
            );
            conn = conn.where("UR.ROLE_ID", roleIdConstraint);
        }

        // Apply orgstr constraint.
        if (orgstrIdConstraint) {
            conn = conn.where(`${orgAlias}.ORGSTR_ID`, orgstrIdConstraint);
        }

        if (activeUsersOnly) {
            // Only active users and order by its name.
            conn = conn.where(`${userAlias}.USER_STATUS`, "A");
        }
        if (sort) {
            conn = conn.orderBy(
                this.connection.raw(
                    `"${userAlias}"."USER_LAST_NAME" ${globalThis.orm.db.concat()} "${userAlias}"."USER_FIRST_NAME"`,
                ),
            );
        }

        return { connection: conn };
    }

    /*

         const ASSESMENT_HIERARCHY_GUARANTOR = 'G';
         const ASSESMENT_HIERARCHY_CHILDREN = 'C';
         const ASSESMENT_HIERARCHY_DESCENDANTS = 'D';
         const ASSESMENT_HIERARCHY_PARENT = 'P';
         const ASSESMENT_HIERARCHY_ANCESTORS = 'A';
         const ASSESMENT_HIERARCHY_SIBLINGS = 'S';
         const ASSESMENT_HIERARCHY_ALL = 'L';

         * */
    async getUsersWithOrgStr(userId, userOrgstrId, task): Promise<User[]> {
        // Return unique rows.
        const { connection } = await this.getUsersWithOrgStrConnection(
            userId,
            userOrgstrId,
            task,
        );
        const users = await connection;

        return _.uniqBy(
            users,
            (row) =>
                // Get unique user chairs.
                `${row.USER_ID}-${row.ORGSTR_ID}`,
        );
    }

    getChairs() {
        return this.connection.select().from(function () {
            this.select([
                "U.USER_ID",
                "U.USER_NAME",
                "U.USER_FIRST_NAME",
                "U.USER_LAST_NAME",
                "OS.ORGSTR_ID",
                "OS.ORGSTR_NAME",
                "OS.MANAGER_USER_ID",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
            ])
                .from("USER_ORGANIZATION_STRUCTURE as UOS")
                .leftJoin("USERS as U", "UOS.USER_ID", "U.USER_ID")
                .leftJoin(
                    "ORGANIZATION_STRUCTURE as OS",
                    "UOS.ORGSTR_ID",
                    "OS.ORGSTR_ID",
                )
                .where("U.USER_STATUS", "A")
                .where("OS.ORGANIZATION_STATUS", "A")
                .union(function () {
                    this.select([
                        "U.USER_ID",
                        "U.USER_NAME",
                        "U.USER_FIRST_NAME",
                        "U.USER_LAST_NAME",
                        "OS.ORGSTR_ID",
                        "OS.ORGSTR_NAME",
                        "OS.MANAGER_USER_ID",
                        globalThis.database.raw(
                            `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                        ),
                        "U.USER_DISPLAY_NAME",
                    ])
                        .from("ORGANIZATION_STRUCTURE as OS")
                        .leftJoin(
                            "USERS as U",
                            "OS.MANAGER_USER_ID",
                            "U.USER_ID",
                        )
                        .where("U.USER_STATUS", "A")
                        .where("OS.ORGANIZATION_STATUS", "A");
                })
                .as("ignored_alias");
        });
    }

    /**
     * @param {User|Number} user
     * @param {Boolean} withNonActive
     * @returns {Promise<AuthorizedUser>}
     */
    async getUserInfo(user, withNonActive = false) {
        const userData = await this.connection
            .select(
                "U.USER_ID",
                "U.USER_NAME",
                "U.USER_LAST_NAME",
                "U.USER_FIRST_NAME",
                "U.USER_DISPLAY_NAME",
                "UR.ROLE_ID",
                "VOR.PATH",
                "OS.ORGSTR_ID",
                "OSTR.ORGSTR_NAME",
                "LANG.USRPAR_VALUE as USER_LANGUAGE",
                "DA.USRPAR_VALUE as DISABLE_ADMIN",
                "DF.USRPAR_VALUE as DATE_FORMAT",
                "U.LOGIN_COUNT",
            )
            .from(`USERS as U`)
            .leftJoin(`USER_ROLES as UR`, "U.USER_ID", "UR.USER_ID")
            .joinRaw(
                ` LEFT JOIN "USER_PARAMETERS" "LANG" on "UR"."USER_ID" = "LANG"."USER_ID" AND "LANG"."USRPAR_NAME" = 'CLIENT_LANGUAGE' `,
            )
            .joinRaw(
                ` LEFT JOIN "USER_PARAMETERS" "DA" on "UR"."USER_ID" = "DA"."USER_ID" AND "DA"."USRPAR_NAME" = 'DISABLE_ADMIN' `,
            )
            .joinRaw(
                ` LEFT JOIN "USER_PARAMETERS" "DF" on "UR"."USER_ID" = "DF"."USER_ID" AND "DF"."USRPAR_NAME" = 'DATE_FORMAT' `,
            )
            .leftJoin(
                `USER_ORGANIZATION_STRUCTURE as OS`,
                "OS.USER_ID",
                "U.USER_ID",
            )
            .leftJoin(
                `ORGANIZATION_STRUCTURE as OSTR`,
                "OS.ORGSTR_ID",
                "OSTR.ORGSTR_ID",
            )
            .leftJoin("V_ORG_STRUCT as VOR", "OS.ORGSTR_ID", "VOR.ORGSTR_ID")
            .where("U.USER_ID", user.USER_ID || user)
            .whereIn(
                "U.USER_STATUS",
                withNonActive
                    ? [
                          userConstants.STATUS_ACTIVE,
                          userConstants.STATUS_LOCKED,
                          userConstants.STATUS_DELETED,
                      ]
                    : [userConstants.STATUS_ACTIVE],
            );

        return await this.userInfo(userData);
    }

    async userInfo(data) {
        // Retreive roles
        let roles = [];
        let userData = {};

        // ORGANIZATION STRUCTURE INFO
        const organization = {};
        if (data && Array.isArray(data) && data.length > 0) {
            const orgPath = data[0].PATH;
            organization.ORGSTR_ID = data[0].ORGSTR_ID;
            organization.ORGSTR_NAME = data[0].ORGSTR_NAME;

            if (orgPath) {
                organization.ORGSTR_PATH = orgPath.split("/").slice(1);
            }

            // ROLES INFO
            roles.push(...data.map((item) => item.ROLE_ID));

            userData = {
                USER_NAME: data[0].USER_NAME,
                USER_LAST_NAME: data[0].USER_LAST_NAME,
                USER_FIRST_NAME: data[0].USER_FIRST_NAME,
                USER_DISPLAY_NAME: data[0].USER_DISPLAY_NAME,
                VIEW_ONLY: data[0].VIEW_ONLY
                    ? data[0].VIEW_ONLY === "Y"
                    : false, // View only only if vicing and is set to Y.
                USER_ID: data[0].USER_ID,
                LANGUAGE: data[0].USER_LANGUAGE,
                DATE_FORMAT: data[0].DATE_FORMAT || "L",
                USER_ID_VICED: data[0].USER_ID_VICED,
                DISABLE_ADMIN: data[0].DISABLE_ADMIN,
                LOGIN_COUNT: data[0].LOGIN_COUNT,
                USER_SYSTEM: data[0].USER_SYSTEM,
            };
        } else {
            throw new AuthException("Can not store user. Is user active?");
        }

        if (organization.ORGSTR_PATH) {
            const orgRepo: OrganizationStructureRepository =
                globalThis.orm.repo("organizationStructure");
            let logoUrl = null;
            for (const path of organization.ORGSTR_PATH) {
                const org = await orgRepo.get(path);
                if (org.LOGO_URL) {
                    logoUrl = org.LOGO_URL;
                }
            }
            organization.LOGO_URL = logoUrl;
        }

        const primaryUserId = userData.USER_ID;
        let userId = userData.USER_ID;
        if (userData.USER_ID_VICED && userId !== userData.USER_ID_VICED) {
            userId = userData.USER_ID_VICED;

            roles = roles.filter((rid) => rid > 0 || rid === ROLES.ALL_USERS);
        }

        const outUser = {
            USER_NAME: userData.USER_NAME,
            USER_LAST_NAME: userData.USER_LAST_NAME,
            USER_FIRST_NAME: userData.USER_FIRST_NAME,
            USER_DISPLAY_NAME: userData.USER_DISPLAY_NAME,
            USER_ID: userId,
            LANGUAGE: userData.LANGUAGE || globalThis.dynamicConfig.defaultLang,
            DATE_FORMAT: userData.DATE_FORMAT || "L",
            PRIMARY_USER_ID: primaryUserId,
            ORGANIZATION: organization,
        };

        return new AuthorizedUser(outUser, roles);
    }

    /**
     *
     * @param {User} user
     * @returns {number}
     */
    async setDefaultChair(user) {
        if (!user || !user.USER_ID) {
            throw new InternalException(
                "Invalid user object. USER_ID is not defined.",
            );
        }

        const orgRepo: UserOrganizationStructureRepository =
            globalThis.orm.repo("userOrganizationStructure");
        const orgstrId = await orgRepo.getUserOrgstrId(user.USER_ID);
        user.setActualChair(orgstrId);
        return user;
    }

    getDirectManagerToUser(userId) {
        const sql = `
        WITH RECURSIVE org_tree AS (
            -- Anchor: Start with the given USER_ID
            SELECT os."MANAGER_USER_ID", os."ORGSTR_ID", os."PARENT_ORGSTR_ID"
            FROM "ORGANIZATION_STRUCTURE" os
            WHERE os."ORGSTR_ID" IN (
                SELECT uos."ORGSTR_ID"
                FROM "USER_ORGANIZATION_STRUCTURE" uos
                WHERE uos."USER_ID" = ?
            )

            UNION ALL

            -- Recursive: Traverse the org structure hierarchy
            SELECT child."MANAGER_USER_ID", child."ORGSTR_ID", child."PARENT_ORGSTR_ID"
            FROM "ORGANIZATION_STRUCTURE" child
            INNER JOIN org_tree parent ON parent."ORGSTR_ID" = child."PARENT_ORGSTR_ID"
        )
        SELECT uos."MANAGER_USER_ID"
        FROM org_tree uos
        LEFT JOIN "USERS" u ON u."USER_ID" = uos."MANAGER_USER_ID"
        WHERE uos."MANAGER_USER_ID" IS NOT NULL
        AND u."USER_STATUS" = 'A';
    `;

        const params = [userId];
        return globalThis.container.client.database
            .callKnexRaw(sql, params, this.connection)
            .then((managers) => {
                if (Array.isArray(managers) && managers.length > 0) {
                    return managers[0].MANAGER_USER_ID;
                }
                throw new UserException(`No manager for user_id = ${userId}`);
            });
    }

    /**
     * Returns all users with the same role
     *
     * @param {Role} roleEntity
     * @param {boolean} includeCompetenceRoles
     * @returns {Knex.Builder}
     */
    getUserListByRole(roleEntity, includeCompetenceRoles = true) {
        // TODO: Don't return user password?
        if (!roleEntity || !roleEntity.ROLE_ID) {
            throw new UserException(
                "roleEntity is not defined in UserRepository.getByRole",
            );
        }

        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("USER_ID", function () {
                this.select("USER_ID")
                    .from("USER_ROLES")
                    .where("ROLE_ID", roleEntity.ROLE_ID);

                if (!includeCompetenceRoles) {
                    this.whereNot("ROLE_SOURCE", ROLES.SOURCE_COMPETENCE);
                }
            });
    }

    /**
     * Returns all users in the same Organization Structure
     *
     * @param {OrganizationStructure} orgStrEntity
     * @param {Boolean} all
     * @returns {Knex.Builder}
     */
    getUserListByOrgStructure(orgStrEntity, all = false) {
        // TODO: Don't return user password?
        let conn = this.connection
            .select([
                "U.USER_ID",
                "U.USER_NAME",
                "U.USER_STATUS",
                "U.USER_EMAIL",
                "U.USER_FIRST_NAME",
                "U.USER_LAST_NAME",
                "U.USER_DISPLAY_NAME",
                "U.USER_TITLE_PREFIX",
                "U.USER_COMP_CODE",
                "U.USER_TITLE_SUFFIX",
            ])
            .from("USER_ORGANIZATION_STRUCTURE as UOS")
            .innerJoin("USERS as U", "U.USER_ID", "=", "UOS.USER_ID")
            .where("UOS.ORGSTR_ID", orgStrEntity.ORGSTR_ID);

        if (!all) {
            conn = conn.whereIn("U.USER_STATUS", [
                userConstants.STATUS_ACTIVE,
                userConstants.STATUS_LOCKED,
            ]);
        }
        return conn;
    }

    async getUserListByOrgStructureTree(orgStrEntity) {
        // TODO: Don't return user password?
        let usersList = [];

        const organizationRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        const organizations = await organizationRepo.getChildrenUnits(
            orgStrEntity.ORGSTR_ID,
        );
        for (const orgStr of organizations) {
            const users = await this.getUserListByOrgStructure(orgStr).orderBy(
                this.connection.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concat()} "U"."USER_FIRST_NAME"`,
                ),
            );

            // Concatenate new users into the usersList
            usersList = usersList.concat(users);
        }

        // Ensure unique users in the final list
        return _.uniq(usersList);
    }

    /**
     * Finds all Users where @attributeName = @attributeValue
     *
     * @param attributeName
     * @param attributeValue
     * @param userStatus
     * @returns {Knex.Builder}
     */
    getUserListByAttribute(
        attributeName,
        attributeValue,
        userStatus = [userConstants.STATUS_ACTIVE, userConstants.STATUS_LOCKED],
    ) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .andWhereRaw(
                `UPPER ("${attributeName.split(".").join('"."')}") LIKE UPPER ('${attributeValue}')`,
            );

        if (userStatus) {
            conn.whereIn(
                "USER_STATUS",
                Array.isArray(userStatus) ? userStatus : [userStatus],
            );
        }
        return conn;
    }

    getNonDeletedUsers(username) {
        const coll = this.getCollection();
        coll.knex.whereRaw(
            `${globalThis.orm.db.upper("USER_NAME", true)} = ${globalThis.orm.db.upper("?")} and "USER_STATUS" <> ?`,
            [username, "D"],
        ); // ignore deleted users (don't report any error)
        return coll;
    }

    /**
     * Finds all Users in OrganizationStructure
     *
     * @param orgstrId
     * @returns {Knex.Builder}
     */
    getForOrganizationStructure(orgstrId) {
        return this.connection
            .select()
            .from("USER_ORGANIZATION_STRUCTURE as UOS")
            .innerJoin("USERS as U", "U.USER_ID", "=", "UOS.USER_ID")
            .where("UOS.ORGSTR_ID", orgstrId);
    }

    async store(entity, force?) {
        // It's impossible to make an 'Async' setter, handle hashing here
        const { USER_PASSWORD: userPassword } = entity;
        // User may not have a password set
        if (userPassword) {
            const isHashed =
                userPassword.startsWith("$") && userPassword.length === 60;
            const isEmpty = userPassword === "EMPTY";

            // validate password (what if user has 60 char password with $ at start??)
            if (!isHashed) {
                this.validatePassword(userPassword);
            }

            if (!isHashed && !isEmpty) {
                entity.USER_PASSWORD = await Bcrypt.hash(
                    userPassword + globalThis.dynamicConfig.security.pepper,
                    globalThis.dynamicConfig.security.saltRounds,
                );
            }
        }

        if (
            !entity.USER_ID &&
            !entity.USER_DISPLAY_NAME &&
            entity.USER_LAST_NAME &&
            entity.USER_FIRST_NAME
        ) {
            entity.USER_DISPLAY_NAME =
                entity.USER_LAST_NAME + " " + entity.USER_FIRST_NAME;
        }

        // USER_SYSTEM cannot be null
        if (entity.USER_SYSTEM === undefined) {
            entity.USER_SYSTEM = false;
        }
        return await super.store(entity, force);
    }

    async updateUserLock(userId) {
        const user = await this.get(userId);

        // Switch user_status.
        if (user.USER_STATUS === userConstants.STATUS_LOCKED) {
            user.USER_STATUS = userConstants.STATUS_ACTIVE;
        } else if (user.USER_STATUS === userConstants.STATUS_ACTIVE) {
            user.USER_STATUS = userConstants.STATUS_LOCKED;
        } else {
            throw new InternalException(
                "Wrong user state. Can not update user lock because it has invalid status.",
            );
        }

        return await this.store(user).then(async () => {
            if (
                user.USER_STATUS === userConstants.STATUS_ACTIVE &&
                user.USER_EMAIL
            ) {
                const lang = globalThis.dynamicConfig.defaultLang;

                const userData = {
                    user_activated: user.USER_NAME,
                    app_url: globalThis.dynamicConfig.frontendUrl,
                };

                const mailOptionsParams: MailOptionsParams = {
                    addresses: user.USER_EMAIL,
                    subject: `${globalThis.__({ phrase: "userActivation", locale: lang })}`,

                    bccOnMoreAddresses: true,
                    ignoreError: true,
                };

                try {
                    return await globalThis.routerMail.sendEmailViaClient(
                        "userActivation",
                        mailOptionsParams,
                        userData,
                        lang,
                    );
                } catch (err) {
                    globalThis.tasLogger.error(err.message, {
                        err,
                    });
                    throw err;
                }
            }
        });
    }

    getAllProcessUserTaskUsers(iprocId) {
        const repo = this.connection
            .select()
            .from(function () {
                this.distinct("IPROC_ID", "ITASK_USER_ID")
                    .select()
                    .from("INSTANCE_TASKS")
                    .whereNotNull("ITASK_USER_ID")
                    .whereIn("ITASK_TYPE", ["S", "I"])
                    .union(function () {
                        this.distinct(
                            "ITH.IPROC_ID",
                            "ITH.ITASKH_FINISHED_BY_USER_ID",
                        )
                            .select()
                            .from("INSTANCE_TASK_HISTORY as ITH")
                            .leftJoin(
                                "INSTANCE_TASKS as IT",
                                "ITH.ITASK_ID",
                                "IT.ITASK_ID",
                            )
                            .whereNotNull("ITH.ITASKH_FINISHED_BY_USER_ID")
                            .whereIn("IT.ITASK_TYPE", ["S", "I"]);
                    })
                    .union(function () {
                        this.distinct("IPROC_ID", "IPROC_INST_OWNER_USER_ID")
                            .select()
                            .from("INSTANCE_PROCESSES")
                            .whereNotNull("IPROC_INST_OWNER_USER_ID");
                    })
                    .as("ignored_alias");
            })
            .where("IPROC_ID", iprocId)
            .join("USERS as U", "ITASK_USER_ID", "U.USER_ID");

        return globalThis.orm.collection("User", repo);
    }

    getUserIdByEmail(emails) {
        return this.connection
            .pluck("USER_ID")
            .from(this.tableName)
            .whereIn("USER_EMAIL", Array.isArray(emails) ? emails : [emails]);
    }

    findOneByAttribute(attributeName, attributeValue, caseSensitive = true) {
        let conn = this.connection.select().from(this.tableName);

        if (caseSensitive) {
            conn = conn.where(attributeName, attributeValue);
        } else {
            conn = conn.whereRaw(
                globalThis.database.raw(
                    `LOWER("${attributeName}") = ?`,
                    attributeValue.toLowerCase(),
                ),
            );
        }

        const collection = globalThis.orm.collection("User", conn);
        return collection.collectAll().then((rows) => {
            if (Array.isArray(rows) && rows.length > 0) {
                return rows[0];
            }
            return null;
        });
    }

    async sync(identUser, identOrg, items, inheritManagers) {
        if (!items || !Array.isArray(items)) {
            throw new UserException("Invalid param 'items'.");
        }
        if (!identOrg) {
            throw new UserException("Invalid param 'identOrg'");
        }
        if (!identUser) {
            throw new UserException("Invalid param 'identUser'");
        }

        const orgRepo: OrganizationStructureRepository = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );
        const userRepo = globalThis.orm.repo("user", this.connection);

        if (inheritManagers) {
            await this.connection
                .select()
                .from("ORGANIZATION_STRUCTURE")
                .where("ORGSTR_ID", ">", 0)
                .whereNot("ORGSTR_NAME", "Root")
                .update({ MANAGER_USER_ID: null });
        }

        const sync = new LDAPSync({ ldap: {} });
        sync.setConnection(this.connection);
        for (const item of items) {
            if (!item.id) {
                globalThis.tasLogger.warning(
                    `Invalid id ${item.name}. Skipping.`,
                    {
                        entity: item,
                    },
                );
                continue;
            }
            // Find user
            const userEntity = await userRepo.findOneByAttribute(
                identUser,
                item.id,
                false,
            );
            // User not found. Error !
            if (userEntity === null) {
                // throw new UserException(`User '${item.id}' not found in db.`, {item: item});
                globalThis.tasLogger.warning(
                    `User '${item.id}' not found in db.`,
                    {
                        item,
                    },
                );
                continue;
            }

            // Unlock user
            if (userEntity.USER_STATUS === userConstants.STATUS_LOCKED) {
                userEntity.USER_STATUS = userConstants.STATUS_ACTIVE;
                globalThis.tasLogger.info(
                    `Unlocking '${userEntity.USER_NAME}'`,
                );
                await userRepo.store(userEntity);
            }

            // Find organization
            const usersOrganization = await orgRepo.findOneByAttribute(
                identOrg,
                item.orgId,
            );

            // Organization not found. Error !
            if (usersOrganization === null) {
                globalThis.tasLogger.warning(
                    `Could not find organization ${item.orgId} for user '${item.id}'`,
                );
                continue;
            }

            // Set organization for user.
            await userRepo.setOrganization(
                userEntity.USER_ID,
                usersOrganization.ORGSTR_ID,
            );

            // Set organization manager
            if (Array.isArray(item.managerOrgId)) {
                for (const managerOrgId of item.managerOrgId) {
                    // Find organization
                    const managerOrganization =
                        await orgRepo.findOneByAttribute(
                            identOrg,
                            managerOrgId,
                        );
                    if (managerOrganization === null) {
                        throw new UserException(
                            `Could not find manager organization ${item.orgId} for user '${item.id}'`,
                        );
                    }

                    // Set organization for user.
                    await orgRepo.setManager(
                        managerOrganization.ORGSTR_ID,
                        userEntity.USER_ID,
                    );
                }
            }
        }

        // Lock users
        // const allUsers = _.map(await this.getCollection(['USER_ID']).fetchAll(), 'USER_ID');
        // const lockUsers = _.difference(allUsers, noLock);
        // globalThis.tasLogger.info('Locking users.', {lockUsers: lockUsers});
        // await userRepo.lockUsers(lockUsers);

        if (inheritManagers) {
            await orgRepo.inheritManagers();
        }

        return true;
    }

    async setDefaults(userId) {
        // Create dashboard if new user !
        const orgRepo = globalThis.orm.repo("organization", this.connection);
        const organization = await orgRepo.get(1);
        const dashboard = organization.ORG_DASHBOARD;

        // Default dashboard
        const usrParRepo = globalThis.orm.repo(
            "userParameter",
            this.connection,
        );
        await usrParRepo.updateGlobalSettings(
            [
                {
                    USRPAR_NAME: "DASHBOARD",
                    USRPAR_VALUE: dashboard,
                },
                {
                    USRPAR_NAME: "DMS_COLUMNS",
                    USRPAR_VALUE: null,
                },
            ],
            userId,
        );

        /**
         * t3b-754 Tas4 - zapnutí e-mailů - nový uživatel (nový úkol, přehled úkolů)
         */
        const userParameterRepo = globalThis.orm.repo(
            "userParameter",
            this.connection,
        );
        const enableNewTaskNotification = {
            USRPAR_NAME: "MAIL_PROMPTLY",
            USRPAR_VALUE: globalThis.dynamicConfig.mail
                .newTaskNotificationEnabledByDefault
                ? USER_PARAMETERS.ENABLED_YES
                : USER_PARAMETERS.ENABLED_NO,
        };
        const enableTaskOverViewNotification = {
            USRPAR_NAME: "MAIL_TOTAL",
            USRPAR_VALUE: globalThis.dynamicConfig.mail
                .taskOverviewNofificationEnabledByDefault
                ? USER_PARAMETERS.ENABLED_YES
                : USER_PARAMETERS.ENABLED_NO,
        };
        const enableTaskToPullNotification = {
            USRPAR_NAME: "MAIL_PULL",
            USRPAR_VALUE: globalThis.dynamicConfig.mail
                .taskToPullNotificationEnabledByDefault
                ? USER_PARAMETERS.ENABLED_YES
                : USER_PARAMETERS.ENABLED_NO,
        };
        await userParameterRepo.updateGlobalSettings(
            [
                enableNewTaskNotification,
                enableTaskOverViewNotification,
                enableTaskToPullNotification,
            ],
            userId,
        );

        // Check $AllUser role.
        const userRoles = await globalThis.orm
            .repo("role", this.connection)
            .getForUser(userId, ["RO.ROLE_ID"])
            .collectAll();

        if (userRoles.length === 0) {
            await this.assignRoles(
                userId,
                [
                    /* empty = only default roles */
                ],
                null,
                null,
                ROLES.SOURCE_EXTERNAL,
            );
        }

        // Check user organization.
        const userOrgs = await this.connection
            .select("ORGSTR_ID")
            .from("USER_ORGANIZATION_STRUCTURE")
            .where("USER_ID", userId);
        if (userOrgs.length === 0) {
            return await this.setOrganization(
                userId,
                organizationStructureConstants.UNASSIGNED,
            );
        }

        return userId;
    }

    async verifyPassword(id, password) {
        const collection = this.getCollection();
        collection.knex.from(this.tableName);
        collection.knex.where("USER_ID", id);
        const repoUser = await collection.collectOne();
        return repoUser.verifyPassword(password);
    }

    validatePassword(password) {
        const schema = new PasswordValidator();

        const rules = globalThis.dynamicConfig.security.passwordValidations;
        try {
            _.forEach(rules, (value, rule) => {
                if (value) {
                    return schema[rule](value);
                }
            });
        } catch (_err) {
            throw new InternalException(
                "Password validation rules are not valid",
            );
        }

        if (!schema.validate(password)) {
            throw new UserException(
                "Password is not secure. Use stronger (longer/mixed) password.",
                "INVALID_PASSWORD",
            );
        }
    }

    checkDb() {
        return globalThis.database.transaction(async (trx) => {
            if (globalThis.dynamicConfig.db.client === "mssql") {
                await trx.raw(
                    `SELECT "LOGIN_COUNT" FROM "USERS" WITH (UPDLOCK, HOLDLOCK)`,
                );
            } else {
                await trx.raw(`SELECT "LOGIN_COUNT" FROM "USERS" FOR UPDATE`);
            }
        });
    }

    getByUserRestrictions(authUser, restriction, status) {
        const columns = [
            this.connection.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
            "U.USER_LAST_NAME",
            "U.USER_FIRST_NAME",
            "U.USER_ID",
            "U.USER_STATUS",
        ];

        const conObj = this.getUsersWithOrgStrConnection(
            authUser.USER_ID,
            authUser.ORGANIZATION.ORGSTR_ID,
            {
                ITASK_ASSESMENT_HIERARCHY: restriction,
            },
            false,
            false,
            columns,
        );

        let conn = conObj.connection;
        const userAlias =
            restriction === userConstants.RELATIONSHIP_SUBORDINATES_NO_CHAIR
                ? "WRAP"
                : "U";
        conn = conn.whereIn(
            `${userAlias}.USER_STATUS`,
            Array.isArray(status) ? status : [status],
        );

        return globalThis.orm.collection("User", conn);
    }

    hasRightsToWholeList(authUser) {
        const { userRestrictions } = globalThis.dynamicConfig.security;
        // show - All (A), Colleagues only (S), Direct subordinates (CA), All subordinates (DA)
        const userRestrictionsArr = [
            userConstants.RELATIONSHIP_COLLEAGUES,
            userConstants.RELATIONSHIP_DIRECT_SUBORDINATES_NO_CHAIR,
            userConstants.RELATIONSHIP_SUBORDINATES_NO_CHAIR,
        ];
        let hasRights = userRestrictionsArr.indexOf(userRestrictions) === -1;

        if (
            authUser.isAdministrator() ||
            authUser.isGlobalSupervisor() ||
            authUser.isInspector()
        ) {
            hasRights = true;
        }

        return hasRights;
    }

    async getByEnumerationRights(
        authUser,
        userStatus,
        ignoreUserRestrictions = false,
    ) {
        const hasRightsToWholeList = this.hasRightsToWholeList(authUser);
        let collection;

        if (hasRightsToWholeList || ignoreUserRestrictions) {
            collection = this.getByStatus(userStatus);
        } else {
            const { userRestrictions } = globalThis.dynamicConfig.security;
            collection = await this.getByUserRestrictions(
                authUser,
                userRestrictions,
                userStatus,
            );
        }

        return collection;
    }

    async getPasswordExpirationStatus(userId) {
        const { expirationTime, expirationNotificationPeriod } =
            globalThis.dynamicConfig.security.passwordExpiration;
        const col = await this.getById(userId);
        const user = await col.collectOne();

        if (!user) {
            throw new UserException("User not found", "USER_NOT_FOUND", {
                user_id: userId,
            });
        }

        if (!expirationTime) {
            return userConstants.PASSWORD_EXPIRATION_NULL; // 'CONFIG set to NEVER';
        }

        const { USER_PASSWORD_LAST_CHANGE: lastChange } = user;

        if (lastChange === null) {
            user.USER_PASSWORD_LAST_CHANGE = new Date();
            await this.store(user);
            return userConstants.PASSWORD_WAS_NULL; // 'NULL to NEW DATE';
        }

        const today = new Date();
        const lastChangeDate = new Date(lastChange);
        const passwordAge = (today - lastChangeDate) / 1000 / 60 / 60 / 24;

        const userInfo =
            await globalThis.container.service.auth.getUserInfoFromId(userId);
        const isAdmin =
            userInfo.isAdministrator() || userInfo.isSuperAdministrator();

        if (expirationTime <= passwordAge && !isAdmin) {
            return userConstants.PASSWORD_EXPIRED; // 'PASSWORD is EXPIRED';
        }

        if (
            expirationNotificationPeriod !== 0 &&
            expirationTime - expirationNotificationPeriod <= passwordAge
        ) {
            return userConstants.PASSWORD_EXPIRATION_NOTIF; // 'PASSWORD will EXPIRE';
        }

        return userConstants.PASSWORD_OK;
    }

    async setNewPassword(userId, password) {
        const entity = this.getEntity();
        entity.USER_ID = userId;
        entity.password = password;
        entity.USER_PASSWORD_LAST_CHANGE = new Date();
        return await this.store(entity);
    }

    getByPasswordAge(minDays, maxDays, filterNoMail, filterAdmin) {
        const dateUpperThreshold = new Date(
            Date.now() - minDays * 24 * 60 * 60 * 1000,
        );
        const dateLowerThreshold = new Date(
            Date.now() - maxDays * 24 * 60 * 60 * 1000,
        );

        let conn = this.connection
            .select([
                "USER_ID",
                "USER_EMAIL",
                "USER_PASSWORD_LAST_CHANGE",
                "LANG.USRPAR_VALUE as USER_LANGUAGE",
                "DF.USRPAR_VALUE as DATE_FORMAT",
            ])
            .from("USERS")
            .joinRaw(
                " LEFT JOIN USER_PARAMETERS LANG on UR.USER_ID = LANG.USER_ID AND LANG.USRPAR_NAME = 'CLIENT_LANGUAGE' ",
            )
            .joinRaw(
                " LEFT JOIN USER_PARAMETERS DF on UR.USER_ID = DF.USER_ID AND DF.USRPAR_NAME = 'DATE_FORMAT' ",
            )
            .where("USER_STATUS", userConstants.STATUS_ACTIVE)
            .whereNotNull("USER_PASSWORD_LAST_CHANGE")
            .where("USER_PASSWORD_LAST_CHANGE", "<", dateUpperThreshold);

        if (filterAdmin) {
            conn = conn.whereNotIn("USER_ID", (whereInBuilder) => {
                whereInBuilder
                    .distinct("USER_ID")
                    .from("USER_ROLES")
                    .whereIn("ROLE_ID", [
                        ROLES.ADMINISTRATOR,
                        ROLES.SUPER_ADMINISTRATOR,
                    ]);
            });
        }

        if (filterNoMail) {
            conn = conn.whereNotNull("USER_EMAIL");
        }

        if (maxDays !== 0) {
            conn = conn.where(
                "USER_PASSWORD_LAST_CHANGE",
                ">",
                dateLowerThreshold,
            );
        }

        return globalThis.orm.collection("User", conn);
    }

    async initUserPasswordLastChange() {
        const date = new Date();

        return await this.connection
            .select()
            .from(this.tableName)
            .where("USER_STATUS", userConstants.STATUS_ACTIVE)
            .where("USER_PASSWORD_LAST_CHANGE", null)
            .update("USER_PASSWORD_LAST_CHANGE", date);
    }

    getUsersForExport() {
        return this.connection
            .select(
                "U.USER_ID",
                "U.USER_NAME",
                "U.USER_FIRST_NAME",
                "U.USER_LAST_NAME",
                "U.USER_DISPLAY_NAME",
                "U.USER_EMAIL",
                "U.USER_STATUS",
                "U.EXTERNAL_ID",
                "OS.ORGSTR_NAME",
                "OS.ORGSTR_ID",
                "OS.EXTERNAL_ID AS ORGSTR_EXTERNAL_ID",
            )
            .from("USERS as U")
            .leftJoin(
                "USER_ORGANIZATION_STRUCTURE as UOS",
                "U.USER_ID",
                "UOS.USER_ID",
            )
            .whereNot("U.USER_STATUS", "D")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "UOS.ORGSTR_ID",
                "OS.ORGSTR_ID",
            );
    }

    async getByAttribute(attrName, attrValue, singleResult = true) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .whereIn("USER_STATUS", [
                userConstants.STATUS_ACTIVE,
                userConstants.STATUS_LOCKED,
            ])
            .where(attrName.toUpperCase(), attrValue);
        const collection = globalThis.orm.collection("User", conn);
        const rows = await collection.collectAll();
        if (singleResult) {
            return rows.length > 0 ? rows[0] : null;
        }
        return rows;
    }

    getUsersByParameter(
        usrparName,
        usrparValue,
        userStatus = [userConstants.STATUS_ACTIVE, userConstants.STATUS_LOCKED],
    ) {
        const conn = this.connection
            .select("U.*")
            .from("USER_PARAMETERS as UP")
            .leftJoin("USERS as U", "U.USER_ID", "UP.USER_ID")
            .where("USRPAR_NAME", usrparName)
            .where("USRPAR_VALUE", usrparValue);

        if (userStatus) {
            conn.whereIn(
                "USER_STATUS",
                Array.isArray(userStatus) ? userStatus : [userStatus],
            );
        }

        return conn;
    }

    async hasAdminRole(userId) {
        return !!(
            await this.connection
                .pluck("ROLE_ID")
                .from("USER_ROLES")
                .where("USER_ID", userId)
                .where("ROLE_ID", ROLES.ADMINISTRATOR)
        ).length;
    }

    async getUnassignedUsersByProperty(
        columnName: string,
        value: string,
        tableName: string,
        authUser,
        userStatus: string,
        ignoreUserRestrictions = false,
        addUserIds: string[] = [],
    ) {
        const collection = await this.getByEnumerationRights(
            authUser,
            userStatus,
            ignoreUserRestrictions,
        );

        collection.knex.whereNotIn("U.USER_ID", (builder) => {
            builder.select("USER_ID").from(tableName).where(columnName, value);
        });

        if (addUserIds.length > 0) {
            const addedUsersColl = await this.getByEnumerationRights(
                authUser,
                userStatus,
                ignoreUserRestrictions,
            );

            addedUsersColl.knex.whereIn("USER_ID", addUserIds);
            collection.union(addedUsersColl);
        }

        return collection;
    }

    async getUnassignedUsersByRole(
        roleId,
        authUser,
        userStatus,
        ignoreUserRestrictions = false,
        addUserIds: string[] = [],
    ) {
        return await this.getUnassignedUsersByProperty(
            "ROLE_ID",
            roleId,
            "USER_ROLES",
            authUser,
            userStatus,
            ignoreUserRestrictions,
            addUserIds,
        );
    }

    async getUnassignedUsersByOrgStr(
        orgStrId,
        authUser,
        userStatus,
        ignoreUserRestrictions = false,
        addUserIds: string[] = [],
    ) {
        return await this.getUnassignedUsersByProperty(
            "ORGSTR_ID",
            orgStrId,
            "USER_ORGANIZATION_STRUCTURE",
            authUser,
            userStatus,
            ignoreUserRestrictions,
            addUserIds,
        );
    }
}
