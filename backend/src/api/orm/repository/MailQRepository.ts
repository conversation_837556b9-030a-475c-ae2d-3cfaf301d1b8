// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { Task as InstanceTask } from "../entity/Task";
import { Process as InstanceProcess } from "../entity/Process";
import { MailQ } from "../entity/MailQ";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

import { MailOptionsParams } from "../../../client/mail/BaseMailClient";
import * as userParamConst from "../entity/const/userParameterConsts";
import * as processConst from "../entity/const/processConst";
import { LogCategory } from "../../../utils/logger/logConsts";

export class MailQRepository extends BaseRepository<MailQ> {
    meta() {
        return {
            tableName: "MAIL_QUEUE",
            entityName: "MailQ",
            entity: () => new MailQ(),
            archived: true,
            archParams: {
                subQueryTable: "INSTANCE_TASKS",
                subQueryColumn: "ITASK_ID",
            },
        };
    }

    /*
     * Find MailQ entries in the DB that match 'Active' status criteria
     * extract information and send the emails, then change their status
     */
    async sendDelayedMails(status) {
        const dateNow = new Date();

        const data = await this.connection
            .select(["USER_ID", "MAQU_BODY_DATA", "MAQU_META_DATA", "MAQU_ID"])
            .from(this.tableName)
            .leftJoin(
                "INSTANCE_TASKS",
                "INSTANCE_TASKS.ITASK_ID",
                "MAIL_QUEUE.ITASK_ID",
            )
            .leftJoin(
                "INSTANCE_PROCESSES",
                "INSTANCE_PROCESSES.IPROC_ID",
                "INSTANCE_TASKS.IPROC_ID",
            )
            .where("MAIL_QUEUE.MAQU_STATUS", status)
            .where("MAIL_QUEUE.MAQU_SEND_ON", "<", dateNow)
            .whereIn("INSTANCE_TASKS.ITASK_STATUS", [
                InstanceTask.consts.STATUS_ACTIVE,
                InstanceTask.consts.STATUS_PULL,
                null,
            ])
            .whereIn("INSTANCE_PROCESSES.IPROC_STATUS", [
                InstanceProcess.consts.STATUS_ACTIVE,
                null,
            ]);
        for (const mailQ of data) {
            const userRepo = globalThis.orm.repo("user", this.connection);
            const userParamRepo = globalThis.orm.repo(
                "userParameter",
                this.connection,
            );
            const userViceRepo = globalThis.orm.repo(
                "userVice",
                this.connection,
            );
            const metaData = JSON.parse(mailQ.MAQU_META_DATA);

            // @t3b-1145 Neodesílají se TaskMailCron maily kvůli chybě, že nemohl najít uživatele s id ''
            let user = null;

            // Check if there exists an enabled Vice

            await globalThis.tasLogger.runTask(async () => {
                globalThis.tasLogger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_MAIL,
                );
                try {
                    user = await userRepo.get(mailQ.USER_ID);
                } catch (err) {
                    // Could not find any user
                    const meta = {
                        ticket: "t3b-1145",
                        reason: "Could not find any User with the given id.",
                        userId: mailQ.USER_ID,
                        subject: metaData.subjectPhrase,
                        err,
                    };
                    globalThis.tasLogger.warning(err.message, {
                        meta,
                    });
                    const entity = await this.get(mailQ.MAQU_ID);
                    entity.MAQU_STATUS = MailQ.consts.MQ_FAILED;
                    await this.store(entity);
                    return;
                }
            });

            if (!user.USER_EMAIL) {
                globalThis.tasLogger.error(
                    `User with id ${mailQ.USER_ID} and display name ${user.USER_DISPLAY_NAME} dont have email set`,
                    {
                        mailQ,
                        user,
                    },
                );
                continue;
            }

            const vice = await userViceRepo
                .getUsersViced(mailQ.USER_ID, true)
                .collectOne();

            const targets = [
                {
                    USER_ID: mailQ.USER_ID,
                    USER_EMAIL: user.USER_EMAIL,
                    USER_FULL_NAME: user.getFullName(),
                    USER_DISPLAY_NAME: user.USER_DISPLAY_NAME,
                },
            ];
            if (vice) {
                targets.push({
                    USER_ID: vice.raw.USER_VICE_ID,
                    USER_EMAIL: vice.raw.USER_VICE_EMAIL,
                    USER_FULL_NAME: vice.raw.USER_VICE_FULL_NAME,
                    USER_DISPLAY_NAME: vice.raw.USER_VICE_DISPLAY_NAME,
                    UV_ID: vice.UV_ID,
                });
            }

            // Send to user and vice separately (could be different language)
            for (const target of targets) {
                // Add 'uv-id' if there is active vice
                const bodyData = JSON.parse(mailQ.MAQU_BODY_DATA);

                if (target.UV_ID) {
                    bodyData.itask_id = `${bodyData.itask_id}?uv-id=${target.UV_ID}`;
                    bodyData.iproc_id = `${bodyData.iproc_id}?uv-id=${target.UV_ID}`;
                    bodyData.viceUser = vice.raw.USER_VICE_DISPLAY_NAME;
                }

                // Is mail notification enabled?
                const solverUserMailSettings = await globalThis.orm
                    .repo("userParameter", this.connection)
                    .getUserEnabledMailSettings(target.USER_ID);
                const newTaskNotificationEnabled = _.find(
                    solverUserMailSettings,
                    {
                        USRPAR_NAME: userParamConst.SEND_NEW_TASK_NOTIFICATION,
                    },
                );

                // Check language settings and render
                const languageParameter = await userParamRepo.getUserParameter(
                    target.USER_ID,
                    userParamConst.LANGUAGE_CLIENT,
                );
                const userLanguage = languageParameter.length
                    ? languageParameter[0].USRPAR_VALUE
                    : globalThis.dynamicConfig.defaultLang;
                const renderedSubject = `${globalThis.__({ phrase: metaData.subjectPhrase, locale: userLanguage })} ${metaData.subject}`;

                const dateFormatParam = await userParamRepo.getUserParameter(
                    target.USER_ID,
                    "DATE_FORMAT",
                );
                const dateFormat = dateFormatParam.length
                    ? dateFormatParam[0].USRPAR_VALUE
                    : "L";

                // send firebase notification
                const firebaseTokens = await globalThis.orm
                    .repo("registeredMobileDevice")
                    .getAllByAttrAndIncreaseBadgeCount(target.USER_ID);

                await globalThis.container.client.firebase.fcm.sendNewITaskNotification(
                    firebaseTokens,
                    bodyData,
                    renderedSubject,
                    metaData,
                    userLanguage,
                );

                if (
                    !newTaskNotificationEnabled ||
                    newTaskNotificationEnabled.value ===
                        userParamConst.ENABLED_NO
                ) {
                    // Notification disabled, don't send mail
                    continue;
                }

                const mailOptionsParams: MailOptionsParams = {
                    addresses: [
                        `"${target.USER_DISPLAY_NAME}" <${target.USER_EMAIL}>`,
                    ],
                    subject: renderedSubject,
                    bccOnMoreAddresses: true,
                    ignoreError: true,
                };

                await globalThis.tasLogger.runTask(async () => {
                    globalThis.tasLogger.setContextProperty(
                        "category",
                        LogCategory.CATEGORY_MAIL,
                    );
                    try {
                        await globalThis.routerMail.sendEmailViaClient(
                            metaData.templatePhrase,
                            mailOptionsParams,
                            bodyData,
                            userLanguage,
                            dateFormat,
                        );

                        const entity = await this.get(mailQ.MAQU_ID);
                        entity.MAQU_STATUS = MailQ.consts.MQ_SENT;
                        await this.store(entity);
                    } catch (err) {
                        const meta = {
                            err,
                            address: target.USER_EMAIL,
                            subject: renderedSubject,
                        };

                        globalThis.tasLogger.error(err.message, {
                            meta,
                            category: LogCategory.CATEGORY_MAIL,
                        });

                        const entity = await this.get(mailQ.MAQU_ID);
                        entity.MAQU_STATUS = MailQ.consts.MQ_FAILED;
                        await this.store(entity);

                        throw err;
                    }
                });
            }
        }

        await this.expireMails(dateNow);
    }

    /*
     * Create MailQ entries in the database
     * these will later be sent by this.sendDelayedMails() method
     */
    generateTemplateMail(user, templateName, mailData, templateData) {
        // @t3b-611 V DB se ukládají celé maily
        mailData.MAQU_BODY_DATA = JSON.stringify(templateData);
        mailData.MAQU_META_DATA = JSON.stringify({
            subject: mailData.MAQU_SUBJECT.text,
            subjectPhrase: mailData.MAQU_SUBJECT.phrase,
            templatePhrase: templateName,
        });
        const entity = this.getEntity();
        const attrs = entity.attributes();
        const attrKeys = Object.keys(attrs);

        attrKeys.forEach((key) => {
            if (typeof mailData[key] !== "undefined" && key !== "MAQU_ID") {
                entity[key] = mailData[key];
            }
        });

        // Always generate template mail as 'A'ctive
        entity.MAQU_STATUS = MailQ.consts.MQ_ACTIVE;
        entity.USER_ID = user.USER_ID;

        const now = new Date();
        entity.MAQU_SEND_ON = new Date(
            now.getTime() + globalThis.dynamicConfig.mail.promptlyDelay * 60000,
        );

        return this.store(entity)
            .then((id) => {
                if (id === null) {
                    throw new Error("Creation of MAILQ failed");
                }
                return this.invalidateMails(entity);
            })
            .catch((err) => {
                throw new InternalException(err);
            });
    }

    async invalidateMails(entity) {
        // Only one email notification per ITASK, invalidate other, not yet sent notifications
        const mailsToInvalidate = await this.connection
            .pluck("MAQU_ID")
            .from(this.tableName)
            .where("ITASK_ID", entity.ITASK_ID)
            .where("MAQU_STATUS", MailQ.consts.MQ_ACTIVE)
            .whereNot("MAQU_ID", entity.id);

        for (const mailId of mailsToInvalidate) {
            await this.connection
                .from(this.tableName)
                .where("MAQU_ID", mailId)
                .update("MAQU_STATUS", MailQ.consts.MQ_NOT_SENT);
        }

        // return this.connection.select()
        //     .from(this.tableName)
        //     .where('ITASK_ID', entity.ITASK_ID)
        //     .where('MAQU_STATUS', MailQ.consts.MQ_ACTIVE)
        //     .whereNot('MAQU_ID', entity.id)
        //     .update('MAQU_STATUS', MailQ.consts.MQ_NOT_SENT);
    }

    async expireMails(date) {
        // Expire mails after sending all delayed
        const mailsToExpire = await this.connection
            .pluck("MAQU_ID")
            .from(this.tableName)
            .where("MAQU_SEND_ON", "<", date)
            .where("MAQU_STATUS", MailQ.consts.MQ_ACTIVE);

        for (const mailId of mailsToExpire) {
            await this.connection
                .from(this.tableName)
                .where("MAQU_ID", mailId)
                .update("MAQU_STATUS", MailQ.consts.MQ_NOT_SENT);
        }

        // return this.connection
        //     .select()
        //     .from(this.tableName)
        //     .where('MAQU_SEND_ON', '<', date)
        //     .where('MAQU_STATUS', MailQ.consts.MQ_ACTIVE)
        //     .update({
        //         MAQU_STATUS: MailQ.consts.MQ_NOT_SENT,
        //     });
    }

    /**
     * Changes the 'newTask' notification addressee of a given Task
     *
     * @param {Task|Number} task - Task or id of Task
     * @param {User|Number} user - User or id of User
     * @returns {Promise<*>}
     */
    async changeMailRecipient(task, user) {
        const iTaskId = task.ITASK_ID || task;
        const userId = user.USER_ID || user;

        return await this.connection
            .select()
            .from(this.tableName)
            .where("ITASK_ID", iTaskId)
            .update({
                USER_ID: userId,
            });
    }

    /**
     * Gets emails queue
     * @param {boolean} substrClobColumns - truncates clob column values to 200 characters
     * @returns {Collection}
     */
    getQueue(substrClobColumns = true) {
        const columns = [
            "MQ.MAQU_ID",
            "MQ.MAQU_SEND_ON",
            "MQ.MAQU_STATUS",
            "MQ.USER_ID",
            "MQ.ITASK_ID",
            "IT.ITASK_NAME",
            "IP.IPROC_NAME",
            globalThis.database.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ];

        if (substrClobColumns) {
            columns.push(
                globalThis.database.raw(
                    `${globalThis.orm.db.substr(`"MQ.MAQU_META_DATA"`, 1, 200)} as "MAQU_META_DATA"`,
                ),
                globalThis.database.raw(
                    `${globalThis.orm.db.substr(`"MQ.MAQU_BODY_DATA"`, 1, 200)} as "MAQU_BODY_DATA"`,
                ),
            );
        } else {
            columns.push("MQ.MAQU_META_DATA", "MQ.MAQU_BODY_DATA");
        }

        if (Array.isArray(globalThis.dynamicConfig.langs)) {
            globalThis.dynamicConfig.langs.forEach((lang) => {
                columns.push(`TT.TTASK_NAME_${lang.toUpperCase()}`);
            });
        }

        return this.getQueueCollection(columns);
    }

    getQueueCollection(columns) {
        const conn = this.connection
            .select(columns)
            .from(`${this.tableName} as MQ`)
            .leftJoin("INSTANCE_TASKS as IT", "IT.ITASK_ID", "MQ.ITASK_ID")
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IT.IPROC_ID")
            .leftJoin("USERS as U", "U.USER_ID", "MQ.USER_ID")
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID");

        return globalThis.orm.collection("MailQ", conn);
    }

    async updateStatus(coll, status) {
        return globalThis.tasLogger.runTask(async () => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_MAIL,
            );
            coll.MAQU_STATUS = status;
            await this.store(coll);
            globalThis.tasLogger.info(
                `MailQ with id ${coll.MAQU_ID} status changed to ${status}`,
                {
                    iproc_id: coll._raw.IPROC_ID,
                    itask_id: coll.ITASK_ID,
                },
            );
            return await this.store(coll);
        });
    }

    removeOld(
        thresholdDate,
        deleteLimit,
        processStatuses = [processConst.STATUS_DONE],
    ) {
        let procStatuses = processStatuses || [];
        if (
            procStatuses.some(
                (status) =>
                    status === processConst.STATUS_ACTIVE ||
                    status === processConst.STATUS_NOT_ACTIVE,
            )
        ) {
            globalThis.tasLogger.warning(
                "Cannot delete mails from process with status ACTIVE or NEW. Skipping prohibited statuses.",
                { category: LogCategory.CATEGORY_MAIL },
            );
            procStatuses = procStatuses.filter(
                (status) =>
                    status !== processConst.STATUS_ACTIVE &&
                    status !== processConst.STATUS_NOT_ACTIVE,
            );
        }

        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("MAQU_ID", (builder) => {
                builder
                    .select("MAQU_ID")
                    .from(`${this.tableName} as MQ`)
                    .leftJoin(
                        "INSTANCE_TASKS",
                        "INSTANCE_TASKS.ITASK_ID",
                        "MQ.ITASK_ID",
                    )
                    .leftJoin(
                        "INSTANCE_PROCESSES",
                        "INSTANCE_PROCESSES.IPROC_ID",
                        "INSTANCE_TASKS.IPROC_ID",
                    )
                    .where((whereBuilder) => {
                        whereBuilder
                            .whereIn(
                                "INSTANCE_PROCESSES.IPROC_STATUS",
                                procStatuses,
                            )
                            .orWhere("MAQU_STATUS", MailQ.consts.MQ_SENT);
                    })
                    .whereRaw(
                        `MAQU_SEND_ON < ${globalThis.orm.db.toDate()}`,
                        thresholdDate,
                    )
                    .orderBy("MAQU_SEND_ON")
                    .limit(deleteLimit);
            })
            .delete();
    }
}
