// @ts-nocheck
// @ts-nocheck
import * as babel from "@babel/core";
import { BaseRepository } from "./BaseRepository";
import { UtilsService } from "../../services/UtilsService";
import * as JsScriptConstants from "../entity/const/jsScriptConst";
import { TemplatePrint } from "../entity/TemplatePrint";

export class TemplatePrintRepository extends BaseRepository<TemplatePrint> {
    meta() {
        return {
            tableName: "TEMPLATE_PRINT",
            entityName: "TemplatePrint",
            entity: () => new TemplatePrint(),
        };
    }

    getTemplateProcessPrints(tprocId) {
        const coll = this.collection;

        coll.knex.select().from(this.tableName).where("TPROC_ID", tprocId);
        return coll;
    }

    /**
     * Get prints for process
     * @param {number} iprocId
     * @param {string} name Null to ignore. String to find print with PRNT_NAME.
     * @param {function} columns filter callback
     * @returns {BaseCollection}
     */
    getInstanceProcessPrints(
        iprocId,
        name: number | string | null = null,
        columns:
            | ((_item: Record<string, any>, key: string) => boolean)
            | null = null,
        archived = false,
    ) {
        const coll = this.collection;
        const extraColumns = [
            "IP.IPROC_NAME",
            this.connection
                .select()
                .max("JS_VERSION")
                .from("JS_SCRIPTS")
                .where("JS_TYPE", JsScriptConstants.TYPE_FRONTEND)
                .as("JS_VERSION"),
        ];

        let cols = this.getEntity().getColumnNames(columns, "TP"); // columns as filter callback
        cols = cols.concat(extraColumns);

        coll.knex
            .select(cols)
            .from(
                `${archived ? "ARCH_INSTANCE_PROCESSES" : "INSTANCE_PROCESSES"} as IP`,
            )
            .join(`${this.tableName} as TP`, "IP.TPROC_ID", "TP.TPROC_ID")
            .where("IPROC_ID", iprocId);

        if (UtilsService.isNumericString(name)) {
            coll.knex.where("PRNT_ID", name);
        } else if (name) {
            coll.knex.where("PRNT_NAME", name);
        }
        return coll;
    }

    /**
     * Get latest print by name.
     * @param printName
     * @returns {*}
     */
    getByName(printName) {
        const coll = this.collection;
        return coll.knex
            .select()
            .from(this.tableName)
            .where("PRNT_NAME", printName)
            .orderBy("PRNT_ID", "DESC")
            .limit(1);
    }

    compile(script) {
        try {
            return babel.transform(script, {
                presets: [
                    [
                        "@babel/preset-env",
                        {
                            targets: {
                                // base on browserlist defaults+yPhantomJS 2.1
                                android: "67",
                                chrome: "73",
                                edge: "17",
                                firefox: "60",
                                ie: "11",
                                ios: "11.3",
                                opera: "60",
                                safari: "6", // due to Phantom 2.1
                                // safari: '12', without Phantom
                                samsung: "8.2",
                            },
                        },
                    ],
                ],
            }).code;
        } catch (err) {
            globalThis.tasLogger.error(err.message, err);
            return script;
        }
    }

    /**
     * Get latest print by name.
     * @param tprocId
     * @param printName
     * @returns {*}
     */
    getByNameForProcess(tprocId, printName) {
        const coll = this.collection;
        return coll.knex
            .select()
            .from(this.tableName)
            .where("PRNT_NAME", printName)
            .where("TPROC_ID", tprocId)
            .orderBy("PRNT_ID", "DESC")
            .limit(1)
            .then((prints) => {
                if (Array.isArray(prints) && prints.length > 0) {
                    return prints[0];
                }
                return null;
            });
    }
}
