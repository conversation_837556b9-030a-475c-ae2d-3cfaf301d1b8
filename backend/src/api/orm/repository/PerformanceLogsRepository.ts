// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as performanceLogConst from "../entity/const/performanceLogConst";
import { PerformanceLog } from "../entity/PerformanceLog";

export class PerformanceLogsRepository extends BaseRepository<PerformanceLog> {
    constructor() {
        super();

        this.consts = performanceLogConst;
        this.setBatched(globalThis.dynamicConfig.logger.batchAmount);
    }

    meta() {
        return {
            tableName: "PERFORMANCE_LOGS",
            entityName: "PerformanceLog",
            entity: () => new PerformanceLog(),
        };
    }

    /**
     * Get logs with metadata. Process, task, etc is joined.
     * @returns {BaseCollection}
     */
    detailList() {
        const conn = this.connection
            .select([
                "PL.*",
                "IP.IPROC_NAME",
                "TP.TPROC_NAME",
                "IT.ITASK_NAME",
                "TP.TPROC_ID",
                "TT.TTASK_NAME",
                "TT.TTASK_ID",
                "U.USER_NAME",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
            ])
            .from(`${this.tableName} as PL`)
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "PL.IPROC_ID")
            .leftJoin("TEMPLATE_PROCESSES as TP", "IP.TPROC_ID", "TP.TPROC_ID")
            .leftJoin("INSTANCE_TASKS as IT", "IT.ITASK_ID", "PL.ITASK_ID")
            .leftJoin("TEMPLATE_TASKS as TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .leftJoin("USERS as U", "U.USER_ID", "PL.USER_ID");
        return globalThis.orm.collection("PerformanceLogs", conn);
    }

    async getGraphList(data) {
        let conn = this.connection
            .select("PL.*")
            .from(`${this.tableName} as PL`);

        if (
            [
                this.consts.TYPE_PROCESS_ACTIVATION,
                this.consts.TYPE_CHECK_COMPLETITION,
            ].indexOf(data.category) !== -1
        ) {
            conn = conn
                .leftJoin(
                    "INSTANCE_PROCESSES as IP",
                    "IP.IPROC_ID",
                    "PL.IPROC_ID",
                )
                .where("CATEGORY", data.category)
                .where("IP.TPROC_ID", data.tproc_id);
        } else if (
            [
                this.consts.TYPE_CUSTOM_VIEW,
                this.consts.TYPE_PRINT,
                this.consts.TYPE_PRINT_FILE,
            ].indexOf(data.category) !== -1
        ) {
            conn = conn
                .where("CATEGORY", data.category)
                .where("LPARAM", data.lparam);
        } else if (
            [
                this.consts.TYPE_TASK_SOLVE,
                this.consts.TYPE_CLONE_TASK,
                this.consts.TYPE_SEND_NOTIFICATION,
                this.consts.TYPE_FIND_SOLVER,
                this.consts.TYPE_INCOMINGLINKS_FULLFILLED,
            ].indexOf(data.category) !== -1
        ) {
            conn = conn
                .leftJoin("INSTANCE_TASKS as IT", "IT.ITASK_ID", "PL.ITASK_ID")
                .where("CATEGORY", data.category)
                .where("IT.TTASK_ID", data.ttask_id);
        } else if (
            [this.consts.TYPE_CALCULATIONS].indexOf(data.category) !== -1
        ) {
            conn = conn
                .leftJoin("INSTANCE_TASKS as IT", "IT.ITASK_ID", "PL.ITASK_ID")
                .where("CATEGORY", data.category)
                .where("IT.TTASK_ID", data.ttask_id)
                .where("LPARAM", data.lparam);
        } else if (
            [this.consts.TYPE_XML_PROCESS_IMPORT].indexOf(data.category) !== -1
        ) {
            conn = conn
                .join("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "PL.IPROC_ID")
                .where("CATEGORY", data.category)
                .where("LPARAM", data.lparam);
        } else if (
            [this.consts.TYPE_PROCESS_ARCHIVATION].indexOf(data.category) !== -1
        ) {
            conn = conn
                .join(
                    `${this.getArchTableName("INSTANCE_PROCESSES")} as IP`,
                    "IP.IPROC_ID",
                    "PL.IPROC_ID",
                )
                .where("CATEGORY", data.category)
                .where("IP.TPROC_ID", data.tproc_id);
        } else if (
            [this.consts.TYPE_PROCESS_UNARCHIVATION].indexOf(data.category) !==
            -1
        ) {
            conn = conn
                .join("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "PL.IPROC_ID")
                .where("CATEGORY", data.category)
                .where("IP.TPROC_ID", data.tproc_id);
        } else {
            // Default behaviour is graph for all processes same template.
            globalThis.tasLogger.warning(
                `Performance logs using default graph view. You should implement category=${data.category}`,
            );
            conn = conn
                .leftJoin(
                    "INSTANCE_PROCESSES as IP",
                    "IP.IPROC_ID",
                    "PL.IPROC_ID",
                )
                .where("CATEGORY", data.category)
                .where("IP.TPROC_ID", data.tproc_id);
        }

        return globalThis.orm.collection("PerformanceLogs", conn);
    }

    /**
     * Using batch insert. There must be 10 items to insert log into db. Batched inserts are ~60% faster.
     * @param batched
     */
    setBatched(batched) {
        this.batched = batched;
        if (batched) {
            this.batchAmount = 10; // 10-30 rows is best for batch insert.
            this.batch = [];
        } else {
            this.batchAmount = 1;
        }
    }

    async log(category, duration, meta) {
        try {
            const logLevel =
                globalThis.dynamicConfig.logger.performanceCategory *
                this.consts.TYPE_STEP;
            if (logLevel === 0 || category > logLevel) {
                return false;
            }

            const row = {
                CATEGORY: category,
                PERF_DATE: new Date(),
                DURATION: Number(duration).toFixed(0),
                IPROC_ID: meta.IPROC_ID || null,
                ITASK_ID: meta.ITASK_ID || null,
                USER_ID: meta.USER_ID || null,
                WPARAM: meta.WPARAM || null,
                LPARAM: meta.LPARAM || null,
            };

            if (this.batchAmount > 1) {
                return await this.logBatched(row);
            }
            return await this.connection(this._tableName).insert(row);
        } catch (err) {
            globalThis.tasLogger.error("Performance log store error ", {
                err,
                category,
                duration,
                meta,
            });
        }
    }

    async logBatched(data) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `PERFORMANCE:LOGS-BATCH`,
        );

        try {
            this.batch.push(data);

            if (this.batch.length >= this.batchAmount) {
                await this.connection.batchInsert(this.tableName, this.batch);
                this.batch = [];
            }
        } finally {
            await lock.release();
        }
    }

    async flush(): Promise<void> {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `PERFORMANCE:LOGS-FLUSH`,
        );

        try {
            if (Array.isArray(this.batch) && this.batch.length > 0) {
                await this.connection.batchInsert(this.tableName, this.batch);
                this.batch = [];
            }
        } finally {
            await lock.release();
        }
    }

    removeOld(thresholdDate: Date | string, deleteLimit: number) {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("PERF_DATE", (builder) => {
                builder
                    .select("PERF_DATE")
                    .from(this.tableName)
                    .whereRaw(
                        `PERF_DATE < ${globalThis.orm.db.toDate()}`,
                        thresholdDate,
                    )
                    .orderBy("PERF_DATE")
                    .limit(deleteLimit);
            })
            .delete();
    }
}
