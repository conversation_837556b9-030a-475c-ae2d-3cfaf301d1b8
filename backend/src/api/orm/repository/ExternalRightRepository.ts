// @ts-nocheck
// @ts-nocheck
import AssignManagerRights from "../../queue/jobs/AssignManagerRights";
import SyncManagerRights from "../../queue/jobs/SyncManagerRights";
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as TASK from "../entity/const/taskConst";
import { InstanceProcessStaticRight } from "../entity/InstanceProcessStaticRight";
import { LogCategory } from "../../../utils/logger/logConsts";

export class ExternalRightRepository extends BaseRepository<InstanceProcessStaticRight> {
    meta() {
        return {
            tableName: "INSTANCE_PROCESS_STATIC_RIGHTS",
            entityName: "InstanceProcessStaticRight",
            entity: () => new InstanceProcessStaticRight(),
        };
    }

    async rebuildRights() {
        await this.rebuildStaticRights();
        await this.rebuildDynamicRights();
    }

    rebuildStaticRights() {
        return this.truncateDynamicRights()
            .then(() =>
                this.connection.raw(`
            INSERT INTO "INSTANCE_PROCESS_STATIC_RIGHTS" (
                "IPROC_ID",
                "USER_ID"
            )
                ( SELECT
                    "ip1"."IPROC_ID",
                    "ip1"."USER_ID"
                  FROM
                    (
                        SELECT
                            "IPROC_ID",
                            "IPROC_INST_OWNER_USER_ID" AS "USER_ID"
                        FROM
                            "INSTANCE_PROCESSES"
                        WHERE
                            "IPROC_INST_OWNER_USER_ID" IS NOT NULL
                        UNION
                        SELECT
                            "IPROC_ID",
                            "ITASK_USER_ID" AS "USER_ID"
                        FROM
                            "INSTANCE_TASKS"
                        WHERE
                            "ITASK_USER_ID" IS NOT NULL
                        UNION
                        SELECT
                            "IPROC_ID",
                            "ITASK_ASSESMENT_USER_ID" AS "USER_ID"
                        FROM
                            "INSTANCE_TASKS"
                        WHERE
                            "ITASK_ASSESMENT_USER_ID" IS NOT NULL
                    ) ip1
                  WHERE
                    NOT EXISTS (
                        SELECT
                            "USER_ID"
                        FROM
                            "INSTANCE_PROCESS_STATIC_RIGHTS"
                        WHERE
                            "IPROC_ID" = ip1."IPROC_ID"
                            AND   "USER_ID" = ip1."USER_ID"
                    )
                )
        `),
            )
            .catch(async (err) => {
                await globalThis.dynamicConfig.error(
                    `Error in rebuildStaticRights ${err.message}`,
                    err,
                );
                throw err;
            });
    }

    /**
     * Clear dynamic rights.
     */
    truncateDynamicRights(userId = null) {
        const query = this.connection
            .delete()
            .from("INSTANCE_PROCESS_DYN_RIGHTS");
        if (userId === null) {
            return query;
        }

        return query.where("USER_ID", userId);
    }

    /**
     * Rebuild all dynamic rights. Static rights must be done!
     * @return {Promise<Object>}
     */
    async rebuildDynamicRights(): Promise<any> {
        await this.truncateDynamicRights();

        const users = await this.connection
            .select("USER_ID AS USER_ID")
            .distinct()
            .from(this.tableName);

        return await Promise.all(
            users.map((user) => this.assignManagerRights(user.USER_ID)),
        );
    }

    async assignManagerRights(userId: number): Promise<void> {
        const managerUsers = await this.getManagerUsers(userId);

        if (!Array.isArray(managerUsers) || managerUsers.length === 0) {
            return;
        }

        // Use Promise.all for concurrent operations
        await Promise.all(
            managerUsers.map(async (manager) => {
                if (
                    !manager.MANAGER_USER_ID ||
                    manager.MANAGER_USER_ID === userId
                ) {
                    return;
                }

                // Execute raw SQL query for each manager
                return await globalThis.container.client.database.callKnexRaw(
                    `
                    INSERT INTO "INSTANCE_PROCESS_DYN_RIGHTS"
                      ("USER_ID", "IPROC_ID")
                    SELECT :MANAGER_ID, "IPROC_ID"
                    FROM "INSTANCE_PROCESS_STATIC_RIGHTS" "IPSR"
                    WHERE "USER_ID" = :USER_ID
                    AND "IPSR"."IPROC_ID" NOT IN (
                        SELECT "IPROC_ID"
                        FROM "INSTANCE_PROCESS_STATIC_RIGHTS"
                        WHERE "USER_ID" = :MANAGER_ID
                        UNION
                        SELECT "IPROC_ID" 
                        FROM "INSTANCE_PROCESS_DYN_RIGHTS" 
                        WHERE "USER_ID" = :MANAGER_ID
                    )
                    `,
                    { MANAGER_ID: manager.MANAGER_USER_ID, USER_ID: userId },
                    this.connection,
                );
            }),
        );
    }

    getManagerUsers(userId: number) {
        return globalThis.container.client.database.callKnexRaw(
            `
WITH RECURSIVE org_tree AS (
    -- Anchor: start from the user's assigned org unit
    SELECT 
        os."ORGSTR_ID",
        os."PARENT_ORGSTR_ID",
        os."MANAGER_USER_ID"
    FROM "ORGANIZATION_STRUCTURE" os
    WHERE os."ORGSTR_ID" = (
        SELECT "ORGSTR_ID"
        FROM "USER_ORGANIZATION_STRUCTURE"
        WHERE "USER_ID" = :USER_ID
    )

    UNION ALL

    -- Recursive step: move up the hierarchy from child to parent
    SELECT 
        parent."ORGSTR_ID",
        parent."PARENT_ORGSTR_ID",
        parent."MANAGER_USER_ID"
    FROM "ORGANIZATION_STRUCTURE" parent
    INNER JOIN org_tree child ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
)
SELECT "MANAGER_USER_ID"
FROM org_tree
WHERE "MANAGER_USER_ID" IS NOT NULL;
        `,
            { USER_ID: userId },
            this.connection,
        );
    }

    async hasProcessRightsByTask(
        userId: number,
        iTaskId: number,
        hasElevatedRole?: boolean,
        archived?: boolean,
    ) {
        return await this.getUserProcesses(
            userId,
            hasElevatedRole,
            true,
            archived,
        )
            .where("IP.IPROC_ID", (builder) => {
                builder
                    .select("IPROC_ID")
                    .from(archived ? "ARCH_INSTANCE_TASKS" : "INSTANCE_TASKS")
                    .where("ITASK_ID", iTaskId);
            })
            .then((list) => !!list.length);
    }

    async hasProcessRights(
        userId,
        iProcId,
        hasElevatedRole,
        archived: boolean,
    ) {
        return await this.getUserProcesses(
            userId,
            hasElevatedRole,
            true,
            archived,
        )
            .where("IP.IPROC_ID", iProcId)
            .then((list) => !!list.length);
    }

    getUserProcesses(
        userId: number,
        hasElevatedRole?: boolean,
        addWithClause: boolean = false,
        archived?: boolean,
    ) {
        if (hasElevatedRole) {
            return this.connection
                .select("IPROC_ID")
                .from(`${archived ? "ARCH_" : ""}INSTANCE_PROCESSES as IP`);
        }

        let knex = this.connection;
        if (addWithClause) {
            knex = knex.with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            });
        }

        return knex
            .select("IP.IPROC_ID")
            .from(function () {
                this.select("IPDR.IPROC_ID")
                    .from(
                        `${archived ? "ARCH_" : ""}INSTANCE_PROCESS_STATIC_RIGHTS as IPDR`,
                    )
                    .where("IPDR.USER_ID", userId)
                    .union(function () {
                        this.select("IPDR.IPROC_ID")
                            .from(
                                `${archived ? "ARCH_" : ""}INSTANCE_PROCESS_DYN_RIGHTS as IPDR`,
                            )
                            .where("IPDR.USER_ID", userId);
                    })
                    .union(function () {
                        this.select("IP.IPROC_ID")
                            .from(
                                `${archived ? "ARCH_" : ""}INSTANCE_PROCESSES as IP`,
                            )
                            .whereIn("IP.IPROC_VIS_ROLE_ID", (builder) => {
                                builder
                                    .select("ROLE_ID")
                                    .from("SINGLE_USER_ROLES");
                            });
                    })
                    .union(function () {
                        this.select("IP.IPROC_ID")
                            .from(
                                `${archived ? "ARCH_" : ""}INSTANCE_PROCESSES as IP`,
                            )
                            .innerJoin(
                                "TEMPLATE_PROCESSES as TP",
                                "TP.TPROC_ID",
                                "IP.TPROC_ID",
                            )
                            .whereIn("TP.TPROC_VIS_ROLE_ID", (builder) => {
                                builder
                                    .select("ROLE_ID")
                                    .from("SINGLE_USER_ROLES");
                            });
                    })
                    .union(function () {
                        this.select("IP.IPROC_ID")
                            .from(
                                `${archived ? "ARCH_" : ""}INSTANCE_PROCESSES as IP`,
                            )
                            .innerJoin(
                                "HEADERS as HDR",
                                "HDR.HEADER_ID",
                                "IP.HEADER_ID",
                            )
                            .whereIn("HDR.HDR_VIS_ROLE_ID", (builder) => {
                                builder
                                    .select("ROLE_ID")
                                    .from("SINGLE_USER_ROLES");
                            });
                    })
                    .union(function () {
                        this.select("IP.IPROC_ID")
                            .from("TEMPLATE_PROCESSES as TP")
                            .leftJoin(
                                "USER_ORGANIZATION_STRUCTURE as URS",
                                "URS.ORGSTR_ID",
                                "TP.TPROC_VIS_ORGSTR_ID",
                            )
                            .leftJoin(
                                `${archived ? "ARCH_" : ""}INSTANCE_PROCESSES as IP`,
                                "TP.TPROC_ID",
                                "IP.TPROC_ID",
                            )
                            .whereNotNull("TP.TPROC_VIS_ORGSTR_ID")
                            .where("URS.USER_ID", userId);
                    })
                    .as("IP");
            })
            .whereNotNull("IP.IPROC_ID");
    }

    async assignUserRights(iprocId, userIds, groupName = "Wf") {
        if (!userIds) {
            return;
        }

        if (groupName === "pull" && Array.isArray(userIds)) {
            if (userIds.length > 100) {
                // truncate due to performance
                userIds = userIds.slice(0, 100);
            }
        }

        const uniqueUserIdsArr = _.uniq(
            Array.isArray(userIds) ? userIds : [userIds],
        );

        const rights = await this.connection
            .select("USER_ID")
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .whereIn("USER_ID", uniqueUserIdsArr);

        const rightsMap = new Map(rights.map((right) => [right.USER_ID, true]));

        const insertData = uniqueUserIdsArr
            .filter((userId) => !rightsMap.has(userId))
            .map((userId) => ({
                IPROC_ID: iprocId,
                USER_ID: userId,
                GROUP_NAME: groupName,
            }));

        if (insertData.length > 0) {
            await this.connection.batchInsert(
                "INSTANCE_PROCESS_STATIC_RIGHTS",
                insertData,
                100,
            ); // Adjust the chunk size as needed
        }

        return await this.updateDynamicRights(uniqueUserIdsArr, iprocId);
    }

    removeStaticRightsGroup(iprocId, group) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("GROUP_NAME", group)
            .delete();
    }

    /**
     * Update dynamic rights(managers) based on users given as argument.
     * You have adjust number of users due to performance.
     *
     * @param userIds {Array<userId>|userId}
     * @param iprocId
     * @return {Promise<unknown>}
     */
    async updateDynamicRights(userIds: number[], iprocId: number) {
        userIds = Array.isArray(userIds) ? userIds : [userIds]; // convert to Array
        const mngUserIds = [];
        for (const userId of userIds) {
            const mngsResult = await this.getManagerUsers(userId);
            mngsResult.forEach((managerUser) => {
                mngUserIds.push(managerUser.MANAGER_USER_ID);
            });
        } // we have all managers of all users now

        return await this.addDynamicRights(iprocId, mngUserIds);
    }

    /**
     * Add dynamic rights. Not delete existing.
     * Adds rights only if not present.
     *
     * @param iprocId
     * @param userIds {Array<userId>|userId}
     * @return {Promise<unknown>}
     */
    async addDynamicRights(
        iprocId: number,
        userIds: number | number[],
    ): Promise<void> {
        userIds = Array.isArray(userIds) ? userIds : [userIds];
        userIds = _.uniq(userIds);

        const rights = await this.connection
            .select("USER_ID")
            .from("INSTANCE_PROCESS_STATIC_RIGHTS")
            .whereIn("USER_ID", userIds)
            .where("IPROC_ID", iprocId)
            .union(function () {
                this.select("USER_ID")
                    .from("INSTANCE_PROCESS_DYN_RIGHTS")
                    .whereIn("USER_ID", userIds)
                    .where("IPROC_ID", iprocId);
            });

        for (const userId of userIds) {
            const userHasRight = rights.some(
                (right: { USER_ID: number }) => right.USER_ID === userId,
            );

            if (!userHasRight) {
                await this.connection.raw(
                    `insert into "INSTANCE_PROCESS_DYN_RIGHTS" ("IPROC_ID", "USER_ID") values (?, ?)`,
                    [iprocId, userId],
                );
            }
        }
    }

    /**
     * Removes users from static_rights and preserves managers
     */
    removeStaticRights(iprocId, userIds) {
        userIds = Array.isArray(userIds) ? userIds : [userIds]; // convert to Array

        return this.connection
            .select()
            .from("INSTANCE_PROCESS_STATIC_RIGHTS")
            .whereIn("USER_ID", Array.isArray(userIds) ? userIds : [userIds])
            .where("IPROC_ID", iprocId)
            .delete();
    }

    /**
     * Returns all users which have static rights in passed group and process
     * @param iprocId
     * @param group
     * @return {BaseCollection}
     */
    getUsersByGroup(iprocId, group = null) {
        const coll = this.getCollection();
        coll.knex = coll.knex
            .where("IPROC_ID", iprocId)
            .where("GROUP_NAME", group);
        return coll;
    }

    async onManagerChanged(
        managerUserId,
        formerManagerUserId,
        immediate = true,
    ) {
        try {
            const useQueue =
                !immediate &&
                globalThis.dynamicConfig.scaling.queue.rights.enabled;

            if (formerManagerUserId) {
                await this.processSyncDynamicRightsByManagerId(
                    formerManagerUserId,
                    useQueue,
                );
            }

            if (!managerUserId) {
                return null;
            }

            const managedUsers = await globalThis.orm
                .repo("organizationStructure", this.connection)
                .getManagedUsers(managerUserId);

            if (useQueue) {
                if (managedUsers.length === 0) {
                    return globalThis.tasLogger.info(
                        `No managed users for manager ${managerUserId}.`,
                    );
                }
                globalThis.tasLogger.info(
                    `Invoking queue job to build dynamic rights for manager ${managerUserId}.`,
                );

                for (const userId of managedUsers) {
                    await globalThis.container.client.managedQueue.rights.queue.assignManagerRights(
                        {
                            userId,
                        },
                    );
                }
            }

            globalThis.tasLogger.info(
                `Building dynamic rights for manager ${managerUserId}.`,
            );
            for (const userId of managedUsers) {
                const action = new AssignManagerRights();
                await globalThis.tasLogger.runTask(async (): Promise<void> => {
                    globalThis.tasLogger.setContextProperty(
                        "category",
                        LogCategory.CATEGORY_AUDIT,
                    );
                    await action.run(this.connection, userId);
                });
            }
            return globalThis.tasLogger.info(
                `Building dynamic rights for manager ${managerUserId} done.`,
            );
        } catch (err) {
            return globalThis.tasLogger.warning(
                `Building dynamic rights for manager ${managerUserId} errored.`,
                err,
            );
        }
    }

    async onUserOrganizationChanged(userId, immediate = true) {
        try {
            if (!userId) {
                return null;
            }

            if (
                !immediate &&
                globalThis.dynamicConfig.scaling.queue.rights.enabled
            ) {
                globalThis.tasLogger.info(
                    `Invoking queue job to build dynamic rights for user ${userId}.`,
                );
                return await globalThis.container.client.managedQueue.rights.queue.assignManagerRights(
                    {
                        userId,
                    },
                );
            }

            globalThis.tasLogger.info(
                `Building dynamic rights for user ${userId}.`,
            );
            const action = new AssignManagerRights();
            await globalThis.tasLogger.runTask(async (): Promise<void> => {
                globalThis.tasLogger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_AUDIT,
                );
                await action.run(this.connection, userId);
            });
            return globalThis.tasLogger.info(
                `Building dynamic rights for user ${userId} done.`,
            );
        } catch (err) {
            return globalThis.tasLogger.warning(
                `Building dynamic rights for user ${userId} errored.`,
                err,
            );
        }
    }

    async removeStaticRightsWhenChangingCaseOwner(
        iprocIds,
        oldUserId: number | null = null,
    ) {
        let iprocIdsArr = Array.isArray(iprocIds) ? iprocIds : [iprocIds];
        let oldUserIdsArr = Array.isArray(oldUserId) ? oldUserId : [oldUserId];
        let userIdsToRemoveRights = oldUserIdsArr;
        let procUserMap = {};

        if (!oldUserId) {
            // get the case owners of the changed processes
            const processes = await this.connection
                .select("IPROC_ID", "IPROC_INST_OWNER_USER_ID")
                .from("INSTANCE_PROCESSES")
                .whereIn(
                    "IPROC_ID",
                    Array.isArray(iprocIds) ? iprocIds : [iprocIds],
                );

            iprocIdsArr = _.map(processes, "IPROC_ID");
            oldUserIdsArr = _.map(processes, "IPROC_INST_OWNER_USER_ID");

            procUserMap = _.reduce(
                processes,
                (resultObj, obj) => {
                    resultObj[obj.IPROC_ID] = obj.IPROC_INST_OWNER_USER_ID;
                    return resultObj;
                },
                {},
            );
        }

        // get active tasks in the processes where the case owner or old user is the solver
        const tasks = await this.connection
            .select("IPROC_ID", "ITASK_USER_ID")
            .from("INSTANCE_TASKS")
            .whereIn(
                "IPROC_ID",
                Array.isArray(iprocIds) ? iprocIds : [iprocIds],
            )
            .whereIn(
                "ITASK_USER_ID",
                Array.isArray(oldUserIdsArr) ? oldUserIdsArr : [oldUserIdsArr],
            )
            .where("ITASK_STATUS", "A")
            .whereIn("ITASK_TYPE", [TASK.TYPE_STANDARD, TASK.TYPE_INVITATION]);

        // case ids where the user has no active task
        const procIdsToRemoveRights = _.difference(
            iprocIdsArr.map(Number),
            _.map(tasks, "IPROC_ID"),
        );

        if (!oldUserId) {
            userIdsToRemoveRights = procIdsToRemoveRights.map(
                (id) => procUserMap[id],
            );
        }

        if (!_.isEmpty(procIdsToRemoveRights)) {
            await this.connection
                .select()
                .from(this.tableName)
                .whereIn(
                    "USER_ID",
                    Array.isArray(userIdsToRemoveRights)
                        ? userIdsToRemoveRights
                        : [userIdsToRemoveRights],
                )
                .whereIn(
                    "IPROC_ID",
                    Array.isArray(procIdsToRemoveRights)
                        ? procIdsToRemoveRights
                        : [procIdsToRemoveRights],
                )
                .delete();
        }
    }

    async syncDynamicRightsByManagerId(managerId) {
        globalThis.tasLogger.info(
            `Syncing dynamic rights for manager ${managerId}.`,
        );
        await this.truncateDynamicRights(managerId);
        await this.onManagerChanged(managerId, null, true);
        globalThis.tasLogger.info(
            `Syncing dynamic rights for manager ${managerId} done.`,
        );
    }

    async processSyncDynamicRightsByManagerId(
        managerId,
        useQueue = globalThis.dynamicConfig.scaling.queue.rights.enabled,
    ) {
        if (useQueue) {
            globalThis.tasLogger.info(
                `Invoking queue job to sync dynamic rights for manager ${managerId}.`,
            );
            return await globalThis.container.client.managedQueue.rights.queue.syncManagerRights(
                {
                    managerId,
                },
            );
        }

        const action = new SyncManagerRights();
        return globalThis.tasLogger.runTask(async () => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_AUDIT,
            );
            return await action.run(this.connection, managerId);
        });
    }
}
