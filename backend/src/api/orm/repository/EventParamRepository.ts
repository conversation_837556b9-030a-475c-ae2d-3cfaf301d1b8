import { BaseRepository } from "./BaseRepository";
import * as eventConsts from "../entity/const/eventConst";
import { EventParam } from "../entity/EventParam";
import { BaseCollection } from "../BaseCollection";

export class EventParamRepository extends BaseRepository<EventParam> {
    meta() {
        return {
            tableName: "EVENT_PARAM",
            entityName: "EventParam",
            entity: () => new EventParam(),
            archived: false,
        };
    }

    getForEvent(eveId: number): BaseCollection<EventParam> {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("EVE_ID", eveId);
        return globalThis.orm.collection("eventParam", conn);
    }

    removeOld(thresholdDate: string, deleteLimit: number) {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("EVEPAR_ID", (builder) => {
                builder
                    .select("EP.EVEPAR_ID")
                    .from(`${this.tableName} as EP`)
                    .join("EVENT as E", "E.EVE_ID", "EP.EVE_ID")
                    .where("E.EVE_STATUS", eventConsts.STATUS_PROCESSED)
                    .whereRaw(
                        `E.EVE_DATETIME < ${globalThis.orm.db.toDate()}`,
                        thresholdDate,
                    )
                    .orderBy("E.EVE_DATETIME")
                    .limit(deleteLimit);
            })
            .delete();
    }
}
