import { DmsFileAccessLog } from "../entity/DmsFileAccessLog";
import { BaseRepository } from "./BaseRepository";
import { BaseCollection } from "../BaseCollection";

export class ArchivedDmsFileAccessLogRepository extends BaseRepository<DmsFileAccessLog> {
    meta() {
        return {
            tableName: "ARCH_DMS_FILE_ACCESS_LOG",
            entityName: "archivedDmsFileAccessLog",
            entity: () => new DmsFileAccessLog(),
            archParams: {
                subQueryTable: "ARCH_DMS_FILE",
                subQueryColumn: "DMSF_ID",
            },
        };
    }

    getAll(): BaseCollection<DmsFileAccessLog> {
        const conn = this.connection
            .select([
                "DMSAL.*",
                "IP.IPROC_NAME",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
                "IT.ITASK_NAME",
                "DF.NAME",
            ])
            .from(`${this.tableName} as DMSAL`)
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES as IP",
                "DMSAL.IPROC_ID",
                "IP.IPROC_ID",
            )
            .leftJoin("USERS as U", "DMSAL.USER_ID", "U.USER_ID")
            .leftJoin(
                "ARCH_INSTANCE_TASKS as IT",
                "DMSAL.ITASK_ID",
                "IT.ITASK_ID",
            )
            .leftJoin("ARCH_DMS_FILE as DF", "DMSAL.DMSF_ID", "DF.DMSF_ID");

        return globalThis.orm.collection("dmsFileAccessLog", conn);
    }
}
