// @ts-nocheck
import _ from "lodash";
import { CustomViewRepository } from "./CustomViewRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";

export class mssql_CustomViewRepository extends CustomViewRepository {
    async processSort(orderData, sortData, columns, conn, columnsToSkip = []) {
        let order = orderData;
        let sort = sortData;

        if (typeof order === "string" && order.split(",").length > 1) {
            order = order.split(",");
            sort = sort ? sort.split(",") : [];
        }

        if (Array.isArray(order)) {
            let outConn = { conn };
            for (let i = 0; i < order.length; i += 1) {
                if (_.includes(columnsToSkip, order[i])) {
                    continue;
                }
                outConn = await this.processSort(
                    order[i],
                    sort.length > i ? sort[i] : null,
                    columns,
                    outConn.conn,
                );
            }
            return outConn;
        }

        const self = this;

        if (order === null || typeof order === "undefined") {
            return { conn };
        }

        if (sort === null || typeof sort === "undefined") {
            sort = "ASC";
        }

        let columnName = null;
        if (order.match(/V-/)) {
            if (order === "V-id") {
                // t3f-1148 Řazení tabulek podle nejednoznačného sloupce vrací různorodé výsledky
                columnName = `IP.IPROC_ID`;
            } else if (order === "V-1") {
                columnName = `"IP"."IPROC_NAME"`;
            } else if (order === "V-2") {
                columnName = `"IP"."IPROC_DESCRIPTION"`;
            } else if (order === "V-3") {
                columnName = globalThis.database.raw(
                    `"UIP"."USER_DISPLAY_NAME"`,
                );
            } else if (order === "V-4") {
                columnName = `"IP"."IPROC_ACTUAL_START_DATE"`;
            } else if (order === "V-5") {
                columnName = `"IP"."IPROC_DUE_DATE_FINISH"`;
            } else if (order === "V-6") {
                columnName = `"IP"."IPROC_PRIORITY"`;
            } else if (order === "V-7") {
                columnName = `"IP"."IPROC_SUMMARY"`;
            } else if (order === "V-8") {
                columnName = `"IP"."IPROC_ACTUAL_FINISH_DATE"`;
            } else if (order === "V-9") {
                columnName = `"IP"."IPROC_CASE_STATUS"`;
            } else if (order.includes("V-9") && order.includes(",")) {
                // @t3b-1105 Úprava sloupce status v přehledech Cases
                // order=cs_name_cs,V-9
                const cols = order.split(",");
                columnName = [
                    `"CS"."${cols[0].toUpperCase()}"`,
                    `"IP"."IPROC_CASE_STATUS"`,
                ];
            } else if (order === "V-0") {
                columnName = `IP.IPROC_ID`;
            } else {
                throw new UserException(
                    `Unknown ordering system column ['${order}']`,
                );
            }
        } else {
            if (order.startsWith("V") || order.startsWith("v")) {
                order = order.substring(1, order.length);
            }

            const tvar = self.getTvar(columns, order);
            if (tvar === null) {
                globalThis.tasLogger.warning(
                    "Suspicious custom view order column. No variables found.",
                    { order },
                );
                return { conn };
            }

            // Is CASE_STATUS translation
            if (tvar.isColumn) {
                columnName = tvar.column;
            } else {
                const type = tvar.TVAR_TYPE;
                const attr = tvar.TVAR_ATTRIBUTE;
                const multi = tvar.TVAR_MULTI;

                if (multi != null && type !== "LT") {
                    throw new UserException(
                        `This sort type is not supported. IVAR_TYPE = '${type}' IVAR_ATTRIBTE = '${attr}' IVAR_MULTI = ' ${multi} " tvarId = " ${tvar.TVAR_ID}`,
                    );
                }

                if (
                    type === "T" ||
                    type === "LT" ||
                    type === "DT" ||
                    (type === "DL" && attr === null)
                ) {
                    columnName = `IPV.${tvar.TVAR_ALIAS}`;
                } else if (
                    (type === "N" || type === "LN") &&
                    (attr === null || attr === "S") /* SEQUENCE */
                ) {
                    columnName = `IPV.${tvar.TVAR_ALIAS}`;
                } else if (type === "D" || type === "LD") {
                    columnName = `IPV.${tvar.TVAR_ALIAS}`;
                } else if (
                    (type === "N" || type === "LN" || type === "DL") &&
                    attr != null
                ) {
                    if (attr === "U") {
                        columnName = globalThis.database.raw(
                            `(select "USER_DISPLAY_NAME" from "USERS" where "USER_ID" = "IPV"."${tvar.TVAR_ALIAS}")`,
                        );
                    } else if (attr === "O") {
                        columnName = globalThis.database.raw(
                            `(select "ORGSTR_NAME" from "ORGANIZATION_STRUCTURE" where "ORGSTR_ID" = "IPV"."${tvar.TVAR_ALIAS}")`,
                        );
                    } else if (attr === "R") {
                        columnName = globalThis.database.raw(
                            `(select "ROLE_NAME" from "ROLES" where "ROLE_ID" = "IPV"."${tvar.TVAR_ALIAS}")`,
                        );
                    } else {
                        throw new UserException(
                            `This sort type is not supported. IVAR_TYPE = ${type} IVAR_ATTRIBTE = ${attr} IVAR_MULTI = ${multi} tvarId = ${tvar.TVAR_ID}`,
                        );
                    }
                }
            }
        }

        let rawOrder = "";
        const arrayOrder = Array.isArray(columnName)
            ? columnName
            : [columnName];
        const arraySort = sort.split(",");

        arrayOrder.forEach((col, index) => {
            rawOrder += ` CASE WHEN ${col} IS NULL THEN 1 ELSE 0 END, ${col} ${arraySort[index]} ${index === arrayOrder.length - 1 ? "" : ","} `;
        });

        conn.orderByRaw(rawOrder);
        return { conn };
    }
}
