// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { DynamicTableCol } from "../entity/DynamicTableCol";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../services/UtilsService";
import { BaseCollection } from "../BaseCollection";

export class DynamicTableColRepository extends BaseRepository<DynamicTableCol> {
    meta() {
        return {
            tableName: "DYNAMIC_TABLE_COLS",
            entityName: "DynamicTableCol",
            sequenceName: "none",
            entity: () => new DynamicTableCol(),
        };
    }

    /**
     * Delete column and its values.
     * @param dtId
     * @param dtcId
     * @returns {Promise<void>}
     */
    deleteCol(dtId, dtcId) {
        return this.deleteCols(dtId, dtcId);
    }

    async deleteCols(dtId, dtcId) {
        await this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", dtId)
            .whereIn("DTC_ID", Array.isArray(dtcId) ? dtcId : [dtcId])
            .delete();
        const dtvRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );
        await dtvRepo.deleteCols(dtId, dtcId);
    }

    /**
     * Remove all cols.
     * @param {number} dtId
     * @returns {Promise<void>}
     */
    async clearTable(dtId) {
        await this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", dtId)
            .delete();
    }

    async appendCols(identifier, colNames, originalColumns = []) {
        if (!Array.isArray(colNames) || colNames.length === 0) {
            return true;
        }

        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dtcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        const dtvRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );

        // @t3b-2104 Pri prepsani DT zachovat pripadne preruseni ciselne sekvence
        let maxElement = _.last(originalColumns);
        const getNextDtcId = (value) => {
            if (value <= _.last(originalColumns)) {
                return originalColumns.shift();
            }

            // Return +1 in case of NaN
            return ++maxElement || value + 1;
        };

        const targetTable = await dtRepo.get(identifier);
        let dtcId = await dtcRepo.getMaxColId(targetTable);
        let appendColsCount = 0;
        for (const [index, colName] of colNames.entries()) {
            const col = await dtcRepo.getColByName(targetTable, colName);
            if (col || ["ID", "Index"].indexOf(colName) !== -1) {
                // Column already exists or is system column.
                // Shift columns if 'merging'
                if (col) {
                    originalColumns = originalColumns.map((item) => item + 1);
                    maxElement += 1;
                }
                continue;
            }

            if (UtilsService.isNumericString(colName)) {
                throw new UserException("DLC_NAME can not be numeric");
            }

            const skippedColumns = index - appendColsCount;
            dtcId = getNextDtcId(index - skippedColumns);

            await this.connection(this.tableName).insert({
                DT_ID: targetTable.DT_ID,
                DTC_ID: dtcId,
                DTC_NAME: colName,
            });

            await dtvRepo.createCol(targetTable.DT_ID, dtcId);
            appendColsCount += 1;
        }

        return appendColsCount;
    }

    async getForTable(
        identifier: number,
    ): Promise<BaseCollection<DynamicTableCol>> {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const dtId = UtilsService.isNumericString(identifier)
            ? identifier
            : await dtRepo.get(identifier).DT_ID;

        const con = this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", dtId)
            .orderBy("DTC_ID");
        return globalThis.orm.collection("dynamicTableCol", con);
    }

    async getDbColNames(dtId) {
        const cols = await (
            await globalThis.orm
                .repo("dynamicTableCol", this.connection)
                .getForTable(dtId)
        ).fetchAll();
        const out = [];
        for (let i = 0; i < cols.length; i += 1) {
            out.push(`COL_${cols[i].DTC_ID}`);
        }
        return out;
    }

    async mapCols(identifier) {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const targetTable = await dtRepo.get(identifier);

        const cols = await this.connection
            .select()
            .from("DYNAMIC_TABLE_COLS")
            .where("DT_ID", targetTable.DT_ID);

        const colsMap = {};
        cols.forEach((col) => {
            colsMap[col.DTC_NAME] = `COL_${col.DTC_ID}`;
            colsMap[`COL_${col.DTC_ID}`] = col.DTC_NAME;
        });

        return colsMap;
    }

    async getMaxColId(identifier) {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const targetTable = await dtRepo.get(identifier);

        const cols = await globalThis.container.client.database.callKnexRaw(
            `select max("DTC_ID") as "MAX_ID"
                                          from "${this.tableName}"
                                          where "DT_ID" = ?`,
            [targetTable.DT_ID],
            this.connection,
        );
        return cols.length === 0 || !cols[0].MAX_ID ? 0 : cols[0].MAX_ID;
    }

    async getColByName(identifier, dlcName) {
        const dtRepo = globalThis.orm.repo("dynamicTable", this.connection);
        const targetTable = await dtRepo.get(identifier);

        const connection = this.connection
            .select()
            .from(this.tableName)
            .where("DT_ID", targetTable.DT_ID)
            .where("DTC_NAME", dlcName);

        return await globalThis.orm
            .collection("DynamicTableCol", connection)
            .collectOne();
    }

    async copy(fromId, toId) {
        const cols = await (await this.getForTable(fromId)).fetchAll();
        const dtvRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );
        for (let i = 0; i < cols.length; i += 1) {
            const col = cols[i];
            col.DT_ID = toId;
            await this.connection(this.tableName).insert(col);
            await dtvRepo.createCol(toId, col.DTC_ID);
        }
    }

    async createCol(entity) {
        const dtvRepo = globalThis.orm.repo(
            "dynamicTableValue",
            this.connection,
        );
        await dtvRepo.createCol(entity.DT_ID, entity.DTC_ID);
        await this.connection(this.tableName).insert(entity);
    }

    async addCol(dtId, colName) {
        const dtcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        const lastColId = await this.getMaxColId(dtId);

        await dtcRepo.createCol({
            DT_ID: dtId,
            DTC_ID: lastColId + 1,
            DTC_NAME: colName,
        });
    }

    /**
     *
     * @param dtId
     * @param dtcId
     * @param newColName
     * @returns {Promise<*>}
     */
    async renameCol(dtId, dtcId, newColName) {
        const entity = this.getEntity({
            DT_ID: dtId,
            DTC_ID: dtcId,
            DTC_NAME: newColName,
        });

        return await this.store(entity);
    }
}
