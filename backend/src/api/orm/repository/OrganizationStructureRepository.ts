// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import * as OrganizationStructureConstants from "../entity/const/organizationStructureConsts";
import * as USER from "../entity/const/userConst";
import { OrganizationStructure } from "../entity/OrganizationStructure";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

import { Rest } from "../../services/Rest";

export class OrganizationStructureRepository extends BaseRepository<OrganizationStructure> {
    meta() {
        return {
            tableName: "ORGANIZATION_STRUCTURE",
            entityName: "OrganizationStructure",
            defaultAlias: "OS",
            entity: () => new OrganizationStructure(),
        };
    }

    /**
     * Used for Knex.with()
     * Returns all OrganizationStructure children units
     * Don't forget to bind MANAGER_USER_ID!
     *
     * INCLUDES THE STARTING NODES
     *
     * @returns {string}
     */
    withChildrenUnitsForManager() {
        return `
    SELECT os.*
    FROM "ORGANIZATION_STRUCTURE" os
    WHERE os."PARENT_ORGSTR_ID" IN (
        SELECT "ORGSTR_ID"
        FROM "ORGANIZATION_STRUCTURE"
        WHERE "MANAGER_USER_ID" = $1
    )

    UNION ALL

    -- Recursive: get children by walking down the org structure
    SELECT child.*
    FROM "ORGANIZATION_STRUCTURE" child
    INNER JOIN "CHILDREN_UNITS" parent ON parent."ORGSTR_ID" = child."PARENT_ORGSTR_ID"
        `;
    }

    // When calling this function, you must also include the commented 'with' below
    // to the top-most query
    withManagedUsers() {
        return `
            select "U"."USER_ID" from "USERS" "U"
            inner join "USER_ORGANIZATION_STRUCTURE" "UOS" ON "UOS"."USER_ID" = "U"."USER_ID" AND "U"."USER_ID" <> :USER_ID
            where "U"."USER_STATUS" = :USER_STATUS_ACTIVE
            and
            (
                "UOS"."ORGSTR_ID" in (select "ORGSTR_ID" from "CHILDREN_UNITS")
                or
                "U"."USER_ID" in (select "MANAGER_USER_ID" from "CHILDREN_UNITS")
            )`;
    }

    getByStatus(status) {
        let conn = this.connection
            .select([
                "O.ORGSTR_NAME",
                this.connection.raw(
                    `"U"."USER_DISPLAY_NAME" as "MANAGER_USER_NAME"`,
                ),
                "O.EXTERNAL_ID",
                "O.PARENT_IC",
                "O.ORGANIZATION_TYPE",
                "O.ADDITIONAL_ID",
                "O.EXTERNAL_STATUS",
                "O.COMPANY_IC",
                "O.ORGANIZATION_STATUS",
                "O.ORG_ID",
                "O.ORGSTR_ID",
                "O.PARENT_ORGSTR_ID",
                "O.MANAGER_USER_ID",
                "O.LOGO_URL",
            ])
            .leftJoin("USERS as U", "U.USER_ID", "O.MANAGER_USER_ID")
            .from(`${this.tableName} as O`);
        if (Array.isArray(status) && status.length > 0) {
            conn = conn.whereIn("O.ORGANIZATION_STATUS", status);
        } else {
            conn = conn.where("O.ORGANIZATION_STATUS", status);
        }

        return globalThis.orm.collection("OrganizationStructure", conn);
    }

    getById(id) {
        const collection = this.getCollection();
        if (Array.isArray(id) && id.length === 1) {
            id = id[0]; // due to incorrect behaviour of knex
        }

        if (Array.isArray(id)) {
            collection.knex.from(this.tableName).whereIn("ORGSTR_ID", id);
        } else {
            collection.knex.from(this.tableName).where("ORGSTR_ID", id);
        }

        return collection;
    }

    getFlatTreeSQL() {
        return `WITH RECURSIVE "OSTREE" AS (
    -- Anchor: top-level org units
    SELECT
        os."ORGSTR_ID",
        os."ORGSTR_NAME",
        os."MANAGER_USER_ID",
        os."PARENT_ORGSTR_ID",
        os."EXTERNAL_ID" AS "ORGSTR_EXTERNAL_ID",
        1 AS "LEVEL",
        os."ORGSTR_NAME"::TEXT AS "PATH"
    FROM "ORGANIZATION_STRUCTURE" os
    WHERE os."ORGANIZATION_STATUS" = 'A'
      AND os."PARENT_ORGSTR_ID" IS NULL

    UNION ALL

    -- Recursive: descend through org structure
    SELECT
        child."ORGSTR_ID",
        child."ORGSTR_NAME",
        child."MANAGER_USER_ID",
        child."PARENT_ORGSTR_ID",
        child."EXTERNAL_ID" AS "ORGSTR_EXTERNAL_ID",
        parent."LEVEL" + 1,
        parent."PATH" || '\`' || child."ORGSTR_NAME"
    FROM "ORGANIZATION_STRUCTURE" child
    INNER JOIN "OSTREE" parent ON parent."ORGSTR_ID" = child."PARENT_ORGSTR_ID"
    WHERE child."ORGANIZATION_STATUS" = 'A'
)

-- Main SELECT with JOIN on the recursive data
SELECT
    os."ORGSTR_ID",
    os."ORGSTR_NAME",
    os."PARENT_ORGSTR_ID",
    os."MANAGER_USER_ID",
    os."ORGSTR_EXTERNAL_ID" AS "EXTERNAL_ID",
    os."LEVEL",
    u."USER_LAST_NAME" || ' ' || u."USER_FIRST_NAME" AS "MANAGER_USER",
    os."PATH"
FROM "OSTREE" os
LEFT JOIN "USERS" u ON os."MANAGER_USER_ID" = u."USER_ID"
ORDER BY os."PATH"
`;
    }

    async getTree() {
        const orgs = await globalThis.container.client.database.callKnexRaw(
            this.getFlatTreeSQL(),
        );
        if (!Array.isArray(orgs) || orgs.length === 0) {
            return orgs;
        }

        const orgsByParent = {};
        let root;
        orgs.forEach((org) => {
            org = Rest.keysToLowerCase(org);

            org.id = org.orgstr_id;
            org.label = org.orgstr_name;
            org.tree_index = org.level;

            delete org.orgstr_id;
            delete org.orgstr_name;
            delete org.level;

            // reorganize by parent_id
            if (org.parent_orgstr_id !== null) {
                if (!orgsByParent.hasOwnProperty(org.parent_orgstr_id)) {
                    orgsByParent[org.parent_orgstr_id] = [];
                }
                orgsByParent[org.parent_orgstr_id].push(org);
            } else {
                root = org; // root org
            }
        });

        // join children, save as reference = affects original object
        function fillChildren(org, parentId) {
            if (orgsByParent.hasOwnProperty(parentId)) {
                org.children = orgsByParent[parentId];
            } else {
                org.children = [];
            }

            if (org.children.length > 0) {
                org.children.forEach((subOrg) => {
                    fillChildren(subOrg, subOrg.id);
                });
            }
        }

        fillChildren(root, root.id); // prepared output and root ID
        return root;
    }

    async deleteOrgStr(entity) {
        if (Number(entity.id) <= 1) {
            throw new UserException(
                "This organization unit cannot be deleted!",
                "BAD_REQUEST",
            );
        }

        const userRepo = globalThis.orm.repo("user", this.connection);
        const orgUnits = await this.getChildrenUnits(entity.id);

        const deletedOrgUnits: number[] = [];
        for (const orgUnit of orgUnits) {
            orgUnit.ORGANIZATION_STATUS = "D";
            const orgEntity = this.getEntity(orgUnit);
            deletedOrgUnits.push(await this.store(orgEntity));
        }

        const usersToMove: number[] = [];
        for (const orgUnit of deletedOrgUnits) {
            const userIds = await userRepo
                .getUserListByOrgStructure({ ORGSTR_ID: orgUnit }, true)
                .pluck("U.USER_ID");
            usersToMove.push(...userIds);
        }

        const flattenedUsers = _.flattenDeep(usersToMove);

        return await userRepo.updateOrganization(
            flattenedUsers,
            OrganizationStructureConstants.UNASSIGNED,
        );
    }

    async setManager(orgstrId, userId) {
        const repo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );

        if (!Array.isArray(orgstrId)) {
            orgstrId = [orgstrId];
        }

        for (const orgId of orgstrId) {
            const { entity } = repo;
            entity.ORGSTR_ID = orgId;
            entity.MANAGER_USER_ID = userId;

            await this.store(entity);
        }
    }

    async store(entity) {
        const currentEntity = await this.getById(entity.ORGSTR_ID).collectOne();

        if (this.connection !== globalThis.database) {
            this.connection.once("committed", () => {
                globalThis.orm
                    .repo("ExternalRight")
                    .onManagerChanged(
                        entity.MANAGER_USER_ID,
                        currentEntity.MANAGER_USER_ID,
                        false,
                    );
            });
        } else {
            globalThis.tasLogger.warning(
                "Dynamic Rights not recalculated upon Organization Structure update. Connection is the same as globalThis.database. Not running in transaction",
            );
        }

        return await super.store(entity);
    }

    async getAllChildrenUnits(orgstrId) {
        return await globalThis.container.client.database.callKnexRaw(
            `
            WITH RECURSIVE org_hierarchy AS (
    -- Anchor member: starting from the given ORGSTR_ID
    SELECT *
    FROM "ORGANIZATION_STRUCTURE"
    WHERE "ORGSTR_ID" = :orgstrId

    UNION ALL

    -- Recursive member: fetch children
    SELECT child.*
    FROM "ORGANIZATION_STRUCTURE" child
    INNER JOIN org_hierarchy parent ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
)
SELECT *
FROM org_hierarchy;
`,
            { orgstrId },
            this.connection,
        );
    }

    getChildrenUnits(orgstr_id) {
        const columns = [
            "OS.ORGSTR_NAME",
            "OS.ORGSTR_ID",
            "OS.PARENT_ORGSTR_ID",
            "OS.MANAGER_USER_ID",
            "OS.COMPANY_IC",
            "OS.ORGANIZATION_TYPE",
            "OS.EXTERNAL_ID",
            this.connection.raw(
                `"U"."USER_DISPLAY_NAME" AS "MANAGER_USER_NAME"`,
            ),
            this.connection.raw(`"OS_PARENT"."ORGSTR_NAME" AS "PARENT"`),
            this.connection.raw(
                `"OS_PARENT"."EXTERNAL_ID" AS "PARENT_EXTERNAL_ID"`,
            ),
        ];

        return this.connection
            .withRecursive("OS", (qb) => {
                qb.select("*")
                    .from("ORGANIZATION_STRUCTURE")
                    .where("ORGSTR_ID", "=", orgstr_id)
                    .unionAll(function () {
                        this.select("child.*")
                            .from("ORGANIZATION_STRUCTURE as child")
                            .join(
                                "OS as parent",
                                "child.PARENT_ORGSTR_ID",
                                "parent.ORGSTR_ID",
                            );
                    });
            })
            .select(columns)
            .from("OS")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as OS_PARENT",
                "OS.PARENT_ORGSTR_ID",
                "OS_PARENT.ORGSTR_ID",
            )
            .leftJoin("USERS as U", "U.USER_ID", "OS.MANAGER_USER_ID")
            .where("OS.ORGANIZATION_STATUS", "A");
    }

    getParentUnits(orgstr_id) {
        const columns = [
            "OS.ORGSTR_NAME",
            "OS.ORGSTR_ID",
            "OS.PARENT_ORGSTR_ID",
            "OS.MANAGER_USER_ID",
            "OS.COMPANY_IC",
            "OS.ORGANIZATION_TYPE",
            this.connection.raw(`"U"."USER_DISPLAY_NAME" "MANAGER_USER_NAME"`),
            this.connection.raw(`"OS_PARENT"."ORGSTR_NAME" AS "PARENT"`),
            this.connection.raw(
                `"OS_PARENT"."EXTERNAL_ID" AS "PARENT_EXTERNAL_ID"`,
            ),
        ];

        return this.connection
            .select(columns)
            .from(
                this.connection.raw(
                    `
        WITH RECURSIVE "OS" AS (
    -- Anchor: start with given ORGSTR_ID
    SELECT *
    FROM "ORGANIZATION_STRUCTURE"
    WHERE "ORGSTR_ID" = $1

    UNION ALL

    -- Recursive: follow parent links up the hierarchy
    SELECT parent.*
    FROM "ORGANIZATION_STRUCTURE" parent
    INNER JOIN "OS" child ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
)
SELECT *
FROM "OS";

    `,
                    [orgstr_id],
                ),
            )
            .as("OS")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as OS_PARENT",
                "OS.PARENT_ORGSTR_ID",
                "OS_PARENT.ORGSTR_ID",
            )
            .leftJoin("USERS as U", "U.USER_ID", "OS.MANAGER_USER_ID")
            .where("OS.ORGANIZATION_STATUS", "A")
            .as("OS");
    }

    getOrgStructure(orgStrId = "IS NULL") {
        if (orgStrId !== "IS NULL" && typeof orgStrId === "string") {
            throw new UserException(
                "Organization Structure ID must be a number!",
                "BAD_REQUEST",
            );
        }
        if (orgStrId !== "IS NULL") {
            orgStrId = `= ${orgStrId}`;
        }
        return globalThis.container.client.database.callKnexRaw(
            `
                WITH RECURSIVE "OSTREE" AS (
    -- Anchor: start with the given orgStrId as parent
    SELECT 
        os."ORGSTR_ID",
        os."ORGSTR_NAME",
        os."MANAGER_USER_ID",
        os."PARENT_ORGSTR_ID",
        1 AS "LEVEL",
        os."ORGSTR_NAME"::TEXT AS "PATH"
    FROM "ORGANIZATION_STRUCTURE" os
    WHERE os."ORGANIZATION_STATUS" = 'A'
      AND os."PARENT_ORGSTR_ID" = $1  -- Replace with your orgStrId param

    UNION ALL

    -- Recursive: walk down the hierarchy
    SELECT 
        child."ORGSTR_ID",
        child."ORGSTR_NAME",
        child."MANAGER_USER_ID",
        child."PARENT_ORGSTR_ID",
        parent."LEVEL" + 1,
        parent."PATH" || '\`' || child."ORGSTR_NAME"
    FROM "ORGANIZATION_STRUCTURE" child
    INNER JOIN "OSTREE" parent ON parent."ORGSTR_ID" = child."PARENT_ORGSTR_ID"
    WHERE child."ORGANIZATION_STATUS" = 'A'
)

-- Final select with join to USERS
SELECT 
    o."ORGSTR_ID",
    o."ORGSTR_NAME",
    o."PARENT_ORGSTR_ID",
    o."MANAGER_USER_ID",
    o."LEVEL",
    u."USER_DISPLAY_NAME" AS "MANAGER_USER"
FROM "OSTREE" o
LEFT JOIN "USERS" u ON o."MANAGER_USER_ID" = u."USER_ID"
ORDER BY o."PATH";

       `,
            undefined,
            this.connection,
        );
    }

    async getManagedUsers(userId: number): Promise<string[]> {
        // Get all managed organizations and their users
        const uos = await globalThis.container.client.database.callKnexRaw(
            `
                WITH RECURSIVE org_tree AS (
                    SELECT os."ORGSTR_ID"
                    FROM "ORGANIZATION_STRUCTURE" os
                    WHERE os."MANAGER_USER_ID" = :userId
    
                    UNION ALL
    
                    SELECT child."ORGSTR_ID"
                    FROM "ORGANIZATION_STRUCTURE" child
                    INNER JOIN org_tree parent ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
                )
                SELECT uos."USER_ID"
                FROM org_tree os
                LEFT JOIN "USER_ORGANIZATION_STRUCTURE" uos ON uos."ORGSTR_ID" = os."ORGSTR_ID"
                LEFT JOIN "USERS" u ON uos."USER_ID" = u."USER_ID"
                WHERE uos."USER_ID" IS NOT NULL
                AND u."USER_STATUS" = 'A';
                `,
            { userId },
            this.connection,
        );

        let users: string[] = [];
        if (Array.isArray(uos) && uos.length > 0) {
            users = uos.map((user: any) => user.USER_ID);
        }

        // Get all managers in the org tree
        const managerUos =
            await globalThis.container.client.database.callKnexRaw(
                `
                WITH RECURSIVE org_tree AS (
                    SELECT os."ORGSTR_ID", os."MANAGER_USER_ID"
                    FROM "ORGANIZATION_STRUCTURE" os
                    WHERE os."MANAGER_USER_ID" = :userId
    
                    UNION ALL
    
                    SELECT child."ORGSTR_ID", child."MANAGER_USER_ID"
                    FROM "ORGANIZATION_STRUCTURE" child
                    INNER JOIN org_tree parent ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
                )
                SELECT DISTINCT ot."MANAGER_USER_ID"
                FROM org_tree ot
                LEFT JOIN "USERS" u ON u."USER_ID" = ot."MANAGER_USER_ID"
                WHERE ot."MANAGER_USER_ID" IS NOT NULL
                AND u."USER_STATUS" = 'A';
                `,
                { userId },
                this.connection,
            );

        let managers: string[] = [];
        if (Array.isArray(managerUos) && managerUos.length > 0) {
            managers = managerUos.map(
                (manager: any) => manager.MANAGER_USER_ID,
            );
        }

        const allUsers = _.uniq([...users, ...managers]);
        return _.pull(allUsers, userId);
    }

    /**
     * Get collection by name.
     * @param orgstrName
     * @returns {*}
     */
    getByNameOrgStr(orgstrName, containsDeleted = true) {
        let conn = this.connection
            .select()
            .from(this.tableName)
            .where("ORGSTR_NAME", orgstrName);
        if (!containsDeleted) {
            conn = conn.where("ORGANIZATION_STATUS", "A");
        }
        return globalThis.orm.collection("organizationStructure", conn);
    }

    /**
     * Get parents up to root.
     */
    getAllParents(orgstrId) {
        return this.connection
            .raw(
                `
WITH RECURSIVE org_tree AS (
    -- Anchor: start from the given ORGSTR_ID
    SELECT *
    FROM "ORGANIZATION_STRUCTURE"
    WHERE "ORGANIZATION_STATUS" = 'A'
      AND "ORGSTR_ID" = $1  -- Replace $1 with your parameter (formerly :ORGSTR_ID)

    UNION ALL

    -- Recursive: follow child orgs
    SELECT child.*
    FROM "ORGANIZATION_STRUCTURE" child
    JOIN org_tree parent ON child."PARENT_ORGSTR_ID" = parent."ORGSTR_ID"
    WHERE child."ORGANIZATION_STATUS" = 'A'
)
SELECT *
FROM org_tree;
        `,
                { ORGSTR_ID: orgstrId },
            )
            .then((rows) => this.castRows(rows, this.entity.attributes()));
    }

    async getByAttr(attrName, attrValue, singleResult = true) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("ORGANIZATION_STATUS", "A")
            .where(attrName.toUpperCase(), attrValue);
        const collection = globalThis.orm.collection(
            "OrganizationStructure",
            conn,
        );
        const rows = await collection.collectAll();
        if (singleResult) {
            return rows.length > 0 ? rows[0] : null;
        }
        return rows;
    }

    async createIfNotExists(orgName, parent) {
        const orgstr = await this.getByAttr("ORGSTR_NAME", orgName);

        if (orgstr === null) {
            const entity = this.getEntity({
                ORGSTR_NAME: orgName,
                PARENT_ORGSTR_ID: parent,
            });
            return await this.store(entity);
        }

        return orgstr.ORGSTR_ID;
    }

    /**
     * Finds all managers
     *
     * @returns {Knex.Builder}
     */
    getManagers() {
        // TODO: Don't return password?
        return this.connection
            .select()
            .from("USERS")
            .whereIn("USERS.USER_STATUS", [
                USER.STATUS_ACTIVE,
                USER.STATUS_LOCKED,
            ])
            .innerJoin(
                `${this.tableName} as ORGSTR`,
                "ORGSTR.MANAGER_USER_ID",
                "=",
                "USERS.USER_ID",
            );
    }

    findManagerInUsersOrganization(usersQuery, userIdColumn) {
        const conn = this.connection
            .select(["OS.MANAGER_USER_ID", "UO.USER_ID"])
            .from("ORGANIZATION_STRUCTURE as OS")
            .leftJoin(
                "USER_ORGANIZATION_STRUCTURE as UO",
                "OS.ORGSTR_ID",
                "UO.ORGSTR_ID",
            )
            .whereIn("OS.ORGSTR_ID", function () {
                this.select("UO.ORGSTR_ID")
                    .from("USER_ORGANIZATION_STRUCTURE as UO")
                    .whereIn("UO.USER_ID", (builder) => {
                        builder
                            .select(userIdColumn)
                            .from(usersQuery.as("ignored_alias"));
                    });
            });

        return globalThis.orm.collection("OrganizationStructure", conn);
    }

    /**
     * Finds all managers in the OrganizationStructure @level
     *
     * @param level
     * @returns {Knex.Builder}
     */
    getManagersInLevel(level) {
        return this.getOrgStructure().then((orgStrTree) => {
            const managerIds = [];
            let orgStrInLevel = _.filter(orgStrTree, {
                LEVEL: level,
            });
            orgStrInLevel = _.filter(
                orgStrInLevel,
                (orgStr) => orgStr.MANAGER_USER_ID !== null,
            );
            orgStrInLevel.forEach((orgStr) => {
                if (orgStr.MANAGER_USER_ID !== null) {
                    managerIds.push(orgStr.MANAGER_USER_ID);
                }
            });
            return this.connection
                .select()
                .from("USERS")
                .innerJoin(
                    `${this.tableName} as ORGSTR`,
                    "ORGSTR.MANAGER_USER_ID",
                    "=",
                    "USERS.USER_ID",
                )
                .whereIn(
                    "MANAGER_USER_ID",
                    Array.isArray(managerIds) ? managerIds : [managerIds],
                );
        });
    }

    /**
     * Finds all OrganizationStructures in the same @param level
     *
     * @param level
     * @returns Array<integer> OrganizationStructure IDs
     */
    getByLevel(level) {
        return this.getOrgStructure().then((orgStrs) => {
            const orgStrsInLevel = _.filter(orgStrs, {
                LEVEL: parseInt(level, 10),
            });
            return orgStrsInLevel;
        });
    }

    /**
     * Finds all OrganizationStructures where @attributeName = @attributeValue
     *
     * @param attributeName
     * @param attributeValue
     * @returns {Knex.Builder}
     */
    getByAttribute(attributeName, attributeValue) {
        return this.connection
            .select()
            .from(this.tableName)
            .where(
                "ORGANIZATION_STATUS",
                OrganizationStructureConstants.STATUS_ACTIVE,
            )
            .andWhereRaw(
                `UPPER (${attributeName}) LIKE UPPER ('${attributeValue}')`,
            );
    }

    findOneByAttribute(attributeName, attributeValue) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where(attributeName, attributeValue);
        const collection = globalThis.orm.collection(
            "OrganizationStructure",
            conn,
        );
        return collection.collectAll().then((rows) => {
            if (Array.isArray(rows) && rows.length > 0) {
                return rows[0];
            }
            return null;
        });
    }

    /**
     * Returns all org.units (chairs) for given user
     *
     * @param userId
     * @return {BaseCollection}
     */
    getUsersChairs(userId) {
        const repoUser = globalThis.orm.repo("user", this.connection);
        return this.createCollection(
            repoUser.getChairs().where("USER_ID", userId),
        );
    }

    lockOrganizations(orgsId) {
        if (!orgsId || orgsId.length === 0) {
            return;
        }
        // Unlock all, lock them next time
        return this.connection
            .select()
            .from(this.tableName)
            .where(
                "ORGANIZATION_STATUS",
                OrganizationStructureConstants.STATUS_DELETED,
            )
            .update({
                ORGANIZATION_STATUS:
                    OrganizationStructureConstants.STATUS_ACTIVE,
            })
            .then(() => {
                let connection = this.connection.select().from(this.tableName);

                // Prevent: ORA-01795: maximum number of expressions in a list is 1000
                const chunked = _.chunk(orgsId, 999);
                chunked.forEach((chunk) => {
                    connection = connection.orWhereIn(
                        "ORGSTR_ID",
                        globalThis.database.raw(chunk),
                    );
                });
                return connection.update({
                    ORGANIZATION_STATUS:
                        OrganizationStructureConstants.STATUS_DELETED,
                });
            });
    }

    // t3b-905 Metoda na import organizační struktury
    async sync(ident, items) {
        if (!items || !Array.isArray(items)) {
            throw new UserException("Invalid param 'items'.");
        }
        if (!ident) {
            throw new UserException("Invalid param 'ident'");
        }
        if (ident === "ORGSTR_ID") {
            throw new UserException(
                "ident must not be 'ORGSTR_ID'. Please copy ORGSTR_ID to EXTERNAL_ID and use sync again with ident='EXTERNAL_ID'.",
            );
        }

        // Create all organizations. Skip parentId for now.
        for (const syncOrgItem of items) {
            const dbEntity = await this.findOneByAttribute(
                ident,
                syncOrgItem.id,
            ); // TODO optimision -> load all from db, add to map if new created.
            if (dbEntity === null) {
                // Insert
                const data = {
                    ORGSTR_NAME: syncOrgItem.name,
                };
                data[ident] = syncOrgItem.id;

                globalThis.tasLogger.info(
                    `New organization '${syncOrgItem.name}'`,
                );
                await this.store(this.getEntity(data));
                // Update
            } else if (dbEntity.ORGSTR_NAME !== syncOrgItem.name) {
                // Only if some change
                globalThis.tasLogger.info(
                    `Updating '${dbEntity.ORGSTR_NAME}' -> '${syncOrgItem.name}'`,
                );
                dbEntity.ORGSTR_NAME = syncOrgItem.name;
                await this.store(dbEntity);
            }
        }

        // Fill parentId. If parentId not found there is missing organization.
        const rootOrg = await this.findOneByAttribute("ORGSTR_NAME", "Root");
        if (!rootOrg) {
            throw new InternalException("Root not found.");
        }

        // No lock Root and Unassigned
        const noLock = [rootOrg.ORGSTR_ID];
        const unassignedOrg = await this.findOneByAttribute(
            "ORGSTR_NAME",
            "Unassigned",
        );
        if (unassignedOrg) {
            noLock.push(unassignedOrg.ORGSTR_ID);
        }

        for (const syncOrgItem of items) {
            const dbEntity = await this.findOneByAttribute(
                ident,
                syncOrgItem.id,
            ); // TODO optimision -> load all db entities. Search in entities map.
            noLock.push(dbEntity.ORGSTR_ID);

            if (syncOrgItem.parentId || syncOrgItem.parentId === 0) {
                const parentDbEntity = await this.findOneByAttribute(
                    ident,
                    syncOrgItem.parentId,
                );
                if (parentDbEntity === null) {
                    globalThis.tasLogger.warning(
                        `Could not find parent organization for '${syncOrgItem.name}' with parentId=${syncOrgItem.parentId}`,
                    );
                    continue;
                }
                dbEntity.PARENT_ORGSTR_ID = parentDbEntity.ORGSTR_ID;
            } else {
                dbEntity.PARENT_ORGSTR_ID = rootOrg.ORGSTR_ID;
            }
            dbEntity.ORGANIZATION_STATUS = "A";

            await this.store(dbEntity);
        }

        // Lock users
        this.getCollection(["ORGSTR_ID"]).knex.whereNotIn("ORGSTR_NAME", [
            "Root",
            "Unassigned",
        ]); // -1 Unsassigned, 0|1 Root
        const allOrgs = _.map(
            await this.getCollection(["ORGSTR_ID"]).fetchAll(),
            "ORGSTR_ID",
        );
        const lockOrgs = _.difference(allOrgs, noLock);
        globalThis.tasLogger.info("Locking organizations.", {
            lockUsers: lockOrgs,
            noLock,
        });
        await this.lockOrganizations(lockOrgs);

        return true;
    }

    /**
     * Fill managers from parent organizations.
     */
    async inheritManagers() {
        const allOrgs = _.map(
            await this.getCollection(["ORGSTR_ID"]).fetchAll(),
            "ORGSTR_ID",
        );

        if (Array.isArray(allOrgs) && allOrgs.length > 0) {
            for (const org of allOrgs) {
                const manager = await this.findParentManager(org);
                if (manager) {
                    await this.connection
                        .select()
                        .from(this.tableName)
                        .where("ORGSTR_ID", org)
                        .update({
                            MANAGER_USER_ID: manager,
                        });
                }
            }
        }
    }

    async findParentManager(orgId) {
        const org = await this.get(orgId);

        // If parent org has manager return.
        if (org.MANAGER_USER_ID) {
            return org.MANAGER_USER_ID;
        }

        // Search parent, parent, parent until root.
        if (org.PARENT_ORGSTR_ID) {
            return this.findParentManager(org.PARENT_ORGSTR_ID);
        }

        // There is no manager in parent orgs.
        return null;
    }

    async getOrganizationInfo(orgstrId) {
        const organization = await this.connection
            .select(["ORGSTR_ID", "ORGSTR_NAME", "PATH"])
            .from("V_ORG_STRUCT as VOR")
            .where("VOR.ORGSTR_ID", orgstrId)
            .first();

        const [, ...path] = organization.PATH.split("/");
        organization.ORGSTR_PATH = path;
        delete organization.PATH;
        return organization;
    }

    getPathForUser(userId) {
        return this.connection
            .select("PATH")
            .from("V_ORG_STRUCT")
            .where("ORGSTR_ID", (builder) => {
                builder
                    .select("ORGSTR_ID")
                    .from("USER_ORGANIZATION_STRUCTURE")
                    .where("USER_ID", userId);
            });
    }

    getOrgChild(orgstrId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("PARENT_ORGSTR_ID", orgstrId)
            .where(
                "ORGANIZATION_STATUS",
                OrganizationStructureConstants.STATUS_ACTIVE,
            );
    }

    async createOrgStr(
        orgstr_name,
        parent_orgstr_id,
        users: number[] = [],
        manager_user_id: number | null,
        organization_type: string | null,
        logo_url: string | null,
    ) {
        const data = {
            orgstr_name,
            parent_orgstr_id,
            users,
            manager_user_id,
            organization_type,
            logo_url,
        };

        const hasUniqueName = await this.isOrgStrNameUnique(
            orgstr_name,
            parent_orgstr_id,
        );
        if (!hasUniqueName) {
            throw new UserException(
                "Ogranization with given name already exists.",
                orgstr_name,
            );
        }

        return await this.saveOrgStrData(data);
    }

    async saveOrgStrData(data) {
        const { orgstr_name, parent_orgstr_id, orgstr_id } = data;

        // update existing orgStr - check name uniqueness (name + parent_orgstr)
        if (orgstr_id && orgstr_name) {
            const currentOrgStr = await this.getById(orgstr_id).collectOne();
            if (currentOrgStr && currentOrgStr.ORGSTR_NAME !== orgstr_name) {
                const hasUniqueName = await this.isOrgStrNameUnique(
                    orgstr_name,
                    parent_orgstr_id,
                );
                if (!hasUniqueName) {
                    throw new UserException(
                        "Ogranization with given name already exists.",
                        orgstr_name,
                    );
                }
            }
        }

        const entity = this.getEntity(
            _.mapKeys(data, (_value, key) => key.toUpperCase()),
        );
        const userRepo = globalThis.orm.repo("user", this.connection);

        const orgstrId = await this.store(entity);
        entity.ORGSTR_ID = orgstrId;

        const { users } = data;
        if (typeof users !== "undefined" && Array.isArray(users)) {
            // @t3b-631
            // Move Users that have been removed from the current ORGSTR to the 'UNASSIGNED' ORGSTR
            const foundUsers = await this.connection
                .pluck("USER_ID")
                .from("USER_ORGANIZATION_STRUCTURE")
                .where("ORGSTR_ID", orgstrId);
            // .then(foundUsers => {
            // Assign all removed Users to the Unassigned organization unit
            const unassignedUsers = _.difference(foundUsers, users);
            await userRepo.updateOrganization(
                unassignedUsers,
                OrganizationStructureConstants.UNASSIGNED,
            );
            await userRepo.setOrganization(users, orgstrId);
        }

        return entity.ORGSTR_ID;
    }

    async isOrgStrNameUnique(orgstr_name, parent_orgstr_id) {
        const foundOrgStrs = await this.getByNameOrgStr(orgstr_name, false);
        const foundCount = await foundOrgStrs
            .filter(`"PARENT_ORGSTR_ID"<eq>${parent_orgstr_id}`)
            .getTotalCount();
        return foundCount <= 0;
    }
}
