// @ts-nocheck
// @ts-nocheck
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import crypto from "node:crypto";
import { RegisteredMobileDevice } from "../entity/RegisteredMobileDevice";
import { BaseRepository } from "./BaseRepository";
import { UtilsService } from "../../services/UtilsService";
import { TooManyMobileDevices } from "../../../utils/errorHandling/exceptions/tooManyMobileDevices";
import { AuthException } from "../../../utils/errorHandling/exceptions/authException";

export class RegisteredMobileDeviceRepository extends BaseRepository<RegisteredMobileDevice> {
    meta() {
        return {
            tableName: "REGISTERED_MOBILE_DEVICES",
            entityName: "RegisteredMobileDevice",
            entity: () => new RegisteredMobileDevice(),
        };
    }

    async register(userId, name) {
        const [row] = await this.connection
            .count("* as cnt")
            .from(this.tableName)
            .where("RMD_USER_ID", userId);
        if (row && row.cnt >= 10) {
            throw new TooManyMobileDevices();
        }

        const token = crypto.randomUUID();
        const entity = this.getEntity({
            RMD_USER_ID: userId,
            RMD_NAME: name,
            RMD_VERIFIED: "N",
            RMD_CREATED_AT: new Date(),
            RMD_LOGIN_TOKEN: token,
            RMD_LOGIN_COUNT: 0,
        });
        const id = await this.store(entity);
        return {
            RMD_ID: id,
            RMD_LOGIN_TOKEN: token,
        };
    }

    async confirm(rmdToken) {
        const rmdEntity = await this.getRmdEntity(rmdToken);

        // TODO some token timeout.

        rmdEntity.RMD_VERIFIED = "Y";
        await this.store(rmdEntity);

        return rmdEntity.RMD_USER_ID;
    }

    async setToken(rmdId, fcmToken) {
        const rmdEntity = await this.getRmdEntity(rmdId);
        rmdEntity.RMD_FCM_TOKEN = fcmToken;
        return await this.store(rmdEntity);
    }

    async unregister(id) {
        await this.connection
            .select()
            .from(this.tableName)
            .where("RMD_ID", id)
            .delete();
    }

    getForUser(userId) {
        const collection = this.getCollection();
        collection.knex.where("RMD_USER_ID", userId);
        return collection;
    }

    async getRmdEntity(rmdIdent) {
        const rmdData = UtilsService.isNumericString(rmdIdent)
            ? await this.getByAttr("RMD_ID", rmdIdent)
            : await this.getByAttr("RMD_LOGIN_TOKEN", rmdIdent);
        if (!rmdData) {
            throw new UserException("Invalid registration token.");
        }

        return this.getEntity(rmdData);
    }

    deleteForUser(userId) {
        if (!userId) {
            throw new Error(
                "User not defined in RegisteredMobileDeviceRepository.deleteForUser.",
            );
        }
        return this.connection
            .select()
            .from(this.tableName)
            .where("RMD_USER_ID", userId)
            .delete();
    }

    async updateLastLogin(rmdToken) {
        if (!rmdToken) {
            return;
        }
        await this.connection
            .select()
            .from(this.tableName)
            .where("RMD_LOGIN_TOKEN", rmdToken)
            .update({
                RMD_LOGIN_COUNT: this.connection.raw("?? + 1", [
                    `RMD_LOGIN_COUNT`,
                ]),
                RMD_LAST_LOGIN: new Date(),
            });
    }

    rename(rmdId, newName) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("RMD_ID", rmdId)
            .update({
                RMD_NAME: newName,
            });
    }

    async getAllByAttrAndIncreaseBadgeCount(
        userId: number,
    ): Promise<{ token: string; badgeCounter: number }[]> {
        const tokens: RegisteredMobileDevice[] = await this.getAllByAttr(
            "RMD_USER_ID",
            userId,
        );

        const filteredTokens = tokens.filter(
            (item, index) =>
                !!item && tokens[index].RMD_MUTE_NOTIFICATION !== true,
        );
        const result: { token: string; badgeCounter: number }[] = [];
        for (const token of filteredTokens) {
            await this.increaseBadge(token.RMD_ID, token.BADGE_COUNTER);
            result.push({
                token: token.RMD_FCM_TOKEN,
                badgeCounter: token.BADGE_COUNTER + 1,
            });
        }

        return result;
    }

    async increaseBadge(
        rmdId: number,
        increaseBadgeCounter: boolean,
    ): Promise<void> {
        await this.connection
            .from(this.tableName)
            .where("RMD_ID", rmdId)
            .update({
                BADGE_COUNTER: increaseBadgeCounter + 1,
            });
    }

    async resetBadge(token: string): Promise<void> {
        await this.connection
            .from(this.tableName)
            .where("RMD_LOGIN_TOKEN", token)
            .update({
                BADGE_COUNTER: 0,
            });
    }

    async muteNotification(
        rmdId: number,
        muteNotification: boolean,
    ): Promise<void> {
        await this.connection
            .from(this.tableName)
            .where("RMD_ID", rmdId)
            .update({
                RMD_MUTE_NOTIFICATION: muteNotification,
            });
    }

    async verify(token) {
        const row = await this.connection
            .first(["RMD_USER_ID", "RMD_VERIFIED"])
            .from(this.tableName)
            .where("RMD_LOGIN_TOKEN", token);

        if (!row) {
            throw new AuthException("Authorization token not found.");
        }

        if (row.RMD_VERIFIED !== "Y") {
            await this.confirm(token);
        }

        return {
            user_id: row.RMD_USER_ID,
            logoutParams: {
                url: "/authenticate",
            },
        };
    }

    async hasFCMToken(token: string): Promise<boolean> {
        const row = await this.connection
            .first(["RMD_FCM_TOKEN"])
            .from(this.tableName)
            .where("RMD_LOGIN_TOKEN", token);

        if (!row) {
            return false;
        }

        return row.RMD_FCM_TOKEN !== "Y";
    }

    async getUsersWithPairedDevices(): Promise<Set<any>> {
        const result: Record<string, any>[] = await this.connection
            .select("RMD_USER_ID")
            .from(this.tableName)
            .whereNotNull("RMD_USER_ID");

        const uniqueUsers = new Set(result.map((row) => row.RMD_USER_ID));

        return uniqueUsers;
    }
}
