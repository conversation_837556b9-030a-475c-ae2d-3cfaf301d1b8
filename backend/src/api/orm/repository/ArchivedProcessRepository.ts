import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as PROCESS from "../entity/const/processConst";
import * as TaskConstants from "../entity/const/taskConst";
import { Role } from "../entity/Role";
import * as TASK from "../entity/const/taskConst";
import * as TAG from "../entity/const/dmsTagConsts";
import * as ProcHistoryConst from "../entity/const/processHistoryConst";
import { Process } from "../entity/Process";
import { BaseCollection } from "../BaseCollection";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class ArchivedProcessRepository extends BaseRepository<Process> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_PROCESSES",
            entityName: "archivedProcess",
            defaultAlias: "IP",
            entity: () => new Process(),
        };
    }

    async collectProcess(
        iprocId: number,
        throwError: boolean = false,
    ): Promise<Process> {
        const process = await (await this.getProcess(iprocId)).collectOne();

        if (throwError && process === null) {
            throw new InternalException(`Process '${iprocId}' not found`);
        }
        return process;
    }

    getProcess(iprocId: number): BaseCollection<Process> {
        const extraColumns: any[] = [];

        // TTask translations
        const tprocEntity = globalThis.orm
            .repo("templateProcess", this.connection)
            .getEntity();
        let attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        // Header translations
        const headerEntity = globalThis.orm
            .repo("header", this.connection)
            .getEntity();
        attrs = headerEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TH.${attrName}`);
            }
        });

        const columns = [
            "TH.HEADER_CODE",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
            "IP.ORG_ID",
            "IP.IPROC_ID",
            "IP.TPROC_ID",
            "IP.IPROC_NAME",
            "IP.IPROC_DESCRIPTION",
            "IP.IPROC_NOTES",
            "IP.IPROC_INST_OWNER_USER_ID",
            "IP.IPROC_STATUS",
            "IP.IPROC_ACTUAL_START_DATE",
            "IP.IPROC_ACTUAL_FINISH_DATE",
            "IP.IPROC_DUE_DATE_FINISH",
            "IP.IPROC_PRIORITY",
            "IP.IPROC_SUMMARY",
            "IP.IPROC_MAIN_IPROC_ID",
            "IP.IPROC_VIS_ROLE_ID",
            "U1.USER_ID",
            "U1.USER_NAME as IPROC_OWNER_USER_NAME",
            "TP.TPROC_DESCRIPTION",
            this.connection.raw(
                `"U1"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U1"."USER_FIRST_NAME" as IPROC_OWNER_FULL_NAME`,
            ),
            "U1.USER_DISPLAY_NAME as IPROC_OWNER_DISPLAY_NAME",
            "TP.TPROC_NAME",
            this.connection.raw(
                'CASE WHEN "IP"."IPROC_STATUS" = \'A\' THEN 1 ELSE 0 END C_HEV',
            ),
            "IP.IPROC_CASE_STATUS",
        ].concat(extraColumns);

        const conn = this.connection
            .select(columns)
            .from(`${this.tableName} as IP`)
            .leftJoin(
                "USERS AS U1",
                "U1.USER_ID",
                "IP.IPROC_INST_OWNER_USER_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .where("IP.IPROC_ID", iprocId);

        return this.createCollection(conn);
    }

    async getProcessList(userId: number): Promise<BaseCollection<Process>> {
        const extraColumns = [];

        // CASE_STATUS translations
        const caseStatusEntity = globalThis.orm
            .repo("caseStatus", this.connection)
            .getEntity();
        extraColumns.push(
            ...caseStatusEntity.getTranslatedProperties([], "CS"),
        );

        // TTask translations
        const tprocEntity = globalThis.orm
            .repo("templateProcess", this.connection)
            .getEntity();
        let attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        // iProc translations
        const iProcEntity = globalThis.orm
            .repo("archivedProcess", this.connection)
            .getEntity();
        attrs = iProcEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
            }
        });

        // Header translations
        const headerEntity = globalThis.orm
            .repo("header", this.connection)
            .getEntity();
        attrs = headerEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TH.${attrName}`);
            }
        });

        const c = this.connection;

        // let  self = this;
        const columns = [
            "TH.HEADER_CODE",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
            "IP.ORG_ID",
            "IP.IPROC_ID",
            "IP.TPROC_ID",
            "IP.IPROC_NAME",
            "IP.IPROC_DESCRIPTION",
            "IP.IPROC_NOTES",
            "IP.IPROC_INST_OWNER_USER_ID",
            "IP.IPROC_STATUS",
            "IP.IPROC_CASE_STATUS",
            "IP.IPROC_ACTUAL_START_DATE",
            "IP.IPROC_ACTUAL_FINISH_DATE",
            "IP.IPROC_DUE_DATE_FINISH",
            "IP.IPROC_PRIORITY",
            "IP.IPROC_SUMMARY",
            "IP.IPROC_MAIN_IPROC_ID",
            "IP.IPROC_VIS_ROLE_ID",
            "U1.USER_ID",
            "TP.TPROC_DESCRIPTION",
            this.connection.raw(
                `"U1"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U1"."USER_FIRST_NAME" as IPROC_OWNER_FULL_NAME`,
            ),
            "U1.USER_DISPLAY_NAME as IPROC_OWNER_DISPLAY_NAME",
            "TP.TPROC_NAME",
            this.connection.raw(
                'CASE WHEN -1 IN (SELECT "ROLE_ID" FROM "USER_ROLES" WHERE "USER_ID" = ? ) OR "IP"."IPROC_INST_OWNER_USER_ID" = ? THEN 1 ELSE 0 END "C_SUS"',
                [userId, userId],
            ),
            this.connection.raw(
                'CASE WHEN "IP"."IPROC_STATUS" = \'A\' THEN 1 ELSE 0 END C_HEV',
            ),
        ].concat(extraColumns);

        let conn = this.connection
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(columns)
            .from(`${this.tableName} as IP`)
            .leftJoin(
                "USERS AS U1",
                "U1.USER_ID",
                "IP.IPROC_INST_OWNER_USER_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .leftJoin("CASE_STATUSES as CS", (cStats) => {
                cStats
                    .on("CS.TPROC_ID", "=", "TP.TPROC_ID")
                    .andOn("CS.CS_NAME", "=", "IP.IPROC_CASE_STATUS");
            })
            .as("IP");
        // TODO ADMIN_DISABLED feature.

        const roleRelation = await globalThis.orm
            .repo("user", this.connection)
            .getRoleRelation(userId, [
                Role.consts.INSPECTOR,
                Role.consts.GLOBAL_SUPERVISOR,
            ]);
        // @ts-expect-error dyno
        const inspector = roleRelation[Role.consts.INSPECTOR];
        // @ts-expect-error dyno
        const globalSupervisor = roleRelation[Role.consts.GLOBAL_SUPERVISOR];
        if (!inspector && !globalSupervisor) {
            conn = conn.where(function () {
                this.whereIn(
                    "IP.IPROC_ID",
                    globalThis.orm
                        .repo("externalRight", c)
                        .getUserProcesses(userId, false, false, true),
                );
            });
        }

        // t3b-1457
        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("header")
                .applyExternalLoginRights(conn, userId);
        }

        return globalThis.orm.collection("archivedProcess", conn);
    }

    async getMyProblemProcessList(userId: number) {
        const collection = await this.getMineProcessList(userId);
        collection.knex = collection.knex
            .where("U1.USER_ID", userId)
            .where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .whereExists((builder) => {
                builder
                    .select()
                    .from("ARCH_INSTANCE_TASKS as ITS")
                    .where(
                        "ITS.IPROC_ID",
                        this.connection.raw("??", [`"IP"."IPROC_ID"`]),
                    )
                    .whereNotNull("ITS.ITASK_DUE_DATE_FINISH")
                    .whereIn("ITS.ITASK_STATUS", [
                        TaskConstants.STATUS_ACTIVE,
                        TaskConstants.STATUS_WAITING,
                    ])
                    .where(
                        "ITS.ITASK_DUE_DATE_FINISH",
                        "<",
                        this.connection.raw(`${globalThis.orm.db.sysDate()}`),
                    );
            });
        return collection;
    }

    async getMineProcessList(userId: number) {
        const collection = await this.getProcessList(userId);
        collection.knex = collection.knex
            .where("IP.IPROC_STATUS", "A")
            .where("IP.IPROC_INST_OWNER_USER_ID", userId);
        return collection;
    }

    async getAllMineProcessList(userId: number) {
        const collection = await this.getProcessList(userId);
        collection.knex = collection.knex
            .whereIn("IP.IPROC_STATUS", ["A", "D"])
            .where("IP.IPROC_INST_OWNER_USER_ID", userId);
        return collection;
    }

    async getAllUsedProcessList(userId: number) {
        const collection = await this.getProcessList(userId);
        collection.knex = collection.knex.whereIn("IP.IPROC_STATUS", [
            "A",
            "D",
        ]);
        return collection;
    }

    public async getListSubProcess(iprocId: number): Promise<number[]> {
        const subprocessesIprocIds = await this.connection
            .select("IPROC_ID")
            .from(this.tableName)
            .where("IPROC_MAIN_IPROC_ID", iprocId)
            .whereIn("IPROC_STATUS", ["A", "D"]);

        return subprocessesIprocIds.map((oneRow) => oneRow.IPROC_ID);
    }

    getSubProcesses(iprocId: number, attachWithClause = true) {
        const conn = this.connection
            .select()
            .from("SUB_PROCESSES")
            .whereNot("IPROC_ID", iprocId)
            .as("SUB_PROCESSES");

        if (attachWithClause) {
            conn.with(
                "SUB_PROCESSES",
                this.connection.raw(this.withSubProcesses(), {
                    IPROC_ID: iprocId,
                }),
            );
        }

        return conn;
    }

    withDirectSubProcesses() {
        return `
        WITH RECURSIVE process_tree AS (
            -- Anchor: Start with the given IPROC_ID from the DIRECT_USER_PROCESSES table
            SELECT "IPROC_ID", "IPROC_NAME", "IPROC_MAIN_IPROC_ID"
            FROM "ARCH_INSTANCE_PROCESSES"
            WHERE "IPROC_ID" IN (SELECT "IPROC_ID" FROM "DIRECT_USER_PROCESSES")
    
            UNION ALL
    
            -- Recursive part: Find child processes by matching IPROC_MAIN_IPROC_ID
            SELECT ip."IPROC_ID", ip."IPROC_NAME", ip."IPROC_MAIN_IPROC_ID"
            FROM "ARCH_INSTANCE_PROCESSES" ip
            INNER JOIN process_tree pt ON pt."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
        )
        SELECT * FROM process_tree;
        `;
    }

    withSubProcesses() {
        return `
        WITH RECURSIVE sub_process_tree AS (
            -- Anchor: Start with the given IPROC_ID
            SELECT "IPROC_ID", "IPROC_MAIN_IPROC_ID", "IPROC_DMS_VISIBILITY"
            FROM "ARCH_INSTANCE_PROCESSES"
            WHERE "IPROC_ID" = :IPROC_ID
    
            UNION ALL
    
            -- Recursive part: Find child processes by matching IPROC_MAIN_IPROC_ID
            SELECT ip."IPROC_ID", ip."IPROC_MAIN_IPROC_ID", ip."IPROC_DMS_VISIBILITY"
            FROM "ARCH_INSTANCE_PROCESSES" ip
            INNER JOIN sub_process_tree spt ON spt."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
        )
        SELECT * FROM sub_process_tree;
        `;
    }

    getListMainProcess(iprocId: number) {
        let repo = null;
        if (iprocId && Array.isArray(iprocId)) {
            repo = this.connection.raw(
                `
                WITH RECURSIVE process_tree AS (
                    -- Anchor: Start with the given IPROC_ID(s)
                    SELECT "IPROC_ID", "IPROC_NAME", "IPROC_MAIN_IPROC_ID"
                    FROM "ARCH_INSTANCE_PROCESSES"
                    WHERE "IPROC_ID" IN (:iprocId)
            
                    UNION ALL
            
                    -- Recursive part: Traverse through the IPROC_MAIN_IPROC_ID hierarchy
                    SELECT ip."IPROC_ID", ip."IPROC_NAME", ip."IPROC_MAIN_IPROC_ID"
                    FROM "ARCH_INSTANCE_PROCESSES" ip
                    INNER JOIN process_tree pt ON pt."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
                )
                SELECT * FROM process_tree
                WHERE "IPROC_ID" NOT IN (:excludedIprocId);
                `,
                { iprocId: iprocId, excludedIprocId: iprocId },
            );
        } else {
            repo = this.connection.raw(
                `
                            WITH RECURSIVE process_tree AS (
                                -- Anchor: Start with the given IPROC_ID
                                SELECT "IPROC_ID", "IPROC_NAME", "IPROC_MAIN_IPROC_ID"
                                FROM "ARCH_INSTANCE_PROCESSES"
                                WHERE "IPROC_ID" = :iprocId
                        
                                UNION ALL
                        
                                -- Recursive part: Traverse through the IPROC_MAIN_IPROC_ID hierarchy
                                SELECT ip."IPROC_ID", ip."IPROC_NAME", ip."IPROC_MAIN_IPROC_ID"
                                FROM "ARCH_INSTANCE_PROCESSES" ip
                                INNER JOIN process_tree pt ON pt."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
                            )
                            SELECT * 
                            FROM process_tree
                            WHERE "IPROC_ID" <> :iprocId;
                            `,
                { iprocId: iprocId },
            );
        }

        return repo.then((data) => {
            const ids: any[] = [];
            if (data && Array.isArray(data)) {
                data.forEach((proc) => {
                    ids.push(proc.IPROC_ID);
                });
            }
            return ids;
        });
    }

    getMainProcesses(iprocId: number, attachWithClause = true) {
        const conn = this.connection
            .select()
            .from("MAIN_PROCESSES")
            .whereNot("IPROC_ID", iprocId)
            .as("MAIN_PROCESSES");

        if (attachWithClause) {
            conn.with(
                "MAIN_PROCESSES",
                this.connection.raw(this.withMainProcesses(), {
                    IPROC_ID: iprocId,
                }),
            );
        }

        return conn;
    }

    withMainProcesses() {
        return `
        WITH RECURSIVE main_processes AS (
            -- Anchor: Start with the given IPROC_ID
            SELECT "IPROC_ID", "IPROC_MAIN_IPROC_ID", "IPROC_DMS_VISIBILITY"
            FROM "ARCH_INSTANCE_PROCESSES"
            WHERE "IPROC_ID" = :IPROC_ID
    
            UNION ALL
    
            -- Recursive part: Traverse through the IPROC_MAIN_IPROC_ID hierarchy
            SELECT ip."IPROC_ID", ip."IPROC_MAIN_IPROC_ID", ip."IPROC_DMS_VISIBILITY"
            FROM "ARCH_INSTANCE_PROCESSES" ip
            INNER JOIN main_processes mp ON mp."IPROC_MAIN_IPROC_ID" = ip."IPROC_ID"
        )
        SELECT "IPROC_ID", "IPROC_MAIN_IPROC_ID", "IPROC_DMS_VISIBILITY"
        FROM main_processes;
        `;
    }

    getEvents(iprocId: number) {
        // TODO respect rights
        return this.connection
            .select()
            .where("ITASK_STATUS", TaskConstants.STATUS_DELAYED)
            .where("IPROC_ID", iprocId)
            .where("ITASK_TYPE", TaskConstants.TYPE_EVENT_WAIT)
            .whereNotNull("ITASK_EVENT")
            .from("ARCH_INSTANCE_TASKS");
    }

    async setStatusCascade(iProcId: number, status: string): Promise<void> {
        const affectedProcessIds = (
            await this.getSubProcesses(iProcId).pluck("IPROC_ID")
        )
            // @ts-ignore
            .concat(iProcId);
        await this.connection
            .from(this.tableName)
            .whereIn(
                "IPROC_ID",
                Array.isArray(affectedProcessIds)
                    ? affectedProcessIds
                    : [affectedProcessIds],
            )
            .update({
                IPROC_STATUS: status,
            });

        await Promise.all(
            affectedProcessIds.map((id: number) =>
                globalThis.tasLogger.warning("Changing process status!", {
                    iproc_id: Number(id),
                    status: Object.keys(PROCESS).find(
                        // @ts-ignore
                        (key) => PROCESS[key] === status,
                    ),
                }),
            ),
        );
    }

    suspend(iprocId: number): Promise<void> {
        return this.setStatusCascade(iprocId, PROCESS.STATUS_SUSPEND);
    }

    erase(iprocId: number): Promise<void> {
        return this.setStatusCascade(iprocId, PROCESS.STATUS_ERASED);
    }

    fail(iProcId: number): Promise<void> {
        return this.setStatusCascade(iProcId, PROCESS.STATUS_ERRORED);
    }

    // TODO: Does not consider subprocesses. It should, but not finished ones?
    resume(iprocId: number) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .update({ IPROC_STATUS: PROCESS.STATUS_ACTIVE });
    }

    getTTaskList(iprocId: number) {
        return this.connection
            .select([
                "A.*",
                globalThis.database.raw(
                    `(select count(*) from "INSTANCE_TASK_LINKS" "TTL" where "TTL"."ITASKLINK_TO_TTASK_ID" = "A"."ITASK_ID" and "TTL"."ORG_ID" = "A"."ORG_ID") as "ITASK_INC_CNT"`,
                ),
                "B.ROLE_NAME as ITASK_ASSESMENT_ROLE",
                "C.USER_NAME as ITASK_ASSESMENT_USER,",
                "D.ORGSTR_NAME as ITASK_ASSESMENT_ORG,",
                globalThis.database.raw(
                    `(select count(*) from "TEMPLATE_TASK_JS_CALCULATIONS" "TTC" where "TTC"."TTASK_ID" = "A"."TTASK_ID") as "ITASK_CALC_COUNT"`,
                ),
            ])
            .from("ARCH_INSTANCE_TASKS as A")
            .leftJoin("ROLES as B", "A.ITASK_ASSESMENT_ROLE_ID", "B.ROLE_ID")
            .leftJoin("USERS as C", "A.ITASK_ASSESMENT_USER_ID", "C.USER_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as D",
                "A.ITASK_ASSESMENT_ORGSTR_CNST",
                "D.ORGSTR_ID",
            )
            .where("A.IPROC_ID", iprocId);
    }

    getTTaskAssTasks(iprocId: number) {
        /* Todo: Raw SQL to Knex
        return this.connection.select([
            'T.ORG_ID',
            'T.IPROC_ID',
            'T.TTASK_ID',
            'AT.TTASK_ID as ASSESMENT_ITASK_ID',
            'AT.ITASK_NAME as ASSESMENT_ITASK_NAME',
        ])
            .from('INSTANCE_TASKS as T')
            .innerJoin('INSTANCE_TASKS as AT', 'AT.ITASK_ID', 'T.ITASK_ASSESMENT_TTASK_ID')
            .whereNotNull('T.ITASK_ASSESMENT_TTASK_ID')
            .where('T.ITASK_ASSESMENT_METHOD', TaskConstants.ASSESMENT_METHOD_LAST_SOLVER)
            .where('T.IPROC_ID', iprocId);
        */
        return this.connection
            .select([
                "T.ORG_ID",
                "T.IPROC_ID",
                "T.TTASK_ID",
                "AT.TTASK_ID as ASSESMENT_ITASK_ID",
                "AT.ITASK_NAME as ASSESMENT_ITASK_NAME",
            ])
            .from(
                globalThis.database.raw(
                    `"ARCH_INSTANCE_TASKS" "T", "ARCH_INSTANCE_TASKS" "AT"`,
                ),
            )
            .whereRaw(`"T"."ITASK_ASSESMENT_TTASK_ID" = "AT"."ITASK_ID"`)
            .whereNotNull("T.ITASK_ASSESMENT_TTASK_ID")
            .where("T.ITASK_ASSESMENT_METHOD", "L")
            .where("T.IPROC_ID", iprocId);
    }

    getITask(itaskId: number) {
        return this.connection
            .select([
                "IT.ORG_ID",
                "TT.TTASK_VAR_GLOBAL_ORDER",
                "IT.ITASK_ID",
                "IT.IPROC_ID",
                "IT.ITASK_NAME",
                "IT.ITASK_IS_IPROC_ID",
                "IT.ITASK_DESCRIPTION",
                "IT.ITASK_ASSESMENT_ROLE_ID",
                "IT.ITASK_ASSESMENT_HIERARCHY",
                "IT.ITASK_ASSESMENT_METHOD",
                "IT.ITASK_ASSESMENT_USER_ID",
                "IT.ITASK_AGAIN",
                "IT.ITASK_PETRI_NET_INPUT",
                "TT.TTASK_IS_DELETED",
                "IT.ITASK_DUE_OFFSET",
                "IT.ITASK_DURATION",
                "IT.ITASK_SUFFICIENT_END",
                "IT.ITASK_TYPE",
                "IT.ITASK_ASSESMENT_ORGSTR_ID",
                "IT.ITASK_ASSESMENT_TTASK_ID",
                "IT.ITASK_ASSESMENT_ORGSTR_CNST",
                "IT.ITASK_EVENT",
                "IT.ITASK_EVENT_WAIT",
                "IT.ITASK_SUBPROCESS_IPROC_ID",
                "IT.ITASK_ASSESMENT_TVAR_ID",
                "IT.ITASK_DUTY",
                "IT.ITASK_GEN_HISTORY",
                "IT.ITASK_INVOKE_EVENT",
                "IT.ITASK_ITERATE_OVER",
                "IT.ITASK_REFERENCE_USER",
                "ITEN.ITASK_ENOT_TGT_TYPE",
                "ITEN.ITASK_ENOT_TGT_TTASK_ID",
                "ITEN.ITASK_ENOT_TGT",
                "ITEN.ITASK_ENOT_TGT_ORGSTR_ID",
                "ORG.ORGSTR_NAME as ITASK_ENOT_TGT_ORGSTR",
                "ITEN.ITASK_ENOT_TGT_ROLE_ID",
                "R.ROLE_NAME as ITASK_ENOT_TGT_ROLE",
                "ITEN.ITASK_ENOT_SUBJECT",
                "ITEN.ITASK_ENOT_BODY2",
                "IT.ITASK_INSTRUCTION",
                "IT.ITASK_RUN_ONLY_ONCE",
                "IT.ITASK_DISC_FLAG",
                "IT.ITASK_MULTIINSTANCE_FLAG",
                "ITIN.ITASK_INV_ATTENDEES",
                "ITIN.ITASK_INV_SUMMARY",
                "ITIN.ITASK_INV_DESCRIPTION",
                "ITIN.ITASK_INV_DTSTART",
                "ITIN.ITASK_INV_DTEND",
                "ITIN.ITASK_INV_LOCATION",
                "ITIN.ITASK_INV_CLASS",
                "ITIN.ITASK_INV_PRIORITY",
                "ITIN.ITASK_INV_CATEGORIES",
            ])
            .from("ARCH_INSTANCE_TASKS as IT")
            .leftJoin(
                "ARCH_INSTANCE_TASK_EMAIL_NOTIFS as ITEN",
                "IT.ITASK_ID",
                "ITEN.ITASK_ID",
            )
            .leftJoin(
                "INSTANCE_TASK_INVITATIONS as ITIN",
                "IT.ITASK_ID",
                "ITIN.ITASK_ID",
            )
            .leftJoin("TEMPLATE_TASKS as TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .leftJoin("ROLES as R", "R.ROLE_ID", "ITEN.ITASK_ENOT_TGT_ROLE_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as ORG",
                "ORG.ORGSTR_ID",
                "ITEN.ITASK_ENOT_TGT_ORGSTR_ID",
            )
            .where("IT.ITASK_ID", itaskId);
    }

    getTTaskLinkList(iprocId: number) {
        /* Todo: Raw SQL to Knex
        ---> replace by a Constant ---> .where('TGR.IGRAPH_OBJECT_TYPE', 'L')

        return this.connection.select([
            'TTL.*', 'TGR.IGRAPH_LINKSRC_PORT', 'TGR.IGRAPH_LINKTGT_PORT',
            'TTF.ITASK_NAME AS FROM_NAME',
            'TTT.ITASK_NAME AS TO_NAME',
            {
                CONDITION_COUNT: this.connection.count()
                    .from('INSTANCE_LINK_CONDITIONS as TLC')
                    .where('TLC.ITASKLINK_ID', 'TTL.ITASKLINK_ID'),
            },
        ])
            .from('INSTANCE_TASK_LINKS as TTL')
            .leftJoin('INSTANCE_TASKS as TTF', 'TTL.ITASKLINK_FROM_TTASK_ID', 'TTF.ITASK_ID')
            .leftJoin('INSTANCE_TASKS as TTT', 'TTL.ITASKLINK_TO_TTASK_ID', 'TTT.ITASK_ID')
            .leftJoin('INSTANCE_GRAPH as TGR', 'TTL.ITASKLINK_ID', 'TGR.IGRAPH_ITASKCON_ID')
            .where('TGR.IGRAPH_OBJECT_TYPE', 'L') // <--- replace by a Constant <---
            .where('TTL.IPROC_ID', iprocId);
        */

        return this.connection
            .select([
                "TTL.*",
                "TGR.IGRAPH_LINKSRC_PORT",
                "TGR.IGRAPH_LINKTGT_PORT",
                "TTF.ITASK_NAME AS FROM_NAME",
                "TTT.ITASK_NAME AS TO_NAME",
                globalThis.database.raw(
                    `(SELECT COUNT(*) FROM "INSTANCE_LINK_CONDITIONS" "TLC" WHERE "TLC"."ITASKLINK_ID" = "TTL"."ITASKLINK_ID") AS "CONDITION_COUNT"`,
                ),
            ])
            .from("INSTANCE_TASK_LINKS as TTL")
            .leftJoin(
                "ARCH_INSTANCE_TASKS as TTF",
                "TTL.ITASKLINK_FROM_TTASK_ID",
                "TTF.ITASK_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_TASKS as TTT",
                "TTL.ITASKLINK_TO_TTASK_ID",
                "TTT.ITASK_ID",
            )
            .joinRaw(
                "LEFT JOIN ARCH_INSTANCE_GRAPH TGR ON TTL.ITASKLINK_ID = TGR.IGRAPH_ITASKCON_ID AND TGR.IGRAPH_OBJECT_TYPE = 'L'",
            )
            .where("TTL.IPROC_ID", iprocId);
    }

    getTProcConditionList(iprocId: number) {
        return this.connection
            .select("TLC.*")
            .from("INSTANCE_LINK_CONDITIONS as TLC")
            .leftJoin(
                "INSTANCE_TASK_LINKS as TTL",
                "TLC.ITASKLINK_ID",
                "TTL.ITASKLINK_ID",
            )
            .where("TTL.IPROC_ID", iprocId);
    }

    getHistory(iprocId: number) {
        // Todo: Raw SQL to Knex
        // Couldn't make column concat work without using raw. Is it a problem with MSSQL?
        return this.connection
            .select([
                "IV.IVAR_NAME",
                "IV.IVAR_TYPE",
                "IV.TVAR_ID",
                "IT.ITASK_NAME",
                "IT.TTASK_ID",
                "IVH.IVARH_TEXT_VALUE",
                "IVH.IVARH_NUMBER_VALUE",
                "IVH.IVARH_DATE_VALUE",
                "ITH.ITASKH_ID",
                "ITH.ITASKH_ACTUAL_DATE_START",
                "ITH.ITASKH_ACTUAL_DATE_FINISH",
                "ITH.ITASKH_DUE_DATE_START",
                "ITH.ITASKH_DUE_DATE_FINISH",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                globalThis.database.raw(
                    `"UF"."USER_DISPLAY_NAME" "ITASKH_FINISHED_BY_USER"`,
                ), // <---
                "ITH.ITASKH_USER_ID",
                globalThis.database.raw(
                    `"UO"."USER_DISPLAY_NAME" "ITASKH_USER"`,
                ), // <---
                "ITH.ITASKH_NOTE",
                "ITH.ITASK_ID",
            ])
            .from("ARCH_INSTANCE_TASK_HISTORY as ITH")
            .leftJoin(
                "ARCH_INSTANCE_VARIABLE_HISTORY as IVH",
                "ITH.ITASKH_ID",
                "IVH.ITASKH_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_VARIABLES as IV",
                "IVH.IVAR_ID",
                "IV.IVAR_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_TASKS as IT",
                "IT.ITASK_ID",
                "ITH.ITASK_ID",
            )
            .leftJoin("USERS AS UO", "UO.USER_ID", "ITH.ITASKH_USER_ID")
            .leftJoin(
                "USERS AS UF",
                "UF.USER_ID",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
            )
            .where("IT.IPROC_ID", iprocId)
            .orderBy("ITH.ITASKH_ACTUAL_DATE_FINISH", "asc")
            .orderBy("ITH.ITASKH_ID")
            .orderBy("IV.IVAR_NAME");
    }

    getByTemplate(tProcId: number, columns: any) {
        const conn = this.connection
            .select(columns || "IPROC_ID")
            .from(this.tableName)
            .where("TPROC_ID", tProcId);

        return globalThis.orm.collection("archivedProcess", conn);
    }

    getDefaultHeaderId(tprocId: number): Promise<any> {
        const headerRepo = globalThis.orm.repo("header", this.connection);
        return headerRepo.getHeadersByTProcId(tprocId).fetchOne();
    }

    async updateProcessFilesTags(entity: any) {
        if (globalThis.dynamicConfig.dms.fulltext) {
            // Trigger only when updating IPROC_NAME
            const { IPROC_NAME: newProcName } = entity.getAttributes(
                true,
                false,
            );

            if (newProcName && entity.id) {
                const fileRepo = globalThis.orm.repo(
                    "archivedDmsFile",
                    this.connection,
                );
                const files = await (
                    await fileRepo.getForProcessOnly(entity.id)
                ).collectAll();

                // Trigger only if files are present
                if (files.length) {
                    // Prepare tags
                    const tagsToStore = files.map(({ DMSF_ID }) => ({
                        DMST_ID: TAG.TYPE_CASE,
                        DMSF_ID,
                        DMST_VALUE: newProcName,
                        DATETIME_INSERT: new Date(),
                        USER_ID_INSERT: -1,
                    }));

                    // @t3b-2080 Chyba pri zakladani pripadu a zmene pripadu
                    // We don't really re-index files by the Cron, so let's just disable this
                    // There will be a mismatch, but nobody seems to mind

                    /*
                    // Mark the File so it can later be Elastic-indexed by Cron
                    await this
                        .connection('DMS_FILE')
                        .update({
                            TO_INDEX: FILE.TO_INDEX_YES,
                        })
                        // Re-run the query because of a possible big number of files => slow .whereIn
                        .whereIn('DMSF_ID', (await fileRepo.getForProcessOnly(entity.id))
                            .knex
                            .clearSelect()
                            .select('DF.DMSF_ID'),
                        );
                    */

                    // Delete DMS_FILE tags
                    await this.connection("ARCH_DMS_FILE_TAG")
                        // Re-run the query because of a possible big number of files => slow .whereIn
                        .whereIn(
                            "DMSF_ID",
                            (await fileRepo.getForProcessOnly(entity.id)).knex
                                .clearSelect()
                                .select("DF.DMSF_ID"),
                        )
                        .where("DMST_ID", TAG.TYPE_CASE)
                        .delete();

                    // Insert DMS_FILE tags
                    await this.connection("DMS_FILE_TAG").insert(tagsToStore);
                }
            }
        }
    }

    async store(entity: any) {
        // if (!entity.IPROC_INST_OWNER_USER_ID){
        //     entity.IPROC_INST_OWNER_USER_ID = 1;
        // }

        // @t3b-1529 autozápis CASE_STATUSu
        if (entity.IPROC_CASE_STATUS) {
            const caseStatusRepo = globalThis.orm.repo(
                "caseStatus",
                this.connection,
            );
            const statusExists = (
                await caseStatusRepo.getByValueForTemplate(
                    entity.IPROC_CASE_STATUS,
                    entity.TPROC_ID,
                )
            ).length;
            if (!statusExists) {
                const caseStatusEntity = caseStatusRepo.getEntity({
                    TPROC_ID: entity.TPROC_ID,
                    CS_NAME: entity.IPROC_CASE_STATUS,
                });
                caseStatusEntity.forceToInsert();
                globalThis.tasLogger.warning(
                    "Case status not found, creating a new entry",
                    {
                        iproc_id: entity.IPROC_ID,
                        tproc_id: entity.TPROC_ID,
                        case_status: caseStatusEntity.CS_NAME,
                    },
                );
                await caseStatusRepo.store(caseStatusEntity);
            }
        }

        // When updating IPROC_NAME, attached file's tags need to be updated as well
        await this.updateProcessFilesTags(entity);

        const rightsRepo = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );
        return await super.store(entity).then((id) => {
            entity.IPROC_ID = id;
            return rightsRepo
                .assignUserRights(
                    entity.IPROC_ID,
                    entity.IPROC_INST_OWNER_USER_ID,
                )
                .then(() => entity);
        });
    }

    changeProcessOwner(
        headerId: number,
        newUserId: number,
        oldUserId: number,
        userId: number,
    ) {
        const headerIds = !Array.isArray(headerId) ? [headerId] : headerId;
        const self = this;
        // Do not validate old user. You can use this method to fix db integrity.
        const conn = this.connection
            .select("IPROC_ID", "HEADER_ID")
            .from(this.tableName)
            .where("IPROC_INST_OWNER_USER_ID", oldUserId);
        if (typeof headerId !== "undefined" && headerId != null) {
            conn.whereIn("HEADER_ID", headerIds);
        }
        return conn.then((processes) => {
            if (Array.isArray(processes) && processes.length > 0) {
                const ids: number[] = _.map(processes, "IPROC_ID");
                return self.changeProcessOwnerForProcesses(
                    ids,
                    newUserId,
                    userId,
                    oldUserId,
                );
            }
        });
    }

    async changeProcessOwnerForProcesses(
        iprocIds: number[],
        newUserId: number,
        userId: number,
        oldUserId: number | null = null,
    ) {
        const userRepo = globalThis.orm.repo("user", this.connection);
        const rightRepo = globalThis.orm.repo("externalRight", this.connection);
        const user = await userRepo.get(newUserId); // Throw error if user not found!

        await rightRepo.removeStaticRightsWhenChangingCaseOwner(
            iprocIds,
            oldUserId,
        );

        await this.connection
            .select()
            .from("ARCH_INSTANCE_PROCESSES")
            .whereIn("IPROC_ID", iprocIds)
            .update({ IPROC_INST_OWNER_USER_ID: newUserId });

        const instanceProcessHistoryRepo = globalThis.orm.repo(
            "archivedInstanceProcessHistory",
            this.connection,
        );

        const results = [];
        for (const id of iprocIds) {
            await instanceProcessHistoryRepo.generateRecord(
                id,
                `HR changed case owner to ${user.USER_DISPLAY_NAME}`,
                userId,
                userId,
                new Date(),
                false,
                ProcHistoryConst.CASE_OWNER_CHANGE,
            );

            const result = await rightRepo.assignUserRights(id, newUserId);
            results.push(result);
        }
        return results;
    }

    getParentProcessStatus(iprocId: number) {
        return this.connection
            .select("IPROC_STATUS")
            .from("ARCH_INSTANCE_PROCESSES")
            .whereIn("IPROC_ID", function () {
                this.select("IPROC_MAIN_IPROC_ID")
                    .from("ARCH_INSTANCE_PROCESSES")
                    .where("IPROC_ID", iprocId);
            })
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    return null;
                }
                return rows[0].IPROC_STATUS;
            });
    }

    setIProcessSubProcessReturn(process: Process): Promise<any> {
        process.setSubprocessReturn();
        return this.store(process);
    }

    // async delete(process: Process): Promise<any> {
    //     try {
    //         // if is subprocess then end main task and mark with as deleted
    //         // todo: subprocess
    //         /* if (process.isSubprocess()) {
    //             try {
    //                 task = this.getMainTask();
    //                 task.closeTask('Subprocess deleted');
    //             } catch (e) {
    //                 // mask out if main process was not found
    //                 if($e->getCode() != 1404)
    //                     throw $e;
    //             }
    //         } */
    //
    //         await this.connection
    //             .from("ARCH_INSTANCE_GRAPH")
    //             .where("IPROC_ID", process.IPROC_ID)
    //             .delete();
    //
    //         await this.connection
    //             .from("XML_PROCESS_IMPORT")
    //             .where("IPROC_ID", process.IPROC_ID)
    //             .delete();
    //
    //         await this.connection
    //             .from("ARCH_DMS_FILE_ACCESS_LOG")
    //             .where("IPROC_ID", process.IPROC_ID)
    //             .delete();
    //
    //         const vars = await this.repo("archivedVariable", this.connection)
    //             .getByProcess(process.IPROC_ID)
    //             .collectAll();
    //         for (const ivar of vars) {
    //             await this.repo("archivedVariable", this.connection).delete(
    //                 ivar,
    //             );
    //         }
    //
    //         const tasks = await this.repo(
    //             "archivedInstanceTask",
    //             this.connection,
    //         )
    //             .getByProcess(process.IPROC_ID)
    //             .collectAll();
    //         for (const itask of tasks) {
    //             await this.repo("instanceTask", this.connection).delete(itask);
    //         }
    //         await this.connection
    //             .from("ARCH_INSTANCE_PROCESS_HISTORY")
    //             .where("IPROC_ID", process.IPROC_ID)
    //             .delete();
    //         await this.connection
    //             .from("ARCH_INSTANCE_PROCESS_STATIC_RIGHTS")
    //             .where("IPROC_ID", process.IPROC_ID)
    //             .delete();
    //         await this.connection
    //             .from("ARCH_INSTANCE_PROCESS_NOTES")
    //             .where("IPROC_ID", process.IPROC_ID)
    //             .delete();
    //
    //         // todo: remove all files
    //         return BaseRepository.prototype.delete.call(this, process); // workaround of super() in async
    //     } catch (e) {
    //         throw new InternalException(e);
    //     }
    // }

    getLastTask(iprocId: number, onlyStandard = true, currentlyActive = false) {
        const conn = this.connection
            .select("ITASK_ID")
            .from("ARCH_INSTANCE_TASKS")
            .where("IPROC_ID", iprocId)
            .orderBy("ITASK_ID", "desc")
            .first();

        if (onlyStandard) {
            conn.where("ITASK_TYPE", TASK.TYPE_STANDARD);
        }

        if (currentlyActive) {
            conn.where("ITASK_STATUS", TASK.STATUS_ACTIVE);
        } else {
            conn.where("ITASK_STATUS", TASK.STATUS_DONE);
        }

        return conn;
    }
}
