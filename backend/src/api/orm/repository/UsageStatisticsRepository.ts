// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import moment from "moment";

import { BaseRepository } from "./BaseRepository";
import * as UsageStatisticsConstants from "../entity/const/usageStatisticsConst";
import * as UserConstants from "../entity/const/userConst";
import * as ProcessConstants from "../entity/const/processConst";
import * as HeaderConstants from "../entity/const/headerConst";
import * as RoleConstants from "../entity/const/roleConst";
import * as TaskConstants from "../entity/const/taskConst";
import { UsageStatistics } from "../entity/UsageStatistics";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class UsageStatisticsRepository extends BaseRepository<UsageStatistics> {
    meta() {
        return {
            tableName: "USAGE_STATISTICS",
            entityName: "UsageStatistics",
            entity: () => new UsageStatistics(),
        };
    }

    async getIgnoredUsers(
        ignoredUsersIds = globalThis.dynamicConfig.crons.usageStatistics
            .ignoredUsers,
    ) {
        const userRepo = globalThis.orm.repo("user", this.connection);
        const usersCollection = userRepo.getById(ignoredUsersIds);
        return await usersCollection.collectAll();
    }

    /**
     * Finds Tasks and associated Process information
     * Can ignore Task solvers by supplying a USER_ID in @param ignoredUserIds, which has a default value
     *
     * @param {Array<number>} [ignoredUsersIds = globalThis.dynamicConfig.crons.usageStatistics.ignoredUsers]
     * @returns {BaseCollection}
     */
    getFinishedTasksReportData(
        ignoredUsersIds = globalThis.dynamicConfig.crons.usageStatistics
            .ignoredUsers,
    ) {
        const conn = this.connection
            .select([
                this.connection.raw(
                    `${globalThis.orm.db.extract(
                        "month",
                        `"IT"."ITASK_ACTUAL_DATE_FINISH"`,
                    )} "FINISHED_AT_MONTH"`,
                ),
                this.connection.raw(
                    `${globalThis.orm.db.extract(
                        "year",
                        `"IT"."ITASK_ACTUAL_DATE_FINISH"`,
                    )} "FINISHED_AT_YEAR"`,
                ),
                "IT.ITASK_ID",
                "IT.ITASK_NAME",
                "TP.TPROC_ID",
                "TP.TPROC_NAME",
                "H.HEADER_ID",
                "H.HEADER_NAME",
                "U.USER_ID",
                this.connection.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" "ITASK_SOLVER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME AS ITASK_SOLVER_DISPLAY_NAME",
            ])
            .from("INSTANCE_TASKS as IT")
            .innerJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IT.IPROC_ID")
            .innerJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "IP.TPROC_ID")
            .innerJoin("HEADERS as H", "H.HEADER_ID", "IP.HEADER_ID")
            .innerJoin(
                "USERS as U",
                "U.USER_ID",
                "IT.ITASK_FINISHED_BY_USER_ID",
            )
            .whereNotNull("IT.ITASK_FINISHED_BY_USER_ID")
            .whereNotNull("IT.ITASK_ACTUAL_DATE_FINISH")
            .where("IT.ITASK_TYPE", TaskConstants.TYPE_STANDARD);

        // Allow ignoring of users (system users such as Admin etc.)
        if (ignoredUsersIds) {
            conn.whereNotIn("U.USER_ID", ignoredUsersIds);
        }

        return globalThis.orm.collection("Task", conn);
    }

    /**
     *
     * @param {Number} [month = current month] - Numeric value of the month you want to focus on (1 - 12)
     * @param {Number} [year = current year] - Numeric value of the year you want to focus on
     *
     * @returns Array<object>
     */
    async getFinishedTasksReport(
        month = moment().format("M"),
        year = moment().format("YYYY"),
    ) {
        const collection = this.getFinishedTasksReportData();
        collection.knex
            .where(
                this.connection.raw(
                    globalThis.orm.db.extract(
                        "month",
                        `"IT"."ITASK_ACTUAL_DATE_FINISH"`,
                    ),
                ),
                month,
            )
            .where(
                this.connection.raw(
                    globalThis.orm.db.extract(
                        "year",
                        `"IT"."ITASK_ACTUAL_DATE_FINISH"`,
                    ),
                ),
                year,
            );

        // Fetch all finished tasks for the chosen period
        const finishedTasks = await collection.fetchAll();

        // Group the tasks by Templates and Headers for easier iteration
        const groupedTasks = _(finishedTasks)
            .groupBy("TPROC_NAME")
            .mapValues((tasksByTemplate) =>
                _(tasksByTemplate).groupBy("HEADER_NAME").value(),
            )
            .value();

        // Calculate relative and absolute tasks count for Templates and Headers
        const finishedTasksCount = finishedTasks.length;
        const tasksWithCount = Object.entries(groupedTasks).map(
            ([tprocName, headersWithTasks]) => {
                const absoluteTemplateTasksCount = _.sum(
                    headersWithTasks,
                    (tasks) => tasks.length,
                );
                const headersWithAbsoluteTasksCount = Object.entries(
                    headersWithTasks,
                ).map(([headerName, tasks]) => {
                    const absoluteHeaderTasksCount = tasks.length;
                    return {
                        HEADER_NAME: headerName,
                        TASKS_ABSOLUTE_COUNT: absoluteHeaderTasksCount,
                        TASKS_RELATIVE_COUNT: Number(
                            (
                                absoluteHeaderTasksCount /
                                absoluteTemplateTasksCount
                            ).toFixed(2),
                        ),
                    };
                });
                return {
                    TPROC_NAME: tprocName,
                    TASKS_ABSOLUTE_COUNT: absoluteTemplateTasksCount,
                    TASKS_RELATIVE_COUNT: Number(
                        (
                            absoluteTemplateTasksCount / finishedTasksCount
                        ).toFixed(2),
                    ),
                    TASKS_BY_HEADER: headersWithAbsoluteTasksCount,
                };
            },
        );

        return tasksWithCount;
    }

    async getReport() {
        const data = await this.connection
            .select()
            .from(this.tableName)
            .whereNot("US_TYPE", "FINISHED_TASKS_BY_USER")
            .whereNot("US_TYPE", "PROCESSES_BY_HEADER")
            .whereNot("US_TYPE", "FINISHED_TASKS_BY_ORGSTR");

        data.forEach((item) => {
            const value = JSON.parse(item.US_VALUE);
            item.US_VALUE =
                item.US_TYPE.includes("USER") || item.US_TYPE.includes("SOLVER")
                    ? this.filterOutIgnoredUsers(value)?.length
                    : value?.length;
            item.US_MONTH = item.US_PERIOD.getUTCMonth();
            item.US_YEAR = item.US_PERIOD.getUTCFullYear();
        });

        let allScenarioData = [];

        for (const scenario of UsageStatisticsConstants.SCENARIOS) {
            const scenarioData = await this.getForScenario(scenario);

            scenarioData.forEach((scenarioItem) => {
                if (Array.isArray(scenarioItem)) {
                    scenarioItem.forEach((item) => {
                        const value = item.US_VALUE;
                        item.US_VALUE =
                            item.US_TYPE.includes("USER") ||
                            item.US_TYPE.includes("SOLVER")
                                ? this.filterOutIgnoredUsers(value)?.length || 0
                                : value?.length || 0;
                        item.US_MONTH = item.US_PERIOD.getUTCMonth();
                        item.US_YEAR = item.US_PERIOD.getUTCFullYear();
                    });
                } else {
                    const item = scenarioItem;
                    const value = item.US_VALUE;
                    item.US_VALUE =
                        item.US_TYPE.includes("USER") ||
                        item.US_TYPE.includes("SOLVER")
                            ? this.filterOutIgnoredUsers(value)?.length || 0
                            : value?.length || 0;
                    item.US_MONTH = item.US_PERIOD.getUTCMonth();
                    item.US_YEAR = item.US_PERIOD.getUTCFullYear();
                }
                allScenarioData = allScenarioData.concat(scenarioItem);
            });
        }
        return data.concat(allScenarioData);
    }

    /**
     * Collects data by a predefined scenario
     *
     * @param {string} type - Must be IN usageStatisticsConst.TYPES
     */
    async gatherData(type: string, date: Date) {
        const period = date;

        const { ignoredUsers = [] } =
            globalThis.dynamicConfig.crons.usageStatistics;

        switch (type) {
            case "ACTIVE_USERS": {
                const users = await this.fetchDistinctIds("USERS", "USER_ID", [
                    ["USER_STATUS", UserConstants.STATUS_ACTIVE],
                ]);
                const userIds = users.map((user) => user.USER_ID);
                const filteredUsers = this.filterOutIgnoredUsers(userIds);
                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_VALUE: filteredUsers,
                    US_COUNT: filteredUsers.length,
                });
            }
            case "ACTIVE_TEMPLATE_PROCESSES": {
                const processes = await this.fetchDistinctIds(
                    "TEMPLATE_PROCESSES",
                    "TPROC_ID",
                    [["TPROC_STATUS", ProcessConstants.STATUS_ACTIVE]],
                );
                const processIds = processes.map((process) => process.TPROC_ID);
                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_VALUE: processIds,
                });
            }
            case "ACTIVE_HEADERS": {
                const headers = await this.fetchDistinctIds(
                    "HEADERS",
                    "HEADER_ID",
                    [["HEADER_ENABLED", HeaderConstants.STATUS_ACTIVE]],
                );
                const headerIds = headers.map((header) => header.HEADER_ID);
                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_VALUE: headerIds,
                });
            }
            case "ACTIVE_USERS_ABLE_TO_CREATE_A_PROCESS": {
                const users = await this.fetchDistinctIds("USERS", "USER_ID", [
                    ["USER_STATUS", UserConstants.STATUS_ACTIVE],
                ]);
                const repo = globalThis.orm.repo("templateProcess");
                const userIds = await Promise.all(
                    users.map(async (user) => {
                        const roles = await this.connection
                            .select()
                            .from("USER_ROLES")
                            .where("USER_ID", user.USER_ID)
                            .whereIn("ROLE_ID", [
                                RoleConstants.ADMINISTRATOR,
                                RoleConstants.POWERUSER,
                            ]);

                        const superStatus = roles.length > 0;
                        const templates = await repo.getTemplatesToStart(
                            user.USER_ID,
                            superStatus,
                        );
                        templates.knex.where(
                            "TPROC_STATUS",
                            ProcessConstants.STATUS_ACTIVE,
                        );

                        const activeTemplates = await templates.fetchAll();

                        return activeTemplates.length > 0
                            ? user.USER_ID
                            : false;
                    }),
                );

                const filteredUsers = this.filterOutIgnoredUsers(
                    _.compact(userIds),
                );
                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_VALUE: filteredUsers,
                    US_COUNT: filteredUsers.length,
                });
            }
            case "MOBILE_APP_PAIRED_USERS": {
                const result: Record<string, any>[] = await this.connection
                    .select("RMD_USER_ID")
                    .from("REGISTERED_MOBILE_DEVICES")
                    .whereNotNull("RMD_USER_ID")
                    .distinct();

                const uniqueUsers = result.map((row) => row.RMD_USER_ID);
                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_VALUE: uniqueUsers,
                    US_COUNT: uniqueUsers.length,
                });
            }
            case "USERS_THAT_SOLVED_A_TASK": {
                const users = await this.connection
                    .distinct("ITH.ITASKH_FINISHED_BY_USER_ID as USER_ID")
                    .from("INSTANCE_TASK_HISTORY as ITH")
                    .leftJoin(
                        "INSTANCE_TASKS as IT",
                        "ITH.ITASK_ID",
                        "IT.ITASK_ID",
                    )
                    .where("IT.ITASK_TYPE", TaskConstants.TYPE_STANDARD)
                    .whereNotNull("ITH.ITASKH_FINISHED_BY_USER_ID")
                    .whereNotIn("ITH.ITASKH_FINISHED_BY_USER_ID", ignoredUsers)
                    .where((builder) =>
                        this.addWhereForMonthItaskh(builder, period),
                    );
                const userIds = users.map((user) => user.USER_ID);
                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_VALUE: userIds,
                    US_COUNT: userIds.length,
                });
            }
            case "FINISHED_TASKS_BY_USER": {
                const results = await this.connection
                    .select(
                        this.connection.raw(
                            `count(distinct "ITH"."ITASK_ID") as "FINISHED_TASKS"`,
                        ),
                        "ITH.ITASKH_FINISHED_BY_USER_ID",
                    )
                    .from("INSTANCE_TASK_HISTORY as ITH")
                    .leftJoin(
                        "INSTANCE_TASKS as IT",
                        "ITH.ITASK_ID",
                        "IT.ITASK_ID",
                    )
                    .where("IT.ITASK_TYPE", TaskConstants.TYPE_STANDARD)
                    .whereNotNull("ITH.ITASKH_FINISHED_BY_USER_ID")
                    .whereNotIn("ITH.ITASKH_FINISHED_BY_USER_ID", ignoredUsers)
                    .where((builder) =>
                        this.addWhereForMonthItaskh(builder, period),
                    )
                    .groupBy("ITH.ITASKH_FINISHED_BY_USER_ID");

                for (const result of results) {
                    await this.storeForCurrentMonth({
                        US_TYPE: type,
                        US_PERIOD: period,
                        US_COUNT: result.FINISHED_TASKS,
                        USER_ID: result.ITASKH_FINISHED_BY_USER_ID,
                    });
                }
                break;
            }
            case "FINISHED_TASKS_BY_ORGSTR": {
                const results = await this.connection
                    .select(
                        this.connection.raw(
                            `count(distinct "ITH"."ITASK_ID") as "FINISHED_TASKS"`,
                        ),
                        "UOS.ORGSTR_ID",
                    )
                    .from("INSTANCE_TASK_HISTORY as ITH")
                    .leftJoin(
                        "INSTANCE_TASKS as IT",
                        "ITH.ITASK_ID",
                        "IT.ITASK_ID",
                    )
                    .leftJoin(
                        "USER_ORGANIZATION_STRUCTURE as UOS",
                        "ITH.ITASKH_FINISHED_BY_USER_ID",
                        "UOS.USER_ID",
                    )
                    .where("IT.ITASK_TYPE", TaskConstants.TYPE_STANDARD)
                    .whereNotNull("ITH.ITASKH_FINISHED_BY_USER_ID")
                    .whereNotIn("ITH.ITASKH_FINISHED_BY_USER_ID", ignoredUsers)
                    .where((builder) =>
                        this.addWhereForMonthItaskh(builder, period),
                    )
                    .groupBy("UOS.ORGSTR_ID");

                for (const result of results) {
                    await this.storeForCurrentMonth({
                        US_TYPE: type,
                        US_PERIOD: period,
                        US_COUNT: result.FINISHED_TASKS,
                        ORGSTR_ID: result.ORGSTR_ID,
                    });
                }
                break;
            }
            case "PROCESSES_BY_HEADER": {
                const results = await this.connection
                    .select(
                        this.connection.raw(
                            `count(distinct "IP"."IPROC_ID") as "STARTED_PROCESSES"`,
                        ),
                        "IP.HEADER_ID",
                    )
                    .from("INSTANCE_PROCESSES as IP")
                    .whereNotIn("IP.IPROC_INST_OWNER_USER_ID", ignoredUsers)
                    .where((builder) =>
                        this.addWhereForMonthIproc(builder, period),
                    )
                    .groupBy("IP.HEADER_ID");

                for (const result of results) {
                    await this.storeForCurrentMonth({
                        US_TYPE: type,
                        US_PERIOD: period,
                        US_COUNT: result.STARTED_PROCESSES,
                        HEADER_ID: result.HEADER_ID,
                    });
                }
                break;
            }

            case "TAS_FORMS_GENERATED": {
                const billingData =
                    await globalThis.container.client.tasForms.getBillingData(
                        globalThis.dynamicConfig.tasForms.companyId,
                        period.getMonth() + 1,
                        period.getFullYear(),
                    );

                return await this.storeForCurrentMonth({
                    US_TYPE: type,
                    US_PERIOD: period,
                    US_COUNT: billingData?.billing ?? 0,
                });
            }

            default:
                throw new InternalException(
                    `Unknown UsageStatistics type '${type}'`,
                    "BAD_INPUT",
                );
        }
    }

    private async fetchDistinctIds(table, idField, conditions) {
        const query = this.connection
            .distinct(idField)
            .select()
            .from(table)
            .whereNotNull(idField);
        for (const condition of conditions) {
            query.where(...condition);
        }
        return await query;
    }

    addWhereForMonthItaskh(builder, period) {
        // Collect data only for the past month
        builder.where(
            this.connection.raw(
                `extract(month from "ITH"."ITASKH_ACTUAL_DATE_FINISH")`,
            ),
            this.connection.raw("extract(month from ?)", [period]),
        );
        builder.where(
            this.connection.raw(
                `extract(year from "ITH"."ITASKH_ACTUAL_DATE_FINISH")`,
            ),
            this.connection.raw("extract(year from ?)", [period]),
        );

        return builder;
    }

    addWhereForMonthIproc(builder, period) {
        // Collect data only for the past month
        builder.where(
            this.connection.raw(
                `extract(month from "IP"."IPROC_ACTUAL_START_DATE")`,
            ),
            this.connection.raw("extract(month from ?)", [period]),
        );
        builder.where(
            this.connection.raw(
                `extract(year from "IP"."IPROC_ACTUAL_START_DATE")`,
            ),
            this.connection.raw("extract(year from ?)", [period]),
        );

        return builder;
    }

    /**
     * Finds the last record of the given entity TYPE
     * for this month and UPDATES it, otherwise INSERTS
     *
     * @param {UsageStatistics} entity
     */
    storeForCurrentMonth(entity) {
        if (!entity.ORGSTR_ID) {
            entity.ORGSTR_ID = null;
        }
        if (!entity.HEADER_ID) {
            entity.HEADER_ID = null;
        }
        if (!entity.USER_ID) {
            entity.USER_ID = null;
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where((builder) => {
                builder
                    .where("US_TYPE", entity.US_TYPE)
                    .where("ORGSTR_ID", entity.ORGSTR_ID)
                    .where("USER_ID", entity.USER_ID)
                    .where("HEADER_ID", entity.HEADER_ID)
                    .where(
                        this.connection.raw(`extract(month from "US_PERIOD")`),
                        this.connection.raw("extract(month from ?)", [
                            entity.US_PERIOD,
                        ]),
                    )
                    .where(
                        this.connection.raw(`extract(year from "US_PERIOD")`),
                        this.connection.raw("extract(year from ?)", [
                            entity.US_PERIOD,
                        ]),
                    );
            })
            .then((recordsForCurrentMonth) => {
                if (
                    recordsForCurrentMonth &&
                    recordsForCurrentMonth.length > 0
                ) {
                    // UPDATE
                    const updatedRecord = recordsForCurrentMonth[0];
                    if (Array.isArray(entity.US_VALUE)) {
                        updatedRecord.US_VALUE = JSON.parse(
                            updatedRecord.US_VALUE,
                        );
                        entity.US_VALUE = _.union(
                            entity.US_VALUE,
                            updatedRecord.US_VALUE,
                        );

                        if (
                            entity.US_TYPE.includes("USER") ||
                            entity.US_TYPE.includes("SOLVER")
                        ) {
                            entity.US_VALUE = this.filterOutIgnoredUsers(
                                entity.US_VALUE,
                            );
                        }

                        if (entity.US_VALUE.length !== entity.US_VALUE) {
                            entity.US_COUNT = entity.US_VALUE.length;
                        }
                        entity.US_VALUE = JSON.stringify(entity.US_VALUE);
                    }

                    return this.connection
                        .from(this.tableName)
                        .update({
                            US_PERIOD: entity.US_PERIOD,
                            US_VALUE: entity.US_VALUE,
                            US_COUNT: entity.US_COUNT,
                        })
                        .where("US_PERIOD", updatedRecord.US_PERIOD)
                        .where("US_TYPE", entity.US_TYPE)
                        .where("ORGSTR_ID", entity.ORGSTR_ID)
                        .where("USER_ID", entity.USER_ID)
                        .where("HEADER_ID", entity.HEADER_ID);
                }

                // INSERT
                if (!entity.US_COUNT) {
                    entity.US_COUNT = entity.US_VALUE.length;
                }
                entity.US_VALUE = JSON.stringify(entity.US_VALUE);
                return this.connection.from(this.tableName).insert(entity);
            });
    }

    /**
     * Returns data by a predefined scenario
     *
     * @param {string} scenario - Must be IN usageStatisticsConst.SCENARIOS
     * @returns {Knex.Builder}
     */
    getForScenario(scenario) {
        switch (scenario) {
            // Returns Users that have solved a Task or could have created a Process
            case "SOLVERS_OR_CAN_CREATE_A_PROCESS":
                return this.connection
                    .select()
                    .from(this.tableName)
                    .where((builder) => {
                        builder
                            .where("US_TYPE", UsageStatisticsConstants.SOLVER)
                            .orWhere(
                                "US_TYPE",
                                UsageStatisticsConstants.POSSIBLE_PROCESS_CREATOR,
                            );
                    })
                    .then((data) => {
                        const scenarioItems = [];
                        data.forEach((item) => {
                            if (
                                item.US_TYPE === UsageStatisticsConstants.SOLVER
                            ) {
                                const possibleProcessCreator = _.find(
                                    data,
                                    (ent) =>
                                        ent.US_PERIOD.getTime() ===
                                            item.US_PERIOD.getTime() &&
                                        ent.US_TYPE !== item.US_TYPE,
                                );

                                item.US_VALUE = JSON.parse(item.US_VALUE);
                                item.US_TYPE = scenario;

                                if (possibleProcessCreator) {
                                    possibleProcessCreator.US_VALUE =
                                        JSON.parse(
                                            possibleProcessCreator.US_VALUE,
                                        );
                                    item.US_VALUE = _.union(
                                        item.US_VALUE,
                                        possibleProcessCreator.US_VALUE,
                                    );
                                }
                                scenarioItems.push(item);
                            }
                        });
                        return scenarioItems;
                    });
            default:
                throw new InternalException(
                    `Unknown scenario '${scenario}', BAD_INPUT`,
                );
        }
    }

    /**
     * Returns all records for the given type
     *
     * @param {string} type - Must be IN usageStatisticsConst.TYPES
     * @returns {Knex.Builder}
     */
    // getByType(type) {
    //     if (UsageStatisticsConstants.TYPES.indexOf(type) === -1) {
    //         throw new InternalException(`Unknown UsageStatistics type '${type}'`);
    //     }
    //     return this.connection
    //         .select('US_VALUE')
    //         .from(this.tableName)
    //         .where('US_TYPE', type);
    // }

    getReportDataByType(type) {
        const columns = [
            "US.US_PERIOD",
            this.connection.raw(
                `${globalThis.orm.db.extract(
                    "month",
                    `"US"."US_PERIOD"`,
                )} "US_MONTH"`,
            ),
            this.connection.raw(
                `${globalThis.orm.db.extract(
                    "year",
                    `"US"."US_PERIOD"`,
                )} "US_YEAR"`,
            ),
            "US.US_COUNT",
            "US.US_VALUE",
            "US.US_TYPE",
        ];

        const { ignoredUsers = [] } =
            globalThis.dynamicConfig.crons.usageStatistics;

        let conn;
        switch (type) {
            case "FINISHED_TASKS_BY_USER": {
                columns.push(
                    "OS.ORGSTR_ID",
                    "OS.ORGSTR_NAME",
                    "U.USER_ID",
                    this.connection.raw(
                        `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                    ),
                    "U.USER_DISPLAY_NAME",
                );

                conn = this.connection
                    .select(columns)
                    .from("USAGE_STATISTICS as US")
                    .where("US.US_TYPE", type)
                    .whereNotIn("U.USER_ID", ignoredUsers)
                    .leftJoin("USERS as U", "U.USER_ID", "US.USER_ID")
                    .leftJoin(
                        "USER_ORGANIZATION_STRUCTURE as UOS",
                        "U.USER_ID",
                        "UOS.USER_ID",
                    )
                    .leftJoin(
                        "ORGANIZATION_STRUCTURE as OS",
                        "OS.ORGSTR_ID",
                        "UOS.ORGSTR_ID",
                    );
                break;
            }
            case "FINISHED_TASKS_BY_ORGSTR": {
                columns.push("OS.ORGSTR_ID", "OS.ORGSTR_NAME");

                conn = this.connection
                    .select(columns)
                    .from("USAGE_STATISTICS as US")
                    .where("US.US_TYPE", type)
                    .leftJoin(
                        "ORGANIZATION_STRUCTURE as OS",
                        "OS.ORGSTR_ID",
                        "US.ORGSTR_ID",
                    );
                break;
            }
            case "PROCESSES_BY_HEADER": {
                columns.push("H.HEADER_ID", "H.HEADER_NAME");

                conn = this.connection
                    .select(columns)
                    .from("USAGE_STATISTICS as US")
                    .where("US.US_TYPE", type)
                    .leftJoin("HEADERS as H", "H.HEADER_ID", "US.HEADER_ID");
                break;
            }
            case "COMBINED_REPORT": {
                const combinedTypes = [
                    "ACTIVE_USERS",
                    "ACTIVE_HEADERS",
                    "ACTIVE_TEMPLATE_PROCESSES",
                    "ACTIVE_USERS_ABLE_TO_CREATE_A_PROCESS",
                    "USERS_THAT_SOLVED_A_TASK",
                    "MOBILE_APP_PAIRED_USERS",
                    "TAS_FORMS_GENERATED",
                ];

                conn = this.connection
                    .select(columns)
                    .from("USAGE_STATISTICS as US")
                    .where((builder) => {
                        builder.whereIn("US.US_TYPE", combinedTypes);
                    });
                break;
            }
            case "ACTIVE_USERS":
            case "ACTIVE_HEADERS":
            case "ACTIVE_TEMPLATE_PROCESSES":
            case "ACTIVE_USERS_ABLE_TO_CREATE_A_PROCESS":
            case "USERS_THAT_SOLVED_A_TASK":
            case "MOBILE_APP_PAIRED_USERS": {
                conn = this.connection
                    .select(columns)
                    .from("USAGE_STATISTICS as US")
                    .where("US.US_TYPE", type);
                break;
            }
            default:
                throw new InternalException(
                    `Unknown UsageStatistics type '${type}'`,
                );
        }

        return globalThis.orm.collection("UsageStatistics", conn);
    }

    filterOutIgnoredUsers(
        users,
        ignoredUsers = globalThis.dynamicConfig.crons.usageStatistics
            .ignoredUsers,
    ) {
        return users.filter((user) => !ignoredUsers.includes(user));
    }

    async dataExistsByDate(date) {
        const result = await this.connection
            .select("*")
            .from(this.tableName)
            .where(
                this.connection.raw(`extract(month from "US_PERIOD")`),
                this.connection.raw("extract(month from ?)", [date]),
            )
            .where(
                this.connection.raw(`extract(year from "US_PERIOD")`),
                this.connection.raw("extract(year from ?)", [date]),
            )
            .limit(1);

        return result.length === 1;
    }
}
