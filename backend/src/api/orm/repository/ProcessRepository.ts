// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as PROCESS from "../entity/const/processConst";
import * as TaskConstants from "../entity/const/taskConst";
import { Role } from "../entity/Role";
import * as TASK from "../entity/const/taskConst";
import * as TAG from "../entity/const/dmsTagConsts";
import * as ProcHistoryConst from "../entity/const/processHistoryConst";
import { Process } from "../entity/Process";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { TimeMeasure } from "../../utils/TimeMeasure";

export class ProcessRepository extends BaseRepository<Process> {
    meta() {
        return {
            tableName: "INSTANCE_PROCESSES",
            entityName: "Process",
            defaultAlias: "IP",
            entity: () => new Process(),
            archived: true,
        };
    }

    /**
     * Returns detail info about Process without checking rights in sync way.
     * Full details can be retrieved with ent.raw.TPROC_NAME.
     *
     * @param iprocId
     * @param throwError If true throws error if process doesn't exist.
     * @returns {Process}
     */
    async collectProcess(iprocId, throwError = false) {
        const process = await this.getProcess(iprocId).collectOne();

        if (throwError && process === null) {
            throw new InternalException(`Process '${iprocId}' not found`);
        }
        return process;
    }

    /**
     * Returns detail info about Process without checking rights.

     * @param iprocId
     * @returns {BaseCollection}
     */
    getProcess(iprocId) {
        const extraColumns = [];

        // TTask translations
        const tprocEntity = globalThis.orm
            .repo("TemplateProcess", this.connection)
            .getEntity();
        let attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        // Header translations
        const headerEntity = globalThis.orm
            .repo("Header", this.connection)
            .getEntity();
        attrs = headerEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TH.${attrName}`);
            }
        });

        const columns = [
            "TH.HEADER_CODE",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
            "IP.ORG_ID",
            "IP.IPROC_ID",
            "IP.TPROC_ID",
            "IP.IPROC_NAME",
            "IP.IPROC_DESCRIPTION",
            "IP.IPROC_NOTES",
            "IP.IPROC_INST_OWNER_USER_ID",
            "IP.IPROC_STATUS",
            "IP.IPROC_ACTUAL_START_DATE",
            "IP.IPROC_ACTUAL_FINISH_DATE",
            "IP.IPROC_DUE_DATE_FINISH",
            "IP.IPROC_PRIORITY",
            "IP.IPROC_SUMMARY",
            "IP.IPROC_MAIN_IPROC_ID",
            "IP.IPROC_VIS_ROLE_ID",
            "U1.USER_ID",
            "U1.USER_NAME as IPROC_OWNER_USER_NAME",
            "TP.TPROC_DESCRIPTION",
            this.connection.raw(
                `"U1"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U1"."USER_FIRST_NAME" as "IPROC_OWNER_FULL_NAME"`,
            ),
            "U1.USER_DISPLAY_NAME as IPROC_OWNER_DISPLAY_NAME",
            "TP.TPROC_NAME",
            this.connection.raw(
                'CASE WHEN "IP"."IPROC_STATUS" = \'A\' THEN 1 ELSE 0 END C_HEV',
            ),
            "IP.IPROC_CASE_STATUS",
        ].concat(extraColumns);

        const conn = this.connection
            .select(columns)
            .from("INSTANCE_PROCESSES as IP")
            .leftJoin(
                "USERS AS U1",
                "U1.USER_ID",
                "IP.IPROC_INST_OWNER_USER_ID",
            )
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .where("IP.IPROC_ID", iprocId);

        return this.createCollection(conn);
    }

    async getProcessList(userId) {
        const extraColumns = [];

        // CASE_STATUS translations
        const caseStatusEntity = globalThis.orm
            .repo("caseStatus", this.connection)
            .getEntity();
        extraColumns.push(
            ...caseStatusEntity.getTranslatedProperties([], "CS"),
        );

        // TTask translations
        const tprocEntity = globalThis.orm
            .repo("templateProcess", this.connection)
            .getEntity();
        let attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        // iProc translations
        const iProcEntity = globalThis.orm
            .repo("process", this.connection)
            .getEntity();
        attrs = iProcEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
            }
        });

        // Header translations
        const headerEntity = globalThis.orm
            .repo("header", this.connection)
            .getEntity();
        attrs = headerEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TH.${attrName}`);
            }
        });

        const c = this.connection;

        // let  self = this;
        const columns = [
            "TH.HEADER_CODE",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
            "IP.ORG_ID",
            "IP.IPROC_ID",
            "IP.TPROC_ID",
            "IP.IPROC_NAME",
            "IP.IPROC_DESCRIPTION",
            "IP.IPROC_NOTES",
            "IP.IPROC_INST_OWNER_USER_ID",
            "IP.IPROC_STATUS",
            "IP.IPROC_CASE_STATUS",
            "IP.IPROC_ACTUAL_START_DATE",
            "IP.IPROC_ACTUAL_FINISH_DATE",
            "IP.IPROC_DUE_DATE_FINISH",
            "IP.IPROC_PRIORITY",
            "IP.IPROC_SUMMARY",
            "IP.IPROC_MAIN_IPROC_ID",
            "IP.IPROC_VIS_ROLE_ID",
            "U1.USER_ID",
            "TP.TPROC_DESCRIPTION",
            this.connection.raw(
                `"U1"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U1"."USER_FIRST_NAME" as "IPROC_OWNER_FULL_NAME"`,
            ),
            "U1.USER_DISPLAY_NAME AS IPROC_OWNER_DISPLAY_NAME",
            "TP.TPROC_NAME",
            this.connection.raw(
                'CASE WHEN -1 IN (SELECT "ROLE_ID" FROM "USER_ROLES" WHERE "USER_ID" = ? ) OR "IP"."IPROC_INST_OWNER_USER_ID" = ? THEN 1 ELSE 0 END C_SUS',
                [userId, userId],
            ),
            this.connection.raw(
                'CASE WHEN "IP"."IPROC_STATUS" = \'A\' THEN 1 ELSE 0 END C_HEV',
            ),
        ].concat(extraColumns);

        let conn = this.connection
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(columns)
            .from("INSTANCE_PROCESSES as IP")
            .leftJoin(
                "USERS AS U1",
                "U1.USER_ID",
                "IP.IPROC_INST_OWNER_USER_ID",
            )
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .leftJoin("CASE_STATUSES as CS", (cStats) => {
                cStats
                    .on("CS.TPROC_ID", "=", "TP.TPROC_ID")
                    .andOn("CS.CS_NAME", "=", "IP.IPROC_CASE_STATUS");
            })
            .as("IP");

        const roleRelation = await globalThis.orm
            .repo("user", this.connection)
            .getRoleRelation(userId, [
                Role.consts.INSPECTOR,
                Role.consts.GLOBAL_SUPERVISOR,
            ]);
        const inspector = roleRelation[Role.consts.INSPECTOR];
        const globalSupervisor = roleRelation[Role.consts.GLOBAL_SUPERVISOR];
        if (!inspector && !globalSupervisor) {
            conn = conn.where(function () {
                this.whereIn(
                    "IP.IPROC_ID",
                    globalThis.orm
                        .repo("externalRight", c)
                        .getUserProcesses(userId),
                );
            });
        }

        // t3b-1457
        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("header")
                .applyExternalLoginRights(conn, userId);
        }

        return globalThis.orm.collection("process", conn);
    }

    async getMyProblemProcessList(userId) {
        const collection = await this.getMineProcessList(userId);
        collection.knex = collection.knex
            .where("U1.USER_ID", userId)
            .where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE)
            .whereExists((builder) => {
                builder
                    .select()
                    .from("INSTANCE_TASKS as ITS")
                    .where(
                        "ITS.IPROC_ID",
                        this.connection.raw("??", [`IP.IPROC_ID`]),
                    )
                    .whereNotNull("ITS.ITASK_DUE_DATE_FINISH")
                    .whereIn("ITS.ITASK_STATUS", [
                        TaskConstants.STATUS_ACTIVE,
                        TaskConstants.STATUS_WAITING,
                    ])
                    .where(
                        "ITS.ITASK_DUE_DATE_FINISH",
                        "<",
                        this.connection.raw(`${globalThis.orm.db.sysDate()}`),
                    );
            });
        return collection;
    }

    async getMineProcessList(userId) {
        const collection = await this.getProcessList(userId);
        collection.knex = collection.knex
            .where("IP.IPROC_STATUS", "A")
            .where("IP.IPROC_INST_OWNER_USER_ID", userId);
        return collection;
    }

    async getAllMineProcessList(userId) {
        const collection = await this.getProcessList(userId);
        collection.knex = collection.knex
            .whereIn("IP.IPROC_STATUS", ["A", "D"])
            .where("IP.IPROC_INST_OWNER_USER_ID", userId);
        return collection;
    }

    async getAllUsedProcessList(userId) {
        const collection = await this.getProcessList(userId);
        collection.knex = collection.knex.whereIn("IP.IPROC_STATUS", [
            "A",
            "D",
        ]);
        return collection;
    }

    public async getListSubProcess(iprocId: number): Promise<number[]> {
        const subprocessesIprocIds = await this.connection
            .select("IPROC_ID")
            .from(this.tableName)
            .where("IPROC_MAIN_IPROC_ID", iprocId)
            .whereIn("IPROC_STATUS", ["A", "D"]);

        return subprocessesIprocIds.map((oneRow) => oneRow.IPROC_ID);
    }

    /**
     * Returns all subprocesses of a given INSTANCE_PROCESS
     *
     * @param {number} iprocId - ID of INSTANCE_PROCESS
     * @param {boolean} attachWithClause
     * @returns {Knex.Builder}
     */
    getSubProcesses(iprocId, attachWithClause = true) {
        const conn = this.connection
            .select()
            .from("SUB_PROCESSES")
            .whereNot("IPROC_ID", iprocId)
            .as("SUB_PROCESSES");

        if (attachWithClause) {
            conn.with(
                "SUB_PROCESSES",
                this.connection.raw(this.withSubProcesses(), {
                    IPROC_ID: iprocId,
                }),
            );
        }

        return conn;
    }

    withDirectSubProcesses() {
        return `
        WITH RECURSIVE direct_subprocesses AS (
            SELECT * 
            FROM "INSTANCE_PROCESSES"
            WHERE "IPROC_ID" IN (SELECT "IPROC_ID" FROM "DIRECT_USER_PROCESSES")
    
            UNION ALL
    
            SELECT ip.* 
            FROM "INSTANCE_PROCESSES" ip
            INNER JOIN direct_subprocesses dsp ON dsp."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
        )
        SELECT * FROM direct_subprocesses
        `;
    }

    withSubProcesses() {
        return `
        WITH RECURSIVE sub_processes AS (
            SELECT 
                "IPROC_ID", 
                "IPROC_MAIN_IPROC_ID", 
                "IPROC_DMS_VISIBILITY"
            FROM "INSTANCE_PROCESSES"
            WHERE "IPROC_ID" = :IPROC_ID
    
            UNION ALL
    
            SELECT 
                ip."IPROC_ID", 
                ip."IPROC_MAIN_IPROC_ID", 
                ip."IPROC_DMS_VISIBILITY"
            FROM "INSTANCE_PROCESSES" ip
            INNER JOIN sub_processes sp ON sp."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
        )
        SELECT * FROM sub_processes
        `;
    }

    getListMainProcess(iprocId) {
        let repo = null;
        if (iprocId && Array.isArray(iprocId)) {
            repo = this.connection.raw(
                `WITH RECURSIVE sub_processes AS (
                    -- Anchor query: Start with the processes having "IPROC_ID" in the given list
                    SELECT "IPROC_ID", "IPROC_NAME", "IPROC_MAIN_IPROC_ID"
                    FROM "INSTANCE_PROCESSES"
                    WHERE "IPROC_ID" = ANY(?)
            
                    UNION ALL
            
                    -- Recursive part: Traverse the sub-processes by matching parent-child relationships
                    SELECT ip."IPROC_ID", ip."IPROC_NAME", ip."IPROC_MAIN_IPROC_ID"
                    FROM "INSTANCE_PROCESSES" ip
                    JOIN sub_processes sp ON sp."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
                )
                SELECT * 
                FROM sub_processes
                WHERE "IPROC_ID" NOT IN (?)`,
                [iprocId, iprocId],
            );
        } else {
            repo = this.connection.raw(
                `WITH RECURSIVE sub_processes AS (
                                -- Anchor query: Start with the process having the given "IPROC_ID"
                                SELECT "IPROC_ID", "IPROC_NAME", "IPROC_MAIN_IPROC_ID"
                                FROM "INSTANCE_PROCESSES"
                                WHERE "IPROC_ID" = ?
                        
                                UNION ALL
                        
                                -- Recursive query: Traverse child processes by matching "IPROC_MAIN_IPROC_ID"
                                SELECT ip."IPROC_ID", ip."IPROC_NAME", ip."IPROC_MAIN_IPROC_ID"
                                FROM "INSTANCE_PROCESSES" ip
                                JOIN sub_processes sp ON sp."IPROC_ID" = ip."IPROC_MAIN_IPROC_ID"
                            )
                            SELECT * 
                            FROM sub_processes
                            WHERE "IPROC_ID" <> ?`,
                [iprocId, iprocId],
            );
        }

        return repo.then((data) => {
            const ids = [];
            if (data && Array.isArray(data)) {
                data.forEach((proc) => {
                    ids.push(proc.IPROC_ID);
                });
            }
            return ids;
        });
    }

    /**
     * Returns all mainprocesses of a given INSTANCE_PROCESS
     *
     * @param {number} iprocId - ID of INSTANCE_PROCESS
     * @param {boolean} attachWithClause
     * @returns {Knex.Builder}
     */
    getMainProcesses(iprocId, attachWithClause = true) {
        const conn = this.connection
            .select()
            .from("MAIN_PROCESSES")
            .whereNot("IPROC_ID", iprocId)
            .as("MAIN_PROCESSES");

        if (attachWithClause) {
            conn.with(
                "MAIN_PROCESSES",
                this.connection.raw(this.withMainProcesses(), {
                    IPROC_ID: iprocId,
                }),
            );
        }

        return conn;
    }

    withMainProcesses() {
        return `
        WITH RECURSIVE main_processes AS (
            SELECT 
                "IPROC_ID", 
                "IPROC_MAIN_IPROC_ID", 
                "IPROC_DMS_VISIBILITY"
            FROM "INSTANCE_PROCESSES"
            WHERE "IPROC_ID" = :IPROC_ID
    
            UNION ALL
    
            SELECT 
                ip."IPROC_ID", 
                ip."IPROC_MAIN_IPROC_ID", 
                ip."IPROC_DMS_VISIBILITY"
            FROM "INSTANCE_PROCESSES" ip
            INNER JOIN main_processes mp ON mp."IPROC_MAIN_IPROC_ID" = ip."IPROC_ID"
        )
        SELECT * FROM main_processes
        `;
    }

    getEvents(iprocId) {
        // TODO respect rights
        return this.connection
            .select()
            .where("ITASK_STATUS", TaskConstants.STATUS_DELAYED)
            .where("IPROC_ID", iprocId)
            .where("ITASK_TYPE", TaskConstants.TYPE_EVENT_WAIT)
            .whereNotNull("ITASK_EVENT")
            .from("INSTANCE_TASKS");
    }

    /**
     *
     * @param {Number} iProcId
     * @param {PROCESS} status
     * @returns {Promise<void>}
     */
    async setStatusCascade(iProcId, status) {
        const affectedProcessIds = (
            await this.getSubProcesses(iProcId).pluck("IPROC_ID")
        ).concat(iProcId);
        await this.connection
            .from(this.tableName)
            .whereIn(
                "IPROC_ID",
                Array.isArray(affectedProcessIds)
                    ? affectedProcessIds
                    : [affectedProcessIds],
            )
            .update({
                IPROC_STATUS: status,
            });

        await Promise.all(
            affectedProcessIds.map((id) =>
                globalThis.tasLogger.warning("Changing process status!", {
                    iproc_id: Number(id),
                    status: Object.keys(PROCESS).find(
                        (key) => PROCESS[key] === status,
                    ),
                }),
            ),
        );
    }

    /**
     *
     * @param iprocId
     * @returns {Promise<void>}
     */
    suspend(iprocId) {
        return this.setStatusCascade(iprocId, PROCESS.STATUS_SUSPEND);
    }

    /**
     *
     * @param iprocId
     * @returns {Promise<void>}
     */
    erase(iprocId) {
        return this.setStatusCascade(iprocId, PROCESS.STATUS_ERASED);
    }

    /**
     *
     * @param iProcId
     * @returns {Promise<void>}
     */
    fail(iProcId) {
        return this.setStatusCascade(iProcId, PROCESS.STATUS_ERRORED);
    }

    // TODO: Does not consider subprocesses. It should, but not finished ones?
    resume(iprocId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .update({ IPROC_STATUS: PROCESS.STATUS_ACTIVE });
    }

    getTTaskList(iprocId) {
        return this.connection
            .select([
                "A.*",
                globalThis.database.raw(
                    `(select count(*) from "INSTANCE_TASK_LINKS" "TTL" where "TTL"."ITASKLINK_TO_TTASK_ID" = "A"."ITASK_ID" and "TTL"."ORG_ID" = "A"."ORG_ID") as "ITASK_INC_CNT"`,
                ),
                "B.ROLE_NAME as ITASK_ASSESMENT_ROLE",
                "C.USER_NAME as ITASK_ASSESMENT_USER,",
                "D.ORGSTR_NAME as ITASK_ASSESMENT_ORG,",
                globalThis.database.raw(
                    `(select count(*) from "TEMPLATE_TASK_JS_CALCULATIONS" "TTC" where "TTC"."TTASK_ID" = "A"."TTASK_ID") as "ITASK_CALC_COUNT"`,
                ),
            ])
            .from("INSTANCE_TASKS as A")
            .leftJoin("ROLES as B", "A.ITASK_ASSESMENT_ROLE_ID", "B.ROLE_ID")
            .leftJoin("USERS as C", "A.ITASK_ASSESMENT_USER_ID", "C.USER_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as D",
                "A.ITASK_ASSESMENT_ORGSTR_CNST",
                "D.ORGSTR_ID",
            )
            .where("A.IPROC_ID", iprocId);
    }

    getTTaskAssTasks(iprocId) {
        /* Todo: Raw SQL to Knex
        return this.connection.select([
            'T.ORG_ID',
            'T.IPROC_ID',
            'T.TTASK_ID',
            'AT.TTASK_ID as ASSESMENT_ITASK_ID',
            'AT.ITASK_NAME as ASSESMENT_ITASK_NAME',
        ])
            .from('INSTANCE_TASKS as T')
            .innerJoin('INSTANCE_TASKS as AT', 'AT.ITASK_ID', 'T.ITASK_ASSESMENT_TTASK_ID')
            .whereNotNull('T.ITASK_ASSESMENT_TTASK_ID')
            .where('T.ITASK_ASSESMENT_METHOD', TaskConstants.ASSESMENT_METHOD_LAST_SOLVER)
            .where('T.IPROC_ID', iprocId);
        */
        return this.connection
            .select([
                "T.ORG_ID",
                "T.IPROC_ID",
                "T.TTASK_ID",
                "AT.TTASK_ID as ASSESMENT_ITASK_ID",
                "AT.ITASK_NAME as ASSESMENT_ITASK_NAME",
            ])
            .from(
                globalThis.database.raw(
                    `"INSTANCE_TASKS" "T", "INSTANCE_TASKS" "AT"`,
                ),
            )
            .whereRaw("T.ITASK_ASSESMENT_TTASK_ID = AT.ITASK_ID")
            .whereNotNull("T.ITASK_ASSESMENT_TTASK_ID")
            .where("T.ITASK_ASSESMENT_METHOD", "L")
            .where("T.IPROC_ID", iprocId);
    }

    getITask(itaskId) {
        return this.connection
            .select([
                "IT.ORG_ID",
                "TT.TTASK_VAR_GLOBAL_ORDER",
                "IT.ITASK_ID",
                "IT.IPROC_ID",
                "IT.ITASK_NAME",
                "IT.ITASK_IS_IPROC_ID",
                "IT.ITASK_DESCRIPTION",
                "IT.ITASK_ASSESMENT_ROLE_ID",
                "IT.ITASK_ASSESMENT_HIERARCHY",
                "IT.ITASK_ASSESMENT_METHOD",
                "IT.ITASK_ASSESMENT_USER_ID",
                "IT.ITASK_AGAIN",
                "IT.ITASK_PETRI_NET_INPUT",
                "TT.TTASK_IS_DELETED",
                "IT.ITASK_DUE_OFFSET",
                "IT.ITASK_DURATION",
                "IT.ITASK_SUFFICIENT_END",
                "IT.ITASK_TYPE",
                "IT.ITASK_ASSESMENT_ORGSTR_ID",
                "IT.ITASK_ASSESMENT_TTASK_ID",
                "IT.ITASK_ASSESMENT_ORGSTR_CNST",
                "IT.ITASK_EVENT",
                "IT.ITASK_EVENT_WAIT",
                "IT.ITASK_SUBPROCESS_IPROC_ID",
                "IT.ITASK_ASSESMENT_TVAR_ID",
                "IT.ITASK_DUTY",
                "IT.ITASK_GEN_HISTORY",
                "IT.ITASK_INVOKE_EVENT",
                "IT.ITASK_ITERATE_OVER",
                "IT.ITASK_REFERENCE_USER",
                "ITEN.ITASK_ENOT_TGT_TYPE",
                "ITEN.ITASK_ENOT_TGT_TTASK_ID",
                "ITEN.ITASK_ENOT_TGT",
                "ITEN.ITASK_ENOT_TGT_ORGSTR_ID",
                "ORG.ORGSTR_NAME as ITASK_ENOT_TGT_ORGSTR",
                "ITEN.ITASK_ENOT_TGT_ROLE_ID",
                "R.ROLE_NAME as ITASK_ENOT_TGT_ROLE",
                "ITEN.ITASK_ENOT_SUBJECT",
                "ITEN.ITASK_ENOT_BODY2",
                "IT.ITASK_INSTRUCTION",
                "IT.ITASK_RUN_ONLY_ONCE",
                "IT.ITASK_DISC_FLAG",
                "IT.ITASK_MULTIINSTANCE_FLAG",
                "ITIN.ITASK_INV_ATTENDEES",
                "ITIN.ITASK_INV_SUMMARY",
                "ITIN.ITASK_INV_DESCRIPTION",
                "ITIN.ITASK_INV_DTSTART",
                "ITIN.ITASK_INV_DTEND",
                "ITIN.ITASK_INV_LOCATION",
                "ITIN.ITASK_INV_CLASS",
                "ITIN.ITASK_INV_PRIORITY",
                "ITIN.ITASK_INV_CATEGORIES",
            ])
            .from("INSTANCE_TASKS as IT")
            .leftJoin(
                "INSTANCE_TASK_EMAIL_NOTIFS as ITEN",
                "IT.ITASK_ID",
                "ITEN.ITASK_ID",
            )
            .leftJoin(
                "INSTANCE_TASK_INVITATIONS as ITIN",
                "IT.ITASK_ID",
                "ITIN.ITASK_ID",
            )
            .leftJoin("TEMPLATE_TASKS as TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .leftJoin("ROLES as R", "R.ROLE_ID", "ITEN.ITASK_ENOT_TGT_ROLE_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as ORG",
                "ORG.ORGSTR_ID",
                "ITEN.ITASK_ENOT_TGT_ORGSTR_ID",
            )
            .where("IT.ITASK_ID", itaskId);
    }

    getTTaskLinkList(iprocId) {
        /* Todo: Raw SQL to Knex
        ---> replace by a Constant ---> .where('TGR.IGRAPH_OBJECT_TYPE', 'L')

        return this.connection.select([
            'TTL.*', 'TGR.IGRAPH_LINKSRC_PORT', 'TGR.IGRAPH_LINKTGT_PORT',
            'TTF.ITASK_NAME AS FROM_NAME',
            'TTT.ITASK_NAME AS TO_NAME',
            {
                CONDITION_COUNT: this.connection.count()
                    .from('INSTANCE_LINK_CONDITIONS as TLC')
                    .where('TLC.ITASKLINK_ID', 'TTL.ITASKLINK_ID'),
            },
        ])
            .from('INSTANCE_TASK_LINKS as TTL')
            .leftJoin('INSTANCE_TASKS as TTF', 'TTL.ITASKLINK_FROM_TTASK_ID', 'TTF.ITASK_ID')
            .leftJoin('INSTANCE_TASKS as TTT', 'TTL.ITASKLINK_TO_TTASK_ID', 'TTT.ITASK_ID')
            .leftJoin('INSTANCE_GRAPH as TGR', 'TTL.ITASKLINK_ID', 'TGR.IGRAPH_ITASKCON_ID')
            .where('TGR.IGRAPH_OBJECT_TYPE', 'L') // <--- replace by a Constant <---
            .where('TTL.IPROC_ID', iprocId);
        */

        return this.connection
            .select([
                "TTL.*",
                "TGR.IGRAPH_LINKSRC_PORT",
                "TGR.IGRAPH_LINKTGT_PORT",
                "TTF.ITASK_NAME AS FROM_NAME",
                "TTT.ITASK_NAME AS TO_NAME",
                globalThis.database.raw(
                    `(SELECT COUNT(*) FROM "INSTANCE_LINK_CONDITIONS" "TLC" WHERE "TLC"."ITASKLINK_ID" = "TTL"."ITASKLINK_ID") AS "CONDITION_COUNT"`,
                ),
            ])
            .from("INSTANCE_TASK_LINKS as TTL")
            .leftJoin(
                "INSTANCE_TASKS as TTF",
                "TTL.ITASKLINK_FROM_TTASK_ID",
                "TTF.ITASK_ID",
            )
            .leftJoin(
                "INSTANCE_TASKS as TTT",
                "TTL.ITASKLINK_TO_TTASK_ID",
                "TTT.ITASK_ID",
            )
            .joinRaw(
                "LEFT JOIN INSTANCE_GRAPH TGR ON TTL.ITASKLINK_ID = TGR.IGRAPH_ITASKCON_ID AND TGR.IGRAPH_OBJECT_TYPE = 'L'",
            )
            .where("TTL.IPROC_ID", iprocId);
    }

    getTProcConditionList(iprocId) {
        return this.connection
            .select("TLC.*")
            .from("INSTANCE_LINK_CONDITIONS as TLC")
            .leftJoin(
                "INSTANCE_TASK_LINKS as TTL",
                "TLC.ITASKLINK_ID",
                "TTL.ITASKLINK_ID",
            )
            .where("TTL.IPROC_ID", iprocId);
    }

    getHistory(iprocId) {
        // Todo: Raw SQL to Knex
        // Couldn't make column concat work without using raw. Is it a problem with MSSQL?
        return this.connection
            .select([
                "IV.IVAR_NAME",
                "IV.IVAR_TYPE",
                "IV.TVAR_ID",
                "IT.ITASK_NAME",
                "IT.TTASK_ID",
                "IVH.IVARH_TEXT_VALUE",
                "IVH.IVARH_NUMBER_VALUE",
                "IVH.IVARH_DATE_VALUE",
                "ITH.ITASKH_ID",
                "ITH.ITASKH_ACTUAL_DATE_START",
                "ITH.ITASKH_ACTUAL_DATE_FINISH",
                "ITH.ITASKH_DUE_DATE_START",
                "ITH.ITASKH_DUE_DATE_FINISH",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                globalThis.database.raw(
                    `"UF"."USER_DISPLAY_NAME" "ITASKH_FINISHED_BY_USER"`,
                ), // <---
                "ITH.ITASKH_USER_ID",
                globalThis.database.raw(
                    `"UO"."USER_DISPLAY_NAME" "ITASKH_USER"`,
                ), // <---
                "ITH.ITASKH_NOTE",
                "ITH.ITASK_ID",
            ])
            .from("INSTANCE_TASK_HISTORY as ITH")
            .leftJoin(
                "INSTANCE_VARIABLE_HISTORY as IVH",
                "ITH.ITASKH_ID",
                "IVH.ITASKH_ID",
            )
            .leftJoin("INSTANCE_VARIABLES as IV", "IVH.IVAR_ID", "IV.IVAR_ID")
            .leftJoin("INSTANCE_TASKS as IT", "IT.ITASK_ID", "ITH.ITASK_ID")
            .leftJoin("USERS AS UO", "UO.USER_ID", "ITH.ITASKH_USER_ID")
            .leftJoin(
                "USERS AS UF",
                "UF.USER_ID",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
            )
            .where("IT.IPROC_ID", iprocId)
            .orderBy("ITH.ITASKH_ACTUAL_DATE_FINISH", "asc")
            .orderBy("ITH.ITASKH_ID")
            .orderBy("IV.IVAR_NAME");
    }

    // ************************* INSTANCE_INTERFACE ******************************
    async copyInstanceFromTemplate(
        userId,
        tprocPrimary,
        headerId,
        iprocName,
        iprocNotes,
        iprocOrgstrId,
        dueDateFinish,
        priority,
        iprocEventStarter,
        useCache = true,
    ) {
        try {
            if (!Array.isArray(tprocPrimary) || tprocPrimary.length !== 2) {
                throw new InternalException(
                    "Can not instantiate template process. TprocPrimary is not array. Please fix with [tprocId, tprocVersion] value.",
                );
            }

            const tprocRepo = globalThis.orm.repo(
                "templateProcess",
                this.connection,
            );

            // Check access rights
            const template = await tprocRepo.canInstantiateProcess(
                userId,
                tprocPrimary[0] /* TPROC_ID */,
                useCache,
            );

            // Instantiate process
            const proc = await this.createProcess(
                headerId,
                1,
                tprocPrimary,
                iprocName,
                template.TPROC_DESCRIPTION,
                iprocNotes,
                userId,
                iprocOrgstrId,
                dueDateFinish,
                priority,
                template.TPROC_DMS_VISIBILITY,
                iprocEventStarter,
                template.TPROC_URL,
                template.TPROC_URL_TAB_NAME,
                template.TPROC_ASSIGN_OR_TAKEOVER_ROLE_ID,
            );

            // Instantiate tasks
            const taskRepo = globalThis.orm.repo(
                "instanceTask",
                this.connection,
            );
            await taskRepo.copyTasksFromTemplate(proc);

            // Update sequence number.
            await globalThis.orm
                .repo("InstanceVariableSequence", this.connection)
                .updateSequenceForProcess(proc);

            return proc;
        } catch (err) {
            globalThis.tasLogger.error(
                `copyInstanceFromTemplate error: ${err.message}`,
                {
                    err,
                    userId,
                    tprocPrimary,
                    headerId,
                    iprocName,
                    iprocNotes,
                    iprocOrgstrId,
                    dueDateFinish,
                    priority,
                    iprocEventStarter,
                },
            );
            throw err;
        }
    }

    // ************************* INSTANCE_INTERFACE ******************************
    async copyInstanceEventFromTemplate(
        tprocPrimary,
        iprocName,
        iprocNotes = "",
        iprocUserId,
        iprocOrgstrId: number | null = null,
        dueDateFinish: Date | null = null,
        priority = "M",
        iprocEventStarter: number | null = null,
    ) {
        if (!Array.isArray(tprocPrimary) || tprocPrimary.length !== 2) {
            throw new InternalException(
                "Can not instantiate template process. TprocPrimary is not array. Please fix with [tprocId, tprocVersion] value.",
            );
        }

        const tprocRepo = globalThis.orm.repo(
            "templateProcess",
            this.connection,
        );
        const tproc = await tprocRepo.get(tprocPrimary);
        const proc = await this.createProcess(
            null,
            1,
            tprocPrimary,
            iprocName,
            tproc.TPROC_DESCRIPTION,
            iprocNotes,
            Number(iprocUserId),
            Number(iprocOrgstrId),
            dueDateFinish,
            priority,
            tproc.TPROC_DMS_VISIBILITY,
            iprocEventStarter,
            tproc.TPROC_URL,
            tproc.TPROC_URL_TAB_NAME,
            tproc.TPROC_VERSION,
        );

        const taskRepo = globalThis.orm.repo("instanceTask", this.connection);
        await taskRepo.copyTasksFromTemplate(proc);
        return proc;
    }

    /**
     * Create process.
     * @param header_id
     * @param org_id
     * @param tprocPrimary
     * @param iproc_name
     * @param iproc_description
     * @param iproc_notes
     * @param iproc_inst_owner_user_id
     * @param iproc_inst_owner_orgstr_id
     * @param iproc_due_date_finish
     * @param iproc_priority
     * @param iproc_dms_visibility
     * @param iproc_event_starter
     * @param iproc_url
     * @param iproc_url_tab_name
     * @returns {*}
     */
    async createProcess(
        header_id,
        org_id,
        tprocPrimary,
        iproc_name,
        iproc_description,
        iproc_notes,
        iproc_inst_owner_user_id,
        iproc_inst_owner_orgstr_id,
        iproc_due_date_finish,
        iproc_priority,
        iproc_dms_visibility,
        iproc_event_starter,
        iproc_url,
        iproc_url_tab_name,
        iproc_assign_or_takeover_role_id,
    ) {
        // Fill default header id.
        if (!header_id) {
            header_id = (await this.getDefaultHeaderId(tprocPrimary[0]))
                .HEADER_ID;
        }

        const entity = this.getEntity({
            ORG_ID: org_id,
            HEADER_ID: header_id,
            TPROC_ID: tprocPrimary[0],
            IPROC_NAME: iproc_name || "Edit",
            IPROC_NOTES: iproc_notes,
            IPROC_DESCRIPTION: iproc_description,
            IPROC_INST_OWNER_USER_ID: iproc_inst_owner_user_id,
            IPROC_INST_OWNER_ORGSTR_ID: iproc_inst_owner_orgstr_id,
            IPROC_STATUS: "N",
            IPROC_DUE_DATE_FINISH: iproc_due_date_finish,
            IPROC_PRIORITY: iproc_priority,
            IPROC_EVENT_STARTER: iproc_event_starter,
            IPROC_DMS_VISIBILITY: iproc_dms_visibility,
            IPROC_URL_TAB_NAME: iproc_url_tab_name,
            IPROC_URL: iproc_url,
            IPROC_ASSIGN_OR_TAKEOVER_ROLE_ID: iproc_assign_or_takeover_role_id,
        });

        // Translations
        const extraColumns = [];
        const tProcEntity = globalThis.orm.repo("templateProcess").getEntity();
        const attrs = tProcEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(attrName);
            }
        });

        const iproc = await this.store(entity);
        await globalThis.orm
            .repo("InstanceProcessVersion", this.connection)
            .createForProcess(iproc.IPROC_ID, tprocPrimary[1]);

        const instanceProcessHistoryRepo = globalThis.orm.repo(
            "instanceProcessHistory",
            this.connection,
        );
        const userRepo = globalThis.orm.repo("user", this.connection);
        const user = await userRepo.get(iproc_inst_owner_user_id);

        await instanceProcessHistoryRepo.generateRecord(
            iproc.IPROC_ID,
            `Case owner: ${user.USER_DISPLAY_NAME}`,
            null,
            null,
            new Date(),
            false,
            ProcHistoryConst.CASE_OWNER_CHANGE,
        );

        return iproc;
    }

    getByTemplate(tProcId, columns) {
        const conn = this.connection
            .select(columns || "IPROC_ID")
            .from(this.tableName)
            .where("TPROC_ID", tProcId);

        return globalThis.orm.collection("Process", conn);
    }

    /**
     * Get first fetched header.
     * @param {number} tprocId
     * @returns {Promise}
     */
    getDefaultHeaderId(tprocId) {
        const headerRepo = globalThis.orm.repo("header", this.connection);
        return headerRepo.getHeadersByTProcId(tprocId).fetchOne();
    }

    async updateProcessFilesTags(entity) {
        if (globalThis.dynamicConfig.dms.fulltext) {
            // Trigger only when updating IPROC_NAME
            const { IPROC_NAME: newProcName } = entity.getAttributes(
                true,
                false,
            );

            if (newProcName && entity.id) {
                const fileRepo = globalThis.orm.repo(
                    "dmsFile",
                    this.connection,
                );
                const files = await (
                    await fileRepo.getForProcessOnly(entity.id)
                ).collectAll();

                // Trigger only if files are present
                if (files.length) {
                    // Prepare tags
                    const tagsToStore = files.map(({ DMSF_ID }) => ({
                        DMST_ID: TAG.TYPE_CASE,
                        DMSF_ID,
                        DMST_VALUE: newProcName,
                        DATETIME_INSERT: new Date(),
                        USER_ID_INSERT: -1,
                    }));

                    // @t3b-2080 Chyba pri zakladani pripadu a zmene pripadu
                    // We don't really re-index files by the Cron, so let's just disable this
                    // There will be a mismatch, but nobody seems to mind

                    /*
                    // Mark the File so it can later be Elastic-indexed by Cron
                    await this
                        .connection('DMS_FILE')
                        .update({
                            TO_INDEX: FILE.TO_INDEX_YES,
                        })
                        // Re-run the query because of a possible big number of files => slow .whereIn
                        .whereIn('DMSF_ID', (await fileRepo.getForProcessOnly(entity.id))
                            .knex
                            .clearSelect()
                            .select('DF.DMSF_ID'),
                        );
                    */

                    // Delete DMS_FILE tags
                    await this.connection("DMS_FILE_TAG")
                        // Re-run the query because of a possible big number of files => slow .whereIn
                        .whereIn(
                            "DMSF_ID",
                            (await fileRepo.getForProcessOnly(entity.id)).knex
                                .clearSelect()
                                .select("DF.DMSF_ID"),
                        )
                        .where("DMST_ID", TAG.TYPE_CASE)
                        .delete();

                    // Insert DMS_FILE tags
                    await this.connection("DMS_FILE_TAG").insert(tagsToStore);
                }
            }
        }
    }

    async store(entity) {
        // if (!entity.IPROC_INST_OWNER_USER_ID){
        //     entity.IPROC_INST_OWNER_USER_ID = 1;
        // }

        // @t3b-1529 autozápis CASE_STATUSu
        if (entity.IPROC_CASE_STATUS) {
            const caseStatusRepo = globalThis.orm.repo(
                "caseStatus",
                this.connection,
            );
            const statusExists = (
                await caseStatusRepo.getByValueForTemplate(
                    entity.IPROC_CASE_STATUS,
                    entity.TPROC_ID,
                )
            ).length;
            if (!statusExists) {
                const caseStatusEntity = caseStatusRepo.getEntity({
                    TPROC_ID: entity.TPROC_ID,
                    CS_NAME: entity.IPROC_CASE_STATUS,
                });
                caseStatusEntity.forceToInsert();
                globalThis.tasLogger.warning(
                    "Case status not found, creating a new entry",
                    {
                        iproc_id: entity.IPROC_ID,
                        tproc_id: entity.TPROC_ID,
                        case_status: caseStatusEntity.CS_NAME,
                    },
                );
                await caseStatusRepo.store(caseStatusEntity);
            }
        }

        // When updating IPROC_NAME, attached file's tags need to be updated as well
        await this.updateProcessFilesTags(entity);

        const rightsRepo = globalThis.orm.repo(
            "externalRight",
            this.connection,
        );
        return await super.store(entity).then((id) => {
            entity.IPROC_ID = id;
            return rightsRepo
                .assignUserRights(
                    entity.IPROC_ID,
                    entity.IPROC_INST_OWNER_USER_ID,
                )
                .then(() => entity);
        });
    }

    /**
     * Change process owner.
     * @param {number} headerId
     * @param {number} newUserId
     * @param {number} oldUserId
     * @param {number} userId
     */
    async changeProcessOwner(
        headerId: number | number[] | undefined,
        newUserId: number,
        oldUserId: number | undefined,
        userId: number,
    ): Promise<void> {
        const headerIds = Array.isArray(headerId)
            ? headerId
            : headerId !== undefined
              ? [headerId]
              : undefined;

        // Build the base query
        const query = this.connection
            .select("IPROC_ID", "HEADER_ID")
            .from(this.tableName)
            .where("IPROC_INST_OWNER_USER_ID", oldUserId);

        if (headerIds) {
            query.whereIn("HEADER_ID", headerIds);
        }

        const processes = await query;

        if (Array.isArray(processes) && processes.length > 0) {
            const processIds = processes.map(
                (process: { IPROC_ID: number }) => process.IPROC_ID,
            );
            await this.changeProcessOwnerForProcesses(
                processIds,
                newUserId,
                userId,
                oldUserId,
            );
        }
    }

    /**
     * Change process owner.
     * @param iprocIds
     * @param newUserId
     * @param userId
     * @param oldUserId
     */
    async changeProcessOwnerForProcesses(
        iprocIds: number[],
        newUserId: number,
        userId: number,
        oldUserId: number = null,
    ) {
        const userRepo = globalThis.orm.repo("user", this.connection);
        const rightRepo = globalThis.orm.repo("externalRight", this.connection);
        const user = await userRepo.get(newUserId); // Throw error if user not found !

        await rightRepo.removeStaticRightsWhenChangingCaseOwner(
            iprocIds,
            oldUserId,
        );

        await this.connection
            .select()
            .from("INSTANCE_PROCESSES")
            .whereIn("IPROC_ID", iprocIds)
            .update({ IPROC_INST_OWNER_USER_ID: newUserId });

        const instanceProcessHistoryRepo = globalThis.orm.repo(
            "instanceProcessHistory",
            this.connection,
        );

        const rightsAssignment = [];

        for (const id of iprocIds) {
            await instanceProcessHistoryRepo.generateRecord(
                id,
                `HR changed case owner to ${user.USER_DISPLAY_NAME}`,
                userId,
                userId,
                new Date(),
                false,
                ProcHistoryConst.CASE_OWNER_CHANGE,
            );

            // Assign new rights
            const result = await rightRepo.assignUserRights(id, newUserId);
            rightsAssignment.push(result);
        }
        return rightsAssignment;
    }

    /**
     *
     * @param {number} iprocId IPROC_ID
     */
    getParentProcessStatus(iprocId) {
        return this.connection
            .select("IPROC_STATUS")
            .from("INSTANCE_PROCESSES")
            .whereIn("IPROC_ID", function () {
                this.select("IPROC_MAIN_IPROC_ID")
                    .from("INSTANCE_PROCESSES")
                    .where("IPROC_ID", iprocId);
            })
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    return null;
                }
                return rows[0].IPROC_STATUS;
            });
    }

    /**
     * Set process as returned. Set IPROC_SUBPROCESS_FLAG = 2
     * @param process
     * @returns {*}
     */
    setIProcessSubProcessReturn(process) {
        process.setSubprocessReturn();
        return this.store(process);
    }

    /**
     * Remove whole process and dependent entities.
     *
     * @param process
     */
    async delete(process) {
        try {
            // if is subprocess then end main task and mark with as deleted
            // todo: subprocess
            /* if (process.isSubprocess()) {
                try {
                    task = this.getMainTask();
                    task.closeTask('Subprocess deleted');
                } catch (e) {
                    // mask out if main process was not found
                    if($e->getCode() != 1404)
                        throw $e;
                }
            } */

            await this.connection
                .from("INSTANCE_GRAPH")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();

            await this.connection
                .from("XML_PROCESS_IMPORT")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();

            await this.connection
                .from("DMS_FILE_ACCESS_LOG")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();

            await this.connection
                .from("INSTANCE_PROCESS_VERSIONS")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();

            await this.connection
                .from("INSTANCE_TASK_LINK_DONE")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();

            const vars = await this.repo("Variable", this.connection)
                .getByProcess(process.IPROC_ID)
                .collectAll();
            for (const ivar of vars) {
                await this.repo("Variable", this.connection).delete(ivar);
            }

            const tasks = await this.repo("InstanceTask", this.connection)
                .getByProcess(process.IPROC_ID)
                .collectAll();
            for (const itask of tasks) {
                await this.repo("InstanceTask", this.connection).delete(itask);
            }
            await this.connection
                .from("INSTANCE_PROCESS_HISTORY")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();
            await this.connection
                .from("INSTANCE_PROCESS_STATIC_RIGHTS")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();
            await this.connection
                .from("INSTANCE_PROCESS_NOTES")
                .where("IPROC_ID", process.IPROC_ID)
                .delete();

            // todo: remove all files
            return await BaseRepository.prototype.delete.call(this, process); // workaround of super() in async
        } catch (e) {
            throw new InternalException(e);
        }
    }

    getLastTask(iprocId, onlyStandard = true, currentlyActive = false) {
        const conn = this.connection
            .select("ITASK_ID")
            .from("INSTANCE_TASKS")
            .where("IPROC_ID", iprocId)
            .orderBy("ITASK_ID", "desc")
            .first();

        if (onlyStandard) {
            conn.where("ITASK_TYPE", TASK.TYPE_STANDARD);
        }

        if (currentlyActive) {
            conn.where("ITASK_STATUS", TASK.STATUS_ACTIVE);
        } else {
            conn.where("ITASK_STATUS", TASK.STATUS_DONE);
        }

        return conn;
    }

    async archive(iprocId) {
        const tm = new TimeMeasure();
        tm.start();
        // Keep order!
        const archivedRepos = [
            "Task",
            "DmsFile",
            "InstanceProcessDynRights",
            "InstanceProcessHistory",
            "InstanceProcessStaticRight",
            "InstanceProcessVersion",
            "InstanceTaskLinkDone",
            "PlanProcessLog",
            "InstanceGraph",
            "InstanceProcessNote",
            "Variable",
            "InstanceTaskHistory",
            "DmsFileAccessLog", // -- traverse --
            "DmsFileTag",
            "VariableLov",
            "VariableSnap",
            "InstanceTaskCompletition",
            "InstanceTaskEmailNotifs",
            "MailQ",
            "InstanceVariableHistory",
        ];

        await super.archive(iprocId);
        for (let i = 0; i < archivedRepos.length; i += 1) {
            await globalThis.orm
                .repo(archivedRepos[i], this.connection)
                .archive(iprocId);
        }

        for (let i = archivedRepos.length - 1; i >= 0; i -= 1) {
            await globalThis.orm
                .repo(archivedRepos[i], this.connection)
                .deleteForProcess(iprocId);
        }
        await super.deleteForProcess(iprocId);

        await globalThis.perfLogs.log(
            globalThis.perfLogs.consts.TYPE_PROCESS_ARCHIVATION,
            tm.endMs(),
            {
                IPROC_ID: iprocId,
            },
        );
    }

    async unarchive(iprocId) {
        const tm = new TimeMeasure();
        tm.start();
        // Keep order!
        const archivedRepos = [
            "Task",
            "DmsFile",
            "InstanceProcessDynRights",
            "InstanceProcessHistory",
            "InstanceProcessStaticRight",
            "InstanceProcessVersion",
            "InstanceTaskLinkDone",
            "PlanProcessLog",
            "InstanceGraph",
            "InstanceProcessNote",
            "Variable",
            "InstanceTaskHistory",
            "DmsFileAccessLog", // -- traverse --
            "DmsFileTag",
            "VariableLov",
            "VariableSnap",
            "InstanceTaskCompletition",
            "InstanceTaskEmailNotifs",
            "MailQ",
            "InstanceVariableHistory",
        ];

        await super.unarchive(iprocId);
        for (let i = 0; i < archivedRepos.length; i += 1) {
            globalThis.tasLogger.info(`Archiving ${iprocId}`, {
                iproc_id: iprocId,
            });
            await globalThis.orm
                .repo(archivedRepos[i], this.connection)
                .unarchive(iprocId);
        }

        for (let i = archivedRepos.length - 1; i >= 0; i -= 1) {
            await globalThis.orm
                .repo(archivedRepos[i], this.connection)
                .deleteArchivedForProcess(iprocId);
        }
        await super.deleteArchivedForProcess(iprocId);

        await globalThis.perfLogs.log(
            globalThis.perfLogs.consts.TYPE_PROCESS_UNARCHIVATION,
            tm.endMs(),
            {
                IPROC_ID: iprocId,
            },
        );
    }

    async getTTaskVariableList(iprocId) {
        const connection = this.connection
            .select()
            .from("INSTANCE_TASKS as IT")
            .join(
                "TEMPLATE_TASK_VAR_USAGE as ITVU",
                "ITVU.TTASK_ID",
                "IT.TTASK_ID",
            )
            .join("INSTANCE_VARIABLES as VAR", {
                "VAR.TVAR_ID": "ITVU.TVAR_ID",
                "VAR.IPROC_ID": "IT.IPROC_ID",
            })
            .where("IT.IPROC_ID", iprocId)
            .where("IT.ITASK_MULTIINSTANCE_FLAG", "<>", "M");

        return globalThis.orm.collection("Process", connection);
    }
}
