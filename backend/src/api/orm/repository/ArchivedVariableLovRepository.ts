import { BaseRepository } from "./BaseRepository";
import { BaseCollection } from "../BaseCollection";
import { Variable } from "../entity/Variable";
import * as VARIABLE from "../entity/const/variableConst";
import { VariableLov } from "../entity/VariableLov";

export class ArchivedVariableLovRepository extends BaseRepository<VariableLov> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_VARIABLE_LOV",
            entityName: "archivedVariableLov",
            entity: () => new VariableLov(),
            archParams: {
                subQueryTable: "ARCH_INSTANCE_VARIABLES",
                subQueryColumn: "IVAR_ID",
            },
        };
    }

    getForVariables(
        columns: string[],
        vars: any[],
    ): BaseCollection<VariableLov> {
        const coll = this.getCollection(columns);

        coll.knex
            .whereIn(
                "IVAR_ID",
                vars instanceof BaseCollection ? vars.knex : vars,
            )
            .orderBy("IVARLOV_ID");
        return coll;
    }

    async fillLovValues(vars: any[]) {
        if (!Array.isArray(vars)) {
            vars = [vars];
        }

        for (const item of vars) {
            if (item.value === null || item.IVAR_TYPE !== "DL") {
                continue;
            }

            if (item.isMultichoice()) {
                if (!item.value) {
                    globalThis.tasLogger.error(
                        `Variable for DL store is empty ${item.IVAR_NAME}`,
                    );
                    continue;
                }

                const ids =
                    typeof item.value === "string"
                        ? JSON.parse(item.value || "[]")
                        : item.value;

                try {
                    if (item.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER) {
                        const users = await globalThis.orm
                            .repo("user", this.connection)
                            .getMulti(ids);
                        item.lovValues = {};
                        users.forEach((user) => {
                            item.lovValues[user.USER_ID] =
                                user.USER_DISPLAY_NAME;
                        });
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT
                    ) {
                        const orgs = await globalThis.orm
                            .repo("organizationStructure", this.connection)
                            .getMulti(ids);
                        item.lovValues = {};
                        orgs.forEach((orgstr) => {
                            item.lovValues[orgstr.ORGSTR_ID] =
                                orgstr.ORGSTR_NAME;
                        });
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE
                    ) {
                        const roles = await globalThis.orm
                            .repo("role", this.connection)
                            .getMulti(ids);
                        item.lovValues = {};
                        roles.forEach((role) => {
                            item.lovValues[role.ROLE_ID] = role.ROLE_NAME;
                        });
                    }
                } catch (err) {
                    globalThis.tasLogger.error({
                        err,
                    });
                    item.lovValues = {};
                }
            } else {
                try {
                    if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER &&
                        item.value > 0
                    ) {
                        const user = await globalThis.orm
                            .repo("user", this.connection)
                            .get(item.value);
                        item.lovValue = user.USER_DISPLAY_NAME;
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT
                    ) {
                        const org = await globalThis.orm
                            .repo("organizationStructure", this.connection)
                            .get(item.value);
                        item.lovValue = org.ORGSTR_NAME;
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE
                    ) {
                        const role = await globalThis.orm
                            .repo("role", this.connection)
                            .get(item.value);
                        item.lovValue = role.ROLE_NAME;
                    }
                } catch (err) {
                    globalThis.tasLogger.error(
                        `${err} ivar_id = ${item.IVAR_ID}`,
                    );
                    item.lovValue = null;
                }
            }
        }
        return vars;
    }

    getVariablesByProcess(iProcId: number): BaseCollection<Variable> {
        const conn = this.connection
            .select(["IV.IVAR_ID"])
            .from("ARCH_INSTANCE_VARIABLES as IV")
            .where("IV.IPROC_ID", iProcId)
            .where("IV.IVAR_TYPE", VARIABLE.TYPE_TEXT_LIST);

        return globalThis.orm.collection("archivedVariable", conn);
    }

    getListMutations(iprocId: number, ivarIds?: number[]) {
        const cols = ["IVARLOV_TEXT_VALUE"];
        globalThis.dynamicConfig.langs.forEach((language: string) => {
            cols.push(`${cols[0]}_${language.toUpperCase()}`);
        });
        if (Array.isArray(ivarIds) && ivarIds.length > 0) {
            cols.push("IVAR_ID");
            const coll = this.getCollection([...cols], "IVL");
            coll.knex.whereIn("IVAR_ID", ivarIds);
            return coll.fetchAll();
        }
        const coll = this.getCollection([...cols, "IV.IVAR_ID"], "IVL");
        coll.knex
            .join("ARCH_INSTANCE_VARIABLES as IV", "IV.IVAR_ID", "IVL.IVAR_ID")
            .where("IPROC_ID", iprocId)
            .andWhere("IVAR_TYPE", "LT");
        return coll.fetchAll();
    }
}
