// @ts-nocheck
// @ts-nocheck
import { TemplateSections } from "../entity/TemplateSections";
import { BaseRepository } from "./BaseRepository";

interface SectionWithTask {
    TSEC_ID: number;
    TSEC_NAME: string;
    TTASK_ID: number;
    TTASK_NAME: string;
}

export class TemplateSectionsRepository extends BaseRepository<TemplateSections> {
    meta() {
        return {
            tableName: "TEMPLATE_SECTIONS",
            entityName: "TemplateSections",
            entity: () => new TemplateSections(),
        };
    }

    getById(sectionId) {
        return globalThis.orm.collection(
            this,
            this.connection
                .select(`${this.tableName}.*`)
                .from(this.tableName)
                .where("TSEC_ID", sectionId),
        );
    }

    getFromTemplateByName(tprocId, sectionName) {
        return this.connection
            .first(`${this.tableName}.*`)
            .from(this.tableName)
            .where({
                TSEC_NAME: sectionName,
                TPROC_ID: tprocId,
            });
    }

    getForTemplateProcess(tProcId) {
        return globalThis.orm.collection(
            this,
            this.connection
                .select(`${this.tableName}.*`)
                .from(this.tableName)
                .where("TPROC_ID", tProcId),
        );
    }

    getForTemplateProcessWithTasks(tProcId): Promise<SectionWithTask[]> {
        const columns = [
            "TS.TSEC_ID",
            "TS.TSEC_NAME",
            "TT.TTASK_ID",
            "TT.TTASK_NAME",
        ];

        globalThis.dynamicConfig.langs.forEach((lang) => {
            columns.push(`TT.TTASK_NAME_${lang.toUpperCase()}`);
            columns.push(`TS.TSEC_NAME_${lang.toUpperCase()}`);
        });

        return this.connection
            .select(columns)
            .from(`${this.tableName} as TS`)
            .leftJoin(
                "TEMPLATE_TASK_VAR_USAGE as TVU",
                "TS.TSEC_ID",
                "TVU.TSEC_ID",
            )
            .leftJoin("TEMPLATE_TASKS as TT", "TVU.TTASK_ID", "TT.TTASK_ID")
            .where("TS.TPROC_ID", tProcId)
            .orderBy("TS.TSEC_NAME", "ASC")
            .orderBy("TT.TTASK_NAME", "ASC");
    }

    delete(entity) {
        return this.connection.transaction(async (trx) => {
            const defaultAxisObject = {};
            const rowsToUpdate = await trx("TEMPLATE_TASK_VAR_USAGE")
                .select("TVAR_ID", "TTASK_ID", "AXIS_Y", "TSEC_Y")
                .where("TSEC_ID", entity.id);

            for (const row of rowsToUpdate) {
                const { TVAR_ID, TTASK_ID, AXIS_Y } = row;
                let taskRows = await trx("TEMPLATE_TASK_VAR_USAGE")
                    .select("AXIS_Y", "TSEC_Y")
                    .where({
                        TTASK_ID,
                        TSEC_ID: null,
                    })
                    .orderBy("AXIS_Y", "desc")
                    .first();

                // zero section does not exist, use the TSEC_Y of the last section
                if (!taskRows) {
                    const maxTsecY = await trx("TEMPLATE_TASK_VAR_USAGE")
                        .max("TSEC_Y as maxY")
                        .where("TTASK_ID", TTASK_ID)
                        .first();

                    taskRows = {
                        AXIS_Y: 0,
                        TSEC_Y: maxTsecY.maxY + 1,
                    };
                }

                if (!(TTASK_ID in defaultAxisObject)) {
                    defaultAxisObject[TTASK_ID] = taskRows.AXIS_Y;
                }

                const { TSEC_Y } = taskRows;

                await trx("TEMPLATE_TASK_VAR_USAGE")
                    .update({
                        TSEC_ID: null,
                        TSEC_X: 0,
                        TSEC_Y,
                        AXIS_Y:
                            Number(AXIS_Y) +
                            Number(defaultAxisObject[TTASK_ID]),
                    })
                    .where({ TVAR_ID, TTASK_ID });
            }

            for (const TTASK_ID of Object.keys(defaultAxisObject)) {
                // if there is no other section at the same TSEC_Y as the deleted section,
                // the sections below the deleted section will be moved up
                await trx("TEMPLATE_TASK_VAR_USAGE")
                    .where("TTASK_ID", TTASK_ID)
                    .where("TSEC_Y", ">", rowsToUpdate[0].TSEC_Y)
                    .whereRaw(
                        `NOT EXISTS (
                            SELECT 1 FROM TEMPLATE_TASK_VAR_USAGE
                            WHERE TTASK_ID = ?
                            AND TSEC_ID <> ?
                            AND TSEC_Y = ?
                        )`,
                        [TTASK_ID, entity.id, rowsToUpdate[0].TSEC_Y],
                    )
                    .update({
                        TSEC_Y: trx.raw("TSEC_Y - 1"),
                    });
            }

            await trx(this._tableName)
                .where(this.primaryWhere(entity, entity.id))
                .del();
        });
    }
}
