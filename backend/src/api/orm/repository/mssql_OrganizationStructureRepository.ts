// @ts-nocheck
import _ from "lodash";

import { OrganizationStructureRepository } from "./OrganizationStructureRepository";
import * as OrganizationStructureConstants from "../entity/const/organizationStructureConsts";
import * as USER from "../entity/const/userConst";
import { OrganizationStructure } from "../entity/OrganizationStructure";
import { CATEGORY_REST } from "../../../utils/logger/logConsts";

export class mssql_OrganizationStructureRepository extends OrganizationStructureRepository {
    meta() {
        return {
            tableName: "ORGANIZATION_STRUCTURE",
            entityName: "OrganizationStructure",
            defaultAlias: "OS",
            entity: () => new OrganizationStructure(),
        };
    }

    getFlatTree() {
        const columns = [
            "OSTREE.ORGSTR_ID",
            "OSTREE.ORGSTR_NAME",
            "OSTREE.PARENT_ORGSTR_ID",
            "OSTREE.LVL as LEVEL",
            "OSTREE.MANAGER_USER_ID",
            "OS.EXTERNAL_ID",
            this.connection.raw(`U.USER_DISPLAY_NAME as MANAGER_USER`),
        ];

        return this.connection
            .select(columns)
            .from("V_ORG_STRUCT as OSTREE")
            .leftJoin("USERS as U", "U.USER_ID", "OSTREE.MANAGER_USER_ID")
            .innerJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "OS.ORGSTR_ID",
                "OSTREE.ORGSTR_ID",
            )
            .where(
                "OS.ORGANIZATION_STATUS",
                OrganizationStructureConstants.STATUS_ACTIVE,
            )
            .orderBy("OSTREE.PATH");
    }

    /**
     * This function returns *ALL CHILDREN* units of a given ORGSTR unit
     *
     * @param {number|Array<OrganizationStructure>} orgstrId - ID of OrganizationStructure or Array with a *SINGLE* OrganizationStructure
     * @returns {Knex.Builder} - returns a builder with ALL PARENT OrganizationStructures
     */
    getAllChildrenUnits(orgstrId) {
        if (Array.isArray(orgstrId)) {
            if (orgstrId.length === 0) {
                return false;
            }
            orgstrId = orgstrId[0].ORGSTR_ID;
        }

        return this.connection
            .with(
                "CHILDREN_UNITS",
                this.connection.raw(this.withChildrenUnits(), {
                    ORGSTR_ID: orgstrId,
                }),
            )
            .select()
            .from("CHILDREN_UNITS")
            .as("CHILDREN_UNITS");
    }

    /**
     * Used for Knex.with()
     * Returns all OrganizationStructure children units
     * Don't forget to bind ORGSTR_ID!
     *
     * @returns {string}
     */
    withChildrenUnits() {
        return `
        select top 1 * from ORGANIZATION_STRUCTURE OS
        where (OS.ORGSTR_ID = :ORGSTR_ID or :ORGSTR_ID is null)
            UNION ALL
        select CHILDREN_UNITS_2.*
        from ORGANIZATION_STRUCTURE as CHILDREN_UNITS_2, CHILDREN_UNITS
        where CHILDREN_UNITS.ORGSTR_ID = CHILDREN_UNITS_2.PARENT_ORGSTR_ID
        `;
    }

    /**
     * Used for Knex.with()
     * Returns all OrganizationStructure children units
     * Don't forget to bind MANAGER_USER_ID!
     *
     * INCLUDES THE STARTING NODES
     *
     * @returns {string}
     */
    withChildrenUnitsForManager() {
        return `
        select * from ORGANIZATION_STRUCTURE OS
        where (OS.ORGSTR_ID in (select ORGSTR_ID from ORGANIZATION_STRUCTURE where MANAGER_USER_ID = :MANAGER_USER_ID))
            UNION ALL
        select CHILDREN_UNITS_2.*
        from ORGANIZATION_STRUCTURE as CHILDREN_UNITS_2, CHILDREN_UNITS
        where CHILDREN_UNITS.ORGSTR_ID = CHILDREN_UNITS_2.PARENT_ORGSTR_ID
        `;
    }

    /**
     * This function returns *ACTIVE CHILDREN* units of a given ORGSTR unit
     *
     * @param {number} orgstrId - ID of OrganizationStructure
     * @returns {Knex.Builder} - returns a builder with ALL CHILDREN OrganizationStructures
     */
    getChildrenUnits(orgstrId) {
        const columns = [
            "OS.ORGSTR_NAME",
            "OS.ORGSTR_ID",
            "OS.PARENT_ORGSTR_ID",
            "OS.MANAGER_USER_ID",
            "OS.COMPANY_IC",
            "OS.ORGANIZATION_TYPE",
            "OS.EXTERNAL_ID",
            this.connection.raw(`U.USER_DISPLAY_NAME as MANAGER_USER_NAME`),
            this.connection.raw("OS_PARENT.ORGSTR_NAME as PARENT"),
            this.connection.raw("OS_PARENT.EXTERNAL_ID AS PARENT_EXTERNAL_ID"),
        ];

        return this.connection
            .with(
                "CHILDREN_UNITS",
                this.connection.raw(this.withChildrenUnits(), {
                    ORGSTR_ID: orgstrId,
                }),
            )
            .select()
            .from(
                this.connection
                    .select(columns)
                    .from("CHILDREN_UNITS as OSTREE")
                    .innerJoin(
                        "ORGANIZATION_STRUCTURE as OS",
                        "OS.ORGSTR_ID",
                        "OSTREE.ORGSTR_ID",
                    )
                    .leftJoin(
                        "ORGANIZATION_STRUCTURE as OS_PARENT",
                        "OS.PARENT_ORGSTR_ID",
                        "OS_PARENT.ORGSTR_ID",
                    )
                    .leftJoin(
                        "USERS as U",
                        "U.USER_ID",
                        "OSTREE.MANAGER_USER_ID",
                    )
                    .where(
                        "OS.ORGANIZATION_STATUS",
                        OrganizationStructureConstants.STATUS_ACTIVE,
                    )
                    .as("OS"),
            )
            .as("OS");
    }

    /**
     * This function returns *ACTIVE PARENT* units of a given ORGSTR unit
     *
     * @param {number} orgstrId - ID of OrganizationStructure
     * @returns {Knex.Builder} - returns a builder with ALL CHILDREN OrganizationStructures
     */
    getParentUnits(orgstrId) {
        const columns = [
            "OS.ORGSTR_NAME",
            "OS.ORGSTR_ID",
            "OS.PARENT_ORGSTR_ID",
            "OS.MANAGER_USER_ID",
            "OS.COMPANY_IC",
            "OS.ORGANIZATION_TYPE",
            this.connection.raw(`U.USER_DISPLAY_NAME as MANAGER_USER_NAME`),
            this.connection.raw("OS_PARENT.ORGSTR_NAME as PARENT"),
            this.connection.raw("OS_PARENT.EXTERNAL_ID AS PARENT_EXTERNAL_ID"),
        ];

        return this.connection
            .with(
                "PARENT_UNITS",
                this.connection.raw(this.withParentUnits(), {
                    ORGSTR_ID: orgstrId,
                }),
            )
            .select()
            .from(
                this.connection
                    .select(columns)
                    .from("PARENT_UNITS as OSTREE")
                    .innerJoin(
                        "ORGANIZATION_STRUCTURE as OS",
                        "OS.ORGSTR_ID",
                        "OSTREE.ORGSTR_ID",
                    )
                    .leftJoin(
                        "ORGANIZATION_STRUCTURE as OS_PARENT",
                        "OS.PARENT_ORGSTR_ID",
                        "OS_PARENT.ORGSTR_ID",
                    )
                    .leftJoin(
                        "USERS as U",
                        "U.USER_ID",
                        "OSTREE.MANAGER_USER_ID",
                    )
                    .where(
                        "OS.ORGANIZATION_STATUS",
                        OrganizationStructureConstants.STATUS_ACTIVE,
                    )
                    .as("OS"),
            )
            .as("OS");
    }

    /**
     * Used for Knex.with()
     * Returns all OrganizationStructure parent units
     * Don't forget to bind ORGSTR_ID!
     *
     * @returns {string}
     */
    withParentUnits() {
        return `
        select top 1 * from ORGANIZATION_STRUCTURE OS
        where (OS.ORGSTR_ID = :ORGSTR_ID or :ORGSTR_ID is null)
            UNION ALL
        select PARENT_UNITS_2.*
        from ORGANIZATION_STRUCTURE as PARENT_UNITS_2, PARENT_UNITS
        where PARENT_UNITS.PARENT_ORGSTR_ID = PARENT_UNITS_2.ORGSTR_ID
        `;
    }

    /**
     * Returns Organization tree starting at the given OrganizationStructure
     *
     * @param {number} orgStrId - ID of OrganizationStructure
     * @returns {Knex.Builder} - Returns a builder with Organization tree
     */
    getOrgStructure(orgStrId = null) {
        const conn = this.connection
            .with(
                "ORGANIZATION_TREE",
                this.connection.raw(this.withOrganizationTree(), {
                    ORGSTR_ID: orgStrId,
                }),
            )
            .select(
                "OSTREE.ORGSTR_ID",
                "OSTREE.ORGSTR_NAME",
                "OSTREE.PARENT_ORGSTR_ID",
                "OSTREE.MANAGER_USER_ID",
                "OSTREE.LVL as LEVEL",
                this.connection.raw(`U.USER_DISPLAY_NAME as MANAGER_USER`),
            )
            .from("ORGANIZATION_TREE as OSTREE")
            .innerJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "OS.ORGSTR_ID",
                "OSTREE.ORGSTR_ID",
            )
            .leftJoin("USERS as U", "OSTREE.MANAGER_USER_ID", "U.USER_ID")
            .where(
                "OS.ORGANIZATION_STATUS",
                OrganizationStructureConstants.STATUS_ACTIVE,
            )
            .orderBy("OSTREE.PATH");

        return conn;
    }

    /**
     * Used for Knex.with()
     * Returns the whole OrganizationStructure tree
     * Don't forget to bind ORGSTR_ID!
     *
     * @returns {string}
     */
    withOrganizationTree() {
        return `
        select ORGSTR_ID, ORG_ID, ORGSTR_NAME, MANAGER_USER_ID, PARENT_ORGSTR_ID, ORGANIZATION_STATUS, 1 AS LVL, cast('/' as varchar) + cast('' as varchar) + cast(ORGSTR_ID as varchar) as PATH
        from ORGANIZATION_STRUCTURE
        where (PARENT_ORGSTR_ID = :ORGSTR_ID or (PARENT_ORGSTR_ID is null and :ORGSTR_ID is null))
          union all
        select 
            ORGANIZATION_TREE_2.ORGSTR_ID, ORGANIZATION_TREE_2.ORG_ID, ORGANIZATION_TREE_2.ORGSTR_NAME, ORGANIZATION_TREE_2.MANAGER_USER_ID, 
            ORGANIZATION_TREE_2.PARENT_ORGSTR_ID, ORGANIZATION_TREE_2.ORGANIZATION_STATUS, LVL + 1, cast(PATH as varchar) + cast('/' as varchar) + cast(ORGANIZATION_TREE_2.ORGSTR_ID as varchar)
        from ORGANIZATION_STRUCTURE as ORGANIZATION_TREE_2, ORGANIZATION_TREE
        where ORGANIZATION_TREE.ORGSTR_ID = ORGANIZATION_TREE_2.PARENT_ORGSTR_ID
        `;
    }

    async getManagedUsers(userId) {
        // Get User OrganizationStructure

        try {
            const orgStructures = await this.connection
                .select("OS.ORGSTR_ID")
                .from("ORGANIZATION_STRUCTURE as OS")
                .where("OS.MANAGER_USER_ID", userId);
            // Get children OrganizationStructures

            const childrenOrgStrIds = [];
            for (const orgStr of orgStructures) {
                const childrenUnits = await this.connection
                    .with(
                        "CHILDREN_UNITS",
                        this.connection.raw(this.withChildrenUnits(), {
                            ORGSTR_ID: orgStr.ORGSTR_ID,
                        }),
                    )
                    .select("ORGSTR_ID")
                    .from("CHILDREN_UNITS")
                    .as("CHILDREN_UNITS");

                if (Array.isArray(childrenUnits) && childrenUnits.length > 0) {
                    childrenOrgStrIds.push(
                        ...childrenUnits.map((child) => child.ORGSTR_ID),
                    );
                }
            }

            const users = await this.connection
                .select("U.USER_ID")
                .from("USER_ORGANIZATION_STRUCTURE as UOS")
                .leftJoin("USERS as U", "U.USER_ID", "UOS.USER_ID")
                .whereIn(
                    "ORGSTR_ID",
                    Array.isArray(childrenOrgStrIds)
                        ? childrenOrgStrIds
                        : [childrenOrgStrIds],
                )
                .where("U.USER_STATUS", USER.STATUS_ACTIVE)
                .whereNotNull("U.USER_ID");

            // Get all manager Users in children OrganizationStructures
            // TODO: this.on('U.USER_ID', 'OS.MANAGER_USER_ID').orOn('U.USER_ID', 'UOS.USER_ID'); // manager or user

            const managers = await this.connection
                .select("OS.MANAGER_USER_ID")
                .from("ORGANIZATION_STRUCTURE as OS")
                .whereNotNull("OS.MANAGER_USER_ID")
                .whereIn(
                    "OS.ORGSTR_ID",
                    Array.isArray(childrenOrgStrIds)
                        ? childrenOrgStrIds
                        : [childrenOrgStrIds],
                );

            let userIds = users.map((user) => user.USER_ID);

            // Ensure that managers exist before adding them to the list
            if (Array.isArray(managers) && managers.length > 0) {
                const managerIds = managers.map(
                    (manager) => manager.MANAGER_USER_ID,
                );
                userIds = userIds.concat(managerIds);
            }

            userIds = _.uniq(userIds);
            userIds = _.pull(userIds, userId);

            return userIds;
        } catch (err) {
            globalThis.tasLogger.error({
                err,
                category: CATEGORY_REST,
            });
        }
    }

    /**
     * Get parents up to root.
     */
    getAllParents(orgstrId) {
        return this.connection
            .with(
                "PARENT_UNITS",
                this.connection.raw(this.withParentUnits(), {
                    ORGTSTR_ID: orgstrId,
                }),
            )
            .select()
            .from("ORGANIZATION_STRUCTURE as OS")
            .where(function () {
                this.where(
                    "OS.ORGANIZATION_STATUS",
                    OrganizationStructureConstants.STATUS_ACTIVE,
                ).whereIn("OS.ORGSTR_ID", function () {
                    this.select("ORGSTR_ID").from("PARENT_UNITS");
                });
            })
            .then((rows) => this.castRows(rows, this.entity.attributes()));
    }
}
