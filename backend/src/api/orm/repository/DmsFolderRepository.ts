// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { DmsFolder } from "../entity/DmsFolder";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { Rest } from "../../services/Rest";

export class DmsFolderRepository extends BaseRepository<DmsFolder> {
    meta() {
        return {
            tableName: "DMS_FOLDER",
            entityName: "DmsFolder",
            entity: () => new DmsFolder(),
        };
    }

    getAll() {
        const repo = this.connection.select().from(this.tableName);
        return globalThis.orm.collection("DmsFolder", repo);
    }

    async deleteFolder(folder) {
        if (!folder.FOLDER_ID) {
            throw new UserException("FOLDER_ID is not defined!");
        }

        await this.delete(folder);
        this.updateFolderLOV();
    }

    async createFolder(folder) {
        if (!folder.FOLDER_NAME) {
            throw new UserException("FOLDER_NAME is not defined!");
        }

        const folders = await this.connection
            .select(this.entity.primaryKey)
            .from(this.tableName)
            .where("FOLDER_NAME", folder.FOLDER_NAME)
            .where("PARENT_ID", folder.PARENT_ID);
        if (folders.length > 0) {
            throw new UserException(
                `Folder already exists! [FOLDER_NAME = ${folder.FOLDER_NAME}, PARENT_ID = ${folder.PARENT_ID}]`,
            );
        }

        const entity = await this.store(folder);
        this.updateFolderLOV();
        return entity;
    }

    createTree(folders) {
        if (!folders || !Array.isArray(folders) || folders.length == 0) {
            return null;
        }

        // Restify deeper items
        folders.forEach((f) => {
            f = Rest.keysToLowerCase(f);
            f.id = f.folder_id;
            delete f.folder_id;
        });

        // Create tree
        folders.forEach((fdst) => {
            fdst.children = [];

            fdst.label = fdst.folder_name;
            delete fdst.folder_name;

            folders.forEach((fsrc) => {
                if (fdst.id == fsrc.parent_id) {
                    fdst.children.push(fsrc);
                }
            });
        });

        // Remove duplicites
        let anyChange;
        do {
            anyChange = false;
            folders.forEach((f) => {
                if (f.parent_id) {
                    _.remove(folders, f);
                    anyChange = true;
                }
            });
        } while (anyChange);

        // Add TREE_INDEX info
        const fillTreeIndex = function (item, actualIndex) {
            item.tree_index = actualIndex;
            item.children.forEach((fnext) => {
                const nextIndex = actualIndex + 1;

                fillTreeIndex(fnext, nextIndex);
            });
        };
        folders.forEach((f) => {
            fillTreeIndex(f, 0);
        });

        return folders;
    }

    async getListOfValues() {
        const collection = this.getAll();
        const folders = await collection.fetchAll();
        const tree = this.createTree(folders);

        let out = "";
        const recursion = function (tree) {
            // exit on invalid or empty array
            if (!Array.isArray(tree) || tree.length == 0) {
                return;
            }
            tree.forEach((node) => {
                const spacedLabel = JSON.stringify(
                    "\u00A0\u00A0\u00A0\u00A0".repeat(node.tree_index) +
                        node.label,
                );
                out += `"${node.id}": ${spacedLabel}, `;

                recursion(node.children);
            });
        };
        recursion(tree);

        // Remove last comma
        out = `{${out.substring(0, out.length - 2)}}`;

        return out;
    }

    async updateFolderLOV() {
        const lov = await this.getListOfValues();
        const repo = globalThis.orm.repo("dmsTag", this.connection);
        const folderTag = await repo.get(-4);
        folderTag.LIST_OF_VALUES = lov;
        await repo.store(folderTag);
    }

    async getFolderByName(folderName, parentFolderId) {
        const connection = this.connection
            .select(this.entity.primaryKey)
            .from(this.tableName)
            .where("FOLDER_NAME", folderName);

        if (parentFolderId) {
            connection.where("PARENT_ID", parentFolderId);
        }

        const rows = await connection;
        return rows.length > 0 ? rows[0] : null;
    }
}
