// @ts-nocheck
// @ts-nocheck
import moment from "moment";
import momentBt from "moment-business-time";
import { BaseRepository } from "./BaseRepository";
import * as PlanConstants from "../entity/const/planConst";
import * as ProcessConstants from "../entity/const/processConst";
import { Workflow } from "../../workflow/Workflow";
import { OrmFactory } from "../OrmFactory";
import { PlanProcess } from "../entity/PlanProcess";
import { AuthException } from "../../../utils/errorHandling/exceptions/authException";
import { PlanUserRepository } from "./PlanUserRepository";
import { UserRepository } from "./UserRepository";
import { PlanProcessLogRepository } from "./PlanProcessLogRepository";
import { DateBusinessUtils } from "../../utils/DateBusinessUtils";
import { LogCategory } from "../../../utils/logger/logConsts";

export class PlanProcessRepository extends BaseRepository<PlanProcess> {
    meta() {
        return {
            tableName: "PLAN_PROCESS",
            entityName: "PlanProcess",
            entity: () => new PlanProcess(),
        };
    }

    getPlans() {
        const columns = [
            "OS.ORGSTR_NAME as PLN_ORGSTR_NAME",
            "OS2.ORGSTR_NAME as PLN_ASSESMENT_ORGSTR_NAME",
            "PP.PLN_ID",
            "PP.PLN_STATUS",
            "PP.PLN_ASSESMENT_ROLE_ID",
            "PP.PLN_USER_ID",
            "PP.PLN_NAME",
            "PP.PLN_REPEAT_OFFSET_TYPE",
            "PP.PLN_REPEAT_OFFSET_SUBTYPE",
            "PP.PLN_REPEAT_OFFSET",
            "PP.PLN_REPEAT_COUNT",
            "PP.PLN_REPEAT_EXCEPTIONS",
            "PP.PLN_START_DATETIME",
            "PP.PLN_ORGSTR_ID",
            "PP.PLN_REPEAT_UNTIL_DATE",
            "PP.PLN_ASSESMENT_ORGSTR_CNST",
            "PP.PLN_ASSESMENT_HIERARCHY",
            "PP.PLN_ASSESMENT_METHOD",
            "R.ROLE_NAME",
            "TH.*",
            "TP.TPROC_NAME",
            globalThis.database.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ];

        const conn = this.connection
            .select(columns)
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("PP.TPROC_ID", "TP.TPROC_ID")
                    .andOn("PP.TPROC_VERSION", "TP.TPROC_VERSION");
            })
            .leftJoin("HEADERS as TH", "TH.HEADER_ID", "PP.HEADER_ID")
            .leftJoin("USERS as U", "PP.PLN_USER_ID", "U.USER_ID")
            .leftJoin("ROLES as R", "PP.PLN_ASSESMENT_ROLE_ID", "R.ROLE_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "PP.PLN_ORGSTR_ID",
                "OS.ORGSTR_ID",
            )
            .leftJoin(
                "ORGANIZATION_STRUCTURE as OS2",
                "PP.PLN_ASSESMENT_ORGSTR_CNST",
                "OS2.ORGSTR_ID",
            )
            .from(`${this.tableName} as PP`)
            .where("PP.PLN_STATUS", "<>", "D");

        return globalThis.orm.collection("PlanProcess", conn);
    }

    getPlanUsers(planId) {
        return this.connection
            .select([
                "U.USER_ID",
                "U.USER_NAME",
                "OS.ORGSTR_NAME",
                "OS.ORGSTR_ID",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
            ])
            .from("PLAN_USERS as PU")
            .leftJoin("USERS as U", "PU.USER_ID", "U.USER_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as OS",
                "PU.ORGSTR_ID",
                "OS.ORGSTR_ID",
            )
            .where("PU.PLN_ID", planId);
    }

    async store(entity, plan_user_list) {
        if (typeof plan_user_list !== "undefined") {
            entity.PLN_USERS_LIST_FLAG =
                Array.isArray(plan_user_list) && plan_user_list.length > 0
                    ? "Y"
                    : "N";
        }

        const plnId = await super.store(entity);

        if (typeof plan_user_list !== "undefined") {
            const plUserRepo = globalThis.orm.repo("planUser", this.connection);
            await plUserRepo.connection
                .select()
                .from(plUserRepo.tableName)
                .where("PLN_ID", plnId)
                .delete();

            if (Array.isArray(plan_user_list) && plan_user_list.length > 0) {
                for (const user of plan_user_list) {
                    const entityToStore = plUserRepo.getEntity({
                        PLN_ID: plnId,
                        USER_ID: user.user_id,
                        ORGSTR_ID: user.orgstr_id,
                    });
                    await plUserRepo.store(entityToStore);
                }
            }
        }

        return plnId;
    }

    delete(entity) {
        const plUserRepo: PlanUserRepository = globalThis.orm.repo(
            "planUser",
            this.connection,
        );
        return plUserRepo.connection
            .select()
            .from(plUserRepo.tableName)
            .where("PLN_ID", entity.id)
            .delete()
            .then(() => super.delete(entity));
    }

    async runPlan(planId, date) {
        const userRepo: UserRepository = globalThis.orm.repo("user");
        const plogRepo: PlanProcessLogRepository =
            globalThis.orm.repo("planProcessLog");
        const plan: PlanProcess = await this.get(planId);
        let iprocess;

        const usrs = [];
        if (
            plan.PLN_REPEAT_OFFSET_TYPE === PlanConstants.OFFSET_TYPE_NONE &&
            plan.PLN_RUN_COUNT > 0
        ) {
            return;
        }

        if (plan.PLN_ASSESMENT_HIERARCHY === "G") {
            usrs.push({
                USER_ID: plan.PLN_USER_ID,
                ORGSTR_ID: plan.PLN_ORGSTR_ID,
            });
        } else if (plan.PLN_USERS_LIST_FLAG === "Y") {
            usrs.push({
                USER_ID: plan.PLN_USER_ID,
                ORGSTR_ID: plan.PLN_ORGSTR_ID,
            });
        } else {
            // compute users based on hierarchy
            const usrsCom = await userRepo.getUsersWithOrgStr(
                plan.PLN_USER_ID,
                plan.PLN_ORGSTR_ID,
                {
                    ITASK_ASSESMENT_HIERARCHY: plan.PLN_ASSESMENT_HIERARCHY,
                    ITASK_ASSESMENT_ROLE_ID: plan.PLN_ASSESMENT_ROLE_ID,
                    ITASK_ASSESMENT_ORGSTR_CNST: plan.PLN_ASSESMENT_ORGSTR_CNST,
                },
            );

            for (const usr of usrsCom) {
                usrs.push({
                    USER_ID: usr.USER_ID,
                    ORGSTR_ID: usr.ORGSTR_ID,
                });
            }
        }

        for (const usr of usrs) {
            try {
                const cUser =
                    await globalThis.container.service.auth.getUserInfoFromId(
                        usr.USER_ID,
                        false,
                    );

                // only for active users can be runned plans
                // Start transaction
                await globalThis.database.transaction(async (trx) => {
                    // Create orm based on transaction
                    const orm = new OrmFactory(
                        trx,
                        globalThis.dynamicConfig.db.client,
                    );
                    // Retrieve header
                    await orm.repo("header").get(plan.HEADER_ID);

                    // Retrieve template process
                    const tproc = await orm
                        .repo("templateProcess")
                        .get([plan.TPROC_ID, 1]);

                    // Instantiate process
                    iprocess = await orm
                        .repo("process")
                        .copyInstanceFromTemplate(
                            usr.USER_ID,
                            [tproc.TPROC_ID, 1],
                            plan.HEADER_ID,
                            tproc.TPROC_DEFAULT_CASE_NAME,
                            null,
                            usr.ORGSTR_ID,
                            null,
                            "M",
                            null,
                            false, // @t3b-1251 Admin pravidla a problém s jejich odebíráním
                        );

                    const wf = new Workflow(orm, cUser);
                    await globalThis.tasLogger.runTask(async () => {
                        globalThis.tasLogger.setContextProperty(
                            "category",
                            LogCategory.CATEGORY_WORKFLOW,
                        );
                        await wf.start(iprocess);
                    });
                });

                plan.PLN_STATUS = "R";
                plan.PLN_LAST_RUN = date ? new Date(date) : new Date();
                plan.PLN_RUN_COUNT += 1;
                await this.store(plan);

                const plog = plogRepo.getEntity({
                    PLN_ID: planId,
                    IPROC_ID: iprocess.IPROC_ID,
                    PLNPRC_DATETIME: new Date(),
                    PLNPRC_STATUS: PlanConstants.LOG_OK, // 0
                    PLNPRC_USER_ID: cUser.USER_ID,
                });

                await plogRepo.store(plog);
            } catch (err) {
                globalThis.tasLogger.error(`Error on plan ${err.message}`, {
                    user_id: usr.USER_ID,
                });

                if (!(err instanceof AuthException)) {
                    throw err; // Re-throw non-authentication related errors
                }
            }
        }
    }

    finishPlans() {
        return this.connection("PLAN_PROCESS")
            .where(function () {
                this.whereNotNull("PLN_REPEAT_UNTIL_DATE").andWhere(
                    function () {
                        this.whereRaw(
                            `"PLN_REPEAT_UNTIL_DATE" < ${globalThis.orm.db.sysDate()}`,
                        );
                    },
                );
            })
            .orWhere(function () {
                this.whereNotNull("PLN_REPEAT_COUNT").andWhere(
                    "PLN_REPEAT_COUNT",
                    "=",
                    globalThis.database.raw(`"PLN_RUN_COUNT"`),
                );
            })
            .update({ PLN_STATUS: PlanConstants.STATUS_FINISHED });
    }

    getNextRun(plan) {
        const du = new DateBusinessUtils(
            globalThis.dynamicConfig.tas.calculations,
        );
        const nextRunMoments = [];
        const futureDateDT = new Date("3000-01-01");
        const status = plan.PLN_STATUS;
        const offs = plan.PLN_REPEAT_OFFSET
            ? plan.PLN_REPEAT_OFFSET.split("|")
            : [];
        type OffArr = Record<string, string[]>;
        const offArr: OffArr = offs
            .map((opts) => opts.replace(/[\["\]]+/g, "").replace(/","/g, ","))
            .flatMap((cleanedOpts) => cleanedOpts.split(","))
            .reduce((acc: OffArr, entry: string) => {
                const [key, value] = entry.split("#");

                if (key) {
                    if (!acc[key]) {
                        acc[key] = [];
                    }
                    acc[key].push(value || key);
                }

                return acc;
            }, {});

        const repeatOffset = plan.PLN_REPEAT_OFFSET;
        const startDateDT: Date = new Date(plan.PLN_START_DATETIME);
        const correctInput =
            plan.PLN_LAST_RUN ||
            du.substractBussinesDays(plan.PLN_START_DATETIME, 1);
        const lastRunDT: Date = new Date(correctInput);
        // Corrected datetime derived from startDate. Fixing rolling startTime.
        // Working for basic repetition now.
        const correctLastRunDT: Date = new Date(lastRunDT);

        let nextRunDT: Date = futureDateDT;

        moment.updateLocale("en", {
            weekdaysShort: ["SON", "MON", "TUE", "WED", "THU", "FRI", "SAT"],
        });

        moment.updateLocale("en", {
            workingWeekdays: [1, 2, 3, 4, 5],
        });

        const suffixMap = {
            S: (date, interval) => moment(date).add(interval, "seconds"),
            MM: (date, interval) => moment(date).add(interval, "minutes"),
            H: (date, interval) => moment(date).add(interval, "hours"),
            D: (date, interval) => moment(date).add(interval, "days"),
            W: (date, interval) => moment(date).add(interval * 7, "days"),
            M: (date, interval) => moment(date).add(interval, "months"),
            Y: (date, interval) => moment(date).add(interval, "years"),
        };

        // Handle exceptions in `PLN_REPEAT_EXCEPTIONS`
        const exceptions = JSON.parse(plan.PLN_REPEAT_EXCEPTIONS);
        const isDateInExceptions = (date) => {
            if (!exceptions) {
                return false;
            }
            const formattedDate = moment(date).format("YYYY-MM-DD");

            if (Array.isArray(exceptions)) {
                return exceptions.some(({ isoDate, date: exceptionDate }) => {
                    if (isoDate && isoDate === formattedDate) {
                        return true;
                    }

                    if (
                        exceptionDate &&
                        moment(exceptionDate).format("YYYY-MM-DD") ===
                            formattedDate
                    ) {
                        return true;
                    }
                    return false;
                });
            }
            if (
                typeof exceptions === "object" &&
                exceptions.from &&
                exceptions.to
            ) {
                const fromDate = moment(exceptions.from).format("YYYY-MM-DD");
                const toDate = moment(exceptions.to).format("YYYY-MM-DD");
                return formattedDate >= fromDate && formattedDate <= toDate;
            }
            return false;
        };

        if (plan.PLN_REPEAT_OFFSET_SUBTYPE !== "F") {
            switch (plan.PLN_REPEAT_OFFSET_TYPE) {
                case "N": // never
                    nextRunDT = status === "A" ? startDateDT : futureDateDT;
                    break;
                case "D": // daily
                    for (let i: number = 0; i < offArr.H.length; i++) {
                        let hEntry = offArr.H[i];
                        hEntry = moment(hEntry.split("#")[0]).format(
                            "HH:mm:ss",
                        );
                        const [hour, minute] = hEntry.split(":").map(Number);

                        const dateOption = new Date(correctLastRunDT);
                        dateOption.setHours(hour, minute, 0, 0);

                        if (offArr.WD) {
                            while (
                                !momentBt(dateOption).isWorkingDay() ||
                                dateOption <= correctLastRunDT
                            ) {
                                dateOption.setDate(dateOption.getDate() + 1);
                            }
                        } else if (dateOption <= correctLastRunDT) {
                            dateOption.setDate(dateOption.getDate() + 1);
                        }

                        if (!isDateInExceptions(dateOption)) {
                            nextRunDT =
                                !nextRunDT || dateOption < nextRunDT
                                    ? dateOption
                                    : nextRunDT;
                        }
                    }

                    while (isDateInExceptions(nextRunDT)) {
                        nextRunDT = offArr.WD
                            ? du.addBussinesDays(nextRunDT, 1)
                            : du.addDays(nextRunDT, 1);
                    }
                    break;
                case "W": // weekly
                    if (status === "A") {
                        nextRunDT = startDateDT;
                    }

                    if (offArr.DAYS && offArr.H) {
                        const days = offArr.DAYS;
                        const times = offArr.H;
                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < days.length; i++) {
                            const targetDay = days[i];
                            let targetTime = times[i] || "00:00";
                            targetTime = moment(
                                targetTime.split("#")[0],
                            ).format("HH:mm:ss");

                            const [hour, minute] = targetTime
                                .split(":")
                                .map(Number);

                            const dayIndex = moment()
                                .isoWeekday(targetDay.toUpperCase())
                                .isoWeekday();

                            const potentialNextRun = moment(correctLastRunDT)
                                .isoWeekday(dayIndex)
                                .set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });

                            if (
                                !potentialNextRun.isAfter(
                                    moment(correctLastRunDT),
                                )
                            ) {
                                potentialNextRun.add(1, "week");
                            }

                            while (
                                isDateInExceptions(
                                    potentialNextRun.toDate(),
                                    exceptions,
                                )
                            ) {
                                potentialNextRun
                                    .add(1, "week")
                                    .isoWeekday(dayIndex);
                            }

                            if (
                                !closestNextRun ||
                                potentialNextRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialNextRun.clone();
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    }
                    break;
                case "M": // monthly
                    if (offArr.XD && offArr.DAYS) {
                        const occurrences = offArr.XD;
                        const days = offArr.DAYS;
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < occurrences.length; i++) {
                            const nthWeek = parseInt(occurrences[i], 10);
                            const dayOfWeek = days[i];
                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            let potentialRun =
                                moment(correctLastRunDT).startOf("month");

                            //Find n occurrance of the day in actual month
                            let counter = 0;
                            while (
                                potentialRun.month() ===
                                moment(correctLastRunDT).month()
                            ) {
                                if (
                                    potentialRun.format("ddd").toUpperCase() ===
                                    dayOfWeek
                                ) {
                                    counter++;
                                    if (counter === nthWeek) {
                                        break;
                                    }
                                }
                                potentialRun.add(1, "day");
                            }

                            potentialRun.set({
                                hour,
                                minute,
                                second: 0,
                                millisecond: 0,
                            });

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = moment(correctLastRunDT)
                                    .add(1, "month")
                                    .startOf("month");
                                counter = 0;
                                while (
                                    potentialRun.month() ===
                                    moment(correctLastRunDT)
                                        .add(1, "month")
                                        .month()
                                ) {
                                    if (
                                        potentialRun
                                            .format("ddd")
                                            .toUpperCase() === dayOfWeek
                                    ) {
                                        counter++;
                                        if (counter === nthWeek) {
                                            break;
                                        }
                                    }
                                    potentialRun.add(1, "day");
                                }
                                potentialRun.set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });
                            }
                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .startOf("month");
                                counter = 0;
                                while (
                                    potentialRun.month() ===
                                    moment(potentialRun).month()
                                ) {
                                    if (
                                        potentialRun
                                            .format("ddd")
                                            .toUpperCase() === dayOfWeek
                                    ) {
                                        counter++;
                                        if (counter === nthWeek) {
                                            break;
                                        }
                                    }
                                    potentialRun.add(1, "day");
                                }
                                potentialRun.set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    } else if (offArr.D && offArr.H) {
                        const days = offArr.D;
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < days.length; i++) {
                            const dayOfMonth = parseInt(days[i], 10);
                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            //potential day in actual month
                            let potentialRun = moment(correctLastRunDT).set({
                                date: dayOfMonth,
                                hour,
                                minute,
                                second: 0,
                                millisecond: 0,
                            });

                            //time is in past move to next month
                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .set({
                                        date: dayOfMonth,
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            while (
                                isDateInExceptions(
                                    potentialRun.toDate(),
                                    exceptions,
                                )
                            ) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .set({
                                        date: dayOfMonth,
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }
                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    } else if (offArr.FD) {
                        const days = offArr.FD;
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < days.length; i++) {
                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            let potentialRun = moment(correctLastRunDT)
                                .startOf("month")
                                .set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .startOf("month")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .startOf("month")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    } else if (offArr.LD) {
                        const days = offArr.LD;
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < days.length; i++) {
                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            let potentialRun = moment(correctLastRunDT)
                                .endOf("month")
                                .set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .endOf("month")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun = potentialRun
                                    .add(1, "month")
                                    .endOf("month")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    }
                    break;
                case "Y":
                    if (offArr.XD && offArr.DAYS && offArr.M) {
                        const occurrences = offArr.XD;
                        const days = offArr.DAYS;
                        const months = offArr.M;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < occurrences.length; i++) {
                            const nthWeek = parseInt(occurrences[i], 10);
                            const dayOfWeek = days[i];
                            const month = parseInt(months[i], 10);

                            let potentialRun = moment(correctLastRunDT)
                                .year(moment(correctLastRunDT).year())
                                .month(month - 1)
                                .startOf("month");

                            //Move to nth occurance in month
                            let counter = 0;
                            let validRunFound = false;
                            let lastValidRun = null;
                            while (potentialRun.month() === month - 1) {
                                if (
                                    potentialRun.format("ddd").toUpperCase() ===
                                    dayOfWeek
                                ) {
                                    counter++;
                                    lastValidRun = potentialRun.clone();
                                    if (counter === nthWeek) {
                                        validRunFound = true;
                                        break;
                                    }
                                }
                                potentialRun.add(1, "day");
                            }

                            if (!validRunFound && lastValidRun) {
                                potentialRun = lastValidRun;
                            }

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = moment(correctLastRunDT)
                                    .add(1, "year")
                                    .month(month - 1)
                                    .startOf("month");
                                counter = 0;
                                lastValidRun = null;
                                while (potentialRun.month() === month - 1) {
                                    if (
                                        potentialRun
                                            .format("ddd")
                                            .toUpperCase() === dayOfWeek
                                    ) {
                                        counter++;
                                        lastValidRun = potentialRun.clone();
                                        if (counter === nthWeek) {
                                            validRunFound = true;
                                            break;
                                        }
                                    }
                                    potentialRun.add(1, "day");
                                }
                                if (!validRunFound && lastValidRun) {
                                    potentialRun = lastValidRun;
                                }
                            }
                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun = potentialRun
                                    .add(1, "year")
                                    .month(month - 1)
                                    .startOf("month");
                                counter = 0;
                                lastValidRun = null;
                                while (potentialRun.month() === month - 1) {
                                    if (
                                        potentialRun
                                            .format("ddd")
                                            .toUpperCase() === dayOfWeek
                                    ) {
                                        counter++;
                                        lastValidRun = potentialRun.clone();
                                        if (counter === nthWeek) {
                                            validRunFound = true;
                                            break;
                                        }
                                    }
                                    potentialRun.add(1, "day");
                                }
                                if (!validRunFound && lastValidRun) {
                                    potentialRun = lastValidRun;
                                }
                            }

                            potentialRun.set({
                                hour: moment(correctLastRunDT).hour(),
                                minute: moment(correctLastRunDT).minute(),
                                second: moment(correctLastRunDT).second(),
                                millisecond:
                                    moment(correctLastRunDT).millisecond(),
                            });

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    }
                    if (offArr.DT) {
                        const dates = offArr.DT;
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < dates.length; i++) {
                            const specificDate = moment(dates[i]);

                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            const potentialRun = specificDate.clone().set({
                                hour,
                                minute,
                                second: 0,
                                millisecond: 0,
                            });

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun.add(1, "year");
                            }

                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun.add(1, "year");
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    }
                    if (offArr.FD) {
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < times.length; i++) {
                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            let potentialRun = moment(correctLastRunDT)
                                .startOf("year")
                                .set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = moment(correctLastRunDT)
                                    .add(1, "year")
                                    .startOf("year")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun = potentialRun
                                    .add(1, "year")
                                    .startOf("year")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    }
                    if (offArr.LD) {
                        const times = offArr.H;

                        let closestNextRun: moment.Moment | null = null;

                        for (let i = 0; i < times.length; i++) {
                            let time = times[i] || "00:00";
                            time = moment(time.split("#")[0]).format(
                                "HH:mm:ss",
                            );

                            const [hour, minute] = time.split(":").map(Number);

                            let potentialRun = moment(correctLastRunDT)
                                .endOf("year")
                                .set({
                                    hour,
                                    minute,
                                    second: 0,
                                    millisecond: 0,
                                });

                            if (
                                !potentialRun.isAfter(moment(correctLastRunDT))
                            ) {
                                potentialRun = moment(correctLastRunDT)
                                    .add(1, "year")
                                    .endOf("year")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            while (isDateInExceptions(potentialRun.toDate())) {
                                potentialRun = potentialRun
                                    .add(1, "year")
                                    .endOf("year")
                                    .set({
                                        hour,
                                        minute,
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }

                            if (
                                !closestNextRun ||
                                potentialRun.isBefore(closestNextRun)
                            ) {
                                closestNextRun = potentialRun;
                            }
                        }

                        if (closestNextRun) {
                            nextRunDT = closestNextRun.toDate();
                        }
                    }
                    break;
                case "T":
                    const isWorkingDay = (date) =>
                        momentBt(date).isBusinessDay();

                    let closestNextRun: moment.Moment | null = null;

                    const offset = repeatOffset.split(",");

                    for (let i = 0; i < offset.length; i++) {
                        const [dayType, freqPart] = offset[i].split("|");
                        const [unit, interval] = freqPart.split("#");
                        const frequency = parseInt(interval, 10) || 1;

                        const addFunction = suffixMap[unit];
                        if (!addFunction) {
                            throw new Error(`Invalid time unit: ${unit}`);
                        }

                        let potentialRun = addFunction(
                            correctLastRunDT,
                            frequency,
                        );

                        if (dayType === "WD") {
                            while (!isWorkingDay(potentialRun)) {
                                potentialRun.add(1, "day");
                            }
                        }

                        while (isDateInExceptions(potentialRun.toDate())) {
                            if (dayType === "WD") {
                                potentialRun.add(1, "day");
                                while (!isWorkingDay(potentialRun)) {
                                    potentialRun.add(1, "day");
                                }
                            } else {
                                potentialRun = addFunction(
                                    potentialRun,
                                    frequency,
                                );
                            }
                        }

                        if (
                            !closestNextRun ||
                            potentialRun.isBefore(closestNextRun)
                        ) {
                            closestNextRun = potentialRun;
                        }
                    }

                    if (closestNextRun) {
                        nextRunDT = closestNextRun.toDate();
                    }
                    break;
                default:
                    break;
            }
        }

        if (plan.PLN_REPEAT_OFFSET_SUBTYPE === "F") {
            switch (plan.PLN_REPEAT_OFFSET_TYPE) {
                case "N": // never
                    nextRunDT = status === "A" ? startDateDT : futureDateDT;
                    break;
                case "D":
                    nextRunDT =
                        status === "A"
                            ? startDateDT
                            : new Date(correctLastRunDT);

                    const isWorkingDay = (date) =>
                        momentBt(date).isBusinessDay();

                    const cleanedD = repeatOffset
                        .trim()
                        .replace(/^\[|\]$/g, "")
                        .replace(/\\"/g, '"');

                    const offsets = cleanedD
                        .split('","')
                        .map((pattern) => pattern.replace(/^"|"$/g, ""));

                    offsets.forEach((offset) => {
                        const [dayType, startPart, endPart, freqUnit] =
                            offset.split("|");
                        const startTime = moment(
                            startPart.split("#")[1],
                        ).format("HH:mm:ss");
                        const endTime = moment(endPart.split("#")[1]).format(
                            "HH:mm:ss",
                        );
                        const frequency = 1;
                        const addFunction = suffixMap[freqUnit];
                        if (!addFunction) {
                            throw new Error(`Invalid time unit: ${freqUnit}`);
                        }

                        let nextRunMoment = addFunction(
                            moment(correctLastRunDT),
                            frequency,
                        );

                        if (
                            !nextRunMoment.isBetween(
                                moment(nextRunMoment).set({
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: 0,
                                    millisecond: 0,
                                }),
                                moment(nextRunMoment).set({
                                    hour: parseInt(endTime.split(":")[0], 10),
                                    minute: parseInt(endTime.split(":")[1], 10),
                                    second: 0,
                                    millisecond: 0,
                                }),
                                null,
                                freqUnit === "H" ? "[]" : "[)",
                            )
                        ) {
                            if (
                                nextRunMoment.isAfter(
                                    moment(nextRunMoment).set({
                                        hour: parseInt(
                                            endTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            endTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    }),
                                )
                            ) {
                                nextRunMoment = moment(nextRunMoment)
                                    .add(1, "day")
                                    .set({
                                        hour: parseInt(
                                            startTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            startTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    });
                            } else {
                                nextRunMoment = moment(nextRunMoment).set({
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: 0,
                                    millisecond: 0,
                                });
                            }
                        }

                        if (dayType === "WD" && !isWorkingDay(nextRunMoment)) {
                            while (!isWorkingDay(nextRunMoment)) {
                                nextRunMoment.add(1, "day");
                            }
                        }

                        while (
                            nextRunMoment.isBefore(moment(correctLastRunDT)) ||
                            isDateInExceptions(nextRunMoment.toDate())
                        ) {
                            nextRunMoment = addFunction(
                                nextRunMoment,
                                frequency,
                            );

                            if (
                                dayType === "WD" &&
                                !isWorkingDay(nextRunMoment)
                            ) {
                                while (!isWorkingDay(nextRunMoment)) {
                                    nextRunMoment.add(1, "day");
                                }
                            }

                            if (
                                nextRunMoment.isAfter(
                                    moment(nextRunMoment).set({
                                        hour: parseInt(
                                            endTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            endTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    }),
                                )
                            ) {
                                nextRunMoment = moment(nextRunMoment)
                                    .add(1, "day")
                                    .set({
                                        hour: parseInt(
                                            startTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            startTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    });
                            }
                        }

                        nextRunMoments.push(nextRunMoment.toDate());
                    });

                    nextRunDT = nextRunMoments.sort((a, b) => a - b)[0];
                    break;
                case "W":
                    nextRunDT =
                        status === "A"
                            ? startDateDT
                            : new Date(correctLastRunDT);

                    const cleaned = repeatOffset
                        .trim()
                        .replace(/^\[|\]$/g, "")
                        .replace(/\\"/g, '"');

                    const weeklyOffsets = cleaned
                        .split('","')
                        .map((pattern) => pattern.replace(/^"|"$/g, ""));

                    weeklyOffsets.forEach((offset) => {
                        const [daysPart, startPart, endPart, freqPart] =
                            offset.split("|");
                        const targetDay = daysPart.split("#")[1];
                        const startTime = moment(
                            startPart.split("#")[1],
                        ).format("HH:mm:ss");
                        const endTime = moment(endPart.split("#")[1]).format(
                            "HH:mm:ss",
                        );
                        const freqUnit = freqPart.split("#")[0];
                        const frequency = 1;
                        const addFunction = suffixMap[freqUnit];

                        if (!addFunction) {
                            throw new Error(`Invalid time unit: ${freqUnit}`);
                        }

                        let nextRunMoment =
                            moment(correctLastRunDT).isoWeekday(targetDay);

                        if (
                            nextRunMoment.isSame(
                                moment(correctLastRunDT),
                                "day",
                            )
                        ) {
                            nextRunMoment = moment(correctLastRunDT);
                        }

                        nextRunMoment.set({
                            hour: parseInt(startTime.split(":")[0], 10),
                            minute: parseInt(startTime.split(":")[1], 10),
                            second: 0,
                            millisecond: 0,
                        });

                        while (
                            nextRunMoment.isSameOrBefore(
                                moment(nextRunMoment).set({
                                    hour: parseInt(endTime.split(":")[0], 10),
                                    minute: parseInt(endTime.split(":")[1], 10),
                                    second: 0,
                                    millisecond: 0,
                                }),
                            )
                        ) {
                            if (
                                nextRunMoment.isAfter(
                                    moment(correctLastRunDT),
                                ) &&
                                !isDateInExceptions(
                                    nextRunMoment.toDate(),
                                    exceptions,
                                )
                            ) {
                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                            nextRunMoment = addFunction(
                                nextRunMoment,
                                frequency,
                            );
                        }

                        if (nextRunMoments.length === 0) {
                            nextRunMoment = moment(correctLastRunDT)
                                .isoWeekday(targetDay)
                                .add(1, "week")
                                .set({
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: 0,
                                    millisecond: 0,
                                });

                            while (
                                isDateInExceptions(
                                    nextRunMoment.toDate(),
                                    exceptions,
                                )
                            ) {
                                nextRunMoment
                                    .add(1, "week")
                                    .isoWeekday(targetDay);
                            }

                            while (
                                nextRunMoment.isSameOrBefore(
                                    moment(nextRunMoment).set({
                                        hour: parseInt(
                                            endTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            endTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    }),
                                )
                            ) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }
                        }
                    });

                    nextRunDT = nextRunMoments.sort((a, b) => a - b)[0];
                    break;
                case "M":
                    if (offArr.XD && offArr.DAYS) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedXD = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const xdOffsets = cleanedXD
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        xdOffsets.forEach((offset) => {
                            const [
                                xdPart,
                                daysPart,
                                startPart,
                                endPart,
                                freqPart,
                            ] = offset.split("|");
                            const nthWeek = parseInt(xdPart.split("#")[1], 10);
                            const targetDay = daysPart.split("#")[1];
                            const startTime = moment(
                                startPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const endTime = moment(
                                endPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const freqUnit = freqPart;
                            const frequency = 1;
                            const addFunction = suffixMap[freqUnit];
                            const startInterval = moment(
                                startPart.split("#")[1],
                            );

                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            const currentMonth = moment(nextRunDT);

                            let targetDate: moment.Moment | null = null;
                            let found = false;

                            const lastRunDay = moment(correctLastRunDT)
                                .format("ddd")
                                .toUpperCase();
                            const lastRunWeek = Math.ceil(
                                moment(correctLastRunDT).date() / 7,
                            );
                            const isLastRunMatchingPattern =
                                lastRunDay === targetDay &&
                                lastRunWeek === nthWeek;

                            if (isLastRunMatchingPattern) {
                                if (
                                    isDateInExceptions(
                                        correctLastRunDT,
                                        exceptions,
                                    )
                                ) {
                                    while (!found) {
                                        const checkDate = currentMonth
                                            .clone()
                                            .startOf("month");
                                        let weekCounter = 0;

                                        while (
                                            checkDate.month() ===
                                            currentMonth.month()
                                        ) {
                                            if (
                                                checkDate
                                                    .format("ddd")
                                                    .toUpperCase() === targetDay
                                            ) {
                                                weekCounter++;
                                                if (weekCounter === nthWeek) {
                                                    targetDate =
                                                        checkDate.clone();

                                                    if (
                                                        !targetDate.isBefore(
                                                            moment(
                                                                correctLastRunDT,
                                                            ),
                                                        ) &&
                                                        !isDateInExceptions(
                                                            targetDate.toDate(),
                                                            exceptions,
                                                        )
                                                    ) {
                                                        found = true;
                                                    }
                                                    break;
                                                }
                                            }
                                            checkDate.add(1, "day");
                                        }

                                        if (!found) {
                                            currentMonth.add(1, "month");
                                        }
                                    }
                                } else {
                                    // If not in exceptions, use lastRun as starting point
                                    targetDate = moment(correctLastRunDT);
                                    found = true;
                                }
                            } else {
                                // Find next occurrence if lastRun doesn't match pattern
                                while (!found) {
                                    const checkDate = currentMonth
                                        .clone()
                                        .startOf("month");
                                    let weekCounter = 0;

                                    while (
                                        checkDate.month() ===
                                        currentMonth.month()
                                    ) {
                                        if (
                                            checkDate
                                                .format("ddd")
                                                .toUpperCase() === targetDay
                                        ) {
                                            weekCounter++;
                                            if (weekCounter === nthWeek) {
                                                targetDate = checkDate.clone();

                                                if (
                                                    !targetDate.isBefore(
                                                        moment(
                                                            correctLastRunDT,
                                                        ),
                                                    ) &&
                                                    !isDateInExceptions(
                                                        targetDate.toDate(),
                                                        exceptions,
                                                    )
                                                ) {
                                                    found = true;
                                                }
                                                break;
                                            }
                                        }
                                        checkDate.add(1, "day");
                                    }

                                    if (!found) {
                                        currentMonth.add(1, "month");
                                    }
                                }
                            }
                            if (
                                moment(correctLastRunDT).isBefore(startInterval)
                            ) {
                                targetDate.set({
                                    hour: startInterval.hours(),
                                    minute: startInterval.minutes(),
                                    second: 0,
                                    millisecond: 0,
                                });
                            }
                            if (targetDate) {
                                let nextRunMoment = targetDate.clone();
                                // If starting from lastRun, use its time, otherwise use startTime
                                if (
                                    isLastRunMatchingPattern &&
                                    !isDateInExceptions(
                                        correctLastRunDT,
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment = moment(correctLastRunDT);
                                    if (
                                        moment(correctLastRunDT).isBefore(
                                            startInterval,
                                        )
                                    ) {
                                        nextRunMoment.set({
                                            hour: startInterval.hours(),
                                            minute: startInterval.minutes(),
                                            second: 0,
                                            millisecond: 0,
                                        });
                                    }
                                    // Add frequency immediately to get next occurrence
                                    nextRunMoment = addFunction(
                                        nextRunMoment,
                                        frequency,
                                    );
                                } else {
                                    nextRunMoment.set({
                                        hour: parseInt(
                                            startTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            startTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    });
                                }

                                const endMoment = moment(nextRunMoment).set({
                                    hour: parseInt(endTime.split(":")[0], 10),
                                    minute: parseInt(endTime.split(":")[1], 10),
                                    second: 0,
                                    millisecond: 0,
                                });

                                while (
                                    nextRunMoment.isSameOrBefore(endMoment)
                                ) {
                                    if (
                                        !isDateInExceptions(
                                            nextRunMoment.toDate(),
                                            exceptions,
                                        )
                                    ) {
                                        nextRunMoments.push(
                                            nextRunMoment.toDate(),
                                        );
                                    }
                                    nextRunMoment = addFunction(
                                        nextRunMoment.clone(),
                                        frequency,
                                    );
                                }
                                if (nextRunMoment.isAfter(endMoment)) {
                                    const nextMonth = moment(nextRunMoment)
                                        .add(1, "month")
                                        .startOf("month");
                                    let foundNext = false;

                                    while (!foundNext) {
                                        const checkDate = nextMonth.clone();
                                        let weekCounter = 0;

                                        while (
                                            checkDate.month() ===
                                            nextMonth.month()
                                        ) {
                                            if (
                                                checkDate
                                                    .format("ddd")
                                                    .toUpperCase() === targetDay
                                            ) {
                                                weekCounter++;
                                                if (weekCounter === nthWeek) {
                                                    nextRunMoment =
                                                        checkDate.clone();
                                                    nextRunMoment.set({
                                                        hour: parseInt(
                                                            startTime.split(
                                                                ":",
                                                            )[0],
                                                            10,
                                                        ),
                                                        minute: parseInt(
                                                            startTime.split(
                                                                ":",
                                                            )[1],
                                                            10,
                                                        ),
                                                        second: 0,
                                                        millisecond: 0,
                                                    });

                                                    if (
                                                        !isDateInExceptions(
                                                            nextRunMoment.toDate(),
                                                            exceptions,
                                                        )
                                                    ) {
                                                        foundNext = true;
                                                    }
                                                    break;
                                                }
                                            }
                                            checkDate.add(1, "day");
                                        }
                                        if (!foundNext) {
                                            nextMonth.add(1, "month");
                                        }
                                    }
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                            }
                        });

                        nextRunDT = nextRunMoments.sort(
                            (a, b) => a.getTime() - b.getTime(),
                        )[0];
                    } else if (offArr.D) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleaned = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const monthlyOffsetDays = cleaned
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        monthlyOffsetDays.forEach((offset) => {
                            const [daysPart, startPart, endPart, freqPart] =
                                offset.split("|");

                            const targetDay = parseInt(
                                daysPart.split("#")[1],
                                10,
                            );

                            const startTime = moment(
                                startPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const endTime = moment(
                                endPart.split("#")[1],
                            ).format("HH:mm:ss");

                            const freqUnit = freqPart;
                            const frequency = 1;

                            const addFunction = suffixMap[freqUnit];
                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(correctLastRunDT);
                            const currentMonth = nextRunMoment.month();
                            const currentYear = nextRunMoment.year();
                            const currentDay = nextRunMoment.date();

                            const startTimeOnly = moment(correctLastRunDT).set({
                                hour: parseInt(startTime.split(":")[0], 10),
                                minute: parseInt(startTime.split(":")[1], 10),
                                second: parseInt(startTime.split(":")[2], 10),
                                millisecond: 0,
                            });

                            const endTimeOnly = moment(correctLastRunDT).set({
                                hour: parseInt(endTime.split(":")[0], 10),
                                minute: parseInt(endTime.split(":")[1], 10),
                                second: parseInt(endTime.split(":")[2], 10),
                                millisecond: 0,
                            });

                            if (currentDay === targetDay) {
                                if (nextRunMoment.isBefore(startTimeOnly)) {
                                    nextRunMoment.set({
                                        hour: parseInt(
                                            startTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            startTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    });
                                }

                                if (
                                    nextRunMoment.isBetween(
                                        startTimeOnly,
                                        endTimeOnly,
                                        null,
                                        "[)",
                                    )
                                ) {
                                    while (
                                        nextRunMoment.isSameOrBefore(
                                            endTimeOnly,
                                        )
                                    ) {
                                        if (
                                            !isDateInExceptions(
                                                nextRunMoment.toDate(),
                                                exceptions,
                                            )
                                        ) {
                                            nextRunMoments.push(
                                                nextRunMoment.toDate(),
                                            );
                                        }
                                        nextRunMoment = addFunction(
                                            nextRunMoment.clone(),
                                            frequency,
                                        );
                                    }
                                }

                                nextRunMoment.add(1, "month").startOf("month");

                                while (
                                    nextRunMoment.daysInMonth() < targetDay
                                ) {
                                    nextRunMoment.add(1, "month");
                                }

                                nextRunMoment.set({
                                    date: targetDay,
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: 0,
                                    millisecond: 0,
                                });

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "month")
                                        .date(targetDay);
                                }

                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                            } else {
                                const targetDate = moment([
                                    currentYear,
                                    currentMonth,
                                    targetDay,
                                ])
                                    .hours(
                                        parseInt(startTime.split(":")[0], 10),
                                    )
                                    .minutes(
                                        parseInt(startTime.split(":")[1], 10),
                                    )
                                    .seconds(0);

                                if (targetDate.isAfter(nextRunMoment)) {
                                    if (
                                        !isDateInExceptions(
                                            targetDate.toDate(),
                                            exceptions,
                                        )
                                    ) {
                                        nextRunMoments.push(
                                            targetDate.toDate(),
                                        );
                                    }
                                } else {
                                    nextRunMoment
                                        .add(1, "month")
                                        .startOf("month");

                                    while (
                                        nextRunMoment.daysInMonth() < targetDay
                                    ) {
                                        nextRunMoment.add(1, "month");
                                    }

                                    nextRunMoment.set({
                                        date: targetDay,
                                        hour: parseInt(
                                            startTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            startTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    });

                                    while (
                                        isDateInExceptions(
                                            nextRunMoment.toDate(),
                                            exceptions,
                                        )
                                    ) {
                                        nextRunMoment
                                            .add(1, "month")
                                            .date(targetDay);
                                    }

                                    if (
                                        !isDateInExceptions(
                                            nextRunMoment.toDate(),
                                            exceptions,
                                        )
                                    ) {
                                        nextRunMoments.push(
                                            nextRunMoment.toDate(),
                                        );
                                    }
                                }
                            }
                        });

                        if (nextRunMoments.length > 0) {
                            nextRunDT = nextRunMoments.sort(
                                (a, b) => a.getTime() - b.getTime(),
                            )[0];
                        }
                    } else if (offArr.LD) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedLD = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const ldOffsets = cleanedLD
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        ldOffsets.forEach((offset) => {
                            const parts = offset.split("|");
                            const startTime = moment(
                                parts[1].split("#")[1],
                            ).format("HH:mm:ss");
                            const endTime = moment(
                                parts[2].split("#")[1],
                            ).format("HH:mm:ss");
                            const freqUnit = parts[3].split("#")[0];
                            const frequency = 1;
                            const addFunction = suffixMap[freqUnit];

                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(correctLastRunDT);
                            const lastDayOfMonth = nextRunMoment
                                .clone()
                                .endOf("month");

                            if (
                                correctLastRunDT.getDate() ===
                                    lastDayOfMonth.date() &&
                                moment(correctLastRunDT).format("HH:mm:ss") >=
                                    startTime &&
                                moment(correctLastRunDT).format("HH:mm:ss") <=
                                    endTime
                            ) {
                                nextRunMoment = moment(correctLastRunDT);
                            } else {
                                nextRunMoment = lastDayOfMonth.clone().set({
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: 0,
                                    millisecond: 0,
                                });

                                if (
                                    nextRunMoment.isBefore(
                                        moment(correctLastRunDT),
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "month")
                                        .endOf("month")
                                        .set({
                                            hour: parseInt(
                                                startTime.split(":")[0],
                                                10,
                                            ),
                                            minute: parseInt(
                                                startTime.split(":")[1],
                                                10,
                                            ),
                                            second: 0,
                                            millisecond: 0,
                                        });
                                }
                            }

                            while (
                                isDateInExceptions(
                                    nextRunMoment.toDate(),
                                    exceptions,
                                )
                            ) {
                                nextRunMoment.add(1, "month").endOf("month");
                            }

                            while (
                                nextRunMoment.format("HH:mm:ss") <= endTime
                            ) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }
                            if (nextRunMoment.format("HH:mm:ss") >= endTime) {
                                nextRunMoment
                                    .add(1, "month")
                                    .endOf("month")
                                    .set({
                                        hour: parseInt(
                                            startTime.split(":")[0],
                                            10,
                                        ),
                                        minute: parseInt(
                                            startTime.split(":")[1],
                                            10,
                                        ),
                                        second: 0,
                                        millisecond: 0,
                                    });

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "month")
                                        .endOf("month")
                                        .set({
                                            hour: parseInt(
                                                startTime.split(":")[0],
                                                10,
                                            ),
                                            minute: parseInt(
                                                startTime.split(":")[1],
                                                10,
                                            ),
                                            second: 0,
                                            millisecond: 0,
                                        });
                                }

                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                        });

                        if (nextRunMoments.length > 0) {
                            nextRunDT = nextRunMoments.sort((a, b) => a - b)[0];
                        }
                    } else if (offArr.FD) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedFD = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const fdOffsets = cleanedFD
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        fdOffsets.forEach((offset) => {
                            const parts = offset.split("|");
                            const startTime = moment(parts[1].split("#")[1]);
                            const endTime = moment(parts[2].split("#")[1]);
                            const freqUnit = parts[3].split("#")[0];
                            const frequency = 1;
                            const addFunction = suffixMap[freqUnit];

                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(correctLastRunDT);

                            const startHour = startTime.get("hour");
                            const startMinute = startTime.get("minute");
                            const startSecond = startTime.get("second");

                            const endHour = endTime.get("hour");
                            const endMinute = endTime.get("minute");
                            const endSecond = endTime.get("second");

                            if (correctLastRunDT.getDate() === 1) {
                                if (
                                    nextRunMoment.isBetween(
                                        startTime,
                                        endTime,
                                        "seconds",
                                        "[)",
                                    )
                                ) {
                                    while (
                                        nextRunMoment.isSameOrBefore(endTime)
                                    ) {
                                        if (
                                            !isDateInExceptions(
                                                nextRunMoment.toDate(),
                                                exceptions,
                                            )
                                        ) {
                                            nextRunMoments.push(
                                                nextRunMoment.toDate(),
                                            );
                                        }
                                        nextRunMoment = addFunction(
                                            nextRunMoment,
                                            frequency,
                                        );
                                    }
                                } else {
                                    nextRunMoment.set({
                                        hour: startHour,
                                        minute: startMinute,
                                        second: startSecond,
                                        millisecond: 0,
                                    });
                                }
                            } else {
                                nextRunMoment.add(1, "month").startOf("month");

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "month")
                                        .startOf("month");
                                }

                                nextRunMoment.set({
                                    hour: startHour,
                                    minute: startMinute,
                                    second: startSecond,
                                    millisecond: 0,
                                });
                            }

                            while (
                                nextRunMoment.isSameOrBefore(
                                    moment(nextRunMoment).set({
                                        hour: endHour,
                                        minute: endMinute,
                                        second: endSecond,
                                        millisecond: 0,
                                    }),
                                )
                            ) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }

                            if (nextRunMoment.isAfter(endTime)) {
                                nextRunMoment.add(1, "month").startOf("month");

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "month")
                                        .startOf("month");
                                }

                                nextRunMoment.set({
                                    hour: startHour,
                                    minute: startMinute,
                                    second: startSecond,
                                    millisecond: 0,
                                });

                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                        });
                        nextRunDT = nextRunMoments.sort((a, b) => a - b)[0];
                    }
                    break;
                case "Y":
                    if (offArr.XD && offArr.DAYS && offArr.M) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedXDY = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const xdyOffsets = cleanedXDY
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        xdyOffsets.forEach((offset: string) => {
                            const [
                                nthPart,
                                daysPart,
                                monthPart,
                                startPart,
                                endPart,
                                freqPart,
                            ] = offset.split("|");

                            const nthWeek = parseInt(nthPart.split("#")[1], 10);
                            const targetDay = daysPart.split("#")[1];
                            const targetMonth =
                                parseInt(monthPart.split("#")[1], 10) - 1;
                            const startTime = moment(startPart.split("#")[1]);
                            const endTime = moment(endPart.split("#")[1]);
                            const freqUnit = freqPart;
                            const frequency = 1;

                            const addFunction =
                                suffixMap[freqUnit as keyof typeof suffixMap];
                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(correctLastRunDT)
                                .month(targetMonth)
                                .startOf("month");

                            let occurrence = 0;
                            while (nextRunMoment.month() === targetMonth) {
                                if (
                                    nextRunMoment
                                        .format("ddd")
                                        .toUpperCase() === targetDay
                                ) {
                                    occurrence++;
                                    if (occurrence === nthWeek) {
                                        break;
                                    }
                                }
                                nextRunMoment.add(1, "day");
                            }

                            if (moment(correctLastRunDT).isBefore(startTime)) {
                                nextRunMoment.set({
                                    hour: startTime.hours(),
                                    minute: startTime.minutes(),
                                    second: 0,
                                    millisecond: 0,
                                });
                            }

                            if (
                                nextRunMoment.isSame(
                                    moment(correctLastRunDT),
                                    "day",
                                ) &&
                                moment(correctLastRunDT).isBetween(
                                    startTime,
                                    endTime,
                                    null,
                                    "[]",
                                )
                            ) {
                                nextRunMoment = moment(correctLastRunDT);
                            } else {
                                if (
                                    nextRunMoment.isBefore(
                                        moment(correctLastRunDT),
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "year")
                                        .month(targetMonth)
                                        .startOf("month");
                                    occurrence = 0;
                                    while (
                                        nextRunMoment.month() === targetMonth
                                    ) {
                                        if (
                                            nextRunMoment
                                                .format("ddd")
                                                .toUpperCase() === targetDay
                                        ) {
                                            occurrence++;
                                            if (occurrence === nthWeek) {
                                                break;
                                            }
                                        }
                                        nextRunMoment.add(1, "day");
                                    }
                                }
                                nextRunMoment.set({
                                    hour: startTime.hours(),
                                    minute: startTime.minutes(),
                                    second: startTime.seconds(),
                                    millisecond: 0,
                                });
                            }

                            while (
                                isDateInExceptions(
                                    nextRunMoment.toDate(),
                                    exceptions,
                                )
                            ) {
                                nextRunMoment
                                    .add(1, "year")
                                    .month(targetMonth)
                                    .startOf("month");
                                occurrence = 0;
                                while (nextRunMoment.month() === targetMonth) {
                                    if (
                                        nextRunMoment
                                            .format("ddd")
                                            .toUpperCase() === targetDay
                                    ) {
                                        occurrence++;
                                        if (occurrence === nthWeek) {
                                            break;
                                        }
                                    }
                                    nextRunMoment.add(1, "day");
                                }
                            }

                            while (
                                nextRunMoment.isSameOrBefore(
                                    moment(nextRunMoment).set({
                                        hour: endTime.hours(),
                                        minute: endTime.minutes(),
                                        second: endTime.seconds(),
                                        millisecond: 0,
                                    }),
                                )
                            ) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment = addFunction(
                                        nextRunMoment,
                                        frequency,
                                    );
                                }
                                nextRunMoments.push(nextRunMoment.toDate());
                            }

                            if (nextRunMoment.isAfter(endTime)) {
                                nextRunMoment.add(1, "year").set({
                                    hour: startTime.hours(),
                                    minute: startTime.minutes(),
                                    second: 0,
                                    millisecond: 0,
                                });

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment.add(1, "year");
                                }
                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                        });

                        if (nextRunMoments.length > 0) {
                            nextRunDT = nextRunMoments.sort(
                                (a, b) => a.getTime() - b.getTime(),
                            )[0];
                        }
                    } else if (offArr.DT) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedDTY = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const dtyOffsets = cleanedDTY
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        dtyOffsets.forEach((offset: string) => {
                            const [_dtPart, startPart, endPart, freqPart] =
                                offset.split("|");
                            const targetDate = moment(startPart.split("#")[1]);
                            const startTime = moment(startPart.split("#")[1]);
                            const endTime = moment(endPart.split("#")[1]);
                            const freqUnit = freqPart;
                            const frequency = 1;

                            const addFunction =
                                suffixMap[freqUnit as keyof typeof suffixMap];
                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(targetDate);

                            if (
                                nextRunMoment.isBefore(
                                    moment(correctLastRunDT),
                                ) &&
                                !nextRunMoment.isSame(
                                    moment(correctLastRunDT),
                                    "day",
                                )
                            ) {
                                nextRunMoment.add(1, "year");
                            }

                            while (
                                isDateInExceptions(
                                    nextRunMoment.toDate(),
                                    exceptions,
                                )
                            ) {
                                nextRunMoment.add(1, "year");
                            }

                            if (
                                nextRunMoment.isSame(
                                    moment(correctLastRunDT),
                                    "day",
                                )
                            ) {
                                nextRunMoment = moment(correctLastRunDT);

                                if (
                                    moment(correctLastRunDT).isBefore(startTime)
                                ) {
                                    nextRunMoment = startTime.clone();
                                }

                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            } else {
                                nextRunMoment.set({
                                    hour: startTime.hours(),
                                    minute: startTime.minutes(),
                                    second: startTime.seconds(),
                                    millisecond: 0,
                                });
                            }

                            while (
                                nextRunMoment.isSameOrBefore(
                                    moment(nextRunMoment).set({
                                        hour: endTime.hours(),
                                        minute: endTime.minutes(),
                                        second: endTime.seconds(),
                                        millisecond: 0,
                                    }),
                                )
                            ) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }

                            if (nextRunMoment.isSameOrAfter(endTime)) {
                                nextRunMoment.add(1, "year").set({
                                    hour: startTime.hours(),
                                    minute: startTime.minutes(),
                                    second: startTime.seconds(),
                                    millisecond: 0,
                                });
                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                        });

                        if (nextRunMoments.length > 0) {
                            nextRunDT = nextRunMoments.sort(
                                (a, b) => a.getTime() - b.getTime(),
                            )[0];
                        }
                    } else if (offArr.FD) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedFDY = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const fdyOffsets = cleanedFDY
                            .split('","')

                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        fdyOffsets.forEach((offset: string) => {
                            const [_dtPart, startPart, endPart, freqPart] =
                                offset.split("|");

                            const startTime = moment(
                                startPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const endTime = moment(
                                endPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const freqUnit = freqPart;
                            const frequency = 1;

                            const addFunction =
                                suffixMap[freqUnit as keyof typeof suffixMap];
                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(correctLastRunDT);

                            const startHour = parseInt(
                                startTime.split(":")[0],
                                10,
                            );
                            const startMinute = parseInt(
                                startTime.split(":")[1],
                                10,
                            );
                            const startSecond = parseInt(
                                startTime.split(":")[2],
                                10,
                            );

                            const endHour = parseInt(endTime.split(":")[0], 10);
                            const endMinute = parseInt(
                                endTime.split(":")[1],
                                10,
                            );
                            const endSecond = parseInt(
                                endTime.split(":")[2],
                                10,
                            );

                            const startTimeOnly = moment(correctLastRunDT).set({
                                hour: startHour,
                                minute: startMinute,
                                second: startSecond,
                                millisecond: 0,
                            });

                            const endTimeOnly = moment(correctLastRunDT).set({
                                hour: endHour,
                                minute: endMinute,
                                second: endSecond,
                                millisecond: 0,
                            });

                            if (
                                correctLastRunDT.getMonth() === 0 &&
                                correctLastRunDT.getDate() === 1 &&
                                moment(correctLastRunDT).isBefore(startTimeOnly)
                            ) {
                                nextRunMoment = startTimeOnly;
                            } else if (
                                correctLastRunDT.getMonth() === 0 &&
                                correctLastRunDT.getDate() === 1 &&
                                moment(correctLastRunDT).isBetween(
                                    startTimeOnly,
                                    endTimeOnly,
                                    "seconds",
                                    "[]",
                                )
                            ) {
                                nextRunMoment = moment(correctLastRunDT);
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            } else {
                                nextRunMoment =
                                    moment(correctLastRunDT).startOf("year");
                                if (
                                    nextRunMoment.isBefore(
                                        moment(correctLastRunDT),
                                    )
                                ) {
                                    nextRunMoment.add(1, "year");
                                }

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "year")
                                        .startOf("year");
                                }

                                nextRunMoment.set({
                                    hour: startHour,
                                    minute: startMinute,
                                    second: startSecond,
                                    millisecond: 0,
                                });
                            }

                            while (nextRunMoment.isSameOrBefore(endTimeOnly)) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }

                            if (nextRunMoment.isAfter(endTimeOnly)) {
                                nextRunMoment.add(1, "year").startOf("year");

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment
                                        .add(1, "year")
                                        .startOf("year");
                                }

                                nextRunMoment.set({
                                    hour: startHour,
                                    minute: startMinute,
                                    second: startSecond,
                                    millisecond: 0,
                                });

                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                        });

                        nextRunDT = nextRunMoments.sort((a, b) => a - b)[0];
                    } else if (offArr.LD) {
                        nextRunDT =
                            status === "A"
                                ? startDateDT
                                : new Date(correctLastRunDT);

                        const cleanedLDY = repeatOffset
                            .trim()
                            .replace(/^\[|\]$/g, "")
                            .replace(/\\"/g, '"');

                        const ldyOffsets = cleanedLDY
                            .split('","')
                            .map((pattern) => pattern.replace(/^"|"$/g, ""));

                        ldyOffsets.forEach((offset) => {
                            const [dtPart, startPart, endPart, freqPart] =
                                offset.split("|");

                            if (
                                !dtPart.startsWith("LD") ||
                                !startPart.startsWith("S#") ||
                                !endPart.startsWith("E#")
                            ) {
                                throw new Error("Invalid repeatOffset format");
                            }

                            const startTime = moment(
                                startPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const endTime = moment(
                                endPart.split("#")[1],
                            ).format("HH:mm:ss");
                            const freqUnit = freqPart;
                            const frequency = 1;
                            const addFunction = suffixMap[freqUnit];

                            if (!addFunction) {
                                throw new Error(
                                    `Invalid time unit: ${freqUnit}`,
                                );
                            }

                            let nextRunMoment = moment(correctLastRunDT);

                            const lastRunTimeOnly =
                                moment(correctLastRunDT).format("HH:mm:ss");

                            if (
                                nextRunMoment.month() === 11 &&
                                nextRunMoment.date() === 31 &&
                                moment(lastRunTimeOnly, "HH:mm:ss").isBefore(
                                    moment(startTime, "HH:mm:ss"),
                                )
                            ) {
                                nextRunMoment.set({
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: parseInt(
                                        startTime.split(":")[2],
                                        10,
                                    ),
                                    millisecond: 0,
                                });
                            } else if (
                                nextRunMoment.month() === 11 &&
                                nextRunMoment.date() === 31 &&
                                moment(lastRunTimeOnly, "HH:mm:ss").isBetween(
                                    moment(startTime, "HH:mm:ss"),
                                    moment(endTime, "HH:mm:ss"),
                                    "seconds",
                                    "[]",
                                )
                            ) {
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }

                            while (
                                moment(
                                    nextRunMoment.format("HH:mm:ss"),
                                    "HH:mm:ss",
                                ).isSameOrBefore(moment(endTime, "HH:mm:ss"))
                            ) {
                                if (
                                    !isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoments.push(nextRunMoment.toDate());
                                }
                                nextRunMoment = addFunction(
                                    nextRunMoment,
                                    frequency,
                                );
                            }

                            if (
                                moment(
                                    nextRunMoment.format("HH:mm:ss"),
                                    "HH:mm:ss",
                                ).isAfter(moment(endTime, "HH:mm:ss"))
                            ) {
                                nextRunMoment.add(1, "year").endOf("year");

                                while (
                                    isDateInExceptions(
                                        nextRunMoment.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunMoment.add(1, "year").endOf("year");
                                }

                                nextRunMoment.set({
                                    hour: parseInt(startTime.split(":")[0], 10),
                                    minute: parseInt(
                                        startTime.split(":")[1],
                                        10,
                                    ),
                                    second: parseInt(
                                        startTime.split(":")[2],
                                        10,
                                    ),
                                    millisecond: 0,
                                });

                                nextRunMoments.push(nextRunMoment.toDate());
                            }
                        });

                        if (nextRunMoments.length > 0) {
                            nextRunDT = nextRunMoments.sort((a, b) => a - b)[0];
                        }
                    }
                    break;
                case "T":
                    nextRunDT =
                        status === "A"
                            ? startDateDT
                            : new Date(correctLastRunDT);

                    const cleanedT = repeatOffset
                        .trim()
                        .replace(/^\[|\]$/g, "")
                        .replace(/\\"/g, '"');

                    const timeOffsetPatterns = cleanedT
                        .split('","')
                        .map((pattern) => pattern.replace(/^"|"$/g, ""));

                    timeOffsetPatterns.forEach((offset: string) => {
                        const [dayType, freqPart, startPart, endPart] =
                            offset.split("|");

                        const frequencyUnit = freqPart.split("#")[0];
                        const frequencyValue = parseInt(
                            freqPart.split("#")[1],
                            10,
                        );
                        const startTime = moment(startPart.split("#")[1]);
                        const endTime = moment(endPart.split("#")[1]);
                        const addFunction =
                            suffixMap[frequencyUnit as keyof typeof suffixMap];
                        if (!addFunction) {
                            throw new Error(
                                `Invalid time unit: ${frequencyUnit}`,
                            );
                        }

                        let nextRunMoment = moment(correctLastRunDT);

                        if (nextRunMoment.isBefore(startTime)) {
                            nextRunMoment = startTime.clone();
                        }

                        let nextRunTimeOnly = moment(correctLastRunDT).set({
                            hour: nextRunMoment.hours(),
                            minute: nextRunMoment.minutes(),
                            second: nextRunMoment.seconds(),
                            millisecond: 0,
                        });

                        const endTimeOnly = endTime.clone().set({
                            year: nextRunTimeOnly.year(),
                            month: nextRunTimeOnly.month(),
                            date: nextRunTimeOnly.date(),
                        });

                        while (
                            nextRunTimeOnly.isSameOrBefore(endTimeOnly) &&
                            !nextRunTimeOnly.isSame(endTimeOnly)
                        ) {
                            nextRunTimeOnly = addFunction(
                                nextRunTimeOnly,
                                frequencyValue,
                            );

                            if (
                                !isDateInExceptions(
                                    nextRunTimeOnly.toDate(),
                                    exceptions,
                                )
                            ) {
                                nextRunMoments.push(nextRunTimeOnly.toDate());
                            }
                        }
                        if (nextRunTimeOnly.isSameOrAfter(endTimeOnly)) {
                            if (["S", "MM", "H"].includes(frequencyUnit)) {
                                nextRunTimeOnly.add(1, "day").set({
                                    hour: startTime.hours(),
                                    minute: startTime.minutes(),
                                    second: 0,
                                    millisecond: 0,
                                });

                                while (
                                    isDateInExceptions(
                                        nextRunTimeOnly.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunTimeOnly.add(1, "day");
                                }
                                nextRunMoments.push(nextRunTimeOnly.toDate());
                            } else if (
                                ["D", "W", "M", "Y"].includes(frequencyUnit)
                            ) {
                                const timeOnly =
                                    moment(nextRunTimeOnly).format("HH:mm:ss");
                                const endTimeOnlyFormatted =
                                    moment(endTimeOnly).format("HH:mm:ss");

                                if (
                                    moment(timeOnly, "HH:mm:ss").isSameOrAfter(
                                        moment(
                                            endTimeOnlyFormatted,
                                            "HH:mm:ss",
                                        ),
                                    )
                                ) {
                                    nextRunTimeOnly.add(
                                        frequencyValue,
                                        frequencyUnit.toLowerCase(),
                                    );

                                    nextRunTimeOnly.set({
                                        hour: startTime.hours(),
                                        minute: startTime.minutes(),
                                        second: 0,
                                        millisecond: 0,
                                    });
                                }

                                while (
                                    isDateInExceptions(
                                        nextRunTimeOnly.toDate(),
                                        exceptions,
                                    )
                                ) {
                                    nextRunTimeOnly = addFunction(
                                        nextRunTimeOnly,
                                        frequencyValue,
                                    );
                                }
                                nextRunMoments.push(nextRunTimeOnly.toDate());
                            }
                        }
                        if (
                            dayType === "WD" &&
                            !momentBt(nextRunTimeOnly).isWorkingDay()
                        ) {
                            while (!momentBt(nextRunTimeOnly).isWorkingDay()) {
                                nextRunTimeOnly.add(1, "day");
                            }
                        }
                    });
                    if (nextRunMoments.length > 0) {
                        nextRunDT = nextRunMoments.sort(
                            (a, b) => a.getTime() - b.getTime(),
                        )[0];
                    }
                    break;
                default:
                    break;
            }
        }
        return nextRunDT;
    }

    addLastRunTime(lastRun, nextRun) {
        const next = nextRun.setHours(
            lastRun.getHours(),
            lastRun.getMinutes(),
            lastRun.getSeconds(),
            lastRun.getMilliseconds(),
        );
        return new Date(next);
    }

    async getPlansToRun(date = new Date()) {
        // @t3b-1876
        // DST sensitive (for mssql) date detection
        const formattedDate = (() => {
            const formattedDate = moment(date);
            if (globalThis.dynamicConfig.db.client === "mssql") {
                // Subtract 2 hours in the Summer, 1 in the Winter
                const subtractAmount = formattedDate.isDST() ? 2 : 1;
                formattedDate.subtract({ hours: subtractAmount });
            }
            // @t3b-1969 Plánování 29. února a nepřestupný rok
            return formattedDate.isValid()
                ? formattedDate.format("YYYY-MM-DD HH:mm:ss")
                : "1970-01-01 00:00:00";
        })();

        // still not runned plans - but only with active template
        const connNotRunned = this.connection
            .select([
                "PLN.ORG_ID",
                "PLN.PLN_ID",
                "PLN.PLN_STATUS",
                "PLN.PLN_REPEAT_OFFSET_TYPE",
                "PLN.PLN_REPEAT_OFFSET",
                "PLN.PLN_START_DATETIME",
                "PLN.PLN_LAST_RUN",
            ])
            .from("PLAN_PROCESS as PLN")
            .join(
                "TEMPLATE_PROCESSES as TPROC",
                "PLN.TPROC_ID",
                "=",
                "TPROC.TPROC_ID",
            )
            .where("TPROC.TPROC_STATUS", ProcessConstants.STATUS_ACTIVE)
            .andWhere("PLN.PLN_STATUS", PlanConstants.STATUS_ACTIVE)
            .andWhere(function () {
                this.whereRaw(
                    `PLN.PLN_START_DATETIME <= ${globalThis.orm.db.toDateTime()}`,
                    formattedDate,
                );
            });

        const notRunnedPlans =
            await this.createCollection(connNotRunned).collectAll();

        // repeating plans - but only plans with active template
        const connRepeat = this.connection
            .select("PLN.*")
            .from("PLAN_PROCESS as PLN")
            .join(
                "TEMPLATE_PROCESSES as TPROC",
                "PLN.TPROC_ID",
                "=",
                "TPROC.TPROC_ID",
            )
            .where("TPROC.TPROC_STATUS", ProcessConstants.STATUS_ACTIVE)
            .andWhere("PLN.PLN_STATUS", PlanConstants.STATUS_RUNNING);

        const repeatingPlans = await this.createCollection(connRepeat)
            .collectAll()
            .then((plans) => {
                const now = date.getTime();
                const plansArr = [];

                plans.forEach((plan) => {
                    // run only if he already can run
                    const nextRun = this.getNextRun(plan);
                    const next = nextRun.getTime();
                    if (now >= next) {
                        plansArr.push(plan);
                    }
                });

                return plansArr;
            });

        return notRunnedPlans.concat(repeatingPlans);
    }

    setPlanStatus(status, planId) {
        return this.connection("PLAN_PROCESS")
            .where("PLN_ID", planId)
            .update({ PLN_STATUS: status });
    }

    async runPlanedProcesses(date = new Date()) {
        const plans = await this.getPlansToRun(date);
        for (const plan of plans) {
            try {
                await this.runPlan(plan.PLN_ID, date);
                if (
                    plan.PLN_STATUS === PlanConstants.STATUS_ACTIVE &&
                    plan.PLN_REPEAT_OFFSET_TYPE === "N"
                ) {
                    // finish plan, because it has no repeat
                    await this.setPlanStatus(
                        PlanConstants.STATUS_FINISHED,
                        plan.PLN_ID,
                    );
                }
            } catch (err) {
                globalThis.tasLogger.error(`Plan error ${err.message}`, {
                    err,
                    pln_id: plan.PLN_ID,
                });
                throw err;
            }
        }
    }
}
