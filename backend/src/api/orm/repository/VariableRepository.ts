// @ts-nocheck
// @ts-nocheck
import moment from "moment-timezone";
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../services/UtilsService";
import { VariableLovRepository } from "./VariableLovRepository";
import { baseGet } from "../../utils/LodashCustomized";
import * as VARIABLES from "../entity/const/variableConst";
import { Variable } from "../entity/Variable";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class VariableRepository extends BaseRepository<Variable> {
    meta() {
        return {
            tableName: "INSTANCE_VARIABLES",
            entityName: "Variable",
            defaultAlias: "IV",
            entity: () => new Variable(),
            archived: true,
        };
    }

    async storeAndLog(entity) {
        const result = await super.store(entity).catch(async (err) => {
            globalThis.tasLogger.error(
                `Could not store Variable '${entity.IVAR_NAME}'.`,
                {
                    iproc_id: entity.IPROC_ID,
                    entity,
                },
            );
            throw err;
        });
        globalThis.tasLogger.info(`Variable '${entity.IVAR_NAME}' stored.`, {
            iproc_id: entity.IPROC_ID,
            entity,
        });
        return result;
    }

    getValuesByMapping(
        data: Record<string, any> = {},
        mapping: Record<
            string,
            string | { value: string; option: string }
        > = {},
        allowOptions: boolean = false,
        prefix = null,
        enterArray: boolean = false,
        returnUnexpectedArray: boolean = false,
        arrayJoinChar: any = false,
        allowNullValues?: boolean,
    ): Record<string, string | number | Date | null> {
        const variableNames = Object.keys(mapping);
        return variableNames.reduce((result, variableName) => {
            let newValue = null;
            const pathInObject = mapping[variableName];
            const pathArray = _.toPath(pathInObject);

            if (prefix) {
                // Apply prefix to all parts of the path - excluding numbers
                const segments = pathArray.map((segment) =>
                    UtilsService.isNumericString(segment)
                        ? segment
                        : `${prefix}${segment}`,
                );

                // Create a path-like string
                const segmentsPath = `[${segments.join("][")}]`;

                if (!enterArray) {
                    // Get value from object by path
                    newValue = _.get(data, segmentsPath, null);
                } else {
                    // Get value from object by path
                    newValue = baseGet(
                        data,
                        segments,
                        segments,
                        true,
                        returnUnexpectedArray,
                        arrayJoinChar,
                    );
                }
            } else if (!enterArray) {
                // Get value from object by path
                newValue = _.get(data, pathInObject, null);
            } else {
                newValue = baseGet(
                    data,
                    pathArray,
                    pathArray,
                    enterArray,
                    returnUnexpectedArray,
                    arrayJoinChar,
                );
            }

            // Does the required value exist?
            if (!newValue && newValue !== false) {
                //
                // ONLY FOR EWS PURPOSES
                //
                if (
                    allowOptions &&
                    typeof pathInObject === "object" &&
                    pathInObject &&
                    pathInObject.value
                ) {
                    const option = pathInObject.isConstant
                        ? "constant"
                        : pathInObject.option; // .isConstant backwards compatibility
                    switch (option) {
                        case "constant":
                            newValue = pathInObject.value;
                            break;
                        case "optional":
                            newValue =
                                baseGet(
                                    data,
                                    _.toPath(pathInObject.value),
                                    _.toPath(pathInObject.value),
                                    enterArray,
                                    returnUnexpectedArray,
                                    arrayJoinChar,
                                ) || null;
                            break;
                        default:
                            throw new InternalException(
                                `Unknown option '${option}'`,
                                "UNKNOWN_OPTION",
                            );
                    }
                } else if (newValue !== null || !allowNullValues) {
                    // Could not retrieve the value
                    throw new InternalException(
                        `Property '${pathInObject}' was not found in the provided data!`,
                        "MAPPING_MISMATCH",
                        {
                            iproc_id: this.iprocId(),
                            data,
                            property: pathInObject,
                            prefix,
                            enterArray,
                        },
                    );
                }
            }
            result[variableName] = newValue;
            return result;
        }, {});
    }

    async updateForProcessByMapping(
        iprocId,
        data = {},
        mapping = {},
        allowOptions = false,
        prefix = null,
        enterArray = false,
        returnUnexpectedArray = false,
        arrayJoinChar: any = false,
        variableList?: any[],
        allowNullValues?: boolean,
    ): Record<string, string | number | Date | null> {
        // Is Empty object?

        if (Object.keys(mapping).length) {
            const coll = this.getByProcess(iprocId);
            coll.knex.whereIn("TV.TVAR_NAME", Object.keys(mapping));
            const variablesToUpdate = await coll.collectAll();

            // Mapping must be valid
            if (variablesToUpdate.length !== Object.keys(mapping).length) {
                // TODO: detect which var in mapping is wrong
                throw new InternalException(
                    "The provided mapping does not match the Process variables!",
                    "MAPPING_MISMATCH",
                    {
                        variables: variablesToUpdate.map(
                            (item) => item.IVAR_NAME,
                        ),
                    },
                );
            }

            const mappedValues = this.getValuesByMapping(
                data,
                mapping,
                allowOptions,
                prefix,
                enterArray,
                returnUnexpectedArray,
                arrayJoinChar,
                allowNullValues,
            );

            // Update Variable values from the provided data
            for (const variable of variablesToUpdate) {
                const newValue = mappedValues[variable.IVAR_NAME];

                const oldValue = variable.value;
                variable.value = newValue;

                // Update sandbox?
                if (variableList) {
                    const sandboxVariable = variableList.getBy(
                        "IVAR_NAME",
                        variable.IVAR_NAME,
                    );
                    if (sandboxVariable) {
                        sandboxVariable.value = newValue;
                    }
                }

                await this.store(variable);

                // Shorten values if necessary
                const oldValueToLog =
                    oldValue && oldValue.length > 300
                        ? `${oldValue.substring(0, 300)}...`
                        : oldValue;
                const newValueToLog =
                    newValue && newValue.length > 300
                        ? `${newValue.substring(0, 300)}...`
                        : newValue;

                globalThis.tasLogger.info(
                    `Updated variable ${variable.IVAR_NAME}(${variable.IVAR_ID}) from '${oldValueToLog}' to '${newValueToLog}'`,
                    {
                        iproc_id: iprocId,
                        ivar_id: variable.IVAR_ID,
                        ivar_name: variable.IVAR_NAME,
                        values: {
                            old: oldValueToLog,
                            new: newValueToLog,
                        },
                    },
                );
            }
            return mappedValues;
        }
    }

    /**
     * Check whether the status variable can take the specified value
     * @param {object} entity
     */
    async validateStateVarValue(entity) {
        const oldValue = entity.IVAR_DT_INDEX;
        const newValue = entity.value;

        if (oldValue !== null && newValue !== null && oldValue !== newValue) {
            // get the DTC_ID of the new value
            const newValueColConn = this.connection
                .select("DT.DT_ID", "DTC.DTC_ID", "DTC.DTC_NAME")
                .from("DYNAMIC_TABLE as DT")
                .leftJoin("DYNAMIC_TABLE_COLS as DTC", "DTC.DT_ID", "DT.DT_ID")
                .where("DT.DT_NAME", entity.DLIST_NAME)
                .where("DTC.DTC_NAME", newValue);

            const newValueColumns = await newValueColConn;
            const newValueColumn = newValueColumns[0];

            // get COL_X according to the new value
            if (newValueColumn) {
                const oldValueConn = this.connection
                    .select(
                        "DT_ID",
                        "DTV_INDEX",
                        `COL_${newValueColumn.DTC_ID}`,
                    )
                    .from("DYNAMIC_TABLE_VALUES")
                    .where("DT_ID", newValueColumn.DT_ID)
                    .where("DTV_INDEX", oldValue);

                const oldValueColumns = await oldValueConn;
                const oldValueColumn = oldValueColumns[0];

                if (
                    oldValueColumn &&
                    oldValueColumn[`COL_${newValueColumn.DTC_ID}`] === null
                ) {
                    globalThis.tasLogger.warning(
                        "The object state has been changed to a value that violates the allowed state transitions defined by the object life cycle.",
                        {
                            iproc_id: entity.IPROC_ID,
                            ivar_id: entity.IVAR_ID,
                            ivar_name: entity.IVAR_NAME,
                            dlist_name: entity.DLIST_NAME,
                            old_value: oldValue,
                            new_value: newValue,
                        },
                    );
                }
            }
        }
    }

    async store(entity) {
        // Reschedule task
        if (entity.IVAR_TYPE === "D" && entity.IPROC_ID) {
            await globalThis.orm
                .repo("InstanceTask", this.connection)
                .rescheduleByVariable(
                    entity.IPROC_ID,
                    entity.TVAR_ID,
                    entity.IVAR_DATE_VALUE,
                );
        }

        if (entity.IVAR_TYPE === "DT" && entity.TVAR_ID) {
            if (entity.IVAR_MULTI === "X" && _.isEmpty(entity.value)) {
                entity.IVAR_DT_INDEX = null;
                entity.IVAR_MULTI_SELECTED = JSON.stringify([]);
                entity.value = null;
                return await this.storeAndLog(entity);
            }
            if (
                entity.value === null ||
                entity.value === "" ||
                typeof entity.value === "undefined"
            ) {
                entity.IVAR_DT_INDEX = null;
                entity.value = null;
                return await this.storeAndLog(entity);
            }

            if (
                entity.IVAR_ATTRIBUTE === VARIABLES.ATTR_STATE_VAR &&
                entity.IVAR_MULTI !== "X"
            ) {
                await this.validateStateVarValue(entity);
            }

            const tvarRepo = globalThis.orm.repo(
                "templateVariable",
                this.connection,
            );
            const tvar = await tvarRepo.get(entity.TVAR_ID, ["TVAR_COL_INDEX"]);
            const dtRepo = globalThis.orm.repo(
                "dynamicTableValue",
                this.connection,
            );
            const collection = await dtRepo.getForVariable(entity.IVAR_ID);

            let { value } = entity;
            if (!Array.isArray(entity.value)) {
                value = [value];
            }

            // store from event [{"DTV_INDEX":"DTV_VALUE"}, ...]
            if (
                entity.IVAR_MULTI === "X" &&
                !_.isEmpty(value) &&
                typeof value[0] === "object"
            ) {
                value = entity.getDtMultiIndexesAndValues(value).indexes;
            }

            collection.knex.whereIn(
                "DTV_INDEX",
                _.map(
                    value,
                    (singleValue) => `${singleValue}`, // eq to .toString
                ),
            );
            const values = await collection.fetchAll();

            if (!Array.isArray(values) || values.length === 0) {
                throw new UserException(
                    `No value for template variable in dynamic table. [IVAR_ID=${entity.IVAR_ID}] where [DLC_ID=${tvar.TVAR_COL_INDEX}] and DLV_INDEX=${entity.value}]`,
                    "BAD_INPUT",
                );
            }

            if (entity.IVAR_MULTI === "X") {
                entity.value = null;
                entity.IVAR_DT_INDEX = null;
                entity.IVAR_COL_INDEX = tvar.TVAR_COL_INDEX;
                entity.IVAR_MULTI_SELECTED = JSON.stringify(
                    values.map((val) => ({
                        [val.DTV_INDEX]: val.DTV_VALUE,
                    })),
                );
                const newIvarTextValue = values
                    .map((val) => val.DTV_VALUE)
                    .toString();
                if (newIvarTextValue) {
                    const byteLength =
                        UtilsService.byteLength(newIvarTextValue);
                    if (byteLength >= 4000) {
                        const diff = byteLength - newIvarTextValue.length;
                        entity.IVAR_BIG_VALUE = newIvarTextValue;
                        entity.IVAR_TEXT_VALUE = newIvarTextValue.substring(
                            0,
                            3950 - diff,
                        );
                    } else {
                        entity.IVAR_BIG_VALUE = null;
                        entity.IVAR_TEXT_VALUE = newIvarTextValue;
                    }
                }
            } else {
                entity.IVAR_DT_INDEX = entity.value;
                entity.value = values[0].DTV_VALUE;
                entity.IVAR_COL_INDEX = tvar.TVAR_COL_INDEX;
            }

            return await this.storeAndLog(entity);
        }
        if (
            entity.IVAR_TYPE === "T" &&
            entity.TVAR_ID &&
            entity.IVAR_ATTRIBUTE !== "F"
        ) {
            const newValue = entity.value;

            if (newValue) {
                const byteLength = UtilsService.byteLength(newValue);
                if (byteLength >= 4000) {
                    const diff = byteLength - newValue.length;
                    entity.IVAR_BIG_VALUE = newValue;
                    entity.IVAR_TEXT_VALUE = `${newValue.substring(
                        0,
                        3950 - diff,
                    )}... (text je příliš dlouhý)`;
                } else {
                    entity.IVAR_BIG_VALUE = null;
                    entity.IVAR_TEXT_VALUE = newValue;
                }
            } else {
                entity.IVAR_BIG_VALUE = null;
                entity.IVAR_TEXT_VALUE = newValue;
            }

            return await this.storeAndLog(entity);
        }
        if (
            entity.IVAR_MULTI === "X" &&
            entity.IVAR_TYPE === "DL" &&
            entity.IVAR_ATTRIBUTE !== null
        ) {
            if (entity.IVAR_MULTI_SELECTED) {
                if (entity.IVAR_MULTI_SELECTED === "[null]") {
                    // quick fix, temporally solution, converts bad input to empty array
                    entity.IVAR_MULTI_SELECTED = "[]";
                } else if (
                    !_.every(JSON.parse(entity.IVAR_MULTI_SELECTED), Number)
                ) {
                    throw new UserException(
                        `DL multi variable ${entity.IVAR_NAME}: Only numbers are allowed as a value`,
                        "BAD_INPUT",
                    );
                }
            }
        }

        return await this.storeAndLog(entity);
    }

    /**
     * Get all variables for process.
     *
     * @param iprocId
     * @param ivarIds {[]|null} null to retrieve all variables
     * @returns {*|BaseCollection}
     */
    getByProcess(iprocId, ivarIds = null) {
        // Map tvar columns
        const tvarEntity = globalThis.orm
            .repo("TemplateVariable", this.connection)
            .getEntity();
        let attrs = tvarEntity.attributes();
        attrs = _.omit(attrs, ["DLIST_NAME", "ORG_ID"]);
        let tvarCols = Object.keys(attrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const ivarEntity = this.getEntity();
        attrs = ivarEntity.attributes();
        attrs = _.omit(attrs, "TVAR_ID");
        let ivarCols = Object.keys(attrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        // @t3b-1595 Zobrazení jazykových mutací LOV proměnných v CO
        // Map tvar lov columns
        const tvarLovCols = [];
        globalThis.dynamicConfig.langs.forEach((language) => {
            tvarLovCols.push(`TVARLOV_TEXT_VALUE_${language.toUpperCase()}`);
        });

        const con = this.connection
            .select([].concat(tvarCols, tvarLovCols, ivarCols))
            .from(`${this.tableName} as IV`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "IV.TVAR_ID", "TV.TVAR_ID")
            .leftJoin("TEMPLATE_VARIABLE_LOV as TVLOV", (builder) => {
                builder
                    .on("IV.TVAR_ID", "TVLOV.TVAR_ID")
                    .andOn("IV.IVAR_TEXT_VALUE", "TVLOV.TVARLOV_TEXT_VALUE");
            })
            .where("IV.IPROC_ID", iprocId);

        // for specific
        if (ivarIds !== null && ivarIds.length > 0) {
            con.whereIn(
                "IV.IVAR_ID",
                Array.isArray(ivarIds) ? ivarIds : [ivarIds],
            );
        }

        return globalThis.orm.collection(this, con);
    }

    /**
     * @param {number} iProcId
     * @returns {(*|BaseCollection|BaseCollection)[]}
     */
    prepareCollectionsForVariableList(iProcId) {
        const variablesForProcessCollection = this.getByProcess(iProcId);

        const variableLovRepo = globalThis.orm.repo(
            "VariableLov",
            this.connection,
        );
        const variablesForProcessLovsCollection =
            variableLovRepo.getForVariables(
                "*",
                variableLovRepo.getVariablesByProcess(iProcId),
            );

        return [
            variablesForProcessCollection,
            variablesForProcessLovsCollection,
        ];
    }

    /**
     * Assemble variables and their lov values. Use this, when you want complex Variable entities.
     *
     * @param iprocId
     * @param collVars {BaseCollection} Collection of vars (usually result from this.getByProcess)
     * @return {Promise<any>}
     */
    assembleVariables(_iprocId, collVars) {
        const varLovRepo = globalThis.orm.repo("variableLov", this.connection);

        return collVars.collectAll().then((items) =>
            // retrieving lov data
            varLovRepo
                .getForVariables("*", _.map(items, "IVAR_ID"))
                .collectAssoc("IVAR_ID")
                .then((varLovs) =>
                    // join retrieved variables with retrieved lovs data
                    VariableLovRepository.joinLov(items, varLovs),
                )
                .then((items) =>
                    // translate id to names (orgs, users, roles)
                    varLovRepo.fillLovValues(items),
                ),
        );
    }

    filterByName(iprocId, names) {
        return (
            globalThis.database
                .select()
                .from(this.tableName)
                // Retrieve all variables for INSTANCE_PROCESS by IPROC_ID
                .where("IPROC_ID", iprocId)
                // Filter the variables - only select the ones being updated
                .whereIn("IVAR_NAME", Array.isArray(names) ? names : [names])
        );
    }

    getCollection() {
        // Map tvar columns
        const tvarEntity = globalThis.orm
            .repo("TemplateVariable", this.connection)
            .getEntity();
        let attrs = tvarEntity.attributes();
        attrs = _.omit(attrs, ["DLIST_NAME", "ORG_ID"]);
        let tvarCols = Object.keys(attrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const ivarEntity = this.getEntity();
        attrs = ivarEntity.attributes();
        attrs = _.omit(attrs, "TVAR_ID");
        let ivarCols = Object.keys(attrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        const con = this.connection
            .select(["DT.DT_ID"].concat(tvarCols).concat(ivarCols))
            .from(`${this.tableName} as IV`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "IV.TVAR_ID", "TV.TVAR_ID")
            .leftJoin("DYNAMIC_TABLE AS DT", "IV.DLIST_NAME", "DT.DT_NAME");

        return globalThis.orm.collection(this, con);
    }

    async getForTask(columns, itaskId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getForTask)",
            );
        }

        if (version <= 0) {
            throw new InternalException(
                "Version must be defined. INSTANCE_TASK_VAR_USAGE is no longer used.",
            );
        }

        // Map tvar entities -
        const tvarEntity = globalThis.orm
            .repo("TemplateVariable", this.connection)
            .getEntity();
        let attrs1 = tvarEntity.attributes();
        attrs1 = _.omit(attrs1, ["DLIST_NAME", "ORG_ID", "TVAR_ID"]);
        let tvarCols = Object.keys(attrs1);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const varCols = [];
        const attrs2 = this.entity.attributes();
        const attrNames = Object.keys(attrs2);
        attrNames.forEach((attrName) => {
            if (attrs2[attrName].translated) {
                varCols.push(`IV.${attrName}`);
            }
        });

        // usage - grid axis
        const axisCols = [];
        axisCols.push("ITVU.AXIS_X AS AXIS_X", "ITVU.AXIS_Y AS AXIS_Y");

        const cols = [
            "IVS.IVAR_ID AS IVAR_ID",
            "IV.TVAR_ID AS TVAR_ID",
            "IV.IPROC_ID AS IPROC_ID",
            "IVS.IVAR_DT_INDEX AS IVAR_DT_INDEX",
            "IV.IVAR_COL_INDEX AS IVAR_COL_INDEX",
            "IV.IVAR_NAME AS IVAR_NAME",
            "IV.IVAR_TYPE AS IVAR_TYPE",
            "IVS.IVARSN_TEXT_VALUE AS IVAR_TEXT_VALUE",
            "IVS.IVARSN_NUMBER_VALUE AS IVAR_NUMBER_VALUE",
            "IVS.IVARSN_DATE_VALUE AS IVAR_DATE_VALUE",
            "IV.IVAR_ATTRIBUTE AS IVAR_ATTRIBUTE",
            `ITVU.TTASKVARUSG_USAGE AS IVAR_USAGE`,
            "IV.DLIST_NAME AS DLIST_NAME",
            "IVS.IVARSN_MULTI AS IVAR_MULTI",
            "IVS.IVARSN_MULTI_SELECTED AS IVAR_MULTI_SELECTED",
            "IVS.IVARSN_BIG_VALUE AS IVAR_BIG_VALUE",
            "IVS.ITASK_ID AS ITASK_ID",
            `ITVU.TTASKVARUSG_ID AS ITASKVARUSG_ID`,
            "DT.DT_ID",
        ]
            .concat(tvarCols)
            .concat(varCols)
            .concat(axisCols);

        const coll = BaseRepository.prototype.getCollection.call(this, columns); // Workaround to call super.getCollection(). See https://github.com/babel/babel/issues/3930.
        coll.knex.from(function () {
            // Using templates only
            this.select(cols)
                .from("INSTANCE_TASKS AS IT")
                .join(
                    "TEMPLATE_TASK_VAR_USAGE AS ITVU",
                    "ITVU.TTASK_ID",
                    "IT.TTASK_ID",
                )
                .join("TEMPLATE_VARIABLES AS TV", "ITVU.TVAR_ID", "TV.TVAR_ID")
                .join("INSTANCE_VARIABLES AS IV", {
                    "TV.TVAR_ID": "IV.TVAR_ID",
                    "IT.IPROC_ID": "IV.IPROC_ID",
                })
                .join("INSTANCE_VARIABLES_SNAP AS IVS", {
                    "IVS.IVAR_ID": "IV.IVAR_ID",
                    "IVS.ITASK_ID": "IT.ITASK_ID",
                })
                .leftJoin("DYNAMIC_TABLE AS DT", "IV.DLIST_NAME", "DT.DT_NAME")
                .where("IT.ITASK_ID", itaskId)
                .where("IT.ITASK_MULTIINSTANCE_FLAG", "M")
                .where("ITVU.TTASKVARUSG_VERSION", version)
                .unionAll(function () {
                    this.select(
                        [
                            "IV.IVAR_ID AS IVAR_ID",
                            "IV.TVAR_ID AS TVAR_ID",
                            "IV.IPROC_ID AS IPROC_ID",
                            "IV.IVAR_DT_INDEX AS IVAR_DT_INDEX",
                            "IV.IVAR_COL_INDEX AS IVAR_COL_INDEX",
                            "IV.IVAR_NAME AS IVAR_NAME",
                            "IV.IVAR_TYPE AS IVAR_TYPE",
                            "IV.IVAR_TEXT_VALUE AS IVAR_TEXT_VALUE",
                            "IV.IVAR_NUMBER_VALUE AS IVAR_NUMBER_VALUE",
                            "IV.IVAR_DATE_VALUE AS IVAR_DATE_VALUE",
                            "IV.IVAR_ATTRIBUTE AS IVAR_ATTRIBUTE",
                            "ITVU.TTASKVARUSG_USAGE AS IVAR_USAGE",
                            "IV.DLIST_NAME AS DLIST_NAME",
                            "IV.IVAR_MULTI AS IVAR_MULTI",
                            "IV.IVAR_MULTI_SELECTED AS IVAR_MULTI_SELECTED",
                            "IV.IVAR_BIG_VALUE AS IVAR_BIG_VALUE",
                            "IT.ITASK_ID AS ITASK_ID",
                            "ITVU.TTASKVARUSG_ID AS ITASKVARUSG_ID",
                            "DT.DT_ID",
                        ]
                            .concat(tvarCols)
                            .concat(varCols)
                            .concat(axisCols),
                    )
                        .from("INSTANCE_TASKS AS IT")
                        .join(
                            "TEMPLATE_TASK_VAR_USAGE as ITVU",
                            "ITVU.TTASK_ID",
                            "IT.TTASK_ID",
                        )
                        .join(
                            "TEMPLATE_VARIABLES AS TV",
                            "ITVU.TVAR_ID",
                            "TV.TVAR_ID",
                        )
                        .join("INSTANCE_VARIABLES AS IV", {
                            "TV.TVAR_ID": "IV.TVAR_ID",
                            "IT.IPROC_ID": "IV.IPROC_ID",
                        })
                        .leftJoin(
                            "DYNAMIC_TABLE AS DT",
                            "IV.DLIST_NAME",
                            "DT.DT_NAME",
                        )
                        .where("IT.ITASK_ID", itaskId)
                        .where("ITVU.TTASKVARUSG_VERSION", version)
                        .where("IT.ITASK_MULTIINSTANCE_FLAG", "<>", "M");
                })
                .as("ignored_alias");
        });
        return coll;
    }

    getForEvent(itaskId) {
        // Map tvar entities -
        const tvarEntity = globalThis.orm
            .repo("TemplateVariable", this.connection)
            .getEntity();
        let attrs1 = tvarEntity.attributes();
        attrs1 = _.omit(attrs1, ["DLIST_NAME", "ORG_ID", "TVAR_ID"]);
        let tvarCols = Object.keys(attrs1);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const varCols = [];
        const attrs2 = this.entity.attributes();
        const attrNames = Object.keys(attrs2);
        attrNames.forEach((attrName) => {
            if (attrs2[attrName].translated) {
                varCols.push(`IV.${attrName}`);
            }
        });

        const columns = [
            "IV.IVAR_ID",
            "IV.TVAR_ID",
            "IV.IVAR_NAME",
            "IV.IVAR_TYPE",
            "IV.IVAR_TEXT_VALUE",
            "IV.IVAR_NUMBER_VALUE",
            "IV.IVAR_DATE_VALUE",
            "IV.IVAR_BIG_VALUE",
            "IV.IVAR_ATTRIBUTE",
            "IV.DLIST_NAME",
            "IV.IVAR_DT_INDEX AS IVAR_DT_INDEX",
            "IV.IVAR_COL_INDEX AS IVAR_COL_INDEX",
            "IT.ITASK_ID",
        ]
            .concat(tvarCols)
            .concat(varCols);

        return globalThis.orm.collection(
            "variable",
            this.connection
                .select(columns)
                .from("INSTANCE_TASKS as IT")
                .join(
                    "EVENT_DEFINITION as ED",
                    "ED.EVEDEF_NAME",
                    "IT.ITASK_EVENT",
                )
                .joinRaw(
                    `JOIN "RULE_DEFINITION" "RD" ON "RD"."EVEDEF_ID" = "ED"."EVEDEF_ID" AND "RD"."RDEF_VALUE" = '$EVEW_' ${globalThis.orm.db.concat()} "IT"."TTASK_ID"`,
                )
                .join("RULE_DEFINITION_PARAM as RP", "RP.RDEF_ID", "RD.RDEF_ID")
                .joinRaw(
                    `JOIN "INSTANCE_VARIABLES" "IV" ON "IV"."IPROC_ID" = "IT"."IPROC_ID" AND "IV"."TVAR_ID" = "RP"."TVAR_ID_DEST"`,
                )
                .joinRaw(
                    `JOIN "TEMPLATE_VARIABLES" "TV" ON "IV"."TVAR_ID" = "TV"."TVAR_ID"`,
                )
                .where("IT.ITASK_ID", itaskId),
        );
    }

    /**
     * Removes entity and dependencies.
     *
     * @param {Variable} ivar
     * @return {Promise}
     */
    async delete(ivar) {
        await this.connection
            .from("INSTANCE_VARIABLE_HISTORY")
            .where("IVAR_ID", ivar.IVAR_ID)
            .del();
        await this.connection
            .from("INSTANCE_TASK_COMPLETION")
            .where("IVAR_ID", ivar.IVAR_ID)
            .del();
        await this.connection
            .from("INSTANCE_VARIABLES_SNAP")
            .where("IVAR_ID", ivar.IVAR_ID)
            .del();
        await this.connection
            .from("INSTANCE_VARIABLE_LOV")
            .where("IVAR_ID", ivar.IVAR_ID)
            .del();

        return await BaseRepository.prototype.delete.call(this, ivar); // workaround of super() in async
    }

    cloneVariablesFromTemplate(iprocId) {
        const iprocRepo = globalThis.orm.repo("process", this.connection);
        const tvarRepo = globalThis.orm.repo(
            "TemplateVariable",
            this.connection,
        );

        const self = this;
        return iprocRepo.get(iprocId).then((iproc) =>
            tvarRepo
                .getForTemplateProcess(iproc.TPROC_ID)
                .collectAll()
                .then((tvars) => {
                    if (!Array.isArray(tvars) || tvars.length === 0) {
                        return;
                    }

                    for (const tvar of tvars) {
                        self.connection
                            .select("IVAR_ID")
                            .from(self.tableName)
                            .where("IPROC_ID", iprocId)
                            .where("TVAR_ID", tvar.TVAR_ID)
                            .then((vars) => {
                                const entity = self.getEntity();

                                if (Array.isArray(vars) && vars.length > 0) {
                                    // Already exists do not clear filled value.
                                    entity.IVAR_ID = vars[0].IVAR_ID;
                                    entity.IVAR_TYPE = tvar.TVAR_TYPE;
                                    entity.IVAR_ATTRIBUTE = tvar.TVAR_ATTRIBUTE;
                                    entity.IVAR_MULTI = tvar.TVAR_MULTI;
                                    entity.ORG_ID = 1;

                                    return self.store(entity);
                                }

                                entity.IPROC_ID = iprocId;
                                entity.IVAR_NAME = tvar.TVAR_NAME;
                                entity.IVAR_TYPE = tvar.TVAR_TYPE;
                                entity.IVAR_MULTITASK_BEHAVIOUR =
                                    tvar.TVAR_MULTITASK_BEHAVIOUR;
                                entity.IVAR_TEXT_VALUE = tvar.TVAR_TEXT_VALUE;
                                entity.IVAR_NUMBER_VALUE =
                                    tvar.TVAR_NUMBER_VALUE;
                                entity.IVAR_DATE_VALUE = tvar.TVAR_DATE_VALUE;
                                entity.IVAR_ATTRIBUTE = tvar.TVAR_ATTRIBUTE;
                                entity.TVAR_ID = tvar.TVAR_ID;
                                entity.DLIST_NAME = tvar.DLIST_NAME;
                                entity.IVAR_MULTI = tvar.TVAR_MULTI;
                                entity.IVAR_BIG_VALUE = tvar.TVAR_BIG_VALUE;
                                entity.ORG_ID = 1;
                                entity.IVAR_COL_INDEX = tvar.TVAR_COL_INDEX;

                                return self.store(entity);
                            });
                    }
                }),
        );
    }

    /**
     * Copy instances from template variable.
     * @param iprocId
     */
    copyVariablesFromTemplate(iprocId) {
        const tvarTranslations = [];
        const ivarTranslations = [];

        globalThis.dynamicConfig.langs.forEach((lang) => {
            tvarTranslations.push(`"TV"."TVAR_NAME_${lang.toUpperCase()}"`);

            ivarTranslations.push(`"IVAR_NAME_${lang.toUpperCase()}"`);
        });

        let tvarAppend = tvarTranslations.join(", ");
        tvarAppend = tvarAppend ? `, ${tvarAppend}` : tvarAppend;
        let ivarAppend = ivarTranslations.join(", ");
        ivarAppend = ivarAppend ? `, ${ivarAppend}` : ivarAppend;

        const insertProcessToCacheSql = `
            insert into "INSTANCE_PROCESS_VARIABLES" ("IPROC_ID")
            select :IPROC_ID ${globalThis.orm.db.fromDual()} 
            where not exists
                 (select 1 from "INSTANCE_PROCESS_VARIABLES" where "IPROC_ID" = :IPROC_ID)
        `;
        // All variables copied with one sql. Much faster than iteration.
        const copyVariablesSql = `                    
            INSERT INTO "INSTANCE_VARIABLES" (
    "IPROC_ID",
    "ORG_ID",
    "IVAR_ID",
    "IVAR_NAME",
    "IVAR_TYPE",
    "IVAR_MULTITASK_BEHAVIOUR",
    "IVAR_TEXT_VALUE",
    "IVAR_NUMBER_VALUE",
    "IVAR_DATE_VALUE",
    "IVAR_ATTRIBUTE",
    "DLIST_NAME",
    "IVAR_MULTI",
    "IVAR_BIG_VALUE",
    "IVAR_COL_INDEX",
    "TVAR_ID" ${ivarAppend}
)
SELECT 
    :IPROC_ID0 AS "IPROC_ID",
    1 AS "ORG_ID",
    ${globalThis.orm.db.sequence("IVAR_ID_SEQ")} AS "IVAR_ID",
    "TV"."TVAR_NAME",
    "TV"."TVAR_TYPE",
    "TV"."TVAR_MULTITASK_BEHAVIOUR",
    "TV"."TVAR_TEXT_VALUE",
    "TV"."TVAR_NUMBER_VALUE",
    "TV"."TVAR_DATE_VALUE",
    "TV"."TVAR_ATTRIBUTE",
    "TV"."DLIST_NAME",
    "TV"."TVAR_MULTI",
    "TV"."TVAR_BIG_VALUE",
    "TV"."TVAR_COL_INDEX",
    "TV"."TVAR_ID" ${tvarAppend}
FROM "INSTANCE_PROCESSES" AS "IP"
JOIN "TEMPLATE_VARIABLES" AS "TV" 
  ON "IP"."TPROC_ID" = "TV"."TPROC_ID"
WHERE "IP"."IPROC_ID" = :IPROC_ID1;`;

        return this.connection
            .raw(insertProcessToCacheSql, { IPROC_ID: iprocId })
            .then(() =>
                this.connection.raw(copyVariablesSql, {
                    IPROC_ID0: iprocId,
                    IPROC_ID1: iprocId,
                }),
            )
            .then(() => {
                const lovsRepo = globalThis.orm.repo(
                    "VariableLov",
                    this.connection,
                );
                return lovsRepo.copyVariableLovsFromTemplate(iprocId);
            });
    }

    async search(userId, query) {
        if (!query || typeof query !== "string" || query.length < 3) {
            throw new UserException(
                "Illegal query input. Query string must be at least 3 chars long.",
                "INVALID_SEARCH_QUERY",
            );
        }

        let conn = this.connection;

        // TODO: CI AI like?
        const likeSearch = `%${query.toUpperCase()}%`;

        // Map tvar columns
        const tvarEntity = globalThis.orm
            .repo("TemplateVariable", this.connection)
            .getEntity();
        let attrs = tvarEntity.attributes();
        attrs = _.omit(attrs, ["DLIST_NAME", "ORG_ID"]);
        let tvarCols = Object.keys(attrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const ivarEntity = this.getEntity();
        attrs = ivarEntity.attributes();
        attrs = _.omit(attrs, "TVAR_ID");
        let ivarCols = Object.keys(attrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        // Header translations
        const headerEntity = globalThis.orm
            .repo("Header", this.connection)
            .getEntity();
        attrs = headerEntity.attributes();
        const headerCols = [];
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                headerCols.push(`TH.${attrName}`);
            }
        });

        conn = conn
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(
                []
                    .concat(tvarCols)
                    .concat(ivarCols)
                    .concat(headerCols)
                    .concat([
                        "IP.IPROC_NAME",
                        "TH.HEADER_CODE",
                        "TH.HEADER_ID",
                        "TH.HEADER_NAME",
                    ]),
            )
            .from("INSTANCE_VARIABLES as IV")
            .leftJoin("TEMPLATE_VARIABLES as TV", "TV.TVAR_ID", "IV.TVAR_ID")
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IV.IPROC_ID")
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .where(function () {
                this.whereRaw(
                    `${globalThis.orm.db.upper(`"IV"."IVAR_TEXT_VALUE"`, false)} like ${globalThis.orm.db.upper("?")}`,
                    [likeSearch],
                );

                if (!isNaN(query)) {
                    this.orWhere("IV.IVAR_NUMBER_VALUE", "like", likeSearch);
                }

                // Date filtering or "string date filtering"?
                // @t3b-1177 Chyba v celoTasovém vyhledávání => Do not use 'new Date()' for validation, it is too robust
                const isValid = moment(query, "YYYY-MM-DD", true).isValid(); // Do not use 'moment.ISO_8601' because it will deem "YYYY-MM" valid
                if (!isValid) {
                    globalThis.tasLogger.error(
                        `Date '${query}' is not a valid date.`,
                    );
                    this.orWhere("IV.IVAR_DATE_VALUE", "like", likeSearch);
                } else {
                    const date = moment(query);
                    globalThis.tasLogger.info(
                        `Date is a valid date. Commencing search for ${date.year()}-${date.month() + 1}-${date.date()}`,
                    );
                    this.orWhereRaw(
                        `${globalThis.orm.db.trunc("IV.IVAR_DATE_VALUE", date)} = ${globalThis.orm.db.toDate()}`,
                        `${date.year()}-${date.month() + 1}-${date.date()}`,
                    );
                }

                // Users
                this.orWhere(function () {
                    this.whereIn(
                        "IV.IVAR_NUMBER_VALUE",
                        globalThis.database
                            .select("USER_ID")
                            .from("USERS")
                            .whereRaw(
                                `${globalThis.orm.db.upper("USER_NAME", true)} like ${globalThis.orm.db.upper("?")}`,
                                [likeSearch],
                            ),
                    ).where("IV.IVAR_ATTRIBUTE", "U");
                });

                // Organizations
                this.orWhere(function () {
                    this.whereIn(
                        "IV.IVAR_NUMBER_VALUE",
                        globalThis.database
                            .select("ORGSTR_ID")
                            .from("ORGANIZATION_STRUCTURE")
                            .whereRaw(
                                `${globalThis.orm.db.upper("ORGSTR_NAME", true)} like ${globalThis.orm.db.upper("?")}`,
                                [likeSearch],
                            ),
                    ).where("IV.IVAR_ATTRIBUTE", "O");
                });

                // Roles
                this.orWhere(function () {
                    this.whereIn(
                        "IV.IVAR_NUMBER_VALUE",
                        globalThis.database
                            .select("ROLE_ID")
                            .from("ROLES")
                            .whereRaw(
                                `${globalThis.orm.db.upper("ROLE_NAME", true)} like ${globalThis.orm.db.upper("?")}`,
                                [likeSearch],
                            ),
                    ).where("IV.IVAR_ATTRIBUTE", "R");
                });
            });

        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("Header")
                .applyExternalLoginRights(conn, userId);
        }

        conn = conn.whereIn("IV.IPROC_ID", function () {
            globalThis.orm.repo("externalRight", this).getUserProcesses(userId);
        });

        const coll = globalThis.orm.collection("Variable", conn);
        coll.filteringColumns = {};
        return coll;
    }

    getByTvarId(iprocId, tvarId) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("TVAR_ID", tvarId);
        return globalThis.orm
            .collection("Variable", conn)
            .collectAll()
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    throw new UserException(
                        `Variable with iproc_id=${iprocId} and tvar_id=${tvarId} not found.`,
                    );
                }
                return rows[0];
            });
    }

    getByTvarName(iprocId, tvarName) {
        const conn = this.connection
            .select()
            .from(`${this.tableName} as IV`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "TV.TVAR_ID", "IV.TVAR_ID")
            .where("IV.IPROC_ID", iprocId)
            .where("TV.TVAR_NAME", tvarName);
        return globalThis.orm
            .collection("Variable", conn)
            .collectAll()
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    throw new UserException(
                        `Variable with iproc_id=${iprocId} and tvar_name=${tvarName} not found.`,
                    );
                }
                return rows[0];
            });
    }

    getByDlistName(dlistName) {
        const conn = this.connection
            .select()
            .from(`${this.tableName} as IV`)
            .where("DLIST_NAME", dlistName);
        return globalThis.orm
            .collection("Variable", conn)
            .collectAll()
            .then((rows) => rows);
    }

    async updateVariablesDListName(oldDListName, newDListName) {
        return await this.connection
            .select()
            .from(`${this.tableName}`)
            .whereIn(
                "DLIST_NAME",
                Array.isArray(oldDListName) ? oldDListName : [oldDListName],
            )
            .update({ DLIST_NAME: newDListName });
    }

    getByActiveProcesses(tprocId) {
        const conn = this.connection
            .select("IV.*")
            .from(`${this.tableName} as IV`)
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IV.IPROC_ID")
            .where("IP.TPROC_ID", tprocId)
            .where("IP.IPROC_STATUS", "A");
        return globalThis.orm.collection("Variable", conn);
    }

    /**
     * Because of overwritten getCollection method
     * @returns {BaseCollection}
     */
    getCollectionForProcess(iprocId, columns, alias, extraColumns) {
        const coll = super.getCollection(columns, alias, extraColumns);
        coll.knex.where("IPROC_ID", iprocId);
        return coll;
    }

    /**
     * Check the rights to store variables
     * @param {number|string} iprocId
     * @param {number|string} itaskId
     * @param {array} ivarIds
     */
    async checkPostVariableRights(
        iprocId: number | string,
        itaskId: number | string,
        ivarIds: any[],
    ) {
        const varIds = _.map(ivarIds, (id) => parseInt(id, 10));
        const version = await globalThis.orm
            .repo("instanceProcessVersion")
            .getProcessVersion(iprocId);
        const usageVersion = version.TTASKVARUSG_VERSION || 0;

        if (usageVersion <= 0) {
            throw new InternalException(
                "Version must be defined. INSTANCE_TASK_VAR_USAGE is no longer used.",
            );
        }
        // template usage
        const connection = this.connection
            .select("IV.IVAR_ID")
            .from(`${this.tableName} as IV`)
            .join(
                "TEMPLATE_TASK_VAR_USAGE as TTVU",
                "TTVU.TVAR_ID",
                "IV.TVAR_ID",
            )
            .join("INSTANCE_TASKS as IT", {
                "IT.TTASK_ID": "TTVU.TTASK_ID",
                "IT.IPROC_ID": "IV.IPROC_ID",
            })
            .whereIn("IV.IVAR_ID", varIds)
            .whereIn("TTVU.TTASKVARUSG_USAGE", ["W", "M"])
            .where("IT.ITASK_STATUS", "A")
            .where("IT.ITASK_ID", itaskId);

        return await globalThis.orm
            .collection(this, connection)
            .fetchAll()
            .then(async (rows) => {
                const suspiciousVariableIds = _.difference(
                    varIds,
                    _.map(rows, "IVAR_ID"),
                );

                if (!_.isEmpty(suspiciousVariableIds)) {
                    globalThis.tasLogger.error(
                        "Suspicious request. An attempt was made to save a variable from another task/case or a read-only variable.",
                        {
                            iprocId,
                            itaskId,
                            ivarIds,
                            suspiciousIvarIds: suspiciousVariableIds,
                        },
                    );

                    throw new UserException(
                        "Insufficient permissions to store variable.",
                        "LACK_OF_PERMISSIONS",
                        {
                            suspiciousIvarIds: suspiciousVariableIds,
                        },
                    );
                }
            });
    }
}
