// @ts-nocheck
import { VariableSnap } from "../entity/VariableSnap";
import { BaseRepository } from "./BaseRepository";

export class VariableSnapRepository extends BaseRepository<VariableSnap> {
    meta() {
        return {
            tableName: "INSTANCE_VARIABLES_SNAP",
            entityName: "VariableSnap",
            entity: () => new VariableSnap(),
            archived: true,
            archParams: {
                subQueryTable: "INSTANCE_VARIABLES",
                subQueryColumn: "IVAR_ID",
            },
        };
    }

    /**
     * Get collection by IPROC_ID
     * @param {number} iprocId
     * @returns {*}
     */
    getByProcess(iprocId: number) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .leftJoin("INSTANCE_VARIABLES AS IV", "IV.IVAR_ID", "SNAP.IVAR_ID")
            .where("IPROC_ID", iprocId);
        return globalThis.orm.collection<"variableSnap">(this, conn);
    }

    /**
     * Get collection by ITASK_ID
     * @param {number} itaskId
     * @param {number} ivarId Ivar id of global variable if necessary
     * @returns {*}
     */
    getByTask(itaskId, ivarId = null) {
        const conn = this.connection
            // .select()
            .select(
                "SNAP.ORG_ID",
                "SNAP.IVARSN_ID",
                "SNAP.IVAR_ID",
                "SNAP.IPROC_ID",
                "SNAP.ITASK_ID",
                "SNAP.IVARSN_TEXT_VALUE",
                "SNAP.IVARSN_TEXT_VALUE_PREV",
                "SNAP.IVARSN_NUMBER_VALUE",
                "SNAP.IVARSN_NUMBER_VALUE_PREV",
                "SNAP.IVARSN_DATE_VALUE",
                "SNAP.IVARSN_DATE_VALUE_PREV",
                "SNAP.TVAR_ID",
                "SNAP.IVARSN_MULTI",
                "SNAP.IVARSN_MULTI_SELECTED",
                "SNAP.IVARSN_BIG_VALUE",
                "SNAP.IVAR_DT_INDEX",
                "SNAP.IVAR_COL_INDEX",

                "IV.IVAR_NAME",
                "IV.IVAR_TYPE",
                "IV.IVAR_MULTITASK_BEHAVIOUR",
                "IV.IVAR_TEXT_VALUE",
                "IV.IVAR_NUMBER_VALUE",
                "IV.IVAR_DATE_VALUE",
                "IV.IVAR_ATTRIBUTE",
                "IV.DLIST_NAME",
                "IV.IVAR_CLASS",
                "IV.IVAR_MULTI",
                "IV.IVAR_MULTI_SELECTED",
                "IV.IVAR_BIG_VALUE",
            )
            .from(`${this.tableName} AS SNAP`)
            .leftJoin("INSTANCE_VARIABLES AS IV", "IV.IVAR_ID", "SNAP.IVAR_ID")
            .where("SNAP.ITASK_ID", itaskId);

        if (ivarId) {
            conn.where("SNAP.IVAR_ID", ivarId);
        }
        return globalThis.orm.collection<"variableSnap">(this, conn);
    }

    async updateAllSnaps(variable, itaskId) {
        if (!itaskId) {
            globalThis.tasLogger.warning(
                "Can not update snapshots. ITASK_ID is null. Please review WorkflowEvent->updateAllSnaps.",
                {
                    variable,
                    itaskId,
                },
            );
        }

        const updateData = {};
        if (typeof variable.IVAR_MULTI_SELECTED !== "undefined") {
            updateData.IVARSN_MULTI_SELECTED = variable.IVAR_MULTI_SELECTED;
        }
        if (typeof variable.IVAR_BIG_VALUE !== "undefined") {
            updateData.IVARSN_BIG_VALUE = variable.IVAR_BIG_VALUE;
        }
        if (typeof variable.IVAR_TEXT_VALUE !== "undefined") {
            updateData.IVARSN_TEXT_VALUE = variable.IVAR_TEXT_VALUE;
        }
        if (typeof variable.IVAR_NUMBER_VALUE !== "undefined") {
            updateData.IVARSN_NUMBER_VALUE = variable.IVAR_NUMBER_VALUE;
        }
        if (typeof variable.IVAR_DATE_VALUE !== "undefined") {
            updateData.IVARSN_DATE_VALUE = variable.IVAR_DATE_VALUE;
        }

        if (variable.IVAR_TYPE === "DT") {
            updateData.IVAR_DT_INDEX = variable.IVAR_DT_INDEX;
            updateData.IVAR_COL_INDEX = variable.IVAR_COL_INDEX;
        }

        return await this.connection
            .select()
            .from(this.tableName)
            .where("IVAR_ID", variable.IVAR_ID)
            .where("ITASK_ID", itaskId)
            .update(updateData);
    }
}
