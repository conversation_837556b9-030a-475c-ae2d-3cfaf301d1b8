// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as ROL<PERSON> from "../entity/const/roleConst";
import { CompetenceRuleRole } from "../entity/CompetenceRuleRole";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";

export class CompetenceRuleRoleRepository extends BaseRepository<CompetenceRuleRole> {
    meta() {
        return {
            tableName: "COMPETENCE_RULE_ROLES",
            entityName: "CompetenceRuleRole",
            entity: () => new CompetenceRuleRole(),
        };
    }

    /**
     * Assign competence roles from competence rules.
     * @param competenceId
     * @param competenceRuleId
     * @returns {Promise<void>}
     */
    async copyDefaultsForCompetence(competenceId, competenceRuleId) {
        await this.connection
            .into(
                globalThis.database.raw(
                    `"COMPETENCE_ROLES" ("ROLE_ID", "COMPETENCE_ID", "COMPETENCE_RULE_ID")`,
                ),
            )
            .insert((builder) => {
                builder
                    .select(["ROLE_ID", competenceId, competenceRuleId])
                    .from(this.tableName)
                    .where("COMPETENCE_RULE_ID", competenceRuleId)
                    .whereNotIn("ROLE_ID", (b) => {
                        b.select("ROLE_ID")
                            .from("COMPETENCE_ROLES")
                            .where("COMPETENCE_ID", competenceId);
                    });
            });
    }

    async addRoles(roleIds = [], competenceRuleId) {
        // Check for system Roles
        const includesSystemRole = roleIds.some(
            (roleId) => roleId < 0 && roleId !== ROLES.ALL_USERS,
        );

        // Do not allow system Roles
        if (includesSystemRole) {
            throw new UserException(
                "Competence cannot include a system Role!",
                "NOT_ALLOWED",
                {
                    roleIds,
                    competenceRuleId,
                },
            );
        }

        const competenceIds = await this.connection
            .pluck("COMPETENCE_ID")
            .from("COMPETENCES")
            .where("COMPETENCE_RULE_ID", competenceRuleId);
        const competenceRoles = [];
        for (const competenceId of competenceIds) {
            await globalThis.orm
                .repo("competence", this.connection)
                .markForRebuild(competenceId);
            for (const roleId of roleIds) {
                competenceRoles.push({
                    COMPETENCE_ID: competenceId,
                    COMPETENCE_RULE_ID: competenceRuleId,
                    ROLE_ID: roleId,
                });
            }
        }
        await globalThis.database
            .batchInsert("COMPETENCE_ROLES", competenceRoles, 10)
            .transacting(this.connection);

        // Add Roles
        if (roleIds.length) {
            await this.connection.into(this.tableName).insert(
                roleIds.map((roleId) => ({
                    COMPETENCE_RULE_ID: competenceRuleId,
                    ROLE_ID: roleId,
                })),
            );
        }
    }

    /**
     *
     * @param {Array<number>} roleIds
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async removeRoles(roleIds = [], competenceRuleId) {
        // Delete all Roles in the list
        await this.connection
            .from("COMPETENCE_ROLES")
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .whereIn("ROLE_ID", Array.isArray(roleIds) ? roleIds : [roleIds])
            .del();

        // Delete all Roles in the list
        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .whereIn("ROLE_ID", Array.isArray(roleIds) ? roleIds : [roleIds])
            .del();
    }

    /**
     *
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async deleteRoles(competenceRuleId) {
        const competenceIds = await this.connection
            .pluck("COMPETENCE_ID")
            .from("COMPETENCES")
            .where("COMPETENCE_RULE_ID", competenceRuleId);
        for (const competenceId of competenceIds) {
            await globalThis.orm
                .repo("competenceRole", this.connection)
                .deleteRoles(competenceId);
            await globalThis.orm
                .repo("competence", this.connection)
                .markForRebuild(competenceId);
        }

        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .del();
    }

    /**
     *
     * @param {Array<number>} roleIds
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async setRoles(roleIds: number[] = [], competenceRuleId: number) {
        // Delete all current roles
        await this.deleteRoles(competenceRuleId);
        // Add new roles
        await this.addRoles(roleIds, competenceRuleId);
    }

    async getForCompetence(competenceRuleId) {
        return await this.connection
            .select(["CR.ROLE_ID", "R.ROLE_NAME"])
            .from(`${this.tableName} as CR`)
            .innerJoin("ROLES as R", "R.ROLE_ID", "CR.ROLE_ID")
            .where("CR.COMPETENCE_RULE_ID", competenceRuleId);
    }

    /**
     *
     * @param {number} competenceRuleId
     * @returns {Promise<*>}
     */
    async getRoles(competenceRuleId) {
        return await this.connection
            .pluck("ROLE_ID")
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId);
    }
}
