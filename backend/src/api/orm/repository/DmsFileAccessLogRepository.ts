import { DmsFileAccessLog } from "../entity/DmsFileAccessLog";
import { BaseRepository } from "./BaseRepository";

export class DmsFileAccessLogRepository extends BaseRepository<DmsFileAccessLog> {
    meta() {
        return {
            tableName: "DMS_FILE_ACCESS_LOG",
            entityName: "DmsFileAccessLog",
            entity: () => new DmsFileAccessLog(),
            archived: true,
            archParams: {
                subQueryTable: "DMS_FILE",
                subQueryColumn: "DMSF_ID",
            },
        };
    }

    getAll() {
        const conn = this.connection
            .select([
                "DMSAL.*",
                "IP.IPROC_NAME",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
                "IT.ITASK_NAME",
                "DF.NAME",
            ])
            .from(`${this.tableName} as DMSAL`)
            .leftJoin(
                "INSTANCE_PROCESSES as IP",
                "DMSAL.IPROC_ID",
                "IP.IPROC_ID",
            )
            .leftJoin("USERS as U", "DMSAL.USER_ID", "U.USER_ID")
            .leftJoin("INSTANCE_TASKS as IT", "DMSAL.ITASK_ID", "IT.ITASK_ID")
            .leftJoin("DMS_FILE as DF", "DMSAL.DMSF_ID", "DF.DMSF_ID");

        return globalThis.orm.collection("dmsFileAccessLog", conn);
    }

    removeOld(thresholdDate: string, deleteLimit: number) {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("DMSFAL_ID", (builder) => {
                builder
                    .select("DMSFAL_ID")
                    .from(this.tableName)
                    .whereRaw(
                        `DATE_TIME < ${globalThis.orm.db.toDate()}`,
                        thresholdDate,
                    )
                    .orderBy("DATE_TIME")
                    .limit(deleteLimit);
            })
            .delete();
    }
}
