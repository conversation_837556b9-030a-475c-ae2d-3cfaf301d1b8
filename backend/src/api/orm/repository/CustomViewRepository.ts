// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import moment from "moment-timezone";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { Filter } from "../Filter";
import { Rest } from "../../services/Rest";
import * as CustomViewConsts from "../entity/const/customViewConsts";
import * as ROLES from "../entity/const/roleConst";
import { UtilsService } from "../../services/UtilsService";
import * as tprocConsts from "../entity/const/tprocConsts";
import * as VARIABLE from "../entity/const/variableConst";
import { CustomView } from "../entity/CustomView";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { IllegalArgumentException } from "../../../utils/errorHandling/exceptions/illegalArgumentException";
import { FastifyRequest } from "fastify";

const DATEPARTS = ["YEAR", "MONTH", "DAY", "WEEK", "WEEKDAY"];
const SYSTEM_COLUMNS = {
    "V-0": {
        columnName: "IP.IPROC_ID",
        columnType: "N",
        columnAlias: "IPROC_ID",
    },
    "V-1": {
        columnName: "IP.IPROC_NAME",
        columnType: "T",
        columnAlias: "iproc_name",
    },
    "V-2": {
        columnName: "IP.IPROC_DESCRIPTION",
        columnType: "T",
        columnAlias: "iproc_description",
    },
    "V-3": {
        columnName: "IP.IPROC_INST_OWNER_USER_ID",
        columnType: "U",
        columnAlias: "iproc_inst_owner_user",
    },
    "V-4": {
        columnName: "IP.IPROC_ACTUAL_START_DATE",
        columnType: "D",
        columnAlias: "iproc_actual_start_date",
    },
    "V-5": {
        columnName: "IP.IPROC_DUE_DATE_FINISH",
        columnType: "D",
        columnAlias: "iproc_due_date_finish",
    },
    "V-6": {
        columnName: "IP.IPROC_PRIORITY",
        columnType: "T",
        columnAlias: "iproc_priority",
    },
    "V-7": {
        columnName: "IP.IPROC_SUMMARY",
        columnType: "T",
        columnAlias: "iproc_summary",
    },
    "V-8": {
        columnName: "IP.IPROC_ACTUAL_FINISH_DATE",
        columnType: "D",
        columnAlias: "iproc_actual_finish_date",
    },
    "V-9": {
        columnName: "IP.IPROC_CASE_STATUS",
        columnType: "T",
        columnAlias: "iproc_case_status",
    },
};

export class CustomViewRepository extends BaseRepository<CustomView> {
    req: FastifyRequest;

    meta() {
        return {
            tableName: "CUSTOM_VIEWS",
            entityName: "CustomView",
            entity: () => new CustomView(),
        };
    }

    async getExtended(cvId, userId, roleIds, orgstrIds) {
        // Prepare mutations
        const extraTvarAttrsWithAlias = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity()
            .getTranslatedProperties([], "TV");
        const extraCvAttrsWithAlias = globalThis.orm
            .repo("customView", this.connection)
            .getEntity()
            .getTranslatedProperties([], "CV");
        const extraTprocAttrsWithAlias = globalThis.orm
            .repo("templateProcess", this.connection)
            .getEntity()
            .getTranslatedProperties(["TPROC_DEFAULT_CASE_NAME"], "TP");
        const extraHeaderAttrsWithAlias = globalThis.orm
            .repo("header", this.connection)
            .getEntity()
            .getTranslatedProperties([], "TH");

        const extraTvarAttrs = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity()
            .getTranslatedProperties();
        const extraCvAttrs = globalThis.orm
            .repo("customView", this.connection)
            .getEntity()
            .getTranslatedProperties();
        const extraTprocAttrs = globalThis.orm
            .repo("templateProcess", this.connection)
            .getEntity()
            .getTranslatedProperties(["TPROC_DEFAULT_CASE_NAME"]);
        const extraHeaderAttrs = globalThis.orm
            .repo("header", this.connection)
            .getEntity()
            .getTranslatedProperties();

        const extraColumns = [].concat(
            extraTvarAttrsWithAlias,
            extraCvAttrsWithAlias,
            extraTprocAttrsWithAlias,
            extraHeaderAttrsWithAlias,
        );

        const columns = [
            "CV.CV_ID",
            "CV.CV_SORT",
            "CV.CV_ORDER",
            "CV.ORG_ID",
            "CV.IGNORE_PROCESS_RIGHTS",
            "CV_NAME",
            "CV_FILTER",
            "CV.CV_TABLE_SOURCE",
            "INCLUDE_SIMILAR_PROCESSES",
            globalThis.database.raw(
                `"U"."USER_DISPLAY_NAME" as "CV_OWNER_NAME"`,
            ),
            "CV_USER_ID",
            "CV.TPROC_ID",
            "TP.TPROC_NAME",
            "CV.CV_IPROC_STATUS_LIST",
            "CVS.TVAR_ID",
            "CVS.CVC_NAME",
            "CVS.CVC_DB_COLUMN",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
            "TH.HEADER_ENABLED",
            "TH.HEADER_CODE",
            "TV.TVAR_TYPE",
            "TV.TVAR_NAME",
            "TV.TVAR_META",
            "TV.TVAR_ATTRIBUTE",
            "TV.TVAR_MULTI",
            "TV.TVAR_DATE_WITHOUT_TIME",
        ].concat(extraColumns);

        const data = await this.connection
            .select(columns)
            .from("CUSTOM_VIEWS as CV")
            .leftJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "CV.TPROC_ID")
            .leftJoin("HEADERS as TH", "CV.HEADER_ID", "TH.HEADER_ID")
            .leftJoin("CUSTOM_VIEW_COLUMNS as CVS", "CV.CV_ID", "CVS.CV_ID")
            .leftJoin("TEMPLATE_VARIABLES as TV", "TV.TVAR_ID", "CVS.TVAR_ID")
            .leftJoin("USERS as U", "CV.CV_USER_ID", "U.USER_ID")
            .where("CV.CV_IS_DELETED", CustomViewConsts.IS_NOT_DELETED)
            .where("CV.CV_ID", cvId)
            .where("TP.TPROC_VERSION", tprocConsts.TPROC_DEFAULT_VERSION)
            .whereNotNull("TP.TPROC_ID")
            .whereIn("CV.CV_ID", (builder) => {
                builder
                    .select("CV_ID")
                    .from(
                        globalThis.orm
                            .repo("customView", this.connection)
                            .getMine(userId, roleIds, orgstrIds)
                            .as("ignored_alias"),
                    );
            })
            .orderBy("CV.CV_NAME")
            .orderBy("CVS.CVC_ID");

        try {
            await globalThis.orm.repo("customView", this.connection).get(cvId);
        } catch (_e) {
            throw new UserException(
                "Object does not exist!",
                "OBJECT_NOT_FOUND",
            );
        }

        if (!data.length) {
            throw new UserException(
                "Lack of permissions!",
                "LACK_OF_PERMISSIONS",
            );
        }
        const castedData = await globalThis.orm
            .repo("customView", this.connection)
            .castRows(
                data,
                _.assign(
                    { TVAR_META: { type: "text" } },
                    globalThis.orm
                        .repo("customView", this.connection)
                        .entity.getAttributes(false, true),
                ),
            );

        const cvsRepo = globalThis.orm.repo("customViewShare", this.connection);
        const result = await cvsRepo.fillShares(castedData);
        const out = [];
        const ids = {};
        const dataModels = { models: result };

        dataModels.models.forEach((cv) => {
            if (!ids[cv.CV_ID]) {
                const ncv = {
                    ...cv,
                    CV_FILTER: cv.CV_FILTER ? JSON.parse(cv.CV_FILTER) : null,
                    COLUMNS: [],
                };

                extraCvAttrs.forEach((attrName) => {
                    ncv[attrName] = cv[attrName];
                });

                extraHeaderAttrs.forEach((attrName) => {
                    ncv[attrName] = cv[attrName];
                });

                extraTprocAttrs.forEach((attrName) => {
                    ncv[attrName] = cv[attrName];
                });

                out.push(ncv);
                ids[cv.CV_ID] = ncv;
            }

            if (cv.TVAR_ID) {
                const column = {
                    tvar_id: cv.TVAR_ID,
                    tvar_name: cv.TVAR_NAME,
                    cvc_name: cv.CVC_NAME,
                    tvar_attribute: cv.TVAR_ATTRIBUTE,
                    tvar_multi: cv.TVAR_MULTI,
                    tvar_type: cv.TVAR_TYPE,
                    tvar_meta: cv.TVAR_META,
                    cvc_db_column: cv.CVC_DB_COLUMN,
                    tvar_date_without_time: cv.TVAR_DATE_WITHOUT_TIME,
                };

                extraTvarAttrs.forEach((attrName) => {
                    column[attrName.toLowerCase()] = cv[attrName];
                });

                ids[cv.CV_ID].COLUMNS.push(column);
            }
        });
        dataModels.totalCount = Object.keys(ids).length
            ? Object.keys(ids).length
            : 0;
        dataModels.models = out;

        return dataModels;
    }

    getMine(userId, _roleIds, orgstrIds) {
        // Translations
        const extraColumns = [];
        const entity = this.getEntity();
        const attrs = entity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`CV.${attrName}`);
            }
        });
        const columns = [
            "CV.CV_ID",
            "CV.CV_NAME",
            "CV.TPROC_ID",
            globalThis.database.raw(`"U"."USER_NAME" as "CV_OWNER_USER_NAME"`),
        ].concat(extraColumns);

        return this.connection
            .distinct(columns)
            .select()
            .from("CUSTOM_VIEWS as CV")
            .leftJoin("USERS as U", "CV.CV_USER_ID", "U.USER_ID")
            .leftJoin("CUSTOM_VIEW_SHARE as CVS", "CVS.CVS_CV_ID", "CV.CV_ID")
            .where("CV.CV_IS_DELETED", CustomViewConsts.IS_NOT_DELETED)
            .where((builder) => {
                builder
                    .where("CVS.CVS_USER_ID", userId)
                    .orWhere("CV_USER_ID", userId)
                    .orWhereIn(
                        "CVS.CVS_OS_ID",
                        globalThis.database.raw(orgstrIds),
                    )
                    .orWhereIn("CVS.CVS_ROLE_ID", (builder) => {
                        builder
                            .select("UR.ROLE_ID")
                            .from("USER_ROLES as UR")
                            .where("UR.USER_ID", userId);
                    });

                /*
                @t3b-1491 Uzivatel si nemuze zobrazit seznam prehledu kvuli vysokemu poctu roli

                Prevent: ORA-01795: maximum number of expressions in a list is 1000
                const chunked = _.chunk(roleIds, 999);
                chunked.forEach(chunk => {
                    builder.orWhereIn('CVS.CVS_ROLE_ID', globalThis.database.raw(chunk));
                }); */
            })
            .orderBy("CV.CV_NAME")
            .orderBy("CV.CV_ID")
            .limit(Number.MAX_SAFE_INTEGER);
    }

    async getList(
        cvId,
        userId,
        filter,
        order,
        sort,
        offset,
        limit,
        countRows,
        onlyCount,
    ) {
        const start = new Date();

        try {
            const [data, totalCount, columns] = await this.getCvProcessList(
                cvId,
                userId,
                filter,
                order,
                sort,
                offset,
                limit,
                countRows,
            );

            const modifiedData = !onlyCount
                ? await this.applyPrecision(data)
                : [];

            const columnsMap = columns.reduce(
                (obj, item) => ({
                    ...obj,
                    [`v${item.TVAR_ID}`]: item.TVAR_NAME,
                }),
                {},
            );

            const typesMap = columns.reduce(
                (obj, item) => ({
                    ...obj,
                    [`v${item.TVAR_ID}`]: item.TVAR_TYPE,
                }),
                {},
            );

            const metaMap = columns.reduce(
                (obj, item) => ({
                    ...obj,
                    [`v${item.TVAR_ID}`]: item.TVAR_META,
                }),
                {},
            );

            return {
                ...Rest.restifyData(modifiedData, {
                    primaryCol: "IPROC_ID",
                    single: false,
                    totalCount,
                    startTime: start,
                }),
                columns: columnsMap,
                columnTypes: typesMap,
                columnMeta: metaMap,
            };
        } catch (err) {
            throw err;
        }
    }

    getListWithTasks(
        cvId,
        userId,
        filter,
        order,
        sort,
        offset,
        limit,
        countRows,
        onlyCount,
    ) {
        const start = new Date();
        return this.getCvProcessList(
            cvId,
            userId,
            filter,
            order,
            sort,
            offset,
            limit,
            countRows,
            true,
        )
            .then((result) => Promise.all(result))
            .then(([data, totalCount]) => {
                if (onlyCount) {
                    return [[], totalCount];
                }

                return this.applyPrecision(data).then((modifiedData) => [
                    modifiedData,
                    totalCount,
                ]);
            })
            .then((result) => Promise.all(result))
            .then(([data, totalCount]) =>
                Rest.restifyData(data, {
                    primaryCol: "ITASK_ID",
                    single: false,
                    totalCount,
                    startTime: start,
                }),
            )
            .catch((err) => {
                throw err;
            });
    }

    /**
     * Round every variable with defined precision.
     * @param data
     */
    applyPrecision(data) {
        // Are there any data to ?
        if (
            !Array.isArray(data) ||
            data.length === 0 ||
            Object.keys(data[0]).length === 0
        ) {
            return data;
        }

        const keys = Object.keys(data[0]);
        const tvars = [];

        // Map tvar ids
        keys.forEach((key) => {
            // Ignore mutations ie. V156_CS
            if (
                key.startsWith("V") &&
                UtilsService.isNumericString(key.substring(1))
            ) {
                const tvarId = key.substr(1, key.length);
                tvars.push(tvarId);
            }
        });

        if (tvars.length === 0) {
            return data;
        }

        // Read tvar metas
        return globalThis.orm
            .collection(
                "TemplateVariable",
                this.connection
                    .select("TVAR_META", "TVAR_ID")
                    .from("TEMPLATE_VARIABLES")
                    .whereIn("TVAR_ID", Array.isArray(tvars) ? tvars : [tvars])
                    .whereNotNull("TVAR_META"),
            )
            .collectAll()
            .then((rows) => {
                // PArse precisions from json
                const precisions = {};
                rows.forEach((row) => {
                    try {
                        const parsedMeta = JSON.parse(row.TVAR_META);
                        if (
                            typeof parsedMeta.numberOfDecimals !== "undefined"
                        ) {
                            precisions[`V${row.TVAR_ID}`] =
                                parsedMeta.numberOfDecimals;
                        }
                    } catch (_ignored) {
                        // ignore
                    }
                });

                const precisionVaraibleKeys = Object.keys(precisions);
                if (precisionVaraibleKeys.length === 0) {
                    return data;
                }

                // Apply precision to all rows
                precisionVaraibleKeys.forEach((varKey) => {
                    data.forEach((row) => {
                        row[varKey] =
                            row[varKey] !== null
                                ? parseFloat(
                                      UtilsService.roundNumber(
                                          row[varKey],
                                          precisions[varKey],
                                      ),
                                  )
                                : row[varKey];
                    });
                });
                return data;
            });
    }

    /**
     *
     * @param cvId
     * @param userId
     * @param filter
     * @param order
     * @param sort
     * @param offset
     * @param limit
     * @param countRows
     * @param withTasks Fill processes with tasks
     * @param aggregation count,max,min,sum,avg
     * @param aggregationColumn column to do aggregation with
     * @param aggregationFilter Array<> All filter to apply
     * @param groupColumns columns to group by
     * @return {*}
     */
    async getCvProcessList(
        cvId,
        userId,
        filter,
        order,
        sort,
        offset,
        limit,
        countRows,
        withTasks = false,
        aggregation?,
        aggregationColumn = "*",
        aggregationFilter: any = [],
        groupColumns = [],
    ) {
        const self = this;

        const columns = await this.getColumns(cvId);
        // Assign custom view columns.
        const cols = [];
        const usedMultiVars = {};
        const usedDRVars = {};
        const dtMultiVars = [];
        const ldMultiVars = [];

        // Assign user columns. (selected in CustomView GUI)
        let externalSource = null;
        columns.forEach((cvColumn) => {
            if (cvColumn.CVID == null || cvColumn.CVID < 0) {
                return;
            }
            externalSource = cvColumn.CV_TABLE_SOURCE;

            if (!cvColumn.TVAR_ALIAS) {
                throw new UserException("TVAR_ALIAS is incorrect!");
            }

            const varType = `${cvColumn.TVAR_TYPE}.${
                cvColumn.TVAR_ATTRIBUTE ? cvColumn.TVAR_ATTRIBUTE : ""
            }`;
            let type = "IVAR_TEXT_VALUE";
            let subselect = false;

            switch (varType) {
                case "LD.":
                    if (cvColumn.TVAR_MULTI === "X") {
                        ldMultiVars.push(`V${cvColumn.TVAR_ID}`);
                    }
                    break;
                case "N":
                case "LN":
                case "N.S":
                    type = "IVAR_NUMBER_VALUE";
                    break;
                case "DL.U":
                    if (cvColumn.TVAR_MULTI == "X") {
                        usedMultiVars[`V${cvColumn.TVAR_ID}`] = {
                            TVAR_ATTRIBUTE: cvColumn.TVAR_ATTRIBUTE,
                        };

                        type = "IVAR_MULTI_SELECTED";
                    } else {
                        type = `(select "USER_DISPLAY_NAME" from "USERS" where "USER_ID" = "IPV"."${cvColumn.TVAR_ALIAS}")`;
                        subselect = true;
                    }
                    break;
                case "DL.R":
                    if (cvColumn.TVAR_MULTI == "X") {
                        usedMultiVars[`V${cvColumn.TVAR_ID}`] = {
                            TVAR_ATTRIBUTE: cvColumn.TVAR_ATTRIBUTE,
                        };
                        type = "IVAR_MULTI_SELECTED";
                    } else {
                        type = `(select "ROLE_NAME" from "ROLES" where "ROLE_ID" = "IPV"."${cvColumn.TVAR_ALIAS}")`;
                        subselect = true;
                    }
                    break;
                case "DL.O":
                    if (cvColumn.TVAR_MULTI == "X") {
                        usedMultiVars[`V${cvColumn.TVAR_ID}`] = {
                            TVAR_ATTRIBUTE: cvColumn.TVAR_ATTRIBUTE,
                        };
                        type = "IVAR_MULTI_SELECTED";
                    } else {
                        type = `(select "ORGSTR_NAME" from "ORGANIZATION_STRUCTURE" where "ORGSTR_ID" = "IPV"."${cvColumn.TVAR_ALIAS}")`;
                        subselect = true;
                    }
                    break;
                case "DR.":
                    usedDRVars[`V${cvColumn.TVAR_ID}`] = {
                        TVAR_ATTRIBUTE: cvColumn.TVAR_ATTRIBUTE,
                    };
                    break;
                case "DT.":
                    if (cvColumn.TVAR_MULTI === "X") {
                        dtMultiVars.push(`V${cvColumn.TVAR_ID}`);
                    }
                    break;
                default:
                    break;
            }

            if (subselect) {
                cols.push(
                    globalThis.database.raw(
                        `${type} as "V${cvColumn.TVAR_ID}"`,
                    ),
                );
            } else {
                cols.push(
                    globalThis.database.raw(
                        `"IPV"."${cvColumn.TVAR_ALIAS}" as "V${cvColumn.TVAR_ID}"`,
                    ),
                );
            }
        });

        if (withTasks) {
            cols.push(globalThis.database.raw(`"IT"."ITASK_ID" AS "ITASK_ID"`));
            cols.push(
                globalThis.database.raw(`"IT"."ITASK_NAME" AS "ITASK_NAME"`),
            );
        }
        // Assign system columns.
        cols.push(
            globalThis.database.raw(
                `"IP"."IPROC_CASE_STATUS" AS "IPROC_CASE_STATUS"`,
            ),
            globalThis.database.raw(`"IP"."IPROC_NAME" AS "iproc_name"`),
            globalThis.database.raw(
                `"IP"."IPROC_DESCRIPTION" AS "iproc_description"`,
            ),
            globalThis.database.raw(
                `"UIP"."USER_DISPLAY_NAME" AS "iproc_inst_owner_user"`,
            ),
            globalThis.database.raw(
                `"IP"."IPROC_ACTUAL_START_DATE" AS "iproc_actual_start_date"`,
            ),
            globalThis.database.raw(
                `"IP"."IPROC_DUE_DATE_FINISH" AS "iproc_due_date_finish"`,
            ),
            globalThis.database.raw(
                `"IP"."IPROC_PRIORITY" AS "iproc_priority"`,
            ),
            globalThis.database.raw(`"IP"."IPROC_SUMMARY" AS "iproc_summary"`),
            globalThis.database.raw(
                `"IP"."IPROC_ACTUAL_FINISH_DATE" AS "iproc_actual_finish_date"`,
            ),
            globalThis.database.raw(`"IP"."IPROC_ID"`),
        );

        // CASE_STATUS translations
        const caseStatusEntity = globalThis.orm
            .repo("caseStatus", this.connection)
            .getEntity();
        cols.push(...caseStatusEntity.getTranslatedProperties([], "CS"));

        // IPROC_SUMMARY translations
        const instanceProcessesEntity = globalThis.orm
            .repo("process", this.connection)
            .getEntity();
        cols.push(...instanceProcessesEntity.getTranslatedProperties([], "IP"));

        const cvItem = await this.get(cvId);
        const status: "A" | "D" | "AD" =
            cvItem.CV_IPROC_STATUS_LIST === "ARCH"
                ? "AD"
                : cvItem.CV_IPROC_STATUS_LIST || "AD";
        const isArchived = cvItem.CV_IPROC_STATUS_LIST === "ARCH";

        let conn = this.connection
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(cols)
            .from("CUSTOM_VIEWS as CV")
            .leftJoin(
                `${isArchived ? "ARCH_" : ""}INSTANCE_PROCESSES as IP`,
                function () {
                    if (cvItem.TPROC_ID_EXTRAS) {
                        this.on("IP.TPROC_ID", "CV.TPROC_ID").orOnIn(
                            "IP.TPROC_ID",
                            JSON.parse(cvItem.TPROC_ID_EXTRAS),
                        );
                    } else {
                        this.on("IP.TPROC_ID", "CV.TPROC_ID");
                    }
                },
            )
            .leftJoin("HEADERS as TH", "TH.HEADER_ID", "IP.HEADER_ID");

        if (externalSource) {
            conn = conn.leftJoin(
                `${externalSource} as IPV`,
                "IP.IPROC_ID",
                "IPV.IPROC_ID",
            );
        } else {
            conn = conn.leftJoin(
                "INSTANCE_PROCESS_VARIABLES as IPV",
                "IP.IPROC_ID",
                "IPV.IPROC_ID",
            );
        }
        conn = conn
            .leftJoin(
                "USERS as UIP",
                "UIP.USER_ID",
                "IP.IPROC_INST_OWNER_USER_ID",
            )
            .leftJoin("CASE_STATUSES as CS", function () {
                this.on("CS.TPROC_ID", "=", "IP.TPROC_ID").andOn(
                    "CS.CS_NAME",
                    "=",
                    "IP.IPROC_CASE_STATUS",
                );
            });

        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("header")
                .applyExternalLoginRights(conn, userId);
        }

        if (withTasks) {
            conn.leftJoin(
                `${isArchived ? "ARCH_" : ""}INSTANCE_TASKS as IT`,
                "IT.IPROC_ID",
                "IP.IPROC_ID",
            );
            globalThis.orm
                .repo("instanceTask", this.connection)
                .applyActiveTasks(conn, userId);
        }

        // Apply header filter
        if (cvItem.HEADER_ID && cvItem.INCLUDE_SIMILAR_PROCESSES === "N") {
            if (cvItem.TPROC_ID_EXTRAS) {
                conn.where(function () {
                    this.where("IP.HEADER_ID", cvItem.HEADER_ID).orWhereIn(
                        "IP.HEADER_ID",
                        function () {
                            this.select("EXTRA.HEADER_ID")
                                .from("HEADERS as EXTRA")
                                .whereIn(
                                    "EXTRA.TPROC_ID",
                                    JSON.parse(cvItem.TPROC_ID_EXTRAS),
                                )
                                .where("EXTRA.HEADER_ENABLED", "Y");

                            if (cvItem.HEADER_ID_EXTRAS) {
                                this.whereIn(
                                    "EXTRA.HEADER_ID",
                                    JSON.parse(cvItem.HEADER_ID_EXTRAS),
                                );
                            }
                        },
                    );
                });
            } else {
                conn.where("IP.HEADER_ID", cvItem.HEADER_ID);
            }
        }

        // Get user roles
        const isInspector = await globalThis.orm
            .repo("user", this.connection)
            .hasRole(userId, ROLES.INSPECTOR);
        const isGlobalSupervisor = await globalThis.orm
            .repo("user", this.connection)
            .hasRole(userId, ROLES.GLOBAL_SUPERVISOR);
        // @tas-548 prehled s omezenym vyhodnocenim prav
        const ignoreProcessRights = cvItem.IGNORE_PROCESS_RIGHTS;
        const returnAllProcesses =
            isInspector || isGlobalSupervisor || ignoreProcessRights;

        // There is not need to include IDs list if the User should be able to see all of them
        if (!returnAllProcesses) {
            // Get a list of the User Processes and query them
            conn.whereIn(
                "IP.IPROC_ID",
                globalThis.orm
                    .repo("externalRight", this.connection)
                    .getUserProcesses(userId, false, false, isArchived),
            );
        }

        // iproc status
        conn.where("CV.CV_ID", cvId).whereIn(
            "IP.IPROC_STATUS",
            status.split(""),
        );

        let defaultFilter;
        const cvFilter = cvItem.CV_FILTER;

        if (cvFilter) {
            defaultFilter = JSON.parse(cvFilter);
            if (!_.isEmpty(aggregationFilter)) {
                defaultFilter = [
                    { op: "(", enabled: true },
                    ...defaultFilter,
                    { op: ")", enabled: true },
                    { op: "and", enabled: true },
                    { op: "(", enabled: true },
                    ...aggregationFilter,
                    { op: ")", enabled: true },
                ];
            }
        }

        if (defaultFilter) {
            const defaultFilters = self.findDefaultFilter(defaultFilter);
            let defaultRawFilter: string | null = null;
            let conjunction = "<and>";

            if (defaultFilters && Array.isArray(defaultFilters)) {
                for (const filter of defaultFilters) {
                    if (filter.operator === "and" || filter.operator === "or") {
                        conjunction = "";
                        const op = `<${filter.operator}>`;
                        defaultRawFilter = defaultRawFilter
                            ? defaultRawFilter + op
                            : op;
                        continue;
                    }

                    if (filter.operator === "(" || filter.operator === ")") {
                        defaultRawFilter = defaultRawFilter
                            ? defaultRawFilter + filter.operator
                            : filter.operator;
                        continue;
                    }

                    const piece = `${filter.column}<${filter.operator}>"${filter.value}"`;

                    if (["(", "((", "((("].includes(defaultRawFilter || "")) {
                        conjunction = "";
                    }

                    defaultRawFilter = defaultRawFilter
                        ? defaultRawFilter + conjunction + piece
                        : piece;

                    conjunction = "<and>";
                }
            }

            if (defaultRawFilter) {
                const result = await self.processRawFilter(
                    conn,
                    defaultRawFilter,
                    columns,
                );
                conn = result.conn;
            }
        }

        const result = await self.processRawFilter(conn, filter, columns);
        conn = result.conn;

        await self.processSort(order, sort, columns, conn);

        let defaultOrder = cvItem.CV_ORDER;
        const defaultSort = cvItem.CV_SORT;

        const filterOrderFirstColumn = order?.split(",")[0];

        if (
            defaultOrder &&
            defaultOrder !== filterOrderFirstColumn &&
            `v${defaultOrder}` !== filterOrderFirstColumn
        ) {
            if (
                !defaultOrder.match(/V-/) &&
                defaultOrder.toUpperCase().startsWith("V")
            ) {
                defaultOrder = defaultOrder.substring(1);
            }

            const columnsToSkip = order ? order.split(",") : [];
            await self.processSort(
                defaultOrder,
                defaultSort,
                columns,
                conn,
                columnsToSkip,
            );
        }

        if (
            !order &&
            !defaultOrder &&
            globalThis.dynamicConfig.db.client === "mssql"
        ) {
            conn.orderByRaw("(select null)");
        }

        if (globalThis.dynamicConfig.db.client === "mssql" && aggregation) {
            conn.limit(Number.MAX_SAFE_INTEGER);
        }

        const totalCount = countRows
            ? await globalThis.orm.collection("Process", conn).getTotalCount()
            : null;

        if (aggregation) {
            let aggrConn = this.connection.queryBuilder();

            if (groupColumns) {
                for (const gCol of groupColumns) {
                    const columnName =
                        gCol.tvar_id < 0
                            ? SYSTEM_COLUMNS[`V${gCol.tvar_id}`].columnAlias
                            : `V${gCol.tvar_id}`;

                    if (
                        _.has(gCol, "datepart") &&
                        DATEPARTS.includes(gCol.datepart)
                    ) {
                        aggrConn = aggrConn.select(
                            globalThis.database.raw(
                                ...globalThis.orm.db.datepart(
                                    gCol.datepart,
                                    columnName,
                                    `"V${gCol.tvar_id}${gCol.datepart}"`,
                                ),
                            ),
                        );
                    } else {
                        aggrConn = aggrConn.select(columnName);
                    }
                }
            }

            switch (aggregation) {
                case "count":
                    aggrConn = aggrConn.count("*", { as: "aggr" });
                    break;
                case "sum":
                case "avg":
                case "max":
                case "min":
                    aggrConn = aggrConn.select(
                        globalThis.database.raw(
                            globalThis.orm.db.statistical(aggregation, 2),
                            [`V${aggregationColumn}`, "aggr"],
                        ),
                    );
                    break;
                default:
                    throw new Error("Unknown type of aggregation.");
            }

            if (groupColumns) {
                const orderBy = [];

                for (const gCol of groupColumns) {
                    const columnName =
                        gCol.tvar_id < 0
                            ? SYSTEM_COLUMNS[`V${gCol.tvar_id}`].columnAlias
                            : `V${gCol.tvar_id}`;

                    if (
                        _.has(gCol, "datepart") &&
                        DATEPARTS.includes(gCol.datepart)
                    ) {
                        aggrConn = aggrConn.groupByRaw(
                            ...globalThis.orm.db.datepart(
                                gCol.datepart,
                                columnName,
                            ),
                        );
                        orderBy.push({
                            column: `V${gCol.tvar_id}${gCol.datepart}`,
                            order: "asc",
                        });
                    } else {
                        aggrConn = aggrConn.groupBy(columnName);
                        orderBy.push({ column: columnName, order: "asc" });
                    }
                }

                if (orderBy.length > 0) {
                    aggrConn = aggrConn.orderBy(orderBy);
                }
            }

            const tvarLovsConn = globalThis.database
                .select()
                .from("TEMPLATE_VARIABLE_LOV")
                .whereIn("TVAR_ID", (builder) =>
                    builder
                        .select("TVAR_ID")
                        .from(this.getColumns(cvId).as("ignored_alias")),
                );

            if (
                conn._statements.length > 0 &&
                conn._statements[0].grouping === "with"
            ) {
                const withStatement = conn._statements.shift();
                aggrConn = aggrConn.from(conn.as("aggr_tbl"));
                aggrConn._statements.unshift(withStatement);
            } else {
                aggrConn = aggrConn.from(conn.as("aggr_tbl"));
            }

            const [data, lovs] = await Promise.all([aggrConn, tvarLovsConn]);

            let items = await self.fillDLValues(data, usedMultiVars);
            items = await self.fillDtValues(data, dtMultiVars, isArchived);
            items = self.fillLovMutations(data, lovs);
            items = self.formatLdMultiValues(data, ldMultiVars);

            return [items, totalCount, columns];
        }

        conn = conn.limit ? conn.limit(limit || 100) : conn;
        conn = conn.offset ? conn.offset(offset || 0) : conn;

        const lovsConn = globalThis.database
            .select()
            .from("TEMPLATE_VARIABLE_LOV")
            .whereIn("TVAR_ID", (builder) =>
                builder
                    .select("TVAR_ID")
                    .from(this.getColumns(cvId).as("ignored_alias")),
            );

        const [data, tlovs] = await Promise.all([conn, lovsConn]);

        let items = await self.fillDLValues(data, usedMultiVars);
        items = await self.fillDRBigValues(items, usedDRVars, isArchived);
        items = await self.fillDtValues(data, dtMultiVars, isArchived);
        items = self.fillLovMutations(data, tlovs);
        items = self.formatLdMultiValues(data, ldMultiVars);

        return [items, totalCount, columns];
    }

    fillLovMutations(variables, lovs) {
        return variables.map((variable) => {
            Object.entries(variable).forEach(([key, value]) => {
                // Determine is an attribute is a Variable by pattern
                const { STARTS_WITH_V, TVAR_ID } = key.match(
                    /(?<STARTS_WITH_V>V)?(?<TVAR_ID>\d+)?$/,
                ).groups;
                const isVariable = STARTS_WITH_V && TVAR_ID;

                if (isVariable) {
                    // Try to find LOVs by TVAR_ID and value (the Variable type is now known)
                    const variableLov = _.find(lovs, {
                        TVAR_ID: Number(TVAR_ID),
                        TVARLOV_TEXT_VALUE: value,
                    });

                    if (variableLov) {
                        // Variable is LOV and match has been found, add mutations
                        globalThis.dynamicConfig.langs.forEach((lang) => {
                            variable[`${key}_${lang.toUpperCase()}`] =
                                variableLov[
                                    `TVARLOV_TEXT_VALUE_${lang.toUpperCase()}`
                                ];
                        });
                    }
                }
            });
            return variable;
        });
    }

    /**
     * Find @BV_ in custom view and replace with big value from instance variables.
     * @param data
     * @param usedVars
     * @param isArchived
     * @returns {Promise.<*>}
     */
    async fillDRBigValues(data, usedVars, isArchived?: boolean) {
        // Find variable link.

        const drVars = Object.keys(usedVars);
        if (drVars.length === 0) {
            return data;
        }

        // Search missing.
        const vars = [];
        for (const drVar of drVars) {
            for (const row of data) {
                const rowValue = row[drVar];
                if (
                    typeof rowValue === "string" &&
                    rowValue.startsWith("@BV_")
                ) {
                    const ivarId = rowValue.substring(4);
                    vars.push(ivarId);
                }
            }
        }

        // Retrieve big value for missing variables.
        const collection = globalThis.orm.collection(
            "Variable",
            this.connection
                .select("IVAR_BIG_VALUE", "IVAR_ID")
                .from(`${isArchived ? "ARCH_" : ""}INSTANCE_VARIABLES`)
                .whereIn("IVAR_ID", vars),
        );
        const bigValues = await collection.collectAll();
        const bigValuesMap = {};
        for (const bigRow of bigValues) {
            bigValuesMap[`@BV_${bigRow.IVAR_ID}`] = bigRow.IVAR_BIG_VALUE;
        }

        // Replace values.
        for (const drVar of drVars) {
            for (const row of data) {
                const rowValue = row[drVar];
                if (
                    typeof rowValue === "string" &&
                    rowValue.startsWith("@BV_")
                ) {
                    row[drVar] = bigValuesMap[rowValue];
                }
            }
        }

        return data;
    }

    async fillDLValues(data, usedVariables) {
        let hasUserLovs = false;
        let hasRoleLovs = false;
        let hasOrgLovs = false;

        Object.keys(usedVariables).forEach((key) => {
            if (usedVariables[key].TVAR_ATTRIBUTE === "U") {
                hasUserLovs = true;
            } else if (usedVariables[key].TVAR_ATTRIBUTE === "O") {
                hasOrgLovs = true;
            } else if (usedVariables[key].TVAR_ATTRIBUTE === "R") {
                hasRoleLovs = true;
            }
        });

        const userRepo = globalThis.orm.repo("user", this.connection);
        const orgRepo = globalThis.orm.repo(
            "OrganizationStructure",
            this.connection,
        );
        const roleRepo = globalThis.orm.repo("role", this.connection);

        const lists = {};
        const tasks = [];

        if (hasUserLovs) {
            const fetchUsers = async () => {
                const list = await userRepo.fetchAll([
                    "USER_ID",
                    "USER_NAME",
                    "USER_FIRST_NAME",
                    "USER_LAST_NAME",
                ]);

                lists.U = {};
                list.forEach((user) => {
                    lists.U[user.USER_ID] = `${user.USER_DISPLAY_NAME}`;
                });
            };
            tasks.push(fetchUsers());
        }

        if (hasOrgLovs) {
            const fetchOrgs = async () => {
                const list = await orgRepo.fetchAll([
                    "ORGSTR_ID",
                    "ORGSTR_NAME",
                ]);

                lists.O = {};
                list.forEach((org) => {
                    lists.O[org.ORGSTR_ID] = org.ORGSTR_NAME;
                });
            };

            tasks.push(fetchOrgs());
        }

        if (hasRoleLovs) {
            const fetchRoles = async () => {
                const list = await roleRepo.fetchAll(["ROLE_ID", "ROLE_NAME"]);
                lists.R = {};
                list.forEach((role) => {
                    lists.R[role.ROLE_ID] = role.ROLE_NAME;
                });
            };

            tasks.push(fetchRoles());
        }

        try {
            await Promise.all(tasks);

            if (!usedVariables || Object.keys(usedVariables).length === 0) {
                return data;
            }

            if (Array.isArray(data) && data.length > 0) {
                data.forEach((item) => {
                    Object.keys(usedVariables).forEach((key) => {
                        const attr = usedVariables[key].TVAR_ATTRIBUTE;
                        const raw = item[key];
                        if (raw) {
                            try {
                                const parsed = JSON.parse(raw);
                                if (
                                    Array.isArray(parsed) &&
                                    parsed.length > 0
                                ) {
                                    const buf = [];
                                    parsed.forEach((id) => {
                                        buf.push(lists[attr][id]);
                                    });
                                    item[key] = buf.join(", ");
                                }
                            } catch (err) {
                                globalThis.tasLogger.error(
                                    "Error in fillDtValues",
                                    {
                                        err,
                                    },
                                );
                            }
                        }
                    });
                });
            }

            return data;
        } catch (err) {
            globalThis.tasLogger.error("Error in fillDtValues", {
                err,
            });
            throw err;
        }
    }

    async fillDtValues(data, dtMultiVars, isArchived?: boolean) {
        if (_.isEmpty(dtMultiVars)) {
            return data;
        }

        const vars = [];
        // Search missing.
        dtMultiVars.forEach((dt) => {
            data.forEach((item) => {
                const rowValue = item[dt];
                if (
                    typeof rowValue === "string" &&
                    rowValue.startsWith("@DTM_")
                ) {
                    const ivarId = rowValue.substring(5);
                    vars.push(ivarId);
                }
            });
        });

        // Retrieve IVAR_MULTI_SELECTED for missing variables.
        const chunkSize = 1000;
        let dtValues = [];
        for (let i = 0; i < vars.length; i += chunkSize) {
            const chunk = vars.slice(i, i + chunkSize);
            const collection = globalThis.orm.collection(
                "Variable",
                this.connection
                    .select("IVAR_MULTI_SELECTED", "IVAR_ID")
                    .from(`${isArchived ? "ARCH_" : ""}INSTANCE_VARIABLES`)
                    .whereIn("IVAR_ID", chunk),
            );
            const dtValuesChunk = await collection.collectAll();
            dtValues = dtValues.concat(dtValuesChunk);
        }

        const dtValuesMap = {};

        dtValues.forEach((row) => {
            let ivarMultiSelected = row.IVAR_MULTI_SELECTED || [];
            try {
                ivarMultiSelected = JSON.parse(ivarMultiSelected);
            } catch (_ignored) {
                // nothing
            }

            const valuesArr = [];
            // get DT values [{"DTV_INDEX":"DTV_VALUE"}, ...] -> ["DTV_VALUE", ...]
            ivarMultiSelected.forEach((obj) => {
                const val = Object.values(obj)[0];
                valuesArr.push(val);
            });

            dtValuesMap[`@DTM_${row.IVAR_ID}`] = valuesArr.join(", ");
        });

        // Replace values.
        dtMultiVars.forEach((dt) => {
            data.forEach((item) => {
                const rowValue = item[dt];
                if (
                    typeof rowValue === "string" &&
                    rowValue.startsWith("@DTM_")
                ) {
                    item[dt] = dtValuesMap[rowValue];
                }
            });
        });

        return data;
    }

    formatLdMultiValues(data, ldMultiVars) {
        if (_.isEmpty(ldMultiVars)) {
            return data;
        }

        ldMultiVars.forEach((dt) => {
            data.forEach((item) => {
                const dtValue = item[dt];

                try {
                    const arr = [];

                    JSON.parse(dtValue).forEach((val) => {
                        if (
                            moment(val, "YYYY-MM-DDTHH:mm:ss", true).isValid()
                        ) {
                            const date = moment(val, "YYYY-MM-DDTHH:mm:ss");
                            const zuluTime = new Date(date.utc());
                            arr.push(zuluTime.toISOString());
                        }

                        item[dt] = arr;
                    });
                } catch (_e) {
                    item[dt] = null;
                }
            });
        });

        return data;
    }

    findDefaultFilter(defaultFilter) {
        if (!defaultFilter || !Array.isArray(defaultFilter)) {
            return;
        }

        const result = [];
        defaultFilter.forEach((filter) => {
            if (filter.op && filter.enabled) {
                // Column
                const column = filter.column?.match(/V-/)
                    ? filter.column
                    : filter.column?.match(/^V/)
                      ? filter.column.substr(1)
                      : filter.column;

                result.push({
                    column: filter.datepart
                        ? `${column}#datepart_${filter.datepart}#`
                        : column, // if filter use datepart, insert sign of using it for future use
                    operator: filter.op,
                    value: filter.value,
                    // datepart: filter.datepart ? filter.datepart : null,
                });
            }
        });
        return result;
    }

    async processSort(orderData, sortData, columns, conn, columnsToSkip = []) {
        let order = orderData;
        let sort = sortData;

        if (typeof order === "string" && order.split(",").length > 1) {
            order = order.split(",");
            sort = sort ? sort.split(",") : [];
        }

        if (Array.isArray(order)) {
            let outConn = { conn };
            for (let i = 0; i < order.length; i += 1) {
                if (_.includes(columnsToSkip, order[i])) {
                    continue;
                }
                outConn = await this.processSort(
                    order[i],
                    sort.length > i ? sort[i] : null,
                    columns,
                    outConn.conn,
                );
            }
            return outConn;
        }

        const self = this;

        if (order == null || typeof order === "undefined") {
            return { conn };
        }

        if (sort == null || typeof sort === "undefined") {
            sort = "ASC";
        }

        const rawSort = sort;

        sort += " nulls last";

        let rawOrder = null;
        if (order.match(/V-/)) {
            if (order === "V-id") {
                // t3f-1148 Řazení tabulek podle nejednoznačného sloupce vrací různorodé výsledky
                rawOrder = `"IP"."IPROC_ID" ${sort}`;
            } else if (order == "V-1") {
                rawOrder = `"IP"."IPROC_NAME" ${sort}`;
            } else if (order == "V-2") {
                rawOrder = `"IP"."IPROC_DESCRIPTION" ${sort}`;
            } else if (order == "V-3") {
                rawOrder =
                    globalThis.database.raw(`"UIP"."USER_DISPLAY_NAME"`) + sort;
            } else if (order == "V-4") {
                rawOrder = `"IP"."IPROC_ACTUAL_START_DATE" ${sort}`;
            } else if (order == "V-5") {
                rawOrder = `"IP"."IPROC_DUE_DATE_FINISH" ${sort}`;
            } else if (order == "V-6") {
                rawOrder = `"IP"."IPROC_PRIORITY" ${sort}`;
            } else if (order == "V-7") {
                rawOrder = `"IP"."IPROC_SUMMARY" ${sort}`;
            } else if (order == "V-8") {
                rawOrder = `"IP"."IPROC_ACTUAL_FINISH_DATE" ${sort}`;
            } else if (order == "V-9") {
                rawOrder = `"IP"."IPROC_CASE_STATUS" ${sort}`;
            } else if (order == "V-0") {
                rawOrder = `"IP"."IPROC_ID" ${sort}`;
            } else if (order.includes("V-7") && order.includes(",")) {
                // order=iproc_summary_cs,V-7
                const cols = order.split(",");
                const arraySort = rawSort.split(",");
                const columnNames = [
                    `"IP"."${cols[0].toUpperCase()}"`,
                    `"IP"."IPROC_SUMMARY"`,
                ];

                rawOrder = "";

                columnNames.forEach((col, i) => {
                    rawOrder += ` ${col} ${arraySort[i]} nulls last ${
                        i === columnNames.length - 1 ? "" : ","
                    } `;
                });
            } else if (order.includes("V-9") && order.includes(",")) {
                // @t3b-1105 Úprava sloupce status v přehledech Cases
                // order=cs_name_cs,V-9
                const cols = order.split(",");
                const arraySort = rawSort.split(",");
                const columnNames = [
                    `"CS"."${cols[0].toUpperCase()}"`,
                    `"IP"."IPROC_CASE_STATUS"`,
                ];

                rawOrder = "";

                columnNames.forEach((col, i) => {
                    rawOrder += ` ${col} ${arraySort[i]} nulls last ${
                        i === columnNames.length - 1 ? "" : ","
                    } `;
                });
            } else {
                throw new UserException(
                    `Unknown ordering system column [${order}]`,
                );
            }
        } else {
            // variable id starts with v or V
            const regex = new RegExp(/[v]\d+/, "i");

            if (order.match(regex)) {
                order = order.substring(1, order.length);
            }

            // order as string (test var 1)
            if (
                isNaN(order) &&
                order.indexOf("cs_name_") === -1 &&
                order.indexOf("iproc_summary_") === -1
            ) {
                order = self.getTvarByName(columns, order).TVAR_ID;
            }

            const tvar = self.getTvar(columns, order);

            if (tvar == null) {
                globalThis.tasLogger.warning(
                    "Suspicious custom view order column. No variables found.",
                    { order },
                );
                return { conn };
            }

            // Is CASE_STATUS translation
            if (tvar.isColumn) {
                rawOrder = `${tvar.column} ${sort}`;
            } else {
                // Is TEMPLATE_VARIABLE
                const type = tvar.TVAR_TYPE;
                const attr = tvar.TVAR_ATTRIBUTE;
                const multi = tvar.TVAR_MULTI;

                if (multi != null && type != "LT") {
                    throw new UserException(
                        `This sort type is not supported. IVAR_TYPE = ${type} IVAR_ATTRIBTE = ${attr} IVAR_MULTI = ${multi} tvarId = ${tvar.TVAR_ID}`,
                    );
                }

                if (
                    type == "T" ||
                    type == "LT" ||
                    type == "DT" ||
                    (type == "DL" && attr == null)
                ) {
                    rawOrder = `"IPV"."${tvar.TVAR_ALIAS}" ${sort}`;
                } else if (
                    (type == "N" || type == "LN") &&
                    (attr == null || attr == "S") /* SEQUENCE */
                ) {
                    rawOrder = `"IPV"."${tvar.TVAR_ALIAS}" ${sort}`;
                } else if (type == "D" || type == "LD") {
                    rawOrder = `"IPV"."${tvar.TVAR_ALIAS}" ${sort}`;
                } else if (
                    (type == "N" || type == "LN" || type == "DL") &&
                    attr != null
                ) {
                    if (attr == "U") {
                        rawOrder =
                            globalThis.database.raw(
                                `(select "USER_DISPLAY_NAME" from "USERS" where "USER_ID" = "IPV"."${tvar.TVAR_ALIAS}") `,
                            ) + sort;
                    } else if (attr == "O") {
                        rawOrder =
                            globalThis.database.raw(
                                `(select "ORGSTR_NAME" from "ORGANIZATION_STRUCTURE" where "ORGSTR_ID" = "IPV"."${tvar.TVAR_ALIAS}") `,
                            ) + sort;
                    } else if (attr == "R") {
                        rawOrder =
                            globalThis.database.raw(
                                `(select "ROLE_NAME" from "ROLES" where "ROLE_ID" = "IPV"."${tvar.TVAR_ALIAS}") `,
                            ) + sort;
                    } else {
                        throw new UserException(
                            `This sort type is not supported. IVAR_TYPE = ${type} IVAR_ATTRIBTE = ${attr} IVAR_MULTI = ${multi} tvarId = ${tvar.TVAR_ID}`,
                        );
                    }
                }
            }
        }

        conn.orderByRaw(rawOrder);
        return { conn };
    }

    async processRawFilter(conn, filter, columns) {
        const ormFilter = new Filter(null, this.req);
        let parsedFilter = await ormFilter.parseRawFilterAndEvaluate(filter);

        if (!parsedFilter) {
            return { conn };
        }

        // Each filter is deeper filter. No filter can affect another
        parsedFilter = [parsedFilter];

        conn = this.chainMain(conn, parsedFilter, columns);
        return { conn };
    }

    chainMain(promise, parsed, columns) {
        for (const item of parsed) {
            if (item.type && item.type === "conjunction") {
                this._lastConjuction = item.value;
            } else if (item.type && item.type === "expression") {
                promise = this.processFilter(item, columns, promise);
            } else if (Array.isArray(item) && item.length > 0) {
                promise = this.chainDeeper(promise, item, columns);
            }
        }
        return promise;
    }

    chainDeeper(promise, deeper, columns) {
        const self = this;
        promise[self.modifyByConjuction("where", this._lastConjuction)](
            function () {
                self.chainMain(this, deeper, columns);
            },
        );
        return promise;
    }

    processFilter(item, columns, conn) {
        const self = this;

        const ormFilter = new Filter(null, this.req, {
            disableAccentSensitivity:
                JSON.parse(_.get(this.req, "query.disable_accent", "false")) ||
                globalThis.dynamicConfig.tas.disableAccentSensitivity, // Parsing to type convert 'string' to 'boolean'
        });
        const { disableAccentSensitivity } = ormFilter.options;

        const simpleOperator = ormFilter.getSimpleCondition(item.operator);
        const likeComparator = ormFilter.getLikeCondition(item.operator);
        const nullOperator = ormFilter.getNullCondition(item.operator);

        const datepartRegex = item.key.match(
            new RegExp(/([vV]?-?\d+)#datepart_([A-Z]*)#/),
        );
        if (datepartRegex) {
            if (datepartRegex[2]) {
                item.datepart = datepartRegex[2];
                item.key = datepartRegex[1];
            }
        }

        if (item.key.match(/V-/)) {
            const sysColumnFilter = SYSTEM_COLUMNS[item.key];
            if (sysColumnFilter == null) {
                throw new UserException(`Invalid filter ${item.key}.`);
            }

            if (_.has(item, "datepart") && DATEPARTS.includes(item.datepart)) {
                conn = conn[
                    this.modifyByConjuction("where", this._lastConjuction)
                ](function () {
                    const datepartWhere = globalThis.orm.db.datepart(
                        item.datepart,
                        sysColumnFilter.columnName,
                    );
                    const bindings = [...datepartWhere[1], item.value];

                    this.whereRaw(
                        `${datepartWhere[0]} ${simpleOperator} ?`,
                        bindings,
                    );
                });

                if (simpleOperator === "<>" && item.value) {
                    this.orWhereNull(sysColumnFilter.columnName);
                }
            } else if (
                sysColumnFilter.columnType == "T" ||
                sysColumnFilter.columnType == "N"
            ) {
                if (simpleOperator) {
                    conn = conn[
                        this.modifyByConjuction("where", this._lastConjuction)
                    ](function () {
                        if (sysColumnFilter.columnType === "N") {
                            this.whereRaw(
                                sysColumnFilter.columnName +
                                    " " +
                                    simpleOperator +
                                    "?",
                                item.value,
                            );
                        } else {
                            this.whereRaw(
                                globalThis.orm.db.like(
                                    sysColumnFilter.columnName,
                                    "",
                                    disableAccentSensitivity,
                                    simpleOperator,
                                    item.value,
                                ),
                                item.value,
                            );
                        }

                        if (simpleOperator == "<>" && item.value) {
                            this.orWhereNull(sysColumnFilter.columnName);
                        }
                    });
                } else if (likeComparator) {
                    if (!item.value.includes("%")) {
                        item.value = `%${item.value}%`;
                    }

                    conn = conn[
                        this.modifyByConjuction(
                            "whereRaw",
                            this._lastConjuction,
                        )
                    ](
                        globalThis.orm.db.like(
                            sysColumnFilter.columnName,
                            "",
                            disableAccentSensitivity,
                            likeComparator,
                            item.value,
                        ),
                        item.value,
                    );
                } else if (nullOperator) {
                    if (nullOperator == "is null") {
                        conn = conn[
                            this.modifyByConjuction(
                                "whereNull",
                                this._lastConjuction,
                            )
                        ](sysColumnFilter.columnName);
                    } else if (nullOperator == "is not null") {
                        conn = conn[
                            this.modifyByConjuction(
                                "whereNotNull",
                                this._lastConjuction,
                            )
                        ](sysColumnFilter.columnName);
                    }
                }
            } else if (sysColumnFilter.columnType == "D") {
                let date = item.value;
                if (typeof date === "string") {
                    date = new Date(date);
                }

                if (simpleOperator) {
                    conn = conn.whereRaw(
                        `${globalThis.orm.db.trunc(
                            sysColumnFilter.columnName,
                            item.value,
                        )} ${simpleOperator} ${globalThis.orm.db.toDate()}`,
                        `${date.getFullYear()}-${
                            date.getMonth() + 1
                        }-${date.getDate()}`,
                    );
                } else if (nullOperator) {
                    if (nullOperator == "is null") {
                        conn = conn.whereNull(sysColumnFilter.columnName);
                    } else if (nullOperator == "is not null") {
                        conn = conn.whereNotNull(sysColumnFilter.columnName);
                    }
                }
            } else if (item.key.match(/V-3/)) {
                if (simpleOperator) {
                    conn = conn[
                        this.modifyByConjuction(
                            "whereRaw",
                            this._lastConjuction,
                        )
                    ](
                        globalThis.orm.db.like(
                            `UIP.USER_DISPLAY_NAME`,
                            "",
                            disableAccentSensitivity,
                            simpleOperator,
                            item.value,
                        ),
                        item.value,
                    );
                } else if (likeComparator) {
                    if (!item.value.includes("%")) {
                        item.value = `%${item.value}%`;
                    }

                    conn = conn[
                        this.modifyByConjuction(
                            "whereRaw",
                            this._lastConjuction,
                        )
                    ](
                        globalThis.orm.db.like(
                            `UIP.USER_DISPLAY_NAME`,
                            "",
                            disableAccentSensitivity,
                            likeComparator,
                            item.value,
                        ),
                        item.value,
                    );
                } else if (nullOperator) {
                    if (nullOperator == "is null") {
                        conn =
                            conn[
                                this.modifyByConjuction(
                                    "whereNull",
                                    this._lastConjuction,
                                )
                            ]("UIP.USER_ID");
                    } else if (nullOperator == "is not null") {
                        conn =
                            conn[
                                this.modifyByConjuction(
                                    "whereNotNull",
                                    this._lastConjuction,
                                )
                            ]("UIP.USER_ID");
                    }
                }
            }
        } else {
            // variable id starts with v or V
            const regex = new RegExp(/[v]\d+/, "i");

            if (item.key.match(regex)) {
                item.key = item.key.substring(1, item.key.length);
            }

            // variable as string (test var 1)
            if (
                isNaN(item.key) &&
                item.key.indexOf("cs_name_") === -1 &&
                item.key.indexOf("iproc_summary_") === -1
            ) {
                item.key = self.getTvarByName(columns, item.key).TVAR_ID;
            }

            const tvar = self.getTvar(columns, item.key);
            if (tvar == null) {
                throw new UserException(
                    `TVAR id not found in custom view. TVAR_ID = ${item.key}.`,
                    "INVALID_TVAR",
                );
            }

            // Determine operator
            let operator = null;
            if (simpleOperator) {
                operator = "simple";
            } else if (likeComparator) {
                operator = "like";
            } else {
                operator = "null";
            }

            // CASE STATUS
            if (tvar.isColumn) {
                switch (operator) {
                    case "simple":
                        conn = conn[
                            this.modifyByConjuction(
                                "where",
                                this._lastConjuction,
                            )
                        ](function () {
                            this.whereRaw(
                                globalThis.orm.db.like(
                                    tvar.column,
                                    "",
                                    disableAccentSensitivity,
                                    simpleOperator,
                                    item.value,
                                ),
                                item.value,
                            );

                            if (simpleOperator === "<>") {
                                this.orWhereNull(tvar.column);
                            }
                        });
                        break;
                    case "like":
                        if (!item.value.includes("%")) {
                            item.value = `%${item.value}%`;
                        }

                        conn = conn[
                            this.modifyByConjuction(
                                "whereRaw",
                                this._lastConjuction,
                            )
                        ](
                            globalThis.orm.db.like(
                                tvar.column,
                                "",
                                disableAccentSensitivity,
                                likeComparator,
                                item.value,
                            ),
                            item.value,
                        );
                        break;
                    case "null":
                        if (nullOperator === "is null") {
                            conn = conn[
                                this.modifyByConjuction(
                                    "whereNull",
                                    this._lastConjuction,
                                )
                            ](tvar.column);
                        } else if (nullOperator === "is not null") {
                            conn = conn[
                                this.modifyByConjuction(
                                    "whereNotNull",
                                    this._lastConjuction,
                                )
                            ](tvar.column);
                        }
                        break;
                    default:
                        break;
                }
            } else if (
                _.has(item, "datepart") &&
                DATEPARTS.includes(item.datepart)
            ) {
                // date filter
                conn = conn[
                    this.modifyByConjuction("where", this._lastConjuction)
                ](function () {
                    const datepartWhere = globalThis.orm.db.datepart(
                        item.datepart,
                        `"IPV"."${tvar.TVAR_ALIAS}"`,
                    );
                    const bindings = [...datepartWhere[1], item.value];

                    this.whereRaw(
                        `${datepartWhere[0]} ${simpleOperator} ?`,
                        bindings,
                    );
                });

                if (simpleOperator === "<>" && item.value) {
                    this.orWhereNull(`IPV.${tvar.TVAR_ALIAS}`);
                }
            } else if (simpleOperator) {
                if (
                    tvar.TVAR_TYPE == "T" ||
                    tvar.TVAR_TYPE == "DT" ||
                    tvar.TVAR_TYPE == "LT" ||
                    tvar.TVAR_TYPE == "N" ||
                    tvar.TVAR_TYPE == "LN" ||
                    (tvar.TVAR_TYPE == "DL" && tvar.TVAR_ATTRIBUTE == null)
                ) {
                    conn = conn[
                        this.modifyByConjuction("where", this._lastConjuction)
                    ](function () {
                        if (tvar.TVAR_TYPE === "N") {
                            this.whereRaw(
                                `"IPV"."${tvar.TVAR_ALIAS}"` +
                                    " " +
                                    simpleOperator +
                                    "?",
                                item.value,
                            );
                        } else {
                            this.whereRaw(
                                globalThis.orm.db.like(
                                    `"IPV"."${tvar.TVAR_ALIAS}"`,
                                    "",
                                    disableAccentSensitivity,
                                    simpleOperator,
                                    item.value,
                                ),
                                item.value,
                            );
                        }

                        if (simpleOperator == "<>") {
                            this.orWhereNull(`IPV.${tvar.TVAR_ALIAS}`);
                        }
                    });
                } else if (tvar.TVAR_TYPE == "D" || tvar.TVAR_TYPE == "LD") {
                    let date = item.value;
                    if (typeof date === "string") {
                        date = new Date(date);
                    }
                    conn = conn[
                        this.modifyByConjuction(
                            "whereRaw",
                            this._lastConjuction,
                        )
                    ](
                        `${globalThis.orm.db.trunc(
                            `"IPV"."${tvar.TVAR_ALIAS}"`,
                            item.value,
                        )} ${simpleOperator} ${globalThis.orm.db.toDate()}`,
                        `${date.getFullYear()}-${
                            date.getMonth() + 1
                        }-${date.getDate()}`,
                    );
                } else if (tvar.TVAR_TYPE === "DL") {
                    if (tvar.TVAR_MULTI === "X") {
                        throw new UserException(
                            `Filter operator ${simpleOperator} not supported for dynamic list with multichoice.`,
                        );
                    }
                    const columnName = this.getColumnNameForDynamicList(
                        tvar.TVAR_ATTRIBUTE,
                        this.getSubqueryForDynamicListValues(
                            tvar.TVAR_MULTI,
                            `"IPV"."${tvar.TVAR_ALIAS}"`,
                        ),
                    );
                    conn[
                        this.modifyByConjuction(
                            "whereRaw",
                            this._lastConjuction,
                        )
                    ](`${columnName + simpleOperator} ?`, item.value);
                }
            } else if (likeComparator) {
                if (!item.value.includes("%")) {
                    item.value = `%${item.value}%`;
                }

                let columnName = "NOT_DEFINED.NOT_DEFINED";
                switch (tvar.TVAR_TYPE) {
                    case VARIABLE.TYPE_TEXT:
                    case VARIABLE.TYPE_TEXT_LIST:
                    case VARIABLE.TYPE_DYNAMIC_TABLE:
                    case VARIABLE.TYPE_NUMBER:
                    case VARIABLE.TYPE_NUMBER_LIST:
                    case VARIABLE.TYPE_DATE:
                    case VARIABLE.TYPE_DATE_LIST:
                        columnName = `"IPV"."${tvar.TVAR_ALIAS}"`;
                        break;

                    case VARIABLE.TYPE_DYNAMIC_LIST:
                        columnName = this.getColumnNameForDynamicList(
                            tvar.TVAR_ATTRIBUTE,
                            this.getSubqueryForDynamicListValues(
                                tvar.TVAR_MULTI,
                                `"IPV"."${tvar.TVAR_ALIAS}"`,
                            ),
                        );
                        break;
                    default:
                        throw new InternalException(
                            `Invalid TVAR_TYPE ${tvar.TVAR_TYPE} for CO filtering`,
                            "INVALID_TVAR_TYPE",
                            { tvar },
                        );
                }

                if (
                    tvar.TVAR_TYPE === VARIABLE.TYPE_NUMBER ||
                    tvar.TVAR_TYPE === VARIABLE.TYPE_NUMBER_LIST
                ) {
                    const commanNumber = item.value.replace(",", ".");
                    return conn[
                        this.modifyByConjuction(
                            "whereRaw",
                            this._lastConjuction,
                        )
                    ](
                        globalThis.orm.db.like(
                            columnName,
                            "",
                            false,
                            likeComparator,
                            commanNumber,
                        ),
                        commanNumber,
                    );
                }

                return conn[
                    this.modifyByConjuction("whereRaw", this._lastConjuction)
                ](
                    globalThis.orm.db.like(
                        columnName,
                        "",
                        disableAccentSensitivity,
                        likeComparator,
                        item.value,
                    ),
                    item.value,
                );
            } else if (nullOperator) {
                if (nullOperator == "is null") {
                    conn = conn[
                        this.modifyByConjuction(
                            "whereNull",
                            this._lastConjuction,
                        )
                    ](`"IPV"."${tvar.TVAR_ALIAS}"`);
                } else if (nullOperator == "is not null") {
                    conn = conn[
                        this.modifyByConjuction(
                            "whereNotNull",
                            this._lastConjuction,
                        )
                    ](`"IPV"."${tvar.TVAR_ALIAS}"`);
                }
            }
        }
        return conn;
    }

    getColumnNameForDynamicList(attribute, subquery) {
        if (globalThis.dynamicConfig.db.client === "mssql") {
            switch (attribute) {
                case VARIABLE.ATTR_USER:
                    return `(select STRING_AGG(CAST(USER_DISPLAY_NAME as nvarchar(MAX)), ' ') from USERS where USER_ID IN (${subquery}))`;
                case VARIABLE.ATTR_ORG_STRUCT:
                    return `(select STRING_AGG(CAST(ORGSTR_NAME as nvarchar(MAX)), ' ') from ORGANIZATION_STRUCTURE where ORGSTR_ID IN (${subquery}))`;
                case VARIABLE.ATTR_ROLE:
                    return `(select STRING_AGG(CAST(ROLE_NAME as nvarchar(MAX)), ' ') from ROLES where ROLE_ID IN (${subquery}))`;
                default:
                    throw new InternalException(
                        `Invalid TVAR_ATTRIBUTE ${attribute} for CO filtering`,
                        "INVALID_TVAR_ATTRIBUTE",
                        { attribute },
                    );
            }
        } else {
            switch (attribute) {
                case VARIABLE.ATTR_USER:
                    return `(SELECT STRING_AGG("USER_DISPLAY_NAME", ' ' ORDER BY "USER_LAST_NAME") AS "concatenated_names" from "USERS" where "USER_ID" IN (${subquery}))`;
                case VARIABLE.ATTR_ORG_STRUCT:
                    return `(SELECT STRING_AGG("ORGSTR_NAME", ' ' ORDER BY "ORGSTR_NAME") AS "concatenated_orgstr" from "ORGANIZATION_STRUCTURE" where "ORGSTR_ID" IN (${subquery}))`;
                case VARIABLE.ATTR_ROLE:
                    return `(SELECT STRING_AGG("ROLE_NAME", ' ' ORDER BY "ROLE_NAME") AS "concatenated_roles" FROM "ROLES" WHERE "ROLE_ID" IN (${subquery}))`;
                default:
                    throw new InternalException(
                        `Invalid TVAR_ATTRIBUTE ${attribute} for CO filtering`,
                        "INVALID_TVAR_ATTRIBUTE",
                        { attribute },
                    );
            }
        }
    }

    getSubqueryForDynamicListValues(multi, alias) {
        if (multi === "X") {
            if (globalThis.dynamicConfig.db.client === "mssql") {
                return `SELECT value FROM STRING_SPLIT(REPLACE(REPLACE(REPLACE(${alias}, '[', ''), ']', ''), '"', ''), ',')`;
            }
            return `SELECT TRIM(BOTH ' "' FROM value)::INTEGER AS value FROM UNNEST(STRING_TO_ARRAY(REPLACE(REPLACE(REPLACE(${alias}, '[', ''), ']', ''), '"', ''), ',')) AS value`;
        }
        return `${alias}`;
    }

    modifyByConjuction(fceName, conjuction) {
        if (!fceName) {
            throw new IllegalArgumentException("fceName can not be undefined.");
        }

        if (!conjuction) {
            return fceName;
        }
        if (conjuction == "<and>") {
            return fceName;
        }
        if (conjuction == "<or>") {
            fceName = `orW${fceName.substr(1)}`;
        }

        return fceName;
    }

    getTvar(columns, id) {
        let res = null;
        columns.forEach((column) => {
            if (column.TVAR_ID == id) {
                res = column;
            }
        });

        // Check for CASE_STATUS translations
        if (!res && id.indexOf("cs_name_") > -1) {
            return {
                isColumn: true,
                column: `CS.${id.toUpperCase()}`,
            };
        }

        // Check for IPROC_SUMMARY translations
        if (!res && id.indexOf("iproc_summary_") > -1) {
            return {
                isColumn: true,
                column: `IP.${id.toUpperCase()}`,
            };
        }

        return res;
    }

    getTvarByName(columns, name) {
        let res = null;
        const findColumn = _.find(columns, ["TVAR_NAME", name]);

        if (findColumn) {
            res = findColumn;
        }

        return res;
    }

    // 1 ordering
    // 2 default order and filter
    // 3 transform lov
    getColumns(cvId) {
        return this.connection
            .select([
                "CVC.CVC_NAME",
                this.connection.raw(`
                        case
                            when
                                "CV"."CV_TABLE_SOURCE" is not null
                            then
                                "CVC"."CVC_DB_COLUMN"
                            else
                                "TV"."TVAR_ALIAS"
                        end
                        as "TVAR_ALIAS"
                    `),
                "TV.TVAR_ID",
                this.connection.raw(`"CVC"."TVAR_ID" as "CVID"`),
                "TV.TVAR_TYPE",
                "TV.TVAR_ATTRIBUTE",
                "TV.TVAR_MULTI",
                "TV.TVAR_NAME",
                "TV.TVAR_META",
                "CVC.CVC_DB_COLUMN",
                "CV.CV_TABLE_SOURCE",
            ])
            .from("CUSTOM_VIEWS as CV")
            .leftJoin("TEMPLATE_VARIABLES as TV", "CV.TPROC_ID", "TV.TPROC_ID")
            .joinRaw(
                `LEFT JOIN "CUSTOM_VIEW_COLUMNS" "CVC" ON ("TV"."TVAR_ID" = "CVC"."TVAR_ID" AND "CVC"."CV_ID" = "CV"."CV_ID")`,
            )
            .where("CV.CV_ID", cvId);
    }

    /**
     * Remove custom view and its shares
     * @param id
     * @returns {*}
     */
    async delete(id) {
        await this.connection
            .select()
            .from("CUSTOM_VIEWS_MAIL")
            .where("CV_ID", id)
            .delete();
        return await this.connection
            .select()
            .from("CUSTOM_VIEW_COLUMNS")
            .where("CV_ID", id)
            .delete()
            .then(() =>
                this.connection
                    .select()
                    .from("CUSTOM_VIEW_SHARE")
                    .where("CVS_CV_ID", id)
                    .delete(),
            )
            .then(() =>
                this.connection
                    .select()
                    .from("CUSTOM_VIEWS")
                    .where("CV_ID", id)
                    .delete(),
            );
    }

    /**
     * Makes copy of custom view and its shares. New name is generated
     * automatically with `clone($index)_$oldName` where $index is count of previous copies.
     * @param id
     * @param ownerUserId
     * @returns {Promise.<Number>}
     */
    clone(id, ownerUserId, customNames = {}) {
        let newId = null;
        let oldId = null;
        return this.get(id)
            .then((cv) => {
                // Clear id to create new copy of cv
                oldId = cv.CV_ID;
                cv.CV_ID = null;
                cv.makeAllDirty();
                return cv;
            })
            .then(async (cv) => {
                // Find clone name (including mutations)
                customNames.CV_NAME = customNames.CV_NAME || "";
                const nameExists =
                    (await this.findByName(customNames.CV_NAME)).length > 0;

                if (nameExists) {
                    throw new UserException(
                        "Custom view with this name already exists.",
                        "DUPLICATE_NAME",
                    );
                }

                const nameAttributes = customNames.CV_NAME
                    ? customNames
                    : await this.findUniqueCloneName(cv.CV_NAME);

                return this.getEntity({
                    ...cv,
                    ...nameAttributes,
                    CV_USER_ID: ownerUserId,
                    IGNORE_PROCESS_RIGHTS: false,
                });
            })
            .then((cv) =>
                // Clone columns
                this.store(cv).then((copyId) => {
                    cv.CV_ID = copyId;
                    newId = copyId;
                    const cvcRepo = globalThis.orm.repo(
                        "CustomViewColumn",
                        this.connection,
                    );
                    return cvcRepo.copy({ CV_ID: oldId }, { CV_ID: newId });
                    // Don't copy CVS (shares) - @t3b-985
                }),
            )
            .then(() => newId);
    }

    /**
     * Generate non-existing cv name like `clone($number+$n)_$cvName` where $n is count of previous clones.
     * > Including translations
     * @param cvName
     * @param number
     * @param lastFoundData
     */
    findUniqueCloneName(cvName, number = 0, lastFoundData = {}) {
        const newName = number ? `clone(${number})_${cvName}` : cvName;
        const nameColumns = this.getEntity().getTranslatedProperties(
            [],
            null,
            false,
        );
        return this.connection
            .first()
            .select(nameColumns)
            .from(this.tableName)
            .where("CV_NAME", newName)
            .then((data) =>
                data
                    ? this.findUniqueCloneName(cvName, number + 1, data)
                    : // Add 'clone(x) to all Name attributes
                      {
                          ...Object.fromEntries(
                              Object.entries(lastFoundData).map(
                                  ([key, value]) => [
                                      key,
                                      value
                                          ? `clone(${number})_${value}`
                                          : value,
                                  ],
                              ),
                          ),
                          CV_NAME: newName,
                      },
            );
    }

    findByName(cvName) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("CV_NAME", cvName);
    }
}
