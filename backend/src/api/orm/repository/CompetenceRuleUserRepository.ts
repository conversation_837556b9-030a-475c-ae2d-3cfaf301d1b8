// @ts-nocheck
import { CompetenceRuleUser } from "../entity/CompetenceRuleUser";
import { BaseRepository } from "./BaseRepository";

export class CompetenceRuleUserRepository extends BaseRepository<CompetenceRuleUser> {
    meta() {
        return {
            tableName: "COMPETENCE_RULE_USERS",
            entityName: "CompetenceRuleUser",
            entity: () => new CompetenceRuleUser(),
        };
    }

    /**
     * Assign competence roles from competence rules.
     * @param competenceId
     * @param competenceRuleId
     * @returns {Promise<void>}
     */
    async copyDefaultsForCompetence(competenceId, competenceRuleId) {
        await this.connection
            .into(
                globalThis.database.raw(
                    `"COMPETENCE_USERS" ("USER_ID", "COMPETENCE_ID", "COMPETENCE_RULE_ID")`,
                ),
            )
            .insert((builder) => {
                builder
                    .select(["USER_ID", competenceId, competenceRuleId])
                    .from(this.tableName)
                    .where("COMPETENCE_RULE_ID", competenceRuleId)
                    .whereNotIn("USER_ID", (b) => {
                        b.select("USER_ID")
                            .from("COMPETENCE_USERS")
                            .where("COMPETENCE_ID", competenceId);
                    });
            });
    }

    /**
     *
     * @param {Array<number>} userIds
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async addUsers(userIds = [], competenceRuleId) {
        if (userIds.length) {
            const competenceIds = await this.connection
                .pluck("COMPETENCE_ID")
                .from("COMPETENCES")
                .where("COMPETENCE_RULE_ID", competenceRuleId);
            const competenceUsers = [];
            for (const competenceId of competenceIds) {
                await globalThis.orm
                    .repo("competence", this.connection)
                    .markForRebuild(competenceId);
                for (const userId of userIds) {
                    competenceUsers.push({
                        COMPETENCE_ID: competenceId,
                        COMPETENCE_RULE_ID: competenceRuleId,
                        USER_ID: userId,
                    });
                }
            }
            await globalThis.database
                .batchInsert("COMPETENCE_USERS", competenceUsers, 10)
                .transacting(this.connection);

            await this.connection.into(this.tableName).insert(
                userIds.map((userId) => ({
                    COMPETENCE_RULE_ID: competenceRuleId,
                    USER_ID: userId,
                })),
            );
        }
    }

    /**
     *
     * @param {Array<number>} userIds
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async removeUsers(userIds = [], competenceRuleId) {
        // Delete all Roles in the list
        await this.connection
            .from("COMPETENCE_USERS")
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .whereIn("USER_ID", Array.isArray(userIds) ? userIds : [userIds])
            .del();

        // Delete all Users in the list
        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .whereIn("USER_ID", Array.isArray(userIds) ? userIds : [userIds])
            .del();
    }

    /**
     *
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async deleteUsers(competenceRuleId) {
        const competenceIds = await this.connection
            .pluck("COMPETENCE_ID")
            .from("COMPETENCES")
            .where("COMPETENCE_RULE_ID", competenceRuleId);
        for (const competenceId of competenceIds) {
            await globalThis.orm
                .repo("competenceUser", this.connection)
                .deleteUsers(competenceId);
            await globalThis.orm
                .repo("competence", this.connection)
                .markForRebuild(competenceId);
        }

        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .del();
    }

    /**
     *
     * @param {Array<number>} userIds
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async setUsers(userIds: number[] = [], competenceRuleId: number) {
        // Remove current Users
        await this.deleteUsers(competenceRuleId);
        // Add new Users
        await this.addUsers(userIds, competenceRuleId);
    }

    /**
     *
     * @param {number} competenceRuleId
     * @returns {Promise<*>}
     */
    async getUsers(competenceRuleId) {
        return await this.connection
            .pluck("USER_ID")
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId);
    }

    async getForCompetence(competenceRuleId) {
        return await this.connection
            .select([
                "CU.USER_ID",
                globalThis.database.raw(
                    `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
                ),
                "U.USER_DISPLAY_NAME",
            ])
            .from(`${this.tableName} as CU`)
            .innerJoin("USERS as U", "U.USER_ID", "CU.USER_ID")
            .where("CU.COMPETENCE_RULE_ID", competenceRuleId);
    }

    /**
     *
     * @param {number} userId
     */
    deleteForUser(userId) {
        return this.connection
            .from(this.tableName)
            .where("USER_ID", userId)
            .delete();
    }

    /**
     *
     * @param {number} userId
     */
    getForUser(userId) {
        return this.connection
            .select("COMPETENCE_RULE_ID")
            .from(this.tableName)
            .where("USER_ID", userId);
    }

    /**
     * Deletes and then clones Competences of the given Users
     *
     * @param sourceUserId
     * @param targetUserId
     * @returns {Promise<void>}
     */
    async cloneCompetences(sourceUserId, targetUserId) {
        // Delete Competences for the target User for exact match (and no insert conflicts)
        await this.deleteForUser(targetUserId);

        // Insert from Select (clone)
        await this.connection
            .into(
                globalThis.database.raw(
                    `"COMPETENCE_USERS" ("COMPETENCE_RULE_ID", "USER_ID")`,
                ),
            )
            .insert((builder) => {
                builder
                    .select([
                        "COMPETENCE_RULE_ID",
                        globalThis.database.raw(targetUserId),
                    ])
                    .from(
                        this.getForUser(sourceUserId).as("SOURCE_COMPETENCES"),
                    );
            });

        // Rebuild affected Competences
        const competenceRepo = globalThis.orm.repo(
            "competence",
            this.connection,
        );
        await competenceRepo.markForRebuild(this.getForUser(sourceUserId));
    }
}
