// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as tprocConsts from "../entity/const/tprocConsts";
import { TemplateTaskLink } from "../entity/TemplateTaskLink";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class TemplateTaskLinkRepository extends BaseRepository<TemplateTaskLink> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_LINKS",
            entityName: "TemplateTaskLink",
            sequenceName: "TTASKLINK_ID",
            entity: () => new TemplateTaskLink(),
        };
    }

    async delete(ids) {
        if (!Array.isArray(ids)) {
            ids = [ids];
        }

        for (const id of ids) {
            try {
                await this.connection
                    .select()
                    .from("TEMPLATE_GRAPH")
                    .where("TGRAPH_TTASKCON_ID", id)
                    .delete();

                await this.connection
                    .select()
                    .from("TEMPLATE_LINK_CONDITIONS")
                    .where("TTASKLINK_ID", id)
                    .delete();

                await this.connection
                    .select()
                    .from("TEMPLATE_TASK_LINKS")
                    .where("TTASKLINK_ID", id)
                    .delete();
            } catch (err) {
                throw err;
            }
        }
    }

    deleteForTemplateProcess(tprocIds, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskLinkRepository.deleteForTemplateProcess)",
            );
        }

        return this.forTemplateProcess(tprocIds, version)
            .collectAll()
            .then((links) => {
                const ids = _.map(links, "TTASKLINK_ID");
                return this.delete(ids);
            });
    }

    async deleteForTTask(ttaskId, version) {
        if (
            (version === null || typeof version === "undefined") &&
            version !== tprocConsts.TPROC_ALL_VERSIONS
        ) {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskLinkRepository.deleteForTTask)",
            );
        }

        if (version === tprocConsts.TPROC_ALL_VERSIONS) {
            await this.connection
                .select()
                .from("TEMPLATE_GRAPH")
                .whereRaw(
                    `
            tgraph_ttaskcon_id in(
              select ttasklink_id
              from template_task_links
              where ttasklink_from_ttask_id = ?
                or ttasklink_to_ttask_id = ?
            )
        `,
                    [ttaskId, ttaskId],
                )
                .delete();

            await this.connection
                .select()
                .from("TEMPLATE_LINK_CONDITIONS")
                .whereRaw(
                    `
            ttasklink_id in(
              select ttasklink_id
              from template_task_links
              where ttasklink_from_ttask_id = ?
                or ttasklink_to_ttask_id = ?
            )
        `,
                    [ttaskId, ttaskId],
                )
                .delete();

            await this.connection
                .select()
                .from(this.tableName)
                .where("TTASKLINK_FROM_TTASK_ID", ttaskId)
                .orWhere("TTASKLINK_TO_TTASK_ID", ttaskId)
                .delete();
        } else {
            throw new InternalException(
                `Remove link only for version ${version} is not implemented yet !. Please fix your code! (TemplateTaskLinkRepository.deleteForTTask)`,
            );
        }
    }

    /**
     * Copy links with new version number. Old links are removed.
     * @param {Number} tprocId
     * @param {Number} srcVersion
     * @param {Number} dstVersion
     * @returns {Promise<boolean>}
     */
    async copyVersion(tprocId, srcVersion, dstVersion) {
        const srcLinks = await this.forTemplateProcess(
            tprocId,
            srcVersion,
        ).collectAll(); // Get source completitions
        await this.deleteForTemplateProcess(tprocId, dstVersion); // Remove old completitions

        // No completitions, do nothing
        if (!Array.isArray(srcLinks) || srcLinks.length === 0) {
            return srcLinks;
        }

        // Copy completitions with new version and id.
        const map = {};
        for (const link of srcLinks) {
            const oldLinkId = link.id;

            link.TTASKLINK_VERSION = dstVersion; // New version
            link.id = null; // New id
            link.makeAllDirty();

            const linkId = await this.store(link);
            map[oldLinkId] = linkId;

            // Copy conditions.
            await globalThis.orm
                .repo("templateLinkCondition", this.connection)
                .copyLinkConditions(tprocId, linkId, oldLinkId);
        }

        return map;
    }

    async outgoingLinks2(ttaskId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskLinkRepository.outgoingLinks2)",
            );
        }

        const links = await this.connection
            .select([
                "TTL.TTASKLINK_ID",
                "TTL.TTASKLINK_IS_MANDATORY",
                "TTL.TTASKLINK_TYPE",
                "TTL.TTASKLINK_TO_TTASK_ID",
            ])
            .from(`${this.tableName} as TTL`)
            .where("TTL.TTASKLINK_FROM_TTASK_ID", ttaskId)
            .where("TTL.TTASKLINK_VERSION", version)
            .orderBy("TTL.TTASKLINK_PRIORITY");

        const ids = _.map(links, "TTASKLINK_ID");
        const conds = await this.connection
            .select([
                "TTC.TTASKLINK_ID",
                "TTC.TCOND_OPERATOR",
                "TTC.TCOND_VALUE",
                "TTC.TCOND_VARIABLE",
                "TTC.TVAR_ID",
            ])
            .from("TEMPLATE_LINK_CONDITIONS as TTC")
            .whereIn("TTASKLINK_ID", Array.isArray(ids) ? ids : [ids]);

        for (const link of links) {
            link.CONDITIONS = _.filter(conds, {
                TTASKLINK_ID: link.TTASKLINK_ID,
            });
        }
        return links;
    }

    forTemplateProcess(tprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskLinkRepository.forTemplateProcess)",
            );
        }

        const coll = this.all(version);
        coll.knex.where("TTL.TPROC_ID", tprocId);
        return coll;
    }

    all(version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskLinkRepository.all)",
            );
        }

        // Add TTASK_NAME translations
        const extraColumns = [];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            const upperLang = lang.toUpperCase();
            extraColumns.push(
                `TTFROM.TTASK_NAME_${upperLang} as TTASKLINK_FROM_TTASK_NAME_${upperLang}`,
            );
            extraColumns.push(
                `TTTO.TTASK_NAME_${upperLang} as TTASKLINK_TO_TTASK_NAME_${upperLang}`,
            );
        });

        const columns = [
            "TTL.*",
            "TTFROM.TTASK_NAME as TTASKLINK_FROM_TTASK_NAME",
            "TTTO.TTASK_NAME as TTASKLINK_TO_TTASK_NAME",
            this.connection.raw(
                `(select count("TCOND_ID") from "TEMPLATE_LINK_CONDITIONS" where "TTASKLINK_ID" = "TTL"."TTASKLINK_ID") as "COND_COUNT"`,
            ),
        ].concat(extraColumns);

        const conn = this.connection
            .select(columns)
            .from(`${this.tableName} as TTL`)
            .leftJoin(
                "TEMPLATE_TASKS as TTFROM",
                "TTL.TTASKLINK_FROM_TTASK_ID",
                "TTFROM.TTASK_ID",
            )
            .leftJoin(
                "TEMPLATE_TASKS as TTTO",
                "TTL.TTASKLINK_TO_TTASK_ID",
                "TTTO.TTASK_ID",
            )
            .where("TTL.TTASKLINK_VERSION", version);

        return globalThis.orm.collection("TemplateTaskLink", conn);
    }

    async create(entity, sourcePort, targetPort, version, conditions) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskLinkRepository.create)",
            );
        }

        entity.ORG_ID = 1;

        try {
            if (entity.id) {
                await this.connection
                    .select()
                    .from("TEMPLATE_GRAPH")
                    .where("TGRAPH_TTASKCON_ID", entity.id)
                    .delete();

                await this.connection
                    .select()
                    .from("TEMPLATE_LINK_CONDITIONS")
                    .where("TTASKLINK_ID", entity.id)
                    .delete();

                await this.connection
                    .select()
                    .from("TEMPLATE_TASK_LINKS")
                    .where("TTASKLINK_ID", entity.id);
            }
            const id = await super.create(entity);
            const graphRepo = globalThis.orm.repo(
                "templateGraph",
                this.connection,
            );
            const graphEntity = graphRepo.getEntity();
            graphEntity.TGRAPH_OBJECT_TYPE = "L";
            graphEntity.TGRAPH_TTASKCON_ID = id;
            graphEntity.TPROC_ID = entity.TPROC_ID;
            graphEntity.TGRAPH_FROM_TTASK_ID = entity.TTASKLINK_FROM_TTASK_ID;
            graphEntity.TGRAPH_TO_TTASK_ID = entity.TTASKLINK_TO_TTASK_ID;
            graphEntity.TGRAPH_LINKSRC_PORT = sourcePort;
            graphEntity.TGRAPH_LINKTGT_PORT = targetPort;
            graphEntity.TGRAPH_VERSION = version;
            graphEntity.ORG_ID = 1;

            await graphRepo.store(graphEntity);
            if (
                conditions &&
                Array.isArray(conditions) &&
                conditions.length > 0
            ) {
                const condRepo = globalThis.orm.repo(
                    "templateLinkCondition",
                    this.connection,
                );
                for (const cond of conditions) {
                    const { entity } = condRepo;

                    if (typeof cond.tcond_variable !== "undefined") {
                        entity.TCOND_VARIABLE = cond.tcond_variable;
                    }
                    if (typeof cond.tcond_value !== "undefined") {
                        entity.TCOND_VALUE = cond.tcond_value;
                    }
                    if (typeof cond.tcond_operator !== "undefined") {
                        entity.TCOND_OPERATOR = cond.tcond_operator;
                    }
                    if (typeof cond.tcond_description !== "undefined") {
                        entity.TCOND_DESCRIPTION = cond.tcond_description;
                    }
                    entity.TTASKLINK_ID = id;
                    entity.ORG_ID = 1;

                    await condRepo.store(entity);
                }
            }
            return id;
        } catch (err) {
            throw err;
        }
    }

    async fillConditions(links) {
        if (!Array.isArray(links) && links.TTASKLINK_ID) {
            links = [links];
        }

        const condRepo = globalThis.orm.repo(
            "templateLinkCondition",
            this.connection,
        );
        const out = [];

        for (const link of links) {
            const conditions = await condRepo.getForLink(link);
            link.conditions = conditions;
            out.push(link);
        }

        return out;
    }
}
