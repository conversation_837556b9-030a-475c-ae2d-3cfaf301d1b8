// @ts-nocheck
// @ts-nocheck
import * as TASK from "../entity/const/taskConst";
import { TaskRepository } from "./TaskRepository";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class WfTaskRepository extends TaskRepository {
    /**
     * Return root of multiinstance, if task is not multiinstance throws exception
     *
     * @access public
     * @return ITask
     */
    getMultiinstanceRoot(_task, process) {
        const coll = this.getCollection();
        coll.knex
            .where("IPROC_ID", process.IPROC_ID)
            .andWhere("ITASK_MULTIINSTANCE_FLAG", TASK.MULTIINSTANCE_FLAG_YES);
        return coll;
    }

    /**
     * Get initial tasks for starting process
     *
     * @param process
     * @param {number} version
     * @returns {BaseCollection}
     */
    getInitialTasks(process, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (doCreateTTask)",
            );
        }

        // Get instance links.
        const coll = this.getCollection(null, "IT");

        if (version === 0) {
            coll.knex
                .leftJoin(
                    "INSTANCE_TASK_LINKS AS ITL",
                    "ITL.ITASKLINK_TO_TTASK_ID",
                    "IT.ITASK_ID",
                )
                .whereNull("ITL.ITASKLINK_TO_TTASK_ID");
        } else {
            coll.knex
                .leftJoin("TEMPLATE_TASK_LINKS AS TTL", (builder) => {
                    builder
                        .on("TTL.TTASKLINK_TO_TTASK_ID", "IT.TTASK_ID")
                        .andOn(
                            "TTL.TTASKLINK_VERSION",
                            globalThis.database.raw("?", [version]),
                        );
                })
                .whereNull("TTL.TTASKLINK_TO_TTASK_ID");
        }

        coll.knex.where("IT.IPROC_ID", process.IPROC_ID);

        return coll;
    }

    /**
     * Count tasks to solve for every possible user.
     * @param usrIds
     */
    getCountToSolveTasks(usrIds) {
        const conn = this.connection
            .select([
                "USER_ID",
                {
                    CNT: this.connection.raw(`COUNT("ITASK_ID")`),
                },
            ])
            .from("USERS")
            .leftJoin("INSTANCE_TASKS AS IT", (sql) => {
                // must include users with no tasks
                sql.on("USER_ID", "=", "ITASK_USER_ID").andOn(
                    "ITASK_STATUS",
                    "=",
                    this.connection.raw("'A'"),
                );
            })
            .leftJoin("INSTANCE_PROCESSES AS IP", (sql) => {
                sql.on("IP.IPROC_ID", "=", "IT.IPROC_ID").andOn(
                    "IPROC_STATUS",
                    "=",
                    this.connection.raw("'A'"),
                );
            })
            .whereIn("USER_ID", Array.isArray(usrIds) ? usrIds : [usrIds])
            .groupBy("USER_ID")
            .orderBy("CNT");

        return this.createCollection(conn);
    }

    /**
     * return active tasks or waiting tasks or to pull tasks
     */
    getTasksToComplete(processId) {
        const coll = this.getCollection();
        coll.knex.where("IPROC_ID", processId).andWhere(function () {
            this.where("ITASK_STATUS", "A")
                .orWhere({ ITASK_STATUS: "L", ITASK_TYPE: "W" })
                .orWhere("ITASK_STATUS", "T");
        });
        return coll;
    }

    getTasksToDueDateSchedule(processId) {
        const coll = this.getCollection();
        coll.knex
            .where("IPROC_ID", processId)
            .where("ITASK_DUE_OFFSET", "like", "vc%");
        return coll;
    }

    /**
     * Returns last or actual (if missing) solver of specific task
     *
     * @param process
     * @param ttaskId
     * @return {Promise<null>}
     */
    async getLastSolver(process, ttaskId) {
        const rows = await globalThis.container.client.database.callKnexRaw(
            `SELECT "ITASK_USER_ID", "ITASK_ACTUAL_DATE_START", 'A' AS "A" FROM "INSTANCE_TASKS"
                    WHERE "IPROC_ID" = :IPROC_ID AND "TTASK_ID" = :TTASK_ID AND "ITASK_STATUS" in ('A', 'L')
                        UNION    ALL
                    SELECT "ITASKH_USER_ID", "ITASKH_ACTUAL_DATE_FINISH" AS "ITASK_ACTUAL_DATE_START", 'B' AS "A" FROM
                            (SELECT "ITH"."ITASKH_USER_ID", "ITH"."ITASKH_ACTUAL_DATE_FINISH" FROM "INSTANCE_TASK_HISTORY" "ITH"
                                JOIN "INSTANCE_TASKS" "IT" ON "ITH"."ITASK_ID" = "IT"."ITASK_ID"
                                WHERE "ITH"."IPROC_ID" = :IPROC_ID AND "IT"."TTASK_ID" = :TTASK_ID
                                    AND "ITH"."ITASKH_USER_ID" IS NOT NULL
                                    AND "ITH"."ITASKH_NOTE" IN (:NOTES)
                                ORDER BY "ITH"."ITASKH_ACTUAL_DATE_FINISH" DESC)
                    ORDER BY 3 DESC, 2 DESC`,
            {
                IPROC_ID: process.IPROC_ID,
                TTASK_ID: ttaskId,
                NOTES: TASK.HISTORY_NOTE_LAST_SOLVER,
            },
            this.connection,
        );

        if (!Array.isArray(rows) || rows.length === 0) {
            return null;
        }
        return rows[0].ITASK_USER_ID;
    }

    getOutgoingTasks(task) {
        const collLinks = this.getCollection("*");
        collLinks.knex
            .join(
                "INSTANCE_TASK_LINKS as IL",
                "ITASK_ID",
                "ITASKLINK_TO_TTASK_ID",
            )
            .where("ITASKLINK_FROM_TTASK_ID", task.ITASK_ID);
        return collLinks;
    }

    /**
     * Get tasks waiting for subprocess.
     * @param {number} iprocId
     */
    getMainProcessWaitingTasks(iprocId) {
        const collection = this.getCollection("*");
        collection.knex.where("ITASK_SUBPROCESS_IPROC_ID", iprocId);
        return collection;
    }
}
