import { BaseRepository } from "./BaseRepository";
import * as CRON from "../entity/const/cronConsts";
import { Cron } from "../entity/Cron";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class CronRepository extends BaseRepository<Cron> {
    meta() {
        return {
            tableName: "CRONS",
            entityName: "Cron",
            entity: () => new Cron(),
        };
    }

    getAllJobs() {
        const repo = this.connection.select().from(this.tableName);
        return globalThis.orm.collection("cron", repo);
    }

    async getById(id: number): Promise<Cron> {
        const repo = this.connection
            .select()
            .from(this.tableName)
            .where("CRON_ID", id);
        return await globalThis.orm.collection("cron", repo).collectOne();
    }

    getActiveJobs() {
        const collection = this.getAllJobs();
        collection.knex.where("CRON_STATUS", CRON.STATUS_ACTIVE);
        return collection;
    }

    async clone(cronId: number) {
        const originalCron = (await this.get(cronId)).raw;

        if (!originalCron) {
            throw new InternalException(
                "Could not find the original Entity to copy!",
                "ENTITY_NOT_FOUND",
                {
                    cronId,
                },
            );
        }

        const cronEntity = this.getEntity(originalCron);
        if (cronEntity.CRON_IS_CLONE === "Y") {
            throw new InternalException(
                "Can not clone a cloned Cron!",
                "CRON_CLONE_CLONING_FORBIDDEN",
            );
        }

        const cloneName = await this.findUniqueCloneName(
            "CRON_NAME",
            originalCron.CRON_NAME,
        );

        // Create a new Entity with 'Disabled' status
        const cloneEntity = this.getEntity({
            ...originalCron,
            CRON_ID: null,
            CRON_ALIAS: null,
            CRON_NAME: cloneName,
            CRON_IS_CLONE: "Y",
            CRON_STATUS: CRON.STATUS_NOT_ACTIVE,
        });

        const cloneId = await this.store(cloneEntity);
        return [cloneId, cloneName];
    }

    // @ts-expect-error different from parent
    async delete(cronId: number) {
        const cronToDelete = (await this.get(cronId)).raw;

        if (!cronToDelete) {
            throw new InternalException(
                "Could not find the cron to delete - it does not exist.",
                "CRON_NOT_FOUND",
                {
                    cronId,
                },
            );
        }

        const cronIsClone = cronToDelete.CRON_IS_CLONE === "Y";
        if (!cronIsClone) {
            throw new InternalException(
                "Deleting non-cloned crons is not allowed!",
                "CRON_CAN_ONLY_DELETE_CLONES",
            );
        }

        await globalThis.database
            .select()
            .from("CRONS")
            .where("CRON_ID", cronId)
            .del();
    }
}
