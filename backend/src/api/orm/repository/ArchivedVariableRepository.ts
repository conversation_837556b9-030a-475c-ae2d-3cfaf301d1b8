import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { Variable } from "../entity/Variable";
import { BaseCollection } from "../BaseCollection";
import { VariableLov } from "../entity/VariableLov";

import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class ArchivedVariableRepository extends BaseRepository<Variable> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_VARIABLES",
            entityName: "archivedVariable",
            defaultAlias: "IV",
            entity: () => new Variable(),
        };
    }

    getByProcess(
        iprocId: number,
        ivarIds: number[] | null = null,
    ): BaseCollection<any> {
        // Map tvar columns
        const tvarEntity = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity();
        let attrs = tvarEntity.attributes();
        attrs = _.omit(attrs, ["DLIST_NAME", "ORG_ID"]);
        let tvarCols: string[] = Object.keys(attrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const ivarEntity = this.getEntity();
        attrs = ivarEntity.attributes();
        attrs = _.omit(attrs, "TVAR_ID");
        let ivarCols = Object.keys(attrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        // @t3b-1595 Zobrazení jazykových mutací LOV proměnných v CO
        // Map tvar lov columns
        const tvarLovCols: string[] = [];
        globalThis.dynamicConfig.langs.forEach((language: string) => {
            tvarLovCols.push(`TVARLOV_TEXT_VALUE_${language.toUpperCase()}`);
        });

        const con = this.connection
            .select(([] as string[]).concat(tvarCols, tvarLovCols, ivarCols))
            .from(`${this.tableName} as IV`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "IV.TVAR_ID", "TV.TVAR_ID")
            .leftJoin("TEMPLATE_VARIABLE_LOV as TVLOV", (builder) => {
                builder
                    .on("IV.TVAR_ID", "TVLOV.TVAR_ID")
                    .andOn("IV.IVAR_TEXT_VALUE", "TVLOV.TVARLOV_TEXT_VALUE");
            })
            .where("IV.IPROC_ID", iprocId);

        // for specific
        if (ivarIds !== null && ivarIds.length > 0) {
            con.whereIn("IV.IVAR_ID", ivarIds);
        }

        return globalThis.orm.collection(this, con);
    }

    prepareCollectionsForVariableList(
        iProcId: number,
    ): BaseCollection<Variable | VariableLov>[] {
        const variablesForProcessCollection = this.getByProcess(iProcId);

        const variableLovRepo = globalThis.orm.repo(
            "archivedVariableLov",
            this.connection,
        );
        const variablesForProcessLovsCollection =
            variableLovRepo.getForVariables(
                // @ts-ignore
                "*",
                variableLovRepo.getVariablesByProcess(iProcId),
            );

        return [
            variablesForProcessCollection,
            variablesForProcessLovsCollection,
        ];
    }

    filterByName(iprocId: number, names: string[]) {
        return (
            globalThis.database
                .select()
                .from(this.tableName)
                // Retrieve all variables for INSTANCE_PROCESS by IPROC_ID
                .where("IPROC_ID", iprocId)
                // Filter the variables - only select the ones being updated
                .whereIn("IVAR_NAME", names)
        );
    }

    // @ts-ignore
    getCollection() {
        // Map tvar columns
        const tvarEntity = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity();
        let attrs = tvarEntity.attributes();
        attrs = _.omit(attrs, ["DLIST_NAME", "ORG_ID"]);
        let tvarCols = Object.keys(attrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const ivarEntity = this.getEntity();
        attrs = ivarEntity.attributes();
        attrs = _.omit(attrs, "TVAR_ID");
        let ivarCols = Object.keys(attrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        const con = this.connection
            .select(["DT.DT_ID"].concat(tvarCols).concat(ivarCols))
            .from(`${this.tableName} as IV`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "IV.TVAR_ID", "TV.TVAR_ID")
            .leftJoin("DYNAMIC_TABLE AS DT", "IV.DLIST_NAME", "DT.DT_NAME");

        return globalThis.orm.collection(this, con);
    }

    async getForTask(
        columns: string[] | string,
        itaskId: number,
        version: number,
    ) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getForTask)",
            );
        }

        if (version <= 0) {
            throw new InternalException(
                "Version must be defined. INSTANCE_TASK_VAR_USAGE is no longer used.",
            );
        }

        // Map tvar entities -
        const tvarEntity = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity();
        let attrs1 = tvarEntity.attributes();
        attrs1 = _.omit(attrs1, ["DLIST_NAME", "ORG_ID", "TVAR_ID"]);
        let tvarCols = Object.keys(attrs1);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const varCols: string[] = [];
        const attrs2 = this.entity.attributes();
        const attrNames = Object.keys(attrs2);
        attrNames.forEach((attrName) => {
            if (attrs2[attrName].translated) {
                varCols.push(`IV.${attrName}`);
            }
        });

        // usage - grid axis
        const axisCols: string[] = [];
        axisCols.push("ITVU.AXIS_X AS AXIS_X", "ITVU.AXIS_Y AS AXIS_Y");

        const cols = [
            "IVS.IVAR_ID AS IVAR_ID",
            "IV.TVAR_ID AS TVAR_ID",
            "IV.IPROC_ID AS IPROC_ID",
            "IVS.IVAR_DT_INDEX AS IVAR_DT_INDEX",
            "IV.IVAR_COL_INDEX AS IVAR_COL_INDEX",
            "IV.IVAR_NAME AS IVAR_NAME",
            "IV.IVAR_TYPE AS IVAR_TYPE",
            "IVS.IVARSN_TEXT_VALUE AS IVAR_TEXT_VALUE",
            "IVS.IVARSN_NUMBER_VALUE AS IVAR_NUMBER_VALUE",
            "IVS.IVARSN_DATE_VALUE AS IVAR_DATE_VALUE",
            "IV.IVAR_ATTRIBUTE AS IVAR_ATTRIBUTE",
            `ITVU.TTASKVARUSG_USAGE AS IVAR_USAGE`,
            "IV.DLIST_NAME AS DLIST_NAME",
            "IVS.IVARSN_MULTI AS IVAR_MULTI",
            "IVS.IVARSN_MULTI_SELECTED AS IVAR_MULTI_SELECTED",
            "IVS.IVARSN_BIG_VALUE AS IVAR_BIG_VALUE",
            "IVS.ITASK_ID AS ITASK_ID",
            `ITVU.TTASKVARUSG_ID AS ITASKVARUSG_ID`,
            "DT.DT_ID",
        ]
            .concat(tvarCols)
            .concat(varCols)
            .concat(axisCols);

        //@ts-expect-error - in InstanceTasksController columns is a *
        const coll = BaseRepository.prototype.getCollection.call(this, columns); // Workaround to call super.getCollection(). See https://github.com/babel/babel/issues/3930.
        coll.knex.from(function () {
            // Using templates only
            // @ts-ignore
            this.select(cols)
                .from("ARCH_INSTANCE_TASKS AS IT")
                .join(
                    "TEMPLATE_TASK_VAR_USAGE AS ITVU",
                    "ITVU.TTASK_ID",
                    "IT.TTASK_ID",
                )
                .join("TEMPLATE_VARIABLES AS TV", "ITVU.TVAR_ID", "TV.TVAR_ID")
                .join("ARCH_INSTANCE_VARIABLES AS IV", {
                    "TV.TVAR_ID": "IV.TVAR_ID",
                    "IT.IPROC_ID": "IV.IPROC_ID",
                })
                .join("ARCH_INSTANCE_VARIABLES_SNAP AS IVS", {
                    "IVS.IVAR_ID": "IV.IVAR_ID",
                    "IVS.ITASK_ID": "IT.ITASK_ID",
                })
                .leftJoin("DYNAMIC_TABLE AS DT", "IV.DLIST_NAME", "DT.DT_NAME")
                .where("IT.ITASK_ID", itaskId)
                .where("IT.ITASK_MULTIINSTANCE_FLAG", "M")
                .where("ITVU.TTASKVARUSG_VERSION", version)
                .unionAll(function () {
                    // @ts-ignore
                    this.select(
                        [
                            "IV.IVAR_ID AS IVAR_ID",
                            "IV.TVAR_ID AS TVAR_ID",
                            "IV.IPROC_ID AS IPROC_ID",
                            "IV.IVAR_DT_INDEX AS IVAR_DT_INDEX",
                            "IV.IVAR_COL_INDEX AS IVAR_COL_INDEX",
                            "IV.IVAR_NAME AS IVAR_NAME",
                            "IV.IVAR_TYPE AS IVAR_TYPE",
                            "IV.IVAR_TEXT_VALUE AS IVAR_TEXT_VALUE",
                            "IV.IVAR_NUMBER_VALUE AS IVAR_NUMBER_VALUE",
                            "IV.IVAR_DATE_VALUE AS IVAR_DATE_VALUE",
                            "IV.IVAR_ATTRIBUTE AS IVAR_ATTRIBUTE",
                            "ITVU.TTASKVARUSG_USAGE AS IVAR_USAGE",
                            "IV.DLIST_NAME AS DLIST_NAME",
                            "IV.IVAR_MULTI AS IVAR_MULTI",
                            "IV.IVAR_MULTI_SELECTED AS IVAR_MULTI_SELECTED",
                            "IV.IVAR_BIG_VALUE AS IVAR_BIG_VALUE",
                            "IT.ITASK_ID AS ITASK_ID",
                            "ITVU.TTASKVARUSG_ID AS ITASKVARUSG_ID",
                            "DT.DT_ID",
                        ]
                            .concat(tvarCols)
                            .concat(varCols)
                            .concat(axisCols),
                    )
                        .from("ARCH_INSTANCE_TASKS AS IT")
                        .join(
                            "TEMPLATE_TASK_VAR_USAGE as ITVU",
                            "ITVU.TTASK_ID",
                            "IT.TTASK_ID",
                        )
                        .join(
                            "TEMPLATE_VARIABLES AS TV",
                            "ITVU.TVAR_ID",
                            "TV.TVAR_ID",
                        )
                        .join("ARCH_INSTANCE_VARIABLES AS IV", {
                            "TV.TVAR_ID": "IV.TVAR_ID",
                            "IT.IPROC_ID": "IV.IPROC_ID",
                        })
                        .leftJoin(
                            "DYNAMIC_TABLE AS DT",
                            "IV.DLIST_NAME",
                            "DT.DT_NAME",
                        )
                        .where("IT.ITASK_ID", itaskId)
                        .where("ITVU.TTASKVARUSG_VERSION", version)
                        .where("IT.ITASK_MULTIINSTANCE_FLAG", "<>", "M");
                })
                .as("ignored_alias");
        });
        return coll;
    }

    getForEvent(itaskId: number) {
        // Map tvar entities -
        const tvarEntity = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity();
        let attrs1 = tvarEntity.attributes();
        attrs1 = _.omit(attrs1, ["DLIST_NAME", "ORG_ID", "TVAR_ID"]);
        let tvarCols = Object.keys(attrs1);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar columns
        const varCols: string[] = [];
        const attrs2 = this.entity.attributes();
        const attrNames = Object.keys(attrs2);
        attrNames.forEach((attrName) => {
            if (attrs2[attrName].translated) {
                varCols.push(`IV.${attrName}`);
            }
        });

        const columns = [
            "IV.IVAR_ID",
            "IV.TVAR_ID",
            "IV.IVAR_NAME",
            "IV.IVAR_TYPE",
            "IV.IVAR_TEXT_VALUE",
            "IV.IVAR_NUMBER_VALUE",
            "IV.IVAR_DATE_VALUE",
            "IV.IVAR_BIG_VALUE",
            "IV.IVAR_ATTRIBUTE",
            "IV.DLIST_NAME",
            "IV.IVAR_DT_INDEX AS IVAR_DT_INDEX",
            "IV.IVAR_COL_INDEX AS IVAR_COL_INDEX",
            "IT.ITASK_ID",
        ]
            .concat(tvarCols)
            .concat(varCols);

        return globalThis.orm.collection(
            "archivedVariable",
            this.connection
                .select(columns)
                .from("ARCH_INSTANCE_TASKS as IT")
                .join(
                    "EVENT_DEFINITION as ED",
                    "ED.EVEDEF_NAME",
                    "IT.ITASK_EVENT",
                )
                .joinRaw(
                    `JOIN "RULE_DEFINITION" "RD" ON "RD"."EVEDEF_ID" = "ED"."EVEDEF_ID" AND "RD"."RDEF_VALUE" = '$EVEW_' ${globalThis.orm.db.concat()} "IT"."TTASK_ID"`,
                )
                .join("RULE_DEFINITION_PARAM as RP", "RP.RDEF_ID", "RD.RDEF_ID")
                .joinRaw(
                    `JOIN "ARCH_INSTANCE_VARIABLES" "IV" ON "IV"."IPROC_ID" = "IT"."IPROC_ID" AND "IV"."TVAR_ID" = "RP"."TVAR_ID_DEST"`,
                )
                .joinRaw(
                    `JOIN "TEMPLATE_VARIABLES" "TV" ON "IV"."TVAR_ID" = "TV"."TVAR_ID"`,
                )
                .where("IT.ITASK_ID", itaskId),
        );
    }

    getByTvarId(iprocId: number, tvarId: number) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("TVAR_ID", tvarId);
        return globalThis.orm
            .collection("archivedVariable", conn)
            .collectAll()
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    throw new UserException(
                        `Variable with iproc_id=${iprocId} and tvar_id=${tvarId} not found.`,
                    );
                }
                return rows[0];
            });
    }

    getByTvarName(iprocId: number, tvarName: string) {
        const conn = this.connection
            .select()
            .from(`${this.tableName} as IV`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "TV.TVAR_ID", "IV.TVAR_ID")
            .where("IV.IPROC_ID", iprocId)
            .where("TV.TVAR_NAME", tvarName);
        return globalThis.orm
            .collection("archivedVariable", conn)
            .collectAll()
            .then((rows) => {
                if (!Array.isArray(rows) || rows.length === 0) {
                    throw new UserException(
                        `Variable with iproc_id=${iprocId} and tvar_name=${tvarName} not found.`,
                    );
                }
                return rows[0];
            });
    }

    async updateVariablesDListName(oldDListName: string, newDListName: string) {
        return await this.connection
            .select()
            .from(`${this.tableName}`)
            // @ts-ignore
            .whereIn(
                "DLIST_NAME",
                Array.isArray(oldDListName) ? oldDListName : [oldDListName],
            )
            .update({ DLIST_NAME: newDListName });
    }

    getByActiveProcesses(tprocId: number) {
        const conn = this.connection
            .select("IV.*")
            .from(`${this.tableName} as IV`)
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES as IP",
                "IP.IPROC_ID",
                "IV.IPROC_ID",
            )
            .where("IP.TPROC_ID", tprocId)
            .where("IP.IPROC_STATUS", "A");
        return globalThis.orm.collection("archivedVariable", conn);
    }

    getCollectionForProcess(
        iprocId: number,
        columns: any[],
        alias: string,
        extraColumns: any[],
    ): BaseCollection<Variable> {
        const coll = super.getCollection(columns, alias, extraColumns);
        coll.knex.where("IPROC_ID", iprocId);
        return coll;
    }
}
