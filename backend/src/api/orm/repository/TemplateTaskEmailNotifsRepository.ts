// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as NOTIFICATION from "../entity/const/notificationConsts";
import { TemplateTaskEmailNotifs } from "../entity/TemplateTaskEmailNotifs";

export class TemplateTaskEmailNotifsRepository extends BaseRepository<TemplateTaskEmailNotifs> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_EMAIL_NOTIFS",
            entityName: "TemplateTaskEmailNotifs",
            entity: () => new TemplateTaskEmailNotifs(),
        };
    }

    getForTTask(ttaskId) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId);
        return globalThis.orm.collection("TemplateTaskEmailNotifs", conn);
    }

    deleteForTTask(ttaskId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId)
            .delete();
    }

    async setTTaskEmailNotification(ttask_id, obj) {
        try {
            const { entity } = this;

            entity.TTASK_ID = ttask_id;
            let anyChange = false;

            Object.keys(obj).forEach((key) => {
                if (obj[key]) {
                    entity[key] = obj[key];
                    anyChange = true;
                }
            });

            if (!anyChange) {
                return true;
            }

            return await this.connection.transaction(async (trx) => {
                await this.deleteForTTask(ttask_id).transacting(trx);

                const types = ["TGT", "REPLY", "BLIND", "COPY"];
                for (const type of types) {
                    if (
                        obj[`TTASK_ENOT_${type}_TYPE`] ===
                        NOTIFICATION.TYPE_USER_NAME
                    ) {
                        const tvarRepo = globalThis.orm.repo(
                            "templateVariable",
                            this.connection,
                        );
                        const tVar = await tvarRepo.get(
                            obj[`TTASK_ENOT_${type}_TVAR_ID`],
                        );

                        const targetAttr =
                            type === "TGT"
                                ? "TTASK_ENOT_TGT"
                                : `TTASK_ENOT_${type}_TARGET`;
                        entity[targetAttr] = tVar.TVAR_NAME;
                    }
                }

                [
                    "TTASK_ENOT_TGT",
                    "TTASK_ENOT_BLIND_TARGET",
                    "TTASK_ENOT_REPLY_TARGET",
                    "TTASK_ENOT_COPY_TARGET",
                ].forEach((attr) => {
                    if (entity[attr] && Array.isArray(entity[attr])) {
                        entity[attr] = entity[attr].join(";");
                    }
                });

                // Store entity
                const result = await trx(this.tableName).insert(
                    entity.getAttributes(true),
                );
                // Update large body.
                return result;
            });
        } catch (err) {
            globalThis.tasLogger.error(
                `Error in TemplateTaskEmailNotifsRepository.setTTaskEmailNotification(): ${err.message}`,
                err,
            );
            throw err;
        }
    }
}
