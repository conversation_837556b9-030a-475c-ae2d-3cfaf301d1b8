import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { InstanceVariableHistory } from "../entity/InstanceVariableHistory";
import { ITask, Task } from "../entity/Task";
import { BaseCollection } from "../BaseCollection";

export class InstanceVariableHistoryRepository extends BaseRepository<InstanceVariableHistory> {
    meta() {
        return {
            tableName: "INSTANCE_VARIABLE_HISTORY",
            entityName: "InstanceVariableHistory",
            entity: () => new InstanceVariableHistory(),
            archived: true,
            archParams: {
                subQueryTable: "INSTANCE_TASK_HISTORY",
                subQueryColumn: "ITASKH_ID",
            },
        };
    }

    async getForProcess(
        iprocId: number,
        tvarNames?: string[],
    ): Promise<BaseCollection<InstanceVariableHistory>> {
        const version = await globalThis.orm
            .repo("instanceProcessVersion")
            .getProcessVersion(iprocId);
        const coll = this.all(version.TTASKVARUSG_VERSION || 0);
        coll.knex.where("IT.IPROC_ID", iprocId);
        coll.knex.where("IV.IPROC_ID", iprocId);

        if (!_.isEmpty(tvarNames)) {
            coll.knex = coll.knex.whereIn(
                // @ts-ignore
                "TV.TVAR_NAME",
                Array.isArray(tvarNames) ? tvarNames : [tvarNames],
            );
        }
        return coll;
    }

    async getForTask(
        itaskId: number,
        itaskhId: number | null = null,
        tvarNames: any[] = [],
    ): Promise<BaseCollection<InstanceVariableHistory>> {
        const task = await globalThis.orm
            .repo("instanceTask", this.connection)
            .get(itaskId);
        const coll = await this.getForProcess(task.IPROC_ID);
        if (itaskhId) {
            coll.knex = coll.knex.where("ITH.ITASKH_ID", itaskhId);
        }
        // Due to progressHistory
        if (!_.isEmpty(tvarNames)) {
            coll.knex = coll.knex.whereIn(
                "TV.TVAR_NAME",
                Array.isArray(tvarNames) ? tvarNames : [tvarNames],
            );
        }
        coll.knex
            .orderBy("AXIS_Y", "asc")
            .orderBy("AXIS_X", "asc")
            .orderBy("TTASKVARUSG_ID", "asc"); // version?

        return coll;
    }

    async getLastHistoryForTask(itaskId: number): Promise<any> {
        return await this.connection
            .max("ITASKH_ID as max_ITASKH_ID")
            .from("INSTANCE_TASK_HISTORY")
            .where("ITASK_ID", itaskId)
            .where("ITASKH_NOTE", "Task finished")
            .then((res) => {
                if ("max_ITASKH_ID" in res[0]) {
                    return res[0].max_ITASKH_ID;
                }
                return null;
            });
    }

    all(version = 0): BaseCollection<InstanceVariableHistory> {
        // Map tvar entities -
        const tvarEntity = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity();
        let tvarAttrs = tvarEntity.attributes();
        tvarAttrs = _.omit(tvarAttrs, ["DLIST_NAME", "TVAR_ID", "ORG_ID"]);
        let tvarCols = Object.keys(tvarAttrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar entities -
        const ivarEntity = globalThis.orm
            .repo("variable", this.connection)
            .getEntity();
        let ivarAttrs = ivarEntity.attributes();
        ivarAttrs = _.omit(ivarAttrs, ["ORG_ID"]);
        let ivarCols = Object.keys(ivarAttrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        // usage - grid axis
        const axisCols = [];
        if (version > 0) {
            axisCols.push("TTVU.AXIS_X AS AXIS_X", "TTVU.AXIS_Y AS AXIS_Y");
        }

        const conn = this.connection
            .select(
                [
                    "IT.ITASK_NAME",
                    "IT.TTASK_ID",
                    "IVH.IVARH_TEXT_VALUE",
                    "IVH.IVARH_MULTI_SELECTED",
                    "IVH.IVARH_NUMBER_VALUE",
                    "IVH.IVARH_DATE_VALUE",
                    "IVH.IVARH_BIG_VALUE",
                    "ITH.ITASKH_ID",
                    "ITH.ITASKH_ACTUAL_DATE_START",
                    "ITH.ITASKH_ACTUAL_DATE_FINISH",
                    "ITH.ITASKH_DUE_DATE_START",
                    "ITH.ITASKH_DUE_DATE_FINISH",
                    "ITH.ITASKH_FINISHED_BY_USER_ID",
                    globalThis.database.raw(
                        `"UF"."USER_DISPLAY_NAME" "ITASKH_FINISHED_BY_USER"`,
                    ),
                    "ITH.ITASKH_USER_ID",
                    globalThis.database.raw(
                        `"UO"."USER_DISPLAY_NAME" "ITASKH_USER"`,
                    ),
                    "ITH.ITASKH_NOTE",
                    "ITH.ITASK_ID",
                    "TTVU.TTASKVARUSG_ID AS TTASKVARUSG_ID",
                ]
                    .concat(tvarCols)
                    .concat(ivarCols)
                    .concat(axisCols),
            )
            .from("INSTANCE_TASK_HISTORY as ITH")
            .leftJoin(
                "INSTANCE_VARIABLE_HISTORY as IVH",
                "ITH.ITASKH_ID",
                "IVH.ITASKH_ID",
            )
            .leftJoin("INSTANCE_VARIABLES as IV", "IVH.IVAR_ID", "IV.IVAR_ID")
            .leftJoin("INSTANCE_TASKS as IT", "IT.ITASK_ID", "ITH.ITASK_ID")
            .leftJoin("USERS as UO", "UO.USER_ID", "ITH.ITASKH_USER_ID")
            .leftJoin("TEMPLATE_VARIABLES as TV", "TV.TVAR_ID", "IV.TVAR_ID")
            .leftJoin(
                "USERS as UF",
                "UF.USER_ID",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
            )
            .leftJoin("TEMPLATE_TASK_VAR_USAGE AS TTVU", function () {
                this.on("IV.TVAR_ID", "TTVU.TVAR_ID");
                this.on("IT.TTASK_ID", "TTVU.TTASK_ID");
            })
            .whereNotNull("IV.IVAR_ID")
            .orderBy("IVH.IVARH_ID", "asc");

        return globalThis.orm.collection("instanceVariableHistory", conn);
    }

    async generateForTask(
        task: ITask | Task,
        variables: any[],
        note: string,
        primaryUserId?: number | null,
        finishedUserId?: number | null,
        actualDate = false,
        forcedUserId: number | null = null,
    ): Promise<any> {
        // Create TASK history record
        const iTaskHistoryRepo = globalThis.orm.repo(
            "instanceTaskHistory",
            this.connection,
        );
        const iTaskHistoryId = await iTaskHistoryRepo.generateRecord(
            task,
            actualDate,
            note,
            primaryUserId,
            finishedUserId,
            forcedUserId,
        );
        return await this.bulkInsert(variables, iTaskHistoryId);
    }

    // @ts-ignore
    async bulkInsert(
        variables: any[],
        id: number | number[] | null,
    ): Promise<any> {
        // @t3b-1196 Chyba pri generovani historie
        // const toInsert = variables.map(item => {
        //     return {
        //         ORG_ID: 1,
        //         ITASKH_ID: id,
        //         IVAR_ID: item.IVAR_ID,
        //         IVARH_TEXT_VALUE: item.IVAR_TEXT_VALUE,
        //         IVARH_NUMBER_VALUE: item.IVAR_NUMBER_VALUE,
        //         IVARH_DATE_VALUE: item.IVAR_DATE_VALUE,
        //         IVARH_MULTI: item.IVAR_MULTI,
        //         IVARH_MULTI_SELECTED: item.IVAR_MULTI_SELECTED,
        //         IVARH_BIG_VALUE: item.IVAR_BIG_VALUE,
        //     };
        // });
        // return super.bulkInsert(toInsert);

        const entities = variables.map((item) => {
            const data = {
                ORG_ID: 1,
                ITASKH_ID: id,
                IVAR_ID: item.IVAR_ID,
                IVARH_TEXT_VALUE: item.IVAR_TEXT_VALUE,
                IVARH_NUMBER_VALUE: item.IVAR_NUMBER_VALUE,
                IVARH_DATE_VALUE: item.IVAR_DATE_VALUE,
                IVARH_MULTI: item.IVAR_MULTI,
                IVARH_MULTI_SELECTED: item.IVAR_MULTI_SELECTED,
                IVARH_BIG_VALUE: item.IVAR_BIG_VALUE,
            };

            return this.getEntity(data);
        });

        const storedEntities = [];
        for (const entity of entities) {
            const storedEntity = await this.store(entity);
            storedEntities.push(storedEntity);
        }
        return storedEntities;
    }
}
