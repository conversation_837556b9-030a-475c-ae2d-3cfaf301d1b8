// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as ROLE from "../entity/const/roleConst";
import { UserRole } from "../entity/UserRole";

export class UserRoleRepository extends BaseRepository<UserRole> {
    meta() {
        return {
            tableName: "USER_ROLES",
            entityName: "UserRole",
            entity: () => new UserRole(),
        };
    }

    getForCompetence(competenceId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("ROLE_SOURCE", ROLE.SOURCE_COMPETENCE)
            .where("ROLE_COMPETENCE_ID", competenceId);
    }

    async clearRole(roleId) {
        const usersToRemoveRole = await this.connection
            .pluck("USER_ID")
            .from("USER_ROLES")
            .where({ ROLE_ID: roleId });

        await this.connection
            .from("USER_ROLES")
            .where({ ROLE_ID: roleId })
            .delete();

        for (const userId of usersToRemoveRole) {
            await globalThis.container.service.temporary.cacheModule.invalidateCachedUser(
                userId,
            );
        }
    }

    deleteForUserByCompetence(USER_ID, ROLE_COMPETENCE_ID) {
        return globalThis.container.client.database.callKnexRaw(
            `
                        DELETE FROM "USER_ROLES"
                        WHERE "USER_ID" = :USER_ID
                        AND "ROLE_COMPETENCE_ID" = :ROLE_COMPETENCE_ID`,
            {
                USER_ID,
                ROLE_COMPETENCE_ID,
            },
            this.connection,
        );
    }

    async deleteForSource(source) {
        const usersAndRolesToRemove = await this.connection
            .select(["USER_ID", "ROLE_ID"])
            .from("USER_ROLES")
            .where({ ROLE_SOURCE: source });

        await this.connection
            .from("USER_ROLES")
            .where({ ROLE_SOURCE: source })
            .delete();

        const usersToRemoveRoles = _.groupBy(usersAndRolesToRemove, "USER_ID");
        for (const [userId] of Object.entries(usersToRemoveRoles)) {
            await globalThis.container.service.temporary.cacheModule.invalidateCachedUser(
                userId,
            );
        }
    }

    async store(entity, force) {
        const result = await super.store(entity, force);
        await globalThis.container.service.temporary.cacheModule.invalidateCachedUser(
            entity.USER_ID,
        );
        return result;
    }
}
