import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { InstanceVariableHistory } from "../entity/InstanceVariableHistory";
import { BaseCollection } from "../BaseCollection";

export class ArchivedInstanceVariableHistoryRepository extends BaseRepository<InstanceVariableHistory> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_VARIABLE_HISTORY",
            entityName: "archivedInstanceVariableHistory",
            entity: () => new InstanceVariableHistory(),
            archParams: {
                subQueryTable: "ARCH_INSTANCE_TASK_HISTORY",
                subQueryColumn: "ITASKH_ID",
            },
        };
    }

    async getForProcess(
        iprocId: number,
        tvarNames: string[],
    ): Promise<BaseCollection<InstanceVariableHistory>> {
        const version = await globalThis.orm
            .repo("archivedInstanceProcessVersion")
            .getProcessVersion(iprocId);
        const coll = this.all(version.TTASKVARUSG_VERSION || 0);
        coll.knex.where("IT.IPROC_ID", iprocId);
        coll.knex.where("IV.IPROC_ID", iprocId);

        if (!_.isEmpty(tvarNames)) {
            coll.knex = coll.knex.whereIn("TV.TVAR_NAME", tvarNames);
        }
        return coll;
    }

    all(version = 0): BaseCollection<InstanceVariableHistory> {
        // Map tvar entities -
        const tvarEntity = globalThis.orm
            .repo("templateVariable", this.connection)
            .getEntity();
        let tvarAttrs = tvarEntity.attributes();
        tvarAttrs = _.omit(tvarAttrs, ["DLIST_NAME", "TVAR_ID", "ORG_ID"]);
        let tvarCols = Object.keys(tvarAttrs);
        tvarCols = tvarCols.map((el) => `TV.${el}`);

        // Map ivar entities -
        const ivarEntity = globalThis.orm
            .repo("archivedVariable", this.connection)
            .getEntity();
        let ivarAttrs = ivarEntity.attributes();
        ivarAttrs = _.omit(ivarAttrs, ["ORG_ID"]);
        let ivarCols = Object.keys(ivarAttrs);
        ivarCols = ivarCols.map((el) => `IV.${el}`);

        // usage - grid axis
        const axisCols = [];
        if (version > 0) {
            axisCols.push("TTVU.AXIS_X AS AXIS_X", "TTVU.AXIS_Y AS AXIS_Y");
        }

        const conn = this.connection
            .select(
                [
                    "IT.ITASK_NAME",
                    "IT.TTASK_ID",
                    "IVH.IVARH_TEXT_VALUE",
                    "IVH.IVARH_MULTI_SELECTED",
                    "IVH.IVARH_NUMBER_VALUE",
                    "IVH.IVARH_DATE_VALUE",
                    "IVH.IVARH_BIG_VALUE",
                    "ITH.ITASKH_ID",
                    "ITH.ITASKH_ACTUAL_DATE_START",
                    "ITH.ITASKH_ACTUAL_DATE_FINISH",
                    "ITH.ITASKH_DUE_DATE_START",
                    "ITH.ITASKH_DUE_DATE_FINISH",
                    "ITH.ITASKH_FINISHED_BY_USER_ID",
                    globalThis.database.raw(
                        `"UF"."USER_DISPLAY_NAME" "ITASKH_FINISHED_BY_USER"`,
                    ),
                    "ITH.ITASKH_USER_ID",
                    globalThis.database.raw(
                        `"UO"."USER_DISPLAY_NAME" "ITASKH_USER"`,
                    ),
                    "ITH.ITASKH_NOTE",
                    "ITH.ITASK_ID",
                    "TTVU.TTASKVARUSG_ID AS TTASKVARUSG_ID",
                ]
                    .concat(tvarCols)
                    .concat(ivarCols)
                    .concat(axisCols),
            )
            .from("ARCH_INSTANCE_TASK_HISTORY as ITH")
            .leftJoin(
                "ARCH_INSTANCE_VARIABLE_HISTORY as IVH",
                "ITH.ITASKH_ID",
                "IVH.ITASKH_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_VARIABLES as IV",
                "IVH.IVAR_ID",
                "IV.IVAR_ID",
            )
            .leftJoin(
                "ARCH_INSTANCE_TASKS as IT",
                "IT.ITASK_ID",
                "ITH.ITASK_ID",
            )
            .leftJoin("USERS as UO", "UO.USER_ID", "ITH.ITASKH_USER_ID")
            .leftJoin("TEMPLATE_VARIABLES as TV", "TV.TVAR_ID", "IV.TVAR_ID")
            .leftJoin(
                "USERS as UF",
                "UF.USER_ID",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
            )
            .leftJoin("TEMPLATE_TASK_VAR_USAGE AS TTVU", function () {
                this.on("IV.TVAR_ID", "TTVU.TVAR_ID");
                this.on("IT.TTASK_ID", "TTVU.TTASK_ID");
            })
            .whereNotNull("IV.IVAR_ID")
            .orderBy("IVH.IVARH_ID", "asc");

        return globalThis.orm.collection("instanceVariableHistory", conn);
    }
}
