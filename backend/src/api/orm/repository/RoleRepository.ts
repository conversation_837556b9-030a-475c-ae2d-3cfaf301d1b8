// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as ROLES from "../entity/const/roleConst";
import { Role } from "../entity/Role";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { User } from "../entity/User";

export class RoleRepository extends BaseRepository<Role> {
    meta() {
        return {
            tableName: "ROLES",
            entityName: "Role",
            defaultAlias: "R",
            entity: () => new Role(),
        };
    }

    async setRoleList(roleId, userIds: number[] = [], roleSource) {
        // Make sure Users are integers
        const newUserIds = userIds.map((userId) => Number(userId));

        // Get current Users of the Role
        const userRepo = globalThis.orm.repo("user", this.connection);
        const userRoleRepo = globalThis.orm.repo("userRole", this.connection);
        const currentUserIds = await userRepo
            .getUserListByRole({ ROLE_ID: roleId }, false)
            .pluck("USER_ID");

        // Determine if a User Role is to be deleted, inserted or ignored
        const userIdsToInsert = _.difference(newUserIds, currentUserIds);
        const userIdsToDelete = _.difference(currentUserIds, newUserIds);

        // Delete User Roles
        await this.connection
            .from("USER_ROLES")
            .where("ROLE_ID", roleId)
            .whereNot("ROLE_SOURCE", ROLES.SOURCE_COMPETENCE)
            .whereIn("USER_ID", userIdsToDelete)
            .del();

        // Insert User Roles
        for (const userId of userIdsToInsert) {
            const userRoleEntity = userRoleRepo.getEntity({
                USER_ID: userId,
                ROLE_ID: roleId,
                ROLE_SOURCE: roleSource,
            });

            await userRoleRepo.store(userRoleEntity);
        }

        await this.checkRoleAssignsCount(roleId);

        return {
            userIdsToInsert,
            userIdsToDelete,
        };
    }

    async delete(entity) {
        await this.connection
            .delete()
            .from("USER_ROLES")
            .where("ROLE_ID", entity.ROLE_ID);
        await this.connection
            .delete()
            .from("ROLES")
            .where("ROLE_ID", entity.ROLE_ID);
    }

    /**
     * Get role by name
     * @param roleName
     */
    async getByName(roleName): Promise<User | null> {
        if (!roleName) {
            return null;
        }

        const conn = this.connection
            .select()
            .from("ROLES")
            .where("ROLE_NAME", roleName);
        const roles = await globalThis.orm
            .collection("Role", conn)
            .collectAll();
        if (!Array.isArray(roles) || roles.length === 0) {
            return null;
        }
        return roles[0];
    }

    /**
     * Get all roles for userId.
     *
     * @param columns
     * @param userId
     * @returns {*}
     */
    getForUser(userId, columns) {
        const list = this.getCollection(columns, "RO", ["UR.ROLE_SOURCE"]);

        list.knex
            .leftJoin("USER_ROLES AS UR", "RO.ROLE_ID", "UR.ROLE_ID")
            .where("UR.USER_ID", userId);
        return list;
    }

    /**
     * Get all roles and user roles.
     *
     * @returns {*}
     */
    getRolesAndUserRoles() {
        const conn = this.connection
            .distinct([
                "R.ROLE_ID",
                "R.ROLE_NAME",
                "R.ROLE_CATEGORY",
                "R.ROLE_NOTE",
                this.connection.raw(
                    `CASE WHEN "U"."USER_STATUS" = 'D' THEN NULL ELSE "U"."USER_ID" END as "USER_ID"`,
                ),
                this.connection.raw(
                    `CASE WHEN "U"."USER_STATUS" = 'D' THEN NULL ELSE "U"."USER_NAME" END as "USER_NAME"`,
                ),
                this.connection.raw(
                    `CASE WHEN "U"."USER_STATUS" = 'D' THEN NULL ELSE "U"."EXTERNAL_ID" END as "EXTERNAL_ID"`,
                ),
                "U.USER_STATUS",
            ])
            .from("ROLES as R")
            .leftJoin("USER_ROLES as UR", "R.ROLE_ID", "UR.ROLE_ID")
            .leftJoin("USERS as U", "UR.USER_ID", "U.USER_ID");

        return conn;
    }

    /**
     *
     * @param roleName
     * @param roleCategory
     * @returns Role
     */
    createIfNotExists(roleName, roleCategory) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("ROLE_NAME", roleName)
            .where("ROLE_CATEGORY", roleCategory)
            .then((roles) => {
                // Role found -> return as is.
                if (Array.isArray(roles) && roles.length > 0) {
                    const entity = this.getEntity();
                    entity.fill(roles[0]);

                    // Block Role calls to locked Roles
                    if (
                        (entity.id < 0 && entity.id !== ROLES.ALL_USERS) ||
                        entity.ROLE_ACCESS_ROLE_ID
                    ) {
                        throw new InternalException(
                            `Role '${roleName}' is locked. It cannot be called in calculations.`,
                            "LACK_OF_PERMISSIONS",
                        );
                    }

                    return entity;
                }

                // Role does not exists -> create and return.
                const entity = this.getEntity({
                    ROLE_NAME: roleName,
                    ROLE_CATEGORY: roleCategory,
                });
                return this.store(entity).then((id) => {
                    entity.id = id;
                    return entity;
                });
            });
    }

    async clone(id) {
        if (id < 0) {
            throw new UserException("System roles cannot be copied.");
        }

        try {
            const role = await this.get(id);
            const oldId = role.ROLE_ID;

            // Clear id to create new copy of role
            role.ROLE_ID = null;
            role.makeAllDirty();
            role.ROLE_NAME = await this.findUniqueCloneName(
                "ROLE_NAME",
                role.ROLE_NAME,
            );
            const newId = await this.store(role, false);

            // Clone users
            const userRoleRepo = globalThis.orm.repo(
                "UserRole",
                this.connection,
            );
            await userRoleRepo.copy({ ROLE_ID: oldId }, { ROLE_ID: newId });
            return newId;
        } catch (e) {
            throw new InternalException(e);
        }
    }

    async checkRoleAssignsCount(roleId) {
        const { ROLE_MAX_ASSIGNS: maxCount } = await this.connection
            .first("ROLE_MAX_ASSIGNS")
            .from(this.tableName)
            .where("ROLE_ID", roleId);
        if (!maxCount && maxCount !== 0) {
            return;
        }

        const [{ TOTAL_COUNT: count }] = await this.connection
            .count("ROLE_ID as TOTAL_COUNT")
            .from("USER_ROLES")
            .where("ROLE_ID", roleId);

        if (count > maxCount) {
            const role = await this.get(roleId, ["ROLE_NAME"]);
            throw new UserException(
                `Maximum assigns for role '${role.ROLE_NAME}' exceeded. Assigned ${count}, allowed ${maxCount}.`,
                "ROLE_ASSIGNS_EXCEEDED_ERROR",
            );
        }
    }

    async getByAttribute(attrName, attrValue, singleResult = true) {
        const conn = this.connection
            .select()
            .from(this.tableName)
            .where(attrName.toUpperCase(), attrValue);
        const collection = globalThis.orm.collection("Role", conn);
        const rows = await collection.collectAll();
        if (singleResult) {
            return rows.length > 0 ? rows[0] : null;
        }
        return rows;
    }
}
