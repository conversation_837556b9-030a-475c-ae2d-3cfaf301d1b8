// @ts-nocheck
import { CompetenceRoleRegex } from "../entity/CompetenceRoleRegex";
import { BaseRepository } from "./BaseRepository";

export class CompetenceRoleRegexRepository extends BaseRepository<CompetenceRoleRegex> {
    meta() {
        return {
            tableName: "COMPETENCE_ROLE_REGEX",
            entityName: "CompetenceRoleRegex",
            entity: () => new CompetenceRoleRegex(),
        };
    }

    getForCompetence(competenceId) {
        return this.connection
            .select(["REGEX_ID", "REGEX_NAME", "REGEX_VALUE"])
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId);
    }

    regexPreview(regexId) {
        return this.connection
            .select(["ROLE_ID", "ROLE_NAME", "ROLE_CATEGORY", "ROLE_NOTE"])
            .from("ROLES as R")
            .whereRaw(
                `"R"."ROLE_NAME" like (${this.connection
                    .select("CX.REGEX_VALUE")
                    .from("COMPETENCE_ROLE_REGEX as CX")
                    .where("CX.REGEX_ID", regexId)}) escape '^'`,
            );
    }

    async addRegexes(regexes = [], competenceId) {
        // Add regexes
        for (const { REGEX_NAME, REGEX_VALUE, COMPETENCE_RULE_ID } of regexes) {
            const entity = this.getEntity({
                COMPETENCE_ID: competenceId,
                REGEX_NAME,
                REGEX_VALUE,
                COMPETENCE_RULE_ID,
            });

            await this.store(entity);
        }

        await globalThis.orm
            .repo("competence", this.connection)
            .markForRebuild(competenceId);
    }

    async removeRegexesByName(regexesName, competenceId) {
        if (!regexesName || regexesName.length === 0) {
            return;
        }
        const regexes = this.connection
            .pluck("REGEX_ID")
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId)
            .whereIn(
                "REGEX_VALUE",
                Array.isArray(regexesName) ? regexesName : [regexesName],
            );

        // Delete all from USER_ROLES
        await this.connection
            .from("USER_ROLES")
            .whereIn("ROLE_COMPETENCE_REGEX_ID", regexes)
            .del();

        // Delete
        await this.connection
            .from(this.tableName)
            .whereIn("REGEX_ID", regexes)
            .del();

        // Mark the competence for rebuild
        await globalThis.orm
            .repo("competence", this.connection)
            .markForRebuild(competenceId);
    }

    async deleteRegexes(competenceId) {
        // Delete all from USER_ROLES
        await this.connection
            .from("USER_ROLES")
            .where("ROLE_COMPETENCE_ID", competenceId)
            .del();
        // Delete all for the Competence
        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId)
            .del();

        // Mark the competence for rebuild
        await globalThis.orm
            .repo("competence", this.connection)
            .markForRebuild(competenceId);
    }

    async setRegexes(regexes, competenceId) {
        // Delete all current regexes
        await this.deleteRegexes(competenceId);
        // Add new regexes
        await this.addRegexes(regexes, competenceId);
    }
}
