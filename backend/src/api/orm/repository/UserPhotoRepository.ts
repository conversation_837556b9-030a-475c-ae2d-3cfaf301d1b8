import { BaseRepository } from "./BaseRepository";
import { UserPhoto as photoUtils, PhotoMeta } from "../../utils/UserPhoto";
import { UserPhoto } from "../entity/UserPhoto";
import { PhotoType } from "../entity/const/userPhotoConst";
import { chunk } from "lodash";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class UserPhotoRepository extends BaseRepository<UserPhoto> {
    meta() {
        return {
            tableName: "USER_PHOTO",
            entityName: "UserPhoto",
            sequenceName: "USER_PHOTO_ID_SEQ",
            entity: () => new UserPhoto(),
        };
    }

    async getByUserId(
        userId: number,
        width: number,
        height: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<UserPhoto | undefined> {
        if (!width || !height) {
            throw new InternalException(
                `Expected both arguments width and height defined.`,
            );
        }
        const row = await this.getUserPhotoExactSize(
            userId,
            width,
            height,
            type,
        );

        if (row) {
            return row;
        }

        // check to total number of user photos
        const numberOfUserPhotosInDb = await this.getUserPhotoCount(
            userId,
            type,
        );

        // if there are no photos for this user just return
        if (numberOfUserPhotosInDb === 0) {
            return;
        }

        if (
            numberOfUserPhotosInDb >=
            globalThis.dynamicConfig.userPhoto.maxPhotosPerUser
        ) {
            // if there are already maximum number of user photos then return the closest width x height photo
            return await this.getClosestQualityUserPhoto(
                userId,
                width,
                height,
                type,
            );
        }

        const closestDimensionPhoto = await this.getClosestQualityUserPhoto(
            userId,
            width,
            height,
            type,
        );

        const closestDimensionPhotoPath = photoUtils.getPhotoPath(
            closestDimensionPhoto.PHOTO_NAME,
        );

        const photoCreatedSuccessfully = await this.generateAndStorePhotoTodb(
            closestDimensionPhotoPath,
            userId,
            {
                width,
                height,
                extension: globalThis.dynamicConfig.userPhoto.defaultExtension,
            },
            type,
        );

        if (photoCreatedSuccessfully) {
            return await this.getUserPhotoExactSize(
                userId,
                width,
                height,
                type,
            );
        }

        throw new InternalException(
            `Failed to create photo '${type}' ${width}x${height} for user ${userId}.`,
        );
    }

    async getUserPhotoByName(
        name: string,
        width: number,
        height: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<UserPhoto | undefined> {
        const userPhoto = await this.getByName(name, type);

        if (!userPhoto) {
            return;
        }

        return await this.getByUserId(userPhoto.USER_ID, width, height, type);
    }

    async storeUserPhoto(
        photoPath: string,
        userId: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<void> {
        globalThis.tasLogger.info(
            `Storing new user photo of type ${type}. Deleting all previous profile pictures!`,
            {
                path: photoPath,
                user_id: userId,
                type,
            },
        );

        const userPhotos = await this.getForUser(userId, type);

        // get current photo sizes
        const getKey = (width: number, height: number): string =>
            `${width}x${height}`;

        const sizeToIds = new Map<string, Array<number>>();
        userPhotos.forEach((photo) => {
            const key = getKey(photo.PHOTO_WIDTH, photo.PHOTO_HEIGHT);
            const ids = [photo.USER_PHOTO_ID];
            if (sizeToIds.has(key)) {
                ids.push(sizeToIds.get(key));
            }
            sizeToIds.set(key, ids);
        });

        // get the sizes of the original picture and if they are less or equal to 4K return those sizes otherwise return the capped sizes
        const [cappedWidth, cappedHeight] =
            await photoUtils.getCappedPictureSizes(photoPath);
        const capKey = getKey(cappedWidth, cappedHeight);
        if (!sizeToIds.has(capKey)) {
            sizeToIds.set(capKey, []);
        }

        // delete all the photos
        await this.deleteForUser(userId, type);

        for (const [key, ids] of sizeToIds.entries()) {
            const [width, height] = key.split("x").map((a) => parseInt(a, 10));
            await this.generateAndStorePhotoTodb(
                photoPath,
                userId,
                {
                    width,
                    height,
                    extension:
                        globalThis.dynamicConfig.userPhoto.defaultExtension,
                },
                type,
            );

            await this.deletePhoto(ids, type);
        }
    }

    async generateAndStorePhotoTodb(
        sourcePath: string,
        userId: number,
        photoMeta: PhotoMeta,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ) {
        const { photoName } = await photoUtils.generatePhoto(
            sourcePath,
            userId,
            photoMeta,
        );

        return await this.store(
            this.getEntity({
                PHOTO_NAME: photoName,
                USER_ID: userId,
                PHOTO_WIDTH: photoMeta.width,
                PHOTO_HEIGHT: photoMeta.height,
                PHOTO_TYPE: type,
            }),
        );
    }

    getForUser(userId: number, type = PhotoType.TYPE_PROFILE_PHOTO) {
        const connection = this.connection
            .select()
            .from(this.tableName)
            .where("USER_ID", userId)
            .where("PHOTO_TYPE", type);

        return connection;
    }

    async deletePhoto(
        photoIds: Array<number>,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<void> {
        const idChunks = chunk(photoIds, 1000);
        for (const ids of idChunks) {
            await this.connection(this.tableName)
                .select()
                .whereIn("USER_PHOTO_ID", ids)
                .where("PHOTO_TYPE", type)
                .delete();
        }
    }

    async deleteForUser(
        userId: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<void> {
        await this.getForUser(userId, type).delete();
    }

    async getUserPhotoCount(
        userId: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<number> {
        const [count] = await this.connection
            .count("* as photoCount")
            .from(this.tableName)
            .where("USER_ID", userId)
            .where("PHOTO_TYPE", type);

        if ("photoCount" in count) {
            return Number(count.photoCount);
        }

        return 0;
    }

    async getUserPhotoExactSize(
        userId: number,
        width: number,
        height: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<UserPhoto> {
        const [row] = await this.connection
            .select()
            .from(this.tableName)
            .where("USER_ID", userId)
            .where("PHOTO_TYPE", type)
            .where("PHOTO_WIDTH", width)
            .where("PHOTO_HEIGHT", height);

        return row;
    }

    async getClosestQualityUserPhoto(
        userId: number,
        width: number,
        height: number,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<UserPhoto> {
        const [row] = await this.connection
            .select()
            .from(this.tableName)
            .where("USER_ID", userId)
            .where("PHOTO_TYPE", type)
            .orderByRaw(`ABS("PHOTO_WIDTH" - ?) + ABS("PHOTO_HEIGHT" - ?)`, [
                width,
                height,
            ])
            .limit(1);

        return row;
    }

    async getByName(
        photoName: string,
        type: PhotoType = PhotoType.TYPE_PROFILE_PHOTO,
    ): Promise<UserPhoto> {
        const [row] = await this.connection
            .select()
            .from(this.tableName)
            .where("PHOTO_NAME", photoName)
            .where("PHOTO_TYPE", type)
            .limit(1);

        return row;
    }
}
