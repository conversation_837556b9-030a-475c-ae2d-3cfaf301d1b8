// @ts-nocheck
// @ts-nocheck
import { Sequence } from "../entity/Sequence";
import { BaseRepository } from "./BaseRepository";

export class SequenceRepository extends BaseRepository<Sequence> {
    meta() {
        return {
            tableName: "SEQUENCES",
            entityName: "Sequence",
            entity: () => new Sequence(),
        };
    }

    getAll() {
        const repo = this.connection.select().from(this.tableName);
        return globalThis.orm.collection("sequence", repo);
    }

    /**
     * Get next value from sequence. Sequence is lazy loaded. Created if not exists.
     * @param {string} name Sequence name.
     */
    async getNext(name: string) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `SEQUENCE:NEXT-${name}`,
        );

        try {
            const current = await this.getCurrent(name);
            // Increment sequence
            const nextId = current + 1;
            return await this.connection
                .select()
                .from(this.tableName)
                .where("SEQ_NAME", name)
                .update({
                    SEQ_ID: nextId,
                })
                .then(() => nextId);
        } finally {
            await lock.release();
        }
    }

    async update(name, nextId) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `SEQUENCE:UPDATE-${name}`,
        );

        try {
            // Update sequence
            return await this.connection
                .select()
                .from(this.tableName)
                .where("SEQ_NAME", name)
                .update({
                    SEQ_ID: nextId,
                })
                .then(() => nextId);
        } finally {
            await lock.release();
        }
    }

    /**
     * Get current sequence value. If sequence not exists new is created.
     * @param {string} name
     */
    async getCurrent(name) {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `SEQUENCE:CURRENT-${name}`,
        );

        try {
            const rows = await this.getAndLock(name);
            const id =
                Array.isArray(rows) && rows.length > 0 ? rows[0].SEQ_ID : null;
            // Lazy seq create.
            if (id === null) {
                const entity = this.getEntity({
                    SEQ_ID: SequenceRepository.getStartSequenceId(),
                    SEQ_NAME: name,
                    SEQ_LAST_READ: new Date(),
                });
                return await this.create(entity).then(() => entity.SEQ_ID);
            }
            return id;
        } finally {
            await lock.release();
        }
    }

    getAndLock(name) {
        return globalThis.container.client.database.callKnexRaw(
            `select * from "${this.tableName}" where "SEQ_NAME" = :SEQ_NAME for update`,
            { SEQ_NAME: name },
            this.connection,
        );
    }

    /**
     * Get first index generated for new sequence.
     * @returns {number}
     */
    static getStartSequenceId() {
        return 0;
    }
}
