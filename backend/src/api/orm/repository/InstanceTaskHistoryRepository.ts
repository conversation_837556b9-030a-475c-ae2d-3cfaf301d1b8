// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as TASK from "../entity/const/taskConst";
import { InstanceTaskHistory } from "../entity/InstanceTaskHistory";

export class InstanceTaskHistoryRepository extends BaseRepository<InstanceTaskHistory> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_HISTORY",
            entityName: "InstanceTaskHistory",
            entity: () => new InstanceTaskHistory(),
            archived: true,
        };
    }

    /**
     * Generates task history for a given task
     *
     * @param {Task|Number} taskInput
     * @param {Boolean} actualDate
     * @param {String} note
     * @param {Number} primaryUserId
     * @param {Number} finishedUserId
     * @returns {Promise<Id>} returns generated ITASKH_ID
     */
    async generateRecord(
        taskInput,
        actualDate,
        note,
        primaryUserId,
        finishedUserId,
        forcedUserId: number | null = null,
    ) {
        // Check if TAS<PERSON> was passed or only ITASK_ID
        let task = taskInput;
        const itaskId = taskInput.ITASK_ID;
        if (!itaskId) {
            // Only ITASK_ID was passed, get the whole TASK
            task = await globalThis.orm
                .repo("InstanceTask", this.connection)
                .get(task, [
                    "IPROC_ID",
                    "ITASK_ID",
                    "ITASK_ACTUAL_DATE_START",
                    "ITASK_ACTUAL_DATE_FINISH",
                    "ITASK_USER_ID",
                    "ITASK_SUBPROCESS_IPROC_ID",
                    "ITASK_GEN_HISTORY",
                ]);
        }

        const values = {
            IPROC_ID: task.IPROC_ID,
            ITASK_ID: task.ITASK_ID,
            ITASKH_ACTUAL_DATE_START:
                task.ITASK_ACTUAL_DATE_START !== null && !actualDate
                    ? task.ITASK_ACTUAL_DATE_START
                    : new Date(),
            ITASKH_ACTUAL_DATE_FINISH:
                task.ITASK_ACTUAL_DATE_FINISH !== null && !actualDate
                    ? task.ITASK_ACTUAL_DATE_FINISH
                    : new Date(),
            ITASKH_DUE_DATE_START: task.ITASK_DUE_DATE_START,
            ITASKH_DUE_DATE_FINISH: task.ITASK_DUE_DATE_FINISH,
            ITASKH_FINISHED_BY_USER_ID: finishedUserId,
            ITASKH_USER_ID: forcedUserId || task.ITASK_USER_ID,
            ITASKH_PRIMARY_USER_ID:
                primaryUserId !== finishedUserId ? primaryUserId : null,
            ITASKH_NOTE: note,
            ITASKH_SUBPROCESS_IPROC_ID: task.ITASK_SUBPROCESS_IPROC_ID,
            ITASKH_HIDDEN: task.ITASK_GEN_HISTORY !== "Y" ? "Y" : "N",
        };

        const entity = this.getEntity(values);
        return await this.store(entity);
    }

    forProcess(iprocId, showHiddenTasks, showDetails) {
        const extraColumns = globalThis.dynamicConfig.langs.map(
            (lang) => `TT.TTASK_NAME_${lang.toUpperCase()}`,
        );

        const con = this.connection
            .select(
                [
                    "ITH.*",
                    "IT.ITASK_NAME",
                    this.connection.raw(
                        `"ITU"."USER_DISPLAY_NAME" AS "ITASK_USER_NAME"`,
                    ),
                    this.connection.raw(
                        `"ITFBU"."USER_DISPLAY_NAME" AS "ITASKH_FINISHED_BY_USER_NAME"`,
                    ),
                ].concat(extraColumns),
            )
            .from(`${this.tableName} as ITH`)
            .leftJoin("USERS as ITU", "ITH.ITASKH_USER_ID", "ITU.USER_ID")
            .leftJoin(
                "USERS as ITFBU",
                "ITH.ITASKH_FINISHED_BY_USER_ID",
                "ITFBU.USER_ID",
            )
            .leftJoin("INSTANCE_TASKS as IT", "ITH.ITASK_ID", "IT.ITASK_ID")
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .where("ITH.IPROC_ID", iprocId);

        if (!showHiddenTasks) {
            con.where("ITH.ITASKH_HIDDEN", "N");
        }

        if (!showDetails) {
            con.whereIn("ITH.ITASKH_NOTE", [TASK.HISTORY_NOTE_LAST_SOLVER]);
        }

        return globalThis.orm.collection("InstanceTaskHistory", con);
    }

    saveHistory(note, itaskId, userId) {
        let self = this;
        const taskRepo = globalThis.orm.repo("instanceTask", this.connection);
        return taskRepo.get(itaskId).then((itask) => {
            const entity = self.getEntity({
                ORG_ID: 1,
                IPROC_ID: itask.IPROC_ID,
                ITASK_ID: itask.ITASK_ID,
                ITASKH_ACTUAL_DATE_START:
                    itask.ITASKH_ACTUAL_DATE_START == null
                        ? new Date()
                        : itask.ITASKH_ACTUAL_DATE_START,
                ITASKH_ACTUAL_DATE_FINISH:
                    itask.ITASKH_ACTUAL_DATE_FINISH == null
                        ? new Date()
                        : itask.ITASKH_ACTUAL_DATE_FINISH,
                ITASKH_DUE_DATE_START: itask.ITASKH_DUE_DATE_START,
                ITASKH_DUE_DATE_FINISH: itask.ITASKH_DUE_DATE_FINISH,
                ITASKH_FINISHED_BY_USER_ID:
                    itask.ITASK_FINISHED_BY_USER_ID == null
                        ? userId
                        : itask.ITASK_FINISHED_BY_USER_ID,
                ITASKH_USER_ID: userId,
                ITASKH_NOTE: note,
                ITASKH_SUBPROCESS_IPROC_ID: itask.ITASK_SUBPROCESS_IPROC_ID,
                ITASKH_HIDDEN: itask.ITASK_GEN_HISTORY != "Y" ? "Y" : "N",
            });

            return self
                .store(entity)
                .then(() => {
                    // Store variables
                    self = null; // Enable GC feature.
                })
                .catch((err) => {
                    self = null; // Enable GC feature.
                    globalThis.tasLogger.error(err.message, {
                        err,
                    });
                    throw err;
                });
        });
    }

    primarySolver(iprocId, ttaskName) {
        return this.connection
            .select("ITASKH_PRIMARY_USER_ID")
            .from(`${this.tableName} as ITH`)
            .leftJoin(
                "INSTANCE_PROCESSES as IP",
                "IP.IPROC_ID",
                globalThis.database.raw(iprocId),
            )
            .leftJoin("TEMPLATE_TASKS as TT", function () {
                this.on(
                    "TT.TTASK_NAME",
                    globalThis.database.raw("?", ttaskName),
                ).andOn("TT.TPROC_ID", "IP.TPROC_ID");
            })
            .where("ITH.IPROC_ID", iprocId)
            .then((rows) => {
                if (!Array.isArray(rows) || rows.rows === 0) {
                    return null;
                }
                return rows[0].ITASKH_PRIMARY_USER_ID;
            });
    }
}
