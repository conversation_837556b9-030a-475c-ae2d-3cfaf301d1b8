// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { InstanceGraph } from "../entity/InstanceGraph";

export class InstanceGraphRepository extends BaseRepository<InstanceGraph> {
    private readonly copySql = `INSERT INTO "INSTANCE_GRAPH" ("ORG_ID",
        "IGRAPH_ID", "IPROC_ID", "IGRAPH_OBJECT_TYPE", "IGRAPH_POSITION1_X",
        "IGRAPH_POSITION1_Y", "IGRAPH_POSITION2_X", "IGRAPH_POSITION2_Y", "IGRAPH_SIZE_X",
        "IGRAPH_SIZE_Y", "IGRAPH_COLOR", "IGRAPH_FONT", "IGRAPH_FONT_COLOR",
        "IGRAPH_FONT_SIZE", "IGRAPH_FROM_TTASK_ID", "IGRAPH_TO_TTASK_ID",
        "IGRAPH_ITASKCON_ID", "IGR<PERSON>H_LINE_TYPE", "IGRAPH_LINKSRC_PORT",
        "IGRAPH_LINKTGT_PORT", "IGRAPH_BPMN_TYPE", "IGRAPH_BPMN_EVENT_DEF_TYPE", "ITASK_ID")
      SELECT 1, ${globalThis.orm.db.sequence("IGRAPH_ID_SEQ")}, "IP"."IPROC_ID", "TGRAPH_OBJECT_TYPE"  ,
        "TGRAPH_POSITION1_X", "TGRAPH_POSITION1_Y", "TGRAPH_POSITION2_X",
        "TGRAPH_POSITION2_Y", "TGRAPH_SIZE_X", "TGRAPH_SIZE_Y", "TGRAPH_COLOR",
        "TGRAPH_FONT", "TGRAPH_FONT_COLOR", "TGRAPH_FONT_SIZE", "ITFROM"."ITASK_ID",
        "ITTO"."ITASK_ID", "ITL"."ITASKLINK_ID", "TGRAPH_LINE_TYPE", "TGRAPH_LINKSRC_PORT", "TGRAPH_LINKTGT_PORT",
        "TGRAPH_BPMN_TYPE", "TGRAPH_BPMN_EVENT_DEF_TYPE", "IT"."ITASK_ID"
      FROM "INSTANCE_PROCESSES" "IP" 
        LEFT JOIN "TEMPLATE_GRAPH" "TG" ON ("IP"."TPROC_ID" = "TG"."TPROC_ID")
        LEFT JOIN "INSTANCE_TASK_LINKS" "ITL" ON ("ITL"."TTASKLINK_ID" = "TG"."TGRAPH_TTASKCON_ID" AND "ITL"."IPROC_ID" = "IP"."IPROC_ID")
        LEFT JOIN "INSTANCE_TASKS" "ITFROM" ON ("ITFROM"."TTASK_ID" = "TG"."TGRAPH_FROM_TTASK_ID" AND "ITFROM"."IPROC_ID" = "IP"."IPROC_ID")
        LEFT JOIN "INSTANCE_TASKS" "ITTO" ON ("ITTO"."TTASK_ID" = "TG"."TGRAPH_TO_TTASK_ID" AND "ITTO"."IPROC_ID" = "IP"."IPROC_ID")
        LEFT JOIN "INSTANCE_TASKS" "IT" ON ("IT"."TTASK_ID" = "TG"."TTASK_ID" AND "IT"."IPROC_ID" = "IP"."IPROC_ID")`;

    meta() {
        return {
            tableName: "INSTANCE_GRAPH",
            entityName: "InstanceGraph",
            entity: () => new InstanceGraph(),
            archived: true,
        };
    }

    getForProcess(iprocId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("IPROC_ID", iprocId);
    }

    async getGraphInfo(iprocId) {
        const procRepo = globalThis.orm.repo("process", this.connection);
        const tprocRepo = globalThis.orm.repo(
            "templateProcess",
            this.connection,
        );
        const proc = await procRepo.get(iprocId);

        // TASKS
        const taskInfo = {};
        const tasks = await procRepo.getTTaskList(iprocId);
        if (Array.isArray(tasks) && tasks.length > 0) {
            for (const task of tasks) {
                if (task.ITASK_TYPE === "N") {
                    await procRepo.getITask(task.ITASK_ID).then((info) => {
                        taskInfo[info.ITASK_ID] = info;
                        if (
                            info[
                                "ITASK_ENOT_TGT_TYPE" === "T" &&
                                    info.ITASK_ENOT_TGT_TTASK_ID
                            ]
                        ) {
                            return procRepo
                                .getITask(info.ITASK_ENOT_TGT_TTASK_ID)
                                .then((info2) => {
                                    taskInfo[info2.ITASK_ID] = info2;
                                });
                        }
                    });
                } else if (task.ITASK_TYPE === "I") {
                    return await procRepo
                        .getITask(task.ITASK_ID)
                        .then((info) => {
                            taskInfo[info.ITASK_ID] = info;
                        });
                }
            }
        }

        // VARIABLES
        let vars;
        const varsByTVarId = {};
        await procRepo
            .getTTaskVariableList(iprocId)
            .then(async (collection) => {
                vars = await collection.fetchAll();
                if (Array.isArray(vars) && vars.length > 0) {
                    vars.forEach((tvar) => {
                        varsByTVarId[tvar.IVAR_ID] = tvar;
                    });
                }
            });

        // ASS TASKS
        const assTasks = await procRepo.getTTaskAssTasks(iprocId);

        // ALL USERS
        const allUsersArr = {};
        const userRepo = globalThis.orm.repo("user", this.connection);
        const allUsers = await userRepo.getByStatus(["A", "L"]).fetchAll();
        if (Array.isArray(allUsers) && allUsers.length > 0) {
            allUsers.forEach((user) => {
                allUsersArr[user.USER_ID] = user;
            });
        }

        // ALL ORGANIZATIONS
        const allOrganizationsArr = {};
        const orgRepo = globalThis.orm.repo(
            "OrganizationStructure",
            this.connection,
        );
        const allOrganizations = await orgRepo.getOrgStructure();
        if (Array.isArray(allOrganizations) && allOrganizations.length > 0) {
            allOrganizations.forEach((org) => {
                allOrganizationsArr[org.ORGSTR_ID] = org;
            });
        }

        // HISTORY
        const allHistoryArr = {};
        const allHistory = await procRepo.getHistory(iprocId);
        if (Array.isArray(allHistory) && allHistory.length > 0) {
            allHistory.forEach((hist) => {
                allHistoryArr[hist.TTASK_ID] = hist;
            });
        }

        // CONDS
        const allCondsArr = {};
        const allConds = await tprocRepo.getTProcConditionList(
            proc.TPROC_ID,
            1,
        ); // TODO fix version
        if (Array.isArray(allConds) && allConds.length > 0) {
            allConds.forEach((cond) => {
                if (!allCondsArr[cond.TTASKLINK_ID]) {
                    allCondsArr[cond.TTASKLINK_ID] = [];
                }
                allCondsArr[cond.TTASKLINK_ID].push(cond);
            });
        }

        // GRAPHS
        const posArr = {};
        const posRes = await this.getForProcess(iprocId);
        if (Array.isArray(posRes) && posRes.length > 0) {
            posRes.forEach((pos) => {
                posArr[pos.ITASK_ID] = pos;
            });
        }

        // CONNECT DATA
        const out = {};

        // Process vars
        const varArr = {};
        if (Array.isArray(vars) && vars.length > 0) {
            vars.forEach((tvar) => {
                varsByTVarId[tvar.TVAR_ID] = tvar;

                const outVar = {};
                outVar.taskId = tvar.ITASK_ID;
                outVar.varId = tvar.IVAR_ID;
                outVar.varName = tvar.IVAR_NAME;
                outVar.varType = tvar.IVAR_TYPE;
                outVar.varUsage = tvar.ITASKVARUSG_USAGE;
                outVar.varUsgM = "Proměnné pro povinný zápis";
                outVar.varUsgW = "Proměnné pro zápis";
                outVar.varUsgR = "Proměnné pro čtení";
                outVar.varTextVal = tvar.IVAR_TEXT_VALUE;
                outVar.varNumVal = tvar.IVAR_NUMBER_VALUE;
                outVar.varDatVal = tvar.IVAR_DATE_VALUE;
                outVar.varAtt = tvar.IVAR_ATTRIBUTE;

                if (!varArr[tvar.ITASK_ID]) {
                    varArr[tvar.ITASK_ID] = [];
                }
                varArr[tvar.ITASK_ID].push(outVar);
            });
        }

        const outTasks = [];
        if (Array.isArray(tasks) && tasks.length > 0) {
            tasks.forEach((task) => {
                const arr = {};

                // New mapping
                arr.itask_id = task.ITASK_ID;
                arr.itask_name = task.ITASK_NAME;
                arr.itask_inc_cnt = task.ITASK_INC_CNT;
                arr.itask_sufficient_end = task.ITASK_SUFFICIENT_END;
                arr.itask_petri_net_input = task.ITASK_PETRI_NET_INPUT;
                arr.itask_disc_flag = task.ITASK_DISC_FLAG;
                arr.itask_assesment_role = task.ITASK_ASSESMENT_ROLE;
                arr.itask_assesment_hierarchy = task.ITASK_ASSESMENT_HIERARCHY;
                arr.itask_assesment_method = task.ITASK_ASSESMENT_METHOD;
                arr.itask_assesment_user_id = task.ITASK_ASSESMENT_USER_ID;
                arr.itask_multiinstance_flag = task.ITASK_MULTIINSTANCE_FLAG;
                arr.itask_run_only_once = task.ITASK_RUN_ONLY_ONCE;

                let taskStatus = task.ITASK_STATUS;
                if (
                    taskStatus === "W" &&
                    task.ITASK_DUE_OFFSET === "po" &&
                    task.ITASK_DUE_DATE_START == null
                ) {
                    taskStatus = "WT";
                }
                if (
                    taskStatus === "W" &&
                    task.ITASK_DURATION === "po" &&
                    task.ITASK_DUE_DATE_START == null
                ) {
                    taskStatus = "WT";
                }
                if (taskStatus === "W" && task.USER_NAME == null) {
                    taskStatus = "WU";
                }
                if (task.ITASK_STATUS === "A") {
                    arr.itask_user = task.USER_NAME;
                }

                if (taskStatus === "" && allHistoryArr[task.TTASK_ID]) {
                    const hist = allHistoryArr[task.TTASK_ID];
                    arr.historyFinishedBy = hist.FINISHED_BY_USER;
                    arr.historyActualDateStart = hist.ACTUAL_DATE_START;
                    arr.historyActualDateFinish = hist.ACTUAL_DATE_FINISH;
                    taskStatus = "H";
                }
                arr.itask_status = taskStatus;

                arr.itask_assesment_ttask_name = "";
                if (task.ITASK_ASSESMENT_METHOD === "L") {
                    if (Array.isArray(assTasks) && assTasks.length > 0) {
                        assTasks.forEach((assTask) => {
                            if (assTask.ITASK_ID === task.ITASK_ID) {
                                arr.itask_assesment_itask_name =
                                    assTask.ASSESMENT_ITASK_NAME;
                            }
                        });
                    }
                }

                if (task.ITASK_ASSESMENT_METHOD === "L") {
                    if (varsByTVarId[task.ITASK_ASSESMENT_TVAR_ID]) {
                        arr.itask_assesment_ttask_name =
                            varsByTVarId[
                                task.ITASK_ASSESMENT_TVAR_ID
                            ].TVAR_NAME;
                    } else {
                        arr.itask_assesment_ttask_name = "-unknown-";
                    }
                }

                if (!task.ITASK_ASSESMENT_USER_ID) {
                    if (
                        typeof task.ITASK_ASSESMENT_ORGSTR_ID !== "undefined" &&
                        task.ITASK_ASSESMENT_ORGSTR_ID != null
                    ) {
                        arr.itask_garant = "OS";
                        arr.itask_garant_orgstr_id = "";
                        arr.itask_garant_user_full_name = "";
                    } else {
                        arr.itask_garant = "PO";
                        arr.itask_garant = "PO";
                    }
                } else {
                    arr.itask_garant = "SU";
                    arr.itask_garant_user_id = task.ITASK_ASSESMENT_USER_ID;
                    if (allUsersArr[task.ITASK_ASSESMENT_USER_ID]) {
                        arr.itask_garant_user_full_name =
                            allUsersArr[
                                task.ITASK_ASSESMENT_USER_ID
                            ].USER_DISPLAY_NAME;
                    } else {
                        arr.itask_garant_user_full_name = "Deleted user.";
                    }
                }

                if (task.ITASK_ASSESMENT_METHOD === "N") {
                    const info = taskInfo[task.TASK_INFO];

                    arr.itask_enot_tgt_type = info.ITASK_ENOT_TGT_TYPE;
                    arr.itask_enot_tgt = info.ITASK_ENOT_TGT;
                    arr.itask_enot_tgt_orgstr = info.ITASK_ENOT_TGT_ORGSTR;
                    arr.itask_enot_tgt_role = info.ITASK_ENOT_TGT_ROLE;

                    if (
                        info.ITASK_ENOT_TGT_TYPE === "T" &&
                        info.ITASK_ENOT_TGT_TTASK_ID
                    ) {
                        const info2 = taskInfo[info.ITASK_ENOT_TGT_TTASK_ID];
                        arr.itask_enot_tgt_ttask_name = info2.ITASK_NAME;
                    }
                }

                if (task.ITASK_ASSESMENT_METHOD === "I") {
                    const info = taskInfo[task.TASK_INFO];

                    arr.itask_inv_attendees = info.ITASK_INV_ATTENDEES;
                    arr.itask_inv_dtstart = info.ITASK_INV_DTSTART;
                    arr.itask_inv_dtend = info.ITASK_INV_DTEND;
                    arr.itask_inv_location = info.ITASK_INV_LOCATION;
                }

                // Due offset
                const dueOffset = task.ITASK_DUE_OFFSET;

                let type;
                if (dueOffset) {
                    type = dueOffset.substring(0, 2);
                    if (type === "P+") {
                        arr.taskStartType = "ps";
                        arr.taskStartOffsetVal = dueOffset.substring(2, 4);
                    } else if (type === "T+") {
                        arr.taskStartType = "ts";
                        arr.taskStartOffsetVal = dueOffset.substring(2, 4);
                    } else if (dueOffset === "po") {
                        arr.taskStartType = "po";
                    }
                }
                arr.itask_due_offset = task.ITASK_DUE_OFFSET;

                // Deadline
                const deadline = task.ITASK_DURATION;
                if (deadline) {
                    type = deadline.substring(0, 2);
                    if (type === "P+") {
                        arr.taskDurationType = "ps";
                        arr.taskDurationVal = deadline.substring(2, 4);
                    } else if (type === "T+") {
                        arr.taskDurationType = "ts";
                        arr.taskDurationVal = deadline.substring(2, 4);
                    } else if (dueOffset === "po") {
                        arr.taskDurationType = "po";
                    }
                }
                arr.itask_duration = task.ITASK_DURATION;

                if (posArr[task.ITASK_ID]) {
                    const pos = posArr[task.ITASK_ID];
                    arr.igraph_position1_x = pos.IGRAPH_POSITION1_X;
                    arr.igraph_position1_y = pos.IGRAPH_POSITION1_Y;
                    arr.igraph_bpmn_type = pos.IGRAPH_BPMN_TYPE;
                    arr.igraph_bpmn_event_def_type =
                        pos.IGRAPH_BPMN_EVENT_DEF_TYPE;

                    if (pos.IGRAPH_COLOR) {
                        arr.igraph_color = pos.IGRAPH_COLOR;
                    } else {
                        arr.igraph_color = null;
                    }
                } else {
                    arr.igraph_position1_x = 0;
                    arr.igraph_position1_y = 0;
                }

                if (!task.ITASK_TYPE) {
                    task.ITASK_TYPE = "S";
                }
                arr.itask_type = task.ITASK_TYPE;

                if (varArr[task.ITASK_ID]) {
                    arr.taskVars = varArr[task.ITASK_ID];
                }

                outTasks.push(arr);
            });
        }

        out.tasks = outTasks;

        const outLinks = [];
        const allLinks = await tprocRepo.getTTaskLinkList(proc.TPROC_ID, 1);

        if (Array.isArray(allLinks) && allLinks.length > 0) {
            allLinks.forEach((link) => {
                const arr = {};

                const toTask = _.find(tasks, {
                    TTASK_ID: link.TTASKLINK_TO_TTASK_ID,
                });
                const fromTask = _.find(tasks, {
                    TTASK_ID: link.TTASKLINK_FROM_TTASK_ID,
                });

                arr.ttasklink_id = link.TTASKLINK_ID;
                arr.itasklink_from_ttask_id = fromTask
                    ? fromTask.ITASK_ID
                    : null;
                arr.itasklink_to_ttask_id = toTask ? toTask.ITASK_ID : null;
                arr.ttasklink_is_mandatory = link.TTASKLINK_IS_MANDATORY;
                arr.ttasklink_type = link.TTASKLINK_TYPE;
                arr.ttasklink_priority = link.TTASKLINK_PRIORITY;
                arr.condition_count = link.CONDITION_COUNT;
                arr.sourcePort = link.TGRAPH_LINKSRC_PORT;
                arr.targetPort = link.TGRAPH_LINKTGT_PORT;
                const conds = allCondsArr[link.TTASKLINK_ID];
                arr.conditions = [];
                if (Array.isArray(conds) && conds.length > 0) {
                    conds.forEach((cond) => {
                        arr.conditions.push({
                            org_id: cond.ORG_ID,
                            tcond_id: cond.TCOND_ID,
                            ttasklink_id: cond.TTASKLINK_ID,
                            tcond_description: cond.TCOND_DESCRIPTION,
                            tcond_variable: cond.TCOND_VARIABLE,
                            tcond_operator: cond.TCOND_OPERATOR,
                            tcond_value: cond.TCOND_VALUE,
                        });
                    });
                }

                outLinks.push(arr);
            });
        }

        out.links = outLinks;

        return out;
    }

    copyGraphForTTaskId(ttaskId) {
        const sql = `
            ${this.copySql}
            WHERE IT.TTASK_ID = :TTASK_ID`;

        const params = { TTASK_ID: ttaskId };
        return globalThis.container.client.database.callKnexRaw(
            sql,
            params,
            this.connection,
        );
    }

    copyGraphFromTemplate(iprocId, itaskId = null) {
        // Copy all graph entites for process. Join task, link, conditions to instantiate.
        let sql = `
            ${this.copySql}
            WHERE "IP"."IPROC_ID" = :IPROC_ID`;

        const params = { IPROC_ID: iprocId };
        if (itaskId) {
            sql += ` and "IT"."ITASK_ID" = :ITASK_ID`;
            params.ITASK_ID = itaskId;
        }

        return globalThis.container.client.database.callKnexRaw(
            sql,
            params,
            this.connection,
        );
    }
}
