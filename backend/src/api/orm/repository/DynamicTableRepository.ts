// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UtilsService } from "../../services/UtilsService";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { DynamicTable } from "../entity/DynamicTable";
import * as ROLE from "../entity/const/roleConst";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class DynamicTableRepository extends BaseRepository<DynamicTable> {
    meta() {
        return {
            tableName: "DYNAMIC_TABLE",
            entityName: "DynamicTable",
            sequenceName: "DT_ID_SEQ",
            entity: () => new DynamicTable(),
        };
    }

    async canBeCalled(dllIdentifier, headerId, throwError = false) {
        // Check cache
        const cachedDll = await globalThis.container.client.cache.cache.get(
            `DT_ACCESS-${dllIdentifier}`,
        );
        if (cachedDll) {
            // Cache available
            const { headerRestrictionId } = cachedDll;

            if (
                headerRestrictionId &&
                headerRestrictionId !== headerId &&
                throwError
            ) {
                throw new InternalException(
                    `DLL '${dllIdentifier}' can not be accessed from this Header!`,
                    "LACK_OF_PERMISSIONS",
                    {
                        headerId,
                        headerRestrictionId,
                    },
                );
            } else {
                return !headerRestrictionId || headerRestrictionId === headerId;
            }
        } else {
            // Cache not available, load from DB
            const table = await this.get(dllIdentifier);

            // Cache result
            if (table) {
                const data = {
                    headerRestrictionId: table.DT_ACCESS_HEADER_ID,
                };
                await globalThis.container.client.cache.cache.set(
                    `DT_ACCESS-${dllIdentifier}`,
                    data,
                );
            } else {
                throw new UserException(
                    `Could not find DLL by the identifier '${dllIdentifier}', 'OBJECT_NOT_FOUND`,
                );
            }

            // Cache is loaded, try again
            return this.canBeCalled(dllIdentifier, headerId, throwError);
        }
    }

    async hasRights(roleIds, dtId) {
        // SuperAdmin has rights to all DLLs
        if (roleIds.includes(ROLE.SUPER_ADMINISTRATOR)) {
            return true;
        }

        const PublicDTs = await globalThis.container.client.cache.cache.get(
            `PUBLIC_DT-${dtId}`,
        );
        if (!PublicDTs || typeof PublicDTs.value === "undefined") {
            const table = await this.get(dtId);
            const value = table.DT_PUBLIC === "Y";
            await globalThis.container.client.cache.cache.set(
                `PUBLIC_DT-${dtId}`,
                {
                    value,
                },
            );
        }

        const { value: isPublicUpdated } =
            await globalThis.container.client.cache.cache.get(
                `PUBLIC_DT-${dtId}`,
            );
        if (isPublicUpdated) {
            return true;
        }

        const conn = this.connection
            .select()
            .from("DYNAMIC_TABLE")
            .where(
                UtilsService.isNumericString(dtId) ? "DT_ID" : "DT_NAME",
                dtId,
            )
            .where((builder) => {
                builder.whereNull("DT_ACCESS_ROLE_ID");

                // Prevent: ORA-01795: maximum number of expressions in a list is 1000
                const chunked = _.chunk(roleIds, 999);
                chunked.forEach((chunk) => {
                    builder.orWhereIn(
                        "DT_ACCESS_ROLE_ID",
                        globalThis.database.raw(chunk),
                    );
                });
            });

        const results = await conn;
        if (results.length) {
            // User needs to be Admin to access any non-public DLL
            return (
                roleIds.includes(ROLE.ADMINISTRATOR) ||
                results[0].DT_PUBLIC === "Y"
            );
        }
        return false;
    }

    async hasCreationRights(roleIds, dllIdentifier) {
        if (
            roleIds.includes(ROLE.ADMINISTRATOR) ||
            roleIds.includes(ROLE.SUPER_ADMINISTRATOR)
        ) {
            const results = await this.connection
                .select(1)
                .from("DYNAMIC_TABLE")
                .where(
                    UtilsService.isNumericString(dllIdentifier)
                        ? "DT_ID"
                        : "DT_NAME",
                    dllIdentifier,
                )
                .where((builder) => {
                    builder.whereNull("DT_ACCESS_ROLE_ID");

                    // Prevent: ORA-01795: maximum number of expressions in a list is 1000
                    const chunked = _.chunk(roleIds, 999);
                    chunked.forEach((chunk) => {
                        builder.orWhereIn(
                            "DT_ACCESS_ROLE_ID",
                            globalThis.database.raw(chunk),
                        );
                    });
                });

            return !!results.length;
        }
        return false;
    }

    /**
     * Create empty dynamic_table
     * @param {string} dtName DT name.
     * @param {boolean} dtPublic Is DT public ?
     * @param {string|null} dtDescription DT description
     * @param {string|null} dtNote DT note
     * @returns {Promise<*>}
     */
    async create(dtName, dtPublic = false, dtDescription, dtNote) {
        const tables = await this.connection
            .select()
            .from(this.tableName)
            .where("DT_NAME", dtName);
        if (Array.isArray(tables) && tables.length > 0) {
            throw new UserException(
                `${dtName} already exists in dynamic tables.`,
            );
        }

        // Sequence can be broken by user import.
        let isUnique = false;
        let dtId = null;
        do {
            dtId = await this.genID(this._sequenceName);
            const rows = await this.connection
                .select()
                .from(this.tableName)
                .where("DT_ID", dtId);
            isUnique = rows.length === 0;
        } while (!isUnique);

        const entity = this.getEntity();
        entity.DT_NAME = dtName;
        entity.DT_PUBLIC = dtPublic && dtPublic !== "N" ? "Y" : "N";
        entity.DT_DESCRIPTION = dtDescription;
        entity.DT_NOTE = dtNote;
        entity.DT_ID = dtId;
        await super.create(entity);

        await globalThis.orm
            .repo("dynamicTableValue", this.connection)
            .create(dtId);

        return dtId;
    }

    async update(data) {
        if (!data.DT_ID) {
            throw new InternalException("No DT_ID, can't update.");
        }

        const tables = await this.connection
            .select()
            .from(this.tableName)
            .where((builder) => {
                builder
                    .where("DT_NAME", data.DT_NAME)
                    .whereNot("DT_ID", data.DT_ID);
            });
        if (Array.isArray(tables) && tables.length > 0) {
            throw new UserException(
                `${data.DT_NAME} already exists in dynamic tables.`,
            );
        }

        const res = await this.store(this.getEntity(data));
        return res;
    }

    async edit(data) {
        const dtTable = await this.get(data.DT_ID);
        await this.update(data);

        if (dtTable && dtTable._raw && dtTable._raw.DT_NAME) {
            const oldDtName = dtTable._raw.DT_NAME;

            const varRepo = globalThis.orm.repo("variable", this.connection);
            await varRepo.updateVariablesDListName(oldDtName, data.DT_NAME);

            const tempVarRepo = globalThis.orm.repo(
                "TemplateVariable",
                this.connection,
            );
            await tempVarRepo.updateVariablesDListName(oldDtName, data.DT_NAME);
        }

        return data.DT_ID;
    }

    async createIfNotExists(dtName, dtPublic = false) {
        const table = await this.get(dtName);
        if (table) {
            return table.DT_ID;
        }
        return await this.create(dtName, dtPublic);
    }

    /**
     * DT entity or null.
     * @param identifier
     * @returns {*}
     */
    get(identifier) {
        if (identifier instanceof DynamicTable) {
            return identifier;
        }

        let connection = this.connection.select().from(this.tableName);

        if (UtilsService.isNumericString(identifier)) {
            connection = connection.where("DT_ID", identifier);
        } else {
            connection = connection.whereRaw(
                `${globalThis.orm.db.upper("DT_NAME", true)} = ${globalThis.orm.db.upper("?")}`,
                identifier,
            );
        }

        const collection = globalThis.orm.collection(
            "DynamicTable",
            connection,
        );
        return collection.collectOne();
    }

    getTable(identifier) {
        return this.get(identifier);
    }

    /**
     * Clear cols and values
     * @param identifier
     * @returns {Promise<void>}
     */
    async clearTable(identifier) {
        const table = await this.get(identifier);

        // Drop values
        if (table) {
            const dtcRepo = globalThis.orm.repo(
                "DynamicTableCol",
                this.connection,
            );
            await dtcRepo.clearTable(table.DT_ID);

            const dtvRepo = globalThis.orm.repo(
                "DynamicTableValue",
                this.connection,
            );
            await dtvRepo.clearTable(table.DT_ID);
        }
    }

    /**
     * Remove table, cols and values.
     * @param identifier
     * @returns {Promise<void>}
     */
    async removeTable(identifier) {
        const table = await this.get(identifier);
        if (table) {
            await this.clearTable(identifier);
            await this.connection
                .select()
                .from(this.tableName)
                .where("DT_ID", table.DT_ID)
                .delete();
        }
    }

    async clone(dtId, includeData) {
        const dt = await this.get(dtId);
        const oldName = dt.DT_NAME;
        const newName = await this.findUniqueCloneName("DT_NAME", oldName);
        const newDtId = await this.create(
            newName,
            dt.DT_PUBLIC,
            dt.DT_DESCRIPTION,
            dt.DT_NOTE,
        );

        // copy columns
        const dlcRepo = globalThis.orm.repo("dynamicTableCol", this.connection);
        await dlcRepo.copy(dtId, newDtId);

        // copy values
        if (includeData) {
            const dlvRepo = globalThis.orm.repo(
                "DynamicTableValue",
                this.connection,
            );
            await dlvRepo.copyValues(dtId, newDtId);
        }

        return newDtId;
    }

    async createView(dtId) {
        try {
            const dlcRepo = globalThis.orm.repo(
                "DynamicTableCol",
                this.connection,
            );
            const dbCols = await await dlcRepo.getDbColNames(dtId);

            const cols = [];
            for (let i = 0; i < dbCols.length; i += 1) {
                cols.push(
                    `CASE 
  WHEN "CLOB_${dbCols[i]}" IS NULL 
  THEN "${dbCols[i]}" 
  ELSE (
    SELECT "CLOB_VALUE" 
    FROM "CLOBS" 
    WHERE "CLOB_ID" = "CLOB_${dbCols[i]}"
  ) 
END AS "${dbCols[i]}"`,
                );
            }
            cols.unshift(`"DT_ID"`, `"DTV_INDEX"`, `"DTV_ORDER_INDEX"`);

            await this.connection.raw(`
            CREATE OR REPLACE VIEW "DT_${dtId}" AS 
            SELECT ${cols.join(", ")} FROM "DYNAMIC_TABLE_VALUES" 
            WHERE "DT_ID" = ${dtId}
        `);
        } catch (err) {
            globalThis.tasLogger.warning(`Cant create db view for dt ${dtId}`, {
                err,
                dtId,
            });
        }
    }

    getById(ids) {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("DT_ID", Array.isArray(ids) ? ids : [ids]);
    }
}
