// @ts-nocheck
// @ts-nocheck
import * as LIN<PERSON> from "../entity/const/linkConst";
import { InstanceTaskLinkRepository } from "./InstanceTaskLinkRepository";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class WfLinkRepository extends InstanceTaskLinkRepository {
    getDiscInfo(itaskLinkId, toItaskId) {
        const sql = `SUM(CASE WHEN "ITASKLINK_DISC_FLAG" = 'N' THEN 1 ELSE 0 END) AS "NOT_ACTIVE_LINKS",
SUM(CASE WHEN "ITASKLINK_DISC_FLAG" = 'Y' THEN 1 ELSE 0 END) AS "ACTIVE_LINKS",
SUM(CASE WHEN "ITASKLINK_DISC_NEXT_RUN" = 'Y' THEN 1 ELSE 0 END) AS "NEXT_RUN",
COUNT(1) AS "ALL_LINKS",
SUM(CASE WHEN "ITASKLINK_ID" = :itaskLinkId AND "ITASKLINK_DISC_FLAG" = 'N' THEN 1 ELSE 0 END) AS "THIS_NOT_ACTIVE",
SUM(CASE WHEN "ITASKLINK_ID" = :itaskLinkId AND "ITASKLINK_DISC_FLAG" = 'Y' THEN 1 ELSE 0 END) AS "THIS_ACTIVE"`;

        return this.connection
            .select(this.connection.raw(sql, { itaskLinkId }))
            .from(this.tableName)
            .where("ITASKLINK_TO_TTASK_ID", toItaskId)
            .then((rows) => rows[0]);
    }

    resetDiscFlags(toItaskId) {
        return this.connection(this._tableName)
            .where("ITASKLINK_TO_TTASK_ID", toItaskId)
            .update({
                ITASKLINK_DISC_FLAG: "N",
                ITASKLINK_DISC_NEXT_RUN: "N",
            });
    }

    /**
     * getIncomingLinksV2 - returns all links which have this task on right side
     * @return array of ILink objects
     */
    getIncomingLinksV2(task, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateProcessRepository.getTTaskList)",
            );
        }

        return this.connection
            .select([
                "TTL.TTASKLINK_ID",
                "TTL.TTASKLINK_IS_MANDATORY",
                globalThis.database.raw(`"ITLD"."COUNT"`),
                globalThis.database.raw(`"ITLD"."MAX_COUNT"`),
            ])
            .from("TEMPLATE_TASK_LINKS as TTL")
            .leftJoin("INSTANCE_TASK_LINK_DONE as ITLD", (builder) => {
                builder
                    .on("ITLD.TTASKLINK_ID", "TTL.TTASKLINK_ID")
                    .on(
                        "ITLD.IPROC_ID",
                        globalThis.database.raw(task.IPROC_ID),
                    );
            })
            .where("TTASKLINK_TO_TTASK_ID", task.TTASK_ID)
            .where("TTASKLINK_VERSION", version);
    }

    /**
     * getIncomingLinks - returns all links which have this task on right side
     *
     * @param $restrictDone if true then only links from done tasks are taken
     * @return array of ILink objects
     */
    getIncomingLinks(task, restrictDone = false) {
        const collLinks = this.getCollection(null, "ITL");

        collLinks.knex
            .join(
                "INSTANCE_TASKS AS IT",
                "ITL.ITASKLINK_FROM_TTASK_ID",
                "IT.ITASK_ID",
            )
            .where("ITASKLINK_TO_TTASK_ID", task.ITASK_ID)
            .orderBy("ITASKLINK_PRIORITY", "asc");

        if (restrictDone) {
            // outcoming task cannot be multiinstance template
            collLinks.knex
                .andWhere("ITASKLINK_STATUS", LINK.STATUS_END)
                .andWhere("IT.ITASK_MULTIINSTANCE_FLAG", "<>", "Y");
        } else {
            // outcoming task cannot be multiinstance template
            collLinks.knex
                .andWhere("IT.ITASK_MULTIINSTANCE_FLAG", "<>", "Y")
                .andWhere(function () {
                    this.where("IT.ITASK_MULTIINSTANCE_FLAG", "N").orWhere(
                        function () {
                            this.where(
                                "IT.ITASK_MULTIINSTANCE_FLAG",
                                "M",
                            ).andWhere(function () {
                                this.where(
                                    "IT.ITASK_STATUS",
                                    "<>",
                                    "D",
                                ).orWhere("ITASKLINK_STATUS", "E");
                            });
                        },
                    );
                });
        }

        return collLinks;
    }

    /**
     * getOutgoingLinks - returns all links which have this task on left side
     *
     * @access public
     * @return void
     */
    getOutgoingLinks(task) {
        const collLinks = this.getCollection();
        collLinks.knex
            .where("ITASKLINK_FROM_TTASK_ID", task.ITASK_ID)
            .orderBy("ITASKLINK_PRIORITY", "asc");
        return collLinks;
    }
}
