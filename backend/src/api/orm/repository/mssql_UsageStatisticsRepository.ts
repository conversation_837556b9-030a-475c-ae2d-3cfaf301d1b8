// @ts-nocheck
// @ts-nocheck
import _ from "lodash";

import { UsageStatistics } from "../entity/UsageStatistics";
import { UsageStatisticsRepository } from "./UsageStatisticsRepository";

export class mssql_UsageStatisticsRepository extends UsageStatisticsRepository {
    meta() {
        return {
            tableName: "USAGE_STATISTICS",
            entityName: "UsageStatistics",
            entity: () => new UsageStatistics(),
        };
    }

    /**
     * Finds the last record of the given entity TYPE
     * for this month and UPDATES it, otherwise INSERTS
     *
     * @param {UsageStatistics} entity
     */
    storeForCurrentMonth(entity) {
        if (!entity.ORGSTR_ID) {
            entity.ORGSTR_ID = null;
        }
        if (!entity.HEADER_ID) {
            entity.HEADER_ID = null;
        }
        if (!entity.USER_ID) {
            entity.USER_ID = null;
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where((builder) => {
                builder
                    .where("US_TYPE", entity.US_TYPE)
                    .where("ORGSTR_ID", entity.ORGSTR_ID)
                    .where("USER_ID", entity.USER_ID)
                    .where("HEADER_ID", entity.HEADER_ID)
                    .where(
                        this.connection.raw("month(US_PERIOD)"),
                        this.connection.raw("month(?)", [entity.US_PERIOD]),
                    )
                    .where(
                        this.connection.raw("year(US_PERIOD)"),
                        this.connection.raw("year(?)", [entity.US_PERIOD]),
                    );
            })
            .then((recordsForCurrentMonth) => {
                if (
                    recordsForCurrentMonth &&
                    recordsForCurrentMonth.length > 0
                ) {
                    // UPDATE
                    const updatedRecord = recordsForCurrentMonth[0];
                    if (Array.isArray(entity.US_VALUE)) {
                        updatedRecord.US_VALUE = JSON.parse(
                            updatedRecord.US_VALUE,
                        );
                        entity.US_VALUE = _.union(
                            entity.US_VALUE,
                            updatedRecord.US_VALUE,
                        );

                        if (
                            entity.US_TYPE.includes("USER") ||
                            entity.US_TYPE.includes("SOLVER")
                        ) {
                            entity.US_VALUE = this.filterOutIgnoredUsers(
                                entity.US_VALUE,
                            );
                        }

                        if (entity.US_VALUE.lenght !== entity.US_VALUE) {
                            entity.US_COUNT = entity.US_VALUE.length;
                        }
                        entity.US_VALUE = JSON.stringify(entity.US_VALUE);
                    }

                    return this.connection
                        .from(this.tableName)
                        .update({
                            US_PERIOD: entity.US_PERIOD,
                            US_VALUE: entity.US_VALUE,
                            US_COUNT: entity.US_COUNT,
                        })
                        .where("US_PERIOD", updatedRecord.US_PERIOD)
                        .where("US_TYPE", entity.US_TYPE)
                        .where("ORGSTR_ID", entity.ORGSTR_ID)
                        .where("USER_ID", entity.USER_ID)
                        .where("HEADER_ID", entity.HEADER_ID);
                }

                // INSERT
                entity.US_VALUE = JSON.stringify(entity.US_VALUE);
                return this.connection.from(this.tableName).insert(entity);
            });
    }

    addWhereForMonthItaskh(builder, period) {
        // Collect data only for the past month
        builder.where(
            this.connection.raw("month(ITH.ITASKH_ACTUAL_DATE_FINISH)"),
            this.connection.raw("month(?)", [period]),
        );
        builder.where(
            this.connection.raw("year(ITH.ITASKH_ACTUAL_DATE_FINISH)"),
            this.connection.raw("year(?)", [period]),
        );
        return builder;
    }

    addWhereForMonthIproc(builder, period) {
        // Collect data only for the past month
        builder.where(
            this.connection.raw(`month("IP"."IPROC_ACTUAL_START_DATE")`),
            this.connection.raw("month(?)", [period]),
        );
        builder.where(
            this.connection.raw(`year("IP"."IPROC_ACTUAL_START_DATE")`),
            this.connection.raw("year(?)", [period]),
        );
        return builder;
    }

    async dataExistsByDate(date) {
        const result = await this.connection
            .select("*")
            .from(this.tableName)
            .where(
                this.connection.raw("month(US_PERIOD)"),
                this.connection.raw("month(?)", [date]),
            )
            .where(
                this.connection.raw("year(US_PERIOD)"),
                this.connection.raw("year(?)", [date]),
            )
            .limit(1);

        return result.length === 1;
    }
}
