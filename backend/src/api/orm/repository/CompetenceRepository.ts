// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as COMPETENCE from "../entity/const/competenceConsts";
import * as ROLES from "../entity/const/roleConst";
import { Competence } from "../entity/Competence";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class CompetenceRepository extends BaseRepository<Competence> {
    meta() {
        return {
            tableName: "COMPETENCES",
            entityName: "Competence",
            sequenceName: "COMP_ID_SEQ",
            entity: () => new Competence(),
        };
    }

    getCollection(_columns?, alias = "CC") {
        const coll = super.getCollection(
            [
                `${alias}.*`,
                "CR.COMPETENCE_RULE_REGEX",
                "CR.COMPETENCE_RULE_NAME",
            ],
            alias,
        );
        coll.knex.leftJoin(
            "COMPETENCE_RULES as CR",
            "CR.COMPETENCE_RULE_ID",
            `${alias}.COMPETENCE_RULE_ID`,
        );
        return coll;
    }

    /**
     * Marks a Competence to be rebuilt later by the Cron
     *
     * @param {number|Array<number>} competenceId
     * @returns {Knex.QueryBuilder}
     */
    markForRebuild(competenceId) {
        return this.setRebuildStatus(competenceId, COMPETENCE.REBUILD_YES);
    }

    /**
     * Marks a Competence as rebuild by the Cron
     *
     * @param {number|Array<number>} competenceId
     * @returns {*}
     */
    markAsRebuilt(competenceId) {
        return this.setRebuildStatus(competenceId, COMPETENCE.REBUILD_NO);
    }

    /**
     *
     * @param {number|Array<number>} competenceId
     * @param {string} status
     * @returns {Knex.QueryBuilder<TRecord, TResult>}
     */
    setRebuildStatus(competenceId, status) {
        return this.connection(this.tableName)
            .update({
                COMPETENCE_REBUILD_STATUS: status,
            })
            .whereIn(
                "COMPETENCE_ID",
                Array.isArray(competenceId) ? competenceId : [competenceId],
            );
    }

    /**
     * Returns all Competences considered to be active
     * @returns {Promise<Knex.QueryBuilder>}
     */
    getActive() {
        return this.connection
            .select()
            .from(this.tableName)
            .where("COMPETENCE_STATUS", COMPETENCE.STATUS_ACTIVE);
    }

    /**
     * Returns all Competences considered to be inactive
     * @returns {Promise<Knex.QueryBuilder>}
     */
    getInactive() {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("COMPETENCE_STATUS", [
                COMPETENCE.STATUS_NOT_ACTIVE,
                COMPETENCE.STATUS_DELETED,
            ]);
    }

    getForTruncate() {
        return this.getInactive().where(
            "COMPETENCE_REBUILD_STATUS",
            COMPETENCE.REBUILD_YES,
        );
    }

    getForRebuild() {
        return this.getActive().where((builder) => {
            builder
                .where("COMPETENCE_REBUILD_STATUS", COMPETENCE.REBUILD_YES)
                .orWhereIn("COMPETENCE_ID", (whereInBuilder) => {
                    whereInBuilder
                        .distinct("COMPETENCE_ID")
                        .from("COMPETENCE_ROLE_REGEX");
                });
        });
    }

    /**
     * Returns a Competence Collection with associated Roles and Users
     *
     * @returns {BaseCollection}
     */
    getExtendedCollection() {
        const collection = this.getCollection(null, "COMP", [
            "CX.REGEX_ID",
            "CX.REGEX_NAME",
            "CX.REGEX_VALUE",
            "R.ROLE_ID",
            "R.ROLE_NAME",
            "U.USER_ID",
            globalThis.database.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ]);

        // Join COMPETENCE_ROLES
        collection.knex
            .leftJoin(
                "COMPETENCE_ROLES as CR",
                "CR.COMPETENCE_ID",
                "COMP.COMPETENCE_ID",
            )
            .leftJoin("ROLES as R", "R.ROLE_ID", "CR.ROLE_ID");

        // Join COMPETENCE_USERS
        collection.knex
            .leftJoin(
                "COMPETENCE_USERS as CU",
                "CU.COMPETENCE_ID",
                "COMP.COMPETENCE_ID",
            )
            .leftJoin("USERS as U", "U.USER_ID", "CU.USER_ID");

        // Join COMPETENCE_ROLE_REGEX
        collection.knex.leftJoin(
            "COMPETENCE_ROLE_REGEX as CX",
            "CX.COMPETENCE_ID",
            "COMP.COMPETENCE_ID",
        );

        // Do not return 'DELETED' Competences
        collection.knex.whereNot(
            "COMPETENCE_STATUS",
            COMPETENCE.STATUS_DELETED,
        );

        return collection;
    }

    /**
     * Deletes related Roles of the given Competence
     *
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async deleteRelatedRoles(competenceId) {
        await this.connection
            .from("COMPETENCE_ROLES")
            .where(competenceId)
            .delete();
    }

    /**
     * Deletes related Users of the given Competence
     *
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async deleteRelatedUsers(competenceId) {
        await this.connection
            .from("COMPETENCE_USERS")
            .where(competenceId)
            .delete();
    }

    /**
     * Deletes User Roles for the given Competence
     *
     * @param {number} competenceId
     * @returns {Promise<Array<number>>} truncatedUserIds
     */
    async truncate(competenceId) {
        const deletedUsers = await this.connection
            .pluck("UR.USER_ID")
            .from("USER_ROLES AS UR")
            .leftJoin("USERS AS U", "U.USER_ID", "UR.USER_ID")
            .where("U.USER_STATUS", "A")
            .where("UR.ROLE_SOURCE", ROLES.SOURCE_COMPETENCE)
            .where("UR.ROLE_COMPETENCE_ID", competenceId);

        await this.connection.raw(
            `
                        DELETE FROM "USER_ROLES"
                        WHERE "USER_ID" IN (
                            SELECT "USER_ID"
                            FROM "USERS"
                            WHERE "USER_STATUS" = 'A'
                        )
                        AND "ROLE_SOURCE" = :source
                        AND "ROLE_COMPETENCE_ID" = :competenceId`,
            {
                source: ROLES.SOURCE_COMPETENCE,
                competenceId,
            },
        );

        return _.uniq(deletedUsers);
    }

    /**
     * Generates User Roles
     *
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async generate(competenceId) {
        // Generate from COMPETENCE_ROLES for all COMPETENCE_USERS
        await this.connection
            .into(
                globalThis.database.raw(
                    `"USER_ROLES" ("USRROL_ID", "ORG_ID", "USER_ID", "ROLE_ID", "ROLE_SOURCE", "ROLE_COMPETENCE_ID")`,
                ),
            )
            .insert((builder) => {
                builder
                    .select([
                        globalThis.database.raw(
                            globalThis.orm.db.sequence("USRROL_ID_SEQ"),
                        ),
                        1,
                        "CU.USER_ID",
                        "CR.ROLE_ID",
                        globalThis.database.raw(`'${ROLES.SOURCE_COMPETENCE}'`),
                        competenceId,
                    ])
                    .from("COMPETENCE_USERS as CU")
                    .innerJoin(
                        "COMPETENCE_ROLES as CR",
                        "CU.COMPETENCE_ID",
                        "CR.COMPETENCE_ID",
                    )
                    .leftJoin("USERS as U", "U.USER_ID", "CU.USER_ID")
                    .where("U.USER_STATUS", "A")
                    .where("CU.COMPETENCE_ID", competenceId)
                    .where("CR.COMPETENCE_ID", competenceId);
            })
            .catch(async (err) => {
                globalThis.tasLogger.error(
                    "Error generating COMPETENCE_ROLES",
                    {
                        err,
                        competenceId,
                    },
                );
                throw new InternalException(
                    `Error generating COMPETENCE_ROLES. ${err.message}`,
                    "INVALID_INSERT",
                    { err, competenceId },
                );
            });

        // Generate from COMPETENCE_ROLE_REGEX for all COMPETENCE_USERS
        const regularExpressions = await globalThis.orm
            .repo("competenceRoleRegex", this.connection)
            .getForCompetence(competenceId)
            .clearSelect()
            .pluck("REGEX_ID");

        for (const expressionId of regularExpressions) {
            await this.connection
                .into(
                    globalThis.database.raw(
                        `"USER_ROLES" ("USRROL_ID", "ORG_ID", "USER_ID", "ROLE_ID", "ROLE_SOURCE", "ROLE_COMPETENCE_ID", "ROLE_COMPETENCE_REGEX_ID")`,
                    ),
                )
                .insert((builder) => {
                    builder
                        .select([
                            globalThis.database.raw(
                                globalThis.orm.db.sequence("USRROL_ID_SEQ"),
                            ),
                            1,
                            "CU.USER_ID",
                            "R.ROLE_ID",
                            globalThis.database.raw(
                                `'${ROLES.SOURCE_COMPETENCE}'`,
                            ),
                            competenceId,
                            expressionId,
                        ])
                        .from("COMPETENCE_USERS as CU")
                        .leftJoin("USERS as U", "U.USER_ID", "CU.USER_ID")
                        .where("U.USER_STATUS", "A")
                        .crossJoin("ROLES as R")
                        .where("R.ROLE_ID", ">", 0)
                        .whereRaw(
                            `"R"."ROLE_NAME" like (${this.connection
                                .select("CX.REGEX_VALUE")
                                .from("COMPETENCE_ROLE_REGEX as CX")
                                .where("CX.COMPETENCE_ID", competenceId)
                                .where(
                                    "CX.REGEX_ID",
                                    expressionId,
                                )}) escape '^'`,
                        )
                        .where("CU.COMPETENCE_ID", competenceId);
                })
                .catch(async (err) => {
                    globalThis.tasLogger.error(
                        "Error generating COMPETENCE_ROLE_REGEX",
                        {
                            err,
                            competenceId,
                        },
                    );
                    throw new InternalException(
                        `Error generating COMPETENCE_ROLE_REGEX. ${err.message}`,
                        "INVALID_INSERT",
                        { err, competenceId },
                    );
                });
        }

        const competenceUserRepo = globalThis.orm.repo(
            "competenceUser",
            this.connection,
        );
        return await competenceUserRepo.getActiveUsers(competenceId);
    }

    /**
     * Rebuilds User Roles for the given Competence
     *
     * @param {number} competenceId
     * @returns {Promise<void>}
     */
    async rebuild(competenceId) {
        const comp = await this.get(competenceId);

        if (comp.COMPETENCE_RULE_ID) {
            await globalThis.orm
                .repo("competenceRoleRegex", this.connection)
                .deleteRegexes(competenceId);
            await globalThis.orm
                .repo("competenceRuleRoleRegex", this.connection)
                .copyDefaultsForCompetence(
                    competenceId,
                    comp.COMPETENCE_RULE_ID,
                );
        }
        const truncatedUsers = [].concat(await this.truncate(competenceId));
        const generatedUsers = [].concat(await this.generate(competenceId));
        return [...truncatedUsers, ...generatedUsers];
    }

    async store(entity) {
        // Mark the Entity for rebuild on update
        if (entity.id) {
            await this.markForRebuild(entity.id);
        }
        return await super.store(entity);
    }

    async delete(competenceId) {
        await globalThis.orm
            .repo("competenceRole", this.connection)
            .deleteRoles(competenceId);
        await globalThis.orm
            .repo("competenceRoleRegex", this.connection)
            .deleteRegexes(competenceId);
        await globalThis.orm
            .repo("competenceUser", this.connection)
            .deleteUsers(competenceId);

        await this.connection
            .from(this.tableName)
            .where("COMPETENCE_ID", competenceId)
            .delete();
    }
}
