// @ts-nocheck
// @ts-nocheck
import { BaseCollection } from "../BaseCollection";
import { InstanceTaskCompletition } from "../entity/InstanceTaskCompletition";
import { Task } from "../entity/Task";
import * as TASK from "../entity/const/taskConst";

import * as TPROC_CONST from "../entity/const/tprocConsts";

import { BaseRepository } from "./BaseRepository";

export class InstanceTaskCompletitionRepository extends BaseRepository<InstanceTaskCompletition> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_COMPLETION",
            entityName: "InstanceTaskCompletition",
            entity: () => new InstanceTaskCompletition(),
            archived: true,
            archParams: {
                subQueryTable: "INSTANCE_TASKS",
                subQueryColumn: "ITASK_ID",
            },
        };
    }

    /**
     * Get completition or canceling conditions for multiinstance.
     *
     * @param task
     * @param toCancel
     * @return {BaseCollection}
     */
    getMultiTaskCompletions(
        task: Task,
        toCancel: boolean = false,
    ): BaseCollection<InstanceTaskCompletition> {
        return this.connection
            .select()
            .from("INSTANCE_TASK_COMPLETION AS ITC")
            .join("INSTANCE_TASKS AS IT", "ITC.ITASK_ID", "IT.ITASK_ID")
            .join("INSTANCE_TASKS AS IT2", function () {
                this.on("IT2.TTASK_ID", "=", "IT.TTASK_ID")
                    .on(
                        "IT.ITASK_MULTIINSTANCE_FLAG",
                        "=",
                        globalThis.database.raw(
                            `'${TASK.MULTIINSTANCE_FLAG_YES}'`,
                        ),
                    )
                    .on("IT.IPROC_ID", "=", "IT2.IPROC_ID");
            })
            .where("IT2.ITASK_ID", task.ITASK_ID)
            .where("ITC_CANCEL_FLAG", toCancel ? "Y" : "N")
            .orderBy("ITC_ID");
    }

    /**
     * Get completition or canceling conditions.
     *
     * @param task
     * @param toCancel {bool} True if want only canceling conditions
     * @param version
     * @return {BaseCollection}
     */
    getTaskCompletions(
        task: Task,
        toCancel: boolean = false,
        version: number,
    ): BaseCollection<InstanceTaskCompletition> {
        if (version > 0) {
            let conn = this.connection
                .select(
                    globalThis.database.raw(
                        `"TC"."TTC_OPERATOR" as "ITC_OPERATOR"`,
                    ),
                    globalThis.database.raw(`"TC"."TTC_VALUE" as "ITC_VALUE"`),
                    globalThis.database.raw(
                        `"TC"."TTC_CANCEL_FLAG" as "ITC_CANCEL_FLAG"`,
                    ),
                    globalThis.database.raw(
                        `"TC"."TTC_CONCAT_OP" as "ITC_CONCAT_OP"`,
                    ),
                    "TVAR_ID",
                )
                .from("TEMPLATE_TASK_COMPLETION as TC")
                .where("TTASK_ID", task.TTASK_ID)
                .where("TTC_CANCEL_FLAG", toCancel ? "Y" : "N")
                .orderBy("TTC_ID");
            if (version !== TPROC_CONST.TPROC_ALL_VERSIONS) {
                conn = conn.where("TTC_VERSION", version);
            }

            return conn;
        }

        return this.connection
            .select()
            .from("INSTANCE_TASK_COMPLETION AS ITC")
            .where("ITASK_ID", task.ITASK_ID)
            .where("ITC_CANCEL_FLAG", toCancel ? "Y" : "N")
            .orderBy("ITC_ID");
    }
}
