// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import { Role } from "../entity/Role";
import { Header } from "../entity/Header";

export class HeaderRepository extends BaseRepository<Header> {
    meta() {
        return {
            tableName: "HEADERS",
            entityName: "Header",
            entity: () => new Header(),
        };
    }

    /**
     * Create header.
     * @param entity
     * @param {bool} createAllUsersRole create default visibility role defalt=true
     */
    store(entity, createAllUsersRole?) {
        const roleRepo = globalThis.orm.repo("headerRole", this.connection);
        return super
            .store(entity)
            .then((id) => {
                // All AllUsers header !
                if (
                    !createAllUsersRole &&
                    typeof createAllUsersRole !== "undefined"
                ) {
                    // default = true
                    return id;
                }

                // t3f-736 Nastavení defaultního spo<PERSON><PERSON><PERSON> na Administrators
                return this.connection
                    .select()
                    .from("HEADER_ROLES")
                    .where("ROLE_ID", Role.consts.ADMINISTRATOR)
                    .where("HEADER_ID", id)
                    .then((rows) => {
                        if (Array.isArray(rows) && rows.length > 0) {
                            return id;
                        }

                        const roleEntity = roleRepo.getEntity({
                            ROLE_ID: Role.consts.ADMINISTRATOR,
                            HEADER_ID: id,
                        });
                        return roleRepo.store(roleEntity).then(() => id);
                    });
            })
            .then(async (id) => {
                if (entity.HDR_VIS_INTERNAL_USER_ONLY === "Y") {
                    globalThis.dynamicConfig.usingExternalLoginRights = true;
                } else {
                    // There must be at least one header with internal vis only to enable this feature.
                    const securedHeaders = await this.connection
                        .select()
                        .from("HEADERS")
                        .where("HDR_VIS_INTERNAL_USER_ONLY", "Y")
                        .limit(1);
                    globalThis.dynamicConfig.usingExternalLoginRights =
                        securedHeaders.length > 0;
                }

                return id;
            });
    }

    /**
     *
     * @param tprocId
     * @param attributeName
     * @param attributeValue
     * @returns {Promise<*|null>}
     */
    async getBy(tprocId, attributeName, attributeValue) {
        return await this.getByAttributes(
            ["TPROC_ID", attributeName],
            [tprocId, attributeValue],
        );
    }

    getByName(tprocId, headerName) {
        return this.getBy(tprocId, "HEADER_NAME", headerName);
    }

    getHeaders() {
        const columns = [
            "TH.*",
            "TP.TPROC_STATUS",
            "RL.ROLE_NAME as HDR_VIS_ROLE_NAME",
            "HR.ROLE_NAME as HEADER_HR_ROLE_NAME",
        ];
        const conn = this.connection
            .select(columns)
            .from(`${this.tableName} as TH`)
            .leftJoin("ROLES as RL", "HDR_VIS_ROLE_ID", "RL.ROLE_ID")
            .leftJoin("ROLES as HR", "HEADER_HR_ROLE_ID", "HR.ROLE_ID")
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("TH.TPROC_ID", "TP.TPROC_ID")
                    .on("TP.TPROC_VERSION", globalThis.database.raw(1));
            })
            .whereNot("TP.TPROC_STATUS", "E");
        return globalThis.orm.collection("Header", conn);
    }

    async applyExternalLoginRights(conn, userId) {
        const usr = await globalThis.orm
            .repo("user", this.connection)
            .get(userId, ["USER_EXTERNAL_LOGIN"]);
        if (usr.USER_EXTERNAL_LOGIN === "Y") {
            // External user login.
            // Override connection ...
            conn = conn.where((builder) => {
                builder
                    .whereNot("TH.HDR_VIS_INTERNAL_USER_ONLY", "Y")
                    .orWhereNull("TH.HDR_VIS_INTERNAL_USER_ONLY");
            });
        }
    }

    getHeadersByTProcId(tprocId) {
        const coll = this.getHeaders();
        coll.knex.where("TH.TPROC_ID", tprocId);
        return coll;
    }

    remove(headerId) {
        const roleRepo = globalThis.orm.repo("headerRole", this.connection);
        const orgstrRepo = globalThis.orm.repo("headerOrgstr", this.connection);

        return this.connection
            .select()
            .from(this.tableName)
            .where("HEADER_ID", headerId)
            .delete()
            .then(() => roleRepo.removeForHeader(headerId))
            .then(() => orgstrRepo.removeForHeader(headerId));
    }

    async fillRoleRights(headers) {
        if (!Array.isArray(headers) && headers.HEADER_ID) {
            headers = [headers];
        }

        const headerRole = globalThis.orm.repo("headerRole", this.connection);
        const out = [];

        for (const header of headers) {
            const roleRights = await headerRole
                .getHeaderRoles(header.HEADER_ID)
                .collectAll();
            header.header_roles = roleRights.map((roleRight) => roleRight._raw);
            out.push(header);
        }

        return out;
    }

    async fillOrgstrRights(headers) {
        if (!Array.isArray(headers) && headers.HEADER_ID) {
            headers = [headers];
        }

        const headerRole = globalThis.orm.repo("headerOrgstr", this.connection);
        const out = [];
        for (const header of headers) {
            const orgstrRights = await headerRole
                .getHeaderOrganizations(header.HEADER_ID)
                .collectAll();
            header.header_orgstrs = orgstrRights.map(
                (orgstrRight) => orgstrRight._raw,
            );

            out.push(header);
        }

        return out;
    }

    findUniqueCloneName(headerName, number) {
        if (!number) {
            number = 0;
        }
        number++;

        const newName = `clone(${number})_${headerName}`;
        return this.connection
            .select()
            .from(this.tableName)
            .where("HEADER_NAME", newName)
            .then((data) => {
                if (Array.isArray(data) && data.length > 0) {
                    return this.findUniqueCloneName(headerName, number);
                }
                return newName;
            });
    }

    clone(id) {
        let newId = null;
        let oldId = null;
        return this.get(id)
            .then((header) => {
                // Clear id to create new copy of header
                oldId = header.HEADER_ID;
                header.HEADER_ID = null;
                header.makeAllDirty();
                header.HEADER_ENABLED = "N";
                return header;
            })
            .then((header) =>
                // Find clone name
                this.findUniqueCloneName(header.HEADER_NAME).then((newName) => {
                    header.HEADER_NAME = newName;
                    return header;
                }),
            )
            .then((header) =>
                // Clone roles
                this.store(header, false).then((copyId) => {
                    header.HEADER_ID = copyId;
                    newId = copyId;
                    const hrRepo = globalThis.orm.repo(
                        "HeaderRole",
                        this.connection,
                    );
                    return hrRepo
                        .copy({ HEADER_ID: oldId }, { HEADER_ID: newId })
                        .then(() =>
                            // Clone orgs
                            this.store(header).then(() => {
                                const hoRepo = globalThis.orm.repo(
                                    "HeaderOrgstr",
                                    this.connection,
                                );
                                return hoRepo.copy(
                                    { HEADER_ID: oldId },
                                    { HEADER_ID: newId },
                                );
                            }),
                        );
                }),
            )
            .then(() => newId);
    }
}
