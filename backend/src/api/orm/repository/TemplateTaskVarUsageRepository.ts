// @ts-nocheck
// @ts-nocheck
import _ from "lodash";

import { BaseRepository } from "./BaseRepository";
import * as tprocConsts from "../entity/const/tprocConsts";
import { TemplateTaskVarUsage } from "../entity/TemplateTaskVarUsage";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class TemplateTaskVarUsageRepository extends BaseRepository<TemplateTaskVarUsage> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_VAR_USAGE",
            entityName: "TemplateTaskVarUsage",
            entity: () => new TemplateTaskVarUsage(),
        };
    }

    getAll(version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskVarUsage.getAll)",
            );
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where("TTASKVARUSG_VERSION", version);
    }

    getUsagesForTemplateProcess(tprocId, _iprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getUsagesForTemplateProcess)",
            );
        }

        if (version === 0) {
            throw new InternalException(
                "Version must be defined. INSTANCE_TASK_VAR_USAGE is no longer used.",
            );
        }

        const coll = this.getCollection();
        coll.knex
            .where("TPROC_ID", tprocId)
            .where("TTASKVARUSG_VERSION", version);
        return coll;
    }

    getForTTask(tprocId, ttaskId, version) {
        if (!version) {
            // No null, undefined nor 0, '0'
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskVarUsage.getForTTask)",
            );
        }

        const repo = this.connection
            .select([
                "TV.ORG_ID",
                "TTVU.TTASKVARUSG_ID",
                "TV.TPROC_ID",
                "TTVU.TTASK_ID",
                "TV.TVAR_ID",
                "TTVU.TTASKVARUSG_USAGE",
                "TV.TVAR_COPY_SNAPSHOT",
                "TTVU.AXIS_X",
                "TTVU.AXIS_Y",
                "TTVU.TSEC_ID",
                "TTVU.TSEC_X",
                "TTVU.TSEC_Y",
            ])
            .from("TEMPLATE_VARIABLES as TV")
            .leftJoin(`${this.tableName} as TTVU`, "TV.TVAR_ID", "TTVU.TVAR_ID")
            .where("TTVU.TTASK_ID", ttaskId)
            .where("TTVU.TPROC_ID", tprocId)
            .where("TTASKVARUSG_VERSION", version)
            .orderBy("TTVU.TSEC_Y", "acs")
            .orderBy("TTVU.TSEC_X", "acs")
            .orderBy("TTVU.AXIS_Y", "asc")
            .orderBy("TTVU.AXIS_X", "asc")
            .orderBy("TTVU.TTASKVARUSG_ID", "asc");

        return globalThis.orm.collection("TemplateTaskVarUsage", repo);
    }

    getForTProcess(tprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getForTProcess)",
            );
        }

        const columns = [
            "TV.ORG_ID",
            "TTVU.TTASKVARUSG_ID",
            "TV.TPROC_ID",
            "TTVU.TTASK_ID",
            "TV.TVAR_ID",
            "TTVU.TTASKVARUSG_USAGE",
            "TTVU.AXIS_X",
            "TTVU.AXIS_Y",
            "TV.TVAR_COPY_SNAPSHOT",
            "TTVU.TSEC_ID",
            "TTVU.TSEC_X",
            "TTVU.TSEC_Y",
        ];

        const repo = this.connection
            .select(columns)
            .from("TEMPLATE_VARIABLES as TV")
            .leftJoin(`${this.tableName} as TTVU`, "TV.TVAR_ID", "TTVU.TVAR_ID")
            .where("TTVU.TPROC_ID", tprocId)
            .where("TTASKVARUSG_VERSION", version)
            .orderBy("TTVU.TSEC_Y", "acs")
            .orderBy("TTVU.TSEC_X", "acs")
            .orderBy("TTVU.AXIS_Y", "asc")
            .orderBy("TTVU.AXIS_X", "asc")
            .orderBy("TTVU.TTASKVARUSG_ID", "asc");

        return globalThis.orm.collection("TemplateTaskVarUsage", repo);
    }

    deleteForTProc(tprocId, version) {
        const coll = this.getCollection();
        return coll.knex
            .where("TPROC_ID", tprocId)
            .where("TTASKVARUSG_VERSION", version)
            .delete();
    }

    deleteForTTask(ttaskId, version?) {
        if (
            (version === null || typeof version === "undefined") &&
            version !== tprocConsts.TPROC_ALL_VERSIONS
        ) {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskVarUsageRepository.deleteForTTask)",
            );
        }

        let delConnection = this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId);

        if (version !== tprocConsts.TPROC_ALL_VERSIONS) {
            delConnection = delConnection.where("TTASKVARUSG_VERSION", version);
        }

        return delConnection.delete();
    }

    /**
     *
     * @param {Number} tprocId
     * @param {Number} srcVersion
     * @param {Number} dstVersion
     * @returns {Promise<boolean>}
     */
    async copyVersion(tprocId, srcVersion, dstVersion) {
        const srcMapping = await this.getUsagesForTemplateProcess(
            tprocId,
            null,
            srcVersion,
        ).collectAll(); // Get source calculations
        await this.deleteForTProc(tprocId, dstVersion); // Remove old calculations
        // No mapping, do nothing
        if (!Array.isArray(srcMapping) || srcMapping.length === 0) {
            return srcMapping;
        }

        // Copy calcs with new version and id.
        for (const map of srcMapping) {
            map.TTASKVARUSG_VERSION = dstVersion; // New version
            map.id = null; // New id
            map.makeAllDirty();

            await this.store(map);
        }

        return true;
    }

    async setTTaskVariableUsage(ttaskId, tprocId, ttaskVarMapping, version) {
        if (!version) {
            throw new InternalException(
                "Version must be defined. Please fix your code! (setTTaskVariableUsage)",
            );
        }

        const toDeleteCon = this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId)
            .where("TTASKVARUSG_VERSION", version);
        const toDeleteCol = this.createCollection(toDeleteCon);
        const toDeleteArr = await toDeleteCol.collectAll();

        await toDeleteCon.delete();

        const insertedRecords = [];

        for (const tvar of ttaskVarMapping) {
            let axisX = tvar.axis_x ?? null;
            let axisY = tvar.axis_y || null;
            let tsecId = tvar.tsec_id || null;
            let tsecX = tvar.tsec_x ?? 0;
            let tsecY = tvar.tsec_y || null;

            if (axisX === null) {
                const findDeleted = _.find(toDeleteArr, {
                    TVAR_ID: tvar.tvar_id,
                });

                if (findDeleted) {
                    axisX = findDeleted.AXIS_X;
                    axisY = findDeleted.AXIS_Y;
                    tsecId = findDeleted.TSEC_ID;
                    tsecX = findDeleted.TSEC_X;
                    tsecY = findDeleted.TSEC_Y;
                }
            }

            // Insert with seq_id select due to optimisation !!
            const insertResult =
                await globalThis.container.client.database.callKnexRaw(
                    `INSERT INTO "TEMPLATE_TASK_VAR_USAGE" ("TTASKVARUSG_ID", "ORG_ID", "TPROC_ID", "TTASK_ID", "TVAR_ID", "TTASKVARUSG_USAGE", "AXIS_X", "AXIS_Y", "TTASKVARUSG_VERSION", "TSEC_ID", "TSEC_X", "TSEC_Y") ` +
                        `select ${globalThis.orm.db.sequence("TTASKVARUSG_ID_SEQ")}, 1, :TPROC_ID, :TTASK_ID, :TVAR_ID, :TTASKVARUSG_USAGE, :AXIS_X, :AXIS_Y, :TTASKVARUSG_VERSION, :TSEC_ID, :TSEC_X, :TSEC_Y ${globalThis.orm.db.fromDual()}`,
                    {
                        TPROC_ID: tprocId,
                        TTASK_ID: ttaskId,
                        TVAR_ID: tvar.tvar_id,
                        TTASKVARUSG_USAGE: tvar.usage,
                        TTASKVARUSG_VERSION: version,
                        AXIS_X: axisX,
                        AXIS_Y: axisY,
                        TSEC_ID: tsecId,
                        TSEC_X: tsecX,
                        TSEC_Y: tsecY,
                    },
                    this.connection,
                );
            insertedRecords.push(insertResult);
        }
        return insertedRecords;
    }
}
