// @ts-nocheck
// @ts-nocheck
import _ from "lodash";

import { TemplateTaskCompletion } from "../entity/TemplateTaskCompletion";
import { BaseRepository } from "./BaseRepository";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

import * as TPROC_CONSTS from "../entity/const/tprocConsts";

export class TemplateTaskCompletionRepository extends BaseRepository<TemplateTaskCompletion> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_COMPLETION",
            entityName: "TemplateTaskCompletion",
            entity: () => new TemplateTaskCompletion(),
        };
    }

    getForTTasks(ttaskId, version) {
        return globalThis.orm.collection(
            "TemplateTaskCompletion",
            this.getForTTask(ttaskId, version),
        );
    }

    getForTTask(ttaskId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskCompletitionRepository.getForTTask)",
            );
        }

        const conn = this.connection
            .select("TC.*", "TV.TVAR_TYPE", "TV.TVAR_MULTI", "TV.TVAR_NAME")
            .from(`${this.tableName} as TC`)
            .leftJoin("TEMPLATE_VARIABLES as TV", "TC.TVAR_ID", "TV.TVAR_ID")
            .whereIn(
                "TC.TTASK_ID",
                Array.isArray(ttaskId) ? ttaskId : [ttaskId],
            );

        if (version === TPROC_CONSTS.TPROC_ALL_VERSIONS) {
            return conn;
        }

        return conn.where("TTC_VERSION", version);
    }

    async copyVersion(tprocId, srcVersion, dstVersion) {
        const tasks = await globalThis.orm
            .repo("templateTask", this.connection)
            .getForTProcess(tprocId);
        const ids = _.map(tasks, "TTASK_ID");

        const srcCompletions = await this.getForTTasks(
            ids,
            srcVersion,
        ).collectAll(); // Get source completitions
        await this.deleteForTTask(ids, dstVersion); // Remove old completitions

        // No completitions, do nothing
        if (!Array.isArray(srcCompletions) || srcCompletions.length === 0) {
            return srcCompletions;
        }

        // Copy completitions with new version and id.
        for (const completion of srcCompletions) {
            completion.TTC_VERSION = dstVersion; // New version
            completion.id = null; // New id
            completion.makeAllDirty();

            await this.store(completion);
        }
    }

    deleteForTTask(ttaskId) {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("TTASK_ID", Array.isArray(ttaskId) ? ttaskId : [ttaskId])
            .delete();
    }

    async setTTaskCompletion(ttask_id, conditions, version): Promise<void> {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskCompletionRepository.setTTaskCompletion)",
            );
        }

        try {
            await this.deleteForTTask(ttask_id, version);

            if (
                !conditions ||
                !Array.isArray(conditions) ||
                conditions.length === 0
            ) {
                return;
            }

            for (const condition of conditions) {
                const { entity } = this;

                entity.TTASK_ID = ttask_id;
                entity.TTASK_ID = ttask_id;
                entity.TTC_OPERATOR = condition.operator;
                entity.TTC_VALUE = condition.value;
                entity.TTC_CONCAT_OP = condition.ttc_concat_operator;
                entity.TTC_CANCEL_FLAG = condition.ttc_cancel_flag;
                entity.TVAR_ID = condition.tvar_id;
                entity.TTC_VERSION = version;

                await this.store(entity);
            }
        } catch (err) {
            globalThis.tasLogger.error(
                `Error in TemplateTaskCompletionRepository.setTTaskCompletion ${err.message}`,
                err,
            );
            throw err;
        }
    }
}
