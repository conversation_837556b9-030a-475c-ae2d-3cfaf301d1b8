import { ExternalRightRepository } from "./ExternalRightRepository";

export class mssql_ExternalRightRepository extends ExternalRightRepository {
    getManagerUsers(userId: number) {
        return globalThis.container.client.database.callKnexRaw(
            `
            WITH n("OR<PERSON>TR_ID", "PARENT_ORGSTR_ID", "MANAGER_USER_ID") AS
                (
                SELECT "ORGSTR_ID", "PARENT_ORGSTR_ID", "MANAGER_USER_ID"
                FROM "ORGANIZATION_STRUCTURE"
                WHERE "ORGSTR_ID" = (SELECT "ORGSTR_ID" FROM "USER_ORGANIZATION_STRUCTURE" WHERE "USER_ID" = :USER_ID)
                    UNION ALL
                SELECT nPlus1."ORGSTR_ID", nPlus1."PARENT_ORGSTR_ID", nPlus1."MANAGER_USER_ID"
                FROM "ORGANIZATION_STRUCTURE" as nPlus1, n
                WHERE n."PARENT_ORGSTR_ID" = nPlus1."ORGSTR_ID"
                )
                SELECT "MANAGER_USER_ID"
                FROM n
                WHERE "MANAGER_USER_ID" IS NOT NULL`,
            {
                USER_ID: userId,
            },
            this.connection,
        );
    }
}
