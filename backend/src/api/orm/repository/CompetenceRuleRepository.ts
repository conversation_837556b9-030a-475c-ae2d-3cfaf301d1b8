// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as COMPETENCE from "../entity/const/competenceRuleConsts";
import { CompetenceRule } from "../entity/CompetenceRule";

export class CompetenceRuleRepository extends BaseRepository<CompetenceRule> {
    meta() {
        return {
            tableName: "COMPETENCE_RULES",
            entityName: "CompetenceRule",
            sequenceName: "COMP_RULE_ID_SEQ",
            entity: () => new CompetenceRule(),
        };
    }

    async findRulesForCompetenceName(competenceName) {
        if (!competenceName) {
            return [];
        }

        const rules = await this.connection
            .select()
            .from(this.tableName)
            .where("COMPETENCE_RULE_STATUS", COMPETENCE.STATUS_ACTIVE);

        return _.filter(rules, (rule) => {
            const regex = new RegExp(rule.COMPETENCE_RULE_REGEX, "g");
            return !!competenceName.match(regex);
        });
    }

    async copyDefaults(competenceNameToMatch) {
        const competenceRules = await this.findRulesForCompetenceName(
            competenceNameToMatch,
        );

        const competenceRes = await Promise.all(
            competenceRules.map(async (competenceRule) => {
                const competenceId = await this.createCompetenceFromRule(
                    competenceNameToMatch,
                    competenceRule.COMPETENCE_RULE_ID,
                );
                return {
                    COMPETENCE_ID: competenceId,
                    COMPETENCE_RULE_ID: competenceRule.COMPETENCE_RULE_ID,
                };
            }),
        );
        return competenceRes;
    }

    async createCompetenceFromRule(competenceInputName, competenceRuleId) {
        const competenceRuleRepo = await globalThis.orm.repo(
            "competenceRule",
            this.connection,
        );
        const competenceRepo = await globalThis.orm.repo(
            "competence",
            this.connection,
        );
        const competenceRule = await competenceRuleRepo.get(competenceRuleId);

        let competenceName = competenceRule.COMPETENCE_RULE_NAME;
        const match = new RegExp(competenceRule.COMPETENCE_RULE_REGEX).exec(
            competenceInputName,
        );
        if (Array.isArray(match)) {
            for (let i = 1; i < match.length; i += 1) {
                competenceName = competenceName.replace(
                    new RegExp(`\\$${i}`, "g"),
                    match[i],
                );
            }
        }

        const competence = await competenceRepo.getByAttr(
            "COMPETENCE_NAME",
            competenceName,
        );
        if (competence) {
            return competence.COMPETENCE_ID;
        }

        const competenceId = await competenceRepo.store(
            competenceRepo.getEntity({
                COMPETENCE_NAME: competenceName,
                COMPETENCE_RULE_ID: competenceRule.COMPETENCE_RULE_ID,
                COMPETENCE_DESCRIPTION:
                    competenceRule.COMPETENCE_RULE_DESCRIPTION,
                COMPETENCE_STATUS: competenceRule.COMPETENCE_RULE_STATUS,
                COMPETENCE_SOURCE: `${competenceRuleId}:${competenceInputName}`,
            }),
        );

        await globalThis.orm
            .repo("competenceRuleRole", this.connection)
            .copyDefaultsForCompetence(
                competenceId,
                competenceRule.COMPETENCE_RULE_ID,
            );
        await globalThis.orm
            .repo("competenceRuleUser", this.connection)
            .copyDefaultsForCompetence(
                competenceId,
                competenceRule.COMPETENCE_RULE_ID,
            );
        await globalThis.orm
            .repo("competenceRuleRoleRegex", this.connection)
            .copyDefaultsForCompetence(
                competenceId,
                competenceRule.COMPETENCE_RULE_ID,
            );

        return competenceId;
    }

    /**
     * Marks a Competence to be rebuilt later by the Cron
     *
     * @param {number|Array<number>} competenceId
     * @returns {Knex.QueryBuilder}
     */
    markForRebuild(competenceId) {
        return this.setRebuildStatus(competenceId, COMPETENCE.REBUILD_YES);
    }

    /**
     * Marks a Competence as rebuild by the Cron
     *
     * @param {number|Array<number>} competenceId
     * @returns {*}
     */
    markAsRebuilt(competenceId) {
        return this.setRebuildStatus(competenceId, COMPETENCE.REBUILD_NO);
    }

    /**
     *
     * @param {number|Array<number>} competenceRuleId
     * @param {string} status
     * @returns {Knex.QueryBuilder<TRecord, TResult>}
     */
    setRebuildStatus(competenceRuleId, status) {
        return this.connection(this.tableName)
            .update({
                COMPETENCE_REBUILD_STATUS: status,
            })
            .whereIn(
                "COMPETENCE_RULE_ID",
                Array.isArray(competenceRuleId)
                    ? competenceRuleId
                    : [competenceRuleId],
            );
    }

    /**
     * Returns all Competences considered to be active
     * @returns {Promise<Knex.QueryBuilder>}
     */
    getActive() {
        return this.connection
            .select()
            .from(this.tableName)
            .where("COMPETENCE_RULE_STATUS", COMPETENCE.STATUS_ACTIVE);
    }

    /**
     * Returns all Competences considered to be inactive
     * @returns {Promise<Knex.QueryBuilder>}
     */
    getInactive() {
        return this.connection
            .select()
            .from(this.tableName)
            .whereIn("COMPETENCE_RULE_STATUS", [
                COMPETENCE.STATUS_NOT_ACTIVE,
                COMPETENCE.STATUS_DELETED,
            ]);
    }

    /**
     * Returns a Competence Collection with associated Roles and Users
     *
     * @returns {BaseCollection}
     */
    getExtendedCollection() {
        const collection = this.getCollection(null, "COMP", [
            "CX.REGEX_ID",
            "CX.REGEX_NAME",
            "CX.REGEX_VALUE",
            "R.ROLE_ID",
            "R.ROLE_NAME",
            "U.USER_ID",
            globalThis.database.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" as "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ]);

        // Join COMPETENCE_ROLES
        collection.knex
            .leftJoin(
                "COMPETENCE_RULE_ROLES as CR",
                "CR.COMPETENCE_RULE_ID",
                "COMP.COMPETENCE_RULE_ID",
            )
            .leftJoin("ROLES as R", "R.ROLE_ID", "CR.ROLE_ID");

        // Join COMPETENCE_USERS
        collection.knex
            .leftJoin(
                "COMPETENCE_RULE_USERS as CU",
                "CU.COMPETENCE_RULE_ID",
                "COMP.COMPETENCE_RULE_ID",
            )
            .leftJoin("USERS as U", "U.USER_ID", "CU.USER_ID");

        // Join COMPETENCE_ROLE_REGEX
        collection.knex.leftJoin(
            "COMPETENCE_RULE_ROLE_REGEX as CX",
            "CX.COMPETENCE_RULE_ID",
            "COMP.COMPETENCE_RULE_ID",
        );

        // Do not return 'DELETED' Competences
        collection.knex.whereNot(
            "COMPETENCE_RULE_STATUS",
            COMPETENCE.STATUS_DELETED,
        );

        return collection;
    }

    /**
     * Deletes related Roles of the given Competence
     *
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async deleteRelatedRoles(competenceRuleId) {
        await this.connection
            .from("COMPETENCE_RULE_ROLES")
            .where(competenceRuleId)
            .delete();
    }

    /**
     * Deletes related Users of the given Competence
     *
     * @param {number} competenceRuleId
     * @returns {Promise<void>}
     */
    async deleteRelatedUsers(competenceRuleId) {
        await this.connection
            .from("COMPETENCE_RULE_USERS")
            .where(competenceRuleId)
            .delete();
    }

    async delete(competenceRuleId) {
        // Delete competences.
        const competenceIds = await this.connection
            .pluck("COMPETENCE_ID")
            .from("COMPETENCES")
            .where("COMPETENCE_RULE_ID", competenceRuleId);
        for (const competenceId of competenceIds) {
            await globalThis.orm
                .repo("competence", this.connection)
                .delete(competenceId);
        }

        // Delete rule
        await globalThis.orm
            .repo("competenceRuleRole", this.connection)
            .deleteRoles(competenceRuleId);
        await globalThis.orm
            .repo("competenceRuleRoleRegex", this.connection)
            .deleteRegexes(competenceRuleId);
        await globalThis.orm
            .repo("competenceRuleUser", this.connection)
            .deleteUsers(competenceRuleId);

        await this.connection
            .select()
            .from(this.tableName)
            .where("COMPETENCE_RULE_ID", competenceRuleId)
            .delete();
    }
}
