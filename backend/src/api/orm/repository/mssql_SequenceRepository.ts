// @ts-nocheck
// @ts-nocheck
import { SequenceRepository } from "./SequenceRepository";

export class mssql_SequenceRepository extends SequenceRepository {
    getAndLock(name) {
        return globalThis.container.client.database.callKnexRaw(
            `select * from ${this.tableName} with (updlock, holdlock) where SEQ_NAME = :SEQ_NAME`,
            { SEQ_NAME: name },
            this.connection,
        );
    }
}
