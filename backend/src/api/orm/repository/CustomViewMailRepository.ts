// @ts-nocheck
import moment from "moment";
import _ from "lodash";

import { BaseRepository } from "./BaseRepository";
import * as USER_PARAMETERS from "../entity/const/userParameterConsts";
import * as VARIABLES from "../entity/const/variableConst";
import { CustomViewMail } from "../entity/CustomViewMail";
import { MailOptionsParams } from "../../../client/mail/BaseMailClient";
import { ICustomViewSubscription } from "../../../client/mail/emailClients/customViewSubscription/CustomViewSubscriptionMailClient";
import { LogCategory } from "../../../utils/logger/logConsts";

export class CustomViewMailRepository extends BaseRepository<CustomViewMail> {
    meta() {
        return {
            tableName: "CUSTOM_VIEWS_MAIL",
            entityName: "CustomViewMail",
            entity: () => new CustomViewMail(),
        };
    }

    /**
     * Sends all subscriptions for the current hour. Also checks rights.
     * @returns {Promise<any[]>}
     */
    async sendMails() {
        const toMail = await this.getToSend();
        const userIds = _.uniq(toMail.map((cvm) => cvm.USER_ID));
        const validations = {};
        const usersCache: Record<string, any> = {};

        // Only for ACTIVE users
        const activeUsers = await globalThis.orm
            .repo("user", this.connection)
            .getActiveById(userIds)
            .fetchAll();

        // Get all user language settings
        const enabledLanguages = await globalThis.orm
            .repo("userParameter", this.connection)
            .getUserParameter(
                activeUsers.map((item) => item.USER_ID),
                USER_PARAMETERS.LANGUAGE_CLIENT,
            );

        const enabledDateFormats = await globalThis.orm
            .repo("userParameter", this.connection)
            .getUserParameter(
                activeUsers.map((item) => item.USER_ID),
                "DATE_FORMAT",
            );

        // Must check if user has rights for the CustomView
        for (const user of activeUsers) {
            const roles = await globalThis.orm
                .repo("role", this.connection)
                .getForUser(user.USER_ID, ["RO.ROLE_ID"])
                .fetchAll();
            const organizations = await globalThis.orm
                .repo("organizationStructure", this.connection)
                .getPathForUser(user.USER_ID);

            const roleIds = roles.map((role) => role.ROLE_ID);
            const orgstrIds = organizations[0].PATH.split("/");
            orgstrIds.shift(); // Remove first 'empty' item

            const visibleCustomViews = await globalThis.orm
                .repo("customView", this.connection)
                .getMine(user.USER_ID, roleIds, orgstrIds);

            // Cache IDs
            validations[user.USER_ID] = visibleCustomViews.map(
                (cv) => cv.CV_ID,
            );

            usersCache[user.USER_ID] = {
                roles: roleIds,
                organizations: orgstrIds,
                user_email: user.USER_EMAIL,
                user_full_name: `${user.USER_LAST_NAME} ${user.USER_FIRST_NAME}`,
                user_display_name: user.USER_DISPLAY_NAME,
                user_language: (
                    _.find(enabledLanguages, { USER_ID: user.USER_ID }) || {
                        USRPAR_VALUE: globalThis.dynamicConfig.defaultLang,
                    }
                ).USRPAR_VALUE,
                date_format: (
                    _.find(enabledDateFormats, {
                        USER_ID: user.USER_ID,
                    }) || {
                        USRPAR_VALUE: "L",
                    }
                ).USRPAR_VALUE,
            };
        }

        // Filter mails by Rights validations
        const toMailValidated = toMail.filter(
            (cvm) =>
                validations[cvm.USER_ID] &&
                validations[cvm.USER_ID].includes(cvm.CV_ID),
        );

        // Send validated mails
        const limit = 100;
        await Promise.all(
            toMailValidated.map(async (cvm) => {
                const processes = (
                    await globalThis.orm
                        .repo("customView", this.connection)
                        .getList(
                            cvm.CV_ID,
                            cvm.USER_ID,
                            null,
                            null,
                            null,
                            0,
                            limit,
                            null,
                        )
                ).items;
                // No processes found, CV is empty => do not send!
                if (!processes.length) {
                    return;
                }
                const customView: ICustomViewSubscription["data"]["cv"] = (
                    await globalThis.orm
                        .repo("customView", this.connection)
                        .getExtended(
                            cvm.CV_ID,
                            cvm.USER_ID,
                            usersCache[cvm.USER_ID].roles,
                            usersCache[cvm.USER_ID].organizations,
                        )
                ).models[0];
                // Not all TVARS are in the Custom View, select only the used ones
                const usedTemplateVariablesIds = customView.COLUMNS.map(
                    (item) => item.tvar_id,
                );
                const templateVariables = await globalThis.orm
                    .repo("TemplateVariable", this.connection)
                    .getExtendedForTemplateProcess(
                        cvm.TPROC_ID,
                        usedTemplateVariablesIds,
                    );
                // Cache lovs
                const lovsCache = {};
                // Cache vars for later formatting
                const tVarCache = {};
                templateVariables.forEach((item) => {
                    if (item._lovData.length) {
                        lovsCache[item.TVAR_ID] = {
                            default: item._lovData,
                        };
                        globalThis.dynamicConfig.langs.forEach((lang) => {
                            lovsCache[item.TVAR_ID][lang] =
                                item[`_lovData_${lang.toLowerCase()}`];
                        });
                    }
                    tVarCache[item.TVAR_ID] = {
                        TVAR_TYPE: item.TVAR_TYPE,
                        TVAR_META: item.TVAR_META,
                    };
                });
                // Translate IPROC attributes into SYS VARS IDs
                const user = usersCache[cvm.USER_ID];
                const processesTranslated: ICustomViewSubscription["data"]["processes"] =
                    [];
                const replaceMap = {
                    iproc_name: -1,
                    iproc_description: -2,
                    iproc_inst_owner_user: -3,
                    iproc_actual_start_date: -4,
                    iproc_due_date_finish: -5,
                    iproc_priority: -6,
                    iproc_summary: -7,
                    iproc_actual_finish_date: -8,
                    iproc_case_status: -9,
                };
                const alignRightTvarIds = new Set();
                for (const item of processes) {
                    const out = {};
                    Object.keys(item).forEach((attr) => {
                        // Remove 'v' from the start
                        out[replaceMap[attr] || attr.substr(1)] = item[attr];
                    });
                    // Cast rows
                    // Cannot make simple switch/case block because of system variables, need general detection
                    Object.keys(out).forEach((tvarId) => {
                        if (
                            lovsCache[tvarId] &&
                            tVarCache[tvarId].TVAR_TYPE ===
                                VARIABLES.TYPE_TEXT_LIST
                        ) {
                            // TVAR LOVs translations
                            out[tvarId] = {
                                value: out[tvarId] || "",
                            };
                            globalThis.dynamicConfig.langs.forEach((lang) => {
                                const mutation = _.find(
                                    lovsCache[tvarId][lang],
                                    {
                                        value: out[tvarId].value,
                                    },
                                );
                                out[tvarId][`value_${lang}`] = mutation
                                    ? mutation.title
                                    : null;
                            });
                            // Format date
                        } else if (
                            out[tvarId] &&
                            typeof out[tvarId].getDate === "function"
                        ) {
                            moment.locale(user.user_language);
                            const date = moment(out[tvarId]);
                            out[tvarId] =
                                `${date.format(user.date_format)}, ${date.format("LTS")}`;
                            // Format number - exclude "d", which is the IPROC_ID
                        } else if (
                            (out[tvarId] || out[tvarId] === 0) &&
                            tvarId !== "d" &&
                            out[tvarId] !== "" &&
                            !isNaN(out[tvarId])
                        ) {
                            // Is a system variable
                            if (!tVarCache[tvarId]) {
                                out[tvarId] = out[tvarId].toLocaleString(
                                    "en-US",
                                    { minimumFractionDigits: 2 },
                                );
                                alignRightTvarIds.add(Number(tvarId));
                                // Is a numerical Template variable
                            } else if (
                                tVarCache[tvarId].TVAR_TYPE ===
                                VARIABLES.TYPE_NUMBER
                            ) {
                                // Format decimals
                                const meta = tVarCache[tvarId].TVAR_META
                                    ? JSON.parse(tVarCache[tvarId].TVAR_META)
                                    : { numberOfDecimals: 2 };
                                out[tvarId] = out[tvarId].toLocaleString(
                                    "en-US",
                                    {
                                        minimumFractionDigits:
                                            meta.numberOfDecimals || 2,
                                    },
                                );
                                alignRightTvarIds.add(Number(tvarId));
                            } else if (
                                tVarCache[tvarId].TVAR_TYPE ===
                                VARIABLES.TYPE_NUMBER_LIST
                            ) {
                                alignRightTvarIds.add(Number(tvarId));
                            }
                        }
                    });
                    processesTranslated.push(out);
                }
                // Delete NaN
                alignRightTvarIds.delete(NaN);
                // Prepare data to be rendered
                const data: ICustomViewSubscription["data"] = {
                    user_name: usersCache[cvm.USER_ID].user_display_name,
                    cv: customView,
                    order: usedTemplateVariablesIds,
                    processes: processesTranslated,
                    limit,
                    alignRight: alignRightTvarIds,
                };
                // Send mails
                if (user.user_email) {
                    const mailOptionsParams: MailOptionsParams = {
                        addresses: user.user_email,
                        subject: `${globalThis.__({ phrase: "customViewSubscription", locale: user.user_language })}`,
                        bccOnMoreAddresses: true,
                        ignoreError: true,
                    };
                    await globalThis.tasLogger.runTask(async () => {
                        globalThis.tasLogger.setContextProperty(
                            "category",
                            LogCategory.CATEGORY_MAIL,
                        );
                        try {
                            await globalThis.routerMail.sendEmailViaClient(
                                "customViewSubscription",
                                mailOptionsParams,
                                data,
                                user.user_language,
                                user.date_format,
                            );
                        } catch (err) {
                            const meta = {
                                err,
                                address: user.user_email,
                                subject: `${globalThis.__({ phrase: "customViewSubscription", locale: user.user_language })}`,
                            };
                            globalThis.tasLogger.error(err.message, {
                                meta,
                            });
                            throw err;
                        }
                    });
                }
            }),
        );
    }

    /**
     * Returns all subscriptions to be sent at the current hour.
     *
     * @returns {Knex.builder}
     */
    getToSend() {
        const extraColumns = globalThis.orm
            .repo("customView")
            .getEntity()
            .getTranslatedProperties([], "CV");
        const currentHour = moment().hour();
        return this.connection
            .select(
                [
                    "CVM.CV_ID",
                    "CVM.USER_ID",
                    "CV.CV_NAME",
                    "TP.TPROC_ID",
                ].concat(extraColumns),
            )
            .from(`${this.tableName} as CVM`)
            .innerJoin("CUSTOM_VIEWS as CV", "CV.CV_ID", "CVM.CV_ID")
            .innerJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "CV.TPROC_ID")
            .where("CVM.CVM_HOUR", currentHour);
    }

    /**
     * Gets all subscriptions for a given User. Filtered by CustomView, if provided.
     *
     * @param {number} userId - ID of user
     * @param cvId - ID of CustomView
     * @returns {Collection}
     */
    getForUser(userId, cvId?) {
        const extraColumns = globalThis.orm
            .repo("customView")
            .getEntity()
            .getTranslatedProperties([], "CV");
        const conn = this.connection
            .select(
                [
                    "CV.CV_NAME",
                    "CVM.CVM_ID",
                    "CVM.CV_ID",
                    "CVM.USER_ID",
                    "CVM.CVM_HOUR",
                ].concat(extraColumns),
            )
            .from(`${this.tableName} as CVM`)
            .innerJoin("CUSTOM_VIEWS as CV", "CV.CV_ID", "CVM.CV_ID")
            .where("CVM.USER_ID", userId);

        if (cvId) {
            conn.where("CVM.CV_ID", cvId);
        }

        return globalThis.orm.collection(this.entityName, conn);
    }
}
