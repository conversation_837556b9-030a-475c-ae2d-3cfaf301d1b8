// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import { BaseCollection } from "../BaseCollection";
import { Variable } from "../entity/Variable";
import * as VARIABLE from "../entity/const/variableConst";
import { VariableLov } from "../entity/VariableLov";

export class VariableLovRepository extends BaseRepository<VariableLov> {
    meta() {
        return {
            tableName: "INSTANCE_VARIABLE_LOV",
            entityName: "VariableLov",
            entity: () => new VariableLov(),
            archived: true,
            archParams: {
                subQueryTable: "INSTANCE_VARIABLES",
                subQueryColumn: "IVAR_ID",
            },
        };
    }

    /**
     *
     * @param columns {array|string}
     * @param vars {array<int>|number|BaseCollection}
     * @returns {BaseCollection}
     */
    getForVariables(columns, vars) {
        const coll = this.getCollection(columns);
        const selectedIds =
            vars instanceof BaseCollection
                ? vars.knex
                : Array.isArray(vars)
                  ? vars
                  : [vars];

        coll.knex.whereIn("IVAR_ID", selectedIds).orderBy("IVARLOV_ID");

        return coll;
    }

    /**
     * Join lovs with variables.
     *
     * @param vars {Variable[]} entities
     * @param lovs {VariableLov[]} entitites
     * @returns {Variable[]}
     */
    static joinLov(vars: any = [], lovs: any = {}) {
        // connect data of lovs with Variable entities
        vars.map((item) => {
            if (
                [
                    VARIABLE.TYPE_TEXT_LIST,
                    VARIABLE.TYPE_DATE_LIST,
                    VARIABLE.TYPE_NUMBER_LIST,
                ].includes(item.IVAR_TYPE)
            ) {
                [null]
                    .concat(globalThis.dynamicConfig.langs)
                    .forEach((lang) => {
                        const lov = [];
                        // if not empty object
                        if (
                            Object.keys(lovs).length !== 0 &&
                            typeof lovs[item.IVAR_ID] !== "undefined"
                        ) {
                            lovs[item.IVAR_ID].map((lovItem) => {
                                const lovItemValue = lovItem.getValue(
                                    item.IVAR_TYPE,
                                );
                                const lovItemTitle = lovItem.getValue(
                                    item.IVAR_TYPE,
                                    lang,
                                );

                                // Lang mutation found
                                if (
                                    typeof lovItemTitle !== "undefined" &&
                                    lovItemTitle !== null
                                ) {
                                    // t3b-1592 Do číselníku čísel nejde vložit 0 - nezůstane po uložení
                                    lov.push({
                                        title: lovItemTitle,
                                        value: lovItemValue,
                                    });
                                }
                                return null;
                            });
                        }
                        item.setLovData(lov, lang);
                    });
            }
            return null;
        });
        return vars; // Variable[]
    }

    async fillLovValues(vars) {
        if (!Array.isArray(vars)) {
            vars = [vars];
        }

        for (const item of vars) {
            if (item.value === null || item.IVAR_TYPE !== "DL") {
                continue;
            }
            if (item.isMultichoice()) {
                if (!item.value) {
                    globalThis.tasLogger.error(
                        `Variable for DL store is empty ${item.IVAR_NAME}`,
                    );
                    continue;
                }
                const ids =
                    typeof item.value === "string"
                        ? JSON.parse(item.value || "[]")
                        : item.value;

                try {
                    if (item.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER) {
                        const users = await globalThis.orm
                            .repo("user", this.connection)
                            .getMulti(ids);

                        item.lovValues = {};
                        users.forEach((user) => {
                            item.lovValues[user.USER_ID] =
                                user.USER_DISPLAY_NAME;
                        });
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT
                    ) {
                        const orgs = await globalThis.orm
                            .repo("organizationStructure", this.connection)
                            .getMulti(ids);
                        item.lovValues = {};
                        orgs.forEach((orgstr) => {
                            item.lovValues[orgstr.ORGSTR_ID] =
                                orgstr.ORGSTR_NAME;
                        });
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE
                    ) {
                        const roles = await globalThis.orm
                            .repo("role", this.connection)
                            .getMulti(ids);
                        item.lovValues = {};
                        roles.forEach((role) => {
                            item.lovValues[role.ROLE_ID] = role.ROLE_NAME;
                        });
                    }
                } catch (err) {
                    globalThis.tasLogger.error({
                        err,
                    });
                    item.lovValues = {};
                }
            } else {
                try {
                    if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER &&
                        item.value > 0
                    ) {
                        const user = await globalThis.orm
                            .repo("user", this.connection)
                            .get(item.value);
                        item.lovValue = user.USER_DISPLAY_NAME;
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT
                    ) {
                        const org = await globalThis.orm
                            .repo("organizationStructure", this.connection)
                            .get(item.value);
                        item.lovValue = org.ORGSTR_NAME;
                    } else if (
                        item.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE
                    ) {
                        const role = await globalThis.orm
                            .repo("role", this.connection)
                            .get(item.value);
                        item.lovValue = role.ROLE_NAME;
                    }
                } catch (err) {
                    globalThis.tasLogger.error(
                        `${err} ivar_id = ${item.IVAR_ID}`,
                    );
                    item.lovValue = null;
                }
            }
        }
        return vars;
    }

    copyVariableLovsFromTemplate(iprocId) {
        // Translations
        const extraColumns = [];
        const tprocEntity = globalThis.orm
            .repo("VariableLov", this.connection)
            .getEntity();
        const attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (
                attrs[attrName].translated &&
                attrName.indexOf("IVARLOV_TEXT_VALUE") > -1
            ) {
                extraColumns.push(attrName);
            }
        });

        const extraColumnsInto = extraColumns;
        const extraColumnsFrom = extraColumns.map(
            (col) => `"TVL"."T${col.substring(1)}"`,
        );

        const columnsInto = [
            "ORG_ID",
            "IVARLOV_ID",
            "IVAR_ID",
            "IVARLOV_IS_SELECTED",
            "IVARLOV_TEXT_VALUE",
            "IVARLOV_NUMBER_VALUE",
            "IVARLOV_DATE_VALUE",
        ].concat(extraColumnsInto);
        const columnsFrom = [
            `"IV"."IVAR_ID"`,
            `"TVL"."TVARLOV_IS_SELECTED"`,
            `"TVL"."TVARLOV_TEXT_VALUE"`,
            `"TVL"."TVARLOV_NUMBER_VALUE"`,
            `"TVL"."TVARLOV_DATE_VALUE"`,
        ].concat(extraColumnsFrom);

        const copyLovsSql = `
        INSERT INTO "INSTANCE_VARIABLE_LOV"
            (
                "${columnsInto.join(`","`)}"
            )
        SELECT  1, ${globalThis.orm.db.sequence("IVARLOV_ID_SEQ").toString()}, se.*
        FROM
(
    SELECT ${columnsFrom.join(",")} 
    FROM "INSTANCE_VARIABLES" "IV"
    LEFT JOIN "TEMPLATE_VARIABLE_LOV" "TVL"
    ON "TVL"."TVAR_ID" = "IV"."TVAR_ID"
    WHERE "IV"."IPROC_ID" = :IPROC_ID
    AND "IV"."IVAR_TYPE" IN ('LT', 'LN', 'LD')
    ORDER BY "TVL"."TVARLOV_ID" ASC
) se
       `;

        return globalThis.container.client.database.callKnexRaw(
            copyLovsSql,
            { IPROC_ID: iprocId },
            this.connection,
        );
    }

    /**
     *
     * @param {boolean} returnDynamicLists - if true then dynamic lists are returned
     * @param {boolean} returnFiltered - if true then returns filered list
     * @access public
     * @return array of LOV in following format array('TYPE'=>.., 'VALUE'=>..)
     */
    async getLOV(variable, returnDynamicLists = false, returnFiltered = false) {
        // I never see this to be true. Not implemented for filtering.
        if (returnFiltered) {
            throw new Error("returnFiltered is not implementend now!");
        }

        // Is dynamic list ?
        if (
            variable.IVAR_TYPE === Variable.consts.TYPE_DYN_LIST &&
            returnDynamicLists
        ) {
            // Is user ?
            const lovs = [];
            if (variable.IVAR_ATTRIBUTE === Variable.consts.ATTR_USER) {
                const userRepo = globalThis.orm.repo("user", this.connection);
                const users = await userRepo.getByStatus("A").collectAll();

                // Filtering dyn.list - Implement later.
                // $filtered = array(); // filtered id
                // if ($returnFiltered && !empty($info['IVAR_BIG_VALUE'])) {
                //     $json = json_decode($info['IVAR_BIG_VALUE'], TRUE);
                //     if (!empty($json['filtered'])) {
                //         $filtered = $json['filtered'];
                //     }
                // }

                if (Array.isArray(users) && users.length > 0) {
                    users.forEach((user) => {
                        lovs.push({
                            ID: user.USER_ID,
                            VALUE: user._raw.USER_DISPLAY_NAME,
                            TYPE: Variable.consts.TYPE_DYN_LIST,
                        });
                    });
                }
                return lovs;
            }

            // Is organizationn
            if (variable.IVAR_ATTRIBUTE === Variable.consts.ATTR_ORG_STRUCT) {
                const repo = globalThis.orm.repo(
                    "organizationStructure",
                    this.connection,
                );
                const orgs =
                    await globalThis.container.client.database.callKnexRaw(
                        repo.getFlatTreeSQL(),
                        undefined,
                        this.connection,
                    );
                if (Array.isArray(orgs) && orgs.length > 0) {
                    orgs.forEach((org) => {
                        const val =
                            " ".repeat((org.LEVEL - 1) * 4) + org.ORGSTR_NAME;
                        lovs.push({
                            ID: org.ORGSTR_ID,
                            VALUE: val,
                            TYPE: Variable.consts.TYPE_DYN_LIST,
                        });
                    });
                }

                return lovs;
            }

            // Is role
            if (variable.IVAR_ATTRIBUTE === Variable.consts.ATTR_ROLE) {
                const repo = globalThis.orm.repo("role", this.connection);
                const roles = await repo
                    .getCollection(["ROLE_ID", "ROLE_NAME"])
                    .collectAll();

                if (Array.isArray(roles) && roles.length > 0) {
                    roles.forEach((role) => {
                        lovs.push({
                            ID: role.ROLE_ID,
                            VALUE: role.ROLE_NAME,
                            TYPE: Variable.consts.TYPE_DYN_LIST,
                        });
                    });
                }
                return lovs;
            }

            const dlRepo = globalThis.orm.repo("dynamicList", this.connection);
            const values = await dlRepo
                .getByName(variable.DLIST_NAME)
                .collectAll();
            if (Array.isArray(values) && values.length > 0) {
                values.forEach((val) => {
                    lovs.push({
                        ID: val.DLIST_VALUE,
                        VALUE: val.DLIST_VALUE,
                        TYPE: Variable.consts.TYPE_DYN_LIST,
                    });
                });
                return lovs;
            }

            throw new Error("Can not get LOV for variable");
        } else {
            return await this.getForVariables(
                [
                    "IVARLOV_ID",
                    "IVAR_ID",
                    "IVARLOV_IS_SELECTED",
                    "IVARLOV_TEXT_VALUE",
                    "IVARLOV_NUMBER_VALUE",
                    "IVARLOV_DATE_VALUE",
                ],
                variable.IVAR_ID,
            )
                .collectAll()
                .then((lovs) => {
                    if (Array.isArray(lovs) && lovs.length > 0) {
                        const out = [];
                        lovs.forEach((lov) => {
                            const row = {
                                ID: lov.getValue(variable.IVAR_TYPE),
                                TYPE: variable.IVAR_TYPE,
                            };
                            row.VALUE = row.ID;
                            out.push(row);
                        });

                        return out;
                    }
                    return [];
                });
        }
    }

    /**
     * @param {number} iProcId
     * @returns {BaseCollection}
     */
    getVariablesByProcess(iProcId) {
        const conn = this.connection
            .select(["IV.IVAR_ID"])
            .from("INSTANCE_VARIABLES as IV")
            .where("IV.IPROC_ID", iProcId)
            .where("IV.IVAR_TYPE", VARIABLE.TYPE_TEXT_LIST);

        return globalThis.orm.collection("Variable", conn);
    }

    async setLOV(variable, values, selectedValue) {
        await this.connection
            .select("IVAR_ID")
            .from(this.tableName)
            .where("IVAR_ID", variable.IVAR_ID)
            .delete();

        const type = variable.IVAR_TYPE;

        const storedEntities = [];
        for (const value of values) {
            const entity = this.getEntity({
                ORG_ID: 1,
                IVAR_ID: variable.IVAR_ID,
                IVARLOV_IS_SELECTED:
                    value === selectedValue
                        ? Variable.consts.LOV_IS_SELECTED_YES
                        : Variable.consts.LOV_IS_SELECTED_NO,
                IVARLOV_TEXT_VALUE:
                    type === Variable.consts.TYPE_LOV_TEXT ? value : null,
                IVARLOV_NUMBER_VALUE:
                    type === Variable.consts.TYPE_LOV_NUMBER ? value : null,
                IVARLOV_DATE_VALUE:
                    type === Variable.consts.TYPE_LOV_DATE ? value : null,
            });

            const result = await this.store(entity);
            storedEntities.push(result);
        }

        return storedEntities;
    }

    /**
     * Returns all mutations for all list variables in process
     * @param {number} iprocId
     * @param {array|null} ivarIds null or array of LT variables.
     * @returns {Promise}
     */
    getListMutations(iprocId, ivarIds = null) {
        const cols = ["IVARLOV_TEXT_VALUE"];
        globalThis.dynamicConfig.langs.forEach((language) => {
            cols.push(`${cols[0]}_${language.toUpperCase()}`);
        });
        if (Array.isArray(ivarIds) && ivarIds.length > 0) {
            cols.push("IVAR_ID");
            const coll = this.getCollection([...cols], "IVL");
            coll.knex.whereIn("IVAR_ID", ivarIds);
            return coll.fetchAll();
        }
        const coll = this.getCollection([...cols, "IV.IVAR_ID"], "IVL");
        coll.knex
            .join("INSTANCE_VARIABLES as IV", "IV.IVAR_ID", "IVL.IVAR_ID")
            .where("IPROC_ID", iprocId)
            .andWhere("IVAR_TYPE", "LT");
        return coll.fetchAll();
    }
}
