// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import * as calcConsts from "../entity/const/calcConsts";
import { InstanceTaskJSCalculation } from "../entity/InstanceTaskJSCalculation";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class InstanceTaskJSCalculationRepository extends BaseRepository<InstanceTaskJSCalculation> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_JS_CALCULATIONS",
            entityName: "InstanceTaskJSCalculation",
            sequenceName: "ITASKJS_CALC_SEQ",
            entity: () => new InstanceTaskJSCalculation(),
        };
    }

    deleteFor(itaskId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("ITASK_ID", itaskId)
            .delete();
    }

    getFor(itaskId) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("ITASK_ID", itaskId)
            .orderBy("ITJSCALC_ID");
    }

    /**
     * Get calculations for task.
     * @param task
     * @param execType S-start, H-hand, E-end, R-recalc
     * @param {Number|null} version
     */
    async getByExecType(task, execType, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getByExecType)",
            );
        }

        const pfx = version > 0 ? "T" : "I"; // Use ICALC or TCALC.

        execType = Array.isArray(execType) ? execType : [execType];

        const columnsToSelect = [
            globalThis.database.raw(`"${pfx}TJSCALC_ID" as "${pfx}TJSCALC_ID"`),
            globalThis.database.raw(`${task.ITASK_ID} as "ITASK_ID"`),
            globalThis.database.raw(`${version} as "CALC_VERSION"`),
            globalThis.database.raw(`"${pfx}TJSCALC_JS" as "ITJSCALC_JS"`),
            globalThis.database.raw(
                `"${pfx}TJSCALC_EXEC_START" as "ITJSCALC_EXEC_START"`,
            ),
            globalThis.database.raw(
                `"${pfx}TJSCALC_TITLE" as "ITJSCALC_TITLE"`,
            ),
            globalThis.database.raw(
                `"${pfx}TJSCALC_EXEC_END" as "ITJSCALC_EXEC_END"`,
            ),
            globalThis.database.raw(
                `"${pfx}TJSCALC_EXEC_HAND" as "ITJSCALC_EXEC_HAND"`,
            ),
            globalThis.database.raw(
                `"${pfx}TJSCALC_EXEC_RECALC" as "ITJSCALC_EXEC_RECALC"`,
            ),
            globalThis.database.raw(
                `"${pfx}TJSCALC_EXEC_PULL" as "ITJSCALC_EXEC_PULL"`,
            ),
        ];

        if (version > 0) {
            columnsToSelect.push(
                globalThis.database.raw(
                    `"${pfx}TJSCALC_JS_ES6" as "ITJSCALC_JS_ES6"`,
                ),
            );
        }

        let conn = this.connection
            .select(columnsToSelect)
            .from(
                version > 0 ? "TEMPLATE_TASK_JS_CALCULATIONS" : this.tableName,
            )
            .where(`${pfx}TASK_ID`, task[`${pfx}TASK_ID`])
            .where(function () {
                if (execType.indexOf(calcConsts.EXEC_ON_START) !== -1) {
                    this.orWhere(`${pfx}TJSCALC_EXEC_START`, "Y");
                }
                if (execType.indexOf(calcConsts.EXEC_ON_END) !== -1) {
                    this.orWhere(`${pfx}TJSCALC_EXEC_END`, "Y");
                }
                if (execType.indexOf(calcConsts.EXEC_RECALC) !== -1) {
                    this.orWhere(`${pfx}TJSCALC_EXEC_RECALC`, "Y");
                }
                if (execType.indexOf(calcConsts.EXEC_HAND) !== -1) {
                    this.orWhere(`${pfx}TJSCALC_EXEC_HAND`, "Y");
                }
                if (execType.indexOf(calcConsts.EXEC_PRE_PULL) !== -1) {
                    this.orWhere(`${pfx}TJSCALC_EXEC_PULL`, "Y");
                }
            });

        if (version > 0) {
            conn = conn.where("TTJSCALC_VERSION", version);
        }

        conn = conn.orderBy(`${pfx}TJSCALC_ID`);

        return globalThis.orm.collection("InstanceTaskJSCalculation", conn);
    }
}
