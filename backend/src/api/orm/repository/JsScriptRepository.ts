import { BaseRepository } from "./BaseRepository";
import * as JsScriptConstants from "../entity/const/jsScriptConst";
import { JsScript } from "../entity/JsScript";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { BaseCollection } from "../BaseCollection";

export class JsScriptRepository extends BaseRepository<JsScript> {
    meta() {
        return {
            tableName: "JS_SCRIPTS",
            entityName: "JsScript",
            entity: () => new JsScript(),
        };
    }

    getByName(name: string): Promise<JsScript> {
        const rows = this.connection
            .select()
            .from(this.tableName)
            .where("NAME", name);
        if (!Array.isArray(rows) || rows.length === 0) {
            throw new UserException(
                `JS with name ${name} not found`,
                "JS_SCRIPT_NOT_FOUND",
            );
        }

        const row = rows[0];
        return this.castRow(row, this.entity.getAttributes(false, true));
    }

    getByType(type: string): BaseCollection<JsScript> {
        const collection: BaseCollection<JsScript> = this.getCollection();
        collection.knex.from(this.tableName).where("JS_TYPE", type);
        return collection;
    }

    async getTTaskCalcGlobalScripts(ttjscalcId: number): Promise<number[]> {
        const ids = await this.connection
            .select("TTJSCALC_APPEND_SCRIPTS")
            .from("TEMPLATE_TASK_JS_CALCULATIONS")
            .where("TTJSCALC_ID", ttjscalcId);
        if (ids.length === 0 || !ids[0].TTJSCALC_APPEND_SCRIPTS) {
            return [];
        }

        try {
            return JSON.parse(ids[0].TTJSCALC_APPEND_SCRIPTS);
        } catch (err) {
            globalThis.tasLogger.error(
                `Can not parse TTJSCALC_APPEND_SCRIPTS for TTJSCALC_ID=${ttjscalcId}`,
                {
                    adminCalc: ids[0].TTJSCALC_APPEND_SCRIPTS,
                },
            );
            throw err;
        }
    }

    async updateVersion(): Promise<(number | number[] | null)[]> {
        try {
            const conn = await globalThis.container.client.database.callKnexRaw(
                `SELECT ${globalThis.orm.db.sequence(`JS_SCRIPTS_VERSION_SEQ`)} as "NID" ${globalThis.orm.db.fromDual()}`,
                undefined,
                this.connection,
            );

            if (!conn[0].NID) {
                throw new Error(
                    `Fails in retrieving new id for ${this._tableName}.`,
                );
            }

            const nid = conn[0].NID;

            const scripts: JsScript[] = await this.getByType(
                JsScriptConstants.TYPE_FRONTEND,
            ).collectAll();

            const storedEntities: (number | number[] | null)[] = [];
            for (const script of scripts) {
                const entity: JsScript = this.getEntity();
                entity.JS_ID = script.JS_ID;
                entity.JS_VERSION = nid;

                const storedEntity: number | number[] | null =
                    await this.store(entity);
                storedEntities.push(storedEntity);
            }

            return storedEntities;
        } catch (err: any) {
            globalThis.tasLogger.error(err.message, {
                err,
            });
            throw err;
        }
    }

    async generateUniqueScriptName(
        name: string,
        number: number = 0,
    ): Promise<string> {
        const newName: string = number === 0 ? name : `${name}_(${number})`;
        const existingScript = await this.getId("NAME", newName);
        if (existingScript) {
            return await this.generateUniqueScriptName(name, number + 1);
        }
        return newName;
    }
}
