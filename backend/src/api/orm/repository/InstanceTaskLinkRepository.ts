// @ts-nocheck
// @ts-nocheck
import { InstanceTaskLink } from "../entity/InstanceTaskLink";
import { BaseRepository } from "./BaseRepository";

export class InstanceTaskLinkRepository extends BaseRepository<InstanceTaskLink> {
    meta() {
        return {
            tableName: "INSTANCE_TASK_LINKS",
            entityName: "InstanceTaskLink",
            entity: () => new InstanceTaskLink(),
            archived: false,
        };
    }

    async copyLinkFromTemplate(iprocId, ttaskId, itaskMap) {
        const tlinks = await this.connection
            .select([
                "TTASKLINK_ID",
                "TTASKLINK_IS_MANDATORY",
                "TTASKLINK_TYPE",
                "TTASKLINK_FROM_TTASK_ID",
                "TTASKLINK_TO_TTASK_ID",
                "TTASKLINK_PRIORITY",
            ])
            .from("TEMPLATE_TASK_LINKS")
            .where("TTASKLINK_FROM_TTASK_ID", ttaskId);

        if (!Array.isArray(tlinks) || tlinks.length === 0) {
            return true;
        }
        // Create links
        const linkMap = {};
        const condRepo = globalThis.orm.repo(
            "instanceLinkCondition",
            this.connection,
        );
        for (const tlink of tlinks) {
            // New link in template but task not in old instance_process
            if (
                !itaskMap[tlink.TTASKLINK_TO_TTASK_ID] ||
                !itaskMap[tlink.TTASKLINK_FROM_TTASK_ID]
            ) {
                globalThis.tasLogger.warning(
                    `Ignoring link for ${iprocId}. Probably old instance_process with updated temlpate_process links.`,
                    {
                        iprocId,
                        itaskMap,
                        tlink,
                    },
                );
                continue;
            }

            const ilinkId = await this.store(
                this.getEntity({
                    ORG_ID: 1,
                    IPROC_ID: iprocId,
                    ITASKLINK_IS_MANDATORY: tlink.TTASKLINK_IS_MANDATORY,
                    ITASKLINK_PRIORITY: tlink.TTASKLINK_PRIORITY,
                    ITASKLINK_TYPE: tlink.TTASKLINK_TYPE,
                    ITASKLINK_TO_TTASK_ID:
                        itaskMap[tlink.TTASKLINK_TO_TTASK_ID].ITASK_ID,
                    ITASKLINK_FROM_TTASK_ID:
                        itaskMap[tlink.TTASKLINK_FROM_TTASK_ID].ITASK_ID,
                    TTASKLINK_ID: tlink.TTASKLINK_ID,
                }),
            );
            if (ilinkId) {
                linkMap[tlink.TTASKLINK_ID] = ilinkId;
            }
        }

        await condRepo.copyConditionFromTemplate(linkMap);

        return linkMap;
    }
}
