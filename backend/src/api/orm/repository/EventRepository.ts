// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import { RuleDefinition } from "../entity/RuleDefinition";
import { Event } from "../entity/Event";
import { BaseCollection } from "../BaseCollection";
import { UtilsService } from "../../services/UtilsService";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { EventHandlerMan } from "../../events/eventHandlerMan";
import * as eventConsts from "../entity/const/eventConst";
import * as processConsts from "../entity/const/processConst";
import * as taskConsts from "../entity/const/taskConst";
import * as variableConsts from "../entity/const/variableConst";

export class EventRepository extends BaseRepository<Event> {
    meta() {
        return {
            tableName: "EVENT",
            entityName: "Event",
            entity: () => new Event(),
            archived: false,
        };
    }

    getEventButtons(iprocIds) {
        const columns = ["IT.ITASK_ID", "IT.IPROC_ID", "IT.ITASK_NAME"];
        globalThis.dynamicConfig.langs.forEach((lang) => {
            columns.push(`TT.TTASK_NAME_${lang.toUpperCase()}`);
        });

        return this.connection
            .select(columns)
            .from("INSTANCE_TASKS as IT")
            .leftJoin("TEMPLATE_TASKS as TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .where("IT.ITASK_STATUS", taskConsts.STATUS_DELAYED)
            .where("IT.ITASK_TYPE", taskConsts.TYPE_EVENT_WAIT)
            .whereNotNull("ITASK_EVENT")
            .whereIn(
                "IPROC_ID",
                Array.isArray(iprocIds) ? iprocIds : [iprocIds],
            )
            .where("TT.TTASK_CAN_BE_BUTTON", "Y")
            .then((events) => {
                const out = {};
                if (Array.isArray(events) && events.length > 0) {
                    events.forEach((event) => {
                        if (!out[event.IPROC_ID]) {
                            out[event.IPROC_ID] = [];
                        }
                        out[event.IPROC_ID].push(event);
                    });
                }
                return UtilsService.recursivelyLowercaseJSONKeys(out);
            });
    }

    async deleteSubprocess(ttaskId): Promise<void> {
        const eveDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );

        const events = await eveDefRepo.getByTTaskId(ttaskId);

        if (!Array.isArray(events) || events.length === 0) {
            return;
        }

        await Promise.all(
            events.map(async (eventDef) => {
                const event = this.entity;
                event.EVEDEF_ID = eventDef.EVEDEF_ID;
                return await this.delete(event);
            }),
        );
    }

    async delete(entity): Promise<void> {
        const ruleDefRepo = globalThis.orm.repo(
            "ruleDefinition",
            this.connection,
        );
        const ruleRepo = globalThis.orm.repo("rule", this.connection);
        const eveDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );

        const rules = await ruleDefRepo.connection
            .select()
            .from(ruleDefRepo.tableName)
            .where("EVEDEF_ID", entity.EVEDEF_ID);

        if (!Array.isArray(rules) || rules.length === 0) {
            return;
        }

        // Use Promise.all to handle rule deletions concurrently
        await Promise.all(
            rules.map(
                async (ruleDefinition) =>
                    await ruleRepo.deleteTRule(ruleDefinition.RDEF_ID),
            ),
        );

        await eveDefRepo.connection
            .select()
            .from(eveDefRepo.tableName)
            .where("EVEDEF_ID", entity.EVEDEF_ID)
            .delete();
    }

    createSubprocess(
        eventName,
        eventDesc,
        eventNameReturn,
        eventDescReturn,
        subpTprocId,
        mapSubp,
        mapSubr,
    ) {
        if (!Array.isArray(subpTprocId) || subpTprocId.length !== 2) {
            throw new UserException(
                "subpTprocId in EventRepository.createSubprocess is invalid. subpTprocId must be array of 2 items. [TPROC_ID, TPROC_VERSION]",
            );
        }

        const eveDefRepo = globalThis.orm.repo(
            "eventDefinition",
            this.connection,
        );
        const ruleDefRepo = globalThis.orm.repo(
            "ruleDefinition",
            this.connection,
        );
        const ruleDefParamRepo = globalThis.orm.repo(
            "ruleDefinitionParam",
            this.connection,
        );

        return eveDefRepo
            .createEvent(eventName, eventDesc, true)
            .then((eveDefId) =>
                ruleDefRepo
                    .create(
                        eveDefId,
                        "",
                        RuleDefinition.consts.TRULE_TYPE_PROCESS,
                        subpTprocId.join("."),
                        "ACTIVE",
                    )
                    .then((ruleId) => ruleId),
            )
            .then((ruleId) => {
                if (mapSubp && Array.isArray(mapSubp)) {
                    return ruleDefParamRepo.updateRuleParams(ruleId, mapSubp);
                }
                return true;
            })
            .then(() =>
                eveDefRepo.createEvent(eventNameReturn, eventDescReturn, true),
            )
            .then((eveDefId) =>
                ruleDefRepo
                    .create(
                        eveDefId,
                        "",
                        RuleDefinition.consts.TRULE_TYPE_RETURN,
                        eventName,
                        "ACTIVE",
                    )
                    .then((ruleId) => ruleId),
            )
            .then((ruleId) => {
                if (mapSubr && Array.isArray(mapSubr)) {
                    return ruleDefParamRepo.updateRuleParams(ruleId, mapSubr);
                }
                return true;
            });
    }

    getSubprocessMappings(ttaskId) {
        const parSubp = `$SUBP_${ttaskId}`;
        const parSubr = `$SUBR_${ttaskId}`;

        return this.connection
            .select([
                "EDEF.EVEDEF_NAME",
                globalThis.database.raw(
                    `case when "EDEF"."EVEDEF_NAME" = '${parSubp}' then 'subp' else 'subr' end "EVEDEF_TYPE"`,
                ),
                "PAR.RDEFPAR_TYPE",
                globalThis.database.raw(
                    `${globalThis.orm.db.nvl(`"TVAR"."TVAR_NAME"`, `"PAR"."RDEFPAR_NAME"`)} as "RDEFPAR_NAME"`,
                ),
                globalThis.database.raw(
                    `${globalThis.orm.db.nvl(`"SVAR"."TVAR_NAME"`, `"PAR"."EVEPAR_NAME"`)} as "EVEPAR_NAME"`,
                ),
                "PAR.TVAR_ID",
                "PAR.TVAR_ID_DEST",
                "PAR.RDEFPAR_VALUE",
            ])
            .from("EVENT_DEFINITION as EDEF")
            .join("RULE_DEFINITION as RDEF", "RDEF.EVEDEF_ID", "EDEF.EVEDEF_ID")
            .join("RULE_DEFINITION_PARAM as PAR", "RDEF.RDEF_ID", "PAR.RDEF_ID")
            .leftJoin(
                "TEMPLATE_VARIABLES as SVAR",
                "SVAR.TVAR_ID",
                "PAR.TVAR_ID",
            )
            .leftJoin(
                "TEMPLATE_VARIABLES as TVAR",
                "TVAR.TVAR_ID",
                "PAR.TVAR_ID_DEST",
            )
            .whereIn("EDEF.EVEDEF_NAME", [parSubp, parSubr])
            .then((params) => {
                const out = { subp: [], subr: [] };
                if (Array.isArray(params) && params.length > 0) {
                    params.forEach((param) => {
                        out[param.EVEDEF_TYPE].push({
                            rdefpar_type: param.RDEFPAR_TYPE,
                            rdefpar_name: param.RDEFPAR_NAME,
                            evepar_name: param.EVEPAR_NAME,
                            tvar_id: param.TVAR_ID,
                            tvar_name: param.TVAR_NAME,
                            tvar_id_dest: param.TVAR_ID_DEST,
                            rdefpar_value: param.RDEFPAR_VALUE,
                        });
                    });
                }
                return out;
            });
    }

    getEventWaitMappings(ttaskId) {
        const parSubp = `$EVEW_${ttaskId}`;

        return this.connection
            .select([
                "EDEF.EVEDEF_NAME",
                globalThis.database.raw(
                    `case when "EDEF"."EVEDEF_NAME" = '${parSubp}' then 'subp' else 'subr' end "EVEDEF_TYPE"`,
                ),
                "PAR.RDEFPAR_TYPE",
                globalThis.database.raw(
                    `${globalThis.orm.db.nvl(`"TVAR"."TVAR_NAME"`, `"PAR"."RDEFPAR_NAME"`)} as "RDEFPAR_NAME"`,
                ),
                globalThis.database.raw(
                    `${globalThis.orm.db.nvl(`"SVAR"."TVAR_NAME"`, `"PAR"."EVEPAR_NAME"`)} as "EVEPAR_NAME"`,
                ),
                "PAR.TVAR_ID",
                "PAR.TVAR_ID_DEST",
                "PAR.RDEFPAR_VALUE",
            ])
            .from("EVENT_DEFINITION as EDEF")
            .join("RULE_DEFINITION as RDEF", "RDEF.EVEDEF_ID", "EDEF.EVEDEF_ID")
            .join("RULE_DEFINITION_PARAM as PAR", "RDEF.RDEF_ID", "PAR.RDEF_ID")
            .join("TEMPLATE_TASKS as TT", "EDEF.EVEDEF_NAME", " TT.TTASK_EVENT")
            .leftJoin(
                "TEMPLATE_VARIABLES as SVAR",
                "SVAR.TVAR_ID",
                "PAR.TVAR_ID",
            )
            .leftJoin(
                "TEMPLATE_VARIABLES as TVAR",
                "TVAR.TVAR_ID",
                "PAR.TVAR_ID_DEST",
            )
            .where("TT.TTASK_ID", ttaskId)
            .where("RDEF.RDEF_VALUE", parSubp)
            .then((params) => {
                const out = { subr: [] };
                if (Array.isArray(params) && params.length > 0) {
                    params.forEach((param) => {
                        out.subr.push({
                            rdefpar_type: param.RDEFPAR_TYPE,
                            rdefpar_name: param.RDEFPAR_NAME,
                            evepar_name: param.EVEPAR_NAME,
                            tvar_id: param.TVAR_ID,
                            tvar_id_dest: param.TVAR_ID_DEST,
                            rdefpar_value: param.RDEFPAR_VALUE,
                        });
                    });
                }
                return out;
            });
    }

    getAllTprocMapping(tprocId, tprocIdSub) {
        const out = {};
        return this.getMapping("mappingTarget", "SUBP", tprocIdSub).then(
            (s1) => {
                out.subp_target = s1;
                return this.getMapping("mappingSource", "SUBP", tprocId).then(
                    (s1) => {
                        out.subp_source = s1;
                        return this.getMapping(
                            "mappingTarget",
                            "SUBR",
                            tprocId,
                        ).then((s1) => {
                            out.subr_target = s1;
                            return this.getMapping(
                                "mappingSource",
                                "SUBR",
                                tprocIdSub,
                            ).then((s1) => {
                                out.subr_source = s1;
                                return out;
                            });
                        });
                    },
                );
            },
        );
    }

    getTEventSourceVariables(evedefId, rdefType, rdefValue) {
        switch (rdefType) {
            case "PROCESS":
                return this.connection
                    .select(
                        "TTASK.ORG_ID",
                        "TTASK.TPROC_ID",
                        "TTASK.TTASK_ID",
                        "VAR.TVAR_ID",
                        "VAR.TVAR_NAME",
                        "VAR.TVAR_TYPE",
                    )
                    .from("EVENT_DEFINITION as EDEF")
                    .join(
                        "TEMPLATE_TASKS as TTASK",
                        "TTASK.TTASK_EVENT",
                        "EDEF.EVEDEF_NAME",
                    )
                    .join(
                        "TEMPLATE_TASK_VAR_USAGE as USG",
                        "USG.TTASK_ID",
                        "TTASK.TTASK_ID",
                    )
                    .join(
                        "TEMPLATE_VARIABLES as VAR",
                        "VAR.TVAR_ID",
                        "USG.TVAR_ID",
                    )
                    .where("EDEF.EVEDEF_ID", evedefId);
            case "RETURN":
                return (
                    this.connection
                        .select(
                            "RDEF.RDEF_VALUE",
                            this.connection.raw(`-1 as "TTASK_ID"`),
                            "TVAR.TVAR_ID",
                            "TVAR.TVAR_NAME",
                            "TVAR.TVAR_TYPE",
                        )
                        .from("EVENT_DEFINITION as EDEF")
                        .join(
                            "RULE_DEFINITION as RDEF",
                            "RDEF.EVEDEF_ID",
                            "EDEF.EVEDEF_ID",
                        )
                        //         .joinRaw('left join template_variables t on t.tvar_id like ?', ['%-1%'])
                        .joinRaw(
                            `LEFT JOIN "TEMPLATE_VARIABLES" AS "TVAR" ON "TV"."TPROC_ID" LIKE  "R"."RDEF_VALUE" ${globalThis.orm.db.concat()} '.%'`,
                        ) // TODO test.
                        .where("EDEF.EVEDEF_NAME", rdefValue)
                );
            case "CSV_RUN_PROCESSES":
            case "CSV_UPDATE_PROCESSES":
            case "CSV_MERGE_PROCESSES":
            case "CSV_UPDATE_PROCESS":
            case "SUBP":
            case "SUBR":
                return [];
            case "CSV_EXPORT_PROCESSES":
                return this.connection
                    .select(
                        "ORG_ID",
                        "TPROC_ID",
                        this.connection.raw(`-1 "TTASK_ID"`),
                        "TVAR_ID",
                        "TVAR_NAME",
                        "TVAR_TYPE",
                    )
                    .from("TEMPLATE_VARIABLES")
                    .where("TPROC_ID", rdefValue);
            default:
                throw new UserException(`Unknown rdefType: ${rdefType}.`);
        }
    }

    getTTaskVariablesByEvent(tevent_id, type) {
        let conn = this.connection
            .select(
                "TTASK.TTASK_EVENT",
                "TTASK.TPROC_ID",
                "VAR.TVAR_ID",
                "VAR.TVAR_NAME",
                "VAR.TVAR_TYPE",
                "TPL.TPROC_NAME",
            )
            .from("EVENT_DEFINITION as EDEF")
            .leftJoin(
                "TEMPLATE_TASKS as TTASK",
                "EDEF.EVEDEF_NAME",
                "TTASK.TTASK_EVENT",
            )
            .leftJoin(
                "TEMPLATE_TASK_VAR_USAGE as USG",
                "USG.TTASK_ID",
                "TTASK.TTASK_ID",
            )
            .leftJoin("TEMPLATE_VARIABLES as VAR", "USG.TVAR_ID", "VAR.TVAR_ID")
            .leftJoin("TEMPLATE_PROCESSES as TPL", (builder) => {
                builder
                    .on("TPL.TPROC_ID", "VAR.TPROC_ID")
                    .andOn(globalThis.database.raw(`"TPROC_VERSION" = 1`));
            })
            .whereNot("TTASK.TTASK_EVENT_WAIT", "1");

        if (isNaN(tevent_id)) {
            conn = conn.whereRaw(
                "EDEF.EVEDEF_ID = (SELECT MAX(EVEDEF_ID) FROM EVENT_DEFINITION WHERE EVEDEF_NAME = ?)",
                [tevent_id],
            );
        } else {
            conn = conn.where("EDEF.EVEDEF_ID", tevent_id);
        }

        if (type) {
            conn = conn.where("TVAR_TYPE", "in", type);
        }

        return conn;
    }

    getVariablesByEventName(evedefName) {
        return this.connection
            .select(
                "VAR.TVAR_ID",
                "VAR.TVAR_NAME",
                "VAR.TVAR_TYPE",
                "VAR.ORG_ID",
            )
            .from("TEMPLATE_TASKS as TASK")
            .join("TEMPLATE_VARIABLES as VAR", "VAR.TPROC_ID", "TASK.TPROC_ID")
            .where("TASK.TTASK_EVENT", evedefName)
            .whereNot("TASK.TTASK_EVENT_WAIT", "1");
    }

    async getMapping(
        action,
        type,
        value,
        event?: any,
    ): Promise<Record<string, any>> {
        const varRepo = globalThis.orm.repo(
            "TemplateVariable",
            this.connection,
        );
        const out = [];

        if (action === "extEventParams") {
            const eventHandlerMan = new EventHandlerMan();
            eventHandlerMan.eventHSelectEventHandler(type);

            // Set params
            const repo = globalThis.orm.repo("rule", this.connection);
            const ruleVariables = await repo.getTRuleVariableList(
                value /* rdefid */,
            );
            eventHandlerMan.eventHSetRuleVariables(ruleVariables);
            return await eventHandlerMan.eventHGetParameterList();
        }
        // TODO refactor other action types.

        return await new Promise((resolve, reject) => {
            if (action === "mappingSource") {
                if (
                    ["PROCESS", "SUBP", "SUBR", "CSV_EXPORT_PROCESSES"].indexOf(
                        type,
                    ) !== -1
                ) {
                    out.push({
                        param_id: "$TERMIN",
                        param_name: "$Termín",
                        param_type: variableConsts.TYPE_DATE,
                    });

                    if (type === "CSV_EXPORT_PROCESSES") {
                        out.push({
                            param_id: "$PROC_OWNER",
                            param_name: "$Iniciátor",
                            param_type: variableConsts.TYPE_TEXT,
                        });
                        out.push({
                            param_id: "$PROC_START_DATE",
                            param_name: "$Datum zadání",
                            param_type: variableConsts.TYPE_DATE,
                        });
                        out.push({
                            param_id: "$PROC_FINISH_DATE",
                            param_name: "$Skutečný konec",
                            param_type: variableConsts.TYPE_DATE,
                        });
                    }
                }

                if (
                    [
                        "RETURN",
                        "PROCESS",
                        "CSV_RUN_PROCESSES",
                        "CSV_UPDATE_PROCESSES",
                        "CSV_MERGE_PROCESSES",
                        "CSV_UPDATE_PROCESS",
                        "CSV_EXPORT_PROCESSES",
                    ].indexOf(type) !== -1
                ) {
                    return this.getTEventSourceVariables(event, type, value)
                        .then((data) => {
                            if (Array.isArray(data) && data.length > 0) {
                                data.forEach((tvar) => {
                                    out.push({
                                        param_id: tvar.TVAR_ID,
                                        param_name: tvar.TVAR_NAME,
                                        param_type: tvar.TVAR_TYPE,
                                    });
                                });
                            }
                            return resolve(out);
                        })
                        .catch((err) => reject(err));
                }
                if (type === "SUBP" || type === "SUBR") {
                    return varRepo
                        .getForTemplateProcess(value)
                        .collectAll()
                        .then((tvars) => {
                            if (Array.isArray(tvars) && tvars.length > 0) {
                                tvars.forEach((tvar) => {
                                    out.push({
                                        param_id: tvar.TVAR_ID,
                                        param_name: tvar.TVAR_NAME,
                                        param_type: tvar.TVAR_TYPE,
                                    });
                                });
                            }
                            return resolve(out);
                        })
                        .catch((err) => reject(err));
                }
                if (type === "EVENTWAIT") {
                    return this.getTTaskVariablesByEvent(value)
                        .then((data) => {
                            if (Array.isArray(data) && data.length > 0) {
                                data.forEach((tvar) => {
                                    out.push({
                                        param_id: tvar.TVAR_ID,
                                        param_name: tvar.TVAR_NAME,
                                        param_type: tvar.TVAR_TYPE,
                                        tproc_name: tvar.TPROC_NAME,
                                    });
                                });
                            }
                            return resolve(out);
                        })
                        .catch((err) => reject(err));
                }
            } else if (action === "mappingTarget") {
                if (
                    [
                        "PROCESS",
                        "SUBP",
                        "SUBR",
                        "CSV_RUN_PROCESSES",
                        "CSV_UPDATE_PROCESSES",
                        "CSV_MERGE_PROCESSES",
                        "CSV_UPDATE_PROCESS",
                    ].indexOf(type) !== -1
                ) {
                    if (type === "SUBR") {
                        out.push({
                            param_id: "$TERMIN",
                            param_name: "$Termín",
                            param_type: variableConsts.TYPE_DATE,
                        });
                    }
                    if (
                        type === "CSV_RUN_PROCESSES" ||
                        type === "CSV_MERGE_PROCESSES"
                    ) {
                        out.push({
                            param_id: "$PROC_OWNER",
                            param_name: "$Iniciátor",
                            param_type: variableConsts.TYPE_TEXT,
                        });
                    }

                    return varRepo
                        .getForTemplateProcess(value)
                        .collectAll()
                        .then((tvars) => {
                            if (Array.isArray(tvars) && tvars.length > 0) {
                                tvars.forEach((tvar) => {
                                    out.push({
                                        param_id: tvar.TVAR_ID,
                                        param_name: tvar.TVAR_NAME,
                                        param_type: tvar.TVAR_TYPE,
                                    });
                                });
                            }
                            return resolve(out);
                        })
                        .catch((err) => reject(err));
                }
                if (type === "CSV_EXPORT_PROCESSES") {
                    return resolve([]);
                }
                if (type === "RETURN") {
                    return this.getVariablesByEventName(value)
                        .then((tvars) => {
                            if (Array.isArray(tvars) && tvars.length > 0) {
                                tvars.forEach((tvar) => {
                                    out.push({
                                        param_id: tvar.TVAR_ID,
                                        param_name: tvar.TVAR_NAME,
                                        param_type: tvar.TVAR_TYPE,
                                    });
                                });
                            }
                            return resolve(out);
                        })
                        .catch((err) => reject(err));
                }
                if (
                    type === "CSV_UPDATE_LIST" ||
                    type === "UPDATE_LIST_OF_PROCESSES"
                ) {
                    out.push({
                        param_id: "$LIST_NAME",
                        param_name: "$Název seznamu",
                        param_type: variableConsts.TYPE_TEXT,
                    });
                    out.push({
                        param_id: "$LIST_VALUE",
                        param_name: "$Hodnota seznamu",
                        param_type: variableConsts.TYPE_TEXT,
                    });

                    return resolve(out);
                }
                return reject(
                    new UserException("Unknown type.", "UNKNOWN_EVENT_TYPE"),
                );
            } else {
                return reject(
                    new UserException(
                        "Unknown action. Should be mappingTarget or mappingSource.",
                        "UNKNOWN_EVENT_ACTION",
                    ),
                );
            }
            return reject(
                new UserException(
                    "Unknown action or type.",
                    "UNKNOWN_EVENT_DATA",
                ),
            );
        });
    }

    /**
     * Get all instance tasks waiting for current evend_definition.
     * @param {Event} event
     * @param {number} ttaskId
     * @returns {*}
     */
    getWaitingTasks(event, ttaskId) {
        const conn = this.connection
            .select("IT.*")
            .from("INSTANCE_TASKS as IT")
            .join("INSTANCE_PROCESSES as IP", "IT.IPROC_ID", "IP.IPROC_ID")
            .join("EVENT as E", "IT.ITASK_EVENT", "E.EVEDEF_NAME")
            .leftJoin(
                "INSTANCE_VARIABLES as IV",
                "IV.IVAR_ID",
                "IT.ITASK_EVENT_FILTER_ID",
            ) // Variables for read filter value from process
            .leftJoin("EVENT_PARAM as EP", function parentEvent() {
                // Get all params for current event
                this.on("EP.EVE_ID", "E.EVE_ID").andOn(
                    "EP.EVEPAR_NAME",
                    "IT.ITASK_EVENT_FILTER_VALUE",
                );
            })
            .where("IP.IPROC_STATUS", processConsts.STATUS_ACTIVE) // Only active processes
            .where("IT.ITASK_STATUS", taskConsts.STATUS_DELAYED) // Only Later tasks
            .where("IT.TTASK_ID", ttaskId)
            .where("E.EVE_ID", event.EVE_ID)
            .where(function filterProcesses() {
                // FILTER target process.
                this.where(function noFilter() {
                    // Filter is not applied
                    this.whereNull("IT.ITASK_EVENT_FILTER_ID").whereNull(
                        "IT.ITASK_EVENT_FILTER_VALUE",
                    );
                })
                    .orWhere(function textMatch() {
                        // Text value match filter value
                        this.whereNotNull("IV.IVAR_TEXT_VALUE").whereRaw(
                            "IV.IVAR_TEXT_VALUE = EP.EVEPAR_TEXT_VALUE",
                        );
                    })
                    .orWhere(function numberMatch() {
                        // Number value match filter value
                        this.whereNotNull("IV.IVAR_NUMBER_VALUE").whereRaw(
                            "IV.IVAR_NUMBER_VALUE = EP.EVEPAR_NUMBER_VALUE",
                        );
                    })
                    .orWhere(function dateMatch() {
                        // Date value match filter value
                        this.whereNotNull("IV.IVAR_DATE_VALUE").whereRaw(
                            "IV.IVAR_DATE_VALUE = EP.EVEPAR_DATE_VALUE",
                        );
                    });
            });
        return globalThis.orm.collection<"instanceTask">("instanceTask", conn);
    }

    /**
     * Returns events to start.
     *
     * @return {BaseCollection}
     */
    getWaitingBackgroundEvents(
        fromTime: Date | null = null,
    ): BaseCollection<Event> {
        const conn = this.connection
            .select("EV.*", "IP_ORIG.IPROC_ID", "IP.IPROC_INST_OWNER_USER_ID")
            .from("EVENT as EV")
            .leftJoin(
                "INSTANCE_PROCESSES as IP",
                "IP.IPROC_ID",
                globalThis.database.raw(
                    `${globalThis.orm.db.substr(`"EV"."EVEDEF_NAME"`, 7)}`,
                ),
            )
            .leftJoin(
                "INSTANCE_PROCESSES as IP_ORIG",
                "IP_ORIG.IPROC_ID",
                globalThis.database.raw(
                    `${globalThis.orm.db.substr(`"SID_ORIG"`, 5)}`,
                ),
            )
            .where("EV.EVEDEF_NAME", "like", "$SUBP%")
            .where("IP.IPROC_STATUS", processConsts.STATUS_ACTIVE)
            .whereNull("SID_ANSWER")
            .where("EV.EVE_STATUS", eventConsts.STATUS_READY_BACKGROUND)
            .whereNotNull("IP_ORIG.IPROC_ID")
            .union(function () {
                this.select(
                    "EV.*",
                    "IP_ORIG.IPROC_ID",
                    "IP_ORIG.IPROC_INST_OWNER_USER_ID",
                )
                    .from("EVENT as EV")
                    .leftJoin(
                        "INSTANCE_PROCESSES as IP_ORIG",
                        "IP_ORIG.IPROC_ID",
                        globalThis.database.raw(
                            `${globalThis.orm.db.substr(`"SID_ORIG"`, 5)}`,
                        ),
                    )
                    .where("EV.EVE_STATUS", eventConsts.STATUS_READY_BACKGROUND)
                    .where("EV.EVEDEF_NAME", "not like", "$SUBP%")
                    .whereNull("EV.SID_ANSWER")
                    .whereNotNull("IP_ORIG.IPROC_ID")
                    .where("IP_ORIG.IPROC_STATUS", processConsts.STATUS_ACTIVE);
            });
        const coll = this.createCollection(conn);

        if (fromTime) {
            coll.knex.where("EVE_DATETIME", ">", fromTime);
        }

        return coll;
    }
}
