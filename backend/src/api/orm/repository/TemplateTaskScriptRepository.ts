import { TemplateTaskScript } from "../entity/TemplateTaskScript";
import { BaseRepository } from "./BaseRepository";

export class TemplateTaskScriptRepository extends BaseRepository<TemplateTaskScript> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_SCRIPTS",
            entityName: "TemplateTaskScript",
            entity: () => new TemplateTaskScript(),
        };
    }

    async getForITask(itaskId: number, type: "START" | "END") {
        if (["START", "END"].indexOf(type) === -1) {
            globalThis.tasLogger.error(`Invalid calculation type ${type}`);
            return [];
        }

        const data = await this.connection
            .select("TTS.*")
            .from("INSTANCE_TASKS as IT")
            .leftJoin(
                "TEMPLATE_TASK_SCRIPTS as TTS",
                "IT.TTASK_ID",
                "TTS.TTASK_ID",
            )
            .where("IT.ITASK_ID", itaskId)
            .where("TTSCRIPT_EXEC_TYPE", type);
        const casted = await this.castRows(
            data,
            this.entity.getAttributes(false, true),
        );
        return casted;
    }
}
