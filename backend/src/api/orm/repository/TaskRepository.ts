// @ts-nocheck
// @ts-nocheck
import { Task } from "../entity/Task";
import { BaseRepository } from "./BaseRepository";

export class TaskRepository extends BaseRepository<Task> {
    meta() {
        return {
            tableName: "INSTANCE_TASKS",
            entityName: "Task",
            defaultAlias: "IT",
            entity: () => new Task(),
            archived: true,
        };
    }

    async getTaskForHistory() {
        // Fetch the data
        return await this.connection
            .select()
            .from("TEMPLATE_TASKS as TT")
            .innerJoin("INSTANCE_TASKS", "IT.TTASK_ID", "TT.TTASK_ID");
    }

    getTaskList(userId) {
        const columns = [
            globalThis.database.raw(
                `"TLSU1"."IS_MANAGER" as "TLSU1IS_MANAGER"`,
            ),
            globalThis.database.raw(
                `"TLSUG"."IS_MANAGER" as "TLSUGIS_MANAGER"`,
            ),
            "TLSIT.ITASK_ID",
            "TLSIT.ITASK_TYPE",
            "TLSIT.ITASK_SUBPROCESS_IPROC_ID",
            "TLSIT.TTASK_ID",
            "TLSIT.ORG_ID",
            "TLSIT.IPROC_ID",
            "TLSIT.ITASK_NAME",
            "TLSIT.ITASK_IS_IPROC_ID",
            "TLSIT.ITASK_DESCRIPTION",
            "TLSIT.ITASK_STATUS",
            "TLSIT.ITASK_DUE_DATE_START",
            "TLSIT.ITASK_DUE_DATE_FINISH",
            "TLSIT.ITASK_ACTUAL_DATE_START",
            "TLSIT.ITASK_ACTUAL_DATE_FINISH",
            "TLSIT.ITASK_AUTO_START",
            "TLSIT.ITASK_USER_ID",
            "TLSIT.ITASK_FINISHED_BY_USER_ID",
            "TLSIT.ITASK_ASSESMENT_ROLE_ID",
            "TLSIT.ITASK_ASSESMENT_HIERARCHY",
            "TLSIT.ITASK_ASSESMENT_METHOD",
            "TLSIT.ITASK_ASSESMENT_USER_ID",
            "TLSIT.ITASK_ASSESMENT_ORGSTR_ID",
            "TLSIT.ITASK_ASSESMENT_ORGSTR_CNST",
            "TLSIT.ITASK_PETRI_NET_INPUT",
            "TLSIT.ITASK_DUE_OFFSET",
            "TLSIT.ITASK_DURATION",
            "TLSIT.ITASK_SUFFICIENT_END",
            "TT.TTASK_INSTRUCTION",
            "TLSIT.ITASK_DISC_FLAG",
            "TLSIT.ITASK_MULTIINSTANCE_FLAG",
            "TLSIT.ITASK_DUTY",
            "TLSIT.ITASK_COMMENT",
            "TLSIP.IPROC_STATUS",
            this.connection.raw(`"TLSU1A"."USER_DISPLAY_NAME" AS "USER_NAME"`),
            "TLSIT.ITASK_ASSESMENT_USER_ID",
            this.connection.raw(
                `"TLSU2A"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "TLSU2A"."USER_FIRST_NAME" as "IPROC_OWNER_FULL_NAME"`,
            ),
            this.connection.raw(
                `"TLSU2A"."USER_DISPLAY_NAME" AS "IPROC_OWNER_DISPLAY_NAME"`,
            ),
            this.connection.raw(
                `"TLSAU"."USER_DISPLAY_NAME" AS "ITASK_ASSESMENT_USER"`,
            ),
            this.connection.raw(
                `(select COUNT("TTJSCALC_EXEC_RECALC") FROM "TEMPLATE_TASK_JS_CALCULATIONS" WHERE "TTASK_ID" = "TLSIT"."TTASK_ID" and "TTJSCALC_EXEC_RECALC" LIKE 'Y') as "EXEC_RECALC"`,
            ),
            "TLSIP.IPROC_NAME",
            "TLSIP.IPROC_PRIORITY",
            "TLSIP.IPROC_MAIN_IPROC_ID",
            "TPROC_NAME",
            this.connection.raw(`"TLSUG"."USER_ID" "GUARANT_ID"`),
            this.connection.raw(
                `
            CASE
            WHEN "TLSIT"."ITASK_USER_ID"  = ?
            AND "TLSIP"."IPROC_STATUS"    = 'A'
            AND "TLSIT"."ITASK_TYPE" NOT IN ('P','W')
            AND "TLSIT"."ITASK_STATUS"   <> 'L'
            THEN 1
            ELSE 0
            END C_SOLVE
        `,
                [userId],
            ),
            this.connection.raw(
                `
            CASE
            WHEN ("TLSUG"."USER_ID"     = ?
            OR "TLSu1"."IS_MANAGER"     = 1
            OR "TLSug"."IS_MANAGER"     = 1)
            AND "TLSIP"."IPROC_STATUS"  = 'A'
            AND "TLSIT"."ITASK_STATUS" <> 'L'
            AND "TLSIT"."ITASK_TYPE"   <> 'P'
            THEN 1
            WHEN "TLSIT"."ITASK_STATUS" = 'T'
            THEN 1
            ELSE 0
            END C_TAKE
        `,
                [userId],
            ),
            this.connection.raw(
                `
            CASE
            WHEN ("TLSUG"."USER_ID"     = ?
            OR "TLSu1"."IS_MANAGER"     = 1
            OR "TLSug"."IS_MANAGER"     = 1
            OR "TLSIT"."ITASK_USER_ID"  = ?)
            AND "TLSIP"."IPROC_STATUS"  = 'A'
            AND "TLSIT"."ITASK_STATUS" <> 'L'
            AND "TLSIT"."ITASK_TYPE"   <> 'P'
            THEN 1
            ELSE 0
            END C_HANDOVER
        `,
                [userId, userId],
            ),
            this.connection.raw(
                `
            CASE
            WHEN ("TLSIT"."ITASK_USER_ID"            = ?
            OR ("TLSIP"."TPROC_ID"                  IS NULL
            AND ( "TLSIP"."IPROC_INST_OWNER_USER_ID" = ?
            OR "TLSug"."USER_ID"                     = ?)))
            AND "TLSIP"."IPROC_STATUS"               = 'A'
            THEN 1
            ELSE 0
            END C_ADD
        `,
                [userId, userId, userId],
            ),
            this.connection.raw(
                `
            CASE
            WHEN (("TLSug"."USER_ID"        = ?
            OR "TLSu1"."IS_MANAGER"         = 1
            OR "TLSug"."IS_MANAGER"         = 1
            OR ("TLSIT"."ITASK_USER_ID"     = ?
            AND "TLSIP"."TPROC_ID"         IS NULL))
            AND ("TLSIT"."ITASK_DUE_OFFSET" = 'po'
            OR "TLSIT"."ITASK_DURATION"     = 'po'))
            AND "TLSIP"."IPROC_STATUS"      = 'A'
            THEN 1
            ELSE 0
            END C_DUE
        `,
                [userId, userId],
            ),
        ];

        return this.connection
            .select(columns)
            .from("INSTANCE_TASKS as TLSIT")
            .joinRaw(
                `
            JOIN (
    SELECT 
        "IP"."ORG_ID" AS "ORG_ID",
        "IP"."IPROC_ID" AS "IPROC_ID",
        "IP"."TPROC_ID" AS "TPROC_ID",
        "IP"."IPROC_NAME" AS "IPROC_NAME",
        "IP"."IPROC_DESCRIPTION" AS "IPROC_DESCRIPTION",
        "IP"."IPROC_NOTES" AS "IPROC_NOTES",
        "IP"."IPROC_INST_OWNER_USER_ID" AS "IPROC_INST_OWNER_USER_ID",
        "IP"."IPROC_STATUS" AS "IPROC_STATUS",
        "IP"."IPROC_ACTUAL_START_DATE" AS "IPROC_ACTUAL_START_DATE",
        "IP"."IPROC_ACTUAL_FINISH_DATE" AS "IPROC_ACTUAL_FINISH_DATE",
        "IP"."IPROC_DUE_DATE_FINISH" AS "IPROC_DUE_DATE_FINISH",
        "IP"."IPROC_PRIORITY" AS "IPROC_PRIORITY",
        "IP"."IPROC_SUMMARY" AS "IPROC_SUMMARY",
        "IP"."IPROC_MAIN_IPROC_ID" AS "IPROC_MAIN_IPROC_ID",
        "IP"."IPROC_VIS_ROLE_ID" AS "IPROC_VIS_ROLE_ID",
        "U1"."USER_ID" AS "USER_ID",
        "U1"."USER_LAST_NAME",
        ${globalThis.orm.db.concatColumns()}
        "U1"."USER_FIRST_NAME" AS "IPROC_OWNER_FULL_NAME",
        "U1"."USER_DISPLAY_NAME" AS "IPROC_OWNER_DISPLAY_NAME",
        "TP"."TPROC_NAME" AS "TPROC_NAME",
        
        CASE
            WHEN -1 IN (SELECT "ROLE_ID" FROM "USER_ROLES" WHERE "USER_ID" = ?) 
            OR "IP"."IPROC_INST_OWNER_USER_ID" = ? 
            THEN 1
            ELSE 0
        END AS "C_SUS",

        CASE
            WHEN "IP"."IPROC_STATUS" = 'A'
            THEN 1
            ELSE 0
        END AS "C_HEV"
    FROM (
        SELECT "EXT"."IPROC_ID"
        FROM (
            SELECT "IP"."IPROC_ID"
            FROM "INSTANCE_PROCESSES" "IP"
            LEFT JOIN "HEADERS" "HDR"
                ON ("HDR"."HEADER_ID" = "IP"."HEADER_ID")
            LEFT JOIN "TEMPLATE_PROCESSES" "TP"
                ON ("IP"."TPROC_ID" = "TP"."TPROC_ID")
            LEFT JOIN "USER_ROLES" "UR"
                ON (
                    "UR"."ROLE_ID" = "IP"."IPROC_VIS_ROLE_ID"
                    OR "UR"."ROLE_ID" = "TP"."TPROC_VIS_ROLE_ID"
                    OR "UR"."ROLE_ID" = "HDR"."HDR_VIS_ROLE_ID"
                    OR (
                        "UR"."ROLE_ID" = -3 
                        AND NOT EXISTS (
                            SELECT "USER_ID" 
                            FROM "USER_PARAMETERS" 
                            WHERE "USER_ID" = ? 
                            AND "USRPAR_NAME" = 'DISABLE_ADMIN' 
                            AND "USRPAR_VALUE" = 'Y'
                        )
                    )
                )
            LEFT JOIN "USERS" "U"
                ON ("U"."USER_ID" = "UR"."USER_ID")
            WHERE "U"."USER_ID" = ?
            UNION
            SELECT "IPDR"."IPROC_ID"
            FROM "INSTANCE_PROCESS_DYN_RIGHTS" "IPDR"
            WHERE "IPDR"."USER_ID" = ?
            UNION
            SELECT "IPDR"."IPROC_ID"
            FROM "INSTANCE_PROCESS_STATIC_RIGHTS" "IPDR"
            WHERE "IPDR"."USER_ID" = ?
        ) "EXT"
        LEFT JOIN "INSTANCE_PROCESSES" "IP"
            ON ("EXT"."IPROC_ID" = "IP"."IPROC_ID")
        WHERE "IP"."IPROC_ID" IS NOT NULL
    ) "IPER"
    LEFT JOIN "INSTANCE_PROCESSES" "IP"
        ON ("IP"."IPROC_ID" = "IPER"."IPROC_ID")
    LEFT JOIN "USERS" "U1"
        ON ("U1"."USER_ID" = "IP"."IPROC_INST_OWNER_USER_ID")
    LEFT JOIN "TEMPLATE_PROCESSES" "TP"
        ON ("IP"."TPROC_ID" = "TP"."TPROC_ID")
) "TLSIP" 
ON "TLSIP"."IPROC_ID" = "TLSIP"."IPROC_ID"
        `,
                [userId, userId, userId, userId, userId, userId],
            )
            .leftJoin(
                "USERS as TLSU1A",
                "TLSIT.ITASK_USER_ID",
                "TLSU1A.USER_ID",
            )
            .leftJoin("TEMPLATE_TASKS as TT", "TLSIT.TTASK_ID", "TT.TTASK_ID")
            .leftJoin(
                "USERS as TLSU2A",
                "TLSIP.IPROC_INST_OWNER_USER_ID",
                "TLSU2A.USER_ID",
            )
            .leftJoin(
                "USERS as TLSAU",
                "TLSAU.USER_ID",
                "TLSIT.ITASK_ASSESMENT_USER_ID",
            );
    }

    // Mé úkoly +k přiřazení, +k načsování
    getTaskMineList(userId) {
        const repo = globalThis.orm.repo("instanceTask", this.connection);
        return repo.mine(userId);
    }

    // Mé úkoly +k přiřazení, +k načsování
    getTaskMineAfterTerminList(userId, sup) {
        return globalThis.orm
            .repo("Task", this.connection)
            .getTaskList(userId, sup)
            .where(function () {
                // Mé úkoly
                this.where(function () {
                    this.where("IPROC_STATUS", "A")
                        .where("ITASK_USER_ID", userId)
                        .where("ITASK_STATUS", "A")
                        .whereNotIn("ITASK_TYPE", ["P", "W"]);
                    // K přiřazení
                })
                    .orWhere(function () {
                        this.where("IPROC_STATUS", "A")
                            .where("ITASK_STATUS", "W")
                            .whereIn("ITASK_ASSESMENT_METHOD", ["S", "U", "P"])
                            .whereNotNull("ITASK_USER_ID");
                        // K načasování
                    })
                    .orWhere(function () {
                        this.where("IPROC_STATUS", "A")
                            .where("ITASK_STATUS", "W")
                            .where(function () {
                                this.where("ITASK_DUE_OFFSET", "po").orWhere(
                                    "ITASK_DURATION",
                                    "po",
                                );
                            });
                    });
            })
            .whereRaw(
                `"ITASK_DUE_DATE_FINISH" < ${globalThis.orm.db.sysDate()}`,
            )
            .as("IT");
    }

    // K načasování
    getToAssignDues(userId, sup) {
        return globalThis.orm
            .repo("Task", this.connection)
            .getTaskList(userId, sup)
            .where(function () {
                this.where("IPROC_STATUS", "A")
                    .where("ITASK_STATUS", "W")
                    .where(function () {
                        this.where("ITASK_DUE_OFFSET", "po").orWhere(
                            "ITASK_DURATION",
                            "po",
                        );
                    });
            })
            .as("IT");
    }

    getTaskAllList(userId) {
        const repo = globalThis.orm.repo("instanceTask", this.connection);
        return repo.getAll(userId);
    }

    // Aktivní
    getActiveTaskList(userId, sup) {
        return globalThis.orm
            .repo("Task", this.connection)
            .getTaskList(userId, sup)
            .where(function () {
                this.where("ITASK_STATUS", "A");
            })
            .as("IT");
    }

    // Neaktivní
    getNonActiveTaskList(userId, sup) {
        return globalThis.orm
            .repo("Task", this.connection)
            .getTaskList(userId, sup)
            .where(function () {
                this.where("ITASK_STATUS", "D");
            })
            .as("IT");
    }

    // Dokončené
    getDone(userId) {
        const repo = globalThis.orm.repo("instanceTask", this.connection);
        return repo.done(userId);
    }

    // Dle řešitelů
    getBySolvers(userId) {
        const repo = globalThis.orm.repo("instanceTask", this.connection);
        return repo.getBySolvers(userId);
    }

    // K načasování
    getAfterTermin(userId, sup) {
        return globalThis.orm
            .repo("Task", this.connection)
            .getTaskList(userId, sup)
            .where(function () {
                this.whereRaw(
                    `
        EXISTS (SELECT 1 FROM "INSTANCE_TASKS" "IT" WHERE "IT"."IPROC_ID" = "TLSIP"."IPROC_ID"
        AND "IT"."ITASK_STATUS" IN ('A','W')
        AND "IT"."ITASK_DUE_DATE_FINISH" IS NOT NULL
        AND "IT"."ITASK_DUE_DATE_FINISH" < ${globalThis.orm.db.sysDate()} and "ITASK_USER_ID"=? and "ITASK_STATUS" = 'A')
            `,
                    [userId],
                );
            })
            .as("IT");
    }

    /**
     * getListWithoutLeftLink - returns list of tasks which have no left link
     *
     * @param processId
     * @access public
     * @return BaseList
     */
    getListWithoutLeftLink(processId) {
        const taskColl = this.getCollection();
        taskColl.knex
            .leftJoin(
                "INSTANCE_TASK_LINKS as ITL",
                "ITL.ITASKLINK_TO_TTASK_ID",
                "IT.ITASK_ID",
            )
            .where("ITL.ITASKLINK_TO_TTASK_ID is NULL")
            .where("IT.IPROC_ID", processId);
        return taskColl;
    }

    getByTtaskId(processId, ttaskId) {
        const taskColl = this.getCollection();
        taskColl.knex.where("TTASK_ID", ttaskId).where("IPROC_ID", processId);
        return taskColl;
    }
}
