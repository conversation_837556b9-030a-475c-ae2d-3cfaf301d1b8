import { BaseRepository } from "./BaseRepository";
import * as TASK from "../entity/const/taskConst";
import * as PROCESS from "../entity/const/processConst";
import * as ROLE from "../entity/const/roleConst";
import { STATUS_ACTIVE as USER_STATUS_ACTIVE } from "../entity/const/userConst";
import { Task } from "../entity/Task";
import { BaseCollection } from "../BaseCollection";

export class ArchivedInstanceTaskRepository extends BaseRepository<Task> {
    meta() {
        return {
            tableName: "ARCH_INSTANCE_TASKS",
            entityName: "archivedInstanceTask",
            entity: () => new Task(),
        };
    }

    async all(
        userId: number,
        extraColumns?: string[] | null,
        checkRoles?: boolean,
        _skipComputedValues?: any,
    ): Promise<BaseCollection<Task>> {
        const orgStrRepo = globalThis.orm.repo(
            "organizationStructure",
            this.connection,
        );

        const assessmentManagerSql = `"IT"."ITASK_ASSESMENT_USER_ID" in (select "USER_ID" from "MANAGED_USERS")`;
        const procOwnerManagerSql = `"IP"."IPROC_INST_OWNER_USER_ID" in (select "USER_ID" from "MANAGED_USERS")`;
        const solverManagerSql = `"IT"."ITASK_USER_ID" in (select "USER_ID" from "MANAGED_USERS")`;
        const hasHrRightsSql = `
        -- Access by Template Process
        "TP"."TPROC_HR_ROLE_ID" IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
            OR
        -- Access by Instance Process
        "IP"."IPROC_HR_ROLE_ID" IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
            OR
        -- Access by Header
        "TH"."HEADER_HR_ROLE_ID" IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
        `;

        const user = !checkRoles
            ? await globalThis.container.service.temporary.cacheModule.getCachedUser(
                  userId,
              )
            : undefined;
        //Casting as integer for SQL case query
        const inspector = user && user.isInspector() ? 1 : 0;
        const globalSupervisor = user && user.isGlobalSupervisor() ? 1 : 0;

        // Add translations.
        if (!Array.isArray(extraColumns)) {
            extraColumns = [];
        }
        // iProc translations
        const iProcEntity = globalThis.orm.repo("archivedProcess").getEntity();
        let attrs = iProcEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
            }
        });

        // Tproc translations
        const tprocEntity = globalThis.orm.repo("templateProcess").getEntity();
        attrs = tprocEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TP.${attrName}`);
            }
        });

        // Itask translations are in columns below

        // TTask translations
        const ttaskEntity = globalThis.orm.repo("templateTask").getEntity();
        attrs = ttaskEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TT.${attrName}`);
            }
        });

        // Header translations
        const headerEntity = globalThis.orm.repo("header").getEntity();
        attrs = headerEntity.attributes();
        Object.keys(attrs).forEach((attrName) => {
            if (attrs[attrName].translated) {
                extraColumns.push(`TH.${attrName}`);
            }
        });

        let columns: any[] = this.getEntity().getColumnNames(undefined, "IT");
        columns = columns.concat([
            "TH.HEADER_NAME",
            "TH.HEADER_CODE",
            "TH.HEADER_ENABLED",
            "TH.TPROC_ID",
            "TH.HEADER_ID",
            "IP.IPROC_STATUS",
            "IT_FINISHED.USER_DISPLAY_NAME AS ITASK_FINISHED_BY_USER_DNAME",
            globalThis.database.raw(
                `"IT_SOLVER"."USER_DISPLAY_NAME" AS "ITASK_USER_NAME"`,
            ),
            "IT_SOLVER.USER_DISPLAY_NAME AS ITASK_USER_DISPLAY_NAME",
            globalThis.database.raw(
                `"IP_OWNER"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "IP_OWNER"."USER_FIRST_NAME" AS "IPROC_OWNER_FULL_NAME"`,
            ),
            "IP_OWNER.USER_DISPLAY_NAME AS IPROC_OWNER_DISPLAY_NAME",
            globalThis.database.raw(
                `"IT_ASS"."USER_DISPLAY_NAME" AS "ITASK_ASSESMENT_USER"`,
            ),
            "IT_ASS.USER_DISPLAY_NAME AS ITASK_ASSESMENT_DISPLAY_NAME",
            "IT_SOLVER.USER_DISPLAY_NAME AS SOLVER_USER_FULL_NAME",
            "IT_SOLVER.USER_DISPLAY_NAME AS SOLVER_USER_DISPLAY_NAME",
            globalThis.database.raw(
                `(select COUNT("TTJSCALC_EXEC_RECALC") FROM "TEMPLATE_TASK_JS_CALCULATIONS" WHERE "TTASK_ID" = "TT"."TTASK_ID" and "TTJSCALC_EXEC_RECALC" LIKE 'Y') as "EXEC_RECALC"`,
            ),
            globalThis.database.raw(
                `(select MAX("JS_VERSION") FROM "JS_SCRIPTS" WHERE "JS_TYPE" = 'F') as "JS_VERSION"`,
            ),
            "IP.IPROC_NAME",
            "IP.IPROC_PRIORITY",
            "IP.IPROC_MAIN_IPROC_ID",
            "TT.TTASK_NAME",
            "TT.TTASK_DESCRIPTION",
            "TT.TTASK_INSTRUCTION",
            "TT.TTASK_IS_REJECTABLE",
            "TT.TTASK_IS_DELEGATABLE",
            "TT.TTASK_JS",
            "TT.TTASK_CAN_BE_BUTTON",
            "TP.TPROC_NAME",
            "TP.TPROC_VERSION",
            // CAN I SOLVE ?
            globalThis.database.raw(
                `
                CASE WHEN "IT"."ITASK_USER_ID" = ?
                AND "IP"."IPROC_STATUS" = 'A'
                AND "IT"."ITASK_TYPE" not in ('P','W')
                AND "IT"."ITASK_STATUS" <> 'L'
                THEN 1
                ELSE 0 END "C_SOLVE" `,
                userId,
            ),

            // CAN I TAKE ?
            globalThis.database.raw(
                `
                CASE
                when 1 = ${inspector} then 1
                when 1 = ${globalSupervisor} then 1
                when "IT"."ITASK_STATUS" = 'T' then 1
                WHEN (
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                            or (${assessmentManagerSql}) or
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerSql}))
                            or (${solverManagerSql})
                        )
                        OR
                        (${hasHrRightsSql})
                )
                AND "IP"."IPROC_STATUS" = 'A'
                AND "IT"."ITASK_TYPE" not in ('P','W')
                AND "IT"."ITASK_STATUS" <> 'L'
                AND "IT"."ITASK_TYPE" <> 'P'
                then 1 else 0 end "C_TAKE"`,
                [userId, userId],
            ),

            // CAN I HAND OVER ?
            globalThis.database.raw(
                `
                CASE
                when 1 = ${inspector} then 1
                when 1 = ${globalSupervisor} then 1
                WHEN (
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                            or (${assessmentManagerSql}) or
                        (
                            "IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerSql}))
                            or (${solverManagerSql})
                        )
                        OR
                        (${hasHrRightsSql})
                )
                AND "IP"."IPROC_STATUS" = 'A'
                AND "IT"."ITASK_TYPE" not in ('P','W')
                AND "IT"."ITASK_STATUS" <> 'L'
                AND "IT"."ITASK_TYPE" <> 'P'
                then 1 else 0 end "C_HANDOVER"`,
                [userId, userId],
            ),

            // CAN I HAND OVER WITHOUT LIMITATIONS?
            globalThis.database.raw(`
            CASE when
                (${hasHrRightsSql})
                or ${ROLE.SUPER_ADMINISTRATOR} IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
                or ${ROLE.ADMINISTRATOR} IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
                or ${ROLE.GLOBAL_SUPERVISOR} IN (select "ROLE_ID" from "SINGLE_USER_ROLES")
            then 1 else 0 end "C_HANDOVER_NO_LIMITS"
            `),

            // CAN I ADD ?
            globalThis.database.raw(
                `
                CASE
                when ("IT"."ITASK_USER_ID" = ? or ("IP"."TPROC_ID" is null and ("IP"."IPROC_INST_OWNER_USER_ID" = ? or
                (${procOwnerManagerSql})
                )))
                and "IP"."IPROC_STATUS" = 'A'
                then 1 else 0 end "C_ADD"`,
                [userId, userId],
            ),

            // IS DUE ?
            globalThis.database.raw(
                `
                CASE
                 WHEN (
                    "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                    or (${assessmentManagerSql}) or
                    ("IT"."ITASK_ASSESMENT_USER_ID" is null and (${procOwnerManagerSql}))
                    or (${solverManagerSql})
                )
                AND ("IT"."ITASK_DUE_OFFSET" = 'po' OR "IT"."ITASK_DURATION" = 'po')
                AND "IP"."IPROC_STATUS" = 'A'
                then 1 else 0 end "C_DUE"`,
                [userId, userId],
            ),
            // when active tasks are in done processes
            globalThis.database.raw(`
                CASE WHEN "IP"."IPROC_STATUS" = 'A'
                THEN "IT"."ITASK_STATUS"
                ELSE 'D' END "REAL_ITASK_STATUS"`),
        ]);
        if (Array.isArray(extraColumns) && extraColumns.length > 0) {
            columns = columns.concat(extraColumns);
        }

        const conn = globalThis.orm
            .repo("task")
            .connection.withRecursive(
                "CHILDREN_UNITS",
                this.connection.raw(orgStrRepo.withChildrenUnitsForManager(), {
                    MANAGER_USER_ID: userId,
                }),
            )
            .with(
                "MANAGED_USERS",
                this.connection.raw(orgStrRepo.withManagedUsers(), {
                    USER_ID: userId,
                    USER_STATUS_ACTIVE,
                }),
            )
            .with("SINGLE_USER_ROLES", (builder) => {
                builder
                    .select("SUR.ROLE_ID")
                    .from("USER_ROLES as SUR")
                    .where("SUR.USER_ID", userId);
            })
            .select(columns)
            .from("ARCH_INSTANCE_TASKS AS IT")
            .leftJoin(
                "ARCH_INSTANCE_PROCESSES AS IP",
                "IT.IPROC_ID",
                "IP.IPROC_ID",
            )
            .leftJoin("TEMPLATE_TASKS AS TT", "TT.TTASK_ID", "IT.TTASK_ID")
            .leftJoin(
                "ARCH_INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", "TP.TPROC_ID", "IP.TPROC_ID")
            .leftJoin("HEADERS as TH", "IP.HEADER_ID", "TH.HEADER_ID")
            .leftJoin(
                "USERS AS IP_OWNER",
                "IP.IPROC_INST_OWNER_USER_ID",
                "IP_OWNER.USER_ID",
            )
            .leftJoin(
                "USERS AS IT_SOLVER",
                "IT.ITASK_USER_ID",
                "IT_SOLVER.USER_ID",
            )
            .leftJoin(
                "USERS AS IT_FINISHED",
                "IT.ITASK_FINISHED_BY_USER_ID",
                "IT_FINISHED.USER_ID",
            )
            .leftJoin(
                "USERS AS IT_ASS",
                "IT.ITASK_ASSESMENT_USER_ID",
                "IT_ASS.USER_ID",
            );

        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("header")
                .applyExternalLoginRights(conn, userId);
        }

        return this.createCollection(conn);
    }

    getForProcess(userId: number, iprocId: number, getAll?: boolean) {
        return this.all(userId).then((coll) => {
            coll.knex.where(function () {
                const self = this;
                self.where("IP.IPROC_ID", iprocId);

                if (!getAll) {
                    self.where("IP.IPROC_STATUS", PROCESS.STATUS_ACTIVE).where(
                        function () {
                            this.whereIn("IT.ITASK_STATUS", [
                                TASK.STATUS_ACTIVE,
                                TASK.STATUS_WAITING,
                                TASK.STATUS_PULL,
                            ]).orWhere(function () {
                                this.where("IT.ITASK_STATUS", TASK.STATUS_NEW)
                                    .where(
                                        "IT.ITASK_AUTO_START",
                                        TASK.AUTOSTART_YES,
                                    )
                                    .where(
                                        "IP.IPROC_STATUS",
                                        PROCESS.STATUS_ACTIVE,
                                    );
                            });
                        },
                    );
                }
            });
            return coll;
        });
    }

    mine(userId: number, checkRoles = false) {
        return this.all(userId, null, checkRoles).then((coll) => {
            coll.knex = this.applyActiveTasks(coll.knex, userId);
            return coll;
        });
    }

    applyActiveTasks(conn: any, userId: number) {
        return conn.where(function () {
            // @ts-ignore
            this.where(function () {
                // @ts-ignore
                this.where("IP.IPROC_STATUS", "A")
                    .where("IT.ITASK_STATUS", "W")
                    .whereIn("IT.ITASK_ASSESMENT_METHOD", ["S", "U", "P"])
                    .whereNull("IT.ITASK_USER_ID")
                    .whereNot("IT.ITASK_TYPE", "P")
                    .where(function () {
                        // @ts-ignore
                        this.whereRaw(
                            `
                       "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)
                    `,
                            [userId, userId],
                        );
                    });
            })
                .orWhere(function () {
                    // @ts-ignore
                    this.where("IP.IPROC_STATUS", "A")
                        .where("IT.ITASK_USER_ID", userId)
                        .where("IT.ITASK_STATUS", "A")
                        .whereNotIn("ITASK_TYPE", ["P", "W"]);
                })
                .orWhere(function () {
                    // @ts-ignore
                    this.where("IP.IPROC_STATUS", "A")
                        .where("IT.ITASK_STATUS", "W")
                        .where(function () {
                            // @ts-ignore
                            this.where("IT.ITASK_DUE_OFFSET", "po").orWhere(
                                "IT.ITASK_DURATION",
                                "po",
                            );
                        })
                        .where(function () {
                            // @ts-ignore
                            this.where(function () {
                                // @ts-ignore
                                this.where(
                                    "IT.ITASK_USER_ID",
                                    userId,
                                ).whereNull("IP.TPROC_ID");
                            }).orWhere(function () {
                                // @ts-ignore
                                this.whereRaw(
                                    `
                           "IT"."ITASK_ASSESMENT_USER_ID" = ? or ("IT"."ITASK_ASSESMENT_USER_ID" is null and "IP"."IPROC_INST_OWNER_USER_ID" = ?)`,
                                    [userId, userId],
                                );
                            });
                        });
                });
        });
    }

    // @ts-ignore
    getByName(iprocId: number, taskName: string): BaseCollection<Task> {
        const collection = this.getCollection();
        collection.knex
            .from(this.tableName)
            .where("IPROC_ID", iprocId)
            .where("ITASK_NAME", taskName);
        return collection;
    }

    getById(id: number): BaseCollection<Task> {
        const collection = this.getCollection();

        if (Array.isArray(id)) {
            collection.knex.from(this.tableName).whereIn("ITASK_ID", id);
        } else {
            collection.knex.from(this.tableName).where("ITASK_ID", id);
        }

        return collection;
    }

    getWaitingTasks(iprocId: number) {
        // Add TTASK_NAME mutations
        const extraColumns = ["TT.TTASK_NAME"];
        globalThis.dynamicConfig.langs.forEach((language: string) => {
            extraColumns.push(`TT.TTASK_NAME_${language.toUpperCase()}`);
        });

        if (!Array.isArray(iprocId)) {
            // @ts-ignore
            iprocId = [iprocId];
        }

        const columns = ["IT.*"].concat(extraColumns);

        const conn = this.connection
            .select(columns)
            .from(`${this.tableName} as IT`)
            .leftJoin("TEMPLATE_TASKS as TT", "TT.TTASK_ID", "IT.TTASK_ID")
            // @ts-ignore
            .whereIn(
                "IT.IPROC_ID",
                Array.isArray(iprocId) ? iprocId : [iprocId],
            )
            .where("IT.ITASK_STATUS", TASK.STATUS_DELAYED)
            .where("IT.ITASK_TYPE", TASK.TYPE_EVENT_WAIT)
            .whereNotNull("IT.ITASK_EVENT");

        return globalThis.orm.collection<"instanceTask">("instanceTask", conn);
    }

    getByProcess(
        iprocId: number,
        columns?: string[],
        alias?: string,
    ): BaseCollection<Task> {
        const coll = this.getCollection(columns, alias);
        coll.knex.where("IPROC_ID", iprocId);
        return coll;
    }

    getTaskSections(iTaskId: number) {
        return this.connection
            .select(["TS.*", "TTVU.TVAR_ID", "TTVU.TSEC_X", "TTVU.TSEC_Y"])
            .from("TEMPLATE_TASK_VAR_USAGE as TTVU")
            .leftJoin("TEMPLATE_SECTIONS as TS", "TS.TSEC_ID", "TTVU.TSEC_ID")
            .whereNotNull("TTVU.TSEC_ID")
            .where(
                "TTVU.TTASK_ID",
                "=",
                this.connection
                    .select("TTASK_ID")
                    .from("ARCH_INSTANCE_TASKS")
                    .where("ITASK_ID", iTaskId),
            );
    }
}
