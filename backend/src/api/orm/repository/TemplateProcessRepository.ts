// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import * as ROLES from "../entity/const/roleConst";
import { TemplateProcess } from "../entity/TemplateProcess";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

import { TemplateProcessExport } from "../../utils/TemplateProcessExport";
import { TemplateProcessImport } from "../../utils/TemplateProcessImport";

export class TemplateProcessRepository extends BaseRepository<TemplateProcess> {
    meta() {
        return {
            tableName: "TEMPLATE_PROCESSES",
            entityName: "TemplateProcess",
            entity: () => new TemplateProcess(),
        };
    }

    get(tprocId: number | (string | number)[] | string, columns?: any) {
        if (!Array.isArray(tprocId)) {
            tprocId = [tprocId, 1];
        }
        return super.get(tprocId, columns);
    }

    async getTasksMutationInformation(tProcId) {
        const columns = globalThis.orm
            .repo("TemplateTask")
            .getEntity()
            .getTranslatedProperties([], null, true);
        const mutationData = await this.connection()
            .select(columns)
            .from("TEMPLATE_TASKS")
            .where("TPROC_ID", tProcId);

        return mutationData;
    }

    async enumVersions(tprocId: number): Promise<string[]> {
        const allTprocs = await this.connection
            .select("TPROC_VERSION")
            .from(this.tableName)
            .where("TPROC_ID", tprocId);
        return _.map(allTprocs, "TPROC_VERSION");
    }

    async createNewVersion(tprocId: number, srcVersion: number): Promise<void> {
        const lock = await globalThis.container.client.cache.lock.getLock(
            `VERSIONING:PROCESS_VERSION-${tprocId}`,
            1000 * 60 * 2,
        );

        try {
            // Get current version.
            const nextVersion = (await this.getLastVersion(tprocId)) + 1;

            // Clone version + 1.
            const tproc = await this.get([tprocId, srcVersion]);
            tproc.forceToInsert();
            tproc.TPROC_VERSION = nextVersion; // Increase version.
            await this.store(tproc); // Store new version clone.

            // Copy mapping.
            await globalThis.orm
                .repo("templateTaskVarUsage", this.connection)
                .copyVersion(tprocId, srcVersion, nextVersion);

            // Copy calcs.
            await globalThis.orm
                .repo("templateTaskJSCalculation", this.connection)
                .copyVersion(tprocId, srcVersion, nextVersion);

            // Copy completition.
            await globalThis.orm
                .repo("templateTaskCompletion", this.connection)
                .copyVersion(tprocId, srcVersion, nextVersion);

            // Copy links and conditions.
            const linkMap = await globalThis.orm
                .repo("templateTaskLink", this.connection)
                .copyVersion(tprocId, srcVersion, nextVersion);

            // Copy graph.
            await globalThis.orm
                .repo("templateGraph", this.connection)
                .copyVersion(tprocId, srcVersion, nextVersion, linkMap);

            return tproc;
        } finally {
            await lock.release();
        }
    }

    /**
     *
     * @param tprocId
     * @returns {Promise<number|*>}
     */
    async getLastVersion(tprocId) {
        const rows = await this.connection
            .max("TPROC_VERSION as MAX_TPROC_VERSION")
            .from(this.tableName)
            .where("TPROC_ID", tprocId);
        if (Array.isArray(rows) && rows.length > 0) {
            return rows[0].MAX_TPROC_VERSION;
        }
        throw new UserException("Template process not found.", "NOT_FOUND");
    }

    /**
     * Allows to use template process version.
     * @param tprocId
     * @param version
     * @returns {Promise<void>}
     */
    async setCurrentVersion(tprocId, version) {
        return globalThis.orm
            .repo("TemplateProcessVersion", this.connection)
            .setVersion(tprocId, version);
    }

    /**
     * Extended validation rules.
     * @param entity
     * @return {Promise<any[]> | Promise.<*[]>}
     */
    validate(entity) {
        return Promise.all([
            super.validate(entity),
            this.validateUniqueName(entity),
        ]);
    }

    validateUniqueName(entity) {
        if (
            entity.TPROC_NAME === null ||
            typeof entity.TPROC_NAME === "undefined"
        ) {
            return;
        }

        return this.connection
            .select(entity.primaryKey)
            .from(this.tableName)
            .where("TPROC_NAME", entity.TPROC_NAME)
            .whereNot("TPROC_STATUS", "E")
            .whereNot("TPROC_ID", entity.id[0])
            .then((templates) => {
                if (Array.isArray(templates) && templates.length > 0) {
                    throw new UserException(
                        "Template process already exists!",
                        "UNIQUE_CONSTRAINT",
                    );
                }
            });
    }

    copy(tprocId, newTprocName, prefix) {
        const tpi = new TemplateProcessImport(this.connection);
        const tpe = new TemplateProcessExport(this.connection);

        return tpe.exportProcesses(tprocId).then((data) => {
            // Use compatible !!
            if (data.users) {
                data.users.forEach((user) => {
                    user.import_rules = {
                        action: "USE_COMPATIBLE",
                        data: {
                            user_id: user.user_id,
                        },
                    };
                });
            }

            if (data.orgstrs) {
                data.orgstrs.forEach((orgstr) => {
                    orgstr.import_rules = {
                        action: "USE_COMPATIBLE",
                        data: {
                            orgstr_id: orgstr.orgstr_id,
                        },
                    };
                });
            }

            if (data.roles) {
                data.roles.forEach((role) => {
                    role.import_rules = {
                        action: "USE_COMPATIBLE",
                        data: {
                            role_id: role.role_id,
                        },
                    };
                });
            }

            if (data.dynamic_tables) {
                data.dynamic_tables.forEach((dt) => {
                    dt.import_rules = {
                        action: "USE_COMPATIBLE",
                        data: {
                            dt_id: dt.dt_id,
                        },
                    };
                });
            }

            if (data.events) {
                data.dynamic_tables.forEach((event) => {
                    event.import_rules = {
                        action: "USE_COMPATIBLE",
                        data: {
                            evedef_id: event.evedef_id,
                        },
                    };
                });
            }

            if (data.template_processes) {
                data.template_processes.forEach((tproc) => {
                    let tprocName = null;

                    // Template
                    if (tproc.tproc_id === tprocId) {
                        tprocName = newTprocName;
                    } else {
                        tprocName = prefix + tproc.tproc_name;
                    }
                    tproc.import_rules = {
                        action: "CREATE",
                        data: {
                            tproc_name: tprocName,
                        },
                    };
                });
            }

            return tpi.importProcess(data);
        });
    }

    superStore(entity) {
        // Can not call super.store in async method
        return super.store(entity);
    }

    async store(entity, createDefaultHeader = true) {
        const newEntity = !entity.TPROC_ID || !entity.TPROC_VERSION; // At least one primary is missing.

        if (newEntity) {
            await this.autoGeneratePrimary(entity);
            entity.forceToInsert();
        }

        const id = await this.superStore(entity);
        if (newEntity && createDefaultHeader) {
            const headerRepo = globalThis.orm.repo("header", this.connection);
            const headerEntity = headerRepo.getEntity({
                TPROC_ID: entity.TPROC_ID,
                HEADER_ENABLED: "Y",
                HEADER_NAME: entity.TPROC_NAME,
            });
            await headerRepo.store(headerEntity);
        }

        return id;
    }

    getTemplates(userId) {
        const columns = [
            "TP.*",
            globalThis.database.raw(
                `"TR"."ROLE_NAME" as "TPROC_VIS_ROLE_NAME"`,
            ),
            globalThis.database.raw(`"HR"."ROLE_NAME" as "TPROC_HR_ROLE_NAME"`),
            this.connection.raw(
                `"U1"."USER_DISPLAY_NAME" AS "TPROC_OWNER_USER"`,
            ),
            this.connection.raw(
                `"U2"."USER_DISPLAY_NAME" AS "TPROC_LAST_CHANGED_BY_USER"`,
            ),
        ];

        return this.connection
            .select(columns)
            .from("TEMPLATE_PROCESSES as TP")
            .leftJoin("USERS as U1", "TP.TPROC_OWNER_USER_ID", "U1.USER_ID")
            .leftJoin(
                "USERS as U2",
                "TP.TPROC_LAST_CHANGED_BY_USER_ID",
                "U2.USER_ID",
            )
            .leftJoin("ROLES as TR", "TP.TPROC_VIS_ROLE_ID", "TR.ROLE_ID")
            .leftJoin("ROLES as HR", "TP.TPROC_HR_ROLE_ID", "HR.ROLE_ID")
            .where(function () {
                this.where("TP.TPROC_OWNER_USER_ID", userId);
                this.orWhereExists(function () {
                    this.select()
                        .from("USER_ROLES")
                        .where("USER_ID", userId)
                        .where(function () {
                            return this.where(function () {
                                this.where("ROLE_ID", -1).whereRaw(
                                    `not exists (select "USER_ID" from "USER_PARAMETERS" where "USER_ID" = ? and "USRPAR_NAME" = 'DISABLE_ADMIN' and "USRPAR_VALUE" = 'Y')`,
                                    [userId],
                                );
                            }).orWhere(function () {
                                this.where("ROLE_ID", -3).whereRaw(
                                    `not exists (select "USER_ID" from "USER_PARAMETERS" where "USER_ID" = ? and "USRPAR_NAME" = 'DISABLE_ADMIN' and "USRPAR_VALUE" = 'Y')`,
                                    [userId],
                                );
                            });
                        });
                });
            })
            .as("TP");
    }

    getById(id) {
        const collection = this.getCollection();

        if (Array.isArray(id)) {
            collection.knex
                .from(this.tableName)
                .whereIn("TPROC_ID", Array.isArray(id) ? id : [id]);
        } else {
            collection.knex.from(this.tableName).where("TPROC_ID", id);
        }

        return collection;
    }

    async getTemplatesToStart(userId, superStatus) {
        const columns = [
            "TP.*",
            "TH.HEADER_ID",
            "TH.HEADER_NAME",
            "TH.HEADER_ENABLED",
            "TH.HEADER_CODE",
            globalThis.database.raw(
                `"TR"."ROLE_NAME" as "TPROC_VIS_ROLE_NAME"`,
            ),
            this.connection.raw(
                `"U1"."USER_DISPLAY_NAME" AS "TPROC_OWNER_USER"`,
            ),
            this.connection.raw(
                `"U2"."USER_DISPLAY_NAME" AS "TPROC_LAST_CHANGED_BY_USER"`,
            ),
        ];

        // Add header translations
        const headerRepo = globalThis.orm.repo("header", this.connection);
        const entity = headerRepo.getEntity();
        const attrs = entity.attributes();
        const attrNames = Object.keys(attrs);
        attrNames.forEach((attrName) => {
            if (attrs[attrName].translated) {
                columns.push(`TH.${attrName}`);
            }
        });

        const conn = this.connection
            .select(columns)
            .from("TEMPLATE_PROCESSES as TP")
            .leftJoin("USERS as U1", "TP.TPROC_OWNER_USER_ID", "U1.USER_ID")
            .leftJoin(
                "USERS as U2",
                "TP.TPROC_LAST_CHANGED_BY_USER_ID",
                "U2.USER_ID",
            )
            .leftJoin("ROLES as TR", "TP.TPROC_VIS_ROLE_ID", "TR.ROLE_ID")
            .rightJoin("HEADERS as TH", "TP.TPROC_ID", "TH.TPROC_ID")
            .where("TP.TPROC_STATUS", "A")
            .where("TH.HEADER_ENABLED", "Y")
            .where(function () {
                // User is Admin or PowerUser
                if (!superStatus) {
                    // User has role
                    this.whereExists(function () {
                        this.select("THR.HEADER_ROLE_ID")
                            .from("HEADER_ROLES as THR")
                            .whereIn("THR.ROLE_ID", function () {
                                this.select("ROLE_ID")
                                    .from("USER_ROLES")
                                    .where("USER_ID", userId)
                                    .whereNotIn("ROLE_ID", [-1, -2]);
                            })
                            .where(
                                "THR.HEADER_ID",
                                globalThis.database.raw(`"TH"."HEADER_ID"`),
                            );
                    })
                        .orWhereExists(function () {
                            // User is in organization structure
                            this.select("THO.HEADER_ORGSTR_ID")
                                .from("HEADER_ORGSTR as THO")
                                .whereIn("THO.ORGSTR_ID", function () {
                                    this.select("ORGSTR_ID")
                                        .from("USER_ORGANIZATION_STRUCTURE")
                                        .where("USER_ID", userId);
                                })
                                .where(
                                    "THO.HEADER_ID",
                                    globalThis.database.raw(`"TH"."HEADER_ID"`),
                                );
                        })
                        .orWhereExists(function () {
                            // User is manager of organization
                            this.select("THO.HEADER_ORGSTR_ID")
                                .from("HEADER_ORGSTR as THO")
                                .whereIn("THO.ORGSTR_ID", function () {
                                    this.select("ORGSTR_ID")
                                        .from("ORGANIZATION_STRUCTURE")
                                        .where("MANAGER_USER_ID", userId);
                                })
                                .where(
                                    "THO.HEADER_ID",
                                    globalThis.database.raw(`"TH"."HEADER_ID"`),
                                );
                        });
                }
            });
        if (globalThis.dynamicConfig.usingExternalLoginRights) {
            await globalThis.orm
                .repo("Header")
                .applyExternalLoginRights(conn, userId);
        }
        return globalThis.orm.collection("TemplateProcess", conn);
    }

    /**
     * Check if user can instantiate template otherwise exception is thrown.
     * @param {number} userId
     * @param {number} tprocId
     * @param {Boolean} useCache - Determine if cached User data is used (sometimes you don't want that, ie. DISABLED_ADMINISTRATOR for WorkFlow purposes)
     * @returns {*|Promise<U>|Promise.<T>}
     */
    async canInstantiateProcess(userId, tprocId, useCache = true) {
        let self = this;

        // @t3b-1251 Admin pravidla a problém s jejich odebíráním
        const user = useCache
            ? await globalThis.container.service.temporary.cacheModule.getCachedUser(
                  userId,
              )
            : await globalThis.container.service.auth.getUserInfoFromId(
                  userId,
                  false,
              );
        const superStatus =
            user.hasRole(ROLES.ADMINISTRATOR) || user.hasRole(ROLES.POWER_USER);

        const coll = await this.getTemplatesToStart(userId, superStatus);
        return await coll.knex
            .where("TP.TPROC_ID", tprocId)
            .then((data) => {
                if (!data || !Array.isArray(data) || data.length === 0) {
                    throw new UserException(
                        "Can not instantiate process from template. Not found among active templates.",
                        {
                            userId,
                            tprocId,
                        },
                    );
                }

                return self
                    .castRows(data, self.entity.getAttributes(false, true))
                    .then((casted) => {
                        self = null; // Enable GC feature.
                        return casted[0];
                    });
            })
            .catch((err) => {
                throw err;
            });
    }

    /**
     * Check if user can instantiate template otherwise exception is thrown.
     * Optimised method for instantiating rights.
     * @param {number} userId
     * @param {number} tprocId
     * @param headerId
     * @returns {*|Promise<U>|Promise.<T>}
     */
    async canInstantiateHeader(userId, _tprocId, headerId) {
        const user =
            await globalThis.container.service.temporary.cacheModule.getCachedUser(
                userId,
            );

        /**
         * Optimised method for access rights control.
         * Detect admin/poweruser, then roles from users cache and indexed db table.
         * Orgstr check and manager check. header/tproc_status TOTO.
         */

        // Is user with high privileges
        const superStatus = user.isAdministrator() || user.isPowerUser();
        if (superStatus) {
            return true;
        }

        // Has header role.
        const roles = await this.connection
            .select("ROLE_ID")
            .from("HEADER_ROLES")
            .where("HEADER_ID", headerId);
        const diff = _.difference(user.ROLES, _.map(roles, "ROLE_ID")); // Has some role ?
        if (user.ROLES.length !== diff.length) {
            // User has at least one role from header_roles.
            return true;
        }

        // Is in orgstr
        const orgs = await this.connection
            .select("ORGSTR_ID")
            .from("HEADER_ORGSTR")
            .where("HEADER_ID", headerId)
            .where("ORGSTR_ID", user.ORGANIZATION.ORGSTR_ID);
        if (orgs.length > 0) {
            return true;
        }

        // Is manager of header orgstr.
        const manager = this.connection
            .select("THO.HEADER_ORGSTR_ID")
            .from("HEADER_ORGSTR as THO")
            .whereIn("THO.ORGSTR_ID", (builder) => {
                builder
                    .select("ORGSTR_ID")
                    .from("ORGANIZATION_STRUCTURE")
                    .where("MANAGER_USER_ID", userId);
            })
            .where(
                "THO.HEADER_ID",
                globalThis.database.raw(`"TH"."HEADER_ID"`),
            );
        if (manager.length > 0) {
            return true;
        }

        // Has no rights.
        return false;
    }

    getTTaskList(tprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateProcessRepository.getTTaskList)",
            );
        }

        const versionCondLinks =
            version > 0 ? `and "TTL"."TTASKLINK_VERSION" = ?` : "";
        const versionCondCalc =
            version > 0 ? `"TTC"."TTJSCALC_VERSION" = ?` : "";
        const binding = version > 0 ? [version] : [];

        return this.connection
            .select([
                "A.*",
                globalThis.database.raw(
                    `(select count(*) from "TEMPLATE_TASK_LINKS" "TTL" where "TTL"."TTASKLINK_TO_TTASK_ID" = "A"."TTASK_ID" and "TTL"."ORG_ID" = "A"."ORG_ID" ${versionCondLinks}) as "TTASK_INC_CNT"`,
                    binding,
                ),
                "B.ROLE_NAME as TTASK_ASSESMENT_ROLE",
                "C.USER_NAME as TTASK_ASSESMENT_USER,",
                "D.ORGSTR_NAME as TTASK_ASSESMENT_ORG,",
                globalThis.database.raw(
                    `(select count("TTASK_ID") from "TEMPLATE_TASK_JS_CALCULATIONS" "TTC" where "TTC"."TTASK_ID" = "A"."TTASK_ID" and ${versionCondCalc}) as "TTASK_CALC_COUNT"`,
                    binding,
                ),
            ])
            .from("TEMPLATE_TASKS as A")
            .leftJoin("ROLES as B", "A.TTASK_ASSESMENT_ROLE_ID", "B.ROLE_ID")
            .leftJoin("USERS as C", "A.TTASK_ASSESMENT_USER_ID", "C.USER_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as D",
                "A.TTASK_ASSESMENT_ORGSTR_CNST",
                "D.ORGSTR_ID",
            )
            .where("A.TTASK_IS_DELETED", "N")
            .where("A.TPROC_ID", tprocId);
    }

    getTTaskVariableList(tprocId) {
        return this.connection
            .select(["VAR.*", "US.*"])
            .from(
                globalThis.database.raw(
                    `"TEMPLATE_TASK_VAR_USAGE" "US", "TEMPLATE_VARIABLES" "VAR"`,
                ),
            )
            .whereRaw("VAR.TVAR_ID = US.TVAR_ID")
            .whereRaw("VAR.ORG_ID = US.ORG_ID")
            .whereRaw("VAR.TPROC_ID = US.TPROC_ID")
            .where("US.TPROC_ID", tprocId);
    }

    getTTaskAssTasks(tprocId) {
        return this.connection
            .select([
                "T.ORG_ID",
                "T.TPROC_ID",
                "T.TTASK_ID",
                "AT.TTASK_ID as ASSESMENT_TTASK_ID",
                "AT.TTASK_NAME as ASSESMENT_TTASK_NAME",
            ])
            .from(
                globalThis.database.raw(
                    `"TEMPLATE_TASKS" "T", "TEMPLATE_TASKS" "AT"`,
                ),
            )
            .whereRaw("T.TTASK_ASSESMENT_TTASK_ID = AT.TTASK_ID")
            .whereNotNull("T.TTASK_ASSESMENT_TTASK_ID")
            .where("T.TTASK_ASSESMENT_METHOD", "L")
            .where("T.TPROC_ID", tprocId);
    }

    getTTask(ttaskId) {
        return this.connection
            .select([
                "TT.ORG_ID",
                "TT.TTASK_VAR_GLOBAL_ORDER",
                "TT.TTASK_ID",
                "TT.TPROC_ID",
                "TT.TTASK_NAME",
                "TT.TTASK_IS_PROC_ID",
                "TT.TTASK_DESCRIPTION",
                "TT.TTASK_ASSESMENT_ROLE_ID",
                "TT.TTASK_ASSESMENT_HIERARCHY",
                "TT.TTASK_ASSESMENT_METHOD",
                "TT.TTASK_ASSESMENT_USER_ID",
                "TT.TTASK_AGAIN",
                "TT.TTASK_PETRI_NET_INPUT",
                "TT.TTASK_IS_DELETED",
                "TT.TTASK_DUE_OFFSET",
                "TT.TTASK_DURATION",
                "TT.TTASK_SUFFICIENT_END",
                "TT.TTASK_TYPE",
                "TT.TTASK_ASSESMENT_ORGSTR_ID",
                "TT.TTASK_ASSESMENT_TTASK_ID",
                "TT.TTASK_ASSESMENT_ORGSTR_CNST",
                "TT.TTASK_EVENT",
                "TT.TTASK_EVENT_WAIT",
                "TT.TTASK_SUBPROCESS_TPROC_ID",
                "TT.TTASK_ASSESMENT_TVAR_ID",
                "TT.TTASK_DUTY",
                "TT.TTASK_GEN_HISTORY",
                "TT.TTASK_INVOKE_EVENT",
                "TT.TTASK_ITERATE_OVER",
                "TT.TTASK_REFERENCE_USER",
                "TTEN.TTASK_ENOT_TGT_TYPE",
                "TTEN.TTASK_ENOT_TGT_TTASK_ID",
                "TTEN.TTASK_ENOT_TGT",
                "TTEN.TTASK_ENOT_TGT_ORGSTR_ID",
                "ORG.ORGSTR_NAME as TTASK_ENOT_TGT_ORGSTR",
                "TTEN.TTASK_ENOT_TGT_ROLE_ID",
                "R.ROLE_NAME as TTASK_ENOT_TGT_ROLE",
                "TTEN.TTASK_ENOT_SUBJECT",
                "TTEN.TTASK_ENOT_BODY2",
                "TT.TTASK_INSTRUCTION",
                "TT.TTASK_RUN_ONLY_ONCE",
                "TT.TTASK_DISC_FLAG",
                "TT.TTASK_MULTIINSTANCE_FLAG",
                "TTIN.TTASK_INV_ATTENDEES",
                "TTIN.TTASK_INV_SUMMARY",
                "TTIN.TTASK_INV_DESCRIPTION",
                "TTIN.TTASK_INV_DTSTART",
                "TTIN.TTASK_INV_DTEND",
                "TTIN.TTASK_INV_LOCATION",
                "TTIN.TTASK_INV_CLASS",
                "TTIN.TTASK_INV_PRIORITY",
                "TTIN.TTASK_INV_CATEGORIES",
            ])
            .from("TEMPLATE_TASKS as TT")
            .leftJoin(
                "TEMPLATE_TASK_EMAIL_NOTIFS as TTEN",
                "TT.TTASK_ID",
                "TTEN.TTASK_ID",
            )
            .leftJoin(
                "TEMPLATE_TASK_INVITATIONS as TTIN",
                "TT.TTASK_ID",
                "TTIN.TTASK_ID",
            )
            .leftJoin("ROLES as R", "R.ROLE_ID", "TTEN.TTASK_ENOT_TGT_ROLE_ID")
            .leftJoin(
                "ORGANIZATION_STRUCTURE as ORG",
                "ORG.ORGSTR_ID",
                "TTEN.TTASK_ENOT_TGT_ORGSTR_ID",
            )
            .where("TT.TTASK_ID", ttaskId);
    }

    getTTaskLinkList(tprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateProcessRepository.getTTaskLinkList)",
            );
        }

        return this.connection
            .select([
                "TTL.*",
                "TGR.TGRAPH_LINKSRC_PORT",
                "TGR.TGRAPH_LINKTGT_PORT",
                "TTF.TTASK_NAME AS FROM_NAME",
                "TTT.TTASK_NAME AS TO_NAME",
                globalThis.database.raw(
                    `(SELECT COUNT(*) FROM "TEMPLATE_LINK_CONDITIONS" "TLC" WHERE "TLC"."TTASKLINK_ID" = "TTL"."TTASKLINK_ID") AS "CONDITION_COUNT"`,
                ),
            ])
            .from("TEMPLATE_TASK_LINKS as TTL")
            .leftJoin(
                "TEMPLATE_TASKS as TTF",
                "TTL.TTASKLINK_FROM_TTASK_ID",
                "TTF.TTASK_ID",
            )
            .leftJoin(
                "TEMPLATE_TASKS as TTT",
                "TTL.TTASKLINK_TO_TTASK_ID",
                "TTT.TTASK_ID",
            )
            .joinRaw(
                "LEFT JOIN TEMPLATE_GRAPH TGR ON TTL.TTASKLINK_ID = TGR.TGRAPH_TTASKCON_ID AND TGR.TGRAPH_OBJECT_TYPE = 'L'",
            )
            .where("TTL.TPROC_ID", tprocId)
            .where("TTL.TTASKLINK_VERSION", version);
    }

    getTProcConditionList(tprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateProcessRepository.getTTaskLinkList)",
            );
        }
        return this.connection
            .select("TLC.*")
            .from("TEMPLATE_LINK_CONDITIONS as TLC")
            .leftJoin(
                "TEMPLATE_TASK_LINKS as TTL",
                "TLC.TTASKLINK_ID",
                "TTL.TTASKLINK_ID",
            )
            .where("TTL.TPROC_ID", tprocId)
            .where("TTL.TTASKLINK_VERSION", version);
    }

    delete(id, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateProcessRepository.delete)",
            );
        }

        const entity = this.getEntity();
        entity.id = [id, version];
        entity.TPROC_STATUS = "E";

        return this.store(entity);
    }

    // t3b-1850 Nepropisuje se, kdo poslední upravil šablonu při všech změnách např. grafu, úkolů apod.
    setTemplateChange(id, version, userId) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code!",
            );
        }

        const entity = this.getEntity();
        entity.id = [id, version];
        entity.TPROC_LAST_CHANGED_BY_USER_ID = userId;
        entity.TPROC_LAST_CHANGED_DATE = new Date();

        return this.store(entity);
    }
}
