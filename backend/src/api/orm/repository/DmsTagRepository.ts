// @ts-nocheck
// @ts-nocheck
import { BaseRepository } from "./BaseRepository";
import { DmsTag } from "../entity/DmsTag";

export class DmsTagRepository extends BaseRepository<DmsTag> {
    meta() {
        return {
            tableName: "DMS_TAG",
            entityName: "DmsTag",
            entity: () => new DmsTag(),
        };
    }

    /**
     * Return default logical tag id.
     */
    async getDefaultLogicalTagId() {
        const result = await this.connection
            .select("DEFAULT_VALUE")
            .from("DMS_TAG")
            .where({ DMST_ID: DmsTag.consts.LOGICAL });

        return result[0].DEFAULT_VALUE;
    }

    getById(id) {
        const repo = this.connection
            .select()
            .from(this.tableName)
            .where("DMST_ID", id);
        return globalThis.orm.collection("DmsTag", repo);
    }

    getByLogical(logicalFileType) {
        const columns = [
            "T.DMST_ID",
            "T.NAME",
            "T.DESCRIPTION",
            "T.TYPE",
            "T.DEFAULT_VALUE",
            "T.LIST_OF_VALUES",
            "DTT.DMSTT_MODE",
        ];

        if (Array.isArray(globalThis.dynamicConfig.langs)) {
            globalThis.dynamicConfig.langs.forEach((lang) => {
                columns.push(`T.NAME_${lang.toUpperCase()}`);
                columns.push(`T.LIST_OF_VALUES_${lang.toUpperCase()}`);
            });
        }

        const repo = this.connection
            .select(columns)
            .from(`${this.tableName} as T`)
            .joinRaw(
                " join DMS_TAG_TAG DTT ON DTT.DMST_ID_TGT = T.DMST_ID AND DTT.DMSTT_SRC_VALUE = ? AND DTT.DMST_ID_SRC = -1",
                logicalFileType,
            );

        return globalThis.orm.collection("DmsTag", repo);
    }

    getAll() {
        return this.getColumns();
    }

    /**
     * Returns DMST_IDs of all tags. Suitable for preparing filter method.
     *
     * @returns {Promise}
     */
    getAllId() {
        return this.getCollection(["DMST_ID"]);
    }

    getColumns(columns) {
        const repo = this.connection.select(columns).from(this.tableName);
        return globalThis.orm.collection("DmsTag", repo);
    }

    async getForUser(userId) {
        const userParamRepo = globalThis.orm.repo(
            "UserParameter",
            this.connection,
        );
        let userColumns = await userParamRepo
            .getUserParameter(userId, "DMS_COLUMNS")
            .where("USER_ID", userId)
            .whereNotNull("USRPAR_VALUE")
            .first();

        if (!userColumns || !JSON.parse(userColumns.USRPAR_VALUE).length) {
            // User has no defined DMS_TAGS, use ORG defaults
            const defaultColumns = await globalThis.orm
                .repo("Organization", this.connection)
                .getDmsTags();

            if (defaultColumns) {
                userColumns = {
                    USRPAR_VALUE: defaultColumns.ORG_DMS_COLUMNS,
                };
            } else {
                return [];
            }
        }

        const dmsTags = (JSON.parse(userColumns.USRPAR_VALUE) || []).map(
            (tag) => Number(tag),
        );
        return await this.connection
            .select(["DMST_ID", "TYPE"])
            .from(this.tableName)
            .whereIn("DMST_ID", dmsTags);
    }

    /**
     * Adds given tags to collection so they can be filtered.
     *
     * @param tagIds {Array.<DMST_ID>|Array.<{DMST_ID: {Number}}>}
     * @returns {Array}
     */
    addFilteringTags(tagIds) {
        const filteringColumns = {
            "DTF-4": {
                type: "string",
                key: "DT.DMST_VALUE",
            },
        };

        if (tagIds && Array.isArray(tagIds) && tagIds.length > 0) {
            tagIds.forEach((tag) => {
                const key = `DTF${tag.DMST_ID}`;
                filteringColumns[key] = {
                    type: "string",
                    key: `"${key}".DMST_VALUE`,
                };
            });
        }
        return filteringColumns;
    }
}
