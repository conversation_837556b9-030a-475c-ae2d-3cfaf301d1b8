import { BaseRepository } from "./BaseRepository";
import * as ruleConst from "../entity/const/ruleConst";
import { Rule } from "../entity/Rule";
import { UserException } from "../../../utils/errorHandling/exceptions/userException";
import { BaseCollection } from "../BaseCollection";
import { Event } from "../entity/Event";
import { EventDefinition } from "../entity/EventDefinition";

export class RuleRepository extends BaseRepository<Rule> {
    meta() {
        return {
            tableName: "RULE",
            entityName: "Rule",
            entity: () => new Rule(),
        };
    }

    getReturnTEventList(evedefId: number): BaseCollection<EventDefinition> {
        const conn = this.connection
            .select([
                "EDEF.EVEDEF_ID",
                "EDEF.EVEDEF_NAME",
                "EDEF.EVEDEF_DESCRIPTION",
            ])
            .from("EVENT_DEFINITION as EDEF")
            .join("RULE_DEFINITION as RDEF", "RDEF.EVEDEF_ID", "EDEF.EVEDEF_ID")
            .where("RDEF.RDEF_TYPE", "PROCESS")
            .whereNot("EDEF.EVEDEF_ID", evedefId);

        return globalThis.orm.collection("eventDefinition", conn);
    }

    async getTRuleVariableList(ruleId: number) {
        const rules = await this.connection
            .select()
            .from("RULE_DEFINITION_VARIABLE")
            .where("RDEF_ID", ruleId);

        if (Array.isArray(rules) && rules.length > 0) {
            for (const rule of rules) {
                if (
                    rule.RDEFVAR_NAME === "RDEF_PROC_OWNER" &&
                    rule.RDEFVAR_VALUE
                ) {
                    const usrOrg = rule.RDEFVAR_VALUE.split(".");
                    rule.RDEFVAR_VALUE = {
                        value: rule.RDEFVAR_VALUE,
                    };

                    const usrRepo = globalThis.orm.repo(
                        "user",
                        this.connection,
                    );
                    const orgRepo = globalThis.orm.repo(
                        "organizationStructure",
                        this.connection,
                    );

                    try {
                        const user = await usrRepo.get(usrOrg[0]);
                        if (user) {
                            rule.RDEFVAR_VALUE.user_name =
                                user.USER_DISPLAY_NAME;
                        }
                    } catch (error: any) {
                        globalThis.tasLogger.error(
                            "Error fetching user:",
                            error,
                        );
                    }
                    if (usrOrg.length > 1) {
                        try {
                            const organization = await orgRepo.get(usrOrg[1]);
                            if (organization) {
                                rule.RDEFVAR_VALUE.orgstr_name =
                                    organization.ORGSTR_NAME;
                            }
                        } catch (error) {
                            globalThis.tasLogger.error(
                                "Cannot find orgstr for user in event.",
                                {
                                    error,
                                    ruleId,
                                },
                            );
                            rule.RDEFVAR_VALUE.orgstr_name = "Root";
                        }
                    }
                }
            }
        }
        return rules;
    }

    getTRuleParamList(ruleId: number) {
        return this.connection
            .select(
                "RD.*",
                this.connection.raw(`"TV"."TVAR_NAME" as "TVAR_NAME"`),
                this.connection.raw(`"TVDEST"."TVAR_NAME" as "DEST_TVAR_NAME"`),
            )
            .from("RULE_DEFINITION_PARAM as RD")
            .leftJoin(
                "TEMPLATE_VARIABLES AS TVDEST",
                "TVDEST.TVAR_ID",
                "RD.TVAR_ID_DEST",
            )
            .leftJoin("TEMPLATE_VARIABLES AS TV", "TV.TVAR_ID", "RD.TVAR_ID")
            .where("RDEF_ID", ruleId);
    }

    async deleteTRule(rdefId: number): Promise<boolean> {
        try {
            await this.connection
                .from("RULE_DEFINITION_PARAM")
                .where("RDEF_ID", rdefId)
                .delete();

            await this.connection
                .from("RULE_DEFINITION_VARIABLE")
                .where("RDEF_ID", rdefId)
                .delete();

            await this.connection
                .from("RULE_DEFINITION")
                .where("RDEF_ID", rdefId)
                .delete();

            return true;
        } catch (error: any) {
            globalThis.tasLogger.error("Error deleting rule", error);
            return false;
        }
    }

    async deleteTRules(eveDefId: number, rdefValue: string) {
        if (!eveDefId && !rdefValue) {
            throw new UserException(
                "Undefined parameters Please define eveDefId or rdefValue!",
            );
        }

        let query = this.connection.select().from("RULE_DEFINITION");
        if (eveDefId) {
            query = query.where("EVEDEF_ID", eveDefId);
        }
        if (rdefValue) {
            query = query.where("RDEF_VALUE", rdefValue);
        }

        const defs = await query;
        if (Array.isArray(defs) && defs.length > 0) {
            for (const def of defs) {
                await this.deleteTRule(def.RDEF_ID);
            }
        }
    }

    allRulesProcessed(event: Event) {
        return this.connection
            .select()
            .from(`${this.tableName} as R`)
            .innerJoin("RULE_DEFINITION as RD", "RD.RDEF_ID", "R.RDEF_ID") // Do not check rule with no rule_definition.
            .where("EVE_ID", event.EVE_ID)
            .whereNot("RULE_STATUS", ruleConst.RULE_STATUS_PROCESSED)
            .then((rules) => !Array.isArray(rules) || rules.length === 0);
    }

    setAsProcesses(eveId: number, rdefId: number) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("EVE_ID", eveId)
            .where("RDEF_ID", rdefId)
            .update({
                RULE_STATUS: ruleConst.RULE_STATUS_PROCESSED,
            });
    }
}
