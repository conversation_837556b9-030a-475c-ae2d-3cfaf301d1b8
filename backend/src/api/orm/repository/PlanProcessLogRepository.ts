// @ts-nocheck
// @ts-nocheck
import { PlanProcessLog } from "../entity/PlanProcessLog";
import { BaseRepository } from "./BaseRepository";

export class PlanProcessLogRepository extends BaseRepository<PlanProcessLog> {
    meta() {
        return {
            tableName: "PLAN_IPROC_LOG",
            entityName: "PlanProcessLog",
            entity: () => new PlanProcessLog(),
            archived: true,
        };
    }

    getLogs(plnId) {
        const con = this.connection
            .select([
                "PL.PLN_ID",
                "PL.PLNPRC_ID",
                "PL.IPROC_ID",
                "IP.IPROC_NAME",
                "PL.PLNPRC_STATUS",
                "PL.PLNPRC_USER_ID",
                this.connection.raw(`"U"."USER_DISPLAY_NAME" AS "USER_NAME"`),
                "PL.PLNPRC_DATETIME",
            ])
            .from(`${this.tableName} as PL`)
            .leftJoin("INSTANCE_PROCESSES as IP", "PL.IPROC_ID", "IP .IPROC_ID")
            .leftJoin("USERS as U", "PL.PLNPRC_USER_ID", "U.USER_ID")
            .where("PL.PLN_ID", plnId);

        return globalThis.orm.collection("PlanProcessLog", con);
    }
}
