// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as tprocConsts from "../entity/const/tprocConsts";
import { TemplateTaskJSCalculation } from "../entity/TemplateTaskJSCalculation";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { TasOldTranspiler } from "../../../infrastructure/calculation/utils/TasOldTranspiler";
import { AsyncAwaitTranspiler } from "../../../infrastructure/calculation/utils/AsyncAwaitTranspiler";

export class TemplateTaskJSCalculationRepository extends BaseRepository<TemplateTaskJSCalculation> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_JS_CALCULATIONS",
            entityName: "TemplateTaskJSCalculation",
            sequenceName: "TTASKJS_CALC_SEQ",
            entity: () => new TemplateTaskJSCalculation(),
        };
    }

    getForTTask(ttaskId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskJSCalculationRepository.getForTTask)",
            );
        }

        return this.getForTTasks(ttaskId, version);
    }

    getForTTasks(ttaskIds, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskJSCalculationRepository.getForTTasks)",
            );
        }

        const repo = this.connection
            .select()
            .from(`${this.tableName} as TTC`)
            .whereIn(
                "TTASK_ID",
                Array.isArray(ttaskIds) ? ttaskIds : [ttaskIds],
            )
            .where("TTJSCALC_VERSION", version)
            .orderBy("TTJSCALC_ID", "asc");

        return globalThis.orm.collection("TemplateTaskJSCalculation", repo);
    }

    async getForProcess(tprocId, version) {
        const tprocRepo = globalThis.orm.repo(
            "TemplateProcess",
            this.connection,
        );
        const tasks = await tprocRepo.getTTaskList(tprocId, version);
        // if (Array.isArray(tasks) && tasks.length > 0) {
        const ttaskIds = tasks.map((task) => task.TTASK_ID);
        return this.getForTTasks(ttaskIds, version);
        // }
    }

    async copyVersion(tprocId, srcVersion, dstVersion) {
        const tasks = await globalThis.orm
            .repo("templateTask", this.connection)
            .getForTProcess(tprocId);
        const ids = _.map(tasks, "TTASK_ID");

        const srcCalcs = await this.getForTTasks(ids, srcVersion).collectAll(); // Get source calculations
        await this.deleteForTTask(ids, dstVersion); // Remove old calculations

        // No calcs, do nothing
        if (!Array.isArray(srcCalcs) || srcCalcs.length === 0) {
            return srcCalcs;
        }

        const storedCalcs = [];
        // Copy calcs with new version and id.
        for (const calc of srcCalcs) {
            calc.TTJSCALC_VERSION = dstVersion; // Assign new version
            calc.id = null; // Reset ID for new entry
            calc.makeAllDirty(); // Mark as dirty for persistence

            const storedCalc = await this.store(calc);
            storedCalcs.push(storedCalc);
        }

        return storedCalcs;
    }

    async getForTProcess(tprocId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getForTProcess)",
            );
        }
        const tasks = await globalThis.orm
            .repo("TemplateTask", this.connection)
            .getForTProcess(tprocId);
        const ids = _.map(tasks, "TTASK_ID");
        return this.getForTTasks(ids, version);
    }

    async getForTTaskTranslated(ttaskId, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (getForTTaskTranslated)",
            );
        }

        const repo = this.connection
            .select()
            .from(`${this.tableName} as TTC`)
            .where("TTASK_ID", ttaskId)
            .where("TTJSCALC_VERSION", version)
            .orderBy("TTJSCALC_ID", "asc");

        const rows = await globalThis.orm
            .collection("TemplateTaskJSCalculation", repo)
            .collectAll();
        const variables = await globalThis.orm
            .repo("TemplateVariable")
            .getByTTaskId(ttaskId);

        if (Array.isArray(rows) && rows.length > 0) {
            const transpiler = new TasOldTranspiler();
            transpiler.setVariables(variables);
            for (let i = 0; i < rows.length; i += 1) {
                const row = rows[i];
                row.TTJSCALC_JS = transpiler.replaceTVarIdWithName(
                    row.TTJSCALC_JS,
                );
            }
        }

        return rows;
    }

    deleteForTTask(ttaskId, version?) {
        if (
            (version === null || typeof version === "undefined") &&
            version !== tprocConsts.TPROC_ALL_VERSIONS
        ) {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskJSCalculationRepository.deleteForTTask)",
            );
        }

        let delConnection = this.connection
            .select()
            .from(this.tableName)
            .whereIn("TTASK_ID", Array.isArray(ttaskId) ? ttaskId : [ttaskId]);

        if (version !== tprocConsts.TPROC_ALL_VERSIONS) {
            delConnection = delConnection.where("TTJSCALC_VERSION", version);
        }

        return delConnection.delete();
    }

    async create(ttaskId, _tprocId, calculations, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskJSCalculationsRepository.create)",
            );
        }

        if (!Array.isArray(calculations)) {
            return null;
        }

        await this.deleteForTTask(ttaskId, version);
        if (calculations.length === 0) {
            return true;
        }

        const storedCalculations = [];

        for (const calc of calculations) {
            const transpiler = new AsyncAwaitTranspiler(calc.ttjscalc_js);
            transpiler.parse();
            const transpiledCode = transpiler.generateCode();
            const storedCalc = await this.store(
                this.getEntity({
                    TTASK_ID: ttaskId,
                    TTJSCALC_JS: calc.ttjscalc_js,
                    TTJSCALC_JS_ES6: transpiledCode,
                    TTJSCALC_EXEC_START: calc.ttjscalc_exec_start,
                    TTJSCALC_EXEC_END: calc.ttjscalc_exec_end,
                    TTJSCALC_EXEC_RECALC: calc.ttjscalc_exec_recalc
                        ? calc.ttjscalc_exec_recalc
                        : "N",
                    TTJSCALC_EXEC_HAND: calc.ttjscalc_exec_hand
                        ? calc.ttjscalc_exec_hand
                        : "N",
                    TTJSCALC_EXEC_PULL: calc.ttjscalc_exec_pull
                        ? calc.ttjscalc_exec_pull
                        : "N",
                    TTJSCALC_APPEND_SCRIPTS: calc.ttjscalc_append_scripts
                        ? calc.ttjscalc_append_scripts
                        : "[]",
                    TTJSCALC_VERSION: version,
                }),
            );
            storedCalculations.push(storedCalc);
        }
        return storedCalculations;
    }

    /**
     * Variable name changed. Should change name in js.
     */
    async onVariableNameChange(oldTvar, tvar, version) {
        const jsLines = await (
            await this.getForTProcess(tvar.TPROC_ID, version)
        ).collectAll();

        if (jsLines.length === 0) {
            return true;
        }

        const transpiler = new TasOldTranspiler(this.connection, null, null);
        const updatedJsLines = [];

        for (const jsLine of jsLines) {
            jsLine.TTJSCALC_JS = transpiler.rename(
                oldTvar.TVAR_NAME,
                tvar.TVAR_NAME,
                jsLine.TTJSCALC_JS,
            );
            const es6transpiler = new AsyncAwaitTranspiler(jsLine.TTJSCALC_JS);
            es6transpiler.parse();
            jsLine.TTJSCALC_JS_ES6 = es6transpiler.generateCode();

            const storedJsLine = await this.store(jsLine);
            updatedJsLines.push(storedJsLine);
        }
        return updatedJsLines;
    }
}
