// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import { InstanceProcessVariables } from "../../utils/InstanceProcessVariables";
import { VariableVersioning } from "../../versioning/VariableVersioning";
import { TemplateVariableLovRepository as tvarLovRepoStatic } from "./TemplateVariableLovRepository";
import * as VARIABLES from "../entity/const/variableConst";
import { TemplateVariable } from "../entity/TemplateVariable";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";
import { BaseCollection } from "../BaseCollection";

export class TemplateVariableRepository extends BaseRepository<TemplateVariable> {
    meta() {
        return {
            tableName: "TEMPLATE_VARIABLES",
            entityName: "TemplateVariable",
            entity: () => new TemplateVariable(),
        };
    }

    async getExtendedForTemplateProcess(tprocId, tvarIds) {
        const tvarRepo = globalThis.orm.repo(
            "templateVariable",
            this.connection,
        );
        const tvarLovRepo = globalThis.orm.repo(
            "templateVariableLov",
            this.connection,
        );
        const collection = tvarRepo.getForTemplateProcess(tprocId);

        if (tvarIds) {
            collection.knex.whereIn(
                "TVAR_ID",
                Array.isArray(tvarIds) ? tvarIds : [tvarIds],
            );
        }

        const templateVariables = await collection.collectAll();
        const listsIds = [];
        const lists = [
            VARIABLES.TYPE_DYNAMIC_LIST,
            VARIABLES.TYPE_TEXT_LIST,
            VARIABLES.TYPE_DATE_LIST,
            VARIABLES.TYPE_NUMBER_LIST,
        ];

        templateVariables.forEach((variable) => {
            if (lists.includes(variable.TVAR_TYPE)) {
                listsIds.push(variable.TVAR_ID);
            }
        });

        const varLovs = await tvarLovRepo
            .getForTemplateVariables("*", listsIds)
            .collectAssoc("TVAR_ID");

        // Join retrieved variables with retrieved LOVs data
        const templateVariablesJoined = tvarLovRepoStatic.joinLov(
            templateVariables,
            varLovs,
        );
        const templateVariablesFilled = tvarLovRepo.fillLovValues(
            templateVariablesJoined,
        );
        return templateVariablesFilled;
    }

    superStore(entity) {
        // Can not call super.store in async method
        return super.store(entity);
    }

    async store(entity, version, updateInstances = false) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (doCreateTTask)",
            );
        }

        // Do some changes before tvar is stored.
        const isUpdate =
            typeof entity.TVAR_ID !== "undefined" && entity.TVAR_ID !== null;
        if (isUpdate) {
            await this.onUpdate(entity, version);
        }

        const tvarId = await this.superStore(entity);
        if (entity._lovs && Array.isArray(entity._lovs)) {
            const lovRepo = globalThis.orm.repo(
                "templateVariableLov",
                this.connection,
            );

            await lovRepo.connection
                .select()
                .from(lovRepo.tableName)
                .where("TVAR_ID", tvarId)
                .delete();

            for (const lov of entity._lovs) {
                lov.TVAR_ID = tvarId;
                await lovRepo.store(lov);
            }
        }

        // Update alias
        const ipv = new InstanceProcessVariables(this.connection);
        await ipv.updateTvarAlias(tvarId);

        // Store sequence.
        if (
            !isUpdate &&
            entity.TVAR_TYPE === "N" &&
            entity.TVAR_ATTRIBUTE === "S"
        ) {
            const seqRepo = globalThis.orm.repo(
                "instanceVariableSequence",
                this.connection,
            );
            await seqRepo.createIfNotExists(
                seqRepo.getEntity({
                    TPROC_ID: entity.TPROC_ID,
                    TVAR_ID: tvarId,
                }),
            );
        }

        // Fill instances
        const vv = new VariableVersioning(this.connection);
        await vv.fillInstances(tvarId); // Add vars to processes.
        if (updateInstances) {
            await vv.updateDefinition(tvarId); // Change definition.
        }

        return tvarId;
    }

    async onUpdate(entity, version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (onUpdate)",
            );
        }

        // Find tvar name usage in LinkConditions.
        return await this.get(entity.TVAR_ID).then(async (oldEntity) => {
            // Find tvar name usage in JsCalculations.
            const condRepo = globalThis.orm.repo(
                "TemplateLinkCondition",
                this.connection,
            );
            await condRepo.onVariableNameChange(oldEntity, entity);

            // Find tvar name usage in JsCalculations.
            const jsRepo = globalThis.orm.repo(
                "TemplateTaskJSCalculation",
                this.connection,
            );
            await jsRepo.onVariableNameChange(oldEntity, entity, version); // Should change name everywhere ?

            return true;
        });
    }

    public async delete(entity: TemplateVariable): Promise<void> {
        const usageRepo = globalThis.orm.repo(
            "templateTaskVarUsage",
            this.connection,
        );

        const templateLovRepo = globalThis.orm.repo(
            "templateVariableLov",
            this.connection,
        );

        await usageRepo.connection
            .select()
            .from(usageRepo.tableName)
            .where("TVAR_ID", entity.TVAR_ID)
            .delete();

        await templateLovRepo.connection
            .select()
            .from(templateLovRepo.tableName)
            .where("TVAR_ID", entity.TVAR_ID)
            .delete();

        await super.delete(entity);
    }

    getForTemplateProcess(tprocId): BaseCollection<TemplateVariable> {
        const columns = [
            `${this.tableName}.*`,
            globalThis.database.raw(
                `"TVAR_MM_CLASS" ${globalThis.orm.db.concatColumns(".")} "TVAR_MM_CLASS_ATTR" as "CLASS_AND_ATTR"`,
            ),
        ];

        return globalThis.orm.collection(
            this,
            this.connection
                .select(columns)
                .from(this.tableName)
                .where("TPROC_ID", tprocId),
        );
    }

    async getByTTaskId(ttaskId) {
        const task = await globalThis.orm
            .repo("TemplateTask", this.connection)
            .get(ttaskId, ["TPROC_ID"]);
        return await globalThis.orm
            .collection(
                this,
                this.connection
                    .select()
                    .from("TEMPLATE_VARIABLES")
                    .where("TPROC_ID", task.TPROC_ID),
            )
            .collectAll();
    }

    getByName(tprocId, tvarName) {
        return this.connection
            .select()
            .from(this.tableName)
            .where("TVAR_NAME", tvarName)
            .where("TPROC_ID", tprocId)
            .then((tvar) => {
                if (!Array.isArray(tvar) || tvar.length === 0) {
                    return null;
                }
                return tvar[0];
            });
    }

    getById(tvarId) {
        const collection = this.getCollection();

        if (Array.isArray(tvarId)) {
            collection.knex.from(this.tableName).whereIn("TVAR_ID", tvarId);
        } else {
            collection.knex.from(this.tableName).where("TVAR_ID", tvarId);
        }

        return collection;
    }

    getByDlistName(dlistName) {
        const conn = this.connection
            .select()
            .from(`${this.tableName} as IV`)
            .where("DLIST_NAME", dlistName);
        return globalThis.orm
            .collection("Variable", conn)
            .collectAll()
            .then((rows) => rows);
    }

    async updateVariablesDListName(oldDListName, newDListName) {
        return await this.connection
            .select()
            .from(`${this.tableName}`)
            .whereIn(
                "DLIST_NAME",
                Array.isArray(oldDListName) ? oldDListName : [oldDListName],
            )
            .update({ DLIST_NAME: newDListName });
    }

    async fillValues(items, cols) {
        if (items.TVAR_ID) {
            items = [items];
        }
        if (!Array.isArray(items) || items.length === 0) {
            return items;
        }
        const colWhere = cols.slice(0); // clone
        if (cols.indexOf("TVAR_ID") === -1) {
            colWhere.push("TVAR_ID"); // insert missing primary
        }
        const ids = _.map(items, "TVAR_ID");
        const tvars = await this.getMulti(ids, colWhere);

        for (const item of items) {
            const tvar = _.find(tvars, { TVAR_ID: item.TVAR_ID });
            cols.forEach((colName) => {
                item[colName] = tvar[colName];
            });
        }

        return items;
    }

    async clone(tvarId, tprocVersion, extraAttributes = {}) {
        try {
            const tvar = await this.get(tvarId);
            const oldId = tvar.TVAR_ID;
            const oldName = tvar.TVAR_NAME;
            const newName = await this.findUniqueCloneName(
                "TVAR_NAME",
                oldName,
            );
            tvar.TVAR_ID = null;
            tvar.TVAR_ALIAS = null; // @t3b-2130 kopirovani promenne duplikuje TVAR_ALIAS
            tvar.makeAllDirty();
            tvar.TVAR_NAME = newName;

            const attrs = Object.keys(extraAttributes);
            for (const attr of attrs) {
                tvar[attr.toUpperCase()] = extraAttributes[attr];
            }

            const newId = await this.store(tvar, tprocVersion);

            // Clone lovs
            const varLovRepo = globalThis.orm.repo(
                "TemplateVariableLov",
                this.connection,
            );
            await varLovRepo.copy({ TVAR_ID: oldId }, { TVAR_ID: newId });

            return newId;
        } catch (e) {
            throw new InternalException(e);
        }
    }
}
