import { Knex } from "knex";

import { InstanceProcessNote } from "../entity/InstanceProcessNote";
import { BaseRepository } from "./BaseRepository";
import { BaseCollection } from "../BaseCollection";
import { ProcessRepository } from "./ProcessRepository";
import { Process } from "../entity/Process";
import { EntityAttribute } from "../entity/BaseEntity";

export class InstanceProcessNoteRepository extends BaseRepository<InstanceProcessNote> {
    meta() {
        return {
            tableName: "INSTANCE_PROCESS_NOTES",
            entityName: "InstanceProcessNote",
            entity: () => new InstanceProcessNote(),
            archived: true,
        };
    }

    all(iprocId: number): BaseCollection<InstanceProcessNote> {
        // Add translations
        const extraColumns: string[] = [];

        const iProcAttrs: Record<string, EntityAttribute> = globalThis.orm
            .repo("process")
            .getEntity()
            .attributes();
        const iTaskAttrs: Record<string, EntityAttribute> = globalThis.orm
            .repo("instanceTask")
            .getEntity()
            .attributes();
        const tTaskAttrs: Record<string, EntityAttribute> = globalThis.orm
            .repo("templateTask")
            .getEntity()
            .attributes();

        // Instance Process translations
        Object.keys(iProcAttrs).forEach((attrName: string) => {
            if (iProcAttrs[attrName].translated) {
                extraColumns.push(`IP.${attrName}`);
                extraColumns.push(`IPI.${attrName} as ${attrName}_ORIGINAL`);
            }
        });

        // Instance Task translations
        Object.keys(iTaskAttrs).forEach((attrName: string) => {
            if (iTaskAttrs[attrName].translated) {
                extraColumns.push(`IT.${attrName}`);
            }
        });

        // Template Task translations
        Object.keys(tTaskAttrs).forEach((attrName: string) => {
            if (tTaskAttrs[attrName].translated) {
                extraColumns.push(`TT.${attrName}`);
            }
        });

        const columns: (string | Knex.Raw<any>)[] = [
            "IPN.*",
            this.connection.raw(`"IPI"."IPROC_NAME" as "IPROC_NAME_ORIGINAL"`),
            "IP.IPROC_NAME",
            this.connection.raw(
                `"UV"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "UV"."USER_FIRST_NAME" AS "USER_VICE_FULL_NAME"`,
            ),
            "UV.USER_DISPLAY_NAME AS USER_VICE_DISPLAY_NAME",
            "IT.ITASK_NAME",
            this.connection.raw(
                `"U"."USER_LAST_NAME" ${globalThis.orm.db.concatColumns()} "U"."USER_FIRST_NAME" AS "USER_FULL_NAME"`,
            ),
            "U.USER_DISPLAY_NAME",
        ].concat(extraColumns);

        const con = this.connection
            .select(columns)
            .from(`${this.tableName} as IPN`)
            .leftJoin("USERS as U", "IPN.IPN_USER_ID", "U.USER_ID")
            .leftJoin("USERS as UV", "IPN.IPN_VICE_USER_ID", "UV.USER_ID")
            .leftJoin("INSTANCE_TASKS as IT", "IPN.IPN_ITASK_ID", "IT.ITASK_ID")
            .leftJoin("TEMPLATE_TASKS as TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .leftJoin("INSTANCE_PROCESSES as IP", "IPN.IPROC_ID", "IP.IPROC_ID")
            .leftJoin(
                "INSTANCE_PROCESSES as IPI",
                "IPN.IPROC_ID_INSERTED",
                "IPI.IPROC_ID",
            )
            .where("IPN.IPROC_ID", iprocId);

        return globalThis.orm.collection("instanceProcessNote", con);
    }

    userNotes(
        iprocId: number,
        includeDeleted: boolean,
    ): BaseCollection<InstanceProcessNote> {
        const coll: BaseCollection<InstanceProcessNote> = this.all(iprocId);
        coll.knex.where("IPN.IPN_TYPE", "USER");
        if (!includeDeleted) {
            coll.knex.where("IPN.DELETED_AT", null);
        }
        return coll;
    }

    systemNotes(
        iprocId: number,
        includeDeleted: boolean,
    ): BaseCollection<InstanceProcessNote> {
        const coll: BaseCollection<InstanceProcessNote> = this.all(iprocId);
        coll.knex.where("IPN.IPN_TYPE", "SYSTEM");
        if (!includeDeleted) {
            coll.knex.where("IPN.DELETED_AT", null);
        }
        return coll;
    }

    async upsert(
        iprocId: number,
        iprocNote: string,
        itaskId: number | null,
        userId: number,
        userIdVice: number | null,
        iprocIdInserted: number,
        ipnId?: number,
    ): Promise<void> {
        const procRepo: ProcessRepository = globalThis.orm.repo(
            "process",
            this.connection,
        );

        if (ipnId) {
            const existingNote = await this.connection
                .table(this.meta().tableName)
                .where({
                    IPN_ID: ipnId,
                })
                .select("IPN_USER_ID")
                .first();

            if (existingNote.IPN_USER_ID !== userId) {
                throw new Error("You are not allowed to modify this note.");
            }
            await this.connection
                .table(this.meta().tableName)
                .where({
                    IPN_ID: ipnId,
                    DELETED_AT: null,
                })
                .update({
                    DELETED_AT: new Date(),
                });
        }

        const process: Process = await procRepo.get(iprocId);

        const entity: InstanceProcessNote = this.getEntity({
            IPN_TYPE: "USER",
            IPN_USER_ID: userId,
            IPN_VICE_USER_ID: userIdVice,
            IPROC_ID: iprocId,
            IPN_ITASK_ID: itaskId,
            IPN_DATETIME: new Date(),
            IPN_NOTE: iprocNote,
            IPROC_ID_INSERTED: iprocIdInserted,
            DELETED_AT: null,
        });

        await this.store(entity);

        if (process.IPROC_MAIN_IPROC_ID > 0) {
            await this.upsert(
                process.IPROC_MAIN_IPROC_ID,
                iprocNote,
                itaskId,
                userId,
                userIdVice,
                iprocId,
            );
        }
    }

    async toggleVisibility(
        ipnId: number,
        currentUserId: number,
    ): Promise<void> {
        const note = await this.connection
            .table(this.meta().tableName)
            .where("IPN_ID", ipnId)
            .select("IS_VISIBLE", "IPN_USER_ID")
            .first();

        if (note.IPN_USER_ID !== currentUserId) {
            throw new Error("You are not allowed to modify this note.");
        }

        await this.connection
            .table(this.meta().tableName)
            .where("IPN_ID", ipnId)
            .update({ IS_VISIBLE: !note.IS_VISIBLE });
    }
}
