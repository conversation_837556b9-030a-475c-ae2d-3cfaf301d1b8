// @ts-nocheck
// @ts-nocheck
import _ from "lodash";
import { BaseRepository } from "./BaseRepository";
import * as tprocConsts from "../entity/const/tprocConsts";
import { TemplateTaskMassUsage } from "../entity/TemplateTaskMassUsage";
import { InternalException } from "../../../utils/errorHandling/exceptions/internalException";

export class TemplateTaskMassUsageRepository extends BaseRepository<TemplateTaskMassUsage> {
    meta() {
        return {
            tableName: "TEMPLATE_TASK_MASS_USAGE",
            entityName: "TemplateTaskMassUsage",
            entity: () => new TemplateTaskMassUsage(),
        };
    }

    getAll(version) {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskMassUsageRepository.getAll)",
            );
        }

        return this.connection
            .select()
            .from(this.tableName)
            .where("TSKMSS_VERSION", version);
    }

    getForTTask(tprocId, ttaskId, version) {
        if (!version) {
            // No null, undefined nor 0, '0'
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskMassUsageRepository.getForTTask)",
            );
        }

        const tvarEntity = globalThis.orm
            .repo("TemplateVariable", this.connection)
            .getEntity();
        const attrs1 = _.omit(tvarEntity.attributes(), ["ORG_ID", "TVAR_ID"]);
        const tvarCols = Object.keys(attrs1).map((el) => `TV.${el}`);

        const repo = this.connection
            .select(
                [
                    "TTVU.TSKMSS_ID",
                    "TV.TPROC_ID",
                    "TP.TPROC_NAME",
                    "TP.TPROC_VERSION",
                    "TTVU.TTASK_ID",
                    "TV.TVAR_ID",
                    "TTVU.TSKMSS_USAGE",
                    "TV.TVAR_COPY_SNAPSHOT",
                    "TTVU.TSKMSS_ORDER",
                    "DT.DT_ID",
                ].concat(tvarCols),
            )
            .from("TEMPLATE_VARIABLES as TV")
            .leftJoin(`${this.tableName} as TTVU`, "TV.TVAR_ID", "TTVU.TVAR_ID")
            .leftJoin("TEMPLATE_PROCESSES as TP", "TV.TPROC_ID", "TP.TPROC_ID")
            .leftJoin("DYNAMIC_TABLE AS DT", "TV.DLIST_NAME", "DT.DT_NAME")
            .where("TTVU.TTASK_ID", ttaskId)
            .where("TTVU.TPROC_ID", tprocId)
            .where("TSKMSS_VERSION", version)
            .orderBy("TTVU.TSKMSS_ORDER", "asc")
            .orderBy("TTVU.TSKMSS_ID", "asc");

        return globalThis.orm.collection("TemplateTaskMassUsage", repo);
    }

    deleteForTProc(tprocId, version) {
        const coll = this.getCollection();
        return coll.knex
            .where("TPROC_ID", tprocId)
            .where("TSKMSS_VERSION", version)
            .delete();
    }

    deleteForTTask(ttaskId, version?) {
        if (
            (version === null || typeof version === "undefined") &&
            version !== tprocConsts.TPROC_ALL_VERSIONS
        ) {
            throw new InternalException(
                "Version must be defined. Please fix your code! (TemplateTaskMassUsageRepository.deleteForTTask)",
            );
        }

        let delConnection = this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId);

        if (version !== tprocConsts.TPROC_ALL_VERSIONS) {
            delConnection = delConnection.where("TSKMSS_VERSION", version);
        }

        return delConnection.delete();
    }

    async copyVersion(tprocId, srcVersion, dstVersion): Promise<any> {
        const srcMapping = await this.getUsagesForTemplateProcess(
            tprocId,
            null,
            srcVersion,
        ).collectAll();
        await this.deleteForTProc(tprocId, dstVersion);
        if (!Array.isArray(srcMapping) || srcMapping.length === 0) {
            return srcMapping;
        }
        for (const map of srcMapping) {
            map.TSKMSS_VERSION = dstVersion;
            map.id = null;
            map.makeAllDirty();

            await this.store(map);
        }
    }

    async setTTaskMassUsage(ttaskId, tprocId, ttaskVarMapping, version) {
        if (!version) {
            throw new InternalException(
                "Version must be defined. Please fix your code! (setTTaskMassUsage)",
            );
        }

        const toDeleteCon = this.connection
            .select()
            .from(this.tableName)
            .where("TTASK_ID", ttaskId)
            .where("TSKMSS_VERSION", version);
        const toDeleteCol = this.createCollection(toDeleteCon);
        const toDeleteArr = await toDeleteCol.collectAll();

        await toDeleteCon.delete();

        for (const tvar of ttaskVarMapping) {
            let axisX =
                typeof tvar.TSKMSS_ORDER !== "undefined" &&
                tvar.TSKMSS_ORDER !== null
                    ? tvar.TSKMSS_ORDER
                    : null;

            if (axisX === null) {
                const findDeleted = _.find(toDeleteArr, {
                    TVAR_ID: tvar.tvar_id,
                });

                if (findDeleted) {
                    axisX = findDeleted.TSKMSS_ORDER;
                }
            }

            // Insert with seq_id select due to optimisation !!
            await this.connection.raw(
                `INSERT INTO "TEMPLATE_TASK_MASS_USAGE" ("TSKMSS_ID", "TPROC_ID", "TTASK_ID", "TVAR_ID", "TSKMSS_USAGE", "TSKMSS_ORDER", "TSKMSS_VERSION") ` +
                    `select ${globalThis.orm.db.sequence(`TSKMSS_ID_SEQ`)}, :TPROC_ID, :TTASK_ID, :TVAR_ID, :TSKMSS_USAGE, :TSKMSS_ORDER, :TSKMSS_VERSION ${globalThis.orm.db.fromDual()}`,
                {
                    TPROC_ID: tprocId,
                    TTASK_ID: ttaskId,
                    TVAR_ID: tvar.tvar_id,
                    TSKMSS_USAGE: tvar.usage,
                    TSKMSS_VERSION: version,
                    TSKMSS_ORDER: axisX,
                },
            );
        }
    }
}
