import { IInstanceTaskLinkAttributes } from "../orm/entity/InstanceTaskLink";
import { IProcess } from "../orm/entity/Process";
import { ITask } from "../orm/entity/Task";
import * as LINK from "../orm/entity/const/linkConst";
import * as taskConst from "../orm/entity/const/taskConst";
import { VariableList } from "./VariableList";
import { WorkflowBase } from "./WorkflowBase";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";

import { TimeMeasure } from "../utils/TimeMeasure";

interface IExtendedInstanceTaskLinkAttributes
    extends IInstanceTaskLinkAttributes {
    ITASK_DISC_FLAG: "Y" | "N";
}

interface IWorkflowLinkMethods {
    getIncomingLinksV2(
        task: ITask,
        version?: number | null,
    ): Record<string, any>[];
    getIncomingLinks(task: ITask, restrictDone: boolean): Record<string, any>[];
    getOutgoingLinks(task: ITask): Record<string, any>[];
    finishLink(task: ITask, link: any): Promise<boolean>;
    activateRight2(
        task: ITask,
        process: any,
        version: number | null | undefined,
    ): Promise<void>;
    activateTTaskId(
        process: any,
        ttaskId: number,
        canFinish: boolean,
        forceActivate: boolean,
    ): Promise<void>;
    activateRight(
        link: IExtendedInstanceTaskLinkAttributes,
        task: ITask,
        process: IProcess,
        canFinish: boolean,
    ): Promise<any>;
    getDiscInfo(itaskLinkId: number, toItaskId: string): Promise<any>;
    checkConditionsV2(
        task: ITask,
        link: Record<string, any>,
        version: number | null | undefined,
    ): Promise<boolean>;
    checkConditions(
        link: IExtendedInstanceTaskLinkAttributes,
        task: ITask,
    ): Promise<boolean>;
}

export interface IWorkflowLink extends IWorkflowLinkMethods {}

/**
 * Process workflow.
 * Use task class to start Process, activate tasks and other workflow operations.
 *
 * <AUTHOR> Klima
 *
 */
export class WorkflowLink extends WorkflowBase implements IWorkflowLink {
    /**
     * getIncomingLinks - returns all links which have this task on right side
     * return array of ILink objects
     */
    getIncomingLinksV2(task: ITask, version?: number | null): any {
        return this.orm.repo("wfLink").getIncomingLinksV2(task, version);
    }

    /**
     * getIncomingLinks - returns all links which have this task on right side
     * param restrictDone if true then only links from done tasks are taken
     * return array of ILink objects
     */
    getIncomingLinks(task: ITask, restrictDone: boolean = false): any {
        return this.orm
            .repo("wfLink")
            .getIncomingLinks(task, restrictDone)
            .collectAll();
    }

    /**
     * getOutgoingLinks - returns all links which have this task on left side
     */
    getOutgoingLinks(task: ITask): any {
        return this.orm.repo("wfLink").getOutgoingLinks(task).collectAll();
    }

    /**
     * Mark link as finished. If belongs to multiinstance task then link counter is increased.
     */
    async finishLink(task: ITask, link: any): Promise<boolean> {
        const linkDoneRepo = this.orm.repo(
            "instanceTaskLinkDone",
            this.orm.connection,
        );

        if (
            task.ITASK_MULTIINSTANCE_FLAG === taskConst.MULTIINSTANCE_FLAG_INST
        ) {
            const count = await linkDoneRepo.getCount(
                task.IPROC_ID,
                link.TTASKLINK_ID,
            );
            if (count > 0) {
                await linkDoneRepo.setCount(
                    task.IPROC_ID,
                    link.TTASKLINK_ID,
                    count + 1,
                );
            } else {
                const multiTasks = await globalThis.orm
                    .repo("instanceTask")
                    .getMultiinstanceInstances(task.TTASK_ID, task.IPROC_ID);
                const maxLinks = multiTasks.length;

                try {
                    await linkDoneRepo
                        //@ts-expect-error private field
                        .connection(linkDoneRepo._tableName)
                        .insert({
                            IPROC_ID: task.IPROC_ID,
                            TTASKLINK_ID: link.TTASKLINK_ID,
                            COUNT: 1, // Only one link of every multiinstance finished
                            MAX_COUNT: maxLinks,
                        });
                } catch (_err) {
                    await linkDoneRepo
                        //@ts-expect-error private field
                        .connection(linkDoneRepo._tableName)
                        .where({
                            IPROC_ID: task.IPROC_ID,
                            TTASKLINK_ID: link.TTASKLINK_ID,
                        })
                        .update({
                            IPROC_ID: task.IPROC_ID,
                            TTASKLINK_ID: link.TTASKLINK_ID,
                            COUNT: 1, // Only one link of every multiinstance finished
                            MAX_COUNT: maxLinks,
                        });
                }
            }
        } else {
            const exists = await this.orm.connection
                .select()
                .from("INSTANCE_TASK_LINK_DONE")
                .where({
                    IPROC_ID: task.IPROC_ID,
                    TTASKLINK_ID: link.TTASKLINK_ID,
                });

            if (exists.length) {
                await this.orm
                    .connection("INSTANCE_TASK_LINK_DONE")
                    .where({
                        IPROC_ID: task.IPROC_ID,
                        TTASKLINK_ID: link.TTASKLINK_ID,
                    })
                    .update({
                        IPROC_ID: task.IPROC_ID,
                        TTASKLINK_ID: link.TTASKLINK_ID,
                        COUNT: 1,
                        MAX_COUNT: 1,
                    });
            } else {
                await this.orm.connection("INSTANCE_TASK_LINK_DONE").insert({
                    IPROC_ID: task.IPROC_ID,
                    TTASKLINK_ID: link.TTASKLINK_ID,
                    COUNT: 1,
                    MAX_COUNT: 1,
                });
            }

            return true;
        }
        return false;
    }

    /**
     * activateRight activates right task of this link
     */
    async activateRight2(
        task: ITask,
        process: any,
        version: number | null | undefined,
    ): Promise<void> {
        const outgoingLinks = await this.orm
            .repo("templateTaskLink")
            .outgoingLinks2(task.TTASK_ID, version);

        let useElseLink = true;
        let elseLink = null;

        // Check all link statuses.
        for (const link of outgoingLinks) {
            if (link.TTASKLINK_TYPE !== LINK.TYPE_ELSE) {
                link.isOk = await this.wfLink.checkConditionsV2(
                    task,
                    link,
                    // Assert that version is not null
                    version!,
                );
                if (link.isOk) {
                    globalThis.tasLogger.info(
                        `✔ Link (${link.CONDITIONS.length} conditions) from task '${task.ITASK_NAME}' -> ${link.TTASKLINK_TO_TTASK_ID} is OK.`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );
                    useElseLink = false;

                    link.forceActivate = await this.finishLink(task, link);
                } else {
                    globalThis.tasLogger.info(
                        `✘ Link (${link.CONDITIONS.length} conditions) from task '${task.ITASK_NAME}' -> ${link.TTASKLINK_TO_TTASK_ID} is not OK.`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );
                }
            } else {
                elseLink = link;
            }
        }

        if (elseLink && useElseLink) {
            // There is no link completed. Use else link.
            elseLink.forceActivate = await this.finishLink(task, elseLink);
            await this.activateTTaskId(
                process,
                elseLink.TTASKLINK_TO_TTASK_ID,
                true,
                elseLink.forceActivate,
            );
        } else {
            // Check if any link is done.
            for (const link of outgoingLinks) {
                // keeping the link order.
                if (link.isOk && link.TTASKLINK_TYPE !== LINK.TYPE_ELSE) {
                    await this.activateTTaskId(
                        process,
                        link.TTASKLINK_TO_TTASK_ID,
                        false,
                        link.forceActivate,
                    );
                }
            }
        }
    }

    async activateTTaskId(
        process: IProcess,
        ttaskId: number,
        canFinish: boolean = true,
        forceActivate: boolean = false,
    ): Promise<void> {
        const task = await globalThis.orm
            .collection(
                "instanceTask",
                this.orm.connection
                    .select()
                    .from("INSTANCE_TASKS")
                    .where("IPROC_ID", process.IPROC_ID)
                    .where("TTASK_ID", ttaskId)
                    .where((multiWhere: any) => {
                        // Select multiinstance template or standard task.
                        multiWhere
                            .where(
                                "ITASK_MULTIINSTANCE_FLAG",
                                taskConst.MULTIINSTANCE_FLAG_YES,
                            )
                            .orWhere(
                                "ITASK_MULTIINSTANCE_FLAG",
                                taskConst.MULTIINSTANCE_FLAG_NO,
                            );
                    }),
            )
            .collectOne();
        await this.wfTask.activate(
            task,
            process,
            canFinish,
            undefined,
            forceActivate,
        );
    }

    /**
     * activateRight activates right task of this link
     * param {boolean} canFinish - subsequent tasks cannot finish process if set to false
     */
    async activateRight(
        link: any,
        task: ITask,
        process: IProcess,
        canFinish: boolean = true,
    ): Promise<any> {
        link.ITASKLINK_STATUS = LINK.STATUS_START;
        await this.orm.repo("wfLink").store(link); // important for next sql queries

        if (await this.checkConditions(link, task)) {
            link.ITASKLINK_STATUS = LINK.STATUS_END;
            await this.orm.repo("wfLink").store(link); // important for next sql queries
            globalThis.tasLogger.info(
                `activate right from ITASK_ID='${task.ITASK_ID}', TTASK_ID='${task.TTASK_ID}', ITASK_NAME='${task.ITASK_NAME}'`,
                {
                    iproc_id: process.IPROC_ID,
                    itask_id: task.ITASK_ID,
                },
            );
            let rtask = await this.orm
                .repo("wfTask")
                .get(link.ITASKLINK_TO_TTASK_ID); // ITASKLINK_TO_TTASK_ID is marked worng, it is ..._TO_ITASK_ID
            if (
                rtask &&
                rtask.ITASK_ID &&
                //Assert that workflow.taskList is not null
                this.workflow.taskList![rtask.ITASK_ID]
            ) {
                // Task was activated again. Use cached entity instead of db entity.
                globalThis.tasLogger.info(
                    `Task ${rtask.ITASK_ID} was activated again. Use cached entity instead of db entity.`,
                    {
                        iproc_id: process.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                //Assert that workflow.taskList is not null
                rtask = this.workflow.taskList![rtask.ITASK_ID];
            }

            if (rtask.ITASK_DISC_FLAG === "Y") {
                // can run only once
                // kouknu se jestli ostatni jsem prvni
                // Assert that values are not null
                const discInfo = await this.getDiscInfo(
                    link.ITASKLINK_ID!,
                    link.ITASKLINK_TO_TTASK_ID!,
                );

                if (discInfo.NOT_ACTIVE_LINKS === discInfo.ALL_LINKS) {
                    // is first one
                    // activate task
                    // je prvni, spustim aktivuju task
                    link.ITASKLINK_DISC_FLAG = "Y";
                    await this.orm.repo("wfLink").store(link);
                    await this.wfTask.activate(rtask, process, canFinish);
                    return rtask;
                }
                if (
                    discInfo.ALL_LINKS - discInfo.ACTIVE_LINKS === 1 &&
                    discInfo.THIS_NOT_ACTIVE === 1
                ) {
                    // is last one
                    // reset disc flags off all links
                    // je posledni, resetuju
                    await this.orm
                        .repo("wfLink")
                        .resetDiscFlags(link.ITASKLINK_TO_TTASK_ID);

                    if (discInfo.NEXT_RUN > 0) {
                        // something waiting - must activate next run
                        await this.wfTask.activate(rtask, process, canFinish);
                        return rtask;
                    }

                    return false;
                }
                if (discInfo.THIS_ACTIVE === 1) {
                    // is middle one already activated
                    // set disc next run flag to yes
                    link.ITASKLINK_DISC_NEXT_RUN = "Y";
                    await this.orm.repo("wfLink").store(link);
                    return false;
                }
                // is middle and did not run yet
                // set disc flag to yes
                // neni prvni ani posledni, jen setuju flag
                link.ITASK_DISC_FLAG = "Y";
                await this.orm.repo("wfLink").store(link);
                return false;
            }
            await this.wfTask.activate(rtask, process, canFinish);
            return rtask;
        }
        return false;
    }

    getDiscInfo(itaskLinkId: number, toItaskId: string): Promise<any> {
        return this.workflow.orm
            .repo("wfLink")
            .getDiscInfo(itaskLinkId, toItaskId);
    }

    async checkConditionsV2(
        task: ITask,
        link: Record<string, any>,
        version: number,
    ): Promise<boolean> {
        if (version === null || typeof version === "undefined") {
            throw new InternalException(
                "Version must be defined. Please fix your code! (WorkflowLink.checkConditionsV2)",
            );
        }

        const conds = link.CONDITIONS;

        // else cannot have conditions
        if (link.TTASKLINK_TYPE === LINK.TYPE_ELSE) {
            return true;
        }

        // not conds
        if (!conds || conds.length === 0) {
            return true;
        }

        // Fill snap variables if multiinstance.
        let varList;
        if (task.isMultiinstance()) {
            varList = new VariableList();
            varList.setVariables(
                await this.workflow.orm
                    .repo("variableSnap")
                    .getByTask(task.ITASK_ID)
                    .collectAll(),
            );
        } else {
            varList = this.variableList;
        }

        // Check all conditions.
        for (const cond of conds) {
            const val = await this.wfCondition.evaluateCondition(varList, cond);

            if (link.TTASKLINK_TYPE === LINK.TYPE_AND && !val) {
                return false;
            }
            if (link.TTASKLINK_TYPE === LINK.TYPE_OR && val) {
                return true;
            }
        }
        // If AND then all conditions are OK. If OR then no condition is OK.
        return link.TTASKLINK_TYPE === LINK.TYPE_AND;
    }

    /**
     * checkConditions returns state of conditions
     * return true if conditions met
     */
    async checkConditions(link: any, task: ITask): Promise<boolean> {
        const tm = new TimeMeasure();
        tm.start("link");
        let duration = 0;

        // else cannot have conditions
        if (link.ITASKLINK_TYPE === LINK.TYPE_ELSE) {
            return true;
        }

        // load multiinstance varibles if necessary
        let varList;
        if (task.isMultiinstance()) {
            varList = new VariableList();
            varList.setVariables(
                await this.workflow.orm
                    .repo("variableSnap")
                    .getByTask(task.ITASK_ID)
                    .collectAll(),
            );
        } else {
            varList = this.variableList;
        }

        const conds = await this.workflow.orm
            .repo("instanceLinkCondition")
            .getConditions(link)
            .collectAll();
        switch (link.ITASKLINK_TYPE) {
            case LINK.TYPE_AND:
                for (const cond of conds) {
                    const val = await this.wfCondition.evaluateLink(
                        varList,
                        cond,
                    );
                    if (!val) {
                        duration = tm.end("link");
                        if (
                            duration >
                            globalThis.dynamicConfig.tas.measurement
                                .linkEvaluation
                        ) {
                            globalThis.tasLogger.warning(
                                `Link for '${task.ITASK_NAME}' takes too long ($measure=${duration}s)`,
                                {
                                    iproc_id: task.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                    conds,
                                    link,
                                },
                            );
                        }

                        return false;
                    }
                }

                duration = tm.end("link");
                if (
                    duration >
                    globalThis.dynamicConfig.tas.measurement.linkEvaluation
                ) {
                    globalThis.tasLogger.warning(
                        `Link for '${task.ITASK_NAME}' takes too long ($measure=${duration}s)`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                            conds,
                            link,
                        },
                    );
                }
                return true;
            case LINK.TYPE_OR:
                // if no condition is set then always return true
                if (conds.length === 0) {
                    duration = tm.end("link");
                    if (
                        duration >
                        globalThis.dynamicConfig.tas.measurement.linkEvaluation
                    ) {
                        globalThis.tasLogger.warning(
                            `Link for '${task.ITASK_NAME}' takes too long ($measure=${duration}s)`,
                            {
                                iproc_id: task.IPROC_ID,
                                itask_id: task.ITASK_ID,
                                conds,
                                link,
                            },
                        );
                    }
                    return true;
                }
                for (const cond of conds) {
                    const val = await this.wfCondition.evaluateLink(
                        varList,
                        cond,
                    );
                    if (val) {
                        duration = tm.end("link");
                        if (
                            duration >
                            globalThis.dynamicConfig.tas.measurement
                                .linkEvaluation
                        ) {
                            globalThis.tasLogger.warning(
                                `Link for '${task.ITASK_NAME}' takes too long ($measure=${duration}s)`,
                                {
                                    iproc_id: task.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                    conds,
                                    link,
                                },
                            );
                        }
                        return true;
                    }
                }
                duration = tm.end("link");
                if (
                    duration >
                    globalThis.dynamicConfig.tas.measurement.linkEvaluation
                ) {
                    globalThis.tasLogger.warning(
                        `Link for '${task.ITASK_NAME}' takes too long ($measure=${duration}s)`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                            conds,
                            link,
                        },
                    );
                }
                return false;
            default:
                throw new InternalException(
                    `Not supported ITASKLINK_TYPE '${link.ITASKLINK_TYPE}'`,
                );
        }
    }
}
