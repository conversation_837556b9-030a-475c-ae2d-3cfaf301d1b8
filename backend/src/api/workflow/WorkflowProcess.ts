import * as PROCESS from "../orm/entity/const/processConst";
import { DmsAccessLogger } from "../utils/DmsAccessLogger";
import fs from "fs";
import byteTruncate from "truncate-utf8-bytes";
import { IProcess } from "../orm/entity/Process";
import { ITask } from "../orm/entity/Task";
import { ITemplateProcess } from "../orm/entity/TemplateProcess";
import { IUser } from "../orm/entity/User";
import * as templateShreddingConst from "../orm/entity/const/templateShreddingConst";
import { Workflow } from "./Workflow";
import { WorkflowBase } from "./WorkflowBase";
import { Knex } from "knex";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { DmsFile } from "../orm/entity/DmsFile";
import { LogCategory } from "../../utils/logger/logConsts";

interface IWorkflowProcessMethods {
    activateTasks(
        process: IProcess,
        user: IUser,
        tasks: ITask[],
    ): Promise<void>;
    processVersion(proc: IProcess | number): Promise<any>;
    verifyStatus(process: IProcess): Promise<IProcess["IPROC_STATUS"]>;
    setStatusDone(process: IProcess): Promise<number>;
    setStatusActive(process: any): Promise<void>;
    setStatusError(process: any): Promise<number>;
    updateSummary(proc: any): Promise<boolean | number>;
    autostartInitialTasks(process: IProcess, taskRepo: any): Promise<any[]>;
    checkCompletionProcess(
        process: IProcess,
        canFinish: boolean,
    ): Promise<number | boolean>;
    shredProcess(iprocId: number): Promise<void>;
    checkProcessDueDateSchedule(process: IProcess): Promise<any>;
    getTproc(tprocId: number): Promise<ITemplateProcess>;
}

export interface IWorkflowProcess extends IWorkflowProcessMethods {}

/**
 * Process workflow.
 * Use this class to start Process, activate tasks and other workflow operations.
 *
 * <AUTHOR> Klima
 *
 */
export class WorkflowProcess extends WorkflowBase implements IWorkflowProcess {
    /**
     * Activate tasks after copy instances
     */
    async activateTasks(
        process: IProcess,
        user: IUser,
        tasks: ITask[],
    ): Promise<void> {
        if (process.IPROC_STATUS === PROCESS.STATUS_ACTIVE) {
            throw new InternalException(
                "Cannot activate activated process again.",
            );
        }
        if (process.IPROC_STATUS === PROCESS.STATUS_DONE) {
            throw new InternalException("Cannot reactivate finished process.");
        }
        if (process.IPROC_STATUS === PROCESS.STATUS_ERASED) {
            throw new InternalException("Cannot reactivate erased process.");
        }

        process.IPROC_STATUS = PROCESS.STATUS_ACTIVE;
        process.IPROC_INST_OWNER_USER_ID = user.USER_ID;
        process.IPROC_ACTUAL_START_DATE = new Date();

        await this.orm.repo("wfProcess").store(process);

        // TODO:hooks

        // activate tasks one by one
        for (const task of tasks) {
            await this.wfTask.activate(task, process);
        }
    }

    /**
     * Process or iproc_id
     * returns {Promise<*|*>}
     */
    async processVersion(proc: IProcess | number): Promise<any> {
        const id = typeof proc === "object" ? proc.IPROC_ID : proc;
        const key = `PROC_VERSION-${id}` as const;
        if (!(await globalThis.container.client.cache.cache.cacheExists(key))) {
            await globalThis.container.client.cache.cache.set(
                key,
                await this.workflow.orm
                    .repo(
                        "instanceProcessVersion",
                        this.workflow.orm.connection,
                    )
                    .getProcessVersion(id),
            );
        }

        return await globalThis.container.client.cache.cache.get(key);
    }

    /**
     * Verify and update status of the process
     *

     * @access public
     * @return New, Done or active
     */
    async verifyStatus(process: IProcess): Promise<IProcess["IPROC_STATUS"]> {
        try {
            if (
                process.IPROC_STATUS === PROCESS.STATUS_NOT_ACTIVE ||
                process.IPROC_STATUS === PROCESS.STATUS_DONE
            ) {
                return process.IPROC_STATUS;
            }

            // fixme v pripade je aktivni pouze jeden task, ktery nema naslednika se proces ukonci
            const procVersion = await this.wfProcess.processVersion(
                // Assert that process.IPROC_ID is not null
                process.IPROC_ID!,
            );
            const collection = await this.orm
                .repo("wfProcess")
                .checkActiveTasks(process, procVersion.TTASKLINK_VERSION);
            const activeTasks = await collection.collectAll();
            if (activeTasks.length === 0) {
                globalThis.tasLogger.info(
                    `Process ${process.IPROC_NAME} has 0 active tasks. Setting done.`,
                    { iproc_id: process.IPROC_ID },
                );
                await this.setStatusDone(process);
                return PROCESS.STATUS_DONE;
            }
            return PROCESS.STATUS_ACTIVE;
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                e,
            });
            throw e;
        }
    }

    /**

     * @param process
     * @returns {Promise<*>}
     */
    async setStatusDone(process: any): Promise<number> {
        globalThis.tasLogger.info(
            `Set status done for '${process.IPROC_NAME}'`,
            {
                iproc_id: process.IPROC_ID,
            },
        );

        // check subprocess status
        if (process.IPROC_SUBPROCESS_FLAG === 1) {
            // subprocess need return values throw event
            // Find main process event.
            // Assert that this.workflow.orm is not null
            const event = await this.workflow
                .orm!.repo("event")
                .get(process.IPROC_EVENT_STARTER); // main process event

            // $SUBP_ -> $SUBR_ to get mapping for RETURN rule.
            const subrEventDefName = event.EVEDEF_NAME.replace(
                "$SUBP_",
                "$SUBR_",
            );

            // Copy mapped variables from subprocess to main process.
            await this.wfEvent.returnMappingFromSubProcess(
                process,
                subrEventDefName,
                this.variableList,
            );

            const waitingMainProcess = await this.orm
                .repo("wfTask")
                .getMainProcessWaitingTasks(process.IPROC_ID)
                .collectAll();
            if (
                Array.isArray(waitingMainProcess) &&
                waitingMainProcess.length > 0
            ) {
                // Activate and finish akk tasks waiting to return from subprocess.
                for (const waitingTask of waitingMainProcess) {
                    globalThis.tasLogger.info(
                        `Status done for '${process.IPROC_NAME}' invoked activation of waiting task '${waitingTask.ITASK_NAME}' with ITASK_ID=${waitingTask.ITASK_ID}.`,
                        {
                            iproc_id: process.IPROC_ID,
                        },
                    );

                    const wf = new Workflow(this.orm, this.currentUser);
                    await wf.activateTask(waitingTask);
                    await wf.solveTask(waitingTask);
                }
            }
        } else {
            globalThis.tasLogger.info(
                `No subprocess in '${process.IPROC_NAME}'`,
                {
                    iproc_id: process.IPROC_ID,
                },
            );
        }

        process.IPROC_STATUS = PROCESS.STATUS_DONE;
        process.IPROC_ACTUAL_FINISH_DATE = new Date();

        process.IPROC_SUMMARY = null;
        [...globalThis.dynamicConfig.langs].forEach((lang) => {
            process[`IPROC_SUMMARY_${lang.toUpperCase()}`] = null;
        });

        return await this.orm.repo("wfProcess").store(process);
    }

    async setStatusActive(process: IProcess): Promise<void> {
        globalThis.tasLogger.info(
            `Set status active for '${process.IPROC_NAME}'`,
            {
                iproc_id: process.IPROC_ID,
            },
        );

        process.IPROC_STATUS = PROCESS.STATUS_ACTIVE;
        await this.updateSummary(process);
    }

    /**

     * @param process
     * @returns {*}
     */
    setStatusError(process: any): Promise<number> {
        process.IPROC_STATUS = PROCESS.STATUS_ERRORED;
        process.IPROC_ACTUAL_START_DATE = new Date();
        process.IPROC_SUMMARY = null;
        [...globalThis.dynamicConfig.langs].forEach((lang) => {
            process[`IPROC_SUMMARY_${lang.toUpperCase()}`] = null;
        });

        return this.orm.repo("wfProcess").store(process);
    }

    /**

     * @param proc
     * @returns {Promise<unknown>}
     */
    async updateSummary(
        proc: any,
        trx?: Knex.Transaction,
    ): Promise<boolean | number> {
        if (proc.IPROC_STATUS === PROCESS.STATUS_DONE) {
            return true;
        }

        const warnCaseWhen = globalThis.database.raw(
            `CASE WHEN "IT"."ITASK_DUE_DATE_FINISH" < ${globalThis.orm.db.sysDate()} THEN '!' ELSE '' END WARN`,
        );

        const translatedNames = globalThis.dynamicConfig.langs.map(
            (lang: string) => `TTASK_NAME_${lang.toUpperCase()}`,
        );
        translatedNames.push("TTASK_NAME");

        // Assert that this.workflow.orm is not null
        const tasksToSolve = await this.workflow
            .orm!.connection.select([
                "IT.ITASK_NAME",
                ...translatedNames,
                "U.USER_DISPLAY_NAME",
                "IT.ITASK_STATUS",
                warnCaseWhen,
            ])
            .from("INSTANCE_TASKS as IT")
            .leftJoin("USERS as U", "IT.ITASK_USER_ID", "U.USER_ID")
            .leftJoin("TEMPLATE_TASKS AS TT", "IT.TTASK_ID", "TT.TTASK_ID")
            .where("IT.IPROC_ID", proc.IPROC_ID)
            .where(function status(t: any) {
                t.where("IT.ITASK_STATUS", "A")
                    .orWhere("IT.ITASK_STATUS", "W")
                    .orWhere("IT.ITASK_STATUS", "T");
            });

        const langs = [...globalThis.dynamicConfig.langs, ""]; // empty string as default
        for (let langIndex = 0; langIndex < langs.length; langIndex += 1) {
            const lang = langs[langIndex].toUpperCase();

            const summary = [];
            for (
                let taskIndex = 0;
                taskIndex < tasksToSolve.length;
                taskIndex += 1
            ) {
                const task = tasksToSolve[taskIndex];
                task.WARN = task.WARN ? task.WARN : "";
                let value;

                if (task.ITASK_TYPE === "T") {
                    value = `${task[`TTASK_NAME${lang ? `_${lang}` : ""}`] || task.TTASK_NAME}: PULL`;
                } else {
                    value = `${task[`TTASK_NAME${lang ? `_${lang}` : ""}`] || task.TTASK_NAME}: ${task.USER_DISPLAY_NAME}${task.WARN}`;
                }
                summary.push(value);
            }

            // Max 4000 bytes.
            const stringSummary = summary.join(", \n") || null;
            proc[`IPROC_SUMMARY${lang ? `_${lang}` : ""}`] = stringSummary
                ? byteTruncate(stringSummary, 3999)
                : null;
        }

        return await this.workflow.orm.repo("process", trx).store(proc);
    }

    /**
     * @param process
     * @param taskRepo
     * @returns {Promise.<*>}
     */
    async autostartInitialTasks(
        process: IProcess,
        taskRepo: any,
    ): Promise<any[]> {
        const procVersion = await this.wfProcess.processVersion(
            // Assert that process.IPROC_ID is not null
            process.IPROC_ID!,
        );

        const initialTasks = await taskRepo
            .getInitialTasks(process, procVersion.TTASKLINK_VERSION || 0)
            .collectAll();
        initialTasks.forEach((itask: ITask) => {
            itask.ITASK_AUTO_START = "Y";
        });
        return initialTasks;
    }

    /**
     * checkCompletion - checks all tasks for completing completion conditions

     */
    async checkCompletionProcess(
        process: IProcess,
        canFinish: boolean = true,
    ): Promise<number | boolean> {
        try {
            // check itask status and finish process
            if (canFinish) {
                globalThis.tasLogger.info(
                    "Checking whole process completition.",
                    {
                        iproc_id: process.IPROC_ID,
                    },
                );
                const status = await this.verifyStatus(process);
                if (status === PROCESS.STATUS_DONE) {
                    return await this.orm.repo("wfProcess").store(process);
                }
            }

            // Check auto completition tasks
            let repeat = true;
            while (repeat) {
                const tasks = await this.orm
                    .repo("wfTask")
                    .getTasksToComplete(process.IPROC_ID)
                    .collectAll();
                repeat = false;
                for (const ctask of tasks) {
                    await this.wfTask.checkCanceling(ctask, process);
                    const completeHash = await this.wfTask.checkCompletion(
                        ctask,
                        process,
                        canFinish,
                    );
                    // await this.wfTask.checkCompletion(ctask, process, canFinish, true);
                    if (completeHash !== false) {
                        repeat = true;
                        break;
                    }
                }
            }
            return true;
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                e,
            });
            throw e;
        }
    }

    async shredProcess(iprocId: number): Promise<void> {
        globalThis.tasLogger.info("Shredding process.", {
            iproc_id: iprocId,
        });

        // Shred files
        // Assert that this.workflow.orm is not null
        const procRepo = this.workflow.orm!.repo("process");
        const proc = await procRepo.get(iprocId, ["IPROC_ID", "TPROC_ID"]);
        const shredConfig = await this.workflow
            .orm!.repo("templateProcessShredding")
            .getForTprocId(proc.TPROC_ID);
        const shredDocuments =
            shredConfig.TPS_SHRED_DMS_FILES === templateShreddingConst.TPS_YES; // TPS_SHRED_DMS_FILES
        const deleteProcess =
            shredConfig.TPS_SHRED_PROCESS === templateShreddingConst.TPS_YES; // TPS_SHRED_PROCESS
        if (shredDocuments) {
            const dmsFileRepo = this.workflow.orm!.repo("dmsFile");
            const files: DmsFile[] = await (
                await dmsFileRepo.getForProcessOnly(iprocId)
            ).collectAll();
            for (const file of files) {
                await globalThis.tasLogger.runTask(async () => {
                    globalThis.tasLogger.setContextProperty(
                        "category",
                        LogCategory.CATEGORY_ACTOR,
                    );
                    const dmsAccessLogger = new DmsAccessLogger();
                    await dmsFileRepo.removePhysically(file.DMSF_ID);
                    try {
                        fs.unlinkSync(file.getAbsoluteFilePath());
                    } catch (err) {
                        globalThis.tasLogger.info("Cant shred dms file.", {
                            err,
                            file,
                        });
                    }

                    if (globalThis.dynamicConfig.dms.fulltext !== null) {
                        try {
                            await globalThis.container.client.elastic.elasticSearch.delete(
                                file.DMSF_ID,
                            );
                        } catch (err: any) {
                            globalThis.tasLogger.error(err.message, {
                                err,
                            });
                        }
                    }

                    await dmsAccessLogger.log(
                        file.DMSF_ID,
                        iprocId,
                        null,
                        null,
                        DmsAccessLogger.consts.DOCUMENT_SHREDED,
                    );
                });
            }
        }

        if (deleteProcess) {
            await procRepo.delete(proc);
            globalThis.tasLogger.info("Process shreded (deleted).", {
                iproc_id: iprocId,
            });
            return;
        }

        // Shred variables
        // Assert that this.workflow.orm is not null
        await this.workflow
            .orm!.connection.select()
            .from("INSTANCE_VARIABLES")
            .where("IPROC_ID", iprocId)
            .whereIn("TVAR_ID", (b: any) =>
                b
                    .select("TVAR_ID")
                    .from("TEMPLATE_VARIABLES")
                    .where("TVAR_IS_SHREDABLE", "Y"),
            )
            .update({
                IVAR_TEXT_VALUE: null,
                IVAR_DATE_VALUE: null,
                IVAR_NUMBER_VALUE: null,
                IVAR_BIG_VALUE: null,
                IVAR_MULTI_SELECTED: null,
                IVAR_COL_INDEX: null,
                IVAR_DT_INDEX: null,
            });

        // Shred snaps
        // Assert that this.workflow.orm is not null
        await this.workflow
            .orm!.connection.select()
            .from("INSTANCE_VARIABLES_SNAP")
            .whereIn("IPROC_ID", [iprocId])
            .whereIn("IVAR_ID", (b: any) =>
                b
                    .select("IVAR_ID")
                    .from("INSTANCE_VARIABLES as IV")
                    .join(
                        "TEMPLATE_VARIABLES as TV",
                        "TV.TVAR_ID",
                        "IV.TVAR_ID",
                    )
                    .where("TVAR_IS_SHREDABLE", "Y"),
            )
            .update({
                IVARSN_TEXT_VALUE: null,
                IVARSN_NUMBER_VALUE: null,
                IVARSN_DATE_VALUE: null,
                IVARSN_TEXT_VALUE_PREV: null,
                IVARSN_NUMBER_VALUE_PREV: null,
                IVARSN_DATE_VALUE_PREV: null,
                IVARSN_MULTI_SELECTED: null,
                IVARSN_BIG_VALUE: null,
                IVAR_DT_INDEX: null,
                IVAR_COL_INDEX: null,
            });

        // Shred history
        // Assert that this.workflow.orm is not null
        await this.workflow
            .orm!.connection.select()
            .from("INSTANCE_VARIABLE_HISTORY")
            .whereIn("IVAR_ID", (b: any) =>
                b
                    .select("IVAR_ID")
                    .from("INSTANCE_VARIABLES as IV")
                    .join(
                        "TEMPLATE_VARIABLES as TV",
                        "TV.TVAR_ID",
                        "IV.TVAR_ID",
                    )
                    .where("IV.IPROC_ID", iprocId)
                    .where("TVAR_IS_SHREDABLE", "Y"),
            )
            .update({
                IVARH_TEXT_VALUE: null,
                IVARH_NUMBER_VALUE: null,
                IVARH_DATE_VALUE: null,
                IVARH_MULTI: null,
                IVARH_MULTI_SELECTED: null,
                IVARH_BIG_VALUE: null,
            });

        proc.IPROC_SHREDDED = PROCESS.SHREDDED;
        await procRepo.store(proc);
    }

    async checkProcessDueDateSchedule(process: IProcess): Promise<any> {
        globalThis.tasLogger.info("Checking whole process due date schedule.", {
            iproc_id: process.IPROC_ID,
        });
        const tasks: ITask[] = await this.orm
            .repo("wfTask")
            .getTasksToDueDateSchedule(process.IPROC_ID)
            .collectAll();
        if (tasks.length > 0) {
            for (const task of tasks) {
                // Assert that this.workflow.orm is not null
                const cachedTask = this.workflow.taskList![task.ITASK_ID!];
                await this.wfTask.checkTaskDueDateSchedule(
                    cachedTask || task,
                    process,
                );
            }
        }
    }

    async getTproc(tprocId: number): Promise<ITemplateProcess> {
        // Assert that this.workflow.orm is not null
        const tprocRepo = this.workflow.orm!.repo("templateProcess");
        const tproc = await tprocRepo.getById(tprocId).collectOne();

        return tproc;
    }
}
