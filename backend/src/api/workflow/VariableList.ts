import * as VARIABLE from "../orm/entity/const/variableConst";

import _ from "lodash";
import { Variable } from "../orm/entity/Variable";
import { VariableLov } from "../orm/entity/VariableLov";
import { VariableLovRepository } from "../orm/repository/VariableLovRepository";
import { VariableUse } from "./entity/VariableUse";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";

import { Loggable } from "../../utils/logger/Loggable";

interface IVariableListAttributes {
    variables: Variable[] | null;
    usages: any | null;
    variablesMap: any | null;
    multiInstances: any | null;
}

interface IVariableListMethods {
    getVariables(): Variable[];
    setVariables(variables: Variable[], lovs?: VariableLov[]): void;
    setVariablesLovs(variables: Variable[], lovs: VariableLov[]): Variable[];
    setVariableUsages(usages: any[]): void;
    getUsage(task: any): any[];
    exportAssoc(
        attrKey: string,
        attrValue: string,
        errOnDuplicate: boolean,
    ): Promise<any>;
    getBy(attrName: string, value: any): Variable;
    getListForMultiinstance(task: any): any;
    getDynamicLists(
        filteredTypes: string[],
        returnPrimitiveValues: boolean,
        dateFormat: string,
    ): Promise<any>;
    exportPrintWithLangValues(
        safe: boolean,
        lovMutations: any,
        dateFormat: string,
    ): Promise<any>;
    toLogObject(): any;
}

export interface IVariableList
    extends IVariableListAttributes,
        IVariableListMethods {}

/**
 * List of variables for caching and other purposes.
 * Needs to setVariables and setVariableUsages to work.
 */
export class VariableList extends Loggable implements IVariableList {
    variables: Variable[];

    usages: any;

    variablesMap: any;

    multiInstances: any;

    constructor() {
        super();

        this.variables = [];
        this.usages = {};
        this.variablesMap = {};
        this.multiInstances = {}; // store varibleLists for multiinstances
    }

    /**
     * Simply returns all variables
     */
    getVariables() {
        return this.variables;
    }

    setVariables(variables: Variable[], lovs?: VariableLov[]): void {
        this.variables = variables;

        // caching to map by IVAR_ID to fast resolve
        for (const v of variables) {
            if (v.IVAR_ID) {
                this.variablesMap[v.IVAR_ID] = v;
            }
        }

        this.setVariablesLovs(this.variables, lovs);
    }

    /**
     * Join lovs with variables.
     */
    setVariablesLovs(
        variables: Variable[],
        lovs: VariableLov[] | [] = [],
    ): any[] {
        return VariableLovRepository.joinLov(
            variables,
            Array.isArray(lovs) ? _.groupBy(lovs, "IVAR_ID") : lovs,
        );
    }

    /**
     * Set usages for all tasks. Preserves order of usage in tasks.
     * usagesByTasks Needs Simple array of TEMPLATE_TASK_VAR_USAGE
     */
    async setVariableUsages(usages: Record<string, any>[]): Promise<void> {
        const tvarMap = await this.exportAssoc("TVAR_ID");

        for (const taskUse of usages) {
            if (typeof tvarMap[taskUse.TVAR_ID] === "undefined") {
                throw new InternalException(
                    `Undefined ${taskUse.TVAR_ID} variable in usage to map.`,
                );
            }
            const variableUse = new VariableUse(tvarMap[taskUse.TVAR_ID]);
            variableUse.setUsage(taskUse.TTASKVARUSG_USAGE);
            // creates new Array
            if (typeof this.usages[taskUse.TTASK_ID] === "undefined") {
                this.usages[taskUse.TTASK_ID] = [];
            }
            this.usages[taskUse.TTASK_ID].push(variableUse); // group by ITASK_ID
        }
    }

    getUsage(task: any): any[] {
        if (this.usages === null) {
            throw new InternalException("Task usages was not loaded.");
        }

        return typeof this.usages[task.TTASK_ID] !== "undefined"
            ? this.usages[task.TTASK_ID]
            : [];
    }

    /**
     * Exports variables as object with value of given attribute as key(property) and given argument for value.
     * attrKey Attribute Default as variable name.
     * attrValue Attribute to retrieve. Default is whole entity (null).
     */
    async exportAssoc(
        attrKey: string = "IVAR_NAME",
        attrValue: any = null,
        errOnDuplicate: boolean = true,
    ): Promise<any> {
        const exportData: any = {};

        for (const ent of this.variables) {
            if (exportData[ent[attrKey]]) {
                globalThis.tasLogger.error(`Duplicate of key ${attrKey}.`, {
                    ent,
                });
            }

            if (attrValue === null) {
                exportData[ent[attrKey]] = ent;
            } else if (attrValue === "getWithLovValue") {
                // Or any other (typeof) function ... TODO
                exportData[ent[attrKey]] = await ent.getWithLovValue();
            } else {
                exportData[ent[attrKey]] = ent[attrValue];
            }
        }
        if (
            errOnDuplicate &&
            Object.keys(exportData).length !== this.variables.length
        ) {
            throw new InternalException(`Duplicate of key ${attrKey}.`);
        }
        return exportData;
    }

    /**
     * Find and return object by attribute name and value.
     * Uses cache if used IVAR_ID.
     */
    getBy(attrName: string, value: any): any {
        // use cache
        if (
            attrName === "IVAR_ID" &&
            Object.prototype.hasOwnProperty.call(this.variablesMap, value)
        ) {
            return this.variablesMap[value];
        }

        const find = _.find(
            this.variables,
            (ivar: any) => ivar[attrName] == value,
        ); // be aware of number type

        if (find) {
            return find;
        }

        throw new InternalException(
            `Element with attribute name: ${attrName} and value: ${value} not found.`,
        );
    }

    getListForMultiinstance(task: any) {
        if (typeof this.multiInstances[task.ITASK_ID] !== "undefined") {
            return this.multiInstances[task.ITASK_ID];
        }
        this.multiInstances[task.ITASK_ID] = new VariableList();
    }

    /**
     * Return local Variables of DynamicList type with the required attribute(s)
     */
    async getDynamicLists(
        filteredTypes: string[] = [],
        returnPrimitiveValues: boolean = false,
        dateFormat: string = "L",
    ): Promise<any> {
        // Prepare the desired lists by ATTR
        const dlu = this.getVariables().filter(
            (item: any) =>
                (item.IVAR_TYPE === VARIABLE.TYPE_DYNAMIC_LIST &&
                    (!filteredTypes || !filteredTypes.length)) ||
                filteredTypes.includes(item.IVAR_ATTRIBUTE),
        );

        let dluValues;
        const result: any = {};

        // Return primitive or display value?
        if (returnPrimitiveValues) {
            dluValues = dlu.map((item) => item.value);
        } else {
            dluValues = await Promise.all(
                dlu.map(async (item) => await item.getPrintValue(dateFormat)),
            );
        }

        // Prepare object
        dlu.forEach((item: any, index) => {
            result[item.IVAR_NAME] = dluValues[index];
        });

        return result;
    }

    /**
     * Export associative list of variables with lang mutations.
     *

     */
    async exportPrintWithLangValues(
        safe: boolean = false,
        lovMutations: any,
        dateFormat: string = "L",
    ): Promise<any> {
        const vars: any = {};

        for (const variable of this.getVariables()) {
            // Detect and add LANGUAGE mutations
            if (variable.IVAR_TYPE === VARIABLE.TYPE_TEXT_LIST) {
                vars[`${variable.IVAR_NAME}_default`] = safe
                    ? await variable.getPrintSafeValue(dateFormat)
                    : await variable.getPrintValue(dateFormat);

                globalThis.dynamicConfig.langs.forEach((language: any) => {
                    const tLLangValues: any = [];
                    let defaultValues = [];
                    if (variable.isMultichoice()) {
                        defaultValues = JSON.parse(
                            vars[`${variable.IVAR_NAME}_default`],
                        );
                    } else {
                        defaultValues = [vars[`${variable.IVAR_NAME}_default`]];
                    }

                    defaultValues.forEach((defaultValue: any) => {
                        const row = _.find(
                            lovMutations,
                            (lovMutation: any) =>
                                // lovMutations (text list items) with same default values may not be picked correctly
                                // since we pick the corresponding LOV mutation by comparing the LOV default value (IVARLOV_TEXT_VALUE)
                                // with the default values from vars[`${variable.IVAR_NAME}_default`]
                                // TODO: if there's a way of distinguishing multichoice text list items of same values, it should be implemeted here as well
                                lovMutation.IVARLOV_TEXT_VALUE ===
                                    defaultValue &&
                                lovMutation.IVAR_ID &&
                                lovMutation.IVAR_ID === variable.IVAR_ID &&
                                lovMutation[
                                    `IVARLOV_TEXT_VALUE_${language.toUpperCase()}`
                                ],
                        );

                        if (row) {
                            tLLangValues.push(
                                row[
                                    `IVARLOV_TEXT_VALUE_${language.toUpperCase()}`
                                ],
                            );
                        }
                    });

                    if (tLLangValues.length > 0) {
                        if (variable.isMultichoice()) {
                            vars[
                                `${variable.IVAR_NAME}_${language.toUpperCase()}`
                            ] = safe ? tLLangValues.join(", ") : tLLangValues;
                        } else {
                            vars[
                                `${variable.IVAR_NAME}_${language.toUpperCase()}`
                            ] = tLLangValues[0];
                        }
                    } else {
                        vars[
                            `${variable.IVAR_NAME}_${language.toUpperCase()}`
                        ] = null;
                    }
                });
            }

            vars[variable.IVAR_NAME] = safe
                ? await variable.getPrintSafeValue(dateFormat)
                : await variable.getPrintValue(dateFormat);
        }
        return vars;
    }

    toLogObject(): any {
        return "alreadyLogged";
    }
}
