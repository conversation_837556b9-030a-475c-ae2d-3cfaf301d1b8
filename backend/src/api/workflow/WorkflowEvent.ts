import _ from "lodash";
import moment from "moment";
import { ITask, Task } from "../orm/entity/Task";
import { Event, IEvent } from "../orm/entity/Event";
import { IProcess, Process } from "../orm/entity/Process";
import * as taskConsts from "../orm/entity/const/taskConst";
import * as eventConsts from "../orm/entity/const/eventConst";
import * as ruleDefinitionConsts from "../orm/entity/const/ruleDefinitionConst";
import * as calcConsts from "../orm/entity/const/calcConsts";
import * as variableConsts from "../orm/entity/const/variableConst";
import { CsvIterator } from "../utils/CsvIterator";
import { CsvWriter } from "../utils/CsvWriter";
import { EventHandlerMan } from "../events/eventHandlerMan";
import { WorkflowBase } from "./WorkflowBase";
import { IVariable, Variable } from "../orm/entity/Variable";
import { Workflow } from "./Workflow";
import { IVariableList } from "./VariableList";
import { IVariableSnap } from "../orm/entity/VariableSnap";
import { IRuleDefinition, RuleDefinition } from "../orm/entity/RuleDefinition";
import { IEventParam, IEventParamAttributes } from "../orm/entity/EventParam";
import { IRuleDefinitionParam } from "../orm/entity/RuleDefinitionParam";
import { ITemplateVariable } from "../orm/entity/TemplateVariable";
import { User } from "../orm/entity/User";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { IVariableApi } from "../../entrypoint/calculation/IVariableApi";
import { TaskJsCalculations } from "../../infrastructure/calculation/TaskJsCalculations";
import { ProcessJsCalculations } from "../../infrastructure/calculation/ProcessJsCalculations";
import { LogCategory } from "../../utils/logger/logConsts";

interface ExtendedIEventParamAttributes extends Partial<IEventParamAttributes> {
    IVAR_TEXT_VALUE?: string;
}

interface IWorkflowEventMethods {
    activateEventTask(
        process: IProcess,
        task: ITask,
        variableList: IVariableList,
        taskVariableSnaps: IVariableSnap[],
    ): Promise<any>;
    createIEventFromITask(process: IProcess, task: ITask): Promise<IEvent>;
    createIParamsFromITask(event: IEvent, task: ITask): Promise<void>;
    createIEvent(
        process: IProcess,
        evedefName: string,
        sidOrig: string,
        orgGlobalIdOrig?: string,
        sidAnswer?: string,
        orgGlobalIdAnswer?: string,
        eveUserId?: number | null,
    ): Promise<IEvent>;
    createIRulesFromEvent(event: IEvent): Promise<void>;
    _addParamsFromProcess(event: IEvent, process: IProcess): Promise<any>;
    activateIEvent(
        process: IProcess,
        task: ITask,
        event: IEvent,
        processVariables: Variable[],
    ): Promise<any>;
    allRulesProcessed(event: IEvent): Promise<boolean>;
    activateRulesByIEvent(
        process: IProcess,
        task: ITask,
        event: IEvent,
        processVariables: Variable[],
    ): Promise<any>;
    activateRule(
        event: IEvent,
        mainProcess: IProcess,
        mainTask: ITask,
        ruleDefinition: IRuleDefinition,
        eveParams: IEventParam[],
        processVariables: Variable[],
    ): Promise<any>;
    runProcessCsvExport(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean>;
    runProcessIRuleEventWait(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParams: IEventParam[],
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void>;
    returnMappingFromSubProcess(
        subProcess: IProcess,
        subEvent: IEvent,
        subVarList: IVariableList,
    ): Promise<void>;
    runCsvProcess(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void>;
    runCsvMergeProcess(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
        createMissingProcesses: boolean,
    ): Promise<any>;
    runCsvUpdateProcess(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean>;
    updateProcessVariablesFromCsvRow(
        process: IProcess,
        ruleDefinition: IRuleDefinition,
        csvRow: Record<string, any>,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void>;
    runProcessUpdateDynamicList(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean>;
    runCsvUpdateDynamiList(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean>;
    runProcessRule(
        mainProcess: IProcess,
        mainTask: any,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParams: IEventParam[],
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void>;
    getMapVariables(
        process: IProcess,
        task: ITask,
        eVars: Record<string, any>[],
        pars: Record<string, any>[],
    ): Promise<Record<string, any>[]>;
    setIProcessVariables(
        mainProc: IProcess,
        subProc: IProcess,
        vars: IVariable[],
    ): Promise<boolean | (true | null | Record<string, any>)[]>;
    handEventList(
        process: IProcess,
        onlyButtons: boolean,
    ): Promise<Record<string, any>[]>;
}

export interface IWorkflowEvent extends IWorkflowEventMethods {}

/**
 * Event workflow.
 * Use this class to activate/process events.
 *
 * <AUTHOR> Fuka
 *
 */
export class WorkflowEvent extends WorkflowBase implements IWorkflowEvent {
    // TASK/PROCESS FLOW
    // --------------------------------------------------------------------------------------------------------------
    // --------------------------------------------------------------------------------------------------------------
    // --------------------------------------------------------------------------------------------------------------

    /**
     *  Activate event task and process all rules.
     * @param process
     * @param task
     * @param {VariableList} variableList
     * @param taskVariableSnaps
     * @returns {Promise.<void>}
     */
    async activateEventTask(
        process: IProcess,
        task: ITask,
        variableList: IVariableList,
        taskVariableSnaps: IVariableSnap[],
    ): Promise<void> {
        return globalThis.tasLogger.runTask(async (): Promise<void> => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_EVENTS,
            );
            if (!(task instanceof Task)) {
                throw new InternalException(
                    "Invalid argument type. Task must be instanceof Task entity.",
                    { task },
                );
            }
            if (!(process instanceof Process)) {
                throw new InternalException(
                    "Invalid argument type. Process must be instanceof Process entity.",
                    { process },
                );
            }
            this.log.info(`Executing event '${task.ITASK_EVENT}'`, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
            });

            // Task must be E or P -> Event or SubProcess
            if (
                task.ITASK_TYPE !== taskConsts.TYPE_EVENT &&
                task.ITASK_TYPE !== taskConsts.TYPE_SUBPROCESS
            ) {
                throw new InternalException(
                    `Task is not event. Task type should be ${taskConsts.TYPE_EVENT} but is ${task.ITASK_TYPE}.`,
                    "TASK_NOT_EVENT",
                    { task },
                );
            }

            // Evaluate calculations.

            const calcs = new TaskJsCalculations(
                this.orm,
                globalThis.tasLogger,
            );
            calcs.setVariables(variableList.getVariables());
            calcs.setSnapshots(taskVariableSnaps);
            calcs.setWorkflow(this.workflow);
            await calcs.evaluateTas2Calculations(
                this.currentUser,
                task,
                process,
                calcConsts.EXEC_ON_START,
                (await this.wfProcess.processVersion(process))
                    .TTJSCALC_VERSION || 0,
            );

            const event = await this.createIEventFromITask(process, task);

            // Event is not processes in background.
            if (
                task.ITASK_INVOKE_EVENT === eventConsts.INVOKE_EVENT_IMMEDIATE
            ) {
                return await this.activateIEvent(
                    process,
                    task,
                    event,
                    variableList.getVariables(),
                );
            }
        });
    }

    // EVENT FLOW
    // --------------------------------------------------------------------------------------------------------------
    // --------------------------------------------------------------------------------------------------------------
    // --------------------------------------------------------------------------------------------------------------

    /**
     * createIEventFromITask - creates new event from task, sets up variables assigned to task
     *
     */
    async createIEventFromITask(
        process: IProcess,
        task: ITask,
    ): Promise<IEvent> {
        if (!(task instanceof Task)) {
            throw new InternalException(
                "Invalid argument type. Task must be instanceof Task entity.",
                { task },
            );
        }
        if (!(process instanceof Process)) {
            throw new InternalException(
                "Invalid argument type. Process must be instanceof Process entity.",
                { process },
            );
        }

        const sid = `TAS_${task.IPROC_ID}`;
        const event = await this.createIEvent(
            process,
            task.ITASK_EVENT,
            sid,
            "tas3",
            "",
            -1,
            task.ITASK_USER_ID,
            task.ITASK_INVOKE_EVENT === eventConsts.INVOKE_EVENT_BACKGROUND
                ? eventConsts.STATUS_READY_BACKGROUND
                : undefined,
        );
        await this.createIParamsFromITask(event, task);
        return event;
    }

    /**
     *  Setup EVENT_PARAMS from task entity and task variables.
     * @param {Event} event
     * @param {Task} task
     * @returns {Promise<any[]>}
     */
    async createIParamsFromITask(event: IEvent, task: ITask): Promise<void> {
        if (!(event instanceof Event)) {
            throw new InternalException(
                "Invalid argument type. Event must be instance of Event.",
                { event },
            );
        }
        if (!(task instanceof Task)) {
            throw new InternalException(
                "Invalid argument type. task must be instanceof Task entity.",
                { task },
            );
        }

        const eveParamRepo = this.orm.repo("eventParam");

        /* ITASK_USER_ID param */
        const param1 = eveParamRepo.getEntity({
            EVE_ID: event.EVE_ID,
            EVEPAR_NAME: "$ITASK_USER_ID",
            EVEPAR_TYPE: variableConsts.TYPE_NUMBER,
            EVEPAR_NUMBER_VALUE: task.ITASK_USER_ID,
        });
        await eveParamRepo.store(param1);

        /* ITASK_ID param */
        const param2 = eveParamRepo.getEntity({
            EVE_ID: event.EVE_ID,
            EVEPAR_NAME: "$ITASK_ID",
            EVEPAR_TYPE: variableConsts.TYPE_NUMBER,
            EVEPAR_NUMBER_VALUE: task.ITASK_ID,
        });
        await eveParamRepo.store(param2);

        /* ORGANIZATION_ID param */
        const param3 = eveParamRepo.getEntity({
            EVE_ID: event.EVE_ID,
            EVEPAR_NAME: "$ITASK_ORGSTR_ID",
            EVEPAR_TYPE: variableConsts.TYPE_NUMBER,
            //@ts-expect-error types
            EVEPAR_NUMBER_VALUE: this.currentUser.ORGANIZATION.ORGSTR_ID,
        });
        await eveParamRepo.store(param3);

        /* ITASK_TYPE param */
        const param4 = eveParamRepo.getEntity({
            EVE_ID: event.EVE_ID,
            EVEPAR_NAME: "$ITASK_TYPE",
            EVEPAR_TYPE: variableConsts.TYPE_TEXT,
            EVEPAR_TEXT_VALUE: task.ITASK_TYPE,
        });
        await eveParamRepo.store(param4);

        const procVersion = await this.wfProcess.processVersion(task.IPROC_ID);
        // Add all task variables and rule variables from mapping.
        const vars = await this.orm.connection
            .select("VAR.IVAR_ID")
            .from("INSTANCE_VARIABLES as VAR")
            .where("VAR.IPROC_ID", task.IPROC_ID)
            .whereIn("VAR.TVAR_ID", function (t: any) {
                t.select("IVAR_ID")
                    .from("TEMPLATE_TASK_VAR_USAGE")
                    .where(
                        "TTASKVARUSG_VERSION",
                        procVersion.TTASKVARUSG_VERSION || 1,
                    )
                    .where("TTASK_ID", task.TTASK_ID);
            })
            .union(function (t: any) {
                t.select("VAR.IVAR_ID")
                    .from("INSTANCE_TASKS AS ITASK")
                    .join(
                        "EVENT_DEFINITION as EDEF",
                        "EDEF.EVEDEF_NAME",
                        "ITASK.ITASK_EVENT",
                    )
                    .join(
                        "RULE_DEFINITION as RDEF",
                        "RDEF.EVEDEF_ID",
                        "EDEF.EVEDEF_ID",
                    )
                    .join(
                        "RULE_DEFINITION_PARAM as RPAR",
                        "RPAR.RDEF_ID",
                        "RDEF.RDEF_ID",
                    )
                    .join("INSTANCE_VARIABLES as VAR", function (t: any) {
                        t.on("VAR.TVAR_ID", "RPAR.TVAR_ID").on(
                            "VAR.IPROC_ID",
                            "ITASK.IPROC_ID",
                        );
                    })
                    .where("ITASK_ID", task.ITASK_ID);
            });

        // Store all searched variables into params.
        for (const variable of vars) {
            const procVar = this.variableList.getBy(
                "IVAR_ID",
                variable.IVAR_ID,
            );
            const param = eveParamRepo.getEntity({
                EVE_ID: event.EVE_ID,
                TVAR_ID: procVar.TVAR_ID,
                EVEPAR_NAME: procVar.IVAR_NAME,
                EVEPAR_TYPE: procVar.IVAR_TYPE,
                EVEPAR_MULTITASK_BEHAVIOUR: procVar.IVAR_MULTITASK_BEHAVIOUR,
                EVEPAR_TEXT_VALUE: procVar.IVAR_TEXT_VALUE,
                EVEPAR_NUMBER_VALUE: procVar.IVAR_NUMBER_VALUE,
                EVEPAR_DATE_VALUE: procVar.IVAR_DATE_VALUE,
                EVEPAR_ATTRIBUTE: procVar.IVAR_ATTRIBUTE,
                EVEPAR_MULTI: procVar.IVAR_MULTI,
                EVEPAR_BIG_VALUE: procVar.IVAR_BIG_VALUE,
                EVEPAR_MULTI_SELECTED: procVar.IVAR_MULTI_SELECTED,
                EVEPAR_DT_INDEX: procVar.IVAR_DT_INDEX,
            });
            await eveParamRepo.store(param);
        }
    }

    /**
     *
     * @param {Process} process
     * @param {string} evedefName
     * @param {string} sidOrig
     * @param {number} orgGlobalIdOrig
     * @param {string} sidAnswer
     * @param {string} orgGlobalIdAnswer
     * @param {number} eveUserId
     * @returns {Promise.<Event>}
     */
    async createIEvent(
        process: IProcess,
        evedefName: string,
        sidOrig: string,
        orgGlobalIdOrig: string = "",
        sidAnswer: string = "",
        orgGlobalIdAnswer: string | number = "",
        eveUserId: number | null = null,
        eventStatus: string = eventConsts.STATUS_READY_IMMEDIATE,
    ): Promise<IEvent> {
        if (!eveUserId) {
            // Assert that USER_ID is set.
            eveUserId = this.currentUser.USER_ID!;
        }

        // Create event entity
        const eveRepo = this.orm.repo("event");
        const eventEntity = eveRepo.getEntity({
            EVEDEF_NAME: evedefName,
            EVE_STATUS: eventStatus,
            ORG_GLOBAL_ID_ORIG: orgGlobalIdOrig,
            SID_ORIG: sidOrig,
            ORG_GLOBAL_ID_ANSWER: orgGlobalIdAnswer,
            SID_ANSWER: sidAnswer,
            EVE_DATETIME: new Date(),
            EVE_USER_ID: eveUserId,
        });
        await eveRepo.store(eventEntity);
        // Create rules for event
        await this.createIRulesFromEvent(eventEntity);

        // najdu promenne procesu a pridam je k evente
        await this._addParamsFromProcess(eventEntity, process);

        return eventEntity;
    }

    /**
     *  Copy rule for event.
     * @param {Event} event
     * @returns {Promise.<*>}
     */
    async createIRulesFromEvent(event: IEvent): Promise<void> {
        if (!(event instanceof Event)) {
            throw new InternalException(
                "Invalid argument type. Event must be instance of Event.",
                { event },
            );
        }

        const ruleDefRepo = this.orm.repo("ruleDefinition");
        const rules = await ruleDefRepo.getByEveDefName(
            event.EVEDEF_NAME,
            ruleDefinitionConsts.TRULE_STATUS_ACTIVE,
        );

        const ruleRepo = this.orm.repo("rule");
        for (const rule of rules) {
            const ruleEntity = ruleRepo.getEntity({
                EVE_ID: event.EVE_ID,
                EVEDEF_ID: rule.EVEDEF_ID,
                RDEF_ID: rule.RDEF_ID,
                RULE_STATUS: "SET",
            });
            await ruleRepo.store(ruleEntity);
        }
    }

    async _addParamsFromProcess(
        event: IEvent,
        process: IProcess,
    ): Promise<any> {
        //Asserted value for process.IPROC_ID
        if (!process || isNaN(process.IPROC_ID!)) {
            return;
        }
        if (!(event instanceof Event)) {
            throw new InternalException(
                "Invalid argument type. Event must be instance of Event.",
                { event },
            );
        }

        const userRepo = this.orm.repo("user");
        const user = await userRepo.get(process.IPROC_INST_OWNER_USER_ID);

        const procPars = [
            {
                EVEPAR_NAME: "$PROCESS_NAME",
                EVEPAR_TYPE: variableConsts.TYPE_TEXT,
                EVEPAR_TEXT_VALUE: process.IPROC_NAME,
            },
            {
                EVEPAR_NAME: "$PROCESS_OWNER",
                EVEPAR_TYPE: variableConsts.TYPE_TEXT,
                EVEPAR_TEXT_VALUE: user.USER_DISPLAY_NAME,
            },
            {
                EVEPAR_NAME: "$PROCESS_START",
                EVEPAR_TYPE: variableConsts.TYPE_DATE,
                EVEPAR_TEXT_VALUE: process.IPROC_ACTUAL_START_DATE,
            },
            {
                EVEPAR_NAME: "$PRIORITY",
                EVEPAR_TYPE: variableConsts.TYPE_DATE,
                EVEPAR_TEXT_VALUE: process.IPROC_PRIORITY,
            },
            {
                EVEPAR_NAME: "$TERMIN",
                EVEPAR_TYPE: variableConsts.TYPE_TEXT,
                EVEPAR_TEXT_VALUE: process.IPROC_DUE_DATE_FINISH,
            },
            {
                EVEPAR_NAME: "$IPROC_USER_ID",
                EVEPAR_TYPE: variableConsts.TYPE_NUMBER,
                EVEPAR_NUMBER_VALUE: process.IPROC_INST_OWNER_USER_ID,
            },
        ];

        const eventParamRepo = this.orm.repo("eventParam");
        const eveParRes = [];

        for (const param of procPars) {
            const entity = eventParamRepo.getEntity({
                EVE_ID: event.EVE_ID,
                EVEPAR_TYPE: param.EVEPAR_TYPE,
                EVEPAR_NAME: param.EVEPAR_NAME,
                EVEPAR_TEXT_VALUE: param.EVEPAR_TEXT_VALUE ?? null,
                EVEPAR_NUMBER_VALUE: (param as any).EVEPAR_NUMBER_VALUE ?? null,
                EVEPAR_DATE_VALUE: (param as any).EVEPAR_DATE_VALUE ?? null,
                EVEPAR_DT_INDEX: (param as any).EVEPAR_DT_INDEX ?? null,
            });
            eveParRes.push(await eventParamRepo.store(entity));
        }
        return eveParRes;
    }

    /**
     *  Activate event and process all rules.
     * @param process
     * @param task
     * @param process
     * @param processVariables
     * @param task
     * @param {Event} event
     * @returns {Promise.<void>}
     */
    async activateIEvent(
        process: IProcess,
        task: ITask,
        event: IEvent,
        processVariables: Variable[],
    ): Promise<void> {
        if (!(event instanceof Event)) {
            throw new InternalException(
                "Invalid argument type. Event must be instance of Event.",
                { event },
            );
        }
        if (!(process instanceof Process)) {
            throw new InternalException(
                "Invalid argument type. Process must be instanceof Process entity.",
                { process },
            );
        }

        const eveRepo = this.orm.repo("event");
        if (
            event.EVE_STATUS !== eventConsts.STATUS_READY_BACKGROUND &&
            event.EVE_STATUS !== eventConsts.STATUS_READY_IMMEDIATE
        ) {
            globalThis.tasLogger.info(
                `Event '${event.EVE_ID}' is not SET. Skipping activation.`,
                {
                    iproc_id: process.IPROC_ID,
                    itask_id: task.ITASK_ID,
                },
            );
            return;
        }

        // Process all rules from event.
        const result = await this.activateRulesByIEvent(
            process,
            task,
            event,
            processVariables,
        );

        if (await this.allRulesProcessed(event)) {
            event.EVE_STATUS = eventConsts.STATUS_PROCESSED;
            await eveRepo.store(event);
        }
        return result;
    }

    /**
     * Check if all rules are processed.
     * @param {Event} event
     * @returns {*}
     */
    allRulesProcessed(event: IEvent): Promise<boolean> {
        if (!(event instanceof Event)) {
            throw new InternalException(
                "Invalid argument type. Event must be instance of Event.",
                { event },
            );
        }

        const ruleRepo = this.orm.repo("rule");
        return ruleRepo.allRulesProcessed(event);
    }

    async activateRulesByIEvent(
        process: IProcess,
        task: ITask,
        event: IEvent,
        processVariables: Variable[],
    ): Promise<void> {
        if (!(event instanceof Event)) {
            throw new InternalException(
                "Invalid argument type. Event must be instance of Event.",
                { event },
            );
        }
        if (!(process instanceof Process)) {
            throw new InternalException(
                "Invalid argument type. Process must be instanceof Process entity.",
                { process },
            );
        }

        // Get event params
        const eveParamRepo = this.orm.repo("eventParam");
        const eveParams = await eveParamRepo
            .getForEvent(event.EVE_ID)
            .collectAll();

        // Find event process. TODO process must be pass as parameter
        // let proc = {};
        // if (event.SID_ORIG && typeof event.SID_ORIG === 'string') {
        //     const iprocId = Number(event.SID_ORIG.substring(4)); // Gte iprocid from sid like: TAS_1122
        //     const procRepo = this.orm.repo('Process');
        //     proc = await procRepo.get(iprocId);
        // }

        // Activate all rules.
        const ruleDefRepo = this.orm.repo("ruleDefinition");
        const rules = await ruleDefRepo.getForEvent(event.EVE_ID).collectAll();

        if (Array.isArray(rules) && rules.length > 0) {
            for (const ruleDefinition of rules) {
                if (ruleDefinition.isActive()) {
                    try {
                        await this.activateRule(
                            event,
                            process,
                            task,
                            ruleDefinition,
                            eveParams,
                            processVariables,
                        );
                    } catch (err: any) {
                        globalThis.tasLogger.error(
                            `Activating rule error: ${err.message}`,
                            {
                                iproc_id: process.IPROC_ID,
                                err,
                                event,
                                ruleDefinition,
                            },
                        );
                        throw err;
                    }
                }
            }
        }
    }

    /**
     *
     * @param {Event} event
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {RuleDefinition} ruleDefinition RuleDefinition entity
     * @param {Array<EventParam>} eveParams EventParam array.
     * @param processVariables
     * @returns {Promise.<void>}
     */
    async activateRule(
        event: Event,
        mainProcess: IProcess,
        mainTask: ITask,
        ruleDefinition: RuleDefinition,
        eveParams: IEventParam[],
        processVariables: Variable[],
    ): Promise<any> {
        if (!(mainTask instanceof Task)) {
            throw new InternalException(
                "Invalid argument type. Task must be instanceof Task entity.",
                { task: mainTask },
            );
        }
        if (!(mainProcess instanceof Process)) {
            throw new InternalException(
                "Invalid argument type. Process must be instanceof Process entity.",
                { process: mainProcess },
            );
        }

        /**
         * SET/READ RULE_PARAMS/RULE_VARIABLES --- SET/READ RULE_PARAMS/RULE_VARIABLES --- SET/READ RULE_PARAMS/RULE_VARIABLES
         * SET/READ RULE_PARAMS/RULE_VARIABLES --- SET/READ RULE_PARAMS/RULE_VARIABLES --- SET/READ RULE_PARAMS/RULE_VARIABLES
         * SET/READ RULE_PARAMS/RULE_VARIABLES --- SET/READ RULE_PARAMS/RULE_VARIABLES --- SET/READ RULE_PARAMS/RULE_VARIABLES
         */

        // Create eventparams map
        const eveParamsMap: { [key: string]: any } = {}; // Define the type of eveParamsMap
        for (const param of eveParams) {
            eveParamsMap[param.EVEPAR_NAME as keyof typeof eveParamsMap] =
                param.value; // Use typeof to refer to the type of eveParamsMap

            // Add tvar_id key value.
            if (param.TVAR_ID) {
                eveParamsMap[param.TVAR_ID] =
                    eveParamsMap[
                        param.EVEPAR_NAME as keyof typeof eveParamsMap
                    ];
            }
        }

        // Get ruleDefinition params (mapping definition for event)
        const ruleDefParamRepo = this.orm.repo("ruleDefinitionParam");
        const ruleParams = await ruleDefParamRepo
            .getForRuleDefinition(ruleDefinition.RDEF_ID)
            .collectAll(); // Rule params: mapping from subprocess, etc.
        // Get ruleDefinition variables
        const ruleVarParamRepo = this.orm.repo("ruleDefinitionVariable");
        const ruleVars = await ruleVarParamRepo
            .getForRuleDefinition(ruleDefinition.RDEF_ID)
            .collectAll(); // Rule variables e.g. CSV headers, Soap host, etc.
        const ruleVarsMap: { [key: string]: any } = {};
        if (Array.isArray(ruleVars) && ruleVars.length > 0) {
            ruleVars.forEach((ruleVar) => {
                ruleVarsMap[ruleVar.RDEFVAR_NAME] =
                    // @ts-expect-error missing typing
                    globalThis.lang.replaceTemplates(
                        ruleVar.RDEFVAR_VALUE,
                        eveParamsMap,
                    ); // Replace {name} with real params.
            });
        }

        /**
         * RUN RULE --RUN RULE --RUN RULE --RUN RULE --RUN RULE --RUN RULE
         * RUN RULE --RUN RULE --RUN RULE --RUN RULE --RUN RULE --RUN RULE
         * RUN RULE --RUN RULE --RUN RULE --RUN RULE --RUN RULE --RUN RULE
         */

        // Event run another process or subprocess.
        globalThis.tasLogger.info(
            `Processing rule: RDEF_ID=${ruleDefinition.RDEF_ID} RDEF_TYPE='${ruleDefinition.RDEF_TYPE}'`,
            {
                iproc_id: mainProcess.IPROC_ID,
                itask_id: mainTask.ITASK_ID,
            },
        );
        let result;
        switch (ruleDefinition.RDEF_TYPE) {
            case ruleDefinitionConsts.TRULE_TYPE_PROCESS:
                result = await this.runProcessRule(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParams,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_CSV_RUN_PROCESSES:
                result = await this.runCsvProcess(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_CSV_UPDATE_PROCESS:
            case ruleDefinitionConsts.TRULE_TYPE_CSV_UPDATE_PROCESSES:
                result = await this.runCsvMergeProcess(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                    false,
                );
                break;
            case ruleDefinitionConsts.CSV_MERGE_PROCESSES:
                result = await this.runCsvMergeProcess(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                    true,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_CSV_UPDATE_LIST:
                result = await this.runCsvUpdateDynamiList(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_UPDATE_LIST_OF_PROCESSES:
                result = await this.runProcessUpdateDynamicList(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_EVENTW:
                result = await this.runProcessIRuleEventWait(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParams,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_CSV_EXPORT_PROCESSES:
                result = await this.runProcessCsvExport(
                    mainProcess,
                    mainTask,
                    event,
                    ruleDefinition,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
                break;
            case ruleDefinitionConsts.TRULE_TYPE_EXTERNAL: {
                const eveHandler = new EventHandlerMan(
                    this.orm,
                    this.currentUser,
                );
                eveHandler.eventHSelectEventHandler(ruleDefinition.RDEF_VALUE);
                eveHandler.eventHSetEventParams(eveParams);
                eveHandler.eventHSetRuleVariables(ruleVars);
                eveHandler.eventHSetProcess(mainProcess);
                eveHandler.eventHSetProcessVariables(processVariables);
                result = await eveHandler.eventHCall();
                break;
            }
            default:
                throw new InternalException(
                    `'Rule ${ruleDefinition.RDEF_TYPE} not implemented!'`,
                );
        }

        globalThis.tasLogger.info(`Rule processed: ${ruleDefinition.RDEF_ID}`, {
            iproc_id: mainProcess.IPROC_ID,
            itask_id: mainTask.ITASK_ID,
        });
        await this.orm
            .repo("rule")
            .setAsProcesses(event.EVE_ID, ruleDefinition.RDEF_ID);

        return result;
    }

    /**
     * Event wait is create automatically after any task wait for specified event_Definition.
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param {RuleDefinition} ruleDefinition
     * @param {Object} eveParamsMap
     * @param {Object} ruleVarsMap
     * @param {Array<RuleDefinitionParam>} ruleParams
     * @returns {Promise.<*>}
     */
    async runProcessCsvExport(
        _mainProcess: IProcess,
        _mainTask: ITask,
        _event: IEvent,
        ruleDefinition: IRuleDefinition,
        _eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean> {
        // Find all used template variables
        const connection = globalThis.database;
        const tvarIds: number[] = _.compact(
            _.uniq(_.map(ruleParams, "TVAR_ID")),
        ); // Get unique tvar ids from rule params. Null/False values are omited by _.compact

        // Load template variable aliases.
        const tvarRepo = this.orm.repo("templateVariable");
        const tvars = await tvarRepo.getMulti(tvarIds);
        const tvarAliases: string[] = _.map(tvars, "TVAR_ALIAS");

        // Read processes and its variables.
        //Assert that ruleDefinition.RDEF_VALUE is not null
        const id = ruleDefinition.RDEF_VALUE!.split(".");
        const allProcessRows = await connection
            .select(["IP.*", "U.USER_NAME"].concat(tvarAliases))
            .from("INSTANCE_PROCESSES as IP")
            .leftJoin(
                "INSTANCE_PROCESS_VERSIONS as IPV",
                "IPV.IPROC_ID",
                "IP.IPROC_ID",
            ) // Join PROCESS version
            .leftJoin("TEMPLATE_PROCESSES as TP", (builder) => {
                builder
                    .on("IP.TPROC_ID", "TP.TPROC_ID") // TPROC_ID must be same
                    .andOn((onBuilder) => {
                        onBuilder
                            .on("IPV.TPROC_VERSION", "TP.TPROC_VERSION") // Find right version
                            .orOn(
                                globalThis.database.raw(
                                    `"IPV"."TPROC_VERSION" IS NULL AND "TP"."TPROC_VERSION" = 1`,
                                ),
                            ); // Fallback to version = 1
                    });
            })
            .leftJoin(
                "INSTANCE_PROCESS_VARIABLES as IPV",
                "IP.IPROC_ID",
                "IPV.IPROC_ID",
            )
            .leftJoin("USERS as U", "IP.IPROC_INST_OWNER_USER_ID", "U.USER_ID")
            .where("TP.TPROC_ID", id[0])
            .where("TP.TPROC_VERSION", id[1]);

        // Some non tvar values. Taken from process or user owner.
        const processConstantMap = {
            $PROC_START_DATE: "IPROC_ACTUAL_START_DATE",
            $PROC_OWNER: "USER_NAME",
        };

        const exportRows = []; // Array of rows from rule mapping and process variables.

        // Create table from variables and process values.
        if (Array.isArray(allProcessRows) && allProcessRows.length > 0) {
            for (const processRow of allProcessRows) {
                // All processes
                const currLine: { [key: string]: any } = {};
                for (const ruleParam of ruleParams) {
                    // All params
                    if (ruleParam.TVAR_ID) {
                        const tvar = _.find(tvars, {
                            TVAR_ID: ruleParam.TVAR_ID,
                        }) as ITemplateVariable; // Find alias for tvar
                        currLine[tvar.TVAR_NAME!] =
                            processRow[tvar.TVAR_ALIAS!];
                    } else if (
                        processConstantMap[
                            ruleParam.EVEPAR_NAME as keyof typeof processConstantMap
                        ]
                    ) {
                        currLine[
                            ruleParam.RDEFPAR_NAME as keyof typeof processConstantMap
                        ] =
                            processRow[
                                processConstantMap[
                                    ruleParam.EVEPAR_NAME as keyof typeof processConstantMap
                                ]
                            ];
                    } else {
                        currLine[
                            ruleParam.RDEFPAR_NAME as keyof typeof processConstantMap
                        ] = ""; // Add empty value. Dont break csv table.
                    }
                }
                exportRows.push(currLine);
            }
        }

        // Get headers.
        const headers = [];
        for (const ruleParam of ruleParams) {
            if (ruleParam.TVAR_ID) {
                // Header name is tvar_name
                const tvar = _.find(tvars, {
                    TVAR_ID: ruleParam.TVAR_ID,
                }) as ITemplateVariable; // Find alias for tvar
                headers.push(tvar.TVAR_NAME);
            } else {
                // Header name is eveparName like $PROC_OWNER
                headers.push(ruleParam.RDEFPAR_NAME);
            }
        }

        // Write to csv
        const csvWriter = new CsvWriter();
        csvWriter.setDelimiter(ruleVarsMap.RDEF_CSV_SEPARATOR || ",");
        const csvFilePath = `${globalThis.dynamicConfig.csvStorageDir}/${ruleVarsMap.RDEF_CSV_MASK}`;
        await csvWriter.writeToFile(csvFilePath, exportRows, headers);

        return true;
    }

    /**
     * Event wait is create automatically after any task wait for specified event_Definition.
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param {RuleDefinition} ruleDefinition
     * @param {Object} eveParamsMap
     * @param {Object} ruleVarsMap
     * @param {Array<RuleDefinitionParam>} ruleParams
     * @returns {Promise.<*>}
     */
    async runProcessIRuleEventWait(
        mainProcess: IProcess,
        _mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParams: IEventParam[],
        _eveParamsMap: Record<string, any>,
        _ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void> {
        const ttaskId = ruleDefinition.getTTAskIdFromValue();

        const eventRepo = this.orm.repo("event");
        const waitingTasks = await eventRepo
            .getWaitingTasks(event, ttaskId)
            .collectAll(); // Get all tasks activated by this event.

        if (Array.isArray(waitingTasks) && waitingTasks.length > 0) {
            for (const itask of waitingTasks) {
                // Get process variable values from event definition.
                const subProc = this.orm
                    .repo("process")
                    .getEntity({ IPROC_ID: itask.IPROC_ID });
                const varsMap = await this.getMapVariables(
                    subProc,
                    itask,
                    eveParams,
                    ruleParams,
                ); // Fill ruleDefinition maping with real(variable from process) values.
                await this.setIProcessVariables(mainProcess, subProc, varsMap);
                globalThis.tasLogger.info(
                    `Waking up '${itask.ITASK_NAME}' ${itask.ITASK_ID} by event.`,
                    {
                        iproc_id: mainProcess.IPROC_ID,
                        event: event.EVE_ID,
                        itask_id_waked: itask.ITASK_ID,
                    },
                );
                await this.wfTask.wakeupFromEventWait(itask);
            }
        }
    }

    /**
     * Copy subProcess variables into main process. Set constants and calcs.
     * @param {Process} subProcess
     * @param {Event} subEvent with RETURN rule
     * @param {VariableList} subVarList
     * @returns {Promise.<void>}
     */
    async returnMappingFromSubProcess(
        subProcess: IProcess,
        subEvent: IEvent,
        subVarList: IVariableList,
    ): Promise<void> {
        // TODO map back to main process.
        // Get subProcess mapping
        const ruleDefParamRepo = this.orm.repo("ruleDefinitionParam");
        const mapping = await ruleDefParamRepo
            .getForEventDefinition(subEvent)
            .collectAll();

        // Load main process variables if any calculation in mapping.
        const anyCalc = _.find(mapping, { EVEPAR_NAME: "$CALC" });
        let mainProcess = null;
        if (anyCalc) {
            // Need main process if any calculations.
            const procRepo = this.orm.repo("process");
            mainProcess = await procRepo.get(subProcess.IPROC_MAIN_IPROC_ID);
        }

        if (Array.isArray(mapping) && mapping.length > 0) {
            const varRepo = this.orm.repo("variable");
            for (const map of mapping) {
                const destVar = await varRepo.getByTvarId(
                    subProcess.IPROC_MAIN_IPROC_ID,
                    map.TVAR_ID_DEST,
                );

                // Map variable into variable
                if (map.TVAR_ID) {
                    // Varaible into variable
                    const srcVar = subVarList.getBy("TVAR_ID", map.TVAR_ID);
                    destVar.copyFromVariable(srcVar);
                } else if (map.EVEPAR_NAME === "$CONSTANT") {
                    // Constant
                    destVar.value = map.RDEFPAR_VALUE;
                } else if (map.EVEPAR_NAME === "$CALC") {
                    // Calculation
                    const calcs = new ProcessJsCalculations(this.orm);
                    calcs.setVariables(subVarList.getVariables() || []);
                    calcs.setWorkflow(this.workflow);
                    destVar.value = await calcs.evaluate(
                        this.currentUser,
                        mainProcess,
                        map.RDEFPAR_VALUE,
                    );
                }

                // Store var.
                await varRepo.store(destVar);

                // Store to snaps. TODO realy store mapping to snap values ?
                // const varSnapRepo = this.orm.repo('VariableSnap');
                // await varSnapRepo.updateAllSnaps(destVar); // fix for all tasks
            }
        }
    }

    /**
     *
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param {RuleDefinition} ruleDefinition
     * @param {Object} eveParamsMap
     * @param {Object} ruleVarsMap
     * @param {Array<RuleDefinitionParam>} ruleParams
     * @returns {Promise.<*>}
     */
    async runCsvProcess(
        mainProcess: IProcess,
        _mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void> {
        // Parse csv, read lines, make process
        if (!ruleVarsMap.RDEF_CSV_MASK) {
            throw new InternalException(
                `RDEF_CSV_MASK is not defined. ${ruleDefinitionConsts.TRULE_TYPE_CSV_RUN_PROCESSES}
            is broken! Please view RULE_DEFINITION where RDEF_ID = ${ruleDefinition.RDEF_ID}`,
                "INVALID_RULE",
            );
        }

        // Assert that ruleDefinition.RDEF_VALUE is not null
        const id = ruleDefinition.RDEF_VALUE!.split(".");

        // Check template process existence.
        const tprocRepo = this.orm.repo("templateProcess");
        const tproc = await tprocRepo.get(id);
        if (tproc.isDeleted()) {
            throw new InternalException(
                `Cannot instantiate deleted template process. TPROC_ID=${tproc.TPROC_ID}. Please check rule definition with RDEF_ID=${ruleDefinition.RDEF_ID}`,
            );
        }

        // Instantiate as many processes as many rows in csv.
        // Find termin - if termin set (set is via process event call = always ?!) then set termin from process. Bullshit !
        let termin = null;
        if (_.find(ruleParams, { RDEFPAR_NAME: "$TERMIN" })) {
            termin = mainProcess.IPROC_DUE_DATE_FINISH;
        }

        // Find process name.
        const procName =
            ruleVarsMap.RDEF_CSV_PROC_NAME || tproc.TPROC_DEFAULT_CASE_NAME;

        // Find process solver.
        let userId: number | null = null;
        let orgstrId: number | null = null;
        let procOwnerInCsvColumn = null; // Used only if RDEF_CSV_USER is CSV.
        switch (ruleVarsMap.RDEF_CSV_USER) {
            case "PROCESS": // Process initiator
                userId = eveParamsMap.$IPROC_USER_ID;
                orgstrId = eveParamsMap.$IPROC_ORGSTR_ID;
                break;
            case "TASK": // Task solver
                userId = eveParamsMap.$ITASK_USER_ID;
                orgstrId = eveParamsMap.$ITASK_ORGSTR_ID;
                break;
            case "CSV": {
                // Owner is mapped in csv.
                const csvOwnerColumn = _.find(ruleParams, {
                    RDEFPAR_NAME: "$PROC_OWNER",
                });
                if (!csvOwnerColumn) {
                    throw new InternalException(
                        `Can not find RULE_DEFINITION_PARAM for $PROC_OWNER. Please view rule with RDEF_ID=${ruleDefinition.RDEF_ID}`,
                    );
                }
                // Assert that csvOwnerColumn.EVEPAR_NAME is not null
                const buf = csvOwnerColumn.EVEPAR_NAME!.split("|");
                if (buf.length !== 2) {
                    throw new InternalException(
                        `$PROC_OWNER in rule_definition_param must contains only one '|' to define typeof csv column assign. $PROC_OWNER = ${csvOwnerColumn.EVEPAR_NAME}`,
                    );
                }
                procOwnerInCsvColumn = {
                    assign: buf[0],
                    column: buf[1],
                };

                break;
            }
            default:
                throw new InternalException(
                    `Solver is not defined. Please view broken rule with RDEF_ID = ${ruleDefinition.RDEF_ID}. Check RDEF_CSV_USER in RULE_DEFINITION_VARIABLES.`,
                );
        }

        // Instantiate process by csv row.
        const userRepo = this.orm.repo("user");

        const csvFilePath = `${globalThis.dynamicConfig.csvStorageDir}/${ruleVarsMap.RDEF_CSV_MASK}`;
        const firstLineHeaders = ruleVarsMap.RDEF_CSV_HEADER === "Y";
        const csvIterator = new CsvIterator(firstLineHeaders, true); // Create iterator
        await csvIterator.fromFile(csvFilePath); // Read csv data
        const rowCount = csvIterator.count();

        for (let i = 0; i < rowCount; i += 1) {
            const row = csvIterator.getRow(i); // current csv row data
            if (procOwnerInCsvColumn) {
                // Process owner is defined in csv column.
                // Retrieve CONSTANT value or proper csv column
                const userName =
                    procOwnerInCsvColumn.column === "C"
                        ? /* column is constant */ procOwnerInCsvColumn.column
                        : row[procOwnerInCsvColumn.column];
                const user = await userRepo.getByLogin(userName).collectOne();
                await userRepo.setDefaultChair(user); // Find user first orgstr id.

                userId = user.USER_ID;
                orgstrId = user.getActualChair();
            }

            const procRepo = this.orm.repo("process");
            const subProc = await procRepo.copyInstanceEventFromTemplate(
                id,
                procName,
                "",
                userId,
                orgstrId,
                termin,
                "M",
                event.EVE_ID,
            );
            await this.updateProcessVariablesFromCsvRow(
                subProc,
                ruleDefinition,
                row,
                eveParamsMap,
                ruleVarsMap,
                ruleParams,
            );
            await this.activateProcess(subProc);
        }

        globalThis.tasLogger.info("End of runCsvProcess");

        // const data = fs.readFileSync();
    }

    async runCsvMergeProcess(
        mainProcess: IProcess,
        mainTask: ITask,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
        createMissingProcesses: boolean = true,
    ): Promise<any> {
        // Find primary key from rule_params.
        const primaryKeys = ruleParams.filter((ruleParam) =>
            ruleParam.isCsvPrimaryKey(),
        );

        let primaryInfo = null;
        // if primary key defined find out primary variable from current process.
        if (primaryKeys.length > 0) {
            primaryInfo = [];

            // Find primary variable and its value.
            const varRepo = this.orm.repo("templateVariable");

            for (const primaryKey of primaryKeys) {
                const primaryVariable = await varRepo.get(
                    primaryKey.TVAR_ID_DEST,
                );
                primaryInfo.push({
                    primary: primaryKey,
                    primaryVariable,
                    variableValueColumn: `I${primaryVariable
                        .getValueColumn()
                        .substr(1)}`,
                });
            }
        }

        // Test if variable name {name}
        let isDMSFileVariable = false;
        const varMatch = ruleVarsMap.RDEF_CSV_MASK.match(/{(.*?)}/);
        if (varMatch) {
            const varName = varMatch[1];
            const variable = await this.orm
                .repo("variable")
                .getByTvarName(mainProcess.IPROC_ID, varName);

            if (variable.IVAR_ATTRIBUTE === variableConsts.ATTR_FILE) {
                const files = variable.value;
                if (!files || files.length === 0) {
                    this.log.error(
                        `No file selected in '${varName}' for event '${event.EVEDEF_NAME}'.`,
                        { iproc_id: mainProcess.IPROC_ID },
                    );
                    throw new UserException(
                        `No file selected in '${varName}' for event.`,
                    );
                }
                ruleVarsMap.RDEF_CSV_MASK = variable;
                isDMSFileVariable = true;
            } else {
                ruleVarsMap.RDEF_CSV_MASK = variable.value;
            }
        }

        // Read CSV file or load dynamic table.
        const isDTImport =
            !isDMSFileVariable && !ruleVarsMap.RDEF_CSV_MASK.endsWith(".csv"); // CSV or DT

        let csvIterator;
        const firstLineHeaders = ruleVarsMap.RDEF_CSV_HEADER === "Y";
        if (isDTImport) {
            csvIterator = new CsvIterator(true, true); // Create iterator
            try {
                const csvString = await this.orm
                    .repo("dynamicTableValue")
                    .toCsv(ruleVarsMap.RDEF_CSV_MASK);
                await csvIterator.fromString(csvString); // Read csv data
            } catch (err: any) {
                if (err.codeName === "UNKNOWN_DYNAMIC_TABLE") {
                    globalThis.tasLogger.warning(
                        "Can not run event merge_csv. DynamicTable not found.",
                        err,
                    );
                    return false;
                }
            }
        } else if (isDMSFileVariable) {
            csvIterator = new CsvIterator(firstLineHeaders, true); // Create iterator
            const api = new IVariableApi(this.orm.connection, {
                variable: ruleVarsMap.RDEF_CSV_MASK,
                process: mainProcess,
            });
            const csvStringBinary = await api.call("getFileStream", ["utf8"]);
            const csvString = String(csvStringBinary);
            await csvIterator.fromString(csvString); // Read csv data
        } else {
            csvIterator = new CsvIterator(firstLineHeaders, true); // Create iterator
            const csvFilePath = `${globalThis.dynamicConfig.csvStorageDir}/${ruleVarsMap.RDEF_CSV_MASK}`;
            await csvIterator.fromFile(csvFilePath); // Read csv data
        }

        // Iterate over csv and find row to update process variables.
        const procRepo = this.orm.repo("process");
        // Assert that ruleDefinition.RDEF_VALUE is not null
        const id = ruleDefinition.RDEF_VALUE!.split(".");
        const procName = ruleVarsMap.RDEF_CSV_PROC_NAME;
        let userId: number | null = null;
        let orgstrId: number | null = null;

        // Find user ...
        let csvOwnerColumn = null;
        if (ruleVarsMap.RDEF_CSV_USER === "PROCESS") {
            // Assert that mainProcess.IPROC_INST_OWNER_USER_ID and IPROC_INST_OWNER_ORGSTR_ID is not null
            userId = mainProcess.IPROC_INST_OWNER_USER_ID!;
            orgstrId = mainProcess.IPROC_INST_OWNER_ORGSTR_ID!;
        } else if (ruleVarsMap.RDEF_CSV_USER === "TASK") {
            userId = eveParamsMap.$ITASK_USER_ID;
            orgstrId = eveParamsMap.$ITASK_ORGSTR_ID;
        } else if (ruleVarsMap.RDEF_CSV_USER === "CSV") {
            csvOwnerColumn = _.find(ruleParams, {
                RDEFPAR_NAME: "$PROC_OWNER",
            });
            if (!csvOwnerColumn) {
                throw new UserException(
                    "Process owner not defined in csv mapping. Please change RDEF_CSV_USER to TASK or PROCESS ovner.",
                );
            }
        }

        // Find termin - if termin set (set is via process event call = always ?!) then set termin from process. Bullshit !
        let termin = null;
        if (_.find(ruleParams, { RDEFPAR_NAME: "$TERMIN" })) {
            termin = mainProcess.IPROC_DUE_DATE_FINISH;
        }

        const rowCount = csvIterator.count();
        for (let i = 0; i < rowCount; i += 1) {
            const row = csvIterator.getRow(i);
            // Process owner is defined in csv. Find him.
            if (csvOwnerColumn) {
                const userData = csvOwnerColumn.getValueFromCsvRow(row);
                const userApi = await this.wfSolver.getUserApi(userData); // from login name, user_id, ..
                const user = userApi.getUser(); // only active users can act in tasks
                user.setActualChair(
                    user.getActualChair() === null
                        ? await this.wfSolver.getDefaultChair(
                              user.USER_ID,
                              mainTask,
                              mainProcess,
                          )
                        : user.getActualChair(),
                );

                userId = user.USER_ID;
                orgstrId = user.getActualChair();
            }

            // If primary key defined then update all process with value otherwise create process for each csv row.
            let mergedProcesses: any = null;
            let activate = false;
            if (primaryInfo) {
                for (const info of primaryInfo) {
                    // Find all processes where value equals to csvRow value...
                    const searchValue = info.primary.getValueFromCsvRow(row);

                    const pkProcesses = _.map(
                        await this.orm.connection
                            .select("IPROC_ID")
                            .from("INSTANCE_VARIABLES")
                            .where("TVAR_ID", info.primaryVariable.TVAR_ID)
                            .whereRaw(
                                `${info.variableValueColumn} = ?`,
                                searchValue,
                            ),
                        "IPROC_ID",
                    );

                    if (mergedProcesses == null) {
                        mergedProcesses = pkProcesses;
                    } else {
                        mergedProcesses = _.intersection(
                            mergedProcesses,
                            pkProcesses,
                        );
                    }
                }

                // No primary found, create new.
                if (mergedProcesses.length === 0) {
                    if (createMissingProcesses) {
                        // No primary key, create process and update values.
                        const mergedProcess =
                            await procRepo.copyInstanceEventFromTemplate(
                                id,
                                procName,
                                "",
                                userId,
                                orgstrId,
                                termin,
                                "M",
                                event.EVE_ID,
                            );
                        mergedProcesses = [mergedProcess];
                        globalThis.tasLogger.info(
                            `Event activating because no process match primary key process with IPROC_ID = ${mergedProcess.IPROC_ID}`,
                        );
                        activate = true;
                    }
                } else {
                    const processes = [];
                    for (const iprocId of mergedProcesses) {
                        processes.push(await procRepo.get(iprocId));
                    }
                    mergedProcesses = processes;
                }
            } else if (createMissingProcesses) {
                // No primary key, create process and update values.
                const mergedProcess =
                    await procRepo.copyInstanceEventFromTemplate(
                        id,
                        procName,
                        "",
                        userId,
                        orgstrId,
                        termin,
                        "M",
                        event.EVE_ID,
                    );
                mergedProcesses = [mergedProcess];
                globalThis.tasLogger.info(
                    `Event activating process with IPROC_ID = ${mergedProcess.IPROC_ID}`,
                );
                activate = true;
            }

            if (Array.isArray(mergedProcesses) && mergedProcesses.length > 0) {
                const processVariableUpdate = [];
                for (const mergedProcess of mergedProcesses) {
                    globalThis.tasLogger.info(
                        `Update process variables by event. IPROC_ID=${mergedProcess.IPROC_ID}`,
                    );
                    const res = await this.updateProcessVariablesFromCsvRow(
                        mergedProcess,
                        ruleDefinition,
                        row,
                        eveParamsMap,
                        ruleVarsMap,
                        ruleParams,
                    );
                    processVariableUpdate.push(res);
                }

                if (!activate) {
                    return processVariableUpdate;
                }
            }

            // Activate new process.
            if (activate) {
                await this.activateProcess(mergedProcesses[0]);
            }
        }
    }

    /**
     * Update process variables from csv. There can be primary key to define which row from csv should be used.
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param ruleDefinition
     * @param-validator event(isString, isProcess, isValidArray)
     * @param eveParamsMap
     * @param ruleVarsMap
     * @param ruleParams
     * @returns {Promise.<boolean>}
     */
    async runCsvUpdateProcess(
        mainProcess: IProcess,
        _mainTask: ITask,
        _event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean> {
        /**
         * Check if event has primary key for update. It means variable from process must has same value as column in csv.
         * Both defined in rule_definition_param.
         * EVEPAR_NAME like 'N|{csvCol}|PK' - PK means primary key.
         * Now Search TVAR_ID_DEST in process and check its value against csv[{csvCol}]
         */

        // Find primary key from rule_params.
        const primaryKeys = ruleParams.filter((ruleParam) =>
            ruleParam.isCsvPrimaryKey(),
        );
        // There cant be multiple primary keys. Primary key can be undefined.
        if (Array.isArray(primaryKeys) && primaryKeys.length > 1) {
            const mapping = _.map(primaryKeys, "EVEPAR_NAME");
            throw new UserException(
                `Multiple primary keys in rule_definition_param for RDEF_ID=${
                    ruleDefinition.RDEF_ID
                }. There is ${primaryKeys.length} primary keys: ${mapping.join(
                    ", ",
                )}`,
            );
        }

        let primaryVariable = null;
        let csvPrimaryCol = null;
        // if primary key defined find out primary variable from current process.
        if (primaryKeys.length > 0) {
            const primaryKey = primaryKeys[0]; // Get first primary key. There canot be more than one !
            csvPrimaryCol = primaryKey.getCsvColumnName();

            // Find primary variable and its value.
            const varRepo = this.orm.repo("variable");
            primaryVariable = await varRepo.getByTvarId(
                mainProcess.IPROC_ID,
                primaryKey.TVAR_ID_DEST,
            );
        }

        // Read CSV file.
        const csvFilePath = `${globalThis.dynamicConfig.csvStorageDir}/${ruleVarsMap.RDEF_CSV_MASK}`;
        const firstLineHeaders = ruleVarsMap.RDEF_CSV_HEADER === "Y";
        const csvIterator = new CsvIterator(firstLineHeaders, true); // Create iterator
        await csvIterator.fromFile(csvFilePath); // Read csv data

        // Iterate over csv and find row to update process variables.
        const rowCount = csvIterator.count();
        for (let i = 0; i < rowCount; i += 1) {
            const csvRow = csvIterator.getRow(i);

            // Update row if not primary defined or primary match process value.
            if (
                !csvPrimaryCol ||
                csvRow[csvPrimaryCol] === primaryVariable?.value
            ) {
                await this.updateProcessVariablesFromCsvRow(
                    mainProcess,
                    ruleDefinition,
                    csvRow,
                    eveParamsMap,
                    ruleVarsMap,
                    ruleParams,
                );
            }
        }

        return true;
    }

    async updateProcessVariablesFromCsvRow(
        process: IProcess,
        _ruleDefinition: IRuleDefinition,
        csvRow: Record<string, any>,
        _eveParamsMap: Record<string, any>,
        _ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void> {
        await globalThis.tasLogger.runTask(async (): Promise<void> => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_EVENTS,
            );
            if (Array.isArray(ruleParams) && ruleParams.length > 0) {
                // Prepare all variables to update,
                const varRepo = this.orm.repo("variable");
                const updatedTvars = ruleParams
                    .filter((ruleParam) => ruleParam.TVAR_ID_DEST !== undefined)
                    .map((ruleParam) => ruleParam.TVAR_ID_DEST!.toString());
                const coll = varRepo.getByProcess(process.IPROC_ID);
                coll.knex.whereIn("TV.TVAR_ID", updatedTvars);
                const updatedVariables = await coll.collectAll();

                // Update all variable from ruleDefinition mapping.
                for (const ruleParam of ruleParams) {
                    // Update only rules where TVAR_ID_DEST not null. Other mapping is used to update process like: RDEFPAR_NAME=$PROC_OWNER.
                    if (
                        ruleParam.TVAR_ID_DEST &&
                        !ruleParam.isCsvPrimaryKey()
                    ) {
                        const newValue = ruleParam.getValueFromCsvRow(csvRow);
                        const variable: any = _.find(updatedVariables, {
                            TVAR_ID: ruleParam.TVAR_ID_DEST,
                        });

                        if (variable) {
                            globalThis.tasLogger.info(
                                `Event updating variable ${variable.IVAR_NAME}(${variable.IVAR_ID}) from ${variable.value} to ${newValue}`,
                            );
                            variable.value = newValue;
                            await varRepo.store(variable);
                        } else {
                            // Old process ? Variable not found
                            globalThis.tasLogger.warning(
                                `Varaible  TVAR_ID=${ruleParam.TVAR_ID_DEST} not found in IPROC_ID=${process.IPROC_ID}. Value is not updated!`,
                                {
                                    iproc_id: process.IPROC_ID,
                                    csvRow,
                                    ruleParam,
                                },
                            );
                        }
                    }
                }
            }
        });
    }

    /**
     * Update DYNAMIC_LIST values from variables from processes of one tempalte process.
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param {RuleDefinition} ruleDefinition
     * @param {Object} eveParamsMap
     * @param {Object} ruleVarsMap
     * @param {Array<RuleDefinitionParam>} ruleParams
     * @returns {Promise.<void>}
     */
    async runProcessUpdateDynamicList(
        _mainProcess: IProcess,
        _mainTask: ITask,
        _event: IEvent,
        ruleDefinition: IRuleDefinition,
        _eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean> {
        // Check template process existence.
        const tprocRepo = this.orm.repo("templateProcess");
        const tproc = await tprocRepo.get(Number(ruleVarsMap.RDEF_PROCESS_ID));
        if (tproc.isDeleted()) {
            throw new InternalException(
                `Cannot instantiate deleted template process. TPROC_ID=${tproc.TPROC_ID}. Please check rule definition with RDEF_ID=${ruleDefinition.RDEF_ID}`,
            );
        }

        // Get list name and list vlaue. Its used as variable name.
        // Assert both listName and listValue are defined.
        const listName = _.find(ruleParams, { RDEFPAR_NAME: "$LIST_NAME" })!;
        const listValue = _.find(ruleParams, { RDEFPAR_NAME: "$LIST_VALUE" })!;

        // Values to update can be only mapped as ColumNane (type=N).
        if (
            listName?.getCsvColumnType() !== "N" ||
            listValue?.getCsvColumnType() !== "N"
        ) {
            throw new InternalException(`Invalid mapping type. Update dynamic list values from csv can be only mapped as constant.
            Found ${listName.EVEPAR_NAME} for ${listName.RDEFPAR_NAME} and ${listValue.EVEPAR_NAME} for ${listValue.RDEFPAR_NAME}`);
        }

        // Get all values from processes.
        const varRepo = this.orm.repo("variable");
        const listVariables = varRepo.getByActiveProcesses(tproc.TPROC_ID);
        listVariables.knex.where("IV.IVAR_NAME", listValue.getCsvColumnName());
        const variables = await listVariables.collectAll();
        const variableValues = _.uniq(_.map(variables, "value")); // Get distinct variableValues.

        // Clear dynamic list and update values
        const dlistRepo = this.orm.repo("dynamicList");
        await dlistRepo.deleteList(listName.getCsvColumnName());
        const dlistName = listName.getCsvColumnName();
        if (Array.isArray(variableValues)) {
            for (const dlistValue of variableValues) {
                if (dlistValue) {
                    await dlistRepo.insertRow(dlistName, dlistValue);
                }
            }
        }
        // Setup DL
        return true;
    }

    /**
     * Update DYNAMIC_LIST values from csv.
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param {RuleDefinition} ruleDefinition
     * @param {Object} eveParamsMap
     * @param {Object} ruleVarsMap
     * @param {Array<RuleDefinitionParam>} ruleParams
     * @returns {Promise.<boolean>}
     */
    async runCsvUpdateDynamiList(
        _mainProcess: IProcess,
        _mainTask: ITask,
        _event: IEvent,
        _ruleDefinition: IRuleDefinition,
        _eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<boolean> {
        // Read CSV file.
        const csvFilePath = `${globalThis.dynamicConfig.csvStorageDir}/${ruleVarsMap.RDEF_CSV_MASK}`;
        const firstLineHeaders = ruleVarsMap.RDEF_CSV_HEADER === "Y";
        const csvIterator = new CsvIterator(firstLineHeaders, true); // Create iterator
        await csvIterator.fromFile(csvFilePath); // Read csv data

        /**
         * Each row from csv in $LIST_VALUE column is inserted to dynamiclist with name $LIST_NAME.
         */
        const listName = _.find(ruleParams, { RDEFPAR_NAME: "$LIST_NAME" });
        const listValue = _.find(ruleParams, { RDEFPAR_NAME: "$LIST_VALUE" });

        const dlistRepo = this.orm.repo("dynamicList");

        const clearedDlists: any = {}; // Csv can contain more dynamic lists. Clear if new dlist value found.
        const rowCount = csvIterator.count();
        for (let i = 0; i < rowCount; i += 1) {
            const row = csvIterator.getRow(i);

            // Get real dlist name and value. From constant or csv row.
            const dlistValue = listValue?.getValueFromCsvRow(row) || "";
            const dlistName = listName?.getValueFromCsvRow(row) || "";

            // Clear dynamic list if is affected with csv !
            if (!clearedDlists[dlistName]) {
                await dlistRepo.deleteList(dlistName);
                clearedDlists[dlistName] = true;
            }

            await dlistRepo.insertRow(dlistName, dlistValue);
        }

        return true;
    }

    /**
     * runProcessRule PROCESS event or SUBPROCESS.
     * @param {Process} mainProcess
     * @param {Task} mainTask
     * @param {Event} event
     * @param {RuleDefinition} ruleDefinition
     * @param {Object} eveParamsMap
     * @param {Object} ruleVarsMap
     * @param {Array<RuleDefinitionParam>} ruleParams
     * @returns {Promise.<*>}
     */
    async runProcessRule(
        mainProcess: IProcess,
        mainTask: any,
        event: IEvent,
        ruleDefinition: IRuleDefinition,
        eveParams: IEventParam[],
        eveParamsMap: Record<string, any>,
        ruleVarsMap: Record<string, any>,
        ruleParams: IRuleDefinitionParam[],
    ): Promise<void> {
        // Assert rule definition RDEF_VALUE is set.
        const id = ruleDefinition.RDEF_VALUE!.split(".");
        let addDollarSign = true;
        let procName;
        let userId;
        let orgstrId;

        // Is subprocess !
        if (eveParamsMap.$ITASK_TYPE === taskConsts.TYPE_SUBPROCESS) {
            userId = eveParamsMap.$ITASK_USER_ID;
            orgstrId = eveParamsMap.$ITASK_ORGSTR_ID;

            const tprocRepo = this.orm.repo("templateProcess");
            const tprocSubp = await tprocRepo.get(id, [
                "TPROC_NAME",
                "TPROC_DEFAULT_CASE_NAME",
            ]);

            if (tprocSubp.TPROC_DEFAULT_CASE_NAME) {
                procName = tprocSubp.TPROC_DEFAULT_CASE_NAME;
                addDollarSign = false;
            } else {
                procName =
                    tprocSubp.TPROC_NAME + moment().format("d.m.Y HH:mm:ss");
            }
        } else {
            // Run process event
            // get user from rule_definition_variable
            const buf = ruleVarsMap.RDEF_PROC_OWNER.split(".");
            userId = buf[0];
            orgstrId = buf[1];
            procName = ruleVarsMap.RDEF_PROC_NAME;
        }

        // Find termin - if termin set (set is via process event call = always ?!) then set termin from process. Bullshit !
        let termin = null;
        if (_.find(ruleParams, { RDEFPAR_NAME: "$TERMIN" })) {
            termin = mainProcess.IPROC_DUE_DATE_FINISH;
        }

        // Create process instance.
        const procRepo = this.orm.repo("process");
        if (!userId) {
            globalThis.tasLogger.info(
                "No user found for Process. Using ITASK_USER_ID.",
            );
            userId = mainTask.ITASK_USER_ID;
        }

        //
        // id
        const subProc = await procRepo.copyInstanceEventFromTemplate(
            id,
            procName,
            "",
            userId,
            orgstrId,
            termin,
            "M",
            event.EVE_ID,
        );

        // Get process variable values from event definition.
        const varsMap = await this.getMapVariables(
            subProc,
            mainTask,
            eveParams,
            ruleParams,
        ); // Fill ruleDefinition maping with real(variable from process) values.

        globalThis.tasLogger.info(
            `I create new Process from Event --> ID: ${subProc.IPROC_ID} USER_ID: ${subProc.IPROC_INST_OWNER_USER_ID}`,
        );
        await this.setIProcessVariables(mainProcess, subProc, varsMap);

        // Write subprocess id to main process.
        if (eveParamsMap.$ITASK_TYPE === taskConsts.TYPE_SUBPROCESS) {
            mainTask.ITASK_SUBPROCESS_IPROC_ID = subProc.IPROC_ID;
            await this.orm.repo("task").store(mainTask);

            // set flag subprocess started in instance process
            if (addDollarSign) {
                procName = `$ ${mainProcess.IPROC_ID} ${mainProcess.IPROC_NAME}`;
            }

            // setIProcessSubProcessStart() - mark subprocess as started subprocess. Write main process id.
            subProc.IPROC_SUBPROCESS_FLAG = 1;
            subProc.IPROC_MAIN_IPROC_ID = mainProcess.IPROC_ID;
            subProc.IPROC_NAME = procName;
        }

        // Activate new process/subprocess.
        const wf = await this.activateProcess(subProc);

        // Add subprocess task to current workflow list.
        const tasks = wf.wfTask.getTasksToSolve();
        const currentTasks = this.wfTask.getTasksToSolve();
        this.wfTask.setTasksToSolve([...currentTasks, ...tasks]);

        return subProc;
        // Activate, fill variables, etc !
    }

    /**
     * Combine "Real variables from process" and "Event mapping definition" into one map.
     */
    async getMapVariables(
        process: IProcess,
        task: ITask,
        eVars: Record<string, any>[],
        pars: Record<string, any>[],
    ): Promise<Record<string, any>[]> {
        // Check params
        if (!(task instanceof Task)) {
            throw new InternalException(
                "Invalid argument type. Task must be instanceof Task entity.",
                { task },
            );
        }
        if (!(process instanceof Process)) {
            throw new InternalException(
                "Invalid argument type. Process must be instanceof Process entity.",
                { process },
            );
        }

        const tvars: any = {};
        const vars: any = {};

        // Map by tvar id and eveparName
        if (Array.isArray(eVars) && eVars.length > 0) {
            for (let i = 0; i < eVars.length; i += 1) {
                if (eVars[i].TVAR_ID) {
                    tvars[eVars[i].TVAR_ID] = eVars[i];
                }
                vars[eVars[i].EVEPAR_NAME] = eVars[i];
            }
        }

        const map = [];
        const varRepo = this.orm.repo("variable");
        // Iterate each rule param.
        if (Array.isArray(pars) && pars.length > 0) {
            for (const par of pars) {
                if (par.TVAR_ID && tvars[par.TVAR_ID]) {
                    // tvar definition and tvar variable exists
                    // Variable to another variable mapping. (e.g. process -> subprocess)
                    if (par.TVAR_ID_DEST) {
                        // Make variable. This is used to fill process variables later.
                        const collection = varRepo.getByProcess(
                            process.IPROC_ID,
                        );
                        collection.knex.where("IV.TVAR_ID", par.TVAR_ID_DEST);
                        const targetVariable = await collection.collectOne();

                        // Source Variable
                        const colSrc = varRepo.getByProcess(task.IPROC_ID);
                        colSrc.knex.where("IV.TVAR_ID", par.TVAR_ID);
                        const srcVariable = await colSrc.collectOne();

                        if (!targetVariable) {
                            throw new InternalException(
                                "Can not find target variable for event mapping.",
                                "INTERNAL_ERROR",
                                {
                                    process,
                                    TVAR_ID: par.TVAR_ID_DEST,
                                },
                            );
                        }

                        targetVariable.fill(
                            {
                                TVAR_ID: par.TVAR_ID_DEST,
                                IVAR_TEXT_VALUE:
                                    tvars[par.TVAR_ID].EVEPAR_TEXT_VALUE,
                                IVAR_NUMBER_VALUE:
                                    tvars[par.TVAR_ID].EVEPAR_NUMBER_VALUE,
                                IVAR_DATE_VALUE:
                                    tvars[par.TVAR_ID].EVEPAR_DATE_VALUE,
                                IVAR_BIG_VALUE:
                                    tvars[par.TVAR_ID].EVEPAR_BIG_VALUE,
                                IVAR_MULTI: tvars[par.TVAR_ID].EVEPAR_MULTI,
                                IVAR_MULTI_SELECTED:
                                    tvars[par.TVAR_ID].EVEPAR_MULTI_SELECTED,
                                IVAR_DT_INDEX:
                                    tvars[par.TVAR_ID].EVEPAR_DT_INDEX,
                            },
                            true,
                        );

                        // Variable is read from INDEX later.
                        if (tvars[par.TVAR_ID].EVEPAR_DT_INDEX) {
                            targetVariable.IVAR_TEXT_VALUE =
                                tvars[par.TVAR_ID].EVEPAR_DT_INDEX;
                        }

                        // t3b-672 Mapování proměnných událostí
                        if (
                            targetVariable.IVAR_TYPE ===
                                variableConsts.TYPE_DATE_LIST &&
                            tvars[par.TVAR_ID].EVEPAR_TEXT_VALUE &&
                            srcVariable.IVAR_TYPE === variableConsts.TYPE_TEXT
                        ) {
                            // Text -> DL.U
                            if (
                                targetVariable.IVAR_ATTRIBUTE ===
                                variableConsts.ATTR_USER
                            ) {
                                const user = await this.orm
                                    .repo("user")
                                    .getByUserNameOrDisplayname(
                                        tvars[par.TVAR_ID].EVEPAR_TEXT_VALUE,
                                    );
                                targetVariable.IVAR_TEXT_VALUE = null;
                                targetVariable.IVAR_NUMBER_VALUE = user.USER_ID;
                            }
                            // Text -> DL.R
                            if (
                                targetVariable.IVAR_ATTRIBUTE ===
                                variableConsts.ATTR_ROLE
                            ) {
                                const user = await this.orm
                                    .repo("role")
                                    .getByName(
                                        tvars[par.TVAR_ID].EVEPAR_TEXT_VALUE,
                                    );
                                targetVariable.IVAR_TEXT_VALUE = null;
                                targetVariable.IVAR_NUMBER_VALUE =
                                    user?.ROLE_ID;
                            }
                            // Text -> DL.O
                            if (
                                targetVariable.IVAR_ATTRIBUTE ===
                                variableConsts.ATTR_ORG_STRUCT
                            ) {
                                const user = await this.orm
                                    .repo("organizationStructure")
                                    .getByAttr(
                                        "ORGSTR_NAME",
                                        tvars[par.TVAR_ID].EVEPAR_TEXT_VALUE,
                                        true,
                                    );
                                targetVariable.IVAR_TEXT_VALUE = null;
                                targetVariable.IVAR_NUMBER_VALUE = (
                                    user as User
                                )?.ROLE_ID;
                            }
                        }
                        if (
                            targetVariable.IVAR_TYPE ===
                                variableConsts.TYPE_TEXT &&
                            tvars[par.TVAR_ID].EVEPAR_NUMBER_VALUE &&
                            srcVariable.IVAR_TYPE ===
                                variableConsts.TYPE_DATE_LIST
                        ) {
                            // DL.U -> Text
                            if (
                                srcVariable.IVAR_ATTRIBUTE ===
                                variableConsts.ATTR_USER
                            ) {
                                const user = await this.orm
                                    .repo("user")
                                    .get(
                                        tvars[par.TVAR_ID].EVEPAR_NUMBER_VALUE,
                                    );
                                targetVariable.IVAR_TEXT_VALUE = user.USER_NAME;
                                targetVariable.IVAR_NUMBER_VALUE = null;
                            }
                            // DL.O -> Text
                            if (
                                srcVariable.IVAR_ATTRIBUTE ===
                                variableConsts.ATTR_ORG_STRUCT
                            ) {
                                const user = await this.orm
                                    .repo("organizationStructure")
                                    .get(
                                        tvars[par.TVAR_ID].EVEPAR_NUMBER_VALUE,
                                    );
                                targetVariable.IVAR_TEXT_VALUE =
                                    user.ORGSTR_NAME;
                                targetVariable.IVAR_NUMBER_VALUE = null;
                            }
                            // DL.R -> Text
                            if (
                                srcVariable.IVAR_ATTRIBUTE ===
                                variableConsts.ATTR_ROLE
                            ) {
                                const user = await this.orm
                                    .repo("role")
                                    .get(
                                        tvars[par.TVAR_ID].EVEPAR_NUMBER_VALUE,
                                    );
                                targetVariable.IVAR_TEXT_VALUE = user.ROLE_NAME;
                                targetVariable.IVAR_NUMBER_VALUE = null;
                            }
                        }

                        map.push(targetVariable);
                    } else {
                        // Make event param. This is used as variables in event.
                        const param: ExtendedIEventParamAttributes = {
                            EVEPAR_NAME: par.EVEPAR_NAME,
                            EVEPAR_TEXT_VALUE:
                                tvars[par.TVAR_ID].EVEPAR_TEXT_VALUE,
                            EVEPAR_NUMBER_VALUE:
                                tvars[par.TVAR_ID].EVEPAR_NUMBER_VALUE,
                            EVEPAR_DATE_VALUE:
                                tvars[par.TVAR_ID].EVEPAR_DATE_VALUE,
                            EVEPAR_BIG_VALUE:
                                tvars[par.TVAR_ID].EVEPAR_BIG_VALUE,
                            EVEPAR_MULTI: tvars[par.TVAR_ID].EVEPAR_MULTI,
                            EVEPAR_MULTI_SELECTED:
                                tvars[par.TVAR_ID].EVEPAR_MULTI_SELECTED,
                            EVEPAR_DT_INDEX: tvars[par.TVAR_ID].EVEPAR_DT_INDEX,
                        };

                        // Variable is read from INDEX later.
                        if (param.EVEPAR_DT_INDEX) {
                            param.IVAR_TEXT_VALUE = param.EVEPAR_DT_INDEX;
                        }

                        map.push(param);
                    }
                } else {
                    // Is mapping Constant or Calculation ??
                    let textValue = null;
                    let numberValue = null;
                    let dateValue = null;
                    let bigValue = null;
                    let DTValue = null;
                    let multi = null;
                    let multiSelected = null;

                    let targetVariable;
                    if (par.TVAR_ID_DEST) {
                        const collection = varRepo.getByProcess(
                            process.IPROC_ID,
                        );
                        collection.knex.where("IV.TVAR_ID", par.TVAR_ID_DEST);
                        targetVariable = await collection.collectOne();
                    }

                    if (
                        par.EVEPAR_NAME === "$CONSTANT" ||
                        par.EVEPAR_NAME === "$CALC"
                    ) {
                        let value = "";
                        if (par.EVEPAR_NAME === "$CONSTANT") {
                            value = par.RDEFPAR_VALUE;
                        } else if (par.EVEPAR_NAME === "$CALC") {
                            const calcs = new TaskJsCalculations(
                                this.orm,
                                globalThis.tasLogger,
                            );
                            calcs.setVariables(
                                this.variableList.getVariables(),
                            );
                            calcs.setWorkflow(this.workflow);
                            value = await calcs.evaluate(
                                this.currentUser,
                                task,
                                process,
                                par.RDEFPAR_VALUE,
                            );

                            if (targetVariable) {
                                value = await calcs.getPrimitiveValue(
                                    value,
                                    targetVariable.IVAR_TYPE,
                                    targetVariable.IVAR_ATTRIBUTE,
                                );
                            } else {
                                value = await calcs.getPrimitiveValue(
                                    value,
                                    variableConsts.TYPE_TEXT,
                                );
                            }
                        }
                        switch (par.RDEFPAR_TYPE) {
                            case variableConsts.TYPE_TEXT:
                            case variableConsts.TYPE_TEXT_LIST:
                            case variableConsts.TYPE_DYNAMIC_ROW:
                            case variableConsts.TYPE_DYNAMIC_TABLE:
                                textValue = value;
                                break;
                            case variableConsts.TYPE_NUMBER:
                            case variableConsts.TYPE_NUMBER_LIST:
                            case variableConsts.TYPE_DYNAMIC_LIST:
                                numberValue = value;
                                break;
                            case variableConsts.TYPE_DATE:
                            case variableConsts.TYPE_DATE_LIST:
                                dateValue = value;
                                break;
                            case variableConsts.TYPE_BIG:
                                bigValue = value;
                                break;
                            default:
                                break;
                        }
                    } else if (vars[par.RDEFPAR_NAME]) {
                        textValue = vars[par.RDEFPAR_NAME].EVEPAR_TEXT_VALUE;
                        numberValue =
                            vars[par.RDEFPAR_NAME].EVEPAR_NUMBER_VALUE;
                        dateValue = vars[par.RDEFPAR_NAME].EVEPAR_DATE_VALUE;
                        bigValue = vars[par.RDEFPAR_NAME].EVEPAR_BIG_VALUE;
                        multi = vars[par.RDEFPAR_NAME].EVEPAR_MULTI;
                        multiSelected =
                            vars[par.RDEFPAR_NAME].EVEPAR_MULTI_SELECTED;
                        DTValue = vars[par.RDEFPAR_NAME].EVEPAR_DT_INDEX;
                    }

                    if (targetVariable) {
                        // Make variable. This is used to fill process variables later.
                        if (!targetVariable) {
                            throw new InternalException(
                                "Can not find target variable for event mapping.",
                                "INTERNAL_ERROR",
                                {
                                    process,
                                    TVAR_ID: par.TVAR_ID_DEST,
                                },
                            );
                        }
                        targetVariable.fill(
                            {
                                TVAR_ID: par.TVAR_ID_DEST,
                                IVAR_TEXT_VALUE: textValue,
                                IVAR_NUMBER_VALUE: numberValue,
                                IVAR_DATE_VALUE: dateValue,
                                IVAR_BIG_VALUE: bigValue,
                                IVAR_MULTI: multi,
                                IVAR_MULTI_SELECTED: multiSelected,
                                IVAR_DT_INDEX: DTValue,
                            },
                            true,
                        );
                        map.push(targetVariable);
                    } else {
                        // Make event param. This is used as variables in event.
                        map.push({
                            EVEPAR_NAME: par.EVEPAR_NAME,
                            EVEPAR_TEXT_VALUE: textValue,
                            EVEPAR_NUMBER_VALUE: numberValue,
                            EVEPAR_DATE_VALUE: dateValue,
                            EVEPAR_BIG_VALUE: bigValue,
                            EVEPAR_MULTI: multi,
                            EVEPAR_MULTI_SELECTED: multiSelected,
                            EVEPAR_DT_INDEX: DTValue,
                        });
                    }
                }
            }
        }

        return map;
    }

    async setIProcessVariables(
        mainProc: IProcess,
        subProc: IProcess,
        vars: Record<string, any>[],
    ): Promise<any> {
        const varRepo = this.orm.repo("variable");

        if (!Array.isArray(vars) || vars.length === 0) {
            return false;
        }

        const variableStorage = [];

        for (const variable of vars) {
            if (!variable.IVAR_ID || !variable.IPROC_ID) {
                throw new InternalException(
                    "Can not store variable without iproc_id or ivar_id",
                );
            }

            // Copy file link.
            if (variable.IVAR_ATTRIBUTE === Variable.consts.ATTR_FILE) {
                const files = variable.value;
                if (Array.isArray(files) && files.length > 0) {
                    const dmsRepo = this.orm.repo("dmsFile");
                    for (const file of files) {
                        const dmsFiles = await dmsRepo.getByName(
                            mainProc.IPROC_ID,
                            file,
                        );
                        if (
                            !Array.isArray(dmsFiles) &&
                            //@ts-expect-error types
                            dmsFiles.length === 0
                        ) {
                            continue;
                        }
                        const dmsFile = dmsFiles[0];

                        await dmsRepo.copyFileLink(
                            dmsRepo.getEntity(dmsFile),
                            subProc.IPROC_ID,
                        );
                    }
                }
            }

            const result = await varRepo.store(variable);
            variableStorage.push(result);
        }
        return variableStorage;
    }

    /**
     *  Returns hand events for process.
     * @param {Process} process hand events for process
     * @param {bool|string} onlyButtons Return only event-tasks that can be buttons.
     */
    async handEventList(
        process: IProcess,
        onlyButtons: boolean,
    ): Promise<Record<string, any>[]> {
        // Get all waiting tasks.
        const processes = Array.isArray(process) ? process : [process];
        const taskRepo = this.orm.repo("instanceTask");

        const out = [];
        for (const proc of processes) {
            const waitingTasksCollection = this.orm
                .repo("instanceTask")
                .getWaitingTasks(proc.IPROC_ID);

            // Return only buttons.
            let buttonSwitch;
            if (onlyButtons === true) {
                // compatibility to boolean
                buttonSwitch = "Y";
            } else if (
                typeof onlyButtons === "string" &&
                ["Y", "N"].indexOf(onlyButtons) !== -1
            ) {
                buttonSwitch = onlyButtons;
            }
            if (buttonSwitch) {
                waitingTasksCollection.knex.where(
                    "TTASK_CAN_BE_BUTTON",
                    buttonSwitch,
                );
            }

            const waitingTasks = await waitingTasksCollection.collectAll();

            // @t3b-1955 HR práva ve WorkFlow
            const hasHrRights = await taskRepo.hasHrRights(
                this.currentUser,
                proc.IPROC_ID,
            );

            if (Array.isArray(waitingTasks) && waitingTasks.length > 0) {
                for (const waitingTask of waitingTasks) {
                    // If the User has HR rights, he should be able to activate all waiting Events
                    if (hasHrRights) {
                        out.push(waitingTask);
                    } else {
                        // Get all possible solvers.
                        const wf = new Workflow(this.orm, this.currentUser);

                        let possibleUsers;
                        if (
                            !waitingTask.ITASK_REFERENCE_USER ||
                            waitingTask.ITASK_REFERENCE_USER === "supervisor()"
                        ) {
                            possibleUsers = await wf.wfSolver.getPossibleUsers(
                                waitingTask,
                                proc,
                            );
                        } else {
                            possibleUsers = await wf.posibleUsers(waitingTask);
                        }

                        // List all possible users
                        if (
                            Array.isArray(possibleUsers) &&
                            possibleUsers.length > 0
                        ) {
                            // Search current user in list
                            const match = _.find(possibleUsers, {
                                USER_ID: this.currentUser.USER_ID,
                            });
                            if (match) {
                                out.push(waitingTask);
                            }
                        }
                    }
                }
            }
        }
        return out;
    }

    async activateProcess(process: Process): Promise<Workflow> {
        let userStart = this.currentUser;
        if (
            process.IPROC_INST_OWNER_USER_ID &&
            process.IPROC_INST_OWNER_USER_ID !== this.currentUser.USER_ID
        ) {
            userStart =
                await globalThis.container.service.auth.getUserInfoFromId(
                    process.IPROC_INST_OWNER_USER_ID,
                    true,
                );
            globalThis.tasLogger.info(
                `Using another user cache to start Process workflow. USER_ID = ${process.IPROC_INST_OWNER_USER_ID}`,
            );
        }

        const wf = new Workflow(this.orm, userStart);
        await wf.start(process);

        return wf;
    }
}
