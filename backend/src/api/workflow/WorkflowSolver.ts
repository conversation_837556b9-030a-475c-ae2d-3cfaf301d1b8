// @ts-nocheck
// @ts-nocheck
import * as TASK from "../orm/entity/const/taskConst";
import _ from "lodash";
import { IProcess } from "../orm/entity/Process";
import { ITask, Task } from "../orm/entity/Task";
import { IUser, User } from "../orm/entity/User";
import { IUserParameter } from "../orm/entity/UserVice";
import * as USER_PARAMETERS from "../orm/entity/const/userParameterConsts";
import { WorkflowBase } from "./WorkflowBase";
import { Knex } from "knex";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { SolverException } from "../../utils/errorHandling/exceptions/solverException";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { MailOptionsParams } from "../../client/mail/BaseMailClient";
import { ITaskSolverAssign } from "../../client/mail/emailClients/taskSolverAssign/TaskSolverAssignMailClient";
import { IToPullTask } from "../../client/mail/emailClients/toPullTask/ToPullTaskMailClient";
import { TaskJsCalculations } from "../../infrastructure/calculation/TaskJsCalculations";
import { UserApi } from "../../entrypoint/calculation/UserApi";
import { BaseApi } from "../../entrypoint/calculation/BaseApi";

import { htmlSanitize } from "../utils/functions";

interface IWorkflowSolverMethods {
    findSolver(task: ITask, process: IProcess): Promise<IUser | boolean>;
    selectSolverPull(process: IProcess, task: ITask): Promise<void>;
    sendToPullNotification(process: IProcess, task: ITask): Promise<void>;
    getPossibleUsersConnection(
        task: ITask,
        process: IProcess,
        multipleChairs?: boolean,
    ): Promise<{
        connection: Knex.QueryBuilder;
        referenceUser: User;
    }>;
    getPossibleUsers(
        task: ITask,
        process: IProcess,
        multipleChairs?: boolean,
    ): Promise<User[]>;
    getReferenceUser(task: ITask, process: IProcess): Promise<User>;
    getUserApi(user: IUser): Promise<any>;
    getSupervisor(task: ITask, process: IProcess): Promise<User | null>;
    selectSolverByLastSolverOfTask(process: IProcess, task: any): Promise<any>;
    getGuarantor(task: ITask, process: IProcess): Promise<User>;
    selectSolverByGuarantor(
        task: any,
        garant: IUser,
        process: IProcess,
    ): Promise<any>;
    getLastSolver(
        process: IProcess,
        taskId: number | string | ITask,
    ): Promise<number | null>;
    getDefaultChair(
        userId: number | null,
        task: any,
        process: IProcess,
    ): Promise<number | null>;
}

export interface IWorkflowSolver extends IWorkflowSolverMethods {}

/**
 * Process workflow.
 * Use task class to start Process, activate tasks and other workflow operations.
 *
 * <AUTHOR> Klima
 *
 */
export class WorkflowSolver extends WorkflowBase implements IWorkflowSolver {
    /**
     * Returns user solver for task based on settings in task.
     * task should contain: ITASK_AGAIN, ITASK_ASSESMENT_METHOD, IPROC_ID
     * Returns false when solver is not known (has to choose another user etc.)
     */
    async findSolver(task: any, process: IProcess): Promise<IUser | boolean> {
        const garant = await this.getGuarantor(task, process);
        try {
            // solver is known - return it
            if (task.ITASK_USER_ID) {
                return await this.orm
                    .repo("user")
                    .getNonDeletedById(task.ITASK_USER_ID)
                    .collectOne();
            }

            // Itask solver is defined in varaible.
            if (
                task.ITASK_ASSESMENT_METHOD === TASK.ASSESMENT_METHOD_VARIABLE
            ) {
                if (!task.ITASK_ASSESMENT_TVAR_ID || !task.IPROC_ID) {
                    throw new SolverException(
                        "Not enough input parameters.",
                        "BAD_CALL",
                    );
                }

                const variable = await this.workflow.orm
                    .repo("variable")
                    .getByTvarId(task.IPROC_ID, task.ITASK_ASSESMENT_TVAR_ID); // Get assesment variable
                const userId = variable.value;

                if (userId) {
                    const user = await this.orm
                        .repo("user")
                        .getNonDeletedById(userId)
                        .collectOne();
                    if (user) {
                        if (Array.isArray(userId) && userId.length > 1) {
                            globalThis.tasLogger.warning(
                                `The Solver is being chosen from a Variable, but it is multi-choice. The User '${user.USER_DISPLAY_NAME}' was chosen.`,
                                {
                                    iproc_id: task.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                    usedValue: user.USER_ID,
                                    values: userId,
                                },
                            );
                        }
                        return user;
                    }
                    throw new SolverException(
                        `Solver is not exist or active.`,
                        "BAD_USER",
                    );
                }

                // solver is selected by supervisor
            } else if (
                task.ITASK_ASSESMENT_METHOD ===
                    TASK.ASSESMENT_METHOD_SELECT_BY ||
                task.ITASK_ASSESMENT_METHOD === TASK.ASSESMENT_METHOD_SELECT
            ) {
                await this.selectSolverByGuarantor(task, garant, process);
                return false;

                // deprecated method - owner will be solver, is assigned to tasks by default (for ex. automatic tasks)
            } else if (
                task.ITASK_ASSESMENT_METHOD === TASK.ASSESMENT_METHOD_OWNER
            ) {
                // is not deprecated is archaic, is used
                // globalThis.tasLogger.error('ASSESMENT METHOD OWNER IS DEPRECATED - CHECK THIS OUT');
                throw new SolverException(
                    `Solver is deprecated ASSESMENT_METHOD_OWNER. Using garant.`,
                    "BAD_USER",
                ); // use garant, due to compatibility issue
                // solver is last solver of specific task
            } else if (
                task.ITASK_ASSESMENT_METHOD ===
                TASK.ASSESMENT_METHOD_LAST_SOLVER
            ) {
                const userId = await this.getLastSolver(
                    process,
                    task.ITASK_ASSESMENT_TTASK_ID!,
                );
                if (userId) {
                    return await this.orm
                        .repo("user")
                        .getNonDeletedById(userId)
                        .collectOne();
                }
                throw new SolverException(
                    `No last user found for TTASK_ID ${task.ITASK_ASSESMENT_TTASK_ID} - using garant: ${garant.USER_NAME}.`,
                    "BAD_USER",
                );

                // solver is selected by solver of another task
            } else if (
                task.ITASK_ASSESMENT_METHOD ===
                TASK.ASSESMENT_METHOD_LAST_SOLVER_CHOICE
            ) {
                await this.selectSolverByLastSolverOfTask(process, task);
                return false;

                // task will be as to-pull to specific solvers
            } else if (
                task.ITASK_ASSESMENT_METHOD === TASK.ASSESMENT_METHOD_PULL
            ) {
                await this.selectSolverPull(process, task);
                return false;

                // solver is chosen automatic with minimum tasks or by calculation of reference user
            } else if (
                task.ITASK_ASSESMENT_METHOD ===
                    TASK.ASSESMENT_METHOD_LEAST_WORKLOAD ||
                task.ITASK_ASSESMENT_METHOD === TASK.ASSESMENT_METHOD_AUTOMATIC
            ) {
                const referenceUser = await this.getReferenceUser(
                    task,
                    process,
                );

                // relation = only reference user
                if (
                    task.ITASK_ASSESMENT_HIERARCHY ===
                    TASK.ASSESMENT_HIERARCHY_REFERENCE
                ) {
                    globalThis.tasLogger.info(
                        `Selected solver as reference user: ${referenceUser.USER_ID} on chair: ${referenceUser.getActualChair()}`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );
                    return referenceUser;
                }

                const userRepo = this.orm.repo("user", this.orm.connection);

                const users = await userRepo.getUsersWithOrgStr(
                    referenceUser.USER_ID,
                    referenceUser.getActualChair(),
                    task,
                );

                // if no user was found, use garant
                if (!Array.isArray(users) || users.length === 0) {
                    throw new SolverException(
                        `Selected solver (not found any user - using garant): ${garant.USER_NAME}`,
                        "BAD_USER",
                    );
                }

                if (
                    task.ITASK_ASSESMENT_METHOD ===
                    TASK.ASSESMENT_METHOD_LEAST_WORKLOAD
                ) {
                    const usrIds = _.map(users, "USER_ID");

                    // Count tasks to solve for every possible user.
                    const userTasksCount = await this.orm
                        .repo("wfTask")
                        .getCountToSolveTasks(usrIds)
                        .fetchAll();

                    // Search user with minimum tasks to solve.
                    const minTaskUsers = [];
                    for (let i = 0; i < userTasksCount.length; i += 1) {
                        // Is first element or task count same as prev.
                        if (
                            minTaskUsers.length === 0 ||
                            minTaskUsers[0].CNT === userTasksCount[i].CNT
                        ) {
                            minTaskUsers.push(userTasksCount[i]);
                        } else {
                            // ends processing
                            break;
                        }
                    }

                    // return random user !
                    const random =
                        minTaskUsers[
                            Math.floor(Math.random() * minTaskUsers.length)
                        ];
                    const randomUser = await this.orm
                        .repo("user")
                        .getNonDeletedById(random.USER_ID)
                        .collectOne();
                    globalThis.tasLogger.info(
                        `Selected solver with least WORKLOAD (${random.CNT} tasks): ${randomUser.USER_NAME} ${randomUser.USER_ID}`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );
                    return randomUser;
                }

                // bad: users is choosen from more chairs
                const random = users[Math.floor(Math.random() * users.length)];
                const randomUser = await this.orm
                    .repo("user")
                    .getNonDeletedById((random as any).USER_ID)
                    .collectOne();
                globalThis.tasLogger.info(
                    `Selected solver randomly from ${users.length} users: ${randomUser.USER_NAME} ${randomUser.USER_ID}`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                return randomUser;
            }
            // no ITASK_ASSESMENT_METHOD is selected - default select garant as solver
            // use garant, due to compatibility and itask in bad states
            throw new SolverException(
                `Not known ITASK_ASSESMENT_METHOD '${task.ITASK_ASSESMENT_METHOD}' is selected. Using garant.`,
                "BAD_USER",
            );
        } catch (e: any) {
            // predicable error, missing User, return supervizor
            if (e instanceof SolverException && e.codeName === "BAD_USER") {
                globalThis.tasLogger.info(e.message, {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    error: e,
                });
                return garant;
            }
            throw e;
        }
    }

    async selectSolverPull(_process: IProcess, task: Task): Promise<void> {
        globalThis.tasLogger.info(`Task is to-pull to specific users.`);
        task.ITASK_STATUS = TASK.STATUS_PULL;
        task.ITASK_ACTUAL_DATE_START = new Date();
        task.ITASK_USER_ID = null;
        await this.orm.repo("task").store(task);
    }

    async sendToPullNotification(process: IProcess, task: any): Promise<any> {
        const possibleUsers = await this.getPossibleUsers(task, process);

        // Get all user (and vice) settings
        const users: any = {};
        const activeVices = await this.orm
            .repo("userVice")
            .getUsersViced(
                possibleUsers.map((user: IUser) => user.USER_ID),
                true,
            )
            .collectAll();
        const userIds = _.uniq(
            possibleUsers
                .map((user: IUser) => user.USER_ID)
                .concat(
                    activeVices.map(
                        (vice: IUserParameter) => vice.USER_ID_VICE,
                    ),
                ),
        );
        const enabledLanguages = await this.orm
            .repo("userParameter")
            .getUserParameter(userIds, USER_PARAMETERS.LANGUAGE_CLIENT);
        const enabledDateFormats = await this.orm
            .repo("userParameter")
            .getUserParameter(userIds, "DATE_FORMAT");
        const enabledNotifications = await this.orm
            .repo("userParameter")
            .getUserParameter(
                userIds,
                USER_PARAMETERS.SEND_TASK_TO_PULL_NOTIFICATION,
                true,
            );

        // Cache settings
        userIds.forEach((userId: any) => {
            const user: any =
                _.find(possibleUsers, { USER_ID: userId }) ||
                _.find(activeVices, { USER_ID_VICE: userId });
            const isVice = !!user.USER_ID_VICE;
            users[userId] = {
                USER_EMAIL: isVice ? user.raw.USER_VICE_EMAIL : user.USER_EMAIL,
                USER_LANGUAGE: (
                    (_.find(enabledLanguages, { USER_ID: userId }) as any) || {
                        USRPAR_VALUE: globalThis.dynamicConfig.defaultLang,
                    }
                ).USRPAR_VALUE,
                DATE_FORMAT: (
                    (_.find(enabledDateFormats, {
                        USER_ID: userId,
                    }) as any) || {
                        USRPAR_VALUE: "L",
                    }
                ).USRPAR_VALUE,
                NOTIFICATION_ENABLED: !!_.find(enabledNotifications, {
                    USER_ID: userId,
                }),
                UV_ID: isVice ? user.UV_ID : null,
                UV_USER: isVice ? `${user.raw.USER_VICE_DISPLAY_NAME}` : null,
                USER_FIRST_NAME: isVice
                    ? user.raw.USER_FIRST_NAME
                    : user.USER_FIRST_NAME,
                USER_LAST_NAME: isVice
                    ? user.raw.USER_LAST_NAME
                    : user.USER_LAST_NAME,
            };
        });

        // Get templates
        // Assert that process.TPROC_ID is not null
        const tProc = await this.wfProcess.getTproc(process.TPROC_ID!);
        const tTask = await this.wfTask.getTtask(task.TTASK_ID);

        // Prepare data to be rendered
        const iTaskToPullData: Record<string, any> = {
            itask_id: task.ITASK_ID,
            itask_name: task.ITASK_NAME,
            iproc_id: task.IPROC_ID,
            iproc_name: process.IPROC_NAME,
            tproc_name: tProc.TPROC_NAME,
            itask_due_date_finish: task.ITASK_DUE_DATE_FINISH,
            itask_comment: !task.ITASK_COMMENT
                ? ""
                : htmlSanitize(task.ITASK_COMMENT),
            itask_description: !task.ITASK_DESCRIPTION
                ? ""
                : htmlSanitize(task.ITASK_DESCRIPTION),
        };

        // Add language mutations
        globalThis.dynamicConfig.langs.forEach((lang: string) => {
            iTaskToPullData[`itask_comment_${lang}`] = !task[
                `ITASK_COMMENT_${lang.toUpperCase()}`
            ]
                ? ""
                : htmlSanitize(task[`ITASK_COMMENT_${lang.toUpperCase()}`]);
            // @ts-expect-error types...
            iTaskToPullData[`ttask_description_${lang}`] = !tTask[
                `TTASK_DESCRIPTION_${lang.toUpperCase()}`
            ]
                ? ""
                : htmlSanitize(
                      // @ts-expect-error types...
                      tTask[`TTASK_DESCRIPTION_${lang.toUpperCase()}`],
                  );
            iTaskToPullData[`ttask_name_${lang}`] =
                // @ts-expect-error types...
                tTask[`TTASK_NAME_${lang.toUpperCase()}`];
            iTaskToPullData[`tproc_name_${lang}`] =
                // @ts-expect-error types...
                tProc[`TPROC_NAME_${lang.toUpperCase()}`];
        });

        // Send emails to each user - asynchronously, else it could take a long time
        // > Always synchronous during tests for consistent results
        for (const userId of userIds.sort()) {
            const user = users[userId];
            // @ts-expect-error dunno
            const body: IToPullTask["data"] = _.cloneDeep(iTaskToPullData);
            // Does user have email and notification enabled?
            if (user.USER_EMAIL && user.NOTIFICATION_ENABLED) {
                // Set user name
                body.user_name = `${user.USER_DISPLAY_NAME}`;
                body.viceUser = user.UV_USER;

                // Is vice? Add 'uv-id'
                if (user.UV_ID) {
                    body.itask_id = `${iTaskToPullData.itask_id}?uv-id=${user.UV_ID}`;
                    body.iproc_id = `${iTaskToPullData.iproc_id}?uv-id=${user.UV_ID}`;
                }

                const header = await this.orm
                    .repo("header")
                    .get(process.HEADER_ID);

                // Add HEADER translations
                body.header_name = header.HEADER_NAME;
                globalThis.dynamicConfig.langs.forEach((language: string) => {
                    const headerName = `header_name_${language}`;
                    body[headerName] = header[headerName.toUpperCase()] || "";
                });

                // Need to render data for each user because of user name/vice
                const mailOptionsParams: MailOptionsParams = {
                    addresses: user.USER_EMAIL,
                    subject: `${globalThis.__({ phrase: "iTaskToPull", locale: user.USER_LANGUAGE })} | ${iTaskToPullData[`ttask_name_${user.USER_LANGUAGE}`] || task.ITASK_NAME}: ${iTaskToPullData.iproc_name}`,

                    bccOnMoreAddresses: true,
                    ignoreError: true,
                };

                try {
                    await globalThis.routerMail.sendEmailViaClient(
                        "toPullTask",
                        mailOptionsParams,
                        body,
                        user.USER_LANGUAGE,
                        user.DATE_FORMAT,
                    );
                    const firebaseTokens = await globalThis.orm
                        .repo("registeredMobileDevice")
                        .getAllByAttrAndIncreaseBadgeCount(userId);

                    await globalThis.container.client.firebase.fcm.sendITaskToPullNotification(
                        firebaseTokens,
                        iTaskToPullData,
                        user,
                        task.ITASK_NAME,
                    );
                } catch (err) {
                    globalThis.tasLogger.info(err.message, {
                        err,
                    });
                }
            }
        }
    }

    /**
     * param multipleChairs - Possibly include the same User multiple times, on different chairs
     */
    async getPossibleUsersConnection(
        task: ITask,
        process: IProcess,
        _multipleChairs: boolean = true,
    ): Promise<{
        connection: Knex.QueryBuilder;
        referenceUser: User;
    }> {
        const referenceUser = await this.getReferenceUser(task, process);

        if (
            !referenceUser.USER_ID ||
            typeof referenceUser.getActualChair() === "undefined"
        ) {
            throw new UserException(
                "Can not call getPossibleUsers(). UserId or OrgstrId is not defined.",
                "MISSING_PARAM",
            );
        }

        const userRepo = this.orm.repo("user");
        return {
            ...(await userRepo.getUsersWithOrgStrConnection(
                referenceUser.USER_ID,
                referenceUser.getActualChair(),
                task,
            )),
            referenceUser,
        };
    }

    /**
     * multipleChairs - Possibly include the same User multiple times, on different chairs
     */
    async getPossibleUsers(
        task: ITask,
        process: IProcess,
        multipleChairs: boolean = true,
    ): Promise<User[]> {
        const referenceUser = await this.getReferenceUser(task, process);

        if (
            !referenceUser.USER_ID ||
            typeof referenceUser.getActualChair() === "undefined"
        ) {
            throw new UserException(
                "Can not call getPossibleUsers(). UserId or OrgstrId is not defined.",
                "MISSING_PARAM",
            );
        }

        const userRepo = this.orm.repo("user");
        const possibleUsers = await userRepo.getUsersWithOrgStr(
            referenceUser.USER_ID,
            referenceUser.getActualChair(),
            task,
        );

        globalThis.tasLogger.info("Possible users", {
            iproc_id: process.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        globalThis.tasLogger.debug("Possible users (more detailed log)", {
            referenceUser,
            possibleUsers,
            iproc_id: process.IPROC_ID,
            itask_id: task.ITASK_ID,
        });

        return multipleChairs
            ? possibleUsers
            : _.uniqBy(possibleUsers, "USER_ID");
    }

    /**
     * Get reference user from task calculation.
     */
    async getReferenceUser(task: ITask, process: IProcess): Promise<User> {
        let refUser = task.ITASK_REFERENCE_USER;
        if (!refUser) {
            refUser = "supervisor()";
        }

        // Some optimalizations. Skip calculation return value directly.
        if (refUser === "supervisor()") {
            const user = await this.getSupervisor(task, process);
            if (user) {
                return user;
            }
        }

        // TODO optimize iterator(1). Not needed to be calculated.

        // Evaluate reference user.
        try {
            const calcs = new TaskJsCalculations(
                this.workflow.orm,
                globalThis.tasLogger,
            );
            calcs.setVariables(this.variableList.getVariables());
            // Set snapshots if multiinstance task.
            if (task.isMultiinstance()) {
                const snaps = await this.workflow.orm
                    .repo("variableSnap")
                    .getByTask(task.ITASK_ID)
                    .collectAll();
                calcs.setSnapshots(snaps);
            }
            calcs.setWorkflow(this.workflow);

            let userApi = await calcs.evaluate(
                this.currentUser,
                task,
                process,
                refUser,
            );

            // Calculation sometimes return user_id or userDisplayName .. Fix to Tas2 compatibility.
            if (!(userApi instanceof UserApi)) {
                // Is not object or is not valid UserApi...
                userApi = await this.getUserApi(userApi);
            }

            if (userApi && userApi.getUser()) {
                const user = userApi.getUser(); // only active users can act in tasks
                user.setActualChair(
                    user.getActualChair() === null
                        ? await this.getDefaultChair(
                              user.USER_ID,
                              task,
                              process,
                          )
                        : user.getActualChair(),
                );
                return user;
            }
        } catch (err: any) {
            globalThis.tasLogger.error(
                "Failed to get a Supervisor from calculation, falling back to getting a Guarantor.",
                {
                    calculation: task.ITASK_REFERENCE_USER,
                    message: err.message,
                    err,
                },
            );
        }

        return await this.getGuarantor(task, process);
    }

    /**
     * Transform user_id, user_name, userDisplayName, UserEntity to UserApi
     */
    getUserApi(user: any): any {
        try {
            const api = new BaseApi(this.orm.connection, {}, this.currentUser);
            return api.getUserApiFromInput(user);
        } catch (_err) {
            return user;
        }
    }

    async getSupervisor(task: ITask, process: IProcess): Promise<User | null> {
        try {
            return await this.getGuarantor(task, process);
        } catch (err: any) {
            globalThis.tasLogger.error(err.message, {
                err,
            });
            return null;
        }
    }

    /**
     * Task solver will be selected by last solver of task in ITASK_ASSESMENT_TTASK_ID attribute
     */
    async selectSolverByLastSolverOfTask(
        process: IProcess,
        task: any,
    ): Promise<any> {
        const userId = await this.getLastSolver(
            process,
            // Assert that task.ITASK_ASSESMENT_TTASK_ID is set
            task.ITASK_ASSESMENT_TTASK_ID!,
        );
        const now = new Date();
        if (!userId) {
            task.ITASK_STATUS = TASK.STATUS_WAITING;
            task.ITASK_ACTUAL_DATE_START = now;
            task.ITASK_USER_ID = null;
        } else {
            task.ITASK_STATUS = TASK.STATUS_WAITING;
            task.ITASK_ASSESMENT_USER_ID = userId;
            task.ITASK_ASSESMENT_METHOD = TASK.ASSESMENT_METHOD_SELECT_BY; // last solver is known, change to it
            task.ITASK_ACTUAL_DATE_START = now;
            task.ITASK_USER_ID = null;
        }
        return await this.orm.repo("task").store(task);
    }

    /**
     * Get Guarantor(supervisor) for task.
     * If set uses supervisor of task otherwise supervisor of process.
     * return {Promise<User>} User with actual chair.
     */
    async getGuarantor(task: ITask, process: IProcess): Promise<User> {
        globalThis.tasLogger.info(
            `Preselect getGuarantor for ${task.ITASK_NAME} with ITASK_ASSESMENT_USER_ID=${task.ITASK_ASSESMENT_USER_ID} and IPROC_INST_OWNER_USER_ID=${process.IPROC_INST_OWNER_USER_ID}`,
            {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
            },
        );
        let user;
        if (task.ITASK_ASSESMENT_USER_ID) {
            user = await this.orm
                .repo("user")
                .getNonDeletedById(task.ITASK_ASSESMENT_USER_ID)
                .collectOne();
            user.setActualChair(task.ITASK_ASSESMENT_ORGSTR_ID);
        } else {
            user = await this.orm
                .repo("user")
                .getNonDeletedById(process.IPROC_INST_OWNER_USER_ID)
                .collectOne();
            user.setActualChair(process.IPROC_INST_OWNER_ORGSTR_ID);
        }
        return user;
    }

    /**
     * Solver will be set by guarantor
     */
    async selectSolverByGuarantor(
        task: any,
        garant: IUser,
        process: IProcess,
    ): Promise<any> {
        task.ITASK_ACTUAL_DATE_START = new Date();
        task.ITASK_USER_ID = null;
        task.ITASK_STATUS = TASK.STATUS_WAITING;

        // Assert that process.IPROC_ID is not null
        const tproc = await this.wfProcess.getTproc(process.TPROC_ID!);
        const ttask = await this.wfTask.getTtask(task.TTASK_ID);

        const targets = [
            {
                USER_ID: garant.USER_ID,
                USER_EMAIL: garant.USER_EMAIL,
                USER_FIRST_NAME: garant.USER_FIRST_NAME,
                USER_LAST_NAME: garant.USER_LAST_NAME,
                UV_ID: null,
                UV_USER: "",
            },
        ];

        // Is user viced?
        const vice = await this.orm
            .repo("userVice")
            .getUsersViced(garant.USER_ID, true)
            .collectAll();
        if (vice && vice.length) {
            targets.push({
                USER_ID: vice[0].USER_ID_VICE,
                USER_EMAIL: vice[0].raw.USER_VICE_EMAIL,
                USER_FIRST_NAME: garant.USER_FIRST_NAME,
                USER_LAST_NAME: garant.USER_LAST_NAME,
                UV_ID: vice[0].UV_ID,
                UV_USER: `${vice[0].raw.USER_VICE_DISPLAY_NAME}`,
            });
        }

        for (const target of targets) {
            const notificationIsEnabled = !!(
                await this.orm
                    .repo("userParameter")
                    .getUserParameter(
                        target.USER_ID,
                        USER_PARAMETERS.SEND_NEW_TASK_NOTIFICATION,
                        true,
                    )
            ).length;
            if (target.USER_EMAIL && notificationIsEnabled) {
                const langParam = await this.orm
                    .repo("userParameter")
                    .getUserParameter(
                        target.USER_ID,
                        USER_PARAMETERS.LANGUAGE_CLIENT,
                    );
                const userLanguage = langParam.length
                    ? langParam[0].USRPAR_VALUE
                    : globalThis.dynamicConfig.defaultLang;

                const dateFormatParam = await this.orm
                    .repo("userParameter")
                    .getUserParameter(target.USER_ID, "DATE_FORMAT");
                const dateFormat = dateFormatParam.length
                    ? dateFormatParam[0].USRPAR_VALUE
                    : "L";

                const taskSolverAssignData: ITaskSolverAssign["data"] = {
                    user_name: target.USER_DISPLAY_NAME,
                    viceUser: target.UV_USER,
                    itask_id: task.ITASK_ID,
                    itask_name: task.ITASK_NAME,
                    iproc_id: task.IPROC_ID,
                    iproc_name: process.IPROC_NAME,
                    tproc_name: tproc.TPROC_NAME,
                    itask_due_date_finish: task.ITASK_DUE_DATE_FINISH,
                    itask_comment: !task.ITASK_COMMENT
                        ? ""
                        : htmlSanitize(task.ITASK_COMMENT),
                    itask_description: !task.ITASK_DESCRIPTION
                        ? ""
                        : htmlSanitize(task.ITASK_DESCRIPTION),
                };

                // Add 'uv-id'
                if (target.UV_ID) {
                    taskSolverAssignData.itask_id = `${taskSolverAssignData.itask_id}?uv-id=${target.UV_ID}`;
                    taskSolverAssignData.iproc_id = `${taskSolverAssignData.iproc_id}?uv-id=${target.UV_ID}`;
                }

                const header = await this.orm
                    .repo("header")
                    .get(process.HEADER_ID);

                // Add translations
                taskSolverAssignData.header_name = header.HEADER_NAME;
                globalThis.dynamicConfig.langs.forEach((language: string) => {
                    // Add COMMENT translations
                    const itaskComment = `itask_comment_${language}`;
                    taskSolverAssignData[itaskComment] = !task[
                        itaskComment.toUpperCase()
                    ]
                        ? ""
                        : htmlSanitize(task[itaskComment.toUpperCase()]);
                    // Add DESCRIPTION translations
                    const ttaskDesc = `ttask_description_${language}`;
                    // @ts-expect-error types...
                    taskSolverAssignData[ttaskDesc] = !ttask[
                        ttaskDesc.toUpperCase()
                    ]
                        ? ""
                        : htmlSanitize(
                              // @ts-expect-error types...
                              ttask[ttaskDesc.toUpperCase()],
                          );
                    // Add PROCESS translations
                    const tprocName = `tproc_name_${language}`;
                    taskSolverAssignData[tprocName] =
                        // @ts-expect-error types...
                        tproc[tprocName.toUpperCase()] || "";
                    // Add HEADER translations
                    const headerName = `header_name_${language}`;
                    taskSolverAssignData[headerName] =
                        header[headerName.toUpperCase()] || "";
                });

                const mailOptionsParams: MailOptionsParams = {
                    addresses: target.USER_EMAIL,
                    subject: `${globalThis.__({ phrase: "taskSolverAssign", locale: userLanguage })} | ${task.ITASK_NAME}`,

                    bccOnMoreAddresses: true,
                    ignoreError: true,
                };

                try {
                    await globalThis.routerMail.sendEmailViaClient(
                        "taskSolverAssign",
                        mailOptionsParams,
                        taskSolverAssignData,
                        userLanguage,
                        dateFormat,
                    );
                } catch (err: any) {
                    globalThis.tasLogger.warning({
                        err,
                    });
                    throw err;
                }
            }
        }
        return await this.orm.repo("task").store(task);
    }

    /**
     * param process Process entity
     * param task task Is ttaks_id, ttask_name or object containing TTASK_ID e.g. Task entity.
     */
    async getLastSolver(process: IProcess, task: any): Promise<number | null> {
        let ttaskId = null;
        if (typeof task === "string") {
            // Is ttask name ?
            const taskEntity = await this.orm
                .repo("instanceTask")
                .getByName(process.IPROC_ID, task)
                .collectOne();
            ttaskId = taskEntity.TTASK_ID;
        } else if (typeof task === "object" && task?.TTASK_ID) {
            // Is itask entity ?
            ttaskId = task.TTASK_ID;
        } else if (!isNaN(task)) {
            // is TTASK_ID ?
            ttaskId = task;
        }

        if (!ttaskId) {
            throw new Error(
                "Unknown task. Can not use as id, task_name neither Task entity.",
            );
        }

        // Need to store changed tasks before query. There can be unsaved changes.
        // t3b-765 Přiřazování řešitele - Poslední řešitel úkolu
        const changedTasks = Object.keys(this.workflow.taskList);
        if (Array.isArray(changedTasks) && changedTasks.length > 0) {
            for (let i = 0; i < changedTasks.length; i += 1) {
                const objectKey = changedTasks[i];
                const taskToStore = this.workflow.taskList[objectKey];
                await this.orm.repo("wfTask").store(taskToStore);
            }
        }

        return await this.orm.repo("wfTask").getLastSolver(process, ttaskId);
    }

    /**
     * Returns chair for user if not known. Use only as last choice of recognize chair.
     */
    async getDefaultChair(
        userId: number,
        task: any,
        process: IProcess,
    ): Promise<number | null> {
        if (userId === null) {
            return null;
        }

        // get all users orgstr_ids
        const orgs = await this.orm
            .repo("organizationStructure")
            .getUsersChairs(userId)
            .collectAll();
        // rule 0: if only one org then it is the only one
        if (orgs.length === 1) {
            // Log::logError('Rule0: orgId is '.$orgs[0]['ORGSTR_ID']);
            return orgs[0].ORGSTR_ID;
        }

        // rule 1: if isset ITASK_ASSESMENT_ORGSTR_CNST and user is in it then this is the right one
        if (task.ITASK_ASSESMENT_ORGSTR_CNST !== null) {
            for (const org of orgs) {
                if (org.ORGSTR_ID === task.ITASK_ASSESMENT_ORGSTR_CNST) {
                    // Log::logError('Rule1: orgId is '.$task['ITASK_ASSESMENT_ORGSTR_CNST']);
                    return task.ITASK_ASSESMENT_ORGSTR_CNST;
                }
            }
        }

        // rule 2: if user is case initiator then his chair is the right one
        if (task.IPROC_INST_OWNER_USER_ID === userId) {
            // Log::logError('Rule2: orgId is '.$info['IPROC_INST_OWNER_ORGSTR_ID']);
            return task.IPROC_INST_OWNER_ORGSTR_ID;
        }

        // rule 3: if user is guarantor then his chair is winning
        const guarantor = await this.getGuarantor(task, process);
        if (guarantor.USER_ID === userId) {
            // Log::logError('Rule3: orgId is '.$guarantor['orgstr_id']);
            return guarantor.getActualChair();
        }

        // rule 4: not implemented, higher chair depending on hierarchy

        // rule 5: higher chair from all possible
        // hack - doesn't use level
        let orgId = null;
        for (const org of orgs) {
            // filter out only managers - they have bigger rights then normals
            if (/* org.LVL < minLevel && */ org.MANAGER_USER_ID === userId) {
                // minLevel = org.LVL;
                orgId = org.ORGSTR_ID;
            }
        }
        if (orgId !== null) {
            // Log::logError('Rule5: orgId is '.$orgId);
            return orgId;
        }

        throw new InternalException(`Chair not found for user: ${userId}`);
    }
}
