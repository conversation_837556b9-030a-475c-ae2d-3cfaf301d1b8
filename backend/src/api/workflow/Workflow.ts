import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { Knex } from "knex";
import { OrmFactory } from "../orm/OrmFactory";
import { IEvent } from "../orm/entity/Event";
import { IProcess, Process } from "../orm/entity/Process";
import { ITask, Task } from "../orm/entity/Task";
import { IVariableList, VariableList } from "./VariableList";
import { WorkflowCondition } from "./WorkflowCondition";
import { WorkflowContext } from "./WorkflowContext";
import { WorkflowLink } from "./WorkflowLink";
import { WorkflowIterationPerf } from "./performance/WorkflowIterationPerf";
import { WorkflowProcessPerf } from "./performance/WorkflowProcessPerf";
import { WorkflowSolverPerf } from "./performance/WorkflowSolverPerf";
import { WorkflowTaskPerf } from "./performance/WorkflowTaskPerf";
import { WorkflowEvent } from "./WorkflowEvent";
import { UserCollection } from "../orm/collection/UserCollection";
import { User } from "../orm/entity/User";
import { TimeMeasure } from "../utils/TimeMeasure";
import { GuiDebugException } from "../../utils/errorHandling/exceptions/guiDebugException";
import { ProcessActivationException } from "../../utils/errorHandling/exceptions/processActivationException";
import { TasLogger } from "../../utils/logger/TasLogger";

export type TCalcExecType = "S" | "E" | "H" | "R";

interface IWorkflowAttributes {
    orm: OrmFactory;
    connection: Knex;
    logger: TasLogger;
    context: WorkflowContext;
}

interface IWorkflowMethods {
    start(process: IProcess): Promise<boolean>;
    solveTask(task: ITask): Promise<ITask[]>;
    activateTask(
        task: ITask,
        canFinish: boolean,
        linksSnap: any[],
        forceActivate: boolean,
    ): Promise<void>;
    recalculateTask(task: ITask, calcExecType: string): Promise<void>;
    addTask(currentTask: ITask, data: any): Promise<boolean>;
    activateEvent(event: IEvent): Promise<void>;
    getPossibleUsersCollection(task: ITask, process: IProcess): any;
    posibleUsers(task: ITask): Promise<User[]>;
    checkWorkflowConcurrency(process: IProcess): void;
    saveVariablesErroredProcess(variableList: IVariableList): Promise<boolean>;
}

export interface IWorkflow
    extends Partial<IWorkflowAttributes>,
        IWorkflowMethods {}

/**
 * Process workflow.
 * Use this class to start Process, activate tasks and other workflow operations.
 *
 */
export class Workflow implements IWorkflow {
    orm: OrmFactory;

    connection: Knex;

    logger: TasLogger;

    context: WorkflowContext;

    constructor(orm: OrmFactory, user: any) {
        this.orm = orm;
        this.connection = orm.connection;

        this.logger = globalThis.tasLogger;

        // setup service locator
        this.context = new WorkflowContext(
            new WorkflowProcessPerf(this),
            new WorkflowTaskPerf(this),
            new WorkflowCondition(this),
            new WorkflowLink(this),
            new WorkflowSolverPerf(this),
            new WorkflowIterationPerf(this),
            new WorkflowEvent(this),
            user, // user all operations doing under, needs to set before start
            // used only for single workflow
            new VariableList(),
            null,
            {}, // holder for currently db-loaded entities.
        );

        // Workaround for circular dependencies
        this.context.wfProcess.context = this.context;
        this.context.wfTask.context = this.context;
        this.context.wfCondition.context = this.context;
        this.context.wfLink.context = this.context;
        this.context.wfSolver.context = this.context;
        this.context.wfIteration.context = this.context;
        this.context.wfEvent.context = this.context;
    }

    /**
     * Activate process.
     **/
    async start(process: Process): Promise<boolean> {
        try {
            this.logger.info(`Activating process '${process.IPROC_NAME}'`, {
                iproc_id: process.IPROC_ID,
            });
            this.logger.debug(
                `Activating process '${process.IPROC_NAME}' - more details`,
                {
                    iproc_id: process.IPROC_ID,
                    process,
                },
            );

            this.checkWorkflowConcurrency(process);

            // retrieve tasks without incoming links as initial
            const initialTasks =
                await this.context.wfProcess.autostartInitialTasks(
                    process,
                    this.orm.repo("wfTask"),
                );

            // @t3b-817 proměnná sekvence se nanaplňuje při spuštění jako podproces
            // Fill and increase sequences - only for subprocess
            if (process.IPROC_MAIN_IPROC_ID) {
                await this.orm
                    .repo("instanceVariableSequence")
                    .updateSequenceForProcess(process);
            }
            // load and cache all variables for future use

            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = globalThis.orm
                .repo("variable", this.connection)
                .prepareCollectionsForVariableList(process.IPROC_ID);
            this.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );

            await this.context.variableList.setVariableUsages(
                await this.orm
                    .repo("templateTaskVarUsage")
                    .getUsagesForTemplateProcess(
                        process.TPROC_ID,
                        process.IPROC_ID,
                        (await this.context.wfProcess.processVersion(process))
                            .TTASKVARUSG_VERSION || 0,
                    )
                    .collectAll(),
            );

            // persist
            await this.orm.repo("wfTask").storeMulti(initialTasks);

            // and activate these
            await this.context.wfProcess.activateTasks(
                process,
                // Assert that currentUser is not null
                this.context.currentUser!,
                initialTasks,
            );
            await this.context.wfProcess.updateSummary(process);
        } catch (e: any) {
            this.logger.error(
                `Process '${process.IPROC_NAME}' activation error ${e.message}`,
                {
                    iproc_id: process.IPROC_ID,
                    err: e,
                },
            );
            // GuiDebugException
            if (e instanceof GuiDebugException) {
                // error raised in calculations
                throw e;
            }

            const procActError = new ProcessActivationException(
                e.message,
                e.codeName,
            );
            procActError.variableList = this.context.variableList;
            throw procActError; // in case of return, return variables to store
        }

        return true;
    }

    async solveTask(task: ITask): Promise<ITask[]> {
        const tm = new TimeMeasure();
        tm.start();

        this.logger.info(`Solving task '${task.ITASK_NAME}'`, {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        this.logger.debug(`Solving task '${task.ITASK_NAME}' - more details`, {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
            task,
        });

        const process = await this.orm.repo("process").get(task.IPROC_ID);
        this.checkWorkflowConcurrency(process);

        // load and cache all variables for future use
        const [
            variablesForProcessCollection,
            variablesForProcessLovsCollection,
        ] = this.orm
            .repo("variable")
            .prepareCollectionsForVariableList(process.IPROC_ID);
        this.context.variableList.setVariables(
            await variablesForProcessCollection.collectAll(),
            await variablesForProcessLovsCollection.collectAll(),
        );
        await this.context.variableList.setVariableUsages(
            await this.orm
                .repo("templateTaskVarUsage")
                .getUsagesForTemplateProcess(
                    process.TPROC_ID,
                    process.IPROC_ID,
                    (await this.context.wfProcess.processVersion(process))
                        .TTJSCALC_VERSION || 0,
                )
                .collectAll(),
        );

        // and finish
        await this.context.wfTask.finish(
            task,
            process,
            this.context.currentUser,
        );

        await this.context.wfProcess.updateSummary(process);

        const data = await this.context.wfTask.getTasksToSolve();

        return data;
    }

    async activateTask(
        task: ITask,
        canFinish: boolean = true,
        linksSnap: any[] = [],
        forceActivate: boolean = false,
    ): Promise<void> {
        try {
            this.logger.info(`Activating task '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            this.logger.debug(
                `Activating task '${task.ITASK_NAME}' - more details`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    task,
                },
            );

            const process = await this.orm.repo("process").get(task.IPROC_ID);
            this.checkWorkflowConcurrency(process);

            // needs variables
            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = this.orm
                .repo("variable")
                .prepareCollectionsForVariableList(process.IPROC_ID);
            this.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );
            await this.context.variableList.setVariableUsages(
                await this.orm
                    .repo("templateTaskVarUsage")
                    .getUsagesForTemplateProcess(
                        process.TPROC_ID,
                        process.IPROC_ID,
                        // Assert that task.IPROC_ID is not null
                        (
                            await this.context.wfProcess.processVersion(
                                task.IPROC_ID!,
                            )
                        ).TTJSCALC_VERSION || 0,
                    )
                    .collectAll(),
            );

            // and finish
            await this.context.wfTask.activate(
                task,
                process,
                canFinish,
                linksSnap,
                forceActivate,
            );
            await this.context.wfProcess.updateSummary(process);

            this.logger.info(`Task activated '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            this.logger.debug(
                `Task activated '${task.ITASK_NAME}' - more details`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    task,
                },
            );
        } catch (e: any) {
            this.logger.error(
                `Task activation error '${task.ITASK_NAME}' -> ${e.message}`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    err: e,
                    task,
                },
            );
            throw e;
        }
    }

    /**
     * Calculate task calculations with CALC_TYPE=H.
     */
    async recalculateTask(
        task: ITask,
        calcExecType: TCalcExecType,
    ): Promise<void> {
        try {
            this.logger.info(`Recalculating task '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            this.logger.debug(
                `Recalculating task '${task.ITASK_NAME}' - more details`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    task,
                },
            );

            const process = await this.orm.repo("process").get(task.IPROC_ID);
            this.checkWorkflowConcurrency(process);

            // needs variables
            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = this.orm
                .repo("variable", this.connection)
                .prepareCollectionsForVariableList(process.IPROC_ID);
            this.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );

            // and finish
            await this.context.wfTask.calculate(
                task,
                process,
                this.context.variableList,
                calcExecType,
            );
            await this.context.wfProcess.updateSummary(process);

            this.logger.info(`Task recalculated '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            this.logger.debug(
                `Task recalculated '${task.ITASK_NAME}' - more details`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    task,
                },
            );
        } catch (e: any) {
            this.logger.error(
                `Task recalculation error '${task.ITASK_NAME}' -> ${e.message}`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    err: e,
                    task,
                },
            );
            throw e;
        }
    }

    /**
     * Add/prepend currentTask to process.
     * currentTask New currentTask is added from this currentTask.
     */
    async addTask(currentTask: ITask, data: any): Promise<boolean> {
        try {
            this.logger.info(`Adding task '${currentTask.ITASK_NAME}'`, {
                iproc_id: currentTask.IPROC_ID,
                itask_id: currentTask.ITASK_ID,
            });
            this.logger.debug(
                `Adding task '${currentTask.ITASK_NAME}' - more details`,
                {
                    iproc_id: currentTask.IPROC_ID,
                    itask_id: currentTask.ITASK_ID,
                    currentTask,
                },
            );

            const process = await this.orm
                .repo("process")
                .get(currentTask.IPROC_ID);
            this.checkWorkflowConcurrency(process);

            // needs variables
            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = this.orm
                .repo("variable")
                .prepareCollectionsForVariableList(process.IPROC_ID);
            this.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );

            const newTask = await this.context.wfTask.addTask(
                currentTask,
                process,
                data,
            );
            await this.context.wfProcess.updateSummary(process);

            await this.logger.info(`Task added '${newTask.ITASK_NAME}'`, {
                iproc_id: newTask.IPROC_ID,
                itask_id: newTask.ITASK_ID,
                newTask,
                task: currentTask,
            });

            return true;
        } catch (e: any) {
            await this.logger.error(
                `Task add error '${currentTask.ITASK_NAME}' -> ${e.message}`,
                {
                    iproc_id: currentTask.IPROC_ID,
                    itask_id: currentTask.ITASK_ID,
                    err: e,
                    task: currentTask,
                },
            );
            throw e;
        }
    }

    /**
     * Activate waiting (SET) event.
     * Needs filled IPROC_ID.
     */
    async activateEvent(event: any): Promise<void> {
        try {
            if (typeof event.raw.IPROC_ID === "undefined") {
                throw new InternalException("Needs filled IPROC_ID.");
            }

            await this.logger.info(`Activating event '${event.EVEDEF_NAME}'`, {
                iproc_id: event.raw.IPROC_ID,
                event,
            });

            // Get event process.
            const process = await this.orm
                .repo("process")
                .get(event.raw.IPROC_ID);

            // Get variables
            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = this.orm
                .repo("variable")
                .prepareCollectionsForVariableList(process.IPROC_ID);
            this.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );

            // Get task entity. Itask id is known from EVENT_PARAM where paramName = $ITASK_ID
            const collection = this.orm
                .repo("eventParam")
                .getForEvent(event.EVE_ID);
            collection.knex.where("EVEPAR_NAME", "$ITASK_ID");
            const evePar = await collection.collectOne();
            const task = await this.orm
                .repo("instanceTask")
                .get(evePar.EVEPAR_NUMBER_VALUE);

            // Activate
            await this.context.wfEvent.activateIEvent(
                process,
                task,
                event,
                this.context.variableList.getVariables(),
            );
            await this.context.wfProcess.updateSummary(process);

            this.logger.info(`Event activated '${event.EVEDEF_NAME}'`, {
                iproc_id: event.raw.IPROC_ID,
            });
            this.logger.debug(
                `Event activated '${event.EVEDEF_NAME}' - more details`,
                {
                    iproc_id: event.raw.IPROC_ID,
                    event,
                },
            );
        } catch (e: any) {
            await this.logger.error(
                `Event activation error '${event.EVEDEF_NAME}' -> ${e.message}`,
                {
                    iproc_id: event.raw.IPROC_ID,
                    err: e,
                    event,
                },
            );
            throw e;
        }
    }

    /**
     * Same as `posibleUsers`, but allows for broad input of values,
     * which is useful for larger operations, where you can cache these values
     * - Does not execute the final query
     */
    async getPossibleUsersCollection(
        task: Task,
        process: Process,
    ): Promise<UserCollection> {
        this.checkWorkflowConcurrency(process);

        const { connection, referenceUser } =
            await this.wfSolver.getPossibleUsersConnection(task, process);
        const collection = globalThis.orm.collection("user", connection);
        collection.referenceUser = referenceUser;

        return collection;
    }

    /**
     * Finish task with solving it.
     */
    async posibleUsers(task: Task): Promise<User[]> {
        try {
            this.logger.info(`Possible users for '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            this.logger.debug(
                `Possible users for '${task.ITASK_NAME}' - more details`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    task,
                },
            );

            const process = await this.orm.repo("process").get(task.IPROC_ID);
            this.checkWorkflowConcurrency(process);

            const processVersion =
                await this.context.wfProcess.processVersion(process);

            // load and cache all variables for future use
            const [
                variablesForProcessCollection,
                variablesForProcessLovsCollection,
            ] = this.orm
                .repo("variable")
                .prepareCollectionsForVariableList(process.IPROC_ID);
            this.context.variableList.setVariables(
                await variablesForProcessCollection.collectAll(),
                await variablesForProcessLovsCollection.collectAll(),
            );
            await this.context.variableList.setVariableUsages(
                await this.orm
                    .repo("templateTaskVarUsage")
                    .getUsagesForTemplateProcess(
                        process.TPROC_ID,
                        process.IPROC_ID,
                        processVersion.TTASKVARUSG_VERSION || 0,
                    )
                    .collectAll(),
            );

            // and finish
            const users = await this.context.wfSolver.getPossibleUsers(
                task,
                process,
            );
            this.logger.info(`Possible users done for '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            this.logger.debug(
                `Possible users done for '${task.ITASK_NAME}' - more details`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    task,
                },
            );

            return users;
        } catch (e) {
            this.logger.info(`Possible users error for '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
                task,
            });
            throw e;
        }
    }

    /**
     * Check if workflow handle only one instance of process.
     */
    checkWorkflowConcurrency(process: Process): void {
        // Check if workflow already has assigned process.
        if (
            this.context.currentProcess &&
            this.context.currentProcess.IPROC_ID !== process.IPROC_ID
        ) {
            throw new InternalException(`Can not start process with current Workflow. 
                Workflow currently handle another process with IPROC_ID=${this.context.currentProcess.IPROC_ID}. 
                Please instantiate new Workflow() to start process with IPROC_ID = ${process.IPROC_ID}`);
        }

        // Assign current process.
        this.context.currentProcess = process;
    }

    /**
     * Save variables on last call (working with process)
     */
    async saveVariablesErroredProcess(
        variableList: VariableList,
    ): Promise<boolean> {
        const variableRepo = this.orm.repo("variable", this.orm.connection);

        for (const variable of variableList.getVariables()) {
            if (variable.IVAR_TYPE === "DT") {
                const dtRepo = this.orm.repo(
                    "dynamicTableValue",
                    this.orm.connection,
                );
                dtRepo.connection
                    .select(`COL_${variable.raw.dlc_id}`)
                    .from(dtRepo.tableName)
                    .where("DT_ID", variable.raw.dt_id)
                    .where("DLV_INDEX", `${variable.raw.dlv_index}`)
                    .then((rows: any) => {
                        if (!Array.isArray(rows) || rows.length == 0) {
                            throw new InternalException(
                                `No value for variable in dynamic table.`,
                            );
                        }
                        variable.value = rows[0][`COL_${variable.raw.dlc_id}`];
                        return variable;
                    });
            }
            await variableRepo.store(variable);
        }
        return true;
    }

    /**
     * Returns logger instance. (to call wf.log.error(), wf.log.info())
     */
    get log() {
        return this.logger;
    }

    get wfTask() {
        return this.context.wfTask;
    }

    get wfProcess() {
        return this.context.wfProcess;
    }

    get wfLink() {
        return this.context.wfLink;
    }

    get wfEvent() {
        return this.context.wfEvent;
    }

    get wfCondition() {
        return this.context.wfCondition;
    }

    get wfSolver() {
        return this.context.wfSolver;
    }

    get wfIteration() {
        return this.context.wfIteration;
    }

    get variableList() {
        return this.context.variableList;
    }

    get currentUser() {
        return this.context.currentUser;
    }

    get taskList() {
        return this.context.taskList;
    }
}
