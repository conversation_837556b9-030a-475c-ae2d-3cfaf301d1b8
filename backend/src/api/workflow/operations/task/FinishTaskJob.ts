import { <PERSON><PERSON> } from "knex";
import { OrmFactory } from "../../../orm/OrmFactory";
import { Workflow } from "../../Workflow";
import Operation from "../Operation";
import { LogCategory } from "../../../../utils/logger/logConsts";
import { AuthorizedUser } from "../../../../service/authorization/AuthorizedUser";

export class FinishTaskJob extends Operation {
    override async run(
        connection: Knex,
        itaskId: number,
        user: AuthorizedUser,
    ) {
        let task: any;
        try {
            const orm = new OrmFactory(
                connection,
                globalThis.dynamicConfig.db.client,
            );
            const repo = orm.repo("task", connection);

            task = await repo.get(itaskId);

            globalThis.tasLogger.info(
                `Action task/finish of task '${task.ITASK_NAME}' invoked by '${user.USER_NAME}'.`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: itaskId,
                },
            );

            const workflow = new Workflow(orm, user);
            await globalThis.tasLogger.runTask(async () => {
                globalThis.tasLogger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_WORKFLOW,
                );
                await workflow.solveTask(task);
            });

            globalThis.tasLogger.info(
                `Action task/finish of task '${task.ITASK_NAME}' invoked by '${user.USER_NAME}' finished.`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: itaskId,
                },
            );
        } catch (err: any) {
            globalThis.tasLogger.error(
                `Action task/finish of tasks ${itaskId} failed. ${err.message}`,
                {
                    itask_id: itaskId,
                    iproc_id: task?.IPROC_ID,
                    err,
                },
            );
            throw err;
        }
    }
}
