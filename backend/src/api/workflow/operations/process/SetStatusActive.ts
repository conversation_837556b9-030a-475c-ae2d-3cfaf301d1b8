import { <PERSON><PERSON> } from "knex";
import { OrmFactory } from "../../../orm/OrmFactory";
import { Workflow } from "../../Workflow";
import { WorkflowProcess } from "../../WorkflowProcess";
import Operation from "../Operation";
import { LogCategory } from "../../../../utils/logger/logConsts";
import { AuthorizedUser } from "../../../../service/authorization/AuthorizedUser";

export class SetStatusActive extends Operation {
    override async run(
        connection: Knex,
        iprocId: number,
        user: AuthorizedUser,
    ) {
        try {
            const orm = new OrmFactory(
                connection,
                globalThis.dynamicConfig.db.client,
            );
            const repo = orm.repo("process", connection);

            const process = await repo.get(iprocId);

            globalThis.tasLogger.info(
                `Action process/setStatusActive of process '${process.IPROC_NAME}' invoked by '${user.USER_NAME}'.`,
                {
                    iproc_id: iprocId,
                },
            );

            const workflow = new Workflow(orm, user);
            const workflowProcess = new WorkflowProcess(workflow);
            await globalThis.tasLogger.runTask(async () => {
                globalThis.tasLogger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_WORKFLOW,
                );
                await workflowProcess.setStatusActive(process);
            });

            globalThis.tasLogger.info(
                `Action process/setStatusActive of process '${process.IPROC_NAME}' invoked by '${user.USER_NAME}'.`,
                {
                    iproc_id: iprocId,
                },
            );
        } catch (err: any) {
            globalThis.tasLogger.info(
                `Action process/setStatusActive of process ${iprocId} failed.`,
                {
                    iproc_id: iprocId,
                },
            );
            throw err;
        }
    }
}
