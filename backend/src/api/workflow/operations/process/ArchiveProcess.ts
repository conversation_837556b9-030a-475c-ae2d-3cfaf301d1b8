import { <PERSON><PERSON> } from "knex";
import { OrmFactory } from "../../../orm/OrmFactory";
import Operation from "../Operation";
import { AuthorizedUser } from "../../../../service/authorization/AuthorizedUser";

export class ArchiveProcess extends Operation {
    override async run(
        connection: Knex,
        iprocId: number,
        user: AuthorizedUser,
    ) {
        try {
            const orm = new OrmFactory(
                connection,
                globalThis.dynamicConfig.db.client,
            );
            const repo = orm.repo("process", connection);

            const process = await repo.get(iprocId);

            globalThis.tasLogger.info(
                `Action archivation of process '${process.IPROC_NAME}' invoked by '${user.USER_NAME}'.`,
                {
                    iproc_id: iprocId,
                },
            );

            await repo.archive(iprocId);
            globalThis.tasLogger.info(
                `Archivation of process '${process.IPROC_NAME}' invoked by '${user.USER_NAME}' was successful.`,
                {
                    iproc_id: iprocId,
                },
            );
        } catch (err: any) {
            globalThis.tasLogger.info(
                `Action archivation of process ${iprocId} failed.`,
                {
                    iproc_id: iprocId,
                },
            );
            throw err;
        }
    }
}
