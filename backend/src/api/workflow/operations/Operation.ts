import { Knex } from "knex";
import { IUser } from "../../orm/entity/User";
import { AuthorizedUser } from "../../../service/authorization/AuthorizedUser";

export default class Operation {
    run(_connection: Knex, _batch: any, _user: IUser | AuthorizedUser) {
        throw new Error("To be implemented.");
    }
}

export type OperationType = {
    new (): {
        run(
            trx: Knex.Transaction,
            id: number,
            user: AuthorizedUser,
        ): Promise<void>;
    };
    name: string;
};
