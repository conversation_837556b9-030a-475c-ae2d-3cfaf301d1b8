import _ from "lodash";
import moment from "moment";
import { BaseCollection } from "../orm/BaseCollection";
import { InstanceTaskCompletition } from "../orm/entity/InstanceTaskCompletition";
import { IInstanceTaskLink } from "../orm/entity/InstanceTaskLink";
import { IProcess } from "../orm/entity/Process";
import { ITask, Task } from "../orm/entity/Task";
import { ITemplateTask } from "../orm/entity/TemplateTask";
import { IVariableList, VariableList } from "./VariableList";
import { TCalcExecType, Workflow } from "./Workflow";
import { WorkflowBase } from "./WorkflowBase";
import { IUser } from "../orm/entity/User";
import { VariableSnap } from "../orm/entity/VariableSnap";
import { Knex } from "knex";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { Invitation } from "../mails/Invitation";
import { DmsAccessLogger } from "../utils/DmsAccessLogger";
import { TimeMeasure } from "../utils/TimeMeasure";
import { htmlSanitize } from "../utils/functions";
import { SysVars } from "../orm/facade/SysVars";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { MailOptionsParams } from "../../client/mail/BaseMailClient";
import { ITaskTiming } from "../../client/mail/emailClients/taskTiming/TaskTimingMailClient";
import { Attachment } from "nodemailer/lib/mailer";
import { TaskJsCalculations } from "../../infrastructure/calculation/TaskJsCalculations";
import { LogCategory } from "../../utils/logger/logConsts";
import * as linkConsts from "../orm/entity/const/linkConst";
import * as processConsts from "../orm/entity/const/processConst";
import * as taskConsts from "../orm/entity/const/taskConst";
import * as calcConsts from "../orm/entity/const/calcConsts";
import * as variableConsts from "../orm/entity/const/variableConst";
import * as userParamConsts from "../orm/entity/const/userParameterConsts";

export interface IParseDue {
    type: "A" | "C" | null;
    interval: moment.Duration;
    date: Date;
}

interface IWorkflowTaskAttributes {}

interface IWorkflowTaskMethods {
    activate(
        task: ITask,
        process: IProcess,
        canFinish: boolean,
        linksSnap: IInstanceTaskLink[],
        forceActivate: boolean,
    ): Promise<boolean>;
    sendNotification(
        task: ITask,
        process: IProcess,
        customRecipients?: Record<string, any> | null,
    ): Promise<void>;
    incomingLinksFulfilled(
        task: ITask,
        linksSnap: IInstanceTaskLink[],
    ): Promise<{
        fulfilled: boolean;
    }>;
    incomingLinksFulfilledV2(task: ITask): Promise<{
        fulfilled: boolean;
        minimumLinkCount?: number;
        realFulfilledLinkCount?: number;
    }>;
    updateWaiting(
        task: ITask,
        process: IProcess,
        canFinish?: boolean,
    ): Promise<boolean>;
    finish(
        task: ITask,
        process: IProcess,
        finishUser?: any,
        canFinish?: boolean,
        alertMandatory?: boolean,
    ): Promise<boolean>;
    taskActivated(task: any, process: IProcess): Promise<void | number>;
    checkCanceling(task: Task, process: IProcess): Promise<any>;
    checkCompletion(
        task: Task,
        process: IProcess,
        canFinish?: boolean,
    ): Promise<boolean>;
    getCompletions(
        task: Task,
        toCancel?: boolean,
    ): Promise<BaseCollection<InstanceTaskCompletition>>;
    calculate(
        task: ITask,
        process: IProcess,
        variableList: IVariableList,
        execTyp: TCalcExecType,
    ): Promise<any>;
    parseDue(
        due: string,
        variableList: IVariableList | null,
        process: IProcess,
    ): Promise<IParseDue>;
    finishHard(
        task: ITask,
        process: IProcess,
        canFinish?: boolean,
    ): Promise<boolean>;
    cancel(task: any, _process: IProcess): Promise<number | number[] | null>;
    saveHistory(
        task: ITask,
        note?: string,
        userId?: number | null,
        _actualDate?: boolean,
    ): Promise<void>;
    addTask(currentTask: ITask, process: IProcess, data: any): Promise<ITask>;
    wakeupFromEventWait(itask: ITask): Promise<ITask[]>;
    delay(task: ITask): Promise<void>;
    changeSolver(
        userId: number,
        newUserId: number,
        headerId: number,
    ): Promise<number>;
    checkTaskDueDateSchedule(task: ITask, process: IProcess): Promise<void>;
    getTasksToSolve(): ITask[];
    getTtask(ttaskId: number): Promise<ITemplateTask>;
    sendTaskTimingNotification(task: ITask, process: IProcess): Promise<void>;
}

export interface IWorkflowTask
    extends IWorkflowTaskAttributes,
        IWorkflowTaskMethods {}

/**
 * Process workflow.
 * Use task class to start Process, activate tasks and other workflow operations.
 *
 * <AUTHOR> Klima
 *
 */
export class WorkflowTask extends WorkflowBase implements IWorkflowTask {
    private _taskToSolve: ITask[];

    lastClonedTask: any;

    constructor(workflow: Workflow) {
        super(workflow);
        this._taskToSolve = []; // Tasks activated by workflow to be solved by user.
    }

    /**
     * activate - activates task. Creates copy of changeable variables into local snapshot.
     * Must perform check whether task can be opened (incoming mandatory links must be fulfilled and sufficient links number must be fulfilled)
     *
     * @param {Task} task - automated tasks will not end case if set to false
     * @param {Process} process - automated tasks will not end case if set to false
     * @param {boolean} canFinish - automated tasks will not end case if set to false
     * @param {Array} linksSnap
     * @param {boolean} forceActivate - if true then some activation checks are

     * @return {boolean}
     */
    async activate(
        task: any,
        process: IProcess,
        canFinish: boolean = true,
        linksSnap: IInstanceTaskLink[] = [],
        forceActivate: boolean = false,
    ): Promise<boolean> {
        const tm = new TimeMeasure();
        tm.start("activate");

        try {
            globalThis.tasLogger.info(
                `➽ Activating Task '${task.ITASK_NAME}' with id ${task.ITASK_ID}.`,
                {
                    iproc_id: process.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    js_stack: new Error().stack,
                },
            );
            this.workflow.taskList[task.ITASK_ID] = task; // Task was loaded. Add to cache.

            // do not activate tasks in non-activate processes
            if (process.IPROC_STATUS !== processConsts.STATUS_ACTIVE) {
                globalThis.tasLogger.info(
                    `Process is not activated. Is '${process.IPROC_STATUS}'`,
                    {
                        iproc_id: process.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                return true;
            }
            tm.start("fullfilled");

            const procVersion = await this.wfProcess.processVersion(
                task.IPROC_ID,
            );
            const incomingLinks =
                procVersion.TTASKLINK_VERSION > 0
                    ? await this.incomingLinksFulfilledV2(task)
                    : await this.incomingLinksFulfilled(task, linksSnap);

            const duration = tm.end("fullfilled");
            if (
                duration >
                globalThis.dynamicConfig.tas.measurement.incomingLinks
            ) {
                globalThis.tasLogger.warning(
                    `Detecting incoming links for '${task.ITASK_NAME}' fullfilled takes too long ($measure=${duration}s)`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
            }

            // Multi task can not be forced.
            if (incomingLinks.minimumLinkCount !== 1) {
                forceActivate = false;
            }

            if (forceActivate && !incomingLinks.fulfilled) {
                globalThis.tasLogger.warning(
                    `The task '${task.ITASK_NAME}' has not been fulfilled. Activation should be forced. Deprecated since version 5.10.2`,
                    {
                        isForced: false,
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                        incomingLinks,
                    },
                );
            }

            if (incomingLinks.fulfilled) {
                // Iterations
                if (task.isIteratedStandard()) {
                    await this.wfIteration.setupIteration(task);
                    await this.wfIteration.getNextIteration(task);
                    await this.orm.repo("task").store(task);
                }

                // next method updates some parameters as due date start or sets task to waiting state for event
                const res = await this.updateWaiting(task, process, canFinish);

                if (!res) {
                    if (task.ITASK_TYPE === taskConsts.TYPE_EVENT_WAIT) {
                        await this.calculate(
                            task,
                            process,
                            this.variableList,
                            calcConsts.EXEC_ON_START,
                        );
                    }
                    // due date set, task activated
                    return true;
                }

                // start when offset is fulfilled
                const cdate = new Date();

                // refreshment necessary after update waiting
                if (
                    !task.ITASK_DUE_DATE_START ||
                    (task.ITASK_DUE_DATE_START &&
                        cdate.getTime() >= task.ITASK_DUE_DATE_START.getTime())
                ) {
                    if (task.ITASK_DUE_DATE_START) {
                        globalThis.tasLogger.info(
                            `Task should start ${task.ITASK_DUE_DATE_START.toLocaleString()}`,
                            {
                                iproc_id: process.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                        globalThis.tasLogger.info(
                            task.ITASK_DUE_DATE_START.getTime(),
                            {
                                iproc_id: process.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                        globalThis.tasLogger.info(cdate.getTime().toString(), {
                            iproc_id: process.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        });
                    }
                    globalThis.tasLogger.info(
                        `Due date start fulfilled, method: ${task.ITASK_ASSESMENT_METHOD}, hierarchy: ${task.ITASK_ASSESMENT_HIERARCHY}, user_id: ${task.ITASK_ASSESMENT_USER_ID}`,
                        {
                            iproc_id: process.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );

                    // lets set guarantor if it has to be manager of org. structure
                    if (
                        task.ITASK_ASSESMENT_USER_ID === null &&
                        task.ITASK_ASSESMENT_ORGSTR_ID !== null
                    ) {
                        const os = await this.workflow.orm
                            .repo("organizationStructure")
                            .getById(task.ITASK_ASSESMENT_ORGSTR_ID)
                            .collectOne();

                        // manager is not set
                        if (os.MANAGER_USER_ID !== null) {
                            task.ITASK_ASSESMENT_USER_ID = os.MANAGER_USER_ID;
                            await this.orm.repo("task").store(task);
                            globalThis.tasLogger.info(
                                `Guarant set by org. struct to ${task.ITASK_ASSESMENT_USER_ID}`,
                                {
                                    iproc_id: process.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                },
                            );

                            // Add read access to user for process.
                            await this.orm
                                .repo("externalRight")
                                .assignUserRights(
                                    task.IPROC_ID,
                                    task.ITASK_ASSESMENT_USER_ID,
                                );
                        }
                    }

                    if (
                        task.ITASK_TYPE === taskConsts.TYPE_STANDARD ||
                        task.ITASK_TYPE === taskConsts.TYPE_SUBPROCESS ||
                        task.ITASK_TYPE === taskConsts.TYPE_EVENT ||
                        task.ITASK_TYPE === taskConsts.TYPE_INVITATION
                    ) {
                        // in all these tasks types is solver important

                        // task is in repeated activation with reevaluation
                        if (
                            task.ITASK_AGAIN === taskConsts.AGAIN &&
                            task.ITASK_STATUS === taskConsts.STATUS_DONE
                        ) {
                            globalThis.tasLogger.info(
                                `Repeated activation of task with reevaluation,`,
                                {
                                    iproc_id: process.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                },
                            );
                            task.ITASK_USER_ID = null; // clear previous solver
                        }

                        // set task deadline
                        if (
                            task.ITASK_DURATION !== null &&
                            task.ITASK_DURATION !== "po"
                        ) {
                            let date;
                            const offset = await this.parseDue(
                                task.ITASK_DURATION,
                                this.variableList,
                                process,
                            );
                            if (offset.type === "T") {
                                // offset from task
                                date = moment();
                                date.add(offset.interval);
                                task.ITASK_DURATION = null;
                            } else if (offset.type === "P") {
                                // offset from case
                                date = moment(process.IPROC_ACTUAL_START_DATE);
                                date.add(offset.interval);
                                task.ITASK_DURATION = null;
                            } else if (
                                offset.type === "A" ||
                                offset.type === "C"
                            ) {
                                // absolute date
                                date = offset.date;
                                // in non continous changing deadline remove info about duration - it will not be used anymore
                                if (offset.type === "A") {
                                    task.ITASK_DURATION = null;
                                }
                            }

                            task.ITASK_DUE_DATE_FINISH = date;
                            await this.orm.repo("task").store(task);
                        }

                        let solverUser = await this.wfSolver.findSolver(
                            task,
                            process,
                        ); // it doesn't need to be used (event?)
                        // if we have no user then task is delayed, just exit
                        if (!solverUser) {
                            const possibleUsers =
                                await this.wfSolver.getPossibleUsers(
                                    task,
                                    process,
                                    false,
                                );

                            // run calculations for task before pull
                            if (task.ITASK_STATUS === taskConsts.STATUS_PULL) {
                                await this.calculate(
                                    task,
                                    process,
                                    this.variableList,
                                    calcConsts.EXEC_PRE_PULL,
                                );
                            }

                            // only one solver for pull-tasks
                            if (
                                task.ITASK_STATUS === taskConsts.STATUS_PULL &&
                                Array.isArray(possibleUsers) &&
                                possibleUsers.length === 1
                            ) {
                                solverUser = possibleUsers[0];
                                globalThis.tasLogger.info(
                                    `Only one possible solver found for task '${task.ITASK_NAME}' with type ${task.ITASK_TYPE}.`,
                                    {
                                        iproc_id: process.IPROC_ID,
                                        itask_id: task.ITASK_ID,
                                    },
                                );
                            } else {
                                globalThis.tasLogger.info(
                                    `No solver found for task '${task.ITASK_NAME}' with type ${task.ITASK_TYPE}, task is delayed.`,
                                    {
                                        iproc_id: process.IPROC_ID,
                                        itask_id: task.ITASK_ID,
                                    },
                                );

                                if (
                                    task.ITASK_STATUS === taskConsts.STATUS_PULL
                                ) {
                                    const possibleUserIds = (
                                        await this.wfSolver.getPossibleUsers(
                                            task,
                                            process,
                                        )
                                    ).map((user: any) => user.USER_ID);
                                    const activeVicesIds = (
                                        await this.orm
                                            .repo("userVice")
                                            .getUsersViced(
                                                possibleUserIds,
                                                true,
                                            )
                                            .collectAll()
                                    ).map((vice: any) => vice.USER_ID_VICE);
                                    const possibleUsersWithVices =
                                        possibleUserIds.concat(activeVicesIds);
                                    await this.orm
                                        .repo("externalRight")
                                        .assignUserRights(
                                            task.IPROC_ID,
                                            possibleUsersWithVices,
                                            "pull",
                                        );
                                    await this.wfSolver.sendToPullNotification(
                                        process,
                                        task,
                                    );
                                }

                                // it can invoke process finish if no other task is active
                                // check if task can be finished with completion
                                await this.checkCanceling(task, process);
                                await this.checkCompletion(
                                    task,
                                    process,
                                    canFinish,
                                );
                                return true;
                            }
                        }

                        // Add read access to user for process.
                        if (task.ITASK_ASSESMENT_USER_ID !== null) {
                            // sometimes is supervisor inherits from process
                            await this.orm
                                .repo("externalRight")
                                .assignUserRights(
                                    task.IPROC_ID,
                                    task.ITASK_ASSESMENT_USER_ID,
                                );
                        }

                        // set actual task start
                        if (task.ITASK_STATUS !== taskConsts.STATUS_ACTIVE) {
                            task.ITASK_ACTUAL_DATE_START = new Date();
                        }

                        // DSI 25.7.2011 - added for event processing - event needs user_id for processing
                        if (task.ITASK_TYPE === taskConsts.TYPE_EVENT) {
                            // Load snapshots if necessary
                            let snapshotVariables: VariableSnap[] = [];
                            if (task.isMultiinstance()) {
                                snapshotVariables = await this.workflow.orm
                                    .repo("variableSnap")
                                    .getByTask(task.ITASK_ID)
                                    .collectAll();
                            }

                            // Activate event.
                            await this.wfEvent.activateEventTask(
                                process,
                                task,
                                this.variableList,
                                snapshotVariables,
                            );

                            // Modify status
                            task.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
                            task.ITASK_AUTO_START = taskConsts.AUTOSTART_NO;
                            task.ITASK_ACTUAL_DATE_START = new Date();
                            task.ITASK_USER_ID = null;
                            task.ITASK_ORGSTR_ID = null;
                            task.ITASK_RUN_COUNT += 1;

                            // Persist task.
                            await this.orm.repo("task").store(task);

                            await this.taskActivated(task, process);
                            await this.finish(
                                task,
                                process,
                                this.currentUser,
                                canFinish,
                            );
                            return true;
                        }
                        // END DSI 25.7.2011 - event processing

                        // invitation - send invititation with the name of current solver
                        if (task.ITASK_TYPE === taskConsts.TYPE_INVITATION) {
                            // invitation is normal task for now
                            /* globalThis.tasLogger.task.info('Task is invitation');
                            ndata = array('ITASK_STATUS'=ITask.STATUS_ACTIVE,
                                          'ITASK_AUTO_START'=Itask.AUTOSTART_NO,
                                          'ITASK_ACTUAL_DATE_START'=new nDateTime(),
                                          'ITASK_USER_ID'=user.USER_ID,
                                          'ITASK_ORGSTR_ID'=null,
                                          'ITASK_RUN_COUNT'=(task.ITASK_RUN_COUNT+1)
                            );
                            data = array_merge(data, ndata);
                            this.infoCache = array_merge(this.infoCache, data);
                            dbSetAttributes(this.conn, 'INSTANCE_TASKS', data, 'ITASK_ID = :ITASK_ID',
                                array('ITASK_ID'=this.getId()));
                            this.sendInvitation(user.USER_ID);
                            this.getProcess().updateSummary();
                            this.conn.completeTrans();
                            this.finish(canFinish);
                            return true; */
                        }
                        // end of invitation

                        if (task.ITASK_TYPE === taskConsts.TYPE_SUBPROCESS) {
                            // pokud je to podproces, tak vytvorim eventu
                            task.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
                            task.ITASK_AUTO_START = taskConsts.AUTOSTART_NO;
                            task.ITASK_ACTUAL_DATE_START = new Date();
                            task.ITASK_USER_ID = null;
                            task.ITASK_ORGSTR_ID = null;
                            task.ITASK_RUN_COUNT += 1;

                            await this.calculate(
                                task,
                                process,
                                this.variableList,
                                calcConsts.EXEC_ON_START,
                            );
                            await this.taskActivated(task, process);

                            task.ITASK_USER_ID = solverUser.USER_ID;
                            // deprecated
                            task.ITASK_ORGSTR_ID = null;
                            // task.ITASK_ORGSTR_ID = this.findOrgStrId(user.USER_ID, info);
                            const event =
                                await this.wfEvent.createIEventFromITask(
                                    process,
                                    task,
                                );
                            globalThis.tasLogger.info(
                                `Subprocess event generated with eve_id = ${event.EVE_ID}`,
                                {
                                    iproc_id: process.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                },
                            );
                            // this is subprocess, we activate event immediate
                            await this.wfEvent.activateIEvent(
                                process,
                                task,
                                event,
                                this.variableList.getVariables(),
                            );
                            return true;
                        }

                        /** ******************** STANDARD TASK TYPE ****************************** */

                        task.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
                        task.ITASK_AUTO_START = taskConsts.AUTOSTART_NO;
                        task.ITASK_USER_ID = solverUser.USER_ID;
                        task.ITASK_RUN_COUNT += 1;
                        task.ITASK_ACTUAL_DATE_FINISH = null;
                        // task.ITASK_ACTUAL_DATE_START = null;
                        task.ITASK_ORGSTR_ID = null;

                        if (!task.ITASK_USER_ID) {
                            globalThis.tasLogger.error(
                                `Cannot activate task '${task.ITASK_NAME}' without user.`,
                                {
                                    iproc_id: process.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                    task,
                                },
                            );
                            throw new InternalException(
                                "Cannot activate task without user",
                            );
                        }

                        // deprecated
                        // task.ITASK_ORGSTR_ID = this.findOrgStrId(task.ITASK_USER_ID, task);

                        // Add read access to user for process.
                        await this.orm
                            .repo("externalRight")
                            .assignUserRights(
                                task.IPROC_ID,
                                solverUser.USER_ID,
                            );

                        // store data in task
                        // lets make variable calculations
                        await this.calculate(
                            task,
                            process,
                            this.variableList,
                            calcConsts.EXEC_ON_START,
                        );
                        await this.orm.repo("task").store(task);
                        globalThis.tasLogger.info(
                            `Task '${task.ITASK_NAME}' activated ($measure=${tm.end("activate")}s).`,
                            {
                                iproc_id: process.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                        await this.taskActivated(task, process);
                        this._taskToSolve.push(task);

                        // Log.logMessage('sending mail to: ' . mailUser);
                        await this.checkCanceling(task, process);
                        await this.checkCompletion(task, process, canFinish);

                        // Check settings in Cron, not here
                        // const solverUserMailSettings = await this.orm.repo('UserParameter').getUserEnabledMailSettings(solverUser.USER_ID);
                        // const newTaskNotificationEnabled = _.find(solverUserMailSettings, { USRPAR_NAME: USER_PARAMETERS.SEND_NEW_TASK_NOTIFICATION });

                        if (solverUser.USER_EMAIL) {
                            const tproc = await this.wfProcess.getTproc(
                                // Assert that process TPROC_ID is not null
                                process.TPROC_ID!,
                            );
                            const ttask = await this.wfTask.getTtask(
                                task.TTASK_ID,
                            );
                            const header = await this.orm
                                .repo("header")
                                .get(process.HEADER_ID);

                            const iTaskActivatedData: Record<string, any> = {
                                user_name: solverUser.USER_DISPLAY_NAME,
                                itask_id: task.ITASK_ID,
                                itask_name: task.ITASK_NAME,
                                iproc_id: task.IPROC_ID,
                                iproc_name: process.IPROC_NAME,
                                tproc_name: tproc.TPROC_NAME,
                                header_name: header.HEADER_NAME,
                                itask_due_date_finish:
                                    task.ITASK_DUE_DATE_FINISH,
                                itask_comment: !task.ITASK_COMMENT
                                    ? ""
                                    : htmlSanitize(task.ITASK_COMMENT),
                                itask_description: !task.ITASK_DESCRIPTION
                                    ? ""
                                    : htmlSanitize(task.ITASK_DESCRIPTION),
                            };

                            // Add COMMENT, DESCRIPTION, TASK, PROCESS, HEADER translations
                            globalThis.dynamicConfig.langs.forEach(
                                (language: string) => {
                                    const itaskComment = `itask_comment_${language}`;
                                    iTaskActivatedData[itaskComment] = !task[
                                        itaskComment.toUpperCase()
                                    ]
                                        ? ""
                                        : htmlSanitize(
                                              task[itaskComment.toUpperCase()],
                                          );
                                    const ttaskDesc = `ttask_description_${language}`;
                                    //@ts-expect-error
                                    iTaskActivatedData[ttaskDesc] = !ttask[
                                        ttaskDesc.toUpperCase()
                                    ]
                                        ? ""
                                        : htmlSanitize(
                                              //@ts-expect-error
                                              ttask[ttaskDesc.toUpperCase()],
                                          );
                                    const ttaskName = `ttask_name_${language}`;
                                    //@ts-expect-error
                                    iTaskActivatedData[ttaskName] = !ttask[
                                        ttaskName.toUpperCase()
                                    ]
                                        ? ""
                                        : htmlSanitize(
                                              //@ts-expect-error
                                              ttask[ttaskName.toUpperCase()],
                                          );
                                    const tprocName = `tproc_name_${language}`;
                                    iTaskActivatedData[tprocName] =
                                        //@ts-expect-error
                                        tproc[tprocName.toUpperCase()] || "";
                                    const headerName = `header_name_${language}`;
                                    iTaskActivatedData[headerName] =
                                        header[headerName.toUpperCase()] || "";
                                },
                            );

                            // Get user language for accurate subject (includes mutation)
                            const langParam = await this.orm
                                .repo("userParameter")
                                .getUserParameter(
                                    solverUser.USER_ID,
                                    userParamConsts.LANGUAGE_CLIENT,
                                );
                            const userLanguage = langParam.length
                                ? langParam[0].USRPAR_VALUE
                                : globalThis.dynamicConfig.defaultLang;
                            const langSubjAttr =
                                `TTASK_NAME_${userLanguage}`.toLowerCase();

                            const mailQrepo = this.orm.repo("mailQ");
                            await mailQrepo
                                .generateTemplateMail(
                                    // no await for async sending emails
                                    solverUser,
                                    "newTask",
                                    {
                                        MAQU_SUBJECT: {
                                            phrase: `iTaskActivated`,
                                            text: `| ${iTaskActivatedData[langSubjAttr] || task.ITASK_NAME}: ${process.IPROC_NAME}`,
                                        },
                                        ITASK_ID: task.ITASK_ID,
                                    },
                                    iTaskActivatedData,
                                )
                                .catch((err: any) => {
                                    globalThis.tasLogger.warning(err.message, {
                                        iproc_id: process.IPROC_ID,
                                        itask_id: task.ITASK_ID,
                                        err,
                                    });
                                    throw err;
                                });
                        }

                        /** ******************** SOCKETS *********************** */
                        const taskInfo = task.getAttributes();
                        const tproc = await this.wfProcess.getTproc(
                            // Assert that process TPROC_ID is not null
                            process.TPROC_ID!,
                        );
                        const ttask = await this.wfTask.getTtask(task.TTASK_ID);
                        const header = await this.orm
                            .repo("header")
                            .get(process.HEADER_ID);
                        taskInfo.IPROC_NAME = process.IPROC_NAME;
                        taskInfo.TPROC_NAME = tproc.TPROC_NAME;
                        taskInfo.TTASK_NAME = ttask.TTASK_NAME;
                        taskInfo.HEADER_NAME = header.HEADER_NAME;
                        // copy all translations.
                        if (globalThis.dynamicConfig.langs) {
                            globalThis.dynamicConfig.langs.forEach(
                                (lang: string) => {
                                    const tprocName = `TPROC_NAME_${lang.toUpperCase()}`;
                                    //@ts-expect-error
                                    taskInfo[tprocName] = tproc[tprocName];
                                    const headerName = `HEADER_NAME_${lang.toUpperCase()}`;
                                    taskInfo[headerName] = header[headerName];
                                    const itaskName = `TTASK_NAME_${lang.toUpperCase()}`;
                                    //@ts-expect-error
                                    taskInfo[itaskName] = ttask[itaskName];
                                },
                            );
                        }
                    } else if (
                        task.ITASK_TYPE === taskConsts.TYPE_AUTOMAT ||
                        task.ITASK_TYPE === taskConsts.TYPE_EVENT ||
                        task.ITASK_TYPE === taskConsts.TYPE_EVENT_WAIT
                    ) {
                        globalThis.tasLogger.info(
                            `Task '${task.ITASK_NAME}' is automatic (or event).`,
                            {
                                iproc_id: process.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                        await this.calculate(
                            task,
                            process,
                            this.variableList,
                            calcConsts.EXEC_ON_START,
                        );

                        /**
                        if (task.ITASK_TYPE === TASK.TYPE_EVENT){
                            // pokud je typu udalost, tak spustim udalost
                            //Log.logError(this.getId());
                            task.ITASK_USER_ID = user.USER_ID;
                            task.ITASK_ORGSTR_ID = this.findOrgStrId(user.USER_ID, task);

                            eve_id = IEvent.createIEventFromITask(array_merge(info, itaskUser));
                            this._activateEvent(eve_id);
                            globalThis.tasLogger.info('Event generated in automatic task with eve_id = '.eve_id);
                        } */

                        task.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
                        task.ITASK_AUTO_START = taskConsts.AUTOSTART_NO;
                        task.ITASK_ACTUAL_DATE_START = new Date();
                        task.ITASK_USER_ID = null;
                        task.ITASK_ORGSTR_ID = null;
                        task.ITASK_RUN_COUNT += 1;
                        await this.orm.repo("task").store(task);
                        await this.taskActivated(task, process);

                        const finishedBy =
                            task.ITASK_TYPE === taskConsts.TYPE_AUTOMAT
                                ? null
                                : this.currentUser; // Event should have solver !
                        await this.finish(task, process, finishedBy, canFinish);
                    } else if (
                        task.ITASK_TYPE === taskConsts.TYPE_EMAIL_NOTIFICATION
                    ) {
                        globalThis.tasLogger.info(
                            "Task is email notification.",
                            {
                                iproc_id: process.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                        await this.calculate(
                            task,
                            process,
                            this.variableList,
                            calcConsts.EXEC_ON_START,
                        );

                        task.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
                        task.ITASK_AUTO_START = taskConsts.AUTOSTART_NO;
                        task.ITASK_ACTUAL_DATE_START = new Date();
                        task.ITASK_USER_ID = null;
                        task.ITASK_ORGSTR_ID = null;
                        task.ITASK_RUN_COUNT += 1;

                        await this.sendNotification(task, process);
                        await this.orm.repo("task").store(task);
                        await this.taskActivated(task, process);
                        await this.finish(task, process, null, canFinish);
                    } else {
                        throw new InternalException(
                            "Specified task type not found",
                        );
                    }
                    await this.workflow.orm.repo("wfTask").store(task);
                    return true;
                }
                globalThis.tasLogger.info(
                    `Due date start of task '${task.ITASK_ID}' not fulfilled. Waiting till '${task.ITASK_DUE_DATE_START}'.`,
                    {
                        iproc_id: process.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                task.ITASK_STATUS = "N"; // TASK.STATUS_NOTPERFORMABLE;
                task.ITASK_AUTO_START = taskConsts.AUTOSTART_YES;
                await this.orm.repo("task").store(task);
            } else {
                globalThis.tasLogger.info(
                    `Incoming tasks ${task.ITASK_NAME} not fulfilled.`,
                    {
                        iproc_id: process.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
            }
            return true;
        } catch (e: any) {
            globalThis.tasLogger.error(`Error in updateWaiting: ${e.message}`, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
                process,
                task,
            });
            throw e;
        }
    }

    /**

     * @param task
     * @param process
     * @param customRecipients - recipients to override default recipients from template task
     * @returns {Promise<*>}
     */
    async sendNotification(
        task: any,
        process: any,
        customRecipients: Record<string, any> | null = null,
    ): Promise<void> {
        /*
            TODO: Vice ???
         */
        const accessLogger = new DmsAccessLogger(this.orm.connection);
        const fileIds: any[] = [];
        const userRepo = this.orm.repo("user");
        const userParamRepo = globalThis.orm.repo("userParameter");
        const emailRepo = this.orm.repo("instanceTaskEmailNotifs");
        const emailTask = await emailRepo.getByTask(task.ITASK_ID).collectOne();
        const localVarList = this.variableList;
        const lov = await globalThis.orm
            .repo("variableLov" /* this.connection */)
            .getListMutations(process.IPROC_ID);

        // get system Variables
        const user = await userRepo.get(process.IPROC_INST_OWNER_USER_ID);
        const lastUser = task.ITASK_USER_ID
            ? await userRepo.get(task.ITASK_USER_ID)
            : null;
        const solverUser = task.ITASK_FINISHED_BY_USER_ID
            ? await userRepo.get(task.ITASK_FINISHED_BY_USER_ID)
            : null;

        const userLangParam = await userParamRepo.getUserParameter(
            user.USER_ID,
            userParamConsts.LANGUAGE_CLIENT,
        );
        const userLang = userLangParam.length
            ? userLangParam[0].USRPAR_VALUE
            : globalThis.dynamicConfig.defaultLang;

        const dateFormatParam = await userParamRepo.getUserParameter(
            user.USER_ID,
            "DATE_FORMAT",
        );
        const dateFormat = dateFormatParam.length
            ? dateFormatParam[0].USRPAR_VALUE
            : "L";

        const sysVariables: any = emailRepo.getSystemVariables(
            task,
            process,
            user,
            lastUser,
            solverUser,
            userLang,
            dateFormat,
        );
        sysVariables.CaseNotes = await SysVars.printProcessLog(
            this.orm.connection,
            process.IPROC_ID,
            null,
            userLang,
            dateFormat,
        );

        // get correct values
        let allVars = await localVarList.exportPrintWithLangValues(
            false,
            lov,
            user.DATE_FORMAT,
        );
        let varsSafe = await localVarList.exportPrintWithLangValues(
            true,
            lov,
            user.DATE_FORMAT,
        );
        const primitiveDlu = await localVarList.getDynamicLists(
            [variableConsts.ATTR_USER],
            true,
            user.DATE_FORMAT,
        );

        allVars = { ...allVars, ...sysVariables }; // merge with sys
        varsSafe = { ...varsSafe, ...sysVariables }; // merge with sys
        // let's sanitize sys.variables
        varsSafe.CaseOwnerName = htmlSanitize(varsSafe.CaseOwnerName);
        varsSafe.CaseName = htmlSanitize(varsSafe.CaseName);

        // Get attachments
        let att: Attachment[] | undefined;
        await globalThis.tasLogger.runTask(async () => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_MAIL,
            );
            try {
                const files =
                    JSON.parse(emailTask.ITASK_ENOT_ATTACHMENTS) || [];
                att = await this.prepareAttachments(files);
                files.forEach((file: any) => {
                    fileIds.push(file.id);
                });
            } catch (err: any) {
                // Ignore attachment email errors, only log them
                globalThis.tasLogger.error(err.message, err);
            }
        });

        if (customRecipients) {
            const upperRecipients = _.mapKeys(
                customRecipients,
                (_value, key: string) =>
                    key.toUpperCase().replace("TTASK", "ITASK"),
            );

            _.keys(upperRecipients).forEach((key) => {
                const v = upperRecipients[key];
                const val = _.isObject(v) ? (v as any).value : v;

                emailTask[key] = val == undefined ? null : val;
            });
        }

        const targets = await emailRepo.getNotificationTargets.apply(this, [
            task,
            process,
            emailTask,
            primitiveDlu,
            allVars,
        ]);

        const recipientsIds = _.chain(Object.values(targets))
            .flattenDeep()
            .map("USER_ID")
            .uniq()
            .value();

        // @t3b-1030 Cílené emailové překlady
        const usersByLanguage = await emailRepo.getUserLanguage(
            recipientsIds,
            emailTask.ITASK_ENOT_EXTERNAL_LANGUAGE ||
                emailTask.raw.TTASK_ENOT_EXTERNAL_LANGUAGE ||
                globalThis.dynamicConfig.defaultLang,
        );
        const targetsByLanguage = await emailRepo.sortTargetsByLanguage(
            targets,
            usersByLanguage,
        );

        await globalThis.tasLogger.runTask(async () => {
            globalThis.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_MAIL,
            );

            globalThis.tasLogger.info("Task email notification targets", {
                targets,
                recipientsIds,
                usersByLanguage,
                targetsByLanguage,
                iproc_id: task.IPROC_ID || process.IPROC_ID,
            });

            // Send emails to users by language
            for (const language of Object.keys(targetsByLanguage)) {
                // Translate {vars}
                // Email targets are replaced inside 'getNotificationTargets' method

                // Instance mutation => Template mutation => Instance => Template
                const getPriorityAttribute = (
                    etask: any,
                    attr: any,
                    lang: any,
                ) =>
                    etask[`I${attr}_${lang.toUpperCase()}`] ||
                    etask.raw[`T${attr}_${lang.toUpperCase()}`] ||
                    etask[`I${attr}`] ||
                    etask.raw[`T${attr}`];
                // @ts-expect-error missing typing
                const sub = globalThis.lang.replaceTemplates(
                    getPriorityAttribute(
                        emailTask,
                        "TASK_ENOT_SUBJECT",
                        language,
                    ),
                    allVars,
                    null,
                    null,
                    null,
                    language.toUpperCase(),
                );
                // @ts-expect-error missing typing
                let content = globalThis.lang.replaceTemplates(
                    getPriorityAttribute(
                        emailTask,
                        "TASK_ENOT_BODY2",
                        language,
                    ),
                    allVars,
                    null,
                    null,
                    null,
                    language.toUpperCase(),
                );

                if (content) {
                    content = content.replace(/(?<!>)\n/gi, "<br />");
                }

                // Are there any recipients for the current language?
                try {
                    if (
                        //@ts-expect-error
                        targetsByLanguage[language].target.length
                    ) {
                        //@ts-expect-error
                        const emails = targetsByLanguage[language].target.map(
                            (item: any) => item.USER_EMAIL,
                        );
                        //@ts-expect-error
                        const blind = targetsByLanguage[language].blind.map(
                            (item: any) => item.USER_EMAIL,
                        );
                        //@ts-expect-error
                        const copy = targetsByLanguage[language].copy.map(
                            (item: any) => item.USER_EMAIL,
                        );

                        const reply = targets.reply.map(
                            (item: any) => item.USER_EMAIL,
                        );

                        const meta = {
                            iproc_id: process.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        };

                        const mailOptionsParams: MailOptionsParams = {
                            addresses: emails,
                            subject: sub,
                            attachments: att,
                            bccOnMoreAddresses: true,
                            replyMail: reply,
                            blindCopy: blind,
                            ignoreError: true,
                            additionalMeta: meta,
                            copies: copy,
                            replyToMessageId: emailTask.ITASK_ENOT_MESSAGE_ID,
                        };

                        await globalThis.routerMail.sendEmailViaClient(
                            "iTaskNotification",
                            mailOptionsParams,
                            content,
                            language,
                            "L",
                        );
                    } else if (
                        //@ts-expect-error
                        targetsByLanguage[language].blind.length ||
                        //@ts-expect-error
                        targetsByLanguage[language].copy.length
                    ) {
                        globalThis.tasLogger.warning(
                            `Could not find any 'targets' for language '${language}'`,
                            {
                                targetsByLanguage,
                            },
                        );
                    }
                } catch (err) {
                    //@ts-expect-error
                    globalThis.tasLogger.error(err.message, {
                        err,
                    });
                }
            }
        });

        // Log attachments to DMS_FILE_ACCESS_LOG
        const objectsToStore: any[] = [];
        fileIds.forEach((fileId) => {
            recipientsIds.forEach((userId) => {
                objectsToStore.push({
                    ITASK_ID: task.id,
                    IPROC_ID: process.id,
                    USER_ID: userId,
                    DMSF_ID: fileId,
                });
            });
        });

        return await accessLogger.batchLog(
            objectsToStore,
            DmsAccessLogger.consts.EMAIL_NOTIFICATION_ATTACHMENT,
        );
    }

    async prepareAttachments(
        attachments: any[],
    ): Promise<Attachment[] | undefined> {
        try {
            const preparedAttachments: Attachment[] = [];
            for (const attachment of attachments) {
                const buffer =
                    await globalThis.container.service.file.fileMetadataServiceModule.fileToBuffer(
                        `${globalThis.dynamicConfig.dms.storagePath}/${attachment.path.substr(4)}`,
                    );
                preparedAttachments.push({
                    filename: attachment.name,
                    content: buffer,
                });
            }
            return preparedAttachments;
        } catch (err: any) {
            globalThis.tasLogger.error(
                `Mail attachment error: ${err.message}`,
                {
                    err,
                },
            );
        }
    }

    /**
     * incomingLinksFulfilled checks whether incoming links are fulfilled

     * @return boolean
     */
    async incomingLinksFulfilled(
        task: any,
        linksSnap: IInstanceTaskLink[],
    ): Promise<any> {
        // if is active subprocess do not check incoming links
        if (task.ITASK_TYPE === "P" && task.ITASK_STATUS === "A") {
            return {
                fulfilled: true,
            };
        }

        let minimumLinkCount = task.ITASK_PETRI_NET_INPUT;
        globalThis.tasLogger.info(`Petri net input: ${minimumLinkCount}`, {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
        });

        const maxLinkCount = (await this.wfLink.getIncomingLinks(task)).length;
        globalThis.tasLogger.info(`Real incoming links: ${maxLinkCount}`, {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        if (maxLinkCount === 0) {
            return {
                fulfilled: true,
            };
        }

        // must be satisfied all links, so get their full number
        if (minimumLinkCount === 0) {
            minimumLinkCount = maxLinkCount;
        }
        globalThis.tasLogger.info(
            `Minimum incoming links : ${minimumLinkCount}`,
            {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            },
        );

        let links;
        // get links only from done tasks
        if (typeof linksSnap[task.ITASK_ID] !== "undefined") {
            links = linksSnap[task.ITASK_ID];
        } else {
            links = await this.wfLink.getIncomingLinks(task, true);
        }

        let flink = 0;
        for (const link of links) {
            const linkEnd = link.ITASKLINK_STATUS === linkConsts.STATUS_END;
            if (
                link.ITASKLINK_IS_MANDATORY === linkConsts.MANDATORY_YES &&
                !linkEnd
            ) {
                return {
                    fulfilled: false,
                };
            }
            if (linkEnd) {
                flink += 1;
            }
        }

        globalThis.tasLogger.info(`Actual incoming links : ${flink}`, {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        return {
            fulfilled: flink >= minimumLinkCount,
        };
    }

    /**

     * @param task
     * @returns {Promise<boolean>}
     */
    async incomingLinksFulfilledV2(task: ITask): Promise<{
        fulfilled: boolean;
        minimumLinkCount?: number;
        realFulfilledLinkCount?: number;
    }> {
        // Multiinstance task alway to be activated.
        if (
            task.ITASK_MULTIINSTANCE_FLAG === taskConsts.MULTIINSTANCE_FLAG_INST
        ) {
            return {
                fulfilled: true,
            };
        }

        // Assert that task has IPROC_ID.
        const procVersion = await this.wfProcess.processVersion(task.IPROC_ID!);
        const incomingLinks = await this.wfLink.getIncomingLinksV2(
            task,
            procVersion.TTASKLINK_VERSION,
        );

        // No incoming tasks. Its autostart task probably.
        if (incomingLinks.length === 0) {
            globalThis.tasLogger.info(
                `Links from ${task.ITASK_NAME} has 0 links. Fulfilled.`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                },
            );
            return {
                fulfilled: true,
            };
        }

        let minimumLinkCount =
            task.ITASK_PETRI_NET_INPUT === 0
                ? incomingLinks.length
                : task.ITASK_PETRI_NET_INPUT;
        let realFulfilledLinkCount = 0;
        for (const link of incomingLinks) {
            if (
                link.TTASKLINK_IS_MANDATORY === linkConsts.MANDATORY_YES &&
                !link.COUNT
            ) {
                globalThis.tasLogger.info(
                    `Link for task ${link.TTASKLINK_TO_TTASK_ID} is mandatory but not OK. For fulfilled.`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                return {
                    fulfilled: false,
                };
            }

            // If link is multiinstance and task waits to each link then increase minimumLinkCount.
            if (task.ITASK_PETRI_NET_INPUT === 0 && link.MAX_COUNT > 1) {
                minimumLinkCount += link.MAX_COUNT - 1;
            }

            if (link.COUNT) {
                realFulfilledLinkCount += link.COUNT;
            }
        }

        globalThis.tasLogger.info(
            `Links from ${task.ITASK_NAME} has ${realFulfilledLinkCount} OK links of ${minimumLinkCount} links.`,
            {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            },
        );
        return {
            realFulfilledLinkCount,
            minimumLinkCount,
            fulfilled: realFulfilledLinkCount >= minimumLinkCount,
        };
    }

    /**
     * updateWaiting - updates some parameters as due date start or sets task to waiting state for event

     * @return {boolean} Continue or not
     */
    async updateWaiting(
        task: any,
        process: IProcess,
        canFinish?: boolean,
    ): Promise<boolean> {
        globalThis.tasLogger.info(`Updated waiting...`, {
            iproc_id: process.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        try {
            // let garant = task.getGuarantor();
            if (task.isMultiinstanceTemplate()) {
                // create clone of task and activate it, do not continue in this task
                if (task.isIteratedMultiinstance()) {
                    globalThis.tasLogger.info(
                        `Task is iterated multiinstance, cloning original task '${task.ITASK_NAME}'.`,
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );
                    await this.wfIteration.setupIteration(task);
                    let vars = this.wfIteration.getNextIteration(task);

                    while (vars) {
                        const ntask = await this.wfIteration.cloneTask(task);
                        await this.activate(ntask, process, canFinish);
                        vars = this.wfIteration.getNextIteration(task);
                    }
                    await this.orm.repo("task").store(task);
                    return false;
                }
                globalThis.tasLogger.info(
                    `Task is multiinstance, cloning original task '${task.ITASK_NAME}'.`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                const ntask = await this.wfIteration.cloneTask(task);
                await this.activate(ntask, process, canFinish);
                this.lastClonedTask = ntask;
                return false;
            }

            // deadline is not set, wait for set it by user
            if (
                task.ITASK_DURATION === "po" &&
                task.ITASK_DUE_DATE_FINISH === null
            ) {
                globalThis.tasLogger.info(
                    `Waiting for setting deadline for '${task.ITASK_NAME}'.`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                task.ITASK_STATUS = taskConsts.STATUS_WAITING;
                task.ITASK_ACTUAL_DATE_START = new Date();
                await this.orm.repo("task").store(task);
                await this.sendTaskTimingNotification(task, process);
                return false;
            }

            // run only once
            if (
                task.ITASK_RUN_ONLY_ONCE === "Y" &&
                task.ITASK_RUN_COUNT > 0 &&
                task.ITASK_STATUS !== "A"
            ) {
                // already runned, run as automat
                globalThis.tasLogger.info(
                    `Task '${task.ITASK_NAME}' already runned, now is automatic.`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                // but calculations should be executed
                await this.calculate(
                    task,
                    process,
                    this.variableList,
                    calcConsts.EXEC_ON_START,
                );

                task.ITASK_RUN_COUNT += 1;
                task.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
                await this.orm.repo("task").store(task);
                await this.taskActivated(task, process);
                await this.finish(task, process, this.currentUser, canFinish);
                return false;
            }

            // wait for event
            if (
                task.ITASK_TYPE === taskConsts.TYPE_EVENT_WAIT ||
                (task.ITASK_TYPE === taskConsts.TYPE_SUBPROCESS &&
                    task.ITASK_STATUS === taskConsts.STATUS_ACTIVE)
            ) {
                // no active event was found - task should wait for new event
                if (
                    task.ITASK_TYPE === taskConsts.TYPE_SUBPROCESS &&
                    task.ITASK_STATUS === taskConsts.STATUS_ACTIVE
                ) {
                    // is active subprocess - just wait
                } else {
                    globalThis.tasLogger.info("Waiting for new event.", {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    });
                    task.ITASK_STATUS = taskConsts.STATUS_DELAYED;
                    await this.orm.repo("task").store(task);
                    await this.checkCanceling(task, process);
                    await this.checkCompletion(task, process, canFinish);
                }

                return false;
            }

            // set due date start
            if (
                task.ITASK_DUE_OFFSET !== null &&
                task.ITASK_DUE_DATE_START === null
            ) {
                // process owner have to set due offset
                if (task.ITASK_DUE_OFFSET === "po") {
                    globalThis.tasLogger.info(
                        "Waiting for due date start by process owner.",
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );

                    task.ITASK_STATUS = taskConsts.STATUS_WAITING;
                    task.ITASK_ACTUAL_DATE_START = new Date();
                    await this.orm.repo("task").store(task);
                    await this.sendTaskTimingNotification(task, process);
                    return false;
                }

                // process owner have to set due date finish
                if (
                    task.ITASK_DURATION !== null &&
                    task.ITASK_DURATION === "po"
                ) {
                    globalThis.tasLogger.info(
                        "Waiting for duration by process owner.",
                        {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        },
                    );

                    task.ITASK_STATUS = taskConsts.STATUS_WAITING;
                    task.ITASK_ACTUAL_DATE_START = new Date();
                    await this.orm.repo("task").store(task);
                    await this.sendTaskTimingNotification(task, process);
                    return false;
                }

                // setup due offset for task start
                task.ITASK_AUTO_START = "Y";
                const offset = await this.parseDue(
                    task.ITASK_DUE_OFFSET,
                    this.variableList,
                    process,
                );

                if (offset.type === "T") {
                    const dueDate = moment();
                    dueDate.add(offset.interval);
                    task.ITASK_DUE_DATE_START = dueDate.toDate();

                    // absolute date
                } else if (offset.type === "A" || offset.type === "C") {
                    // in non continous changing deadline remove info about duration - it will not be used anymore
                    if (offset.type === "A") {
                        task.ITASK_DUE_OFFSET = null;
                    }
                    task.ITASK_DUE_DATE_START = offset.date;

                    // due date start based on process start
                } else if (offset.type === "P") {
                    task.ITASK_DUE_DATE_START = moment(
                        process.IPROC_ACTUAL_START_DATE,
                    )
                        .add(offset.interval)
                        .toDate();

                    // when error, start the task immediately
                } else {
                    task.ITASK_DUE_DATE_START = new Date();
                }

                globalThis.tasLogger.info(
                    `Setting due date start to ${task.ITASK_DUE_DATE_START}...`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
            } else if (
                task.ITASK_DUE_OFFSET &&
                task.ITASK_DUE_OFFSET.substring(0, 2) === "vc"
            ) {
                // for dynamic updated variable
                // setup due offset for task start
                task.ITASK_AUTO_START = taskConsts.AUTOSTART_YES;

                const offset = await this.parseDue(
                    task.ITASK_DUE_OFFSET,
                    this.variableList,
                    process,
                );

                // in non continous changing deadline remove info about duration - it will not be used anymore
                task.ITASK_DUE_DATE_START = offset.date;

                globalThis.tasLogger.info(
                    `Setting due date start (from updated variable) to ${task.ITASK_DUE_DATE_START}...`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
            }

            await this.orm.repo("task").store(task);
            return true;
        } catch (e: any) {
            globalThis.tasLogger.error(`Error in updateWaiting: ${e.message}`, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
                process,
                task,
            });
            throw e;
        }
    }

    /**
     * finish - finishes task. Copies variables from local snapshot to globalThis. Changes its state, triggers all links. First all non else links. If none success it triggers else link
     *
     * @param boolean canFinish - this task will not check end of case if set to false
     * @param boolean alertMandatory - if false will not throw error, but still will not end task
     * @access public

     * @return boolean - true if task is finished (or was) and false if task cannot be finished
     */
    async finish(
        task: any,
        process: IProcess,
        finishUser: any = null,
        canFinish: boolean = true,
        alertMandatory: boolean = true,
    ): Promise<boolean> {
        const tm = new TimeMeasure();
        tm.start("finish");

        try {
            // if multiinstance is set we do not need to check finish conditions - they are checked in previous run
            globalThis.tasLogger.info(`Finishing task '${task.ITASK_NAME}'`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });
            task.checkModifiable();

            const procVersion = await this.wfProcess.processVersion(
                task.IPROC_ID,
            );

            // only tasks in A state can be finished
            if (
                !(
                    task.ITASK_STATUS === taskConsts.STATUS_ACTIVE ||
                    task.ITASK_STATUS === taskConsts.STATUS_PULL ||
                    task.ITASK_STATUS === taskConsts.STATUS_WAITING ||
                    (task.ITASK_TYPE === taskConsts.TYPE_EVENT_WAIT &&
                        task.ITASK_STATUS === taskConsts.STATUS_DELAYED)
                )
            ) {
                return true;
            }

            // are mandatory variables set? valid only for non automatic tasks (automat, enotif)
            if (
                [
                    taskConsts.TYPE_AUTOMAT,
                    taskConsts.TYPE_EMAIL_NOTIFICATION,
                    taskConsts.TYPE_SUBPROCESS,
                ].indexOf(task.ITASK_TYPE) === -1
            ) {
                const variables = this.variableList.getUsage(task);
                for (const ivaruse of variables) {
                    if (ivaruse.use === "M") {
                        // Check mandatory value in snapshot or variable ..
                        let { variable } = ivaruse;
                        if (task.isMultiinstance()) {
                            variable = await this.workflow.orm
                                .repo("variableSnap")
                                .getByTask(
                                    task.ITASK_ID,
                                    ivaruse.variable.IVAR_ID,
                                )
                                .collectOne();
                        }

                        const val = variable.value;
                        if (
                            val === null ||
                            val === "" ||
                            typeof val === "undefined"
                        ) {
                            if (alertMandatory) {
                                globalThis.tasLogger.error(
                                    `Mandatory variable '${ivaruse.variable.IVAR_NAME}' was not set in task '${task.ITASK_NAME}'.`,
                                    {
                                        iproc_id: task.IPROC_ID,
                                        itask_id: task.ITASK_ID,
                                    },
                                );
                                throw new UserException(
                                    `Mandatory variable '${ivaruse.variable.IVAR_NAME}' was not set`,
                                );
                            }

                            globalThis.tasLogger.info(
                                `Task '${task.ITASK_NAME}' finished ($measure=${tm.end("finish")}s)`,
                                {
                                    iproc_id: task.IPROC_ID,
                                    itask_id: task.ITASK_ID,
                                },
                            );
                            return false;
                        }
                    }
                }
            }

            // now starting finishing
            task.ITASK_STATUS = taskConsts.STATUS_DONE;
            task.ITASK_FINISHED_BY_USER_ID = !finishUser
                ? null
                : finishUser.isViced()
                  ? finishUser.PRIMARY_USER_ID
                  : finishUser.USER_ID;
            task.ITASK_ACTUAL_DATE_FINISH = new Date();

            await this.workflow.orm.repo("wfTask").store(task);
            await this.calculate(
                task,
                process,
                this.variableList,
                calcConsts.EXEC_ON_END,
            );

            if (await this.checkCompleteLoop(task)) {
                throw new InternalException(
                    `Infinite loop detected in ITASK_ID='${task.ITASK_ID}, TTASK_ID=${task.TTASK_ID}, ITASK_NAME=${task.ITASK_NAME}'.`,
                );
            }

            // store before calculation
            await this.workflow.orm.repo("wfTask").store(task);

            if (task.ITASK_TYPE === taskConsts.TYPE_INVITATION) {
                const invite = await this.workflow.orm
                    .repo("instanceTaskInvitation")
                    .get(task.ITASK_ID)
                    .collectOne();
                const userInfo = await this.workflow.orm
                    .repo("user")
                    .getById(task.ITASK_USER_ID)
                    .collectOne();
                const invMail = new Invitation(
                    globalThis.container.client.withoutTemplateMail,
                    invite,
                    userInfo,
                );
                invMail.send();
            }

            await this.saveHistory(
                task,
                "Task finished",
                task.ITASK_FINISHED_BY_USER_ID,
            );

            // go through all outgoing links, check activate all tasks from fulfilled links or activate else branch
            if (procVersion.TTASKLINK_VERSION > 0) {
                // t3b-1150 Optimalizovaný šablonovitý linky
                await this.wfLink.activateRight2(
                    task,
                    process,
                    procVersion.TTASKLINK_VERSION,
                );
            } else {
                // Sorry, just for compatibility !
                const olinks = await this.wfLink.getOutgoingLinks(task);
                let flinks = 0;
                let elseLink = null;

                globalThis.tasLogger.info(
                    `Has ${olinks.length} outgoing links.`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );

                for (const link of olinks) {
                    if (link.ITASKLINK_TYPE !== linkConsts.TYPE_ELSE) {
                        // false mean that subsequent tasks cannot finish process
                        if (
                            (await this.wfLink.activateRight(
                                link,
                                task,
                                process,
                                false,
                            )) !== false
                        ) {
                            flinks += 1;
                        }
                    } else {
                        elseLink = link;
                    }
                }

                if (flinks === 0) {
                    if (elseLink !== null) {
                        await this.wfLink.activateRight(
                            elseLink,
                            task,
                            process,
                            true,
                        );
                        globalThis.tasLogger.info("Activating else link", {
                            iproc_id: task.IPROC_ID,
                            itask_id: task.ITASK_ID,
                        });
                    }
                }
            }

            if (task.ITASK_SUFFICIENT_END === taskConsts.SUFFICIENT_END_YES) {
                globalThis.tasLogger.info("Sufficient end. Finishing process", {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                });
                this._taskToSolve = []; // No task can be solved after process is done.
                await this.wfProcess.setStatusDone(process);
                await this.orm.repo("process").store(process);
                globalThis.tasLogger.info(
                    `Task '${task.ITASK_NAME}' finished ($measure=${tm.end("finish")}s)`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                return true;
            }

            // check for iteration - must be before checking for process completion
            if (this.wfIteration.hasNextIteration(task)) {
                globalThis.tasLogger.info(
                    "Task reactivated for iteration " +
                        JSON.stringify(task.iteratePos),
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
                // clean user for seting it up again
                task.TASK_USER_ID = null;
                task.ITASK_ORGSTR_ID = null;
                await this.activate(task, process, canFinish, [], true);
            }

            globalThis.tasLogger.info(`Testing finish: ${canFinish}`, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });

            // check completion of process
            await this.wfProcess.checkCompletionProcess(process, canFinish);

            globalThis.tasLogger.info(
                `Task '${task.ITASK_NAME}' finished ($measure=${tm.end("finish")}s)`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                },
            );
            return true;
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
            });
            throw e;
        }
    }

    /**
     * taskActivated - is called after task was really activated
     * Change status of incoming links.
     *

     * @access private
     * @return void
     */
    async taskActivated(task: any, process: IProcess): Promise<void | number> {
        try {
            const procVersion = await this.wfProcess.processVersion(
                task.IPROC_ID,
            );

            if (procVersion.TTASKLINK_VERSION > 0) {
                return await this.orm
                    .repo("instanceTaskLinkDone")
                    .resetTTask(task.TTASK_ID, process.IPROC_ID);
            }
            let links = [];
            // reset statuses for all incoming links
            if (task.isMultiinstance()) {
                const miTaskRoot = await this.getMultiinstanceRoot(
                    task,
                    process,
                );
                links = await this.wfLink.getIncomingLinks(miTaskRoot);
            } else {
                links = await this.wfLink.getIncomingLinks(task);
            }

            for (const link of links) {
                link.ITASKLINK_STATUS = linkConsts.STATUS_NOT_ACTIVE;
            }

            await this.orm.repo("wfLink").storeMulti(links);
            await this.orm.repo("wfTask").store(task);
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
            });
            throw e;
        }
    }

    /**

     * @param task
     * @param process
     * @returns {Promise}
     */
    getMultiinstanceRoot(task: ITask, process: IProcess): Promise<ITask> {
        return this.orm
            .repo("wfTask")
            .getMultiinstanceRoot(task, process)
            .collectOne();
    }

    /**
     * checks for fulfilled conditions to cancel task
     *

     * @param task
     * @param process
     * @return {Promise<void>}
     */
    async checkCanceling(task: Task, process: IProcess): Promise<any> {
        try {
            const completions = (await this.getCompletions(
                task,
                true,
            )) as unknown as InstanceTaskCompletition[];
            if (
                !_.isEmpty(completions) ||
                !globalThis.dynamicConfig.logger.ignoreTasksWithoutConditions
            ) {
                globalThis.tasLogger.info(
                    `Check canceling. ${task.ITASK_NAME}`,
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );
            }

            const concat = !_.isEmpty(completions)
                ? completions[0].ITC_CONCAT_OP
                : null; // flag A|O if conditions are connected by logical and or or, condition is same for all rows
            let fullResult = false; // contains result over all conditions
            let localVarList;

            // Task is multi instance, need to load Snaps
            if (task.isMultiinstance() && completions.length > 0) {
                localVarList = new VariableList();
                localVarList.setVariables(
                    await this.workflow.orm
                        .repo("variableSnap")
                        .getByTask(task.ITASK_ID)
                        .collectAll(),
                );
            }

            for (const comp of completions) {
                const result = await this.wfCondition.evaluateCompletition(
                    this.variableList,
                    //Assert that localVarList is not null
                    localVarList!,
                    comp,
                    this.getSystemVariables(task, process),
                );

                fullResult = result;

                if (concat === "O" && result) {
                    fullResult = true;
                    break;
                }
                if (concat === "A" && !result) {
                    fullResult = false;
                    break; // break foreach when the callback returns true;
                }
            }

            if (fullResult) {
                return await this.cancel(task, process);
            }
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
            });
            throw e;
        }
    }

    /**
     * checkCompletion - checks for fulfilled conditions, if fulfilled then automaticaly finish current task
     * @param canFinish - automated (automatically ended) tasks will not end case if set to false
     * @return string - returns complete hash or false if task was not finished
     */
    async checkCompletion(
        task: Task,
        process: IProcess,
        canFinish?: boolean,
    ): Promise<boolean> {
        try {
            const completions = (await this.getCompletions(
                task,
            )) as unknown as InstanceTaskCompletition[];
            if (
                !_.isEmpty(completions) ||
                !globalThis.dynamicConfig.logger.ignoreTasksWithoutConditions
            ) {
                globalThis.tasLogger.info("Check completition.", {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                });
            }

            const concat = !_.isEmpty(completions)
                ? completions[0].ITC_CONCAT_OP
                : null; // flag A|O if conditions are connected by logical and or or, condition is same for all rows
            let fullResult = false; // contains result over all conditions

            /* completeHash is used for checking infinite loops
            it is constructed as string like this:
                <ITASK_ID>:<IVAR_ID>.<VALUE>,<IVAR_ID>.<VALUE>,
            if there is task finished again with the same completeHash then completion should stop
            */
            let localVarList: VariableList | null = null;
            // Task is multi instance, need to load Snaps
            if (task.isMultiinstance() && completions.length > 0) {
                localVarList = new VariableList();
                localVarList.setVariables(
                    await this.workflow.orm
                        .repo("variableSnap")
                        .getByTask(task.ITASK_ID)
                        .collectAll(),
                );
            }
            for (const comp of completions) {
                // TODO: const systemVariablesList;
                const result = await this.wfCondition.evaluateCompletition(
                    this.variableList,
                    //Assert that localVarList is not null
                    localVarList!,
                    comp,
                    this.getSystemVariables(task, process),
                );
                /* $vars = $this.getVariableValues();
                $todoVal = null;
                $result = ICondition._evaluate($row.OPERATOR, $todoVal , $row.VALUE); */

                /* Follows ugly hack
                   During condition evaluation are loaded used process variables and in method ICondition:getProcessVariableValue
                   are these variables cached in global variable named getProcessVariableValues
                   Variables from this cache are only ones used during condition evaluation and from them is constructed $completeHash
                   variable, which prevents infinite looping
                 */

                // not used anymore?
                /* if(is_a($GLOBALS.getProcessVariableValue']['VALUE, 'nDateTime'))
                    $GLOBALS.getProcessVariableValue']['VALUE = $GLOBALS.getProcessVariableValue']['VALUE.format(nDateTime.ISO8601); */

                // const double = this.variableList.map(ivar => `${ivar.IVAR_NAME},${ivar.value}`);
                // completeHash += double.join(',');

                fullResult = result;

                if (concat === "O" && result) {
                    fullResult = true;
                    break;
                }
                if (concat === "A" && !result) {
                    fullResult = false;
                    break; // break foreach when the callback returns true;
                }
            }

            if (fullResult) {
                // check for infinite loops

                /* if(!Globals.is_set('completeHashes'))
                    Globals.put('completeHashes',  array());
                if(isset($GLOBALS['completeHashes'][$taskId]) && $GLOBALS['completeHashes'][$taskId] == $completeHash) {
                    throw new Exception('Infinite loop detected');
                } */
                globalThis.tasLogger.info(
                    "Conditions of auto-completition task are met.",
                    {
                        iproc_id: task.IPROC_ID,
                        itask_id: task.ITASK_ID,
                    },
                );

                // @t3b-1349 Zobrazení stornovaných úkolů v historii případu
                task.ITASK_AUTO_RESOLVED = taskConsts.AUTO_RESOLVED_FINISHED;
                await this.saveHistory(
                    task,
                    "Task automatically completed",
                    null,
                );
                const res = await this.finish(
                    task,
                    process,
                    this.currentUser,
                    canFinish,
                    false,
                ); // can finish, but do not report missing mandatory variables

                if (res) {
                    // store complete hash for infinite loop control
                    // this.setCompleteLoop(task);
                    // $GLOBALS['completeHashes'][$taskId] = $completeHash;
                    await this.workflow.wfProcess.updateSummary(process);
                    return true;
                }
                return false;
            }
            return false;
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                iproc_id: process.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
            });
            throw e;
        }
    }

    /**
     * Return completion condition list to finish or cancel task.
     *

     * @return BaseCollection
     */
    async getCompletions(
        task: Task,
        toCancel: boolean = false,
    ): Promise<BaseCollection<InstanceTaskCompletition>> {
        try {
            const procVersion = await this.wfProcess.processVersion(
                // Assert that task has IPROC_ID
                task.IPROC_ID!,
            );
            if (task.isMultiinstance() && !procVersion.TTC_VERSION) {
                return this.workflow.orm
                    .repo("instanceTaskCompletition")
                    .getMultiTaskCompletions(task, toCancel);
            }

            return this.workflow.orm
                .repo("instanceTaskCompletition")
                .getTaskCompletions(
                    task,
                    toCancel,
                    procVersion.TTC_VERSION || 0,
                );
        } catch (e: any) {
            globalThis.tasLogger.error(e.message, {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
                err: e,
            });
            throw e;
        }
    }

    async calculate(
        task: ITask,
        process: IProcess,
        variableList: IVariableList,
        execTyp: string,
    ): Promise<any> {
        const procVersion = await this.wfProcess.processVersion(process);

        // Evaluate calculations.

        const calcs = new TaskJsCalculations(
            this.workflow.orm,
            globalThis.tasLogger,
        );
        calcs.setVariables(variableList.getVariables());
        calcs.setWorkflow(this.workflow);

        // Set snapshots if multiinstance task.
        if (task.isMultiinstance()) {
            const snaps = await this.workflow.orm
                .repo("variableSnap")
                .getByTask(task.ITASK_ID)
                .collectAll();
            calcs.setSnapshots(snaps);
        }

        return await calcs.evaluateTas2Calculations(
            this.currentUser,
            task,
            process,
            execTyp,
            procVersion.TTJSCALC_VERSION || 0,
        );
    }

    /**
     * Parsing tas specifc format for duration.
     *

     * @param due <type>+<days>[:<hours>[:<minutes>]] ie. T+32:12, P+12, T+0:0:20
     * @param {VariableList} variableList Is optional, can use db to retrieve variable.
     * @returns {{type: A|C, interval: moment, date: Date}}
     */
    async parseDue(
        due: string,
        variableList: IVariableList | null = null,
        process: IProcess,
    ): Promise<any> {
        try {
            // check for vo, vc type
            const type = due.substring(0, 2);
            const out: any = {};

            if (type === "vo" || type === "vc") {
                if (type === "vo") {
                    out.type = "A"; // type is absolute date
                } else {
                    out.type = "C"; // type is continuous changing date
                }

                let varDate;
                if (variableList === null) {
                    varDate = await this.orm
                        .repo("task")
                        //@ts-expect-error
                        .getVariableByTVarId(due.substring(2));
                } else {
                    varDate = variableList.getBy("TVAR_ID", due.substring(2));
                }

                if (!varDate.hasValidDate()) {
                    throw new InternalException(
                        `Invalid variable type/value for task due in task (variable ${varDate.IVAR_NAME}) `,
                    );
                }
                out.date = varDate.value;

                // fixme add variable change hook
            } else {
                const regex = /([T|P])\+(\d*)(:(\d*)(:(\d*)|)|)/;
                // Assert that match is not null
                const match = regex.exec(due)!;
                if (match?.length === 0) {
                    throw new InternalException(`Invalid due '${regex}'`);
                }

                out.type = match[1];

                const days = Number(match[2]);
                const hours =
                    typeof match[4] !== "undefined" ? Number(match[4]) : 0;
                const minutes =
                    typeof match[6] !== "undefined" ? Number(match[6]) : 0;
                const duration = moment.duration({ days, hours, minutes });

                out.interval = duration;
            }

            return out;
        } catch (e: any) {
            // supress errors because doesn't affect the process
            globalThis.tasLogger.warning(e.message, {
                iproc_id: process.IPROC_ID,
                err: e,
            });
            return { type: null };
        }
    }

    /**
     * finishHard - stops current task without calculations and without activating next tasks
     *

     * @param bool $canFinish
     * @return boolean - true if task is finished
     */
    async finishHard(
        task: any,
        process: IProcess,
        canFinish: boolean = true,
    ): Promise<boolean> {
        task.ITASK_STATUS = taskConsts.STATUS_DONE;

        // Store changes.
        await this.workflow.orm.repo("wfTask").store(task);

        globalThis.tasLogger.info("Finished task hard.", {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        await this.wfProcess.checkCompletionProcess(process, canFinish);
        return true;
    }

    /**
     * Cancel task.
     *

     * @param task
     * @param process
     * @return {ITASK_ID}
     */
    async cancel(
        task: any,
        _process: IProcess,
    ): Promise<number | number[] | null> {
        globalThis.tasLogger.info("Canceling task due to cancel conditions.", {
            iproc_id: task.IPROC_ID,
            itask_id: task.ITASK_ID,
        });
        task.ITASK_STATUS = taskConsts.STATUS_NEW;
        task.ITASK_USER_ID = null;
        // t3b-1349 Zobrazení stornovaných úkolů v historii případu
        task.ITASK_AUTO_RESOLVED = taskConsts.AUTO_RESOLVED_CANCELLED;
        await this.saveHistory(task, "Task automatically cancelled", null);
        return await this.orm.repo("task").store(task);
    }

    /**
     * saveHistory - saves task to the history log, including it's local variables
     *

     * @param {Task} task
     * @param {string} note
     * @param {int} userId
     * @param {boolean} actualDate - if set to true actual date will be used instead of date of task finish
     * @return void
     */
    async saveHistory(
        task: ITask,
        note: string = "",
        userId: number | null = null,
        _actualDate: boolean = false,
        trx?: Knex.Transaction,
    ): Promise<void> {
        try {
            // dont save history if can run only once and this is technical run
            if (
                task.ITASK_RUN_ONLY_ONCE === "Y" &&
                task.ITASK_RUN_COUNT !== 1
            ) {
                return;
            }

            const finishedUserId =
                userId !== null ? userId : task.ITASK_FINISHED_BY_USER_ID;
            let primaryUserId = null;

            globalThis.tasLogger.info("Saving history...", {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            });

            if (userId) {
                try {
                    const info =
                        await globalThis.container.service.temporary.cacheModule.getCachedUser(
                            userId,
                        );
                    primaryUserId = info.PRIMARY_USER_ID;
                } catch (_err) {
                    // ignored - user is not in cache, can not be vicing ...
                }
            }

            const vars = this.variableList
                .getUsage(task)
                .map((item: any) => item.getVar());
            await this.orm
                .repo("instanceVariableHistory", trx)
                .generateForTask(
                    task,
                    vars,
                    note,
                    primaryUserId,
                    finishedUserId,
                );
        } catch (e: any) {
            globalThis.tasLogger.warning(
                `Error while saving history for '${task.ITASK_NAME}' : ${e.message}`,
                {
                    iproc_id: task.IPROC_ID,
                    itask_id: task.ITASK_ID,
                    err: e,
                },
            );
            throw e;
        }
    }

    /**

     * @param currentTask
     * @param process
     * @param data
     * @returns {Promise<*>}
     */
    async addTask(
        currentTask: ITask,
        process: IProcess,
        data: any,
    ): Promise<ITask> {
        const taskRepo = this.orm.repo("instanceTask");
        // Set properly task type. Use S for F(Free).
        const taskType =
            data.itask_type === "F"
                ? taskConsts.TYPE_STANDARD
                : data.itask_type;

        // Store new task.
        let newTask = taskRepo.getEntity({
            IPROC_ID: currentTask.IPROC_ID,
            ITASK_TYPE: taskType,
            ITASK_NAME: data.itask_name,
            ITASK_AUTO_START: taskConsts.AUTOSTART_NO,
            ITASK_PETRI_NET_INPUT: 0,
            ITASK_DUTY: "Y",
            ITASK_USER_ID: data.itask_assesment_user_id
                ? data.itask_assesment_user_id
                : this.currentUser.USER_ID,
            ITASK_DUE_DATE_START: data.itask_due_date_start,
            ITASK_DUE_DATE_FINISH: data.itask_due_date_finish,
            ITASK_ASSESMENT_HIERARCHY: data.itask_assesment_hierarchy,
            ITASK_ASSESMENT_METHOD: data.itask_assesment_method,
        });

        const newTaskId = await taskRepo.store(newTask);
        // Entity has been stored and filled with default values, need to refresh it
        //@ts-expect-error
        newTask = await taskRepo.get(newTaskId);

        // Add link between
        if (data.itask_type === taskConsts.TYPE_STANDARD) {
            // Prepend. Add link and delay current task.
            await this.orm
                .repo("instanceTaskLink")
                //@ts-expect-error
                .addLink(
                    currentTask.IPROC_ID,
                    newTask.ITASK_ID,
                    currentTask.ITASK_ID,
                    linkConsts.MANDATORY_YES,
                    linkConsts.TYPE_AND,
                );
            await this.delay(currentTask);
        }

        // Activate this task.
        await this.activate(newTask, process);

        return newTask;
    }

    /**
     * Activate and solve event waiting task with new Workflow.

     * @param {InstanceTask} itask
     */
    async wakeupFromEventWait(itask: any): Promise<ITask[]> {
        const wf = new Workflow(this.orm, this.currentUser);

        // t3b-765 Přiřazování řešitele - Poslední řešitel úkolu
        itask.ITASK_STATUS = taskConsts.STATUS_ACTIVE;
        itask.ITASK_USER_ID = this.currentUser.USER_ID;
        itask.ITASK_ACTUAL_DATE_START = new Date();
        await this.workflow.orm.repo("instanceTask").store(itask);

        // Must store -> lastSolver using itask_status. Failed

        return await wf.solveTask(itask);
    }

    /**

     * Delay task.
     * @param {Task} task
     */
    async delay(task: any): Promise<void> {
        task.ITASK_STATUS = taskConsts.STATUS_DELAYED;
        await this.orm.repo("task").store(task);
    }

    /**

     * @param userId
     * @param newUserId
     * @param headerId
     * @returns {Promise<number|*>}
     */
    async changeSolver(
        userId: number | null,
        newUserId: number,
        headerId: number[] | number,
        tasksList: Task[] = [],
        trx?: Knex.Transaction,
    ): Promise<number> {
        const headerIds = !Array.isArray(headerId) ? [headerId] : headerId;
        const conn = this.orm.connection
            .select("IT.*")
            .from("INSTANCE_TASKS as IT")
            .leftJoin("INSTANCE_PROCESSES as IP", "IP.IPROC_ID", "IT.IPROC_ID")
            .whereIn("IP.IPROC_STATUS", [
                processConsts.STATUS_ACTIVE,
                processConsts.STATUS_SUSPEND,
            ])
            .whereIn("IT.ITASK_STATUS", [
                taskConsts.STATUS_ACTIVE,
                taskConsts.STATUS_WAITING,
                taskConsts.STATUS_DELAYED,
            ])
            .where("IT.ITASK_USER_ID", userId);

        if (headerId) {
            conn.whereIn("IP.HEADER_ID", headerIds);
        }

        const tasks = tasksList.length
            ? tasksList
            : await this.orm.collection("instanceTask", conn).collectAll();

        if (Array.isArray(tasks) && tasks.length > 0) {
            for (const task of tasks) {
                // Assign rights
                await this.orm
                    .repo("externalRight", trx)
                    .assignUserRights(task.IPROC_ID, newUserId); // Assign visibility rights

                // Change entity
                const orgRepo = this.orm.repo("userOrganizationStructure");
                const orgstrId = await orgRepo.getUserOrgstrId(newUserId);
                task.ITASK_USER_ID = newUserId;
                task.ITASK_ORGSTR_ID = orgstrId;
                await this.workflow.orm.repo("instanceTask", trx).store(task);

                // Save history.
                await this.saveHistory(
                    task,
                    "User changed",
                    newUserId,
                    false,
                    trx,
                );

                // Update summary
                const proc = await this.workflow.orm
                    .repo("process")
                    .get(task.IPROC_ID);
                await this.workflow.wfProcess.updateSummary(proc, trx);
            }
            return tasks.length;
        }
        return 0;
    }

    /**

     * Rechecks tasks where DUE_OFFSET=vc{tvarid} to change waiting status.
     * @returns {Promise.<void>}
     */
    async checkTaskDueDateSchedule(
        task: any,
        process: IProcess,
    ): Promise<void> {
        globalThis.tasLogger.info(
            `Check dueDate schedule for are ${task.ITASK_NAME} ITASK_DUE_OFFSET=${task.ITASK_DUE_OFFSET}.`,
            {
                iproc_id: task.IPROC_ID,
                itask_id: task.ITASK_ID,
            },
        );
        if (
            typeof task.ITASK_DUE_OFFSET === "string" &&
            task.ITASK_DUE_OFFSET.startsWith("vc")
        ) {
            const offset = await this.parseDue(
                task.ITASK_DUE_OFFSET,
                this.variableList,
                process,
            );

            if (offset.date > new Date()) {
                globalThis.tasLogger.info(
                    `Task ${task.ITASK_NAME} was rescheduled till ${offset.date}`,
                );
                task.ITASK_DUE_DATE_START = offset.date;
                await this.workflow.orm.repo("wfTask").store(task);
            }
        }
    }

    getTasksToSolve(): ITask[] {
        return this._taskToSolve;
    }

    setTasksToSolve(tasks: ITask[]): void {
        this._taskToSolve = tasks;
    }

    /**

     * @param ttaskId
     * @returns {*}
     */
    getTtask(ttaskId: number): Promise<ITemplateTask> {
        const tTaskRepo = this.workflow.orm.repo("templateTask");
        return tTaskRepo.get(ttaskId);
    }

    /**

     * @param task
     * @param process
     * @returns {Promise<void>}
     */
    async sendTaskTimingNotification(
        task: any,
        process: IProcess,
    ): Promise<void> {
        const solverUser = (await this.wfSolver.findSolver(
            task,
            process,
        )) as IUser;
        // Assert that process.TPROC_ID is not null
        const tproc = await this.wfProcess.getTproc(process.TPROC_ID!);
        const ttask = await this.wfTask.getTtask(task.TTASK_ID);

        const targets: Record<string, any>[] = [
            {
                USER_ID: solverUser.USER_ID,
                USER_EMAIL: solverUser.USER_EMAIL,
                USER_FIRST_NAME: solverUser.USER_FIRST_NAME,
                USER_LAST_NAME: solverUser.USER_LAST_NAME,
                UV_ID: null,
            },
        ];

        const vice = await this.orm
            .repo("userVice")
            .getUsersViced(solverUser.USER_ID, true)
            .collectOne();
        if (vice) {
            targets.push({
                USER_ID: vice.USER_ID_VICE,
                USER_EMAIL: vice.raw.USER_VICE_EMAIL,
                USER_FIRST_NAME: solverUser.USER_FIRST_NAME,
                USER_LAST_NAME: solverUser.USER_LAST_NAME,
                UV_ID: vice.UV_ID,
                UV_USER: `${vice.raw.USER_VICE_DISPLAY_NAME}`,
            });
        }

        for (const target of targets) {
            const notificationIsEnabled = !!(
                await this.orm
                    .repo("userParameter")
                    .getUserParameter(
                        target.USER_ID,
                        userParamConsts.SEND_NEW_TASK_NOTIFICATION,
                        true,
                    )
            ).length;
            if (target.USER_EMAIL && notificationIsEnabled) {
                const langParam = await this.orm
                    .repo("userParameter")
                    .getUserParameter(
                        target.USER_ID,
                        userParamConsts.LANGUAGE_CLIENT,
                    );
                const userLanguage = langParam.length
                    ? langParam[0].USRPAR_VALUE
                    : globalThis.dynamicConfig.defaultLang;

                const dateFormatParam = await this.orm
                    .repo("userParameter")
                    .getUserParameter(target.USER_ID, "DATE_FORMAT");
                const dateFormat = dateFormatParam.length
                    ? dateFormatParam[0].USRPAR_VALUE
                    : "L";

                const taskTimingData: ITaskTiming["data"] = {
                    user_name: target.USER_DISPLAY_NAME,
                    viceUser: target.UV_USER,
                    itask_id: task.ITASK_ID,
                    itask_name: task.ITASK_NAME,
                    iproc_id: task.IPROC_ID,
                    iproc_name: process.IPROC_NAME,
                    tproc_name: tproc.TPROC_NAME,
                    itask_due_date_finish: task.raw.ITASK_DUE_DATE_FINISH,
                    itask_comment: !task.ITASK_COMMENT
                        ? ""
                        : htmlSanitize(task.ITASK_COMMENT),
                    itask_description: !task.ITASK_DESCRIPTION
                        ? ""
                        : htmlSanitize(task.ITASK_DESCRIPTION),
                };

                // Add 'uv-id'
                if (target.UV_ID) {
                    taskTimingData.itask_id = `${taskTimingData.itask_id}?uv-id=${target.UV_ID}`;
                    taskTimingData.iproc_id = `${taskTimingData.iproc_id}?uv-id=${target.UV_ID}`;
                }

                const header = await this.orm
                    .repo("header")
                    .get(process.HEADER_ID);

                // Add translations
                taskTimingData.header_name = header.HEADER_NAME;
                globalThis.dynamicConfig.langs.forEach((language: string) => {
                    // Add COMMENT translations
                    const itaskComment = `itask_comment_${language}`;
                    //@ts-expect-error
                    taskTimingData[itaskComment] = !task[
                        itaskComment.toUpperCase()
                    ]
                        ? ""
                        : htmlSanitize(task[itaskComment.toUpperCase()]);
                    // Add DESCRIPTION translations
                    const ttaskDesc = `ttask_description_${language}`;
                    //@ts-expect-error
                    taskTimingData[ttaskDesc] = !ttask[ttaskDesc.toUpperCase()]
                        ? ""
                        : htmlSanitize(
                              //@ts-expect-error
                              ttask[ttaskDesc.toUpperCase()],
                          );
                    // Add TASK translations
                    const ttaskName = `ttask_name_${language}`;
                    //@ts-expect-error
                    taskTimingData[ttaskName] = !ttask[ttaskName.toUpperCase()]
                        ? ""
                        : htmlSanitize(
                              //@ts-expect-error
                              ttask[ttaskName.toUpperCase()],
                          );
                    // Add PROCESS translations
                    const tprocName = `tproc_name_${language}`;
                    //@ts-expect-error
                    taskTimingData[tprocName] =
                        //@ts-expect-error
                        tproc[tprocName.toUpperCase()] || "";
                    // Add HEADER translations
                    const headerName = `header_name_${language}`;
                    //@ts-expect-error
                    taskTimingData[headerName] =
                        header[headerName.toUpperCase()] || "";
                });

                const mailOptionsParams: MailOptionsParams = {
                    addresses: target.USER_EMAIL,
                    subject: `${globalThis.__({ phrase: "taskTiming", locale: userLanguage })} | ${task.ITASK_NAME}`,
                    bccOnMoreAddresses: true,
                    ignoreError: true,
                };

                try {
                    await globalThis.routerMail.sendEmailViaClient(
                        "taskTiming",
                        mailOptionsParams,
                        taskTimingData,
                        userLanguage,
                        dateFormat,
                    );
                } catch (err: any) {
                    globalThis.tasLogger.warning({
                        err,
                    });
                    throw err;
                }
            }
        }
    }
}
