import { OrmFactory, RepositoryInstances } from "../../orm/OrmFactory";

export default class SyncManagerRights {
    async run(connection: any, managerId: any): Promise<void> {
        try {
            const orm: OrmFactory = new OrmFactory(
                connection,
                globalThis.dynamicConfig.db.client,
            );
            const repo: RepositoryInstances["externalRight"] = orm.repo(
                "externalRight",
                connection,
            );

            globalThis.tasLogger.info(
                `Action SyncManagerRights for managerId ${managerId} started`,
                {
                    manager_id: managerId,
                },
            );

            await repo.syncDynamicRightsByManagerId(managerId);

            globalThis.tasLogger.info(
                `Action SyncManagerRights for managerId ${managerId} done`,
                {
                    manager_id: managerId,
                },
            );
        } catch (_err: any) {
            globalThis.tasLogger.info(
                `Action SyncManagerRights for managerId ${managerId} failed`,
                {
                    manager_id: managerId,
                },
            );
        }
    }
}
