import { OrmFactory, RepositoryInstances } from "../../orm/OrmFactory";

export default class AssignManagerRights {
    async run(connection: any, userId: any): Promise<void> {
        try {
            const orm: OrmFactory = new OrmFactory(
                connection,
                globalThis.dynamicConfig.db.client,
            );
            const repo: RepositoryInstances["externalRight"] = orm.repo(
                "externalRight",
                connection,
            );

            globalThis.tasLogger.info(
                `Action assignManagerRights for userId ${userId} started`,
                {
                    user_id: userId,
                },
            );

            await repo.assignManagerRights(userId);

            globalThis.tasLogger.info(
                `Action assignManagerRights for userId ${userId} done`,
                {
                    user_id: userId,
                },
            );
        } catch (_err: any) {
            globalThis.tasLogger.info(
                `Action assignManagerRights for userId ${userId} failed`,
                {
                    user_id: userId,
                },
            );
        }
    }
}
