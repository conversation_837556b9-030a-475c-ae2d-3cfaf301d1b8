import path from "path";
import fs from "fs";
import moment from "moment";
import _ from "lodash";
import sanitizeHtml from "sanitize-html";

import { OrmFactory } from "../../api/orm/OrmFactory";
import * as PROCESS from "../../api/orm/entity/const/processConst";
import { IMsGraphConfig } from "../../api/utils/MsGraphHelper";
import { MsGraphClient } from "../../api/lib/MsGraph";
import { MsGraphFileAttachment, MsGraphMail } from "../../api/lib/MsGraphTypes";
import { Process } from "../../api/orm/entity/Process";
import { EXTERNAL_IMAGE } from "../../api/utils/Base64Files";
import { Workflow } from "../../api/workflow/Workflow";
import { BaseCron } from "./BaseCron";
import { DmsAccessLogger } from "../../api/utils/DmsAccessLogger";
import { Service } from "typedi";
import { InternalException } from "../../utils/errorHandling/exceptions/internalException";
import { WithoutTemplateMailClient } from "../../client/mail/emailClients/withoutTemplate/WithoutTemplateMailClient";
import { LogCategory } from "../../utils/logger/logConsts";
import { TasSendMailOptions } from "../../client/mail/types";
import { Auth } from "../../service/authorization/Auth";
import { TasLogger } from "../../utils/logger/TasLogger";
import { DatabaseClient } from "../../client/database/databaseClient";
import { CacheClient } from "../../client/cache/cacheClient";
import { STATUS_NOT_ACTIVE } from "../../api/orm/entity/const/cronConsts";

@Service()
export class MsGraphCreateProcessesFromMailCron extends BaseCron {
    public schedule = "50 */5 3-19 * * *";

    public parameters = JSON.stringify({
        items: [
            {
                auth: {
                    emailAddress: "",
                    scope: ["optional string array"],
                    tenantId: "",
                    clientId: "",
                    clientSecret: "",
                    type: "secret/dedicated",
                },
                folders: {
                    in: {
                        id: "",
                    },
                    out: {
                        id: "",
                    },
                },
                process: {
                    user_id: "",
                    tproc_id: "",
                    tproc_version: 1,
                    header_id: "",
                    holderData: {},
                },
                config: {
                    setEmailProcessedOnError: true,
                    errorEmailAddress: "",
                },
                ignoreAttachmentErrors: true,
                ignoreVariablesUpdateAndUseDataHolder: true,
                useEmailObjectInDataHolder: true,
                mapping: {
                    field1: "",
                    field2: {
                        value: "",
                        option: "",
                        isConstant: true,
                    },
                },
            },
        ],
    });

    public alias = "Create Processes from MS Graph mails";

    public description =
        "Creates Processes using information extracted from MS Graph emails";

    public defaultStatus = STATUS_NOT_ACTIVE;

    public help = `
        <div>
            <span><span style="font-weight:700">Common variables mapping uses:</span></span>
            <table class="invoice-table" style="width: 100%;">
                <tbody>
                    <tr><td><b>auth-emailAddress</b> - The email address associated with the credentials.<br /></td></tr>
                    <tr><td><b>auth-scope</b> - An optional array of strings representing the scopes or permissions requested.<br /></td></tr>
                    <tr><td><b>auth-tenantId</b> - The Microsoft Entra tenant (directory) ID.<br /></td></tr>
                    <tr><td><b>auth-clientId</b> - The client (application) ID of an App Registration in the tenant.<br /></td></tr>
                    <tr><td><b>auth-clientSecret</b> - The secret key for the application (used only with "secret" type).<br /></td></tr>
                    <tr><td><b>auth-type</b> - Specifies the authentication type, either "secret" or "dedicated".<br /></td></tr>
                    <tr><td><b>folders-in-id</b> -  Configuration parameter that defines the folder in the mailbox from which emails will be retrieved for processing.<br /></td></tr>
                    <tr><td><b>folders-out-id</b> - Configuration parameter that defines the folder where emails will be moved after they have been processed.<br /></td></tr>
                    <tr><td><b>process-user_id</b> - ID of the user responsible for initiating the process.<br /></td></tr>
                    <tr><td><b>process-tproc_id</b> - ID of the template process used to create the instance process.<br /></td></tr>
                    <tr><td><b>process-tproc_version</b> -  ID of the group header under which the process instance will be categorized.<br /></td></tr>
                    <tr><td><b>process-header_id</b> - Version of the template process to ensure the correct iteration is used.<br /></td></tr>
                    <tr><td><b>process-holderData</b> - Optional field for email data or mapped values.<br /></td></tr>
                    <tr><td><b>setEmailProcessedOnError</b> - Defaultly set to true. When there is an error in creating process from email it will mark the email as read <br />
                    and move it to the out folder. This will prevent the email from being processed indefinitely.<br /></td></tr>
                    <tr><td><b>errorEmailAddress</b> - Additional email address to send errors from parsing.<br /></td></tr>
                    <tr><td><b>ignoreAttachmentErrors</b> - If set to true, errors related to processing attachments will be ignored.<br /></td></tr>
                    <tr><td><b>ignoreVariablesUpdateAndUseDataHolder</b> -If set to true variables for the process instance will not be updated. Instead, data from the email <br />
                    or a predefined mapping will be stored in the holderData.<br /></td></tr>
                    <tr><td><b>useEmailObjectInDataHolder</b> - When set to true, the complete email object will be saved in the holderData property of the process.<br /></td></tr>
                    <tr><td><b>mapping-field1</b> - <br /></td></tr>
                    <tr><td><b>mapping-field2</b> - <br /></td></tr>
                    <tr><td><b>mapping-field2-value</b> - <br /></td></tr>
                    <tr><td><b>mapping-field1-option</b> - <br /></td></tr>
                    <tr><td><b>mapping-field1-isConstant</b> - <br /></td></tr>
                </tbody>
            </table>
            </div>
        `;

    public timeout = 3600000;

    constructor(
        protected auth: Auth,
        protected tasLogger: TasLogger,
        private withoutTemplateMailClient: WithoutTemplateMailClient,
        dbClient: DatabaseClient,
        protected readonly cacheClient: CacheClient,
    ) {
        super(auth, tasLogger, dbClient, cacheClient);
    }

    async start(config: IMsGraphConfig) {
        try {
            const cronRunId: number = await this.getCronRunId();

            for (const configItem of config.items) {
                const client = new MsGraphClient(configItem.auth);

                // First get emails from "in" folder
                const emails = await client.listMailsFromFolder(
                    configItem.folders.in.id,
                    undefined,
                    { top: config.amountOfEmailByRun || 100, skip: 0 },
                );

                let email: MsGraphMail;

                // Process each email individually
                for (const emailToProcess of emails.value) {
                    let iprocId: number;
                    try {
                        const emailAndAttachments =
                            await client.getEmailAndDecodeAttachments(
                                emailToProcess.id,
                            );
                        const { attachments } = emailAndAttachments;
                        email = emailAndAttachments.email;

                        if (
                            !(await this.checkIfEmailIsStandardMessageType(
                                email,
                                configItem,
                                cronRunId,
                            ))
                        ) {
                            return await client.moveMail(
                                email.id,
                                configItem.folders.out.id,
                            );
                        }

                        if (email.isRead) {
                            await this.logEmailIsAlreadyRead(email);
                            client.setEmailAsUnread(email.id);
                            continue;
                        }

                        // create Process From Configuration Template
                        let process: Process;
                        const user =
                            await globalThis.container.service.auth.getUserInfoFromId(
                                configItem.process.user_id,
                                false,
                            );
                        await globalThis.database.transaction(async (trx) => {
                            const processRepo = globalThis.orm.repo(
                                "process",
                                trx,
                            );
                            const tproc = await globalThis.orm
                                .repo("templateProcess", trx)
                                .get(
                                    [
                                        configItem.process.tproc_id,
                                        configItem.process.tproc_version || 1,
                                    ],
                                    ["TPROC_ID", "TPROC_DEFAULT_CASE_NAME"],
                                );
                            const iproc =
                                await processRepo.copyInstanceFromTemplate(
                                    configItem.process.user_id,
                                    [
                                        configItem.process.tproc_id,
                                        configItem.process.tproc_version || 1,
                                    ],
                                    configItem.process.header_id,
                                    tproc.TPROC_DEFAULT_CASE_NAME,
                                    null,
                                    user.ORGANIZATION.ORGSTR_ID,
                                    null,
                                    "M",
                                    null,
                                    false,
                                );
                            // THIS IS REDUNDANT VARIABLE - iprocId is included in process
                            iprocId = iproc.IPROC_ID;
                            // Set Process start, in case it gets Errored
                            await trx
                                .select()
                                .from("INSTANCE_PROCESSES")
                                .where("IPROC_ID", iprocId)
                                .update({
                                    IPROC_ACTUAL_START_DATE: new Date(),
                                });
                            process = iproc;
                        });

                        // handleMailAttachments
                        // Hard code inline attachments
                        const expression = 'src="cid:(.*?)"';
                        const externalExpression = 'src="http(.*?)"';
                        let html = email.body.content;

                        // Get inline images
                        const results = html.match(new RegExp(expression, "g"));
                        if (results) {
                            results.forEach((result) => {
                                // Capturing groups don't work in this case, skip 'src="cid:' and truncate ending quote
                                const fileId = result.substring(
                                    9,
                                    result.length - 1,
                                );

                                // Get inline attachment content
                                let inlineAttachment = _.find(attachments, {
                                    ContentId: fileId,
                                });

                                // None found, try signature
                                if (!inlineAttachment) {
                                    inlineAttachment = _.find(attachments, {
                                        name: `${fileId.substring(0, fileId.indexOf("@"))}`,
                                    });
                                }

                                // Inline not found? Could be external img URL etc.
                                if (inlineAttachment) {
                                    // Replace inline attachment internal_id with it's Base64 content
                                    html = html.replace(
                                        result,
                                        `src="data:image/jpeg;base64,${(inlineAttachment as Pick<MsGraphFileAttachment, "name" | "contentBytes">).contentBytes}"`,
                                    );
                                }
                            });
                        }

                        // Replace external image sources with hard coded <external_img> for aesthetic purposes
                        const externalResults = html.match(
                            new RegExp(externalExpression, "g"),
                        );
                        if (externalResults) {
                            externalResults.forEach((result) => {
                                html = html.replace(
                                    result,
                                    `src="data:image/png;base64,${EXTERNAL_IMAGE}"`,
                                );
                            });
                        }

                        // Rewrite with new content
                        email.body.content = html;

                        // Make Attachment names unique
                        const itemsCount = _.countBy(
                            _.map(attachments, "Name"),
                        );
                        attachments.forEach((attachment) => {
                            const actualFileNameCount = itemsCount[
                                attachment.name
                            ]--;

                            if (actualFileNameCount > 1) {
                                const { name, ext } = path.parse(
                                    attachment.name,
                                );
                                attachment.name = `${name}_${actualFileNameCount}${ext}`;
                            }
                        });

                        try {
                            await this.uploadAttachmentsIntoTasAndLinkWithProcess(
                                attachments,
                                user,
                                // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                                iprocId,
                            );
                        } catch (error) {
                            if (configItem.ignoreAttachmentErrors === true) {
                                // Ignore the error, it is to be handled in the Workflow branching by AttachmentErrorIsPresent in the variable mapping
                                this.tasLogger.error(
                                    "Ignoring MsGraphCreateProcessesFromMailCron attachment error!",
                                    {
                                        // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                                        iproc_id: iprocId,
                                        error,
                                    },
                                );
                            } else {
                                throw error;
                            }
                        }

                        await globalThis.database.transaction(async (trx) => {
                            const varRepo = globalThis.orm.repo(
                                "variable",
                                trx,
                            );

                            // Sanitization
                            Object.values(configItem.mapping).forEach(
                                (value) => {
                                    switch (value) {
                                        case "body.content": {
                                            // Get default options
                                            const { allowedTags } =
                                                sanitizeHtml.defaults;
                                            // Remove 'style'
                                            allowedTags.splice(
                                                allowedTags.indexOf("style"),
                                                1,
                                            );
                                            // Add 'img'
                                            allowedTags.push("img");
                                            // Sanitize
                                            const options = {
                                                allowedTags,
                                                allowedSchemesByTag: {
                                                    img: ["data"],
                                                }, // Allow 'img src="data...'
                                            };
                                            email.body.content = sanitizeHtml(
                                                email.body.content,
                                                options,
                                            );
                                            break;
                                        }
                                        default:
                                            break;
                                    }
                                },
                            );

                            try {
                                // Sync Variables
                                if (
                                    configItem.ignoreVariablesUpdateAndUseDataHolder ===
                                    true
                                ) {
                                    if (
                                        configItem.useEmailObjectInDataHolder ===
                                        true
                                    ) {
                                        process.holderData = email;
                                    } else {
                                        process.holderData =
                                            varRepo.getValuesByMapping(
                                                email,
                                                configItem.mapping,
                                                true,
                                                null,
                                                true,
                                                true,
                                                ";",
                                            );
                                    }
                                } else {
                                    process.holderData = {};
                                    await varRepo.updateForProcessByMapping(
                                        process.IPROC_ID,
                                        email,
                                        configItem.mapping,
                                        true,
                                        null,
                                        true,
                                        true,
                                        ";",
                                    );
                                }
                            } catch (err: any) {
                                this.tasLogger.error(err.message, {
                                    iproc_id: process.IPROC_ID,
                                    tproc_id: configItem.process.tproc_id,
                                    mailData: email,
                                    mapping: configItem.mapping,
                                    err,
                                    cronRunId,
                                });
                                throw err;
                            }
                        });

                        try {
                            // Activate process
                            const orm = new OrmFactory(
                                globalThis.database,
                                globalThis.dynamicConfig.db.client,
                            );
                            const wf = new Workflow(orm, user);
                            await this.tasLogger.runTask(async () => {
                                this.tasLogger.setContextProperty(
                                    "category",
                                    LogCategory.CATEGORY_WORKFLOW,
                                );
                                await wf.start(process);
                            });
                        } catch (error: any) {
                            await this.handleProcessStartFailedError(
                                error,
                                email,
                                // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                                iprocId,
                                configItem,
                                client,
                            );
                        }

                        // Check the Process status, make sure the WorkFlow hasn't failed without an error
                        const processBeforeMove = await globalThis.database
                            .select()
                            .from("INSTANCE_PROCESSES")
                            // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                            .where("IPROC_ID", iprocId)
                            .first();

                        if (
                            ![
                                PROCESS.STATUS_ACTIVE,
                                PROCESS.STATUS_DONE,
                            ].includes(processBeforeMove.IPROC_STATUS)
                        ) {
                            throw new InternalException(
                                `Process initialized in invalid state '${processBeforeMove.IPROC_STATUS}'`,
                                "INVALID_WORKFLOW",
                                {
                                    processBeforeMove,
                                    // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                                    iprocId,
                                },
                            );
                        }

                        await client.setEmailAsRead(email.id);

                        // Updating mail's attr can (and should) change it's changeKey.
                        // Need to keep track of it if chaining operations
                        // mailProcessingParams.mailInfo.ChangeKey =
                        //     updateResponse[0].ItemId.attributes.ChangeKey;

                        try {
                            await client.moveMail(
                                email.id,
                                configItem.folders.out.id,
                            );
                        } catch (mailMoveError: any) {
                            await this.handleMailMoveError(
                                mailMoveError,
                                // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                                iprocId,
                                email,
                                configItem,
                                client,
                            );
                        }
                    } catch (mailProcessingErr: any) {
                        this.tasLogger.error(mailProcessingErr);
                        await this.handleProcessingEmailError(
                            mailProcessingErr,
                            // @ts-expect-error Variable 'email' is used before being assigned.ts(2454)
                            email,
                            // @ts-expect-error Variable 'iprocId' is used before being assigned.ts(2454)
                            iprocId,
                        );
                    }
                }
            }
        } catch (cronRunErr: any) {
            this.tasLogger.error(
                `Cron (MsGraphCreateProcessesFromMail) error: ${cronRunErr.message}`,
                { cronRunErr },
            );
            throw cronRunErr;
        }
    }

    private async checkIfEmailIsStandardMessageType(
        mail: MsGraphMail,
        configItem: IMsGraphConfig["items"][0],
        _cronRunId: number,
    ) {
        if (!mail) {
            return false;
        }
        if (!configItem) {
            return false;
        }

        return true;
    }

    private async logEmailIsAlreadyRead(mail: MsGraphMail): Promise<void> {
        this.tasLogger.warning(
            "Skipping email processing because it is already 'read'. Setting status to 'unread'.",
            {
                isRead: mail.isRead,
                mailId: mail.id,
            },
        );
    }

    private async uploadAttachmentsIntoTasAndLinkWithProcess(
        attachments: Pick<MsGraphFileAttachment, "name" | "contentBytes">[],
        user: any,
        iprocId: number,
    ): Promise<
        {
            result: boolean;
            id: any;
            name: any;
            message: any[];
        }[]
    > {
        const uploadFileResults: {
            result: boolean;
            id: any;
            name: any;
            message: any[];
        }[] = [];

        const tempDirPath = path.resolve(
            `${globalThis.dynamicConfig.paths.tmp}/uploads/`,
        );
        for (const attachment of attachments) {
            const fileTempDestination = `${tempDirPath}${path.sep}${attachment.name}`;
            await fs.promises.writeFile(
                fileTempDestination,
                attachment.contentBytes,
                "base64",
            );

            // Need to make a new transaction, 15sec timeout :(
            await globalThis.database.transaction(async (trx) => {
                const accessLogger = new DmsAccessLogger(trx);
                const dmsFileRepo = globalThis.orm.repo("dmsFile", trx);

                try {
                    const result = await dmsFileRepo.saveFile(
                        attachment.name,
                        user,
                        iprocId,
                        null,
                        fileTempDestination,
                        null,
                    );
                    await accessLogger.log(
                        result.id,
                        iprocId,
                        null,
                        user.USER_ID,
                        DmsAccessLogger.consts.EWS_COPY_ATTACHMENT,
                    );
                    uploadFileResults.push(result);
                } catch (err: any) {
                    await accessLogger.log(
                        null,
                        iprocId,
                        null,
                        user.USER_ID,
                        DmsAccessLogger.consts.EWS_COPY_ATTACHMENT,
                        err.message,
                    );
                    throw err;
                }
            });
        }
        return uploadFileResults;
    }

    private async handleProcessingEmailError(
        err: Error,
        email: MsGraphMail,
        iprocId?: number,
    ): Promise<void> {
        // Error process
        if (iprocId) {
            await globalThis.orm.repo("process").fail(iprocId);
            this.tasLogger.error("Failing process!", {
                iproc_id: iprocId,
                emailId: email.id,
                err,
            });
        }

        // Do not log Auth
        this.tasLogger.error("Error while creating Process!", {
            iproc_id: iprocId,
            emailId: email.id,
            err,
        });

        await this.sendErrorMailMessage(err, {
            type: "Mail",
            info: email.subject,
        });
    }

    private async sendErrorMailMessage(
        error: Error,
        objectInfo: { type: string; info: string },
        itemConfig?: { errorEmailAddress: string },
    ): Promise<void> {
        const addresses = [
            ...globalThis.dynamicConfig.crons.sendErrorMailAddresses,
            ...globalThis.dynamicConfig.crons.ews.errorMailAddresses,
        ];
        if (itemConfig) {
            addresses.push(itemConfig.errorEmailAddress);
        }
        const subjectFull = `[MS-GRAPH - FAILED] => ${objectInfo.type}: ${objectInfo.info}`;
        const message = `Cron job failed to finish successfully
            at '${moment(new Date())}' on instance '${globalThis.dynamicConfig.frontendUrl}'\n
            There was a problem with object '${JSON.stringify(objectInfo)}'\n
            Error details: ${error}}\n`;

        const mailOptions: TasSendMailOptions = {
            from: globalThis.dynamicConfig.mail.from,
            to: undefined,
            replyTo: globalThis.dynamicConfig.mail.noReplyMail,
            subject: subjectFull,
            html: message,
            bcc: [],
        };

        if (addresses.length > 1 && Array.isArray(mailOptions.bcc)) {
            mailOptions.to = [globalThis.dynamicConfig.mail.noReplyMail];
            mailOptions.bcc = _.uniq(mailOptions.bcc.concat(addresses).sort());
        } else {
            mailOptions.to = addresses;
        }

        await this.withoutTemplateMailClient.sendEmail(mailOptions);
    }

    private async handleProcessStartFailedError(
        error: Error,
        mail: MsGraphMail,
        iprocId: number,
        configItem: IMsGraphConfig["items"][0],
        client: MsGraphClient,
    ) {
        this.tasLogger.error("Error in starting workflow", {
            iproc_id: iprocId,
            error,
        });
        await this.sendErrorMailMessage(
            error,
            { type: "Mail", info: mail.subject },
            configItem.config,
        );
        if (
            configItem.config &&
            configItem.config.setEmailProcessedOnError === false
        ) {
            // @tas-627 Oznacit email jako precteny a presunout ho do 'out' folderu (nove defaultni chovani)
            await this.tasLogger.runTask(async () => {
                this.tasLogger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_EWS,
                );
                try {
                    await client.setEmailAsRead(mail.id);
                    await client.moveMail(mail.id, configItem.folders.out.id);
                    this.tasLogger.warning(
                        `Email marked as read and moved to out folder. It will not be processed again!`,
                        {
                            iproc_id: iprocId,
                            configItem,
                        },
                    );
                } catch (err) {
                    this.tasLogger.error(
                        "Failed to move the email to the out folder. It might be processed indefinitely!!!",
                        {
                            iproc_id: iprocId,
                            configItem,
                            err,
                        },
                    );
                }
            });
        }
        throw error;
    }

    private async handleMailMoveError(
        mailMoveError: Error,
        iprocId: number,
        mail: MsGraphMail,
        configItem: IMsGraphConfig["items"][0],
        client: MsGraphClient,
    ) {
        await this.tasLogger.runTask(async () => {
            this.tasLogger.setContextProperty(
                "category",
                LogCategory.CATEGORY_EWS,
            );
            this.tasLogger.error(`moveMail error: ${mailMoveError.message}`, {
                iproc_id: iprocId,
                configItem,
                mailMoveError,
            });
            try {
                await client.getMail(mail.id);
            } catch (getMailError) {
                // The moveMail request seemingly failed, but the Mail has actually been moved to the OUT folder
                // FAILing the process now would result in a Mail loss situation
                this.tasLogger.warning(
                    `Mail has actually moved during: ${mailMoveError.message}`,
                    {
                        iproc_id: iprocId,
                        configItem,
                        getMailError,
                    },
                );
                return;
            }

            // Mail was supposed to be moved to the OUT folder but the Mail is still in the IN folder
            // It's safe to FAIl this process, because the Mail will be 'unread' and processed in the next Cron run
            this.tasLogger.warning(
                "The mail remains in the IN folder, and the will be reprocessed in the next Cron run.",
                {
                    iproc_id: iprocId,
                    configItem,
                },
            );
            throw mailMoveError;
        });
    }
}
