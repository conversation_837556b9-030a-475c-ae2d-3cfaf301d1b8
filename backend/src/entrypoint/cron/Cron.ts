import { BaseCron } from "./BaseCron";
import { OrmFactory } from "../../api/orm/OrmFactory";
import { Workflow } from "../../api/workflow/Workflow";
import { Service } from "typedi";
import { TasLogger } from "../../utils/logger/TasLogger";
import { Auth } from "../../service/authorization/Auth";
import { DatabaseClient } from "../../client/database/databaseClient";
import { CacheClient } from "../../client/cache/cacheClient";
import { STATUS_NOT_ACTIVE } from "../../api/orm/entity/const/cronConsts";
import { LogCategory } from "../../utils/logger/logConsts";

@Service()
export class Cron extends BaseCron {
    public schedule = "01 */5 * * * *";

    public parameters = JSON.stringify({ fromTime: null });

    public alias = "Run scheduled tasks and events";

    public description = "This job runs all scheduled tasks and events.";

    public defaultStatus = STATUS_NOT_ACTIVE;

    public help =
        "For time limiting of searched tasks and events in this cron fill in fromTime parameter with time (string or timestamp) default is 1970";

    public timeout = undefined;

    constructor(
        protected auth: Auth,
        protected tasLogger: TasLogger,
        dbClient: DatabaseClient,
        protected cacheClient: CacheClient,
    ) {
        super(auth, tasLogger, dbClient, cacheClient);
    }

    async start(params?: { fromTime?: string | null }) {
        try {
            const fromTime = params?.fromTime
                ? new Date(params.fromTime)
                : null;

            await this.runEvents(fromTime);
            await this.runScheduledTasks(fromTime);
        } catch (err: any) {
            this.tasLogger.error(`Cron (Cron) error: ${err.message}`, {
                err,
            });
            throw err;
        }
    }

    private async runScheduledTasks(fromTime: Date | null) {
        // Run scheduled tasks
        let i = 0;

        const collection = await globalThis.orm
            .repo("instanceTask")
            .getScheduledTasks(fromTime);

        collection.knex.orderBy([
            { column: "ITASK_DUE_DATE_START", order: "asc" },
            { column: "ITASK_ID", order: "asc" },
        ]);

        const scheduledTasks = await collection.collectAll();

        try {
            for (const task of scheduledTasks) {
                i += 1;
                await globalThis.database
                    .transaction(async (trx) => {
                        // transaction
                        const proc = await globalThis.orm
                            .repo("process", trx)
                            .get(task.IPROC_ID);
                        this.tasLogger.info(
                            `Activating scheduled Task ${task.ITASK_ID} ${i}/${scheduledTasks.length}`,
                            {
                                iproc_id: proc.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                        const user =
                            await globalThis.container.service.auth.getUserInfoFromId(
                                proc.IPROC_INST_OWNER_USER_ID,
                                true,
                            );
                        const wf = new Workflow(
                            new OrmFactory(
                                trx,
                                globalThis.dynamicConfig.db.client,
                            ),
                            user,
                        );
                        await this.tasLogger.runTask(async () => {
                            this.tasLogger.setContextProperty(
                                "category",
                                LogCategory.CATEGORY_WORKFLOW,
                            );
                            await wf.activateTask(task);
                        });
                        this.tasLogger.info(
                            `Scheduled Task ${task.ITASK_ID} activated ${i}/${scheduledTasks.length}. next...`,
                            {
                                iproc_id: proc.IPROC_ID,
                                itask_id: task.ITASK_ID,
                            },
                        );
                    })
                    .catch(async (err) => {
                        this.tasLogger.error(err.message, {
                            err,
                            itask_id: task.ITASK_ID,
                            task,
                        });
                    });
            }
        } catch (err: any) {
            this.tasLogger.error(err.message, {
                err,
            });
        }
    }

    private async runEvents(fromTime: Date | null) {
        // Run background events.
        const events = await globalThis.orm
            .repo("event")
            .getWaitingBackgroundEvents(fromTime)
            .collectAll();
        let i = 0;
        for (const event of events) {
            i += 1;
            return await globalThis.database
                .transaction(async (trx) => {
                    const user =
                        await globalThis.container.service.auth.getUserInfoFromId(
                            event.raw.IPROC_INST_OWNER_USER_ID,
                            true,
                        );
                    const wf = new Workflow(
                        new OrmFactory(trx, globalThis.dynamicConfig.db.client),
                        user,
                    );
                    await this.tasLogger.runTask(async () => {
                        this.tasLogger.setContextProperty(
                            "category",
                            LogCategory.CATEGORY_WORKFLOW,
                        );
                        await wf.activateEvent(event);
                    });
                    this.tasLogger.info(
                        `Event activated ${i}/${events.length}. next...`,
                        {
                            iproc_id: event.raw.IPROC_ID,
                        },
                    );
                })
                .catch(async (err) => {
                    this.tasLogger.error(err.message, {
                        err,
                        eve_id: event.EVE_ID,
                        event,
                    });
                });
        }
    }
}
