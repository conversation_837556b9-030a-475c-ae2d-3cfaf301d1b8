import { BaseCron } from "./BaseCron";

import { Service } from "typedi";
import { STATUS_NOT_ACTIVE } from "../../api/orm/entity/const/cronConsts";

@Service()
export class SyncProcessRightsCron extends BaseCron {
    public schedule = "00 00 03 * * *";

    public parameters = undefined;

    public alias = "Sync process rights.";

    public description = "Sync process rights.";

    public defaultStatus = STATUS_NOT_ACTIVE;

    public help = undefined;

    public timeout = undefined;

    async start() {
        try {
            await globalThis.database.transaction((trx) => {
                const repo = globalThis.orm.repo("externalRight", trx);
                return repo.rebuildRights();
            });
        } catch (err: any) {
            this.tasLogger.error(
                `Cron (SyncProcessRightsCron) error: ${err.message}`,
                err,
            );
            throw err;
        }
    }
}
