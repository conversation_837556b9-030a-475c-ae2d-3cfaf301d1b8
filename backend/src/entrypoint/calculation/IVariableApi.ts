// @ts-nocheck
// @ts-nocheck
import { BaseA<PERSON> } from "./BaseApi";
import { UserException } from "../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../api/services/UtilsService";
import * as VARIABLE from "../../api/orm/entity/const/variableConst";
import { StorageApi } from "./StorageApi";

const privateContext = new WeakMap();

export class IVariableApi extends BaseApi {
    constructor(connection, sandbox) {
        super(connection, sandbox);

        privateContext.set(this, {
            connection,
            sandbox,
        });

        this.readOnly = false;

        this.generateGettersAndSetters(
            "variable",
            "Variable",
            [],
            ["TVAR_ID", "IVAR_ID"],
        );
    }

    /**
     * @hashtag Variable
     * @cs Uloží novou hodnotu attributu
     * @en Saves the new attribute value
     * @param {string} value
     * @returns {*}
     */
    async setAttribute(attrName, value) {
        if (["TVAR_ID", "IVAR_ID"].includes(attrName)) {
            throw new UserException(
                `'Write' access denied for '${attrName}' attribute of entity.`,
            );
        }

        privateContext.get(this).sandbox.variable[attrName] = value;
        await globalThis.orm
            .repo("Variable", privateContext.get(this).connection)
            .store(privateContext.get(this).sandbox.variable);
    }

    /**
     * @hashtag Process
     * @cs Legacy funkce pro uložení hodnoty attributu. Použijte setAttribute.
     * @en Legacy function for saving the new attribute value. Use setAttribute instead.
     * @param {string} value
     * @returns {*}
     */
    async setAttributeLegacy(attrName, value) {
        if (["TVAR_ID", "IVAR_ID"].includes(attrName)) {
            throw new UserException(
                `'Write' access denied for '${attrName}' attribute of entity.`,
            );
        }
        // tas-700 Kontrola migrace proc.setAttribute 4.15 -> 4.16
        // this.error(`Directly changing the attribute '${this.constructor.name}.${attrName}' is not recommended due to poor performance, and has been deprecated. Instead, please use the new 'setAttribute('${this.constructor.name}.${attrName}', value)' method to modify this object's attribute.`);
        this.info(
            `Changing task property ${attrName} from ${privateContext.get(this).sandbox.task[attrName]} to ${value}`,
        );
        privateContext.get(this).sandbox.variable[attrName] = value;
        await globalThis.orm
            .repo("Variable", privateContext.get(this).connection)
            .store(privateContext.get(this).sandbox.variable);
    }

    /**
     * @hashtag Variable
     * @cs Zjistí, jestli je proměnná zaškrtnuta.
     * @en Determines if the variable is checked.
     * @returns {boolean}
     */
    isChecked() {
        return !!privateContext.get(this).sandbox.variable.IVAR_NUMBER_VALUE;
    }

    /**
     * @hashtag Variable
     * @cs Vrátí proměnnou.
     * @en Returns the variable.
     * @returns {IVariableApi}
     */
    getVariable() {
        this.validateWriteState();
        return privateContext.get(this).sandbox.variable;
    }

    /**
     * Get variable value
     * @hashtag Variable
     * @cs Vrátí hodnotu proměnné.
     * @en Returns the variable value.
     * @visibility visible
     * @returns {object}
     */
    getValue(language) {
        const { variable } = privateContext.get(this).sandbox;
        const { value, IVAR_TYPE: type } = variable;

        switch (type) {
            case VARIABLE.TYPE_DYNAMIC_TABLE: {
                // Dynamic table must return Index in calculations.
                if (variable.isMultichoice()) {
                    return variable.getDtMultiIndexesAndValues().indexes;
                }
                return variable.IVAR_DT_INDEX === null
                    ? ""
                    : variable.IVAR_DT_INDEX;
            }
            case VARIABLE.TYPE_DYNAMIC_ROW:
                return JSON.stringify(value);
            case VARIABLE.TYPE_DYNAMIC_LIST: {
                // @t3b-1828 u Dynamickeho seznamu se spatne vyhodnocuje .getValue
                if (
                    ![
                        VARIABLE.ATTR_USER,
                        VARIABLE.ATTR_ROLE,
                        VARIABLE.ATTR_ORG_STRUCT,
                    ].includes(variable.IVAR_ATTRIBUTE)
                ) {
                    return this;
                }
                if (
                    value === null ||
                    (!value.length && typeof value !== "number")
                ) {
                    // Backwards compatibility - no 'null' or '[]'
                    return "";
                }
                break;
            }
            case VARIABLE.TYPE_TEXT: {
                if (value === null) {
                    return ""; // php compatibility null + "abc" = "abc" not "nullabc"
                }
                if (UtilsService.isNumericString(value)) {
                    return Number(value); // php compatibility "10" + 5 = 15 not 105 !!
                }
                break;
            }
            case VARIABLE.TYPE_TEXT_LIST: {
                // @t3b-1595 Zobrazení jazykových mutací LOV proměnných v CO
                if (language) {
                    return (
                        variable._raw[
                            `TVARLOV_TEXT_VALUE_${language.toUpperCase()}`
                        ] || value
                    );
                }
            }
            case VARIABLE.TYPE_NUMBER: {
                return Number(value);
            }
        }

        return value;
    }

    /**
     * Get variable value parsed into JSON.
     * @hashtag Variable
     * @cs Vrátí hodnotu proměnné rozpadnutou do JSON.
     * @en Returns the variable value decomposed into JSON.
     * @visibility visible
     * @returns {object}
     */
    getJSON() {
        try {
            return JSON.parse(this.getValue());
        } catch (_err) {
            this.warn(
                `Nepodařilo se hodnotu proměnné ${privateContext.get(this).sandbox.variable.IVAR_NAME} konvertovat do JSON`,
                { value: this.getValue() },
            );
            return this.getValue();
        }
    }

    /**
     * @hashtag Variable
     * @cs Nastaví hodnotu objektu
     * @en Sets the object value
     *
     * @param {String} key
     * @param {Any} value
     * @returns {*}
     */
    async setJSONAttr(key, value) {
        let json = this.getJSON();
        if (!json) {
            json = {};
        }
        json[key] = value;
        await this.setValue(JSON.stringify(json));
        return "CALC_CALL";
    }

    /**
     * @hashtag Variable
     * @cs Získá hodnotu objektu
     * @en Gets the object value
     *
     * @param {String} key
     * @returns {Any}
     */
    getJSONAttr(key) {
        const json = this.getJSON();
        if (!json) {
            return null;
        }
        return json[key];
    }

    /**
     * @hashtag Variable
     * @cs Vrací textovou podobu proměnné. U DT vrací Title jinak vrací getValue().
     * @en Returns the text form of the variable. For DT, returns Title otherwise getValue().
     * @returns {*}
     */
    getTitle() {
        if (privateContext.get(this).sandbox.variable.IVAR_TYPE === "DT") {
            if (privateContext.get(this).sandbox.variable.IVAR_MULTI !== "X") {
                return privateContext.get(this).sandbox.variable
                    .IVAR_TEXT_VALUE;
            }
            if (
                Array.isArray(
                    JSON.parse(
                        privateContext.get(this).sandbox.variable
                            .IVAR_MULTI_SELECTED,
                    ),
                )
            ) {
                return JSON.parse(
                    privateContext.get(this).sandbox.variable
                        .IVAR_MULTI_SELECTED,
                ).map((obj) => Object.values(obj)[0]);
            }
            return privateContext.get(this).sandbox.variable
                .IVAR_MULTI_SELECTED;
        }
        return this.getValue();
    }

    /**
     * @hashtag Variable
     * @cs U DT vrací textovou hodnotu prvku. U Dynamického seznamu (uživatel, role, org. jednotka) vrací id (u multi výběru vrací pole id). V ostatních případech vrací stejnou hodnotu jako getValue()
     * @en For DT, returns the text value of the element. For Dynamic list (user, role, org. unit) it returns id (for multi selection it returns array of ids). In other cases, it returns the same value as getValue()
     * @returns {*}
     */
    getIndex() {
        if (privateContext.get(this).sandbox.variable.IVAR_TYPE === "DT") {
            return privateContext.get(this).sandbox.variable.IVAR_DT_INDEX;
        }
        if (
            privateContext.get(this).sandbox.variable.IVAR_TYPE === "DL" &&
            ["U", "O", "R"].indexOf(
                privateContext.get(this).sandbox.variable.IVAR_ATTRIBUTE,
            ) !== -1
        ) {
            return privateContext.get(this).sandbox.variable.value;
        }
        return this.getValue();
    }

    /**
     * @hashtag Variable
     * @cs Vrací název proměnné.
     * @en Returns the variable name
     * @returns {string}
     */
    getName() {
        return privateContext.get(this).sandbox.variable.IVAR_NAME;
    }

    /**
     * @hashtag Variable
     * @cs Vratí DR jako JSON
     * @en Get DR as JSON.
     *
     * @param {string} col
     * @returns {string}
     */
    getDR(col) {
        return this.getDRObject(col);
    }

    /**
     * @visibility private
     */
    getDRObject(col?) {
        if (col) {
            return privateContext.get(this).sandbox.variable.value[col];
        }

        return privateContext.get(this).sandbox.variable.value;
    }

    /**
     * Store variable.
     * Translates Wrapped values into raw value.
     * @visibility hidden
     * @param val
     */
    async store() {
        this.validateWriteState();
        const varRepo = globalThis.orm.repo(
            "variable",
            privateContext.get(this).connection,
        );
        return await varRepo
            .store(privateContext.get(this).sandbox.variable)
            .then((ok) => ok)
            .catch((err) => {
                this.error({
                    message:
                        "Error at IVariableApi.setValueAsync(). Can not set variable value.",
                    err,
                    variable: privateContext.get(this).sandbox.variable,
                });
                throw err;
            });
    }

    /**
     * @hashtag Variable
     * @cs Vrací stream souboru. Použití pouze na proměnnou typu příloha.
     * @en Returns the file stream. Apply only to the variable of type attachment.
     *
     * @param {string} encoding {"cs": "Defaultně 'binary'", "en": "Default 'binary'"}
     * @returns {string}
     */
    async getFileStream(encoding: BufferEncoding = "binary") {
        const storage = new StorageApi(
            privateContext.get(this).connection,
            privateContext.get(this).sandbox,
            privateContext.get(this).currentUser,
        );

        const files = privateContext.get(this).sandbox.variable.value;
        if (!Array.isArray(files) || files.length === 0) {
            return null;
        }
        const fileName = files[0];
        return await storage.getDmsContent(fileName, encoding);
    }

    /**
     * Set variable value.
     * @hashtag Variable
     * @cs Změní hodnotu proměnné. U proměnné typu 'Dynamický seznam' lze použít hodnotu 'null' pro vynulování.
     * @en Changes the variable value. For a variable of type 'Dynamic list', the value 'null' can be used to reset.
     * @visibility visible
     *
     * @param value
     * @returns {*}
     */
    async setValue(value) {
        this.validateWriteState();
        if (typeof value === "undefined") {
            const err = new Error(
                "Can not set variable with undefined. You should fill null or empty string instead." +
                    " If you dont want to change variable value use CALC_CALL constant.",
            );
            this.error(err);
            throw err;
        }

        let primitiveValue;
        // Allow emptying of the list, i.e. 'vars[varName].setValue(0)'
        if (
            privateContext.get(this).sandbox.variable.IVAR_TYPE ===
                VARIABLE.TYPE_DYNAMIC_LIST &&
            !value
        ) {
            /**
             * t3b-755 Vynulování proměnné typu uživatel
             */
            primitiveValue = null;
        } else {
            // Returns value based on value type. Can be e.g. UserApi -> return user_id
            primitiveValue = await this.getPrimitiveValue(
                value,
                privateContext.get(this).sandbox.variable.IVAR_TYPE,
                privateContext.get(this).sandbox.variable.IVAR_ATTRIBUTE,
            );
        }

        // Skip this ultra magic value to be compatible with TasOld.
        if (primitiveValue === "CALC_CALL") {
            return true;
        }

        // @t3b-2703 Keep the correct mutation text value of LOVs in the sandbox
        if (
            privateContext.get(this).sandbox.variable.IVAR_TYPE ===
            VARIABLE.TYPE_TEXT_LIST
        ) {
            privateContext
                .get(this)
                .sandbox.variable.setLovDataByBaseValue(value);
        }

        privateContext.get(this).sandbox.variable.value = primitiveValue;
        privateContext.get(this).sandbox.variable._raw.CALCULATED = 1; // Hand event -> this change MUST WRITE to hidden.

        return await this.store();
    }

    /**
     * @visibility private
     * @returns {*}
     */
    inspect() {
        return this.toString();
    }

    /**
     * @visibility private
     * @returns {*}
     */
    toString() {
        return `${IVariableApi.name}(${privateContext.get(this).sandbox.variable.IVAR_ID})`;
        // try {
        //     // Returns value based on value type. Can be e.g. UserApi -> return user_id
        //     return this.getPrimitiveValue(privateContext.get(this).sandbox.variable, 'T');
        // } catch (err) {
        //     // toString is called outside of yield function.
        //     if (err.message.indexOf('await functions, yield functions') > -1) {
        //         return this.toStringAwaited();
        //     }
        //     throw err;
        // }
    }

    toStringAwaited() {
        // API use wait fiber library. Need to call as async method !!
        const foo = async () => this.toString();
        return foo();
    }

    setReadOnly() {
        this.validateWriteState();
        this.readOnly = true;
    }

    validateWriteState() {
        if (this.readOnly) {
            throw new UserException(
                "Variable is in read only state. You can not modify value.",
            );
        }
    }
}
