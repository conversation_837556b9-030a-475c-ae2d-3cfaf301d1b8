import { FastifyReply, FastifyRequest, HookHandlerDoneFunction } from "fastify";

import { AccessRights } from "../../api/utils/AccessRights";
import { UserException } from "../../utils/errorHandling/exceptions/userException";

export const customViewUpdate = async (
    req: FastifyRequest,
    res: FastifyReply,
    next: HookHandlerDoneFunction,
): Promise<void> => {
    const data: any = req.body;
    const user = await globalThis.container.service.auth.getUserData(req, res);

    if (!data.cv_id) {
        return next();
    }

    const handleError = async function (err: unknown, res: FastifyReply) {
        if (err !== null) {
            res.status((err as any).status || 500);
            throw err;
        }

        try {
            const result = await globalThis.database
                .select("U.USER_DISPLAY_NAME")
                .leftJoin("USERS as U", "CV.CV_USER_ID", "U.USER_ID")
                .from("CUSTOM_VIEWS as CV")
                .where("CV.CV_ID", data.cv_id);
            let username = "Admin";
            if (Array.isArray(result) && result.length > 0) {
                username = result[0].USER_DISPLAY_NAME;
            }

            throw new UserException(
                `Lack of permissions. Contact ${username}.`,
                "LACK_OF_PERMISSIONS_CUSTOM_VIEW",
            );
        } catch (queryError: unknown) {
            res.status((queryError as any).status || 500);
            throw queryError;
        }
    };

    const ac = new AccessRights(globalThis.database);
    try {
        const acc = await ac.getAccessRight(
            "CUSTOM_VIEWS",
            data.cv_id,
            user.USER_ID,
        );
        if (acc.W !== "Y") {
            // Find CV owner
            await handleError(null, res);
        }
        return next();
    } catch (err: unknown) {
        await handleError(err, res);
    }
};
