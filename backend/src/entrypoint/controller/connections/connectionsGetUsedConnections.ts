import _ from "lodash";
import { FastifyReply, FastifyRequest } from "fastify";
import { Service } from "typedi";

import { BaseController } from "../baseController";
import { InputType } from "../../../entity/schema/base";
import { UtilsService } from "../../../api/services/UtilsService";

@Service()
export class ConnectionsGetUsedConnectionsController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/connections/used-connections";

    public readonly schema = {};

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isAdministrator",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const info = await UtilsService.getActiveQueries();
            return this.handleResponse(request, reply, {
                items: _.orderBy(info.queries, "timestamp", "asc"),
                info,
            });
        } catch (err) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
