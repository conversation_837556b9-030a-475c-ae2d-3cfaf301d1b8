import { FastifyReply, FastifyRequest } from "fastify";
import { Service } from "typedi";
import { DatabaseClient } from "../../../client/database/databaseClient";
import { InputType } from "../../../entity/schema/base";
import { Auth } from "../../../service/authorization/Auth";
import { PostService } from "../../../service/post/PostService";
import { TasLogger } from "../../../utils/logger/TasLogger";
import { HooksHandler } from "../../hooksHandler";
import { BaseController } from "../baseController";

@Service()
export class PostGetPostsForController extends BaseController {
    public constructor(
        hooksHandler: HooksHandler,
        auth: Auth,
        tasLogger: TasLogger,
        dbClient: DatabaseClient,
        private readonly postService: PostService,
    ) {
        super(hooksHand<PERSON>, auth, tasLogger, dbClient);
    }

    public readonly method = "GET";

    public readonly url = "/posts/:entity/:id";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                entity: {
                    type: "string" as const,
                    enum: ["template", "header", "task"] as const,
                },
                id: {
                    type: "number" as const,
                },
            },
            required: ["entity"] as const,
        },
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                disable_accent: { type: "boolean" as const },
                sort: { type: "string" as const, nullable: true },
                order: { type: "string" as const, nullable: true },
                all: { type: "boolean" as const, nullable: true },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const { collection, visibilePostMatrix } =
                await this.postService.getPostsRecords(user, {
                    entity: request.params.entity,
                    params: request.params,
                    query: request.query,
                });

            const [items, totalCount, startTime] = await this.handleGetRequest(
                request,
                collection,
                { ignoreRequestId: true },
            );

            const postIds = items.map((item) => item.POST_ID);
            const tags =
                await this.postService.postTagModule.getTagsForPosts(postIds);
            const postsWithVisibility =
                this.postService.postVisibilityModule.pairVisibilityToPosts(
                    items,
                    visibilePostMatrix,
                    tags,
                );

            const data = this.restifyData(postsWithVisibility, {
                primaryCol: "postId",
                single: false,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
            });

            return this.handleResponse(request, reply, data);
        } catch (err) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
