import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");

@Service()
export class TemplateSectionsCreateController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-processes/:tprocId/create-section/:id?";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const, nullable: true },
            },
            required: ["tprocId"] as const,
        },

        body: {
            type: "object" as const,
            properties: {
                tproc_id: { type: "string" as const },
                tsec_name: { type: "string" as const },
            },
            patternProperties: {
                "^tsec_name_": { type: "string" as const, nullable: true },
            },
            required: ["tproc_id", "tsec_name"],
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const { tprocId } = request.params;
            const data = request.body;
            let id;

            await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templateSections", trx);
                const tProcRepo = globalThis.orm.repo("templateProcess", trx);

                const entity = repo.getEntity(data.data);
                const attrs = entity.attributes();
                const attrKeys = Object.keys(attrs);

                attrKeys.forEach((key) => {
                    const lk = key.toLowerCase();
                    if (typeof data[lk] !== "undefined") {
                        entity[key] = data[lk];
                    }
                });

                await repo.store(entity);
                await tProcRepo.setTemplateChange(
                    tprocId,
                    entity.TPROC_VERSION || 1,
                    user.USER_ID,
                );

                id = entity.TSEC_ID;
            });

            return this.handleResponse(request, reply, { result: true, id });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
