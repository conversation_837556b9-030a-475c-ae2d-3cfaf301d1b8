import { Service } from "typedi";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { FastifyReply, FastifyRequest } from "fastify";

@Service()
export class TemplateSectionsRemoveController extends BaseController {
    public readonly method = "DELETE";

    public readonly url = "/template-processes/:tprocId/delete-section/:id";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const },
            },
            required: ["tprocId", "id"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const { tprocId, id } = request.params;
            // const tprocVersion = request.params.tprocVersion;
            const tprocVersion = 1;

            await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templateSections", trx);
                const tprocRepo = globalThis.orm.repo("templateProcess", trx);

                const entity = repo.getEntity();
                entity.TSEC_ID = id;

                await repo.delete(entity);

                this.tasLogger.info("Template section deleted", {
                    tsecId: id,
                    tprocId,
                    tprocVersion,
                    actorUserId: user.USER_ID,
                });

                await tprocRepo.setTemplateChange(
                    tprocId,
                    tprocVersion,
                    user.USER_ID,
                );
            });

            return this.handleResponse(request, reply, { result: true });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
