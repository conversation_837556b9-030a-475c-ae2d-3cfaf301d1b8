import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";

@Service()
export class TemplateSectionsGetController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/template-processes/:tprocId/get-sections/section/:id?";

    public readonly schema = {
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const },
            },
            required: ["tprocId"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);

            const repo = globalThis.orm.repo("templateSections");
            repo.setLocalization(user.LANGUAGE);

            const collection = repo.getById(request.params.id);
            const primaryCol = repo.entity.primaryColumn;

            const items = await this.handleGetRequest(request, collection, {
                returnEntities: true,
            });
            const processedItems = await repo.castRows(
                items[0],
                repo.entity.getAttributes(false, true),
            );

            if (
                request.params.id &&
                (!Array.isArray(processedItems) || !processedItems.length)
            ) {
                throw new UserException(
                    "Object doesn't exist.",
                    "OBJECT_NOT_FOUND",
                );
            }

            const out = processedItems.map((item) => {
                const entity = this.restifyEntity(
                    item,
                    primaryCol,
                    request.url,
                    (ent: any) => {
                        const outItem: { [key: string]: any } = {
                            tsec_id: ent.raw.TSEC_ID,
                            tsec_name: ent.raw.TSEC_NAME,
                        };

                        const sectionsEntity = globalThis.orm
                            .repo("templateSections")
                            .getEntity();
                        const attrs = sectionsEntity.attributes();

                        Object.keys(attrs).forEach((attrName) => {
                            if (attrs[attrName].translated) {
                                const parts = attrName.split("_");
                                outItem[
                                    `tsec_name_${
                                        parts[parts.length - 1]
                                    }`.toLowerCase()
                                ] = ent.raw[attrName];
                            }
                        });

                        return outItem;
                    },
                );

                return entity;
            });

            const result = this.restifyData(out, {
                primaryCol,
                single: !!request.params.id,
                totalCount: items[1],
                startTime: items[2],
                routePath: request.routeOptions.url,
                reqUrl: request.url,
                locale: user.LANGUAGE,
            });

            return this.handleResponse(request, reply, result);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
