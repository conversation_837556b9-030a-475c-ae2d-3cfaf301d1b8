import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";
import beautify from "js-beautify";
import * as taskConsts from "../../../../api/orm/entity/const/taskConst";

@Service()
export class TemplateTasksGetController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-tasks/:id";

    public readonly schema = {
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const },
                tprocVersion: { type: "number" as const },
            },
            required: ["tprocId", "tprocVersion", "id"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const repo = globalThis.orm.repo("templateTask");
            repo.setLocalization(user.LANGUAGE);
            const tprocVersion = request.params.tprocVersion;
            const id = request.params.id;

            const collection = repo.getTTask(id, tprocVersion);

            const primaryCol = repo.entity.primaryColumn;

            const result = await this.handleGetRequest(request, collection);

            const items = await repo.castRows(
                result[0],
                _.assign(
                    { TTASK_INSTRUCTION: { type: "text" } },
                    { TTASK_ENOT_BODY2: { type: "text" } },
                    { TTASK_INV_DESCRIPTION: { type: "text" } },
                    { TTASK_INV_ATTENDEES: { type: "text" } },
                    repo.entity.getAttributes(false, true),
                ),
            );

            const totalCount = result[1];
            const startTime = result[2];

            if (id && items.length > 0) {
                const ttask = items[0];

                ttask.TTASK_PETRI_NET_INPUTN = ttask.TTASK_PETRI_NET_INPUT;
                if (
                    ttask.TTASK_PETRI_NET_INPUT == 1 &&
                    ttask.TTASK_DISC_FLAG == "N"
                ) {
                    ttask.TTASK_PETRI_NET_INPUT = "O";
                } else if (
                    ttask.TTASK_PETRI_NET_INPUT == 0 &&
                    ttask.TTASK_DISC_FLAG == "N"
                ) {
                    ttask.TTASK_PETRI_NET_INPUT = "A";
                } else if (
                    ttask.TTASK_PETRI_NET_INPUT == 1 &&
                    ttask.TTASK_DISC_FLAG == "Y"
                ) {
                    ttask.TTASK_PETRI_NET_INPUT = "D";
                } else if (ttask.TTASK_DISC_FLAG == "N") {
                    ttask.TTASK_PETRI_NET_INPUT = "N";
                }

                if (ttask.TTASK_AGAIN == null) {
                    ttask.TTASK_AGAIN = taskConsts.NOT_AGAIN;
                }
                ttask.TTASK_JS = ttask.TTASK_JS
                    ? beautify.js(ttask.TTASK_JS)
                    : ttask.TTASK_JS;

                const parse = (data: any) => {
                    if (!data) {
                        return null;
                    }
                    let type = data.substring(0, 2);
                    if (type == "P+") {
                        type = "ps";
                    } else if (type == "T+") {
                        type = "ts";
                    }

                    const time = data.substring(2);
                    return { type, value: time };
                };

                ttask.TTASK_DUE_OFFSET = parse(ttask.TTASK_DUE_OFFSET);
                ttask.TTASK_DURATION = parse(ttask.TTASK_DURATION);

                const repoCalc = globalThis.orm.repo(
                    "templateTaskJSCalculation",
                );
                const calcs = await repoCalc.getForTTaskTranslated(
                    ttask.TTASK_ID,
                    tprocVersion,
                );

                ttask.ttask_calculations = calcs.map((item) => ({
                    ttjscalc_js: item.TTJSCALC_JS
                        ? beautify.js(item.TTJSCALC_JS)
                        : null,
                    ttjscalc_js_es6: item.TTJSCALC_JS_ES6
                        ? beautify.js(item.TTJSCALC_JS_ES6)
                        : null,
                    ttjscalc_exec_start: item.TTJSCALC_EXEC_START,
                    ttjscalc_exec_end: item.TTJSCALC_EXEC_END,
                    ttjscalc_exec_hand: item.TTJSCALC_EXEC_HAND ?? "N",
                    ttjscalc_exec_recalc: item.TTJSCALC_EXEC_RECALC ?? "N",
                    ttjscalc_exec_pull: item.TTJSCALC_EXEC_PULL ?? "N",
                    ttjscalc_append_scripts:
                        item.TTJSCALC_APPEND_SCRIPTS ?? null,
                }));

                const repoVarUsage = globalThis.orm.repo(
                    "templateTaskVarUsage",
                );
                const varUsages = await repoVarUsage
                    .getForTTask(ttask.TPROC_ID, ttask.TTASK_ID, tprocVersion)
                    .collectAll();

                ttask.ttask_var_mapping = varUsages.map((item) => {
                    if (
                        item._raw.TVAR_COPY_SNAPSHOT !== "Y" &&
                        ttask.TTASK_MULTIINSTANCE_FLAG === "Y"
                    ) {
                        this.tasLogger.warning(
                            `Variable should not be used in variable mapping while not marked as snapshot variable. TVAR_ID=${item.TVAR_ID}`,
                        );
                    }
                    return {
                        tvar_id: item.TVAR_ID,
                        usage: item.TTASKVARUSG_USAGE,
                        axis_x: item.AXIS_X,
                        axis_y: item.AXIS_Y,
                        tsec_id: item.TSEC_ID,
                        tsec_x: item.TSEC_X,
                        tsec_y: item.TSEC_Y,
                    };
                });

                const repoMassUsage = globalThis.orm.repo(
                    "templateTaskMassUsage",
                );
                const massUsages = await repoMassUsage
                    .getForTTask(ttask.TPROC_ID, ttask.TTASK_ID, tprocVersion)
                    .collectAll();

                ttask.ttask_mass_mapping = massUsages.map((item) => ({
                    tvar_id: item.TVAR_ID,
                    usage: item.TSKMSS_USAGE,
                    tskmss_order: item.TSKMSS_ORDER,
                }));

                const repoCompletion = globalThis.orm.repo(
                    "templateTaskCompletion",
                );
                const completions = await repoCompletion.getForTTask(
                    ttask.TTASK_ID,
                    tprocVersion,
                );
                ttask.ttask_completions = completions.map((co) => ({
                    ttc_id: co.TTC_ID,
                    ttask_id: co.TTASK_ID,
                    tvar_id: co.TVAR_ID,
                    ttc_operator: co.TTC_OPERATOR,
                    ttc_value: co.TTC_VALUE,
                    ttc_concat_operator: co.TTC_CONCAT_OP,
                    ttc_cancel_flag: co.TTC_CANCEL_FLAG,
                    tvar_type: co.TVAR_TYPE,
                    tvar_multi: co.TVAR_MULTI,
                    tvar_name: co.TVAR_NAME,
                }));
                ttask.ttask_completion_co = ttask.ttask_completions.length
                    ? ttask.ttask_completions[0].ttc_concat_operator
                    : null;

                if (ttask.TTASK_ASSESMENT_USER_ID != null) {
                    ttask.TTASK_GARANT = "SU";
                } else if (ttask.TTASK_ASSESMENT_ORGSTR_ID != null) {
                    ttask.TTASK_GARANT = "OS";
                } else {
                    ttask.TTASK_GARANT = "PO";
                }

                if (
                    ttask.TTASK_SUBPROCESS_TPROC_ID != -1 &&
                    ttask.TTASK_SUBPROCESS_TPROC_ID != null
                ) {
                    const eveRepo = globalThis.orm.repo("event");
                    ttask.MAPPING = await eveRepo.getSubprocessMappings(
                        ttask.TTASK_ID,
                    );
                    await eveRepo.getAllTprocMapping(
                        ttask.TPROC_ID,
                        ttask.TTASK_SUBPROCESS_TPROC_ID,
                    );
                }

                if (ttask.TTASK_EVENT) {
                    const eveRepo = globalThis.orm.repo("event");
                    await eveRepo.getMapping(
                        "mappingSource",
                        "SUBP",
                        ttask.TPROC_ID,
                    );
                    await eveRepo.getMapping(
                        "mappingTarget",
                        "SUBR",
                        ttask.TPROC_ID,
                    );
                    ttask.mapping = await eveRepo.getEventWaitMappings(
                        ttask.TTASK_ID,
                    );
                }
            }

            const response = this.restifyData(items, {
                primaryCol,
                single: !!id,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
            });

            return this.handleResponse(request, reply, response);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
