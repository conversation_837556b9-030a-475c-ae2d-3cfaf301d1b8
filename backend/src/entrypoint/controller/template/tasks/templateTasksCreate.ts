import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { AccessRights } from "../../../../api/utils/AccessRights";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../../../api/services/UtilsService";
import * as eventConsts from "../../../../api/orm/entity/const/eventConst";
import * as taskConsts from "../../../../api/orm/entity/const/taskConst";
import { AuthorizedUser } from "../../../../service/authorization/AuthorizedUser";

@Service()
export class TemplateTasksCreateController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-tasks";

    public readonly schema = {
        body: {
            type: "array" as const,
            items: {
                type: "object" as const,
                properties: {
                    tproc_version: { type: "number" as const },
                    ttask_id: { type: "number" as const },
                    tproc_id: { type: "number" as const },
                    ttask_name: { type: "string" as const, nullable: true },
                    ttask_description: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_type: { type: "string" as const, nullable: true },
                    ttask_garant: { type: "string" as const, nullable: true },
                    ttask_gen_history: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_is_delegatable: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_is_rejectable: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_can_be_button: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_is_bulk_completable: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_mass_mapping: {
                        type: "array" as const,
                        items: {
                            type: "object" as const,
                            properties: {
                                tvar_id: { type: "number" as const },
                                usage: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                            },
                            required: ["tvar_id", "usage"] as const,
                        },
                    },
                    ttask_sufficient_end: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_run_only_once: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_multiinstance_flag: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_iterate_over: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_assesment_user_id: {
                        type: "number" as const,
                        nullable: true,
                    },
                    ttask_assesment_user_name: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_assesment_orgstr_id: {
                        type: "number" as const,
                        nullable: true,
                    },
                    ttask_due_offset: {
                        type: "object" as const,
                        properties: {
                            type: { type: "string" as const, nullable: true },
                            value: { type: "string" as const, nullable: true },
                        },
                        required: ["type", "value"] as const,
                        nullable: true,
                    },
                    ttask_duration: {
                        type: "object" as const,
                        properties: {
                            type: { type: "string" as const, nullable: true },
                            value: { type: "string" as const, nullable: true },
                        },
                        required: ["type", "value"] as const,
                        nullable: true,
                    },
                    ttask_operations: {
                        type: "array" as const,
                        items: {
                            type: "object" as const,
                            properties: {
                                calcSendEs6: { type: "boolean" as const },
                                ttjscalc_js: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttjscalc_exec_start: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttjscalc_exec_end: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttjscalc_exec_hand: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttjscalc_exec_recalc: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttjscalc_exec_pull: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttjscalc_append_scripts: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                            },
                        },
                    },
                    ttask_js: { type: "string" as const, nullable: true },
                    ttask_instruction: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_assesment_role_name: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_assesment_orgstr_cnst: {} as const,
                    ttask_reference_user: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_assesment_hierarchy: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_assesment_method: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_again: { type: "string" as const, nullable: true },
                    ttask_duty: { type: "string" as const, nullable: true },
                    ttask_completions: {
                        type: "array" as const,
                        items: {
                            type: "object" as const,
                            properties: {
                                tvar_id: { type: "number" as const },
                                ttc_value: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttc_operator: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttc_concat_operator: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                tvar_type: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                ttc_cancel_flag: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                            },
                        },
                    },
                    ttask_subprocess_tproc_id: {
                        type: "number" as const,
                        nullable: true,
                    },
                    ttask_subprocess_tproc_version: {
                        type: "string" as const,
                        nullable: true,
                    },
                    subp_mapping: {
                        type: "array" as const,
                        items: {
                            type: "string" as const,
                        },
                    },

                    subr_mapping: {
                        type: "array" as const,
                        items: {
                            type: "string" as const,
                        },
                    },
                    ttask_var_mapping: {
                        type: "array" as const,
                        items: {
                            type: "object" as const,
                            properties: {
                                tvar_id: { type: "number" as const },
                                usage: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                axis_x: { type: "number" as const },
                                axis_y: { type: "number" as const },
                                tsec_id: { type: "number" as const },
                                tsec_x: { type: "number" as const },
                                tsec_y: { type: "number" as const },
                            },
                        },
                    },
                    ttask_invoke_event: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttask_event: { type: "string" as const, nullable: true },
                },
                patternProperties: {
                    "^ttask_name_": { type: "string" as const, nullable: true },
                    "^ttask_description_": {
                        type: "string" as const,
                        nullable: true,
                    },
                    "^ttask_instruction_": {
                        type: "string" as const,
                        nullable: true,
                    },
                },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "logRouteAccess",
                "isTemplater",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        let user: AuthorizedUser | undefined;
        try {
            user = await this.auth.getUserData(request);
            const body = request.body;

            this.tasLogger.info(`Updating template tasks definitions`, {
                actorUserId: user.USER_ID,
                body,
            });

            const ids: any = [];
            let tprocId: any;
            let tprocVersion: any;

            await globalThis.database.transaction(async (trx) => {
                for (const item of body) {
                    tprocId = item.tproc_id;
                    tprocVersion = item.tproc_version;
                    const ttaskId = item.ttask_id;

                    const ac = new AccessRights(trx);
                    const acc = await ac.getAccessRight(
                        "TEMPLATE_TASKS",
                        ttaskId,
                        user?.USER_ID,
                    );
                    if (acc.W !== "Y") {
                        throw new UserException(
                            "Lack of permissions.",
                            "LACK_OF_PERMISSIONS",
                        );
                    }

                    const data = item;
                    const repo = globalThis.orm.repo("templateTask", trx);
                    const entity = repo.getEntity();
                    const attrs = entity.attributes();
                    const attrKeys = Object.keys(attrs);

                    // Prepare ttask.
                    if (typeof data.subprocess !== "undefined") {
                        data.ttask_is_proc_id = data.subprocess;
                    }

                    if (
                        data.ttask_due_offset != null &&
                        typeof data.ttask_due_offset !== "undefined" &&
                        data.ttask_due_offset.type === "no"
                    ) {
                        data.ttask_due_offset = null as any;
                    } else if (
                        data.ttask_due_offset != null &&
                        typeof data.ttask_due_offset !== "undefined"
                    ) {
                        if (data.ttask_due_offset.type === "ps") {
                            data.ttask_due_offset.type = "P+";
                        } else if (data.ttask_due_offset.type === "ts") {
                            data.ttask_due_offset.type = "T+";
                        }
                        data.ttask_due_offset = (data.ttask_due_offset.type +
                            data.ttask_due_offset.value) as any;
                    }

                    if (
                        data.ttask_duration != null &&
                        typeof data.ttask_duration !== "undefined" &&
                        data.ttask_duration.type === "no"
                    ) {
                        data.ttask_duration = null as any;
                    } else if (
                        data.ttask_duration != null &&
                        typeof data.ttask_duration !== "undefined"
                    ) {
                        if (data.ttask_duration.type === "ps") {
                            data.ttask_duration.type = "P+";
                        } else if (data.ttask_duration.type === "ts") {
                            data.ttask_duration.type = "T+";
                        }

                        data.ttask_duration = (data.ttask_duration.type +
                            data.ttask_duration.value) as any;
                    }

                    if (
                        typeof data.ttask_invoke_event === "undefined" &&
                        !ttaskId
                    ) {
                        data.ttask_invoke_event =
                            eventConsts.INVOKE_EVENT_BACKGROUND;
                    }
                    if (typeof data.ttask_inv_attendees !== "undefined") {
                        data.ttask_inv_attendees = data.ttask_inv_attendees;
                    }

                    if (typeof data.ttasks !== "undefined") {
                        data.ttask_assesment_ttask_id = data.ttasks;
                    }

                    // Change petri net if evet wait
                    if (data.ttask_type === taskConsts.TYPE_EVENT_WAIT) {
                        data.ttask_petri_net_input = "O";
                    }

                    if (typeof data.ttask_petri_net_input !== "undefined") {
                        if (data.ttask_petri_net_input === "O") {
                            data.ttask_petri_net_input = 1;
                            data.ttask_disc_flag = "N";
                        } else if (data.ttask_petri_net_input === "A") {
                            data.ttask_petri_net_input = 0;
                            data.ttask_disc_flag = "N";
                        } else if (data.ttask_petri_net_input === "N") {
                            data.ttask_petri_net_input =
                                data.ttask_petri_net_inputn
                                    ? data.ttask_petri_net_inputn
                                    : 0;
                            data.ttask_disc_flag = "N";
                        } else if (data.ttask_petri_net_input === "D") {
                            data.ttask_petri_net_input = 1;
                            data.ttask_disc_flag = "Y";
                        }
                    }

                    if (data.ttask_type === taskConsts.TYPE_EVENT) {
                        data.ttask_subprocess_tproc_id = -1;
                        data.ttask_event_wait = 0;
                    } else if (data.ttask_type === taskConsts.TYPE_EVENT_WAIT) {
                        data.ttask_subprocess_tproc_id = -1;
                        data.ttask_event_wait = 1;

                        if (typeof data.subr_mapping !== "undefined") {
                            data.ttask_subr_map = this.prepareMappingParams(
                                -1,
                                data.subr_mapping,
                            );
                        }
                    } else if (data.ttask_type === taskConsts.TYPE_SUBPROCESS) {
                        if (typeof data.subp_mapping !== "undefined") {
                            data.ttask_subp_map = this.prepareMappingParams(
                                -1,
                                data.subp_mapping,
                            );
                        }
                        if (typeof data.subr_mapping !== "undefined") {
                            data.ttask_subr_map = this.prepareMappingParams(
                                -1,
                                data.subr_mapping,
                            );
                        }
                        data.ttask_event_wait = 1;
                    } else if (!ttaskId) {
                        data.ttask_subprocess_tproc_id = -1;
                        data.ttask_event_wait = 0;
                    }

                    // @t3b-1055 ttask_assesment_tvar_id  se nesmaže, pokud změním přiřazení úkolu
                    if (
                        data.ttask_type &&
                        [
                            taskConsts.TYPE_EVENT,
                            taskConsts.TYPE_EVENT_WAIT,
                            taskConsts.TYPE_SUBPROCESS,
                        ].indexOf(data.ttask_type) === -1
                    ) {
                        data.ttask_subprocess_tproc_id = -1;
                        data.ttask_event_wait = 0;
                        data.ttask_event = null as any;
                    }

                    if (data.ttask_js) {
                        try {
                            const compiled = await UtilsService.babelTransform(
                                data.ttask_js,
                            );
                            data.ttask_js_compiled = compiled;
                        } catch (err) {
                            throw new UserException(
                                `Babel transform error. ${err}`,
                                "BABEL_TRANSFORM_ERROR",
                                {
                                    err,
                                    ttask_id: item.ttask_id,
                                    tprocVersion: item.tproc_version,
                                    tprocId: item.tproc_id,
                                },
                            );
                        }
                    } else if (data.ttask_js === null || data.ttask_js === "") {
                        data.ttask_js_compiled = null;
                    }

                    // Fill ttask entity
                    attrKeys.forEach((key) => {
                        const lk = key.toLowerCase();

                        if (typeof data[lk] !== "undefined") {
                            entity[key] = data[lk];
                        }
                    });

                    this.tasLogger.info(
                        ttaskId
                            ? `Updating template task ${data.ttask_name} with id ${ttaskId} on template process ${tprocId}`
                            : `Creating new template task ${data.ttask_name} on template process ${tprocId}`,
                        {
                            actorUserId: user?.USER_ID,
                            tprocId,
                            ttaskId,
                        },
                    );

                    const id = await repo.doCreateTTask(
                        entity,
                        data,
                        item.tproc_version,
                    );
                    ids.push(id);
                }

                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                if (tprocId && tprocVersion) {
                    await tprocRepo.setTemplateChange(
                        tprocId,
                        tprocVersion,
                        user?.USER_ID,
                    );
                }
            });

            this.tasLogger.info(
                "Updated template tasks definitions successfully",
                {
                    actorUserId: user.USER_ID,
                    ttaskIds: ids,
                },
            );

            return this.handleResponse(request, reply, {
                result: true,
                id: ids.length == 1 ? ids[0] : ids,
            });
        } catch (err: any) {
            this.tasLogger.error(
                "Updating template tasks definitions was not successfully",
                {
                    userId: user?.USER_ID,
                    err,
                },
            );
            return this.handleErrorResponse(request, reply, err);
        }
    }

    private prepareMappingParams(rdefId: any, params: any) {
        if (!params) {
            return [];
        }
        if (!Array.isArray(params)) {
            params = [params];
        }

        const isNumeric = function (n: any) {
            return !isNaN(parseFloat(n)) && isFinite(n);
        };

        const paramsArr: any = [];
        params.forEach((param: any) => {
            let buf;
            buf = param.split("=");
            const targetStr = buf[0];
            const source = buf[1];

            buf = targetStr.split("#");
            const targetType = buf[0];
            const target = buf[1];

            const tvarId = isNumeric(source) ? source : null;
            const tvarIdDest = isNumeric(target) ? target : null;
            const rdefparName = isNumeric(target) ? null : target;
            let eveparName = isNumeric(source) ? null : source;
            let rdefparValue = null;

            if (source.substr(0, 1) == "#") {
                if (source.substr(1, 1) == "!") {
                    rdefparValue = source.substr(2);
                    eveparName = "$CALC";
                } else {
                    rdefparValue = source.substr(1);
                    eveparName = "$CONSTANT";
                }
            }

            paramsArr.push({
                rdef_id: rdefId,
                tvar_id: tvarId,
                tvar_id_dest: tvarIdDest,
                rdefpar_name: rdefparName,
                evepar_name: eveparName,
                rdefpar_value: rdefparValue,
                rdefpar_type: targetType,
            });
        });

        return paramsArr;
    }
}
