import { Service } from "typedi";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { FastifyReply, FastifyRequest } from "fastify";

@Service()
export class TemplateTasksRemoveController extends BaseController {
    public readonly method = "DELETE";

    public readonly url = "/template-task/:tprocId/:tprocVersion/:id?";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: { type: "number" as const },
                tprocId: { type: "number" as const },
                tprocVersion: { type: "number" as const },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        body: {
            type: "array" as const,
            items: { type: "number" as const },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "logRouteAccess",
                "isTemplater",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        const user = await this.auth.getUserData(request);
        // Determine IDs to delete:
        // If single ID is in URL => wrap it in array
        // Else expect array of IDs from request body
        const taskIds: number[] = request.params.id
            ? [request.params.id]
            : request.body;
        const tprocId = request.params.tprocId;
        const tprocVersion = request.params.tprocVersion;

        try {
            const taskRepo = globalThis.orm.repo("templateTask");

            for (const id of taskIds) {
                const ttask = await taskRepo.get(id);
                this.tasLogger.info(
                    `Deleting template task '${ttask.TTASK_NAME}' with id ${id} on template process ${tprocId}`,
                    {
                        actorUserId: user.USER_ID,
                        ttaskId: id,
                        tprocId,
                    },
                );
            }

            await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templateTask", trx);
                await repo.deleteTTask(taskIds);

                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                await tprocRepo.setTemplateChange(
                    tprocId,
                    tprocVersion,
                    user.USER_ID,
                );
            });

            this.tasLogger.info(
                `Deleted template task(s) [${taskIds.join(", ")}] on template process ${tprocId} successfully`,
                {
                    actorUserId: user.USER_ID,
                    ttaskIds: taskIds,
                    tprocId,
                },
            );

            return this.handleResponse(request, reply, { result: true });
        } catch (err: any) {
            this.tasLogger.error(
                `Deleting template task(s) [${taskIds.join(", ")}] on template process ${tprocId} was not successful`,
                {
                    userId: user?.USER_ID,
                    ttaskIds: taskIds,
                    tprocId,
                    err,
                },
            );
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
