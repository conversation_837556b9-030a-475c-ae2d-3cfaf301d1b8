import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";

@Service()
export class TemplateProcessesTasksGetController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-tasks";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const { tprocId } = request.params;

            const repo = globalThis.orm.repo("templateTask");
            repo.setLocalization(user.LANGUAGE);

            const coll = repo.getAll();
            coll.knex.where("TPROC_ID", tprocId);

            const primaryCol = repo.entity.primaryColumn;

            coll.filteringColumns = {
                TTASK_ASSESMENT_ROLE_NAME: {
                    type: "string",
                    key: "ASSR.ROLE_NAME",
                },
                TTASK_ASSESMENT_ORGSTR_NAME: {
                    type: "string",
                    key: "ORG.ORGSTR_NAME",
                },
                TTASK_CALC_COUNT: {
                    type: "string",
                    key: "(select count(*) from TEMPLATE_TASK_JS_CALCULATIONS where TTASK_ID = TT.TTASK_ID)",
                },
                TTASK_ID: { type: "string", key: "TT.TTASK_ID" },
            };
            coll.orderingColumns = _.cloneDeep(coll.filteringColumns);

            const items = await this.handleGetRequest(request, coll);

            if (!request.params.id) {
                items[0] = _.map(items[0], (o) =>
                    _.omit(o, "TTASK_INSTRUCTION"),
                ) as (typeof items)[0];
            }

            const castedItems = await repo.castRows(
                items[0],
                _.assign(
                    { TTASK_INSTRUCTION: { type: "text" } },
                    repo.entity.getAttributes(false, true),
                ),
            );

            const responseData = this.restifyData(castedItems, {
                primaryCol,
                single: !!request.params.id,
                totalCount: items[1],
                startTime: items[2],
                routePath: request.routeOptions.url,
                reqUrl: request.url,
                locale: user.LANGUAGE,
            });

            return this.handleResponse(request, reply, responseData);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
