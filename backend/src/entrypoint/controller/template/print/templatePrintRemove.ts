import { Service } from "typedi";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { FastifyReply, FastifyRequest } from "fastify";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import { AccessRights } from "../../../../api/utils/AccessRights";

@Service()
export class TemplatePrintRemoveController extends BaseController {
    public readonly method = "DELETE";

    public readonly url = "/template-prints/prints/:id";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: {
                    type: "number" as const,
                },
            },
            required: ["id"] as const,
        },
        body: {
            type: "object" as const,
            properties: {
                tproc_id: { type: "string" as const },
                tproc_version: { type: "string" as const },
            },
            required: ["tproc_id", "tproc_version"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isTemplater",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const ac = new AccessRights(globalThis.database);

            const acc = await ac.getAccessRight(
                "TEMPLATE_TASKS",
                undefined,
                user.USER_ID,
            );
            if (acc.D !== "Y") {
                throw new UserException(
                    "Lack of permissions.",
                    "LACK_OF_PERMISSIONS",
                );
            }

            await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templatePrint");
                const entity = repo.getEntity();
                entity.PRNT_ID = request.params.id;
                await repo.delete(entity);

                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                const { tproc_id, tproc_version } = request.body;

                this.tasLogger.info("Template print deleted", {
                    prntId: request.params.id,
                    tprocId: tproc_id,
                    tprocVersion: tproc_version,
                    actorUserId: user.USER_ID,
                });

                await tprocRepo.setTemplateChange(
                    tproc_id,
                    tproc_version,
                    user.USER_ID,
                );
            });

            return this.handleResponse(request, reply, { return: true });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
