import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";

@Service()
export class TemplatePrintGetController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/prints/:id?";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: {
                    type: "number" as const,
                },
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const repo = globalThis.orm.repo("templatePrint");

            const collection = repo.getTemplateProcessPrints(
                request.params.tprocId,
            );

            collection.filteringColumns = {
                PRNT_STATUS: { type: "string", key: "PRNT_STATUS" },
                PRNT_ORDER: { type: "integer", key: "PRNT_ORDER" },
            };
            collection.orderingColumns = _.cloneDeep(
                collection.filteringColumns,
            );

            if (!user.isPowerUser()) {
                collection.knex.where("PRNT_STATUS", "A");
            }

            const primaryCol = repo.entity.primaryColumn;

            const result = await this.handleGetRequest(request, collection);
            const [items, totalCount, startTime] = await Promise.all(result);

            const processedItems = await repo.castRows(
                items,
                repo.entity.getAttributes(false, true),
            );

            const responseData = this.restifyData(processedItems, {
                primaryCol,
                single: !!request.params.id,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
            });

            return this.handleResponse(request, reply, responseData);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
