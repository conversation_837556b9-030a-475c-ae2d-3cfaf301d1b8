import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import { UtilsService } from "../../../../api/services/UtilsService";
import _ from "lodash";
import { AccessRights } from "../../../../api/utils/AccessRights";

@Service()
export class TemplatePrintCreateController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-processes/prints";

    public readonly schema = {
        body: {
            type: "object" as const,
            properties: {
                prnt_name: { type: "string" as const },
                tproc_id: { type: "string" as const },
                tproc_version: { type: "string" as const, nullable: true },
                prnt_js: { type: "string" as const, nullable: true },
                prnt_status: { type: "string" as const, nullable: true },
                prnt_apply_styles: { type: "string" as const, nullable: true },
                prnt_css: { type: "string" as const, nullable: true },
                prnt_react: { type: "string" as const, nullable: true },
                prnt_ready_in_js: { type: "string" as const, nullable: true },
                prnt_timeout: { type: "number" as const, nullable: true },
            },
            required: ["prnt_name", "tproc_id"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isTemplater",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const tprocId = request.body.tproc_id;
            const tprocVersion = request.body.tproc_version || "1";

            const ac = new AccessRights(globalThis.database);
            const acc = await ac.getAccessRight(
                "TEMPLATE_TASKS",
                undefined,
                user.USER_ID,
            );
            if (acc.W !== "Y") {
                throw new UserException(
                    "Lack of permissions.",
                    "LACK_OF_PERMISSIONS",
                );
            }

            const id = await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templatePrint", trx);
                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                const entity = repo.getEntity();
                const data = request.body;

                Object.keys(entity.attributes()).forEach((key) => {
                    const lowerKey = key.toLowerCase();
                    if (data[lowerKey] !== undefined) {
                        entity[key] = data[lowerKey];
                    }
                });

                try {
                    entity.PRNT_JS_COMPILED = await UtilsService.babelTransform(
                        data.prnt_js,
                    );
                } catch (err: any) {
                    throw new UserException(
                        `Babel transform error. ${err} Position: ${JSON.stringify(err.loc)}`,
                        "BABEL_TRANSFORM_ERROR",
                        {
                            err,
                            prnt_id: _.parseInt(entity.PRNT_ID),
                            tproc_id: _.parseInt(entity.TPROC_ID),
                            tproc_version: _.parseInt(tprocVersion),
                        },
                    );
                }

                const newId = await repo.store(entity);
                await tprocRepo.setTemplateChange(
                    tprocId,
                    tprocVersion,
                    user.USER_ID,
                );
                return newId;
            });

            return this.handleResponse(request, reply, { result: true, id });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
