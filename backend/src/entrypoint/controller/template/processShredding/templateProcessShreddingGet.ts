import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";

@Service()
export class TemplateProcessShreddingGetController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/template-process-shredding/list";

    public readonly schema = {
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const repo = globalThis.orm.repo("templateProcessShredding");
            const collection = repo.get();

            const result = await this.handleGetRequest(request, collection);
            const [items, totalCount, startTime] = await Promise.all(result);

            const response = this.restifyData(items, {
                primaryCol: null,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
            });

            return this.handleResponse(request, reply, response);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
