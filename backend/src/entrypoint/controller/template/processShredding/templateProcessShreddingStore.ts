import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import { AccessRights } from "../../../../api/utils/AccessRights";

@Service()
export class TemplateProcessShreddingStoreController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-process-shredding/store";

    public readonly schema = {
        body: {
            type: "object" as const,
            properties: {
                tproc_id: { type: "number" as const },
            },
            required: ["tproc_id"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            await globalThis.database.transaction(async (trx) => {
                const ac = new AccessRights(trx);
                const acc = await ac.getAccessRight(
                    "TEMPLATE_PROCESSES",
                    request.body.tproc_id,
                    user.USER_ID,
                );

                if (acc.W !== "Y") {
                    throw new UserException(
                        "Lack of permissions.",
                        "LACK_OF_PERMISSIONS",
                    );
                }

                const repo = globalThis.orm.repo(
                    "templateProcessShredding",
                    trx,
                );
                await repo.save(this.keysToLowerCase(request.body));

                return this.handleResponse(request, reply, { result: true });
            });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
