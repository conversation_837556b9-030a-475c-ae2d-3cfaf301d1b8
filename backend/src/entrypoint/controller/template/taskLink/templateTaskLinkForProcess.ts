import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";

@Service()
export class TemplateTaskLinkForProcessController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/templates/template/:tprocId/:tprocVersion/template-link/:id?";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
                id: {
                    type: "number" as const,
                },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const { tprocId, tprocVersion } = request.params;

            const repo = globalThis.orm.repo("templateTaskLink");
            const collection = repo.forTemplateProcess(tprocId, tprocVersion);
            const primaryCol = repo.entity.primaryColumn;

            collection.filteringColumns = {
                TTASKLINK_FROM_TTASK_NAME: {
                    type: "string",
                    key: "TTFROM.TTASK_NAME",
                },
                TTASKLINK_TO_TTASK_NAME: {
                    type: "string",
                    key: "TTTO.TTASK_NAME",
                },
            };
            collection.orderingColumns = _.cloneDeep(
                collection.filteringColumns,
            );

            const [items, totalCount, startTime] = await this.handleGetRequest(
                request,
                collection,
            );

            if (Array.isArray(items) && items.length > 0) {
                items.forEach((item) => {
                    item.TTASKLINK_ELSE =
                        item.TTASKLINK_TYPE === "ELSE"
                            ? "ELSE"
                            : item.COND_COUNT > 0
                              ? "COND"
                              : "NCOND";
                });
            }

            const links = await repo.fillConditions(items);
            links.forEach((link) => {
                link.conditions.forEach((cond: any) => {
                    this.restifyEntity(cond, "TCOND_ID", null);
                });
            });

            const result = this.restifyData(links, {
                primaryCol,
                single: !!request.params.id,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
            });

            return this.handleResponse(request, reply, result);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
