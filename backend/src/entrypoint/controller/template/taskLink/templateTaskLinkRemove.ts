import { Service } from "typedi";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { FastifyReply, FastifyRequest } from "fastify";
import { AccessRights } from "../../../../api/utils/AccessRights";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";

@Service()
export class TemplateTaskLinkRemoveController extends BaseController {
    public readonly method = "DELETE";

    public readonly url = "/template-link/:tprocId/:tprocVersion/:id?";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: { type: "number" as const },
                tprocId: { type: "number" as const },
                tprocVersion: { type: "number" as const },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        body: {
            type: "array" as const,
            items: { type: "number" as const },
            nullable: true,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            // Determine IDs to delete:
            // If single ID is in URL => wrap it in array
            // Else expect array of IDs from request body
            const ids: number[] = request.params.id
                ? [request.params.id]
                : request.body;
            const ac = new AccessRights(globalThis.database);

            for (const id of ids) {
                const acc = await ac.getAccessRight(
                    "TEMPLATE_TASK_LINKS",
                    id,
                    user.USER_ID,
                );
                if (acc.W !== "Y") {
                    throw new UserException(
                        "Lack of permissions.",
                        "LACK_OF_PERMISSIONS",
                    );
                }
            }
            await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templateTaskLink", trx);
                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                await repo.delete(ids);

                await tprocRepo.setTemplateChange(
                    request.params.tprocId,
                    request.params.tprocVersion,
                    user.USER_ID,
                );
            });

            return this.handleResponse(request, reply, { result: true });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
