import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");

@Service()
export class TemplateTaskLinkCreateController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-link";

    public readonly schema = {
        body: {
            type: "array" as const,
            items: {
                type: "object" as const,
                properties: {
                    tproc_id: { type: "string" as const, nullable: true },
                    tproc_version: { type: "string" as const, nullable: true },
                    ttasklink_id: { type: "number" as const },
                    ttasklink_is_mandatory: {
                        type: "string" as const,
                        nullable: true,
                    },
                    ttasklink_else: { type: "string" as const, nullable: true },
                    ttasklink_from_ttask_id: { type: "number" as const },
                    ttasklink_priority: {
                        type: "number" as const,
                        nullable: true,
                    },
                    ttasklink_to_ttask_id: { type: "number" as const },
                    ttasklink_type: { type: "string" as const, nullable: true },
                    conditions: {
                        type: "array" as const,
                        items: {
                            type: "object" as const,
                            properties: {
                                tcond_variable: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                tcond_value: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                tcond_operator: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                type: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                            },
                        },
                    },
                },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        const data = Array.isArray(request.body)
            ? request.body
            : [request.body];
        let tprocId: any;
        let tprocVersion: any;

        try {
            const ids = await globalThis.database.transaction(async (trx) => {
                const taskLinkRepo = globalThis.orm.repo(
                    "templateTaskLink",
                    trx,
                );
                const createdIds = [];

                for (const item of data) {
                    tprocId = item.tproc_id;
                    tprocVersion = item.tproc_version;
                    const tlink = taskLinkRepo.getEntity();
                    const attrs = tlink.attributes();
                    const attrKeys = Object.keys(attrs);

                    attrKeys.forEach((key) => {
                        const lk = key.toLowerCase();
                        if (typeof item[lk] !== "undefined") {
                            tlink[key] = item[lk];
                        }
                    });

                    if (tlink.TTASKLINK_TYPE === null) {
                        tlink.TTASKLINK_TYPE = "AND";
                    }
                    tlink.TTASKLINK_VERSION = item.tproc_version;

                    const id = await taskLinkRepo.create(
                        tlink,
                        item.sourcePort,
                        item.targetPort,
                        item.tproc_version,
                        item.conditions,
                    );
                    createdIds.push(id);
                }
                const userId = (await this.auth.getUserData(request)).USER_ID;
                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                if (tprocId && tprocVersion) {
                    await tprocRepo.setTemplateChange(
                        tprocId,
                        tprocVersion,
                        userId,
                    );
                }

                return createdIds;
            });
            return this.handleResponse(request, reply, {
                result: true,
                id: ids.length === 1 ? ids[0] : ids,
            });
        } catch (err: any) {
            this.handleErrorResponse(request, reply, err);
        }
    }
}
