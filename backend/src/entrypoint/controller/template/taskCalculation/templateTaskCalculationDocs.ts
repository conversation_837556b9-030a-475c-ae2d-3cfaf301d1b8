import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import { GraphQL } from "../../../../api/utils/GraphQL";
import _ from "lodash";
import fs from "fs";
import path from "path";

@Service()
export class TemplateTaskCalculationDocsController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/js-scripts/task/docs";

    public readonly schema = {};

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const wikiData = await GraphQL.getCalculationExamples();

            const calculationsExamples = _.chain(wikiData)
                .map(({ path, render }) => {
                    const [, apiNameSpace, calculationName] = path.split("/");
                    return { apiNameSpace, calculationName, example: render };
                })
                .groupBy("apiNameSpace")
                .mapValues((items) =>
                    items.reduce(
                        (result, { calculationName, example }) => {
                            result[calculationName] = { example };
                            return result;
                        },
                        {} as Record<string, any>,
                    ),
                )
                .value();

            const walk = (dir: string): string[] => {
                let results: string[] = [];
                fs.readdirSync(dir).forEach((file) => {
                    const fullPath = path.join(dir, file);
                    if (fs.statSync(fullPath).isDirectory()) {
                        results = results.concat(walk(fullPath));
                    } else {
                        results.push(fullPath);
                    }
                });
                return results;
            };

            const documentationDir = path.resolve(
                __dirname,
                "../../../../../dist/documentation/calculation",
            );

            const files = walk(documentationDir);

            const disabledApi = new Set([
                "Api.json",
                "SqlApi.json",
                "NumberApi.json",
                "TasApi.json",
                "VoidApi.json",
                "AutoTestApi.json",
            ]);

            let jsons: Record<string, any> = {};
            files.forEach((filePath) => {
                const fileName = path.basename(filePath);
                if (!disabledApi.has(fileName)) {
                    const docName = fileName.replace(".json", "");
                    jsons[docName] = require(filePath);
                }
            });

            jsons = _.merge(jsons, calculationsExamples);

            return this.handleResponse(request, reply, jsons);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
