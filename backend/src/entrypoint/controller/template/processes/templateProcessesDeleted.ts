import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";

@Service()
export class TemplateProcessesDeletedController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/template-processes/deleted";

    public readonly schema = {
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);

            const repo = globalThis.orm.repo("templateProcess");
            repo.setLocalization(user.LANGUAGE);

            const collection = globalThis.orm.collection(
                repo,
                repo.getTemplates(user.USER_ID),
            );
            collection.knex.where("TPROC_STATUS", "E");

            collection.filteringColumns = {
                TPROC_OWNER_USER: {
                    type: "string",
                    key: `U1.USER_DISPLAY_NAME`,
                },
                TPROC_LAST_CHANGED_BY_USER: {
                    type: "string",
                    key: `U2.USER_DISPLAY_NAME`,
                },
                TPROC_VIS_ROLE_NAME: { type: "string", key: "TR.ROLE_NAME" },
                HEADER_NAME: { type: "string", key: "TH.HEADER_NAME" },
            };

            if (Array.isArray(globalThis.dynamicConfig.langs)) {
                globalThis.dynamicConfig.langs.forEach((lang) => {
                    const translated = `HEADER_NAME_${lang.toUpperCase()}`;
                    collection.filteringColumns[translated] = {
                        type: "string",
                        key: `TH.${translated}`,
                    };
                });
            }

            collection.orderingColumns = _.cloneDeep(
                collection.filteringColumns,
            );

            const itemsResult = await this.handleGetRequest(
                request,
                collection,
            );

            itemsResult[0] = itemsResult[0].map((o: any) =>
                _.omit(o, "TPROC_TVAR_ORDER"),
            ) as any[];

            const casted = await repo.castRows(
                itemsResult[0],
                repo.entity.getAttributes(false, true),
            );

            const [items, totalCount, startTime] = [
                casted,
                itemsResult[1],
                itemsResult[2],
            ];

            const result = this.restifyData(items, {
                primaryCol: repo.entity.primaryColumn,
                single: false,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
                locale: user.LANGUAGE,
            });

            return this.handleResponse(request, reply, result);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
