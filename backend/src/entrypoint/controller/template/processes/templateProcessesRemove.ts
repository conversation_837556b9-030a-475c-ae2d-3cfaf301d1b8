import { Service } from "typedi";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { FastifyReply, FastifyRequest } from "fastify";

@Service()
export class TemplateProcessesRemoveController extends BaseController {
    public readonly method = "DELETE";

    public readonly url = "/template-processes/:id/:tprocVersion";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["id", "tprocVersion"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isTemplater",
                "logRouteAccess",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        let user;
        let tprocId;
        let tproc;
        try {
            user = await this.auth.getUserData(request);
            tprocId = request.params.id;
            const tprocVersion = request.params.tprocVersion;

            const tprocRepo = globalThis.orm.repo("templateProcess");
            tproc = await tprocRepo.get(tprocId);

            if (!tproc) {
                throw new Error(
                    `Template process with ID ${tprocId} not found.`,
                );
            }

            this.tasLogger.info(
                `Deleting template process '${tproc.TPROC_NAME}' with ID ${tprocId}`,
                {
                    actorUserId: user.USER_ID,
                    tprocId,
                },
            );

            await tprocRepo.delete(tprocId, tprocVersion);

            this.tasLogger.info(
                `Deleted template process '${tproc.TPROC_NAME}' with ID ${tprocId} successfully`,
                {
                    actorUserId: user.USER_ID,
                    tprocId,
                },
            );

            this.handleResponse(request, reply, { result: true });
        } catch (err: any) {
            this.tasLogger.error(
                `Deleting template process '${tproc?.TPROC_NAME}' with id ${tprocId} was not successful`,
                {
                    userId: user?.USER_ID,
                    tprocId: request.params.id,
                    err,
                },
            );

            return this.handleErrorResponse(request, reply, err);
        }
    }
}
