import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import ejs from "ejs";
import moment from "moment-timezone";
import path from "path";

@Service()
export class TemplateProcessesHtmlController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/template-processes/:id/:tprocVersion/html";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["id", "tprocVersion"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const lang = user.LANGUAGE;
            const dateFormat = user.DATE_FORMAT;
            const tprocId = request.params.id;
            const tprocVersion = request.params.tprocVersion;

            if (!tprocId) {
                throw new UserException("Missing TPROC_ID.");
            }

            const procRepo = globalThis.orm.repo("templateProcess");
            const procColl = globalThis.orm.collection(
                procRepo,
                procRepo.getTemplates(user.USER_ID),
            );
            procColl.knex.where("TPROC_ID", tprocId);

            const taskRepo = globalThis.orm.repo("templateTask");
            const taskColl = taskRepo.getAll();

            const varRepo = globalThis.orm.repo("templateVariable");
            const varColl = varRepo.getForTemplateProcess(tprocId);

            const varUsageRepo = globalThis.orm.repo("templateTaskVarUsage");
            const varUsageColl = varUsageRepo.getForTProcess(
                tprocId,
                tprocVersion,
            );

            const taskLink = globalThis.orm.repo("templateTaskLink");
            const taskLinkColl = taskLink.forTemplateProcess(
                tprocId,
                tprocVersion,
            );

            const condRepo = globalThis.orm.repo("templateLinkCondition");
            const calcRepo = globalThis.orm.repo("templateTaskJSCalculation");
            const varLovRepo = globalThis.orm.repo("templateVariableLov");
            const userRepo = globalThis.orm.repo("user");
            const orgstrRepo = globalThis.orm.repo("organizationStructure");
            const eveRepo = globalThis.orm.repo("event");

            const headerRepo = globalThis.orm.repo("header");
            const headerColl = headerRepo.getHeadersByTProcId(tprocId);
            const headerOrgRepo = globalThis.orm.repo("headerOrgstr");
            const headerRoleRepo = globalThis.orm.repo("headerRole");

            const [template, tasks, variables, varUsage, taskLinks, headers] =
                await Promise.all([
                    procColl.collectOne(),
                    taskColl.knex.where("TPROC_ID", tprocId),
                    varColl.collectAll(),
                    varUsageColl.collectAll(),
                    taskLinkColl.collectAll(),
                    headerColl.collectAll(),
                ]);

            const tasksIds = [];
            const orgIds = [template.TPROC_VIS_ORGSTR_ID];
            const usersIds = [];
            const subpIds = [];

            for (const task of tasks) {
                tasksIds.push(task.TTASK_ID);
                orgIds.push(task.TTASK_ASSESMENT_ORGSTR_ID);
                usersIds.push(task.TTASK_ASSESMENT_USER_ID);
                if (
                    task.TTASK_SUBPROCESS_TPROC_ID &&
                    task.TTASK_SUBPROCESS_TPROC_ID !== -1
                ) {
                    subpIds.push(task.TTASK_SUBPROCESS_TPROC_ID);
                }
            }

            const linksIds = taskLinks.map((link) => link.TTASKLINK_ID);
            variables.sort((a, b) => a.TVAR_NAME.localeCompare(b.TVAR_NAME));

            const ids = variables
                .filter((item) =>
                    ["DL", "LT", "LD", "LN"].includes(item.TVAR_TYPE),
                )
                .map((item) => item.TVAR_ID);

            for (const header of headers) {
                const orgs = await headerOrgRepo
                    .getHeaderOrganizations(header.HEADER_ID)
                    .fetchAll();
                header.HEADER_ORGSTRS = orgs;

                const roles = await headerRoleRepo
                    .getHeaderRoles(header.HEADER_ID)
                    .fetchAll();
                header.HEADER_ROLES = roles;
            }

            const [calculations, conditions, lovs, users, orgUnits, processes] =
                await Promise.all([
                    calcRepo
                        .getForTTasks(tasksIds, template.TPROC_VERSION)
                        .collectAll(),
                    condRepo.getForLinks(linksIds),
                    varLovRepo
                        .getForTemplateVariables("*", ids)
                        .collectAssoc("TVAR_ID"),
                    userRepo.getById(usersIds).collectAll(),
                    orgstrRepo.getById(orgIds).collectAll(),
                    procRepo.getById(subpIds).collectAll(),
                ]);

            for (const task of tasks) {
                if (
                    task.TTASK_SUBPROCESS_TPROC_ID &&
                    task.TTASK_SUBPROCESS_TPROC_ID !== -1
                ) {
                    task.MAPPING = await eveRepo.getSubprocessMappings(
                        task.TTASK_ID,
                    );
                }
            }

            const renderedHtml = await ejs.renderFile(
                path.resolve(
                    __dirname,
                    "../../../../../src/api/prints/templates",
                    lang,
                    "documentProcess.ejs",
                ),
                {
                    template,
                    tasks,
                    variables,
                    varUsage,
                    calculations,
                    taskLinks,
                    conditions,
                    lovs,
                    users,
                    orgUnits,
                    processes,
                    headers,
                    moment,
                    dateFormat,
                    _,
                },
                { rmWhitespace: true },
            );
            return this.handleResponse(request, reply, {
                result: renderedHtml,
            });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
