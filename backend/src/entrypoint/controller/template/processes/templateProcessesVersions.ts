import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";

@Service()
export class TemplateProcessesVersionsController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/template-processes/:id/:tprocVersion/versions";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["id", "tprocVersion"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const tprocId = request.params.id;
            const versions = await globalThis.orm
                .repo("templateProcess")
                .enumVersions(tprocId);

            return this.handleResponse(request, reply, versions);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
