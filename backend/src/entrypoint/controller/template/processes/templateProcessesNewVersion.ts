import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";

@Service()
export class TemplateProcessesNewVersionController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-processes/:id/:tprocVersion/new-version";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["id", "tprocVersion"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const tprocId = request.params.id;
            const { tprocVersion } = request.params;

            const tproc = (await globalThis.database.transaction(async (trx) =>
                globalThis.orm
                    .repo("templateProcess", trx)
                    .createNewVersion(tprocId, tprocVersion),
            )) as any;

            return this.handleResponse(request, reply, {
                result: true,
                version: tproc.TPROC_VERSION,
            });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
