import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import { AccessRights } from "../../../../api/utils/AccessRights";

@Service()
export class TemplateProcessesCreateController extends BaseController {
    public readonly method = "POST";

    public readonly url = "/template-processes";

    public readonly schema = {
        body: {
            type: "object" as const,
            properties: {
                tproc_name: { type: "string" as const },
                tproc_id: { type: "number" as const },
                tproc_description: { type: "string" as const, nullable: true },
                tproc_default_case_name: { type: "string" as const },
                tproc_dms_visibility: {
                    type: "string" as const,
                    nullable: true,
                },
                tproc_hr_role_id: { type: "number" as const, nullable: true },
                tproc_note: { type: "string" as const, nullable: true },
                tproc_status: { type: "string" as const, nullable: true },
                tproc_vis_orgstr_id: {
                    type: "number" as const,
                },
                tproc_vis_role_id: { type: "number" as const },
                tpa_archive_on: { type: "string" as const, nullable: true },
                tpa_archive_delay: { type: "number" as const },
                tps_shred_on: { type: "string" as const, nullable: true },
                tps_shred_delay: { type: "number" as const },
            },
            patternProperties: {
                "^tproc_name_": { type: "string" as const, nullable: true },
                "^tproc_description_": {
                    type: "string" as const,
                    nullable: true,
                },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isTemplater",
                "logRouteAccess",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        let user: any, tprocId: any, tprocName;
        try {
            user = await this.auth.getUserData(request);
            const { tproc_name, ...data } = request.body;
            tprocName = tproc_name;

            this.tasLogger.info(
                tprocId
                    ? `Updating template process '${tprocName}' with id ${tprocId}`
                    : `Creating new template process '${tprocName}'`,
                {
                    actorUserId: user.USER_ID,
                    data,
                    tprocId,
                },
            );

            const resultId = await globalThis.database.transaction(
                async (trx) => {
                    const ac = new AccessRights(trx);
                    const acc = await ac.getAccessRight(
                        "TEMPLATE_PROCESSES",
                        tprocId,
                        user.USER_ID,
                    );
                    if (acc.W !== "Y") {
                        throw new UserException(
                            "Lack of permissions.",
                            "LACK_OF_PERMISSIONS",
                        );
                    }

                    const data = request.body;
                    const repo = globalThis.orm.repo("templateProcess", trx);
                    const entity = repo.getEntity();
                    const attrs = entity.attributes();
                    const attrKeys = Object.keys(attrs);

                    attrKeys.forEach((key) => {
                        const lk = key.toLowerCase();

                        if (
                            typeof data[lk] !== "undefined" &&
                            lk !== "tproc_owner_user_id"
                        ) {
                            entity[key] = data[lk];
                        }
                    });

                    if (
                        typeof entity.TPROC_ID === "undefined" ||
                        entity.TPROC_ID == null
                    ) {
                        entity.TPROC_OWNER_USER_ID = user.USER_ID;
                    }
                    entity.TPROC_LAST_CHANGED_BY_USER_ID = user.USER_ID;
                    entity.TPROC_LAST_CHANGED_DATE = new Date();
                    entity.ORG_ID = 1;

                    if (!entity.TPROC_VERSION) {
                        entity.TPROC_VERSION = 1;
                    }

                    const id = await repo.store(entity);
                    const storedId = Array.isArray(id) ? id[0] : id;

                    if (typeof data.tps_shred_on !== "undefined") {
                        await globalThis.orm
                            .repo("templateProcessShredding", trx)
                            .save({
                                TPROC_ID: storedId,
                                TPROC_VERSION: entity.TPROC_VERSION,
                                TPS_SHRED_ON: data.tps_shred_on,
                                TPS_SHRED_DELAY: data.tps_shred_delay,
                                TPS_SHRED_DMS_FILES:
                                    data.tps_shred_dms_files === "Y" ||
                                    data.tps_shred_dms_files === true
                                        ? "Y"
                                        : "N",
                                TPS_SHRED_PROCESS:
                                    data.tps_shred_process === "Y" ||
                                    data.tps_shred_process === true
                                        ? "Y"
                                        : "N",
                            });
                    }

                    if (typeof data.tpa_archive_on !== "undefined") {
                        await globalThis.orm
                            .repo("templateProcessArchivation", trx)
                            .save({
                                TPROC_ID: storedId,
                                TPA_ARCHIVE_ON: data.tpa_archive_on,
                                TPA_ARCHIVE_DELAY: data.tpa_archive_delay,
                            });
                    }

                    return id;
                },
            );

            this.tasLogger.info(
                tprocId
                    ? `Updated template process '${tprocName}' with id ${resultId} succesfully`
                    : `Created new template process '${tprocName}' successfully`,
                {
                    actorUserId: user.USER_ID,
                    tprocId: resultId,
                },
            );

            return this.handleResponse(request, reply, {
                result: true,
                id: resultId,
            });
        } catch (err: any) {
            this.tasLogger.error(
                tprocId
                    ? `Updating template process '${tprocName}' with id ${tprocId} was not succesful`
                    : `Creating new template process '${tprocName}' was not successful`,
                {
                    userId: user?.USER_ID,
                    tprocId,
                    err,
                },
            );

            return this.handleErrorResponse(request, reply, err);
        }
    }
}
