import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";

@Service()
export class TemplateProcessesAllController extends BaseController {
    public readonly method = "GET";

    public readonly url = "/template-processes/:id/:tprocVersion";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["id", "tprocVersion"] as const,
        },
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);

            const repo = globalThis.orm.repo("templateProcess");
            repo.setLocalization(user.LANGUAGE);

            const collection = globalThis.orm.collection(
                repo,
                repo.getTemplates(user.USER_ID),
            );
            collection.knex.whereNot("TPROC_STATUS", "E");

            const patchedRequest: any = {
                ...request,
                params: {
                    ...request.params,
                    id: {
                        TPROC_ID: request.params.id,
                        TPROC_VERSION: request.params.tprocVersion,
                    },
                },
            };

            const primaryCol = repo.entity.primaryColumn;

            collection.filteringColumns = {
                TPROC_OWNER_USER: {
                    type: "string",
                    key: `U1.USER_DISPLAY_NAME`,
                },
                TPROC_LAST_CHANGED_BY_USER: {
                    type: "string",
                    key: `U2.USER_DISPLAY_NAME`,
                },
                TPROC_VIS_ROLE_NAME: {
                    type: "string",
                    key: "TR.ROLE_NAME",
                },
                HEADER_NAME: {
                    type: "string",
                    key: "TH.HEADER_NAME",
                },
            };

            if (Array.isArray(globalThis.dynamicConfig.langs)) {
                globalThis.dynamicConfig.langs.forEach((lang) => {
                    const upperLang = lang.toUpperCase();
                    collection.filteringColumns[`HEADER_NAME_${upperLang}`] = {
                        type: "string",
                        key: `TH.HEADER_NAME_${upperLang}`,
                    };
                });
            }

            collection.orderingColumns = _.cloneDeep(
                collection.filteringColumns,
            );

            const itemsResult = await this.handleGetRequest(
                patchedRequest,
                collection,
            );

            if (!request.params.id) {
                itemsResult[0] = itemsResult[0].map((o: any) =>
                    _.omit(o, "TPROC_TVAR_ORDER"),
                ) as any[];
            }

            const [castedItems, totalCount, startTime] = await Promise.all([
                repo.castRows(
                    itemsResult[0],
                    repo.entity.getAttributes(false, true),
                ),
                itemsResult[1],
                itemsResult[2],
            ]);

            if (request.params.id && castedItems.length !== 1) {
                try {
                    await repo.get([
                        request.params.id,
                        request.params.tprocVersion,
                    ]);
                    throw new UserException(
                        "Lack of permissions.",
                        "LACK_OF_PERMISSIONS",
                    );
                } catch (err: any) {
                    if (err.codeName === "LACK_OF_PERMISSIONS") {
                        throw err;
                    }
                    throw new UserException(
                        "Object doesn't exist.",
                        "OBJECT_NOT_FOUND",
                    );
                }
            }

            if (request.params.id) {
                const tprocShredding = await globalThis.orm
                    .repo("templateProcessShredding")
                    .getForTprocId(castedItems[0].TPROC_ID);

                castedItems[0] = {
                    ...castedItems[0],
                    ...tprocShredding,
                };
            }

            const result = this.restifyData(castedItems, {
                primaryCol,
                single: !!request.params.id,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
                locale: user.LANGUAGE,
            });

            return this.handleResponse(request, reply, result);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
