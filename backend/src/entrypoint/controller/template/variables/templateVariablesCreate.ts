import { Service } from "typedi";
import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { AccessRights } from "../../../../api/utils/AccessRights";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import _ from "lodash";
import { UtilsService } from "../../../../api/services/UtilsService";
import * as VARIABLES from "../../../../api/orm/entity/const/variableConst";
import { InternalException } from "../../../../utils/errorHandling/exceptions/internalException";

@Service()
export class TemplateVariablesCreateController extends BaseController {
    public readonly method = "POST";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-variables/:id?";

    public readonly schema = {
        body: {
            type: "object" as const,
            properties: {
                dlist_name: { type: "string" as const, nullable: true },
                tvar_attribute: { type: "string" as const, nullable: true },
                tvar_business: { type: "string" as const, nullable: true },
                tvar_col_index: { type: "number" as const, nullable: true },
                tvar_copy_snapshot: { type: "string" as const, nullable: true },
                tvar_date_without_time: {
                    type: "string" as const,
                    nullable: true,
                },
                tvar_id: { type: "number" as const },
                tvar_is_shared: { type: "string" as const, nullable: true },
                tvar_is_shredable: { type: "string" as const, nullable: true },
                tvar_meta: { type: "string" as const, nullable: true },
                tvar_mm_class: { type: "string" as const, nullable: true },
                tvar_mm_class_attr: { type: "string" as const, nullable: true },
                tvar_multi: { type: "string" as const, nullable: true },
                tvar_name: { type: "string" as const },
                tvar_tooltip: { type: "string" as const, nullable: true },
                tvar_type: { type: "string" as const },
                updateInstances: { type: "boolean" as const, nullable: true },
                lovs: {
                    type: "array" as const,
                    items: {
                        anyOf: [
                            { type: "number" as const },
                            { type: "string" as const },
                            {
                                type: "object" as const,
                                properties: {
                                    value: {
                                        type: "string" as const,
                                        nullable: true,
                                    },
                                    title: {
                                        type: "string" as const,
                                        nullable: true,
                                    },
                                },
                            },
                        ],
                    },
                    nullable: true,
                },
            },
            patternProperties: {
                "^tvar_name_": { type: "string" as const, nullable: true },
                "^tvar_tooltip_": { type: "string" as const, nullable: true },
            },
            required: ["tvar_type", "tvar_name"] as const,
        },
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const },
                tprocVersion: { type: "number" as const },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "logRouteAccess",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const { tprocId, tprocVersion } = request.params;
            const data = request.body;

            const tvarId = await globalThis.database.transaction(
                async (trx) => {
                    const repo = globalThis.orm.repo("templateVariable", trx);
                    const tprocRepo = globalThis.orm.repo(
                        "templateProcess",
                        trx,
                    );
                    const ac = new AccessRights(trx);

                    const acc = await ac.getAccessRight(
                        "TEMPLATE_PROCESSES",
                        tprocId,
                        user.USER_ID,
                    );
                    if (acc.W !== "Y") {
                        throw new UserException(
                            "Lack of permissions.",
                            "LACK_OF_PERMISSIONS",
                        );
                    }

                    let entity;
                    if (request.body.tvar_id) {
                        entity = await repo.get(request.body.tvar_id);
                    } else {
                        entity = repo.getEntity();
                    }

                    const attrs = entity.attributes();
                    Object.keys(attrs).forEach((key) => {
                        const lk = key.toLowerCase();
                        if (typeof data[lk] !== "undefined") {
                            entity[key] = data[lk];
                        }
                    });
                    entity.TPROC_ID = tprocId;

                    if (entity.TVAR_TYPE === VARIABLES.TYPE_DYNAMIC_TABLE) {
                        const dllRepo = globalThis.orm.repo("dynamicTable");
                        const hasRights = await dllRepo.hasCreationRights(
                            user.ROLES,
                            entity.DLIST_NAME,
                        );
                        if (!hasRights) {
                            throw new UserException(
                                "Lack of permissions.",
                                "LACK_OF_PERMISSIONS",
                            );
                        }
                    }

                    if (entity.TVAR_TYPE === VARIABLES.TYPE_DYNAMIC_ROW) {
                        const tvarMeta = JSON.parse(entity.TVAR_META || "{}");
                        if (tvarMeta.script) {
                            try {
                                const compiled =
                                    await UtilsService.babelTransform(
                                        tvarMeta.script,
                                    );
                                tvarMeta.script_compiled = compiled;
                                entity.TVAR_META = JSON.stringify(tvarMeta);
                            } catch (err: any) {
                                throw new UserException(
                                    `Babel transform error. ${err}`,
                                    "BABEL_TRANSFORM_ERROR",
                                    {
                                        err,
                                        tvar_id: _.parseInt(entity.TVAR_ID),
                                        tproc_id: _.parseInt(entity.TPROC_ID),
                                        tproc_version: _.parseInt(
                                            entity.TPROC_VERSION,
                                        ),
                                    },
                                );
                            }
                        }
                    }

                    if (typeof data.value !== "undefined") {
                        entity.TVAR_NUMBER_VALUE = null;
                        entity.TVAR_TEXT_VALUE = null;
                        entity.TVAR_DATE_VALUE = null;
                        entity.value = data.value;
                    }

                    if (Array.isArray(request.body.lovs)) {
                        const lovs: any[] = [];
                        request.body.lovs.forEach((lovValue: any) => {
                            const lovRepo = globalThis.orm.repo(
                                "templateVariableLov",
                                trx,
                            );
                            const lov = lovRepo.getEntity();

                            switch (entity.TVAR_TYPE) {
                                case VARIABLES.TYPE_NUMBER_LIST:
                                    lov.TVARLOV_NUMBER_VALUE =
                                        lovValue.hasOwnProperty("value")
                                            ? lovValue.value
                                            : lovValue;
                                    break;

                                case VARIABLES.TYPE_TEXT_LIST:
                                    lov.TVARLOV_TEXT_VALUE =
                                        lovValue.value || lovValue;

                                    globalThis.dynamicConfig.langs.forEach(
                                        (lang: any) => {
                                            const lovsForLang = request.body[
                                                `lovs_${lang}`
                                            ] as unknown;
                                            if (Array.isArray(lovsForLang)) {
                                                const valueMutation = _.find(
                                                    lovsForLang,
                                                    {
                                                        value:
                                                            lovValue.value ||
                                                            lovValue,
                                                    },
                                                );
                                                if (valueMutation) {
                                                    lov[
                                                        `TVARLOV_TEXT_VALUE_${lang.toUpperCase()}`
                                                    ] = valueMutation.title;
                                                }
                                            }
                                        },
                                    );
                                    break;

                                case VARIABLES.TYPE_DATE_LIST:
                                    lov.TVARLOV_DATE_VALUE =
                                        lovValue.value || lovValue;
                                    break;

                                default:
                                    throw new InternalException(
                                        `Unknown TVAR_TYPE: '${entity.TVAR_TYPE}'`,
                                        "TYPE_NOT_IMPLEMENTED",
                                    );
                            }

                            lovs.push(lov);
                        });
                        entity._lovs = lovs;
                    }

                    const storedId = await repo.store(
                        entity,
                        tprocVersion,
                        request.body.updateInstances || false,
                    );

                    await tprocRepo.setTemplateChange(
                        tprocId,
                        tprocVersion,
                        user.USER_ID,
                    );

                    return storedId;
                },
            );

            return this.handleResponse(request, reply, {
                result: true,
                id: tvarId,
            });
        } catch (err: any) {
            if (err.codeName === "UNIQUE_CONSTRAINT") {
                return this.handleErrorResponse(
                    request,
                    reply,
                    new UserException(
                        "Variable name already exists!",
                        "UNIQUE_CONSTRAINT",
                    ),
                );
            }
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
