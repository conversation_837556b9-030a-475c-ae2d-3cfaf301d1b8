import { Service } from "typedi";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { FastifyReply, FastifyRequest } from "fastify";
import { AccessRights } from "../../../../api/utils/AccessRights";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";

@Service()
export class TemplateVariablesRemoveController extends BaseController {
    public readonly method = "DELETE";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-variables/:id";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const },
                tprocVersion: { type: "number" as const },
            },
            required: ["tprocId", "tprocVersion", "id"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const { tprocId, id } = request.params;
            const tprocVersion = request.params.tprocVersion;

            const ac = new AccessRights(globalThis.database);
            const acc = await ac.getAccessRight(
                "TEMPLATE_VARIABLES",
                id,
                user.USER_ID,
            );
            if (acc.D !== "Y") {
                throw new UserException(
                    "Lack of permissions.",
                    "LACK_OF_PERMISSIONS",
                );
            }

            await globalThis.database.transaction(async (trx) => {
                const repo = globalThis.orm.repo("templateVariable", trx);
                const varRepo = globalThis.orm.repo("variable", trx);
                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                const entity = repo.getEntity();
                entity.TVAR_ID = request.params.id;

                const vars = await varRepo.connection
                    .select("IVAR_ID")
                    .from(varRepo.tableName)
                    .where("TVAR_ID", id);

                if (Array.isArray(vars) && vars.length > 0) {
                    throw new UserException(
                        "Cannot delete template variable. Some variables are derived from it.",
                        "CONSTRAINT_EXCEPTION",
                    );
                }

                await repo.delete(entity);

                this.tasLogger.info("Template variable deleted", {
                    tvarId: id,
                    tprocId,
                    tprocVersion,
                    actorUserId: user.USER_ID,
                });

                await tprocRepo.setTemplateChange(
                    tprocId,
                    tprocVersion,
                    user.USER_ID,
                );
            });

            return this.handleResponse(request, reply, { result: true });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
