import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";
import _ from "lodash";
import { UserException } from "../../../../utils/errorHandling/exceptions/userException";
import * as VARIABLES from "../../../../api/orm/entity/const/variableConst";
import { TemplateVariableLovRepository } from "../../../../api/orm/repository/TemplateVariableLovRepository";

@Service()
export class TemplateVariablesGetController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-variables/:id?";

    public readonly schema = {
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
        params: {
            type: "object" as const,
            properties: {
                tprocId: { type: "number" as const },
                id: { type: "number" as const },
                tprocVersion: { type: "number" as const },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const user = await this.auth.getUserData(request);
            const repo = globalThis.orm.repo("templateVariable");
            repo.setLocalization(user.LANGUAGE);

            const collection = repo.getForTemplateProcess(
                request.params.tprocId,
            );

            const filteringColumns = {
                CLASS_AND_ATTR: {
                    type: "string",
                    key: `TVAR_MM_CLASS ${globalThis.orm.db.concatColumns(
                        ".",
                    )} TVAR_MM_CLASS_ATTR`,
                },
            };
            collection.filteringColumns = filteringColumns;
            collection.orderingColumns = _.cloneDeep(filteringColumns);

            const primaryCol = repo.entity.primaryColumn;

            const varLovRepo = globalThis.orm.repo("templateVariableLov");
            varLovRepo.setLocalization(user.LANGUAGE);

            const [data, totalCount, startTime] = await this.handleGetRequest(
                request,
                collection,
                {
                    returnEntities: true,
                },
            );
            const items = await repo.castRows(
                data,
                repo.entity.getAttributes(false, true),
            );

            if (request.params.id && (!Array.isArray(items) || !items.length)) {
                throw new UserException(
                    "Object doesn't exist.",
                    "OBJECT_NOT_FOUND",
                );
            }

            const lists = [
                VARIABLES.TYPE_DYNAMIC_LIST,
                VARIABLES.TYPE_TEXT_LIST,
                VARIABLES.TYPE_DATE_LIST,
                VARIABLES.TYPE_NUMBER_LIST,
            ];

            const ids = items
                .filter((item) => lists.includes(item.TVAR_TYPE))
                .map((item) => item.TVAR_ID);

            const varLovs = await varLovRepo
                .getForTemplateVariables("*", ids)
                .collectAssoc("TVAR_ID");

            const enrichedItems = TemplateVariableLovRepository.joinLov(
                items,
                varLovs,
            );

            const filledItems = await varLovRepo.fillLovValues(enrichedItems);

            const output = filledItems.map((ent: any) => {
                const out: { [key: string]: any } = {
                    tvar_value: ent.value,
                    tvar_group: "not implemented",
                    tvar_lov: ent.getLovData(),
                    tvar_lov_value: ent.lovValue,
                    class_and_attr: ent._raw.CLASS_AND_ATTR,
                };

                const entityAttributes = ent.attributes();
                Object.keys(entityAttributes).forEach((attrName) => {
                    out[attrName.toLowerCase()] = ent[attrName];
                });

                const tvarEntity = globalThis.orm
                    .repo("templateVariable")
                    .getEntity();
                Object.keys(tvarEntity.attributes()).forEach((attrName) => {
                    if (tvarEntity.attributes()[attrName].translated) {
                        out[attrName.toLowerCase()] = ent.raw[attrName];
                    }
                });

                globalThis.dynamicConfig.langs.forEach((lang: any) => {
                    out[`tvar_lov_${lang}`] = ent.getLovData(lang);
                });

                return out;
            });

            const responseData = this.restifyData(output, {
                primaryCol,
                single: !!request.params.id,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
                locale: user.LANGUAGE,
            });

            return this.handleResponse(request, reply, responseData);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
