import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";

@Service()
export class TemplateVariablesUsageForProcessController extends BaseController {
    public readonly method = "GET";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-variables/usage/:id?";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: {
                    type: "number" as const,
                },
                id: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        querystring: {
            type: "object" as const,
            properties: {
                filter: { type: "string" as const, nullable: true },
                total_count: { type: "boolean" as const },
                only_count: { type: "boolean" as const },
                limit: { type: "number" as const },
                offset: { type: "number" as const },
                order: { type: "string" as const, nullable: true },
                sort: { type: "string" as const, nullable: true },
                disable_accent: { type: "boolean" as const },
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const { tprocId, tprocVersion } = request.params;

            const repo = globalThis.orm.repo("templateTaskVarUsage");
            const collection = repo.getForTProcess(tprocId, tprocVersion);
            const primaryCol = repo.entity.primaryColumn;

            collection.filteringColumns = {};

            const result = await this.handleGetRequest(request, collection);
            let [rawItems, totalCount, startTime] = await Promise.all(result);

            const items = await repo.castRows(
                rawItems,
                repo.entity.getAttributes(false, true),
            );

            const taskMap: Record<number, any> = {};

            items.forEach((item) => {
                if (!taskMap[item.TTASK_ID]) {
                    taskMap[item.TTASK_ID] = {
                        ttask_id: item.TTASK_ID,
                        global_mapping: "N",
                        tproc_id: tprocId,
                        ttask_var_mapping: [],
                    };
                }

                taskMap[item.TTASK_ID].ttask_var_mapping.push({
                    tvar_id: item.TVAR_ID,
                    usage: item.TTASKVARUSG_USAGE,
                    axis_x: item.AXIS_X,
                    axis_y: item.AXIS_Y,
                    tsec_id: item.TSEC_ID,
                    tsec_x: item.TSEC_X,
                    tsec_y: item.TSEC_Y,
                });
            });

            const processedItems = Object.values(taskMap);
            totalCount = processedItems.length;

            const responseData = this.restifyData(processedItems, {
                primaryCol,
                single: !!request.params.id,
                totalCount,
                startTime,
                routePath: request.routeOptions.url,
                reqUrl: request.url,
            });

            return this.handleResponse(request, reply, responseData);
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
