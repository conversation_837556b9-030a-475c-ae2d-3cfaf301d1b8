import { FastifyReply, FastifyRequest } from "fastify";
import { BaseController } from "../../baseController";
import { InputType } from "../../../../entity/schema/base";
("../../baseController");
import { Service } from "typedi";

@Service()
export class TemplateVariablesUsagePostProcessController extends BaseController {
    public readonly method = "POST";

    public readonly url =
        "/template-processes/:tprocId/:tprocVersion/template-variables/usage";

    public readonly schema = {
        params: {
            type: "object" as const,
            properties: {
                tprocId: {
                    type: "number" as const,
                },
                tprocVersion: {
                    type: "number" as const,
                },
            },
            required: ["tprocId", "tprocVersion"] as const,
        },
        body: {
            type: "array" as const,
            items: {
                type: "object" as const,
                properties: {
                    ttask_id: { type: "number" as const },
                    name: { type: "string" as const, nullable: true },
                    global_mapping: { type: "string" as const, nullable: true },
                    type: { type: "string" as const, nullable: true },
                    visibleBySearch: { type: "boolean" as const },
                    lang: {
                        type: "object" as const,
                        properties: {
                            lang: { type: "string" as const, nullable: true },
                        },
                        patternProperties: {
                            "^[a-z]{2}$": {
                                type: "string" as const,
                                nullable: true,
                            },
                        },
                    },
                    ttask_var_mapping: {
                        type: "array" as const,
                        items: {
                            type: "object" as const,
                            properties: {
                                tvar_id: { type: "number" as const },
                                usage: {
                                    type: "string" as const,
                                    nullable: true,
                                },
                                axis_x: {
                                    type: "number" as const,
                                    nullable: true,
                                },
                                axis_y: {
                                    type: "number" as const,
                                    nullable: true,
                                },
                                tsec_id: {
                                    type: "number" as const,
                                    nullable: true,
                                },
                                tsec_x: {
                                    type: "number" as const,
                                    nullable: true,
                                },
                                tsec_y: {
                                    type: "number" as const,
                                    nullable: true,
                                },
                            },
                        },
                        nullable: true,
                    },
                },
                required: ["ttask_id"],
            },
        },
    };

    public get hooks() {
        return {
            preValidation: this.hooksHandler.getHooks([
                "isLoggedIn",
                "inMaintenance",
                "logRouteAccess",
                "isLicenseValid",
            ]),
        };
    }

    public async handler(
        request: FastifyRequest<InputType<typeof this.schema>>,
        reply: FastifyReply,
    ): Promise<void> {
        try {
            const { tprocId, tprocVersion } = request.params;

            await globalThis.database.transaction(async (trx) => {
                const tvarUsageRepo = globalThis.orm.repo(
                    "templateTaskVarUsage",
                    trx,
                );

                for (const map of request.body) {
                    await tvarUsageRepo.setTTaskVariableUsage(
                        map.ttask_id,
                        tprocId,
                        map.ttask_var_mapping,
                        tprocVersion,
                    );
                }
                const userId = (await this.auth.getUserData(request)).USER_ID;
                const tprocRepo = globalThis.orm.repo("templateProcess", trx);
                await tprocRepo.setTemplateChange(
                    tprocId,
                    tprocVersion,
                    userId,
                );
            });
            return this.handleResponse(request, reply, { result: true });
        } catch (err: any) {
            return this.handleErrorResponse(request, reply, err);
        }
    }
}
