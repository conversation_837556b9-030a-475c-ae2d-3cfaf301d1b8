import path from "path";
import fs from "fs";
import os from "os";

/**
 * Some default added configuration which can be overridden with local.js.
 * All objects are extended with local.js so you never loose any property of overriden objects.
 */

const readSecretFile = (path: string): string | undefined => {
    try {
        return fs.readFileSync(path, "utf-8").trim();
    } catch {
        return undefined;
    }
};

const getEnvSecret = <T>(
    envVar: string,
    defaultValue: T | undefined = undefined,
): string | T | undefined => {
    const fromEnv = process.env[envVar];
    const filePath = process.env[`${envVar}_FILE`];

    let fromFile = null;
    if (filePath) {
        try {
            fromFile = readSecretFile(filePath);
        } catch (_err) {
            //
        }
    }

    return fromFile || fromEnv || defaultValue;
};

const getEnvBool = (envVar: string, defaultValue: boolean = true): boolean => {
    const envValue: string | undefined = getEnvSecret(envVar);

    if (typeof envValue === "undefined" || envValue === "") {
        return defaultValue;
    }

    return (
        Number(envValue) === 1 ||
        envValue.toUpperCase().startsWith("Y") ||
        envValue === "true"
    );
};

const getEnvJson = <T extends Record<string, any>>(
    envVar: string,
    defaultValue: T | undefined,
): T | undefined => {
    const envValue: string | undefined = getEnvSecret(envVar);

    if (typeof envValue === "undefined" || envValue === "") {
        return defaultValue;
    }

    return JSON.parse(envValue);
};

const getEnvNumber = (
    envVar: string,
    defaultValue?: number,
): number | undefined => {
    const envValue = getEnvSecret(envVar);

    if (typeof envValue === "undefined" || envValue === "") {
        return defaultValue;
    }

    return Number(envValue);
};

module.exports = {
    host: "0.0.0.0",
    port: getEnvNumber("TAS_BACKEND_PORT", getEnvNumber("PORT", 8001)),
    hostname: getEnvSecret("TAS_BACKEND_URL", "http://localhost:8001"), // Self hostname to generate dynamic wsdl.
    frontendUrl: getEnvSecret("TAS_FRONTEND_URL", "http://localhost:8000"),
    teamassistantTheme: getEnvSecret("TAS_FRONTEND_TASTHEME", false), // Team Assistant logo theme for mail.
    tenantId: null, // Client tenant identification for multitenant run,  tenantId == 'admin' for tenant Admin
    tenantPath: null, // Client tenant path for storing specific files (cache, tmp, ...)
    prefix: null, // global route prefix - for ex. filling with 'test' prefixes all routes like '/test/status/db
    hookTimeout: 120000,
    serverZone: getEnvSecret("TAS_SERVER_TIMEZONE", "Europe/Prague"), // zone has to correspond to the server and DB time
    NLS_SORT: "CZECH",
    OPTIMIZER_FEATURES_ENABLE: null, // Oracle 12 should has optimizer=******** enabled (eg Aspironix).
    NLS_NUMERIC_CHARACTERS: ".,",
    NODE_TLS_REJECT_UNAUTHORIZED: getEnvNumber(
        "TAS_NODE_TLS_REJECT_UNAUTHORIZED",
        0,
    ),
    defaultRequestItemLimit: 100,
    possibleLangs: [
        "cs",
        "de",
        "en",
        "fr",
        "hr",
        "it",
        "pl",
        "ro",
        "ru",
        "sk",
        "sr",
    ],
    langs: getEnvSecret("TAS_LANGUAGES", "cs en sk")
        ?.split(" ")
        .filter(function (l) {
            return l.length;
        }), // the first is main language and it's in main (old) column (tvar_name, ...)
    defaultLang: getEnvSecret("TAS_DEFAULT_LANGUAGE", "cs"), // is set where new user was logged
    floatingPointPrecision: 6,
    appPath: path.normalize(`${__dirname}/../`),
    archivation: {
        enabled: true,
    },
    cache: {
        client: "local",
        accessTokenExpireDuration: 8 * 3600, // 8 hour in seconds
        refreshTokenExpireDuration: 30 * 3 * 28800, // 30 days in seconds
        secureCookie: true,
        httpOnlyCookie: true,
        sameSiteCookie: "strict",
        accessTokenName: "accessTokenV2",
        refreshRefreshTokenName: "refreshTokenV2",
        refreshLogoutTokenName: "refreshTokenLogoutV2",
    },
    connections: {
        knexConnection: {
            host: getEnvSecret("TAS_DB_HOSTNAME"),
            port: getEnvNumber("TAS_DB_PORT"),
            user: getEnvSecret("TAS_DB_USERNAME", "TAS"),
            password: getEnvSecret(
                "TAS_DB_PASSWORD",
                readSecretFile("/run/secrets/tas_db_pw"),
            ),
            database: getEnvSecret("TAS_DB_NAME", "TAS"),
            requestTimeout: getEnvNumber("TAS_DB_REQUEST_TIMEOUT", 120000),
            driverOptions:
                getEnvSecret("TAS_DB_VENDOR") === "mssql"
                    ? {
                          connection: {
                              encrypt: getEnvBool("TAS_DB_ENCRYPTION", false),
                              options: {
                                  trustServerCertificate: getEnvBool(
                                      "TAS_DB_ENCRYPTION_IGNORE_CERTIFICATE",
                                      false,
                                  ),
                              },
                          },
                      }
                    : {},
        },
    },
    db: {
        debug: getEnvBool("TAS_DB_DEBUG", false), // if true, all sql queries are logged to console
        sqlPerf: getEnvBool("TAS_DB_SQL_PERF", false), // enable measurement of all sql queries
        client: getEnvSecret("TAS_DB_VENDOR"),
        maxPoolConnections: getEnvNumber("TAS_DB_MAX_POOL_CONNECTION", 40), // Always keep lower than UV_THREADPOOL_SIZE
        maxRows: 10000, // Maximum rows retrieved for connection.raw().
        minSqlVersion: 11, // Lowest major version supported
        version: 0, // will be updated during bootstrap
        maxParameters: 2100,
        encryptionKey: getEnvSecret("TAS_DATABASE_ENCRYPTION_KEY", ""),
        encryptionIvLength: getEnvSecret(
            "TAS_DATABASE_ENCRYPTION_IV_LENGTH",
            32,
        ),
        method: "aes-256-cbc",
    },
    csvStorageDir: "/app/tas/storage/assets/csv", // dir where csv will be stored
    tas: {
        cachePath: "/app/tas/storage/cache",
        customAssetsPath: undefined,
        toPdf: {
            logErrors: false,
        },
        locks: {
            // Mutex lock, tasks queue
            timeouts: {
                workflow: 1 * 60 * 1000,
            },
        },
        calculations: {
            dateFormat: "DD.MM.YYYY HH:mm:ss", // Date assign to text will be format with this dateFormat. See https://momentjs.com/.
            locale: "cs", // locale for moment operations
            requestHttpProtocol: "",
            eval: {
                baseURL: "//wwwinfo.mfcr.cz",
            },
        },
        addingTasks: false, // default false; possibility to add a task to an existing case
    },
    crons: {
        startCrons: getEnvBool("TAS_CRONS_START", true),
        runOnStart: getEnvBool("TAS_CRONS_RUN_ON_START", true), // When the main process is started, the Cron process will start too
        allowManualControl: true, // User can manually run and stop Crons
        canBeModified: true, // User can modify the Crons
        additionalCrons: [
            // You can add your own custom scripts here, i.e. "node/php ..."
            /*
            {
                CRON_NAME: // MANDATORY,
                CRON_FILE: // MANDATORY,
                CRON_PARAMETERS: {
                    command: '' // MANDATORY
                    timeout: // Optional
                },
                CRON_ALIAS: // Optional,
                CRON_DESCRIPTION:  // Optional,
            },
            */
        ],
        customizedCrons: [
            // You can enable customizedCronFolders Crons to be added here.
        ],
    },
    mail: {
        sendingEnabled: getEnvBool("TAS_MAIL_SENDING_ENABLED", true),
        smtp: {
            type: "basic",
            secure: getEnvBool("TAS_MAIL_SECURE", false), // upgrade later with STARTTLS
            tls: getEnvJson("TAS_MAIL_TLS", {
                rejectUnauthorized: getEnvBool(
                    "TAS_MAIL_REJECT_UNAUTHORIZED",
                    true,
                ),
            }),
            authMethod: getEnvSecret("TAS_MAIL_AUTH_METHOD", undefined),
            debug: getEnvBool("TAS_MAIL_DEBUG", false),
            logger: getEnvBool("TAS_MAIL_LOGGER", false),
            ignoreTLS: getEnvBool("TAS_MAIL_IGNORE_TLS", false),
            requireTLS: getEnvBool("TAS_MAIL_REQUIRE_TLS", false),
            name: getEnvSecret("TAS_MAIL_CONNECTION_NAME", undefined),
            localAddress: getEnvSecret(
                "TAS_MAIL_CONNECTION_LOCAL_ADDRESS",
                undefined,
            ),
            connectionTimeout: getEnvNumber(
                "TAS_MAIL_CONNECTION_TIMEOUT",
                undefined,
            ),
            greetingTimeout: getEnvNumber(
                "TAS_MAIL_CONNECTION_GREETING_TIMEOUT",
                undefined,
            ),
            socketTimeout: getEnvNumber(
                "TAS_MAIL_CONNECTION_SOCKET_TIMEOUT",
                undefined,
            ),
            dnsTimeout: getEnvNumber(
                "TAS_MAIL_CONNECTION_DNS_TIMEOUT",
                undefined,
            ),
        },
        footer: `
            <div class="footer" style="font-size:0.8em;margin-top:2em;">
                Na tento e-mail neodpovídejte, byl vygenerován automaticky systémem <a style="color:#0000FF;" href="http://www.teamassistant.cz/">Team Assistant</a>.<br/>
            </div>
        `,
        sendmailPath: "/sbin/sendmail", // null for default,
        maxConcurrentCalls: 1,
    },
    events: {
        forceSoap12Headers: false, // V1 or V2 ?
        soap: {
            headers: () =>
                // client.addSoapHeader(name1, value1);
                // client.addSoapHeader(name2, value2);
                true,
            timeout: 30 * 1000, // max request timeout ms
        },
    },
    license: {
        silenceExpiredLicenses: getEnvBool(
            "LICENSE_SILENCE_EXPIRED_LICENSES",
            false,
        ),
        keysLocation: "../config/key/featureFlag",
    },
    dms: {
        tikaUrl: getEnvSecret("TAS_TIKA_URL", "http://tika:9998"), // url to tika, turnoff with tikaEnabled
        elasticUrl: getEnvSecret(
            "TAS_ELASTIC_URL",
            "http://elasticsearch:9200",
        ), // url to elasticSearch, turnoff with fulltext
        elasticVersion: getEnvNumber("TAS_ELASTIC_VERSION", 7), // used version of elasticsearch (5 or 7)
        fulltext: getEnvSecret("TAS_ELASTIC_INDEX_NAME") || null,
        maximumRevisionFiles: 100,
        storagePath: getEnvSecret("TAS_DMS_STORAGE_PATH", "/app/tas/dms"),
        security: {
            allowedExternalSources: getEnvSecret(
                "TAS_DMS_ALLOWED_EXTERNAL_SOURCES",
                os.tmpdir(),
            )?.split("|"),
            encryptContent: getEnvBool("TAS_DMS_ENCRYPT_CONTENT", false), // Encrypt data before store to file storage.
            encryptFileNames: getEnvBool("TAS_DMS_ENCRYPT_FILENAMES", false), // Encrypt file names
            password: getEnvSecret("TAS_DMS_ENCRYPT_KEY", ""), // Encryption password
            algorythm: getEnvSecret("TAS_DMS_ENCRYPT_METHOD", "aes-256-ctr"), // Encryption method.
            elasticsearch: {
                secured: getEnvBool("TAS_ELASTIC_SECURED", true),
                user: getEnvSecret("TAS_ELASTIC_USERNAME", "elastic"),
                password: getEnvSecret(
                    "TAS_ELASTIC_PASSWORD",
                    readSecretFile("/run/secrets/tas_elastic_pw"),
                ),
            },
        },
    },
    security: {
        saltRounds: getEnvNumber("TAS_SECURITY_SALT_ROUNDS", 2), // change this number depending on the server performance
        pepper: getEnvSecret(
            "TAS_SECURITY_PEPPER",
            "4enqL99M7Cz/cGlM3Dy/eCUVs",
        ), // random secret 25-char string
    },
    watchers: [], // list of enabled watchers scripts in tas3back/watchers/*.js. Watcher can execute any executable program after any file is changed.
    majorVersion: 4, // Version number. tas3=3, tas4=4, ...
    firebase: {
        auth: {
            projectId: getEnvSecret("FIREBASE_AUTH_PROJECT_ID", null),
            privateKey: getEnvSecret("FIREBASE_AUTH_PRIVATE_KEY", null),
            clientEmail: getEnvSecret("FIREBASE_AUTH_CLIENT_EMAIL", null),
            androidAppId: getEnvSecret("FIREBASE_AUTH_ANDROID_APP_ID", null),
            androidApiKey: getEnvSecret("FIREBASE_AUTH_ANDROID_API_KEY", null),
            iosAppId: getEnvSecret("FIREBASE_AUTH_IOS_APP_ID", null),
            iosApiKey: getEnvSecret("FIREBASE_AUTH_IOS_API_KEY", null),
            senderId: getEnvSecret("FIREBASE_AUTH_SENDER_ID", null),
        },
    },
    logger: {
        elastic: {
            // only node 14.6.0+, es 7.9+
            enabled: getEnvBool("TAS_LOGGER_ELASTIC_ENABLE", false),
            pinoConfig: {
                index: getEnvSecret("TAS_LOGGER_ELASTIC_INDEX_NAME", null),
                consistency: "one",
                node: getEnvSecret(
                    "TAS_LOGGER_ELASTIC_URL",
                    "http://localhost:9200",
                ),
                "es-version": getEnvNumber("TAS_LOGGER_ELASTIC_VERSION", 8),
                op_type: "create", // needed to data-streams
                auth: {
                    username: getEnvSecret("TAS_LOGGER_ELASTIC_USERNAME", null),
                    password: getEnvSecret("TAS_LOGGER_ELASTIC_PASSWORD", null),
                },
            },
            logRemoveFilters: JSON.stringify([
                {
                    bool: {
                        must: [
                            {
                                range: {
                                    "@timestamp": {
                                        lt: "now-10d/d",
                                    },
                                },
                            },
                            {
                                range: {
                                    level: {
                                        gt: 2,
                                    },
                                },
                            },
                        ],
                    },
                },
                {
                    bool: {
                        must: [
                            {
                                bool: {
                                    should: [
                                        {
                                            bool: {
                                                must_not: {
                                                    exists: {
                                                        field: "@timestamp",
                                                    },
                                                },
                                            },
                                        },
                                        {
                                            range: {
                                                "@timestamp": {
                                                    lt: "now-20d/d",
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        ],
                    },
                },
            ]),
            calculationLogRemoveFilters: JSON.stringify([
                {
                    bool: {
                        must: [
                            {
                                term: {
                                    calc_log: true,
                                },
                            },
                            {
                                bool: {
                                    should: [
                                        {
                                            bool: {
                                                must_not: {
                                                    exists: {
                                                        field: "@timestamp",
                                                    },
                                                },
                                            },
                                        },
                                        {
                                            range: {
                                                "@timestamp": {
                                                    lt: "now-10d/d",
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        ],
                    },
                },
            ]),
            metaRemoveFilters: JSON.stringify([]),
        },
        logLevel: 3, // Only logs with level lower than logLevel are logged. 1=error; 2=warn; 3=info; 4=debug; 5=trace.
        logFilePath: getEnvSecret(
            "TAS_LOGGER_FILE_PATH",
            `${path.resolve(".")}/../storage/log/fallback.log`,
        ), // Path to log file. Used only as Arango fallback.
        metaDirectory: getEnvSecret(
            "TAS_LOGGER_FILE_META_DIR",
            `${path.resolve(".")}/../storage/log/`,
        ), // ends with / !
        enableStdoutLog: getEnvBool("TAS_LOGGER_STDOUT_ENABLE", false), // Enable stdout -> console log file or any other piped destination.
        arango: {
            enabled: getEnvBool("TAS_LOGGER_ARANGO_ENABLE", true),
            host: getEnvSecret("TAS_LOGGER_ARANGO_URL", "http://arango:8529"), // Arango host:port (http://127.0.0.1:8529). Use null to disable arango logger. Fallback file will be used.
            db_name: getEnvSecret(
                "TAS_ARANGO_DB_NAME",
                readSecretFile("/run/secrets/tas_arangodb_user") + "_db",
            ), // Database name, use something like '{instance_id}_db'.
            collections: {
                logs: "logs", // Logs collection name. Logs for errors, debug logs etc.
                meta: "meta", // Meta for logs collection name.
                calculation_logs: "calculation_logs", // Logs collection name. Logs for errors, debug logs etc.
                zarch_logs: "zarch_logs", // Logs zea archiv name. Logs for errors, debug logs etc.
            },
            auth: {
                user: getEnvSecret(
                    "TAS_ARANGO_USERNAME",
                    readSecretFile("/run/secrets/tas_arangodb_user"),
                ),
                pass: getEnvSecret(
                    "TAS_ARANGO_PASSWORD",
                    readSecretFile("/run/secrets/tas_arangodb_user_pw"),
                ),
                // https://www.arangodb.com/docs/stable/http/general.html#user-jwt-token
                jwt: null,
            },
            options: {
                // options to pass as a main Arango config, host, auth, db_name must pass separately
            },
            logRemoveFilters: JSON.stringify([
                [
                    'row.time < DATE_SUBTRACT(DATE_NOW(), 10, "day")',
                    "row.level > 2", // level > WARN
                ],
                [
                    // Remove all older 30 days or with no time information.
                    'row.time == null || row.time < DATE_SUBTRACT(DATE_NOW(), 20, "day")',
                ],
            ]),
            calculationLogRemoveFilters: JSON.stringify([
                [
                    'row.time == null || row.time < DATE_SUBTRACT(DATE_NOW(), 10, "day")',
                ],
            ]),
            metaRemoveFilters: JSON.stringify([
                // Remove meta without relation to logs only by date
                ['row.time < DATE_SUBTRACT(DATE_NOW(), 30, "day") LIMIT 1000'],
            ]),
            hidePassword: true,
        },
    },
    datadog: {
        tracing: false,
    },
    secrets: {},
    redis: getEnvSecret("TAS_REDIS_URL", {
        host: getEnvSecret("TAS_REDIS_HOST", undefined),
        port: getEnvNumber(
            "TAS_REDIS_PORT",
            getEnvBool("TAS_REDIS_TLS", false) ? 6380 : 6379,
        ),
        username: getEnvSecret("TAS_REDIS_USERNAME", undefined),
        password: getEnvSecret("TAS_REDIS_PASSWORD", undefined),
        tls: getEnvBool("TAS_REDIS_TLS", false)
            ? {
                  ca: getEnvSecret("TAS_REDIS_TLS_CA", undefined),
              }
            : undefined,
    }),
    loginRateLimiter: {
        opts: {
            points: getEnvNumber("TAS_LOGIN_RATE_FAILED_COUNT", 6), // number of failed attemps to start ban
            duration: getEnvNumber("TAS_LOGIN_RATE_BAN_DURATION", 5 * 60), // (s) time until reset of count of failed attempts
            keyPrefix: "LOGIN_RATE_LIMITER",
        },
    },
    authorizationHeader: getEnvSecret(
        "TAS_BACKEND_AUTHORIZATION_HEADER",
        "authorization",
    ),
    unauthorizedStatusCode: getEnvNumber("TAS_UNAUTHORIZED_STATUS_CODE", 401),
    certificates: {
        storage: `${path.resolve(".")}/../storage/certificate`,
    },
    responseHeaders: {
        // 'X-Content-Type-Options': 'nosniff',
        // 'Referrer-Policy': 'origin',
        // 'Access-Control-Expose-Headers': '',
        // 'Cross-Origin-Opener-Policy': 'same-origin',
        // 'Access-Control-Max-Age': 60,
    },
    plugins: {
        destination: "/app/tas/storage/plugins",
    },
    serviceOperations: {
        maxUpdatedRows: 1000,
    },
    paths: {
        tmp: "/app/tas/storage/tmp",
        userPhotoPath: "/app/tas/storage/photos",
    },
    userPhoto: {
        maxPhotosPerUser: 10,
        defaultExtension: "webp",
        maxWidth: 3840,
        maxHeight: 2160,
    },
    monaco: {
        defaultsDir: path.join(__dirname, "../../dist/documentation/monaco"),
        defaultsFilename: "defaults.json",
    },
    puppeteer: {
        args: [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--no-zygote",
            "--disable-software-rasterizer",
            "--disable-gpu",
        ],
        forbiddenPaths: [
            "/socket.io/",
            "/sockjs-node/",
            "/tasks/watch",
            "/authorization/list",
        ],
    },
};
