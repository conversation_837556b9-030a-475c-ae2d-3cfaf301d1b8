import { Container } from "typedi";

import { TemporaryService } from "../../service/temporary/temporaryService";
import { Auth } from "../../service/authorization/Auth";
import { ExportService } from "../../service/export/exportService";
import { FileService } from "../../service/file/FileService";
import { ImportService } from "../../service/import/importService";
import { PostService } from "../../service/post/PostService";
import { FeatureFlagService } from "../../service/secretStore/featureFlag/featureFlagService";
import { PrintService } from "../../service/print/printService";
import { CronService } from "../../service/cron/cronService";

export const initServiceContainer = async () => ({
    temporary: Container.get(TemporaryService),
    auth: Container.get(Auth),
    file: Container.get(FileService),
    export: Container.get(ExportService),
    featureFlag: Container.get(FeatureFlagService),
    import: Container.get(ImportService),
    print: Container.get(PrintService),
    cron: Container.get(CronService),
    post: Container.get(PostService),
});
