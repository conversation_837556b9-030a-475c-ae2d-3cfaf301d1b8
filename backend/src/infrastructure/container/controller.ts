import { Container } from "typedi";
import { PluginManager } from "../plugin/pluginManager";
import { ArchivationGetAllController } from "../../entrypoint/controller/archivation/archivationAll";
import { ArchivationProcessArchiveController } from "../../entrypoint/controller/archivation/archivationArchive";
import { ArchivationGetListController } from "../../entrypoint/controller/archivation/archivationList";
import { ArchivationProcessUnarchiveController } from "../../entrypoint/controller/archivation/archivationUnarchive";
import { AuthorizationAuthController } from "../../entrypoint/controller/authorization/authorizationAuth";
import { AuthorizationAuthByModuleController } from "../../entrypoint/controller/authorization/authorizationAuthByModule";
import { AuthorizationAuthByTokenPairController } from "../../entrypoint/controller/authorization/authorizationAuthByTokenPair";
import { AuthorizationDeleteController } from "../../entrypoint/controller/authorization/authorizationDeleteAuthorization";
import { AuthorizationAdminAuthController } from "../../entrypoint/controller/authorization/authorizationFilterSystemRoles";
import { AuthorizationGetConfigController } from "../../entrypoint/controller/authorization/authorizationGetAuthorizationConfig";
import { AuthorizationGetListController } from "../../entrypoint/controller/authorization/authorizationGetAuthorizationList";
import { AuthorizationLogoutController } from "../../entrypoint/controller/authorization/authorizationLogout";
import { AuthorizationMobileAuthController } from "../../entrypoint/controller/authorization/authorizationMobileAuth";
import { AuthorizationParseAccessTokenController } from "../../entrypoint/controller/authorization/authorizationParseAccessToken";
import { AuthorizationsSaveController } from "../../entrypoint/controller/authorization/authorizationSaveAuthorizations";
import { AuthorizationTestController } from "../../entrypoint/controller/authorization/authorizationTestAuthorization";
import { AzureCallbackController } from "../../entrypoint/controller/authorization/callback/azureCallback";
import { CalculationsDefaultsGetController } from "../../entrypoint/controller/calculcation/calculationsDefaultsGet";
import { CaseStatusDeleteController } from "../../entrypoint/controller/caseStatus/caseStatusDelete";
import { CaseStatusGetController } from "../../entrypoint/controller/caseStatus/caseStatusGet";
import { CaseStatusPostController } from "../../entrypoint/controller/caseStatus/caseStatusPost";
import { CompetenceCloneUserUpsertController } from "../../entrypoint/controller/competence/competenceCloneUserUpsert";
import { CompetenceGetController } from "../../entrypoint/controller/competence/competenceGet";
import { CompetenceRegexGetController } from "../../entrypoint/controller/competence/competenceRegexGet";
import { CompetenceRegexUpsertController } from "../../entrypoint/controller/competence/competenceRegexUpsert";
import { CompetenceRoleGetController } from "../../entrypoint/controller/competence/competenceRoleGet";
import { CompetenceRoleUpsertController } from "../../entrypoint/controller/competence/competenceRoleUpsert";
import { CompetenceUpsertController } from "../../entrypoint/controller/competence/competenceUpsert";
import { CompetenceUserController } from "../../entrypoint/controller/competence/competenceUserGet";
import { CompetenceUserUpsertController } from "../../entrypoint/controller/competence/competenceUserUpsert";
import { CompetenceRuleCloneUserUpsertController } from "../../entrypoint/controller/competenceRule/competenceRuleCloneUserUpsert";
import { CompetenceRuleGetForRoleController } from "../../entrypoint/controller/competenceRule/competenceRuleforRole";
import { CompetenceRuleGetForUserController } from "../../entrypoint/controller/competenceRule/competenceRuleforUser";
import { CompetenceRuleGetController } from "../../entrypoint/controller/competenceRule/competenceRuleGet";
import { CompetenceRuleRegexGetController } from "../../entrypoint/controller/competenceRule/competenceRuleRegexGet";
import { CompetenceRuleRegexUpsertController } from "../../entrypoint/controller/competenceRule/competenceRuleRegexUpsert";
import { CompetenceRuleRoleUpsertController } from "../../entrypoint/controller/competenceRule/competenceRuleRoleUpsert";
import { CompetenceRuleUpsertController } from "../../entrypoint/controller/competenceRule/competenceRuleUpsert";
import { CompetenceRuleUseController } from "../../entrypoint/controller/competenceRule/competenceRuleUse";
import { CompetenceRuleUserUpsertController } from "../../entrypoint/controller/competenceRule/competenceRuleUserUpsert";
import { ConfigGetController } from "../../entrypoint/controller/config/configGet";
import { ConfigGetPasswordValidationsController } from "../../entrypoint/controller/config/configGetPasswordValidations";
import { ConfigPostController } from "../../entrypoint/controller/config/configPost";
import { ConnectionsDestroyUsedConnectionController } from "../../entrypoint/controller/connections/connectionsDestroyUsedConnection";
import { ConnectionsGetUsedConnectionsController } from "../../entrypoint/controller/connections/connectionsGetUsedConnections";
import { ConsoleCommandController } from "../../entrypoint/controller/console/consoleCommand";
import { CronCloneController } from "../../entrypoint/controller/cron/cronClone";
import { CronDeleteController } from "../../entrypoint/controller/cron/cronDelete";
import { CronGetController } from "../../entrypoint/controller/cron/cronGet";
import { CronRestartCronController } from "../../entrypoint/controller/cron/cronRestartCron";
import { CronRunController } from "../../entrypoint/controller/cron/cronRun";
import { CronSetDefaultController } from "../../entrypoint/controller/cron/cronSetDefault";
import { CronUpsertController } from "../../entrypoint/controller/cron/cronUpsert";
import { CronRunsHistoryGetCronRunController } from "../../entrypoint/controller/cronRunsHistory/cronRunsHistoryGetCronRun";
import { CronRunsHistoryGetCronRunsController } from "../../entrypoint/controller/cronRunsHistory/cronRunsHistoryGetCronRuns";
import { CustomViewsCloneController } from "../../entrypoint/controller/customViews/customViewsClone";
import { CustomViewsGetController } from "../../entrypoint/controller/customViews/customViewsGet";
import { CustomViewsGetListController } from "../../entrypoint/controller/customViews/customViewsGetList";
import { CustomViewsGetListWithTasksController } from "../../entrypoint/controller/customViews/customViewsGetListWithTasks";
import { CustomViewsMineController } from "../../entrypoint/controller/customViews/customViewsMine";
import { CustomViewsPostController } from "../../entrypoint/controller/customViews/customViewsPost";
import { CustomViewsRemoveController } from "../../entrypoint/controller/customViews/customViewsRemove";
import { CustomViewsMailGetController } from "../../entrypoint/controller/customViewsMail/customViewsMailGet";
import { CustomViewsMailPostController } from "../../entrypoint/controller/customViewsMail/customViewsMailPost";
import { CustomViewsMailRemoveController } from "../../entrypoint/controller/customViewsMail/customViewsMailRemove";
import { DynamicTableAddColController } from "../../entrypoint/controller/dynamicTable/dynamicTableAddCol";
import { DynamicTableAddRowController } from "../../entrypoint/controller/dynamicTable/dynamicTableAddRow";
import { DynamicTableChangeValuesController } from "../../entrypoint/controller/dynamicTable/dynamicTableChangeValues";
import { DynamicTableCloneController } from "../../entrypoint/controller/dynamicTable/dynamicTableClone";
import { DynamicTableCreateController } from "../../entrypoint/controller/dynamicTable/dynamicTableCreate";
import { DynamicTableEditController } from "../../entrypoint/controller/dynamicTable/dynamicTableEdit";
import { DynamicTableGetController } from "../../entrypoint/controller/dynamicTable/dynamicTableGet";
import { DynamicTableImportCsvController } from "../../entrypoint/controller/dynamicTable/dynamicTableImportCsv";
import { DynamicTableMoveRowController } from "../../entrypoint/controller/dynamicTable/dynamicTableMoveRow";
import { DynamicTableRemoveColController } from "../../entrypoint/controller/dynamicTable/dynamicTableRemoveCol";
import { DynamicTableRemoveRowController } from "../../entrypoint/controller/dynamicTable/dynamicTableRemoveRow";
import { DynamicTableRemoveTableController } from "../../entrypoint/controller/dynamicTable/dynamicTableRemoveTable";
import { DynamicTableRenameColController } from "../../entrypoint/controller/dynamicTable/dynamicTableRenameCol";
import { DynamicTableValuesController } from "../../entrypoint/controller/dynamicTable/dynamicTableValues";
import { DynamicTableValuesGetColumnController } from "../../entrypoint/controller/dynamicTableValues/dynamicTableValuesGetColumn";
import { EventDefinitionGetEventsController } from "../../entrypoint/controller/eventDefinition/eventDefinitionGetEvents";
import { EventDefinitionGetReturnTEventListController } from "../../entrypoint/controller/eventDefinition/eventDefinitionGetReturnTEventList";
import { EventDefinitionPostController } from "../../entrypoint/controller/eventDefinition/eventDefinitionPost";
import { EventDefinitionRemoveController } from "../../entrypoint/controller/eventDefinition/eventDefinitionRemove";
import { JsScriptCalculationsController } from "../../entrypoint/controller/jsScript/jsScriptCalculations";
import { JsScriptExportController } from "../../entrypoint/controller/jsScript/jsScriptExport";
import { JsScriptFrontendController } from "../../entrypoint/controller/jsScript/jsScriptFrontend";
import { JsScriptFrontendReactController } from "../../entrypoint/controller/jsScript/jsScriptFrontendReact";
import { JsScriptImportController } from "../../entrypoint/controller/jsScript/jsScriptImport";
import { JsScriptPostController } from "../../entrypoint/controller/jsScript/jsScriptPost";
import { JsScriptRemoveController } from "../../entrypoint/controller/jsScript/jsScriptRemove";
import { LogDownloadDetailController } from "../../entrypoint/controller/log/logDownloadDetail";
import { LogGetCalculationLogsController } from "../../entrypoint/controller/log/logGetCalculationLogs";
import { LogGetCategoriesController } from "../../entrypoint/controller/log/logGetCategories";
import { LogGetDetailController } from "../../entrypoint/controller/log/logGetDetail";
import { LogGetLogsController } from "../../entrypoint/controller/log/logGetLogs";
import { LogPostController } from "../../entrypoint/controller/log/logPost";
import { PlanGetAllController } from "../../entrypoint/controller/instancePlan/planGetAll";
import { PlanPostController } from "../../entrypoint/controller/instancePlan/planPost";
import { PlanDeleteController } from "../../entrypoint/controller/instancePlan/planDelete";
import { PlanProcessLogGetController } from "../../entrypoint/controller/planProcessLog/planProcessLogGet";
import { RoleCloneController } from "../../entrypoint/controller/role/roleClone";
import { RoleGetController } from "../../entrypoint/controller/role/roleGet";
import { RoleGetByUserController } from "../../entrypoint/controller/role/roleGetByUser";
import { RoleGetStructureController } from "../../entrypoint/controller/role/roleGetStructure";
import { RoleRemoveController } from "../../entrypoint/controller/role/roleRemove";
import { RoleUpsertController } from "../../entrypoint/controller/role/roleUpsert";
import { UserActiveController } from "../../entrypoint/controller/user/userActive";
import { UserAllController } from "../../entrypoint/controller/user/userAll";
import { UserDeletedController } from "../../entrypoint/controller/user/userDeleted";
import { UserGetChairsController } from "../../entrypoint/controller/user/userGetChairs";
import { UserGetPasswordStatusController } from "../../entrypoint/controller/user/userGetPasswordStatus";
import { UserGetUnassingedByOrgStrController } from "../../entrypoint/controller/user/userGetUnassingedByOrgstr";
import { UserGetUnassingedByRoleController } from "../../entrypoint/controller/user/userGetUnassingedByRole";
import { UserGetUsersForExportController } from "../../entrypoint/controller/user/userGetUsersForExport";
import { UserGetUsersWithOrgStrController } from "../../entrypoint/controller/user/userGetUsersWithOrgStr";
import { UserLockedController } from "../../entrypoint/controller/user/userLocked";
import { UserLoggedUserController } from "../../entrypoint/controller/user/userLoggedUser";
import { UserNonDeletedController } from "../../entrypoint/controller/user/userNonDeleted";
import { UserPossibleUsersController } from "../../entrypoint/controller/user/userPossibleUsers";
import { UserRemoveController } from "../../entrypoint/controller/user/userRemove";
import { UserRemoveManagerController } from "../../entrypoint/controller/user/userRemoveManager";
import { UserSetExpiredPasswordController } from "../../entrypoint/controller/user/userSetExpiredPassword";
import { UserSetManagerController } from "../../entrypoint/controller/user/userSetManager";
import { UserSetPasswordController } from "../../entrypoint/controller/user/userSetPassword";
import { UserSetUserViceController } from "../../entrypoint/controller/user/userSetUserVice";
import { UserSyncController } from "../../entrypoint/controller/user/userSync";
import { UserUpdateUserLockController } from "../../entrypoint/controller/user/userUpdateUserLock";
import { UserUpsertController } from "../../entrypoint/controller/user/userUpsert";
import { UserParametersGetController } from "../../entrypoint/controller/userParameters/userParametersGet";
import { UserParametersGetCsvFileController } from "../../entrypoint/controller/userParameters/userParametersGetCsvFile";
import { UserParametersGetCsvFileListController } from "../../entrypoint/controller/userParameters/userParametersGetCsvFileList";
import { UserParametersPostController } from "../../entrypoint/controller/userParameters/userParametersPost";
import { UserParametersUploadCsvFileController } from "../../entrypoint/controller/userParameters/userParametersUploadCsvFile";
import { UserDeletePhotoController } from "../../entrypoint/controller/userPhoto/userDeletePhoto";
import { UserDownloadMinePhotoController } from "../../entrypoint/controller/userPhoto/userDownloadMinePhoto";
import { UserDownloadPhotoByNameController } from "../../entrypoint/controller/userPhoto/userDownloadPhotoByName";
import { UserDownloadPhotoByUserIdController } from "../../entrypoint/controller/userPhoto/userDownloadPhotoByUserId";
import { UserUploadPhotoController } from "../../entrypoint/controller/userPhoto/userUploadPhoto";
import { EntityImportOrgStrImportController } from "../../entrypoint/controller/entityImport/entityImportOrgstrImport";
import { EntityImportOrgStrImportPreviewController } from "../../entrypoint/controller/entityImport/entityImportOrgstrImportPreview";
import { EntityImportRoleImportController } from "../../entrypoint/controller/entityImport/entityImportRoleImport";
import { EntityImportRoleImportPreviewController } from "../../entrypoint/controller/entityImport/entityImportRoleImportPreview";
import { EntityImportUserImportController } from "../../entrypoint/controller/entityImport/entityImportUserImport";
import { EntityImportUserImportPreviewController } from "../../entrypoint/controller/entityImport/entityImportUserImportPreview";
import { PerformanceLogsDetailListController } from "../../entrypoint/controller/performanceLogs/performanceLogsDetailList";
import { PerformanceLogsGraphListController } from "../../entrypoint/controller/performanceLogs/performanceLogsGraphList";
import { PdfToPdfController } from "../../entrypoint/controller/pdf/pdfToPdf";
import { InstanceMassTasksMassFinishController } from "../../entrypoint/controller/instanceTask/instanceMassTasksMassFinish";
import { InstanceProcessesNotesAddController } from "../../entrypoint/controller/instanceProcess/notes/instanceProcessesNotesAdd";
import { InstanceProcessesNotesEditController } from "../../entrypoint/controller/instanceProcess/notes/instanceProcessesNotesEdit";
import { InstanceProcessesNotesSystemController } from "../../entrypoint/controller/instanceProcess/notes/instanceProcessesNotesSystem";
import { InstanceProcessesNotesUsersController } from "../../entrypoint/controller/instanceProcess/notes/instanceProcessesNotesUsers";
import { InstanceProcessesNotesUsersArchivedController } from "../../entrypoint/controller/instanceProcess/notes/instanceProcessesNotesUsersArchived";
import { InstanceProcessesNoteToggleVisibilityController } from "../../entrypoint/controller/instanceProcess/notes/instanceProcessesNoteToggleVisibility";
import { InstanceProcessesVariablesGetController } from "../../entrypoint/controller/instanceProcess/variables/instanceProcessesVariablesGet";
import { InstanceProcessesVariablesGetArchivedController } from "../../entrypoint/controller/instanceProcess/variables/instanceProcessesVariablesGetArchived";
import { InstanceProcessesVariablesGetSharedController } from "../../entrypoint/controller/instanceProcess/variables/instanceProcessesVariablesGetShared";
import { InstanceProcessesVariablesUpdateController } from "../../entrypoint/controller/instanceProcess/variables/instanceProcessesVariablesUpdate";
import { InstanceProcessesVariablesUpdateByNameController } from "../../entrypoint/controller/instanceProcess/variables/instanceProcessesVariablesUpdateByName";
import { ReportGraphCloneController } from "../../entrypoint/controller/reportGraph/reportGraphClone";
import { ReportGraphGetController } from "../../entrypoint/controller/reportGraph/reportGraphGet";
import { ReportGraphGetChartController } from "../../entrypoint/controller/reportGraph/reportGraphGetChart";
import { ReportGraphGetChartPreviewController } from "../../entrypoint/controller/reportGraph/reportGraphGetChartPreview";
import { ReportGraphGetForGraphController } from "../../entrypoint/controller/reportGraph/reportGraphGetForGraph";
import { ReportGraphGetForTableController } from "../../entrypoint/controller/reportGraph/reportGraphGetForTable";
import { ReportGraphMineController } from "../../entrypoint/controller/reportGraph/reportGraphMine";
import { ReportGraphPostController } from "../../entrypoint/controller/reportGraph/reportGraphPost";
import { ReportGraphRemoveController } from "../../entrypoint/controller/reportGraph/reportGraphRemove";
import { ReportGraphGlobalFilterGetForFilteringController } from "../../entrypoint/controller/reportGraph/filter/reportGraphGlobalFilterGetForFiltering";
import { ReportGraphGlobalFilterGetForFormController } from "../../entrypoint/controller/reportGraph/filter/reportGraphGlobalFilterGetForForm";
import { ReportGraphGlobalFilterPostController } from "../../entrypoint/controller/reportGraph/filter/reportGraphGlobalFilterPost";
import { HrChangeAgendaController } from "../../entrypoint/controller/hr/hrChangeAgenda";
import { HrChangeUserBusinessController } from "../../entrypoint/controller/hr/hrChangeUserBusiness";
import { HrChangeUserBusinessLockedController } from "../../entrypoint/controller/hr/hrChangeUserBusinessLocked";
import { HrChangeUserFullController } from "../../entrypoint/controller/hr/hrChangeUserFull";
import { HrGetActiveController } from "../../entrypoint/controller/hr/hrGetActive";
import { HrGetAllController } from "../../entrypoint/controller/hr/hrGetAll";
import { HrGetBusinessController } from "../../entrypoint/controller/hr/hrGetBusiness";
import { HrGetBusinessLockedController } from "../../entrypoint/controller/hr/hrGetBusinessLocked";
import { HrGetFullController } from "../../entrypoint/controller/hr/hrGetFull";
import { HrGetInactiveController } from "../../entrypoint/controller/hr/hrGetInactive";
import { HrGetLogsController } from "../../entrypoint/controller/hr/hrGetLogs";
import { DmsAccessLogGetController } from "../../entrypoint/controller/dms/accessLog/dmsAccessLogGet";
import { DmsFileMetadataAllController } from "../../entrypoint/controller/dms/fileMetadata/dmsFileMetadataAll";
import { DmsFileMetadataAllArchivedController } from "../../entrypoint/controller/dms/fileMetadata/dmsFileMetadataAllArchived";
import { DmsFileTagPostController } from "../../entrypoint/controller/dms/fileTag/dmsFileTagPost";
import { DmsFileRevisionGetController } from "../../entrypoint/controller/dms/fileRevision/dmsFileRevisionGet";
import { DmsFileRevisionGetArchivedController } from "../../entrypoint/controller/dms/fileRevision/dmsFileRevisionGetArchived";
import { DmsFolderDelController } from "../../entrypoint/controller/dms/folder/dmsFolderDel";
import { DmsFolderGetController } from "../../entrypoint/controller/dms/folder/dmsFolderGet";
import { DmsFolderPostController } from "../../entrypoint/controller/dms/folder/dmsFolderPost";
import { DmsFolderTreeController } from "../../entrypoint/controller/dms/folder/dmsFolderTree";
import { DmsLogicalTagGetController } from "../../entrypoint/controller/dms/logicalTag/dmsLogicalTagGet";
import { DmsLogicalTagPostController } from "../../entrypoint/controller/dms/logicalTag/dmsLogicalTagPost";
import { DmsTagGetController } from "../../entrypoint/controller/dms/tag/dmsTagGet";
import { DmsTagPostController } from "../../entrypoint/controller/dms/tag/dmsTagPost";
import { DmsTagRemoveController } from "../../entrypoint/controller/dms/tag/dmsTagRemove";
import { DmsAllController } from "../../entrypoint/controller/dms/dms/dmsAll";
import { DmsCurrentController } from "../../entrypoint/controller/dms/dms/dmsCurrent";
import { DmsDeletedController } from "../../entrypoint/controller/dms/dms/dmsDeleted";
import { DmsDownloadController } from "../../entrypoint/controller/dms/dms/dmsDownload";
import { DmsDownloadArchivedController } from "../../entrypoint/controller/dms/dms/dmsDownloadArchived";
import { DmsDownloadBulkController } from "../../entrypoint/controller/dms/dms/dmsDownloadBulk";
import { DmsDownloadMultipleLegacyController } from "../../entrypoint/controller/dms/dms/dmsDownloadMultipleLegacy";
import { DmsFileController } from "../../entrypoint/controller/dms/dms/dmsFile";
import { DmsForProcessController } from "../../entrypoint/controller/dms/dms/dmsForProcess";
import { DmsForProcessArchivedController } from "../../entrypoint/controller/dms/dms/dmsForProcessArchived";
import { DmsForTaskController } from "../../entrypoint/controller/dms/dms/dmsForTask";
import { DmsForTaskArchivedController } from "../../entrypoint/controller/dms/dms/dmsForTaskArchived";
import { DmsGetIndexedCountController } from "../../entrypoint/controller/dms/dms/dmsGetIndexedCount";
import { DmsIndexDocumentController } from "../../entrypoint/controller/dms/dms/dmsIndexDocument";
import { DmsRemoveBulkController } from "../../entrypoint/controller/dms/dms/dmsRemoveBulk";
import { DmsRemovePermanentlyController } from "../../entrypoint/controller/dms/dms/dmsRemovePremanently";
import { DmsRemoveSingleController } from "../../entrypoint/controller/dms/dms/dmsRemoveSingle";
import { DmsRestoreController } from "../../entrypoint/controller/dms/dms/dmsRestore";
import { DmsSearchController } from "../../entrypoint/controller/dms/dms/dmsSearch";
import { DmsSimpleSearchController } from "../../entrypoint/controller/dms/dms/dmsSimpleSearch";
import { DmsUploadFileController } from "../../entrypoint/controller/dms/dms/dmsUploadFile";
import { DmsUploadPrivateFileController } from "../../entrypoint/controller/dms/dms/dmsUploadPrivateFile";
import { RoleUsersActiveController } from "../../entrypoint/controller/roleUsers/roleUsersActive";
import { RoleUsersDeletedController } from "../../entrypoint/controller/roleUsers/roleUsersDeleted";
import { RuleDefinitionCreateController } from "../../entrypoint/controller/rule/definition/ruleDefinitionCreate";
import { RuleDefinitionGetRulesController } from "../../entrypoint/controller/rule/definition/ruleDefinitionGetRules";
import { RuleDefinitionRemoveController } from "../../entrypoint/controller/rule/definition/ruleDefinitionRemove";
import { Saml2CallbackController } from "../../entrypoint/controller/authorization/callback/saml2Callback";
import { OrganizationTreeGetController } from "../../entrypoint/controller/organization/tree/organizationTreeGet";
import { OrganizationUsersActiveController } from "../../entrypoint/controller/organization/users/organizationUsersActive";
import { OrganizationUsersDeletedController } from "../../entrypoint/controller/organization/users/organizationUsersDeleted";
import { OrganizationStructureTreeGetController } from "../../entrypoint/controller/organization/structureTree/organizationStructureTreeGet";
import { OrganizationStructureTreeGetFlatController } from "../../entrypoint/controller/organization/structureTree/organizationStructureTreeGetFlat";
import { OrganizationStructureActiveController } from "../../entrypoint/controller/organization/structure/organizationStructureActive";
import { OrganizationStructureAllController } from "../../entrypoint/controller/organization/structure/organizationStructureAll";
import { OrganizationStructureCreateController } from "../../entrypoint/controller/organization/structure/organizationStructureCreate";
import { OrganizationStructureGetManagedUsersController } from "../../entrypoint/controller/organization/structure/organizationStructureGetManagedUsers";
import { OrganizationStructureRemoveController } from "../../entrypoint/controller/organization/structure/organizationStructureRemove";
import { OrganizationGetColorsController } from "../../entrypoint/controller/organization/organization/organizationGetColors";
import { OrganizationGetMailColorsController } from "../../entrypoint/controller/organization/organization/organizationGetMailColors";
import { MaintenancePostController } from "../../entrypoint/controller/maintenance/maintenancePost";
import { MaintenanceGetController } from "../../entrypoint/controller/maintenance/maintenanceGet";
import { InstanceTaskGetAllController } from "../../entrypoint/controller/instanceTask/instanceTaskGetAll";
import { InstanceTaskGetActiveController } from "../../entrypoint/controller/instanceTask/instanceTaskGetActive";
import { InstanceTaskMineAndToPullController } from "../../entrypoint/controller/instanceTask/instanceTaskMineAndToPull";
import { InstanceTaskMineController } from "../../entrypoint/controller/instanceTask/instanceTaskMine";
import { InstanceTaskForProcessController } from "../../entrypoint/controller/instanceTask/instanceTaskForProcess";
import { InstanceTaskFinishController } from "../../entrypoint/controller/instanceTask/instanceTaskFinish";
import { InstanceTaskInfoController } from "../../entrypoint/controller/instanceTask/instanceTaskInfo";
import { InstanceTaskAssignToMeController } from "../../entrypoint/controller/instanceTask/instanceTaskAssignToMe";
import { InstanceTaskRejectController } from "../../entrypoint/controller/instanceTask/instanceTaskReject";
import { InstanceTaskDelegateController } from "../../entrypoint/controller/instanceTask/instanceTaskDelegate";
import { InstanceTaskGetScheduledTasksController } from "../../entrypoint/controller/instanceTask/instanceTaskGetScheduledTasks";
import { InstanceTaskGetFailedScheduledTasksController } from "../../entrypoint/controller/instanceTask/instanceTaskGetFailedScheduledTasks";
import { InstanceTaskRescheduleTasksController } from "../../entrypoint/controller/instanceTask/instanceTaskRescheduleTasks";
import { InstanceTaskToPullController } from "../../entrypoint/controller/instanceTask/instanceTaskToPull";
import { InstanceTaskBySolversController } from "../../entrypoint/controller/instanceTask/instanceTaskBySolvers";
import { InstanceTaskBulkCompletableController } from "../../entrypoint/controller/instanceTask/instanceTaskBulkCopletable";
import { InstanceTaskPullToMeController } from "../../entrypoint/controller/instanceTask/instanceTaskPullToMe";
import { InstanceTaskSetTaskDues } from "../../entrypoint/controller/instanceTask/instanceTaskSetTaskDues";
import { InstanceTaskRefreshCalculation } from "../../entrypoint/controller/instanceTask/instanceTaskRefreshCalculation";
import { InstanceTaskAddITaskController } from "../../entrypoint/controller/instanceTask/instanceTaskAddITask";
import { InstanceTaskChangeSolverController } from "../../entrypoint/controller/instanceTask/instanceTaskChangeSolver";
import { InstanceTaskResendEmailNotificationController } from "../../entrypoint/controller/instanceTask/instanceTaskResendEmailNotification";
import { InstanceTaskGetTaskSectionsController } from "../../entrypoint/controller/instanceTask/instanceTaskGetTaskSections";
import { InstanceTaskGetTaskSectionsArchivedController } from "../../entrypoint/controller/instanceTask/instanceTaskGetTaskSectionsArchived";
import { InstanceTaskAssignToController } from "../../entrypoint/controller/instanceTask/instanceTaskAssignTo";
import { InstanceGetAllArchivedController } from "../../entrypoint/controller/instanceTask/instanceGetAllArchived";
import { InstanceTaskDoneController } from "../../entrypoint/controller/instanceTask/instanceTaskDone";
import { InstanceTaskProgressActualController } from "../../entrypoint/controller/instanceTask/instanceTaskProgressActual";
import { ProcessEventGetController } from "../../entrypoint/controller/process/event/processEventGet";
import { ProcessEventHandEventsListController } from "../../entrypoint/controller/process/event/processEventHandEventsList";
import { ProcessEventHandInvokeController } from "../../entrypoint/controller/process/event/processEventHandInvoke";
import { ProcessInfoHandEventMapController } from "../../entrypoint/controller/process/info/processInfoHandEventMap";
import { ProcessInfoHandEventMapArchivedController } from "../../entrypoint/controller/process/info/processInfoHandEventMapArchived";
import { ProcessInfoProcessRightsMapController } from "../../entrypoint/controller/process/info/processInfoProcessRightsMap";
import { ProcessInfoProcessRightsMapArchivedController } from "../../entrypoint/controller/process/info/processInfoProcessRightsMapArchived";
import { ProcessPrintGetController } from "../../entrypoint/controller/process/print/processPrintGet";
import { ProcessPrintGetArchivedController } from "../../entrypoint/controller/process/print/processPrintGetArchived";
import { ProcessPrintGetConvertedPrintController } from "../../entrypoint/controller/process/print/processPrintGetConvertedPrint";
import { ProcessPrintPrintToFileController } from "../../entrypoint/controller/process/print/processPrintPrintToFile";
import { EventsMappingController } from "../../entrypoint/controller/events/eventsMapping";
import { EventsGetEventButtons } from "../../entrypoint/controller/events/eventsGetEventButtons";
import { RegisteredMobileDeviceDeleteController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceDelete";
import { RegisteredMobileDeviceGetDomainWhitelistController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceGetDomainWhitelist";
import { RegisteredMobileDeviceGetFirebaseProjectController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceGetFirebaseProject";
import { RegisteredMobileDeviceListController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceList";
import { RegisteredMobileDeviceListAllController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceListAll";
import { RegisteredMobileDeviceMuteNotificationController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceMuteNotification";
import { RegisteredMobileDeviceRegisterController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceRegister";
import { RegisteredMobileDeviceReloadQrController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceReloadQr";
import { RegisteredMobileDeviceRenameController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceRename";
import { RegisteredMobileDeviceResetFCMBadgeCounterController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceResetFCMBadgeCounter";
import { RegisteredMobileDeviceSetDeviceTokenController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceSetDeviceToken";
import { RegisteredMobileDeviceSetTokenController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceSetToken";
import { RegisteredMobileDeviceUnregisterController } from "../../entrypoint/controller/registeredMobileDevice/registeredMobileDeviceUnregister";
import { SequenceGetController } from "../../entrypoint/controller/sequence/sequenceGet";
import { SequencePostController } from "../../entrypoint/controller/sequence/sequencePost";
import { ExportExcel } from "../../entrypoint/controller/export/exportExcel";
import { ExportCsv } from "../../entrypoint/controller/export/exportCsv";
import { InstanceProcessesHistoryGetController } from "../../entrypoint/controller/instanceProcess/history/instanceProcessesHistoryGet";
import { InstanceProcessesHistoryGetArchivedController } from "../../entrypoint/controller/instanceProcess/history/instanceProcessesHistoryGetArchived";
import { InstanceProcessesHistoryProgressHistoryController } from "../../entrypoint/controller/instanceProcess/history/instanceProcessesHistoryProgressHistory";
import { InstanceProcessActivateController } from "../../entrypoint/controller/instanceProcess/instanceProcessActivate";
import { InstanceProcessChangeCaseOwnerController } from "../../entrypoint/controller/instanceProcess/instanceProcessChangeCaseOwner";
import { InstanceProcessEraseController } from "../../entrypoint/controller/instanceProcess/instanceProcessErase";
import { InstanceProcessErroredController } from "../../entrypoint/controller/instanceProcess/instanceProcessErrored";
import { InstanceProcessGetController } from "../../entrypoint/controller/instanceProcess/instanceProcessGet";
import { InstanceProcessGetAllUsedController } from "../../entrypoint/controller/instanceProcess/instanceProcessGetAllUsed";
import { InstanceProcessGetNotUsedController } from "../../entrypoint/controller/instanceProcess/instanceProcessGetNotUsed";
import { InstanceProcessGetSubprocessController } from "../../entrypoint/controller/instanceProcess/instanceProcessGetSubprocess";
import { InstanceProcessMineController } from "../../entrypoint/controller/instanceProcess/instanceProcessMine";
import { InstanceProcessRemoveController } from "../../entrypoint/controller/instanceProcess/instanceProcessRemove";
import { InstanceProcessShreddedController } from "../../entrypoint/controller/instanceProcess/instanceProcessShredded";
import { InstanceProcessSuspendController } from "../../entrypoint/controller/instanceProcess/instanceProcessSuspend";
import { InstanceTaskGetComputedValuesController } from "../../entrypoint/controller/instanceTask/instanceTaskGetComputedValues";
import { InstanceTaskGetToSolveController } from "../../entrypoint/controller/instanceTask/instanceTaskGetToSolve";
import { ExportTemplate } from "../../entrypoint/controller/export/exportTemplate";
import { ImportTemplate } from "../../entrypoint/controller/import/importTemplate";
import { ExportVariablesUsageController } from "../../entrypoint/controller/export/exportVariablesUsage";
import { ImportVariablesUsageController } from "../../entrypoint/controller/import/importVariablesUsage";
import { DynamicTableRemoveTableBulkController } from "../../entrypoint/controller/dynamicTable/dynamicTableRemoveTableBulk";
import { ProxyGetController } from "../../entrypoint/controller/proxy/proxyGet";
import { QueueGetJobCountsController } from "../../entrypoint/controller/queue/queueGetJobCounts";
import { QueueGetJobsController } from "../../entrypoint/controller/queue/queueGetJobs";
import { QueueGetWorkersController } from "../../entrypoint/controller/queue/queueGetWorkers";
import { QueueJobMoveToCompletedController } from "../../entrypoint/controller/queue/queueJobMoveToCompleted";
import { QueueJobMoveToDelayedController } from "../../entrypoint/controller/queue/queueJobMoveToDelayed";
import { QueueJobMoveToFailedController } from "../../entrypoint/controller/queue/queueJobMoveToFailed";
import { QueueJobRemoveController } from "../../entrypoint/controller/queue/queueJobRemove";
import { QueueScaleWorkerConcurrencyController } from "../../entrypoint/controller/queue/queueScaleWorkerConcurrency";
import { QueueWorkerPauseController } from "../../entrypoint/controller/queue/queueWorkerPause";
import { QueueWorkerResumeController } from "../../entrypoint/controller/queue/queueWorkerResume";
import { GetLicensesController } from "../../entrypoint/controller/license/getLicenses";
import { StoreLicenseController } from "../../entrypoint/controller/license/storeLicense";
import { GetLicenseResultController } from "../../entrypoint/controller/license/getLicenseResult";
import { DynamicTableColsGetController } from "../../entrypoint/controller/dynamicTable/dynamicTableColsGet";
import { DynamicListGetController } from "../../entrypoint/controller/dynamicList/dynamicListGet";
import { DynamicListListController } from "../../entrypoint/controller/dynamicList/dynamicListList";
import { GuidesGetController } from "../../entrypoint/controller/guides/guidesGet";
import { GuidesPostController } from "../../entrypoint/controller/guides/guidesPost";
import { HeaderCloneController } from "../../entrypoint/controller/header/headerClone";
import { HeaderCreateController } from "../../entrypoint/controller/header/headerCreate";
import { HeaderGetForTProcessController } from "../../entrypoint/controller/header/headerGetForTProcess";
import { HeaderGetListController } from "../../entrypoint/controller/header/headerGetList";
import { HeaderRemoveController } from "../../entrypoint/controller/header/headerRemove";
import { InstanceProcessResumeController } from "../../entrypoint/controller/instanceProcess/instanceProcessResume";
import { MailQAllController } from "../../entrypoint/controller/mailQ/mailQAll";
import { MailQDetailController } from "../../entrypoint/controller/mailQ/mailQDetail";
import { MailQUpdateStatusController } from "../../entrypoint/controller/mailQ/mailQUpdateStatus";
import { KerberosCallbackController } from "../../entrypoint/controller/authorization/callback/kerberosCallback";
import { TemplatePrintGetController } from "../../entrypoint/controller/template/print/templatePrintGet";
import { TemplatePrintCreateController } from "../../entrypoint/controller/template/print/templatePrintCreate";
import { TemplatePrintRemoveController } from "../../entrypoint/controller/template/print/templatePrintRemove";
import { TemplateTaskLinkCreateController } from "../../entrypoint/controller/template/taskLink/templateTaskLinkCreate";
import { TemplateTaskLinkAllController } from "../../entrypoint/controller/template/taskLink/templateTaskLinkAll";
import { TemplateTaskLinkRemoveController } from "../../entrypoint/controller/template/taskLink/templateTaskLinkRemove";
import { TemplateTaskLinkForProcessController } from "../../entrypoint/controller/template/taskLink/templateTaskLinkForProcess";
import { TemplateVariablesCreateController } from "../../entrypoint/controller/template/variables/templateVariablesCreate";
import { TemplateVariablesGetController } from "../../entrypoint/controller/template/variables/templateVariablesGet";
import { TemplateVariablesRemoveController } from "../../entrypoint/controller/template/variables/templateVariablesRemove";
import { TemplateVariablesUsageForProcessController } from "../../entrypoint/controller/template/variablesUsage/templateVariablesUsageForProcess";
import { TemplateVariablesUsagePostProcessController } from "../../entrypoint/controller/template/variablesUsage/templateVariablesUsagePostProcess";
import { TemplateTaskCalculationDocsController } from "../../entrypoint/controller/template/taskCalculation/templateTaskCalculationDocs";
import { TemplateSectionsRemoveController } from "../../entrypoint/controller/template/sections/templateSectionsRemove";
import { TemplateSectionsCreateController } from "../../entrypoint/controller/template/sections/templateSectionsCreate";
import { TemplateSectionsForProcessController } from "../../entrypoint/controller/template/sections/templateSectionsForProcess";
import { TemplateSectionsGetController } from "../../entrypoint/controller/template/sections/templateSectionsGet";
import { TemplateSectionsGetWithTasksController } from "../../entrypoint/controller/template/sections/templateSectionsGetWithTasks";
import { TemplateProcessesTasksGetController } from "../../entrypoint/controller/template/processesTasks/templateProcessesTasksGet";
import { TemplateTasksGetController } from "../../entrypoint/controller/template/tasks/templateTasksGet";
import { TemplateTasksCreateController } from "../../entrypoint/controller/template/tasks/templateTasksCreate";
import { TemplateTasksRemoveController } from "../../entrypoint/controller/template/tasks/templateTasksRemove";
import { TemplateProcessShreddingGetController } from "../../entrypoint/controller/template/processShredding/templateProcessShreddingGet";
import { TemplateProcessShreddingStoreController } from "../../entrypoint/controller/template/processShredding/templateProcessShreddingStore";
import { TemplateProcessesAllController } from "../../entrypoint/controller/template/processes/templateProcessesAll";
import { TemplateProcessesCreateController } from "../../entrypoint/controller/template/processes/templateProcessesCreate";
import { TemplateProcessesDeletedController } from "../../entrypoint/controller/template/processes/templateProcessesDeleted";
import { TemplateProcessesGraphController } from "../../entrypoint/controller/template/processes/templateProcessesGraph";
import { TemplateProcessesHtmlController } from "../../entrypoint/controller/template/processes/templateProcessesHtml";
import { TemplateProcessesNewVersionController } from "../../entrypoint/controller/template/processes/templateProcessesNewVersion";
import { TemplateProcessesRemoveController } from "../../entrypoint/controller/template/processes/templateProcessesRemove";
import { TemplateProcessesToStartController } from "../../entrypoint/controller/template/processes/templateProcessesToStart";
import { TemplateProcessesVersionsController } from "../../entrypoint/controller/template/processes/templateProcessesVersions";
import { TemplateProcessesListController } from "../../entrypoint/controller/template/processes/templateProcessesList";
import { InstanceProcessesGraphController } from "../../entrypoint/controller/instanceProcess/instanceProcessesGraph";
import { ActivateTaskController } from "../../entrypoint/controller/serviceOperation/activateTask";
import { ArchiveProcessController } from "../../entrypoint/controller/serviceOperation/archiveProcess";
import { ArchiveProcessFromTaskTableController } from "../../entrypoint/controller/serviceOperation/archiveProcessFromTaskTable";
import { ArchiveProcessFromVariableTableController } from "../../entrypoint/controller/serviceOperation/archiveProcessFromVariableTable";
import { FinishTaskController } from "../../entrypoint/controller/serviceOperation/finishTask";
import { SetProcessStatusActiveController } from "../../entrypoint/controller/serviceOperation/setProcessStatusActive";
import { SetProcessStatusActiveFromTaskTableController } from "../../entrypoint/controller/serviceOperation/setProcessStatusActiveFromTaskTable";
import { SetProcessStatusActiveFromVariableTableController } from "../../entrypoint/controller/serviceOperation/setProcessStatusActiveFromVariableTable";
import { SetProcessStatusDoneFromVariableTableController } from "../../entrypoint/controller/serviceOperation/setProcessStatusDoneFromVariableTable";
import { ChangeProcessesController } from "../../entrypoint/controller/serviceOperation/changeProcesses";
import { ChangeProcessFromTaskTableController } from "../../entrypoint/controller/serviceOperation/changeProcessFromTaskTable";
import { ChangeProcessFromVariableTableController } from "../../entrypoint/controller/serviceOperation/changeProcessFromVariableTable";
import { ChangeRoleController } from "../../entrypoint/controller/serviceOperation/changeRole";
import { ChangeTasksController } from "../../entrypoint/controller/serviceOperation/changeTasks";
import { ChangeUserController } from "../../entrypoint/controller/serviceOperation/changeUser";
import { ChangeUserFromUserParameterTableController } from "../../entrypoint/controller/serviceOperation/changeUserFromUserParameterTable";
import { ChangeUserParameterController } from "../../entrypoint/controller/serviceOperation/changeUserParameter";
import { ChangeVariablesController } from "../../entrypoint/controller/serviceOperation/changeVariables";
import { DescribeOrganizationStructureController } from "../../entrypoint/controller/serviceOperation/describeOrganizationStructure";
import { DescribeProcessesListController } from "../../entrypoint/controller/serviceOperation/describeProcessesList";
import { DescribeRolesController } from "../../entrypoint/controller/serviceOperation/describeRoles";
import { DescribeTasksListController } from "../../entrypoint/controller/serviceOperation/describeTasksList";
import { DescribeUserParametersController } from "../../entrypoint/controller/serviceOperation/describeUserParameters";
import { DescribeUsersController } from "../../entrypoint/controller/serviceOperation/describeUsers";
import { DescribeVariablesController } from "../../entrypoint/controller/serviceOperation/describeVariables";
import { RolesListController } from "../../entrypoint/controller/serviceOperation/rolesList";
import { SetOrganizationStructureController } from "../../entrypoint/controller/serviceOperation/setOrganizationStructure";
import { TasksListController } from "../../entrypoint/controller/serviceOperation/tasksList";
import { UserParameterListController } from "../../entrypoint/controller/serviceOperation/userParameterList";
import { UsersListController } from "../../entrypoint/controller/serviceOperation/usersList";
import { VariablesListController } from "../../entrypoint/controller/serviceOperation/variablesList";
import { SetProcessStatusDoneController } from "../../entrypoint/controller/serviceOperation/setProcessStatusDone";
import { SetProcessStatusDoneFromTaskTableController } from "../../entrypoint/controller/serviceOperation/setProcessStatusDoneFromTaskTable";
import { ProcessesListController } from "../../entrypoint/controller/serviceOperation/processesList";
import { OrganizationStructureListController } from "../../entrypoint/controller/serviceOperation/organizationStructureList";
import { HrChangeOwnerForProcessesController } from "../../entrypoint/controller/hr/hrChangeOwnerForProcesses";
import { HrchangeOwnerForTasksController } from "../../entrypoint/controller/hr/hrChangeOwnerForTasks";
import { PostGetPostsController } from "../../entrypoint/controller/post/postGetPosts";
import { PostGetPostsForController } from "../../entrypoint/controller/post/postGetPostsFor";
import { PostGetTagsController } from "../../entrypoint/controller/post/postGetTags";
import { PostRemoveController } from "../../entrypoint/controller/post/postRemove";
import { PostTagRemoveController } from "../../entrypoint/controller/post/postTagRemove";
import { PostTagUpsertController } from "../../entrypoint/controller/post/postTagUpsert";
import { PostUpsertController } from "../../entrypoint/controller/post/postUpsert";
import { VariablesGetController } from "../../entrypoint/controller/variables/variablesGet";
import { XmlProcessImportAuditController } from "../../entrypoint/controller/xmlProcessImport/audit";
import { XmlProcessImportDownloadProcessedDmsController } from "../../entrypoint/controller/xmlProcessImport/downloadProcessedDms";
import { XmlProcessImportDownloadProcessedXmlController } from "../../entrypoint/controller/xmlProcessImport/downloadProcessedXml";
import { XmlProcessImportDownloadScheduledDmsController } from "../../entrypoint/controller/xmlProcessImport/downloadScheduledDms";
import { XmlProcessImportDownloadScheduledXmlController } from "../../entrypoint/controller/xmlProcessImport/downloadScheduledXml";
import { XmlProcessImportHistoryController } from "../../entrypoint/controller/xmlProcessImport/history";
import { XmlProcessImportProcessController } from "../../entrypoint/controller/xmlProcessImport/process";
import { XmlProcessImportRescheduleController } from "../../entrypoint/controller/xmlProcessImport/reschedule";
import { XmlProcessImportScheduledController } from "../../entrypoint/controller/xmlProcessImport/scheduled";
import { XmlProcessImportSetNoteController } from "../../entrypoint/controller/xmlProcessImport/setNote";
import { XmlProcessImportSkipController } from "../../entrypoint/controller/xmlProcessImport/skip";
import { InstanceTaskScheduledActivateController } from "../../entrypoint/controller/instanceTask/instanceTaskScheduledActivate";
import { ConfigRefreshController } from "../../entrypoint/controller/config/configRefresh";

export const initControllerContainer = async (
    pluginManager: PluginManager,
) => ({
    calculations: {
        calculationDefaulsGet: Container.get(CalculationsDefaultsGetController),
    },
    plugins: await pluginManager.getControllerPlugins(),
    role: {
        roleClone: Container.get(RoleCloneController),
        roleGet: Container.get(RoleGetController),
        roleGetByUser: Container.get(RoleGetByUserController),
        roleGetStructure: Container.get(RoleGetStructureController),
        roleRemove: Container.get(RoleRemoveController),
        roleUpsert: Container.get(RoleUpsertController),
    },
    users: {
        usersAll: Container.get(UserAllController),
        usersActive: Container.get(UserActiveController),
        usersLocked: Container.get(UserLockedController),
        usersDeleted: Container.get(UserDeletedController),
        usersNonDeleted: Container.get(UserNonDeletedController),
        usersLoggedUser: Container.get(UserLoggedUserController),
        usersUpsert: Container.get(UserUpsertController),
        usersRemoveManager: Container.get(UserRemoveManagerController),
        usersSetManager: Container.get(UserSetManagerController),
        usersSetPassword: Container.get(UserSetPasswordController),
        usersSetExpiredPassword: Container.get(
            UserSetExpiredPasswordController,
        ),
        usersGetPasswordStatus: Container.get(UserGetPasswordStatusController),
        usersSetUserVice: Container.get(UserSetUserViceController),
        usersRemove: Container.get(UserRemoveController),
        usersGetUsersWithOrgStr: Container.get(
            UserGetUsersWithOrgStrController,
        ),
        usersGetUsersForExport: Container.get(UserGetUsersForExportController),
        usersGetChairs: Container.get(UserGetChairsController),
        usersUpdateUserLock: Container.get(UserUpdateUserLockController),
        usersSync: Container.get(UserSyncController),
        usersPossibleUsers: Container.get(UserPossibleUsersController),
        usersUnassignedByRole: Container.get(UserGetUnassingedByRoleController),
        usersUnassignedByOrgStr: Container.get(
            UserGetUnassingedByOrgStrController,
        ),
    },
    userPhoto: {
        userUploadPhoto: Container.get(UserUploadPhotoController),
        userDownloadMinePhoto: Container.get(UserDownloadMinePhotoController),
        userDownloadPhotoByUserId: Container.get(
            UserDownloadPhotoByUserIdController,
        ),
        userDeletePhoto: Container.get(UserDeletePhotoController),
        userDownloadPhotoByName: Container.get(
            UserDownloadPhotoByNameController,
        ),
    },
    userParameters: {
        userParametersGet: Container.get(UserParametersGetController),
        userParametersPost: Container.get(UserParametersPostController),
        userParametersUploadCsvFile: Container.get(
            UserParametersUploadCsvFileController,
        ),
        userParametersGetCsvFile: Container.get(
            UserParametersGetCsvFileController,
        ),
        userParametersGetCsvFileList: Container.get(
            UserParametersGetCsvFileListController,
        ),
    },
    cron: {
        cronGet: Container.get(CronGetController),
        cronUpsert: Container.get(CronUpsertController),
        cronRestartCron: Container.get(CronRestartCronController),
        cronRun: Container.get(CronRunController),
        cronSetDefault: Container.get(CronSetDefaultController),
        cronClone: Container.get(CronCloneController),
        cronDelete: Container.get(CronDeleteController),
    },

    caseStatus: {
        get: Container.get(CaseStatusGetController),
        post: Container.get(CaseStatusPostController),
        delete: Container.get(CaseStatusDeleteController),
    },
    config: {
        get: Container.get(ConfigGetController),
        getPasswordValidations: Container.get(
            ConfigGetPasswordValidationsController,
        ),
        post: Container.get(ConfigPostController),
        refresh: Container.get(ConfigRefreshController),
    },
    authorization: {
        callback: {
            azureCallback: Container.get(AzureCallbackController),
            saml2Callback: Container.get(Saml2CallbackController),
            kerberosCallback: Container.get(KerberosCallbackController),
        },
        authorizationAuth: Container.get(AuthorizationAuthController),
        authorizationAuthByModule: Container.get(
            AuthorizationAuthByModuleController,
        ),
        authorizationAuthByTokenPair: Container.get(
            AuthorizationAuthByTokenPairController,
        ),
        authorizationDeleteAuthorization: Container.get(
            AuthorizationDeleteController,
        ),
        authorizationFilterSystemRoles: Container.get(
            AuthorizationAdminAuthController,
        ),
        authorizationGetAuthorizationConfig: Container.get(
            AuthorizationGetConfigController,
        ),
        authorizationGetAuthorizationList: Container.get(
            AuthorizationGetListController,
        ),
        authorizationLogout: Container.get(AuthorizationLogoutController),
        authorizationMobileAuth: Container.get(
            AuthorizationMobileAuthController,
        ),
        authorizationParseAccessToken: Container.get(
            AuthorizationParseAccessTokenController,
        ),
        authorizationSaveAuthorizations: Container.get(
            AuthorizationsSaveController,
        ),
        authorizationTestAuthorization: Container.get(
            AuthorizationTestController,
        ),
    },
    archivation: {
        getAll: Container.get(ArchivationGetAllController),
        getList: Container.get(ArchivationGetListController),
        processArchive: Container.get(ArchivationProcessArchiveController),
        processUnarchive: Container.get(ArchivationProcessUnarchiveController),
    },
    competences: {
        competenceGet: Container.get(CompetenceGetController),
        competenceRegexGet: Container.get(CompetenceRegexGetController),
        competenceRoleGet: Container.get(CompetenceRoleGetController),
        competenceUserGet: Container.get(CompetenceUserController),
        competenceUpsert: Container.get(CompetenceUpsertController),
        competenceRoleUpsert: Container.get(CompetenceRoleUpsertController),
        competenceUserUpsert: Container.get(CompetenceUserUpsertController),
        competenceRegexUpsert: Container.get(CompetenceRegexUpsertController),
        competenceCloneUserUpsert: Container.get(
            CompetenceCloneUserUpsertController,
        ),
    },
    competenceRules: {
        competenceRuleGet: Container.get(CompetenceRuleGetController),
        competenceRuleRegexGet: Container.get(CompetenceRuleRegexGetController),
        competenceRuleUse: Container.get(CompetenceRuleUseController),
        competenceRuleGetForRole: Container.get(
            CompetenceRuleGetForRoleController,
        ),
        competenceRuleGetForUser: Container.get(
            CompetenceRuleGetForUserController,
        ),
        competenceRuleUpsert: Container.get(CompetenceRuleUpsertController),
        competenceRuleRoleUpsert: Container.get(
            CompetenceRuleRoleUpsertController,
        ),
        competenceRuleUserUpsert: Container.get(
            CompetenceRuleUserUpsertController,
        ),
        competenceRuleRegexUpsert: Container.get(
            CompetenceRuleRegexUpsertController,
        ),
        competenceRuleCloneUserUpsert: Container.get(
            CompetenceRuleCloneUserUpsertController,
        ),
    },
    console: {
        consoleCommand: Container.get(ConsoleCommandController),
    },
    cronRunsHistory: {
        cronRunsHistoryGetCronRun: Container.get(
            CronRunsHistoryGetCronRunController,
        ),
        cronRunsHistoryGetCronRuns: Container.get(
            CronRunsHistoryGetCronRunsController,
        ),
    },
    connections: {
        connectionsGetUsedConnections: Container.get(
            ConnectionsGetUsedConnectionsController,
        ),
        connectionsDestroyUsedConnection: Container.get(
            ConnectionsDestroyUsedConnectionController,
        ),
    },
    instanceProcessesNotesController: {
        instanceProcessesNotesAdd: Container.get(
            InstanceProcessesNotesAddController,
        ),
        instanceProcessesNotesEdit: Container.get(
            InstanceProcessesNotesEditController,
        ),
        instanceProcessesNotesSystem: Container.get(
            InstanceProcessesNotesSystemController,
        ),
        instanceProcessesNotesUsers: Container.get(
            InstanceProcessesNotesUsersController,
        ),
        instanceProcessesNotesUsersArchived: Container.get(
            InstanceProcessesNotesUsersArchivedController,
        ),
        instanceProcessesNoteToggleVisibility: Container.get(
            InstanceProcessesNoteToggleVisibilityController,
        ),
    },
    InstanceProcessesVariablesController: {
        instanceProcessesVariablesGet: Container.get(
            InstanceProcessesVariablesGetController,
        ),
        instanceProcessesVariableGetArchived: Container.get(
            InstanceProcessesVariablesGetArchivedController,
        ),
        instanceProcessesVariablesGetSharedController: Container.get(
            InstanceProcessesVariablesGetSharedController,
        ),
        instanceProcessesVariablesUpdate: Container.get(
            InstanceProcessesVariablesUpdateController,
        ),
        instanceProcessesVariablesUpdateByName: Container.get(
            InstanceProcessesVariablesUpdateByNameController,
        ),
    },
    customViews: {
        customViewsClone: Container.get(CustomViewsCloneController),
        customViewsGet: Container.get(CustomViewsGetController),
        customViewsMine: Container.get(CustomViewsMineController),
        customViewsPost: Container.get(CustomViewsPostController),
        customViewsRemove: Container.get(CustomViewsRemoveController),
        customViewsGetList: Container.get(CustomViewsGetListController),
        customViewsGetListWithTasks: Container.get(
            CustomViewsGetListWithTasksController,
        ),
    },
    dmsAccessLog: {
        dmsAccessLogGet: Container.get(DmsAccessLogGetController),
    },
    dmsFileMetadata: {
        dmsFileMetadataAll: Container.get(DmsFileMetadataAllController),
        dmsFileMetadataAllArchived: Container.get(
            DmsFileMetadataAllArchivedController,
        ),
    },
    dmsFileTag: {
        dmsFileTagPost: Container.get(DmsFileTagPostController),
    },
    dmsFileRevision: {
        dmsFileRevisionGet: Container.get(DmsFileRevisionGetController),
        dmsFileRevisionGetArchived: Container.get(
            DmsFileRevisionGetArchivedController,
        ),
    },
    dmsFolder: {
        dmsFolderDel: Container.get(DmsFolderDelController),
        dmsFolderGet: Container.get(DmsFolderGetController),
        dmsFolderPost: Container.get(DmsFolderPostController),
        dmsFolderTree: Container.get(DmsFolderTreeController),
    },
    dmsLogicalTag: {
        dmsLogicalTagGet: Container.get(DmsLogicalTagGetController),
        dmsLogicalTagPost: Container.get(DmsLogicalTagPostController),
    },
    dmsTag: {
        dmsTagGet: Container.get(DmsTagGetController),
        dmsTagPost: Container.get(DmsTagPostController),
        dmsTagRemove: Container.get(DmsTagRemoveController),
    },
    dms: {
        dmsAll: Container.get(DmsAllController),
        dmsCurrent: Container.get(DmsCurrentController),
        dmsDeleted: Container.get(DmsDeletedController),
        dmsDownload: Container.get(DmsDownloadController),
        dmsDownloadArchived: Container.get(DmsDownloadArchivedController),
        dmsDownloadBulk: Container.get(DmsDownloadBulkController),
        dmsDownloadMultipleLegacy: Container.get(
            DmsDownloadMultipleLegacyController,
        ),
        dmsFile: Container.get(DmsFileController),
        dmsForProcess: Container.get(DmsForProcessController),
        dmsForProcessArchived: Container.get(DmsForProcessArchivedController),
        dmsForTask: Container.get(DmsForTaskController),
        dmsForTaskArchived: Container.get(DmsForTaskArchivedController),
        dmsGetIndexedCount: Container.get(DmsGetIndexedCountController),
        dmsIndexDocument: Container.get(DmsIndexDocumentController),
        dmsRomoveBulk: Container.get(DmsRemoveBulkController),
        dmsRemovePermanently: Container.get(DmsRemovePermanentlyController),
        dmsRemoveSingle: Container.get(DmsRemoveSingleController),
        dmsRestore: Container.get(DmsRestoreController),
        dmsSearch: Container.get(DmsSearchController),
        dmsSimpleSearch: Container.get(DmsSimpleSearchController),
        dmsUploadFile: Container.get(DmsUploadFileController),
        dmsUploadPrivateFile: Container.get(DmsUploadPrivateFileController),
    },
    jsScript: {
        jsScriptPost: Container.get(JsScriptPostController),
        jsScriptRemove: Container.get(JsScriptRemoveController),
        jsScriptCalculations: Container.get(JsScriptCalculationsController),
        jsScriptFrontend: Container.get(JsScriptFrontendController),
        jsScriptFrontendReact: Container.get(JsScriptFrontendReactController),
        jsScriptExport: Container.get(JsScriptExportController),
        jsScriptImport: Container.get(JsScriptImportController),
    },
    log: {
        logGetCategories: Container.get(LogGetCategoriesController),
        logGetDetail: Container.get(LogGetDetailController),
        logDownloadDetail: Container.get(LogDownloadDetailController),
        logGetLogs: Container.get(LogGetLogsController),
        logPost: Container.get(LogPostController),
        logGetCalculationLogsController: Container.get(
            LogGetCalculationLogsController,
        ),
    },
    customViewsMail: {
        customViewsMailGet: Container.get(CustomViewsMailGetController),
        customViewsMailPost: Container.get(CustomViewsMailPostController),
        customViewsMailRemove: Container.get(CustomViewsMailRemoveController),
    },
    dynamicTable: {
        dynamicTableAddCol: Container.get(DynamicTableAddColController),
        dynamicTableAddRow: Container.get(DynamicTableAddRowController),
        dynamicTableChangeValues: Container.get(
            DynamicTableChangeValuesController,
        ),
        dynamicTableClone: Container.get(DynamicTableCloneController),
        dynamicTableCreate: Container.get(DynamicTableCreateController),
        dynamicTableGet: Container.get(DynamicTableGetController),
        dynamicTableImportCsv: Container.get(DynamicTableImportCsvController),
        dynamicTableMoveRow: Container.get(DynamicTableMoveRowController),
        dynamicTableRemoveCol: Container.get(DynamicTableRemoveColController),
        dynamicTableRemoveRow: Container.get(DynamicTableRemoveRowController),
        dynamicTableRemoveTable: Container.get(
            DynamicTableRemoveTableController,
        ),
        dynamicTableRemoveTableBulk: Container.get(
            DynamicTableRemoveTableBulkController,
        ),
        dynamicTableEdit: Container.get(DynamicTableEditController),
        dynamicTableRenameCol: Container.get(DynamicTableRenameColController),
        dynamicTableValues: Container.get(DynamicTableValuesController),
        dynamicTableColsGet: Container.get(DynamicTableColsGetController),
    },
    dynamicTableValues: {
        dynamicTableValuesGetColumn: Container.get(
            DynamicTableValuesGetColumnController,
        ),
    },
    eventDefinition: {
        eventDefinitionGetEvents: Container.get(
            EventDefinitionGetEventsController,
        ),
        eventDefinitionRemove: Container.get(EventDefinitionRemoveController),
        eventDefinitionPost: Container.get(EventDefinitionPostController),
        eventDefinitionGetReturnTEventList: Container.get(
            EventDefinitionGetReturnTEventListController,
        ),
    },
    events: {
        eventsGetEventButtons: Container.get(EventsGetEventButtons),
        eventsMapping: Container.get(EventsMappingController),
    },
    entityImport: {
        entityImportOrgStrImport: Container.get(
            EntityImportOrgStrImportController,
        ),
        entityImportOrgStrImportPreview: Container.get(
            EntityImportOrgStrImportPreviewController,
        ),
        entityImportRoleImport: Container.get(EntityImportRoleImportController),
        entityImportRoleImportPreview: Container.get(
            EntityImportRoleImportPreviewController,
        ),
        entityImportUserImport: Container.get(EntityImportUserImportController),
        entityImportUserImportPreview: Container.get(
            EntityImportUserImportPreviewController,
        ),
    },
    export: {
        exportExcel: Container.get(ExportExcel),
        exportCsv: Container.get(ExportCsv),
        exportTemplate: Container.get(ExportTemplate),
        exportVariablesUsage: Container.get(ExportVariablesUsageController),
    },
    performanceLogs: {
        performanceLogsDetailList: Container.get(
            PerformanceLogsDetailListController,
        ),
        performanceLogsGraphList: Container.get(
            PerformanceLogsGraphListController,
        ),
    },
    pdf: {
        pdfToPdf: Container.get(PdfToPdfController),
    },
    instanceMassTasks: {
        instanceMassTasksMassFinnish: Container.get(
            InstanceMassTasksMassFinishController,
        ),
    },
    reportGraph: {
        reportGraphClone: Container.get(ReportGraphCloneController),
        reportGraphGet: Container.get(ReportGraphGetController),
        reportGraphGetChart: Container.get(ReportGraphGetChartController),
        reportGraphGetChartPreview: Container.get(
            ReportGraphGetChartPreviewController,
        ),
        reportGraphGetForGraph: Container.get(ReportGraphGetForGraphController),
        reportGraphGetForTable: Container.get(ReportGraphGetForTableController),
        reportGraphMine: Container.get(ReportGraphMineController),
        reportGraphPost: Container.get(ReportGraphPostController),
        reportGraphRemove: Container.get(ReportGraphRemoveController),
        filter: {
            reportGraphGlobalFilterGetForFiltering: Container.get(
                ReportGraphGlobalFilterGetForFilteringController,
            ),
            reportGraphGlobalFilterGetForForm: Container.get(
                ReportGraphGlobalFilterGetForFormController,
            ),
            reportGraphGlobalFilterPost: Container.get(
                ReportGraphGlobalFilterPostController,
            ),
        },
    },
    hr: {
        hrChangeAgenda: Container.get(HrChangeAgendaController),
        hrChangeUserBusiness: Container.get(HrChangeUserBusinessController),
        hrChangeUserBusinessLocked: Container.get(
            HrChangeUserBusinessLockedController,
        ),
        hrChangeUserFull: Container.get(HrChangeUserFullController),
        hrGetActive: Container.get(HrGetActiveController),
        hrGetAll: Container.get(HrGetAllController),
        hrGetBusiness: Container.get(HrGetBusinessController),
        hrGetBusinessLocked: Container.get(HrGetBusinessLockedController),
        hrGetFull: Container.get(HrGetFullController),
        hrGetInactive: Container.get(HrGetInactiveController),
        hrGetLogs: Container.get(HrGetLogsController),
        hrChangeOwnerForProcesses: Container.get(
            HrChangeOwnerForProcessesController,
        ),
        hrChangeOwnerForTasks: Container.get(HrchangeOwnerForTasksController),
    },
    roleUsers: {
        roleUsersActive: Container.get(RoleUsersActiveController),
        roleUsersDeleted: Container.get(RoleUsersDeletedController),
    },
    rule: {
        definition: {
            ruleDefinitionCreate: Container.get(RuleDefinitionCreateController),
            ruleDefinitionGetRules: Container.get(
                RuleDefinitionGetRulesController,
            ),
            ruleDefinitionRemove: Container.get(RuleDefinitionRemoveController),
        },
    },
    organization: {
        organization: {
            organizationGetColors: Container.get(
                OrganizationGetColorsController,
            ),
            organizationGetMailColors: Container.get(
                OrganizationGetMailColorsController,
            ),
        },
        structure: {
            organizationStructureActive: Container.get(
                OrganizationStructureActiveController,
            ),
            organizationStructureAll: Container.get(
                OrganizationStructureAllController,
            ),
            organizationStructureCreate: Container.get(
                OrganizationStructureCreateController,
            ),
            organizationStructureGetManagedUser: Container.get(
                OrganizationStructureGetManagedUsersController,
            ),
            organizationStructureRemove: Container.get(
                OrganizationStructureRemoveController,
            ),
        },
        structureTree: {
            organizationStructureTreeGet: Container.get(
                OrganizationStructureTreeGetController,
            ),
            organizationStructureTreeGetFlat: Container.get(
                OrganizationStructureTreeGetFlatController,
            ),
        },
        tree: {
            organizationTreeGet: Container.get(OrganizationTreeGetController),
        },
        users: {
            organizationUsersActive: Container.get(
                OrganizationUsersActiveController,
            ),
            organizationUsersDeleted: Container.get(
                OrganizationUsersDeletedController,
            ),
        },
    },
    maintenance: {
        maintenancePost: Container.get(MaintenancePostController),
        maintenanceGet: Container.get(MaintenanceGetController),
    },
    instancePlan: {
        planGetAll: Container.get(PlanGetAllController),
        planPost: Container.get(PlanPostController),
        planDelete: Container.get(PlanDeleteController),
    },
    planProcessLog: {
        planProcessLogGet: Container.get(PlanProcessLogGetController),
    },
    instanceTasks: {
        instanceTaskGetAll: Container.get(InstanceTaskGetAllController),
        instanceGetAllArchived: Container.get(InstanceGetAllArchivedController),
        instanceTaskGetActive: Container.get(InstanceTaskGetActiveController),
        instanceTaskMineAndToPull: Container.get(
            InstanceTaskMineAndToPullController,
        ),
        instanceTaskMine: Container.get(InstanceTaskMineController),
        instanceTaskDone: Container.get(InstanceTaskDoneController),
        instanceTaskForProcess: Container.get(InstanceTaskForProcessController),
        instanceTaskFinish: Container.get(InstanceTaskFinishController),
        instanceTaskInfo: Container.get(InstanceTaskInfoController),
        instanceTaskAssignToMe: Container.get(InstanceTaskAssignToMeController),
        instanceTaskAssignTo: Container.get(InstanceTaskAssignToController),
        instanceTaskReject: Container.get(InstanceTaskRejectController),
        instanceTaskDelegate: Container.get(InstanceTaskDelegateController),
        instanceTaskGetScheduledTasks: Container.get(
            InstanceTaskGetScheduledTasksController,
        ),
        instanceTaskGetFailedScheduledTasks: Container.get(
            InstanceTaskGetFailedScheduledTasksController,
        ),
        instanceTaskRescheduleTasks: Container.get(
            InstanceTaskRescheduleTasksController,
        ),
        instanceTaskScheduledActivate: Container.get(
            InstanceTaskScheduledActivateController,
        ),
        instanceTaskToPull: Container.get(InstanceTaskToPullController),
        instanceTaskBySolvers: Container.get(InstanceTaskBySolversController),
        instanceTaskBulkCompletable: Container.get(
            InstanceTaskBulkCompletableController,
        ),
        instanceTaskPullToMe: Container.get(InstanceTaskPullToMeController),
        instanceTaskSetTaskDues: Container.get(InstanceTaskSetTaskDues),
        instanceTaskRefreshCalculation: Container.get(
            InstanceTaskRefreshCalculation,
        ),
        instanceTaskAddITask: Container.get(InstanceTaskAddITaskController),
        instanceTaskChangeSolver: Container.get(
            InstanceTaskChangeSolverController,
        ),
        instanceTaskResendEmailNotification: Container.get(
            InstanceTaskResendEmailNotificationController,
        ),
        instanceTaskGetTaskSections: Container.get(
            InstanceTaskGetTaskSectionsController,
        ),
        instanceTaskGetTaskSectionsArchived: Container.get(
            InstanceTaskGetTaskSectionsArchivedController,
        ),
        instanceTaskProgressActual: Container.get(
            InstanceTaskProgressActualController,
        ),
        instanceTaskGetComputedValuesController: Container.get(
            InstanceTaskGetComputedValuesController,
        ),
        instanceTaskGetToSolveController: Container.get(
            InstanceTaskGetToSolveController,
        ),
    },
    process: {
        event: {
            processEventGet: Container.get(ProcessEventGetController),
            processEventHandEventsList: Container.get(
                ProcessEventHandEventsListController,
            ),
            processEventHandInvoke: Container.get(
                ProcessEventHandInvokeController,
            ),
        },
        info: {
            processInfoHandEventMap: Container.get(
                ProcessInfoHandEventMapController,
            ),
            processInfoHandEventMapArchived: Container.get(
                ProcessInfoHandEventMapArchivedController,
            ),
            processInfoProcessRightsMap: Container.get(
                ProcessInfoProcessRightsMapController,
            ),
            processInfoProcessRightsMapArchived: Container.get(
                ProcessInfoProcessRightsMapArchivedController,
            ),
        },
        print: {
            processPrintGet: Container.get(ProcessPrintGetController),
            processPrintGetArchived: Container.get(
                ProcessPrintGetArchivedController,
            ),
            processPrintGetConvertedPrint: Container.get(
                ProcessPrintGetConvertedPrintController,
            ),
            processPrintPrintToFile: Container.get(
                ProcessPrintPrintToFileController,
            ),
        },
        instanceProccessesHistory: {
            instanceProcessesHistoryGet: Container.get(
                InstanceProcessesHistoryGetController,
            ),
            instanceProcessesHistoryGetArchived: Container.get(
                InstanceProcessesHistoryGetArchivedController,
            ),
            instanceProcessesHistoryProgressHistory: Container.get(
                InstanceProcessesHistoryProgressHistoryController,
            ),
        },
        instanceProcess: {
            instanceProcessActivate: Container.get(
                InstanceProcessActivateController,
            ),
            instanceProcessChangeCaseOwner: Container.get(
                InstanceProcessChangeCaseOwnerController,
            ),
            instanceProcessErase: Container.get(InstanceProcessEraseController),
            instanceProcessErrored: Container.get(
                InstanceProcessErroredController,
            ),
            instanceProcessGet: Container.get(InstanceProcessGetController),
            instanceProcessGetAllUsed: Container.get(
                InstanceProcessGetAllUsedController,
            ),
            instanceProcessGetNotUsed: Container.get(
                InstanceProcessGetNotUsedController,
            ),
            instanceProcessGetSubprocess: Container.get(
                InstanceProcessGetSubprocessController,
            ),
            instanceProcessMine: Container.get(InstanceProcessMineController),
            instanceProcessRemove: Container.get(
                InstanceProcessRemoveController,
            ),
            instanceProcessShredded: Container.get(
                InstanceProcessShreddedController,
            ),
            instanceProcessSuspend: Container.get(
                InstanceProcessSuspendController,
            ),
            instanceProcessResume: Container.get(
                InstanceProcessResumeController,
            ),
        },
        graph: {
            instanceProcessesGraph: Container.get(
                InstanceProcessesGraphController,
            ),
        },
    },
    registeredMobileDevice: {
        registeredMobileDeviceDelete: Container.get(
            RegisteredMobileDeviceDeleteController,
        ),
        registeredMobileDeviceGetDomainWhitelist: Container.get(
            RegisteredMobileDeviceGetDomainWhitelistController,
        ),
        registeredMobileDeviceGetFirebaseProject: Container.get(
            RegisteredMobileDeviceGetFirebaseProjectController,
        ),
        registeredMobileDeviceList: Container.get(
            RegisteredMobileDeviceListController,
        ),
        registeredMobileDeviceListAll: Container.get(
            RegisteredMobileDeviceListAllController,
        ),
        registeredMobileDeviceMuteNotification: Container.get(
            RegisteredMobileDeviceMuteNotificationController,
        ),
        registeredMobileDeviceRegister: Container.get(
            RegisteredMobileDeviceRegisterController,
        ),
        registeredMobileDeviceReloadQr: Container.get(
            RegisteredMobileDeviceReloadQrController,
        ),
        registeredMobileDeviceRename: Container.get(
            RegisteredMobileDeviceRenameController,
        ),
        registeredMobileDeviceResetFCMBadgeCounter: Container.get(
            RegisteredMobileDeviceResetFCMBadgeCounterController,
        ),
        registeredMobileDeviceSetDeviceToken: Container.get(
            RegisteredMobileDeviceSetDeviceTokenController,
        ),
        registeredMobileDeviceSetToken: Container.get(
            RegisteredMobileDeviceSetTokenController,
        ),
        registeredMobileDeviceUnregister: Container.get(
            RegisteredMobileDeviceUnregisterController,
        ),
    },
    sequence: {
        sequenceGet: Container.get(SequenceGetController),
        sequencePost: Container.get(SequencePostController),
    },
    import: {
        importVariablesUsage: Container.get(ImportVariablesUsageController),
        importTemplate: Container.get(ImportTemplate),
    },
    proxy: {
        proxyGet: Container.get(ProxyGetController),
    },
    queue: {
        queueGetJobCounts: Container.get(QueueGetJobCountsController),
        queueGetJobs: Container.get(QueueGetJobsController),
        queueGetWorkers: Container.get(QueueGetWorkersController),
        queueJobMoveToCompleted: Container.get(
            QueueJobMoveToCompletedController,
        ),
        queueJobMoveToDelayed: Container.get(QueueJobMoveToDelayedController),
        queueJobMoveToFailed: Container.get(QueueJobMoveToFailedController),
        queueJobRemove: Container.get(QueueJobRemoveController),
        queueScaleWorkerConcurrency: Container.get(
            QueueScaleWorkerConcurrencyController,
        ),
        queueWorkerResume: Container.get(QueueWorkerResumeController),
        queueWorkerPause: Container.get(QueueWorkerPauseController),
    },
    license: {
        getLicenses: Container.get(GetLicensesController),
        getLicenseResult: Container.get(GetLicenseResultController),
        storeLicense: Container.get(StoreLicenseController),
    },
    dynamicList: {
        dynamicListGet: Container.get(DynamicListGetController),
        dynamicListList: Container.get(DynamicListListController),
    },
    guides: {
        guidesGet: Container.get(GuidesGetController),
        guidesPost: Container.get(GuidesPostController),
    },
    header: {
        headerClone: Container.get(HeaderCloneController),
        headerCreate: Container.get(HeaderCreateController),
        headerGetForTProcess: Container.get(HeaderGetForTProcessController),
        headerGetList: Container.get(HeaderGetListController),
        headerRemove: Container.get(HeaderRemoveController),
    },
    mailQ: {
        mailQAll: Container.get(MailQAllController),
        mailQDetail: Container.get(MailQDetailController),
        mailQUpdateStatus: Container.get(MailQUpdateStatusController),
    },
    template: {
        print: {
            templatePrintGet: Container.get(TemplatePrintGetController),
            templatePrintCreate: Container.get(TemplatePrintCreateController),
            templatePrintRemove: Container.get(TemplatePrintRemoveController),
        },
        taskLink: {
            templateTaskLinkCreate: Container.get(
                TemplateTaskLinkCreateController,
            ),
            templateTaskLinkAll: Container.get(TemplateTaskLinkAllController),
            templateTaskLinkRemove: Container.get(
                TemplateTaskLinkRemoveController,
            ),
            templateTaskLinkForProcess: Container.get(
                TemplateTaskLinkForProcessController,
            ),
        },
        variables: {
            templateVariablesCreate: Container.get(
                TemplateVariablesCreateController,
            ),
            templateVariablesGet: Container.get(TemplateVariablesGetController),
            templateVariablesRemove: Container.get(
                TemplateVariablesRemoveController,
            ),
        },
        variablesUsage: {
            templateVariablesUsageForProcess: Container.get(
                TemplateVariablesUsageForProcessController,
            ),
            templateVariablesUsagePostProcess: Container.get(
                TemplateVariablesUsagePostProcessController,
            ),
        },
        taskCalculationDocs: {
            templateTaskCalculationDocs: Container.get(
                TemplateTaskCalculationDocsController,
            ),
        },
        sections: {
            templateSectionsRemove: Container.get(
                TemplateSectionsRemoveController,
            ),
            templateSectionsCreate: Container.get(
                TemplateSectionsCreateController,
            ),
            templateSectionsForProcess: Container.get(
                TemplateSectionsForProcessController,
            ),
            templateSectionsGet: Container.get(TemplateSectionsGetController),
            templateSectionsGetWithTasks: Container.get(
                TemplateSectionsGetWithTasksController,
            ),
        },
        processesTasks: {
            templateProcessesTasksGet: Container.get(
                TemplateProcessesTasksGetController,
            ),
        },
        tasks: {
            templateTasksGet: Container.get(TemplateTasksGetController),
            templateTasksCreate: Container.get(TemplateTasksCreateController),
            templateTasksRemove: Container.get(TemplateTasksRemoveController),
        },
        processShredding: {
            templateProcessShreddingGet: Container.get(
                TemplateProcessShreddingGetController,
            ),
            templateProcessShreddingStore: Container.get(
                TemplateProcessShreddingStoreController,
            ),
        },
        processes: {
            templateProcessesAll: Container.get(TemplateProcessesAllController),
            templateProcessesCreate: Container.get(
                TemplateProcessesCreateController,
            ),
            templateProcessesDeleted: Container.get(
                TemplateProcessesDeletedController,
            ),
            templateProcessesGraph: Container.get(
                TemplateProcessesGraphController,
            ),
            templateProcessesHtml: Container.get(
                TemplateProcessesHtmlController,
            ),
            templateProcessesNewVersion: Container.get(
                TemplateProcessesNewVersionController,
            ),
            templateProcessesRemove: Container.get(
                TemplateProcessesRemoveController,
            ),
            templateProcessesToStart: Container.get(
                TemplateProcessesToStartController,
            ),
            templateProcessesVersions: Container.get(
                TemplateProcessesVersionsController,
            ),
            templateProcessesList: Container.get(
                TemplateProcessesListController,
            ),
        },
    },
    serviceOperation: {
        activateTask: Container.get(ActivateTaskController),
        archiveProcess: Container.get(ArchiveProcessController),
        archiveProcessFromTaskTable: Container.get(
            ArchiveProcessFromTaskTableController,
        ),
        archiveProcessFromVariableTable: Container.get(
            ArchiveProcessFromVariableTableController,
        ),
        finishTask: Container.get(FinishTaskController),
        setProcessStatusActive: Container.get(SetProcessStatusActiveController),
        setProcessStatusActiveFromTaskTable: Container.get(
            SetProcessStatusActiveFromTaskTableController,
        ),
        setProcessStatusActiveFromVariableTable: Container.get(
            SetProcessStatusActiveFromVariableTableController,
        ),
        setProcessStatusDone: Container.get(SetProcessStatusDoneController),
        setProcessStatusDoneFromTaskTable: Container.get(
            SetProcessStatusDoneFromTaskTableController,
        ),
        setProcessStatusDoneFromVariableTable: Container.get(
            SetProcessStatusDoneFromVariableTableController,
        ),
        changeProcesses: Container.get(ChangeProcessesController),
        changeProcessFromTaskTable: Container.get(
            ChangeProcessFromTaskTableController,
        ),
        changeProcessFromVariableTable: Container.get(
            ChangeProcessFromVariableTableController,
        ),
        changeRole: Container.get(ChangeRoleController),
        changeTasks: Container.get(ChangeTasksController),
        changeUser: Container.get(ChangeUserController),
        changeUserFromUserParameterTable: Container.get(
            ChangeUserFromUserParameterTableController,
        ),
        changeUserParameter: Container.get(ChangeUserParameterController),
        changeVariables: Container.get(ChangeVariablesController),
        describeOrganizationStructure: Container.get(
            DescribeOrganizationStructureController,
        ),
        describeProcessesList: Container.get(DescribeProcessesListController),
        describeRoles: Container.get(DescribeRolesController),
        describeTasksList: Container.get(DescribeTasksListController),
        describeUserParameters: Container.get(DescribeUserParametersController),
        organizationStructureList: Container.get(
            OrganizationStructureListController,
        ),
        describeUsers: Container.get(DescribeUsersController),
        describeVariables: Container.get(DescribeVariablesController),
        processesList: Container.get(ProcessesListController),
        rolesList: Container.get(RolesListController),
        setOrganizationStructure: Container.get(
            SetOrganizationStructureController,
        ),
        tasksList: Container.get(TasksListController),
        userParameterList: Container.get(UserParameterListController),
        usersList: Container.get(UsersListController),
        variablesList: Container.get(VariablesListController),
    },
    post: {
        postGetPosts: Container.get(PostGetPostsController),
        postGetPostsFor: Container.get(PostGetPostsForController),
        postGetTags: Container.get(PostGetTagsController),
        postRemove: Container.get(PostRemoveController),
        postTagRemove: Container.get(PostTagRemoveController),
        postTagUpsert: Container.get(PostTagUpsertController),
        postUpsert: Container.get(PostUpsertController),
    },
    variables: {
        variablesGet: Container.get(VariablesGetController),
    },
    xmlProcessImport: {
        audit: Container.get(XmlProcessImportAuditController),
        downloadProcessedDms: Container.get(
            XmlProcessImportDownloadProcessedDmsController,
        ),
        downloadProcessedXml: Container.get(
            XmlProcessImportDownloadProcessedXmlController,
        ),
        downloadScheduledDms: Container.get(
            XmlProcessImportDownloadScheduledDmsController,
        ),
        downloadScheduledXml: Container.get(
            XmlProcessImportDownloadScheduledXmlController,
        ),
        history: Container.get(XmlProcessImportHistoryController),
        process: Container.get(XmlProcessImportProcessController),
        reschedule: Container.get(XmlProcessImportRescheduleController),
        scheduled: Container.get(XmlProcessImportScheduledController),
        setNote: Container.get(XmlProcessImportSetNoteController),
        skip: Container.get(XmlProcessImportSkipController),
    },
});
