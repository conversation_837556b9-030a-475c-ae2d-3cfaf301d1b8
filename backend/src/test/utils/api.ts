// @ts-nocheck
import { Exception } from "../../utils/errorHandling/exceptions/exception";
import _ from "lodash";
import fs from "fs";
import * as RoleConstants from "../../api/orm/entity/const/roleConst";
import { UtilsService } from "../../api/services/UtilsService";
import * as OrganizationStructureConstants from "../../api/orm/entity/const/organizationStructureConsts";
import { assert } from "chai";

export type TemplateProcessId = [number, number];
export interface RuleDefinitionParameter {
    evepar_name: string | null;
    rdefpar_name: string | null;
    rdefpar_order: number | null;
    rdefpar_type: string | null;
    rdefpar_value: string | null;
    tvar_id: number | null;
    tvar_id_dest: number | null;
}
export interface RuleDefinitionVariables {
    RDEF_PROC_OWNER?: string;
    RDEF_PROC_NAME?: string;
    RDEF_CSV_HEADER?: string;
    RDEF_CSV_MASK?: string;
    RDEF_PROCESS_ID?: string;
    RDEF_CSV_FILE?: string;
    RDEF_CSV_SEPARATOR?: string;
}

export interface CustomViewFilter {
    column: string;
    enabled: boolean;
    op: string;
    value: string;
}

export interface CustomViewColumn {
    tvar_id: number;
    cvc_name: string;
}

export interface CustomViewBody {
    cv_sort: string;
    cv_order: string;
    cv_name: string;
    cv_filter: CustomViewFilter[];
    cv_iproc_status_list: string;
    tproc_id: number;
    roles_share: number[];
    users_share: number[];
    orgs_share: number[];
    columns: CustomViewColumn[];
    include_similar_processes: string;
}

export interface BaseResponseItem {
    id: number;
}

export interface HeaderItem extends BaseResponseItem {
    tproc_id: number;
    header_name: number;
    header_enabled: string;
    header_code: string;
    hdr_vis_role_id: number;
    hdr_vis_internal_user_only: string;
    header_hr_role_id: number;
    tproc_status: string;
    hdr_vis_role_name: string;
    header_hr_role_name: string;
}

export interface CustomViewFilterItem {
    enabled: boolean;
    op: string;
    column?: string;
    value?: string | number;
}

export async function post(url, token, body): Promise<number> {
    const res = await agent
        .post(url)
        .set("Authorization", token)
        .send(body)
        .expect(200);

    return res.body.id;
}

export async function postFullResponse(
    url: string,
    token?: string,
    body?: any,
): Promise<any> {
    const res = await agent
        .post(url)
        .set("Authorization", token)
        .send(body)
        .expect(200);

    return res.body;
}

export async function get(url: string, token: string): Promise<any> {
    const res = await agent.get(url).set("Authorization", token).expect(200);

    return res.body;
}

export async function getFullResponse(
    url: string,
    token?: string,
    headers?: any,
): Promise<any> {
    const request = agent.get(url).set("Authorization", token);

    if (headers && Object.keys(headers).length > 0) {
        const headerKeys = Object.keys(headers);
        for (const headerKey of headerKeys) {
            request.set(headerKey, headers[headerKey]);
        }
    }

    return await request;
}

export async function getValidHash(login, pass): Promise<string> {
    const loginUser = {
        username: login,
        password: pass,
        grant_type: "password",
    };
    const res = await agent.post("/authenticate").send(loginUser).expect(200);

    return `Bearer ${res.body.access_token}`;
}

export async function logInUsers(users): Promise<string[]> {
    const res = [];
    for (const user of users) {
        res.push(await getValidHash(user.USER_NAME, user.USER_PASSWORD));
    }
    return res;
}

export async function createOrgUnit(
    orgstrName,
    users,
    parentOrgstrId,
    managerUserId,
): Promise<number> {
    const body = {
        orgstr_name: orgstrName,
        users,
        parent_orgstr_id: parentOrgstrId,
        manager_user_id: managerUserId,
    };

    const res = await agent
        .post("/org-units/")
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);

    return res.body.id;
}

export const genRanHex = (size: number): string =>
    [...Array(size)]
        .map(() => Math.floor(Math.random() * 16).toString(16))
        .join("");

export async function createRole(roleName: string, users?): Promise<number> {
    const body = await createCustomRole(roleName, users);

    body.role_name = roleName;
    body.role_category = `${roleName}_${roleName}`;
    body.users = users;

    const res = await post("/roles", authorizationTokenAdmin, body);
    return res;
}

export async function createUser(
    userName,
    userFirstName,
    userLastName,
    userPassword,
    activeOrLocked: string = "A",
    organization: { orgstr_id: number; manager: number[] } = {
        orgstr_id: 1,
        manager: [],
    },
    roles: any[] = [],
    userDisplayName?: string,
): Promise<number> {
    const user = {
        user_name: userName,
        user_email: `${userName}@api.test`,
        user_first_name: userFirstName,
        user_last_name: userLastName,
        user_status: activeOrLocked,
        user_password: userPassword,
        organization,
        roles,
        user_display_name: userDisplayName,
    };

    return await post("/users", authorizationTokenAdmin, user);
}

export async function getUserToken(username, password) {
    const response = await agent
        .post("/authenticate")
        .send({
            username,
            password,
            grant_type: "password",
        })
        .expect(200);

    return `Bearer ${response.body.access_token}`;
}

export async function setUserParameter(parameters, userToken) {
    return await post("/user-parameters", userToken, parameters);
}

export async function generateUsers(
    numberOfUsers: number,
    testName: string,
    organization?,
    roles?,
) {
    const users = [];
    const result = [];
    for (let i = 1; i <= numberOfUsers; i += 1) {
        const user = {};
        const userName = `testUser${i}_${testName}`;
        user.USER_NAME = userName;
        user.USER_FIRST_NAME = userName;
        user.USER_LAST_NAME = userName;
        user.USER_DISPLAY_NAME = `${userName} ${userName}`;
        user.USER_PASSWORD = "MoreSecure1Password";
        user.ORGANIZATION = organization;
        user.ROLES = roles;
        user.USER_EMAIL = `${userName}@api.test`;
        users.push(user);
    }

    const createdUsers = [];
    for (const usr of users) {
        const userId = await createUser(
            usr.USER_NAME,
            usr.USER_FIRST_NAME,
            usr.USER_LAST_NAME,
            usr.USER_PASSWORD,
            usr.ACTIVE_OR_LOCKED,
            usr.ORGANIZATION,
            usr.ROLES,
        );
        usr.USER_ID = userId;
        delete usr.ORGANIZATION;
        delete usr.ROLES;
        createdUsers.push(usr);
    }
    result.push(createdUsers);

    if (!organization) {
        const orgStrId = await createOrgUnit(
            testName,
            createdUsers.map((user) => user.USER_ID),
            OrganizationStructureConstants.ROOT,
        );
        result.push([orgStrId]);
    } else {
        result.push([organization]);
    }

    if (!roles) {
        const roleId = await createRole(
            testName,
            createdUsers.map((user) => user.USER_ID),
        );
        result.push([roleId]);
    } else {
        result.push([roles]);
    }
    return _.compact(result);
}

export async function deleteUser(userId: number): Promise<void> {
    const body = {
        id: userId,
    };
    await agent
        .delete(`/users/${userId}`)
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
}

export async function createCustomTemplateProcess(tProcName) {
    return {
        tproc_name: tProcName,
        tproc_description: null,
        tproc_vis_orgstr_id: null,
    };
}
export async function createCustomRole(roleName, users) {
    return {
        role_name: roleName,
        users,
    };
}

export async function createCustomCustomView(
    cvName: string,
    tProcId: TemplateProcessId,
    columns?: { tvar_id: number; cvc_name: string }[],
    filter?: any[],
    sort: string = "asc",
    order: string = "V-1",
    cvIprocStatusList: string = "A",
    rolesShare = [],
    orgsShare = [],
    usersShare = [],
): CustomViewBody {
    const cols = [
        {
            tvar_id: -1,
            cvc_name: "Název případu",
        },
        {
            tvar_id: -2,
            cvc_name: "Popis",
        },
        {
            tvar_id: -3,
            cvc_name: "Iniciátor",
        },
        {
            tvar_id: -4,
            cvc_name: "Zadáno",
        },
    ];

    columns = columns || cols;

    return {
        cv_sort: sort,
        cv_order: order,
        cv_name: cvName,
        cv_filter: filter,
        cv_iproc_status_list: cvIprocStatusList,
        tproc_id: tProcId[0],
        roles_share: rolesShare,
        users_share: usersShare,
        orgs_share: orgsShare,
        columns,
        include_similar_processes: "N",
    };
}

export async function createCustomView(
    cvName: string,
    tProcId: TemplateProcessId,
    headerId: number,
    token: string,
    columns?,
    filter?: CustomViewFilterItem[],
    sort?: string | null,
    order?: string | null,
    cvIprocStatusList: string = "A",
    rolesShare: number[] = [],
    orgsShare: number[] = [],
    usersShare: number[] = [],
): Promise<number> {
    const body = await createCustomCustomView(
        cvName,
        tProcId[0],
        columns,
        filter,
        sort,
        order,
        cvIprocStatusList,
        rolesShare,
        orgsShare,
        usersShare,
    );
    body.header_id = headerId;
    return await post("/custom-views/", token, body);
}

export async function createTaskVariable(
    taskId: number,
    body: {
        id: number;
        value: string;
    }[],
): Promise<number> {
    return await post(
        `/tasks/${taskId}/variables`,
        authorizationTokenAdmin,
        body,
    );
}

export async function createTemplateVariable(
    tProcId: TemplateProcessId,
    tVarName: string,
    tVarType: string,
    value?: any,
    tVarAttribute: string | null = null,
    tVarMulti: string | null = null,
    tVarLov:
        | number[]
        | { title: number | string; value: number | string }[] = [],
    params = {},
): Promise<number> {
    const body = {
        tvar_name: tVarName,
        tvar_type: tVarType,
        value,
        tvar_attribute: tVarAttribute,
        tvar_multi: tVarMulti,
        lovs: tVarLov,
    };

    Object.keys(params).forEach((param) => {
        body[param] = params[param];
    });

    const res = await agent
        .post(
            `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables`,
        )
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
    return res.body.id;
}

export async function updateTemplateVariable(
    tProcId,
    tVarId,
    value,
    type,
    name,
): Promise<number> {
    const body = {
        tvar_id: tVarId,
        value,
        tvar_type: type,
        tvar_name: name,
    };

    return await post(
        `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables/`,
        authorizationTokenAdmin,
        body,
    );
}

export async function getProcessVariables(iProcId) {
    return await get(
        `/processes/${iProcId}/variables`,
        authorizationTokenAdmin,
    );
}

export async function getTaskVariables(iTaskId: string) {
    return await get(`/tasks/${iTaskId}/variables`, authorizationTokenAdmin);
}

export async function updateVariable(
    iProcId,
    tvarId,
    newValue,
): Promise<number> {
    const data = await getProcessVariables(iProcId);
    const ivarId = _.find(data.items, { tvar_id: tvarId }).id;
    const body = [
        {
            ivar_id: ivarId,
            ivar_value: newValue,
        },
    ];
    return await post(
        `/processes/${iProcId}/variables`,
        authorizationTokenAdmin,
        body,
    );
}

export async function createCustomTemplateTask(tProcId, tTaskName) {
    return [
        {
            tproc_id: tProcId[0],
            tproc_version: tProcId[1],
            ttask_name: tTaskName,
            ttask_description: null,
            ttask_type: "S",
            ttask_gen_history: "N",
            ttask_petri_net_input: "O",
            ttask_sufficient_end: "N",
            ttask_run_only_once: "N",
            ttask_multiinstance_flag: "N",
            ttask_instruction: "",
            ttask_assesment_role_id: null,
            ttask_assesment_orgstr_cnst: null,
            ttask_reference_user: "supervisor()",
            ttask_assesment_hierarchy: "P",
            ttask_assesment_method: "T",
            ttask_garant: "PO",
            ttask_assesment_user_id: null,
            ttask_again: "N",
            ttask_duty: "Y",
            ttask_completion_co: "A",
            ttask_completions: [],
            ttask_var_mapping: [],
            ttask_var_global_order: "Y",
            ttask_due_offset: {
                type: "no",
                value: "",
            },
            ttask_duration: {
                type: "no",
                value: "",
            },
            ttask_operations: [],
        },
    ];
}

export async function createPlan(
    headerId,
    planName,
    startTime,
    tprocId,
    planUserId,
    token,
) {
    const body = {
        header_id: headerId,
        pln_assesment_hierarchy: "G",
        pln_name: planName,
        pln_orgstr_id: 1,
        pln_repeat_offset_type: "N",
        pln_start_datetime: startTime,
        pln_status: "A",
        pln_user_id: planUserId,
        pln_users_list: [],
        tproc_id: tprocId,
    };

    try {
        const response = await agent
            .post("/plans")
            .set("Authorization", token)
            .send(body);
        return response.body;
    } catch (error) {
        throw new Error(
            `There was an error in creating a plan: ${error.message}. Status: ${error.status}`,
        );
    }
}

export async function uploadFile(
    iProcId: number,
    file: string,
    token: string,
    lastRevisionId: number | null = null,
    privateFile: boolean = false,
    body?: any,
): Promise<number> {
    if (iProcId !== null) {
        // Use for absolute path to file
        if (body) {
            const res = await agent
                .post(`/dms/upload${privateFile ? "/private" : ""}`)
                .set("Authorization", token)
                .send(body);
            return res.body.id;
        } else if (lastRevisionId !== null) {
            const res = await agent
                .post(`/dms/upload${privateFile ? "/private" : ""}`)
                .set("Authorization", token)
                .field("filename", file)
                .field("iprocId", iProcId)
                .field("lastRevisionId", lastRevisionId)
                .attach("file", `./test/utils/${file}`);
            return res.body.id;
        } else {
            const res = await agent
                .post(`/dms/upload${privateFile ? "/private" : ""}`)
                .set("Authorization", token)
                .field("filename", file)
                .field("iprocId", iProcId)
                .attach("file", `./test/utils/${file}`);
            return res.body.id;
        }
    } else if (lastRevisionId !== null) {
        const res = await agent
            .post(`/dms/upload${privateFile ? "/private" : ""}`)
            .set("Authorization", token)
            .field("filename", file)
            .field("lastRevisionId", lastRevisionId)
            .attach("file", `./test/utils/${file}`);
        return res.body.id;
    } else {
        const res = await agent
            .post(`/dms/upload${privateFile ? "/private" : ""}`)
            .set("Authorization", token)
            .field("filename", file)
            .attach("file", `./test/utils/${file}`);
        return res.body.id;
    }
}

export async function getSubprocess(iProcId, token): Promise<number> {
    const res = await agent
        .get(`/processes/${iProcId}/subprocesses/`)
        .set("Authorization", token);
    return res.body.items[0].id;
}

export async function createTemplateTask(
    tProcId,
    tTaskName,
    tTaskAssesmentHierarchy,
    tTaskAssesmentMethod = "T",
): Promise<number> {
    const [body] = await createCustomTemplateTask(tProcId, tTaskName);
    body.ttask_assesment_hierarchy = tTaskAssesmentHierarchy;
    body.ttask_assesment_method = tTaskAssesmentMethod;
    return await post("/template-tasks", authorizationTokenAdmin, [body]);
}

export async function deleteTemplateProcess(tProcId) {
    const body = {};
    await agent
        .delete(`/template-processes/${tProcId[0]}/${tProcId[1]}`)
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
}

export async function deleteCaseStatuses(tProcId) {
    const body = {
        items: [
            {
                tproc_id: tProcId[0],
                tproc_version: tProcId[1],
            },
        ],
    };
    await agent
        .delete(`/case-status`)
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
}

export async function deleteInstanceProcess(iProcId) {
    await agent
        .post(`/processes/to-delete/${iProcId}`)
        .set("Authorization", authorizationTokenAdmin)
        .send({})
        .expect(200);
}

export async function activateProcess(
    tProcId: TemplateProcessId,
    authToken: string,
    headerId: number | null = null,
    returnFullResponse?: false,
): Promise<number>;
export async function activateProcess(
    tProcId: TemplateProcessId,
    authToken: string,
    headerId: number | null = null,
    returnFullResponse?: true,
): Promise<{
    IPROC_ID: number;
    NEXT_ITASK_ID: number;
    NEXT_ITASK_NAME: string;
    result: boolean;
    tasksToQueue: {
        ITASK_ID: any;
        ITASK_NAME: any;
    }[];
}>;
export async function activateProcess(
    tProcId: TemplateProcessId,
    authToken: string,
    headerId: number | null = null,
    returnFullResponse: boolean = false,
): Promise<any> {
    const headers = await findHeaders(tProcId);
    const body = {
        header_id: headers[0].id,
    };
    if (headerId != null) {
        body.header_id = headerId;
    }
    const resBody = await postFullResponse(
        `/processes/activate/${tProcId[0]}/${tProcId[1]}`,
        authToken,
        body,
    );
    return returnFullResponse ? resBody : resBody.IPROC_ID;
}

export async function addNoteToTask(iprocId, itaskId, note) {
    await agent
        .post(`/processes/${iprocId}/notes/add/`)
        .set("Authorization", authorizationTokenAdmin)
        .send({
            iproc_note: note,
            itask_id: itaskId,
        })
        .expect(200);
}

export async function getNotes(iprocId, token = authorizationTokenAdmin) {
    return await get(`/processes/${iprocId}/notes`, token);
}

export async function finishTask(
    iTaskId,
    authToken,
): Promise<{ NEXT_ITASK_ID: number }> {
    let id = iTaskId;

    // Is Array? Use last entry
    if (Array.isArray(iTaskId)) {
        id = iTaskId[iTaskId.length - 1];
    }
    const res = await agent
        .post(`/tasks/finish/${id}`)
        .send({})
        .set("Authorization", authToken);

    assert(res.status === 200, JSON.stringify(res.body));

    return res.body;
}

export async function deleteOrgUnit(orgUnitId) {
    const body = {};
    await agent
        .delete(`/org-units/${orgUnitId}`)
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
}

export async function deleteRequest(url: string, token?: string) {
    const body = {};
    await agent
        .delete(url)
        .set("Authorization", token ?? authorizationTokenAdmin)
        .send(body)
        .expect(200);
}

export async function createHeader(
    tProcId,
    headerName = "defaultName",
    headerEnabled = "Y",
    headerCode = "defaultCode",
    headerVisRole: number | null = null,
): Promise<number> {
    const body = {
        tproc_id: tProcId[0],
        tproc_version: tProcId[1],
        header_name: headerName,
        header_enabled: headerEnabled,
        header_code: headerCode,
        hdr_vis_role_id: headerVisRole,
    };
    return await post("/header", authorizationTokenAdmin, body);
}

export async function createHeaderRole(
    headerId: number,
    roleIds: number[],
): Promise<void> {
    const repo = globalThis.orm.repo("headerRole");

    await repo.removeForHeader(headerId);

    for (const roleId of roleIds) {
        const entity = repo.getEntity({
            ROLE_ID: roleId,
            HEADER_ID: headerId,
        });
        await repo.store(entity);
    }
}

export async function createHeaderOrganization(
    headerId: number,
    orgIds: number[],
): Promise<void> {
    const repo = globalThis.orm.repo("headerOrgstr");

    await repo.removeForHeader(headerId);

    for (const orgId of orgIds) {
        const entity = repo.getEntity({
            ORGSTR_ID: orgId,
            HEADER_ID: headerId,
        });
        await repo.store(entity);
    }
}

export async function findHeaderByName(tProcId, headerName): Promise<number> {
    const filter = `header_name<eq>"${headerName}"`;

    const res = await agent
        .get(
            `/template-processes/${tProcId[0]}/${
                tProcId[1]
            }/headers?filter=${encodeURIComponent(filter)}`,
        )
        .set("authorization", authorizationTokenAdmin)
        .expect(200);

    if (res.status !== 200) {
        throw new Error(
            `There was an error in findHeaderByName function: ${res.body.error.message}, Status: ${res.status}`,
        );
    } else if (!res.body.items[0]) {
        throw new Error(
            `Could not find header '${headerName}' for template '${tProcId[0]}/${tProcId[1]}'`,
        );
    } else {
        return res.body.items[0].id;
    }
}

/**
 * @description tries to find template by it's name, if fails import the template.
 */
export async function importTemplate(templateBody) {
    templateBody = Array.isArray(templateBody) ? templateBody[0] : templateBody;

    const processes = templateBody
        ? templateBody.template_processes
        : undefined;
    const tname =
        processes && processes.length > 0 ? processes[0].tproc_name : null;
    if (!tname) {
        return null;
    }

    const importOk = await postFullResponse(
        "/template-processes/import",
        authorizationTokenAdmin,
        templateBody,
    );

    if (importOk) {
        const filter = `tproc_name<eq>"${tname}"`;
        const url = `/template-processes?filter=${encodeURIComponent(filter)}`;
        const stored = await get(url, authorizationTokenAdmin);

        if (stored && stored.total_count > 0) {
            return stored.items[0];
        }
    }

    return null;
}

export async function createTemplateProcess(
    tProcName: string,
    tProcDesc?: string,
    tProcStatus: string = "A",
    visOrg: number | null = null,
    visRole: number | null = null,
    dmsVisibility = null,
    allUsersRoleInDefaultHeader = true,
    params = {},
): Promise<TemplateProcessId> {
    const body = await createCustomTemplateProcess(tProcName);
    body.tproc_description = tProcDesc;
    body.tproc_status = tProcStatus;
    body.tproc_vis_orgstr_id = visOrg;
    body.tproc_vis_role_id = visRole;
    body.tproc_dms_visibility = dmsVisibility;

    Object.keys(params).forEach((param) => {
        body[param] = params[param];
    });

    const tProcId = await post(
        "/template-processes",
        authorizationTokenAdmin,
        body,
    );

    if (allUsersRoleInDefaultHeader) {
        const headerId = await findHeaderByName(tProcId, tProcName);
        await createHeaderRole(headerId, [RoleConstants.ALL_USERS]);
    }
    return tProcId;
}

export async function updateUser(body): Promise<number> {
    return await post("/users", authorizationTokenAdmin, body);
}

export async function update(url, body): Promise<number> {
    return await post(url, authorizationTokenAdmin, body);
}

export async function findHeaders(tProcId): Promise<Array<HeaderItem>> {
    const body = await get(
        `/template-processes/${tProcId[0]}/${tProcId[1]}/headers`,
        authorizationTokenAdmin,
    );
    if (!body.items[0]) {
        throw new Error(
            `Could not find any headers for TemplateProcess '${tProcId[0]}/${tProcId[1]}'`,
        );
    }
    return body.items;
}

export async function findTask(
    token,
    iprocId,
    itaskName,
    multiinstance = "N",
    status = "A",
) {
    const filter = `iproc_id<eq>"${iprocId}"<and>itask_name<eq>"${itaskName}"<and>itask_multiinstance_flag<eq>"${multiinstance}"<and>itask_status<eq>"${status}"`;

    const res = await agent
        .get(`/tasks?filter=${encodeURIComponent(filter)}`)
        .set("Authorization", token)
        .expect(200);
    return res.body.items;
}

export async function getActiveTasks(token, iprocId) {
    const res = await agent
        .get(`/processes/${iprocId}/tasks`)
        .set("Authorization", token)
        .expect(200);
    return res.body.items;
}

export function variables(): any {
    return {
        headers: [],
        template_processes: [],
        instance_processes: [],
        template_variables: [],
        instance_variables: [],
        template_tasks: [],
        instance_tasks: [],
        dynamic_tables: [],
        users: [],
        hashes: [],
        roles: [],
        organization_structures: [],
        custom_views: [],
        vices: [],
        files: [],
        additionalMeta: [],
    };
}

/**
 * Create template task link
 * @param {number} tProcId
 * @param {number} fromTaskId
 * @param {number} toTaskId
 * @param {string} type - AND|OR|ELSE
 * @param {array} conditions - [{tcond_variable: 'text1', tcond_value: 'aaa', tcond_operator: 'eq'}, ...]
 * @param {string} mandatory - Y|N
 * @param {null|number} priority
 * @returns {number} link id
 */
export async function createLink(
    tProcId,
    fromTaskId,
    toTaskId,
    type = "AND",
    conditions?,
    mandatory = "N",
    priority = null,
): Promise<number> {
    const body = {
        tproc_id: tProcId[0],
        tproc_version: tProcId[1],
        ttasklink_from_ttask_id: fromTaskId,
        ttasklink_to_ttask_id: toTaskId,
        ttasklink_is_mandatory: mandatory,
        ttasklink_type: type,
        ttasklink_priority: priority,
        ttasklink_else: "NCOND",
    };
    if (type === "ELSE") {
        body.ttasklink_else = "ELSE";
    }
    if (!_.isEmpty(conditions)) {
        body.ttasklink_else = "COND";
        body.conditions = conditions;
    }
    const res = await agent
        .post("/template-link")
        .set("Authorization", authorizationTokenAdmin)
        .send([body])
        .expect(200);

    return res.body.id;
}

export async function createLinks(
    tProcId: TemplateProcessId,
    tasks: any[],
    type: string = "AND",
    conditions?: any,
    mandatory: string = "N",
    priority = null,
) {
    const linkCreation = [];
    for (let i = 0; i < tasks.length - 1; i++) {
        const link = await createLink(
            tProcId,
            tasks[i],
            tasks[i + 1],
            type,
            conditions,
            mandatory,
            priority,
        );
        linkCreation.push(link);
    }
    return linkCreation;
}

export async function mapVariableToTask(tProcId, body): Promise<number> {
    const res = await agent
        .post(`/template-processes/${tProcId}/1/template-variables/usage`)
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
    return res.body.id;
}

export async function createTtask(
    procId,
    taskName,
    params = {},
): Promise<number> {
    // Mapping compatibility
    if (params.ttask_var_mapping && !Array.isArray(params.ttask_var_mapping)) {
        const out = [];

        const map = ["read", "write", "must"];
        for (const level of map) {
            const mapping = params.ttask_var_mapping[level];
            if (mapping > 0) {
                mapping.forEach((tvarId) => {
                    out.push({
                        tvar_id: tvarId,
                        usage: level.substring(0, 1).toUpperCase(), // R, W, M
                        axis_x: null,
                        axis_y: null,
                    });
                });
            }
        }
        params.ttask_var_mapping = out;
    }

    const body = [
        {
            tproc_id: procId[0],
            tproc_version: procId[1],
            ttask_name: taskName,
            ttask_description: params.ttask_description || null,
            ttask_type: params.ttask_type || "S",
            ttask_gen_history: params.ttask_gen_history || "N",
            ttask_petri_net_input: params.ttask_petri_net_input || "O",
            ttask_sufficient_end: params.ttask_sufficient_end || "N",
            ttask_run_only_once: params.ttask_run_only_once || "N",
            ttask_multiinstance_flag: params.ttask_multiinstance_flag || "N",
            ttask_instruction: params.ttask_instruction || "",
            ttask_assesment_role_id: params.ttask_assesment_role_id || null,
            ttask_assesment_orgstr_cnst:
                params.ttask_assesment_orgstr_cnst || null,
            ttask_reference_user: params.ttask_reference_user || "supervisor()",
            ttask_assesment_hierarchy: params.ttask_assesment_hierarchy || "G",
            ttask_assesment_method: params.ttask_assesment_method || "T",
            ttask_garant: params.ttask_garant || "PO",
            ttask_assesment_user_id: params.ttask_assesment_user_id || null,
            ttask_again: params.ttask_again || "N",
            ttask_duty: params.ttask_duty || "Y",
            ttask_completion_co: params.ttask_completion_co || "A",
            ttask_completions: params.ttask_completions || [],
            ttask_var_mapping: params.ttask_var_mapping || [],
            ttask_var_global_order: params.ttask_var_global_order || "Y",
            ttask_due_offset: params.ttask_due_offset || {
                type: "no",
                value: "",
            },
            ttask_duration: params.ttask_duration || {
                type: "no",
                value: "",
            },
            ttask_operations: params.ttask_operations || [],
            ttask_is_delegatable: params.ttask_is_delegatable || "N",
            ttask_iterate_over: params.ttask_iterate_over || null,
            ttask_is_rejectable: params.ttask_is_rejectable || "N",
            ttask_is_bulk_completable: params.ttask_is_bulk_completable || "N",
            ttask_assesment_tvar_id: params.ttask_assesment_tvar_id || null,
            ttask_assesment_ttask_id: params.ttask_assesment_ttask_id || null,
            ttask_assesment_orgstr_id: params.ttask_assesment_orgstr_id || null,
            ttask_enot_body: params.ttask_enot_body || null,
            ttask_enot_subject: params.ttask_enot_subject || null,
            // ENOT TARGET
            ttask_enot_tgt_type: params.ttask_enot_tgt_type || null,
            ttask_enot_tgt: params.ttask_enot_tgt || null,
            ttask_enot_tgt_ttask_id: params.ttask_enot_tgt_ttask_id || null,
            ttask_enot_tgt_orgstr_id: params.ttask_enot_tgt_orgstr_id || null,
            ttask_enot_tgt_role_id: params.ttask_enot_tgt_role_id || null,
            ttask_enot_tgt_tvar_id: params.ttask_enot_tgt_tvar_id || null,
            // ENOT REPLY
            ttask_enot_reply_type: params.ttask_enot_reply_type || null,
            ttask_enot_reply_target: params.ttask_enot_reply_target || null,
            ttask_enot_reply_ttask_id: params.ttask_enot_reply_ttask_id || null,
            ttask_enot_reply_orgstr_id:
                params.ttask_enot_reply_orgstr_id || null,
            ttask_enot_reply_role_id: params.ttask_enot_reply_role_id || null,
            ttask_enot_reply_tvar_id: params.ttask_enot_reply_tvar_id || null,
            // ENOT BLIND
            ttask_enot_blind_type: params.ttask_enot_blind_type || null,
            ttask_enot_blind_target: params.ttask_enot_blind_target || null,
            ttask_enot_blind_ttask_id: params.ttask_enot_blind_ttask_id || null,
            ttask_enot_blind_orgstr_id:
                params.ttask_enot_blind_orgstr_id || null,
            ttask_enot_blind_role_id: params.ttask_enot_blind_role_id || null,
            ttask_enot_blind_tvar_id: params.ttask_enot_blind_tvar_id || null,
            // COPY
            ttask_enot_copy_type: params.ttask_enot_copy_type || null,
            ttask_enot_copy_target: params.ttask_enot_copy_target || null,
            ttask_enot_copy_ttask_id: params.ttask_enot_copy_ttask_id || null,
            ttask_enot_copy_orgstr_id: params.ttask_enot_copy_orgstr_id || null,
            ttask_enot_copy_role_id: params.ttask_enot_copy_role_id || null,
            ttask_enot_copy_tvar_id: params.ttask_enot_copy_tvar_id || null,

            // LANGUAGE
            ttask_enot_external_language:
                params.ttask_enot_external_language || null,
        },
    ];

    // Add mutations
    globalThis.dynamicConfig.langs.forEach((lang) => {
        body[0][`ttask_enot_body_${lang}`] = params[`ttask_enot_body_${lang}`];
        body[0][`ttask_enot_subject_${lang}`] =
            params[`ttask_enot_subject_${lang}`];
        body[0][`ttask_description_${lang}`] =
            params[`ttask_description_${lang}`];
        body[0][`ttask_name_${lang}`] = params[`ttask_name_${lang}`];
    });

    if (params.hasOwnProperty("ttask_petri_net_inputn")) {
        body[0].ttask_petri_net_inputn = params.ttask_petri_net_inputn;
    }
    if (params.hasOwnProperty("ttask_event")) {
        body[0].ttask_event = params.ttask_event;
    }
    if (params.hasOwnProperty("ttask_subprocess_tproc_id")) {
        body[0].ttask_subprocess_tproc_id = params.ttask_subprocess_tproc_id;
    }
    if (params.hasOwnProperty("ttask_subprocess_tproc_version")) {
        body[0].ttask_subprocess_tproc_version =
            params.ttask_subprocess_tproc_version;
    }
    if (params.hasOwnProperty("ttask_invoke_event")) {
        body[0].ttask_invoke_event = params.ttask_invoke_event;
    }
    if (params.hasOwnProperty("subr_mapping")) {
        body[0].subr_mapping = params.subr_mapping;
    }
    if (params.hasOwnProperty("subp_mapping")) {
        body[0].subp_mapping = params.subp_mapping;
    }
    const res = await agent
        .post("/template-tasks")
        .set("Authorization", authorizationTokenAdmin)
        .send(body)
        .expect(200);
    return res.body.id;
}

export async function getProcessAttachments(iProcId, token) {
    return await get(`/processes/${iProcId}/attachments`, token);
}

export async function getProcessAttachmentsCount(
    iProcId,
    token,
): Promise<number> {
    if (!UtilsService.isNumericString(iProcId)) {
        throw new Error(
            `Is invalid iProcId = ${iProcId} in api.getProcessAttachmentsCount`,
        );
    }

    const res = await agent
        .get(`/processes/${iProcId}/attachments`)
        .set("Authorization", token)
        .expect(200);
    return res.body.total_count;
}

export async function readFile(path) {
    return await fs.promises.readFile(path, "utf8");
}

export async function createDt(dtName, token): Promise<number> {
    const body = {
        dt_name: dtName,
        dt_public: true,
    };

    const res = await agent
        .post("/dyn-table")
        .send(body)
        .set("Authorization", token)
        .expect(200);
    return res.body.id;
}

export async function importDtCsv(dtId, csvName, token): Promise<number> {
    const csvData = await readFile(`${__dirname}/../api/csv/${csvName}`);
    const body = {
        data: csvData,
        delimiter: ",",
        firstLineColNames: true,
        append: false,
    };

    const res = await agent
        .post(`/dyn-table/${dtId}/importCsv`)
        .send(body)
        .set("Authorization", token)
        .expect(200);
    return res.body.id;
}

export async function createDynamicTable(
    dtName,
    csvName,
    token,
): Promise<number> {
    const dtId = await createDt(dtName, token);
    return await importDtCsv(dtId, csvName, token);
}

export async function deleteDynamicTable(dtId, token): Promise<any> {
    await agent
        .delete(`/dyn-table/${dtId}`)
        .set("Authorization", token)
        .expect(200);
}

export async function handOverTask(iTaskId, userId): Promise<number> {
    const body = {
        itask_user_id: userId,
    };
    return await post(
        `/tasks/assign-to/${iTaskId}`,
        authorizationTokenAdmin,
        body,
    );
}

export async function assignToMe(iTaskId, userId): Promise<number> {
    const body = {
        itask_user_id: userId,
    };
    return await post(
        `/tasks/assign-to-me/${iTaskId}`,
        authorizationTokenAdmin,
        body,
    );
}

export async function delegateTask(iTaskId, userId, token): Promise<number> {
    const body = {
        itask_id: iTaskId,
        delegate_user_id: userId,
    };
    return await post("/tasks/delegate", token, body);
}

/*
var d = new Date();
 d.setDate(d.getDate()-5);
 */

export async function setManager(orgstrId, userId): Promise<number> {
    const body = {
        manager_user_id: userId,
        orgstr_id: `${orgstrId}`,
    };

    return await post("/org-units", authorizationTokenAdmin, body);
}

export async function setVice(
    userId,
    viceId,
    from = new Date(new Date().setDate(new Date().getDate() - 1)),
    to = new Date(new Date().setDate(new Date().getDate() + 1)),
): Promise<number> {
    const body = {
        from,
        to,
        user_id: userId,
        user_id_vice: viceId,
        uv_enabled: 1,
        view_only: "N",
    };

    return await post("/vice", authorizationTokenAdmin, body);
}

export async function switchVice(userId, token, viceId) {
    return await postFullResponse(`/logged-user/set-vice/${userId}`, token, {
        uv_id: viceId,
    });
}

export async function getProcessTasks(
    iProcId,
    token = authorizationTokenAdmin,
) {
    return await get(`/processes/${iProcId}/tasks`, token);
}

export async function getTaskVariables(
    itaskId,
    token = authorizationTokenAdmin,
) {
    return await get(`/tasks/${itaskId}/variables`, token);
}

export async function saveTaskVariables(
    itaskId,
    body,
    token = authorizationTokenAdmin,
): Promise<number> {
    return await post(`/tasks/${itaskId}/variables`, token, body);
}

// to know how to change the next password, "generator class would be better"
let counter = 0;
export function generatePassword(rules = {}): string {
    const chars = {
        digits: 1,
        lowercase: "a",
        uppercase: "A",
        symbols: "#",
    };

    let password = "";
    const min = rules.min || 0;
    const max = rules.max || Number.MAX_VALUE;

    if (min > max) {
        throw new Exception("min greater than max");
    }

    _.forEach(rules, (value, rule) => {
        if (chars.hasOwnProperty(rule)) {
            password = _.padEnd(password, password.length + value, chars[rule]);
        }
    });

    const alphaCount = rules.lowercase || 0 + rules.uppercase || 0;
    if (rules.letters && rules.letters > alphaCount) {
        const diffCount = rules.letters - alphaCount;
        password = _.padEnd(
            password,
            password.length + diffCount,
            chars.lowercase,
        );
    }

    password = _.padEnd(password, min, chars.lowercase);

    if (password.length > max) {
        throw new Exception("max limit is too small");
    }

    const rounds = Math.floor(counter / password.length);
    for (let i = rounds; i >= 0; i -= 1) {
        const index = (counter + rounds) % password.length;
        const replacement = String.fromCharCode(password.charCodeAt(index) + 1);
        const leftPart = password.substr(0, index);
        const rightPart = password.substr(index + 1);
        password = `${leftPart}${replacement}${rightPart}`;
    }

    counter += 1;
    return password || `no_validations_${Math.random()}`;
}

export async function updateXpiNote(xpiId, xpiNote): Promise<number> {
    return await post(
        `/xml-process-import-audit/${xpiId}/set-note`,
        authorizationTokenAdmin,
        {
            xpi_note: xpiNote,
        },
    );
}

export async function listXpiAudit() {
    return await get(`/xml-process-import/audit`, authorizationTokenAdmin);
}

export async function createEvent(
    eventName,
    description = null,
): Promise<number> {
    const body = {
        evedef_name: eventName,
        evedef_description: description,
    };

    return await post("/event-definition", authorizationTokenAdmin, body);
}

export async function createEventRule(
    eventDefinitionId,
    ruleDefinitionType,
    ruleDefinitionValue,
    ruleDefinitionStatus = "ACTIVE",
): Promise<number> {
    const body = {
        evedef_id: eventDefinitionId,
        rdef_type: ruleDefinitionType,
        rdef_value: ruleDefinitionValue,
        rdef_status: ruleDefinitionStatus,
        org_id: 1,
    };

    return await post(
        `/event-definition/${eventDefinitionId}/rule-definition`,
        authorizationTokenAdmin,
        body,
    );
}

export async function createEventRuleParam(
    eventDefinitionId: number,
    ruleDefinitionId: number,
    ruleParams: RuleDefinitionParameter[],
    ruleVariables: RuleDefinitionVariables,
): Promise<number> {
    const body = {
        evedef_id: eventDefinitionId,
        rdef_id: ruleDefinitionId,
        rule_params: ruleParams,
        rule_variables: ruleVariables,
    };

    return await post(
        `/event-definition/${eventDefinitionId}/rule-definition`,
        authorizationTokenAdmin,
        body,
    );
}

export function normalizeHtmlContent(html: string): string {
    return html
        .replace(/<br\s*\/?>\s*/gi, "<br />") // normalize <br>, <br/>, <br /> and remove trailing spaces
        .replace(/\s{2,}/g, " ") // collapse multiple spaces
        .replace(/>\s+</g, "><") // remove spaces between tags
        .replace(/\n/g, "") // remove newlines (optional)
        .replace(/\t/g, "") // remove tabs (optional)
        .trim(); // final trim
}

export function updateInstanceProcess(
    iProcId: number,
    body: Record<string, any>,
): Promise<number> {
    return post(
        `/service-operations/processes/list?filter=iproc_id<eq>"${iProcId}"`,
        authorizationTokenAdmin,
        body,
    );
}
