import { assert } from "chai";
import * as util from "../utils/api";

import moment from "moment-timezone";

const genRanHex = (size: number): string =>
    [...Array(size)]
        .map(() => Math.floor(Math.random() * 16).toString(16))
        .join("");

const cvName1 = `_${genRanHex(6)}`;
const cvName2 = `_2${genRanHex(6)}`;
const cvName3 = `_3${genRanHex(6)}`;
const cvName4 = `_4${genRanHex(6)}`;
const cvName5 = `_5${genRanHex(6)}`;
const tvarName = `_${genRanHex(6)}`;
const userPassword = "MoreSecure1Password";
let tProcId: util.TemplateProcessId;
let headerId2: number;
let idUserA: number;
let idUserB: number;
let idUserC: number;
let orgUnitId: number;
let roleId: number;
let tVarId1: number;
let tVarId3: number;
let tVarId4: number;
let tVarId5: number;
let tVarId6: number;
let tVarId7: number;
let customViewId1: number;
let customViewId2: number;
let customViewId3: number;
let customViewId4: number;
let customViewId5: number;
let authorizationTokenUserA: string;
let authorizationTokenUserB: string;
let authorizationTokenUserC: string;
let authorizationTokenUserD: string;
const tomorrow = moment().add(1, "day").toISOString();
const minus3days = moment().subtract(3, "day").toISOString();

describe("CustomViews: ", () => {
    before(async () => {
        globalThis.dynamicConfig.test.tests.mails = {};
        globalThis.dynamicConfig.test.tests.mailId = 0;

        tProcId = await util.createTemplateProcess(
            `TstTmplProcess${cvName1}`,
            "Description...",
        );
        await util.createHeader(tProcId);
        tVarId1 = await util.createTemplateVariable(
            tProcId,
            "testVarOne",
            "T",
            null,
        );
        await util.createTemplateVariable(tProcId, "testVarTwo", "T", null);
        tVarId3 = await util.createTemplateVariable(
            tProcId,
            "testVarThree",
            "T",
            null,
        );
        tVarId4 = await util.createTemplateVariable(
            tProcId,
            "testVarFour",
            "T",
            null,
        );
        tVarId5 = await util.createTemplateVariable(
            tProcId,
            "testVarDateTomorrow",
            "T",
            null,
        );
        tVarId6 = await util.createTemplateVariable(
            tProcId,
            "testVarDate-3",
            "T",
            null,
        );
        tVarId7 = await util.createTemplateVariable(
            tProcId,
            "testVarNum",
            "N",
            null,
        );
        idUserA = await util.createUser(
            `Arnost${cvName1}`,
            `Arnost${cvName1}`,
            `Apac${cvName1}`,
            userPassword,
        );
        orgUnitId = await util.createOrgUnit(
            `orgStruct${cvName1}`,
            [idUserA],
            1,
            idUserA,
        );
        idUserB = await util.createUser(
            `Arnost2${cvName1}`,
            `Arnost2${cvName1}`,
            `Apac2${cvName1}`,
            userPassword,
        );
        roleId = await util.createRole(`role${cvName1}`, [idUserB]);
        idUserC = await util.createUser(
            `Arnost3${cvName1}`,
            `Arnost3${cvName1}`,
            `Apac3${cvName1}`,
            userPassword,
        );
        await util.createUser(
            `Arnost4${cvName1}`,
            `Arnost4${cvName1}`,
            `Apac4${cvName1}`,
            userPassword,
        );
        await util.createTtask(tProcId, `test1${cvName1}`);
        await util.createTtask(tProcId, `test2${cvName1}`);
        await util.createTtask(tProcId, `test3${cvName1}`);
        authorizationTokenUserA = await util.getValidHash(
            `Arnost${cvName1}`,
            userPassword,
        );
        await util.activateProcess(tProcId, authorizationTokenUserA);
        await util.activateProcess(tProcId, authorizationTokenUserA);
        await util.updateTemplateVariable(
            tProcId,
            tVarId1,
            "nope",
            "T",
            `name1${tvarName}`,
        );
        await util.updateTemplateVariable(
            tProcId,
            tVarId3,
            `Apac${cvName1} Arnost${cvName1}`,
            "T",
            `name2${tvarName}`,
        );
        await util.updateTemplateVariable(
            tProcId,
            tVarId4,
            new Date(),
            "D",
            `name3${tvarName}`,
        );
        await util.updateTemplateVariable(
            tProcId,
            tVarId5,
            tomorrow,
            "D",
            `name4${tvarName}`,
        );
        await util.updateTemplateVariable(
            tProcId,
            tVarId6,
            minus3days,
            "D",
            `name5${tvarName}`,
        );
        await util.updateTemplateVariable(
            tProcId,
            tVarId7,
            500,
            "N",
            `name6${tvarName}`,
        );
        await util.activateProcess(tProcId, authorizationTokenUserA);
        const headers = await util.findHeaders(tProcId);
        headerId2 = headers[1].id;
        await util.activateProcess(tProcId, authorizationTokenUserA, headerId2);
        authorizationTokenUserB = await util.getValidHash(
            `Arnost2${cvName1}`,
            userPassword,
        );
        authorizationTokenUserC = await util.getValidHash(
            `Arnost3${cvName1}`,
            userPassword,
        );
        authorizationTokenUserD = await util.getValidHash(
            `Arnost4${cvName1}`,
            userPassword,
        );
    });

    it("TC0039 | Create custom view1", async () => {
        const headers = await util.findHeaders(tProcId);
        const body = {
            cv_name: `test custom view${cvName1}`,
            cv_iproc_status_list: "A",
            header_id: headers[0].id,
            columns: [
                {
                    tvar_id: -1,
                    cvc_name: "Název případu",
                },
                {
                    tvar_id: -2,
                    cvc_name: "Popis",
                },
                {
                    tvar_id: -3,
                    cvc_name: "Iniciátor",
                },
                {
                    tvar_id: -4,
                    cvc_name: "Zadáno",
                },
            ],
            cv_filter: [
                {
                    column: `V${tVarId1}`,
                    value: "nope",
                    op: "like",
                    enabled: true,
                },
            ],
            users_share: [idUserC],
            roles_share: [roleId],
            orgs_share: [orgUnitId],
            cv_order: "V-1",
            cv_sort: "asc",
            include_similar_processes: "N",
        };

        const res = await agent
            .post("/custom-views/")
            .set("Authorization", authorizationTokenUserA)
            .send(body)
            .expect(200);
        customViewId1 = res.body.id;
        assert.isTrue(res.body.result);
        assert.equal(res.status, 200);
    });

    it("TC0040 | Check filtered items of custom view1", async () => {
        const res = await agent
            .get(`/cv/${customViewId1}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
        assert.equal(res.status, 200);
    });

    it("TC0041 | User A should see custom view1 shared by organization unit", async () => {
        const res = await agent
            .get("/custom-views/mine")
            .set("Authorization", authorizationTokenUserA)
            .expect(200);

        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
    });

    it("TC0042 | User B should see custom view1 shared by role", async () => {
        const res = await agent
            .get("/custom-views/mine")
            .set("Authorization", authorizationTokenUserB)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
    });

    it("TC0043 | User C should see custom view1 shared by user", async () => {
        const res = await agent
            .get("/custom-views/mine")
            .set("Authorization", authorizationTokenUserC)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
    });

    it("TC0044 | User D should not see custom view1", async () => {
        const res = await agent
            .get("/custom-views/mine")
            .set("Authorization", authorizationTokenUserD)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 0);
    });

    it("TC0045 | Create custom view2", async () => {
        const body = {
            cv_name: `test custom view${cvName2}`,
            cv_iproc_status_list: "A",
            header_id: headerId2,
            columns: [
                {
                    tvar_id: -1,
                    cvc_name: "Název případu",
                },
                {
                    tvar_id: -2,
                    cvc_name: "Popis",
                },
                {
                    tvar_id: -3,
                    cvc_name: "Iniciátor",
                },
                {
                    tvar_id: -4,
                    cvc_name: "Zadáno",
                },
            ],
            cv_filter: [
                {
                    column: `V${tVarId1}`,
                    value: "nope",
                    op: "like",
                    enabled: true,
                },
            ],
            users_share: [idUserC],
            roles_share: [roleId],
            orgs_share: [orgUnitId],
            cv_order: "V-1",
            cv_sort: "asc",
            include_similar_processes: "Y",
        };

        const res = await agent
            .post("/custom-views/")
            .set("Authorization", authorizationTokenUserA)
            .send(body)
            .expect(200);
        customViewId2 = res.body.id;
        assert.isTrue(res.body.result);
        assert.equal(res.status, 200);
    });

    it("TC0046 | Include similar processes in custom view2", async () => {
        const res = await agent
            .get(`/cv/${customViewId2}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 2);
        assert.equal(res.status, 200);
    });

    it("TC0047 | FAIL - Delete custom view1 as user C", async () => {
        const res = await agent
            .delete(`/custom-views/${customViewId1}`)
            .set("Authorization", authorizationTokenUserC)
            .expect(400);

        assert.exists(res.body.error);
        assert.equal(res.body.error.message, `Lack of permissions.`);
        assert.equal(
            res.body.error.codeName,
            "LACK_OF_PERMISSIONS_CUSTOM_VIEW",
        );
        assert.equal(res.status, 400);
    });

    it("TC0048 | Delete custom view1 as user A (CV owner)", async () => {
        const res = await agent
            .delete(`/custom-views/${customViewId1}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.equal(res.status, 200);
        assert.isTrue(res.body.result);
    });

    it("TC0049 | Custom view1 should be deleted", async () => {
        const res = await agent
            .get("/custom-views/mine")
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
    });

    it("TC0050 | Delete custom view2 as Admin", async () => {
        const res = await agent
            .delete(`/custom-views/${customViewId2}`)
            .set("Authorization", authorizationTokenAdmin)
            .expect(200);
        assert.equal(res.status, 200);
        assert.isTrue(res.body.result);
    });

    it("TC0051 | Custom view2 should be deleted", async () => {
        const res = await agent
            .get("/custom-views/mine")
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 0);
    });

    it("TC0052 | Create custom view3", async () => {
        const body = {
            cv_name: `test custom view${cvName3}`,
            cv_iproc_status_list: "A",
            header_id: headerId2,
            columns: [
                {
                    tvar_id: -1,
                    cvc_name: "Název případu",
                },
                {
                    tvar_id: -2,
                    cvc_name: "Popis",
                },
                {
                    tvar_id: -3,
                    cvc_name: "Iniciátor",
                },
                {
                    tvar_id: -4,
                    cvc_name: "Zadáno",
                },
                {
                    tvar_id: tVarId3,
                    cvc_name: "testVarThree",
                },
            ],
            cv_filter: [
                {
                    column: `V${tVarId3}`,
                    value: "#user#",
                    op: "eq",
                    enabled: true,
                },
            ],
            users_share: [idUserC],
            roles_share: [roleId],
            orgs_share: [orgUnitId],
            cv_order: "V-1",
            cv_sort: "asc",
            include_similar_processes: "Y",
        };

        const res = await agent
            .post("/custom-views/")
            .set("Authorization", authorizationTokenUserA)
            .send(body)
            .expect(200);
        customViewId3 = res.body.id;
        assert.isTrue(res.body.result);
        assert.equal(res.status, 200);
    });

    it("TC0053 | Check filtered items of custom view3 {#user#}", async () => {
        const res = await agent
            .get(`/cv/${customViewId3}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);

        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 2);
        assert.equal(res.status, 200);
    });

    it("TC0054 | Create custom view4", async () => {
        const headers = await util.findHeaders(tProcId);

        const body = {
            cv_name: `test custom view${cvName4}`,
            cv_iproc_status_list: "A",
            header_id: headers[0].id,
            columns: [
                {
                    tvar_id: -1,
                    cvc_name: "Název případu",
                },
                {
                    tvar_id: -2,
                    cvc_name: "Popis",
                },
                {
                    tvar_id: -3,
                    cvc_name: "Iniciátor",
                },
                {
                    tvar_id: -4,
                    cvc_name: "Zadáno",
                },
                {
                    tvar_id: tVarId4,
                    cvc_name: "testVarFour",
                },
            ],
            cv_filter: [
                { op: "(", enabled: true },
                {
                    column: `V${tVarId4}`,
                    value: "#now#",
                    op: "eq",
                    enabled: true,
                },
                { op: ")", enabled: true },
                { op: "and", enabled: true },
                { op: "(", enabled: true },
                {
                    column: "V-1",
                    value: null,
                    op: "isnn",
                    enabled: true,
                },
                { op: ")", enabled: true },
            ],
            users_share: [idUserC],
            roles_share: [roleId],
            orgs_share: [orgUnitId],
            cv_order: "V-1",
            cv_sort: "asc",
            include_similar_processes: "N",
        };

        const res = await agent
            .post("/custom-views/")
            .set("Authorization", authorizationTokenUserA)
            .send(body)
            .expect(200);
        customViewId4 = res.body.id;
        assert.isTrue(res.body.result);
        assert.equal(res.status, 200);
    });

    it("TC0055 | Check filtered items of custom view4 {#now#}", async () => {
        const res = await agent
            .get(`/cv/${customViewId4}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
        assert.equal(res.status, 200);
    });

    it("TC0056 | Create custom view5", async () => {
        const headers = await util.findHeaders(tProcId);

        const body = {
            cv_name: `test custom view${cvName5}`,
            cv_iproc_status_list: "A",
            header_id: headers[0].id,
            columns: [
                {
                    tvar_id: -1,
                    cvc_name: "Název případu",
                },
                {
                    tvar_id: -2,
                    cvc_name: "Popis",
                },
                {
                    tvar_id: -3,
                    cvc_name: "Iniciátor",
                },
                {
                    tvar_id: -4,
                    cvc_name: "Zadáno",
                },
                {
                    tvar_id: tVarId5,
                    cvc_name: "testVarDateTomorrow",
                },
                {
                    tvar_id: tVarId6,
                    cvc_name: "testVarDate-3",
                },
            ],
            cv_filter: [
                {
                    column: `V${tVarId5}`,
                    value: "#tomorrow#",
                    op: "eq",
                    enabled: true,
                },
                {
                    column: `V${tVarId6}`,
                    value: "#now-4#",
                    op: "gt",
                    enabled: true,
                },
                {
                    column: `V${tVarId5}`,
                    value: "#yesterday#",
                    op: "ne",
                    enabled: true,
                },
            ],
            users_share: [idUserC],
            roles_share: [roleId],
            orgs_share: [orgUnitId],
            cv_order: "V-1",
            cv_sort: "asc",
            include_similar_processes: "N",
        };

        const res = await agent
            .post("/custom-views/")
            .set("Authorization", authorizationTokenUserA)
            .send(body)
            .expect(200);
        customViewId5 = res.body.id;
        assert.isTrue(res.body.result);
        assert.equal(res.status, 200);
    });

    it("TC0057 | Check filtered items of custom view5 {#tomorrow#}, {#yesterday#}, {#now-3#}", async () => {
        const res = await agent
            .get(`/cv/${customViewId5}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);
        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 1);
        assert.equal(res.status, 200);
    });

    it("TC0058 | Check custom view5 with tasks for dashboard", async () => {
        const res = await agent
            .get(`/cv/with-tasks/${customViewId5}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200);

        assert.exists(res.body.items);
        assert.equal(res.body.items.length, 3);
        assert.property(res.body.items[0], "itask_name");
        assert.equal(res.status, 200);
    });

    describe("Filtering by number values [420, 643]", () => {
        let viewId: number;
        before(async () => {
            const headerId = await util.createHeader(tProcId);
            const iprocId1 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );
            const iprocId2 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );
            await util.updateVariable(iprocId1, tVarId7, 643);
            await util.updateVariable(iprocId2, tVarId7, 420);

            const body = {
                cv_name: `test custom view${cvName1}`,
                cv_iproc_status_list: "A",
                header_id: headerId,
                columns: [
                    {
                        tvar_id: -1,
                        cvc_name: "Název případu",
                    },
                    {
                        tvar_id: -2,
                        cvc_name: "Popis",
                    },
                    {
                        tvar_id: -3,
                        cvc_name: "Iniciátor",
                    },
                    {
                        tvar_id: -4,
                        cvc_name: "Zadáno",
                    },
                    {
                        tvar_id: tVarId7,
                        cvc_name: "NumValue",
                    },
                ],
                cv_filter: [],
                users_share: [idUserC],
                roles_share: [roleId],
                orgs_share: [orgUnitId],
                cv_order: "V-1",
                cv_sort: "asc",
                include_similar_processes: "N",
            };

            const res = await agent
                .post("/custom-views/")
                .set("Authorization", authorizationTokenUserA)
                .send(body)
                .expect(200);
            viewId = res.body.id;
            assert.isTrue(res.body.result);
            assert.equal(res.status, 200);
        });

        it("Simple operator < 500", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V${tVarId7}<lt>"500"`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 1);
            assert.equal(res.status, 200);
        });

        it("Simple operator < 900", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V${tVarId7}<lt>"900"`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 2);
            assert.equal(res.status, 200);
        });

        it("Simple operator > 500", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V${tVarId7}<gt>"500"`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 1);
            assert.equal(res.status, 200);
        });

        it("Simple operator > 900", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V${tVarId7}<gt>"900"`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 0);
            assert.equal(res.status, 200);
        });
    });

    describe("Default filter with dates and OR operator", () => {
        let viewId: number;
        const filterDate = "2025-05-28T22:00:00.000Z";
        before(async () => {
            const headerId = await util.createHeader(tProcId);
            const iprocId1 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );
            const iprocId2 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );
            const iprocId3 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );

            await util.updateInstanceProcess(iprocId2, {
                data: {
                    IPROC_ACTUAL_START_DATE: "2025-04-28T22:00:00.000Z",
                },
            });
            await util.updateInstanceProcess(iprocId3, {
                data: {
                    IPROC_ACTUAL_START_DATE: filterDate,
                },
            });

            viewId = await util.createCustomView(
                `test custom view default filter${cvName1}`,
                tProcId,
                headerId,
                authorizationTokenUserA,
                [
                    {
                        tvar_id: -1,
                        cvc_name: "Název případu",
                    },
                    {
                        tvar_id: -2,
                        cvc_name: "Popis",
                    },
                    {
                        tvar_id: -3,
                        cvc_name: "Iniciátor",
                    },
                    {
                        tvar_id: -4,
                        cvc_name: "Zadáno",
                    },
                    {
                        tvar_id: tVarId7,
                        cvc_name: "NumValue",
                    },
                ],
                [
                    {
                        column: "V-4",
                        enabled: true,
                        op: "eq",
                        value: new Date().toISOString(),
                    },
                    {
                        enabled: true,
                        op: "or",
                    },
                    {
                        column: "V-4",
                        enabled: true,
                        op: "eq",
                        value: filterDate,
                    },
                ],
                "asc",
                "V-1",
                "A",
                [roleId],
                [orgUnitId],
                [idUserC],
            );
        });

        it("should filter out one process", async () => {
            const res = await agent
                .get(`/cv/${viewId}`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 2);
        });

        it("query filter on date <gt>", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V-4<gt>"${filterDate}"`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 1);
        });

        it("query filter on date <lt>", async () => {
            const res = await agent
                .get(
                    `/cv/${viewId}?filter=V-4<lt>"${new Date().toISOString()}"`,
                )
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 1);
        });

        it("query filter on date is null", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V-4<isn>""`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 0);
        });

        it("query filter on date is not null", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V-4<isnn>""`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 2);
        });
    });

    describe("Filter by date", () => {
        let viewId: number;
        const filterDate = "2025-05-28T22:00:00.000Z";
        before(async () => {
            const headerId = await util.createHeader(tProcId);
            const iprocId1 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );
            const iprocId2 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );
            const iprocId3 = await util.activateProcess(
                tProcId,
                authorizationTokenUserA,
                headerId,
            );

            await util.updateInstanceProcess(iprocId2, {
                data: {
                    IPROC_ACTUAL_START_DATE: "2025-04-28T22:00:00.000Z",
                },
            });
            await util.updateInstanceProcess(iprocId3, {
                data: {
                    IPROC_ACTUAL_START_DATE: filterDate,
                },
            });

            viewId = await util.createCustomView(
                `test custom view default filter${cvName1}`,
                tProcId,
                headerId,
                authorizationTokenUserA,
                [
                    {
                        tvar_id: -1,
                        cvc_name: "Název případu",
                    },
                    {
                        tvar_id: -2,
                        cvc_name: "Popis",
                    },
                    {
                        tvar_id: -3,
                        cvc_name: "Iniciátor",
                    },
                    {
                        tvar_id: -4,
                        cvc_name: "Zadáno",
                    },
                    {
                        tvar_id: tVarId7,
                        cvc_name: "NumValue",
                    },
                ],
                [],
                "asc",
                "V-1",
                "A",
                [roleId],
                [orgUnitId],
                [idUserC],
            );
        });

        it("should filter out one process", async () => {
            const res = await agent
                .get(`/cv/${viewId}`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 3);
        });

        it("query filter on date <gt>", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V-4<gt>"${filterDate}"`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 1);
        });

        it("query filter on date <lt>", async () => {
            const res = await agent
                .get(
                    `/cv/${viewId}?filter=V-4<lt>"${new Date().toISOString()}"`,
                )
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 2);
        });

        it("query filter on date is null", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V-4<isn>""`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 0);
        });

        it("query filter on date is not null", async () => {
            const res = await agent
                .get(`/cv/${viewId}?filter=V-4<isnn>""`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);
            assert.equal(res.status, 200);
            assert.exists(res.body.items);
            assert.equal(res.body.items.length, 3);
        });
    });

    describe("Changing the owner of the view", () => {
        it("should create a custom view as user A and then change to ownershiop of the view to user B", async () => {
            const body: Record<string, any> = {
                cv_name: `test custom view${cvName2}`,
                cv_iproc_status_list: "A",
                header_id: headerId2,
                columns: [
                    {
                        tvar_id: -1,
                        cvc_name: "Název případu",
                    },
                    {
                        tvar_id: -2,
                        cvc_name: "Popis",
                    },
                    {
                        tvar_id: -3,
                        cvc_name: "Iniciátor",
                    },
                    {
                        tvar_id: -4,
                        cvc_name: "Zadáno",
                    },
                ],
                cv_filter: [
                    {
                        column: `V${tVarId1}`,
                        value: "nope",
                        op: "like",
                        enabled: true,
                    },
                ],
                users_share: [idUserC],
                roles_share: [roleId],
                orgs_share: [orgUnitId],
                cv_order: "V-1",
                cv_sort: "asc",
                include_similar_processes: "Y",
            };
            const { body: resOneBody } = await agent
                .post("/custom-views")
                .set("Authorization", authorizationTokenUserA)
                .send(body)
                .expect(200);

            const { body: resTwoBody } = await agent
                .get(`/custom-views/${resOneBody.id}`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);

            body.cv_id = resTwoBody.id;
            body.cv_user_id = idUserB;

            await agent
                .post("/custom-views")
                .set("Authorization", authorizationTokenUserA)
                .send(body)
                .expect(200);

            const { body: resThreeBody } = await agent
                .get(`/custom-views/${resOneBody.id}`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);

            assert.equal(resThreeBody.cv_user_id, idUserB);
        });
        it("only a view owner or admin can change the ownership of the view", async () => {
            const body: Record<string, any> = {
                cv_name: `test custom view${cvName2}`,
                cv_iproc_status_list: "A",
                header_id: headerId2,
                columns: [
                    {
                        tvar_id: -1,
                        cvc_name: "Název případu",
                    },
                    {
                        tvar_id: -2,
                        cvc_name: "Popis",
                    },
                    {
                        tvar_id: -3,
                        cvc_name: "Iniciátor",
                    },
                    {
                        tvar_id: -4,
                        cvc_name: "Zadáno",
                    },
                ],
                cv_filter: [
                    {
                        column: `V${tVarId1}`,
                        value: "nope",
                        op: "like",
                        enabled: true,
                    },
                ],
                users_share: [idUserC],
                roles_share: [roleId],
                orgs_share: [orgUnitId],
                cv_order: "V-1",
                cv_sort: "asc",
                include_similar_processes: "Y",
            };
            const { body: resOneBody } = await agent
                .post("/custom-views/")
                .set("Authorization", authorizationTokenUserA)
                .send(body)
                .expect(200);

            const { body: resTwoBody } = await agent
                .get(`/custom-views/${resOneBody.id}`)
                .set("Authorization", authorizationTokenUserA)
                .expect(200);

            body.cv_id = resTwoBody.id;
            body.cv_user_id = idUserB;

            const { body: resThreeBody } = await agent
                .post("/custom-views")
                .set("Authorization", authorizationTokenUserB)
                .send(body)
                .expect(400);

            assert.equal(
                resThreeBody.error.message,
                `Lack of permissions. Contact Apac${cvName1} Arnost${cvName1}.`,
            );
            assert.equal(
                resThreeBody.error.codeName,
                "LACK_OF_PERMISSIONS_CUSTOM_VIEW",
            );

            await agent
                .post("/custom-views")
                .set("Authorization", globalThis.authorizationTokenAdmin)
                .send(body)
                .expect(200);

            const { body: resFourBody } = await agent
                .get(`/custom-views/${resOneBody.id}`)
                .set("Authorization", authorizationTokenUserB)
                .expect(200);

            assert.equal(resFourBody.cv_user_id, idUserB);
        });
    });
});
