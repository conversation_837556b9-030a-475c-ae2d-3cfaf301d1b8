// @ts-nocheck

import * as util from "../utils/api";

import should from "should";
import _ from "lodash";

const genRanHex = (size: number): string =>
    [...Array(size)]
        .map(() => Math.floor(Math.random() * 16).toString(16))
        .join("");

const name = `exp_imp_events_vars_map${genRanHex(6)}`;
const userPassword = "MoreSecure1Password";
let idUserA;
let authorizationTokenUserA;
let templateImport;
let templateImportVars;
let tProcIdImportVars;
let tProcIdImport;
let tProcIdImportSup;
let convertedImportMapping;
let convertedImportMappingSub;
let convertedImportMappingSubr;
let tTaskId1_2Import;
let tTaskId1_3Import;
let templateExport;
let tProcIdCopyVars;
let tProcIdCopy;
let tProcIdCopySup;
let tTaskId1_2Copy;
let tTaskId1_3Copy;

function getTemplateVariablesObj(tProcId) {
    return new Promise((resolve, reject) => {
        util.get(
            `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables`,
            authorizationTokenUserA,
        )
            .then((body) => {
                const tProcVars = {};
                body.items.forEach((i) => {
                    tProcVars[i.id] = i.tvar_name;
                });
                resolve(tProcVars); // { '2225': 'text', '2226': 'num', ... }
            })
            .catch((err) => {
                reject(err);
            });
    });
}
/** Convert
 * var_id and tvar_id_dest
 * in mapping object to variable name
 * tvar_id: 8408 -> tvar_id: date
 */
function convertMapping(paramsArr, varsObj) {
    paramsArr.forEach((p) => {
        if (p.tvar_id !== null) {
            p.tvar_id = varsObj[p.tvar_id];
        }
        if (p.tvar_id_dest !== null) {
            p.tvar_id_dest = varsObj[p.tvar_id_dest];
        }
        delete p.rdef_id;
        delete p.evepar_name;
        delete p.rdefpar_function;
        delete p.org_id;
        delete p.rdefpar_order;
    });

    return paramsArr;
}

describe("Template - export/import - mapping variables - events/sub processes:", () => {
    before(async () => {
        idUserA = await util.createUser(
            `AA_${name}`,
            `A_${name}`,
            `A_${name}`,
            userPassword,
            "A",
            { orgstr_id: 1, manager: [] },
            [-1],
        );
        authorizationTokenUserA = await util.getValidHash(
            `AA_${name}`,
            userPassword,
        );
    });

    // Import
    it("TC0654 | (exp_imp_5 - import) Import the template", (done) => {
        templateImport = {
            dynamic_tables: [],
            events: [
                {
                    evedef_id: 609,
                    evedef_name: name,
                    evedef_description: null,
                    org_id: 1,
                    rules: [
                        {
                            rdef_id: 446,
                            evedef_id: 609,
                            org_global_id: "-1",
                            rdef_type: "EVENTW",
                            rdef_value: "$EVEW_3902",
                            org_id: 1,
                            rdef_status: "ACTIVE",
                            rule_variables: [],
                            rule_params: [
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "date",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5732,
                                    rdefpar_value: "25. 4. 2018",
                                    rdefpar_type: "D",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "dateList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5732,
                                    org_id: 1,
                                    tvar_id_dest: 5736,
                                    rdefpar_value: null,
                                    rdefpar_type: "LD",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "dlu",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5739,
                                    org_id: 1,
                                    tvar_id_dest: 5739,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "docList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5730,
                                    org_id: 1,
                                    tvar_id_dest: 5750,
                                    rdefpar_value: null,
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "num",
                                    evepar_name: "$CALC",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5733,
                                    rdefpar_value: "1+1",
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "numList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5737,
                                    org_id: 1,
                                    tvar_id_dest: 5737,
                                    rdefpar_value: null,
                                    rdefpar_type: "LN",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "num2",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5734,
                                    rdefpar_value: "55",
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "sequence",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5733,
                                    org_id: 1,
                                    tvar_id_dest: 5738,
                                    rdefpar_value: null,
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "textArea",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5730,
                                    org_id: 1,
                                    tvar_id_dest: 5731,
                                    rdefpar_value: null,
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "textList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5730,
                                    org_id: 1,
                                    tvar_id_dest: 5735,
                                    rdefpar_value: null,
                                    rdefpar_type: "LT",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "text",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5730,
                                    rdefpar_value: "cont",
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "dlo",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 7775,
                                    org_id: 1,
                                    tvar_id_dest: 7775,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 446,
                                    rdefpar_name: "dlr",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 7776,
                                    org_id: 1,
                                    tvar_id_dest: 7776,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                            ],
                        },
                    ],
                    import_rules: {
                        action: "CREATE",
                        data: {
                            evedef_name: `${name}_import`,
                        },
                    },
                },
                {
                    evedef_id: 610,
                    evedef_name: "$SUBP_3903",
                    evedef_description:
                        "$SubProcess exp_imp_events_vars_map --> exp_imp_events_vars_map sub-process",
                    org_id: 1,
                    rules: [
                        {
                            rdef_id: 447,
                            evedef_id: 610,
                            org_global_id: null,
                            rdef_type: "PROCESS",
                            rdef_value: "1860.1",
                            org_id: 1,
                            rdef_status: "ACTIVE",
                            rule_variables: [],
                            rule_params: [
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp dlu",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5739,
                                    org_id: 1,
                                    tvar_id_dest: 5749,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp num",
                                    evepar_name: "$CALC",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5744,
                                    rdefpar_value: "1+1",
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp sequence",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5738,
                                    org_id: 1,
                                    tvar_id_dest: 5747,
                                    rdefpar_value: null,
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "$TERMIN",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: null,
                                    rdefpar_value: "5. 4. 2018",
                                    rdefpar_type: "D",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp docList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5750,
                                    org_id: 1,
                                    tvar_id_dest: 5748,
                                    rdefpar_value: null,
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp textArea",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5730,
                                    org_id: 1,
                                    tvar_id_dest: 5742,
                                    rdefpar_value: null,
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp textList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5735,
                                    org_id: 1,
                                    tvar_id_dest: 5745,
                                    rdefpar_value: null,
                                    rdefpar_type: "LT",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp text",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5741,
                                    rdefpar_value: "const",
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp numList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5737,
                                    org_id: 1,
                                    tvar_id_dest: 5751,
                                    rdefpar_value: null,
                                    rdefpar_type: "LN",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp date",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5736,
                                    org_id: 1,
                                    tvar_id_dest: 5743,
                                    rdefpar_value: null,
                                    rdefpar_type: "D",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp dateList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5736,
                                    org_id: 1,
                                    tvar_id_dest: 5746,
                                    rdefpar_value: null,
                                    rdefpar_type: "LD",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp dlo",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 7775,
                                    org_id: 1,
                                    tvar_id_dest: 7777,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 447,
                                    rdefpar_name: "sp dlr",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 7776,
                                    org_id: 1,
                                    tvar_id_dest: 7778,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                            ],
                        },
                    ],
                },
                {
                    evedef_id: 754,
                    evedef_name: "$SUBR_3903",
                    evedef_description:
                        "$SubReturn exp_imp_events_vars_map_sub-process --> exp_imp_events_vars_map",
                    org_id: 1,
                    rules: [
                        {
                            rdef_id: 574,
                            evedef_id: 754,
                            org_global_id: null,
                            rdef_type: "RETURN",
                            rdef_value: "$SUBP_3903",
                            org_id: 1,
                            rdef_status: "ACTIVE",
                            rule_variables: [],
                            rule_params: [
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "date",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5732,
                                    rdefpar_value: "27. 4. 2018",
                                    rdefpar_type: "D",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "sp dateList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5746,
                                    org_id: 1,
                                    tvar_id_dest: 5746,
                                    rdefpar_value: null,
                                    rdefpar_type: "LD",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "dlo",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 7777,
                                    org_id: 1,
                                    tvar_id_dest: 7775,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "dlr",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 7778,
                                    org_id: 1,
                                    tvar_id_dest: 7776,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "dlu",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5749,
                                    org_id: 1,
                                    tvar_id_dest: 5739,
                                    rdefpar_value: null,
                                    rdefpar_type: "DL",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "docList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5748,
                                    org_id: 1,
                                    tvar_id_dest: 5750,
                                    rdefpar_value: null,
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "num",
                                    evepar_name: "$CALC",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5733,
                                    rdefpar_value: "1+1",
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "numList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5744,
                                    org_id: 1,
                                    tvar_id_dest: 5737,
                                    rdefpar_value: null,
                                    rdefpar_type: "LN",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "num2",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5747,
                                    org_id: 1,
                                    tvar_id_dest: 5734,
                                    rdefpar_value: null,
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "sequence",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5744,
                                    org_id: 1,
                                    tvar_id_dest: 5738,
                                    rdefpar_value: null,
                                    rdefpar_type: "N",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "text",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5742,
                                    org_id: 1,
                                    tvar_id_dest: 5730,
                                    rdefpar_value: null,
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "textArea",
                                    evepar_name: "$CONSTANT",
                                    rdefpar_function: null,
                                    tvar_id: null,
                                    org_id: 1,
                                    tvar_id_dest: 5731,
                                    rdefpar_value: "Const",
                                    rdefpar_type: "T",
                                    rdefpar_order: null,
                                },
                                {
                                    rdef_id: 574,
                                    rdefpar_name: "textList",
                                    evepar_name: null,
                                    rdefpar_function: null,
                                    tvar_id: 5745,
                                    org_id: 1,
                                    tvar_id_dest: 5735,
                                    rdefpar_value: null,
                                    rdefpar_type: "LT",
                                    rdefpar_order: null,
                                },
                            ],
                        },
                    ],
                },
            ],
            orgstrs: [
                {
                    orgstr_name: "Test",
                    manager_user_name: "Fanda Josef",
                    external_id: null,
                    parent_ic: null,
                    organization_type: null,
                    additional_id: null,
                    external_status: null,
                    company_ic: null,
                    organization_status: "A",
                    org_id: 1,
                    orgstr_id: 26,
                    parent_orgstr_id: 1,
                    manager_user_id: 2,
                    logo_url: null,
                    import_rules: {
                        action: "USE_COMPATIBLE",
                        data: {
                            orgstr_id: 1,
                        },
                    },
                },
            ],
            roles: [],
            template_processes: [
                {
                    tproc_version: 1,
                    tproc_id: 1852,
                    tproc_name: name,
                    tproc_owner_user_id: 1064,
                    tproc_locked_by_user_id: null,
                    tproc_last_changed_by_user_id: 1064,
                    tproc_last_changed_date: "2018-04-23T08:16:11.000Z",
                    tproc_status: "D",
                    tproc_description: null,
                    tproc_url: null,
                    tproc_tvar_order: null,
                    tproc_url_tab_name: null,
                    tproc_vis_orgstr_id: null,
                    tproc_vis_role_id: null,
                    tproc_dms_visibility: null,
                    tproc_default_case_name: name,
                    org_id: 1,
                    template_tasks: [
                        {
                            org_id: 1,
                            ttask_id: 3903,
                            tproc_id: 1852,
                            ttask_name: "sub-process",
                            ttask_is_proc_id: null,
                            ttask_description: null,
                            ttask_assesment_role_id: null,
                            ttask_assesment_hierarchy: "G",
                            ttask_assesment_method: "T",
                            ttask_assesment_user_id: null,
                            ttask_petri_net_input: 1,
                            ttask_is_deleted: "N",
                            ttask_due_offset: null,
                            ttask_duration: null,
                            ttask_sufficient_end: "N",
                            ttask_type: "P",
                            ttask_assesment_orgstr_id: null,
                            ttask_assesment_ttask_id: null,
                            ttask_assesment_orgstr_cnst: null,
                            ttask_instruction: null,
                            ttask_event: "$SUBP_3903",
                            ttask_event_wait: 2,
                            ttask_subprocess_tproc_id: 1860,
                            ttask_subprocess_tproc_version: 1,
                            ttask_run_only_once: "N",
                            ttask_disc_flag: "N",
                            ttask_multiinstance_flag: "N",
                            ttask_assesment_tvar_id: null,
                            ttask_duty: "Y",
                            ttask_gen_history: "Y",
                            ttask_invoke_event: "B",
                            ttask_iterate_over: null,
                            ttask_reference_user: "supervisor()",
                            ttask_can_be_button: "N",
                            ttask_var_global_order: "Y",
                            ttask_again: "N",
                            ttask_is_delegatable: "Y",
                            ttask_is_rejectable: "N",
                            ttask_js: null,
                            ttask_calc_count: 0,
                            ttask_assesment_role_name: null,
                            ttask_assesment_orgstr_name: null,
                            calculations: [],
                            completition: [],
                            variable_usages: [],
                        },
                        {
                            org_id: 1,
                            ttask_id: 3902,
                            tproc_id: 1852,
                            ttask_name: "event wait",
                            ttask_is_proc_id: null,
                            ttask_description: null,
                            ttask_assesment_role_id: null,
                            ttask_assesment_hierarchy: "G",
                            ttask_assesment_method: "T",
                            ttask_assesment_user_id: null,
                            ttask_petri_net_input: 1,
                            ttask_is_deleted: "N",
                            ttask_due_offset: null,
                            ttask_duration: null,
                            ttask_sufficient_end: "N",
                            ttask_type: "W",
                            ttask_assesment_orgstr_id: null,
                            ttask_assesment_ttask_id: null,
                            ttask_assesment_orgstr_cnst: null,
                            ttask_instruction: null,
                            ttask_event: name,
                            ttask_event_wait: 1,
                            ttask_subprocess_tproc_id: -1,
                            ttask_run_only_once: "N",
                            ttask_disc_flag: "N",
                            ttask_multiinstance_flag: "N",
                            ttask_assesment_tvar_id: null,
                            ttask_duty: "Y",
                            ttask_gen_history: "Y",
                            ttask_invoke_event: "B",
                            ttask_iterate_over: null,
                            ttask_reference_user: "supervisor()",
                            ttask_can_be_button: "N",
                            ttask_var_global_order: "Y",
                            ttask_again: "N",
                            ttask_is_delegatable: "Y",
                            ttask_is_rejectable: "N",
                            ttask_js: null,
                            ttask_calc_count: 0,
                            ttask_assesment_role_name: null,
                            ttask_assesment_orgstr_name: null,
                            calculations: [],
                            completition: [],
                            variable_usages: [],
                        },
                        {
                            org_id: 1,
                            ttask_id: 3887,
                            tproc_id: 1852,
                            ttask_name: "event start",
                            ttask_is_proc_id: null,
                            ttask_description: null,
                            ttask_assesment_role_id: null,
                            ttask_assesment_hierarchy: "G",
                            ttask_assesment_method: "T",
                            ttask_assesment_user_id: null,
                            ttask_petri_net_input: 1,
                            ttask_is_deleted: "N",
                            ttask_due_offset: null,
                            ttask_duration: null,
                            ttask_sufficient_end: "N",
                            ttask_type: "E",
                            ttask_assesment_orgstr_id: null,
                            ttask_assesment_ttask_id: null,
                            ttask_assesment_orgstr_cnst: null,
                            ttask_instruction: null,
                            ttask_event: name,
                            ttask_event_wait: 0,
                            ttask_subprocess_tproc_id: -1,
                            ttask_run_only_once: "N",
                            ttask_disc_flag: "N",
                            ttask_multiinstance_flag: "N",
                            ttask_assesment_tvar_id: null,
                            ttask_duty: "Y",
                            ttask_gen_history: "Y",
                            ttask_invoke_event: "I",
                            ttask_iterate_over: null,
                            ttask_reference_user: "supervisor()",
                            ttask_can_be_button: "N",
                            ttask_var_global_order: "Y",
                            ttask_again: "N",
                            ttask_is_delegatable: "Y",
                            ttask_is_rejectable: "N",
                            ttask_js: null,
                            ttask_calc_count: 0,
                            ttask_assesment_role_name: null,
                            ttask_assesment_orgstr_name: null,
                            calculations: [],
                            completition: [],
                            variable_usages: [
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5932,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5732,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5933,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5736,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5934,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5739,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5935,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5733,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5936,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5737,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5937,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5734,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5938,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5738,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5939,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5730,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5940,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5731,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5941,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5735,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5942,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 5750,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5943,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 7775,
                                    ttaskvarusg_usage: "R",
                                },
                                {
                                    org_id: 1,
                                    ttaskvarusg_id: 5944,
                                    tproc_id: 1852,
                                    ttask_id: 3887,
                                    tvar_id: 7776,
                                    ttaskvarusg_usage: "R",
                                },
                            ],
                        },
                    ],
                    template_links: [],
                    template_variables: [
                        {
                            org_id: 1,
                            tvar_id: 5736,
                            tproc_id: 1852,
                            tvar_name: "dateList",
                            tvar_type: "LD",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_D_2",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5738,
                            tproc_id: 1852,
                            tvar_name: "sequence",
                            tvar_type: "N",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "S",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_4",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5730,
                            tproc_id: 1852,
                            tvar_name: "text",
                            tvar_type: "T",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_1",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5737,
                            tproc_id: 1852,
                            tvar_name: "numList",
                            tvar_type: "LN",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_3",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5732,
                            tproc_id: 1852,
                            tvar_name: "date",
                            tvar_type: "D",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_D_1",
                            tvar_col_index: null,
                            tvar_meta: '{"useOnlyFutureDates":false}',
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5739,
                            tproc_id: 1852,
                            tvar_name: "dlu",
                            tvar_type: "DL",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "U",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_5",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5731,
                            tproc_id: 1852,
                            tvar_name: "textArea",
                            tvar_type: "T",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "M",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_2",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5733,
                            tproc_id: 1852,
                            tvar_name: "num",
                            tvar_type: "N",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_1",
                            tvar_col_index: null,
                            tvar_meta: '{"numberOfDecimals":0}',
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5734,
                            tproc_id: 1852,
                            tvar_name: "num2",
                            tvar_type: "N",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_2",
                            tvar_col_index: null,
                            tvar_meta: '{"numberOfDecimals":0}',
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5735,
                            tproc_id: 1852,
                            tvar_name: "textList",
                            tvar_type: "LT",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_3",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5750,
                            tproc_id: 1852,
                            tvar_name: "docList",
                            tvar_type: "T",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "F",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_4",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 7776,
                            tproc_id: 1852,
                            tvar_name: "dlr",
                            tvar_type: "DL",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "R",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_7",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 7775,
                            tproc_id: 1852,
                            tvar_name: "dlo",
                            tvar_type: "DL",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "O",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_6",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                    ],
                    template_prints: [],
                    template_graph: [],
                    headers: [
                        {
                            header_id: 2072,
                            tproc_id: 1852,
                            header_name: `${name}_sub-process`,
                            header_enabled: "Y",
                            header_code: null,
                            header_roles: [
                                {
                                    role_id: -8,
                                    header_role_id: 2775,
                                    header_id: 2072,
                                    role_name: "$AllUsers",
                                },
                            ],
                            header_orgstrs: [],
                        },
                    ],
                    template_variable_lovs: {
                        5735: [
                            {
                                org_id: 1,
                                tvarlov_id: 2414,
                                tvar_id: 5735,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: "a",
                                tvarlov_number_value: null,
                                tvarlov_date_value: null,
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2415,
                                tvar_id: 5735,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: "b",
                                tvarlov_number_value: null,
                                tvarlov_date_value: null,
                            },
                        ],
                        5736: [
                            {
                                org_id: 1,
                                tvarlov_id: 2416,
                                tvar_id: 5736,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: null,
                                tvarlov_date_value: "2018-03-22T23:00:00.000Z",
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2417,
                                tvar_id: 5736,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: null,
                                tvarlov_date_value: "2018-03-23T23:00:00.000Z",
                            },
                        ],
                        5737: [
                            {
                                org_id: 1,
                                tvarlov_id: 2418,
                                tvar_id: 5737,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: 1,
                                tvarlov_date_value: null,
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2419,
                                tvar_id: 5737,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: 2,
                                tvarlov_date_value: null,
                            },
                        ],
                    },
                    import_rules: {
                        action: "CREATE",
                        data: {
                            tproc_name: `${name}_import`,
                        },
                    },
                },
                {
                    tproc_id: 1860,
                    tproc_version: 1,
                    tproc_name: `${name}_sub-process`,
                    tproc_owner_user_id: 1064,
                    tproc_locked_by_user_id: null,
                    tproc_last_changed_by_user_id: 1064,
                    tproc_last_changed_date: "2018-04-23T08:27:23.000Z",
                    tproc_status: "D",
                    tproc_description: null,
                    tproc_url: null,
                    tproc_tvar_order: null,
                    tproc_url_tab_name: null,
                    tproc_vis_orgstr_id: null,
                    tproc_vis_role_id: null,
                    tproc_dms_visibility: null,
                    tproc_default_case_name: `${name}_sub-process`,
                    org_id: 1,
                    template_tasks: [],
                    template_links: [],
                    template_variables: [
                        {
                            org_id: 1,
                            tvar_id: 5751,
                            tproc_id: 1860,
                            tvar_name: "sp numList",
                            tvar_type: "LN",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_4",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5746,
                            tproc_id: 1860,
                            tvar_name: "sp dateList",
                            tvar_type: "LD",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_D_2",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5744,
                            tproc_id: 1860,
                            tvar_name: "sp num",
                            tvar_type: "N",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_1",
                            tvar_col_index: null,
                            tvar_meta: '{"numberOfDecimals":0}',
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5747,
                            tproc_id: 1860,
                            tvar_name: "sp sequence",
                            tvar_type: "N",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "S",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_2",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5741,
                            tproc_id: 1860,
                            tvar_name: "sp text",
                            tvar_type: "T",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_1",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5742,
                            tproc_id: 1860,
                            tvar_name: "sp textArea",
                            tvar_type: "T",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "M",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_2",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5743,
                            tproc_id: 1860,
                            tvar_name: "sp date",
                            tvar_type: "D",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_D_1",
                            tvar_col_index: null,
                            tvar_meta: '{"useOnlyFutureDates":false}',
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5745,
                            tproc_id: 1860,
                            tvar_name: "sp textList",
                            tvar_type: "LT",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: null,
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_3",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5748,
                            tproc_id: 1860,
                            tvar_name: "sp docList",
                            tvar_type: "T",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "F",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_T_4",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 5749,
                            tproc_id: 1860,
                            tvar_name: "sp dlu",
                            tvar_type: "DL",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "U",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_3",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 7778,
                            tproc_id: 1860,
                            tvar_name: "sp dlr",
                            tvar_type: "DL",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "R",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_6",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                        {
                            org_id: 1,
                            tvar_id: 7777,
                            tproc_id: 1860,
                            tvar_name: "sp dlo",
                            tvar_type: "DL",
                            tvar_multitask_behaviour: null,
                            tvar_text_value: null,
                            tvar_number_value: null,
                            tvar_date_value: null,
                            tvar_attribute: "O",
                            dlist_name: null,
                            tvar_class: null,
                            tvar_multi: null,
                            tvar_big_value: null,
                            tvar_alias: "VAR_N_5",
                            tvar_col_index: null,
                            tvar_meta: null,
                            tvar_label: null,
                            tvar_tooltip: null,
                        },
                    ],
                    template_prints: [],
                    template_graph: [],
                    headers: [
                        {
                            header_id: 2080,
                            tproc_id: 1860,
                            header_name: `${name}_sub-process`,
                            header_enabled: "Y",
                            header_code: null,
                            header_roles: [
                                {
                                    role_id: -8,
                                    header_role_id: 2774,
                                    header_id: 2080,
                                    role_name: "$AllUsers",
                                },
                            ],
                            header_orgstrs: [],
                        },
                    ],
                    template_variable_lovs: {
                        5745: [
                            {
                                org_id: 1,
                                tvarlov_id: 2421,
                                tvar_id: 5745,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: "x",
                                tvarlov_number_value: null,
                                tvarlov_date_value: null,
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2422,
                                tvar_id: 5745,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: "y",
                                tvarlov_number_value: null,
                                tvarlov_date_value: null,
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2423,
                                tvar_id: 5745,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: "z",
                                tvarlov_number_value: null,
                                tvarlov_date_value: null,
                            },
                        ],
                        5746: [
                            {
                                org_id: 1,
                                tvarlov_id: 2426,
                                tvar_id: 5746,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: null,
                                tvarlov_date_value: "2018-03-27T22:00:00.000Z",
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2427,
                                tvar_id: 5746,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: null,
                                tvarlov_date_value: "2018-03-28T22:00:00.000Z",
                            },
                        ],
                        5751: [
                            {
                                org_id: 1,
                                tvarlov_id: 2428,
                                tvar_id: 5751,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: 7,
                                tvarlov_date_value: null,
                            },
                            {
                                org_id: 1,
                                tvarlov_id: 2429,
                                tvar_id: 5751,
                                tvarlov_is_selected: "N",
                                tvarlov_text_value: null,
                                tvarlov_number_value: 8,
                                tvarlov_date_value: null,
                            },
                        ],
                    },
                    import_rules: {
                        action: "CREATE",
                        data: {
                            tproc_name: `${name}_sub-process_import`,
                        },
                    },
                },
            ],
            users: [
                {
                    user_id: 1064,
                    user_full_name: "Fanda Josef",
                    user_display_name: "Fanda Josef",
                    user_name: "JOFA",
                    user_first_name: "Josef",
                    user_last_name: "Fanda",
                    user_status: "A",
                    user_email: null,
                    org_id: 1,
                    user_change_password: null,
                    user_bad_login_count: 0,
                    user_title_prefix: null,
                    user_external_login: null,
                    user_external_source: null,
                    user_comp_id: null,
                    user_comp_code: null,
                    import_rules: {
                        action: "USE_COMPATIBLE",
                        data: {
                            user_id: idUserA,
                        },
                    },
                },
            ],
        };

        agent
            .post("/template-processes/import")
            .set("Authorization", authorizationTokenUserA)
            .send(templateImport)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                done();
            });
    });

    // template_processes
    it("TC0655 | (exp_imp_5 - import) Get the template variables object (prescription)", (done) => {
        templateImportVars = {};
        templateImport.template_processes[0].template_variables.forEach((v) => {
            templateImportVars[v.tvar_id] = v.tvar_name;
        });
        templateImport.template_processes[1].template_variables.forEach((v) => {
            templateImportVars[v.tvar_id] = v.tvar_name;
        });
        done();
    });

    it("TC0656 | (exp_imp_5 - import) Find the imported template", (done) => {
        const filter = `tproc_name<eq>"${name}_import"`;
        agent
            .get(`/template-processes?filter=${encodeURIComponent(filter)}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                res.body.items.length.should.equal(1);
                tProcIdImport = [
                    res.body.items[0].id.tproc_id,
                    res.body.items[0].id.tproc_version,
                ];
                done();
            });
    });

    it("TC0657 | (exp_imp_5 - import) Find the imported sup process", (done) => {
        const filter = `tproc_name<eq>"${name}_sub-process_import"`;
        agent
            .get(`/template-processes?filter=${encodeURIComponent(filter)}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                res.body.items.length.should.equal(1);
                tProcIdImportSup = [
                    res.body.items[0].id.tproc_id,
                    res.body.items[0].id.tproc_version,
                ];
                done();
            });
    });

    it("TC0658 | (exp_imp_5 - import) Get the template variables object", (done) => {
        getTemplateVariablesObj(tProcIdImport)
            .then((vars) => {
                tProcIdImportVars = vars;
                return getTemplateVariablesObj(tProcIdImportSup);
            })
            .then((vars) => {
                tProcIdImportVars = _.assign(tProcIdImportVars, vars);
                done();
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0659 | (exp_imp_5 - import) Find the template tasks", (done) => {
        agent
            .get(
                `/template-processes/${tProcIdImport[0]}/${tProcIdImport[1]}/template-tasks`,
            )
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                res.body.items.length.should.equal(3);
                tTaskId1_2Import = _.find(res.body.items, {
                    ttask_name: "event wait",
                }).id;
                tTaskId1_3Import = _.find(res.body.items, {
                    ttask_name: "sub-process",
                }).id;
                done();
            });
    });

    it('TC0660 | (exp_imp_5 - import) Check the mapping of variables in "event wait" task', (done) => {
        convertedImportMapping = convertMapping(
            templateImport.events[0].rules[0].rule_params,
            templateImportVars,
        );

        agent
            .get(
                `/template-processes/${tProcIdImport[0]}/${tProcIdImport[1]}/template-tasks/${tTaskId1_2Import}`,
            )
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                should.deepEqual(
                    convertMapping(
                        _.sortBy(res.body.mapping.subr, "rdefpar_name"),
                        tProcIdImportVars,
                    ),
                    _.sortBy(convertedImportMapping, "rdefpar_name"),
                );
                done();
            });
    });

    it('TC0661 | (exp_imp_5 - import) Check the mapping of variables in "sub-process" task', (done) => {
        convertedImportMappingSub = convertMapping(
            templateImport.events[1].rules[0].rule_params,
            templateImportVars,
        );
        convertedImportMappingSubr = convertMapping(
            templateImport.events[2].rules[0].rule_params,
            templateImportVars,
        );

        agent
            .get(
                `/template-processes/${tProcIdImport[0]}/${tProcIdImport[1]}/template-tasks/${tTaskId1_3Import}`,
            )
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                should.deepEqual(
                    convertMapping(
                        _.sortBy(res.body.mapping.subp, "rdefpar_name"),
                        tProcIdImportVars,
                    ),
                    _.sortBy(convertedImportMappingSub, "rdefpar_name"),
                );
                should.deepEqual(
                    convertMapping(
                        _.sortBy(res.body.mapping.subr, "rdefpar_name"),
                        tProcIdImportVars,
                    ),
                    _.sortBy(convertedImportMappingSubr, "rdefpar_name"),
                );
                done();
            });
    });

    // Copy
    it("TC0662 | (exp_imp_5 - copy) Export the template", (done) => {
        const body = [
            { tproc_id: tProcIdImport[0], tproc_version: tProcIdImport[1] },
        ];
        agent
            .post("/template-processes/export")
            .set("Authorization", authorizationTokenUserA)
            .send(body)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                templateExport = res.body[0];
                done();
            });
    });

    it("TC0663 | (exp_imp_5 - copy) Import the template", (done) => {
        const exportTemplate = _.find(templateExport.template_processes, {
            tproc_name: `${name}_import`,
        });
        exportTemplate.import_rules = {
            action: "CREATE",
            data: { tproc_name: `${name}_copy` },
        };

        const exportTemplateSup = _.find(templateExport.template_processes, {
            tproc_name: `${name}_sub-process_import`,
        });
        exportTemplateSup.import_rules = {
            action: "CREATE",
            data: { tproc_name: `${name}_sub-process_copy` },
        };
        const exportEvent = _.find(templateExport.events, {
            evedef_name: `${name}_import`,
        });
        exportEvent.import_rules = {
            action: "CREATE",
            data: { evedef_name: `${name}_copy` },
        };
        templateExport.users[0].import_rules = {
            action: "USE_COMPATIBLE",
            data: { user_id: idUserA },
        };
        agent
            .post("/template-processes/import")
            .set("Authorization", authorizationTokenUserA)
            .send(templateExport)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0664 | (exp_imp_5 - copy) Find the copy of the template", (done) => {
        const filter = `tproc_name<eq>"${name}_copy"`;
        agent
            .get(`/template-processes?filter=${encodeURIComponent(filter)}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                res.body.items.length.should.equal(1);
                tProcIdCopy = [
                    res.body.items[0].id.tproc_id,
                    res.body.items[0].id.tproc_version,
                ];
                done();
            });
    });

    it("TC0665 | (exp_imp_5 - copy) Find the copy of the sup process", (done) => {
        const filter = `tproc_name<eq>"${name}_sub-process_copy"`;
        agent
            .get(`/template-processes?filter=${encodeURIComponent(filter)}`)
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                res.body.items.length.should.equal(1);
                tProcIdCopySup = [
                    res.body.items[0].id.tproc_id,
                    res.body.items[0].id.tproc_version,
                ];
                done();
            });
    });

    it("TC0666 | (exp_imp_5 - copy) Get the template variables object", (done) => {
        getTemplateVariablesObj(tProcIdCopy)
            .then((vars) => {
                tProcIdCopyVars = vars;
                return getTemplateVariablesObj(tProcIdCopySup);
            })
            .then((vars) => {
                tProcIdCopyVars = _.assign(tProcIdCopyVars, vars);
                done();
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0667 | (exp_imp_5 - copy) Find the template tasks", (done) => {
        agent
            .get(
                `/template-processes/${tProcIdCopy[0]}/${tProcIdCopy[1]}/template-tasks`,
            )
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                res.body.items.length.should.equal(3);
                tTaskId1_2Copy = _.find(res.body.items, {
                    ttask_name: "event wait",
                }).id;
                tTaskId1_3Copy = _.find(res.body.items, {
                    ttask_name: "sub-process",
                }).id;
                done();
            });
    });

    it('TC0668 | (exp_imp_5 - copy) Check the mapping of variables in "event wait" task', (done) => {
        agent
            .get(
                `/template-processes/${tProcIdImport[0]}/${tProcIdImport[1]}/template-tasks/${tTaskId1_2Copy}`,
            )
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                should.deepEqual(
                    convertMapping(
                        _.sortBy(res.body.mapping.subr, "rdefpar_name"),
                        tProcIdCopyVars,
                    ),
                    _.sortBy(convertedImportMapping, "rdefpar_name"),
                );
                done();
            });
    });

    it('TC0669 | (exp_imp_5 - copy) Check the mapping of variables in "sub-process" task', (done) => {
        agent
            .get(
                `/template-processes/${tProcIdImport[0]}/${tProcIdImport[1]}/template-tasks/${tTaskId1_3Copy}`,
            )
            .set("Authorization", authorizationTokenUserA)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                should.deepEqual(
                    convertMapping(
                        _.sortBy(res.body.mapping.subp, "rdefpar_name"),
                        tProcIdCopyVars,
                    ),
                    _.sortBy(convertedImportMappingSub, "rdefpar_name"),
                );
                should.deepEqual(
                    convertMapping(
                        _.sortBy(res.body.mapping.subr, "rdefpar_name"),
                        tProcIdCopyVars,
                    ),
                    _.sortBy(convertedImportMappingSubr, "rdefpar_name"),
                );
                done();
            });
    });
});
