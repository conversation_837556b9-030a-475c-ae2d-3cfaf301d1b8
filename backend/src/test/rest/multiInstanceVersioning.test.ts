// @ts-nocheck

import * as util from "../utils/api";

import _ from "lodash";

import * as PROCESS from "../../api/orm/entity/const/processConst";
import * as VARIABLE from "../../api/orm/entity/const/variableConst";
import { OrmFactory } from "../../api/orm/OrmFactory";
import { Workflow } from "../../api/workflow/Workflow";
import { assert } from "chai";

const genRanHex = (size: number): string =>
    [...Array(size)]
        .map(() => Math.floor(Math.random() * 16).toString(16))
        .join("");

const testName = `multiInstanceVersioning_${genRanHex(6)}`;

let variables;

describe("Multi instance fallback to old Process Version (variable mapping, links)", () => {
    before(async () => {
        try {
            variables = util.variables();
            const [users, organization, role] = await util.generateUsers(
                3,
                testName,
            );
            // Users, Organizations, Roles
            variables.users = users;
            variables.organization_structures = organization;
            variables.roles = role;

            // Users and Hashes
            variables.hashes = await util.logInUsers(variables.users);

            // Template Process and Headers
            variables.template_processes.push(
                await util.createTemplateProcess(
                    testName,
                    testName,
                    PROCESS.STATUS_ACTIVE,
                    variables.organization_structures[0],
                ),
            );
            variables.headers.push(
                (await util.findHeaders(variables.template_processes[0]))[0].id,
            );

            // Template Variables
            variables.template_variables.push(
                await util.createTemplateVariable(
                    variables.template_processes[0],
                    "users_list",
                    VARIABLE.TYPE_DYNAMIC_LIST,
                    null,
                    VARIABLE.ATTR_USER,
                    VARIABLE.MULTI_INSTANCE_YES,
                ),
            );
            variables.template_variables.push(
                await util.createTemplateVariable(
                    variables.template_processes[0],
                    "var1",
                    VARIABLE.TYPE_TEXT,
                ),
            );
            variables.template_variables.push(
                await util.createTemplateVariable(
                    variables.template_processes[0],
                    "var2",
                    VARIABLE.TYPE_TEXT,
                ),
            );
            variables.template_variables.push(
                await util.createTemplateVariable(
                    variables.template_processes[0],
                    "var3",
                    VARIABLE.TYPE_TEXT,
                ),
            );

            // Template Tasks
            // Start
            variables.template_tasks.push(
                await util.createTtask(
                    variables.template_processes[0],
                    `${testName}_start`,
                    {
                        ttask_var_mapping: [
                            {
                                tvar_id: variables.template_variables[0],
                                usage: "M",
                                axis_x: null,
                                axis_y: null,
                            },
                        ],
                    },
                ),
            );

            // Multi instance over variable
            variables.template_tasks.push(
                await util.createTtask(
                    variables.template_processes[0],
                    `${testName}_multiInstance`,
                    {
                        ttask_multiinstance_flag: "Y",
                        ttask_iterate_over: "users_list",
                        ttask_var_mapping: [
                            {
                                tvar_id: variables.template_variables[1],
                                usage: "W",
                                axis_x: null,
                                axis_y: null,
                            },
                            {
                                tvar_id: variables.template_variables[2],
                                usage: "W",
                                axis_x: null,
                                axis_y: null,
                            },
                            {
                                tvar_id: variables.template_variables[3],
                                usage: "W",
                                axis_x: null,
                                axis_y: null,
                            },
                        ],
                    },
                ),
            );

            // End Task waits for all multi instance Tasks to be finished
            variables.template_tasks.push(
                await util.createTtask(
                    variables.template_processes[0],
                    `${testName}_end`,
                    {
                        ttask_petri_net_input: "A",
                    },
                ),
            );

            // Create Template links
            await util.createLinks(
                variables.template_processes[0],
                variables.template_tasks,
            );

            // Copy Instance from Template
            await globalThis.database.transaction(async (trx) => {
                const process = await globalThis.orm
                    .repo("Process", trx)
                    .copyInstanceFromTemplate(
                        authorizationAdminId,
                        variables.template_processes[0],
                        variables.headers[0],
                        testName,
                        null,
                        1,
                        null,
                        "M",
                        null,
                    );

                const tasks = await trx
                    .select()
                    .from("INSTANCE_TASKS")
                    .where("IPROC_ID", process.IPROC_ID)
                    .orderBy("ITASK_ID", "asc");

                const itaskMap = {};
                tasks.forEach((task) => {
                    itaskMap[task.TTASK_ID] = task;
                });

                // Create Instance Links the old way
                for (const task of tasks) {
                    await globalThis.orm
                        .repo("InstanceTaskLink", trx)
                        .copyLinkFromTemplate(
                            process.IPROC_ID,
                            task.TTASK_ID,
                            itaskMap,
                        );
                }

                // Create Variable Usage the old way
                await globalThis.orm
                    .repo("instanceTaskVarUsage", trx)
                    .cloneVaraibleUsagesFromTemplateProcess(process.IPROC_ID);

                // Artificially make TAS create an old version of the Process by changing its version before starting the Process
                await trx
                    .from("INSTANCE_PROCESS_VERSIONS")
                    .where("IPROC_ID", process.IPROC_ID)
                    .update({
                        TTASKVARUSG_VERSION: null,
                        TTASKLINK_VERSION: null,
                    });

                // Activate process
                const user =
                    await globalThis.container.service.temporary.cacheModule.getCachedUser(
                        authorizationAdminId,
                    );
                const orm = new OrmFactory(
                    trx,
                    globalThis.dynamicConfig.db.client,
                );
                const wf = new Workflow(orm, user);
                await wf.start(process);

                // Store IDs
                variables.instance_tasks.push(
                    ...tasks.map((item) => item.ITASK_ID),
                );
                variables.instance_processes.push(process.IPROC_ID);
            });

            // Find IDs of the created INSTANCE_VARIABLEs and update their values
            const { items: vars } = await util.get(
                `/processes/${variables.instance_processes[0]}/variables`,
                authorizationTokenAdmin,
            );
            variables.instance_variables.push(vars.map((item) => item.IVAR_ID));

            const usersVariableId = _.find(vars, {
                tvar_id: variables.template_variables[0],
            }).id;
            await util.post(
                `/tasks/${variables.instance_tasks[0]}/variables`,
                authorizationTokenAdmin,
                [
                    {
                        id: usersVariableId,
                        value: variables.users.map((user) => user.USER_ID),
                    },
                ],
            );

            globalThis.tasLogger.info("**********END OF BEFORE HOOK**********");
        } catch (err) {
            globalThis.tasLogger.error(err);
            throw err;
        }
    });

    it("TC0422 | First TASK (start) matches the expected values", async () => {
        const { items: tasks } = await util.getProcessTasks(
            variables.instance_processes[0],
            authorizationTokenAdmin,
        );
        const { items: taskVariables } = await util.getTaskVariables(
            variables.instance_tasks[0],
            authorizationTokenAdmin,
        );

        assert.equal(
            tasks.length,
            1,
            "There should be only one actual active TASK",
        );
        assert.equal(
            tasks[0].ttask_id,
            variables.template_tasks[0],
            "TTASK_ID should match",
        );
        assert.equal(
            taskVariables.length,
            1,
            "There should be only one active VARIABLE",
        );
        assert.equal(
            taskVariables[0].tvar_id,
            variables.template_variables[0],
            "TVAR_ID should match",
        );

        await util.finishTask(
            variables.instance_tasks[0],
            authorizationTokenAdmin,
        );
    });

    it("TC0423 | Three Multi Instance Tasks should be created and links lead to the last Task", async () => {
        const { items: tasks } = await util.getProcessTasks(
            variables.instance_processes[0],
            authorizationTokenAdmin,
        );

        assert.equal(
            tasks.length,
            3,
            "There should be three active Multi Instance Tasks",
        );

        for (const [_index, task] of tasks.entries()) {
            const { items: taskVariables } = await util.getTaskVariables(
                task.id,
                authorizationTokenAdmin,
            );

            assert.equal(
                taskVariables.length,
                3,
                "Task ${index + 1} should have 3 active VARIABLEs",
            );

            const { NEXT_ITASK_NAME: nextTaskName, NEXT_ITASK_ID: nextTaskId } =
                await util.finishTask(task.id, authorizationTokenAdmin);

            if (nextTaskId && nextTaskName) {
                assert.equal(
                    nextTaskId,
                    variables.instance_tasks[2],
                    "ITASK_ID of the last task should equal",
                );
                assert.equal(
                    nextTaskName,
                    `${testName}_end`,
                    "TTASK_NAME of the last task should equal",
                );
            }
        }
    });
});
