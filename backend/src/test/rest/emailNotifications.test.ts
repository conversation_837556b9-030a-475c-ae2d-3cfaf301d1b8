// @ts-nocheck

import * as util from "../utils/api";

import _ from "lodash";

import should from "should";
import { assert } from "chai";
import * as VARIABLE from "../../api/orm/entity/const/variableConst";
import * as TASK from "../../api/orm/entity/const/taskConst";
import * as NOTIFICATION from "../../api/orm/entity/const/notificationConsts";
import * as PROCESS from "../../api/orm/entity/const/processConst";
import { RouterMail } from "../../client/mail/RouterMail";
import * as USER_PARAMETER from "../../api/orm/entity/const/userParameterConsts";

const genRanHex = (size: number): string =>
    [...Array(size)]
        .map(() => Math.floor(Math.random() * 16).toString(16))
        .join("");

const testName = `testEnot${genRanHex(6)}`;
const externalMails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
];

/*
    All we need to do is check if the addresses, blind copies, etc. equal our expected values
    - if you need to check the actual status of you SMTP configuration, call 'npm-run script config'
 */

let variables;
const filterObject = (raw, allowedKeys = ["to", "replyTo", "bcc", "cc"]) => {
    // Check if sending emails is enabled
    if (!globalThis.dynamicConfig.mail.sendingEnabled) {
        throw new Error("For this test, sending of emails must be enabled.");
    }
    return Object.keys(raw)
        .filter((key) => allowedKeys.includes(key))
        .reduce((obj, key) => {
            obj[key] = raw[key];
            return obj;
        }, {});
};

describe("Email Notifications", () => {
    let temp;
    before(() => {
        temp = globalThis.dynamicConfig.mail.sendingEnabled;
        globalThis.dynamicConfig.mail.sendingEnabled = true;
    });

    after(() => {
        globalThis.dynamicConfig.mail.sendingEnabled = temp;
    });

    describe("Resolve addressees from mail options", () => {
        before((done) => {
            if (globalThis.dynamicConfig.langs.length < 3) {
                throw new Error(
                    "For this test, please make sure you have at least 3 languages enabled in your config.",
                );
            }

            variables = util.variables();
            globalThis.dynamicConfig.test.tests.mails = {};
            globalThis.dynamicConfig.test.tests.mailId = 1001;
            util.generateUsers(3, `${testName}1`)
                .then(([users, organization, role]) => {
                    variables.users = users;
                    variables.organization_structures = organization;
                    variables.roles = role;
                    return util.logInUsers(variables.users);
                })
                .then((hashes) => {
                    variables.hashes = hashes;
                    return util.generateUsers(
                        globalThis.dynamicConfig.langs.length,
                        `${testName}1_lang`,
                    );
                })
                .then(([users, organization, role]) => {
                    variables.extra_users = users;
                    variables.organization_structures.push(organization);
                    variables.roles.push(role);
                    return util.logInUsers(variables.extra_users);
                })
                .then(async (hashes) => {
                    variables.hashes.push(...hashes);
                    let counter = 0;
                    for (const lang of globalThis.dynamicConfig.langs) {
                        variables.extra_users[counter].USER_LANGUAGE = lang;
                        counter += 1;
                        await util.setUserParameter(
                            [
                                {
                                    name: USER_PARAMETER.LANGUAGE_CLIENT,
                                    value: lang,
                                },
                            ],
                            variables.hashes[2 + counter],
                        );
                    }
                })
                .then(() =>
                    util.createTemplateProcess(
                        testName,
                        testName,
                        PROCESS.STATUS_ACTIVE,
                        variables.organization_structures[0],
                    ),
                )
                .then((tProcId) => {
                    variables.template_processes.push(tProcId);
                    return util.findHeaders(tProcId);
                })
                .then((headers) => {
                    variables.headers.push(headers[0].id);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "TARGET_USER",
                        VARIABLE.TYPE_DYNAMIC_LIST,
                        null,
                        VARIABLE.ATTR_USER,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "TARGET_USER_MULTI",
                        VARIABLE.TYPE_DYNAMIC_LIST,
                        null,
                        VARIABLE.ATTR_USER,
                        VARIABLE.MULTI_INSTANCE_YES,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "TARGET_PLAIN",
                        VARIABLE.TYPE_TEXT,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "REPLY_USER",
                        VARIABLE.TYPE_DYNAMIC_LIST,
                        null,
                        VARIABLE.ATTR_USER,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "REPLY_USER_MULTI",
                        VARIABLE.TYPE_DYNAMIC_LIST,
                        null,
                        VARIABLE.ATTR_USER,
                        VARIABLE.MULTI_INSTANCE_YES,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "REPLY_PLAIN",
                        VARIABLE.TYPE_TEXT,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "BLIND_USER",
                        VARIABLE.TYPE_DYNAMIC_LIST,
                        null,
                        VARIABLE.ATTR_USER,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "BLIND_USER_MULTI",
                        VARIABLE.TYPE_DYNAMIC_LIST,
                        null,
                        VARIABLE.ATTR_USER,
                        VARIABLE.MULTI_INSTANCE_YES,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "BLIND_PLAIN",
                        VARIABLE.TYPE_TEXT,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "TARGET_PLAIN_2",
                        VARIABLE.TYPE_TEXT,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "REPLY_PLAIN_2",
                        VARIABLE.TYPE_TEXT,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        "BLIND_PLAIN_2",
                        VARIABLE.TYPE_TEXT,
                    );
                })
                .then(async (tVarId) => {
                    // copy_user_var, copy_user_var_multi, copy_plain, copy_plain_2
                    variables.template_variables.push(tVarId);
                    variables.template_variables.push(
                        await util.createTemplateVariable(
                            variables.template_processes[0],
                            "COPY_USER",
                            VARIABLE.TYPE_DYNAMIC_LIST,
                            null,
                            VARIABLE.ATTR_USER,
                        ),
                    );
                    variables.template_variables.push(
                        await util.createTemplateVariable(
                            variables.template_processes[0],
                            "COPY_USER_MULTI",
                            VARIABLE.TYPE_DYNAMIC_LIST,
                            null,
                            VARIABLE.ATTR_USER,
                            VARIABLE.MULTI_INSTANCE_YES,
                        ),
                    );
                    variables.template_variables.push(
                        await util.createTemplateVariable(
                            variables.template_processes[0],
                            "COPY_PLAIN",
                            VARIABLE.TYPE_TEXT,
                        ),
                    );
                    variables.template_variables.push(
                        await util.createTemplateVariable(
                            variables.template_processes[0],
                            "COPY_PLAIN_2",
                            VARIABLE.TYPE_TEXT,
                        ),
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_PROCESS_OWNER`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_tgt_type:
                                NOTIFICATION.TYPE_PROCESS_OWNER,
                            ttask_enot_reply_type:
                                NOTIFICATION.TYPE_PROCESS_OWNER,
                            ttask_enot_blind_type:
                                NOTIFICATION.TYPE_PROCESS_OWNER,
                            ttask_enot_copy_type:
                                NOTIFICATION.TYPE_PROCESS_OWNER,
                            ttask_enot_subject: testName,
                        },
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_PROCESS_GUARANTOR`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_tgt_type:
                                NOTIFICATION.TYPE_PROCESS_GUARANTOR,
                            ttask_enot_reply_type:
                                NOTIFICATION.TYPE_PROCESS_GUARANTOR,
                            ttask_enot_blind_type:
                                NOTIFICATION.TYPE_PROCESS_GUARANTOR,
                            ttask_enot_copy_type:
                                NOTIFICATION.TYPE_PROCESS_GUARANTOR,
                            ttask_enot_subject: testName,
                        },
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[0],
                        variables.template_tasks[1],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_FILL_VARS`,
                        {
                            ttask_var_mapping: [
                                {
                                    tvar_id: variables.template_variables[0],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[1],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[2],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[9],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[3],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[4],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[5],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[10],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[6],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[7],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[8],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[11],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[12],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[13],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[14],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                                {
                                    tvar_id: variables.template_variables[15],
                                    usage: "M",
                                    axis_x: null,
                                    axis_y: null,
                                },
                            ],
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[1],
                        variables.template_tasks[2],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_LAST_SOLVER`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type:
                                NOTIFICATION.TYPE_LAST_TASK_SOLVER,
                            ttask_enot_tgt_ttask_id:
                                variables.template_tasks[2],

                            // REPLY
                            ttask_enot_reply_type:
                                NOTIFICATION.TYPE_LAST_TASK_SOLVER,
                            ttask_enot_reply_ttask_id:
                                variables.template_tasks[2],

                            // BLIND
                            ttask_enot_blind_type:
                                NOTIFICATION.TYPE_LAST_TASK_SOLVER,
                            ttask_enot_blind_ttask_id:
                                variables.template_tasks[2],

                            // COPY
                            ttask_enot_copy_type:
                                NOTIFICATION.TYPE_LAST_TASK_SOLVER,
                            ttask_enot_copy_ttask_id:
                                variables.template_tasks[2],
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[2],
                        variables.template_tasks[3],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_PLAIN`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_tgt: variables.users[2].USER_EMAIL,

                            // REPLY
                            ttask_enot_reply_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_reply_target:
                                variables.users[1].USER_EMAIL,

                            // BLIND
                            ttask_enot_blind_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_blind_target:
                                variables.users[0].USER_EMAIL,

                            // COPY
                            ttask_enot_copy_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_copy_target:
                                variables.users[1].USER_EMAIL,
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[3],
                        variables.template_tasks[4],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_ORGSTR`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type:
                                NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE,
                            ttask_enot_tgt_orgstr_id:
                                variables.organization_structures[0],

                            // REPLY
                            ttask_enot_reply_type:
                                NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE,
                            ttask_enot_reply_orgstr_id:
                                variables.organization_structures[0],

                            // BLIND
                            ttask_enot_blind_type:
                                NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE,
                            ttask_enot_blind_orgstr_id:
                                variables.organization_structures[0],

                            // COPY
                            ttask_enot_copy_type:
                                NOTIFICATION.TYPE_ORGANIZATION_STRUCTURE,
                            ttask_enot_copy_orgstr_id:
                                variables.organization_structures[0],
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[4],
                        variables.template_tasks[5],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_VAR`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_tgt_tvar_id:
                                variables.template_variables[0],

                            // REPLY
                            ttask_enot_reply_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_reply_tvar_id:
                                variables.template_variables[3],

                            // BLIND
                            ttask_enot_blind_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_blind_tvar_id:
                                variables.template_variables[6],

                            // COPY
                            ttask_enot_copy_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_copy_tvar_id:
                                variables.template_variables[12],
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[5],
                        variables.template_tasks[6],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_VAR_MULTI`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_tgt_tvar_id:
                                variables.template_variables[1],

                            // REPLY
                            ttask_enot_reply_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_reply_tvar_id:
                                variables.template_variables[4],

                            // BLIND
                            ttask_enot_blind_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_blind_tvar_id:
                                variables.template_variables[7],

                            // COPY
                            ttask_enot_copy_type: NOTIFICATION.TYPE_USER_NAME,
                            ttask_enot_copy_tvar_id:
                                variables.template_variables[13],
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[6],
                        variables.template_tasks[7],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_ROLE`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type: NOTIFICATION.TYPE_ROLE,
                            ttask_enot_tgt_role_id: variables.roles[0],

                            // REPLY
                            ttask_enot_reply_type: NOTIFICATION.TYPE_ROLE,
                            ttask_enot_reply_role_id: variables.roles[0],

                            // BLIND
                            ttask_enot_blind_type: NOTIFICATION.TYPE_ROLE,
                            ttask_enot_blind_role_id: variables.roles[0],

                            // COPY
                            ttask_enot_copy_type: NOTIFICATION.TYPE_ROLE,
                            ttask_enot_copy_role_id: variables.roles[0],
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[7],
                        variables.template_tasks[8],
                    );
                })
                .then(() =>
                    util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_PLAIN_VARIABLE`,
                        {
                            ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                            ttask_enot_subject: testName,

                            // TARGET
                            ttask_enot_tgt_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_tgt: `{TARGET_PLAIN},{TARGET_PLAIN_2};${externalMails[0]}`,

                            // REPLY
                            ttask_enot_reply_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_reply_target: `{REPLY_PLAIN},{REPLY_PLAIN_2};${externalMails[0]}`,

                            // BLIND
                            ttask_enot_blind_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_blind_target: `{BLIND_PLAIN},{BLIND_PLAIN_2};${externalMails[0]}`,

                            // COPY
                            ttask_enot_copy_type:
                                NOTIFICATION.TYPE_PLAIN_ADDRESS,
                            ttask_enot_copy_target: `{COPY_PLAIN},{COPY_PLAIN_2};${externalMails[0]}`,
                        },
                    ),
                )
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[8],
                        variables.template_tasks[9],
                    );
                })
                .then(() => {
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: variables.extra_users[0].USER_EMAIL,

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: variables.extra_users
                            .slice(1)
                            .map((user) => user.USER_EMAIL), // [variables.extra_users[1].USER_EMAIL, variables.extra_users[2].USER_EMAIL],

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            variables.extra_users[0].USER_EMAIL,
                            variables.extra_users[1].USER_EMAIL,
                        ],
                    };

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_TEMPLATE_BODY`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[9],
                        variables.template_tasks[10],
                    );
                })
                .then(() => {
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",
                        ttask_operations: [
                            {
                                ttjscalc_js:
                                    "lib.changeMailAttribute('body', 'INSTANCE_BODY');\nlib.changeMailAttribute('subject', 'INSTANCE_SUBJECT');",
                                ttjscalc_exec_start: "Y",
                                ttjscalc_exec_end: "N",
                                ttjscalc_exec_hand: "N",
                                ttjscalc_exec_recalc: "N",
                                ttjscalc_exec_pull: "N",
                                ttjscalc_append_scripts: "[]",
                            },
                        ],

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: variables.extra_users[0].USER_EMAIL,

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: variables.extra_users
                            .slice(1)
                            .map((user) => user.USER_EMAIL),

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            variables.extra_users[0].USER_EMAIL,
                            variables.extra_users[1].USER_EMAIL,
                        ],
                    };

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_INSTANCE_BODY`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[10],
                        variables.template_tasks[11],
                    );
                })
                .then(() => {
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: variables.extra_users[0].USER_EMAIL,

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: variables.extra_users
                            .slice(1)
                            .map((user) => user.USER_EMAIL),

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            variables.extra_users[0].USER_EMAIL,
                            variables.extra_users[1].USER_EMAIL,
                        ],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_enot_body_${lang}`] =
                            `TEMPLATE_BODY_${lang.toUpperCase()}`;
                        params[`ttask_enot_subject_${lang}`] =
                            `TEMPLATE_SUBJECT_${lang.toUpperCase()}`;
                    });

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_TEMPLATE_MUTATIONS_BODY`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[11],
                        variables.template_tasks[12],
                    );
                })
                .then(() => {
                    const operations = [];
                    [null]
                        .concat(globalThis.dynamicConfig.langs)
                        .forEach((lang) => {
                            operations.push({
                                ttjscalc_js: `lib.changeMailAttribute('body', 'INSTANCE_BODY${lang ? `_${lang.toUpperCase()}` : ""}'${lang ? `, '${lang}'` : ""});\nlib.changeMailAttribute('subject', 'INSTANCE_SUBJECT${lang ? `_${lang.toUpperCase()}` : ""}'${lang ? `, '${lang}'` : ""});`,
                                ttjscalc_exec_start: "Y",
                                ttjscalc_exec_end: "N",
                                ttjscalc_exec_hand: "N",
                                ttjscalc_exec_recalc: "N",
                                ttjscalc_exec_pull: "N",
                                ttjscalc_append_scripts: "[]",
                            });
                        });
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",
                        ttask_operations: operations,

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: variables.extra_users[0].USER_EMAIL,

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: variables.extra_users
                            .slice(1)
                            .map((user) => user.USER_EMAIL),

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            variables.extra_users[0].USER_EMAIL,
                            variables.extra_users[1].USER_EMAIL,
                        ],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_enot_body_${lang}`] =
                            `TEMPLATE_BODY_${lang.toUpperCase()}`;
                        params[`ttask_enot_subject_${lang}`] =
                            `TEMPLATE_SUBJECT_${lang.toUpperCase()}`;
                    });

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_INSTANCE_MUTATIONS_BODY`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[12],
                        variables.template_tasks[13],
                    );
                })
                .then(() => {
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: externalMails[0],

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: [
                            externalMails[1],
                            externalMails[2],
                        ],

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            externalMails[0],
                            externalMails[1],
                        ],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_enot_body_${lang}`] =
                            `TEMPLATE_BODY_${lang.toUpperCase()}`;
                        params[`ttask_enot_subject_${lang}`] =
                            `TEMPLATE_SUBJECT_${lang.toUpperCase()}`;
                    });

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_EXTERNAL_DEFAULT_LANGUAGE`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[13],
                        variables.template_tasks[14],
                    );
                })
                .then(() => {
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: externalMails[0],

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: [
                            externalMails[1],
                            externalMails[2],
                        ],

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            externalMails[0],
                            externalMails[1],
                        ],

                        // LANGUAGE
                        ttask_enot_external_language:
                            globalThis.dynamicConfig.langs[1],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_enot_body_${lang}`] =
                            `TEMPLATE_BODY_${lang.toUpperCase()}`;
                        params[`ttask_enot_subject_${lang}`] =
                            `TEMPLATE_SUBJECT_${lang.toUpperCase()}`;
                    });

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_EXTERNAL_TEMPLATE_LANGUAGE`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[14],
                        variables.template_tasks[15],
                    );
                })
                .then(() => {
                    const params = {
                        ttask_type: TASK.TYPE_EMAIL_NOTIFICATION,
                        ttask_enot_body: "TEMPLATE_BODY",
                        ttask_enot_subject: "TEMPLATE_SUBJECT",

                        // CALCULATIONS
                        ttask_operations: [
                            {
                                ttjscalc_js: `lib.changeMailAttribute('language', '${globalThis.dynamicConfig.langs[2]}');`,
                                ttjscalc_exec_start: "Y",
                                ttjscalc_exec_end: "N",
                                ttjscalc_exec_hand: "N",
                                ttjscalc_exec_recalc: "N",
                                ttjscalc_exec_pull: "N",
                                ttjscalc_append_scripts: "[]",
                            },
                        ],

                        // TARGET
                        ttask_enot_tgt_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_tgt: externalMails[0],

                        // BLIND
                        ttask_enot_blind_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_blind_target: [
                            externalMails[1],
                            externalMails[2],
                        ],

                        // COPY
                        ttask_enot_copy_type: NOTIFICATION.TYPE_PLAIN_ADDRESS,
                        ttask_enot_copy_target: [
                            externalMails[0],
                            externalMails[1],
                        ],

                        // LANGUAGE
                        ttask_enot_external_language:
                            globalThis.dynamicConfig.langs[1],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_enot_body_${lang}`] =
                            `TEMPLATE_BODY_${lang.toUpperCase()}`;
                        params[`ttask_enot_subject_${lang}`] =
                            `TEMPLATE_SUBJECT_${lang.toUpperCase()}`;
                    });

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_ENOT_EXTERNAL_INSTANCE_LANGUAGE`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.createLink(
                        variables.template_processes[0],
                        variables.template_tasks[15],
                        variables.template_tasks[16],
                    );
                })
                .then(() =>
                    util.activateProcess(
                        variables.template_processes[0],
                        variables.hashes[0],
                        variables.headers[0],
                        true,
                    ),
                )
                .then((body) => {
                    // find ID of created INSTANCE_VARIABLEs and update their values
                    variables.instance_processes.push(body.IPROC_ID);
                    const iprocId = body.IPROC_ID;
                    const nextItaskId = body.NEXT_ITASK_ID;
                    return util
                        .get(
                            `/processes/${iprocId}/variables`,
                            variables.hashes[0],
                        )
                        .then((body) => {
                            const vars = body.items;

                            // Target
                            const targetUserVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[0],
                            }).id;
                            const targetUserVariableMultiId = _.find(vars, {
                                tvar_id: variables.template_variables[1],
                            }).id;
                            const targetPlainVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[2],
                            }).id;
                            const targetPlain2VariableId = _.find(vars, {
                                tvar_id: variables.template_variables[9],
                            }).id;

                            // Reply
                            const replyUserVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[3],
                            }).id;
                            const replyUserVariableMultiId = _.find(vars, {
                                tvar_id: variables.template_variables[4],
                            }).id;
                            const replyPlainVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[5],
                            }).id;
                            const replyPlain2VariableId = _.find(vars, {
                                tvar_id: variables.template_variables[10],
                            }).id;

                            // Blind
                            const blindUserVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[6],
                            }).id;
                            const blindUserVariableMultiId = _.find(vars, {
                                tvar_id: variables.template_variables[7],
                            }).id;
                            const blindPlainVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[8],
                            }).id;
                            const blindPlain2VariableId = _.find(vars, {
                                tvar_id: variables.template_variables[11],
                            }).id;

                            // Plain copy
                            const copyUserVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[12],
                            }).id;
                            const copyUserVariableMultiId = _.find(vars, {
                                tvar_id: variables.template_variables[13],
                            }).id;
                            const copyPlainVariableId = _.find(vars, {
                                tvar_id: variables.template_variables[14],
                            }).id;
                            const copyPlain2VariableId = _.find(vars, {
                                tvar_id: variables.template_variables[15],
                            }).id;
                            const updateVarsBody = [
                                // Target
                                {
                                    id: targetUserVariableId,
                                    value: variables.users[0].USER_ID,
                                },
                                {
                                    id: targetUserVariableMultiId,
                                    value: [
                                        variables.users[1].USER_ID,
                                        variables.users[2].USER_ID,
                                    ],
                                },
                                {
                                    id: targetPlainVariableId,
                                    value: variables.users[2].USER_EMAIL,
                                },
                                {
                                    id: targetPlain2VariableId,
                                    value: variables.users[0].USER_EMAIL,
                                },
                                // Reply
                                {
                                    id: replyUserVariableId,
                                    value: variables.users[1].USER_ID,
                                },
                                {
                                    id: replyUserVariableMultiId,
                                    value: [
                                        variables.users[2].USER_ID,
                                        variables.users[0].USER_ID,
                                    ],
                                },
                                {
                                    id: replyPlainVariableId,
                                    value: variables.users[0].USER_EMAIL,
                                },
                                {
                                    id: replyPlain2VariableId,
                                    value: variables.users[1].USER_EMAIL,
                                },
                                // Blind
                                {
                                    id: blindUserVariableId,
                                    value: variables.users[2].USER_ID,
                                },
                                {
                                    id: blindUserVariableMultiId,
                                    value: [
                                        variables.users[0].USER_ID,
                                        variables.users[1].USER_ID,
                                    ],
                                },
                                {
                                    id: blindPlainVariableId,
                                    value: variables.users[1].USER_EMAIL,
                                },
                                {
                                    id: blindPlain2VariableId,
                                    value: variables.users[2].USER_EMAIL,
                                },
                                // Plain copy
                                {
                                    id: copyUserVariableId,
                                    value: variables.users[0].USER_ID,
                                },
                                {
                                    id: copyUserVariableMultiId,
                                    value: [
                                        variables.users[1].USER_ID,
                                        variables.users[2].USER_ID,
                                    ],
                                },
                                {
                                    id: copyPlainVariableId,
                                    value: variables.users[1].USER_EMAIL,
                                },
                                {
                                    id: copyPlain2VariableId,
                                    value: variables.users[2].USER_EMAIL,
                                },
                            ];

                            return util.post(
                                `/tasks/${nextItaskId}/variables`,
                                variables.hashes[0],
                                updateVarsBody,
                            );
                        })
                        .then(() =>
                            util.finishTask(nextItaskId, variables.hashes[0]),
                        );
                })
                .then(() =>
                    // get ID of created INSTANCE_TASKs
                    util.get(
                        `/processes/${variables.instance_processes[0]}/tasks?all=true&order=itask_id`,
                        variables.hashes[0],
                    ),
                )
                .then((body) => {
                    variables.instance_tasks.push(
                        ...body.items.map((iTask) => iTask.id),
                    );
                    globalThis.tasLogger.info(
                        "**********END OF BEFORE HOOK**********",
                    );
                    done();
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    done(err);
                });
        });

        it("Enot Task 1 (PROCESS_OWNER) - recipients match", (done) => {
            const PROCESS_OWNER = variables.users[0];
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1001],
            );
            const expected = {
                to: [PROCESS_OWNER.USER_EMAIL],
                replyTo: [PROCESS_OWNER.USER_EMAIL],
                bcc: [PROCESS_OWNER.USER_EMAIL],
                cc: [PROCESS_OWNER.USER_EMAIL],
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("Enot Task 2 (PROCESS_GUARANTOR) - recipients match", (done) => {
            const PROCESS_GUARANTOR = variables.users[0];
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1002],
            );
            const expected = {
                to: [PROCESS_GUARANTOR.USER_EMAIL],
                replyTo: [PROCESS_GUARANTOR.USER_EMAIL],
                bcc: [PROCESS_GUARANTOR.USER_EMAIL],
                cc: [PROCESS_GUARANTOR.USER_EMAIL],
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 3 (LAST_TASK_SOLVER) - recipients match", (done) => {
            const LAST_TASK_SOLVER = variables.users[0];
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1003],
            );
            const expected = {
                to: [LAST_TASK_SOLVER.USER_EMAIL],
                replyTo: [LAST_TASK_SOLVER.USER_EMAIL],
                bcc: [LAST_TASK_SOLVER.USER_EMAIL],
                cc: [LAST_TASK_SOLVER.USER_EMAIL],
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 4 (PLAIN) - recipients match", (done) => {
            const PLAIN_TARGET = variables.users[2];
            const PLAIN_REPLY = variables.users[1];
            const PLAIN_BLIND = variables.users[0];
            const PLAIN_COPY = variables.users[1];
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1004],
            );
            const expected = {
                to: [PLAIN_TARGET.USER_EMAIL],
                replyTo: [PLAIN_REPLY.USER_EMAIL],
                bcc: [PLAIN_BLIND.USER_EMAIL],
                cc: [PLAIN_COPY.USER_EMAIL],
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 5 (ORGANIZATION_STRUCTURE) - recipients match", async () => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1005],
            );
            const expected = {
                to: [globalThis.dynamicConfig.mail.noReplyMail],
                replyTo: variables.users.map((user) => user.USER_EMAIL).sort(),
                bcc: variables.users.map((user) => user.USER_EMAIL).sort(),
                cc: variables.users.map((user) => user.USER_EMAIL).sort(),
            };

            assert.includeMembers(mailOptions.to, expected.to);
            assert.includeMembers(mailOptions.replyTo, expected.replyTo);
            assert.includeMembers(mailOptions.bcc, expected.bcc);
            assert.includeMembers(mailOptions.cc, expected.cc);
        });

        it("ENOT Task 6 (DLU VARIABLE) - recipients match", (done) => {
            const TARGET_USER = variables.users[0];
            const REPLY_USER = variables.users[1];
            const BLIND_USER = variables.users[2];
            const COPY_USER = variables.users[0];
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1006],
            );
            const expected = {
                to: [TARGET_USER.USER_EMAIL],
                replyTo: [REPLY_USER.USER_EMAIL],
                bcc: [BLIND_USER.USER_EMAIL],
                cc: [COPY_USER.USER_EMAIL],
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 7 (DLU VARIABLE MULTI) - recipients match", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1007],
            );
            const expected = {
                to: [globalThis.dynamicConfig.mail.noReplyMail],
                replyTo: [
                    variables.users[2].USER_EMAIL,
                    variables.users[0].USER_EMAIL,
                ].sort(),
                bcc: [
                    variables.users[0].USER_EMAIL,
                    variables.users[1].USER_EMAIL,
                    variables.users[2].USER_EMAIL,
                ].sort(),
                cc: [
                    variables.users[1].USER_EMAIL,
                    variables.users[2].USER_EMAIL,
                ].sort(),
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 8 (ROLE) - recipients match", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1008],
            );
            const expected = {
                to: [globalThis.dynamicConfig.mail.noReplyMail],
                replyTo: variables.users.map((user) => user.USER_EMAIL).sort(),
                bcc: variables.users.map((user) => user.USER_EMAIL).sort(),
                cc: variables.users.map((user) => user.USER_EMAIL).sort(),
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 9 (PLAIN VARIABLE) - recipients match", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[1009],
            );
            const expected = {
                to: [globalThis.dynamicConfig.mail.noReplyMail],
                replyTo: [
                    variables.users[0].USER_EMAIL,
                    variables.users[1].USER_EMAIL,
                    externalMails[0],
                ],
                bcc: [
                    externalMails[0],
                    variables.users[0].USER_EMAIL,
                    variables.users[1].USER_EMAIL,
                    variables.users[2].USER_EMAIL,
                ],
                cc: [
                    variables.users[2].USER_EMAIL,
                    externalMails[0],
                    variables.users[1].USER_EMAIL,
                ],
            };

            should.deepEqual(mailOptions, expected);
            done();
        });
    });

    describe("Resolve mutations by user language (Instance mutation => Template mutation => Instance => Template)", () => {
        it("ENOT Task 10 (Template body) - recipient <=> body & subject match", (done) => {
            globalThis.dynamicConfig.langs.forEach((lang, index) => {
                const mailOptions = filterObject(
                    globalThis.dynamicConfig.test.tests.mails[1010 + index],
                    ["to", "replyTo", "subject", "html"],
                );
                const expected = {
                    to: [variables.extra_users[index].USER_EMAIL],
                    replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                    // TODO: Multiple emails will be sent, can't simply compare it like this
                    // bcc: [],
                    // cc: [],
                    subject: "TEMPLATE_SUBJECT",
                    html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    TEMPLATE_BODY\n</body>\n`,
                };

                should.deepEqual(mailOptions, expected);
            });
            done();
        });

        it("ENOT Task 11 (Instance body) - recipient <=> body & subject match", (done) => {
            globalThis.dynamicConfig.langs.forEach((lang, index) => {
                const user = variables.extra_users[index];
                const mailOptions = filterObject(
                    globalThis.dynamicConfig.test.tests.mails[
                        1010 + globalThis.dynamicConfig.langs.length + index
                    ],
                    ["to", "replyTo", "subject", "html"],
                );
                const expected = {
                    to: [user.USER_EMAIL],
                    replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                    // TODO: Multiple emails will be sent, can't simply compare it like this
                    // bcc: [],
                    // cc: [],
                    subject: "INSTANCE_SUBJECT",
                    html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    INSTANCE_BODY\n</body>\n`,
                };

                should.deepEqual(mailOptions, expected);
            });
            done();
        });

        it("ENOT Task 12 (Template mutation body) - recipient <=> body & subject match", (done) => {
            globalThis.dynamicConfig.langs.forEach((lang, index) => {
                const user = variables.extra_users[index];
                const mailOptions = filterObject(
                    globalThis.dynamicConfig.test.tests.mails[
                        1010 + globalThis.dynamicConfig.langs.length * 2 + index
                    ],
                    ["to", "replyTo", "subject", "html"],
                );
                const expected = {
                    to: [user.USER_EMAIL],
                    replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                    // TODO: Multiple emails will be sent, can't simply compare it like this
                    // bcc: [],
                    // cc: [],
                    subject: `TEMPLATE_SUBJECT_${user.USER_LANGUAGE.toUpperCase()}`,
                    html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    TEMPLATE_BODY_${user.USER_LANGUAGE.toUpperCase()}\n</body>\n`,
                };

                should.deepEqual(mailOptions, expected);
            });
            done();
        });

        it("ENOT Task 13 (Instance mutation body) - recipient <=> body & subject match", (done) => {
            globalThis.dynamicConfig.langs.forEach((lang, index) => {
                const user = variables.extra_users[index];
                const mailOptions = filterObject(
                    globalThis.dynamicConfig.test.tests.mails[
                        1010 + globalThis.dynamicConfig.langs.length * 3 + index
                    ],
                    ["to", "replyTo", "subject", "html"],
                );
                const expected = {
                    to: [user.USER_EMAIL],
                    replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                    // TODO: Multiple emails will be sent, can't simply compare it like this
                    // bcc: [],
                    // cc: [],
                    subject: `INSTANCE_SUBJECT_${user.USER_LANGUAGE.toUpperCase()}`,
                    html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    INSTANCE_BODY_${user.USER_LANGUAGE.toUpperCase()}\n</body>\n`,
                };

                should.deepEqual(mailOptions, expected);
            });
            done();
        });
    });

    describe("Resolve external language (Instance => Template => Default)", () => {
        it("ENOT Task 14 (Default external language) - external recipient DEFAULT language match", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[
                    1010 + globalThis.dynamicConfig.langs.length * 4
                ],
                ["to", "replyTo", "bcc", "subject", "html", "cc"],
            );
            const expected = {
                to: [externalMails[0]],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                bcc: [externalMails[1], externalMails[2]],
                cc: [externalMails[0], externalMails[1]],
                subject: `TEMPLATE_SUBJECT_${globalThis.dynamicConfig.defaultLang.toUpperCase()}`,
                html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    TEMPLATE_BODY_${globalThis.dynamicConfig.defaultLang.toUpperCase()}\n</body>\n`,
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 15 (Template external language) - external recipient TEMPLATE language match", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[
                    1010 + globalThis.dynamicConfig.langs.length * 4 + 1
                ],
                ["to", "replyTo", "bcc", "subject", "html", "cc"],
            );
            const expected = {
                to: [externalMails[0]],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                bcc: [externalMails[1], externalMails[2]],
                cc: [externalMails[0], externalMails[1]],
                subject: `TEMPLATE_SUBJECT_${globalThis.dynamicConfig.langs[1].toUpperCase()}`,
                html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    TEMPLATE_BODY_${globalThis.dynamicConfig.langs[1].toUpperCase()}\n</body>\n`,
            };

            should.deepEqual(mailOptions, expected);
            done();
        });

        it("ENOT Task 16 (Instance external language) - external recipient INSTANCE language match", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[
                    1010 + globalThis.dynamicConfig.langs.length * 4 + 2
                ],
                ["to", "replyTo", "bcc", "subject", "html", "cc"],
            );
            const expected = {
                to: [externalMails[0]],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                bcc: [externalMails[1], externalMails[2]],
                cc: [externalMails[0], externalMails[1]],
                subject: `TEMPLATE_SUBJECT_${globalThis.dynamicConfig.langs[2].toUpperCase()}`,
                html: `\n\n<body style="font-size: 100%; font-family: Inter, sans-serif; color: #111827;">\n    <span style=\"color: red;\">${globalThis.dynamicConfig.tas.headerWarning || ""}</span>\n    TEMPLATE_BODY_${globalThis.dynamicConfig.langs[2].toUpperCase()}\n</body>\n`,
            };

            should.deepEqual(mailOptions, expected);
            done();
        });
    });

    describe('Check notifications from "to-pull" task (@t3b-1048)', () => {
        before((done) => {
            variables = util.variables();
            // Reset mails
            globalThis.routerMail = new RouterMail(
                globalThis.container.client.mail,
            );
            globalThis.dynamicConfig.test.tests.mailId = 2001;
            globalThis.dynamicConfig.test.tests.mails = {};
            util.generateUsers(2, `${testName}2`)
                .then(([users, organization, role]) => {
                    variables.users = users;
                    variables.organization_structures = organization;
                    variables.roles = role;
                    return util.logInUsers(variables.users);
                })
                .then((hashes) => {
                    variables.hashes = hashes;
                    return util.generateUsers(2, `${testName}3`);
                })
                .then(([users, organization, role]) => {
                    variables.extra_users = users;
                    variables.organization_structures.push(organization);
                    variables.roles.push(role);
                    return util.logInUsers(variables.extra_users);
                })
                .then((hashes) => {
                    variables.hashes.push(...hashes);
                    // Add mutations
                    const params = {
                        tproc_default_case_name: `${testName}2_DEFAULT`,
                    };
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tproc_name_${lang}`] =
                            `TPROC_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateProcess(
                        `${testName}2`,
                        `${testName}2`,
                        PROCESS.STATUS_ACTIVE,
                        variables.organization_structures[0],
                        null,
                        null,
                        true,
                        params,
                    );
                })
                .then((tProcId) => {
                    variables.template_processes.push(tProcId);
                    return util.findHeaders(tProcId);
                })
                .then((headers) => {
                    variables.headers.push(headers[0].id);
                    //
                    const params = {
                        ttask_assesment_hierarchy: TASK.ASSESMENT_HIERARCHY_ALL,
                        ttask_assesment_method: TASK.ASSESMENT_METHOD_PULL,
                        ttask_assesment_role_id: variables.roles[0],
                        ttask_assesment_orgstr_cnst:
                            variables.organization_structures[0],
                        ttask_operations: [],
                    };

                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        // Mutations
                        params[`ttask_name_${lang}`] =
                            `TTASK_NAME_${lang.toUpperCase()}`;
                        params[`ttask_description_${lang}`] =
                            `TTASK_DESCRIPTION_${lang.toUpperCase()}`;

                        // Calculations
                        params.ttask_operations.push({
                            ttjscalc_js: `task.ITASK_COMMENT_${lang.toUpperCase()} = 'ITASK_COMMENT_${lang.toUpperCase()}'`,
                            ttjscalc_exec_start: "N",
                            ttjscalc_exec_end: "N",
                            ttjscalc_exec_hand: "N",
                            ttjscalc_exec_recalc: "N",
                            ttjscalc_exec_pull: "Y",
                            ttjscalc_append_scripts: "[]",
                        });
                    });

                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_TO_PULL`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    // Activate user vice
                    return util.setVice(
                        variables.users[0].USER_ID,
                        variables.extra_users[0].USER_ID,
                    );
                })
                .then((viceId) => {
                    variables.vices.push(viceId);
                    // Activate user vice
                    return util.setVice(
                        variables.users[1].USER_ID,
                        variables.extra_users[1].USER_ID,
                    );
                })
                .then((viceId) => {
                    variables.vices.push(viceId);
                    // Set custom user parameters
                    return util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[0],
                            },
                        ],
                        variables.hashes[0],
                    );
                })
                .then(() =>
                    util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[1],
                            },
                        ],
                        variables.hashes[1],
                    ),
                )
                .then(() =>
                    util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[2],
                            },
                        ],
                        variables.hashes[2],
                    ),
                )
                .then(() =>
                    util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.SEND_NEW_TASK_NOTIFICATION,
                                value: USER_PARAMETER.ENABLED_NO,
                            },
                            {
                                name: USER_PARAMETER.SEND_TASK_TO_PULL_NOTIFICATION,
                                value: USER_PARAMETER.ENABLED_NO,
                            },
                        ],
                        variables.hashes[3],
                    ),
                )
                .then(() =>
                    util.activateProcess(
                        variables.template_processes[0],
                        variables.hashes[0],
                        variables.headers[0],
                        true,
                    ),
                )
                .then((body) => {
                    variables.instance_processes.push(body.IPROC_ID);
                    globalThis.tasLogger.info(
                        "**********END OF BEFORE HOOK**********",
                    );
                    done();
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    done(err);
                });
        });

        it("First email matches (mutations)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[2005],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );
            const expected = {
                to: [variables.users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "iTaskToPull", locale: globalThis.dynamicConfig.langs[0] })} | TTASK_NAME_${globalThis.dynamicConfig.langs[0].toUpperCase()}: ${testName}2_DEFAULT`,
            };

            mailOptions.html.indexOf("?uv-id=").should.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}?uv-id=`,
                )
                .should.equal(-1);

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[0]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[0].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[0].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/tasks/to-pull/`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });

        it("Second email matches (mutations)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[2006],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );
            const expected = {
                to: [variables.users[1].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "iTaskToPull", locale: globalThis.dynamicConfig.langs[1] })} | TTASK_NAME_${globalThis.dynamicConfig.langs[1].toUpperCase()}: ${testName}2_DEFAULT`,
            };

            mailOptions.html.indexOf("?uv-id=").should.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}?uv-id=`,
                )
                .should.equal(-1);

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[1]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[1].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[1].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/tasks/to-pull/`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });

        it("Third email matches (mutations & substitute)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[2007],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );
            const expected = {
                to: [variables.extra_users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "iTaskToPull", locale: globalThis.dynamicConfig.langs[2] })} | TTASK_NAME_${globalThis.dynamicConfig.langs[2].toUpperCase()}: ${testName}2_DEFAULT`,
            };

            mailOptions.html.indexOf("?uv-id=").should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}?uv-id=`,
                )
                .should.not.equal(-1);

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[2]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[2].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[2].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/tasks/to-pull/`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });

        it("There is no fourth email (substitute has disabled notifications)", (done) => {
            should.not.exist(globalThis.dynamicConfig.test.tests.mails[2008]);
            done();
        });
    });

    describe("Change history vars", () => {
        before((done) => {
            // Reset global variables
            variables = util.variables();
            globalThis.routerMail = new RouterMail(
                globalThis.container.client.mail,
            );
            globalThis.dynamicConfig.test.tests.mailId = 0;
            globalThis.dynamicConfig.test.tests.mails = {};
            util.generateUsers(1, `${testName}_historyVars1`)
                .then(([users, organization, role]) => {
                    variables.users = users;
                    variables.organization_structures = organization;
                    variables.roles = role;
                    return util.logInUsers(variables.users);
                })
                .then((hashes) => {
                    variables.hashes = hashes;
                    return util.generateUsers(1, `${testName}_historyVars2`);
                })
                .then(([users, organization, role]) => {
                    variables.extra_users = users;
                    variables.organization_structures.push(organization);
                    variables.roles.push(role);
                    return util.logInUsers(variables.extra_users);
                })
                .then(async (hashes) => {
                    variables.hashes.push(...hashes);
                    return await util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[0],
                            },
                        ],
                        variables.hashes[0],
                    );
                })
                .then(() =>
                    util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[1],
                            },
                        ],
                        variables.hashes[1],
                    ),
                )
                .then(() =>
                    // Activate user vice
                    util.setVice(
                        variables.users[0].USER_ID,
                        variables.extra_users[0].USER_ID,
                    ),
                )
                .then((viceId) => {
                    variables.vices.push(viceId);
                    const params = {};
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tproc_name_${lang}`] =
                            `TPROC_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateProcess(
                        `${testName}_historyVars`,
                        `${testName}_historyVars`,
                        PROCESS.STATUS_ACTIVE,
                        variables.organization_structures[0],
                        null,
                        null,
                        true,
                        params,
                    );
                })
                .then((tProcId) => {
                    variables.template_processes.push(tProcId);
                    return util.findHeaders(tProcId);
                })
                .then((headers) => {
                    variables.headers.push(headers[0].id);

                    // Add mutations
                    const params = {};
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tvar_name_${lang}`] =
                            `VAR_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        `${testName}_historyVars`,
                        VARIABLE.TYPE_TEXT,
                        "DEFAULT",
                        null,
                        null,
                        [],
                        params,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_historyVars`,
                        {
                            ttask_var_mapping: [
                                {
                                    tvar_id: variables.template_variables[0],
                                    usage: "W",
                                    axis_x: null,
                                    axis_y: null,
                                },
                            ],
                        },
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.activateProcess(
                        variables.template_processes[0],
                        variables.hashes[0],
                        variables.headers[0],
                        true,
                    );
                })
                .then((body) => {
                    variables.instance_processes.push(body.IPROC_ID);
                    variables.instance_tasks.push(body.NEXT_ITASK_ID);
                    return util.finishTask(
                        variables.instance_tasks[0],
                        variables.hashes[0],
                    );
                })
                .then(() => {
                    globalThis.tasLogger.info(
                        "**********END OF BEFORE HOOK**********",
                    );
                    done();
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    done(err);
                });
        });

        it("There should be no outgoing emails", (done) => {
            // '0' and '1' is 'Vice activation'
            should.not.exist(globalThis.dynamicConfig.test.tests.mails[2]);
            done();
        });

        it("Change variable", (done) => {
            util.updateVariable(
                variables.instance_processes[0],
                variables.template_variables[0],
                "CHANGED",
            )
                .then(() => {
                    done();
                })
                .catch((err) => {
                    done(err);
                });
        });

        it("There should be 2 outgoing emails", (done) => {
            should.exist(globalThis.dynamicConfig.test.tests.mails[2]); // Notification about the change of a Variable value User1
            should.exist(globalThis.dynamicConfig.test.tests.mails[3]); // Notification about the change of a Variable value ExternalUser1
            should.exist(globalThis.dynamicConfig.test.tests.mails[4]); // Notification about the change of a Variable value Tst_api_Admin
            should.not.exist(globalThis.dynamicConfig.test.tests.mails[5]);
            done();
        });

        it.skip("First email should match (mutations)", (done) => {
            const mail = globalThis.dynamicConfig.test.tests.mails[2];

            const mailOptions = filterObject(mail, [
                "replyTo",
                "bcc",
                "subject",
                "language",
                "html",
            ]);
            const expected = {
                // to: [variables.users[0].USER_EMAIL], // Do not use 'to' field, because there are 2 emails with the same lang at this point
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "changeHistoryVars", locale: globalThis.dynamicConfig.langs[0] })}`,
            };

            mailOptions.html
                .indexOf(
                    `VAR_NAME_${globalThis.dynamicConfig.langs[0].toUpperCase()}`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}?uv-id=`,
                )
                .should.equal(-1);

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });

        it.skip("Second email should match (mutations & substitute)", (done) => {
            const mail = globalThis.dynamicConfig.test.tests.mails[4];

            const mailOptions = filterObject(mail, [
                "to",
                "replyTo",
                "bcc",
                "subject",
                "language",
                "html",
            ]);
            const expected = {
                to: [variables.extra_users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "changeHistoryVars", locale: globalThis.dynamicConfig.langs[1] })}`,
            };

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[1]);
            mailOptions.html
                .indexOf(
                    `VAR_NAME_${globalThis.dynamicConfig.langs[1].toUpperCase()}`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}?uv-id=`,
                )
                .should.not.equal(-1);

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });
    });

    describe("Task Solver Assign", () => {
        before((done) => {
            // Reset global variables
            variables = util.variables();
            globalThis.routerMail = new RouterMail(
                globalThis.container.client.mail,
            );
            globalThis.dynamicConfig.test.tests.mailId = 0;
            globalThis.dynamicConfig.test.tests.mails = {};
            util.generateUsers(1, `${testName}_taskSolverAssign1`)
                .then(([users, organization, role]) => {
                    variables.users = users;
                    variables.organization_structures = organization;
                    variables.roles = role;
                    return util.logInUsers(variables.users);
                })
                .then((hashes) => {
                    variables.hashes = hashes;
                    return util.generateUsers(
                        1,
                        `${testName}_taskSolverAssign2`,
                    );
                })
                .then(([users, organization, role]) => {
                    variables.extra_users = users;
                    variables.organization_structures.push(organization);
                    variables.roles.push(role);
                    return util.logInUsers(variables.extra_users);
                })
                .then(async (hashes) => {
                    variables.hashes.push(...hashes);
                    return await util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[0],
                            },
                        ],
                        variables.hashes[0],
                    );
                })
                .then(() =>
                    util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[1],
                            },
                        ],
                        variables.hashes[1],
                    ),
                )
                .then(() =>
                    // Activate user vice
                    util.setVice(
                        variables.users[0].USER_ID,
                        variables.extra_users[0].USER_ID,
                    ),
                )
                .then((viceId) => {
                    variables.vices.push(viceId);
                    const params = {};
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tproc_name_${lang}`] =
                            `TPROC_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateProcess(
                        `${testName}_taskSolverAssign`,
                        `${testName}_taskSolverAssign`,
                        PROCESS.STATUS_ACTIVE,
                        variables.organization_structures[0],
                        null,
                        null,
                        true,
                        params,
                    );
                })
                .then((tProcId) => {
                    variables.template_processes.push(tProcId);
                    return util.findHeaders(tProcId);
                })
                .then((headers) => {
                    variables.headers.push(headers[0].id);
                    // Add mutations
                    const params = {};
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tvar_name_${lang}`] =
                            `VAR_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        `${testName}_taskSolverAssign`,
                        VARIABLE.TYPE_TEXT,
                        "DEFAULT",
                        null,
                        null,
                        [],
                        params,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    const params = {
                        ttask_operations: [],
                        ttask_assesment_method: TASK.ASSESMENT_METHOD_SELECT,
                        ttask_var_mapping: [
                            {
                                tvar_id: variables.template_variables[0],
                                usage: "W",
                                axis_x: null,
                                axis_y: null,
                            },
                        ],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_description_${lang}`] =
                            `TTASK_DESCRIPTION_${lang.toUpperCase()}`;

                        // Calculations
                        params.ttask_operations.push({
                            ttjscalc_js: `task.ITASK_COMMENT_${lang.toUpperCase()} = 'ITASK_COMMENT_${lang.toUpperCase()}'`,
                            ttjscalc_exec_start: "Y",
                            ttjscalc_exec_end: "N",
                            ttjscalc_exec_hand: "N",
                            ttjscalc_exec_recalc: "N",
                            ttjscalc_exec_pull: "N",
                            ttjscalc_append_scripts: "[]",
                        });
                    });
                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_taskSolverAssign`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.activateProcess(
                        variables.template_processes[0],
                        variables.hashes[0],
                        variables.headers[0],
                        true,
                    );
                })
                .then((body) => {
                    variables.instance_processes.push(body.IPROC_ID);
                })
                .then(() => {
                    globalThis.tasLogger.info(
                        "**********END OF BEFORE HOOK**********",
                    );
                    done();
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    done(err);
                });
        });

        it("There should be 2 outgoing emails", (done) => {
            should.exist(globalThis.dynamicConfig.test.tests.mails[2]);
            should.exist(globalThis.dynamicConfig.test.tests.mails[3]);
            // should.notEqual(globalThis.dynamicConfig.test.tests.mails[3], 'undefined');
            should.not.exist(globalThis.dynamicConfig.test.tests.mails[4]);
            done();
        });

        it("First email should match (mutations)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[2],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[0]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[0].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`HEADER_NAME_${globalThis.dynamicConfig.langs[0].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(`${globalThis.dynamicConfig.frontendUrl}/tasks/task/`)
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);
            mailOptions.html.indexOf("?uv-id=").should.equal(-1);
            // Vypocet se provede az po vybrani resitele, je to spravne? Novy typ vypoctu, neco jako PRE_PULL ?
            // (pokud je komentar zmenen vypoctem z predchoziho ukolu, dostane se tam)
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[0].toUpperCase()}`).should.not.equal(-1);

            const expected = {
                to: [variables.users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "taskSolverAssign", locale: globalThis.dynamicConfig.langs[0] })} | ${testName}_taskSolverAssign`,
            };

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });

        it("Second email should match (mutations & substitute)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[3],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[1]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[1].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`HEADER_NAME_${globalThis.dynamicConfig.langs[1].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(`${globalThis.dynamicConfig.frontendUrl}/tasks/task/`)
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);
            mailOptions.html.indexOf("?uv-id=").should.not.equal(-1);
            mailOptions.html
                .indexOf("?uv-id=")
                .should.not.equal(mailOptions.html.lastIndexOf("?uv-id="));
            // Vypocet se provede az po vybrani resitele, je to spravne? Novy typ vypoctu, neco jako PRE_PULL ?
            // (pokud je komentar zmenen vypoctem z predchoziho ukolu, dostane se tam)
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[1].toUpperCase()}`).should.not.equal(-1);

            const expected = {
                to: [variables.extra_users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "taskSolverAssign", locale: globalThis.dynamicConfig.langs[1] })} | ${testName}_taskSolverAssign`,
            };

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });
    });

    describe("Task Timing", () => {
        before((done) => {
            // Reset global variables
            variables = util.variables();
            globalThis.routerMail = new RouterMail(
                globalThis.container.client.mail,
            );
            globalThis.dynamicConfig.test.tests.mailId = 0;
            globalThis.dynamicConfig.test.tests.mails = {};
            util.generateUsers(1, `${testName}_taskTiming1`)
                .then(([users, organization, role]) => {
                    variables.users = users;
                    variables.organization_structures = organization;
                    variables.roles = role;
                    return util.logInUsers(variables.users);
                })
                .then((hashes) => {
                    variables.hashes = hashes;
                    return util.generateUsers(1, `${testName}_taskTiming2`);
                })
                .then(([users, organization, role]) => {
                    variables.extra_users = users;
                    variables.organization_structures.push(organization);
                    variables.roles.push(role);
                    return util.logInUsers(variables.extra_users);
                })
                .then(async (hashes) => {
                    variables.hashes.push(...hashes);
                    return await util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[0],
                            },
                        ],
                        variables.hashes[0],
                    );
                })
                .then(() =>
                    util.setUserParameter(
                        [
                            {
                                name: USER_PARAMETER.LANGUAGE_CLIENT,
                                value: globalThis.dynamicConfig.langs[1],
                            },
                        ],
                        variables.hashes[1],
                    ),
                )
                .then(() =>
                    // Activate user vice
                    util.setVice(
                        variables.users[0].USER_ID,
                        variables.extra_users[0].USER_ID,
                    ),
                )
                .then((viceId) => {
                    variables.vices.push(viceId);
                    const params = {};
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tproc_name_${lang}`] =
                            `TPROC_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateProcess(
                        `${testName}_taskTiming`,
                        `${testName}_taskTiming`,
                        PROCESS.STATUS_ACTIVE,
                        variables.organization_structures[0],
                        null,
                        null,
                        true,
                        params,
                    );
                })
                .then((tProcId) => {
                    variables.template_processes.push(tProcId);
                    return util.findHeaders(tProcId);
                })
                .then((headers) => {
                    variables.headers.push(headers[0].id);
                    // Add mutations
                    const params = {};
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`tvar_name_${lang}`] =
                            `VAR_NAME_${lang.toUpperCase()}`;
                    });
                    return util.createTemplateVariable(
                        variables.template_processes[0],
                        `${testName}_taskTiming`,
                        VARIABLE.TYPE_TEXT,
                        "DEFAULT",
                        null,
                        null,
                        [],
                        params,
                    );
                })
                .then((tVarId) => {
                    variables.template_variables.push(tVarId);
                    const params = {
                        ttask_operations: [],
                        // Supervisor will select the start time of the task
                        ttask_due_offset: { type: "po", value: "" },
                        ttask_var_mapping: [
                            {
                                tvar_id: variables.template_variables[0],
                                usage: "W",
                                axis_x: null,
                                axis_y: null,
                            },
                        ],
                    };

                    // Add mutations
                    globalThis.dynamicConfig.langs.forEach((lang) => {
                        params[`ttask_description_${lang}`] =
                            `TTASK_DESCRIPTION_${lang.toUpperCase()}`;

                        // Calculations
                        params.ttask_operations.push({
                            ttjscalc_js: `task.ITASK_COMMENT_${lang.toUpperCase()} = 'ITASK_COMMENT_${lang.toUpperCase()}'`,
                            ttjscalc_exec_start: "Y",
                            ttjscalc_exec_end: "N",
                            ttjscalc_exec_hand: "N",
                            ttjscalc_exec_recalc: "N",
                            ttjscalc_exec_pull: "N",
                            ttjscalc_append_scripts: "[]",
                        });
                    });
                    return util.createTtask(
                        variables.template_processes[0],
                        `${testName}_taskTiming`,
                        params,
                    );
                })
                .then((ttaskId) => {
                    variables.template_tasks.push(ttaskId);
                    return util.activateProcess(
                        variables.template_processes[0],
                        variables.hashes[0],
                        variables.headers[0],
                        true,
                    );
                })
                .then((body) => {
                    variables.instance_processes.push(body.IPROC_ID);
                })
                .then(() => {
                    globalThis.tasLogger.info(
                        "**********END OF BEFORE HOOK**********",
                    );
                    done();
                })
                .catch((err) => {
                    globalThis.tasLogger.error(err);
                    done(err);
                });
        });

        it("There should be 2 outgoing emails", (done) => {
            should.exist(globalThis.dynamicConfig.test.tests.mails[2]);
            should.exist(globalThis.dynamicConfig.test.tests.mails[3]);
            should.not.exist(globalThis.dynamicConfig.test.tests.mails[4]);
            done();
        });

        it("First email should match (mutations)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[2],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[0]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[0].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`HEADER_NAME_${globalThis.dynamicConfig.langs[0].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(`${globalThis.dynamicConfig.frontendUrl}/tasks/task/`)
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);
            mailOptions.html.indexOf("?uv-id=").should.equal(-1);
            // Vypocet se provede az po vybrani resitele, je to spravne? Novy typ vypoctu, neco jako PRE_PULL ?
            // (pokud je komentar zmenen vypoctem z predchoziho ukolu, dostane se tam)
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[0].toUpperCase()}`).should.not.equal(-1);

            const expected = {
                to: [variables.users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "taskTiming", locale: globalThis.dynamicConfig.langs[0] })} | ${testName}_taskTiming`,
            };

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });

        it("Second email should match (mutations & substitute)", (done) => {
            const mailOptions = filterObject(
                globalThis.dynamicConfig.test.tests.mails[3],
                ["to", "replyTo", "bcc", "subject", "language", "html"],
            );

            // mailOptions.language.should.equal(globalThis.dynamicConfig.langs[1]);
            mailOptions.html
                .indexOf(
                    `TTASK_DESCRIPTION_${globalThis.dynamicConfig.langs[1].toUpperCase()}`,
                )
                .should.not.equal(-1);
            // mailOptions.html.indexOf(`HEADER_NAME_${globalThis.dynamicConfig.langs[1].toUpperCase()}`).should.not.equal(-1);
            mailOptions.html
                .indexOf(`${globalThis.dynamicConfig.frontendUrl}/tasks/task/`)
                .should.not.equal(-1);
            mailOptions.html
                .indexOf(
                    `${globalThis.dynamicConfig.frontendUrl}/cases/case/${variables.instance_processes[0]}`,
                )
                .should.not.equal(-1);
            mailOptions.html.indexOf("?uv-id=").should.not.equal(-1);
            mailOptions.html
                .indexOf("?uv-id=")
                .should.not.equal(mailOptions.html.lastIndexOf("?uv-id="));
            // Vypocet se provede az po vybrani resitele, je to spravne? Novy typ vypoctu, neco jako PRE_PULL ?
            // (pokud je komentar zmenen vypoctem z predchoziho ukolu, dostane se tam)
            // mailOptions.html.indexOf(`ITASK_COMMENT_${globalThis.dynamicConfig.langs[1].toUpperCase()}`).should.not.equal(-1);

            const expected = {
                to: [variables.extra_users[0].USER_EMAIL],
                bcc: [],
                replyTo: [globalThis.dynamicConfig.mail.noReplyMail],
                subject: `${globalThis.__({ phrase: "taskTiming", locale: globalThis.dynamicConfig.langs[1] })} | ${testName}_taskTiming`,
            };

            const mailOptionsFiltered = filterObject(mailOptions, [
                "to",
                "bcc",
                "replyTo",
                "subject",
            ]);
            should.deepEqual(mailOptionsFiltered, expected);
            done();
        });
    });
});
