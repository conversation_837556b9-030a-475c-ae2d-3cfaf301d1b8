// @ts-nocheck

import * as util from "../utils/api";

import should from "should";
import _ from "lodash";
import { assert } from "chai";

const genRanHex = (size: number): string =>
    [...Array(size)]
        .map(() => Math.floor(Math.random() * 16).toString(16))
        .join("");

const name = `test_template${genRanHex(6)}`;
let iProcId;
let tVarIdFirst;
let tVarIdSecond;
let tTaskId;
let iTaskId;
let tProcId;
const tvarName = `_${genRanHex(6)}`;

describe("Template:", () => {
    it("TC0630 | Get all template processes", async () => {
        const res = await agent
            .get("/template-processes")
            .set("Authorization", authorizationTokenAdmin)
            .expect(200);
        res.status.should.equal(200);
    });

    it("TC0631 | Create template process", async () => {
        tProcId = await util.createTemplateProcess(
            name,
            "Hello world",
            "A",
            1,
            -1,
        );
    });

    it("TC0632 | Get the new template process", async () => {
        const res = await agent
            .get(`/template-processes/${tProcId[0]}/${tProcId[1]}`)
            .set("Authorization", authorizationTokenAdmin)
            .expect(200);
        res.status.should.equal(200);
    });

    it("TC0633 | Create first template variable", async () => {
        tVarIdFirst = await util.createTemplateVariable(
            tProcId,
            "testVarOne",
            "T",
        );
    });

    it("TC0634 | Get the new template variable", (done) => {
        agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables/${tVarIdFirst}`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200)
            .end((err, res) => {
                res.body.should.not.equal("");
                done();
            });
    });

    it("TC0635 | Create second template variable", (done) => {
        util.createTemplateVariable(tProcId, "testVarTwo", "N")
            .then((tVarId) => {
                tVarIdSecond = tVarId;
                done();
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0636 | Get the new template variable", (done) => {
        agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables/${tVarIdSecond}`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200)
            .end((err, res) => {
                res.status.should.not.equal("");
                done();
            });
    });

    it("TC0637 | Get all template variables", (done) => {
        agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0638 | Update the first variable", (done) => {
        util.updateTemplateVariable(
            tProcId,
            tVarIdFirst,
            "testValue",
            "T",
            `name1${tvarName}`,
        )
            .then((tVarId) => {
                tVarIdSecond = tVarId;
                done();
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0639 | Confirm change of the first variable", (done) => {
        agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables/${tVarIdFirst}`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200)
            .end((err, res) => {
                res.body.tvar_value.should.equal("testValue");
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0640 | Update value of the second variable", (done) => {
        util.updateTemplateVariable(
            tProcId,
            tVarIdSecond,
            7892,
            "N",
            `name2${tvarName}`,
        )
            .then((tVarId) => {
                tVarIdSecond = tVarId;
                done();
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0641 | Confirm change of the second variable", async () => {
        const res = await agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables/${tVarIdSecond}`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200);
        res.body.tvar_value.should.equal(7892);
        res.status.should.equal(200);
    });

    it('TC0642 | Update second variable - tvar_type: "D" without value: null', (done) => {
        const body = {
            tvar_id: tVarIdSecond,
            tvar_type: "D",
            tvar_name: "",
        };
        agent
            .post(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-variables`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .send(body)
            .expect(200)
            .end((err, res) => {
                res.body.result.should.equal(true);
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0643 | Create template task to the new template process", (done) => {
        const params = {
            ttask_var_mapping: [
                {
                    tvar_id: tVarIdFirst,
                    usage: "W",
                    axis_x: null,
                    axis_y: null,
                },
                {
                    tvar_id: tVarIdSecond,
                    usage: "W",
                    axis_x: null,
                    axis_y: null,
                },
            ],
        };
        util.createTtask(tProcId, `task_${name}`, params)
            .then((taskId) => {
                tTaskId = taskId;
                done();
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0644 | Get the new template task", (done) => {
        agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-tasks/${tTaskId}`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200)
            .end((err, res) => {
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0645 | Confirm that there is a task in the new process", async () => {
        const res = await agent
            .get(
                `/template-processes/${tProcId[0]}/${tProcId[1]}/template-tasks`,
            )
            .set("Authorization", authorizationTokenAdmin)
            .expect(200);
        res.body.total_count.should.not.equal(0);
    });

    it("Export template process by TPROC_ID and VERSION to HTML", async () => {
        const res = await agent
            .get(`/template-processes/${tProcId[0]}/${tProcId[1]}/html`)
            .set("Authorization", authorizationTokenAdmin)
            .expect(200);

        assert.exists(res.body.result);
        assert.match(res.body.result, /^<!DOCTYPE html>/);
    });

    it("TC0646 | Activate Process", (done) => {
        util.findHeaders(tProcId)
            .then((headers) => {
                const body = {
                    header_id: headers[0].id,
                };
                agent
                    .post(`/processes/activate/${tProcId[0]}/${tProcId[1]}`)
                    .set("Authorization", authorizationTokenAdmin)
                    .send(body)
                    .expect(200)
                    .end((err, res) => {
                        iProcId = res.body.IPROC_ID;
                        res.body.result.should.equal(true);
                        res.status.should.equal(200);
                        done();
                    });
            })
            .catch((err) => {
                done(err);
            });
    });

    it("TC0647 | Find the instance task ID (iTaskId)", (done) => {
        agent
            .get("/tasks/mine")
            .set("Authorization", authorizationTokenAdmin)
            .expect(200)
            .end((err, res) => {
                res.body.should.not.equal("");
                res.status.should.equal(200);
                const task = _.find(res.body.items, {
                    iproc_id: Number(iProcId),
                });
                should.exist(task);
                iTaskId = task.id;
                done();
            });
    });

    it("TC0648 | Task instance finish", (done) => {
        agent
            .post(`/tasks/finish/${iTaskId}`)
            .set("Authorization", authorizationTokenAdmin)
            .send({})
            .expect(200)
            .end((err, res) => {
                res.body.result.should.equal(true);
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0649 | Delete the new template process", (done) => {
        agent
            .delete(`/template-processes/${tProcId[0]}/${tProcId[1]}`)
            .set("Authorization", authorizationTokenAdmin)
            .send({})
            .expect(200)
            .end((err, res) => {
                res.body.result.should.equal(true);
                res.status.should.equal(200);
                done();
            });
    });

    it("TC0650 | Confirm template process deletion", (done) => {
        agent
            .get(`/template-processes/${tProcId[0]}/${tProcId[1]}`)
            .set("Authorization", authorizationTokenAdmin)
            .expect(400)
            .end((err, res) => {
                res.body.error.codeName.should.equal("LACK_OF_PERMISSIONS");
                res.status.should.equal(400);
                done();
            });
    });
});
