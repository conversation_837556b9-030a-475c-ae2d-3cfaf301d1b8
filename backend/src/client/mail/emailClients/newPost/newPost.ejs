<%- include('../../assets/functions') %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="<%= userLang %>>">
<head>
    <meta http-equiv="Content-Type" content="text/html charset=UTF-8" />
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <meta name="x-apple-disable-message-reformatting" content="">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no, address=no, email=no, url=no">
    <title></title>

    <style type="text/css">
        .ExternalClass {width: 100%;}
        table td a:hover {
            text-decoration: none;
        }
    </style>
</head>

<body style="margin: 0">
<div class="es-wrapper-color" style="background-color:#f6f6f6;">
    <%- include('../../assets/paddingTable') %>
    <table cellpadding="0" cellspacing="0" class="es-wrapper" style="mso-table-lspace:0;mso-table-rspace:0;border-collapse:collapse;border-spacing:0;padding-top:0;padding-bottom:0;padding-right:0;padding-left:0;margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;background-color:#f6f6f6;" width="100%">
        <tbody>
        <tr style="border-collapse:collapse;">
            <td align="center" style="padding-top:0;padding-bottom:0;padding-right:0;padding-left:0;margin:0;" valign="top">
                <div style="background-color:#f6f6f6;margin:0;padding:0;outline:none;border:none;">
                    <table cellpadding="0" cellspacing="0" margin="0 auto" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;border-collapse:collapse;border-spacing:0; table-layout:fixed !important;align:center;margin:0">
                        <tbody>
                        <tr style="border-collapse:collapse;">
                            <td align="center" style="padding:0;margin:0">
                                <table align="center" cellpadding="0" cellspacing="0" margin-bottom="0" style="mso-table-lspace:0;mso-table-rspace:0;border-collapse:collapse;border-spacing:0;background-color:white;<%= css.mainWidth %>">
                                    <tbody>
                                    <tr style="border-collapse:collapse;border:none">
                                        <td align="center" style="padding:0;margin:0">
                                            <table cellpadding="0" cellspacing="0" margin-bottom="0" style="mso-table-lspace:0;mso-table-rspace:0;border-collapse:collapse;border-spacing:0; <%= css.innerWidth %>">
                                                <tbody>
                                                <tr style="border-collapse:collapse;">
                                                    <td align="center" valign="top">
                                                        <table bgcolor="transparent" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;border-collapse:collapse;border-spacing:0;background-color:transparent" width="100%">
                                                            <tbody>
                                                            <%- include('../../assets/logoHeaderTr') %>
                                                            <tr style="border-collapse:collapse;">
                                                                <td colspan="2" style="border-collapse:collapse; padding: 8px 0;" align="center">
                                                                    <hr style="border: none; background: #D0D3DB; height: 1px" />
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse;">
                                                                <td align="center" style="margin: 0; padding: 0">
                                                                    <h1 style="<%= css.h1 %>">
                                                                        <%= data[`post_title_${userLang}`] || data.post_title %>
                                                                    </h1>
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse">
                                                                <td style="width: 100%; display: flex; column-gap: 24px; row-gap: 12px; flex-wrap: wrap">
                                                                    <table>
                                                                        <tbody>
                                                                        <tr style="border-collapse:collapse">
                                                                            <td align="left" style="<%= css.tableTdLeft %>">
                                                                                <p style="<%= css.tdP %><%= css.tdPLabel %>">
                                                                                    <%= globalThis.__({ phrase: 'published', locale: userLang }) %>
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style="border-collapse:collapse">
                                                                            <td align="left" style="<%= css.tableTdRight %>">
                                                                                <p style="<%= css.tdP %>">
                                                                                    <%= getDate(data.post_publication_date, userLang, dateFormat) %>
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        </tbody>
                                                                    </table>
                                                                    <% if (data.post_publication_end_date) {%>
                                                                        <table>
                                                                            <tbody>
                                                                                <tr style="border-collapse:collapse">
                                                                                    <td align="left" style="<%= css.tableTdLeft %>">
                                                                                        <p style="<%= css.tdP %><%= css.tdPLabel %>">
                                                                                            <%= globalThis.__({ phrase: 'avaibleUntil', locale: userLang }) %>
                                                                                        </p>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr style="border-collapse:collapse">
                                                                                    <td align="left" style="<%= css.tableTdRight %>">
                                                                                        <p style="<%= css.tdP %>">
                                                                                            <%= getDate(data.post_publication_end_date, userLang, dateFormat) %>
                                                                                        </p>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    <% } %>
                                                                    <% if (data.post_tags && data.post_tags.length > 0) {%>
                                                                        <table>
                                                                            <tbody>
                                                                                <tr style="border-collapse:collapse">
                                                                                    <td align="left" style="<%= css.tableTdLeft %>">
                                                                                        <p style="<%= css.tdP %><%= css.tdPLabel %>">
                                                                                            <%= globalThis.__({ phrase: 'tags', locale: userLang }) %>
                                                                                        </p>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr style="border-collapse:collapse">
                                                                                    <td align="left" style="<%= css.tableTdRight %> display: flex; column-gap: 8px; flex-wrap: wrap;">
                                                                                        <% data.post_tags.forEach(function(tag, i){ %>
                                                                                            <%- include('../../assets/label', { color: 'success', title: tag[`tag_name_${userLang}`] || tag.tag_name }) %>
                                                                                        <% }); %>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                    <% } %>
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse;">
                                                                <td colspan="2" style="border-collapse:collapse; padding: 8px 0;" align="center">
                                                                    <hr style="border: none; background: #D0D3DB; height: 1px" />
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse;">
                                                                <td colspan="2" style="border-collapse:collapse;" align="center">
                                                                    <p style="<%= css.tdP %> text-align: left;" colspan="2">
                                                                        <%- data[`post_content_${userLang}`] || data.post_content %>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse;">
                                                                <td colspan="2" style="border-collapse:collapse; padding: 8px 0;" align="center">
                                                                    <hr style="border: none; background: #D0D3DB; height: 1px" />
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse;">
                                                                <td colspan="2" style="border-collapse:collapse;" align="center">
                                                                    <div style="<%= css.tdP %> padding: 12px 24px; display: flex; align-items: center; justify-content: center; column-gap: 24px; row-gap: 12px; flex-wrap: wrap; border-radius: 12px; background-color: #EFF3F4;">
                                                                        <div style="display: flex; column-gap: 8px; flex-wrap: nowrap; color: #5D6371">
                                                                            <%= /* add mail icon */ %>
                                                                            <span style="font-size: 14px;">
                                                                                <%= globalThis.__({ phrase: 'contacts', locale: userLang }) %>
                                                                            </span>
                                                                        </div>
                                                                        <div style="display: flex; align-items: center; justify-content: center; column-gap: 24px; row-gap: 12px; flex-wrap: wrap; text-decoration: underline; font-size=14px;">
                                                                            <a href="tel:<%= data.post_phone %>" style="color: black;">
                                                                                <%= data.post_phone %>
                                                                            </a>
                                                                            <a href="mailto:<%= data.post_email %>" style="color: black;">
                                                                                <%= data.post_email %>
                                                                            </a>
                                                                            <a href="http://<%= data.post_custom_url %>" style="color: black;">
                                                                                <%= data.post_custom_url %>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr style="border-collapse:collapse;">
                                                                <td colspan="2" style="border-collapse:collapse; text-align: center; padding: 16px 0 8px 0;" align="center">
                                                                    <a rel="noopener" target="_blank" href="<%= globalThis.dynamicConfig.frontendUrl %>/news" style="<%= css.buttonLink %> <%= css.primaryBtn %>">
                                                                        <!--[if mso]>
                                                                        <i style="letter-spacing: 25px; mso-font-width: -100%; mso-text-raise: 20pt;">&nbsp;</i>
                                                                        <![endif]-->
                                                                        <span style="mso-text-raise: 11pt;">
                                                                            <%- globalThis.__({ phrase: 'moreNews', locale: userLang }) %>
                                                                        </span>
                                                                        <!--[if mso]>
                                                                        <i style="letter-spacing: 25px; mso-font-width: -100%;">&nbsp;</i>
                                                                        <![endif]-->
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <%- include('../../assets/footerTable') %>
                </div>
            </td>
        </tr>
        </tbody>
    </table>
    <%- include('../../assets/paddingTable') %>
</div>
</body>
</html>
