import { <PERSON>, <PERSON>tityManager, IDatabaseDriver } from "@mikro-orm/core";
import { uniq, upperFirst } from "lodash";
import { Service } from "typedi";
import { BaseCollection } from "../../api/orm/BaseCollection";
import * as USER_PARAMETERS from "../../api/orm/entity/const/userParameterConsts";
import { Post as OldPost } from "../../api/orm/entity/Post";
import { DatabaseClient } from "../../client/database/databaseClient";
import { getCss } from "../../client/mail/assets/css";
import {
    INewPost,
    NewPostMailClient,
} from "../../client/mail/emailClients/newPost/NewPostMailClient";
import { TasSendMailOptions } from "../../client/mail/types";
import { Post, PostState } from "../../entity/database/post";
import { PostTag } from "../../entity/database/postTag";
import { PostVisibility } from "../../entity/database/postVisibility";
import { Roles } from "../../entity/database/roles";
import { TemplateProcesses } from "../../entity/database/templateProcesses";
import { UserOrganizationStructure } from "../../entity/database/userOrganizationStructure";
import { UserParameters } from "../../entity/database/userParameters";
import { UserRoles } from "../../entity/database/userRoles";
import { Users } from "../../entity/database/users";
import { TPostUpsertControllerSchema } from "../../entity/schema/postUpsertControllerSchema";
import { LogCategory } from "../../utils/logger/logConsts";
import { TasLogger } from "../../utils/logger/TasLogger";
import { AuthorizedUser } from "../authorization/AuthorizedUser";
import { BaseService } from "../baseService";
import { PostTagService } from "./modules/PostTagService";
import { PostVisibilityService } from "./modules/PostVisibilityService";

@Service()
export class PostService extends BaseService {
    public constructor(
        logger: TasLogger,
        dbClient: DatabaseClient,
        public readonly postTagModule: PostTagService,
        public readonly postVisibilityModule: PostVisibilityService,
        private readonly newPostMailClient: NewPostMailClient,
    ) {
        super(logger, dbClient);
    }

    public async getPostsRecords(
        user: AuthorizedUser,
        {
            entity,
            params,
            query,
        }: {
            entity?: string;
            params?: { id?: number };
            query?: { all?: boolean; filter?: string };
        },
    ): Promise<{
        collection: BaseCollection<OldPost>;
        visibilePostMatrix: PostVisibility[];
    }> {
        const userRoles = await this.em.findAll(Roles, {
            where: {
                userRolesCollection: {
                    userId: user.USER_ID,
                },
            },
            fields: ["roleId", "roleName"],
        });

        const userOrgStructureRepository = globalThis.orm.repo(
            "userOrganizationStructure",
        );
        const userOrganizationStructureId = [
            await userOrgStructureRepository.getUserOrgstrId(user.USER_ID),
        ];

        const userAccessibleVisibility =
            await this.postVisibilityModule.getPostVisibilityMatrix({
                roleIds: entity ? [] : userRoles.map((role) => role.roleId),
                orgStrIds: entity ? [] : userOrganizationStructureId,
                userIds: entity ? [] : [user.USER_ID],
                templateIds:
                    entity === "template" && params?.id
                        ? [params.id]
                        : undefined,
                headerIds:
                    entity === "header" && params?.id ? [params.id] : undefined,
                tTaskId:
                    entity === "task" && params?.id ? [params.id] : undefined,
            });

        const { id: postId } = params ?? {};
        const { all, filter } = query ?? {};
        let postIds: number[] | undefined;

        if (all) {
            postIds = undefined;
        } else if (postId && !entity) {
            postIds = [postId];
        } else {
            const ids = uniq(
                userAccessibleVisibility.map((v) => v.postId?.postId),
            );
            postIds =
                ids.length > 0
                    ? ids.filter((id): id is number => id !== undefined)
                    : [0];
        }

        const visibilePostMatrix =
            await this.postVisibilityModule.getPostVisibilityMatrix({
                postIds: postIds,
            });

        const collection = await this.getPosts(postIds, { filter });

        return { collection, visibilePostMatrix };
    }

    public async getPosts(
        postIds?: number[] | Set<number>,
        options?: {
            filter?: string;
            columns?: string[];
        },
    ): Promise<BaseCollection<OldPost>> {
        const translationColumns =
            globalThis.dynamicConfig.langs?.flatMap((lang: string) => [
                `POST_TITLE_${lang.toUpperCase()}`,
                `POST_CONTENT_${lang.toUpperCase()}`,
            ]) ?? [];

        const baseColumns = [
            "POST.POST_ID",
            "POST.POST_TITLE",
            "POST.POST_CONTENT",
            "POST.CREATED_AT",
            "POST.UPDATED_AT",
            "POST.POST_PHONE",
            "POST.POST_EMAIL",
            "POST.POST_CUSTOM_URL",
            "POST.POST_STATE",
            "POST.POST_PUBLICATION_DATE",
            "POST.POST_PUBLICATION_END_DATE",
            "POST.POST_SEND_EMAIL",
            "POST.POST_PRIORITY",
            "POST.POST_CREATED_BY",
            "POST.POST_UPDATED_BY",
            "USERS.USER_DISPLAY_NAME",
            ...translationColumns,
        ];

        let connection = globalThis.database
            .select([...baseColumns])
            .from("POST")
            .innerJoin("USERS", "USERS.USER_ID", "POST.POST_CREATED_BY")
            .modify((qb) => {
                const idsArray =
                    postIds instanceof Set ? Array.from(postIds) : postIds;

                if (Array.isArray(idsArray)) {
                    qb.whereIn("POST.POST_ID", idsArray);
                }
            })
            .groupBy(baseColumns);

        if (options?.filter?.includes("tag_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(POST_TAG.TAG_NAME, ',') AS TAGS",
                    ),
                    ...(globalThis.dynamicConfig.langs?.flatMap(
                        (lang: string) => [
                            globalThis.database.raw(
                                `STRING_AGG(POST_TAG.TAG_NAME_${lang.toUpperCase()}, ',') AS TAGS_${lang.toUpperCase()}`,
                            ),
                        ],
                    ) ?? []),
                ])
                .leftJoin("POSTS_TAGS", "POSTS_TAGS.POST_ID", "POST.POST_ID")
                .leftJoin("POST_TAG", "POST_TAG.TAG_ID", "POSTS_TAGS.TAG_ID");
        }

        if (options?.filter?.includes("role_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(ROLES.ROLE_NAME, ',') AS ROLES",
                    ),
                ])
                .leftJoin(
                    "POST_VISIBILITY",
                    "POST_VISIBILITY.POST_ID",
                    "POST.POST_ID",
                )
                .leftJoin("ROLES", "ROLES.ROLE_ID", "POST_VISIBILITY.ROLE_ID");
        }

        if (options?.filter?.includes("orgstr_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(ORGANIZATION_STRUCTURE.ORGSTR_NAME, ',') AS ORGSTRS",
                    ),
                ])
                .leftJoin(
                    "POST_VISIBILITY",
                    "POST_VISIBILITY.POST_ID",
                    "POST.POST_ID",
                )
                .leftJoin(
                    "ORGANIZATION_STRUCTURE",
                    "ORGANIZATION_STRUCTURE.ORGSTR_ID",
                    "POST_VISIBILITY.ORGSTR_ID",
                );
        }

        if (options?.filter?.includes("user_full_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(USERS.USER_FULL_NAME, ',') AS USERS",
                    ),
                ])
                .leftJoin(
                    "POST_VISIBILITY",
                    "POST_VISIBILITY.POST_ID",
                    "POST.POST_ID",
                )
                .leftJoin("USERS", "USERS.USER_ID", "POST_VISIBILITY.USER_ID");
        }

        if (options?.filter?.includes("template_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(TEMPLATE_PROCESSES.TPROC_NAME, ',') AS TEMPLATES",
                    ),
                    ...(globalThis.dynamicConfig.langs?.flatMap(
                        (lang: string) => [
                            globalThis.database.raw(
                                `STRING_AGG(TEMPLATE_PROCESSES.TPROC_NAME_${lang.toUpperCase()}, ',') AS TEMPLATES_${lang.toUpperCase()}`,
                            ),
                        ],
                    ) ?? []),
                ])
                .leftJoin(
                    "POST_VISIBILITY",
                    "POST_VISIBILITY.POST_ID",
                    "POST.POST_ID",
                )
                .leftJoin(
                    "TEMPLATE_PROCESSES",
                    "TEMPLATE_PROCESSES.TPROC_ID",
                    "POST_VISIBILITY.TPROC_ID",
                );
        }

        if (options?.filter?.includes("header_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(HEADERS.HEADER_NAME, ',') AS HEADERS",
                    ),

                    ...(globalThis.dynamicConfig.langs?.flatMap(
                        (lang: string) => [
                            globalThis.database.raw(
                                `STRING_AGG(HEADERS.HEADER_NAME_${lang.toUpperCase()}, ',') AS HEADERS_${lang.toUpperCase()}`,
                            ),
                        ],
                    ) ?? []),
                ])
                .leftJoin(
                    "POST_VISIBILITY",
                    "POST_VISIBILITY.POST_ID",
                    "POST.POST_ID",
                )
                .leftJoin(
                    "HEADERS",
                    "HEADERS.HEADER_ID",
                    "POST_VISIBILITY.HEADER_ID",
                );
        }

        if (options?.filter?.includes("task_name")) {
            connection = connection
                .select([
                    globalThis.database.raw(
                        "STRING_AGG(TEMPLATE_TASKS.TTASK_NAME, ',') AS TASKS",
                    ),
                    ...(globalThis.dynamicConfig.langs?.flatMap(
                        (lang: string) => [
                            globalThis.database.raw(
                                `STRING_AGG(TEMPLATE_TASKS.TTASK_NAME_${lang.toUpperCase()}, ',') AS TASKS_${lang.toUpperCase()}`,
                            ),
                        ],
                    ) ?? []),
                ])
                .leftJoin(
                    "POST_VISIBILITY",
                    "POST_VISIBILITY.POST_ID",
                    "POST.POST_ID",
                )
                .leftJoin(
                    "TEMPLATE_TASKS",
                    "TEMPLATE_TASKS.TTASK_ID",
                    "POST_VISIBILITY.TTASK_ID",
                );
        }

        if (options?.columns) {
            connection = connection
                .select(options.columns)
                .groupBy(options.columns);
        }

        return globalThis.orm.collection("post", connection);
    }

    public async deletePost(post: Post): Promise<void> {
        await this.em.transactional(async (tem) => {
            await tem.nativeDelete(PostVisibility, { postId: post.postId });
            await tem.removeAndFlush(post);
        });
    }

    async upsertPost(
        postData: TPostUpsertControllerSchema["Body"],
        user?: AuthorizedUser,
    ): Promise<number> {
        const { post_id: postId } = postData;
        let post: OldPost;

        if (postId) {
            const postExistsCollection = await this.getPosts([postId]);
            const postExists = (await postExistsCollection.fetchAll()).shift();

            if (!postExists) {
                throw new Error("Post does not exist.");
            }
        }

        let postPublicationDate: string | null,
            postPublicationEndDate: string | null;

        if (postId) {
            const postCollection = await this.getPosts([postId]);
            post = (await postCollection.fetchAll()).shift();
            const postStateChanged = post.POST_STATE !== postData.post_state;

            if (postStateChanged && postData.post_state === PostState.ACTIVE) {
                postPublicationDate = new Date().toISOString();
            } else {
                postPublicationDate =
                    postData.post_publication_date ??
                    post.POST_PUBLICATION_DATE;
            }
        } else {
            postPublicationDate =
                postData.post_state === PostState.ACTIVE
                    ? new Date().toISOString()
                    : (postData.post_publication_date ?? null);
        }

        postPublicationEndDate =
            postPublicationDate &&
            postData.post_publication_end_date &&
            new Date(postData.post_publication_end_date) > new Date() &&
            new Date(postData.post_publication_end_date) >
                new Date(postPublicationDate)
                ? postData.post_publication_end_date
                : null;

        return this.em.transactional(async (tem) => {
            return this.processPostUpsert(
                tem,
                postData,
                user,
                postPublicationDate,
                postPublicationEndDate,
                post,
            );
        });
    }

    private async processPostUpsert(
        tem: EntityManager<IDatabaseDriver<Connection>>,
        postData: TPostUpsertControllerSchema["Body"],
        user?: AuthorizedUser,
        postPublicationDate?: string | null,
        postPublicationEndDate?: string | null,
        existingPost?: OldPost,
    ): Promise<number> {
        const isUpdate = !!postData.post_id;
        const now = new Date();

        const basePostData = this.createBasePostData(
            postData,
            existingPost,
            postPublicationDate,
            postPublicationEndDate,
            user,
            tem,
            now,
        );

        const post = await this.createOrUpdatePost(
            tem,
            isUpdate,
            postData,
            basePostData,
            user,
            now,
        );

        this.setPostTags(tem, post, postData);

        await tem.persistAndFlush(post);

        const postId = post.postId;

        await this.handlePostVisibility(tem, postData, postId, now);

        if (
            postData.post_send_email &&
            postData.post_state === PostState.ACTIVE
        ) {
            await this.logger.runTask(async () => {
                this.logger.setContextProperty(
                    "category",
                    LogCategory.CATEGORY_MAIL,
                );
                await this.sendMailToUsers(postId);
            });
        }

        return postId;
    }

    private createBasePostData(
        postData: TPostUpsertControllerSchema["Body"],
        existingPost: OldPost | undefined,
        postPublicationDate: string | null | undefined,
        postPublicationEndDate: string | null | undefined,
        user: AuthorizedUser | undefined,
        tem: any,
        now: Date,
    ): any {
        const baseData = {
            postState: postData.post_state ?? existingPost?.POST_STATE ?? null,
            postPublicationDate: postPublicationDate
                ? new Date(postPublicationDate)
                : null,
            postPublicationEndDate: postPublicationEndDate
                ? new Date(postPublicationEndDate)
                : null,
            updatedAt: now,
        };

        if (!!existingPost && Object.keys(postData).length === 3) {
            return baseData;
        }

        return {
            ...baseData,
            postTitle: postData.post_title ?? existingPost?.POST_TITLE ?? null,
            postContent:
                postData.post_content ?? existingPost?.POST_CONTENT ?? null,
            postPhone: postData.post_phone ?? existingPost?.POST_PHONE ?? null,
            postEmail: postData.post_email ?? existingPost?.POST_EMAIL ?? null,
            postCustomUrl:
                postData.post_custom_url ??
                existingPost?.POST_CUSTOM_URL ??
                null,
            postSendEmail:
                postData.post_send_email !== undefined
                    ? !!postData.post_send_email
                    : (existingPost?.POST_SEND_EMAIL ?? false),
            postPriority:
                postData.post_priority !== undefined &&
                postData.post_priority !== null
                    ? postData.post_priority
                    : (existingPost?.POST_PRIORITY ?? 1),
            postUpdatedBy: user
                ? tem.getReference(Users, user.USER_ID, { wrapped: true })
                : null,
            ...this.getLocalizedFields(postData, existingPost),
        };
    }

    private async createOrUpdatePost(
        tem: any,
        isUpdate: boolean,
        postData: TPostUpsertControllerSchema["Body"],
        basePostData: any,
        user: AuthorizedUser | undefined,
        now: Date,
    ): Promise<Post> {
        let post: Post;

        if (isUpdate) {
            post = await tem.findOneOrFail(Post, {
                postId: postData.post_id,
            });
            const cleanedBasePostData = Object.fromEntries(
                Object.entries(basePostData).filter(
                    ([_, v]) => v !== undefined,
                ),
            );
            tem.assign(post, cleanedBasePostData);
        } else {
            post = tem.create(Post, {
                ...basePostData,
                postCreatedBy: user
                    ? tem.getReference(Users, user.USER_ID, {
                          wrapped: true,
                      })
                    : null,
                createdAt: now,
            });
        }

        return post;
    }

    private setPostTags(
        tem: any,
        post: Post,
        postData: TPostUpsertControllerSchema["Body"],
    ): void {
        post.postsTags.set(
            postData.post_tags?.map((tagId) =>
                tem.getReference(PostTag, tagId),
            ) ?? [],
        );
    }

    private async handlePostVisibility(
        tem: any,
        postData: TPostUpsertControllerSchema["Body"],
        postId: number,
        now: Date,
    ): Promise<void> {
        if (!postData.post_visibility) {
            return;
        }

        const visibility: PostVisibility[] = [];
        const postVisibilityEntries = Object.entries(
            postData.post_visibility ?? {},
        );

        for (const [key, values] of postVisibilityEntries) {
            if (!Array.isArray(values)) {
                continue;
            }

            for (const value of values) {
                visibility.push(
                    this.createVisibilityEntry(tem, key, value, postId, now),
                );
            }
        }

        await this.postVisibilityModule.storeVisibility(visibility, postId);
    }

    private createVisibilityEntry(
        tem: any,
        key: string,
        value: any,
        postId: number,
        now: Date,
    ): PostVisibility {
        return tem.create(PostVisibility, {
            postId,
            createdAt: now,
            updatedAt: now,
            ...(key === "templates"
                ? {
                      tprocId: tem.getReference(TemplateProcesses, [value, 1]),
                  }
                : {
                      roleId: key === "roles" ? value : undefined,
                      userId: key === "users" ? value : undefined,
                      orgstrId:
                          key === "organization_structures" ? value : undefined,
                      headerId: key === "headers" ? value : undefined,
                      tTaskId: key === "tasks" ? value : undefined,
                  }),
        });
    }

    async sendMailToUsers(postId: number): Promise<void> {
        const post = await this.em.findOneOrFail(Post, {
            postId: postId,
        });

        post.postsTags.set(
            await this.em.findAll(PostTag, {
                where: {
                    tagId: { $in: post.postsTags.map((tag) => tag.tagId) },
                },
            }),
        );

        const postVisibilityRecords = await this.em.findAll(PostVisibility, {
            where: { postId: postId },
            populate: ["userId", "roleId", "orgstrId"],
        });

        const postVisibilityMatrix = postVisibilityRecords.reduce(
            (
                acc: {
                    roles: number[];
                    users: number[];
                    organization_structures: number[];
                },
                postVisibility,
            ) => {
                if (postVisibility.roleId?.roleId) {
                    acc.roles.push(postVisibility.roleId.roleId);
                }
                if (postVisibility.userId?.userId) {
                    acc.users.push(postVisibility.userId.userId);
                }
                if (postVisibility.orgstrId?.orgstrId) {
                    acc.organization_structures.push(
                        postVisibility.orgstrId.orgstrId,
                    );
                }
                return acc;
            },
            {
                roles: [],
                users: [],
                organization_structures: [],
            },
        );

        const usersFromRoles = await this.em.findAll(UserRoles, {
            where: {
                roleId: { $in: postVisibilityMatrix.roles },
            },
            fields: ["userId"],
        });

        const usersFromOrgstr = await this.em.findAll(
            UserOrganizationStructure,
            {
                where: {
                    orgstrId: {
                        $in: postVisibilityMatrix.organization_structures,
                    },
                },
                fields: ["userId"],
            },
        );

        const userIds = new Set([
            ...usersFromRoles.map((user) => {
                return user.userId;
            }),
            ...usersFromOrgstr.map((user) => {
                return user.userId;
            }),
            ...postVisibilityMatrix.users,
        ]);

        const users = await this.em.findAll(Users, {
            where: {
                userId: { $in: Array.from(userIds) },
            },
            fields: ["userId", "userEmail", "userDisplayName"],
        });

        for (const user of users) {
            if (!user?.userEmail) {
                this.logger.error("No email address found for user", {
                    user,
                });
                continue;
            }

            const targetLanguageParameter = await this.em.find(
                UserParameters,
                {
                    userId: user.userId,
                    usrparName: USER_PARAMETERS.LANGUAGE_CLIENT,
                },
                { fields: ["usrparValue"] },
            );
            const targetLanguage: string = targetLanguageParameter.length
                ? targetLanguageParameter[0].usrparValue
                : globalThis.dynamicConfig.defaultLang;

            const targetDateFormatParameter = await globalThis.orm
                .repo("userParameter")
                .getUserParameter(user.userId, "DATE_FORMAT");
            const targetDateFormat = targetDateFormatParameter.length
                ? targetDateFormatParameter[0].USRPAR_VALUE
                : "L";

            const emailData: INewPost = {
                data: {
                    post_title: post.postTitle ?? null,
                    post_content: post.postContent ?? null,
                    post_phone: post.postPhone ?? null,
                    post_email: post.postEmail ?? null,
                    post_custom_url: post.postCustomUrl ?? null,
                    post_publication_date:
                        post.postPublicationDate?.toISOString() ?? null,
                    post_publication_end_date:
                        post.postPublicationEndDate?.toISOString() ?? null,
                    post_tags: post.postsTags.map((tag) => ({
                        tag_name: tag.tagName ?? "",
                        [`tag_name_${targetLanguage}`]:
                            tag[
                                `tagName${upperFirst(
                                    targetLanguage,
                                )}` as keyof PostTag
                            ] || undefined,
                    })),
                },
                userLang: targetLanguage,
                dateFormat: targetDateFormat,
                css: await getCss(globalThis.dynamicConfig.mail),
            };

            const mailOptions: TasSendMailOptions = {
                from: globalThis.dynamicConfig.mail.from,
                to: user?.userEmail ?? "<EMAIL>",
                replyTo: globalThis.dynamicConfig.mail.noReplyMail,
                subject: `${globalThis.__(
                    {
                        phrase: "newPost",
                        locale: targetLanguage,
                    },
                    {
                        postTitle:
                            post[
                                `postTitle${upperFirst(
                                    targetLanguage,
                                )}` as keyof Post
                            ] || post.postTitle,
                    },
                )}`,
            };

            emailData.data[`post_title_${targetLanguage}`] =
                post[`postTitle${upperFirst(targetLanguage)}` as keyof Post];

            emailData.data[`post_content_${targetLanguage}`] =
                post[`postContent${upperFirst(targetLanguage)}` as keyof Post];

            await this.newPostMailClient.sendEmail(emailData, mailOptions);
        }
    }

    private getLocalizedFields(
        postData: Record<string, any>,
        existingPost?: OldPost,
    ): Record<string, string> {
        const localized: Record<string, string> = {};

        for (const [key, value] of Object.entries(postData)) {
            if (
                typeof value === "string" &&
                globalThis.dynamicConfig.langs.includes(key.slice(-2))
            ) {
                if (key.startsWith("post_title_")) {
                    const lang = key.slice(-2);
                    const suffix = lang[0].toUpperCase() + lang[1];
                    localized[`postTitle${suffix}`] = value;
                }
                if (key.startsWith("post_content_")) {
                    const lang = key.slice(-2);
                    const suffix = lang[0].toUpperCase() + lang[1];
                    localized[`postContent${suffix}`] = value;
                }
            }
        }

        if (existingPost) {
            for (const lang of globalThis.dynamicConfig.langs) {
                const suffix = lang[0].toUpperCase() + lang[1];
                const titleKey = `postTitle${suffix}`;
                const contentKey = `postContent${suffix}`;
                const existingTitleKey = `POST_TITLE_${lang.toUpperCase()}`;
                const existingContentKey = `POST_CONTENT_${lang.toUpperCase()}`;

                if (!localized[titleKey] && existingPost[existingTitleKey]) {
                    localized[titleKey] = existingPost[existingTitleKey];
                }
                if (
                    !localized[contentKey] &&
                    existingPost[existingContentKey]
                ) {
                    localized[contentKey] = existingPost[existingContentKey];
                }
            }
        }

        return localized;
    }
}
