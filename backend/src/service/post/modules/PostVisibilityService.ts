import { Service } from "typedi";
import { Post as OldPost } from "../../../api/orm/entity/Post";
import { PostsTags } from "../../../api/orm/entity/PostsTags";
import { Headers } from "../../../entity/database/headers";
import { PostVisibility } from "../../../entity/database/postVisibility";
import { TemplateProcesses } from "../../../entity/database/templateProcesses";
import { TemplateTasks } from "../../../entity/database/templateTasks";
import { BaseService } from "../../baseService";

@Service()
export class PostVisibilityService extends BaseService {
    public async storeVisibility(
        visibility: PostVisibility[],
        postId: number,
    ): Promise<void> {
        await this.em.nativeDelete(PostVisibility, { postId: postId });

        for (const vis of visibility) {
            this.em.persist(vis);
        }

        await this.em.flush();
    }

    public async deleteVisibility(postId: number): Promise<void> {
        await this.em.nativeDelete(PostVisibility, { postId: postId });
        await this.em.flush();
    }

    public async getPostVisibilityMatrix(args: {
        postIds?: number[];
        roleIds?: number[];
        orgStrIds?: number[];
        userIds?: number[];
        templateIds?: number[];
        headerIds?: number[];
        tTaskId?: number[];
    }): Promise<PostVisibility[]> {
        const translationFields =
            globalThis.dynamicConfig.langs?.flatMap((lang: string) => {
                const firstLetterUpper = lang.charAt(0).toUpperCase();
                const parsedLang = `${firstLetterUpper}${lang.slice(1)}`;
                return [
                    `tprocId.tprocName${parsedLang}`,
                    `headerId.headerName${parsedLang}`,
                    `tTaskId.ttaskName${parsedLang}`,
                ];
            }) || [];
        const filterConditions: Record<string, { $in: number[] }>[] = [];

        if (args.postIds?.length) {
            filterConditions.push({ postId: { $in: args.postIds } });
        }
        if (args.roleIds?.length) {
            filterConditions.push({ roleId: { $in: args.roleIds } });
        }
        if (args.orgStrIds?.length) {
            filterConditions.push({ orgstrId: { $in: args.orgStrIds } });
        }
        if (args.userIds?.length) {
            filterConditions.push({ userId: { $in: args.userIds } });
        }
        if (args.templateIds?.length) {
            filterConditions.push({ tprocId: { $in: args.templateIds } });
        }
        if (args.headerIds?.length) {
            filterConditions.push({ headerId: { $in: args.headerIds } });
        }
        if (args.tTaskId?.length) {
            filterConditions.push({ tTaskId: { $in: args.tTaskId } });
        }

        const where =
            filterConditions.length > 1
                ? { $or: filterConditions }
                : filterConditions[0] || {};

        const visibilityRecords = await this.em.findAll(PostVisibility, {
            where,
            populate: [
                "roleId",
                "roleId.roleName",
                "orgstrId",
                "orgstrId.orgstrName",
                "userId",
                "userId.userDisplayName",
                "tprocId",
                "tprocId.tprocName",
                "headerId",
                "headerId.headerName",
                "tTaskId",
                "tTaskId.ttaskName",
                ...translationFields,
            ],
        });

        return visibilityRecords;
    }

    public pairVisibilityToPosts(
        posts: OldPost[],
        postVisibility: PostVisibility[],
        tags: PostsTags[],
    ): OldPost[] {
        const postsWithVisibility = [...posts];

        for (const post of postsWithVisibility) {
            const filteredVisibility = postVisibility.filter(
                (v) => v.postId?.postId === post.POST_ID,
            );

            post.POST_VISIBILITY = {
                roles: filteredVisibility
                    .filter((v) => v.roleId?.roleId !== undefined)
                    .map((v) => ({
                        role_id: v.roleId?.roleId,
                        role_name: v.roleId?.unwrap()?.roleName,
                    })),
                users: filteredVisibility
                    .filter((v) => v.userId?.userId !== undefined)
                    .map((v) => ({
                        user_id: v.userId?.userId,
                        user_display_name: v.userId?.unwrap()?.userDisplayName,
                    })),
                organization_structures: filteredVisibility
                    .filter((v) => v.orgstrId?.orgstrId !== undefined)
                    .map((v) => ({
                        orgstr_id: v.orgstrId?.orgstrId,
                        orgstr_name: v.orgstrId?.unwrap()?.orgstrName,
                    })),
                templates: filteredVisibility
                    .filter((v) => v.tprocId?.tprocId !== undefined)
                    .map((v) => ({
                        tproc_id: v.tprocId?.tprocId,
                        tproc_version: v.tprocId?.tprocVersion,
                        tproc_name: v.tprocId?.unwrap()?.tprocName,
                        ...globalThis.dynamicConfig.langs?.reduce(
                            (acc: Record<string, string>, lang: string) => ({
                                ...acc,
                                [`TPROC_NAME_${lang.toUpperCase()}`]:
                                    v.tprocId?.unwrap()?.[
                                        `tprocName${
                                            lang[0].toUpperCase() +
                                            lang.slice(1)
                                        }` as keyof TemplateProcesses
                                    ],
                            }),
                            {},
                        ),
                    })),
                headers: filteredVisibility
                    .filter((v) => v.headerId?.headerId !== undefined)
                    .map((v) => ({
                        header_id: v.headerId?.headerId,
                        header_name: v.headerId?.unwrap()?.headerName,
                        ...globalThis.dynamicConfig.langs?.reduce(
                            (acc: Record<string, string>, lang: string) => ({
                                ...acc,
                                [`HEADER_NAME_${lang.toUpperCase()}`]:
                                    v.headerId?.unwrap()?.[
                                        `headerName${
                                            lang[0].toUpperCase() +
                                            lang.slice(1)
                                        }` as keyof Headers
                                    ],
                            }),
                            {},
                        ),
                    })),
                tasks: filteredVisibility
                    .filter((v) => v.tTaskId?.ttaskId !== undefined)
                    .map((v) => ({
                        ttask_id: v.tTaskId?.ttaskId,
                        ttask_name: v.tTaskId?.unwrap()?.ttaskName,
                        ...globalThis.dynamicConfig.langs?.reduce(
                            (acc: Record<string, string>, lang: string) => ({
                                ...acc,
                                [`TTASK_NAME_${lang.toUpperCase()}`]:
                                    v.tTaskId?.unwrap()?.[
                                        `ttaskName${
                                            lang[0].toUpperCase() +
                                            lang.slice(1)
                                        }` as keyof TemplateTasks
                                    ],
                            }),
                            {},
                        ),
                    })),
            };

            post.POST_TAGS = tags.filter((tag) => tag.POST_ID === post.POST_ID);
        }

        return postsWithVisibility;
    }
}
