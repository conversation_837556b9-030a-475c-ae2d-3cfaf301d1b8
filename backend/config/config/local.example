var path = require('path');

module.exports = {
    port: process.env.PORT || {{=it.backPort}}, //
    connections: {
        knexConnection: {
            host: '{{=it.dbServer}}',
            user: '{{=it.dbUser}}',
            password: '{{=it.dbPass}}',
            database: '{{=it.dbSid}}'
        }
    },
    db: {
        client: 'postgresql', // you can choose between 'postgresql' and 'mssql'
    },
    frontendUrl: '{{=it.frontUrl}}', // frontend url to enable XHR and disable browsers CORS, in same domain environment is the same as backend url
    langs: ['cs', 'en', 'sk'], // the first is main language
    dms: {
        fulltext: null, // name of index for multiinstance enviroment (ex. 'tas'), set null to turnoff indexing
        storagePath: '{{=it.storageDir}}' // path where DMS files will be stored
    },
    crons: {
        runOnStart: true, // When the main process is started, the Cron process will start too
    },
    logger: {
        arango: {
            host: 'http://127.0.0.1:8529', // Arango host:port (http://127.0.0.1:8529). Use null to disable arango logger. Fallback file will be used.
            db_name: '{{=it.idName}}', // Database name, use something like '{instance_id}_db'.
            auth: { // Use 'auth: null' to turn off. Use (login/pass) or jwt token authentication.
                user: 'root',
                pass: '',
                // https://www.arangodb.com/docs/stable/http/general.html#user-jwt-token
                jwt: null,
            },
        },
    },
};
