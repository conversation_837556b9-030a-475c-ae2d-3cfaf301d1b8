{"name": "tasbackend", "private": true, "version": "5.6.3", "description": "TAS Backend", "scripts": {"start": "node ./dist/app.js", "start:cron": "node ./dist/appCron.js", "prebuild": "cd src && rsync -a --include='*/' --exclude='*.ts' '.' '../dist' && cd ..", "prebuild:win": "cd src & robocopy . ..\\dist /E /XF *.ts & cd ..", "build": "tsc -b", "build:win": "tsc -b", "postbuild": "ts-node src/generateDocumentation.ts", "typecheck": "tsc --project tsconfig.json --noEmit", "dev": "nodemon --watch './**/*.ts' --exec 'NODE_ENV=DEVELOP ts-node' ./src/app.ts", "dev:win": "set NODE_ENV=DEVELOP&& nodemon --watch ./**/*.ts --exec ts-node ./src/app.ts", "dev:cron": "nodemon --watch './**/*.ts' --exec 'NODE_ENV=DEVELOP ts-node' ./src/appCron.ts", "dev:cron:win": "set NODE_ENV=DEVELOP&& nodemon --watch ./**/*.ts --exec ts-node ./src/appCron.ts", "mig": "node dist/migrate-cli.js", "mig:ts": "ts-node src/migrate-cli.ts", "test:unit": "mocha dist/test/unit/**/*.js --exit", "test:unit:ts": "mocha --require ts-node/register src/test/unit/**/*.ts --exit", "coverage:unit": "coverage --reporter=json --report-dir=/app/tas/coverage/unit mocha dist/test/unit/**/*.js --exit ", "test:integration": "mocha --require dist/test/bootstrap.test.js test/integration/**/*.js --exit ", "test:integration:ts": "mocha --require ts-node/register --require src/test/bootstrap.test.ts test/integration/**/*.ts --exit ", "coverage:integration": "coverage --reporter=json --report-dir=/app/tas/coverage/integration mocha --require dist/test/bootstrap.test.js test/integration/**/*.js --exit ", "test:libreoffice": "mocha --require dist/test/bootstrap.test.js test/libreoffice/*.js --timeout 120000 --exit", "test:rest": "mocha --require dist/test/bootstrap.test.js  test/rest/**/*.js --exit ", "test:rest:ts": "mocha --require ts-node/register --require src/test/bootstrap.test.ts  test/rest/**/*.ts --exit ", "coverage:rest": "coverage --reporter=json --report-dir=/app/tas/coverage/rest mocha --require dist/test/bootstrap.test.js test/rest/**/*.js --exit", "coverage:merge": "npm run coverage:unit && npm run coverage:rest && npm run coverage:integration && nyc merge coverage coverage.json && nyc report --reporter=html --report-dir=coverage/final ", "obfuscate": "node dist/create-obfuscated.js", "precommit": "biome check --changed", "lint": "npx @biomejs/biome@1.9.4 check --diagnostic-level=error --config-path=./ ./src", "lint:all": "npx @biomejs/biome@1.9.4 check --diagnostic-level=info --config-path=./ ./src", "lint:fix": "npx @biomejs/biome@1.9.4 check --write --diagnostic-level=error --config-path=./ ./src", "prepare": "cd .. && husky"}, "lint-staged": {"*": ["biome check --write --config-path=../ --organize-imports-enabled=false --no-errors-on-unmatched"]}, "main": "dist/app.js", "repository": {"type": "git", "url": "git://github.com/anonymous node/sails user/tasbackend.git"}, "dependencies": {"@azure/identity": "4.5.0", "@babel/core": "7.26.0", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.25.9", "@debugr/console": "3.0.0-rc.10", "@debugr/core": "3.0.0-rc.13", "@debugr/elastic": "3.0.0-rc.10", "@egodigital/node-ews": "4.1.1", "@elastic/elasticsearch": "8.16.2", "@fastify/cookie": "11.0.1", "@fastify/cors": "10.0.1", "@fastify/express": "4.0.1", "@fastify/formbody": "8.0.1", "@fastify/helmet": "13.0.0", "@fastify/multipart": "9.0.1", "@fastify/swagger": "9.4.0", "@google-cloud/precise-date": "4.0.0", "@json2csv/node": "7.0.6", "@microsoft/microsoft-graph-client": "3.0.7", "@node-saml/passport-saml": "5.0.1", "adm-zip": "0.5.16", "arangojs": "9.2.0", "async": "3.2.6", "axios": "1.7.9", "bcrypt": "5.1.1", "blocked": "1.3.0", "bullmq": "5.31.1", "bwip-js": "4.5.1", "chalk": "4.1.0", "client-oauth2": "4.3.3", "cron": "3.2.1", "csv-parse": "5.6.0", "csvtojson": "2.0.10", "docx-templates": "4.13.0", "docxtemplater": "3.55.0", "dotenv": "16.4.7", "ejs": "3.1.10", "escodegen": "2.1.0", "esprima-next": "6.0.3", "estraverse": "5.3.0", "exceljs": "4.4.0", "exec-sh": "0.4.0", "fastify": "5.1.0", "fastify-xml-body-parser": "2.2.0", "file-system-cache": "2.1.1", "firebase-admin": "13.0.1", "flatted": "3.3.2", "get-parameter-names": "0.3.0", "graphql": "16.9.0", "graphql-request": "6.0.0", "helmet": "8.0.0", "htmlparser2": "9.1.0", "htmlspecialchars": "1.0.5", "httpntlm": "1.8.13", "i18n": "0.15.1", "iconv-lite": "0.6.3", "immutable-ics": "0.4.0", "ini": "5.0.0", "ioredis": "5.4.1", "isdoc-pdf": "0.1.2", "jimp": "1.6.0", "js-beautify": "1.15.1", "jsdoc-to-markdown": "9.1.1", "json-schema-to-ts": "3.1.1", "json2csv": "5.0.7", "jsonwebtoken": "9.0.2", "jszip": "3.10.1", "knex": "git+https://github.com/teamassistant/knex#7f383305d521923f21fbbe46e664d34c8b8248e8", "ldapts": "7.2.2", "lodash": "4.17.21", "mailparser": "3.7.2", "moment": "2.30.1", "moment-business-time": "2.0.0", "moment-timezone": "0.5.46", "neon-js": "1.1.2", "node-cache": "5.1.2", "node-html-parser": "6.1.13", "node-libcurl": "4.0.0", "node-sp-auth": "3.0.9", "nodemailer": "6.9.16", "oracledb": "6.7.0", "passport": "0.7.0", "passport-azure-ad": "4.3.5", "passport-saml-metadata": "4.0.0", "password-validator": "5.3.0", "pdf-lib": "1.17.1", "pdf2json": "3.1.4", "performance-now": "2.1.0", "pino": "9.5.0", "pino-pretty": "13.0.0", "pizzip": "3.1.7", "puppeteer": "23.10.0", "query-string": "7.1.1", "quoted-printable": "1.0.1", "rate-limiter-flexible": "5.0.4", "redlock": "5.0.0-beta.2", "reflect-metadata": "0.2.2", "sanitize-filename": "1.6.3", "sanitize-html": "2.13.1", "sharp": "0.33.5", "skipper-disk": "0.5.12", "string-replace-async": "2.0.0", "string-to-stream": "3.0.1", "strong-soap": "4.1.6", "strtotime": "1.0.0", "systeminformation": "5.23.25", "table": "6.9.0", "tedious": "13.2.0", "tmp": "0.2.3", "truncate-utf8-bytes": "1.0.2", "typedi": "0.10.0", "unzipper": "0.12.3", "uuid": "11.0.3", "vm2": "3.9.19", "xlsx": "0.18.5", "xml2js": "0.6.2", "xregexp": "5.1.1", "xregexp-lookbehind": "1.0.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/adm-zip": "0.5.7", "@types/assert": "1.5.11", "@types/async": "3.2.24", "@types/async-lock": "1.4.2", "@types/babel__core": "7.20.5", "@types/babel__preset-env": "7.9.7", "@types/bcrypt": "10.0.28-alpha", "@types/blocked": "1.3.4", "@types/bwip-js": "3.2.3", "@types/chai": "5.0.1", "@types/debug": "4.1.12", "@types/ejs": "3.1.5", "@types/escodegen": "0.0.10", "@types/estraverse": "5.1.7", "@types/http-proxy": "1.17.15", "@types/i18n": "0.13.12", "@types/ini": "4.1.1", "@types/js-beautify": "1.14.3", "@types/jsdoc-to-markdown": "7.0.6", "@types/jsonwebtoken": "9.0.7", "@types/lint-staged": "13.3.0", "@types/lodash": "4.17.13", "@types/mailparser": "3.4.5", "@types/mocha": "10.0.10", "@types/mochawesome": "6.2.4", "@types/moment-business-time": "1.1.4", "@types/node": "22.10.1", "@types/nodemailer": "6.4.17", "@types/oracledb": "6.5.2", "@types/passport": "1.0.17", "@types/passport-azure-ad": "4.3.6", "@types/quoted-printable": "1.0.2", "@types/sanitize-html": "2.13.0", "@types/shell-quote": "1.7.5", "@types/sinon": "17.0.3", "@types/sqlstring": "2.3.2", "@types/supertest": "6.0.2", "@types/tedious": "4.0.14", "@types/tmp": "0.2.6", "@types/truncate-utf8-bytes": "1.0.2", "@types/unzipper": "0.10.10", "@types/uuid": "10.0.0", "@types/walk": "2.3.4", "@types/xml2js": "0.4.14", "assert": "2.1.0", "chai": "4.3.4", "coverage": "0.4.1", "husky": "9.1.7", "javascript-obfuscator": "4.1.1", "lint-staged": "15.2.10", "mocha": "11.0.1", "mochawesome": "7.1.3", "nodemon": "3.1.7", "nyc": "17.1.0", "should": "13.2.3", "sinon": "19.0.2", "supertest": "7.0.0", "ts-node": "10.9.2", "typescript": "5.7.2", "walk": "2.3.15"}}