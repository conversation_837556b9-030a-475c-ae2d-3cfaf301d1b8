name: E2E Tests

on:
  workflow_dispatch:
    inputs:
      runner:
        description: "Specify runner"
        type: choice
        required: true
        default: e2e
        options:
          - e2e
          - qa
          - gh1.h3
          - d1.h5

concurrency:
  group: ${{ github.workflow }}-e2e-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-and-deploy:
    runs-on: ${{ github.event.inputs.runner || 'e2e' }}
    outputs:
      admin_password: ${{ steps.generate-secrets.outputs.admin_password }}
      frontend_port: ${{ steps.set-ports.outputs.frontend_port }}
      frontend_url: ${{ steps.set-urls.outputs.frontend_url }}
      schema_name: ${{ steps.set-env-vars.outputs.schema_name }}
      arango_user: ${{ steps.set-env-vars.outputs.arango_user }}
      frontend_image_name: ${{ steps.set-env-vars.outputs.frontend_image_name }}
      backend_image_name: ${{ steps.set-env-vars.outputs.backend_image_name }}
      config_dir: ${{ steps.set-env-vars.outputs.config_dir }}
      db_client: ${{ steps.set-env-vars.outputs.db_client }}
      db_host: ${{ steps.set-env-vars.outputs.db_host }}
      db_port: ${{ steps.set-env-vars.outputs.db_port }}
      backend_port: ${{ steps.set-ports.outputs.backend_port }}
    timeout-minutes: 90

    steps:
      - name: Set environment variables
        id: set-env-vars
        run: |
          echo "schema_name=TAS_E2E_${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}_${GITHUB_RUN_ATTEMPT}" >> $GITHUB_OUTPUT
          echo "arango_user=TAS_E2E_${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}_${GITHUB_RUN_ATTEMPT}" >> $GITHUB_OUTPUT
          echo "frontend_image_name=action-e2e-tas-front" >> $GITHUB_OUTPUT
          echo "backend_image_name=action-e2e-tas-back" >> $GITHUB_OUTPUT
          echo "config_dir=/home/<USER>/QAfolder/e2e_${GITHUB_REF_NAME}" >> $GITHUB_OUTPUT
          echo "db_client=mssql" >> $GITHUB_OUTPUT
          echo "db_host=mssqldb.cicd.neit.cz" >> $GITHUB_OUTPUT
          echo "db_port=1433" >> $GITHUB_OUTPUT

          # Also set them as environment variables for this job
          echo "SCHEMA_NAME=TAS_E2E_${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}_${GITHUB_RUN_ATTEMPT}" >> $GITHUB_ENV
          echo "ARANGO_USER=TAS_E2E_${GITHUB_RUN_ID}_${GITHUB_RUN_NUMBER}_${GITHUB_RUN_ATTEMPT}" >> $GITHUB_ENV
          echo "FRONTEND_IMAGE_NAME=action-e2e-tas-front" >> $GITHUB_ENV
          echo "BACKEND_IMAGE_NAME=action-e2e-tas-back" >> $GITHUB_ENV
          echo "CONFIG_DIR=/home/<USER>/QAfolder/e2e_${GITHUB_REF_NAME}" >> $GITHUB_ENV
          echo "DB_CLIENT=mssql" >> $GITHUB_ENV
          echo "DB_HOST=mssqldb.cicd.neit.cz" >> $GITHUB_ENV
          echo "DB_PORT=1433" >> $GITHUB_ENV

      - name: Generate encryption key
        run: |
          ENCRYPTION_KEY=$(hexdump -vn32 -e'8/4 "%08X" 1 "\n"' /dev/urandom)
          echo "ENCRYPTION_KEY=${ENCRYPTION_KEY}" >> $GITHUB_ENV
          echo "Generated database encryption key"

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Extract branch and environment info
        id: extract_branch
        run: |
          BRANCH_NAME="e2e_$(echo "${{ github.ref }}" | awk -F'/' '{print $3}' | awk '{gsub(/[^a-zA-Z0-9]/,"-")}1')"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          echo "RUNNER_NAME_SANITIZED=$(echo "$RUNNER_NAME" | cut -d'@' -f2)" >> $GITHUB_ENV
          echo "COMMIT_HASH=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker

      - name: Build frontend image
        uses: docker/build-push-action@v5
        with:
          target: prod
          context: frontend
          push: false
          tags: ${{ env.FRONTEND_IMAGE_NAME }}:${{ github.sha }}
          load: true

      # tas license support
      - name: upload tas-license public key
        run: |
          echo "${{ secrets.CICD_TAS_LICENSE_PUBLIC_KEY }}" | sed 's/\\n/\n/g' > backend/config/key/featureFlag/publickey.crt

      - name: find all .crt files and generate their SHA256
        run: |
          for cert_file in backend/config/key/featureFlag/*.crt; do
            if [ -f "$cert_file" ]; then
              cert_sha=$(sed ':a;/\S/!{$d;N;ba}' "$cert_file" | sed -z 's/\n$//' | sha256sum | awk '{print $1}')
              sed -i "/^[[:blank:]]*export[[:blank:]]\+const[[:blank:]]\+controls[[:blank:]]\+=[[:blank:]]\+\[/a \    \"$cert_sha\"," backend/src/service/secretStore/featureFlag/control.ts
            fi
          done

      - name: Build backend image
        uses: docker/build-push-action@v5
        with:
          target: prod
          context: backend
          push: false
          tags: ${{ env.BACKEND_IMAGE_NAME }}:${{ github.sha }}
          load: true

      - name: Configure application ports
        id: set-ports
        run: |
          BACKEND_PORT=$(( 8001 + $GITHUB_RUN_NUMBER % 1000 ))
          FRONTEND_PORT=$(( 7000 + $GITHUB_RUN_NUMBER % 1000 ))
          echo "BACKEND_PORT=$BACKEND_PORT" >> $GITHUB_ENV
          echo "FRONTEND_PORT=$FRONTEND_PORT" >> $GITHUB_ENV
          echo "frontend_port=$FRONTEND_PORT" >> "$GITHUB_OUTPUT"
          echo "backend_port=$BACKEND_PORT" >> "$GITHUB_OUTPUT"

      - name: Resolve database hostname
        run: |
          DB_IP=$(getent hosts ${{ env.DB_HOST }} | awk '{ print $1 }')
          echo "DB_IP=$DB_IP" >> $GITHUB_ENV

      - name: Create environment directory
        run: |
          CONFIG_DIR="/home/<USER>/QAfolder/${{ env.BRANCH_NAME }}"
          echo "CONFIG_DIR=$CONFIG_DIR" >> $GITHUB_ENV
          mkdir -p "$CONFIG_DIR"

      - name: Generate application secrets
        id: generate-secrets
        run: |
          # Generate schema password with required complexity
          DB_LOWER=$(tr -dc 'a-z' </dev/urandom | head -c 1)
          DB_UPPER=$(tr -dc 'A-Z' </dev/urandom | head -c 1)
          DB_DIGIT=$(tr -dc '0-9' </dev/urandom | head -c 1)
          DB_SPECIAL=$(tr -dc '!#%' </dev/urandom | head -c 1)
          DB_REST=$(tr -dc 'A-Za-z0-9!#%' </dev/urandom | head -c 9)
          SCHEMA_PASSWORD=$(echo "${DB_LOWER}${DB_UPPER}${DB_DIGIT}${DB_SPECIAL}${DB_REST}" | fold -w1 | shuf | tr -d '\n')
          echo "SCHEMA_PASSWORD=$SCHEMA_PASSWORD" >> $GITHUB_ENV

          # Generate Arango password
          ARANGO_PASSWORD=$(tr -dc 'A-Za-z0-9_' </dev/urandom | head -c 13)
          echo "ARANGO_PASSWORD=$ARANGO_PASSWORD" >> $GITHUB_ENV

          # Generate admin password
          ADMIN_PASSWORD=$(tr -dc 'A-Za-z0-9_' </dev/urandom | head -c 12)
          RANDOM_DIGIT=$(tr -dc '0-9' </dev/urandom | head -c 1)
          ADMIN_PASSWORD=$(echo "$ADMIN_PASSWORD$RANDOM_DIGIT" | fold -w1 | shuf | tr -d '\n')
          echo "ADMIN_PASSWORD=$ADMIN_PASSWORD" >> $GITHUB_ENV
          echo "admin_password=$ADMIN_PASSWORD" >> $GITHUB_OUTPUT

      - name: Setup MSSQL tools container
        if: ${{ env.DB_CLIENT == 'mssql' }}
        run: |
          MSSQL_TOOLS_CONTAINER_ID=$(docker run -d \
            --name mssql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
            --add-host=mssqldb.cicd.neit.cz:${{ env.DB_IP }} \
            --entrypoint tail \
            mcr.microsoft.com/mssql-tools \
            -f /dev/null)
          echo "MSSQL_TOOLS_CONTAINER_ID=$MSSQL_TOOLS_CONTAINER_ID" >> $GITHUB_ENV

      - name: Create MSSQL database and user
        run: |
          cat << EOF > ${{ env.CONFIG_DIR }}/mssql_schema.sql
          CREATE LOGIN ${{ env.SCHEMA_NAME }} WITH PASSWORD='${{ env.SCHEMA_PASSWORD }}';
          GO
          CREATE DATABASE [${{ env.SCHEMA_NAME }}] COLLATE Czech_100_CI_AS;
          GO
          USE [${{ env.SCHEMA_NAME }}];
          GO
          CREATE USER ${{ env.SCHEMA_NAME }} FOR LOGIN ${{ env.SCHEMA_NAME }};
          GO
          ALTER ROLE [db_owner] ADD MEMBER ${{ env.SCHEMA_NAME }};
          GO
          CREATE SCHEMA tas AUTHORIZATION ${{ env.SCHEMA_NAME }};
          GO
          ALTER USER ${{ env.SCHEMA_NAME }} WITH DEFAULT_SCHEMA = tas;
          GO
          GRANT CREATE VIEW TO ${{ env.SCHEMA_NAME }};
          GRANT CREATE TABLE TO ${{ env.SCHEMA_NAME }};
          GO
          ALTER DATABASE [${{ env.SCHEMA_NAME }}] SET READ_COMMITTED_SNAPSHOT ON;
          GO
          EOF

          docker cp ${{ env.CONFIG_DIR }}/mssql_schema.sql ${{ env.MSSQL_TOOLS_CONTAINER_ID }}:/tmp/mssql_schema.sql
          docker exec ${{ env.MSSQL_TOOLS_CONTAINER_ID }} /opt/mssql-tools/bin/sqlcmd \
            -S ${{ env.DB_HOST }} \
            -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} \
            -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} \
            -i /tmp/mssql_schema.sql

      - name: Configure backend environment
        run: |
          cat << EOF > ${{ env.CONFIG_DIR }}/local.js
          module.exports = {
            port: ${{ env.BACKEND_PORT }},
            prefix: null,
            hostname: 'http://host.docker.internal:${{ env.BACKEND_PORT }}',
            connections: {
              knexConnection: {
                host: '${{ env.DB_HOST }}',
                port: ${{ env.DB_PORT }},
                user: '${{ env.SCHEMA_NAME }}',
                password: '${{ env.SCHEMA_PASSWORD }}',
                database: '${{ env.SCHEMA_NAME }}',
                schema: 'tas',
                requestTimeout: 130000,
                options: {
                  idleTimeoutMillis: 130000,
                  requestTimeout: 300000,
                },
              },
            },
            db: {
              client: '${{ env.DB_CLIENT }}',
              maxPoolConnections: 40,
            },
            cache: {
              secureCookie: false,
            },
            langs: ['cs', 'en', 'de'],
            dms: {
              tikaUrl: 'http://tika:9998',
              elasticUrl: 'http://elasticsearch:9200',
              elasticVersion: 7,
              storagePath: './_storage',
            },
            security: {
              saltRounds: 2,
              pepper: '4enqL99M7Cz/cGlM3Dy/eCUVs',
              authTokenSecret: 'EMFtBuWsup',
              refreshTokenSecret: 'L5duAhH5Yp',
            },
            frontendUrl: 'http://host.docker.internal:${{ env.FRONTEND_PORT }}',
            redis: {
              host: 'redis',
              port: 6379,
            },
            logger: {
              arango: {
                host: 'http://arango:8529',
                db_name: '${{ env.ARANGO_USER }}_db',
                auth: {
                  user: '${{ env.ARANGO_USER }}',
                  pass: '${{ env.ARANGO_PASSWORD }}',
                },
              },
            },
            paths: {
              tmp: '/tmp',
              userPhotoPath: '/tmp',
            }
          };
          EOF

      - name: Configure frontend environment
        run: |
          cat << EOF > ${{ env.CONFIG_DIR }}/tas.js
          var config = require('./tas.base.js');

          config.backendUrl = 'http://host.docker.internal:${{ env.BACKEND_PORT }}';
          config.useSsl = false;
          config.port = ${{ env.FRONTEND_PORT }};
          config.webpackPort = 8888;
          config.cloneDynRows = true;
          config.socketNotifs = false;

          module.exports = config;
          EOF

      - name: Setup Arango database
        run: |
          ARANGO_ID=$(docker ps -q -f name=arango)
          cat << EOF | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}"
          var users = require('@arangodb/users');
          users.save('${{ env.ARANGO_USER }}', '${{ env.ARANGO_PASSWORD }}');
          db._createDatabase('${{ env.ARANGO_USER }}_db');
          users.grantDatabase('${{ env.ARANGO_USER }}', '${{ env.ARANGO_USER }}_db', 'rw');
          users.grantCollection('${{ env.ARANGO_USER }}', '${{ env.ARANGO_USER }}_db', '*', 'rw');
          EOF

      - name: Initialize database schema
        if: ${{ env.DB_CLIENT == 'mssql' }}
        run: |
          cat << EOF > ${{ env.CONFIG_DIR }}/prefix.sql
          USE ${{ env.SCHEMA_NAME }};
          GO
          SET NOCOUNT ON;
          GO
          EOF

          sed -e 's/\[TAS\]\./\[tas\]\./g' \
            -e '/^USE \[TAS\]/d' \
            -e '/^GO$/d' \
            ${{ github.workspace }}/tests-cy/test-db/tests-cy-mssql-setup.sql >> ${{ env.CONFIG_DIR }}/prefix.sql

          docker cp ${{ env.CONFIG_DIR }}/prefix.sql ${{ env.MSSQL_TOOLS_CONTAINER_ID }}:/tmp/mssql-setup.sql

          docker exec \
            -e SQLCMDPASSWORD="${{ env.SCHEMA_PASSWORD }}" \
            ${{ env.MSSQL_TOOLS_CONTAINER_ID }} /opt/mssql-tools/bin/sqlcmd \
            -S "${{ env.DB_HOST }}" \
            -U "${{ env.SCHEMA_NAME }}" \
            -d "${{ env.SCHEMA_NAME }}" \
            -I \
            -b \
            -i /tmp/mssql-setup.sql

      - name: Synchronize database sequences
        if: ${{ env.DB_CLIENT == 'mssql' }}
        run: |
          sed -e 's/\[TAS\]\./\[tas\]\./g' \
            -e '/^USE \[TAS\]/d' \
            -e '/^GO$/d' \
            ${{ github.workspace }}/tests-cy/test-db/tests-cy-mssql-sequence-sync.sql > ${{ env.CONFIG_DIR }}/sequence-sync.sql

          docker cp ${{ env.CONFIG_DIR }}/sequence-sync.sql ${{ env.MSSQL_TOOLS_CONTAINER_ID }}:/tmp/sequence-sync.sql

          docker exec \
            -e SQLCMDPASSWORD="${{ env.SCHEMA_PASSWORD }}" \
            ${{ env.MSSQL_TOOLS_CONTAINER_ID }} /opt/mssql-tools/bin/sqlcmd \
            -S "${{ env.DB_HOST }}" \
            -U "${{ env.SCHEMA_NAME }}" \
            -d "${{ env.SCHEMA_NAME }}" \
            -I \
            -b \
            -i /tmp/sequence-sync.sql

      - name: Update admin password
        if: ${{ env.DB_CLIENT == 'mssql' }}
        run: |
          ADMIN_PASSWORD_HASH=$(echo -n "${{ env.ADMIN_PASSWORD }}" | md5sum | cut -d ' ' -f 1)

          cat << EOF > ${{ env.CONFIG_DIR }}/admin_pw.sql
          USE ${{ env.SCHEMA_NAME }};
          GO
          UPDATE tas.USERS
          SET user_password='$ADMIN_PASSWORD_HASH'
          WHERE user_name='admin';
          GO
          EOF

          docker cp ${{ env.CONFIG_DIR }}/admin_pw.sql ${{ env.MSSQL_TOOLS_CONTAINER_ID }}:/tmp/admin_pw.sql
          docker exec \
            -e SQLCMDPASSWORD="${{ env.SCHEMA_PASSWORD }}" \
            ${{ env.MSSQL_TOOLS_CONTAINER_ID }} /opt/mssql-tools/bin/sqlcmd \
            -S "${{ env.DB_HOST }}" \
            -U "${{ env.SCHEMA_NAME }}" \
            -d "${{ env.SCHEMA_NAME }}" \
            -I \
            -b \
            -i /tmp/admin_pw.sql

      - name: Run database migrations
        run: |
          docker run --rm \
            --name ${{ env.BACKEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }} \
            --add-host=arango:host-gateway \
            --add-host=redis:host-gateway \
            --add-host=elasticsearch:host-gateway \
            --add-host=tika:host-gateway \
            --add-host=${{ env.DB_HOST }}:${{ env.DB_IP }} \
            -p ${{ env.BACKEND_PORT }}:${{ env.BACKEND_PORT }} \
            -v ${{ env.CONFIG_DIR }}/local.js:/app/tas/backend/config/config/local.js \
            -e TAS_DATABASE_ENCRYPTION_KEY=${{ env.ENCRYPTION_KEY }} \
            ${{ env.BACKEND_IMAGE_NAME }}:${{ github.sha }} \
            node dist/migrate.js up

      - name: Start backend service
        run: |
          docker run -d \
            --name ${{ env.BACKEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }} \
            --add-host=arango:host-gateway \
            --add-host=redis:host-gateway \
            --add-host=host.docker.internal:host-gateway \
            --add-host=elasticsearch:host-gateway \
            --add-host=tika:host-gateway \
            --add-host=${{ env.DB_HOST }}:${{ env.DB_IP }} \
            -p ${{ env.BACKEND_PORT }}:${{ env.BACKEND_PORT }} \
            -v ${{ env.CONFIG_DIR }}/local.js:/app/tas/backend/config/config/local.js \
            -e TAS_DATABASE_ENCRYPTION_KEY=${{ env.ENCRYPTION_KEY }} \
            ${{ env.BACKEND_IMAGE_NAME }}:${{ github.sha }}

      - name: Set Deployment URL for API calls
        run: |
          # URL for API calls from the runner to the exposed backend port
          echo "DEPLOYMENT_URL=http://localhost:${{ env.BACKEND_PORT }}" >> $GITHUB_ENV

      - name: create license
        run: |
          DEPLOYMENT_URL_FOR_LICENSE="http://localhost:${{ env.BACKEND_PORT }}"
          FUTURE_DATE=$(date -u -d "+5 days" +"%Y-%m-%dT%H:%M:%S.%3NZ") # Shorter validity for E2E

          LICENSE_RESPONSE=$(curl -s -X 'POST' \
            'https://dev.license.teamassistant.app/license' \
            -H 'accept: application/json' \
            -H 'authorization: ${{ secrets.CICD_TAS_LICENSE_AUTH_KEY }}' \
            -H 'Content-Type: application/json' \
            -d '{
            "hostname": "'$DEPLOYMENT_URL_FOR_LICENSE'",
            "content": {
              "template": true
            },
            "validTo": "'$FUTURE_DATE'"
          }')

          # Extract and validate token
          LICENSE_TOKEN=$(echo "$LICENSE_RESPONSE" | jq -r '.item.token // empty')
          if [ -z "$LICENSE_TOKEN" ]; then
            echo "Error: Failed to extract LICENSE_TOKEN from response:"
            printf '%s\\n' "$LICENSE_RESPONSE"
            exit 1
          fi
          echo "LICENSE_TOKEN=$LICENSE_TOKEN" >> $GITHUB_ENV
        shell: bash

      - name: Wait for backend service
        # Waits for the backend API to return HTTP 200 OK
        run: |
          max_retries=30
          interval=2
          endpoint="${{ env.DEPLOYMENT_URL }}/status"
          backend_ready=false

          echo "Checking backend status at $endpoint (max $max_retries attempts, $interval sec interval)..."
          for i in $(seq 1 $max_retries); do
            # Added --fail to make curl return non-zero on server errors (4xx, 5xx)
            http_status=$(curl -s -o /dev/null -w '%{http_code}' --max-time 5 --fail "$endpoint" || echo "Error")
            if [ "$http_status" = "200" ]; then
              echo "Backend is ready (HTTP 200 OK)."
              backend_ready=true
              break
            elif [ "$http_status" = "Error" ]; then
               echo "Attempt $i/$max_retries: Connection failed or non-2xx/3xx response. Waiting $interval seconds..."
            else
               echo "Attempt $i/$max_retries: Backend not ready (HTTP $http_status). Waiting $interval seconds..."
            fi
            sleep $interval
          done

          if [ "$backend_ready" != "true" ]; then
            echo "Error: Backend did not become ready after $max_retries attempts."
            exit 1
          fi
        shell: bash

      - name: Get TAS access token
        run: |
          AUTH_RESPONSE=$(curl -s "${{ env.DEPLOYMENT_URL }}/authenticate" \
            --data-raw "username=admin&password=${{ env.ADMIN_PASSWORD }}&grant_type=password&scope=&auth_id=1")

          # Extract and validate token
          TAS_ACCESS_TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.access_token // empty')
          if [ -z "$TAS_ACCESS_TOKEN" ]; then
            echo "Error: Failed to get TAS_ACCESS_TOKEN from response:"
            printf '%s\\n' "$AUTH_RESPONSE"
            exit 1
          fi
          echo "TAS_ACCESS_TOKEN=$TAS_ACCESS_TOKEN" >> $GITHUB_ENV
        shell: bash

      - name: activate license
        run: |
          JSON_PAYLOAD=$(jq -n --arg name "$SCHEMA_NAME" --arg key "$LICENSE_TOKEN" \
            '{name: $name, licenseKey: $key}')

          curl '${{ env.DEPLOYMENT_URL }}/license' \
            -H 'Content-Type: application/json' \
            -H "Cookie: accessTokenV2=Bearer%20${TAS_ACCESS_TOKEN}" \
            --data "$JSON_PAYLOAD"
        shell: bash

      - name: Start frontend service
        run: |
          docker run -d \
            --network QA-TAS \
            --name ${{ env.FRONTEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }} \
            --add-host=host.docker.internal:host-gateway \
            -v ${{ env.CONFIG_DIR }}/tas.js:/app/tas/frontend/config/tas.js \
            -p ${{ env.FRONTEND_PORT }}:${{ env.FRONTEND_PORT }} \
            ${{ env.FRONTEND_IMAGE_NAME }}:${{ github.sha }}

      - name: Save environment variables
        run: |
          cat << EOF > ${{ env.CONFIG_DIR }}/variables.txt
          FRONTEND_ID=$(docker ps -q -f name=${{ env.FRONTEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }})
          BACKEND_ID=$(docker ps -q -f name=${{ env.BACKEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }})
          SCHEMA_NAME=${{ env.SCHEMA_NAME }}
          ARANGO_USER=${{ env.ARANGO_USER }}
          DB_CLIENT=${{ env.DB_CLIENT }}
          DB_HOST=${{ env.DB_HOST }}
          DB_PORT=${{ env.DB_PORT }}
          EOF

      - name: Set container URLs
        id: set-urls
        run: |
          FRONTEND_NAME="${{ env.FRONTEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }}"
          echo "frontend_url=${FRONTEND_NAME}" >> "$GITHUB_OUTPUT"

      - name: Cleanup MSSQL tools
        run: |
          docker stop ${{ env.MSSQL_TOOLS_CONTAINER_ID }} && docker rm ${{ env.MSSQL_TOOLS_CONTAINER_ID }} || true

  setup-cypress:
    needs: build-and-deploy
    env:
      ADMIN_PASSWORD: ${{ needs.build-and-deploy.outputs.admin_password }}
      FRONTEND_PORT: ${{ needs.build-and-deploy.outputs.frontend_port }}
      FRONTEND_URL: ${{ needs.build-and-deploy.outputs.frontend_url }}
    runs-on: ${{ github.event.inputs.runner }}
    steps:
      - name: Create Cypress configuration
        run: |
          cat << EOF > ${{ github.workspace }}/tests-cy/cypress.config.local.ts
          const localConfig: CypressConfigLocal = {
            e2e: {
              baseUrl: "http://host.docker.internal:${{ env.FRONTEND_PORT }}",
              viewportWidth: 1920,
              viewportHeight: 1080,
              excludeSpecPattern: [
                'cypress/e2e/__meta_tests__/**',
              ],
              env: {
                defaultUserUsername: 'admin',
                defaultUserPassword: '${{ env.ADMIN_PASSWORD }}',
                testInstanceUserPasswords: {
                  testInstanceAdmin: '${{ env.ADMIN_PASSWORD }}',
                  testInstanceUser1: '',
                  testInstanceUser2: '',
                },
                skipExcludedTestTagsSilently: false,
                validateFixturesIfPossible: true,
                batchFixtureValidationExcludedPatterns: [
                  '__meta_tests__/**',
                ],
              },
            },
            screenshotsFolder: '/srv/screenshot'
          };

          export default localConfig;
          EOF

  run-tests:
    needs: [build-and-deploy, setup-cypress]
    runs-on: ${{ github.event.inputs.runner }}
    permissions:
      contents: read
      id-token: write
    container:
      image: cypress/included:13.17.0
      options: --add-host=host.docker.internal:host-gateway --user root
      volumes:
        - ${{ github.workspace }}/tests-cy:/srv/tests-cy
        - ${{ github.workspace }}/tests-cy/package.json:/srv/package.json
      env:
        HOME: /root
        DBUS_SESSION_BUS_ADDRESS: /dev/null
        DBUS_SYSTEM_BUS_ADDRESS: /dev/null

    steps:
      - name: Setup NPM environment
        run: |
          mkdir -p /srv/.npm
          npm config set cache /srv/.npm --global
          npm config set prefix /srv/.npm --global

      - name: Create screenshots directory
        run: |
          mkdir -p /srv/screenshot
          chmod 777 /srv/screenshot

      - name: Install dependencies
        working-directory: /srv
        env:
          HOME: /srv
          npm_config_cache: /srv/.npm
        run: npm i --ignore-scripts

      - name: Execute Cypress tests
        working-directory: /srv/tests-cy
        env:
          HOME: /root
        run: cypress run --browser chrome
        continue-on-error: true

      - name: Upload test artifacts
        uses: actions/upload-artifact@v4
        with:
          name: cypress-screenshots
          path: /srv/screenshot/*
          if-no-files-found: warn

  cleanup:
    if: always()
    needs: [build-and-deploy, setup-cypress, run-tests]
    runs-on: ${{ github.event.inputs.runner }}
    env:
      SCHEMA_NAME: ${{ needs.build-and-deploy.outputs.schema_name }}
      ARANGO_USER: ${{ needs.build-and-deploy.outputs.arango_user }}
      FRONTEND_IMAGE_NAME: ${{ needs.build-and-deploy.outputs.frontend_image_name }}
      BACKEND_IMAGE_NAME: ${{ needs.build-and-deploy.outputs.backend_image_name }}
      CONFIG_DIR: ${{ needs.build-and-deploy.outputs.config_dir }}
      DB_CLIENT: ${{ needs.build-and-deploy.outputs.db_client }}
      DB_HOST: ${{ needs.build-and-deploy.outputs.db_host }}
      DB_PORT: ${{ needs.build-and-deploy.outputs.db_port }}

    steps:
      - name: Stop and remove Docker containers
        run: |
          # Stop and remove frontend container
          FRONTEND_CONTAINER="${{ env.FRONTEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }}"
          docker stop $FRONTEND_CONTAINER 2>/dev/null || true
          docker rm $FRONTEND_CONTAINER 2>/dev/null || true

          # Stop and remove backend container
          BACKEND_CONTAINER="${{ env.BACKEND_IMAGE_NAME }}-${{ github.run_number }}-${{ env.BRANCH_NAME }}"
          docker stop $BACKEND_CONTAINER 2>/dev/null || true
          docker rm $BACKEND_CONTAINER 2>/dev/null || true

          # Stop and remove any leftover MSSQL tools containers
          docker ps -a | grep "mssql-tools-${{ github.run_id }}" | awk '{print $1}' | xargs -r docker stop
          docker ps -a | grep "mssql-tools-${{ github.run_id }}" | awk '{print $1}' | xargs -r docker rm

      - name: Remove Docker images
        run: |
          # Remove frontend image
          docker rmi ${{ env.FRONTEND_IMAGE_NAME }}:${{ github.sha }} 2>/dev/null || true
          # Remove backend image
          docker rmi ${{ env.BACKEND_IMAGE_NAME }}:${{ github.sha }} 2>/dev/null || true

      - name: Cleanup MSSQL database
        run: |
          # Create cleanup script
          cat << EOF > ${{ env.CONFIG_DIR }}/cleanup.sql
          -- Kill all connections to the database
          USE master;
          GO
          ALTER DATABASE [${{ env.SCHEMA_NAME }}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
          GO
          -- Drop the database
          DROP DATABASE IF EXISTS [${{ env.SCHEMA_NAME }}];
          GO
          -- Drop the login
          DROP LOGIN [${{ env.SCHEMA_NAME }}];
          GO
          EOF

          # Create temporary MSSQL tools container for cleanup
          CLEANUP_CONTAINER_ID=$(docker run -d \
            --name mssql-tools-cleanup-${{ github.run_id }} \
            --add-host=mssqldb.cicd.neit.cz:$(getent hosts ${{ env.DB_HOST }} | awk '{ print $1 }') \
            --entrypoint tail \
            mcr.microsoft.com/mssql-tools \
            -f /dev/null)

          # Copy and execute cleanup script
          docker cp ${{ env.CONFIG_DIR }}/cleanup.sql $CLEANUP_CONTAINER_ID:/tmp/cleanup.sql
          docker exec $CLEANUP_CONTAINER_ID /opt/mssql-tools/bin/sqlcmd \
            -S ${{ env.DB_HOST }} \
            -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} \
            -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} \
            -i /tmp/cleanup.sql

          # Remove cleanup container
          docker stop $CLEANUP_CONTAINER_ID
          docker rm $CLEANUP_CONTAINER_ID

      - name: Cleanup Arango database
        run: |
          ARANGO_ID=$(docker ps -q -f name=arango)
          cat << EOF | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}"
          try {
            db._dropDatabase('${{ env.ARANGO_USER }}_db');
          } catch(err) {
            // Ignore if database doesn't exist
          }
          try {
            require('@arangodb/users').remove('${{ env.ARANGO_USER }}');
          } catch(err) {
            // Ignore if user doesn't exist
          }
          EOF

      - name: Remove configuration directory
        run: |
          rm -rf ${{ env.CONFIG_DIR }} || true
