name: QA Instance Retention

permissions:
  issues: write
  
on:
  schedule:
    - cron: '0 * * * *' # Every hour
  workflow_dispatch:
    inputs:
      debug:
        description: 'Enable debug logging'
        required: false
        default: 'false'
        type: choice
        options:
          - 'true'
          - 'false'
      retention_hours:
        description: 'Hours before warning (default: 48)'
        required: false
        default: '48'
      inactivity_hours:
        description: 'Hours of inactivity before closing (default: 24)'
        required: false
        default: '24'
jobs:
  check-retention:
    runs-on: neit-linux
    permissions:
      issues: write
      contents: read
      packages: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Setup Deno
        uses: denoland/setup-deno@v2
        with:
          deno-version: v2.2.x
      - name: Run Retention Check
        working-directory: .github/retention
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITHUB_OWNER: ${{ github.repository_owner }}
          GITHUB_REPO: ${{ github.event.repository.name }}
          RETENTION_HOURS: ${{ inputs.retention_hours || 48 }}
          INACTIVITY_THRESHOLD_HOURS: ${{ inputs.inactivity_hours || 24 }}
          DEBUG: ${{ inputs.debug || 'false' }}
        run: |
          deno run --allow-net=api.github.com --allow-env main.ts