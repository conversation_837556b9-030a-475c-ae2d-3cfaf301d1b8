name: Build TAS archive for standalone installation
run-name: Build TAS archive for standalone installation by ${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag version'
        required: false
      chosen-os:
        description: 'runner selection'
        required: false
        type: choice
        options:
          - builder
          - gh1.int
          - gh2.int
          - gh3.int
          - gh4.int
          - gh1.h3
          - gh1.h7
          - d1.h5
      used-dockerfile:
        description: "Debian / Alpine based image"
        required: true
        type: choice
        default: Dockerfile
        options:
          - Dockerfile
          - Dockerfile-Debian
      obfuscate:
        description: 'Use obfuscated build?'
        required: true
        type: choice
        default: 'true'
        options:
          - 'true'
          - 'false'

jobs:
  prepare:
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    outputs:
      image_tag: ${{ env.IMAGE_TAG }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Set image tag
        run: |
          SHORT_COMMIT_HASH=$(git rev-parse --short HEAD)
          TIMESTAMP=$(date +"%y%m%d%H%M")

          if [[ $GITHUB_REF == refs/tags/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/tags/"}
          elif [[ $GITHUB_REF == refs/heads/stable/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/stable/"}
          elif [[ $GITHUB_REF == refs/heads/dev/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/dev/"}
          elif [[ $GITHUB_EVENT_NAME == "workflow_dispatch" ]]; then
              TAG_NAME="${{ github.event.inputs.tag_name }}"
          fi

          TAG_NAME_CLEANED=$(echo "$TAG_NAME" | sed -E "s/'//g; s/[^a-zA-Z0-9\.\-]/-/g")
          IMAGE_TAG="${TAG_NAME_CLEANED}-${SHORT_COMMIT_HASH}-${TIMESTAMP}"
          echo "TAG_NAME_CLEANED=${TAG_NAME_CLEANED}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV

  build-backend:
    needs: prepare
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Build backend image
        uses: docker/build-push-action@v5
        with:
          target: ${{ inputs.obfuscate == 'false' && 'prod' || 'prod_obfuscated' }}
          context: backend
          file: backend/${{ inputs.used-dockerfile || 'Dockerfile' }}
          push: false
          tags: tas-backend:${{ needs.prepare.outputs.image_tag }}

      - name: Extract backend files
        run: |
          CONTAINER_ID=$(docker create tas-backend:${{ needs.prepare.outputs.image_tag }})
          mkdir -p ${{ github.workspace }}/extract/backend
          docker cp $CONTAINER_ID:/app/tas/backend/. - | tar -xC ${{ github.workspace }}/extract/backend/
          docker rm $CONTAINER_ID

      - name: Upload backend files
        uses: actions/upload-artifact@v4
        with:
          name: backend-${{ needs.prepare.outputs.image_tag }}${{ inputs.obfuscate == 'false' && '' || '-obfuscated' }}
          path: ${{ github.workspace }}/extract/backend
          include-hidden-files: true
          retention-days: 90

  build-frontend:
    needs: prepare
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true

      - name: Build frontend image
        uses: docker/build-push-action@v5
        with:
          target: ${{ inputs.obfuscate == 'false' && 'prod' || 'prod_obfuscated' }}
          context: frontend
          file: frontend/${{ inputs.used-dockerfile || 'Dockerfile' }}
          push: false
          tags: tas-frontend:${{ needs.prepare.outputs.image_tag }}

      - name: Extract frontend files
        run: |
          CONTAINER_ID=$(docker create tas-frontend:${{ needs.prepare.outputs.image_tag }})
          mkdir -p ${{ github.workspace }}/extract/frontend
          docker cp $CONTAINER_ID:/app/tas/frontend/. - | tar -xC ${{ github.workspace }}/extract/frontend/
          docker rm $CONTAINER_ID

      - name: Upload frontend files
        uses: actions/upload-artifact@v4
        with:
          name: frontend-${{ needs.prepare.outputs.image_tag }}${{ inputs.obfuscate == 'false' && '' || '-obfuscated' }}
          path: ${{ github.workspace }}/extract/frontend
          include-hidden-files: true
          retention-days: 90

      - name: Cleanup Docker Containers and Extract Dir
        if: always()
        run: |
          echo "Cleaning up any leftover containers and extract directory"
          rm -rf ${{ github.workspace }}/extract 
          docker rm $(docker ps -aq --filter "ancestor=tas-frontend:${{ needs.prepare.outputs.image_tag }}") 2>/dev/null || true
          docker rm $(docker ps -aq --filter "ancestor=tas-backend:${{ needs.prepare.outputs.image_tag }}") 2>/dev/null || true
          echo "Cleanup finished."