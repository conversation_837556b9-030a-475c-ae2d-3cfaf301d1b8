name: QA Cleanup Dispatcher

on:
  issues:
    types: [closed]

permissions:
  actions: write
  contents: read

jobs:
  dispatch-cleanup:
    runs-on: ubuntu-latest
    # Only run if the issue title contains QA-Instance ready
    if: contains(github.event.issue.title, 'QA-Instance ready')
    
    steps:
      - name: "Extract deployment information from issue"
        id: extract_info
        run: |
          # Extract URL from the issue title (format: "QA-Instance ready [URL]")
          DEPLOYMENT_URL=$(echo "${{ github.event.issue.title }}" | awk -F'[][]' '{print $2}')
          
          # Extract branch name from the issue body
          BRANCH_NAME=$(echo "${{ github.event.issue.body }}" | grep "Branch:" | awk '{print $2}')
          
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          
          echo "DEBUG: Extracted deployment URL: $DEPLOYMENT_URL"
          echo "DEBUG: Extracted branch name: $BRANCH_NAME"
          
          # Validate that we have both URL and branch
          if [ -z "$DEPLOYMENT_URL" ] || [ -z "$BRANCH_NAME" ]; then
            echo "ERROR: Could not extract deployment URL or branch name from issue"
            echo "Issue title: ${{ github.event.issue.title }}"
            echo "Issue body: ${{ github.event.issue.body }}"
            exit 1
          fi

      - name: "Trigger cleanup workflow on specific branch"
        run: |
          curl -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/actions/workflows/qa-cleanup.yml/dispatches \
            -d '{
              "ref": "${{ env.BRANCH_NAME }}",
              "inputs": {
                "deployment_url": "${{ env.DEPLOYMENT_URL }}",
                "triggered_by": "issue_close_api"
              }
            }'
          
          echo "Successfully triggered cleanup workflow on branch: ${{ env.BRANCH_NAME }}"
          echo "Deployment URL: ${{ env.DEPLOYMENT_URL }}"
