name: Cleanup QA environment

on:

  workflow_dispatch:
    inputs:
      deployment_url:
        description: 'URL'
        required: true
        type: string
      triggered_by:
        description: 'How this workflow was triggered'
        required: false
        type: string
        default: 'manual'

  workflow_call:
    inputs:
      deployment_url:
        description: 'URL'
        required: true
        type: string
      triggered_by:
        description: 'How this workflow was triggered'
        required: false
        type: string
        default: 'workflow_call'

jobs:

  prepare:

    runs-on: qa
    outputs:
      DEPLOYMENT_BRANCH: ${{ steps.parse_url.outputs.DEPLOYMENT_BRANCH }}
      DEPLOYMENT_RUNNER: ${{ steps.parse_url.outputs.DEPLOYMENT_RUNNER }}
    steps:

      - name: "Determine the deployment URL based on the trigger"
        id: determine_url
        run: |

          # Use the URL provided by input (works for both workflow_dispatch and workflow_call)
          DEPLOYMENT_URL="${{ inputs.deployment_url }}"
          TRIGGERED_BY="${{ inputs.triggered_by }}"

          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          echo "TRIGGERED_BY=$TRIGGERED_BY" >> $GITHUB_ENV
          echo "DEBUG: Deployment URL: $DEPLOYMENT_URL"
          echo "DEBUG: Triggered by: $TRIGGERED_BY"

      - name: "Parse URL and extract information"
        id: parse_url
        run: |

          # Extract branch name from the deployment URL
          DEPLOYMENT_BRANCH=$(echo $DEPLOYMENT_URL | awk -F[/:] '{print $4}' | awk -F. '{print $1}')
          echo "DEPLOYMENT_BRANCH=$DEPLOYMENT_BRANCH" >> $GITHUB_OUTPUT
          echo "DEBUG: Parsed deployment branch: $DEPLOYMENT_BRANCH"

          # Extract runner name from the deployment URL
          DEPLOYMENT_RUNNER=$(echo "$DEPLOYMENT_URL" | sed -n 's|.*://[^.]*\.\([^/]*\)\.qa\.teamassistant\.cz.*|\1|p')
          echo "DEPLOYMENT_RUNNER=$DEPLOYMENT_RUNNER" >> $GITHUB_OUTPUT
          echo "DEBUG: Parsed deployment runner: $DEPLOYMENT_RUNNER"

  cleanup:
    runs-on: "${{ needs.prepare.outputs.DEPLOYMENT_RUNNER }}"
    timeout-minutes: 90
    needs: prepare
    steps:

      - name: "Checkout code"
        uses: actions/checkout@v4

      - name: "Set environment variables"
        run: |

          echo "DEPLOYMENT_BRANCH=${{ needs.prepare.outputs.DEPLOYMENT_BRANCH }}" >> $GITHUB_ENV
          echo "DEBUG: Prepared deployment branch: ${{ needs.prepare.outputs.DEPLOYMENT_BRANCH }}"

          echo "DEPLOYMENT_RUNNER=${{ needs.prepare.outputs.DEPLOYMENT_RUNNER }}" >> $GITHUB_ENV
          echo "DEBUG: Prepared deployment runner: ${{ needs.prepare.outputs.DEPLOYMENT_RUNNER }}"

          echo "CONFIG_FOLDER=/home/<USER>/QAfolder/${{ needs.prepare.outputs.DEPLOYMENT_BRANCH }}" >> $GITHUB_ENV

      - name: "Debug environment variables"
        run: |

          echo "DEBUG: Prepared deployment branch: ${{ env.DEPLOYMENT_BRANCH }}"
          echo "DEBUG: Prepared deployment runner: ${{ env.DEPLOYMENT_RUNNER }}"

      - name: "Get deployment variables"
        shell: bash
        run: |
          FRONTEND_ID=$(grep '^FRONTEND_ID=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          BACKEND_ID=$(grep '^BACKEND_ID=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          BACKEND_CRON_ID=$(grep '^BACKEND_CRON_ID=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          SCHEMA_NAME=$(grep '^SCHEMA_NAME=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          ARANGO_USER=$(grep '^ARANGO_USER=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_CLIENT=$(grep '^DB_CLIENT=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_HOST=$(grep '^DB_HOST=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_PORT=$(grep '^DB_PORT=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_TYPE=$(grep '^DB_TYPE=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          echo "FRONTEND_ID=$FRONTEND_ID" >> $GITHUB_ENV
          echo "BACKEND_ID=$BACKEND_ID" >> $GITHUB_ENV
          echo "BACKEND_CRON_ID=$BACKEND_CRON_ID" >> $GITHUB_ENV
          echo "SCHEMA_NAME=$SCHEMA_NAME" >> $GITHUB_ENV
          echo "ARANGO_USER=$ARANGO_USER" >> $GITHUB_ENV
          echo "DB_CLIENT=$DB_CLIENT" >> $GITHUB_ENV
          echo "DB_HOST=$DB_HOST" >> $GITHUB_ENV
          echo "DB_PORT=$DB_PORT" >> $GITHUB_ENV
          echo "DB_TYPE=$DB_TYPE" >> $GITHUB_ENV

      - name: "Drop MSSQL database"
        if: ${{ env.DB_TYPE == 'mssql' }}
        run: |
          echo "DB_CLIENT=${{ env.DB_CLIENT }}"
          echo "
          USE master;
          DROP DATABASE ${{ env.SCHEMA_NAME }};
          DROP LOGIN ${{ env.SCHEMA_NAME }};
          GO
          " > ${{ env.CONFIG_FOLDER }}/mssql_drop.sql

          # Start MS SQL tools container
          CONTAINER_ID=`docker run -d --name mssql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          --add-host=mssqldb.cicd.neit.cz:$(getent hosts mssqldb.cicd.neit.cz | awk '{ print $1 }') \
          --entrypoint tail \
          mcr.microsoft.com/mssql-tools \
          -f /dev/null`

          # Copy drop script
          docker cp ${{ env.CONFIG_FOLDER }}/mssql_drop.sql $CONTAINER_ID:/tmp/mssql_drop.sql

          # Exec and remove User
          docker exec $CONTAINER_ID /opt/mssql-tools/bin/sqlcmd -S ${{ env.DB_HOST }} -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} -i /tmp/mssql_drop.sql

          # Stop and remove container
          docker stop $CONTAINER_ID && docker rm $CONTAINER_ID

      - name: "Drop Oracle schema"
        if: ${{ github.event.inputs.database == 'oracledb' || inputs.database == 'oracledb' }}
        run: |
          echo "DB_CLIENT=${{ env.DB_CLIENT }}"
          SQL_COMMAND="WHENEVER SQLERROR EXIT SQL.SQLCODE\nWHENEVER OSERROR EXIT FAILURE\nDROP USER \"TAS_${{ github.run_id }}_${{ github.run_number }}_${{ github.run_attempt }}\" CASCADE;"
          echo -e "$SQL_COMMAND" > drop_ora_schema.sql
          cat drop_ora_schema.sql  # For debugging purposes
          for i in $(seq 1 10); do
            sqlplus ${{ secrets.CICD_DB_ORACLE_ADMIN_USER }}/${{ secrets.CICD_DB_ORACLE_ADMIN_PASSWORD }}@${{ env.DB_HOST }}:${{ env.DB_POR }}/${ORACLE_DB_SID:-XEPDB1} as sysdba @${{ github.workspace }}/drop_ora_schema.sql
            if [ $? -eq 0 ]; then
              break
            else
              sleep 3
            fi
          done

      - name: "Drop Arango database and user"
        run: |
          # Grab container ID
          ARANGO_ID=$(docker ps -q -f name=arango)
          # Arango cleanup commands
          ARANGO_CLEANUP=$(cat <<EOF
          db._dropDatabase('${{ env.ARANGO_USER }}');
          require("@arangodb/users").remove('${{ env.ARANGO_USER }}');
          EOF
          )
          # Execute arango cleanup
          echo "$ARANGO_CLEANUP" | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}"

      - name: "Remove deployment folder"
        run: rm -rf ${{ env.CONFIG_FOLDER }}

      - name: "Remove nginx config file"
        run: unlink /home/<USER>/nginx-sites/${{ env.DEPLOYMENT_BRANCH }}.${{ env.DEPLOYMENT_RUNNER }}.conf

      - name: "Remove backend and frontend container"
        run: |
          echo "Removing containers:"
          echo "  FRONTEND_ID: ${{ env.FRONTEND_ID }}"
          echo "  BACKEND_ID: ${{ env.BACKEND_ID }}"
          echo "  BACKEND_CRON_ID: ${{ env.BACKEND_CRON_ID }}"
          docker rm -f ${{ env.FRONTEND_ID }} || true
          docker rm -f ${{ env.BACKEND_ID }} || true
          docker rm -f ${{ env.BACKEND_CRON_ID }} || true

      - name: "Restart nginx"
        run: docker restart nginx
