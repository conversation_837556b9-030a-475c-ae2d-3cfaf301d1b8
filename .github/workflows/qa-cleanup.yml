name: Cleanup QA environment

on:
  workflow_dispatch:
    inputs:
      deployment_url:
        description: 'URL'
        required: true
        type: string
      triggered_by:
        description: 'How this workflow was triggered'
        required: false
        type: string
        default: 'manual'
  issues:
    types: [closed]

jobs:
  prepare:
    runs-on: qa
    if: github.event_name == 'workflow_dispatch' || contains(github.event.issue.title, 'QA-Instance ready')
    outputs:
      DEPLOYMENT_BRANCH: ${{ steps.parse_url.outputs.DEPLOYMENT_BRANCH }}
      DEPLOYMENT_RUNNER: ${{ steps.parse_url.outputs.DEPLOYMENT_RUNNER }}
      DEPLOYMENT_URL: ${{ steps.determine_url.outputs.DEPLOYMENT_URL }}
      SHOULD_TRIGGER_API: ${{ steps.determine_trigger.outputs.SHOULD_TRIGGER_API }}
    steps:
      - name: Get deployment URL
        id: determine_url
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            DEPLOYMENT_URL="${{ inputs.deployment_url }}"
          else
            DEPLOYMENT_URL=$(echo "${{ github.event.issue.title }}" | awk -F'[][]' '{print $2}')
          fi

          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_ENV
          echo "DEPLOYMENT_URL=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT

      - name: Parse URL
        id: parse_url
        run: |
          DEPLOYMENT_BRANCH=$(echo $DEPLOYMENT_URL | awk -F[/:] '{print $4}' | awk -F. '{print $1}')
          echo "DEPLOYMENT_BRANCH=$DEPLOYMENT_BRANCH" >> $GITHUB_OUTPUT

          DEPLOYMENT_RUNNER=$(echo "$DEPLOYMENT_URL" | sed -n 's|.*://[^.]*\.\([^/]*\)\.qa\.teamassistant\.cz.*|\1|p')
          echo "DEPLOYMENT_RUNNER=$DEPLOYMENT_RUNNER" >> $GITHUB_OUTPUT

          # Get current branch name for comparison
          CURRENT_BRANCH=$(echo "${{ github.ref }}" | awk -F'/' '{print $3}')
          echo "CURRENT_BRANCH=$CURRENT_BRANCH" >> $GITHUB_OUTPUT

      - name: Determine trigger type
        id: determine_trigger
        run: |
          DEPLOYMENT_BRANCH="${{ steps.parse_url.outputs.DEPLOYMENT_BRANCH }}"
          CURRENT_BRANCH="${{ steps.parse_url.outputs.CURRENT_BRANCH }}"

          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ inputs.triggered_by }}" == "api_call" ]]; then
              # This is an API call, run cleanup directly
              SHOULD_TRIGGER_API="false"
            else
              # This is manual trigger, check if deployment branch matches current branch
              if [[ "$DEPLOYMENT_BRANCH" != "$CURRENT_BRANCH" ]]; then
                SHOULD_TRIGGER_API="true"
                echo "Manual trigger: deployment branch ($DEPLOYMENT_BRANCH) differs from current branch ($CURRENT_BRANCH) - will use API"
              else
                SHOULD_TRIGGER_API="false"
                echo "Manual trigger: deployment branch ($DEPLOYMENT_BRANCH) matches current branch ($CURRENT_BRANCH) - will run directly"
              fi
            fi
          else
            # This is issue closure, check if deployment branch matches current branch
            if [[ "$DEPLOYMENT_BRANCH" != "$CURRENT_BRANCH" ]]; then
              SHOULD_TRIGGER_API="true"
              echo "Issue closure: deployment branch ($DEPLOYMENT_BRANCH) differs from current branch ($CURRENT_BRANCH) - will use API"
            else
              SHOULD_TRIGGER_API="false"
              echo "Issue closure: deployment branch ($DEPLOYMENT_BRANCH) matches current branch ($CURRENT_BRANCH) - will run directly"
            fi
          fi

          echo "SHOULD_TRIGGER_API=$SHOULD_TRIGGER_API" >> $GITHUB_OUTPUT

  trigger-api:
    runs-on: neit-linux
    needs: prepare
    if: needs.prepare.outputs.SHOULD_TRIGGER_API == 'true'
    steps:
      - name: Use deployment branch
        run: |
          BRANCH_NAME="${{ needs.prepare.outputs.DEPLOYMENT_BRANCH }}"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

          if [ -z "$BRANCH_NAME" ]; then
            echo "Could not extract branch name from deployment URL"
            exit 1
          fi

      - name: Trigger cleanup on branch
        run: |
          curl -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/actions/workflows/qa-cleanup.yml/dispatches \
            -d '{
              "ref": "${{ env.BRANCH_NAME }}",
              "inputs": {
                "deployment_url": "${{ needs.prepare.outputs.DEPLOYMENT_URL }}",
                "triggered_by": "api_call"
              }
            }'

  cleanup:
    runs-on: "${{ needs.prepare.outputs.DEPLOYMENT_RUNNER }}"
    timeout-minutes: 90
    needs: prepare
    if: needs.prepare.outputs.SHOULD_TRIGGER_API == 'false'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set environment variables
        run: |
          echo "DEPLOYMENT_BRANCH=${{ needs.prepare.outputs.DEPLOYMENT_BRANCH }}" >> $GITHUB_ENV
          echo "DEPLOYMENT_RUNNER=${{ needs.prepare.outputs.DEPLOYMENT_RUNNER }}" >> $GITHUB_ENV
          echo "CONFIG_FOLDER=/home/<USER>/QAfolder/${{ needs.prepare.outputs.DEPLOYMENT_BRANCH }}" >> $GITHUB_ENV

      - name: Load deployment variables
        run: |
          FRONTEND_ID=$(grep '^FRONTEND_ID=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          BACKEND_ID=$(grep '^BACKEND_ID=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          BACKEND_CRON_ID=$(grep '^BACKEND_CRON_ID=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          SCHEMA_NAME=$(grep '^SCHEMA_NAME=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          ARANGO_USER=$(grep '^ARANGO_USER=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_CLIENT=$(grep '^DB_CLIENT=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_HOST=$(grep '^DB_HOST=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_PORT=$(grep '^DB_PORT=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)
          DB_TYPE=$(grep '^DB_TYPE=' ${{ env.CONFIG_FOLDER }}/variables.txt | cut -d '=' -f 2)

          echo "FRONTEND_ID=$FRONTEND_ID" >> $GITHUB_ENV
          echo "BACKEND_ID=$BACKEND_ID" >> $GITHUB_ENV
          echo "BACKEND_CRON_ID=$BACKEND_CRON_ID" >> $GITHUB_ENV
          echo "SCHEMA_NAME=$SCHEMA_NAME" >> $GITHUB_ENV
          echo "ARANGO_USER=$ARANGO_USER" >> $GITHUB_ENV
          echo "DB_CLIENT=$DB_CLIENT" >> $GITHUB_ENV
          echo "DB_HOST=$DB_HOST" >> $GITHUB_ENV
          echo "DB_PORT=$DB_PORT" >> $GITHUB_ENV
          echo "DB_TYPE=$DB_TYPE" >> $GITHUB_ENV

      - name: Drop MSSQL database
        if: ${{ env.DB_TYPE == 'mssql' }}
        run: |
          echo "
          USE master;
          DROP DATABASE ${{ env.SCHEMA_NAME }};
          DROP LOGIN ${{ env.SCHEMA_NAME }};
          GO
          " > ${{ env.CONFIG_FOLDER }}/mssql_drop.sql

          # Start MS SQL tools container
          CONTAINER_ID=`docker run -d --name mssql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          --add-host=mssqldb.cicd.neit.cz:$(getent hosts mssqldb.cicd.neit.cz | awk '{ print $1 }') \
          --entrypoint tail \
          mcr.microsoft.com/mssql-tools \
          -f /dev/null`

          # Copy drop script
          docker cp ${{ env.CONFIG_FOLDER }}/mssql_drop.sql $CONTAINER_ID:/tmp/mssql_drop.sql

          # Exec and remove User
          docker exec $CONTAINER_ID /opt/mssql-tools/bin/sqlcmd -S ${{ env.DB_HOST }} -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} -i /tmp/mssql_drop.sql

          # Stop and remove container
          docker stop $CONTAINER_ID && docker rm $CONTAINER_ID

      - name: Drop Arango database and user
        run: |
          ARANGO_ID=$(docker ps -q -f name=arango)
          ARANGO_CLEANUP=$(cat <<EOF
          db._dropDatabase('${{ env.ARANGO_USER }}');
          require("@arangodb/users").remove('${{ env.ARANGO_USER }}');
          EOF
          )
          echo "$ARANGO_CLEANUP" | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}"

      - name: Remove deployment folder
        run: rm -rf ${{ env.CONFIG_FOLDER }}

      - name: Remove nginx config
        run: unlink /home/<USER>/nginx-sites/${{ env.DEPLOYMENT_BRANCH }}.${{ env.DEPLOYMENT_RUNNER }}.conf

      - name: Remove containers
        run: |
          docker rm -f ${{ env.FRONTEND_ID }} || true
          docker rm -f ${{ env.BACKEND_ID }} || true
          docker rm -f ${{ env.BACKEND_CRON_ID }} || true

      - name: Restart nginx
        run: docker restart nginx
