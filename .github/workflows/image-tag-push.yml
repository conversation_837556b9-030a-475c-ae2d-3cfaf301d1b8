name: Tag image and push to repository

on:
  push:
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag version'
        required: false
      chosen-os:
        description: ''
        required: false
        type: choice
        options:
          - builder
          - gh1.int
          - gh2.int
          - gh3.int
          - gh4.int
          - gh1.h3
          - gh1.h7
          - d1.h5
      run_tests:
        description: 'Run tests before tag and push?'
        required: false
        default: 'true'
        type: choice
        options:
          - 'true'
          - 'false'
      used-dockerfile:
        required: true
        type: choice
        default: Dockerfile
        options:
          - Dockerfile
          - Dockerfile-Debian

concurrency:
  group: ${{ github.workflow }}-tag-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  check-branch:
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    outputs:
      on_stable_or_dev_branch: ${{ steps.check_branch.outputs.on_stable_or_dev_branch }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: Check if Tag is from a Stable or Dev Branch
        id: check_branch
        run: |
          onStableOrDevBranch=false
          branches=$(git branch -r --contains ${{ github.sha }} | sed 's/ *origin\///')
          for branch in $branches; do
            echo "Checking branch: $branch"
            if [[ $branch == stable/* || $branch == dev/* ]]; then
              onStableOrDevBranch=true
              echo "Tag is from a stable or dev branch."
              echo "on_stable_or_dev_branch=true" >> $GITHUB_OUTPUT
              break
            fi
          done
          if [ "$onStableOrDevBranch" == "false" ]; then
            echo "Tag is not on a stable or dev branch."
            echo "on_stable_or_dev_branch=false" >> $GITHUB_OUTPUT
          fi
        shell: bash

  trigger-backend-tests:
    if: >
      needs.check-branch.outputs.on_stable_or_dev_branch == 'true' && (github.event_name != 'workflow_dispatch' || (github.event.inputs.run_tests != 'false'))
    needs: [check-branch]
    uses: ./.github/workflows/backend-tests.yml
    with:
      run_tests: ${{ github.event.inputs.run_tests }}
      used-dockerfile: ${{ github.event.inputs.used-dockerfile || 'Dockerfile' }}
    secrets: inherit

  trigger-frontend-tests:
    if: >
      needs.check-branch.outputs.on_stable_or_dev_branch == 'true' && (github.event_name != 'workflow_dispatch' || (github.event.inputs.run_tests != 'false'))
    needs: [check-branch]
    uses: ./.github/workflows/frontend-tests.yml
    with:
      run_tests: ${{ github.event.inputs.run_tests }}
      used-dockerfile: ${{ github.event.inputs.used-dockerfile || 'Dockerfile' }}
    secrets: inherit

  backend-tag-image-push:
    needs: [trigger-backend-tests, trigger-frontend-tests]
    if: ${{ always() && needs.check-branch.outputs.on_stable_or_dev_branch == 'true' || github.event_name == 'workflow_dispatch' && !failure() && !cancelled() }}
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    timeout-minutes: 90
    outputs:
      backend_tag: ${{ steps.backend_tag.outputs.backend_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: Set image tag
        id: backend_tag
        run: |
          TAG_PREFIX="${{ vars.TAS_REGISTRY_URL }}/tas"
          SHORT_COMMIT_HASH=$(git rev-parse --short HEAD)
          TIMESTAMP=$(date +"%y%m%d%H%M")
          TAG_SUFFIX="backend"

          if [[ $GITHUB_REF == refs/tags/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/tags/"}
          elif [[ $GITHUB_REF == refs/heads/stable/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/stable/"}
          elif [[ $GITHUB_REF == refs/heads/dev/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/dev/"}
          elif [[ $GITHUB_EVENT_NAME == "workflow_dispatch" ]]; then
              TAG_NAME="${{ github.event.inputs.tag_name }}"
          fi

          TAG_NAME_CLEANED=$(echo "$TAG_NAME" | sed -E "s/'//g; s/[^a-zA-Z0-9\.\-]/-/g")
          IMAGE_TAG="${TAG_NAME_CLEANED}-${SHORT_COMMIT_HASH}-${TIMESTAMP}"
          TAG="${TAG_PREFIX}/${TAG_SUFFIX}:${IMAGE_TAG}"
          echo "TAG_NAME_CLEANED=${TAG_NAME_CLEANED}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "TAG=$TAG" >> $GITHUB_ENV
          echo "backend_tag=$TAG" >> $GITHUB_OUTPUT
        shell: bash
      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.TAS_REGISTRY_USERNAME }}
          password: ${{ secrets.TAS_REGISTRY_PASSWORD }}
          registry: ${{ vars.TAS_REGISTRY_URL }}
          logout: false
      - name: Create build.json
        run: |
          echo '{' > backend/config/build.json
          echo '  "version": "'"${{ env.TAG_NAME_CLEANED }}"'",' >> backend/config/build.json
          echo '  "build_tag": "'"${{ env.IMAGE_TAG }}"'",' >> backend/config/build.json
          echo '  "github_sha": "'"${{ github.sha }}"'",' >> backend/config/build.json
          echo '  "image": "backend"' >> backend/config/build.json
          echo '}' >> backend/config/build.json

      - name: upload tas-license public key
        run: |
          echo "${{ secrets.CICD_TAS_LICENSE_PUBLIC_KEY }}" | sed 's/\\n/\n/g' > backend/config/key/featureFlag/publickey.crt

      - name: find all .crt files and generate their SHA256
        run: |
          for cert_file in backend/config/key/featureFlag/*.crt; do
            if [ -f "$cert_file" ]; then
              cert_sha=$(sed ':a;/\S/!{$d;N;ba}' "$cert_file" | sed -z 's/\n$//' | sha256sum | awk '{print $1}')
              sed -i "/^[[:blank:]]*export[[:blank:]]\+const[[:blank:]]\+controls[[:blank:]]\+=[[:blank:]]\+\[/a \    \"$cert_sha\"," backend/src/service/secretStore/featureFlag/control.ts
            fi
          done

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          target: prod_obfuscated
          context: backend
          file: backend/${{ inputs.used-dockerfile || 'Dockerfile' }}
          push: false
          no-cache: true
          tags: ${{ env.TAG }}
      - name: Push the Image to registry
        run: |
          docker push "${{ env.TAG }}"
          echo "Pushed image $TAG"
        env:
          TAG: ${{ env.TAG }}

  frontend-tag-image-push:
    needs: [trigger-frontend-tests, trigger-backend-tests]
    if: ${{ always() && needs.check-branch.outputs.on_stable_or_dev_branch == 'true' || github.event_name == 'workflow_dispatch' && !failure() && !cancelled() }}
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    timeout-minutes: 90
    outputs:
      frontend_tag: ${{ steps.frontend-tag.outputs.frontend_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: Set image tag
        id: frontend-tag
        run: |
          TAG_PREFIX="${{ vars.TAS_REGISTRY_URL }}/tas"
          SHORT_COMMIT_HASH=$(git rev-parse --short HEAD)
          TIMESTAMP=$(date +"%y%m%d%H%M")
          TAG_SUFFIX="frontend"

          if [[ $GITHUB_REF == refs/tags/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/tags/"}
          elif [[ $GITHUB_REF == refs/heads/stable/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/stable/"}
          elif [[ $GITHUB_REF == refs/heads/dev/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/dev/"}
          elif [[ $GITHUB_EVENT_NAME == "workflow_dispatch" ]]; then
              TAG_NAME="${{ github.event.inputs.tag_name }}"
          fi

          TAG_NAME_CLEANED=$(echo "$TAG_NAME" | sed -E "s/'//g; s/[^a-zA-Z0-9\.\-]/-/g")
          IMAGE_TAG="${TAG_NAME_CLEANED}-${SHORT_COMMIT_HASH}-${TIMESTAMP}"
          TAG="${TAG_PREFIX}/${TAG_SUFFIX}:${IMAGE_TAG}"
          echo "TAG_NAME_CLEANED=${TAG_NAME_CLEANED}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "TAG=$TAG" >> $GITHUB_ENV
          echo "frontend_tag=$TAG" >> $GITHUB_OUTPUT
        shell: bash
      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.TAS_REGISTRY_USERNAME }}
          password: ${{ secrets.TAS_REGISTRY_PASSWORD }}
          registry: ${{ vars.TAS_REGISTRY_URL }}
          logout: false
      - name: Update tasVersion
        run: |
          sed -i "s/tasVersion:[[:space:]]*['\"][^'\"]*['\"]*/tasVersion: \'${{ env.TAG_NAME_CLEANED }}\'/" frontend/src/components5.0/zustand/loggedUserStore.ts
          echo "Verifying tasVersion update:"
          grep "tasVersion:" frontend/src/components5.0/zustand/loggedUserStore.ts
      - name: Create build.json
        run: |
          echo '{' > frontend/config/build.json
          echo '  "version": "'"${{ env.TAG_NAME_CLEANED }}"'",' >> frontend/config/build.json
          echo '  "build_tag": "'"${{ env.IMAGE_TAG }}"'",' >> frontend/config/build.json
          echo '  "github_sha": "'"${{ github.sha }}"'",' >> frontend/config/build.json
          echo '  "image": "frontend"' >> frontend/config/build.json
          echo '}' >> frontend/config/build.json
      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          target: prod_obfuscated
          context: frontend
          file: frontend/${{ inputs.used-dockerfile || 'Dockerfile' }}
          push: false
          no-cache: true
          tags: ${{ env.TAG }}
      - name: Push the Image to registry
        run: |
          docker push "${{ env.TAG }}"
          echo "Pushed image $TAG"
        env:
          TAG: ${{ env.TAG }}

  publish-changelog-to-slack:
    needs: [frontend-tag-image-push, backend-tag-image-push]
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    timeout-minutes: 90
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: Get image version tag
        run: |
          TAG_NAME="unspecified"

          if [[ $GITHUB_REF == refs/tags/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/tags/"}
          elif [[ $GITHUB_REF == refs/heads/stable/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/stable/"}
              TAG_NAME=$(echo "$TAG_NAME" | sed 's/\//-/g')
          elif [[ $GITHUB_REF == refs/heads/dev/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/dev/"}
              TAG_NAME=$(echo "$TAG_NAME" | sed 's/\//-/g')
          elif [[ $GITHUB_EVENT_NAME == "workflow_dispatch" ]]; then
              TAG_NAME="${{ github.event.inputs.tag_name }}"
          fi

          echo "TAG_NAME=${TAG_NAME}" >> $GITHUB_ENV
        shell: bash
      - name: Publish changelog
        run: |
          TAS_CHANGELOG=$(sed -n '/^## \[${{ env.TAG_NAME }}]/,/^## \[/p' backend/changelog.md | head -n -1 | head -100 | sed -e '${/^$/d;}' -e 's/$/\\n/g' -e 's/"/\\"/g' | tr -d '\n';)

          if [[ "${TAS_CHANGELOG}" != "" ]]; then
            curl -v -X POST -H "Content-Type: application/json" https://hooks.slack.com/services/${{ secrets.CICD_SLACK_CHANGELOG_NOTIFICATION_HOOK }} --data "{\"blocks\":[{\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\"Completed build of new TAS image for version ${{ env.TAG_NAME }}\"}},{\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\"\`\`\`${TAS_CHANGELOG}\`\`\`\"}}]}" || true
          else
            echo "${TAS_CHANGELOG}"
          fi
        shell: bash

  publish-changelog-to-email:
    needs: [frontend-tag-image-push, backend-tag-image-push]
    runs-on: ['neit-linux', "${{ inputs.chosen-os || 'builder' }}"]
    timeout-minutes: 90
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: Get image version tag
        run: |
          TAG_NAME="unspecified"

          if [[ $GITHUB_REF == refs/tags/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/tags/"}
          elif [[ $GITHUB_REF == refs/heads/stable/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/stable/"}
              TAG_NAME=$(echo "$TAG_NAME" | sed 's/\//-/g')
          elif [[ $GITHUB_REF == refs/heads/dev/* ]]; then
              TAG_NAME=${GITHUB_REF#"refs/heads/dev/"}
              TAG_NAME=$(echo "$TAG_NAME" | sed 's/\//-/g')
          elif [[ $GITHUB_EVENT_NAME == "workflow_dispatch" ]]; then
              TAG_NAME="${{ github.event.inputs.tag_name }}"
          fi

          echo "TAG_NAME=${TAG_NAME}" >> $GITHUB_ENV
        shell: bash
      - name: Publish changelog using e-mail
        run: |
          FRONTEND_TAG=${{needs.frontend-tag-image-push.outputs.frontend_tag}}
          BACKEND_TAG=${{needs.backend-tag-image-push.outputs.backend_tag}}

          TAS_CHANGELOG=$(sed -n '/^## \[${{ env.TAG_NAME }}]/,/^## \[/p' backend/changelog.md | head -n -1 | head -100 | sed -e '${/^$/d;}' -e 's/$/\\n/g' -e 's/"/\\"/g' | tr -d '\n';)
          
          NOTIFICATION_JSON="{\"data\": { \"changelog\": \"${TAS_CHANGELOG}\", \"images\": { \"frontend\": \"${FRONTEND_TAG}\", \"backend\": \"${BACKEND_TAG}\" }}}"

          (
            echo -e "From: ${{ vars.CICD_SMTP_NOTIFICATION_CHANGELOG_SENDER }}\nTo: ${{ vars.CICD_SMTP_NOTIFICATION_CHANGELOG_RECIPIENT }}\nSubject: TAS release announcement\n\n"
            echo "${NOTIFICATION_JSON}"
          ) \
            | curl -s --url '${{ vars.CICD_SMTP_NOTIFICATION_URL }}' --mail-from '${{ vars.CICD_SMTP_NOTIFICATION_CHANGELOG_SENDER }}' --mail-rcpt '${{ vars.CICD_SMTP_NOTIFICATION_CHANGELOG_RECIPIENT }}' -u ${{ vars.CICD_SMTP_NOTIFICATION_USERNAME }}:${{ secrets.CICD_SMTP_NOTIFICATION_PASSWORD }} --upload-file - \
            || true
        shell: bash
