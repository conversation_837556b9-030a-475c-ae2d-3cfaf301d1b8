name: Deploy QA environment

on:
  workflow_dispatch:
    inputs:
      runner:
        description: 'Specify runner'
        type: choice
        required: true
        options:
          - qa
          - gh1.h3
          - d1.h5
      database:
        description: 'Specify database'
        type: choice
        required: true
        options:
          - mssql

concurrency:
  group: ${{ github.workflow}}-deployment-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  SCHEMA_NAME: TAS_${{ github.run_id }}_${{ github.run_number }}_${{ github.run_attempt }}
  ARANGO_USER: TAS_${{ github.run_id }}_${{ github.run_number }}_${{ github.run_attempt }}
  FRONTEND_QA_IMAGE_NAME: action-qa-tas-front
  BACKEND_QA_IMAGE_NAME: action-qa-tas-back

jobs:
  Build-images:
    runs-on: ${{ github.event.inputs.runner }}
    timeout-minutes: 90

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set DB variables
        run: |
          if [ "${{ github.event.inputs.database }}" == "mssql" ]; then
            echo "DB_CLIENT=mssql" >> $GITHUB_ENV
            echo "DB_HOST=mssqldb.cicd.neit.cz" >> $GITHUB_ENV
            echo "DB_PORT=1433" >> $GITHUB_ENV
          else
            echo "Wrong DB Type" >&2
            exit 1
          fi

      - name: Generate encryption key
        run: |
          ENCRYPTION_KEY=$(hexdump -vn32 -e'8/4 "%08X" 1 "\n"' /dev/urandom)
          echo "ENCRYPTION_KEY=${ENCRYPTION_KEY}" >> $GITHUB_ENV
          echo "Generated database encryption key"

      - name: Extract branch name and runner
        id: extract_branch
        run: |
          # Use shell variables for same-step calculations
          BRANCH_NAME=$(echo "${{ github.ref }}" | awk -F'/' '{print $3}' | awk '{gsub(/[^a-zA-Z0-9]/,"-")}1' | sed 's/-$//')
          CONTAINER_BASE="qa-${{ github.run_number }}-${BRANCH_NAME}"
          TAS_BACKEND_NAME="${{ env.BACKEND_QA_IMAGE_NAME }}-${CONTAINER_BASE}"
          TAS_BACKEND_CRON_NAME="${TAS_BACKEND_NAME}-cron"
          TAS_FRONTEND_NAME="${{ env.FRONTEND_QA_IMAGE_NAME }}-${CONTAINER_BASE}"
          
          # Export to GITHUB_ENV
          echo "BRANCH_NAME=${BRANCH_NAME}" >> $GITHUB_ENV
          echo "RUNNER_NAME_SANITIZED=$(echo "$RUNNER_NAME" | cut -d'@' -f2)" >> $GITHUB_ENV
          echo "HASH=$(git rev-parse HEAD)" >> $GITHUB_ENV
          echo "CONTAINER_BASE=${CONTAINER_BASE}" >> $GITHUB_ENV
          echo "TAS_BACKEND_NAME=${TAS_BACKEND_NAME}" >> $GITHUB_ENV
          echo "TAS_BACKEND_CRON_NAME=${TAS_BACKEND_CRON_NAME}" >> $GITHUB_ENV
          echo "TAS_FRONTEND_NAME=${TAS_FRONTEND_NAME}" >> $GITHUB_ENV

      - name: Check for duplicity
        run: |
          config="/home/<USER>/nginx-sites/${{ env.BRANCH_NAME }}.${{ env.RUNNER_NAME_SANITIZED }}.conf"
          if [ -f "$config" ]; then
            echo "Configuration file for this branch already exists."
            exit 0
          fi

      - name: Set URL
        id: set_url
        run: |
          DEPLOYMENT_DOMAIN="${{ env.BRANCH_NAME }}.${{ env.RUNNER_NAME_SANITIZED }}.qa.teamassistant.cz"
          echo "DEPLOYMENT_DOMAIN=${DEPLOYMENT_DOMAIN}" >> $GITHUB_ENV
          echo "Deployment domain: $DEPLOYMENT_DOMAIN"
          URL="https://${{ env.BRANCH_NAME }}.${{ env.RUNNER_NAME_SANITIZED }}.qa.teamassistant.cz"
          echo "DEPLOYMENT_URL=${URL}" >> $GITHUB_ENV
          echo "Deployment URL: $DEPLOYMENT_URL"
          echo "CONFIG_FOLDER=/home/<USER>/QAfolder/${{ env.BRANCH_NAME }}" >> $GITHUB_ENV

      # Prepare Docker buildx to use caching
      - name: Prepare Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker

      - name: Build frontend image
        uses: docker/build-push-action@v5
        with:
          target: prod
          context: frontend
          push: false
          tags: ${{ env.FRONTEND_QA_IMAGE_NAME }}:${{ github.sha }}
          load: true

      # tas license support
      - name: upload tas-license public key
        run: |
          echo "${{ secrets.CICD_TAS_LICENSE_PUBLIC_KEY }}" | sed 's/\\n/\n/g' > backend/config/key/featureFlag/publickey.crt

      - name: find all .crt files and generate their SHA256
        run: |
          for cert_file in backend/config/key/featureFlag/*.crt; do
            if [ -f "$cert_file" ]; then
              cert_sha=$(sed ':a;/\S/!{$d;N;ba}' "$cert_file" | sed -z 's/\n$//' | sha256sum | awk '{print $1}')
              sed -i "/^[[:blank:]]*export[[:blank:]]\+const[[:blank:]]\+controls[[:blank:]]\+=[[:blank:]]\+\[/a \    \"$cert_sha\"," backend/src/service/secretStore/featureFlag/control.ts
            fi
          done

      # Build backend and image
      - name: Build backend image
        uses: docker/build-push-action@v5
        with:
          target: prod
          context: backend
          push: false
          tags: ${{ env.BACKEND_QA_IMAGE_NAME }}:${{ github.sha }}
          load: true

      # Randomize ports for backend,ldap and sftp containers for parallel test run
      - name: Randomize ports
        run: |
          echo "BACKEND_PORT=$(echo $(( 8001 + $GITHUB_RUN_NUMBER % 1000 )))" >> $GITHUB_ENV
          echo "FRONTEND_PORT=$(echo $(( 7000 + $GITHUB_RUN_NUMBER % 1000 )))" >> $GITHUB_ENV

      # Generate hostname for DB IP
      - name: Generate hostname for db
        run: |
          IP_ADD=$(getent hosts ${{ env.DB_HOST }} | awk '{ print $1 }')
          echo "IP_ADD=$IP_ADD" >> $GITHUB_ENV

      - name: Create folder for environment
        run: |
          mkdir -p ${{ env.CONFIG_FOLDER }}

      - name: Generate DB user pw
        id: generate-vars
        run: |
          DB_LOWER=$(tr -dc 'a-z' </dev/urandom | head -c 1)
          DB_UPPER=$(tr -dc 'A-Z' </dev/urandom | head -c 1)
          DB_DIGIT=$(tr -dc '0-9' </dev/urandom | head -c 1)
          DB_SPECIAL=$(tr -dc '!#%' </dev/urandom | head -c 1)
          DB_REST=$(tr -dc 'A-Za-z0-9!#%' </dev/urandom | head -c 9)
          SCHEMA_PW=$(echo "${DB_LOWER}${DB_UPPER}${DB_DIGIT}${DB_SPECIAL}${DB_REST}" | fold -w1 | shuf | tr -d '\n')
          echo "SCHEMA_PW=$SCHEMA_PW" >> $GITHUB_ENV
          ARANGO_PW=$(tr -dc 'A-Za-z0-9_' 2>/dev/null </dev/urandom | head -c 13 2>/dev/null)
          echo "ARANGO_PW=$ARANGO_PW" >> $GITHUB_ENV
          ADMIN_PW=$(tr -dc 'A-Za-z0-9_' 2>/dev/null </dev/urandom | head -c 12 2>/dev/null)
          RANDOM_NUMBER=$(tr -dc '0-9' 2>/dev/null </dev/urandom | head -c 1)
          ADMIN_PW=$(echo "$ADMIN_PW$RANDOM_NUMBER" | fold -w1 | shuf | tr -d '\n')
          echo "ADMIN_PW=$ADMIN_PW" >> $GITHUB_ENV

      # Create MS SQL tools container
      - name: Create MS SQL tools container
        if: ${{ env.DB_HOST }} == 'mssql'
        run: |
          MSSQL_TOOLS_CONTAINER_ID=`docker run -d --name mssql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          --add-host=mssqldb.cicd.neit.cz:$(getent hosts mssqldb.cicd.neit.cz | awk '{ print $1 }') \
          --entrypoint tail \
          mcr.microsoft.com/mssql-tools \
          -f /dev/null`
          echo "MSSQL_TOOLS_CONTAINER_ID=$MSSQL_TOOLS_CONTAINER_ID" >> $GITHUB_ENV

      # RUN MSSQL SCHEMA CREATION AND MSSQL-TOOLS CONTAINER SPIN UP
      - name: Prepare user creation and grants with schema for mssql database
        if: ${{ env.DB_CLIENT }} == 'mssql'
        run: |
          echo "
          CREATE LOGIN ${{ env.SCHEMA_NAME }} WITH PASSWORD='${{ env.SCHEMA_PW }}';
          GO
          CREATE DATABASE ${{ env.SCHEMA_NAME }} COLLATE Czech_100_CI_AS;
          GO
          USE ${{ env.SCHEMA_NAME }};
          GO
          CREATE USER ${{ env.SCHEMA_NAME }} FOR LOGIN ${{ env.SCHEMA_NAME }};
          GO
          ALTER ROLE [db_owner] ADD MEMBER ${{ env.SCHEMA_NAME }};
          GO
          CREATE SCHEMA tas AUTHORIZATION ${{ env.SCHEMA_NAME }};
          GO
          ALTER USER ${{ env.SCHEMA_NAME }} WITH DEFAULT_SCHEMA = tas;
          GO
          GRANT CREATE VIEW TO ${{ env.SCHEMA_NAME }};
          GRANT CREATE TABLE TO ${{ env.SCHEMA_NAME }};
          GO
          ALTER DATABASE [${{ env.SCHEMA_NAME }}] SET READ_COMMITTED_SNAPSHOT ON;
          GO
          " > ${{ env.CONFIG_FOLDER }}/mssql_schema.sql

          docker cp ${{ env.CONFIG_FOLDER }}/mssql_schema.sql ${{ env.MSSQL_TOOLS_CONTAINER_ID }}:/tmp/mssql_schema.sql
          docker exec ${{ env.MSSQL_TOOLS_CONTAINER_ID }} /opt/mssql-tools/bin/sqlcmd -S ${{ env.DB_HOST }} -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} -i /tmp/mssql_schema.sql

      # Generate and fill backend config for mssql connection
      - name: Generate backend config for mssql connection
        if: ${{ env.DB_CLIENT }} == 'mssql'
        run: |
          echo "
          module.exports = {
              port: ${{ env.BACKEND_PORT }},
              prefix: null,
              hostname: '${{ env.DEPLOYMENT_URL }}/api',
              connections: {
                  knexConnection: {
                      host: '${{ env.DB_HOST }}',
                      port: ${{ env.DB_PORT }},
                      user: '${{ env.SCHEMA_NAME }}',
                      password: '${{ env.SCHEMA_PW }}',
                      database: '${{ env.SCHEMA_NAME }}',
                      schema: 'tas',
                      requestTimeout: 130000,
                      options: {
                          idleTimeoutMillis: 130000,
                          requestTimeout: 300000,
                      },
                  },
              },
              db: {
                  client: '${{ env.DB_CLIENT }}',
                  maxPoolConnections: 40,
              },
              langs: ['cs', 'en', 'de'],
              dms: {
                  tikaUrl: 'http://tika:9998', // url to tika
                  elasticUrl: 'http://elasticsearch:9200', // url to elasticSearch, turnoff with fulltext
                  elasticVersion: 7,
                  storagePath: './_storage', // path where DMS files will be stored
              },
              security: {
                  saltRounds: 2, // change this number depending on the server performance
                  pepper: '4enqL99M7Cz/cGlM3Dy/eCUVs', // random secret 25-char string
                  authTokenSecret: 'EMFtBuWsup',
                  refreshTokenSecret: 'L5duAhH5Yp',
              },
              frontendUrl: '${{ env.DEPLOYMENT_URL }}',
              redis: {
                  host: 'redis',
                  port: 6379,
              },
              logger: {
                  arango: {
                      host: 'http://arango:8529', // Arango host:port (http://127.0.0.1:8529). Use null to disable arango logger. Fallback file will be used.
                      db_name: '${{ env.ARANGO_USER }}_db', // Database name, use something like '{instance_id}_db'.
                      auth: { // Use 'auth: null' to turn off. Only simple (login/pass) authentication allowed.
                          user: '${{ env.ARANGO_USER }}',
                          pass: '${{ env.ARANGO_PW }}',
                      },
                  },
              },
               paths: {
                tmp: '/tmp',
                userPhotoPath: '/tmp',
              }
          };" > ${{ env.CONFIG_FOLDER }}/local.js

      # Generate and fill frontend config - tas.js
      - name: Generate frontend config tas.js
        run: |
          echo "
          var config = require('./tas.base.js');

          config.backendUrl = '${{ env.DEPLOYMENT_URL }}/api';
          config.useSsl = false;
          config.port = ${{ env.FRONTEND_PORT }}; // listening on port
          config.webpackPort = 8888;
          config.socketNotifs = false;

          module.exports = config;
          " > ${{ env.CONFIG_FOLDER }}/tas.js

      # Prepare Arango commands and user creation
      - name: Prepare and execute arango commands
        run: |
          # Grab container ID
          ARANGO_ID=$(docker ps -q -f name=arango)
          # Prepare arango commands
          ARANGO_COMMANDS=$(cat <<EOF
          var users = require('@arangodb/users');
          users.save('${{ env.ARANGO_USER }}', '${{ env.ARANGO_PW }}');
          db._createDatabase('${{ env.ARANGO_USER }}_db');
          users.grantDatabase('${{ env.ARANGO_USER }}', '${{ env.ARANGO_USER }}_db', 'rw');
          users.grantCollection('${{ env.ARANGO_USER }}', '${{ env.ARANGO_USER }}_db', '*', 'rw');
          EOF
          )
          # Execute arango commands
          echo "$ARANGO_COMMANDS" | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}"

      - name: Create network
        run: |
          docker network create -d bridge QA-TAS || true

      - name: Run migrations
        run: |
          docker run \
          --name ${{ env.TAS_BACKEND_NAME }} \
          --network QA-TAS \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=elasticsearch:host-gateway \
          --add-host=tika:host-gateway \
          --add-host=${{ env.DB_HOST }}:${{ env.IP_ADD }} \
          -p ${{ env.BACKEND_PORT }}:${{ env.BACKEND_PORT }} \
          -v ${{ env.CONFIG_FOLDER }}/local.js:/app/tas/backend/config/config/local.js \
          -e TAS_DATABASE_ENCRYPTION_KEY=${{ env.ENCRYPTION_KEY }} \
          ${{ env.BACKEND_QA_IMAGE_NAME }}:${{ github.sha }} \
          node dist/migrate.js init

      - name: Remove the container
        run: |
          docker container rm ${{ env.TAS_BACKEND_NAME }}

      - name: Start backend container
        run: |
          docker run -d \
          --name ${{ env.TAS_BACKEND_NAME }} \
          --network QA-TAS \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=elasticsearch:host-gateway \
          --add-host=tika:host-gateway \
          --add-host=${{ env.DB_HOST }}:${{ env.IP_ADD }} \
          -p ${{ env.BACKEND_PORT }}:${{ env.BACKEND_PORT }} \
          -v ${{ env.CONFIG_FOLDER }}/local.js:/app/tas/backend/config/config/local.js \
          -e TAS_DATABASE_ENCRYPTION_KEY=${{ env.ENCRYPTION_KEY }} \
          ${{ env.BACKEND_QA_IMAGE_NAME }}:${{ github.sha }}

      - name: Start frontend container
        run: |
          docker run -d \
          --network QA-TAS \
          --name ${{ env.TAS_FRONTEND_NAME }} \
          -v ${{ env.CONFIG_FOLDER }}/tas.js:/app/tas/frontend/config/tas.js \
          -p ${{ env.FRONTEND_PORT }}:${{ env.FRONTEND_PORT }} \
          ${{ env.FRONTEND_QA_IMAGE_NAME }}:${{ github.sha }}

      - name: Start backend cron container
        run: |
          docker run -d \
          --name ${{ env.TAS_BACKEND_CRON_NAME }} \
          --network QA-TAS \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=elasticsearch:host-gateway \
          --add-host=tika:host-gateway \
          --add-host=${{ env.DB_HOST }}:${{ env.IP_ADD }} \
          -v ${{ env.CONFIG_FOLDER }}/local.js:/app/tas/backend/config/config/local.js \
          -e TAS_DATABASE_ENCRYPTION_KEY=${{ env.ENCRYPTION_KEY }} \
          ${{ env.BACKEND_QA_IMAGE_NAME }}:${{ github.sha }} \
          npm run start:cron

      - name: Prepare configuration for nginx
        run: |
          echo "
          server {
              listen 80;
              server_name ${{ env.DEPLOYMENT_DOMAIN }};

              location / {
                  proxy_set_header Host \$host;
                  proxy_set_header X-Real-IP \$remote_addr;
                  proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
                  proxy_pass http://${{ env.TAS_FRONTEND_NAME }}:${{ env.FRONTEND_PORT }};
                  proxy_connect_timeout       900;
                  proxy_send_timeout          900;
                  proxy_read_timeout          900;
                  send_timeout                900;
                  fastcgi_read_timeout        900;
              }
              location /api/ {
                  proxy_redirect     off;
                  proxy_set_header   Host \$host;
                  proxy_set_header   X-Real-IP \$remote_addr;
                  proxy_set_header   X-Forwarded-For \$proxy_add_x_forwarded_for;
                  proxy_set_header   X-Forwarded-Host \$server_name;
                  proxy_pass http://${{ env.TAS_BACKEND_NAME }}:${{ env.BACKEND_PORT }}/;
                  proxy_connect_timeout       900;
                  proxy_send_timeout          900;
                  proxy_read_timeout          900;
                  send_timeout                900;
                  fastcgi_read_timeout        900;
              }
          }
          " > /home/<USER>/nginx-sites/${{ env.BRANCH_NAME }}.${{ env.RUNNER_NAME_SANITIZED }}.conf

      - name: Set artifact for variables
        run: |
          echo "FRONTEND_ID=$(docker ps -q -f name=${{ env.TAS_FRONTEND_NAME }})" > ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "BACKEND_ID=$(docker ps -q -f name=${{ env.TAS_BACKEND_NAME }})" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "BACKEND_CRON_ID=$(docker ps -q -f name=${{ env.TAS_BACKEND_CRON_NAME }})" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "SCHEMA_NAME=${{ env.SCHEMA_NAME }}" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "ARANGO_USER=${{ env.ARANGO_USER }}" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "DB_CLIENT=${{ env.DB_CLIENT }}" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "DB_HOST=${{ env.DB_HOST }}" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "DB_PORT=${{ env.DB_PORT }}" >> ${{ env.CONFIG_FOLDER }}/variables.txt
          echo "DB_TYPE=${{ github.event.inputs.database }}" >> ${{ env.CONFIG_FOLDER }}/variables.txt

      - name: Change TAS password in MS SQL
        if: ${{ env.DB_HOST }} == 'mssql'
        run: |
          TAS_MD=$(echo -n "${{ env.ADMIN_PW }}" | md5sum | cut -d ' ' -f 1)
          TAS_SUBSCHEMA=.TAS
          echo "
          UPDATE ${{ env.SCHEMA_NAME }}$TAS_SUBSCHEMA.USERS SET user_password='$TAS_MD' WHERE user_name='ADMIN';
          GO
          " >admin_pw.sql

          docker cp admin_pw.sql ${{ env.MSSQL_TOOLS_CONTAINER_ID }}:/admin_pw.sql
          docker exec ${{ env.MSSQL_TOOLS_CONTAINER_ID }} /opt/mssql-tools/bin/sqlcmd -S ${{ env.DB_HOST }} -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} -i admin_pw.sql

      - name: Remove MS SQL tools container
        if: ${{ env.DB_HOST }} == 'mssql'
        run: |
          # Stop and remove mssql-tools container
          docker stop ${{ env.MSSQL_TOOLS_CONTAINER_ID }} && docker rm ${{ env.MSSQL_TOOLS_CONTAINER_ID }} || true

      - name: "Restart nginx"
        run: docker restart nginx

      - name: create license
        run: |
          FUTURE_DATE=$(date -u -d "+25 days" +"%Y-%m-%dT%H:%M:%S.%3NZ")

          # Request license token
          LICENSE_RESPONSE=$(curl -s -X 'POST' \
            'https://dev.license.teamassistant.app/license' \
            -H 'accept: application/json' \
            -H 'authorization: ${{ secrets.CICD_TAS_LICENSE_AUTH_KEY }}' \
            -H 'Content-Type: application/json' \
            -d '{
            "hostname": "${{ env.DEPLOYMENT_URL }}/api",
            "content": {
              "template": true
            },
            "validTo": "'$FUTURE_DATE'"
          }')

          # Extract and validate token
          LICENSE_TOKEN=$(echo "$LICENSE_RESPONSE" | jq -r '.item.token // empty')
          if [ -z "$LICENSE_TOKEN" ]; then
            echo "Error: Failed to extract LICENSE_TOKEN from response:"
            printf '%s\\n' "$LICENSE_RESPONSE"
            exit 1
          fi

          echo "LICENSE_TOKEN=$LICENSE_TOKEN" >> $GITHUB_ENV
        shell: bash

      - name: Wait for backend service
        # Waits for the backend API to return HTTP 200 OK
        run: |
          max_retries=30
          interval=2
          endpoint="${{ env.DEPLOYMENT_URL }}/api/status"
          backend_ready=false

          echo "Checking backend status at $endpoint (max $max_retries attempts, $interval sec interval)..."
          for i in $(seq 1 $max_retries); do
            http_status=$(curl -s -o /dev/null -w '%{http_code}' --max-time 5 "$endpoint")
            if [ "$http_status" -eq 200 ]; then
              echo "Backend is ready (HTTP 200 OK)."
              backend_ready=true
              break
            fi
            echo "Attempt $i/$max_retries: Backend not ready (HTTP $http_status). Waiting $interval seconds..."
            sleep $interval
          done

          if [ "$backend_ready" != "true" ]; then
            echo "Error: Backend did not become ready after $max_retries attempts."
            exit 1
          fi
        shell: bash

      - name: Get TAS access token
        run: |
          AUTH_RESPONSE=$(curl -s '${{ env.DEPLOYMENT_URL }}/api/authenticate' \
            --data-raw 'username=admin&password=${{ env.ADMIN_PW }}&grant_type=password&scope=&auth_id=1')
          
          TAS_ACCESS_TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.access_token // empty')

          if [ -z "$TAS_ACCESS_TOKEN" ]; then
            echo "Error: Failed to get TAS_ACCESS_TOKEN from response:"
            printf '%s\\n' "$AUTH_RESPONSE"
            exit 1
          fi

          echo "TAS_ACCESS_TOKEN=$TAS_ACCESS_TOKEN" >> $GITHUB_ENV
        shell: bash

      - name: activate license
        run: |
          JSON_PAYLOAD=$(jq -n --arg name "$SCHEMA_NAME" --arg key "$LICENSE_TOKEN" \
            '{name: $name, licenseKey: $key}')

          curl '${{ env.DEPLOYMENT_URL }}/api/license' \
            -H 'Content-Type: application/json' \
            -H "Cookie: accessTokenV2=Bearer%20${TAS_ACCESS_TOKEN}" \
            --data "$JSON_PAYLOAD"
        shell: bash

      - name: Create issue
        run: |
          curl -X POST -sSL \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/$GITHUB_REPOSITORY/issues \
            -d '{"title":"QA-Instance ready ['"${{ env.DEPLOYMENT_URL }}"']","body":"URL: '"${{ env.DEPLOYMENT_URL }}"'\nBranch: '${{ env.BRANCH_NAME }}'\nCommit: '${{ env.HASH }}'\nPassword: ${{ env.ADMIN_PW }}","assignees":["'"${{ github.actor }}"'"]}'
