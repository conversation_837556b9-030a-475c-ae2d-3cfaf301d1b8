name: Backend tests

on:
  pull_request:
    paths:
      - 'backend/**'
      - '!backend/changelog.md'
      - '!backend/README.md'
    types: [opened, synchronize, reopened, ready_for_review]
  workflow_dispatch:
    inputs:
      chosen-os:
        required: true
        type: choice
        options:
          - tester
          - gh1.int
          - gh2.int
          - gh3.int
          - gh4.int
          - gh1.h3
          - gh1.h7
          - d1.h5
      used-dockerfile:
        required: true
        type: choice
        default: Dockerfile
        options:
          - Dockerfile
          - Dockerfile-Debian
      cancel-concurrent:
        type: choice
        default: 'true'
        options:
          - true
          - false
  workflow_call:
    inputs:
      run_tests:
        description: 'Run Tests'
        required: true
        type: string
        default: 'true'
      used-dockerfile:
        required: true
        type: string
        default: Dockerfile

concurrency:
  group: ${{ github.workflow }}-backend-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: ${{ inputs.cancel-concurrent != 'false' }}

env:
  SCHEMA_NAME: TAS_${{ github.run_id }}_${{ github.run_number }}_${{ github.run_attempt }}
  ARANGO_USER: TAS_${{ github.run_id }}_${{ github.run_number }}_${{ github.run_attempt }}
  REGISTRY_URL: ghcr.neit.cz

jobs:
  Build-image-run-tests:
    if: github.event.pull_request.draft == false

    runs-on:
      - neit-linux
      - ${{ inputs.chosen-os || 'tester' }}
      - ${{ matrix.target_runner }}
    timeout-minutes: 90

    strategy:
      fail-fast: false
      matrix:
        db: ['mssql', 'postgresql']
        include:
          - db: mssql
            db_client: mssql
            db_host: mssqldb.cicd.neit.cz
            db_port: 1433
            target_runner: mssqldb
          - db: postgresql
            db_client: postgresql
            db_host: postgresqldb.cicd.neit.cz
            db_port: 5432
            target_runner: postgresql

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.TAS_REGISTRY_USERNAME }}
          password: ${{ secrets.TAS_REGISTRY_PASSWORD }}
          registry: ${{ vars.TAS_REGISTRY_URL }}
          logout: false

      # Prepare Docker Buildx to use caching
      - name: Prepare Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker

      # Prepare test image and do offline tests during build
      - name: Build test image
        uses: docker/build-push-action@v5
        with:
          target: test
          context: backend
          file: backend/${{ inputs.used-dockerfile || 'Dockerfile' }}
          push: false
          tags: tas-backend-test:${{ github.sha }}
          load: true

      # Randomize ports for backend,ldap and sftp containers for parallel test run
      - name: Randomize ports
        run: |
          echo "BACKEND_PORT=$(echo $(( 8000 + $GITHUB_RUN_NUMBER % 1000 )))" >> $GITHUB_ENV
          echo "LDAP_PORT=$(echo $(( 52000 + $GITHUB_RUN_NUMBER % 1000 )))" >> $GITHUB_ENV
          echo "CURL_PORT=$(echo $(( 3000 + $GITHUB_RUN_NUMBER % 1000 )))" >> $GITHUB_ENV
          echo "SFTP_PORT=$(echo $(( 53000 + $GITHUB_RUN_NUMBER % 1000 )))" >> $GITHUB_ENV

      # Generate hostname for DB IP
      - name: Generate hostname for db
        run: |
          IP_ADD=$(getent hosts ${{ matrix.db_host }} | awk '{ print $1 }')
          echo "IP_ADD=$IP_ADD" >> $GITHUB_ENV

      # Generate DB PW
      - name: Generate DB user pw
        id: generate-vars
        run: |
          DB_LOWER=$(tr -dc 'a-z' </dev/urandom | head -c 1)
          DB_UPPER=$(tr -dc 'A-Z' </dev/urandom | head -c 1)
          DB_DIGIT=$(tr -dc '0-9' </dev/urandom | head -c 1)
          DB_SPECIAL=$(tr -dc '!$#%' </dev/urandom | head -c 1)
          DB_REST=$(tr -dc 'A-Za-z0-9!$#%' </dev/urandom | head -c 9)
          SCHEMA_PW=$(echo "${DB_LOWER}${DB_UPPER}${DB_DIGIT}${DB_SPECIAL}${DB_REST}" | fold -w1 | shuf | tr -d '\n')
          echo "SCHEMA_PW=$SCHEMA_PW" >> $GITHUB_ENV
          ARANGO_PW=$(tr -dc 'A-Za-z0-9_' 2>/dev/null </dev/urandom | head -c 13 2>/dev/null)
          echo "ARANGO_PW=$ARANGO_PW" >> $GITHUB_ENV

      # RUN MSSQL SCHEMA CREATION AND MSSQL-TOOLS CONTAINER SPIN UP
      - name: MSSQL Schema execution
        if: matrix.db == 'mssql'
        run: |
          echo "
          CREATE LOGIN $SCHEMA_NAME WITH PASSWORD='$SCHEMA_PW';
          GO
          CREATE DATABASE $SCHEMA_NAME;
          GO
          USE $SCHEMA_NAME;
          GO
          CREATE USER $SCHEMA_NAME FOR LOGIN $SCHEMA_NAME;
          GO
          ALTER ROLE [db_owner] ADD MEMBER $SCHEMA_NAME;
          GO
          CREATE SCHEMA $SCHEMA_NAME AUTHORIZATION $SCHEMA_NAME;
          GO
          ALTER USER $SCHEMA_NAME WITH DEFAULT_SCHEMA = $SCHEMA_NAME;
          GO
          GRANT CREATE VIEW TO $SCHEMA_NAME;
          GRANT CREATE TABLE TO $SCHEMA_NAME;
          GO
          ALTER DATABASE [$SCHEMA_NAME] SET READ_COMMITTED_SNAPSHOT ON;
          GO
          " > ${{ github.workspace }}/mssql_schema.sql

          # Grab container ID
          CONTAINER_ID=`docker run -d --name mssql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          --add-host=mssqldb.cicd.neit.cz:$(getent hosts mssqldb.cicd.neit.cz | awk '{ print $1 }') \
          --entrypoint tail \
          mcr.microsoft.com/mssql-tools \
          -f /dev/null`
          # Copy schema
          docker cp ${{ github.workspace }}/mssql_schema.sql $CONTAINER_ID:/tmp/mssql_schema.sql
          # Exec into container
          docker exec $CONTAINER_ID /opt/mssql-tools/bin/sqlcmd -S ${{ matrix.db_host }} -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} -i /tmp/mssql_schema.sql

      # RUN POSTGRESQL SCHEMA CREATION AND POSTGRES CONTAINER SPIN UP
      - name: postgres db creation
        if: matrix.db == 'postgresql'
        run: |
          echo "
          CREATE DATABASE \"$SCHEMA_NAME\";
          CREATE USER \"$SCHEMA_NAME\" WITH PASSWORD '$SCHEMA_PW';
          GRANT ALL PRIVILEGES ON DATABASE \"$SCHEMA_NAME\" TO \"$SCHEMA_NAME\";
          \\c \"$SCHEMA_NAME\";
          CREATE EXTENSION IF NOT EXISTS unaccent;
          GRANT ALL ON SCHEMA public TO \"$SCHEMA_NAME\";
          CREATE SCHEMA \"tas\" AUTHORIZATION \"$SCHEMA_NAME\";
          " > ${{ github.workspace }}/pgsql_schema.sql

          # Grab container ID
          CONTAINER_ID=$(docker run -d --name pgsql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} --add-host=${{ matrix.db_host }}:$(getent hosts ${{ matrix.db_host }} | awk '{ print $1 }') --entrypoint tail postgres:17.5 -f /dev/null)
          # Copy schema
          docker cp ${{ github.workspace }}/pgsql_schema.sql $CONTAINER_ID:/tmp/pgsql_schema.sql
          # Exec into container
          docker exec -e PGPASSWORD=${{ secrets.CICD_DB_POSTGRESQL_ADMIN_PASSWORD }} $CONTAINER_ID psql -h ${{ matrix.db_host }} -U ${{ secrets.CICD_DB_POSTGRESQL_ADMIN_USER }} -d postgres -f /tmp/pgsql_schema.sql

      # Generate and fill backend config for mssql connection
      - name: Generate backend config for mssql connection
        if: matrix.db == 'mssql'
        run: |
          echo "
          module.exports = {
              port: $BACKEND_PORT,
              prefix: null,
              hostname: 'http://localhost:$BACKEND_PORT',
              connections: {
                  knexConnection: {
                      host: '${{ matrix.db_host }}',
                      port: ${{ matrix.db_port }},
                      user: '$SCHEMA_NAME',
                      password: '$SCHEMA_PW',
                      database: '$SCHEMA_NAME',
                      requestTimeout: 130000,
                      options: {
                          idleTimeoutMillis: 130000,
                          requestTimeout: 300000,
                      },
                  },
              },
              db: {
                  client: '${{ matrix.db_client }}',
                  maxPoolConnections: 40,
              },
              archivation: {
                  enabled: true,
              },
              langs: ['cs'],
              mail: {
                  from: '\"Team assistant :: TAS\" <<EMAIL>>',
                  sendingEnabled: true,
              },
              csvStorageDir: '/tmp',
              dms: {
                  tikaUrl: 'http://tika:9998', // url to tika
                  elasticUrl: 'http://elasticsearch:9200', // url to elasticSearch, turnoff with fulltext
                  elasticVersion: 7,
                  storagePath: '/tmp', // path where DMS files will be stored
              },
              security: {
                  saltRounds: 2, // change this number depending on the server performance
                  pepper: '4enqL99M7Cz/cGlM3Dy/eCUVs', // random secret 25-char string
                  authTokenSecret: 'EMFtBuWsup',
                  refreshTokenSecret: 'L5duAhH5Yp',
              },
              frontendUrl: 'http://localhost:9000',
              redis: {
                  host: 'redis',
                  port: 6379,
                  // keyPrefix: '$SCHEMA_NAME',
              },
              logger: {
                  arango: {
                      host: 'http://arango:8529', // Arango host:port (http://127.0.0.1:8529). Use null to disable arango logger. Fallback file will be used.
                      db_name: '$ARANGO_USER', // Database name, use something like '{instance_id}_db'.
                      auth: { // Use 'auth: null' to turn off. Only simple (login/pass) authentication allowed.
                          user: '$ARANGO_USER',
                          pass: '$ARANGO_PW',
                      },
                  },
              },
               paths: {
                tmp: '/tmp',
                userPhotoPath: '/tmp',
              },
              crons: {
                runOnStart: false,
                startCrons: false,
              }
          };" > ${{ github.workspace }}/local.js

      # Generate backend config for postgresql connection
      - name: Generate backend config for postgresql connection
        if: matrix.db == 'postgresql'
        run: |
          echo "
          module.exports = {
              port: $BACKEND_PORT,
              prefix: null,
              hostname: 'http://localhost:$BACKEND_PORT',
              connections: {
                  knexConnection: {
                      host: '${{ matrix.db_host }}',
                      port: ${{ matrix.db_port }},
                      user: '$SCHEMA_NAME',
                      password: '$SCHEMA_PW',
                      database: '$SCHEMA_NAME',
                      requestTimeout: 130000,
                      options: {
                          idleTimeoutMillis: 130000,
                          requestTimeout: 300000,
                      },
                  },
              },
              db: {
                  client: '${{ matrix.db_client }}',
                  maxPoolConnections: 40,
              },
              archivation: {
                  enabled: true,
              },
              langs: ['cs'],
              mail: {
                  from: '\"Team assistant :: TAS\" <<EMAIL>>',
                  sendingEnabled: true,
              },
              csvStorageDir: '/tmp',
              dms: {
                  tikaUrl: 'http://tika:9998', // url to tika
                  elasticUrl: 'http://elasticsearch:9200', // url to elasticSearch, turnoff with fulltext
                  elasticVersion: 7,
                  storagePath: '/tmp', // path where DMS files will be stored
              },
              security: {
                  saltRounds: 2, // change this number depending on the server performance
                  pepper: '4enqL99M7Cz/cGlM3Dy/eCUVs', // random secret 25-char string
                  authTokenSecret: 'EMFtBuWsup',
                  refreshTokenSecret: 'L5duAhH5Yp',
              },
              frontendUrl: 'http://localhost:9000',
              redis: {
                  host: 'redis',
                  port: 6379,
                  keyPrefix: '$SCHEMA_NAME',
              },
              logger: {
                  arango: {
                      host: 'http://arango:8529', // Arango host:port (http://127.0.0.1:8529). Use null to disable arango logger. Fallback file will be used.
                      db_name: '$ARANGO_USER', // Database name, use something like '{instance_id}_db'.
                      auth: { // Use 'auth: null' to turn off. Only simple (login/pass) authentication allowed.
                          user: '$ARANGO_USER',
                          pass: '$ARANGO_PW',
                      },
                  },
              },
               paths: {
                tmp: '/tmp',
                userPhotoPath: '/tmp',
              },
              crons: {
                runOnStart: false,
                startCrons: false,
              }
          };" > ${{ github.workspace }}/local.js

      # Create folder for coverage tests
      - name: Create temporary folder for coverage tests
        run: |
          mkdir -p ${{ github.workspace }}/coverage/${{ matrix.db_client }}/{integration,unit,rest} \
          && touch ${{ github.workspace }}/coverage/${{ matrix.db_client }}/{integration,unit,rest}/coverage-final.json \
          && chmod a+w ${{ github.workspace }}/coverage/${{ matrix.db_client }}/{integration,unit,rest}/coverage-final.json \
          && touch ${{ github.workspace }}/coverage/${{ matrix.db_client }}/result.tar \
          && chmod a+w ${{ github.workspace }}/coverage/${{ matrix.db_client }}/result.tar


      # Run Unit tests
      - name: Run Unit test
        run: |
          docker run --rm \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=elasticsearch:host-gateway \
          --add-host=tika:host-gateway \
          --add-host=ldap-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}:host-gateway \
          -e TAS_TEST_AD_SERVER=ldap-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -e TAS_TEST_AD_PORT=$LDAP_PORT \
          --add-host=${{ matrix.db_host }}:$IP_ADD \
          -v ${{ github.workspace }}/local.js:/app/tas/backend/config/config/local.js \
          -v ${{ github.workspace }}/coverage/${{ matrix.db_client }}:/app/tas/coverage-output \
          --mount type=tmpfs,destination=/app/tas/coverage \
          tas-backend-test:${{ github.sha }} \
          bash -c "npm run coverage:unit && cat /app/tas/coverage/unit/coverage-final.json > /app/tas/coverage-output/unit/coverage-final.json"

      # Prepare Arango commands and user creation
      - name: Prepare and execute arango commands
        run: |
          # Grab container ID
          ARANGO_ID=$(docker ps -q -f name=arango)
          # Prepare arango commands
          ARANGO_COMMANDS=$(cat <<EOF
          var users = require('@arangodb/users');
          users.save('$ARANGO_USER', '$ARANGO_PW');
          db._createDatabase('$ARANGO_USER');
          users.grantDatabase('$ARANGO_USER', '$ARANGO_USER', 'rw');
          users.grantCollection('$ARANGO_USER', '$ARANGO_USER', '*', 'rw');
          EOF
          )
          # Execute arango commands
          echo "$ARANGO_COMMANDS" | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}"

      # Run migration
      - name: Run migration
        run: |
          docker run --rm \
          --add-host=redis:host-gateway \
          --add-host=arango:host-gateway \
          --add-host=${{ matrix.db_host }}:$IP_ADD \
          -v ${{ github.workspace }}/local.js:/app/tas/backend/config/config/local.js \
          tas-backend-test:${{ github.sha }} \
          node dist/migrate.js init

      # Start Curl server container
      - name: Start Curl server container
        run: |
          docker run -d \
          --name curl-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -p $CURL_PORT:3001 \
          ${{ vars.TAS_REGISTRY_URL }}/dev/curl-upload-server:1.0.0 \

      # Create folder for SFTP container
      - name: Create folder for sftp
        run: mkdir -m 777 -p ${{ github.workspace }}/data

      # Create temp custom config file
      - name: Create temp custom config file
        run: |
          customconfig=$(mktemp)
          echo -e "HostKey /etc/ssh/ssh_host_rsa_key\nHostKeyAlgorithms +ssh-rsa\nInclude /etc/ssh/sftpdata/sshd_config" > "$customconfig"
          chmod 777 "$customconfig"
          echo "CUSTOM_CONFIG=$customconfig" >> $GITHUB_ENV

      # Start SFTP container
      - name: Start SFTP container
        run: |
          docker run -d \
          --name sftp-integration-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -e TAS_TEST_SFTP_USER_PASSWORD=${{ secrets.CICD_SFTP_PASSWORD }} \
          -v ${{ github.workspace }}/data:/home/<USER>/data \
          -v "$CUSTOM_CONFIG:/etc/sshd/sftpdata/custom_sshd_config.conf" \
          -e TAS_TEST_SFTP_USER=${{ secrets.CICD_SFTP_USER }} \
          -p $SFTP_PORT:53122 \
          ${{ vars.TAS_REGISTRY_URL }}/dev/sftp-integration:cicdpassword \
          /usr/sbin/sshd -f /etc/sshd/sftpdata/custom_sshd_config.conf -e -D

      # Run Integration tests
      - name: Run integration tests
        run: |
          docker run --rm \
          --add-host=curl-server:host-gateway \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=${{ matrix.db_host }}:$IP_ADD \
          -e FILE_UPLOAD_SERVER=curl-server:$CURL_PORT \
          --add-host=sftp-integration-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}:host-gateway \
          -e TAS_TEST_SFTP_SERVER=sftp-integration-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -e TAS_TEST_SFTP_USER=${{ secrets.CICD_SFTP_USER }} \
          -e TAS_TEST_SFTP_USER_PASSWORD=${{ secrets.CICD_SFTP_PASSWORD }} \
          -e TAS_TEST_SFTP_PORT=$SFTP_PORT \
          -v ${{ github.workspace }}/local.js:/app/tas/backend/config/config/local.js \
          -v ${{ github.workspace }}/coverage/${{ matrix.db_client }}:/app/tas/coverage-output \
          --mount type=tmpfs,destination=/app/tas/coverage \
          tas-backend-test:${{ github.sha }} \
          bash -c "npm run coverage:integration && cat /app/tas/coverage/integration/coverage-final.json > /app/tas/coverage-output/integration/coverage-final.json"

      # Run LO tests
      - name: Run LibreOffice test
        run: |
          docker run --rm \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=${{ matrix.db_host }}:$IP_ADD \
          -v ${{ github.workspace }}/local.js:/app/tas/backend/config/config/local.js \
          tas-backend-test:${{ github.sha }} \
          npm run test:libreoffice

      # Start LDAP server container
      - name: Start LDAP server container
        run: |
          docker run -d \
          --name ldap-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -p $LDAP_PORT:10389 \
          ${{ vars.TAS_REGISTRY_URL }}/dev/ldap-test-server:latest \

      # Run Rest tests
      - name: Run Rest test
        run: |
          docker run --rm \
          --add-host=arango:host-gateway \
          --add-host=redis:host-gateway \
          --add-host=elasticsearch:host-gateway \
          --add-host=tika:host-gateway \
          --add-host=ldap-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}:host-gateway \
          -e TAS_TEST_AD_SERVER=ldap-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -e TAS_TEST_AD_PORT=$LDAP_PORT \
          --add-host=${{ matrix.db_host }}:$IP_ADD \
          -v ${{ github.workspace }}/local.js:/app/tas/backend/config/config/local.js \
          -v ${{ github.workspace }}/coverage/${{ matrix.db_client }}:/app/tas/coverage-output \
          --mount type=tmpfs,destination=/app/tas/coverage \
          tas-backend-test:${{ github.sha }} \
          bash -c "npm run coverage:rest && cat /app/tas/coverage/rest/coverage-final.json > /app/tas/coverage-output/rest/coverage-final.json"

      - name: Merge coverage files
        run: |
          docker run --rm \
          --name tas-backend-test-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} \
          -v ${{ github.workspace }}/coverage/${{ matrix.db_client }}:/app/tas/coverage-output \
          --mount type=tmpfs,destination=/app/tas/coverage \
          tas-backend-test:${{ github.sha }} \
          bash -c "cat /app/tas/coverage-output/integration/coverage-final.json > /app/tas/coverage/integration.json && cat /app/tas/coverage-output/rest/coverage-final.json > /app/tas/coverage/rest.json && cat /app/tas/coverage-output/unit/coverage-final.json > /app/tas/coverage/unit.json && nyc report --reporter html -t /app/tas/coverage/ --report-dir /app/tas/coverage/final && tar -cpf /app/tas/coverage-output/result.tar -C /app/tas/coverage/final ."


      - name: Create coverage folder to copy
        run: |
          if [ ! -d "/home/<USER>/coverage/${{ matrix.db_client }}" ]; then
            mkdir -p /home/<USER>/coverage/${{ matrix.db_client }}
            echo "Folder created."
          else
            echo "Folder already exists."
          fi

      - name: Copy coverage results
        run: |
          tar -xvf ${{ github.workspace }}/coverage/${{ matrix.db_client }}/result.tar -C /home/<USER>/coverage/${{ matrix.db_client }}

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-backend-${{ matrix.db_client }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: /home/<USER>/coverage/${{ matrix.db_client }}
          retention-days: 20

      - name: Login to GitHub Container Registry
        if: success()
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.REGISTRY_USER }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
          registry: ${{ env.REGISTRY_URL }}
          logout: false

      - name: Push Test Image to Registry
        if: success()
        run: |
          docker tag tas-backend-test:${{ github.sha }} ${{ env.REGISTRY_URL }}/tas-ci/backend:${{ github.sha }}
          docker push ${{ env.REGISTRY_URL }}/tas-ci/backend:${{ github.sha }}

      # Drop Mssql user and schema/db
      - name: Drop MSSQL DB
        if: always() && matrix.db == 'mssql'
        run: |
          echo "
          USE master;
          DROP DATABASE $SCHEMA_NAME;
          DROP LOGIN $SCHEMA_NAME;
          GO
          " > mssql_drop.sql
          # Grab ID
          CONTAINER_ID=$(docker ps -q -f name=mssql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }})
          # Copy drop script
          docker cp ${{ github.workspace }}/mssql_drop.sql $CONTAINER_ID:/tmp/mssql_drop.sql
          # Exec and remove User
          docker exec $CONTAINER_ID /opt/mssql-tools/bin/sqlcmd -S ${{ matrix.db_host }} -U ${{ secrets.CICD_DB_MSSQL_ADMIN_USER }} -P ${{ secrets.CICD_DB_MSSQL_ADMIN_PASSWORD }} -i /tmp/mssql_drop.sql
          # Stop and remove container
          docker stop $CONTAINER_ID && docker rm $CONTAINER_ID

      # Drop PostgreSQL DB
      - name: Drop PostgreSQL DB
        if: always() && matrix.db == 'postgresql'
        run: |
          echo "
          DROP DATABASE IF EXISTS \"$SCHEMA_NAME\";
          DROP USER IF EXISTS \"$SCHEMA_NAME\";
          " > pgsql_drop.sql
          # Grab ID
          CONTAINER_ID=$(docker ps -a -q -f name=pgsql-tools-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }})
          if [ ! -z "$CONTAINER_ID" ]; then
            # Copy drop script
            docker cp ${{ github.workspace }}/pgsql_drop.sql $CONTAINER_ID:/tmp/pgsql_drop.sql
            # Exec and remove User/DB
            docker exec -e PGPASSWORD=${{ secrets.CICD_DB_POSTGRESQL_ADMIN_PASSWORD }} $CONTAINER_ID psql -h ${{ matrix.db_host }} -U ${{ secrets.CICD_DB_POSTGRESQL_ADMIN_USER }} -d postgres -f /tmp/pgsql_drop.sql
            # Stop and remove container
            docker stop $CONTAINER_ID && docker rm $CONTAINER_ID
          fi

      # Drop Arango DB and user
      - name: Drop Arango db and user
        if: ${{ always() }}
        run: |
          # Grab container ID
          ARANGO_ID=$(docker ps -q -f name=arango)
          # Arango cleanup commands
          ARANGO_CLEANUP=$(cat <<EOF
          db._dropDatabase('$ARANGO_USER');
          require("@arangodb/users").remove('$ARANGO_USER');
          EOF
          )
          # Execute arango cleanup
          echo "$ARANGO_CLEANUP" | docker exec -i $ARANGO_ID sh -c "arangosh --server.username=${{ secrets.CICD_DB_ARANGO_ADMIN_USER }} --server.password=${{ secrets.CICD_DB_ARANGO_ADMIN_PASSWORD }}" || true

      # Stop and remove LDAP container
      - name: Cleanup LDAP server container
        if: ${{ always() }}
        run: |
          docker rm -v -f ldap-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} || true
          docker rm -v -f curl-server-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} || true

      # # Stop and remove SFTP container and delete custom files
      - name: Cleanup SFTP container
        if: ${{ always() }}
        run: |
          docker rm -v -f sftp-integration-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }} || true
          rm $CUSTOM_CONFIG || true
          rm -rf ${{ github.workspace }}/data || true

      - name: Remove coverage folders
        if: ${{ always() }}
        run: |
          rm -rf ${{ github.workspace }}/coverage/${{ matrix.db_client }} || true
