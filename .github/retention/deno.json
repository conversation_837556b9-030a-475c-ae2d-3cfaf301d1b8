{"tasks": {"start": "deno run -A main.ts", "check": "deno check main.ts", "lint": "deno lint", "fmt": "deno fmt", "test": "deno test -A", "setup-tests": "deno run -A test-scenarios.ts"}, "fmt": {"include": ["**/*.ts"], "lineWidth": 100, "indentWidth": 2}, "lint": {"include": ["**/*.ts"], "rules": {"tags": ["recommended"]}}, "compilerOptions": {"strict": true, "noUnusedLocals": true, "noUnusedParameters": true}}