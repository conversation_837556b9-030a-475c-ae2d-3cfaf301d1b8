/**
 * This file is used to define the list of browsers that will be installed and used by Cypress,
 * if it's launched with "--env tasTestsUseCustomBrowsers=true".
 * */

import { Browser, computeExecutablePath, InstallOptions } from '@puppeteer/browsers';

type CustomBrowser = {
    installOptions: InstallOptions & { unpack?: true };
    getCypressBrowserConfig: (config: Cypress.PluginConfigOptions) => Cypress.PluginConfigOptions['browsers'][0];
};

// Keep the buildIds up to date with versions inside the cypress/included Docker image used in the GitHub Actions workflow
// Workflow file -> see cypress/included version -> find corresponing tag on Docker hub -> see its layers -> find CHROME_VERSION env variable
const browsersInstallOptions: Partial<Record<CustomBrowser['installOptions']['browser'], CustomBrowser['installOptions']>> = {
    chrome: {
        browser: Browser.CHROME,
        buildId: '131.0.6778.264',
        cacheDir: './custom-browsers/cache',
    },
    // potentially add more browsers here
};

const customBrowsersList: CustomBrowser[] = [
    // the first browser in the list will be the default browser used by the tests
    {
        installOptions: browsersInstallOptions.chrome,
        getCypressBrowserConfig: (config) => ({
            name: 'chrome',
            family: 'chromium',
            displayName: 'Chrome',
            path: computeExecutablePath(browsersInstallOptions.chrome),
            channel: 'stable',
            isHeaded: config.isInteractive,
            isHeadless: !config.isInteractive,
            majorVersion: browsersInstallOptions.chrome.buildId.split('.')[0],
            version: browsersInstallOptions.chrome.buildId,
        }),
    },
    // potentially add more browsers here
    // - if we ever add Firefox (or a Firefox-based browser) to the list:
    //   - see e2e.ts for a TODO for pre-setting the testing timezone in Firefox
    //     - right now, it would not be set at all, creating a timezone discrepancy when the machine's timezone is different
    // - check the "before:browser:launch" hook in cypress.config.ts - it contains browser-specific settings
    //   - new browsers might require additional settings to be added there (see the settings for Chrome)
];

export default customBrowsersList;
