import { install, computeExecutablePath } from '@puppeteer/browsers';

import browsersList from './customBrowsersList';

(async () => {
    console.log('Installing custom browsers...');

    Promise.all(browsersList.map((browserToInstall) => new Promise(() => {
        console.log(`- Installing browser "${browserToInstall.installOptions.browser}", version ${browserToInstall.installOptions.buildId}...`);
        install(browserToInstall.installOptions).then(() => {
            console.log(`  - "${browserToInstall.installOptions.browser}" executable installed at: ${computeExecutablePath(browserToInstall.installOptions)}`);
        });
    })));
})();
