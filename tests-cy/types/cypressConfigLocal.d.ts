type TestInstanceUser = 'testInstanceAdmin' | 'testInstanceUser1' | 'testInstanceUser2';

type CypressConfigLocal = Cypress.ConfigOptions & {
    e2e: {
        env: {
            /**
             * - The default user should be the admin of the TAS instance.
             * - The default user will (and should) be used for every test without the `testInstanceOnly` test tag
             * (any test that doesn't need to be run on a test instance with a pre-prepared DB state)
             *    - If the tests are run on a test instance with a pre-prepared DB state,
             *      the default user should be the same user as the test instance admin
             * - This username will be overwritten by the corresponding system enviroment variable (CYPRESS_TAS_USER_DEFAULT_USERNAME), if it's set (see the readme).
             */
            defaultUserUsername?: string,
            /**
             * This password will be overwritten by the corresponding system enviroment variable (CYPRESS_TAS_USER_DEFAULT_PASSWORD), if it's set (see the readme).
             */
            defaultUserPassword?: string,
            /**
             * These passwords will be overwritten by the corresponding system enviroment variables, if they're set (see the readme).
             */
            testInstanceUserPasswords?: Partial<Record<TestInstanceUser, string>>
            /**
             * Tags of the tests which should be skipped during test runs.
             */
            excludedTestTags?: Array<TestTag>,
            /**
             * Don't show the tests / tests suites with excluded tags as 'skipped' during test runs (fully hide them instead).
             * Can be overriden on an individual test / test suite basis.
             */
            skipExcludedTestTagsSilently?: boolean,
            /**
             * - In interactive mode:
             *   - The first time a JSON fixture is loaded within a spec, its type file will be used to validate its contents. The validation takes a few seconds.
             * - In headless mode (running all the tests):
             *   - All of the JSON fixtures in the cypress/fixtures will be validated before the rest of the tests are run. The validation takes a while (a few seconds per fixture).
             */
            validateFixturesIfPossible?: boolean,
            /**
             * Glob patterns of fixture paths (relative to `cypress/fixtures`) to exclude from batch fixture validation
             * (headless mode validation & the `__allFixturesAreValid.cy.ts` spec).
             */
            batchFixtureValidationExcludedPatterns?: string[],
        }
    }
}
