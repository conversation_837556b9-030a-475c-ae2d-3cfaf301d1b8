declare namespace Cypress {
    interface Chainable {
        /**
         * Create / restore a user session (log in).
         * - The 'default' user should be used for every test without the `testInstanceOnly` test tag
         *   (any test that doesn't need to be run on a test instance with a pre-prepared DB state)
         * @param user 'default' for the default user, or one of the test instance users defined in the "test-instance/users.json" fixture
         * @param customLoginCommand if provided, this tasLogin will only pass the login info (and login-related messages to log) to the custom command, without creating any session
         */
        tasLogin(user: 'default' | TestInstanceUser, customLoginCommand?: (username: string, password: string, toLog: string[]) => void): void,
        /**
         * Create / Match a snapshot of the subject value. (cypress-plugin-snapshot)
         * See https://github.com/meinaart/cypress-plugin-snapshots#usage-for-text-snapshots for more info.
         * @param options
         */
        toMatchSnapshot(options?: {
            /**
             * Ignore fields that are not in snapshot
             */
            ignoreExtraFields?: boolean,
            /**
             * Ignore if there are extra array items in result
             */
            ignoreExtraArrayItems?: boolean,
            /**
             * Alphabetically sort keys in JSON
             */
            normalizeJson?: boolean,
            /**
             * Replace `key`s in snapshot with the provided values.
             */
            replace?: {
                [key: string]: unknown
            }
        }): Chainable,
        /**
         * Create / Match a snapshot of the subject value, but ignore the specified object fields.
         * Internally passes the provided subject to cy.toMatchSnapshot from cypress-plugin-snapshot.
         * See https://github.com/meinaart/cypress-plugin-snapshots#usage-for-text-snapshots for more info.
         * @param excludedFields object fields to ignore
         * @param options options passed in to the internal cy.toMatchSnapshot
         */
        toMatchSnapshotWithout(excludedFields?: string[], options?: Parameters<Cypress.Chainable['toMatchSnapshot']>[0]): Chainable,
        /**
         * Asserts that a redirect happened (against the URL stored under the `urlAlias` alias) & stores the new url as the `urlAlias`
         * @param urlAlias alias holding the previous URL (will be ovewritten with the new URL)
         * @param options
         */
        trackExpectedRedirect(
            urlAlias: string,
            options?: {
                /**
                 * URL to use as the previous URL instead of the one stored as the `urlAlias`
                 */
                refUrl?: string,
                /**
                 * RegExp that the new URL should match (extra assertion)
                 */
                newUrlMatch?: RegExp
            }
        ): void,
        /**
         * Validates a fixture against its type. See the readme for fixture type file & naming conventions.
         * @param fixturePath Fixture path relative to cypress/fixtures (`cypress/fixtures/<fixturePath>`).
         * Should include the file extension (.json) - same format as `cy.fixture(...)`.
         */
        validateFixture(
            fixturePath: string,
            options?: {
                /**
                 * An instance of the Ajv class.
                 * Useful when iterating over multiple fixtures so that we don't have to create a new instance for each fixture.
                 */
                ajvInstance?: import('ajv').default,
                /**
                 * If automatic fixture validation is enabled & Cypress is running in interactive mode,
                 * enabling this will prevent any further automatic validation (in the current spec) from happening.
                 *  - The auto-validation would be initiated by the first use of the cy.fixture command with the fixture in question.
                 */
                preventsAutoValidation?: boolean,
            },
        ): Chainable<{
            result: 'valid' | 'invalid' | 'type-file-error',
            errors?: import('ajv').ValidateFunction['errors'],
        }>,
        /**
         * Drags the subject element to the target element.
         * Assumes that the DnD mechanic is implemented using react-beautiful-dnd.
         * @param target a selector or an alias of the element to drag to, same as you would use for cy.get()
         */
        reactBdndDragTo(target: string): void,
    }
}
