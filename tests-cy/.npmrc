# Revision number from the "Stable" table from https://chromiumdash.appspot.com/releases
# "node_chromium_revision" is used by npm "chromium" module to determine the version of Chromium to use
# see https://www.npmjs.com/package/chromium#install-a-concrete-revision
# upon `npm install`, if the module is missing, this version of Chromium will be downloaded (or loaded from cache, if it was already downloaded)
# & installed to the corresponding directory in node_modules
# to update Chromium after changing this value (without the need to delete node_modules) run `npm rebuild chromium`
# when updating this value, also update the "node_chromium_major_version" & "node_chromium_version" values below
node_chromium_revision=1313161

# "node_chromium_major_version" & "node_chromium_version" are used in cypress.config.ts to provide Chromium version info to Cypress
node_chromium_major_version=127
node_chromium_version=127.0.6533.122
