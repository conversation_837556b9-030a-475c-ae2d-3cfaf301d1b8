import { defineConfig } from 'cypress';
import { initPlugin } from 'cypress-plugin-snapshots/plugin';
import cypressFailFast = require('cypress-fail-fast/plugin');

import * as _ from 'lodash';
import { createServer } from 'node:http';

import * as tasks from 'cypress/tasks';

// @ts-expect-error .ts import needed for pipeline run
import localConfig from 'cypress.config.local.ts';
import customBrowsersList from './custom-browsers/customBrowsersList';

export default defineConfig(_.merge({
    e2e: {
        async setupNodeEvents(on: Cypress.PluginEvents, config: Cypress.PluginConfigOptions): Promise<Cypress.PluginConfigOptions> {
            initPlugin(on, config);
            cypressFailFast(on, config);

            on('task', {...tasks});

            on('before:browser:launch', (browser, launchOptions) => {
                if (browser.isHeadless) {
                    const width = Number(config.env['headlessBrowserWindowWidth'] || 1920);
                    const height = Number(config.env['headlessBrowserWindowHeight'] || 1080);

                    if (browser.name === 'chrome' || browser.name === 'chromium') {
                        launchOptions.args.push(`--window-size=${width},${height}`);

                        // force screen to be non-retina
                        launchOptions.args.push('--force-device-scale-factor=1');

                        // force primaryPointerType to be mouse
                        // source (anwser) & reason (the question): https://stackoverflow.com/a/76953351
                        // - MUI DatePickers & TimePickers use their desktop versions when the "@media (pointer: fine)" CSS query applies,
                        //   which for some reason is not the case in the GitHub Actions pipeline
                        //   - setting this flag fixes the issue
                        launchOptions.args.push('--blink-settings=primaryPointerType=4');
                    }

                    if (browser.name === 'electron') {
                        launchOptions.preferences.width = width;
                        launchOptions.preferences.height = height;
                    }

                    if (browser.name === 'firefox') {
                        launchOptions.env.MOZ_HEADLESS_WIDTH = String(width);
                        launchOptions.env.MOZ_HEADLESS_HEIGHT = String(height);
                    }
                }

                return launchOptions;
            });

            if (config.env['tasTestsFixtureValidationRun']) {
                const server = createServer((_inc, res) => {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({}));
                });

                server.listen(0, 'localhost');

                config.baseUrl = server.address() as string;
                config.screenshotOnRunFailure = false;
            }

            // use custom browsers (see ./custom-browsers) if tasTestsUseCustomBrowsers is enabled
            if (config.env['tasTestsUseCustomBrowsers']) {
                const browsers = customBrowsersList.map((browser) => browser.getCypressBrowserConfig(config));

                return {
                    ...config,
                    defaultBrowser: browsers[0].name,
                    browsers,
                };
            }

            return config;
        },
        viewportWidth: 1920,
        viewportHeight: 1080,
        excludeSpecPattern: ['**/__snapshots__/*'],
        defaultCommandTimeout: 8000,
        requestTimeout: 10000,
        env: {
            'cypress-plugin-snapshots': {
                excludeFields: [],
                updateSnapshots: false,
            },
            FAIL_FAST_ENABLED: false,
        },
    },
} as Cypress.ConfigOptions, localConfig));
