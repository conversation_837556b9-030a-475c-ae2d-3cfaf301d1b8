import { defineConfig } from 'cypress';
import { initPlugin } from 'cypress-plugin-snapshots/plugin';
import cypressFailFast = require('cypress-fail-fast/plugin');

import * as _ from 'lodash';
import { createServer } from 'node:http';

import * as tasks from 'cypress/tasks';
import localConfig from 'cypress.config.local';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const chromium = require('chromium');

// the local config will override these options (behavior of _.merge())
export default defineConfig(_.merge({
    e2e: {
        async setupNodeEvents(on: Cypress.PluginEvents, config: Cypress.PluginConfigOptions): Promise<Cypress.PluginConfigOptions> {
            initPlugin(on, config);
            cypressFailFast(on, config);
            on('task', {...tasks});

            if (config.env['tasFixtureValidationRun']) {
                const server = createServer((_inc, res) => {
                    res.writeHead(200, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({}));
                });

                server.listen(0, 'localhost');

                config.baseUrl = server.address() as string;
                config.screenshotOnRunFailure = false;
            }

            return {
                ...config,
                browsers: [
                    ...config.browsers,
                    {
                        // named 'chromium-tests-cy' to avoid conflicts if the user has Chromium installed on their system
                        name: 'chromium-tests-cy',
                        family: 'chromium',
                        displayName: 'Chromium',
                        path: chromium.path,
                        channel: 'stable',
                        isHeaded: config.isInteractive,
                        isHeadless: !config.isInteractive,

                        // both of these are configured in .npmrc
                        majorVersion: process.env['npm_config_node_chromium_major_version'],
                        version: process.env['npm_config_node_chromium_version'],
                    },
                ],
            };
        },
        baseUrl: 'http://localhost:8000',
        viewportWidth: 1920,
        viewportHeight: 1080,
        excludeSpecPattern: ['**/__snapshots__/*'],
        defaultCommandTimeout: 8000,
        requestTimeout: 10000,
        env: {
            'cypress-plugin-snapshots': { // for more options, see https://github.com/meinaart/cypress-plugin-snapshots#make-changes-to-default-configuration
                excludeFields: [], // Array of fieldnames that should be excluded from snapshot
                updateSnapshots: false, // Automatically update snapshots, useful if you have lots of changes
            },

            // disable this plugin's main feature globally - we only use it in certain tests
            // (see https://github.com/javierbrea/cypress-fail-fast?tab=readme-ov-file#configuration)
            FAIL_FAST_ENABLED: false,
        },
    },
} as Cypress.ConfigOptions, localConfig));
