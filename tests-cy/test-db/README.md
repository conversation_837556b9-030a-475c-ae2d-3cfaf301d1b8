# Test instance database setup
Most of the tests need to be run against a test instance with a database containing specific test data.
The scripts in this directory, along with this guide, will help you set up the database.

## Prerequisites
- The scripts assume a clean instance of a MSSQL Server (2019) database.
- For a local test instance, you can use the provided [test-db-mssql.yml](./test-db-mssql.yml) docker-compose file.
    - `docker-compose -f test-db-mssql.yml up -d`

## Running the scripts
1. Using your preferred DB management tool (e.g. SSMS), connect to the database instance & log in **as the SA**.
2. Run the [tests-cy-mssql-setup.sql](./tests-cy-mssql-setup.sql) script to create the schema & insert the test data.
3. Run the [tests-cy-mssql-sequence-sync.sql](./tests-cy-mssql-sequence-sync.sql) script to restart the sequences from the correct values.
    - This is necessary because the script generated by SSMS doesn't include the current sequence values.
3. Do **one** of the following:
    - Run the [tests-cy-mssql-activate-tas-user.sql](./tests-cy-mssql-activate-tas-user.sql) script to activate the **tas** DB login with the default password.
    - Run `ALTER LOGIN tas WITH PASSWORD = '<PASSWORD>'`, then `ALTER LOGIN tas ENABLE` to activate the **tas** DB login manually with a custom password.
4. The database is now set up and ready for use by the TAS instance that's used for the tests.

## SSMS script generation
- If you have an existing set-up instance of the test database and you wish to update the main script with a new schema/data, you should use the SSMS script generation feature.

### Main setup script
1. Open SQL Server Management Studio (SSMS).
2. Connect to the database instance & log in **as the SA**.
3. Open *Databases* in the left-side folder tree.
4. Right-click on the **TAS** database -> *Tasks* -> *Generate Scripts...*
5. Click *Next* on the first screen.
6. Make sure that the *Script entire database and all database objects* option is selected & click *Next*.
7. Pick *Save as a script file* & pick a location for the script.
8. Click *Advanced* in the upper right corner.
9. Set these options:
    - Include Scripting Parameters Header: **True**
    - Include system constraint names: **True**
    - Script Bindings: **True**
    - Script Collation: **True**
    - Script Logins: **True**
    - Script Object-Level Permissions: **True**
    - Script Owner: **True**
    - Types of data to script: **Schema and data**
    - Script Data Compression Options: **True**
    - Script Full-Text Indexes: **True**
    - Script Change Tracking: **True**
    - Script Triggers: **True**
    - Script Xml Compression Options: **True**
10. Click *OK*.
11. Click *Next*.
11. Click *Next >* - the script generation will begin.
12. Click *Finish* when the script generation is done.
13. The script should be saved in the location you specified in step 7.
    You can now use it to update the main script ([tests-cy-mssql-setup.sql](./tests-cy-mssql-setup.sql)).
14. Before commiting the new version of the script, make sure that its encoding is UTF-8. If not, change it using your preferred text editor / IDE.
    - SSMS saves the script in UTF-16 LE by default.


- See the [corresponding SSMS documentation entry](https://learn.microsoft.com/en-us/sql/ssms/scripting/generate-and-publish-scripts-wizard) for a detailed walk-through of the process.

### Sequence sync script
- Since the script that SSMS generates doesn't include current sequence values, you'll need to generate a separate script to update sequences.
- To do so:
    1. Run the [generate-sequence-sync.sql](./generate-sequence-sync.sql) script on the TAS DB as SA.
    2. Use the results to update the sequence sync script ([tests-cy-mssql-sequence-sync.sql](./tests-cy-mssql-sequence-sync.sql)).
