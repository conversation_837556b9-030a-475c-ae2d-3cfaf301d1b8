GO
USE [TAS]
GO

------------------------ start of generated script ------------------------
ALTER SEQUENCE [TAS].[MIG_STEP_ID_SEQ] RESTART WITH 793
ALTER SEQUENCE [TAS].[TTASKCON_ID] RESTART WITH 2
ALTER SEQUENCE [TAS].[TTASKCON_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TTASKLINK_ID] RESTART WITH 98
ALTER SEQUENCE [TAS].[TTASKLINK_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TTASKVARUSG_ID_SEQ] RESTART WITH 1188
ALTER SEQUENCE [TAS].[TTC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[COMP_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TTCALC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TVAR_ID_SEQ] RESTART WITH 143
ALTER SEQUENCE [TAS].[TVARLOV_ID_SEQ] RESTART WITH 498
ALTER SEQUENCE [TAS].[USER_ID_SEQ] RESTART WITH 4
ALTER SEQUENCE [TAS].[HEADER_ID_SEQ] RESTART WITH 49
ALTER SEQUENCE [TAS].[CRON_ID_SEQ] RESTART WITH 76
ALTER SEQUENCE [TAS].[HEADER_ORGSTR_ID_SEQ] RESTART WITH 3
ALTER SEQUENCE [TAS].[HEADER_ROLE_ID_SEQ] RESTART WITH 48
ALTER SEQUENCE [TAS].[USER_PASS_HISTORY_SEQ] RESTART WITH 100
ALTER SEQUENCE [TAS].[USRORGSTR_ID_SEQ] RESTART WITH 3
ALTER SEQUENCE [TAS].[USER_PHOTO_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[USRPAR_ID_SEQ] RESTART WITH 62
ALTER SEQUENCE [TAS].[USRPAR_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[USRROL_ID_SEQ] RESTART WITH 25
ALTER SEQUENCE [TAS].[REGEX_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[UV_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[UVL_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[GUIDE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[COMP_RULE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[JS_SCRIPTS_VERSION_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[ITASKJS_CALC_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[REGEX_RULE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[XPI_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TTASKJS_CALC_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[XPI_BATCH_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RGGF_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RGGFO_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RGGFD_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[CRON_RUN_ID_SEQ] RESTART WITH 1247
ALTER SEQUENCE [TAS].[BACKEND_APPLICATION_ID_SEQ] RESTART WITH 273
ALTER SEQUENCE [TAS].[CVM_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[CLOB_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[CAL_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DT_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[COUNTER_ID_SEQ] RESTART WITH 62
ALTER SEQUENCE [TAS].[COUNTER_LOG_ID_SEQ] RESTART WITH 102
ALTER SEQUENCE [TAS].[CV_ID_SEQ] RESTART WITH 15
ALTER SEQUENCE [TAS].[CVC_ID_SEQ] RESTART WITH 91
ALTER SEQUENCE [TAS].[CVS_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DBV_ID_SEQ] RESTART WITH 62
ALTER SEQUENCE [TAS].[DLL_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMS_WEBDAV_SQ1] RESTART WITH 100
ALTER SEQUENCE [TAS].[DMSAD_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSAR_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSAS_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSF_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSFAL_ID_SEQ] RESTART WITH 21
ALTER SEQUENCE [TAS].[DMSI_QUEUE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMST_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSTT_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSV_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[DMSVC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[EVE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[EVEDEF_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[EVEPAR_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[AUTH_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[FOLDER_ID_SEQ] RESTART WITH 122
ALTER SEQUENCE [TAS].[FOLSTR_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[ICOND_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[IGRAPH_ID_SEQ] RESTART WITH 21
ALTER SEQUENCE [TAS].[IPN_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[IPROC_ID_SEQ] RESTART WITH 21
ALTER SEQUENCE [TAS].[ITASK_ID_SEQ] RESTART WITH 21
ALTER SEQUENCE [TAS].[ITASKH_ID_SEQ] RESTART WITH 41
ALTER SEQUENCE [TAS].[ITASKLINK_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[ITASKVARUSG_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[ITC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[ITCALC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[IVAR_ID_SEQ] RESTART WITH 641
ALTER SEQUENCE [TAS].[IPH_ID_SEQ] RESTART WITH 21
ALTER SEQUENCE [TAS].[IVARH_ID_SEQ] RESTART WITH 1231
ALTER SEQUENCE [TAS].[IVARLOV_ID_SEQ] RESTART WITH 2621
ALTER SEQUENCE [TAS].[IVARS_ID_SEQ] RESTART WITH 3
ALTER SEQUENCE [TAS].[IVARSN_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[GRAPH_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[JS_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[LOGIN_ID_SEQ] RESTART WITH 24
ALTER SEQUENCE [TAS].[MAQU_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[OA_ID_SEQ] RESTART WITH 7
ALTER SEQUENCE [TAS].[ORGSTR_ID_SEQ] RESTART WITH 3
ALTER SEQUENCE [TAS].[RGS_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[PLN_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[PLNPRC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[PRNT_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RACC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RDEF_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[ROLE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RULE_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[STAT_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[STATC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TSKMSS_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[STATP_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TASK_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TCOND_ID_SEQ] RESTART WITH 139
ALTER SEQUENCE [TAS].[TFO_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[RMD_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TFR_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[MIGRATION_ID_SEQ] RESTART WITH 205
ALTER SEQUENCE [TAS].[TGRAPH_ID] RESTART WITH 2
ALTER SEQUENCE [TAS].[TGRAPH_ID_SEQ] RESTART WITH 346
ALTER SEQUENCE [TAS].[TSEC_ID_SEQ] RESTART WITH 2
ALTER SEQUENCE [TAS].[TPROC_ID_SEQ] RESTART WITH 48
ALTER SEQUENCE [TAS].[TTASK_ID_SEQ] RESTART WITH 103
------------------------- end of generated script -------------------------

GO
