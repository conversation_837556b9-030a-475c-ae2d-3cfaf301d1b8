const localConfig: CypressConfigLocal = {
    e2e: {
        baseUrl: 'http://localhost:8000',
        excludeSpecPattern: [ // exclude certain specs or paths
            'cypress/e2e/__meta_tests__/**',
        ],
        env: { // the local config file enviroment variables are documented within the CypressLocalConfig type

            // these variables also have system environment variable counterparts (see the readme),
            // the system environment variables will override these
            defaultUserUsername: '',
            defaultUserPassword: '',
            testInstanceUserPasswords: {
                testInstanceAdmin: '',
                testInstanceUser1: '',
                testInstanceUser2: '',
            },

            // these variables have no system environment variable conterparts,
            // they can only be set in this local config
            excludedTestTags: [
                'testInstanceOnly', // to include these, a test instance DB is needed
            ],
            skipExcludedTestTagsSilently: false,
            validateFixturesIfPossible: true,
            batchFixtureValidationExcludedPatterns: [
                '__meta_tests__/**',
            ],
            headlessBrowserWindowWidth: 1920,
            headlessBrowserWindowHeight: 1080,
        },
    },
};

export default localConfig;
