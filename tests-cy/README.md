# Setup
To be able to test anything at an actual TAS instance, you need to:
- Set up a local cypress config using the *cypress.config.local.ts.default.dot* template.
- Add the default user's username & password to the local environment by either:
    - setting the `defaultUserUsername` & `defaultUserPassword` environment variables in the local cypress config (`e2e.env...`)
    - setting the `CYPRESS_TAS_USER_DEFAULT_USERNAME` & `CYPRESS_TAS_USER_DEFAULT_PASSWORD` system environment variables
- If you wish to run tests using a [test instance DB](#test-instance-database-setup), you need to:
    - remove "testInstanceOnly" from the excludedTestTags in the local cypress config
    - add the test instance users' passwords to the the local environment by either:
        - defining the `testInstanceUserPasswords` environment variable in the local config (`e2e.env...`)
        - setting the `CYPRESS_TAS_USER_TI_<ADMIN|USER1|USER2>_PASSWORD` system environment variables
        - (only the test instance admin is required for now)
- See the *cypress.config.local.ts.default.dot* template and the *CypressConfigLocal* type (used by the local config) for additional info.

# JSON Fixtures
- Every Cypress JSON fixture should ideally be typed by a TS type. There are 2 rules for typing fixtures:
    1. The name of the file containing the fixture type must be of this format: `<full_fixture_file_name>.type.ts`
        - The full fixture file name should include the `.json` extension.
        - The type file should be in the same directory as the fixture.
        - As a fallback, you can also provide a `<directory_name>.common.type.ts` file in a directory containg multiple fixtures.
            - This file will be used to type all of the fixtures in the directory that don't have their own associated type files.
            - The directory name refers to the directory containing the fixture.
    2. The type used for the fixture must be marked with the `FixtureType` JSDoc tag.
    - See the `_meta_tests_/fixtureValidationTest.cy.ts` spec,
      the `__meta_tests__\testFixture.json` fixture & its type file (`__meta_tests__\testFixture.json.type.ts`) for an example.
- If the `validateFixturesIfPossible` config option is set to `true`, depending on the Cypress launch mode, these things will happen:
    - In interactive mode (`npm run cy:open`):
        - The first time a JSON fixture is loaded within a spec, its type file will be used to validate its contents.
            - The validation takes a few seconds.
            - This can be prevented by validating the fixture separately by using `cy.validateFixture` with the `preventsAutoValidation` option.
    - In headless mode (`npm run cy:run`):
        - All of the JSON fixtures in the `cypress/fixtures` diretory will be validated before the rest of the tests are run.
            - The validation takes a while (a few seconds per fixture).
            - If the validation fails, the whole test run is terminated. 
- You can always validate a fixture manually using the custom `cy.validateFixture` command.
    - You can use this command repeatedly - it will always validate the fixture, no matter the config.
- You can also use the `cy:validate-fixtures` npm script to validate all of the JSON fixtures.

# Snapshot tests
When writing snapshot tests, changing test names will cause the snapshot plugin to create a new snapshot.
- The old snapshot will remain in the corresponding snapshot file until you MANUALLY remove it (delete the export within the .snap file).
- If you only wished to rename the test, you can just rename the old snapshot to match the new name & delete the new snapshot.

# npm scripts
Run all tests (headless):
`npm run cy:run`

Validate JSON fixtures:
`npm run cy:validate-fixtures`

Open Cypress:
`npm run cy:open`

# Screenshots
- In headless mode (using `npm run cy:run`), screenshots are taken automatically on test failures.
- These screenshots will be saved in the `cypress/screenshots` directory.

# Troubleshooting:
- If a `cy.visit(...)` results in a white blank page and there's a `net::ERR_EMPTY_RESPONSE` error in the DevTools console (in interactive mode):
    - Before launching Cypress, set the proxy to `localhost` ([more info here](https://docs.cypress.io/guides/references/proxy-configuration)).
        - Linux: `export HTTP_PROXY=localhost`
        - Windows: `set HTTP_PROXY=localhost`
        - These will only affect the current terminal/cmd session.

# Test instance database setup
- see [test-db/README.md](./test-db/README.md)
