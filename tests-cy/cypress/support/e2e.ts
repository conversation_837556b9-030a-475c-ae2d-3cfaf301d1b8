// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.ts using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// "commands" have a specific meaning within Cypress, so we seperate non-command alterations as "mods"
import './mods/tagged';

beforeEach(() => {
    // don't log XHRs (TAS has too many of them - they would clutter the test log)
    cy.intercept({ resourceType: 'xhr' }, { log: false });
});

// Set the timezone to Europe/Prague before each test run.
// (so that the timezone is always the same, regardless of the machine's timezone)
// source: https://github.com/cypress-io/cypress/issues/1043#issuecomment-2089006560
Cypress.on('test:before:run', () => {
    if (Cypress.browser.family !== 'firefox') {
        // The Emulation.setTimezoneOverride command is part of CDP (Chrome DevTools Protocol).
        // - a request was made to add it to Firefox, but it was marked as WONTFIX: https://bugzilla.mozilla.org/show_bug.cgi?id=1729875
        // A subset of CDP used to be supported by Firefox, but then it was deprecated alltogether.
        // - see https://fxdx.dev/deprecating-cdp-support-in-firefox-embracing-the-future-with-webdriver-bidi/
        // TODO: If we ever start testing in Firefox, we will need to find a different way to pre-set the timezone.

        Cypress.automation('remote:debugger:protocol', {
            command: 'Emulation.setTimezoneOverride',
            params: {
                timezoneId: 'Europe/Prague',
            },
        });
    }
});
