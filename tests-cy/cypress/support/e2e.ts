// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.ts using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// "commands" have a specific meaning within Cypress, so we seperate non-command alterations as "mods"
import './mods/tagged';

beforeEach(() => {
    // don't log XHRs (TAS has too many of them - they would clutter the test log)
    cy.intercept({ resourceType: 'xhr' }, { log: false });
});
