const dragulaDragTo = (
    subject: J<PERSON><PERSON>y<HTMLElement>,
    target: string,
    options?: {
        deviation?: {
            x: number,
            y: number,
        },
    },
) => {
    const deviation = options?.deviation ?? { x: 0, y: 0 };

    cy.get(target).then(($target) => {
        // eslint-disable-next-line cypress/unsafe-to-chain-command
        (cy).wrap(subject)
            .trigger('mousedown', { which: 1 })
            .trigger('mousemove', {
                clientX: $target.get(0).getBoundingClientRect().x + $target.get(0).getBoundingClientRect().width / 2 + deviation.x,
                clientY: $target.get(0).getBoundingClientRect().y + $target.get(0).getBoundingClientRect().height / 2 + deviation.y,
            })
            .trigger('mouseup', { force: true });
    });
};

export default dragulaDragTo;
