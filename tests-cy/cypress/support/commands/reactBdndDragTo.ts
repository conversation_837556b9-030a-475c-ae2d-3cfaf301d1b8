const reactBdndDragTo = (
    subject: JQuery<HTMLElement>,
    target: string | JQuery<HTMLElement> | HTMLElement,
) => {
    const BUTTON_INDEX = 0;
    const SLOPPY_CLICK_THRESHOLD = 10;

    cy.then(() => {
        if (typeof target === 'string') {
            return cy.get(target);
        }

        return cy.wrap(Cypress.$(target));
    }).then($target => {
        const coordsDrop = $target.get(0).getBoundingClientRect();
        const coordsDrag = subject.get(0).getBoundingClientRect();

        const closestCommonAncestor = $target.parents().has(subject.get(0)).first();

        // eslint-disable-next-line cypress/unsafe-to-chain-command
        cy.wrap(subject)
            .trigger('mousedown', {
                button: BUTTON_INDEX,
                clientX: coordsDrag.x,
                clientY: coordsDrag.y,
                force: true,
                scrollBehavior: false,
            })
            .trigger('mousemove', {
                button: BUTTON_INDEX,
                clientX: coordsDrag.x + SLOPPY_CLICK_THRESHOLD,
                clientY: coordsDrag.y,
                force: true,
                scrollBehavior: false,
            });
        // eslint-disable-next-line cypress/unsafe-to-chain-command
        cy.wrap(closestCommonAncestor)
            .trigger('mousemove', {
                button: BUTTON_INDEX,
                clientX: coordsDrop.x,
                clientY: coordsDrop.y,
                force: true,
                scrollBehavior: false,
            })
            .trigger('mouseup', {
                scrollBehavior: false,
            });
    });
};

export default reactBdndDragTo;
