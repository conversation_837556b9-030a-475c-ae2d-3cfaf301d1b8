// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

import '@testing-library/cypress/add-commands';
import 'cypress-plugin-snapshots/commands';
import 'cypress-plugin-steps';
import 'cypress-fail-fast';

import tasLogin from './commands/tasLogin';
import toMatchSnapshotWithout from './commands/toMatchSnapshotWithout';
import trackExpectedRedirect from './commands/trackExpectedRedirect';
import validateFixture, { originalFixtureCmdWithValidation } from './commands/validateFixture';
import reactBdndDragTo from './commands/reactBdndDragTo';
import dragulaDragTo from './commands/dragulaDragTo';

Cypress.Commands.add('tasLogin', tasLogin);
Cypress.Commands.add('toMatchSnapshotWithout', { prevSubject: true }, toMatchSnapshotWithout);
Cypress.Commands.add('trackExpectedRedirect', trackExpectedRedirect);
Cypress.Commands.add('validateFixture', validateFixture);
Cypress.Commands.add('reactBdndDragTo', { prevSubject: true }, reactBdndDragTo);
Cypress.Commands.add('dragulaDragTo', { prevSubject: true }, dragulaDragTo);

if (Cypress.config().isInteractive && Cypress.env('validateFixturesIfPossible')) {
    Cypress.Commands.overwrite('fixture', originalFixtureCmdWithValidation);
}
