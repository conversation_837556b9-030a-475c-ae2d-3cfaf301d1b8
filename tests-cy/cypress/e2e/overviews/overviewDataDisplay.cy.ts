import OverviewTreeUtils from './utils/OverviewTreeUtils';
import { TestOverviewJSONType } from '../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import commonOverviewQueries from './utils/commonOverviewQueries';

const otu = new OverviewTreeUtils();

const testOverviewFixtures = [
    'test-instance/overviews/testOverviews/testOverview1.json',
    'test-instance/overviews/testOverviews/testOverview2.json',
    'test-instance/overviews/testOverviews/testOverview3.json',
];

it.tagged(['testInstanceOnly'], 'The overviews display their data correctly', () => {
    cy.tasLogin('testInstanceAdmin');
    cy.visit('/overviews');

    cy.wrap(testOverviewFixtures).each((testOverviewFixturePath: unknown) => {
        cy.fixture(testOverviewFixturePath as string).then((testOverviewFixture: TestOverviewJSONType) => {
            cy.section(`Test the "${testOverviewFixture.name_cs} overview`);
            {
                cy.intercept('GET', '/cv/*').as(`getOverviewData_${testOverviewFixture.name}`);

                cy.step(`View the "${testOverviewFixture.name_cs}" overview`);
                otu.revealOverviewWithPath(testOverviewFixture.name_cs.split('/'), 'all').then((treeItem) => {
                    cy.wrap(treeItem).click();
                });

                cy.step('Wait for the overview data to load');
                cy.wait(`@getOverviewData_${testOverviewFixture.name}`).its('response.statusCode').should('eq', 200);

                cy.step('Assert the column headers\' text content against the corresponding columns in the overview\'s fixture');
                commonOverviewQueries.table.columnHeaders()
                    .should('have.length', testOverviewFixture.columns.length + 1)
                    .should((columnHeaders) => {
                        columnHeaders.each((columnIndex, column) => {
                            if (columnIndex === testOverviewFixture.columns.length) return;
                            expect(column).to.have.text(testOverviewFixture.columns[columnIndex].name_cs);
                        });
                    });

                cy.step('Assert the real rows against the corresponding sample rows in the overview\'s fixture');
                cy.wrap(testOverviewFixture.sampleRows).each((sampleRow: TestOverviewJSONType['sampleRows'][0]) => {
                    commonOverviewQueries.table.rows().eq(sampleRow.defaultPosition).within(() => {
                        (cy).findAllByRole('gridcell', { timeout: 8000 })
                            .should('have.length', testOverviewFixture.columns.length + 1)
                            .should((cells) => {
                                cells.each((cellIndex, cell) => {
                                    // The last cell is the cell with buttons, which is not in the fixture
                                    if (cellIndex === testOverviewFixture.columns.length) return false;

                                    expect(cell).to.have.text(sampleRow.displayedDataArray[cellIndex]);
                                });
                            });
                    });
                });
            }
        });
    });
});
