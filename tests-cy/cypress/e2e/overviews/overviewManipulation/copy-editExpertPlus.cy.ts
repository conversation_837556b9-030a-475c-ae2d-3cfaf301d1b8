import { ColumnType, TestOverviewJSONType } from '../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';
import parseRawNumberCellValue from '../utils/OverviewManipulationUtils/support/parseRawNumberCellValue';

const primaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview3.json';
const secondaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview2.json';

const omu = new OverviewManipulationUtils();

type ColumnsToKeepType = (
    Record<'text' | 'number' | 'date', {
        name: ColumnType['name'],
        originalIndex: number,
    }>
);

/**
 * Test for copying an overview & advanced editing using the expert edit mode.
 * Tests these expert mode settings:
 * - overview renaming
 * - column addition/removal
 * - column reordering
 * - filtering
 * - sorting
 * - save confirmation dialogue (on cancel)
 * - tab switching checks required field validation
 *
 * **Steps:**
 * - Login as an instance admin & navigate to the overviews page.
 * - Copy an existing overview.
 * - Wait for the overview to save.
 * - Open the expert edit mode.
 * - Delete the overview's names
 * - Attempt to leave to another tab & assert that it is not possible
 * - Set new names for the overview.
 * - Remove some of the overview's columns.
 * - Add new columns to the overview.
 * - Reorder the overview's columns.
 * - Set a complex filter in the filter tab.
 * - Set complex sorting rules in the sorting tab.
 * - Attempt to cancel, then save the changes using the confirmation dialog.
 * - Close the simple edit mode.
 * - Wait for the overview to load.
 * - Assert that the overview name has changed.
 * - Assert that the overview has the correct columns.
 * - Assert that the sorting rules that were set are effective.
 * - Assert that the filters that were set are effective.
 * - Delete the overview.
 */
it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Copy an overview, then edit it using the EXPERT edit mode & use its more complex features', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        // wait for the initially opened overview to load (to prevent these requests being part of the next sections's intercepts)
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        omu.overviewTreeUtils.resetExpandedPathPieces('all');
    }

    cy.section('Copy an existing overview');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 2);

        cy.fixture(primaryTestOverviewFixturePath).then((testOverview: TestOverviewJSONType) => {
            omu.cloneOverview(
                testOverview,
                {
                    cloneNames: {
                        name: `test_copy_editExpertPlus_${new Date().getTime()}`,
                        name_cs: `test_copy_editExpertPlus_${new Date().getTime()} (CS)`,
                    },
                },
            );

            // keep these column for the filtering & sorting tests
            cy.wrap(testOverview.columns).then((columns): ColumnsToKeepType => {
                const toReturn = {
                    text: {
                        name: null,
                        originalIndex: null,
                    },
                    number: {
                        name: null,
                        originalIndex: null,
                    },
                    date: {
                        name: null,
                        originalIndex: null,
                    },
                };

                columns.some((column, columnIndex) => {
                    // only LT to get some rows where these cells are equal (for the sorting test)
                    if (column.type === 'LT' && !column.multichoice) {
                        toReturn.text.name = column.name;
                        toReturn.text.originalIndex = columnIndex;
                        return true;
                    }
                });

                columns.some((column, columnIndex) => {
                    if ((column.type === 'N' && !column.isCheckbox) || (column.type === 'LN' && !column.multichoice)) {
                        toReturn.number.name = column.name;
                        toReturn.number.originalIndex = columnIndex;
                        return true;
                    }
                });

                columns.some((column, columnIndex) => {
                    if (column.type === 'D' || (column.type === 'LD' && !column.multichoice)) {
                        toReturn.date.name = column.name;
                        toReturn.date.originalIndex = columnIndex;
                        return true;
                    }
                });

                return toReturn;
            }).as('columnsToKeep');
        });

    }

    cy.section('Wait for the overview to save');
    {
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Open the expert edit mode');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);

        cy.step('Open the expert edit mode');
        cy.findAllByRole('button').contains('Expertní mód').click();

        cy.step('Wait for the expert edit mode data to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Delete the overview\'s names');
    {
        cy.findByLabelText('Název *').click();
        cy.findByLabelText('Název *').clear();
        cy.findByLabelText('Název CS').click();
        cy.findByLabelText('Název CS').clear();
    }

    cy.section('Attempt to leave to another tab & assert that it is not possible');
    {
        cy.step('Try to switch to the filter tab');
        cy.findAllByRole('button').contains('Filtr').click();

        cy.step('Assert that the name fields are still visible - the tab switch was not successful');
        cy.findByLabelText('Název *').should('be.visible');
        cy.findByLabelText('Název CS').should('be.visible');
    }

    cy.section('Change the overview\'s names');
    {
        const name = `TST-${new Date().getTime()}-CPY-EDT-EXPRT-PLS`;
        const name_cs = `(CS)_TST-${new Date().getTime()}-CPY-EDT-EXPRT-PLS`;

        cy.findByLabelText('Název *').click();
        cy.findByLabelText('Název *').type(name);
        cy.findByLabelText('Název CS').click();
        cy.findByLabelText('Název CS').type(name_cs);

        omu.overviewStateHandler.get().then((overviewState) => {
            omu.overviewStateHandler.set({
                ...overviewState,
                name,
                name_cs,
            });
        });
    }

    cy.section('Remove some of the overview\'s columns');
    {
        // remove 3 columns that are not in the columnsToKeep list
        cy.get('@columnsToKeep').then((columnsToKeep) => {
            const columnsToKeepNames = Object.values(columnsToKeep as unknown).map((column) => column.name) as ColumnType['name'][];

            console.log('columnsToKeepNames', columnsToKeepNames);

            const indexesToRemove = [];

            omu.overviewStateHandler.get().then((overviewState) => {
                for (let i = 0; i < overviewState.columns.length; i++) {
                    if (!columnsToKeepNames.some((name) => name === overviewState.columns[i].name)) {
                        indexesToRemove.push(i);
                    }

                    if (indexesToRemove.length === 3) {
                        return;
                    }
                }
            });

            cy.wrap(indexesToRemove.sort((a, b) => a - b)).each((columnIndex: number, removedAmount) => {
                const actualIndex = columnIndex - removedAmount;
                omu.expertEditUtils.columnMultiselectUtils.removeColumn(actualIndex);
            });
        });
    }

    cy.section('Add new columns to the overview');
    {
        cy.fixture(primaryTestOverviewFixturePath).then((primaryOverview: TestOverviewJSONType) => {
            (cy).fixture(secondaryTestOverviewFixturePath)
                .then((secondaryOverview: TestOverviewJSONType) => (
                    // keep only secondary overview columns that are not already present in the primary overview
                    secondaryOverview.columns.filter((secondaryColumn) => (
                        primaryOverview.columns.findIndex((primaryColumn: ColumnType) => (
                            primaryColumn.name === (secondaryColumn as ColumnType).name
                        )) === -1
                    ))
                ))
                .then((newColumns: unknown) => (newColumns as ColumnType[]).splice(0, 3))
                .each((columnToAdd: ColumnType) => {
                    omu.expertEditUtils.columnMultiselectUtils.addColumn(columnToAdd);
                });
        });
    }

    cy.section('Reorder the overview\'s columns');
    {
        omu.overviewStateHandler.get().then((overviewState) => {
            omu.expertEditUtils.columnMultiselectUtils.moveColumn(0, 1);
            omu.expertEditUtils.columnMultiselectUtils.moveColumn(1, 2);
            omu.expertEditUtils.columnMultiselectUtils.moveColumn(0, overviewState.columns.length - 1);
        });
    }

    cy.section('Set a complex filter in the filter tab');
    {
        cy.findAllByRole('button').contains('Filtr').click();

        cy.fixture(primaryTestOverviewFixturePath).then((primaryOverview: TestOverviewJSONType) => {
            cy.get('@columnsToKeep').then((colsToKeep) => {
                const columnsToKeep = colsToKeep as unknown as ColumnsToKeepType;

                omu.expertEditUtils.filterTabUtils.addFilter(0, {
                    columnName: columnsToKeep.text.name,
                    operator: 'contains',
                    value: 'a',
                });

                omu.expertEditUtils.filterTabUtils.addFilter(0, {
                    junction: 'AND',
                    columnName: columnsToKeep.date.name,
                    operator: 'lt',
                    value: primaryOverview.sampleRows[2].displayedDataArray[columnsToKeep.date.originalIndex],
                });

                omu.expertEditUtils.filterTabUtils.addFilterGroup('OR');

                // change the default tproc_name filter
                omu.expertEditUtils.filterTabUtils.changeFilter(1, 0, {
                    columnName: columnsToKeep.number.name,
                    operator: 'gte',
                    value: String(
                        parseRawNumberCellValue(
                            primaryOverview.sampleRows[1].displayedDataArray[columnsToKeep.number.originalIndex],
                        ),
                    ),
                });

                omu.expertEditUtils.filterTabUtils.addFilter(1, {
                    junction: 'AND',
                    columnName: columnsToKeep.number.name,
                    operator: 'notEqual',
                    value: String(
                        parseRawNumberCellValue(
                            primaryOverview.sampleRows[0].displayedDataArray[columnsToKeep.number.originalIndex],
                        ),
                    ),
                });
            });
        });
    }

    cy.section('Set complex sorting rules in the sorting tab');
    {
        cy.findAllByRole('button').contains('Řazení').click();

        cy.get('@columnsToKeep').then((colsToKeep) => {
            const columnsToKeep = colsToKeep as unknown as ColumnsToKeepType;

            omu.expertEditUtils.sortingTabUtils.addSortingRule(columnsToKeep.text.name, 'descending');
            omu.expertEditUtils.sortingTabUtils.addSortingRule(columnsToKeep.date.name, 'ascending');
            omu.expertEditUtils.sortingTabUtils.addSortingRule(columnsToKeep.number.name, 'ascending');
        });
    }

    cy.section('Attempt to close, then save the changes using the confirmation dialog');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.step('Click the close button');
        cy.findAllByRole('button').contains('Zavřít').click();

        cy.step('Assert that the confirmation dialog appeared');
        cy.findAllByRole('dialog').contains('Uložit provedené změny?').should('be.visible');

        cy.step('Save the overview (using the "Yes" button in the confirmation dialog)');
        cy.findAllByRole('button').contains('Ano').click();

        cy.step('Wait for the overview to save');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Close the simple edit mode');
    {
        cy.findAllByRole('button').contains('Zavřít').click();
    }

    cy.section('Wait for the overview to load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);
    }

    cy.section('Assert that the overview name has changed');
    {
        omu.assertionUtils.assertDisplayName({ view: 'main' });
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    cy.section('Assert that the filters that were set are effective');
    {
        omu.assertionUtils.assertFiltering();
    }

    cy.section('Assert that the sorting rules that were set are effective');
    {
        omu.assertionUtils.assertSorting(5);
    }

    // Delete the overview (cy.section called in omu.deleteOverview)
    {
        omu.deleteOverview();
    }
});
