import { ColumnType, TestOverviewJSONType } from '../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';

const primaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview2.json';
const secondaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview3.json';

const omu = new OverviewManipulationUtils();

/**
 * Test for copying an overview & basic editing using the expert edit mode.
 * Tests these expert mode settings:
 * - overview renaming
 * - column addition/removal
 *
 * **Steps:**
 * - Login as an instance admin & navigate to the overviews page.
 * - Copy an existing overview.
 * - Wait for the overview to save.
 * - Open the expert edit mode.
 * - Change the overview's names.
 * - Remove some of the overview's columns.
 * - Add new columns to the overview.
 * - Save & close the expert edit mode.
 * - Close the simple edit mode.
 * - Wait for the overview to load.
 * - Assert that the overview has the correct columns.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Copy an overview, then edit it using the EXPERT edit mode', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        // wait for the initially opened overview to load (to prevent these requests being part of the next sections's intercepts)
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        omu.overviewTreeUtils.resetExpandedPathPieces('all');
    }

    cy.section('Copy an existing overview');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 2);

        cy.fixture(primaryTestOverviewFixturePath).then((testOverview: TestOverviewJSONType) => {
            omu.cloneOverview(
                testOverview,
                {
                    cloneNames: {
                        name: `test_copy_editExpert_${new Date().getTime()}`,
                        name_cs: `test_copy_editExpert_${new Date().getTime()} (CS)`,
                    },
                },
            );
        });
    }

    cy.section('Wait for the overview to save');
    {
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Open the expert edit mode');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);

        cy.step('Open the expert edit mode');
        cy.findAllByRole('button').contains('Expertní mód').click();

        cy.step('Wait for the expert edit mode data to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Change the overview\'s names');
    {
        const name = `TST-${new Date().getTime()}-CPY-EDT-EXPRT`;
        const name_cs = `(CS)_TST-${new Date().getTime()}-CPY-EDT-EXPRT`;

        cy.findByLabelText('Název *').clear();
        cy.findByLabelText('Název *').type(name);
        cy.findByLabelText('Název CS').clear();
        cy.findByLabelText('Název CS').type(name_cs);

        omu.overviewStateHandler.get().then((overviewState) => {
            omu.overviewStateHandler.set({
                ...overviewState,
                name,
                name_cs,
            });
        });
    }

    cy.section('Remove some of the overview\'s columns');
    {
        omu.overviewStateHandler.get().then((overviewState) => {
            omu.expertEditUtils.columnMultiselectUtils.removeColumn(overviewState.columns.length - 1);
            omu.expertEditUtils.columnMultiselectUtils.removeColumn(0);
            omu.expertEditUtils.columnMultiselectUtils.removeColumn(Math.floor(overviewState.columns.length / 2));
        });
    }

    cy.section('Add new columns to the overview');
    {
        cy.fixture(primaryTestOverviewFixturePath).then((primaryOverview: TestOverviewJSONType) => {
            (cy).fixture(secondaryTestOverviewFixturePath)
                .then((secondaryOverview: TestOverviewJSONType) => (
                    // keep only secondary overview columns that are not already present in the primary overview
                    secondaryOverview.columns.filter((secondaryColumn) => (
                        primaryOverview.columns.findIndex((primaryColumn: ColumnType) => (
                            primaryColumn.name === (secondaryColumn as ColumnType).name
                        )) === -1
                    ))
                ))
                .then((newColumns: unknown) => (newColumns as ColumnType[]).splice(0, 3))
                .each((columnToAdd: ColumnType) => {
                    omu.expertEditUtils.columnMultiselectUtils.addColumn(columnToAdd);
                });
        });
    }

    cy.section('Save & close the expert edit mode');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.step('Save the expert edit mode');
        cy.findAllByRole('button').contains('Uložit a zavřít').click();

        cy.step('Wait for the overview to save');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Close the simple edit mode');
    {
        cy.findAllByRole('button').contains('Zavřít').click();
    }

    cy.section('Wait for the overview to load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    // Delete the overview (cy.section called in omu.deleteOverview)
    {
        omu.deleteOverview();
    }
});
