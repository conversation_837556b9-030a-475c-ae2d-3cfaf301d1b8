import { ColumnType, TestOverviewJSONType } from '../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';

const primaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview1.json';
const secondaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview3.json';
const tertiaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview2.json';

const omu = new OverviewManipulationUtils();

/**
 * Test for creating an overview using both the simple and expert edit modes (only basic editing).
 * Tests:
 * - column addition/removal (both modes)
 * - overview renaming (expert mode)
 * - changes in each mode are reflected in the other mode
 * - interplay between the two edit modes
 *
 * **Steps:**
 * - Login as an instance admin & navigate to the overviews page.
 * - Create an overview.
 * - Wait for the overview to save & load.
 * - Pick some columns using the simple edit mode.
 * - Attempt to open the expert edit mode & assert that the save confirmation dialog appears
 * - Open the expert edit mode (confirm the dialog).
 * - Assert that the column multiselect reflects the already-selected columns.
 * - Change the overview's names.
 * - Remove some of the overview's columns.
 * - Add some new columns to the overview.
 * - Save & close the expert edit mode.
 * - Wait for the overview to save.
 * - Assert that the simple edit mode reflects the overview name change.
 * - Assert that the simple edit mode table reflects the column changes.
 * - Remove some of the overview's columns using the simple edit mode.
 * - Add some new columns to the overview using the simple edit mode.
 * - Save the overview.
 * - Wait for the overview to save & load.
 * - Assert that the overview has the new name.
 * - Assert that the overview has the correct columns.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Create an overview using BOTH edit modes', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        // wait for the initially opened overview to load (to prevent these requests being part of the next sections's intercepts)
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        omu.overviewTreeUtils.resetExpandedPathPieces('all');
    }

    // Create a new overview (cy.section called in omu.createOverview)
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 2);

        omu.createOverview({
            baseTemplateName: 'template_overviewData',
            newOverviewNames: {
                name: `test_createComplex_${new Date().getTime()}`,
                name_cs: `test_createComplex_${new Date().getTime()} (CS)`,
            },
        });
    }

    cy.section('Wait for the overview to save & load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Pick some columns using the simple edit mode');
    {
        cy.findAllByRole('button').contains('Sloupce').click();

        omu.simpleEditUtils.columnListUtils.removeColumn(0);

        cy.fixture(primaryTestOverviewFixturePath).then((referenceOverview: TestOverviewJSONType) => {
            cy.wrap(
                (referenceOverview.columns.slice(0, referenceOverview.columns.length - 2)).reverse(),
            ).each((referenceColumn: ColumnType) => {
                omu.simpleEditUtils.columnListUtils.addColumn(referenceColumn);
            });
        });

        cy.get('body').click(); // closes the column list popover
    }

    cy.section('Attempt to open the expert edit mode & assert that the save confirmation dialog appears');
    {
        cy.findAllByRole('button').contains('Expertní mód').click();
        cy.contains('Uložit provedené změny?').should('be.visible');
    }

    cy.section('Open the expert edit mode (confirm the dialog)');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.step('Confirm the dialog');
        cy.findAllByRole('button').contains('Ano').click();

        cy.step('Wait for the overview to save');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        cy.step('Wait for the expert edit mode data to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the column multiselect reflects the already-selected columns');
    {
        omu.expertEditUtils.columnMultiselectUtils.queries.columnMultiselectOptionsWrapper('right').within(() => {
            omu.overviewStateHandler.get().then((overviewState) => {
                cy.wrap(overviewState.columns).each((column: ColumnType) => {
                    cy.findAllByRole('button').contains(column.name_cs).should('exist');
                });
            });
        });
    }

    cy.section('Change the overview\'s names');
    {
        const name = `TST-${new Date().getTime()}-CRT-CMPLX`;
        const name_cs = `(CS)_TST-${new Date().getTime()}-CRT-CMPLX`;

        cy.findByLabelText('Název *').clear();
        cy.findByLabelText('Název *').type(name);
        cy.findByLabelText('Název CS').clear();
        cy.findByLabelText('Název CS').type(name_cs);

        omu.overviewStateHandler.get().then((overviewState) => {
            omu.overviewStateHandler.set({
                ...overviewState,
                name,
                name_cs,
            });
        });
    }

    cy.section('Remove some of the overview\'s columns');
    {
        omu.overviewStateHandler.get().then((overviewState) => {
            omu.expertEditUtils.columnMultiselectUtils.removeColumn(overviewState.columns.length - 1);
            omu.expertEditUtils.columnMultiselectUtils.removeColumn(0);
        });
    }

    cy.section('Add new columns to the overview');
    {
        cy.fixture(primaryTestOverviewFixturePath).then((primaryOverview: TestOverviewJSONType) => {
            (cy).fixture(secondaryTestOverviewFixturePath)
                .then((secondaryOverview: TestOverviewJSONType) => (
                    // keep only secondary overview columns that are not already present in the primary overview
                    secondaryOverview.columns.filter((secondaryColumn) => (
                        primaryOverview.columns.findIndex((primaryColumn: ColumnType) => (
                            primaryColumn.name === (secondaryColumn as ColumnType).name
                        )) === -1
                    ))
                ))
                .then((newColumns: unknown) => (newColumns as ColumnType[]).splice(0, 3))
                .each((columnToAdd: ColumnType) => {
                    omu.expertEditUtils.columnMultiselectUtils.addColumn(columnToAdd);
                });
        });
    }

    cy.section('Save & close the expert edit mode');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.findAllByRole('button').contains('Uložit a zavřít').click();
    }

    cy.section('Wait for the overview to save');
    {
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the simple edit mode reflects the overview name change');
    {
        omu.assertionUtils.assertDisplayName({ view: 'simpleEditMode' });
    }

    cy.section('Assert that the simple edit mode table reflects the column changes');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    cy.section('Remove some of the overview\'s columns using the simple edit mode');
    {
        cy.findAllByRole('button').contains('Sloupce').click();

        omu.overviewStateHandler.get().then((overviewState) => {
            omu.simpleEditUtils.columnListUtils.removeColumn(overviewState.columns.length - 1);
            omu.simpleEditUtils.columnListUtils.removeColumn(0);
            omu.simpleEditUtils.columnListUtils.removeColumn(Math.floor(overviewState.columns.length / 2));
        });
    }

    cy.section('Add some new columns to the overview using the simple edit mode');
    {
        cy.fixture(tertiaryTestOverviewFixturePath).then((tertiaryOverview: TestOverviewJSONType) => {
            omu.overviewStateHandler.get().then((overviewState) => {
                // keep only tertiary overview columns that are not already present in the overview
                const newColumns = tertiaryOverview.columns.filter((tertiaryColumn) => (
                    overviewState.columns.findIndex((column: ColumnType) => (
                        column.name === (tertiaryColumn as ColumnType).name
                    )) === -1
                ));

                cy.wrap(newColumns.splice(0, 3)).each((columnToAdd: ColumnType) => {
                    omu.simpleEditUtils.columnListUtils.addColumn(columnToAdd);
                });
            });
        });
    }

    cy.section('Save the overview');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.get('body').click(); // closes the column list popover
        cy.findAllByRole('button').contains('Uložit').click();
    }

    cy.section('Wait for the overview to save & load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the overview has the new name');
    {
        omu.assertionUtils.assertDisplayName({ view: 'main' });
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    // Delete the overview (cy.section called in omu.deleteOverview)
    {
        omu.deleteOverview();
    }
});
