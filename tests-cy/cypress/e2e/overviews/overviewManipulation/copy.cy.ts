import { TestOverviewJSONType } from '../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';

const testOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview2.json';

const omu = new OverviewManipulationUtils();

/**
 * Test for copying an overview.
 *
 * **Steps:**
 * - Login as an instance admin & navigate to the overviews page.
 * - Copy an existing overview.
 * - Close the simple edit mode.
 * - Assert that the overview has the correct columns.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Copy an overview', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');
    }

    // Copy an existing overview (cy.section called in omu.createOverview)
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);

        cy.fixture(testOverviewFixturePath).then((testOverview: TestOverviewJSONType) => {
            omu.cloneOverview(
                testOverview,
                {
                    cloneNames: {
                        name: `test_copy_${new Date().getTime()}`,
                        name_cs: `test_copy_${new Date().getTime()} (CS)`,
                    },
                },
            );
        });

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Close the simple edit mode');
    {
        cy.findAllByRole('button').contains('Zrušit').click();
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    // Delete the overview (cy.section called in omu.deleteOverview)
    {
        omu.deleteOverview();
    }
});
