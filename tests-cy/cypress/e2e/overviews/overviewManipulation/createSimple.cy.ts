import { ColumnType, TestOverviewJSONType } from 'cypress/fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';

const testOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview1.json';

const omu = new OverviewManipulationUtils();

/**
 * Test for creating an overview using the simple edit mode.
 *
 * **Steps:**
 * - Login as an instance admin & navigate to the overviews page.
 * - Create a new overview.
 * - Wait for the overview to save & load.
 * - Pick the overview's columns.
 * - Save the overview.
 * - Wait for the overview to save & load.
 * - Assert that the overview has the correct columns.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Create an overview using the SIMPLE edit mode', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        // wait for the initially opened overview to load (to prevent these requests being part of the next sections's intercepts)
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        omu.overviewTreeUtils.resetExpandedPathPieces('all');
    }

    // Create a new overview (cy.section called in omu.createOverview)
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 2);

        omu.createOverview({
            baseTemplateName: 'template_overviewData',
            newOverviewNames: {
                name: `test_createSimple_${new Date().getTime()}`,
                name_cs: `test_createSimple_${new Date().getTime()} (CS)`,
            },
        });
    }

    cy.section('Wait for the overview to save & load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Pick the overview\'s columns');
    {
        cy.step('Open the column list');
        cy.findAllByRole('button').contains('Sloupce').click();

        cy.step('Remove the system case name column');
        omu.simpleEditUtils.columnListUtils.removeColumn(0);

        cy.step('Add some columns to the overview');
        cy.fixture(testOverviewFixturePath).then((referenceOverview: TestOverviewJSONType) => {
            cy.wrap(referenceOverview.columns.reverse()).each((referenceColumn: ColumnType) => {
                omu.simpleEditUtils.columnListUtils.addColumn(referenceColumn);
            });
        });
    }

    cy.section('Save the overview');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.get('body').click(); // closes the column list popover
        cy.findAllByRole('button').contains('Uložit').click();
    }

    cy.section('Wait for the overview to save & load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    // Delete the overview (cy.section called in omu.createOverview)
    {
        omu.deleteOverview();
    }
});
