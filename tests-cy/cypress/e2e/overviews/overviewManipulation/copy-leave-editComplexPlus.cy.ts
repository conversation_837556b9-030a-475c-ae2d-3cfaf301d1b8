import { ColumnType, TestOverviewJSONType } from '../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';
import parseRawNumberCellValue from '../utils/OverviewManipulationUtils/support/parseRawNumberCellValue';

const primaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview3.json';
const secondaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview2.json';
const tertiaryTestOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview1.json';

const omu = new OverviewManipulationUtils();

type ColumnsToKeepType = (
    Record<'text' | 'number' | 'date', {
        name: ColumnType['name'],
        originalIndex: number,
    }>
);

/**
 * Test for copying an overview using both the simple and expert edit modes (advanced editing).
 * Tests:
 * - column addition/removal (both modes)
 * - overview renaming (expert mode)
 * - column reordering (expert mode)
 * - filtering (expert mode)
 * - sorting (simple mode)
 * - changes in each mode are reflected in the other mode
 * - interplay between the two edit modes
 *
 * **Steps:**
 * - Login as an instance admin & navigate to the overviews page.
 * - Copy an overview.
 * - Close the simple edit mode.
 * - Reload the page.
 * - Open the simple edit mode again.
 * - Add some columns using the simple edit mode.
 * - Attempt to open the expert edit mode & assert that the save confirmation dialog appears
 * - Open the expert edit mode (confirm the dialog).
 * - Assert that the column multiselect reflects the already-selected columns.
 * - Change the overview's names.
 * - Remove some of the overview's columns.
 * - Add some new columns to the overview.
 * - Reorder the overview's columns.
 * - Set a complex filter in the filter tab.
 * - Attempt to close, then save the changes using the confirmation dialog.
 * - Assert that the simple edit mode reflects the overview name change.
 * - Assert that the simple edit mode table reflects the column changes.
 * - Assert that the simple edit mode table reflects the applied filters.
 * - Remove some of the overview's columns using the simple edit mode.
 * - Add some new columns to the overview using the simple edit mode.
 * - Sort the overview by a column.
 * - Save the overview.
 * - Assert that the overview has the new name.
 * - Assert that the overview has the correct columns.
 * - Assert that the sorting rule that was set is effective.
 * - Assert that the filters that were set are effective.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Create an overview using BOTH edit modes', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');
    }

    cy.section('Copy an existing overview');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);

        cy.fixture(primaryTestOverviewFixturePath).then((testOverview: TestOverviewJSONType) => {
            omu.cloneOverview(
                testOverview,
                {
                    cloneNames: {
                        name: `test_copy_editExpertPlus_${new Date().getTime()}`,
                        name_cs: `test_copy_editExpertPlus_${new Date().getTime()} (CS)`,
                    },
                },
            );

            // keep these column for the filtering & sorting tests
            cy.wrap(testOverview.columns).then((columns): ColumnsToKeepType => {
                const toReturn = {
                    text: {
                        name: null,
                        originalIndex: null,
                    },
                    number: {
                        name: null,
                        originalIndex: null,
                    },
                    date: {
                        name: null,
                        originalIndex: null,
                    },
                };

                columns.some((column, columnIndex) => {
                    // only LT to get some rows where these cells are equal (for the sorting test)
                    if (column.type === 'LT' && !column.multichoice) {
                        toReturn.text.name = column.name;
                        toReturn.text.originalIndex = columnIndex;
                        return true;
                    }
                });

                columns.some((column, columnIndex) => {
                    if ((column.type === 'N' && !column.isCheckbox) || (column.type === 'LN' && !column.multichoice)) {
                        toReturn.number.name = column.name;
                        toReturn.number.originalIndex = columnIndex;
                        return true;
                    }
                });

                columns.some((column, columnIndex) => {
                    if (column.type === 'D' || (column.type === 'LD' && !column.multichoice)) {
                        toReturn.date.name = column.name;
                        toReturn.date.originalIndex = columnIndex;
                        return true;
                    }
                });

                return toReturn;
            }).as('columnsToKeep');
        });

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Close the simple edit mode');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.findAllByRole('button').contains('Zrušit').click();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Reload the page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.reload();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Open the simple edit mode again');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);

        cy.findAllByRole('button').contains('Upravit přehled').click();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Add some columns using the simple edit mode');
    {
        cy.findAllByRole('button').contains('Sloupce').click();

        cy.fixture(tertiaryTestOverviewFixturePath).then((referenceOverview: TestOverviewJSONType) => {
            cy.wrap(
                referenceOverview.columns.reverse().splice(0, 2),
            ).each((referenceColumn: ColumnType) => {
                omu.simpleEditUtils.columnListUtils.addColumn(referenceColumn);
            });
        });

        cy.get('body').click(); // closes the column list popover
    }

    cy.section('Attempt to open the expert edit mode & assert that the save confirmation dialog appears');
    {
        cy.findAllByRole('button').contains('Expertní mód').click();
        cy.contains('Uložit provedené změny?').should('be.visible');
    }

    cy.section('Open the expert edit mode (confirm the dialog)');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 1);

        cy.findAllByRole('button').contains('Ano').click();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the column multiselect reflects the already-selected columns');
    {
        omu.expertEditUtils.columnMultiselectUtils.queries.columnMultiselectOptionsWrapper('right').within(() => {
            omu.overviewStateHandler.get().then((overviewState) => {
                cy.wrap(overviewState.columns).each((column: ColumnType) => {
                    cy.findAllByRole('button').contains(column.name_cs).should('exist');
                });
            });
        });
    }

    cy.section('Change the overview\'s names');
    {
        const name = `TST-${new Date().getTime()}-CPY-LV-EDT-CMPLX-PLS`;
        const name_cs = `(CS)_TST-${new Date().getTime()}-CPY-LV-EDT-CMPLX-PLS`;

        cy.findByLabelText('Název *').clear();
        cy.findByLabelText('Název *').type(name);
        cy.findByLabelText('Název CS').clear();
        cy.findByLabelText('Název CS').type(name_cs);

        omu.overviewStateHandler.get().then((overviewState) => {
            omu.overviewStateHandler.set({
                ...overviewState,
                name,
                name_cs,
            });
        });
    }

    cy.section('Remove some of the overview\'s columns');
    {
        // remove 4 columns that are not in the columnsToKeep list
        cy.get('@columnsToKeep').then((columnsToKeep) => {
            const columnsToKeepNames = Object.values(columnsToKeep as unknown).map((column) => column.name) as ColumnType['name'][];

            console.log('columnsToKeepNames', columnsToKeepNames);

            const indexesToRemove = [];

            omu.overviewStateHandler.get().then((overviewState) => {
                for (let i = 0; i < overviewState.columns.length; i++) {
                    if (!columnsToKeepNames.some((name) => name === overviewState.columns[i].name)) {
                        indexesToRemove.push(i);
                    }

                    if (indexesToRemove.length === 4) {
                        return;
                    }
                }
            });

            cy.wrap(indexesToRemove.sort((a, b) => a - b)).each((columnIndex: number, removedAmount) => {
                const actualIndex = columnIndex - removedAmount;
                omu.expertEditUtils.columnMultiselectUtils.removeColumn(actualIndex);
            });
        });
    }

    cy.section('Add new columns to the overview');
    {
        cy.fixture(primaryTestOverviewFixturePath).then((primaryOverview: TestOverviewJSONType) => {
            (cy).fixture(secondaryTestOverviewFixturePath)
                .then((secondaryOverview: TestOverviewJSONType) => (
                    // keep only secondary overview columns that are not already present in the primary overview
                    secondaryOverview.columns.filter((secondaryColumn) => (
                        primaryOverview.columns.findIndex((primaryColumn: ColumnType) => (
                            primaryColumn.name === (secondaryColumn as ColumnType).name
                        )) === -1
                    ))
                ))
                .then((newColumns: unknown) => (newColumns as ColumnType[]).splice(0, 2))
                .each((columnToAdd: ColumnType) => {
                    omu.expertEditUtils.columnMultiselectUtils.addColumn(columnToAdd);
                });
        });
    }

    cy.section('Reorder the overview\'s columns');
    {
        omu.overviewStateHandler.get().then((overviewState) => {
            omu.expertEditUtils.columnMultiselectUtils.moveColumn(0, 2);
            omu.expertEditUtils.columnMultiselectUtils.moveColumn(2, 1);
            omu.expertEditUtils.columnMultiselectUtils.moveColumn(overviewState.columns.length - 1, 0);
        });
    }

    cy.section('Set a complex filter in the filter tab');
    {
        cy.findAllByRole('button').contains('Filtr').click();

        cy.fixture(primaryTestOverviewFixturePath).then((primaryOverview: TestOverviewJSONType) => {
            cy.get('@columnsToKeep').then((colsToKeep) => {
                const columnsToKeep = colsToKeep as unknown as ColumnsToKeepType;

                omu.expertEditUtils.filterTabUtils.addFilter(0, {
                    columnName: columnsToKeep.text.name,
                    operator: 'contains',
                    value: 'e',
                });

                omu.expertEditUtils.filterTabUtils.addFilterGroup('OR');

                // change the default tproc_name filter
                omu.expertEditUtils.filterTabUtils.changeFilter(1, 0, {
                    columnName: columnsToKeep.date.name,
                    operator: 'gt',
                    value: primaryOverview.sampleRows[0].displayedDataArray[columnsToKeep.date.originalIndex],
                });

                omu.expertEditUtils.filterTabUtils.addFilter(1, {
                    junction: 'AND',
                    columnName: columnsToKeep.number.name,
                    operator: 'equal',
                    value: String(
                        parseRawNumberCellValue(
                            primaryOverview.sampleRows[2].displayedDataArray[columnsToKeep.number.originalIndex],
                        ),
                    ),
                });

                omu.expertEditUtils.filterTabUtils.addFilter(1, {
                    junction: 'AND',
                    columnName: columnsToKeep.number.name,
                    operator: 'gte',
                    value: String(
                        parseRawNumberCellValue(
                            primaryOverview.sampleRows[1].displayedDataArray[columnsToKeep.number.originalIndex],
                        ),
                    ),
                });
            });
        });
    }

    cy.section('Attempt to close, then save the changes using the confirmation dialog');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.step('Click the close button');
        cy.findAllByRole('button').contains('Zavřít').click();

        cy.step('Assert that the confirmation dialog appeared');
        cy.findAllByRole('dialog').contains('Uložit provedené změny?').should('be.visible');

        cy.step('Save the overview (using the "Yes" button in the confirmation dialog)');
        cy.findAllByRole('button').contains('Ano').click();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the simple edit mode reflects the overview name change');
    {
        omu.assertionUtils.assertDisplayName();
    }

    cy.section('Assert that the simple edit mode table reflects the column changes');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    cy.section('Assert that the simple edit mode table reflects the applied filters');
    {
        omu.assertionUtils.assertFiltering();
    }

    cy.section('Remove some of the overview\'s columns using the simple edit mode');
    {
        cy.findAllByRole('button').contains('Sloupce').click();

        // remove 2 columns that are not in the columnsToKeep list
        cy.get('@columnsToKeep').then((columnsToKeep) => {
            const columnsToKeepNames = Object.values(columnsToKeep as unknown).map((column) => column.name) as ColumnType['name'][];

            console.log('columnsToKeepNames', columnsToKeepNames);

            const indexesToRemove = [];

            omu.overviewStateHandler.get().then((overviewState) => {
                for (let i = 0; i < overviewState.columns.length; i++) {
                    if (!columnsToKeepNames.some((name) => name === overviewState.columns[i].name)) {
                        indexesToRemove.push(i);
                    }

                    if (indexesToRemove.length === 2) {
                        return;
                    }
                }
            });

            cy.wrap(indexesToRemove.sort((a, b) => a - b)).each((columnIndex: number, removedAmount) => {
                const actualIndex = columnIndex - removedAmount;
                omu.simpleEditUtils.columnListUtils.removeColumn(actualIndex);
            });
        });
    }

    cy.section('Add some new columns to the overview using the simple edit mode');
    {
        cy.fixture(tertiaryTestOverviewFixturePath).then((tertiaryOverview: TestOverviewJSONType) => {
            omu.overviewStateHandler.get().then((overviewState) => {
                // keep only tertiary overview columns that are not already present in the overview
                const newColumns = tertiaryOverview.columns.filter((tertiaryColumn) => (
                    overviewState.columns.findIndex((column: ColumnType) => (
                        column.name === (tertiaryColumn as ColumnType).name
                    )) === -1
                ));

                cy.wrap(newColumns.splice(0, 3)).each((columnToAdd: ColumnType) => {
                    omu.simpleEditUtils.columnListUtils.addColumn(columnToAdd);
                });
            });
        });

        cy.get('body').click(); // closes the column list popover
    }

    cy.section('Sort the overview by a column');
    {
        cy.get('@columnsToKeep').then((columnsToKeep: unknown) => {
            omu.simpleEditUtils.tableUtils.setSortingRule((columnsToKeep as ColumnsToKeepType).date.name, 'descending');
        });
    }

    cy.section('Save the overview');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.get('body').click(); // closes the column list popover
        cy.findAllByRole('button').contains('Uložit').click();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the overview has the new name');
    {
        omu.assertionUtils.assertDisplayName();
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    cy.section('Assert that the sorting rule that was set is effective');
    {
        omu.assertionUtils.assertSorting(5, 'simple');
    }

    cy.section('Assert that the filters that were set are effective');
    {
        omu.assertionUtils.assertFiltering();
    }
});
