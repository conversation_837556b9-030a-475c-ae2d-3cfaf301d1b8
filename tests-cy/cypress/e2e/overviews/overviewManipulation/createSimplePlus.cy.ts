import { ColumnType, TestOverviewJSONType } from 'cypress/fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';

const testOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview1.json';

const omu = new OverviewManipulationUtils();

/**
 * Test for creating an overview using the simple edit mode.
 * Also tests simple mode sorting & save confirmation dialogues.
 *
 * **Steps:**
 * - <PERSON>gin as an instance admin & navigate to the overviews page.
 * - Create a new overview.
 * - Wait for the overview to save & load.
 * - Pick the overview's columns (alternate between adding & removing columns).
 * - Attempt to close, then save the overview using the confirmation dialog.
 * - Wait for the overview to save & load.
 * - Assert that the overview has the correct columns.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Create an overview using the SIMPLE edit mode & use its more complex features', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        // wait for the initially opened overview to load (to prevent these requests being part of the next sections's intercepts)
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        omu.overviewTreeUtils.resetExpandedPathPieces('all');
    }

    // Create a new overview (cy.section called in omu.createOverview)
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 2);

        omu.createOverview({
            baseTemplateName: 'template_overviewData',
            newOverviewNames: {
                name: `test_createSimplePlus_${new Date().getTime()}`,
                name_cs: `test_createSimplePlus_${new Date().getTime()} (CS)`,
            },
        });
    }

    cy.section('Wait for the overview to save & load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Pick the overview\'s columns (alternate between adding & removing columns)');
    {
        cy.step('Open the column list');
        cy.findAllByRole('button').contains('Sloupce').click();

        cy.step('Remove the system case name column');
        omu.simpleEditUtils.columnListUtils.removeColumn(0);

        cy.fixture(testOverviewFixturePath).then((referenceOverview: TestOverviewJSONType) => {
            cy.step('Add 2 columns to the overview');
            cy.wrap(referenceOverview.columns.reverse().slice(0, 2)).each((referenceColumn: ColumnType) => {
                omu.simpleEditUtils.columnListUtils.addColumn(referenceColumn);
            });

            // [column0, column1]

            cy.step('Remove the first of the 2 added columns');
            omu.simpleEditUtils.columnListUtils.removeColumn(0);

            // [column1]

            cy.step('Add 2 more columns to the overview');
            cy.wrap(referenceOverview.columns.slice(2, 4)).each((referenceColumn: ColumnType) => {
                omu.simpleEditUtils.columnListUtils.addColumn(referenceColumn);
            });

            // [column1, column2, column3]

            cy.step('Remove the first of the 2 added columns');
            omu.simpleEditUtils.columnListUtils.removeColumn(1);

            // [column1, column3]
        });

        cy.step('Close the column list');
        cy.get('body').click(); // closes the column list popover
    }

    cy.section('Attempt to close, then save the overview using the confirmation dialog');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.step('Click the cancel button');
        cy.findAllByRole('button').contains('Zavřít').click();

        cy.step('Assert that the confirmation dialog appeared');
        cy.findAllByRole('dialog').contains('Uložit provedené změny?').should('be.visible');

        cy.step('Save the overview (using the "Yes" button in the confirmation dialog)');
        cy.findAllByRole('button').contains('Ano').click();
    }

    cy.section('Wait for the overview to save & load');
    {
        cy.findAllByRole('progressbar').its('length').should('eq', 0);

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    // Delete the overview (cy.section called in omu.createOverview)
    {
        omu.deleteOverview();
    }
});
