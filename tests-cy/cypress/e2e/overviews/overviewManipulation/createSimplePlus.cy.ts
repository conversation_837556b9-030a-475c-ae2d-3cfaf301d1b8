import { ColumnType, TestOverviewJSONType } from 'cypress/fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import interceptOverviewRequests from '../utils/interceptOverviewRequests';
import OverviewManipulationUtils from '../utils/OverviewManipulationUtils/OverviewManipulationUtils';

const testOverviewFixturePath = 'test-instance/overviews/testOverviews/testOverview1.json';

const omu = new OverviewManipulationUtils();

/**
 * Test for creating an overview using the simple edit mode.
 * Also tests simple mode sorting & save confirmation dialogues.
 *
 * **Steps:**
 * - <PERSON>gin as an instance admin & navigate to the overviews page.
 * - Create a new overview.
 * - Pick the overview's columns.
 * - Attempt to cancel & assert that the confirmation dialog appears.
 * - Sort by a column.
 * - Attempt to cancel, then save the overview using the confirmation dialog.
 * - Assert that the overview has the correct columns.
 * - Assert that the overview has the correct sorting.
 * - Delete the overview.
 */

it.tagged(['instanceInvasive', 'testInstanceOnly'], 'Create an overview using the SIMPLE edit mode & use its more complex features', () => {
    cy.section('Login as an instance admin & navigate to the overviews page');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        // wait for the initially opened overview to load (to prevent these requests being part of the next sections's intercepts)
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    // Create a new overview (cy.section called in omu.createOverview)
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest', 'headerRequest'], 2);

        omu.createOverview({
            baseTemplateName: 'template_overviewData',
            newOverviewNames: {
                name: `test_createSimplePlus_${new Date().getTime()}`,
                name_cs: `test_createSimplePlus_${new Date().getTime()} (CS)`,
            },
        });

        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
        cy.wait(['@customViewsRequest', '@templateVariablesRequest', '@headerRequest'], { requestTimeout: 20000 });
    }

    cy.section('Remove the case name system column & pick the overview\'s columns');
    {
        cy.findAllByRole('button').contains('Sloupce').click();

        omu.simpleEditUtils.columnListUtils.removeColumn(0);

        cy.fixture(testOverviewFixturePath).then((referenceOverview: TestOverviewJSONType) => {
            cy.wrap(referenceOverview.columns.reverse()).each((referenceColumn: ColumnType) => {
                omu.simpleEditUtils.columnListUtils.addColumn(referenceColumn);
            });
        });

        cy.get('body').click(); // closes the column list popover
    }

    cy.section('Attempt to cancel & assert that the confirmation dialog appears');
    {
        cy.step('Click the cancel button');
        cy.findAllByRole('button').contains('Zrušit').click();

        cy.step('Assert that the confirmation dialog appeared');
        cy.findAllByRole('dialog').contains('Uložit provedené změny?');

        cy.step('Continue editing the overview (click the close button in the confirmation dialog)');
        cy.findAllByRole('button').find('.icon2-close').filter((_i, e) => e.textContent === '').eq(0).click();
    }

    cy.section('Sort by a column');
    {
        omu.overviewStateHandler.get().then((overviewState) => {
            omu.simpleEditUtils.tableUtils.setSortingRule(overviewState.columns[0].name, 'ascending');
        });
    }

    cy.section('Attempt to cancel, then save the overview using the confirmation dialog');
    {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.step('Click the cancel button');
        cy.findAllByRole('button').contains('Zrušit').click();

        cy.step('Assert that the confirmation dialog appeared');
        cy.findAllByRole('dialog').contains('Uložit provedené změny?').should('be.visible');

        cy.step('Save the overview (using the "Yes" button in the confirmation dialog)');
        cy.findAllByRole('button').contains('Ano').click();

        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });
    }

    cy.section('Assert that the overview has the correct columns');
    {
        omu.assertionUtils.assertColumnHeaders();
    }

    cy.section('Assert that the overview has the correct sorting');
    {
        omu.assertionUtils.assertSorting(5, 'simple');
    }

    // Delete the overview (cy.section called in omu.createOverview)
    {
        omu.deleteOverview();
    }
});
