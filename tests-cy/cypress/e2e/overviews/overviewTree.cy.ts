import { OverviewsFixtureType } from 'cypress/fixtures/test-instance/overviews/overviews.json.type';
import OverviewTreeUtils from './utils/overviewTreeUtils';
import interceptOverviewRequests from './utils/interceptOverviewRequests';

const overviewsFixture = 'test-instance/overviews/overviews.json';

const otu = new OverviewTreeUtils();

describe.tagged(['testInstanceOnly', 'instanceInvasive'], 'The overview tree behaves correctly (assuming no hidden or favourited overviews)', () => {
    it('Favouriting & un-favouriting an overview behaves correctly', () => {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        cy.step('Wait for the overviews to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        otu.resetExpandedPathPieces('all', { logAsStep: true });

        otu.getPathForNthNestedOverview(1, { minNestLevel: 2 }).then((path) => {
            cy.step('Store the amount of items with the overview\'s name in the "all" tree');
            otu.revealAllOverviews('all');
            otu.getTreeElement('all').within(() => {
                cy.findAllByText([...path].pop()).its('length').as('initialOverviewItemAmountInAll', { type: 'static' });
            });

            cy.step('Favourite an overview');
            otu.revealOverviewWithPath(path, 'all').then((treeItem) => {
                cy.intercept('POST', '/user-parameters').as('favouriteOverviewRequest');
                otu.doOverviewAction(treeItem, 'favourite');
                cy.wait('@favouriteOverviewRequest');
            });

            cy.step('Reload the page');
            interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);
            cy.reload();

            cy.step('Wait for the overviews to load');
            cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

            otu.resetExpandedPathPieces('all', { logAsStep: true });

            cy.step('Assert that the overview is still present in the "all" tree');
            otu.revealOverviewWithPath(path, 'all');

            cy.step('Assert that the amount of items with the overview\'s name in the "all" tree is the same as before favouriting');
            cy.get('@initialOverviewItemAmountInAll').then((intialAmount: unknown) => {
                otu.revealAllOverviews('all');
                otu.getTreeElement('all').within(() => {
                    cy.findAllByText([...path].pop()).its('length').should('eq', (intialAmount as number));
                });
            });

            cy.step('Assert that the overview is correctly placed within the "favourites" tree');
            cy.log('The "favourites" tree should auto-expand (if there is more than zero favourites) - so there should be more than just the "all" tree');
            cy.findAllByRole('tree').its('length').should('be.above', 1);
            otu.revealOverviewWithPath(path, 'favourites').then((treeItem) => {
                cy.step('Un-favourite the overview');
                otu.doOverviewAction(treeItem, 'unfavourite');

                cy.step('Assert that the overview is no longer present in the "favourites" tree');
                cy.wrap(treeItem).should('not.exist');
            });

            cy.step('Assert that the amount of items with the overview\'s name in the "all" tree is still the same as before favouriting');
            cy.get('@initialOverviewItemAmountInAll').then((intialAmount: unknown) => {
                otu.getTreeElement('all').within(() => {
                    cy.findAllByText([...path].pop()).its('length').should('eq', (intialAmount as number));
                });
            });

            cy.step('Assert that the overview is present in the "all" tree by collapsing and revealing its path');
            otu.collapseOverviewWithPath(path, 'all');
            otu.revealOverviewWithPath(path, 'all');
        });
    });

    it('Hiding & un-hiding an overview behaves correctly', () => {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        cy.step('Wait for the overviews to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        otu.resetExpandedPathPieces('all', { logAsStep: true });

        otu.getPathForNthNestedOverview(0, { minNestLevel: 2 }).then((path) => {
            cy.step('Store the amount of items with the overview\'s name in the "all" tree');
            otu.revealAllOverviews('all');
            otu.getTreeElement('all').within(() => {
                cy.findAllByText([...path].pop()).its('length').as('initialOverviewItemAmountInAll', { type: 'static' });
            });

            cy.step('Hide an overview');
            otu.revealOverviewWithPath(path, 'all').then((treeItem) => {
                cy.intercept('POST', '/user-parameters').as('hideOverviewRequest');
                otu.doOverviewAction(treeItem, 'hide');
                cy.wait('@hideOverviewRequest');
            });

            cy.step('Reload the page');
            interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);
            cy.reload();

            cy.step('Wait for the overviews to load');
            cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

            otu.resetExpandedPathPieces('all', { logAsStep: true });

            otu.resetExpandedPathPieces('hidden', { logAsStep: true });

            cy.step('Assert that the amount of items with the overview\'s name in the "all" tree is <one less> than before hiding it');
            cy.get('@initialOverviewItemAmountInAll').then((intialAmount: unknown) => {
                otu.revealAllOverviews('all');
                otu.getTreeElement('all').within(() => {
                    cy.findAllByText([...path].pop()).its('length').should('eq', (intialAmount as number) - 1);
                });
            });

            cy.step('Assert that the overview is correctly placed within the "hidden" tree');
            otu.revealOverviewWithPath(path, 'hidden').then((treeItem) => {
                cy.step('Un-hide the overview');
                otu.doOverviewAction(treeItem, 'unhide');

                cy.step('Assert that the overview is no longer present in the "hidden" tree');
                cy.wrap(treeItem).should('not.exist');
            });

            cy.step('Assert that the amount of items with the overview\'s name in the "all" tree is the same as before hiding it');
            cy.get('@initialOverviewItemAmountInAll').then((intialAmount: unknown) => {
                otu.getTreeElement('all').within(() => {
                    cy.findAllByText([...path].pop()).its('length').should('eq', (intialAmount as number));
                });
            });

            cy.step('Assert that the overview is present in the "all" tree by collapsing and revealing its path');
            otu.collapseOverviewWithPath(path, 'all');
            otu.revealOverviewWithPath(path, 'all');
        });

    });

    it('Deleting an overview sends the correct request', () => {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        cy.step('Wait for the overviews to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        otu.resetExpandedPathPieces('all', { logAsStep: true });

        otu.getPathForNthNestedOverview(3, { minNestLevel: 2 }).then((path) => {
            otu.revealOverviewWithPath(path, 'all').then((treeItem) => {
                cy.step('Get the ID of the overview to delete (from its URL when viewing it)');
                cy.wrap(treeItem).click(); // Click the tree item to go to its url (which contains its id)
                cy.url().should('include', '/overviews/').as('toDeleteOverviewUrl', { type: 'static' });

                cy.step('Set up the interception for the delete request');
                cy.intercept('DELETE', '/custom-views/*', (req) => {
                    req.reply({ statusCode: 418 }); // stub a response to halt the outgoing request
                }).as('deleteOverviewRequest');

                cy.step('Attempt to delete the overview');
                otu.doOverviewAction(treeItem, 'delete');
                cy.findByRole('dialog').within(() => {
                    cy.findAllByRole('button').contains('Smazat').click();
                });

                cy.step('Assert that the correct request was sent');
                cy.wait('@deleteOverviewRequest').then((interception) => {
                    cy.get('@toDeleteOverviewUrl').then((url: unknown) => {
                        expect(interception.request.url).to.include(`/custom-views/${(url as string).split('/').pop()}`);
                    });
                });
            });
        });
    });

    it('Searching for an overview behaves correctly', () => {
        interceptOverviewRequests(['customViewsRequest', 'templateVariablesRequest'], 1);

        cy.tasLogin('testInstanceAdmin');
        cy.visit('/overviews');

        cy.step('Wait for the overviews to load');
        cy.wait(['@customViewsRequest', '@templateVariablesRequest'], { requestTimeout: 20000 });

        otu.resetExpandedPathPieces('all', { logAsStep: true });

        cy.step('Favourite a nested overview');
        otu.getPathForNthNestedOverview(0, { minNestLevel: 2 }).then((path) => {
            cy.wrap(path).as('favouritedOverviewPath', { type: 'static' });
            otu.revealOverviewWithPath(path, 'all').then((treeItem) => {
                otu.doOverviewAction(treeItem, 'favourite');
            });

            cy.step('Hide a nested overview with almost the same path - except for the last part');
            cy.fixture(overviewsFixture).then((overviews: OverviewsFixtureType) => {
                const overviewToHide = overviews.find((overview) => (
                    overview.name_cs.split('/').slice(0, -1).join('/') === path.slice(0, -1).join('/')
                    && overview.name_cs !== path.join('/')
                ));
                const pathToHide = overviewToHide.name_cs.split('/');
                const commonOverviewPath = overviewToHide.name_cs.split('/').slice(0, -1);

                otu.revealOverviewWithPath(pathToHide, 'all').then((treeItem) => {
                    otu.doOverviewAction(treeItem, 'hide');
                });

                cy.wrap(pathToHide).as('hiddenOverviewPath', { type: 'static' });
                cy.wrap(commonOverviewPath).as('commonOverviewPath', { type: 'static' });
            });
        });

        cy.step('Wait for the overviews to load');
        otu.getTreeElement('all').within(() => {
            cy.findAllByRole('treeitem').its('length').should('be.above', 0);
        });

        cy.step('Search for a part of an overview name that matches the favourited & hidden overview');
        cy.get('@commonOverviewPath').then((commonOverviewPath: unknown) => {
            const fullPathString = (commonOverviewPath as string[]).join('/');
            const toType = fullPathString.slice(Math.floor((fullPathString.length / 5) * 3), fullPathString.length - 1);

            cy.wrap(toType).as('searchedPart', { type: 'static' });
            cy.findByPlaceholderText('Hledat').type(toType);
        });

        cy.log('After a search, the trees should be already expanded (the "hidden" tree section needs to be opened though) -> update the expanded path pieces');
        otu.resetExpandedPathPieces('all', { logAsStep: true });
        otu.resetExpandedPathPieces('favourites', { logAsStep: true });

        cy.step('Assert that the search results within the "all" tree contain every overview with the searched part in its name (except for the hidden one)');
        cy.get('@searchedPart').then((searchedPart: unknown) => {
            cy.get('@hiddenOverviewPath').then((hiddenOverviewPath: unknown) => {
                cy.fixture(overviewsFixture).then((overviews: OverviewsFixtureType) => (
                    overviews.filter((overview) => (
                        (overview as OverviewsFixtureType[0]).name_cs.includes(searchedPart as string)
                        && (overview as OverviewsFixtureType[0]).name_cs !== ((hiddenOverviewPath as string[]).join('/'))
                    ))
                )).each((expectedResultOverview: OverviewsFixtureType[0]) => {
                    otu.revealOverviewWithPath(expectedResultOverview.name_cs.split('/'), 'all').should('exist');
                });
            });
        });

        cy.step('Assert that the search results within the "all" tree do not contain any overviews that they should not (the hidden overview should also stay hidden)');
        cy.get('@searchedPart').then((searchedPart: unknown) => {
            cy.get('@hiddenOverviewPath').then((hiddenOverviewPath: unknown) => {
                cy.fixture(overviewsFixture).then((overviews: OverviewsFixtureType) => (
                    overviews.filter((overview) => (
                        !(overview as OverviewsFixtureType[0]).name_cs.includes(searchedPart as string)
                        || (overview as OverviewsFixtureType[0]).name_cs === ((hiddenOverviewPath as string[]).join('/'))
                    ))
                )).each((expectedExcludedOverview: OverviewsFixtureType[0]) => {
                    const path = expectedExcludedOverview.name_cs.split('/');
                    const pathPartsPresence = new Array(path.length).fill(false);

                    otu.getTreeElement('all').within(() => {
                        cy.findAllByRole('treeitem').findAllByRole('heading').then((treeItemHeadings) => {
                            cy.wrap(path).each((pathPart: unknown, pathPartIndex) => {
                                const pathPartPresent = treeItemHeadings.toArray().some((treeItemHeading) => treeItemHeading.textContent === pathPart);
                                pathPartsPresence[pathPartIndex] = pathPartPresent;
                            });
                        });
                    });

                    cy.then(() => {
                        const somePartMissing = pathPartsPresence.some((partPresent) => !partPresent);
                        expect(
                            somePartMissing,
                            `At least one part of the supposedly excluded overview path "${expectedExcludedOverview.name_cs}" should be missing`,
                        ).to.be.true;
                    });
                });
            });
        });

        cy.step('Assert that the "favourites" tree contains the favourited overview');
        cy.get('@favouritedOverviewPath').then((favouritedOverviewPath: unknown) => {
            otu.revealOverviewWithPath(favouritedOverviewPath as string[], 'favourites').should('exist');
        });

        cy.step('Assert that the "hidden" tree contains the hidden overview');
        otu.getTreeTitleElement('hidden').click();
        cy.log('After a search, the "hidden" tree should also be already expanded -> update the expanded path pieces');
        otu.resetExpandedPathPieces('hidden');
        cy.get('@hiddenOverviewPath').then((hiddenOverviewPath: unknown) => {
            otu.revealOverviewWithPath(hiddenOverviewPath as string[], 'hidden').should('exist');
        });

        cy.step('Un-favourite the favourited overview');
        cy.get('@favouritedOverviewPath').then((favouritedOverviewPath: unknown) => {
            otu.revealOverviewWithPath(favouritedOverviewPath as string[], 'favourites').then((treeItem) => {
                otu.doOverviewAction(treeItem, 'unfavourite');
            });
        });

        cy.step('Un-hide the hidden overview');
        cy.get('@hiddenOverviewPath').then((hiddenOverviewPath: unknown) => {
            otu.revealOverviewWithPath(hiddenOverviewPath as string[], 'hidden').then((treeItem) => {
                otu.doOverviewAction(treeItem, 'unhide');
            });
        });
    });
});
