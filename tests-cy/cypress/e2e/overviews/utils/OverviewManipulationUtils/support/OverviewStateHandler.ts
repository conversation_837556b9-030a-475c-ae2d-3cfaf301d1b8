import { TestOverviewJSONType } from '../../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';

type OverviewState = Pick<TestOverviewJSONType, 'name' | 'name_cs' | 'columns'> & {
    columns: {
        sortRule?: 'ascending' | 'descending';
    }[],
    filterGroups: TestOverviewJSONType['filterGroups'],
    sortingRules: TestOverviewJSONType['sortingRules'],
};

/**
 * A class for handling the state of an overview.
 *
 * All of its methods respect & will be executed within the Cypress command queue (they are chainables).
 */
class OverviewStateHandler {
    #stateRaw?: OverviewState;
    constructor(state?: OverviewState) {
        if (typeof state === 'undefined') {
            this.#stateRaw = state;
        }
    }

    get #state() {
        if (typeof this.#stateRaw === 'undefined') {
            throw new Error('OverviewStateHandler: The state is not yet set. Set a state before managing it.');
        }

        return this.#stateRaw;
    }

    set #state(state: OverviewState) {
        this.#stateRaw = state;
    }

    get() {
        return cy.then(() => this.#state);
    }
    set(state: OverviewState | null) {
        return cy.then(() => { this.#state = state; });
    }
    clear() {
        return cy.then(() => { this.#state = undefined; });
    }

    columns = {
        moveColumn: (columnIndex: number, newColumnIndex: number) => cy.then(() => {
            const columnToMove = this.#state.columns[columnIndex];
            this.#state.columns.splice(columnIndex, 1);
            this.#state.columns.splice(newColumnIndex, 0, columnToMove);
        }),
        addColumn: (column: TestOverviewJSONType['columns'][0]) => cy.then(() => {
            this.#state.columns.push(column);
        }),
        removeColumn: (columnIndex: number) => cy.then(() => {
            this.#state.columns.splice(columnIndex, 1);
        }),
    };

    filters = {
        addFilterGroup: (filterGroup: TestOverviewJSONType['filterGroups'][0]) => cy.then(() => {
            if (!this.#state.filterGroups) {
                this.#state.filterGroups = [];
            }

            this.#state.filterGroups.push(filterGroup);
        }),
        removeFilterGroup: (filterGroupIndex: number) => cy.then(() => {
            this.#state.filterGroups?.splice(filterGroupIndex, 1);
        }),
        addFilter: (filterGroupIndex: number, filter: TestOverviewJSONType['filterGroups'][0]['filters'][0]) => cy.then(() => {
            if (!this.#state.filterGroups) {
                this.#state.filterGroups = [];
            }

            if (!this.#state.filterGroups[filterGroupIndex].filters) {
                this.#state.filterGroups[filterGroupIndex].filters = [];
            }

            this.#state.filterGroups[filterGroupIndex].filters.push(filter);
        }),
        removeFilter: (filterGroupIndex: number, filterIndex: number) => cy.then(() => {
            this.#state.filterGroups?.[filterGroupIndex].filters?.splice(filterIndex, 1);
        }),
        changeFilter: (filterGroupIndex: number, filterIndex: number, newFilter: TestOverviewJSONType['filterGroups'][0]['filters'][0]) => cy.then(() => {
            this.#state.filterGroups?.[filterGroupIndex].filters?.splice(filterIndex, 1, newFilter);
        }),
    };

    sorting = {
        addSortingRule: (
            columnName: OverviewState['sortingRules'][0]['columnName'],
            rule: OverviewState['sortingRules'][0]['rule'],
        ) => cy.then(() => {
            this.#state.sortingRules.push({ columnName, rule });
        }),
        changeSortingRule: (
            columnName: OverviewState['sortingRules'][0]['columnName'],
            rule: OverviewState['sortingRules'][0]['rule'],
        ) => cy.then(() => {
            const sortRuleIndex = this.#state.sortingRules.findIndex((sortRule) => sortRule.columnName === columnName);
            if (sortRuleIndex !== -1) {
                this.#state.sortingRules[sortRuleIndex].rule = rule;
            }
        }),
        removeSortingRule: (columnName: OverviewState['sortingRules'][0]['columnName']) => cy.then(() => {
            const sortRuleIndex = this.#state.sortingRules.findIndex((sortRule) => sortRule.columnName === columnName);
            if (sortRuleIndex !== -1) {
                this.#state.sortingRules.splice(sortRuleIndex, 1);
            }
        }),
    };
}

export default OverviewStateHandler;
export type { OverviewState };
