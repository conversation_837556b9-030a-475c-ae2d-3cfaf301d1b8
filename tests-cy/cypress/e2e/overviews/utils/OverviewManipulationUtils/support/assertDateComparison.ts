const assertDateComparison = (
    rawDateA: string,
    rawDateB: string,
    operator: 'lte' | 'gte' | 'lt' | 'gt',
    message?: string,
) => {
    const dateRegex = /(\d{1,2})\.(\d{1,2})\.(\d{4})( (\d{1,2}):(\d{2}))?/;

    const dateAMatch = rawDateA.match(dateRegex).map(Number);
    const dateAContainsTime = !Number.isNaN(dateAMatch[5]);
    const dateA = new Date(
        dateAMatch[3], dateAMatch[2] - 1, dateAMatch[1],
        ...(dateAContainsTime ? [dateAMatch[5], dateAMatch[6]] : []),
    );

    const dateBMatch = rawDateB.match(dateRegex).map(Number);
    const dateBContainsTime = !Number.isNaN(dateBMatch[5]);
    const dateB = new Date(
        dateBMatch[3], dateBMatch[2] - 1, dateBMatch[1],
        ...(dateBContainsTime ? [dateBMatch[5], dateBMatch[6]] : []),
    );

    switch (operator) {
        case 'lte':
            return expect(
                dateA,
                message || `Expected the date "${rawDateA}" to be less than or equal to the date "${rawDateB}"`,
            ).to.be.lte(dateB);
        case 'gte':
            return expect(
                dateA,
                message || `Expected the date "${rawDateA}" to be greater than or equal to the date "${rawDateB}"`,
            ).to.be.gte(dateB);
        case 'lt':
            return expect(
                dateA,
                message || `Expected the date "${rawDateA}" to be less than the date "${rawDateB}"`,
            ).to.be.lt(dateB);
        case 'gt':
            return expect(
                dateA,
                message || `Expected the date "${rawDateA}" to be greater than the date "${rawDateB}"`,
            ).to.be.gt(dateB);
    }
};

export default assertDateComparison;
