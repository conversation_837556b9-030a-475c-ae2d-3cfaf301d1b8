import { ColumnType } from '../../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import commonOverviewQueries from '../../commonOverviewQueries';
import OMUSubUtilsBase from '../OMUSubUtilsBase';
import getRowFilterValidator from '../support/filters/getRowFilterValidator';
import getRowSortingValidator from '../support/sorting/getRowSortingValidator';

class OMUAssertionUtils extends OMUSubUtilsBase {
    /**
     * Asserts that the overview's display name corresponds to its expected state.
     * - The localized version, if it was set, is used as reference.
     * - The non-localized version is used as fallback.
     *
     * **Assumption:**
     * - The overview is opened - either normally, or in the simple edit mode (the expert edit mode must be closed).
     */
    assertDisplayName(): Cypress.Chainable<void> {
        cy.step('Assert that the overview\'s display name corresponds to its expected state');

        this.overviewStateHandler.get().then((overviewState) => {
            cy.contains(
                typeof overviewState.name_cs !== 'undefined' ? overviewState.name_cs : overviewState.name,
            ).should('be.visible');
        });

        return cy.end();
    }

    /**
     * Asserts that the overview's columns correspond to their expected state:
     * - The columns' names are correct.
     * - The columns' order is correct.
     *
     * **Assumption:**
     * - The overview table & column headers are visible (either normally, or in the simple edit mode).
     */
    assertColumnHeaders(): Cypress.Chainable<void> {
        cy.step('Assert that the overview\'s columns correspond to their expected state');

        this.overviewStateHandler.get().then((overviewState) => {
            commonOverviewQueries.table.columnHeaders().should((columnHeaders) => {
                overviewState.columns.forEach((column: ColumnType, columnIndex) => {
                    expect(columnHeaders.eq(columnIndex)).to.have.text(column.name_cs);
                });
            });
        });

        return cy.end();
    }

    /**
     * Asserts that the overview's rows are sorted in accordance to the overview's presumed sorting rules.
     * @param numberOfRows number of rows to check; 'all' to check all rows
     * @param ruleSet whether to check against the presumed simple/expert mode sorting rules
     *
     * **Assumption:**
     * - The overview table is visible (either normally, or in the simple edit mode).
     */
    assertSorting(numberOfRows: number | 'all', ruleSet: 'simple' | 'expert'): Cypress.Chainable<void> {
        cy.step(`
            Assert that ${numberOfRows === 'all' ? 'ALL of' : `the first ${numberOfRows} of`}
            the overview's rows are sorted in accordance to the overview's presumed
            ${ruleSet === 'expert' ? 'EXPERT mode sorting rules' : 'SIMPLE mode sorting rule'}
        `);

        this.overviewStateHandler.get().then((overviewState) => {
            getRowSortingValidator(
                ruleSet === 'expert' ? overviewState.expertSortingRules : [overviewState.simpleSortingRule],
                overviewState.columns,
            ).then((rowSortingValidator) => {
                commonOverviewQueries.table.rows().each((row, rowIndex) => {
                    if (rowIndex === 0) {
                        cy.wrap(row, { log: false }).within(() => {
                            cy.findAllByRole('gridcell', { log: false }).as('previousRowCells');
                        });
                    } else {
                        cy.get('@previousRowCells').then((previousRowCells) => {
                            cy.wrap(row).within(() => {
                                cy.findAllByRole('gridcell').then((currentRowCells) => {
                                    rowSortingValidator(previousRowCells, currentRowCells);
                                });
                            });
                        });

                        cy.wrap(row, { log: false }).within(() => {
                            cy.findAllByRole('gridcell', { log: false }).as('previousRowCells');
                        });

                        if (numberOfRows !== 'all' && rowIndex >= numberOfRows - 1) { // check only first numberOfRows rows
                            return false;
                        }
                    }
                });
            });
        });

        return cy.end();
    }

    /**
     * Asserts that the overview's rows are filtered in accordance to the overview's presumed filter rules.
     * - Checks for all rows (otherwise the test would be incomplete).
     *
     * **Assumption:**
     * - The overview table is visible (either normally, or in the simple edit mode).
     */
    assertFiltering(): Cypress.Chainable<void> {
        cy.step('Assert that the overview\'s rows are filtered in accordance to the overview\'s presumed filter rules');

        this.overviewStateHandler.get().then((overviewState) => {
            commonOverviewQueries.table.rows().each((row) => {
                cy.wrap(row).within(() => {
                    getRowFilterValidator(overviewState.filterGroups, overviewState.columns).then((rowFilterValidator) => {
                        cy.findAllByRole('gridcell').then(rowFilterValidator);
                    });
                });
            });
        });

        return cy.end();
    }
}

export default OMUAssertionUtils;
