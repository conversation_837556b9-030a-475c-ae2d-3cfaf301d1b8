import { TestOverviewJSONType } from '../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import OverviewTreeUtils from '../overviewTreeUtils';
import OverviewStateHandler from './support/OverviewStateHandler';
import ExpertEditUtils from './expertEditUtils/ExpertEditUtils';
import SimpleEditUtils from './simpleEditUtils/SimpleEditUtils';
import AssertionUtils from './assertionUtils/AssertionUtils';
import { OverviewState } from './support/OverviewStateHandler';
import { RelevantSystemColumnsJSONType } from 'cypress/fixtures/test-instance/overviews/relevantSystemColumns.json.type';

/**
 * Utility class for manipulating overviews.
 *
 * All of the utils respect & will be executed within the Cypress command queue (they are chainables).
 */
class OverviewManipulationUtils {
    overviewStateHandler: OverviewStateHandler;
    overviewTreeUtils: OverviewTreeUtils;

    /**
     * A set of utilities for manipulating overviews in the expert edit mode.
     *
     * - **Assumption of every utility (not tested):**
     *   - an overview is being edited in the expert edit mode (the expert edit mode is open)
     * - **Resulting state of every utility (not tested):**
     *   - the overview is still being edited in the expert edit mode
     *   - the applied changes are **not saved**
     *   - the opened tab within the expert edit mode may have changed
     */
    expertEditUtils: ExpertEditUtils;

    /**
     * A set of utilities for manipulating overviews in the simple edit mode.
     *
     * - **Assumption of every utility (not tested):**
     *   - an overview is being edited in the simple edit mode (the simple edit mode is open)
     * - **Resulting state of every utility (not tested):**
     *   - the overview is still being edited in the simple edit mode
     *   - the applied changes are **not saved**
     */
    simpleEditUtils: SimpleEditUtils;

    /**
     * A set of utilities for asserting the real state of the overview.
     */
    assertionUtils: AssertionUtils;

    constructor() {
        this.overviewStateHandler = new OverviewStateHandler();
        this.overviewTreeUtils = new OverviewTreeUtils();
        this.expertEditUtils = new ExpertEditUtils(this.overviewStateHandler);
        this.simpleEditUtils = new SimpleEditUtils(this.overviewStateHandler);
        this.assertionUtils = new AssertionUtils(this.overviewStateHandler);
    }

    #initOverviewStateHandler(initialState: OverviewState) {
        this.overviewStateHandler.set(initialState);
    }

    /**
     * The utils keep track of the changes that are made through them.
     * This state is then internally used as the state the overview SHOULD be in for assertions & further manipulations.
     * The overview you set here will be used as the base for this state, discarding any previous state.
     *
     * No need to call this function if you used the `createOverview`/`cloneOverview` functions - these will set the state themselves.
     *
     * **This is one of the init methods. (see the class description for more details)**
     * @param overview The base overview
     * @returns (yields) The base overview (same as the input)
     */
    setBaseOverview(overview: TestOverviewJSONType): Cypress.Chainable<TestOverviewJSONType> {
        this.#initOverviewStateHandler({
            name: overview.name,
            name_cs: overview.name_cs,
            columns: overview.columns,
            filterGroups: overview.filterGroups,
            sortingRules: overview.sortingRules,
        });

        return cy.wrap(overview);
    }

    /**
     * Creates a new overview.
     * - **Assumptions (not tested):**
     *   - the current location is the the overviews page (/overviews)
     * - **Expected effects (not tested):**
     *   - a redirect to the simple (default) edit mode of the new overview
     *
     * @param createOverviewConfig.newOverviewNames The names of the new overview
     * @param createOverviewConfig.baseTemplateName The default (non-translated) name of the base template for the new overview
     */
    createOverview(createOverviewConfig: {
        newOverviewNames: Pick<TestOverviewJSONType, 'name' | 'name_cs'>,
        baseTemplateName: string,
    }) {
        (cy).fixture('test-instance/overviews/relevantSystemColumns.json')
            .then((relevantSystemColumns: RelevantSystemColumnsJSONType) => {
                this.overviewStateHandler.set({
                    ...createOverviewConfig.newOverviewNames,
                    columns: [relevantSystemColumns.iproc_name],
                    filterGroups: [{ filters: [] }],
                    sortingRules: [],
                });
            });

        cy.section('Create a new overview');
        {
            cy.step('Open the overview creation modal');
            cy.findAllByRole('button').contains('Přidat přehled').click();

            cy.step('Pick the base template');
            cy.findByLabelText('Použít šablonu *').click();
            cy.contains(createOverviewConfig.baseTemplateName).click();

            cy.step('Fill in the names');
            cy.findByLabelText('Název přehledu *').type(createOverviewConfig.newOverviewNames.name);
            cy.contains('Další varianty jazyků').click();
            cy.findByLabelText('Název CS').type(createOverviewConfig.newOverviewNames.name_cs);

            cy.step('Create the overview');
            cy.findAllByRole('button').contains('Vytvořit').click();
        }
    }

    /**
     * Creates an overview clone.
     * - **Assumptions (not tested):**
     *   - the current location is the the overviews page (/overviews)
     * - **Expected effects (not tested):**
     *   - a redirect to the simple (default) edit mode of the overview clone
     *
     * **This is one of the init methods. (see the class description for more details)**
     *
     * @param overview The overview to clone
     * @param cloneNames.cloneNames The names of the overview clone
     * @param cloneNames.useCreateModal Whether the checkbox for cloning an existing overview in the overview creation modal
     * should be used to clone the overview (instead of the overview tree)
     */
    cloneOverview(
        this: OverviewManipulationUtils,
        overview: TestOverviewJSONType,
        cloneOverviewConfig: {
            cloneNames: Pick<TestOverviewJSONType, 'name' | 'name_cs'>,
            useCreateModal?: boolean,
        },
    ): Cypress.Chainable<void> {
        this.#initOverviewStateHandler({
            ...cloneOverviewConfig.cloneNames,
            columns: overview.columns,
            filterGroups: overview.filterGroups,
            sortingRules: overview.sortingRules,
        });

        if(cloneOverviewConfig.useCreateModal) {
            cy.section('Clone an overview using the overview creation modal');
            {
                cy.step('Open the overview creation modal');
                cy.findAllByRole('button').contains('Přidat přehled').click();

                cy.step('Check the checkbox to clone an existing overview');
                cy.findByLabelText('Kopírovat existující přehled').check();

                cy.step('Pick the overview to clone');
                cy.findByLabelText('Vybrat přehled *').click();
                cy.contains(overview.name).click();

                cy.step('Fill in the clone names');
                cy.findByLabelText('Název přehledu *').clear();
                cy.findByLabelText('Název přehledu *').type(cloneOverviewConfig.cloneNames.name);
                cy.contains('Další varianty jazyků').click();
                cy.findByLabelText('Název CS').clear();
                cy.findByLabelText('Název CS').type(cloneOverviewConfig.cloneNames.name_cs);

                cy.step('Clone the overview');
                cy.findAllByRole('button').contains('Vytvořit').click();
            }
        } else {
            cy.section('Clone an overview using its tree item in the overview tree');
            {
                cy.step('Reveal the overview\'s tree item');
                this.overviewTreeUtils.revealOverviewWithPath(overview.name_cs.split('/'), 'all').then((treeItem) => {
                    cy.step('Click the clone option in the tree item\'s three-dot menu');
                    this.overviewTreeUtils.doOverviewAction(treeItem, 'clone');
                });

                cy.step('Fill in the clone names');
                cy.findByLabelText('Název *').clear();
                cy.findByLabelText('Název *').type(cloneOverviewConfig.cloneNames.name);
                cy.contains('Další varianty jazyků').click();
                cy.findByLabelText('Název CS').clear();
                cy.findByLabelText('Název CS').type(cloneOverviewConfig.cloneNames.name_cs);

                cy.step('Clone the overview');
                cy.findAllByRole('button').contains('Kopírovat').click();
            }
        }

        return cy.end();
    }

    /**
     * Deletes the manipulated overview.
     * - **Assumptions (not tested):**
     *   - the current location is the /overviews page
     * - **Resulting state (not tested):**
     *   - we are still at the /overviews page
     *   - the overview tree is accessible (no modal or anything else is blocking the access)
     * - **Expected effects (not tested):**
     *   - the overview is no longer listed in any overview tree
     *
     * **This is one of the init methods. (see the class description for more details)**
     */
    deleteOverview(): Cypress.Chainable<void> {
        cy.section('Delete the overview');
        {
            cy.step('Reveal the overview\'s tree item');
            this.overviewStateHandler.get().then((overviewState) => {
                this.overviewTreeUtils.revealOverviewWithPath(overviewState.name_cs.split('/'), 'all').then((treeItem) => {
                    cy.step('Click the delete option in the tree item\'s three-dot menu');
                    this.overviewTreeUtils.doOverviewAction(treeItem, 'delete');
                });
            });

            cy.step('Confirm the deletion');
            cy.findAllByRole('button').contains('Smazat').click();
        }

        this.overviewStateHandler.clear();

        return cy.end();
    }
}

export default OverviewManipulationUtils;
