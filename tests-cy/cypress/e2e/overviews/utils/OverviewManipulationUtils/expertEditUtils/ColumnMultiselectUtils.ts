import { ColumnType } from '../../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import OMUSubUtilsBase from '../OMUSubUtilsBase';

class OMUExpertEditUtilsColumnMultiselectUtils extends OMUSubUtilsBase {
    queries = {
        /**
         * Returns the wrapper of the left or right part of the column multiselect options.
         * @param part left or right part of the column multiselect
         */
        columnMultiselectOptionsWrapper(part: 'left' | 'right') {
            const labelText = part === 'left' ? 'Dostupná pole' : 'Přiřazení';

            return (
                (cy).get('label')
                    .filter((_index, labelElement) => labelElement.textContent.includes(labelText))
                    .then(multiselectPartLabel => (
                        cy.findAllByRole('dialog').then(dialogs => (
                            dialogs.get().sort((a, b) => { // sort by distance to the label
                                const { x: xLabel } = multiselectPartLabel.get(0).getBoundingClientRect();
                                const { x: xA } = a.getBoundingClientRect();
                                const { x: xB } = b.getBoundingClientRect();

                                return Math.abs(xA - xLabel) - Math.abs(xB - xLabel);
                            }) // this should be the multiselect part with the "dialog" role horizontally closest to the label
                        )).then((closestDialogs) => {
                            return cy.wrap(closestDialogs).eq(0);
                        })
                    ))
            );
        },
    };

    /**
     * Adds a column to the overview using the expert edit mode's column multiselect.
     * @param column the column to add
     */
    addColumn(column: ColumnType): Cypress.Chainable<void> {
        cy.step(`Add column "${column.name}"`);
        this.queries.columnMultiselectOptionsWrapper('left').within(() => {
            (cy).findAllByRole('button')
                .filter((_index, button) => button.textContent.includes(column.name_cs))
                .eq(0).as('columnButton')
                .click();

            cy.get('@columnButton').within(() => {
                cy.findAllByRole('button').find('.icon2-add').click();
            });
        });

        this.overviewStateHandler.columns.addColumn(column);

        return cy.end();
    }

    /**
     * Removes a column from the overview using the expert edit mode's column multiselect.
     * @param columnIndex index of the column to remove
     */
    removeColumn(columnIndex: number): Cypress.Chainable<void> {
        this.overviewStateHandler.get().then((overviewState) => {
            (cy).fixture('test-instance/overviews/relevantSystemColumns.json').then((relevantSystemColumns) => {
                const column = overviewState.columns[columnIndex];

                const isSystemColumn = Object.prototype.hasOwnProperty.call(relevantSystemColumns, column.name);

                if (isSystemColumn) {
                    cy.step(`Remove system column with display name: "${column.name_cs}"`);
                } else {
                    cy.step(`Remove column "${column.name}"`);
                }

                this.queries.columnMultiselectOptionsWrapper('right').within(() => {
                    (cy).findAllByRole('button')
                        .filter((_index, button) => (
                            button.textContent.includes(column.name_cs)
                        ))
                        .eq(0).as('columnButton')
                        .click();

                    cy.get('@columnButton').within(() => {
                        cy.findAllByRole('button').find('.icon2-minus').click();
                    });
                });
            });
        });

        this.overviewStateHandler.columns.removeColumn(columnIndex);

        return cy.end();
    }

    /**
     * Moves a column (using drag and drop) within the right part of the column multiselect.
     * If `columnIndex === newColumnIndex`, the method does nothing.
     * @param columnIndex index of the column to move
     * @param newColumnIndex new index of the column
     */
    moveColumn(columnIndex: number, newColumnIndex: number): Cypress.Chainable<void> {
        const goingUp = newColumnIndex < columnIndex;

        if (columnIndex === newColumnIndex) {
            return cy.end();
        }

        this.overviewStateHandler.get().then((overviewState) => {
            // moving the column to a non-visible part of the multiselect seems to work as-is,
            // so it's not handled here

            cy.step(`Move column at position ${columnIndex} to position ${newColumnIndex}`);
            this.queries.columnMultiselectOptionsWrapper('right').as('rightOptionsWrapper').within(() => {
                (cy).findAllByRole('button')
                    .filter((_index, button) => button.textContent.includes(overviewState.columns[newColumnIndex].name_cs))
                    .eq(0)
                    .as('targetColumnButton');

                (cy).findAllByRole('button')
                    .filter((_index, button) => button.textContent.includes(overviewState.columns[columnIndex].name_cs))
                    .eq(0)
                    .as('movedColumnButton')
                    .click(); // click the column to make its inner grab handle visible

                cy.get('@movedColumnButton').within(() => {
                    cy.findAllByRole('button').eq(0).as('movedColumnGrabHandle');
                });

                cy.get('@movedColumnGrabHandle').dragulaDragTo(
                    '@targetColumnButton',
                    {
                        deviation: {
                            x: 0,
                            y: goingUp ? -2 : 2,
                        },
                    },
                );
            });
        });

        this.overviewStateHandler.columns.moveColumn(columnIndex, newColumnIndex);

        // scroll the first column into view (return to the initial state)
        this.overviewStateHandler.get().then((overviewState) => {
            this.queries.columnMultiselectOptionsWrapper('right').within(() => {
                (cy).findAllByRole('button')
                    .filter((_index, button) => button.textContent.includes(overviewState.columns[0].name_cs))
                    .scrollIntoView();
            });
        });

        return cy.end();
    }
}

export default OMUExpertEditUtilsColumnMultiselectUtils;
