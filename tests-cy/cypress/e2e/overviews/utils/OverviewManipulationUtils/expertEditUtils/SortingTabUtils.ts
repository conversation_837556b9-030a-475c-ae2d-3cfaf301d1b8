import { ColumnType, SortingRule } from '../../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import OMUSubUtilsBase from '../OMUSubUtilsBase';

type SortingRuleControlElementType = 'columnName' | 'rule' | 'trashButton';

class OMUExpertEditUtilsSortingTabUtils extends OMUSubUtilsBase {
    /**
     * Yields the sorting rule control elements for a specific sorting rule.
     *
     * @param sortingRuleIndex - The index of the sorting rule, or 'new' to select the newest sorting rule that is not yet represented in the overviewState.
     * @param getTypes - An optional array of sorting rule control element types to retrieve. Defaults to all of the control elements.
     * @returns A function that executes a set of Cypress commands
     * resulting in a Cypress.Chainable containing a record/map from the element types to Cypress command results
     * containing the respective sorting rule control elements.
     */
    getSortingRuleControlElementQueries(
        sortingRuleIndex: number | 'new',
        getTypes: SortingRuleControlElementType[] = ['columnName', 'rule', 'trashButton'],
    ): Cypress.Chainable<Partial<Record<SortingRuleControlElementType, () => Cypress.Chainable<JQuery<HTMLElement>>>>> {
        return this.overviewStateHandler.get().then((overviewState) => {
            const index = sortingRuleIndex === 'new' ? overviewState.sortingRules.length : sortingRuleIndex;

            const typeToInputLabelMap = {
                columnName: 'Sloupec',
                rule: 'Řazení',
            };

            const getSortedControlElements = (type: SortingRuleControlElementType) => {
                if (type === 'trashButton') {
                    return (
                        (cy).findAllByRole('button')
                            .find('.icon2-trash')
                            .then((elements) => elements.get().sort((a, b) => a.getBoundingClientRect().top - b.getBoundingClientRect().top))
                    );
                } else {
                    return (
                        (cy).findAllByLabelText(typeToInputLabelMap[type])
                            .then((elements) => elements.get().sort((a, b) => a.getBoundingClientRect().top - b.getBoundingClientRect().top))
                    );
                }
            };

            return cy.wrap(getTypes.reduce((acc, key) => {
                acc[key] = () => getSortedControlElements(key).eq(index);
                return acc;
            }, {} as Partial<Record<SortingRuleControlElementType, () => Cypress.Chainable<JQuery<HTMLElement>>>>));
        });
    }

    /**
     * Adds a new sorting rule to the sorting rules within the expert edit mode's sorting tab.
     *
     * - **Assumption (not tested):**
     *   - the affected column has no other existing associated sorting rule
     * @param columnName name of the column to sort by
     * @param rule 'ascending' or 'descending'
     */
    addSortingRule(
        columnName: ColumnType['name'],
        rule: SortingRule['rule'],
    ): Cypress.Chainable<void> {
        cy.step(`Add a new sorting rule - [${columnName} | ${rule}]`);
        cy.findAllByRole('button').contains('Přidat řazení').click();

        this.overviewStateHandler.get().then((overviewState) => {
            this.getSortingRuleControlElementQueries('new', ['columnName', 'rule']).then((sortingRuleInputQueries) => {
                const columnNameTranslation = overviewState.columns.find((column) => column.name === columnName)?.name_cs;

                sortingRuleInputQueries.columnName().click();
                cy.findAllByRole('option').contains(columnNameTranslation).click({ waitForAnimations: false });

                sortingRuleInputQueries.rule().click();
                cy.findAllByRole('option').contains(rule === 'ascending' ? 'Vzestupně' : 'Sestupně').click({ waitForAnimations: false });
            });
        });

        this.overviewStateHandler.sorting.addSortingRule(columnName, rule);

        return cy.end();
    }

    /**
     * Changes the sorting rule for a specific column.
     *
     * - **Assumption (not tested):**
     *  - the affected column has an existing associated sorting rule
     *
     * @param columnName name of the column where the sorting rule should be changed
     * @param rule the new rule, either 'ascending' or 'descending'
     */
    changeSortingRule(
        columnName: ColumnType['name'],
        rule: SortingRule['rule'],
    ): Cypress.Chainable<void> {
        cy.step(`Change the sorting rule for column "${columnName}" to ${rule}`);
        this.overviewStateHandler.get().then((overviewState) => {
            const sortingRuleIndex = overviewState.sortingRules.findIndex((sortingRule) => sortingRule.columnName === columnName);

            this.getSortingRuleControlElementQueries(sortingRuleIndex, ['rule']).then((sortingRuleInputQueries) => {
                sortingRuleInputQueries.rule().click();
                cy.findAllByRole('option').contains(rule === 'ascending' ? 'Vzestupně' : 'Sestupně').click({ waitForAnimations: false });
            });
        });

        this.overviewStateHandler.sorting.changeSortingRule(columnName, rule);

        return cy.end();
    }

    /**
     * Removes the sorting rule for a specific column.
     *
     * - **Assumption (not tested):**
     *  - the affected column has an existing associated sorting rule
     *
     * @param columnName name of the column where the sorting rule should be removed
     */
    removeSortingRule(
        columnName: ColumnType['name'],
    ): Cypress.Chainable<void> {
        cy.step(`Remove the sorting rule for column "${columnName}"`);
        this.overviewStateHandler.get().then((overviewState) => {
            const sortingRuleIndex = overviewState.sortingRules.findIndex((sortingRule) => sortingRule.columnName === columnName);

            this.getSortingRuleControlElementQueries(sortingRuleIndex, ['trashButton']).then((sortingRuleInputQueries) => {
                sortingRuleInputQueries.trashButton().click();
            });
        });

        this.overviewStateHandler.sorting.removeSortingRule(columnName);

        return cy.end();
    }
}

export default OMUExpertEditUtilsSortingTabUtils;
