import { ColumnType } from '../../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import commonOverviewQueries from '../../commonOverviewQueries';
import interceptOverviewRequests from '../../interceptOverviewRequests';
import OMUSubUtilsBase from '../OMUSubUtilsBase';

class OMUSimpleEditUtilsColumnListUtils extends OMUSubUtilsBase {
    /**
     * Adds a column to the overview using the simple edit mode's column list.
     * @param column the column to add
     */
    addColumn(column: ColumnType): Cypress.Chainable<void> {
        interceptOverviewRequests(['templateVariablesRequest'], 1);

        cy.step(`Add column "${column.name}"`);
        (cy).findAllByRole('presentation')
            .filter((_i, e) => !Cypress.dom.isHidden(e))
            .as('columnList')
            .findAllByRole('checkbox')
            .then((columnCheckboxes) => {
                let columnIndexWithinColumnList = 0;
                (cy).get('@columnList')
                    .find('label')
                    // filter out the column table header with the same text content as the column within the column list
                    .filter((index, labelElement) => {
                        const found = labelElement.textContent === column.name_cs;

                        if (found) {
                            columnIndexWithinColumnList = index;
                        }

                        return found;
                    })
                    .eq(0).then((labelWithColumnName) => {
                        cy.wrap(columnCheckboxes.eq(columnIndexWithinColumnList)).should((checkbox) => (
                            expect(
                                checkbox,
                                'Since we\'re attempting to add the column, the column\'s checkbox should NOT be checked at first',
                            ).to.not.be.checked
                        ));

                        cy.wrap(labelWithColumnName).click();
                    });
            });


        cy.wait('@templateVariablesRequest');

        this.overviewStateHandler.columns.addColumn(column);

        return cy.end();
    }

    /**
     * Removes a column from the overview using the simple edit mode's column list.
     * @param columnIndex index of the column to remove
     */
    removeColumn(columnIndex: number): Cypress.Chainable<void> {
        this.overviewStateHandler.get().then((overviewState) => {
            (cy).fixture('test-instance/overviews/relevantSystemColumns.json').then((relevantSystemColumns) => {
                const column = overviewState.columns[columnIndex];

                const isSystemColumn = Object.prototype.hasOwnProperty.call(relevantSystemColumns, column.name);

                if (isSystemColumn) {
                    cy.step(`Remove system column with display name: "${column.name_cs}"`);
                } else {
                    cy.step(`Remove column "${column.name}"`);
                }

                (cy).findAllByRole('presentation')
                    .filter((_i, e) => !Cypress.dom.isHidden(e))
                    .as('columnList')
                    .findAllByRole('checkbox')
                    .then((columnCheckboxes) => {
                        let columnIndexWithinColumnList = 0;
                        commonOverviewQueries.table.columnHeaders().then((columnHeaders) => {
                            (cy).get('@columnList')
                                .find('label')
                                // filter out the column table header with the same text content as the column within the column list
                                .filter((index, labelElement) => {
                                    const found = (
                                        labelElement.textContent === column.name_cs &&
                                        !Cypress.dom.isDescendent(columnHeaders.eq(columnIndex), Cypress.$(labelElement))
                                    );

                                    if (found) {
                                        columnIndexWithinColumnList = index;
                                    }

                                    return found;
                                })
                                .eq(0).then((labelWithColumnName) => {
                                    cy.wrap(columnCheckboxes.eq(columnIndexWithinColumnList)).should((checkbox) => (
                                        expect(
                                            checkbox,
                                            'Since we\'re attempting to remove the column, the column\'s checkbox should be checked at first',
                                        ).to.be.checked
                                    ));

                                    cy.wrap(labelWithColumnName).click();
                                });
                        });
                    });
            });
        });

        this.overviewStateHandler.columns.removeColumn(columnIndex);

        return cy.end();
    }
}

export default OMUSimpleEditUtilsColumnListUtils;
