import OMUSubUtilsBase from '../OMUSubUtilsBase';
import OverviewStateHandler from '../support/OverviewStateHandler';
import ColumnListUtils from './ColumnListUtils';
import TableUtils from './TableUtils';

class OMUSimpleEditUtils extends OMUSubUtilsBase {
    /**
     * A set of utilities for manipulating the column list in the simple edit mode.
     *
     * - **Assumption of every utility (not tested):**
     *   - the column list is accessible
     */
    columnListUtils: ColumnListUtils;

    /**
     * A set of utilities for manipulating the table in the simple edit mode.
     *
     * - **Assumption of every utility (not tested):**
     *  - the table is accessible & no modal or popover is open
     */
    tableUtils: TableUtils;

    constructor(overviewStateHandler: OverviewStateHandler) {
        super(overviewStateHandler);
        this.columnListUtils = new ColumnListUtils(this.overviewStateHandler);
        this.tableUtils = new TableUtils(this.overviewStateHandler);
    }
}

export default OMUSimpleEditUtils;
