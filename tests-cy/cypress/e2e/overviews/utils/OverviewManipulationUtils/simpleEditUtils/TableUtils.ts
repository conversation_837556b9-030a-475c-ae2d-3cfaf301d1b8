import { ColumnType, SortingRule } from '../../../../../fixtures/test-instance/overviews/testOverviews/testOverviews.common.type';
import commonOverviewQueries from '../../commonOverviewQueries';
import OMUSubUtilsBase from '../OMUSubUtilsBase';

class OMUSimpleEditUtilsTableUtils extends OMUSubUtilsBase {
    /**
     * Sets a new sorting rule within the simple edit mode by click the affected column's header
     * (there can only be one sorting rule in the simple edit mode).
     *
     * @param columnName name of the column for the new sorting rule
     * @param rule the new rule, either 'ascending' or 'descending'
     */
    setSortingRule(
        columnName: ColumnType['name'],
        rule: SortingRule['rule'],
    ): Cypress.Chainable<void> {
        cy.step(`Set the simple edit mode sorting rule to [${columnName} | ${rule}]`);
        this.overviewStateHandler.get().then((overviewState) => {
            const rulePosition = rule === 'ascending' ? 1 : 2;
            const currentSortingRule = overviewState.simpleSortingRule;
            const currentPosition = currentSortingRule === null ? 0 : (currentSortingRule.rule === 'ascending' ? 1 : 2);
            const clickCount = currentPosition < rulePosition ? rulePosition - currentPosition : (rulePosition + 3) - currentPosition;

            const columnNameTranslation = overviewState.columns.find((column) => column.name === columnName)?.name_cs;

            commonOverviewQueries.table.columnHeaders().then((columnHeaders) => {
                cy.wrap(new Array(clickCount).fill(null)).each(() => {
                    cy.wrap(columnHeaders).contains(columnNameTranslation).click();
                });
            });
        });

        this.overviewStateHandler.sorting.simple.setSortingRule(columnName, rule);

        return cy.end();
    }

    /**
     * Removes the current sorting rule within the simple edit mode by clicking the affected column's header.
     */
    removeCurrentSortingRule(): Cypress.Chainable<void> {
        cy.step('Remove the current simple edit mode sorting rule');
        this.overviewStateHandler.get().then((overviewState) => {
            const currentSortingRule = overviewState.simpleSortingRule;
            const currentPosition = currentSortingRule === null ? 0 : (currentSortingRule.rule === 'ascending' ? 1 : 2);
            const clickCount = 3 - currentPosition;

            const columnNameTranslation = overviewState.columns.find((column) => column.name === overviewState.simpleSortingRule.columnName)?.name_cs;

            commonOverviewQueries.table.columnHeaders().then((columnHeaders) => {
                cy.wrap(new Array(clickCount).fill(null)).each(() => {
                    cy.wrap(columnHeaders).contains(columnNameTranslation).click();
                });
            });
        });

        this.overviewStateHandler.sorting.simple.removeSortingRule();

        // TODO: actually sort by the column
        return cy.end();
    }
}

export default OMUSimpleEditUtilsTableUtils;
