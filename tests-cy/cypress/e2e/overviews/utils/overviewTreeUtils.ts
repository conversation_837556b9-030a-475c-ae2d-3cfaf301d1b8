import { OverviewsFixtureType } from '../../../fixtures/test-instance/overviews/overviews.json.type';

const overviewsFixture = 'test-instance/overviews/overviews.json';

type OverviewTreeType = 'favourites' | 'all' | 'hidden';
type ExpandedPathPiecesType = Record<OverviewTreeType, Set<string>>;
type OverviewTreeItemActionType =  'edit' | 'favourite' | 'unfavourite' | 'clone' | 'hide' | 'unhide' | 'delete';

/**
 * Utility class for interacting with the overview tree.
 *
 * All of the utils respect & will be executed within the Cypress command queue (they are chainables).
 */
class OverviewTreeUtils {
    expandedPathPieces: ExpandedPathPiecesType = {
        favourites: new Set<string>(),
        all: new Set<string>(),
        hidden: new Set<string>(),
    };

    constructor() {
        beforeEach(() => {
            cy.fixture(overviewsFixture).then((json: OverviewsFixtureType) => {
                cy.wrap(json.map((overview) => overview.name_cs.split('/'))).as('overviewPaths', { type: 'static' });
            });
        });

        afterEach(() => {
            this.clearExpandedPathPieces();
        });
    }

    /**
     * Click through the tree items (folders & only the collapsed ones) to ensure that the overview
     * with the provided path is visible within the specified tree.
     * Yields the overview tree item itself.
     **/
    revealOverviewWithPath(path: string[], withinTree: OverviewTreeType): Cypress.Chainable<JQuery<HTMLElement>> {
        this.getTreeElement(withinTree).within(() => {
            cy.wrap(path).each((pathPiece: unknown, index) => {
                if (index !== path.length - 1 && !this.expandedPathPieces[withinTree].has(pathPiece as string)) {
                    cy.findAllByRole('treeitem').contains(pathPiece as string).click();
                    this.expandedPathPieces[withinTree].add(pathPiece as string);
                }
            });

            cy.findAllByRole('treeitem').filter((_index, treeItem) => (
                treeItem.innerText === path[path.length - 1]
            )).eq(0).as('toYield');
        });

        return cy.get('@toYield');
    }

    /**
     * Click through the tree items (folders & only the expanded ones) in reverse order to collapse the overview
     * with the provided path within the specified tree.
     **/
    collapseOverviewWithPath(path: string[], withinTree: OverviewTreeType): Cypress.Chainable<void> {
        this.getTreeElement(withinTree).within(() => {
            cy.wrap([...path].reverse()).each((pathPiece: unknown, index) => {
                if (index !== 0 && this.expandedPathPieces[withinTree].has(pathPiece as string)) {
                    cy.findAllByRole('treeitem').contains(pathPiece as string).click();
                    this.expandedPathPieces[withinTree].delete(pathPiece as string);
                }
            });
        });

        return cy.end();
    }

    /**
     * Click through the tree items (folders & only the collapsed ones) to ensure that all of the overviews within the specified tree are visible.
     * Yields all the tree items (the overviews).
     **/
    revealAllOverviews(withinTree: OverviewTreeType): Cypress.Chainable<JQuery<HTMLElement>> {
        const lastPathPieces = new Set<string>();

        this.getTreeElement(withinTree).within(() => {
            cy.get('@overviewPaths').then((paths: unknown) => {
                cy.wrap((paths as string[][]).map((path) => {
                    const pathClone = [...path];
                    lastPathPieces.add(pathClone.pop()); // Remove the last element (the overview itself)
                    return pathClone;
                })).each((folderPath: string[]) => {
                    cy.wrap(folderPath).each((pathPiece: unknown) => {
                        if (!this.expandedPathPieces[withinTree].has(pathPiece as string)) { // Click the folder only if it hasn't been clicked before
                            cy.findAllByRole('treeitem').contains(pathPiece as string).click();
                            this.expandedPathPieces[withinTree].add(pathPiece as string);
                        }
                    });
                });
            });

            cy.findAllByRole('treeitem').filter((_index, treeItem) => (lastPathPieces.has(treeItem.innerText))).as('toYield');
        });

        return cy.get('@toYield');
    }

    /**
     * Yields the path to the nth nested overview within the overviews.json fixture.
     **/
    getPathForNthNestedOverview(
        n: number,
        options?: {
            /** Minimum nest level to filter the items from which the nth item will be picked. 1 by default. */
            minNestLevel?: number,
            /** Maximum nest level to filter the items from which the nth item will be picked. */
            maxNestLevel?: number,
        },
    ): Cypress.Chainable<string[] | undefined> {
        return cy.get('@overviewPaths').then(((paths: unknown) => {
            const filteredPaths = (paths as string[][]).filter((path) => (
                path.length >= (typeof options?.minNestLevel === 'undefined' ? 1 : options?.minNestLevel)
                && (typeof options?.maxNestLevel === 'undefined' || path.length <= options?.maxNestLevel)
            ));

            const selectedPath = filteredPaths[n];
            if (typeof selectedPath === 'undefined') {
                expect(
                    selectedPath,
                    'getNthNestedOverview: The overviews.json fixture doesn\'t contain enough entries meeting the specified arguments',
                ).not.to.be.undefined;
                return undefined;
            }

            return selectedPath;
        }));
    }

    getTreeTitleElement(tree: OverviewTreeType): Cypress.Chainable<JQuery<HTMLElement>> {
        const titleTranslations = {
            favourites: 'Oblíbené',
            all: 'Všechny',
            hidden: 'Skryté',
        };

        return cy.contains(titleTranslations[tree]);
    }

    /**
     * Get the corresponding tree element.
     **/
    getTreeElement(tree: OverviewTreeType): Cypress.Chainable<JQuery<HTMLElement>> {
        return this.getTreeTitleElement(tree).then((treeTitleElement) => {
            return cy.findAllByRole('tree', { timeout: 10000 }).then(((elements) => (
                elements.get().filter((element) => ( // Filter out the trees that are below the tree title
                    element.getBoundingClientRect().y
                        > treeTitleElement.get()[0].getBoundingClientRect().y
                )).sort((a, b) => ( // Sort the remaining trees by their y position
                    a.getBoundingClientRect().y - b.getBoundingClientRect().y
                ))[0] // Pick the top-most tree (closest to the tree title)
            )));
        });
    }

    /**
     * Perform an action on the overview's tree item (click the corresponding button within the tree item).
     */
    doOverviewAction(treeItem: JQuery<HTMLElement>, action: OverviewTreeItemActionType): Cypress.Chainable<void> {
        const firstButtonIndex = action === 'edit' ? 0 : 1;
        cy.wrap(treeItem).findAllByRole('button', { hidden: true }).then((buttons) => {
            cy.wrap(buttons.get().sort((a, b) => a.getBoundingClientRect().x - b.getBoundingClientRect().x)[firstButtonIndex]).click({ force: true });
        });

        if (firstButtonIndex === 1) {
            const actionTranslations = {
                favourite: 'Přidat do oblíbených',
                unfavourite: 'Odebrat z oblíbených?',
                clone: 'Duplikovat',
                hide: 'Skrýt',
                delete: 'Smazat',
                unhide: 'Odebrat ze skrytých',
            };

            cy.findAllByRole('button').contains(actionTranslations[action]).click();
        }

        return cy.end();
    }

    /**
     * Clear the expanded path pieces set, which is used for keeping track of the expanded "folders" within the overview trees.
     */
    clearExpandedPathPieces(): Cypress.Chainable<void> {
        cy.then(() => {
            this.expandedPathPieces.all.clear();
            this.expandedPathPieces.favourites.clear();
            this.expandedPathPieces.hidden.clear();
        });

        return cy.end();
    }

    /**
     * Reset the expanded path pieces set based on the current state of the DOM.
     * The expanded path pieces set is used for keeping track of the expanded "folders" within the overview trees.
     */
    resetExpandedPathPieces(withinTree: OverviewTreeType): Cypress.Chainable<void> {
        this.getTreeElement(withinTree).within(() => {
            const newExpandedPathPieces = new Set<string>();
            cy.findAllByRole('treeitem').filter((_i, treeItem) => treeItem.ariaExpanded === 'true').findAllByRole('heading').each((treeFolderItemHeading) => {
                newExpandedPathPieces.add(treeFolderItemHeading.get(0).textContent);
            });
            cy.then(() => {
                this.expandedPathPieces[withinTree] = newExpandedPathPieces;
            });
        });

        return cy.end();
    }
}

export default OverviewTreeUtils;
