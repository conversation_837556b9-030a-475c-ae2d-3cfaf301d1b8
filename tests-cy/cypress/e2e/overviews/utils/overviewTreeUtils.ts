import { OverviewsFixtureType } from '../../../fixtures/test-instance/overviews/overviews.json.type';

const overviewsFixture = 'test-instance/overviews/overviews.json';

type OverviewTreeType = 'favourites' | 'all' | 'hidden';
type ExpandedPathPiecesType = Record<OverviewTreeType, Set<string>>;
type OverviewTreeItemActionType =  'edit' | 'favourite' | 'unfavourite' | 'clone' | 'hide' | 'unhide' | 'delete';

/**
 * Utility class for interacting with the overview tree.
 *
 * All of the utils respect & will be executed within the Cypress command queue (they are chainables).
 */
class OverviewTreeUtils {
    expandedPathPieces: ExpandedPathPiecesType = {
        favourites: new Set<string>(),
        all: new Set<string>(),
        hidden: new Set<string>(),
    };

    constructor() {
        beforeEach(() => {
            cy.fixture(overviewsFixture).then((json: OverviewsFixtureType) => {
                cy.wrap(json.map((overview) => overview.name_cs.split('/'))).as('overviewPaths', { type: 'static' });
            });
        });

        afterEach(() => {
            this.clearExpandedPathPieces();
        });
    }

    /**
     * Get the title of the tree item.
     * The title is the first line of the tree item's text content.
     * */
    getTreeItemTitle(treeItem: HTMLElement): string {
        return treeItem.innerText.split(/[\n\r][\n]?/)[0];
    }

    /**
     * Check if the tree item an immediate child of the parent tree item.
     * @param treeItem the tree item to check
     * @param parentTreeItem the parent tree item of the tree item
     * @param treeRoot the root of the tree (use getTreeElement to get the tree root)
     * @returns `true` if the tree item is an immediate child of the parent tree item, `false` otherwise
     */
    isTreeItemImmediateChildOf(treeItem: HTMLElement, parentTreeItem: HTMLElement, treeRoot: HTMLElement): boolean {
        let currentElement: HTMLElement | null = treeItem;

        while (currentElement && currentElement !== treeRoot) {
            currentElement = currentElement.parentElement;

            if (currentElement === parentTreeItem) {
                return true;
            }

            if (currentElement?.getAttribute('role') === 'treeitem') {
                return false;
            }
        }

        return false;
    }

    /**
     * Check if the tree item is a top-level item (not nested within any other tree item).
     * @param treeItem the tree item to check
     * @param treeRoot the root of the tree (use getTreeElement to get the tree root)
     * @returns `true` if the tree item is a top-level item, `false` otherwise
     */
    isTreeItemTopLevel(treeItem: HTMLElement, treeRoot: HTMLElement): boolean {
        let currentElement: HTMLElement | null = treeItem;

        while (currentElement && currentElement !== treeRoot) {
            currentElement = currentElement.parentElement;

            if (currentElement === treeRoot) {
                return true;
            }

            if (currentElement?.getAttribute('role') === 'treeitem') {
                return false;
            }
        }

        return false;
    }

    /**
     * Check if the tree item is expanded.
     * @param treeItem the tree item to check
     * @returns
     * - `true` if the tree item is expanded
     * - `false` if it is collapsed
     * - `null` if the tree item doesn't have the aria-expanded attribute (it's a leaf node)
     */
    isTreeItemExpanded(treeItem: HTMLElement): boolean | null {
        const ariaExpanded = treeItem.getAttribute('aria-expanded');
        if (ariaExpanded === null) {
            return null;
        }

        return ariaExpanded === 'true' ? true : false;
    }

    /**
     * Find the tree items corresponding to the provided path within the specified tree.
     * @param path the path to the overview
     * @param withinTree the tree in which to search for the path
     * @param options options for the search
     * @yields the tree items (the folders & the leaf node at the end of the array)
     **/
    findPathTreeItems(
        path: string[],
        withinTree: OverviewTreeType,
        options?: {
            /** whether to click through the tree items to ensure that the path is visible */
            revealPathIfNecessary?: boolean,
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<JQuery<HTMLElement>> {
        (options?.logAsStep ? cy.step : cy.log)(
            `Find the tree items corresponding to the path "${path.join('/')}" within the "${withinTree}" tree`
            + (options?.revealPathIfNecessary ? ' (also reveal them if necessary)' : ''),
        );

        if (options?.revealPathIfNecessary) {
            cy.log(`The internal test state of the overview "${withinTree}" tree's expanded folders is: `, this.expandedPathPieces[withinTree]);
        }

        const getRemainderContainerTreeItem = (remainingTreeItems: JQuery<HTMLElement>, pathRemainder: string[]) => {
            const remainderContainerTreeItems = remainingTreeItems.filter((_i, treeItem) => (
                // Check if the tree item's title matches the first piece of the path remainder
                this.getTreeItemTitle(treeItem) === pathRemainder[0]
            ));

            return remainderContainerTreeItems[0];
        };

        this.getTreeElement(withinTree).within(() => {
            cy.findAllByRole('treeitem').then((treeItems) => {
                // holds the result
                cy.wrap([]).as('pathTreeItems');

                // holds the tree items that are yet to be checked
                // (bellow the last resulting item of getRemainderContainerTreeItem)
                cy.wrap(treeItems).as('remainingTreeItems');

                // holds the remaining path pieces that are yet to be found
                // (their tree items are yet to be found using getRemainderContainerTreeItem)
                cy.wrap([...path]).as('pathRemainder');

                // Click through the tree items (folders & only the collapsed ones) to ensure that the overview is visible
                cy.wrap(new Array(path.length - 1)).each((_item, index) => {
                    cy.get('@remainingTreeItems').then((remainingTreeItems) => {
                        cy.get('@pathRemainder').then((pathRemainder: unknown) => {
                            cy.wrap(
                                getRemainderContainerTreeItem(remainingTreeItems, pathRemainder as string[]),
                            ).then((containerTreeItem) => {
                                const containerTreeItemPath = path.slice(0, index + 1).join('/');

                                cy.wrap((pathRemainder as string[]).slice(1)).as('pathRemainder');

                                // Click the tree item only if it hasn't been expanded before
                                // & the option to reveal the path is set to true
                                if (!this.expandedPathPieces[withinTree].has(containerTreeItemPath)) {
                                    if (!options.revealPathIfNecessary) {
                                        // Error if the path is not visible & we can't reveal it
                                        cy.wrap(containerTreeItem).should('have.attr', 'aria-expanded', 'true');
                                    } else {
                                        cy.wrap(containerTreeItem).click();
                                        this.expandedPathPieces[withinTree].add(containerTreeItemPath);
                                    }
                                }

                                cy.get('@pathTreeItems').then((pathTreeItems: unknown) => {
                                    cy.wrap([...pathTreeItems as HTMLElement[], containerTreeItem.get(0)]).as('pathTreeItems');
                                });

                                cy.findAllByRole('treeitem', { container: containerTreeItem }).as('remainingTreeItems');
                            });
                        });
                    });
                });

                // Find the last path tree item
                // (needs to be so that we can check that the inner text matches the last path piece exactly
                // - otherwise it could be inside an unrelated deeper nested folder)
                (cy).get('@remainingTreeItems').then((remainingTreeItems: unknown) => {
                    (cy).wrap(remainingTreeItems)
                        .filter((_index, treeItem) => treeItem.innerText === path[path.length - 1])
                        .then((lastPathTreeItem) => {
                            cy.wrap([...remainingTreeItems as HTMLElement[], lastPathTreeItem.get(0)]).as('pathTreeItems');
                        });
                });
            });
        });

        return cy.get('@pathTreeItems');
    }

    /**
     * Click through the tree items (folders & only the collapsed ones) to ensure that the overview
     * with the provided path is visible within the specified tree.
     * @param path the path to the overview
     * @param withinTree the tree in which to search for the path
     * @yields the revealed overview tree item
     **/
    revealOverviewWithPath(
        path: string[],
        withinTree: OverviewTreeType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<JQuery<HTMLElement>> {
        (options?.logAsStep ? cy.step : cy.log)(`Reveal the "${path.join('/')}" overview within the "${withinTree}" tree (traverse the tree)`);

        this.findPathTreeItems(path, withinTree, { revealPathIfNecessary: true }).then((pathTreeItems: unknown) => {
            cy.wrap(pathTreeItems as HTMLElement[]).last().as('toYield');
        });

        return cy.get('@toYield');
    }

    /**
     * Click through the tree items (folders & only the expanded ones) in reverse order to collapse the overview
     * with the provided path within the specified tree.
     * @param path the path to the overview
     * @param withinTree the tree in which to search for the path
     **/
    collapseOverviewWithPath(
        path: string[],
        withinTree: OverviewTreeType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<void> {
        (options?.logAsStep ? cy.step : cy.log)(`Collapse the "${path.join('/')}" overview (its whole path) within the "${withinTree}" tree`);

        this.findPathTreeItems(path, withinTree, { revealPathIfNecessary: false }).then((pathTreeItems: unknown) => {
            cy.wrap((pathTreeItems as JQuery<HTMLElement>).get().reverse().slice(1)).each((treeItem) => {
                cy.wrap(treeItem).click();
            });
        });

        return cy.end();
    }

    /**
     * Click through the tree items (folders & only the collapsed ones) to ensure that all of the overviews within the specified tree are visible.
     * @param withinTree the tree in which to search for the path
     * @yields the revealed overview tree items (the overviews)
     **/
    revealAllOverviews(
        withinTree: OverviewTreeType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<JQuery<HTMLElement>> {
        (options?.logAsStep ? cy.step : cy.log)(`Reveal all of the overviews within the "${withinTree}" tree (traverse the tree)`);
        cy.log('The internal test state of the overview "${withinTree}" tree\'s expanded folders is: ', this.expandedPathPieces[withinTree]);

        const lastPathPieces = new Set<string>();

        const expandNonExpandedTreeItems = (): Cypress.Chainable<boolean> => {
            cy.findAllByRole('treeitem').its('length').as('treeItemsCount');
            return cy.findAllByRole('treeitem').then((treeItems) => {
                const treeItemsLength = treeItems.length;
                const nonExpandedTreeItems = treeItems.filter((_index, treeItem) => this.isTreeItemExpanded(treeItem) === false);

                if (nonExpandedTreeItems.length > 0) {
                    cy.wrap(nonExpandedTreeItems).each((treeItem) => {
                        cy.wrap(treeItem).click();
                    });

                    // wait for the tree items to expand
                    cy.get('@treeItemsCount').should('be.gt', treeItemsLength);
                }

                return cy.findAllByRole('treeitem').then((treeItems) => {
                    const nonExpandedTreeItems = treeItems.filter((_index, treeItem) => this.isTreeItemExpanded(treeItem) === false);
                    return nonExpandedTreeItems.length === 0;
                });
            });
        };

        const recursiveExpandAllTreeItems = () => {
            expandNonExpandedTreeItems().then((result) => {
                if (!result) {
                    recursiveExpandAllTreeItems();
                }
            });
        };

        this.getTreeElement(withinTree).within(() => {
            recursiveExpandAllTreeItems();

            cy.get('@overviewPaths').each((path: unknown) => {
                const pathCast = [...path as string[]].slice(0, -1); // Remove the last element (the overview itself)

                pathCast.forEach((_pathPiece, index) => {
                    this.expandedPathPieces[withinTree].add(pathCast.slice(0, index + 1).join('/'));
                });

                lastPathPieces.add(path[(path as string[]).length - 1]);
            });

            cy.findAllByRole('treeitem').filter((_index, treeItem) => (lastPathPieces.has(treeItem.innerText))).as('toYield');
        });

        return cy.get('@toYield');
    }

    /**
     * Yields the path to the `n`th nested overview within the overviews.json fixture.
     * @param n the index of the overview to get
     * @param options options for filtering the considered overviews & logging
     * @yields the path to the `n`th nested overview
     **/
    getPathForNthNestedOverview(
        n: number,
        options?: {
            /** Minimum nest level to filter the items from which the nth item will be picked. 1 by default. */
            minNestLevel?: number,
            /** Maximum nest level to filter the items from which the nth item will be picked. */
            maxNestLevel?: number,
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<string[] | undefined> {
        (options?.logAsStep ? cy.step : cy.log)(
            `Get the path to the "${n}th" nested overview, given the specified arguments
                (minNestLevel: ${options.minNestLevel}, maxNestLevel: ${options.maxNestLevel})`,
        );

        return cy.get('@overviewPaths').then(((paths: unknown) => {
            const filteredPaths = (paths as string[][]).filter((path) => (
                path.length >= (typeof options?.minNestLevel === 'undefined' ? 1 : options?.minNestLevel)
                && (typeof options?.maxNestLevel === 'undefined' || path.length <= options?.maxNestLevel)
            ));

            const selectedPath = filteredPaths[n];
            if (typeof selectedPath === 'undefined') {
                expect(
                    selectedPath,
                    'getNthNestedOverview: The overviews.json fixture doesn\'t contain enough entries meeting the specified arguments',
                ).not.to.be.undefined;
                return undefined;
            }

            return selectedPath;
        }));
    }

    /**
     * Get the tree title element.
     * @param tree the tree for which to get the title element
     * @param options options for the get
     * @yields the tree title element
     **/
    getTreeTitleElement(
        tree: OverviewTreeType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<JQuery<HTMLElement>> {
        (options?.logAsStep ? cy.step : cy.log)(`Get the "${tree}" overview tree title element`);

        const titleTranslations = {
            favourites: 'Oblíbené',
            all: 'Všechny',
            hidden: 'Skryté',
        };

        return cy.contains(titleTranslations[tree]);
    }

    /**
     * Get the corresponding tree root element.
     * @param tree the tree for which to get the tree element
     * @param options options for the get
     * @yields the tree element
     **/
    getTreeElement(
        tree: OverviewTreeType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<JQuery<HTMLElement>> {
        (options?.logAsStep ? cy.step : cy.log)(`Get the "${tree}" overview tree element (the tree root) base on its position relative to the tree title`);

        return this.getTreeTitleElement(tree).then((treeTitleElement) => {
            return cy.findAllByRole('tree', { timeout: 10000 }).then(((elements) => (
                elements.get().filter((element) => ( // Filter out the trees that are below the tree title
                    element.getBoundingClientRect().y
                        > treeTitleElement.get()[0].getBoundingClientRect().y
                )).sort((a, b) => ( // Sort the remaining trees by their y position
                    a.getBoundingClientRect().y - b.getBoundingClientRect().y
                ))[0] // Pick the top-most tree (closest to the tree title)
            )));
        });
    }

    /**
     * Perform an action on the overview's tree item (click the corresponding button within the tree item).
     * @param treeItem the tree item on which to perform the action
     * @param action the action to perform
     * @param options options for the action
     */
    doOverviewAction(
        treeItem: JQuery<HTMLElement>,
        action: OverviewTreeItemActionType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<void> {
        (options?.logAsStep ? cy.step : cy.log)(`Perform the "${action}" action on the "${this.getTreeItemTitle(treeItem.get(0))}" overview tree item`);

        const firstButtonIndex = action === 'edit' ? 0 : 1;
        cy.wrap(treeItem).findAllByRole('button', { hidden: true }).then((buttons) => {
            cy.wrap(buttons.get().sort((a, b) => a.getBoundingClientRect().x - b.getBoundingClientRect().x)[firstButtonIndex]).click({ force: true });
        });

        if (firstButtonIndex === 1) {
            const actionTranslations = {
                favourite: 'Přidat do oblíbených',
                unfavourite: 'Odebrat z oblíbených?',
                clone: 'Kopírovat',
                hide: 'Skrýt',
                delete: 'Smazat',
                unhide: 'Odebrat ze skrytých',
            };

            cy.findAllByRole('button').contains(actionTranslations[action]).click();
        }

        return cy.end();
    }

    /**
     * Clear the expanded path pieces set, which is used for keeping track of the expanded "folders" within the overview trees.
     * @param options options for the clear
     */
    clearExpandedPathPieces(
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<void> {
        (options?.logAsStep ? cy.step : cy.log)('Clear the internal test state of the overview trees\' expanded folders');

        cy.then(() => {
            this.expandedPathPieces.all.clear();
            this.expandedPathPieces.favourites.clear();
            this.expandedPathPieces.hidden.clear();
        });

        return cy.end();
    }

    /**
     * Reset the expanded path pieces set based on the current state of the DOM.
     * The expanded path pieces set is used for keeping track of the expanded "folders" within the overview trees.
     * @param withinTree the tree for which to reset the expanded path pieces
     * @param options options for the reset
     */
    resetExpandedPathPieces(
        withinTree: OverviewTreeType,
        options?: {
            /** Log this util using `cy.step` */
            logAsStep?: boolean,
        },
    ): Cypress.Chainable<void> {
        (options?.logAsStep ? cy.step : cy.log)(`Reset the internal test state of the overview "${withinTree}" tree's expanded folders (traverse the tree)`);
        cy.log(`The current internal test state of the overview "${withinTree}" tree's expanded folders is: `, this.expandedPathPieces[withinTree]);

        this.getTreeElement(withinTree).within((treeRoot) => {
            const newExpandedPathPieces = new Set<string>();
            let expandedTreeItemsExist = false;

            if (treeRoot.find('[role="treeitem"]').length === 0) {
                return;
            }

            cy.findAllByRole('treeitem').then((treeItems) => {
                expandedTreeItemsExist = treeItems.get().some((treeItem) => this.isTreeItemExpanded(treeItem));

                return treeItems;
            }).then((treeItems) => {
                if (expandedTreeItemsExist) {
                    cy.wrap(treeItems).then((items) => (
                        items
                            .get()
                            .filter((treeItem) => (
                                this.isTreeItemExpanded(treeItem)
                                && this.isTreeItemTopLevel(treeItem, treeRoot.get(0))
                            ))
                    )).then((topLevelTreeItems) => {
                        type ParentTreeItemInfoType = {
                            element: HTMLElement,
                            path: string[],
                        };

                        cy.wrap(
                            topLevelTreeItems
                                .map((_index, topLevelTreeItem) => ({
                                    element: topLevelTreeItem,
                                    path: [this.getTreeItemTitle(topLevelTreeItems.get(0))],
                                } as ParentTreeItemInfoType)),
                        ).each((topLevelTreeItemInfo: unknown) => {
                            const traverseAllChildrenOf = (parentTreeItemInfo: ParentTreeItemInfoType) => {
                                if (Cypress.$(parentTreeItemInfo.element).find('[role="treeitem"]').length === 0) {
                                    return;
                                }

                                cy.findAllByRole('treeitem', { container: parentTreeItemInfo.element }).then((childrenTreeItems) => (
                                    childrenTreeItems.filter((_index, treeItem) => (
                                        this.isTreeItemExpanded(treeItem)
                                        && this.isTreeItemImmediateChildOf(treeItem, parentTreeItemInfo.element, treeRoot.get(0))
                                    ))
                                )).each((immediateChildExpandedTreeItem) => {
                                    cy.wrap(immediateChildExpandedTreeItem).then((treeItem) => {
                                        const path = [...parentTreeItemInfo.path, this.getTreeItemTitle(treeItem.get(0))];
                                        this.expandedPathPieces[withinTree].add(path.join('/'));

                                        traverseAllChildrenOf({ element: treeItem.get(0), path });
                                    });
                                });
                            };

                            this.expandedPathPieces[withinTree].add((topLevelTreeItemInfo as ParentTreeItemInfoType).path.join('/'));
                            traverseAllChildrenOf(topLevelTreeItemInfo as ParentTreeItemInfoType);
                        });
                    });
                }
            });

            cy.then(() => {
                this.expandedPathPieces[withinTree] = newExpandedPathPieces;
            });
        });

        return cy.end();
    }
}

export default OverviewTreeUtils;
