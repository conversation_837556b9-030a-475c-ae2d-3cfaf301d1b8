type ColumnType = {
    name: string,
    name_cs: string,
} & ({
    type: 'T' | 'D',
} | {
    type: 'N',
    isCheckbox?: boolean,
} | {
    type: 'LT' | 'LD' | 'LN' | 'DL',
    multichoice?: boolean,
});

type SortingRule = {
    columnName: ColumnType['name'],
    rule: 'ascending' | 'descending',
};

type FilterOperator = (
    'equal' |
    'notEqual' |
    'gt' |
    'gte' |
    'lt' |
    'lte' |
    'contains' |
    'doesNotContain' |
    'isEmpty' |
    'isNotEmpty'
);

type FilterType = {
    junction?: 'AND' | 'OR',
    columnName: ColumnType['name'],
    operator: FilterOperator,
    value: string,
};

/** @FixtureType */
export type TestOverviewJSONType = {
    name: string,
    name_cs: string,
    columns: ColumnType[],
    /** The first filter group should have no junction */
    filterGroups: {
        junction?: 'AND' | 'OR',
        /** The first filter should have no junction */
        filters: FilterType[],
    }[],
    simpleSortingRule: SortingRule | null,
    expertSortingRules: SortingRule[],
    sampleRows: {
        defaultPosition: number,
        displayedDataArray: string[],
    }[],
};

export type { ColumnType, SortingRule, FilterType };
