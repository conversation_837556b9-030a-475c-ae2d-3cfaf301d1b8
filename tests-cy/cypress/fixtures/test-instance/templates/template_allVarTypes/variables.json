{"text": {"type": "T", "suggest": true, "editor": false, "name": "text", "name_cs": "Text", "rwmTestModes": ["W", "M"], "value": "asdfjklů"}, "textMultipleLines": {"type": "T", "suggest": false, "multiline": true, "editor": false, "name": "textMultipleLines", "name_cs": "Text s v<PERSON><PERSON>", "rwmTestModes": ["M", "R"], "value": ["asdfjklů yxcv qwer uiop", "asdfjklů", "asdfjklů yxcv qwer", "asdfjklů yxcv"]}, "date": {"type": "D", "name": "date", "name_cs": "Datum", "rwmTestModes": ["M", "R"], "value": "17.05.2024"}, "number": {"type": "N", "name": "number", "name_cs": "<PERSON><PERSON><PERSON>", "rwmTestModes": ["W", "M"], "value": 42}, "checkbox": {"type": "N", "name": "checkbox", "name_cs": "Checkbox", "rwmTestModes": ["M", "R"], "isCheckbox": true, "value": 1}, "textList": {"type": "LT", "multichoice": false, "name": "textList", "name_cs": "Číselník textů", "rwmTestModes": ["W", "M"], "value": {"index": 1, "actualValue": "text2 text2 text2 text2 text2 text2 text2 text2 text2 text2 "}}, "dateList": {"type": "LD", "multichoice": false, "name": "dateList", "name_cs": "Číselník datumů", "rwmTestModes": ["M", "R"], "value": {"index": 3, "actualValue": "29.02.2024"}}, "numberList": {"type": "LN", "multichoice": false, "name": "numberList", "name_cs": "Číselník čísel", "rwmTestModes": ["W", "M"], "value": {"index": 6, "actualValue": 7777777}, "displayValue": {"index": 6, "actualValue": "7 777 777"}}, "dynamicList": {"type": "DL", "multichoice": false, "name": "dynamicList", "name_cs": "Dynamický seznam", "rwmTestModes": ["W", "M"], "value": {"index": 2, "actualValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "dynamicRows": {"type": "DR", "name": "dynamicRows", "name_cs": "Dynamick<PERSON>", "rwmTestModes": ["M", "R"], "value": [["yxcv", "asdfjklů", 1, 222, 333, "18.05.2024", "11:45"], ["asdf", "yxcv qwer", 111, 33, 2, "19.05.2024", "07:05"]], "displayValue": [["yxcv", "asdfjklů", "1", "222", "333,00 Kč", "18.05.2024", "11:45"], ["asdf", "yxcv qwer", "111", "33", "2,00 Kč", "19.05.2024", "07:05"]]}, "dynamicList_multiselect": {"type": "DL", "multichoice": true, "name": "dynamicList_multiselect", "name_cs": "Dynamický seznam - výběr více položek", "rwmTestModes": ["M", "R"], "value": [{"index": 1, "actualValue": "<PERSON><PERSON>"}, {"index": 2, "actualValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "text_suggest_users_user_full_name": {"type": "T", "suggest": true, "multiline": false, "editor": false, "name": "text_suggest_users_user_full_name", "name_cs": "Text - na<PERSON><PERSON><PERSON><PERSON><PERSON> (/users - user_full_name)", "rwmTestModes": ["W", "M"], "value": "<PERSON><PERSON>"}, "textMultipleLines_editor": {"type": "T", "suggest": false, "multiline": true, "editor": true, "name": "textMultipleLines_editor", "name_cs": "Text s <PERSON><PERSON><PERSON> - editor", "rwmTestModes": ["M", "R"], "value": ["asdfjklů", "asdfjklů yxcv qwer", "asdfjklů yxcv"]}, "textMultipleLines_editor_extended": {"type": "T", "suggest": false, "multiline": true, "editor": "E", "name": "textMultipleLines_editor_extended", "name_cs": "Text s v<PERSON><PERSON> - rozšířený editor", "rwmTestModes": ["W", "M"], "value": ["asdfjklů yxcv qwer uiop", "asdfjklů", "asdfjklů yxcv qwer"]}}