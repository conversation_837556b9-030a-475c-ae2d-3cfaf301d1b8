{"private": true, "scripts": {"cy:run": "cypress run --e2e --env tasTestsUseCustomBrowsers=true", "cy:run:js": "cypress run --e2e --config-file cypress.config.js", "cy:validate-fixtures": "cypress run --env tasTestsFixtureValidationRun=true --spec \"cypress/e2e/___allFixturesAreValid.cy.ts\"", "cy:open": "cypress open --e2e --env tasTestsUseCustomBrowsers=true", "install-custom-browsers": "tsx ./custom-browsers/install-custom-browsers.ts", "postinstall": "tsx ./custom-browsers/install-custom-browsers.ts"}, "devDependencies": {"@puppeteer/browsers": "2.7.1", "@testing-library/cypress": "10.0.1", "@types/lodash": "4.14.198", "@types/node": "20.11.4", "@typescript-eslint/eslint-plugin": "6.7.0", "@typescript-eslint/parser": "6.7.0", "ajv": "8.12.0", "cypress": "13.17.0", "cypress-fail-fast": "7.1.0", "cypress-plugin-snapshots": "1.4.4", "cypress-plugin-steps": "1.1.1", "eslint": "8.49.0", "eslint-plugin-cypress": "2.14.0", "glob": "10.3.10", "lodash": "4.17.21", "tsx": "4.19.2", "typescript": "5.2.2", "typescript-json-schema": "0.62.0"}, "overrides": {"cypress": "13.17.0"}}